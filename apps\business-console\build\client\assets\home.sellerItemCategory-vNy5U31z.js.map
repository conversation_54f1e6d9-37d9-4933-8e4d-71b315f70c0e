{"version": 3, "file": "home.sellerItemCategory-vNy5U31z.js", "sources": ["../../../app/routes/home.sellerItemCategory.tsx"], "sourcesContent": ["import { json, LoaderFunction } from \"@remix-run/node\";\r\nimport { useLoaderData } from \"@remix-run/react\";\r\nimport { error } from \"console\";\r\nimport { useState } from \"react\";\r\nimport { Input } from \"~/components/ui/input\";\r\nimport { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from \"~/components/ui/table\";\r\nimport { getMasterItemCategory, getNetWorkItemCategories, getSellerItemCategories } from \"~/services/masterItemCategories\";\r\nimport { SellerItemCategories } from \"~/types/api/businessConsoleService/MasterItemCategory\";\r\nimport { withAuth, withResponse } from \"~/utils/auth-utils\";\r\n\r\n\r\n\r\nexport const loader = withAuth(async ({ user, request }) => {\r\n\r\n      const networkId = 6;\r\n\r\n      try {\r\n            const SellerItemCategoryResponse = await getSellerItemCategories(networkId, request);\r\n            return withResponse({ data: SellerItemCategoryResponse.data }, SellerItemCategoryResponse.headers)\r\n      }\r\n      catch (error) {\r\n            if (error instanceof Response && error.status === 404) {\r\n                  throw json({ error: \"MasterItemCategory pg Not found\" }, { status: 404 });\r\n            }\r\n            throw new Response(\"Failed to fetch MasterItemCategory \", { status: 500 });\r\n      }\r\n\r\n})\r\n\r\n\r\nexport default function SellerItemCategory() {\r\n\r\n      const categories = useLoaderData<{ data: SellerItemCategories[] }>()\r\n      const [searchTerm, setSearchTerm] = useState('')\r\n\r\n      return (\r\n            <div className=\"container mx-auto p-6\">\r\n                  <div className=\"flex justify-between items-center mb-6\">\r\n                        <h1 className=\"text-2xl font-bold\">Seller Item Category</h1>\r\n                  </div>\r\n\r\n                  <div className=\"flex justify-between mb-4\">\r\n                        <Input\r\n                              placeholder=\"Search by Item Name\"\r\n                              value={searchTerm}\r\n                              onChange={(e) => setSearchTerm(e.target.value)}\r\n                              className=\"max-w-sm\"\r\n                        />\r\n                  </div>\r\n                  <Table>\r\n                        <TableHeader>\r\n                              <TableRow>\r\n                                    <TableHead className=\"cursor-pointer\" >Item CategoryId </TableHead>\r\n                                    <TableHead className=\"cursor-pointer\" >SICId</TableHead>\r\n                                    <TableHead className=\"cursor-pointer\" > Image</TableHead>\r\n                                    <TableHead className=\"cursor-pointer\" > Name </TableHead>\r\n                                    <TableHead className=\"cursor-pointer\" >picturex </TableHead>\r\n                                    <TableHead className=\"cursor-pointer\" >picturexx </TableHead>\r\n                                    <TableHead className=\"cursor-pointer\" >level </TableHead>\r\n\r\n                              </TableRow>\r\n                        </TableHeader>\r\n                        <TableBody>\r\n                              {categories.data?.filter((x) => x.name.toLowerCase().includes(searchTerm.toLowerCase())).sort((a, b) => a.name.localeCompare(b.name)).map((item) => {\r\n\r\n\r\n                                    return (\r\n                                          <TableRow key={item.sICId}>\r\n                                                <TableCell>{item.itemCategoryId}</TableCell>\r\n                                                <TableCell>{item.sICId}</TableCell>\r\n\r\n                                                <TableCell> <img src={item?.picture}\r\n                                                      alt=\"ItemImage\"\r\n                                                      className=\"h-10 w-10\" /></TableCell>\r\n                                                <TableCell>{item.name}</TableCell>\r\n                                                <TableCell> <img src={item?.picturex}\r\n                                                      alt=\"ItemImage\"\r\n                                                      className=\"h-10 w-10\" /></TableCell>\r\n                                                <TableCell> <img src={item?.picturexx}\r\n                                                      alt=\"ItemImage\"\r\n                                                      className=\"h-10 w-10\" /></TableCell>\r\n\r\n                                                <TableCell>{item.level}</TableCell>\r\n\r\n                                          </TableRow>\r\n                                    )\r\n                              })}\r\n                        </TableBody>\r\n                  </Table>\r\n\r\n\r\n            </div>\r\n      )\r\n\r\n\r\n\r\n}"], "names": ["SellerItemCategory", "categories", "useLoaderData", "searchTerm", "setSearchTerm", "useState", "jsxs", "className", "children", "jsx", "Input", "placeholder", "value", "onChange", "e", "target", "Table", "TableHeader", "TableRow", "TableHead", "TableBody", "data", "filter", "x", "name", "toLowerCase", "includes", "sort", "a", "b", "localeCompare", "map", "item", "TableCell", "itemCategoryId", "sICId", "src", "picture", "alt", "picturex", "picturexx", "level"], "mappings": ";;;;;;;AA8BA,SAAwBA,qBAAqB;;AAEvC,QAAMC,aAAaC,cAAgD;AACnE,QAAM,CAACC,YAAYC,aAAa,IAAIC,aAAAA,SAAS,EAAE;AAGzC,SAAAC,kCAAAA,KAAC,OAAI;AAAA,IAAAC,WAAU;AAAA,IACTC,UAAA,CAACC,kCAAA,IAAA,OAAA;AAAA,MAAIF,WAAU;AAAA,MACTC,UAAAC,kCAAA,IAAC;QAAGF,WAAU;AAAA,QAAqBC;MAAoB,CAAA;AAAA,IAC7D,CAAA,GAEAC,kCAAA,IAAC,OAAI;AAAA,MAAAF,WAAU;AAAA,MACTC,UAAAC,kCAAA,IAACC,OAAA;AAAA,QACKC,aAAY;AAAA,QACZC,OAAOT;AAAAA,QACPU,UAAWC,OAAMV,cAAcU,EAAEC,OAAOH,KAAK;AAAA,QAC7CL,WAAU;AAAA,MAChB,CAAA;AAAA,IACN,CAAA,0CACCS,OACK;AAAA,MAAAR,UAAA,CAACC,kCAAA,IAAAQ,aAAA;AAAA,QACKT,iDAACU,UACK;AAAA,UAAAV,UAAA,CAACC,kCAAA,IAAAU,WAAA;AAAA,YAAUZ,WAAU;AAAA,YAAkBC,UAAgB;AAAA,UAAA,CAAA,GACtDC,kCAAA,IAAAU,WAAA;AAAA,YAAUZ,WAAU;AAAA,YAAkBC,UAAK;AAAA,UAAA,CAAA,GAC3CC,kCAAA,IAAAU,WAAA;AAAA,YAAUZ,WAAU;AAAA,YAAkBC,UAAM;AAAA,UAAA,CAAA,GAC5CC,kCAAA,IAAAU,WAAA;AAAA,YAAUZ,WAAU;AAAA,YAAkBC,UAAM;AAAA,UAAA,CAAA,GAC5CC,kCAAA,IAAAU,WAAA;AAAA,YAAUZ,WAAU;AAAA,YAAkBC,UAAS;AAAA,UAAA,CAAA,GAC/CC,kCAAA,IAAAU,WAAA;AAAA,YAAUZ,WAAU;AAAA,YAAkBC,UAAU;AAAA,UAAA,CAAA,GAChDC,kCAAA,IAAAU,WAAA;AAAA,YAAUZ,WAAU;AAAA,YAAkBC,UAAM;AAAA,UAAA,CAAA,CAAA;AAAA,QAEnD,CAAA;AAAA,MACN,CAAA,GACCC,kCAAA,IAAAW,WAAA;AAAA,QACMZ,WAAWP,gBAAAoB,SAAApB,mBAAMqB,OAAQC,OAAMA,EAAEC,KAAKC,YAAc,EAAAC,SAASvB,WAAWsB,YAAa,CAAA,GAAGE,KAAK,CAACC,GAAGC,MAAMD,EAAEJ,KAAKM,cAAcD,EAAEL,IAAI,GAAGO,IAAKC,UAAS;AAG9I,wDACOd,UACK;AAAA,YAAAV,UAAA,CAACC,kCAAA,IAAAwB,WAAA;AAAA,cAAWzB,eAAK0B;AAAAA,YAAe,CAAA,GAChCzB,kCAAA,IAACwB,WAAW;AAAA,cAAAzB,UAAAwB,KAAKG;AAAAA,YAAM,CAAA,0CAEtBF,WAAU;AAAA,cAAAzB,UAAA,CAAA,KAACC,kCAAA,IAAC,OAAA;AAAA,gBAAI2B,KAAKJ,6BAAMK;AAAAA,gBACtBC,KAAI;AAAA,gBACJ/B,WAAU;AAAA,cAAA,CAAY,CAAA;AAAA,YAAE,CAAA,GAC9BE,kCAAA,IAACwB,WAAW;AAAA,cAAAzB,UAAAwB,KAAKR;AAAAA,YAAK,CAAA,0CACrBS,WAAU;AAAA,cAAAzB,UAAA,CAAA,KAACC,kCAAA,IAAC,OAAA;AAAA,gBAAI2B,KAAKJ,6BAAMO;AAAAA,gBACtBD,KAAI;AAAA,gBACJ/B,WAAU;AAAA,cAAA,CAAY,CAAA;AAAA,YAAE,CAAA,0CAC7B0B,WAAU;AAAA,cAAAzB,UAAA,CAAA,KAACC,kCAAA,IAAC,OAAA;AAAA,gBAAI2B,KAAKJ,6BAAMQ;AAAAA,gBACtBF,KAAI;AAAA,gBACJ/B,WAAU;AAAA,cAAA,CAAY,CAAA;AAAA,YAAE,CAAA,GAE9BE,kCAAA,IAACwB,WAAW;AAAA,cAAAzB,UAAAwB,KAAKS;AAAAA,YAAM,CAAA,CAAA;AAAA,UAAA,GAfdT,KAAKG,KAiBpB;AAAA,QAEX;AAAA,MACP,CAAA,CAAA;AAAA,IACN,CAAA,CAAA;AAAA,EAGN,CAAA;AAKZ;"}