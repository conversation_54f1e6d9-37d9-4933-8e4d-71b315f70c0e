{"version": 3, "file": "home.bankTransactions-wl7n8wS-.js", "sources": ["../../../app/components/ui/markAsPaidPopup.tsx", "../../../app/routes/home.bankTransactions.tsx"], "sourcesContent": ["import { <PERSON>alog, DialogContent, DialogTitle } from \"@radix-ui/react-dialog\"\r\nimport { useFetcher } from \"@remix-run/react\"\r\nimport { useEffect } from \"react\"\r\nimport { useToast } from \"./ToastProvider\"\r\nimport { Transaction } from \"~/types/api/businessConsoleService/payments\";\r\nimport { markAsUntransferable } from \"node:worker_threads\";\r\nimport SpinnerLoader from \"../loader/SpinnerLoader\";\r\n\r\ninterface MarkAsPaidProps {\r\n      heading: string;\r\n      isOpen: boolean;\r\n      content: string;\r\n      isClosed: () => void;\r\n      row: Transaction | null;\r\n}\r\n\r\nexport default function MarkAsPaid({ heading, isOpen, content, isClosed, row }: MarkAsPaidProps) {\r\n      const fetcher = useFetcher();\r\n      const { showToast } = useToast();\r\n      const isLoading = fetcher.state !== \"idle\";\r\n\r\n      useEffect(() => {\r\n            if (fetcher.data) {\r\n                  if (fetcher.data !== undefined && isOpen === true) {\r\n                        showToast(\"Mark As Paid\", \"success\");\r\n                        isClosed();\r\n                  } else if (fetcher.data === undefined) {\r\n                        showToast(\"Fail To create Agent\", \"error\");\r\n                  }\r\n            }\r\n      }, [fetcher.data, isOpen, isClosed, showToast]);\r\n\r\n\r\n\r\n      const handleSubmit = () => {\r\n            const formData = new FormData();\r\n            formData.append(\"intent\", \"markAsPaid\");\r\n            formData.append(\"amount\", row?.amount as unknown as string);\r\n            formData.append(\"depositId\", row?.id as unknown as string);\r\n            formData.append(\"note\", \"Mark as Paid\");\r\n\r\n            fetcher.submit(formData, { method: \"post\" })\r\n      }\r\n\r\n\r\n      return (\r\n            <Dialog open={isOpen} onOpenChange={isClosed}>\r\n                  {/* Overlay Background */}\r\n                  {/* <div className=\"fixed inset-0 white-bg-black/50 backdrop-blur-sm\" /> */}\r\n\r\n                  {/* Dialog Content */}\r\n                  {isLoading && (\r\n                        <div className=\"absolute inset-0 flex items-center justify-center bg-white/10 backdrop-blur-sm\">\r\n                              <SpinnerLoader loading={isLoading} />\r\n                        </div>\r\n                  )}\r\n                  <DialogContent className=\"fixed inset-0 flex items-center justify-center\">\r\n                        <div className=\"bg-orange-50 p-6 rounded-lg shadow-lg w-[400px]\">\r\n                              <DialogTitle className=\"text-lg font-bold\">{heading}</DialogTitle>\r\n                              <form onSubmit={() => handleSubmit()} className=\"space-y-4\">\r\n                                    <p className=\"text-red-400 font-bold text-sm\">{content}</p>\r\n                                    <div className=\"flex justify-end space-x-2\">\r\n                                          <button\r\n                                                type=\"button\"\r\n                                                onClick={isClosed}\r\n                                                className=\"px-4 py-2 bg-gray-300 rounded\"\r\n                                          >\r\n                                                Cancel\r\n                                          </button>\r\n                                          <button\r\n                                                type=\"submit\"\r\n                                                className=\"px-4 py-2 bg-blue-600 text-white rounded\"\r\n                                          >\r\n                                                {isLoading ? \"Submitting...\" : \"Submit\"}\r\n                                          </button>\r\n                                    </div>\r\n                              </form>\r\n                        </div>\r\n                  </DialogContent>\r\n            </Dialog>\r\n      );\r\n}\r\n", "import { ActionFunction, json, LoaderFunction, redirect } from \"@remix-run/node\";\r\nimport { Await, Form, useFetcher, useLoaderData } from \"@remix-run/react\";\r\nimport * as React from \"react\";\r\nimport { CalendarIcon } from \"lucide-react\";\r\nimport { <PERSON><PERSON> } from \"~/components/ui/button\";\r\nimport { Input } from \"~/components/ui/input\";\r\n\r\nimport {\r\n      Table,\r\n      TableBody,\r\n      TableCell,\r\n      TableHead,\r\n      TableHeader,\r\n      TableRow,\r\n} from \"~/components/ui/table\";\r\nimport {\r\n      Popover,\r\n      PopoverContent,\r\n      PopoverTrigger,\r\n} from \"~/components/ui/popover\";\r\nimport { Calendar } from \"~/components/ui/calendar\";\r\nimport { format } from \"date-fns\";\r\nimport { Transaction } from \"~/types/api/businessConsoleService/payments\";\r\nimport { getTransactionDetails, updateMarkAsPaid, updateTransaction } from \"~/services/payments\";\r\nimport { Checkbox } from \"~/components/ui/checkbox\";\r\nimport {\r\n      Select,\r\n      SelectContent,\r\n      SelectItem,\r\n      SelectTrigger,\r\n      SelectValue,\r\n} from \"~/components/ui/select\";\r\nimport SucessPopUp from \"~/components/ui/sucessPopUp\";\r\nimport { withAuth, withResponse } from \"@utils/auth-utils\";\r\nimport MarkAsPaid from \"~/components/ui/markAsPaidPopup\";\r\nimport { useState } from \"react\";\r\nimport SpinnerLoader from \"~/components/loader/SpinnerLoader\";\r\n\r\ninterface LoaderData {\r\n      bankData: Transaction[],\r\n      adminPermission: boolean\r\n}\r\n\r\nexport const loader: LoaderFunction = withAuth(async ({ request, user }) => {\r\n      try {\r\n            const permission = user?.userDetails.roles?.includes(\"AC_Basic\")\r\n            // console.log(permission, \"444444444444444444\")\r\n            const url = new URL(request.url);\r\n            const date = url.searchParams.get(\"date\") ? url.searchParams.get(\"date\") : new Date().toISOString().split(\"T\")[0];\r\n\r\n            if (!date) {\r\n                  return json({ error: \"Date is required\" }, { status: 400 });\r\n            }\r\n            const response = await getTransactionDetails(date, request);\r\n            return withResponse({\r\n                  bankData: response.data,\r\n                  adminPermission: permission\r\n\r\n            }, response.headers);\r\n      } catch (error) {\r\n            console.error(error);\r\n            throw new Response(\"Failed to fetch transactions\", { status: 500 });\r\n      }\r\n});\r\nexport const action: ActionFunction = withAuth(async ({ request, user }) => {\r\n      const formData = await request.formData();\r\n      const rowId = Number(formData.get(\"rowId\"));\r\n      const intent = formData.get(\"intent\")\r\n      const date = formData.get(\"date\") as string\r\n      const depositId = formData.get(\"depositId\") as unknown as number;\r\n      const amount = formData.get(\"amount\") as unknown as number;\r\n      const note = formData.get(\"note\") as string\r\n      const userId = user?.userId;\r\n      const permission = user?.userPermissions?.includes(\"seller_app.mnetAdminBasic\")\r\n      console.log(user, \"444444444444444444\")\r\n      if (intent === \"markAsPaid\") {\r\n            try {\r\n                  const response = await updateMarkAsPaid(userId, depositId, request);\r\n                  return withResponse({\r\n                        bankData: response.data,\r\n                        adminPermission: permission\r\n                  }, response.headers)\r\n            }\r\n\r\n            catch (error) {\r\n                  console.error(error);\r\n                  throw new Response(\"Failed to update transaction\", { status: 500 });\r\n            }\r\n      }\r\n\r\n\r\n      if (intent == \"updateTransaction\") {\r\n            try {\r\n                  const response = await updateTransaction(rowId, request);\r\n                  return withResponse({\r\n                        bankData: response.data,\r\n                        adminPermission: permission\r\n                  }, response.headers)\r\n            } catch (error) {\r\n                  console.error(error);\r\n                  throw new Response(\"Failed to update transaction\", { status: 500 });\r\n            }\r\n      }\r\n\r\n\r\n\r\n\r\n});\r\n\r\nexport default function BankTransactions() {\r\n      const { bankData, adminPermission } = useLoaderData<LoaderData>();\r\n      const fetcher = useFetcher<{ bankData: Transaction[] }>();\r\n      const [data, setData] = React.useState<Transaction[]>(bankData);\r\n      const [date, setDate] = React.useState<Date | undefined>(new Date());\r\n      const [filters, setFilters] = React.useState<string[]>([\"ALL\"]);\r\n      const [searchTerm, setSearchTerm] = React.useState(\"\");\r\n      const [loading, setLoading] = React.useState(false);\r\n      const [pageSize, setPageSize] = React.useState(\"100\");\r\n      const [currentPage, setCurrentPage] = React.useState(1);\r\n\r\n      React.useEffect(() => {\r\n            if (fetcher.data?.bankData) {\r\n                  applyFilters(fetcher.data.bankData); // Always filter from fresh fetched data\r\n            }\r\n      }, [fetcher.data, filters, searchTerm]);\r\n\r\n      const applyFilters = (newData: Transaction[]) => {\r\n            if (!Array.isArray(newData)) {\r\n                  return []; // Return an empty array as a fallback\r\n            }\r\n            else {\r\n                  const filteredData = newData?.filter(\r\n                        (trans) =>\r\n                              (filters.includes(\"ALL\") || filters.includes(trans.status)) &&\r\n                              (\r\n                                    trans.businessName?.toLowerCase().includes(searchTerm.toLowerCase()) ||\r\n                                    trans.orderGroupId?.toString().includes(searchTerm) ||\r\n                                    trans.amount?.toString().includes(searchTerm) ||\r\n                                    trans.id?.toString().includes(searchTerm)\r\n                              )\r\n                  );\r\n                  setData(filteredData);\r\n                  setCurrentPage(1);\r\n            }\r\n\r\n\r\n      };\r\n\r\n      React.useEffect(() => {\r\n            if (fetcher.data?.bankData) {\r\n                  applyFilters(fetcher.data.bankData);\r\n            }\r\n      }, [filters, searchTerm]);\r\n\r\n\r\n\r\n      const handleCheckboxChange = (status: string) => {\r\n            setFilters((prev) => {\r\n                  let newFilters;\r\n\r\n                  if (status === \"ALL\") {\r\n                        newFilters = [\"ALL\"];\r\n                  } else {\r\n                        if (prev.includes(status)) {\r\n                              newFilters = prev.filter((x) => x !== status);\r\n                        } else {\r\n                              newFilters = prev.filter((x) => x !== \"ALL\").concat(status);\r\n                        }\r\n                  }\r\n\r\n                  return newFilters;\r\n            });\r\n      };\r\n\r\n\r\n      ;\r\n\r\n      const formatDate = (dateString: string) => {\r\n            const date = new Date(dateString);\r\n            const options: Intl.DateTimeFormatOptions = {\r\n                  day: \"2-digit\",\r\n                  month: \"short\",\r\n                  hour: \"2-digit\",\r\n                  minute: \"2-digit\",\r\n                  hour12: true,\r\n            };\r\n            return date.toLocaleString(\"en-US\", options);\r\n      };\r\n\r\n\r\n      const handleSubmit = (date: Date) => {\r\n            if (!date) return;\r\n            const formattedDate = new Date(date);\r\n\r\n\r\n            fetcher.load(`/home/<USER>\"yyyy-MM-dd\")}`);\r\n      };\r\n\r\n      const paginatedData = React.useMemo(() => {\r\n            const start = (currentPage - 1) * Number(pageSize);\r\n            const end = start + Number(pageSize);\r\n            return [...data]\r\n                  .sort((a, b) => (a.id > b.id ? -1 : 1))\r\n                  .slice(start, end);\r\n      }, [data, currentPage, pageSize]);\r\n\r\n      const totalPages = Math.ceil(data.length / Number(pageSize));\r\n\r\n      return (\r\n            <React.Suspense fallback={<div>Loading...</div>}>\r\n                  <div>\r\n                        <div className=\"flex flex-col sm:flex-row sm:space-x-3 my-3\">\r\n                              <Form className=\"flex sm:space-x-2 mb-3 sm:mb-0\">\r\n                                    <Popover>\r\n                                          <PopoverTrigger asChild>\r\n                                                <Button variant=\"outline\" className=\"w-[280px]\">\r\n                                                      <CalendarIcon className=\"mr-2 h-4 w-4\" />\r\n                                                      {date ? format(date, \"PPP\") : \"Pick a date\"}\r\n                                                </Button>\r\n                                          </PopoverTrigger>\r\n                                          <PopoverContent className=\"w-auto p-0\" >\r\n                                                <Calendar\r\n                                                      mode=\"single\"\r\n                                                      selected={date}\r\n                                                      onSelect={(newDate) => {\r\n                                                            if (newDate) {\r\n                                                                  setDate(newDate);\r\n                                                            }\r\n                                                      }}\r\n                                                      initialFocus\r\n                                                />\r\n                                          </PopoverContent>\r\n                                    </Popover>\r\n\r\n                                    <Button onClick={() => date && handleSubmit(date)} >\r\n                                          {loading ? \"Submitting\" : \"Get Transactions\"}\r\n                                    </Button>\r\n                              </Form>\r\n                              <Input\r\n                                    placeholder=\"Search by name, id, or order id...\"\r\n                                    value={searchTerm}\r\n                                    onChange={(e) => setSearchTerm(e.target.value)}\r\n                                    className=\"max-w-sm\"\r\n                              />\r\n                              <Select value={pageSize} onValueChange={setPageSize}>\r\n                                    <SelectTrigger className=\"w-[180px]\">\r\n                                          <SelectValue placeholder=\"Rows per page\" />\r\n                                    </SelectTrigger>\r\n                                    <SelectContent>\r\n                                          <SelectItem value=\"5\">5 per page</SelectItem>\r\n                                          <SelectItem value=\"10\">10 per page</SelectItem>\r\n                                          <SelectItem value=\"20\">20 per page</SelectItem>\r\n                                          <SelectItem value=\"50\">50 per page</SelectItem>\r\n                                          <SelectItem value=\"100\">100 per page</SelectItem>\r\n                                    </SelectContent>\r\n                              </Select>\r\n                        </div>\r\n\r\n                        <div className=\"flex flex-wrap sm:flex-nowrap sm:space-x-8\">\r\n                              {[\"ALL\", \"INITIATED\", \"PENDING\", \"PAID\", \"FAILED\"].map((status) => (\r\n                                    <div key={status} className=\"flex items-center space-x-2 my-2\">\r\n                                          <Checkbox\r\n                                                id={status}\r\n                                                checked={filters.includes(status)}\r\n                                                onCheckedChange={() => handleCheckboxChange(status)}\r\n                                          />\r\n                                          <label\r\n                                                htmlFor={status}\r\n                                                className=\"text-sm font-medium leading-none\"\r\n                                          >\r\n                                                {status}\r\n                                          </label>\r\n                                    </div>\r\n                              ))}\r\n                        </div>\r\n\r\n                        <div className=\"overflow-x-auto rounded-md border\">\r\n                              <Table>\r\n                                    <TableHeader>\r\n                                          <TableRow>\r\n                                                <TableHead>Id</TableHead>\r\n                                                <TableHead>Name</TableHead>\r\n                                                <TableHead>Amount</TableHead>\r\n                                                <TableHead>Int Time</TableHead>\r\n                                                <TableHead>Status</TableHead>\r\n                                                <TableHead>Last Updated</TableHead>\r\n                                                <TableHead>Paid Time</TableHead>\r\n                                                <TableHead>Order Details</TableHead>\r\n                                                <TableHead>Bank Details</TableHead>\r\n                                          </TableRow>\r\n                                    </TableHeader>\r\n                                    <TableBody>\r\n                                          {paginatedData.length > 0 ? (\r\n                                                paginatedData.map((row) => (\r\n                                                      <TransactionRow\r\n                                                            key={row.id}\r\n                                                            row={row}\r\n                                                            formatDate={formatDate}\r\n                                                            adminPermission={adminPermission}\r\n                                                      />))\r\n                                          ) : (\r\n                                                <TableRow>\r\n                                                      <TableCell\r\n                                                            colSpan={9}\r\n                                                            className=\"h-24 text-center\"\r\n                                                      >\r\n                                                            No results.\r\n                                                      </TableCell>\r\n                                                </TableRow>\r\n                                          )}\r\n                                    </TableBody>\r\n                              </Table>\r\n                        </div>\r\n\r\n                        <div className=\"flex items-center justify-between px-2 py-4\">\r\n                              <div className=\"text-sm text-gray-500\">\r\n                                    Showing {(currentPage - 1) * Number(pageSize) + 1} to{\" \"}\r\n                                    {Math.min(currentPage * Number(pageSize), data.length)} of{\" \"}\r\n                                    {data.length} results\r\n                              </div>\r\n                              <div className=\"flex items-center space-x-2\">\r\n                                    <Button\r\n                                          variant=\"outline\"\r\n                                          size=\"sm\"\r\n                                          onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}\r\n                                          disabled={currentPage === 1}\r\n                                    >\r\n                                          Previous\r\n                                    </Button>\r\n                                    <Button\r\n                                          variant=\"outline\"\r\n                                          size=\"sm\"\r\n                                          onClick={() =>\r\n                                                setCurrentPage((prev) => Math.min(prev + 1, totalPages))\r\n                                          }\r\n                                          disabled={currentPage === totalPages}\r\n                                    >\r\n                                          Next\r\n                                    </Button>\r\n                              </div>\r\n                        </div>\r\n                  </div>\r\n            </React.Suspense>\r\n      );\r\n}\r\n\r\n\r\n\r\n\r\nfunction TransactionRow({\r\n      row,\r\n      formatDate,\r\n      adminPermission\r\n\r\n}: {\r\n      row: Transaction;\r\n      formatDate: (dateString: string) => string;\r\n      adminPermission: boolean\r\n}) {\r\n      const [showModal, setShowModal] = useState(false);\r\n      const [selectedTransaction, setSelectedTransaction] = useState<Transaction | null>(null);\r\n      const fetcher = useFetcher()\r\n\r\n\r\n\r\n      const [loadingRowId, setLoadingRowId] = React.useState<string | null>(null);\r\n\r\n      const handleUpdateTransaction = async (rowId: string) => {\r\n            setLoadingRowId(rowId); // Start loading for this row\r\n            const formData = new FormData();\r\n            formData.append(\"intent\", \"updateTransaction\");\r\n            formData.append(\"rowId\", rowId);\r\n\r\n            await fetcher.submit(formData, { method: \"PUT\" });\r\n\r\n            setLoadingRowId(null); // Stop loading\r\n      };\r\n      const handleShowModal = () => {\r\n            setSelectedTransaction(row);\r\n            setShowModal(true);\r\n      }\r\n\r\n      const isLoading = fetcher.state !== \"idle\";\r\n      return (\r\n            <>\r\n                  <TableRow key={row.id}>\r\n\r\n                        <TableCell>{row.id}</TableCell>\r\n                        <TableCell>{row.businessName}</TableCell>\r\n                        <TableCell>\r\n                              {new Intl.NumberFormat(\"en-US\", {\r\n                                    style: \"currency\",\r\n                                    currency: \"INR\",\r\n                              }).format(parseFloat(row.amount.toString()))}\r\n                        </TableCell>\r\n                        <TableCell>{formatDate(row.initiatedTime)}</TableCell>\r\n\r\n                        <TableCell className=\"flex flex-col gap-2 mt-2\">\r\n                              <button\r\n                                    className={`${row.status === \"PENDING\"\r\n                                          ? \"bg-orange-400 hover:bg-orange-200\"\r\n                                          : row.status === \"INITIATED\"\r\n                                                ? \"bg-blue-600 hover:bg-blue-400\"\r\n                                                : \"\"\r\n                                          } flex-1 cursor-pointer px-2 py-1 rounded flex items-center justify-center`}\r\n                                    onClick={() => handleUpdateTransaction(row.id.toString())}\r\n                                    disabled={row.status === \"PAID\" || row.status === \"FAILED\" || loadingRowId === row.id.toString()}\r\n                              >\r\n                                    {loadingRowId === row.id.toString() ? (\r\n                                          <span className=\"animate-spin border-2 border-red-200 border-t-transparent rounded-full w-5 h-5\"></span>\r\n                                    ) : (\r\n                                          <p\r\n                                                className={\r\n                                                      row.status === \"PAID\"\r\n                                                            ? \"font-bold text-green-600\"\r\n                                                            : row.status === \"FAILED\"\r\n                                                                  ? \"font-bold text-red-600\"\r\n                                                                  : \"\"\r\n                                                }\r\n                                          >\r\n                                                {isLoading ? \"updating...\" : row.status}\r\n                                          </p>\r\n                                    )}\r\n                              </button>\r\n\r\n                              {(row.status === \"FAILED\" || row.status === \"PENDING\") && adminPermission === true && <Button className=\"bg-sky-400 text-white font-bold\" onClick={handleShowModal}>\r\n                                    Mark As Paid\r\n                              </Button>}\r\n                        </TableCell>\r\n                        <TableCell>{formatDate(row.lastUpdatedTime)}</TableCell>\r\n                        <TableCell>{formatDate(row.completedTime)}</TableCell>\r\n                        <TableCell>\r\n                              <div className=\"space-y-1\">\r\n                                    <p>Id: {row.orderGroupId}</p>\r\n                                    <p>{row.orderStatus}</p>\r\n                                    <p>{row.sellerName}</p>\r\n                                    <p>{row.deliveryDate}</p>\r\n                              </div>\r\n                        </TableCell>\r\n                        <TableCell>\r\n                              <div className=\"space-y-1\">\r\n                                    <p>Id: {row.bankRRN}</p>\r\n                                    <p>Ch: {row.channel}</p>\r\n                                    <p>Note: {row.note}</p>\r\n                              </div>\r\n                        </TableCell>\r\n                  </TableRow>\r\n\r\n                  {/* Show modal when button is clicked */}\r\n                  <MarkAsPaid\r\n                        isClosed={() => setShowModal(false)}\r\n                        heading={`Transaction from ${selectedTransaction?.businessName ?? \"\"}`}\r\n                        isOpen={showModal}\r\n                        content=\"You are about to mark the payment status as paid. This is an irreversible action.\" row={selectedTransaction} />\r\n\r\n\r\n            </>\r\n      );\r\n}\r\n\r\n\r\n\r\n\r\n"], "names": ["useEffect", "jsxs", "jsx", "BankTransactions", "bankData", "adminPermission", "useLoaderData", "fetcher", "useFetcher", "data", "setData", "React", "date", "setDate", "Date", "filters", "setFilters", "searchTerm", "setSearchTerm", "loading", "setLoading", "pageSize", "setPageSize", "currentPage", "setCurrentPage", "applyFilters", "newData", "Array", "isArray", "filteredData", "filter", "trans", "includes", "status", "businessName", "toLowerCase", "orderGroupId", "toString", "amount", "id", "handleCheckboxChange", "prev", "newFilters", "x", "concat", "formatDate", "dateString", "options", "day", "month", "hour", "minute", "hour12", "toLocaleString", "handleSubmit", "formattedDate", "load", "format", "paginatedData", "start", "Number", "end", "sort", "a", "b", "slice", "totalPages", "Math", "ceil", "length", "fallback", "children", "className", "Form", "Popover", "PopoverTrigger", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "variant", "CalendarIcon", "PopoverC<PERSON>nt", "Calendar", "mode", "selected", "onSelect", "newDate", "initialFocus", "onClick", "Input", "placeholder", "value", "onChange", "e", "target", "Select", "onValueChange", "SelectTrigger", "SelectValue", "SelectContent", "SelectItem", "map", "Checkbox", "checked", "onCheckedChange", "htmlFor", "Table", "TableHeader", "TableRow", "TableHead", "TableBody", "row", "TransactionRow", "TableCell", "colSpan", "min", "size", "max", "disabled", "showModal", "setShowModal", "useState", "selectedTransaction", "setSelectedTransaction", "loadingRowId", "setLoadingRowId", "handleUpdateTransaction", "rowId", "formData", "FormData", "append", "submit", "method", "handleShowModal", "isLoading", "state", "Fragment", "Intl", "NumberFormat", "style", "currency", "parseFloat", "initiatedTime", "lastUpdatedTime", "completedTime", "orderStatus", "sellerName", "deliveryDate", "bankRRN", "channel", "note", "MarkAsPaid", "isClosed", "heading", "isOpen", "content"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgBA,SAAwB,WAAW,EAAE,SAAS,QAAQ,SAAS,UAAU,OAAwB;AAC3F,QAAM,UAAU,WAAW;AACrB,QAAA,EAAE,UAAU,IAAI,SAAS;AACzB,QAAA,YAAY,QAAQ,UAAU;AAEpCA,eAAAA,UAAU,MAAM;AACV,QAAI,QAAQ,MAAM;AACZ,UAAI,QAAQ,SAAS,UAAa,WAAW,MAAM;AAC7C,kBAAU,gBAAgB,SAAS;AAC1B,iBAAA;AAAA,MAAA,WACJ,QAAQ,SAAS,QAAW;AACjC,kBAAU,wBAAwB,OAAO;AAAA,MAAA;AAAA,IAC/C;AAAA,EACN,GACH,CAAC,QAAQ,MAAM,QAAQ,UAAU,SAAS,CAAC;AAI9C,QAAM,eAAe,MAAM;AACf,UAAA,WAAW,IAAI,SAAS;AACrB,aAAA,OAAO,UAAU,YAAY;AAC7B,aAAA,OAAO,UAAU,2BAAK,MAA2B;AACjD,aAAA,OAAO,aAAa,2BAAK,EAAuB;AAChD,aAAA,OAAO,QAAQ,cAAc;AAEtC,YAAQ,OAAO,UAAU,EAAE,QAAQ,QAAQ;AAAA,EACjD;AAGA,SACOC,kCAAAA,KAAA,QAAA,EAAO,MAAM,QAAQ,cAAc,UAK7B,UAAA;AAAA,IACK,aAAAC,kCAAAA,IAAC,SAAI,WAAU,kFACT,gDAAC,eAAc,EAAA,SAAS,WAAW,EACzC,CAAA;AAAA,0CAEL,eAAc,EAAA,WAAU,kDACnB,UAACD,kCAAA,KAAA,OAAA,EAAI,WAAU,mDACT,UAAA;AAAA,MAACC,kCAAA,IAAA,aAAA,EAAY,WAAU,qBAAqB,UAAQ,SAAA;AAAA,6CACnD,QAAK,EAAA,UAAU,MAAM,aAAa,GAAG,WAAU,aAC1C,UAAA;AAAA,QAACA,kCAAA,IAAA,KAAA,EAAE,WAAU,kCAAkC,UAAQ,SAAA;AAAA,QACvDD,kCAAAA,KAAC,OAAI,EAAA,WAAU,8BACT,UAAA;AAAA,UAAAC,kCAAA;AAAA,YAAC;AAAA,YAAA;AAAA,cACK,MAAK;AAAA,cACL,SAAS;AAAA,cACT,WAAU;AAAA,cACf,UAAA;AAAA,YAAA;AAAA,UAED;AAAA,UACAA,kCAAA;AAAA,YAAC;AAAA,YAAA;AAAA,cACK,MAAK;AAAA,cACL,WAAU;AAAA,cAET,sBAAY,kBAAkB;AAAA,YAAA;AAAA,UAAA;AAAA,QACrC,EACN,CAAA;AAAA,MAAA,EACN,CAAA;AAAA,IAAA,EAAA,CACN,EACN,CAAA;AAAA,EAAA,GACN;AAEZ;AC4BA,SAAwBC,mBAAmB;AACrC,QAAM;AAAA,IAAEC;AAAAA,IAAUC;AAAAA,EAAgB,IAAIC,cAA0B;AAChE,QAAMC,UAAUC,WAAwC;AACxD,QAAM,CAACC,MAAMC,OAAO,IAAIC,aAAAA,SAA8BP,QAAQ;AACxD,QAAA,CAACQ,MAAMC,OAAO,IAAIF,aAAAA,SAAiC,oBAAIG,MAAM;AAC7D,QAAA,CAACC,SAASC,UAAU,IAAIL,aAAAA,SAAyB,CAAC,KAAK,CAAC;AAC9D,QAAM,CAACM,YAAYC,aAAa,IAAIP,aAAAA,SAAe,EAAE;AACrD,QAAM,CAACQ,SAASC,UAAU,IAAIT,aAAAA,SAAe,KAAK;AAClD,QAAM,CAACU,UAAUC,WAAW,IAAIX,aAAAA,SAAe,KAAK;AACpD,QAAM,CAACY,aAAaC,cAAc,IAAIb,aAAAA,SAAe,CAAC;AAEtDA,eAAAA,UAAgB,MAAM;;AACZ,SAAAJ,aAAQE,SAARF,mBAAcH,UAAU;AACTqB,mBAAAlB,QAAQE,KAAKL,QAAQ;AAAA,IACxC;AAAA,KACH,CAACG,QAAQE,MAAMM,SAASE,UAAU,CAAC;AAEhC,QAAAQ,eAAgBC,aAA2B;AAC3C,QAAI,CAACC,MAAMC,QAAQF,OAAO,GAAG;AACvB,aAAO,CAAC;AAAA,IACd,OACK;AACC,YAAMG,eAAeH,mCAASI,OACvBC,WACM;;AAAAhB,wBAAQiB,SAAS,KAAK,KAAKjB,QAAQiB,SAASD,MAAME,MAAM,SAEnDF,WAAMG,iBAANH,mBAAoBI,cAAcH,SAASf,WAAWkB,YAAa,SACnEJ,WAAMK,iBAANL,mBAAoBM,WAAWL,SAASf,kBACxCc,WAAMO,WAANP,mBAAcM,WAAWL,SAASf,kBAClCc,WAAMQ,OAANR,mBAAUM,WAAWL,SAASf;AAAAA;AAGhDP,cAAQmB,YAAY;AACpBL,qBAAe,CAAC;AAAA,IACtB;AAAA,EAGN;AAEAb,eAAAA,UAAgB,MAAM;;AACZ,SAAAJ,aAAQE,SAARF,mBAAcH,UAAU;AACTqB,mBAAAlB,QAAQE,KAAKL,QAAQ;AAAA,IACxC;AAAA,EACN,GAAG,CAACW,SAASE,UAAU,CAAC;AAIlB,QAAAuB,uBAAwBP,YAAmB;AAC3CjB,eAAYyB,UAAS;AACX,UAAAC;AAEJ,UAAIT,WAAW,OAAO;AAChBS,qBAAa,CAAC,KAAK;AAAA,MACzB,OAAO;AACG,YAAAD,KAAKT,SAASC,MAAM,GAAG;AACrBS,uBAAaD,KAAKX,OAAQa,OAAMA,MAAMV,MAAM;AAAA,QAClD,OAAO;AACYS,uBAAAD,KAAKX,OAAQa,OAAMA,MAAM,KAAK,EAAEC,OAAOX,MAAM;AAAA,QAChE;AAAA,MACN;AAEO,aAAAS;AAAAA,IACb,CAAC;AAAA,EACP;AAKM,QAAAG,aAAcC,gBAAuB;AAC/BlC,UAAAA,QAAO,IAAIE,KAAKgC,UAAU;AAChC,UAAMC,UAAsC;AAAA,MACtCC,KAAK;AAAA,MACLC,OAAO;AAAA,MACPC,MAAM;AAAA,MACNC,QAAQ;AAAA,MACRC,QAAQ;AAAA,IACd;AACOxC,WAAAA,MAAKyC,eAAe,SAASN,OAAO;AAAA,EACjD;AAGM,QAAAO,eAAgB1C,WAAe;AAC/B,QAAI,CAACA,MAAM;AACL,UAAA2C,gBAAgB,IAAIzC,KAAKF,KAAI;AAGnCL,YAAQiD,KAAK,+BAA+BC,OAAOF,eAAe,YAAY,CAAC,EAAE;AAAA,EACvF;AAEM,QAAAG,gBAAgB/C,aAAAA,QAAc,MAAM;AACpC,UAAMgD,SAASpC,cAAc,KAAKqC,OAAOvC,QAAQ;AAC3C,UAAAwC,MAAMF,QAAQC,OAAOvC,QAAQ;AACnC,WAAO,CAAC,GAAGZ,IAAI,EACRqD,KAAK,CAACC,GAAGC,MAAOD,EAAExB,KAAKyB,EAAEzB,KAAK,KAAK,CAAE,EACrC0B,MAAMN,OAAOE,GAAG;AAAA,EAC1B,GAAA,CAACpD,MAAMc,aAAaF,QAAQ,CAAC;AAEhC,QAAM6C,aAAaC,KAAKC,KAAK3D,KAAK4D,SAAST,OAAOvC,QAAQ,CAAC;AAGrD,SAAAnB,kCAAAA,IAACS,aAAAA,UAAA;AAAA,IAAe2D,gDAAW,OAAI;AAAA,MAAAC,UAAA;AAAA,IAAA,CAAU;AAAA,IACnCA,UAAAtE,kCAAA,KAAC,OACK;AAAA,MAAAsE,UAAA,CAACtE,kCAAA,KAAA,OAAA;AAAA,QAAIuE,WAAU;AAAA,QACTD,UAAA,CAACtE,kCAAA,KAAAwE,MAAA;AAAA,UAAKD,WAAU;AAAA,UACVD,UAAA,CAAAtE,kCAAA,KAACyE,SACK;AAAA,YAAAH,UAAA,CAACrE,kCAAA,IAAAyE,gBAAA;AAAA,cAAeC,SAAO;AAAA,cACjBL,UAAAtE,kCAAA,KAAC4E;gBAAOC,SAAQ;AAAA,gBAAUN,WAAU;AAAA,gBAC9BD,UAAA,CAACrE,kCAAA,IAAA6E,UAAA;AAAA,kBAAaP,WAAU;AAAA,gBAAe,CAAA,GACtC5D,OAAO6C,OAAO7C,MAAM,KAAK,IAAI,aAAA;AAAA,cACpC,CAAA;AAAA,YACN,CAAA,GACAV,kCAAA,IAAC8E,gBAAe;AAAA,cAAAR,WAAU;AAAA,cACpBD,UAAArE,kCAAA,IAAC+E,YAAA;AAAA,gBACKC,MAAK;AAAA,gBACLC,UAAUvE;AAAAA,gBACVwE,UAAWC,aAAY;AACjB,sBAAIA,SAAS;AACPxE,4BAAQwE,OAAO;AAAA,kBACrB;AAAA,gBACN;AAAA,gBACAC,cAAY;AAAA,cAClB,CAAA;AAAA,YACN,CAAA,CAAA;AAAA,UACN,CAAA,GAEApF,kCAAA,IAAC2E,QAAO;AAAA,YAAAU,SAASA,MAAM3E,QAAQ0C,aAAa1C,IAAI;AAAA,YACzC2D,UAAUpD,UAAA,eAAe;AAAA,UAChC,CAAA,CAAA;AAAA,QACN,CAAA,GACAjB,kCAAA,IAACsF,OAAA;AAAA,UACKC,aAAY;AAAA,UACZC,OAAOzE;AAAAA,UACP0E,UAAWC,OAAM1E,cAAc0E,EAAEC,OAAOH,KAAK;AAAA,UAC7ClB,WAAU;AAAA,QAAA,CAChB,GACCvE,kCAAA,KAAA6F,QAAA;AAAA,UAAOJ,OAAOrE;AAAAA,UAAU0E,eAAezE;AAAAA,UAClCiD,UAAA,CAAArE,kCAAA,IAAC8F;YAAcxB,WAAU;AAAA,YACnBD,gDAAC0B,aAAY;AAAA,cAAAR,aAAY;AAAA,YAAgB,CAAA;AAAA,UAC/C,CAAA,0CACCS,eACK;AAAA,YAAA3B,UAAA,CAACrE,kCAAA,IAAAiG,YAAA;AAAA,cAAWT,OAAM;AAAA,cAAInB,UAAU;AAAA,YAAA,CAAA,GAC/BrE,kCAAA,IAAAiG,YAAA;AAAA,cAAWT,OAAM;AAAA,cAAKnB,UAAW;AAAA,YAAA,CAAA,GACjCrE,kCAAA,IAAAiG,YAAA;AAAA,cAAWT,OAAM;AAAA,cAAKnB,UAAW;AAAA,YAAA,CAAA,GACjCrE,kCAAA,IAAAiG,YAAA;AAAA,cAAWT,OAAM;AAAA,cAAKnB,UAAW;AAAA,YAAA,CAAA,GACjCrE,kCAAA,IAAAiG,YAAA;AAAA,cAAWT,OAAM;AAAA,cAAMnB,UAAY;AAAA,YAAA,CAAA,CAAA;AAAA,UAC1C,CAAA,CAAA;AAAA,QACN,CAAA,CAAA;AAAA,MACN,CAAA,yCAEC,OAAI;AAAA,QAAAC,WAAU;AAAA,QACRD,UAAA,CAAC,OAAO,aAAa,WAAW,QAAQ,QAAQ,EAAE6B,IAAKnE,YACjDhC,kCAAAA,KAAA,OAAA;AAAA,UAAiBuE,WAAU;AAAA,UACtBD,UAAA,CAAArE,kCAAA,IAACmG,UAAA;AAAA,YACK9D,IAAIN;AAAAA,YACJqE,SAASvF,QAAQiB,SAASC,MAAM;AAAA,YAChCsE,iBAAiBA,MAAM/D,qBAAqBP,MAAM;AAAA,UAAA,CACxD,GACA/B,kCAAA,IAAC,SAAA;AAAA,YACKsG,SAASvE;AAAAA,YACTuC,WAAU;AAAA,YAETD,UAAAtC;AAAAA,UAAA,CACP,CAAA;AAAA,QAXI,GAAAA,MAYV,CACL;AAAA,MACP,CAAA,GAEC/B,kCAAA,IAAA,OAAA;AAAA,QAAIsE,WAAU;AAAA,QACTD,iDAACkC,OACK;AAAA,UAAAlC,UAAA,CAACrE,kCAAA,IAAAwG,aAAA;AAAA,YACKnC,iDAACoC,UACK;AAAA,cAAApC,UAAA,CAAArE,kCAAA,IAAC0G;gBAAUrC,UAAE;AAAA,cAAA,CAAA,GACbrE,kCAAA,IAAC0G;gBAAUrC,UAAI;AAAA,cAAA,CAAA,GACfrE,kCAAA,IAAC0G;gBAAUrC,UAAM;AAAA,cAAA,CAAA,GACjBrE,kCAAA,IAAC0G;gBAAUrC,UAAQ;AAAA,cAAA,CAAA,GACnBrE,kCAAA,IAAC0G;gBAAUrC,UAAM;AAAA,cAAA,CAAA,GACjBrE,kCAAA,IAAC0G;gBAAUrC,UAAY;AAAA,cAAA,CAAA,GACvBrE,kCAAA,IAAC0G;gBAAUrC,UAAS;AAAA,cAAA,CAAA,GACpBrE,kCAAA,IAAC0G;gBAAUrC,UAAa;AAAA,cAAA,CAAA,GACxBrE,kCAAA,IAAC0G;gBAAUrC,UAAY;AAAA,cAAA,CAAA,CAAA;AAAA,YAC7B,CAAA;AAAA,UACN,CAAA,GACArE,kCAAA,IAAC2G;YACMtC,UAAcb,cAAAW,SAAS,IAClBX,cAAc0C,IAAKU,SACb5G,kCAAA,IAAC6G,gBAAA;AAAA,cAEKD;AAAAA,cACAjE;AAAAA,cACAxC;AAAAA,YAAA,GAHKyG,IAAIvE,EAIf,CAAG,IAETrC,kCAAAA,IAACyG,UACK;AAAA,cAAApC,UAAArE,kCAAA,IAAC8G,WAAA;AAAA,gBACKC,SAAS;AAAA,gBACTzC,WAAU;AAAA,gBACfD,UAAA;AAAA,cAED,CAAA;AAAA,YACN,CAAA;AAAA,UAEZ,CAAA,CAAA;AAAA,QACN,CAAA;AAAA,MACN,CAAA,GAEAtE,kCAAA,KAAC,OAAI;AAAA,QAAAuE,WAAU;AAAA,QACTD,UAAA,CAACtE,kCAAA,KAAA,OAAA;AAAA,UAAIuE,WAAU;AAAA,UAAwBD,UAAA,CAAA,aACvBhD,cAAc,KAAKqC,OAAOvC,QAAQ,IAAI,GAAE,OAAI,KACrD8C,KAAK+C,IAAI3F,cAAcqC,OAAOvC,QAAQ,GAAGZ,KAAK4D,MAAM,GAAE,OAAI,KAC1D5D,KAAK4D,QAAO,UAAA;AAAA,QACnB,CAAA,GACApE,kCAAA,KAAC,OAAI;AAAA,UAAAuE,WAAU;AAAA,UACTD,UAAA,CAAArE,kCAAA,IAAC2E,QAAA;AAAA,YACKC,SAAQ;AAAA,YACRqC,MAAK;AAAA,YACL5B,SAASA,MAAM/D,eAAgBiB,UAAS0B,KAAKiD,IAAI3E,OAAO,GAAG,CAAC,CAAC;AAAA,YAC7D4E,UAAU9F,gBAAgB;AAAA,YAC/BgD,UAAA;AAAA,UAAA,CAED,GACArE,kCAAA,IAAC2E,QAAA;AAAA,YACKC,SAAQ;AAAA,YACRqC,MAAK;AAAA,YACL5B,SAASA,MACH/D,eAAgBiB,UAAS0B,KAAK+C,IAAIzE,OAAO,GAAGyB,UAAU,CAAC;AAAA,YAE7DmD,UAAU9F,gBAAgB2C;AAAAA,YAC/BK,UAAA;AAAA,UAAA,CAED,CAAA;AAAA,QACN,CAAA,CAAA;AAAA,MACN,CAAA,CAAA;AAAA,IACN,CAAA;AAAA,EACN,CAAA;AAEZ;AAKA,SAASwC,eAAe;AAAA,EAClBD;AAAAA,EACAjE;AAAAA,EACAxC;AAEN,GAIG;AACG,QAAM,CAACiH,WAAWC,YAAY,IAAIC,aAAAA,SAAS,KAAK;AAChD,QAAM,CAACC,qBAAqBC,sBAAsB,IAAIF,aAAAA,SAA6B,IAAI;AACvF,QAAMjH,UAAUC,WAAW;AAI3B,QAAM,CAACmH,cAAcC,eAAe,IAAIjH,aAAAA,SAA8B,IAAI;AAEpE,QAAAkH,0BAA0B,OAAOC,UAAkB;AACnDF,oBAAgBE,KAAK;AACf,UAAAC,WAAW,IAAIC,SAAS;AACrBD,aAAAE,OAAO,UAAU,mBAAmB;AACpCF,aAAAE,OAAO,SAASH,KAAK;AAE9B,UAAMvH,QAAQ2H,OAAOH,UAAU;AAAA,MAAEI,QAAQ;AAAA,IAAM,CAAC;AAEhDP,oBAAgB,IAAI;AAAA,EAC1B;AACA,QAAMQ,kBAAkBA,MAAM;AACxBV,2BAAuBZ,GAAG;AAC1BS,iBAAa,IAAI;AAAA,EACvB;AAEM,QAAAc,YAAY9H,QAAQ+H,UAAU;AACpC,SAEYrI,kCAAAA,KAAAsI,kBAAAA,UAAA;AAAA,IAAAhE,UAAA,CAAAtE,kCAAA,KAAC0G,UAEK;AAAA,MAAApC,UAAA,CAACrE,kCAAA,IAAA8G,WAAA;AAAA,QAAWzC,cAAIhC;AAAAA,MAAG,CAAA,GACnBrC,kCAAA,IAAC8G,WAAW;AAAA,QAAAzC,UAAAuC,IAAI5E;AAAAA,MAAa,CAAA,GAC5BhC,kCAAA,IAAA8G,WAAA;AAAA,QACMzC,UAAI,IAAAiE,KAAKC,aAAa,SAAS;AAAA,UAC1BC,OAAO;AAAA,UACPC,UAAU;AAAA,QAChB,CAAC,EAAElF,OAAOmF,WAAW9B,IAAIxE,OAAOD,SAAA,CAAU,CAAC;AAAA,MACjD,CAAA,GACCnC,kCAAA,IAAA8G,WAAA;AAAA,QAAWzC,UAAW1B,WAAAiE,IAAI+B,aAAa;AAAA,MAAE,CAAA,GAE1C5I,kCAAA,KAAC+G,WAAU;AAAA,QAAAxC,WAAU;AAAA,QACfD,UAAA,CAAArE,kCAAA,IAAC,UAAA;AAAA,UACKsE,WAAW,GAAGsC,IAAI7E,WAAW,YACrB,sCACA6E,IAAI7E,WAAW,cACT,kCACA,EACR;AAAA,UACNsD,SAASA,MAAMsC,wBAAwBf,IAAIvE,GAAGF,UAAU;AAAA,UACxDgF,UAAUP,IAAI7E,WAAW,UAAU6E,IAAI7E,WAAW,YAAY0F,iBAAiBb,IAAIvE,GAAGF,SAAS;AAAA,UAE9FkC,UAAAoD,iBAAiBb,IAAIvE,GAAGF,aAClBnC,kCAAA,IAAA,QAAA;AAAA,YAAKsE,WAAU;AAAA,UAAA,CAAiF,IAEjGtE,kCAAA,IAAC,KAAA;AAAA,YACKsE,WACMsC,IAAI7E,WAAW,SACP,6BACA6E,IAAI7E,WAAW,WACT,2BACA;AAAA,YAGnBsC,UAAA8D,YAAY,gBAAgBvB,IAAI7E;AAAAA,UACvC,CAAA;AAAA,QAEZ,CAAA,IAEE6E,IAAI7E,WAAW,YAAY6E,IAAI7E,WAAW,cAAc5B,oBAAoB,QAAQH,kCAAAA,IAAC2E,QAAO;AAAA,UAAAL,WAAU;AAAA,UAAkCe,SAAS6C;AAAAA,UAAiB7D,UAEpK;AAAA,QAAA,CAAA,CAAA;AAAA,MACN,CAAA,GACCrE,kCAAA,IAAA8G,WAAA;AAAA,QAAWzC,UAAW1B,WAAAiE,IAAIgC,eAAe;AAAA,MAAE,CAAA,GAC3C5I,kCAAA,IAAA8G,WAAA;AAAA,QAAWzC,UAAW1B,WAAAiE,IAAIiC,aAAa;AAAA,MAAE,CAAA,GACzC7I,kCAAA,IAAA8G,WAAA;AAAA,QACKzC,UAACtE,kCAAA,KAAA,OAAA;AAAA,UAAIuE,WAAU;AAAA,UACTD,UAAA,CAAAtE,kCAAA,KAAC,KAAE;AAAA,YAAAsE,UAAA,CAAA,QAAKuC,IAAI1E,YAAA;AAAA,UAAa,CAAA,GACzBlC,kCAAA,IAAC,KAAG;AAAA,YAAAqE,UAAAuC,IAAIkC;AAAAA,UAAY,CAAA,GACpB9I,kCAAA,IAAC,KAAG;AAAA,YAAAqE,UAAAuC,IAAImC;AAAAA,UAAW,CAAA,GACnB/I,kCAAA,IAAC,KAAG;AAAA,YAAAqE,UAAAuC,IAAIoC;AAAAA,UAAa,CAAA,CAAA;AAAA,QAC3B,CAAA;AAAA,MACN,CAAA,GACChJ,kCAAA,IAAA8G,WAAA;AAAA,QACKzC,UAACtE,kCAAA,KAAA,OAAA;AAAA,UAAIuE,WAAU;AAAA,UACTD,UAAA,CAAAtE,kCAAA,KAAC,KAAE;AAAA,YAAAsE,UAAA,CAAA,QAAKuC,IAAIqC,OAAA;AAAA,UAAQ,CAAA,0CACnB,KAAE;AAAA,YAAA5E,UAAA,CAAA,QAAKuC,IAAIsC,OAAA;AAAA,UAAQ,CAAA,0CACnB,KAAE;AAAA,YAAA7E,UAAA,CAAA,UAAOuC,IAAIuC,IAAA;AAAA,UAAK,CAAA,CAAA;AAAA,QACzB,CAAA;AAAA,MACN,CAAA,CAAA;AAAA,IAAA,GA5DSvC,IAAIvE,EA6DnB,GAGArC,kCAAAA,IAACoJ,YAAA;AAAA,MACKC,UAAUA,MAAMhC,aAAa,KAAK;AAAA,MAClCiC,SAAS,qBAAoB/B,2DAAqBvF,iBAAgB,EAAE;AAAA,MACpEuH,QAAQnC;AAAAA,MACRoC,SAAQ;AAAA,MAAoF5C,KAAKW;AAAAA,IAAA,CAAqB,CAAA;AAAA,EAGlI,CAAA;AAEZ;"}