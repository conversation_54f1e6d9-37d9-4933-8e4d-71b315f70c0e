{"version": 3, "file": "home.fmMetaHourlySales-B_A_F2nt.js", "sources": ["../../../app/routes/home.fmMetaHourlySales.tsx"], "sourcesContent": ["import { useLoaderData, useSubmit, useSearchParams } from \"@remix-run/react\";\r\nimport { LoaderFunction, json } from \"@remix-run/node\";\r\nimport jwt from \"jsonwebtoken\";\r\nimport { useEffect, useState } from \"react\";\r\nimport { withAuth } from \"~/utils/auth-utils\";\r\n\r\n// Environment variables\r\nconst METABASE_SECRET_KEY = process.env.METABASE_SECRET_KEY || \"\";\r\nconst METABASE_SITE_URL = process.env.METABASE_SITE_URL || \"http://43.205.118.52:4001\";\r\n\r\nexport const loader: LoaderFunction = withAuth(async ({ request, user }) => {\r\n      const sellerId = user.sellerId;\r\n\r\n      // Get the date from query parameters\r\n      const url = new URL(request.url);\r\n\r\n\r\n      const dateRange = url.searchParams.get(\"dateRange\") || new Date().toISOString().split(\"T\")[0]; // Default to today\r\n\r\n      if (!METABASE_SECRET_KEY) {\r\n            throw new Error(\"Metabase secret key is not configured.\");\r\n      }\r\n\r\n      const payload = {\r\n            resource: { question: 79 },\r\n            params: {\r\n                  // Pass the selected date to Metabase\r\n            },\r\n            exp: Math.round(Date.now() / 1000) + 10 * 60, // Expires in 10 minutes\r\n      };\r\n\r\n      const token = jwt.sign(payload, METABASE_SECRET_KEY);\r\n      const embedUrl = `${METABASE_SITE_URL}/embed/question/${token}#bordered=false&titled=false`;\r\n\r\n      return json({ embedUrl, dateRange });\r\n});\r\n\r\nexport default function MetaDashboard() {\r\n      const { embedUrl, dateRange } = useLoaderData<typeof loader>();\r\n      const [isLoading, setIsLoading] = useState(true);\r\n      const submit = useSubmit();\r\n      const [searchParams] = useSearchParams();\r\n\r\n      // Handle iframe loading state\r\n      useEffect(() => {\r\n            if (embedUrl) {\r\n                  setIsLoading(false);\r\n            }\r\n      }, [embedUrl]);\r\n\r\n      // Scroll synchronization\r\n      useEffect(() => {\r\n            const handleScroll = () => {\r\n                  const iframe = document.getElementById(\"metabase-iframe\") as HTMLIFrameElement;\r\n                  if (iframe) {\r\n                        iframe.contentWindow?.scrollTo(0, window.scrollY);\r\n                  }\r\n            };\r\n\r\n            window.addEventListener(\"scroll\", handleScroll);\r\n            return () => window.removeEventListener(\"scroll\", handleScroll);\r\n      }, []);\r\n\r\n      // Handle date input change\r\n      // const handleDateRangeChange = (event: React.ChangeEvent<HTMLInputElement>) => {\r\n      //       const newDateRange = event.target.value;\r\n      //       submit({ dateRange: newDateRange }, { method: \"get\", action: \".\" });\r\n      // };\r\n\r\n      return (\r\n            <div className=\"flex min-h-screen\">\r\n                  <main className=\"flex-1 overflow-y-auto\">\r\n                        <div className=\"p-4 sm:p-6\">\r\n                              <div className=\"flex justify-between items-center mb-4\">\r\n                                    <h1 className=\"text-2xl font-bold text-gray-800\">FOS Hourly Sales Report</h1>\r\n                                    {/* <div>\r\n                                          <label htmlFor=\"dateRange\" className=\"mr-2 text-gray-700\">\r\n                                                Select Delivery Date:\r\n                                          </label>\r\n                                          <input\r\n                                                type=\"date\"\r\n                                                id=\"dateRange\"\r\n                                                name=\"dateRange\"\r\n                                                value={dateRange}\r\n                                                onChange={handleDateRangeChange}\r\n                                                className=\"border border-gray-300 rounded-md p-2 focus:outline-none focus:ring-2 focus:ring-blue-500\"\r\n                                                max={new Date().toISOString().split(\"T\")[0]} // Optional: Prevent future dates\r\n                                          />\r\n                                    </div> */}\r\n                              </div>\r\n\r\n                              <div className=\"bg-white shadow-md rounded-md overflow-hidden\">\r\n                                    {isLoading ? (\r\n                                          <div className=\"flex justify-center items-center h-96\">\r\n                                                <div className=\"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500\"></div>\r\n                                          </div>\r\n                                    ) : embedUrl ? (\r\n                                          <iframe\r\n                                                id=\"metabase-iframe\"\r\n                                                src={embedUrl}\r\n                                                title=\"Metabase Dashboard\"\r\n                                                className=\"w-full h-[600px] border-0\"\r\n                                                allowTransparency\r\n                                          />\r\n                                    ) : (\r\n                                          <div className=\"p-6 text-center text-red-500\">\r\n                                                Failed to load the dashboard.\r\n                                          </div>\r\n                                    )}\r\n                              </div>\r\n                        </div>\r\n                  </main>\r\n            </div>\r\n      );\r\n}"], "names": ["MetaDashboard", "embedUrl", "date<PERSON><PERSON><PERSON>", "useLoaderData", "isLoading", "setIsLoading", "useState", "useSubmit", "useSearchParams", "useEffect", "handleScroll", "iframe", "document", "getElementById", "contentWindow", "scrollTo", "window", "scrollY", "addEventListener", "removeEventListener", "jsx", "className", "children", "jsxs", "id", "src", "title", "allowTransparency"], "mappings": ";;;;AAqCA,SAAwBA,gBAAgB;AAClC,QAAM;AAAA,IAAEC;AAAAA,IAAUC;AAAAA,EAAU,IAAIC,cAA6B;AAC7D,QAAM,CAACC,WAAWC,YAAY,IAAIC,aAAAA,SAAS,IAAI;AAChCC,YAAU;AACFC,kBAAgB;AAGvCC,eAAAA,UAAU,MAAM;AACV,QAAIR,UAAU;AACRI,mBAAa,KAAK;AAAA,IACxB;AAAA,EACN,GAAG,CAACJ,QAAQ,CAAC;AAGbQ,eAAAA,UAAU,MAAM;AACV,UAAMC,eAAeA,MAAM;;AACf,YAAAC,SAASC,SAASC,eAAe,iBAAiB;AACxD,UAAIF,QAAQ;AACNA,qBAAOG,kBAAPH,mBAAsBI,SAAS,GAAGC,OAAOC;AAAAA,MAC/C;AAAA,IACN;AAEOD,WAAAE,iBAAiB,UAAUR,YAAY;AAC9C,WAAO,MAAMM,OAAOG,oBAAoB,UAAUT,YAAY;AAAA,EACpE,GAAG,EAAE;AASC,SAAAU,kCAAAA,IAAC,OAAI;AAAA,IAAAC,WAAU;AAAA,IACTC,UAAAF,kCAAA,IAAC,QAAK;AAAA,MAAAC,WAAU;AAAA,MACVC,UAAAC,kCAAA,KAAC,OAAI;AAAA,QAAAF,WAAU;AAAA,QACTC,UAAA,CAACF,kCAAA,IAAA,OAAA;AAAA,UAAIC,WAAU;AAAA,UACTC,UAAAF,kCAAA,IAAC;YAAGC,WAAU;AAAA,YAAmCC;UAAuB,CAAA;AAAA,QAe9E,CAAA,GAECF,kCAAA,IAAA,OAAA;AAAA,UAAIC,WAAU;AAAA,UACRC,sBACMF,kCAAA,IAAA,OAAA;AAAA,YAAIC,WAAU;AAAA,YACTC,gDAAC,OAAI;AAAA,cAAAD,WAAU;AAAA,YAA4E,CAAA;AAAA,WACjG,IACFpB,WACEmB,kCAAA,IAAC,UAAA;AAAA,YACKI,IAAG;AAAA,YACHC,KAAKxB;AAAAA,YACLyB,OAAM;AAAA,YACNL,WAAU;AAAA,YACVM,mBAAiB;AAAA,UAAA,CACvB,IAECP,kCAAA,IAAA,OAAA;AAAA,YAAIC,WAAU;AAAA,YAA+BC;UAE9C,CAAA;AAAA,QAEZ,CAAA,CAAA;AAAA,MACN,CAAA;AAAA,IACN,CAAA;AAAA,EACN,CAAA;AAEZ;"}