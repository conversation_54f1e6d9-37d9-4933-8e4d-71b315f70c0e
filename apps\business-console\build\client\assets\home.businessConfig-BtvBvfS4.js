import { r as reactExports, j as jsxRuntimeExports } from "./jsx-runtime-bB2y7OuD.js";
import { C as Checkbox } from "./checkbox-DoRUSdrQ.js";
import { I as Input } from "./input-3v87qohQ.js";
import { T as Table, a as TableHeader, b as TableRow, c as TableHead, d as TableBody, e as TableCell } from "./table-CAxdJZsY.js";
import "./index-z_byfFrQ.js";
import "./index-D7VH9Fc8.js";
import "./index-dIGjFc0I.js";
import "./index-CpKiYcZd.js";
import "./index-CkL5tk39.js";
import "./index-QLGF6kQx.js";
import "./index-CzZ6EYkN.js";
import "./index-Vp2vNLNM.js";
import "./utils-GkgzjW3c.js";
import "./check-_dbWxIzT.js";
import "./createLucideIcon-uwkRm45G.js";
function BusinessConfig() {
  const [searchTerm, setSearchTerm] = reactExports.useState("");
  return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
    className: "container mx-auto p-6",
    children: [/* @__PURE__ */ jsxRuntimeExports.jsx("div", {
      className: "flex justify-between items-center mb-6",
      children: /* @__PURE__ */ jsxRuntimeExports.jsx("h1", {
        className: "text-2xl font-bold",
        children: "Business Config"
      })
    }), /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
      className: "flex justify-between mb-4",
      children: /* @__PURE__ */ jsxRuntimeExports.jsx(Input, {
        placeholder: "Search by name or owner",
        value: searchTerm,
        onChange: (e) => setSearchTerm(e.target.value),
        className: "max-w-sm"
      })
    }), /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
      className: "flex space-x-8",
      children: ["Seller", "Business", "Supplier", "Agent", "ALL"].map((status) => /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
        className: "flex items-center space-x-2 my-2",
        children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Checkbox, {
          id: status,
          checked: true,
          onCheckedChange: () => {
          }
        }), /* @__PURE__ */ jsxRuntimeExports.jsx("label", {
          htmlFor: status,
          className: "text-sm font-medium leading-none",
          children: status
        })]
      }, status))
    }), /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
      className: "rounded-md border",
      children: /* @__PURE__ */ jsxRuntimeExports.jsxs(Table, {
        children: [/* @__PURE__ */ jsxRuntimeExports.jsx(TableHeader, {
          children: /* @__PURE__ */ jsxRuntimeExports.jsxs(TableRow, {
            children: [/* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
              children: "ID"
            }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
              children: "Name"
            }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
              children: " Owner/ Mobile"
            }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
              children: "Enable"
            })]
          })
        }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableBody, {}), /* @__PURE__ */ jsxRuntimeExports.jsx(TableRow, {
          children: /* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
            colSpan: 9,
            className: "h-24 text-center",
            children: "No results."
          })
        })]
      })
    })]
  });
}
export {
  BusinessConfig as default
};
//# sourceMappingURL=home.businessConfig-BtvBvfS4.js.map
