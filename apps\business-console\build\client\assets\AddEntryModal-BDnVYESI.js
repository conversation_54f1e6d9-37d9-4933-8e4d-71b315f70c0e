import { c as createLucideIcon } from "./createLucideIcon-uwkRm45G.js";
import { r as reactExports, j as jsxRuntimeExports } from "./jsx-runtime-bB2y7OuD.js";
import { B as Button } from "./button-ByAXMyvk.js";
import { D as Dialog, a as <PERSON><PERSON>Content, c as <PERSON><PERSON><PERSON>eader, b as <PERSON><PERSON>Title, e as <PERSON>alogFooter } from "./dialog-BqKosxNq.js";
/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */
const Printer = createLucideIcon("Printer", [
  [
    "path",
    {
      d: "M6 18H4a2 2 0 0 1-2-2v-5a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v5a2 2 0 0 1-2 2h-2",
      key: "143wyd"
    }
  ],
  ["path", { d: "M6 9V3a1 1 0 0 1 1-1h10a1 1 0 0 1 1 1v6", key: "1itne7" }],
  ["rect", { x: "6", y: "14", width: "12", height: "8", rx: "1", key: "1ue0tg" }]
]);
var StockTransactionType = /* @__PURE__ */ ((StockTransactionType2) => {
  StockTransactionType2["RECEIVED"] = "RECEIVED";
  StockTransactionType2["DELIVERED"] = "DELIVERED";
  StockTransactionType2["SPOILED"] = "SPOILED";
  StockTransactionType2["RETURNED"] = "RETURNED";
  StockTransactionType2["CONVERTED"] = "CONVERTED";
  StockTransactionType2["CORRECTION"] = "CORRECTION";
  return StockTransactionType2;
})(StockTransactionType || {});
function AddEntryModal({ isOpen, onClose, itemId, onAddEntry }) {
  const [formData, setFormData] = reactExports.useState({
    stockTransactionType: StockTransactionType.CORRECTION,
    // or another valid StockTransactionType value
    narration: "",
    quantity: 0,
    deliveryDate: (/* @__PURE__ */ new Date()).toISOString().split("T")[0]
  });
  const [error, setError] = reactExports.useState(null);
  const [loading, setLoading] = reactExports.useState(false);
  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!formData.stockTransactionType || !formData.narration || formData.quantity <= 0) {
      setError("All fields are required and must be valid");
      return;
    }
    setLoading(true);
    try {
      await onAddEntry(formData);
      setFormData({
        stockTransactionType: StockTransactionType.CORRECTION,
        narration: "",
        quantity: 0,
        deliveryDate: (/* @__PURE__ */ new Date()).toISOString().split("T")[0]
      });
      setError(null);
      onClose();
    } catch (err) {
      setError(err.message || "Failed to create transaction");
    } finally {
      setLoading(false);
    }
  };
  return /* @__PURE__ */ jsxRuntimeExports.jsx(Dialog, { open: isOpen, onOpenChange: onClose, children: /* @__PURE__ */ jsxRuntimeExports.jsxs(DialogContent, { className: "sm:max-w-[425px]", children: [
    /* @__PURE__ */ jsxRuntimeExports.jsx(DialogHeader, { children: /* @__PURE__ */ jsxRuntimeExports.jsx(DialogTitle, { children: "Add New Transaction" }) }),
    /* @__PURE__ */ jsxRuntimeExports.jsxs("form", { onSubmit: handleSubmit, children: [
      error && /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "mb-4 text-red-600", children: error }),
      /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "grid gap-4 py-4", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "grid grid-cols-4 items-center gap-4", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx("label", { htmlFor: "transactionType", className: "text-right text-sm font-medium", children: "Type:" }),
          /* @__PURE__ */ jsxRuntimeExports.jsxs(
            "select",
            {
              id: "transactionType",
              value: formData.stockTransactionType,
              onChange: (e) => setFormData({ ...formData, stockTransactionType: e.target.value }),
              className: "col-span-3 border rounded-md p-2",
              required: true,
              children: [
                /* @__PURE__ */ jsxRuntimeExports.jsx("option", { value: "", children: "Select Type" }),
                /* @__PURE__ */ jsxRuntimeExports.jsx("option", { value: StockTransactionType.RECEIVED, children: "Received" }),
                /* @__PURE__ */ jsxRuntimeExports.jsx("option", { value: StockTransactionType.DELIVERED, children: "Delivered" }),
                /* @__PURE__ */ jsxRuntimeExports.jsx("option", { value: StockTransactionType.SPOILED, children: "Spoiled" }),
                /* @__PURE__ */ jsxRuntimeExports.jsx("option", { value: StockTransactionType.RETURNED, children: "Returned" }),
                /* @__PURE__ */ jsxRuntimeExports.jsx("option", { value: StockTransactionType.CORRECTION, children: "Correction" }),
                /* @__PURE__ */ jsxRuntimeExports.jsx("option", { value: StockTransactionType.CONVERTED, children: "Converted" })
              ]
            }
          )
        ] }),
        /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "grid grid-cols-4 items-center gap-4", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx("label", { htmlFor: "narration", className: "text-right text-sm font-medium", children: "Narration:" }),
          /* @__PURE__ */ jsxRuntimeExports.jsx(
            "input",
            {
              id: "narration",
              value: formData.narration,
              onChange: (e) => setFormData({ ...formData, narration: e.target.value }),
              className: "col-span-3 border rounded-md p-2",
              required: true
            }
          )
        ] }),
        /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "grid grid-cols-4 items-center gap-4", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx("label", { htmlFor: "quantity", className: "text-right text-sm font-medium", children: "Quantity:" }),
          /* @__PURE__ */ jsxRuntimeExports.jsx(
            "input",
            {
              id: "quantity",
              type: "number",
              value: formData.quantity,
              onChange: (e) => setFormData({ ...formData, quantity: Number(e.target.value) }),
              min: "1",
              className: "col-span-3 border rounded-md p-2",
              required: true
            }
          )
        ] }),
        /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "grid grid-cols-4 items-center gap-4", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx("label", { htmlFor: "deliveryDate", className: "text-right text-sm font-medium", children: "Date:" }),
          /* @__PURE__ */ jsxRuntimeExports.jsx(
            "input",
            {
              id: "deliveryDate",
              type: "date",
              value: formData.deliveryDate,
              onChange: (e) => setFormData({ ...formData, deliveryDate: e.target.value }),
              className: "col-span-3 border rounded-md p-2",
              required: true
            }
          )
        ] })
      ] }),
      /* @__PURE__ */ jsxRuntimeExports.jsxs(DialogFooter, { children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx(Button, { type: "button", variant: "outline", onClick: onClose, children: "Cancel" }),
        /* @__PURE__ */ jsxRuntimeExports.jsx(
          Button,
          {
            type: "submit",
            loading,
            className: "ml-2",
            disabled: loading,
            children: loading ? "Creating..." : "Add Transaction"
          }
        )
      ] })
    ] })
  ] }) });
}
export {
  AddEntryModal as A,
  Printer as P,
  StockTransactionType as S
};
//# sourceMappingURL=AddEntryModal-BDnVYESI.js.map
