{"version": 3, "file": "index-D7VH9Fc8.js", "sources": ["../../../node_modules/@radix-ui/primitive/dist/index.mjs", "../../../node_modules/@radix-ui/react-context/dist/index.mjs", "../../../node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs", "../../../node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs"], "sourcesContent": ["// packages/core/primitive/src/primitive.tsx\nfunction composeEventHandlers(originalEventHandler, ourEventHandler, { checkForDefaultPrevented = true } = {}) {\n  return function handleEvent(event) {\n    originalEventHandler?.(event);\n    if (checkForDefaultPrevented === false || !event.defaultPrevented) {\n      return ourEventHandler?.(event);\n    }\n  };\n}\nexport {\n  composeEventHandlers\n};\n//# sourceMappingURL=index.mjs.map\n", "// packages/react/context/src/createContext.tsx\nimport * as React from \"react\";\nimport { jsx } from \"react/jsx-runtime\";\nfunction createContext2(rootComponentName, defaultContext) {\n  const Context = React.createContext(defaultContext);\n  const Provider = (props) => {\n    const { children, ...context } = props;\n    const value = React.useMemo(() => context, Object.values(context));\n    return /* @__PURE__ */ jsx(Context.Provider, { value, children });\n  };\n  Provider.displayName = rootComponentName + \"Provider\";\n  function useContext2(consumerName) {\n    const context = React.useContext(Context);\n    if (context) return context;\n    if (defaultContext !== void 0) return defaultContext;\n    throw new Error(`\\`${consumerName}\\` must be used within \\`${rootComponentName}\\``);\n  }\n  return [Provider, useContext2];\n}\nfunction createContextScope(scopeName, createContextScopeDeps = []) {\n  let defaultContexts = [];\n  function createContext3(rootComponentName, defaultContext) {\n    const BaseContext = React.createContext(defaultContext);\n    const index = defaultContexts.length;\n    defaultContexts = [...defaultContexts, defaultContext];\n    const Provider = (props) => {\n      const { scope, children, ...context } = props;\n      const Context = scope?.[scopeName]?.[index] || BaseContext;\n      const value = React.useMemo(() => context, Object.values(context));\n      return /* @__PURE__ */ jsx(Context.Provider, { value, children });\n    };\n    Provider.displayName = rootComponentName + \"Provider\";\n    function useContext2(consumerName, scope) {\n      const Context = scope?.[scopeName]?.[index] || BaseContext;\n      const context = React.useContext(Context);\n      if (context) return context;\n      if (defaultContext !== void 0) return defaultContext;\n      throw new Error(`\\`${consumerName}\\` must be used within \\`${rootComponentName}\\``);\n    }\n    return [Provider, useContext2];\n  }\n  const createScope = () => {\n    const scopeContexts = defaultContexts.map((defaultContext) => {\n      return React.createContext(defaultContext);\n    });\n    return function useScope(scope) {\n      const contexts = scope?.[scopeName] || scopeContexts;\n      return React.useMemo(\n        () => ({ [`__scope${scopeName}`]: { ...scope, [scopeName]: contexts } }),\n        [scope, contexts]\n      );\n    };\n  };\n  createScope.scopeName = scopeName;\n  return [createContext3, composeContextScopes(createScope, ...createContextScopeDeps)];\n}\nfunction composeContextScopes(...scopes) {\n  const baseScope = scopes[0];\n  if (scopes.length === 1) return baseScope;\n  const createScope = () => {\n    const scopeHooks = scopes.map((createScope2) => ({\n      useScope: createScope2(),\n      scopeName: createScope2.scopeName\n    }));\n    return function useComposedScopes(overrideScopes) {\n      const nextScopes = scopeHooks.reduce((nextScopes2, { useScope, scopeName }) => {\n        const scopeProps = useScope(overrideScopes);\n        const currentScope = scopeProps[`__scope${scopeName}`];\n        return { ...nextScopes2, ...currentScope };\n      }, {});\n      return React.useMemo(() => ({ [`__scope${baseScope.scopeName}`]: nextScopes }), [nextScopes]);\n    };\n  };\n  createScope.scopeName = baseScope.scopeName;\n  return createScope;\n}\nexport {\n  createContext2 as createContext,\n  createContextScope\n};\n//# sourceMappingURL=index.mjs.map\n", "// packages/react/use-callback-ref/src/useCallbackRef.tsx\nimport * as React from \"react\";\nfunction useCallbackRef(callback) {\n  const callbackRef = React.useRef(callback);\n  React.useEffect(() => {\n    callbackRef.current = callback;\n  });\n  return React.useMemo(() => (...args) => callbackRef.current?.(...args), []);\n}\nexport {\n  useCallbackRef\n};\n//# sourceMappingURL=index.mjs.map\n", "// packages/react/use-layout-effect/src/useLayoutEffect.tsx\nimport * as React from \"react\";\nvar useLayoutEffect2 = Boolean(globalThis?.document) ? React.useLayoutEffect : () => {\n};\nexport {\n  useLayoutEffect2 as useLayoutEffect\n};\n//# sourceMappingURL=index.mjs.map\n"], "names": ["React.createContext", "React.useMemo", "jsx", "React.useContext", "React.useRef", "React.useEffect", "React.useLayoutEffect"], "mappings": ";AACA,SAAS,qBAAqB,sBAAsB,iBAAiB,EAAE,2BAA2B,KAAM,IAAG,IAAI;AAC7G,SAAO,SAAS,YAAY,OAAO;AACjC,iEAAuB;AACvB,QAAI,6BAA6B,SAAS,CAAC,MAAM,kBAAkB;AACjE,aAAO,mDAAkB;AAAA,IAC/B;AAAA,EACG;AACH;ACLA,SAAS,eAAe,mBAAmB,gBAAgB;AACzD,QAAM,UAAUA,aAAmB,cAAC,cAAc;AAClD,QAAM,WAAW,CAAC,UAAU;AAC1B,UAAM,EAAE,UAAU,GAAG,QAAO,IAAK;AACjC,UAAM,QAAQC,aAAAA,QAAc,MAAM,SAAS,OAAO,OAAO,OAAO,CAAC;AACjE,WAAuBC,kCAAAA,IAAI,QAAQ,UAAU,EAAE,OAAO,SAAQ,CAAE;AAAA,EACjE;AACD,WAAS,cAAc,oBAAoB;AAC3C,WAAS,YAAY,cAAc;AACjC,UAAM,UAAUC,aAAgB,WAAC,OAAO;AACxC,QAAI,QAAS,QAAO;AACpB,QAAI,mBAAmB,OAAQ,QAAO;AACtC,UAAM,IAAI,MAAM,KAAK,YAAY,4BAA4B,iBAAiB,IAAI;AAAA,EACtF;AACE,SAAO,CAAC,UAAU,WAAW;AAC/B;AACA,SAAS,mBAAmB,WAAW,yBAAyB,IAAI;AAClE,MAAI,kBAAkB,CAAE;AACxB,WAAS,eAAe,mBAAmB,gBAAgB;AACzD,UAAM,cAAcH,aAAmB,cAAC,cAAc;AACtD,UAAM,QAAQ,gBAAgB;AAC9B,sBAAkB,CAAC,GAAG,iBAAiB,cAAc;AACrD,UAAM,WAAW,CAAC,UAAU;;AAC1B,YAAM,EAAE,OAAO,UAAU,GAAG,QAAS,IAAG;AACxC,YAAM,YAAU,oCAAQ,eAAR,mBAAqB,WAAU;AAC/C,YAAM,QAAQC,aAAAA,QAAc,MAAM,SAAS,OAAO,OAAO,OAAO,CAAC;AACjE,aAAuBC,kCAAAA,IAAI,QAAQ,UAAU,EAAE,OAAO,SAAQ,CAAE;AAAA,IACjE;AACD,aAAS,cAAc,oBAAoB;AAC3C,aAAS,YAAY,cAAc,OAAO;;AACxC,YAAM,YAAU,oCAAQ,eAAR,mBAAqB,WAAU;AAC/C,YAAM,UAAUC,aAAgB,WAAC,OAAO;AACxC,UAAI,QAAS,QAAO;AACpB,UAAI,mBAAmB,OAAQ,QAAO;AACtC,YAAM,IAAI,MAAM,KAAK,YAAY,4BAA4B,iBAAiB,IAAI;AAAA,IACxF;AACI,WAAO,CAAC,UAAU,WAAW;AAAA,EACjC;AACE,QAAM,cAAc,MAAM;AACxB,UAAM,gBAAgB,gBAAgB,IAAI,CAAC,mBAAmB;AAC5D,aAAOH,aAAAA,cAAoB,cAAc;AAAA,IAC/C,CAAK;AACD,WAAO,SAAS,SAAS,OAAO;AAC9B,YAAM,YAAW,+BAAQ,eAAc;AACvC,aAAOC,aAAa;AAAA,QAClB,OAAO,EAAE,CAAC,UAAU,SAAS,EAAE,GAAG,EAAE,GAAG,OAAO,CAAC,SAAS,GAAG,SAAQ;QACnE,CAAC,OAAO,QAAQ;AAAA,MACjB;AAAA,IACF;AAAA,EACF;AACD,cAAY,YAAY;AACxB,SAAO,CAAC,gBAAgB,qBAAqB,aAAa,GAAG,sBAAsB,CAAC;AACtF;AACA,SAAS,wBAAwB,QAAQ;AACvC,QAAM,YAAY,OAAO,CAAC;AAC1B,MAAI,OAAO,WAAW,EAAG,QAAO;AAChC,QAAM,cAAc,MAAM;AACxB,UAAM,aAAa,OAAO,IAAI,CAAC,kBAAkB;AAAA,MAC/C,UAAU,aAAc;AAAA,MACxB,WAAW,aAAa;AAAA,IAC9B,EAAM;AACF,WAAO,SAAS,kBAAkB,gBAAgB;AAChD,YAAM,aAAa,WAAW,OAAO,CAAC,aAAa,EAAE,UAAU,gBAAgB;AAC7E,cAAM,aAAa,SAAS,cAAc;AAC1C,cAAM,eAAe,WAAW,UAAU,SAAS,EAAE;AACrD,eAAO,EAAE,GAAG,aAAa,GAAG,aAAc;AAAA,MAC3C,GAAE,EAAE;AACL,aAAOA,qBAAc,OAAO,EAAE,CAAC,UAAU,UAAU,SAAS,EAAE,GAAG,WAAU,IAAK,CAAC,UAAU,CAAC;AAAA,IAC7F;AAAA,EACF;AACD,cAAY,YAAY,UAAU;AAClC,SAAO;AACT;ACzEA,SAAS,eAAe,UAAU;AAChC,QAAM,cAAcG,aAAY,OAAC,QAAQ;AACzCC,eAAAA,UAAgB,MAAM;AACpB,gBAAY,UAAU;AAAA,EAC1B,CAAG;AACD,SAAOJ,aAAa,QAAC,MAAM,IAAI,SAAI;;AAAK,6BAAY,YAAZ,qCAAsB,GAAG;AAAA,KAAO,EAAE;AAC5E;ACNG,IAAC,mBAAmB,QAAQ,yCAAY,QAAQ,IAAIK,aAAqB,kBAAG,MAAM;AACrF;", "x_google_ignoreList": [0, 1, 2, 3]}