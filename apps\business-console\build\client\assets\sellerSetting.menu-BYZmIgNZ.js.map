{"version": 3, "file": "sellerSetting.menu-BYZmIgNZ.js", "sources": ["../../../app/routes/sellerSetting.menu.tsx"], "sourcesContent": ["import { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"~/components/ui/card\";\r\n\r\nexport default function Menu() {\r\n  return (\r\n    <div className=\"p-6\">\r\n      <div className=\"mb-6\">\r\n        <h1 className=\"text-3xl font-bold text-gray-900\">Menu Management</h1>\r\n        <p className=\"text-gray-600 mt-2\">Manage your restaurant menu items</p>\r\n      </div>\r\n      \r\n      <div className=\"flex items-center justify-center min-h-[400px]\">\r\n        <Card className=\"w-full max-w-md\">\r\n          <CardHeader className=\"text-center\">\r\n            <div className=\"mx-auto mb-4 w-16 h-16 bg-gradient-to-r from-orange-500 to-red-600 rounded-full flex items-center justify-center\">\r\n              <svg className=\"w-8 h-8 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6h16M4 10h16M4 14h16M4 18h16\" />\r\n              </svg>\r\n            </div>\r\n            <CardTitle className=\"text-2xl\">Menu Management Coming Soon</CardTitle>\r\n            <CardDescription className=\"text-lg\">\r\n              We&apos;re creating an intuitive menu management system for your restaurant.\r\n            </CardDescription>\r\n          </CardHeader>\r\n          <CardContent className=\"text-center\">\r\n            <p className=\"text-gray-500 mb-4\">\r\n              Add, edit, and organize your menu items with ease. Manage categories, pricing, and availability.\r\n            </p>\r\n            <div className=\"inline-flex items-center px-4 py-2 bg-gradient-to-r from-orange-500 to-red-600 text-black rounded-lg\">\r\n              <span className=\"mr-2\">🍽️</span>\r\n              Launching Soon\r\n            </div>\r\n          </CardContent>\r\n        </Card>\r\n      </div>\r\n    </div>\r\n  );\r\n} "], "names": ["<PERSON><PERSON>", "jsxs", "className", "children", "jsx", "Card", "<PERSON><PERSON><PERSON><PERSON>", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "CardTitle", "CardDescription", "<PERSON><PERSON><PERSON><PERSON>"], "mappings": ";;;AAEA,SAAwBA,OAAO;AAE3B,SAAAC,kCAAAA,KAAC,OAAI;AAAA,IAAAC,WAAU;AAAA,IACbC,UAAA,CAACF,kCAAA,KAAA,OAAA;AAAA,MAAIC,WAAU;AAAA,MACbC,UAAA,CAACC,kCAAA,IAAA,MAAA;AAAA,QAAGF,WAAU;AAAA,QAAmCC,UAAe;AAAA,MAAA,CAAA,GAC/DC,kCAAA,IAAA,KAAA;AAAA,QAAEF,WAAU;AAAA,QAAqBC,UAAiC;AAAA,MAAA,CAAA,CAAA;AAAA,IACrE,CAAA,yCAEC,OAAI;AAAA,MAAAD,WAAU;AAAA,MACbC,UAACF,kCAAA,KAAAI,MAAA;AAAA,QAAKH,WAAU;AAAA,QACdC,UAAA,CAACF,kCAAA,KAAAK,YAAA;AAAA,UAAWJ,WAAU;AAAA,UACpBC,UAAA,CAACC,kCAAA,IAAA,OAAA;AAAA,YAAIF,WAAU;AAAA,YACbC,UAACC,kCAAA,IAAA,OAAA;AAAA,cAAIF,WAAU;AAAA,cAAqBK,MAAK;AAAA,cAAOC,QAAO;AAAA,cAAeC,SAAQ;AAAA,cAC5EN,UAACC,kCAAA,IAAA,QAAA;AAAA,gBAAKM,eAAc;AAAA,gBAAQC,gBAAe;AAAA,gBAAQC,aAAa;AAAA,gBAAGC,GAAE;AAAA,cAAkC,CAAA;AAAA,YACzG,CAAA;AAAA,UACF,CAAA,GACCT,kCAAA,IAAAU,WAAA;AAAA,YAAUZ,WAAU;AAAA,YAAWC,UAA2B;AAAA,UAAA,CAAA,GAC1DC,kCAAA,IAAAW,iBAAA;AAAA,YAAgBb,WAAU;AAAA,YAAUC,UAErC;AAAA,UAAA,CAAA,CAAA;AAAA,QACF,CAAA,GACAF,kCAAA,KAACe,aAAY;AAAA,UAAAd,WAAU;AAAA,UACrBC,UAAA,CAACC,kCAAA,IAAA,KAAA;AAAA,YAAEF,WAAU;AAAA,YAAqBC,UAElC;AAAA,UAAA,CAAA,GACAF,kCAAA,KAAC,OAAI;AAAA,YAAAC,WAAU;AAAA,YACbC,UAAA,CAACC,kCAAA,IAAA,QAAA;AAAA,cAAKF,WAAU;AAAA,cAAOC,UAAG;AAAA,YAAA,CAAA,GAAO,gBAAA;AAAA,UAEnC,CAAA,CAAA;AAAA,QACF,CAAA,CAAA;AAAA,MACF,CAAA;AAAA,IACF,CAAA,CAAA;AAAA,EACF,CAAA;AAEJ;"}