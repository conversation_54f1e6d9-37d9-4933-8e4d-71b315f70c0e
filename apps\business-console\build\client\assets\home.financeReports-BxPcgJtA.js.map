{"version": 3, "file": "home.financeReports-BxPcgJtA.js", "sources": ["../../../app/routes/home.financeReports.tsx"], "sourcesContent": ["import { useState } from \"react\"\r\nimport { Input } from \"~/components/ui/input\"\r\n\r\n\r\nexport default function FinanceConfig() {\r\n      const [searchTerm, setSearchTerm] = useState('')\r\n\r\n      return (\r\n            <div className=\"container mx-auto p-6\">\r\n                  <div className=\"flex justify-between items-center mb-6\">\r\n                        <h1 className=\"text-2xl font-bold\">mNet Users</h1>\r\n                  </div>\r\n\r\n                  <div className=\"flex justify-between mb-4\">\r\n                        <Input\r\n                              placeholder=\"Search by name or owner\"\r\n                              value={searchTerm}\r\n                              onChange={(e) => setSearchTerm(e.target.value)}\r\n                              className=\"max-w-sm\"\r\n                        />\r\n                  </div>\r\n            </div>\r\n      )\r\n}"], "names": ["FinanceConfig", "searchTerm", "setSearchTerm", "useState", "jsxs", "className", "children", "jsx", "Input", "placeholder", "value", "onChange", "e", "target"], "mappings": ";;;AAIA,SAAwBA,gBAAgB;AAClC,QAAM,CAACC,YAAYC,aAAa,IAAIC,aAAAA,SAAS,EAAE;AAGzC,SAAAC,kCAAAA,KAAC,OAAI;AAAA,IAAAC,WAAU;AAAA,IACTC,UAAA,CAACC,kCAAA,IAAA,OAAA;AAAA,MAAIF,WAAU;AAAA,MACTC,UAAAC,kCAAA,IAAC;QAAGF,WAAU;AAAA,QAAqBC;MAAU,CAAA;AAAA,IACnD,CAAA,GAEAC,kCAAA,IAAC,OAAI;AAAA,MAAAF,WAAU;AAAA,MACTC,UAAAC,kCAAA,IAACC,OAAA;AAAA,QACKC,aAAY;AAAA,QACZC,OAAOT;AAAAA,QACPU,UAAWC,OAAMV,cAAcU,EAAEC,OAAOH,KAAK;AAAA,QAC7CL,WAAU;AAAA,MAChB,CAAA;AAAA,IACN,CAAA,CAAA;AAAA,EACN,CAAA;AAEZ;"}