import { j as jsxRuntimeExports } from "./jsx-runtime-bB2y7OuD.js";
import { T as Tabs, a as <PERSON><PERSON>List, b as <PERSON><PERSON><PERSON>rigger, c as TabsContent } from "./tabs-CfSdyzWr.js";
import { u as useLoaderData } from "./components-D7UvGag_.js";
import "./index-D7VH9Fc8.js";
import "./index-CG37gmC0.js";
import "./index-qCtcpOcW.js";
import "./index-z_byfFrQ.js";
import "./index-DVTNuYOr.js";
import "./index-CzZ6EYkN.js";
import "./index-Vp2vNLNM.js";
import "./index-dIGjFc0I.js";
import "./index-BTdCMChR.js";
import "./index-QLGF6kQx.js";
import "./utils-GkgzjW3c.js";
import "./index-DhHTcibu.js";
function Analytics() {
  const {
    overviewUrl,
    itemsAnalyticsUrl
  } = useLoaderData();
  return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
    className: "p-6",
    children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
      className: "mb-6",
      children: [/* @__PURE__ */ jsxRuntimeExports.jsx("h1", {
        className: "text-3xl font-bold text-gray-900",
        children: "Analytics"
      }), /* @__PURE__ */ jsxRuntimeExports.jsx("p", {
        className: "text-gray-600 mt-2",
        children: "Detailed performance insights"
      })]
    }), /* @__PURE__ */ jsxRuntimeExports.jsxs(Tabs, {
      defaultValue: "overview",
      className: "w-full",
      children: [/* @__PURE__ */ jsxRuntimeExports.jsxs(TabsList, {
        className: "grid w-full grid-cols-2",
        children: [/* @__PURE__ */ jsxRuntimeExports.jsx(TabsTrigger, {
          value: "overview",
          children: "Overview"
        }), /* @__PURE__ */ jsxRuntimeExports.jsx(TabsTrigger, {
          value: "items-analytics",
          children: "Items Analytics"
        })]
      }), /* @__PURE__ */ jsxRuntimeExports.jsx(TabsContent, {
        value: "overview",
        className: "mt-6",
        children: /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
          className: "w-full h-screen",
          children: overviewUrl ? /* @__PURE__ */ jsxRuntimeExports.jsx("iframe", {
            id: "metabase-overview-iframe",
            src: overviewUrl,
            title: "Overview Dashboard",
            className: "w-full h-full border-0"
          }) : /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
            className: "flex items-center justify-center min-h-[400px]",
            children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
              className: "text-center",
              children: [/* @__PURE__ */ jsxRuntimeExports.jsx("div", {
                className: "mx-auto mb-4 w-16 h-16 bg-gradient-to-r from-teal-500 to-green-600 rounded-full flex items-center justify-center",
                children: /* @__PURE__ */ jsxRuntimeExports.jsx("svg", {
                  className: "w-8 h-8 text-white",
                  fill: "none",
                  stroke: "currentColor",
                  viewBox: "0 0 24 24",
                  children: /* @__PURE__ */ jsxRuntimeExports.jsx("path", {
                    strokeLinecap: "round",
                    strokeLinejoin: "round",
                    strokeWidth: 2,
                    d: "M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                  })
                })
              }), /* @__PURE__ */ jsxRuntimeExports.jsx("h2", {
                className: "text-2xl font-bold mb-2",
                children: "Overview Dashboard Loading"
              }), /* @__PURE__ */ jsxRuntimeExports.jsx("p", {
                className: "text-gray-500",
                children: "Loading your overview analytics..."
              })]
            })
          })
        })
      }), /* @__PURE__ */ jsxRuntimeExports.jsx(TabsContent, {
        value: "items-analytics",
        className: "mt-6",
        children: /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
          className: "w-full h-screen",
          children: itemsAnalyticsUrl ? /* @__PURE__ */ jsxRuntimeExports.jsx("iframe", {
            id: "metabase-items-iframe",
            src: itemsAnalyticsUrl,
            title: "Items Analytics Dashboard",
            className: "w-full h-full border-0"
          }) : /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
            className: "flex items-center justify-center min-h-[400px]",
            children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
              className: "text-center",
              children: [/* @__PURE__ */ jsxRuntimeExports.jsx("div", {
                className: "mx-auto mb-4 w-16 h-16 bg-gradient-to-r from-teal-500 to-green-600 rounded-full flex items-center justify-center",
                children: /* @__PURE__ */ jsxRuntimeExports.jsx("svg", {
                  className: "w-8 h-8 text-white",
                  fill: "none",
                  stroke: "currentColor",
                  viewBox: "0 0 24 24",
                  children: /* @__PURE__ */ jsxRuntimeExports.jsx("path", {
                    strokeLinecap: "round",
                    strokeLinejoin: "round",
                    strokeWidth: 2,
                    d: "M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                  })
                })
              }), /* @__PURE__ */ jsxRuntimeExports.jsx("h2", {
                className: "text-2xl font-bold mb-2",
                children: "Items Analytics Loading"
              }), /* @__PURE__ */ jsxRuntimeExports.jsx("p", {
                className: "text-gray-500",
                children: "Loading your items analytics..."
              })]
            })
          })
        })
      })]
    })]
  });
}
export {
  Analytics as default
};
//# sourceMappingURL=sellerSetting.analytics-vkhoVEkz.js.map
