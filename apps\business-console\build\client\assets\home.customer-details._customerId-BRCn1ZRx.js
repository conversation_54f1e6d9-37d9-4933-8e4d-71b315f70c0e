import { r as reactExports, j as jsxRuntimeExports } from "./jsx-runtime-bB2y7OuD.js";
import { B as Button } from "./button-ByAXMyvk.js";
import { I as Input } from "./input-3v87qohQ.js";
import { T as Table, a as TableHeader, b as TableRow, c as TableHead, d as TableBody, e as TableCell } from "./table-CAxdJZsY.js";
import { T as Tabs, a as TabsList, b as TabsTrigger, c as TabsContent } from "./tabs-CfSdyzWr.js";
import { C as Card, b as CardHeader, c as CardTitle, a as CardContent } from "./card-BJQMSLe_.js";
import { S as Select, a as SelectTrigger, b as SelectValue, c as SelectContent, d as SelectItem } from "./select-BFGSXKcr.js";
import { B as Badge } from "./badge-BsHDHlRV.js";
import { D as Dialog, a as DialogContent, c as <PERSON><PERSON><PERSON>eader, b as DialogTitle, f as DialogDescription } from "./dialog-BqKosxNq.js";
import { c as cva } from "./index-ImHKLo0a.js";
import { c as cn } from "./utils-GkgzjW3c.js";
import { S as Switch } from "./switch-DQ_qW6ch.js";
import { d as useParams, a as useNavigate } from "./index-DhHTcibu.js";
import { u as useLoaderData, d as useActionData, a as useFetcher, F as Form } from "./components-D7UvGag_.js";
import { A as ArrowLeft } from "./arrow-left-D_Ztdrp5.js";
import { P as Phone } from "./phone-HnpbSr97.js";
import { M as MapPin } from "./map-pin-BWBTUiG2.js";
import { c as createLucideIcon } from "./createLucideIcon-uwkRm45G.js";
import { S as Save } from "./save-xzNIILKr.js";
import { S as SquarePen } from "./square-pen-BXxSi9JH.js";
import "./index-z_byfFrQ.js";
import "./index-D7VH9Fc8.js";
import "./index-CG37gmC0.js";
import "./index-qCtcpOcW.js";
import "./index-DVTNuYOr.js";
import "./index-CzZ6EYkN.js";
import "./index-Vp2vNLNM.js";
import "./index-dIGjFc0I.js";
import "./index-BTdCMChR.js";
import "./index-QLGF6kQx.js";
import "./index-IXOTxK3N.js";
import "./index-BXzPK7u0.js";
import "./index-CEHS9zzk.js";
import "./index-DscYByPT.js";
import "./index-CkL5tk39.js";
import "./index-CpKiYcZd.js";
import "./index-C88PRvfd.js";
import "./chevron-down-pCP5jmjX.js";
import "./check-_dbWxIzT.js";
import "./index-DdafHWkt.js";
import "./x-CCG_WJDF.js";
/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */
const Bell = createLucideIcon("Bell", [
  ["path", { d: "M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9", key: "1qo2s2" }],
  ["path", { d: "M10.3 21a1.94 1.94 0 0 0 3.4 0", key: "qgo35s" }]
]);
const alertVariants = cva(
  "relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",
  {
    variants: {
      variant: {
        default: "bg-background text-foreground",
        destructive: "border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"
      }
    },
    defaultVariants: {
      variant: "default"
    }
  }
);
const Alert = reactExports.forwardRef(({ className, variant, ...props }, ref) => /* @__PURE__ */ jsxRuntimeExports.jsx(
  "div",
  {
    ref,
    role: "alert",
    className: cn(alertVariants({ variant }), className),
    ...props
  }
));
Alert.displayName = "Alert";
const AlertTitle = reactExports.forwardRef(({ className, ...props }, ref) => /* @__PURE__ */ jsxRuntimeExports.jsx(
  "h5",
  {
    ref,
    className: cn("mb-1 font-medium leading-none tracking-tight", className),
    ...props
  }
));
AlertTitle.displayName = "AlertTitle";
const AlertDescription = reactExports.forwardRef(({ className, ...props }, ref) => /* @__PURE__ */ jsxRuntimeExports.jsx(
  "div",
  {
    ref,
    className: cn("text-sm [&_p]:leading-relaxed", className),
    ...props
  }
));
AlertDescription.displayName = "AlertDescription";
function CustomerDetailsPage() {
  var _a;
  const params = useParams();
  const buyerId = parseInt(params.customerId);
  const {
    user,
    customerDetails,
    orders,
    contractPrices,
    currentPage,
    userPerMissions,
    customerId,
    customerDashBoardData,
    dashBoardGroupBy,
    totalPages
  } = useLoaderData();
  const actionData = useActionData();
  const [searchTerm, setSearchTerm] = reactExports.useState("");
  const [sortBy, setSortBy] = reactExports.useState("id");
  const [sortOrder, setSortOrder] = reactExports.useState("asc");
  const [activeTab, setActiveTab] = reactExports.useState("dashboard");
  const [isDialogOpen, setIsDialogOpen] = reactExports.useState(false);
  const [isSubmitting, setIsSubmitting] = reactExports.useState(false);
  const [uniqueId, setUnqueId] = reactExports.useState(0);
  const [isEdit, setIsEdit] = reactExports.useState(false);
  const [cPrice, setCPrice] = reactExports.useState(0);
  const fetcher = useFetcher();
  const navigate = useNavigate();
  const [cPriceEnable, setCPriceEnable] = reactExports.useState(false);
  const [cPriceEditable, setCPriceEditable] = reactExports.useState(false);
  const previewMessage = `Hi ${customerDetails.ownerName},
        You have pending dues with us. Please open the app to review and clear them as soon as possible. Ignore if already paid.
        Thank you !`;
  const [customergraphData, setCustomerGraphData] = reactExports.useState(customerDashBoardData);
  const [dashboardGroup, setIsDashBoardGroup] = reactExports.useState(dashBoardGroupBy);
  const [pageSize, setPageSize] = reactExports.useState("10");
  const [selectedCDate, setSelectedCDate] = reactExports.useState(/* @__PURE__ */ new Date());
  reactExports.useEffect(() => {
    var _a2;
    if (((_a2 = fetcher.data) == null ? void 0 : _a2.data) && activeTab === "dashboard") {
      setCustomerGraphData(fetcher.data.data);
      setIsDashBoardGroup(fetcher.data.dashboardGroupBy);
    }
  }, [fetcher.state, activeTab === "dashboard"]);
  reactExports.useEffect(() => {
    if (userPerMissions) {
      const isContractPrice = userPerMissions.includes("seller_app.contracPriceEnabled");
      const isContractPriceEditable = userPerMissions.includes("seller_app.contracPriceEditable");
      setCPriceEditable(isContractPriceEditable);
      setCPriceEnable(isContractPrice);
    }
  }, [userPerMissions]);
  const handlefilter = (contract) => {
    return contract.itemName.toLowerCase().includes(searchTerm.toLowerCase());
  };
  const handlePageSizeChange = (newPageSize) => {
    navigate(`/home/<USER>/${customerId}?page=${currentPage}&pageSize=${newPageSize}&activeTab=${activeTab}`);
    setPageSize(newPageSize);
  };
  orders.filter((order) => order.orderGroupId.toString().includes(searchTerm) || order.orderStatus.toLowerCase().includes(searchTerm.toLowerCase())).sort((a, b) => {
    const getValue = (order) => {
      switch (sortBy) {
        case "id":
          return order.orderGroupId;
        case "deliveryDate":
          return order.deliveryDate;
        case "status":
          return order.orderStatus;
        case "totalAmount":
          return order.orderAmount;
        default:
          return order.orderGroupId;
      }
    };
    const aValue = getValue(a);
    const bValue = getValue(b);
    if (aValue < bValue) return sortOrder === "asc" ? -1 : 1;
    if (aValue > bValue) return sortOrder === "asc" ? 1 : -1;
    return 0;
  });
  const handleAddContractPrice = (siItemId, buyerId2) => {
    setUnqueId(siItemId);
    const formData = new FormData();
    formData.append("intent", "ContractPrice");
    formData.append("siItemId", siItemId.toString());
    formData.append("buyerId", buyerId2.toString());
    fetcher.submit(formData, {
      method: "POST"
    });
  };
  const handleUpdatePrice = (siItemId, cPriceId, cPrice2) => {
    setUnqueId(siItemId);
    const formData = new FormData();
    formData.append("intent", "ContractUpdatePrice");
    formData.append("cPriceId", cPriceId.toString());
    formData.append("cPrice", cPrice2.toString());
    fetcher.submit(formData, {
      method: "POST"
    });
    setIsEdit(false);
    setCPrice(0);
  };
  const handleUpdateStatus = (siItemId, contractPriceId, contractPriceEnabled) => {
    setUnqueId(siItemId);
    const formData = new FormData();
    formData.append("intent", "ContractUpdateStatus");
    formData.append("cPriceId", contractPriceId.toString());
    formData.append("cPriceEnable", contractPriceEnabled.toString());
    fetcher.submit(formData, {
      method: "POST"
    });
    setIsEdit(false);
  };
  const handleEdit = (itemId) => {
    setUnqueId(itemId);
    setIsEdit(true);
  };
  const handleTabChange = (value) => {
    setActiveTab(value);
    navigate(`/home/<USER>/${customerId}?page=0&pageSize=${pageSize}&activeTab=${value}`);
  };
  const renderPagination = (currentPage2, totalPages2) => {
    return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
      className: "flex items-center justify-between space-x-2 py-4",
      children: [/* @__PURE__ */ jsxRuntimeExports.jsx("div", {
        className: "flex-1 text-sm text-muted-foreground"
      }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
        className: "flex items-center space-x-2",
        children: [/* @__PURE__ */ jsxRuntimeExports.jsxs(Form, {
          method: "get",
          children: [/* @__PURE__ */ jsxRuntimeExports.jsx("input", {
            type: "hidden",
            name: "page",
            value: currentPage2 - 1
          }), /* @__PURE__ */ jsxRuntimeExports.jsx("input", {
            type: "hidden",
            name: "pageSize",
            value: pageSize
          }), /* @__PURE__ */ jsxRuntimeExports.jsx("input", {
            type: "hidden",
            name: "activeTab",
            value: activeTab
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
            variant: "outline",
            size: "sm",
            type: "submit",
            disabled: currentPage2 <= 0,
            children: "Previous"
          })]
        }), /* @__PURE__ */ jsxRuntimeExports.jsxs(Form, {
          method: "get",
          children: [/* @__PURE__ */ jsxRuntimeExports.jsx("input", {
            type: "hidden",
            name: "page",
            value: currentPage2 + 1
          }), /* @__PURE__ */ jsxRuntimeExports.jsx("input", {
            type: "hidden",
            name: "pageSize",
            value: pageSize
          }), /* @__PURE__ */ jsxRuntimeExports.jsx("input", {
            type: "hidden",
            name: "activeTab",
            value: activeTab
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
            variant: "outline",
            size: "sm",
            type: "submit",
            children: "Next"
          })]
        })]
      })]
    });
  };
  return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
    className: "container mx-auto p-6",
    children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Dialog, {
      open: isDialogOpen,
      onOpenChange: setIsDialogOpen,
      children: /* @__PURE__ */ jsxRuntimeExports.jsx(DialogContent, {
        children: (actionData == null ? void 0 : actionData.success) ? /* @__PURE__ */ jsxRuntimeExports.jsxs(jsxRuntimeExports.Fragment, {
          children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Alert, {
            className: "mt-4",
            children: /* @__PURE__ */ jsxRuntimeExports.jsx(AlertDescription, {
              className: "text-green-600",
              children: "Payment reminder sent successfully!"
            })
          }), /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
            className: "flex justify-end mt-4",
            children: /* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
              onClick: () => setIsDialogOpen(false),
              children: "Dismiss"
            })
          })]
        }) : /* @__PURE__ */ jsxRuntimeExports.jsxs(jsxRuntimeExports.Fragment, {
          children: [/* @__PURE__ */ jsxRuntimeExports.jsxs(DialogHeader, {
            children: [/* @__PURE__ */ jsxRuntimeExports.jsx(DialogTitle, {
              children: "Send Payment Reminder"
            }), /* @__PURE__ */ jsxRuntimeExports.jsx(DialogDescription, {
              children: "Preview of the message that will be sent:"
            })]
          }), /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
            className: "bg-muted p-4 rounded-lg my-4",
            children: /* @__PURE__ */ jsxRuntimeExports.jsx("p", {
              className: "whitespace-pre-wrap",
              children: previewMessage
            })
          }), /* @__PURE__ */ jsxRuntimeExports.jsxs(Form, {
            method: "post",
            onSubmit: () => setIsSubmitting(true),
            onChange: () => {
              if (actionData) {
                setIsSubmitting(false);
              }
            },
            children: [/* @__PURE__ */ jsxRuntimeExports.jsx("input", {
              type: "hidden",
              name: "phoneNo",
              value: customerDetails.mobileNumber
            }), /* @__PURE__ */ jsxRuntimeExports.jsx("input", {
              type: "hidden",
              name: "amount",
              value: customerDetails.pendingAmount.toString()
            }), /* @__PURE__ */ jsxRuntimeExports.jsx("input", {
              type: "hidden",
              name: "customerName",
              value: customerDetails.ownerName
            }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
              className: "flex justify-end gap-2",
              children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
                variant: "outline",
                onClick: () => setIsDialogOpen(false),
                type: "button",
                children: "Cancel"
              }), /* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
                type: "submit",
                disabled: isSubmitting,
                children: isSubmitting ? "Sending..." : "Send Reminder"
              })]
            })]
          }), (actionData == null ? void 0 : actionData.error) && /* @__PURE__ */ jsxRuntimeExports.jsx(Alert, {
            className: "mt-4",
            children: /* @__PURE__ */ jsxRuntimeExports.jsx(AlertDescription, {
              className: "text-red-600",
              children: actionData.error
            })
          })]
        })
      })
    }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
      className: "flex items-center gap-2 mb-6",
      children: [/* @__PURE__ */ jsxRuntimeExports.jsxs(Button, {
        variant: "ghost",
        size: "sm",
        onClick: () => navigate(-1),
        children: [/* @__PURE__ */ jsxRuntimeExports.jsx(ArrowLeft, {
          className: "h-4 w-4 mr-2"
        }), "Back to Customers"]
      }), /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
        className: "text-muted-foreground",
        children: "/"
      }), /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
        className: "font-semibold",
        children: customerDetails.buyerName
      })]
    }), /* @__PURE__ */ jsxRuntimeExports.jsxs(Card, {
      className: "mb-6",
      children: [/* @__PURE__ */ jsxRuntimeExports.jsx(CardHeader, {
        children: /* @__PURE__ */ jsxRuntimeExports.jsx(CardTitle, {
          className: "flex items-center justify-between",
          children: /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
            children: customerDetails.buyerName
          })
        })
      }), /* @__PURE__ */ jsxRuntimeExports.jsx(CardContent, {
        children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
          className: "grid grid-cols-1 md:grid-cols-3 gap-4",
          children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
            children: [/* @__PURE__ */ jsxRuntimeExports.jsx("p", {
              className: "font-semibold",
              children: customerDetails.ownerName
            }), /* @__PURE__ */ jsxRuntimeExports.jsxs("p", {
              className: "flex items-center mt-1 text-sm",
              children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Phone, {
                className: "h-4 w-4 mr-2"
              }), customerDetails.mobileNumber]
            }), /* @__PURE__ */ jsxRuntimeExports.jsxs("p", {
              className: "flex items-start mt-1 text-sm",
              children: [/* @__PURE__ */ jsxRuntimeExports.jsx(MapPin, {
                className: "h-4 w-4 mr-2 mt-1"
              }), customerDetails.address]
            })]
          }), /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
            className: "flex flex-col gap-2 md:col-span-2 justify-end",
            children: /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
              className: "flex w-full flex-wrap gap-2 justify-end",
              children: ((_a = user == null ? void 0 : user.userDetails) == null ? void 0 : _a.mobileNumber) === "5555555550" ? /* @__PURE__ */ jsxRuntimeExports.jsxs(Button, {
                variant: "outline",
                size: "sm",
                onClick: () => setIsDialogOpen(true),
                children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Bell, {
                  className: "h-4 w-4 mr-2"
                }), "Send Payment reminder"]
              }) : null
            })
          })]
        })
      })]
    }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
      className: "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6",
      children: [/* @__PURE__ */ jsxRuntimeExports.jsxs(Card, {
        children: [/* @__PURE__ */ jsxRuntimeExports.jsx(CardHeader, {
          className: "flex flex-row items-center justify-between space-y-0 pb-2",
          children: /* @__PURE__ */ jsxRuntimeExports.jsx(CardTitle, {
            className: "text-sm font-medium",
            children: "Revenue"
          })
        }), /* @__PURE__ */ jsxRuntimeExports.jsxs(CardContent, {
          children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
            className: "text-2xl font-bold",
            children: ["₹ ", customerDetails.totalAmount.toLocaleString()]
          }), /* @__PURE__ */ jsxRuntimeExports.jsxs("p", {
            className: "text-xs text-muted-foreground",
            children: ["from ", customerDetails.totalOrders, " orders"]
          })]
        })]
      }), /* @__PURE__ */ jsxRuntimeExports.jsxs(Card, {
        children: [/* @__PURE__ */ jsxRuntimeExports.jsx(CardHeader, {
          className: "flex flex-row items-center justify-between space-y-0 pb-2",
          children: /* @__PURE__ */ jsxRuntimeExports.jsx(CardTitle, {
            className: "text-sm font-medium",
            children: "Pending Payments"
          })
        }), /* @__PURE__ */ jsxRuntimeExports.jsxs(CardContent, {
          children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
            className: "text-2xl font-bold",
            children: ["₹ ", customerDetails.pendingAmount.toLocaleString()]
          }), /* @__PURE__ */ jsxRuntimeExports.jsx("p", {
            className: "text-xs text-muted-foreground",
            children: "total pending amount"
          })]
        })]
      })]
    }), /* @__PURE__ */ jsxRuntimeExports.jsxs(Tabs, {
      value: activeTab,
      onValueChange: handleTabChange,
      children: [/* @__PURE__ */ jsxRuntimeExports.jsxs(TabsList, {
        children: [/* @__PURE__ */ jsxRuntimeExports.jsx(TabsTrigger, {
          value: "dashboard",
          children: "Dashboard"
        }), /* @__PURE__ */ jsxRuntimeExports.jsx(TabsTrigger, {
          value: "orders",
          children: "Orders"
        }), cPriceEnable && /* @__PURE__ */ jsxRuntimeExports.jsx(TabsTrigger, {
          value: "contractprice",
          children: "Contract Prices"
        })]
      }), activeTab !== "contractprice" && /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
        className: "flex justify-between items-center my-4",
        children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Input, {
          placeholder: "Search by order ID, status, or seller",
          value: searchTerm,
          onChange: (e) => setSearchTerm(e.target.value),
          className: "max-w-sm"
        }), /* @__PURE__ */ jsxRuntimeExports.jsxs(Select, {
          value: pageSize,
          onValueChange: handlePageSizeChange,
          children: [/* @__PURE__ */ jsxRuntimeExports.jsx(SelectTrigger, {
            className: "w-[180px]",
            children: /* @__PURE__ */ jsxRuntimeExports.jsx(SelectValue, {
              placeholder: "Items per page"
            })
          }), /* @__PURE__ */ jsxRuntimeExports.jsxs(SelectContent, {
            children: [/* @__PURE__ */ jsxRuntimeExports.jsx(SelectItem, {
              value: "5",
              children: "5 per page"
            }), /* @__PURE__ */ jsxRuntimeExports.jsx(SelectItem, {
              value: "10",
              children: "10 per page"
            }), /* @__PURE__ */ jsxRuntimeExports.jsx(SelectItem, {
              value: "20",
              children: "20 per page"
            }), /* @__PURE__ */ jsxRuntimeExports.jsx(SelectItem, {
              value: "50",
              children: "50 per page"
            })]
          })]
        })]
      }), /* @__PURE__ */ jsxRuntimeExports.jsxs(TabsContent, {
        value: "orders",
        children: [/* @__PURE__ */ jsxRuntimeExports.jsxs(Table, {
          children: [/* @__PURE__ */ jsxRuntimeExports.jsx(TableHeader, {
            children: /* @__PURE__ */ jsxRuntimeExports.jsxs(TableRow, {
              children: [/* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
                children: "Order ID"
              }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
                children: "Order Date"
              }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
                children: "Status"
              }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
                children: "Delivery Date"
              }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
                children: "Amount"
              }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
                children: "Payment Status"
              })]
            })
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableBody, {
            children: orders.map((order) => /* @__PURE__ */ jsxRuntimeExports.jsxs(TableRow, {
              children: [/* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
                children: order.orderGroupId
              }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
                children: new Date(order.orderDateTime).toLocaleDateString("en-IN", {
                  day: "numeric",
                  month: "short",
                  year: "2-digit"
                })
              }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
                children: /* @__PURE__ */ jsxRuntimeExports.jsx(Badge, {
                  variant: order.orderStatus === "Delivered" ? "default" : "secondary",
                  children: order.orderStatus
                })
              }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
                children: order.deliveryDate
              }), /* @__PURE__ */ jsxRuntimeExports.jsxs(TableCell, {
                children: ["₹ ", order.orderAmount.toLocaleString()]
              }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
                children: order.paymentStatus ? "Paid" : "Pending"
              })]
            }, order.orderGroupId))
          })]
        }), renderPagination(currentPage)]
      }), /* @__PURE__ */ jsxRuntimeExports.jsx(TabsContent, {
        value: "payments",
        children: /* @__PURE__ */ jsxRuntimeExports.jsx("p", {
          children: "Payments content (to be implemented with payment data)"
        })
      }), /* @__PURE__ */ jsxRuntimeExports.jsx(TabsContent, {
        value: "notifications",
        children: /* @__PURE__ */ jsxRuntimeExports.jsx("p", {
          children: "Notifications content (to be implemented)"
        })
      }), /* @__PURE__ */ jsxRuntimeExports.jsx(TabsContent, {
        value: "support",
        children: /* @__PURE__ */ jsxRuntimeExports.jsx("p", {
          children: "Support Tickets content (to be implemented)"
        })
      }), cPriceEnable && /* @__PURE__ */ jsxRuntimeExports.jsxs(TabsContent, {
        value: "contractprice",
        children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
          className: "flex flex-col md:flex-row justify-between items-center gap-4 my-4",
          children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Input, {
            placeholder: "Search by item name",
            value: searchTerm,
            onChange: (e) => setSearchTerm(e.target.value),
            className: "w-full md:max-w-xs rounded-full"
          }), /* @__PURE__ */ jsxRuntimeExports.jsxs(Form, {
            className: "flex flex-col md:flex-row items-center gap-4 w-full md:w-auto",
            method: "post",
            children: [/* @__PURE__ */ jsxRuntimeExports.jsx("input", {
              type: "hidden",
              name: "intent",
              value: "UpdateContractPriceValidity"
            }), /* @__PURE__ */ jsxRuntimeExports.jsx("input", {
              type: "hidden",
              name: "buyerId",
              value: buyerId.toString()
            }), /* @__PURE__ */ jsxRuntimeExports.jsx("input", {
              type: "hidden",
              name: "selectedDate",
              value: selectedCDate.toString()
            }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
              className: "flex flex-col md:flex-row items-center gap-2",
              children: [/* @__PURE__ */ jsxRuntimeExports.jsx("label", {
                htmlFor: "dashboardGroupBy",
                className: "text-sm font-medium text-gray-700",
                children: "Contract Price Validity:"
              }), /* @__PURE__ */ jsxRuntimeExports.jsx(Input, {
                value: selectedCDate.toISOString().split("T")[0],
                onChange: (e) => {
                  const date = new Date(e.target.value);
                  setSelectedCDate(date);
                },
                type: "date",
                className: "w-full md:max-w-xs bg-orange-100 border border-orange-300 rounded-md focus:ring-2 focus:ring-orange-400 focus:outline-none custom-date-input cursor-pointer"
              })]
            }), /* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
              className: "bg-orange-400 hover:bg-orange-500 text-white font-bold px-4 py-2 rounded-md transition-all",
              size: "sm",
              type: "submit",
              children: "SAVE"
            })]
          }), /* @__PURE__ */ jsxRuntimeExports.jsxs(Select, {
            value: pageSize,
            onValueChange: handlePageSizeChange,
            children: [/* @__PURE__ */ jsxRuntimeExports.jsx(SelectTrigger, {
              className: "w-full md:w-[180px] border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-400 focus:outline-none",
              children: /* @__PURE__ */ jsxRuntimeExports.jsx(SelectValue, {
                placeholder: "Items per page"
              })
            }), /* @__PURE__ */ jsxRuntimeExports.jsxs(SelectContent, {
              children: [/* @__PURE__ */ jsxRuntimeExports.jsx(SelectItem, {
                value: "5",
                children: "5 per page"
              }), /* @__PURE__ */ jsxRuntimeExports.jsx(SelectItem, {
                value: "10",
                children: "10 per page"
              }), /* @__PURE__ */ jsxRuntimeExports.jsx(SelectItem, {
                value: "20",
                children: "20 per page"
              }), /* @__PURE__ */ jsxRuntimeExports.jsx(SelectItem, {
                value: "50",
                children: "50 per page"
              })]
            })]
          })]
        }), /* @__PURE__ */ jsxRuntimeExports.jsxs(Table, {
          children: [/* @__PURE__ */ jsxRuntimeExports.jsx(TableHeader, {
            children: /* @__PURE__ */ jsxRuntimeExports.jsxs(TableRow, {
              children: [/* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
                children: "Item Image"
              }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
                children: "Item Name"
              }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
                children: "Price"
              }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
                children: "Enabled"
              })]
            })
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableBody, {
            children: contractPrices.filter((contract) => handlefilter(contract)).map((price) => /* @__PURE__ */ jsxRuntimeExports.jsxs(TableRow, {
              children: [/* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
                children: /* @__PURE__ */ jsxRuntimeExports.jsx("img", {
                  src: price.picture,
                  alt: price.itemName,
                  className: "h-10 w-10"
                })
              }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
                children: price.itemName
              }), price.newItem === false && /* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
                className: "flex gap-2",
                children: isEdit && uniqueId === price.sellerItemId ? /* @__PURE__ */ jsxRuntimeExports.jsxs(jsxRuntimeExports.Fragment, {
                  children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Input, {
                    placeholder: price.cbItemPrice.toString(),
                    onChange: (e) => {
                      const value = Number(e.target.value);
                      if (value > 0 || e.target.value === "") {
                        setCPrice(value);
                      }
                    },
                    type: "number",
                    className: "w-20 h-10 "
                  }), /* @__PURE__ */ jsxRuntimeExports.jsx(Save, {
                    height: 20,
                    width: 20,
                    onClick: () => handleUpdatePrice(price.sellerItemId, price.contractPriceId, cPrice),
                    className: "my-3 cursor-pointer "
                  })]
                }) : /* @__PURE__ */ jsxRuntimeExports.jsxs(jsxRuntimeExports.Fragment, {
                  children: [" ", `₹ ${price.cbItemPrice}`, cPriceEditable && /* @__PURE__ */ jsxRuntimeExports.jsx(SquarePen, {
                    height: 20,
                    width: 20,
                    onClick: () => handleEdit(price.sellerItemId)
                  })]
                })
              }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
                children: price.newItem ? /* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
                  type: "button",
                  onClick: () => handleAddContractPrice(price.sellerItemId, buyerId),
                  children: price.sellerItemId === uniqueId && fetcher.state !== "idle" ? "ADDING..." : "ADD"
                }) : /* @__PURE__ */ jsxRuntimeExports.jsx(Switch, {
                  checked: price.enabled,
                  onCheckedChange: () => handleUpdateStatus(price.sellerItemId, price.contractPriceId, !price.enabled)
                })
              })]
            }, price.sellerItemId))
          })]
        }), renderPagination(currentPage)]
      })]
    })]
  });
}
export {
  CustomerDetailsPage as default
};
//# sourceMappingURL=home.customer-details._customerId-BRCn1ZRx.js.map
