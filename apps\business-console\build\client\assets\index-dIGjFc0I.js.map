{"version": 3, "file": "index-dIGjFc0I.js", "sources": ["../../../node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs"], "sourcesContent": ["// packages/react/use-controllable-state/src/useControllableState.tsx\nimport * as React from \"react\";\nimport { useCallbackRef } from \"@radix-ui/react-use-callback-ref\";\nfunction useControllableState({\n  prop,\n  defaultProp,\n  onChange = () => {\n  }\n}) {\n  const [uncontrolledProp, setUncontrolledProp] = useUncontrolledState({ defaultProp, onChange });\n  const isControlled = prop !== void 0;\n  const value = isControlled ? prop : uncontrolledProp;\n  const handleChange = useCallbackRef(onChange);\n  const setValue = React.useCallback(\n    (nextValue) => {\n      if (isControlled) {\n        const setter = nextValue;\n        const value2 = typeof nextValue === \"function\" ? setter(prop) : nextValue;\n        if (value2 !== prop) handleChange(value2);\n      } else {\n        setUncontrolledProp(nextValue);\n      }\n    },\n    [isControlled, prop, setUncontrolledProp, handleChange]\n  );\n  return [value, setValue];\n}\nfunction useUncontrolledState({\n  defaultProp,\n  onChange\n}) {\n  const uncontrolledState = React.useState(defaultProp);\n  const [value] = uncontrolledState;\n  const prevValueRef = React.useRef(value);\n  const handleChange = useCallbackRef(onChange);\n  React.useEffect(() => {\n    if (prevValueRef.current !== value) {\n      handleChange(value);\n      prevValueRef.current = value;\n    }\n  }, [value, prevValueRef, handleChange]);\n  return uncontrolledState;\n}\nexport {\n  useControllableState\n};\n//# sourceMappingURL=index.mjs.map\n"], "names": ["React.useCallback", "React.useState", "React.useRef", "React.useEffect"], "mappings": ";;AAGA,SAAS,qBAAqB;AAAA,EAC5B;AAAA,EACA;AAAA,EACA,WAAW,MAAM;AAAA,EACnB;AACA,GAAG;AACD,QAAM,CAAC,kBAAkB,mBAAmB,IAAI,qBAAqB,EAAE,aAAa,UAAU;AAC9F,QAAM,eAAe,SAAS;AAC9B,QAAM,QAAQ,eAAe,OAAO;AACpC,QAAM,eAAe,eAAe,QAAQ;AAC5C,QAAM,WAAWA,aAAiB;AAAA,IAChC,CAAC,cAAc;AACb,UAAI,cAAc;AAChB,cAAM,SAAS;AACf,cAAM,SAAS,OAAO,cAAc,aAAa,OAAO,IAAI,IAAI;AAChE,YAAI,WAAW,KAAM,cAAa,MAAM;AAAA,MAChD,OAAa;AACL,4BAAoB,SAAS;AAAA,MACrC;AAAA,IACK;AAAA,IACD,CAAC,cAAc,MAAM,qBAAqB,YAAY;AAAA,EACvD;AACD,SAAO,CAAC,OAAO,QAAQ;AACzB;AACA,SAAS,qBAAqB;AAAA,EAC5B;AAAA,EACA;AACF,GAAG;AACD,QAAM,oBAAoBC,aAAc,SAAC,WAAW;AACpD,QAAM,CAAC,KAAK,IAAI;AAChB,QAAM,eAAeC,aAAY,OAAC,KAAK;AACvC,QAAM,eAAe,eAAe,QAAQ;AAC5CC,eAAAA,UAAgB,MAAM;AACpB,QAAI,aAAa,YAAY,OAAO;AAClC,mBAAa,KAAK;AAClB,mBAAa,UAAU;AAAA,IAC7B;AAAA,EACG,GAAE,CAAC,OAAO,cAAc,YAAY,CAAC;AACtC,SAAO;AACT;", "x_google_ignoreList": [0]}