{"version": 3, "file": "switch-DQ_qW6ch.js", "sources": ["../../../node_modules/@radix-ui/react-switch/dist/index.mjs", "../../../app/components/ui/switch.tsx"], "sourcesContent": ["\"use client\";\n\n// packages/react/switch/src/switch.tsx\nimport * as React from \"react\";\nimport { composeEventHandlers } from \"@radix-ui/primitive\";\nimport { useComposedRefs } from \"@radix-ui/react-compose-refs\";\nimport { createContextScope } from \"@radix-ui/react-context\";\nimport { useControllableState } from \"@radix-ui/react-use-controllable-state\";\nimport { usePrevious } from \"@radix-ui/react-use-previous\";\nimport { useSize } from \"@radix-ui/react-use-size\";\nimport { Primitive } from \"@radix-ui/react-primitive\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar SWITCH_NAME = \"Switch\";\nvar [createSwitchContext, createSwitchScope] = createContextScope(SWITCH_NAME);\nvar [SwitchProvider, useSwitchContext] = createSwitchContext(SWITCH_NAME);\nvar Switch = React.forwardRef(\n  (props, forwardedRef) => {\n    const {\n      __scopeSwitch,\n      name,\n      checked: checkedProp,\n      defaultChecked,\n      required,\n      disabled,\n      value = \"on\",\n      onCheckedChange,\n      form,\n      ...switchProps\n    } = props;\n    const [button, setButton] = React.useState(null);\n    const composedRefs = useComposedRefs(forwardedRef, (node) => setButton(node));\n    const hasConsumerStoppedPropagationRef = React.useRef(false);\n    const isFormControl = button ? form || !!button.closest(\"form\") : true;\n    const [checked = false, setChecked] = useControllableState({\n      prop: checkedProp,\n      defaultProp: defaultChecked,\n      onChange: onCheckedChange\n    });\n    return /* @__PURE__ */ jsxs(SwitchProvider, { scope: __scopeSwitch, checked, disabled, children: [\n      /* @__PURE__ */ jsx(\n        Primitive.button,\n        {\n          type: \"button\",\n          role: \"switch\",\n          \"aria-checked\": checked,\n          \"aria-required\": required,\n          \"data-state\": getState(checked),\n          \"data-disabled\": disabled ? \"\" : void 0,\n          disabled,\n          value,\n          ...switchProps,\n          ref: composedRefs,\n          onClick: composeEventHandlers(props.onClick, (event) => {\n            setChecked((prevChecked) => !prevChecked);\n            if (isFormControl) {\n              hasConsumerStoppedPropagationRef.current = event.isPropagationStopped();\n              if (!hasConsumerStoppedPropagationRef.current) event.stopPropagation();\n            }\n          })\n        }\n      ),\n      isFormControl && /* @__PURE__ */ jsx(\n        BubbleInput,\n        {\n          control: button,\n          bubbles: !hasConsumerStoppedPropagationRef.current,\n          name,\n          value,\n          checked,\n          required,\n          disabled,\n          form,\n          style: { transform: \"translateX(-100%)\" }\n        }\n      )\n    ] });\n  }\n);\nSwitch.displayName = SWITCH_NAME;\nvar THUMB_NAME = \"SwitchThumb\";\nvar SwitchThumb = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeSwitch, ...thumbProps } = props;\n    const context = useSwitchContext(THUMB_NAME, __scopeSwitch);\n    return /* @__PURE__ */ jsx(\n      Primitive.span,\n      {\n        \"data-state\": getState(context.checked),\n        \"data-disabled\": context.disabled ? \"\" : void 0,\n        ...thumbProps,\n        ref: forwardedRef\n      }\n    );\n  }\n);\nSwitchThumb.displayName = THUMB_NAME;\nvar BubbleInput = (props) => {\n  const { control, checked, bubbles = true, ...inputProps } = props;\n  const ref = React.useRef(null);\n  const prevChecked = usePrevious(checked);\n  const controlSize = useSize(control);\n  React.useEffect(() => {\n    const input = ref.current;\n    const inputProto = window.HTMLInputElement.prototype;\n    const descriptor = Object.getOwnPropertyDescriptor(inputProto, \"checked\");\n    const setChecked = descriptor.set;\n    if (prevChecked !== checked && setChecked) {\n      const event = new Event(\"click\", { bubbles });\n      setChecked.call(input, checked);\n      input.dispatchEvent(event);\n    }\n  }, [prevChecked, checked, bubbles]);\n  return /* @__PURE__ */ jsx(\n    \"input\",\n    {\n      type: \"checkbox\",\n      \"aria-hidden\": true,\n      defaultChecked: checked,\n      ...inputProps,\n      tabIndex: -1,\n      ref,\n      style: {\n        ...props.style,\n        ...controlSize,\n        position: \"absolute\",\n        pointerEvents: \"none\",\n        opacity: 0,\n        margin: 0\n      }\n    }\n  );\n};\nfunction getState(checked) {\n  return checked ? \"checked\" : \"unchecked\";\n}\nvar Root = Switch;\nvar Thumb = SwitchThumb;\nexport {\n  Root,\n  Switch,\n  SwitchThumb,\n  Thumb,\n  createSwitchScope\n};\n//# sourceMappingURL=index.mjs.map\n", "import * as React from \"react\"\r\nimport * as SwitchPrimitives from \"@radix-ui/react-switch\"\r\n\r\nimport { cn } from \"~/lib/utils\"\r\n\r\nconst Switch = React.forwardRef<\r\n  React.ElementRef<typeof SwitchPrimitives.Root>,\r\n  React.ComponentPropsWithoutRef<typeof SwitchPrimitives.Root>\r\n>(({ className, ...props }, ref) => (\r\n  <SwitchPrimitives.Root\r\n    className={cn(\r\n      \"peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input\",\r\n      className\r\n    )}\r\n    {...props}\r\n    ref={ref}\r\n  >\r\n    <SwitchPrimitives.Thumb\r\n      className={cn(\r\n        \"pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0\"\r\n      )}\r\n    />\r\n  </SwitchPrimitives.Root>\r\n))\r\nSwitch.displayName = SwitchPrimitives.Root.displayName\r\n\r\nexport { Switch }\r\n"], "names": ["Switch", "React.forwardRef", "React.useState", "React.useRef", "jsxs", "jsx", "React.useEffect", "SwitchPrimitives.Root", "SwitchPrimitives.Thumb"], "mappings": ";;;;;;;;AAYA,IAAI,cAAc;AAClB,IAAI,CAAC,qBAAqB,iBAAiB,IAAI,mBAAmB,WAAW;AAC7E,IAAI,CAAC,gBAAgB,gBAAgB,IAAI,oBAAoB,WAAW;AACxE,IAAIA,WAASC,aAAgB;AAAA,EAC3B,CAAC,OAAO,iBAAiB;AACvB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA,SAAS;AAAA,MACT;AAAA,MACA;AAAA,MACA;AAAA,MACA,QAAQ;AAAA,MACR;AAAA,MACA;AAAA,MACA,GAAG;AAAA,IACT,IAAQ;AACJ,UAAM,CAAC,QAAQ,SAAS,IAAIC,aAAAA,SAAe,IAAI;AAC/C,UAAM,eAAe,gBAAgB,cAAc,CAAC,SAAS,UAAU,IAAI,CAAC;AAC5E,UAAM,mCAAmCC,aAAY,OAAC,KAAK;AAC3D,UAAM,gBAAgB,SAAS,QAAQ,CAAC,CAAC,OAAO,QAAQ,MAAM,IAAI;AAClE,UAAM,CAAC,UAAU,OAAO,UAAU,IAAI,qBAAqB;AAAA,MACzD,MAAM;AAAA,MACN,aAAa;AAAA,MACb,UAAU;AAAA,IAChB,CAAK;AACD,WAAuBC,kCAAAA,KAAK,gBAAgB,EAAE,OAAO,eAAe,SAAS,UAAU,UAAU;AAAA,MAC/EC,kCAAG;AAAA,QACjB,UAAU;AAAA,QACV;AAAA,UACE,MAAM;AAAA,UACN,MAAM;AAAA,UACN,gBAAgB;AAAA,UAChB,iBAAiB;AAAA,UACjB,cAAc,SAAS,OAAO;AAAA,UAC9B,iBAAiB,WAAW,KAAK;AAAA,UACjC;AAAA,UACA;AAAA,UACA,GAAG;AAAA,UACH,KAAK;AAAA,UACL,SAAS,qBAAqB,MAAM,SAAS,CAAC,UAAU;AACtD,uBAAW,CAAC,gBAAgB,CAAC,WAAW;AACxC,gBAAI,eAAe;AACjB,+CAAiC,UAAU,MAAM,qBAAsB;AACvE,kBAAI,CAAC,iCAAiC,QAAS,OAAM,gBAAiB;AAAA,YACpF;AAAA,UACW,CAAA;AAAA,QACX;AAAA,MACO;AAAA,MACD,iBAAiCA,kCAAG;AAAA,QAClC;AAAA,QACA;AAAA,UACE,SAAS;AAAA,UACT,SAAS,CAAC,iCAAiC;AAAA,UAC3C;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA,OAAO,EAAE,WAAW,oBAAmB;AAAA,QACjD;AAAA,MACA;AAAA,IACA,GAAO;AAAA,EACP;AACA;AACAL,SAAO,cAAc;AACrB,IAAI,aAAa;AACjB,IAAI,cAAcC,aAAgB;AAAA,EAChC,CAAC,OAAO,iBAAiB;AACvB,UAAM,EAAE,eAAe,GAAG,WAAU,IAAK;AACzC,UAAM,UAAU,iBAAiB,YAAY,aAAa;AAC1D,WAAuBI,kCAAG;AAAA,MACxB,UAAU;AAAA,MACV;AAAA,QACE,cAAc,SAAS,QAAQ,OAAO;AAAA,QACtC,iBAAiB,QAAQ,WAAW,KAAK;AAAA,QACzC,GAAG;AAAA,QACH,KAAK;AAAA,MACb;AAAA,IACK;AAAA,EACL;AACA;AACA,YAAY,cAAc;AAC1B,IAAI,cAAc,CAAC,UAAU;AAC3B,QAAM,EAAE,SAAS,SAAS,UAAU,MAAM,GAAG,WAAU,IAAK;AAC5D,QAAM,MAAMF,aAAY,OAAC,IAAI;AAC7B,QAAM,cAAc,YAAY,OAAO;AACvC,QAAM,cAAc,QAAQ,OAAO;AACnCG,eAAAA,UAAgB,MAAM;AACpB,UAAM,QAAQ,IAAI;AAClB,UAAM,aAAa,OAAO,iBAAiB;AAC3C,UAAM,aAAa,OAAO,yBAAyB,YAAY,SAAS;AACxE,UAAM,aAAa,WAAW;AAC9B,QAAI,gBAAgB,WAAW,YAAY;AACzC,YAAM,QAAQ,IAAI,MAAM,SAAS,EAAE,QAAO,CAAE;AAC5C,iBAAW,KAAK,OAAO,OAAO;AAC9B,YAAM,cAAc,KAAK;AAAA,IAC/B;AAAA,EACG,GAAE,CAAC,aAAa,SAAS,OAAO,CAAC;AAClC,SAAuBD,kCAAG;AAAA,IACxB;AAAA,IACA;AAAA,MACE,MAAM;AAAA,MACN,eAAe;AAAA,MACf,gBAAgB;AAAA,MAChB,GAAG;AAAA,MACH,UAAU;AAAA,MACV;AAAA,MACA,OAAO;AAAA,QACL,GAAG,MAAM;AAAA,QACT,GAAG;AAAA,QACH,UAAU;AAAA,QACV,eAAe;AAAA,QACf,SAAS;AAAA,QACT,QAAQ;AAAA,MAChB;AAAA,IACA;AAAA,EACG;AACH;AACA,SAAS,SAAS,SAAS;AACzB,SAAO,UAAU,YAAY;AAC/B;AACA,IAAI,OAAOL;AACX,IAAI,QAAQ;ACnIN,MAAA,SAASC,aAGb,WAAA,CAAC,EAAE,WAAW,GAAG,MAAM,GAAG,QAC1BI,kCAAA;AAAA,EAACE;AAAAA,EAAA;AAAA,IACC,WAAW;AAAA,MACT;AAAA,MACA;AAAA,IACF;AAAA,IACC,GAAG;AAAA,IACJ;AAAA,IAEA,UAAAF,kCAAA;AAAA,MAACG;AAAAA,MAAA;AAAA,QACC,WAAW;AAAA,UACT;AAAA,QAAA;AAAA,MACF;AAAA,IAAA;AAAA,EACF;AACF,CACD;AACD,OAAO,cAAcD,KAAsB;", "x_google_ignoreList": [0]}