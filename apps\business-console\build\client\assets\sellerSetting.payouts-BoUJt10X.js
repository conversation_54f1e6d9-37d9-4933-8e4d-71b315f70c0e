import { j as jsxRuntimeExports } from "./jsx-runtime-bB2y7OuD.js";
import { u as useLoaderData } from "./components-D7UvGag_.js";
import "./index-Vp2vNLNM.js";
import "./index-DhHTcibu.js";
function Payouts() {
  const {
    embedUrl
  } = useLoaderData();
  return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
    className: "p-6",
    children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
      className: "mb-6",
      children: [/* @__PURE__ */ jsxRuntimeExports.jsx("h1", {
        className: "text-3xl font-bold text-gray-900",
        children: "Payouts"
      }), /* @__PURE__ */ jsxRuntimeExports.jsx("p", {
        className: "text-gray-600 mt-2",
        children: "Manage your earnings and payments"
      })]
    }), /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
      className: "w-full h-screen",
      children: embedUrl ? /* @__PURE__ */ jsxRuntimeExports.jsx("iframe", {
        id: "metabase-payouts-iframe",
        src: embedUrl,
        title: "Payouts Dashboard",
        className: "w-full h-full border-0"
      }) : /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
        className: "flex items-center justify-center min-h-[400px]",
        children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
          className: "text-center",
          children: [/* @__PURE__ */ jsxRuntimeExports.jsx("div", {
            className: "mx-auto mb-4 w-16 h-16 bg-gradient-to-r from-green-500 to-yellow-500 rounded-full flex items-center justify-center",
            children: /* @__PURE__ */ jsxRuntimeExports.jsx("svg", {
              className: "w-8 h-8 text-white",
              fill: "none",
              stroke: "currentColor",
              viewBox: "0 0 24 24",
              children: /* @__PURE__ */ jsxRuntimeExports.jsx("path", {
                strokeLinecap: "round",
                strokeLinejoin: "round",
                strokeWidth: 2,
                d: "M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"
              })
            })
          }), /* @__PURE__ */ jsxRuntimeExports.jsx("h2", {
            className: "text-2xl font-bold mb-2",
            children: "Payouts Dashboard Loading"
          }), /* @__PURE__ */ jsxRuntimeExports.jsx("p", {
            className: "text-gray-500",
            children: "Loading your payouts and earnings data..."
          })]
        })
      })
    })]
  });
}
export {
  Payouts as default
};
//# sourceMappingURL=sellerSetting.payouts-BoUJt10X.js.map
