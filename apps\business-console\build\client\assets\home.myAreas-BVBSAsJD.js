import { r as reactExports, j as jsxRuntimeExports } from "./jsx-runtime-bB2y7OuD.js";
import { B as Button } from "./button-ByAXMyvk.js";
import { I as Input } from "./input-3v87qohQ.js";
import { T as Table, a as TableHeader, b as TableRow, c as TableHead, d as TableBody, e as TableCell } from "./table-CAxdJZsY.js";
import { R as RefreshCcw } from "./refresh-ccw-VPDpzaz8.js";
import "./index-z_byfFrQ.js";
import "./index-ImHKLo0a.js";
import "./utils-GkgzjW3c.js";
import "./createLucideIcon-uwkRm45G.js";
function MyAreas() {
  const [searchTerm, setSearchTerm] = reactExports.useState("");
  return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
    className: "container mx-auto p-6 ",
    children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
      className: "flex justify-between items-center mb-6",
      children: [/* @__PURE__ */ jsxRuntimeExports.jsx("h1", {
        className: "text-2xl font-bold",
        children: "My Areas"
      }), /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
        className: "flex gap-2",
        children: /* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
          variant: "outline",
          size: "icon",
          children: /* @__PURE__ */ jsxRuntimeExports.jsx(RefreshCcw, {
            className: "h-4 w-4"
          })
        })
      })]
    }), /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
      className: "flex justify-between mb-4",
      children: /* @__PURE__ */ jsxRuntimeExports.jsx(Input, {
        placeholder: "Search by name",
        value: searchTerm,
        onChange: (e) => setSearchTerm(e.target.value),
        className: "max-w-sm"
      })
    }), /* @__PURE__ */ jsxRuntimeExports.jsxs(Table, {
      children: [/* @__PURE__ */ jsxRuntimeExports.jsx(TableHeader, {
        children: /* @__PURE__ */ jsxRuntimeExports.jsxs(TableRow, {
          children: [/* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
            className: "cursor-pointer",
            children: "Id"
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
            className: "cursor-pointer",
            children: "AreaName"
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
            className: "cursor-pointer",
            children: "Area Manager"
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
            className: "cursor-pointer",
            children: " Is Active"
          })]
        })
      }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableBody, {
        children: /* @__PURE__ */ jsxRuntimeExports.jsx(TableRow, {
          children: /* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
            children: "12312"
          })
        })
      })]
    })]
  });
}
export {
  MyAreas as default
};
//# sourceMappingURL=home.myAreas-BVBSAsJD.js.map
