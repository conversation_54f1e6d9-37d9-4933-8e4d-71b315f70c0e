import { r as reactExports, j as jsxRuntimeExports } from "./jsx-runtime-bB2y7OuD.js";
import { S as SpinnerLoader } from "./SpinnerLoader-CAZRmIJX.js";
import { B as Button } from "./button-ByAXMyvk.js";
import { u as useToast } from "./ToastProvider-rciVe3-g.js";
import { L as Label } from "./label-cSASrwzW.js";
import { I as Input } from "./input-3v87qohQ.js";
import { a as useFetcher } from "./components-D7UvGag_.js";
import { T as Trash } from "./trash-cZnr6Uhr.js";
import { L as LoaderCircle } from "./loader-circle-BLZgch8Y.js";
import { c as createLucideIcon } from "./createLucideIcon-uwkRm45G.js";
import { R as RadioGroup, a as RadioGroupItem } from "./radio-group-ChzooXbR.js";
import { X } from "./x-CCG_WJDF.js";
import { S as SquarePen } from "./square-pen-BXxSi9JH.js";
import { P as Pencil } from "./pencil-C1goHmP8.js";
import { S as Save } from "./save-xzNIILKr.js";
import "./index-z_byfFrQ.js";
import "./index-ImHKLo0a.js";
import "./utils-GkgzjW3c.js";
import "./index-CzZ6EYkN.js";
import "./index-Vp2vNLNM.js";
import "./index-DhHTcibu.js";
import "./index-D7VH9Fc8.js";
import "./index-CG37gmC0.js";
import "./index-qCtcpOcW.js";
import "./index-DVTNuYOr.js";
import "./index-dIGjFc0I.js";
import "./index-BTdCMChR.js";
import "./index-CkL5tk39.js";
import "./index-CpKiYcZd.js";
import "./index-QLGF6kQx.js";
/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */
const Upload = createLucideIcon("Upload", [
  ["path", { d: "M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4", key: "ih7n3h" }],
  ["polyline", { points: "17 8 12 3 7 8", key: "t8dd8p" }],
  ["line", { x1: "12", x2: "12", y1: "3", y2: "15", key: "widbto" }]
]);
const ImageUploadComponent = ({ onChange, multiple = false }) => {
  const fileInputRef = reactExports.useRef(null);
  const [selectedFiles, setSelectedFiles] = reactExports.useState([]);
  const [previewUrls, setPreviewUrls] = reactExports.useState([]);
  const [uploadError, setUploadError] = reactExports.useState(null);
  const [uploading, setUploading] = reactExports.useState(false);
  const { showToast } = useToast();
  const uploadFetcher = useFetcher();
  const handleFileSelect = (event) => {
    const files = Array.from(event.target.files || []);
    if (files.length === 0) return;
    setUploadError(null);
    const MAX_FILE_SIZE = 500 * 1024;
    const allowedTypes = ["image/jpeg", "image/png", "image/gif", "image/webp"];
    const validFiles = files.filter((file) => {
      if (file.size > MAX_FILE_SIZE) {
        setUploadError("File size exceeds 500Kb limit.");
        return false;
      }
      if (!allowedTypes.includes(file.type)) {
        setUploadError("Only JPEG, PNG, GIF, and WEBP images are allowed.");
        return false;
      }
      return true;
    });
    if (validFiles.length === 0) return;
    setSelectedFiles(multiple ? [...selectedFiles, ...validFiles] : [validFiles[0]]);
    setPreviewUrls(multiple ? [...previewUrls, ...validFiles.map((file) => URL.createObjectURL(file))] : [URL.createObjectURL(validFiles[0])]);
  };
  const handleUpload = () => {
    if (selectedFiles.length === 0) return;
    setUploading(true);
    const formData = new FormData();
    formData.append("_action", "uploadImage");
    selectedFiles.forEach((file) => {
      formData.append("file", file, file.name);
    });
    uploadFetcher.submit(formData, {
      method: "post",
      action: "/home/<USER>",
      encType: "multipart/form-data"
    });
  };
  reactExports.useEffect(() => {
    if (uploadFetcher.data) {
      if (uploadFetcher.data.error) {
        setUploadError(uploadFetcher.data.error);
      } else if (uploadFetcher.data.fileUrl) {
        const uploadedUrls = multiple ? [...previewUrls, uploadFetcher.data.fileUrl] : [uploadFetcher.data.fileUrl];
        onChange(multiple ? uploadedUrls : uploadFetcher.data.fileUrl);
        setSelectedFiles([]);
        setPreviewUrls(uploadedUrls);
        setUploadError(null);
        if (fileInputRef.current) fileInputRef.current.value = "";
      }
      setUploading(false);
    }
  }, [uploadFetcher.data, onChange]);
  const handleRemoveImage = (index, e) => {
    e == null ? void 0 : e.preventDefault();
    e == null ? void 0 : e.stopPropagation();
    const updatedFiles = selectedFiles.filter((_, i) => i !== index);
    const updatedPreviews = previewUrls.filter((_, i) => i !== index);
    setSelectedFiles(updatedFiles);
    setPreviewUrls(updatedPreviews);
    onChange(multiple ? updatedPreviews : "");
  };
  return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "p-6 border border-gray-200 rounded-xl shadow-lg bg-gradient-to-br from-gray-50 to-gray-100 transition-all duration-300 hover:shadow-xl", children: [
    /* @__PURE__ */ jsxRuntimeExports.jsx(Label, { className: "block text-lg font-semibold text-gray-800 mb-3", children: "Upload Image" }),
    /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "mt-4 flex flex-col items-center gap-6", children: [
      previewUrls.length > 0 && /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "flex flex-wrap gap-4 justify-center", children: previewUrls.map((url, index) => /* @__PURE__ */ jsxRuntimeExports.jsxs(
        "div",
        {
          className: "relative w-44 h-44 group transition-transform duration-300 hover:scale-105",
          children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx(
              "img",
              {
                src: url,
                alt: "Selected Preview",
                className: "w-full h-full object-cover rounded-lg border border-gray-200 shadow-md"
              }
            ),
            /* @__PURE__ */ jsxRuntimeExports.jsx(
              "button",
              {
                onClick: (e) => handleRemoveImage(index, e),
                className: "absolute -top-3 -right-3 bg-red-600 text-white p-2 rounded-full shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 hover:bg-red-700",
                children: /* @__PURE__ */ jsxRuntimeExports.jsx(Trash, { className: "h-5 w-5" })
              }
            )
          ]
        },
        index
      )) }),
      /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex gap-3 items-center w-full max-w-lg", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx(
          Input,
          {
            type: "text",
            readOnly: true,
            value: selectedFiles.length > 0 ? `${selectedFiles.length} file(s) selected` : "No file selected",
            className: "flex-grow cursor-pointer bg-gray-50 border-gray-200 rounded-lg px-4 py-2 text-gray-600 focus:ring-2 focus:ring-blue-300 transition-all duration-200",
            onClick: () => {
              var _a;
              return (_a = fileInputRef.current) == null ? void 0 : _a.click();
            }
          }
        ),
        /* @__PURE__ */ jsxRuntimeExports.jsx(
          "input",
          {
            ref: fileInputRef,
            type: "file",
            accept: "image/*",
            multiple,
            onChange: handleFileSelect,
            className: "hidden"
          }
        ),
        /* @__PURE__ */ jsxRuntimeExports.jsxs(
          Button,
          {
            type: "button",
            onClick: selectedFiles.length > 0 ? handleUpload : () => {
              var _a;
              return (_a = fileInputRef.current) == null ? void 0 : _a.click();
            },
            disabled: uploading,
            className: `flex items-center gap-2 ${uploading ? "bg-gray-400 cursor-not-allowed" : "bg-blue-600 hover:bg-blue-700"} text-white px-5 py-2 rounded-lg shadow-md transition-all duration-300 hover:shadow-lg`,
            children: [
              uploading ? /* @__PURE__ */ jsxRuntimeExports.jsx(LoaderCircle, { className: "h-5 w-5 animate-spin" }) : /* @__PURE__ */ jsxRuntimeExports.jsx(Upload, { className: "h-5 w-5" }),
              uploading ? "Uploading..." : selectedFiles.length > 0 ? "Upload" : "Add Image"
            ]
          }
        )
      ] }),
      uploadError && /* @__PURE__ */ jsxRuntimeExports.jsx("p", { className: "text-red-500 mt-2 text-sm font-medium animate-pulse", children: uploadError })
    ] })
  ] });
};
function NetWorkConfig({
  networkConfig,
  networkName,
  onAttributeUpdate
}) {
  var _a, _b, _c, _d, _e, _f, _g, _h, _i, _j, _k, _l, _m, _n, _o, _p;
  const [businessConfigEditable, setBusinessConfigEditable] = reactExports.useState(false);
  const [editingStartPage, setEditingStartPage] = reactExports.useState(false);
  const [pendingConfig, setPendingConfig] = reactExports.useState({});
  const keyMapping = {
    id: "id",
    domain: "domain",
    businessLogo: "business_logo",
    homePageBanner: "home_page_banner",
    pwaAppIcon: "pwa_app_icon",
    footerAppIcon: "footer_app_icon",
    networkId: "networkId",
    multiSeller: "multi_seller",
    defaultSellerId: "defaultSellerId",
    wabEnabled: "wab_enabled",
    wabMobileNumber: "wab_mobile_number",
    defaultStartPage: "default_start_page",
    imageBaseUrl: "imageBaseUrl",
    networkType: "",
    wabDatasetId: "wabDatasetId"
  };
  const handleBusinessConfig = reactExports.useCallback(() => {
    setBusinessConfigEditable((prevState) => {
      if (prevState) {
        setPendingConfig({});
        setVisibleSaveButtons({});
      }
      return !prevState;
    });
  }, []);
  reactExports.useEffect(() => {
    setConfig(networkConfig);
  }, [networkConfig]);
  const [BasicConfig, setBasicConfig] = reactExports.useState(false);
  const BasicConFigEdit = reactExports.useCallback(() => {
    setBasicConfig((prev) => {
      if (prev) {
        setPendingConfig({});
        setVisibleSaveButtons({});
      }
      return !prev;
    });
  }, []);
  const [visibleSaveButtons, setVisibleSaveButtons] = reactExports.useState({});
  const [config, setConfig] = reactExports.useState(networkConfig);
  const [showsave, setShowsave] = reactExports.useState(false);
  const handleConfigChange = (key, value) => {
    console.log(value, key, "Config Change Triggered");
    setPendingConfig((prev) => {
      let newValue = value;
      if (typeof value === "string" && (value === "yes" || value === "no")) {
        newValue = value === "yes";
      } else if (typeof value === "string" && !isNaN(Number(value))) {
        newValue = Number(value);
      } else if (value === null || value === void 0) {
        newValue = "";
      }
      return {
        ...prev,
        [key]: newValue
      };
    });
    setVisibleSaveButtons((prev) => ({
      ...prev,
      [key]: true
    }));
  };
  const fetcher = useFetcher();
  const handleSave = async (key, value) => {
    var _a2;
    try {
      setConfig((prevConfig) => ({
        ...prevConfig,
        [key]: value !== void 0 ? value : pendingConfig[key] ?? ""
      }));
      setVisibleSaveButtons((prev) => ({
        ...prev,
        [key]: false
      }));
      setShowsave(false);
      let hasError = false;
      const newValue = value !== void 0 ? value : pendingConfig[key] ?? "";
      try {
        await onAttributeUpdate(keyMapping[key], typeof newValue === "string" ? encodeURIComponent(newValue) : newValue, ((_a2 = networkConfig[0]) == null ? void 0 : _a2.id) ?? 0);
      } catch (error) {
        console.error(`Failed to update ${key}:`, error);
        hasError = true;
      }
      if (hasError) {
        alert("Some settings could not be saved. Please try again.");
      } else {
        setPendingConfig({});
      }
    } catch (error) {
      console.error("Error saving config:", error);
      alert("Failed to save settings. Please try again later.");
    }
  };
  const handleImageUpload = (key, url) => {
    setPendingConfig((prev) => ({
      ...prev,
      [key]: url
    }));
    setVisibleSaveButtons((prev) => ({
      ...prev,
      [key]: true
    }));
  };
  const [editingFooter, setEditingFooter] = reactExports.useState(false);
  const [editingBanners, setEditingBanners] = reactExports.useState(false);
  const [showSaveFooter, setShowSaveFooter] = reactExports.useState(false);
  const [showSaveBanners, setShowSaveBanners] = reactExports.useState(false);
  const [editingPlaystore, setEditingPlaystore] = reactExports.useState(false);
  const [showSavePlaystore, setShowSavePlaystore] = reactExports.useState(false);
  const [editingBusinessLogo, setEditingBusinessLogo] = reactExports.useState(false);
  const [showSaveBusinessLogo, setShowSaveBusinessLogo] = reactExports.useState(false);
  const Loading = fetcher.state !== "idle";
  return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
    className: "flex flex-col gap-4 border bg-white rounded-xl border-neutral-200 my-4 p-4",
    children: [Loading && /* @__PURE__ */ jsxRuntimeExports.jsx(SpinnerLoader, {
      loading: Loading
    }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
      className: "flex flex-col gap-4 border rounded-xl border-neutral-400 p-4 shadow",
      children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
        className: "flex flex-row justify-between",
        children: [/* @__PURE__ */ jsxRuntimeExports.jsx("div", {
          className: "text-lg font-semibold text-typography-700",
          children: "Basic Configurations"
        }), /* @__PURE__ */ jsxRuntimeExports.jsx("button", {
          className: "inline-flex items-center gap-1 text-blue-600 hover:text-blue-800",
          onClick: () => BasicConFigEdit(),
          children: BasicConfig ? /* @__PURE__ */ jsxRuntimeExports.jsxs(jsxRuntimeExports.Fragment, {
            children: [/* @__PURE__ */ jsxRuntimeExports.jsx(X, {
              className: "h-4 w-4"
            }), "Cancel"]
          }) : /* @__PURE__ */ jsxRuntimeExports.jsxs(jsxRuntimeExports.Fragment, {
            children: [/* @__PURE__ */ jsxRuntimeExports.jsx(SquarePen, {
              className: "h-4 w-4"
            }), "Edit"]
          })
        })]
      }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
        className: "flex gap-2",
        children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
          className: "p-2 border border-neutral-50 rounded-sm",
          children: [!editingBusinessLogo ? ((_a = networkConfig[0]) == null ? void 0 : _a.businessLogo) ? /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
            className: "flex flex-row gap-1 items-center",
            children: [/* @__PURE__ */ jsxRuntimeExports.jsx("img", {
              src: (_b = networkConfig[0]) == null ? void 0 : _b.businessLogo,
              className: "w-32 rounded-md border border-neutral-200 transition-transform hover:scale-105",
              alt: "Business Logo"
            }), /* @__PURE__ */ jsxRuntimeExports.jsx("button", {
              onClick: () => setEditingBusinessLogo(true),
              children: /* @__PURE__ */ jsxRuntimeExports.jsx(Pencil, {
                className: "h-5 w-5 text-blue-600 hover:text-blue-800"
              })
            }), /* @__PURE__ */ jsxRuntimeExports.jsx("button", {
              onClick: () => {
                handleSave("businessLogo", '""');
              },
              className: "text-red-500 hover:text-red-700",
              children: /* @__PURE__ */ jsxRuntimeExports.jsx(Trash, {
                className: "h-5 w-5"
              })
            })]
          }) : /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
            className: "text-gray-500 flex items-center gap-2",
            children: ["No Business Logo", /* @__PURE__ */ jsxRuntimeExports.jsx("button", {
              onClick: () => setEditingBusinessLogo(true),
              children: /* @__PURE__ */ jsxRuntimeExports.jsx(Pencil, {
                className: "h-5 w-5 text-blue-600 hover:text-blue-800"
              })
            })]
          }) : /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
            className: "flex flex-col gap-4 bg-gray-50 p-4 rounded-lg shadow-sm",
            children: [/* @__PURE__ */ jsxRuntimeExports.jsx("div", {
              className: "bg-white border border-dashed border-neutral-300 rounded-md p-4",
              children: /* @__PURE__ */ jsxRuntimeExports.jsx(ImageUploadComponent, {
                onChange: (url) => {
                  setShowSaveBusinessLogo(true);
                  const businessLogoUrl = Array.isArray(url) ? url[0] : url;
                  handleImageUpload("businessLogo", businessLogoUrl);
                  setEditingBusinessLogo(false);
                }
              })
            }), /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
              className: "flex flex-row items-center justify-center gap-4",
              children: /* @__PURE__ */ jsxRuntimeExports.jsxs(Button, {
                className: "bg-gray-500 hover:bg-gray-600 text-white flex items-center gap-2 px-4 py-2 rounded-md transition-all duration-200 shadow-sm",
                onClick: () => {
                  setEditingBusinessLogo(false);
                  setShowSaveBusinessLogo(false);
                  setPendingConfig((prev) => ({
                    ...prev,
                    businessLogo: void 0
                  }));
                  setVisibleSaveButtons((prev) => ({
                    ...prev,
                    businessLogo: false
                  }));
                },
                children: [/* @__PURE__ */ jsxRuntimeExports.jsx(X, {
                  className: "h-5 w-5"
                }), " Cancel"]
              })
            })]
          }), showSaveBusinessLogo && /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
            className: "flex flex-row items-center justify-center mt-2",
            children: /* @__PURE__ */ jsxRuntimeExports.jsxs(Button, {
              className: "bg-blue-600 hover:bg-blue-700 text-white flex items-center gap-2 px-4 py-2 rounded-md transition-all duration-200 shadow-sm",
              onClick: () => {
                handleSave("businessLogo");
                setEditingBusinessLogo(false);
                setShowSaveBusinessLogo(false);
              },
              children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Save, {
                className: "h-5 w-5"
              }), " Save"]
            })
          })]
        }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
          className: "flex flex-col gap-0",
          children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
            className: "text-sm text-typography-300",
            children: ["NetWork Id : ", (_c = networkConfig[0]) == null ? void 0 : _c.networkId]
          }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
            className: "text-md text-typography-600",
            children: ["NetWork Name : ", networkName]
          })]
        })]
      }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
        className: "flex flex-col gap-0",
        children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
          className: "text-sm text-typography-300",
          children: ["Domain ID : ", (_d = networkConfig[0]) == null ? void 0 : _d.id]
        }), /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
          className: "flex flex-col gap-2 text-md text-typography-400",
          children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
            className: "flex flex-row gap-4 items-center",
            children: [/* @__PURE__ */ jsxRuntimeExports.jsx("div", {
              className: "text-md text-typography-500 w-sm",
              children: "Domain URL :"
            }), /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
              className: "font-semibold text-typography-800 flex gap-2 items-center",
              children: /* @__PURE__ */ jsxRuntimeExports.jsx("input", {
                type: "text",
                value: (pendingConfig.domain ?? ((_e = networkConfig[0]) == null ? void 0 : _e.domain)) || "",
                onChange: (e) => handleConfigChange("domain", e.target.value),
                className: "border border-neutral-400 rounded-md p-1 px-2",
                disabled: !BasicConfig
              })
            }), BasicConfig && visibleSaveButtons["domain"] && /* @__PURE__ */ jsxRuntimeExports.jsx("button", {
              className: "flex flex-row text-blue-500 items-center gap-1 cursor-pointer text-lg font-bold",
              onClick: () => handleSave("domain"),
              children: /* @__PURE__ */ jsxRuntimeExports.jsx(Save, {
                className: "h-5 w-5"
              })
            })]
          })
        })]
      })]
    }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
      className: "flex flex-col gap-4 border rounded-xl border-neutral-400 p-4 shadow",
      children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
        className: "flex w-full justify-between",
        children: [/* @__PURE__ */ jsxRuntimeExports.jsx("div", {
          className: "abstractart-typography-700",
          children: "Business Configurations"
        }), /* @__PURE__ */ jsxRuntimeExports.jsx("button", {
          className: "inline-flex items-center gap-1 text-blue-600 hover:text-blue-800",
          onClick: () => handleBusinessConfig(),
          children: businessConfigEditable ? /* @__PURE__ */ jsxRuntimeExports.jsxs(jsxRuntimeExports.Fragment, {
            children: [/* @__PURE__ */ jsxRuntimeExports.jsx(X, {
              className: "h-4 w-4"
            }), "Cancel"]
          }) : /* @__PURE__ */ jsxRuntimeExports.jsxs(jsxRuntimeExports.Fragment, {
            children: [/* @__PURE__ */ jsxRuntimeExports.jsx(SquarePen, {
              className: "h-4 w-4"
            }), "Edit"]
          })
        })]
      }), [{
        label: "Are Multiple Sellers Allowed?  :",
        key: "multiSeller"
      }, {
        label: "Is WhatsApp Enabled?  :",
        key: "wabEnabled"
      }].map(({
        label,
        key
      }) => {
        var _a2;
        return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
          className: "flex gap-4 items-center text-md text-typography-400",
          children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Label, {
            className: "w-[400px] text-md text-typography-400",
            children: label
          }), /* @__PURE__ */ jsxRuntimeExports.jsxs(RadioGroup, {
            value: pendingConfig[key] ?? ((_a2 = config[0]) == null ? void 0 : _a2[key]) ? "yes" : "no",
            onValueChange: (val) => handleConfigChange(key, val),
            className: "flex gap-4 items-center text-md font-semibold text-typography-800",
            disabled: !businessConfigEditable,
            children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
              className: "flex items-center gap-2",
              children: [/* @__PURE__ */ jsxRuntimeExports.jsx(RadioGroupItem, {
                id: `${key}-yes`,
                value: "yes"
              }), /* @__PURE__ */ jsxRuntimeExports.jsx(Label, {
                htmlFor: `${key}-yes`,
                children: "Yes"
              })]
            }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
              className: "flex items-center gap-2",
              children: [/* @__PURE__ */ jsxRuntimeExports.jsx(RadioGroupItem, {
                id: `${key}-no`,
                value: "no"
              }), /* @__PURE__ */ jsxRuntimeExports.jsx(Label, {
                htmlFor: `${key}-no`,
                children: "No"
              })]
            })]
          }), businessConfigEditable && visibleSaveButtons[key] && /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
            className: "flex items-center gap-2",
            children: /* @__PURE__ */ jsxRuntimeExports.jsx("button", {
              className: "flex flex-row text-blue-500 items-center gap-1 cursor-pointer text-lg font-bold",
              onClick: () => handleSave(key),
              children: /* @__PURE__ */ jsxRuntimeExports.jsx(Save, {
                className: "h-5 w-5"
              })
            })
          })]
        }, key);
      }), /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
        className: "flex flex-col gap-2 text-md text-typography-400",
        children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
          className: "flex gap-4 items-center",
          children: [/* @__PURE__ */ jsxRuntimeExports.jsx("div", {
            className: "text-md text-typography-500 w-[400px]",
            children: "WhatsApp Contact Number"
          }), /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
            className: "font-semibold text-typography-800 flex gap-2 items-center",
            children: /* @__PURE__ */ jsxRuntimeExports.jsx("input", {
              type: "tel",
              value: pendingConfig.wabMobileNumber ?? ((_f = config[0]) == null ? void 0 : _f.wabMobileNumber) ?? "",
              onChange: (e) => {
                const value = e.target.value;
                if (/^\d{0,10}$/.test(value)) {
                  handleConfigChange("wabMobileNumber", value);
                }
              },
              className: "border border-neutral-400 rounded-md p-1 px-2",
              disabled: !businessConfigEditable,
              maxLength: 10,
              placeholder: "Enter 10-digit number",
              pattern: "[0-9]{10}",
              onKeyDown: (e) => {
                if (!/[\d]/.test(e.key) && !["Backspace", "ArrowLeft", "ArrowRight", "Delete", "Tab"].includes(e.key)) {
                  e.preventDefault();
                }
              }
            })
          }), businessConfigEditable && visibleSaveButtons["wabMobileNumber"] && /* @__PURE__ */ jsxRuntimeExports.jsx("button", {
            className: "flex flex-row text-blue-500 items-center gap-1 cursor-pointer text-lg font-bold",
            onClick: () => {
              var _a2;
              const value = pendingConfig.wabMobileNumber ?? ((_a2 = config[0]) == null ? void 0 : _a2.wabMobileNumber) ?? "";
              if (/^\d{10}$/.test(value)) {
                handleSave("wabMobileNumber");
              } else {
                alert("Please enter a valid 10-digit WhatsApp number.");
              }
            },
            children: /* @__PURE__ */ jsxRuntimeExports.jsx(Save, {
              className: "h-5 w-5"
            })
          })]
        })
      }), /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
        className: "flex flex-col gap-2 text-md text-typography-400",
        children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
          className: "flex gap-4 items-center",
          children: [/* @__PURE__ */ jsxRuntimeExports.jsx("div", {
            className: "text-md text-typography-500 w-[400px]",
            children: "WhatsApp Pixel Id"
          }), /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
            className: "font-semibold text-typography-800 flex gap-2 items-center",
            children: /* @__PURE__ */ jsxRuntimeExports.jsx("input", {
              value: pendingConfig.wabDatasetId ?? ((_g = config[0]) == null ? void 0 : _g.wabDatasetId) ?? "",
              onChange: (e) => {
                const value = e.target.value;
                handleConfigChange("wabDatasetId", value);
              },
              className: "border border-neutral-400 rounded-md p-1 px-2",
              disabled: !businessConfigEditable,
              placeholder: "Enter whatsapp pixel id",
              onKeyDown: (e) => {
                if (!/[\d]/.test(e.key) && !["Backspace", "ArrowLeft", "ArrowRight", "Delete", "Tab"].includes(e.key)) {
                  e.preventDefault();
                }
              }
            })
          }), businessConfigEditable && visibleSaveButtons["wabDatasetId"] && /* @__PURE__ */ jsxRuntimeExports.jsx("button", {
            className: "flex flex-row text-blue-500 items-center gap-1 cursor-pointer text-lg font-bold",
            onClick: () => {
              var _a2;
              pendingConfig.wabDatasetId ?? ((_a2 = config[0]) == null ? void 0 : _a2.wabDatasetId) ?? "";
              handleSave("wabDatasetId");
            },
            children: /* @__PURE__ */ jsxRuntimeExports.jsx(Save, {
              className: "h-5 w-5"
            })
          })]
        })
      })]
    }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
      className: "flex flex-col gap-4 border rounded-xl border-neutral-400 p-4 shadow",
      children: [/* @__PURE__ */ jsxRuntimeExports.jsx("div", {
        className: "flex flex-row justify-between",
        children: /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
          className: "text-lg font-semibold text-typography-700",
          children: "App Configurations"
        })
      }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
        className: "text-md text-typography-600 flex gap-4",
        children: ["Home Page:", !editingStartPage ? /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
          className: "font-semibold text-typography-800",
          children: ((_h = networkConfig[0]) == null ? void 0 : _h.defaultStartPage) === "chooseitems" ? "Item SRP Page" : "Trips Page"
        }) : /* @__PURE__ */ jsxRuntimeExports.jsxs("select", {
          value: (pendingConfig.defaultStartPage ?? ((_i = networkConfig[0]) == null ? void 0 : _i.defaultStartPage)) || "chooseitems",
          onChange: (e) => handleConfigChange("defaultStartPage", e.target.value),
          className: "border border-gray-300 rounded-md px-2 py-1",
          children: [/* @__PURE__ */ jsxRuntimeExports.jsx("option", {
            value: "chooseitems",
            children: "Item SRP Page"
          }), /* @__PURE__ */ jsxRuntimeExports.jsx("option", {
            value: "home",
            children: "Trips Page"
          })]
        }), /* @__PURE__ */ jsxRuntimeExports.jsx("button", {
          onClick: () => setEditingStartPage(!editingStartPage),
          children: !editingStartPage ? /* @__PURE__ */ jsxRuntimeExports.jsx(Pencil, {
            className: "h-5 w-5 text-blue-600 hover:text-blue-800"
          }) : /* @__PURE__ */ jsxRuntimeExports.jsx(X, {
            className: "h-5 w-5 text-gray-600 hover:text-gray-800"
          })
        }), visibleSaveButtons.defaultStartPage && /* @__PURE__ */ jsxRuntimeExports.jsxs(Button, {
          className: "bg-blue-600 hover:bg-blue-700 text-white flex items-center gap-2 px-4 py-2 rounded-md transition-all duration-200 shadow-sm",
          onClick: () => {
            handleSave("defaultStartPage");
            setEditingStartPage(false);
          },
          children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Save, {
            className: "h-5 w-5"
          }), " Save"]
        })]
      }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
        className: "text-md text-typography-600 flex flex-row gap-2",
        children: [/* @__PURE__ */ jsxRuntimeExports.jsx("div", {
          className: "flex items-center gap-2",
          children: "LoginImage:"
        }), !editingBanners ? ((_j = networkConfig[0]) == null ? void 0 : _j.homePageBanner) ? (_l = (_k = networkConfig[0]) == null ? void 0 : _k.homePageBanner) == null ? void 0 : _l.split(",").map((banner, index) => /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
          className: "flex gap-4 items-center",
          children: [/* @__PURE__ */ jsxRuntimeExports.jsx("div", {
            children: index + 1
          }), /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
            className: "p-2 border border-neutral-50 rounded-md shadow-sm",
            children: /* @__PURE__ */ jsxRuntimeExports.jsx("img", {
              src: banner,
              className: "h-32 rounded-md border border-neutral-200 transition-transform hover:scale-105",
              alt: `Banner ${index + 1}`
            })
          }), /* @__PURE__ */ jsxRuntimeExports.jsx("button", {
            onClick: () => setEditingBanners(true),
            children: /* @__PURE__ */ jsxRuntimeExports.jsx(Pencil, {
              className: "h-5 w-5 text-blue-600 hover:text-blue-800"
            })
          }), /* @__PURE__ */ jsxRuntimeExports.jsx("button", {
            onClick: () => {
              handleSave("homePageBanner", '""');
            },
            className: "text-red-500 hover:text-red-700",
            children: /* @__PURE__ */ jsxRuntimeExports.jsx(Trash, {
              className: "h-5 w-5"
            })
          })]
        }, index)) : /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
          className: "flex flex-row gap-2",
          children: [/* @__PURE__ */ jsxRuntimeExports.jsx("div", {
            className: "text-md text-typography-200",
            children: "No Banner Active"
          }), /* @__PURE__ */ jsxRuntimeExports.jsx("button", {
            onClick: () => setEditingBanners(true),
            children: /* @__PURE__ */ jsxRuntimeExports.jsx(Pencil, {
              className: "h-5 w-5 text-blue-600 hover:text-blue-800"
            })
          })]
        }) : /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
          className: "flex flex-col gap-4 bg-gray-50 p-4 rounded-lg shadow-sm",
          children: [/* @__PURE__ */ jsxRuntimeExports.jsx("div", {
            className: "bg-white border border-dashed border-neutral-300 rounded-md p-4",
            children: /* @__PURE__ */ jsxRuntimeExports.jsx(ImageUploadComponent, {
              multiple: false,
              onChange: (urls) => {
                setShowSaveBanners(true);
                const bannerUrls = Array.isArray(urls) ? urls.join(",") : urls;
                handleImageUpload("homePageBanner", bannerUrls);
                setEditingBanners(false);
              }
            })
          }), /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
            className: "flex flex-row justify-center items-center gap-4",
            children: /* @__PURE__ */ jsxRuntimeExports.jsxs(Button, {
              className: "bg-gray-500 hover:bg-gray-600 text-white flex items-center gap-2 px-4 py-2 rounded-md transition-all duration-200 shadow-sm",
              onClick: () => {
                setEditingBanners(false);
                setShowSaveBanners(false);
                setPendingConfig((prev) => ({
                  ...prev,
                  homePageBanner: void 0
                }));
                setVisibleSaveButtons((prev) => ({
                  ...prev,
                  homePageBanner: false
                }));
              },
              children: [/* @__PURE__ */ jsxRuntimeExports.jsx(X, {
                className: "h-5 w-5"
              }), " Cancel"]
            })
          })]
        }), showSaveBanners && /* @__PURE__ */ jsxRuntimeExports.jsxs(Button, {
          className: "bg-blue-600 hover:bg-blue-700 text-white flex items-center gap-2 px-4 py-2 rounded-md transition-all duration-200 shadow-sm",
          onClick: () => {
            handleSave("homePageBanner");
            setEditingBanners(false);
            setShowSaveBanners(false);
          },
          children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Save, {
            className: "h-5 w-5"
          }), " Save"]
        })]
      }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
        className: "text-md text-typography-600 flex gap-4",
        children: [/* @__PURE__ */ jsxRuntimeExports.jsx("div", {
          children: "Playstore App Icon :"
        }), /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
          className: "p-2 border border-neutral-50 rounded-md shadow-sm",
          children: !editingPlaystore ? ((_m = networkConfig[0]) == null ? void 0 : _m.pwaAppIcon) ? /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
            className: "flex flex-row gap-1 items-center",
            children: [/* @__PURE__ */ jsxRuntimeExports.jsx("img", {
              src: (_n = networkConfig[0]) == null ? void 0 : _n.pwaAppIcon,
              className: "h-32 rounded-md border border-neutral-200 transition-transform hover:scale-105",
              alt: "Playstore Icon"
            }), /* @__PURE__ */ jsxRuntimeExports.jsx("button", {
              onClick: () => setEditingPlaystore(true),
              children: /* @__PURE__ */ jsxRuntimeExports.jsx(Pencil, {
                className: "h-5 w-5 text-blue-600 hover:text-blue-800"
              })
            }), /* @__PURE__ */ jsxRuntimeExports.jsx("button", {
              onClick: () => {
                handleSave("pwaAppIcon", '""');
              },
              className: "text-red-500 hover:text-red-700",
              children: /* @__PURE__ */ jsxRuntimeExports.jsx(Trash, {
                className: "h-5 w-5"
              })
            })]
          }) : /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
            className: "text-gray-500 flex items-center gap-2",
            children: ["No Playstore Icon", /* @__PURE__ */ jsxRuntimeExports.jsx("button", {
              onClick: () => setEditingPlaystore(true),
              children: /* @__PURE__ */ jsxRuntimeExports.jsx(Pencil, {
                className: "h-5 w-5 text-blue-600 hover:text-blue-800"
              })
            })]
          }) : /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
            className: "flex flex-col gap-4 bg-gray-50 p-4 rounded-lg shadow-sm",
            children: [/* @__PURE__ */ jsxRuntimeExports.jsx("div", {
              className: "bg-white border border-dashed border-neutral-300 rounded-md p-4",
              children: /* @__PURE__ */ jsxRuntimeExports.jsx(ImageUploadComponent, {
                onChange: (url) => {
                  setShowSavePlaystore(true);
                  const playstoreUrl = Array.isArray(url) ? url[0] : url;
                  handleImageUpload("pwaAppIcon", playstoreUrl);
                  setEditingPlaystore(false);
                }
              })
            }), /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
              className: "flex flex-row justify-center items-center gap-4",
              children: /* @__PURE__ */ jsxRuntimeExports.jsxs(Button, {
                className: "bg-gray-500 hover:bg-gray-600 text-white flex items-center gap-2 px-4 py-2 rounded-md transition-all duration-200 shadow-sm",
                onClick: () => {
                  setEditingPlaystore(false);
                  setShowSavePlaystore(false);
                  setPendingConfig((prev) => ({
                    ...prev,
                    pwaAppIcon: void 0
                  }));
                  setVisibleSaveButtons((prev) => ({
                    ...prev,
                    pwaAppIcon: false
                  }));
                },
                children: [/* @__PURE__ */ jsxRuntimeExports.jsx(X, {
                  className: "h-5 w-5"
                }), " Cancel"]
              })
            })]
          })
        }), showSavePlaystore && /* @__PURE__ */ jsxRuntimeExports.jsxs(Button, {
          className: "bg-blue-600 hover:bg-blue-700 text-white flex items-center gap-2 px-4 py-2 rounded-md transition-all duration-200 shadow-sm",
          onClick: () => {
            handleSave("pwaAppIcon");
            setEditingPlaystore(false);
            setShowSavePlaystore(false);
          },
          children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Save, {
            className: "h-5 w-5"
          }), " Save"]
        })]
      }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
        className: "text-md text-typography-600 flex gap-4",
        children: [/* @__PURE__ */ jsxRuntimeExports.jsx("div", {
          children: "In-App Footer Logo :"
        }), /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
          className: "p-2 border border-neutral-50 rounded-md shadow-sm",
          children: !editingFooter ? ((_o = networkConfig[0]) == null ? void 0 : _o.footerAppIcon) ? /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
            className: "flex flex-row gap-1 items-center",
            children: [/* @__PURE__ */ jsxRuntimeExports.jsx("img", {
              src: (_p = networkConfig[0]) == null ? void 0 : _p.footerAppIcon,
              className: "h-32 rounded-md border border-neutral-200 transition-transform hover:scale-105",
              alt: "Footer Logo"
            }), /* @__PURE__ */ jsxRuntimeExports.jsx("button", {
              onClick: () => setEditingFooter(true),
              children: /* @__PURE__ */ jsxRuntimeExports.jsx(Pencil, {
                className: "h-5 w-5 text-blue-600 hover:text-blue-800"
              })
            }), /* @__PURE__ */ jsxRuntimeExports.jsx("button", {
              onClick: () => {
                handleSave("footerAppIcon", '""');
              },
              className: "text-red-500 hover:text-red-700",
              children: /* @__PURE__ */ jsxRuntimeExports.jsx(Trash, {
                className: "h-5 w-5"
              })
            })]
          }) : /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
            className: "text-gray-500 flex items-center gap-2",
            children: ["No Footer Logo", /* @__PURE__ */ jsxRuntimeExports.jsx("button", {
              onClick: () => setEditingFooter(true),
              children: /* @__PURE__ */ jsxRuntimeExports.jsx(Pencil, {
                className: "h-5 w-5 text-blue-600 hover:text-blue-800"
              })
            })]
          }) : /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
            className: "flex flex-col gap-4 bg-gray-50 p-4 rounded-lg shadow-sm",
            children: [/* @__PURE__ */ jsxRuntimeExports.jsx("div", {
              className: "bg-white border border-dashed border-neutral-300 rounded-md p-4",
              children: /* @__PURE__ */ jsxRuntimeExports.jsx(ImageUploadComponent, {
                onChange: (url) => {
                  setShowSaveFooter(true);
                  const footerUrl = Array.isArray(url) ? url[0] : url;
                  handleImageUpload("footerAppIcon", footerUrl);
                  setEditingFooter(false);
                }
              })
            }), /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
              className: "flex flex-row justify-center items-center gap-4",
              children: /* @__PURE__ */ jsxRuntimeExports.jsxs(Button, {
                className: "bg-gray-500 hover:bg-gray-600 text-white flex items-center gap-2 px-4 py-2 rounded-md transition-all duration-200 shadow-sm",
                onClick: () => {
                  setEditingFooter(false);
                  setShowSaveFooter(false);
                  setPendingConfig((prev) => ({
                    ...prev,
                    footerAppIcon: void 0
                  }));
                  setVisibleSaveButtons((prev) => ({
                    ...prev,
                    footerAppIcon: false
                  }));
                },
                children: [/* @__PURE__ */ jsxRuntimeExports.jsx(X, {
                  className: "h-5 w-5"
                }), " Cancel"]
              })
            })]
          })
        }), showSaveFooter && /* @__PURE__ */ jsxRuntimeExports.jsxs(Button, {
          className: "bg-blue-600 hover:bg-blue-700 text-white flex items-center gap-2 px-4 py-2 rounded-md transition-all duration-200 shadow-sm",
          onClick: () => {
            handleSave("footerAppIcon");
            setEditingFooter(false);
            setShowSaveFooter(false);
          },
          children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Save, {
            className: "h-5 w-5"
          }), " Save"]
        })]
      })]
    })]
  });
}
export {
  NetWorkConfig as default
};
//# sourceMappingURL=netWorkConfig-DCee6agm.js.map
