{"version": 3, "file": "pagination-DzgbTb6G.js", "sources": ["../../../node_modules/lucide-react/dist/esm/icons/ellipsis.js", "../../../app/components/ui/pagination.tsx"], "sourcesContent": ["/**\n * @license lucide-react v0.453.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst Ellipsis = createLucideIcon(\"Ellipsis\", [\n  [\"circle\", { cx: \"12\", cy: \"12\", r: \"1\", key: \"41hilf\" }],\n  [\"circle\", { cx: \"19\", cy: \"12\", r: \"1\", key: \"1wjl8i\" }],\n  [\"circle\", { cx: \"5\", cy: \"12\", r: \"1\", key: \"1pcz8c\" }]\n]);\n\nexport { Ellipsis as default };\n//# sourceMappingURL=ellipsis.js.map\n", "import * as React from \"react\"\r\nimport { ChevronLeft, ChevronRight, MoreHorizontal } from \"lucide-react\"\r\n\r\nimport { cn } from \"~/lib/utils\"\r\nimport { ButtonProps, buttonVariants } from \"~/components/ui/button\"\r\n\r\nconst Pagination = ({ className, ...props }: React.ComponentProps<\"nav\">) => (\r\n  <nav\r\n    role=\"navigation\"\r\n    aria-label=\"pagination\"\r\n    className={cn(\"mx-auto flex w-full justify-center\", className)}\r\n    {...props}\r\n  />\r\n)\r\nPagination.displayName = \"Pagination\"\r\n\r\nconst PaginationContent = React.forwardRef<\r\n  HTMLUListElement,\r\n  React.ComponentProps<\"ul\">\r\n>(({ className, ...props }, ref) => (\r\n  <ul\r\n    ref={ref}\r\n    className={cn(\"flex flex-row items-center gap-1\", className)}\r\n    {...props}\r\n  />\r\n))\r\nPaginationContent.displayName = \"PaginationContent\"\r\n\r\nconst PaginationItem = React.forwardRef<\r\n  HTMLLIElement,\r\n  React.ComponentProps<\"li\">\r\n>(({ className, ...props }, ref) => (\r\n  <li ref={ref} className={cn(\"\", className)} {...props} />\r\n))\r\nPaginationItem.displayName = \"PaginationItem\"\r\n\r\ntype PaginationLinkProps = {\r\n  isActive?: boolean\r\n} & Pick<ButtonProps, \"size\"> &\r\n  React.ComponentProps<\"a\">\r\n\r\nconst PaginationLink = ({\r\n  className,\r\n  isActive,\r\n  size = \"icon\",\r\n  ...props\r\n}: PaginationLinkProps) => (\r\n  <a\r\n    aria-current={isActive ? \"page\" : undefined}\r\n    className={cn(\r\n      buttonVariants({\r\n        variant: isActive ? \"outline\" : \"ghost\",\r\n        size,\r\n      }),\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n)\r\nPaginationLink.displayName = \"PaginationLink\"\r\n\r\nconst PaginationPrevious = ({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof PaginationLink>) => (\r\n  <PaginationLink\r\n    aria-label=\"Go to previous page\"\r\n    size=\"default\"\r\n    className={cn(\"gap-1 pl-2.5\", className)}\r\n    {...props}\r\n  >\r\n    <ChevronLeft className=\"h-4 w-4\" />\r\n    <span>Previous</span>\r\n  </PaginationLink>\r\n)\r\nPaginationPrevious.displayName = \"PaginationPrevious\"\r\n\r\nconst PaginationNext = ({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof PaginationLink>) => (\r\n  <PaginationLink\r\n    aria-label=\"Go to next page\"\r\n    size=\"default\"\r\n    className={cn(\"gap-1 pr-2.5\", className)}\r\n    {...props}\r\n  >\r\n    <span>Next</span>\r\n    <ChevronRight className=\"h-4 w-4\" />\r\n  </PaginationLink>\r\n)\r\nPaginationNext.displayName = \"PaginationNext\"\r\n\r\nconst PaginationEllipsis = ({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"span\">) => (\r\n  <span\r\n    aria-hidden\r\n    className={cn(\"flex h-9 w-9 items-center justify-center\", className)}\r\n    {...props}\r\n  >\r\n    <MoreHorizontal className=\"h-4 w-4\" />\r\n    <span className=\"sr-only\">More pages</span>\r\n  </span>\r\n)\r\nPaginationEllipsis.displayName = \"PaginationEllipsis\"\r\n\r\nexport {\r\n  Pagination,\r\n  PaginationContent,\r\n  PaginationEllipsis,\r\n  PaginationItem,\r\n  PaginationLink,\r\n  PaginationNext,\r\n  PaginationPrevious,\r\n}\r\n"], "names": ["jsx", "React.forwardRef", "jsxs", "MoreHorizontal"], "mappings": ";;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA,MAAM,WAAW,iBAAiB,YAAY;AAAA,EAC5C,CAAC,UAAU,EAAE,IAAI,MAAM,IAAI,MAAM,GAAG,KAAK,KAAK,UAAU;AAAA,EACxD,CAAC,UAAU,EAAE,IAAI,MAAM,IAAI,MAAM,GAAG,KAAK,KAAK,UAAU;AAAA,EACxD,CAAC,UAAU,EAAE,IAAI,KAAK,IAAI,MAAM,GAAG,KAAK,KAAK,SAAU,CAAA;AACzD,CAAC;ACPD,MAAM,aAAa,CAAC,EAAE,WAAW,GAAG,MAClC,MAAAA,kCAAA;AAAA,EAAC;AAAA,EAAA;AAAA,IACC,MAAK;AAAA,IACL,cAAW;AAAA,IACX,WAAW,GAAG,sCAAsC,SAAS;AAAA,IAC5D,GAAG;AAAA,EAAA;AACN;AAEF,WAAW,cAAc;AAEnB,MAAA,oBAAoBC,aAGxB,WAAA,CAAC,EAAE,WAAW,GAAG,MAAM,GAAG,QAC1BD,kCAAA;AAAA,EAAC;AAAA,EAAA;AAAA,IACC;AAAA,IACA,WAAW,GAAG,oCAAoC,SAAS;AAAA,IAC1D,GAAG;AAAA,EAAA;AACN,CACD;AACD,kBAAkB,cAAc;AAE1B,MAAA,iBAAiBC,aAGrB,WAAA,CAAC,EAAE,WAAW,GAAG,SAAS,8CACzB,MAAG,EAAA,KAAU,WAAW,GAAG,IAAI,SAAS,GAAI,GAAG,OAAO,CACxD;AACD,eAAe,cAAc;AAO7B,MAAM,iBAAiB,CAAC;AAAA,EACtB;AAAA,EACA;AAAA,EACA,OAAO;AAAA,EACP,GAAG;AACL,MACED,kCAAA;AAAA,EAAC;AAAA,EAAA;AAAA,IACC,gBAAc,WAAW,SAAS;AAAA,IAClC,WAAW;AAAA,MACT,eAAe;AAAA,QACb,SAAS,WAAW,YAAY;AAAA,QAChC;AAAA,MAAA,CACD;AAAA,MACD;AAAA,IACF;AAAA,IACC,GAAG;AAAA,EAAA;AACN;AAEF,eAAe,cAAc;AAE7B,MAAM,qBAAqB,CAAC;AAAA,EAC1B;AAAA,EACA,GAAG;AACL,MACEE,kCAAA;AAAA,EAAC;AAAA,EAAA;AAAA,IACC,cAAW;AAAA,IACX,MAAK;AAAA,IACL,WAAW,GAAG,gBAAgB,SAAS;AAAA,IACtC,GAAG;AAAA,IAEJ,UAAA;AAAA,MAACF,kCAAAA,IAAA,aAAA,EAAY,WAAU,UAAU,CAAA;AAAA,MACjCA,kCAAAA,IAAC,UAAK,UAAQ,WAAA,CAAA;AAAA,IAAA;AAAA,EAAA;AAChB;AAEF,mBAAmB,cAAc;AAEjC,MAAM,iBAAiB,CAAC;AAAA,EACtB;AAAA,EACA,GAAG;AACL,MACEE,kCAAA;AAAA,EAAC;AAAA,EAAA;AAAA,IACC,cAAW;AAAA,IACX,MAAK;AAAA,IACL,WAAW,GAAG,gBAAgB,SAAS;AAAA,IACtC,GAAG;AAAA,IAEJ,UAAA;AAAA,MAAAF,kCAAAA,IAAC,UAAK,UAAI,OAAA,CAAA;AAAA,MACVA,kCAAAA,IAAC,cAAa,EAAA,WAAU,UAAU,CAAA;AAAA,IAAA;AAAA,EAAA;AACpC;AAEF,eAAe,cAAc;AAE7B,MAAM,qBAAqB,CAAC;AAAA,EAC1B;AAAA,EACA,GAAG;AACL,MACEE,kCAAA;AAAA,EAAC;AAAA,EAAA;AAAA,IACC,eAAW;AAAA,IACX,WAAW,GAAG,4CAA4C,SAAS;AAAA,IAClE,GAAG;AAAA,IAEJ,UAAA;AAAA,MAACF,kCAAAA,IAAAG,UAAA,EAAe,WAAU,UAAU,CAAA;AAAA,MACnCH,kCAAA,IAAA,QAAA,EAAK,WAAU,WAAU,UAAU,aAAA,CAAA;AAAA,IAAA;AAAA,EAAA;AACtC;AAEF,mBAAmB,cAAc;", "x_google_ignoreList": [0]}