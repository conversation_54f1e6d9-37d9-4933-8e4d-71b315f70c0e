{"version": 3, "file": "sellerSetting.payouts-BoUJt10X.js", "sources": ["../../../app/routes/sellerSetting.payouts.tsx"], "sourcesContent": ["import { useLoaderData } from \"@remix-run/react\";\r\nimport { metabaseService } from \"../utils/metabase\";\r\nimport { withAuth, withResponse } from \"../utils/auth-utils\";\r\n\r\nexport const loader = withAuth(async ({ user }) => {\r\n  const embedUrl = metabaseService.generateDashboardUrl(9, {\r\n    seller_id_: user.sellerId,\r\n  });\r\n  \r\n  return withResponse({ embedUrl });\r\n});\r\n\r\nexport default function Payouts() {\r\n  const { embedUrl } = useLoaderData<typeof loader>();\r\n\r\n  return (\r\n    <div className=\"p-6\">\r\n      <div className=\"mb-6\">\r\n        <h1 className=\"text-3xl font-bold text-gray-900\">Payouts</h1>\r\n        <p className=\"text-gray-600 mt-2\">Manage your earnings and payments</p>\r\n      </div>\r\n      \r\n      <div className=\"w-full h-screen\">\r\n        {embedUrl ? (\r\n          <iframe\r\n            id=\"metabase-payouts-iframe\"\r\n            src={embedUrl}\r\n            title=\"Payouts Dashboard\"\r\n            className=\"w-full h-full border-0\"\r\n          />\r\n        ) : (\r\n          <div className=\"flex items-center justify-center min-h-[400px]\">\r\n            <div className=\"text-center\">\r\n              <div className=\"mx-auto mb-4 w-16 h-16 bg-gradient-to-r from-green-500 to-yellow-500 rounded-full flex items-center justify-center\">\r\n                <svg className=\"w-8 h-8 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z\" />\r\n                </svg>\r\n              </div>\r\n              <h2 className=\"text-2xl font-bold mb-2\">Payouts Dashboard Loading</h2>\r\n              <p className=\"text-gray-500\">\r\n                Loading your payouts and earnings data...\r\n              </p>\r\n            </div>\r\n          </div>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n} "], "names": ["Payouts", "embedUrl", "useLoaderData", "jsxs", "className", "children", "jsx", "id", "src", "title", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d"], "mappings": ";;;;AAYA,SAAwBA,UAAU;AAC1B,QAAA;AAAA,IAAEC;AAAAA,EAAS,IAAIC,cAA6B;AAGhD,SAAAC,kCAAAA,KAAC,OAAI;AAAA,IAAAC,WAAU;AAAA,IACbC,UAAA,CAACF,kCAAA,KAAA,OAAA;AAAA,MAAIC,WAAU;AAAA,MACbC,UAAA,CAACC,kCAAA,IAAA,MAAA;AAAA,QAAGF,WAAU;AAAA,QAAmCC,UAAO;AAAA,MAAA,CAAA,GACvDC,kCAAA,IAAA,KAAA;AAAA,QAAEF,WAAU;AAAA,QAAqBC,UAAiC;AAAA,MAAA,CAAA,CAAA;AAAA,IACrE,CAAA,GAECC,kCAAA,IAAA,OAAA;AAAA,MAAIF,WAAU;AAAA,MACZC,UACCJ,WAAAK,kCAAA,IAAC,UAAA;AAAA,QACCC,IAAG;AAAA,QACHC,KAAKP;AAAAA,QACLQ,OAAM;AAAA,QACNL,WAAU;AAAA,MAAA,CACZ,0CAEC,OAAI;AAAA,QAAAA,WAAU;AAAA,QACbC,UAACF,kCAAA,KAAA,OAAA;AAAA,UAAIC,WAAU;AAAA,UACbC,UAAA,CAACC,kCAAA,IAAA,OAAA;AAAA,YAAIF,WAAU;AAAA,YACbC,UAACC,kCAAA,IAAA,OAAA;AAAA,cAAIF,WAAU;AAAA,cAAqBM,MAAK;AAAA,cAAOC,QAAO;AAAA,cAAeC,SAAQ;AAAA,cAC5EP,UAACC,kCAAA,IAAA,QAAA;AAAA,gBAAKO,eAAc;AAAA,gBAAQC,gBAAe;AAAA,gBAAQC,aAAa;AAAA,gBAAGC,GAAE;AAAA,cAAyF,CAAA;AAAA,YAChK,CAAA;AAAA,UACF,CAAA,GACCV,kCAAA,IAAA,MAAA;AAAA,YAAGF,WAAU;AAAA,YAA0BC,UAAyB;AAAA,UAAA,CAAA,GAChEC,kCAAA,IAAA,KAAA;AAAA,YAAEF,WAAU;AAAA,YAAgBC,UAE7B;AAAA,UAAA,CAAA,CAAA;AAAA,QACF,CAAA;AAAA,MACF,CAAA;AAAA,IAEJ,CAAA,CAAA;AAAA,EACF,CAAA;AAEJ;"}