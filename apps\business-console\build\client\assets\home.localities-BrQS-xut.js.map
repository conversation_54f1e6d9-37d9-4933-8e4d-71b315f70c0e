{"version": 3, "file": "home.localities-BrQS-xut.js", "sources": ["../../../node_modules/lucide-react/dist/esm/icons/eye-off.js", "../../../node_modules/@googlemaps/polyline-codec/dist/index.esm.js", "../../../app/routes/home.localities.tsx"], "sourcesContent": ["/**\n * @license lucide-react v0.453.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst EyeOff = createLucideIcon(\"EyeOff\", [\n  [\n    \"path\",\n    {\n      d: \"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49\",\n      key: \"ct8e1f\"\n    }\n  ],\n  [\"path\", { d: \"M14.084 14.158a3 3 0 0 1-4.242-4.242\", key: \"151rxh\" }],\n  [\n    \"path\",\n    {\n      d: \"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143\",\n      key: \"13bj9a\"\n    }\n  ],\n  [\"path\", { d: \"m2 2 20 20\", key: \"1ooewy\" }]\n]);\n\nexport { EyeOff as default };\n//# sourceMappingURL=eye-off.js.map\n", "/**\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Decodes an encoded path string into a sequence of LatLngs.\n *\n * See {@link https://developers.google.com/maps/documentation/utilities/polylinealgorithm}\n *\n *  #### Example\n *\n * ```js\n * import { decode } from \"@googlemaps/polyline-codec\";\n *\n * const encoded = \"_p~iF~ps|U_ulLnnqC_mqNvxq`@\";\n * console.log(decode(encoded, 5));\n * // [\n * //   [38.5, -120.2],\n * //   [40.7, -120.95],\n * //   [43.252, -126.453],\n * // ]\n * ```\n */\nvar decode = function (encodedPath, precision) {\n    if (precision === void 0) { precision = 5; }\n    var factor = Math.pow(10, precision);\n    var len = encodedPath.length;\n    // For speed we preallocate to an upper bound on the final length, then\n    // truncate the array before returning.\n    var path = new Array(Math.floor(encodedPath.length / 2));\n    var index = 0;\n    var lat = 0;\n    var lng = 0;\n    var pointIndex = 0;\n    // This code has been profiled and optimized, so don't modify it without\n    // measuring its performance.\n    for (; index < len; ++pointIndex) {\n        // Fully unrolling the following loops speeds things up about 5%.\n        var result = 1;\n        var shift = 0;\n        var b = void 0;\n        do {\n            // Invariant: \"result\" is current partial result plus (1 << shift).\n            // The following line effectively clears this bit by decrementing \"b\".\n            b = encodedPath.charCodeAt(index++) - 63 - 1;\n            result += b << shift;\n            shift += 5;\n        } while (b >= 0x1f); // See note above.\n        lat += result & 1 ? ~(result >> 1) : result >> 1;\n        result = 1;\n        shift = 0;\n        do {\n            b = encodedPath.charCodeAt(index++) - 63 - 1;\n            result += b << shift;\n            shift += 5;\n        } while (b >= 0x1f);\n        lng += result & 1 ? ~(result >> 1) : result >> 1;\n        path[pointIndex] = [lat / factor, lng / factor];\n    }\n    // truncate array\n    path.length = pointIndex;\n    return path;\n};\n/**\n * Polyline encodes an array of objects having lat and lng properties.\n *\n * See {@link https://developers.google.com/maps/documentation/utilities/polylinealgorithm}\n *\n * #### Example\n *\n * ```js\n * import { encode } from \"@googlemaps/polyline-codec\";\n *\n * const path = [\n *   [38.5, -120.2],\n *   [40.7, -120.95],\n *   [43.252, -126.453],\n * ];\n * console.log(encode(path, 5));\n * // \"_p~iF~ps|U_ulLnnqC_mqNvxq`@\"\n * ```\n */\nvar encode = function (path, precision) {\n    if (precision === void 0) { precision = 5; }\n    var factor = Math.pow(10, precision);\n    var transform = function latLngToFixed(latLng) {\n        if (!Array.isArray(latLng)) {\n            latLng = [latLng.lat, latLng.lng];\n        }\n        return [round(latLng[0] * factor), round(latLng[1] * factor)];\n    };\n    return polylineEncodeLine(path, transform);\n};\n/**\n * Encodes a generic polyline; optionally performing a transform on each point\n * before encoding it.\n *\n * @ignore\n */\nvar polylineEncodeLine = function (array, transform) {\n    var v = [];\n    var start = [0, 0];\n    var end;\n    for (var i = 0, I = array.length; i < I; ++i) {\n        // In order to prevent drift (from quantizing deltas), we explicitly convert\n        // coordinates to fixed-precision to obtain integer deltas.\n        end = transform(array[i]);\n        // Push the next edge\n        polylineEncodeSigned(round(end[0]) - round(start[0]), v); // lat\n        polylineEncodeSigned(round(end[1]) - round(start[1]), v); // lng\n        start = end;\n    }\n    return v.join(\"\");\n};\n/**\n * Encodes the given value in our compact polyline format, appending the\n * encoded value to the given array of strings.\n *\n * @ignore\n */\nvar polylineEncodeSigned = function (value, array) {\n    return polylineEncodeUnsigned(value < 0 ? ~(value << 1) : value << 1, array);\n};\n/**\n * Helper function for encodeSigned.\n *\n * @ignore\n */\nvar polylineEncodeUnsigned = function (value, array) {\n    while (value >= 0x20) {\n        array.push(String.fromCharCode((0x20 | (value & 0x1f)) + 63));\n        value >>= 5;\n    }\n    array.push(String.fromCharCode(value + 63));\n    return array;\n};\n/**\n * @ignore\n */\nvar round = function (v) {\n    return Math.floor(Math.abs(v) + 0.5) * (v >= 0 ? 1 : -1);\n};\n\nexport { decode, encode, polylineEncodeLine };\n//# sourceMappingURL=index.esm.js.map\n", "import { useRef, useState, useCallback, useEffect, useMemo } from \"react\";\r\nimport {\r\n  useLoaderData,\r\n  useNavigate,\r\n  useLocation,\r\n  useSearchParams,\r\n  useActionData,\r\n  Form,\r\n  Outlet\r\n} from \"@remix-run/react\";\r\nimport type { LoaderFunction, ActionFunction } from \"@remix-run/node\";\r\nimport { json, redirect } from \"@remix-run/node\";\r\n\r\n// ----- Services (server-side safe) -----\r\nimport {\r\n  getGlobalAreas,\r\n  getDistrictsAndStates,\r\n  updateArea,\r\n  createArea,\r\n  disableMasterLocality\r\n} from \"~/services/businessConsoleService\";\r\n\r\n// ----- Utils (server-side safe) -----\r\nimport { withAuth, withResponse } from \"@utils/auth-utils\";\r\n// Use polylineCodec for encoding coordinates\r\nimport * as polylineCodec from \"@googlemaps/polyline-codec\";\r\n// We'll continue using your decodePolygon function for decoding\r\nimport { decodePolygon } from \"@utils/polyline-utils\";\r\n\r\n// ----- Types -----\r\nimport type { Seller<PERSON><PERSON> } from \"~/types/api/businessConsoleService/Areas\";\r\n\r\n\r\n\r\nimport {\r\n  CreateAreaRequest,\r\n  CreateAreaResponse,\r\n  UpdateAreaRequest\r\n} from \"~/types/api/businessConsoleService/Areas\";\r\n\r\n// ----- UI & Components -----\r\nimport {\r\n  GoogleMap,\r\n  LoadScript,\r\n  Polygon,\r\n  InfoWindow,\r\n  DrawingManager\r\n} from \"@react-google-maps/api\";\r\nimport { Button } from \"@components/ui/button\";\r\nimport { Input } from \"@components/ui/input\";\r\nimport {\r\n  Select,\r\n  SelectContent,\r\n  SelectItem,\r\n  SelectTrigger,\r\n  SelectValue\r\n} from \"@components/ui/select\";\r\nimport { Pencil, Trash2, ArrowUpDown, Eye, EyeOff, X } from \"lucide-react\";\r\n\r\n// ------------- Loader (server side) -------------\r\ninterface LoaderData {\r\n  areas: SellerArea[];\r\n  googleMapsApiKey: string;\r\n  states: string[];\r\n  districts: { [state: string]: string[] };\r\n}\r\n\r\nexport const loader: LoaderFunction = withAuth(async ({ request, user }) => {\r\n  const url = new URL(request.url);\r\n  const stateParam = url.searchParams.get(\"state\") || \"Karnataka\";\r\n  const districtParam = url.searchParams.get(\"district\") || \"Bangalore\";\r\n\r\n  const [areasResponse, districtsAndStatesResponse] = await Promise.all([\r\n    getGlobalAreas(user.userId, stateParam, districtParam, request),\r\n    getDistrictsAndStates(user.userId, request)\r\n  ]);\r\n\r\n  const googleMapsApiKey = process.env.GOOGLE_MAPS_API_KEY || \"\";\r\n  const areas = areasResponse.data;\r\n  const districtsAndStates = districtsAndStatesResponse.data;\r\n\r\n  const statesSet = new Set<string>();\r\n  const districtsMap: { [state: string]: Set<string> } = {};\r\n\r\n  districtsAndStates?.forEach((area: SellerArea) => {\r\n    statesSet.add(area.state);\r\n    if (!districtsMap[area.state]) {\r\n      districtsMap[area.state] = new Set<string>();\r\n    }\r\n    districtsMap[area.state].add(area.district);\r\n  });\r\n\r\n  return withResponse({\r\n    areas,\r\n    googleMapsApiKey,\r\n    states: Array.from(statesSet).sort(),\r\n    districts: Object.fromEntries(\r\n      Object.entries(districtsMap).map(([state, districtsSet]) => [\r\n        state,\r\n        Array.from(districtsSet).sort()\r\n      ])\r\n    )\r\n  });\r\n});\r\n\r\n// ------------- Action (server side) -------------\r\n// New: if the form field \"action\" equals \"delete\", call disableMasterLocality.\r\nexport const action: ActionFunction = withAuth(async ({ request, user }) => {\r\n  const formData = await request.formData();\r\n  const actionType = formData.get(\"action\")?.toString();\r\n  const id = Number(formData.get(\"id\"));\r\n\r\n  const name = formData.get(\"name\")?.toString() || \"\";\r\n  const state = formData.get(\"state\")?.toString() || \"\";\r\n  const district = formData.get(\"district\")?.toString() || \"\";\r\n  const polygon = formData.get(\"polygon\")?.toString() || \"\";\r\n  const mode = formData.get(\"mode\")\r\n  const radius = Number(formData.get(\"radius\"));\r\n  const latitude = Number(formData.get(\"latitude\"));\r\n  const longitude = Number(formData.get(\"longitude\"));\r\n\r\n\r\n  console.log(radius, latitude, longitude, \"0000000000\")\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n  if (actionType === \"delete\") {\r\n    try {\r\n      await disableMasterLocality(id, request);\r\n      return redirect(`/home/<USER>\n    } catch (error) {\r\n      return json({ error: \"Failed to delete area\" }, { status: 400 });\r\n    }\r\n  }\r\n\r\n\r\n  if (id === 0) {\r\n    const createReq: CreateAreaRequest = {\r\n      name,\r\n      state,\r\n      district,\r\n      polygon: polygon || \"\",\r\n      radius: radius,\r\n      latitude: latitude,\r\n      longitude: longitude\r\n    };\r\n    const createRadiusReq: any = {\r\n      name,\r\n      state,\r\n      district,\r\n      radius: radius,\r\n      latitude: latitude,\r\n      longitude: longitude\r\n    };\r\n\r\n    const finalCreateRequest = mode === \"RadiusCreate\" ? createRadiusReq : createReq;\r\n    try {\r\n      await createArea(user.userId, finalCreateRequest, request);\r\n      return redirect(`/home/<USER>\n    } catch (error) {\r\n      return json({ error: \"Failed to create area\" }, { status: 400 });\r\n    }\r\n  } else {\r\n    const updateReq: UpdateAreaRequest = {\r\n      id,\r\n      name,\r\n      state,\r\n      district,\r\n      polygon: polygon || undefined\r\n    };\r\n    try {\r\n      await updateArea(user.userId, updateReq, request);\r\n      return redirect(`/home/<USER>\n    } catch (error) {\r\n      return json({ error: \"Failed to update area\" }, { status: 400 });\r\n    }\r\n  }\r\n});\r\n\r\n// ------------- Helper: Deduplicate Coordinates -------------\r\n// Receives an array of [lat, lng] pairs and returns a deduplicated array.\r\nfunction deduplicateCoordinates(coords: number[][]): number[][] {\r\n  if (coords.length === 0) return coords;\r\n  const deduped = [coords[0]];\r\n  for (let i = 1; i < coords.length; i++) {\r\n    const prev = deduped[deduped.length - 1];\r\n    const curr = coords[i];\r\n    if (prev[0] === curr[0] && prev[1] === curr[1]) continue;\r\n    deduped.push(curr);\r\n  }\r\n  return deduped;\r\n}\r\n\r\n// ------------- Constants & Helpers -------------\r\nconst BANGALORE_CENTER = { lat: 12.9716, lng: 77.5946 };\r\nconst MAP_CONTAINER_STYLE = { width: \"100%\", height: \"100%\" };\r\n\r\nfunction getPolygonColor(index: number) {\r\n  const colors = [\r\n    \"#3b82f6\",\r\n    \"#10b981\",\r\n    \"#f59e0b\",\r\n    \"#ef4444\",\r\n    \"#8b5cf6\",\r\n    \"#ec4899\",\r\n    \"#06b6d4\",\r\n    \"#f97316\"\r\n  ];\r\n  return colors[index % colors.length];\r\n}\r\n\r\n// ------------- Main Component -------------\r\nexport default function LocalitiesSection() {\r\n  const location = useLocation();\r\n  const navigate = useNavigate();\r\n  const [searchParams, setSearchParams] = useSearchParams();\r\n  const actionData = useActionData<{ error?: string }>();\r\n\r\n  const { areas: initialAreas, googleMapsApiKey, states, districts } =\r\n    useLoaderData<LoaderData>();\r\n\r\n  const [localAreas, setLocalAreas] = useState<SellerArea[]>(initialAreas);\r\n  useEffect(() => {\r\n    setLocalAreas(initialAreas);\r\n  }, [initialAreas]);\r\n\r\n  const [selectedState, setSelectedState] = useState(\r\n    searchParams.get(\"state\") || \"Karnataka\"\r\n  );\r\n  const [selectedDistrict, setSelectedDistrict] = useState(\r\n    searchParams.get(\"district\") || \"Bangalore\"\r\n  );\r\n  const [visibleAreas, setVisibleAreas] = useState<Set<number>>(new Set());\r\n  const [map, setMap] = useState<google.maps.Map | null>(null);\r\n  const [mapLoaded, setMapLoaded] = useState(false);\r\n  const [searchTerm, setSearchTerm] = useState(\"\");\r\n  const [sortOrder, setSortOrder] = useState<\"asc\" | \"desc\">(\"asc\");\r\n\r\n  // The area currently being edited (if any). For new areas, id === 0.\r\n  const [editingArea, setEditingArea] = useState<SellerArea | null>(null);\r\n  // This state stores the updated encoded polygon string as the user draws/edits.\r\n  const [editingPolygon, setEditingPolygon] = useState<string>(\"\");\r\n\r\n  // For vertex deletion: index of the selected vertex (if any)\r\n  const [selectedVertexIndex, setSelectedVertexIndex] = useState<number | null>(null);\r\n\r\n  // Info window state: to show area info on polygon click.\r\n  const [infoWindow, setInfoWindow] = useState<{\r\n    position: google.maps.LatLngLiteral;\r\n    content: string;\r\n  } | null>(null);\r\n\r\n  // Flag for drawing mode (for free drawing of new polygon)\r\n  const [drawingMode, setDrawingMode] = useState<boolean>(false);\r\n  const [radiusMode, setRadiusMode] = useState<boolean>(false);\r\n\r\n\r\n  // We'll store references to each polygon so we can attach listeners.\r\n  const polygonRefs = useRef<{ [areaId: number]: google.maps.Polygon }>({});\r\n\r\n  useEffect(() => {\r\n    if (mapLoaded) {\r\n      const initialVisible = new Set(\r\n        localAreas.filter(a => a.polygon).map(a => a.id)\r\n      );\r\n      setVisibleAreas(initialVisible);\r\n    }\r\n  }, [mapLoaded, localAreas]);\r\n\r\n  const onLoadMap = useCallback((mapInstance: google.maps.Map) => {\r\n    setMap(mapInstance);\r\n    setMapLoaded(true);\r\n  }, []);\r\n\r\n  const onUnmountMap = useCallback(() => {\r\n    setMap(null);\r\n    setMapLoaded(false);\r\n  }, []);\r\n\r\n  const fitBounds = useCallback(() => {\r\n    if (map && visibleAreas.size > 0) {\r\n      const bounds = new google.maps.LatLngBounds();\r\n      localAreas.forEach(area => {\r\n        if (visibleAreas.has(area.id) && area.polygon) {\r\n          decodePolygon(area.polygon).forEach(coord => bounds.extend(coord));\r\n        }\r\n      });\r\n      map.fitBounds(bounds);\r\n    }\r\n  }, [map, visibleAreas, localAreas]);\r\n\r\n  useEffect(() => {\r\n    if (mapLoaded) {\r\n      fitBounds();\r\n    }\r\n  }, [fitBounds, mapLoaded]);\r\n\r\n  const toggleAreaVisibility = useCallback((areaId: number) => {\r\n    setVisibleAreas(prev => {\r\n      const newSet = new Set(prev);\r\n      if (newSet.has(areaId)) {\r\n        newSet.delete(areaId);\r\n      } else {\r\n        newSet.add(areaId);\r\n      }\r\n      return newSet;\r\n    });\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    fitBounds();\r\n  }, [visibleAreas, fitBounds]);\r\n\r\n  const setAllVisible = () => {\r\n    const allWithPolygons = new Set(\r\n      localAreas.filter(a => a.polygon).map(a => a.id)\r\n    );\r\n    setVisibleAreas(allWithPolygons);\r\n  };\r\n  const setNoneVisible = () => setVisibleAreas(new Set());\r\n\r\n  const filteredAreas = useMemo(() => {\r\n    return localAreas\r\n      .filter(area => {\r\n        const matchesSearch = area.name\r\n          .toLowerCase()\r\n          .includes(searchTerm.toLowerCase());\r\n        const matchesState =\r\n          selectedState === \"all\" || area.state === selectedState;\r\n        const matchesDistrict =\r\n          selectedDistrict === \"all\" || area.district === selectedDistrict;\r\n        const polygonpresent = area.polygon\r\n        return matchesSearch && matchesState && matchesDistrict && polygonpresent;\r\n      })\r\n      .sort((a, b) =>\r\n        sortOrder === \"asc\"\r\n          ? a.name.localeCompare(b.name)\r\n          : b.name.localeCompare(a.name)\r\n      );\r\n  }, [localAreas, searchTerm, selectedState, selectedDistrict, sortOrder]);\r\n\r\n  const handleSort = () => {\r\n    setSortOrder(sortOrder === \"asc\" ? \"desc\" : \"asc\");\r\n  };\r\n\r\n  if (\r\n    location.pathname.endsWith(\"/new\") ||\r\n    location.pathname.endsWith(\"/attach\") ||\r\n    location.pathname.includes(\"/edit\")\r\n  ) {\r\n    return <Outlet />;\r\n  }\r\n\r\n  // ------------------\r\n  // ADD FLOW:\r\n  // When \"Add\" is clicked, we set drawingMode to true so the user can freely draw.\r\n  const handleAdd = () => {\r\n    setDrawingMode(true);\r\n    // Clear any previous editing state.\r\n    setEditingArea(null);\r\n    setEditingPolygon(\"\");\r\n  };\r\n  const handleRadiusAdd = () => {\r\n    setRadiusMode(true)\r\n    // Clear any previous editing state.\r\n  };\r\n  // ------------------\r\n\r\n  // When the drawing is complete, this callback is called.\r\n  const onPolygonComplete = useCallback(\r\n    (poly: google.maps.Polygon) => {\r\n      // Remove the drawing overlay.\r\n      poly.setMap(null);\r\n      const path = poly.getPath();\r\n      const coords: { lat: number; lng: number }[] = [];\r\n      for (let i = 0; i < path.getLength(); i++) {\r\n        const latLng = path.getAt(i);\r\n        coords.push({ lat: latLng.lat(), lng: latLng.lng() });\r\n      }\r\n      const coordsArray = coords.map(({ lat, lng }) => [lat, lng]);\r\n      const deduped = deduplicateCoordinates(coordsArray);\r\n      const encoded = polylineCodec.encode(deduped);\r\n      // Create new area with id 0.\r\n      const newArea: SellerArea = {\r\n        id: 0,\r\n        name: \"\",\r\n        state: selectedState === \"all\" ? \"\" : selectedState,\r\n        district: selectedDistrict === \"all\" ? \"\" : selectedDistrict,\r\n        polygon: encoded\r\n      };\r\n      setLocalAreas(prev => [...prev, newArea]);\r\n      setEditingArea(newArea);\r\n      setEditingPolygon(encoded);\r\n      setDrawingMode(false);\r\n    },\r\n    [selectedState, selectedDistrict]\r\n  );\r\n\r\n  // Start editing an area (from table \"Edit\" button)\r\n  const startEditing = (area: SellerArea) => {\r\n    setEditingArea(area);\r\n    setEditingPolygon(area.polygon || \"\");\r\n    setSelectedVertexIndex(null);\r\n  };\r\n\r\n  // Store polygon reference for each area.\r\n  const handlePolygonLoad = useCallback(\r\n    (polygon: google.maps.Polygon, areaId: number) => {\r\n      polygonRefs.current[areaId] = polygon;\r\n    },\r\n    []\r\n  );\r\n\r\n  // Update editingPolygon state by reading the polygon's current path.\r\n  const handlePolygonEdit = useCallback(\r\n    (areaId: number) => {\r\n      const poly = polygonRefs.current[areaId];\r\n      if (!poly) return;\r\n      const path = poly.getPath();\r\n      const coords: { lat: number; lng: number }[] = [];\r\n      for (let i = 0; i < path.getLength(); i++) {\r\n        const latLng = path.getAt(i);\r\n        coords.push({ lat: latLng.lat(), lng: latLng.lng() });\r\n      }\r\n      const coordsArray = coords.map(({ lat, lng }) => [lat, lng]);\r\n      const deduped = deduplicateCoordinates(coordsArray);\r\n      if (deduped.length < coordsArray.length) {\r\n        const newPath = deduped.map(([lat, lng]) => ({ lat, lng }));\r\n        poly.setPath(newPath);\r\n      }\r\n      const encoded = polylineCodec.encode(deduped);\r\n      setEditingPolygon(encoded);\r\n    },\r\n    []\r\n  );\r\n\r\n  // Helper: Deduplicate overlapping coordinate pairs.\r\n  function deduplicateCoordinates(coords: number[][]): number[][] {\r\n    if (coords.length === 0) return coords;\r\n    const deduped = [coords[0]];\r\n    for (let i = 1; i < coords.length; i++) {\r\n      const prev = deduped[deduped.length - 1];\r\n      const curr = coords[i];\r\n      if (prev[0] === curr[0] && prev[1] === curr[1]) continue;\r\n      deduped.push(curr);\r\n    }\r\n    return deduped;\r\n  }\r\n\r\n  // Attach listeners to polygon's path when an area is in edit mode.\r\n  useEffect(() => {\r\n    if (editingArea) {\r\n      const poly = polygonRefs.current[editingArea.id];\r\n      if (poly) {\r\n        const path = poly.getPath();\r\n        const listener1 = google.maps.event.addListener(path, \"set_at\", () => {\r\n          handlePolygonEdit(editingArea.id);\r\n        });\r\n        const listener2 = google.maps.event.addListener(path, \"insert_at\", () => {\r\n          handlePolygonEdit(editingArea.id);\r\n        });\r\n        return () => {\r\n          google.maps.event.removeListener(listener1);\r\n          google.maps.event.removeListener(listener2);\r\n        };\r\n      }\r\n    }\r\n  }, [editingArea, handlePolygonEdit]);\r\n\r\n  // Allow deletion of a selected vertex via Delete/Backspace key.\r\n  useEffect(() => {\r\n    function handleKeyDown(e: KeyboardEvent) {\r\n      if (\r\n        (e.key === \"Delete\" || e.key === \"Backspace\") &&\r\n        editingArea !== null &&\r\n        selectedVertexIndex !== null\r\n      ) {\r\n        const poly = polygonRefs.current[editingArea.id];\r\n        if (poly) {\r\n          const path = poly.getPath();\r\n          if (path.getLength() > 3) {\r\n            path.removeAt(selectedVertexIndex);\r\n            handlePolygonEdit(editingArea.id);\r\n            setSelectedVertexIndex(null);\r\n          }\r\n        }\r\n      }\r\n    }\r\n    if (editingArea) {\r\n      window.addEventListener(\"keydown\", handleKeyDown);\r\n    }\r\n    return () => {\r\n      window.removeEventListener(\"keydown\", handleKeyDown);\r\n    };\r\n  }, [editingArea, selectedVertexIndex, handlePolygonEdit]);\r\n\r\n  // When in editing mode, clicking on the polygon selects a vertex.\r\n  const handlePolygonClick = useCallback(\r\n    (e: google.maps.MapMouseEvent, area: SellerArea) => {\r\n      if (editingArea && editingArea.id === area.id && e.latLng) {\r\n        const poly = polygonRefs.current[area.id];\r\n        if (poly) {\r\n          const path = poly.getPath();\r\n          let closestIndex: number | null = null;\r\n          let minDist = Infinity;\r\n          for (let i = 0; i < path.getLength(); i++) {\r\n            const vertex = path.getAt(i);\r\n            const d = Math.sqrt(\r\n              Math.pow(vertex.lat() - e.latLng.lat(), 2) +\r\n              Math.pow(vertex.lng() - e.latLng.lng(), 2)\r\n            );\r\n            if (d < minDist) {\r\n              minDist = d;\r\n              closestIndex = i;\r\n            }\r\n          }\r\n          const threshold = 0.0001;\r\n          if (minDist < threshold) {\r\n            setSelectedVertexIndex(closestIndex);\r\n          } else {\r\n            setSelectedVertexIndex(null);\r\n          }\r\n        }\r\n      } else {\r\n        // When not in editing mode, show an info window.\r\n        if (e.latLng && area) {\r\n          setInfoWindow({\r\n            position: e.latLng.toJSON(),\r\n            content: `${area.id} - ${area.name}`\r\n          });\r\n        }\r\n      }\r\n    },\r\n    [editingArea]\r\n  );\r\n\r\n  const handleCancelEdit = () => {\r\n    setEditingArea(null);\r\n    setEditingPolygon(\"\");\r\n    setSelectedVertexIndex(null);\r\n  };\r\n\r\n\r\n\r\n\r\n  return (\r\n    <LoadScript\r\n      googleMapsApiKey={googleMapsApiKey}\r\n      libraries={[\"drawing\"]}\r\n    >\r\n      <div className=\"flex h-[calc(100vh-64px)] relative\">\r\n        {/* LEFT PANE */}\r\n        <div className=\"w-fit bg-white border-r border-gray-200 overflow-y-auto flex flex-col\">\r\n          <div className=\"p-4 border-b border-gray-200\">\r\n            <div className=\"flex justify-between items-center mb-4\">\r\n              <h2 className=\"text-lg font-semibold text-gray-900\">\r\n                Service Areas\r\n              </h2>\r\n              <div className=\"space-x-2 flex \">\r\n                <Button onClick={handleAdd} size=\"sm\">\r\n                  Add Polygon                </Button>\r\n                <Button onClick={handleRadiusAdd} size=\"sm\">\r\n                  Add Radius\r\n                </Button>\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"flex gap-2 mb-4\">\r\n              <Select\r\n                value={selectedState}\r\n                onValueChange={(value) => {\r\n                  setSelectedState(value);\r\n                  setSelectedDistrict(\"\");\r\n                  setSearchParams({ state: value, district: \"\" });\r\n                }}\r\n              >\r\n                <SelectTrigger className=\"w-1/2\">\r\n                  <SelectValue placeholder=\"Select state\" />\r\n                </SelectTrigger>\r\n                <SelectContent>\r\n                  <SelectItem value=\"all\">All States</SelectItem>\r\n                  {states.map((state) => (\r\n                    <SelectItem key={state} value={state}>\r\n                      {state}\r\n                    </SelectItem>\r\n                  ))}\r\n                </SelectContent>\r\n              </Select>\r\n              <Select\r\n                value={selectedDistrict}\r\n                onValueChange={(value) => {\r\n                  setSelectedDistrict(value);\r\n                  setSearchParams({ state: selectedState, district: value });\r\n                }}\r\n                disabled={selectedState === \"all\"}\r\n              >\r\n                <SelectTrigger className=\"w-1/2\">\r\n                  <SelectValue placeholder=\"Select district\" />\r\n                </SelectTrigger>\r\n                <SelectContent>\r\n                  <SelectItem value=\"all\">All Districts</SelectItem>\r\n                  {selectedState !== \"all\" &&\r\n                    districts[selectedState]?.map((d) => (\r\n                      <SelectItem key={d} value={d}>\r\n                        {d}\r\n                      </SelectItem>\r\n                    ))}\r\n                </SelectContent>\r\n              </Select>\r\n            </div>\r\n            <div className=\"flex justify-between items-center mb-4\">\r\n              <Input\r\n                placeholder=\"Search areas...\"\r\n                value={searchTerm}\r\n                onChange={(e) => setSearchTerm(e.target.value)}\r\n                className=\"flex w-1/2\"\r\n              />\r\n              <div className=\"space-x-2\">\r\n                <Button\r\n                  onClick={setNoneVisible}\r\n                  variant=\"outline\"\r\n                  size=\"sm\"\r\n                >\r\n                  Hide All\r\n                </Button>\r\n                <Button\r\n                  onClick={setAllVisible}\r\n                  variant=\"outline\"\r\n                  size=\"sm\"\r\n                >\r\n                  Show All\r\n                </Button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div className=\"flex-grow overflow-auto\">\r\n            <table className=\"min-w-full divide-y divide-gray-200\">\r\n              <thead className=\"bg-gray-50 sticky top-0\">\r\n                <tr>\r\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n                    <Button\r\n                      variant=\"ghost\"\r\n                      onClick={handleSort}\r\n                      className=\"flex items-center\"\r\n                    >\r\n                      Name\r\n                      <ArrowUpDown size={14} className=\"ml-1\" />\r\n                    </Button>\r\n                  </th>\r\n                  <th className=\"px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n                    Actions\r\n                  </th>\r\n\r\n                </tr>\r\n              </thead>\r\n              <tbody className=\"bg-white divide-y divide-gray-200\">\r\n                {filteredAreas.map((area, index) => {\r\n                  const isEditing = editingArea?.id === area.id;\r\n                  return (\r\n                    <tr\r\n                      key={area.id}\r\n                      className={`hover:bg-gray-50 ${isEditing ? \"bg-blue-50\" : \"\"\r\n                        }`}\r\n                    >\r\n                      <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                        <div className=\"flex items-center\">\r\n                          <div\r\n                            className=\"w-3 h-3 rounded-full mr-2\"\r\n                            style={{\r\n                              backgroundColor: getPolygonColor(index),\r\n                              opacity: visibleAreas.has(area.id) ? 1 : 0.3\r\n                            }}\r\n                          />\r\n                          <span className=\"text-sm text-gray-900\">\r\n                            {area.name}\r\n                            {!area.polygon && (\r\n                              <span className=\"text-xs text-gray-400 ml-2\">\r\n                                (no boundary)\r\n                              </span>\r\n                            )}\r\n                          </span>\r\n                        </div>\r\n                      </td>\r\n                      <td className=\"px-6 py-4 whitespace-nowrap text-right text-sm font-medium flex gap-2\">\r\n                        <Button\r\n                          variant=\"ghost\"\r\n                          size=\"sm\"\r\n                          onClick={() => toggleAreaVisibility(area.id)}\r\n                          disabled={!area.polygon}\r\n                        >\r\n                          {visibleAreas.has(area.id) ? (\r\n                            <Eye size={16} />\r\n                          ) : (\r\n                            <EyeOff size={16} />\r\n                          )}\r\n                        </Button>\r\n\r\n                        <Button\r\n                          variant=\"ghost\"\r\n                          size=\"sm\"\r\n                          className=\"text-indigo-600 hover:text-indigo-900\"\r\n                          onClick={() => startEditing(area)}\r\n                        >\r\n                          <Pencil size={16} />\r\n                        </Button>\r\n                        {/* Delete button wrapped in a Form */}\r\n                        <Form method=\"post\" onSubmit={(e) => {\r\n                          if (!confirm(\"Are you sure you want to delete this area?\")) {\r\n                            e.preventDefault();\r\n                          }\r\n                        }}>\r\n                          <input type=\"hidden\" name=\"id\" value={area.id} />\r\n                          <input type=\"hidden\" name=\"action\" value=\"delete\" />\r\n                          <Button type=\"submit\" variant=\"ghost\" size=\"sm\" className=\"text-red-600 hover:text-red-900\">\r\n                            <Trash2 size={16} />\r\n                          </Button>\r\n                        </Form>\r\n                      </td>\r\n                    </tr>\r\n                  );\r\n                })}\r\n              </tbody>\r\n            </table>\r\n          </div>\r\n        </div>\r\n\r\n        {/* RIGHT PANE - MAP */}\r\n        <div className=\"flex-1 bg-white relative\">\r\n          <GoogleMap\r\n            mapContainerStyle={MAP_CONTAINER_STYLE}\r\n            center={BANGALORE_CENTER}\r\n            zoom={11}\r\n            onLoad={onLoadMap}\r\n            onUnmount={onUnmountMap}\r\n            options={{\r\n              styles: [\r\n                {\r\n                  featureType: \"all\",\r\n                  elementType: \"geometry.fill\",\r\n                  stylers: [{ visibility: \"on\" }]\r\n                }\r\n              ],\r\n              mapTypeControl: false,\r\n              streetViewControl: false,\r\n              fullscreenControl: false\r\n            }}\r\n          >\r\n            {mapLoaded &&\r\n              localAreas.map((area, index) => {\r\n                if (!area.polygon || !visibleAreas.has(area.id)) return null;\r\n\r\n                const isEditing = editingArea?.id === area.id;\r\n                const polygonPath =\r\n                  isEditing && editingPolygon\r\n                    ? decodePolygon(editingPolygon)\r\n                    : decodePolygon(area.polygon);\r\n\r\n                const polygonOptions = isEditing\r\n                  ? {\r\n                    fillColor: getPolygonColor(index),\r\n                    fillOpacity: 0.5,\r\n                    strokeColor: \"#FFD700\",\r\n                    strokeOpacity: 1,\r\n                    strokeWeight: 4,\r\n                    clickable: true,\r\n                    editable: true\r\n                  }\r\n                  : {\r\n                    fillColor: getPolygonColor(index),\r\n                    fillOpacity: 0.2,\r\n                    strokeColor: getPolygonColor(index),\r\n                    strokeOpacity: 1,\r\n                    strokeWeight: 2,\r\n                    clickable: true,\r\n                    editable: false\r\n                  };\r\n\r\n                return (\r\n                  <Polygon\r\n                    key={area.id}\r\n                    paths={polygonPath}\r\n                    options={polygonOptions}\r\n                    onLoad={(poly) => handlePolygonLoad(poly, area.id)}\r\n                    onClick={(e) => handlePolygonClick(e, area)}\r\n                    onMouseUp={() => handlePolygonEdit(area.id)}\r\n                    onDragEnd={() => handlePolygonEdit(area.id)}\r\n                  />\r\n                );\r\n              })}\r\n\r\n            {/* Render DrawingManager if drawingMode is true */}\r\n            {drawingMode && (\r\n              <DrawingManager\r\n                options={{\r\n                  drawingControl: true,\r\n                  drawingMode:\r\n                    window.google.maps.drawing.OverlayType.POLYGON,\r\n                  polygonOptions: {\r\n                    fillColor: \"#FF0000\",\r\n                    fillOpacity: 0.5,\r\n                    strokeWeight: 2,\r\n                    clickable: true,\r\n                    editable: true,\r\n                    zIndex: 1\r\n                  }\r\n                }}\r\n                onPolygonComplete={(poly) => {\r\n                  poly.setMap(null);\r\n                  const path = poly.getPath();\r\n                  const coords: { lat: number; lng: number }[] = [];\r\n                  for (let i = 0; i < path.getLength(); i++) {\r\n                    const latLng = path.getAt(i);\r\n                    coords.push({ lat: latLng.lat(), lng: latLng.lng() });\r\n                  }\r\n                  const coordsArray = coords.map(({ lat, lng }) => [lat, lng]);\r\n                  const deduped = deduplicateCoordinates(coordsArray);\r\n                  const encoded = polylineCodec.encode(deduped);\r\n                  const newArea: SellerArea = {\r\n                    id: 0,\r\n                    name: \"\",\r\n                    state: selectedState === \"all\" ? \"\" : selectedState,\r\n                    district:\r\n                      selectedDistrict === \"all\" ? \"\" : selectedDistrict,\r\n                    polygon: encoded\r\n                  };\r\n                  setLocalAreas((prev) => [...prev, newArea]);\r\n                  setEditingArea(newArea);\r\n                  setEditingPolygon(encoded);\r\n                  setDrawingMode(false);\r\n                }}\r\n              />\r\n            )}\r\n\r\n            {/* Info Window: show area info when a polygon is clicked */}\r\n            {infoWindow && (\r\n              <InfoWindow\r\n                position={infoWindow.position}\r\n                // onCloseClick={() => setInfoWindow(null)}\r\n                options={{\r\n                  headerDisabled: true,\r\n                  disableAutoPan: true,\r\n                }}\r\n              >\r\n                {/* <div>{infoWindow.content}</div> */}\r\n                <div className=\"flex flex-col gap-2 overflow-hidden \">\r\n                  <div className='flex justify-between w-full align-middle items-center'>\r\n                    <h2 className='text-md font-semibold text-typography-300'>Locality Info</h2>\r\n                    <button className=\"inline-flex items-center gap-1 hover:text-blue-800\"\r\n                      onClick={() => setInfoWindow(null)}>\r\n                      <X className=\"h-5 w-5\" />\r\n                    </button>\r\n                  </div>\r\n                  <div className=\"flex flex-col gap-1 text-sm font-medium text-typography-700 w-[calc(100%-1rem)]\">\r\n                    <p> {infoWindow.content}</p>\r\n                  </div>\r\n                </div>\r\n              </InfoWindow>\r\n            )}\r\n          </GoogleMap>\r\n\r\n          {\r\n            radiusMode &&\r\n            <div className=\"absolute top-4 right-4 w-80 bg-white p-4 shadow-lg rounded z-50\">\r\n\r\n              <Form method=\"post\">\r\n                <input type=\"hidden\" name=\"id\" value={editingArea?.id} />\r\n                <input type=\"hidden\" name=\"mode\" value={\"RadiusCreate\"} readOnly />\r\n                <div className=\"mb-3\">\r\n                  <label className=\"block text-sm font-medium text-gray-700\">\r\n                    Name\r\n                  </label>\r\n                  <Input\r\n                    className=\"w-full\"\r\n                    name=\"name\"\r\n                    defaultValue={editingArea?.name}\r\n                  />\r\n                </div>\r\n                <div className=\"mb-3\">\r\n                  <label className=\"block text-sm font-medium text-gray-700\">Radius</label>\r\n                  <Input\r\n                    className=\"w-full\"\r\n                    name=\"radius\"\r\n                    defaultValue={editingArea?.radius}\r\n                  />\r\n                </div>\r\n                {editingArea?.radius !== null &&\r\n                  <>\r\n                    <div className=\"mb-3\">\r\n                      <label className=\"block text-sm font-medium text-gray-700\">Latitude</label>\r\n                      <Input\r\n                        className=\"w-full\"\r\n                        name=\"latitude\"\r\n                        defaultValue={editingArea?.latitude}\r\n                        required={!!editingArea?.radius} // Make required if radius is present\r\n                      />\r\n                    </div>\r\n                    <div className=\"mb-3\">\r\n                      <label className=\"block text-sm font-medium text-gray-700\">Longitude</label>\r\n                      <Input\r\n                        className=\"w-full\"\r\n                        name=\"longitude\"\r\n                        defaultValue={editingArea?.longitude}\r\n                        required={!!editingArea?.radius} // Make required if radius is present\r\n                      />\r\n                    </div>\r\n                  </>}\r\n                <div className=\"mb-3\">\r\n                  <label className=\"block text-sm font-medium text-gray-700\">\r\n                    State\r\n                  </label>\r\n                  <Select name=\"state\" defaultValue={editingArea?.state}>\r\n                    <SelectTrigger className=\"w-full mt-1\">\r\n                      <SelectValue placeholder=\"Select state\" />\r\n                    </SelectTrigger>\r\n                    <SelectContent>\r\n                      <SelectItem value=\"state\">States</SelectItem>\r\n                      {states.map((state) => (\r\n                        <SelectItem key={state} value={state}>\r\n                          {state}\r\n                        </SelectItem>\r\n                      ))}\r\n                    </SelectContent>\r\n                  </Select>\r\n                </div>\r\n                <div className=\"mb-3\">\r\n                  <label className=\"block text-sm font-medium text-gray-700\">\r\n                    District\r\n                  </label>\r\n                  <Select\r\n                    name=\"district\"\r\n                    value={selectedDistrict}\r\n                    onValueChange={(value) => {\r\n                      setSelectedDistrict(value);\r\n                      setSearchParams({ state: selectedState, district: value });\r\n                    }}\r\n                    disabled={selectedState === \"all\"}\r\n                  >\r\n                    <SelectTrigger className=\"w-1/2\">\r\n                      <SelectValue placeholder=\"Select district\" />\r\n                    </SelectTrigger>\r\n                    <SelectContent>\r\n                      <SelectItem value=\"all\">All Districts</SelectItem>\r\n                      {selectedState !== \"all\" &&\r\n                        districts[selectedState]?.map((d) => (\r\n                          <SelectItem key={d} value={d}>\r\n                            {d}\r\n                          </SelectItem>\r\n                        ))}\r\n                    </SelectContent>\r\n                  </Select>\r\n                </div>\r\n                <div className=\"flex justify-end space-x-3 mt-4\">\r\n                  <Button\r\n                    type=\"button\"\r\n                    variant=\"outline\"\r\n                    onClick={() => setRadiusMode(false)}\r\n                  >\r\n                    Cancel\r\n                  </Button>\r\n                  <Button type=\"submit\">\r\n                    {editingArea?.id === 0 ? \"Create\" : \"Save\"}\r\n                  </Button>\r\n                </div>\r\n              </Form>\r\n\r\n            </div>}\r\n\r\n\r\n\r\n          {/* Top-right inline edit panel */}\r\n          {editingArea && (\r\n            <div className=\"absolute top-4 right-4 w-80 bg-white p-4 shadow-lg rounded z-50\">\r\n              <h3 className=\"text-lg font-semibold mb-2\">\r\n                {editingArea.id === 0 ? \"Create New Area\" : \"Edit Area\"}\r\n              </h3>\r\n              {actionData?.error && (\r\n                <p className=\"text-red-500 mb-2\">{actionData.error}</p>\r\n              )}\r\n              <Form method=\"post\">\r\n                <input type=\"hidden\" name=\"id\" value={editingArea.id} />\r\n                <input type=\"hidden\" name=\"polygon\" value={editingPolygon} />\r\n                <div className=\"mb-3\">\r\n                  <label className=\"block text-sm font-medium text-gray-700\">\r\n                    Name\r\n                  </label>\r\n                  <Input\r\n                    className=\"w-full\"\r\n                    name=\"name\"\r\n                    defaultValue={editingArea.name}\r\n                  />\r\n                </div>\r\n                <div className=\"mb-3\">\r\n                  <label className=\"block text-sm font-medium text-gray-700\">\r\n                    State\r\n                  </label>\r\n                  <Select name=\"state\" defaultValue={editingArea.state}>\r\n                    <SelectTrigger className=\"w-full mt-1\">\r\n                      <SelectValue placeholder=\"Select state\" />\r\n                    </SelectTrigger>\r\n                    <SelectContent>\r\n                      {states.map((state) => (\r\n                        <SelectItem key={state} value={state}>\r\n                          {state}\r\n                        </SelectItem>\r\n                      ))}\r\n                    </SelectContent>\r\n                  </Select>\r\n                </div>\r\n                <div className=\"mb-3\">\r\n                  <label className=\"block text-sm font-medium text-gray-700\">\r\n                    District\r\n                  </label>\r\n                  <Select name=\"district\" defaultValue={editingArea.district}>\r\n                    <SelectTrigger className=\"w-full mt-1\">\r\n                      <SelectValue placeholder=\"Select district\" />\r\n                    </SelectTrigger>\r\n                    <SelectContent>\r\n                      {editingArea.state &&\r\n                        districts[editingArea.state]?.map((d) => (\r\n                          <SelectItem key={d} value={d}>\r\n                            {d}\r\n                          </SelectItem>\r\n                        ))}\r\n                    </SelectContent>\r\n                  </Select>\r\n                </div>\r\n                <div className=\"flex justify-end space-x-3 mt-4\">\r\n                  <Button\r\n                    type=\"button\"\r\n                    variant=\"outline\"\r\n                    onClick={handleCancelEdit}\r\n                  >\r\n                    Cancel\r\n                  </Button>\r\n                  <Button type=\"submit\">\r\n                    {editingArea.id === 0 ? \"Create\" : \"Save\"}\r\n                  </Button>\r\n                </div>\r\n              </Form>\r\n            </div>\r\n          )}\r\n        </div>\r\n      </div >\r\n    </LoadScript >\r\n  );\r\n}\r\n\r\n// When not in editing mode, clicking on a polygon shows an info window.\r\nfunction handlePolygonClick(\r\n  e: google.maps.MapMouseEvent,\r\n  area: SellerArea\r\n) {\r\n  if (e.latLng && area) {\r\n    const position = e.latLng.toJSON();\r\n    (window as any).setInfoWindow &&\r\n      (window as any).setInfoWindow({\r\n        position,\r\n        content: `${area.id} - ${area.name}`\r\n      });\r\n  }\r\n}"], "names": ["BANGALORE_CENTER", "lat", "lng", "MAP_CONTAINER_STYLE", "width", "height", "getPolygonColor", "index", "colors", "length", "LocalitiesSection", "location", "useLocation", "useNavigate", "searchParams", "setSearchParams", "useSearchParams", "actionData", "useActionData", "areas", "initialAreas", "googleMapsApiKey", "states", "districts", "useLoaderData", "localAreas", "setLocalAreas", "useState", "useEffect", "selectedState", "setSelectedState", "get", "selectedDistrict", "setSelectedDistrict", "visible<PERSON><PERSON><PERSON>", "setVisibleAreas", "Set", "map", "setMap", "mapLoaded", "setMapLoaded", "searchTerm", "setSearchTerm", "sortOrder", "setSortOrder", "editingArea", "setEditingArea", "editingPolygon", "setEditingPolygon", "selectedVertexIndex", "setSelectedVertexIndex", "infoWindow", "setInfoWindow", "drawingMode", "setDrawingMode", "radiusMode", "setRadiusMode", "polygonRefs", "useRef", "initialVisible", "filter", "a", "polygon", "id", "onLoadMap", "useCallback", "mapInstance", "onUnmountMap", "fitBounds", "size", "bounds", "google", "maps", "LatLngBounds", "for<PERSON>ach", "area", "has", "decodePolygon", "extend", "coord", "toggleAreaVisibility", "areaId", "prev", "newSet", "delete", "add", "setAllVisible", "allWithPolygons", "setNoneVisible", "filtered<PERSON><PERSON>s", "useMemo", "matchesSearch", "name", "toLowerCase", "includes", "matchesState", "state", "matchesDistrict", "district", "polygonpresent", "sort", "b", "localeCompare", "handleSort", "pathname", "endsWith", "Outlet", "handleAdd", "handleRadiusAdd", "poly", "path", "<PERSON><PERSON><PERSON>", "coords", "i", "<PERSON><PERSON><PERSON><PERSON>", "latLng", "getAt", "push", "coordsArray", "deduped", "deduplicateCoordinates", "encoded", "polylineCodec", "newArea", "startEditing", "handlePolygonLoad", "current", "handlePolygonEdit", "newPath", "set<PERSON>ath", "curr", "listener1", "event", "addListener", "listener2", "removeListener", "handleKeyDown", "e", "key", "removeAt", "window", "addEventListener", "removeEventListener", "handlePolygonClick", "closestIndex", "minDist", "Infinity", "vertex", "d", "Math", "sqrt", "pow", "threshold", "position", "toJSON", "content", "handleCancelEdit", "jsx", "LoadScript", "libraries", "children", "jsxs", "className", "<PERSON><PERSON>", "onClick", "Select", "value", "onValueChange", "SelectTrigger", "SelectValue", "placeholder", "SelectContent", "SelectItem", "disabled", "Input", "onChange", "target", "variant", "ArrowUpDown", "isEditing", "style", "backgroundColor", "opacity", "Eye", "Eye<PERSON>ff", "Pencil", "Form", "method", "onSubmit", "confirm", "preventDefault", "type", "Trash2", "GoogleMap", "mapContainerStyle", "center", "zoom", "onLoad", "onUnmount", "options", "styles", "featureType", "elementType", "stylers", "visibility", "mapTypeControl", "streetViewControl", "fullscreenControl", "polygonPath", "polygonOptions", "fillColor", "fillOpacity", "strokeColor", "strokeOpacity", "strokeWeight", "clickable", "editable", "Polygon", "paths", "onMouseUp", "onDragEnd", "DrawingManager", "drawingControl", "drawing", "OverlayType", "POLYGON", "zIndex", "onPolygonComplete", "InfoWindow", "headerDisabled", "disableAutoPan", "X", "readOnly", "defaultValue", "radius", "Fragment", "latitude", "required", "longitude", "error"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA,MAAM,SAAS,iBAAiB,UAAU;AAAA,EACxC;AAAA,IACE;AAAA,IACA;AAAA,MACE,GAAG;AAAA,MACH,KAAK;AAAA,IACX;AAAA,EACG;AAAA,EACD,CAAC,QAAQ,EAAE,GAAG,wCAAwC,KAAK,SAAQ,CAAE;AAAA,EACrE;AAAA,IACE;AAAA,IACA;AAAA,MACE,GAAG;AAAA,MACH,KAAK;AAAA,IACX;AAAA,EACG;AAAA,EACD,CAAC,QAAQ,EAAE,GAAG,cAAc,KAAK,SAAU,CAAA;AAC7C,CAAC;ACmED,IAAI,SAAS,SAAU,MAAM,WAAW;AACpC,MAAI,cAAc,QAAQ;AAAE,gBAAY;AAAA,EAAE;AAC1C,MAAI,SAAS,KAAK,IAAI,IAAI,SAAS;AACnC,MAAI,YAAY,SAAS,cAAc,QAAQ;AAC3C,QAAI,CAAC,MAAM,QAAQ,MAAM,GAAG;AACxB,eAAS,CAAC,OAAO,KAAK,OAAO,GAAG;AAAA,IAC5C;AACQ,WAAO,CAAC,MAAM,OAAO,CAAC,IAAI,MAAM,GAAG,MAAM,OAAO,CAAC,IAAI,MAAM,CAAC;AAAA,EAC/D;AACD,SAAO,mBAAmB,MAAM,SAAS;AAC7C;AAOA,IAAI,qBAAqB,SAAU,OAAO,WAAW;AACjD,MAAI,IAAI,CAAE;AACV,MAAI,QAAQ,CAAC,GAAG,CAAC;AACjB,MAAI;AACJ,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,IAAI,GAAG,EAAE,GAAG;AAG1C,UAAM,UAAU,MAAM,CAAC,CAAC;AAExB,yBAAqB,MAAM,IAAI,CAAC,CAAC,IAAI,MAAM,MAAM,CAAC,CAAC,GAAG,CAAC;AACvD,yBAAqB,MAAM,IAAI,CAAC,CAAC,IAAI,MAAM,MAAM,CAAC,CAAC,GAAG,CAAC;AACvD,YAAQ;AAAA,EAChB;AACI,SAAO,EAAE,KAAK,EAAE;AACpB;AAOA,IAAI,uBAAuB,SAAU,OAAO,OAAO;AAC/C,SAAO,uBAAuB,QAAQ,IAAI,EAAE,SAAS,KAAK,SAAS,GAAG,KAAK;AAC/E;AAMA,IAAI,yBAAyB,SAAU,OAAO,OAAO;AACjD,SAAO,SAAS,IAAM;AAClB,UAAM,KAAK,OAAO,cAAc,KAAQ,QAAQ,MAAS,EAAE,CAAC;AAC5D,cAAU;AAAA,EAClB;AACI,QAAM,KAAK,OAAO,aAAa,QAAQ,EAAE,CAAC;AAC1C,SAAO;AACX;AAIA,IAAI,QAAQ,SAAU,GAAG;AACrB,SAAO,KAAK,MAAM,KAAK,IAAI,CAAC,IAAI,GAAG,KAAK,KAAK,IAAI,IAAI;AACzD;AC8CA,MAAMA,mBAAmB;AAAA,EAAEC,KAAK;AAAA,EAASC,KAAK;AAAQ;AACtD,MAAMC,sBAAsB;AAAA,EAAEC,OAAO;AAAA,EAAQC,QAAQ;AAAO;AAE5D,SAASC,gBAAgBC,OAAe;AACtC,QAAMC,SAAS,CACb,WACA,WACA,WACA,WACA,WACA,WACA,WACA,SAAA;AAEK,SAAAA,OAAOD,QAAQC,OAAOC,MAAM;AACrC;AAGA,SAAwBC,oBAAoB;;AAC1C,QAAMC,WAAWC,YAAY;AACZC,cAAY;AAC7B,QAAM,CAACC,cAAcC,eAAe,IAAIC,gBAAgB;AACxD,QAAMC,aAAaC,cAAkC;AAErD,QAAM;AAAA,IAAEC,OAAOC;AAAAA,IAAcC;AAAAA,IAAkBC;AAAAA,IAAQC;AAAAA,MACrDC,cAA0B;AAE5B,QAAM,CAACC,YAAYC,aAAa,IAAIC,aAAAA,SAAuBP,YAAY;AACvEQ,eAAAA,UAAU,MAAM;AACdF,kBAAcN,YAAY;AAAA,EAC5B,GAAG,CAACA,YAAY,CAAC;AAEX,QAAA,CAACS,eAAeC,gBAAgB,IAAIH,aAAAA,SACxCb,aAAaiB,IAAI,OAAO,KAAK,WAC/B;AACM,QAAA,CAACC,kBAAkBC,mBAAmB,IAAIN,aAAAA,SAC9Cb,aAAaiB,IAAI,UAAU,KAAK,WAClC;AACA,QAAM,CAACG,cAAcC,eAAe,IAAIR,aAAAA,SAAsB,oBAAIS,KAAK;AACvE,QAAM,CAACC,KAAKC,MAAM,IAAIX,aAAAA,SAAiC,IAAI;AAC3D,QAAM,CAACY,WAAWC,YAAY,IAAIb,aAAAA,SAAS,KAAK;AAChD,QAAM,CAACc,YAAYC,aAAa,IAAIf,aAAAA,SAAS,EAAE;AAC/C,QAAM,CAACgB,WAAWC,YAAY,IAAIjB,aAAAA,SAAyB,KAAK;AAGhE,QAAM,CAACkB,aAAaC,cAAc,IAAInB,aAAAA,SAA4B,IAAI;AAEtE,QAAM,CAACoB,gBAAgBC,iBAAiB,IAAIrB,aAAAA,SAAiB,EAAE;AAG/D,QAAM,CAACsB,qBAAqBC,sBAAsB,IAAIvB,aAAAA,SAAwB,IAAI;AAGlF,QAAM,CAACwB,YAAYC,aAAa,IAAIzB,aAAAA,SAG1B,IAAI;AAGd,QAAM,CAAC0B,aAAaC,cAAc,IAAI3B,aAAAA,SAAkB,KAAK;AAC7D,QAAM,CAAC4B,YAAYC,aAAa,IAAI7B,aAAAA,SAAkB,KAAK;AAIrD,QAAA8B,cAAcC,aAAkD,OAAA,EAAE;AAExE9B,eAAAA,UAAU,MAAM;AACd,QAAIW,WAAW;AACb,YAAMoB,iBAAiB,IAAIvB,IACzBX,WAAWmC,OAAYC,OAAAA,EAAEC,OAAO,EAAEzB,IAASwB,OAAAA,EAAEE,EAAE,CACjD;AACA5B,sBAAgBwB,cAAc;AAAA,IAChC;AAAA,EACF,GAAG,CAACpB,WAAWd,UAAU,CAAC;AAEpB,QAAAuC,YAAYC,aAAY,YAACC,iBAAiC;AAC9D5B,WAAO4B,WAAW;AAClB1B,iBAAa,IAAI;AAAA,EACnB,GAAG,EAAE;AAEC,QAAA2B,eAAeF,aAAAA,YAAY,MAAM;AACrC3B,WAAO,IAAI;AACXE,iBAAa,KAAK;AAAA,EACpB,GAAG,EAAE;AAEC,QAAA4B,YAAYH,aAAAA,YAAY,MAAM;AAC9B,QAAA5B,OAAOH,aAAamC,OAAO,GAAG;AAChC,YAAMC,SAAS,IAAIC,OAAOC,KAAKC,aAAa;AAC5ChD,iBAAWiD,QAAgBC,UAAA;AACzB,YAAIzC,aAAa0C,IAAID,KAAKZ,EAAE,KAAKY,KAAKb,SAAS;AAC/Be,wBAAAF,KAAKb,OAAO,EAAEY,mBAAiBJ,OAAOQ,OAAOC,KAAK,CAAC;AAAA,QACnE;AAAA,MACF,CAAC;AACD1C,UAAI+B,UAAUE,MAAM;AAAA,IACtB;AAAA,EACC,GAAA,CAACjC,KAAKH,cAAcT,UAAU,CAAC;AAElCG,eAAAA,UAAU,MAAM;AACd,QAAIW,WAAW;AACH6B,gBAAA;AAAA,IACZ;AAAA,EACF,GAAG,CAACA,WAAW7B,SAAS,CAAC;AAEnB,QAAAyC,uBAAuBf,aAAY,YAACgB,YAAmB;AAC3D9C,oBAAwB+C,UAAA;AAChB,YAAAC,SAAS,IAAI/C,IAAI8C,IAAI;AACvB,UAAAC,OAAOP,IAAIK,MAAM,GAAG;AACtBE,eAAOC,OAAOH,MAAM;AAAA,MACtB,OAAO;AACLE,eAAOE,IAAIJ,MAAM;AAAA,MACnB;AACO,aAAAE;AAAAA,IACT,CAAC;AAAA,EACH,GAAG,EAAE;AAELvD,eAAAA,UAAU,MAAM;AACJwC,cAAA;AAAA,EACZ,GAAG,CAAClC,cAAckC,SAAS,CAAC;AAE5B,QAAMkB,gBAAgBA,MAAM;AAC1B,UAAMC,kBAAkB,IAAInD,IAC1BX,WAAWmC,OAAYC,OAAAA,EAAEC,OAAO,EAAEzB,IAASwB,OAAAA,EAAEE,EAAE,CACjD;AACA5B,oBAAgBoD,eAAe;AAAA,EACjC;AACA,QAAMC,iBAAiBA,MAAMrD,gBAAgB,oBAAIC,KAAK;AAEhD,QAAAqD,gBAAgBC,aAAAA,QAAQ,MAAM;AAC3B,WAAAjE,WACJmC,OAAee,UAAA;AACR,YAAAgB,gBAAgBhB,KAAKiB,KACxBC,cACAC,SAASrD,WAAWoD,aAAa;AACpC,YAAME,eACJlE,kBAAkB,SAAS8C,KAAKqB,UAAUnE;AAC5C,YAAMoE,kBACJjE,qBAAqB,SAAS2C,KAAKuB,aAAalE;AAClD,YAAMmE,iBAAiBxB,KAAKb;AACrB,aAAA6B,iBAAiBI,gBAAgBE,mBAAmBE;AAAAA,IAC7D,CAAC,EACAC,KAAK,CAACvC,GAAGwC,MACR1D,cAAc,QACVkB,EAAE+B,KAAKU,cAAcD,EAAET,IAAI,IAC3BS,EAAET,KAAKU,cAAczC,EAAE+B,IAAI,CACjC;AAAA,EACJ,GAAG,CAACnE,YAAYgB,YAAYZ,eAAeG,kBAAkBW,SAAS,CAAC;AAEvE,QAAM4D,aAAaA,MAAM;AACV3D,iBAAAD,cAAc,QAAQ,SAAS,KAAK;AAAA,EACnD;AAEA,MACEhC,SAAS6F,SAASC,SAAS,MAAM,KACjC9F,SAAS6F,SAASC,SAAS,SAAS,KACpC9F,SAAS6F,SAASV,SAAS,OAAO,GAClC;AACA,iDAAQY,QAAO,EAAA;AAAA,EACjB;AAKA,QAAMC,YAAYA,MAAM;AACtBrD,mBAAe,IAAI;AAEnBR,mBAAe,IAAI;AACnBE,sBAAkB,EAAE;AAAA,EACtB;AACA,QAAM4D,kBAAkBA,MAAM;AAC5BpD,kBAAc,IAAI;AAAA,EAEpB;AAI0BS,eAAAA,YACvB4C,UAA8B;AAE7BA,SAAKvE,OAAO,IAAI;AACV,UAAAwE,OAAOD,KAAKE,QAAQ;AAC1B,UAAMC,SAAyC,CAAC;AAChD,aAASC,IAAI,GAAGA,IAAIH,KAAKI,UAAA,GAAaD,KAAK;AACnC,YAAAE,SAASL,KAAKM,MAAMH,CAAC;AACpBD,aAAAK,KAAK;AAAA,QAAEpH,KAAKkH,OAAOlH;QAAOC,KAAKiH,OAAOjH,IAAI;AAAA,MAAE,CAAC;AAAA,IACtD;AACM,UAAAoH,cAAcN,OAAO3E,IAAI,CAAC;AAAA,MAAEpC;AAAAA,MAAKC;AAAAA,IAAI,MAAM,CAACD,KAAKC,GAAG,CAAC;AACrD,UAAAqH,UAAUC,wBAAuBF,WAAW;AAC5C,UAAAG,UAAUC,OAAqBH,OAAO;AAE5C,UAAMI,UAAsB;AAAA,MAC1B5D,IAAI;AAAA,MACJ6B,MAAM;AAAA,MACNI,OAAOnE,kBAAkB,QAAQ,KAAKA;AAAAA,MACtCqE,UAAUlE,qBAAqB,QAAQ,KAAKA;AAAAA,MAC5C8B,SAAS2D;AAAAA,IACX;AACA/F,kBAAsBwD,UAAA,CAAC,GAAGA,MAAMyC,OAAO,CAAC;AACxC7E,mBAAe6E,OAAO;AACtB3E,sBAAkByE,OAAO;AACzBnE,mBAAe,KAAK;AAAA,EACtB,GACA,CAACzB,eAAeG,gBAAgB,CAClC;AAGM,QAAA4F,eAAgBjD,UAAqB;AACzC7B,mBAAe6B,IAAI;AACD3B,sBAAA2B,KAAKb,WAAW,EAAE;AACpCZ,2BAAuB,IAAI;AAAA,EAC7B;AAGA,QAAM2E,oBAAoB5D,aAAAA,YACxB,CAACH,SAA8BmB,WAAmB;AACpCxB,gBAAAqE,QAAQ7C,MAAM,IAAInB;AAAAA,EAChC,GACA,EACF;AAGA,QAAMiE,oBAAoB9D,aAAA,YACvBgB,YAAmB;AACZ,UAAA4B,OAAOpD,YAAYqE,QAAQ7C,MAAM;AACvC,QAAI,CAAC4B,KAAM;AACL,UAAAC,OAAOD,KAAKE,QAAQ;AAC1B,UAAMC,SAAyC,CAAC;AAChD,aAASC,IAAI,GAAGA,IAAIH,KAAKI,UAAA,GAAaD,KAAK;AACnC,YAAAE,SAASL,KAAKM,MAAMH,CAAC;AACpBD,aAAAK,KAAK;AAAA,QAAEpH,KAAKkH,OAAOlH;QAAOC,KAAKiH,OAAOjH,IAAI;AAAA,MAAE,CAAC;AAAA,IACtD;AACM,UAAAoH,cAAcN,OAAO3E,IAAI,CAAC;AAAA,MAAEpC;AAAAA,MAAKC;AAAAA,IAAI,MAAM,CAACD,KAAKC,GAAG,CAAC;AACrD,UAAAqH,UAAUC,wBAAuBF,WAAW;AAC9C,QAAAC,QAAQ9G,SAAS6G,YAAY7G,QAAQ;AACjC,YAAAuH,UAAUT,QAAQlF,IAAI,CAAC,CAACpC,KAAKC,GAAG,OAAO;AAAA,QAAED;AAAAA,QAAKC;AAAAA,MAAI,EAAE;AAC1D2G,WAAKoB,QAAQD,OAAO;AAAA,IACtB;AACM,UAAAP,UAAUC,OAAqBH,OAAO;AAC5CvE,sBAAkByE,OAAO;AAAA,EAC3B,GACA,EACF;AAGA,WAASD,wBAAuBR,QAAgC;AAC1D,QAAAA,OAAOvG,WAAW,EAAU,QAAAuG;AAChC,UAAMO,UAAU,CAACP,OAAO,CAAC,CAAC;AAC1B,aAASC,IAAI,GAAGA,IAAID,OAAOvG,QAAQwG,KAAK;AACtC,YAAM/B,OAAOqC,QAAQA,QAAQ9G,SAAS,CAAC;AACjC,YAAAyH,OAAOlB,OAAOC,CAAC;AACjB,UAAA/B,KAAK,CAAC,MAAMgD,KAAK,CAAC,KAAKhD,KAAK,CAAC,MAAMgD,KAAK,CAAC,EAAG;AAChDX,cAAQF,KAAKa,IAAI;AAAA,IACnB;AACO,WAAAX;AAAAA,EACT;AAGA3F,eAAAA,UAAU,MAAM;AACd,QAAIiB,aAAa;AACf,YAAMgE,OAAOpD,YAAYqE,QAAQjF,YAAYkB,EAAE;AAC/C,UAAI8C,MAAM;AACF,cAAAC,OAAOD,KAAKE,QAAQ;AAC1B,cAAMoB,YAAY5D,OAAOC,KAAK4D,MAAMC,YAAYvB,MAAM,UAAU,MAAM;AACpEiB,4BAAkBlF,YAAYkB,EAAE;AAAA,QAClC,CAAC;AACD,cAAMuE,YAAY/D,OAAOC,KAAK4D,MAAMC,YAAYvB,MAAM,aAAa,MAAM;AACvEiB,4BAAkBlF,YAAYkB,EAAE;AAAA,QAClC,CAAC;AACD,eAAO,MAAM;AACJQ,iBAAAC,KAAK4D,MAAMG,eAAeJ,SAAS;AACnC5D,iBAAAC,KAAK4D,MAAMG,eAAeD,SAAS;AAAA,QAC5C;AAAA,MACF;AAAA,IACF;AAAA,EACF,GAAG,CAACzF,aAAakF,iBAAiB,CAAC;AAGnCnG,eAAAA,UAAU,MAAM;AACd,aAAS4G,cAAcC,GAAkB;AAEpC,WAAAA,EAAEC,QAAQ,YAAYD,EAAEC,QAAQ,gBACjC7F,gBAAgB,QAChBI,wBAAwB,MACxB;AACA,cAAM4D,OAAOpD,YAAYqE,QAAQjF,YAAYkB,EAAE;AAC/C,YAAI8C,MAAM;AACF,gBAAAC,OAAOD,KAAKE,QAAQ;AACtB,cAAAD,KAAKI,UAAU,IAAI,GAAG;AACxBJ,iBAAK6B,SAAS1F,mBAAmB;AACjC8E,8BAAkBlF,YAAYkB,EAAE;AAChCb,mCAAuB,IAAI;AAAA,UAC7B;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,QAAIL,aAAa;AACR+F,aAAAC,iBAAiB,WAAWL,aAAa;AAAA,IAClD;AACA,WAAO,MAAM;AACJI,aAAAE,oBAAoB,WAAWN,aAAa;AAAA,IACrD;AAAA,EACC,GAAA,CAAC3F,aAAaI,qBAAqB8E,iBAAiB,CAAC;AAGxD,QAAMgB,sBAAqB9E,aAAAA,YACzB,CAACwE,GAA8B9D,SAAqB;AAClD,QAAI9B,eAAeA,YAAYkB,OAAOY,KAAKZ,MAAM0E,EAAEtB,QAAQ;AACzD,YAAMN,OAAOpD,YAAYqE,QAAQnD,KAAKZ,EAAE;AACxC,UAAI8C,MAAM;AACF,cAAAC,OAAOD,KAAKE,QAAQ;AAC1B,YAAIiC,eAA8B;AAClC,YAAIC,UAAUC;AACd,iBAASjC,IAAI,GAAGA,IAAIH,KAAKI,UAAA,GAAaD,KAAK;AACnC,gBAAAkC,SAASrC,KAAKM,MAAMH,CAAC;AAC3B,gBAAMmC,IAAIC,KAAKC,KACbD,KAAKE,IAAIJ,OAAOlJ,QAAQwI,EAAEtB,OAAOlH,OAAO,CAAC,IACzCoJ,KAAKE,IAAIJ,OAAOjJ,IAAI,IAAIuI,EAAEtB,OAAOjH,OAAO,CAAC,CAC3C;AACA,cAAIkJ,IAAIH,SAAS;AACLA,sBAAAG;AACKJ,2BAAA/B;AAAAA,UACjB;AAAA,QACF;AACA,cAAMuC,YAAY;AAClB,YAAIP,UAAUO,WAAW;AACvBtG,iCAAuB8F,YAAY;AAAA,QACrC,OAAO;AACL9F,iCAAuB,IAAI;AAAA,QAC7B;AAAA,MACF;AAAA,IACF,OAAO;AAED,UAAAuF,EAAEtB,UAAUxC,MAAM;AACNvB,sBAAA;AAAA,UACZqG,UAAUhB,EAAEtB,OAAOuC,OAAO;AAAA,UAC1BC,SAAS,GAAGhF,KAAKZ,EAAE,MAAMY,KAAKiB,IAAI;AAAA,QACpC,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF,GACA,CAAC/C,WAAW,CACd;AAEA,QAAM+G,mBAAmBA,MAAM;AAC7B9G,mBAAe,IAAI;AACnBE,sBAAkB,EAAE;AACpBE,2BAAuB,IAAI;AAAA,EAC7B;AAME,SAAA2G,kCAAAA,IAACC,YAAA;AAAA,IACCzI;AAAAA,IACA0I,WAAW,CAAC,SAAS;AAAA,IAErBC,UAAAC,kCAAA,KAAC,OAAI;AAAA,MAAAC,WAAU;AAAA,MAEbF,UAAA,CAACC,kCAAA,KAAA,OAAA;AAAA,QAAIC,WAAU;AAAA,QACbF,UAAA,CAACC,kCAAA,KAAA,OAAA;AAAA,UAAIC,WAAU;AAAA,UACbF,UAAA,CAACC,kCAAA,KAAA,OAAA;AAAA,YAAIC,WAAU;AAAA,YACbF,UAAA,CAACH,kCAAA,IAAA,MAAA;AAAA,cAAGK,WAAU;AAAA,cAAsCF,UAEpD;AAAA,YAAA,CAAA,GACAC,kCAAA,KAAC,OAAI;AAAA,cAAAC,WAAU;AAAA,cACbF,UAAA,CAAAH,kCAAA,IAACM,QAAO;AAAA,gBAAAC,SAASzD;AAAAA,gBAAWtC,MAAK;AAAA,gBAAK2F,UACT;AAAA,cAAA,CAAA,yCAC5BG,QAAO;AAAA,gBAAAC,SAASxD;AAAAA,gBAAiBvC,MAAK;AAAA,gBAAK2F,UAE5C;AAAA,cAAA,CAAA,CAAA;AAAA,YACF,CAAA,CAAA;AAAA,UACF,CAAA,GAEAC,kCAAA,KAAC,OAAI;AAAA,YAAAC,WAAU;AAAA,YACbF,UAAA,CAAAC,kCAAA,KAACI,QAAA;AAAA,cACCC,OAAOzI;AAAAA,cACP0I,eAAgBD,WAAU;AACxBxI,iCAAiBwI,KAAK;AACtBrI,oCAAoB,EAAE;AACtBlB,gCAAgB;AAAA,kBAAEiF,OAAOsE;AAAAA,kBAAOpE,UAAU;AAAA,gBAAG,CAAC;AAAA,cAChD;AAAA,cAEA8D,UAAA,CAAAH,kCAAA,IAACW;gBAAcN,WAAU;AAAA,gBACvBF,gDAACS,aAAY;AAAA,kBAAAC,aAAY;AAAA,gBAAe,CAAA;AAAA,cAC1C,CAAA,0CACCC,eACC;AAAA,gBAAAX,UAAA,CAACH,kCAAA,IAAAe,YAAA;AAAA,kBAAWN,OAAM;AAAA,kBAAMN,UAAU;AAAA,gBAAA,CAAA,GACjC1I,OAAOe,IAAK2D,WACX6D,kCAAAA,IAACe;kBAAuBN,OAAOtE;AAAAA,kBAC5BgE,UADchE;AAAAA,gBAAA,GAAAA,KAEjB,CACD,CAAA;AAAA,cACH,CAAA,CAAA;AAAA,YAAA,CACF,GACAiE,kCAAA,KAACI,QAAA;AAAA,cACCC,OAAOtI;AAAAA,cACPuI,eAAgBD,WAAU;AACxBrI,oCAAoBqI,KAAK;AACzBvJ,gCAAgB;AAAA,kBAAEiF,OAAOnE;AAAAA,kBAAeqE,UAAUoE;AAAAA,gBAAM,CAAC;AAAA,cAC3D;AAAA,cACAO,UAAUhJ,kBAAkB;AAAA,cAE5BmI,UAAA,CAAAH,kCAAA,IAACW;gBAAcN,WAAU;AAAA,gBACvBF,gDAACS,aAAY;AAAA,kBAAAC,aAAY;AAAA,gBAAkB,CAAA;AAAA,cAC7C,CAAA,0CACCC,eACC;AAAA,gBAAAX,UAAA,CAACH,kCAAA,IAAAe,YAAA;AAAA,kBAAWN,OAAM;AAAA,kBAAMN,UAAa;AAAA,gBAAA,CAAA,GACpCnI,kBAAkB,WACjBN,eAAUM,aAAa,MAAvBN,mBAA0Bc,IAAK+G,OAC7BS,kCAAA,IAACe,YAAmB;AAAA,kBAAAN,OAAOlB;AAAAA,kBACxBY,UAAAZ;AAAAA,gBAAA,GADcA,CAEjB,GACD;AAAA,cACL,CAAA,CAAA;AAAA,YAAA,CACF,CAAA;AAAA,UACF,CAAA,GACAa,kCAAA,KAAC,OAAI;AAAA,YAAAC,WAAU;AAAA,YACbF,UAAA,CAAAH,kCAAA,IAACiB,OAAA;AAAA,cACCJ,aAAY;AAAA,cACZJ,OAAO7H;AAAAA,cACPsI,UAAWtC,OAAM/F,cAAc+F,EAAEuC,OAAOV,KAAK;AAAA,cAC7CJ,WAAU;AAAA,YAAA,CACZ,GACAD,kCAAA,KAAC,OAAI;AAAA,cAAAC,WAAU;AAAA,cACbF,UAAA,CAAAH,kCAAA,IAACM,QAAA;AAAA,gBACCC,SAAS5E;AAAAA,gBACTyF,SAAQ;AAAA,gBACR5G,MAAK;AAAA,gBACN2F,UAAA;AAAA,cAAA,CAED,GACAH,kCAAA,IAACM,QAAA;AAAA,gBACCC,SAAS9E;AAAAA,gBACT2F,SAAQ;AAAA,gBACR5G,MAAK;AAAA,gBACN2F,UAAA;AAAA,cAAA,CAED,CAAA;AAAA,YACF,CAAA,CAAA;AAAA,UACF,CAAA,CAAA;AAAA,QACF,CAAA,yCACC,OAAI;AAAA,UAAAE,WAAU;AAAA,UACbF,UAACC,kCAAA,KAAA,SAAA;AAAA,YAAMC,WAAU;AAAA,YACfF,UAAA,CAAAH,kCAAA,IAAC,SAAM;AAAA,cAAAK,WAAU;AAAA,cACfF,UAAAC,kCAAA,KAAC,MACC;AAAA,gBAAAD,UAAA,CAACH,kCAAA,IAAA,MAAA;AAAA,kBAAGK,WAAU;AAAA,kBACZF,UAAAC,kCAAA,KAACE,QAAA;AAAA,oBACCc,SAAQ;AAAA,oBACRb,SAAS7D;AAAAA,oBACT2D,WAAU;AAAA,oBACXF,UAAA,CAAA,QAEEH,kCAAA,IAAAqB,aAAA;AAAA,sBAAY7G,MAAM;AAAA,sBAAI6F,WAAU;AAAA,oBAAO,CAAA,CAAA;AAAA,kBAC1C,CAAA;AAAA,gBACF,CAAA,GACCL,kCAAA,IAAA,MAAA;AAAA,kBAAGK,WAAU;AAAA,kBAAmFF,UAEjG;AAAA,gBAAA,CAAA,CAAA;AAAA,cAEF,CAAA;AAAA,YACF,CAAA,GACAH,kCAAA,IAAC;cAAMK,WAAU;AAAA,cACdF,wBAAc3H,IAAI,CAACsC,MAAMpE,UAAU;AAC5B,sBAAA4K,aAAYtI,2CAAakB,QAAOY,KAAKZ;AAEzC,uBAAAkG,kCAAAA,KAAC,MAAA;AAAA,kBAECC,WAAW,oBAAoBiB,YAAY,eAAe,EACxD;AAAA,kBAEFnB,UAAA,CAAAH,kCAAA,IAAC;oBAAGK,WAAU;AAAA,oBACZF,UAACC,kCAAA,KAAA,OAAA;AAAA,sBAAIC,WAAU;AAAA,sBACbF,UAAA,CAAAH,kCAAA,IAAC,OAAA;AAAA,wBACCK,WAAU;AAAA,wBACVkB,OAAO;AAAA,0BACLC,iBAAiB/K,gBAAgBC,KAAK;AAAA,0BACtC+K,SAASpJ,aAAa0C,IAAID,KAAKZ,EAAE,IAAI,IAAI;AAAA,wBAC3C;AAAA,sBAAA,CACF,GACAkG,kCAAA,KAAC,QAAK;AAAA,wBAAAC,WAAU;AAAA,wBACbF,UAAA,CAAKrF,KAAAiB,MACL,CAACjB,KAAKb,iDACJ,QAAK;AAAA,0BAAAoG,WAAU;AAAA,0BAA6BF,UAE7C;AAAA,wBAAA,CAAA,CAAA;AAAA,sBAEJ,CAAA,CAAA;AAAA,oBACF,CAAA;AAAA,kBACF,CAAA,GACAC,kCAAA,KAAC,MAAG;AAAA,oBAAAC,WAAU;AAAA,oBACZF,UAAA,CAAAH,kCAAA,IAACM,QAAA;AAAA,sBACCc,SAAQ;AAAA,sBACR5G,MAAK;AAAA,sBACL+F,SAASA,MAAMpF,qBAAqBL,KAAKZ,EAAE;AAAA,sBAC3C8G,UAAU,CAAClG,KAAKb;AAAAA,sBAEfkG,UAAa9H,aAAA0C,IAAID,KAAKZ,EAAE,IACvB8F,kCAAA,IAAC0B,KAAI;AAAA,wBAAAlH,MAAM;AAAA,sBAAI,CAAA,IAEdwF,kCAAA,IAAA2B,QAAA;AAAA,wBAAOnH,MAAM;AAAA,sBAAI,CAAA;AAAA,oBAAA,CAEtB,GAEAwF,kCAAA,IAACM,QAAA;AAAA,sBACCc,SAAQ;AAAA,sBACR5G,MAAK;AAAA,sBACL6F,WAAU;AAAA,sBACVE,SAASA,MAAMxC,aAAajD,IAAI;AAAA,sBAEhCqF,UAAAH,kCAAA,IAAC4B,QAAO;AAAA,wBAAApH,MAAM;AAAA,sBAAI,CAAA;AAAA,oBAAA,CACpB,0CAECqH,MAAK;AAAA,sBAAAC,QAAO;AAAA,sBAAOC,UAAWnD,OAAM;AAC/B,4BAAA,CAACoD,QAAQ,4CAA4C,GAAG;AAC1DpD,4BAAEqD,eAAe;AAAA,wBACnB;AAAA,sBAEA;AAAA,sBAAA9B,UAAA,CAAAH,kCAAA,IAAC;wBAAMkC,MAAK;AAAA,wBAASnG,MAAK;AAAA,wBAAK0E,OAAO3F,KAAKZ;AAAAA,sBAAI,CAAA,yCAC9C,SAAM;AAAA,wBAAAgI,MAAK;AAAA,wBAASnG,MAAK;AAAA,wBAAS0E,OAAM;AAAA,sBAAS,CAAA,GACjDT,kCAAA,IAAAM,QAAA;AAAA,wBAAO4B,MAAK;AAAA,wBAASd,SAAQ;AAAA,wBAAQ5G,MAAK;AAAA,wBAAK6F,WAAU;AAAA,wBACxDF,UAAAH,kCAAA,IAACmC,QAAO;AAAA,0BAAA3H,MAAM;AAAA,wBAAI,CAAA;AAAA,sBACpB,CAAA,CAAA;AAAA,oBACF,CAAA,CAAA;AAAA,kBACF,CAAA,CAAA;AAAA,gBAAA,GAzDKM,KAAKZ,EA0DZ;AAAA,cAEH,CAAA;AAAA,YACH,CAAA,CAAA;AAAA,UACF,CAAA;AAAA,QACF,CAAA,CAAA;AAAA,MACF,CAAA,GAGAkG,kCAAA,KAAC,OAAI;AAAA,QAAAC,WAAU;AAAA,QACbF,UAAA,CAAAC,kCAAA,KAACgC,WAAA;AAAA,UACCC,mBAAmB/L;AAAAA,UACnBgM,QAAQnM;AAAAA,UACRoM,MAAM;AAAA,UACNC,QAAQrI;AAAAA,UACRsI,WAAWnI;AAAAA,UACXoI,SAAS;AAAA,YACPC,QAAQ,CACN;AAAA,cACEC,aAAa;AAAA,cACbC,aAAa;AAAA,cACbC,SAAS,CAAC;AAAA,gBAAEC,YAAY;AAAA,cAAM,CAAA;AAAA,YAChC,CAAA;AAAA,YAEFC,gBAAgB;AAAA,YAChBC,mBAAmB;AAAA,YACnBC,mBAAmB;AAAA,UACrB;AAAA,UAEC/C,UAAA,CAAAzH,aACCd,WAAWY,IAAI,CAACsC,MAAMpE,UAAU;AAC1B,gBAAA,CAACoE,KAAKb,WAAW,CAAC5B,aAAa0C,IAAID,KAAKZ,EAAE,EAAU,QAAA;AAElD,kBAAAoH,aAAYtI,2CAAakB,QAAOY,KAAKZ;AACrC,kBAAAiJ,cACJ7B,aAAapI,iBACT8B,cAAc9B,cAAc,IAC5B8B,cAAcF,KAAKb,OAAO;AAEhC,kBAAMmJ,iBAAiB9B,YACnB;AAAA,cACA+B,WAAW5M,gBAAgBC,KAAK;AAAA,cAChC4M,aAAa;AAAA,cACbC,aAAa;AAAA,cACbC,eAAe;AAAA,cACfC,cAAc;AAAA,cACdC,WAAW;AAAA,cACXC,UAAU;AAAA,YACZ,IACE;AAAA,cACAN,WAAW5M,gBAAgBC,KAAK;AAAA,cAChC4M,aAAa;AAAA,cACbC,aAAa9M,gBAAgBC,KAAK;AAAA,cAClC8M,eAAe;AAAA,cACfC,cAAc;AAAA,cACdC,WAAW;AAAA,cACXC,UAAU;AAAA,YACZ;AAGA,mBAAA3D,kCAAAA,IAAC4D,SAAA;AAAA,cAECC,OAAOV;AAAAA,cACPT,SAASU;AAAAA,cACTZ,QAASxF,UAASgB,kBAAkBhB,MAAMlC,KAAKZ,EAAE;AAAA,cACjDqG,SAAU3B,OAAMM,oBAAmBN,GAAG9D,IAAI;AAAA,cAC1CgJ,WAAWA,MAAM5F,kBAAkBpD,KAAKZ,EAAE;AAAA,cAC1C6J,WAAWA,MAAM7F,kBAAkBpD,KAAKZ,EAAE;AAAA,YAAA,GANrCY,KAAKZ,EAOZ;AAAA,WAEH,GAGFV,eACCwG,kCAAA,IAACgE,gBAAA;AAAA,YACCtB,SAAS;AAAA,cACPuB,gBAAgB;AAAA,cAChBzK,aACEuF,OAAOrE,OAAOC,KAAKuJ,QAAQC,YAAYC;AAAAA,cACzChB,gBAAgB;AAAA,gBACdC,WAAW;AAAA,gBACXC,aAAa;AAAA,gBACbG,cAAc;AAAA,gBACdC,WAAW;AAAA,gBACXC,UAAU;AAAA,gBACVU,QAAQ;AAAA,cACV;AAAA,YACF;AAAA,YACAC,mBAAoBtH,UAAS;AAC3BA,mBAAKvE,OAAO,IAAI;AACV,oBAAAwE,OAAOD,KAAKE,QAAQ;AAC1B,oBAAMC,SAAyC,CAAC;AAChD,uBAASC,IAAI,GAAGA,IAAIH,KAAKI,UAAA,GAAaD,KAAK;AACnC,sBAAAE,SAASL,KAAKM,MAAMH,CAAC;AACpBD,uBAAAK,KAAK;AAAA,kBAAEpH,KAAKkH,OAAOlH;kBAAOC,KAAKiH,OAAOjH,IAAI;AAAA,gBAAE,CAAC;AAAA,cACtD;AACM,oBAAAoH,cAAcN,OAAO3E,IAAI,CAAC;AAAA,gBAAEpC;AAAAA,gBAAKC;AAAAA,cAAI,MAAM,CAACD,KAAKC,GAAG,CAAC;AACrD,oBAAAqH,UAAUC,wBAAuBF,WAAW;AAC5C,oBAAAG,UAAUC,OAAqBH,OAAO;AAC5C,oBAAMI,UAAsB;AAAA,gBAC1B5D,IAAI;AAAA,gBACJ6B,MAAM;AAAA,gBACNI,OAAOnE,kBAAkB,QAAQ,KAAKA;AAAAA,gBACtCqE,UACElE,qBAAqB,QAAQ,KAAKA;AAAAA,gBACpC8B,SAAS2D;AAAAA,cACX;AACA/F,4BAAewD,UAAS,CAAC,GAAGA,MAAMyC,OAAO,CAAC;AAC1C7E,6BAAe6E,OAAO;AACtB3E,gCAAkByE,OAAO;AACzBnE,6BAAe,KAAK;AAAA,YACtB;AAAA,WACF,GAIDH,cACC0G,kCAAA,IAACuE,YAAA;AAAA,YACC3E,UAAUtG,WAAWsG;AAAAA,YAErB8C,SAAS;AAAA,cACP8B,gBAAgB;AAAA,cAChBC,gBAAgB;AAAA,YAClB;AAAA,YAGAtE,UAAAC,kCAAA,KAAC,OAAI;AAAA,cAAAC,WAAU;AAAA,cACbF,UAAA,CAACC,kCAAA,KAAA,OAAA;AAAA,gBAAIC,WAAU;AAAA,gBACbF,UAAA,CAACH,kCAAA,IAAA,MAAA;AAAA,kBAAGK,WAAU;AAAA,kBAA4CF,UAAa;AAAA,gBAAA,CAAA,GACvEH,kCAAA,IAAC,UAAA;AAAA,kBAAOK,WAAU;AAAA,kBAChBE,SAASA,MAAMhH,cAAc,IAAI;AAAA,kBACjC4G,UAAAH,kCAAA,IAAC0E,GAAE;AAAA,oBAAArE,WAAU;AAAA,kBAAU,CAAA;AAAA,gBAAA,CACzB,CAAA;AAAA,cACF,CAAA,GACCL,kCAAA,IAAA,OAAA;AAAA,gBAAIK,WAAU;AAAA,gBACbF,iDAAC,KAAE;AAAA,kBAAAA,UAAA,CAAA,KAAE7G,WAAWwG,OAAA;AAAA,gBAAQ,CAAA;AAAA,cAC1B,CAAA,CAAA;AAAA,YACF,CAAA;AAAA,UAAA,CACF,CAAA;AAAA,SAEJ,GAGEpG,oDACC,OAAI;AAAA,UAAA2G,WAAU;AAAA,UAEbF,UAACC,kCAAA,KAAAyB,MAAA;AAAA,YAAKC,QAAO;AAAA,YACX3B,UAAA,CAAAH,kCAAA,IAAC;cAAMkC,MAAK;AAAA,cAASnG,MAAK;AAAA,cAAK0E,OAAOzH,2CAAakB;AAAAA,YAAI,CAAA,GACvD8F,kCAAA,IAAC;cAAMkC,MAAK;AAAA,cAASnG,MAAK;AAAA,cAAO0E,OAAO;AAAA,cAAgBkE,UAAQ;AAAA,YAAC,CAAA,GACjEvE,kCAAA,KAAC,OAAI;AAAA,cAAAC,WAAU;AAAA,cACbF,UAAA,CAACH,kCAAA,IAAA,SAAA;AAAA,gBAAMK,WAAU;AAAA,gBAA0CF,UAE3D;AAAA,cAAA,CAAA,GACAH,kCAAA,IAACiB,OAAA;AAAA,gBACCZ,WAAU;AAAA,gBACVtE,MAAK;AAAA,gBACL6I,cAAc5L,2CAAa+C;AAAAA,cAAA,CAC7B,CAAA;AAAA,YACF,CAAA,GACAqE,kCAAA,KAAC,OAAI;AAAA,cAAAC,WAAU;AAAA,cACbF,UAAA,CAACH,kCAAA,IAAA,SAAA;AAAA,gBAAMK,WAAU;AAAA,gBAA0CF,UAAM;AAAA,cAAA,CAAA,GACjEH,kCAAA,IAACiB,OAAA;AAAA,gBACCZ,WAAU;AAAA,gBACVtE,MAAK;AAAA,gBACL6I,cAAc5L,2CAAa6L;AAAAA,cAAA,CAC7B,CAAA;AAAA,YACF,CAAA,IACC7L,2CAAa6L,YAAW,QAErBzE,kCAAAA,KAAA0E,kBAAAA,UAAA;AAAA,cAAA3E,UAAA,CAACC,kCAAA,KAAA,OAAA;AAAA,gBAAIC,WAAU;AAAA,gBACbF,UAAA,CAACH,kCAAA,IAAA,SAAA;AAAA,kBAAMK,WAAU;AAAA,kBAA0CF,UAAQ;AAAA,gBAAA,CAAA,GACnEH,kCAAA,IAACiB,OAAA;AAAA,kBACCZ,WAAU;AAAA,kBACVtE,MAAK;AAAA,kBACL6I,cAAc5L,2CAAa+L;AAAAA,kBAC3BC,UAAU,CAAC,EAAChM,2CAAa6L;AAAAA,gBAAA,CAC3B,CAAA;AAAA,cACF,CAAA,GACAzE,kCAAA,KAAC,OAAI;AAAA,gBAAAC,WAAU;AAAA,gBACbF,UAAA,CAACH,kCAAA,IAAA,SAAA;AAAA,kBAAMK,WAAU;AAAA,kBAA0CF,UAAS;AAAA,gBAAA,CAAA,GACpEH,kCAAA,IAACiB,OAAA;AAAA,kBACCZ,WAAU;AAAA,kBACVtE,MAAK;AAAA,kBACL6I,cAAc5L,2CAAaiM;AAAAA,kBAC3BD,UAAU,CAAC,EAAChM,2CAAa6L;AAAAA,gBAAA,CAC3B,CAAA;AAAA,cACF,CAAA,CAAA;AAAA,YACF,CAAA,GACFzE,kCAAA,KAAC,OAAI;AAAA,cAAAC,WAAU;AAAA,cACbF,UAAA,CAACH,kCAAA,IAAA,SAAA;AAAA,gBAAMK,WAAU;AAAA,gBAA0CF,UAE3D;AAAA,cAAA,CAAA,0CACCK,QAAO;AAAA,gBAAAzE,MAAK;AAAA,gBAAQ6I,cAAc5L,2CAAamD;AAAAA,gBAC9CgE,UAAA,CAAAH,kCAAA,IAACW;kBAAcN,WAAU;AAAA,kBACvBF,gDAACS,aAAY;AAAA,oBAAAC,aAAY;AAAA,kBAAe,CAAA;AAAA,gBAC1C,CAAA,0CACCC,eACC;AAAA,kBAAAX,UAAA,CAACH,kCAAA,IAAAe,YAAA;AAAA,oBAAWN,OAAM;AAAA,oBAAQN,UAAM;AAAA,kBAAA,CAAA,GAC/B1I,OAAOe,IAAK2D,WACX6D,kCAAAA,IAACe;oBAAuBN,OAAOtE;AAAAA,oBAC5BgE,UADchE;AAAAA,kBAAA,GAAAA,KAEjB,CACD,CAAA;AAAA,gBACH,CAAA,CAAA;AAAA,cACF,CAAA,CAAA;AAAA,YACF,CAAA,GACAiE,kCAAA,KAAC,OAAI;AAAA,cAAAC,WAAU;AAAA,cACbF,UAAA,CAACH,kCAAA,IAAA,SAAA;AAAA,gBAAMK,WAAU;AAAA,gBAA0CF,UAE3D;AAAA,cAAA,CAAA,GACAC,kCAAA,KAACI,QAAA;AAAA,gBACCzE,MAAK;AAAA,gBACL0E,OAAOtI;AAAAA,gBACPuI,eAAgBD,WAAU;AACxBrI,sCAAoBqI,KAAK;AACzBvJ,kCAAgB;AAAA,oBAAEiF,OAAOnE;AAAAA,oBAAeqE,UAAUoE;AAAAA,kBAAM,CAAC;AAAA,gBAC3D;AAAA,gBACAO,UAAUhJ,kBAAkB;AAAA,gBAE5BmI,UAAA,CAAAH,kCAAA,IAACW;kBAAcN,WAAU;AAAA,kBACvBF,gDAACS,aAAY;AAAA,oBAAAC,aAAY;AAAA,kBAAkB,CAAA;AAAA,gBAC7C,CAAA,0CACCC,eACC;AAAA,kBAAAX,UAAA,CAACH,kCAAA,IAAAe,YAAA;AAAA,oBAAWN,OAAM;AAAA,oBAAMN,UAAa;AAAA,kBAAA,CAAA,GACpCnI,kBAAkB,WACjBN,eAAUM,aAAa,MAAvBN,mBAA0Bc,IAAK+G,OAC7BS,kCAAA,IAACe,YAAmB;AAAA,oBAAAN,OAAOlB;AAAAA,oBACxBY,UAAAZ;AAAAA,kBAAA,GADcA,CAEjB,GACD;AAAA,gBACL,CAAA,CAAA;AAAA,cAAA,CACF,CAAA;AAAA,YACF,CAAA,GACAa,kCAAA,KAAC,OAAI;AAAA,cAAAC,WAAU;AAAA,cACbF,UAAA,CAAAH,kCAAA,IAACM,QAAA;AAAA,gBACC4B,MAAK;AAAA,gBACLd,SAAQ;AAAA,gBACRb,SAASA,MAAM5G,cAAc,KAAK;AAAA,gBACnCwG,UAAA;AAAA,cAAA,CAED,GACAH,kCAAA,IAACM;gBAAO4B,MAAK;AAAA,gBACV/B,sDAAajG,QAAO,IAAI,WAAW;AAAA,cACtC,CAAA,CAAA;AAAA,YACF,CAAA,CAAA;AAAA,UACF,CAAA;AAAA,SAEF,GAKDlB,eACCoH,kCAAA,KAAC,OAAI;AAAA,UAAAC,WAAU;AAAA,UACbF,UAAA,CAAAH,kCAAA,IAAC;YAAGK,WAAU;AAAA,YACXF,sBAAYjG,OAAO,IAAI,oBAAoB;AAAA,UAC9C,CAAA,IACC9C,yCAAY8N,UACXlF,kCAAAA,IAAC;YAAEK,WAAU;AAAA,YAAqBF,qBAAW+E;AAAAA,UAAM,CAAA,GAErD9E,kCAAA,KAACyB,MAAK;AAAA,YAAAC,QAAO;AAAA,YACX3B,UAAA,CAAAH,kCAAA,IAAC;cAAMkC,MAAK;AAAA,cAASnG,MAAK;AAAA,cAAK0E,OAAOzH,YAAYkB;AAAAA,YAAI,CAAA,yCACrD,SAAM;AAAA,cAAAgI,MAAK;AAAA,cAASnG,MAAK;AAAA,cAAU0E,OAAOvH;AAAAA,YAAgB,CAAA,GAC3DkH,kCAAA,KAAC,OAAI;AAAA,cAAAC,WAAU;AAAA,cACbF,UAAA,CAACH,kCAAA,IAAA,SAAA;AAAA,gBAAMK,WAAU;AAAA,gBAA0CF,UAE3D;AAAA,cAAA,CAAA,GACAH,kCAAA,IAACiB,OAAA;AAAA,gBACCZ,WAAU;AAAA,gBACVtE,MAAK;AAAA,gBACL6I,cAAc5L,YAAY+C;AAAAA,cAAA,CAC5B,CAAA;AAAA,YACF,CAAA,GACAqE,kCAAA,KAAC,OAAI;AAAA,cAAAC,WAAU;AAAA,cACbF,UAAA,CAACH,kCAAA,IAAA,SAAA;AAAA,gBAAMK,WAAU;AAAA,gBAA0CF,UAE3D;AAAA,cAAA,CAAA,0CACCK,QAAO;AAAA,gBAAAzE,MAAK;AAAA,gBAAQ6I,cAAc5L,YAAYmD;AAAAA,gBAC7CgE,UAAA,CAAAH,kCAAA,IAACW;kBAAcN,WAAU;AAAA,kBACvBF,gDAACS,aAAY;AAAA,oBAAAC,aAAY;AAAA,kBAAe,CAAA;AAAA,gBAC1C,CAAA,GACCb,kCAAA,IAAAc,eAAA;AAAA,kBACEX,UAAO1I,OAAAe,IAAK2D,WACV6D,kCAAAA,IAAAe,YAAA;AAAA,oBAAuBN,OAAOtE;AAAAA,oBAC5BgE,UADchE;AAAAA,kBAAA,GAAAA,KAEjB,CACD;AAAA,gBACH,CAAA,CAAA;AAAA,cACF,CAAA,CAAA;AAAA,YACF,CAAA,GACAiE,kCAAA,KAAC,OAAI;AAAA,cAAAC,WAAU;AAAA,cACbF,UAAA,CAACH,kCAAA,IAAA,SAAA;AAAA,gBAAMK,WAAU;AAAA,gBAA0CF,UAE3D;AAAA,cAAA,CAAA,0CACCK,QAAO;AAAA,gBAAAzE,MAAK;AAAA,gBAAW6I,cAAc5L,YAAYqD;AAAAA,gBAChD8D,UAAA,CAAAH,kCAAA,IAACW;kBAAcN,WAAU;AAAA,kBACvBF,gDAACS,aAAY;AAAA,oBAAAC,aAAY;AAAA,kBAAkB,CAAA;AAAA,gBAC7C,CAAA,yCACCC,eACE;AAAA,kBAAAX,UAAAnH,YAAYmD,WACXzE,eAAUsB,YAAYmD,KAAK,MAA3BzE,mBAA8Bc,IAAK+G,6CAChCwB,YAAmB;AAAA,oBAAAN,OAAOlB;AAAAA,oBACxBY,UADcZ;AAAAA,kBAAA,GAAAA,CAEjB;AAAA,gBAEN,CAAA,CAAA;AAAA,cACF,CAAA,CAAA;AAAA,YACF,CAAA,GACAa,kCAAA,KAAC,OAAI;AAAA,cAAAC,WAAU;AAAA,cACbF,UAAA,CAAAH,kCAAA,IAACM,QAAA;AAAA,gBACC4B,MAAK;AAAA,gBACLd,SAAQ;AAAA,gBACRb,SAASR;AAAAA,gBACVI,UAAA;AAAA,cAAA,CAED,GACAH,kCAAA,IAACM;gBAAO4B,MAAK;AAAA,gBACV/B,sBAAYjG,OAAO,IAAI,WAAW;AAAA,cACrC,CAAA,CAAA;AAAA,YACF,CAAA,CAAA;AAAA,UACF,CAAA,CAAA;AAAA,QACF,CAAA,CAAA;AAAA,MAEJ,CAAA,CAAA;AAAA,IACF,CAAA;AAAA,EAAA,CACF;AAEJ;", "x_google_ignoreList": [0, 1]}