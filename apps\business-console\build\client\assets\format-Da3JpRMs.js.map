{"version": 3, "file": "format-Da3JpRMs.js", "sources": ["../../../app/utils/format.ts"], "sourcesContent": ["function formatIndianNumber(value: number): string {\r\n  // Format numbers with commas in the Indian system\r\n  return value.toLocaleString(\"en-IN\");\r\n}\r\n\r\nexport function formatWeight(weight: number) {\r\n  // if (weight % 1 === 0) {\r\n  //     return weight.toString();\r\n  // } else {\r\n  //     return weight.toFixed(2);\r\n  // }\r\n  if (weight >= 1000) {\r\n    // Convert to metric tons\r\n    return `${(weight / 1000).toFixed(1)} Ton`;\r\n  } else if (weight >= 1) {\r\n    // Keep in kilograms\r\n    return `${formatIndianNumber(weight)} Kg`;\r\n  } else {\r\n    // Convert to grams (for fractional kg values)\r\n    return `${(weight * 1000).toFixed(2)} g`;\r\n  }\r\n}\r\n\r\nexport function formatCurrency(currency: number) {\r\n  // if(currency % 1 === 0) {\r\n  //     return currency.toString();\r\n  // }else {\r\n  //     return currency.toFixed(2);\r\n  // }\r\n  if (currency >= 10000000) {\r\n    // Convert to crores\r\n    return `₹ ${(currency / 10000000).toFixed(1)} Cr`;\r\n  } else if (currency >= 100000) {\r\n    // Convert to lakhs\r\n    return `₹ ${(currency / 100000).toFixed(1)} L`;\r\n    // } else if (currency >= 1000) {\r\n    //     // Convert to thousands\r\n    //     return `₹${(currency / 1000).toFixed(2)} K`;\r\n  } else {\r\n    // Rupees with commas\r\n    return `₹ ${formatIndianNumber(currency)}`;\r\n  }\r\n}\r\n\r\nexport function camelCaseToWords(text: string) {\r\n  return text\r\n    .replace(/([A-Z])/g, \" $1\")\r\n    .replace(/^./, (str) => str.toUpperCase());\r\n}\r\n"], "names": [], "mappings": "AAAA,SAAS,mBAAmB,OAAuB;AAE1C,SAAA,MAAM,eAAe,OAAO;AACrC;AAEO,SAAS,aAAa,QAAgB;AAM3C,MAAI,UAAU,KAAM;AAElB,WAAO,IAAI,SAAS,KAAM,QAAQ,CAAC,CAAC;AAAA,EAAA,WAC3B,UAAU,GAAG;AAEf,WAAA,GAAG,mBAAmB,MAAM,CAAC;AAAA,EAAA,OAC/B;AAEL,WAAO,IAAI,SAAS,KAAM,QAAQ,CAAC,CAAC;AAAA,EAAA;AAExC;AAEO,SAAS,eAAe,UAAkB;AAM/C,MAAI,YAAY,KAAU;AAExB,WAAO,MAAM,WAAW,KAAU,QAAQ,CAAC,CAAC;AAAA,EAAA,WACnC,YAAY,KAAQ;AAE7B,WAAO,MAAM,WAAW,KAAQ,QAAQ,CAAC,CAAC;AAAA,EAAA,OAIrC;AAEE,WAAA,KAAK,mBAAmB,QAAQ,CAAC;AAAA,EAAA;AAE5C;AAEO,SAAS,iBAAiB,MAAc;AACtC,SAAA,KACJ,QAAQ,YAAY,KAAK,EACzB,QAAQ,MAAM,CAAC,QAAQ,IAAI,YAAA,CAAa;AAC7C;"}