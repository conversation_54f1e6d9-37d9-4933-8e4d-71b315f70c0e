import { r as reactExports, j as jsxRuntimeExports } from "./jsx-runtime-bB2y7OuD.js";
import { T as Tabs, a as <PERSON><PERSON>List, b as <PERSON><PERSON><PERSON>rigger, c as TabsContent } from "./tabs-CfSdyzWr.js";
import { L as LoadScript, G as GoogleMap, P as Polygon, I as InfoWindow, M as Marker, d as decodePolygon } from "./polyline-utils-DkQLXyiU.js";
import { R as ResponsiveTable } from "./responsiveTable-CMOWL3Wl.js";
import { I as Input } from "./input-3v87qohQ.js";
import { B as Button } from "./button-ByAXMyvk.js";
import { u as useToast } from "./ToastProvider-rciVe3-g.js";
import { c as cn } from "./utils-GkgzjW3c.js";
import { C as ChevronDown } from "./chevron-down-pCP5jmjX.js";
import { C as Check } from "./check-_dbWxIzT.js";
import { L as Label } from "./label-cSASrwzW.js";
import { D as Dialog, a as DialogContent, b as DialogTitle } from "./dialog-BqKosxNq.js";
import { S as SpinnerLoader } from "./SpinnerLoader-CAZRmIJX.js";
import { a as useFetcher, u as useLoaderData } from "./components-D7UvGag_.js";
import { R as RadioGroup, a as RadioGroupItem, C as Circle } from "./radio-group-ChzooXbR.js";
import { X } from "./x-CCG_WJDF.js";
import { S as SquarePen } from "./square-pen-BXxSi9JH.js";
import { S as Select, a as SelectTrigger, b as SelectValue, c as SelectContent, d as SelectItem } from "./select-BFGSXKcr.js";
import { C as Checkbox } from "./checkbox-DoRUSdrQ.js";
import { T as Trash2 } from "./trash-2-DjkfFIB-.js";
import { S as Switch } from "./switch-DQ_qW6ch.js";
import { S as Save } from "./save-xzNIILKr.js";
import { R as ResponsivePagination } from "./responsivePagination-D-iSBEkA.js";
import { u as useDebounce } from "./useDebounce-BXbH_IFZ.js";
import { a as useNavigate, O as Outlet } from "./index-DhHTcibu.js";
import { T as Trash } from "./trash-cZnr6Uhr.js";
import { P as Pencil } from "./pencil-C1goHmP8.js";
import { T as TooltipProvider, a as Tooltip, b as TooltipTrigger, c as TooltipContent } from "./tooltip-CmSNYR5K.js";
import "./index-D7VH9Fc8.js";
import "./index-CG37gmC0.js";
import "./index-qCtcpOcW.js";
import "./index-z_byfFrQ.js";
import "./index-DVTNuYOr.js";
import "./index-CzZ6EYkN.js";
import "./index-Vp2vNLNM.js";
import "./index-dIGjFc0I.js";
import "./index-BTdCMChR.js";
import "./index-QLGF6kQx.js";
import "./index-ImHKLo0a.js";
import "./createLucideIcon-uwkRm45G.js";
import "./index-DdafHWkt.js";
import "./index-BXzPK7u0.js";
import "./index-CEHS9zzk.js";
import "./index-CkL5tk39.js";
import "./index-CpKiYcZd.js";
import "./index-IXOTxK3N.js";
import "./index-DscYByPT.js";
import "./index-C88PRvfd.js";
const MultiSelect = ({
  options,
  selectedValues,
  onChange,
  placeholder = "Select...",
  className,
  height
}) => {
  var _a, _b;
  const [isOpen, setIsOpen] = reactExports.useState(false);
  const toggleValue = (value) => {
    onChange(
      (selectedValues == null ? void 0 : selectedValues.includes(value)) ? selectedValues.filter((v) => v !== value) : [...selectedValues, value]
    );
  };
  return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "relative w-full", children: [
    /* @__PURE__ */ jsxRuntimeExports.jsxs(
      "button",
      {
        type: "button",
        onClick: () => setIsOpen(!isOpen),
        className: cn(
          "flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
          className
        ),
        children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "truncate", children: (selectedValues == null ? void 0 : selectedValues.length) > 2 ? `${(_a = options == null ? void 0 : options.find((opt) => (opt == null ? void 0 : opt.value) === selectedValues[0])) == null ? void 0 : _a.label}, ${(_b = options == null ? void 0 : options.find((opt) => (opt == null ? void 0 : opt.value) === selectedValues[1])) == null ? void 0 : _b.label} +${(selectedValues == null ? void 0 : selectedValues.length) - 2} more` : selectedValues.map((value) => {
            var _a2;
            return (_a2 = options == null ? void 0 : options.find((opt) => (opt == null ? void 0 : opt.value) === value)) == null ? void 0 : _a2.label;
          }).join(", ") || placeholder }),
          /* @__PURE__ */ jsxRuntimeExports.jsx(ChevronDown, { className: "h-4 w-4 opacity-50" })
        ]
      }
    ),
    isOpen && /* @__PURE__ */ jsxRuntimeExports.jsx(
      "div",
      {
        className: cn(
          "absolute z-50 mt-1 w-full max-h-60 overflow-y-auto rounded-md border bg-white shadow-md",
          className
        ),
        onWheel: (e) => e.stopPropagation(),
        onTouchMove: (e) => e.stopPropagation(),
        children: options == null ? void 0 : options.map((option) => /* @__PURE__ */ jsxRuntimeExports.jsxs(
          "div",
          {
            className: cn(
              "flex cursor-pointer items-center px-4 py-2 text-sm hover:bg-gray-100",
              (selectedValues == null ? void 0 : selectedValues.includes(option == null ? void 0 : option.value)) && "bg-gray-200"
            ),
            onClick: () => toggleValue(option.value),
            children: [
              (selectedValues == null ? void 0 : selectedValues.includes(option.value)) && /* @__PURE__ */ jsxRuntimeExports.jsx(Check, { className: "h-4 w-4 text-primary mr-2" }),
              option == null ? void 0 : option.label
            ]
          },
          option == null ? void 0 : option.value
        ))
      }
    )
  ] });
};
function CreateUser({
  isOpen,
  onClose,
  sellerId,
  roles,
  user,
  sellerBId
}) {
  const fetcher = useFetcher();
  const { showToast } = useToast();
  const [formData, setFormData] = reactExports.useState({
    firstName: "",
    lastName: "",
    email: "",
    mobileNumber: "",
    address: "",
    password: "",
    businessId: sellerBId,
    // Default to sellerId
    roles: []
  });
  const [selectedRoles, setSelectedRoles] = reactExports.useState([]);
  reactExports.useEffect(() => {
    if (user) {
      console.log(user, "2222222222222222222");
      setFormData({
        firstName: user == null ? void 0 : user.firstName,
        lastName: user == null ? void 0 : user.lastName,
        email: user == null ? void 0 : user.email,
        mobileNumber: user == null ? void 0 : user.mobileNumber,
        address: user == null ? void 0 : user.address,
        password: "",
        // Keep it empty when editing
        businessId: user == null ? void 0 : user.businessId,
        // Use user's business ID
        roles: user == null ? void 0 : user.roles
      });
      setSelectedRoles(user.roles);
    }
  }, [user]);
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };
  const handleSubmit = async (e) => {
    e.preventDefault();
    const userData = {
      ...formData,
      roles: selectedRoles,
      businessId: sellerBId
      // Send sellerBId as businessId
    };
    const formDataToSend = new FormData();
    console.log(sellerBId, sellerId, "999999999999999999");
    formDataToSend.append("sellerId", sellerId);
    formDataToSend.append("userId", user == null ? void 0 : user.userId);
    formDataToSend.append("intent", user ? "editUser" : "createUser");
    formDataToSend.append("userData", JSON.stringify(userData));
    fetcher.submit(formDataToSend, {
      method: "POST",
      encType: "multipart/form-data"
    });
  };
  const isLoading = fetcher.state !== "idle";
  reactExports.useEffect(() => {
    if (fetcher.data && isOpen === true) {
      if (fetcher.data) {
        showToast(user ? "User successfully updated" : "User successfully created", "success");
        onClose();
        setFormData({
          firstName: "",
          lastName: "",
          email: "",
          mobileNumber: "",
          address: "",
          password: "",
          businessId: sellerBId,
          // Default to sellerId
          roles: []
        });
      } else {
        showToast(user ? "User  update Failed" : "User creation failed ", "error");
      }
    }
  }, [fetcher.data]);
  return /* @__PURE__ */ jsxRuntimeExports.jsx(Dialog, { open: isOpen, onOpenChange: onClose, children: /* @__PURE__ */ jsxRuntimeExports.jsxs(DialogContent, { className: "max-h-[60vh] overflow-y-auto", children: [
    isLoading && /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "absolute inset-0 flex items-center justify-center bg-white/10 backdrop-blur-sm", children: /* @__PURE__ */ jsxRuntimeExports.jsx(SpinnerLoader, { loading: isLoading }) }),
    /* @__PURE__ */ jsxRuntimeExports.jsx(DialogTitle, { className: "font-bold", children: user ? "Edit User" : "Add New User" }),
    /* @__PURE__ */ jsxRuntimeExports.jsxs("form", { onSubmit: handleSubmit, className: "space-y-4 ", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex flex-col md:flex-row gap-2", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsxs(Label, { className: "flex-1 flex flex-col gap-y-2 font-semibold", children: [
          "First Name",
          /* @__PURE__ */ jsxRuntimeExports.jsx(
            Input,
            {
              name: "firstName",
              placeholder: "First Name",
              onChange: handleChange,
              value: formData.firstName,
              required: true
            }
          )
        ] }),
        /* @__PURE__ */ jsxRuntimeExports.jsxs(Label, { className: "flex-1 flex flex-col gap-y-2", children: [
          "Last Name",
          /* @__PURE__ */ jsxRuntimeExports.jsx(
            Input,
            {
              name: "lastName",
              placeholder: "Last Name",
              onChange: handleChange,
              value: formData.lastName,
              required: true
            }
          )
        ] })
      ] }),
      /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex flex-col md:flex-row gap-2", children: [
        !user && /* @__PURE__ */ jsxRuntimeExports.jsxs(Label, { className: "flex-1 flex flex-col gap-y-2", children: [
          "Email",
          /* @__PURE__ */ jsxRuntimeExports.jsx(
            Input,
            {
              type: "email",
              name: "email",
              placeholder: "Email",
              onChange: handleChange,
              value: formData.email,
              required: true
            }
          )
        ] }),
        /* @__PURE__ */ jsxRuntimeExports.jsxs(Label, { className: "flex-1 flex flex-col gap-y-2", children: [
          "Mobile Number",
          /* @__PURE__ */ jsxRuntimeExports.jsx(
            Input,
            {
              type: "number",
              name: "mobileNumber",
              placeholder: "Mobile Number",
              onChange: (e) => {
                const value = e.target.value.replace(/\D/g, "");
                if (value.length <= 10) {
                  setFormData((prev) => ({
                    ...prev,
                    mobileNumber: value
                  }));
                }
              },
              value: formData.mobileNumber,
              required: true
            }
          )
        ] })
      ] }),
      /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex flex-col md:flex-row gap-2", children: [
        !user && /* @__PURE__ */ jsxRuntimeExports.jsxs(Label, { className: "flex-1 flex flex-col gap-y-2", children: [
          "Address",
          /* @__PURE__ */ jsxRuntimeExports.jsx(
            Input,
            {
              name: "address",
              placeholder: "Address",
              onChange: handleChange,
              value: formData.address,
              required: true
            }
          )
        ] }),
        /* @__PURE__ */ jsxRuntimeExports.jsxs(Label, { className: "flex-1 flex flex-col gap-y-2", children: [
          "Select Roles",
          /* @__PURE__ */ jsxRuntimeExports.jsx(
            MultiSelect,
            {
              options: roles,
              selectedValues: selectedRoles ? selectedRoles : [],
              onChange: setSelectedRoles,
              placeholder: "Select Roles",
              className: "w-full",
              height: true
            }
          )
        ] })
      ] }),
      /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "flex justify-end", children: /* @__PURE__ */ jsxRuntimeExports.jsx(Button, { type: "submit", className: "rounded-full", children: user ? "Update User" : "Save User" }) })
    ] })
  ] }) });
}
function SellerConfigDetails({ sellerConfig, onAttributeUpdate }) {
  const [config, setConfig] = reactExports.useState(sellerConfig);
  const [error, setError] = reactExports.useState(null);
  const [isEditingOperation, setIsEditingOperation] = reactExports.useState(false);
  const [pendingOperation, setPendingOperation] = reactExports.useState({});
  const [isEditingDelivery, setIsEditingDelivery] = reactExports.useState(false);
  const [pendingDelivery, setPendingDelivery] = reactExports.useState({});
  const [isEditingBusiness, setIsEditingBusiness] = reactExports.useState(false);
  const [pendingBusiness, setPendingBusiness] = reactExports.useState({});
  const [isEditingPackaging, setIsEditingPackaging] = reactExports.useState(false);
  const [pendingPackaging, setPendingPackaging] = reactExports.useState({});
  const [isEditingInventory, setIsEditingInventory] = reactExports.useState(false);
  const [pendingInventory, setPendingInventory] = reactExports.useState({});
  reactExports.useEffect(() => {
    if (sellerConfig) {
      setConfig(sellerConfig);
    }
  }, [sellerConfig]);
  const keyMapping = {
    id: "id",
    name: "name",
    enabled: "enabled",
    auto_accept: "autoAccept",
    auto_pack: "autoPack",
    auto_pickup: "autoPickup",
    auto_dispatch: "autoDispatch",
    listing_seq: "listingSequence",
    mininum_required_balance: "minimumRequiredBalance",
    allow_cod: "allowCoD",
    minimum_order_qty: "minimumOrderQty",
    minimum_order_value: "minimumOrderValue",
    wa_enable: "waEnable",
    approx_pricing: "approxPricing",
    approxPriceVisibility: "approxPriceVisibility",
    strikeoff_enabled: "strikeoffEnabled",
    item_pick_enabled: "itemPickEnabled",
    contract_price_enabled: "contractPriceEnabled",
    delivery_type: "deliveryType",
    fav_items_enabled: "favItemsEnabled",
    category_level: "categoryLevel",
    business_id: "businessId",
    dispatch_time: "dispatchTime",
    delivery_time: "deliveryTime",
    approxDelDateVisibility: "approxDelDateVisibility",
    instantDeliveryTime: "instantDeliveryTime",
    booking_close_time: "bookingCloseTime",
    booking_open_time: "bookingOpenTime",
    is_pay_later_enabled: "isPayLaterEnabled",
    distanceBasedDel: "distanceBasedDel",
    deliveryDistance: "deliveryDistance",
    auto_activate: "autoActivate",
    advance_booking_days: "advanceBookingDays",
    miSources: "miSources",
    t1Open: "t1Open",
    t1Close: "t1Close",
    t2Open: "t2Open",
    t2Close: "t2Close",
    t3Open: "t3Open",
    t3Close: "t3Close",
    menuId: "menuId",
    pos: "pos",
    posCustId: "posCustId",
    posRetName: "posRetName",
    posRetContactNo: "posRetContactNo",
    posRetAddress: "posRetAddress",
    outletId: "outletId",
    ondcDomain: "ondcDomain",
    defaultOrderPrepTime: "defaultOrderPrepTime",
    spCustId: "spCustId",
    spId: "spId",
    sourceSystem: "sourceSystem",
    logisticProvider: "logisticProvider",
    packagingCharge: "packagingCharge",
    packagingChargeType: "packagingChargeType",
    packagingApplicableOn: "packagingApplicableOn",
    platformFee: "platformFee",
    platformFeePerc: "platformFeePerc",
    platformFeePkg: "platformFeePkg",
    sytem: "sytem"
  };
  const [validationErrors, setValidationErrors] = reactExports.useState();
  const updatePending = (section, key, value) => {
    console.log(`updatePending: section=${section}, key=${key}, value=`, value);
    if (section === "operation") {
      setPendingOperation((prev) => ({ ...prev, [key]: value }));
    } else if (section === "delivery") {
      setPendingDelivery((prev) => ({ ...prev, [key]: value }));
    } else if (section === "business") {
      setPendingBusiness((prev) => ({ ...prev, [key]: value }));
    } else if (section === "packaging") {
      setPendingPackaging((prev) => ({ ...prev, [key]: value }));
    } else if (section === "inventory") {
      setPendingInventory((prev) => ({ ...prev, [key]: value }));
    }
  };
  const basicFields = [
    { label: "ID", key: "id" },
    { label: "Name", key: "name" }
  ];
  const operationalConfigFields = [
    { label: "Are the orders required to be accepted manually?", key: "auto_accept" },
    { label: "Are the orders required to be packed manually?", key: "auto_pack" },
    { label: "Are the orders required to be picked manually?", key: "auto_pickup" },
    { label: "Are the orders required to be dispatched manually?", key: "auto_dispatch" },
    { label: "Are the items required to be picked individually?", key: "item_pick_enabled" }
  ];
  const deliveryConfigRadioFields = [
    { label: "Is Cash on delivery allowed?", key: "allow_cod" },
    { label: "Is Buy now pay later (credit) allowed?", key: "is_pay_later_enabled" },
    { label: "Distance based delivery restriction:", key: "distanceBasedDel" },
    { label: "Display Approx Delivery Date?", key: "approxDelDateVisibility" }
  ];
  const deliveryConfigOptions = [
    {
      label: "Delivery Type",
      key: "delivery_type",
      options: [
        { id: "delivery_type-daily", value: "daily", label: "🚚 Daily" },
        { id: "delivery_type-instant", value: "instant", label: "🏍️ Instant" }
      ]
    },
    {
      label: "Point of Sale",
      key: "pos",
      options: [
        { id: "pos-none", value: "none", label: "None" },
        { id: "pos-petpooja", value: "petpooja", label: "Petpooja" }
      ]
    },
    {
      label: "Source System",
      key: "sourceSystem",
      options: [
        { id: "sourceSystem-mnet", value: "MNET", label: "MNET" },
        { id: "sourceSystem-ondc", value: "ONDC", label: "ONDC" }
      ]
    },
    {
      label: " Platform",
      key: "platform",
      options: [
        { id: "platform-mNET", value: "mnet", label: "mNET" },
        { id: "platform-fM", value: "fm", label: "fM" }
      ]
    },
    {
      label: "ONDC Domain",
      key: "ondcDomain",
      options: [
        { id: "ondcDomain-ret11", value: "RET11", label: "Restaurants (RET11)" },
        { id: "ondcDomain-ret10", value: "RET10", label: "Others (RET10)" }
      ]
    }
  ];
  const businessConfigFields = [
    { label: "Business Listing Sequence:", key: "listing_seq", type: "number", prefix: "" },
    { label: "Minimum Required Balance:", key: "mininum_required_balance", type: "number", prefix: "₹" },
    { label: "Platform Fee (Fixed):", key: "platformFee", type: "number", prefix: "₹" },
    { label: "Platform Fee (%)", key: "platformFeePerc", type: "number", prefix: "%" },
    { label: "Platform Fee per Kg:", key: "platformFeePkg", type: "number", prefix: "/kg" }
  ];
  const businessConfigRadioFields = [
    { label: "Is WhatsApp Enabled?", key: "wa_enable" }
  ];
  const inventoryConfigRadioFields = [
    { label: "Is the Inventory required to be opened manually?", key: "auto_activate" },
    { label: "Is item pricing approximate?", key: "approx_pricing" },
    { label: "Display Prices?", key: "approxPriceVisibility" },
    { label: "Is item level discounting allowed?", key: "strikeoff_enabled" },
    { label: "Is item x buyer level prices fixed? (Contract Pricing)", key: "contract_price_enabled" },
    { label: "Are previously bought items recommended?", key: "fav_items_enabled" }
  ];
  const inventoryConfigFields = [
    { label: "Minimum Order Qty:", key: "minimum_order_qty", type: "number", prefix: "", suffix: "kg" },
    { label: "Advance Booking duration:", key: "advance_booking_days", type: "number", prefix: "", suffix: "days" },
    { label: "Minimum Order Value:", key: "minimum_order_value", type: "number", prefix: "₹", suffix: "" },
    { label: "MI Sources:", key: "miSources", type: "text", prefix: "", suffix: "" },
    {
      label: "Logistic Partner:",
      key: "logisticProvider",
      type: "select",
      // Change type to "select" to indicate a dropdown
      prefix: "",
      suffix: "",
      options: [
        { value: "MP2", label: "MP2" },
        { value: "SELF", label: "SELF" }
      ]
    }
  ];
  const handleSaveSection = async (section) => {
    let pending = {};
    if (section === "operation") pending = pendingOperation;
    else if (section === "delivery") pending = pendingDelivery;
    else if (section === "business") pending = pendingBusiness;
    else if (section === "packaging") pending = pendingPackaging;
    else if (section === "inventory") pending = pendingInventory;
    let errors = {};
    if (section === "delivery") {
      if (pending.pos !== void 0 && pending.pos === "petpooja") {
        if (!pending.menuId) errors.menuId = "Menu ID is required";
        if (!pending.posCustId) errors.posCustId = "POS Customer ID is required";
        if (!pending.posRetName) errors.posRetName = "POS Restaurant Name is required";
        if (!pending.posRetContactNo) errors.posRetContactNo = "POS Restaurant Contact Number is required";
        if (!pending.posRetAddress) errors.posRetAddress = "POS Restaurant Address is required";
      }
      if (pending.ondcDomain !== void 0 && pending.ondcDomain === "RET11") {
        if (!pending.defaultOrderPrepTime) errors.defaultOrderPrepTime = "Default Order Preparation Time is required";
      }
    }
    if (section === "packaging") {
      if (pending.packagingCharge === void 0 || pending.packagingCharge === null || String(pending.packagingCharge) === "") errors.packagingCharge = "Packaging Charge is required";
      if (!pending.packagingChargeType) errors.packagingChargeType = "Packaging Charge Type is required";
      if (!pending.packagingApplicableOn) errors.packagingApplicableOn = "Packaging Applicable On is required";
    }
    if (errors && Object.keys(errors).length > 0) {
      setValidationErrors(errors);
      return;
    } else {
      setValidationErrors({});
    }
    const updateArray = Object.entries(pending).filter(([_, value]) => value !== null && value !== void 0).map(([key, value]) => {
      const convertedValue = typeof value === "boolean" ? value ? true : false : value;
      return { attribute: keyMapping[key], value: convertedValue };
    });
    if (updateArray.length === 0) {
      setError("No changes to save");
      return;
    }
    console.log("Prepared update array:", updateArray);
    try {
      await onAttributeUpdate({ updates: JSON.stringify(updateArray) });
      console.log("handleSaveSection: updates sent", updateArray);
      setConfig((prev) => ({ ...prev, ...pending }));
      setError(null);
      if (section === "operation") {
        setPendingOperation({});
        setIsEditingOperation(false);
      } else if (section === "delivery") {
        setPendingDelivery({});
        setIsEditingDelivery(false);
      } else if (section === "business") {
        setPendingBusiness({});
        setIsEditingBusiness(false);
      } else if (section === "packaging") {
        setPendingPackaging({});
        setIsEditingPackaging(false);
      } else if (section === "inventory") {
        setPendingInventory({});
        setIsEditingInventory(false);
      }
    } catch (error2) {
      console.error("Error in handleSaveSection:", error2);
      setError(error2 instanceof Error ? error2.message : "Failed to save changes");
    }
  };
  const handleCancelSection = (section) => {
    if (section === "operation") {
      setPendingOperation({});
      setIsEditingOperation(false);
    } else if (section === "delivery") {
      setPendingDelivery({});
      setIsEditingDelivery(false);
    } else if (section === "business") {
      setPendingBusiness({});
      setIsEditingBusiness(false);
    } else if (section === "packaging") {
      setPendingPackaging({});
      setIsEditingPackaging(false);
    } else if (section === "inventory") {
      setPendingInventory({});
      setIsEditingInventory(false);
    }
    setError(null);
    console.log(`Cancelled editing for ${section}`);
  };
  return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex flex-col gap-4 border bg-white rounded-xl border-neutral-200 my-4 p-4", children: [
    error && /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative", role: "alert", children: /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "block sm:inline", children: error }) }),
    /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex flex-col gap-4 border rounded-xl border-neutral-400 p-4 shadow", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "text-lg font-semibold text-typography-700", children: "Basic Configurations" }),
      /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "flex flex-col gap-1", children: basicFields.map(({ label, key }) => /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "text-sm text-typography-300", children: [
        label,
        ": ",
        config[key]
      ] }, key)) })
    ] }),
    /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex flex-col gap-4 border rounded-xl border-neutral-400 p-4 shadow", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex justify-between", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "text-lg font-semibold text-typography-700", children: "Operational Configurations" }),
        /* @__PURE__ */ jsxRuntimeExports.jsx(
          "button",
          {
            onClick: () => {
              if (!isEditingOperation) {
                setPendingOperation({
                  auto_accept: config.auto_accept,
                  auto_pack: config.auto_pack,
                  auto_pickup: config.auto_pickup,
                  auto_dispatch: config.auto_dispatch,
                  item_pick_enabled: config.item_pick_enabled
                });
                console.log("Entering edit mode for Operational");
              }
              setIsEditingOperation((prev) => !prev);
            },
            className: "inline-flex items-center gap-1 text-blue-600 hover:text-blue-800",
            children: isEditingOperation ? /* @__PURE__ */ jsxRuntimeExports.jsxs(jsxRuntimeExports.Fragment, { children: [
              /* @__PURE__ */ jsxRuntimeExports.jsx(X, { className: "h-4 w-4" }),
              " Cancel"
            ] }) : /* @__PURE__ */ jsxRuntimeExports.jsxs(jsxRuntimeExports.Fragment, { children: [
              /* @__PURE__ */ jsxRuntimeExports.jsx(SquarePen, { className: "h-4 w-4" }),
              " Edit"
            ] })
          }
        )
      ] }),
      operationalConfigFields.map(({ label, key }) => {
        const k = key;
        const currentValue = isEditingOperation ? pendingOperation[k] !== void 0 ? pendingOperation[k] : config[k] : config[k];
        return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex gap-4 items-center text-md", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx(Label, { className: "w-[400px] text-md text-typography-400", children: label }),
          /* @__PURE__ */ jsxRuntimeExports.jsxs(
            RadioGroup,
            {
              value: currentValue ? ["item_pick_enabled"].includes(key) ? "yes" : "no" : ["item_pick_enabled"].includes(key) ? "no" : "yes",
              onValueChange: (val) => {
                if (isEditingOperation) {
                  if (k === "item_pick_enabled") {
                    updatePending("operation", "item_pick_enabled", val === "yes");
                    console.log(`Operational onChange: ${key} set to ${val === "yes"}`);
                  } else {
                    updatePending("operation", k, val === "no");
                    console.log(`Operational onChange: ${key} set to ${val === "no"}`);
                  }
                }
              },
              disabled: !isEditingOperation,
              className: "flex gap-4 items-center text-md font-semibold text-typography-800",
              children: [
                /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center gap-2", children: [
                  /* @__PURE__ */ jsxRuntimeExports.jsx(RadioGroupItem, { id: `${key}-yes`, value: "yes" }),
                  /* @__PURE__ */ jsxRuntimeExports.jsx(Label, { htmlFor: `${key}-yes`, children: "Yes" })
                ] }),
                /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center gap-2", children: [
                  /* @__PURE__ */ jsxRuntimeExports.jsx(RadioGroupItem, { id: `${key}-no`, value: "no" }),
                  /* @__PURE__ */ jsxRuntimeExports.jsx(Label, { htmlFor: `${key}-no`, children: "No" })
                ] })
              ]
            }
          )
        ] }, key);
      }),
      isEditingOperation && /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex gap-4 justify-center", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx("button", { onClick: () => handleCancelSection("operation"), className: "px-4 py-2 border rounded-md", children: "Cancel" }),
        /* @__PURE__ */ jsxRuntimeExports.jsx("button", { onClick: () => handleSaveSection("operation"), className: "px-6 py-2 bg-primary text-white rounded-md", children: "Save" })
      ] })
    ] }),
    /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex flex-col gap-4 border rounded-xl border-neutral-400 p-4 shadow", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex justify-between", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "text-lg font-semibold text-typography-700", children: "Delivery Configurations" }),
        /* @__PURE__ */ jsxRuntimeExports.jsx(
          "button",
          {
            onClick: () => {
              if (!isEditingDelivery) {
                setPendingDelivery({
                  allow_cod: config.allow_cod,
                  is_pay_later_enabled: config.is_pay_later_enabled,
                  distanceBasedDel: config.distanceBasedDel,
                  deliveryDistance: config.deliveryDistance,
                  delivery_type: config.delivery_type,
                  dispatch_time: config.dispatch_time,
                  delivery_time: config.delivery_time,
                  approxDelDateVisibility: config.approxDelDateVisibility,
                  instantDeliveryTime: config.instantDeliveryTime,
                  booking_open_time: config.booking_open_time,
                  booking_close_time: config.booking_close_time,
                  t1Open: config.t1Open,
                  t1Close: config.t1Close,
                  t2Open: config.t2Open,
                  t2Close: config.t2Close,
                  t3Open: config.t3Open,
                  t3Close: config.t3Close,
                  pos: config.pos,
                  menuId: config.menuId,
                  posCustId: config.posCustId,
                  posRetName: config.posRetName,
                  posRetContactNo: config.posRetContactNo,
                  posRetAddress: config.posRetAddress,
                  sourceSystem: config.sourceSystem,
                  ondcDomain: config.ondcDomain,
                  defaultOrderPrepTime: config.defaultOrderPrepTime
                });
                console.log("Entering edit mode for Delivery");
              }
              setIsEditingDelivery((prev) => !prev);
            },
            className: "inline-flex items-center gap-1 text-blue-600 hover:text-blue-800",
            children: isEditingDelivery ? /* @__PURE__ */ jsxRuntimeExports.jsxs(jsxRuntimeExports.Fragment, { children: [
              /* @__PURE__ */ jsxRuntimeExports.jsx(X, { className: "h-4 w-4" }),
              " Cancel"
            ] }) : /* @__PURE__ */ jsxRuntimeExports.jsxs(jsxRuntimeExports.Fragment, { children: [
              /* @__PURE__ */ jsxRuntimeExports.jsx(SquarePen, { className: "h-4 w-4" }),
              " Edit"
            ] })
          }
        )
      ] }),
      deliveryConfigRadioFields.map(({ label, key }) => {
        const k = key;
        const currentValue = isEditingDelivery ? pendingDelivery[k] !== void 0 ? pendingDelivery[k] : config[k] : config[k];
        return /* @__PURE__ */ jsxRuntimeExports.jsxs(jsxRuntimeExports.Fragment, { children: [
          /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex gap-4 items-center text-md", children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx(Label, { className: "w-[400px] text-md text-typography-400", children: label }),
            /* @__PURE__ */ jsxRuntimeExports.jsxs(
              RadioGroup,
              {
                value: currentValue ? "yes" : "no",
                onValueChange: (val) => {
                  if (isEditingDelivery) {
                    if (k === "distanceBasedDel" && val === "no") {
                      updatePending("delivery", "deliveryDistance", 0);
                    }
                    updatePending("delivery", k, val === "yes");
                    console.log(`Delivery onChange: ${key} set to ${val === "yes"}`);
                  }
                },
                disabled: !isEditingDelivery,
                className: "flex gap-4 items-center text-md font-semibold text-typography-800",
                children: [
                  /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center gap-2", children: [
                    /* @__PURE__ */ jsxRuntimeExports.jsx(RadioGroupItem, { id: `${key}-yes`, value: "yes" }),
                    /* @__PURE__ */ jsxRuntimeExports.jsx(Label, { htmlFor: `${key}-yes`, children: "Yes" })
                  ] }),
                  /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center gap-2", children: [
                    /* @__PURE__ */ jsxRuntimeExports.jsx(RadioGroupItem, { id: `${key}-no`, value: "no" }),
                    /* @__PURE__ */ jsxRuntimeExports.jsx(Label, { htmlFor: `${key}-no`, children: "No" })
                  ] })
                ]
              }
            )
          ] }, key),
          key === "distanceBasedDel" && currentValue === true && /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex gap-4 items-center text-md", children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx(Label, { className: "w-[400px] text-md text-typography-400", children: "Delivery Distance(km):" }),
            /* @__PURE__ */ jsxRuntimeExports.jsx(
              "input",
              {
                type: "number",
                min: 0,
                value: pendingDelivery.deliveryDistance !== void 0 ? pendingDelivery.deliveryDistance ?? "" : config.deliveryDistance ?? "",
                onKeyDown: (e) => {
                  if (e.key === "-" || e.key === "e") {
                    e.preventDefault();
                  }
                },
                onChange: (e) => {
                  const value = parseFloat(e.target.value);
                  if (value >= 0) {
                    updatePending("delivery", "deliveryDistance", value);
                    console.log(`Delivery Distance set to ${value}`);
                  }
                },
                disabled: !isEditingDelivery,
                className: "border border-neutral-400 rounded-md p-1 px-2"
              }
            )
          ] })
        ] });
      }),
      /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex gap-4 items-center text-md", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx(Label, { className: "w-[400px] text-md text-typography-400", children: "Instant/Restaurant Delivery time(mins):" }),
        /* @__PURE__ */ jsxRuntimeExports.jsx(
          "input",
          {
            type: "number",
            min: 0,
            value: pendingDelivery.instantDeliveryTime !== void 0 ? pendingDelivery.instantDeliveryTime ?? "" : config.instantDeliveryTime ?? "",
            onKeyDown: (e) => {
              if (e.key === "-" || e.key === "e") {
                e.preventDefault();
              }
            },
            onChange: (e) => {
              const value = parseFloat(e.target.value);
              updatePending("delivery", "instantDeliveryTime", value);
              console.log(`Instant Delivery Time set to ${value}`);
            },
            disabled: !isEditingDelivery,
            className: "border border-neutral-400 rounded-md p-1 px-2"
          }
        )
      ] }),
      deliveryConfigOptions.map(({ label, key, options }) => {
        const k = key;
        const currentValue = isEditingDelivery ? pendingDelivery[k] !== void 0 ? pendingDelivery[k] : config[k] : config[k];
        return /* @__PURE__ */ jsxRuntimeExports.jsxs(jsxRuntimeExports.Fragment, { children: [
          /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex gap-4 items-center text-md", children: [
            /* @__PURE__ */ jsxRuntimeExports.jsxs(Label, { className: "w-[400px] text-md text-typography-400", children: [
              label,
              ":"
            ] }),
            /* @__PURE__ */ jsxRuntimeExports.jsx(
              RadioGroup,
              {
                value: currentValue || "",
                onValueChange: (val) => {
                  if (isEditingDelivery) {
                    if (k === "pos" && val === "none") {
                      updatePending("delivery", "menuId", null);
                      updatePending("delivery", "posCustId", null);
                      updatePending("delivery", "posRetName", null);
                      updatePending("delivery", "posRetContactNo", null);
                      updatePending("delivery", "posRetAddress", null);
                    }
                    if (k === "ondcDomain" && val === "RET10") {
                      updatePending("delivery", "defaultOrderPrepTime", null);
                    }
                    updatePending("delivery", k, val);
                    console.log(`Delivery onChange: ${key} set to ${val}`);
                  }
                },
                disabled: !isEditingDelivery,
                className: "flex gap-4 items-center text-md font-semibold text-typography-800",
                children: options.map(({ id, value, label: label2 }) => /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center gap-2", children: [
                  /* @__PURE__ */ jsxRuntimeExports.jsx(RadioGroupItem, { id, value: String(value) }),
                  /* @__PURE__ */ jsxRuntimeExports.jsx(Label, { htmlFor: id, children: label2 })
                ] }, id))
              }
            )
          ] }, key),
          key === "pos" && currentValue === "petpooja" && /* @__PURE__ */ jsxRuntimeExports.jsxs(jsxRuntimeExports.Fragment, { children: [
            /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex gap-4 items-center text-md", children: [
              /* @__PURE__ */ jsxRuntimeExports.jsx(Label, { className: "w-[400px] text-md text-typography-400", children: "Petpooja Menu ID:" }),
              /* @__PURE__ */ jsxRuntimeExports.jsx(
                "input",
                {
                  type: "text",
                  value: pendingDelivery.menuId !== void 0 ? pendingDelivery.menuId ?? "" : config.menuId ?? "",
                  onChange: (e) => {
                    if (isEditingDelivery) {
                      updatePending("delivery", "menuId", e.target.value);
                      console.log(`Menu ID set to ${e.target.value}`);
                    }
                  },
                  placeholder: "eg. otsmbu6q4n",
                  disabled: !isEditingDelivery,
                  className: "border border-neutral-400 rounded-md p-1 px-2"
                }
              ),
              (validationErrors == null ? void 0 : validationErrors.menuId) && /* @__PURE__ */ jsxRuntimeExports.jsx("p", { className: "text-red-500 text-sm", children: validationErrors.menuId })
            ] }),
            /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex gap-4 items-center text-md", children: [
              /* @__PURE__ */ jsxRuntimeExports.jsx(Label, { className: "w-[400px] text-md text-typography-400", children: "Petpooja Customer ID:" }),
              /* @__PURE__ */ jsxRuntimeExports.jsx(
                "input",
                {
                  type: "text",
                  value: pendingDelivery.posCustId !== void 0 ? pendingDelivery.posCustId ?? "" : config.posCustId ?? "",
                  onChange: (e) => {
                    if (isEditingDelivery) {
                      updatePending("delivery", "posCustId", e.target.value);
                      console.log(`Pos Customer ID set to ${e.target.value}`);
                    }
                  },
                  placeholder: "eg. 4818",
                  disabled: !isEditingDelivery,
                  className: "border border-neutral-400 rounded-md p-1 px-2"
                }
              ),
              (validationErrors == null ? void 0 : validationErrors.posCustId) && /* @__PURE__ */ jsxRuntimeExports.jsx("p", { className: "text-red-500 text-sm", children: validationErrors.posCustId })
            ] }),
            /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex gap-4 items-center text-md", children: [
              /* @__PURE__ */ jsxRuntimeExports.jsx(Label, { className: "w-[400px] text-md text-typography-400", children: "Petpooja Restaurant Name:" }),
              /* @__PURE__ */ jsxRuntimeExports.jsx(
                "input",
                {
                  type: "text",
                  value: pendingDelivery.posRetName !== void 0 ? pendingDelivery.posRetName ?? "" : config.posRetName ?? "",
                  onChange: (e) => {
                    if (isEditingDelivery) {
                      updatePending("delivery", "posRetName", e.target.value);
                      console.log(`Pos Restaurant Name set to ${e.target.value}`);
                    }
                  },
                  placeholder: "",
                  disabled: !isEditingDelivery,
                  className: "border border-neutral-400 rounded-md p-1 px-2"
                }
              ),
              (validationErrors == null ? void 0 : validationErrors.posRetName) && /* @__PURE__ */ jsxRuntimeExports.jsx("p", { className: "text-red-500 text-sm", children: validationErrors.posRetName })
            ] }),
            /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex gap-4 items-center text-md", children: [
              /* @__PURE__ */ jsxRuntimeExports.jsx(Label, { className: "w-[400px] text-md text-typography-400", children: "Petpooja Restaurant Contact No:" }),
              /* @__PURE__ */ jsxRuntimeExports.jsx(
                "input",
                {
                  type: "text",
                  value: pendingDelivery.posRetContactNo !== void 0 ? pendingDelivery.posRetContactNo ?? "" : config.posRetContactNo ?? "",
                  onChange: (e) => {
                    if (isEditingDelivery) {
                      updatePending("delivery", "posRetContactNo", e.target.value);
                      console.log(`Pos Restaurant Contact No set to ${e.target.value}`);
                    }
                  },
                  placeholder: "",
                  disabled: !isEditingDelivery,
                  className: "border border-neutral-400 rounded-md p-1 px-2"
                }
              ),
              (validationErrors == null ? void 0 : validationErrors.posRetContactNo) && /* @__PURE__ */ jsxRuntimeExports.jsx("p", { className: "text-red-500 text-sm", children: validationErrors.posRetContactNo })
            ] }),
            /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex gap-4 items-center text-md", children: [
              /* @__PURE__ */ jsxRuntimeExports.jsx(Label, { className: "w-[400px] text-md text-typography-400", children: "Petpooja Restaurant Address:" }),
              /* @__PURE__ */ jsxRuntimeExports.jsx(
                "input",
                {
                  type: "text",
                  value: pendingDelivery.posRetAddress !== void 0 ? pendingDelivery.posRetAddress ?? "" : config.posRetAddress ?? "",
                  onChange: (e) => {
                    if (isEditingDelivery) {
                      updatePending("delivery", "posRetAddress", e.target.value);
                      console.log(`Pos Restaurant Address set to ${e.target.value}`);
                    }
                  },
                  placeholder: "",
                  disabled: !isEditingDelivery,
                  className: "border border-neutral-400 rounded-md p-1 px-2"
                }
              ),
              (validationErrors == null ? void 0 : validationErrors.posRetAddress) && /* @__PURE__ */ jsxRuntimeExports.jsx("p", { className: "text-red-500 text-sm", children: validationErrors.posRetAddress })
            ] })
          ] }),
          key == "ondcDomain" && currentValue == "RET11" && /* @__PURE__ */ jsxRuntimeExports.jsx(jsxRuntimeExports.Fragment, { children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex gap-4 items-center text-md", children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx(Label, { className: "w-[400px] text-md text-typography-400", children: "Default Order Prep Time (mins):" }),
            /* @__PURE__ */ jsxRuntimeExports.jsx(
              "input",
              {
                type: "number",
                min: 0,
                value: pendingDelivery.defaultOrderPrepTime !== void 0 ? pendingDelivery.defaultOrderPrepTime ?? "" : config.defaultOrderPrepTime ?? "",
                onChange: (e) => {
                  if (isEditingDelivery) {
                    updatePending("delivery", "defaultOrderPrepTime", e.target.value);
                    console.log(`Default Order Prep Time set to ${e.target.value}`);
                  }
                },
                disabled: !isEditingDelivery,
                className: "border border-neutral-400 rounded-md p-1 px-2"
              }
            ),
            (validationErrors == null ? void 0 : validationErrors.defaultOrderPrepTime) && /* @__PURE__ */ jsxRuntimeExports.jsx("p", { className: "text-red-500 text-sm", children: validationErrors.defaultOrderPrepTime })
          ] }) })
        ] });
      }),
      /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex flex-col gap-2 mt-4", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "text-lg text-typography-600", children: "Timings:" }),
        /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex gap-4 items-center", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex gap-2 items-center w-[400px]", children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx(Label, { className: "w-[160px] text-md text-typography-400", children: "Dispatch Time:" }),
            /* @__PURE__ */ jsxRuntimeExports.jsx(
              "input",
              {
                type: "time",
                value: pendingDelivery.dispatch_time !== void 0 ? String(pendingDelivery.dispatch_time) : String(config.dispatch_time || ""),
                onChange: (e) => {
                  updatePending("delivery", "dispatch_time", e.target.value);
                  console.log(`Dispatch Time set to ${e.target.value}`);
                },
                disabled: !isEditingDelivery,
                className: "border border-neutral-400 rounded-md p-1 px-2"
              }
            )
          ] }),
          /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex gap-2 items-center", children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx(Label, { className: "w-[160px] text-md text-typography-400", children: "Delivery Time:" }),
            /* @__PURE__ */ jsxRuntimeExports.jsx(
              "input",
              {
                type: "time",
                value: pendingDelivery.delivery_time !== void 0 ? String(pendingDelivery.delivery_time) : String(config.delivery_time || ""),
                onChange: (e) => {
                  updatePending("delivery", "delivery_time", e.target.value);
                  console.log(`Delivery Time set to ${e.target.value}`);
                },
                disabled: !isEditingDelivery,
                className: "border border-neutral-400 rounded-md p-1 px-2"
              }
            )
          ] })
        ] }),
        /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex gap-4 items-center", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex gap-2 items-center w-[400px]", children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx(Label, { className: "w-[160px] text-md text-typography-400", children: "Booking Open Time:" }),
            /* @__PURE__ */ jsxRuntimeExports.jsx(
              "input",
              {
                type: "number",
                min: 0,
                value: pendingDelivery.booking_open_time !== void 0 ? pendingDelivery.booking_open_time : config.booking_open_time || 0,
                onKeyDown: (e) => {
                  if (e.key === "-" || e.key === "e") {
                    e.preventDefault();
                  }
                },
                onChange: (e) => {
                  const value = parseFloat(e.target.value);
                  if (value >= 0) {
                    updatePending("delivery", "booking_open_time", value);
                    console.log(`Booking Open Time set to ${value} minutes`);
                  }
                },
                disabled: !isEditingDelivery,
                className: "border border-neutral-400 rounded-md p-1 px-2"
              }
            )
          ] }),
          /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex gap-2 items-center", children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx(Label, { className: "w-[160px] text-md text-typography-400", children: "Booking Close Time:" }),
            /* @__PURE__ */ jsxRuntimeExports.jsx(
              "input",
              {
                type: "number",
                min: 0,
                value: pendingDelivery.booking_close_time !== void 0 ? pendingDelivery.booking_close_time : config.booking_close_time || 0,
                onKeyDown: (e) => {
                  if (e.key === "-" || e.key === "e") {
                    e.preventDefault();
                  }
                },
                onChange: (e) => {
                  const value = parseFloat(e.target.value);
                  if (value >= 0) {
                    updatePending("delivery", "booking_close_time", value);
                    console.log(`Booking Close Time set to ${value} minutes`);
                  }
                },
                disabled: !isEditingDelivery,
                className: "border border-neutral-400 rounded-md p-1 px-2"
              }
            )
          ] })
        ] }),
        /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex gap-4 items-center", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex gap-2 items-center w-[400px]", children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx(Label, { className: "w-[160px] text-md text-typography-400", children: "Breakfast Open (t1Open):" }),
            /* @__PURE__ */ jsxRuntimeExports.jsx(
              "input",
              {
                type: "number",
                min: 0,
                value: pendingDelivery.t1Open !== void 0 ? pendingDelivery.t1Open : config.t1Open || 0,
                onKeyDown: (e) => {
                  if (e.key === "-" || e.key === "e") {
                    e.preventDefault();
                  }
                },
                onChange: (e) => {
                  const value = parseFloat(e.target.value);
                  if (value >= 0) {
                    updatePending("delivery", "t1Open", value);
                    console.log(`t1Open set to ${value} minutes`);
                  }
                },
                disabled: !isEditingDelivery,
                className: "border border-neutral-400 rounded-md p-1 px-2"
              }
            )
          ] }),
          /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex gap-2 items-center", children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx(Label, { className: "w-[160px] text-md text-typography-400", children: "Breakfast Close (t1Close):" }),
            /* @__PURE__ */ jsxRuntimeExports.jsx(
              "input",
              {
                type: "number",
                min: 0,
                value: pendingDelivery.t1Close !== void 0 ? pendingDelivery.t1Close : config.t1Close || 0,
                onKeyDown: (e) => {
                  if (e.key === "-" || e.key === "e") {
                    e.preventDefault();
                  }
                },
                onChange: (e) => {
                  const value = parseFloat(e.target.value);
                  if (value >= 0) {
                    updatePending("delivery", "t1Close", value);
                    console.log(`t1Close set to ${value} minutes`);
                  }
                },
                disabled: !isEditingDelivery,
                className: "border border-neutral-400 rounded-md p-1 px-2"
              }
            )
          ] })
        ] }),
        /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex gap-4 items-center", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex gap-2 items-center w-[400px]", children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx(Label, { className: "w-[160px] text-md text-typography-400", children: "Lunch Open (t2Open):" }),
            /* @__PURE__ */ jsxRuntimeExports.jsx(
              "input",
              {
                type: "number",
                min: 0,
                value: pendingDelivery.t2Open !== void 0 ? pendingDelivery.t2Open : config.t2Open || 0,
                onKeyDown: (e) => {
                  if (e.key === "-" || e.key === "e") {
                    e.preventDefault();
                  }
                },
                onChange: (e) => {
                  const value = parseFloat(e.target.value);
                  if (value >= 0) {
                    updatePending("delivery", "t2Open", value);
                    console.log(`t2Open set to ${value} minutes`);
                  }
                },
                disabled: !isEditingDelivery,
                className: "border border-neutral-400 rounded-md p-1 px-2"
              }
            )
          ] }),
          /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex gap-2 items-center", children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx(Label, { className: "w-[160px] text-md text-typography-400", children: "Lunch Close (t2Close):" }),
            /* @__PURE__ */ jsxRuntimeExports.jsx(
              "input",
              {
                type: "number",
                min: 0,
                value: pendingDelivery.t2Close !== void 0 ? pendingDelivery.t2Close : config.t2Close || 0,
                onKeyDown: (e) => {
                  if (e.key === "-" || e.key === "e") {
                    e.preventDefault();
                  }
                },
                onChange: (e) => {
                  const value = parseFloat(e.target.value);
                  if (value >= 0) {
                    updatePending("delivery", "t2Close", value);
                    console.log(`t2Close set to ${value} minutes`);
                  }
                },
                disabled: !isEditingDelivery,
                className: "border border-neutral-400 rounded-md p-1 px-2"
              }
            )
          ] })
        ] }),
        /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex gap-4 items-center", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex gap-2 items-center w-[400px]", children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx(Label, { className: "w-[160px] text-md text-typography-400", children: "Dinner Open (t3Open):" }),
            /* @__PURE__ */ jsxRuntimeExports.jsx(
              "input",
              {
                type: "number",
                min: 0,
                value: pendingDelivery.t3Open !== void 0 ? pendingDelivery.t3Open : config.t3Open || 0,
                onKeyDown: (e) => {
                  if (e.key === "-" || e.key === "e") {
                    e.preventDefault();
                  }
                },
                onChange: (e) => {
                  const value = parseFloat(e.target.value);
                  if (value >= 0) {
                    updatePending("delivery", "t3Open", value);
                    console.log(`t3Open set to ${value} minutes`);
                  }
                },
                disabled: !isEditingDelivery,
                className: "border border-neutral-400 rounded-md p-1 px-2"
              }
            )
          ] }),
          /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex gap-2 items-center", children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx(Label, { className: "w-[160px] text-md text-typography-400", children: "Dinner Close (t3Close):" }),
            /* @__PURE__ */ jsxRuntimeExports.jsx(
              "input",
              {
                type: "number",
                min: 0,
                value: pendingDelivery.t3Close !== void 0 ? pendingDelivery.t3Close : config.t3Close || 0,
                onKeyDown: (e) => {
                  if (e.key === "-" || e.key === "e") {
                    e.preventDefault();
                  }
                },
                onChange: (e) => {
                  const value = parseFloat(e.target.value);
                  if (value >= 0) {
                    updatePending("delivery", "t3Close", value);
                    console.log(`t3Close set to ${value} minutes`);
                  }
                },
                disabled: !isEditingDelivery,
                className: "border border-neutral-400 rounded-md p-1 px-2"
              }
            )
          ] })
        ] })
      ] }),
      isEditingDelivery && /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex gap-4 justify-center mt-4", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx("button", { onClick: () => handleCancelSection("delivery"), className: "px-4 py-2 border rounded-md", children: "Cancel" }),
        /* @__PURE__ */ jsxRuntimeExports.jsx("button", { onClick: () => handleSaveSection("delivery"), className: "px-6 py-2 bg-primary text-white rounded-md", children: "Save" })
      ] })
    ] }),
    /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex flex-col gap-4 border rounded-xl border-neutral-400 p-4 shadow", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex justify-between", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "text-lg font-semibold text-typography-700", children: "Business Configurations" }),
        /* @__PURE__ */ jsxRuntimeExports.jsx(
          "button",
          {
            onClick: () => {
              if (!isEditingBusiness) {
                setPendingBusiness({
                  listing_seq: config.listing_seq,
                  mininum_required_balance: config.mininum_required_balance,
                  wa_enable: config.wa_enable
                });
                console.log("Entering edit mode for Business");
              }
              setIsEditingBusiness((prev) => !prev);
            },
            className: "inline-flex items-center gap-1 text-blue-600 hover:text-blue-800",
            children: isEditingBusiness ? /* @__PURE__ */ jsxRuntimeExports.jsxs(jsxRuntimeExports.Fragment, { children: [
              /* @__PURE__ */ jsxRuntimeExports.jsx(X, { className: "h-4 w-4" }),
              " Cancel"
            ] }) : /* @__PURE__ */ jsxRuntimeExports.jsxs(jsxRuntimeExports.Fragment, { children: [
              /* @__PURE__ */ jsxRuntimeExports.jsx(SquarePen, { className: "h-4 w-4" }),
              " Edit"
            ] })
          }
        )
      ] }),
      /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex gap-4 items-center", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx(Label, { className: "w-[400px] text-md text-typography-400", children: "Business ID:" }),
        /* @__PURE__ */ jsxRuntimeExports.jsx(
          "input",
          {
            type: "text",
            value: String(config.business_id),
            disabled: true,
            className: "border border-neutral-400 rounded-md p-1 px-2 bg-gray-100"
          }
        )
      ] }),
      businessConfigFields.map(({ label, key, type, prefix }) => {
        const k = key;
        isEditingBusiness ? pendingBusiness[k] !== void 0 ? pendingBusiness[k] : config[k] : config[k];
        return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex gap-4 items-center text-md", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx(Label, { className: "w-[400px] text-md text-typography-400", children: label }),
          /* @__PURE__ */ jsxRuntimeExports.jsxs("span", { className: "font-semibold flex gap-2 items-center", children: [
            prefix,
            /* @__PURE__ */ jsxRuntimeExports.jsx(
              "input",
              {
                type,
                value: String(isEditingBusiness ? pendingBusiness[k] ?? config[k] : config[k] || ""),
                onChange: (e) => {
                  if (isEditingBusiness) {
                    let value = e.target.value;
                    if (key === "platformFeePerc") {
                      value = value.replace(/[^0-9.]/g, "");
                      let num = Number(value);
                      if (num > 100) num = 100;
                      if (num < 0) num = 0;
                      value = value === "" ? "" : String(num);
                    }
                    updatePending("business", k, value);
                    console.log(`Business onChange: ${key} set to ${value}`);
                  }
                },
                disabled: !isEditingBusiness,
                min: key === "platformFeePerc" ? 0 : void 0,
                max: key === "platformFeePerc" ? 100 : void 0,
                className: "border border-neutral-400 rounded-md p-1 px-2"
              }
            )
          ] })
        ] }, key);
      }),
      businessConfigRadioFields.map(({ label, key }) => {
        const k = key;
        const currentValue = isEditingBusiness ? pendingBusiness[k] !== void 0 ? pendingBusiness[k] : config[k] : config[k];
        return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex gap-4 items-center text-md", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx(Label, { className: "w-[400px] text-md text-typography-400", children: label }),
          /* @__PURE__ */ jsxRuntimeExports.jsxs(
            RadioGroup,
            {
              value: currentValue ? "yes" : "no",
              onValueChange: (val) => {
                if (isEditingBusiness) {
                  updatePending("business", k, val === "yes");
                  console.log(`Business onChange: ${key} set to ${val === "yes"}`);
                }
              },
              disabled: !isEditingBusiness,
              className: "flex gap-4 items-center text-md font-semibold text-typography-800",
              children: [
                /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center gap-2", children: [
                  /* @__PURE__ */ jsxRuntimeExports.jsx(RadioGroupItem, { id: `${key}-yes`, value: "yes" }),
                  /* @__PURE__ */ jsxRuntimeExports.jsx(Label, { htmlFor: `${key}-yes`, children: "Yes" })
                ] }),
                /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center gap-2", children: [
                  /* @__PURE__ */ jsxRuntimeExports.jsx(RadioGroupItem, { id: `${key}-no`, value: "no" }),
                  /* @__PURE__ */ jsxRuntimeExports.jsx(Label, { htmlFor: `${key}-no`, children: "No" })
                ] })
              ]
            }
          )
        ] }, key);
      }),
      isEditingBusiness && /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex gap-4 justify-center", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx("button", { onClick: () => handleCancelSection("business"), className: "px-4 py-2 border rounded-md", children: "Cancel" }),
        /* @__PURE__ */ jsxRuntimeExports.jsx("button", { onClick: () => handleSaveSection("business"), className: "px-6 py-2 bg-primary text-white rounded-md", children: "Save" })
      ] })
    ] }),
    /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex flex-col gap-4 border rounded-xl border-neutral-400 p-4 shadow", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex justify-between", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "text-lg font-semibold text-typography-700", children: "Packaging Configurations" }),
        /* @__PURE__ */ jsxRuntimeExports.jsx(
          "button",
          {
            onClick: () => {
              if (!isEditingPackaging) {
                setPendingPackaging({
                  packagingCharge: config.packagingCharge,
                  packagingChargeType: config.packagingChargeType,
                  packagingApplicableOn: config.packagingApplicableOn
                });
                console.log("Entering edit mode for Packaging");
              }
              setIsEditingPackaging((prev) => !prev);
            },
            className: "inline-flex items-center gap-1 text-blue-600 hover:text-blue-800",
            children: isEditingPackaging ? /* @__PURE__ */ jsxRuntimeExports.jsxs(jsxRuntimeExports.Fragment, { children: [
              /* @__PURE__ */ jsxRuntimeExports.jsx(X, { className: "h-4 w-4" }),
              " Cancel"
            ] }) : /* @__PURE__ */ jsxRuntimeExports.jsxs(jsxRuntimeExports.Fragment, { children: [
              /* @__PURE__ */ jsxRuntimeExports.jsx(SquarePen, { className: "h-4 w-4" }),
              " Edit"
            ] })
          }
        )
      ] }),
      /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex gap-4 items-center text-md", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx(Label, { className: "w-[400px] text-md text-typography-400", children: "Packaging Charge:" }),
        /* @__PURE__ */ jsxRuntimeExports.jsx(
          "input",
          {
            type: "text",
            value: pendingPackaging.packagingCharge !== void 0 ? pendingPackaging.packagingCharge ?? "" : config.packagingCharge ?? "",
            onChange: (e) => {
              if (isEditingPackaging) {
                updatePending("packaging", "packagingCharge", e.target.value);
                console.log(`Packaging Charge set to ${e.target.value}`);
              }
            },
            disabled: !isEditingPackaging,
            className: "border border-neutral-400 rounded-md p-1 px-2"
          }
        ),
        (validationErrors == null ? void 0 : validationErrors.packagingCharge) && /* @__PURE__ */ jsxRuntimeExports.jsx("p", { className: "text-red-500 text-sm", children: validationErrors.packagingCharge })
      ] }),
      /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex gap-4 items-center text-md", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx(Label, { className: "w-[400px] text-md text-typography-400", children: "Packaging Charge Type:" }),
        /* @__PURE__ */ jsxRuntimeExports.jsxs(
          "select",
          {
            value: pendingPackaging.packagingChargeType !== void 0 ? pendingPackaging.packagingChargeType : config.packagingChargeType,
            onChange: (e) => {
              if (isEditingPackaging) {
                updatePending("packaging", "packagingChargeType", e.target.value);
                console.log(`Packaging Charge Type set to ${e.target.value}`);
              }
            },
            disabled: !isEditingPackaging,
            className: "border border-neutral-400 rounded-md p-1 px-2",
            children: [
              /* @__PURE__ */ jsxRuntimeExports.jsx("option", { value: "PERCENTAGE", children: "Percentage" }),
              /* @__PURE__ */ jsxRuntimeExports.jsx("option", { value: "FIXED", children: "Fixed" })
            ]
          }
        ),
        (validationErrors == null ? void 0 : validationErrors.packagingChargeType) && /* @__PURE__ */ jsxRuntimeExports.jsx("p", { className: "text-red-500 text-sm", children: validationErrors.packagingChargeType })
      ] }),
      /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex gap-4 items-center text-md", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx(Label, { className: "w-[400px] text-md text-typography-400", children: "Packaging Applicable On:" }),
        /* @__PURE__ */ jsxRuntimeExports.jsxs(
          "select",
          {
            value: pendingPackaging.packagingApplicableOn !== void 0 ? pendingPackaging.packagingApplicableOn : config.packagingApplicableOn,
            onChange: (e) => {
              if (isEditingPackaging) {
                updatePending("packaging", "packagingApplicableOn", e.target.value);
                console.log(`Packaging Applicable On set to ${e.target.value}`);
              }
            },
            disabled: !isEditingPackaging,
            className: "border border-neutral-400 rounded-md p-1 px-2",
            children: [
              /* @__PURE__ */ jsxRuntimeExports.jsx("option", { value: "NONE", children: "None" }),
              /* @__PURE__ */ jsxRuntimeExports.jsx("option", { value: "ITEM", children: "Item" }),
              /* @__PURE__ */ jsxRuntimeExports.jsx("option", { value: "ORDER", children: "Order" })
            ]
          }
        ),
        (validationErrors == null ? void 0 : validationErrors.packagingApplicableOn) && /* @__PURE__ */ jsxRuntimeExports.jsx("p", { className: "text-red-500 text-sm", children: validationErrors.packagingApplicableOn })
      ] }),
      isEditingPackaging && /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex gap-4 justify-center", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx("button", { onClick: () => handleCancelSection("packaging"), className: "px-4 py-2 border rounded-md", children: "Cancel" }),
        /* @__PURE__ */ jsxRuntimeExports.jsx("button", { onClick: () => handleSaveSection("packaging"), className: "px-6 py-2 bg-primary text-white rounded-md", children: "Save" })
      ] })
    ] }),
    /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex flex-col gap-4 border rounded-xl border-neutral-400 p-4 shadow", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex justify-between", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "text-lg font-semibold text-typography-700", children: "Inventory Configurations" }),
        /* @__PURE__ */ jsxRuntimeExports.jsx(
          "button",
          {
            onClick: () => {
              if (!isEditingInventory) {
                setPendingInventory({
                  auto_activate: config.auto_activate,
                  approx_pricing: config.approx_pricing,
                  approxPriceVisibility: config.approxPriceVisibility,
                  strikeoff_enabled: config.strikeoff_enabled,
                  contract_price_enabled: config.contract_price_enabled,
                  fav_items_enabled: config.fav_items_enabled,
                  minimum_order_qty: config.minimum_order_qty,
                  advance_booking_days: config.advance_booking_days,
                  minimum_order_value: config.minimum_order_value,
                  category_level: config.category_level,
                  miSources: config.miSources,
                  logisticProvider: config.logisticProvider
                });
                console.log("Entering edit mode for Inventory");
              }
              setIsEditingInventory((prev) => !prev);
            },
            className: "inline-flex items-center gap-1 text-blue-600 hover:text-blue-800",
            children: isEditingInventory ? /* @__PURE__ */ jsxRuntimeExports.jsxs(jsxRuntimeExports.Fragment, { children: [
              /* @__PURE__ */ jsxRuntimeExports.jsx(X, { className: "h-4 w-4" }),
              " Cancel"
            ] }) : /* @__PURE__ */ jsxRuntimeExports.jsxs(jsxRuntimeExports.Fragment, { children: [
              /* @__PURE__ */ jsxRuntimeExports.jsx(SquarePen, { className: "h-4 w-4" }),
              " Edit"
            ] })
          }
        )
      ] }),
      inventoryConfigRadioFields.map(({ label, key }) => {
        const k = key;
        const currentValue = isEditingInventory ? pendingInventory[k] !== void 0 ? pendingInventory[k] : config[k] : config[k];
        return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex gap-4 items-center text-md", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx(Label, { className: "w-[400px] text-md text-typography-400", children: label }),
          /* @__PURE__ */ jsxRuntimeExports.jsxs(
            RadioGroup,
            {
              value: currentValue ? ["auto_activate"].includes(key) ? "no" : "yes" : ["auto_activate"].includes(key) ? "yes" : "no",
              onValueChange: (val) => {
                if (isEditingInventory) {
                  if (k === "auto_activate") {
                    updatePending("inventory", "auto_activate", val === "no");
                    console.log(`Inventory onChange: ${key} set to ${val === "no"}`);
                  } else {
                    updatePending("inventory", k, val === "yes");
                    console.log(`Inventory onChange: ${key} set to ${val === "yes"}`);
                  }
                }
              },
              disabled: !isEditingInventory,
              className: "flex gap-4 items-center text-md font-semibold text-typography-800",
              children: [
                /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center gap-2", children: [
                  /* @__PURE__ */ jsxRuntimeExports.jsx(RadioGroupItem, { id: `${key}-yes`, value: "yes" }),
                  /* @__PURE__ */ jsxRuntimeExports.jsx(Label, { htmlFor: `${key}-yes`, children: "Yes" })
                ] }),
                /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center gap-2", children: [
                  /* @__PURE__ */ jsxRuntimeExports.jsx(RadioGroupItem, { id: `${key}-no`, value: "no" }),
                  /* @__PURE__ */ jsxRuntimeExports.jsx(Label, { htmlFor: `${key}-no`, children: "No" })
                ] })
              ]
            }
          )
        ] }, key);
      }),
      /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex gap-4 items-center text-md", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx(Label, { className: "w-[400px] text-md text-typography-400", children: "Category Level:" }),
        /* @__PURE__ */ jsxRuntimeExports.jsxs(
          RadioGroup,
          {
            value: isEditingInventory ? pendingInventory.category_level !== void 0 ? String(pendingInventory.category_level) : String(config.category_level) : String(config.category_level),
            onValueChange: (val) => {
              if (isEditingInventory) {
                updatePending("inventory", "category_level", Number(val));
                console.log(`Inventory onChange: category_level set to ${val}`);
              }
            },
            disabled: !isEditingInventory,
            className: "flex gap-4 items-center text-md font-semibold text-typography-800",
            children: [
              /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center gap-2", children: [
                /* @__PURE__ */ jsxRuntimeExports.jsx(RadioGroupItem, { id: "category_level-0", value: "0" }),
                /* @__PURE__ */ jsxRuntimeExports.jsx(Label, { htmlFor: "category_level-0", children: "No Categories" })
              ] }),
              /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center gap-2", children: [
                /* @__PURE__ */ jsxRuntimeExports.jsx(RadioGroupItem, { id: "category_level-1", value: "1" }),
                /* @__PURE__ */ jsxRuntimeExports.jsx(Label, { htmlFor: "category_level-1", children: "Level 1" })
              ] }),
              /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center gap-2", children: [
                /* @__PURE__ */ jsxRuntimeExports.jsx(RadioGroupItem, { id: "category_level-2", value: "2" }),
                /* @__PURE__ */ jsxRuntimeExports.jsx(Label, { htmlFor: "category_level-2", children: "Level 2" })
              ] }),
              /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center gap-2", children: [
                /* @__PURE__ */ jsxRuntimeExports.jsx(RadioGroupItem, { id: "category_level-3", value: "3" }),
                /* @__PURE__ */ jsxRuntimeExports.jsx(Label, { htmlFor: "category_level-3", children: "Level 3" })
              ] })
            ]
          }
        )
      ] }),
      inventoryConfigFields.map(({ label, key, type, prefix, suffix, options }) => {
        const k = key;
        isEditingInventory ? pendingInventory[k] !== void 0 ? pendingInventory[k] : config[k] : config[k];
        return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex gap-4 items-center text-md", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx(Label, { className: "w-[400px] text-md text-typography-400", children: label }),
          /* @__PURE__ */ jsxRuntimeExports.jsxs("span", { className: "font-semibold flex gap-2 items-center", children: [
            prefix,
            type === "select" ? /* @__PURE__ */ jsxRuntimeExports.jsxs(
              "select",
              {
                value: String(isEditingInventory ? pendingInventory[k] ?? config[k] : config[k] || ""),
                onChange: (e) => {
                  if (isEditingInventory) {
                    updatePending("inventory", k, e.target.value);
                    console.log(`Inventory onChange: ${key} set to ${e.target.value}`);
                  }
                },
                disabled: !isEditingInventory,
                className: "border border-neutral-400 rounded-md p-1 px-2",
                children: [
                  /* @__PURE__ */ jsxRuntimeExports.jsx("option", { value: "", hidden: true, children: "Select an option" }),
                  options == null ? void 0 : options.map((option) => /* @__PURE__ */ jsxRuntimeExports.jsx("option", { value: option.value, children: option.label }, option.value))
                ]
              }
            ) : /* @__PURE__ */ jsxRuntimeExports.jsx(
              "input",
              {
                type,
                value: String(isEditingInventory ? pendingInventory[k] ?? config[k] : config[k] || ""),
                onChange: (e) => {
                  if (isEditingInventory) {
                    updatePending("inventory", k, e.target.value);
                    console.log(`Inventory onChange: ${key} set to ${e.target.value}`);
                  }
                },
                disabled: !isEditingInventory,
                className: "border border-neutral-400 rounded-md p-1 px-2"
              }
            ),
            suffix
          ] })
        ] }, key);
      }),
      isEditingInventory && /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex gap-4 justify-center", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx("button", { onClick: () => handleCancelSection("inventory"), className: "px-4 py-2 border rounded-md", children: "Cancel" }),
        /* @__PURE__ */ jsxRuntimeExports.jsx("button", { onClick: () => handleSaveSection("inventory"), className: "px-6 py-2 bg-primary text-white rounded-md", children: "Save" })
      ] })
    ] })
  ] });
}
const MapComponent = ({
  googleMapsApiKey,
  sellerAreas,
  visibleAreas,
  getPolygonColor: getPolygonColor2,
  decodePolygon: decodePolygon2,
  getPolygonCenter,
  handleLocateShopClicked,
  onLoad,
  onUnmount,
  isLocateShopClicked,
  updateToggle,
  statesAndDistricts,
  userId,
  sellerId,
  handleSubmit
}) => {
  var _a, _b, _c, _d, _e, _f;
  const [mapLoaded, setMapLoaded] = reactExports.useState(false);
  const [latitude, setLatitude] = reactExports.useState(null);
  const [longitude, setLongitude] = reactExports.useState(null);
  const [infoWindowShown, setInfoWindowShown] = reactExports.useState({
    isShown: false,
    areaId: null,
    selectedAreaDetails: null
  });
  const [pointerLocation, setPointerLocation] = reactExports.useState({ latitude: null, longitude: null });
  const [showMarker, setShowMarker] = reactExports.useState(false);
  const [isAddLocalityClicked, setisAddLocalityClicked] = reactExports.useState(false);
  const [selectedState, setSelectedState] = reactExports.useState("");
  const [masterLocalities, setMasterLocalities] = reactExports.useState([]);
  const [newLocalities, setNewLocalities] = reactExports.useState([]);
  let stateList = [];
  statesAndDistricts == null ? void 0 : statesAndDistricts.forEach((area) => stateList.push(area.state));
  const [selectedDistrict, setSelectedDistrict] = reactExports.useState("");
  const handleFindLocations = reactExports.useCallback((lat, long, showMarker2) => {
    if (lat && long) {
      setPointerLocation({ latitude: lat, longitude: long });
      setShowMarker(showMarker2);
    } else {
      alert("Please enter valid numeric values for latitude and longitude.");
    }
  }, []);
  const handleSwitch = (areaId) => {
    updateToggle(areaId);
  };
  const handleAddLocalityClicked = reactExports.useCallback(
    (state) => {
      setisAddLocalityClicked(state);
    },
    []
  );
  const handleMasterLocalitiesClicked = reactExports.useCallback(async (userId2, state, district) => {
    if (!state || !district) {
      alert("Please select a state and district to proceed...");
      return;
    }
    try {
      const response = await fetch(`./api/masterLocalities?userId=${userId2}&state=${state}&district=${district}`);
      const data = await response.json();
      if (!response.ok) {
        throw new Error(data.error || "Unknown error");
      }
      setMasterLocalities(data.masterLocalities.data);
    } catch (error) {
      console.error("Error fetching Master Localities:", error);
      alert("Fetching Master Localities failed. Please try again.");
    }
  }, []);
  const [selectedLocalities, setSelectedLocalities] = reactExports.useState([]);
  const [selectedLocalityId, setSelectedLocalityId] = reactExports.useState(null);
  const handleNewLocalities = reactExports.useCallback((id, localityDetails, agentUserId) => {
    setNewLocalities((prevState) => {
      let updatedLocalities;
      if (prevState.some((abc) => abc.id === id)) {
        updatedLocalities = prevState.filter((abc) => abc.id !== id);
      } else {
        updatedLocalities = [...prevState, localityDetails];
      }
      setSelectedLocalities(updatedLocalities);
      return updatedLocalities;
    });
  }, []);
  const fetcher = useFetcher();
  const isLoading = fetcher.state !== "idle";
  const handleSubmitLocalities = () => {
    handleSubmit(selectedLocalities);
    setisAddLocalityClicked(false);
    setSelectedDistrict("");
    setSelectedState("");
    stateList = [];
    setMasterLocalities([]);
    setSelectedLocalityId(null);
    setSelectedLocalities([]);
  };
  const handleMarkerClick = reactExports.useCallback(
    (id, areaDetails) => {
      setInfoWindowShown((prevState) => {
        if (prevState.isShown) {
          if (prevState.areaId === id) {
            return { areaId: null, isShown: false, selectedAreaDetails: null };
          } else {
            return { areaId: id, isShown: true, selectedAreaDetails: areaDetails };
          }
        } else {
          return { areaId: id, isShown: true, selectedAreaDetails: areaDetails };
        }
      });
    },
    []
  );
  return /* @__PURE__ */ jsxRuntimeExports.jsx(LoadScript, { googleMapsApiKey, children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex w-full h-[87vh] border rounded-xl border-neutral-200 my-4", children: [
    /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "flex flex-col gap-4 w-[20vw] overflow-auto bg-white shadow rounded-xl p-3", children: sellerAreas.length ? sellerAreas.map((seller, index) => {
      return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex flex-col w-full gap-3 bg-white p-2 rounded-lg shadow", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex gap-2 justify-between items-center", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { children: [
            /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "text-sm text-typography-400", children: [
              "id: ",
              seller.sellerAreaId
            ] }),
            /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "text-lg text-typography-800", children: seller.area.name })
          ] }),
          /* @__PURE__ */ jsxRuntimeExports.jsx(Circle, { size: "1rem", fill: getPolygonColor2(index), color: getPolygonColor2(index) })
        ] }),
        /* @__PURE__ */ jsxRuntimeExports.jsx(
          Button,
          {
            variant: "ghost",
            size: "sm",
            className: "text-red-500 hover:text-red-900",
            onClick: () => {
              if (confirm("Are you sure you want to delete this area?")) {
                handleSwitch(seller == null ? void 0 : seller.sellerAreaId);
              }
            },
            style: { alignSelf: "flex-end" },
            children: /* @__PURE__ */ jsxRuntimeExports.jsx(Trash2, { size: 16 })
          }
        )
      ] }, seller.sellerAreaId);
    }) : /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "h-24 text-center", children: "No Network Areas found." }) }),
    /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "relative flex-1 h-full bg-white", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "absolute flex z-10 top-0 left-0 w-full justify-between p-3 max-h-full", children: [
        !isAddLocalityClicked ? /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "p-3 h-fit bg-white rounded-xl flex gap-2 text-blue-600 items-center shadow ", children: /* @__PURE__ */ jsxRuntimeExports.jsx(
          "button",
          {
            onClick: () => {
              handleAddLocalityClicked(true);
            },
            children: "+   Add New Locality"
          }
        ) }) : /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "p-3 bg-white rounded-xl flex flex-col gap-2  items-center shadow ", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex w-full justify-between", children: [
            "Add Localities",
            /* @__PURE__ */ jsxRuntimeExports.jsx("button", { onClick: () => {
              handleAddLocalityClicked(false);
            }, children: /* @__PURE__ */ jsxRuntimeExports.jsx(X, { size: 16 }) })
          ] }),
          /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex w-full p-1 items-center gap-3", children: [
            /* @__PURE__ */ jsxRuntimeExports.jsxs(Select, { value: selectedState, onValueChange: setSelectedState, children: [
              /* @__PURE__ */ jsxRuntimeExports.jsx(SelectTrigger, { className: "w-[180px]", children: /* @__PURE__ */ jsxRuntimeExports.jsx(SelectValue, { placeholder: "Select state" }) }),
              /* @__PURE__ */ jsxRuntimeExports.jsx(SelectContent, { children: stateList.map((state) => /* @__PURE__ */ jsxRuntimeExports.jsx(SelectItem, { value: state, children: state }, state)) })
            ] }),
            /* @__PURE__ */ jsxRuntimeExports.jsxs(Select, { value: selectedDistrict, onValueChange: (newDistrict) => {
              setSelectedDistrict(newDistrict);
              handleMasterLocalitiesClicked(userId, selectedState, newDistrict);
            }, disabled: !selectedState, children: [
              /* @__PURE__ */ jsxRuntimeExports.jsx(SelectTrigger, { className: "w-[180px]", children: /* @__PURE__ */ jsxRuntimeExports.jsx(SelectValue, { placeholder: "Select District" }) }),
              /* @__PURE__ */ jsxRuntimeExports.jsx(SelectContent, { children: statesAndDistricts == null ? void 0 : statesAndDistricts.filter((abc) => abc.state === selectedState).map((district) => /* @__PURE__ */ jsxRuntimeExports.jsx(SelectItem, { value: district.district, children: district.district }, district.district)) })
            ] })
          ] }),
          /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex flex-col gap-2 self-start max-h-full w-full overflow-auto p-2 ", children: [
            masterLocalities && masterLocalities.length > 0 ? (masterLocalities.filter((a) => !(sellerAreas == null ? void 0 : sellerAreas.some((na) => na.area.id === a.id))).length > 0 ? masterLocalities.filter((a) => !(sellerAreas == null ? void 0 : sellerAreas.some((na) => na.area.id === a.id))) : masterLocalities).map((locality, index) => /* @__PURE__ */ jsxRuntimeExports.jsxs(jsxRuntimeExports.Fragment, { children: [
              /* @__PURE__ */ jsxRuntimeExports.jsx("div", { children: /* @__PURE__ */ jsxRuntimeExports.jsxs("label", { className: "cursor-pointer flex items-center gap-2 p-1", htmlFor: `locality-${locality.id}`, children: [
                /* @__PURE__ */ jsxRuntimeExports.jsx(
                  Checkbox,
                  {
                    id: `locality-${locality.id}`,
                    checked: newLocalities.some((abc) => abc.id === locality.id),
                    onCheckedChange: () => handleNewLocalities(locality.id, locality)
                  }
                ),
                /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "flex flex-col gap-2", children: /* @__PURE__ */ jsxRuntimeExports.jsxs("span", { children: [
                  locality.id,
                  " - ",
                  locality.name
                ] }) })
              ] }) }, locality.id),
              index < masterLocalities.length - 1 && /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "border-b border-neutral-200" }),
              " "
            ] })) : /* @__PURE__ */ jsxRuntimeExports.jsx("div", { children: "No Localities Fetched yet" }),
            " "
          ] }),
          /* @__PURE__ */ jsxRuntimeExports.jsx(
            "button",
            {
              type: "submit",
              className: "px-4 py-2 bg-blue-500 text-white rounded disabled:bg-gray-400",
              onClick: () => handleSubmitLocalities(),
              disabled: selectedLocalities.length === 0,
              children: isLoading ? "Submitting..." : "Submit Localities"
            }
          )
        ] }),
        !isLocateShopClicked ? /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "p-3 absolute z-20 top-2 right-2 bg-white rounded-xl flex gap-2 text-blue-600 items-center shadow", children: /* @__PURE__ */ jsxRuntimeExports.jsx("button", { onClick: () => handleLocateShopClicked(true), children: "📍   Locate a shop" }) }) : /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "p-3 absolute z-20 top-2 right-2 bg-white rounded-xl flex flex-col gap-2 items-center shadow", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex w-full justify-between", children: [
            "Locate a shop",
            /* @__PURE__ */ jsxRuntimeExports.jsx("button", { onClick: () => handleLocateShopClicked(false), children: /* @__PURE__ */ jsxRuntimeExports.jsx(X, { size: 16 }) })
          ] }),
          /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex w-full p-1 items-center justify-between", children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx("p", { children: "Latitude : " }),
            /* @__PURE__ */ jsxRuntimeExports.jsx(
              "input",
              {
                type: "text",
                className: "border border-neutral-400 rounded-md p-1",
                onChange: (e) => setLatitude(parseFloat(e.target.value))
              }
            )
          ] }),
          /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex gap-1 p-1 items-center", children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx("p", { children: "Longitude : " }),
            /* @__PURE__ */ jsxRuntimeExports.jsx(
              "input",
              {
                type: "text",
                className: "border border-neutral-400 rounded-md p-1",
                onChange: (e) => setLongitude(parseFloat(e.target.value))
              }
            )
          ] }),
          /* @__PURE__ */ jsxRuntimeExports.jsx(
            "button",
            {
              className: "text-primary border border-primary p-2 rounded-md",
              onClick: () => handleFindLocations(latitude ?? 0, longitude ?? 0, true),
              children: "📍   Find Location"
            }
          )
        ] })
      ] }),
      !mapLoaded && /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "flex items-center justify-center h-full", children: /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "loader", children: "Loading Map..." }) }),
      googleMapsApiKey ? /* @__PURE__ */ jsxRuntimeExports.jsxs(
        GoogleMap,
        {
          mapContainerStyle: { width: "100%", height: "100%" },
          center: { lat: 12.9716, lng: 77.5946 },
          zoom: 11,
          onLoad: (mapInstance) => {
            onLoad(mapInstance);
            setMapLoaded(true);
          },
          onUnmount: () => {
            onUnmount();
            setMapLoaded(false);
          },
          options: {
            mapTypeControl: false,
            streetViewControl: false,
            fullscreenControl: false,
            clickableIcons: false,
            gestureHandling: "auto"
          },
          children: [
            mapLoaded ? (_a = sellerAreas == null ? void 0 : sellerAreas.filter((abc) => visibleAreas.has(abc.sellerAreaId))) == null ? void 0 : _a.map((area, index) => area.area.polygon && /* @__PURE__ */ jsxRuntimeExports.jsx(
              Polygon,
              {
                paths: decodePolygon2(area.area.polygon),
                options: {
                  fillColor: isAddLocalityClicked ? "#3b82f6" : getPolygonColor2(index),
                  fillOpacity: 0.2,
                  strokeColor: isAddLocalityClicked ? "#3b82f6" : getPolygonColor2(index),
                  strokeOpacity: 1,
                  strokeWeight: 2,
                  draggable: false,
                  editable: false,
                  geodesic: false,
                  zIndex: 1,
                  clickable: true
                  // Ensure polygons remain clickable
                },
                onClick: () => handleMarkerClick(area.sellerAreaId, area)
              },
              area.sellerAreaId
            )) : null,
            mapLoaded ? newLocalities == null ? void 0 : newLocalities.map((area, index) => area.polygon && /* @__PURE__ */ jsxRuntimeExports.jsx(
              Polygon,
              {
                paths: decodePolygon2(area.polygon),
                options: {
                  fillColor: "#10b981",
                  fillOpacity: 0.4,
                  strokeColor: "#10b981",
                  strokeOpacity: 1,
                  strokeWeight: 2,
                  draggable: false,
                  editable: false,
                  geodesic: false,
                  zIndex: 10,
                  clickable: false
                  // Ensure polygons remain clickable
                }
              },
              area.id
            )) : null,
            infoWindowShown && infoWindowShown.areaId && infoWindowShown.isShown && infoWindowShown.selectedAreaDetails && /* @__PURE__ */ jsxRuntimeExports.jsxs(jsxRuntimeExports.Fragment, { children: [
              console.log("entered infowindow params with ##########", infoWindowShown),
              /* @__PURE__ */ jsxRuntimeExports.jsx(
                InfoWindow,
                {
                  position: getPolygonCenter(decodePolygon2(((_c = (_b = infoWindowShown.selectedAreaDetails) == null ? void 0 : _b.area) == null ? void 0 : _c.polygon) || "")),
                  onCloseClick: () => setInfoWindowShown({ isShown: false, areaId: null, selectedAreaDetails: null }),
                  options: {
                    headerDisabled: true,
                    minWidth: 200,
                    disableAutoPan: true
                  },
                  children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex flex-col gap-2 overflow-hidden ", children: [
                    /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex justify-between w-full align-middle items-center", children: [
                      /* @__PURE__ */ jsxRuntimeExports.jsx("h2", { className: "text-md font-semibold text-typography-300", children: "Locality Info" }),
                      /* @__PURE__ */ jsxRuntimeExports.jsx(
                        "button",
                        {
                          className: "inline-flex items-center gap-1 hover:text-blue-800",
                          onClick: () => setInfoWindowShown({ isShown: false, areaId: null, selectedAreaDetails: null }),
                          children: /* @__PURE__ */ jsxRuntimeExports.jsx(X, { className: "h-5 w-5" })
                        }
                      )
                    ] }),
                    /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex flex-col gap-1 text-sm font-medium text-typography-700 w-[calc(100%-1rem)]", children: [
                      /* @__PURE__ */ jsxRuntimeExports.jsxs("p", { children: [
                        /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "text-sm text-typography-300 font-thin", children: "Area id:" }),
                        " ",
                        (_d = infoWindowShown.selectedAreaDetails) == null ? void 0 : _d.sellerAreaId
                      ] }),
                      /* @__PURE__ */ jsxRuntimeExports.jsxs("p", { children: [
                        /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "text-sm text-typography-300 font-thin", children: "Area Name: " }),
                        " ",
                        (_f = (_e = infoWindowShown.selectedAreaDetails) == null ? void 0 : _e.area) == null ? void 0 : _f.name
                      ] })
                    ] })
                  ] })
                }
              ),
              /* @__PURE__ */ jsxRuntimeExports.jsx("style", { children: `.info-window-wrapper {
                                  padding: 10px; 
                                }
                              
                                .gm-style-iw { 
                                  padding: 12px !important; 
                                }
                                .gm-style-iw-d { 
                                  padding: 0px !important; 
                                  overflow:hidden !important;
                                }` }),
              " "
            ] }),
            showMarker && pointerLocation.latitude !== null && pointerLocation.longitude !== null && /* @__PURE__ */ jsxRuntimeExports.jsx(Marker, { position: { lat: pointerLocation.latitude, lng: pointerLocation.longitude } })
          ]
        }
      ) : /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "flex items-center justify-center h-full", children: /* @__PURE__ */ jsxRuntimeExports.jsx("p", { className: "text-red-500", children: "Google Maps API key is missing." }) })
    ] })
  ] }) });
};
function BillingConfig({ billingConfig, onSellerAttributeUpdate }) {
  const bankConfigFields = [
    { label: "Branch", key: "baBranch", type: "text", prefix: "" },
    { label: "Account Number", key: "baNumber", type: "number", prefix: "" },
    { label: "Verified By", key: "baVerifiedBy", type: "text", prefix: "" },
    { label: "IFSC Code", key: "baIfsc", type: "text", prefix: "" },
    { label: "Account Name", key: "baName", type: "text", prefix: "" },
    { label: "Bank Name", key: "baBank", type: "text", prefix: "" },
    { label: "Bank Account Verified", key: "baVerified", type: "checkbox", prefix: "" }
  ];
  const plaFormConfigFields = [
    { label: "Platform Commission Percentage:  ", key: "pcBasicPc", type: "number", prefix: "%" },
    { label: "Fixed Platform Commission per month :  ", key: "pcBasicPmFixed", type: "number", prefix: "₹" },
    { label: "Minimum Platform Commission per month:  ", key: "pcBasicPmMin", type: "number", prefix: "₹" },
    { label: "Sales Commission Percentage:  ", key: "salesCommPc", type: "number", prefix: "%" },
    { label: ".Sales Commission per Kg:  ", key: "salesCommPkg", type: "number", prefix: "₹" },
    { label: "Fixed Sales Commission per month:  ", key: "salesCommPmFixed", type: "number", prefix: "₹" },
    { label: "Minimum Sales Commission per month:  ", key: "salesCommPmMin", type: "number", prefix: "₹" },
    { label: "Agent Commision Percentage :  ", key: "agentCommPc", type: "number", prefix: "%" },
    { label: "Agent Commision per Kg:  ", key: "agentCommPkg", type: "number", prefix: "₹" }
  ];
  const [bankConfigEditable, setBankConfigEditable] = reactExports.useState(false);
  const [platFormEditable, setPlatFormEditable] = reactExports.useState(false);
  const [config, setConfig] = reactExports.useState(billingConfig);
  const setBankEditableClick = reactExports.useCallback(() => {
    setBankConfigEditable((prevState) => {
      if (prevState) {
        return false;
      } else {
        return true;
      }
    });
  }, []);
  const setPlatformEditableClick = reactExports.useCallback(() => {
    setPlatFormEditable((prevState) => {
      if (prevState) {
        return false;
      } else {
        return true;
      }
    });
  }, []);
  const [pendingConfig, setPendingConfig] = reactExports.useState({});
  const [visibleSaveButtons, setVisibleSaveButtons] = reactExports.useState({});
  const bankConfigKeyMapping = {
    lubyName: "lubyName",
    lubyId: "lubyId",
    bid: "bid",
    baVerified: "baVerified",
    baBranch: "baBranch",
    baNumber: "baNumber",
    baVerifiedBy: "baVerifiedBy",
    baIfsc: "baIfsc",
    baName: "baName",
    baBank: "baBank"
  };
  const handleConfigChange = (key, value) => {
    console.log(value, key, "Config Change Triggered");
    setPendingConfig((prev) => {
      let newValue = value;
      if (typeof value === "string") {
        if (value.toLowerCase() === "yes") {
          newValue = true;
        } else if (value.toLowerCase() === "no") {
          newValue = false;
        } else if (!isNaN(Number(value))) {
          newValue = Number(value);
        } else if (value === "0") {
          newValue = "";
        }
      }
      if (key in bankConfigKeyMapping) {
        return {
          ...prev,
          bankConfig: {
            ...prev.bankConfig,
            [key]: newValue
          }
        };
      } else {
        return {
          ...prev,
          [key]: newValue
        };
      }
    });
    setVisibleSaveButtons((prev) => ({
      ...prev,
      [key]: true
    }));
  };
  const handleSave = (key) => {
    var _a;
    let updatedValue;
    if (key in bankConfigKeyMapping) {
      updatedValue = (_a = pendingConfig.bankConfig) == null ? void 0 : _a[key];
      onSellerAttributeUpdate(`${key}`, updatedValue);
    } else {
      updatedValue = pendingConfig[key];
      onSellerAttributeUpdate(key, updatedValue);
    }
    setConfig((prevConfig) => ({
      ...prevConfig,
      ...key in bankConfigKeyMapping ? {
        bankConfig: {
          ...prevConfig.bankConfig,
          [key]: updatedValue
        }
      } : { [key]: updatedValue }
    }));
    setVisibleSaveButtons((prev) => ({
      ...prev,
      [key]: false
    }));
    setPendingConfig((prev) => {
      var _a2;
      const updatedConfig = { ...prev };
      if (key in bankConfigKeyMapping) {
        (_a2 = updatedConfig.bankConfig) == null ? true : delete _a2[key];
      } else {
        delete updatedConfig[key];
      }
      return updatedConfig;
    });
  };
  return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex flex-col gap-4 border bg-white rounded-xl border-neutral-200 my-4 p-4", children: [
    /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex flex-col gap-4 border rounded-xl border-neutral-400 p-4 shadow", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex justify-between w-full", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "text-lg font-semibold text-typography-700", children: "Bank Configurations" }),
        /* @__PURE__ */ jsxRuntimeExports.jsx(
          "button",
          {
            className: "inline-flex items-center gap-1 text-blue-600 hover:text-blue-800",
            onClick: () => {
              if (!bankConfigEditable) {
                setPendingConfig({
                  bankConfig: { ...billingConfig.bankConfig }
                });
              }
              setBankEditableClick();
            },
            children: bankConfigEditable ? /* @__PURE__ */ jsxRuntimeExports.jsxs(jsxRuntimeExports.Fragment, { children: [
              /* @__PURE__ */ jsxRuntimeExports.jsx(X, { className: "h-4 w-4" }),
              "Cancel"
            ] }) : /* @__PURE__ */ jsxRuntimeExports.jsxs(jsxRuntimeExports.Fragment, { children: [
              /* @__PURE__ */ jsxRuntimeExports.jsx(SquarePen, { className: "h-4 w-4" }),
              "Edit"
            ] })
          }
        )
      ] }),
      /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex gap-4 items-center", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "text-md text-typography-500 w-[400px]", children: "Business ID:" }),
        billingConfig.businessId
      ] }),
      /* @__PURE__ */ jsxRuntimeExports.jsx("div", { children: bankConfigFields.map(({ label, key, type, prefix }) => {
        var _a, _b, _c, _d, _e, _f, _g, _h, _i, _j;
        return /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "flex flex-col gap-2 text-md text-typography-400", children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex gap-4 items-center", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "text-md text-typography-500 w-[400px] mt-2", children: [
            label,
            ":"
          ] }),
          /* @__PURE__ */ jsxRuntimeExports.jsxs("span", { className: "font-semibold text-typography-800 flex items-center", children: [
            prefix,
            type === "checkbox" ? /* @__PURE__ */ jsxRuntimeExports.jsx(
              "input",
              {
                type: "checkbox",
                checked: Boolean(
                  ((_a = pendingConfig == null ? void 0 : pendingConfig.bankConfig) == null ? void 0 : _a[key]) ?? ((_b = billingConfig == null ? void 0 : billingConfig.bankConfig) == null ? void 0 : _b[key]) ?? false
                ),
                onChange: (e) => handleConfigChange(
                  key,
                  e.target.checked
                ),
                className: "border border-neutral-400 rounded-md p-1 mt-2",
                disabled: !bankConfigEditable
              }
            ) : type === "number" ? /* @__PURE__ */ jsxRuntimeExports.jsx(
              "input",
              {
                type: "number",
                value: ((_c = pendingConfig == null ? void 0 : pendingConfig.bankConfig) == null ? void 0 : _c[key]) ?? ((_d = billingConfig == null ? void 0 : billingConfig.bankConfig) == null ? void 0 : _d[key]) ? String(
                  ((_e = pendingConfig == null ? void 0 : pendingConfig.bankConfig) == null ? void 0 : _e[key]) ?? ((_f = billingConfig == null ? void 0 : billingConfig.bankConfig) == null ? void 0 : _f[key])
                ) : "",
                onChange: (e) => {
                  const value = e.target.value.trim();
                  if (value === "") {
                    handleConfigChange(key, "");
                    return;
                  }
                  const numericValue = Math.max(0, Number(value));
                  handleConfigChange(key, numericValue);
                },
                className: "border border-neutral-400 rounded-md p-1 px-2 mt-2",
                disabled: !bankConfigEditable
              }
            ) : /* @__PURE__ */ jsxRuntimeExports.jsx(
              "input",
              {
                type: "text",
                value: ((_g = pendingConfig == null ? void 0 : pendingConfig.bankConfig) == null ? void 0 : _g[key]) ?? ((_h = billingConfig == null ? void 0 : billingConfig.bankConfig) == null ? void 0 : _h[key]) ? String(
                  ((_i = pendingConfig == null ? void 0 : pendingConfig.bankConfig) == null ? void 0 : _i[key]) ?? ((_j = billingConfig == null ? void 0 : billingConfig.bankConfig) == null ? void 0 : _j[key])
                ) : "",
                onChange: (e) => {
                  const value = e.target.value;
                  handleConfigChange(key, value === "0" || value.trim() === "" ? "" : value);
                },
                className: "border border-neutral-400 rounded-md p-1 px-2 mt-2",
                disabled: !bankConfigEditable
              }
            )
          ] }),
          bankConfigEditable && visibleSaveButtons[key] && /* @__PURE__ */ jsxRuntimeExports.jsx(
            "button",
            {
              className: "flex flex-row text-blue-500 items-center gap-1 cursor-pointer text-lg font-bold",
              onClick: () => handleSave(key),
              children: /* @__PURE__ */ jsxRuntimeExports.jsx(Save, { className: "h-5 w-5" })
            }
          )
        ] }) }, key);
      }) })
    ] }),
    /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex flex-col gap-4 border rounded-xl border-neutral-400 p-4 shadow", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex justify-between w-full", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "text-lg font-semibold text-typography-700", children: "PlatForm Configurations" }),
        /* @__PURE__ */ jsxRuntimeExports.jsx(
          "button",
          {
            className: "inline-flex items-center gap-1 text-blue-600 hover:text-blue-800",
            onClick: () => setPlatformEditableClick(),
            children: platFormEditable ? /* @__PURE__ */ jsxRuntimeExports.jsxs(jsxRuntimeExports.Fragment, { children: [
              /* @__PURE__ */ jsxRuntimeExports.jsx(X, { className: "h-4 w-4" }),
              "Cancel"
            ] }) : /* @__PURE__ */ jsxRuntimeExports.jsxs(jsxRuntimeExports.Fragment, { children: [
              /* @__PURE__ */ jsxRuntimeExports.jsx(SquarePen, { className: "h-4 w-4" }),
              "Edit"
            ] })
          }
        )
      ] }),
      plaFormConfigFields.map(({ label, key, type, prefix }) => /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "flex flex-col gap-2 text-md text-typography-400", children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex gap-4 items-center", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "text-md text-typography-500 w-[400px]", children: label }),
        /* @__PURE__ */ jsxRuntimeExports.jsxs("span", { className: "font-semibold text-typography-800 flex gap-2 items-center", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx(
            "input",
            {
              type,
              value: String(pendingConfig[key] ?? config[key]),
              onChange: (e) => {
                const numericValue = Math.max(0, Number(e.target.value));
                handleConfigChange(key, numericValue);
              },
              className: "border border-neutral-400 rounded-md p-1 px-2",
              disabled: !platFormEditable
            }
          ),
          prefix
        ] }),
        plaFormConfigFields && visibleSaveButtons[key] && /* @__PURE__ */ jsxRuntimeExports.jsx(
          "button",
          {
            className: "flex flex-row text-blue-500 items-center gap-1 cursor-pointer text-lg font-bold",
            onClick: () => handleSave(key),
            children: /* @__PURE__ */ jsxRuntimeExports.jsx(Save, { className: "h-5 w-5" })
          }
        )
      ] }) }, key))
    ] })
  ] });
}
const fields = [
  { name: "name", type: "text", label: "Name" },
  { name: "unit", type: "text", label: "Unit" },
  { name: "packaging", type: "text", label: "Packaging" },
  { name: "weightFactor", type: "number", label: "Weight Factor" },
  { name: "maxAvailableQty", type: "number", label: "Max Available Quantity" },
  { name: "minOrderQty", type: "number", label: "Min Order Quantity" },
  { name: "maxOrderQty", type: "number", label: "Max Order Quantity" },
  { name: "incrementOrderQty", type: "number", label: "Increment Order Quantity" },
  { name: "pricePerUnit", type: "number", label: "Price per Unit" },
  { name: "displayOrder", type: "number", label: "Display Order" },
  { name: "description", type: "text", label: "Description" },
  { name: "strikeOffPrice", type: "number", label: "StrikeOffPrice" },
  { name: "distChargePu", type: "number", label: "DistChargePu" },
  { name: "distChargePc", type: "number", label: "DistChargePc" },
  { name: "minAcPkg", type: "number", label: "MinAcPkg" },
  { name: "maxAcPkg", type: "number", label: "MaxAcPkg" },
  { name: "minDistcPkg", type: "number", label: "MinDistcPkg" },
  { name: "maxDistcPkg", type: "number", label: "MaxDistcPkg" },
  { name: "packagingCharge", type: "number", label: "Packaging Charge" }
];
const EditModal = ({
  isOpen,
  data,
  onClose,
  onSave,
  multiple,
  supplierList
}) => {
  const [formData, setFormData] = reactExports.useState(data);
  const uploadFetcher = useFetcher();
  const [uploadError, setUploadError] = reactExports.useState(null);
  const [uploading, setUploading] = reactExports.useState(false);
  const fileInputRef = reactExports.useRef(null);
  reactExports.useEffect(() => {
    setFormData(data);
  }, [data]);
  const handleFileSelect = async (event) => {
    const files = Array.from(event.target.files || []);
    if (files.length === 0) return;
    setUploadError(null);
    const MAX_FILE_SIZE = 500 * 1024;
    const allowedTypes = ["image/jpeg", "image/png", "image/gif", "image/webp"];
    const validFile = files.find((file) => {
      if (file.size > MAX_FILE_SIZE) {
        setUploadError("File size exceeds 500kb limit.");
        return false;
      }
      if (!allowedTypes.includes(file.type)) {
        setUploadError("Only JPEG, PNG, GIF, and WEBP images are allowed.");
        return false;
      }
      return true;
    });
    if (!validFile) return;
    const uploadFormData = new FormData();
    uploadFormData.append("_action", "uploadImage");
    uploadFormData.append("file", validFile, validFile.name);
    setUploading(true);
    uploadFetcher.submit(uploadFormData, {
      method: "post",
      action: "/home/<USER>",
      encType: "multipart/form-data"
    });
  };
  const handleChange = (e) => {
    const { name, value, type } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: type === "number" ? value === "" ? "" : Number(value) : type === "radio" && (name === "taxExempt" || name === "freeItem") ? value === "true" : value
    }));
  };
  reactExports.useEffect(() => {
    if (uploadFetcher.data) {
      if (uploadFetcher.data.error) {
        setUploadError(uploadFetcher.data.error);
      } else if (uploadFetcher.data.fileUrl) {
        const uploadedUrl = uploadFetcher.data.fileUrl;
        setFormData((prev) => ({
          ...prev,
          picture: uploadedUrl
        }));
        setUploadError(null);
        if (fileInputRef.current) fileInputRef.current.value = "";
      }
      setUploading(false);
    }
  }, [uploadFetcher.data]);
  const handleSave = () => {
    const updatedData = { ...formData };
    if (Object.keys(data).length > 0) {
      for (const key in formData) {
        const typedKey = key;
        if (updatedData[typedKey] === data[typedKey]) {
          delete updatedData[typedKey];
        }
      }
      updatedData.Id = data.Id;
    }
    onSave(updatedData);
  };
  if (!isOpen) return null;
  return /* @__PURE__ */ jsxRuntimeExports.jsx(Dialog, { open: isOpen, onOpenChange: onClose, children: /* @__PURE__ */ jsxRuntimeExports.jsxs(
    DialogContent,
    {
      className: "p-4 bg-white rounded-lg shadow-lg w-full max-w-md sm:max-w-lg md:max-w-xl max-h-[80vh] flex flex-col",
      style: { width: "95vw" },
      children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx(DialogTitle, { className: "text-xl font-bold mb-2 text-center", children: "Edit Seller Item" }),
        /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex-1 overflow-y-auto space-y-4", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex flex-col items-center gap-2", children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx("h3", { className: "text-base font-semibold text-gray-800", children: "Upload Image" }),
            /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center gap-4", children: [
              formData && "picture" in formData && formData.picture && /* @__PURE__ */ jsxRuntimeExports.jsx(
                "img",
                {
                  src: formData.picture,
                  alt: "Preview",
                  className: "h-20 w-20 object-cover rounded-full border-2 border-gray-300"
                }
              ),
              /* @__PURE__ */ jsxRuntimeExports.jsx(
                "input",
                {
                  ref: fileInputRef,
                  type: "file",
                  accept: "image/*",
                  onChange: handleFileSelect,
                  className: "file:mr-4 file:py-1 file:px-3 file:rounded-full file:border-0 file:text-xs file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
                }
              )
            ] }),
            uploadError && /* @__PURE__ */ jsxRuntimeExports.jsx("p", { className: "text-red-500 text-xs", children: uploadError })
          ] }),
          /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "grid grid-cols-1 sm:grid-cols-2 gap-3", children: fields.map((field) => /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex flex-col", children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx("label", { className: "text-xs font-medium text-gray-700 mb-1", children: field.label }),
            /* @__PURE__ */ jsxRuntimeExports.jsx(
              "input",
              {
                type: field.type,
                name: field.name,
                value: field.type === "number" ? (formData == null ? void 0 : formData[field.name]) ?? "" : (formData == null ? void 0 : formData[field.name]) || "",
                onChange: handleChange,
                className: "p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-400 text-sm",
                placeholder: field.label,
                min: field.type === "number" ? 0 : void 0
              }
            )
          ] }, field.name)) }),
          /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx("label", { className: "block text-xs font-medium text-gray-700 mb-1", children: "Select Supplier" }),
            /* @__PURE__ */ jsxRuntimeExports.jsxs(
              "select",
              {
                name: "supplierItemId",
                value: formData.supplierItemId,
                onChange: handleChange,
                required: true,
                className: "w-full rounded border border-gray-300 p-2 focus:border-blue-400 focus:ring focus:ring-blue-200 focus:ring-opacity-50 text-sm",
                children: [
                  /* @__PURE__ */ jsxRuntimeExports.jsx("option", { value: "", children: "Select Supplier" }),
                  supplierList == null ? void 0 : supplierList.map((supplier) => {
                    var _a;
                    return /* @__PURE__ */ jsxRuntimeExports.jsx(
                      "option",
                      {
                        value: (_a = supplier == null ? void 0 : supplier.supplierItemId) == null ? void 0 : _a.toString(),
                        children: supplier == null ? void 0 : supplier.supplierName
                      },
                      supplier == null ? void 0 : supplier.supplierItemId
                    );
                  })
                ]
              }
            )
          ] }),
          /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "grid grid-cols-1 sm:grid-cols-2 gap-3", children: [
            /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex flex-col", children: [
              /* @__PURE__ */ jsxRuntimeExports.jsx("label", { className: "text-xs font-medium text-gray-700 mb-1", children: "Exclude Tax:" }),
              /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex gap-4", children: [
                /* @__PURE__ */ jsxRuntimeExports.jsxs("label", { className: "flex items-center gap-1 text-xs", children: [
                  /* @__PURE__ */ jsxRuntimeExports.jsx(
                    "input",
                    {
                      type: "radio",
                      name: "taxExempt",
                      value: "true",
                      checked: (formData == null ? void 0 : formData.taxExempt) === true,
                      onChange: handleChange
                    }
                  ),
                  "Yes"
                ] }),
                /* @__PURE__ */ jsxRuntimeExports.jsxs("label", { className: "flex items-center gap-1 text-xs", children: [
                  /* @__PURE__ */ jsxRuntimeExports.jsx(
                    "input",
                    {
                      type: "radio",
                      name: "taxExempt",
                      value: "false",
                      checked: (formData == null ? void 0 : formData.taxExempt) === false,
                      onChange: handleChange
                    }
                  ),
                  "No"
                ] })
              ] })
            ] }),
            /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex flex-col", children: [
              /* @__PURE__ */ jsxRuntimeExports.jsx("label", { className: "text-xs font-medium text-gray-700 mb-1", children: "Free Item:" }),
              /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex gap-4", children: [
                /* @__PURE__ */ jsxRuntimeExports.jsxs("label", { className: "flex items-center gap-1 text-xs", children: [
                  /* @__PURE__ */ jsxRuntimeExports.jsx(
                    "input",
                    {
                      type: "radio",
                      name: "freeItem",
                      value: "true",
                      checked: (formData == null ? void 0 : formData.freeItem) === true,
                      onChange: handleChange
                    }
                  ),
                  "Yes"
                ] }),
                /* @__PURE__ */ jsxRuntimeExports.jsxs("label", { className: "flex items-center gap-1 text-xs", children: [
                  /* @__PURE__ */ jsxRuntimeExports.jsx(
                    "input",
                    {
                      type: "radio",
                      name: "freeItem",
                      value: "false",
                      checked: (formData == null ? void 0 : formData.freeItem) === false,
                      onChange: handleChange
                    }
                  ),
                  "No"
                ] })
              ] })
            ] })
          ] }),
          /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx("label", { className: "text-xs font-medium text-gray-700 mb-1", children: "Diet:" }),
            /* @__PURE__ */ jsxRuntimeExports.jsxs(
              Select,
              {
                value: formData == null ? void 0 : formData.diet,
                onValueChange: (value) => setFormData((prev) => ({ ...prev, diet: value })),
                children: [
                  /* @__PURE__ */ jsxRuntimeExports.jsx(SelectTrigger, { className: "w-full", children: /* @__PURE__ */ jsxRuntimeExports.jsx(SelectValue, { placeholder: "Select Diet" }) }),
                  /* @__PURE__ */ jsxRuntimeExports.jsxs(SelectContent, { children: [
                    /* @__PURE__ */ jsxRuntimeExports.jsx(SelectItem, { value: "veg", children: "Veg" }),
                    /* @__PURE__ */ jsxRuntimeExports.jsx(SelectItem, { value: "nonveg", children: "Nonveg" }),
                    /* @__PURE__ */ jsxRuntimeExports.jsx(SelectItem, { value: "egg", children: "Egg" })
                  ] })
                ]
              }
            )
          ] })
        ] }),
        /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex flex-col sm:flex-row justify-end gap-2 mt-6", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx(
            "button",
            {
              onClick: onClose,
              className: "w-full sm:w-auto px-5 py-2 bg-gray-400 text-white rounded hover:bg-gray-500 transition",
              children: "Cancel"
            }
          ),
          /* @__PURE__ */ jsxRuntimeExports.jsx(
            "button",
            {
              onClick: handleSave,
              className: "w-full sm:w-auto px-5 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition",
              children: "Save"
            }
          )
        ] })
      ]
    }
  ) });
};
const AddonsGroupModal = ({ isOpen, data, onClose, header, sellerId }) => {
  const [formData, setFormData] = reactExports.useState(
    data || {
      internalName: "",
      displayName: "",
      description: "",
      active: false
    }
  );
  const addonsGroupFetcher = useFetcher();
  const loading = addonsGroupFetcher.state != "idle";
  const { showToast } = useToast();
  reactExports.useEffect(() => {
    var _a, _b;
    if (addonsGroupFetcher.data) {
      if ((_a = addonsGroupFetcher.data) == null ? void 0 : _a.sucess) {
        showToast("sucessFully added", "success");
        onClose();
        setFormData({
          internalName: "",
          displayName: "",
          description: "",
          active: false
        });
      } else if (!((_b = addonsGroupFetcher.data) == null ? void 0 : _b.sucess)) {
        showToast("Failed to add. Please try again.", "error");
      }
    }
  }, [addonsGroupFetcher.data]);
  reactExports.useEffect(() => {
    if (data && isOpen) {
      setFormData(data);
    } else if (!isOpen) {
      setFormData({
        internalName: "",
        displayName: "",
        description: "",
        active: false
      });
    }
  }, [data, isOpen]);
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prevData) => ({
      ...prevData,
      [name]: name === "active" ? value === "true" : value
      // Convert active to boolean
    }));
  };
  const handleSave = () => {
    var _a;
    const fetcherData = new FormData();
    fetcherData.append("actionType", "addonGroupAdd");
    fetcherData.append("mode", "editMode");
    fetcherData.append("AddonGId", (_a = data == null ? void 0 : data.id) == null ? void 0 : _a.toString());
    fetcherData.append("sellerId", sellerId);
    fetcherData.append("addonGroupData", JSON.stringify(formData));
    addonsGroupFetcher.submit(fetcherData, { method: "POST" });
    onClose();
  };
  if (!isOpen) return null;
  return /* @__PURE__ */ jsxRuntimeExports.jsx(Dialog, { open: isOpen, onOpenChange: onClose, children: /* @__PURE__ */ jsxRuntimeExports.jsxs(DialogContent, { className: "max-w-md p-6 bg-white rounded-xl shadow-2xl sm:max-w-lg", children: [
    /* @__PURE__ */ jsxRuntimeExports.jsx(DialogTitle, { className: "text-xl font-bold text-gray-900 sm:text-2xl", children: header }),
    /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "mt-4 space-y-5 max-h-[60vh] overflow-y-auto pr-2 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "space-y-4", children: [
        { id: 1, name: "internalName", type: "text", label: "InternalName" },
        { id: 2, name: "displayName", type: "text", label: "DisplayName" },
        { id: 3, name: "description", type: "text", label: "Description" }
      ].map((field) => /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex flex-col gap-2 sm:flex-row sm:items-center", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx("label", { className: "w-full text-sm font-medium text-gray-700 sm:w-1/3", children: field.label }),
        /* @__PURE__ */ jsxRuntimeExports.jsx(
          "input",
          {
            type: field.type,
            name: field.name,
            value: (formData == null ? void 0 : formData[field.name]) ?? "",
            onChange: handleChange,
            className: "w-full rounded-lg border-gray-300 bg-gray-50 p-2.5 text-sm text-gray-900 shadow-sm focus:border-blue-500 focus:ring-2 focus:ring-blue-500 sm:w-2/3",
            placeholder: `Enter ${field.label}`
          }
        )
      ] }, field.name)) }),
      /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex flex-col gap-2 sm:flex-row sm:items-center", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx("label", { className: "w-full text-sm font-medium text-gray-700 sm:w-1/3", children: "Active Status" }),
        /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex gap-6 w-full sm:w-2/3", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsxs("label", { className: "flex items-center gap-2 text-sm text-gray-600 cursor-pointer", children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx(
              "input",
              {
                type: "radio",
                name: "active",
                value: "true",
                checked: (formData == null ? void 0 : formData.active) === true,
                onChange: handleChange,
                className: "h-4 w-4 text-blue-600 focus:ring-blue-500"
              }
            ),
            "Yes"
          ] }),
          /* @__PURE__ */ jsxRuntimeExports.jsxs("label", { className: "flex items-center gap-2 text-sm text-gray-600 cursor-pointer", children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx(
              "input",
              {
                type: "radio",
                name: "active",
                value: "false",
                checked: (formData == null ? void 0 : formData.active) === false,
                onChange: handleChange,
                className: "h-4 w-4 text-blue-600 focus:ring-blue-500"
              }
            ),
            "No"
          ] })
        ] })
      ] })
    ] }),
    /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "mt-6 flex flex-col gap-3 sm:flex-row sm:justify-end", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx(
        "button",
        {
          onClick: onClose,
          className: "w-full rounded-lg bg-gray-100 px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-300 sm:w-auto",
          children: "Cancel"
        }
      ),
      /* @__PURE__ */ jsxRuntimeExports.jsxs(
        "button",
        {
          onClick: handleSave,
          disabled: loading,
          className: `w-full rounded-lg ${loading ? "bg-gray-400" : "bg-blue-600"} px-4 py-2 text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 sm:w-auto`,
          children: [
            loading ? "Saving..." : "Save",
            " "
          ]
        }
      )
    ] })
  ] }) });
};
const myAddonGroupHeader = [
  "Id",
  "InternalName",
  "DisplayName",
  "Description",
  "Active",
  "",
  ""
];
const MyAddonsGroupTab = ({
  addonGroupTabData,
  sellerId
}) => {
  const [searchTerm, setSearchTerm] = reactExports.useState("");
  const addOnGroupFetcher = useFetcher();
  const handleDelete = (addgroupData) => {
    const formData = new FormData();
    formData.append("actionType", "addonsgroupDelete");
    formData.append("addonGId", addgroupData == null ? void 0 : addgroupData.id.toString());
    formData.append("sellerId", sellerId.toString());
    addOnGroupFetcher.submit(formData, { method: "post" });
  };
  const navigate = useNavigate();
  const [selectedAddonsGData, setSelectedAddonsGData] = reactExports.useState();
  const [isAddonsGEdit, setIsAddonsGEdit] = reactExports.useState(false);
  const [addonGroupModalOpen, setAddonGroupModalOpen] = reactExports.useState(false);
  const handleEditModal = (row) => {
    setSelectedAddonsGData(row);
    setIsAddonsGEdit(true);
    setAddonGroupModalOpen(true);
  };
  const handleAddModal = () => {
    setSelectedAddonsGData(void 0);
    setIsAddonsGEdit(false);
    setAddonGroupModalOpen(true);
  };
  const [filteredGroupData, setFilteredGroupData] = reactExports.useState(addonGroupTabData);
  const debouncedSearchTerm = useDebounce(searchTerm, 300);
  reactExports.useEffect(() => {
    if (debouncedSearchTerm.length >= 3 && debouncedSearchTerm !== "") {
      const filtered = addonGroupTabData.filter(
        (item) => [item.displayName, item.internalName].some(
          (field) => field == null ? void 0 : field.toLowerCase().includes(debouncedSearchTerm.toLowerCase())
        )
      );
      setFilteredGroupData(filtered);
    } else {
      setFilteredGroupData(addonGroupTabData);
    }
  }, [debouncedSearchTerm, addonGroupTabData]);
  const loading = addOnGroupFetcher.state !== "idle";
  return /* @__PURE__ */ jsxRuntimeExports.jsxs(jsxRuntimeExports.Fragment, { children: [
    /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex justify-between mb-4", children: [
      loading && /* @__PURE__ */ jsxRuntimeExports.jsx(SpinnerLoader, { loading, size: 20 }),
      /* @__PURE__ */ jsxRuntimeExports.jsx(
        Input,
        {
          placeholder: "Search by Internal Name && display Name",
          value: searchTerm,
          type: "search",
          onChange: (e) => setSearchTerm(e.target.value),
          className: "max-w-sm mt-2 rounded-full "
        }
      )
    ] }),
    filteredGroupData.length === 0 ? /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "text-center py-4", children: "No results found" }) : /* @__PURE__ */ jsxRuntimeExports.jsx(
      ResponsiveTable,
      {
        headers: myAddonGroupHeader,
        data: filteredGroupData,
        renderRow: (row) => /* @__PURE__ */ jsxRuntimeExports.jsxs("tr", { className: "border-b", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx("td", { className: "py-2 px-3 text-center whitespace-normal break-words ", children: row.id }),
          /* @__PURE__ */ jsxRuntimeExports.jsx("td", { className: "py-2 px-3 text-center whitespace-normal break-words text-blue-600 cursor-pointer ", onClick: () => navigate(`/home/<USER>
          /* @__PURE__ */ jsxRuntimeExports.jsx("td", { className: "py-2 px-3 text-center whitespace-normal break-words", children: row == null ? void 0 : row.displayName }),
          /* @__PURE__ */ jsxRuntimeExports.jsx("td", { className: "py-2 px-3 text-center whitespace-normal break-words", children: row == null ? void 0 : row.description }),
          /* @__PURE__ */ jsxRuntimeExports.jsx("td", { className: "py-2 px-3 text-center whitespace-normal break-words", children: (row == null ? void 0 : row.active) ? /* @__PURE__ */ jsxRuntimeExports.jsxs("span", { className: "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800", children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx(
              "svg",
              {
                className: "w-4 h-4 mr-1 text-green-500",
                fill: "currentColor",
                viewBox: "0 0 20 20",
                xmlns: "http://www.w3.org/2000/svg",
                children: /* @__PURE__ */ jsxRuntimeExports.jsx(
                  "path",
                  {
                    fillRule: "evenodd",
                    d: "M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",
                    clipRule: "evenodd"
                  }
                )
              }
            ),
            "Active"
          ] }) : /* @__PURE__ */ jsxRuntimeExports.jsxs("span", { className: "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800", children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx(
              "svg",
              {
                className: "w-4 h-4 mr-1 text-red-500",
                fill: "currentColor",
                viewBox: "0 0 20 20",
                xmlns: "http://www.w3.org/2000/svg",
                children: /* @__PURE__ */ jsxRuntimeExports.jsx(
                  "path",
                  {
                    fillRule: "evenodd",
                    d: "M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 001.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",
                    clipRule: "evenodd"
                  }
                )
              }
            ),
            "Inactive"
          ] }) }),
          /* @__PURE__ */ jsxRuntimeExports.jsx("td", { className: "py-2 px-3 text-center cursor-pointer", children: /* @__PURE__ */ jsxRuntimeExports.jsx(
            Button,
            {
              variant: "ghost",
              size: "sm",
              className: "text-red-500 hover:text-red-900",
              onClick: () => {
                if (confirm("Are you sure you want to delete this addonsGroup?")) {
                  handleDelete(row);
                }
              },
              style: { alignSelf: "flex-end" },
              children: /* @__PURE__ */ jsxRuntimeExports.jsx(Trash, { size: 20 })
            }
          ) }),
          /* @__PURE__ */ jsxRuntimeExports.jsx("td", { className: "py-2 px-3 text-center cursor-pointer", children: /* @__PURE__ */ jsxRuntimeExports.jsx(Pencil, { color: "blue", size: 20, onClick: () => handleEditModal(row) }) })
        ] }, row.id)
      }
    ),
    /* @__PURE__ */ jsxRuntimeExports.jsx(
      AddonsGroupModal,
      {
        isOpen: addonGroupModalOpen,
        data: isAddonsGEdit ? selectedAddonsGData : void 0,
        onClose: () => setAddonGroupModalOpen(false),
        sellerId,
        header: isAddonsGEdit ? "Edit AddonGroup" : "Add AddonGroup"
      }
    ),
    /* @__PURE__ */ jsxRuntimeExports.jsx(Button, { className: "fixed bottom-5 right-5 rounded-full cursor-pointer", onClick: () => handleAddModal(), children: "+ Add AddonsGroup" })
  ] });
};
const AddonsModal = ({ isOpen, data, onClose, onSave, header, sellerId }) => {
  const [formData, setFormData] = reactExports.useState(data);
  const [loading, setLoading] = reactExports.useState(false);
  reactExports.useEffect(() => {
    setFormData(data);
  }, [data]);
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prevData) => ({
      ...prevData,
      [name]: name === "active" ? value === "true" : value
      // Convert active to boolean
    }));
  };
  const addonFetcher = useFetcher();
  const handleSave = () => {
    const fetcherData = new FormData();
    console.log(formData, "kkkkkkkkkkkkkkkkk");
    fetcherData.append("actionType", "addonsAdd");
    fetcherData.append("sellerId", sellerId);
    fetcherData.append("addonData", JSON.stringify(formData));
    addonFetcher.submit(fetcherData, { method: "POST" });
    onClose();
  };
  if (!isOpen) return null;
  return /* @__PURE__ */ jsxRuntimeExports.jsx(Dialog, { open: isOpen, onOpenChange: onClose, children: /* @__PURE__ */ jsxRuntimeExports.jsxs(DialogContent, { className: "max-w-md p-6 bg-white rounded-xl shadow-2xl sm:max-w-lg", children: [
    /* @__PURE__ */ jsxRuntimeExports.jsx(DialogTitle, { className: "text-xl font-bold text-gray-900 sm:text-2xl", children: header }),
    /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "mt-4 space-y-5 max-h-[60vh] overflow-y-auto pr-2 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "space-y-4", children: [
        { name: "name", type: "text", label: "Name" },
        { name: "diet", type: "select", label: "Diet" }
      ].map((field) => /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex flex-col gap-2 sm:flex-row sm:items-center", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx("label", { className: "w-full text-sm font-medium text-gray-700 sm:w-1/3", children: field.label }),
        field.name === "diet" ? /* @__PURE__ */ jsxRuntimeExports.jsxs(
          "select",
          {
            name: field.name,
            value: (formData == null ? void 0 : formData[field.name]) || "",
            onChange: handleChange,
            className: "w-full rounded-lg border-gray-300 bg-gray-50 p-2.5 text-sm text-gray-900 shadow-sm focus:border-blue-500 focus:ring-2 focus:ring-blue-500 sm:w-2/3",
            children: [
              /* @__PURE__ */ jsxRuntimeExports.jsx("option", { value: "", children: "Select Diet" }),
              /* @__PURE__ */ jsxRuntimeExports.jsx("option", { value: "veg", children: "Veg" }),
              /* @__PURE__ */ jsxRuntimeExports.jsx("option", { value: "nonveg", children: "Nonveg" }),
              /* @__PURE__ */ jsxRuntimeExports.jsx("option", { value: "egg", children: "Egg" }),
              /* @__PURE__ */ jsxRuntimeExports.jsx("option", { value: "vegan", children: "vegan" }),
              /* @__PURE__ */ jsxRuntimeExports.jsx("option", { value: "na", children: "Na" })
            ]
          }
        ) : /* @__PURE__ */ jsxRuntimeExports.jsx(
          "input",
          {
            type: field.type,
            name: field.name,
            value: (formData == null ? void 0 : formData[field.name]) || "",
            onChange: handleChange,
            className: "w-full rounded-lg border-gray-300 bg-gray-50 p-2.5 text-sm text-gray-900 shadow-sm focus:border-blue-500 focus:ring-2 focus:ring-blue-500 sm:w-2/3",
            placeholder: `Enter ${field.label}`
          }
        )
      ] }, field.name)) }),
      /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex flex-col gap-2 sm:flex-row sm:items-center", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx("label", { className: "w-full text-sm font-medium text-gray-700 sm:w-1/3", children: "Active Status" }),
        /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex gap-6 w-full sm:w-2/3", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsxs("label", { className: "flex items-center gap-2 text-sm text-gray-600 cursor-pointer", children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx(
              "input",
              {
                type: "radio",
                name: "active",
                value: "true",
                checked: (formData == null ? void 0 : formData.active) === true,
                onChange: handleChange,
                className: "h-4 w-4 text-blue-600 focus:ring-blue-500"
              }
            ),
            "Yes"
          ] }),
          /* @__PURE__ */ jsxRuntimeExports.jsxs("label", { className: "flex items-center gap-2 text-sm text-gray-600 cursor-pointer", children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx(
              "input",
              {
                type: "radio",
                name: "active",
                value: "false",
                checked: (formData == null ? void 0 : formData.active) === false,
                onChange: handleChange,
                className: "h-4 w-4 text-blue-600 focus:ring-blue-500"
              }
            ),
            "No"
          ] })
        ] })
      ] })
    ] }),
    /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "mt-6 flex flex-col gap-3 sm:flex-row sm:justify-end", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx(
        "button",
        {
          onClick: onClose,
          className: "w-full rounded-lg bg-gray-100 px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-300 sm:w-auto",
          children: "Cancel"
        }
      ),
      /* @__PURE__ */ jsxRuntimeExports.jsxs(
        "button",
        {
          onClick: handleSave,
          disabled: loading,
          className: `w-full rounded-lg ${loading ? "bg-gray-400" : "bg-blue-600"} px-4 py-2 text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 sm:w-auto`,
          children: [
            loading ? "Saving..." : "Save",
            " "
          ]
        }
      )
    ] })
  ] }) });
};
const myAddonsHeaders = [
  "Id",
  "Name",
  "Diet",
  "Active",
  "",
  ""
];
const MyAddonsTab = ({
  addonsList,
  searchTerm,
  setSearchTerm,
  pageSize,
  setPageSize,
  pageNum,
  setPageNum,
  isItemModalOpen,
  setIsItemModalOpen,
  selectedItem,
  setSelectedItem,
  sellerId
}) => {
  const handlePageSearch = (value) => {
    setSearchTerm(value);
  };
  const handlePageSizeChange = (newPageSize) => {
    setPageSize(newPageSize);
  };
  const handlePageChange = (newPageNum) => {
    setPageNum(newPageNum);
  };
  const addonFetcher = useFetcher();
  const handleSave = (addonsData) => {
    const formData = new FormData();
    console.log("kkkkkkkkkkkkkkkkk");
    formData.append("actionType", "addonsAdd");
    formData.append("addonData", JSON.stringify(addonsData));
    addonFetcher.submit(formData, { method: "POST" });
  };
  const handleDelete = (addonsData) => {
    const formData = new FormData();
    formData.append("actionType", "addonsdelete");
    formData.append("addonId", addonsData.id.toString());
    formData.append("sellerId", sellerId.toString());
    addonFetcher.submit(formData, { method: "post" });
  };
  return /* @__PURE__ */ jsxRuntimeExports.jsxs(jsxRuntimeExports.Fragment, { children: [
    /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex justify-between mb-4", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx(
        Input,
        {
          placeholder: "Search by Item Name",
          value: searchTerm,
          onChange: (e) => handlePageSearch(e.target.value),
          className: "max-w-sm rounded-full"
        }
      ),
      /* @__PURE__ */ jsxRuntimeExports.jsxs(Select, { value: pageSize, onValueChange: handlePageSizeChange, children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx(SelectTrigger, { className: "w-[180px]", children: /* @__PURE__ */ jsxRuntimeExports.jsx(SelectValue, { placeholder: "Items per page" }) }),
        /* @__PURE__ */ jsxRuntimeExports.jsxs(SelectContent, { children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx(SelectItem, { value: "5", children: "5 per page" }),
          /* @__PURE__ */ jsxRuntimeExports.jsx(SelectItem, { value: "10", children: "10 per page" }),
          /* @__PURE__ */ jsxRuntimeExports.jsx(SelectItem, { value: "20", children: "20 per page" }),
          /* @__PURE__ */ jsxRuntimeExports.jsx(SelectItem, { value: "50", children: "50 per page" })
        ] })
      ] })
    ] }),
    /* @__PURE__ */ jsxRuntimeExports.jsx(
      ResponsiveTable,
      {
        headers: myAddonsHeaders,
        data: addonsList,
        renderRow: (row) => /* @__PURE__ */ jsxRuntimeExports.jsxs("tr", { className: "border-b", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx("td", { className: "py-2 px-3 text-center whitespace-normal break-words ", children: row.id }),
          /* @__PURE__ */ jsxRuntimeExports.jsx("td", { className: "py-2 px-3 text-center whitespace-normal break-words ", children: row == null ? void 0 : row.name }),
          /* @__PURE__ */ jsxRuntimeExports.jsx("td", { className: "py-2 px-3 text-center whitespace-normal break-words", children: row == null ? void 0 : row.diet }),
          /* @__PURE__ */ jsxRuntimeExports.jsx("td", { className: "py-2 px-3 text-center whitespace-normal break-words", children: (row == null ? void 0 : row.active) ? /* @__PURE__ */ jsxRuntimeExports.jsxs("span", { className: "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800", children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx(
              "svg",
              {
                className: "w-4 h-4 mr-1 text-green-500",
                fill: "currentColor",
                viewBox: "0 0 20 20",
                xmlns: "http://www.w3.org/2000/svg",
                children: /* @__PURE__ */ jsxRuntimeExports.jsx(
                  "path",
                  {
                    fillRule: "evenodd",
                    d: "M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",
                    clipRule: "evenodd"
                  }
                )
              }
            ),
            "Active"
          ] }) : /* @__PURE__ */ jsxRuntimeExports.jsxs("span", { className: "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800", children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx(
              "svg",
              {
                className: "w-4 h-4 mr-1 text-red-500",
                fill: "currentColor",
                viewBox: "0 0 20 20",
                xmlns: "http://www.w3.org/2000/svg",
                children: /* @__PURE__ */ jsxRuntimeExports.jsx(
                  "path",
                  {
                    fillRule: "evenodd",
                    d: "M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 001.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",
                    clipRule: "evenodd"
                  }
                )
              }
            ),
            "Inactive"
          ] }) }),
          /* @__PURE__ */ jsxRuntimeExports.jsx("td", { className: "py-2 px-3 text-center cursor-pointer", children: /* @__PURE__ */ jsxRuntimeExports.jsx(
            Button,
            {
              variant: "ghost",
              size: "sm",
              className: "text-red-500 hover:text-red-900",
              onClick: () => {
                if (confirm("Are you sure you want to delete this area?")) {
                  handleDelete(row);
                }
              },
              style: { alignSelf: "flex-end" },
              children: /* @__PURE__ */ jsxRuntimeExports.jsx(Trash, { size: 20 })
            }
          ) }),
          /* @__PURE__ */ jsxRuntimeExports.jsx("td", { className: "py-2 px-3 text-center cursor-pointer", children: /* @__PURE__ */ jsxRuntimeExports.jsx(Pencil, { color: "blue", size: 20, onClick: () => {
            setSelectedItem(row);
            setIsItemModalOpen(true);
          } }) })
        ] }, row.id)
      }
    ),
    /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center justify-center space-x-2 py-4 overflow-x-auto whitespace-nowrap", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsxs("h2", { className: "shrink-0", children: [
        "Current Page: ",
        pageNum + 1
      ] }),
      /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "overflow-x-auto", children: /* @__PURE__ */ jsxRuntimeExports.jsx(
        ResponsivePagination,
        {
          totalPages: Number(pageSize),
          currentPage: pageNum,
          onPageChange: handlePageChange
        }
      ) })
    ] }),
    /* @__PURE__ */ jsxRuntimeExports.jsx(
      AddonsModal,
      {
        isOpen: isItemModalOpen,
        data: selectedItem || {},
        onClose: () => {
          setIsItemModalOpen(false);
          setSelectedItem({});
        },
        onSave: () => handleSave,
        sellerId,
        header: selectedItem ? "Edit Addons" : "Add Addons"
      }
    ),
    /* @__PURE__ */ jsxRuntimeExports.jsx(Button, { className: "fixed bottom-5 right-5 rounded-full cursor-pointer", onClick: () => setIsItemModalOpen(true), children: "+ Add Addons" })
  ] });
};
const AddVariation = ({ isOpen, data, onClose, header, sellerId, isEdit }) => {
  const [formData, setFormData] = reactExports.useState(
    data || {
      internalName: "",
      displayName: "",
      groupName: "",
      active: false
    }
  );
  reactExports.useEffect(() => {
    if (data && isEdit) {
      setFormData(data);
    } else {
      setFormData({
        internalName: "",
        displayName: "",
        groupName: "",
        active: false
      });
    }
  }, [data, isOpen]);
  const { showToast } = useToast();
  const variationFetcher = useFetcher();
  reactExports.useEffect(() => {
    var _a, _b, _c;
    if (variationFetcher.data && ((_a = variationFetcher.data) == null ? void 0 : _a.sucess)) {
      showToast("updatedSucessFully", "success");
      onClose();
      setFormData({
        internalName: "",
        displayName: "",
        groupName: "",
        active: false
      });
    } else {
      if ((_b = variationFetcher.data) == null ? void 0 : _b.error) {
        showToast((_c = variationFetcher.data) == null ? void 0 : _c.error, "error");
        onClose();
      }
    }
  }, [variationFetcher.data]);
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prevData) => ({
      ...prevData,
      [name]: name === "active" ? value === "true" : value
    }));
  };
  const handleSave = () => {
    const fetcherData = new FormData();
    fetcherData.append("actionType", "variationAdd");
    fetcherData.append("sellerId", sellerId);
    fetcherData.append("VariationData", JSON.stringify(formData));
    fetcherData.append("isCreating", header === "Add Variation" ? "true" : "false");
    variationFetcher.submit(fetcherData, { method: "POST" });
  };
  if (!isOpen) return null;
  const loading = variationFetcher.state !== "idle";
  const isFormValid = () => {
    return formData.internalName.trim() !== "" && formData.displayName.trim() !== "" && formData.groupName.trim() !== "";
  };
  return /* @__PURE__ */ jsxRuntimeExports.jsxs(Dialog, { open: isOpen, onOpenChange: onClose, children: [
    loading && /* @__PURE__ */ jsxRuntimeExports.jsx(SpinnerLoader, { loading, size: 20 }),
    /* @__PURE__ */ jsxRuntimeExports.jsxs(DialogContent, { className: "max-w-md p-6 bg-white rounded-xl shadow-2xl sm:max-w-lg", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx(DialogTitle, { className: "text-xl font-bold text-gray-900 sm:text-2xl", children: header }),
      /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "mt-4 space-y-5 max-h-[60vh] overflow-y-auto pr-2 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "space-y-4", children: [
          { id: 1, name: "internalName", type: "text", label: "InternalName" },
          { id: 2, name: "displayName", type: "text", label: "DisplayName" },
          { id: 3, name: "groupName", type: "text", label: "GroupName" }
        ].map((field) => /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex flex-col gap-2 sm:flex-row sm:items-center", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx("label", { className: "w-full text-sm font-medium text-gray-700 sm:w-1/3", children: field.label }),
          /* @__PURE__ */ jsxRuntimeExports.jsx(
            "input",
            {
              type: field.type,
              name: field.name,
              value: (formData == null ? void 0 : formData[field.name]) ?? "",
              onChange: handleChange,
              className: "w-full rounded-lg border-gray-300 bg-gray-50 p-2.5 text-sm text-gray-900 shadow-sm focus:border-blue-500 focus:ring-2 focus:ring-blue-500 sm:w-2/3 border",
              placeholder: `Enter ${field.label}`
            }
          )
        ] }, field.name)) }),
        /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex flex-col gap-2 sm:flex-row sm:items-center", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx("label", { className: "w-full text-sm font-medium text-gray-700 sm:w-1/3", children: "Active Status" }),
          /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex gap-6 w-full sm:w-2/3", children: [
            /* @__PURE__ */ jsxRuntimeExports.jsxs("label", { className: "flex items-center gap-2 text-sm text-gray-600 cursor-pointer", children: [
              /* @__PURE__ */ jsxRuntimeExports.jsx(
                "input",
                {
                  type: "radio",
                  name: "active",
                  value: "true",
                  checked: (formData == null ? void 0 : formData.active) === true,
                  onChange: handleChange,
                  className: "h-4 w-4 text-blue-600 focus:ring-blue-500",
                  autoFocus: true
                }
              ),
              "Yes"
            ] }),
            /* @__PURE__ */ jsxRuntimeExports.jsxs("label", { className: "flex items-center gap-2 text-sm text-gray-600 cursor-pointer", children: [
              /* @__PURE__ */ jsxRuntimeExports.jsx(
                "input",
                {
                  type: "radio",
                  name: "active",
                  value: "false",
                  checked: (formData == null ? void 0 : formData.active) === false,
                  onChange: handleChange,
                  className: "h-4 w-4 text-blue-600 focus:ring-blue-500"
                }
              ),
              "No"
            ] })
          ] })
        ] })
      ] }),
      /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "mt-6 flex flex-col gap-3 sm:flex-row sm:justify-end", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx(
          "button",
          {
            onClick: onClose,
            className: "w-full rounded-lg bg-gray-100 px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-300 sm:w-auto",
            children: "Cancel"
          }
        ),
        /* @__PURE__ */ jsxRuntimeExports.jsx(
          "button",
          {
            onClick: handleSave,
            disabled: loading || !isFormValid(),
            className: `w-full rounded-lg px-4 py-2 text-sm font-medium text-white focus:outline-none sm:w-auto ${loading || !isFormValid() ? "bg-gray-400 cursor-not-allowed" : "bg-blue-600 hover:bg-blue-700 focus:ring-2 focus:ring-blue-500"}`,
            children: loading ? "Saving..." : "Save"
          }
        )
      ] })
    ] })
  ] });
};
const myVariationHeader = [
  "Id",
  "InternalName",
  "DisplayName",
  "Group",
  "Active",
  "",
  ""
];
const VariationTab = ({
  variationData,
  sellerId
}) => {
  const variationFetcher = useFetcher();
  const [isItemModalOpen, setIsItemModalOpen] = reactExports.useState(false);
  const [selectedVarData, setSelectedVarData] = reactExports.useState();
  const [isVarEdit, setIsVarEdit] = reactExports.useState(false);
  const [searchTerm, setSearchTerm] = reactExports.useState("");
  const [filteredVariations, setFilteredVariations] = reactExports.useState([]);
  const handleDelete = (variation) => {
    var _a;
    const formData = new FormData();
    formData.append("actionType", "vardelete");
    formData.append("varId", ((_a = variation == null ? void 0 : variation.id) == null ? void 0 : _a.toString()) ?? "");
    variationFetcher.submit(formData, { method: "post" });
  };
  const handleEditModal = (row) => {
    setSelectedVarData(row);
    setIsVarEdit(true);
    setIsItemModalOpen(true);
  };
  const handleAddModal = () => {
    setSelectedVarData(void 0);
    setIsVarEdit(false);
    setIsItemModalOpen(true);
  };
  const loading = variationFetcher.state !== "idle";
  const debouncedSearchTerm = useDebounce(searchTerm, 500);
  reactExports.useEffect(() => {
    if (debouncedSearchTerm.length >= 2 && debouncedSearchTerm !== "") {
      const filtered = variationData.filter(
        (item) => [item.displayName, item.internalName].some(
          (field) => field == null ? void 0 : field.toLowerCase().includes(debouncedSearchTerm.toLowerCase())
        )
      );
      setFilteredVariations(filtered);
    } else {
      setFilteredVariations(variationData);
    }
  }, [debouncedSearchTerm, variationData]);
  useNavigate();
  return /* @__PURE__ */ jsxRuntimeExports.jsxs(jsxRuntimeExports.Fragment, { children: [
    /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex justify-between mb-4", children: [
      loading && /* @__PURE__ */ jsxRuntimeExports.jsx(SpinnerLoader, { loading, size: 20 }),
      /* @__PURE__ */ jsxRuntimeExports.jsx(
        Input,
        {
          placeholder: "Search by Internal Name or display Name",
          value: searchTerm,
          type: "search",
          onChange: (e) => setSearchTerm(e.target.value),
          className: "max-w-sm mt-2 rounded-full "
        }
      )
    ] }),
    /* @__PURE__ */ jsxRuntimeExports.jsx(
      ResponsiveTable,
      {
        headers: myVariationHeader,
        data: filteredVariations,
        renderRow: (row) => /* @__PURE__ */ jsxRuntimeExports.jsxs("tr", { className: "border-b", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx("td", { className: "py-2 px-3 text-center whitespace-normal break-words ", children: row.id }),
          /* @__PURE__ */ jsxRuntimeExports.jsx("td", { className: "py-2 px-3 text-center whitespace-normal break-words text-blue-300 cursor-pointer", children: (row == null ? void 0 : row.internalName) || "-" }),
          /* @__PURE__ */ jsxRuntimeExports.jsx("td", { className: "py-2 px-3 text-center whitespace-normal break-words", children: (row == null ? void 0 : row.displayName) || "-" }),
          /* @__PURE__ */ jsxRuntimeExports.jsx("td", { className: "py-2 px-3 text-center whitespace-normal break-words", children: row == null ? void 0 : row.groupName }),
          /* @__PURE__ */ jsxRuntimeExports.jsx("td", { className: "py-2 px-3 text-center whitespace-normal break-words", children: (row == null ? void 0 : row.active) ? /* @__PURE__ */ jsxRuntimeExports.jsxs("span", { className: "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800", children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx(
              "svg",
              {
                className: "w-4 h-4 mr-1 text-green-500",
                fill: "currentColor",
                viewBox: "0 0 20 20",
                xmlns: "http://www.w3.org/2000/svg",
                children: /* @__PURE__ */ jsxRuntimeExports.jsx(
                  "path",
                  {
                    fillRule: "evenodd",
                    d: "M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",
                    clipRule: "evenodd"
                  }
                )
              }
            ),
            "Active"
          ] }) : /* @__PURE__ */ jsxRuntimeExports.jsxs("span", { className: "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800", children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx(
              "svg",
              {
                className: "w-4 h-4 mr-1 text-red-500",
                fill: "currentColor",
                viewBox: "0 0 20 20",
                xmlns: "http://www.w3.org/2000/svg",
                children: /* @__PURE__ */ jsxRuntimeExports.jsx(
                  "path",
                  {
                    fillRule: "evenodd",
                    d: "M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 001.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",
                    clipRule: "evenodd"
                  }
                )
              }
            ),
            "Inactive"
          ] }) }),
          /* @__PURE__ */ jsxRuntimeExports.jsx("td", { className: "py-2 px-3 text-center cursor-pointer", children: /* @__PURE__ */ jsxRuntimeExports.jsx(
            Button,
            {
              variant: "ghost",
              size: "sm",
              className: "text-red-500 hover:text-red-900",
              onClick: () => {
                if (confirm(`Are you sure you want to delete this Variation? ${row.displayName}`)) {
                  handleDelete(row);
                }
              },
              style: { alignSelf: "flex-end" },
              children: /* @__PURE__ */ jsxRuntimeExports.jsx(Trash, { size: 20 })
            }
          ) }),
          /* @__PURE__ */ jsxRuntimeExports.jsx("td", { className: "py-2 px-3 text-center cursor-pointer", children: /* @__PURE__ */ jsxRuntimeExports.jsx(Pencil, { color: "blue", size: 20, onClick: () => handleEditModal(row) }) })
        ] }, row.id)
      }
    ),
    /* @__PURE__ */ jsxRuntimeExports.jsx(
      AddVariation,
      {
        isOpen: isItemModalOpen,
        isEdit: isVarEdit,
        data: isVarEdit ? selectedVarData : void 0,
        onClose: () => setIsItemModalOpen(false),
        header: isVarEdit ? "Edit Variation" : "Add Variation",
        sellerId
      }
    ),
    /* @__PURE__ */ jsxRuntimeExports.jsx(Button, { className: "fixed bottom-5 right-5 rounded-full cursor-pointer", onClick: () => handleAddModal(), children: "+ Add Variation" })
  ] });
};
const getPolygonColor = (index) => {
  const colors = ["#3b82f6", "#10b981", "#f59e0b", "#ef4444", "#8b5cf6", "#ec4899", "#06b6d4", "#f97316"];
  return colors[index % colors.length];
};
function SellerDetailsPage() {
  const {
    googleMapsApiKey,
    sellerId,
    sellerName,
    activeTab,
    sellerAreas,
    sellerConfig,
    url,
    sellerUser,
    roles,
    sellerBId,
    businessConfig,
    sellerItems,
    statesAndDistricts,
    userId,
    permission,
    currentPage,
    addonGroupList,
    addonsList,
    variationList
  } = useLoaderData();
  sellerAreas ? sellerAreas.length : "";
  const fetchfor = useNavigate();
  const adminBasic = permission && Array.isArray(permission) ? permission.includes("AC_Basic") : false;
  const handleTabChange = (newTab) => {
    if (newTab === "SellerCategories") {
      fetchfor(`/home/<USER>/sellerCategories?sellerId=${sellerId}&sellerName=${sellerName}&activeTab=${newTab}&sellerBId=${sellerBId}`);
    } else if (newTab === "DeliveryConfig") {
      fetchfor(`/home/<USER>/deliveryconfig?sellerId=${sellerId}&sellerName=${sellerName}&activeTab=${newTab}&sellerBId=${sellerBId}`);
    } else {
      fetchfor(`/home/<USER>
    }
  };
  const {
    showToast
  } = useToast();
  const [visibleAreas, setVisibleAreas] = reactExports.useState(/* @__PURE__ */ new Set());
  const [isLoading, setIsLoading] = reactExports.useState(true);
  const [isModalOpen, setIsModalOpen] = reactExports.useState(false);
  const [isModalOpenEdit, setIsModalOpenEdit] = reactExports.useState(false);
  const [map, setMap] = reactExports.useState(null);
  const [mapLoaded, setMapLoaded] = reactExports.useState(false);
  const onLoad = reactExports.useCallback((mapInstance) => {
    setMap(mapInstance);
    setMapLoaded(true);
  }, []);
  const [isLocateShopClicked, setisLocateShopClicked] = reactExports.useState(false);
  const [pointerLocation, setPointerLocation] = reactExports.useState({
    latitude: null,
    longitude: null
  });
  const fetcher = useFetcher();
  const handleLocateShopClicked = reactExports.useCallback((state) => {
    setisLocateShopClicked(state);
  }, []);
  reactExports.useEffect(() => {
    if (map && sellerAreas) {
      const decodedPolygons = sellerAreas.map((area) => ({
        id: area.sellerAreaId,
        paths: area.area.polygon ? decodePolygon(area.area.polygon) : []
      }));
      setVisibleAreas(new Set(decodedPolygons.map((polygon) => polygon.id)));
    }
  }, [map, sellerAreas]);
  reactExports.useEffect(() => {
    if (googleMapsApiKey && sellerAreas) {
      if (mapLoaded) {
        setIsLoading(false);
      }
    }
  }, [googleMapsApiKey, sellerAreas, mapLoaded]);
  const onUnmount = reactExports.useCallback(() => {
    setMap(null);
    setMapLoaded(false);
  }, []);
  const getPolygonCenter = (paths) => {
    const latitudes = paths.map((path) => path.lat);
    const longitudes = paths.map((path) => path.lng);
    const latSum = latitudes.reduce((a, b) => a + b, 0);
    const lngSum = longitudes.reduce((a, b) => a + b, 0);
    return {
      lat: latSum / latitudes.length,
      lng: lngSum / longitudes.length
    };
  };
  const UserHeaders = ["Id", "Name", "Business Name", "Mobile Number", "Cash With User", "Roles", "Status", ""];
  const sellerItemHeader = ["Name", "WtFactor", "MaxAvQty", "MinMax", "Inc-MaxOrd", "Price", "Supp", "Distcharg(PC-PU)", "AcPkg(min-max)", "DistcPkg(min-max)", ""];
  const navigate = useNavigate();
  const handleUpdate = async (data) => {
    try {
      const formData = new FormData();
      formData.append("updateType", "seller");
      formData.append("sellerId", sellerId.toString());
      formData.append("updates", JSON.stringify(data));
      console.log("handleUpdate called with data:", data);
      for (const [key, value] of formData.entries()) {
        console.log(key, value);
      }
      fetcher.submit(formData, {
        method: "POST"
      });
      console.log("handleUpdate: API call submitted");
      showToast("Seller configuration updated successfully", "success");
    } catch (error) {
      console.error("Error in handleUpdate:", error);
      showToast("Failed to update seller configuration", "error");
    }
  };
  const onSellerAttributeUpdate = async (attribute, value) => {
    try {
      const formData = new FormData();
      formData.append("updateType", "billingConfig");
      formData.append("intent", "updateBillingConfig");
      formData.append("sellerId", sellerId.toString());
      formData.append("attribute", attribute);
      formData.append("value", value.toString());
      await fetcher.submit(formData, {
        method: "POST"
      });
      showToast(`${attribute.replace(/([A-Z])/g, " $1")} updated successfully`, "success");
    } catch (error) {
      showToast(`Failed to update ${attribute.replace(/([A-Z])/g, " $1")}`, "error");
    }
  };
  const [toggleUserStatus, setToggleUserStatus] = reactExports.useState(() => sellerUser == null ? void 0 : sellerUser.reduce((acc, user2) => {
    acc[user2 == null ? void 0 : user2.userId] = user2 == null ? void 0 : user2.disabled;
    return acc;
  }, {}));
  const updateToggleUser = (userId2) => {
    const formData = new FormData();
    formData.append("userId", userId2);
    formData.append("intent", "updateUser");
    fetcher.submit(formData, {
      method: "put"
    });
    if (fetcher.state === "idle") {
      showToast("User Status Updated Success ", "success");
    }
  };
  const handleSwitchUser = async (userId2) => {
    setToggleUserStatus((prev) => ({
      ...prev,
      [userId2]: !prev[userId2]
    }));
    updateToggleUser(userId2);
  };
  const updateToggle = (areaId) => {
    const formData = new FormData();
    formData.append("sellerId", sellerId);
    formData.append("areaId", areaId);
    formData.append("intent", "updateArea");
    fetcher.submit(formData, {
      method: "POST"
    });
  };
  const [user, setUser] = reactExports.useState();
  const handleEdit = (row) => {
    setUser(row);
    setIsModalOpenEdit(true);
  };
  const handleCreateAreas = (areaIds) => {
    const formData = new FormData();
    formData.append("intent", "createSellerArea");
    formData.append("areaIds", JSON.stringify(areaIds));
    formData.append("sellerId", sellerId.toString());
    fetcher.submit(formData, {
      method: "post"
    });
  };
  const loading = fetcher.state !== "idle";
  const [searchTerm, setSearchTerm] = reactExports.useState("");
  const [pageSize, setPageSize] = reactExports.useState("50");
  const [pageNum, setPageNum] = reactExports.useState(0);
  const handlePageSizeChange = (newPageSize) => {
    setPageSize(newPageSize);
    navigate(`?sellerId=${sellerId}&sellerName=${sellerName}&activeTab=${activeTab}&sellerBId=${sellerBId}&page=${currentPage}&pageSize=${newPageSize}&matchBy=${searchTerm}`);
  };
  const debounceSearchTerm = useDebounce(searchTerm, 300);
  const handlePageSearch = (value) => {
    setSearchTerm(value);
  };
  reactExports.useEffect(() => {
    if (debounceSearchTerm.length >= 3) {
      navigate(`?sellerId=${sellerId}&sellerName=${sellerName}&activeTab=${activeTab}&sellerBId=${sellerBId}&page=${currentPage}&pageSize=${pageSize}&matchBy=${encodeURIComponent(debounceSearchTerm)}`);
    } else {
      navigate(`?sellerId=${sellerId}&sellerName=${sellerName}&activeTab=${activeTab}&sellerBId=${sellerBId}&page=${currentPage}&pageSize=${pageSize}`);
    }
  }, [debounceSearchTerm]);
  const handlePageChange = (newPageSize) => {
    setPageNum(Number(newPageSize));
    navigate(`?sellerId=${sellerId}&sellerName=${sellerName}&activeTab=${activeTab}&sellerBId=${sellerBId}&page=${Number(newPageSize)}&pageSize=${pageSize}&matchBy=${searchTerm}`);
  };
  const [selectedItem, setSelectedItem] = reactExports.useState({});
  const [isItemModalOpen, setIsItemModalOpen] = reactExports.useState(false);
  const [supplierList, setSupplierList] = reactExports.useState([]);
  const handleEditModal = reactExports.useCallback(async (row) => {
    var _a;
    try {
      const response = await fetch(`./api/supplierItems?itemId=${row.Id}`);
      if (!response.ok) {
        throw new Error("Failed to fetch item details");
      }
      const supplier = await response.json();
      setSupplierList((_a = supplier == null ? void 0 : supplier.supplierData) == null ? void 0 : _a.data);
      setSelectedItem(row);
      setIsItemModalOpen(true);
    } catch (error) {
      console.error("Error fetching item:", error);
    }
  }, []);
  const handleSave = (updatedData) => {
    console.log("Updated Data:", updatedData);
    const formData = new FormData();
    formData.append("intent", "editSellerItem");
    formData.append("sellerItem", JSON.stringify(updatedData));
    formData.append("itemId", updatedData.Id);
    formData.append("sellerId", sellerId.toString());
    fetcher.submit(formData, {
      method: "put"
    });
    setIsItemModalOpen(false);
  };
  return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
    className: "h-full",
    children: [loading && /* @__PURE__ */ jsxRuntimeExports.jsx(SpinnerLoader, {
      loading
    }), /* @__PURE__ */ jsxRuntimeExports.jsxs("h1", {
      className: " mb-4 font-bold cursor-pointer",
      onClick: () => navigate("/home/<USER>"),
      children: [" ", /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
        className: "text-2xl",
        children: "Seller Management / "
      }), " ", /* @__PURE__ */ jsxRuntimeExports.jsxs("span", {
        className: "text-xl",
        children: [sellerName, " "]
      }), " "]
    }), /* @__PURE__ */ jsxRuntimeExports.jsxs(Tabs, {
      value: activeTab,
      onValueChange: handleTabChange,
      children: [/* @__PURE__ */ jsxRuntimeExports.jsxs(TabsList, {
        children: [/* @__PURE__ */ jsxRuntimeExports.jsx(TabsTrigger, {
          value: "SellerConfig",
          children: "Configurations"
        }), /* @__PURE__ */ jsxRuntimeExports.jsx(TabsTrigger, {
          value: "SellerAreas",
          children: "Areas"
        }), /* @__PURE__ */ jsxRuntimeExports.jsx(TabsTrigger, {
          value: "SellerUser",
          children: "Users"
        }), /* @__PURE__ */ jsxRuntimeExports.jsx(TabsTrigger, {
          value: "SellerItems",
          children: "SellerItems"
        }), /* @__PURE__ */ jsxRuntimeExports.jsx(TabsTrigger, {
          value: "SellerCategories",
          children: "Seller Categories"
        }), /* @__PURE__ */ jsxRuntimeExports.jsx(TabsTrigger, {
          value: "MyAddonsGroup",
          children: "My AddonsGroups"
        }), /* @__PURE__ */ jsxRuntimeExports.jsx(TabsTrigger, {
          value: "MyAddons",
          children: "My Addons"
        }), /* @__PURE__ */ jsxRuntimeExports.jsx(TabsTrigger, {
          value: "MyVariation",
          children: "My Variations"
        }), /* @__PURE__ */ jsxRuntimeExports.jsx(TabsTrigger, {
          value: "DeliveryConfig",
          children: "DeliveryConfig"
        }), adminBasic && /* @__PURE__ */ jsxRuntimeExports.jsx(TabsTrigger, {
          value: "BillingConfig",
          children: "Bank Details"
        })]
      }), /* @__PURE__ */ jsxRuntimeExports.jsx(Outlet, {}), /* @__PURE__ */ jsxRuntimeExports.jsx(TabsContent, {
        value: "MyAddonsGroup",
        children: /* @__PURE__ */ jsxRuntimeExports.jsx(MyAddonsGroupTab, {
          addonGroupTabData: Array.isArray(addonGroupList) ? addonGroupList : addonGroupList ? [addonGroupList] : [],
          sellerId
        })
      }), /* @__PURE__ */ jsxRuntimeExports.jsx(TabsContent, {
        value: "MyAddons",
        children: /* @__PURE__ */ jsxRuntimeExports.jsx(MyAddonsTab, {
          addonsList: Array.isArray(addonsList) ? addonsList : addonsList ? [addonsList] : [],
          searchTerm,
          setSearchTerm,
          pageSize,
          setPageSize,
          pageNum,
          setPageNum,
          isItemModalOpen,
          setIsItemModalOpen,
          selectedItem,
          setSelectedItem,
          sellerId
        })
      }), /* @__PURE__ */ jsxRuntimeExports.jsx(TabsContent, {
        value: "MyVariation",
        children: /* @__PURE__ */ jsxRuntimeExports.jsx(VariationTab, {
          variationData: Array.isArray(variationList) ? variationList : variationList ? [variationList] : [],
          sellerId
        })
      }), /* @__PURE__ */ jsxRuntimeExports.jsxs(TabsContent, {
        value: "SellerItems",
        children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
          className: "flex justify-between mb-4",
          children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Input, {
            placeholder: "Search by Item Name",
            value: searchTerm,
            type: "search",
            onChange: (e) => handlePageSearch(e.target.value),
            className: "max-w-sm  rounded-full"
          }), /* @__PURE__ */ jsxRuntimeExports.jsxs(Select, {
            value: pageSize,
            onValueChange: handlePageSizeChange,
            children: [/* @__PURE__ */ jsxRuntimeExports.jsx(SelectTrigger, {
              className: "w-[180px]",
              children: /* @__PURE__ */ jsxRuntimeExports.jsx(SelectValue, {
                placeholder: "Items per page"
              })
            }), /* @__PURE__ */ jsxRuntimeExports.jsxs(SelectContent, {
              children: [/* @__PURE__ */ jsxRuntimeExports.jsx(SelectItem, {
                value: "5",
                children: "5 per page"
              }), /* @__PURE__ */ jsxRuntimeExports.jsx(SelectItem, {
                value: "10",
                children: "10 per page"
              }), /* @__PURE__ */ jsxRuntimeExports.jsx(SelectItem, {
                value: "20",
                children: "20 per page"
              }), /* @__PURE__ */ jsxRuntimeExports.jsx(SelectItem, {
                value: "50",
                children: "50 per page"
              })]
            })]
          })]
        }), /* @__PURE__ */ jsxRuntimeExports.jsx(ResponsiveTable, {
          headers: sellerItemHeader,
          data: sellerItems,
          renderRow: (row) => {
            return /* @__PURE__ */ jsxRuntimeExports.jsxs("tr", {
              className: `border-b transition-all duration-200 ${row.freeItem ? "bg-[linear-gradient(88deg,#F0FFF0_0%,#FFF_100%)]" : "hover:bg-gray-100 hover:shadow-sm"} rounded-lg`,
              children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("td", {
                className: "py-2 cursor-pointer ",
                onClick: () => navigate(`/home/<USER>
                children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                  className: "flex items-center gap-1 justify-start p-1 rounded-lg bg-white/50 shadow-sm hover:shadow-md transition-shadow duration-150 w-fit",
                  children: [/* @__PURE__ */ jsxRuntimeExports.jsx("img", {
                    src: row == null ? void 0 : row.picture,
                    alt: row == null ? void 0 : row.name,
                    className: "h-12 w-12 rounded-full object-cover flex-shrink-0 border border-gray-200"
                  }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                    className: "flex flex-col items-start text-left",
                    children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("span", {
                      className: "text-sm font-medium text-blue-600 hover:underline max-w-32 break-words",
                      children: [row == null ? void 0 : row.name, " ", row == null ? void 0 : row.unit]
                    }), (row == null ? void 0 : row.freeItem) && /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                      className: "text-xs font-semibold text-teal-500 bg-teal-100 px-2 py-1 rounded-full mt-1",
                      children: "Free"
                    })]
                  })]
                }), row.description && /* @__PURE__ */ jsxRuntimeExports.jsx(TooltipProvider, {
                  children: /* @__PURE__ */ jsxRuntimeExports.jsxs(Tooltip, {
                    children: [/* @__PURE__ */ jsxRuntimeExports.jsx(TooltipTrigger, {
                      asChild: true,
                      children: /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
                        className: "mt-2 flex justify-center",
                        children: /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                          className: "text-xs font-semibold text-orange-500 hover:text-orange-600 cursor-pointer underline",
                          children: "View Description"
                        })
                      })
                    }), /* @__PURE__ */ jsxRuntimeExports.jsx(TooltipContent, {
                      className: "bg-gray-800 text-white p-3 rounded-md max-w-xs",
                      children: /* @__PURE__ */ jsxRuntimeExports.jsxs("p", {
                        className: "text-sm",
                        children: ["Description: ", (row == null ? void 0 : row.description) || "-"]
                      })
                    })]
                  })
                })]
              }), /* @__PURE__ */ jsxRuntimeExports.jsx("td", {
                className: "py-2 px-2 text-center  whitespace-normal break-words",
                children: row == null ? void 0 : row.weightFactor
              }), /* @__PURE__ */ jsxRuntimeExports.jsx("td", {
                className: "py-2 px-2 text-center  whitespace-normal break-words",
                children: row == null ? void 0 : row.maxAvailableQty
              }), /* @__PURE__ */ jsxRuntimeExports.jsxs("td", {
                className: "py-2 px-2 text-center  whitespace-normal break-words",
                children: [row == null ? void 0 : row.minOrderQty, "-", row == null ? void 0 : row.maxOrderQty]
              }), /* @__PURE__ */ jsxRuntimeExports.jsxs("td", {
                className: "py-2 px-3 text-center  whitespace-normal break-words",
                children: [row == null ? void 0 : row.maxOrderQty, "-", row == null ? void 0 : row.incrementOrderQty]
              }), /* @__PURE__ */ jsxRuntimeExports.jsx("td", {
                className: "py-2 px-3 text-center  whitespace-normal break-words",
                children: row == null ? void 0 : row.pricePerUnit
              }), /* @__PURE__ */ jsxRuntimeExports.jsx("td", {
                className: "py-2 px-3 text-center  whitespace-normal break-words",
                children: row == null ? void 0 : row.supplierName
              }), /* @__PURE__ */ jsxRuntimeExports.jsxs("td", {
                className: "py-2 px-3 text-center  whitespace-normal break-words",
                children: [row == null ? void 0 : row.distChargePc, "-", row == null ? void 0 : row.distChargePu]
              }), /* @__PURE__ */ jsxRuntimeExports.jsxs("td", {
                className: "py-2 px-3 text-center  whitespace-normal break-words",
                children: [row == null ? void 0 : row.minAcPkg, "-", row == null ? void 0 : row.maxAcPkg]
              }), /* @__PURE__ */ jsxRuntimeExports.jsxs("td", {
                className: "py-2 px-3 text-center  whitespace-normal break-words",
                children: [row == null ? void 0 : row.minDistcPkg, "-", row == null ? void 0 : row.maxDistcPkg]
              }), /* @__PURE__ */ jsxRuntimeExports.jsx("td", {
                className: "py-2 px-3 text-center cursor-pointer",
                children: /* @__PURE__ */ jsxRuntimeExports.jsx(Pencil, {
                  onClick: () => handleEditModal(row)
                })
              })]
            }, row.Id);
          }
        }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
          className: "flex items-center justify-center space-x-2 py-4 overflow-x-auto whitespace-nowrap",
          children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("h2", {
            className: "shrink-0",
            children: ["Current Page: ", pageNum + 1]
          }), /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
            className: "overflow-x-auto",
            children: /* @__PURE__ */ jsxRuntimeExports.jsx(ResponsivePagination, {
              totalPages: Number(pageSize),
              currentPage: pageNum,
              onPageChange: (pageNum2) => handlePageChange(pageNum2.toString())
            })
          })]
        }), /* @__PURE__ */ jsxRuntimeExports.jsx(EditModal, {
          isOpen: isItemModalOpen,
          data: selectedItem || {},
          onClose: () => {
            setIsItemModalOpen(false);
            setSelectedItem({});
          },
          onSave: handleSave,
          multiple: false,
          supplierList
        })]
      }), /* @__PURE__ */ jsxRuntimeExports.jsxs(TabsContent, {
        value: "SellerUser",
        children: [/* @__PURE__ */ jsxRuntimeExports.jsx(ResponsiveTable, {
          headers: UserHeaders,
          data: sellerUser,
          renderRow: (row) => {
            var _a;
            return /* @__PURE__ */ jsxRuntimeExports.jsxs("tr", {
              className: "border-b",
              children: [/* @__PURE__ */ jsxRuntimeExports.jsx("td", {
                className: "py-2 px-3 text-center  whitespace-normal break-words",
                children: row.userId
              }), /* @__PURE__ */ jsxRuntimeExports.jsx("td", {
                className: "py-2 px-3 text-center whitespace-normal break-words",
                children: row == null ? void 0 : row.userName
              }), /* @__PURE__ */ jsxRuntimeExports.jsx("td", {
                className: "py-2 px-3 text-center  whitespace-normal break-words",
                children: row == null ? void 0 : row.businessName
              }), /* @__PURE__ */ jsxRuntimeExports.jsx("td", {
                className: "py-2 px-3 text-center whitespace-normal break-words",
                children: row == null ? void 0 : row.mobileNumber
              }), /* @__PURE__ */ jsxRuntimeExports.jsx("td", {
                className: "py-2 px-3 text-center  whitespace-normal break-words",
                children: row == null ? void 0 : row.cashWithUser
              }), /* @__PURE__ */ jsxRuntimeExports.jsx("td", {
                className: "py-2 px-3 text-center  whitespace-normal break-words",
                children: (_a = row == null ? void 0 : row.roles) == null ? void 0 : _a.map((x) => /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                  children: [" ", x === "SellerOwner" ? "Owner" : x === "DriverFull" ? "Driver" : x === "SupplierBasic" ? "SupplierBasic" : x === "AgentFull" ? "Agent" : x === "SellerManager" ? "Manager" : x === "SellerSupervisor" ? "Supervisor" : x === "PickerFull" ? " Warehouse Helper/ Picker" : x === "AdvancedBuyer" ? "Buyer" : x === "ContractPriceFull" ? "ContractPrice" : x === "NetworkManager" ? "NetworkManager(mNET)" : x === "MnetManager" ? "MnetManager(mNET)" : x === "MnetAdmin" ? "MnetAdmin(mNET)" : x === "SalesManager" ? "SalesManager(mNET)" : x === "WhatsappFull" ? "WhatsappFull(mNET)" : x === "MnetAgent" ? "Agent(mNET)" : x === "SC_Basic" ? "SellerBasic" : x === "OC_Manager" ? "OperationManager" : x === "AC_Basic" ? "AccountManager" : x === "FmSalesManager" ? "FmSalesManager" : ""]
                }))
              }), /* @__PURE__ */ jsxRuntimeExports.jsx("td", {
                className: "py-2 px-3 text-center  whitespace-normal break-words",
                children: /* @__PURE__ */ jsxRuntimeExports.jsx(Switch, {
                  checked: !toggleUserStatus[row.userId],
                  onClick: () => handleSwitchUser(row.userId)
                })
              }), /* @__PURE__ */ jsxRuntimeExports.jsx("td", {
                className: "py-2 px-3 text-center",
                children: /* @__PURE__ */ jsxRuntimeExports.jsx(Pencil, {
                  onClick: () => handleEdit(row)
                })
              })]
            }, row.userId);
          }
        }), /* @__PURE__ */ jsxRuntimeExports.jsx(CreateUser, {
          isOpen: isModalOpenEdit,
          onClose: () => setIsModalOpenEdit(false),
          sellerId,
          roles: roles ? roles : [],
          sellerBId,
          user
        }), /* @__PURE__ */ jsxRuntimeExports.jsx(CreateUser, {
          isOpen: isModalOpen,
          onClose: () => setIsModalOpen(false),
          sellerId,
          roles: roles ? roles : [],
          sellerBId
        }), /* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
          className: "fixed bottom-5 right-5 rounded-full cursor-pointer",
          onClick: () => setIsModalOpen(true),
          children: "+ Add User"
        })]
      }), /* @__PURE__ */ jsxRuntimeExports.jsx(TabsContent, {
        value: "BillingConfig",
        children: /* @__PURE__ */ jsxRuntimeExports.jsx(BillingConfig, {
          billingConfig: businessConfig,
          onSellerAttributeUpdate
        })
      })]
    }), activeTab === "SellerConfig" && (sellerConfig ? /* @__PURE__ */ jsxRuntimeExports.jsx(SellerConfigDetails, {
      sellerConfig,
      onAttributeUpdate: handleUpdate
    }) : /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
      className: "flex items-center justify-center h-full",
      children: /* @__PURE__ */ jsxRuntimeExports.jsx("p", {
        className: "text-red-500",
        children: "Unable to find network configurations."
      })
    })), activeTab === "SellerAreas" && (sellerAreas ? /* @__PURE__ */ jsxRuntimeExports.jsx(MapComponent, {
      googleMapsApiKey,
      sellerAreas,
      visibleAreas,
      decodePolygon,
      getPolygonCenter,
      getPolygonColor,
      handleLocateShopClicked,
      updateToggle,
      onLoad,
      onUnmount,
      isLocateShopClicked,
      statesAndDistricts,
      userId,
      sellerId,
      handleSubmit: (areaIds) => handleCreateAreas(areaIds)
    }) : /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
      className: "flex items-center justify-center h-full",
      children: /* @__PURE__ */ jsxRuntimeExports.jsx("p", {
        className: "text-red-500",
        children: "Unable to find sellerAreas ."
      })
    }))]
  });
}
export {
  SellerDetailsPage as default
};
//# sourceMappingURL=home.sellerDetails-CnMRRK_f.js.map
