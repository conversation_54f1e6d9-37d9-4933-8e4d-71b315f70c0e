{"version": 3, "file": "bar-dashboard-TOc0xTaf.js", "sources": ["../../../app/types/home/<USER>", "../../../app/components/ui/bar-dashboard.tsx"], "sourcesContent": ["export enum DashboardGroupBy {\r\n    Daily = \"Daily\",\r\n    Weekly = \"Weekly\",\r\n    Monthly = \"Monthly\",\r\n}\r\n", "import { DashboardGroupBy } from \"~/types/home\";\r\nimport { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from \"./tabs\";\r\nimport { useEffect, useState } from \"react\";\r\nimport { SellerConsoleDataResponse } from \"~/types/api/businessConsoleService/SellerConsoleDataResponse\";\r\n\r\nimport { addDays, addMonths, format, getWeekOfMonth, startOfWeek } from \"date-fns\";\r\nimport { Card, CardContent, CardHeader, CardTitle } from \"./card\";\r\nimport { ChevronLeft, ChevronRight } from \"lucide-react\";\r\nimport { Button } from \"./button\";\r\nimport { Bar, BarChart, CartesianGrid, Label, ResponsiveContainer, Tooltip, XAxis, YAxis } from \"recharts\";\r\nimport { formatCurrency } from \"~/utils/format\";\r\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from \"./select\";\r\n\r\n\r\nconst COLORS = ['#afded7', '#f8c8cd', '#cfc2ed', '#AFEFED', '#90c8e7', '#FDDFB4', '#E789B7', '#FFD384', '#00a390', '#7551ce']\r\n\r\nexport interface DataPoint {\r\n    periodLabel: string;\r\n    startDate: Date;\r\n    Revenue: number;\r\n    returnAmount: number;\r\n    returnweight: number;\r\n    Quantity: number;\r\n    Orders: number;\r\n    AovValue: number;\r\n    NewCustomers: number;\r\n\r\n\r\n\r\n}\r\n\r\nexport function calculateTargetDate(today: Date, groupBy: DashboardGroupBy, periodIndex: number): Date {\r\n    // console.log('period...Index....Calculate...Tg', periodIndex);\r\n    switch (groupBy) {\r\n        case DashboardGroupBy.Daily:\r\n            // return addDays(today, -7 * periodIndex);\r\n            // Calculate last day of the week (Saturday)\r\n            if (periodIndex > 0) {\r\n                const dayOfWeek = today.getDay(); // Sunday = 0, Monday = 1, ..., Saturday = 6\r\n                const daysUntilSaturday = 6 - dayOfWeek;\r\n                return addDays(addDays(today, -7 * periodIndex), daysUntilSaturday);\r\n            } else { return addDays(today, 0); }\r\n\r\n        case DashboardGroupBy.Weekly:\r\n            //return addDays(today, -28 * periodIndex);\r\n            const lastDayOfMonth = new Date(today.getFullYear(), today.getMonth() + 1 - periodIndex, 0); // Day 0 gives the last day of the previous month\r\n            return lastDayOfMonth;\r\n        case DashboardGroupBy.Monthly:\r\n            return new Date(today.getFullYear() - periodIndex, 11, 31);\r\n        default:\r\n            return today;\r\n    }\r\n}\r\n\r\nexport const calculatePeriodDates = (dataPoint: DataPoint, activeTab: DashboardGroupBy): { startDate: Date, endDate: Date } | null => {\r\n    try {\r\n        let startDate = dataPoint.startDate;\r\n        let endDate = activeTab === DashboardGroupBy.Daily ? dataPoint.startDate :\r\n            activeTab === DashboardGroupBy.Weekly ? addDays(startDate, 6) : addDays(addMonths(startDate, 1), -1);\r\n        return { startDate: startDate, endDate: endDate };\r\n    } catch (error) {\r\n        console.error(\"Error calculating date range:\", error);\r\n        return null;\r\n    }\r\n};\r\n\r\nconst CustomerDashboard = ({\r\n    data, handleGraph,\r\n    dashboardGroupBy,\r\n    handleBarSelected,\r\n\r\n}: { data: SellerConsoleDataResponse[], handleGraph: (value: string, summaryDate: Date) => void, dashboardGroupBy: DashboardGroupBy, handleBarSelected?: (startDate: Date, endDate: Date, tab: string, summaryDate: Date, selectedSellerData: DataPoint) => void }) => {\r\n\r\n\r\n    const [chartData, setChartData] = useState<DataPoint[]>([]);\r\n\r\n    const getDefaultSelectedData = (): DataPoint | null => {\r\n        if (chartData === null || chartData.length === 0)\r\n            return null;\r\n        return chartData[chartData.length - 1];\r\n    }\r\n\r\n    const [activeTab, setActiveTab] = useState<DashboardGroupBy>(dashboardGroupBy);\r\n    //const [selectedPeriod, setSelectedPeriod] = useState<string | null>(getDefaultSelectedPeriod(dashboardGroupBy));\r\n    const [selectedData, setSelectedData] = useState<DataPoint | null>(getDefaultSelectedData());\r\n\r\n    useEffect(() => {\r\n        if (data) {\r\n            const preparedData: DataPoint[] = [];\r\n\r\n            if (Array.isArray(data)) {\r\n\r\n                console.log(data, \"&&&&&&&&&&&&&&&7\")\r\n                data.forEach((item) => {\r\n                    var label = dashboardGroupBy === DashboardGroupBy.Daily ? format(item.startDate, \"dd/MM\") :\r\n                        (dashboardGroupBy === DashboardGroupBy.Weekly ? format(item.startDate, \"dd/MM\") : format(item.startDate, \"MMMM\"))\r\n\r\n                    //                     returnAmount:number;\r\n                    // returnweight:number;\r\n                    // totalweight:number;\r\n                    // totalAmount:number;\r\n                    preparedData.push({\r\n                        periodLabel: label, startDate: new Date(item.startDate),\r\n                        Revenue: Math.round(item.totalAmount), returnAmount: item.returnAmount, returnweight: item.returnWeight, Quantity: Math.round(item.totalWeight), Orders: item.totalOrders, AovValue: Math.round(item.avgOrderValue), NewCustomers: item.newCustomerCount\r\n                    })\r\n                    // console.log(item)\r\n                });\r\n            }\r\n            setChartData(preparedData);\r\n            // console.log(chartData, \"chartData\")\r\n\r\n        }\r\n    }, [data]);\r\n\r\n\r\n    useEffect(() => {\r\n        if (data) {\r\n            const preparedData: DataPoint[] = [];\r\n\r\n            if (Array.isArray(data)) {\r\n                data.forEach((item) => {\r\n                    var label = dashboardGroupBy === DashboardGroupBy.Daily ? format(item.startDate, \"dd/MM\") :\r\n                        (dashboardGroupBy === DashboardGroupBy.Weekly ? format(item.startDate, \"dd/MM\") : format(item.startDate, \"MMMM\"))\r\n                    preparedData.push({ periodLabel: label, startDate: new Date(item.startDate), Revenue: Math.round(item.totalAmount), returnAmount: item.returnAmount, returnweight: item.returnWeight, Quantity: Math.round(item.totalWeight), Orders: item.totalOrders, AovValue: Math.round(item.avgOrderValue), NewCustomers: item.newCustomerCount })\r\n                    // console.log(item)\r\n                });\r\n            }\r\n\r\n            setChartData(preparedData);\r\n            setActiveTab(dashboardGroupBy);\r\n        }\r\n\r\n    }, [data, selectedData]);\r\n\r\n    const handleBarClick = (dp: DataPoint) => {\r\n        setSelectedData(dp);\r\n\r\n        if (handleBarSelected) {\r\n            const periodDates = calculatePeriodDates(dp, activeTab);\r\n            if (periodDates) {\r\n                handleBarSelected(periodDates.startDate, periodDates.endDate, activeTab, chartData[chartData.length - 1].startDate, dp);\r\n            }\r\n        }\r\n    };\r\n    const isBarSelected = (periodLabel: string) => {\r\n        return periodLabel === selectedData?.periodLabel\r\n    }\r\n\r\n    const getTimeRangeTitle = () => {\r\n        if (chartData === null || chartData.length === 0)\r\n            return '';\r\n        try {\r\n            const startDate = chartData[0].startDate;\r\n            const endDate = chartData[chartData.length - 1].startDate;\r\n\r\n            if (activeTab === DashboardGroupBy.Daily) {\r\n                return `${format(startDate, 'd MMM')} - ${format(endDate, 'd MMM, yyyy')}`;\r\n            }\r\n            if (activeTab === DashboardGroupBy.Weekly) {\r\n                const lastDay = addDays(endDate, 6);\r\n                return `${format(startDate, 'd MMM')} - ${format(lastDay, 'd MMM, yyyy')}`;\r\n            }\r\n            if (activeTab === DashboardGroupBy.Monthly) {\r\n                return `${format(startDate, (new Date(startDate)).getFullYear() === (new Date(endDate)).getFullYear() ? 'MMM' : 'MMM yyyy')} - ${format(endDate, 'MMM yyyy')}`;\r\n            }\r\n        } catch (error) {\r\n            console.error('Date calculation error:', error);\r\n            return 'Date range unavailable';\r\n        }\r\n        return '';\r\n    };\r\n\r\n\r\n    const getSelectedPeriodTitle = () => {\r\n        if (!selectedData) return '';\r\n\r\n        try {\r\n            const startDate = selectedData.startDate;\r\n\r\n            if (activeTab === DashboardGroupBy.Daily) {\r\n                return `${format(startDate, 'EEEE, dd MMM')}`;\r\n            }\r\n            if (activeTab === DashboardGroupBy.Weekly) {\r\n                const lastDay = addDays(selectedData.startDate, 6);\r\n                return `${format(startDate, 'd MMM')} - ${format(lastDay, 'd MMM, yyyy')}`;\r\n            }\r\n            if (activeTab === DashboardGroupBy.Monthly) {\r\n                return `${format(startDate, 'MMMM yyyy')}`;\r\n            }\r\n        } catch (error) {\r\n            console.error('Date calculation error:', error);\r\n            return 'Date range unavailable';\r\n        }\r\n        return '';\r\n    };\r\n\r\n    const handlePreviousPeriod = () => {\r\n        // const newIndex = currentPeriodIndex + 1;\r\n        // setCurrentPeriodIndex(newIndex);\r\n\r\n        var currentSummaryDate = chartData[chartData.length - 1].startDate;\r\n        var prevSD = activeTab === DashboardGroupBy.Daily ? addDays(currentSummaryDate, -15)\r\n            : activeTab === DashboardGroupBy.Weekly ? addDays(currentSummaryDate, -4 * 7)\r\n                : addMonths(currentSummaryDate, -3);\r\n\r\n        handleGraph(activeTab, prevSD);\r\n\r\n    };\r\n\r\n    const handleNextPeriod = () => {\r\n        var currentSummaryDate = chartData[chartData.length - 1].startDate;\r\n        var nextSD = activeTab === DashboardGroupBy.Daily ? addDays(currentSummaryDate, 15)\r\n            : activeTab === DashboardGroupBy.Weekly ? addDays(currentSummaryDate, 4 * 7)\r\n                : addMonths(currentSummaryDate, 3);\r\n\r\n        if (addDays(nextSD, -1) > new Date())\r\n            nextSD = new Date();\r\n\r\n        handleGraph(activeTab, nextSD);\r\n    };\r\n\r\n    const getPrevDisabled = () => {\r\n        if (activeTab === DashboardGroupBy.Daily && (chartData === null || chartData.length < 30)) {\r\n            return true\r\n        }\r\n        if (activeTab === DashboardGroupBy.Weekly && (chartData === null || chartData.length < 12)) {\r\n            return true\r\n        }\r\n        if (activeTab === DashboardGroupBy.Monthly && (chartData === null || chartData.length < 6)) {\r\n            return true\r\n        }\r\n        return false\r\n    }\r\n    const getNextDisabled = () => {\r\n        if (activeTab === DashboardGroupBy.Daily && (chartData === null || chartData.length === 0\r\n            || addDays(chartData[chartData.length - 1].startDate, 1) >= new Date())) {\r\n            return true\r\n        }\r\n        if (activeTab === DashboardGroupBy.Weekly && (chartData === null || chartData.length === 0\r\n            || addDays(chartData[chartData.length - 1].startDate, 7) >= new Date())) {\r\n            return true\r\n        }\r\n        if (activeTab === DashboardGroupBy.Monthly && (chartData === null || chartData.length === 0\r\n            || addMonths(chartData[chartData.length - 1].startDate, 1) >= new Date())) {\r\n            return true\r\n        }\r\n        return false\r\n    }\r\n\r\n\r\n    const handleTabChange = (value: DashboardGroupBy) => {\r\n        // console.log(value,)\r\n        setActiveTab(value);\r\n\r\n        handleGraph(value, new Date())\r\n\r\n\r\n    };\r\n    const [selectedMetric, setSelectedMetric] = useState(\"Revenue\")\r\n    const updateMetrics = (val: string) => [\r\n        setSelectedMetric(val)\r\n    ]\r\n    return (\r\n        <div>\r\n            <div className=\"flex justify-between items-center p-4 \">\r\n                <h1 className=\"text-2xl font-bold text-primary\">Dashboard</h1>\r\n                <div className=\" flex flex-row gap-2\">\r\n                    <Label>Metrics</Label>\r\n                    <Select\r\n                        onValueChange={(val) => updateMetrics(val)}\r\n                        value={selectedMetric}\r\n                    >\r\n                        <SelectTrigger>\r\n                            <SelectValue placeholder=\"Select Metrics\" />\r\n                        </SelectTrigger>\r\n                        <SelectContent>\r\n                            <SelectItem value=\"Revenue\">Revenue ( ₹ )</SelectItem>\r\n                            <SelectItem value=\"Quantity\">Quantity ( kg )</SelectItem>\r\n                            <SelectItem value=\"AovValue\">Avg Order Value ( ₹ )</SelectItem>\r\n                            <SelectItem value=\"Orders\">Orders</SelectItem>\r\n                            <SelectItem value=\"NewCustomers\">New Customers</SelectItem>\r\n                        </SelectContent>\r\n                    </Select>\r\n\r\n\r\n                    <Tabs\r\n                        value={activeTab}\r\n                        onValueChange={(value) => handleTabChange(value as DashboardGroupBy)}\r\n                    >\r\n                        <TabsList>\r\n                            <TabsTrigger value={DashboardGroupBy.Daily}>Daily</TabsTrigger>\r\n                            <TabsTrigger value={DashboardGroupBy.Weekly}>Weekly</TabsTrigger>\r\n                            <TabsTrigger value={DashboardGroupBy.Monthly}>Monthly</TabsTrigger>\r\n                        </TabsList>\r\n                    </Tabs>\r\n                </div>\r\n            </div>\r\n            <Card>\r\n                <CardHeader className=\"flex flex-row items-center justify-between\">\r\n                    <div className=\"space-y-1\">\r\n                        <CardTitle>Revenue Overview</CardTitle>\r\n                        <p className=\"text-sm text-muted-foreground\">{getTimeRangeTitle()}</p>\r\n                    </div>\r\n                    <div className=\"flex items-center space-x-2\">\r\n                        <Button variant=\"outline\" size=\"icon\" onClick={() => { handlePreviousPeriod() }}\r\n                            disabled={getPrevDisabled()}\r\n                        >\r\n                            <ChevronLeft className=\"h-4 w-4\" />\r\n                        </Button>\r\n                        <Button variant=\"outline\" size=\"icon\" onClick={() => { handleNextPeriod() }}\r\n                            disabled={getNextDisabled()}\r\n                        >\r\n                            <ChevronRight className=\"h-4 w-4\" />\r\n                        </Button>\r\n                    </div>\r\n                </CardHeader>\r\n                <CardContent>\r\n                    <ResponsiveContainer width=\"100%\" height={250} >\r\n                        <BarChart data={chartData}\r\n                            margin={{ top: 20, right: 20, left: 20, bottom: 20 }}\r\n                            onClick={(data) => {\r\n                                if (data && data.activePayload && data.activePayload.length > 0) {\r\n                                    handleBarClick(data.activePayload[0].payload)\r\n                                }\r\n                            }}>\r\n                            <CartesianGrid strokeDasharray=\"8 8\" vertical={false} />\r\n                            <XAxis dataKey=\"periodLabel\" />\r\n                            {/* <YAxis tickFormatter={(value: number) => `₹${value}`}/> */}\r\n                            <YAxis\r\n                                width={80}\r\n                                orientation=\"right\"\r\n                                axisLine={false}\r\n                                tickLine={false}\r\n                            />\r\n                            <Tooltip formatter={(value: number) => (value)} />\r\n                            <Bar\r\n                                dataKey={selectedMetric}\r\n                                fill=\"hsl(var(--primary))\"\r\n                                // label={{ position: 'outside', fill: '#000' }}\r\n                                shape={(props: any) => {\r\n                                    const { x, y, width, height, periodLabel } = props\r\n                                    const fill = isBarSelected(periodLabel)\r\n                                        ? \"hsl(var(--primary))\"\r\n                                        : \"hsl(var(--primary) / 0.7)\"\r\n                                    return (\r\n                                        <rect\r\n                                            x={x}\r\n                                            y={y}\r\n                                            width={width}\r\n                                            height={height}\r\n                                            fill={fill}\r\n                                            rx={4}\r\n                                            ry={4}\r\n                                        />\r\n                                    )\r\n                                }}\r\n                            />\r\n                        </BarChart>\r\n                    </ResponsiveContainer>\r\n                </CardContent>\r\n            </Card>\r\n            {selectedData && (\r\n                <h2 className=\"text-2xl font-semibold mt-2 text-center mb-0 pb-0\">\r\n                    {getSelectedPeriodTitle()}\r\n                </h2>\r\n            )}\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default CustomerDashboard;"], "names": ["DashboardGroupBy", "useState", "useEffect", "jsxs", "jsx", "data"], "mappings": ";;;;;;;;;;;AAAY,IAAA,qCAAAA,sBAAL;AACHA,oBAAA,OAAQ,IAAA;AACRA,oBAAA,QAAS,IAAA;AACTA,oBAAA,SAAU,IAAA;AAHFA,SAAAA;AAAA,GAAA,oBAAA,CAAA,CAAA;ACsDC,MAAA,uBAAuB,CAAC,WAAsB,cAA2E;AAC9H,MAAA;AACA,QAAI,YAAY,UAAU;AAC1B,QAAI,UAAU,cAAc,iBAAiB,QAAQ,UAAU,YAC3D,cAAc,iBAAiB,SAAS,QAAQ,WAAW,CAAC,IAAI,QAAQ,UAAU,WAAW,CAAC,GAAG,EAAE;AAChG,WAAA,EAAE,WAAsB,QAAiB;AAAA,WAC3C,OAAO;AACJ,YAAA,MAAM,iCAAiC,KAAK;AAC7C,WAAA;AAAA,EAAA;AAEf;AAEA,MAAM,oBAAoB,CAAC;AAAA,EACvB;AAAA,EAAM;AAAA,EACN;AAAA,EACA;AAEJ,MAAuQ;AAGnQ,QAAM,CAAC,WAAW,YAAY,IAAIC,aAAAA,SAAsB,CAAA,CAAE;AAE1D,QAAM,yBAAyB,MAAwB;AAC/C,QAAA,cAAc,QAAQ,UAAU,WAAW;AACpC,aAAA;AACJ,WAAA,UAAU,UAAU,SAAS,CAAC;AAAA,EACzC;AAEA,QAAM,CAAC,WAAW,YAAY,IAAIA,aAAAA,SAA2B,gBAAgB;AAE7E,QAAM,CAAC,cAAc,eAAe,IAAIA,aAAA,SAA2B,wBAAwB;AAE3FC,eAAAA,UAAU,MAAM;AACZ,QAAI,MAAM;AACN,YAAM,eAA4B,CAAC;AAE/B,UAAA,MAAM,QAAQ,IAAI,GAAG;AAEb,gBAAA,IAAI,MAAM,kBAAkB;AAC/B,aAAA,QAAQ,CAAC,SAAS;AACf,cAAA,QAAQ,qBAAqB,iBAAiB,QAAQ,OAAO,KAAK,WAAW,OAAO,IACnF,qBAAqB,iBAAiB,SAAS,OAAO,KAAK,WAAW,OAAO,IAAI,OAAO,KAAK,WAAW,MAAM;AAMnH,uBAAa,KAAK;AAAA,YACd,aAAa;AAAA,YAAO,WAAW,IAAI,KAAK,KAAK,SAAS;AAAA,YACtD,SAAS,KAAK,MAAM,KAAK,WAAW;AAAA,YAAG,cAAc,KAAK;AAAA,YAAc,cAAc,KAAK;AAAA,YAAc,UAAU,KAAK,MAAM,KAAK,WAAW;AAAA,YAAG,QAAQ,KAAK;AAAA,YAAa,UAAU,KAAK,MAAM,KAAK,aAAa;AAAA,YAAG,cAAc,KAAK;AAAA,UAAA,CAC3O;AAAA,QAAA,CAEJ;AAAA,MAAA;AAEL,mBAAa,YAAY;AAAA,IAAA;AAAA,EAG7B,GACD,CAAC,IAAI,CAAC;AAGTA,eAAAA,UAAU,MAAM;AACZ,QAAI,MAAM;AACN,YAAM,eAA4B,CAAC;AAE/B,UAAA,MAAM,QAAQ,IAAI,GAAG;AAChB,aAAA,QAAQ,CAAC,SAAS;AACf,cAAA,QAAQ,qBAAqB,iBAAiB,QAAQ,OAAO,KAAK,WAAW,OAAO,IACnF,qBAAqB,iBAAiB,SAAS,OAAO,KAAK,WAAW,OAAO,IAAI,OAAO,KAAK,WAAW,MAAM;AACnH,uBAAa,KAAK,EAAE,aAAa,OAAO,WAAW,IAAI,KAAK,KAAK,SAAS,GAAG,SAAS,KAAK,MAAM,KAAK,WAAW,GAAG,cAAc,KAAK,cAAc,cAAc,KAAK,cAAc,UAAU,KAAK,MAAM,KAAK,WAAW,GAAG,QAAQ,KAAK,aAAa,UAAU,KAAK,MAAM,KAAK,aAAa,GAAG,cAAc,KAAK,kBAAkB;AAAA,QAAA,CAE1U;AAAA,MAAA;AAGL,mBAAa,YAAY;AACzB,mBAAa,gBAAgB;AAAA,IAAA;AAAA,EACjC,GAED,CAAC,MAAM,YAAY,CAAC;AAEjB,QAAA,iBAAiB,CAAC,OAAkB;AACtC,oBAAgB,EAAE;AAElB,QAAI,mBAAmB;AACb,YAAA,cAAc,qBAAqB,IAAI,SAAS;AACtD,UAAI,aAAa;AACK,0BAAA,YAAY,WAAW,YAAY,SAAS,WAAW,UAAU,UAAU,SAAS,CAAC,EAAE,WAAW,EAAE;AAAA,MAAA;AAAA,IAC1H;AAAA,EAER;AACM,QAAA,gBAAgB,CAAC,gBAAwB;AAC3C,WAAO,iBAAgB,6CAAc;AAAA,EACzC;AAEA,QAAM,oBAAoB,MAAM;AACxB,QAAA,cAAc,QAAQ,UAAU,WAAW;AACpC,aAAA;AACP,QAAA;AACM,YAAA,YAAY,UAAU,CAAC,EAAE;AAC/B,YAAM,UAAU,UAAU,UAAU,SAAS,CAAC,EAAE;AAE5C,UAAA,cAAc,iBAAiB,OAAO;AAC/B,eAAA,GAAG,OAAO,WAAW,OAAO,CAAC,MAAM,OAAO,SAAS,aAAa,CAAC;AAAA,MAAA;AAExE,UAAA,cAAc,iBAAiB,QAAQ;AACjC,cAAA,UAAU,QAAQ,SAAS,CAAC;AAC3B,eAAA,GAAG,OAAO,WAAW,OAAO,CAAC,MAAM,OAAO,SAAS,aAAa,CAAC;AAAA,MAAA;AAExE,UAAA,cAAc,iBAAiB,SAAS;AACjC,eAAA,GAAG,OAAO,WAAY,IAAI,KAAK,SAAS,EAAG,YAAY,MAAO,IAAI,KAAK,OAAO,EAAG,YAAA,IAAgB,QAAQ,UAAU,CAAC,MAAM,OAAO,SAAS,UAAU,CAAC;AAAA,MAAA;AAAA,aAE3J,OAAO;AACJ,cAAA,MAAM,2BAA2B,KAAK;AACvC,aAAA;AAAA,IAAA;AAEJ,WAAA;AAAA,EACX;AAGA,QAAM,yBAAyB,MAAM;AAC7B,QAAA,CAAC,aAAqB,QAAA;AAEtB,QAAA;AACA,YAAM,YAAY,aAAa;AAE3B,UAAA,cAAc,iBAAiB,OAAO;AACtC,eAAO,GAAG,OAAO,WAAW,cAAc,CAAC;AAAA,MAAA;AAE3C,UAAA,cAAc,iBAAiB,QAAQ;AACvC,cAAM,UAAU,QAAQ,aAAa,WAAW,CAAC;AAC1C,eAAA,GAAG,OAAO,WAAW,OAAO,CAAC,MAAM,OAAO,SAAS,aAAa,CAAC;AAAA,MAAA;AAExE,UAAA,cAAc,iBAAiB,SAAS;AACxC,eAAO,GAAG,OAAO,WAAW,WAAW,CAAC;AAAA,MAAA;AAAA,aAEvC,OAAO;AACJ,cAAA,MAAM,2BAA2B,KAAK;AACvC,aAAA;AAAA,IAAA;AAEJ,WAAA;AAAA,EACX;AAEA,QAAM,uBAAuB,MAAM;AAI/B,QAAI,qBAAqB,UAAU,UAAU,SAAS,CAAC,EAAE;AACzD,QAAI,SAAS,cAAc,iBAAiB,QAAQ,QAAQ,oBAAoB,GAAG,IAC7E,cAAc,iBAAiB,SAAS,QAAQ,oBAAoB,KAAK,CAAC,IACtE,UAAU,oBAAoB,EAAE;AAE1C,gBAAY,WAAW,MAAM;AAAA,EAEjC;AAEA,QAAM,mBAAmB,MAAM;AAC3B,QAAI,qBAAqB,UAAU,UAAU,SAAS,CAAC,EAAE;AACzD,QAAI,SAAS,cAAc,iBAAiB,QAAQ,QAAQ,oBAAoB,EAAE,IAC5E,cAAc,iBAAiB,SAAS,QAAQ,oBAAoB,IAAI,CAAC,IACrE,UAAU,oBAAoB,CAAC;AAEzC,QAAI,QAAQ,QAAQ,EAAE,wBAAQ,KAAK;AAC/B,mCAAa,KAAK;AAEtB,gBAAY,WAAW,MAAM;AAAA,EACjC;AAEA,QAAM,kBAAkB,MAAM;AAC1B,QAAI,cAAc,iBAAiB,UAAU,cAAc,QAAQ,UAAU,SAAS,KAAK;AAChF,aAAA;AAAA,IAAA;AAEX,QAAI,cAAc,iBAAiB,WAAW,cAAc,QAAQ,UAAU,SAAS,KAAK;AACjF,aAAA;AAAA,IAAA;AAEX,QAAI,cAAc,iBAAiB,YAAY,cAAc,QAAQ,UAAU,SAAS,IAAI;AACjF,aAAA;AAAA,IAAA;AAEJ,WAAA;AAAA,EACX;AACA,QAAM,kBAAkB,MAAM;AAC1B,QAAI,cAAc,iBAAiB,UAAU,cAAc,QAAQ,UAAU,WAAW,KACjF,QAAQ,UAAU,UAAU,SAAS,CAAC,EAAE,WAAW,CAAC,KAAK,oBAAI,SAAS;AAClE,aAAA;AAAA,IAAA;AAEX,QAAI,cAAc,iBAAiB,WAAW,cAAc,QAAQ,UAAU,WAAW,KAClF,QAAQ,UAAU,UAAU,SAAS,CAAC,EAAE,WAAW,CAAC,KAAK,oBAAI,SAAS;AAClE,aAAA;AAAA,IAAA;AAEX,QAAI,cAAc,iBAAiB,YAAY,cAAc,QAAQ,UAAU,WAAW,KACnF,UAAU,UAAU,UAAU,SAAS,CAAC,EAAE,WAAW,CAAC,KAAK,oBAAI,SAAS;AACpE,aAAA;AAAA,IAAA;AAEJ,WAAA;AAAA,EACX;AAGM,QAAA,kBAAkB,CAAC,UAA4B;AAEjD,iBAAa,KAAK;AAEN,gBAAA,OAAW,oBAAA,MAAM;AAAA,EAGjC;AACA,QAAM,CAAC,gBAAgB,iBAAiB,IAAID,aAAAA,SAAS,SAAS;AACxD,QAAA,gBAAgB,CAAC,QAAgB;AAAA,IACnC,kBAAkB,GAAG;AAAA,EACzB;AACA,gDACK,OACG,EAAA,UAAA;AAAA,IAACE,kCAAAA,KAAA,OAAA,EAAI,WAAU,0CACX,UAAA;AAAA,MAACC,kCAAA,IAAA,MAAA,EAAG,WAAU,mCAAkC,UAAS,aAAA;AAAA,MACzDD,kCAAAA,KAAC,OAAI,EAAA,WAAU,wBACX,UAAA;AAAA,QAAAC,kCAAAA,IAAC,SAAM,UAAO,UAAA,CAAA;AAAA,QACdD,kCAAA;AAAA,UAAC;AAAA,UAAA;AAAA,YACG,eAAe,CAAC,QAAQ,cAAc,GAAG;AAAA,YACzC,OAAO;AAAA,YAEP,UAAA;AAAA,cAAAC,sCAAC,eACG,EAAA,UAAAA,kCAAA,IAAC,aAAY,EAAA,aAAY,iBAAiB,CAAA,GAC9C;AAAA,qDACC,eACG,EAAA,UAAA;AAAA,gBAACA,kCAAA,IAAA,YAAA,EAAW,OAAM,WAAU,UAAa,iBAAA;AAAA,gBACxCA,kCAAA,IAAA,YAAA,EAAW,OAAM,YAAW,UAAe,mBAAA;AAAA,gBAC3CA,kCAAA,IAAA,YAAA,EAAW,OAAM,YAAW,UAAqB,yBAAA;AAAA,gBACjDA,kCAAA,IAAA,YAAA,EAAW,OAAM,UAAS,UAAM,UAAA;AAAA,gBAChCA,kCAAA,IAAA,YAAA,EAAW,OAAM,gBAAe,UAAa,gBAAA,CAAA;AAAA,cAAA,EAClD,CAAA;AAAA,YAAA;AAAA,UAAA;AAAA,QACJ;AAAA,QAGAA,kCAAA;AAAA,UAAC;AAAA,UAAA;AAAA,YACG,OAAO;AAAA,YACP,eAAe,CAAC,UAAU,gBAAgB,KAAyB;AAAA,YAEnE,iDAAC,UACG,EAAA,UAAA;AAAA,cAAAA,kCAAA,IAAC,aAAY,EAAA,OAAO,iBAAiB,OAAO,UAAK,SAAA;AAAA,cAChDA,kCAAA,IAAA,aAAA,EAAY,OAAO,iBAAiB,QAAQ,UAAM,UAAA;AAAA,cAClDA,kCAAA,IAAA,aAAA,EAAY,OAAO,iBAAiB,SAAS,UAAO,UAAA,CAAA;AAAA,YAAA,EACzD,CAAA;AAAA,UAAA;AAAA,QAAA;AAAA,MACJ,EACJ,CAAA;AAAA,IAAA,GACJ;AAAA,2CACC,MACG,EAAA,UAAA;AAAA,MAACD,kCAAAA,KAAA,YAAA,EAAW,WAAU,8CAClB,UAAA;AAAA,QAACA,kCAAAA,KAAA,OAAA,EAAI,WAAU,aACX,UAAA;AAAA,UAAAC,kCAAAA,IAAC,aAAU,UAAgB,mBAAA,CAAA;AAAA,UAC1BA,kCAAA,IAAA,KAAA,EAAE,WAAU,iCAAiC,8BAAoB,CAAA;AAAA,QAAA,GACtE;AAAA,QACAD,kCAAAA,KAAC,OAAI,EAAA,WAAU,+BACX,UAAA;AAAA,UAAAC,kCAAA;AAAA,YAAC;AAAA,YAAA;AAAA,cAAO,SAAQ;AAAA,cAAU,MAAK;AAAA,cAAO,SAAS,MAAM;AAAuB,qCAAA;AAAA,cAAE;AAAA,cAC1E,UAAU,gBAAgB;AAAA,cAE1B,UAAAA,kCAAAA,IAAC,aAAY,EAAA,WAAU,UAAU,CAAA;AAAA,YAAA;AAAA,UACrC;AAAA,UACAA,kCAAA;AAAA,YAAC;AAAA,YAAA;AAAA,cAAO,SAAQ;AAAA,cAAU,MAAK;AAAA,cAAO,SAAS,MAAM;AAAmB,iCAAA;AAAA,cAAE;AAAA,cACtE,UAAU,gBAAgB;AAAA,cAE1B,UAAAA,kCAAAA,IAAC,cAAa,EAAA,WAAU,UAAU,CAAA;AAAA,YAAA;AAAA,UAAA;AAAA,QACtC,EACJ,CAAA;AAAA,MAAA,GACJ;AAAA,4CACC,aACG,EAAA,UAAAA,kCAAA,IAAC,uBAAoB,OAAM,QAAO,QAAQ,KACtC,UAAAD,kCAAA;AAAA,QAAC;AAAA,QAAA;AAAA,UAAS,MAAM;AAAA,UACZ,QAAQ,EAAE,KAAK,IAAI,OAAO,IAAI,MAAM,IAAI,QAAQ,GAAG;AAAA,UACnD,SAAS,CAACE,UAAS;AACf,gBAAIA,SAAQA,MAAK,iBAAiBA,MAAK,cAAc,SAAS,GAAG;AAC7D,6BAAeA,MAAK,cAAc,CAAC,EAAE,OAAO;AAAA,YAAA;AAAA,UAEpD;AAAA,UACA,UAAA;AAAA,YAAAD,kCAAA,IAAC,eAAc,EAAA,iBAAgB,OAAM,UAAU,OAAO;AAAA,YACtDA,kCAAAA,IAAC,OAAM,EAAA,SAAQ,cAAc,CAAA;AAAA,YAE7BA,kCAAA;AAAA,cAAC;AAAA,cAAA;AAAA,gBACG,OAAO;AAAA,gBACP,aAAY;AAAA,gBACZ,UAAU;AAAA,gBACV,UAAU;AAAA,cAAA;AAAA,YACd;AAAA,YACCA,kCAAA,IAAA,SAAA,EAAQ,WAAW,CAAC,UAAmB,MAAQ,CAAA;AAAA,YAChDA,kCAAA;AAAA,cAAC;AAAA,cAAA;AAAA,gBACG,SAAS;AAAA,gBACT,MAAK;AAAA,gBAEL,OAAO,CAAC,UAAe;AACnB,wBAAM,EAAE,GAAG,GAAG,OAAO,QAAQ,gBAAgB;AAC7C,wBAAM,OAAO,cAAc,WAAW,IAChC,wBACA;AAEF,yBAAAA,kCAAA;AAAA,oBAAC;AAAA,oBAAA;AAAA,sBACG;AAAA,sBACA;AAAA,sBACA;AAAA,sBACA;AAAA,sBACA;AAAA,sBACA,IAAI;AAAA,sBACJ,IAAI;AAAA,oBAAA;AAAA,kBACR;AAAA,gBAAA;AAAA,cAER;AAAA,YAAA;AAAA,UACJ;AAAA,QAAA;AAAA,SAER,EACJ,CAAA;AAAA,IAAA,GACJ;AAAA,IACC,gBACIA,kCAAA,IAAA,MAAA,EAAG,WAAU,qDACT,mCACL,CAAA;AAAA,EAAA,GAER;AAER;"}