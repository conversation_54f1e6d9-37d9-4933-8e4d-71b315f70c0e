{"version": 3, "file": "home.createSeller-DV-XCS_s.js", "sources": ["../../../app/routes/home.createSeller.tsx"], "sourcesContent": ["import { ActionFunction, json, redirect } from \"@remix-run/node\";\r\nimport { useActionData, Form, useNavigate } from \"@remix-run/react\";\r\nimport { ArrowLeft } from \"lucide-react\";\r\nimport { useState } from \"react\";\r\nimport { Button } from \"~/components/ui/button\";\r\nimport { Input } from \"~/components/ui/input\";\r\nimport { createSeller } from \"~/services/masterItemCategories\";\r\nimport { withResponse } from \"~/utils/auth-utils\";\r\n\r\nexport const action: ActionFunction = async ({ request }) => {\r\n      const formData = await request.formData();\r\n      const seller = {\r\n            name: formData.get(\"name\"),\r\n            address: formData.get(\"address\"),\r\n            email: formData.get(\"email\"),\r\n            customerSupportNumber: formData.get(\"customerSupportNumber\"),\r\n            owner: {\r\n                  firstName: formData.get(\"owner.firstName\"),\r\n                  lastName: formData.get(\"owner.lastName\"),\r\n                  email: formData.get(\"owner.email\"),\r\n                  mobileNumber: formData.get(\"owner.mobileNumber\"),\r\n                  address: formData.get(\"owner.address\"),\r\n                  password: formData.get(\"owner.password\"),\r\n                  businessId: Number(formData.get(\"owner.businessId\")),\r\n                  roles: [formData.get(\"owner.roles\")],\r\n            },\r\n            areaId: Number(formData.get(\"areaId\")),\r\n            latitude: formData.get(\"latitude\"),\r\n            longitude: formData.get(\"longitude\"),\r\n      };\r\n\r\n      try {\r\n            const response = await createSeller(seller, request);\r\n\r\n            if (response && response.data) {\r\n                  return redirect(request.headers.get(\"referer\") || \"/success\");\r\n            }\r\n\r\n            return withResponse({ data: response.data }, response.headers);\r\n      } catch (error) {\r\n            if (error instanceof Response && error.status === 404) {\r\n                  throw json({ error: \"create Seller page Not found\" }, { status: 404 });\r\n            }\r\n            throw new Response(\"Failed to create Seller\", { status: 500 });\r\n      }\r\n};\r\n\r\n\r\nexport default function CreateSeller() {\r\n      const actionData = useActionData();\r\n      const [formData, setFormData] = useState({\r\n            name: \"\",\r\n            address: \"\",\r\n            email: \"\",\r\n            customerSupportNumber: \"\",\r\n            owner: {\r\n                  firstName: \"\",\r\n                  lastName: \"\",\r\n                  email: \"\",\r\n                  mobileNumber: \"\",\r\n                  address: \"\",\r\n                  password: \"\",\r\n                  businessId: 0,\r\n                  roles: \"\",\r\n            },\r\n            areaId: 0,\r\n            latitude: \"\",\r\n            longitude: \"\",\r\n      });\r\n\r\n      const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n            const { name, value } = e.target;\r\n            if (name.startsWith(\"owner.\")) {\r\n                  const field = name.split(\".\")[1];\r\n                  setFormData((prev) => ({\r\n                        ...prev,\r\n                        owner: {\r\n                              ...prev.owner,\r\n                              [field]: value,\r\n                        },\r\n                  }));\r\n            } else {\r\n                  setFormData((prev) => ({ ...prev, [name]: value }));\r\n            }\r\n      };\r\n      const navigate = useNavigate()\r\n\r\n      return (\r\n            <Form method=\"post\" className=\"space-y-4 p-4 border rounded max-w-2xl bg-slate-100\">\r\n                  <div className=\"flex items-center gap-2 mb-1\">\r\n                        <Button variant=\"ghost\" size=\"sm\" onClick={() => navigate(\"/home/<USER>\")}>\r\n                              <ArrowLeft className=\"h-4 w-4 mr-2\" />\r\n                              Back to Sellers\r\n                        </Button>\r\n                  </div>\r\n\r\n                  <div >\r\n                        <h3>Seller Details</h3>\r\n                        <div className=\"space-y-3 mt-2\"> {/* Added space-y-3 here */}\r\n                              <div className=\"flex gap-3 \">\r\n                                    <Input type=\"text\" name=\"name\" placeholder=\"Name\" onChange={handleChange} required className=\"max-w-sm\" />\r\n                                    <Input type=\"text\" name=\"address\" placeholder=\"Address\" onChange={handleChange} required className=\"max-w-sm\" />\r\n                              </div>\r\n                              <div className=\"flex gap-3\">\r\n                                    <Input type=\"email\" name=\"email\" placeholder=\"Email\" onChange={handleChange} required className=\"max-w-sm\" />\r\n                                    <Input type=\"text\" name=\"customerSupportNumber\" placeholder=\"Customer Support Number\" onChange={handleChange} required className=\"max-w-sm\" />\r\n                              </div>\r\n                        </div>\r\n                  </div>\r\n                  <h3>Owner Details</h3>\r\n\r\n                  <div className=\"flex gap-3\">\r\n                        <Input type=\"text\" name=\"owner.firstName\" placeholder=\"First Name\" onChange={handleChange} required className=\"max-w-sm\" />\r\n                        <Input type=\"text\" name=\"owner.lastName\" placeholder=\"Last Name\" onChange={handleChange} required className=\"max-w-sm\" />\r\n\r\n                  </div>\r\n                  <div className=\"flex gap-3\">\r\n\r\n                        <Input type=\"email\" name=\"owner.email\" placeholder=\"Owner Email\" onChange={handleChange} required className=\"max-w-sm\" />\r\n                        <Input type=\"text\" name=\"owner.mobileNumber\" placeholder=\"Mobile Number\" onChange={handleChange} required className=\"max-w-sm\" />\r\n                  </div>\r\n                  <div className=\"flex gap-3\">\r\n                        <Input type=\"text\" name=\"owner.address\" placeholder=\"Owner Address\" onChange={handleChange} required className=\"max-w-sm\" />\r\n                        <Input type=\"password\" name=\"owner.password\" placeholder=\"Password\" onChange={handleChange} required className=\"max-w-sm\" />\r\n                  </div>\r\n                  <div className=\"flex gap-3\">\r\n                        <Input type=\"number\" name=\"owner.businessId\" placeholder=\"Business ID\" onChange={handleChange} required className=\"max-w-sm\" />\r\n                        <Input type=\"text\" name=\"owner.roles\" placeholder=\"Roles (comma separated)\" onChange={handleChange} required className=\"max-w-sm\" />\r\n                  </div>\r\n                  <h3>Location</h3>\r\n                  <div className=\"flex gap-3\">\r\n\r\n                        <Input type=\"number\" name=\"areaId\" placeholder=\"Area ID\" onChange={handleChange} required className=\"max-w-sm\" />\r\n                        <Input type=\"text\" name=\"latitude\" placeholder=\"Latitude\" onChange={handleChange} required className=\"max-w-sm\" />\r\n                        <Input type=\"text\" name=\"longitude\" placeholder=\"Longitude\" onChange={handleChange} required className=\"max-w-sm\" />\r\n                  </div>\r\n                  <div className=\"flex justify-end\">\r\n                        <Button type=\"submit\" className=\"rounded-full\">Create Seller</Button>\r\n                  </div>\r\n\r\n            </Form>\r\n      );\r\n}\r\n"], "names": ["CreateSeller", "useActionData", "formData", "setFormData", "useState", "name", "address", "email", "customerSupportNumber", "owner", "firstName", "lastName", "mobileNumber", "password", "businessId", "roles", "areaId", "latitude", "longitude", "handleChange", "e", "value", "target", "startsWith", "field", "split", "prev", "navigate", "useNavigate", "jsxs", "Form", "method", "className", "children", "jsx", "<PERSON><PERSON>", "variant", "size", "onClick", "ArrowLeft", "Input", "type", "placeholder", "onChange", "required"], "mappings": ";;;;;;;;;;;AAgDA,SAAwBA,eAAe;AACdC,gBAAc;AACjC,QAAM,CAACC,UAAUC,WAAW,IAAIC,sBAAS;AAAA,IACnCC,MAAM;AAAA,IACNC,SAAS;AAAA,IACTC,OAAO;AAAA,IACPC,uBAAuB;AAAA,IACvBC,OAAO;AAAA,MACDC,WAAW;AAAA,MACXC,UAAU;AAAA,MACVJ,OAAO;AAAA,MACPK,cAAc;AAAA,MACdN,SAAS;AAAA,MACTO,UAAU;AAAA,MACVC,YAAY;AAAA,MACZC,OAAO;AAAA,IACb;AAAA,IACAC,QAAQ;AAAA,IACRC,UAAU;AAAA,IACVC,WAAW;AAAA,EACjB,CAAC;AAEK,QAAAC,eAAgBC,OAA2C;AAC3D,UAAM;AAAA,MAAEf;AAAAA,MAAMgB;AAAAA,IAAM,IAAID,EAAEE;AACtB,QAAAjB,KAAKkB,WAAW,QAAQ,GAAG;AACzB,YAAMC,QAAQnB,KAAKoB,MAAM,GAAG,EAAE,CAAC;AAC/BtB,kBAAauB,WAAU;AAAA,QACjB,GAAGA;AAAAA,QACHjB,OAAO;AAAA,UACD,GAAGiB,KAAKjB;AAAAA,UACR,CAACe,KAAK,GAAGH;AAAAA,QACf;AAAA,MACN,EAAE;AAAA,IACR,OAAO;AACWlB,kBAACuB,WAAU;AAAA,QAAE,GAAGA;AAAAA,QAAM,CAACrB,IAAI,GAAGgB;AAAAA,MAAM,EAAE;AAAA,IACxD;AAAA,EACN;AACA,QAAMM,WAAWC,YAAY;AAE7B,SACOC,kCAAAA,KAAAC,MAAA;AAAA,IAAKC,QAAO;AAAA,IAAOC,WAAU;AAAA,IACxBC,UAAA,CAAAC,kCAAA,IAAC,OAAI;AAAA,MAAAF,WAAU;AAAA,MACTC,UAAAJ,kCAAA,KAACM,QAAO;AAAA,QAAAC,SAAQ;AAAA,QAAQC,MAAK;AAAA,QAAKC,SAASA,MAAMX,SAAS,kBAAkB;AAAA,QACtEM,UAAA,CAACC,kCAAA,IAAAK,WAAA;AAAA,UAAUP,WAAU;AAAA,QAAe,CAAA,GAAE,iBAAA;AAAA,MAE5C,CAAA;AAAA,IACN,CAAA,0CAEC,OACK;AAAA,MAAAC,UAAA,CAAAC,kCAAA,IAAC;QAAGD,UAAc;AAAA,MAAA,CAAA,GAClBJ,kCAAA,KAAC,OAAI;AAAA,QAAAG,WAAU;AAAA,QAAiBC,UAAA,CAAA,KAC1BJ,kCAAA,KAAC,OAAI;AAAA,UAAAG,WAAU;AAAA,UACTC,UAAA,CAAAC,kCAAA,IAACM,OAAM;AAAA,YAAAC,MAAK;AAAA,YAAOpC,MAAK;AAAA,YAAOqC,aAAY;AAAA,YAAOC,UAAUxB;AAAAA,YAAcyB,UAAQ;AAAA,YAACZ,WAAU;AAAA,UAAW,CAAA,GACvGE,kCAAA,IAAAM,OAAA;AAAA,YAAMC,MAAK;AAAA,YAAOpC,MAAK;AAAA,YAAUqC,aAAY;AAAA,YAAUC,UAAUxB;AAAAA,YAAcyB,UAAQ;AAAA,YAACZ,WAAU;AAAA,UAAW,CAAA,CAAA;AAAA,QACpH,CAAA,GACAH,kCAAA,KAAC,OAAI;AAAA,UAAAG,WAAU;AAAA,UACTC,UAAA,CAAAC,kCAAA,IAACM,OAAM;AAAA,YAAAC,MAAK;AAAA,YAAQpC,MAAK;AAAA,YAAQqC,aAAY;AAAA,YAAQC,UAAUxB;AAAAA,YAAcyB,UAAQ;AAAA,YAACZ,WAAU;AAAA,UAAW,CAAA,GAC1GE,kCAAA,IAAAM,OAAA;AAAA,YAAMC,MAAK;AAAA,YAAOpC,MAAK;AAAA,YAAwBqC,aAAY;AAAA,YAA0BC,UAAUxB;AAAAA,YAAcyB,UAAQ;AAAA,YAACZ,WAAU;AAAA,UAAW,CAAA,CAAA;AAAA,QAClJ,CAAA,CAAA;AAAA,MACN,CAAA,CAAA;AAAA,IACN,CAAA,GACAE,kCAAA,IAAC;MAAGD,UAAa;AAAA,IAAA,CAAA,GAEjBJ,kCAAA,KAAC,OAAI;AAAA,MAAAG,WAAU;AAAA,MACTC,UAAA,CAAAC,kCAAA,IAACM,OAAM;AAAA,QAAAC,MAAK;AAAA,QAAOpC,MAAK;AAAA,QAAkBqC,aAAY;AAAA,QAAaC,UAAUxB;AAAAA,QAAcyB,UAAQ;AAAA,QAACZ,WAAU;AAAA,MAAW,CAAA,GACxHE,kCAAA,IAAAM,OAAA;AAAA,QAAMC,MAAK;AAAA,QAAOpC,MAAK;AAAA,QAAiBqC,aAAY;AAAA,QAAYC,UAAUxB;AAAAA,QAAcyB,UAAQ;AAAA,QAACZ,WAAU;AAAA,MAAW,CAAA,CAAA;AAAA,IAE7H,CAAA,GACAH,kCAAA,KAAC,OAAI;AAAA,MAAAG,WAAU;AAAA,MAETC,UAAA,CAAAC,kCAAA,IAACM,OAAM;AAAA,QAAAC,MAAK;AAAA,QAAQpC,MAAK;AAAA,QAAcqC,aAAY;AAAA,QAAcC,UAAUxB;AAAAA,QAAcyB,UAAQ;AAAA,QAACZ,WAAU;AAAA,MAAW,CAAA,GACtHE,kCAAA,IAAAM,OAAA;AAAA,QAAMC,MAAK;AAAA,QAAOpC,MAAK;AAAA,QAAqBqC,aAAY;AAAA,QAAgBC,UAAUxB;AAAAA,QAAcyB,UAAQ;AAAA,QAACZ,WAAU;AAAA,MAAW,CAAA,CAAA;AAAA,IACrI,CAAA,GACAH,kCAAA,KAAC,OAAI;AAAA,MAAAG,WAAU;AAAA,MACTC,UAAA,CAAAC,kCAAA,IAACM,OAAM;AAAA,QAAAC,MAAK;AAAA,QAAOpC,MAAK;AAAA,QAAgBqC,aAAY;AAAA,QAAgBC,UAAUxB;AAAAA,QAAcyB,UAAQ;AAAA,QAACZ,WAAU;AAAA,MAAW,CAAA,GACzHE,kCAAA,IAAAM,OAAA;AAAA,QAAMC,MAAK;AAAA,QAAWpC,MAAK;AAAA,QAAiBqC,aAAY;AAAA,QAAWC,UAAUxB;AAAAA,QAAcyB,UAAQ;AAAA,QAACZ,WAAU;AAAA,MAAW,CAAA,CAAA;AAAA,IAChI,CAAA,GACAH,kCAAA,KAAC,OAAI;AAAA,MAAAG,WAAU;AAAA,MACTC,UAAA,CAAAC,kCAAA,IAACM,OAAM;AAAA,QAAAC,MAAK;AAAA,QAASpC,MAAK;AAAA,QAAmBqC,aAAY;AAAA,QAAcC,UAAUxB;AAAAA,QAAcyB,UAAQ;AAAA,QAACZ,WAAU;AAAA,MAAW,CAAA,GAC5HE,kCAAA,IAAAM,OAAA;AAAA,QAAMC,MAAK;AAAA,QAAOpC,MAAK;AAAA,QAAcqC,aAAY;AAAA,QAA0BC,UAAUxB;AAAAA,QAAcyB,UAAQ;AAAA,QAACZ,WAAU;AAAA,MAAW,CAAA,CAAA;AAAA,IACxI,CAAA,GACAE,kCAAA,IAAC;MAAGD,UAAQ;AAAA,IAAA,CAAA,GACZJ,kCAAA,KAAC,OAAI;AAAA,MAAAG,WAAU;AAAA,MAETC,UAAA,CAAAC,kCAAA,IAACM,OAAM;AAAA,QAAAC,MAAK;AAAA,QAASpC,MAAK;AAAA,QAASqC,aAAY;AAAA,QAAUC,UAAUxB;AAAAA,QAAcyB,UAAQ;AAAA,QAACZ,WAAU;AAAA,MAAW,CAAA,GAC9GE,kCAAA,IAAAM,OAAA;AAAA,QAAMC,MAAK;AAAA,QAAOpC,MAAK;AAAA,QAAWqC,aAAY;AAAA,QAAWC,UAAUxB;AAAAA,QAAcyB,UAAQ;AAAA,QAACZ,WAAU;AAAA,MAAW,CAAA,GAC/GE,kCAAA,IAAAM,OAAA;AAAA,QAAMC,MAAK;AAAA,QAAOpC,MAAK;AAAA,QAAYqC,aAAY;AAAA,QAAYC,UAAUxB;AAAAA,QAAcyB,UAAQ;AAAA,QAACZ,WAAU;AAAA,MAAW,CAAA,CAAA;AAAA,IACxH,CAAA,GACAE,kCAAA,IAAC,OAAI;AAAA,MAAAF,WAAU;AAAA,MACTC,UAAAC,kCAAA,IAACC,QAAO;AAAA,QAAAM,MAAK;AAAA,QAAST,WAAU;AAAA,QAAeC,UAAA;AAAA,MAAa,CAAA;AAAA,IAClE,CAAA,CAAA;AAAA,EAEN,CAAA;AAEZ;"}