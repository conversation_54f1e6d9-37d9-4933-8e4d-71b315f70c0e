{"version": 3, "file": "truck-ypDO_-A_.js", "sources": ["../../../node_modules/lucide-react/dist/esm/icons/truck.js"], "sourcesContent": ["/**\n * @license lucide-react v0.453.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst Truck = createLucideIcon(\"Truck\", [\n  [\"path\", { d: \"M14 18V6a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2v11a1 1 0 0 0 1 1h2\", key: \"wrbu53\" }],\n  [\"path\", { d: \"M15 18H9\", key: \"1lyqi6\" }],\n  [\n    \"path\",\n    {\n      d: \"M19 18h2a1 1 0 0 0 1-1v-3.65a1 1 0 0 0-.22-.624l-3.48-4.35A1 1 0 0 0 17.52 8H14\",\n      key: \"lysw3i\"\n    }\n  ],\n  [\"circle\", { cx: \"17\", cy: \"18\", r: \"2\", key: \"332jqn\" }],\n  [\"circle\", { cx: \"7\", cy: \"18\", r: \"2\", key: \"19iecd\" }]\n]);\n\nexport { Truck as default };\n//# sourceMappingURL=truck.js.map\n"], "names": [], "mappings": ";AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASK,MAAC,QAAQ,iBAAiB,SAAS;AAAA,EACtC,CAAC,QAAQ,EAAE,GAAG,6DAA6D,KAAK,SAAQ,CAAE;AAAA,EAC1F,CAAC,QAAQ,EAAE,GAAG,YAAY,KAAK,SAAQ,CAAE;AAAA,EACzC;AAAA,IACE;AAAA,IACA;AAAA,MACE,GAAG;AAAA,MACH,KAAK;AAAA,IACX;AAAA,EACG;AAAA,EACD,CAAC,UAAU,EAAE,IAAI,MAAM,IAAI,MAAM,GAAG,KAAK,KAAK,UAAU;AAAA,EACxD,CAAC,UAAU,EAAE,IAAI,KAAK,IAAI,MAAM,GAAG,KAAK,KAAK,SAAU,CAAA;AACzD,CAAC;", "x_google_ignoreList": [0]}