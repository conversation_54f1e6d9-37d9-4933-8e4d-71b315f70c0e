{"version": 3, "file": "home.myTrips-YQ1qVfx2.js", "sources": ["../../../app/routes/home.myTrips.tsx"], "sourcesContent": ["\r\n\r\nimport { <PERSON>, json, use<PERSON><PERSON><PERSON>, use<PERSON><PERSON>derD<PERSON> } from \"@remix-run/react\";\r\nimport { format } from \"date-fns\";\r\nimport { CalendarIcon } from \"lucide-react\";\r\nimport { useEffect, useState } from \"react\";\r\nimport { Button } from \"~/components/ui/button\";\r\nimport { Calendar } from \"~/components/ui/calendar\";\r\nimport { Input } from \"~/components/ui/input\";\r\nimport { Popover, PopoverContent, PopoverTrigger } from \"~/components/ui/popover\";\r\nimport { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from \"~/components/ui/table\";\r\nimport { TripSummary } from \"~/types/api/businessConsoleService/tripSummaryDetails\";\r\nimport { withAuth, withResponse } from \"@utils/auth-utils\";\r\nimport { getSellerTrips } from \"~/services/myTripsService\";\r\nimport { BcTripSummaryDto } from \"~/types/api/businessConsoleService/MyTrips\";\r\n\r\n\r\n\r\nexport const loader = withAuth(async ({ request }) => {\r\n      try {\r\n            const url = new URL(request.url);\r\n            const date = url.searchParams.get(\"date\") || new Date().toISOString().split(\"T\")[0];\r\n\r\n            if (!date) {\r\n                  throw json(\r\n                        { error: \"Date is required\" },\r\n                        { status: 400 }\r\n                  );\r\n            }\r\n\r\n            const response = await getSellerTrips(date, request);\r\n            return withResponse({ data: response.data }, response.headers);\r\n      } catch (error) {\r\n            console.error('Trip summary error:', error);\r\n            throw new Error(`Error fetching trip details: ${error}`);\r\n      }\r\n});\r\n\r\nexport default function MyTrips() {\r\n      const data = useLoaderData<{ data: BcTripSummaryDto[] }>();\r\n      const [date, setDate] = useState<Date | undefined>(new Date());\r\n      const [searchTerm, setSearchTerm] = useState(\"\")\r\n      const fetcher = useFetcher<{ data: BcTripSummaryDto[] }>()\r\n      const [tripData, setTriData] = useState<BcTripSummaryDto[] | []>(data.data || [])\r\n\r\n      const handleSubmit = (date: Date | undefined) => {\r\n            const formData = new FormData();\r\n            const deliveryDate = format(date || \"\", \"yyyy-MM-dd\")\r\n            formData.append(\"date\", deliveryDate || \"\")\r\n            fetcher.submit(formData, { method: \"GET\" });\r\n      }\r\n      const itemsPerPage = 200\r\n      const [currentPage, setCurrentPage] = useState(1)\r\n\r\n      const totalPages = Math.ceil(tripData.length / itemsPerPage);\r\n\r\n      const startIndex = (currentPage - 1) * itemsPerPage;\r\n\r\n      const currentTrips = tripData.slice(startIndex, startIndex + itemsPerPage);\r\n\r\n      useEffect(() => {\r\n\r\n            if (fetcher.data) {\r\n                  setTriData(fetcher.data.data)\r\n            }\r\n\r\n      }, [fetcher.data?.data])\r\n\r\n      const filterTrips = (trip: BcTripSummaryDto) => {\r\n            return (\r\n\r\n                  (trip.driverName?.toLowerCase() || \"\").includes(searchTerm.toLowerCase())\r\n            );\r\n      }\r\n\r\n\r\n      const handleSetPage = (page: number) => {\r\n            if (page >= 1 && page <= totalPages) {\r\n                  setCurrentPage(page)\r\n            }\r\n      }\r\n\r\n      const handleSort = (trip: BcTripSummaryDto) => {\r\n\r\n            return trip.tripStatus === \"Dispatched\"\r\n\r\n      }\r\n      return (\r\n            <div className=\"container mx-auto w-full\" >\r\n                  <div className=\"flex my-7 space-x-10\">\r\n                        <div className=\" flex space-x-2\">\r\n                              <Popover>\r\n                                    <PopoverTrigger asChild>\r\n                                          <Button variant=\"outline\" className=\"w-[280px]\">\r\n                                                <CalendarIcon className=\"mr-2 h-4 w-4\" />\r\n                                                {date ? format(date, \"PPP\") : \"Pick a date\"}\r\n                                          </Button>\r\n                                    </PopoverTrigger>\r\n                                    <PopoverContent className=\"w-auto p-0\" >\r\n                                          <Calendar\r\n\r\n                                                mode=\"single\"\r\n                                                selected={date}\r\n                                                onSelect={setDate}\r\n                                                initialFocus\r\n                                          />\r\n                                    </PopoverContent>\r\n                              </Popover>\r\n\r\n                              <Button onClick={() => handleSubmit(date)}  >\r\n                                    Get Trips\r\n                              </Button>\r\n                        </div>\r\n                        <Input placeholder=\"Search By DriverName\"\r\n                              value={searchTerm}\r\n                              onChange={(e) => setSearchTerm(e.target.value)}\r\n                              className=\"max-w-sm\"\r\n                        />\r\n                  </div>\r\n                  <Table>\r\n                        <TableHeader className=\"bg-gray-100\">\r\n                              <TableHead>Driver</TableHead>\r\n                              {/* <TableHead>Truck No</TableHead> */}\r\n                              <TableHead>Booked</TableHead>\r\n                              <TableHead>Status</TableHead>\r\n                              <TableHead>Delivered</TableHead>\r\n\r\n\r\n                        </TableHeader>\r\n                        <TableBody>\r\n                              {currentTrips.length > 0 ? currentTrips.sort((a, b) => {\r\n                                    const Priority: { [key: string]: number } = { Dispatched: 1, Open: 2 };\r\n                                    return (Priority[a.tripStatus] || 3) - (Priority[b.tripStatus] || 3);\r\n                              }).filter((trip) => filterTrips(trip)).map((x) => (\r\n                                    <TableRow key={x.tripId}>\r\n                                          <TableCell>{x.driverName}</TableCell>\r\n\r\n\r\n\r\n                                          <TableCell><p>{x.totalOrders}</p>\r\n\r\n                                          </TableCell>\r\n                                          <TableCell className={x.tripStatus === \"Dispatched\" ? \"text-red-500\" : x.tripStatus === \"Open\" ? \"text-orange-500\" : \"text-green-600\"}>{x.tripStatus}</TableCell>\r\n                                          <TableCell>{x.totalDeliveredOrders}</TableCell>\r\n\r\n\r\n                                    </TableRow>\r\n                              )\r\n\r\n                              ) : (\r\n                                    <TableRow>\r\n                                          <TableCell\r\n                                                colSpan={9}\r\n                                                className=\"h-24 text-center\"\r\n                                          >\r\n                                                No results.\r\n                                          </TableCell>\r\n                                    </TableRow>\r\n                              )\r\n                              }\r\n                        </TableBody>\r\n                  </Table>\r\n                  <div className=\"flex items-center space-x-2 my-2\">\r\n                        <Button\r\n                              variant=\"outline\"\r\n                              size=\"sm\"\r\n                              onClick={() => handleSetPage(currentPage - 1)}\r\n                              disabled={currentPage === 1}\r\n                        >\r\n                              Previous\r\n                        </Button>\r\n                        <span className=\"text-gray-700\">\r\n                              Page {currentPage} of {totalPages}\r\n                        </span>\r\n                        <Button\r\n                              variant=\"outline\"\r\n                              size=\"sm\"\r\n                              onClick={() =>\r\n                                    handleSetPage(currentPage + 1)\r\n                              }\r\n                              disabled={currentPage === totalPages}\r\n                        >\r\n                              Next\r\n                        </Button>\r\n                  </div>\r\n            </div>\r\n      )\r\n}\r\n"], "names": ["MyTrips", "data", "useLoaderData", "date", "setDate", "useState", "Date", "searchTerm", "setSearchTerm", "fetcher", "useFetcher", "tripData", "setTriData", "handleSubmit", "formData", "FormData", "deliveryDate", "format", "append", "submit", "method", "itemsPerPage", "currentPage", "setCurrentPage", "totalPages", "Math", "ceil", "length", "startIndex", "currentTrips", "slice", "useEffect", "filterTrips", "trip", "<PERSON><PERSON><PERSON>", "toLowerCase", "includes", "handleSetPage", "page", "jsxs", "className", "children", "Popover", "jsx", "PopoverTrigger", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "variant", "CalendarIcon", "PopoverC<PERSON>nt", "Calendar", "mode", "selected", "onSelect", "initialFocus", "onClick", "Input", "placeholder", "value", "onChange", "e", "target", "Table", "TableHeader", "TableHead", "TableBody", "sort", "a", "b", "Priority", "Dispatched", "Open", "tripStatus", "filter", "map", "x", "TableRow", "TableCell", "totalOrders", "totalDeliveredOrders", "tripId", "colSpan", "size", "disabled"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsCA,SAAwBA,UAAU;;AAC5B,QAAMC,OAAOC,cAA4C;AACzD,QAAM,CAACC,MAAMC,OAAO,IAAIC,aAAAA,SAA2B,oBAAIC,MAAM;AAC7D,QAAM,CAACC,YAAYC,aAAa,IAAIH,aAAAA,SAAS,EAAE;AAC/C,QAAMI,UAAUC,WAAyC;AACnD,QAAA,CAACC,UAAUC,UAAU,IAAIP,sBAAkCJ,KAAKA,QAAQ,EAAE;AAE1E,QAAAY,eAAgBV,WAA2B;AACrC,UAAAW,WAAW,IAAIC,SAAS;AAC9B,UAAMC,eAAeC,OAAOd,SAAQ,IAAI,YAAY;AAC3CW,aAAAI,OAAO,QAAQF,gBAAgB,EAAE;AAC1CP,YAAQU,OAAOL,UAAU;AAAA,MAAEM,QAAQ;AAAA,IAAM,CAAC;AAAA,EAChD;AACA,QAAMC,eAAe;AACrB,QAAM,CAACC,aAAaC,cAAc,IAAIlB,aAAAA,SAAS,CAAC;AAEhD,QAAMmB,aAAaC,KAAKC,KAAKf,SAASgB,SAASN,YAAY;AAErD,QAAAO,cAAcN,cAAc,KAAKD;AAEvC,QAAMQ,eAAelB,SAASmB,MAAMF,YAAYA,aAAaP,YAAY;AAEzEU,eAAAA,UAAU,MAAM;AAEV,QAAItB,QAAQR,MAAM;AACDW,iBAAAH,QAAQR,KAAKA,IAAI;AAAA,IAClC;AAAA,EAEH,GAAA,EAACQ,aAAQR,SAARQ,mBAAcR,IAAI,CAAC;AAEjB,QAAA+B,cAAeC,UAA2B;;AAGnC,cAAAA,MAAAA,KAAKC,eAALD,gBAAAA,IAAiBE,kBAAiB,IAAIC,SAAS7B,WAAW4B,aAAa;AAAA,EAEpF;AAGM,QAAAE,gBAAiBC,UAAiB;AAC9B,QAAAA,QAAQ,KAAKA,QAAQd,YAAY;AAC/BD,qBAAee,IAAI;AAAA,IACzB;AAAA,EACN;AAQM,SAAAC,kCAAAA,KAAC,OAAI;AAAA,IAAAC,WAAU;AAAA,IACTC,UAAA,CAACF,kCAAA,KAAA,OAAA;AAAA,MAAIC,WAAU;AAAA,MACTC,UAAA,CAACF,kCAAA,KAAA,OAAA;AAAA,QAAIC,WAAU;AAAA,QACTC,UAAA,CAAAF,kCAAA,KAACG,SACK;AAAA,UAAAD,UAAA,CAACE,kCAAA,IAAAC,gBAAA;AAAA,YAAeC,SAAO;AAAA,YACjBJ,UAAAF,kCAAA,KAACO;cAAOC,SAAQ;AAAA,cAAUP,WAAU;AAAA,cAC9BC,UAAA,CAACE,kCAAA,IAAAK,UAAA;AAAA,gBAAaR,WAAU;AAAA,cAAe,CAAA,GACtCrC,OAAOc,OAAOd,MAAM,KAAK,IAAI,aAAA;AAAA,YACpC,CAAA;AAAA,UACN,CAAA,GACAwC,kCAAA,IAACM,gBAAe;AAAA,YAAAT,WAAU;AAAA,YACpBC,UAAAE,kCAAA,IAACO,YAAA;AAAA,cAEKC,MAAK;AAAA,cACLC,UAAUjD;AAAAA,cACVkD,UAAUjD;AAAAA,cACVkD,cAAY;AAAA,YAClB,CAAA;AAAA,UACN,CAAA,CAAA;AAAA,QACN,CAAA,yCAECR,QAAO;AAAA,UAAAS,SAASA,MAAM1C,aAAaV,IAAI;AAAA,UAAKsC,UAE7C;AAAA,QAAA,CAAA,CAAA;AAAA,MACN,CAAA,GACAE,kCAAA,IAACa,OAAA;AAAA,QAAMC,aAAY;AAAA,QACbC,OAAOnD;AAAAA,QACPoD,UAAWC,OAAMpD,cAAcoD,EAAEC,OAAOH,KAAK;AAAA,QAC7ClB,WAAU;AAAA,MAAA,CAChB,CAAA;AAAA,IACN,CAAA,0CACCsB,OACK;AAAA,MAAArB,UAAA,CAACF,kCAAA,KAAAwB,aAAA;AAAA,QAAYvB,WAAU;AAAA,QACjBC,UAAA,CAAAE,kCAAA,IAACqB;UAAUvB,UAAM;AAAA,QAAA,CAAA,GAEjBE,kCAAA,IAACqB;UAAUvB,UAAM;AAAA,QAAA,CAAA,GACjBE,kCAAA,IAACqB;UAAUvB,UAAM;AAAA,QAAA,CAAA,GACjBE,kCAAA,IAACqB;UAAUvB,UAAS;AAAA,QAAA,CAAA,CAAA;AAAA,MAG1B,CAAA,GACAE,kCAAA,IAACsB;QACMxB,UAAaZ,aAAAF,SAAS,IAAIE,aAAaqC,KAAK,CAACC,GAAGC,MAAM;AACjD,gBAAMC,WAAsC;AAAA,YAAEC,YAAY;AAAA,YAAGC,MAAM;AAAA,UAAE;AAC7D,kBAAAF,SAASF,EAAEK,UAAU,KAAK,MAAMH,SAASD,EAAEI,UAAU,KAAK;AAAA,SACvE,EAAEC,OAAQxC,UAASD,YAAYC,IAAI,CAAC,EAAEyC,IAAKC,OACtCpC,kCAAA,KAACqC,UACK;AAAA,UAAAnC,UAAA,CAACE,kCAAA,IAAAkC,WAAA;AAAA,YAAWpC,YAAEP;AAAAA,UAAW,CAAA,yCAIxB2C,WAAU;AAAA,YAAApC,UAAAE,kCAAA,IAAC,KAAG;AAAA,cAAAF,UAAAkC,EAAEG;AAAAA,YAAY,CAAA;AAAA,UAE7B,CAAA,GACCnC,kCAAA,IAAAkC,WAAA;AAAA,YAAUrC,WAAWmC,EAAEH,eAAe,eAAe,iBAAiBG,EAAEH,eAAe,SAAS,oBAAoB;AAAA,YAAmB/B,YAAE+B;AAAAA,UAAW,CAAA,GACrJ7B,kCAAA,IAACkC,WAAW;AAAA,YAAApC,UAAAkC,EAAEI;AAAAA,UAAqB,CAAA,CAAA;AAAA,QAAA,GAT1BJ,EAAEK,MAYjB,CAGN,0CACOJ,UACK;AAAA,UAAAnC,UAAAE,kCAAA,IAACkC,WAAA;AAAA,YACKI,SAAS;AAAA,YACTzC,WAAU;AAAA,YACfC,UAAA;AAAA,UAED,CAAA;AAAA,QACN,CAAA;AAAA,MAGZ,CAAA,CAAA;AAAA,IACN,CAAA,GACAF,kCAAA,KAAC,OAAI;AAAA,MAAAC,WAAU;AAAA,MACTC,UAAA,CAAAE,kCAAA,IAACG,QAAA;AAAA,QACKC,SAAQ;AAAA,QACRmC,MAAK;AAAA,QACL3B,SAASA,MAAMlB,cAAcf,cAAc,CAAC;AAAA,QAC5C6D,UAAU7D,gBAAgB;AAAA,QAC/BmB,UAAA;AAAA,MAAA,CAED,GACAF,kCAAA,KAAC,QAAK;AAAA,QAAAC,WAAU;AAAA,QAAgBC,UAAA,CAAA,SACpBnB,aAAY,QAAKE,UAAA;AAAA,MAC7B,CAAA,GACAmB,kCAAA,IAACG,QAAA;AAAA,QACKC,SAAQ;AAAA,QACRmC,MAAK;AAAA,QACL3B,SAASA,MACHlB,cAAcf,cAAc,CAAC;AAAA,QAEnC6D,UAAU7D,gBAAgBE;AAAAA,QAC/BiB,UAAA;AAAA,MAAA,CAED,CAAA;AAAA,IACN,CAAA,CAAA;AAAA,EACN,CAAA;AAEZ;"}