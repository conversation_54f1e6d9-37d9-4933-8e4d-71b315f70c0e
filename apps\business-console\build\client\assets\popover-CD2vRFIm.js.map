{"version": 3, "file": "popover-CD2vRFIm.js", "sources": ["../../../node_modules/@radix-ui/react-popover/dist/index.mjs", "../../../app/components/ui/popover.tsx"], "sourcesContent": ["\"use client\";\n\n// packages/react/popover/src/popover.tsx\nimport * as React from \"react\";\nimport { composeEventHandlers } from \"@radix-ui/primitive\";\nimport { useComposedRefs } from \"@radix-ui/react-compose-refs\";\nimport { createContextScope } from \"@radix-ui/react-context\";\nimport { DismissableLayer } from \"@radix-ui/react-dismissable-layer\";\nimport { useFocusGuards } from \"@radix-ui/react-focus-guards\";\nimport { FocusScope } from \"@radix-ui/react-focus-scope\";\nimport { useId } from \"@radix-ui/react-id\";\nimport * as PopperPrimitive from \"@radix-ui/react-popper\";\nimport { createPopperScope } from \"@radix-ui/react-popper\";\nimport { Portal as PortalPrimitive } from \"@radix-ui/react-portal\";\nimport { Presence } from \"@radix-ui/react-presence\";\nimport { Primitive } from \"@radix-ui/react-primitive\";\nimport { Slot } from \"@radix-ui/react-slot\";\nimport { useControllableState } from \"@radix-ui/react-use-controllable-state\";\nimport { hideOthers } from \"aria-hidden\";\nimport { RemoveScroll } from \"react-remove-scroll\";\nimport { jsx } from \"react/jsx-runtime\";\nvar POPOVER_NAME = \"Popover\";\nvar [createPopoverContext, createPopoverScope] = createContextScope(POPOVER_NAME, [\n  createPopperScope\n]);\nvar usePopperScope = createPopperScope();\nvar [PopoverProvider, usePopoverContext] = createPopoverContext(POPOVER_NAME);\nvar Popover = (props) => {\n  const {\n    __scopePopover,\n    children,\n    open: openProp,\n    defaultOpen,\n    onOpenChange,\n    modal = false\n  } = props;\n  const popperScope = usePopperScope(__scopePopover);\n  const triggerRef = React.useRef(null);\n  const [hasCustomAnchor, setHasCustomAnchor] = React.useState(false);\n  const [open = false, setOpen] = useControllableState({\n    prop: openProp,\n    defaultProp: defaultOpen,\n    onChange: onOpenChange\n  });\n  return /* @__PURE__ */ jsx(PopperPrimitive.Root, { ...popperScope, children: /* @__PURE__ */ jsx(\n    PopoverProvider,\n    {\n      scope: __scopePopover,\n      contentId: useId(),\n      triggerRef,\n      open,\n      onOpenChange: setOpen,\n      onOpenToggle: React.useCallback(() => setOpen((prevOpen) => !prevOpen), [setOpen]),\n      hasCustomAnchor,\n      onCustomAnchorAdd: React.useCallback(() => setHasCustomAnchor(true), []),\n      onCustomAnchorRemove: React.useCallback(() => setHasCustomAnchor(false), []),\n      modal,\n      children\n    }\n  ) });\n};\nPopover.displayName = POPOVER_NAME;\nvar ANCHOR_NAME = \"PopoverAnchor\";\nvar PopoverAnchor = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopePopover, ...anchorProps } = props;\n    const context = usePopoverContext(ANCHOR_NAME, __scopePopover);\n    const popperScope = usePopperScope(__scopePopover);\n    const { onCustomAnchorAdd, onCustomAnchorRemove } = context;\n    React.useEffect(() => {\n      onCustomAnchorAdd();\n      return () => onCustomAnchorRemove();\n    }, [onCustomAnchorAdd, onCustomAnchorRemove]);\n    return /* @__PURE__ */ jsx(PopperPrimitive.Anchor, { ...popperScope, ...anchorProps, ref: forwardedRef });\n  }\n);\nPopoverAnchor.displayName = ANCHOR_NAME;\nvar TRIGGER_NAME = \"PopoverTrigger\";\nvar PopoverTrigger = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopePopover, ...triggerProps } = props;\n    const context = usePopoverContext(TRIGGER_NAME, __scopePopover);\n    const popperScope = usePopperScope(__scopePopover);\n    const composedTriggerRef = useComposedRefs(forwardedRef, context.triggerRef);\n    const trigger = /* @__PURE__ */ jsx(\n      Primitive.button,\n      {\n        type: \"button\",\n        \"aria-haspopup\": \"dialog\",\n        \"aria-expanded\": context.open,\n        \"aria-controls\": context.contentId,\n        \"data-state\": getState(context.open),\n        ...triggerProps,\n        ref: composedTriggerRef,\n        onClick: composeEventHandlers(props.onClick, context.onOpenToggle)\n      }\n    );\n    return context.hasCustomAnchor ? trigger : /* @__PURE__ */ jsx(PopperPrimitive.Anchor, { asChild: true, ...popperScope, children: trigger });\n  }\n);\nPopoverTrigger.displayName = TRIGGER_NAME;\nvar PORTAL_NAME = \"PopoverPortal\";\nvar [PortalProvider, usePortalContext] = createPopoverContext(PORTAL_NAME, {\n  forceMount: void 0\n});\nvar PopoverPortal = (props) => {\n  const { __scopePopover, forceMount, children, container } = props;\n  const context = usePopoverContext(PORTAL_NAME, __scopePopover);\n  return /* @__PURE__ */ jsx(PortalProvider, { scope: __scopePopover, forceMount, children: /* @__PURE__ */ jsx(Presence, { present: forceMount || context.open, children: /* @__PURE__ */ jsx(PortalPrimitive, { asChild: true, container, children }) }) });\n};\nPopoverPortal.displayName = PORTAL_NAME;\nvar CONTENT_NAME = \"PopoverContent\";\nvar PopoverContent = React.forwardRef(\n  (props, forwardedRef) => {\n    const portalContext = usePortalContext(CONTENT_NAME, props.__scopePopover);\n    const { forceMount = portalContext.forceMount, ...contentProps } = props;\n    const context = usePopoverContext(CONTENT_NAME, props.__scopePopover);\n    return /* @__PURE__ */ jsx(Presence, { present: forceMount || context.open, children: context.modal ? /* @__PURE__ */ jsx(PopoverContentModal, { ...contentProps, ref: forwardedRef }) : /* @__PURE__ */ jsx(PopoverContentNonModal, { ...contentProps, ref: forwardedRef }) });\n  }\n);\nPopoverContent.displayName = CONTENT_NAME;\nvar PopoverContentModal = React.forwardRef(\n  (props, forwardedRef) => {\n    const context = usePopoverContext(CONTENT_NAME, props.__scopePopover);\n    const contentRef = React.useRef(null);\n    const composedRefs = useComposedRefs(forwardedRef, contentRef);\n    const isRightClickOutsideRef = React.useRef(false);\n    React.useEffect(() => {\n      const content = contentRef.current;\n      if (content) return hideOthers(content);\n    }, []);\n    return /* @__PURE__ */ jsx(RemoveScroll, { as: Slot, allowPinchZoom: true, children: /* @__PURE__ */ jsx(\n      PopoverContentImpl,\n      {\n        ...props,\n        ref: composedRefs,\n        trapFocus: context.open,\n        disableOutsidePointerEvents: true,\n        onCloseAutoFocus: composeEventHandlers(props.onCloseAutoFocus, (event) => {\n          event.preventDefault();\n          if (!isRightClickOutsideRef.current) context.triggerRef.current?.focus();\n        }),\n        onPointerDownOutside: composeEventHandlers(\n          props.onPointerDownOutside,\n          (event) => {\n            const originalEvent = event.detail.originalEvent;\n            const ctrlLeftClick = originalEvent.button === 0 && originalEvent.ctrlKey === true;\n            const isRightClick = originalEvent.button === 2 || ctrlLeftClick;\n            isRightClickOutsideRef.current = isRightClick;\n          },\n          { checkForDefaultPrevented: false }\n        ),\n        onFocusOutside: composeEventHandlers(\n          props.onFocusOutside,\n          (event) => event.preventDefault(),\n          { checkForDefaultPrevented: false }\n        )\n      }\n    ) });\n  }\n);\nvar PopoverContentNonModal = React.forwardRef(\n  (props, forwardedRef) => {\n    const context = usePopoverContext(CONTENT_NAME, props.__scopePopover);\n    const hasInteractedOutsideRef = React.useRef(false);\n    const hasPointerDownOutsideRef = React.useRef(false);\n    return /* @__PURE__ */ jsx(\n      PopoverContentImpl,\n      {\n        ...props,\n        ref: forwardedRef,\n        trapFocus: false,\n        disableOutsidePointerEvents: false,\n        onCloseAutoFocus: (event) => {\n          props.onCloseAutoFocus?.(event);\n          if (!event.defaultPrevented) {\n            if (!hasInteractedOutsideRef.current) context.triggerRef.current?.focus();\n            event.preventDefault();\n          }\n          hasInteractedOutsideRef.current = false;\n          hasPointerDownOutsideRef.current = false;\n        },\n        onInteractOutside: (event) => {\n          props.onInteractOutside?.(event);\n          if (!event.defaultPrevented) {\n            hasInteractedOutsideRef.current = true;\n            if (event.detail.originalEvent.type === \"pointerdown\") {\n              hasPointerDownOutsideRef.current = true;\n            }\n          }\n          const target = event.target;\n          const targetIsTrigger = context.triggerRef.current?.contains(target);\n          if (targetIsTrigger) event.preventDefault();\n          if (event.detail.originalEvent.type === \"focusin\" && hasPointerDownOutsideRef.current) {\n            event.preventDefault();\n          }\n        }\n      }\n    );\n  }\n);\nvar PopoverContentImpl = React.forwardRef(\n  (props, forwardedRef) => {\n    const {\n      __scopePopover,\n      trapFocus,\n      onOpenAutoFocus,\n      onCloseAutoFocus,\n      disableOutsidePointerEvents,\n      onEscapeKeyDown,\n      onPointerDownOutside,\n      onFocusOutside,\n      onInteractOutside,\n      ...contentProps\n    } = props;\n    const context = usePopoverContext(CONTENT_NAME, __scopePopover);\n    const popperScope = usePopperScope(__scopePopover);\n    useFocusGuards();\n    return /* @__PURE__ */ jsx(\n      FocusScope,\n      {\n        asChild: true,\n        loop: true,\n        trapped: trapFocus,\n        onMountAutoFocus: onOpenAutoFocus,\n        onUnmountAutoFocus: onCloseAutoFocus,\n        children: /* @__PURE__ */ jsx(\n          DismissableLayer,\n          {\n            asChild: true,\n            disableOutsidePointerEvents,\n            onInteractOutside,\n            onEscapeKeyDown,\n            onPointerDownOutside,\n            onFocusOutside,\n            onDismiss: () => context.onOpenChange(false),\n            children: /* @__PURE__ */ jsx(\n              PopperPrimitive.Content,\n              {\n                \"data-state\": getState(context.open),\n                role: \"dialog\",\n                id: context.contentId,\n                ...popperScope,\n                ...contentProps,\n                ref: forwardedRef,\n                style: {\n                  ...contentProps.style,\n                  // re-namespace exposed content custom properties\n                  ...{\n                    \"--radix-popover-content-transform-origin\": \"var(--radix-popper-transform-origin)\",\n                    \"--radix-popover-content-available-width\": \"var(--radix-popper-available-width)\",\n                    \"--radix-popover-content-available-height\": \"var(--radix-popper-available-height)\",\n                    \"--radix-popover-trigger-width\": \"var(--radix-popper-anchor-width)\",\n                    \"--radix-popover-trigger-height\": \"var(--radix-popper-anchor-height)\"\n                  }\n                }\n              }\n            )\n          }\n        )\n      }\n    );\n  }\n);\nvar CLOSE_NAME = \"PopoverClose\";\nvar PopoverClose = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopePopover, ...closeProps } = props;\n    const context = usePopoverContext(CLOSE_NAME, __scopePopover);\n    return /* @__PURE__ */ jsx(\n      Primitive.button,\n      {\n        type: \"button\",\n        ...closeProps,\n        ref: forwardedRef,\n        onClick: composeEventHandlers(props.onClick, () => context.onOpenChange(false))\n      }\n    );\n  }\n);\nPopoverClose.displayName = CLOSE_NAME;\nvar ARROW_NAME = \"PopoverArrow\";\nvar PopoverArrow = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopePopover, ...arrowProps } = props;\n    const popperScope = usePopperScope(__scopePopover);\n    return /* @__PURE__ */ jsx(PopperPrimitive.Arrow, { ...popperScope, ...arrowProps, ref: forwardedRef });\n  }\n);\nPopoverArrow.displayName = ARROW_NAME;\nfunction getState(open) {\n  return open ? \"open\" : \"closed\";\n}\nvar Root2 = Popover;\nvar Anchor2 = PopoverAnchor;\nvar Trigger = PopoverTrigger;\nvar Portal = PopoverPortal;\nvar Content2 = PopoverContent;\nvar Close = PopoverClose;\nvar Arrow2 = PopoverArrow;\nexport {\n  Anchor2 as Anchor,\n  Arrow2 as Arrow,\n  Close,\n  Content2 as Content,\n  Popover,\n  PopoverAnchor,\n  PopoverArrow,\n  PopoverClose,\n  PopoverContent,\n  PopoverPortal,\n  PopoverTrigger,\n  Portal,\n  Root2 as Root,\n  Trigger,\n  createPopoverScope\n};\n//# sourceMappingURL=index.mjs.map\n", "import * as React from \"react\"\r\nimport * as PopoverPrimitive from \"@radix-ui/react-popover\"\r\n\r\nimport { cn } from \"~/lib/utils\"\r\n\r\nconst Popover = PopoverPrimitive.Root\r\n\r\nconst PopoverTrigger = PopoverPrimitive.Trigger\r\n\r\nconst PopoverContent = React.forwardRef<\r\n  React.ElementRef<typeof PopoverPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof PopoverPrimitive.Content>\r\n>(({ className, align = \"center\", sideOffset = 4, ...props }, ref) => (\r\n  <PopoverPrimitive.Portal>\r\n    <PopoverPrimitive.Content\r\n      ref={ref}\r\n      align={align}\r\n      sideOffset={sideOffset}\r\n      className={cn(\r\n        \"z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  </PopoverPrimitive.Portal>\r\n))\r\nPopoverContent.displayName = PopoverPrimitive.Content.displayName\r\n\r\nconst PopoverClose = React.forwardRef<\r\n  React.ElementRef<typeof PopoverPrimitive.Close>,\r\n  React.ComponentPropsWithoutRef<typeof PopoverPrimitive.Close>\r\n>(({ className, ...props }, ref) => (\r\n  <PopoverPrimitive.Close\r\n    ref={ref}\r\n    className={cn(\r\n      \"focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nPopoverClose.displayName = PopoverPrimitive.Close.displayName\r\n\r\nexport { Popover, PopoverTrigger, PopoverContent, PopoverClose }\r\n"], "names": ["Popover", "React.useRef", "React.useState", "jsx", "PopperPrimitive.Root", "React.useCallback", "React.forwardRef", "React.useEffect", "PopperPrimitive.Anchor", "PopoverTrigger", "PortalPrimitive", "PopoverC<PERSON>nt", "RemoveScroll", "PopperPrimitive.Content", "PopoverClose", "PopperPrimitive.Arrow", "PopoverPrimitive.Root", "PopoverPrimitive.Trigger", "PopoverPrimitive.Portal", "PopoverPrimitive.Content", "PopoverPrimitive.Close"], "mappings": ";;;;;;;;;;;AAqBA,IAAI,eAAe;AACnB,IAAI,CAAC,sBAAsB,kBAAkB,IAAI,mBAAmB,cAAc;AAAA,EAChF;AACF,CAAC;AACD,IAAI,iBAAiB,kBAAmB;AACxC,IAAI,CAAC,iBAAiB,iBAAiB,IAAI,qBAAqB,YAAY;AAC5E,IAAIA,YAAU,CAAC,UAAU;AACvB,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA,MAAM;AAAA,IACN;AAAA,IACA;AAAA,IACA,QAAQ;AAAA,EACZ,IAAM;AACJ,QAAM,cAAc,eAAe,cAAc;AACjD,QAAM,aAAaC,aAAY,OAAC,IAAI;AACpC,QAAM,CAAC,iBAAiB,kBAAkB,IAAIC,aAAAA,SAAe,KAAK;AAClE,QAAM,CAAC,OAAO,OAAO,OAAO,IAAI,qBAAqB;AAAA,IACnD,MAAM;AAAA,IACN,aAAa;AAAA,IACb,UAAU;AAAA,EACd,CAAG;AACD,SAAuBC,kCAAG,IAACC,SAAsB,EAAE,GAAG,aAAa,UAA0BD,kCAAG;AAAA,IAC9F;AAAA,IACA;AAAA,MACE,OAAO;AAAA,MACP,WAAW,MAAO;AAAA,MAClB;AAAA,MACA;AAAA,MACA,cAAc;AAAA,MACd,cAAcE,aAAAA,YAAkB,MAAM,QAAQ,CAAC,aAAa,CAAC,QAAQ,GAAG,CAAC,OAAO,CAAC;AAAA,MACjF;AAAA,MACA,mBAAmBA,aAAAA,YAAkB,MAAM,mBAAmB,IAAI,GAAG,CAAA,CAAE;AAAA,MACvE,sBAAsBA,aAAAA,YAAkB,MAAM,mBAAmB,KAAK,GAAG,CAAA,CAAE;AAAA,MAC3E;AAAA,MACA;AAAA,IACN;AAAA,EACA,GAAK;AACL;AACAL,UAAQ,cAAc;AACtB,IAAI,cAAc;AAClB,IAAI,gBAAgBM,aAAgB;AAAA,EAClC,CAAC,OAAO,iBAAiB;AACvB,UAAM,EAAE,gBAAgB,GAAG,YAAW,IAAK;AAC3C,UAAM,UAAU,kBAAkB,aAAa,cAAc;AAC7D,UAAM,cAAc,eAAe,cAAc;AACjD,UAAM,EAAE,mBAAmB,qBAAoB,IAAK;AACpDC,iBAAAA,UAAgB,MAAM;AACpB,wBAAmB;AACnB,aAAO,MAAM,qBAAsB;AAAA,IACzC,GAAO,CAAC,mBAAmB,oBAAoB,CAAC;AAC5C,WAAuBJ,kCAAG,IAACK,QAAwB,EAAE,GAAG,aAAa,GAAG,aAAa,KAAK,cAAc;AAAA,EAC5G;AACA;AACA,cAAc,cAAc;AAC5B,IAAI,eAAe;AACnB,IAAIC,mBAAiBH,aAAgB;AAAA,EACnC,CAAC,OAAO,iBAAiB;AACvB,UAAM,EAAE,gBAAgB,GAAG,aAAY,IAAK;AAC5C,UAAM,UAAU,kBAAkB,cAAc,cAAc;AAC9D,UAAM,cAAc,eAAe,cAAc;AACjD,UAAM,qBAAqB,gBAAgB,cAAc,QAAQ,UAAU;AAC3E,UAAM,UAA0BH,kCAAG;AAAA,MACjC,UAAU;AAAA,MACV;AAAA,QACE,MAAM;AAAA,QACN,iBAAiB;AAAA,QACjB,iBAAiB,QAAQ;AAAA,QACzB,iBAAiB,QAAQ;AAAA,QACzB,cAAc,SAAS,QAAQ,IAAI;AAAA,QACnC,GAAG;AAAA,QACH,KAAK;AAAA,QACL,SAAS,qBAAqB,MAAM,SAAS,QAAQ,YAAY;AAAA,MACzE;AAAA,IACK;AACD,WAAO,QAAQ,kBAAkB,UAA0BA,kCAAG,IAACK,QAAwB,EAAE,SAAS,MAAM,GAAG,aAAa,UAAU,QAAO,CAAE;AAAA,EAC/I;AACA;AACAC,iBAAe,cAAc;AAC7B,IAAI,cAAc;AAClB,IAAI,CAAC,gBAAgB,gBAAgB,IAAI,qBAAqB,aAAa;AAAA,EACzE,YAAY;AACd,CAAC;AACD,IAAI,gBAAgB,CAAC,UAAU;AAC7B,QAAM,EAAE,gBAAgB,YAAY,UAAU,UAAW,IAAG;AAC5D,QAAM,UAAU,kBAAkB,aAAa,cAAc;AAC7D,SAAuBN,sCAAI,gBAAgB,EAAE,OAAO,gBAAgB,YAAY,UAA0BA,kCAAG,IAAC,UAAU,EAAE,SAAS,cAAc,QAAQ,MAAM,UAA0BA,sCAAIO,UAAiB,EAAE,SAAS,MAAM,WAAW,UAAU,EAAC,CAAE,EAAC,CAAE;AAC5P;AACA,cAAc,cAAc;AAC5B,IAAI,eAAe;AACnB,IAAIC,mBAAiBL,aAAgB;AAAA,EACnC,CAAC,OAAO,iBAAiB;AACvB,UAAM,gBAAgB,iBAAiB,cAAc,MAAM,cAAc;AACzE,UAAM,EAAE,aAAa,cAAc,YAAY,GAAG,aAAc,IAAG;AACnE,UAAM,UAAU,kBAAkB,cAAc,MAAM,cAAc;AACpE,WAAuBH,sCAAI,UAAU,EAAE,SAAS,cAAc,QAAQ,MAAM,UAAU,QAAQ,QAAwBA,kCAAG,IAAC,qBAAqB,EAAE,GAAG,cAAc,KAAK,aAAc,CAAA,IAAoBA,kCAAAA,IAAI,wBAAwB,EAAE,GAAG,cAAc,KAAK,aAAY,CAAE,EAAC,CAAE;AAAA,EAClR;AACA;AACAQ,iBAAe,cAAc;AAC7B,IAAI,sBAAsBL,aAAgB;AAAA,EACxC,CAAC,OAAO,iBAAiB;AACvB,UAAM,UAAU,kBAAkB,cAAc,MAAM,cAAc;AACpE,UAAM,aAAaL,aAAY,OAAC,IAAI;AACpC,UAAM,eAAe,gBAAgB,cAAc,UAAU;AAC7D,UAAM,yBAAyBA,aAAY,OAAC,KAAK;AACjDM,iBAAAA,UAAgB,MAAM;AACpB,YAAM,UAAU,WAAW;AAC3B,UAAI,QAAS,QAAO,WAAW,OAAO;AAAA,IACvC,GAAE,EAAE;AACL,WAAuBJ,kCAAG,IAACS,mBAAc,EAAE,IAAI,MAAM,gBAAgB,MAAM,UAA0BT,kCAAG;AAAA,MACtG;AAAA,MACA;AAAA,QACE,GAAG;AAAA,QACH,KAAK;AAAA,QACL,WAAW,QAAQ;AAAA,QACnB,6BAA6B;AAAA,QAC7B,kBAAkB,qBAAqB,MAAM,kBAAkB,CAAC,UAAU;;AACxE,gBAAM,eAAgB;AACtB,cAAI,CAAC,uBAAuB,QAAS,eAAQ,WAAW,YAAnB,mBAA4B;AAAA,QAC3E,CAAS;AAAA,QACD,sBAAsB;AAAA,UACpB,MAAM;AAAA,UACN,CAAC,UAAU;AACT,kBAAM,gBAAgB,MAAM,OAAO;AACnC,kBAAM,gBAAgB,cAAc,WAAW,KAAK,cAAc,YAAY;AAC9E,kBAAM,eAAe,cAAc,WAAW,KAAK;AACnD,mCAAuB,UAAU;AAAA,UAClC;AAAA,UACD,EAAE,0BAA0B,MAAK;AAAA,QAClC;AAAA,QACD,gBAAgB;AAAA,UACd,MAAM;AAAA,UACN,CAAC,UAAU,MAAM,eAAgB;AAAA,UACjC,EAAE,0BAA0B,MAAK;AAAA,QAC3C;AAAA,MACA;AAAA,IACA,GAAO;AAAA,EACP;AACA;AACA,IAAI,yBAAyBG,aAAgB;AAAA,EAC3C,CAAC,OAAO,iBAAiB;AACvB,UAAM,UAAU,kBAAkB,cAAc,MAAM,cAAc;AACpE,UAAM,0BAA0BL,aAAY,OAAC,KAAK;AAClD,UAAM,2BAA2BA,aAAY,OAAC,KAAK;AACnD,WAAuBE,kCAAG;AAAA,MACxB;AAAA,MACA;AAAA,QACE,GAAG;AAAA,QACH,KAAK;AAAA,QACL,WAAW;AAAA,QACX,6BAA6B;AAAA,QAC7B,kBAAkB,CAAC,UAAU;;AAC3B,sBAAM,qBAAN,+BAAyB;AACzB,cAAI,CAAC,MAAM,kBAAkB;AAC3B,gBAAI,CAAC,wBAAwB,QAAS,eAAQ,WAAW,YAAnB,mBAA4B;AAClE,kBAAM,eAAgB;AAAA,UAClC;AACU,kCAAwB,UAAU;AAClC,mCAAyB,UAAU;AAAA,QACpC;AAAA,QACD,mBAAmB,CAAC,UAAU;;AAC5B,sBAAM,sBAAN,+BAA0B;AAC1B,cAAI,CAAC,MAAM,kBAAkB;AAC3B,oCAAwB,UAAU;AAClC,gBAAI,MAAM,OAAO,cAAc,SAAS,eAAe;AACrD,uCAAyB,UAAU;AAAA,YACjD;AAAA,UACA;AACU,gBAAM,SAAS,MAAM;AACrB,gBAAM,mBAAkB,aAAQ,WAAW,YAAnB,mBAA4B,SAAS;AAC7D,cAAI,gBAAiB,OAAM,eAAgB;AAC3C,cAAI,MAAM,OAAO,cAAc,SAAS,aAAa,yBAAyB,SAAS;AACrF,kBAAM,eAAgB;AAAA,UAClC;AAAA,QACA;AAAA,MACA;AAAA,IACK;AAAA,EACL;AACA;AACA,IAAI,qBAAqBG,aAAgB;AAAA,EACvC,CAAC,OAAO,iBAAiB;AACvB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,GAAG;AAAA,IACT,IAAQ;AACJ,UAAM,UAAU,kBAAkB,cAAc,cAAc;AAC9D,UAAM,cAAc,eAAe,cAAc;AACjD,mBAAgB;AAChB,WAAuBH,kCAAG;AAAA,MACxB;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,MAAM;AAAA,QACN,SAAS;AAAA,QACT,kBAAkB;AAAA,QAClB,oBAAoB;AAAA,QACpB,UAA0BA,kCAAG;AAAA,UAC3B;AAAA,UACA;AAAA,YACE,SAAS;AAAA,YACT;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA,WAAW,MAAM,QAAQ,aAAa,KAAK;AAAA,YAC3C,UAA0BA,kCAAG;AAAA,cAC3BU;AAAAA,cACA;AAAA,gBACE,cAAc,SAAS,QAAQ,IAAI;AAAA,gBACnC,MAAM;AAAA,gBACN,IAAI,QAAQ;AAAA,gBACZ,GAAG;AAAA,gBACH,GAAG;AAAA,gBACH,KAAK;AAAA,gBACL,OAAO;AAAA,kBACL,GAAG,aAAa;AAAA;AAAA,kBAEhB,GAAG;AAAA,oBACD,4CAA4C;AAAA,oBAC5C,2CAA2C;AAAA,oBAC3C,4CAA4C;AAAA,oBAC5C,iCAAiC;AAAA,oBACjC,kCAAkC;AAAA,kBACtD;AAAA,gBACA;AAAA,cACA;AAAA,YACA;AAAA,UACA;AAAA,QACA;AAAA,MACA;AAAA,IACK;AAAA,EACL;AACA;AACA,IAAI,aAAa;AACjB,IAAIC,iBAAeR,aAAgB;AAAA,EACjC,CAAC,OAAO,iBAAiB;AACvB,UAAM,EAAE,gBAAgB,GAAG,WAAU,IAAK;AAC1C,UAAM,UAAU,kBAAkB,YAAY,cAAc;AAC5D,WAAuBH,kCAAG;AAAA,MACxB,UAAU;AAAA,MACV;AAAA,QACE,MAAM;AAAA,QACN,GAAG;AAAA,QACH,KAAK;AAAA,QACL,SAAS,qBAAqB,MAAM,SAAS,MAAM,QAAQ,aAAa,KAAK,CAAC;AAAA,MACtF;AAAA,IACK;AAAA,EACL;AACA;AACAW,eAAa,cAAc;AAC3B,IAAI,aAAa;AACjB,IAAI,eAAeR,aAAgB;AAAA,EACjC,CAAC,OAAO,iBAAiB;AACvB,UAAM,EAAE,gBAAgB,GAAG,WAAU,IAAK;AAC1C,UAAM,cAAc,eAAe,cAAc;AACjD,WAAuBH,kCAAG,IAACY,OAAuB,EAAE,GAAG,aAAa,GAAG,YAAY,KAAK,cAAc;AAAA,EAC1G;AACA;AACA,aAAa,cAAc;AAC3B,SAAS,SAAS,MAAM;AACtB,SAAO,OAAO,SAAS;AACzB;AACA,IAAI,QAAQf;AAEZ,IAAI,UAAUS;AACd,IAAI,SAAS;AACb,IAAI,WAAWE;AACf,IAAI,QAAQG;ACrSZ,MAAM,UAAUE;AAEhB,MAAM,iBAAiBC;AAEvB,MAAM,iBAAiBX,aAAM,WAG3B,CAAC,EAAE,WAAW,QAAQ,UAAU,aAAa,GAAG,GAAG,MAAM,GAAG,QAC3DH,kCAAA,IAAAe,QAAA,EACC,UAAAf,kCAAA;AAAA,EAACgB;AAAAA,EAAA;AAAA,IACC;AAAA,IACA;AAAA,IACA;AAAA,IACA,WAAW;AAAA,MACT;AAAA,MACA;AAAA,IACF;AAAA,IACC,GAAG;AAAA,EAAA;AACN,GACF,CACD;AACD,eAAe,cAAcA,SAAyB;AAEhD,MAAA,eAAeb,aAGnB,WAAA,CAAC,EAAE,WAAW,GAAG,MAAM,GAAG,QAC1BH,kCAAA;AAAA,EAACiB;AAAAA,EAAA;AAAA,IACC;AAAA,IACA,WAAW;AAAA,MACT;AAAA,MACA;AAAA,IACF;AAAA,IACC,GAAG;AAAA,EAAA;AACN,CACD;AACD,aAAa,cAAcA,MAAuB;", "x_google_ignoreList": [0]}