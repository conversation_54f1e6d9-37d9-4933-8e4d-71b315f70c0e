{"version": 3, "file": "home.tripManagement--rK2T-ck.js", "sources": ["../../../app/routes/home.tripManagement.tsx"], "sourcesContent": ["\r\n\r\nimport { ActionFunctionArgs } from \"@remix-run/node\";\r\nimport { Form, json, useFetcher, useLoaderData, useSearchParams } from \"@remix-run/react\";\r\nimport { format } from \"date-fns\";\r\nimport { CalendarIcon } from \"lucide-react\";\r\nimport { useEffect, useState } from \"react\";\r\nimport { Button } from \"~/components/ui/button\";\r\nimport { Calendar } from \"~/components/ui/calendar\";\r\nimport { Input } from \"~/components/ui/input\";\r\nimport { Popover, PopoverContent, PopoverTrigger } from \"~/components/ui/popover\";\r\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from \"~/components/ui/select\";\r\nimport { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from \"~/components/ui/table\";\r\nimport { getTripSummary } from \"~/services/tripsSummary\";\r\nimport { TripSummary } from \"~/types/api/businessConsoleService/tripSummaryDetails\";\r\nimport { withAuth, withResponse } from \"@utils/auth-utils\";\r\nimport React from \"react\";\r\nimport { DateRange } from \"react-day-picker\";\r\nimport { cn } from \"lib/utils\";\r\n\r\n\r\n\r\nexport const loader = withAuth(async ({ request }) => {\r\n      try {\r\n            const url = new URL(request.url);\r\n            const fromDate = url.searchParams.get(\"from\") || new Date().toISOString().split(\"T\")[0];\r\n            const toDate = url.searchParams.get(\"to\") || fromDate;\r\n            if (!fromDate || !toDate) {\r\n                  throw json(\r\n                        { error: \"Date is required\" },\r\n                        { status: 400 }\r\n                  );\r\n            }\r\n\r\n            const response = await getTripSummary(fromDate, toDate, request);\r\n            return withResponse({ data: response.data }, response.headers);\r\n      } catch (error) {\r\n            console.error('Trip summary error:', error);\r\n            throw new Error(`Error fetching trip details: ${error}`);\r\n      }\r\n});\r\n\r\nexport default function TripManagement() {\r\n      const data = useLoaderData<{ data: TripSummary[] }>();\r\n      const [date, setDate] = useState<Date | undefined>(new Date());\r\n      const [searchTerm, setSearchTerm] = useState(\"\")\r\n      const fetcher = useFetcher<{ data: TripSummary[] }>()\r\n      const [tripData, setTriData] = useState<TripSummary[] | []>(data.data || [])\r\n\r\n      const handleSubmit = (date: Date | undefined) => {\r\n\r\n            if (!dateRange.from) return; // Ensure 'from' date exists\r\n\r\n            const formattedFrom = format(dateRange.from, \"yyyy-MM-dd\");\r\n            const formattedTo = dateRange.to ? format(dateRange.to, \"yyyy-MM-dd\") : formattedFrom;\r\n            const formData = new FormData();\r\n            formData.append(\"from\", formattedFrom)\r\n            formData.append(\"to\", formattedTo)\r\n\r\n            fetcher.submit(formData, { method: \"GET\" });\r\n      }\r\n      const itemsPerPage = 200\r\n      const [currentPage, setCurrentPage] = useState(1)\r\n\r\n      const totalPages = Math.ceil(tripData.length / itemsPerPage);\r\n\r\n      const startIndex = (currentPage - 1) * itemsPerPage;\r\n\r\n      const currentTrips = tripData.slice(startIndex, startIndex + itemsPerPage);\r\n\r\n      useEffect(() => {\r\n\r\n            if (fetcher.data) {\r\n                  setTriData(fetcher.data.data)\r\n            }\r\n\r\n      }, [fetcher.data?.data])\r\n\r\n      const filterTrips = (trip: TripSummary) => {\r\n            return (\r\n                  (trip.sellerName?.toLowerCase() || \"\").includes(searchTerm.toLowerCase()) ||\r\n                  (trip.tripId?.toString().toLowerCase() || \"\").includes(searchTerm.toLowerCase()) ||\r\n                  (trip.driverName?.toLowerCase() || \"\").includes(searchTerm.toLowerCase())\r\n            );\r\n      }\r\n\r\n\r\n      const handleSetPage = (page: number) => {\r\n            if (page >= 1 && page <= totalPages) {\r\n                  setCurrentPage(page)\r\n            }\r\n      }\r\n\r\n      const handleSort = (trip: TripSummary) => {\r\n\r\n            return trip.tripStatus === \"Dispatched\"\r\n\r\n      }\r\n      const [searchParams] = useSearchParams();\r\n\r\n      const startDate = searchParams.get(\"from\")\r\n      const endDate = searchParams.get(\"to\")\r\n\r\n      const [dateRange, setDateRange] = React.useState<DateRange>({\r\n            from: startDate ? new Date(startDate) : new Date(),\r\n            to: endDate ? new Date(endDate) : new Date(),\r\n      });\r\n\r\n\r\n      const handleDateChange = (range: DateRange | undefined) => {\r\n            if (!range?.from) return; // Ensure 'from' is never undefined\r\n\r\n            setDateRange({\r\n                  from: range.from,\r\n                  to: range.to || undefined, // If 'to' is not selected, keep it undefined\r\n            });\r\n      };\r\n\r\n\r\n      return (\r\n            <div className=\"container mx-auto w-full\" >\r\n                  <div className=\"flex my-7 space-x-10\">\r\n                        <div className=\" flex space-x-2\">\r\n\r\n                              <div className=\"w-full md:w-auto\">\r\n                                    <Popover>\r\n                                          <PopoverTrigger asChild>\r\n                                                <Button\r\n                                                      id=\"date\"\r\n                                                      variant={\"outline\"}\r\n                                                      className={cn(\r\n                                                            \"w-[300px] justify-start text-left font-normal\",\r\n                                                            !dateRange.from && \"text-muted-foreground\"\r\n                                                      )}\r\n                                                >\r\n                                                      <CalendarIcon />\r\n                                                      {dateRange?.from ? (\r\n                                                            dateRange.to ? (\r\n                                                                  <>\r\n                                                                        {format(dateRange.from, \"LLL dd, y\")} - {format(dateRange.to, \"LLL dd, y\")}\r\n                                                                  </>\r\n                                                            ) : (\r\n                                                                  format(dateRange.from, \"LLL dd, y\")\r\n                                                            )\r\n                                                      ) : (\r\n                                                            <span>Pick a date</span>\r\n                                                      )}\r\n                                                </Button>\r\n\r\n                                          </PopoverTrigger>\r\n                                          <PopoverContent className=\"w-auto p-0\" align=\"start\">\r\n                                                <Calendar\r\n                                                      initialFocus\r\n                                                      selected={dateRange}\r\n                                                      mode=\"range\" // Enable range selection\r\n                                                      onSelect={handleDateChange}\r\n                                                />\r\n                                          </PopoverContent>\r\n                                    </Popover>\r\n                              </div>\r\n                              <Button onClick={() => handleSubmit(date)}  >\r\n                                    Get Trips\r\n                              </Button>\r\n                        </div>\r\n\r\n                        <Input placeholder=\"Search By tripId.Seller,DriverName\"\r\n                              value={searchTerm}\r\n                              onChange={(e) => setSearchTerm(e.target.value)}\r\n                              className=\"max-w-sm\"\r\n                        />\r\n                  </div>\r\n                  <Table>\r\n                        <TableHeader className=\"bg-gray-100\">\r\n                              <TableHead>TripId</TableHead>\r\n                              <TableHead>Seller</TableHead>\r\n                              <TableHead>Driver</TableHead>\r\n                              <TableHead>Orders</TableHead>\r\n                              <TableHead>Total Delivered Order</TableHead>\r\n                              <TableHead>D.Date</TableHead>\r\n                              <TableHead>Status</TableHead>\r\n                        </TableHeader>\r\n                        <TableBody>\r\n                              {currentTrips.length > 0 ? currentTrips.sort((a, b) => {\r\n                                    const Priority: { [key: string]: number } = { Dispatched: 1, Open: 2 };\r\n                                    return (Priority[a.tripStatus] || 3) - (Priority[b.tripStatus] || 3);\r\n                              }).filter((trip) => filterTrips(trip)).map((x) => (\r\n                                    <TableRow key={x.tripId}>\r\n                                          <TableCell> {x.tripId}  </TableCell>\r\n                                          <TableCell>{`${x.sellerId}-${x.sellerName}`}</TableCell>\r\n                                          <TableCell>{x.driverName}</TableCell>\r\n                                          <TableCell>{x.totalOrders}</TableCell>\r\n                                          <TableCell>{x.totalDeliveredOrders}</TableCell>\r\n                                          <TableCell>{x?.deliveryDate ? format(x.deliveryDate, \"MM-dd-yyyy\") : \"\"}</TableCell>\r\n                                          <TableCell className={x.tripStatus === \"Dispatched\" ? \"text-red-500\" : x.tripStatus === \"Open\" ? \"text-orange-500\" : \"text-green-600\"}>{x.tripStatus}</TableCell>\r\n                                    </TableRow>\r\n                              )\r\n\r\n                              ) : (\r\n                                    <TableRow>\r\n                                          <TableCell\r\n                                                colSpan={9}\r\n                                                className=\"h-24 text-center\"\r\n                                          >\r\n                                                No results.\r\n                                          </TableCell>\r\n                                    </TableRow>\r\n                              )\r\n                              }\r\n                        </TableBody>\r\n                  </Table>\r\n                  <div className=\"flex items-center space-x-2 my-2\">\r\n                        <Button\r\n                              variant=\"outline\"\r\n                              size=\"sm\"\r\n                              onClick={() => handleSetPage(currentPage - 1)}\r\n                              disabled={currentPage === 1}\r\n                        >\r\n                              Previous\r\n                        </Button>\r\n                        <span className=\"text-gray-700\">\r\n                              Page {currentPage} of {totalPages}\r\n                        </span>\r\n                        <Button\r\n                              variant=\"outline\"\r\n                              size=\"sm\"\r\n                              onClick={() =>\r\n                                    handleSetPage(currentPage + 1)\r\n                              }\r\n                              disabled={currentPage === totalPages}\r\n                        >\r\n                              Next\r\n                        </Button>\r\n                  </div>\r\n            </div>\r\n      )\r\n}\r\n"], "names": ["TripManagement", "data", "useLoaderData", "date", "setDate", "useState", "Date", "searchTerm", "setSearchTerm", "fetcher", "useFetcher", "tripData", "setTriData", "handleSubmit", "date<PERSON><PERSON><PERSON>", "from", "formattedFrom", "format", "formattedTo", "to", "formData", "FormData", "append", "submit", "method", "itemsPerPage", "currentPage", "setCurrentPage", "totalPages", "Math", "ceil", "length", "startIndex", "currentTrips", "slice", "useEffect", "filterTrips", "trip", "sellerName", "toLowerCase", "includes", "tripId", "toString", "<PERSON><PERSON><PERSON>", "handleSetPage", "page", "searchParams", "useSearchParams", "startDate", "get", "endDate", "setDateRange", "React", "handleDateChange", "range", "jsxs", "className", "children", "jsx", "Popover", "PopoverTrigger", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "id", "variant", "cn", "CalendarIcon", "Fragment", "PopoverC<PERSON>nt", "align", "Calendar", "initialFocus", "selected", "mode", "onSelect", "onClick", "Input", "placeholder", "value", "onChange", "e", "target", "Table", "TableHeader", "TableHead", "TableBody", "sort", "a", "b", "Priority", "Dispatched", "Open", "tripStatus", "filter", "map", "x", "TableRow", "TableCell", "sellerId", "totalOrders", "totalDeliveredOrders", "deliveryDate", "colSpan", "size", "disabled"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0CA,SAAwBA,iBAAiB;;AACnC,QAAMC,OAAOC,cAAuC;AACpD,QAAM,CAACC,MAAMC,OAAO,IAAIC,aAAAA,SAA2B,oBAAIC,MAAM;AAC7D,QAAM,CAACC,YAAYC,aAAa,IAAIH,aAAAA,SAAS,EAAE;AAC/C,QAAMI,UAAUC,WAAoC;AAC9C,QAAA,CAACC,UAAUC,UAAU,IAAIP,sBAA6BJ,KAAKA,QAAQ,EAAE;AAErE,QAAAY,eAAgBV,WAA2B;AAEvC,QAAA,CAACW,UAAUC,KAAM;AAErB,UAAMC,gBAAgBC,OAAOH,UAAUC,MAAM,YAAY;AACzD,UAAMG,cAAcJ,UAAUK,KAAKF,OAAOH,UAAUK,IAAI,YAAY,IAAIH;AAClE,UAAAI,WAAW,IAAIC,SAAS;AACrBD,aAAAE,OAAO,QAAQN,aAAa;AAC5BI,aAAAE,OAAO,MAAMJ,WAAW;AAEjCT,YAAQc,OAAOH,UAAU;AAAA,MAAEI,QAAQ;AAAA,IAAM,CAAC;AAAA,EAChD;AACA,QAAMC,eAAe;AACrB,QAAM,CAACC,aAAaC,cAAc,IAAItB,aAAAA,SAAS,CAAC;AAEhD,QAAMuB,aAAaC,KAAKC,KAAKnB,SAASoB,SAASN,YAAY;AAErD,QAAAO,cAAcN,cAAc,KAAKD;AAEvC,QAAMQ,eAAetB,SAASuB,MAAMF,YAAYA,aAAaP,YAAY;AAEzEU,eAAAA,UAAU,MAAM;AAEV,QAAI1B,QAAQR,MAAM;AACDW,iBAAAH,QAAQR,KAAKA,IAAI;AAAA,IAClC;AAAA,EAEH,GAAA,EAACQ,aAAQR,SAARQ,mBAAcR,IAAI,CAAC;AAEjB,QAAAmC,cAAeC,UAAsB;;AACrC,cACOA,MAAAA,KAAKC,eAALD,gBAAAA,IAAiBE,kBAAiB,IAAIC,SAASjC,WAAWgC,YAAa,CAAA,QACvEF,UAAKI,WAALJ,mBAAaK,WAAWH,kBAAiB,IAAIC,SAASjC,WAAWgC,YAAa,CAAA,QAC9EF,UAAKM,eAALN,mBAAiBE,kBAAiB,IAAIC,SAASjC,WAAWgC,aAAa;AAAA,EAEpF;AAGM,QAAAK,gBAAiBC,UAAiB;AAC9B,QAAAA,QAAQ,KAAKA,QAAQjB,YAAY;AAC/BD,qBAAekB,IAAI;AAAA,IACzB;AAAA,EACN;AAOM,QAAA,CAACC,YAAY,IAAIC,gBAAgB;AAEjC,QAAAC,YAAYF,aAAaG,IAAI,MAAM;AACnC,QAAAC,UAAUJ,aAAaG,IAAI,IAAI;AAErC,QAAM,CAACnC,WAAWqC,YAAY,IAAIC,MAAM/C,SAAoB;AAAA,IACtDU,MAAMiC,YAAY,IAAI1C,KAAK0C,SAAS,wBAAQ1C,KAAK;AAAA,IACjDa,IAAI+B,UAAU,IAAI5C,KAAK4C,OAAO,wBAAQ5C,KAAK;AAAA,EACjD,CAAC;AAGK,QAAA+C,mBAAoBC,WAAiC;AACjD,QAAA,EAACA,+BAAOvC,MAAM;AAELoC,iBAAA;AAAA,MACPpC,MAAMuC,MAAMvC;AAAAA,MACZI,IAAImC,MAAMnC,MAAM;AAAA;AAAA,IACtB,CAAC;AAAA,EACP;AAIM,SAAAoC,kCAAAA,KAAC,OAAI;AAAA,IAAAC,WAAU;AAAA,IACTC,UAAA,CAACF,kCAAA,KAAA,OAAA;AAAA,MAAIC,WAAU;AAAA,MACTC,UAAA,CAACF,kCAAA,KAAA,OAAA;AAAA,QAAIC,WAAU;AAAA,QAETC,UAAA,CAAAC,kCAAA,IAAC,OAAI;AAAA,UAAAF,WAAU;AAAA,UACTC,UAAAF,kCAAA,KAACI,SACK;AAAA,YAAAF,UAAA,CAACC,kCAAA,IAAAE,gBAAA;AAAA,cAAeC,SAAO;AAAA,cACjBJ,UAAAF,kCAAA,KAACO,QAAA;AAAA,gBACKC,IAAG;AAAA,gBACHC,SAAS;AAAA,gBACTR,WAAWS,GACL,iDACA,CAACnD,UAAUC,QAAQ,uBACzB;AAAA,gBAEA0C,UAAA,CAAAC,kCAAAA,IAACQ,UAAa,EAAA,IACbpD,uCAAWC,QACND,UAAUK,KAEGoC,kCAAAA,KAAAY,kBAAAA,UAAA;AAAA,kBAAAV,UAAA,CAAOxC,OAAAH,UAAUC,MAAM,WAAW,GAAE,OAAIE,OAAOH,UAAUK,IAAI,WAAW,CAAA;AAAA,gBAC/E,CAAA,IAEAF,OAAOH,UAAUC,MAAM,WAAW,IAGxC2C,kCAAA,IAAC;kBAAKD,UAAW;AAAA,gBAAA,CAAA,CAAA;AAAA,cAE7B,CAAA;AAAA,YAEN,CAAA,GACCC,kCAAA,IAAAU,gBAAA;AAAA,cAAeZ,WAAU;AAAA,cAAaa,OAAM;AAAA,cACvCZ,UAAAC,kCAAA,IAACY,YAAA;AAAA,gBACKC,cAAY;AAAA,gBACZC,UAAU1D;AAAAA,gBACV2D,MAAK;AAAA,gBACLC,UAAUrB;AAAAA,cAChB,CAAA;AAAA,YACN,CAAA,CAAA;AAAA,UACN,CAAA;AAAA,QACN,CAAA,yCACCS,QAAO;AAAA,UAAAa,SAASA,MAAM9D,aAAiB;AAAA,UAAK4C,UAE7C;AAAA,QAAA,CAAA,CAAA;AAAA,MACN,CAAA,GAEAC,kCAAA,IAACkB,OAAA;AAAA,QAAMC,aAAY;AAAA,QACbC,OAAOvE;AAAAA,QACPwE,UAAWC,OAAMxE,cAAcwE,EAAEC,OAAOH,KAAK;AAAA,QAC7CtB,WAAU;AAAA,MAAA,CAChB,CAAA;AAAA,IACN,CAAA,0CACC0B,OACK;AAAA,MAAAzB,UAAA,CAACF,kCAAA,KAAA4B,aAAA;AAAA,QAAY3B,WAAU;AAAA,QACjBC,UAAA,CAAAC,kCAAA,IAAC0B;UAAU3B,UAAM;AAAA,QAAA,CAAA,GACjBC,kCAAA,IAAC0B;UAAU3B,UAAM;AAAA,QAAA,CAAA,GACjBC,kCAAA,IAAC0B;UAAU3B,UAAM;AAAA,QAAA,CAAA,GACjBC,kCAAA,IAAC0B;UAAU3B,UAAM;AAAA,QAAA,CAAA,GACjBC,kCAAA,IAAC0B;UAAU3B,UAAqB;AAAA,QAAA,CAAA,GAChCC,kCAAA,IAAC0B;UAAU3B,UAAM;AAAA,QAAA,CAAA,GACjBC,kCAAA,IAAC0B;UAAU3B,UAAM;AAAA,QAAA,CAAA,CAAA;AAAA,MACvB,CAAA,GACAC,kCAAA,IAAC2B;QACM5B,UAAaxB,aAAAF,SAAS,IAAIE,aAAaqD,KAAK,CAACC,GAAGC,MAAM;AACjD,gBAAMC,WAAsC;AAAA,YAAEC,YAAY;AAAA,YAAGC,MAAM;AAAA,UAAE;AAC7D,kBAAAF,SAASF,EAAEK,UAAU,KAAK,MAAMH,SAASD,EAAEI,UAAU,KAAK;AAAA,SACvE,EAAEC,OAAQxD,UAASD,YAAYC,IAAI,CAAC,EAAEyD,IAAKC,OACtCxC,kCAAA,KAACyC,UACK;AAAA,UAAAvC,UAAA,CAAAF,kCAAA,KAAC0C,WAAU;AAAA,YAAAxC,UAAA,CAAA,KAAEsC,EAAEtD,QAAO,IAAA;AAAA,UAAE,CAAA,GACxBiB,kCAAA,IAACuC;YAAWxC,UAAG,GAAAsC,EAAEG,QAAQ,IAAIH,EAAEzD,UAAU;AAAA,UAAG,CAAA,GAC5CoB,kCAAA,IAACuC,WAAW;AAAA,YAAAxC,UAAAsC,EAAEpD;AAAAA,UAAW,CAAA,GACzBe,kCAAA,IAACuC,WAAW;AAAA,YAAAxC,UAAAsC,EAAEI;AAAAA,UAAY,CAAA,GAC1BzC,kCAAA,IAACuC,WAAW;AAAA,YAAAxC,UAAAsC,EAAEK;AAAAA,UAAqB,CAAA,GACnC1C,kCAAA,IAACuC;YAAWxC,WAAGsC,uBAAAM,gBAAepF,OAAO8E,EAAEM,cAAc,YAAY,IAAI;AAAA,UAAG,CAAA,GACvE3C,kCAAA,IAAAuC,WAAA;AAAA,YAAUzC,WAAWuC,EAAEH,eAAe,eAAe,iBAAiBG,EAAEH,eAAe,SAAS,oBAAoB;AAAA,YAAmBnC,YAAEmC;AAAAA,UAAW,CAAA,CAAA;AAAA,QAAA,GAP5IG,EAAEtD,MAQjB,CAGN,0CACOuD,UACK;AAAA,UAAAvC,UAAAC,kCAAA,IAACuC,WAAA;AAAA,YACKK,SAAS;AAAA,YACT9C,WAAU;AAAA,YACfC,UAAA;AAAA,UAED,CAAA;AAAA,QACN,CAAA;AAAA,MAGZ,CAAA,CAAA;AAAA,IACN,CAAA,GACAF,kCAAA,KAAC,OAAI;AAAA,MAAAC,WAAU;AAAA,MACTC,UAAA,CAAAC,kCAAA,IAACI,QAAA;AAAA,QACKE,SAAQ;AAAA,QACRuC,MAAK;AAAA,QACL5B,SAASA,MAAM/B,cAAclB,cAAc,CAAC;AAAA,QAC5C8E,UAAU9E,gBAAgB;AAAA,QAC/B+B,UAAA;AAAA,MAAA,CAED,GACAF,kCAAA,KAAC,QAAK;AAAA,QAAAC,WAAU;AAAA,QAAgBC,UAAA,CAAA,SACpB/B,aAAY,QAAKE,UAAA;AAAA,MAC7B,CAAA,GACA8B,kCAAA,IAACI,QAAA;AAAA,QACKE,SAAQ;AAAA,QACRuC,MAAK;AAAA,QACL5B,SAASA,MACH/B,cAAclB,cAAc,CAAC;AAAA,QAEnC8E,UAAU9E,gBAAgBE;AAAAA,QAC/B6B,UAAA;AAAA,MAAA,CAED,CAAA;AAAA,IACN,CAAA,CAAA;AAAA,EACN,CAAA;AAEZ;"}