{"version": 3, "file": "storybook-ci_-JJjw.js", "sources": ["../../../app/routes/storybook.tsx"], "sourcesContent": ["import {Button} from \"@components/ui/button\";\r\n\r\nexport default function Storybook() {\r\n    return (\r\n        <div>\r\n            <h1>Storybook</h1>\r\n\r\n            <p>\r\n                <div>\r\n                    <Button/>\r\n                </div>\r\n            </p>\r\n        </div>\r\n    );\r\n}\r\n"], "names": ["Storybook", "children", "jsx", "<PERSON><PERSON>"], "mappings": ";;;;;AAEA,SAAwBA,YAAY;AAChC,gDACK,OACG;AAAA,IAAAC,UAAA,CAAAC,kCAAA,IAAC;MAAGD,UAAS;AAAA,IAAA,CAAA,yCAEZ,KACG;AAAA,MAAAA,UAAAC,kCAAA,IAAC;QACGD,UAACC,kCAAAA,IAAAC,QAAA,CAAM,CAAA;AAAA,MACX,CAAA;AAAA,IACJ,CAAA,CAAA;AAAA,EACJ,CAAA;AAER;"}