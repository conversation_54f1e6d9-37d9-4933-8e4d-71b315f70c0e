{"version": 3, "file": "home.masterItemCategory-CITOTm55.js", "sources": ["../../../app/routes/home.masterItemCategory.tsx"], "sourcesContent": ["import { j<PERSON>, use<PERSON><PERSON><PERSON>, use<PERSON><PERSON><PERSON><PERSON><PERSON>, useN<PERSON>gate, useSearchParams } from \"@remix-run/react\";\r\nimport { useEffect, useState } from \"react\";\r\nimport { CategoryItem } from \"~/components/masterItems/searchableCategories\";\r\nimport AddMasterCategory from \"~/components/ui/addMasterCategory\";\r\nimport { Input } from \"~/components/ui/input\";\r\nimport { Pagination, PaginationContent, PaginationItem, PaginationLink, PaginationNext, PaginationPrevious } from \"~/components/ui/pagination\";\r\nimport { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from \"~/components/ui/table\";\r\nimport { Tabs, TabsList, TabsTrigger } from \"~/components/ui/tabs\";\r\nimport { useDebounce } from \"~/hooks/useDebounce\";\r\nimport { getMasterItemCategory, UpdateMasterItemCategory } from \"~/services/masterItemCategories\";\r\nimport s3Service from \"~/services/s3.service\";\r\nimport { MasterItemCategories } from \"~/types/api/businessConsoleService/MasterItemCategory\";\r\nimport { withAuth, withResponse } from \"~/utils/auth-utils\";\r\n\r\nexport const loader = withAuth(async ({ request }) => {\r\n      const url = new URL(request.url);\r\n      const tab = url.searchParams.get(\"tab\") || \"0\" as unknown as number;\r\n      const page = parseInt(url.searchParams.get(\"page\") || \"0\", 10);\r\n      const searchTerm = url.searchParams.get(\"searchTerm\") || \"\";\r\n      const isSearch = searchTerm !== \"\" ? true : false;\r\n      const levelNumber = tab === \"0\" ? 0 : tab as number\r\n      try {\r\n            const MasterItemCategoryResponse = await getMasterItemCategory(request, page, 20, isSearch, levelNumber, searchTerm);\r\n            return withResponse({\r\n                  data: MasterItemCategoryResponse.data\r\n            })\r\n      }\r\n      catch (error) {\r\n            if (error instanceof Response && error.status === 404) {\r\n                  throw json({ error: \"MasterItemCategory pg Not found\" }, { status: 404 });\r\n            }\r\n            throw new Response(\"Failed to fetch MasterItemCategory \", { status: 500 });\r\n      }\r\n})\r\nexport const action = withAuth(async ({ request }) => {\r\n      const formData = await request.formData();\r\n      const intent = formData.get(\"_intent\");\r\n      const ondcDomain = formData.get(\"ondcDomain\") as \"RET10\" | \"RET11\";\r\n      const categoryName = formData.get(\"categoryName\") as string;\r\n      const categoryLevel = formData.get(\"categoryLevel\") as unknown as number;\r\n      const sequence = formData.get(\"sequence\") as unknown as number;\r\n\r\n      const picture = formData.get(\"imageUrl\") as string;\r\n      const parentId = formData.get(\"parentId\");\r\n      const icId = formData.get(\"icId\") as unknown as number;\r\n      const mode = formData.get(\"mode\") as string;\r\n      const parsedParentCat = parentId ? JSON.parse(parentId as string) : [];\r\n\r\n      if (formData.get(\"_action\") === \"uploadImage\") {\r\n            try {\r\n                  const file = formData.get(\"file\");\r\n                  console.log(\"Received file:\", {\r\n                        type: file?.constructor.name,\r\n                        isBlob: file instanceof Blob,\r\n                        size: file instanceof Blob ? file.size : 'N/A',\r\n                        contentType: file instanceof Blob ? file.type : 'N/A'\r\n                  });\r\n\r\n                  if (!file || !(file instanceof Blob)) {\r\n                        return json({ success: false, error: \"No file provided\" }, { status: 400 });\r\n                  }\r\n\r\n                  // Validate file size\r\n                  const MAX_FILE_SIZE = 500 * 1024 * 1024; // 5MB\r\n                  if (file.size > MAX_FILE_SIZE) {\r\n                        return json({\r\n                              success: false,\r\n                              error: \"File size exceeds 5MB limit\"\r\n                        }, { status: 400 });\r\n                  }\r\n\r\n                  // Read file as buffer\r\n                  const arrayBuffer = await file.arrayBuffer();\r\n                  const buffer = Buffer.from(arrayBuffer);\r\n\r\n                  const fileUrl = await s3Service.uploadFile({\r\n                        file: buffer,\r\n                        fileName: (file as File).name || 'image.jpg',\r\n                        contentType: file.type || 'image/jpeg',\r\n                  });\r\n\r\n                  return json({ success: true, fileUrl });\r\n            } catch (error) {\r\n                  console.error(\"File upload error:\", error);\r\n                  if (error instanceof Error) {\r\n                        return json({\r\n                              success: false,\r\n                              error: error.message || \"Failed to upload file\"\r\n                        }, { status: 500 });\r\n                  }\r\n                  return json({\r\n                        success: false,\r\n                        error: \"An unexpected error occurred while uploading the file\"\r\n                  }, { status: 500 });\r\n            }\r\n      }\r\n      else if (intent === \"_createCategory\") {\r\n            try {\r\n                  const response = await UpdateMasterItemCategory(ondcDomain, categoryName, categoryLevel, categoryLevel === 1 ? sequence : 0, picture, parsedParentCat, request, mode, icId);\r\n                  return withResponse({ data: response.data }, response.headers);\r\n\r\n            } catch (error) {\r\n                  return json({ error: \"Something went wrong\" }, { status: 500 });\r\n            }\r\n      }\r\n\r\n      return json({ error: \"No valid action found\" }, { status: 400 });\r\n});\r\n\r\n\r\n\r\n\r\nexport default function MasterItemCategory() {\r\n      const navigate = useNavigate();\r\n      const [searchParams] = useSearchParams();\r\n      const categories = useLoaderData<{ data: MasterItemCategories[] }>();\r\n      const currentPage = parseInt(searchParams.get(\"page\") || \"0\", 10);\r\n      const activeTab = searchParams.get(\"tab\") || \"0\";\r\n\r\n\r\n      const [searchTerm, setSearchTerm] = useState(\"\");\r\n\r\n      const debouncedSearchTerm = useDebounce(searchTerm, 500)\r\n\r\n      const fetcher = useFetcher()\r\n\r\n      const handleTabChange = (newTab: string) => {\r\n            navigate(`?tab=${newTab}&page=0&searchTerm=${searchTerm}`);\r\n      };\r\n\r\n      const handlePageChange = (newPage: number) => {\r\n            navigate(`?tab=${activeTab}&page=${newPage}&searchTerm=${searchTerm}`);\r\n      };\r\n\r\n      useEffect(() => {\r\n            if (debouncedSearchTerm.length >= 3) {\r\n                  navigate(`?tab=${activeTab}&page=${currentPage}&searchTerm=${debouncedSearchTerm.toLocaleLowerCase()}`);\r\n            }\r\n            else if (debouncedSearchTerm === \"\") {\r\n                  navigate(`?tab=${activeTab}&page=${currentPage}`)\r\n\r\n            }\r\n      }, [debouncedSearchTerm, navigate, activeTab, currentPage])\r\n\r\n\r\n      const handleSubmit = (ondcDomain: \"RET10\" | \"RET11\", selectedLevel: number, categoryName: string, sequence: number, uploadedImageUrl: string, parentId: CategoryItem[], closeDialog: () => void, mode?: string, icId?: number) => {\r\n            const formData = new FormData();\r\n            formData.append(\"ondcDomain\", ondcDomain)\r\n            formData.append(\"categoryName\", categoryName)\r\n            formData.append(\"categoryLevel\", selectedLevel.toString())\r\n            formData.append(\"sequence\", sequence.toString())\r\n\r\n            formData.append(\"imageUrl\", uploadedImageUrl)\r\n            formData.append(\"parentId\", JSON.stringify(parentId));\r\n            formData.append(\"icId\", icId as unknown as string);\r\n            formData.append(\"mode\", mode as string);\r\n            formData.append(\"_intent\", \"_createCategory\")\r\n            fetcher.submit(formData, { method: \"POST\" })\r\n\r\n            closeDialog()\r\n      }\r\n\r\n      return (\r\n            <div className=\"container mx-auto p-6\">\r\n                  <div className=\"flex justify-between items-center mb-4\">\r\n                        <h1 className=\"text-2xl font-bold\">Categories</h1>\r\n                  </div>\r\n                  <div className=\"flex flex-wrap justify-between items-start gap-4 mb-2\">\r\n                        <div className=\"w-full md:w-auto\">\r\n                              < p className=\"flex flex-wrap\">Type</p>\r\n                              <Tabs value={activeTab} onValueChange={handleTabChange} className=\"mb-6\">\r\n                                    <TabsList>\r\n                                          <TabsTrigger value=\"1\">Level1</TabsTrigger>\r\n                                          <TabsTrigger value=\"2\">Level2</TabsTrigger>\r\n                                          <TabsTrigger value=\"3\">Level3</TabsTrigger>\r\n                                          <TabsTrigger value=\"0\">All</TabsTrigger>\r\n                                    </TabsList>\r\n                              </Tabs>\r\n                        </div>\r\n                        <div className=\" flex flex-wrap justify-center gap-4 items-center\">\r\n                              <Input\r\n                                    placeholder=\"Search by Name\"\r\n                                    value={searchTerm}\r\n                                    onChange={(e) => {\r\n                                          setSearchTerm(e.target.value)\r\n\r\n                                    }}\r\n                                    type=\"search\"\r\n                              />\r\n                              {/* <ListFilter size={45} /> */}\r\n                        </div>\r\n                  </div>\r\n                  <Table>\r\n                        <TableHeader>\r\n                              <TableRow className=\"border-b bg-gray-100\">\r\n                                    <TableHead className=\"font-bold\">Category Id</TableHead>\r\n                                    <TableHead className=\"cursor-pointer font-bold\">Category Name</TableHead>\r\n                                    <TableHead className=\"font-bold\">Category Level</TableHead>\r\n                                    <TableHead className=\"font-bold\">Parent Category</TableHead>\r\n                                    <TableHead className=\"font-bold\">Category Details</TableHead>\r\n                                    <TableHead className=\"font-bold\">Actions</TableHead>\r\n                              </TableRow>\r\n                        </TableHeader>\r\n                        <TableBody>\r\n                              {categories?.data.length === 0 ?\r\n                                    <TableRow>\r\n                                          <TableCell className=\"py-4 text-center align-middle\" colSpan={100}>\r\n                                                No Categories Found\r\n                                          </TableCell>\r\n                                    </TableRow>\r\n\r\n                                    :\r\n                                    categories?.data?.map((item) => (\r\n                                          <TableRow key={item.id}>\r\n                                                <TableCell>{item.id}</TableCell>\r\n                                                <TableCell\r\n                                                      className={`cursor-pointer text-blue-500 font-bold  items-center`}\r\n                                                      onClick={() =>\r\n                                                            navigate(`/home/<USER>\n                                                      }\r\n                                                >\r\n                                                      <div className=\"flex gap-2 items-center\">\r\n                                                            <img src={item?.picture} alt=\"\" className=\"w-12 h-12 object-cover\" />    {item.name}\r\n\r\n                                                      </div>\r\n                                                </TableCell>\r\n                                                {/* <TableCell><span className={`px-2 py-1 rounded-full text-xs ${x.disabled ? 'bg-red-100 text-red-800' : 'bg-green-200 text-green-800'}`}>{item?.disabled === true ? \"Disabled\" : \"Active\"}</span></TableCell> */}\r\n                                                <TableCell>\r\n                                                      {item.level === 1 ? \"L1\" : item.level === 2 ? \"L2\" : item.level === 3 ? \"L3\" : \"-\"}\r\n                                                </TableCell>\r\n\r\n                                                <TableCell>\r\n                                                      <div className={`flex flex-col gap-1  `}>{item?.parentCategories?.length > 0\r\n                                                            ? item?.parentCategories.map((parent) =>\r\n\r\n                                                                  <span key={parent.id}><span className={`${parent.disabled ? 'text-red-600 font-bold' : ''} items-center`}>{parent?.name}</span></span>)\r\n                                                            : \"-\"}\r\n                                                      </div>\r\n                                                </TableCell>\r\n                                                <TableCell>\r\n                                                      <div className=\"text-xs\">\r\n                                                            <span className=\"font-medium\">Items:</span> {item.totalItems || '-'}\r\n                                                      </div>\r\n                                                      <div className=\"text-xs\">\r\n                                                            <span className=\"font-medium\">Sequence:</span> {item.sequence || '-'}\r\n                                                      </div>\r\n                                                      {item?.disabled && <div className=\"text-xs\">\r\n                                                            <span className=\" text-red-600 font-bold\">Disabled</span>\r\n                                                      </div>}\r\n                                                      {/* {item?.disabled && <span className={`px-2 py-1 rounded-full text-xs bg-red-100 text-red-800 }`}>Disabled</span>} */}\r\n\r\n\r\n                                                </TableCell>\r\n                                                <TableCell>\r\n                                                      <AddMasterCategory buttonName=\"Add New Category\" categoryDetails={item}\r\n                                                            handleSubmit={handleSubmit} mode=\"Edit\" />\r\n                                                </TableCell>\r\n                                          </TableRow>\r\n                                    ))}\r\n                        </TableBody>\r\n                  </Table>\r\n                  <div className=\"flex justify-between items-center mt-6 overflow-hidden\">\r\n                        <Pagination>\r\n                              <PaginationContent>\r\n                                    {currentPage > 0 && (\r\n                                          <PaginationItem>\r\n                                                <PaginationPrevious onClick={() => handlePageChange(currentPage - 1)} className=\"cursor-pointer\" />\r\n                                          </PaginationItem>\r\n                                    )}\r\n                                    <PaginationItem className=\"cursor-pointer\">\r\n                                          <PaginationLink>{currentPage + 1}</PaginationLink>\r\n                                    </PaginationItem>\r\n                                    <PaginationItem>\r\n                                          <PaginationNext onClick={() => handlePageChange(currentPage + 1)} className=\"cursor-pointer\" />\r\n                                    </PaginationItem>\r\n                              </PaginationContent>\r\n                        </Pagination>\r\n                        <AddMasterCategory buttonName=\"Add New Category\"\r\n                              handleSubmit={handleSubmit} />\r\n                  </div>\r\n            </div>\r\n      );\r\n}"], "names": ["MasterItemCategory", "navigate", "useNavigate", "searchParams", "useSearchParams", "categories", "useLoaderData", "currentPage", "parseInt", "get", "activeTab", "searchTerm", "setSearchTerm", "useState", "debouncedSearchTerm", "useDebounce", "fetcher", "useFetcher", "handleTabChange", "newTab", "handlePageChange", "newPage", "useEffect", "length", "toLocaleLowerCase", "handleSubmit", "ondcDomain", "selectedLevel", "categoryName", "sequence", "uploadedImageUrl", "parentId", "closeDialog", "mode", "icId", "formData", "FormData", "append", "toString", "JSON", "stringify", "submit", "method", "jsxs", "className", "children", "jsx", "Tabs", "value", "onValueChange", "TabsList", "TabsTrigger", "Input", "placeholder", "onChange", "e", "target", "type", "Table", "TableHeader", "TableRow", "TableHead", "TableBody", "data", "TableCell", "colSpan", "map", "item", "id", "onClick", "name", "level", "src", "picture", "alt", "parentCategories", "parent", "disabled", "totalItems", "AddMasterCategory", "buttonName", "categoryDetails", "Pagination", "Pa<PERSON><PERSON><PERSON><PERSON><PERSON>", "PaginationItem", "PaginationPrevious", "PaginationLink", "PaginationNext"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgHA,SAAwBA,qBAAqB;;AACvC,QAAMC,WAAWC,YAAY;AACvB,QAAA,CAACC,YAAY,IAAIC,gBAAgB;AACvC,QAAMC,aAAaC,cAAgD;AACnE,QAAMC,cAAcC,SAASL,aAAaM,IAAI,MAAM,KAAK,KAAK,EAAE;AAChE,QAAMC,YAAYP,aAAaM,IAAI,KAAK,KAAK;AAG7C,QAAM,CAACE,YAAYC,aAAa,IAAIC,aAAAA,SAAS,EAAE;AAEzC,QAAAC,sBAAsBC,YAAYJ,YAAY,GAAG;AAEvD,QAAMK,UAAUC,WAAW;AAErB,QAAAC,kBAAmBC,YAAmB;AACtClB,aAAS,QAAQkB,MAAM,sBAAsBR,UAAU,EAAE;AAAA,EAC/D;AAEM,QAAAS,mBAAoBC,aAAoB;AACxCpB,aAAS,QAAQS,SAAS,SAASW,OAAO,eAAeV,UAAU,EAAE;AAAA,EAC3E;AAEAW,eAAAA,UAAU,MAAM;AACN,QAAAR,oBAAoBS,UAAU,GAAG;AACtBtB,eAAA,QAAQS,SAAS,SAASH,WAAW,eAAeO,oBAAoBU,kBAAmB,CAAA,EAAE;AAAA,IAC5G,WACSV,wBAAwB,IAAI;AAC/Bb,eAAS,QAAQS,SAAS,SAASH,WAAW,EAAE;AAAA,IAEtD;AAAA,KACH,CAACO,qBAAqBb,UAAUS,WAAWH,WAAW,CAAC;AAGpD,QAAAkB,eAAeA,CAACC,YAA+BC,eAAuBC,cAAsBC,UAAkBC,kBAA0BC,UAA0BC,aAAyBC,MAAeC,SAAkB;AACtN,UAAAC,WAAW,IAAIC,SAAS;AACrBD,aAAAE,OAAO,cAAcX,UAAU;AAC/BS,aAAAE,OAAO,gBAAgBT,YAAY;AAC5CO,aAASE,OAAO,iBAAiBV,cAAcW,SAAA,CAAU;AACzDH,aAASE,OAAO,YAAYR,SAASS,SAAA,CAAU;AAEtCH,aAAAE,OAAO,YAAYP,gBAAgB;AAC5CK,aAASE,OAAO,YAAYE,KAAKC,UAAUT,QAAQ,CAAC;AAC3CI,aAAAE,OAAO,QAAQH,IAAyB;AACxCC,aAAAE,OAAO,QAAQJ,IAAc;AAC7BE,aAAAE,OAAO,WAAW,iBAAiB;AAC5CrB,YAAQyB,OAAON,UAAU;AAAA,MAAEO,QAAQ;AAAA,IAAO,CAAC;AAE/BV,gBAAA;AAAA,EAClB;AAGM,SAAAW,kCAAAA,KAAC,OAAI;AAAA,IAAAC,WAAU;AAAA,IACTC,UAAA,CAACC,kCAAA,IAAA,OAAA;AAAA,MAAIF,WAAU;AAAA,MACTC,UAAAC,kCAAA,IAAC;QAAGF,WAAU;AAAA,QAAqBC;MAAU,CAAA;AAAA,IACnD,CAAA,GACAF,kCAAA,KAAC,OAAI;AAAA,MAAAC,WAAU;AAAA,MACTC,UAAA,CAACF,kCAAA,KAAA,OAAA;AAAA,QAAIC,WAAU;AAAA,QACTC,UAAA,CAAEC,kCAAA,IAAA,KAAA;AAAA,UAAEF,WAAU;AAAA,UAAiBC,UAAI;AAAA,QAAA,CAAA,GACnCC,kCAAA,IAACC;UAAKC,OAAOtC;AAAAA,UAAWuC,eAAe/B;AAAAA,UAAiB0B,WAAU;AAAA,UAC5DC,UAAAF,kCAAA,KAACO,UACK;AAAA,YAAAL,UAAA,CAACC,kCAAA,IAAAK,aAAA;AAAA,cAAYH,OAAM;AAAA,cAAIH,UAAM;AAAA,YAAA,CAAA,GAC5BC,kCAAA,IAAAK,aAAA;AAAA,cAAYH,OAAM;AAAA,cAAIH,UAAM;AAAA,YAAA,CAAA,GAC5BC,kCAAA,IAAAK,aAAA;AAAA,cAAYH,OAAM;AAAA,cAAIH,UAAM;AAAA,YAAA,CAAA,GAC5BC,kCAAA,IAAAK,aAAA;AAAA,cAAYH,OAAM;AAAA,cAAIH,UAAG;AAAA,YAAA,CAAA,CAAA;AAAA,UAChC,CAAA;AAAA,QACN,CAAA,CAAA;AAAA,MACN,CAAA,GACAC,kCAAA,IAAC,OAAI;AAAA,QAAAF,WAAU;AAAA,QACTC,UAAAC,kCAAA,IAACM,OAAA;AAAA,UACKC,aAAY;AAAA,UACZL,OAAOrC;AAAAA,UACP2C,UAAWC,OAAM;AACG3C,0BAAA2C,EAAEC,OAAOR,KAAK;AAAA,UAElC;AAAA,UACAS,MAAK;AAAA,QACX,CAAA;AAAA,MAEN,CAAA,CAAA;AAAA,IACN,CAAA,0CACCC,OACK;AAAA,MAAAb,UAAA,CAAAC,kCAAA,IAACa,aACK;AAAA,QAAAd,UAAAF,kCAAA,KAACiB,UAAS;AAAA,UAAAhB,WAAU;AAAA,UACdC,UAAA,CAACC,kCAAA,IAAAe,WAAA;AAAA,YAAUjB,WAAU;AAAA,YAAYC,UAAW;AAAA,UAAA,CAAA,GAC3CC,kCAAA,IAAAe,WAAA;AAAA,YAAUjB,WAAU;AAAA,YAA2BC,UAAa;AAAA,UAAA,CAAA,GAC5DC,kCAAA,IAAAe,WAAA;AAAA,YAAUjB,WAAU;AAAA,YAAYC,UAAc;AAAA,UAAA,CAAA,GAC9CC,kCAAA,IAAAe,WAAA;AAAA,YAAUjB,WAAU;AAAA,YAAYC,UAAe;AAAA,UAAA,CAAA,GAC/CC,kCAAA,IAAAe,WAAA;AAAA,YAAUjB,WAAU;AAAA,YAAYC,UAAgB;AAAA,UAAA,CAAA,GAChDC,kCAAA,IAAAe,WAAA;AAAA,YAAUjB,WAAU;AAAA,YAAYC,UAAO;AAAA,UAAA,CAAA,CAAA;AAAA,QAC9C,CAAA;AAAA,MACN,CAAA,GACAC,kCAAA,IAACgB;QACMjB,WAAYxC,yCAAA0D,KAAKxC,YAAW,IACvBuB,kCAAA,IAACc,UACK;AAAA,UAAAf,UAAAC,kCAAA,IAACkB,WAAU;AAAA,YAAApB,WAAU;AAAA,YAAgCqB,SAAS;AAAA,YAAKpB,UAEnE;AAAA,UAAA,CAAA;AAAA,QAAA,CACN,KAGAxC,8CAAY0D,SAAZ1D,mBAAkB6D,IAAKC,UACjB;;AAAAxB,mDAAA,KAACiB,UACK;AAAA,YAAAf,UAAA,CAACC,kCAAA,IAAAkB,WAAA;AAAA,cAAWnB,eAAKuB;AAAAA,YAAG,CAAA,GACpBtB,kCAAA,IAACkB,WAAA;AAAA,cACKpB,WAAW;AAAA,cACXyB,SAASA,MACHpE,SAAS,uCAAuCkE,KAAKC,EAAE,SAASD,KAAKG,IAAI,UAAUH,KAAKI,KAAK,EAAE;AAAA,cAGrG1B,UAAAF,kCAAA,KAAC,OAAI;AAAA,gBAAAC,WAAU;AAAA,gBACTC,UAAA,CAAAC,kCAAA,IAAC;kBAAI0B,KAAKL,6BAAMM;AAAAA,kBAASC,KAAI;AAAA,kBAAG9B,WAAU;AAAA,gBAAyB,CAAA,GAAE,QAAKuB,KAAKG,IAAA;AAAA,cAErF,CAAA;AAAA,YAAA,CACN,GAECxB,kCAAA,IAAAkB,WAAA;AAAA,cACMnB,UAAKsB,KAAAI,UAAU,IAAI,OAAOJ,KAAKI,UAAU,IAAI,OAAOJ,KAAKI,UAAU,IAAI,OAAO;AAAA,YACrF,CAAA,GAECzB,kCAAA,IAAAkB,WAAA;AAAA,cACKnB,UAACC,kCAAA,IAAA,OAAA;AAAA,gBAAIF,WAAW;AAAA,gBAA0BC,YAAAsB,MAAAA,6BAAMQ,qBAANR,gBAAAA,IAAwB5C,UAAS,IACnE4C,6BAAMQ,iBAAiBT,IAAKU,YAEvB9B,kCAAAA,IAAA,QAAA;AAAA,kBAAqBD,UAACC,kCAAA,IAAA,QAAA;AAAA,oBAAKF,WAAW,GAAGgC,OAAOC,WAAW,2BAA2B,EAAE;AAAA,oBAAkBhC,UAAQ+B,iCAAAN;AAAAA,kBAAK,CAAA;AAAA,mBAA7GM,OAAOR,EAA6G,KACnI;AAAA,cACR,CAAA;AAAA,YACN,CAAA,0CACCJ,WACK;AAAA,cAAAnB,UAAA,CAACF,kCAAA,KAAA,OAAA;AAAA,gBAAIC,WAAU;AAAA,gBACTC,UAAA,CAACC,kCAAA,IAAA,QAAA;AAAA,kBAAKF,WAAU;AAAA,kBAAcC,UAAM;AAAA,gBAAA,CAAA,GAAO,KAAEsB,KAAKW,cAAc,GAAA;AAAA,cACtE,CAAA,GACAnC,kCAAA,KAAC,OAAI;AAAA,gBAAAC,WAAU;AAAA,gBACTC,UAAA,CAACC,kCAAA,IAAA,QAAA;AAAA,kBAAKF,WAAU;AAAA,kBAAcC,UAAS;AAAA,gBAAA,CAAA,GAAO,KAAEsB,KAAKtC,YAAY,GAAA;AAAA,cACvE,CAAA,IACCsC,6BAAMU,aAAY/B,kCAAAA,IAAC,OAAI;AAAA,gBAAAF,WAAU;AAAA,gBAC5BC,UAAAC,kCAAA,IAAC,QAAK;AAAA,kBAAAF,WAAU;AAAA,kBAA0BC,UAAA;AAAA,gBAAQ,CAAA;AAAA,cACxD,CAAA,CAAA;AAAA,YAIN,CAAA,yCACCmB,WACK;AAAA,cAAAnB,UAAAC,kCAAA,IAACiC,mBAAA;AAAA,gBAAkBC,YAAW;AAAA,gBAAmBC,iBAAiBd;AAAAA,gBAC5D1C;AAAAA,gBAA4BQ,MAAK;AAAA,cAAO,CAAA;AAAA,YACpD,CAAA,CAAA;AAAA,UA3CS,GAAAkC,KAAKC,EA4CpB;AAAA;AAAA,MAElB,CAAA,CAAA;AAAA,IACN,CAAA,GACAzB,kCAAA,KAAC,OAAI;AAAA,MAAAC,WAAU;AAAA,MACTC,UAAA,CAACC,kCAAA,IAAAoC,YAAA;AAAA,QACKrC,iDAACsC,mBACM;AAAA,UAAAtC,UAAA,CAAAtC,cAAc,KACTuC,kCAAAA,IAACsC,gBACK;AAAA,YAAAvC,UAAAC,kCAAA,IAACuC,oBAAmB;AAAA,cAAAhB,SAASA,MAAMjD,iBAAiBb,cAAc,CAAC;AAAA,cAAGqC,WAAU;AAAA,YAAiB,CAAA;AAAA,UACvG,CAAA,GAENE,kCAAA,IAACsC;YAAexC,WAAU;AAAA,YACpBC,gDAACyC,gBAAgB;AAAA,cAAAzC,UAAAtC,cAAc;AAAA,YAAE,CAAA;AAAA,UACvC,CAAA,GACCuC,kCAAA,IAAAsC,gBAAA;AAAA,YACKvC,UAACC,kCAAA,IAAAyC,gBAAA;AAAA,cAAelB,SAASA,MAAMjD,iBAAiBb,cAAc,CAAC;AAAA,cAAGqC,WAAU;AAAA,YAAiB,CAAA;AAAA,UACnG,CAAA,CAAA;AAAA,QACN,CAAA;AAAA,MACN,CAAA,GACAE,kCAAA,IAACiC,mBAAA;AAAA,QAAkBC,YAAW;AAAA,QACxBvD;AAAAA,MAAA,CAA4B,CAAA;AAAA,IACxC,CAAA,CAAA;AAAA,EACN,CAAA;AAEZ;"}