{"version": 3, "file": "home.businessConfig-BtvBvfS4.js", "sources": ["../../../app/routes/home.businessConfig.tsx"], "sourcesContent": ["import { useState } from \"react\"\r\nimport { Checkbox } from \"~/components/ui/checkbox\"\r\nimport { Input } from \"~/components/ui/input\"\r\nimport { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from \"~/components/ui/table\"\r\n\r\n\r\nexport default function BusinessConfig() {\r\n      const [searchTerm, setSearchTerm] = useState('')\r\n\r\n      return (\r\n            <div className=\"container mx-auto p-6\">\r\n                  <div className=\"flex justify-between items-center mb-6\">\r\n                        <h1 className=\"text-2xl font-bold\">Business Config</h1>\r\n                  </div>\r\n\r\n                  <div className=\"flex justify-between mb-4\">\r\n                        <Input\r\n                              placeholder=\"Search by name or owner\"\r\n                              value={searchTerm}\r\n                              onChange={(e) => setSearchTerm(e.target.value)}\r\n                              className=\"max-w-sm\"\r\n                        />\r\n                  </div>\r\n                  <div className=\"flex space-x-8\">\r\n                        {[\"Seller\", \"Business\", \"Supplier\", \"Agent\", \"ALL\"].map((status) => (\r\n                              <div key={status} className=\"flex items-center space-x-2 my-2\">\r\n                                    <Checkbox\r\n                                          id={status}\r\n                                          checked={true}\r\n                                          onCheckedChange={() => { }}\r\n                                    />\r\n                                    <label\r\n                                          htmlFor={status}\r\n                                          className=\"text-sm font-medium leading-none\"\r\n                                    >\r\n                                          {status}\r\n                                    </label>\r\n                              </div>\r\n                        ))}\r\n                  </div>\r\n                  <div className=\"rounded-md border\">\r\n                        <Table>\r\n                              <TableHeader>\r\n                                    <TableRow>\r\n                                          <TableHead>ID</TableHead>\r\n                                          <TableHead>Name</TableHead>\r\n                                          <TableHead> Owner/ Mobile</TableHead>\r\n                                          <TableHead>Enable</TableHead>\r\n                                    </TableRow>\r\n                              </TableHeader>\r\n                              <TableBody>\r\n\r\n                              </TableBody>\r\n                              <TableRow>\r\n                                    <TableCell\r\n                                          colSpan={9}\r\n                                          className=\"h-24 text-center\"\r\n                                    >\r\n                                          No results.\r\n                                    </TableCell>\r\n                              </TableRow>\r\n                        </Table>\r\n                  </div>\r\n            </div>\r\n      )\r\n}"], "names": ["BusinessConfig", "searchTerm", "setSearchTerm", "useState", "jsxs", "className", "children", "jsx", "Input", "placeholder", "value", "onChange", "e", "target", "map", "status", "Checkbox", "id", "checked", "onCheckedChange", "htmlFor", "Table", "TableHeader", "TableRow", "TableHead", "TableBody", "TableCell", "colSpan"], "mappings": ";;;;;;;;;;;;;;;AAMA,SAAwBA,iBAAiB;AACnC,QAAM,CAACC,YAAYC,aAAa,IAAIC,aAAAA,SAAS,EAAE;AAGzC,SAAAC,kCAAAA,KAAC,OAAI;AAAA,IAAAC,WAAU;AAAA,IACTC,UAAA,CAACC,kCAAA,IAAA,OAAA;AAAA,MAAIF,WAAU;AAAA,MACTC,UAAAC,kCAAA,IAAC;QAAGF,WAAU;AAAA,QAAqBC;MAAe,CAAA;AAAA,IACxD,CAAA,GAEAC,kCAAA,IAAC,OAAI;AAAA,MAAAF,WAAU;AAAA,MACTC,UAAAC,kCAAA,IAACC,OAAA;AAAA,QACKC,aAAY;AAAA,QACZC,OAAOT;AAAAA,QACPU,UAAWC,OAAMV,cAAcU,EAAEC,OAAOH,KAAK;AAAA,QAC7CL,WAAU;AAAA,MAChB,CAAA;AAAA,IACN,CAAA,yCACC,OAAI;AAAA,MAAAA,WAAU;AAAA,MACRC,UAAA,CAAC,UAAU,YAAY,YAAY,SAAS,KAAK,EAAEQ,IAAKC,YAClDX,kCAAAA,KAAA,OAAA;AAAA,QAAiBC,WAAU;AAAA,QACtBC,UAAA,CAAAC,kCAAA,IAACS,UAAA;AAAA,UACKC,IAAIF;AAAAA,UACJG,SAAS;AAAA,UACTC,iBAAiBA,MAAM;AAAA,UAAA;AAAA,QAAE,CAC/B,GACAZ,kCAAA,IAAC,SAAA;AAAA,UACKa,SAASL;AAAAA,UACTV,WAAU;AAAA,UAETC,UAAAS;AAAAA,QAAA,CACP,CAAA;AAAA,MAXI,GAAAA,MAYV,CACL;AAAA,IACP,CAAA,GACCR,kCAAA,IAAA,OAAA;AAAA,MAAIF,WAAU;AAAA,MACTC,iDAACe,OACK;AAAA,QAAAf,UAAA,CAACC,kCAAA,IAAAe,aAAA;AAAA,UACKhB,iDAACiB,UACK;AAAA,YAAAjB,UAAA,CAAAC,kCAAA,IAACiB;cAAUlB,UAAE;AAAA,YAAA,CAAA,GACbC,kCAAA,IAACiB;cAAUlB,UAAI;AAAA,YAAA,CAAA,GACfC,kCAAA,IAACiB;cAAUlB,UAAc;AAAA,YAAA,CAAA,GACzBC,kCAAA,IAACiB;cAAUlB,UAAM;AAAA,YAAA,CAAA,CAAA;AAAA,UACvB,CAAA;AAAA,QACN,CAAA,yCACCmB,WAED,CAAA,CAAA,yCACCF,UACK;AAAA,UAAAjB,UAAAC,kCAAA,IAACmB,WAAA;AAAA,YACKC,SAAS;AAAA,YACTtB,WAAU;AAAA,YACfC,UAAA;AAAA,UAED,CAAA;AAAA,QACN,CAAA,CAAA;AAAA,MACN,CAAA;AAAA,IACN,CAAA,CAAA;AAAA,EACN,CAAA;AAEZ;"}