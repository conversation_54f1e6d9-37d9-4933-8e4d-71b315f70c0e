{"version": 3, "file": "index-IXOTxK3N.js", "sources": ["../../../node_modules/@radix-ui/number/dist/index.mjs"], "sourcesContent": ["// packages/core/number/src/number.ts\nfunction clamp(value, [min, max]) {\n  return Math.min(max, Math.max(min, value));\n}\nexport {\n  clamp\n};\n//# sourceMappingURL=index.mjs.map\n"], "names": [], "mappings": "AACA,SAAS,MAAM,OAAO,CAAC,KAAK,GAAG,GAAG;AAChC,SAAO,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK,KAAK,CAAC;AAC3C;", "x_google_ignoreList": [0]}