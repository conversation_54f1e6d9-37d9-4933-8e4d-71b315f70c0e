import { r as reactExports, a as React } from "./jsx-runtime-bB2y7OuD.js";
import { u as useLayoutEffect2 } from "./index-D7VH9Fc8.js";
var useReactId = React["useId".toString()] || (() => void 0);
var count = 0;
function useId(deterministicId) {
  const [id, setId] = reactExports.useState(useReactId());
  useLayoutEffect2(() => {
    setId((reactId) => reactId ?? String(count++));
  }, [deterministicId]);
  return deterministicId || (id ? `radix-${id}` : "");
}
export {
  useId as u
};
//# sourceMappingURL=index-DVTNuYOr.js.map
