import { c as createLucideIcon } from "./createLucideIcon-uwkRm45G.js";
/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */
const ArrowLeft = createLucideIcon("ArrowLeft", [
  ["path", { d: "m12 19-7-7 7-7", key: "1l729n" }],
  ["path", { d: "M19 12H5", key: "x3x0zl" }]
]);
export {
  ArrowLeft as A
};
//# sourceMappingURL=arrow-left-D_Ztdrp5.js.map
