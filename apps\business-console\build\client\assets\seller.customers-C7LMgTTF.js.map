{"version": 3, "file": "seller.customers-C7LMgTTF.js", "sources": ["../../../node_modules/lucide-react/dist/esm/icons/mail.js", "../../../app/routes/seller.customers.tsx"], "sourcesContent": ["/**\n * @license lucide-react v0.453.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst Mail = createLucideIcon(\"Mail\", [\n  [\"rect\", { width: \"20\", height: \"16\", x: \"2\", y: \"4\", rx: \"2\", key: \"18n3k1\" }],\n  [\"path\", { d: \"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7\", key: \"1ocrg3\" }]\n]);\n\nexport { Mail as default };\n//# sourceMappingURL=mail.js.map\n", "import { useState } from \"react\";\r\nimport { Input } from \"@components/ui/input\";\r\nimport { But<PERSON> } from \"@components/ui/button\";\r\nimport {\r\n  Card,\r\n  CardContent,\r\n  CardDescription,\r\n  CardHeader,\r\n  CardTitle,\r\n} from \"@components/ui/card\";\r\nimport { ScrollArea } from \"@components/ui/scroll-area\";\r\nimport { Phone, Mail, MapPin } from \"lucide-react\";\r\n\r\n// Mock data - replace with actual API call\r\nconst mockCustomers = [\r\n  {\r\n    id: 1,\r\n    name: \"<PERSON>\",\r\n    phone: \"+91 **********\",\r\n    email: \"<EMAIL>\",\r\n    address: \"123 Main St, City\",\r\n  },\r\n  {\r\n    id: 2,\r\n    name: \"<PERSON>\",\r\n    phone: \"+91 9876543211\",\r\n    email: \"<EMAIL>\",\r\n    address: \"456 Oak St, City\",\r\n  },\r\n  // Add more mock data as needed\r\n];\r\n\r\nexport default function Customers() {\r\n  const [searchTerm, setSearchTerm] = useState(\"\");\r\n\r\n  const filteredCustomers = mockCustomers.filter((customer) =>\r\n    customer.name.toLowerCase().includes(searchTerm.toLowerCase())\r\n  );\r\n\r\n  return (\r\n    <div className=\"container mx-auto p-4 space-y-4\">\r\n      <div className=\"flex flex-col space-y-4 md:flex-row md:justify-between md:items-center\">\r\n        <h1 className=\"text-2xl font-bold\">My Customers</h1>\r\n        <div className=\"w-full md:w-1/3\">\r\n          <Input\r\n            type=\"search\"\r\n            placeholder=\"Search customers...\"\r\n            value={searchTerm}\r\n            onChange={(e) => setSearchTerm(e.target.value)}\r\n            className=\"w-full\"\r\n          />\r\n        </div>\r\n      </div>\r\n\r\n      <ScrollArea className=\"h-[calc(100vh-200px)]\">\r\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\r\n          {filteredCustomers.map((customer) => (\r\n            <Card key={customer.id} className=\"hover:shadow-lg transition-shadow\">\r\n              <CardHeader>\r\n                <CardTitle>{customer.name}</CardTitle>\r\n                <CardDescription>Customer ID: #{customer.id}</CardDescription>\r\n              </CardHeader>\r\n              <CardContent>\r\n                <div className=\"space-y-2\">\r\n                  <div className=\"flex items-center space-x-2\">\r\n                    <Phone className=\"h-4 w-4 text-gray-500\" />\r\n                    <span>{customer.phone}</span>\r\n                  </div>\r\n                  <div className=\"flex items-center space-x-2\">\r\n                    <Mail className=\"h-4 w-4 text-gray-500\" />\r\n                    <span>{customer.email}</span>\r\n                  </div>\r\n                  <div className=\"flex items-center space-x-2\">\r\n                    <MapPin className=\"h-4 w-4 text-gray-500\" />\r\n                    <span>{customer.address}</span>\r\n                  </div>\r\n                  <Button className=\"w-full mt-4\">View Details</Button>\r\n                </div>\r\n              </CardContent>\r\n            </Card>\r\n          ))}\r\n        </div>\r\n      </ScrollArea>\r\n    </div>\r\n  );\r\n} "], "names": ["mockCustomers", "id", "name", "phone", "email", "address", "Customers", "searchTerm", "setSearchTerm", "useState", "filteredCustomers", "filter", "customer", "toLowerCase", "includes", "jsxs", "className", "children", "jsx", "Input", "type", "placeholder", "value", "onChange", "e", "target", "ScrollArea", "map", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardTitle", "CardDescription", "<PERSON><PERSON><PERSON><PERSON>", "Phone", "Mail", "MapPin", "<PERSON><PERSON>"], "mappings": ";;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA,MAAM,OAAO,iBAAiB,QAAQ;AAAA,EACpC,CAAC,QAAQ,EAAE,OAAO,MAAM,QAAQ,MAAM,GAAG,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,SAAQ,CAAE;AAAA,EAC9E,CAAC,QAAQ,EAAE,GAAG,6CAA6C,KAAK,SAAU,CAAA;AAC5E,CAAC;ACED,MAAMA,gBAAgB;AAAA,EACpB;AAAA,IACEC,IAAI;AAAA,IACJC,MAAM;AAAA,IACNC,OAAO;AAAA,IACPC,OAAO;AAAA,IACPC,SAAS;AAAA,EACX;AAAA,EACA;AAAA,IACEJ,IAAI;AAAA,IACJC,MAAM;AAAA,IACNC,OAAO;AAAA,IACPC,OAAO;AAAA,IACPC,SAAS;AAAA,EACX;AAAA;AAAA;AAIF,SAAwBC,YAAY;AAClC,QAAM,CAACC,YAAYC,aAAa,IAAIC,aAAAA,SAAS,EAAE;AAE/C,QAAMC,oBAAoBV,cAAcW,OAAQC,cAC9CA,SAASV,KAAKW,YAAc,EAAAC,SAASP,WAAWM,YAAa,CAAA,CAC/D;AAGE,SAAAE,kCAAAA,KAAC,OAAI;AAAA,IAAAC,WAAU;AAAA,IACbC,UAAA,CAACF,kCAAA,KAAA,OAAA;AAAA,MAAIC,WAAU;AAAA,MACbC,UAAA,CAACC,kCAAA,IAAA,MAAA;AAAA,QAAGF,WAAU;AAAA,QAAqBC,UAAY;AAAA,MAAA,CAAA,GAC/CC,kCAAA,IAAC,OAAI;AAAA,QAAAF,WAAU;AAAA,QACbC,UAAAC,kCAAA,IAACC,OAAA;AAAA,UACCC,MAAK;AAAA,UACLC,aAAY;AAAA,UACZC,OAAOf;AAAAA,UACPgB,UAAWC,OAAMhB,cAAcgB,EAAEC,OAAOH,KAAK;AAAA,UAC7CN,WAAU;AAAA,QACZ,CAAA;AAAA,MACF,CAAA,CAAA;AAAA,IACF,CAAA,GAECE,kCAAA,IAAAQ,YAAA;AAAA,MAAWV,WAAU;AAAA,MACpBC,gDAAC,OAAI;AAAA,QAAAD,WAAU;AAAA,QACZC,UAAAP,kBAAkBiB,IAAKf,cACrBG,kCAAAA,KAAAa,MAAA;AAAA,UAAuBZ,WAAU;AAAA,UAChCC,UAAA,CAAAF,kCAAA,KAACc,YACC;AAAA,YAAAZ,UAAA,CAACC,kCAAA,IAAAY,WAAA;AAAA,cAAWb,mBAASf;AAAAA,YAAK,CAAA,0CACzB6B,iBAAgB;AAAA,cAAAd,UAAA,CAAA,kBAAeL,SAASX,EAAA;AAAA,YAAG,CAAA,CAAA;AAAA,UAC9C,CAAA,GACCiB,kCAAA,IAAAc,aAAA;AAAA,YACCf,UAACF,kCAAA,KAAA,OAAA;AAAA,cAAIC,WAAU;AAAA,cACbC,UAAA,CAACF,kCAAA,KAAA,OAAA;AAAA,gBAAIC,WAAU;AAAA,gBACbC,UAAA,CAACC,kCAAA,IAAAe,OAAA;AAAA,kBAAMjB,WAAU;AAAA,gBAAwB,CAAA,GACzCE,kCAAA,IAAC,QAAM;AAAA,kBAAAD,UAAAL,SAAST;AAAAA,gBAAM,CAAA,CAAA;AAAA,cACxB,CAAA,GACAY,kCAAA,KAAC,OAAI;AAAA,gBAAAC,WAAU;AAAA,gBACbC,UAAA,CAACC,kCAAA,IAAAgB,MAAA;AAAA,kBAAKlB,WAAU;AAAA,gBAAwB,CAAA,GACxCE,kCAAA,IAAC,QAAM;AAAA,kBAAAD,UAAAL,SAASR;AAAAA,gBAAM,CAAA,CAAA;AAAA,cACxB,CAAA,GACAW,kCAAA,KAAC,OAAI;AAAA,gBAAAC,WAAU;AAAA,gBACbC,UAAA,CAACC,kCAAA,IAAAiB,QAAA;AAAA,kBAAOnB,WAAU;AAAA,gBAAwB,CAAA,GAC1CE,kCAAA,IAAC,QAAM;AAAA,kBAAAD,UAAAL,SAASP;AAAAA,gBAAQ,CAAA,CAAA;AAAA,cAC1B,CAAA,GACCa,kCAAA,IAAAkB,QAAA;AAAA,gBAAOpB,WAAU;AAAA,gBAAcC,UAAY;AAAA,cAAA,CAAA,CAAA;AAAA,YAC9C,CAAA;AAAA,UACF,CAAA,CAAA;AAAA,QAAA,GArBSL,SAASX,EAsBpB,CACD;AAAA,MACH,CAAA;AAAA,IACF,CAAA,CAAA;AAAA,EACF,CAAA;AAEJ;", "x_google_ignoreList": [0]}