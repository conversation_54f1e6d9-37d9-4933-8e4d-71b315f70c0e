{"version": 3, "file": "home.selectedSellerDetail-B0_U6D13.js", "sources": ["../../../app/components/ui/sellerAreaList.tsx", "../../../app/routes/home.selectedSellerDetail.tsx"], "sourcesContent": ["import { Seller<PERSON>reas } from \"~/types/api/businessConsoleService/MasterItemCategory\";\r\nimport { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from \"./table\";\r\nimport { Switch } from \"./switch\";\r\nimport { useState } from \"react\";\r\n\r\ninterface sellerAreasProps {\r\n      sellerAreaData: SellerAreas[],\r\n      updateToggle: (status: boolean, areaId: number) => void\r\n}\r\n\r\nexport default function SellerAreaList({ sellerAreaData, updateToggle }: sellerAreasProps) {\r\n      const [toggledAreas, setToggledAreas] = useState<Set<number>>(new Set());\r\n\r\n      const handleSwitch = (status: boolean, areaId: number) => {\r\n            const updatedToggles = new Set(toggledAreas);\r\n            if (status) {\r\n                  updatedToggles.add(areaId);\r\n            } else {\r\n                  updatedToggles.delete(areaId);\r\n            }\r\n            setToggledAreas(updatedToggles);\r\n            updateToggle(status, areaId);\r\n      }\r\n\r\n      return (\r\n            <Table className=\"w-full min-w-[600px] border-collapse\">\r\n                  <TableHeader >\r\n                        <TableRow className=\"bg-gray-100\">\r\n                              <TableHead className=\"font-bold text-gray-800\">Area Name</TableHead>\r\n                              <TableHead className=\"font-bold  text-gray-800\">District</TableHead>\r\n                              <TableHead className=\"font-bold  text-gray-800\">State</TableHead>\r\n                              <TableHead className=\"font-bold  text-gray-800\">Status</TableHead>\r\n                        </TableRow>\r\n                  </TableHeader>\r\n                  <TableBody>\r\n                        {Array.isArray(sellerAreaData) && sellerAreaData.map((x) => {\r\n                              const isToggled = toggledAreas.has(x?.sellerAreaId);\r\n                              return (\r\n                                    <TableRow key={x?.area?.id} className=\"bg-gray-50 font-medium\">\r\n                                          <TableCell >{x?.area?.name}</TableCell>\r\n                                          <TableCell>{x?.area?.district}</TableCell>\r\n                                          <TableCell>{x?.area?.state}</TableCell>\r\n                                          <TableCell>\r\n                                                <Switch\r\n                                                      checked={isToggled || x?.disabled}\r\n                                                      onClick={() => handleSwitch(!isToggled, x?.sellerAreaId)}\r\n                                                />\r\n                                          </TableCell>\r\n                                    </TableRow>\r\n                              )\r\n                        })}\r\n                  </TableBody>\r\n            </Table>\r\n      )\r\n}\r\n", "import { Tab } from \"@headlessui/react\";\r\nimport { json, LoaderFunction } from \"@remix-run/node\";\r\nimport { Form, useFetcher, useLoaderData, useNavigate } from \"@remix-run/react\";\r\nimport { error } from \"console\";\r\nimport { ArrowLeft } from \"lucide-react\";\r\nimport { useEffect, useState } from \"react\";\r\nimport SpinnerLoader from \"~/components/loader/SpinnerLoader\";\r\nimport { Button } from \"~/components/ui/button\";\r\nimport { Input } from \"~/components/ui/input\";\r\nimport SellerAreaList from \"~/components/ui/sellerAreaList\";\r\nimport { Switch } from \"~/components/ui/switch\";\r\nimport { Tabs, TabsContent, TabsList, TabsTrigger } from \"~/components/ui/tabs\";\r\nimport { useToast } from \"~/components/ui/ToastProvider\";\r\nimport { getSelectedSeller, getSelectedSellerAreas, getSellerItemCategories, updateArea, updateAttributes, } from \"~/services/masterItemCategories\";\r\nimport { MasterItemCategories, SelectedSeller, SellerAreas } from \"~/types/api/businessConsoleService/MasterItemCategory\";\r\nimport { withAuth, withResponse } from \"~/utils/auth-utils\";\r\ninterface LoaderData {\r\n      data: SelectedSeller,\r\n      sellerId: number,\r\n      name: string\r\n      isResponse?: false,\r\n\r\n\r\n}\r\n\r\ntype ActionData = {\r\n      intent: \"netWorkItemDetails\" | \"sellerAreas\" | \"updateAttributes\";\r\n      data?: any; // Replace with specific types for `data` if possible\r\n      selectedId?: number;\r\n      name?: string;\r\n      isResponse?: boolean;\r\n};\r\nexport const loader = withAuth(async ({ user, request }) => {\r\n      const url = new URL(request.url);\r\n      const sellerId = Number(url.searchParams.get(\"sellerId\"));\r\n      const name = (url.searchParams.get(\"name\"));\r\n      try {\r\n            const SelectedNetworkDetails = await getSelectedSeller(sellerId, request);\r\n            return withResponse({\r\n                  data: SelectedNetworkDetails.data,\r\n                  sellerId: sellerId,\r\n                  name: name\r\n            }, SelectedNetworkDetails.headers)\r\n      }\r\n      catch (error) {\r\n            if (error instanceof Response && error.status === 404) {\r\n                  throw json({ error: \"MasterItemCategory pg Not found\" }, { status: 404 });\r\n            }\r\n            throw new Response(\"Failed to fetch MasterItemCategory \", { status: 500 });\r\n      }\r\n})\r\nexport const action = withAuth(async ({ request }) => {\r\n      const formData = await request.formData();\r\n      const intent = formData.get(\"intent\")\r\n      const sellerId = formData.get(\"sellerId\") as unknown as number;\r\n      const attribute = formData.get(\"attribute\") as string;\r\n      const updateValue = formData.get(\"value\");\r\n      const areaStatus = formData.get(\"areaStatus\") as unknown as boolean\r\n      const areaId = formData.get(\"areaId\") as unknown as number\r\n      const type = formData.get(\"updateType\") as string;\r\n      if (intent === \"netWorkItemDetails\") {\r\n            const response = await getSellerItemCategories(sellerId, request);\r\n            return withResponse(\r\n                  {\r\n                        intent: intent,\r\n                        data: response.data,\r\n                        selectedId: sellerId,\r\n                        name: updateValue,\r\n                        isResponse: false\r\n                  },\r\n                  response.headers\r\n            );\r\n      }\r\n      if (intent === \"sellerAreas\") {\r\n            const response = await getSelectedSellerAreas(sellerId, request);\r\n            return withResponse(\r\n                  {\r\n                        intent: intent,\r\n                        data: response.data,\r\n                        selectedId: sellerId,\r\n                        name: updateValue,\r\n                        isResponse: false\r\n                  },\r\n                  response.headers\r\n            );\r\n      }\r\n      if (intent === \"updateArea\") {\r\n            const response = await updateArea(sellerId, areaId, areaStatus, request);\r\n            return withResponse(\r\n                  {\r\n                        intent: intent,\r\n                        data: response.data,\r\n                        selectedId: sellerId,\r\n                        name: updateValue,\r\n                        isResponse: false\r\n                  },\r\n                  response.headers\r\n            );\r\n      }\r\n      const supportedAttributes = [\"autoAccept\", \"autoPack\", \"autoPickup\", \"autoDispatch\", \"approxPricing\", \"strikeoffEnabled\", \"itemPickEnabled\",\r\n            \"contractPriceEnabled\", \"isPayLaterEnabled\", \"wa_enable\", \"autoActivate\", \"allowCoD\", \"favItemsEnabled\", \"name\", \"minimumRequiredBalance\", \"deliveryTime\",\r\n            \"minimumOrderValue\", \"categoryLevel\", \"bookingCloseTime\", \"bookingOpenTime\", \"dispatchTime\", \"deliveryTime\", \"minimumOrderQty\"\r\n\r\n      ];\r\n      console.log(attribute, \"999999999999999999\")\r\n      if (supportedAttributes.includes(attribute)) {\r\n            const updatedResponse = await updateAttributes(type, sellerId, attribute, updateValue, request);\r\n\r\n\r\n            return withResponse(\r\n                  {\r\n                        intent: intent,\r\n                        data: updatedResponse.data,\r\n                        selectedId: sellerId,\r\n                        name: updateValue,\r\n                        isResponse: false\r\n                  },\r\n                  updatedResponse.headers\r\n            );\r\n\r\n\r\n      }\r\n})\r\nexport default function SelectedSellerDetail() {\r\n      const { showToast } = useToast(); // Get showToast function from context\r\n\r\n      const navigate = useNavigate()\r\n      const { data, sellerId, name, isResponse } = useLoaderData<LoaderData>()\r\n      const [selectedNetworkItemCategory, setSelectedNetworkItemCategory] = useState<MasterItemCategories[]>([]);\r\n      const [sellerAreaList, setSellerAreaList] = useState<SellerAreas[]>([]);\r\n      const [activeTab, setActiveTab] = useState('config');\r\n      const [selectedAttribute, setSelectedAttribute] = useState(\"\");\r\n      const [loader, setLoader] = useState(false);\r\n\r\n      const [sellerData, setSellerData] = useState({\r\n            name: data?.name,\r\n            autoAccept: data?.auto_accept,\r\n            autoPack: data?.auto_pack,\r\n            autoPickup: data?.auto_pickup,\r\n            autoDispatch: data?.auto_dispatch,\r\n            strikeoffEnabled: data?.strikeoff_enabled,\r\n            favItemsEnabled: data?.fav_items_enabled,\r\n            itemPickEnabled: data?.item_pick_enabled,\r\n            contractPriceEnabled: data?.contract_price_enabled,\r\n            approxPricing: data?.approx_pricing,\r\n            isPayLaterEnabled: data?.is_pay_later_enabled,\r\n            autoActivate: data?.auto_activate,\r\n            allowCoD: data.allow_cod,\r\n            wa_enable: data.wa_enable,\r\n            minimumRequiredBalance: data.mininum_required_balance,\r\n            minimumOrderQty: data.minimum_order_qty,\r\n            minimumOrderValue: data.minimum_order_value,\r\n            categoryLevel: data.category_level,\r\n            bookingCloseTime: data.booking_close_time,\r\n            bookingOpenTime: data.booking_open_time,\r\n            dispatchTime: data.dispatch_time,\r\n            deliveryTime: data.delivery_time\r\n\r\n      });\r\n      const [updateAttributes, setUpdateAttributes] = useState<boolean | undefined>(isResponse);\r\n      const [selectedField, setSelectedField] = useState('');\r\n      const [searchTerm, setSearchTerm] = useState('')\r\n      const updateSellerData = (key: string, value: any) => {\r\n            setSellerData((prev) => ({ ...prev, [key]: value }));\r\n      };\r\n      const fetcher = useFetcher<{ data: MasterItemCategories[] | SellerAreas[] }>();\r\n      const [loadingFields, setLoadingFields] = useState<Record<string, boolean>>({});\r\n\r\n      const handleTabChange = (newTab: string) => {\r\n            setActiveTab(newTab);\r\n            if (newTab === 'netWorkItemDetails') {\r\n                  const formData = new FormData();\r\n                  formData.append(\"intent\", \"netWorkItemDetails\");\r\n                  formData.append(\"sellerId\", sellerId.toString());\r\n                  fetcher.submit(formData, { method: \"POST\" });\r\n            }\r\n            if (newTab === \"sellerAreas\") {\r\n                  setUpdateAttributes(false)\r\n                  const formData = new FormData();\r\n                  formData.append(\"intent\", \"sellerAreas\");\r\n                  formData.append(\"sellerId\", sellerId.toString());\r\n                  fetcher.submit(formData, { method: \"POST\" });\r\n            }\r\n\r\n      };\r\n\r\n      const handleUpdate = async (attributeType: string, val: any, id: number, type: string) => {\r\n\r\n\r\n            console.log(loader, \"99999999999999\")\r\n            setLoadingFields((prev) => ({ ...prev, [attributeType]: true })); // Set loading to true before request\r\n\r\n            const formData = new FormData();\r\n            formData.append(\"updateType\", type);\r\n            formData.append(\"sellerId\", id.toString());\r\n            formData.append(\"attribute\", attributeType);\r\n            formData.append(\"value\", val.toString());\r\n\r\n            try {\r\n\r\n\r\n                  await fetcher.submit(formData, { method: \"POST\" });\r\n\r\n                  showToast(`${attributeType.replace(/([A-Z])/g, \" $1\")} updated successfully`, \"success\");\r\n                  updateSellerData(attributeType, val);\r\n\r\n\r\n\r\n                  // Update the UI after success\r\n            } catch (error) {\r\n                  showToast(`Failed to update ${attributeType.replace(/([A-Z])/g, \" $1\")}`, \"error\");\r\n            } finally {\r\n                  setLoadingFields((prev) => ({ ...prev, [attributeType]: false })); // Reset loading state\r\n            }\r\n      };\r\n\r\n\r\n      const handleSwitch = async (\r\n            field: \"autoAccept\" | \"autoPack\" | \"autoPickup\" | \"autoDispatch\" | \"approxPricing\" |\r\n                  \"strikeoffEnabled\" | \"itemPickEnabled\" | \"contractPriceEnabled\" | \"isPayLaterEnabled\" |\r\n                  \"wa_enable\" | \"autoActivate\" | \"allowCoD\" | \"favItemsEnabled\"\r\n      ) => {\r\n            setLoadingFields((prev) => ({ ...prev, [field]: true }));\r\n\r\n            const currentValue = sellerData[field];\r\n            const newValue = !currentValue;\r\n\r\n            try {\r\n                  const formData = new FormData();\r\n                  formData.append(\"updateType\", \"seller\");\r\n                  formData.append(\"sellerId\", sellerId.toString());\r\n                  formData.append(\"attribute\", field);\r\n                  formData.append(\"value\", newValue.toString());\r\n\r\n                  await fetcher.submit(formData, { method: \"POST\" });\r\n\r\n                  showToast(`${field.replace(/([A-Z])/g, \" $1\")} updated successfully`, \"success\");\r\n                  updateSellerData(field, newValue);\r\n                  setSelectedField(field)\r\n                  // Update UI only after successful response\r\n            } catch (error) {\r\n                  showToast(`Failed to update ${field.replace(/([A-Z])/g, \" $1\")}`, \"error\");\r\n            } finally {\r\n                  setLoadingFields((prev) => ({ ...prev, [field]: false }));\r\n            }\r\n      };\r\n      useEffect(() => {\r\n            if (fetcher.data) {\r\n                  if (activeTab === \"netWorkItemDetails\" && fetcher.data.data) {\r\n                        setSelectedNetworkItemCategory(fetcher.data.data as MasterItemCategories[]);\r\n                  } else if (activeTab === \"sellerAreas\" && fetcher.data.data) {\r\n                        setSellerAreaList(fetcher.data.data as SellerAreas[]);\r\n                  }\r\n            }\r\n      }, [fetcher.data, activeTab]);\r\n      const updateToggle = (status: boolean, areaId: number) => {\r\n            const formData = new FormData()\r\n            formData.append(\"sellerId\", sellerId as unknown as string)\r\n            formData.append(\"areaStatus\", status as unknown as string)\r\n            formData.append(\"areaId\", areaId as unknown as string)\r\n            formData.append(\"intent\", \"updateArea\")\r\n            formData.append(\"name\", name)\r\n            fetcher.submit(formData, { method: \"POST\" })\r\n      }\r\n      { fetcher.state !== \"idle\" && <SpinnerLoader size={8} loading={true} /> }\r\n      return (\r\n            <div className=\"container mx-auto p-6\">\r\n                  <div className=\"flex items-center gap-2 mb-6\">\r\n                        <Button variant=\"ghost\" size=\"sm\" onClick={() => navigate(-1)}>\r\n                              <ArrowLeft className=\"h-4 w-4 mr-2\" />\r\n                              Back to Sellers\r\n                        </Button>\r\n                        <span className=\"text-muted-foreground\">/</span>\r\n                        <span className=\"font-semibold\">{name}</span>\r\n                  </div>\r\n                  <Tabs value={activeTab} onValueChange={handleTabChange} className=\"mb-6\">\r\n                        <TabsList>\r\n                              <TabsTrigger value=\"config\">Config</TabsTrigger>\r\n                              <TabsTrigger value=\"sellerAreas\">Seller Areas</TabsTrigger>\r\n                              {/* <TabsTrigger value=\"SellerItems\">Seller Items</TabsTrigger>\r\n                              <TabsTrigger value=\"sellerItemCategory\">SellerItem Category</TabsTrigger> */}\r\n                        </TabsList>\r\n                        <TabsContent value=\"config\">\r\n                              <div className=\"flex flex-col md:flex-row gap-6\">\r\n                                    {/* Left Side - Basic Info */}\r\n                                    <div className=\"w-full md:w-1/2 bg-white p-6 border rounded-lg shadow-md space-y-6\">\r\n                                          <div className=\"flex justify-between items-center\">\r\n                                                <p className=\"text-lg font-semibold text-gray-700\">Update Details</p>\r\n                                                <Switch checked={updateAttributes} onClick={() => setUpdateAttributes(!updateAttributes)} />\r\n                                          </div>\r\n\r\n                                          <div className=\"space-y-4\">\r\n                                                <div className=\"flex items-center space-x-4\">\r\n                                                      <p className=\"text-lg font-semibold text-gray-700\">ID:</p>\r\n                                                      <p className=\"text-lg text-gray-900\">{data.id}</p>\r\n                                                </div>\r\n\r\n                                                <div className=\"flex items-center space-x-4\">\r\n                                                      <p className=\"text-lg font-semibold text-gray-700\">Name:</p>\r\n                                                      <Input\r\n                                                            placeholder=\"Enter Name\"\r\n                                                            value={sellerData?.name}\r\n                                                            onChange={(e) => updateSellerData(\"name\", e.target.value)}\r\n                                                            disabled={!updateAttributes}\r\n\r\n                                                      />\r\n                                                      {updateAttributes && (\r\n                                                            <Button onClick={() => handleUpdate(\"name\", sellerData?.name, sellerId, \"seller\")}>\r\n                                                                  Update\r\n                                                            </Button>\r\n                                                      )}\r\n                                                </div>\r\n\r\n\r\n                                                {[\r\n\r\n\r\n\r\n\r\n                                                      { label: \"Minimum Required Balance\", key: \"minimumRequiredBalance\", value: sellerData.minimumRequiredBalance },\r\n                                                      { label: \"Minimum Order Quantity\", key: \"minimumOrderQty\", value: sellerData.minimumOrderQty },\r\n                                                      { label: \"Minimum Order Value\", key: \"minimumOrderValue\", value: sellerData.minimumOrderValue },\r\n                                                      { label: \"Platform Fee (Fixed):\", key: \"platformFee\", value: sellerData.minimumOrderValue },\r\n                                                      { label: \"Platform Fee (%)\", key: \"platformFeePerc\", value: sellerData.minimumOrderValue },\r\n                                                      { label: \"Platform Fee per Kg:\", key: \"platformFeePkg\",value: sellerData.minimumOrderValue },\r\n\r\n                                                ].map((item) => (\r\n                                                      <div key={item.key} className=\"flex items-center space-x-4\">\r\n                                                            <p className=\"text-lg font-semibold text-gray-700\">{item.label}:</p>\r\n                                                            <Input\r\n                                                                  type=\"number\"\r\n                                                                  placeholder={`Enter ${item.label}`}\r\n                                                                  value={item.value}\r\n                                                                  onChange={(e) => updateSellerData(item.key, e.target.value)}\r\n                                                                  disabled={!updateAttributes}\r\n\r\n                                                            />\r\n                                                            {updateAttributes && (\r\n                                                                  <Button onClick={() => handleUpdate(item.key, item.value, sellerId, \"seller\")}>\r\n                                                                        Update\r\n                                                                  </Button>\r\n                                                            )}\r\n                                                      </div>\r\n                                                ))}\r\n\r\n                                                {/* Integer Fields */}\r\n                                                {[\r\n                                                      { label: \"Category Level\", key: \"categoryLevel\", value: sellerData.categoryLevel },\r\n                                                      { label: \"Booking Close Time\", key: \"bookingCloseTime\", value: sellerData.bookingCloseTime },\r\n                                                      { label: \"Booking Open Time\", key: \"bookingOpenTime\", value: sellerData.bookingOpenTime },\r\n                                                ].map((item) => (\r\n                                                      <div key={item.key} className=\"flex items-center space-x-4\">\r\n                                                            <p className=\"text-lg font-semibold text-gray-700\">{item.label}:</p>\r\n                                                            <Input\r\n                                                                  type=\"number\"\r\n                                                                  placeholder={`Enter ${item.label}`}\r\n                                                                  value={item.value}\r\n                                                                  onChange={(e) => updateSellerData(item.key, e.target.value)}\r\n                                                                  disabled={!updateAttributes}\r\n\r\n                                                            />\r\n                                                            {updateAttributes && (\r\n                                                                  <Button onClick={() => handleUpdate(item.key, item.value, sellerId, \"seller\")}>\r\n                                                                        Update\r\n                                                                  </Button>\r\n                                                            )}\r\n                                                      </div>\r\n                                                ))}\r\n\r\n                                                {/* String Fields */}\r\n                                                {[\r\n                                                      { label: \"Dispatch Time\", key: \"dispatchTime\", value: sellerData.dispatchTime },\r\n                                                      { label: \"Delivery Time\", key: \"deliveryTime\", value: sellerData.deliveryTime },\r\n                                                ].map((item) => (\r\n                                                      <div key={item.key} className=\"flex items-center space-x-4\">\r\n                                                            <p className=\"text-lg font-semibold text-gray-700\">{item.label}:</p>\r\n                                                            <Input\r\n                                                                  type=\"text\"\r\n                                                                  placeholder={`Enter ${item.label}`}\r\n                                                                  value={item.value}\r\n                                                                  onChange={(e) => updateSellerData(item.key, e.target.value)}\r\n                                                                  disabled={!updateAttributes}\r\n                                                            />\r\n                                                            {updateAttributes && (\r\n                                                                  <Button onClick={() => handleUpdate(item.key, item.value, sellerId, \"seller\")}\r\n                                                                        disabled={loadingFields[item.key] || !updateAttributes}\r\n\r\n\r\n                                                                        loading={loadingFields[item.key]}\r\n                                                                  >\r\n                                                                        {loadingFields[item.key] ? \"Updating...\" : \"Update\"}\r\n\r\n                                                                  </Button>\r\n                                                            )}\r\n                                                      </div>\r\n                                                ))}\r\n                                          </div>\r\n                                    </div>\r\n\r\n                                    {/* Right Side - Toggle Switches */}\r\n                                    <div className=\"w-full md:w-1/2 bg-white p-6 border rounded-lg shadow-md space-y-4\">\r\n                                          {[\r\n                                                { label: \"Auto Accept\", key: \"autoAccept\" as const, value: sellerData?.autoAccept },\r\n                                                { label: \"Auto Pack\", key: \"autoPack\" as const, value: sellerData?.autoPack },\r\n                                                { label: \"Auto Pick\", key: \"autoPickup\" as const, value: sellerData?.autoPickup },\r\n                                                { label: \"Auto Dispatch\", key: \"autoDispatch\" as const, value: sellerData?.autoDispatch },\r\n                                                { label: \"Strike Off\", key: \"strikeoffEnabled\" as const, value: sellerData?.strikeoffEnabled },\r\n                                                { label: \"Item Pick\", key: \"itemPickEnabled\" as const, value: sellerData?.itemPickEnabled },\r\n                                                { label: \"Contract Price\", key: \"contractPriceEnabled\" as const, value: sellerData?.contractPriceEnabled },\r\n                                                { label: \"Approx Price\", key: \"approxPricing\" as const, value: sellerData?.approxPricing },\r\n                                                { label: \"Pay Later\", key: \"isPayLaterEnabled\" as const, value: sellerData?.isPayLaterEnabled },\r\n                                                { label: \"WhatsApp Enable\", key: \"wa_enable\" as const, value: sellerData?.wa_enable },\r\n                                                { label: \"Favorite Items\", key: \"favItemsEnabled\" as const, value: sellerData?.favItemsEnabled },\r\n                                                { label: \"Auto Activate\", key: \"autoActivate\" as const, value: sellerData?.autoActivate },\r\n                                                { label: \"Allow CoD\", key: \"allowCoD\" as const, value: sellerData?.allowCoD },\r\n\r\n\r\n                                          ].map((item) => (\r\n                                                <div key={item.key} className=\"flex justify-between items-center\">\r\n                                                      <p className=\"text-lg font-semibold text-gray-700\">{item.label}</p>\r\n                                                      <Switch\r\n                                                            checked={!item.value}\r\n                                                            onClick={() => handleSwitch(item.key)}\r\n                                                            disabled={loadingFields[item.key] || !updateAttributes}\r\n                                                            {...loadingFields[item.key] && <SpinnerLoader size={10} loading={true} />}\r\n\r\n                                                      />\r\n                                                </div>\r\n                                          ))}\r\n                                    </div>\r\n                              </div>\r\n                        </TabsContent >\r\n\r\n                        {/* <TabsContent value=\"sellerItemCategory\">\r\n                              <CommonItemCategoryList data={selectedNetworkItemCategory} />\r\n                        </TabsContent> */}\r\n                        < TabsContent value=\"sellerAreas\" >\r\n                              <SellerAreaList sellerAreaData={sellerAreaList} updateToggle={updateToggle} />\r\n                        </TabsContent >\r\n                        {/* <TabsContent value=\"SellerItems\">\r\n                              <div className=\"flex justify-between mb-4 my-4\">\r\n                                    <Input\r\n                                          placeholder=\"Search by Name\"\r\n                                          value={searchTerm}\r\n                                          onChange={(e) => setSearchTerm(e.target.value)}\r\n                                          className=\"max-w-sm\"\r\n                                    />\r\n                                    <Button onClick={() => setIsAddSellerItem(!isAddSellerItem)} size=\"sm\">\r\n                                          Add Seller Items\r\n                                    </Button>\r\n\r\n                              </div>\r\n                              {isAddSellerItem && <AddSellerItem />}\r\n                              <Table>\r\n                                    <TableHeader>\r\n                                          <TableRow>\r\n                                                <TableHead>Item Name</TableHead>\r\n                                                <TableHead>Item Image</TableHead>\r\n                                                <TableHead>Unit</TableHead>\r\n                                                <TableHead>Is Active</TableHead>\r\n                                          </TableRow>\r\n                                    </TableHeader>\r\n                                    <TableBody>\r\n                                          <TableRow>\r\n                                                <TableCell></TableCell>\r\n                                                <TableCell></TableCell>\r\n                                                <TableCell></TableCell>\r\n                                                <TableCell></TableCell>\r\n\r\n                                          </TableRow>\r\n                                    </TableBody>\r\n                              </Table>\r\n                        </TabsContent> */}\r\n\r\n                  </Tabs >\r\n            </div >\r\n      )\r\n}\r\n\r\n\r\n"], "names": ["useState", "jsxs", "jsx", "SelectedSellerDetail", "showToast", "useToast", "navigate", "useNavigate", "data", "sellerId", "name", "isResponse", "useLoaderData", "selectedNetworkItemCategory", "setSelectedNetworkItemCategory", "sellerAreaList", "setSellerAreaList", "activeTab", "setActiveTab", "selectedAttribute", "setSelectedAttribute", "loader", "<PERSON><PERSON><PERSON><PERSON>", "sellerData", "setSellerData", "autoAccept", "auto_accept", "autoPack", "auto_pack", "autoPickup", "auto_pickup", "autoDispatch", "auto_dispatch", "strikeoffEnabled", "strikeoff_enabled", "favItemsEnabled", "fav_items_enabled", "itemPickEnabled", "item_pick_enabled", "contractPriceEnabled", "contract_price_enabled", "approxPricing", "approx_pricing", "isPayLaterEnabled", "is_pay_later_enabled", "autoActivate", "auto_activate", "allowCoD", "allow_cod", "wa_enable", "minimumRequiredBalance", "mininum_required_balance", "minimumOrderQty", "minimum_order_qty", "minimumOrderValue", "minimum_order_value", "categoryLevel", "category_level", "bookingCloseTime", "booking_close_time", "bookingOpenTime", "booking_open_time", "dispatchTime", "dispatch_time", "deliveryTime", "delivery_time", "updateAttributes", "setUpdateAttributes", "<PERSON><PERSON><PERSON>", "setSelectedField", "searchTerm", "setSearchTerm", "updateSellerData", "key", "value", "prev", "fetcher", "useFetcher", "loadingFields", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "handleTabChange", "newTab", "formData", "FormData", "append", "toString", "submit", "method", "handleUpdate", "attributeType", "val", "id", "type", "console", "log", "replace", "error", "handleSwitch", "field", "currentValue", "newValue", "useEffect", "updateToggle", "status", "areaId", "state", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "size", "loading", "className", "children", "<PERSON><PERSON>", "variant", "onClick", "ArrowLeft", "Tabs", "onValueChange", "TabsList", "TabsTrigger", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Switch", "checked", "Input", "placeholder", "onChange", "e", "target", "disabled", "label", "map", "item", "SellerAreaList", "sellerAreaData"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAUA,SAAwB,eAAe,EAAE,gBAAgB,gBAAkC;AACrF,QAAM,CAAC,cAAc,eAAe,IAAIA,aAAAA,SAAsB,oBAAI,KAAK;AAEjE,QAAA,eAAe,CAAC,QAAiB,WAAmB;AAC9C,UAAA,iBAAiB,IAAI,IAAI,YAAY;AAC3C,QAAI,QAAQ;AACN,qBAAe,IAAI,MAAM;AAAA,IAAA,OACxB;AACD,qBAAe,OAAO,MAAM;AAAA,IAAA;AAElC,oBAAgB,cAAc;AAC9B,iBAAa,QAAQ,MAAM;AAAA,EACjC;AAGM,SAAAC,kCAAA,KAAC,OAAM,EAAA,WAAU,wCACX,UAAA;AAAA,IAAAC,sCAAC,aACK,EAAA,UAAAD,kCAAAA,KAAC,UAAS,EAAA,WAAU,eACd,UAAA;AAAA,MAACC,kCAAA,IAAA,WAAA,EAAU,WAAU,2BAA0B,UAAS,aAAA;AAAA,MACvDA,kCAAA,IAAA,WAAA,EAAU,WAAU,4BAA2B,UAAQ,YAAA;AAAA,MACvDA,kCAAA,IAAA,WAAA,EAAU,WAAU,4BAA2B,UAAK,SAAA;AAAA,MACpDA,kCAAA,IAAA,WAAA,EAAU,WAAU,4BAA2B,UAAM,SAAA,CAAA;AAAA,IAAA,EAAA,CAC5D,EACN,CAAA;AAAA,IACAA,kCAAAA,IAAC,aACM,UAAM,MAAA,QAAQ,cAAc,KAAK,eAAe,IAAI,CAAC,MAAM;;AACtD,YAAM,YAAY,aAAa,IAAI,uBAAG,YAAY;AAE5C,aAAAD,kCAAA,KAAC,UAA2B,EAAA,WAAU,0BAChC,UAAA;AAAA,QAACC,kCAAA,IAAA,WAAA,EAAY,WAAG,4BAAA,SAAA,mBAAM,MAAK;AAAA,QAC1BA,kCAAA,IAAA,WAAA,EAAW,WAAG,4BAAA,SAAA,mBAAM,UAAS;AAAA,QAC7BA,kCAAA,IAAA,WAAA,EAAW,WAAG,4BAAA,SAAA,mBAAM,OAAM;AAAA,8CAC1B,WACK,EAAA,UAAAA,kCAAA;AAAA,UAAC;AAAA,UAAA;AAAA,YACK,SAAS,cAAa,uBAAG;AAAA,YACzB,SAAS,MAAM,aAAa,CAAC,WAAW,uBAAG,YAAY;AAAA,UAAA;AAAA,QAAA,EAEnE,CAAA;AAAA,MAAA,MATS,4BAAG,SAAH,mBAAS,EAUxB;AAAA,IAAA,CAEX,EACP,CAAA;AAAA,EAAA,GACN;AAEZ;ACqEA,SAAwBC,uBAAuB;AACnC,QAAA;AAAA,IAAEC;AAAAA,EAAU,IAAIC,SAAS;AAE/B,QAAMC,WAAWC,YAAY;AAC7B,QAAM;AAAA,IAAEC;AAAAA,IAAMC;AAAAA,IAAUC;AAAAA,IAAMC;AAAAA,MAAeC,cAA0B;AACvE,QAAM,CAACC,6BAA6BC,8BAA8B,IAAId,aAAAA,SAAiC,CAAA,CAAE;AACzG,QAAM,CAACe,gBAAgBC,iBAAiB,IAAIhB,aAAAA,SAAwB,CAAA,CAAE;AACtE,QAAM,CAACiB,WAAWC,YAAY,IAAIlB,aAAAA,SAAS,QAAQ;AACnD,QAAM,CAACmB,mBAAmBC,oBAAoB,IAAIpB,aAAAA,SAAS,EAAE;AAC7D,QAAM,CAACqB,SAAQC,SAAS,IAAItB,aAAAA,SAAS,KAAK;AAE1C,QAAM,CAACuB,YAAYC,aAAa,IAAIxB,sBAAS;AAAA,IACvCU,MAAMF,6BAAME;AAAAA,IACZe,YAAYjB,6BAAMkB;AAAAA,IAClBC,UAAUnB,6BAAMoB;AAAAA,IAChBC,YAAYrB,6BAAMsB;AAAAA,IAClBC,cAAcvB,6BAAMwB;AAAAA,IACpBC,kBAAkBzB,6BAAM0B;AAAAA,IACxBC,iBAAiB3B,6BAAM4B;AAAAA,IACvBC,iBAAiB7B,6BAAM8B;AAAAA,IACvBC,sBAAsB/B,6BAAMgC;AAAAA,IAC5BC,eAAejC,6BAAMkC;AAAAA,IACrBC,mBAAmBnC,6BAAMoC;AAAAA,IACzBC,cAAcrC,6BAAMsC;AAAAA,IACpBC,UAAUvC,KAAKwC;AAAAA,IACfC,WAAWzC,KAAKyC;AAAAA,IAChBC,wBAAwB1C,KAAK2C;AAAAA,IAC7BC,iBAAiB5C,KAAK6C;AAAAA,IACtBC,mBAAmB9C,KAAK+C;AAAAA,IACxBC,eAAehD,KAAKiD;AAAAA,IACpBC,kBAAkBlD,KAAKmD;AAAAA,IACvBC,iBAAiBpD,KAAKqD;AAAAA,IACtBC,cAActD,KAAKuD;AAAAA,IACnBC,cAAcxD,KAAKyD;AAAAA,EAEzB,CAAC;AACD,QAAM,CAACC,mBAAkBC,mBAAmB,IAAInE,aAAAA,SAA8BW,UAAU;AACxF,QAAM,CAACyD,eAAeC,gBAAgB,IAAIrE,aAAAA,SAAS,EAAE;AACrD,QAAM,CAACsE,YAAYC,aAAa,IAAIvE,aAAAA,SAAS,EAAE;AACzC,QAAAwE,mBAAmBA,CAACC,KAAaC,UAAe;AAClClD,kBAACmD,WAAU;AAAA,MAAE,GAAGA;AAAAA,MAAM,CAACF,GAAG,GAAGC;AAAAA,IAAM,EAAE;AAAA,EACzD;AACA,QAAME,UAAUC,WAA6D;AAC7E,QAAM,CAACC,eAAeC,gBAAgB,IAAI/E,aAAAA,SAAkC,CAAA,CAAE;AAExE,QAAAgF,kBAAmBC,YAAmB;AACtC/D,iBAAa+D,MAAM;AACnB,QAAIA,WAAW,sBAAsB;AACzB,YAAAC,WAAW,IAAIC,SAAS;AACrBD,eAAAE,OAAO,UAAU,oBAAoB;AAC9CF,eAASE,OAAO,YAAY3E,SAAS4E,SAAA,CAAU;AAC/CT,cAAQU,OAAOJ,UAAU;AAAA,QAAEK,QAAQ;AAAA,MAAO,CAAC;AAAA,IACjD;AACA,QAAIN,WAAW,eAAe;AACxBd,0BAAoB,KAAK;AACnB,YAAAe,WAAW,IAAIC,SAAS;AACrBD,eAAAE,OAAO,UAAU,aAAa;AACvCF,eAASE,OAAO,YAAY3E,SAAS4E,SAAA,CAAU;AAC/CT,cAAQU,OAAOJ,UAAU;AAAA,QAAEK,QAAQ;AAAA,MAAO,CAAC;AAAA,IACjD;AAAA,EAEN;AAEA,QAAMC,eAAe,OAAOC,eAAuBC,KAAUC,IAAYC,SAAiB;AAG5EC,YAAAC,IAAIzE,SAAQ,gBAAgB;AACnB0D,qBAACJ,WAAU;AAAA,MAAE,GAAGA;AAAAA,MAAM,CAACc,aAAa,GAAG;AAAA,IAAK,EAAE;AAEzD,UAAAP,WAAW,IAAIC,SAAS;AACrBD,aAAAE,OAAO,cAAcQ,IAAI;AAClCV,aAASE,OAAO,YAAYO,GAAGN,SAAA,CAAU;AAChCH,aAAAE,OAAO,aAAaK,aAAa;AAC1CP,aAASE,OAAO,SAASM,IAAIL,SAAA,CAAU;AAEnC,QAAA;AAGE,YAAMT,QAAQU,OAAOJ,UAAU;AAAA,QAAEK,QAAQ;AAAA,MAAO,CAAC;AAEjDnF,gBAAU,GAAGqF,cAAcM,QAAQ,YAAY,KAAK,CAAC,yBAAyB,SAAS;AACvFvB,uBAAiBiB,eAAeC,GAAG;AAAA,aAKhCM,QAAO;AACV5F,gBAAU,oBAAoBqF,cAAcM,QAAQ,YAAY,KAAK,CAAC,IAAI,OAAO;AAAA,IACvF,UAAE;AACqBhB,uBAACJ,WAAU;AAAA,QAAE,GAAGA;AAAAA,QAAM,CAACc,aAAa,GAAG;AAAA,MAAM,EAAE;AAAA,IACtE;AAAA,EACN;AAGM,QAAAQ,eAAe,OACfC,UAGD;AACkBnB,qBAACJ,WAAU;AAAA,MAAE,GAAGA;AAAAA,MAAM,CAACuB,KAAK,GAAG;AAAA,IAAK,EAAE;AAEjD,UAAAC,eAAe5E,WAAW2E,KAAK;AACrC,UAAME,WAAW,CAACD;AAEd,QAAA;AACQ,YAAAjB,WAAW,IAAIC,SAAS;AACrBD,eAAAE,OAAO,cAAc,QAAQ;AACtCF,eAASE,OAAO,YAAY3E,SAAS4E,SAAA,CAAU;AACtCH,eAAAE,OAAO,aAAac,KAAK;AAClChB,eAASE,OAAO,SAASgB,SAASf,SAAA,CAAU;AAE5C,YAAMT,QAAQU,OAAOJ,UAAU;AAAA,QAAEK,QAAQ;AAAA,MAAO,CAAC;AAEjDnF,gBAAU,GAAG8F,MAAMH,QAAQ,YAAY,KAAK,CAAC,yBAAyB,SAAS;AAC/EvB,uBAAiB0B,OAAOE,QAAQ;AAChC/B,uBAAiB6B,KAAK;AAAA,aAEnBF,QAAO;AACV5F,gBAAU,oBAAoB8F,MAAMH,QAAQ,YAAY,KAAK,CAAC,IAAI,OAAO;AAAA,IAC/E,UAAE;AACqBhB,uBAACJ,WAAU;AAAA,QAAE,GAAGA;AAAAA,QAAM,CAACuB,KAAK,GAAG;AAAA,MAAM,EAAE;AAAA,IAC9D;AAAA,EACN;AACAG,eAAAA,UAAU,MAAM;AACV,QAAIzB,QAAQpE,MAAM;AACZ,UAAIS,cAAc,wBAAwB2D,QAAQpE,KAAKA,MAAM;AACxBM,uCAAA8D,QAAQpE,KAAKA,IAA8B;AAAA,MACrE,WAAAS,cAAc,iBAAiB2D,QAAQpE,KAAKA,MAAM;AACrCQ,0BAAA4D,QAAQpE,KAAKA,IAAqB;AAAA,MAC1D;AAAA,IACN;AAAA,EACH,GAAA,CAACoE,QAAQpE,MAAMS,SAAS,CAAC;AACtB,QAAAqF,eAAeA,CAACC,QAAiBC,WAAmB;AAC9C,UAAAtB,WAAW,IAAIC,SAAS;AACrBD,aAAAE,OAAO,YAAY3E,QAA6B;AAChDyE,aAAAE,OAAO,cAAcmB,MAA2B;AAChDrB,aAAAE,OAAO,UAAUoB,MAA2B;AAC5CtB,aAAAE,OAAO,UAAU,YAAY;AAC7BF,aAAAE,OAAO,QAAQ1E,IAAI;AAC5BkE,YAAQU,OAAOJ,UAAU;AAAA,MAAEK,QAAQ;AAAA,IAAO,CAAC;AAAA,EACjD;AACA;AAAEX,YAAQ6B,UAAU,UAAUvG,kCAAAA,IAACwG;MAAcC,MAAM;AAAA,MAAGC,SAAS;AAAA,IAAM,CAAA;AAAA,EAAG;AAElE,SAAA3G,kCAAAA,KAAC,OAAI;AAAA,IAAA4G,WAAU;AAAA,IACTC,UAAA,CAAC7G,kCAAA,KAAA,OAAA;AAAA,MAAI4G,WAAU;AAAA,MACTC,UAAA,CAAC7G,kCAAA,KAAA8G,QAAA;AAAA,QAAOC,SAAQ;AAAA,QAAQL,MAAK;AAAA,QAAKM,SAASA,MAAM3G,SAAS,EAAE;AAAA,QACtDwG,UAAA,CAAC5G,kCAAA,IAAAgH,WAAA;AAAA,UAAUL,WAAU;AAAA,QAAe,CAAA,GAAE,iBAAA;AAAA,MAE5C,CAAA,GACC3G,kCAAA,IAAA,QAAA;AAAA,QAAK2G,WAAU;AAAA,QAAwBC,UAAC;AAAA,MAAA,CAAA,GACxC5G,kCAAA,IAAA,QAAA;AAAA,QAAK2G,WAAU;AAAA,QAAiBC,UAAKpG;AAAAA,MAAA,CAAA,CAAA;AAAA,IAC5C,CAAA,0CACCyG,MAAK;AAAA,MAAAzC,OAAOzD;AAAAA,MAAWmG,eAAepC;AAAAA,MAAiB6B,WAAU;AAAA,MAC5DC,UAAA,CAAA7G,kCAAA,KAACoH,UACK;AAAA,QAAAP,UAAA,CAAC5G,kCAAA,IAAAoH,aAAA;AAAA,UAAY5C,OAAM;AAAA,UAASoC,UAAM;AAAA,QAAA,CAAA,GACjC5G,kCAAA,IAAAoH,aAAA;AAAA,UAAY5C,OAAM;AAAA,UAAcoC,UAAY;AAAA,QAAA,CAAA,CAAA;AAAA,MAGnD,CAAA,yCACCS,aAAY;AAAA,QAAA7C,OAAM;AAAA,QACboC,UAAC7G,kCAAA,KAAA,OAAA;AAAA,UAAI4G,WAAU;AAAA,UAETC,UAAA,CAAC7G,kCAAA,KAAA,OAAA;AAAA,YAAI4G,WAAU;AAAA,YACTC,UAAA,CAAC7G,kCAAA,KAAA,OAAA;AAAA,cAAI4G,WAAU;AAAA,cACTC,UAAA,CAAC5G,kCAAA,IAAA,KAAA;AAAA,gBAAE2G,WAAU;AAAA,gBAAsCC,UAAc;AAAA,cAAA,CAAA,GACjE5G,kCAAA,IAACsH;gBAAOC,SAASvD;AAAAA,gBAAkB+C,SAASA,MAAM9C,oBAAoB,CAACD,iBAAgB;AAAA,cAAG,CAAA,CAAA;AAAA,YAChG,CAAA,GAEAjE,kCAAA,KAAC,OAAI;AAAA,cAAA4G,WAAU;AAAA,cACTC,UAAA,CAAC7G,kCAAA,KAAA,OAAA;AAAA,gBAAI4G,WAAU;AAAA,gBACTC,UAAA,CAAC5G,kCAAA,IAAA,KAAA;AAAA,kBAAE2G,WAAU;AAAA,kBAAsCC,UAAG;AAAA,gBAAA,CAAA,GACrD5G,kCAAA,IAAA,KAAA;AAAA,kBAAE2G,WAAU;AAAA,kBAAyBC,eAAKnB;AAAAA,gBAAG,CAAA,CAAA;AAAA,cACpD,CAAA,GAEA1F,kCAAA,KAAC,OAAI;AAAA,gBAAA4G,WAAU;AAAA,gBACTC,UAAA,CAAC5G,kCAAA,IAAA,KAAA;AAAA,kBAAE2G,WAAU;AAAA,kBAAsCC,UAAK;AAAA,gBAAA,CAAA,GACxD5G,kCAAA,IAACwH,OAAA;AAAA,kBACKC,aAAY;AAAA,kBACZjD,OAAOnD,yCAAYb;AAAAA,kBACnBkH,UAAWC,OAAMrD,iBAAiB,QAAQqD,EAAEC,OAAOpD,KAAK;AAAA,kBACxDqD,UAAU,CAAC7D;AAAAA,iBAEjB,GACCA,qBACKhE,kCAAA,IAAC6G,QAAO;AAAA,kBAAAE,SAASA,MAAMzB,aAAa,QAAQjE,yCAAYb,MAAMD,UAAU,QAAQ;AAAA,kBAAGqG,UAEnF;AAAA,gBAAA,CAAA,CAAA;AAAA,cAEZ,CAAA,GAGC,CAKK;AAAA,gBAAEkB,OAAO;AAAA,gBAA4BvD,KAAK;AAAA,gBAA0BC,OAAOnD,WAAW2B;AAAAA,cAAuB,GAC7G;AAAA,gBAAE8E,OAAO;AAAA,gBAA0BvD,KAAK;AAAA,gBAAmBC,OAAOnD,WAAW6B;AAAAA,cAAgB,GAC7F;AAAA,gBAAE4E,OAAO;AAAA,gBAAuBvD,KAAK;AAAA,gBAAqBC,OAAOnD,WAAW+B;AAAAA,cAAkB,GAC9F;AAAA,gBAAE0E,OAAO;AAAA,gBAAyBvD,KAAK;AAAA,gBAAeC,OAAOnD,WAAW+B;AAAAA,cAAkB,GAC1F;AAAA,gBAAE0E,OAAO;AAAA,gBAAoBvD,KAAK;AAAA,gBAAmBC,OAAOnD,WAAW+B;AAAAA,cAAkB,GACzF;AAAA,gBAAE0E,OAAO;AAAA,gBAAwBvD,KAAK;AAAA,gBAAiBC,OAAOnD,WAAW+B;AAAAA,cAAkB,CAAA,EAE/F2E,IAAKC,UACAjI,kCAAAA,KAAA,OAAA;AAAA,gBAAmB4G,WAAU;AAAA,gBACxBC,UAAA,CAAC7G,kCAAA,KAAA,KAAA;AAAA,kBAAE4G,WAAU;AAAA,kBAAuCC,UAAA,CAAKoB,KAAAF,OAAM,GAAA;AAAA,gBAAC,CAAA,GAChE9H,kCAAA,IAACwH,OAAA;AAAA,kBACK9B,MAAK;AAAA,kBACL+B,aAAa,SAASO,KAAKF,KAAK;AAAA,kBAChCtD,OAAOwD,KAAKxD;AAAAA,kBACZkD,UAAWC,OAAMrD,iBAAiB0D,KAAKzD,KAAKoD,EAAEC,OAAOpD,KAAK;AAAA,kBAC1DqD,UAAU,CAAC7D;AAAAA,iBAEjB,GACCA,qBACKhE,kCAAA,IAAC6G,QAAO;AAAA,kBAAAE,SAASA,MAAMzB,aAAa0C,KAAKzD,KAAKyD,KAAKxD,OAAOjE,UAAU,QAAQ;AAAA,kBAAGqG,UAE/E;AAAA,gBAAA,CAAA,CAAA;AAAA,cAbF,GAAAoB,KAAKzD,GAef,CACL,GAGA,CACK;AAAA,gBAAEuD,OAAO;AAAA,gBAAkBvD,KAAK;AAAA,gBAAiBC,OAAOnD,WAAWiC;AAAAA,cAAc,GACjF;AAAA,gBAAEwE,OAAO;AAAA,gBAAsBvD,KAAK;AAAA,gBAAoBC,OAAOnD,WAAWmC;AAAAA,cAAiB,GAC3F;AAAA,gBAAEsE,OAAO;AAAA,gBAAqBvD,KAAK;AAAA,gBAAmBC,OAAOnD,WAAWqC;AAAAA,cAAgB,CAAA,EAC5FqE,IAAKC,UACAjI,kCAAAA,KAAA,OAAA;AAAA,gBAAmB4G,WAAU;AAAA,gBACxBC,UAAA,CAAC7G,kCAAA,KAAA,KAAA;AAAA,kBAAE4G,WAAU;AAAA,kBAAuCC,UAAA,CAAKoB,KAAAF,OAAM,GAAA;AAAA,gBAAC,CAAA,GAChE9H,kCAAA,IAACwH,OAAA;AAAA,kBACK9B,MAAK;AAAA,kBACL+B,aAAa,SAASO,KAAKF,KAAK;AAAA,kBAChCtD,OAAOwD,KAAKxD;AAAAA,kBACZkD,UAAWC,OAAMrD,iBAAiB0D,KAAKzD,KAAKoD,EAAEC,OAAOpD,KAAK;AAAA,kBAC1DqD,UAAU,CAAC7D;AAAAA,iBAEjB,GACCA,qBACKhE,kCAAA,IAAC6G,QAAO;AAAA,kBAAAE,SAASA,MAAMzB,aAAa0C,KAAKzD,KAAKyD,KAAKxD,OAAOjE,UAAU,QAAQ;AAAA,kBAAGqG,UAE/E;AAAA,gBAAA,CAAA,CAAA;AAAA,cAbF,GAAAoB,KAAKzD,GAef,CACL,GAGA,CACK;AAAA,gBAAEuD,OAAO;AAAA,gBAAiBvD,KAAK;AAAA,gBAAgBC,OAAOnD,WAAWuC;AAAAA,cAAa,GAC9E;AAAA,gBAAEkE,OAAO;AAAA,gBAAiBvD,KAAK;AAAA,gBAAgBC,OAAOnD,WAAWyC;AAAAA,cAAa,CAAA,EAClFiE,IAAKC,UACAjI,kCAAAA,KAAA,OAAA;AAAA,gBAAmB4G,WAAU;AAAA,gBACxBC,UAAA,CAAC7G,kCAAA,KAAA,KAAA;AAAA,kBAAE4G,WAAU;AAAA,kBAAuCC,UAAA,CAAKoB,KAAAF,OAAM,GAAA;AAAA,gBAAC,CAAA,GAChE9H,kCAAA,IAACwH,OAAA;AAAA,kBACK9B,MAAK;AAAA,kBACL+B,aAAa,SAASO,KAAKF,KAAK;AAAA,kBAChCtD,OAAOwD,KAAKxD;AAAAA,kBACZkD,UAAWC,OAAMrD,iBAAiB0D,KAAKzD,KAAKoD,EAAEC,OAAOpD,KAAK;AAAA,kBAC1DqD,UAAU,CAAC7D;AAAAA,iBACjB,GACCA,qBACKhE,kCAAA,IAAC6G,QAAA;AAAA,kBAAOE,SAASA,MAAMzB,aAAa0C,KAAKzD,KAAKyD,KAAKxD,OAAOjE,UAAU,QAAQ;AAAA,kBACtEsH,UAAUjD,cAAcoD,KAAKzD,GAAG,KAAK,CAACP;AAAAA,kBAGtC0C,SAAS9B,cAAcoD,KAAKzD,GAAG;AAAA,kBAE9BqC,UAAchC,cAAAoD,KAAKzD,GAAG,IAAI,gBAAgB;AAAA,gBAAA,CAEjD,CAAA;AAAA,cAlBF,GAAAyD,KAAKzD,GAoBf,CACL,CAAA;AAAA,YACP,CAAA,CAAA;AAAA,UACN,CAAA,GAGAvE,kCAAA,IAAC,OAAI;AAAA,YAAA2G,WAAU;AAAA,YACRC,UAAA,CACK;AAAA,cAAEkB,OAAO;AAAA,cAAevD,KAAK;AAAA,cAAuBC,OAAOnD,yCAAYE;AAAAA,YAAW,GAClF;AAAA,cAAEuG,OAAO;AAAA,cAAavD,KAAK;AAAA,cAAqBC,OAAOnD,yCAAYI;AAAAA,YAAS,GAC5E;AAAA,cAAEqG,OAAO;AAAA,cAAavD,KAAK;AAAA,cAAuBC,OAAOnD,yCAAYM;AAAAA,YAAW,GAChF;AAAA,cAAEmG,OAAO;AAAA,cAAiBvD,KAAK;AAAA,cAAyBC,OAAOnD,yCAAYQ;AAAAA,YAAa,GACxF;AAAA,cAAEiG,OAAO;AAAA,cAAcvD,KAAK;AAAA,cAA6BC,OAAOnD,yCAAYU;AAAAA,YAAiB,GAC7F;AAAA,cAAE+F,OAAO;AAAA,cAAavD,KAAK;AAAA,cAA4BC,OAAOnD,yCAAYc;AAAAA,YAAgB,GAC1F;AAAA,cAAE2F,OAAO;AAAA,cAAkBvD,KAAK;AAAA,cAAiCC,OAAOnD,yCAAYgB;AAAAA,YAAqB,GACzG;AAAA,cAAEyF,OAAO;AAAA,cAAgBvD,KAAK;AAAA,cAA0BC,OAAOnD,yCAAYkB;AAAAA,YAAc,GACzF;AAAA,cAAEuF,OAAO;AAAA,cAAavD,KAAK;AAAA,cAA8BC,OAAOnD,yCAAYoB;AAAAA,YAAkB,GAC9F;AAAA,cAAEqF,OAAO;AAAA,cAAmBvD,KAAK;AAAA,cAAsBC,OAAOnD,yCAAY0B;AAAAA,YAAU,GACpF;AAAA,cAAE+E,OAAO;AAAA,cAAkBvD,KAAK;AAAA,cAA4BC,OAAOnD,yCAAYY;AAAAA,YAAgB,GAC/F;AAAA,cAAE6F,OAAO;AAAA,cAAiBvD,KAAK;AAAA,cAAyBC,OAAOnD,yCAAYsB;AAAAA,YAAa,GACxF;AAAA,cAAEmF,OAAO;AAAA,cAAavD,KAAK;AAAA,cAAqBC,OAAOnD,yCAAYwB;AAAAA,YAAS,CAAA,EAGhFkF,IAAKC,UACAjI,kCAAAA,KAAA,OAAA;AAAA,cAAmB4G,WAAU;AAAA,cACxBC,UAAA,CAAA5G,kCAAA,IAAC,KAAE;AAAA,gBAAA2G,WAAU;AAAA,gBAAuCC,UAAAoB,KAAKF;AAAAA,cAAM,CAAA,GAC/D9H,kCAAA,IAACsH,QAAA;AAAA,gBACKC,SAAS,CAACS,KAAKxD;AAAAA,gBACfuC,SAASA,MAAMhB,aAAaiC,KAAKzD,GAAG;AAAA,gBACpCsD,UAAUjD,cAAcoD,KAAKzD,GAAG,KAAK,CAACP;AAAAA,gBACrC,GAAGY,cAAcoD,KAAKzD,GAAG,2CAAMiC,eAAc;AAAA,kBAAAC,MAAM;AAAA,kBAAIC,SAAS;AAAA,gBAAM,CAAA;AAAA,cAAA,CAE7E,CAAA;AAAA,YARI,GAAAsB,KAAKzD,GASf,CACL;AAAA,UACP,CAAA,CAAA;AAAA,QACN,CAAA;AAAA,MACN,CAAA,GAKAvE,kCAAA,IAAEqH;QAAY7C,OAAM;AAAA,QACdoC,gDAACqB,gBAAe;AAAA,UAAAC,gBAAgBrH;AAAAA,UAAgBuF;AAAAA,QAA4B,CAAA;AAAA,MAClF,CAAA,CAAA;AAAA,IAoCN,CAAA,CAAA;AAAA,EACN,CAAA;AAEZ;"}