import { r as reactExports, j as jsxRuntimeExports } from "./jsx-runtime-bB2y7OuD.js";
import { F as FaWhatsapp } from "./index-Bx88Z3Oa.js";
import { B as Button } from "./button-ByAXMyvk.js";
import { S as Sheet, a as SheetTrigger, b as SheetContent } from "./sheet-BP4xlyNq.js";
import { u as useLoaderData, L as Link, F as Form } from "./components-D7UvGag_.js";
import { c as useLocation, N as Navigate, O as Outlet } from "./index-DhHTcibu.js";
import { M as Menu, a as Megaphone } from "./menu-jBs_wTVq.js";
import { L as LayoutDashboard, a as LogOut } from "./log-out-C7033MWA.js";
import { c as createLucideIcon } from "./createLucideIcon-uwkRm45G.js";
import { U as Users } from "./users-Bl2kD8ju.js";
import { C as ChevronDown } from "./chevron-down-pCP5jmjX.js";
import { C as ChevronRight } from "./chevron-right-B-tR7Kir.js";
import { T as Truck } from "./truck-ypDO_-A_.js";
import "./index-z_byfFrQ.js";
import "./index-ImHKLo0a.js";
import "./utils-GkgzjW3c.js";
import "./index-DdafHWkt.js";
import "./index-D7VH9Fc8.js";
import "./index-DVTNuYOr.js";
import "./index-dIGjFc0I.js";
import "./index-BXzPK7u0.js";
import "./index-CzZ6EYkN.js";
import "./index-Vp2vNLNM.js";
import "./index-CEHS9zzk.js";
import "./index-QLGF6kQx.js";
import "./x-CCG_WJDF.js";
/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */
const ChartColumn = createLucideIcon("ChartColumn", [
  ["path", { d: "M3 3v16a2 2 0 0 0 2 2h16", key: "c24i48" }],
  ["path", { d: "M18 17V9", key: "2bz60n" }],
  ["path", { d: "M13 17V5", key: "1frdt8" }],
  ["path", { d: "M8 17v-3", key: "17ska0" }]
]);
/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */
const Cog = createLucideIcon("Cog", [
  ["path", { d: "M12 20a8 8 0 1 0 0-16 8 8 0 0 0 0 16Z", key: "sobvz5" }],
  ["path", { d: "M12 14a2 2 0 1 0 0-4 2 2 0 0 0 0 4Z", key: "11i496" }],
  ["path", { d: "M12 2v2", key: "tus03m" }],
  ["path", { d: "M12 22v-2", key: "1osdcq" }],
  ["path", { d: "m17 20.66-1-1.73", key: "eq3orb" }],
  ["path", { d: "M11 10.27 7 3.34", key: "16pf9h" }],
  ["path", { d: "m20.66 17-1.73-1", key: "sg0v6f" }],
  ["path", { d: "m3.34 7 1.73 1", key: "1ulond" }],
  ["path", { d: "M14 12h8", key: "4f43i9" }],
  ["path", { d: "M2 12h2", key: "1t8f8n" }],
  ["path", { d: "m20.66 7-1.73 1", key: "1ow05n" }],
  ["path", { d: "m3.34 17 1.73-1", key: "nuk764" }],
  ["path", { d: "m17 3.34-1 1.73", key: "2wel8s" }],
  ["path", { d: "m11 13.73-4 6.93", key: "794ttg" }]
]);
/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */
const CreditCard = createLucideIcon("CreditCard", [
  ["rect", { width: "20", height: "14", x: "2", y: "5", rx: "2", key: "ynyp8z" }],
  ["line", { x1: "2", x2: "22", y1: "10", y2: "10", key: "1b3vmo" }]
]);
/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */
const Gift = createLucideIcon("Gift", [
  ["rect", { x: "3", y: "8", width: "18", height: "4", rx: "1", key: "bkv52" }],
  ["path", { d: "M12 8v13", key: "1c76mn" }],
  ["path", { d: "M19 12v7a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2v-7", key: "6wjy6b" }],
  [
    "path",
    {
      d: "M7.5 8a2.5 2.5 0 0 1 0-5A4.8 8 0 0 1 12 8a4.8 8 0 0 1 4.5-5 2.5 2.5 0 0 1 0 5",
      key: "1ihvrl"
    }
  ]
]);
/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */
const Image = createLucideIcon("Image", [
  ["rect", { width: "18", height: "18", x: "3", y: "3", rx: "2", ry: "2", key: "1m3agn" }],
  ["circle", { cx: "9", cy: "9", r: "2", key: "af1f0g" }],
  ["path", { d: "m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21", key: "1xmnt7" }]
]);
/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */
const Settings = createLucideIcon("Settings", [
  [
    "path",
    {
      d: "M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",
      key: "1qme2f"
    }
  ],
  ["circle", { cx: "12", cy: "12", r: "3", key: "1v7zrd" }]
]);
/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */
const ShoppingCart = createLucideIcon("ShoppingCart", [
  ["circle", { cx: "8", cy: "21", r: "1", key: "jimo8o" }],
  ["circle", { cx: "19", cy: "21", r: "1", key: "13723u" }],
  [
    "path",
    {
      d: "M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12",
      key: "9zh506"
    }
  ]
]);
/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */
const UtensilsCrossed = createLucideIcon("UtensilsCrossed", [
  ["path", { d: "m16 2-2.3 2.3a3 3 0 0 0 0 4.2l1.8 1.8a3 3 0 0 0 4.2 0L22 8", key: "n7qcjb" }],
  [
    "path",
    { d: "M15 15 3.3 3.3a4.2 4.2 0 0 0 0 6l7.3 7.3c.7.7 2 .7 2.8 0L15 15Zm0 0 7 7", key: "d0u48b" }
  ],
  ["path", { d: "m2.1 21.8 6.4-6.3", key: "yn04lh" }],
  ["path", { d: "m19 5-7 7", key: "194lzd" }]
]);
/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */
const Zap = createLucideIcon("Zap", [
  [
    "path",
    {
      d: "M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",
      key: "1xq2db"
    }
  ]
]);
function SellerSetting() {
  const loaderData = useLoaderData();
  const location = useLocation();
  const [isOpen, setIsOpen] = reactExports.useState(false);
  const [shouldRedirect, setShouldRedirect] = reactExports.useState(false);
  const [sidebarWidth, setSidebarWidth] = reactExports.useState(256);
  const [isResizing, setIsResizing] = reactExports.useState(false);
  const [isSettingsExpanded, setIsSettingsExpanded] = reactExports.useState(false);
  const activeSection = location.pathname.split("/")[2];
  reactExports.useEffect(() => {
    if (location.pathname === "/sellerSetting") {
      setShouldRedirect(true);
    } else {
      setShouldRedirect(false);
    }
  }, [location.pathname]);
  const handleMouseDown = () => {
    setIsResizing(true);
  };
  const handleMouseMove = (e) => {
    if (!isResizing) return;
    const newWidth = Math.max(200, Math.min(400, e.clientX));
    setSidebarWidth(newWidth);
  };
  const handleMouseUp = () => {
    setIsResizing(false);
  };
  reactExports.useEffect(() => {
    if (isResizing) {
      document.addEventListener("mousemove", handleMouseMove);
      document.addEventListener("mouseup", handleMouseUp);
      document.body.style.cursor = "col-resize";
      document.body.style.userSelect = "none";
    } else {
      document.removeEventListener("mousemove", handleMouseMove);
      document.removeEventListener("mouseup", handleMouseUp);
      document.body.style.cursor = "";
      document.body.style.userSelect = "";
    }
    return () => {
      document.removeEventListener("mousemove", handleMouseMove);
      document.removeEventListener("mouseup", handleMouseUp);
      document.body.style.cursor = "";
      document.body.style.userSelect = "";
    };
  }, [isResizing]);
  if (shouldRedirect) {
    return /* @__PURE__ */ jsxRuntimeExports.jsx(Navigate, {
      to: "/sellerSetting/dashboard",
      replace: true
    });
  }
  const NavContent = () => {
    var _a, _b, _c, _d, _e, _f;
    return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
      className: "flex flex-col h-full bg-white border-r border-border",
      children: [/* @__PURE__ */ jsxRuntimeExports.jsx("div", {
        className: "p-6 border-b border-border",
        children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
          className: "flex items-center space-x-4",
          children: [/* @__PURE__ */ jsxRuntimeExports.jsx("div", {
            className: "h-10 w-10 rounded-full bg-primary flex items-center justify-center text-xl font-bold text-primary-foreground",
            children: ((_d = (_c = (_b = (_a = loaderData == null ? void 0 : loaderData.userDetails) == null ? void 0 : _a.userDetails) == null ? void 0 : _b.businessName) == null ? void 0 : _c[0]) == null ? void 0 : _d.toUpperCase()) || "B"
          }), /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
            className: "text-foreground text-xl font-semibold truncate",
            children: ((_f = (_e = loaderData == null ? void 0 : loaderData.userDetails) == null ? void 0 : _e.userDetails) == null ? void 0 : _f.businessName) || "Business Name"
          })]
        })
      }), /* @__PURE__ */ jsxRuntimeExports.jsx("nav", {
        className: "flex-1 p-4 space-y-2",
        children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
          className: "space-y-1",
          children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Link, {
            to: "/sellerSetting/dashboard",
            onClick: () => setIsOpen(false),
            children: /* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
              variant: activeSection === "dashboard" ? "secondary" : "ghost",
              className: "w-full justify-start",
              children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                className: "flex items-center mr-3",
                children: [/* @__PURE__ */ jsxRuntimeExports.jsx(LayoutDashboard, {
                  className: "w-5 h-5"
                }), /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                  className: "ml-2",
                  children: "Dashboard"
                })]
              })
            })
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(Link, {
            to: "/sellerSetting/orders",
            onClick: () => setIsOpen(false),
            children: /* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
              variant: activeSection === "orders" ? "secondary" : "ghost",
              className: "w-full justify-start",
              children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                className: "flex items-center mr-3",
                children: [/* @__PURE__ */ jsxRuntimeExports.jsx(ShoppingCart, {
                  className: "w-5 h-5"
                }), /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                  className: "ml-2",
                  children: "Orders"
                })]
              })
            })
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(Link, {
            to: "/sellerSetting/menu",
            onClick: () => setIsOpen(false),
            children: /* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
              variant: activeSection === "menu" ? "secondary" : "ghost",
              className: "w-full justify-start",
              children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                className: "flex items-center mr-3",
                children: [/* @__PURE__ */ jsxRuntimeExports.jsx(UtensilsCrossed, {
                  className: "w-5 h-5"
                }), /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                  className: "ml-2",
                  children: "Menu"
                })]
              })
            })
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(Link, {
            to: "/sellerSetting/campaigns",
            onClick: () => setIsOpen(false),
            children: /* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
              variant: activeSection === "campaigns" ? "secondary" : "ghost",
              className: "w-full justify-start",
              children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                className: "flex items-center mr-3",
                children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Megaphone, {
                  className: "w-5 h-5"
                }), /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                  className: "ml-2",
                  children: "Campaigns"
                })]
              })
            })
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(Link, {
            to: "/sellerSetting/customers",
            onClick: () => setIsOpen(false),
            children: /* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
              variant: activeSection === "customers" ? "secondary" : "ghost",
              className: "w-full justify-start",
              children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                className: "flex items-center mr-3",
                children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Users, {
                  className: "w-5 h-5"
                }), /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                  className: "ml-2",
                  children: "Customers"
                })]
              })
            })
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(Link, {
            to: "/sellerSetting/analytics",
            onClick: () => setIsOpen(false),
            children: /* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
              variant: activeSection === "analytics" ? "secondary" : "ghost",
              className: "w-full justify-start",
              children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                className: "flex items-center mr-3",
                children: [/* @__PURE__ */ jsxRuntimeExports.jsx(ChartColumn, {
                  className: "w-5 h-5"
                }), /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                  className: "ml-2",
                  children: "Analytics"
                })]
              })
            })
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(Link, {
            to: "/sellerSetting/integrations",
            onClick: () => setIsOpen(false),
            children: /* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
              variant: activeSection === "integrations" ? "secondary" : "ghost",
              className: "w-full justify-start",
              children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                className: "flex items-center mr-3",
                children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Zap, {
                  className: "w-5 h-5"
                }), /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                  className: "ml-2",
                  children: "Integrations"
                })]
              })
            })
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(Link, {
            to: "/sellerSetting/payouts",
            onClick: () => setIsOpen(false),
            children: /* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
              variant: activeSection === "payouts" ? "secondary" : "ghost",
              className: "w-full justify-start",
              children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                className: "flex items-center mr-3",
                children: [/* @__PURE__ */ jsxRuntimeExports.jsx(CreditCard, {
                  className: "w-5 h-5"
                }), /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                  className: "ml-2",
                  children: "Payouts"
                })]
              })
            })
          }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
            children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
              variant: activeSection === "settings" ? "secondary" : "ghost",
              className: "w-full justify-start",
              onClick: () => setIsSettingsExpanded(!isSettingsExpanded),
              children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                className: "flex items-center justify-between w-full",
                children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                  className: "flex items-center mr-3",
                  children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Settings, {
                    className: "w-5 h-5"
                  }), /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                    className: "ml-2",
                    children: "Settings"
                  })]
                }), isSettingsExpanded ? /* @__PURE__ */ jsxRuntimeExports.jsx(ChevronDown, {
                  className: "w-4 h-4 text-muted-foreground"
                }) : /* @__PURE__ */ jsxRuntimeExports.jsx(ChevronRight, {
                  className: "w-4 h-4 text-muted-foreground"
                })]
              })
            }), isSettingsExpanded && /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
              className: "ml-6 space-y-1 mt-1",
              children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Link, {
                to: "/sellerSetting/settings",
                onClick: () => setIsOpen(false),
                children: /* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
                  variant: activeSection === "settings" ? "secondary" : "ghost",
                  className: "w-full justify-start text-sm",
                  children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                    className: "flex items-center mr-3",
                    children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Cog, {
                      className: "w-4 h-4"
                    }), /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                      className: "ml-2",
                      children: "General Settings"
                    })]
                  })
                })
              }), /* @__PURE__ */ jsxRuntimeExports.jsx(Link, {
                to: "/sellerSetting/deliveryConfig",
                onClick: () => setIsOpen(false),
                children: /* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
                  variant: activeSection === "deliveryConfig" ? "secondary" : "ghost",
                  className: "w-full justify-start text-sm",
                  children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                    className: "flex items-center mr-3",
                    children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Truck, {
                      className: "w-4 h-4"
                    }), /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                      className: "ml-2",
                      children: "Delivery Config"
                    })]
                  })
                })
              }), /* @__PURE__ */ jsxRuntimeExports.jsx(Link, {
                to: "/sellerSetting/nbanners",
                onClick: () => setIsOpen(false),
                children: /* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
                  variant: activeSection === "nbanners" ? "secondary" : "ghost",
                  className: "w-full justify-start text-sm",
                  children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                    className: "flex items-center mr-3",
                    children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Image, {
                      className: "w-4 h-4"
                    }), /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                      className: "ml-2",
                      children: "Banners & Sequence"
                    })]
                  })
                })
              }), /* @__PURE__ */ jsxRuntimeExports.jsx(Link, {
                to: "/sellerSetting/whatsappprofile",
                onClick: () => setIsOpen(false),
                children: /* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
                  variant: activeSection === "whatsappprofile" ? "secondary" : "ghost",
                  className: "w-full justify-start text-sm",
                  children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                    className: "flex items-center mr-3",
                    children: [/* @__PURE__ */ jsxRuntimeExports.jsx(FaWhatsapp, {
                      className: "",
                      size: 16
                    }), /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                      className: "ml-2",
                      children: "WhatsApp Settings"
                    })]
                  })
                })
              }), /* @__PURE__ */ jsxRuntimeExports.jsx(Link, {
                to: "/sellerSetting/coupons",
                onClick: () => setIsOpen(false),
                children: /* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
                  variant: activeSection === "coupons" ? "secondary" : "ghost",
                  className: "w-full justify-start text-sm",
                  children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                    className: "flex items-center mr-3",
                    children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Gift, {
                      className: "w-4 h-4"
                    }), /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                      className: "ml-2",
                      children: "Coupons"
                    })]
                  })
                })
              })]
            })]
          })]
        })
      }), /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
        className: "p-4 border-t border-border",
        children: /* @__PURE__ */ jsxRuntimeExports.jsx(Form, {
          method: "post",
          children: /* @__PURE__ */ jsxRuntimeExports.jsxs(Button, {
            type: "submit",
            variant: "outline",
            className: "w-full justify-start text-destructive hover:text-destructive hover:bg-destructive/10 border-destructive/20 hover:border-destructive/30",
            children: [/* @__PURE__ */ jsxRuntimeExports.jsx(LogOut, {
              className: "w-4 h-4 mr-3"
            }), /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
              children: "Logout"
            })]
          })
        })
      }), /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
        className: "px-4 pb-2 pt-0",
        children: /* @__PURE__ */ jsxRuntimeExports.jsxs("p", {
          className: "text-xs text-muted-foreground text-center",
          children: ["© ", (/* @__PURE__ */ new Date()).getFullYear(), " mNET"]
        })
      })]
    });
  };
  return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
    className: "h-screen bg-white flex overflow-hidden",
    children: [/* @__PURE__ */ jsxRuntimeExports.jsx("div", {
      className: "md:hidden fixed top-4 left-4 z-50",
      children: /* @__PURE__ */ jsxRuntimeExports.jsxs(Sheet, {
        open: isOpen,
        onOpenChange: setIsOpen,
        children: [/* @__PURE__ */ jsxRuntimeExports.jsx(SheetTrigger, {
          asChild: true,
          children: /* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
            variant: "outline",
            size: "icon",
            className: "bg-white shadow-md",
            children: /* @__PURE__ */ jsxRuntimeExports.jsx(Menu, {
              className: "h-4 w-4"
            })
          })
        }), /* @__PURE__ */ jsxRuntimeExports.jsx(SheetContent, {
          side: "left",
          className: "w-64 p-0",
          children: /* @__PURE__ */ jsxRuntimeExports.jsx(NavContent, {})
        })]
      })
    }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
      className: "hidden md:flex relative h-screen",
      style: {
        width: `${sidebarWidth}px`
      },
      children: [/* @__PURE__ */ jsxRuntimeExports.jsx("div", {
        className: "w-full h-full overflow-y-auto",
        children: /* @__PURE__ */ jsxRuntimeExports.jsx(NavContent, {})
      }), /* @__PURE__ */ jsxRuntimeExports.jsx("button", {
        type: "button",
        className: "absolute right-0 top-0 w-1 h-full bg-transparent hover:bg-gray-300 cursor-col-resize transition-colors duration-200 group border-none outline-none focus:ring-2 focus:ring-primary",
        onMouseDown: handleMouseDown,
        "aria-label": "Resize sidebar",
        onKeyDown: (e) => {
          if (e.key === "ArrowLeft") {
            setSidebarWidth((prev) => Math.max(200, prev - 10));
          } else if (e.key === "ArrowRight") {
            setSidebarWidth((prev) => Math.min(400, prev + 10));
          }
        },
        children: /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
          className: "w-full h-full relative",
          children: /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
            className: "absolute right-0 top-1/2 transform -translate-y-1/2 w-1 h-16 bg-gray-400 opacity-0 group-hover:opacity-100 transition-opacity duration-200 rounded-full"
          })
        })
      })]
    }), /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
      className: "flex-1 min-w-0 h-screen overflow-y-auto",
      children: /* @__PURE__ */ jsxRuntimeExports.jsx("main", {
        className: "h-full pt-16 md:pt-0 px-4 md:px-6",
        children: /* @__PURE__ */ jsxRuntimeExports.jsx(Outlet, {
          context: {
            setIsOpen
          }
        })
      })
    })]
  });
}
export {
  SellerSetting as default
};
//# sourceMappingURL=sellerSetting-D3LLnHmd.js.map
