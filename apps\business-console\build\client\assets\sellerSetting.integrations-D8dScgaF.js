import { j as jsxRuntimeExports } from "./jsx-runtime-bB2y7OuD.js";
import { C as Card, b as <PERSON><PERSON>eader, c as <PERSON><PERSON><PERSON><PERSON>, d as CardDescription, a as CardContent } from "./card-BJQMSLe_.js";
import "./utils-GkgzjW3c.js";
function Integrations() {
  return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
    className: "p-6",
    children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
      className: "mb-6",
      children: [/* @__PURE__ */ jsxRuntimeExports.jsx("h1", {
        className: "text-3xl font-bold text-gray-900",
        children: "Integrations"
      }), /* @__PURE__ */ jsxRuntimeExports.jsx("p", {
        className: "text-gray-600 mt-2",
        children: "Connect with third-party services"
      })]
    }), /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
      className: "flex items-center justify-center min-h-[400px]",
      children: /* @__PURE__ */ jsxRuntimeExports.jsxs(Card, {
        className: "w-full max-w-md",
        children: [/* @__PURE__ */ jsxRuntimeExports.jsxs(CardHeader, {
          className: "text-center",
          children: [/* @__PURE__ */ jsxRuntimeExports.jsx("div", {
            className: "mx-auto mb-4 w-16 h-16 bg-gradient-to-r from-yellow-500 to-orange-600 rounded-full flex items-center justify-center",
            children: /* @__PURE__ */ jsxRuntimeExports.jsx("svg", {
              className: "w-8 h-8 text-white",
              fill: "none",
              stroke: "currentColor",
              viewBox: "0 0 24 24",
              children: /* @__PURE__ */ jsxRuntimeExports.jsx("path", {
                strokeLinecap: "round",
                strokeLinejoin: "round",
                strokeWidth: 2,
                d: "M13 10V3L4 14h7v7l9-11h-7z"
              })
            })
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(CardTitle, {
            className: "text-2xl",
            children: "Integrations Coming Soon"
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(CardDescription, {
            className: "text-lg",
            children: "We're working on seamless integrations with your favorite services."
          })]
        }), /* @__PURE__ */ jsxRuntimeExports.jsxs(CardContent, {
          className: "text-center",
          children: [/* @__PURE__ */ jsxRuntimeExports.jsx("p", {
            className: "text-gray-500 mb-4",
            children: "Connect payment gateways, delivery partners, and analytics tools in one place."
          }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
            className: "inline-flex items-center px-4 py-2 bg-gradient-to-r from-yellow-500 to-orange-600 text-black rounded-lg",
            children: [/* @__PURE__ */ jsxRuntimeExports.jsx("span", {
              className: "mr-2",
              children: "🔌"
            }), "Launching Soon"]
          })]
        })]
      })
    })]
  });
}
export {
  Integrations as default
};
//# sourceMappingURL=sellerSetting.integrations-D8dScgaF.js.map
