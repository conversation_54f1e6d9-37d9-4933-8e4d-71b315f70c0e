import { r as reactExports, j as jsxRuntimeExports } from "./jsx-runtime-bB2y7OuD.js";
import { D as Dialog, d as DialogTrigger, a as DialogContent } from "./dialog-BqKosxNq.js";
import { B as Button } from "./button-ByAXMyvk.js";
import { S as SearchableCategories, A as AddMasterCategory } from "./addMasterCategory-TRqpdyp0.js";
import { P as Pencil } from "./pencil-C1goHmP8.js";
import { C as Card, a as CardContent } from "./card-BJQMSLe_.js";
import { I as Input } from "./input-3v87qohQ.js";
import { P as Pagination, a as PaginationContent, b as PaginationItem, c as PaginationPrevious, d as PaginationLink, e as PaginationNext } from "./pagination-DzgbTb6G.js";
import { T as Table, a as TableHeader, b as TableRow, c as TableHead, d as TableBody, e as TableCell } from "./table-CAxdJZsY.js";
import { T as Tabs, a as TabsList, b as TabsTrigger, c as TabsContent } from "./tabs-CfSdyzWr.js";
import { u as useDebounce } from "./useDebounce-BXbH_IFZ.js";
import { a as useNavigate } from "./index-DhHTcibu.js";
import { u as useLoaderData, a as useFetcher } from "./components-D7UvGag_.js";
import { C as ChevronRight } from "./chevron-right-B-tR7Kir.js";
import "./index-DdafHWkt.js";
import "./index-D7VH9Fc8.js";
import "./index-z_byfFrQ.js";
import "./index-DVTNuYOr.js";
import "./index-dIGjFc0I.js";
import "./index-BXzPK7u0.js";
import "./index-CzZ6EYkN.js";
import "./index-Vp2vNLNM.js";
import "./index-CEHS9zzk.js";
import "./index-QLGF6kQx.js";
import "./utils-GkgzjW3c.js";
import "./x-CCG_WJDF.js";
import "./createLucideIcon-uwkRm45G.js";
import "./index-ImHKLo0a.js";
import "./label-cSASrwzW.js";
import "./select-BFGSXKcr.js";
import "./index-IXOTxK3N.js";
import "./index-qCtcpOcW.js";
import "./index-BTdCMChR.js";
import "./index-DscYByPT.js";
import "./index-CkL5tk39.js";
import "./index-CpKiYcZd.js";
import "./index-C88PRvfd.js";
import "./chevron-down-pCP5jmjX.js";
import "./check-_dbWxIzT.js";
import "./radio-group-ChzooXbR.js";
import "./index-CG37gmC0.js";
import "./chevron-left-CLqBlTg1.js";
function AddCategoryItem({
  categoryDetails,
  buttonName,
  handleSubmit,
  mode,
  level,
  itemDetails
}) {
  const [dialogOpen, setDialogOpen] = reactExports.useState(false);
  const transformToCategoryItem = (parentCategories) => {
    return parentCategories.map((category) => ({
      value: category.id.toString(),
      label: category.name,
      numericId: category.id
    }));
  };
  const [updatedCategory, setUpdatedCategory] = reactExports.useState(
    () => mode === "Edit" && categoryDetails ? transformToCategoryItem(categoryDetails) : []
  );
  function handleClose() {
    setDialogOpen(false);
  }
  const handleSubmitBtn = () => {
    const categoriesWithDetails = updatedCategory.map((category) => ({
      id: category.numericId,
      disabled: false,
      name: category.label,
      picture: (itemDetails == null ? void 0 : itemDetails.picture) || "",
      picturex: "",
      picturexx: "",
      level: level || 0,
      totalItems: 1,
      parentCategories: []
    }));
    const requestBody = {
      id: (itemDetails == null ? void 0 : itemDetails.id) || 0,
      disabled: false,
      defaultUnit: (itemDetails == null ? void 0 : itemDetails.defaultUnit) || "",
      name: (itemDetails == null ? void 0 : itemDetails.name) || "Default Item Name",
      picture: (itemDetails == null ? void 0 : itemDetails.picture) || "",
      nameInKannada: (itemDetails == null ? void 0 : itemDetails.nameInKannada) || "",
      nameInTelugu: (itemDetails == null ? void 0 : itemDetails.nameInTelugu) || "",
      nameInTamil: (itemDetails == null ? void 0 : itemDetails.nameInTamil) || "",
      nameInMalayalam: (itemDetails == null ? void 0 : itemDetails.nameInMalayalam) || "",
      nameInHindi: (itemDetails == null ? void 0 : itemDetails.nameInHindi) || "",
      nameInAssame: (itemDetails == null ? void 0 : itemDetails.nameInAssame) || "",
      nameInGujarati: (itemDetails == null ? void 0 : itemDetails.nameInGujarati) || "",
      nameInMarathi: (itemDetails == null ? void 0 : itemDetails.nameInMarathi) || "",
      nameInBangla: (itemDetails == null ? void 0 : itemDetails.nameInBangla) || "",
      defaultWeightFactor: (itemDetails == null ? void 0 : itemDetails.defaultWeightFactor) || 0,
      gstHsnCode: (itemDetails == null ? void 0 : itemDetails.gstHsnCode) || "",
      gstRate: (itemDetails == null ? void 0 : itemDetails.gstRate) || 0,
      source: (itemDetails == null ? void 0 : itemDetails.source) || "",
      sourceKey: (itemDetails == null ? void 0 : itemDetails.sourceKey) || "",
      productId: (itemDetails == null ? void 0 : itemDetails.productId) || "",
      brandName: (itemDetails == null ? void 0 : itemDetails.brandName) || "",
      packaging: (itemDetails == null ? void 0 : itemDetails.packaging) || "",
      mrp: (itemDetails == null ? void 0 : itemDetails.mrp) || 0,
      ownerBId: (itemDetails == null ? void 0 : itemDetails.ownerBId) || 0,
      b2b: (itemDetails == null ? void 0 : itemDetails.b2b) || false,
      b2c: (itemDetails == null ? void 0 : itemDetails.b2c) || false,
      groupId: (itemDetails == null ? void 0 : itemDetails.groupId) || "",
      groupSeq: (itemDetails == null ? void 0 : itemDetails.groupSeq) || 0,
      searchTag: (itemDetails == null ? void 0 : itemDetails.searchTag) || "",
      categories: categoriesWithDetails
    };
    handleSubmit((itemDetails == null ? void 0 : itemDetails.id) ?? 0, updatedCategory, requestBody, handleClose);
  };
  return /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "container mx-auto   ", children: /* @__PURE__ */ jsxRuntimeExports.jsxs(Dialog, { open: dialogOpen, onOpenChange: (isOpen) => {
    setDialogOpen(isOpen);
    if (!isOpen) {
      setDialogOpen(false);
    }
  }, children: [
    /* @__PURE__ */ jsxRuntimeExports.jsx(DialogTrigger, { asChild: true, className: "flex ", children: /* @__PURE__ */ jsxRuntimeExports.jsx(
      Pencil,
      {
        size: 16,
        className: "cursor-pointer"
      }
    ) }),
    /* @__PURE__ */ jsxRuntimeExports.jsxs(
      DialogContent,
      {
        className: "w-full sm:w-[400px] md:w-[600px] max-h-[90vh] overflow-y-auto absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2",
        children: [
          /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "grid gap-4", children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "space-y-2", children: /* @__PURE__ */ jsxRuntimeExports.jsx("h4", { className: "font-medium leading-none text-center md:text-left", children: "Edit Categories For Item" }) }),
            /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "grid grid-cols-1 md:grid-cols-2 gap-4", children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "space-y-2", children: [
              /* @__PURE__ */ jsxRuntimeExports.jsx(
                "img",
                {
                  src: (itemDetails == null ? void 0 : itemDetails.picture) && itemDetails.picture.includes(",") ? itemDetails.picture.split(",")[0] : itemDetails == null ? void 0 : itemDetails.picture,
                  alt: "",
                  className: "w-12 h-12 object-cover"
                }
              ),
              /* @__PURE__ */ jsxRuntimeExports.jsxs("p", { className: "text-md font-bold", children: [
                "Item Name : ",
                itemDetails == null ? void 0 : itemDetails.name,
                "  "
              ] })
            ] }) })
          ] }),
          /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "col-span-full", children: /* @__PURE__ */ jsxRuntimeExports.jsx(
            SearchableCategories,
            {
              label: "Assigned Categories",
              apiUrl: "/home/<USER>",
              selectedCategories: updatedCategory || [],
              onCategoryAdd: (categoryId, categoryName) => {
                setUpdatedCategory((prevUpdatedCategory) => [
                  ...prevUpdatedCategory || [],
                  { numericId: categoryId, value: categoryName, label: "" }
                ]);
              },
              onCategoryRemove: (categoryId) => {
                setUpdatedCategory(
                  (prevUpdatedCategory) => (prevUpdatedCategory || []).filter(
                    (cat) => cat.numericId !== categoryId
                  )
                );
              },
              required: true,
              level: 1
            }
          ) }),
          /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "flex flex-col md:flex-row justify-end gap-2 mt-5", children: /* @__PURE__ */ jsxRuntimeExports.jsx(
            Button,
            {
              size: "sm",
              className: "w-full md:w-auto",
              onClick: () => handleSubmitBtn(),
              children: "Submit"
            }
          ) })
        ]
      }
    )
  ] }) });
}
function SelectedCategory() {
  var _a, _b;
  const navigate = useNavigate();
  const {
    categoryList,
    items,
    selectedId,
    name,
    categoryDetail,
    parentCategory
  } = useLoaderData();
  const [activeTab, setActiveTab] = reactExports.useState(categoryDetail.level === 1 ? "Items" : "parentCategories");
  const [searchTerm, setSearchTerm] = reactExports.useState("");
  const [currentPage, setCurrentPage] = reactExports.useState(0);
  const fetcher = useFetcher();
  const debouncedSearchTerm = useDebounce(searchTerm, 500);
  reactExports.useEffect(() => {
    if (debouncedSearchTerm.length >= 3) {
      navigate(`/home/<USER>
    } else if (debouncedSearchTerm === "") {
      navigate(`/home/<USER>
    }
  }, [debouncedSearchTerm, navigate, selectedId, name, activeTab, currentPage]);
  const handleTabChange = (newTab) => {
    setActiveTab(newTab);
    navigate(`/home/<USER>
  };
  const handlePageChange = (newPage) => {
    setCurrentPage(newPage);
    navigate(`/home/<USER>
  };
  const handleSubmit = (selectedLevel, categoryName, sequence, uploadedImageUrl, parentId, closeDialog, mode, icId) => {
    const formData = new FormData();
    formData.append("categoryName", categoryName);
    formData.append("categoryLevel", selectedLevel.toString());
    formData.append("sequence", sequence.toString());
    formData.append("imageUrl", uploadedImageUrl);
    formData.append("parentId", JSON.stringify(parentId));
    formData.append("icId", icId);
    formData.append("mode", mode);
    formData.append("_intent", "_createCategory");
    fetcher.submit(formData, {
      method: "POST"
    });
    closeDialog();
  };
  const handleEdit = (itemId, parentId, requestBody, closeDialog) => {
    const formData = new FormData();
    formData.append("parentId", JSON.stringify(parentId));
    formData.append("requestBody", JSON.stringify(requestBody));
    formData.append("itemId", itemId);
    formData.append("_intent", "edit");
    fetcher.submit(formData, {
      method: "PUT"
    });
    closeDialog();
  };
  return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
    className: "container mx-auto p-6",
    children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
      className: "flex items-center gap-2 mb-6",
      children: [/* @__PURE__ */ jsxRuntimeExports.jsx("span", {
        className: "font-semibold cursor-pointer",
        onClick: () => navigate("/home/<USER>"),
        children: " Categories"
      }), /* @__PURE__ */ jsxRuntimeExports.jsx(ChevronRight, {}), /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
        className: "font-semibold",
        children: name
      })]
    }), /* @__PURE__ */ jsxRuntimeExports.jsx(Card, {
      children: /* @__PURE__ */ jsxRuntimeExports.jsxs(CardContent, {
        className: "flex flex-col md:flex-row justify-between my-3 gap-4",
        children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
          className: "flex flex-col md:flex-row gap-5",
          children: [/* @__PURE__ */ jsxRuntimeExports.jsx("img", {
            src: categoryDetail == null ? void 0 : categoryDetail.picture,
            alt: "ItemImage",
            className: "h-10 w-10 self-center md:self-auto mt-4"
          }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
            children: [(categoryDetail == null ? void 0 : categoryDetail.name) && /* @__PURE__ */ jsxRuntimeExports.jsxs("p", {
              className: " text-md flex gap-1",
              children: ["Cat.Name: ", /* @__PURE__ */ jsxRuntimeExports.jsx("p", {
                className: "font-bold text-md",
                children: categoryDetail == null ? void 0 : categoryDetail.name
              })]
            }), (categoryDetail == null ? void 0 : categoryDetail.level) && /* @__PURE__ */ jsxRuntimeExports.jsxs("p", {
              className: "text-md flex gap-1",
              children: ["Cat.Level: ", /* @__PURE__ */ jsxRuntimeExports.jsx("p", {
                className: "font-bold text-md",
                children: (categoryDetail == null ? void 0 : categoryDetail.level) === 1 ? "L1" : categoryDetail.level === 2 ? "L2" : categoryDetail.level === 3 ? "L3" : "-"
              })]
            }), ((_a = categoryDetail == null ? void 0 : categoryDetail.parentCategories) == null ? void 0 : _a.length) >= 0 && /* @__PURE__ */ jsxRuntimeExports.jsxs("p", {
              className: "text-md flex gap-1",
              children: ["Parent Categories:", /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
                className: "flex flex-col gap-1",
                children: ((_b = categoryDetail == null ? void 0 : categoryDetail.parentCategories) == null ? void 0 : _b.length) > 0 ? categoryDetail.parentCategories.map((parent, index) => /* @__PURE__ */ jsxRuntimeExports.jsxs("span", {
                  className: "font-bold text-md",
                  children: [" ", index + 1, ".", parent.name]
                }, index)) : "-"
              })]
            })]
          })]
        }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
          className: "flex flex-col gap-3 items-start md:items-end mr-0 md:mr-20",
          children: [/* @__PURE__ */ jsxRuntimeExports.jsx("p", {
            className: "font-semibold text-md flex gap-2 items-center",
            children: /* @__PURE__ */ jsxRuntimeExports.jsx(AddMasterCategory, {
              buttonName: "Edit",
              categoryDetails: categoryDetail,
              handleSubmit,
              mode: "Edit"
            })
          }), (categoryDetail == null ? void 0 : categoryDetail.totalItems) > 0 && /* @__PURE__ */ jsxRuntimeExports.jsxs("p", {
            className: "text-md flex gap-1",
            children: ["Total Items:", /* @__PURE__ */ jsxRuntimeExports.jsxs("p", {
              className: "font-semibold text-md",
              children: [categoryDetail == null ? void 0 : categoryDetail.totalItems, " "]
            })]
          })]
        })]
      })
    }), /* @__PURE__ */ jsxRuntimeExports.jsxs(Tabs, {
      value: activeTab,
      onValueChange: handleTabChange,
      className: "my-5",
      children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
        className: "flex justify-between",
        children: [/* @__PURE__ */ jsxRuntimeExports.jsxs(TabsList, {
          children: [categoryDetail.level === 1 && /* @__PURE__ */ jsxRuntimeExports.jsx(TabsTrigger, {
            value: "Items",
            children: "Items"
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(TabsTrigger, {
            value: "parentCategories",
            children: "Parent Categories"
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(TabsTrigger, {
            value: "childCategories",
            children: "Child Category"
          })]
        }), activeTab !== "parentCategories" && /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
          className: "flex justify-center gap-10",
          children: /* @__PURE__ */ jsxRuntimeExports.jsx(Input, {
            placeholder: "Search ",
            value: searchTerm,
            onChange: (e) => setSearchTerm(e.target.value)
          })
        })]
      }), /* @__PURE__ */ jsxRuntimeExports.jsx(TabsContent, {
        value: "parentCategories",
        children: /* @__PURE__ */ jsxRuntimeExports.jsxs(Table, {
          children: [/* @__PURE__ */ jsxRuntimeExports.jsx(TableHeader, {
            children: /* @__PURE__ */ jsxRuntimeExports.jsxs(TableRow, {
              children: [/* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
                className: "font-bold",
                children: "Category Id"
              }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
                className: "cursor-pointer font-bold ",
                children: "Category Name"
              }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
                className: "font-bold ",
                children: "Category Level"
              }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
                className: "font-bold ",
                children: "Parent Category"
              }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
                className: "font-bold ",
                children: "Actions"
              })]
            })
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableBody, {
            children: (parentCategory == null ? void 0 : parentCategory.length) === 0 ? /* @__PURE__ */ jsxRuntimeExports.jsx(TableRow, {
              children: /* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
                className: "py-4 text-center align-middle",
                colSpan: 100,
                children: "No Categories Found"
              })
            }) : parentCategory == null ? void 0 : parentCategory.map((y) => {
              var _a2;
              return /* @__PURE__ */ jsxRuntimeExports.jsxs(TableRow, {
                children: [/* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
                  children: y == null ? void 0 : y.id
                }), /* @__PURE__ */ jsxRuntimeExports.jsxs(TableCell, {
                  className: "flex gap-2",
                  children: [/* @__PURE__ */ jsxRuntimeExports.jsx("img", {
                    src: y == null ? void 0 : y.picture,
                    alt: "",
                    className: "w-12 h-12 object-cover"
                  }), y == null ? void 0 : y.name]
                }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
                  children: (y == null ? void 0 : y.level) === 1 ? "L1" : (y == null ? void 0 : y.level) === 2 ? "L2" : (y == null ? void 0 : y.level) === 3 ? "L3" : "-"
                }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
                  children: /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
                    className: "flex flex-col gap-1",
                    children: ((_a2 = y == null ? void 0 : y.parentCategories) == null ? void 0 : _a2.length) > 0 ? y == null ? void 0 : y.parentCategories.map((parent) => /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                      children: parent.name
                    }, parent.id)) : "-"
                  })
                }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
                  children: /* @__PURE__ */ jsxRuntimeExports.jsx(AddMasterCategory, {
                    buttonName: "Edit",
                    categoryDetails: y,
                    handleSubmit,
                    mode: "Edit"
                  })
                })]
              }, y.id);
            })
          })]
        })
      }), /* @__PURE__ */ jsxRuntimeExports.jsx(TabsContent, {
        value: "childCategories",
        children: /* @__PURE__ */ jsxRuntimeExports.jsxs(Table, {
          children: [/* @__PURE__ */ jsxRuntimeExports.jsx(TableHeader, {
            children: /* @__PURE__ */ jsxRuntimeExports.jsxs(TableRow, {
              children: [/* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
                className: "font-bold",
                children: "Category Id"
              }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
                className: "cursor-pointer font-bold ",
                children: "Category Name"
              }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
                className: "font-bold ",
                children: "Category Level"
              }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
                className: "font-bold ",
                children: "Parent Category"
              }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
                className: "font-bold ",
                children: "Actions"
              })]
            })
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableBody, {
            children: (categoryList == null ? void 0 : categoryList.length) === 0 ? /* @__PURE__ */ jsxRuntimeExports.jsx(TableRow, {
              children: /* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
                className: "py-4 text-center align-middle",
                colSpan: 100,
                children: "No Categories Found"
              })
            }) : categoryList == null ? void 0 : categoryList.map((x) => {
              var _a2;
              return /* @__PURE__ */ jsxRuntimeExports.jsxs(TableRow, {
                children: [/* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
                  children: x == null ? void 0 : x.id
                }), /* @__PURE__ */ jsxRuntimeExports.jsxs(TableCell, {
                  className: "flex gap-2",
                  children: [" ", /* @__PURE__ */ jsxRuntimeExports.jsx("img", {
                    src: x == null ? void 0 : x.picture,
                    alt: "",
                    className: "w-12 h-12 object-cover"
                  }), " ", x == null ? void 0 : x.name]
                }), /* @__PURE__ */ jsxRuntimeExports.jsxs(TableCell, {
                  children: ["   ", (x == null ? void 0 : x.level) === 1 ? "L1" : (x == null ? void 0 : x.level) === 2 ? "L2" : (x == null ? void 0 : x.level) === 3 ? "L3" : "-"]
                }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
                  children: /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
                    className: " flex flex-col gap-1",
                    children: ((_a2 = x == null ? void 0 : x.parentCategories) == null ? void 0 : _a2.length) > 0 ? x == null ? void 0 : x.parentCategories.map((parent) => /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                      children: parent.name
                    })) : "-"
                  })
                }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
                  children: /* @__PURE__ */ jsxRuntimeExports.jsx(AddMasterCategory, {
                    buttonName: "Edit",
                    categoryDetails: x,
                    handleSubmit,
                    mode: "Edit"
                  })
                })]
              }, x.id);
            })
          })]
        })
      }), /* @__PURE__ */ jsxRuntimeExports.jsx(TabsContent, {
        value: "Items",
        children: /* @__PURE__ */ jsxRuntimeExports.jsxs(Table, {
          children: [/* @__PURE__ */ jsxRuntimeExports.jsx(TableHeader, {
            children: /* @__PURE__ */ jsxRuntimeExports.jsxs(TableRow, {
              children: [/* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
                className: "font-bold",
                children: "Item Id"
              }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
                className: "font-bold",
                children: "Item Name"
              }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
                className: "font-bold",
                children: "Item Details"
              }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
                className: "font-bold",
                children: "Status"
              }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
                className: "font-bold",
                children: "Categories"
              })]
            })
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableBody, {
            children: items.length === 0 ? /* @__PURE__ */ jsxRuntimeExports.jsx(TableRow, {
              children: /* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
                className: "py-4 text-center align-middle",
                colSpan: 100,
                children: "No Items Found"
              })
            }) : items == null ? void 0 : items.map((x) => {
              var _a2;
              return /* @__PURE__ */ jsxRuntimeExports.jsxs(TableRow, {
                children: [/* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
                  children: x == null ? void 0 : x.id
                }), /* @__PURE__ */ jsxRuntimeExports.jsxs(TableCell, {
                  className: "items-center",
                  children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                    className: "flex items-center gap-1",
                    children: [/* @__PURE__ */ jsxRuntimeExports.jsx("img", {
                      src: (x == null ? void 0 : x.picture) && x.picture.includes(",") ? x.picture.split(",")[0] : x.picture,
                      alt: "",
                      className: "w-12 h-12 object-cover"
                    }), "  ", x == null ? void 0 : x.name]
                  }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                    className: "text-xs mt-2",
                    children: [/* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                      className: "font-medium",
                      children: "Packaging:"
                    }), " ", x.packaging || "-"]
                  })]
                }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
                  children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                    className: "space-y-1",
                    children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                      className: "text-xs",
                      children: [/* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                        className: "font-medium",
                        children: "Min Qty:"
                      }), " ", x.minimumOrderQty || "-"]
                    }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                      className: "text-xs",
                      children: [/* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                        className: "font-medium",
                        children: "Increment Qty:"
                      }), " ", x.incrementOrderQty || "-"]
                    }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                      className: "text-xs",
                      children: [/* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                        className: "font-medium",
                        children: "MRP:"
                      }), " ", x.mrp || "-"]
                    }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                      className: "text-xs",
                      children: [/* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                        className: "font-medium",
                        children: "Source:"
                      }), " ", x.source || "-"]
                    })]
                  })
                }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
                  children: /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                    className: `px-2 py-1 rounded-full text-xs ${x.disabled ? "bg-red-100 text-red-800" : "bg-green-200 text-green-800"}`,
                    children: (x == null ? void 0 : x.disabled) === true ? "Disabled" : "Active"
                  })
                }), /* @__PURE__ */ jsxRuntimeExports.jsxs(TableCell, {
                  className: "flex gap-2 align-middle",
                  children: ["  ", /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
                    className: "flex flex-col ",
                    children: ((_a2 = x == null ? void 0 : x.categories) == null ? void 0 : _a2.length) > 0 ? x == null ? void 0 : x.categories.map((parent) => /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                      children: parent.name
                    })) : "-"
                  }), /* @__PURE__ */ jsxRuntimeExports.jsx(AddCategoryItem, {
                    buttonName: "Edit",
                    handleSubmit: handleEdit,
                    itemDetails: x,
                    categoryDetails: x.categories,
                    level: categoryDetail.level,
                    mode: "Edit"
                  })]
                })]
              }, x.id);
            })
          })]
        })
      }), activeTab !== "parentCategories" && /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
        className: "flex justify-between items-center mt-6 overflow-hidden ",
        children: /* @__PURE__ */ jsxRuntimeExports.jsx(Pagination, {
          children: /* @__PURE__ */ jsxRuntimeExports.jsxs(PaginationContent, {
            children: [currentPage > 0 && /* @__PURE__ */ jsxRuntimeExports.jsx(PaginationItem, {
              children: /* @__PURE__ */ jsxRuntimeExports.jsx(PaginationPrevious, {
                onClick: () => handlePageChange(currentPage - 1),
                className: "cursor-pointer"
              })
            }), /* @__PURE__ */ jsxRuntimeExports.jsx(PaginationItem, {
              children: /* @__PURE__ */ jsxRuntimeExports.jsx(PaginationLink, {
                className: "cursor-pointer",
                children: currentPage + 1
              })
            }), /* @__PURE__ */ jsxRuntimeExports.jsx(PaginationItem, {
              children: /* @__PURE__ */ jsxRuntimeExports.jsx(PaginationNext, {
                onClick: () => handlePageChange(currentPage + 1),
                className: "cursor-pointer"
              })
            })]
          })
        })
      })]
    })]
  });
}
export {
  SelectedCategory as default
};
//# sourceMappingURL=home.selectedCategory-CDgHx28o.js.map
