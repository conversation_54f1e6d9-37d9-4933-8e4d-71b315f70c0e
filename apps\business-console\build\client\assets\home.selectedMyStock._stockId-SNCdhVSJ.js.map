{"version": 3, "file": "home.selectedMyStock._stockId-SNCdhVSJ.js", "sources": ["../../../app/routes/home.selectedMyStock.$stockId.tsx"], "sourcesContent": ["import { json } from \"@remix-run/node\";\r\nimport { useLoaderData, useNavigate, useParams, useLocation, Outlet } from \"@remix-run/react\";\r\nimport { ArrowLeft, Plus, Printer } from \"lucide-react\";\r\nimport { Badge } from \"~/components/ui/badge\";\r\nimport { But<PERSON> } from \"~/components/ui/button\";\r\nimport { Card, CardContent } from \"~/components/ui/card\";\r\nimport { Layout } from \"~/root\";\r\nimport { withAuth, withResponse } from \"~/utils/auth-utils\";\r\nimport { getSelectedMyStock } from \"~/services/stockservices/mystockservice\";\r\nimport { ItemStock } from \"~/types/api/businessConsoleService/ItemStock\";\r\n\r\n\r\ninterface LoaderData {\r\n  stock: ItemStock;\r\n  }\r\n\r\n  export const loader = withAuth(async ({ request, user, params }) => {\r\n    const url = new URL(request.url);\r\n    const SellerId= parseInt(url.searchParams.get(\"sellerId\") as string||\"0\")\r\n    const isSeller = SellerId==null || isNaN(SellerId) || SellerId === 0;\r\n  const stockId = (params ?? {}).stockId; // <-- get from params safely\r\n  const numericStockId = stockId ? Number(stockId) : undefined;\r\n  \r\n\r\n  try {\r\n    const response = await getSelectedMyStock(isSeller, numericStockId, SellerId, request);\r\n    return withResponse({\r\n      stock: response.data,\r\n    },response?.headers);\r\n  } catch (error) {\r\n    console.error(\"Error in loader:\", error);\r\n    // Return a JSON-based error shape\r\n    throw new Response(\"failed to get sellers\", { status: 500 })\r\n  }\r\n});\r\n\r\nexport default function ItemDetail() {\r\n  const { stock } = useLoaderData<LoaderData>();\r\n  const navigate = useNavigate()\r\n  const handleBackClick = () => {\r\n    navigate(-1);\r\n  };\r\n  return (\r\n    <Layout>\r\n      <div className=\"flex flex-col gap-6\">\r\n        <div className=\"flex items-center justify-between no-print\">\r\n          <Button variant=\"outline\" size=\"sm\" onClick={handleBackClick}>\r\n            <ArrowLeft className=\"mr-2 h-4 w-4\" /> Back\r\n            <h1>MyStock</h1>\r\n           </Button>\r\n           <div>\r\n\r\n           </div>\r\n        </div>\r\n        <div className=\"printable-area\">\r\n          <Card className=\"mb-6\">\r\n            <CardContent className=\"p-6\">\r\n              <div className=\"flex flex-col md:flex-row justify-between gap-4\">\r\n                <div>\r\n                  <h1 className=\"text-2xl font-bold\">{stock.itemName}</h1>\r\n                  <p className=\"text-muted-foreground\">Distributer: {stock.distributor}</p>\r\n                </div>\r\n                <div className=\"flex flex-col gap-2\">\r\n                  <div className=\"flex items-center gap-2\">\r\n                \r\n                    <Badge variant={stock.active ? \"default\" : \"secondary\"} className=\"font-normal\">\r\n                      {stock.active ? \"Active\" : \"Inactive\"}\r\n                    </Badge>\r\n                  </div>\r\n                  <p className=\"text-sm\">\r\n                    <span className=\"font-medium\">My Price:</span>₹ {stock.pricePerUnit.toFixed(2)}\r\n                  </p>\r\n                  {stock.maxAvailableQty > 0 && (\r\n                    <p className=\"text-sm text-blue-600\">\r\n                      <span className=\"font-medium\">MaxAvail Qty:</span> {stock.maxAvailableQty} \r\n                    </p>\r\n                  )}\r\n                </div>\r\n              </div>\r\n            </CardContent>\r\n          </Card>\r\n\r\n          </div>\r\n          </div>\r\n          <Outlet/>\r\n          \r\n\r\n    </Layout>\r\n  );\r\n}"], "names": ["ItemDetail", "stock", "useLoaderData", "navigate", "useNavigate", "handleBackClick", "Layout", "children", "jsxs", "className", "<PERSON><PERSON>", "variant", "size", "onClick", "jsx", "ArrowLeft", "Card", "<PERSON><PERSON><PERSON><PERSON>", "itemName", "distributor", "Badge", "active", "pricePerUnit", "toFixed", "maxAvailableQty", "Outlet"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAoCA,SAAwBA,aAAa;AAC7B,QAAA;AAAA,IAAEC;AAAAA,EAAM,IAAIC,cAA0B;AAC5C,QAAMC,WAAWC,YAAY;AAC7B,QAAMC,kBAAkBA,MAAM;AAC5BF,aAAS,EAAE;AAAA,EACb;AACA,gDACGG,QACC;AAAA,IAAAC,UAAA,CAACC,kCAAA,KAAA,OAAA;AAAA,MAAIC,WAAU;AAAA,MACbF,UAAA,CAACC,kCAAA,KAAA,OAAA;AAAA,QAAIC,WAAU;AAAA,QACbF,UAAA,CAAAC,kCAAA,KAACE;UAAOC,SAAQ;AAAA,UAAUC,MAAK;AAAA,UAAKC,SAASR;AAAAA,UAC3CE,UAAA,CAACO,kCAAA,IAAAC,WAAA;AAAA,YAAUN,WAAU;AAAA,WAAe,GAAE,SACtCK,kCAAA,IAAC;YAAGP,UAAO;AAAA,UAAA,CAAA,CAAA;AAAA,SACZ,yCACC,OAED,EAAA,CAAA;AAAA,MACH,CAAA,GACCO,kCAAA,IAAA,OAAA;AAAA,QAAIL,WAAU;AAAA,QACbF,gDAACS,MAAK;AAAA,UAAAP,WAAU;AAAA,UACdF,UAAAO,kCAAA,IAACG;YAAYR,WAAU;AAAA,YACrBF,UAACC,kCAAA,KAAA,OAAA;AAAA,cAAIC,WAAU;AAAA,cACbF,UAAA,CAAAC,kCAAA,KAAC,OACC;AAAA,gBAAAD,UAAA,CAAAO,kCAAA,IAAC,MAAG;AAAA,kBAAAL,WAAU;AAAA,kBAAsBF,UAAAN,MAAMiB;AAAAA,gBAAS,CAAA,GACnDV,kCAAA,KAAC,KAAE;AAAA,kBAAAC,WAAU;AAAA,kBAAwBF,UAAA,CAAA,iBAAcN,MAAMkB,WAAA;AAAA,gBAAY,CAAA,CAAA;AAAA,cACvE,CAAA,GACAX,kCAAA,KAAC,OAAI;AAAA,gBAAAC,WAAU;AAAA,gBACbF,UAAA,CAAAO,kCAAA,IAAC;kBAAIL,WAAU;AAAA,kBAEbF,UAACO,kCAAA,IAAAM,OAAA;AAAA,oBAAMT,SAASV,MAAMoB,SAAS,YAAY;AAAA,oBAAaZ,WAAU;AAAA,oBAC/DF,UAAAN,MAAMoB,SAAS,WAAW;AAAA,kBAC7B,CAAA;AAAA,gBACF,CAAA,GACAb,kCAAA,KAAC,KAAE;AAAA,kBAAAC,WAAU;AAAA,kBACXF,UAAA,CAACO,kCAAA,IAAA,QAAA;AAAA,oBAAKL,WAAU;AAAA,oBAAcF,UAAS;AAAA,kBAAA,CAAA,GAAO,MAAGN,MAAMqB,aAAaC,QAAQ,CAAC,CAAA;AAAA,gBAC/E,CAAA,GACCtB,MAAMuB,kBAAkB,KACtBhB,kCAAAA,KAAA,KAAA;AAAA,kBAAEC,WAAU;AAAA,kBACXF,UAAA,CAACO,kCAAA,IAAA,QAAA;AAAA,oBAAKL,WAAU;AAAA,oBAAcF,UAAa;AAAA,kBAAA,CAAA,GAAO,KAAEN,MAAMuB,eAAA;AAAA,gBAC5D,CAAA,CAAA;AAAA,cAEJ,CAAA,CAAA;AAAA,YACF,CAAA;AAAA,UACF,CAAA;AAAA,QACF,CAAA;AAAA,MAEA,CAAA,CAAA;AAAA,KACA,yCACCC,QAAM,EAAA,CAAA;AAAA,EAGb,CAAA;AAEJ;"}