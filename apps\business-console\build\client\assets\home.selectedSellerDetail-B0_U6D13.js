import { r as reactExports, j as jsxRuntimeExports } from "./jsx-runtime-bB2y7OuD.js";
import { S as SpinnerLoader } from "./SpinnerLoader-CAZRmIJX.js";
import { B as Button } from "./button-ByAXMyvk.js";
import { I as Input } from "./input-3v87qohQ.js";
import { T as Table, a as TableHeader, b as TableRow, c as TableHead, d as TableBody, e as TableCell } from "./table-CAxdJZsY.js";
import { S as Switch } from "./switch-DQ_qW6ch.js";
import { T as Tabs, a as TabsList, b as TabsTrigger, c as TabsContent } from "./tabs-CfSdyzWr.js";
import { u as useToast } from "./ToastProvider-rciVe3-g.js";
import { a as useNavigate } from "./index-DhHTcibu.js";
import { u as useLoaderData, a as useFetcher } from "./components-D7UvGag_.js";
import { A as ArrowLeft } from "./arrow-left-D_Ztdrp5.js";
import "./index-z_byfFrQ.js";
import "./index-ImHKLo0a.js";
import "./utils-GkgzjW3c.js";
import "./index-D7VH9Fc8.js";
import "./index-dIGjFc0I.js";
import "./index-CpKiYcZd.js";
import "./index-CkL5tk39.js";
import "./index-CzZ6EYkN.js";
import "./index-Vp2vNLNM.js";
import "./index-CG37gmC0.js";
import "./index-qCtcpOcW.js";
import "./index-DVTNuYOr.js";
import "./index-BTdCMChR.js";
import "./index-QLGF6kQx.js";
import "./createLucideIcon-uwkRm45G.js";
function SellerAreaList({ sellerAreaData, updateToggle }) {
  const [toggledAreas, setToggledAreas] = reactExports.useState(/* @__PURE__ */ new Set());
  const handleSwitch = (status, areaId) => {
    const updatedToggles = new Set(toggledAreas);
    if (status) {
      updatedToggles.add(areaId);
    } else {
      updatedToggles.delete(areaId);
    }
    setToggledAreas(updatedToggles);
    updateToggle(status, areaId);
  };
  return /* @__PURE__ */ jsxRuntimeExports.jsxs(Table, { className: "w-full min-w-[600px] border-collapse", children: [
    /* @__PURE__ */ jsxRuntimeExports.jsx(TableHeader, { children: /* @__PURE__ */ jsxRuntimeExports.jsxs(TableRow, { className: "bg-gray-100", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, { className: "font-bold text-gray-800", children: "Area Name" }),
      /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, { className: "font-bold  text-gray-800", children: "District" }),
      /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, { className: "font-bold  text-gray-800", children: "State" }),
      /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, { className: "font-bold  text-gray-800", children: "Status" })
    ] }) }),
    /* @__PURE__ */ jsxRuntimeExports.jsx(TableBody, { children: Array.isArray(sellerAreaData) && sellerAreaData.map((x) => {
      var _a, _b, _c, _d;
      const isToggled = toggledAreas.has(x == null ? void 0 : x.sellerAreaId);
      return /* @__PURE__ */ jsxRuntimeExports.jsxs(TableRow, { className: "bg-gray-50 font-medium", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, { children: (_a = x == null ? void 0 : x.area) == null ? void 0 : _a.name }),
        /* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, { children: (_b = x == null ? void 0 : x.area) == null ? void 0 : _b.district }),
        /* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, { children: (_c = x == null ? void 0 : x.area) == null ? void 0 : _c.state }),
        /* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, { children: /* @__PURE__ */ jsxRuntimeExports.jsx(
          Switch,
          {
            checked: isToggled || (x == null ? void 0 : x.disabled),
            onClick: () => handleSwitch(!isToggled, x == null ? void 0 : x.sellerAreaId)
          }
        ) })
      ] }, (_d = x == null ? void 0 : x.area) == null ? void 0 : _d.id);
    }) })
  ] });
}
function SelectedSellerDetail() {
  const {
    showToast
  } = useToast();
  const navigate = useNavigate();
  const {
    data,
    sellerId,
    name,
    isResponse
  } = useLoaderData();
  const [selectedNetworkItemCategory, setSelectedNetworkItemCategory] = reactExports.useState([]);
  const [sellerAreaList, setSellerAreaList] = reactExports.useState([]);
  const [activeTab, setActiveTab] = reactExports.useState("config");
  const [selectedAttribute, setSelectedAttribute] = reactExports.useState("");
  const [loader2, setLoader] = reactExports.useState(false);
  const [sellerData, setSellerData] = reactExports.useState({
    name: data == null ? void 0 : data.name,
    autoAccept: data == null ? void 0 : data.auto_accept,
    autoPack: data == null ? void 0 : data.auto_pack,
    autoPickup: data == null ? void 0 : data.auto_pickup,
    autoDispatch: data == null ? void 0 : data.auto_dispatch,
    strikeoffEnabled: data == null ? void 0 : data.strikeoff_enabled,
    favItemsEnabled: data == null ? void 0 : data.fav_items_enabled,
    itemPickEnabled: data == null ? void 0 : data.item_pick_enabled,
    contractPriceEnabled: data == null ? void 0 : data.contract_price_enabled,
    approxPricing: data == null ? void 0 : data.approx_pricing,
    isPayLaterEnabled: data == null ? void 0 : data.is_pay_later_enabled,
    autoActivate: data == null ? void 0 : data.auto_activate,
    allowCoD: data.allow_cod,
    wa_enable: data.wa_enable,
    minimumRequiredBalance: data.mininum_required_balance,
    minimumOrderQty: data.minimum_order_qty,
    minimumOrderValue: data.minimum_order_value,
    categoryLevel: data.category_level,
    bookingCloseTime: data.booking_close_time,
    bookingOpenTime: data.booking_open_time,
    dispatchTime: data.dispatch_time,
    deliveryTime: data.delivery_time
  });
  const [updateAttributes2, setUpdateAttributes] = reactExports.useState(isResponse);
  const [selectedField, setSelectedField] = reactExports.useState("");
  const [searchTerm, setSearchTerm] = reactExports.useState("");
  const updateSellerData = (key, value) => {
    setSellerData((prev) => ({
      ...prev,
      [key]: value
    }));
  };
  const fetcher = useFetcher();
  const [loadingFields, setLoadingFields] = reactExports.useState({});
  const handleTabChange = (newTab) => {
    setActiveTab(newTab);
    if (newTab === "netWorkItemDetails") {
      const formData = new FormData();
      formData.append("intent", "netWorkItemDetails");
      formData.append("sellerId", sellerId.toString());
      fetcher.submit(formData, {
        method: "POST"
      });
    }
    if (newTab === "sellerAreas") {
      setUpdateAttributes(false);
      const formData = new FormData();
      formData.append("intent", "sellerAreas");
      formData.append("sellerId", sellerId.toString());
      fetcher.submit(formData, {
        method: "POST"
      });
    }
  };
  const handleUpdate = async (attributeType, val, id, type) => {
    console.log(loader2, "99999999999999");
    setLoadingFields((prev) => ({
      ...prev,
      [attributeType]: true
    }));
    const formData = new FormData();
    formData.append("updateType", type);
    formData.append("sellerId", id.toString());
    formData.append("attribute", attributeType);
    formData.append("value", val.toString());
    try {
      await fetcher.submit(formData, {
        method: "POST"
      });
      showToast(`${attributeType.replace(/([A-Z])/g, " $1")} updated successfully`, "success");
      updateSellerData(attributeType, val);
    } catch (error2) {
      showToast(`Failed to update ${attributeType.replace(/([A-Z])/g, " $1")}`, "error");
    } finally {
      setLoadingFields((prev) => ({
        ...prev,
        [attributeType]: false
      }));
    }
  };
  const handleSwitch = async (field) => {
    setLoadingFields((prev) => ({
      ...prev,
      [field]: true
    }));
    const currentValue = sellerData[field];
    const newValue = !currentValue;
    try {
      const formData = new FormData();
      formData.append("updateType", "seller");
      formData.append("sellerId", sellerId.toString());
      formData.append("attribute", field);
      formData.append("value", newValue.toString());
      await fetcher.submit(formData, {
        method: "POST"
      });
      showToast(`${field.replace(/([A-Z])/g, " $1")} updated successfully`, "success");
      updateSellerData(field, newValue);
      setSelectedField(field);
    } catch (error2) {
      showToast(`Failed to update ${field.replace(/([A-Z])/g, " $1")}`, "error");
    } finally {
      setLoadingFields((prev) => ({
        ...prev,
        [field]: false
      }));
    }
  };
  reactExports.useEffect(() => {
    if (fetcher.data) {
      if (activeTab === "netWorkItemDetails" && fetcher.data.data) {
        setSelectedNetworkItemCategory(fetcher.data.data);
      } else if (activeTab === "sellerAreas" && fetcher.data.data) {
        setSellerAreaList(fetcher.data.data);
      }
    }
  }, [fetcher.data, activeTab]);
  const updateToggle = (status, areaId) => {
    const formData = new FormData();
    formData.append("sellerId", sellerId);
    formData.append("areaStatus", status);
    formData.append("areaId", areaId);
    formData.append("intent", "updateArea");
    formData.append("name", name);
    fetcher.submit(formData, {
      method: "POST"
    });
  };
  {
    fetcher.state !== "idle" && /* @__PURE__ */ jsxRuntimeExports.jsx(SpinnerLoader, {
      size: 8,
      loading: true
    });
  }
  return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
    className: "container mx-auto p-6",
    children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
      className: "flex items-center gap-2 mb-6",
      children: [/* @__PURE__ */ jsxRuntimeExports.jsxs(Button, {
        variant: "ghost",
        size: "sm",
        onClick: () => navigate(-1),
        children: [/* @__PURE__ */ jsxRuntimeExports.jsx(ArrowLeft, {
          className: "h-4 w-4 mr-2"
        }), "Back to Sellers"]
      }), /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
        className: "text-muted-foreground",
        children: "/"
      }), /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
        className: "font-semibold",
        children: name
      })]
    }), /* @__PURE__ */ jsxRuntimeExports.jsxs(Tabs, {
      value: activeTab,
      onValueChange: handleTabChange,
      className: "mb-6",
      children: [/* @__PURE__ */ jsxRuntimeExports.jsxs(TabsList, {
        children: [/* @__PURE__ */ jsxRuntimeExports.jsx(TabsTrigger, {
          value: "config",
          children: "Config"
        }), /* @__PURE__ */ jsxRuntimeExports.jsx(TabsTrigger, {
          value: "sellerAreas",
          children: "Seller Areas"
        })]
      }), /* @__PURE__ */ jsxRuntimeExports.jsx(TabsContent, {
        value: "config",
        children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
          className: "flex flex-col md:flex-row gap-6",
          children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
            className: "w-full md:w-1/2 bg-white p-6 border rounded-lg shadow-md space-y-6",
            children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
              className: "flex justify-between items-center",
              children: [/* @__PURE__ */ jsxRuntimeExports.jsx("p", {
                className: "text-lg font-semibold text-gray-700",
                children: "Update Details"
              }), /* @__PURE__ */ jsxRuntimeExports.jsx(Switch, {
                checked: updateAttributes2,
                onClick: () => setUpdateAttributes(!updateAttributes2)
              })]
            }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
              className: "space-y-4",
              children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                className: "flex items-center space-x-4",
                children: [/* @__PURE__ */ jsxRuntimeExports.jsx("p", {
                  className: "text-lg font-semibold text-gray-700",
                  children: "ID:"
                }), /* @__PURE__ */ jsxRuntimeExports.jsx("p", {
                  className: "text-lg text-gray-900",
                  children: data.id
                })]
              }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                className: "flex items-center space-x-4",
                children: [/* @__PURE__ */ jsxRuntimeExports.jsx("p", {
                  className: "text-lg font-semibold text-gray-700",
                  children: "Name:"
                }), /* @__PURE__ */ jsxRuntimeExports.jsx(Input, {
                  placeholder: "Enter Name",
                  value: sellerData == null ? void 0 : sellerData.name,
                  onChange: (e) => updateSellerData("name", e.target.value),
                  disabled: !updateAttributes2
                }), updateAttributes2 && /* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
                  onClick: () => handleUpdate("name", sellerData == null ? void 0 : sellerData.name, sellerId, "seller"),
                  children: "Update"
                })]
              }), [{
                label: "Minimum Required Balance",
                key: "minimumRequiredBalance",
                value: sellerData.minimumRequiredBalance
              }, {
                label: "Minimum Order Quantity",
                key: "minimumOrderQty",
                value: sellerData.minimumOrderQty
              }, {
                label: "Minimum Order Value",
                key: "minimumOrderValue",
                value: sellerData.minimumOrderValue
              }, {
                label: "Platform Fee (Fixed):",
                key: "platformFee",
                value: sellerData.minimumOrderValue
              }, {
                label: "Platform Fee (%)",
                key: "platformFeePerc",
                value: sellerData.minimumOrderValue
              }, {
                label: "Platform Fee per Kg:",
                key: "platformFeePkg",
                value: sellerData.minimumOrderValue
              }].map((item) => /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                className: "flex items-center space-x-4",
                children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("p", {
                  className: "text-lg font-semibold text-gray-700",
                  children: [item.label, ":"]
                }), /* @__PURE__ */ jsxRuntimeExports.jsx(Input, {
                  type: "number",
                  placeholder: `Enter ${item.label}`,
                  value: item.value,
                  onChange: (e) => updateSellerData(item.key, e.target.value),
                  disabled: !updateAttributes2
                }), updateAttributes2 && /* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
                  onClick: () => handleUpdate(item.key, item.value, sellerId, "seller"),
                  children: "Update"
                })]
              }, item.key)), [{
                label: "Category Level",
                key: "categoryLevel",
                value: sellerData.categoryLevel
              }, {
                label: "Booking Close Time",
                key: "bookingCloseTime",
                value: sellerData.bookingCloseTime
              }, {
                label: "Booking Open Time",
                key: "bookingOpenTime",
                value: sellerData.bookingOpenTime
              }].map((item) => /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                className: "flex items-center space-x-4",
                children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("p", {
                  className: "text-lg font-semibold text-gray-700",
                  children: [item.label, ":"]
                }), /* @__PURE__ */ jsxRuntimeExports.jsx(Input, {
                  type: "number",
                  placeholder: `Enter ${item.label}`,
                  value: item.value,
                  onChange: (e) => updateSellerData(item.key, e.target.value),
                  disabled: !updateAttributes2
                }), updateAttributes2 && /* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
                  onClick: () => handleUpdate(item.key, item.value, sellerId, "seller"),
                  children: "Update"
                })]
              }, item.key)), [{
                label: "Dispatch Time",
                key: "dispatchTime",
                value: sellerData.dispatchTime
              }, {
                label: "Delivery Time",
                key: "deliveryTime",
                value: sellerData.deliveryTime
              }].map((item) => /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                className: "flex items-center space-x-4",
                children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("p", {
                  className: "text-lg font-semibold text-gray-700",
                  children: [item.label, ":"]
                }), /* @__PURE__ */ jsxRuntimeExports.jsx(Input, {
                  type: "text",
                  placeholder: `Enter ${item.label}`,
                  value: item.value,
                  onChange: (e) => updateSellerData(item.key, e.target.value),
                  disabled: !updateAttributes2
                }), updateAttributes2 && /* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
                  onClick: () => handleUpdate(item.key, item.value, sellerId, "seller"),
                  disabled: loadingFields[item.key] || !updateAttributes2,
                  loading: loadingFields[item.key],
                  children: loadingFields[item.key] ? "Updating..." : "Update"
                })]
              }, item.key))]
            })]
          }), /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
            className: "w-full md:w-1/2 bg-white p-6 border rounded-lg shadow-md space-y-4",
            children: [{
              label: "Auto Accept",
              key: "autoAccept",
              value: sellerData == null ? void 0 : sellerData.autoAccept
            }, {
              label: "Auto Pack",
              key: "autoPack",
              value: sellerData == null ? void 0 : sellerData.autoPack
            }, {
              label: "Auto Pick",
              key: "autoPickup",
              value: sellerData == null ? void 0 : sellerData.autoPickup
            }, {
              label: "Auto Dispatch",
              key: "autoDispatch",
              value: sellerData == null ? void 0 : sellerData.autoDispatch
            }, {
              label: "Strike Off",
              key: "strikeoffEnabled",
              value: sellerData == null ? void 0 : sellerData.strikeoffEnabled
            }, {
              label: "Item Pick",
              key: "itemPickEnabled",
              value: sellerData == null ? void 0 : sellerData.itemPickEnabled
            }, {
              label: "Contract Price",
              key: "contractPriceEnabled",
              value: sellerData == null ? void 0 : sellerData.contractPriceEnabled
            }, {
              label: "Approx Price",
              key: "approxPricing",
              value: sellerData == null ? void 0 : sellerData.approxPricing
            }, {
              label: "Pay Later",
              key: "isPayLaterEnabled",
              value: sellerData == null ? void 0 : sellerData.isPayLaterEnabled
            }, {
              label: "WhatsApp Enable",
              key: "wa_enable",
              value: sellerData == null ? void 0 : sellerData.wa_enable
            }, {
              label: "Favorite Items",
              key: "favItemsEnabled",
              value: sellerData == null ? void 0 : sellerData.favItemsEnabled
            }, {
              label: "Auto Activate",
              key: "autoActivate",
              value: sellerData == null ? void 0 : sellerData.autoActivate
            }, {
              label: "Allow CoD",
              key: "allowCoD",
              value: sellerData == null ? void 0 : sellerData.allowCoD
            }].map((item) => /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
              className: "flex justify-between items-center",
              children: [/* @__PURE__ */ jsxRuntimeExports.jsx("p", {
                className: "text-lg font-semibold text-gray-700",
                children: item.label
              }), /* @__PURE__ */ jsxRuntimeExports.jsx(Switch, {
                checked: !item.value,
                onClick: () => handleSwitch(item.key),
                disabled: loadingFields[item.key] || !updateAttributes2,
                ...loadingFields[item.key] && /* @__PURE__ */ jsxRuntimeExports.jsx(SpinnerLoader, {
                  size: 10,
                  loading: true
                })
              })]
            }, item.key))
          })]
        })
      }), /* @__PURE__ */ jsxRuntimeExports.jsx(TabsContent, {
        value: "sellerAreas",
        children: /* @__PURE__ */ jsxRuntimeExports.jsx(SellerAreaList, {
          sellerAreaData: sellerAreaList,
          updateToggle
        })
      })]
    })]
  });
}
export {
  SelectedSellerDetail as default
};
//# sourceMappingURL=home.selectedSellerDetail-B0_U6D13.js.map
