import { r as reactExports, j as jsxRuntimeExports } from "./jsx-runtime-bB2y7OuD.js";
import { P as Pagination, a as PaginationContent, b as PaginationItem, c as PaginationPrevious, d as PaginationLink, e as PaginationNext } from "./pagination-DzgbTb6G.js";
import { R as ResponsiveTable } from "./responsiveTable-CMOWL3Wl.js";
import { I as Input } from "./input-3v87qohQ.js";
import { S as Switch } from "./switch-DQ_qW6ch.js";
import { S as SpinnerLoader } from "./SpinnerLoader-CAZRmIJX.js";
import { a as useNavigate } from "./index-DhHTcibu.js";
import { u as useLoaderData, a as useFetcher } from "./components-D7UvGag_.js";
import { S as Save } from "./save-xzNIILKr.js";
import { X } from "./x-CCG_WJDF.js";
import { P as Pencil } from "./pencil-C1goHmP8.js";
import "./utils-GkgzjW3c.js";
import "./button-ByAXMyvk.js";
import "./index-z_byfFrQ.js";
import "./index-ImHKLo0a.js";
import "./chevron-left-CLqBlTg1.js";
import "./createLucideIcon-uwkRm45G.js";
import "./chevron-right-B-tR7Kir.js";
import "./index-D7VH9Fc8.js";
import "./index-dIGjFc0I.js";
import "./index-CpKiYcZd.js";
import "./index-CkL5tk39.js";
import "./index-CzZ6EYkN.js";
import "./index-Vp2vNLNM.js";
function SearchBar({ onSearch, onCancel, isLoading }) {
  const [query, setQuery] = reactExports.useState("");
  reactExports.useEffect(() => {
    const handler = setTimeout(() => {
      if (query.trim() !== "" && query.length >= 3) {
        onSearch(query);
      }
    }, 500);
    return () => clearTimeout(handler);
  }, [query]);
  return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "relative w-full max-w-md mx-auto", children: [
    /* @__PURE__ */ jsxRuntimeExports.jsx(
      "input",
      {
        type: "text",
        placeholder: "Search...",
        value: query,
        onChange: (e) => setQuery(e.target.value),
        className: "w-full p-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
      }
    ),
    query && /* @__PURE__ */ jsxRuntimeExports.jsx(
      "button",
      {
        onClick: () => {
          setQuery("");
          onCancel();
        },
        className: "absolute right-3 top-2 text-gray-600 hover:text-red-500",
        children: "✕"
      }
    ),
    isLoading && /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "absolute right-10 top-2 animate-spin", children: "⏳" })
  ] });
}
function BuyerManagement() {
  const navigate = useNavigate();
  const {
    buyerData
  } = useLoaderData();
  const [currentPage, setCurrentPage] = reactExports.useState(0);
  const itemsPerPage = 50;
  const [searchTerm, setSearchTerm] = reactExports.useState("");
  const handlePageChange = (newPage) => {
    setCurrentPage(newPage);
    navigate(`?search=${searchTerm}&pageNo=${newPage}&pageSize=${itemsPerPage}`);
  };
  const BuyerManagementHeader = ["BuyerID", "BuyerName", "MobileNumber", "Address", "Lat/Lan", "W.Balance", "Status"];
  const fetcher = useFetcher();
  const isLoading = fetcher.state !== "idle";
  const handleSearch = async (query) => {
    setSearchTerm(query);
    if (query.length >= 3) {
      navigate(`?search=${query}&pageNo=${0}&pageSize=${itemsPerPage}`);
    } else if (query.length === 0) {
      navigate(`?pageNo=${0}&pageSize=${itemsPerPage}`);
      setSearchTerm("");
    }
  };
  const handleCancel = () => {
    navigate(`?&pageNo=${0}&pageSize=${itemsPerPage}`);
    setSearchTerm("");
  };
  const [updateBuyerName, setUpdateBuyerName] = reactExports.useState({});
  const [isBuyerUpdate, SetIsBuyerUpdate] = reactExports.useState({});
  const handleUpdateName = (buyerId, val) => {
    setUpdateBuyerName((prev) => ({
      ...prev,
      [buyerId]: val
    }));
  };
  const [updateLatLon, setUpdateLatLon] = reactExports.useState({});
  const [isBuyerLatLonUpdate, setIsBuyerLatLonUpdate] = reactExports.useState({});
  const handleSave = (buyerId) => {
    const formData = new FormData();
    formData.append("_intent", "updateBuyerName");
    formData.append("buyerId", buyerId.toString());
    formData.append("buyerName", updateBuyerName[buyerId]);
    formData.append("attribute", "name");
    fetcher.submit(formData, {
      method: "put"
    });
    SetIsBuyerUpdate((prev) => ({
      ...prev,
      [buyerId]: false
    }));
  };
  const [buyerStatus, setBuyerStatus] = reactExports.useState({});
  const handleUpdateLatLon = (buyerId, field, val) => {
    setUpdateLatLon((prev) => ({
      ...prev,
      [buyerId]: {
        ...prev[buyerId],
        [field]: val
      }
    }));
  };
  const handleSaveLatLon = (buyerId) => {
    const latLon = updateLatLon[buyerId];
    if (!(latLon == null ? void 0 : latLon.lat) || !(latLon == null ? void 0 : latLon.lon)) return;
    const formData = new FormData();
    formData.append("_intent", "updateLatLon");
    formData.append("buyerId", buyerId.toString());
    formData.append("latitude", latLon.lat);
    formData.append("longitude", latLon.lon);
    formData.append("attribute", "latlong");
    fetcher.submit(formData, {
      method: "put"
    });
    setIsBuyerLatLonUpdate((prev) => ({
      ...prev,
      [buyerId]: false
    }));
  };
  const handleToggleStatus = (buyerId, currentStatus) => {
    const newStatus = !currentStatus;
    setBuyerStatus((prev) => ({
      ...prev,
      [buyerId]: newStatus
    }));
    const formData = new FormData();
    formData.append("_intent", "updateStatus");
    formData.append("buyerId", buyerId.toString());
    formData.append("status", newStatus.toString());
    formData.append("attribute", "status");
    fetcher.submit(formData, {
      method: "put"
    });
  };
  return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
    className: "container mx-auto w-full p-6",
    children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
      className: "flex flex-row justify-between items-center",
      children: [/* @__PURE__ */ jsxRuntimeExports.jsx("div", {
        className: " items-center my-3",
        children: /* @__PURE__ */ jsxRuntimeExports.jsx("h1", {
          className: "text-2xl font-bold",
          children: "BuyerManagement"
        })
      }), /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
        className: "flex my-3 w-full justify-start",
        children: /* @__PURE__ */ jsxRuntimeExports.jsx(SearchBar, {
          onSearch: handleSearch,
          onCancel: handleCancel,
          isLoading
        })
      })]
    }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
      children: [/* @__PURE__ */ jsxRuntimeExports.jsx(SpinnerLoader, {
        loading: isLoading
      }), /* @__PURE__ */ jsxRuntimeExports.jsx(ResponsiveTable, {
        headers: BuyerManagementHeader,
        data: buyerData,
        renderRow: (row) => {
          var _a, _b;
          return /* @__PURE__ */ jsxRuntimeExports.jsxs("tr", {
            className: "border-b",
            children: [/* @__PURE__ */ jsxRuntimeExports.jsx("td", {
              className: "py-2 px-3 font-medium text-center",
              children: row.buyerId
            }), /* @__PURE__ */ jsxRuntimeExports.jsx("td", {
              className: "py-2 px-3 cursor-pointer text-center w-full",
              children: isBuyerUpdate[row.buyerId] ? /* @__PURE__ */ jsxRuntimeExports.jsx(jsxRuntimeExports.Fragment, {
                children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                  className: "flex flex-row gap-2 justify-center ",
                  children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Input, {
                    type: "text",
                    value: updateBuyerName[row.buyerId] ?? row.name,
                    onChange: (e) => handleUpdateName(row.buyerId, e.target.value),
                    className: " px-2 py-1 border border-gray-300 rounded-md"
                  }), /* @__PURE__ */ jsxRuntimeExports.jsx(Save, {
                    size: 24,
                    onClick: () => handleSave(row.buyerId)
                  }), /* @__PURE__ */ jsxRuntimeExports.jsx(X, {
                    color: "red",
                    size: 24,
                    className: "cursor-pointer text-red-500",
                    onClick: () => SetIsBuyerUpdate({})
                  })]
                })
              }) : /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                className: "flex flex-row gap-2 items-center justify-center",
                children: [row.name || "-", /* @__PURE__ */ jsxRuntimeExports.jsx(Pencil, {
                  size: 15,
                  onClick: () => SetIsBuyerUpdate({
                    [row.buyerId]: true
                  })
                })]
              })
            }), /* @__PURE__ */ jsxRuntimeExports.jsx("td", {
              className: "py-2 px-3 text-center",
              children: (row == null ? void 0 : row.mobileNumber) || "-"
            }), /* @__PURE__ */ jsxRuntimeExports.jsx("td", {
              className: "py-2 px-3 text-center",
              children: (row == null ? void 0 : row.address) || "-"
            }), /* @__PURE__ */ jsxRuntimeExports.jsx("td", {
              className: "py-2 px-3 text-center w-40",
              children: isBuyerLatLonUpdate[row.buyerId] ? /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                className: "flex flex-row gap-2 justify-center items-center",
                children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Input, {
                  type: "text",
                  value: ((_a = updateLatLon[row.buyerId]) == null ? void 0 : _a.lat) ?? row.latitude,
                  onChange: (e) => handleUpdateLatLon(row.buyerId, "lat", e.target.value),
                  className: "w-20 px-2 py-1 border border-gray-300 rounded-md",
                  placeholder: "Lat"
                }), /* @__PURE__ */ jsxRuntimeExports.jsx(Input, {
                  type: "text",
                  value: ((_b = updateLatLon[row.buyerId]) == null ? void 0 : _b.lon) ?? row.longitude,
                  onChange: (e) => handleUpdateLatLon(row.buyerId, "lon", e.target.value),
                  className: "w-20 px-2 py-1 border border-gray-300 rounded-md",
                  placeholder: "Lon"
                }), /* @__PURE__ */ jsxRuntimeExports.jsx(Save, {
                  size: 24,
                  onClick: () => handleSaveLatLon(row.buyerId)
                }), /* @__PURE__ */ jsxRuntimeExports.jsx(X, {
                  color: "red",
                  size: 24,
                  className: "cursor-pointer text-red-500",
                  onClick: () => setIsBuyerLatLonUpdate({})
                })]
              }) : /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                className: "flex flex-row gap-2 justify-center items-center",
                children: [row.latitude, ", ", row.longitude, /* @__PURE__ */ jsxRuntimeExports.jsx(Pencil, {
                  size: 24,
                  onClick: () => setIsBuyerLatLonUpdate({
                    [row.buyerId]: true
                  }),
                  className: "cursor-pointer"
                })]
              })
            }), /* @__PURE__ */ jsxRuntimeExports.jsx("td", {
              className: "py-2 px-3 text-center",
              children: row.walletBalance > 0 ? row == null ? void 0 : row.walletBalance.toFixed(1) : "-"
            }), /* @__PURE__ */ jsxRuntimeExports.jsx("td", {
              className: "py-2 px-3 text-center",
              children: /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
                className: "flex items-center justify-center space-x-2",
                children: /* @__PURE__ */ jsxRuntimeExports.jsx(Switch, {
                  checked: !(buyerStatus[row.buyerId] || row.status),
                  onCheckedChange: () => handleToggleStatus(row.buyerId, buyerStatus[row.buyerId])
                })
              })
            })]
          }, row.buyerId);
        },
        emptyMessage: "No data available for the selected filters."
      })]
    }), /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
      className: "flex justify-center items-center mt-6",
      children: /* @__PURE__ */ jsxRuntimeExports.jsx(Pagination, {
        className: "flex items-center space-x-2 bg-white shadow-md rounded-lg px-4 py-2",
        children: /* @__PURE__ */ jsxRuntimeExports.jsxs(PaginationContent, {
          className: "flex items-center space-x-2",
          children: [currentPage > 0 && /* @__PURE__ */ jsxRuntimeExports.jsx(PaginationItem, {
            children: /* @__PURE__ */ jsxRuntimeExports.jsx(PaginationPrevious, {
              onClick: () => handlePageChange(currentPage - 1),
              className: "px-3 py-1 bg-gray-200 hover:bg-gray-300 text-gray-700 rounded-md transition cursor-pointer"
            })
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(PaginationItem, {
            children: /* @__PURE__ */ jsxRuntimeExports.jsx(PaginationLink, {
              className: "px-4 py-1 bg-blue-500 text-white rounded-md shadow-md",
              children: currentPage + 1
            })
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(PaginationItem, {
            children: /* @__PURE__ */ jsxRuntimeExports.jsx(PaginationNext, {
              onClick: () => handlePageChange(currentPage + 1),
              className: "px-3 py-1 bg-gray-200 hover:bg-gray-300 text-gray-700 rounded-md transition cursor-pointer"
            })
          })]
        })
      })
    })]
  });
}
export {
  BuyerManagement as default
};
//# sourceMappingURL=home.buyerManagement-DoYaCCkj.js.map
