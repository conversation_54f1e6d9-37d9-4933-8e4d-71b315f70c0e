{"version": 3, "file": "open.reports.restaurant-daily-report-S-j8TfGL.js", "sources": ["../../../app/routes/open.reports.restaurant-daily-report.tsx"], "sourcesContent": ["import { json, LoaderFunctionArgs } from \"@remix-run/node\";\r\nimport { useLoaderData  } from \"@remix-run/react\";\r\nimport { metabaseService } from \"../utils/metabase\";\r\n\r\nexport async function loader({ request }: LoaderFunctionArgs) {\r\n      const url = new URL(request.url);\r\n      const sellerId = url.searchParams.get(\"seller_id\");\r\n\r\n      if (!sellerId) {\r\n            throw new Error(\"Seller ID is required.\");\r\n      }\r\n\r\n      const orderDate = url.searchParams.get(\"order_date\") || new Date().toISOString().split('T')[0];\r\n\r\n      const embedUrl = metabaseService.generateQuestionUrl(70, {\r\n            seller_id: sellerId,\r\n            order_date: orderDate,\r\n      });\r\n\r\n      return json({ embedUrl, orderDate });\r\n}\r\n\r\nexport default function MetaRestaurantReport() {\r\n      const { embedUrl, orderDate } = useLoaderData<typeof loader>();\r\n   \r\n\r\n      return (\r\n            <div className=\"container mx-auto p-4\">\r\n                  <h1 className=\"text-2xl font-bold mb-2\">Daily Report</h1>\r\n                  {/* <p className=\"text-gray-500 mb-2\">Seller ID: {sellerId}</p> */}\r\n                  <p className=\"text-gray-500 mb-2\">Order Date: {orderDate}</p>\r\n\r\n                  <div className=\"w-full\">\r\n                        {embedUrl ? (\r\n                              <iframe\r\n                                    id=\"metabase-iframe\"\r\n                                    src={embedUrl}\r\n                                    title=\"Metabase Dashboard\"\r\n                                    className=\"w-full h-screen border-0\"\r\n                              />\r\n                        ) : (\r\n                              <div className=\"p-6 text-center text-red-500\">\r\n                                    Failed to load the dashboard.\r\n                              </div>\r\n                        )}\r\n                  </div>\r\n            </div>\r\n      );\r\n}"], "names": ["MetaRestaurantReport", "embedUrl", "orderDate", "useLoaderData", "jsxs", "className", "children", "jsx", "id", "src", "title"], "mappings": ";;;;AAsBA,SAAwBA,uBAAuB;AACzC,QAAM;AAAA,IAAEC;AAAAA,IAAUC;AAAAA,EAAU,IAAIC,cAA6B;AAIvD,SAAAC,kCAAAA,KAAC,OAAI;AAAA,IAAAC,WAAU;AAAA,IACTC,UAAA,CAACC,kCAAA,IAAA,MAAA;AAAA,MAAGF,WAAU;AAAA,MAA0BC,UAAY;AAAA,IAAA,CAAA,GAEpDF,kCAAA,KAAC,KAAE;AAAA,MAAAC,WAAU;AAAA,MAAqBC,UAAA,CAAA,gBAAaJ,SAAA;AAAA,IAAU,CAAA,GAExDK,kCAAA,IAAA,OAAA;AAAA,MAAIF,WAAU;AAAA,MACRC,UACKL,WAAAM,kCAAA,IAAC,UAAA;AAAA,QACKC,IAAG;AAAA,QACHC,KAAKR;AAAAA,QACLS,OAAM;AAAA,QACNL,WAAU;AAAA,MAAA,CAChB,IAECE,kCAAA,IAAA,OAAA;AAAA,QAAIF,WAAU;AAAA,QAA+BC;MAE9C,CAAA;AAAA,IAEZ,CAAA,CAAA;AAAA,EACN,CAAA;AAEZ;"}