{"version": 3, "file": "select-BFGSXKcr.js", "sources": ["../../../node_modules/lucide-react/dist/esm/icons/chevron-up.js", "../../../node_modules/@radix-ui/react-select/dist/index.mjs", "../../../app/components/ui/select.tsx"], "sourcesContent": ["/**\n * @license lucide-react v0.453.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst ChevronUp = createLucideIcon(\"ChevronUp\", [[\"path\", { d: \"m18 15-6-6-6 6\", key: \"153udz\" }]]);\n\nexport { ChevronUp as default };\n//# sourceMappingURL=chevron-up.js.map\n", "\"use client\";\n\n// packages/react/select/src/select.tsx\nimport * as React from \"react\";\nimport * as ReactDOM from \"react-dom\";\nimport { clamp } from \"@radix-ui/number\";\nimport { composeEventHandlers } from \"@radix-ui/primitive\";\nimport { createCollection } from \"@radix-ui/react-collection\";\nimport { useComposedRefs } from \"@radix-ui/react-compose-refs\";\nimport { createContextScope } from \"@radix-ui/react-context\";\nimport { useDirection } from \"@radix-ui/react-direction\";\nimport { DismissableLayer } from \"@radix-ui/react-dismissable-layer\";\nimport { useFocusGuards } from \"@radix-ui/react-focus-guards\";\nimport { FocusScope } from \"@radix-ui/react-focus-scope\";\nimport { useId } from \"@radix-ui/react-id\";\nimport * as PopperPrimitive from \"@radix-ui/react-popper\";\nimport { createPopperScope } from \"@radix-ui/react-popper\";\nimport { Portal as PortalPrimitive } from \"@radix-ui/react-portal\";\nimport { Primitive } from \"@radix-ui/react-primitive\";\nimport { Slot } from \"@radix-ui/react-slot\";\nimport { useCallbackRef } from \"@radix-ui/react-use-callback-ref\";\nimport { useControllableState } from \"@radix-ui/react-use-controllable-state\";\nimport { useLayoutEffect } from \"@radix-ui/react-use-layout-effect\";\nimport { usePrevious } from \"@radix-ui/react-use-previous\";\nimport { VisuallyHidden } from \"@radix-ui/react-visually-hidden\";\nimport { hideOthers } from \"aria-hidden\";\nimport { RemoveScroll } from \"react-remove-scroll\";\nimport { Fragment, jsx, jsxs } from \"react/jsx-runtime\";\nvar OPEN_KEYS = [\" \", \"Enter\", \"ArrowUp\", \"ArrowDown\"];\nvar SELECTION_KEYS = [\" \", \"Enter\"];\nvar SELECT_NAME = \"Select\";\nvar [Collection, useCollection, createCollectionScope] = createCollection(SELECT_NAME);\nvar [createSelectContext, createSelectScope] = createContextScope(SELECT_NAME, [\n  createCollectionScope,\n  createPopperScope\n]);\nvar usePopperScope = createPopperScope();\nvar [SelectProvider, useSelectContext] = createSelectContext(SELECT_NAME);\nvar [SelectNativeOptionsProvider, useSelectNativeOptionsContext] = createSelectContext(SELECT_NAME);\nvar Select = (props) => {\n  const {\n    __scopeSelect,\n    children,\n    open: openProp,\n    defaultOpen,\n    onOpenChange,\n    value: valueProp,\n    defaultValue,\n    onValueChange,\n    dir,\n    name,\n    autoComplete,\n    disabled,\n    required,\n    form\n  } = props;\n  const popperScope = usePopperScope(__scopeSelect);\n  const [trigger, setTrigger] = React.useState(null);\n  const [valueNode, setValueNode] = React.useState(null);\n  const [valueNodeHasChildren, setValueNodeHasChildren] = React.useState(false);\n  const direction = useDirection(dir);\n  const [open = false, setOpen] = useControllableState({\n    prop: openProp,\n    defaultProp: defaultOpen,\n    onChange: onOpenChange\n  });\n  const [value, setValue] = useControllableState({\n    prop: valueProp,\n    defaultProp: defaultValue,\n    onChange: onValueChange\n  });\n  const triggerPointerDownPosRef = React.useRef(null);\n  const isFormControl = trigger ? form || !!trigger.closest(\"form\") : true;\n  const [nativeOptionsSet, setNativeOptionsSet] = React.useState(/* @__PURE__ */ new Set());\n  const nativeSelectKey = Array.from(nativeOptionsSet).map((option) => option.props.value).join(\";\");\n  return /* @__PURE__ */ jsx(PopperPrimitive.Root, { ...popperScope, children: /* @__PURE__ */ jsxs(\n    SelectProvider,\n    {\n      required,\n      scope: __scopeSelect,\n      trigger,\n      onTriggerChange: setTrigger,\n      valueNode,\n      onValueNodeChange: setValueNode,\n      valueNodeHasChildren,\n      onValueNodeHasChildrenChange: setValueNodeHasChildren,\n      contentId: useId(),\n      value,\n      onValueChange: setValue,\n      open,\n      onOpenChange: setOpen,\n      dir: direction,\n      triggerPointerDownPosRef,\n      disabled,\n      children: [\n        /* @__PURE__ */ jsx(Collection.Provider, { scope: __scopeSelect, children: /* @__PURE__ */ jsx(\n          SelectNativeOptionsProvider,\n          {\n            scope: props.__scopeSelect,\n            onNativeOptionAdd: React.useCallback((option) => {\n              setNativeOptionsSet((prev) => new Set(prev).add(option));\n            }, []),\n            onNativeOptionRemove: React.useCallback((option) => {\n              setNativeOptionsSet((prev) => {\n                const optionsSet = new Set(prev);\n                optionsSet.delete(option);\n                return optionsSet;\n              });\n            }, []),\n            children\n          }\n        ) }),\n        isFormControl ? /* @__PURE__ */ jsxs(\n          BubbleSelect,\n          {\n            \"aria-hidden\": true,\n            required,\n            tabIndex: -1,\n            name,\n            autoComplete,\n            value,\n            onChange: (event) => setValue(event.target.value),\n            disabled,\n            form,\n            children: [\n              value === void 0 ? /* @__PURE__ */ jsx(\"option\", { value: \"\" }) : null,\n              Array.from(nativeOptionsSet)\n            ]\n          },\n          nativeSelectKey\n        ) : null\n      ]\n    }\n  ) });\n};\nSelect.displayName = SELECT_NAME;\nvar TRIGGER_NAME = \"SelectTrigger\";\nvar SelectTrigger = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeSelect, disabled = false, ...triggerProps } = props;\n    const popperScope = usePopperScope(__scopeSelect);\n    const context = useSelectContext(TRIGGER_NAME, __scopeSelect);\n    const isDisabled = context.disabled || disabled;\n    const composedRefs = useComposedRefs(forwardedRef, context.onTriggerChange);\n    const getItems = useCollection(__scopeSelect);\n    const pointerTypeRef = React.useRef(\"touch\");\n    const [searchRef, handleTypeaheadSearch, resetTypeahead] = useTypeaheadSearch((search) => {\n      const enabledItems = getItems().filter((item) => !item.disabled);\n      const currentItem = enabledItems.find((item) => item.value === context.value);\n      const nextItem = findNextItem(enabledItems, search, currentItem);\n      if (nextItem !== void 0) {\n        context.onValueChange(nextItem.value);\n      }\n    });\n    const handleOpen = (pointerEvent) => {\n      if (!isDisabled) {\n        context.onOpenChange(true);\n        resetTypeahead();\n      }\n      if (pointerEvent) {\n        context.triggerPointerDownPosRef.current = {\n          x: Math.round(pointerEvent.pageX),\n          y: Math.round(pointerEvent.pageY)\n        };\n      }\n    };\n    return /* @__PURE__ */ jsx(PopperPrimitive.Anchor, { asChild: true, ...popperScope, children: /* @__PURE__ */ jsx(\n      Primitive.button,\n      {\n        type: \"button\",\n        role: \"combobox\",\n        \"aria-controls\": context.contentId,\n        \"aria-expanded\": context.open,\n        \"aria-required\": context.required,\n        \"aria-autocomplete\": \"none\",\n        dir: context.dir,\n        \"data-state\": context.open ? \"open\" : \"closed\",\n        disabled: isDisabled,\n        \"data-disabled\": isDisabled ? \"\" : void 0,\n        \"data-placeholder\": shouldShowPlaceholder(context.value) ? \"\" : void 0,\n        ...triggerProps,\n        ref: composedRefs,\n        onClick: composeEventHandlers(triggerProps.onClick, (event) => {\n          event.currentTarget.focus();\n          if (pointerTypeRef.current !== \"mouse\") {\n            handleOpen(event);\n          }\n        }),\n        onPointerDown: composeEventHandlers(triggerProps.onPointerDown, (event) => {\n          pointerTypeRef.current = event.pointerType;\n          const target = event.target;\n          if (target.hasPointerCapture(event.pointerId)) {\n            target.releasePointerCapture(event.pointerId);\n          }\n          if (event.button === 0 && event.ctrlKey === false && event.pointerType === \"mouse\") {\n            handleOpen(event);\n            event.preventDefault();\n          }\n        }),\n        onKeyDown: composeEventHandlers(triggerProps.onKeyDown, (event) => {\n          const isTypingAhead = searchRef.current !== \"\";\n          const isModifierKey = event.ctrlKey || event.altKey || event.metaKey;\n          if (!isModifierKey && event.key.length === 1) handleTypeaheadSearch(event.key);\n          if (isTypingAhead && event.key === \" \") return;\n          if (OPEN_KEYS.includes(event.key)) {\n            handleOpen();\n            event.preventDefault();\n          }\n        })\n      }\n    ) });\n  }\n);\nSelectTrigger.displayName = TRIGGER_NAME;\nvar VALUE_NAME = \"SelectValue\";\nvar SelectValue = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeSelect, className, style, children, placeholder = \"\", ...valueProps } = props;\n    const context = useSelectContext(VALUE_NAME, __scopeSelect);\n    const { onValueNodeHasChildrenChange } = context;\n    const hasChildren = children !== void 0;\n    const composedRefs = useComposedRefs(forwardedRef, context.onValueNodeChange);\n    useLayoutEffect(() => {\n      onValueNodeHasChildrenChange(hasChildren);\n    }, [onValueNodeHasChildrenChange, hasChildren]);\n    return /* @__PURE__ */ jsx(\n      Primitive.span,\n      {\n        ...valueProps,\n        ref: composedRefs,\n        style: { pointerEvents: \"none\" },\n        children: shouldShowPlaceholder(context.value) ? /* @__PURE__ */ jsx(Fragment, { children: placeholder }) : children\n      }\n    );\n  }\n);\nSelectValue.displayName = VALUE_NAME;\nvar ICON_NAME = \"SelectIcon\";\nvar SelectIcon = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeSelect, children, ...iconProps } = props;\n    return /* @__PURE__ */ jsx(Primitive.span, { \"aria-hidden\": true, ...iconProps, ref: forwardedRef, children: children || \"\\u25BC\" });\n  }\n);\nSelectIcon.displayName = ICON_NAME;\nvar PORTAL_NAME = \"SelectPortal\";\nvar SelectPortal = (props) => {\n  return /* @__PURE__ */ jsx(PortalPrimitive, { asChild: true, ...props });\n};\nSelectPortal.displayName = PORTAL_NAME;\nvar CONTENT_NAME = \"SelectContent\";\nvar SelectContent = React.forwardRef(\n  (props, forwardedRef) => {\n    const context = useSelectContext(CONTENT_NAME, props.__scopeSelect);\n    const [fragment, setFragment] = React.useState();\n    useLayoutEffect(() => {\n      setFragment(new DocumentFragment());\n    }, []);\n    if (!context.open) {\n      const frag = fragment;\n      return frag ? ReactDOM.createPortal(\n        /* @__PURE__ */ jsx(SelectContentProvider, { scope: props.__scopeSelect, children: /* @__PURE__ */ jsx(Collection.Slot, { scope: props.__scopeSelect, children: /* @__PURE__ */ jsx(\"div\", { children: props.children }) }) }),\n        frag\n      ) : null;\n    }\n    return /* @__PURE__ */ jsx(SelectContentImpl, { ...props, ref: forwardedRef });\n  }\n);\nSelectContent.displayName = CONTENT_NAME;\nvar CONTENT_MARGIN = 10;\nvar [SelectContentProvider, useSelectContentContext] = createSelectContext(CONTENT_NAME);\nvar CONTENT_IMPL_NAME = \"SelectContentImpl\";\nvar SelectContentImpl = React.forwardRef(\n  (props, forwardedRef) => {\n    const {\n      __scopeSelect,\n      position = \"item-aligned\",\n      onCloseAutoFocus,\n      onEscapeKeyDown,\n      onPointerDownOutside,\n      //\n      // PopperContent props\n      side,\n      sideOffset,\n      align,\n      alignOffset,\n      arrowPadding,\n      collisionBoundary,\n      collisionPadding,\n      sticky,\n      hideWhenDetached,\n      avoidCollisions,\n      //\n      ...contentProps\n    } = props;\n    const context = useSelectContext(CONTENT_NAME, __scopeSelect);\n    const [content, setContent] = React.useState(null);\n    const [viewport, setViewport] = React.useState(null);\n    const composedRefs = useComposedRefs(forwardedRef, (node) => setContent(node));\n    const [selectedItem, setSelectedItem] = React.useState(null);\n    const [selectedItemText, setSelectedItemText] = React.useState(\n      null\n    );\n    const getItems = useCollection(__scopeSelect);\n    const [isPositioned, setIsPositioned] = React.useState(false);\n    const firstValidItemFoundRef = React.useRef(false);\n    React.useEffect(() => {\n      if (content) return hideOthers(content);\n    }, [content]);\n    useFocusGuards();\n    const focusFirst = React.useCallback(\n      (candidates) => {\n        const [firstItem, ...restItems] = getItems().map((item) => item.ref.current);\n        const [lastItem] = restItems.slice(-1);\n        const PREVIOUSLY_FOCUSED_ELEMENT = document.activeElement;\n        for (const candidate of candidates) {\n          if (candidate === PREVIOUSLY_FOCUSED_ELEMENT) return;\n          candidate?.scrollIntoView({ block: \"nearest\" });\n          if (candidate === firstItem && viewport) viewport.scrollTop = 0;\n          if (candidate === lastItem && viewport) viewport.scrollTop = viewport.scrollHeight;\n          candidate?.focus();\n          if (document.activeElement !== PREVIOUSLY_FOCUSED_ELEMENT) return;\n        }\n      },\n      [getItems, viewport]\n    );\n    const focusSelectedItem = React.useCallback(\n      () => focusFirst([selectedItem, content]),\n      [focusFirst, selectedItem, content]\n    );\n    React.useEffect(() => {\n      if (isPositioned) {\n        focusSelectedItem();\n      }\n    }, [isPositioned, focusSelectedItem]);\n    const { onOpenChange, triggerPointerDownPosRef } = context;\n    React.useEffect(() => {\n      if (content) {\n        let pointerMoveDelta = { x: 0, y: 0 };\n        const handlePointerMove = (event) => {\n          pointerMoveDelta = {\n            x: Math.abs(Math.round(event.pageX) - (triggerPointerDownPosRef.current?.x ?? 0)),\n            y: Math.abs(Math.round(event.pageY) - (triggerPointerDownPosRef.current?.y ?? 0))\n          };\n        };\n        const handlePointerUp = (event) => {\n          if (pointerMoveDelta.x <= 10 && pointerMoveDelta.y <= 10) {\n            event.preventDefault();\n          } else {\n            if (!content.contains(event.target)) {\n              onOpenChange(false);\n            }\n          }\n          document.removeEventListener(\"pointermove\", handlePointerMove);\n          triggerPointerDownPosRef.current = null;\n        };\n        if (triggerPointerDownPosRef.current !== null) {\n          document.addEventListener(\"pointermove\", handlePointerMove);\n          document.addEventListener(\"pointerup\", handlePointerUp, { capture: true, once: true });\n        }\n        return () => {\n          document.removeEventListener(\"pointermove\", handlePointerMove);\n          document.removeEventListener(\"pointerup\", handlePointerUp, { capture: true });\n        };\n      }\n    }, [content, onOpenChange, triggerPointerDownPosRef]);\n    React.useEffect(() => {\n      const close = () => onOpenChange(false);\n      window.addEventListener(\"blur\", close);\n      window.addEventListener(\"resize\", close);\n      return () => {\n        window.removeEventListener(\"blur\", close);\n        window.removeEventListener(\"resize\", close);\n      };\n    }, [onOpenChange]);\n    const [searchRef, handleTypeaheadSearch] = useTypeaheadSearch((search) => {\n      const enabledItems = getItems().filter((item) => !item.disabled);\n      const currentItem = enabledItems.find((item) => item.ref.current === document.activeElement);\n      const nextItem = findNextItem(enabledItems, search, currentItem);\n      if (nextItem) {\n        setTimeout(() => nextItem.ref.current.focus());\n      }\n    });\n    const itemRefCallback = React.useCallback(\n      (node, value, disabled) => {\n        const isFirstValidItem = !firstValidItemFoundRef.current && !disabled;\n        const isSelectedItem = context.value !== void 0 && context.value === value;\n        if (isSelectedItem || isFirstValidItem) {\n          setSelectedItem(node);\n          if (isFirstValidItem) firstValidItemFoundRef.current = true;\n        }\n      },\n      [context.value]\n    );\n    const handleItemLeave = React.useCallback(() => content?.focus(), [content]);\n    const itemTextRefCallback = React.useCallback(\n      (node, value, disabled) => {\n        const isFirstValidItem = !firstValidItemFoundRef.current && !disabled;\n        const isSelectedItem = context.value !== void 0 && context.value === value;\n        if (isSelectedItem || isFirstValidItem) {\n          setSelectedItemText(node);\n        }\n      },\n      [context.value]\n    );\n    const SelectPosition = position === \"popper\" ? SelectPopperPosition : SelectItemAlignedPosition;\n    const popperContentProps = SelectPosition === SelectPopperPosition ? {\n      side,\n      sideOffset,\n      align,\n      alignOffset,\n      arrowPadding,\n      collisionBoundary,\n      collisionPadding,\n      sticky,\n      hideWhenDetached,\n      avoidCollisions\n    } : {};\n    return /* @__PURE__ */ jsx(\n      SelectContentProvider,\n      {\n        scope: __scopeSelect,\n        content,\n        viewport,\n        onViewportChange: setViewport,\n        itemRefCallback,\n        selectedItem,\n        onItemLeave: handleItemLeave,\n        itemTextRefCallback,\n        focusSelectedItem,\n        selectedItemText,\n        position,\n        isPositioned,\n        searchRef,\n        children: /* @__PURE__ */ jsx(RemoveScroll, { as: Slot, allowPinchZoom: true, children: /* @__PURE__ */ jsx(\n          FocusScope,\n          {\n            asChild: true,\n            trapped: context.open,\n            onMountAutoFocus: (event) => {\n              event.preventDefault();\n            },\n            onUnmountAutoFocus: composeEventHandlers(onCloseAutoFocus, (event) => {\n              context.trigger?.focus({ preventScroll: true });\n              event.preventDefault();\n            }),\n            children: /* @__PURE__ */ jsx(\n              DismissableLayer,\n              {\n                asChild: true,\n                disableOutsidePointerEvents: true,\n                onEscapeKeyDown,\n                onPointerDownOutside,\n                onFocusOutside: (event) => event.preventDefault(),\n                onDismiss: () => context.onOpenChange(false),\n                children: /* @__PURE__ */ jsx(\n                  SelectPosition,\n                  {\n                    role: \"listbox\",\n                    id: context.contentId,\n                    \"data-state\": context.open ? \"open\" : \"closed\",\n                    dir: context.dir,\n                    onContextMenu: (event) => event.preventDefault(),\n                    ...contentProps,\n                    ...popperContentProps,\n                    onPlaced: () => setIsPositioned(true),\n                    ref: composedRefs,\n                    style: {\n                      // flex layout so we can place the scroll buttons properly\n                      display: \"flex\",\n                      flexDirection: \"column\",\n                      // reset the outline by default as the content MAY get focused\n                      outline: \"none\",\n                      ...contentProps.style\n                    },\n                    onKeyDown: composeEventHandlers(contentProps.onKeyDown, (event) => {\n                      const isModifierKey = event.ctrlKey || event.altKey || event.metaKey;\n                      if (event.key === \"Tab\") event.preventDefault();\n                      if (!isModifierKey && event.key.length === 1) handleTypeaheadSearch(event.key);\n                      if ([\"ArrowUp\", \"ArrowDown\", \"Home\", \"End\"].includes(event.key)) {\n                        const items = getItems().filter((item) => !item.disabled);\n                        let candidateNodes = items.map((item) => item.ref.current);\n                        if ([\"ArrowUp\", \"End\"].includes(event.key)) {\n                          candidateNodes = candidateNodes.slice().reverse();\n                        }\n                        if ([\"ArrowUp\", \"ArrowDown\"].includes(event.key)) {\n                          const currentElement = event.target;\n                          const currentIndex = candidateNodes.indexOf(currentElement);\n                          candidateNodes = candidateNodes.slice(currentIndex + 1);\n                        }\n                        setTimeout(() => focusFirst(candidateNodes));\n                        event.preventDefault();\n                      }\n                    })\n                  }\n                )\n              }\n            )\n          }\n        ) })\n      }\n    );\n  }\n);\nSelectContentImpl.displayName = CONTENT_IMPL_NAME;\nvar ITEM_ALIGNED_POSITION_NAME = \"SelectItemAlignedPosition\";\nvar SelectItemAlignedPosition = React.forwardRef((props, forwardedRef) => {\n  const { __scopeSelect, onPlaced, ...popperProps } = props;\n  const context = useSelectContext(CONTENT_NAME, __scopeSelect);\n  const contentContext = useSelectContentContext(CONTENT_NAME, __scopeSelect);\n  const [contentWrapper, setContentWrapper] = React.useState(null);\n  const [content, setContent] = React.useState(null);\n  const composedRefs = useComposedRefs(forwardedRef, (node) => setContent(node));\n  const getItems = useCollection(__scopeSelect);\n  const shouldExpandOnScrollRef = React.useRef(false);\n  const shouldRepositionRef = React.useRef(true);\n  const { viewport, selectedItem, selectedItemText, focusSelectedItem } = contentContext;\n  const position = React.useCallback(() => {\n    if (context.trigger && context.valueNode && contentWrapper && content && viewport && selectedItem && selectedItemText) {\n      const triggerRect = context.trigger.getBoundingClientRect();\n      const contentRect = content.getBoundingClientRect();\n      const valueNodeRect = context.valueNode.getBoundingClientRect();\n      const itemTextRect = selectedItemText.getBoundingClientRect();\n      if (context.dir !== \"rtl\") {\n        const itemTextOffset = itemTextRect.left - contentRect.left;\n        const left = valueNodeRect.left - itemTextOffset;\n        const leftDelta = triggerRect.left - left;\n        const minContentWidth = triggerRect.width + leftDelta;\n        const contentWidth = Math.max(minContentWidth, contentRect.width);\n        const rightEdge = window.innerWidth - CONTENT_MARGIN;\n        const clampedLeft = clamp(left, [\n          CONTENT_MARGIN,\n          // Prevents the content from going off the starting edge of the\n          // viewport. It may still go off the ending edge, but this can be\n          // controlled by the user since they may want to manage overflow in a\n          // specific way.\n          // https://github.com/radix-ui/primitives/issues/2049\n          Math.max(CONTENT_MARGIN, rightEdge - contentWidth)\n        ]);\n        contentWrapper.style.minWidth = minContentWidth + \"px\";\n        contentWrapper.style.left = clampedLeft + \"px\";\n      } else {\n        const itemTextOffset = contentRect.right - itemTextRect.right;\n        const right = window.innerWidth - valueNodeRect.right - itemTextOffset;\n        const rightDelta = window.innerWidth - triggerRect.right - right;\n        const minContentWidth = triggerRect.width + rightDelta;\n        const contentWidth = Math.max(minContentWidth, contentRect.width);\n        const leftEdge = window.innerWidth - CONTENT_MARGIN;\n        const clampedRight = clamp(right, [\n          CONTENT_MARGIN,\n          Math.max(CONTENT_MARGIN, leftEdge - contentWidth)\n        ]);\n        contentWrapper.style.minWidth = minContentWidth + \"px\";\n        contentWrapper.style.right = clampedRight + \"px\";\n      }\n      const items = getItems();\n      const availableHeight = window.innerHeight - CONTENT_MARGIN * 2;\n      const itemsHeight = viewport.scrollHeight;\n      const contentStyles = window.getComputedStyle(content);\n      const contentBorderTopWidth = parseInt(contentStyles.borderTopWidth, 10);\n      const contentPaddingTop = parseInt(contentStyles.paddingTop, 10);\n      const contentBorderBottomWidth = parseInt(contentStyles.borderBottomWidth, 10);\n      const contentPaddingBottom = parseInt(contentStyles.paddingBottom, 10);\n      const fullContentHeight = contentBorderTopWidth + contentPaddingTop + itemsHeight + contentPaddingBottom + contentBorderBottomWidth;\n      const minContentHeight = Math.min(selectedItem.offsetHeight * 5, fullContentHeight);\n      const viewportStyles = window.getComputedStyle(viewport);\n      const viewportPaddingTop = parseInt(viewportStyles.paddingTop, 10);\n      const viewportPaddingBottom = parseInt(viewportStyles.paddingBottom, 10);\n      const topEdgeToTriggerMiddle = triggerRect.top + triggerRect.height / 2 - CONTENT_MARGIN;\n      const triggerMiddleToBottomEdge = availableHeight - topEdgeToTriggerMiddle;\n      const selectedItemHalfHeight = selectedItem.offsetHeight / 2;\n      const itemOffsetMiddle = selectedItem.offsetTop + selectedItemHalfHeight;\n      const contentTopToItemMiddle = contentBorderTopWidth + contentPaddingTop + itemOffsetMiddle;\n      const itemMiddleToContentBottom = fullContentHeight - contentTopToItemMiddle;\n      const willAlignWithoutTopOverflow = contentTopToItemMiddle <= topEdgeToTriggerMiddle;\n      if (willAlignWithoutTopOverflow) {\n        const isLastItem = items.length > 0 && selectedItem === items[items.length - 1].ref.current;\n        contentWrapper.style.bottom = \"0px\";\n        const viewportOffsetBottom = content.clientHeight - viewport.offsetTop - viewport.offsetHeight;\n        const clampedTriggerMiddleToBottomEdge = Math.max(\n          triggerMiddleToBottomEdge,\n          selectedItemHalfHeight + // viewport might have padding bottom, include it to avoid a scrollable viewport\n          (isLastItem ? viewportPaddingBottom : 0) + viewportOffsetBottom + contentBorderBottomWidth\n        );\n        const height = contentTopToItemMiddle + clampedTriggerMiddleToBottomEdge;\n        contentWrapper.style.height = height + \"px\";\n      } else {\n        const isFirstItem = items.length > 0 && selectedItem === items[0].ref.current;\n        contentWrapper.style.top = \"0px\";\n        const clampedTopEdgeToTriggerMiddle = Math.max(\n          topEdgeToTriggerMiddle,\n          contentBorderTopWidth + viewport.offsetTop + // viewport might have padding top, include it to avoid a scrollable viewport\n          (isFirstItem ? viewportPaddingTop : 0) + selectedItemHalfHeight\n        );\n        const height = clampedTopEdgeToTriggerMiddle + itemMiddleToContentBottom;\n        contentWrapper.style.height = height + \"px\";\n        viewport.scrollTop = contentTopToItemMiddle - topEdgeToTriggerMiddle + viewport.offsetTop;\n      }\n      contentWrapper.style.margin = `${CONTENT_MARGIN}px 0`;\n      contentWrapper.style.minHeight = minContentHeight + \"px\";\n      contentWrapper.style.maxHeight = availableHeight + \"px\";\n      onPlaced?.();\n      requestAnimationFrame(() => shouldExpandOnScrollRef.current = true);\n    }\n  }, [\n    getItems,\n    context.trigger,\n    context.valueNode,\n    contentWrapper,\n    content,\n    viewport,\n    selectedItem,\n    selectedItemText,\n    context.dir,\n    onPlaced\n  ]);\n  useLayoutEffect(() => position(), [position]);\n  const [contentZIndex, setContentZIndex] = React.useState();\n  useLayoutEffect(() => {\n    if (content) setContentZIndex(window.getComputedStyle(content).zIndex);\n  }, [content]);\n  const handleScrollButtonChange = React.useCallback(\n    (node) => {\n      if (node && shouldRepositionRef.current === true) {\n        position();\n        focusSelectedItem?.();\n        shouldRepositionRef.current = false;\n      }\n    },\n    [position, focusSelectedItem]\n  );\n  return /* @__PURE__ */ jsx(\n    SelectViewportProvider,\n    {\n      scope: __scopeSelect,\n      contentWrapper,\n      shouldExpandOnScrollRef,\n      onScrollButtonChange: handleScrollButtonChange,\n      children: /* @__PURE__ */ jsx(\n        \"div\",\n        {\n          ref: setContentWrapper,\n          style: {\n            display: \"flex\",\n            flexDirection: \"column\",\n            position: \"fixed\",\n            zIndex: contentZIndex\n          },\n          children: /* @__PURE__ */ jsx(\n            Primitive.div,\n            {\n              ...popperProps,\n              ref: composedRefs,\n              style: {\n                // When we get the height of the content, it includes borders. If we were to set\n                // the height without having `boxSizing: 'border-box'` it would be too big.\n                boxSizing: \"border-box\",\n                // We need to ensure the content doesn't get taller than the wrapper\n                maxHeight: \"100%\",\n                ...popperProps.style\n              }\n            }\n          )\n        }\n      )\n    }\n  );\n});\nSelectItemAlignedPosition.displayName = ITEM_ALIGNED_POSITION_NAME;\nvar POPPER_POSITION_NAME = \"SelectPopperPosition\";\nvar SelectPopperPosition = React.forwardRef((props, forwardedRef) => {\n  const {\n    __scopeSelect,\n    align = \"start\",\n    collisionPadding = CONTENT_MARGIN,\n    ...popperProps\n  } = props;\n  const popperScope = usePopperScope(__scopeSelect);\n  return /* @__PURE__ */ jsx(\n    PopperPrimitive.Content,\n    {\n      ...popperScope,\n      ...popperProps,\n      ref: forwardedRef,\n      align,\n      collisionPadding,\n      style: {\n        // Ensure border-box for floating-ui calculations\n        boxSizing: \"border-box\",\n        ...popperProps.style,\n        // re-namespace exposed content custom properties\n        ...{\n          \"--radix-select-content-transform-origin\": \"var(--radix-popper-transform-origin)\",\n          \"--radix-select-content-available-width\": \"var(--radix-popper-available-width)\",\n          \"--radix-select-content-available-height\": \"var(--radix-popper-available-height)\",\n          \"--radix-select-trigger-width\": \"var(--radix-popper-anchor-width)\",\n          \"--radix-select-trigger-height\": \"var(--radix-popper-anchor-height)\"\n        }\n      }\n    }\n  );\n});\nSelectPopperPosition.displayName = POPPER_POSITION_NAME;\nvar [SelectViewportProvider, useSelectViewportContext] = createSelectContext(CONTENT_NAME, {});\nvar VIEWPORT_NAME = \"SelectViewport\";\nvar SelectViewport = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeSelect, nonce, ...viewportProps } = props;\n    const contentContext = useSelectContentContext(VIEWPORT_NAME, __scopeSelect);\n    const viewportContext = useSelectViewportContext(VIEWPORT_NAME, __scopeSelect);\n    const composedRefs = useComposedRefs(forwardedRef, contentContext.onViewportChange);\n    const prevScrollTopRef = React.useRef(0);\n    return /* @__PURE__ */ jsxs(Fragment, { children: [\n      /* @__PURE__ */ jsx(\n        \"style\",\n        {\n          dangerouslySetInnerHTML: {\n            __html: `[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}`\n          },\n          nonce\n        }\n      ),\n      /* @__PURE__ */ jsx(Collection.Slot, { scope: __scopeSelect, children: /* @__PURE__ */ jsx(\n        Primitive.div,\n        {\n          \"data-radix-select-viewport\": \"\",\n          role: \"presentation\",\n          ...viewportProps,\n          ref: composedRefs,\n          style: {\n            // we use position: 'relative' here on the `viewport` so that when we call\n            // `selectedItem.offsetTop` in calculations, the offset is relative to the viewport\n            // (independent of the scrollUpButton).\n            position: \"relative\",\n            flex: 1,\n            // Viewport should only be scrollable in the vertical direction.\n            // This won't work in vertical writing modes, so we'll need to\n            // revisit this if/when that is supported\n            // https://developer.chrome.com/blog/vertical-form-controls\n            overflow: \"hidden auto\",\n            ...viewportProps.style\n          },\n          onScroll: composeEventHandlers(viewportProps.onScroll, (event) => {\n            const viewport = event.currentTarget;\n            const { contentWrapper, shouldExpandOnScrollRef } = viewportContext;\n            if (shouldExpandOnScrollRef?.current && contentWrapper) {\n              const scrolledBy = Math.abs(prevScrollTopRef.current - viewport.scrollTop);\n              if (scrolledBy > 0) {\n                const availableHeight = window.innerHeight - CONTENT_MARGIN * 2;\n                const cssMinHeight = parseFloat(contentWrapper.style.minHeight);\n                const cssHeight = parseFloat(contentWrapper.style.height);\n                const prevHeight = Math.max(cssMinHeight, cssHeight);\n                if (prevHeight < availableHeight) {\n                  const nextHeight = prevHeight + scrolledBy;\n                  const clampedNextHeight = Math.min(availableHeight, nextHeight);\n                  const heightDiff = nextHeight - clampedNextHeight;\n                  contentWrapper.style.height = clampedNextHeight + \"px\";\n                  if (contentWrapper.style.bottom === \"0px\") {\n                    viewport.scrollTop = heightDiff > 0 ? heightDiff : 0;\n                    contentWrapper.style.justifyContent = \"flex-end\";\n                  }\n                }\n              }\n            }\n            prevScrollTopRef.current = viewport.scrollTop;\n          })\n        }\n      ) })\n    ] });\n  }\n);\nSelectViewport.displayName = VIEWPORT_NAME;\nvar GROUP_NAME = \"SelectGroup\";\nvar [SelectGroupContextProvider, useSelectGroupContext] = createSelectContext(GROUP_NAME);\nvar SelectGroup = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeSelect, ...groupProps } = props;\n    const groupId = useId();\n    return /* @__PURE__ */ jsx(SelectGroupContextProvider, { scope: __scopeSelect, id: groupId, children: /* @__PURE__ */ jsx(Primitive.div, { role: \"group\", \"aria-labelledby\": groupId, ...groupProps, ref: forwardedRef }) });\n  }\n);\nSelectGroup.displayName = GROUP_NAME;\nvar LABEL_NAME = \"SelectLabel\";\nvar SelectLabel = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeSelect, ...labelProps } = props;\n    const groupContext = useSelectGroupContext(LABEL_NAME, __scopeSelect);\n    return /* @__PURE__ */ jsx(Primitive.div, { id: groupContext.id, ...labelProps, ref: forwardedRef });\n  }\n);\nSelectLabel.displayName = LABEL_NAME;\nvar ITEM_NAME = \"SelectItem\";\nvar [SelectItemContextProvider, useSelectItemContext] = createSelectContext(ITEM_NAME);\nvar SelectItem = React.forwardRef(\n  (props, forwardedRef) => {\n    const {\n      __scopeSelect,\n      value,\n      disabled = false,\n      textValue: textValueProp,\n      ...itemProps\n    } = props;\n    const context = useSelectContext(ITEM_NAME, __scopeSelect);\n    const contentContext = useSelectContentContext(ITEM_NAME, __scopeSelect);\n    const isSelected = context.value === value;\n    const [textValue, setTextValue] = React.useState(textValueProp ?? \"\");\n    const [isFocused, setIsFocused] = React.useState(false);\n    const composedRefs = useComposedRefs(\n      forwardedRef,\n      (node) => contentContext.itemRefCallback?.(node, value, disabled)\n    );\n    const textId = useId();\n    const pointerTypeRef = React.useRef(\"touch\");\n    const handleSelect = () => {\n      if (!disabled) {\n        context.onValueChange(value);\n        context.onOpenChange(false);\n      }\n    };\n    if (value === \"\") {\n      throw new Error(\n        \"A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.\"\n      );\n    }\n    return /* @__PURE__ */ jsx(\n      SelectItemContextProvider,\n      {\n        scope: __scopeSelect,\n        value,\n        disabled,\n        textId,\n        isSelected,\n        onItemTextChange: React.useCallback((node) => {\n          setTextValue((prevTextValue) => prevTextValue || (node?.textContent ?? \"\").trim());\n        }, []),\n        children: /* @__PURE__ */ jsx(\n          Collection.ItemSlot,\n          {\n            scope: __scopeSelect,\n            value,\n            disabled,\n            textValue,\n            children: /* @__PURE__ */ jsx(\n              Primitive.div,\n              {\n                role: \"option\",\n                \"aria-labelledby\": textId,\n                \"data-highlighted\": isFocused ? \"\" : void 0,\n                \"aria-selected\": isSelected && isFocused,\n                \"data-state\": isSelected ? \"checked\" : \"unchecked\",\n                \"aria-disabled\": disabled || void 0,\n                \"data-disabled\": disabled ? \"\" : void 0,\n                tabIndex: disabled ? void 0 : -1,\n                ...itemProps,\n                ref: composedRefs,\n                onFocus: composeEventHandlers(itemProps.onFocus, () => setIsFocused(true)),\n                onBlur: composeEventHandlers(itemProps.onBlur, () => setIsFocused(false)),\n                onClick: composeEventHandlers(itemProps.onClick, () => {\n                  if (pointerTypeRef.current !== \"mouse\") handleSelect();\n                }),\n                onPointerUp: composeEventHandlers(itemProps.onPointerUp, () => {\n                  if (pointerTypeRef.current === \"mouse\") handleSelect();\n                }),\n                onPointerDown: composeEventHandlers(itemProps.onPointerDown, (event) => {\n                  pointerTypeRef.current = event.pointerType;\n                }),\n                onPointerMove: composeEventHandlers(itemProps.onPointerMove, (event) => {\n                  pointerTypeRef.current = event.pointerType;\n                  if (disabled) {\n                    contentContext.onItemLeave?.();\n                  } else if (pointerTypeRef.current === \"mouse\") {\n                    event.currentTarget.focus({ preventScroll: true });\n                  }\n                }),\n                onPointerLeave: composeEventHandlers(itemProps.onPointerLeave, (event) => {\n                  if (event.currentTarget === document.activeElement) {\n                    contentContext.onItemLeave?.();\n                  }\n                }),\n                onKeyDown: composeEventHandlers(itemProps.onKeyDown, (event) => {\n                  const isTypingAhead = contentContext.searchRef?.current !== \"\";\n                  if (isTypingAhead && event.key === \" \") return;\n                  if (SELECTION_KEYS.includes(event.key)) handleSelect();\n                  if (event.key === \" \") event.preventDefault();\n                })\n              }\n            )\n          }\n        )\n      }\n    );\n  }\n);\nSelectItem.displayName = ITEM_NAME;\nvar ITEM_TEXT_NAME = \"SelectItemText\";\nvar SelectItemText = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeSelect, className, style, ...itemTextProps } = props;\n    const context = useSelectContext(ITEM_TEXT_NAME, __scopeSelect);\n    const contentContext = useSelectContentContext(ITEM_TEXT_NAME, __scopeSelect);\n    const itemContext = useSelectItemContext(ITEM_TEXT_NAME, __scopeSelect);\n    const nativeOptionsContext = useSelectNativeOptionsContext(ITEM_TEXT_NAME, __scopeSelect);\n    const [itemTextNode, setItemTextNode] = React.useState(null);\n    const composedRefs = useComposedRefs(\n      forwardedRef,\n      (node) => setItemTextNode(node),\n      itemContext.onItemTextChange,\n      (node) => contentContext.itemTextRefCallback?.(node, itemContext.value, itemContext.disabled)\n    );\n    const textContent = itemTextNode?.textContent;\n    const nativeOption = React.useMemo(\n      () => /* @__PURE__ */ jsx(\"option\", { value: itemContext.value, disabled: itemContext.disabled, children: textContent }, itemContext.value),\n      [itemContext.disabled, itemContext.value, textContent]\n    );\n    const { onNativeOptionAdd, onNativeOptionRemove } = nativeOptionsContext;\n    useLayoutEffect(() => {\n      onNativeOptionAdd(nativeOption);\n      return () => onNativeOptionRemove(nativeOption);\n    }, [onNativeOptionAdd, onNativeOptionRemove, nativeOption]);\n    return /* @__PURE__ */ jsxs(Fragment, { children: [\n      /* @__PURE__ */ jsx(Primitive.span, { id: itemContext.textId, ...itemTextProps, ref: composedRefs }),\n      itemContext.isSelected && context.valueNode && !context.valueNodeHasChildren ? ReactDOM.createPortal(itemTextProps.children, context.valueNode) : null\n    ] });\n  }\n);\nSelectItemText.displayName = ITEM_TEXT_NAME;\nvar ITEM_INDICATOR_NAME = \"SelectItemIndicator\";\nvar SelectItemIndicator = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeSelect, ...itemIndicatorProps } = props;\n    const itemContext = useSelectItemContext(ITEM_INDICATOR_NAME, __scopeSelect);\n    return itemContext.isSelected ? /* @__PURE__ */ jsx(Primitive.span, { \"aria-hidden\": true, ...itemIndicatorProps, ref: forwardedRef }) : null;\n  }\n);\nSelectItemIndicator.displayName = ITEM_INDICATOR_NAME;\nvar SCROLL_UP_BUTTON_NAME = \"SelectScrollUpButton\";\nvar SelectScrollUpButton = React.forwardRef((props, forwardedRef) => {\n  const contentContext = useSelectContentContext(SCROLL_UP_BUTTON_NAME, props.__scopeSelect);\n  const viewportContext = useSelectViewportContext(SCROLL_UP_BUTTON_NAME, props.__scopeSelect);\n  const [canScrollUp, setCanScrollUp] = React.useState(false);\n  const composedRefs = useComposedRefs(forwardedRef, viewportContext.onScrollButtonChange);\n  useLayoutEffect(() => {\n    if (contentContext.viewport && contentContext.isPositioned) {\n      let handleScroll2 = function() {\n        const canScrollUp2 = viewport.scrollTop > 0;\n        setCanScrollUp(canScrollUp2);\n      };\n      var handleScroll = handleScroll2;\n      const viewport = contentContext.viewport;\n      handleScroll2();\n      viewport.addEventListener(\"scroll\", handleScroll2);\n      return () => viewport.removeEventListener(\"scroll\", handleScroll2);\n    }\n  }, [contentContext.viewport, contentContext.isPositioned]);\n  return canScrollUp ? /* @__PURE__ */ jsx(\n    SelectScrollButtonImpl,\n    {\n      ...props,\n      ref: composedRefs,\n      onAutoScroll: () => {\n        const { viewport, selectedItem } = contentContext;\n        if (viewport && selectedItem) {\n          viewport.scrollTop = viewport.scrollTop - selectedItem.offsetHeight;\n        }\n      }\n    }\n  ) : null;\n});\nSelectScrollUpButton.displayName = SCROLL_UP_BUTTON_NAME;\nvar SCROLL_DOWN_BUTTON_NAME = \"SelectScrollDownButton\";\nvar SelectScrollDownButton = React.forwardRef((props, forwardedRef) => {\n  const contentContext = useSelectContentContext(SCROLL_DOWN_BUTTON_NAME, props.__scopeSelect);\n  const viewportContext = useSelectViewportContext(SCROLL_DOWN_BUTTON_NAME, props.__scopeSelect);\n  const [canScrollDown, setCanScrollDown] = React.useState(false);\n  const composedRefs = useComposedRefs(forwardedRef, viewportContext.onScrollButtonChange);\n  useLayoutEffect(() => {\n    if (contentContext.viewport && contentContext.isPositioned) {\n      let handleScroll2 = function() {\n        const maxScroll = viewport.scrollHeight - viewport.clientHeight;\n        const canScrollDown2 = Math.ceil(viewport.scrollTop) < maxScroll;\n        setCanScrollDown(canScrollDown2);\n      };\n      var handleScroll = handleScroll2;\n      const viewport = contentContext.viewport;\n      handleScroll2();\n      viewport.addEventListener(\"scroll\", handleScroll2);\n      return () => viewport.removeEventListener(\"scroll\", handleScroll2);\n    }\n  }, [contentContext.viewport, contentContext.isPositioned]);\n  return canScrollDown ? /* @__PURE__ */ jsx(\n    SelectScrollButtonImpl,\n    {\n      ...props,\n      ref: composedRefs,\n      onAutoScroll: () => {\n        const { viewport, selectedItem } = contentContext;\n        if (viewport && selectedItem) {\n          viewport.scrollTop = viewport.scrollTop + selectedItem.offsetHeight;\n        }\n      }\n    }\n  ) : null;\n});\nSelectScrollDownButton.displayName = SCROLL_DOWN_BUTTON_NAME;\nvar SelectScrollButtonImpl = React.forwardRef((props, forwardedRef) => {\n  const { __scopeSelect, onAutoScroll, ...scrollIndicatorProps } = props;\n  const contentContext = useSelectContentContext(\"SelectScrollButton\", __scopeSelect);\n  const autoScrollTimerRef = React.useRef(null);\n  const getItems = useCollection(__scopeSelect);\n  const clearAutoScrollTimer = React.useCallback(() => {\n    if (autoScrollTimerRef.current !== null) {\n      window.clearInterval(autoScrollTimerRef.current);\n      autoScrollTimerRef.current = null;\n    }\n  }, []);\n  React.useEffect(() => {\n    return () => clearAutoScrollTimer();\n  }, [clearAutoScrollTimer]);\n  useLayoutEffect(() => {\n    const activeItem = getItems().find((item) => item.ref.current === document.activeElement);\n    activeItem?.ref.current?.scrollIntoView({ block: \"nearest\" });\n  }, [getItems]);\n  return /* @__PURE__ */ jsx(\n    Primitive.div,\n    {\n      \"aria-hidden\": true,\n      ...scrollIndicatorProps,\n      ref: forwardedRef,\n      style: { flexShrink: 0, ...scrollIndicatorProps.style },\n      onPointerDown: composeEventHandlers(scrollIndicatorProps.onPointerDown, () => {\n        if (autoScrollTimerRef.current === null) {\n          autoScrollTimerRef.current = window.setInterval(onAutoScroll, 50);\n        }\n      }),\n      onPointerMove: composeEventHandlers(scrollIndicatorProps.onPointerMove, () => {\n        contentContext.onItemLeave?.();\n        if (autoScrollTimerRef.current === null) {\n          autoScrollTimerRef.current = window.setInterval(onAutoScroll, 50);\n        }\n      }),\n      onPointerLeave: composeEventHandlers(scrollIndicatorProps.onPointerLeave, () => {\n        clearAutoScrollTimer();\n      })\n    }\n  );\n});\nvar SEPARATOR_NAME = \"SelectSeparator\";\nvar SelectSeparator = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeSelect, ...separatorProps } = props;\n    return /* @__PURE__ */ jsx(Primitive.div, { \"aria-hidden\": true, ...separatorProps, ref: forwardedRef });\n  }\n);\nSelectSeparator.displayName = SEPARATOR_NAME;\nvar ARROW_NAME = \"SelectArrow\";\nvar SelectArrow = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeSelect, ...arrowProps } = props;\n    const popperScope = usePopperScope(__scopeSelect);\n    const context = useSelectContext(ARROW_NAME, __scopeSelect);\n    const contentContext = useSelectContentContext(ARROW_NAME, __scopeSelect);\n    return context.open && contentContext.position === \"popper\" ? /* @__PURE__ */ jsx(PopperPrimitive.Arrow, { ...popperScope, ...arrowProps, ref: forwardedRef }) : null;\n  }\n);\nSelectArrow.displayName = ARROW_NAME;\nfunction shouldShowPlaceholder(value) {\n  return value === \"\" || value === void 0;\n}\nvar BubbleSelect = React.forwardRef(\n  (props, forwardedRef) => {\n    const { value, ...selectProps } = props;\n    const ref = React.useRef(null);\n    const composedRefs = useComposedRefs(forwardedRef, ref);\n    const prevValue = usePrevious(value);\n    React.useEffect(() => {\n      const select = ref.current;\n      const selectProto = window.HTMLSelectElement.prototype;\n      const descriptor = Object.getOwnPropertyDescriptor(\n        selectProto,\n        \"value\"\n      );\n      const setValue = descriptor.set;\n      if (prevValue !== value && setValue) {\n        const event = new Event(\"change\", { bubbles: true });\n        setValue.call(select, value);\n        select.dispatchEvent(event);\n      }\n    }, [prevValue, value]);\n    return /* @__PURE__ */ jsx(VisuallyHidden, { asChild: true, children: /* @__PURE__ */ jsx(\"select\", { ...selectProps, ref: composedRefs, defaultValue: value }) });\n  }\n);\nBubbleSelect.displayName = \"BubbleSelect\";\nfunction useTypeaheadSearch(onSearchChange) {\n  const handleSearchChange = useCallbackRef(onSearchChange);\n  const searchRef = React.useRef(\"\");\n  const timerRef = React.useRef(0);\n  const handleTypeaheadSearch = React.useCallback(\n    (key) => {\n      const search = searchRef.current + key;\n      handleSearchChange(search);\n      (function updateSearch(value) {\n        searchRef.current = value;\n        window.clearTimeout(timerRef.current);\n        if (value !== \"\") timerRef.current = window.setTimeout(() => updateSearch(\"\"), 1e3);\n      })(search);\n    },\n    [handleSearchChange]\n  );\n  const resetTypeahead = React.useCallback(() => {\n    searchRef.current = \"\";\n    window.clearTimeout(timerRef.current);\n  }, []);\n  React.useEffect(() => {\n    return () => window.clearTimeout(timerRef.current);\n  }, []);\n  return [searchRef, handleTypeaheadSearch, resetTypeahead];\n}\nfunction findNextItem(items, search, currentItem) {\n  const isRepeated = search.length > 1 && Array.from(search).every((char) => char === search[0]);\n  const normalizedSearch = isRepeated ? search[0] : search;\n  const currentItemIndex = currentItem ? items.indexOf(currentItem) : -1;\n  let wrappedItems = wrapArray(items, Math.max(currentItemIndex, 0));\n  const excludeCurrentItem = normalizedSearch.length === 1;\n  if (excludeCurrentItem) wrappedItems = wrappedItems.filter((v) => v !== currentItem);\n  const nextItem = wrappedItems.find(\n    (item) => item.textValue.toLowerCase().startsWith(normalizedSearch.toLowerCase())\n  );\n  return nextItem !== currentItem ? nextItem : void 0;\n}\nfunction wrapArray(array, startIndex) {\n  return array.map((_, index) => array[(startIndex + index) % array.length]);\n}\nvar Root2 = Select;\nvar Trigger = SelectTrigger;\nvar Value = SelectValue;\nvar Icon = SelectIcon;\nvar Portal = SelectPortal;\nvar Content2 = SelectContent;\nvar Viewport = SelectViewport;\nvar Group = SelectGroup;\nvar Label = SelectLabel;\nvar Item = SelectItem;\nvar ItemText = SelectItemText;\nvar ItemIndicator = SelectItemIndicator;\nvar ScrollUpButton = SelectScrollUpButton;\nvar ScrollDownButton = SelectScrollDownButton;\nvar Separator = SelectSeparator;\nvar Arrow2 = SelectArrow;\nexport {\n  Arrow2 as Arrow,\n  Content2 as Content,\n  Group,\n  Icon,\n  Item,\n  ItemIndicator,\n  ItemText,\n  Label,\n  Portal,\n  Root2 as Root,\n  ScrollDownButton,\n  ScrollUpButton,\n  Select,\n  SelectArrow,\n  SelectContent,\n  SelectGroup,\n  SelectIcon,\n  SelectItem,\n  SelectItemIndicator,\n  SelectItemText,\n  SelectLabel,\n  SelectPortal,\n  SelectScrollDownButton,\n  SelectScrollUpButton,\n  SelectSeparator,\n  SelectTrigger,\n  SelectValue,\n  SelectViewport,\n  Separator,\n  Trigger,\n  Value,\n  Viewport,\n  createSelectScope\n};\n//# sourceMappingURL=index.mjs.map\n", "import * as React from \"react\"\r\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\r\nimport { Check, ChevronDown, ChevronUp } from \"lucide-react\"\r\n\r\nimport { cn } from \"~/lib/utils\"\r\n\r\nconst Select = SelectPrimitive.Root\r\n\r\nconst SelectGroup = SelectPrimitive.Group\r\n\r\nconst SelectValue = SelectPrimitive.Value\r\n\r\nconst SelectTrigger = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Trigger>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Trigger>\r\n>(({ className, children, ...props }, ref) => (\r\n  <SelectPrimitive.Trigger\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    {children}\r\n    <SelectPrimitive.Icon asChild>\r\n      <ChevronDown className=\"h-4 w-4 opacity-50\" />\r\n    </SelectPrimitive.Icon>\r\n  </SelectPrimitive.Trigger>\r\n))\r\nSelectTrigger.displayName = SelectPrimitive.Trigger.displayName\r\n\r\nconst SelectScrollUpButton = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.ScrollUpButton>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollUpButton>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.ScrollUpButton\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex cursor-default items-center justify-center py-1\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    <ChevronUp className=\"h-4 w-4\" />\r\n  </SelectPrimitive.ScrollUpButton>\r\n))\r\nSelectScrollUpButton.displayName = SelectPrimitive.ScrollUpButton.displayName\r\n\r\nconst SelectScrollDownButton = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.ScrollDownButton>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollDownButton>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.ScrollDownButton\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex cursor-default items-center justify-center py-1\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    <ChevronDown className=\"h-4 w-4\" />\r\n  </SelectPrimitive.ScrollDownButton>\r\n))\r\nSelectScrollDownButton.displayName =\r\n  SelectPrimitive.ScrollDownButton.displayName\r\n\r\nconst SelectContent = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Content>\r\n>(({ className, children, position = \"popper\", ...props }, ref) => (\r\n  <SelectPrimitive.Portal>\r\n    <SelectPrimitive.Content\r\n      ref={ref}\r\n      className={cn(\r\n        \"relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\r\n        position === \"popper\" &&\r\n          \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\r\n        className\r\n      )}\r\n      position={position}\r\n      {...props}\r\n    >\r\n      <SelectScrollUpButton />\r\n      <SelectPrimitive.Viewport\r\n        className={cn(\r\n          \"p-1\",\r\n          position === \"popper\" &&\r\n            \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]\"\r\n        )}\r\n      >\r\n        {children}\r\n      </SelectPrimitive.Viewport>\r\n      <SelectScrollDownButton />\r\n    </SelectPrimitive.Content>\r\n  </SelectPrimitive.Portal>\r\n))\r\nSelectContent.displayName = SelectPrimitive.Content.displayName\r\n\r\nconst SelectLabel = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Label>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Label>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.Label\r\n    ref={ref}\r\n    className={cn(\"py-1.5 pl-8 pr-2 text-sm font-semibold\", className)}\r\n    {...props}\r\n  />\r\n))\r\nSelectLabel.displayName = SelectPrimitive.Label.displayName\r\n\r\nconst SelectItem = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Item>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Item>\r\n>(({ className, children, ...props }, ref) => (\r\n  <SelectPrimitive.Item\r\n    ref={ref}\r\n    className={cn(\r\n      \"relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\r\n      <SelectPrimitive.ItemIndicator>\r\n        <Check className=\"h-4 w-4\" />\r\n      </SelectPrimitive.ItemIndicator>\r\n    </span>\r\n\r\n    <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\r\n  </SelectPrimitive.Item>\r\n))\r\nSelectItem.displayName = SelectPrimitive.Item.displayName\r\n\r\nconst SelectSeparator = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Separator>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Separator>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.Separator\r\n    ref={ref}\r\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\r\n    {...props}\r\n  />\r\n))\r\nSelectSeparator.displayName = SelectPrimitive.Separator.displayName\r\n\r\nexport {\r\n  Select,\r\n  SelectGroup,\r\n  SelectValue,\r\n  SelectTrigger,\r\n  SelectContent,\r\n  SelectLabel,\r\n  SelectItem,\r\n  SelectSeparator,\r\n  SelectScrollUpButton,\r\n  SelectScrollDownButton,\r\n}\r\n"], "names": ["Select", "React.useState", "React.useRef", "jsx", "PopperPrimitive.Root", "jsxs", "React.useCallback", "SelectTrigger", "React.forwardRef", "PopperPrimitive.Anchor", "SelectValue", "useLayoutEffect", "Fragment", "PortalPrimitive", "SelectContent", "ReactDOM.createPortal", "React.useEffect", "RemoveScroll", "PopperPrimitive.Content", "SelectLabel", "SelectItem", "React.useMemo", "SelectScrollUpButton", "SelectScrollDownButton", "SelectSeparator", "PopperPrimitive.Arrow", "SelectPrimitive.Root", "SelectPrimitive.Value", "SelectPrimitive.Trigger", "SelectPrimitive.Icon", "SelectPrimitive.ScrollUpButton", "SelectPrimitive.ScrollDownButton", "SelectPrimitive.Portal", "SelectPrimitive.Content", "SelectPrimitive.Viewport", "SelectPrimitive.Label", "SelectPrimitive.Item", "SelectPrimitive.ItemIndicator", "SelectPrimitive.ItemText", "SelectPrimitive.Separator"], "mappings": ";;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA,MAAM,YAAY,iBAAiB,aAAa,CAAC,CAAC,QAAQ,EAAE,GAAG,kBAAkB,KAAK,SAAQ,CAAE,CAAC,CAAC;ACmBlG,IAAI,YAAY,CAAC,KAAK,SAAS,WAAW,WAAW;AACrD,IAAI,iBAAiB,CAAC,KAAK,OAAO;AAClC,IAAI,cAAc;AAClB,IAAI,CAAC,YAAY,eAAe,qBAAqB,IAAI,iBAAiB,WAAW;AACrF,IAAI,CAAC,qBAAqB,iBAAiB,IAAI,mBAAmB,aAAa;AAAA,EAC7E;AAAA,EACA;AACF,CAAC;AACD,IAAI,iBAAiB,kBAAmB;AACxC,IAAI,CAAC,gBAAgB,gBAAgB,IAAI,oBAAoB,WAAW;AACxE,IAAI,CAAC,6BAA6B,6BAA6B,IAAI,oBAAoB,WAAW;AAClG,IAAIA,WAAS,CAAC,UAAU;AACtB,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA,MAAM;AAAA,IACN;AAAA,IACA;AAAA,IACA,OAAO;AAAA,IACP;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ,IAAM;AACJ,QAAM,cAAc,eAAe,aAAa;AAChD,QAAM,CAAC,SAAS,UAAU,IAAIC,aAAAA,SAAe,IAAI;AACjD,QAAM,CAAC,WAAW,YAAY,IAAIA,aAAAA,SAAe,IAAI;AACrD,QAAM,CAAC,sBAAsB,uBAAuB,IAAIA,aAAAA,SAAe,KAAK;AAC5E,QAAM,YAAY,aAAa,GAAG;AAClC,QAAM,CAAC,OAAO,OAAO,OAAO,IAAI,qBAAqB;AAAA,IACnD,MAAM;AAAA,IACN,aAAa;AAAA,IACb,UAAU;AAAA,EACd,CAAG;AACD,QAAM,CAAC,OAAO,QAAQ,IAAI,qBAAqB;AAAA,IAC7C,MAAM;AAAA,IACN,aAAa;AAAA,IACb,UAAU;AAAA,EACd,CAAG;AACD,QAAM,2BAA2BC,aAAY,OAAC,IAAI;AAClD,QAAM,gBAAgB,UAAU,QAAQ,CAAC,CAAC,QAAQ,QAAQ,MAAM,IAAI;AACpE,QAAM,CAAC,kBAAkB,mBAAmB,IAAID,aAAAA,SAA+B,oBAAI,IAAG,CAAE;AACxF,QAAM,kBAAkB,MAAM,KAAK,gBAAgB,EAAE,IAAI,CAAC,WAAW,OAAO,MAAM,KAAK,EAAE,KAAK,GAAG;AACjG,SAAuBE,kCAAG,IAACC,SAAsB,EAAE,GAAG,aAAa,UAA0BC,kCAAI;AAAA,IAC/F;AAAA,IACA;AAAA,MACE;AAAA,MACA,OAAO;AAAA,MACP;AAAA,MACA,iBAAiB;AAAA,MACjB;AAAA,MACA,mBAAmB;AAAA,MACnB;AAAA,MACA,8BAA8B;AAAA,MAC9B,WAAW,MAAO;AAAA,MAClB;AAAA,MACA,eAAe;AAAA,MACf;AAAA,MACA,cAAc;AAAA,MACd,KAAK;AAAA,MACL;AAAA,MACA;AAAA,MACA,UAAU;AAAA,QACQF,kCAAG,IAAC,WAAW,UAAU,EAAE,OAAO,eAAe,UAA0BA,kCAAG;AAAA,UAC5F;AAAA,UACA;AAAA,YACE,OAAO,MAAM;AAAA,YACb,mBAAmBG,aAAAA,YAAkB,CAAC,WAAW;AAC/C,kCAAoB,CAAC,SAAS,IAAI,IAAI,IAAI,EAAE,IAAI,MAAM,CAAC;AAAA,YACxD,GAAE,EAAE;AAAA,YACL,sBAAsBA,aAAAA,YAAkB,CAAC,WAAW;AAClD,kCAAoB,CAAC,SAAS;AAC5B,sBAAM,aAAa,IAAI,IAAI,IAAI;AAC/B,2BAAW,OAAO,MAAM;AACxB,uBAAO;AAAA,cACvB,CAAe;AAAA,YACF,GAAE,EAAE;AAAA,YACL;AAAA,UACZ;AAAA,QACA,GAAW;AAAA,QACH,gBAAgCD,kCAAI;AAAA,UAClC;AAAA,UACA;AAAA,YACE,eAAe;AAAA,YACf;AAAA,YACA,UAAU;AAAA,YACV;AAAA,YACA;AAAA,YACA;AAAA,YACA,UAAU,CAAC,UAAU,SAAS,MAAM,OAAO,KAAK;AAAA,YAChD;AAAA,YACA;AAAA,YACA,UAAU;AAAA,cACR,UAAU,SAAyBF,sCAAI,UAAU,EAAE,OAAO,GAAI,CAAA,IAAI;AAAA,cAClE,MAAM,KAAK,gBAAgB;AAAA,YACzC;AAAA,UACW;AAAA,UACD;AAAA,QACV,IAAY;AAAA,MACZ;AAAA,IACA;AAAA,EACA,GAAK;AACL;AACAH,SAAO,cAAc;AACrB,IAAI,eAAe;AACnB,IAAIO,kBAAgBC,aAAgB;AAAA,EAClC,CAAC,OAAO,iBAAiB;AACvB,UAAM,EAAE,eAAe,WAAW,OAAO,GAAG,aAAc,IAAG;AAC7D,UAAM,cAAc,eAAe,aAAa;AAChD,UAAM,UAAU,iBAAiB,cAAc,aAAa;AAC5D,UAAM,aAAa,QAAQ,YAAY;AACvC,UAAM,eAAe,gBAAgB,cAAc,QAAQ,eAAe;AAC1E,UAAM,WAAW,cAAc,aAAa;AAC5C,UAAM,iBAAiBN,aAAY,OAAC,OAAO;AAC3C,UAAM,CAAC,WAAW,uBAAuB,cAAc,IAAI,mBAAmB,CAAC,WAAW;AACxF,YAAM,eAAe,WAAW,OAAO,CAAC,SAAS,CAAC,KAAK,QAAQ;AAC/D,YAAM,cAAc,aAAa,KAAK,CAAC,SAAS,KAAK,UAAU,QAAQ,KAAK;AAC5E,YAAM,WAAW,aAAa,cAAc,QAAQ,WAAW;AAC/D,UAAI,aAAa,QAAQ;AACvB,gBAAQ,cAAc,SAAS,KAAK;AAAA,MAC5C;AAAA,IACA,CAAK;AACD,UAAM,aAAa,CAAC,iBAAiB;AACnC,UAAI,CAAC,YAAY;AACf,gBAAQ,aAAa,IAAI;AACzB,uBAAgB;AAAA,MACxB;AACM,UAAI,cAAc;AAChB,gBAAQ,yBAAyB,UAAU;AAAA,UACzC,GAAG,KAAK,MAAM,aAAa,KAAK;AAAA,UAChC,GAAG,KAAK,MAAM,aAAa,KAAK;AAAA,QACjC;AAAA,MACT;AAAA,IACK;AACD,WAAuBC,kCAAAA,IAAIM,QAAwB,EAAE,SAAS,MAAM,GAAG,aAAa,UAA0BN,kCAAG;AAAA,MAC/G,UAAU;AAAA,MACV;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,QACN,iBAAiB,QAAQ;AAAA,QACzB,iBAAiB,QAAQ;AAAA,QACzB,iBAAiB,QAAQ;AAAA,QACzB,qBAAqB;AAAA,QACrB,KAAK,QAAQ;AAAA,QACb,cAAc,QAAQ,OAAO,SAAS;AAAA,QACtC,UAAU;AAAA,QACV,iBAAiB,aAAa,KAAK;AAAA,QACnC,oBAAoB,sBAAsB,QAAQ,KAAK,IAAI,KAAK;AAAA,QAChE,GAAG;AAAA,QACH,KAAK;AAAA,QACL,SAAS,qBAAqB,aAAa,SAAS,CAAC,UAAU;AAC7D,gBAAM,cAAc,MAAO;AAC3B,cAAI,eAAe,YAAY,SAAS;AACtC,uBAAW,KAAK;AAAA,UAC5B;AAAA,QACA,CAAS;AAAA,QACD,eAAe,qBAAqB,aAAa,eAAe,CAAC,UAAU;AACzE,yBAAe,UAAU,MAAM;AAC/B,gBAAM,SAAS,MAAM;AACrB,cAAI,OAAO,kBAAkB,MAAM,SAAS,GAAG;AAC7C,mBAAO,sBAAsB,MAAM,SAAS;AAAA,UACxD;AACU,cAAI,MAAM,WAAW,KAAK,MAAM,YAAY,SAAS,MAAM,gBAAgB,SAAS;AAClF,uBAAW,KAAK;AAChB,kBAAM,eAAgB;AAAA,UAClC;AAAA,QACA,CAAS;AAAA,QACD,WAAW,qBAAqB,aAAa,WAAW,CAAC,UAAU;AACjE,gBAAM,gBAAgB,UAAU,YAAY;AAC5C,gBAAM,gBAAgB,MAAM,WAAW,MAAM,UAAU,MAAM;AAC7D,cAAI,CAAC,iBAAiB,MAAM,IAAI,WAAW,EAAG,uBAAsB,MAAM,GAAG;AAC7E,cAAI,iBAAiB,MAAM,QAAQ,IAAK;AACxC,cAAI,UAAU,SAAS,MAAM,GAAG,GAAG;AACjC,uBAAY;AACZ,kBAAM,eAAgB;AAAA,UAClC;AAAA,QACS,CAAA;AAAA,MACT;AAAA,IACA,GAAO;AAAA,EACP;AACA;AACAI,gBAAc,cAAc;AAC5B,IAAI,aAAa;AACjB,IAAIG,gBAAcF,aAAgB;AAAA,EAChC,CAAC,OAAO,iBAAiB;AACvB,UAAM,EAAE,eAAe,WAAW,OAAO,UAAU,cAAc,IAAI,GAAG,WAAU,IAAK;AACvF,UAAM,UAAU,iBAAiB,YAAY,aAAa;AAC1D,UAAM,EAAE,6BAA4B,IAAK;AACzC,UAAM,cAAc,aAAa;AACjC,UAAM,eAAe,gBAAgB,cAAc,QAAQ,iBAAiB;AAC5EG,qBAAgB,MAAM;AACpB,mCAA6B,WAAW;AAAA,IAC9C,GAAO,CAAC,8BAA8B,WAAW,CAAC;AAC9C,WAAuBR,kCAAG;AAAA,MACxB,UAAU;AAAA,MACV;AAAA,QACE,GAAG;AAAA,QACH,KAAK;AAAA,QACL,OAAO,EAAE,eAAe,OAAQ;AAAA,QAChC,UAAU,sBAAsB,QAAQ,KAAK,IAAoBA,sCAAIS,kBAAAA,UAAU,EAAE,UAAU,YAAW,CAAE,IAAI;AAAA,MACpH;AAAA,IACK;AAAA,EACL;AACA;AACAF,cAAY,cAAc;AAC1B,IAAI,YAAY;AAChB,IAAI,aAAaF,aAAgB;AAAA,EAC/B,CAAC,OAAO,iBAAiB;AACvB,UAAM,EAAE,eAAe,UAAU,GAAG,UAAW,IAAG;AAClD,WAAuBL,kCAAG,IAAC,UAAU,MAAM,EAAE,eAAe,MAAM,GAAG,WAAW,KAAK,cAAc,UAAU,YAAY,IAAQ,CAAE;AAAA,EACvI;AACA;AACA,WAAW,cAAc;AACzB,IAAI,cAAc;AAClB,IAAI,eAAe,CAAC,UAAU;AAC5B,SAAuBA,kCAAAA,IAAIU,UAAiB,EAAE,SAAS,MAAM,GAAG,OAAO;AACzE;AACA,aAAa,cAAc;AAC3B,IAAI,eAAe;AACnB,IAAIC,kBAAgBN,aAAgB;AAAA,EAClC,CAAC,OAAO,iBAAiB;AACvB,UAAM,UAAU,iBAAiB,cAAc,MAAM,aAAa;AAClE,UAAM,CAAC,UAAU,WAAW,IAAIP,sBAAgB;AAChDU,qBAAgB,MAAM;AACpB,kBAAY,IAAI,kBAAkB;AAAA,IACnC,GAAE,EAAE;AACL,QAAI,CAAC,QAAQ,MAAM;AACjB,YAAM,OAAO;AACb,aAAO,OAAOI,gBAAqB;AAAA,QACjBZ,sCAAI,uBAAuB,EAAE,OAAO,MAAM,eAAe,UAA0BA,kCAAG,IAAC,WAAW,MAAM,EAAE,OAAO,MAAM,eAAe,UAA0BA,kCAAG,IAAC,OAAO,EAAE,UAAU,MAAM,SAAQ,CAAE,EAAG,CAAA,GAAG;AAAA,QAC7N;AAAA,MACR,IAAU;AAAA,IACV;AACI,WAAuBA,kCAAAA,IAAI,mBAAmB,EAAE,GAAG,OAAO,KAAK,cAAc;AAAA,EACjF;AACA;AACAW,gBAAc,cAAc;AAC5B,IAAI,iBAAiB;AACrB,IAAI,CAAC,uBAAuB,uBAAuB,IAAI,oBAAoB,YAAY;AACvF,IAAI,oBAAoB;AACxB,IAAI,oBAAoBN,aAAgB;AAAA,EACtC,CAAC,OAAO,iBAAiB;AACvB,UAAM;AAAA,MACJ;AAAA,MACA,WAAW;AAAA,MACX;AAAA,MACA;AAAA,MACA;AAAA;AAAA;AAAA,MAGA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;AAAA,MAEA,GAAG;AAAA,IACT,IAAQ;AACJ,UAAM,UAAU,iBAAiB,cAAc,aAAa;AAC5D,UAAM,CAAC,SAAS,UAAU,IAAIP,aAAAA,SAAe,IAAI;AACjD,UAAM,CAAC,UAAU,WAAW,IAAIA,aAAAA,SAAe,IAAI;AACnD,UAAM,eAAe,gBAAgB,cAAc,CAAC,SAAS,WAAW,IAAI,CAAC;AAC7E,UAAM,CAAC,cAAc,eAAe,IAAIA,aAAAA,SAAe,IAAI;AAC3D,UAAM,CAAC,kBAAkB,mBAAmB,IAAIA,aAAc;AAAA,MAC5D;AAAA,IACD;AACD,UAAM,WAAW,cAAc,aAAa;AAC5C,UAAM,CAAC,cAAc,eAAe,IAAIA,aAAAA,SAAe,KAAK;AAC5D,UAAM,yBAAyBC,aAAY,OAAC,KAAK;AACjDc,iBAAAA,UAAgB,MAAM;AACpB,UAAI,QAAS,QAAO,WAAW,OAAO;AAAA,IAC5C,GAAO,CAAC,OAAO,CAAC;AACZ,mBAAgB;AAChB,UAAM,aAAaV,aAAiB;AAAA,MAClC,CAAC,eAAe;AACd,cAAM,CAAC,WAAW,GAAG,SAAS,IAAI,SAAQ,EAAG,IAAI,CAAC,SAAS,KAAK,IAAI,OAAO;AAC3E,cAAM,CAAC,QAAQ,IAAI,UAAU,MAAM,EAAE;AACrC,cAAM,6BAA6B,SAAS;AAC5C,mBAAW,aAAa,YAAY;AAClC,cAAI,cAAc,2BAA4B;AAC9C,iDAAW,eAAe,EAAE,OAAO,UAAS;AAC5C,cAAI,cAAc,aAAa,SAAU,UAAS,YAAY;AAC9D,cAAI,cAAc,YAAY,SAAU,UAAS,YAAY,SAAS;AACtE,iDAAW;AACX,cAAI,SAAS,kBAAkB,2BAA4B;AAAA,QACrE;AAAA,MACO;AAAA,MACD,CAAC,UAAU,QAAQ;AAAA,IACpB;AACD,UAAM,oBAAoBA,aAAiB;AAAA,MACzC,MAAM,WAAW,CAAC,cAAc,OAAO,CAAC;AAAA,MACxC,CAAC,YAAY,cAAc,OAAO;AAAA,IACnC;AACDU,iBAAAA,UAAgB,MAAM;AACpB,UAAI,cAAc;AAChB,0BAAmB;AAAA,MAC3B;AAAA,IACA,GAAO,CAAC,cAAc,iBAAiB,CAAC;AACpC,UAAM,EAAE,cAAc,yBAAwB,IAAK;AACnDA,iBAAAA,UAAgB,MAAM;AACpB,UAAI,SAAS;AACX,YAAI,mBAAmB,EAAE,GAAG,GAAG,GAAG,EAAG;AACrC,cAAM,oBAAoB,CAAC,UAAU;;AACnC,6BAAmB;AAAA,YACjB,GAAG,KAAK,IAAI,KAAK,MAAM,MAAM,KAAK,OAAK,8BAAyB,YAAzB,mBAAkC,MAAK,EAAE;AAAA,YAChF,GAAG,KAAK,IAAI,KAAK,MAAM,MAAM,KAAK,OAAK,8BAAyB,YAAzB,mBAAkC,MAAK,EAAE;AAAA,UACjF;AAAA,QACF;AACD,cAAM,kBAAkB,CAAC,UAAU;AACjC,cAAI,iBAAiB,KAAK,MAAM,iBAAiB,KAAK,IAAI;AACxD,kBAAM,eAAgB;AAAA,UAClC,OAAiB;AACL,gBAAI,CAAC,QAAQ,SAAS,MAAM,MAAM,GAAG;AACnC,2BAAa,KAAK;AAAA,YAChC;AAAA,UACA;AACU,mBAAS,oBAAoB,eAAe,iBAAiB;AAC7D,mCAAyB,UAAU;AAAA,QACpC;AACD,YAAI,yBAAyB,YAAY,MAAM;AAC7C,mBAAS,iBAAiB,eAAe,iBAAiB;AAC1D,mBAAS,iBAAiB,aAAa,iBAAiB,EAAE,SAAS,MAAM,MAAM,MAAM;AAAA,QAC/F;AACQ,eAAO,MAAM;AACX,mBAAS,oBAAoB,eAAe,iBAAiB;AAC7D,mBAAS,oBAAoB,aAAa,iBAAiB,EAAE,SAAS,MAAM;AAAA,QAC7E;AAAA,MACT;AAAA,IACK,GAAE,CAAC,SAAS,cAAc,wBAAwB,CAAC;AACpDA,iBAAAA,UAAgB,MAAM;AACpB,YAAM,QAAQ,MAAM,aAAa,KAAK;AACtC,aAAO,iBAAiB,QAAQ,KAAK;AACrC,aAAO,iBAAiB,UAAU,KAAK;AACvC,aAAO,MAAM;AACX,eAAO,oBAAoB,QAAQ,KAAK;AACxC,eAAO,oBAAoB,UAAU,KAAK;AAAA,MAC3C;AAAA,IACP,GAAO,CAAC,YAAY,CAAC;AACjB,UAAM,CAAC,WAAW,qBAAqB,IAAI,mBAAmB,CAAC,WAAW;AACxE,YAAM,eAAe,WAAW,OAAO,CAAC,SAAS,CAAC,KAAK,QAAQ;AAC/D,YAAM,cAAc,aAAa,KAAK,CAAC,SAAS,KAAK,IAAI,YAAY,SAAS,aAAa;AAC3F,YAAM,WAAW,aAAa,cAAc,QAAQ,WAAW;AAC/D,UAAI,UAAU;AACZ,mBAAW,MAAM,SAAS,IAAI,QAAQ,MAAK,CAAE;AAAA,MACrD;AAAA,IACA,CAAK;AACD,UAAM,kBAAkBV,aAAiB;AAAA,MACvC,CAAC,MAAM,OAAO,aAAa;AACzB,cAAM,mBAAmB,CAAC,uBAAuB,WAAW,CAAC;AAC7D,cAAM,iBAAiB,QAAQ,UAAU,UAAU,QAAQ,UAAU;AACrE,YAAI,kBAAkB,kBAAkB;AACtC,0BAAgB,IAAI;AACpB,cAAI,iBAAkB,wBAAuB,UAAU;AAAA,QACjE;AAAA,MACO;AAAA,MACD,CAAC,QAAQ,KAAK;AAAA,IACf;AACD,UAAM,kBAAkBA,aAAAA,YAAkB,MAAM,mCAAS,SAAS,CAAC,OAAO,CAAC;AAC3E,UAAM,sBAAsBA,aAAiB;AAAA,MAC3C,CAAC,MAAM,OAAO,aAAa;AACzB,cAAM,mBAAmB,CAAC,uBAAuB,WAAW,CAAC;AAC7D,cAAM,iBAAiB,QAAQ,UAAU,UAAU,QAAQ,UAAU;AACrE,YAAI,kBAAkB,kBAAkB;AACtC,8BAAoB,IAAI;AAAA,QAClC;AAAA,MACO;AAAA,MACD,CAAC,QAAQ,KAAK;AAAA,IACf;AACD,UAAM,iBAAiB,aAAa,WAAW,uBAAuB;AACtE,UAAM,qBAAqB,mBAAmB,uBAAuB;AAAA,MACnE;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACN,IAAQ,CAAE;AACN,WAAuBH,kCAAG;AAAA,MACxB;AAAA,MACA;AAAA,QACE,OAAO;AAAA,QACP;AAAA,QACA;AAAA,QACA,kBAAkB;AAAA,QAClB;AAAA,QACA;AAAA,QACA,aAAa;AAAA,QACb;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,UAA0BA,kCAAAA,IAAIc,mBAAc,EAAE,IAAI,MAAM,gBAAgB,MAAM,UAA0Bd,kCAAG;AAAA,UACzG;AAAA,UACA;AAAA,YACE,SAAS;AAAA,YACT,SAAS,QAAQ;AAAA,YACjB,kBAAkB,CAAC,UAAU;AAC3B,oBAAM,eAAgB;AAAA,YACvB;AAAA,YACD,oBAAoB,qBAAqB,kBAAkB,CAAC,UAAU;;AACpE,4BAAQ,YAAR,mBAAiB,MAAM,EAAE,eAAe,KAAI;AAC5C,oBAAM,eAAgB;AAAA,YACpC,CAAa;AAAA,YACD,UAA0BA,kCAAG;AAAA,cAC3B;AAAA,cACA;AAAA,gBACE,SAAS;AAAA,gBACT,6BAA6B;AAAA,gBAC7B;AAAA,gBACA;AAAA,gBACA,gBAAgB,CAAC,UAAU,MAAM,eAAgB;AAAA,gBACjD,WAAW,MAAM,QAAQ,aAAa,KAAK;AAAA,gBAC3C,UAA0BA,kCAAG;AAAA,kBAC3B;AAAA,kBACA;AAAA,oBACE,MAAM;AAAA,oBACN,IAAI,QAAQ;AAAA,oBACZ,cAAc,QAAQ,OAAO,SAAS;AAAA,oBACtC,KAAK,QAAQ;AAAA,oBACb,eAAe,CAAC,UAAU,MAAM,eAAgB;AAAA,oBAChD,GAAG;AAAA,oBACH,GAAG;AAAA,oBACH,UAAU,MAAM,gBAAgB,IAAI;AAAA,oBACpC,KAAK;AAAA,oBACL,OAAO;AAAA;AAAA,sBAEL,SAAS;AAAA,sBACT,eAAe;AAAA;AAAA,sBAEf,SAAS;AAAA,sBACT,GAAG,aAAa;AAAA,oBACjB;AAAA,oBACD,WAAW,qBAAqB,aAAa,WAAW,CAAC,UAAU;AACjE,4BAAM,gBAAgB,MAAM,WAAW,MAAM,UAAU,MAAM;AAC7D,0BAAI,MAAM,QAAQ,MAAO,OAAM,eAAgB;AAC/C,0BAAI,CAAC,iBAAiB,MAAM,IAAI,WAAW,EAAG,uBAAsB,MAAM,GAAG;AAC7E,0BAAI,CAAC,WAAW,aAAa,QAAQ,KAAK,EAAE,SAAS,MAAM,GAAG,GAAG;AAC/D,8BAAM,QAAQ,WAAW,OAAO,CAAC,SAAS,CAAC,KAAK,QAAQ;AACxD,4BAAI,iBAAiB,MAAM,IAAI,CAAC,SAAS,KAAK,IAAI,OAAO;AACzD,4BAAI,CAAC,WAAW,KAAK,EAAE,SAAS,MAAM,GAAG,GAAG;AAC1C,2CAAiB,eAAe,MAAO,EAAC,QAAS;AAAA,wBAC3E;AACwB,4BAAI,CAAC,WAAW,WAAW,EAAE,SAAS,MAAM,GAAG,GAAG;AAChD,gCAAM,iBAAiB,MAAM;AAC7B,gCAAM,eAAe,eAAe,QAAQ,cAAc;AAC1D,2CAAiB,eAAe,MAAM,eAAe,CAAC;AAAA,wBAChF;AACwB,mCAAW,MAAM,WAAW,cAAc,CAAC;AAC3C,8BAAM,eAAgB;AAAA,sBAC9C;AAAA,oBACqB,CAAA;AAAA,kBACrB;AAAA,gBACA;AAAA,cACA;AAAA,YACA;AAAA,UACA;AAAA,QACA,EAAW,CAAA;AAAA,MACX;AAAA,IACK;AAAA,EACL;AACA;AACA,kBAAkB,cAAc;AAChC,IAAI,6BAA6B;AACjC,IAAI,4BAA4BK,aAAgB,WAAC,CAAC,OAAO,iBAAiB;AACxE,QAAM,EAAE,eAAe,UAAU,GAAG,YAAa,IAAG;AACpD,QAAM,UAAU,iBAAiB,cAAc,aAAa;AAC5D,QAAM,iBAAiB,wBAAwB,cAAc,aAAa;AAC1E,QAAM,CAAC,gBAAgB,iBAAiB,IAAIP,aAAAA,SAAe,IAAI;AAC/D,QAAM,CAAC,SAAS,UAAU,IAAIA,aAAAA,SAAe,IAAI;AACjD,QAAM,eAAe,gBAAgB,cAAc,CAAC,SAAS,WAAW,IAAI,CAAC;AAC7E,QAAM,WAAW,cAAc,aAAa;AAC5C,QAAM,0BAA0BC,aAAY,OAAC,KAAK;AAClD,QAAM,sBAAsBA,aAAY,OAAC,IAAI;AAC7C,QAAM,EAAE,UAAU,cAAc,kBAAkB,kBAAmB,IAAG;AACxE,QAAM,WAAWI,aAAAA,YAAkB,MAAM;AACvC,QAAI,QAAQ,WAAW,QAAQ,aAAa,kBAAkB,WAAW,YAAY,gBAAgB,kBAAkB;AACrH,YAAM,cAAc,QAAQ,QAAQ,sBAAuB;AAC3D,YAAM,cAAc,QAAQ,sBAAuB;AACnD,YAAM,gBAAgB,QAAQ,UAAU,sBAAuB;AAC/D,YAAM,eAAe,iBAAiB,sBAAuB;AAC7D,UAAI,QAAQ,QAAQ,OAAO;AACzB,cAAM,iBAAiB,aAAa,OAAO,YAAY;AACvD,cAAM,OAAO,cAAc,OAAO;AAClC,cAAM,YAAY,YAAY,OAAO;AACrC,cAAM,kBAAkB,YAAY,QAAQ;AAC5C,cAAM,eAAe,KAAK,IAAI,iBAAiB,YAAY,KAAK;AAChE,cAAM,YAAY,OAAO,aAAa;AACtC,cAAM,cAAc,MAAM,MAAM;AAAA,UAC9B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAMA,KAAK,IAAI,gBAAgB,YAAY,YAAY;AAAA,QAC3D,CAAS;AACD,uBAAe,MAAM,WAAW,kBAAkB;AAClD,uBAAe,MAAM,OAAO,cAAc;AAAA,MAClD,OAAa;AACL,cAAM,iBAAiB,YAAY,QAAQ,aAAa;AACxD,cAAM,QAAQ,OAAO,aAAa,cAAc,QAAQ;AACxD,cAAM,aAAa,OAAO,aAAa,YAAY,QAAQ;AAC3D,cAAM,kBAAkB,YAAY,QAAQ;AAC5C,cAAM,eAAe,KAAK,IAAI,iBAAiB,YAAY,KAAK;AAChE,cAAM,WAAW,OAAO,aAAa;AACrC,cAAM,eAAe,MAAM,OAAO;AAAA,UAChC;AAAA,UACA,KAAK,IAAI,gBAAgB,WAAW,YAAY;AAAA,QAC1D,CAAS;AACD,uBAAe,MAAM,WAAW,kBAAkB;AAClD,uBAAe,MAAM,QAAQ,eAAe;AAAA,MACpD;AACM,YAAM,QAAQ,SAAU;AACxB,YAAM,kBAAkB,OAAO,cAAc,iBAAiB;AAC9D,YAAM,cAAc,SAAS;AAC7B,YAAM,gBAAgB,OAAO,iBAAiB,OAAO;AACrD,YAAM,wBAAwB,SAAS,cAAc,gBAAgB,EAAE;AACvE,YAAM,oBAAoB,SAAS,cAAc,YAAY,EAAE;AAC/D,YAAM,2BAA2B,SAAS,cAAc,mBAAmB,EAAE;AAC7E,YAAM,uBAAuB,SAAS,cAAc,eAAe,EAAE;AACrE,YAAM,oBAAoB,wBAAwB,oBAAoB,cAAc,uBAAuB;AAC3G,YAAM,mBAAmB,KAAK,IAAI,aAAa,eAAe,GAAG,iBAAiB;AAClF,YAAM,iBAAiB,OAAO,iBAAiB,QAAQ;AACvD,YAAM,qBAAqB,SAAS,eAAe,YAAY,EAAE;AACjE,YAAM,wBAAwB,SAAS,eAAe,eAAe,EAAE;AACvE,YAAM,yBAAyB,YAAY,MAAM,YAAY,SAAS,IAAI;AAC1E,YAAM,4BAA4B,kBAAkB;AACpD,YAAM,yBAAyB,aAAa,eAAe;AAC3D,YAAM,mBAAmB,aAAa,YAAY;AAClD,YAAM,yBAAyB,wBAAwB,oBAAoB;AAC3E,YAAM,4BAA4B,oBAAoB;AACtD,YAAM,8BAA8B,0BAA0B;AAC9D,UAAI,6BAA6B;AAC/B,cAAM,aAAa,MAAM,SAAS,KAAK,iBAAiB,MAAM,MAAM,SAAS,CAAC,EAAE,IAAI;AACpF,uBAAe,MAAM,SAAS;AAC9B,cAAM,uBAAuB,QAAQ,eAAe,SAAS,YAAY,SAAS;AAClF,cAAM,mCAAmC,KAAK;AAAA,UAC5C;AAAA,UACA;AAAA,WACC,aAAa,wBAAwB,KAAK,uBAAuB;AAAA,QACnE;AACD,cAAM,SAAS,yBAAyB;AACxC,uBAAe,MAAM,SAAS,SAAS;AAAA,MAC/C,OAAa;AACL,cAAM,cAAc,MAAM,SAAS,KAAK,iBAAiB,MAAM,CAAC,EAAE,IAAI;AACtE,uBAAe,MAAM,MAAM;AAC3B,cAAM,gCAAgC,KAAK;AAAA,UACzC;AAAA,UACA,wBAAwB,SAAS;AAAA,WAChC,cAAc,qBAAqB,KAAK;AAAA,QAC1C;AACD,cAAM,SAAS,gCAAgC;AAC/C,uBAAe,MAAM,SAAS,SAAS;AACvC,iBAAS,YAAY,yBAAyB,yBAAyB,SAAS;AAAA,MACxF;AACM,qBAAe,MAAM,SAAS,GAAG,cAAc;AAC/C,qBAAe,MAAM,YAAY,mBAAmB;AACpD,qBAAe,MAAM,YAAY,kBAAkB;AACnD;AACA,4BAAsB,MAAM,wBAAwB,UAAU,IAAI;AAAA,IACxE;AAAA,EACA,GAAK;AAAA,IACD;AAAA,IACA,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,QAAQ;AAAA,IACR;AAAA,EACJ,CAAG;AACDK,mBAAgB,MAAM,YAAY,CAAC,QAAQ,CAAC;AAC5C,QAAM,CAAC,eAAe,gBAAgB,IAAIV,sBAAgB;AAC1DU,mBAAgB,MAAM;AACpB,QAAI,QAAS,kBAAiB,OAAO,iBAAiB,OAAO,EAAE,MAAM;AAAA,EACzE,GAAK,CAAC,OAAO,CAAC;AACZ,QAAM,2BAA2BL,aAAiB;AAAA,IAChD,CAAC,SAAS;AACR,UAAI,QAAQ,oBAAoB,YAAY,MAAM;AAChD,iBAAU;AACV;AACA,4BAAoB,UAAU;AAAA,MACtC;AAAA,IACK;AAAA,IACD,CAAC,UAAU,iBAAiB;AAAA,EAC7B;AACD,SAAuBH,kCAAG;AAAA,IACxB;AAAA,IACA;AAAA,MACE,OAAO;AAAA,MACP;AAAA,MACA;AAAA,MACA,sBAAsB;AAAA,MACtB,UAA0BA,kCAAG;AAAA,QAC3B;AAAA,QACA;AAAA,UACE,KAAK;AAAA,UACL,OAAO;AAAA,YACL,SAAS;AAAA,YACT,eAAe;AAAA,YACf,UAAU;AAAA,YACV,QAAQ;AAAA,UACT;AAAA,UACD,UAA0BA,kCAAG;AAAA,YAC3B,UAAU;AAAA,YACV;AAAA,cACE,GAAG;AAAA,cACH,KAAK;AAAA,cACL,OAAO;AAAA;AAAA;AAAA,gBAGL,WAAW;AAAA;AAAA,gBAEX,WAAW;AAAA,gBACX,GAAG,YAAY;AAAA,cAC/B;AAAA,YACA;AAAA,UACA;AAAA,QACA;AAAA,MACA;AAAA,IACA;AAAA,EACG;AACH,CAAC;AACD,0BAA0B,cAAc;AACxC,IAAI,uBAAuB;AAC3B,IAAI,uBAAuBK,aAAgB,WAAC,CAAC,OAAO,iBAAiB;AACnE,QAAM;AAAA,IACJ;AAAA,IACA,QAAQ;AAAA,IACR,mBAAmB;AAAA,IACnB,GAAG;AAAA,EACP,IAAM;AACJ,QAAM,cAAc,eAAe,aAAa;AAChD,SAAuBL,kCAAG;AAAA,IACxBe;AAAAA,IACA;AAAA,MACE,GAAG;AAAA,MACH,GAAG;AAAA,MACH,KAAK;AAAA,MACL;AAAA,MACA;AAAA,MACA,OAAO;AAAA;AAAA,QAEL,WAAW;AAAA,QACX,GAAG,YAAY;AAAA;AAAA,QAEf,GAAG;AAAA,UACD,2CAA2C;AAAA,UAC3C,0CAA0C;AAAA,UAC1C,2CAA2C;AAAA,UAC3C,gCAAgC;AAAA,UAChC,iCAAiC;AAAA,QAC3C;AAAA,MACA;AAAA,IACA;AAAA,EACG;AACH,CAAC;AACD,qBAAqB,cAAc;AACnC,IAAI,CAAC,wBAAwB,wBAAwB,IAAI,oBAAoB,cAAc,CAAA,CAAE;AAC7F,IAAI,gBAAgB;AACpB,IAAI,iBAAiBV,aAAgB;AAAA,EACnC,CAAC,OAAO,iBAAiB;AACvB,UAAM,EAAE,eAAe,OAAO,GAAG,cAAe,IAAG;AACnD,UAAM,iBAAiB,wBAAwB,eAAe,aAAa;AAC3E,UAAM,kBAAkB,yBAAyB,eAAe,aAAa;AAC7E,UAAM,eAAe,gBAAgB,cAAc,eAAe,gBAAgB;AAClF,UAAM,mBAAmBN,aAAY,OAAC,CAAC;AACvC,WAAuBG,kCAAI,KAACO,4BAAU,EAAE,UAAU;AAAA,MAChCT,kCAAG;AAAA,QACjB;AAAA,QACA;AAAA,UACE,yBAAyB;AAAA,YACvB,QAAQ;AAAA,UACT;AAAA,UACD;AAAA,QACV;AAAA,MACO;AAAA,MACeA,kCAAG,IAAC,WAAW,MAAM,EAAE,OAAO,eAAe,UAA0BA,kCAAG;AAAA,QACxF,UAAU;AAAA,QACV;AAAA,UACE,8BAA8B;AAAA,UAC9B,MAAM;AAAA,UACN,GAAG;AAAA,UACH,KAAK;AAAA,UACL,OAAO;AAAA;AAAA;AAAA;AAAA,YAIL,UAAU;AAAA,YACV,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA,YAKN,UAAU;AAAA,YACV,GAAG,cAAc;AAAA,UAClB;AAAA,UACD,UAAU,qBAAqB,cAAc,UAAU,CAAC,UAAU;AAChE,kBAAM,WAAW,MAAM;AACvB,kBAAM,EAAE,gBAAgB,wBAAuB,IAAK;AACpD,iBAAI,mEAAyB,YAAW,gBAAgB;AACtD,oBAAM,aAAa,KAAK,IAAI,iBAAiB,UAAU,SAAS,SAAS;AACzE,kBAAI,aAAa,GAAG;AAClB,sBAAM,kBAAkB,OAAO,cAAc,iBAAiB;AAC9D,sBAAM,eAAe,WAAW,eAAe,MAAM,SAAS;AAC9D,sBAAM,YAAY,WAAW,eAAe,MAAM,MAAM;AACxD,sBAAM,aAAa,KAAK,IAAI,cAAc,SAAS;AACnD,oBAAI,aAAa,iBAAiB;AAChC,wBAAM,aAAa,aAAa;AAChC,wBAAM,oBAAoB,KAAK,IAAI,iBAAiB,UAAU;AAC9D,wBAAM,aAAa,aAAa;AAChC,iCAAe,MAAM,SAAS,oBAAoB;AAClD,sBAAI,eAAe,MAAM,WAAW,OAAO;AACzC,6BAAS,YAAY,aAAa,IAAI,aAAa;AACnD,mCAAe,MAAM,iBAAiB;AAAA,kBAC1D;AAAA,gBACA;AAAA,cACA;AAAA,YACA;AACY,6BAAiB,UAAU,SAAS;AAAA,UACrC,CAAA;AAAA,QACX;AAAA,MACA,EAAS,CAAA;AAAA,IACT,GAAO;AAAA,EACP;AACA;AACA,eAAe,cAAc;AAC7B,IAAI,aAAa;AACjB,IAAI,CAAC,4BAA4B,qBAAqB,IAAI,oBAAoB,UAAU;AACxF,IAAI,cAAcK,aAAgB;AAAA,EAChC,CAAC,OAAO,iBAAiB;AACvB,UAAM,EAAE,eAAe,GAAG,WAAU,IAAK;AACzC,UAAM,UAAU,MAAO;AACvB,WAAuBL,kCAAG,IAAC,4BAA4B,EAAE,OAAO,eAAe,IAAI,SAAS,UAA0BA,kCAAAA,IAAI,UAAU,KAAK,EAAE,MAAM,SAAS,mBAAmB,SAAS,GAAG,YAAY,KAAK,aAAc,CAAA,GAAG;AAAA,EAC/N;AACA;AACA,YAAY,cAAc;AAC1B,IAAI,aAAa;AACjB,IAAIgB,gBAAcX,aAAgB;AAAA,EAChC,CAAC,OAAO,iBAAiB;AACvB,UAAM,EAAE,eAAe,GAAG,WAAU,IAAK;AACzC,UAAM,eAAe,sBAAsB,YAAY,aAAa;AACpE,WAAuBL,sCAAI,UAAU,KAAK,EAAE,IAAI,aAAa,IAAI,GAAG,YAAY,KAAK,aAAY,CAAE;AAAA,EACvG;AACA;AACAgB,cAAY,cAAc;AAC1B,IAAI,YAAY;AAChB,IAAI,CAAC,2BAA2B,oBAAoB,IAAI,oBAAoB,SAAS;AACrF,IAAIC,eAAaZ,aAAgB;AAAA,EAC/B,CAAC,OAAO,iBAAiB;AACvB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA,WAAW;AAAA,MACX,WAAW;AAAA,MACX,GAAG;AAAA,IACT,IAAQ;AACJ,UAAM,UAAU,iBAAiB,WAAW,aAAa;AACzD,UAAM,iBAAiB,wBAAwB,WAAW,aAAa;AACvE,UAAM,aAAa,QAAQ,UAAU;AACrC,UAAM,CAAC,WAAW,YAAY,IAAIP,aAAAA,SAAe,iBAAiB,EAAE;AACpE,UAAM,CAAC,WAAW,YAAY,IAAIA,aAAAA,SAAe,KAAK;AACtD,UAAM,eAAe;AAAA,MACnB;AAAA,MACA,CAAC,SAAI;;AAAK,oCAAe,oBAAf,wCAAiC,MAAM,OAAO;AAAA;AAAA,IACzD;AACD,UAAM,SAAS,MAAO;AACtB,UAAM,iBAAiBC,aAAY,OAAC,OAAO;AAC3C,UAAM,eAAe,MAAM;AACzB,UAAI,CAAC,UAAU;AACb,gBAAQ,cAAc,KAAK;AAC3B,gBAAQ,aAAa,KAAK;AAAA,MAClC;AAAA,IACK;AACD,QAAI,UAAU,IAAI;AAChB,YAAM,IAAI;AAAA,QACR;AAAA,MACD;AAAA,IACP;AACI,WAAuBC,kCAAG;AAAA,MACxB;AAAA,MACA;AAAA,QACE,OAAO;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,kBAAkBG,aAAAA,YAAkB,CAAC,SAAS;AAC5C,uBAAa,CAAC,kBAAkB,mBAAkB,6BAAM,gBAAe,IAAI,MAAM;AAAA,QAClF,GAAE,EAAE;AAAA,QACL,UAA0BH,kCAAG;AAAA,UAC3B,WAAW;AAAA,UACX;AAAA,YACE,OAAO;AAAA,YACP;AAAA,YACA;AAAA,YACA;AAAA,YACA,UAA0BA,kCAAG;AAAA,cAC3B,UAAU;AAAA,cACV;AAAA,gBACE,MAAM;AAAA,gBACN,mBAAmB;AAAA,gBACnB,oBAAoB,YAAY,KAAK;AAAA,gBACrC,iBAAiB,cAAc;AAAA,gBAC/B,cAAc,aAAa,YAAY;AAAA,gBACvC,iBAAiB,YAAY;AAAA,gBAC7B,iBAAiB,WAAW,KAAK;AAAA,gBACjC,UAAU,WAAW,SAAS;AAAA,gBAC9B,GAAG;AAAA,gBACH,KAAK;AAAA,gBACL,SAAS,qBAAqB,UAAU,SAAS,MAAM,aAAa,IAAI,CAAC;AAAA,gBACzE,QAAQ,qBAAqB,UAAU,QAAQ,MAAM,aAAa,KAAK,CAAC;AAAA,gBACxE,SAAS,qBAAqB,UAAU,SAAS,MAAM;AACrD,sBAAI,eAAe,YAAY,QAAS,cAAc;AAAA,gBACxE,CAAiB;AAAA,gBACD,aAAa,qBAAqB,UAAU,aAAa,MAAM;AAC7D,sBAAI,eAAe,YAAY,QAAS,cAAc;AAAA,gBACxE,CAAiB;AAAA,gBACD,eAAe,qBAAqB,UAAU,eAAe,CAAC,UAAU;AACtE,iCAAe,UAAU,MAAM;AAAA,gBACjD,CAAiB;AAAA,gBACD,eAAe,qBAAqB,UAAU,eAAe,CAAC,UAAU;;AACtE,iCAAe,UAAU,MAAM;AAC/B,sBAAI,UAAU;AACZ,yCAAe,gBAAf;AAAA,kBACpB,WAA6B,eAAe,YAAY,SAAS;AAC7C,0BAAM,cAAc,MAAM,EAAE,eAAe,KAAI,CAAE;AAAA,kBACrE;AAAA,gBACA,CAAiB;AAAA,gBACD,gBAAgB,qBAAqB,UAAU,gBAAgB,CAAC,UAAU;;AACxE,sBAAI,MAAM,kBAAkB,SAAS,eAAe;AAClD,yCAAe,gBAAf;AAAA,kBACpB;AAAA,gBACA,CAAiB;AAAA,gBACD,WAAW,qBAAqB,UAAU,WAAW,CAAC,UAAU;;AAC9D,wBAAM,kBAAgB,oBAAe,cAAf,mBAA0B,aAAY;AAC5D,sBAAI,iBAAiB,MAAM,QAAQ,IAAK;AACxC,sBAAI,eAAe,SAAS,MAAM,GAAG,EAAG,cAAc;AACtD,sBAAI,MAAM,QAAQ,IAAK,OAAM,eAAgB;AAAA,gBAC9C,CAAA;AAAA,cACjB;AAAA,YACA;AAAA,UACA;AAAA,QACA;AAAA,MACA;AAAA,IACK;AAAA,EACL;AACA;AACAiB,aAAW,cAAc;AACzB,IAAI,iBAAiB;AACrB,IAAI,iBAAiBZ,aAAgB;AAAA,EACnC,CAAC,OAAO,iBAAiB;AACvB,UAAM,EAAE,eAAe,WAAW,OAAO,GAAG,cAAe,IAAG;AAC9D,UAAM,UAAU,iBAAiB,gBAAgB,aAAa;AAC9D,UAAM,iBAAiB,wBAAwB,gBAAgB,aAAa;AAC5E,UAAM,cAAc,qBAAqB,gBAAgB,aAAa;AACtE,UAAM,uBAAuB,8BAA8B,gBAAgB,aAAa;AACxF,UAAM,CAAC,cAAc,eAAe,IAAIP,aAAAA,SAAe,IAAI;AAC3D,UAAM,eAAe;AAAA,MACnB;AAAA,MACA,CAAC,SAAS,gBAAgB,IAAI;AAAA,MAC9B,YAAY;AAAA,MACZ,CAAC,SAAS;;AAAA,oCAAe,wBAAf,wCAAqC,MAAM,YAAY,OAAO,YAAY;AAAA;AAAA,IACrF;AACD,UAAM,cAAc,6CAAc;AAClC,UAAM,eAAeoB,aAAa;AAAA,MAChC,MAAsBlB,kCAAG,IAAC,UAAU,EAAE,OAAO,YAAY,OAAO,UAAU,YAAY,UAAU,UAAU,YAAW,GAAI,YAAY,KAAK;AAAA,MAC1I,CAAC,YAAY,UAAU,YAAY,OAAO,WAAW;AAAA,IACtD;AACD,UAAM,EAAE,mBAAmB,qBAAoB,IAAK;AACpDQ,qBAAgB,MAAM;AACpB,wBAAkB,YAAY;AAC9B,aAAO,MAAM,qBAAqB,YAAY;AAAA,IAC/C,GAAE,CAAC,mBAAmB,sBAAsB,YAAY,CAAC;AAC1D,WAAuBN,kCAAI,KAACO,4BAAU,EAAE,UAAU;AAAA,MAChCT,kCAAAA,IAAI,UAAU,MAAM,EAAE,IAAI,YAAY,QAAQ,GAAG,eAAe,KAAK,cAAc;AAAA,MACnG,YAAY,cAAc,QAAQ,aAAa,CAAC,QAAQ,uBAAuBY,gBAAAA,aAAsB,cAAc,UAAU,QAAQ,SAAS,IAAI;AAAA,IACxJ,GAAO;AAAA,EACP;AACA;AACA,eAAe,cAAc;AAC7B,IAAI,sBAAsB;AAC1B,IAAI,sBAAsBP,aAAgB;AAAA,EACxC,CAAC,OAAO,iBAAiB;AACvB,UAAM,EAAE,eAAe,GAAG,mBAAkB,IAAK;AACjD,UAAM,cAAc,qBAAqB,qBAAqB,aAAa;AAC3E,WAAO,YAAY,aAA6BL,sCAAI,UAAU,MAAM,EAAE,eAAe,MAAM,GAAG,oBAAoB,KAAK,aAAY,CAAE,IAAI;AAAA,EAC7I;AACA;AACA,oBAAoB,cAAc;AAClC,IAAI,wBAAwB;AAC5B,IAAImB,yBAAuBd,aAAgB,WAAC,CAAC,OAAO,iBAAiB;AACnE,QAAM,iBAAiB,wBAAwB,uBAAuB,MAAM,aAAa;AACzF,QAAM,kBAAkB,yBAAyB,uBAAuB,MAAM,aAAa;AAC3F,QAAM,CAAC,aAAa,cAAc,IAAIP,aAAAA,SAAe,KAAK;AAC1D,QAAM,eAAe,gBAAgB,cAAc,gBAAgB,oBAAoB;AACvFU,mBAAgB,MAAM;AACpB,QAAI,eAAe,YAAY,eAAe,cAAc;AAC1D,UAAI,gBAAgB,WAAW;AAC7B,cAAM,eAAe,SAAS,YAAY;AAC1C,uBAAe,YAAY;AAAA,MAC5B;AAED,YAAM,WAAW,eAAe;AAChC,oBAAe;AACf,eAAS,iBAAiB,UAAU,aAAa;AACjD,aAAO,MAAM,SAAS,oBAAoB,UAAU,aAAa;AAAA,IACvE;AAAA,EACG,GAAE,CAAC,eAAe,UAAU,eAAe,YAAY,CAAC;AACzD,SAAO,cAA8BR,kCAAG;AAAA,IACtC;AAAA,IACA;AAAA,MACE,GAAG;AAAA,MACH,KAAK;AAAA,MACL,cAAc,MAAM;AAClB,cAAM,EAAE,UAAU,aAAY,IAAK;AACnC,YAAI,YAAY,cAAc;AAC5B,mBAAS,YAAY,SAAS,YAAY,aAAa;AAAA,QACjE;AAAA,MACA;AAAA,IACA;AAAA,EACA,IAAM;AACN,CAAC;AACDmB,uBAAqB,cAAc;AACnC,IAAI,0BAA0B;AAC9B,IAAIC,2BAAyBf,aAAgB,WAAC,CAAC,OAAO,iBAAiB;AACrE,QAAM,iBAAiB,wBAAwB,yBAAyB,MAAM,aAAa;AAC3F,QAAM,kBAAkB,yBAAyB,yBAAyB,MAAM,aAAa;AAC7F,QAAM,CAAC,eAAe,gBAAgB,IAAIP,aAAAA,SAAe,KAAK;AAC9D,QAAM,eAAe,gBAAgB,cAAc,gBAAgB,oBAAoB;AACvFU,mBAAgB,MAAM;AACpB,QAAI,eAAe,YAAY,eAAe,cAAc;AAC1D,UAAI,gBAAgB,WAAW;AAC7B,cAAM,YAAY,SAAS,eAAe,SAAS;AACnD,cAAM,iBAAiB,KAAK,KAAK,SAAS,SAAS,IAAI;AACvD,yBAAiB,cAAc;AAAA,MAChC;AAED,YAAM,WAAW,eAAe;AAChC,oBAAe;AACf,eAAS,iBAAiB,UAAU,aAAa;AACjD,aAAO,MAAM,SAAS,oBAAoB,UAAU,aAAa;AAAA,IACvE;AAAA,EACG,GAAE,CAAC,eAAe,UAAU,eAAe,YAAY,CAAC;AACzD,SAAO,gBAAgCR,kCAAG;AAAA,IACxC;AAAA,IACA;AAAA,MACE,GAAG;AAAA,MACH,KAAK;AAAA,MACL,cAAc,MAAM;AAClB,cAAM,EAAE,UAAU,aAAY,IAAK;AACnC,YAAI,YAAY,cAAc;AAC5B,mBAAS,YAAY,SAAS,YAAY,aAAa;AAAA,QACjE;AAAA,MACA;AAAA,IACA;AAAA,EACA,IAAM;AACN,CAAC;AACDoB,yBAAuB,cAAc;AACrC,IAAI,yBAAyBf,aAAgB,WAAC,CAAC,OAAO,iBAAiB;AACrE,QAAM,EAAE,eAAe,cAAc,GAAG,qBAAsB,IAAG;AACjE,QAAM,iBAAiB,wBAAwB,sBAAsB,aAAa;AAClF,QAAM,qBAAqBN,aAAY,OAAC,IAAI;AAC5C,QAAM,WAAW,cAAc,aAAa;AAC5C,QAAM,uBAAuBI,aAAAA,YAAkB,MAAM;AACnD,QAAI,mBAAmB,YAAY,MAAM;AACvC,aAAO,cAAc,mBAAmB,OAAO;AAC/C,yBAAmB,UAAU;AAAA,IACnC;AAAA,EACG,GAAE,EAAE;AACLU,eAAAA,UAAgB,MAAM;AACpB,WAAO,MAAM,qBAAsB;AAAA,EACvC,GAAK,CAAC,oBAAoB,CAAC;AACzBL,mBAAgB,MAAM;;AACpB,UAAM,aAAa,WAAW,KAAK,CAAC,SAAS,KAAK,IAAI,YAAY,SAAS,aAAa;AACxF,mDAAY,IAAI,YAAhB,mBAAyB,eAAe,EAAE,OAAO;EACrD,GAAK,CAAC,QAAQ,CAAC;AACb,SAAuBR,kCAAG;AAAA,IACxB,UAAU;AAAA,IACV;AAAA,MACE,eAAe;AAAA,MACf,GAAG;AAAA,MACH,KAAK;AAAA,MACL,OAAO,EAAE,YAAY,GAAG,GAAG,qBAAqB,MAAO;AAAA,MACvD,eAAe,qBAAqB,qBAAqB,eAAe,MAAM;AAC5E,YAAI,mBAAmB,YAAY,MAAM;AACvC,6BAAmB,UAAU,OAAO,YAAY,cAAc,EAAE;AAAA,QAC1E;AAAA,MACA,CAAO;AAAA,MACD,eAAe,qBAAqB,qBAAqB,eAAe,MAAM;;AAC5E,6BAAe,gBAAf;AACA,YAAI,mBAAmB,YAAY,MAAM;AACvC,6BAAmB,UAAU,OAAO,YAAY,cAAc,EAAE;AAAA,QAC1E;AAAA,MACA,CAAO;AAAA,MACD,gBAAgB,qBAAqB,qBAAqB,gBAAgB,MAAM;AAC9E,6BAAsB;AAAA,MACvB,CAAA;AAAA,IACP;AAAA,EACG;AACH,CAAC;AACD,IAAI,iBAAiB;AACrB,IAAIqB,oBAAkBhB,aAAgB;AAAA,EACpC,CAAC,OAAO,iBAAiB;AACvB,UAAM,EAAE,eAAe,GAAG,eAAc,IAAK;AAC7C,WAAuBL,kCAAG,IAAC,UAAU,KAAK,EAAE,eAAe,MAAM,GAAG,gBAAgB,KAAK,cAAc;AAAA,EAC3G;AACA;AACAqB,kBAAgB,cAAc;AAC9B,IAAI,aAAa;AACjB,IAAI,cAAchB,aAAgB;AAAA,EAChC,CAAC,OAAO,iBAAiB;AACvB,UAAM,EAAE,eAAe,GAAG,WAAU,IAAK;AACzC,UAAM,cAAc,eAAe,aAAa;AAChD,UAAM,UAAU,iBAAiB,YAAY,aAAa;AAC1D,UAAM,iBAAiB,wBAAwB,YAAY,aAAa;AACxE,WAAO,QAAQ,QAAQ,eAAe,aAAa,WAA2BL,kCAAAA,IAAIsB,OAAuB,EAAE,GAAG,aAAa,GAAG,YAAY,KAAK,aAAY,CAAE,IAAI;AAAA,EACrK;AACA;AACA,YAAY,cAAc;AAC1B,SAAS,sBAAsB,OAAO;AACpC,SAAO,UAAU,MAAM,UAAU;AACnC;AACA,IAAI,eAAejB,aAAgB;AAAA,EACjC,CAAC,OAAO,iBAAiB;AACvB,UAAM,EAAE,OAAO,GAAG,YAAW,IAAK;AAClC,UAAM,MAAMN,aAAY,OAAC,IAAI;AAC7B,UAAM,eAAe,gBAAgB,cAAc,GAAG;AACtD,UAAM,YAAY,YAAY,KAAK;AACnCc,iBAAAA,UAAgB,MAAM;AACpB,YAAM,SAAS,IAAI;AACnB,YAAM,cAAc,OAAO,kBAAkB;AAC7C,YAAM,aAAa,OAAO;AAAA,QACxB;AAAA,QACA;AAAA,MACD;AACD,YAAM,WAAW,WAAW;AAC5B,UAAI,cAAc,SAAS,UAAU;AACnC,cAAM,QAAQ,IAAI,MAAM,UAAU,EAAE,SAAS,MAAM;AACnD,iBAAS,KAAK,QAAQ,KAAK;AAC3B,eAAO,cAAc,KAAK;AAAA,MAClC;AAAA,IACA,GAAO,CAAC,WAAW,KAAK,CAAC;AACrB,WAAuBb,kCAAAA,IAAI,gBAAgB,EAAE,SAAS,MAAM,UAA0BA,sCAAI,UAAU,EAAE,GAAG,aAAa,KAAK,cAAc,cAAc,MAAO,CAAA,GAAG;AAAA,EACrK;AACA;AACA,aAAa,cAAc;AAC3B,SAAS,mBAAmB,gBAAgB;AAC1C,QAAM,qBAAqB,eAAe,cAAc;AACxD,QAAM,YAAYD,aAAY,OAAC,EAAE;AACjC,QAAM,WAAWA,aAAY,OAAC,CAAC;AAC/B,QAAM,wBAAwBI,aAAiB;AAAA,IAC7C,CAAC,QAAQ;AACP,YAAM,SAAS,UAAU,UAAU;AACnC,yBAAmB,MAAM;AACzB,OAAC,SAAS,aAAa,OAAO;AAC5B,kBAAU,UAAU;AACpB,eAAO,aAAa,SAAS,OAAO;AACpC,YAAI,UAAU,GAAI,UAAS,UAAU,OAAO,WAAW,MAAM,aAAa,EAAE,GAAG,GAAG;AAAA,MACnF,GAAE,MAAM;AAAA,IACV;AAAA,IACD,CAAC,kBAAkB;AAAA,EACpB;AACD,QAAM,iBAAiBA,aAAAA,YAAkB,MAAM;AAC7C,cAAU,UAAU;AACpB,WAAO,aAAa,SAAS,OAAO;AAAA,EACrC,GAAE,EAAE;AACLU,eAAAA,UAAgB,MAAM;AACpB,WAAO,MAAM,OAAO,aAAa,SAAS,OAAO;AAAA,EAClD,GAAE,EAAE;AACL,SAAO,CAAC,WAAW,uBAAuB,cAAc;AAC1D;AACA,SAAS,aAAa,OAAO,QAAQ,aAAa;AAChD,QAAM,aAAa,OAAO,SAAS,KAAK,MAAM,KAAK,MAAM,EAAE,MAAM,CAAC,SAAS,SAAS,OAAO,CAAC,CAAC;AAC7F,QAAM,mBAAmB,aAAa,OAAO,CAAC,IAAI;AAClD,QAAM,mBAAmB,cAAc,MAAM,QAAQ,WAAW,IAAI;AACpE,MAAI,eAAe,UAAU,OAAO,KAAK,IAAI,kBAAkB,CAAC,CAAC;AACjE,QAAM,qBAAqB,iBAAiB,WAAW;AACvD,MAAI,mBAAoB,gBAAe,aAAa,OAAO,CAAC,MAAM,MAAM,WAAW;AACnF,QAAM,WAAW,aAAa;AAAA,IAC5B,CAAC,SAAS,KAAK,UAAU,YAAW,EAAG,WAAW,iBAAiB,YAAa,CAAA;AAAA,EACjF;AACD,SAAO,aAAa,cAAc,WAAW;AAC/C;AACA,SAAS,UAAU,OAAO,YAAY;AACpC,SAAO,MAAM,IAAI,CAAC,GAAG,UAAU,OAAO,aAAa,SAAS,MAAM,MAAM,CAAC;AAC3E;AACA,IAAI,QAAQhB;AACZ,IAAI,UAAUO;AACd,IAAI,QAAQG;AACZ,IAAI,OAAO;AACX,IAAI,SAAS;AACb,IAAI,WAAWI;AACf,IAAI,WAAW;AAEf,IAAI,QAAQK;AACZ,IAAI,OAAOC;AACX,IAAI,WAAW;AACf,IAAI,gBAAgB;AACpB,IAAI,iBAAiBE;AACrB,IAAI,mBAAmBC;AACvB,IAAI,YAAYC;ACpnChB,MAAM,SAASE;AAIf,MAAM,cAAcC;AAEd,MAAA,gBAAgBnB,aAAAA,WAGpB,CAAC,EAAE,WAAW,UAAU,GAAG,MAAM,GAAG,QACpCH,kCAAA;AAAA,EAACuB;AAAAA,EAAA;AAAA,IACC;AAAA,IACA,WAAW;AAAA,MACT;AAAA,MACA;AAAA,IACF;AAAA,IACC,GAAG;AAAA,IAEH,UAAA;AAAA,MAAA;AAAA,MACDzB,kCAAAA,IAAC0B,MAAA,EAAqB,SAAO,MAC3B,UAAC1B,kCAAA,IAAA,aAAA,EAAY,WAAU,qBAAqB,CAAA,EAC9C,CAAA;AAAA,IAAA;AAAA,EAAA;AACF,CACD;AACD,cAAc,cAAcyB,QAAwB;AAEpD,MAAM,uBAAuBpB,aAG3B,WAAA,CAAC,EAAE,WAAW,GAAG,MAAM,GAAG,QAC1BL,kCAAA;AAAA,EAAC2B;AAAAA,EAAA;AAAA,IACC;AAAA,IACA,WAAW;AAAA,MACT;AAAA,MACA;AAAA,IACF;AAAA,IACC,GAAG;AAAA,IAEJ,UAAA3B,kCAAAA,IAAC,WAAU,EAAA,WAAU,UAAU,CAAA;AAAA,EAAA;AACjC,CACD;AACD,qBAAqB,cAAc2B,eAA+B;AAElE,MAAM,yBAAyBtB,aAG7B,WAAA,CAAC,EAAE,WAAW,GAAG,MAAM,GAAG,QAC1BL,kCAAA;AAAA,EAAC4B;AAAAA,EAAA;AAAA,IACC;AAAA,IACA,WAAW;AAAA,MACT;AAAA,MACA;AAAA,IACF;AAAA,IACC,GAAG;AAAA,IAEJ,UAAA5B,kCAAAA,IAAC,aAAY,EAAA,WAAU,UAAU,CAAA;AAAA,EAAA;AACnC,CACD;AACD,uBAAuB,cACrB4B,iBAAiC;AAEnC,MAAM,gBAAgBvB,aAAAA,WAGpB,CAAC,EAAE,WAAW,UAAU,WAAW,UAAU,GAAG,SAAS,QACxDL,kCAAAA,IAAA6B,QAAA,EACC,UAAA3B,kCAAA;AAAA,EAAC4B;AAAAA,EAAA;AAAA,IACC;AAAA,IACA,WAAW;AAAA,MACT;AAAA,MACA,aAAa,YACX;AAAA,MACF;AAAA,IACF;AAAA,IACA;AAAA,IACC,GAAG;AAAA,IAEJ,UAAA;AAAA,MAAA9B,kCAAA,IAAC,sBAAqB,EAAA;AAAA,MACtBA,kCAAA;AAAA,QAAC+B;AAAAA,QAAA;AAAA,UACC,WAAW;AAAA,YACT;AAAA,YACA,aAAa,YACX;AAAA,UACJ;AAAA,UAEC;AAAA,QAAA;AAAA,MACH;AAAA,4CACC,wBAAuB,CAAA,CAAA;AAAA,IAAA;AAAA,EAAA;AAC1B,GACF,CACD;AACD,cAAc,cAAcD,SAAwB;AAEpD,MAAM,cAAczB,aAGlB,WAAA,CAAC,EAAE,WAAW,GAAG,MAAM,GAAG,QAC1BL,kCAAA;AAAA,EAACgC;AAAAA,EAAA;AAAA,IACC;AAAA,IACA,WAAW,GAAG,0CAA0C,SAAS;AAAA,IAChE,GAAG;AAAA,EAAA;AACN,CACD;AACD,YAAY,cAAcA,MAAsB;AAE1C,MAAA,aAAa3B,aAAAA,WAGjB,CAAC,EAAE,WAAW,UAAU,GAAG,MAAM,GAAG,QACpCH,kCAAA;AAAA,EAAC+B;AAAAA,EAAA;AAAA,IACC;AAAA,IACA,WAAW;AAAA,MACT;AAAA,MACA;AAAA,IACF;AAAA,IACC,GAAG;AAAA,IAEJ,UAAA;AAAA,MAAAjC,kCAAA,IAAC,QAAK,EAAA,WAAU,gEACd,UAAAA,kCAAAA,IAACkC,eAAA,EACC,UAAAlC,kCAAAA,IAAC,OAAM,EAAA,WAAU,UAAU,CAAA,EAC7B,CAAA,GACF;AAAA,MAECA,sCAAAmC,UAAA,EAA0B,SAAS,CAAA;AAAA,IAAA;AAAA,EAAA;AACtC,CACD;AACD,WAAW,cAAcF,KAAqB;AAE9C,MAAM,kBAAkB5B,aAGtB,WAAA,CAAC,EAAE,WAAW,GAAG,MAAM,GAAG,QAC1BL,kCAAA;AAAA,EAACoC;AAAAA,EAAA;AAAA,IACC;AAAA,IACA,WAAW,GAAG,4BAA4B,SAAS;AAAA,IAClD,GAAG;AAAA,EAAA;AACN,CACD;AACD,gBAAgB,cAAcA,UAA0B;", "x_google_ignoreList": [0, 1]}