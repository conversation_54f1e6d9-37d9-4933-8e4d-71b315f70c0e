import { r as reactExports, j as jsxRuntimeExports } from "./jsx-runtime-bB2y7OuD.js";
import { B as Button } from "./button-ByAXMyvk.js";
import { P as Popover, a as PopoverTrigger, b as PopoverContent } from "./popover-CD2vRFIm.js";
import { C as Calendar, a as Calendar$1 } from "./calendar-_8-DqkPN.js";
import { T as Tabs, a as TabsList, b as TabsTrigger, c as TabsContent } from "./tabs-CfSdyzWr.js";
import { P as Pagination, a as PaginationContent, b as PaginationItem, c as PaginationPrevious, d as PaginationLink, e as PaginationNext } from "./pagination-DzgbTb6G.js";
import { R as ResponsiveTable } from "./responsiveTable-CMOWL3Wl.js";
import { c as cn } from "./utils-GkgzjW3c.js";
import { a as useNavigate } from "./index-DhHTcibu.js";
import { u as useLoaderData, b as useSearchParams } from "./components-D7UvGag_.js";
import { A as ArrowLeft } from "./arrow-left-D_Ztdrp5.js";
import { f as format } from "./format-82yT_5--.js";
import "./index-z_byfFrQ.js";
import "./index-ImHKLo0a.js";
import "./index-D7VH9Fc8.js";
import "./index-BXzPK7u0.js";
import "./index-CzZ6EYkN.js";
import "./index-Vp2vNLNM.js";
import "./index-CEHS9zzk.js";
import "./index-DVTNuYOr.js";
import "./index-DscYByPT.js";
import "./index-CkL5tk39.js";
import "./index-QLGF6kQx.js";
import "./index-dIGjFc0I.js";
import "./createLucideIcon-uwkRm45G.js";
import "./addMonths-Dj4hq91A.js";
import "./isSameDay-BQMn9z7h.js";
import "./addDays-CyH8qBoF.js";
import "./chevron-right-B-tR7Kir.js";
import "./chevron-left-CLqBlTg1.js";
import "./index-CG37gmC0.js";
import "./index-qCtcpOcW.js";
import "./index-BTdCMChR.js";
function AgentWiseSales() {
  const navigate = useNavigate();
  const {
    salesData,
    sellerId,
    selectedTab,
    AgentId,
    name,
    areaId,
    sellerRole
  } = useLoaderData();
  const [activeTab, setActiveTab] = reactExports.useState(selectedTab === "localityWise" ? "sellerWise" : selectedTab);
  const [currentPage, setCurrentPage] = reactExports.useState(0);
  const [searchParams] = useSearchParams();
  const selectedDate = searchParams.get("date");
  const [date, setDate] = reactExports.useState(
    selectedDate ? new Date(selectedDate) : /* @__PURE__ */ new Date()
    // Parse the date if present; use the current date otherwise
  );
  const startDate = searchParams.get("from");
  const endDate = searchParams.get("to");
  const [dateRange, setDateRange] = reactExports.useState({
    from: startDate ? new Date(startDate) : /* @__PURE__ */ new Date(),
    to: endDate ? new Date(endDate) : /* @__PURE__ */ new Date()
  });
  const itemsPerPage = 20;
  const handleTabChange = (newTab) => {
    if (!dateRange.from) return;
    const formattedFrom = format(dateRange.from, "yyyy-MM-dd");
    const formattedTo = dateRange.to ? format(dateRange.to, "yyyy-MM-dd") : formattedFrom;
    setActiveTab(newTab);
    navigate(`?activeTab=${newTab}&page=${currentPage}&pageSize=${itemsPerPage}&from=${formattedFrom}&to=${formattedTo}&agentId=${AgentId}&sellerId=${sellerId}&selectedTab=${selectedTab}&name=${name}&areaId=${areaId}`);
  };
  const handlePageChange = (newPage) => {
    if (!dateRange.from) return;
    const formattedFrom = format(dateRange.from, "yyyy-MM-dd");
    const formattedTo = dateRange.to ? format(dateRange.to, "yyyy-MM-dd") : formattedFrom;
    setCurrentPage(newPage);
    navigate(`?activeTab=${activeTab}&page=${newPage}&pageSize=${itemsPerPage}&from=${formattedFrom}&to=${formattedTo}&agentId=${AgentId}&sellerId=${sellerId}&selectedTab=${selectedTab}&name=${name}&areaId=${areaId}`);
  };
  const totalDeliveredWt = (salesData == null ? void 0 : salesData.salesData) && Array.isArray(salesData.salesData) ? salesData.salesData.map((x) => (x == null ? void 0 : x.deliveredWeight) || 0).reduce((acc, wet) => acc + wet, 0) : 0;
  const totalReturnsWt = (salesData == null ? void 0 : salesData.salesData) && Array.isArray(salesData.salesData) ? salesData.salesData.map((x) => (x == null ? void 0 : x.returnedWeight) || 0).reduce((a, b) => a + b, 0) : 0;
  const totalBookingWt = (salesData == null ? void 0 : salesData.salesData) && Array.isArray(salesData.salesData) ? salesData.salesData.map((x) => (x == null ? void 0 : x.bookedWeight) || 0).reduce((a, b) => a + b, 0) : 0;
  const totalCancelWt = (salesData == null ? void 0 : salesData.salesData) && Array.isArray(salesData.salesData) ? salesData.salesData.map((x) => (x == null ? void 0 : x.cancelledWeight) || 0).reduce((a, b) => a + b, 0) : 0;
  const sellerWiseHeaders = ["Seller ID", "Seller Name", "Booked Qty", "Delivered Qty", "Return Qty", "Cancel Qty", "Total Shops", "Ordered Shops", "Active Shops"];
  const AgentWiseHeaders = ["Agent ID", "Agent Name", "Booked Qty", "Delivered Qty", "Return Qty", "Cancel Qty", "Total Shops", "Ordered Shops", "Active Shops"];
  const sellerAgentWiseHeaders = ["Agent ID", "Agent Name", "Booked Qty", "Delivered Qty", "Return Qty", "Cancel Qty", "Ordered Shops", "BookedAmt", "DeliveredAmt"];
  const LocalityWise = ["Locality ID", "Locality Name", "Booked Qty", "Delivered Qty", "Return Qty", "Cancel Qty", "Total Shops", "Ordered Shops", "Active Shops"];
  const sellerLocalityWise = ["Locality ID", "Locality Name", "Booked Qty", "Delivered Qty", "Return Qty", "Cancel Qty", "Active Shops", "BookedAmt", "DeliveredAmt"];
  const footerTotals = ["", "", totalBookingWt.toFixed(2), totalDeliveredWt.toFixed(2), totalReturnsWt.toFixed(2), totalCancelWt.toFixed(2)];
  const handleViewSales = () => {
    if (!dateRange.from) return;
    const formattedFrom = format(dateRange.from, "yyyy-MM-dd");
    const formattedTo = dateRange.to ? format(dateRange.to, "yyyy-MM-dd") : formattedFrom;
    navigate(`?activeTab=${activeTab}&page=${currentPage}&pageSize=${itemsPerPage}&from=${formattedFrom}&to=${formattedTo}&agentId=${AgentId}&sellerId=${sellerId}&selectedTab=${selectedTab}&name=${name}&areaId=${areaId}`);
  };
  const handleDateChange = (range) => {
    if (!(range == null ? void 0 : range.from)) return;
    setDateRange({
      from: range.from,
      to: range.to || void 0
      // If 'to' is not selected, keep it undefined
    });
  };
  return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
    className: "container mx-auto w-full p-6",
    children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
      className: " items-center mb-6",
      children: [/* @__PURE__ */ jsxRuntimeExports.jsxs(Button, {
        variant: "ghost",
        size: "sm",
        onClick: () => navigate(`/home/<USER>"yyyy-MM-dd")}`),
        children: [/* @__PURE__ */ jsxRuntimeExports.jsx(ArrowLeft, {
          className: "h-4 w-4 mr-2"
        }), "Back to Sales Report"]
      }), /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
        className: "text-muted-foreground",
        children: "/"
      }), /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
        className: "font-semibold",
        children: name
      })]
    }), /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
      className: "flex flex-col space-y-5 md:flex-row md:space-x-5 md:space-y-0 my-5",
      children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
        className: "grid grid-cols-1 gap-3 md:flex md:items-center md:space-x-2",
        children: [/* @__PURE__ */ jsxRuntimeExports.jsx("div", {
          className: "w-full md:w-auto",
          children: /* @__PURE__ */ jsxRuntimeExports.jsxs(Popover, {
            children: [/* @__PURE__ */ jsxRuntimeExports.jsx(PopoverTrigger, {
              asChild: true,
              children: /* @__PURE__ */ jsxRuntimeExports.jsxs(Button, {
                id: "date",
                variant: "outline",
                className: cn("w-[300px] justify-start text-left font-normal", !dateRange.from && "text-muted-foreground"),
                children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Calendar, {}), (dateRange == null ? void 0 : dateRange.from) ? dateRange.to ? /* @__PURE__ */ jsxRuntimeExports.jsxs(jsxRuntimeExports.Fragment, {
                  children: [format(dateRange.from, "LLL dd, y"), " - ", format(dateRange.to, "LLL dd, y")]
                }) : format(dateRange.from, "LLL dd, y") : /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                  children: "Pick a date"
                })]
              })
            }), /* @__PURE__ */ jsxRuntimeExports.jsx(PopoverContent, {
              className: "w-auto p-0",
              align: "start",
              children: /* @__PURE__ */ jsxRuntimeExports.jsx(Calendar$1, {
                initialFocus: true,
                selected: dateRange,
                mode: "range",
                onSelect: handleDateChange
              })
            })]
          })
        }), /* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
          type: "submit",
          className: "w-full md:w-auto md:rounded-full",
          onClick: () => handleViewSales(),
          children: "View Report"
        })]
      })
    }), /* @__PURE__ */ jsxRuntimeExports.jsxs(Tabs, {
      value: activeTab,
      onValueChange: handleTabChange,
      className: "my-5",
      children: [/* @__PURE__ */ jsxRuntimeExports.jsxs(TabsList, {
        children: [!sellerRole && (selectedTab === "sellerWise" || selectedTab === "localityWise") && /* @__PURE__ */ jsxRuntimeExports.jsx(TabsTrigger, {
          value: "sellerWise",
          children: "Seller Wise"
        }), !sellerRole && (selectedTab === "agentWise" || selectedTab === "localityWise") && /* @__PURE__ */ jsxRuntimeExports.jsx(TabsTrigger, {
          value: "agentWise",
          children: "Agent Wise"
        }), selectedTab !== "localityWise" && /* @__PURE__ */ jsxRuntimeExports.jsx(TabsTrigger, {
          value: "localityWise",
          children: "Locality Wise"
        }), "                        "]
      }), /* @__PURE__ */ jsxRuntimeExports.jsx(TabsContent, {
        value: "sellerWise",
        children: /* @__PURE__ */ jsxRuntimeExports.jsx(ResponsiveTable, {
          headers: sellerWiseHeaders,
          data: salesData.salesData,
          renderRow: (row) => /* @__PURE__ */ jsxRuntimeExports.jsxs("tr", {
            className: "border-b",
            children: [/* @__PURE__ */ jsxRuntimeExports.jsx("td", {
              className: "py-2 px-3 font-medium text-left",
              children: row.id
            }), /* @__PURE__ */ jsxRuntimeExports.jsx("td", {
              className: "py-2 px-3  text-left",
              children: row.name
            }), /* @__PURE__ */ jsxRuntimeExports.jsx("td", {
              className: "py-2 px-3 text-right",
              children: (row == null ? void 0 : row.bookedWeight.toFixed(1)) || "-"
            }), /* @__PURE__ */ jsxRuntimeExports.jsx("td", {
              className: "py-2 px-3 text-right",
              children: (row == null ? void 0 : row.deliveredWeight.toFixed(1)) || "-"
            }), /* @__PURE__ */ jsxRuntimeExports.jsx("td", {
              className: "py-2 px-3 text-right",
              children: (row == null ? void 0 : row.returnedWeight.toFixed(1)) || "-"
            }), /* @__PURE__ */ jsxRuntimeExports.jsx("td", {
              className: "py-2 px-3 text-right",
              children: (row == null ? void 0 : row.cancelledWeight.toFixed(1)) || "-"
            }), /* @__PURE__ */ jsxRuntimeExports.jsx("td", {
              className: "py-2 px-3 text-right",
              children: (row == null ? void 0 : row.customerCount) || "-"
            }), /* @__PURE__ */ jsxRuntimeExports.jsx("td", {
              className: "py-2 px-3 text-right",
              children: (row == null ? void 0 : row.orderCount) || "-"
            }), /* @__PURE__ */ jsxRuntimeExports.jsx("td", {
              className: "py-2 px-3 text-right",
              children: (row == null ? void 0 : row.activeShopCount) || "-"
            })]
          }, row.id),
          footerTotals,
          emptyMessage: "No data available for the selected filters."
        })
      }), /* @__PURE__ */ jsxRuntimeExports.jsx(TabsContent, {
        value: "agentWise",
        children: /* @__PURE__ */ jsxRuntimeExports.jsx(ResponsiveTable, {
          headers: sellerRole ? sellerAgentWiseHeaders : AgentWiseHeaders,
          data: salesData.salesData,
          renderRow: (row) => /* @__PURE__ */ jsxRuntimeExports.jsxs("tr", {
            className: "border-b",
            children: [/* @__PURE__ */ jsxRuntimeExports.jsx("td", {
              className: "py-2 px-3 font-medium text-left",
              children: row.id
            }), /* @__PURE__ */ jsxRuntimeExports.jsx("td", {
              className: "py-2 px-3  text-left",
              children: row.name
            }), /* @__PURE__ */ jsxRuntimeExports.jsx("td", {
              className: "py-2 px-3 text-right",
              children: (row == null ? void 0 : row.bookedWeight.toFixed(1)) || "-"
            }), /* @__PURE__ */ jsxRuntimeExports.jsx("td", {
              className: "py-2 px-3 text-right",
              children: (row == null ? void 0 : row.deliveredWeight.toFixed(1)) || "-"
            }), /* @__PURE__ */ jsxRuntimeExports.jsx("td", {
              className: "py-2 px-3 text-right",
              children: (row == null ? void 0 : row.returnedWeight.toFixed(1)) || "-"
            }), /* @__PURE__ */ jsxRuntimeExports.jsx("td", {
              className: "py-2 px-3 text-right",
              children: (row == null ? void 0 : row.cancelledWeight.toFixed(1)) || "-"
            }), !sellerRole && /* @__PURE__ */ jsxRuntimeExports.jsx("td", {
              className: "py-2 px-3 text-right",
              children: (row == null ? void 0 : row.customerCount) || "-"
            }), /* @__PURE__ */ jsxRuntimeExports.jsx("td", {
              className: "py-2 px-3 text-right",
              children: (row == null ? void 0 : row.orderCount) || "-"
            }), /* @__PURE__ */ jsxRuntimeExports.jsx("td", {
              className: "py-2 px-3 text-right",
              children: (row == null ? void 0 : row.activeShopCount) || "-"
            }), sellerRole && /* @__PURE__ */ jsxRuntimeExports.jsx("td", {
              className: "py-2 px-3 text-right",
              children: (row == null ? void 0 : row.bookedAmount.toFixed(2)) || "-"
            }), sellerRole && /* @__PURE__ */ jsxRuntimeExports.jsx("td", {
              className: "py-2 px-3 text-right",
              children: (row == null ? void 0 : row.deliveredAmount.toFixed(2)) || "-"
            })]
          }, row.id),
          footerTotals,
          emptyMessage: "No data available for the selected filters."
        })
      }), /* @__PURE__ */ jsxRuntimeExports.jsx(TabsContent, {
        value: "localityWise",
        children: /* @__PURE__ */ jsxRuntimeExports.jsx(ResponsiveTable, {
          headers: sellerRole ? sellerLocalityWise : LocalityWise,
          data: salesData.salesData,
          renderRow: (row) => /* @__PURE__ */ jsxRuntimeExports.jsxs("tr", {
            className: "border-b",
            children: [/* @__PURE__ */ jsxRuntimeExports.jsx("td", {
              className: "py-2 px-3 font-medium text-left",
              children: row.id
            }), /* @__PURE__ */ jsxRuntimeExports.jsx("td", {
              className: "py-2 px-3  text-left",
              children: row.name
            }), /* @__PURE__ */ jsxRuntimeExports.jsx("td", {
              className: "py-2 px-3 text-right",
              children: (row == null ? void 0 : row.bookedWeight.toFixed(1)) || "-"
            }), /* @__PURE__ */ jsxRuntimeExports.jsx("td", {
              className: "py-2 px-3 text-right",
              children: (row == null ? void 0 : row.deliveredWeight.toFixed(1)) || "-"
            }), /* @__PURE__ */ jsxRuntimeExports.jsx("td", {
              className: "py-2 px-3 text-right",
              children: (row == null ? void 0 : row.returnedWeight.toFixed(1)) || "-"
            }), /* @__PURE__ */ jsxRuntimeExports.jsx("td", {
              className: "py-2 px-3 text-right",
              children: (row == null ? void 0 : row.cancelledWeight.toFixed(1)) || "-"
            }), sellerRole && /* @__PURE__ */ jsxRuntimeExports.jsx("td", {
              className: "py-2 px-3 text-right",
              children: (row == null ? void 0 : row.customerCount) || "-"
            }), /* @__PURE__ */ jsxRuntimeExports.jsx("td", {
              className: "py-2 px-3 text-right",
              children: (row == null ? void 0 : row.orderCount) || "-"
            }), !sellerRole && /* @__PURE__ */ jsxRuntimeExports.jsx("td", {
              className: "py-2 px-3 text-right",
              children: (row == null ? void 0 : row.activeShopCount) || "-"
            }), sellerRole && /* @__PURE__ */ jsxRuntimeExports.jsx("td", {
              className: "py-2 px-3 text-right",
              children: (row == null ? void 0 : row.bookedAmount.toFixed(2)) || "-"
            }), sellerRole && /* @__PURE__ */ jsxRuntimeExports.jsx("td", {
              className: "py-2 px-3 text-right",
              children: (row == null ? void 0 : row.deliveredAmount.toFixed(2)) || "-"
            })]
          }, row.id),
          footerTotals,
          emptyMessage: "No data available for the selected filters."
        })
      })]
    }), /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
      className: "flex justify-between items-center mt-6 overflow-hidden ",
      children: /* @__PURE__ */ jsxRuntimeExports.jsx(Pagination, {
        children: /* @__PURE__ */ jsxRuntimeExports.jsxs(PaginationContent, {
          children: [currentPage > 0 && /* @__PURE__ */ jsxRuntimeExports.jsx(PaginationItem, {
            children: /* @__PURE__ */ jsxRuntimeExports.jsx(PaginationPrevious, {
              onClick: () => handlePageChange(currentPage - 1)
            })
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(PaginationItem, {
            children: /* @__PURE__ */ jsxRuntimeExports.jsx(PaginationLink, {
              children: currentPage + 1
            })
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(PaginationItem, {
            children: /* @__PURE__ */ jsxRuntimeExports.jsx(PaginationNext, {
              onClick: () => handlePageChange(currentPage + 1)
            })
          })]
        })
      })
    })]
  });
}
export {
  AgentWiseSales as default
};
//# sourceMappingURL=home.agentWiseSales-BcmHcWmz.js.map
