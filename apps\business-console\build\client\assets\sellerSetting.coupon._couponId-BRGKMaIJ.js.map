{"version": 3, "file": "sellerSetting.coupon._couponId-BRGKMaIJ.js", "sources": ["../../../app/routes/sellerSetting.coupon.$couponId.tsx"], "sourcesContent": ["import type { LoaderFunction } from \"@remix-run/node\";\r\nimport { json, useF<PERSON>cher, use<PERSON>oader<PERSON><PERSON>, useNavigate } from \"@remix-run/react\";\r\nimport { CalendarIcon, PlusIcon, Trash2Icon } from \"lucide-react\";\r\nimport { useEffect, useMemo, useState } from \"react\";\r\nimport { Button } from \"~/components/ui/button\";\r\nimport { Calendar } from \"~/components/ui/calendar\";\r\nimport { Input } from \"~/components/ui/input\";\r\nimport {\r\n  Popover,\r\n  PopoverContent,\r\n  PopoverTrigger,\r\n} from \"~/components/ui/popover\";\r\nimport {\r\n  Select,\r\n  SelectTrigger,\r\n  SelectValue,\r\n  SelectContent,\r\n  SelectItem,\r\n} from \"~/components/ui/select\";\r\nimport { editCoupon, getCoupon, getCouponPreload } from \"~/services/coupons\";\r\nimport { withAuth, withResponse } from \"~/utils/auth-utils\";\r\nimport { format } from \"date-fns\";\r\nimport { useToast } from \"~/components/ui/ToastProvider\";\r\nimport { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from \"~/components/ui/tabs\";\r\nimport {\r\n  type CouponPreloadType,\r\n  type CouponPayloadType,\r\n  type CouponType,\r\n  type IdConfigItem,\r\n  type ConfigKey,\r\n  type ConfigAdditionalKey,\r\n  type ConfigItem,\r\n  type CouponDetailsType,\r\n  configKeysEnum,\r\n  DISCOUNT_UNIT_MAP,\r\n} from \"~/types/api/businessConsoleService/coupons\";\r\n\r\ninterface Loaderdata {\r\n  preloadData: CouponPreloadType;\r\n  couponData: CouponType;\r\n}\r\n\r\nexport const loader: LoaderFunction = withAuth(async ({ params, request }) => {\r\n  const couponId = params?.couponId;\r\n  if (!couponId) {\r\n    throw new Response(\"Coupon ID is required\", { status: 400 });\r\n  }\r\n\r\n  try {\r\n    const preloadResponse = await getCouponPreload(request);\r\n    const couponResponse = await getCoupon(Number(couponId), request);\r\n    return withResponse(\r\n      { preloadData: preloadResponse.data, couponData: couponResponse.data },\r\n      couponResponse.headers\r\n    );\r\n  } catch (error) {\r\n    console.error(\"Error in loader:\", error);\r\n    throw new Response(\"Failed to fetch coupon data\", {\r\n      status: 500,\r\n    });\r\n  }\r\n});\r\n\r\nexport const action = withAuth(async ({ request }) => {\r\n  const formData = await request.formData();\r\n  const actionType = formData.get(\"actionType\");\r\n  const couponId = Number(formData.get(\"couponId\"));\r\n  const payloadRaw = formData.get(\"payload\");\r\n\r\n  if (actionType === \"editCoupon\") {\r\n    try {\r\n      const parsedPayload = JSON.parse(payloadRaw as string);\r\n      const requestPayload: CouponType = transformToUpdateCoupon(\r\n        parsedPayload,\r\n        couponId\r\n      );\r\n      const response = await editCoupon(couponId, requestPayload, request);\r\n      return withResponse(\r\n        { data: response.data, success: true },\r\n        response.headers\r\n      );\r\n    } catch (error) {\r\n      console.error(\"Error in action:\", error);\r\n      return json({ data: null, success: false }, { status: 500 });\r\n    }\r\n  }\r\n\r\n  console.log(\"Invalid action type:\", actionType);\r\n  return json({ data: null, success: false }, { status: 400 });\r\n});\r\n\r\nfunction transformToUpdateCoupon(\r\n  payload: CouponPayloadType,\r\n  couponId: number\r\n): CouponType {\r\n  const convertItems = (\r\n    base?: IdConfigItem[],\r\n    additional?: IdConfigItem[]\r\n  ): { property: string; operator: string; value: string }[] => {\r\n    const allItems = [...(base || []), ...(additional || [])];\r\n\r\n    return allItems\r\n      .filter((item) => item.value !== undefined && item.value !== \"\")\r\n      .map((item) => ({\r\n        property: item.property,\r\n        operator: item.operator?.trim() || \"EQ\",\r\n        value: item.value,\r\n      }));\r\n  };\r\n\r\n  return {\r\n    id: couponId,\r\n    code: payload.couponCode || \"\",\r\n    name: payload.couponType,\r\n    description: payload.description || \"\",\r\n    discountConfiguration: convertItems(\r\n      payload.customConfiguration?.discount,\r\n      payload.customConfiguration?.discountAdditional\r\n    ),\r\n    filterConfiguration: convertItems(\r\n      payload.customConfiguration?.filter,\r\n      payload.customConfiguration?.filterAdditional\r\n    ),\r\n    validityConfiguration: convertItems(\r\n      payload.customConfiguration?.validity,\r\n      payload.customConfiguration?.validityAdditional\r\n    ),\r\n  };\r\n}\r\n\r\n// Helper function to generate unique IDs\r\nconst generateId = () =>\r\n  Date.now().toString(36) + Math.random().toString(36).substring(2, 9);\r\n\r\nexport default function EditCoupon() {\r\n  const { preloadData: couponPreloadData, couponData: coupon } =\r\n    useLoaderData<Loaderdata>();\r\n\r\n  const fetcher = useFetcher<{ data: CouponDetailsType; success: boolean }>();\r\n  const navigate = useNavigate();\r\n  const { showToast } = useToast();\r\n\r\n  useEffect(() => {\r\n    if (fetcher.state === \"idle\" && fetcher.data) {\r\n      if (fetcher.data.success === true) {\r\n        showToast(\"Coupon updated successfully\", \"success\");\r\n        navigate(-1);\r\n      } else if (fetcher.data.success === false) {\r\n        showToast(\"Failed to update coupon\", \"error\");\r\n      }\r\n    }\r\n  }, [fetcher.state, fetcher.data]);\r\n\r\n  const [payload, setPayload] = useState<CouponPayloadType>({\r\n    couponType: \"\",\r\n    customConfiguration: {\r\n      discount: [],\r\n      filter: [],\r\n      validity: [],\r\n      discountAdditional: [],\r\n      filterAdditional: [],\r\n      validityAdditional: [],\r\n    },\r\n  });\r\n\r\n  // Get the selected coupon type data\r\n  const selectedTypeData = payload.couponType\r\n    ? couponPreloadData.couponTypesConfigurations.find(\r\n        (type) => type.value === payload.couponType\r\n      )\r\n    : null;\r\n\r\n  useEffect(() => {\r\n    if (!coupon || !couponPreloadData) return;\r\n\r\n    const selectedConfig = couponPreloadData.couponTypesConfigurations.find(\r\n      (c) => c.value === coupon.name\r\n    );\r\n\r\n    if (!selectedConfig) return;\r\n\r\n    const getAllowedProps = (section: ConfigKey) =>\r\n      selectedConfig.configuration[section].properties.map((p) => p.value);\r\n\r\n    const getFixedProps = (section: ConfigKey) =>\r\n      selectedConfig.configuration[section].propertyConfig?.map(\r\n        (p) => p.value\r\n      ) || [];\r\n\r\n    const getFixedConfigItems = (section: ConfigKey) =>\r\n      selectedConfig.configuration[section].propertyConfig?.filter(\r\n        (p) => p.type === \"fixed\"\r\n      ) || [];\r\n\r\n    const allowed = {\r\n      discount: getAllowedProps(\"discount\"),\r\n      filter: getAllowedProps(\"filter\"),\r\n      validity: getAllowedProps(\"validity\"),\r\n      discountAdditional: getFixedProps(\"discount\"),\r\n      filterAdditional: getFixedProps(\"filter\"),\r\n      validityAdditional: getFixedProps(\"validity\"),\r\n    };\r\n\r\n    const toIdConfigItem = (item: ConfigItem): IdConfigItem => ({\r\n      id: generateId(),\r\n      property: item.property,\r\n      operator: item.operator,\r\n      value: item.value,\r\n    });\r\n\r\n    const transformedPayload: CouponPayloadType = {\r\n      couponType: coupon.name,\r\n      couponCode: coupon.code,\r\n      description: coupon.description,\r\n      customConfiguration: {\r\n        discount: [],\r\n        filter: [],\r\n        validity: [],\r\n        discountAdditional: [],\r\n        filterAdditional: [],\r\n        validityAdditional: [],\r\n      },\r\n    };\r\n\r\n    const addItems = (configKey: ConfigKey, items: ConfigItem[] = []) => {\r\n      items.forEach((item) => {\r\n        const prop = item.property;\r\n\r\n        // Add to main config if allowed\r\n        if (allowed[configKey].includes(prop)) {\r\n          transformedPayload.customConfiguration![configKey].push(\r\n            toIdConfigItem(item)\r\n          );\r\n        }\r\n\r\n        // Add to additional if fixed\r\n        if (\r\n          allowed[`${configKey}Additional` as keyof typeof allowed].includes(\r\n            prop\r\n          )\r\n        ) {\r\n          (\r\n            transformedPayload.customConfiguration![\r\n              `${configKey}Additional` as keyof typeof transformedPayload.customConfiguration\r\n            ] as IdConfigItem[]\r\n          ).push(toIdConfigItem(item));\r\n        }\r\n      });\r\n    };\r\n\r\n    addItems(\"discount\", coupon.discountConfiguration);\r\n    addItems(\"filter\", coupon.filterConfiguration);\r\n    addItems(\"validity\", coupon.validityConfiguration);\r\n\r\n    // Ensure all fixed inputs are initialized\r\n    const initializeMissingFixedInputs = (configKey: ConfigKey) => {\r\n      const existingProps =\r\n        transformedPayload.customConfiguration?.[`${configKey}Additional`]?.map(\r\n          (item) => item.property\r\n        ) || [];\r\n\r\n      const fixedInputs = getFixedConfigItems(configKey);\r\n\r\n      fixedInputs.forEach((fixedItem) => {\r\n        if (!existingProps.includes(fixedItem.value)) {\r\n          transformedPayload.customConfiguration?.[\r\n            `${configKey}Additional`\r\n          ]!.push({\r\n            id: generateId(),\r\n            property: fixedItem.value,\r\n            value: \"\",\r\n          });\r\n        }\r\n      });\r\n    };\r\n\r\n    initializeMissingFixedInputs(\"discount\");\r\n    initializeMissingFixedInputs(\"filter\");\r\n    initializeMissingFixedInputs(\"validity\");\r\n\r\n    setPayload(transformedPayload);\r\n  }, [coupon, couponPreloadData]);\r\n\r\n  // Get available properties for a section (excluding already selected ones)\r\n  const getAvailableProperties = (section: ConfigKey) => {\r\n    if (!selectedTypeData) return [];\r\n\r\n    if (section === \"filter\") {\r\n      // For filter section, return all properties\r\n      return selectedTypeData.configuration[section].properties;\r\n    }\r\n\r\n    // For discount and validity sections, filter out already selected properties\r\n    const selectedProperties =\r\n      payload.customConfiguration?.[section].map((item) => item.property) || [];\r\n\r\n    return selectedTypeData.configuration[section].properties.filter(\r\n      (prop) => !selectedProperties.includes(prop.value)\r\n    );\r\n  };\r\n\r\n  // Handle property change\r\n  const handlePropertyChange = (\r\n    section: ConfigKey,\r\n    id: string,\r\n    value: string\r\n  ) => {\r\n    if (!payload.customConfiguration) return;\r\n\r\n    // Find the current item\r\n    const currentItem = payload.customConfiguration[section].find(\r\n      (item) => item.id === id\r\n    );\r\n\r\n    // If the property is already set to this value, no need to change\r\n    if (currentItem?.property === value) {\r\n      return;\r\n    }\r\n\r\n    // For discount and validity sections, check if the property is already selected in other rows\r\n    if (section === \"discount\" || section === \"validity\") {\r\n      const isAlreadySelected = payload.customConfiguration[section].some(\r\n        (item) => item.id !== id && item.property === value\r\n      );\r\n\r\n      if (isAlreadySelected) {\r\n        console.warn(\r\n          `Property ${value} is already selected in ${section} section`\r\n        );\r\n        return;\r\n      }\r\n    }\r\n\r\n    const sectionConfig = [...payload.customConfiguration[section]];\r\n    const itemIndex = sectionConfig.findIndex((item) => item.id === id);\r\n\r\n    if (itemIndex === -1) return;\r\n\r\n    // Find the property configuration\r\n    const propertyConfig = selectedTypeData?.configuration[\r\n      section\r\n    ].properties.find((prop) => prop.value === value);\r\n\r\n    // Update the property and reset operator and value\r\n    sectionConfig[itemIndex] = {\r\n      ...sectionConfig[itemIndex],\r\n      property: value,\r\n      operator:\r\n        propertyConfig?.operatorConfig?.options?.[0]?.value || undefined,\r\n      value: \"\",\r\n    };\r\n\r\n    setPayload({\r\n      ...payload,\r\n      customConfiguration: {\r\n        ...payload.customConfiguration,\r\n        [section]: sectionConfig,\r\n      },\r\n    });\r\n  };\r\n\r\n  // Handle operator change\r\n  const handleOperatorChange = (\r\n    section: ConfigKey,\r\n    id: string,\r\n    value: string\r\n  ) => {\r\n    if (!payload.customConfiguration) return;\r\n\r\n    const sectionConfig = [...payload.customConfiguration[section]];\r\n    const itemIndex = sectionConfig.findIndex((item) => item.id === id);\r\n\r\n    if (itemIndex === -1) return;\r\n\r\n    sectionConfig[itemIndex] = {\r\n      ...sectionConfig[itemIndex],\r\n      operator: value,\r\n    };\r\n\r\n    setPayload({\r\n      ...payload,\r\n      customConfiguration: {\r\n        ...payload.customConfiguration,\r\n        [section]: sectionConfig,\r\n      },\r\n    });\r\n  };\r\n\r\n  // Handle value change\r\n  const handleValueChange = (\r\n    section: ConfigKey | ConfigAdditionalKey,\r\n    id: string,\r\n    value: string\r\n  ) => {\r\n    if (!payload.customConfiguration) return;\r\n\r\n    const sectionConfig = [...(payload.customConfiguration[section] || [])];\r\n    const itemIndex = sectionConfig.findIndex((item) => item.id === id);\r\n\r\n    if (itemIndex === -1) return;\r\n\r\n    sectionConfig[itemIndex] = {\r\n      ...sectionConfig[itemIndex],\r\n      value: value,\r\n    };\r\n\r\n    setPayload({\r\n      ...payload,\r\n      customConfiguration: {\r\n        ...payload.customConfiguration,\r\n        [section]: sectionConfig,\r\n      },\r\n    });\r\n  };\r\n\r\n  // Handle date change\r\n  const handleDateChange = (\r\n    section: ConfigKey,\r\n    id: string,\r\n    date: Date | undefined\r\n  ) => {\r\n    if (!payload.customConfiguration || !date) return;\r\n\r\n    const sectionConfig = [...payload.customConfiguration[section]];\r\n    const itemIndex = sectionConfig.findIndex((item) => item.id === id);\r\n\r\n    if (itemIndex === -1) return;\r\n\r\n    sectionConfig[itemIndex] = {\r\n      ...sectionConfig[itemIndex],\r\n      value: format(date, \"yyyy-MM-dd HH:mm:ss\"),\r\n    };\r\n\r\n    setPayload({\r\n      ...payload,\r\n      customConfiguration: {\r\n        ...payload.customConfiguration,\r\n        [section]: sectionConfig,\r\n      },\r\n    });\r\n  };\r\n\r\n  // Add a new row to a section\r\n  const addRow = (section: ConfigKey) => {\r\n    if (!payload.customConfiguration || !selectedTypeData) return;\r\n\r\n    const properties = selectedTypeData.configuration[section].properties;\r\n    if (properties.length === 0) return;\r\n\r\n    // For discount and validity sections, check if we've reached the maximum number of rows\r\n    if (section === \"discount\" || section === \"validity\") {\r\n      const currentRows = payload.customConfiguration[section].length;\r\n      const totalProperties = properties.length;\r\n\r\n      if (currentRows >= totalProperties) {\r\n        console.warn(\r\n          `Cannot add more rows to ${section} section. Maximum reached.`\r\n        );\r\n        return;\r\n      }\r\n\r\n      // Get the first available property that's not already selected\r\n      const availableProperties = getAvailableProperties(section);\r\n      if (availableProperties.length === 0) {\r\n        console.warn(`No more available properties for ${section} section`);\r\n        return;\r\n      }\r\n\r\n      const newProperty = availableProperties[0].value;\r\n      const newOperator =\r\n        availableProperties[0].operatorConfig?.options?.[0]?.value;\r\n\r\n      const newItem: IdConfigItem = {\r\n        id: generateId(),\r\n        property: newProperty,\r\n        operator: newOperator,\r\n        value: \"\",\r\n      };\r\n\r\n      setPayload({\r\n        ...payload,\r\n        customConfiguration: {\r\n          ...payload.customConfiguration,\r\n          [section]: [...payload.customConfiguration[section], newItem],\r\n        },\r\n      });\r\n    } else {\r\n      // For filter section, no restrictions\r\n      const newItem: IdConfigItem = {\r\n        id: generateId(),\r\n        property: properties[0].value,\r\n        operator: properties[0].operatorConfig?.options?.[0]?.value,\r\n        value: \"\",\r\n      };\r\n\r\n      setPayload({\r\n        ...payload,\r\n        customConfiguration: {\r\n          ...payload.customConfiguration,\r\n          [section]: [...payload.customConfiguration[section], newItem],\r\n        },\r\n      });\r\n    }\r\n  };\r\n\r\n  // Remove a row from a section\r\n  const removeRow = (section: ConfigKey, id: string) => {\r\n    if (!payload.customConfiguration) return;\r\n\r\n    const sectionConfig = payload.customConfiguration[section].filter(\r\n      (item) => item.id !== id\r\n    );\r\n\r\n    if (sectionConfig.length === 0) {\r\n      // Prevent removing the last row, or re-add a default one\r\n      addRow(section);\r\n      return;\r\n    }\r\n\r\n    setPayload({\r\n      ...payload,\r\n      customConfiguration: {\r\n        ...payload.customConfiguration,\r\n        [section]: sectionConfig,\r\n      },\r\n    });\r\n  };\r\n\r\n  // Get the unit for a property\r\n  const getPropertyUnit = (property: string) => {\r\n    return DISCOUNT_UNIT_MAP[property] || \"\";\r\n  };\r\n\r\n  // Render operator input based on input type\r\n  const renderOperatorInput = (\r\n    section: ConfigKey,\r\n    item: IdConfigItem,\r\n    property: any\r\n  ) => {\r\n    if (!property?.operatorConfig) return null;\r\n\r\n    const inputType = property.operatorConfig.input;\r\n\r\n    switch (inputType) {\r\n      case \"dropdown\":\r\n        return (\r\n          <Select\r\n            value={item.operator || \"\"}\r\n            onValueChange={(value) =>\r\n              handleOperatorChange(section, item.id, value)\r\n            }\r\n          >\r\n            <SelectTrigger className=\"rounded-lg\">\r\n              <SelectValue placeholder=\"Operator\" />\r\n            </SelectTrigger>\r\n            <SelectContent className=\"rounded-lg\">\r\n              {property.operatorConfig.options?.map((option: any) => (\r\n                <SelectItem key={option.value} value={option.value}>\r\n                  {option.label}\r\n                </SelectItem>\r\n              ))}\r\n            </SelectContent>\r\n          </Select>\r\n        );\r\n      case \"text\":\r\n        return (\r\n          <Input\r\n            type=\"text\"\r\n            value={item.operator || \"\"}\r\n            onChange={(e) =>\r\n              handleOperatorChange(section, item.id, e.target.value)\r\n            }\r\n            placeholder=\"Operator\"\r\n          />\r\n        );\r\n      case \"itemSearch\":\r\n        return (\r\n          <Input\r\n            type=\"text\"\r\n            value={item.operator || \"\"}\r\n            onChange={(e) =>\r\n              handleOperatorChange(section, item.id, e.target.value)\r\n            }\r\n            placeholder=\"Search item...\"\r\n          />\r\n        );\r\n      default:\r\n        return (\r\n          <Input\r\n            type=\"text\"\r\n            value={item.operator || \"\"}\r\n            onChange={(e) =>\r\n              handleOperatorChange(section, item.id, e.target.value)\r\n            }\r\n            placeholder=\"Operator\"\r\n          />\r\n        );\r\n    }\r\n  };\r\n\r\n  // Render input based on input type\r\n  const renderValueInput = (\r\n    section: ConfigKey,\r\n    item: IdConfigItem,\r\n    inputType: string\r\n  ) => {\r\n    const property = selectedTypeData?.configuration[section].properties.find(\r\n      (prop) => prop.value === item.property\r\n    );\r\n\r\n    switch (inputType) {\r\n      case \"text\":\r\n        return (\r\n          <div className=\"relative\">\r\n            <Input\r\n              type=\"text\"\r\n              value={item.value || \"\"}\r\n              onChange={(e) =>\r\n                handleValueChange(section, item.id, e.target.value)\r\n              }\r\n              className=\"pr-7\"\r\n            />\r\n            {getPropertyUnit(item.property) && (\r\n              <div className=\"absolute right-3 top-1/2 -translate-y-1/2 text-sm pointer-events-none text-muted-foreground\">\r\n                {getPropertyUnit(item.property)}\r\n              </div>\r\n            )}\r\n          </div>\r\n        );\r\n      case \"date\":\r\n        return (\r\n          <Popover>\r\n            <PopoverTrigger asChild>\r\n              <Button\r\n                type=\"button\"\r\n                variant=\"outline\"\r\n                className=\"rounded-lg w-full justify-between overflow-hidden\"\r\n              >\r\n                {item.value\r\n                  ? format(new Date(item.value), \"PPP\")\r\n                  : \"Pick a date\"}\r\n                <CalendarIcon className=\"h-4 w-4\" />\r\n              </Button>\r\n            </PopoverTrigger>\r\n            <PopoverContent className=\"w-auto p-0\">\r\n              <Calendar\r\n                mode=\"single\"\r\n                selected={item.value ? new Date(item.value) : undefined}\r\n                onSelect={(date) => handleDateChange(section, item.id, date)}\r\n                initialFocus\r\n              />\r\n            </PopoverContent>\r\n          </Popover>\r\n        );\r\n      case \"userSearch\":\r\n        return (\r\n          <Input\r\n            type=\"text\"\r\n            value={item.operator || \"\"}\r\n            onChange={(e) =>\r\n              handleOperatorChange(section, item.id, e.target.value)\r\n            }\r\n            placeholder=\"Search user...\"\r\n          />\r\n        );\r\n      case \"location\":\r\n        // For simplicity, we'll use a text input for these special types\r\n        return (\r\n          <Input\r\n            type=\"text\"\r\n            value={item.value || \"\"}\r\n            onChange={(e) =>\r\n              handleValueChange(section, item.id, e.target.value)\r\n            }\r\n            placeholder={`Search ${inputType.replace(\"Search\", \"\")}`}\r\n          />\r\n        );\r\n      case \"dropdown\":\r\n        return (\r\n          <Select\r\n            value={item.value || \"\"}\r\n            onValueChange={(value) =>\r\n              handleValueChange(section, item.id, value)\r\n            }\r\n          >\r\n            <SelectTrigger className=\"rounded-lg\">\r\n              <SelectValue placeholder=\"Select\" />\r\n            </SelectTrigger>\r\n            <SelectContent className=\"rounded-lg\">\r\n              {property?.valueConfig?.options?.map((option) => (\r\n                <SelectItem key={option.value} value={option.value}>\r\n                  {option.label}\r\n                </SelectItem>\r\n              ))}\r\n            </SelectContent>\r\n          </Select>\r\n        );\r\n      default:\r\n        return (\r\n          <Input\r\n            type=\"text\"\r\n            value={item.value || \"\"}\r\n            onChange={(e) =>\r\n              handleValueChange(section, item.id, e.target.value)\r\n            }\r\n          />\r\n        );\r\n    }\r\n  };\r\n\r\n  // Render fixed inputs from propertyConfig\r\n  const renderFixedInputs = (section: ConfigKey) => {\r\n    if (!selectedTypeData || !payload.customConfiguration) return null;\r\n\r\n    const propertyConfig =\r\n      selectedTypeData.configuration[section].propertyConfig;\r\n    if (!propertyConfig) return null;\r\n\r\n    const fixedInputs = propertyConfig.filter(\r\n      (input) => input.type === \"fixed\"\r\n    );\r\n    if (fixedInputs.length === 0) return null;\r\n\r\n    return (\r\n      <div className=\"mt-4\">\r\n        {fixedInputs.map((input) => {\r\n          // Find the corresponding item in payload Additionals\r\n          const item = payload.customConfiguration?.[\r\n            configKeysEnum[section]\r\n          ]?.find((item) => item.property === input.value);\r\n\r\n          if (!item) return null;\r\n\r\n          return (\r\n            <div key={item.id} className=\"mt-2\">\r\n              <div className=\"flex flex-row gap-2 items-center\">\r\n                <label className=\"text-sm font-medium\">{input.label}:</label>\r\n                <div>\r\n                  <Input\r\n                    type=\"text\"\r\n                    value={item.value || \"\"}\r\n                    onChange={(e) =>\r\n                      handleValueChange(\r\n                        configKeysEnum[section],\r\n                        item.id,\r\n                        e.target.value\r\n                      )\r\n                    }\r\n                  />\r\n                </div>\r\n              </div>\r\n            </div>\r\n          );\r\n        })}\r\n      </div>\r\n    );\r\n  };\r\n\r\n  // Render a configuration section\r\n  const renderConfigSection = (section: ConfigKey) => {\r\n    if (!selectedTypeData || !payload.customConfiguration) return null;\r\n\r\n    const sectionConfig = selectedTypeData.configuration[section];\r\n    const items = payload.customConfiguration[section];\r\n\r\n    // Find property method config if it exists\r\n    const propertyMethodInput = sectionConfig.propertyConfig?.find(\r\n      (config) => config.type === \"propertyMethod\"\r\n    );\r\n\r\n    // Check if we can add more rows for this section\r\n    const canAddMoreRows =\r\n      section === \"filter\" || items.length < sectionConfig.properties.length;\r\n\r\n    return (\r\n      <div className=\"p-3 pr-8 rounded-lg border mb-3\">\r\n        <p className=\"text-typography-800 font-semibold\">\r\n          {sectionConfig.label} Configurations\r\n        </p>\r\n        {sectionConfig.description && (\r\n          <p className=\"text-sm font-medium text-blue-600\">\r\n            {sectionConfig.description}\r\n          </p>\r\n        )}\r\n\r\n        {items.map((item, index) => {\r\n          const property = sectionConfig.properties.find(\r\n            (prop) => prop.value === item.property\r\n          );\r\n\r\n          const hasOperator = !!property?.operatorConfig;\r\n          const valueInputType = property?.valueConfig?.input || \"text\";\r\n\r\n          // For discount and validity sections, only show properties that aren't already selected\r\n          // or the current property of this row\r\n          const availableProperties =\r\n            section === \"filter\"\r\n              ? sectionConfig.properties\r\n              : sectionConfig.properties.filter(\r\n                  (prop) =>\r\n                    prop.value === item.property ||\r\n                    !payload.customConfiguration?.[section].some(\r\n                      (i) => i.id !== item.id && i.property === prop.value\r\n                    )\r\n                );\r\n\r\n          return (\r\n            <div key={item.id} className=\"mt-3\">\r\n              <div\r\n                className={`relative grid ${\r\n                  hasOperator ? \"grid-cols-3\" : \"grid-cols-2\"\r\n                } gap-2 items-center`}\r\n              >\r\n                <Select\r\n                  value={item.property}\r\n                  onValueChange={(value) =>\r\n                    handlePropertyChange(section, item.id, value)\r\n                  }\r\n                >\r\n                  <SelectTrigger className=\"rounded-lg\">\r\n                    <SelectValue placeholder=\"Property\" />\r\n                  </SelectTrigger>\r\n                  <SelectContent className=\"rounded-lg\">\r\n                    {availableProperties.map((property) => (\r\n                      <SelectItem key={property.value} value={property.value}>\r\n                        {property.label}\r\n                      </SelectItem>\r\n                    ))}\r\n                  </SelectContent>\r\n                </Select>\r\n\r\n                {hasOperator && renderOperatorInput(section, item, property)}\r\n\r\n                {renderValueInput(section, item, valueInputType)}\r\n\r\n                {items.length > 1 && (\r\n                  <div\r\n                    className=\"absolute -right-7 top-1/2 -translate-y-1/2 px-1 py-2 cursor-pointer hover:bg-accent rounded-md\"\r\n                    onClick={() => removeRow(section, item.id)}\r\n                  >\r\n                    <Trash2Icon className=\"h-5 w-5 text-red-500\" />\r\n                  </div>\r\n                )}\r\n              </div>\r\n              {index === 0 && propertyMethodInput && (\r\n                <PropertyMethodInput\r\n                  section={section}\r\n                  propertyInput={propertyMethodInput}\r\n                  payload={payload}\r\n                  setPayload={setPayload}\r\n                  selectedTypeData={selectedTypeData}\r\n                />\r\n              )}\r\n            </div>\r\n          );\r\n        })}\r\n\r\n        {canAddMoreRows && (\r\n          <Button\r\n            type=\"button\"\r\n            className=\"mt-3 underline\"\r\n            variant=\"link\"\r\n            onClick={() => addRow(section)}\r\n          >\r\n            <PlusIcon className=\"w-4 h-4 mr-1\" /> Add another{\" \"}\r\n            {section.toLowerCase()}\r\n          </Button>\r\n        )}\r\n\r\n        {/* Render fixed inputs at the bottom of the section */}\r\n        {renderFixedInputs(section)}\r\n      </div>\r\n    );\r\n  };\r\n\r\n  return (\r\n    <div>\r\n      <div>\r\n        <div\r\n          aria-labelledby=\"edit-coupon-heading\"\r\n          className=\"flex flex-row gap-2 items-center p-2\"\r\n        >\r\n          <div className=\"w-5 h-5 cursor-pointer\" onClick={() => navigate(-1)}>\r\n            <svg\r\n              width=\"100%\"\r\n              height=\"100%\"\r\n              viewBox=\"0 0 16 16\"\r\n              fill=\"none\"\r\n              xmlns=\"http://www.w3.org/2000/svg\"\r\n            >\r\n              <path\r\n                fillRule=\"evenodd\"\r\n                clipRule=\"evenodd\"\r\n                d=\"M7.13805 3.36193C7.3984 3.62228 7.3984 4.04439 7.13805 4.30474L3.60946 7.83333H14C14.3682 7.83333 14.6666 8.13181 14.6666 8.5C14.6666 8.86819 14.3682 9.16667 14 9.16667H3.60946L7.13805 12.6953C7.3984 12.9556 7.3984 13.3777 7.13805 13.6381C6.8777 13.8984 6.45559 13.8984 6.19524 13.6381L1.52858 8.97141C1.26823 8.71106 1.26823 8.28895 1.52858 8.0286L6.19524 3.36193C6.45559 3.10158 6.8777 3.10158 7.13805 3.36193Z\"\r\n                fill=\"#1F2A37\"\r\n              />\r\n            </svg>\r\n          </div>\r\n          <h2 className=\"text-lg font-semibold text-typography-700\">\r\n            Edit coupon\r\n          </h2>\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"pt-3 pb-20\">\r\n        <fetcher.Form method=\"post\">\r\n          <input type=\"hidden\" name=\"actionType\" value=\"editCoupon\" />\r\n          <input type=\"hidden\" name=\"couponId\" value={coupon.id} />\r\n          <input type=\"hidden\" name=\"payload\" value={JSON.stringify(payload)} />\r\n          <div aria-labelledby=\"edit-coupon\" className=\"p-2\">\r\n            <div className=\"p-3 rounded-lg border mb-3\">\r\n              <p className=\"font-semibold text-typography-800\">\r\n                Coupon Details\r\n              </p>\r\n              <p className=\"text-sm font-medium text-blue-600\">\r\n                These details would be shown to the customers.\r\n              </p>\r\n              <div className=\"relative mt-3 flex flex-row gap-5 items-center justify-between\">\r\n                <p className=\"whitespace-nowrap\">Coupon type:</p>\r\n                <Select\r\n                  value={payload.couponType || \"\"}\r\n                  onValueChange={(value) => {\r\n                    setPayload({\r\n                      ...payload,\r\n                      couponType: value,\r\n                    });\r\n                  }}\r\n                >\r\n                  <SelectTrigger className=\"rounded-lg\">\r\n                    <SelectValue placeholder=\"Select\" />\r\n                  </SelectTrigger>\r\n                  <SelectContent className=\"rounded-lg\">\r\n                    {couponPreloadData.couponTypes.map((type) => (\r\n                      <SelectItem key={type.value} value={type.value}>\r\n                        {type.label}\r\n                      </SelectItem>\r\n                    ))}\r\n                  </SelectContent>\r\n                </Select>\r\n              </div>\r\n              <Input\r\n                type=\"text\"\r\n                placeholder=\"Coupon Code\"\r\n                className=\"mt-3 rounded-lg\"\r\n                value={payload.couponCode || \"\"}\r\n                onChange={(e) => {\r\n                  setPayload({\r\n                    ...payload,\r\n                    couponCode: e.target.value,\r\n                  });\r\n                }}\r\n              />\r\n              <Input\r\n                type=\"text\"\r\n                placeholder=\"Description\"\r\n                className=\"mt-3 rounded-lg\"\r\n                value={payload.description || \"\"}\r\n                onChange={(e) => {\r\n                  setPayload({\r\n                    ...payload,\r\n                    description: e.target.value,\r\n                  });\r\n                }}\r\n              />\r\n            </div>\r\n\r\n            {selectedTypeData && (\r\n              <>\r\n                {renderConfigSection(\"discount\")}\r\n                {renderConfigSection(\"filter\")}\r\n                {renderConfigSection(\"validity\")}\r\n              </>\r\n            )}\r\n          </div>\r\n\r\n          <div className=\"fixed bottom-2 left-0 right-0 z-30 flex justify-center md:static md:mt-6 md:justify-end\">\r\n            <div className=\"w-full md:w-auto px-4 flex flex-row gap-4 items-center justify-between\">\r\n              <Button\r\n                onClick={() => navigate(-1)}\r\n                type=\"button\"\r\n                variant=\"outline\"\r\n                className=\"flex-1 md:px-12 pointer-events-auto font-bold flex items-center justify-center rounded-lg shadow-xl p-3\"\r\n              >\r\n                Cancel\r\n              </Button>\r\n              <Button\r\n                type=\"submit\"\r\n                className=\"flex-1 md:px-12 pointer-events-auto font-bold flex items-center justify-center rounded-lg shadow-xl p-3 hover:bg-primary\"\r\n              >\r\n                Save\r\n              </Button>\r\n            </div>\r\n          </div>\r\n        </fetcher.Form>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\ninterface PropertyMethodInputProps {\r\n  section: ConfigKey;\r\n  propertyInput: {\r\n    input: string;\r\n    type: string;\r\n    label: string;\r\n    value: string;\r\n    options?: { label: string; value: string }[];\r\n  };\r\n  payload: CouponPayloadType;\r\n  setPayload: React.Dispatch<React.SetStateAction<CouponPayloadType>>;\r\n  selectedTypeData?: CouponPreloadType[\"couponTypesConfigurations\"][number];\r\n}\r\n\r\nconst PropertyMethodInput: React.FC<PropertyMethodInputProps> = ({\r\n  section,\r\n  propertyInput,\r\n  payload,\r\n  setPayload,\r\n  selectedTypeData,\r\n}) => {\r\n  const shouldRender =\r\n    section === \"discount\" &&\r\n    propertyInput.type === \"propertyMethod\" &&\r\n    payload.customConfiguration?.discount?.length === 2;\r\n\r\n  const configItem = useMemo(() => {\r\n    return (\r\n      payload.customConfiguration?.discountAdditional?.find(\r\n        (item) => item.property === propertyInput.value\r\n      ) || {\r\n        id: generateId(),\r\n        property: propertyInput.value,\r\n        value: propertyInput.options?.[0]?.value || \"\",\r\n      }\r\n    );\r\n  }, [payload, propertyInput]);\r\n\r\n  const handleValueChange = (newValue: string) => {\r\n    const updatedAdditional = [\r\n      ...(payload.customConfiguration?.discountAdditional || []).filter(\r\n        (item) => item.property !== propertyInput.value\r\n      ),\r\n      {\r\n        id: configItem.id,\r\n        property: propertyInput.value,\r\n        value: newValue,\r\n      },\r\n    ];\r\n\r\n    setPayload({\r\n      ...payload,\r\n      customConfiguration: {\r\n        discount: payload.customConfiguration?.discount || [],\r\n        filter: payload.customConfiguration?.filter || [],\r\n        validity: payload.customConfiguration?.validity || [],\r\n        discountAdditional: updatedAdditional,\r\n      },\r\n    });\r\n  };\r\n\r\n  // Manage add/remove logic on mount/unmount or discount length change\r\n  useEffect(() => {\r\n    if (!payload.customConfiguration) return;\r\n\r\n    const discountItems = payload.customConfiguration.discount;\r\n    const propertyMethodConfig =\r\n      selectedTypeData?.configuration.discount.propertyConfig?.find(\r\n        (config) => config.type === \"propertyMethod\"\r\n      );\r\n    if (!propertyMethodConfig) return;\r\n\r\n    const currentAdditional = [\r\n      ...(payload.customConfiguration.discountAdditional || []),\r\n    ];\r\n    const hybDiscStrategyIndex = currentAdditional.findIndex(\r\n      (item) => item.property === propertyMethodConfig.value\r\n    );\r\n\r\n    // Add if not present and condition met\r\n    if (discountItems.length === 2 && hybDiscStrategyIndex === -1) {\r\n      currentAdditional.push({\r\n        id: generateId(),\r\n        property: propertyMethodConfig.value,\r\n        value: propertyMethodConfig.options?.[0]?.value || \"\",\r\n      });\r\n      setPayload((prev) => ({\r\n        ...prev,\r\n        customConfiguration: {\r\n          ...prev.customConfiguration,\r\n          discount: prev.customConfiguration?.discount || [],\r\n          filter: prev.customConfiguration?.filter || [],\r\n          validity: prev.customConfiguration?.validity || [],\r\n          discountAdditional: currentAdditional,\r\n        },\r\n      }));\r\n    }\r\n\r\n    // Remove if condition fails\r\n    if (discountItems.length !== 2 && hybDiscStrategyIndex !== -1) {\r\n      currentAdditional.splice(hybDiscStrategyIndex, 1);\r\n      setPayload((prev) => ({\r\n        ...prev,\r\n        customConfiguration: {\r\n          ...prev.customConfiguration,\r\n          discount: prev.customConfiguration?.discount || [],\r\n          filter: prev.customConfiguration?.filter || [],\r\n          validity: prev.customConfiguration?.validity || [],\r\n          discountAdditional: currentAdditional,\r\n        },\r\n      }));\r\n    }\r\n  }, [payload.customConfiguration?.discount?.length]);\r\n\r\n  if (!shouldRender) return null;\r\n\r\n  return (\r\n    <div className=\"my-2\">\r\n      <Tabs value={configItem.value} onValueChange={handleValueChange}>\r\n        <TabsList>\r\n          {propertyInput.options?.map((option) => (\r\n            <TabsTrigger\r\n              key={option.value}\r\n              value={option.value}\r\n              className=\"flex-1\"\r\n            >\r\n              {option.label}\r\n            </TabsTrigger>\r\n          ))}\r\n        </TabsList>\r\n      </Tabs>\r\n    </div>\r\n  );\r\n};\r\n"], "names": ["generateId", "Date", "now", "toString", "Math", "random", "substring", "EditCoupon", "preloadData", "couponPreloadData", "couponData", "coupon", "useLoaderData", "fetcher", "useFetcher", "navigate", "useNavigate", "showToast", "useToast", "useEffect", "state", "data", "success", "payload", "setPayload", "useState", "couponType", "customConfiguration", "discount", "filter", "validity", "discountAdditional", "filterAdditional", "validityAdditional", "selectedTypeData", "couponTypesConfigurations", "find", "type", "value", "selectedConfig", "c", "name", "getAllowedProps", "section", "configuration", "properties", "map", "p", "getFixedProps", "propertyConfig", "getFixedConfigItems", "allowed", "toIdConfigItem", "item", "id", "property", "operator", "transformedPayload", "couponCode", "code", "description", "addItems", "config<PERSON><PERSON>", "items", "for<PERSON>ach", "prop", "includes", "push", "discountConfiguration", "filterConfiguration", "validityConfiguration", "initializeMissingFixedInputs", "existingProps", "fixedInputs", "fixedItem", "getAvailableProperties", "selectedProperties", "handlePropertyChange", "currentItem", "isAlreadySelected", "some", "console", "warn", "sectionConfig", "itemIndex", "findIndex", "operatorConfig", "options", "handleOperatorChange", "handleValueChange", "handleDateChange", "date", "format", "addRow", "length", "currentRows", "totalProperties", "availableProperties", "newProperty", "newOperator", "newItem", "removeRow", "getPropertyUnit", "DISCOUNT_UNIT_MAP", "renderOperatorInput", "inputType", "input", "jsxs", "Select", "onValueChange", "children", "jsx", "SelectTrigger", "className", "SelectValue", "placeholder", "SelectContent", "option", "SelectItem", "label", "Input", "onChange", "e", "target", "renderValueInput", "Popover", "PopoverTrigger", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "variant", "CalendarIcon", "PopoverC<PERSON>nt", "Calendar", "mode", "selected", "onSelect", "initialFocus", "replace", "valueConfig", "renderFixedInputs", "configKeysEnum", "renderConfigSection", "propertyMethodInput", "config", "canAddMoreRows", "index", "hasOperator", "valueInputType", "i", "onClick", "Trash2Icon", "PropertyMethodInput", "propertyInput", "PlusIcon", "toLowerCase", "width", "height", "viewBox", "fill", "xmlns", "fillRule", "clipRule", "d", "Form", "method", "JSON", "stringify", "couponTypes", "Fragment", "shouldRender", "configItem", "useMemo", "newValue", "updatedAdditional", "discountItems", "propertyMethodConfig", "currentAdditional", "hybDiscStrategyIndex", "prev", "splice", "Tabs", "TabsList", "TabsTrigger"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmIA,MAAMA,aAAaA,MACjBC,KAAKC,IAAI,EAAEC,SAAS,EAAE,IAAIC,KAAKC,SAASF,SAAS,EAAE,EAAEG,UAAU,GAAG,CAAC;AAErE,SAAwBC,aAAa;AACnC,QAAM;AAAA,IAAEC,aAAaC;AAAAA,IAAmBC,YAAYC;AAAAA,MAClDC,cAA0B;AAE5B,QAAMC,UAAUC,WAA0D;AAC1E,QAAMC,WAAWC,YAAY;AACvB,QAAA;AAAA,IAAEC;AAAAA,EAAU,IAAIC,SAAS;AAE/BC,eAAAA,UAAU,MAAM;AACd,QAAIN,QAAQO,UAAU,UAAUP,QAAQQ,MAAM;AACxC,UAAAR,QAAQQ,KAAKC,YAAY,MAAM;AACjCL,kBAAU,+BAA+B,SAAS;AAClDF,iBAAS,EAAE;AAAA,MACF,WAAAF,QAAQQ,KAAKC,YAAY,OAAO;AACzCL,kBAAU,2BAA2B,OAAO;AAAA,MAC9C;AAAA,IACF;AAAA,KACC,CAACJ,QAAQO,OAAOP,QAAQQ,IAAI,CAAC;AAEhC,QAAM,CAACE,SAASC,UAAU,IAAIC,sBAA4B;AAAA,IACxDC,YAAY;AAAA,IACZC,qBAAqB;AAAA,MACnBC,UAAU,CAAC;AAAA,MACXC,QAAQ,CAAC;AAAA,MACTC,UAAU,CAAC;AAAA,MACXC,oBAAoB,CAAC;AAAA,MACrBC,kBAAkB,CAAC;AAAA,MACnBC,oBAAoB,CAAA;AAAA,IACtB;AAAA,EACF,CAAC;AAGD,QAAMC,mBAAmBX,QAAQG,aAC7BjB,kBAAkB0B,0BAA0BC,KACzCC,UAASA,KAAKC,UAAUf,QAAQG,UACnC,IACA;AAEJP,eAAAA,UAAU,MAAM;AACV,QAAA,CAACR,UAAU,CAACF,kBAAmB;AAE7B,UAAA8B,iBAAiB9B,kBAAkB0B,0BAA0BC,KAChEI,OAAMA,EAAEF,UAAU3B,OAAO8B,IAC5B;AAEA,QAAI,CAACF,eAAgB;AAErB,UAAMG,kBAAmBC,aACvBJ,eAAeK,cAAcD,OAAO,EAAEE,WAAWC,IAAKC,OAAMA,EAAET,KAAK;AAErE,UAAMU,gBAAiBL;;AACrBJ,mCAAeK,cAAcD,OAAO,EAAEM,mBAAtCV,mBAAsDO,IACnDC,OAAMA,EAAET,WACN,CAAC;AAAA;AAER,UAAMY,sBAAuBP,aAAA;;AAC3BJ,mCAAeK,cAAcD,OAAO,EAAEM,mBAAtCV,mBAAsDV,OACnDkB,OAAMA,EAAEV,SAAS,aACf,CAAC;AAAA;AAER,UAAMc,UAAU;AAAA,MACdvB,UAAUc,gBAAgB,UAAU;AAAA,MACpCb,QAAQa,gBAAgB,QAAQ;AAAA,MAChCZ,UAAUY,gBAAgB,UAAU;AAAA,MACpCX,oBAAoBiB,cAAc,UAAU;AAAA,MAC5ChB,kBAAkBgB,cAAc,QAAQ;AAAA,MACxCf,oBAAoBe,cAAc,UAAU;AAAA,IAC9C;AAEM,UAAAI,iBAAkBC,WAAoC;AAAA,MAC1DC,IAAItD,WAAW;AAAA,MACfuD,UAAUF,KAAKE;AAAAA,MACfC,UAAUH,KAAKG;AAAAA,MACflB,OAAOe,KAAKf;AAAAA,IACd;AAEA,UAAMmB,qBAAwC;AAAA,MAC5C/B,YAAYf,OAAO8B;AAAAA,MACnBiB,YAAY/C,OAAOgD;AAAAA,MACnBC,aAAajD,OAAOiD;AAAAA,MACpBjC,qBAAqB;AAAA,QACnBC,UAAU,CAAC;AAAA,QACXC,QAAQ,CAAC;AAAA,QACTC,UAAU,CAAC;AAAA,QACXC,oBAAoB,CAAC;AAAA,QACrBC,kBAAkB,CAAC;AAAA,QACnBC,oBAAoB,CAAA;AAAA,MACtB;AAAA,IACF;AAEA,UAAM4B,WAAWA,CAACC,WAAsBC,QAAsB,CAAA,MAAO;AAC7DA,YAAAC,QAASX,UAAS;AACtB,cAAMY,OAAOZ,KAAKE;AAGlB,YAAIJ,QAAQW,SAAS,EAAEI,SAASD,IAAI,GAAG;AAClBR,6BAAA9B,oBAAqBmC,SAAS,EAAEK,KACjDf,eAAeC,IAAI,CACrB;AAAA,QACF;AAGA,YACEF,QAAQ,GAAGW,SAAS,YAAoC,EAAEI,SACxDD,IACF,GACA;AAEER,6BAAmB9B,oBACjB,GAAGmC,SAAS,YACd,EACAK,KAAKf,eAAeC,IAAI,CAAC;AAAA,QAC7B;AAAA,MACF,CAAC;AAAA,IACH;AAESQ,aAAA,YAAYlD,OAAOyD,qBAAqB;AACxCP,aAAA,UAAUlD,OAAO0D,mBAAmB;AACpCR,aAAA,YAAYlD,OAAO2D,qBAAqB;AAG3C,UAAAC,+BAAgCT,eAAyB;;AAC7D,YAAMU,kBACJf,8BAAmB9B,wBAAnB8B,mBAAyC,GAAGK,SAAS,kBAArDL,mBAAoEX,IACjEO,UAASA,KAAKE,cACZ,CAAC;AAEF,YAAAkB,cAAcvB,oBAAoBY,SAAS;AAErCW,kBAAAT,QAASU,eAAc;;AACjC,YAAI,CAACF,cAAcN,SAASQ,UAAUpC,KAAK,GAAG;AAC5CmB,WAAAA,MAAAA,mBAAmB9B,wBAAnB8B,gBAAAA,IACE,GAAGK,SAAS,cACXK,KAAK;AAAA,YACNb,IAAItD,WAAW;AAAA,YACfuD,UAAUmB,UAAUpC;AAAAA,YACpBA,OAAO;AAAA,UACT;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AAEAiC,iCAA6B,UAAU;AACvCA,iCAA6B,QAAQ;AACrCA,iCAA6B,UAAU;AAEvC/C,eAAWiC,kBAAkB;AAAA,EAC/B,GAAG,CAAC9C,QAAQF,iBAAiB,CAAC;AAGxB,QAAAkE,yBAA0BhC,aAAuB;;AACjD,QAAA,CAACT,iBAAkB,QAAO,CAAC;AAE/B,QAAIS,YAAY,UAAU;AAEjB,aAAAT,iBAAiBU,cAAcD,OAAO,EAAEE;AAAAA,IACjD;AAGM,UAAA+B,uBACJrD,aAAQI,wBAARJ,mBAA8BoB,SAASG,IAAKO,UAASA,KAAKE,cAAa,CAAC;AAE1E,WAAOrB,iBAAiBU,cAAcD,OAAO,EAAEE,WAAWhB,OACvDoC,UAAS,CAACW,mBAAmBV,SAASD,KAAK3B,KAAK,CACnD;AAAA,EACF;AAGA,QAAMuC,uBAAuBA,CAC3BlC,SACAW,IACAhB,UACG;;AACC,QAAA,CAACf,QAAQI,oBAAqB;AAGlC,UAAMmD,cAAcvD,QAAQI,oBAAoBgB,OAAO,EAAEP,KACtDiB,UAASA,KAAKC,OAAOA,EACxB;AAGI,SAAAwB,2CAAavB,cAAajB,OAAO;AACnC;AAAA,IACF;AAGI,QAAAK,YAAY,cAAcA,YAAY,YAAY;AACpD,YAAMoC,oBAAoBxD,QAAQI,oBAAoBgB,OAAO,EAAEqC,KAC5D3B,UAASA,KAAKC,OAAOA,MAAMD,KAAKE,aAAajB,KAChD;AAEA,UAAIyC,mBAAmB;AACbE,gBAAAC,KACN,YAAY5C,KAAK,2BAA2BK,OAAO,UACrD;AACA;AAAA,MACF;AAAA,IACF;AAEA,UAAMwC,gBAAgB,CAAC,GAAG5D,QAAQI,oBAAoBgB,OAAO,CAAC;AAC9D,UAAMyC,YAAYD,cAAcE,UAAWhC,UAASA,KAAKC,OAAOA,EAAE;AAElE,QAAI8B,cAAc,GAAI;AAGhB,UAAAnC,iBAAiBf,qDAAkBU,cACvCD,SACAE,WAAWT,KAAM6B,UAASA,KAAK3B,UAAUA;AAG3C6C,kBAAcC,SAAS,IAAI;AAAA,MACzB,GAAGD,cAAcC,SAAS;AAAA,MAC1B7B,UAAUjB;AAAAA,MACVkB,YACEP,kEAAgBqC,mBAAhBrC,mBAAgCsC,YAAhCtC,mBAA0C,OAA1CA,mBAA8CX,UAAS;AAAA,MACzDA,OAAO;AAAA,IACT;AAEWd,eAAA;AAAA,MACT,GAAGD;AAAAA,MACHI,qBAAqB;AAAA,QACnB,GAAGJ,QAAQI;AAAAA,QACX,CAACgB,OAAO,GAAGwC;AAAAA,MACb;AAAA,IACF,CAAC;AAAA,EACH;AAGA,QAAMK,uBAAuBA,CAC3B7C,SACAW,IACAhB,UACG;AACC,QAAA,CAACf,QAAQI,oBAAqB;AAElC,UAAMwD,gBAAgB,CAAC,GAAG5D,QAAQI,oBAAoBgB,OAAO,CAAC;AAC9D,UAAMyC,YAAYD,cAAcE,UAAWhC,UAASA,KAAKC,OAAOA,EAAE;AAElE,QAAI8B,cAAc,GAAI;AAEtBD,kBAAcC,SAAS,IAAI;AAAA,MACzB,GAAGD,cAAcC,SAAS;AAAA,MAC1B5B,UAAUlB;AAAAA,IACZ;AAEWd,eAAA;AAAA,MACT,GAAGD;AAAAA,MACHI,qBAAqB;AAAA,QACnB,GAAGJ,QAAQI;AAAAA,QACX,CAACgB,OAAO,GAAGwC;AAAAA,MACb;AAAA,IACF,CAAC;AAAA,EACH;AAGA,QAAMM,oBAAoBA,CACxB9C,SACAW,IACAhB,UACG;AACC,QAAA,CAACf,QAAQI,oBAAqB;AAE5B,UAAAwD,gBAAgB,CAAC,GAAI5D,QAAQI,oBAAoBgB,OAAO,KAAK,CAAA,CAAG;AACtE,UAAMyC,YAAYD,cAAcE,UAAWhC,UAASA,KAAKC,OAAOA,EAAE;AAElE,QAAI8B,cAAc,GAAI;AAEtBD,kBAAcC,SAAS,IAAI;AAAA,MACzB,GAAGD,cAAcC,SAAS;AAAA,MAC1B9C;AAAAA,IACF;AAEWd,eAAA;AAAA,MACT,GAAGD;AAAAA,MACHI,qBAAqB;AAAA,QACnB,GAAGJ,QAAQI;AAAAA,QACX,CAACgB,OAAO,GAAGwC;AAAAA,MACb;AAAA,IACF,CAAC;AAAA,EACH;AAGA,QAAMO,mBAAmBA,CACvB/C,SACAW,IACAqC,SACG;AACH,QAAI,CAACpE,QAAQI,uBAAuB,CAACgE,KAAM;AAE3C,UAAMR,gBAAgB,CAAC,GAAG5D,QAAQI,oBAAoBgB,OAAO,CAAC;AAC9D,UAAMyC,YAAYD,cAAcE,UAAWhC,UAASA,KAAKC,OAAOA,EAAE;AAElE,QAAI8B,cAAc,GAAI;AAEtBD,kBAAcC,SAAS,IAAI;AAAA,MACzB,GAAGD,cAAcC,SAAS;AAAA,MAC1B9C,OAAOsD,OAAOD,MAAM,qBAAqB;AAAA,IAC3C;AAEWnE,eAAA;AAAA,MACT,GAAGD;AAAAA,MACHI,qBAAqB;AAAA,QACnB,GAAGJ,QAAQI;AAAAA,QACX,CAACgB,OAAO,GAAGwC;AAAAA,MACb;AAAA,IACF,CAAC;AAAA,EACH;AAGM,QAAAU,SAAUlD,aAAuB;;AACrC,QAAI,CAACpB,QAAQI,uBAAuB,CAACO,iBAAkB;AAEvD,UAAMW,aAAaX,iBAAiBU,cAAcD,OAAO,EAAEE;AACvD,QAAAA,WAAWiD,WAAW,EAAG;AAGzB,QAAAnD,YAAY,cAAcA,YAAY,YAAY;AACpD,YAAMoD,cAAcxE,QAAQI,oBAAoBgB,OAAO,EAAEmD;AACzD,YAAME,kBAAkBnD,WAAWiD;AAEnC,UAAIC,eAAeC,iBAAiB;AAC1Bf,gBAAAC,KACN,2BAA2BvC,OAAO,4BACpC;AACA;AAAA,MACF;AAGM,YAAAsD,sBAAsBtB,uBAAuBhC,OAAO;AACtD,UAAAsD,oBAAoBH,WAAW,GAAG;AAC5Bb,gBAAAC,KAAK,oCAAoCvC,OAAO,UAAU;AAClE;AAAA,MACF;AAEM,YAAAuD,cAAcD,oBAAoB,CAAC,EAAE3D;AAC3C,YAAM6D,eACJF,qCAAoB,CAAC,EAAEX,mBAAvBW,mBAAuCV,YAAvCU,mBAAiD,OAAjDA,mBAAqD3D;AAEvD,YAAM8D,UAAwB;AAAA,QAC5B9C,IAAItD,WAAW;AAAA,QACfuD,UAAU2C;AAAAA,QACV1C,UAAU2C;AAAAA,QACV7D,OAAO;AAAA,MACT;AAEWd,iBAAA;AAAA,QACT,GAAGD;AAAAA,QACHI,qBAAqB;AAAA,UACnB,GAAGJ,QAAQI;AAAAA,UACX,CAACgB,OAAO,GAAG,CAAC,GAAGpB,QAAQI,oBAAoBgB,OAAO,GAAGyD,OAAO;AAAA,QAC9D;AAAA,MACF,CAAC;AAAA,IACH,OAAO;AAEL,YAAMA,UAAwB;AAAA,QAC5B9C,IAAItD,WAAW;AAAA,QACfuD,UAAUV,WAAW,CAAC,EAAEP;AAAAA,QACxBkB,WAAUX,4BAAW,CAAC,EAAEyC,mBAAdzC,mBAA8B0C,YAA9B1C,mBAAwC,OAAxCA,mBAA4CP;AAAAA,QACtDA,OAAO;AAAA,MACT;AAEWd,iBAAA;AAAA,QACT,GAAGD;AAAAA,QACHI,qBAAqB;AAAA,UACnB,GAAGJ,QAAQI;AAAAA,UACX,CAACgB,OAAO,GAAG,CAAC,GAAGpB,QAAQI,oBAAoBgB,OAAO,GAAGyD,OAAO;AAAA,QAC9D;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAGM,QAAAC,YAAYA,CAAC1D,SAAoBW,OAAe;AAChD,QAAA,CAAC/B,QAAQI,oBAAqB;AAElC,UAAMwD,gBAAgB5D,QAAQI,oBAAoBgB,OAAO,EAAEd,OACxDwB,UAASA,KAAKC,OAAOA,EACxB;AAEI,QAAA6B,cAAcW,WAAW,GAAG;AAE9BD,aAAOlD,OAAO;AACd;AAAA,IACF;AAEWnB,eAAA;AAAA,MACT,GAAGD;AAAAA,MACHI,qBAAqB;AAAA,QACnB,GAAGJ,QAAQI;AAAAA,QACX,CAACgB,OAAO,GAAGwC;AAAAA,MACb;AAAA,IACF,CAAC;AAAA,EACH;AAGM,QAAAmB,kBAAmB/C,cAAqB;AACrC,WAAAgD,kBAAkBhD,QAAQ,KAAK;AAAA,EACxC;AAGA,QAAMiD,sBAAsBA,CAC1B7D,SACAU,MACAE,aACG;;AACC,QAAA,EAACA,qCAAU+B,gBAAuB,QAAA;AAEhC,UAAAmB,YAAYlD,SAAS+B,eAAeoB;AAE1C,YAAQD,WAAW;AAAA,MACjB,KAAK;AAED,eAAAE,kCAAAA,KAACC,QAAA;AAAA,UACCtE,OAAOe,KAAKG,YAAY;AAAA,UACxBqD,eAAgBvE,WACdkD,qBAAqB7C,SAASU,KAAKC,IAAIhB,KAAK;AAAA,UAG9CwE,UAAA,CAAAC,kCAAA,IAACC;YAAcC,WAAU;AAAA,YACvBH,gDAACI,aAAY;AAAA,cAAAC,aAAY;AAAA,YAAW,CAAA;AAAA,UACtC,CAAA,GACAJ,kCAAA,IAACK;YAAcH,WAAU;AAAA,YACtBH,yBAASxB,eAAeC,+BAASzC,IAAKuE,kDACpCC,YAA8B;AAAA,cAAAhF,OAAO+E,OAAO/E;AAAAA,cAC1CwE,UAAAO,OAAOE;AAAAA,eADOF,OAAO/E,KAExB;AAAA,UAEJ,CAAA,CAAA;AAAA,QAAA,CACF;AAAA,MAEJ,KAAK;AAED,eAAAyE,kCAAAA,IAACS,OAAA;AAAA,UACCnF,MAAK;AAAA,UACLC,OAAOe,KAAKG,YAAY;AAAA,UACxBiE,UAAWC,OACTlC,qBAAqB7C,SAASU,KAAKC,IAAIoE,EAAEC,OAAOrF,KAAK;AAAA,UAEvD6E,aAAY;AAAA,QAAA,CACd;AAAA,MAEJ,KAAK;AAED,eAAAJ,kCAAAA,IAACS,OAAA;AAAA,UACCnF,MAAK;AAAA,UACLC,OAAOe,KAAKG,YAAY;AAAA,UACxBiE,UAAWC,OACTlC,qBAAqB7C,SAASU,KAAKC,IAAIoE,EAAEC,OAAOrF,KAAK;AAAA,UAEvD6E,aAAY;AAAA,QAAA,CACd;AAAA,MAEJ;AAEI,eAAAJ,kCAAAA,IAACS,OAAA;AAAA,UACCnF,MAAK;AAAA,UACLC,OAAOe,KAAKG,YAAY;AAAA,UACxBiE,UAAWC,OACTlC,qBAAqB7C,SAASU,KAAKC,IAAIoE,EAAEC,OAAOrF,KAAK;AAAA,UAEvD6E,aAAY;AAAA,QAAA,CACd;AAAA,IAEN;AAAA,EACF;AAGA,QAAMS,mBAAmBA,CACvBjF,SACAU,MACAoD,cACG;;AACH,UAAMlD,WAAWrB,qDAAkBU,cAAcD,SAASE,WAAWT,KAClE6B,UAASA,KAAK3B,UAAUe,KAAKE;AAGhC,YAAQkD,WAAW;AAAA,MACjB,KAAK;AAED,eAAAE,kCAAAA,KAAC,OAAI;AAAA,UAAAM,WAAU;AAAA,UACbH,UAAA,CAAAC,kCAAA,IAACS,OAAA;AAAA,YACCnF,MAAK;AAAA,YACLC,OAAOe,KAAKf,SAAS;AAAA,YACrBmF,UAAWC,OACTjC,kBAAkB9C,SAASU,KAAKC,IAAIoE,EAAEC,OAAOrF,KAAK;AAAA,YAEpD2E,WAAU;AAAA,UAAA,CACZ,GACCX,gBAAgBjD,KAAKE,QAAQ,KAC5BwD,kCAAAA,IAAC,OAAI;AAAA,YAAAE,WAAU;AAAA,YACZH,UAAAR,gBAAgBjD,KAAKE,QAAQ;AAAA,UAChC,CAAA,CAAA;AAAA,QAEJ,CAAA;AAAA,MAEJ,KAAK;AACH,sDACGsE,SACC;AAAA,UAAAf,UAAA,CAACC,kCAAA,IAAAe,gBAAA;AAAA,YAAeC,SAAO;AAAA,YACrBjB,UAAAH,kCAAA,KAACqB,QAAA;AAAA,cACC3F,MAAK;AAAA,cACL4F,SAAQ;AAAA,cACRhB,WAAU;AAAA,cAETH,UAAA,CAAKzD,KAAAf,QACFsD,OAAO,IAAI3F,KAAKoD,KAAKf,KAAK,GAAG,KAAK,IAClC,eACJyE,kCAAAA,IAACmB,UAAa;AAAA,gBAAAjB,WAAU;AAAA,cAAU,CAAA,CAAA;AAAA,YACpC,CAAA;AAAA,UACF,CAAA,GACAF,kCAAA,IAACoB,gBAAe;AAAA,YAAAlB,WAAU;AAAA,YACxBH,UAAAC,kCAAA,IAACqB,YAAA;AAAA,cACCC,MAAK;AAAA,cACLC,UAAUjF,KAAKf,QAAQ,IAAIrC,KAAKoD,KAAKf,KAAK,IAAI;AAAA,cAC9CiG,UAAW5C,UAASD,iBAAiB/C,SAASU,KAAKC,IAAIqC,IAAI;AAAA,cAC3D6C,cAAY;AAAA,YACd,CAAA;AAAA,UACF,CAAA,CAAA;AAAA,QACF,CAAA;AAAA,MAEJ,KAAK;AAED,eAAAzB,kCAAAA,IAACS,OAAA;AAAA,UACCnF,MAAK;AAAA,UACLC,OAAOe,KAAKG,YAAY;AAAA,UACxBiE,UAAWC,OACTlC,qBAAqB7C,SAASU,KAAKC,IAAIoE,EAAEC,OAAOrF,KAAK;AAAA,UAEvD6E,aAAY;AAAA,QAAA,CACd;AAAA,MAEJ,KAAK;AAGD,eAAAJ,kCAAAA,IAACS,OAAA;AAAA,UACCnF,MAAK;AAAA,UACLC,OAAOe,KAAKf,SAAS;AAAA,UACrBmF,UAAWC,OACTjC,kBAAkB9C,SAASU,KAAKC,IAAIoE,EAAEC,OAAOrF,KAAK;AAAA,UAEpD6E,aAAa,UAAUV,UAAUgC,QAAQ,UAAU,EAAE,CAAC;AAAA,QAAA,CACxD;AAAA,MAEJ,KAAK;AAED,eAAA9B,kCAAAA,KAACC,QAAA;AAAA,UACCtE,OAAOe,KAAKf,SAAS;AAAA,UACrBuE,eAAgBvE,WACdmD,kBAAkB9C,SAASU,KAAKC,IAAIhB,KAAK;AAAA,UAG3CwE,UAAA,CAAAC,kCAAA,IAACC;YAAcC,WAAU;AAAA,YACvBH,gDAACI,aAAY;AAAA,cAAAC,aAAY;AAAA,YAAS,CAAA;AAAA,UACpC,CAAA,GACAJ,kCAAA,IAACK;YAAcH,WAAU;AAAA,YACtBH,2DAAU4B,mCAAanD,+BAASzC,IAAKuE,kDACnCC,YAA8B;AAAA,cAAAhF,OAAO+E,OAAO/E;AAAAA,cAC1CwE,UAAAO,OAAOE;AAAAA,eADOF,OAAO/E,KAExB;AAAA,UAEJ,CAAA,CAAA;AAAA,QAAA,CACF;AAAA,MAEJ;AAEI,eAAAyE,kCAAAA,IAACS,OAAA;AAAA,UACCnF,MAAK;AAAA,UACLC,OAAOe,KAAKf,SAAS;AAAA,UACrBmF,UAAWC,OACTjC,kBAAkB9C,SAASU,KAAKC,IAAIoE,EAAEC,OAAOrF,KAAK;AAAA,QAAA,CAEtD;AAAA,IAEN;AAAA,EACF;AAGM,QAAAqG,oBAAqBhG,aAAuB;AAChD,QAAI,CAACT,oBAAoB,CAACX,QAAQI,oBAA4B,QAAA;AAE9D,UAAMsB,iBACJf,iBAAiBU,cAAcD,OAAO,EAAEM;AACtC,QAAA,CAACA,eAAuB,QAAA;AAE5B,UAAMwB,cAAcxB,eAAepB,OAChC6E,WAAUA,MAAMrE,SAAS,OAC5B;AACI,QAAAoC,YAAYqB,WAAW,EAAU,QAAA;AAErC,iDACG,OAAI;AAAA,MAAAmB,WAAU;AAAA,MACZH,UAAYrC,YAAA3B,IAAK4D,WAAU;;AAE1B,cAAMrD,QAAO9B,mBAAQI,wBAARJ,mBACXqH,eAAejG,OAAO,OADXpB,mBAEVa,KAAMiB,WAASA,MAAKE,aAAamD,MAAMpE;AAEtC,YAAA,CAACe,KAAa,QAAA;AAElB,qDACG,OAAkB;AAAA,UAAA4D,WAAU;AAAA,UAC3BH,UAACH,kCAAA,KAAA,OAAA;AAAA,YAAIM,WAAU;AAAA,YACbH,UAAA,CAACH,kCAAA,KAAA,SAAA;AAAA,cAAMM,WAAU;AAAA,cAAuBH,UAAA,CAAMJ,MAAAa,OAAM,GAAA;AAAA,YAAC,CAAA,yCACpD,OACC;AAAA,cAAAT,UAAAC,kCAAA,IAACS,OAAA;AAAA,gBACCnF,MAAK;AAAA,gBACLC,OAAOe,KAAKf,SAAS;AAAA,gBACrBmF,UAAWC,OACTjC,kBACEmD,eAAejG,OAAO,GACtBU,KAAKC,IACLoE,EAAEC,OAAOrF,KACX;AAAA,cAEJ,CAAA;AAAA,YACF,CAAA,CAAA;AAAA,UACF,CAAA;AAAA,QAAA,GAhBQe,KAAKC,EAiBf;AAAA,MAEH,CAAA;AAAA,IACH,CAAA;AAAA,EAEJ;AAGM,QAAAuF,sBAAuBlG,aAAuB;;AAClD,QAAI,CAACT,oBAAoB,CAACX,QAAQI,oBAA4B,QAAA;AAExD,UAAAwD,gBAAgBjD,iBAAiBU,cAAcD,OAAO;AACtD,UAAAoB,QAAQxC,QAAQI,oBAAoBgB,OAAO;AAG3C,UAAAmG,uBAAsB3D,mBAAclC,mBAAdkC,mBAA8B/C,KACvD2G,YAAWA,OAAO1G,SAAS;AAI9B,UAAM2G,iBACJrG,YAAY,YAAYoB,MAAM+B,SAASX,cAActC,WAAWiD;AAGhE,WAAAa,kCAAAA,KAAC,OAAI;AAAA,MAAAM,WAAU;AAAA,MACbH,UAAA,CAACH,kCAAA,KAAA,KAAA;AAAA,QAAEM,WAAU;AAAA,QACVH,UAAA,CAAc3B,cAAAoC,OAAM,iBAAA;AAAA,MACvB,CAAA,GACCpC,cAAcvB,eACbmD,kCAAAA,IAAC;QAAEE,WAAU;AAAA,QACVH,wBAAclD;AAAAA,MACjB,CAAA,GAGDG,MAAMjB,IAAI,CAACO,MAAM4F,UAAU;;AACpB,cAAA1F,WAAW4B,cAActC,WAAWT,KACvC6B,UAASA,KAAK3B,UAAUe,KAAKE,QAChC;AAEM,cAAA2F,cAAc,CAAC,EAAC3F,qCAAU+B;AAC1B,cAAA6D,mBAAiB5F,MAAAA,qCAAUmF,gBAAVnF,gBAAAA,IAAuBmD,UAAS;AAIvD,cAAMT,sBACJtD,YAAY,WACRwC,cAActC,aACdsC,cAActC,WAAWhB,OACtBoC,UACC;;AAAAA,sBAAK3B,UAAUe,KAAKE,YACpB,GAAChC,MAAAA,QAAQI,wBAARJ,gBAAAA,IAA8BoB,SAASqC,KACrCoE,OAAMA,EAAE9F,OAAOD,KAAKC,MAAM8F,EAAE7F,aAAaU,KAAK3B;AAAAA,SAErD;AAGJ,eAAAqE,kCAAAA,KAAC,OAAkB;AAAA,UAAAM,WAAU;AAAA,UAC3BH,UAAA,CAAAH,kCAAA,KAAC,OAAA;AAAA,YACCM,WAAW,iBACTiC,cAAc,gBAAgB,aAChC;AAAA,YAEApC,UAAA,CAAAH,kCAAA,KAACC,QAAA;AAAA,cACCtE,OAAOe,KAAKE;AAAAA,cACZsD,eAAgBvE,WACduC,qBAAqBlC,SAASU,KAAKC,IAAIhB,KAAK;AAAA,cAG9CwE,UAAA,CAAAC,kCAAA,IAACC;gBAAcC,WAAU;AAAA,gBACvBH,gDAACI,aAAY;AAAA,kBAAAC,aAAY;AAAA,gBAAW,CAAA;AAAA,cACtC,CAAA,yCACCC,eAAc;AAAA,gBAAAH,WAAU;AAAA,gBACtBH,UAAoBb,oBAAAnD,IAAKS,eACxBwD,kCAAAA,IAACO;kBAAgChF,OAAOiB,UAASjB;AAAAA,kBAC9CwE,UAAAvD,UAASgE;AAAAA,mBADKhE,UAASjB,KAE1B,CACD;AAAA,cACH,CAAA,CAAA;AAAA,YAAA,CACF,GAEC4G,eAAe1C,oBAAoB7D,SAASU,MAAME,QAAQ,GAE1DqE,iBAAiBjF,SAASU,MAAM8F,cAAc,GAE9CpF,MAAM+B,SAAS,KACdiB,kCAAA,IAAC,OAAA;AAAA,cACCE,WAAU;AAAA,cACVoC,SAASA,MAAMhD,UAAU1D,SAASU,KAAKC,EAAE;AAAA,cAEzCwD,UAAAC,kCAAA,IAACuC,QAAW;AAAA,gBAAArC,WAAU;AAAA,cAAuB,CAAA;AAAA,YAAA,CAC/C,CAAA;AAAA,UAEJ,CAAA,GACCgC,UAAU,KAAKH,uBACd/B,kCAAAA,IAACwC,qBAAA;AAAA,YACC5G;AAAAA,YACA6G,eAAeV;AAAAA,YACfvH;AAAAA,YACAC;AAAAA,YACAU;AAAAA,UAAA,CACF,CAAA;AAAA,QAAA,GA5CMmB,KAAKC,EA8Cf;AAAA,OAEH,GAEA0F,kBACCrC,kCAAA,KAACqB,QAAA;AAAA,QACC3F,MAAK;AAAA,QACL4E,WAAU;AAAA,QACVgB,SAAQ;AAAA,QACRoB,SAASA,MAAMxD,OAAOlD,OAAO;AAAA,QAE7BmE,UAAA,CAACC,kCAAA,IAAA0C,MAAA;AAAA,UAASxC,WAAU;AAAA,QAAe,CAAA,GAAE,gBAAa,KACjDtE,QAAQ+G,YAAY,CAAA;AAAA,MAAA,CACvB,GAIDf,kBAAkBhG,OAAO,CAAA;AAAA,IAC5B,CAAA;AAAA,EAEJ;AAEA,gDACG,OACC;AAAA,IAAAmE,UAAA,CAAAC,kCAAA,IAAC,OACC;AAAA,MAAAD,UAAAH,kCAAA,KAAC,OAAA;AAAA,QACC,mBAAgB;AAAA,QAChBM,WAAU;AAAA,QAEVH,UAAA,CAAAC,kCAAA,IAAC;UAAIE,WAAU;AAAA,UAAyBoC,SAASA,MAAMtI,SAAS,EAAE;AAAA,UAChE+F,UAAAC,kCAAA,IAAC,OAAA;AAAA,YACC4C,OAAM;AAAA,YACNC,QAAO;AAAA,YACPC,SAAQ;AAAA,YACRC,MAAK;AAAA,YACLC,OAAM;AAAA,YAENjD,UAAAC,kCAAA,IAAC,QAAA;AAAA,cACCiD,UAAS;AAAA,cACTC,UAAS;AAAA,cACTC,GAAE;AAAA,cACFJ,MAAK;AAAA,YACP,CAAA;AAAA,UACF,CAAA;AAAA,QACF,CAAA,GACC/C,kCAAA,IAAA,MAAA;AAAA,UAAGE,WAAU;AAAA,UAA4CH,UAE1D;AAAA,QAAA,CAAA,CAAA;AAAA,MACF,CAAA;AAAA,IACF,CAAA,GAEAC,kCAAA,IAAC;MAAIE,WAAU;AAAA,MACbH,iDAACjG,QAAQsJ,MAAR;AAAA,QAAaC,QAAO;AAAA,QACnBtD,UAAA,CAAAC,kCAAA,IAAC;UAAM1E,MAAK;AAAA,UAASI,MAAK;AAAA,UAAaH,OAAM;AAAA,QAAa,CAAA,GAC1DyE,kCAAA,IAAC;UAAM1E,MAAK;AAAA,UAASI,MAAK;AAAA,UAAWH,OAAO3B,OAAO2C;AAAAA,QAAI,CAAA,GACvDyD,kCAAA,IAAC,SAAM;AAAA,UAAA1E,MAAK;AAAA,UAASI,MAAK;AAAA,UAAUH,OAAO+H,KAAKC,UAAU/I,OAAO;AAAA,QAAG,CAAA,GACnEoF,kCAAA,KAAA,OAAA;AAAA,UAAI,mBAAgB;AAAA,UAAcM,WAAU;AAAA,UAC3CH,UAAA,CAACH,kCAAA,KAAA,OAAA;AAAA,YAAIM,WAAU;AAAA,YACbH,UAAA,CAACC,kCAAA,IAAA,KAAA;AAAA,cAAEE,WAAU;AAAA,cAAoCH,UAEjD;AAAA,YAAA,CAAA,GACCC,kCAAA,IAAA,KAAA;AAAA,cAAEE,WAAU;AAAA,cAAoCH,UAEjD;AAAA,YAAA,CAAA,GACAH,kCAAA,KAAC,OAAI;AAAA,cAAAM,WAAU;AAAA,cACbH,UAAA,CAACC,kCAAA,IAAA,KAAA;AAAA,gBAAEE,WAAU;AAAA,gBAAoBH,UAAY;AAAA,cAAA,CAAA,GAC7CH,kCAAA,KAACC,QAAA;AAAA,gBACCtE,OAAOf,QAAQG,cAAc;AAAA,gBAC7BmF,eAAgBvE,WAAU;AACbd,6BAAA;AAAA,oBACT,GAAGD;AAAAA,oBACHG,YAAYY;AAAAA,kBACd,CAAC;AAAA,gBACH;AAAA,gBAEAwE,UAAA,CAAAC,kCAAA,IAACC;kBAAcC,WAAU;AAAA,kBACvBH,gDAACI,aAAY;AAAA,oBAAAC,aAAY;AAAA,kBAAS,CAAA;AAAA,gBACpC,CAAA,yCACCC,eAAc;AAAA,kBAAAH,WAAU;AAAA,kBACtBH,UAAkBrG,kBAAA8J,YAAYzH,IAAKT,gDACjCiF,YAA4B;AAAA,oBAAAhF,OAAOD,KAAKC;AAAAA,oBACtCwE,UAAAzE,KAAKkF;AAAAA,qBADSlF,KAAKC,KAEtB,CACD;AAAA,gBACH,CAAA,CAAA;AAAA,cAAA,CACF,CAAA;AAAA,YACF,CAAA,GACAyE,kCAAA,IAACS,OAAA;AAAA,cACCnF,MAAK;AAAA,cACL8E,aAAY;AAAA,cACZF,WAAU;AAAA,cACV3E,OAAOf,QAAQmC,cAAc;AAAA,cAC7B+D,UAAWC,OAAM;AACJlG,2BAAA;AAAA,kBACT,GAAGD;AAAAA,kBACHmC,YAAYgE,EAAEC,OAAOrF;AAAAA,gBACvB,CAAC;AAAA,cACH;AAAA,YAAA,CACF,GACAyE,kCAAA,IAACS,OAAA;AAAA,cACCnF,MAAK;AAAA,cACL8E,aAAY;AAAA,cACZF,WAAU;AAAA,cACV3E,OAAOf,QAAQqC,eAAe;AAAA,cAC9B6D,UAAWC,OAAM;AACJlG,2BAAA;AAAA,kBACT,GAAGD;AAAAA,kBACHqC,aAAa8D,EAAEC,OAAOrF;AAAAA,gBACxB,CAAC;AAAA,cACH;AAAA,YAAA,CACF,CAAA;AAAA,WACF,GAECJ,oBAEIyE,kCAAA,KAAA6D,4BAAA;AAAA,YAAA1D,UAAA,CAAA+B,oBAAoB,UAAU,GAC9BA,oBAAoB,QAAQ,GAC5BA,oBAAoB,UAAU,CAAA;AAAA,UACjC,CAAA,CAAA;AAAA,QAEJ,CAAA,yCAEC,OAAI;AAAA,UAAA5B,WAAU;AAAA,UACbH,UAACH,kCAAA,KAAA,OAAA;AAAA,YAAIM,WAAU;AAAA,YACbH,UAAA,CAAAC,kCAAA,IAACiB,QAAA;AAAA,cACCqB,SAASA,MAAMtI,SAAS,EAAE;AAAA,cAC1BsB,MAAK;AAAA,cACL4F,SAAQ;AAAA,cACRhB,WAAU;AAAA,cACXH,UAAA;AAAA,YAAA,CAED,GACAC,kCAAA,IAACiB,QAAA;AAAA,cACC3F,MAAK;AAAA,cACL4E,WAAU;AAAA,cACXH,UAAA;AAAA,YAAA,CAED,CAAA;AAAA,UACF,CAAA;AAAA,QACF,CAAA,CAAA;AAAA,MACF,CAAA;AAAA,IACF,CAAA,CAAA;AAAA,EACF,CAAA;AAEJ;AAgBA,MAAMyC,sBAA0DA,CAAC;AAAA,EAC/D5G;AAAAA,EACA6G;AAAAA,EACAjI;AAAAA,EACAC;AAAAA,EACAU;AACF,MAAM;;AACE,QAAAuI,eACJ9H,YAAY,cACZ6G,cAAcnH,SAAS,sBACvBd,mBAAQI,wBAARJ,mBAA6BK,aAA7BL,mBAAuCuE,YAAW;AAE9C,QAAA4E,aAAaC,aAAAA,QAAQ,MAAM;;AAE7B,aAAApJ,OAAAA,MAAAA,QAAQI,wBAARJ,gBAAAA,IAA6BQ,uBAA7BR,gBAAAA,IAAiDa,KAC9CiB,UAASA,KAAKE,aAAaiG,cAAclH,WACvC;AAAA,MACHgB,IAAItD,WAAW;AAAA,MACfuD,UAAUiG,cAAclH;AAAAA,MACxBA,SAAOkH,OAAAA,MAAAA,cAAcjE,YAAdiE,gBAAAA,IAAwB,OAAxBA,gBAAAA,IAA4BlH,UAAS;AAAA,IAC9C;AAAA,EAEJ,GAAG,CAACf,SAASiI,aAAa,CAAC;AAErB,QAAA/D,oBAAqBmF,cAAqB;;AAC9C,UAAMC,oBAAoB,CACxB,MAAItJ,MAAAA,QAAQI,wBAARJ,gBAAAA,IAA6BQ,uBAAsB,CAAA,GAAIF,OACxDwB,UAASA,KAAKE,aAAaiG,cAAclH,KAC5C,GACA;AAAA,MACEgB,IAAIoH,WAAWpH;AAAAA,MACfC,UAAUiG,cAAclH;AAAAA,MACxBA,OAAOsI;AAAAA,IACT,CAAA;AAGSpJ,eAAA;AAAA,MACT,GAAGD;AAAAA,MACHI,qBAAqB;AAAA,QACnBC,YAAUL,MAAAA,QAAQI,wBAARJ,gBAAAA,IAA6BK,aAAY,CAAC;AAAA,QACpDC,UAAQN,MAAAA,QAAQI,wBAARJ,gBAAAA,IAA6BM,WAAU,CAAC;AAAA,QAChDC,YAAUP,MAAAA,QAAQI,wBAARJ,gBAAAA,IAA6BO,aAAY,CAAC;AAAA,QACpDC,oBAAoB8I;AAAAA,MACtB;AAAA,IACF,CAAC;AAAA,EACH;AAGA1J,eAAAA,UAAU,MAAM;;AACV,QAAA,CAACI,QAAQI,oBAAqB;AAE5B,UAAAmJ,gBAAgBvJ,QAAQI,oBAAoBC;AAClD,UAAMmJ,wBACJ7I,MAAAA,qDAAkBU,cAAchB,SAASqB,mBAAzCf,gBAAAA,IAAyDE,KACtD2G,YAAWA,OAAO1G,SAAS;AAEhC,QAAI,CAAC0I,qBAAsB;AAE3B,UAAMC,oBAAoB,CACxB,GAAIzJ,QAAQI,oBAAoBI,sBAAsB,CAAA,CACxD;AACA,UAAMkJ,uBAAuBD,kBAAkB3F,UAC5ChC,UAASA,KAAKE,aAAawH,qBAAqBzI,KACnD;AAGA,QAAIwI,cAAchF,WAAW,KAAKmF,yBAAyB,IAAI;AAC7DD,wBAAkB7G,KAAK;AAAA,QACrBb,IAAItD,WAAW;AAAA,QACfuD,UAAUwH,qBAAqBzI;AAAAA,QAC/BA,SAAOyI,OAAAA,MAAAA,qBAAqBxF,YAArBwF,gBAAAA,IAA+B,OAA/BA,gBAAAA,IAAmCzI,UAAS;AAAA,MACrD,CAAC;AACDd,iBAAY0J,UAAU;;AAAA;AAAA,UACpB,GAAGA;AAAAA,UACHvJ,qBAAqB;AAAA,YACnB,GAAGuJ,KAAKvJ;AAAAA,YACRC,YAAUsJ,MAAAA,KAAKvJ,wBAALuJ,gBAAAA,IAA0BtJ,aAAY,CAAC;AAAA,YACjDC,UAAQqJ,MAAAA,KAAKvJ,wBAALuJ,gBAAAA,IAA0BrJ,WAAU,CAAC;AAAA,YAC7CC,YAAUoJ,MAAAA,KAAKvJ,wBAALuJ,gBAAAA,IAA0BpJ,aAAY,CAAC;AAAA,YACjDC,oBAAoBiJ;AAAAA,UACtB;AAAA,QACF;AAAA,OAAE;AAAA,IACJ;AAGA,QAAIF,cAAchF,WAAW,KAAKmF,yBAAyB,IAAI;AAC3CD,wBAAAG,OAAOF,sBAAsB,CAAC;AAChDzJ,iBAAY0J,UAAU;;AAAA;AAAA,UACpB,GAAGA;AAAAA,UACHvJ,qBAAqB;AAAA,YACnB,GAAGuJ,KAAKvJ;AAAAA,YACRC,YAAUsJ,MAAAA,KAAKvJ,wBAALuJ,gBAAAA,IAA0BtJ,aAAY,CAAC;AAAA,YACjDC,UAAQqJ,MAAAA,KAAKvJ,wBAALuJ,gBAAAA,IAA0BrJ,WAAU,CAAC;AAAA,YAC7CC,YAAUoJ,MAAAA,KAAKvJ,wBAALuJ,gBAAAA,IAA0BpJ,aAAY,CAAC;AAAA,YACjDC,oBAAoBiJ;AAAAA,UACtB;AAAA,QACF;AAAA,OAAE;AAAA,IACJ;AAAA,KACC,EAACzJ,mBAAQI,wBAARJ,mBAA6BK,aAA7BL,mBAAuCuE,MAAM,CAAC;AAE9C,MAAA,CAAC2E,aAAqB,QAAA;AAE1B,+CACG,OAAI;AAAA,IAAAxD,WAAU;AAAA,IACbH,UAAAC,kCAAA,IAACqE;MAAK9I,OAAOoI,WAAWpI;AAAAA,MAAOuE,eAAepB;AAAAA,MAC5CqB,UAACC,kCAAA,IAAAsE,UAAA;AAAA,QACEvE,8BAAcvB,+BAASzC,IAAKuE,YAC3BN,kCAAA,IAACuE,aAAA;AAAA,UAEChJ,OAAO+E,OAAO/E;AAAAA,UACd2E,WAAU;AAAA,UAETH,UAAOO,OAAAE;AAAAA,QAAA,GAJHF,OAAO/E,KAKd;AAAA,MAEJ,CAAA;AAAA,IACF,CAAA;AAAA,EACF,CAAA;AAEJ;"}