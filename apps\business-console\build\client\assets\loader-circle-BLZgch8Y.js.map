{"version": 3, "file": "loader-circle-BLZgch8Y.js", "sources": ["../../../node_modules/lucide-react/dist/esm/icons/loader-circle.js"], "sourcesContent": ["/**\n * @license lucide-react v0.453.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst LoaderCircle = createLucideIcon(\"LoaderCircle\", [\n  [\"path\", { d: \"M21 12a9 9 0 1 1-6.219-8.56\", key: \"13zald\" }]\n]);\n\nexport { LoaderCircle as default };\n//# sourceMappingURL=loader-circle.js.map\n"], "names": [], "mappings": ";AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASK,MAAC,eAAe,iBAAiB,gBAAgB;AAAA,EACpD,CAAC,QAAQ,EAAE,GAAG,+BAA+B,KAAK,SAAU,CAAA;AAC9D,CAAC;", "x_google_ignoreList": [0]}