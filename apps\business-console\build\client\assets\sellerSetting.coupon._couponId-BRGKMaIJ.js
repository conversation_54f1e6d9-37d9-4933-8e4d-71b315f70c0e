import { r as reactExports, j as jsxRuntimeExports } from "./jsx-runtime-bB2y7OuD.js";
import { B as Button } from "./button-ByAXMyvk.js";
import { C as Calendar, a as Calendar$1 } from "./calendar-_8-DqkPN.js";
import { I as Input } from "./input-3v87qohQ.js";
import { P as Popover, a as PopoverTrigger, b as PopoverContent } from "./popover-CD2vRFIm.js";
import { S as Select, a as SelectTrigger, b as SelectValue, c as SelectContent, d as SelectItem } from "./select-BFGSXKcr.js";
import { u as useToast } from "./ToastProvider-rciVe3-g.js";
import { T as Tabs, a as TabsList, b as TabsTrigger } from "./tabs-CfSdyzWr.js";
import { c as configKeysEnum, D as DISCOUNT_UNIT_MAP } from "./coupons-sX9zH31d.js";
import { u as useLoaderData, a as useFetcher } from "./components-D7UvGag_.js";
import { a as useNavigate } from "./index-DhHTcibu.js";
import { T as Trash2 } from "./trash-2-DjkfFIB-.js";
import { P as Plus } from "./plus-D1sB_2yX.js";
import { f as format } from "./format-82yT_5--.js";
import "./index-z_byfFrQ.js";
import "./index-ImHKLo0a.js";
import "./utils-GkgzjW3c.js";
import "./createLucideIcon-uwkRm45G.js";
import "./addMonths-Dj4hq91A.js";
import "./isSameDay-BQMn9z7h.js";
import "./addDays-CyH8qBoF.js";
import "./chevron-right-B-tR7Kir.js";
import "./chevron-left-CLqBlTg1.js";
import "./index-D7VH9Fc8.js";
import "./index-BXzPK7u0.js";
import "./index-CzZ6EYkN.js";
import "./index-Vp2vNLNM.js";
import "./index-CEHS9zzk.js";
import "./index-DVTNuYOr.js";
import "./index-DscYByPT.js";
import "./index-CkL5tk39.js";
import "./index-QLGF6kQx.js";
import "./index-dIGjFc0I.js";
import "./index-IXOTxK3N.js";
import "./index-qCtcpOcW.js";
import "./index-BTdCMChR.js";
import "./index-CpKiYcZd.js";
import "./index-C88PRvfd.js";
import "./chevron-down-pCP5jmjX.js";
import "./check-_dbWxIzT.js";
import "./index-CG37gmC0.js";
const generateId = () => Date.now().toString(36) + Math.random().toString(36).substring(2, 9);
function EditCoupon() {
  const {
    preloadData: couponPreloadData,
    couponData: coupon
  } = useLoaderData();
  const fetcher = useFetcher();
  const navigate = useNavigate();
  const {
    showToast
  } = useToast();
  reactExports.useEffect(() => {
    if (fetcher.state === "idle" && fetcher.data) {
      if (fetcher.data.success === true) {
        showToast("Coupon updated successfully", "success");
        navigate(-1);
      } else if (fetcher.data.success === false) {
        showToast("Failed to update coupon", "error");
      }
    }
  }, [fetcher.state, fetcher.data]);
  const [payload, setPayload] = reactExports.useState({
    couponType: "",
    customConfiguration: {
      discount: [],
      filter: [],
      validity: [],
      discountAdditional: [],
      filterAdditional: [],
      validityAdditional: []
    }
  });
  const selectedTypeData = payload.couponType ? couponPreloadData.couponTypesConfigurations.find((type) => type.value === payload.couponType) : null;
  reactExports.useEffect(() => {
    if (!coupon || !couponPreloadData) return;
    const selectedConfig = couponPreloadData.couponTypesConfigurations.find((c) => c.value === coupon.name);
    if (!selectedConfig) return;
    const getAllowedProps = (section) => selectedConfig.configuration[section].properties.map((p) => p.value);
    const getFixedProps = (section) => {
      var _a;
      return ((_a = selectedConfig.configuration[section].propertyConfig) == null ? void 0 : _a.map((p) => p.value)) || [];
    };
    const getFixedConfigItems = (section) => {
      var _a;
      return ((_a = selectedConfig.configuration[section].propertyConfig) == null ? void 0 : _a.filter((p) => p.type === "fixed")) || [];
    };
    const allowed = {
      discount: getAllowedProps("discount"),
      filter: getAllowedProps("filter"),
      validity: getAllowedProps("validity"),
      discountAdditional: getFixedProps("discount"),
      filterAdditional: getFixedProps("filter"),
      validityAdditional: getFixedProps("validity")
    };
    const toIdConfigItem = (item) => ({
      id: generateId(),
      property: item.property,
      operator: item.operator,
      value: item.value
    });
    const transformedPayload = {
      couponType: coupon.name,
      couponCode: coupon.code,
      description: coupon.description,
      customConfiguration: {
        discount: [],
        filter: [],
        validity: [],
        discountAdditional: [],
        filterAdditional: [],
        validityAdditional: []
      }
    };
    const addItems = (configKey, items = []) => {
      items.forEach((item) => {
        const prop = item.property;
        if (allowed[configKey].includes(prop)) {
          transformedPayload.customConfiguration[configKey].push(toIdConfigItem(item));
        }
        if (allowed[`${configKey}Additional`].includes(prop)) {
          transformedPayload.customConfiguration[`${configKey}Additional`].push(toIdConfigItem(item));
        }
      });
    };
    addItems("discount", coupon.discountConfiguration);
    addItems("filter", coupon.filterConfiguration);
    addItems("validity", coupon.validityConfiguration);
    const initializeMissingFixedInputs = (configKey) => {
      var _a, _b;
      const existingProps = ((_b = (_a = transformedPayload.customConfiguration) == null ? void 0 : _a[`${configKey}Additional`]) == null ? void 0 : _b.map((item) => item.property)) || [];
      const fixedInputs = getFixedConfigItems(configKey);
      fixedInputs.forEach((fixedItem) => {
        var _a2;
        if (!existingProps.includes(fixedItem.value)) {
          (_a2 = transformedPayload.customConfiguration) == null ? void 0 : _a2[`${configKey}Additional`].push({
            id: generateId(),
            property: fixedItem.value,
            value: ""
          });
        }
      });
    };
    initializeMissingFixedInputs("discount");
    initializeMissingFixedInputs("filter");
    initializeMissingFixedInputs("validity");
    setPayload(transformedPayload);
  }, [coupon, couponPreloadData]);
  const getAvailableProperties = (section) => {
    var _a;
    if (!selectedTypeData) return [];
    if (section === "filter") {
      return selectedTypeData.configuration[section].properties;
    }
    const selectedProperties = ((_a = payload.customConfiguration) == null ? void 0 : _a[section].map((item) => item.property)) || [];
    return selectedTypeData.configuration[section].properties.filter((prop) => !selectedProperties.includes(prop.value));
  };
  const handlePropertyChange = (section, id, value) => {
    var _a, _b, _c;
    if (!payload.customConfiguration) return;
    const currentItem = payload.customConfiguration[section].find((item) => item.id === id);
    if ((currentItem == null ? void 0 : currentItem.property) === value) {
      return;
    }
    if (section === "discount" || section === "validity") {
      const isAlreadySelected = payload.customConfiguration[section].some((item) => item.id !== id && item.property === value);
      if (isAlreadySelected) {
        console.warn(`Property ${value} is already selected in ${section} section`);
        return;
      }
    }
    const sectionConfig = [...payload.customConfiguration[section]];
    const itemIndex = sectionConfig.findIndex((item) => item.id === id);
    if (itemIndex === -1) return;
    const propertyConfig = selectedTypeData == null ? void 0 : selectedTypeData.configuration[section].properties.find((prop) => prop.value === value);
    sectionConfig[itemIndex] = {
      ...sectionConfig[itemIndex],
      property: value,
      operator: ((_c = (_b = (_a = propertyConfig == null ? void 0 : propertyConfig.operatorConfig) == null ? void 0 : _a.options) == null ? void 0 : _b[0]) == null ? void 0 : _c.value) || void 0,
      value: ""
    };
    setPayload({
      ...payload,
      customConfiguration: {
        ...payload.customConfiguration,
        [section]: sectionConfig
      }
    });
  };
  const handleOperatorChange = (section, id, value) => {
    if (!payload.customConfiguration) return;
    const sectionConfig = [...payload.customConfiguration[section]];
    const itemIndex = sectionConfig.findIndex((item) => item.id === id);
    if (itemIndex === -1) return;
    sectionConfig[itemIndex] = {
      ...sectionConfig[itemIndex],
      operator: value
    };
    setPayload({
      ...payload,
      customConfiguration: {
        ...payload.customConfiguration,
        [section]: sectionConfig
      }
    });
  };
  const handleValueChange = (section, id, value) => {
    if (!payload.customConfiguration) return;
    const sectionConfig = [...payload.customConfiguration[section] || []];
    const itemIndex = sectionConfig.findIndex((item) => item.id === id);
    if (itemIndex === -1) return;
    sectionConfig[itemIndex] = {
      ...sectionConfig[itemIndex],
      value
    };
    setPayload({
      ...payload,
      customConfiguration: {
        ...payload.customConfiguration,
        [section]: sectionConfig
      }
    });
  };
  const handleDateChange = (section, id, date) => {
    if (!payload.customConfiguration || !date) return;
    const sectionConfig = [...payload.customConfiguration[section]];
    const itemIndex = sectionConfig.findIndex((item) => item.id === id);
    if (itemIndex === -1) return;
    sectionConfig[itemIndex] = {
      ...sectionConfig[itemIndex],
      value: format(date, "yyyy-MM-dd HH:mm:ss")
    };
    setPayload({
      ...payload,
      customConfiguration: {
        ...payload.customConfiguration,
        [section]: sectionConfig
      }
    });
  };
  const addRow = (section) => {
    var _a, _b, _c, _d, _e, _f;
    if (!payload.customConfiguration || !selectedTypeData) return;
    const properties = selectedTypeData.configuration[section].properties;
    if (properties.length === 0) return;
    if (section === "discount" || section === "validity") {
      const currentRows = payload.customConfiguration[section].length;
      const totalProperties = properties.length;
      if (currentRows >= totalProperties) {
        console.warn(`Cannot add more rows to ${section} section. Maximum reached.`);
        return;
      }
      const availableProperties = getAvailableProperties(section);
      if (availableProperties.length === 0) {
        console.warn(`No more available properties for ${section} section`);
        return;
      }
      const newProperty = availableProperties[0].value;
      const newOperator = (_c = (_b = (_a = availableProperties[0].operatorConfig) == null ? void 0 : _a.options) == null ? void 0 : _b[0]) == null ? void 0 : _c.value;
      const newItem = {
        id: generateId(),
        property: newProperty,
        operator: newOperator,
        value: ""
      };
      setPayload({
        ...payload,
        customConfiguration: {
          ...payload.customConfiguration,
          [section]: [...payload.customConfiguration[section], newItem]
        }
      });
    } else {
      const newItem = {
        id: generateId(),
        property: properties[0].value,
        operator: (_f = (_e = (_d = properties[0].operatorConfig) == null ? void 0 : _d.options) == null ? void 0 : _e[0]) == null ? void 0 : _f.value,
        value: ""
      };
      setPayload({
        ...payload,
        customConfiguration: {
          ...payload.customConfiguration,
          [section]: [...payload.customConfiguration[section], newItem]
        }
      });
    }
  };
  const removeRow = (section, id) => {
    if (!payload.customConfiguration) return;
    const sectionConfig = payload.customConfiguration[section].filter((item) => item.id !== id);
    if (sectionConfig.length === 0) {
      addRow(section);
      return;
    }
    setPayload({
      ...payload,
      customConfiguration: {
        ...payload.customConfiguration,
        [section]: sectionConfig
      }
    });
  };
  const getPropertyUnit = (property) => {
    return DISCOUNT_UNIT_MAP[property] || "";
  };
  const renderOperatorInput = (section, item, property) => {
    var _a;
    if (!(property == null ? void 0 : property.operatorConfig)) return null;
    const inputType = property.operatorConfig.input;
    switch (inputType) {
      case "dropdown":
        return /* @__PURE__ */ jsxRuntimeExports.jsxs(Select, {
          value: item.operator || "",
          onValueChange: (value) => handleOperatorChange(section, item.id, value),
          children: [/* @__PURE__ */ jsxRuntimeExports.jsx(SelectTrigger, {
            className: "rounded-lg",
            children: /* @__PURE__ */ jsxRuntimeExports.jsx(SelectValue, {
              placeholder: "Operator"
            })
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(SelectContent, {
            className: "rounded-lg",
            children: (_a = property.operatorConfig.options) == null ? void 0 : _a.map((option) => /* @__PURE__ */ jsxRuntimeExports.jsx(SelectItem, {
              value: option.value,
              children: option.label
            }, option.value))
          })]
        });
      case "text":
        return /* @__PURE__ */ jsxRuntimeExports.jsx(Input, {
          type: "text",
          value: item.operator || "",
          onChange: (e) => handleOperatorChange(section, item.id, e.target.value),
          placeholder: "Operator"
        });
      case "itemSearch":
        return /* @__PURE__ */ jsxRuntimeExports.jsx(Input, {
          type: "text",
          value: item.operator || "",
          onChange: (e) => handleOperatorChange(section, item.id, e.target.value),
          placeholder: "Search item..."
        });
      default:
        return /* @__PURE__ */ jsxRuntimeExports.jsx(Input, {
          type: "text",
          value: item.operator || "",
          onChange: (e) => handleOperatorChange(section, item.id, e.target.value),
          placeholder: "Operator"
        });
    }
  };
  const renderValueInput = (section, item, inputType) => {
    var _a, _b;
    const property = selectedTypeData == null ? void 0 : selectedTypeData.configuration[section].properties.find((prop) => prop.value === item.property);
    switch (inputType) {
      case "text":
        return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
          className: "relative",
          children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Input, {
            type: "text",
            value: item.value || "",
            onChange: (e) => handleValueChange(section, item.id, e.target.value),
            className: "pr-7"
          }), getPropertyUnit(item.property) && /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
            className: "absolute right-3 top-1/2 -translate-y-1/2 text-sm pointer-events-none text-muted-foreground",
            children: getPropertyUnit(item.property)
          })]
        });
      case "date":
        return /* @__PURE__ */ jsxRuntimeExports.jsxs(Popover, {
          children: [/* @__PURE__ */ jsxRuntimeExports.jsx(PopoverTrigger, {
            asChild: true,
            children: /* @__PURE__ */ jsxRuntimeExports.jsxs(Button, {
              type: "button",
              variant: "outline",
              className: "rounded-lg w-full justify-between overflow-hidden",
              children: [item.value ? format(new Date(item.value), "PPP") : "Pick a date", /* @__PURE__ */ jsxRuntimeExports.jsx(Calendar, {
                className: "h-4 w-4"
              })]
            })
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(PopoverContent, {
            className: "w-auto p-0",
            children: /* @__PURE__ */ jsxRuntimeExports.jsx(Calendar$1, {
              mode: "single",
              selected: item.value ? new Date(item.value) : void 0,
              onSelect: (date) => handleDateChange(section, item.id, date),
              initialFocus: true
            })
          })]
        });
      case "userSearch":
        return /* @__PURE__ */ jsxRuntimeExports.jsx(Input, {
          type: "text",
          value: item.operator || "",
          onChange: (e) => handleOperatorChange(section, item.id, e.target.value),
          placeholder: "Search user..."
        });
      case "location":
        return /* @__PURE__ */ jsxRuntimeExports.jsx(Input, {
          type: "text",
          value: item.value || "",
          onChange: (e) => handleValueChange(section, item.id, e.target.value),
          placeholder: `Search ${inputType.replace("Search", "")}`
        });
      case "dropdown":
        return /* @__PURE__ */ jsxRuntimeExports.jsxs(Select, {
          value: item.value || "",
          onValueChange: (value) => handleValueChange(section, item.id, value),
          children: [/* @__PURE__ */ jsxRuntimeExports.jsx(SelectTrigger, {
            className: "rounded-lg",
            children: /* @__PURE__ */ jsxRuntimeExports.jsx(SelectValue, {
              placeholder: "Select"
            })
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(SelectContent, {
            className: "rounded-lg",
            children: (_b = (_a = property == null ? void 0 : property.valueConfig) == null ? void 0 : _a.options) == null ? void 0 : _b.map((option) => /* @__PURE__ */ jsxRuntimeExports.jsx(SelectItem, {
              value: option.value,
              children: option.label
            }, option.value))
          })]
        });
      default:
        return /* @__PURE__ */ jsxRuntimeExports.jsx(Input, {
          type: "text",
          value: item.value || "",
          onChange: (e) => handleValueChange(section, item.id, e.target.value)
        });
    }
  };
  const renderFixedInputs = (section) => {
    if (!selectedTypeData || !payload.customConfiguration) return null;
    const propertyConfig = selectedTypeData.configuration[section].propertyConfig;
    if (!propertyConfig) return null;
    const fixedInputs = propertyConfig.filter((input) => input.type === "fixed");
    if (fixedInputs.length === 0) return null;
    return /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
      className: "mt-4",
      children: fixedInputs.map((input) => {
        var _a, _b;
        const item = (_b = (_a = payload.customConfiguration) == null ? void 0 : _a[configKeysEnum[section]]) == null ? void 0 : _b.find((item2) => item2.property === input.value);
        if (!item) return null;
        return /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
          className: "mt-2",
          children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
            className: "flex flex-row gap-2 items-center",
            children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("label", {
              className: "text-sm font-medium",
              children: [input.label, ":"]
            }), /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
              children: /* @__PURE__ */ jsxRuntimeExports.jsx(Input, {
                type: "text",
                value: item.value || "",
                onChange: (e) => handleValueChange(configKeysEnum[section], item.id, e.target.value)
              })
            })]
          })
        }, item.id);
      })
    });
  };
  const renderConfigSection = (section) => {
    var _a;
    if (!selectedTypeData || !payload.customConfiguration) return null;
    const sectionConfig = selectedTypeData.configuration[section];
    const items = payload.customConfiguration[section];
    const propertyMethodInput = (_a = sectionConfig.propertyConfig) == null ? void 0 : _a.find((config) => config.type === "propertyMethod");
    const canAddMoreRows = section === "filter" || items.length < sectionConfig.properties.length;
    return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
      className: "p-3 pr-8 rounded-lg border mb-3",
      children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("p", {
        className: "text-typography-800 font-semibold",
        children: [sectionConfig.label, " Configurations"]
      }), sectionConfig.description && /* @__PURE__ */ jsxRuntimeExports.jsx("p", {
        className: "text-sm font-medium text-blue-600",
        children: sectionConfig.description
      }), items.map((item, index) => {
        var _a2;
        const property = sectionConfig.properties.find((prop) => prop.value === item.property);
        const hasOperator = !!(property == null ? void 0 : property.operatorConfig);
        const valueInputType = ((_a2 = property == null ? void 0 : property.valueConfig) == null ? void 0 : _a2.input) || "text";
        const availableProperties = section === "filter" ? sectionConfig.properties : sectionConfig.properties.filter((prop) => {
          var _a3;
          return prop.value === item.property || !((_a3 = payload.customConfiguration) == null ? void 0 : _a3[section].some((i) => i.id !== item.id && i.property === prop.value));
        });
        return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
          className: "mt-3",
          children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
            className: `relative grid ${hasOperator ? "grid-cols-3" : "grid-cols-2"} gap-2 items-center`,
            children: [/* @__PURE__ */ jsxRuntimeExports.jsxs(Select, {
              value: item.property,
              onValueChange: (value) => handlePropertyChange(section, item.id, value),
              children: [/* @__PURE__ */ jsxRuntimeExports.jsx(SelectTrigger, {
                className: "rounded-lg",
                children: /* @__PURE__ */ jsxRuntimeExports.jsx(SelectValue, {
                  placeholder: "Property"
                })
              }), /* @__PURE__ */ jsxRuntimeExports.jsx(SelectContent, {
                className: "rounded-lg",
                children: availableProperties.map((property2) => /* @__PURE__ */ jsxRuntimeExports.jsx(SelectItem, {
                  value: property2.value,
                  children: property2.label
                }, property2.value))
              })]
            }), hasOperator && renderOperatorInput(section, item, property), renderValueInput(section, item, valueInputType), items.length > 1 && /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
              className: "absolute -right-7 top-1/2 -translate-y-1/2 px-1 py-2 cursor-pointer hover:bg-accent rounded-md",
              onClick: () => removeRow(section, item.id),
              children: /* @__PURE__ */ jsxRuntimeExports.jsx(Trash2, {
                className: "h-5 w-5 text-red-500"
              })
            })]
          }), index === 0 && propertyMethodInput && /* @__PURE__ */ jsxRuntimeExports.jsx(PropertyMethodInput, {
            section,
            propertyInput: propertyMethodInput,
            payload,
            setPayload,
            selectedTypeData
          })]
        }, item.id);
      }), canAddMoreRows && /* @__PURE__ */ jsxRuntimeExports.jsxs(Button, {
        type: "button",
        className: "mt-3 underline",
        variant: "link",
        onClick: () => addRow(section),
        children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Plus, {
          className: "w-4 h-4 mr-1"
        }), " Add another", " ", section.toLowerCase()]
      }), renderFixedInputs(section)]
    });
  };
  return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
    children: [/* @__PURE__ */ jsxRuntimeExports.jsx("div", {
      children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
        "aria-labelledby": "edit-coupon-heading",
        className: "flex flex-row gap-2 items-center p-2",
        children: [/* @__PURE__ */ jsxRuntimeExports.jsx("div", {
          className: "w-5 h-5 cursor-pointer",
          onClick: () => navigate(-1),
          children: /* @__PURE__ */ jsxRuntimeExports.jsx("svg", {
            width: "100%",
            height: "100%",
            viewBox: "0 0 16 16",
            fill: "none",
            xmlns: "http://www.w3.org/2000/svg",
            children: /* @__PURE__ */ jsxRuntimeExports.jsx("path", {
              fillRule: "evenodd",
              clipRule: "evenodd",
              d: "M7.13805 3.36193C7.3984 3.62228 7.3984 4.04439 7.13805 4.30474L3.60946 7.83333H14C14.3682 7.83333 14.6666 8.13181 14.6666 8.5C14.6666 8.86819 14.3682 9.16667 14 9.16667H3.60946L7.13805 12.6953C7.3984 12.9556 7.3984 13.3777 7.13805 13.6381C6.8777 13.8984 6.45559 13.8984 6.19524 13.6381L1.52858 8.97141C1.26823 8.71106 1.26823 8.28895 1.52858 8.0286L6.19524 3.36193C6.45559 3.10158 6.8777 3.10158 7.13805 3.36193Z",
              fill: "#1F2A37"
            })
          })
        }), /* @__PURE__ */ jsxRuntimeExports.jsx("h2", {
          className: "text-lg font-semibold text-typography-700",
          children: "Edit coupon"
        })]
      })
    }), /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
      className: "pt-3 pb-20",
      children: /* @__PURE__ */ jsxRuntimeExports.jsxs(fetcher.Form, {
        method: "post",
        children: [/* @__PURE__ */ jsxRuntimeExports.jsx("input", {
          type: "hidden",
          name: "actionType",
          value: "editCoupon"
        }), /* @__PURE__ */ jsxRuntimeExports.jsx("input", {
          type: "hidden",
          name: "couponId",
          value: coupon.id
        }), /* @__PURE__ */ jsxRuntimeExports.jsx("input", {
          type: "hidden",
          name: "payload",
          value: JSON.stringify(payload)
        }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
          "aria-labelledby": "edit-coupon",
          className: "p-2",
          children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
            className: "p-3 rounded-lg border mb-3",
            children: [/* @__PURE__ */ jsxRuntimeExports.jsx("p", {
              className: "font-semibold text-typography-800",
              children: "Coupon Details"
            }), /* @__PURE__ */ jsxRuntimeExports.jsx("p", {
              className: "text-sm font-medium text-blue-600",
              children: "These details would be shown to the customers."
            }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
              className: "relative mt-3 flex flex-row gap-5 items-center justify-between",
              children: [/* @__PURE__ */ jsxRuntimeExports.jsx("p", {
                className: "whitespace-nowrap",
                children: "Coupon type:"
              }), /* @__PURE__ */ jsxRuntimeExports.jsxs(Select, {
                value: payload.couponType || "",
                onValueChange: (value) => {
                  setPayload({
                    ...payload,
                    couponType: value
                  });
                },
                children: [/* @__PURE__ */ jsxRuntimeExports.jsx(SelectTrigger, {
                  className: "rounded-lg",
                  children: /* @__PURE__ */ jsxRuntimeExports.jsx(SelectValue, {
                    placeholder: "Select"
                  })
                }), /* @__PURE__ */ jsxRuntimeExports.jsx(SelectContent, {
                  className: "rounded-lg",
                  children: couponPreloadData.couponTypes.map((type) => /* @__PURE__ */ jsxRuntimeExports.jsx(SelectItem, {
                    value: type.value,
                    children: type.label
                  }, type.value))
                })]
              })]
            }), /* @__PURE__ */ jsxRuntimeExports.jsx(Input, {
              type: "text",
              placeholder: "Coupon Code",
              className: "mt-3 rounded-lg",
              value: payload.couponCode || "",
              onChange: (e) => {
                setPayload({
                  ...payload,
                  couponCode: e.target.value
                });
              }
            }), /* @__PURE__ */ jsxRuntimeExports.jsx(Input, {
              type: "text",
              placeholder: "Description",
              className: "mt-3 rounded-lg",
              value: payload.description || "",
              onChange: (e) => {
                setPayload({
                  ...payload,
                  description: e.target.value
                });
              }
            })]
          }), selectedTypeData && /* @__PURE__ */ jsxRuntimeExports.jsxs(jsxRuntimeExports.Fragment, {
            children: [renderConfigSection("discount"), renderConfigSection("filter"), renderConfigSection("validity")]
          })]
        }), /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
          className: "fixed bottom-2 left-0 right-0 z-30 flex justify-center md:static md:mt-6 md:justify-end",
          children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
            className: "w-full md:w-auto px-4 flex flex-row gap-4 items-center justify-between",
            children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
              onClick: () => navigate(-1),
              type: "button",
              variant: "outline",
              className: "flex-1 md:px-12 pointer-events-auto font-bold flex items-center justify-center rounded-lg shadow-xl p-3",
              children: "Cancel"
            }), /* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
              type: "submit",
              className: "flex-1 md:px-12 pointer-events-auto font-bold flex items-center justify-center rounded-lg shadow-xl p-3 hover:bg-primary",
              children: "Save"
            })]
          })
        })]
      })
    })]
  });
}
const PropertyMethodInput = ({
  section,
  propertyInput,
  payload,
  setPayload,
  selectedTypeData
}) => {
  var _a, _b, _c, _d, _e;
  const shouldRender = section === "discount" && propertyInput.type === "propertyMethod" && ((_b = (_a = payload.customConfiguration) == null ? void 0 : _a.discount) == null ? void 0 : _b.length) === 2;
  const configItem = reactExports.useMemo(() => {
    var _a2, _b2, _c2, _d2;
    return ((_b2 = (_a2 = payload.customConfiguration) == null ? void 0 : _a2.discountAdditional) == null ? void 0 : _b2.find((item) => item.property === propertyInput.value)) || {
      id: generateId(),
      property: propertyInput.value,
      value: ((_d2 = (_c2 = propertyInput.options) == null ? void 0 : _c2[0]) == null ? void 0 : _d2.value) || ""
    };
  }, [payload, propertyInput]);
  const handleValueChange = (newValue) => {
    var _a2, _b2, _c2, _d2;
    const updatedAdditional = [...(((_a2 = payload.customConfiguration) == null ? void 0 : _a2.discountAdditional) || []).filter((item) => item.property !== propertyInput.value), {
      id: configItem.id,
      property: propertyInput.value,
      value: newValue
    }];
    setPayload({
      ...payload,
      customConfiguration: {
        discount: ((_b2 = payload.customConfiguration) == null ? void 0 : _b2.discount) || [],
        filter: ((_c2 = payload.customConfiguration) == null ? void 0 : _c2.filter) || [],
        validity: ((_d2 = payload.customConfiguration) == null ? void 0 : _d2.validity) || [],
        discountAdditional: updatedAdditional
      }
    });
  };
  reactExports.useEffect(() => {
    var _a2, _b2, _c2;
    if (!payload.customConfiguration) return;
    const discountItems = payload.customConfiguration.discount;
    const propertyMethodConfig = (_a2 = selectedTypeData == null ? void 0 : selectedTypeData.configuration.discount.propertyConfig) == null ? void 0 : _a2.find((config) => config.type === "propertyMethod");
    if (!propertyMethodConfig) return;
    const currentAdditional = [...payload.customConfiguration.discountAdditional || []];
    const hybDiscStrategyIndex = currentAdditional.findIndex((item) => item.property === propertyMethodConfig.value);
    if (discountItems.length === 2 && hybDiscStrategyIndex === -1) {
      currentAdditional.push({
        id: generateId(),
        property: propertyMethodConfig.value,
        value: ((_c2 = (_b2 = propertyMethodConfig.options) == null ? void 0 : _b2[0]) == null ? void 0 : _c2.value) || ""
      });
      setPayload((prev) => {
        var _a3, _b3, _c3;
        return {
          ...prev,
          customConfiguration: {
            ...prev.customConfiguration,
            discount: ((_a3 = prev.customConfiguration) == null ? void 0 : _a3.discount) || [],
            filter: ((_b3 = prev.customConfiguration) == null ? void 0 : _b3.filter) || [],
            validity: ((_c3 = prev.customConfiguration) == null ? void 0 : _c3.validity) || [],
            discountAdditional: currentAdditional
          }
        };
      });
    }
    if (discountItems.length !== 2 && hybDiscStrategyIndex !== -1) {
      currentAdditional.splice(hybDiscStrategyIndex, 1);
      setPayload((prev) => {
        var _a3, _b3, _c3;
        return {
          ...prev,
          customConfiguration: {
            ...prev.customConfiguration,
            discount: ((_a3 = prev.customConfiguration) == null ? void 0 : _a3.discount) || [],
            filter: ((_b3 = prev.customConfiguration) == null ? void 0 : _b3.filter) || [],
            validity: ((_c3 = prev.customConfiguration) == null ? void 0 : _c3.validity) || [],
            discountAdditional: currentAdditional
          }
        };
      });
    }
  }, [(_d = (_c = payload.customConfiguration) == null ? void 0 : _c.discount) == null ? void 0 : _d.length]);
  if (!shouldRender) return null;
  return /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
    className: "my-2",
    children: /* @__PURE__ */ jsxRuntimeExports.jsx(Tabs, {
      value: configItem.value,
      onValueChange: handleValueChange,
      children: /* @__PURE__ */ jsxRuntimeExports.jsx(TabsList, {
        children: (_e = propertyInput.options) == null ? void 0 : _e.map((option) => /* @__PURE__ */ jsxRuntimeExports.jsx(TabsTrigger, {
          value: option.value,
          className: "flex-1",
          children: option.label
        }, option.value))
      })
    })
  });
};
export {
  EditCoupon as default
};
//# sourceMappingURL=sellerSetting.coupon._couponId-BRGKMaIJ.js.map
