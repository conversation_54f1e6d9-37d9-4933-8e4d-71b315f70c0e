import { r as reactExports, j as jsxRuntimeExports } from "./jsx-runtime-bB2y7OuD.js";
import { C as Card, b as <PERSON><PERSON>eader, c as <PERSON><PERSON><PERSON><PERSON>, a as CardContent } from "./card-BJQMSLe_.js";
import { B as Button } from "./button-ByAXMyvk.js";
import { I as Input } from "./input-3v87qohQ.js";
import { L as Label } from "./label-cSASrwzW.js";
import { u as useLoaderData, d as useActionData, c as useSubmit, F as Form } from "./components-D7UvGag_.js";
import { c as useLocation, u as useNavigation } from "./index-DhHTcibu.js";
import { A as ArrowLeft } from "./arrow-left-D_Ztdrp5.js";
import "./utils-GkgzjW3c.js";
import "./index-z_byfFrQ.js";
import "./index-ImHKLo0a.js";
import "./index-CzZ6EYkN.js";
import "./index-Vp2vNLNM.js";
import "./createLucideIcon-uwkRm45G.js";
function Login() {
  const loader2 = useLoaderData();
  const [step, setStep] = reactExports.useState("phone");
  const [phoneNumber, setPhoneNumber] = reactExports.useState("");
  const [otp, setOtp] = reactExports.useState("");
  const [delay, setDelay] = reactExports.useState(59);
  const [isMnet, setMnet] = reactExports.useState(false);
  const location = useLocation();
  const searchParams = new URLSearchParams(location.search);
  const returnTo = searchParams.get("returnTo");
  const actionData = useActionData();
  const navigation = useNavigation();
  const submit = useSubmit();
  reactExports.useEffect(() => {
    const accessToken = localStorage.getItem("access_token");
    const refreshToken = localStorage.getItem("refresh_token");
    if (accessToken && refreshToken) {
      submit({
        intent: "verifyOtp",
        phoneNumber,
        otp,
        access_token: accessToken,
        refresh_token: refreshToken,
        returnTo
      }, {
        method: "post"
      });
      localStorage.removeItem("access_token");
      localStorage.removeItem("refresh_token");
    }
  }, []);
  reactExports.useEffect(() => {
    console.log("domain...", loader2 == null ? void 0 : loader2.domain);
    if ((loader2 == null ? void 0 : loader2.domain) && (loader2 == null ? void 0 : loader2.domain) === "farmersmandi.in") {
      setMnet(true);
    }
  }, [loader2 == null ? void 0 : loader2.domain]);
  reactExports.useEffect(() => {
    let timer;
    if (step === "otp" && delay > 0) {
      timer = setTimeout(() => setDelay(delay - 1), 1e3);
    }
    console.log("zazazaza Login useEffect 2: ", step, delay);
    return () => clearTimeout(timer);
  }, [step, delay]);
  const handleResendOtp = () => {
    setOtp("");
    setDelay(59);
    setStep("otp");
  };
  reactExports.useEffect(() => {
    if ((actionData == null ? void 0 : actionData.success) && actionData.intent === "getOtp") {
      setStep("otp");
      setDelay(59);
      console.log("zazazaza Login useEffect 1: ", actionData);
    }
  }, [actionData]);
  const handleBack = () => {
    setStep("phone");
    setOtp("");
    submit({
      intent: "clearActionData"
    }, {
      method: "post",
      replace: true
    });
  };
  return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
    className: "min-h-screen flex items-center justify-center bg-background p-4 relative overflow-hidden",
    children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
      className: "absolute inset-0 opacity-10",
      children: [/* @__PURE__ */ jsxRuntimeExports.jsx("div", {
        className: "absolute inset-0 bg-gradient-to-b from-transparent via-background to-background z-10"
      }), /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
        className: "grid grid-cols-12 gap-4 h-full w-full",
        children: [...Array(144)].map((_, i) => /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
          className: "border-[0.5px] border-fm-green/20 h-full"
        }, i))
      })]
    }), /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
      className: "w-full max-w-6xl relative z-20",
      children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
        className: `grid ${isMnet ? "md:grid-cols-2 gap-8" : ""} min-h-[80vh]`,
        children: [isMnet && /* @__PURE__ */ jsxRuntimeExports.jsxs(Card, {
          className: "bg-gradient-to-br from-fm-green/5 to-transparent backdrop-blur-sm border-fm-green/20 flex flex-col",
          children: [/* @__PURE__ */ jsxRuntimeExports.jsxs(CardHeader, {
            className: "flex-none",
            children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
              className: "flex items-center gap-4 mb-6",
              children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                children: [/* @__PURE__ */ jsxRuntimeExports.jsx("div", {
                  className: "text-xl font-semibold text-fm-green",
                  children: "farmersMandi"
                }), /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
                  className: "text-xs text-gray-500",
                  children: "by Brih Solutions"
                })]
              }), /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
                className: "h-8 w-[1px] bg-gray-300"
              }), /* @__PURE__ */ jsxRuntimeExports.jsx("img", {
                src: "/mnet-logo.svg",
                alt: "mNet Logo",
                className: "h-8"
              })]
            }), /* @__PURE__ */ jsxRuntimeExports.jsx(CardTitle, {
              className: "text-2xl font-medium mb-8",
              children: "Helping businesses launch their own e-commerce app"
            })]
          }), /* @__PURE__ */ jsxRuntimeExports.jsxs(CardContent, {
            className: "flex-grow flex flex-col justify-between",
            children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("ul", {
              className: "space-y-6",
              children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("li", {
                className: "flex items-center gap-4",
                children: [/* @__PURE__ */ jsxRuntimeExports.jsx("div", {
                  className: "bg-fm-green/10 p-3 rounded-full",
                  children: /* @__PURE__ */ jsxRuntimeExports.jsx("svg", {
                    className: "w-6 h-6 text-fm-green",
                    fill: "none",
                    strokeLinecap: "round",
                    strokeLinejoin: "round",
                    strokeWidth: "2",
                    viewBox: "0 0 24 24",
                    stroke: "currentColor",
                    children: /* @__PURE__ */ jsxRuntimeExports.jsx("path", {
                      d: "M5 13l4 4L19 7"
                    })
                  })
                }), /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                  className: "text-base",
                  children: "Launch your own app within a week"
                })]
              }), /* @__PURE__ */ jsxRuntimeExports.jsxs("li", {
                className: "flex items-center gap-4",
                children: [/* @__PURE__ */ jsxRuntimeExports.jsx("div", {
                  className: "bg-fm-green/10 p-3 rounded-full",
                  children: /* @__PURE__ */ jsxRuntimeExports.jsx("svg", {
                    className: "w-6 h-6 text-fm-green",
                    fill: "none",
                    strokeLinecap: "round",
                    strokeLinejoin: "round",
                    strokeWidth: "2",
                    viewBox: "0 0 24 24",
                    stroke: "currentColor",
                    children: /* @__PURE__ */ jsxRuntimeExports.jsx("path", {
                      d: "M5 13l4 4L19 7"
                    })
                  })
                }), /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                  className: "text-base",
                  children: "Go online and increase your sales"
                })]
              }), /* @__PURE__ */ jsxRuntimeExports.jsxs("li", {
                className: "flex items-center gap-4",
                children: [/* @__PURE__ */ jsxRuntimeExports.jsx("div", {
                  className: "bg-fm-green/10 p-3 rounded-full",
                  children: /* @__PURE__ */ jsxRuntimeExports.jsx("svg", {
                    className: "w-6 h-6 text-fm-green",
                    fill: "none",
                    strokeLinecap: "round",
                    strokeLinejoin: "round",
                    strokeWidth: "2",
                    viewBox: "0 0 24 24",
                    stroke: "currentColor",
                    children: /* @__PURE__ */ jsxRuntimeExports.jsx("path", {
                      d: "M5 13l4 4L19 7"
                    })
                  })
                }), /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                  className: "text-base",
                  children: "Build your brand and gain trust"
                })]
              }), /* @__PURE__ */ jsxRuntimeExports.jsxs("li", {
                className: "flex items-center gap-4",
                children: [/* @__PURE__ */ jsxRuntimeExports.jsx("div", {
                  className: "bg-fm-green/10 p-3 rounded-full",
                  children: /* @__PURE__ */ jsxRuntimeExports.jsx("svg", {
                    className: "w-6 h-6 text-fm-green",
                    fill: "none",
                    strokeLinecap: "round",
                    strokeLinejoin: "round",
                    strokeWidth: "2",
                    viewBox: "0 0 24 24",
                    stroke: "currentColor",
                    children: /* @__PURE__ */ jsxRuntimeExports.jsx("path", {
                      d: "M5 13l4 4L19 7"
                    })
                  })
                }), /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                  className: "text-base",
                  children: "Streamline customer communications"
                })]
              })]
            }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
              className: "pt-8 border-t border-gray-200 mt-12",
              children: [/* @__PURE__ */ jsxRuntimeExports.jsx("h3", {
                className: "text-base font-medium mb-6",
                children: "Trusted by businesses across India"
              }), /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
                className: "grid grid-cols-4 gap-6",
                children: [...Array(8)].map((_, index) => /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
                  className: "bg-white p-4 rounded-full w-24 h-24 flex items-center justify-center shadow-sm hover:shadow-md transition-shadow",
                  children: /* @__PURE__ */ jsxRuntimeExports.jsx("img", {
                    src: `/customer${index + 1}.png?height=60&width=60`,
                    alt: `Customer logo ${index + 1}`,
                    className: "w-16 h-16 object-contain"
                  })
                }, index))
              })]
            })]
          })]
        }), /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
          className: "flex items-center justify-center",
          children: /* @__PURE__ */ jsxRuntimeExports.jsxs(Card, {
            className: "backdrop-blur-sm border-fm-green/20 w-full max-w-md shadow-lg",
            children: [/* @__PURE__ */ jsxRuntimeExports.jsxs(CardHeader, {
              className: "text-center pb-4",
              children: [/* @__PURE__ */ jsxRuntimeExports.jsx(CardTitle, {
                className: "text-xl font-bold",
                children: "Login to mNet Business Console"
              }), isMnet && /* @__PURE__ */ jsxRuntimeExports.jsx("p", {
                className: "text-sm text-gray-500 mt-1",
                children: "Your fruit & vegetable Mandi online"
              })]
            }), /* @__PURE__ */ jsxRuntimeExports.jsxs(CardContent, {
              children: [/* @__PURE__ */ jsxRuntimeExports.jsxs(Form, {
                method: "post",
                className: "space-y-6",
                children: [step === "phone" ? /* @__PURE__ */ jsxRuntimeExports.jsxs(jsxRuntimeExports.Fragment, {
                  children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                    className: "space-y-2",
                    children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Label, {
                      htmlFor: "phoneNumber",
                      children: "Phone Number"
                    }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                      className: "flex rounded-md shadow-sm",
                      children: [/* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                        className: "inline-flex items-center rounded-l-md border border-r-0 border-input bg-muted px-3 text-sm text-muted-foreground",
                        children: "+91"
                      }), /* @__PURE__ */ jsxRuntimeExports.jsx(Input, {
                        type: "tel",
                        id: "phoneNumber",
                        name: "phoneNumber",
                        value: phoneNumber,
                        onChange: (e) => setPhoneNumber(e.target.value),
                        maxLength: 10,
                        required: true,
                        className: "rounded-l-none"
                      })]
                    }), /* @__PURE__ */ jsxRuntimeExports.jsx("p", {
                      className: "text-sm text-muted-foreground",
                      children: "We'll send you an OTP on this number"
                    })]
                  }), /* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
                    type: "submit",
                    name: "intent",
                    value: "getOtp",
                    disabled: phoneNumber.length < 10 || navigation.state === "submitting",
                    className: "w-full bg-fm-green hover:bg-fm-hover",
                    children: navigation.state === "submitting" ? "Sending..." : "GET OTP"
                  })]
                }) : /* @__PURE__ */ jsxRuntimeExports.jsxs(jsxRuntimeExports.Fragment, {
                  children: [/* @__PURE__ */ jsxRuntimeExports.jsx("input", {
                    type: "hidden",
                    name: "phoneNumber",
                    value: phoneNumber
                  }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                    className: "space-y-2",
                    children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                      className: "flex items-center",
                      children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
                        type: "button",
                        onClick: handleBack,
                        variant: "ghost",
                        size: "sm",
                        className: "mr-2",
                        children: /* @__PURE__ */ jsxRuntimeExports.jsx(ArrowLeft, {
                          size: 16
                        })
                      }), /* @__PURE__ */ jsxRuntimeExports.jsx(Label, {
                        htmlFor: "otp",
                        children: "Enter OTP"
                      })]
                    }), /* @__PURE__ */ jsxRuntimeExports.jsx(Input, {
                      type: "number",
                      id: "otp",
                      name: "otp",
                      value: otp,
                      onChange: (e) => {
                        const value = e.target.value.replace(/\D/g, "");
                        if (value.length <= 6) {
                          setOtp(e.target.value);
                        }
                      },
                      maxLength: 6,
                      required: true
                    }), /* @__PURE__ */ jsxRuntimeExports.jsxs("p", {
                      className: "text-sm text-muted-foreground",
                      children: ["Didn't receive the OTP?", " ", /* @__PURE__ */ jsxRuntimeExports.jsxs(Button, {
                        type: "button",
                        onClick: handleResendOtp,
                        disabled: delay > 0 || navigation.state === "submitting",
                        variant: "link",
                        className: "p-0 h-auto font-normal text-fm-green",
                        children: ["Resend OTP ", delay > 0 && `in ${delay}s`]
                      })]
                    })]
                  }), /* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
                    type: "submit",
                    name: "intent",
                    value: "verifyOtp",
                    disabled: otp.length < 6 || navigation.state === "submitting",
                    className: "w-full bg-fm-green hover:bg-fm-hover",
                    children: navigation.state === "submitting" ? "Verifying..." : "CONTINUE"
                  })]
                }), returnTo && /* @__PURE__ */ jsxRuntimeExports.jsx("input", {
                  type: "hidden",
                  name: "returnTo",
                  value: returnTo
                })]
              }), (actionData == null ? void 0 : actionData.message) && actionData.intent !== "clearActionData" && /* @__PURE__ */ jsxRuntimeExports.jsx("p", {
                className: `mt-4 text-sm ${actionData.success ? "text-green-600" : "text-destructive"}`,
                children: actionData.message
              })]
            })]
          })
        })]
      })
    })]
  });
}
export {
  Login as default
};
//# sourceMappingURL=login-Bz9xXZlj.js.map
