{"version": 3, "file": "addMasterCategory-TRqpdyp0.js", "sources": ["../../../app/components/masterItems/searchableCategories.tsx", "../../../app/components/ui/addMasterCategory.tsx"], "sourcesContent": ["import * as React from \"react\";\r\nimport { Button } from \"../ui/button\";\r\nimport { Input } from \"../ui/input\";\r\nimport { Label } from \"../ui/label\";\r\nimport { useFetcher } from \"@remix-run/react\";\r\n\r\ninterface Category {\r\n      id: number;\r\n      name: string;\r\n      picture?: string;\r\n      picturex?: string;\r\n      picturexx?: string;\r\n      level?: number;\r\n      totalItems?: number;\r\n      parentCategories?: Array<{\r\n            id: number;\r\n            name: string;\r\n            picture?: string;\r\n            picturex?: string;\r\n            picturexx?: string;\r\n            level?: number;\r\n            parentCategories?: number[];\r\n            totalItems?: number;\r\n            myItems?: number;\r\n      }>;\r\n}\r\n\r\nexport interface CategoryItem {\r\n      value: string;\r\n      label: string;\r\n      numericId: number;\r\n}\r\n\r\ninterface SearchableCategoriesProps {\r\n      label?: string;\r\n      apiUrl: string;\r\n      selectedCategories: CategoryItem[];\r\n      onCategoryAdd: (categoryId: number, categoryName: string) => void;\r\n      onCategoryRemove: (categoryId: number) => void;\r\n      required?: boolean;\r\n      error?: string;\r\n      level?: number\r\n}\r\n\r\nexport function SearchableCategories({\r\n      label = \"Categories\",\r\n      apiUrl,\r\n      selectedCategories,\r\n      onCategoryAdd,\r\n      onCategoryRemove,\r\n      required = false,\r\n      error,\r\n      level\r\n}: SearchableCategoriesProps) {\r\n      // State for search input\r\n      const [categoryInput, setCategoryInput] = React.useState(\"\");\r\n\r\n      // Category state\r\n      const [categoryItems, setCategoryItems] = React.useState<CategoryItem[]>([]);\r\n      const [loadingCategories, setLoadingCategories] = React.useState(false);\r\n      const [categoryPage, setCategoryPage] = React.useState(1);\r\n      const [hasMoreCategories, setHasMoreCategories] = React.useState(true);\r\n      const fetcher = useFetcher<{ categories: Category[]; categoryPage: number }>();\r\n\r\n      // Search categories with server API\r\n      const searchCategories = async (query: string) => {\r\n            setLoadingCategories(true);\r\n            fetcher.load(`${apiUrl}?searchCategories=${query}&categoryPage=0&level=${level}`);\r\n      };\r\n\r\n      // Load more categories for infinite scroll\r\n      const loadMoreCategories = async () => {\r\n            if (!hasMoreCategories || loadingCategories) return;\r\n            const nextPage = categoryPage + 1;\r\n            fetcher.load(`${apiUrl}?searchCategories=${categoryInput}&categoryPage=${nextPage}&level=${level}`);\r\n      };\r\n\r\n      // Handle fetcher data updates\r\n      React.useEffect(() => {\r\n            if (fetcher.data?.categories) {\r\n                  const items: CategoryItem[] = fetcher.data.categories.map(category => ({\r\n                        value: String(category.id),\r\n                        label: category.name,\r\n                        numericId: category.id\r\n                  }));\r\n\r\n                  if (fetcher.data.categoryPage === 0) {\r\n                        setCategoryItems(items);\r\n                  } else {\r\n                        setCategoryItems(prev => [...prev, ...items]);\r\n                  }\r\n\r\n                  setCategoryPage(fetcher.data.categoryPage);\r\n                  setHasMoreCategories(items.length === 20);\r\n                  setLoadingCategories(false);\r\n            }\r\n      }, [fetcher.data]);\r\n\r\n      // Handle category functions\r\n      const handleAddCategory = (categoryValue: string) => {\r\n            const categoryItem = categoryItems.find(item => item.value === categoryValue);\r\n            if (!categoryItem) return;\r\n\r\n            const categoryId = categoryItem.numericId;\r\n            if (!selectedCategories.some(cat => cat.numericId === categoryId)) {\r\n                  onCategoryAdd(categoryId, categoryItem.label);\r\n            }\r\n            setCategoryInput(\"\");\r\n      };\r\n\r\n      // Get category name by ID\r\n      const getCategoryName = (categoryId: number) => {\r\n            // First check in the current categoryItems (new categories)\r\n            const newCategory = categoryItems.find(item => item.numericId === categoryId);\r\n            if (newCategory) return newCategory.label;\r\n\r\n            // Then check in the selectedCategories\r\n            const selectedCategory = selectedCategories.find(item => item.numericId === categoryId);\r\n            if (selectedCategory) return selectedCategory.label;\r\n\r\n            // If no name found, return the ID\r\n            return String(categoryId);\r\n      };\r\n\r\n      return (\r\n            <div className=\"w-full\">\r\n                  <Label>{label} {required && \"(Required)\"}</Label>\r\n                  <div className=\"flex gap-2 items-center\">\r\n                        <Input\r\n                              placeholder=\"Search categories\"\r\n                              value={categoryInput}\r\n                              onChange={(e) => {\r\n                                    setCategoryInput(e.target.value);\r\n                                    searchCategories(e.target.value);\r\n                              }}\r\n                        />\r\n\r\n                  </div>\r\n                  {required && selectedCategories.length < 1 && (\r\n                        <p className=\"text-red-500\">At least one category is required.</p>\r\n                  )}\r\n                  <div className=\"flex gap-2 mt-2 flex-wrap\">\r\n                        {selectedCategories.map((category) => {\r\n                              const categoryName = getCategoryName(category.numericId);\r\n                              return (\r\n                                    <div key={category.numericId} className=\"inline-flex items-center space-x-1 bg-gray-200 p-1 rounded\">\r\n                                          <span>{categoryName}</span>\r\n                                          <button\r\n                                                onClick={() => onCategoryRemove(category.numericId)}\r\n                                                className=\"hover:text-red-500\"\r\n                                          >\r\n                                                ×\r\n                                          </button>\r\n                                    </div>\r\n                              );\r\n                        })}\r\n                  </div>\r\n                  {error && (\r\n                        <p className=\"text-red-500\">{error}</p>\r\n                  )}\r\n                  {loadingCategories && <p>Loading categories...</p>}\r\n                  {categoryItems.length > 0 && categoryInput && (\r\n                        <div className=\"mt-2 border rounded-md p-2\">\r\n                              {categoryItems.map(category => {\r\n                                    // Don't show already assigned categories\r\n                                    if (selectedCategories.some(cat => cat.numericId === category.numericId)) {\r\n                                          return null;\r\n                                    }\r\n                                    return (\r\n                                          <button\r\n                                                key={category.value}\r\n                                                className=\"w-full text-left cursor-pointer hover:bg-gray-100 p-1\"\r\n                                                onClick={() => handleAddCategory(category.value)}\r\n                                                onKeyDown={(e) => {\r\n                                                      if (e.key === 'Enter' || e.key === ' ') {\r\n                                                            handleAddCategory(category.value);\r\n                                                      }\r\n                                                }}\r\n                                          >\r\n                                                {category.label}\r\n                                          </button>\r\n                                    );\r\n                              })}\r\n                              {hasMoreCategories && (\r\n                                    <Button\r\n                                          type=\"button\"\r\n                                          variant=\"ghost\"\r\n                                          className=\"w-full mt-2\"\r\n                                          onClick={loadMoreCategories}\r\n                                    >\r\n                                          Load More\r\n                                    </Button>\r\n                              )}\r\n                        </div>\r\n                  )}\r\n            </div>\r\n      );\r\n} ", "import { Check, CirclePlus } from \"lucide-react\";\r\nimport { Input } from \"./input\";\r\nimport { Label } from \"./label\";\r\nimport { Dialog, DialogContent, DialogTrigger } from \"./dialog\";\r\nimport { But<PERSON> } from \"./button\";\r\nimport { useState, useRef, useEffect } from \"react\";\r\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from \"./select\";\r\nimport { useFetcher } from \"@remix-run/react\";\r\nimport { MasterItemCategories } from \"~/types/api/businessConsoleService/MasterItemCategory\";\r\nimport { CategoryItem, SearchableCategories } from \"../masterItems/searchableCategories\";\r\nimport { RadioGroup, RadioGroupItem } from \"./radio-group\";\r\n\r\ninterface AddMasterCategoryProps {\r\n      categoryDetails?: MasterItemCategories;\r\n      buttonName: string;\r\n      handleSubmit: (ondcDomain: \"RET10\" | \"RET11\", level: number, name: string, sequence: number, picturUrl: string, ParentCategoryIds: CategoryItem[], closeDialog: () => void, mode?: string, icId?: number) => void;\r\n      mode?: string\r\n}\r\nexport default function AddMasterCategory({\r\n      categoryDetails,\r\n      buttonName,\r\n      handleSubmit,\r\n      mode\r\n}: AddMasterCategoryProps) {\r\n      const [ctnBtnClicked, setCtnBtnClicked] = useState(false);\r\n      const [categoryName, setCategoryName] = useState<string | undefined>(mode === \"Edit\" ? categoryDetails?.name : \"\");\r\n\r\n      const [uploadingImage, setUploadingImage] = useState(false);\r\n      const [uploadError, setUploadError] = useState<string | null>(null);\r\n      const [selectedFile, setSelectedFile] = useState<File | null>(null);\r\n      const [filePreviewUrl, setFilePreviewUrl] = useState<string | null>(null); // Preview URL\r\n      const [uploadedImageUrl, setUploadedImageUrl] = useState<string>(\r\n            mode === \"Edit\" && categoryDetails?.picture ? categoryDetails.picture.toString() : \"\"\r\n      ); const fileInputRef = useRef<HTMLInputElement>(null);\r\n      const uploadFetcher = useFetcher<{ success: boolean; fileUrl: string; error?: string }>();\r\n      const [selectedLevel, setSelectedLevel] = useState<string | undefined>(mode === \"Edit\" ? categoryDetails?.level.toString() : \"\");\r\n      const [sequence, setSequence] = useState<string | undefined>(\r\n            mode === \"Edit\" && categoryDetails?.sequence !== undefined\r\n                  ? categoryDetails.sequence.toString()\r\n                  : \"\"\r\n      );\r\n      const [ondcDomain, setOndcDomain] = useState<\"RET10\" | \"RET11\">(\r\n            mode === \"Edit\" && categoryDetails?.ondcDomain ? categoryDetails.ondcDomain as \"RET10\" | \"RET11\" : \"RET10\"\r\n      );\r\n\r\n      const [images, setImages] = useState<any[]>([]);\r\n      const [dialogOpen, setDialogOpen] = useState(false);\r\n\r\n      const transformToCategoryItem = (parentCategories: MasterItemCategories[]): CategoryItem[] => {\r\n            return parentCategories.map((category) => ({\r\n                  value: category.id.toString(),\r\n                  label: category.name,\r\n                  numericId: category.id,\r\n            }));\r\n      };\r\n      const [updatedCategory, setUpdatedCategory] = useState<CategoryItem[]>(() =>\r\n            mode === \"Edit\" && categoryDetails?.parentCategories ? transformToCategoryItem(categoryDetails?.parentCategories) : []\r\n      );\r\n      const resetForm = () => {\r\n            if (mode !== \"Edit\") {\r\n                  setCategoryName(\"\");\r\n                  setSelectedLevel(\"\");\r\n                  setUpdatedCategory([]);\r\n                  setSelectedFile(null);\r\n                  setFilePreviewUrl(null);\r\n                  setUploadedImageUrl(\"\");\r\n                  setUploadingImage(false);\r\n                  setUploadError(null);\r\n                  setCtnBtnClicked(false);\r\n            }\r\n\r\n\r\n      };\r\n\r\n      const levels = [\r\n            { id: \"1\", name: \"1\" },\r\n            { id: \"2\", name: \"2\" },\r\n            { id: \"3\", name: \"3\" },\r\n      ];\r\n      const handleSelectedLevel = (value: string) => {\r\n            setSelectedLevel(value);\r\n\r\n      };\r\n\r\n      const isContinueDisabled = !categoryName || !selectedLevel;\r\n      const isSubmitDisabled = () => {\r\n            if (ondcDomain === \"RET10\") {\r\n                  return !uploadedImageUrl || !categoryName || !selectedLevel;\r\n            } else {\r\n                  return !categoryName || !selectedLevel;\r\n            }\r\n      }\r\n      const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {\r\n            const file = event.target.files?.[0];\r\n            if (!file) return;\r\n\r\n            // Reset error state\r\n            setUploadError(null);\r\n\r\n            // Validate file size (e.g., 5MB limit)\r\n            const MAX_FILE_SIZE = 500 * 1024 * 1024; // 5MB\r\n            if (file.size > MAX_FILE_SIZE) {\r\n                  setUploadError(\"File size exceeds 5MB limit\");\r\n                  return;\r\n            }\r\n\r\n            const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];\r\n            if (!allowedTypes.includes(file.type)) {\r\n                  setUploadError(\"Only JPEG, PNG, GIF, and WEBP images are allowed\");\r\n                  return;\r\n            }\r\n\r\n            setSelectedFile(file);\r\n            const reader = new FileReader();\r\n            reader.onloadend = () => {\r\n                  setFilePreviewUrl(reader.result as string); // Set the preview image URL\r\n            };\r\n            reader.readAsDataURL(file); // Read the file to generate a URL for the preview\r\n      };\r\n\r\n      const handleUpload = () => {\r\n            if (!selectedFile) return;\r\n\r\n            setUploadingImage(true);\r\n            const formData = new FormData();\r\n            formData.append(\"_action\", \"uploadImage\");\r\n            formData.append(\"file\", selectedFile, selectedFile.name);\r\n\r\n            uploadFetcher.submit(formData, {\r\n                  method: \"post\",\r\n                  encType: \"multipart/form-data\",\r\n            });\r\n      };\r\n\r\n      const openFilePicker = () => {\r\n            fileInputRef.current?.click();\r\n      };\r\n      useEffect(() => {\r\n            if (uploadFetcher.data) {\r\n                  if (uploadFetcher.data.error) {\r\n                        setUploadError(uploadFetcher.data.error);\r\n                        setUploadingImage(false);\r\n                  } else if (uploadFetcher.data.fileUrl) {\r\n\r\n                        setUploadedImageUrl(uploadFetcher.data.fileUrl);\r\n\r\n                        const newImage = {\r\n                              id: Date.now(),\r\n                              url: uploadFetcher.data.fileUrl,\r\n                              sequence: images.length + 1,\r\n                              isDefault: false,\r\n                        };\r\n\r\n                        setImages((prevImages) => [...prevImages, newImage]);\r\n                        setUploadingImage(false);\r\n                        setUploadError(null);\r\n                        setSelectedFile(null);\r\n                        setFilePreviewUrl(null);\r\n                        if (fileInputRef.current) {\r\n                              fileInputRef.current.value = ''; // Reset file input\r\n                        }\r\n                  }\r\n            }\r\n      }, [uploadFetcher.data]);\r\n\r\n      const closeDialog = () => {\r\n            if (mode !== \"Edit\") {\r\n                  setCategoryName(\"\");\r\n                  setSelectedLevel(\"\");\r\n                  setSelectedFile(null);\r\n                  setUpdatedCategory([])\r\n                  setFilePreviewUrl(null);\r\n                  setUploadedImageUrl(\"\");\r\n                  setUploadingImage(false);\r\n                  setUploadError(null);\r\n                  setCtnBtnClicked(false);\r\n                  setDialogOpen(false);\r\n            }\r\n            setDialogOpen(false);\r\n\r\n      }\r\n      const onHandleSubmit = () => {\r\n            if (!selectedLevel || !categoryName) {\r\n                  console.error(\"Required fields are missing.\");\r\n                  return;\r\n            }\r\n\r\n            handleSubmit(\r\n                  ondcDomain,\r\n                  parseInt(selectedLevel),\r\n                  categoryName,\r\n                  parseInt(sequence ?? \"0\"),\r\n                  uploadedImageUrl,\r\n                  updatedCategory,\r\n                  closeDialog,\r\n                  mode,\r\n                  categoryDetails?.id\r\n            );\r\n      };\r\n\r\n      const handleOndcDomainChange = (val: \"RET10\" | \"RET11\") => {\r\n            setOndcDomain(val);\r\n            if (val === \"RET11\") {\r\n                  setSelectedLevel(\"1\");\r\n                  setUpdatedCategory([]);\r\n            } else {\r\n                  setSelectedLevel(mode === \"Edit\" ? categoryDetails?.level.toString() : \"\");\r\n                  setUpdatedCategory(() =>\r\n                        mode === \"Edit\" && categoryDetails?.parentCategories ? transformToCategoryItem(categoryDetails?.parentCategories) : []\r\n                  );\r\n            }\r\n      };\r\n\r\n      return (\r\n            <div className=\"container mx-auto   \">\r\n                  <Dialog open={dialogOpen} onOpenChange={(isOpen) => {\r\n                        setDialogOpen(isOpen);\r\n                        if (!isOpen) {\r\n                              resetForm();\r\n                        }\r\n                  }}>\r\n                        {mode === \"Edit\" ? <DialogTrigger asChild className=\"flex\">\r\n                              <Button\r\n                                    size=\"sm\"\r\n                                    variant=\"secondary\"\r\n\r\n                              >\r\n                                    Edit\r\n                              </Button>\r\n\r\n                        </DialogTrigger> : <DialogTrigger asChild className=\"flex\">\r\n                              <Button className=\"fixed bottom-5 right-5 rounded-full\">+ {buttonName}</Button>\r\n\r\n                        </DialogTrigger>}\r\n                        <DialogContent\r\n                              className=\"w-full sm:w-[400px] md:w-[600px] max-h-[90vh] overflow-y-auto absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2\"\r\n                        >\r\n                              <div className=\"grid gap-4\">\r\n                                    <div className=\"space-y-2\">\r\n                                          {mode === \"Edit\" ? <h4 className=\"font-medium leading-none text-center md:text-left\">\r\n                                                Edit Category\r\n                                          </h4> : <h4 className=\"font-medium leading-none text-center md:text-left\">\r\n                                                Add Category\r\n                                          </h4>}\r\n                                    </div>\r\n                                    <div className=\"grid gap-4\">\r\n                                          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n                                                <div className=\"space-y-2 md:col-span-2\">\r\n                                                      <Label htmlFor=\"ondcDomain\">Business Type</Label>\r\n                                                      <RadioGroup\r\n                                                            value={ondcDomain}\r\n                                                            onValueChange={(val: \"RET10\" | \"RET11\") => handleOndcDomainChange(val)}\r\n                                                            className=\"grid grid-cols-3 gap-4 mt-1\"\r\n                                                      // disabled={mode === \"Edit\"}\r\n                                                      >\r\n                                                            <div className=\"flex items-center space-x-2\">\r\n                                                                  <RadioGroupItem id=\"type-RET11\" value=\"RET11\" />\r\n                                                                  <Label htmlFor=\"type-RET11\">Restaurant</Label>\r\n                                                            </div>\r\n                                                            <div className=\"flex items-center space-x-2\">\r\n                                                                  <RadioGroupItem id=\"type-RET10\" value=\"RET10\" />\r\n                                                                  <Label htmlFor=\"type-RET10\">Non-Restaurant</Label>\r\n                                                            </div>\r\n                                                      </RadioGroup>\r\n                                                </div>\r\n                                                <div className=\"space-y-2\">\r\n                                                      <Label htmlFor=\"categoryName\">Category Name (Required)</Label>\r\n                                                      <Input\r\n                                                            id=\"categoryName\"\r\n                                                            placeholder=\"Enter category name\"\r\n                                                            className=\"w-full h-8\"\r\n                                                            required\r\n                                                            value={categoryName}\r\n                                                            onChange={(e) => setCategoryName(e.target.value)}\r\n                                                            disabled={mode === \"Edit\"} />\r\n                                                      <p className=\"text-sm text-slate-400 \">Enter the English Name</p>\r\n                                                </div>\r\n                                                {ondcDomain === \"RET10\" && <div className=\"space-y-2\">\r\n                                                      <Label htmlFor=\"categoryDetails\">Category Details (Required)</Label>\r\n                                                      <Select\r\n                                                            value={selectedLevel}\r\n                                                            onValueChange={handleSelectedLevel}\r\n                                                            disabled={mode === \"Edit\"}\r\n                                                      >\r\n                                                            <SelectTrigger>\r\n                                                                  <SelectValue placeholder=\"Select a Level\" />\r\n                                                            </SelectTrigger>\r\n                                                            <SelectContent>\r\n                                                                  {levels.map((x) => (\r\n                                                                        <SelectItem value={x.id} key={x.id}>\r\n                                                                              {x.name}\r\n                                                                        </SelectItem>\r\n                                                                  ))}\r\n                                                            </SelectContent>\r\n                                                      </Select>\r\n                                                </div>}\r\n                                                {ctnBtnClicked && (\r\n                                                      <>\r\n                                                            <div className=\"col-span-full\">\r\n                                                                  {selectedLevel !== \"3\" && ondcDomain === \"RET10\" && <SearchableCategories\r\n                                                                        label=\"Parent Categories\"\r\n                                                                        apiUrl=\"/home/<USER>\"\r\n\r\n                                                                        selectedCategories={updatedCategory || []}\r\n                                                                        onCategoryAdd={(categoryId, categoryName) => {\r\n\r\n                                                                              setUpdatedCategory((prevUpdatedCategory) => [\r\n                                                                                    ...(prevUpdatedCategory || []),\r\n                                                                                    { numericId: categoryId, value: categoryName, label: \"\" }\r\n                                                                              ]);\r\n                                                                        }}\r\n                                                                        onCategoryRemove={(categoryId) => {\r\n\r\n                                                                              setUpdatedCategory((prevUpdatedCategory) =>\r\n                                                                                    (prevUpdatedCategory || []).filter(\r\n                                                                                          (cat) => cat.numericId !== categoryId\r\n                                                                                    )\r\n                                                                              );\r\n                                                                        }}\r\n                                                                        required={true}\r\n                                                                        level={(Number(selectedLevel) || 0) + 1}\r\n                                                                  />}\r\n                                                            </div>\r\n                                                            <div className=\"col-span-full\">\r\n                                                                  <Label>Category Image {ondcDomain === \"RET10\" ? \"(Required)\" : \"(Optional)\"}</Label>\r\n                                                                  <div className=\"flex gap-2 items-center\">\r\n                                                                        <Input\r\n                                                                              type=\"text\"\r\n                                                                              readOnly\r\n                                                                              value={selectedFile?.name || \"No file selected\"}\r\n                                                                              className=\"flex-grow\"\r\n                                                                              onClick={openFilePicker}\r\n                                                                              style={{ cursor: 'pointer' }}\r\n                                                                        />\r\n                                                                        <input\r\n                                                                              ref={fileInputRef}\r\n                                                                              type=\"file\"\r\n                                                                              accept=\"image/*\"\r\n                                                                              onChange={handleFileSelect}\r\n                                                                              className=\"hidden\"\r\n                                                                        />\r\n                                                                        <Button\r\n                                                                              type=\"button\"\r\n                                                                              onClick={selectedFile ? handleUpload : openFilePicker}\r\n                                                                              disabled={uploadingImage}\r\n                                                                        >\r\n                                                                              {uploadingImage ? (\r\n                                                                                    <span className=\"flex items-center gap-2\">\r\n                                                                                          <svg className=\"animate-spin h-4 w-4\" viewBox=\"0 0 24 24\">\r\n                                                                                                <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\" fill=\"none\" />\r\n                                                                                                <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\" />\r\n                                                                                          </svg>\r\n                                                                                          Uploading...\r\n                                                                                    </span>\r\n                                                                              ) : selectedFile ? (\r\n                                                                                    'Upload'\r\n                                                                              ) : (\r\n                                                                                    'Add Image'\r\n                                                                              )}\r\n                                                                        </Button>\r\n                                                                  </div>\r\n                                                                  {uploadError && (\r\n                                                                        <p className=\"text-red-500 mt-1 text-sm\">{uploadError}</p>\r\n                                                                  )}\r\n                                                                  <div className=\"mt-2 space-y-2\">\r\n                                                                        {filePreviewUrl && (\r\n                                                                              <div className=\"flex justify-center\">\r\n                                                                                    <img src={filePreviewUrl} alt=\"Image Preview\" className=\"max-w-full h-20 object-cover rounded\" />\r\n                                                                              </div>\r\n                                                                        )}\r\n                                                                        {uploadedImageUrl && !filePreviewUrl && (\r\n                                                                              <div className=\"flex justify-center\">\r\n                                                                                    <img src={uploadedImageUrl} alt=\"Uploaded Image\" className=\"max-w-full h-20 object-cover rounded\" />\r\n                                                                              </div>\r\n                                                                        )}\r\n                                                                  </div>\r\n                                                            </div>\r\n                                                            {selectedLevel === \"1\" && <div className=\"space-y-2\">\r\n                                                                  <Label htmlFor=\"Sequence\">Sequence (Required)</Label>\r\n                                                                  <Input\r\n                                                                        id=\"sequence\"\r\n                                                                        placeholder=\"Enter Sequence number\"\r\n                                                                        className=\"w-full h-8\"\r\n                                                                        required\r\n                                                                        value={sequence}\r\n                                                                        onChange={(e) => setSequence(e.target.value)}\r\n                                                                        type=\"number\"\r\n                                                                  />\r\n                                                            </div>}\r\n                                                      </>\r\n                                                )}\r\n                                          </div>\r\n                                    </div>\r\n                              </div>\r\n                              <div className=\"flex flex-col md:flex-row justify-end gap-2 mt-5\">\r\n                                    {!ctnBtnClicked && (\r\n                                          <Button\r\n                                                size=\"sm\"\r\n                                                className=\"w-full md:w-auto\"\r\n                                                disabled={isContinueDisabled}\r\n                                                onClick={() => setCtnBtnClicked(true)}\r\n                                          >\r\n                                                Continue\r\n                                          </Button>\r\n                                    )}\r\n                                    {ctnBtnClicked && (\r\n                                          <Button\r\n                                                size=\"sm\"\r\n                                                className=\"w-full md:w-auto\"\r\n                                                onClick={() => onHandleSubmit()}\r\n                                                disabled={isSubmitDisabled()}>\r\n                                                Submit\r\n                                          </Button>\r\n                                    )}\r\n                              </div>\r\n                        </DialogContent>\r\n                  </Dialog>\r\n\r\n            </div>\r\n      );\r\n}\r\n"], "names": ["React.useState", "React.useEffect", "jsxs", "jsx", "useState", "useRef", "useEffect", "Fragment", "categoryName"], "mappings": ";;;;;;;;AA4CO,SAAS,qBAAqB;AAAA,EAC/B,QAAQ;AAAA,EACR;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,WAAW;AAAA,EACX;AAAA,EACA;AACN,GAA8B;AAExB,QAAM,CAAC,eAAe,gBAAgB,IAAIA,aAAAA,SAAe,EAAE;AAG3D,QAAM,CAAC,eAAe,gBAAgB,IAAIA,aAAAA,SAA+B,CAAA,CAAE;AAC3E,QAAM,CAAC,mBAAmB,oBAAoB,IAAIA,aAAAA,SAAe,KAAK;AACtE,QAAM,CAAC,cAAc,eAAe,IAAIA,aAAAA,SAAe,CAAC;AACxD,QAAM,CAAC,mBAAmB,oBAAoB,IAAIA,aAAAA,SAAe,IAAI;AACrE,QAAM,UAAU,WAA6D;AAGvE,QAAA,mBAAmB,OAAO,UAAkB;AAC5C,yBAAqB,IAAI;AACzB,YAAQ,KAAK,GAAG,MAAM,qBAAqB,KAAK,yBAAyB,KAAK,EAAE;AAAA,EACtF;AAGA,QAAM,qBAAqB,YAAY;AAC7B,QAAA,CAAC,qBAAqB,kBAAmB;AAC7C,UAAM,WAAW,eAAe;AACxB,YAAA,KAAK,GAAG,MAAM,qBAAqB,aAAa,iBAAiB,QAAQ,UAAU,KAAK,EAAE;AAAA,EACxG;AAGAC,eAAAA,UAAgB,MAAM;;AACZ,SAAA,aAAQ,SAAR,mBAAc,YAAY;AACxB,YAAM,QAAwB,QAAQ,KAAK,WAAW,IAAI,CAAa,cAAA;AAAA,QACjE,OAAO,OAAO,SAAS,EAAE;AAAA,QACzB,OAAO,SAAS;AAAA,QAChB,WAAW,SAAS;AAAA,MAAA,EACxB;AAEE,UAAA,QAAQ,KAAK,iBAAiB,GAAG;AAC/B,yBAAiB,KAAK;AAAA,MAAA,OACrB;AACD,yBAAiB,UAAQ,CAAC,GAAG,MAAM,GAAG,KAAK,CAAC;AAAA,MAAA;AAGlC,sBAAA,QAAQ,KAAK,YAAY;AACpB,2BAAA,MAAM,WAAW,EAAE;AACxC,2BAAqB,KAAK;AAAA,IAAA;AAAA,EAChC,GACH,CAAC,QAAQ,IAAI,CAAC;AAGX,QAAA,oBAAoB,CAAC,kBAA0B;AAC/C,UAAM,eAAe,cAAc,KAAK,CAAQ,SAAA,KAAK,UAAU,aAAa;AAC5E,QAAI,CAAC,aAAc;AAEnB,UAAM,aAAa,aAAa;AAChC,QAAI,CAAC,mBAAmB,KAAK,SAAO,IAAI,cAAc,UAAU,GAAG;AAC/C,oBAAA,YAAY,aAAa,KAAK;AAAA,IAAA;AAElD,qBAAiB,EAAE;AAAA,EACzB;AAGM,QAAA,kBAAkB,CAAC,eAAuB;AAE1C,UAAM,cAAc,cAAc,KAAK,CAAQ,SAAA,KAAK,cAAc,UAAU;AACxE,QAAA,oBAAoB,YAAY;AAGpC,UAAM,mBAAmB,mBAAmB,KAAK,CAAQ,SAAA,KAAK,cAAc,UAAU;AAClF,QAAA,yBAAyB,iBAAiB;AAG9C,WAAO,OAAO,UAAU;AAAA,EAC9B;AAGM,SAAAC,kCAAA,KAAC,OAAI,EAAA,WAAU,UACT,UAAA;AAAA,IAAAA,uCAAC,OAAO,EAAA,UAAA;AAAA,MAAA;AAAA,MAAM;AAAA,MAAE,YAAY;AAAA,IAAA,GAAa;AAAA,IACzCC,kCAAAA,IAAC,OAAI,EAAA,WAAU,2BACT,UAAAA,kCAAA;AAAA,MAAC;AAAA,MAAA;AAAA,QACK,aAAY;AAAA,QACZ,OAAO;AAAA,QACP,UAAU,CAAC,MAAM;AACM,2BAAA,EAAE,OAAO,KAAK;AACd,2BAAA,EAAE,OAAO,KAAK;AAAA,QAAA;AAAA,MACrC;AAAA,IAAA,GAGZ;AAAA,IACC,YAAY,mBAAmB,SAAS,2CAClC,KAAE,EAAA,WAAU,gBAAe,UAAkC,qCAAA,CAAA;AAAA,0CAEnE,OAAI,EAAA,WAAU,6BACR,UAAmB,mBAAA,IAAI,CAAC,aAAa;AAC1B,YAAA,eAAe,gBAAgB,SAAS,SAAS;AAEjD,aAAAD,kCAAA,KAAC,OAA6B,EAAA,WAAU,8DAClC,UAAA;AAAA,QAAAC,kCAAAA,IAAC,UAAM,UAAa,aAAA,CAAA;AAAA,QACpBA,kCAAA;AAAA,UAAC;AAAA,UAAA;AAAA,YACK,SAAS,MAAM,iBAAiB,SAAS,SAAS;AAAA,YAClD,WAAU;AAAA,YACf,UAAA;AAAA,UAAA;AAAA,QAAA;AAAA,MAED,EAAA,GAPI,SAAS,SAQnB;AAAA,IAEX,CAAA,GACP;AAAA,IACC,SACKA,kCAAA,IAAC,KAAE,EAAA,WAAU,gBAAgB,UAAM,OAAA;AAAA,IAExC,qBAAsBA,kCAAAA,IAAA,KAAA,EAAE,UAAqB,wBAAA,CAAA;AAAA,IAC7C,cAAc,SAAS,KAAK,iBACtBD,kCAAAA,KAAA,OAAA,EAAI,WAAU,8BACR,UAAA;AAAA,MAAA,cAAc,IAAI,CAAY,aAAA;AAEzB,YAAI,mBAAmB,KAAK,CAAA,QAAO,IAAI,cAAc,SAAS,SAAS,GAAG;AAC7D,iBAAA;AAAA,QAAA;AAGP,eAAAC,kCAAA;AAAA,UAAC;AAAA,UAAA;AAAA,YAEK,WAAU;AAAA,YACV,SAAS,MAAM,kBAAkB,SAAS,KAAK;AAAA,YAC/C,WAAW,CAAC,MAAM;AACZ,kBAAI,EAAE,QAAQ,WAAW,EAAE,QAAQ,KAAK;AAClC,kCAAkB,SAAS,KAAK;AAAA,cAAA;AAAA,YAE5C;AAAA,YAEC,UAAS,SAAA;AAAA,UAAA;AAAA,UATL,SAAS;AAAA,QAUpB;AAAA,MAAA,CAEX;AAAA,MACA,qBACKA,kCAAA;AAAA,QAAC;AAAA,QAAA;AAAA,UACK,MAAK;AAAA,UACL,SAAQ;AAAA,UACR,WAAU;AAAA,UACV,SAAS;AAAA,UACd,UAAA;AAAA,QAAA;AAAA,MAAA;AAAA,IAED,EAEZ,CAAA;AAAA,EAAA,GAEZ;AAEZ;ACnLA,SAAwB,kBAAkB;AAAA,EACpC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACN,GAA2B;AACrB,QAAM,CAAC,eAAe,gBAAgB,IAAIC,aAAAA,SAAS,KAAK;AAClD,QAAA,CAAC,cAAc,eAAe,IAAIA,sBAA6B,SAAS,SAAS,mDAAiB,OAAO,EAAE;AAEjH,QAAM,CAAC,gBAAgB,iBAAiB,IAAIA,aAAAA,SAAS,KAAK;AAC1D,QAAM,CAAC,aAAa,cAAc,IAAIA,aAAAA,SAAwB,IAAI;AAClE,QAAM,CAAC,cAAc,eAAe,IAAIA,aAAAA,SAAsB,IAAI;AAClE,QAAM,CAAC,gBAAgB,iBAAiB,IAAIA,aAAAA,SAAwB,IAAI;AAClE,QAAA,CAAC,kBAAkB,mBAAmB,IAAIA,aAAA;AAAA,IAC1C,SAAS,WAAU,mDAAiB,WAAU,gBAAgB,QAAQ,aAAa;AAAA,EACzF;AAAS,QAAA,eAAeC,oBAAyB,IAAI;AACrD,QAAM,gBAAgB,WAAkE;AAClF,QAAA,CAAC,eAAe,gBAAgB,IAAID,sBAA6B,SAAS,SAAS,mDAAiB,MAAM,aAAa,EAAE;AACzH,QAAA,CAAC,UAAU,WAAW,IAAIA,aAAA;AAAA,IAC1B,SAAS,WAAU,mDAAiB,cAAa,SACzC,gBAAgB,SAAS,aACzB;AAAA,EACd;AACM,QAAA,CAAC,YAAY,aAAa,IAAIA,aAAA;AAAA,IAC9B,SAAS,WAAU,mDAAiB,cAAa,gBAAgB,aAAkC;AAAA,EACzG;AAEA,QAAM,CAAC,QAAQ,SAAS,IAAIA,aAAAA,SAAgB,CAAA,CAAE;AAC9C,QAAM,CAAC,YAAY,aAAa,IAAIA,aAAAA,SAAS,KAAK;AAE5C,QAAA,0BAA0B,CAAC,qBAA6D;AACjF,WAAA,iBAAiB,IAAI,CAAC,cAAc;AAAA,MACrC,OAAO,SAAS,GAAG,SAAS;AAAA,MAC5B,OAAO,SAAS;AAAA,MAChB,WAAW,SAAS;AAAA,IAAA,EACxB;AAAA,EACR;AACM,QAAA,CAAC,iBAAiB,kBAAkB,IAAIA,aAAA;AAAA,IAAyB,MACjE,SAAS,WAAU,mDAAiB,oBAAmB,wBAAwB,mDAAiB,gBAAgB,IAAI,CAAA;AAAA,EAC1H;AACA,QAAM,YAAY,MAAM;AAClB,QAAI,SAAS,QAAQ;AACf,sBAAgB,EAAE;AAClB,uBAAiB,EAAE;AACnB,yBAAmB,CAAA,CAAE;AACrB,sBAAgB,IAAI;AACpB,wBAAkB,IAAI;AACtB,0BAAoB,EAAE;AACtB,wBAAkB,KAAK;AACvB,qBAAe,IAAI;AACnB,uBAAiB,KAAK;AAAA,IAAA;AAAA,EAIlC;AAEA,QAAM,SAAS;AAAA,IACT,EAAE,IAAI,KAAK,MAAM,IAAI;AAAA,IACrB,EAAE,IAAI,KAAK,MAAM,IAAI;AAAA,IACrB,EAAE,IAAI,KAAK,MAAM,IAAI;AAAA,EAC3B;AACM,QAAA,sBAAsB,CAAC,UAAkB;AACzC,qBAAiB,KAAK;AAAA,EAE5B;AAEM,QAAA,qBAAqB,CAAC,gBAAgB,CAAC;AAC7C,QAAM,mBAAmB,MAAM;AACzB,QAAI,eAAe,SAAS;AACtB,aAAO,CAAC,oBAAoB,CAAC,gBAAgB,CAAC;AAAA,IAAA,OAC7C;AACM,aAAA,CAAC,gBAAgB,CAAC;AAAA,IAAA;AAAA,EAErC;AACM,QAAA,mBAAmB,OAAO,UAA+C;;AACzE,UAAM,QAAO,WAAM,OAAO,UAAb,mBAAqB;AAClC,QAAI,CAAC,KAAM;AAGX,mBAAe,IAAI;AAGb,UAAA,gBAAgB,MAAM,OAAO;AAC/B,QAAA,KAAK,OAAO,eAAe;AACzB,qBAAe,6BAA6B;AAC5C;AAAA,IAAA;AAGN,UAAM,eAAe,CAAC,cAAc,aAAa,aAAa,YAAY;AAC1E,QAAI,CAAC,aAAa,SAAS,KAAK,IAAI,GAAG;AACjC,qBAAe,kDAAkD;AACjE;AAAA,IAAA;AAGN,oBAAgB,IAAI;AACd,UAAA,SAAS,IAAI,WAAW;AAC9B,WAAO,YAAY,MAAM;AACnB,wBAAkB,OAAO,MAAgB;AAAA,IAC/C;AACA,WAAO,cAAc,IAAI;AAAA,EAC/B;AAEA,QAAM,eAAe,MAAM;AACrB,QAAI,CAAC,aAAc;AAEnB,sBAAkB,IAAI;AAChB,UAAA,WAAW,IAAI,SAAS;AACrB,aAAA,OAAO,WAAW,aAAa;AACxC,aAAS,OAAO,QAAQ,cAAc,aAAa,IAAI;AAEvD,kBAAc,OAAO,UAAU;AAAA,MACzB,QAAQ;AAAA,MACR,SAAS;AAAA,IAAA,CACd;AAAA,EACP;AAEA,QAAM,iBAAiB,MAAM;;AACvB,uBAAa,YAAb,mBAAsB;AAAA,EAC5B;AACAE,eAAAA,UAAU,MAAM;AACV,QAAI,cAAc,MAAM;AACd,UAAA,cAAc,KAAK,OAAO;AACT,uBAAA,cAAc,KAAK,KAAK;AACvC,0BAAkB,KAAK;AAAA,MAAA,WAClB,cAAc,KAAK,SAAS;AAEb,4BAAA,cAAc,KAAK,OAAO;AAE9C,cAAM,WAAW;AAAA,UACX,IAAI,KAAK,IAAI;AAAA,UACb,KAAK,cAAc,KAAK;AAAA,UACxB,UAAU,OAAO,SAAS;AAAA,UAC1B,WAAW;AAAA,QACjB;AAEA,kBAAU,CAAC,eAAe,CAAC,GAAG,YAAY,QAAQ,CAAC;AACnD,0BAAkB,KAAK;AACvB,uBAAe,IAAI;AACnB,wBAAgB,IAAI;AACpB,0BAAkB,IAAI;AACtB,YAAI,aAAa,SAAS;AACpB,uBAAa,QAAQ,QAAQ;AAAA,QAAA;AAAA,MACnC;AAAA,IACN;AAAA,EACN,GACH,CAAC,cAAc,IAAI,CAAC;AAEvB,QAAM,cAAc,MAAM;AACpB,QAAI,SAAS,QAAQ;AACf,sBAAgB,EAAE;AAClB,uBAAiB,EAAE;AACnB,sBAAgB,IAAI;AACpB,yBAAmB,CAAA,CAAE;AACrB,wBAAkB,IAAI;AACtB,0BAAoB,EAAE;AACtB,wBAAkB,KAAK;AACvB,qBAAe,IAAI;AACnB,uBAAiB,KAAK;AACtB,oBAAc,KAAK;AAAA,IAAA;AAEzB,kBAAc,KAAK;AAAA,EAEzB;AACA,QAAM,iBAAiB,MAAM;AACnB,QAAA,CAAC,iBAAiB,CAAC,cAAc;AAC/B,cAAQ,MAAM,8BAA8B;AAC5C;AAAA,IAAA;AAGN;AAAA,MACM;AAAA,MACA,SAAS,aAAa;AAAA,MACtB;AAAA,MACA,SAAS,YAAY,GAAG;AAAA,MACxB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,mDAAiB;AAAA,IACvB;AAAA,EACN;AAEM,QAAA,yBAAyB,CAAC,QAA2B;AACrD,kBAAc,GAAG;AACjB,QAAI,QAAQ,SAAS;AACf,uBAAiB,GAAG;AACpB,yBAAmB,CAAA,CAAE;AAAA,IAAA,OACpB;AACD,uBAAiB,SAAS,SAAS,mDAAiB,MAAM,aAAa,EAAE;AACzE;AAAA,QAAmB,MACb,SAAS,WAAU,mDAAiB,oBAAmB,wBAAwB,mDAAiB,gBAAgB,IAAI,CAAA;AAAA,MAC1H;AAAA,IAAA;AAAA,EAEZ;AAGM,SAAAH,kCAAA,IAAC,OAAI,EAAA,WAAU,wBACT,UAAAD,uCAAC,UAAO,MAAM,YAAY,cAAc,CAAC,WAAW;AAC9C,kBAAc,MAAM;AACpB,QAAI,CAAC,QAAQ;AACG,gBAAA;AAAA,IAAA;AAAA,EAGf,GAAA,UAAA;AAAA,IAAA,SAAS,SAAUC,kCAAA,IAAA,eAAA,EAAc,SAAO,MAAC,WAAU,QAC9C,UAAAA,kCAAA;AAAA,MAAC;AAAA,MAAA;AAAA,QACK,MAAK;AAAA,QACL,SAAQ;AAAA,QAEb,UAAA;AAAA,MAAA;AAAA,IAAA,EAIP,CAAA,IAAmBA,kCAAAA,IAAC,eAAc,EAAA,SAAO,MAAC,WAAU,QAC9C,UAAAD,kCAAA,KAAC,QAAO,EAAA,WAAU,uCAAsC,UAAA;AAAA,MAAA;AAAA,MAAG;AAAA,IAAA,EAAA,CAAW,EAE5E,CAAA;AAAA,IACAA,kCAAA;AAAA,MAAC;AAAA,MAAA;AAAA,QACK,WAAU;AAAA,QAEV,UAAA;AAAA,UAACA,kCAAAA,KAAA,OAAA,EAAI,WAAU,cACT,UAAA;AAAA,YAAAC,kCAAAA,IAAC,SAAI,WAAU,aACR,UAAS,SAAA,+CAAU,MAAG,EAAA,WAAU,qDAAoD,UAAA,gBAAA,CAErF,IAASA,kCAAAA,IAAA,MAAA,EAAG,WAAU,qDAAoD,0BAE1E,EACN,CAAA;AAAA,kDACC,OAAI,EAAA,WAAU,cACT,UAACD,kCAAA,KAAA,OAAA,EAAI,WAAU,yCACT,UAAA;AAAA,cAACA,kCAAAA,KAAA,OAAA,EAAI,WAAU,2BACT,UAAA;AAAA,gBAACC,kCAAA,IAAA,OAAA,EAAM,SAAQ,cAAa,UAAa,iBAAA;AAAA,gBACzCD,kCAAA;AAAA,kBAAC;AAAA,kBAAA;AAAA,oBACK,OAAO;AAAA,oBACP,eAAe,CAAC,QAA2B,uBAAuB,GAAG;AAAA,oBACrE,WAAU;AAAA,oBAGV,UAAA;AAAA,sBAACA,kCAAAA,KAAA,OAAA,EAAI,WAAU,+BACT,UAAA;AAAA,wBAAAC,kCAAA,IAAC,gBAAe,EAAA,IAAG,cAAa,OAAM,SAAQ;AAAA,wBAC7CA,kCAAA,IAAA,OAAA,EAAM,SAAQ,cAAa,UAAU,aAAA,CAAA;AAAA,sBAAA,GAC5C;AAAA,sBACAD,kCAAAA,KAAC,OAAI,EAAA,WAAU,+BACT,UAAA;AAAA,wBAAAC,kCAAA,IAAC,gBAAe,EAAA,IAAG,cAAa,OAAM,SAAQ;AAAA,wBAC7CA,kCAAA,IAAA,OAAA,EAAM,SAAQ,cAAa,UAAc,iBAAA,CAAA;AAAA,sBAAA,EAChD,CAAA;AAAA,oBAAA;AAAA,kBAAA;AAAA,gBAAA;AAAA,cACN,GACN;AAAA,cACAD,kCAAAA,KAAC,OAAI,EAAA,WAAU,aACT,UAAA;AAAA,gBAACC,kCAAA,IAAA,OAAA,EAAM,SAAQ,gBAAe,UAAwB,4BAAA;AAAA,gBACtDA,kCAAA;AAAA,kBAAC;AAAA,kBAAA;AAAA,oBACK,IAAG;AAAA,oBACH,aAAY;AAAA,oBACZ,WAAU;AAAA,oBACV,UAAQ;AAAA,oBACR,OAAO;AAAA,oBACP,UAAU,CAAC,MAAM,gBAAgB,EAAE,OAAO,KAAK;AAAA,oBAC/C,UAAU,SAAS;AAAA,kBAAA;AAAA,gBAAQ;AAAA,gBAChCA,kCAAA,IAAA,KAAA,EAAE,WAAU,2BAA0B,UAAsB,yBAAA,CAAA;AAAA,cAAA,GACnE;AAAA,cACC,eAAe,WAAYD,uCAAA,OAAA,EAAI,WAAU,aACpC,UAAA;AAAA,gBAACC,kCAAA,IAAA,OAAA,EAAM,SAAQ,mBAAkB,UAA2B,+BAAA;AAAA,gBAC5DD,kCAAA;AAAA,kBAAC;AAAA,kBAAA;AAAA,oBACK,OAAO;AAAA,oBACP,eAAe;AAAA,oBACf,UAAU,SAAS;AAAA,oBAEnB,UAAA;AAAA,sBAAAC,sCAAC,eACK,EAAA,UAAAA,kCAAA,IAAC,aAAY,EAAA,aAAY,iBAAiB,CAAA,GAChD;AAAA,4DACC,eACM,EAAA,UAAA,OAAO,IAAI,CAAC,MACNA,kCAAAA,IAAA,YAAA,EAAW,OAAO,EAAE,IACd,UAAE,EAAA,QADqB,EAAE,EAEhC,CACL,EACP,CAAA;AAAA,oBAAA;AAAA,kBAAA;AAAA,gBAAA;AAAA,cACN,GACN;AAAA,cACC,iBAEWD,kCAAA,KAAAK,4BAAA,EAAA,UAAA;AAAA,gBAAAJ,sCAAC,SAAI,WAAU,iBACR,UAAkB,kBAAA,OAAO,eAAe,WAAWA,kCAAA;AAAA,kBAAC;AAAA,kBAAA;AAAA,oBAC/C,OAAM;AAAA,oBACN,QAAO;AAAA,oBAEP,oBAAoB,mBAAmB,CAAC;AAAA,oBACxC,eAAe,CAAC,YAAYK,kBAAiB;AAEvC,yCAAmB,CAAC,wBAAwB;AAAA,wBACtC,GAAI,uBAAuB,CAAC;AAAA,wBAC5B,EAAE,WAAW,YAAY,OAAOA,eAAc,OAAO,GAAG;AAAA,sBAAA,CAC7D;AAAA,oBACP;AAAA,oBACA,kBAAkB,CAAC,eAAe;AAE5B;AAAA,wBAAmB,CAAC,yBACb,uBAAuB,CAAA,GAAI;AAAA,0BACtB,CAAC,QAAQ,IAAI,cAAc;AAAA,wBAAA;AAAA,sBAEvC;AAAA,oBACN;AAAA,oBACA,UAAU;AAAA,oBACV,QAAQ,OAAO,aAAa,KAAK,KAAK;AAAA,kBAAA;AAAA,gBAAA,GAElD;AAAA,gBACAN,kCAAAA,KAAC,OAAI,EAAA,WAAU,iBACT,UAAA;AAAA,kBAAAA,uCAAC,OAAM,EAAA,UAAA;AAAA,oBAAA;AAAA,oBAAgB,eAAe,UAAU,eAAe;AAAA,kBAAA,GAAa;AAAA,kBAC5EA,kCAAAA,KAAC,OAAI,EAAA,WAAU,2BACT,UAAA;AAAA,oBAAAC,kCAAA;AAAA,sBAAC;AAAA,sBAAA;AAAA,wBACK,MAAK;AAAA,wBACL,UAAQ;AAAA,wBACR,QAAO,6CAAc,SAAQ;AAAA,wBAC7B,WAAU;AAAA,wBACV,SAAS;AAAA,wBACT,OAAO,EAAE,QAAQ,UAAU;AAAA,sBAAA;AAAA,oBACjC;AAAA,oBACAA,kCAAA;AAAA,sBAAC;AAAA,sBAAA;AAAA,wBACK,KAAK;AAAA,wBACL,MAAK;AAAA,wBACL,QAAO;AAAA,wBACP,UAAU;AAAA,wBACV,WAAU;AAAA,sBAAA;AAAA,oBAChB;AAAA,oBACAA,kCAAA;AAAA,sBAAC;AAAA,sBAAA;AAAA,wBACK,MAAK;AAAA,wBACL,SAAS,eAAe,eAAe;AAAA,wBACvC,UAAU;AAAA,wBAET,UACK,iBAAAD,uCAAC,QAAK,EAAA,WAAU,2BACV,UAAA;AAAA,0BAAAA,kCAAA,KAAC,OAAI,EAAA,WAAU,wBAAuB,SAAQ,aACxC,UAAA;AAAA,4BAAAC,kCAAA,IAAC,UAAO,EAAA,WAAU,cAAa,IAAG,MAAK,IAAG,MAAK,GAAE,MAAK,QAAO,gBAAe,aAAY,KAAI,MAAK,QAAO;AAAA,kEACvG,QAAK,EAAA,WAAU,cAAa,MAAK,gBAAe,GAAE,kHAAkH,CAAA;AAAA,0BAAA,GAC3K;AAAA,0BAAM;AAAA,wBAAA,GAEZ,IACF,eACE,WAEA;AAAA,sBAAA;AAAA,oBAAA;AAAA,kBAEZ,GACN;AAAA,kBACC,eACKA,kCAAA,IAAC,KAAE,EAAA,WAAU,6BAA6B,UAAY,aAAA;AAAA,kBAE5DD,kCAAAA,KAAC,OAAI,EAAA,WAAU,kBACR,UAAA;AAAA,oBAAA,kBACMC,kCAAA,IAAA,OAAA,EAAI,WAAU,uBACT,UAACA,sCAAA,OAAA,EAAI,KAAK,gBAAgB,KAAI,iBAAgB,WAAU,uCAAuC,CAAA,GACrG;AAAA,oBAEL,oBAAoB,CAAC,kBAChBA,kCAAA,IAAC,SAAI,WAAU,uBACT,UAACA,kCAAAA,IAAA,OAAA,EAAI,KAAK,kBAAkB,KAAI,kBAAiB,WAAU,wCAAuC,EACxG,CAAA;AAAA,kBAAA,EAEZ,CAAA;AAAA,gBAAA,GACN;AAAA,gBACC,kBAAkB,OAAQD,uCAAA,OAAA,EAAI,WAAU,aACnC,UAAA;AAAA,kBAACC,kCAAA,IAAA,OAAA,EAAM,SAAQ,YAAW,UAAmB,uBAAA;AAAA,kBAC7CA,kCAAA;AAAA,oBAAC;AAAA,oBAAA;AAAA,sBACK,IAAG;AAAA,sBACH,aAAY;AAAA,sBACZ,WAAU;AAAA,sBACV,UAAQ;AAAA,sBACR,OAAO;AAAA,sBACP,UAAU,CAAC,MAAM,YAAY,EAAE,OAAO,KAAK;AAAA,sBAC3C,MAAK;AAAA,oBAAA;AAAA,kBAAA;AAAA,gBACX,EACN,CAAA;AAAA,cAAA,EACN,CAAA;AAAA,YAAA,EAAA,CAEZ,EACN,CAAA;AAAA,UAAA,GACN;AAAA,UACAD,kCAAAA,KAAC,OAAI,EAAA,WAAU,oDACR,UAAA;AAAA,YAAA,CAAC,iBACIC,kCAAA;AAAA,cAAC;AAAA,cAAA;AAAA,gBACK,MAAK;AAAA,gBACL,WAAU;AAAA,gBACV,UAAU;AAAA,gBACV,SAAS,MAAM,iBAAiB,IAAI;AAAA,gBACzC,UAAA;AAAA,cAAA;AAAA,YAED;AAAA,YAEL,iBACKA,kCAAA;AAAA,cAAC;AAAA,cAAA;AAAA,gBACK,MAAK;AAAA,gBACL,WAAU;AAAA,gBACV,SAAS,MAAM,eAAe;AAAA,gBAC9B,UAAU,iBAAiB;AAAA,gBAAG,UAAA;AAAA,cAAA;AAAA,YAAA;AAAA,UAEpC,EAEZ,CAAA;AAAA,QAAA;AAAA,MAAA;AAAA,IAAA;AAAA,EACN,EAAA,CACN,EAEN,CAAA;AAEZ;"}