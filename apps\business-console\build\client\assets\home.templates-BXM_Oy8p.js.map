{"version": 3, "file": "home.templates-BXM_Oy8p.js", "sources": ["../../../app/constants/whatsappTemplates.ts", "../../../app/routes/home.templates.tsx"], "sourcesContent": ["// File: app/constants/whatsappTemplates.ts\r\nexport const REQUIRED_TEMPLATES = [\r\n  {\r\n    name: \"f_business_order_confirm\",\r\n    language: \"en\",\r\n    category: \"UTILITY\",\r\n    components: [\r\n      {\r\n        type: \"BODY\",\r\n        text: \"Hello {{1}} 👋,\\nThank you for your order! 🛒 We've received your request for the following items:\\n\\n{{2}}\\n\\nYour order *#{{3}}* will be delivered tomorrow morning. 🚚\\nTotal payable: ₹{{4}}. 💰\\n\\nThanks for choosing farmersMandi ! 🙏\\n\\nBest,\\nfarmersMandi\",\r\n        example: {\r\n          body_text: [\r\n            [\"John\", \"• Tomatoes + 1 PC  • Potatoes + 2 PC\", \"1234\", \"500\"],\r\n          ],\r\n        },\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    name: \"mnet_seller_order_placed\",\r\n    language: \"en\",\r\n    category: \"UTILITY\",\r\n    components: [\r\n      {\r\n        type: \"HEADER\",\r\n        format: \"TEXT\",\r\n        text: \"New order recieved \",\r\n      },\r\n      {\r\n        type: \"BODY\",\r\n        text: `:new: New Order Alert! \\n Hi {{4}}, \\nYou have a new order from {{3}}! \\n :shopping_trolley: Order ID: {{1}} \\n :moneybag: Amount: ₹{{2}} \\n Please start processing the order promptly! :rocket:`,\r\n        example: {\r\n          body_text: [[\"100\", \"1024\", \"SellerName\", \"buyerName\"]],\r\n        },\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    name: \"pending_dues_buyer_1\",\r\n    language: \"en\",\r\n    category: \"UTILITY\",\r\n    components: [\r\n      {\r\n        type: \"BODY\",\r\n        text: \"Hi {{1}},\\nYou have pending dues with us. Please open the app to review and clear them as soon as possible. Ignore if already paid.\\nThank you !\",\r\n        example: {\r\n          body_text: [[\"John\"]],\r\n        },\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    name: \"hello_mnet\",\r\n    language: \"en\",\r\n    category: \"UTILITY\",\r\n    components: [\r\n      {\r\n        type: \"BODY\",\r\n        text: \"Hello, thanks for requesting an introductory call from us. We will be in touch soon.\",\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    name: \"hello_world\",\r\n    language: \"en_US\",\r\n    category: \"UTILITY\",\r\n    components: [\r\n      {\r\n        type: \"HEADER\",\r\n        format: \"TEXT\",\r\n        text: \"Hello World\",\r\n      },\r\n      {\r\n        type: \"BODY\",\r\n        text: \"Welcome and congratulations!! This message demonstrates your ability to send a WhatsApp message notification from the Cloud API, hosted by Meta. Thank you for taking the time to test with us.\",\r\n      },\r\n      {\r\n        type: \"FOOTER\",\r\n        text: \"WhatsApp Business Platform sample message\",\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    name: \"delivery_completed\",\r\n    language: \"en\",\r\n    category: \"UTILITY\",\r\n    components: [\r\n      {\r\n        type: \"HEADER\",\r\n        format: \"TEXT\",\r\n        text: \"Delivery Completed\",\r\n      },\r\n      {\r\n        type: \"BODY\",\r\n        text: \"Hi {{1}} 👋,\\nYour order *#{{2}}* has been delivered. ✅ We hope you're happy with the fresh produce! 🥗\\n\\nTotal amount paid: *₹{{3}}*. 💰\\nThanks for choosing us! 🙏\\n\\nBest,\\nfarmersMandi\",\r\n        example: {\r\n          body_text: [[\"John\", \"123456\", \"4005.70\"]],\r\n        },\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    name: \"delivery_confirmation_with_credit\",\r\n    language: \"en\",\r\n    category: \"UTILITY\",\r\n    components: [\r\n      {\r\n        type: \"HEADER\",\r\n        format: \"TEXT\",\r\n        text: \"Delivery Confirmation\",\r\n      },\r\n      {\r\n        type: \"BODY\",\r\n        text: \"Hi {{1}} 👋,\\nYour order #{{2}} has been delivered. ✅\\nOrder Amount : ₹{{3}}\\n\\nTotal Amount due: *₹{{4}}*. 💰\\nPlease pay soon. 💳\\n\\nBest,\\nfarmersMandi\",\r\n        example: {\r\n          body_text: [[\"John\", \"1234\", \"100.86\", \"8045.76\"]],\r\n        },\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    name: \"complete_order_cancellation\",\r\n    language: \"en\",\r\n    category: \"UTILITY\",\r\n    components: [\r\n      {\r\n        type: \"HEADER\",\r\n        format: \"TEXT\",\r\n        text: \"Order Cancellation\",\r\n      },\r\n      {\r\n        type: \"BODY\",\r\n        text: \"Dear {{1}},\\n\\nWe're sorry, but your order #{{2}} couldn't be fulfilled due to stock unavailability.❌\\n\\nWe apologize for the inconvenience and hope to serve you again. 🙏\\n\\nBest,\\nfarmersMandi\",\r\n        example: {\r\n          body_text: [[\"John\", \"12345\"]],\r\n        },\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    name: \"out_for_delivery\",\r\n    language: \"en\",\r\n    category: \"UTILITY\",\r\n    components: [\r\n      {\r\n        type: \"HEADER\",\r\n        format: \"TEXT\",\r\n        text: \"Out for Delivery\",\r\n      },\r\n      {\r\n        type: \"BODY\",\r\n        text: \"Good morning {{1}}! 🌅\\nYour order #{{2}} is on the way and will be delivered soon. 🚚\\n\\nTotal payable on delivery: *₹{{3}}*\\n\\nThanks for choosing us! 🙏\\n\\nBest,\\nfarmersMandi\",\r\n        example: {\r\n          body_text: [[\"John\", \"12345\", \"5090.86\"]],\r\n        },\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    \"name\": \"early_pending_payment_special_offer\",\r\n    \"language\": \"en\",\r\n    \"category\": \"UTILITY\",\r\n    \"allow_category_change\": true,\r\n    \"parameter_format\": \"NAMED\",\r\n    \"components\": [\r\n      {\r\n        \"type\": \"HEADER\",\r\n        \"format\": \"TEXT\",\r\n        \"text\": \"Settle Balance & Save!\"\r\n      },\r\n      {\r\n        \"type\": \"BODY\",\r\n        \"text\": \"*🎉 Hi {{customer_name}},*\\n\\nYour account shows an outstanding balance of *₹{{pending_amount}}*. We’re offering an *exclusive discount* or priority services for settling this balance by *{{due_date}}* !\\n\\nThis balance reflects all partial payments made and outstanding amounts. Act now to unlock the benefits!\",\r\n        \"example\": {\r\n          \"body_text_named_params\": [\r\n            {\r\n              \"param_name\": \"customer_name\",\r\n              \"example\": \"John\"\r\n            },\r\n            {\r\n              \"param_name\": \"pending_amount\",\r\n              \"example\": \"5032.45\"\r\n            },\r\n            {\r\n              \"param_name\": \"due_date\",\r\n              \"example\": \"2nd Feb '25\"\r\n            }\r\n          ]\r\n        }\r\n      },\r\n      {\r\n        \"type\": \"FOOTER\",\r\n        \"text\": \"Don’t miss this limited-time offer!\"\r\n      },\r\n      {\r\n        \"type\": \"BUTTONS\",\r\n        \"buttons\": [\r\n          {\r\n            \"type\": \"QUICK_REPLY\",\r\n            \"text\": \"Pay Now\"\r\n          },\r\n          {\r\n            \"type\": \"QUICK_REPLY\",\r\n            \"text\": \"Request Details\"\r\n          }\r\n        ]\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"name\": \"pending_payment_reminder\",\r\n    \"language\": \"en\",\r\n    \"category\": \"UTILITY\",\r\n    \"allow_category_change\": true,\r\n    \"parameter_format\": \"NAMED\",\r\n    \"components\": [\r\n      {\r\n        \"type\": \"HEADER\",\r\n        \"format\": \"TEXT\",\r\n        \"text\": \"Outstanding Balance: ₹{{pending_amount}}\",\r\n        \"example\": {\r\n          \"header_text_named_params\": [\r\n            {\r\n              \"param_name\": \"pending_amount\",\r\n              \"example\": \"250\"\r\n            }\r\n          ]\r\n        }\r\n      },\r\n      {\r\n        \"type\": \"BODY\",\r\n        \"text\": \"*👋 Dear {{customer_name}},*\\n\\nWe’d like to remind you that your current outstanding balance with us is *₹{{pending_amount}}* as of *{{today_date}}*.\\n\\nThis may include multiple orders, and we appreciate your partial payments made. To ensure your account remains in good standing, we kindly request you to settle the remaining amount.\\n\\nTap below to clear your balance or reach out for a detailed breakdown.\",\r\n        \"example\": {\r\n          \"body_text_named_params\": [\r\n            {\r\n              \"param_name\": \"customer_name\",\r\n              \"example\": \"John\"\r\n            },\r\n            {\r\n              \"param_name\": \"pending_amount\",\r\n              \"example\": \"250\"\r\n            },\r\n            {\r\n              \"param_name\": \"today_date\",\r\n              \"example\": \"28 Jan '25\"\r\n            }\r\n          ]\r\n        }\r\n      },\r\n      {\r\n        \"type\": \"FOOTER\",\r\n        \"text\": \"We’re here to assist with any questions.\"\r\n      },\r\n      {\r\n        \"type\": \"BUTTONS\",\r\n        \"buttons\": [\r\n          {\r\n            \"type\": \"QUICK_REPLY\",\r\n            \"text\": \"Clear Dues\"\r\n          },\r\n          {\r\n            \"type\": \"QUICK_REPLY\",\r\n            \"text\": \"Request Details\"\r\n          }\r\n        ]\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"name\": \"open_for_ordering\",\r\n    \"language\": \"en\",\r\n    \"category\": \"MARKETING\",\r\n    \"allow_category_change\": true,\r\n    \"parameter_format\": \"NAMED\",\r\n    \"components\": [\r\n      {\r\n        \"type\": \"HEADER\",\r\n        \"format\": \"TEXT\",\r\n        \"text\": \"Fresh, Fast, and Affordable!\"\r\n      },\r\n      {\r\n        \"type\": \"BODY\",\r\n        \"text\": \"*🍎 Hello,*\\n\\nWe’re open for orders! Explore our wide range of products:\\n\\n🍅 Fresh Fruits & Vegetables\\n🥖 Daily Groceries & Staples\\n🧴 Personal Care Products\\n🧃 Beverages and more!\\n\\nShop today for premium quality and hassle-free delivery.\"\r\n      },\r\n      {\r\n        \"type\": \"FOOTER\",\r\n        \"text\": \"Shop now, and let us deliver to your doorstep.\"\r\n      },\r\n      {\r\n        \"type\": \"BUTTONS\",\r\n        \"buttons\": [\r\n          {\r\n            \"type\": \"QUICK_REPLY\",\r\n            \"text\": \"Start Shopping\"\r\n          },\r\n          {\r\n            \"type\": \"QUICK_REPLY\",\r\n            \"text\": \"Contact Us\"\r\n          }\r\n        ]\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    \"name\": \"item_discount_trial_offer\",\r\n    \"language\": \"en\",\r\n    \"category\": \"MARKETING\",\r\n    \"parameter_format\": \"NAMED\",\r\n    \"components\": [\r\n        {\r\n            \"type\": \"HEADER\",\r\n            \"format\": \"IMAGE\",\r\n            \"example\": {\r\n                \"header_handle\": [\r\n                    \"4:dG9tYXRvLmpwZw==:aW1hZ2UvanBlZw==:ARa6rojXaNTQ_RzAjAWcDdba8XD-pb3o_Ofzv_c4t4V1qqWsKT8jYbKYHo68H59GyH-KMEYXCsBVqKa09Z6mRd4gNg-J7fy0kuOBxt0qdBLR_w:e:1740257819:522237886866403:61564876907669:ARayWjrjjP_GfZltzcg\"\r\n                ]\r\n            }\r\n        },\r\n        {\r\n            \"type\": \"BODY\",\r\n            \"text\": \"*🎉 Exclusive Offer Just for You!* Grab a limited-time deal on *{{item_name}}* now! 🎁 Save more while enjoying the best quality products. 🛒 Don't miss out—this offer ends soon!\",\r\n            \"example\": {\r\n                \"body_text_named_params\": [\r\n                    {\r\n                        \"param_name\": \"item_name\",\r\n                        \"example\": \"Potato\"\r\n                    }\r\n                ]\r\n            }\r\n        },\r\n        {\r\n            \"type\": \"BUTTONS\",\r\n            \"buttons\": [\r\n                {\r\n                    \"type\": \"QUICK_REPLY\",\r\n                    \"text\": \"Claim Discount Now\"\r\n                },\r\n                {\r\n                    \"type\": \"URL\",\r\n                    \"text\": \"Visit website\",\r\n                    \"url\": \"https://mnetonline.in/{{1}}\",\r\n                    \"example\": [\r\n                        \"https://mnetonline.in/wa_marketing?seller_id=4\"\r\n                    ]\r\n                }\r\n            ]\r\n        }\r\n    ]\r\n  },\r\n];\r\n\r\n\r\nexport const B2B_TEMPLATES = [  // ===================================================\r\n  // B2B Templates\r\n  // ===================================================\r\n\r\n  // 1. B2B Order Placed (Quick Commerce)\r\n  {\r\n    name: \"b2b_order_placed_confirmed_quick_v2\",\r\n    language: \"en\",\r\n    category: \"UTILITY\",\r\n    allow_category_change: true,\r\n    parameter_format: \"NAMED\",\r\n    components: [\r\n      {\r\n        type: \"HEADER\",\r\n        format: \"TEXT\",\r\n        text: \"Great news we - just got your order!\"\r\n      },\r\n      {\r\n        type: \"BODY\",\r\n        text: \"Hi *{{customer_name}}*,\\n\\nThanks a lot for your order! Your order *#{{order_id}}* is confirmed and will be on its way by *{{expt_delivery_time}}* today.\\n\\n*Order Details:*\\n{{order_details}}\\n\\nTotal: *₹{{total_amount}}*\\n\\nWe really appreciate you working with us.\\n\\nCheers,\\n*{{business_name}}*\",\r\n        example: {\r\n          body_text_named_params: [\r\n            { param_name: \"customer_name\", example: \"Acme Corp\" },\r\n            { param_name: \"business_name\", example: \"FreshTech Services\" },\r\n            { param_name: \"order_id\", example: \"B12345\" },\r\n            { param_name: \"expt_delivery_time\", example: \"7:30 PM\" },\r\n            { param_name: \"order_details\", example: \"- 10 Apples\\n- 5 Oranges\" },\r\n            { param_name: \"total_amount\", example: \"1500\" }\r\n          ]\r\n        }\r\n      }\r\n    ]\r\n  },\r\n\r\n  // 2. B2B Order Placed (Standard / Next Day Delivery)\r\n  // {\r\n  //   name: \"b2b_order_placed_confirmed_standard_v2\",\r\n  //   language: \"en\",\r\n  //   category: \"UTILITY\",\r\n  //   allow_category_change: true,\r\n  //   parameter_format: \"NAMED\",\r\n  //   components: [\r\n  //     {\r\n  //       type: \"HEADER\",\r\n  //       format: \"TEXT\",\r\n  //       text: \"🛒 New order received!\"\r\n  //     },\r\n  //     {\r\n  //       type: \"BODY\",\r\n  //       text: \"Hi *{{customer_name}}*,\\n\\nThanks for your order! Your order *#{{order_id}}* is confirmed. Just a quick note: orders placed after *8 PM* will be delivered by *6 AM* tomorrow.\\n\\n*Order Details:*\\n{{order_details}}\\n\\nTotal: *₹{{total_amount}}*\\n\\nWe’re grateful for your continued trust.\\n\\nBest,\\n*{{business_name}}*\",\r\n  //       example: {\r\n  //         body_text_named_params: [\r\n  //           { param_name: \"customer_name\", example: \"Acme Corp\" },\r\n  //           { param_name: \"business_name\", example: \"FreshTech Services\" },\r\n  //           { param_name: \"order_id\", example: \"B67890\" },\r\n  //           { param_name: \"order_details\", example: \"- 20 Bananas\\n- 2 Mangoes\" },\r\n  //           { param_name: \"total_amount\", example: \"2300\" }\r\n  //         ]\r\n  //       }\r\n  //     }\r\n  //   ]\r\n  // },\r\n\r\n  // 3. B2B Order Out for Delivery\r\n  {\r\n    name: \"b2b_order_out_for_delivery_v2\",\r\n    language: \"en\",\r\n    category: \"UTILITY\",\r\n    allow_category_change: true,\r\n    parameter_format: \"NAMED\",\r\n    components: [\r\n      {\r\n        type: \"HEADER\",\r\n        format: \"TEXT\",\r\n        text: \"Your order is on its way!\"\r\n      },\r\n      {\r\n        type: \"BODY\",\r\n        text: \"Hi *{{customer_name}}*,\\n\\nYour order *#{{order_id}}* is out for delivery and should reach you by *{{delivery_time}}*.\\n\\nThanks for partnering with us.\\n\\nRegards,\\n*{{business_name}}*\",\r\n        example: {\r\n          body_text_named_params: [\r\n            { param_name: \"customer_name\", example: \"Acme Corp\" },\r\n            { param_name: \"business_name\", example: \"FreshTech Services\" },\r\n            { param_name: \"order_id\", example: \"B12345\" },\r\n            { param_name: \"delivery_time\", example: \"8:00 PM\" }\r\n          ]\r\n        }\r\n      }\r\n    ]\r\n  },\r\n\r\n  // 4. B2B Order Delivered (Standard – Payment Received)\r\n  {\r\n    name: \"b2b_order_delivered_v2\",\r\n    language: \"en\",\r\n    category: \"UTILITY\",\r\n    allow_category_change: true,\r\n    parameter_format: \"NAMED\",\r\n    components: [\r\n      {\r\n        type: \"HEADER\",\r\n        format: \"TEXT\",\r\n        text: \"Delivery complete!\"\r\n      },\r\n      {\r\n        type: \"BODY\",\r\n        text: \"Hi *{{customer_name}}*,\\n\\nWe’re happy to let you know that your order *#{{order_id}}* has been delivered successfully. We hope everything meets your expectations!\\n\\n*Total Paid:* *₹{{paid_amount}}*\\n\\nThank you for doing business with us.\\n\\nWarm regards,\\n*{{business_name}}*\",\r\n        example: {\r\n          body_text_named_params: [\r\n            { param_name: \"customer_name\", example: \"Acme Corp\" },\r\n            { param_name: \"business_name\", example: \"FreshTech Services\" },\r\n            { param_name: \"order_id\", example: \"B12345\" },\r\n            { param_name: \"paid_amount\", example: \"1500\" }\r\n          ]\r\n        }\r\n      }\r\n    ]\r\n  },\r\n\r\n  // 4a. B2B Order Delivered with Credit (Payment to be repaid later)\r\n  {\r\n    name: \"b2b_order_delivered_credit_v2\",\r\n    language: \"en\",\r\n    category: \"UTILITY\",\r\n    allow_category_change: true,\r\n    parameter_format: \"NAMED\",\r\n    components: [\r\n      {\r\n        type: \"HEADER\",\r\n        format: \"TEXT\",\r\n        text: \"Order delivered – Payment on Credit\"\r\n      },\r\n      {\r\n        type: \"BODY\",\r\n        text: \"Hi *{{customer_name}}*,\\n\\nYour order *#{{order_id}}* has been delivered on credit. The order amount is *₹{{order_amount}}* and your total pending balance is now *₹{{total_pending_amount}}*.\\n\\nThank you for your continued partnership.\\n\\nBest regards,\\n*{{business_name}}*\",\r\n        example: {\r\n          body_text_named_params: [\r\n            { param_name: \"customer_name\", example: \"Acme Corp\" },\r\n            { param_name: \"business_name\", example: \"FreshTech Services\" },\r\n            { param_name: \"order_id\", example: \"B12345\" },\r\n            { param_name: \"order_amount\", example: \"1500\" },\r\n            { param_name: \"total_pending_amount\", example: \"500\" }\r\n          ]\r\n        }\r\n      }\r\n    ]\r\n  },\r\n\r\n  // 5. B2B Order Cancelled (with Refund, if applicable)\r\n  {\r\n    name: \"b2b_order_cancelled_v2\",\r\n    language: \"en\",\r\n    category: \"UTILITY\",\r\n    allow_category_change: true,\r\n    parameter_format: \"NAMED\",\r\n    components: [\r\n      {\r\n        type: \"HEADER\",\r\n        format: \"TEXT\",\r\n        text: \"Order Cancellation Notice\"\r\n      },\r\n      {\r\n        type: \"BODY\",\r\n        text: \"Hi *{{customer_name}}*,\\n\\nWe’re sorry to inform you that your order *#{{order_id}}* has been cancelled due to *{{cancellation_reason}}*.\\n{{#if refund_amount}}A refund of *₹{{refund_amount}}* has been processed for any payment received.{{/if}}\\n\\nWe apologize for any inconvenience and thank you for your understanding.\\n\\nBest regards,\\n*{{business_name}}*\",\r\n        example: {\r\n          body_text_named_params: [\r\n            { param_name: \"customer_name\", example: \"Acme Corp\" },\r\n            { param_name: \"business_name\", example: \"FreshTech Services\" },\r\n            { param_name: \"order_id\", example: \"B98765\" },\r\n            { param_name: \"cancellation_reason\", example: \"stock issues\" },\r\n            { param_name: \"refund_amount\", example: \"1500\" }\r\n          ]\r\n        }\r\n      }\r\n    ]\r\n  },\r\n\r\n  // 6. B2B Payment Successful\r\n  {\r\n    name: \"b2b_payment_success_v2\",\r\n    language: \"en\",\r\n    category: \"UTILITY\",\r\n    allow_category_change: true,\r\n    parameter_format: \"NAMED\",\r\n    components: [\r\n      {\r\n        type: \"HEADER\",\r\n        format: \"TEXT\",\r\n        text: \"Payment received – Thank you!\"\r\n      },\r\n      {\r\n        type: \"BODY\",\r\n        text: \"Hi *{{customer_name}}*,\\n\\nWe’ve received your payment of *₹{{paid_amount}}* for order *#{{order_id}}*. Everything is set on our end!\\n\\nThanks for your prompt action.\\n\\nCheers,\\n*{{business_name}}*\",\r\n        example: {\r\n          body_text_named_params: [\r\n            { param_name: \"customer_name\", example: \"Acme Corp\" },\r\n            { param_name: \"business_name\", example: \"FreshTech Services\" },\r\n            { param_name: \"order_id\", example: \"B12345\" },\r\n            { param_name: \"paid_amount\", example: \"1500\" }\r\n          ]\r\n        }\r\n      }\r\n    ]\r\n  },\r\n\r\n  // 7. B2B Payment Failed\r\n  {\r\n    name: \"b2b_payment_failed_v2\",\r\n    language: \"en\",\r\n    category: \"UTILITY\",\r\n    allow_category_change: true,\r\n    parameter_format: \"NAMED\",\r\n    components: [\r\n      {\r\n        type: \"HEADER\",\r\n        format: \"TEXT\",\r\n        text: \"Payment issue – Lets sort it out\"\r\n      },\r\n      {\r\n        type: \"BODY\",\r\n        text: \"Hi *{{customer_name}}*,\\n\\nWe couldn’t process the payment for order *#{{order_id}}* (attempted amount: *₹{{attempted_amount}}*). Kindly try again or let us know if you need any further help.\\n\\nThank you for your patience,\\n*{{business_name}}*\",\r\n        example: {\r\n          body_text_named_params: [\r\n            { param_name: \"customer_name\", example: \"Acme Corp\" },\r\n            { param_name: \"business_name\", example: \"FreshTech Services\" },\r\n            { param_name: \"order_id\", example: \"B12345\" },\r\n            { param_name: \"attempted_amount\", example: \"1500\" }\r\n          ]\r\n        }\r\n      }\r\n    ]\r\n  },\r\n\r\n  // 8. B2B Prepaid but Order Unconfirmed (Cancelled & Refund Processed)\r\n  {\r\n    name: \"b2b_prepaid_unconfirmed_v2\",\r\n    language: \"en\",\r\n    category: \"UTILITY\",\r\n    allow_category_change: true,\r\n    parameter_format: \"NAMED\",\r\n    components: [\r\n      {\r\n        type: \"HEADER\",\r\n        format: \"TEXT\",\r\n        text: \"Order not confirmed – Cancelled\"\r\n      },\r\n      {\r\n        type: \"BODY\",\r\n        text: \"Hi *{{customer_name}}*,\\n\\nWe received your payment of *₹{{paid_amount}}* for order *#{{order_id}}*, but we couldn’t confirm the order due to *{{reason}}*. As a result, the order has been cancelled and a refund of *₹{{refund_amount}}* has been processed.\\n\\nThanks for your patience,\\n*{{business_name}}*\",\r\n        example: {\r\n          body_text_named_params: [\r\n            { param_name: \"customer_name\", example: \"Acme Corp\" },\r\n            { param_name: \"business_name\", example: \"FreshTech Services\" },\r\n            { param_name: \"order_id\", example: \"B12345\" },\r\n            { param_name: \"paid_amount\", example: \"1500\" },\r\n            { param_name: \"reason\", example: \"technical issues\" },\r\n            { param_name: \"refund_amount\", example: \"1500\" }\r\n          ]\r\n        }\r\n      }\r\n    ]\r\n  }\r\n]\r\n\r\nexport const B2C_TEMPLATES = [\r\n  // ===================================================\r\n  // B2C Grocery / Supermarket Templates\r\n  // ===================================================\r\n\r\n  // 1. B2C Grocery Order Placed / Confirmed\r\n  {\r\n    name: \"b2c_grocery_order_placed_confirmed_v2\",\r\n    language: \"en\",\r\n    category: \"UTILITY\",\r\n    allow_category_change: true,\r\n    parameter_format: \"NAMED\",\r\n    components: [\r\n      {\r\n        type: \"HEADER\",\r\n        format: \"TEXT\",\r\n        text: \"Order confirmed – Thank you!\"\r\n      },\r\n      {\r\n        type: \"BODY\",\r\n        text: \"Hi *{{customer_name}}*,\\n\\nThanks so much for your order! Your order *#{{order_id}}* is confirmed and will reach you by *{{expt_delivery_time}}*.\\n\\n*Your Items:*\\n{{order_details}}\\n\\nTotal: *₹{{total_amount}}*\\n\\nWe’re excited to serve you and hope you enjoy your shopping experience with us.\\n\\nTake care,\\n*{{business_name}}*\",\r\n        example: {\r\n          body_text_named_params: [\r\n            { param_name: \"customer_name\", example: \"Jane Doe\" },\r\n            { param_name: \"business_name\", example: \"MarketFresh\" },\r\n            { param_name: \"order_id\", example: \"C54321\" },\r\n            { param_name: \"expt_delivery_time\", example: \"5:30 PM\" },\r\n            { param_name: \"order_details\", example: \"- 2 Loaves of Bread\\n- 1 Liter Milk\" },\r\n            { param_name: \"total_amount\", example: \"350\" }\r\n          ]\r\n        }\r\n      }\r\n    ]\r\n  },\r\n\r\n  // 2. B2C Grocery Order Out for Delivery\r\n  {\r\n    name: \"b2c_grocery_order_out_for_delivery_v2\",\r\n    language: \"en\",\r\n    category: \"UTILITY\",\r\n    allow_category_change: true,\r\n    parameter_format: \"NAMED\",\r\n    components: [\r\n      {\r\n        type: \"HEADER\",\r\n        format: \"TEXT\",\r\n        text: \"Your order is on the way!\"\r\n      },\r\n      {\r\n        type: \"BODY\",\r\n        text: \"Hi *{{customer_name}}*,\\n\\nYour order *#{{order_id}}* is out for delivery and should reach you by *{{delivery_time}}*. We’re looking forward to delighting you with our service!\\n\\nBest,\\n*{{business_name}}*\",\r\n        example: {\r\n          body_text_named_params: [\r\n            { param_name: \"customer_name\", example: \"Jane Doe\" },\r\n            { param_name: \"business_name\", example: \"MarketFresh\" },\r\n            { param_name: \"order_id\", example: \"C54321\" },\r\n            { param_name: \"delivery_time\", example: \"6:00 PM\" }\r\n          ]\r\n        }\r\n      }\r\n    ]\r\n  },\r\n\r\n  // 3. B2C Grocery Order Delivered (Standard – Payment Received)\r\n  {\r\n    name: \"b2c_grocery_order_delivered_v2\",\r\n    language: \"en\",\r\n    category: \"UTILITY\",\r\n    allow_category_change: true,\r\n    parameter_format: \"NAMED\",\r\n    components: [\r\n      {\r\n        type: \"HEADER\",\r\n        format: \"TEXT\",\r\n        text: \"Delivery complete – Enjoy your day!\"\r\n      },\r\n      {\r\n        type: \"BODY\",\r\n        text: \"Hi *{{customer_name}}*,\\n\\nGreat news – your order *#{{order_id}}* has been delivered. We hope everything is perfect!\\n\\n*Total Paid:* *₹{{paid_amount}}*\\n\\nThank you for choosing us. We value your trust!\\n\\nWarm wishes,\\n*{{business_name}}*\",\r\n        example: {\r\n          body_text_named_params: [\r\n            { param_name: \"customer_name\", example: \"Jane Doe\" },\r\n            { param_name: \"business_name\", example: \"MarketFresh\" },\r\n            { param_name: \"order_id\", example: \"C54321\" },\r\n            { param_name: \"paid_amount\", example: \"350\" }\r\n          ]\r\n        }\r\n      }\r\n    ]\r\n  },\r\n\r\n  // 3a. B2C Grocery Order Delivered with Credit (Payment to be repaid later)\r\n  {\r\n    name: \"b2c_grocery_order_delivered_credit_v2\",\r\n    language: \"en\",\r\n    category: \"UTILITY\",\r\n    allow_category_change: true,\r\n    parameter_format: \"NAMED\",\r\n    components: [\r\n      {\r\n        type: \"HEADER\",\r\n        format: \"TEXT\",\r\n        text: \"Order delivered – Payment on Credit\"\r\n      },\r\n      {\r\n        type: \"BODY\",\r\n        text: \"Hi *{{customer_name}}*,\\n\\nYour order *#{{order_id}}* has been delivered on credit. The order amount is *₹{{order_amount}}* and your total pending balance is now *₹{{total_pending_amount}}*.\\n\\nThank you for shopping with us.\\n\\nWarm regards,\\n*{{business_name}}*\",\r\n        example: {\r\n          body_text_named_params: [\r\n            { param_name: \"customer_name\", example: \"Jane Doe\" },\r\n            { param_name: \"business_name\", example: \"MarketFresh\" },\r\n            { param_name: \"order_id\", example: \"C54321\" },\r\n            { param_name: \"order_amount\", example: \"350\" },\r\n            { param_name: \"total_pending_amount\", example: \"100\" }\r\n          ]\r\n        }\r\n      }\r\n    ]\r\n  },\r\n\r\n  // 4. B2C Grocery Order Cancelled (with Refund, if applicable)\r\n  {\r\n    name: \"b2c_grocery_order_cancelled_v2\",\r\n    language: \"en\",\r\n    category: \"UTILITY\",\r\n    allow_category_change: true,\r\n    parameter_format: \"NAMED\",\r\n    components: [\r\n      {\r\n        type: \"HEADER\",\r\n        format: \"TEXT\",\r\n        text: \"Order cancelled\"\r\n      },\r\n      {\r\n        type: \"BODY\",\r\n        text: \"Hi *{{customer_name}}*,\\n\\nWe’re sorry, but your order *#{{order_id}}* has been cancelled due to *{{cancellation_reason}}*.\\n{{#if refund_amount}}A refund of *₹{{refund_amount}}* has been processed for any payment received.{{/if}}\\n\\nThank you for understanding,\\n*{{business_name}}*\",\r\n        example: {\r\n          body_text_named_params: [\r\n            { param_name: \"customer_name\", example: \"Jane Doe\" },\r\n            { param_name: \"business_name\", example: \"MarketFresh\" },\r\n            { param_name: \"order_id\", example: \"C54321\" },\r\n            { param_name: \"cancellation_reason\", example: \"unexpected delay\" },\r\n            { param_name: \"refund_amount\", example: \"350\" }\r\n          ]\r\n        }\r\n      }\r\n    ]\r\n  },\r\n\r\n  // 5. B2C Grocery Payment Successful\r\n  {\r\n    name: \"b2c_grocery_payment_success_v2\",\r\n    language: \"en\",\r\n    category: \"UTILITY\",\r\n    allow_category_change: true,\r\n    parameter_format: \"NAMED\",\r\n    components: [\r\n      {\r\n        type: \"HEADER\",\r\n        format: \"TEXT\",\r\n        text: \"Payment received – Thank you!\"\r\n      },\r\n      {\r\n        type: \"BODY\",\r\n        text: \"Hi *{{customer_name}}*,\\n\\nWe’ve received your payment of *₹{{paid_amount}}* for order *#{{order_id}}*. Your order is now being processed for delivery.\\n\\nThank you for shopping with us.\\n\\nBest,\\n*{{business_name}}*\",\r\n        example: {\r\n          body_text_named_params: [\r\n            { param_name: \"customer_name\", example: \"Jane Doe\" },\r\n            { param_name: \"business_name\", example: \"MarketFresh\" },\r\n            { param_name: \"order_id\", example: \"C54321\" },\r\n            { param_name: \"paid_amount\", example: \"350\" }\r\n          ]\r\n        }\r\n      }\r\n    ]\r\n  },\r\n\r\n  // 6. B2C Grocery Payment Failed\r\n  {\r\n    name: \"b2c_grocery_payment_failed_v2\",\r\n    language: \"en\",\r\n    category: \"UTILITY\",\r\n    allow_category_change: true,\r\n    parameter_format: \"NAMED\",\r\n    components: [\r\n      {\r\n        type: \"HEADER\",\r\n        format: \"TEXT\",\r\n        text: \"Payment issue – Lets fix it\"\r\n      },\r\n      {\r\n        type: \"BODY\",\r\n        text: \"Hi *{{customer_name}}*,\\n\\nWe couldn’t process the payment for order *#{{order_id}}* (attempted amount: *₹{{attempted_amount}}*). Kindly try again or let us know if you need any further help.\\n\\nThanks for your understanding,\\n*{{business_name}}*\",\r\n        example: {\r\n          body_text_named_params: [\r\n            { param_name: \"customer_name\", example: \"Jane Doe\" },\r\n            { param_name: \"business_name\", example: \"MarketFresh\" },\r\n            { param_name: \"order_id\", example: \"C54321\" },\r\n            { param_name: \"attempted_amount\", example: \"350\" }\r\n          ]\r\n        }\r\n      }\r\n    ]\r\n  },\r\n\r\n  // 7. B2C Grocery Prepaid but Order Unconfirmed (Cancelled & Refund Processed)\r\n  {\r\n    name: \"b2c_grocery_prepaid_unconfirmed_v2\",\r\n    language: \"en\",\r\n    category: \"UTILITY\",\r\n    allow_category_change: true,\r\n    parameter_format: \"NAMED\",\r\n    components: [\r\n      {\r\n        type: \"HEADER\",\r\n        format: \"TEXT\",\r\n        text: \"Order not confirmed – Cancelled\"\r\n      },\r\n      {\r\n        type: \"BODY\",\r\n        text: \"Hi *{{customer_name}}*,\\n\\nWe received your payment of *₹{{paid_amount}}* for order *#{{order_id}}*, but we couldn’t confirm the order due to *{{reason}}*. Consequently, the order has been cancelled{{#if refund_amount}} and a refund of *₹{{refund_amount}}* has been processed{{/if}}.\\n\\nThanks for your patience,\\n*{{business_name}}*\",\r\n        example: {\r\n          body_text_named_params: [\r\n            { param_name: \"customer_name\", example: \"Jane Doe\" },\r\n            { param_name: \"business_name\", example: \"MarketFresh\" },\r\n            { param_name: \"order_id\", example: \"C54321\" },\r\n            { param_name: \"paid_amount\", example: \"350\" },\r\n            { param_name: \"reason\", example: \"order volume\" },\r\n            { param_name: \"refund_amount\", example: \"350\" }\r\n          ]\r\n        }\r\n      }\r\n    ]\r\n  },\r\n\r\n  // 8. B2C Grocery Item Cancellation (with Refund, if applicable)\r\n  {\r\n    name: \"b2c_grocery_item_cancelled_v2\",\r\n    language: \"en\",\r\n    category: \"UTILITY\",\r\n    allow_category_change: true,\r\n    parameter_format: \"NAMED\",\r\n    components: [\r\n      {\r\n        type: \"HEADER\",\r\n        format: \"TEXT\",\r\n        text: \"Item cancellation notice\"\r\n      },\r\n      {\r\n        type: \"BODY\",\r\n        text: \"Hi *{{customer_name}}*,\\n\\nWe’re sorry to let you know that the item *{{item_name}}* in your order *#{{order_id}}* has been cancelled due to *{{cancel_reason}}*.\\n{{#if refund_amount}}A refund of *₹{{refund_amount}}* for this item has been processed.{{/if}}\\n\\nWe appreciate your understanding.\\n\\nRegards,\\n*{{business_name}}*\",\r\n        example: {\r\n          body_text_named_params: [\r\n            { param_name: \"customer_name\", example: \"Jane Doe\" },\r\n            { param_name: \"business_name\", example: \"MarketFresh\" },\r\n            { param_name: \"item_name\", example: \"Organic Bananas\" },\r\n            { param_name: \"order_id\", example: \"C54321\" },\r\n            { param_name: \"cancel_reason\", example: \"stock shortage\" },\r\n            { param_name: \"refund_amount\", example: \"150\" }\r\n          ]\r\n        }\r\n      }\r\n    ]\r\n  },\r\n\r\n  // 9. B2C Grocery Payment Refund (handled within cancellation scenarios)\r\n  // (Optional extra reminder message if needed; merge as desired)\r\n]\r\n\r\nexport const B2C_RESTAURANT_TEMPLATES = [\r\n\r\n  // ===================================================\r\n  // B2C Restaurant Templates\r\n  // ===================================================\r\n\r\n  // 1. B2C Restaurant Order Placed / Confirmed\r\n  {\r\n    name: \"b2c_restaurant_order_placed_confirmed_v2\",\r\n    language: \"en\",\r\n    category: \"UTILITY\",\r\n    allow_category_change: true,\r\n    parameter_format: \"NAMED\",\r\n    components: [\r\n      {\r\n        type: \"HEADER\",\r\n        format: \"TEXT\",\r\n        text: \"Order confirmed – Bon appetit!\"\r\n      },\r\n      {\r\n        type: \"BODY\",\r\n        text: \"Hi *{{customer_name}}* 👋,\\n\\nThanks so much for your order! 🙏 \\n  ✅  Your order *#{{order_id}}* has been confirmed and your delicious meal will be delivered in 🕒 *{{expt_delivery_time}} mins*.\\n\\n* 🍽 Meal Details:*\\n{{order_details}}\\n\\nTotal: *₹{{total_amount}}*\\n\\n 😋 We can’t wait for you to enjoy your meal.\\n\\nWarm regards,\\n*{{business_name}}*\",\r\n        example: {\r\n          body_text_named_params: [\r\n            { param_name: \"customer_name\", example: \"John Doe\" },\r\n            { param_name: \"business_name\", example: \"DineFine\" },\r\n            { param_name: \"order_id\", example: \"R12345\" },\r\n            { param_name: \"expt_delivery_time\", example: \"45\" },\r\n            { param_name: \"order_details\", example: \"- Caesar Salad \\n- Grilled Chicken\" },\r\n            { param_name: \"total_amount\", example: \"800\" }\r\n          ]\r\n        }\r\n      }\r\n    ]\r\n  },\r\n\r\n  // 2. B2C Restaurant Order Out for Delivery\r\n  {\r\n    name: \"b2c_restaurant_order_out_for_delivery_v2\",\r\n    language: \"en\",\r\n    category: \"UTILITY\",\r\n    allow_category_change: true,\r\n    parameter_format: \"NAMED\",\r\n    components: [\r\n      {\r\n        type: \"HEADER\",\r\n        format: \"TEXT\",\r\n        text: \"Your meal is on its way!\"\r\n      },\r\n      {\r\n        type: \"BODY\",\r\n        text: \"Hi *{{customer_name}}* 👋,\\n\\n 📦 Your order _#{{order_id}}_ is now out for delivery and should arrive in 🕢 *{{delivery_time}} mins*.\\n\\n 🍽️ We hope you enjoy your meal!\\n\\nCheers,\\n*{{business_name}}*\",\r\n        example: {\r\n          body_text_named_params: [\r\n            { param_name: \"customer_name\", example: \"John Doe\" },\r\n            { param_name: \"business_name\", example: \"DineFine\" },\r\n            { param_name: \"order_id\", example: \"R12345\" },\r\n            { param_name: \"delivery_time\", example: \"7:30 PM\" }\r\n          ]\r\n        }\r\n      }\r\n    ]\r\n  },\r\n\r\n  // 3. B2C Restaurant Order Delivered (Standard – Payment Received)\r\n  {\r\n    name: \"b2c_restaurant_order_delivered_v2\",\r\n    language: \"en\",\r\n    category: \"UTILITY\",\r\n    allow_category_change: true,\r\n    parameter_format: \"NAMED\",\r\n    components: [\r\n      {\r\n        type: \"HEADER\",\r\n        format: \"TEXT\",\r\n        text: \"Enjoy your meal – It’s delivered!\"\r\n      },\r\n      {\r\n        type: \"BODY\",\r\n        text: \"Hi *{{customer_name}}* 🎉,\\n\\n ✅ We’re delighted to let you know that your order _#{{order_id}}_ has been delivered 🚚. \\n 🍽️ We hope the food is delicious and meets your expectations!\\n\\n*Total Paid:* *₹{{paid_amount}}*\\n\\n 🙏 Thank you for choosing us. Bon appétit!\\n\\nBest,\\n*{{business_name}}*\",\r\n        example: {\r\n          body_text_named_params: [\r\n            { param_name: \"customer_name\", example: \"John Doe\" },\r\n            { param_name: \"business_name\", example: \"DineFine\" },\r\n            { param_name: \"order_id\", example: \"R12345\" },\r\n            { param_name: \"paid_amount\", example: \"800\" }\r\n          ]\r\n        }\r\n      }\r\n    ]\r\n  },\r\n\r\n  // 3a. B2C Restaurant Order Delivered with Credit (Payment to be repaid later)\r\n  {\r\n    name: \"b2c_restaurant_order_delivered_credit_v2\",\r\n    language: \"en\",\r\n    category: \"UTILITY\",\r\n    allow_category_change: true,\r\n    parameter_format: \"NAMED\",\r\n    components: [\r\n      {\r\n        type: \"HEADER\",\r\n        format: \"TEXT\",\r\n        text: \"Order delivered – Payment on Credit\"\r\n      },\r\n      {\r\n        type: \"BODY\",\r\n        text: \"Hi *{{customer_name}}* 🎉,\\n\\nYour order _#{{order_id}}_ has been delivered on credit ✅. The order amount is *₹{{order_amount}}* and your total pending balance is now *₹{{total_pending_amount}}*.\\n\\nWe appreciate your patronage and look forward to serving you again soon.\\n\\nBest,\\n*{{business_name}}*\",\r\n        example: {\r\n          body_text_named_params: [\r\n            { param_name: \"customer_name\", example: \"John Doe\" },\r\n            { param_name: \"business_name\", example: \"DineFine\" },\r\n            { param_name: \"order_id\", example: \"R12345\" },\r\n            { param_name: \"order_amount\", example: \"800\" },\r\n            { param_name: \"total_pending_amount\", example: \"200\" }\r\n          ]\r\n        }\r\n      }\r\n    ]\r\n  },\r\n\r\n  // 4. B2C Restaurant Order Cancelled (with Refund, if applicable)\r\n  {\r\n    name: \"b2c_restaurant_order_cancelled_v2\",\r\n    language: \"en\",\r\n    category: \"UTILITY\",\r\n    allow_category_change: true,\r\n    parameter_format: \"NAMED\",\r\n    components: [\r\n      {\r\n        type: \"HEADER\",\r\n        format: \"TEXT\",\r\n        text: \"Order cancelled\"\r\n      },\r\n      {\r\n        type: \"BODY\",\r\n        text: \"Hi *{{customer_name}}*,\\n\\nWe’re sorry to inform you that your order *#{{order_id}}* has been cancelled due to *{{cancellation_reason}}*.\\n{{#if refund_amount}}A refund of *₹{{refund_amount}}* has been processed for any payment received.{{/if}}\\n\\nWe apologize for the inconvenience and hope to serve you better next time.\\n\\nRegards,\\n*{{business_name}}*\",\r\n        example: {\r\n          body_text_named_params: [\r\n            { param_name: \"customer_name\", example: \"John Doe\" },\r\n            { param_name: \"business_name\", example: \"DineFine\" },\r\n            { param_name: \"order_id\", example: \"R12345\" },\r\n            { param_name: \"cancellation_reason\", example: \"kitchen delay\" },\r\n            { param_name: \"refund_amount\", example: \"800\" }\r\n          ]\r\n        }\r\n      }\r\n    ]\r\n  },\r\n\r\n  // 5. B2C Restaurant Payment Successful\r\n  {\r\n    name: \"b2c_restaurant_payment_success_v2\",\r\n    language: \"en\",\r\n    category: \"UTILITY\",\r\n    allow_category_change: true,\r\n    parameter_format: \"NAMED\",\r\n    components: [\r\n      {\r\n        type: \"HEADER\",\r\n        format: \"TEXT\",\r\n        text: \"Payment received – Thank you!\"\r\n      },\r\n      {\r\n        type: \"BODY\",\r\n        text: \"Hi *{{customer_name}}*,\\n\\n We’ve received your payment of *₹{{paid_amount}}* for order _*#{{order_id}}*_. Your meal is now being prepared and will soon be on its way to you.\\n\\nThank you for dining with us,\\n*{{business_name}}*\",\r\n        example: {\r\n          body_text_named_params: [\r\n            { param_name: \"customer_name\", example: \"John Doe\" },\r\n            { param_name: \"business_name\", example: \"DineFine\" },\r\n            { param_name: \"order_id\", example: \"R12345\" },\r\n            { param_name: \"paid_amount\", example: \"800\" }\r\n          ]\r\n        }\r\n      }\r\n    ]\r\n  },\r\n\r\n  // 6. B2C Restaurant Payment Failed\r\n  {\r\n    name: \"b2c_restaurant_payment_failed_v2\",\r\n    language: \"en\",\r\n    category: \"UTILITY\",\r\n    allow_category_change: true,\r\n    parameter_format: \"NAMED\",\r\n    components: [\r\n      {\r\n        type: \"HEADER\",\r\n        format: \"TEXT\",\r\n        text: \"Payment issue – Lets fix it\"\r\n      },\r\n      {\r\n        type: \"BODY\",\r\n        text: \"Hi *{{customer_name}}*,\\n\\n We couldn’t process the payment for order *#{{order_id}}* (attempted amount: *₹{{attempted_amount}}*). Kindly try again or let us know if you need any further help.\\n\\nThanks for your understanding,\\n*{{business_name}}*\",\r\n        example: {\r\n          body_text_named_params: [\r\n            { param_name: \"customer_name\", example: \"John Doe\" },\r\n            { param_name: \"business_name\", example: \"DineFine\" },\r\n            { param_name: \"order_id\", example: \"R12345\" },\r\n            { param_name: \"attempted_amount\", example: \"800\" }\r\n          ]\r\n        }\r\n      }\r\n    ]\r\n  },\r\n\r\n  // 7. B2C Restaurant Prepaid but Order Unconfirmed (Cancelled & Refund Processed)\r\n  {\r\n    name: \"b2c_restaurant_prepaid_unconfirmed_v2\",\r\n    language: \"en\",\r\n    category: \"UTILITY\",\r\n    allow_category_change: true,\r\n    parameter_format: \"NAMED\",\r\n    components: [\r\n      {\r\n        type: \"HEADER\",\r\n        format: \"TEXT\",\r\n        text: \"Order not confirmed – Cancelled\"\r\n      },\r\n      {\r\n        type: \"BODY\",\r\n        text: \"Hi *{{customer_name}}*,\\n\\nWe received your payment of *₹{{paid_amount}}* for order *#{{order_id}}*, but we couldn’t confirm the order due to *{{reason}}*. Consequently, the order has been cancelled{{#if refund_amount}} and a refund of *₹{{refund_amount}}* has been processed{{/if}}.\\n\\nThanks for your patience,\\n*{{business_name}}*\",\r\n        example: {\r\n          body_text_named_params: [\r\n            { param_name: \"customer_name\", example: \"John Doe\" },\r\n            { param_name: \"business_name\", example: \"DineFine\" },\r\n            { param_name: \"order_id\", example: \"R12345\" },\r\n            { param_name: \"paid_amount\", example: \"800\" },\r\n            { param_name: \"reason\", example: \"busy kitchen\" },\r\n            { param_name: \"refund_amount\", example: \"800\" }\r\n          ]\r\n        }\r\n      }\r\n    ]\r\n  }\r\n];", "// File: app/routes/home.templates.tsx\r\nimport {ActionFunction, json, type LoaderFunction} from \"@remix-run/node\";\r\nimport {use<PERSON><PERSON>cher, useLoaderData, useNavigate} from \"@remix-run/react\";\r\nimport { withAuth } from \"~/utils/auth-utils\";\r\nimport { getFirebaseAdmin } from \"~/services/firebase.server\";\r\nimport { Card, CardContent, CardHeader, CardTitle } from \"@components/ui/card\";\r\nimport { Button } from \"@components/ui/button\";\r\nimport { Badge } from \"@components/ui/badge\";\r\nimport {getWhatsAppTemplates, registerTemplate} from \"~/services/whatsappApi.server\";\r\nimport {WhatsAppConnectionData, WhatsAppTemplate} from \"~/types/whatsapp\";\r\nimport {B2B_TEMPLATES, B2C_RESTAURANT_TEMPLATES, B2C_TEMPLATES, REQUIRED_TEMPLATES} from \"~/constants/whatsappTemplates\";\r\nimport { getNetworkTheme } from \"~/services/netWorks\";\r\nimport { NetworkTheme } from \"~/types/api/common\";\r\n\r\ntype LoaderData = {\r\n    templates: WhatsAppTemplate[];\r\n    error?: string;\r\n    connectionData?: WhatsAppConnectionData;\r\n    networkConfig?: NetworkTheme | null;\r\n};\r\n\r\n// Function to get appropriate templates based on network type and ONDC domain\r\nconst getTemplatesForNetwork = (networkConfig: NetworkTheme | null) => {\r\n    if (!networkConfig) return REQUIRED_TEMPLATES;\r\n    \r\n    const { networkType, ondcDomain } = networkConfig;\r\n    \r\n    if (networkType === \"B2B\") {\r\n        return B2B_TEMPLATES;\r\n    } else if (networkType === \"B2C\") {\r\n        if (ondcDomain === \"RET11\") {\r\n            return B2C_RESTAURANT_TEMPLATES;\r\n        } else if (ondcDomain === \"RET10\") {\r\n            return B2C_TEMPLATES;\r\n        }\r\n    }\r\n    \r\n    return REQUIRED_TEMPLATES;\r\n};\r\n\r\nexport const loader: LoaderFunction = withAuth(async ({ user, request }) => {\r\n    if (!user) return json({ templates: [], error: \"User not authenticated\" });\r\n\r\n    const db = getFirebaseAdmin();\r\n    const doc = await db.collection(\"facebook-connects\")\r\n        .doc(user.userDetails.sellerId.toString())\r\n        .get();\r\n\r\n    if (!doc.exists) {\r\n        return json({ templates: [], error: \"WhatsApp connection not found\" });\r\n    }\r\n\r\n    const connectionData = doc.data() as WhatsAppConnectionData;\r\n\r\n    if (!connectionData.wabaId) {\r\n        return json({ templates: [], error: \"WhatsApp Business Account ID not found\" });\r\n    }\r\n\r\n    const response = await getWhatsAppTemplates(\r\n        connectionData.access.access_token,\r\n        connectionData.wabaId\r\n    );\r\n\r\n    if (response.error) {\r\n        return json({ templates: [], error: response.error });\r\n    }\r\n\r\n    let networkConfig: NetworkTheme | null = null;\r\n\r\n    try {\r\n        const networkConfigResponse = await getNetworkTheme(request);\r\n        networkConfig = networkConfigResponse.data || null;\r\n    } catch (error) {\r\n        console.log(error, \"error\");\r\n    }\r\n\r\n    return json({\r\n        templates: response.data,\r\n        connectionData,\r\n        networkConfig\r\n    });\r\n});\r\n\r\nexport const action: ActionFunction = withAuth(async ({ request, user }) => {\r\n    const formData = await request.formData();\r\n    const actionType = formData.get(\"actionType\");\r\n\r\n    if (actionType === \"register-templates\") {\r\n        const db = getFirebaseAdmin();\r\n        const doc = await db.collection(\"facebook-connects\")\r\n            .doc(user.userDetails.sellerId.toString())\r\n            .get();\r\n\r\n        if (!doc.exists) {\r\n            return json({ error: \"WhatsApp connection not found\" });\r\n        }\r\n\r\n        const connectionData = doc.data() as WhatsAppConnectionData;\r\n\r\n        if (!connectionData.wabaId) {\r\n            return json({ error: \"WhatsApp Business Account ID not found\" });\r\n        }\r\n\r\n        // Get network config to determine which templates to use\r\n        let networkConfig: NetworkTheme | null = null;\r\n        try {\r\n            const networkConfigResponse = await getNetworkTheme(request);\r\n            networkConfig = networkConfigResponse.data || null;\r\n        } catch (error) {\r\n            console.log(error, \"error\");\r\n        }\r\n\r\n        const templates = (await getWhatsAppTemplates(connectionData.access.access_token, connectionData.wabaId)).data || [];\r\n        \r\n        const approvedTemplateNames = new Set(\r\n            templates.filter(t => t.status === \"APPROVED\").map(t => t.name)\r\n        );\r\n\r\n        // Get the appropriate templates based on network type\r\n        const templatesForNetwork = getTemplatesForNetwork(networkConfig);\r\n        \r\n        const missingTemplates = templatesForNetwork.filter(\r\n            t => !approvedTemplateNames.has(t.name)\r\n        );\r\n\r\n        const results = await Promise.allSettled(\r\n            missingTemplates.map(template =>\r\n                registerTemplate(\r\n                    connectionData.access.access_token,\r\n                    connectionData.wabaId!,\r\n                    template\r\n                )\r\n            )\r\n        );\r\n\r\n        return json({ success: true, results });\r\n    }\r\n\r\n    return json({ error: \"Invalid action\" });\r\n});\r\n\r\nexport default function Templates() {\r\n    const { templates, error, networkConfig } = useLoaderData<LoaderData>();\r\n    const navigate = useNavigate();\r\n    const fetcher = useFetcher();\r\n\r\n    // Get appropriate templates based on network type\r\n    const templatesForNetwork = getTemplatesForNetwork(networkConfig || null);\r\n\r\n    // Consider both missing and rejected templates as needing registration\r\n    const approvedTemplateNames = new Set(\r\n        templates.filter(t => t.status === \"APPROVED\").map(t => t.name)\r\n    );\r\n    \r\n    const missingTemplates = templatesForNetwork.filter(\r\n        t => !approvedTemplateNames.has(t.name)\r\n    );\r\n\r\n    return (\r\n        <div className=\"p-6\">\r\n            <div className=\"max-w-3xl mx-auto\">\r\n                <div className=\"flex justify-between items-center mb-6\">\r\n                    <div>\r\n                        <h2 className=\"text-2xl font-bold\">WhatsApp Templates</h2>\r\n                        {missingTemplates.length > 0 && (\r\n                            <p className=\"text-yellow-600 mt-2\">\r\n                                Templates to register: {missingTemplates.length}\r\n                            </p>\r\n                        )}\r\n                        {networkConfig && (\r\n                            <p className=\"text-sm text-gray-500 mt-1\">\r\n                                Network: {networkConfig.networkType || \"Default\"}\r\n                                {networkConfig.ondcDomain && ` • Domain: ${networkConfig.ondcDomain}`}\r\n                            </p>\r\n                        )}\r\n                    </div>\r\n                    <div className=\"flex gap-2\">\r\n                        {missingTemplates.length > 0 && (\r\n                            <fetcher.Form method=\"post\">\r\n                                <input type=\"hidden\" name=\"actionType\" value=\"register-templates\"/>\r\n                                <Button\r\n                                    type=\"submit\"\r\n                                    disabled={fetcher.state !== \"idle\"}\r\n                                    className=\"bg-green-600 hover:bg-green-700\"\r\n                                >\r\n                                    {fetcher.state !== \"idle\" ? \"Registering...\" : \"Register Templates\"}\r\n                                </Button>\r\n                            </fetcher.Form>\r\n                        )}\r\n                        <Button\r\n                            variant=\"outline\"\r\n                            onClick={() => navigate(\"/home/<USER>\")}\r\n                        >\r\n                            Back to WhatsApp\r\n                        </Button>\r\n                    </div>\r\n                </div>\r\n\r\n                {error ? (\r\n                    <Card>\r\n                        <CardContent className=\"p-6\">\r\n                            <div className=\"text-center text-red-500\">\r\n                                {error}\r\n                            </div>\r\n                        </CardContent>\r\n                    </Card>\r\n                ) : templates.length === 0 ? (\r\n                    <Card>\r\n                        <CardContent className=\"p-6\">\r\n                            <div className=\"text-center text-gray-500\">\r\n                                No templates found\r\n                            </div>\r\n                        </CardContent>\r\n                    </Card>\r\n                ) : (\r\n                    <div className=\"space-y-4\">\r\n                        {templates.map((template) => (\r\n                            <Card key={template.id}>\r\n                                <CardHeader>\r\n                                    <div className=\"flex justify-between items-center flex-wrap gap-4\">\r\n                                        <CardTitle>{template.name}</CardTitle>\r\n                                        <div className=\"flex gap-2 items-center flex-wrap\">\r\n                                            <Badge className={\r\n                                                template.status === \"APPROVED\" ? \"bg-green-500\" :\r\n                                                    template.status === \"PENDING\" ? \"bg-yellow-500\" :\r\n                                                        template.status === \"REJECTED\" ? \"bg-red-500\" :\r\n                                                            \"bg-gray-500\"\r\n                                            }>\r\n                                                {template.status}\r\n                                            </Badge>\r\n                                            <Badge variant=\"outline\">{template.category}</Badge>\r\n                                            <Badge variant=\"outline\">{template.language}</Badge>\r\n                                        </div>\r\n                                    </div>\r\n                                </CardHeader>\r\n                                <CardContent>\r\n                                    {template.components.map((component, idx) => (\r\n                                        <div key={idx} className=\"mb-4 last:mb-0\">\r\n                                            <div className=\"text-sm text-gray-500 mb-1\">\r\n                                                {component.type} {component.format && `(${component.format})`}\r\n                                            </div>\r\n                                            <div className=\"text-gray-700\">{component.text}</div>\r\n                                        </div>\r\n                                    ))}\r\n                                </CardContent>\r\n                            </Card>\r\n                        ))}\r\n                    </div>\r\n                )}\r\n            </div>\r\n        </div>\r\n    );\r\n}\r\n"], "names": ["getTemplatesForNetwork", "networkConfig", "REQUIRED_TEMPLATES", "networkType", "ondcDomain", "B2B_TEMPLATES", "B2C_RESTAURANT_TEMPLATES", "B2C_TEMPLATES", "Templates", "templates", "error", "useLoaderData", "navigate", "useNavigate", "fetcher", "useFetcher", "templatesForNetwork", "approvedTemplateNames", "Set", "filter", "t", "status", "map", "name", "missingTemplates", "has", "className", "children", "jsxs", "jsx", "length", "Form", "method", "type", "value", "<PERSON><PERSON>", "disabled", "state", "variant", "onClick", "Card", "<PERSON><PERSON><PERSON><PERSON>", "template", "<PERSON><PERSON><PERSON><PERSON>", "CardTitle", "Badge", "category", "language", "components", "component", "idx", "format", "text", "id"], "mappings": ";;;;;;;;;;AACO,MAAM,qBAAqB;AAAA,EAChC;AAAA,IACE,MAAM;AAAA,IACN,UAAU;AAAA,IACV,UAAU;AAAA,IACV,YAAY;AAAA,MACV;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,UACP,WAAW;AAAA,YACT,CAAC,QAAQ,wCAAwC,QAAQ,KAAK;AAAA,UAAA;AAAA,QAChE;AAAA,MACF;AAAA,IACF;AAAA,EAEJ;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,UAAU;AAAA,IACV,UAAU;AAAA,IACV,YAAY;AAAA,MACV;AAAA,QACE,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,MAAM;AAAA,MACR;AAAA,MACA;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QACN,SAAS;AAAA,UACP,WAAW,CAAC,CAAC,OAAO,QAAQ,cAAc,WAAW,CAAC;AAAA,QAAA;AAAA,MACxD;AAAA,IACF;AAAA,EAEJ;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,UAAU;AAAA,IACV,UAAU;AAAA,IACV,YAAY;AAAA,MACV;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,UACP,WAAW,CAAC,CAAC,MAAM,CAAC;AAAA,QAAA;AAAA,MACtB;AAAA,IACF;AAAA,EAEJ;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,UAAU;AAAA,IACV,UAAU;AAAA,IACV,YAAY;AAAA,MACV;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,MAAA;AAAA,IACR;AAAA,EAEJ;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,UAAU;AAAA,IACV,UAAU;AAAA,IACV,YAAY;AAAA,MACV;AAAA,QACE,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,MAAM;AAAA,MACR;AAAA,MACA;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,MACA;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,MAAA;AAAA,IACR;AAAA,EAEJ;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,UAAU;AAAA,IACV,UAAU;AAAA,IACV,YAAY;AAAA,MACV;AAAA,QACE,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,MAAM;AAAA,MACR;AAAA,MACA;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,UACP,WAAW,CAAC,CAAC,QAAQ,UAAU,SAAS,CAAC;AAAA,QAAA;AAAA,MAC3C;AAAA,IACF;AAAA,EAEJ;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,UAAU;AAAA,IACV,UAAU;AAAA,IACV,YAAY;AAAA,MACV;AAAA,QACE,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,MAAM;AAAA,MACR;AAAA,MACA;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,UACP,WAAW,CAAC,CAAC,QAAQ,QAAQ,UAAU,SAAS,CAAC;AAAA,QAAA;AAAA,MACnD;AAAA,IACF;AAAA,EAEJ;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,UAAU;AAAA,IACV,UAAU;AAAA,IACV,YAAY;AAAA,MACV;AAAA,QACE,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,MAAM;AAAA,MACR;AAAA,MACA;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,UACP,WAAW,CAAC,CAAC,QAAQ,OAAO,CAAC;AAAA,QAAA;AAAA,MAC/B;AAAA,IACF;AAAA,EAEJ;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,UAAU;AAAA,IACV,UAAU;AAAA,IACV,YAAY;AAAA,MACV;AAAA,QACE,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,MAAM;AAAA,MACR;AAAA,MACA;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,UACP,WAAW,CAAC,CAAC,QAAQ,SAAS,SAAS,CAAC;AAAA,QAAA;AAAA,MAC1C;AAAA,IACF;AAAA,EAEJ;AAAA,EACA;AAAA,IACE,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,yBAAyB;AAAA,IACzB,oBAAoB;AAAA,IACpB,cAAc;AAAA,MACZ;AAAA,QACE,QAAQ;AAAA,QACR,UAAU;AAAA,QACV,QAAQ;AAAA,MACV;AAAA,MACA;AAAA,QACE,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,WAAW;AAAA,UACT,0BAA0B;AAAA,YACxB;AAAA,cACE,cAAc;AAAA,cACd,WAAW;AAAA,YACb;AAAA,YACA;AAAA,cACE,cAAc;AAAA,cACd,WAAW;AAAA,YACb;AAAA,YACA;AAAA,cACE,cAAc;AAAA,cACd,WAAW;AAAA,YAAA;AAAA,UACb;AAAA,QACF;AAAA,MAEJ;AAAA,MACA;AAAA,QACE,QAAQ;AAAA,QACR,QAAQ;AAAA,MACV;AAAA,MACA;AAAA,QACE,QAAQ;AAAA,QACR,WAAW;AAAA,UACT;AAAA,YACE,QAAQ;AAAA,YACR,QAAQ;AAAA,UACV;AAAA,UACA;AAAA,YACE,QAAQ;AAAA,YACR,QAAQ;AAAA,UAAA;AAAA,QACV;AAAA,MACF;AAAA,IACF;AAAA,EAEJ;AAAA,EACA;AAAA,IACE,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,yBAAyB;AAAA,IACzB,oBAAoB;AAAA,IACpB,cAAc;AAAA,MACZ;AAAA,QACE,QAAQ;AAAA,QACR,UAAU;AAAA,QACV,QAAQ;AAAA,QACR,WAAW;AAAA,UACT,4BAA4B;AAAA,YAC1B;AAAA,cACE,cAAc;AAAA,cACd,WAAW;AAAA,YAAA;AAAA,UACb;AAAA,QACF;AAAA,MAEJ;AAAA,MACA;AAAA,QACE,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,WAAW;AAAA,UACT,0BAA0B;AAAA,YACxB;AAAA,cACE,cAAc;AAAA,cACd,WAAW;AAAA,YACb;AAAA,YACA;AAAA,cACE,cAAc;AAAA,cACd,WAAW;AAAA,YACb;AAAA,YACA;AAAA,cACE,cAAc;AAAA,cACd,WAAW;AAAA,YAAA;AAAA,UACb;AAAA,QACF;AAAA,MAEJ;AAAA,MACA;AAAA,QACE,QAAQ;AAAA,QACR,QAAQ;AAAA,MACV;AAAA,MACA;AAAA,QACE,QAAQ;AAAA,QACR,WAAW;AAAA,UACT;AAAA,YACE,QAAQ;AAAA,YACR,QAAQ;AAAA,UACV;AAAA,UACA;AAAA,YACE,QAAQ;AAAA,YACR,QAAQ;AAAA,UAAA;AAAA,QACV;AAAA,MACF;AAAA,IACF;AAAA,EAEJ;AAAA,EACA;AAAA,IACE,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,yBAAyB;AAAA,IACzB,oBAAoB;AAAA,IACpB,cAAc;AAAA,MACZ;AAAA,QACE,QAAQ;AAAA,QACR,UAAU;AAAA,QACV,QAAQ;AAAA,MACV;AAAA,MACA;AAAA,QACE,QAAQ;AAAA,QACR,QAAQ;AAAA,MACV;AAAA,MACA;AAAA,QACE,QAAQ;AAAA,QACR,QAAQ;AAAA,MACV;AAAA,MACA;AAAA,QACE,QAAQ;AAAA,QACR,WAAW;AAAA,UACT;AAAA,YACE,QAAQ;AAAA,YACR,QAAQ;AAAA,UACV;AAAA,UACA;AAAA,YACE,QAAQ;AAAA,YACR,QAAQ;AAAA,UAAA;AAAA,QACV;AAAA,MACF;AAAA,IACF;AAAA,EAEJ;AAAA,EACA;AAAA,IACE,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,oBAAoB;AAAA,IACpB,cAAc;AAAA,MACV;AAAA,QACI,QAAQ;AAAA,QACR,UAAU;AAAA,QACV,WAAW;AAAA,UACP,iBAAiB;AAAA,YACb;AAAA,UAAA;AAAA,QACJ;AAAA,MAER;AAAA,MACA;AAAA,QACI,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,WAAW;AAAA,UACP,0BAA0B;AAAA,YACtB;AAAA,cACI,cAAc;AAAA,cACd,WAAW;AAAA,YAAA;AAAA,UACf;AAAA,QACJ;AAAA,MAER;AAAA,MACA;AAAA,QACI,QAAQ;AAAA,QACR,WAAW;AAAA,UACP;AAAA,YACI,QAAQ;AAAA,YACR,QAAQ;AAAA,UACZ;AAAA,UACA;AAAA,YACI,QAAQ;AAAA,YACR,QAAQ;AAAA,YACR,OAAO;AAAA,YACP,WAAW;AAAA,cACP;AAAA,YAAA;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AAEJ;AAGO,MAAM,gBAAgB;AAAA;AAAA;AAAA;AAAA;AAAA,EAK3B;AAAA,IACE,MAAM;AAAA,IACN,UAAU;AAAA,IACV,UAAU;AAAA,IACV,uBAAuB;AAAA,IACvB,kBAAkB;AAAA,IAClB,YAAY;AAAA,MACV;AAAA,QACE,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,MAAM;AAAA,MACR;AAAA,MACA;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,UACP,wBAAwB;AAAA,YACtB,EAAE,YAAY,iBAAiB,SAAS,YAAY;AAAA,YACpD,EAAE,YAAY,iBAAiB,SAAS,qBAAqB;AAAA,YAC7D,EAAE,YAAY,YAAY,SAAS,SAAS;AAAA,YAC5C,EAAE,YAAY,sBAAsB,SAAS,UAAU;AAAA,YACvD,EAAE,YAAY,iBAAiB,SAAS,2BAA2B;AAAA,YACnE,EAAE,YAAY,gBAAgB,SAAS,OAAO;AAAA,UAAA;AAAA,QAChD;AAAA,MACF;AAAA,IACF;AAAA,EAEJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAgCA;AAAA,IACE,MAAM;AAAA,IACN,UAAU;AAAA,IACV,UAAU;AAAA,IACV,uBAAuB;AAAA,IACvB,kBAAkB;AAAA,IAClB,YAAY;AAAA,MACV;AAAA,QACE,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,MAAM;AAAA,MACR;AAAA,MACA;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,UACP,wBAAwB;AAAA,YACtB,EAAE,YAAY,iBAAiB,SAAS,YAAY;AAAA,YACpD,EAAE,YAAY,iBAAiB,SAAS,qBAAqB;AAAA,YAC7D,EAAE,YAAY,YAAY,SAAS,SAAS;AAAA,YAC5C,EAAE,YAAY,iBAAiB,SAAS,UAAU;AAAA,UAAA;AAAA,QACpD;AAAA,MACF;AAAA,IACF;AAAA,EAEJ;AAAA;AAAA,EAGA;AAAA,IACE,MAAM;AAAA,IACN,UAAU;AAAA,IACV,UAAU;AAAA,IACV,uBAAuB;AAAA,IACvB,kBAAkB;AAAA,IAClB,YAAY;AAAA,MACV;AAAA,QACE,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,MAAM;AAAA,MACR;AAAA,MACA;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,UACP,wBAAwB;AAAA,YACtB,EAAE,YAAY,iBAAiB,SAAS,YAAY;AAAA,YACpD,EAAE,YAAY,iBAAiB,SAAS,qBAAqB;AAAA,YAC7D,EAAE,YAAY,YAAY,SAAS,SAAS;AAAA,YAC5C,EAAE,YAAY,eAAe,SAAS,OAAO;AAAA,UAAA;AAAA,QAC/C;AAAA,MACF;AAAA,IACF;AAAA,EAEJ;AAAA;AAAA,EAGA;AAAA,IACE,MAAM;AAAA,IACN,UAAU;AAAA,IACV,UAAU;AAAA,IACV,uBAAuB;AAAA,IACvB,kBAAkB;AAAA,IAClB,YAAY;AAAA,MACV;AAAA,QACE,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,MAAM;AAAA,MACR;AAAA,MACA;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,UACP,wBAAwB;AAAA,YACtB,EAAE,YAAY,iBAAiB,SAAS,YAAY;AAAA,YACpD,EAAE,YAAY,iBAAiB,SAAS,qBAAqB;AAAA,YAC7D,EAAE,YAAY,YAAY,SAAS,SAAS;AAAA,YAC5C,EAAE,YAAY,gBAAgB,SAAS,OAAO;AAAA,YAC9C,EAAE,YAAY,wBAAwB,SAAS,MAAM;AAAA,UAAA;AAAA,QACvD;AAAA,MACF;AAAA,IACF;AAAA,EAEJ;AAAA;AAAA,EAGA;AAAA,IACE,MAAM;AAAA,IACN,UAAU;AAAA,IACV,UAAU;AAAA,IACV,uBAAuB;AAAA,IACvB,kBAAkB;AAAA,IAClB,YAAY;AAAA,MACV;AAAA,QACE,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,MAAM;AAAA,MACR;AAAA,MACA;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,UACP,wBAAwB;AAAA,YACtB,EAAE,YAAY,iBAAiB,SAAS,YAAY;AAAA,YACpD,EAAE,YAAY,iBAAiB,SAAS,qBAAqB;AAAA,YAC7D,EAAE,YAAY,YAAY,SAAS,SAAS;AAAA,YAC5C,EAAE,YAAY,uBAAuB,SAAS,eAAe;AAAA,YAC7D,EAAE,YAAY,iBAAiB,SAAS,OAAO;AAAA,UAAA;AAAA,QACjD;AAAA,MACF;AAAA,IACF;AAAA,EAEJ;AAAA;AAAA,EAGA;AAAA,IACE,MAAM;AAAA,IACN,UAAU;AAAA,IACV,UAAU;AAAA,IACV,uBAAuB;AAAA,IACvB,kBAAkB;AAAA,IAClB,YAAY;AAAA,MACV;AAAA,QACE,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,MAAM;AAAA,MACR;AAAA,MACA;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,UACP,wBAAwB;AAAA,YACtB,EAAE,YAAY,iBAAiB,SAAS,YAAY;AAAA,YACpD,EAAE,YAAY,iBAAiB,SAAS,qBAAqB;AAAA,YAC7D,EAAE,YAAY,YAAY,SAAS,SAAS;AAAA,YAC5C,EAAE,YAAY,eAAe,SAAS,OAAO;AAAA,UAAA;AAAA,QAC/C;AAAA,MACF;AAAA,IACF;AAAA,EAEJ;AAAA;AAAA,EAGA;AAAA,IACE,MAAM;AAAA,IACN,UAAU;AAAA,IACV,UAAU;AAAA,IACV,uBAAuB;AAAA,IACvB,kBAAkB;AAAA,IAClB,YAAY;AAAA,MACV;AAAA,QACE,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,MAAM;AAAA,MACR;AAAA,MACA;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,UACP,wBAAwB;AAAA,YACtB,EAAE,YAAY,iBAAiB,SAAS,YAAY;AAAA,YACpD,EAAE,YAAY,iBAAiB,SAAS,qBAAqB;AAAA,YAC7D,EAAE,YAAY,YAAY,SAAS,SAAS;AAAA,YAC5C,EAAE,YAAY,oBAAoB,SAAS,OAAO;AAAA,UAAA;AAAA,QACpD;AAAA,MACF;AAAA,IACF;AAAA,EAEJ;AAAA;AAAA,EAGA;AAAA,IACE,MAAM;AAAA,IACN,UAAU;AAAA,IACV,UAAU;AAAA,IACV,uBAAuB;AAAA,IACvB,kBAAkB;AAAA,IAClB,YAAY;AAAA,MACV;AAAA,QACE,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,MAAM;AAAA,MACR;AAAA,MACA;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,UACP,wBAAwB;AAAA,YACtB,EAAE,YAAY,iBAAiB,SAAS,YAAY;AAAA,YACpD,EAAE,YAAY,iBAAiB,SAAS,qBAAqB;AAAA,YAC7D,EAAE,YAAY,YAAY,SAAS,SAAS;AAAA,YAC5C,EAAE,YAAY,eAAe,SAAS,OAAO;AAAA,YAC7C,EAAE,YAAY,UAAU,SAAS,mBAAmB;AAAA,YACpD,EAAE,YAAY,iBAAiB,SAAS,OAAO;AAAA,UAAA;AAAA,QACjD;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAEJ;AAEO,MAAM,gBAAgB;AAAA;AAAA;AAAA;AAAA;AAAA,EAM3B;AAAA,IACE,MAAM;AAAA,IACN,UAAU;AAAA,IACV,UAAU;AAAA,IACV,uBAAuB;AAAA,IACvB,kBAAkB;AAAA,IAClB,YAAY;AAAA,MACV;AAAA,QACE,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,MAAM;AAAA,MACR;AAAA,MACA;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,UACP,wBAAwB;AAAA,YACtB,EAAE,YAAY,iBAAiB,SAAS,WAAW;AAAA,YACnD,EAAE,YAAY,iBAAiB,SAAS,cAAc;AAAA,YACtD,EAAE,YAAY,YAAY,SAAS,SAAS;AAAA,YAC5C,EAAE,YAAY,sBAAsB,SAAS,UAAU;AAAA,YACvD,EAAE,YAAY,iBAAiB,SAAS,sCAAsC;AAAA,YAC9E,EAAE,YAAY,gBAAgB,SAAS,MAAM;AAAA,UAAA;AAAA,QAC/C;AAAA,MACF;AAAA,IACF;AAAA,EAEJ;AAAA;AAAA,EAGA;AAAA,IACE,MAAM;AAAA,IACN,UAAU;AAAA,IACV,UAAU;AAAA,IACV,uBAAuB;AAAA,IACvB,kBAAkB;AAAA,IAClB,YAAY;AAAA,MACV;AAAA,QACE,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,MAAM;AAAA,MACR;AAAA,MACA;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,UACP,wBAAwB;AAAA,YACtB,EAAE,YAAY,iBAAiB,SAAS,WAAW;AAAA,YACnD,EAAE,YAAY,iBAAiB,SAAS,cAAc;AAAA,YACtD,EAAE,YAAY,YAAY,SAAS,SAAS;AAAA,YAC5C,EAAE,YAAY,iBAAiB,SAAS,UAAU;AAAA,UAAA;AAAA,QACpD;AAAA,MACF;AAAA,IACF;AAAA,EAEJ;AAAA;AAAA,EAGA;AAAA,IACE,MAAM;AAAA,IACN,UAAU;AAAA,IACV,UAAU;AAAA,IACV,uBAAuB;AAAA,IACvB,kBAAkB;AAAA,IAClB,YAAY;AAAA,MACV;AAAA,QACE,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,MAAM;AAAA,MACR;AAAA,MACA;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,UACP,wBAAwB;AAAA,YACtB,EAAE,YAAY,iBAAiB,SAAS,WAAW;AAAA,YACnD,EAAE,YAAY,iBAAiB,SAAS,cAAc;AAAA,YACtD,EAAE,YAAY,YAAY,SAAS,SAAS;AAAA,YAC5C,EAAE,YAAY,eAAe,SAAS,MAAM;AAAA,UAAA;AAAA,QAC9C;AAAA,MACF;AAAA,IACF;AAAA,EAEJ;AAAA;AAAA,EAGA;AAAA,IACE,MAAM;AAAA,IACN,UAAU;AAAA,IACV,UAAU;AAAA,IACV,uBAAuB;AAAA,IACvB,kBAAkB;AAAA,IAClB,YAAY;AAAA,MACV;AAAA,QACE,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,MAAM;AAAA,MACR;AAAA,MACA;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,UACP,wBAAwB;AAAA,YACtB,EAAE,YAAY,iBAAiB,SAAS,WAAW;AAAA,YACnD,EAAE,YAAY,iBAAiB,SAAS,cAAc;AAAA,YACtD,EAAE,YAAY,YAAY,SAAS,SAAS;AAAA,YAC5C,EAAE,YAAY,gBAAgB,SAAS,MAAM;AAAA,YAC7C,EAAE,YAAY,wBAAwB,SAAS,MAAM;AAAA,UAAA;AAAA,QACvD;AAAA,MACF;AAAA,IACF;AAAA,EAEJ;AAAA;AAAA,EAGA;AAAA,IACE,MAAM;AAAA,IACN,UAAU;AAAA,IACV,UAAU;AAAA,IACV,uBAAuB;AAAA,IACvB,kBAAkB;AAAA,IAClB,YAAY;AAAA,MACV;AAAA,QACE,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,MAAM;AAAA,MACR;AAAA,MACA;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,UACP,wBAAwB;AAAA,YACtB,EAAE,YAAY,iBAAiB,SAAS,WAAW;AAAA,YACnD,EAAE,YAAY,iBAAiB,SAAS,cAAc;AAAA,YACtD,EAAE,YAAY,YAAY,SAAS,SAAS;AAAA,YAC5C,EAAE,YAAY,uBAAuB,SAAS,mBAAmB;AAAA,YACjE,EAAE,YAAY,iBAAiB,SAAS,MAAM;AAAA,UAAA;AAAA,QAChD;AAAA,MACF;AAAA,IACF;AAAA,EAEJ;AAAA;AAAA,EAGA;AAAA,IACE,MAAM;AAAA,IACN,UAAU;AAAA,IACV,UAAU;AAAA,IACV,uBAAuB;AAAA,IACvB,kBAAkB;AAAA,IAClB,YAAY;AAAA,MACV;AAAA,QACE,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,MAAM;AAAA,MACR;AAAA,MACA;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,UACP,wBAAwB;AAAA,YACtB,EAAE,YAAY,iBAAiB,SAAS,WAAW;AAAA,YACnD,EAAE,YAAY,iBAAiB,SAAS,cAAc;AAAA,YACtD,EAAE,YAAY,YAAY,SAAS,SAAS;AAAA,YAC5C,EAAE,YAAY,eAAe,SAAS,MAAM;AAAA,UAAA;AAAA,QAC9C;AAAA,MACF;AAAA,IACF;AAAA,EAEJ;AAAA;AAAA,EAGA;AAAA,IACE,MAAM;AAAA,IACN,UAAU;AAAA,IACV,UAAU;AAAA,IACV,uBAAuB;AAAA,IACvB,kBAAkB;AAAA,IAClB,YAAY;AAAA,MACV;AAAA,QACE,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,MAAM;AAAA,MACR;AAAA,MACA;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,UACP,wBAAwB;AAAA,YACtB,EAAE,YAAY,iBAAiB,SAAS,WAAW;AAAA,YACnD,EAAE,YAAY,iBAAiB,SAAS,cAAc;AAAA,YACtD,EAAE,YAAY,YAAY,SAAS,SAAS;AAAA,YAC5C,EAAE,YAAY,oBAAoB,SAAS,MAAM;AAAA,UAAA;AAAA,QACnD;AAAA,MACF;AAAA,IACF;AAAA,EAEJ;AAAA;AAAA,EAGA;AAAA,IACE,MAAM;AAAA,IACN,UAAU;AAAA,IACV,UAAU;AAAA,IACV,uBAAuB;AAAA,IACvB,kBAAkB;AAAA,IAClB,YAAY;AAAA,MACV;AAAA,QACE,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,MAAM;AAAA,MACR;AAAA,MACA;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,UACP,wBAAwB;AAAA,YACtB,EAAE,YAAY,iBAAiB,SAAS,WAAW;AAAA,YACnD,EAAE,YAAY,iBAAiB,SAAS,cAAc;AAAA,YACtD,EAAE,YAAY,YAAY,SAAS,SAAS;AAAA,YAC5C,EAAE,YAAY,eAAe,SAAS,MAAM;AAAA,YAC5C,EAAE,YAAY,UAAU,SAAS,eAAe;AAAA,YAChD,EAAE,YAAY,iBAAiB,SAAS,MAAM;AAAA,UAAA;AAAA,QAChD;AAAA,MACF;AAAA,IACF;AAAA,EAEJ;AAAA;AAAA,EAGA;AAAA,IACE,MAAM;AAAA,IACN,UAAU;AAAA,IACV,UAAU;AAAA,IACV,uBAAuB;AAAA,IACvB,kBAAkB;AAAA,IAClB,YAAY;AAAA,MACV;AAAA,QACE,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,MAAM;AAAA,MACR;AAAA,MACA;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,UACP,wBAAwB;AAAA,YACtB,EAAE,YAAY,iBAAiB,SAAS,WAAW;AAAA,YACnD,EAAE,YAAY,iBAAiB,SAAS,cAAc;AAAA,YACtD,EAAE,YAAY,aAAa,SAAS,kBAAkB;AAAA,YACtD,EAAE,YAAY,YAAY,SAAS,SAAS;AAAA,YAC5C,EAAE,YAAY,iBAAiB,SAAS,iBAAiB;AAAA,YACzD,EAAE,YAAY,iBAAiB,SAAS,MAAM;AAAA,UAAA;AAAA,QAChD;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAKJ;AAEO,MAAM,2BAA2B;AAAA;AAAA;AAAA;AAAA;AAAA,EAOtC;AAAA,IACE,MAAM;AAAA,IACN,UAAU;AAAA,IACV,UAAU;AAAA,IACV,uBAAuB;AAAA,IACvB,kBAAkB;AAAA,IAClB,YAAY;AAAA,MACV;AAAA,QACE,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,MAAM;AAAA,MACR;AAAA,MACA;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,UACP,wBAAwB;AAAA,YACtB,EAAE,YAAY,iBAAiB,SAAS,WAAW;AAAA,YACnD,EAAE,YAAY,iBAAiB,SAAS,WAAW;AAAA,YACnD,EAAE,YAAY,YAAY,SAAS,SAAS;AAAA,YAC5C,EAAE,YAAY,sBAAsB,SAAS,KAAK;AAAA,YAClD,EAAE,YAAY,iBAAiB,SAAS,qCAAqC;AAAA,YAC7E,EAAE,YAAY,gBAAgB,SAAS,MAAM;AAAA,UAAA;AAAA,QAC/C;AAAA,MACF;AAAA,IACF;AAAA,EAEJ;AAAA;AAAA,EAGA;AAAA,IACE,MAAM;AAAA,IACN,UAAU;AAAA,IACV,UAAU;AAAA,IACV,uBAAuB;AAAA,IACvB,kBAAkB;AAAA,IAClB,YAAY;AAAA,MACV;AAAA,QACE,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,MAAM;AAAA,MACR;AAAA,MACA;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,UACP,wBAAwB;AAAA,YACtB,EAAE,YAAY,iBAAiB,SAAS,WAAW;AAAA,YACnD,EAAE,YAAY,iBAAiB,SAAS,WAAW;AAAA,YACnD,EAAE,YAAY,YAAY,SAAS,SAAS;AAAA,YAC5C,EAAE,YAAY,iBAAiB,SAAS,UAAU;AAAA,UAAA;AAAA,QACpD;AAAA,MACF;AAAA,IACF;AAAA,EAEJ;AAAA;AAAA,EAGA;AAAA,IACE,MAAM;AAAA,IACN,UAAU;AAAA,IACV,UAAU;AAAA,IACV,uBAAuB;AAAA,IACvB,kBAAkB;AAAA,IAClB,YAAY;AAAA,MACV;AAAA,QACE,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,MAAM;AAAA,MACR;AAAA,MACA;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,UACP,wBAAwB;AAAA,YACtB,EAAE,YAAY,iBAAiB,SAAS,WAAW;AAAA,YACnD,EAAE,YAAY,iBAAiB,SAAS,WAAW;AAAA,YACnD,EAAE,YAAY,YAAY,SAAS,SAAS;AAAA,YAC5C,EAAE,YAAY,eAAe,SAAS,MAAM;AAAA,UAAA;AAAA,QAC9C;AAAA,MACF;AAAA,IACF;AAAA,EAEJ;AAAA;AAAA,EAGA;AAAA,IACE,MAAM;AAAA,IACN,UAAU;AAAA,IACV,UAAU;AAAA,IACV,uBAAuB;AAAA,IACvB,kBAAkB;AAAA,IAClB,YAAY;AAAA,MACV;AAAA,QACE,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,MAAM;AAAA,MACR;AAAA,MACA;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,UACP,wBAAwB;AAAA,YACtB,EAAE,YAAY,iBAAiB,SAAS,WAAW;AAAA,YACnD,EAAE,YAAY,iBAAiB,SAAS,WAAW;AAAA,YACnD,EAAE,YAAY,YAAY,SAAS,SAAS;AAAA,YAC5C,EAAE,YAAY,gBAAgB,SAAS,MAAM;AAAA,YAC7C,EAAE,YAAY,wBAAwB,SAAS,MAAM;AAAA,UAAA;AAAA,QACvD;AAAA,MACF;AAAA,IACF;AAAA,EAEJ;AAAA;AAAA,EAGA;AAAA,IACE,MAAM;AAAA,IACN,UAAU;AAAA,IACV,UAAU;AAAA,IACV,uBAAuB;AAAA,IACvB,kBAAkB;AAAA,IAClB,YAAY;AAAA,MACV;AAAA,QACE,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,MAAM;AAAA,MACR;AAAA,MACA;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,UACP,wBAAwB;AAAA,YACtB,EAAE,YAAY,iBAAiB,SAAS,WAAW;AAAA,YACnD,EAAE,YAAY,iBAAiB,SAAS,WAAW;AAAA,YACnD,EAAE,YAAY,YAAY,SAAS,SAAS;AAAA,YAC5C,EAAE,YAAY,uBAAuB,SAAS,gBAAgB;AAAA,YAC9D,EAAE,YAAY,iBAAiB,SAAS,MAAM;AAAA,UAAA;AAAA,QAChD;AAAA,MACF;AAAA,IACF;AAAA,EAEJ;AAAA;AAAA,EAGA;AAAA,IACE,MAAM;AAAA,IACN,UAAU;AAAA,IACV,UAAU;AAAA,IACV,uBAAuB;AAAA,IACvB,kBAAkB;AAAA,IAClB,YAAY;AAAA,MACV;AAAA,QACE,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,MAAM;AAAA,MACR;AAAA,MACA;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,UACP,wBAAwB;AAAA,YACtB,EAAE,YAAY,iBAAiB,SAAS,WAAW;AAAA,YACnD,EAAE,YAAY,iBAAiB,SAAS,WAAW;AAAA,YACnD,EAAE,YAAY,YAAY,SAAS,SAAS;AAAA,YAC5C,EAAE,YAAY,eAAe,SAAS,MAAM;AAAA,UAAA;AAAA,QAC9C;AAAA,MACF;AAAA,IACF;AAAA,EAEJ;AAAA;AAAA,EAGA;AAAA,IACE,MAAM;AAAA,IACN,UAAU;AAAA,IACV,UAAU;AAAA,IACV,uBAAuB;AAAA,IACvB,kBAAkB;AAAA,IAClB,YAAY;AAAA,MACV;AAAA,QACE,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,MAAM;AAAA,MACR;AAAA,MACA;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,UACP,wBAAwB;AAAA,YACtB,EAAE,YAAY,iBAAiB,SAAS,WAAW;AAAA,YACnD,EAAE,YAAY,iBAAiB,SAAS,WAAW;AAAA,YACnD,EAAE,YAAY,YAAY,SAAS,SAAS;AAAA,YAC5C,EAAE,YAAY,oBAAoB,SAAS,MAAM;AAAA,UAAA;AAAA,QACnD;AAAA,MACF;AAAA,IACF;AAAA,EAEJ;AAAA;AAAA,EAGA;AAAA,IACE,MAAM;AAAA,IACN,UAAU;AAAA,IACV,UAAU;AAAA,IACV,uBAAuB;AAAA,IACvB,kBAAkB;AAAA,IAClB,YAAY;AAAA,MACV;AAAA,QACE,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,MAAM;AAAA,MACR;AAAA,MACA;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,UACP,wBAAwB;AAAA,YACtB,EAAE,YAAY,iBAAiB,SAAS,WAAW;AAAA,YACnD,EAAE,YAAY,iBAAiB,SAAS,WAAW;AAAA,YACnD,EAAE,YAAY,YAAY,SAAS,SAAS;AAAA,YAC5C,EAAE,YAAY,eAAe,SAAS,MAAM;AAAA,YAC5C,EAAE,YAAY,UAAU,SAAS,eAAe;AAAA,YAChD,EAAE,YAAY,iBAAiB,SAAS,MAAM;AAAA,UAAA;AAAA,QAChD;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAEJ;AC1kCA,MAAMA,yBAA0BC,mBAAuC;AAC/D,MAAA,CAACA,cAAsB,QAAAC;AAErB,QAAA;AAAA,IAAEC;AAAAA,IAAaC;AAAAA,EAAW,IAAIH;AAEpC,MAAIE,gBAAgB,OAAO;AAChB,WAAAE;AAAAA,EACX,WAAWF,gBAAgB,OAAO;AAC9B,QAAIC,eAAe,SAAS;AACjB,aAAAE;AAAAA,IACX,WAAWF,eAAe,SAAS;AACxB,aAAAG;AAAAA,IACX;AAAA,EACJ;AAEO,SAAAL;AACX;AAuGA,SAAwBM,YAAY;AAChC,QAAM;AAAA,IAAEC;AAAAA,IAAWC;AAAAA,IAAOT;AAAAA,MAAkBU,cAA0B;AACtE,QAAMC,WAAWC,YAAY;AAC7B,QAAMC,UAAUC,WAAW;AAGrB,QAAAC,sBAAsBhB,uBAAuBC,iBAAiB,IAAI;AAGxE,QAAMgB,wBAAwB,IAAIC,IAC9BT,UAAUU,OAAOC,OAAKA,EAAEC,WAAW,UAAU,EAAEC,IAASF,OAAAA,EAAEG,IAAI,CAClE;AAEA,QAAMC,mBAAmBR,oBAAoBG,OACpCC,OAAA,CAACH,sBAAsBQ,IAAIL,EAAEG,IAAI,CAC1C;AAEA,+CACK,OAAI;AAAA,IAAAG,WAAU;AAAA,IACXC,UAACC,kCAAA,KAAA,OAAA;AAAA,MAAIF,WAAU;AAAA,MACXC,UAAA,CAACC,kCAAA,KAAA,OAAA;AAAA,QAAIF,WAAU;AAAA,QACXC,UAAA,CAAAC,kCAAA,KAAC,OACG;AAAA,UAAAD,UAAA,CAACE,kCAAA,IAAA,MAAA;AAAA,YAAGH,WAAU;AAAA,YAAqBC,UAAkB;AAAA,UAAA,CAAA,GACpDH,iBAAiBM,SAAS,KACtBF,kCAAAA,KAAA,KAAA;AAAA,YAAEF,WAAU;AAAA,YAAuBC,UAAA,CAAA,2BACRH,iBAAiBM,MAAA;AAAA,WAC7C,GAEH7B,iBACG2B,kCAAA,KAAC,KAAE;AAAA,YAAAF,WAAU;AAAA,YAA6BC,UAAA,CAAA,aAC5B1B,cAAcE,eAAe,WACtCF,cAAcG,cAAc,cAAcH,cAAcG,UAAU,EAAA;AAAA,UACvE,CAAA,CAAA;AAAA,QAER,CAAA,GACAwB,kCAAA,KAAC,OAAI;AAAA,UAAAF,WAAU;AAAA,UACVC,UAAA,CAAAH,iBAAiBM,SAAS,KACvBF,kCAAA,KAACd,QAAQiB,MAAR;AAAA,YAAaC,QAAO;AAAA,YACjBL,UAAA,CAAAE,kCAAA,IAAC;cAAMI,MAAK;AAAA,cAASV,MAAK;AAAA,cAAaW,OAAM;AAAA,YAAoB,CAAA,GACjEL,kCAAA,IAACM,QAAA;AAAA,cACGF,MAAK;AAAA,cACLG,UAAUtB,QAAQuB,UAAU;AAAA,cAC5BX,WAAU;AAAA,cAETC,UAAAb,QAAQuB,UAAU,SAAS,mBAAmB;AAAA,YAAA,CACnD,CAAA;AAAA,UACJ,CAAA,GAEJR,kCAAA,IAACM,QAAA;AAAA,YACGG,SAAQ;AAAA,YACRC,SAASA,MAAM3B,SAAS,uBAAuB;AAAA,YAClDe,UAAA;AAAA,UAAA,CAED,CAAA;AAAA,QACJ,CAAA,CAAA;AAAA,OACJ,GAECjB,QACImB,kCAAA,IAAAW,MAAA;AAAA,QACGb,gDAACc,aAAY;AAAA,UAAAf,WAAU;AAAA,UACnBC,UAACE,kCAAA,IAAA,OAAA;AAAA,YAAIH,WAAU;AAAA,YACVC;UACL,CAAA;AAAA,QACJ,CAAA;AAAA,MACJ,CAAA,IACAlB,UAAUqB,WAAW,IACpBD,kCAAAA,IAAAW,MAAA;AAAA,QACGb,UAACE,kCAAA,IAAAY,aAAA;AAAA,UAAYf,WAAU;AAAA,UACnBC,UAAAE,kCAAA,IAAC;YAAIH,WAAU;AAAA,YAA4BC;UAE3C,CAAA;AAAA,QACJ,CAAA;AAAA,MAAA,CACJ,IAECE,kCAAA,IAAA,OAAA;AAAA,QAAIH,WAAU;AAAA,QACVC,UAAAlB,UAAUa,IAAKoB,qDACXF,MACG;AAAA,UAAAb,UAAA,CAAAE,kCAAA,IAACc,YACG;AAAA,YAAAhB,UAAAC,kCAAA,KAAC,OAAI;AAAA,cAAAF,WAAU;AAAA,cACXC,UAAA,CAACE,kCAAA,IAAAe,WAAA;AAAA,gBAAWjB,mBAASJ;AAAAA,cAAK,CAAA,GAC1BK,kCAAA,KAAC,OAAI;AAAA,gBAAAF,WAAU;AAAA,gBACXC,UAAA,CAAAE,kCAAA,IAACgB;kBAAMnB,WACHgB,SAASrB,WAAW,aAAa,iBAC7BqB,SAASrB,WAAW,YAAY,kBAC5BqB,SAASrB,WAAW,aAAa,eAC7B;AAAA,kBAEXM,mBAASN;AAAAA,gBACd,CAAA,GACCQ,kCAAA,IAAAgB,OAAA;AAAA,kBAAMP,SAAQ;AAAA,kBAAWX,mBAASmB;AAAAA,gBAAS,CAAA,GAC3CjB,kCAAA,IAAAgB,OAAA;AAAA,kBAAMP,SAAQ;AAAA,kBAAWX,mBAASoB;AAAAA,gBAAS,CAAA,CAAA;AAAA,cAChD,CAAA,CAAA;AAAA,YACJ,CAAA;AAAA,UACJ,CAAA,GACAlB,kCAAA,IAACY,aACI;AAAA,YAAAd,UAAAe,SAASM,WAAW1B,IAAI,CAAC2B,WAAWC,QACjCtB,kCAAA,KAAC,OAAc;AAAA,cAAAF,WAAU;AAAA,cACrBC,UAAA,CAACC,kCAAA,KAAA,OAAA;AAAA,gBAAIF,WAAU;AAAA,gBACVC,UAAA,CAAUsB,UAAAhB,MAAK,KAAEgB,UAAUE,UAAU,IAAIF,UAAUE,MAAM,GAAA;AAAA,cAC9D,CAAA,GACCtB,kCAAA,IAAA,OAAA;AAAA,gBAAIH,WAAU;AAAA,gBAAiBC,oBAAUyB;AAAAA,cAAK,CAAA,CAAA;AAAA,YAJzC,GAAAF,GAKV,CACH;AAAA,UACL,CAAA,CAAA;AAAA,QA3BO,GAAAR,SAASW,EA4BpB,CACH;AAAA,MACL,CAAA,CAAA;AAAA,IAER,CAAA;AAAA,EACJ,CAAA;AAER;"}