{"version": 3, "file": "home.salesAnalysis-ByFKRHm0.js", "sources": ["../../../node_modules/lucide-react/dist/esm/icons/phone-call.js", "../../../app/components/ui/chart.tsx", "../../../app/components/ui/salesAnalysisDashBoard.tsx", "../../../app/components/ui/salesDynamicSearchFilters.tsx", "../../../app/components/ui/salesSearchFilters.tsx", "../../../app/routes/home.salesAnalysis.tsx"], "sourcesContent": ["/**\n * @license lucide-react v0.453.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst PhoneCall = createLucideIcon(\"PhoneCall\", [\n  [\n    \"path\",\n    {\n      d: \"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z\",\n      key: \"foiqr5\"\n    }\n  ],\n  [\"path\", { d: \"M14.05 2a9 9 0 0 1 8 7.94\", key: \"vmijpz\" }],\n  [\"path\", { d: \"M14.05 6A5 5 0 0 1 18 10\", key: \"13nbpp\" }]\n]);\n\nexport { PhoneCall as default };\n//# sourceMappingURL=phone-call.js.map\n", "import * as React from \"react\"\r\nimport * as RechartsPrimitive from \"recharts\"\r\n\r\nimport { cn } from \"~/lib/utils\"\r\n\r\n// Format: { THEME_NAME: CSS_SELECTOR }\r\nconst THEMES = { light: \"\", dark: \".dark\" } as const\r\n\r\nexport type ChartConfig = {\r\n  [k in string]: {\r\n    label?: React.ReactNode\r\n    icon?: React.ComponentType\r\n  } & (\r\n    | { color?: string; theme?: never }\r\n    | { color?: never; theme: Record<keyof typeof THEMES, string> }\r\n  )\r\n}\r\n\r\ntype ChartContextProps = {\r\n  config: ChartConfig\r\n}\r\n\r\nconst ChartContext = React.createContext<ChartContextProps | null>(null)\r\n\r\nfunction useChart() {\r\n  const context = React.useContext(ChartContext)\r\n\r\n  if (!context) {\r\n    throw new Error(\"useChart must be used within a <ChartContainer />\")\r\n  }\r\n\r\n  return context\r\n}\r\n\r\nconst ChartContainer = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.ComponentProps<\"div\"> & {\r\n    config: ChartConfig\r\n    children: React.ComponentProps<\r\n      typeof RechartsPrimitive.ResponsiveContainer\r\n    >[\"children\"]\r\n  }\r\n>(({ id, className, children, config, ...props }, ref) => {\r\n  const uniqueId = React.useId()\r\n  const chartId = `chart-${id || uniqueId.replace(/:/g, \"\")}`\r\n\r\n  return (\r\n    <ChartContext.Provider value={{ config }}>\r\n      <div\r\n        data-chart={chartId}\r\n        ref={ref}\r\n        className={cn(\r\n          \"flex aspect-video justify-center text-xs [&_.recharts-cartesian-axis-tick_text]:fill-muted-foreground [&_.recharts-cartesian-grid_line[stroke='#ccc']]:stroke-border/50 [&_.recharts-curve.recharts-tooltip-cursor]:stroke-border [&_.recharts-dot[stroke='#fff']]:stroke-transparent [&_.recharts-layer]:outline-none [&_.recharts-polar-grid_[stroke='#ccc']]:stroke-border [&_.recharts-radial-bar-background-sector]:fill-muted [&_.recharts-rectangle.recharts-tooltip-cursor]:fill-muted [&_.recharts-reference-line_[stroke='#ccc']]:stroke-border [&_.recharts-sector[stroke='#fff']]:stroke-transparent [&_.recharts-sector]:outline-none [&_.recharts-surface]:outline-none\",\r\n          className\r\n        )}\r\n        {...props}\r\n      >\r\n        <ChartStyle id={chartId} config={config} />\r\n        <RechartsPrimitive.ResponsiveContainer>\r\n          {children}\r\n        </RechartsPrimitive.ResponsiveContainer>\r\n      </div>\r\n    </ChartContext.Provider>\r\n  )\r\n})\r\nChartContainer.displayName = \"Chart\"\r\n\r\nconst ChartStyle = ({ id, config }: { id: string; config: ChartConfig }) => {\r\n  const colorConfig = Object.entries(config).filter(\r\n    ([_, config]) => config.theme || config.color\r\n  )\r\n\r\n  if (!colorConfig.length) {\r\n    return null\r\n  }\r\n\r\n  return (\r\n    <style\r\n      dangerouslySetInnerHTML={{\r\n        __html: Object.entries(THEMES)\r\n          .map(\r\n            ([theme, prefix]) => `\r\n${prefix} [data-chart=${id}] {\r\n${colorConfig\r\n  .map(([key, itemConfig]) => {\r\n    const color =\r\n      itemConfig.theme?.[theme as keyof typeof itemConfig.theme] ||\r\n      itemConfig.color\r\n    return color ? `  --color-${key}: ${color};` : null\r\n  })\r\n  .join(\"\\n\")}\r\n}\r\n`\r\n          )\r\n          .join(\"\\n\"),\r\n      }}\r\n    />\r\n  )\r\n}\r\n\r\nconst ChartTooltip = RechartsPrimitive.Tooltip\r\n\r\nconst ChartTooltipContent = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.ComponentProps<typeof RechartsPrimitive.Tooltip> &\r\n    React.ComponentProps<\"div\"> & {\r\n      hideLabel?: boolean\r\n      hideIndicator?: boolean\r\n      indicator?: \"line\" | \"dot\" | \"dashed\"\r\n      nameKey?: string\r\n      labelKey?: string\r\n    }\r\n>(\r\n  (\r\n    {\r\n      active,\r\n      payload,\r\n      className,\r\n      indicator = \"dot\",\r\n      hideLabel = false,\r\n      hideIndicator = false,\r\n      label,\r\n      labelFormatter,\r\n      labelClassName,\r\n      formatter,\r\n      color,\r\n      nameKey,\r\n      labelKey,\r\n    },\r\n    ref\r\n  ) => {\r\n    const { config } = useChart()\r\n\r\n    const tooltipLabel = React.useMemo(() => {\r\n      if (hideLabel || !payload?.length) {\r\n        return null\r\n      }\r\n\r\n      const [item] = payload\r\n      const key = `${labelKey || item.dataKey || item.name || \"value\"}`\r\n      const itemConfig = getPayloadConfigFromPayload(config, item, key)\r\n      const value =\r\n        !labelKey && typeof label === \"string\"\r\n          ? config[label as keyof typeof config]?.label || label\r\n          : itemConfig?.label\r\n\r\n      if (labelFormatter) {\r\n        return (\r\n          <div className={cn(\"font-medium\", labelClassName)}>\r\n            {labelFormatter(value, payload)}\r\n          </div>\r\n        )\r\n      }\r\n\r\n      if (!value) {\r\n        return null\r\n      }\r\n\r\n      return <div className={cn(\"font-medium\", labelClassName)}>{value}</div>\r\n    }, [\r\n      label,\r\n      labelFormatter,\r\n      payload,\r\n      hideLabel,\r\n      labelClassName,\r\n      config,\r\n      labelKey,\r\n    ])\r\n\r\n    if (!active || !payload?.length) {\r\n      return null\r\n    }\r\n\r\n    const nestLabel = payload.length === 1 && indicator !== \"dot\"\r\n\r\n    return (\r\n      <div\r\n        ref={ref}\r\n        className={cn(\r\n          \"grid min-w-[8rem] items-start gap-1.5 rounded-lg border border-border/50 bg-background px-2.5 py-1.5 text-xs shadow-xl\",\r\n          className\r\n        )}\r\n      >\r\n        {!nestLabel ? tooltipLabel : null}\r\n        <div className=\"grid gap-1.5\">\r\n          {payload.map((item, index) => {\r\n            const key = `${nameKey || item.name || item.dataKey || \"value\"}`\r\n            const itemConfig = getPayloadConfigFromPayload(config, item, key)\r\n            const indicatorColor = color || item.payload.fill || item.color\r\n\r\n            return (\r\n              <div\r\n                key={item.dataKey}\r\n                className={cn(\r\n                  \"flex w-full flex-wrap items-stretch gap-2 [&>svg]:h-2.5 [&>svg]:w-2.5 [&>svg]:text-muted-foreground\",\r\n                  indicator === \"dot\" && \"items-center\"\r\n                )}\r\n              >\r\n                {formatter && item?.value !== undefined && item.name ? (\r\n                  formatter(item.value, item.name, item, index, item.payload)\r\n                ) : (\r\n                  <>\r\n                    {itemConfig?.icon ? (\r\n                      <itemConfig.icon />\r\n                    ) : (\r\n                      !hideIndicator && (\r\n                        <div\r\n                          className={cn(\r\n                            \"shrink-0 rounded-[2px] border-[--color-border] bg-[--color-bg]\",\r\n                            {\r\n                              \"h-2.5 w-2.5\": indicator === \"dot\",\r\n                              \"w-1\": indicator === \"line\",\r\n                              \"w-0 border-[1.5px] border-dashed bg-transparent\":\r\n                                indicator === \"dashed\",\r\n                              \"my-0.5\": nestLabel && indicator === \"dashed\",\r\n                            }\r\n                          )}\r\n                          style={\r\n                            {\r\n                              \"--color-bg\": indicatorColor,\r\n                              \"--color-border\": indicatorColor,\r\n                            } as React.CSSProperties\r\n                          }\r\n                        />\r\n                      )\r\n                    )}\r\n                    <div\r\n                      className={cn(\r\n                        \"flex flex-1 justify-between leading-none\",\r\n                        nestLabel ? \"items-end\" : \"items-center\"\r\n                      )}\r\n                    >\r\n                      <div className=\"grid gap-1.5\">\r\n                        {nestLabel ? tooltipLabel : null}\r\n                        <span className=\"text-muted-foreground\">\r\n                          {itemConfig?.label || item.name}\r\n                        </span>\r\n                      </div>\r\n                      {item.value && (\r\n                        <span className=\"font-mono font-medium tabular-nums text-foreground\">\r\n                          {item.value.toLocaleString()}\r\n                        </span>\r\n                      )}\r\n                    </div>\r\n                  </>\r\n                )}\r\n              </div>\r\n            )\r\n          })}\r\n        </div>\r\n      </div>\r\n    )\r\n  }\r\n)\r\nChartTooltipContent.displayName = \"ChartTooltip\"\r\n\r\nconst ChartLegend = RechartsPrimitive.Legend\r\n\r\nconst ChartLegendContent = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.ComponentProps<\"div\"> &\r\n    Pick<RechartsPrimitive.LegendProps, \"payload\" | \"verticalAlign\"> & {\r\n      hideIcon?: boolean\r\n      nameKey?: string\r\n    }\r\n>(\r\n  (\r\n    { className, hideIcon = false, payload, verticalAlign = \"bottom\", nameKey },\r\n    ref\r\n  ) => {\r\n    const { config } = useChart()\r\n\r\n    if (!payload?.length) {\r\n      return null\r\n    }\r\n\r\n    return (\r\n      <div\r\n        ref={ref}\r\n        className={cn(\r\n          \"flex items-center justify-center gap-4\",\r\n          verticalAlign === \"top\" ? \"pb-3\" : \"pt-3\",\r\n          className\r\n        )}\r\n      >\r\n        {payload.map((item) => {\r\n          const key = `${nameKey || item.dataKey || \"value\"}`\r\n          const itemConfig = getPayloadConfigFromPayload(config, item, key)\r\n\r\n          return (\r\n            <div\r\n              key={item.value}\r\n              className={cn(\r\n                \"flex items-center gap-1.5 [&>svg]:h-3 [&>svg]:w-3 [&>svg]:text-muted-foreground\"\r\n              )}\r\n            >\r\n              {itemConfig?.icon && !hideIcon ? (\r\n                <itemConfig.icon />\r\n              ) : (\r\n                <div\r\n                  className=\"h-2 w-2 shrink-0 rounded-[2px]\"\r\n                  style={{\r\n                    backgroundColor: item.color,\r\n                  }}\r\n                />\r\n              )}\r\n              {itemConfig?.label}\r\n            </div>\r\n          )\r\n        })}\r\n      </div>\r\n    )\r\n  }\r\n)\r\nChartLegendContent.displayName = \"ChartLegend\"\r\n\r\n// Helper to extract item config from a payload.\r\nfunction getPayloadConfigFromPayload(\r\n  config: ChartConfig,\r\n  payload: unknown,\r\n  key: string\r\n) {\r\n  if (typeof payload !== \"object\" || payload === null) {\r\n    return undefined\r\n  }\r\n\r\n  const payloadPayload =\r\n    \"payload\" in payload &&\r\n    typeof payload.payload === \"object\" &&\r\n    payload.payload !== null\r\n      ? payload.payload\r\n      : undefined\r\n\r\n  let configLabelKey: string = key\r\n\r\n  if (\r\n    key in payload &&\r\n    typeof payload[key as keyof typeof payload] === \"string\"\r\n  ) {\r\n    configLabelKey = payload[key as keyof typeof payload] as string\r\n  } else if (\r\n    payloadPayload &&\r\n    key in payloadPayload &&\r\n    typeof payloadPayload[key as keyof typeof payloadPayload] === \"string\"\r\n  ) {\r\n    configLabelKey = payloadPayload[\r\n      key as keyof typeof payloadPayload\r\n    ] as string\r\n  }\r\n\r\n  return configLabelKey in config\r\n    ? config[configLabelKey]\r\n    : config[key as keyof typeof config]\r\n}\r\n\r\nexport {\r\n  ChartContainer,\r\n  ChartTooltip,\r\n  ChartTooltipContent,\r\n  ChartLegend,\r\n  ChartLegendContent,\r\n  ChartStyle,\r\n}\r\n", "\"use client\"\r\n\r\nimport { TrendingUp } from \"lucide-react\"\r\nimport { <PERSON>, <PERSON>hart, CartesianGrid, XAxis, YAxis } from \"recharts\"\r\nimport { ChartConfig, ChartContainer, ChartLegend, ChartLegendContent, ChartTooltip, ChartTooltipContent } from \"./chart\"\r\nimport { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from \"./card\"\r\nimport { BcSalesDashboardDto } from \"~/types/api/businessConsoleService/SalesAnalysis\"\r\nimport { format } from \"date-fns\"\r\nimport { useState } from \"react\"\r\n\r\n\r\n\r\ninterface SalesAnalysisDashBoardProps {\r\n      salesData: BcSalesDashboardDto\r\n\r\n}\r\n\r\n\r\n\r\nconst chartConfig = {\r\n      desktop: {\r\n            label: \"Desktop\",\r\n            color: \"hsl(120, 90%, 50%)\",\r\n      },\r\n      mobile: {\r\n            label: \"Mobile\",\r\n            color: \"hsl(120, 90%, 50%)\",\r\n      },\r\n} satisfies ChartConfig\r\n\r\nconst generateColor = (index: number) => `hsl(${(index * 137) % 360}, 90%, 50%)`\r\n\r\nexport function SalesAnalysisDashBoard({ salesData }: SalesAnalysisDashBoardProps) {\r\n\r\n\r\n      console.log(salesData.totalSales, \"676776767676776676767867868\")\r\n\r\n      console.log(\" Sales Data:\", salesData);\r\n\r\n\r\n      console.log(\"Total Sales Data:\", salesData?.totalSales);\r\n      // // Extract unique dates from rows\r\n      // const uniqueDates = Array.from(\r\n      //       new Set(\r\n      //           salesData?.rows?.flatMap(row =>\r\n      //               row?.cols?.map(col => col?.deliveryDate) || []\r\n      //           ) || []\r\n      //       )\r\n      //   ).sort();\r\n\r\n      // Map data to get total sales per date\r\n\r\n\r\n\r\n\r\n      const uniqueDates = Array.from(\r\n            new Set(salesData?.totalSales?.cols?.map(col => col.deliveryDate) || [])\r\n      ).sort();\r\n\r\n\r\n\r\n      let barComponents;\r\n      const chartData = uniqueDates.map(date => {\r\n            const totalSalesQty = salesData?.totalSales?.cols?.reduce((acc, col) => {\r\n                  return col.deliveryDate === date ? acc + (col.salesQty || 0) : acc;\r\n            }, 0);\r\n            return { date, salesQty: totalSalesQty.toFixed(2) };\r\n      });\r\n\r\n      barComponents = (\r\n            <>\r\n                  <Bar\r\n                        dataKey=\"salesQty\"\r\n                        stackId=\"a\"\r\n                        fill=\"hsl(210, 90%, 50%)\"\r\n                        radius={[0, 0, 0, 0]}\r\n                        label={{ position: 'top', fill: '#000' }}\r\n                        barSize={chartData.length === 1 ? 30 : undefined} // Fix size only when 1 data point\r\n\r\n                  />\r\n\r\n            </>\r\n      );\r\n      return (\r\n            <Card className=\"mt-8\">\r\n                  <CardHeader>\r\n                        <CardTitle>Sales Analysis Chart</CardTitle>\r\n                        {/* <CardDescription>January - June 2024</CardDescription> */}\r\n                  </CardHeader>\r\n                  <CardContent>\r\n                        <ChartContainer config={chartConfig}>\r\n\r\n                              <BarChart accessibilityLayer data={chartData} >\r\n\r\n                                    <CartesianGrid vertical={false} />\r\n                                    <XAxis\r\n                                          dataKey=\"date\"\r\n                                          tickLine={false}\r\n                                          tickMargin={10}\r\n                                          axisLine={false}\r\n                                          tickFormatter={(value) => format(new Date(value), \"EEE dd/MM\")}\r\n                                    />\r\n                                    <YAxis />\r\n\r\n                                    <ChartTooltip content={<ChartTooltipContent hideLabel />} />\r\n                                    <ChartLegend content={<ChartLegendContent />} />\r\n                                    {barComponents}\r\n\r\n                                    <ChartTooltip\r\n                                          cursor={false}\r\n                                          content={<ChartTooltipContent indicator=\"dashed\" />}\r\n                                    />\r\n                              </BarChart>\r\n                        </ChartContainer>\r\n\r\n                  </CardContent>\r\n\r\n            </Card>\r\n      )\r\n}\r\n\r\n\r\n\r\n\r\n", "import { useState, useEffect } from \"react\";\r\nimport { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON>eader, DialogTitle } from \"./dialog\";\r\nimport { Input } from \"./input\";\r\nimport { But<PERSON> } from \"./button\";\r\nimport { SearchItems } from \"~/types/api/businessConsoleService/SalesAnalysis\";\r\nimport { useFetcher } from \"@remix-run/react\";\r\nimport { ActionData } from \"~/routes/home.salesAnalysis\";\r\n\r\n\r\n\r\n\r\ninterface ModalProps {\r\n      title: string;\r\n      isOpen: boolean;\r\n      onClose: () => void;\r\n      onSelect: (sales: SearchItems) => void;\r\n      itemList: SearchItems[];\r\n      type: string;\r\n}\r\n\r\nexport default function SalesDynamicSearchFilters({\r\n      title,\r\n      isOpen,\r\n      onClose,\r\n      onSelect,\r\n      itemList,\r\n      type\r\n}: ModalProps) {\r\n      const [search, setSearch] = useState(\"\");\r\n      const [selectedSeller, setSelectedSeller] = useState<SearchItems | null>(null);\r\n      const [items, setItems] = useState<SearchItems[]>(itemList);\r\n      const [page, setPage] = useState(0);\r\n      const [totalPages, setTotalPages] = useState(100);\r\n      const itemFetcher = useFetcher<ActionData>();\r\n\r\n      const handleGetItems = () => {\r\n            if (search.length < 3 && page === 0) return;\r\n\r\n            const formData = new FormData();\r\n            formData.append(\"type\", type);\r\n            formData.append(\"intent\", type);\r\n            formData.append(\"pageNo\", page.toString());\r\n            formData.append(\"size\", \"10\");\r\n            formData.append(\"matchBy\", search);\r\n\r\n            itemFetcher.submit(formData, {\r\n                  method: \"POST\",\r\n                  encType: \"application/x-www-form-urlencoded\"\r\n            });\r\n      };\r\n\r\n      useEffect(() => {\r\n            if (search.length >= 3 || page > 0) {\r\n                  handleGetItems();\r\n            }\r\n      }, [search, page]);\r\n\r\n      useEffect(() => {\r\n            if (!search) {\r\n                  setItems(itemList);\r\n            } else if (itemFetcher?.data?.selectedData) {\r\n                  setItems(itemFetcher.data.selectedData);\r\n            }\r\n      }, [search, itemFetcher.data, itemList]);\r\n\r\n      const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n            setSearch(e.target.value);\r\n            setPage(0);\r\n            if (e.target.value.length >= 3) {\r\n                  handleGetItems();\r\n            }\r\n      };\r\n\r\n      console.log(items, \"45454544455\")\r\n      const nextPage = () => {\r\n            setPage((prev) => (prev < totalPages ? prev + 1 : prev));\r\n            handleGetItems();\r\n      };\r\n\r\n      const prevPage = () => {\r\n            setPage((prev) => (prev > 1 ? prev - 1 : prev));\r\n            handleGetItems();\r\n      };\r\n\r\n      const handleAddFilter = () => {\r\n            if (selectedSeller) {\r\n                  onSelect(selectedSeller);\r\n            }\r\n            onClose();\r\n      };\r\n\r\n      // useEffect(() => {\r\n      //       if (itemFetcher.data?.selectedData) {\r\n      //             console.log(\"Updating Items:\", itemFetcher.data.selectedData);\r\n      //             setItems(itemFetcher.data.selectedData); // Ensure fetched data updates items list\r\n      //       }\r\n      // }, [itemFetcher.data?.selectedData]);\r\n      return (\r\n            <Dialog open={isOpen} onOpenChange={onClose}>\r\n                  <DialogContent className=\"max-w-md p-6 bg-white rounded-lg shadow-lg\">\r\n                        <DialogHeader>\r\n                              <DialogTitle className=\"text-lg font-semibold\">{title}</DialogTitle>\r\n                        </DialogHeader>\r\n\r\n                        <Input\r\n                              placeholder=\"Search...\"\r\n                              value={search}\r\n                              onChange={handleSearchChange}\r\n                              className=\"w-full mt-2 border rounded-md p-2\"\r\n                        />\r\n\r\n                        {itemFetcher.state !== \"idle\" ? (\r\n                              <p className=\"text-center text-gray-500 mt-4\">Loading...</p>\r\n                        ) : (\r\n                              <div className=\"max-h-48 overflow-y-auto mt-4 space-y-2\">\r\n                                    {items.length > 0 ? (\r\n                                          items.map((item) => (\r\n                                                <label key={item.id} className=\"flex items-center space-x-2 cursor-pointer p-2 bg-gray-100 rounded-md hover:bg-gray-200\">\r\n                                                      <input\r\n                                                            type=\"radio\"\r\n                                                            name=\"singleSelect\"\r\n                                                            checked={selectedSeller?.id === item.id}\r\n                                                            onChange={() => setSelectedSeller(item)}\r\n                                                            className=\"form-radio h-5 w-5 text-blue-500\"\r\n                                                      />\r\n                                                      <span>{item.name}</span>\r\n                                                </label>\r\n                                          ))\r\n                                    ) : (\r\n                                          <p className=\"text-gray-500 text-center\">No results found.</p>\r\n                                    )}\r\n                              </div>\r\n                        )}\r\n\r\n                        <div className=\"flex justify-between items-center mt-4\">\r\n                              <Button variant=\"outline\" onClick={prevPage} disabled={page === 1}>\r\n                                    Prev\r\n                              </Button>\r\n                              <span className=\"text-gray-600\">Page {page} of {totalPages}</span>\r\n                              <Button variant=\"outline\" onClick={nextPage} disabled={page === totalPages}>\r\n                                    Next\r\n                              </Button>\r\n                        </div>\r\n\r\n                        <DialogFooter className=\"flex justify-end mt-4\">\r\n                              <Button variant=\"outline\" onClick={onClose}>\r\n                                    Close\r\n                              </Button>\r\n                              <Button className=\"ml-2\" onClick={handleAddFilter} disabled={!selectedSeller}>\r\n                                    + Add Filter\r\n                              </Button>\r\n                        </DialogFooter>\r\n                  </DialogContent>\r\n            </Dialog>\r\n      );\r\n}\r\n", "import { useState } from \"react\";\r\nimport { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogTitle } from \"./dialog\";\r\nimport { Input } from \"./input\";\r\nimport { Button } from \"./button\";\r\nimport { SearchItems } from \"~/types/api/businessConsoleService/SalesAnalysis\";\r\n\r\n\r\ninterface ModalProps {\r\n      title: string;\r\n      isOpen: boolean;\r\n      onClose: () => void;\r\n      items: any;\r\n      onSelect: (seller: SearchItems) => void;\r\n}\r\nexport default function SalesSearchFilters({ title, isOpen, onClose, items, onSelect }: ModalProps) {\r\n\r\n      const [search, setSearch] = useState(\"\");\r\n\r\n      const filteredItems = items?.filter((item: any) =>\r\n            item?.name?.toLowerCase().includes(search.toLowerCase())\r\n\r\n      );\r\n      const [selectedSeller, setSelectedSeller] = useState<{ id: number; name: string } | null>(null);\r\n\r\n\r\n\r\n      const handleAddFilter = () => {\r\n            if (selectedSeller) {\r\n                  onSelect(selectedSeller);\r\n            }\r\n            onClose();\r\n      };\r\n\r\n      return (\r\n            <Dialog open={isOpen} onOpenChange={onClose}>\r\n                  <DialogContent className=\"max-w-md p-6 bg-white rounded-lg shadow-lg\">\r\n                        <DialogHeader>\r\n                              <DialogTitle className=\"text-lg font-semibold\">{title}</DialogTitle>\r\n                        </DialogHeader>\r\n\r\n                        <Input\r\n                              placeholder=\"Search...\"\r\n                              value={search}\r\n                              onChange={(e) => setSearch(e.target.value)}\r\n                              className=\"w-full mt-2 border rounded-md p-2\"\r\n                        />\r\n\r\n                        <div className=\"max-h-48 overflow-y-auto mt-4 space-y-2\">\r\n                              {filteredItems.length > 0 ? (\r\n                                    filteredItems?.map((item: any) => (\r\n                                          <label\r\n                                                key={item.id}\r\n                                                className=\"flex items-center space-x-2 cursor-pointer p-2 bg-gray-100 rounded-md hover:bg-gray-200\"\r\n                                          >\r\n                                                <input\r\n                                                      type=\"radio\"\r\n                                                      name=\"singleSelect\" // Ensures only one can be selected\r\n                                                      checked={selectedSeller?.id === item.id}\r\n                                                      onChange={() => setSelectedSeller({ id: item.id, name: item.name })}\r\n                                                      className=\"form-radio h-5 w-5 text-blue-500\"\r\n                                                />\r\n                                                <span>{item.name}</span>\r\n                                          </label>\r\n                                    ))\r\n                              ) : (\r\n                                    <p className=\"text-gray-500 text-center\">No results found.</p>\r\n                              )}\r\n                        </div>\r\n\r\n\r\n                        <DialogFooter className=\"flex justify-end mt-4\">\r\n                              <Button variant=\"outline\" onClick={onClose}>Close</Button>\r\n                              <Button className=\"ml-2\" onClick={handleAddFilter}>+ Add Filter</Button>\r\n                        </DialogFooter>\r\n\r\n                  </DialogContent>\r\n            </Dialog>\r\n      );\r\n}\r\n", "import { ActionFunction, LoaderFunction } from \"@remix-run/node\";\r\nimport { use<PERSON><PERSON>cher, use<PERSON>oaderD<PERSON>, useNavigate, useSearchParams } from \"@remix-run/react\";\r\nimport { format, isValid } from \"date-fns\";\r\nimport { CalendarIcon, CircleX, PhoneCall, Search, Store } from \"lucide-react\";\r\nimport { useEffect, useMemo, useState } from \"react\";\r\nimport { Button } from \"~/components/ui/button\";\r\nimport { Calendar } from \"~/components/ui/calendar\"; import { Popover, PopoverContent, PopoverTrigger } from \"~/components/ui/popover\";\r\nimport ResponsivePagination from \"~/components/ui/responsivePagination\";\r\nimport { ResponsiveTable } from \"~/components/ui/responsiveTable\";\r\nimport { SalesAnalysisDashBoard } from \"~/components/ui/salesAnalysisDashBoard\";\r\nimport SalesDynamicSearchFilters from \"~/components/ui/salesDynamicSearchFilters\";\r\nimport SalesSearchFilters from \"~/components/ui/salesSearchFilters\";\r\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from \"~/components/ui/select\";\r\nimport { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from \"~/components/ui/tooltip\";\r\nimport { getNetWorkSeller } from \"~/services/netWorks\";\r\nimport { getSalesAnalysis, getSearchFilters } from \"~/services/tripsSummary\";\r\nimport { NetWorkDetails } from \"~/types/api/businessConsoleService/netWorkinfo\";\r\nimport { BcSalesDashboardDto, SearchItems } from \"~/types/api/businessConsoleService/SalesAnalysis\"\r\nimport { withAuth, withResponse } from \"~/utils/auth-utils\";\r\n\r\nexport interface ActionData {\r\n      selectedData: SearchItems[],\r\n      sellerList: NetWorkDetails[],\r\n\r\n}\r\n\r\ninterface LoaderData {\r\n      salesAnalysis: BcSalesDashboardDto;\r\n      selectedDate: string,\r\n      dataTypeWise: string,\r\n      selectedDays: number,\r\n      selectedSellerId: number,\r\n      selectedAgentId: number,\r\n      selectedBuyerId: number,\r\n      selectedLocalityId: number,\r\n      matchBy: string,\r\n      pageNo: number,\r\n      pageSize: number\r\n}\r\nexport const loader: LoaderFunction = withAuth(async ({ request }) => {\r\n      const url = new URL(request.url);\r\n      const date =\r\n            url.searchParams.get(\"date\") ||\r\n            new Date(Date.now() + 24 * 60 * 60 * 1000)?.toISOString()?.split(\"T\")[0];\r\n      const dataType = url.searchParams.get(\"dataType\") as string || \"sellerwise\";\r\n      const selectedDays = Number(url.searchParams.get(\"days\") || \"8\");\r\n      const selectedSellerId = Number(url.searchParams.get(\"sellerId\")) || null;\r\n      const selectedAgentId = Number(url.searchParams.get(\"agentId\")) || null;\r\n      const selectedBuyerId = Number(url.searchParams.get(\"buyerId\")) || null;\r\n      const selectedLocalityId = Number(url.searchParams.get(\"areaId\")) || null;\r\n      const matchBy = url.searchParams.get(\"matchBy\") || \"\";\r\n      const pageNo = Number(url.searchParams.get(\"pageNo\")) || 0;\r\n      const pageSize = Number(url.searchParams.get(\"pageSize\")) || 50;\r\n      const dDate = url.searchParams.get(\"dDate\") || \"\"\r\n\r\n\r\n      try {\r\n            let response = await getSalesAnalysis(date, dataType, selectedDays, selectedSellerId, selectedAgentId, selectedBuyerId, selectedLocalityId, matchBy, pageNo, pageSize, dDate, request)\r\n            return withResponse({\r\n                  salesAnalysis: response?.data,\r\n                  selectedDate: date,\r\n                  dataTypeWise: dataType,\r\n                  selectedDays: selectedDays,\r\n                  selectedSellerId: selectedSellerId,\r\n                  selectedAgentId: selectedAgentId,\r\n                  selectedBuyerId: selectedBuyerId,\r\n                  selectedLocalityId: selectedLocalityId\r\n\r\n\r\n            }, response?.headers)\r\n      } catch (error) {\r\n            console.error(\"Seller sales error:\", error);\r\n            throw new Response(\"Failed to fetch seller sales\", { status: 500 });\r\n      }\r\n});\r\n\r\nexport const action: ActionFunction = async ({ request }) => {\r\n      const formData = await request.formData();\r\n      const intent = formData.get(\"intent\") as string;\r\n      const type = formData.get(\"type\") as string\r\n      const pageNo = Number(formData.get(\"pageNo\")) || 0;\r\n      const pageSize = Number(formData.get(\"size\")) || 50;\r\n      const matchBy = formData.get(\"matchBy\") as string;\r\n      const action = formData.get(\"action\") as string;\r\n\r\n\r\n\r\n\r\n      if (action === \"sellerWise\") {\r\n            try {\r\n                  const response = await getNetWorkSeller(1,\r\n                        request\r\n                  );\r\n\r\n                  return withResponse({\r\n                        sellerList: response?.data,\r\n\r\n\r\n\r\n                  }, response?.headers);\r\n\r\n\r\n            }\r\n            catch (error) {\r\n                  throw new Response(\"Failed to fetch get Sellers\", { status: 500 });\r\n\r\n            }\r\n      }\r\n      if (intent === type) {\r\n            try {\r\n                  const response = await getSearchFilters(\r\n                        type,\r\n                        matchBy,\r\n                        pageNo,\r\n                        pageSize,\r\n                        request\r\n                  );\r\n\r\n                  return withResponse({\r\n                        selectedData: response?.data,\r\n\r\n\r\n\r\n                  }, response?.headers);\r\n\r\n\r\n            }\r\n            catch (error) {\r\n                  throw new Response(\"Failed to fetch seller sales\", { status: 500 });\r\n\r\n            }\r\n      }\r\n\r\n};\r\n\r\n\r\nexport default function SalesAnalysis() {\r\n\r\n      const { salesAnalysis, selectedDate, dataTypeWise, selectedDays, selectedSellerId, selectedAgentId, selectedBuyerId, selectedLocalityId, pageNo, pageSize } = useLoaderData<LoaderData>()\r\n      const [searchParams] = useSearchParams();\r\n      const searchParamsDataType = useMemo(() => searchParams.get(\"dataType\") ?? dataTypeWise, [searchParams, dataTypeWise]);\r\n      const searchParamsDays = useMemo(() => searchParams.get(\"days\") ?? selectedDays?.toString(), [searchParams, selectedDays]);\r\n      const searchParamsDate = useMemo(() => searchParams.get(\"date\") ?? selectedDate, [searchParams, selectedDate]);\r\n      const searchParamsDDate = searchParams.get(\"dDate\")\r\n\r\n      const searchParamsSellerId = Number(searchParams.get(\"sellerId\") ?? selectedSellerId);\r\n      const searchParamsAgentId = Number(searchParams.get(\"agentId\") ?? selectedAgentId);\r\n      const searchParamsBuyerId = Number(searchParams.get(\"buyerId\") ?? selectedBuyerId);\r\n      const searchParamsAreaId = Number(searchParams.get(\"areaId\") ?? selectedLocalityId);\r\n      const searchParamsPageNo = Number(searchParams.get(\"pageNo\") ?? pageNo);\r\n      const searchParamsPageSize = Number(searchParams.get(\"pageSize\") ?? pageSize);\r\n      // const location = useLocation();\r\n      const selectedSellerName = searchParams.get(\"sellerName\") || \"\";\r\n      const selectedAgentName = searchParams.get(\"agentName\") || \"\";\r\n      const selectedBuyerName = searchParams.get(\"buyerName\") || \"\";\r\n      const selectedAreaName = searchParams.get(\"areaName\") || \"\";\r\n\r\n      const [dataType, setDataType] = useState(searchParamsDataType);\r\n      useEffect(() => {\r\n            if (dataType !== searchParamsDataType) setDataType(searchParamsDataType);\r\n            if (days !== searchParamsDays) setDays(searchParamsDays);\r\n            if (date.toISOString().split(\"T\")[0] !== searchParamsDate) {\r\n                  setDate(new Date(searchParamsDate));\r\n            }\r\n            if (searchParamsDDate) {\r\n                  const formattedDDate = new Date(searchParamsDDate).toISOString().split(\"T\")[0];\r\n\r\n                  if (formattedDDate !== dDate?.toISOString().split(\"T\")[0]) {\r\n                        setDDate(new Date(searchParamsDDate)); // Only update if the date is actually different\r\n                  }\r\n            }\r\n\r\n            if (selectedSellerData.id !== searchParamsSellerId) {\r\n                  setSelectedSellerData({ id: searchParamsSellerId, name: selectedSellerName });\r\n            }\r\n            if (selectedAgentData.id !== searchParamsAgentId) {\r\n                  setSelectedAgentData({ id: searchParamsAgentId, name: selectedAgentName });\r\n            }\r\n            if (selectedBuyerData.id !== searchParamsBuyerId) {\r\n                  setSelectedBuyerData({ id: searchParamsBuyerId, name: selectedBuyerName });\r\n            }\r\n            if (selectedAreaData.id !== searchParamsAreaId) {\r\n                  setSelectedAreaData({ id: searchParamsAreaId, name: selectedAreaName });\r\n            }\r\n\r\n      }, [searchParamsDataType, searchParamsDays, searchParamsDate, searchParamsSellerId, searchParamsAgentId, searchParamsBuyerId, searchParamsAreaId]);\r\n\r\n      const [days, setDays] = useState(selectedDays.toString())\r\n      const [dashboardData, setDashboardData] = useState<BcSalesDashboardDto>(salesAnalysis || {});\r\n      const fetcher = useFetcher<BcSalesDashboardDto | null>();\r\n      const navigate = useNavigate();\r\n      const [date, setDate] = useState<Date>(\r\n            selectedDate ? new Date(selectedDate) : new Date(new Date().setDate(new Date().getDate() + 1))\r\n      );\r\n\r\n      const [dDate, setDDate] = useState<Date | undefined>(\r\n            searchParamsDDate ? new Date(searchParamsDDate) : undefined\r\n      );\r\n      const pageTotalSize = 50;\r\n\r\n      const handleSubmit = () => {\r\n            const formattedDate = new Date(date); // Format selected date\r\n            const dformattedDate = dDate ? new Date(dDate) : undefined; // Keep dDate unchanged\r\n            setDashboardData({\r\n\r\n\r\n                  date: \"\",\r\n                  dataType: \"\",\r\n                  days: 0,\r\n                  rows: [],\r\n                  totalSales: [],\r\n            }\r\n            );\r\n\r\n            const queryString = `?date=${format(formattedDate, \"yyyy-MM-dd\")}&dataType=${dataType}&days=${days}&sellerId=${selectedSellerData.id}&agentId=${selectedAgentData.id}&buyerId=${selectedBuyerData.id}&areaId=${selectedAreaData.id}&matchBy=${keyword}&pageNo=0&pageSize=${pageTotalSize}&sellerName=${selectedSellerData.name}&agentName=${selectedAgentData.name}&buyerName=${selectedBuyerData.name}&areaName=${selectedAreaData.name}`;\r\n\r\n            // Append dDate only if it's valid\r\n            const finalUrl = dformattedDate ? `${queryString}&dDate=${format(dformattedDate, \"yyyy-MM-dd\")}` : queryString;\r\n\r\n            navigate(finalUrl);\r\n      };\r\n\r\n      useEffect(() => {\r\n            if (salesAnalysis) {\r\n                  setDashboardData(salesAnalysis);\r\n            }\r\n      }, [salesAnalysis]);\r\n\r\n      console.log(salesAnalysis, \"88888888\")\r\n\r\n      const groupedData = dashboardData?.rows?.reduce((acc, row) => {\r\n            if (!acc[row.name]) {\r\n                  acc[row.name] = {\r\n                        id: row?.id?.toString(), sales: {},\r\n                        lastOrderDate: row?.lastOrderedDate,\r\n                        agentName: row?.agentName,\r\n                        buyerMobile: (row?.buyerMobileNumber ?? '').toString()\r\n\r\n\r\n                  };\r\n            }\r\n\r\n            row.cols.forEach(col => {\r\n                  acc[row?.name].sales[col?.deliveryDate] = {\r\n                        salesQty: col?.salesQty,\r\n                        salesAmount: col?.salesAmount,\r\n                        visit: col?.visits,\r\n                        searchCount: col?.searchCount,\r\n                        calls: col?.calls\r\n                  };\r\n            });\r\n\r\n            return acc;\r\n      }, {} as Record<string, { id: string; lastOrderDate: string; agentName: string, buyerMobile: string, sales: Record<string, { salesQty: number; salesAmount: number; visit: number; searchCount: number; calls: number }> }>);\r\n\r\n      const uniqueDates = Array.from(\r\n            new Set(dashboardData?.rows?.flatMap(row => row.cols.map(col => col.deliveryDate)))\r\n      );\r\n\r\n\r\n      const headers = [\r\n            \"Name\",\r\n            ...uniqueDates.map(date => `${format(date, \"EEE dd/MM\")}`)\r\n      ];\r\n      type FormattedRow = { id: string; name: string; lastOrderDate: string, agentName: string, buyerMobile: string } & Record<string, string | boolean>;\r\n\r\n      const formattedTableData: FormattedRow[] = Object.entries(groupedData || {}).map(([name, group]) => {\r\n            const { id, sales = {}, lastOrderDate, agentName, buyerMobile } = group || {};\r\n            return {\r\n                  id,\r\n                  name,\r\n                  lastOrderDate,\r\n                  agentName,\r\n                  buyerMobile,\r\n                  // Add lastOrderDate\r\n                  ...uniqueDates.reduce((acc, date) => {\r\n                        acc[`${date}_qty`] = sales?.[date]?.salesQty ? String(sales[date].salesQty) : \"-\";\r\n                        acc[`${date}_visit`] = sales?.[date]?.visit > 0;\r\n                        acc[`${date}_search`] = sales?.[date]?.searchCount > 0;\r\n                        acc[`${date}_call`] = sales?.[date]?.calls > 0;\r\n                        acc[`${date}_qtycheck`] = sales?.[date]?.salesQty > 0;\r\n                        return acc;\r\n                  }, {} as Record<string, string | boolean>)\r\n            };\r\n      });\r\n\r\n\r\n      const [showSearchForm, setShowSearchForm] = useState(false);\r\n      const [showSearchAgentForm, setShowSearchAgentForm] = useState(false);\r\n      const [showSearchBuyerForm, setShowSearchBuyerForm] = useState(false);\r\n      const [showSearchAreaForm, setShowSearchAreaForm] = useState(false);\r\n\r\n\r\n      const [selectedSellerData, setSelectedSellerData] = useState<SearchItems>({\r\n            id: searchParamsSellerId,\r\n            name: selectedSellerName\r\n      });\r\n\r\n      const [selectedAgentData, setSelectedAgentData] = useState<SearchItems>({\r\n            id: searchParamsAgentId,\r\n            name: selectedAgentName\r\n      });\r\n\r\n      const [selectedBuyerData, setSelectedBuyerData] = useState<SearchItems>({\r\n            id: searchParamsBuyerId,\r\n            name: selectedBuyerName\r\n      });\r\n\r\n      const [selectedAreaData, setSelectedAreaData] = useState<SearchItems>({\r\n            id: searchParamsAreaId,\r\n            name: selectedAreaName\r\n      });\r\n      // const [selectedSellerData, setSelectedSellerData] = useState<SearchItems>({ id: selectedSellerId, name: selectedSellerName });\r\n      // const [selectedAgentData, setSelectedAgentData] = useState<SearchItems>({ id: selectedAgentId, name: selectedAgentName });\r\n      // const [selectedBuyerData, setSelectedBuyerData] = useState<SearchItems>({ id: selectedBuyerId, name: selectedBuyerName });\r\n      // const [selectedAreaData, setSelectedAreaData] = useState<SearchItems>({ id: selectedLocalityId, name: selectedAreaName });\r\n      const [pageNum, setPageNum] = useState(0)\r\n      const handleAddFilers = async (value: string) => {\r\n            if (value === \"seller\") {\r\n\r\n                  const formData = new FormData();\r\n                  formData.append(\"action\", \"sellerWise\");\r\n                  sellerFetcher.submit(formData, { method: \"POST\" });\r\n\r\n                  await setShowSearchForm(true)\r\n            }\r\n            else if (value === \"agent\") {\r\n                  setShowSearchAgentForm(true)\r\n\r\n                  const formData = new FormData();\r\n                  formData.append(\"intent\", value);\r\n                  formData.append(\"type\", \"agent\");\r\n                  formData.append(\"pageNo\", \"0\") as unknown as number;\r\n                  formData.append(\"size\", \"100\") as unknown as number;\r\n                  agentFetcher.submit(formData, { method: \"POST\" });\r\n            }\r\n            else if (value === \"buyer\") {\r\n                  setShowSearchBuyerForm(true)\r\n                  const formData = new FormData();\r\n                  formData.append(\"intent\", value);\r\n                  formData.append(\"type\", \"buyer\");\r\n                  formData.append(\"pageNo\", \"0\") as unknown as number;\r\n                  formData.append(\"size\", \"100\") as unknown as number;\r\n\r\n                  buyerFetcher.submit(formData, { method: \"POST\" });\r\n\r\n            }\r\n            else if (value === \"locality\") {\r\n                  setShowSearchAreaForm(true)\r\n                  const formData = new FormData();\r\n                  formData.append(\"intent\", value);\r\n                  formData.append(\"type\", \"locality\");\r\n                  formData.append(\"pageNo\", \"0\") as unknown as number;\r\n                  formData.append(\"size\", \"100\") as unknown as number;\r\n\r\n                  localityFetcher.submit(formData, { method: \"POST\" });\r\n\r\n            }\r\n\r\n\r\n      }\r\n      const handleSelectedSeller = (seller: SearchItems) => {\r\n            setSelectedSellerData(seller);\r\n            const formattedDate = new Date(date);\r\n            const dformattedDate = dDate ? new Date(dDate) : \"\";\r\n\r\n            navigate(`?date=${format(formattedDate, \"yyyy-MM-dd\")}&dataType=${dataType}&days=${days}&sellerId=${seller?.id}&agentId=${selectedAgentData?.id}&buyerId=${selectedBuyerData?.id}&areaId=${selectedAreaData.id}&matchBy=${keyword}&pageNo=${0}&pageSize=${pageTotalSize}&sellerName=${seller?.name}&agentName=${selectedAgentData.name}&buyerName=${selectedBuyerData.name}&areaName=${selectedAreaData.name}${isValid(dformattedDate) ? `&dDate=${format(dformattedDate, \"yyyy-MM-dd\")}` : \"\"}`\r\n            );\r\n      }\r\n      const handleSelectedAgent = (agent: SearchItems) => {\r\n            setSelectedAgentData(agent);\r\n            const formattedDate = new Date(date);\r\n            const dformattedDate = dDate ? new Date(dDate) : \"\";\r\n            navigate(`?date=${format(formattedDate, \"yyyy-MM-dd\")}&dataType=${dataType}&days=${days}&sellerId=${selectedSellerData?.id}&agentId=${agent?.id}&buyerId=${selectedBuyerData?.id}&areaId=${selectedAreaData?.id}&matchBy=${keyword}&pageNo=${0}&pageSize=${pageTotalSize}&sellerName=${selectedSellerData.name}&agentName=${agent?.name}&buyerName=${selectedBuyerData.name}&areaName=${selectedAreaData.name}${isValid(dformattedDate) ? `&dDate=${format(dformattedDate, \"yyyy-MM-dd\")}` : \"\"}`);\r\n      }\r\n      const handleSelectedBuyer = (buyer: SearchItems) => {\r\n            setSelectedBuyerData(buyer);\r\n            const formattedDate = new Date(date);\r\n            const dformattedDate = dDate ? new Date(dDate) : \"\";\r\n\r\n            navigate(`?date=${format(formattedDate, \"yyyy-MM-dd\")}&dataType=${dataType}&days=${days}&sellerId=${selectedSellerData?.id}&agentId=${selectedAgentData?.id}&buyerId=${buyer?.id}&areaId=${selectedAreaData?.id}&matchBy=${keyword}&pageNo=${0}&pageSize=${pageTotalSize}&sellerName=${selectedSellerData.name}&agentName=${selectedAgentData?.name}&buyerName=${selectedBuyerData.name}&areaName=${selectedAreaData.name}${isValid(dformattedDate) ? `&dDate=${format(dformattedDate, \"yyyy-MM-dd\")}` : \"\"}`);\r\n      }\r\n      const handleSelectedArea = (locality: SearchItems) => {\r\n            setSelectedAreaData(locality);\r\n            const formattedDate = new Date(date);\r\n            const dformattedDate = dDate ? new Date(dDate) : \"\";\r\n\r\n            navigate(`?date=${format(formattedDate, \"yyyy-MM-dd\")}&dataType=${dataType}&days=${days}&sellerId=${selectedSellerData?.id}&agentId=${selectedAgentData?.id}&buyerId=${selectedBuyerData?.id}&areaId=${locality?.id}&matchBy=${keyword}&pageNo=${0}&pageSize=${pageTotalSize}&sellerName=${selectedSellerData.name}&agentName=${selectedAgentData.name}&buyerName=${selectedBuyerData.name}&areaName=${locality?.name}${isValid(dformattedDate) ? `&dDate=${format(dformattedDate, \"yyyy-MM-dd\")}` : \"\"}`\r\n\r\n            );\r\n      }\r\n      const handleClearFilter = async (value: string) => {\r\n            if (value === \"seller\") {\r\n                  setSelectedSellerData({ id: null, name: \"\" })\r\n                  const formattedDate = new Date(date);\r\n                  const dformattedDate = dDate ? new Date(dDate) : \"\";\r\n\r\n                  navigate(`?date=${format(formattedDate, \"yyyy-MM-dd\")}&dataType=${dataType}&days=${days}&sellerId=${null}&agentId=${selectedAgentData?.id}&buyerId=${selectedBuyerData?.id}&areaId=${selectedAreaData.id}&matchBy=${keyword}&pageNo=${0}&pageSize=${pageTotalSize}&sellerName=${\"\"}&agentName=${selectedAgentData.name}&buyerName=${selectedBuyerData.name}&areaName=${selectedAreaData.name}${isValid(dformattedDate) ? `&dDate=${format(dformattedDate, \"yyyy-MM-dd\")}` : \"\"}`\r\n                  );\r\n\r\n            }\r\n            else if (value === \"agent\") {\r\n                  setSelectedAgentData({ id: null, name: \"\" })\r\n                  const formattedDate = new Date(date);\r\n                  const dformattedDate = dDate ? new Date(dDate) : \"\";\r\n\r\n                  navigate(`?date=${format(formattedDate, \"yyyy-MM-dd\")}&dataType=${dataType}&days=${days}&sellerId=${selectedSellerData?.id}&agentId=${null}&buyerId=${selectedBuyerData?.id}&areaId=${selectedAreaData?.id}&matchBy=${keyword}&pageNo=${0}&pageSize=${pageTotalSize}&sellerName=${selectedSellerData.name}&agentName=${\"\"}&buyerName=${selectedBuyerData.name}&areaName=${selectedAreaData.name}${isValid(dformattedDate) ? `&dDate=${format(dformattedDate, \"yyyy-MM-dd\")}` : \"\"}`\r\n\r\n                  )\r\n            }\r\n            else if (value === \"buyer\") {\r\n                  setSelectedBuyerData({ id: null, name: \"\" })\r\n                  const formattedDate = new Date(date);\r\n                  const dformattedDate = dDate ? new Date(dDate) : \"\";\r\n\r\n                  navigate(`?date=${format(formattedDate, \"yyyy-MM-dd\")}&dataType=${dataType}&days=${days}&sellerId=${selectedSellerData?.id}&agentId=${selectedAgentData?.id}&buyerId=${null}&areaId=${selectedAreaData?.id}&matchBy=${keyword}&pageNo=${0}&pageSize=${pageTotalSize}&sellerName=${selectedSellerData.name}&agentName=${selectedAgentData.name}&buyerName=${\"\"}&areaName=${selectedAreaData.name}${isValid(dformattedDate) ? `&dDate=${format(dformattedDate, \"yyyy-MM-dd\")}` : \"\"}`\r\n\r\n                  );\r\n            }\r\n            else if (value === \"locality\") {\r\n                  setSelectedAreaData({ id: null, name: \"\" })\r\n                  const formattedDate = new Date(date);\r\n                  const dformattedDate = dDate ? new Date(dDate) : \"\";\r\n\r\n                  navigate(`?date=${format(formattedDate, \"yyyy-MM-dd\")}&dataType=${dataType}&days=${days}&sellerId=${selectedSellerData?.id}&agentId=${selectedAgentData?.id}&buyerId=${selectedBuyerData?.id}&areaId=${null}&matchBy=${keyword}&pageNo=${0}&pageSize=${pageTotalSize}&sellerName=${selectedSellerData.name}&agentName=${selectedAgentData.name}&buyerName=${selectedBuyerData.name}&areaName=${\"\"}&${isValid(dformattedDate) ? `&dDate=${format(dformattedDate, \"yyyy-MM-dd\")}` : \"\"}`\r\n\r\n                  );\r\n            }\r\n\r\n      }\r\n      const localityFetcher = useFetcher<ActionData>();\r\n      const [localityList, setLocalityList] = useState<SearchItems[]>([]);\r\n\r\n      useEffect(() => {\r\n            if (localityFetcher.data?.selectedData) {\r\n                  setLocalityList(localityFetcher.data.selectedData);\r\n            }\r\n      }, [localityFetcher.data]);\r\n      const buyerFetcher = useFetcher<ActionData>();\r\n      const [buyerList, setBuyerList] = useState<SearchItems[]>([]);\r\n      const handlePageSizeChange = (newSize: string) => {\r\n            setPageNum(Number(newSize))\r\n            const formattedDate = new Date(date);\r\n            const dformattedDate = dDate ? new Date(dDate) : \"\";\r\n\r\n            navigate(`?date=${format(formattedDate, \"yyyy-MM-dd\")}&dataType=${dataType}&days=${days}&sellerId=${selectedSellerData.id}&agentId=${selectedAgentData.id}&buyerId=${selectedBuyerData.id}&areaId=${selectedAreaData.id}&matchBy=${keyword}&pageNo=${newSize}&pageSize=${pageTotalSize}&sellerName=${selectedSellerData.name}&agentName=${selectedAgentData.name}&buyerName=${selectedBuyerData.name}&areaName=${selectedAreaData.name}${isValid(dformattedDate) ? `&dDate=${format(dformattedDate, \"yyyy-MM-dd\")}` : \"\"}`);\r\n      };\r\n      useEffect(() => {\r\n            if (buyerFetcher.data?.selectedData) {\r\n                  setBuyerList(buyerFetcher.data.selectedData);\r\n            }\r\n      }, [buyerFetcher.data?.selectedData]);\r\n\r\n      // console.log(buyerList, \"eeeeeeeeeeeeeee\")\r\n      const agentFetcher = useFetcher<ActionData>();\r\n      const [agentList, setAgentList] = useState<SearchItems[]>([]);\r\n\r\n      useEffect(() => {\r\n            if (agentFetcher.data?.selectedData) {\r\n                  setAgentList(agentFetcher.data.selectedData);\r\n            }\r\n      }, [agentFetcher.data?.selectedData]);\r\n\r\n\r\n      const sellerFetcher = useFetcher<ActionData>();\r\n\r\n\r\n      const [sellerList, setSellerList] = useState<NetWorkDetails[]>([]);\r\n\r\n      const mappedSellerList = sellerList.map(({ sellerId, seller }) => ({\r\n            id: sellerId ?? null, // If sellerId is undefined, set it to null\r\n            name: seller,\r\n      }));\r\n\r\n      useEffect(() => {\r\n            console.log(\"555555555555555\")\r\n\r\n            if (sellerFetcher.data?.sellerList) {\r\n                  setSellerList(sellerFetcher.data.sellerList);\r\n                  console.log(sellerFetcher.data.sellerList, \"00000000000y7777\")\r\n            }\r\n      }, [sellerFetcher.data?.sellerList]);\r\n\r\n\r\n      console.log(sellerList, \"#################33\");\r\n\r\n\r\n\r\n      useEffect(() => {\r\n            if (fetcher.data && fetcher.state === \"idle\" && fetcher.data !== dashboardData) {\r\n                  setDashboardData(fetcher.data);\r\n            }\r\n      }, [fetcher.data, fetcher.state]);\r\n      const [keyword, setKeyword] = useState(\"\"); // State for keyword input\r\n      const [isKeywordEnabled, setIsKeywordEnabled] = useState(false); //s\r\n      const [dDateSelected, setdDateSelected] = useState(false)\r\n      const [open, setOpen] = useState(false); // Manage popover open state\r\n\r\n\r\n\r\n      const handleClickItem = (value: string, selectedId: number, name: string) => {\r\n\r\n            if (value === \"sellerwise\") {\r\n                  setDataType(\"buyerwise\");\r\n                  const formattedDate = new Date(date);\r\n                  const dformattedDate = dDate ? new Date(dDate) : \"\";\r\n\r\n                  setSelectedSellerData({ id: selectedId, name: name })\r\n\r\n\r\n                  navigate(`?date=${format(formattedDate, \"yyyy-MM-dd\")}&dataType=buyerwise&days=${days}&sellerId=${selectedId}&agentId=${selectedAgentData.id}&buyerId=${selectedBuyerData.id}&areaId=${selectedAreaData.id}&matchBy=${keyword}&pageNo=${0}&pageSize=${pageTotalSize}&sellerName=${name}&agentName=${selectedAgentData.name}&buyerName=${selectedBuyerData.name}&areaName=${selectedAreaData.name}${isValid(dformattedDate) ? `&dDate=${format(dformattedDate, \"yyyy-MM-dd\")}` : \"\"}`,\r\n                  );\r\n            }\r\n            else if (value === \"agentwise\") {\r\n                  setDataType(\"buyerwise\");\r\n                  const formattedDate = new Date(date);\r\n                  const dformattedDate = dDate ? new Date(dDate) : \"\";\r\n\r\n                  setSelectedAgentData({ id: selectedId, name: name })\r\n\r\n\r\n                  navigate(`?date=${format(formattedDate, \"yyyy-MM-dd\")}&dataType=buyerwise&days=${days}&sellerId=${selectedSellerData.id}&agentId=${selectedId}&buyerId=${selectedBuyerData.id}&areaId=${selectedAreaData.id}&matchBy=${keyword}&pageNo=${0}&pageSize=${pageTotalSize}&sellerName=${selectedSellerData.name}&agentName=${name}&buyerName=${selectedBuyerData.name}&areaName=${selectedAreaData.name}${isValid(dformattedDate) ? `&dDate=${format(dformattedDate, \"yyyy-MM-dd\")}` : \"\"}`);\r\n            }\r\n            else if (value === \"localitywise\") {\r\n                  setDataType(\"buyerwise\");\r\n                  const formattedDate = new Date(date);\r\n                  const dformattedDate = dDate ? new Date(dDate) : \"\";\r\n\r\n                  setSelectedAreaData({ id: selectedId, name: name })\r\n\r\n\r\n                  navigate(`?date=${format(formattedDate, \"yyyy-MM-dd\")}&dataType=buyerwise&days=${days}&sellerId=${selectedSellerData.id}&agentId=${selectedAgentData.id}&buyerId=${selectedBuyerData.id}&areaId=${selectedId}&matchBy=${keyword}&pageNo=${0}&pageSize=${pageTotalSize}&sellerName=${selectedSellerData.name}&agentName=${selectedAgentData.name}&buyerName=${selectedBuyerData.name}&areaName=${name}${isValid(dformattedDate) ? `&dDate=${format(dformattedDate, \"yyyy-MM-dd\")}` : \"\"}`);\r\n            }\r\n            else if (value === \"buyerwise\") {\r\n                  setDataType(\"itemwise\");\r\n                  const formattedDate = new Date(date);\r\n                  const dformattedDate = dDate ? new Date(dDate) : \"\";\r\n\r\n                  setSelectedBuyerData({ id: selectedId, name: name })\r\n\r\n\r\n                  navigate(`?date=${format(formattedDate, \"yyyy-MM-dd\")}&dataType=itemwise&days=${days}&sellerId=${selectedSellerData.id}&agentId=${selectedAgentData.id}&buyerId=${selectedId}&areaId=${selectedAreaData.id}&matchBy=${keyword}&pageNo=${0}&pageSize=${pageTotalSize}&sellerName=${selectedSellerData.name}&agentName=${selectedAgentData.name}&buyerName=${name}&areaName=${selectedAreaData.name}${isValid(dformattedDate) ? `&dDate=${format(dformattedDate, \"yyyy-MM-dd\")}` : \"\"}`);\r\n            }\r\n\r\n\r\n\r\n      }\r\n      useEffect(() => {\r\n            if (date || dataType || days || keyword?.length >= 3 || dDate) {\r\n                  handleSubmit()\r\n            }\r\n      }, [date, dataType, days, keyword, dDate]);\r\n\r\n      const [isOpen, setIsOpen] = useState(false);\r\n      const [dDateOpen, setDdateOpen] = useState(false);\r\n\r\n\r\n      const handleQtyClick = async (deliveryDate: string, row: unknown) => {\r\n            const formattedDate = new Date(date);\r\n            const dformattedDate = deliveryDate ? new Date(deliveryDate) : \"\";\r\n            navigate(`?date=${format(formattedDate, \"yyyy-MM-dd\")}&dataType=sellerwise&days=${days}&sellerId=${selectedSellerData.id}&agentId=${selectedAgentData.id}&buyerId=${row?.id}&areaId=${selectedAreaData.id}&matchBy=${keyword}&pageNo=${0}&pageSize=${pageTotalSize}&sellerName=${selectedSellerData.name}&agentName=${selectedAgentData.name}&buyerName=${row?.name}&areaName=${selectedAreaData.name}${isValid(dformattedDate) ? `&dDate=${format(dformattedDate, \"yyyy-MM-dd\")}` : \"\"}`);\r\n            await setdDateSelected(true)\r\n\r\n      }\r\n\r\n      return (\r\n\r\n            <div>\r\n                  <div className=\"flex flex-col my-5\">\r\n                        <h1 className=\"text-2xl font-bold\">Sales Analysis</h1>\r\n                  </div>\r\n\r\n                  {/* Form Section */}\r\n                  <div className=\"flex flex-col sm:flex-row sm:space-x-3 my-3\">\r\n                        <fetcher.Form\r\n                              method=\"post\"\r\n                              className=\"flex flex-col sm:flex-row sm:space-x-2 space-y-2 sm:space-y-0 mb-3 sm:mb-0 w-full\"\r\n                        >\r\n                              {/* Date Picker */}\r\n                              <Popover open={isOpen} onOpenChange={setIsOpen}>\r\n                                    <PopoverTrigger asChild>\r\n                                          <Button variant=\"outline\" className=\"w-full sm:w-[280px]\" onClick={() => setIsOpen((prev) => !prev)}\r\n                                          >\r\n                                                <CalendarIcon className=\"mr-2 h-4 w-4\" />\r\n                                                {date ? format(date, \"PPP\") : \"Pick a date\"}\r\n                                          </Button>\r\n                                    </PopoverTrigger>\r\n                                    <PopoverContent className=\"w-auto p-0\">\r\n                                          <Calendar\r\n                                                mode=\"single\"\r\n                                                selected={date}\r\n                                                onSelect={(day) => {\r\n                                                      if (day) setDate(day);\r\n                                                      setIsOpen(false); // Close the popover\r\n\r\n                                                }}\r\n                                                initialFocus\r\n\r\n                                          />\r\n                                    </PopoverContent>\r\n                              </Popover>\r\n\r\n                              {/* Data Type Selection */}\r\n                              <Select value={dataType} onValueChange={setDataType}>\r\n                                    <SelectTrigger className=\"w-full sm:w-[180px]\">\r\n                                          <SelectValue placeholder=\"Select Data Type\" />\r\n                                    </SelectTrigger>\r\n                                    <SelectContent>\r\n                                          <SelectItem value=\"sellerwise\">Seller Wise</SelectItem>\r\n                                          <SelectItem value=\"buyerwise\">Shop Wise</SelectItem>\r\n                                          <SelectItem value=\"localitywise\">Locality Wise</SelectItem>\r\n                                          <SelectItem value=\"agentwise\">Agent Wise</SelectItem>\r\n                                          <SelectItem value=\"itemwise\">Item Wise</SelectItem>\r\n                                          <SelectItem value=\"all\">All</SelectItem>\r\n\r\n                                    </SelectContent>\r\n                              </Select>\r\n\r\n                              {/* Days Selection */}\r\n                              <Select value={days ? days : selectedDays.toString()} onValueChange={setDays}>\r\n                                    <SelectTrigger className=\"w-full sm:w-[180px]\">\r\n                                          <SelectValue placeholder=\"Select Days\" />\r\n                                    </SelectTrigger>\r\n                                    <SelectContent>\r\n                                          <SelectItem value=\"8\">7 days</SelectItem>\r\n                                          <SelectItem value=\"15\">14 days</SelectItem>\r\n                                    </SelectContent>\r\n                              </Select>\r\n                        </fetcher.Form>\r\n                  </div>\r\n                  <div className=\"flex flex-col sm:flex-row sm:flex-wrap gap-3 my-5 w-full p-4 bg-white rounded-xl shadow-md\">\r\n                        {[\r\n                              { label: \"Seller\", data: selectedSellerData, type: \"seller\" },\r\n                              { label: \"Agent\", data: selectedAgentData, type: \"agent\" },\r\n                              { label: \"Shop\", data: selectedBuyerData, type: \"buyer\" },\r\n                              { label: \"Area\", data: selectedAreaData, type: \"locality\" },\r\n\r\n                        ].map(({ label, data, type }) => (\r\n                              <div\r\n                                    key={type}\r\n                                    className=\"flex items-center gap-3 bg-gray-100 border border-gray-200 rounded-lg px-4 py-3 shadow-sm hover:shadow-md transition\"\r\n                              >\r\n                                    <p className=\"text-sm font-semibold text-gray-700\">\r\n                                          {label}: <span className=\"font-bold text-gray-900\">{data?.name}</span>\r\n                                    </p>\r\n                                    {!data?.id ? (\r\n                                          <Button\r\n                                                variant=\"outline\"\r\n                                                size=\"sm\"\r\n                                                className=\"rounded-full text-xs bg-blue-500 text-white hover:bg-blue-600\"\r\n                                                onClick={() => handleAddFilers(type)}\r\n                                          >\r\n                                                + Select\r\n                                          </Button>\r\n                                    ) : (\r\n                                          <Button\r\n                                                variant=\"outline\"\r\n                                                size=\"sm\"\r\n                                                className=\"rounded-full text-xs bg-red-500 text-white hover:bg-red-600\"\r\n                                                onClick={() => handleClearFilter(type)}\r\n                                          >\r\n                                                <CircleX className=\"w-4 h-4\" />\r\n                                          </Button>\r\n                                    )}\r\n                              </div>\r\n                        ))}\r\n\r\n                        {/* Item Search Section */}\r\n                        <div className=\"flex items-center gap-3 bg-gray-100 border border-gray-200 rounded-lg px-4 py-3 shadow-sm hover:shadow-md transition\">\r\n                              <p className=\"text-sm font-semibold text-gray-700\">Item:</p>\r\n                              {isKeywordEnabled ? (\r\n                                    <div className=\"flex items-center gap-2\">\r\n                                          <input\r\n                                                type=\"text\"\r\n                                                value={keyword}\r\n                                                onChange={(e) => setKeyword(e.target.value)}\r\n                                                className=\"border border-gray-300 rounded-md px-3 py-1 text-sm w-32 focus:ring-2 focus:ring-blue-400 focus:outline-none\"\r\n                                          />\r\n                                          <Button\r\n                                                variant=\"outline\"\r\n                                                size=\"sm\"\r\n                                                className=\"rounded-full text-xs bg-red-500 text-white hover:bg-red-600\"\r\n                                                onClick={() => {\r\n                                                      setKeyword(\"\");\r\n                                                      setIsKeywordEnabled(false);\r\n                                                }}\r\n                                          >\r\n                                                <CircleX className=\"w-4 h-4\" />\r\n                                          </Button>\r\n                                    </div>\r\n                              ) : (\r\n                                    <Button\r\n                                          variant=\"outline\"\r\n                                          size=\"sm\"\r\n                                          className=\"rounded-full text-xs bg-blue-500 text-white hover:bg-blue-600\"\r\n                                          onClick={() => setIsKeywordEnabled(true)}\r\n                                    >\r\n                                          + Search\r\n                                    </Button>\r\n                              )}\r\n                        </div>\r\n                        <div className=\"flex items-center gap-3 bg-gray-100 border border-gray-200 rounded-lg px-4 py-3 shadow-sm hover:shadow-md transition\">\r\n                              <p className=\"text-sm font-semibold text-gray-700\">D.Date:</p>\r\n                              {dDateSelected ? (\r\n                                    <div className=\"flex items-center gap-2\">\r\n                                          <Popover open={dDateOpen} onOpenChange={setDdateOpen}>\r\n                                                <PopoverTrigger asChild>\r\n                                                      <Button variant=\"outline\" className=\"w-full sm:w-[280px]\" onClick={() => setDdateOpen((prev) => !prev)}>\r\n                                                            <CalendarIcon className=\"mr-2 h-4 w-4\" />\r\n                                                            {dDate ? format(dDate, \"PPP\") : \"Pick a date\"}\r\n                                                      </Button>\r\n                                                </PopoverTrigger>\r\n                                                <PopoverContent className=\"w-auto p-0\">\r\n                                                      <Calendar\r\n                                                            mode=\"single\"\r\n                                                            selected={dDate}\r\n                                                            onSelect={(day) => {\r\n                                                                  if (day) setDDate(day);\r\n                                                                  setDdateOpen(false)\r\n                                                            }}\r\n                                                            initialFocus\r\n                                                      />\r\n                                                </PopoverContent>\r\n                                          </Popover>\r\n                                          <Button\r\n                                                variant=\"outline\"\r\n                                                size=\"sm\"\r\n                                                className=\"rounded-full text-xs bg-red-500 text-white hover:bg-red-600\"\r\n                                                onClick={() => {\r\n\r\n                                                      setDDate(undefined)\r\n\r\n                                                      setdDateSelected(false);\r\n                                                }}\r\n                                          >\r\n                                                <CircleX className=\"w-4 h-4\" />\r\n                                          </Button>\r\n                                    </div>\r\n                              ) : (\r\n                                    <Button\r\n                                          variant=\"outline\"\r\n                                          size=\"sm\"\r\n                                          className=\"rounded-full text-xs bg-blue-500 text-white hover:bg-blue-600\"\r\n                                          onClick={() => setdDateSelected(true)}\r\n                                    >\r\n                                          + Select\r\n                                    </Button>\r\n                              )}\r\n                        </div>\r\n                  </div>\r\n                  <div className=\"overflow-x-auto rounded-md border max-w-full\">\r\n                        <div className=\"overflow-x-auto rounded-md border max-w-full\">\r\n                              {uniqueDates.length === 1 ? (\r\n                                    <table className=\"w-full border-collapse\">\r\n                                          <thead>\r\n                                                <tr className=\"bg-gray-100\">\r\n                                                      <th className=\"py-2 px-2 text-center w-1/5\">Name</th> {/* Reduced padding */}\r\n                                                      <th className=\"py-2 px-2 text-center w-2/3\">{format(uniqueDates[0], \"EEE dd/MM\")}</th> {/* Reduced padding */}\r\n                                                </tr>\r\n                                          </thead>\r\n                                          <tbody>\r\n                                                {formattedTableData.map(row => (\r\n\r\n                                                      <tr key={row.id} className=\"text-center\">\r\n                                                            {/* Name Column with flex-wrap */}\r\n                                                            <td\r\n                                                                  className=\"py-2 px-4 text-center cursor-pointer text-blue-300 font-bold sticky left-0 bg-white z-10 shadow-md\"\r\n                                                                  onClick={() => handleClickItem(dashboardData?.dataType, Number(row.id), row.name)}\r\n                                                            >\r\n                                                                  <TooltipProvider>\r\n                                                                        <Tooltip>\r\n                                                                              <TooltipTrigger asChild>\r\n                                                                                    <div className=\"flex flex-wrap justify-center items-center break-words whitespace-normal \">\r\n                                                                                          {row.name}\r\n                                                                                    </div>\r\n                                                                              </TooltipTrigger>\r\n                                                                              {dataType === \"buyerwise\" && <TooltipContent>\r\n                                                                                    <p>AgentName: {row?.agentName || \"-\"}</p>\r\n                                                                                    <p>BuyerMobile: {row?.buyerMobileNumber || \"-\"}</p>\r\n                                                                              </TooltipContent>}\r\n                                                                        </Tooltip>\r\n                                                                  </TooltipProvider>\r\n                                                            </td>\r\n\r\n                                                            {/* Data Column */}\r\n                                                            <td className=\"py-2 px-2 text-center w-2/3 \">\r\n                                                                  <div className={`flex flex-row gap-1 justify-center items-center ${row?.lastOrderDate?.split(\"T\")[0] >= uniqueDates[0] && row[`${uniqueDates[0]}_qtycheck`] ? \"bg-yellow-100\" : \"\"} `}>\r\n                                                                        <button\r\n                                                                              className={`font-medium cursor-pointer ${dataType !== \"buyerwise\" ? \"pointer-events-none opacity-50\" : \"\"}`}\r\n                                                                              onClick={() => handleQtyClick(uniqueDates[0], row)}\r\n                                                                              aria-disabled={dataType !== \"buyerwise\"}\r\n                                                                        >\r\n\r\n                                                                              {row[`${uniqueDates[0]}_qty`] || \"-\"}\r\n                                                                        </button>\r\n                                                                        <span className=\"font-medium\">{row[`${uniqueDates[0]}_visit`] && <Store color=\"red\" size={15} />}</span>\r\n                                                                        <span className=\"font-medium\">{row[`${uniqueDates[0]}_search`] && !row[`${uniqueDates[0]}_qtycheck`] && <Search color=\"orange\" size={15} />}</span>\r\n                                                                        <span className=\"font-medium\">{row[`${uniqueDates[0]}_call`] && <PhoneCall color=\"green\" size={15} />}</span>\r\n                                                                  </div>\r\n                                                            </td>\r\n                                                      </tr>\r\n                                                ))}\r\n                                          </tbody>\r\n                                    </table>\r\n                              ) : (\r\n                                    <ResponsiveTable\r\n                                          headers={headers}\r\n                                          data={formattedTableData}\r\n                                          renderRow={(row) => (\r\n\r\n\r\n                                                <tr key={row.id} className=\"text-center\">\r\n                                                      <td\r\n                                                            className=\"py-2 px-4 text-center cursor-pointer text-blue-300 font-bold sticky left-0 bg-white z-10 shadow-md\"\r\n                                                            onClick={() => handleClickItem(dashboardData?.dataType, Number(row.id), row.name)}\r\n                                                      >\r\n                                                            <TooltipProvider>\r\n                                                                  <Tooltip>\r\n                                                                        <TooltipTrigger asChild>\r\n                                                                              <span className=\"cursor-pointer underline\">{row.name}</span>\r\n                                                                        </TooltipTrigger>\r\n                                                                        {dataType === \"buyerwise\" && <TooltipContent>\r\n                                                                              <p>AgentName: {row?.agentName || \"-\"}</p>\r\n                                                                              <p>BuyerMobile: {row?.buyerMobile || \"-\"}</p>\r\n                                                                        </TooltipContent>}\r\n                                                                  </Tooltip>\r\n                                                            </TooltipProvider>\r\n                                                      </td>\r\n\r\n                                                      {uniqueDates.map(date => (\r\n                                                            <td key={`${row.id}-${date}`} className=\"py-2 px-7 text-center w-auto min-w-[100px]\">\r\n                                                                  <div className={`flex flex-row gap-1 justify-center items-center ${row?.lastOrderDate?.split(\"T\")[0] >= date && row[`${date}_qtycheck`] ? \"bg-yellow-100\" : \"\"}`}>\r\n                                                                        <button\r\n                                                                              className={`font-medium ${dataType !== \"buyerwise\" ? \"pointer-events-none opacity-50\" : \"cursor-pointer\"}`}\r\n                                                                              onClick={() => handleQtyClick(date, row)}\r\n                                                                              disabled={dataType !== \"buyerwise\"}\r\n                                                                        >\r\n                                                                              {row[`${date}_qty`] || \"-\"}\r\n                                                                        </button>\r\n                                                                        <span className=\"font-medium\">{row[`${date}_visit`] && <Store color=\"red\" size={15} />}</span>\r\n                                                                        <span className=\"font-medium\">{row[`${date}_search`] && !row[`${date}_qtycheck`] && <Search color=\"orange\" size={15} />}</span>\r\n                                                                        <span className=\"font-medium\">{row[`${date}_call`] && <PhoneCall color=\"green\" size={15} />}</span>\r\n                                                                  </div>\r\n                                                            </td>\r\n                                                      ))}\r\n                                                </tr>\r\n                                          )}\r\n                                    />\r\n                              )}\r\n                        </div>\r\n\r\n\r\n\r\n\r\n\r\n\r\n                        <div className=\"flex items-center justify-end space-x-2 py-4 overflow-x-auto whitespace-nowrap\">\r\n                              <h2 className=\"shrink-0\">Current Page: {pageNum + 1}</h2>\r\n                              <div className=\"overflow-x-auto\">\r\n                                    <ResponsivePagination\r\n                                          totalPages={50}\r\n                                          currentPage={pageNum}\r\n                                          onPageChange={(pageNum) => handlePageSizeChange(pageNum.toString())}\r\n\r\n                                    />\r\n                              </div>\r\n                        </div>\r\n                  </div>\r\n                  <div>\r\n                        <SalesAnalysisDashBoard salesData={dashboardData} />\r\n                  </div>\r\n\r\n\r\n                  <SalesSearchFilters title={\"Select Seller Filter\"} isOpen={showSearchForm} onClose={() => setShowSearchForm(false)} items={mappedSellerList || []} onSelect={(seller) => handleSelectedSeller(seller)} />\r\n                  <SalesSearchFilters title={\"Select Agent Filter\"} isOpen={showSearchAgentForm} onClose={() => setShowSearchAgentForm(false)} items={agentList || []} onSelect={(agent) => handleSelectedAgent(agent)} />\r\n                  <SalesDynamicSearchFilters title={\"Select Buyer Filter\"} isOpen={showSearchBuyerForm} onClose={() => setShowSearchBuyerForm(false)} itemList={buyerList || []} onSelect={(buyer) => handleSelectedBuyer(buyer)} type=\"buyer\" />\r\n                  <SalesDynamicSearchFilters title={\"Select Locality Filter\"} isOpen={showSearchAreaForm} onClose={() => setShowSearchAreaForm(false)} itemList={localityList || []} onSelect={(locality) => handleSelectedArea(locality)} type=\"area\" />\r\n\r\n            </div>\r\n      )\r\n\r\n}\r\n\r\n\r\n\r\n"], "names": ["React.createContext", "React.useContext", "React.forwardRef", "React.useId", "jsxs", "jsx", "RechartsPrimitive.ResponsiveContainer", "config", "RechartsPrimitive.Tooltip", "React.useMemo", "Fragment", "RechartsPrimitive.Legend", "_b", "_a", "useState", "useEffect", "SalesAnalysis", "salesAnalysis", "selectedDate", "dataTypeWise", "selectedDays", "selectedSellerId", "selectedAgentId", "selected<PERSON><PERSON>erId", "selectedLocalityId", "pageNo", "pageSize", "useLoaderData", "searchParams", "useSearchParams", "searchParamsDataType", "useMemo", "get", "searchParamsDays", "toString", "searchParamsDate", "searchParamsDDate", "searchParamsSellerId", "Number", "searchParamsAgentId", "searchParamsBuyerId", "searchParamsAreaId", "selected<PERSON><PERSON><PERSON><PERSON>", "selectedAgentName", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dataType", "setDataType", "days", "setDays", "date", "toISOString", "split", "setDate", "Date", "formattedDDate", "dDate", "setDDate", "selectedSellerData", "id", "setSelectedSellerData", "name", "selectedAgentData", "setSelectedAgentData", "selected<PERSON>uyerData", "setSelectedBuyerData", "selectedAreaData", "setSelectedAreaData", "dashboardData", "setDashboardData", "fetcher", "useFetcher", "navigate", "useNavigate", "getDate", "pageTotalSize", "handleSubmit", "formattedDate", "dformattedDate", "rows", "totalSales", "queryString", "format", "keyword", "finalUrl", "console", "log", "groupedData", "reduce", "acc", "row", "sales", "lastOrderDate", "lastOrderedDate", "<PERSON><PERSON><PERSON>", "buyerMobile", "buyerMobileNumber", "cols", "for<PERSON>ach", "col", "deliveryDate", "salesQty", "salesAmount", "visit", "visits", "searchCount", "calls", "uniqueDates", "Array", "from", "Set", "flatMap", "map", "headers", "formattedTableData", "Object", "entries", "group", "String", "showSearchForm", "setShowSearchForm", "showSearchAgentForm", "setShowSearchAgentForm", "showSearchBuyerForm", "setShowSearchBuyerForm", "showSearchAreaForm", "setShowSearchAreaForm", "pageNum", "setPageNum", "handleAddFilers", "value", "formData", "FormData", "append", "sellerFetcher", "submit", "method", "<PERSON><PERSON><PERSON><PERSON>", "buyerFetcher", "localityFetcher", "handleSelectedSeller", "seller", "<PERSON><PERSON><PERSON><PERSON>", "handleSelectedAgent", "agent", "handleSelectedBuyer", "buyer", "handleSelectedArea", "locality", "handleClearFilter", "localityList", "setLocalityList", "data", "selectedData", "buyerList", "setBuyerList", "handlePageSizeChange", "newSize", "agentList", "setAgentList", "sellerList", "setSellerList", "mappedSellerList", "sellerId", "state", "setKeyword", "isKeywordEnabled", "setIsKeywordEnabled", "dDateSelected", "setdDateSelected", "open", "<PERSON><PERSON><PERSON>", "handleClickItem", "selectedId", "length", "isOpen", "setIsOpen", "dDateOpen", "setDdateOpen", "handleQtyClick", "children", "className", "Form", "Popover", "onOpenChange", "PopoverTrigger", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "variant", "onClick", "prev", "CalendarIcon", "PopoverC<PERSON>nt", "Calendar", "mode", "selected", "onSelect", "day", "initialFocus", "Select", "onValueChange", "SelectTrigger", "SelectValue", "placeholder", "SelectContent", "SelectItem", "label", "type", "size", "CircleX", "onChange", "e", "target", "TooltipProvider", "<PERSON><PERSON><PERSON>", "TooltipTrigger", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Store", "color", "Search", "PhoneCall", "ResponsiveTable", "renderRow", "disabled", "ResponsivePagination", "totalPages", "currentPage", "onPageChange", "SalesAnalysisDashBoard", "salesData", "SalesSearchFilters", "title", "onClose", "items", "SalesDynamicSearchFilters", "itemList"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA,MAAM,YAAY,iBAAiB,aAAa;AAAA,EAC9C;AAAA,IACE;AAAA,IACA;AAAA,MACE,GAAG;AAAA,MACH,KAAK;AAAA,IACX;AAAA,EACG;AAAA,EACD,CAAC,QAAQ,EAAE,GAAG,6BAA6B,KAAK,SAAQ,CAAE;AAAA,EAC1D,CAAC,QAAQ,EAAE,GAAG,4BAA4B,KAAK,SAAU,CAAA;AAC3D,CAAC;ACbD,MAAM,SAAS,EAAE,OAAO,IAAI,MAAM,QAAQ;AAgB1C,MAAM,eAAeA,aAAM,cAAwC,IAAI;AAEvE,SAAS,WAAW;AACZ,QAAA,UAAUC,aAAM,WAAW,YAAY;AAE7C,MAAI,CAAC,SAAS;AACN,UAAA,IAAI,MAAM,mDAAmD;AAAA,EAAA;AAG9D,SAAA;AACT;AAEA,MAAM,iBAAiBC,aAAAA,WAQrB,CAAC,EAAE,IAAI,WAAW,UAAU,QAAQ,GAAG,MAAM,GAAG,QAAQ;AAClD,QAAA,WAAWC,aAAAA,MAAY;AAC7B,QAAM,UAAU,SAAS,MAAM,SAAS,QAAQ,MAAM,EAAE,CAAC;AAEzD,+CACG,aAAa,UAAb,EAAsB,OAAO,EAAE,UAC9B,UAAAC,kCAAA;AAAA,IAAC;AAAA,IAAA;AAAA,MACC,cAAY;AAAA,MACZ;AAAA,MACA,WAAW;AAAA,QACT;AAAA,QACA;AAAA,MACF;AAAA,MACC,GAAG;AAAA,MAEJ,UAAA;AAAA,QAACC,kCAAAA,IAAA,YAAA,EAAW,IAAI,SAAS,OAAgB,CAAA;AAAA,QACxCA,sCAAAC,qBAAA,EACE,SACH,CAAA;AAAA,MAAA;AAAA,IAAA;AAAA,EAAA,GAEJ;AAEJ,CAAC;AACD,eAAe,cAAc;AAE7B,MAAM,aAAa,CAAC,EAAE,IAAI,aAAkD;AAC1E,QAAM,cAAc,OAAO,QAAQ,MAAM,EAAE;AAAA,IACzC,CAAC,CAAC,GAAGC,OAAM,MAAMA,QAAO,SAASA,QAAO;AAAA,EAC1C;AAEI,MAAA,CAAC,YAAY,QAAQ;AAChB,WAAA;AAAA,EAAA;AAIP,SAAAF,kCAAA;AAAA,IAAC;AAAA,IAAA;AAAA,MACC,yBAAyB;AAAA,QACvB,QAAQ,OAAO,QAAQ,MAAM,EAC1B;AAAA,UACC,CAAC,CAAC,OAAO,MAAM,MAAM;AAAA,EAC/B,MAAM,gBAAgB,EAAE;AAAA,EACxB,YACC,IAAI,CAAC,CAAC,KAAK,UAAU,MAAM;;AAC1B,kBAAM,UACJ,gBAAW,UAAX,mBAAmB,WACnB,WAAW;AACb,mBAAO,QAAQ,aAAa,GAAG,KAAK,KAAK,MAAM;AAAA,UAAA,CAChD,EACA,KAAK,IAAI,CAAC;AAAA;AAAA;AAAA,QAGH,EACC,KAAK,IAAI;AAAA,MAAA;AAAA,IACd;AAAA,EACF;AAEJ;AAEA,MAAM,eAAeG;AAErB,MAAM,sBAAsBN,aAAM;AAAA,EAWhC,CACE;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,IACA,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,gBAAgB;AAAA,IAChB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,KAEF,QACG;AACG,UAAA,EAAE,OAAO,IAAI,SAAS;AAEtB,UAAA,eAAeO,aAAAA,QAAc,MAAM;;AACnC,UAAA,aAAa,EAAC,mCAAS,SAAQ;AAC1B,eAAA;AAAA,MAAA;AAGH,YAAA,CAAC,IAAI,IAAI;AACf,YAAM,MAAM,GAAG,YAAY,KAAK,WAAW,KAAK,QAAQ,OAAO;AAC/D,YAAM,aAAa,4BAA4B,QAAQ,MAAM,GAAG;AAC1D,YAAA,QACJ,CAAC,YAAY,OAAO,UAAU,aAC1B,YAAO,KAA4B,MAAnC,mBAAsC,UAAS,QAC/C,yCAAY;AAElB,UAAI,gBAAgB;AAEhB,eAAAJ,kCAAA,IAAC,OAAI,EAAA,WAAW,GAAG,eAAe,cAAc,GAC7C,UAAA,eAAe,OAAO,OAAO,EAChC,CAAA;AAAA,MAAA;AAIJ,UAAI,CAAC,OAAO;AACH,eAAA;AAAA,MAAA;AAGT,mDAAQ,OAAI,EAAA,WAAW,GAAG,eAAe,cAAc,GAAI,UAAM,OAAA;AAAA,IAAA,GAChE;AAAA,MACD;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IAAA,CACD;AAED,QAAI,CAAC,UAAU,EAAC,mCAAS,SAAQ;AACxB,aAAA;AAAA,IAAA;AAGT,UAAM,YAAY,QAAQ,WAAW,KAAK,cAAc;AAGtD,WAAAD,kCAAA;AAAA,MAAC;AAAA,MAAA;AAAA,QACC;AAAA,QACA,WAAW;AAAA,UACT;AAAA,UACA;AAAA,QACF;AAAA,QAEC,UAAA;AAAA,UAAA,CAAC,YAAY,eAAe;AAAA,UAC7BC,sCAAC,SAAI,WAAU,gBACZ,kBAAQ,IAAI,CAAC,MAAM,UAAU;AAC5B,kBAAM,MAAM,GAAG,WAAW,KAAK,QAAQ,KAAK,WAAW,OAAO;AAC9D,kBAAM,aAAa,4BAA4B,QAAQ,MAAM,GAAG;AAChE,kBAAM,iBAAiB,SAAS,KAAK,QAAQ,QAAQ,KAAK;AAGxD,mBAAAA,kCAAA;AAAA,cAAC;AAAA,cAAA;AAAA,gBAEC,WAAW;AAAA,kBACT;AAAA,kBACA,cAAc,SAAS;AAAA,gBACzB;AAAA,gBAEC,wBAAa,6BAAM,WAAU,UAAa,KAAK,OAC9C,UAAU,KAAK,OAAO,KAAK,MAAM,MAAM,OAAO,KAAK,OAAO,IAGvDD,kCAAA,KAAAM,4BAAA,EAAA,UAAA;AAAA,mBAAA,yCAAY,QACVL,kCAAAA,IAAA,WAAW,MAAX,EAAgB,IAEjB,CAAC,iBACCA,kCAAA;AAAA,oBAAC;AAAA,oBAAA;AAAA,sBACC,WAAW;AAAA,wBACT;AAAA,wBACA;AAAA,0BACE,eAAe,cAAc;AAAA,0BAC7B,OAAO,cAAc;AAAA,0BACrB,mDACE,cAAc;AAAA,0BAChB,UAAU,aAAa,cAAc;AAAA,wBAAA;AAAA,sBAEzC;AAAA,sBACA,OACE;AAAA,wBACE,cAAc;AAAA,wBACd,kBAAkB;AAAA,sBAAA;AAAA,oBACpB;AAAA,kBAEJ;AAAA,kBAGJD,kCAAA;AAAA,oBAAC;AAAA,oBAAA;AAAA,sBACC,WAAW;AAAA,wBACT;AAAA,wBACA,YAAY,cAAc;AAAA,sBAC5B;AAAA,sBAEA,UAAA;AAAA,wBAACA,kCAAAA,KAAA,OAAA,EAAI,WAAU,gBACZ,UAAA;AAAA,0BAAA,YAAY,eAAe;AAAA,gEAC3B,QAAK,EAAA,WAAU,yBACb,WAAY,yCAAA,UAAS,KAAK,KAC7B,CAAA;AAAA,wBAAA,GACF;AAAA,wBACC,KAAK,SACHC,sCAAA,QAAA,EAAK,WAAU,sDACb,UAAA,KAAK,MAAM,eAAA,EACd,CAAA;AAAA,sBAAA;AAAA,oBAAA;AAAA,kBAAA;AAAA,gBAEJ,EACF,CAAA;AAAA,cAAA;AAAA,cApDG,KAAK;AAAA,YAsDZ;AAAA,UAAA,CAEH,EACH,CAAA;AAAA,QAAA;AAAA,MAAA;AAAA,IACF;AAAA,EAAA;AAGN;AACA,oBAAoB,cAAc;AAElC,MAAM,cAAcM;AAEpB,MAAM,qBAAqBT,aAAM;AAAA,EAQ/B,CACE,EAAE,WAAW,WAAW,OAAO,SAAS,gBAAgB,UAAU,QAAQ,GAC1E,QACG;AACG,UAAA,EAAE,OAAO,IAAI,SAAS;AAExB,QAAA,EAAC,mCAAS,SAAQ;AACb,aAAA;AAAA,IAAA;AAIP,WAAAG,kCAAA;AAAA,MAAC;AAAA,MAAA;AAAA,QACC;AAAA,QACA,WAAW;AAAA,UACT;AAAA,UACA,kBAAkB,QAAQ,SAAS;AAAA,UACnC;AAAA,QACF;AAAA,QAEC,UAAA,QAAQ,IAAI,CAAC,SAAS;AACrB,gBAAM,MAAM,GAAG,WAAW,KAAK,WAAW,OAAO;AACjD,gBAAM,aAAa,4BAA4B,QAAQ,MAAM,GAAG;AAG9D,iBAAAD,kCAAA;AAAA,YAAC;AAAA,YAAA;AAAA,cAEC,WAAW;AAAA,gBACT;AAAA,cACF;AAAA,cAEC,UAAA;AAAA,iBAAA,yCAAY,SAAQ,CAAC,iDACnB,WAAW,MAAX,CAAgB,CAAA,IAEjBC,kCAAA;AAAA,kBAAC;AAAA,kBAAA;AAAA,oBACC,WAAU;AAAA,oBACV,OAAO;AAAA,sBACL,iBAAiB,KAAK;AAAA,oBAAA;AAAA,kBACxB;AAAA,gBACF;AAAA,gBAED,yCAAY;AAAA,cAAA;AAAA,YAAA;AAAA,YAfR,KAAK;AAAA,UAgBZ;AAAA,QAEH,CAAA;AAAA,MAAA;AAAA,IACH;AAAA,EAAA;AAGN;AACA,mBAAmB,cAAc;AAGjC,SAAS,4BACP,QACA,SACA,KACA;AACA,MAAI,OAAO,YAAY,YAAY,YAAY,MAAM;AAC5C,WAAA;AAAA,EAAA;AAGH,QAAA,iBACJ,aAAa,WACb,OAAO,QAAQ,YAAY,YAC3B,QAAQ,YAAY,OAChB,QAAQ,UACR;AAEN,MAAI,iBAAyB;AAE7B,MACE,OAAO,WACP,OAAO,QAAQ,GAA2B,MAAM,UAChD;AACA,qBAAiB,QAAQ,GAA2B;AAAA,EAAA,WAEpD,kBACA,OAAO,kBACP,OAAO,eAAe,GAAkC,MAAM,UAC9D;AACA,qBAAiB,eACf,GACF;AAAA,EAAA;AAGF,SAAO,kBAAkB,SACrB,OAAO,cAAc,IACrB,OAAO,GAA0B;AACvC;AC9UA,MAAM,cAAc;AAAA,EACd,SAAS;AAAA,IACH,OAAO;AAAA,IACP,OAAO;AAAA,EACb;AAAA,EACA,QAAQ;AAAA,IACF,OAAO;AAAA,IACP,OAAO;AAAA,EAAA;AAEnB;AAIgB,SAAA,uBAAuB,EAAE,aAA0C;;AAGrE,UAAA,IAAI,UAAU,YAAY,6BAA6B;AAEvD,UAAA,IAAI,gBAAgB,SAAS;AAG7B,UAAA,IAAI,qBAAqB,uCAAW,UAAU;AAetD,QAAM,cAAc,MAAM;AAAA,IACpB,IAAI,MAAI,kDAAW,eAAX,mBAAuB,SAAvB,mBAA6B,IAAI,CAAA,QAAO,IAAI,kBAAiB,CAAE,CAAA;AAAA,IAC3E,KAAK;AAIH,MAAA;AACE,QAAA,YAAY,YAAY,IAAI,CAAQ,SAAA;;AACpC,UAAM,iBAAgBO,OAAAC,MAAA,uCAAW,eAAX,gBAAAA,IAAuB,SAAvB,gBAAAD,IAA6B,OAAO,CAAC,KAAK,QAAQ;AAClE,aAAO,IAAI,iBAAiB,OAAO,OAAO,IAAI,YAAY,KAAK;AAAA,OAClE;AACH,WAAO,EAAE,MAAM,UAAU,cAAc,QAAQ,CAAC,EAAE;AAAA,EAAA,CACvD;AAED,kBAEYP,kCAAA,IAAAK,4BAAA,EAAA,UAAAL,kCAAA;AAAA,IAAC;AAAA,IAAA;AAAA,MACK,SAAQ;AAAA,MACR,SAAQ;AAAA,MACR,MAAK;AAAA,MACL,QAAQ,CAAC,GAAG,GAAG,GAAG,CAAC;AAAA,MACnB,OAAO,EAAE,UAAU,OAAO,MAAM,OAAO;AAAA,MACvC,SAAS,UAAU,WAAW,IAAI,KAAK;AAAA,IAAA;AAAA,EAAA,GAInD;AAGA,SAAAD,kCAAA,KAAC,MAAK,EAAA,WAAU,QACV,UAAA;AAAA,IAAAC,sCAAC,YACK,EAAA,UAAAA,kCAAA,IAAC,WAAU,EAAA,UAAA,uBAAoB,CAAA,GAErC;AAAA,IACAA,kCAAA,IAAC,aACK,EAAA,UAAAA,kCAAA,IAAC,gBAAe,EAAA,QAAQ,aAElB,UAAAD,kCAAA,KAAC,UAAS,EAAA,oBAAkB,MAAC,MAAM,WAE7B,UAAA;AAAA,MAACC,kCAAAA,IAAA,eAAA,EAAc,UAAU,MAAO,CAAA;AAAA,MAChCA,kCAAA;AAAA,QAAC;AAAA,QAAA;AAAA,UACK,SAAQ;AAAA,UACR,UAAU;AAAA,UACV,YAAY;AAAA,UACZ,UAAU;AAAA,UACV,eAAe,CAAC,UAAU,OAAO,IAAI,KAAK,KAAK,GAAG,WAAW;AAAA,QAAA;AAAA,MACnE;AAAA,4CACC,OAAM,EAAA;AAAA,4CAEN,cAAa,EAAA,+CAAU,qBAAoB,EAAA,WAAS,KAAC,CAAA,GAAI;AAAA,MACzDA,kCAAA,IAAA,aAAA,EAAY,SAASA,kCAAA,IAAC,qBAAmB,CAAA,GAAI;AAAA,MAC7C;AAAA,MAEDA,kCAAA;AAAA,QAAC;AAAA,QAAA;AAAA,UACK,QAAQ;AAAA,UACR,SAASA,kCAAAA,IAAC,qBAAoB,EAAA,WAAU,SAAS,CAAA;AAAA,QAAA;AAAA,MAAA;AAAA,IACvD,EACN,CAAA,EACN,CAAA,EAEN,CAAA;AAAA,EAAA,GAEN;AAEZ;ACnGA,SAAwB,0BAA0B;AAAA,EAC5C;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACN,GAAe;AACT,QAAM,CAAC,QAAQ,SAAS,IAAIS,aAAAA,SAAS,EAAE;AACvC,QAAM,CAAC,gBAAgB,iBAAiB,IAAIA,aAAAA,SAA6B,IAAI;AAC7E,QAAM,CAAC,OAAO,QAAQ,IAAIA,aAAAA,SAAwB,QAAQ;AAC1D,QAAM,CAAC,MAAM,OAAO,IAAIA,aAAAA,SAAS,CAAC;AAClC,QAAM,CAAC,YAAY,aAAa,IAAIA,aAAAA,SAAS,GAAG;AAChD,QAAM,cAAc,WAAuB;AAE3C,QAAM,iBAAiB,MAAM;AACvB,QAAI,OAAO,SAAS,KAAK,SAAS,EAAG;AAE/B,UAAA,WAAW,IAAI,SAAS;AACrB,aAAA,OAAO,QAAQ,IAAI;AACnB,aAAA,OAAO,UAAU,IAAI;AAC9B,aAAS,OAAO,UAAU,KAAK,SAAA,CAAU;AAChC,aAAA,OAAO,QAAQ,IAAI;AACnB,aAAA,OAAO,WAAW,MAAM;AAEjC,gBAAY,OAAO,UAAU;AAAA,MACvB,QAAQ;AAAA,MACR,SAAS;AAAA,IAAA,CACd;AAAA,EACP;AAEAC,eAAAA,UAAU,MAAM;AACV,QAAI,OAAO,UAAU,KAAK,OAAO,GAAG;AACf,qBAAA;AAAA,IAAA;AAAA,EACrB,GACH,CAAC,QAAQ,IAAI,CAAC;AAEjBA,eAAAA,UAAU,MAAM;;AACV,QAAI,CAAC,QAAQ;AACP,eAAS,QAAQ;AAAA,IAAA,YACZ,gDAAa,SAAb,mBAAmB,cAAc;AAC7B,eAAA,YAAY,KAAK,YAAY;AAAA,IAAA;AAAA,KAE/C,CAAC,QAAQ,YAAY,MAAM,QAAQ,CAAC;AAEjC,QAAA,qBAAqB,CAAC,MAA2C;AACvD,cAAA,EAAE,OAAO,KAAK;AACxB,YAAQ,CAAC;AACT,QAAI,EAAE,OAAO,MAAM,UAAU,GAAG;AACX,qBAAA;AAAA,IAAA;AAAA,EAE3B;AAEQ,UAAA,IAAI,OAAO,aAAa;AAChC,QAAM,WAAW,MAAM;AACjB,YAAQ,CAAC,SAAU,OAAO,aAAa,OAAO,IAAI,IAAK;AACxC,mBAAA;AAAA,EACrB;AAEA,QAAM,WAAW,MAAM;AACjB,YAAQ,CAAC,SAAU,OAAO,IAAI,OAAO,IAAI,IAAK;AAC/B,mBAAA;AAAA,EACrB;AAEA,QAAM,kBAAkB,MAAM;AACxB,QAAI,gBAAgB;AACd,eAAS,cAAc;AAAA,IAAA;AAErB,YAAA;AAAA,EACd;AASM,SAAAV,kCAAA,IAAC,UAAO,MAAM,QAAQ,cAAc,SAC9B,UAAAD,kCAAA,KAAC,eAAc,EAAA,WAAU,8CACnB,UAAA;AAAA,IAAAC,kCAAAA,IAAC,gBACK,UAACA,kCAAA,IAAA,aAAA,EAAY,WAAU,yBAAyB,iBAAM,EAC5D,CAAA;AAAA,IAEAA,kCAAA;AAAA,MAAC;AAAA,MAAA;AAAA,QACK,aAAY;AAAA,QACZ,OAAO;AAAA,QACP,UAAU;AAAA,QACV,WAAU;AAAA,MAAA;AAAA,IAChB;AAAA,IAEC,YAAY,UAAU,SACjBA,kCAAA,IAAC,OAAE,WAAU,kCAAiC,UAAU,aAAA,CAAA,IAEvDA,kCAAA,IAAA,OAAA,EAAI,WAAU,2CACR,UAAA,MAAM,SAAS,IACV,MAAM,IAAI,CAAC,SACLD,kCAAA,KAAC,SAAoB,EAAA,WAAU,2FACzB,UAAA;AAAA,MAAAC,kCAAA;AAAA,QAAC;AAAA,QAAA;AAAA,UACK,MAAK;AAAA,UACL,MAAK;AAAA,UACL,UAAS,iDAAgB,QAAO,KAAK;AAAA,UACrC,UAAU,MAAM,kBAAkB,IAAI;AAAA,UACtC,WAAU;AAAA,QAAA;AAAA,MAChB;AAAA,MACAA,kCAAAA,IAAC,QAAM,EAAA,UAAA,KAAK,KAAK,CAAA;AAAA,IARX,EAAA,GAAA,KAAK,EASjB,CACL,0CAEA,KAAE,EAAA,WAAU,6BAA4B,UAAA,oBAAA,CAAiB,EAEtE,CAAA;AAAA,IAGND,kCAAAA,KAAC,OAAI,EAAA,WAAU,0CACT,UAAA;AAAA,MAACC,kCAAAA,IAAA,QAAA,EAAO,SAAQ,WAAU,SAAS,UAAU,UAAU,SAAS,GAAG,UAEnE,OAAA,CAAA;AAAA,MACAD,kCAAAA,KAAC,QAAK,EAAA,WAAU,iBAAgB,UAAA;AAAA,QAAA;AAAA,QAAM;AAAA,QAAK;AAAA,QAAK;AAAA,MAAA,GAAW;AAAA,MAC3DC,kCAAAA,IAAC,UAAO,SAAQ,WAAU,SAAS,UAAU,UAAU,SAAS,YAAY,UAE5E,OAAA,CAAA;AAAA,IAAA,GACN;AAAA,IAEAD,kCAAAA,KAAC,cAAa,EAAA,WAAU,yBAClB,UAAA;AAAA,MAAAC,sCAAC,QAAO,EAAA,SAAQ,WAAU,SAAS,SAAS,UAE5C,SAAA;AAAA,MACAA,kCAAAA,IAAC,UAAO,WAAU,QAAO,SAAS,iBAAiB,UAAU,CAAC,gBAAgB,UAE9E,eAAA,CAAA;AAAA,IAAA,EACN,CAAA;AAAA,EAAA,EAAA,CACN,EACN,CAAA;AAEZ;AC7IA,SAAwB,mBAAmB,EAAE,OAAO,QAAQ,SAAS,OAAO,YAAwB;AAE9F,QAAM,CAAC,QAAQ,SAAS,IAAIS,aAAAA,SAAS,EAAE;AAEvC,QAAM,gBAAgB,+BAAO;AAAA,IAAO,CAAC,SAC/B;;AAAA,gDAAM,SAAN,mBAAY,cAAc,SAAS,OAAO,YAAa;AAAA;AAAA;AAG7D,QAAM,CAAC,gBAAgB,iBAAiB,IAAIA,aAAAA,SAA8C,IAAI;AAI9F,QAAM,kBAAkB,MAAM;AACxB,QAAI,gBAAgB;AACd,eAAS,cAAc;AAAA,IAAA;AAErB,YAAA;AAAA,EACd;AAGM,SAAAT,kCAAA,IAAC,UAAO,MAAM,QAAQ,cAAc,SAC9B,UAAAD,kCAAA,KAAC,eAAc,EAAA,WAAU,8CACnB,UAAA;AAAA,IAAAC,kCAAAA,IAAC,gBACK,UAACA,kCAAA,IAAA,aAAA,EAAY,WAAU,yBAAyB,iBAAM,EAC5D,CAAA;AAAA,IAEAA,kCAAA;AAAA,MAAC;AAAA,MAAA;AAAA,QACK,aAAY;AAAA,QACZ,OAAO;AAAA,QACP,UAAU,CAAC,MAAM,UAAU,EAAE,OAAO,KAAK;AAAA,QACzC,WAAU;AAAA,MAAA;AAAA,IAChB;AAAA,IAEAA,kCAAAA,IAAC,OAAI,EAAA,WAAU,2CACR,UAAA,cAAc,SAAS,IAClB,+CAAe,IAAI,CAAC,SACdD,kCAAA;AAAA,MAAC;AAAA,MAAA;AAAA,QAEK,WAAU;AAAA,QAEV,UAAA;AAAA,UAAAC,kCAAA;AAAA,YAAC;AAAA,YAAA;AAAA,cACK,MAAK;AAAA,cACL,MAAK;AAAA,cACL,UAAS,iDAAgB,QAAO,KAAK;AAAA,cACrC,UAAU,MAAM,kBAAkB,EAAE,IAAI,KAAK,IAAI,MAAM,KAAK,MAAM;AAAA,cAClE,WAAU;AAAA,YAAA;AAAA,UAChB;AAAA,UACAA,kCAAAA,IAAC,QAAM,EAAA,UAAA,KAAK,KAAK,CAAA;AAAA,QAAA;AAAA,MAAA;AAAA,MAVZ,KAAK;AAAA,IAAA,KActBA,kCAAAA,IAAC,OAAE,WAAU,6BAA4B,8BAAiB,CAAA,GAEtE;AAAA,IAGAD,kCAAAA,KAAC,cAAa,EAAA,WAAU,yBAClB,UAAA;AAAA,MAAAC,sCAAC,QAAO,EAAA,SAAQ,WAAU,SAAS,SAAS,UAAK,SAAA;AAAA,4CAChD,QAAO,EAAA,WAAU,QAAO,SAAS,iBAAiB,UAAY,eAAA,CAAA;AAAA,IAAA,EACrE,CAAA;AAAA,EAAA,EAAA,CAEN,EACN,CAAA;AAEZ;AC0DA,SAAwBW,gBAAgB;;AAElC,QAAM;AAAA,IAAEC;AAAAA,IAAeC;AAAAA,IAAcC;AAAAA,IAAcC;AAAAA,IAAcC;AAAAA,IAAkBC;AAAAA,IAAiBC;AAAAA,IAAiBC;AAAAA,IAAoBC;AAAAA,IAAQC;AAAAA,EAAS,IAAIC,cAA0B;AAClL,QAAA,CAACC,YAAY,IAAIC,gBAAgB;AACjC,QAAAC,uBAAuBC,aAAAA,QAAQ,MAAMH,aAAaI,IAAI,UAAU,KAAKb,cAAc,CAACS,cAAcT,YAAY,CAAC;AACrH,QAAMc,mBAAmBF,aAAAA,QAAQ,MAAMH,aAAaI,IAAI,MAAM,MAAKZ,6CAAcc,aAAY,CAACN,cAAcR,YAAY,CAAC;AACnH,QAAAe,mBAAmBJ,aAAAA,QAAQ,MAAMH,aAAaI,IAAI,MAAM,KAAKd,cAAc,CAACU,cAAcV,YAAY,CAAC;AACvG,QAAAkB,oBAAoBR,aAAaI,IAAI,OAAO;AAElD,QAAMK,uBAAuBC,OAAOV,aAAaI,IAAI,UAAU,KAAKX,gBAAgB;AACpF,QAAMkB,sBAAsBD,OAAOV,aAAaI,IAAI,SAAS,KAAKV,eAAe;AACjF,QAAMkB,sBAAsBF,OAAOV,aAAaI,IAAI,SAAS,KAAKT,eAAe;AACjF,QAAMkB,qBAAqBH,OAAOV,aAAaI,IAAI,QAAQ,KAAKR,kBAAkB;AACvDc,SAAOV,aAAaI,IAAI,QAAQ,KAAKP,MAAM;AACzCa,SAAOV,aAAaI,IAAI,UAAU,KAAKN,QAAQ;AAE5E,QAAMgB,qBAAqBd,aAAaI,IAAI,YAAY,KAAK;AAC7D,QAAMW,oBAAoBf,aAAaI,IAAI,WAAW,KAAK;AAC3D,QAAMY,oBAAoBhB,aAAaI,IAAI,WAAW,KAAK;AAC3D,QAAMa,mBAAmBjB,aAAaI,IAAI,UAAU,KAAK;AAEzD,QAAM,CAACc,UAAUC,WAAW,IAAIjC,aAAAA,SAASgB,oBAAoB;AAC7Df,eAAAA,UAAU,MAAM;AACN,QAAA+B,aAAahB,qBAAsBiB,aAAYjB,oBAAoB;AACnE,QAAAkB,SAASf,iBAAkBgB,SAAQhB,gBAAgB;AACnD,QAAAiB,KAAKC,cAAcC,MAAM,GAAG,EAAE,CAAC,MAAMjB,kBAAkB;AAC7CkB,cAAA,IAAIC,KAAKnB,gBAAgB,CAAC;AAAA,IACxC;AACA,QAAIC,mBAAmB;AACX,YAAAmB,iBAAiB,IAAID,KAAKlB,iBAAiB,EAAEe,cAAcC,MAAM,GAAG,EAAE,CAAC;AAEzE,UAAAG,oBAAmBC,+BAAOL,cAAcC,MAAM,KAAK,KAAI;AAC5CK,iBAAA,IAAIH,KAAKlB,iBAAiB,CAAC;AAAA,MAC1C;AAAA,IACN;AAEI,QAAAsB,mBAAmBC,OAAOtB,sBAAsB;AAC9CuB,4BAAsB;AAAA,QAAED,IAAItB;AAAAA,QAAsBwB,MAAMnB;AAAAA,MAAmB,CAAC;AAAA,IAClF;AACI,QAAAoB,kBAAkBH,OAAOpB,qBAAqB;AAC5CwB,2BAAqB;AAAA,QAAEJ,IAAIpB;AAAAA,QAAqBsB,MAAMlB;AAAAA,MAAkB,CAAC;AAAA,IAC/E;AACI,QAAAqB,kBAAkBL,OAAOnB,qBAAqB;AAC5CyB,2BAAqB;AAAA,QAAEN,IAAInB;AAAAA,QAAqBqB,MAAMjB;AAAAA,MAAkB,CAAC;AAAA,IAC/E;AACI,QAAAsB,iBAAiBP,OAAOlB,oBAAoB;AAC1C0B,0BAAoB;AAAA,QAAER,IAAIlB;AAAAA,QAAoBoB,MAAMhB;AAAAA,MAAiB,CAAC;AAAA,IAC5E;AAAA,EAEN,GAAG,CAACf,sBAAsBG,kBAAkBE,kBAAkBE,sBAAsBE,qBAAqBC,qBAAqBC,kBAAkB,CAAC;AAEjJ,QAAM,CAACO,MAAMC,OAAO,IAAInC,aAAAA,SAASM,aAAac,UAAU;AACxD,QAAM,CAACkC,eAAeC,gBAAgB,IAAIvD,aAAAA,SAA8BG,iBAAiB,CAAA,CAAE;AAC3F,QAAMqD,UAAUC,WAAuC;AACvD,QAAMC,WAAWC,YAAY;AACvB,QAAA,CAACvB,MAAMG,OAAO,IAAIvC,aAAAA,SAClBI,eAAe,IAAIoC,KAAKpC,YAAY,IAAI,IAAIoC,MAAK,oBAAIA,KAAK,GAAED,SAAY,oBAAAC,KAAA,GAAOoB,QAAQ,IAAI,CAAC,CAAC,CACnG;AAEM,QAAA,CAAClB,OAAOC,QAAQ,IAAI3C,aAAA,SACpBsB,oBAAoB,IAAIkB,KAAKlB,iBAAiB,IAAI,MACxD;AACA,QAAMuC,gBAAgB;AAEtB,QAAMC,eAAeA,MAAM;AACf,UAAAC,gBAAgB,IAAIvB,KAAKJ,IAAI;AACnC,UAAM4B,iBAAiBtB,QAAQ,IAAIF,KAAKE,KAAK,IAAI;AACjDa,qBAAiB;AAAA,MAGXnB,MAAM;AAAA,MACNJ,UAAU;AAAA,MACVE,MAAM;AAAA,MACN+B,MAAM,CAAC;AAAA,MACPC,YAAY,CAAA;AAAA,IAClB,CACA;AAEA,UAAMC,cAAc,SAASC,OAAOL,eAAe,YAAY,CAAC,aAAa/B,QAAQ,SAASE,IAAI,aAAaU,mBAAmBC,EAAE,YAAYG,kBAAkBH,EAAE,YAAYK,kBAAkBL,EAAE,WAAWO,iBAAiBP,EAAE,YAAYwB,OAAO,sBAAsBR,aAAa,eAAejB,mBAAmBG,IAAI,cAAcC,kBAAkBD,IAAI,cAAcG,kBAAkBH,IAAI,aAAaK,iBAAiBL,IAAI;AAGla,UAAAuB,WAAWN,iBAAiB,GAAGG,WAAW,UAAUC,OAAOJ,gBAAgB,YAAY,CAAC,KAAKG;AAEnGT,aAASY,QAAQ;AAAA,EACvB;AAEArE,eAAAA,UAAU,MAAM;AACV,QAAIE,eAAe;AACboD,uBAAiBpD,aAAa;AAAA,IACpC;AAAA,EACN,GAAG,CAACA,aAAa,CAAC;AAEVoE,UAAAC,IAAIrE,eAAe,UAAU;AAErC,QAAMsE,eAAcnB,oDAAeW,SAAfX,mBAAqBoB,OAAO,CAACC,KAAKC,QAAQ;;AACxD,QAAI,CAACD,IAAIC,IAAI7B,IAAI,GAAG;AACV4B,UAAAC,IAAI7B,IAAI,IAAI;AAAA,QACVF,KAAI+B,MAAAA,2BAAK/B,OAAL+B,gBAAAA,IAASxD;AAAAA,QAAYyD,OAAO,CAAC;AAAA,QACjCC,eAAeF,2BAAKG;AAAAA,QACpBC,WAAWJ,2BAAKI;AAAAA,QAChBC,eAAcL,2BAAKM,sBAAqB,IAAI9D,SAAS;AAAA,MAG3D;AAAA,IACN;AAEIwD,QAAAO,KAAKC,QAAeC,SAAA;AAClBV,UAAIC,2BAAK7B,IAAI,EAAE8B,MAAMQ,2BAAKC,YAAY,IAAI;AAAA,QACpCC,UAAUF,2BAAKE;AAAAA,QACfC,aAAaH,2BAAKG;AAAAA,QAClBC,OAAOJ,2BAAKK;AAAAA,QACZC,aAAaN,2BAAKM;AAAAA,QAClBC,OAAOP,2BAAKO;AAAAA,MAClB;AAAA,IACN,CAAC;AAEM,WAAAjB;AAAAA,EACb,GAAG;AAEH,QAAMkB,cAAcC,MAAMC,KACpB,IAAIC,KAAI1C,oDAAeW,SAAfX,mBAAqB2C,QAAQrB,SAAOA,IAAIO,KAAKe,IAAIb,SAAOA,IAAIC,YAAY,EAAE,CACxF;AAGA,QAAMa,UAAU,CACV,QACA,GAAGN,YAAYK,IAAI9D,WAAQ,GAAGgC,OAAOhC,OAAM,WAAW,CAAC,EAAE,CAAA;AAI/D,QAAMgE,qBAAqCC,OAAOC,QAAQ7B,eAAe,CAAA,CAAE,EAAEyB,IAAI,CAAC,CAACnD,MAAMwD,KAAK,MAAM;AACxF,UAAA;AAAA,MAAE1D;AAAAA,MAAIgC,QAAQ,CAAC;AAAA,MAAGC;AAAAA,MAAeE;AAAAA,MAAWC;AAAAA,IAAgB,IAAAsB,SAAS,CAAC;AACrE,WAAA;AAAA,MACD1D;AAAAA,MACAE;AAAAA,MACA+B;AAAAA,MACAE;AAAAA,MACAC;AAAAA;AAAAA,MAEA,GAAGY,YAAYnB,OAAO,CAACC,KAAKvC,UAAS;;AAC/BuC,YAAI,GAAGvC,KAAI,MAAM,MAAIyC,MAAAA,+BAAQzC,WAARyC,gBAAAA,IAAeU,YAAWiB,OAAO3B,MAAMzC,KAAI,EAAEmD,QAAQ,IAAI;AAC9EZ,YAAI,GAAGvC,KAAI,QAAQ,MAAIyC,MAAAA,+BAAQzC,WAARyC,gBAAAA,IAAeY,SAAQ;AAC9Cd,YAAI,GAAGvC,KAAI,SAAS,MAAIyC,MAAAA,+BAAQzC,WAARyC,gBAAAA,IAAec,eAAc;AACrDhB,YAAI,GAAGvC,KAAI,OAAO,MAAIyC,MAAAA,+BAAQzC,WAARyC,gBAAAA,IAAee,SAAQ;AAC7CjB,YAAI,GAAGvC,KAAI,WAAW,MAAIyC,MAAAA,+BAAQzC,WAARyC,gBAAAA,IAAeU,YAAW;AAC7C,eAAAZ;AAAAA,SACV,CAAsC,CAAA;AAAA,IAC/C;AAAA,EACN,CAAC;AAGD,QAAM,CAAC8B,gBAAgBC,iBAAiB,IAAI1G,aAAAA,SAAS,KAAK;AAC1D,QAAM,CAAC2G,qBAAqBC,sBAAsB,IAAI5G,aAAAA,SAAS,KAAK;AACpE,QAAM,CAAC6G,qBAAqBC,sBAAsB,IAAI9G,aAAAA,SAAS,KAAK;AACpE,QAAM,CAAC+G,oBAAoBC,qBAAqB,IAAIhH,aAAAA,SAAS,KAAK;AAGlE,QAAM,CAAC4C,oBAAoBE,qBAAqB,IAAI9C,sBAAsB;AAAA,IACpE6C,IAAItB;AAAAA,IACJwB,MAAMnB;AAAAA,EACZ,CAAC;AAED,QAAM,CAACoB,mBAAmBC,oBAAoB,IAAIjD,sBAAsB;AAAA,IAClE6C,IAAIpB;AAAAA,IACJsB,MAAMlB;AAAAA,EACZ,CAAC;AAED,QAAM,CAACqB,mBAAmBC,oBAAoB,IAAInD,sBAAsB;AAAA,IAClE6C,IAAInB;AAAAA,IACJqB,MAAMjB;AAAAA,EACZ,CAAC;AAED,QAAM,CAACsB,kBAAkBC,mBAAmB,IAAIrD,sBAAsB;AAAA,IAChE6C,IAAIlB;AAAAA,IACJoB,MAAMhB;AAAAA,EACZ,CAAC;AAKD,QAAM,CAACkF,SAASC,UAAU,IAAIlH,aAAAA,SAAS,CAAC;AAClC,QAAAmH,kBAAkB,OAAOC,UAAkB;AAC3C,QAAIA,UAAU,UAAU;AAEZ,YAAAC,WAAW,IAAIC,SAAS;AACrBD,eAAAE,OAAO,UAAU,YAAY;AACtCC,oBAAcC,OAAOJ,UAAU;AAAA,QAAEK,QAAQ;AAAA,MAAO,CAAC;AAEjD,YAAMhB,kBAAkB,IAAI;AAAA,IAClC,WACSU,UAAU,SAAS;AACtBR,6BAAuB,IAAI;AAErB,YAAAS,WAAW,IAAIC,SAAS;AACrBD,eAAAE,OAAO,UAAUH,KAAK;AACtBC,eAAAE,OAAO,QAAQ,OAAO;AACtBF,eAAAE,OAAO,UAAU,GAAG;AACpBF,eAAAE,OAAO,QAAQ,KAAK;AAC7BI,mBAAaF,OAAOJ,UAAU;AAAA,QAAEK,QAAQ;AAAA,MAAO,CAAC;AAAA,IACtD,WACSN,UAAU,SAAS;AACtBN,6BAAuB,IAAI;AACrB,YAAAO,WAAW,IAAIC,SAAS;AACrBD,eAAAE,OAAO,UAAUH,KAAK;AACtBC,eAAAE,OAAO,QAAQ,OAAO;AACtBF,eAAAE,OAAO,UAAU,GAAG;AACpBF,eAAAE,OAAO,QAAQ,KAAK;AAE7BK,mBAAaH,OAAOJ,UAAU;AAAA,QAAEK,QAAQ;AAAA,MAAO,CAAC;AAAA,IAEtD,WACSN,UAAU,YAAY;AACzBJ,4BAAsB,IAAI;AACpB,YAAAK,WAAW,IAAIC,SAAS;AACrBD,eAAAE,OAAO,UAAUH,KAAK;AACtBC,eAAAE,OAAO,QAAQ,UAAU;AACzBF,eAAAE,OAAO,UAAU,GAAG;AACpBF,eAAAE,OAAO,QAAQ,KAAK;AAE7BM,sBAAgBJ,OAAOJ,UAAU;AAAA,QAAEK,QAAQ;AAAA,MAAO,CAAC;AAAA,IAEzD;AAAA,EAGN;AACM,QAAAI,uBAAwBC,YAAwB;AAChDjF,0BAAsBiF,MAAM;AACtB,UAAAhE,gBAAgB,IAAIvB,KAAKJ,IAAI;AACnC,UAAM4B,iBAAiBtB,QAAQ,IAAIF,KAAKE,KAAK,IAAI;AAEjDgB,aAAS,SAASU,OAAOL,eAAe,YAAY,CAAC,aAAa/B,QAAQ,SAASE,IAAI,aAAa6F,iCAAQlF,EAAE,YAAYG,uDAAmBH,EAAE,YAAYK,uDAAmBL,EAAE,WAAWO,iBAAiBP,EAAE,YAAYwB,OAAO,WAAW,CAAC,aAAaR,aAAa,eAAekE,iCAAQhF,IAAI,cAAcC,kBAAkBD,IAAI,cAAcG,kBAAkBH,IAAI,aAAaK,iBAAiBL,IAAI,GAAGiF,QAAQhE,cAAc,IAAI,UAAUI,OAAOJ,gBAAgB,YAAY,CAAC,KAAK,EAAE,EAC9d;AAAA,EACN;AACM,QAAAiE,sBAAuBC,WAAuB;AAC9CjF,yBAAqBiF,KAAK;AACpB,UAAAnE,gBAAgB,IAAIvB,KAAKJ,IAAI;AACnC,UAAM4B,iBAAiBtB,QAAQ,IAAIF,KAAKE,KAAK,IAAI;AACxCgB,aAAA,SAASU,OAAOL,eAAe,YAAY,CAAC,aAAa/B,QAAQ,SAASE,IAAI,aAAaU,yDAAoBC,EAAE,YAAYqF,+BAAOrF,EAAE,YAAYK,uDAAmBL,EAAE,WAAWO,qDAAkBP,EAAE,YAAYwB,OAAO,WAAW,CAAC,aAAaR,aAAa,eAAejB,mBAAmBG,IAAI,cAAcmF,+BAAOnF,IAAI,cAAcG,kBAAkBH,IAAI,aAAaK,iBAAiBL,IAAI,GAAGiF,QAAQhE,cAAc,IAAI,UAAUI,OAAOJ,gBAAgB,YAAY,CAAC,KAAK,EAAE,EAAE;AAAA,EACve;AACM,QAAAmE,sBAAuBC,WAAuB;AAC9CjF,yBAAqBiF,KAAK;AACpB,UAAArE,gBAAgB,IAAIvB,KAAKJ,IAAI;AACnC,UAAM4B,iBAAiBtB,QAAQ,IAAIF,KAAKE,KAAK,IAAI;AAExCgB,aAAA,SAASU,OAAOL,eAAe,YAAY,CAAC,aAAa/B,QAAQ,SAASE,IAAI,aAAaU,yDAAoBC,EAAE,YAAYG,uDAAmBH,EAAE,YAAYuF,+BAAOvF,EAAE,WAAWO,qDAAkBP,EAAE,YAAYwB,OAAO,WAAW,CAAC,aAAaR,aAAa,eAAejB,mBAAmBG,IAAI,cAAcC,uDAAmBD,IAAI,cAAcG,kBAAkBH,IAAI,aAAaK,iBAAiBL,IAAI,GAAGiF,QAAQhE,cAAc,IAAI,UAAUI,OAAOJ,gBAAgB,YAAY,CAAC,KAAK,EAAE,EAAE;AAAA,EACnf;AACM,QAAAqE,qBAAsBC,cAA0B;AAChDjF,wBAAoBiF,QAAQ;AACtB,UAAAvE,gBAAgB,IAAIvB,KAAKJ,IAAI;AACnC,UAAM4B,iBAAiBtB,QAAQ,IAAIF,KAAKE,KAAK,IAAI;AAEjDgB,aAAS,SAASU,OAAOL,eAAe,YAAY,CAAC,aAAa/B,QAAQ,SAASE,IAAI,aAAaU,yDAAoBC,EAAE,YAAYG,uDAAmBH,EAAE,YAAYK,uDAAmBL,EAAE,WAAWyF,qCAAUzF,EAAE,YAAYwB,OAAO,WAAW,CAAC,aAAaR,aAAa,eAAejB,mBAAmBG,IAAI,cAAcC,kBAAkBD,IAAI,cAAcG,kBAAkBH,IAAI,aAAauF,qCAAUvF,IAAI,GAAGiF,QAAQhE,cAAc,IAAI,UAAUI,OAAOJ,gBAAgB,YAAY,CAAC,KAAK,EAAE,EAEve;AAAA,EACN;AACM,QAAAuE,oBAAoB,OAAOnB,UAAkB;AAC7C,QAAIA,UAAU,UAAU;AAClBtE,4BAAsB;AAAA,QAAED,IAAI;AAAA,QAAME,MAAM;AAAA,MAAG,CAAC;AACtC,YAAAgB,gBAAgB,IAAIvB,KAAKJ,IAAI;AACnC,YAAM4B,iBAAiBtB,QAAQ,IAAIF,KAAKE,KAAK,IAAI;AAEjDgB,eAAS,SAASU,OAAOL,eAAe,YAAY,CAAC,aAAa/B,QAAQ,SAASE,IAAI,aAAa,IAAI,YAAYc,uDAAmBH,EAAE,YAAYK,uDAAmBL,EAAE,WAAWO,iBAAiBP,EAAE,YAAYwB,OAAO,WAAW,CAAC,aAAaR,aAAa,eAAe,EAAE,cAAcb,kBAAkBD,IAAI,cAAcG,kBAAkBH,IAAI,aAAaK,iBAAiBL,IAAI,GAAGiF,QAAQhE,cAAc,IAAI,UAAUI,OAAOJ,gBAAgB,YAAY,CAAC,KAAK,EAAE,EAC9c;AAAA,IAEN,WACSoD,UAAU,SAAS;AACtBnE,2BAAqB;AAAA,QAAEJ,IAAI;AAAA,QAAME,MAAM;AAAA,MAAG,CAAC;AACrC,YAAAgB,gBAAgB,IAAIvB,KAAKJ,IAAI;AACnC,YAAM4B,iBAAiBtB,QAAQ,IAAIF,KAAKE,KAAK,IAAI;AAEjDgB,eAAS,SAASU,OAAOL,eAAe,YAAY,CAAC,aAAa/B,QAAQ,SAASE,IAAI,aAAaU,yDAAoBC,EAAE,YAAY,IAAI,YAAYK,uDAAmBL,EAAE,WAAWO,qDAAkBP,EAAE,YAAYwB,OAAO,WAAW,CAAC,aAAaR,aAAa,eAAejB,mBAAmBG,IAAI,cAAc,EAAE,cAAcG,kBAAkBH,IAAI,aAAaK,iBAAiBL,IAAI,GAAGiF,QAAQhE,cAAc,IAAI,UAAUI,OAAOJ,gBAAgB,YAAY,CAAC,KAAK,EAAE,EAEjd;AAAA,IACN,WACSoD,UAAU,SAAS;AACtBjE,2BAAqB;AAAA,QAAEN,IAAI;AAAA,QAAME,MAAM;AAAA,MAAG,CAAC;AACrC,YAAAgB,gBAAgB,IAAIvB,KAAKJ,IAAI;AACnC,YAAM4B,iBAAiBtB,QAAQ,IAAIF,KAAKE,KAAK,IAAI;AAEjDgB,eAAS,SAASU,OAAOL,eAAe,YAAY,CAAC,aAAa/B,QAAQ,SAASE,IAAI,aAAaU,yDAAoBC,EAAE,YAAYG,uDAAmBH,EAAE,YAAY,IAAI,WAAWO,qDAAkBP,EAAE,YAAYwB,OAAO,WAAW,CAAC,aAAaR,aAAa,eAAejB,mBAAmBG,IAAI,cAAcC,kBAAkBD,IAAI,cAAc,EAAE,aAAaK,iBAAiBL,IAAI,GAAGiF,QAAQhE,cAAc,IAAI,UAAUI,OAAOJ,gBAAgB,YAAY,CAAC,KAAK,EAAE,EAEjd;AAAA,IACN,WACSoD,UAAU,YAAY;AACzB/D,0BAAoB;AAAA,QAAER,IAAI;AAAA,QAAME,MAAM;AAAA,MAAG,CAAC;AACpC,YAAAgB,gBAAgB,IAAIvB,KAAKJ,IAAI;AACnC,YAAM4B,iBAAiBtB,QAAQ,IAAIF,KAAKE,KAAK,IAAI;AAEjDgB,eAAS,SAASU,OAAOL,eAAe,YAAY,CAAC,aAAa/B,QAAQ,SAASE,IAAI,aAAaU,yDAAoBC,EAAE,YAAYG,uDAAmBH,EAAE,YAAYK,uDAAmBL,EAAE,WAAW,IAAI,YAAYwB,OAAO,WAAW,CAAC,aAAaR,aAAa,eAAejB,mBAAmBG,IAAI,cAAcC,kBAAkBD,IAAI,cAAcG,kBAAkBH,IAAI,aAAa,EAAE,IAAIiF,QAAQhE,cAAc,IAAI,UAAUI,OAAOJ,gBAAgB,YAAY,CAAC,KAAK,EAAE,EAEpd;AAAA,IACN;AAAA,EAEN;AACA,QAAM6D,kBAAkBpE,WAAuB;AAC/C,QAAM,CAAC+E,cAAcC,eAAe,IAAIzI,aAAAA,SAAwB,CAAA,CAAE;AAElEC,eAAAA,UAAU,MAAM;;AACN,SAAA4H,MAAAA,gBAAgBa,SAAhBb,gBAAAA,IAAsBc,cAAc;AAClBF,sBAAAZ,gBAAgBa,KAAKC,YAAY;AAAA,IACvD;AAAA,EACN,GAAG,CAACd,gBAAgBa,IAAI,CAAC;AACzB,QAAMd,eAAenE,WAAuB;AAC5C,QAAM,CAACmF,WAAWC,YAAY,IAAI7I,aAAAA,SAAwB,CAAA,CAAE;AACtD,QAAA8I,uBAAwBC,aAAoB;AACjC7B,eAAA1F,OAAOuH,OAAO,CAAC;AACpB,UAAAhF,gBAAgB,IAAIvB,KAAKJ,IAAI;AACnC,UAAM4B,iBAAiBtB,QAAQ,IAAIF,KAAKE,KAAK,IAAI;AAExCgB,aAAA,SAASU,OAAOL,eAAe,YAAY,CAAC,aAAa/B,QAAQ,SAASE,IAAI,aAAaU,mBAAmBC,EAAE,YAAYG,kBAAkBH,EAAE,YAAYK,kBAAkBL,EAAE,WAAWO,iBAAiBP,EAAE,YAAYwB,OAAO,WAAW0E,OAAO,aAAalF,aAAa,eAAejB,mBAAmBG,IAAI,cAAcC,kBAAkBD,IAAI,cAAcG,kBAAkBH,IAAI,aAAaK,iBAAiBL,IAAI,GAAGiF,QAAQhE,cAAc,IAAI,UAAUI,OAAOJ,gBAAgB,YAAY,CAAC,KAAK,EAAE,EAAE;AAAA,EAChgB;AACA/D,eAAAA,UAAU,MAAM;;AACN,SAAA2H,MAAAA,aAAac,SAAbd,gBAAAA,IAAmBe,cAAc;AAClBE,mBAAAjB,aAAac,KAAKC,YAAY;AAAA,IACjD;AAAA,EACH,GAAA,EAACf,kBAAac,SAAbd,mBAAmBe,YAAY,CAAC;AAGpC,QAAMhB,eAAelE,WAAuB;AAC5C,QAAM,CAACuF,WAAWC,YAAY,IAAIjJ,aAAAA,SAAwB,CAAA,CAAE;AAE5DC,eAAAA,UAAU,MAAM;;AACN,SAAA0H,MAAAA,aAAae,SAAbf,gBAAAA,IAAmBgB,cAAc;AAClBM,mBAAAtB,aAAae,KAAKC,YAAY;AAAA,IACjD;AAAA,EACH,GAAA,EAAChB,kBAAae,SAAbf,mBAAmBgB,YAAY,CAAC;AAGpC,QAAMnB,gBAAgB/D,WAAuB;AAG7C,QAAM,CAACyF,YAAYC,aAAa,IAAInJ,aAAAA,SAA2B,CAAA,CAAE;AAEjE,QAAMoJ,mBAAmBF,WAAWhD,IAAI,CAAC;AAAA,IAAEmD;AAAAA,IAAUtB;AAAAA,EAAO,OAAO;AAAA,IAC7DlF,IAAIwG,YAAY;AAAA;AAAA,IAChBtG,MAAMgF;AAAAA,EACZ,EAAE;AAEF9H,eAAAA,UAAU,MAAM;;AACVsE,YAAQC,IAAI,iBAAiB;AAEzB,SAAAgD,MAAAA,cAAckB,SAAdlB,gBAAAA,IAAoB0B,YAAY;AAChBC,oBAAA3B,cAAckB,KAAKQ,UAAU;AAC3C3E,cAAQC,IAAIgD,cAAckB,KAAKQ,YAAY,kBAAkB;AAAA,IACnE;AAAA,EACH,GAAA,EAAC1B,mBAAckB,SAAdlB,mBAAoB0B,UAAU,CAAC;AAG3B3E,UAAAC,IAAI0E,YAAY,qBAAqB;AAI7CjJ,eAAAA,UAAU,MAAM;AACV,QAAIuD,QAAQkF,QAAQlF,QAAQ8F,UAAU,UAAU9F,QAAQkF,SAASpF,eAAe;AAC1EC,uBAAiBC,QAAQkF,IAAI;AAAA,IACnC;AAAA,KACH,CAAClF,QAAQkF,MAAMlF,QAAQ8F,KAAK,CAAC;AAChC,QAAM,CAACjF,SAASkF,UAAU,IAAIvJ,aAAAA,SAAS,EAAE;AACzC,QAAM,CAACwJ,kBAAkBC,mBAAmB,IAAIzJ,aAAAA,SAAS,KAAK;AAC9D,QAAM,CAAC0J,eAAeC,gBAAgB,IAAI3J,aAAAA,SAAS,KAAK;AACxD,QAAM,CAAC4J,MAAMC,OAAO,IAAI7J,aAAAA,SAAS,KAAK;AAItC,QAAM8J,kBAAkBA,CAAC1C,OAAe2C,YAAoBhH,SAAiB;AAEvE,QAAIqE,UAAU,cAAc;AACtBnF,kBAAY,WAAW;AACjB,YAAA8B,gBAAgB,IAAIvB,KAAKJ,IAAI;AACnC,YAAM4B,iBAAiBtB,QAAQ,IAAIF,KAAKE,KAAK,IAAI;AAEjDI,4BAAsB;AAAA,QAAED,IAAIkH;AAAAA,QAAYhH;AAAAA,MAAW,CAAC;AAGpDW,eAAS,SAASU,OAAOL,eAAe,YAAY,CAAC,4BAA4B7B,IAAI,aAAa6H,UAAU,YAAY/G,kBAAkBH,EAAE,YAAYK,kBAAkBL,EAAE,WAAWO,iBAAiBP,EAAE,YAAYwB,OAAO,WAAW,CAAC,aAAaR,aAAa,eAAed,IAAI,cAAcC,kBAAkBD,IAAI,cAAcG,kBAAkBH,IAAI,aAAaK,iBAAiBL,IAAI,GAAGiF,QAAQhE,cAAc,IAAI,UAAUI,OAAOJ,gBAAgB,YAAY,CAAC,KAAK,EAAE,EACld;AAAA,IACN,WACSoD,UAAU,aAAa;AAC1BnF,kBAAY,WAAW;AACjB,YAAA8B,gBAAgB,IAAIvB,KAAKJ,IAAI;AACnC,YAAM4B,iBAAiBtB,QAAQ,IAAIF,KAAKE,KAAK,IAAI;AAEjDO,2BAAqB;AAAA,QAAEJ,IAAIkH;AAAAA,QAAYhH;AAAAA,MAAW,CAAC;AAG1CW,eAAA,SAASU,OAAOL,eAAe,YAAY,CAAC,4BAA4B7B,IAAI,aAAaU,mBAAmBC,EAAE,YAAYkH,UAAU,YAAY7G,kBAAkBL,EAAE,WAAWO,iBAAiBP,EAAE,YAAYwB,OAAO,WAAW,CAAC,aAAaR,aAAa,eAAejB,mBAAmBG,IAAI,cAAcA,IAAI,cAAcG,kBAAkBH,IAAI,aAAaK,iBAAiBL,IAAI,GAAGiF,QAAQhE,cAAc,IAAI,UAAUI,OAAOJ,gBAAgB,YAAY,CAAC,KAAK,EAAE,EAAE;AAAA,IAC5d,WACSoD,UAAU,gBAAgB;AAC7BnF,kBAAY,WAAW;AACjB,YAAA8B,gBAAgB,IAAIvB,KAAKJ,IAAI;AACnC,YAAM4B,iBAAiBtB,QAAQ,IAAIF,KAAKE,KAAK,IAAI;AAEjDW,0BAAoB;AAAA,QAAER,IAAIkH;AAAAA,QAAYhH;AAAAA,MAAW,CAAC;AAGzCW,eAAA,SAASU,OAAOL,eAAe,YAAY,CAAC,4BAA4B7B,IAAI,aAAaU,mBAAmBC,EAAE,YAAYG,kBAAkBH,EAAE,YAAYK,kBAAkBL,EAAE,WAAWkH,UAAU,YAAY1F,OAAO,WAAW,CAAC,aAAaR,aAAa,eAAejB,mBAAmBG,IAAI,cAAcC,kBAAkBD,IAAI,cAAcG,kBAAkBH,IAAI,aAAaA,IAAI,GAAGiF,QAAQhE,cAAc,IAAI,UAAUI,OAAOJ,gBAAgB,YAAY,CAAC,KAAK,EAAE,EAAE;AAAA,IAC9d,WACSoD,UAAU,aAAa;AAC1BnF,kBAAY,UAAU;AAChB,YAAA8B,gBAAgB,IAAIvB,KAAKJ,IAAI;AACnC,YAAM4B,iBAAiBtB,QAAQ,IAAIF,KAAKE,KAAK,IAAI;AAEjDS,2BAAqB;AAAA,QAAEN,IAAIkH;AAAAA,QAAYhH;AAAAA,MAAW,CAAC;AAG1CW,eAAA,SAASU,OAAOL,eAAe,YAAY,CAAC,2BAA2B7B,IAAI,aAAaU,mBAAmBC,EAAE,YAAYG,kBAAkBH,EAAE,YAAYkH,UAAU,WAAW3G,iBAAiBP,EAAE,YAAYwB,OAAO,WAAW,CAAC,aAAaR,aAAa,eAAejB,mBAAmBG,IAAI,cAAcC,kBAAkBD,IAAI,cAAcA,IAAI,aAAaK,iBAAiBL,IAAI,GAAGiF,QAAQhE,cAAc,IAAI,UAAUI,OAAOJ,gBAAgB,YAAY,CAAC,KAAK,EAAE,EAAE;AAAA,IAC3d;AAAA,EAIN;AACA/D,eAAAA,UAAU,MAAM;AACV,QAAImC,QAAQJ,YAAYE,SAAQmC,mCAAS2F,WAAU,KAAKtH,OAAO;AAC5CoB,mBAAA;AAAA,IACnB;AAAA,EACN,GAAG,CAAC1B,MAAMJ,UAAUE,MAAMmC,SAAS3B,KAAK,CAAC;AAEzC,QAAM,CAACuH,QAAQC,SAAS,IAAIlK,aAAAA,SAAS,KAAK;AAC1C,QAAM,CAACmK,WAAWC,YAAY,IAAIpK,aAAAA,SAAS,KAAK;AAG1C,QAAAqK,iBAAiB,OAAO/E,cAAsBV,QAAiB;AACzD,UAAAb,gBAAgB,IAAIvB,KAAKJ,IAAI;AACnC,UAAM4B,iBAAiBsB,eAAe,IAAI9C,KAAK8C,YAAY,IAAI;AACtD5B,aAAA,SAASU,OAAOL,eAAe,YAAY,CAAC,6BAA6B7B,IAAI,aAAaU,mBAAmBC,EAAE,YAAYG,kBAAkBH,EAAE,YAAY+B,2BAAK/B,EAAE,WAAWO,iBAAiBP,EAAE,YAAYwB,OAAO,WAAW,CAAC,aAAaR,aAAa,eAAejB,mBAAmBG,IAAI,cAAcC,kBAAkBD,IAAI,cAAc6B,2BAAK7B,IAAI,aAAaK,iBAAiBL,IAAI,GAAGiF,QAAQhE,cAAc,IAAI,UAAUI,OAAOJ,gBAAgB,YAAY,CAAC,KAAK,EAAE,EAAE;AACzd,UAAM2F,iBAAiB,IAAI;AAAA,EAEjC;AAEA,gDAEO,OACK;AAAA,IAAAW,UAAA,CAAC/K,kCAAA,IAAA,OAAA;AAAA,MAAIgL,WAAU;AAAA,MACTD,UAAA/K,kCAAA,IAAC;QAAGgL,WAAU;AAAA,QAAqBD;MAAc,CAAA;AAAA,IACvD,CAAA,GAGA/K,kCAAA,IAAC,OAAI;AAAA,MAAAgL,WAAU;AAAA,MACTD,UAAAhL,kCAAAA,KAACkE,QAAQgH,MAAR;AAAA,QACK9C,QAAO;AAAA,QACP6C,WAAU;AAAA,QAGVD,UAAA,CAAAhL,kCAAA,KAACmL,SAAQ;AAAA,UAAAb,MAAMK;AAAAA,UAAQS,cAAcR;AAAAA,UAC/BI,UAAA,CAAC/K,kCAAA,IAAAoL,gBAAA;AAAA,YAAeC,SAAO;AAAA,YACjBN,UAAAhL,kCAAA,KAACuL,QAAA;AAAA,cAAOC,SAAQ;AAAA,cAAUP,WAAU;AAAA,cAAsBQ,SAASA,MAAMb,UAAWc,UAAS,CAACA,IAAI;AAAA,cAE5FV,UAAA,CAAC/K,kCAAA,IAAA0L,UAAA;AAAA,gBAAaV,WAAU;AAAA,cAAe,CAAA,GACtCnI,OAAOgC,OAAOhC,MAAM,KAAK,IAAI,aAAA;AAAA,YACpC,CAAA;AAAA,UACN,CAAA,GACA7C,kCAAA,IAAC2L,gBAAe;AAAA,YAAAX,WAAU;AAAA,YACpBD,UAAA/K,kCAAA,IAAC4L,YAAA;AAAA,cACKC,MAAK;AAAA,cACLC,UAAUjJ;AAAAA,cACVkJ,UAAWC,SAAQ;AACT,oBAAAA,aAAaA,GAAG;AACpBrB,0BAAU,KAAK;AAAA,cAErB;AAAA,cACAsB,cAAY;AAAA,YAElB,CAAA;AAAA,UACN,CAAA,CAAA;AAAA,QACN,CAAA,GAGClM,kCAAA,KAAAmM,QAAA;AAAA,UAAOrE,OAAOpF;AAAAA,UAAU0J,eAAezJ;AAAAA,UAClCqI,UAAA,CAAA/K,kCAAA,IAACoM;YAAcpB,WAAU;AAAA,YACnBD,gDAACsB,aAAY;AAAA,cAAAC,aAAY;AAAA,YAAmB,CAAA;AAAA,UAClD,CAAA,0CACCC,eACK;AAAA,YAAAxB,UAAA,CAAC/K,kCAAA,IAAAwM,YAAA;AAAA,cAAW3E,OAAM;AAAA,cAAakD,UAAW;AAAA,YAAA,CAAA,GACzC/K,kCAAA,IAAAwM,YAAA;AAAA,cAAW3E,OAAM;AAAA,cAAYkD,UAAS;AAAA,YAAA,CAAA,GACtC/K,kCAAA,IAAAwM,YAAA;AAAA,cAAW3E,OAAM;AAAA,cAAekD,UAAa;AAAA,YAAA,CAAA,GAC7C/K,kCAAA,IAAAwM,YAAA;AAAA,cAAW3E,OAAM;AAAA,cAAYkD,UAAU;AAAA,YAAA,CAAA,GACvC/K,kCAAA,IAAAwM,YAAA;AAAA,cAAW3E,OAAM;AAAA,cAAWkD,UAAS;AAAA,YAAA,CAAA,GACrC/K,kCAAA,IAAAwM,YAAA;AAAA,cAAW3E,OAAM;AAAA,cAAMkD,UAAG;AAAA,YAAA,CAAA,CAAA;AAAA,UAEjC,CAAA,CAAA;AAAA,QACN,CAAA,GAGAhL,kCAAA,KAACmM;UAAOrE,OAAOlF,OAAOA,OAAO5B,aAAac,SAAY;AAAA,UAAAsK,eAAevJ;AAAAA,UAC/DmI,UAAA,CAAA/K,kCAAA,IAACoM;YAAcpB,WAAU;AAAA,YACnBD,gDAACsB,aAAY;AAAA,cAAAC,aAAY;AAAA,YAAc,CAAA;AAAA,UAC7C,CAAA,0CACCC,eACK;AAAA,YAAAxB,UAAA,CAAC/K,kCAAA,IAAAwM,YAAA;AAAA,cAAW3E,OAAM;AAAA,cAAIkD,UAAM;AAAA,YAAA,CAAA,GAC3B/K,kCAAA,IAAAwM,YAAA;AAAA,cAAW3E,OAAM;AAAA,cAAKkD,UAAO;AAAA,YAAA,CAAA,CAAA;AAAA,UACpC,CAAA,CAAA;AAAA,QACN,CAAA,CAAA;AAAA,MACN,CAAA;AAAA,IACN,CAAA,GACAhL,kCAAA,KAAC,OAAI;AAAA,MAAAiL,WAAU;AAAA,MACRD,UAAA,CAAA,CACK;AAAA,QAAE0B,OAAO;AAAA,QAAUtD,MAAM9F;AAAAA,QAAoBqJ,MAAM;AAAA,MAAS,GAC5D;AAAA,QAAED,OAAO;AAAA,QAAStD,MAAM1F;AAAAA,QAAmBiJ,MAAM;AAAA,MAAQ,GACzD;AAAA,QAAED,OAAO;AAAA,QAAQtD,MAAMxF;AAAAA,QAAmB+I,MAAM;AAAA,MAAQ,GACxD;AAAA,QAAED,OAAO;AAAA,QAAQtD,MAAMtF;AAAAA,QAAkB6I,MAAM;AAAA,MAAW,CAAA,EAE9D/F,IAAI,CAAC;AAAA,QAAE8F;AAAAA,QAAOtD;AAAAA,QAAMuD;AAAAA,MAAK,MACrB3M,kCAAAA,KAAC,OAAA;AAAA,QAEKiL,WAAU;AAAA,QAEVD,UAAA,CAAChL,kCAAA,KAAA,KAAA;AAAA,UAAEiL,WAAU;AAAA,UACND,UAAA,CAAA0B,OAAM,MAAGzM,kCAAAA,IAAA,QAAA;AAAA,YAAKgL,WAAU;AAAA,YAA2BD,uCAAMvH;AAAAA,UAAK,CAAA,CAAA;AAAA,QACrE,CAAA,GACC,EAAC2F,6BAAM7F,MACFtD,kCAAAA,IAACsL,QAAA;AAAA,UACKC,SAAQ;AAAA,UACRoB,MAAK;AAAA,UACL3B,WAAU;AAAA,UACVQ,SAASA,MAAM5D,gBAAgB8E,IAAI;AAAA,UACxC3B,UAAA;AAAA,QAAA,CAED,IAEA/K,kCAAA,IAACsL,QAAA;AAAA,UACKC,SAAQ;AAAA,UACRoB,MAAK;AAAA,UACL3B,WAAU;AAAA,UACVQ,SAASA,MAAMxC,kBAAkB0D,IAAI;AAAA,UAErC3B,UAAA/K,kCAAA,IAAC4M,SAAQ;AAAA,YAAA5B,WAAU;AAAA,UAAU,CAAA;AAAA,QAAA,CACnC,CAAA;AAAA,MAAA,GAvBD0B,IAyBX,CACL,GAGD3M,kCAAA,KAAC,OAAI;AAAA,QAAAiL,WAAU;AAAA,QACTD,UAAA,CAAC/K,kCAAA,IAAA,KAAA;AAAA,UAAEgL,WAAU;AAAA,UAAsCD,UAAK;AAAA,SAAA,GACvDd,mBACKlK,kCAAA,KAAC,OAAI;AAAA,UAAAiL,WAAU;AAAA,UACTD,UAAA,CAAA/K,kCAAA,IAAC,SAAA;AAAA,YACK0M,MAAK;AAAA,YACL7E,OAAO/C;AAAAA,YACP+H,UAAWC,OAAM9C,WAAW8C,EAAEC,OAAOlF,KAAK;AAAA,YAC1CmD,WAAU;AAAA,UAAA,CAChB,GACAhL,kCAAA,IAACsL,QAAA;AAAA,YACKC,SAAQ;AAAA,YACRoB,MAAK;AAAA,YACL3B,WAAU;AAAA,YACVQ,SAASA,MAAM;AACTxB,yBAAW,EAAE;AACbE,kCAAoB,KAAK;AAAA,YAC/B;AAAA,YAEAa,UAAA/K,kCAAA,IAAC4M,SAAQ;AAAA,cAAA5B,WAAU;AAAA,YAAU,CAAA;AAAA,UAAA,CACnC,CAAA;AAAA,QAAA,CACN,IAEAhL,kCAAA,IAACsL,QAAA;AAAA,UACKC,SAAQ;AAAA,UACRoB,MAAK;AAAA,UACL3B,WAAU;AAAA,UACVQ,SAASA,MAAMtB,oBAAoB,IAAI;AAAA,UAC5Ca,UAAA;AAAA,QAAA,CAED,CAAA;AAAA,MAEZ,CAAA,GACAhL,kCAAA,KAAC,OAAI;AAAA,QAAAiL,WAAU;AAAA,QACTD,UAAA,CAAC/K,kCAAA,IAAA,KAAA;AAAA,UAAEgL,WAAU;AAAA,UAAsCD,UAAO;AAAA,SAAA,GACzDZ,gBACKpK,kCAAA,KAAC,OAAI;AAAA,UAAAiL,WAAU;AAAA,UACTD,UAAA,CAAAhL,kCAAA,KAACmL,SAAQ;AAAA,YAAAb,MAAMO;AAAAA,YAAWO,cAAcN;AAAAA,YAClCE,UAAA,CAAA/K,kCAAA,IAACoL;cAAeC,SAAO;AAAA,cACjBN,UAAChL,kCAAA,KAAAuL,QAAA;AAAA,gBAAOC,SAAQ;AAAA,gBAAUP,WAAU;AAAA,gBAAsBQ,SAASA,MAAMX,aAAcY,UAAS,CAACA,IAAI;AAAA,gBAC/FV,UAAA,CAAC/K,kCAAA,IAAA0L,UAAA;AAAA,kBAAaV,WAAU;AAAA,gBAAe,CAAA,GACtC7H,QAAQ0B,OAAO1B,OAAO,KAAK,IAAI,aAAA;AAAA,cACtC,CAAA;AAAA,YACN,CAAA,GACAnD,kCAAA,IAAC2L,gBAAe;AAAA,cAAAX,WAAU;AAAA,cACpBD,UAAA/K,kCAAA,IAAC4L,YAAA;AAAA,gBACKC,MAAK;AAAA,gBACLC,UAAU3I;AAAAA,gBACV4I,UAAWC,SAAQ;AACT,sBAAAA,cAAcA,GAAG;AACrBnB,+BAAa,KAAK;AAAA,gBACxB;AAAA,gBACAoB,cAAY;AAAA,cAClB,CAAA;AAAA,YACN,CAAA,CAAA;AAAA,UACN,CAAA,GACAjM,kCAAA,IAACsL,QAAA;AAAA,YACKC,SAAQ;AAAA,YACRoB,MAAK;AAAA,YACL3B,WAAU;AAAA,YACVQ,SAASA,MAAM;AAETpI,uBAAS,MAAS;AAElBgH,+BAAiB,KAAK;AAAA,YAC5B;AAAA,YAEAW,UAAA/K,kCAAA,IAAC4M,SAAQ;AAAA,cAAA5B,WAAU;AAAA,YAAU,CAAA;AAAA,UAAA,CACnC,CAAA;AAAA,QAAA,CACN,IAEAhL,kCAAA,IAACsL,QAAA;AAAA,UACKC,SAAQ;AAAA,UACRoB,MAAK;AAAA,UACL3B,WAAU;AAAA,UACVQ,SAASA,MAAMpB,iBAAiB,IAAI;AAAA,UACzCW,UAAA;AAAA,QAAA,CAED,CAAA;AAAA,MAEZ,CAAA,CAAA;AAAA,IACN,CAAA,GACAhL,kCAAA,KAAC,OAAI;AAAA,MAAAiL,WAAU;AAAA,MACTD,UAAA,CAAC/K,kCAAA,IAAA,OAAA;AAAA,QAAIgL,WAAU;AAAA,QACRD,UAAAzE,YAAYmE,WAAW,IAClB1K,kCAAAA,KAAC,SAAM;AAAA,UAAAiL,WAAU;AAAA,UACXD,UAAA,CAAA/K,kCAAA,IAAC,SACK;AAAA,YAAA+K,UAAAhL,kCAAA,KAAC,MAAG;AAAA,cAAAiL,WAAU;AAAA,cACRD,UAAA,CAAC/K,kCAAA,IAAA,MAAA;AAAA,gBAAGgL,WAAU;AAAA,gBAA8BD,UAAI;AAAA,eAAA,GAAK,KACrD/K,kCAAA,IAAC;gBAAGgL,WAAU;AAAA,gBAA+BD,iBAAOzE,YAAY,CAAC,GAAG,WAAW;AAAA,cAAE,CAAA,GAAK,GAAA;AAAA,YAC5F,CAAA;AAAA,UACN,CAAA,GACAtG,kCAAA,IAAC;YACM+K,UAAmBlE,mBAAAF,aAEb5G;;AAAAA,uDAAAA,KAAA,MAAA;AAAA,gBAAgBiL,WAAU;AAAA,gBAErBD,UAAA,CAAA/K,kCAAA,IAAC,MAAA;AAAA,kBACKgL,WAAU;AAAA,kBACVQ,SAASA,MAAMjB,gBAAgBxG,+CAAetB,UAAUR,OAAOoD,IAAI/B,EAAE,GAAG+B,IAAI7B,IAAI;AAAA,kBAEhFuH,UAAA/K,kCAAA,IAACgN,iBACK;AAAA,oBAAAjC,UAAAhL,kCAAA,KAACkN,WACK;AAAA,sBAAAlC,UAAA,CAAC/K,kCAAA,IAAAkN,gBAAA;AAAA,wBAAe7B,SAAO;AAAA,wBACjBN,UAAA/K,kCAAA,IAAC;0BAAIgL,WAAU;AAAA,0BACRD,UAAI1F,IAAA7B;AAAAA,wBACX,CAAA;AAAA,sBACN,CAAA,GACCf,aAAa,eAAe1C,kCAAAA,KAACoN,gBACxB;AAAA,wBAAApC,UAAA,CAAAhL,kCAAA,KAAC,KAAE;AAAA,0BAAAgL,UAAA,CAAA,gBAAY1F,2BAAKI,cAAa,GAAA;AAAA,wBAAI,CAAA,0CACpC,KAAE;AAAA,0BAAAsF,UAAA,CAAA,kBAAc1F,2BAAKM,sBAAqB,GAAA;AAAA,wBAAI,CAAA,CAAA;AAAA,sBACrD,CAAA,CAAA;AAAA,oBACN,CAAA;AAAA,kBACN,CAAA;AAAA,gBAAA,CACN,GAGA3F,kCAAA,IAAC,MAAG;AAAA,kBAAAgL,WAAU;AAAA,kBACRD,UAAAhL,kCAAA,KAAC,OAAI;AAAA,oBAAAiL,WAAW,qDAAmD3F,MAAAA,2BAAKE,kBAALF,gBAAAA,IAAoBtC,MAAM,KAAK,OAAMuD,YAAY,CAAC,KAAKjB,IAAI,GAAGiB,YAAY,CAAC,CAAC,WAAW,IAAI,kBAAkB,EAAE;AAAA,oBAC5KyE,UAAA,CAAA/K,kCAAA,IAAC,UAAA;AAAA,sBACKgL,WAAW,8BAA8BvI,aAAa,cAAc,mCAAmC,EAAE;AAAA,sBACzG+I,SAASA,MAAMV,eAAexE,YAAY,CAAC,GAAGjB,GAAG;AAAA,sBACjD,iBAAe5C,aAAa;AAAA,sBAG3BsI,cAAI,GAAGzE,YAAY,CAAC,CAAC,MAAM,KAAK;AAAA,oBAAA,CACvC,yCACC,QAAK;AAAA,sBAAA0E,WAAU;AAAA,sBAAeD,UAAA1F,IAAI,GAAGiB,YAAY,CAAC,CAAC,QAAQ,KAAMtG,kCAAA,IAAAoN,OAAA;AAAA,wBAAMC,OAAM;AAAA,wBAAMV,MAAM;AAAA,sBAAI,CAAA;AAAA,oBAAG,CAAA,GACjG3M,kCAAA,IAAC,QAAK;AAAA,sBAAAgL,WAAU;AAAA,sBAAeD,UAAA1F,IAAI,GAAGiB,YAAY,CAAC,CAAC,SAAS,KAAK,CAACjB,IAAI,GAAGiB,YAAY,CAAC,CAAC,WAAW,KAAMtG,kCAAA,IAAAsN,QAAA;AAAA,wBAAOD,OAAM;AAAA,wBAASV,MAAM;AAAA,sBAAI,CAAA;AAAA,oBAAG,CAAA,yCAC3I,QAAK;AAAA,sBAAA3B,WAAU;AAAA,sBAAeD,UAAA1F,IAAI,GAAGiB,YAAY,CAAC,CAAC,OAAO,KAAMtG,kCAAA,IAAAuN,WAAA;AAAA,wBAAUF,OAAM;AAAA,wBAAQV,MAAM;AAAA,sBAAI,CAAA;AAAA,oBAAG,CAAA,CAAA;AAAA,kBAC5G,CAAA;AAAA,gBACN,CAAA,CAAA;AAAA,cApCG,GAAAtH,IAAI/B,EAqCb;AAAA,aACL;AAAA,UACP,CAAA,CAAA;AAAA,QAAA,CACN,IAEAtD,kCAAA,IAACwN,iBAAA;AAAA,UACK5G;AAAAA,UACAuC,MAAMtC;AAAAA,UACN4G,WAAYpI,SAGLtF,kCAAA,KAAA,MAAA;AAAA,YAAgBiL,WAAU;AAAA,YACrBD,UAAA,CAAA/K,kCAAA,IAAC,MAAA;AAAA,cACKgL,WAAU;AAAA,cACVQ,SAASA,MAAMjB,gBAAgBxG,+CAAetB,UAAUR,OAAOoD,IAAI/B,EAAE,GAAG+B,IAAI7B,IAAI;AAAA,cAEhFuH,UAAA/K,kCAAA,IAACgN,iBACK;AAAA,gBAAAjC,UAAAhL,kCAAA,KAACkN,WACK;AAAA,kBAAAlC,UAAA,CAAC/K,kCAAA,IAAAkN,gBAAA;AAAA,oBAAe7B,SAAO;AAAA,oBACjBN,UAAA/K,kCAAA,IAAC;sBAAKgL,WAAU;AAAA,sBAA4BD,UAAI1F,IAAA7B;AAAAA,oBAAK,CAAA;AAAA,kBAC3D,CAAA,GACCf,aAAa,eAAe1C,kCAAAA,KAACoN,gBACxB;AAAA,oBAAApC,UAAA,CAAAhL,kCAAA,KAAC,KAAE;AAAA,sBAAAgL,UAAA,CAAA,gBAAY1F,2BAAKI,cAAa,GAAA;AAAA,oBAAI,CAAA,0CACpC,KAAE;AAAA,sBAAAsF,UAAA,CAAA,kBAAc1F,2BAAKK,gBAAe,GAAA;AAAA,oBAAI,CAAA,CAAA;AAAA,kBAC/C,CAAA,CAAA;AAAA,gBACN,CAAA;AAAA,cACN,CAAA;AAAA,YACN,CAAA,GAECY,YAAYK,IAAI9D,WACX7C;;AAAAA,uDAAAA,IAAC,MAA6B;AAAA,gBAAAgL,WAAU;AAAA,gBAClCD,UAAAhL,kCAAA,KAAC,OAAI;AAAA,kBAAAiL,WAAW,qDAAmD3F,MAAAA,2BAAKE,kBAALF,gBAAAA,IAAoBtC,MAAM,KAAK,OAAMF,SAAQwC,IAAI,GAAGxC,KAAI,WAAW,IAAI,kBAAkB,EAAE;AAAA,kBACxJkI,UAAA,CAAA/K,kCAAA,IAAC,UAAA;AAAA,oBACKgL,WAAW,eAAevI,aAAa,cAAc,mCAAmC,gBAAgB;AAAA,oBACxG+I,SAASA,MAAMV,eAAejI,OAAMwC,GAAG;AAAA,oBACvCqI,UAAUjL,aAAa;AAAA,oBAEtBsI,UAAI1F,IAAA,GAAGxC,KAAI,MAAM,KAAK;AAAA,kBAAA,CAC7B,GACC7C,kCAAA,IAAA,QAAA;AAAA,oBAAKgL,WAAU;AAAA,oBAAeD,cAAI,GAAGlI,KAAI,QAAQ,2CAAMuK,OAAM;AAAA,sBAAAC,OAAM;AAAA,sBAAMV,MAAM;AAAA,oBAAI,CAAA;AAAA,kBAAG,CAAA,GACvF3M,kCAAA,IAAC;oBAAKgL,WAAU;AAAA,oBAAeD,cAAI,GAAGlI,KAAI,SAAS,KAAK,CAACwC,IAAI,GAAGxC,KAAI,WAAW,KAAK7C,kCAAAA,IAACsN;sBAAOD,OAAM;AAAA,sBAASV,MAAM;AAAA,oBAAI,CAAA;AAAA,kBAAG,CAAA,GACvH3M,kCAAA,IAAA,QAAA;AAAA,oBAAKgL,WAAU;AAAA,oBAAeD,cAAI,GAAGlI,KAAI,OAAO,2CAAM0K,WAAU;AAAA,sBAAAF,OAAM;AAAA,sBAAQV,MAAM;AAAA,oBAAI,CAAA;AAAA,kBAAG,CAAA,CAAA;AAAA,gBAClG,CAAA;AAAA,iBAZG,GAAGtH,IAAI/B,EAAE,IAAIT,KAAI,EAa1B;AAAA,aACL,CAAA;AAAA,UAAA,GAjCEwC,IAAI/B,EAkCb;AAAA,QAEZ,CAAA;AAAA,MAEZ,CAAA,GAOAvD,kCAAA,KAAC,OAAI;AAAA,QAAAiL,WAAU;AAAA,QACTD,UAAA,CAAChL,kCAAA,KAAA,MAAA;AAAA,UAAGiL,WAAU;AAAA,UAAWD,UAAA,CAAA,kBAAerD,UAAU,CAAA;AAAA,QAAE,CAAA,GACpD1H,kCAAA,IAAC,OAAI;AAAA,UAAAgL,WAAU;AAAA,UACTD,UAAA/K,kCAAA,IAAC2N,sBAAA;AAAA,YACKC,YAAY;AAAA,YACZC,aAAanG;AAAAA,YACboG,cAAepG,cAAY6B,qBAAqB7B,SAAQ7F,SAAU,CAAA;AAAA,UAExE,CAAA;AAAA,QACN,CAAA,CAAA;AAAA,MACN,CAAA,CAAA;AAAA,IACN,CAAA,yCACC,OACK;AAAA,MAAAkJ,UAAA/K,kCAAA,IAAC+N,wBAAuB;AAAA,QAAAC,WAAWjK;AAAAA,MAAe,CAAA;AAAA,IACxD,CAAA,GAGA/D,kCAAA,IAACiO;MAAmBC,OAAO;AAAA,MAAwBxD,QAAQxD;AAAAA,MAAgBiH,SAASA,MAAMhH,kBAAkB,KAAK;AAAA,MAAGiH,OAAOvE,oBAAoB,CAAI;AAAA,MAAAkC,UAAWvD,YAAWD,qBAAqBC,MAAM;AAAA,IAAG,CAAA,GACvMxI,kCAAA,IAACiO;MAAmBC,OAAO;AAAA,MAAuBxD,QAAQtD;AAAAA,MAAqB+G,SAASA,MAAM9G,uBAAuB,KAAK;AAAA,MAAG+G,OAAO3E,aAAa,CAAI;AAAA,MAAAsC,UAAWpD,WAAUD,oBAAoBC,KAAK;AAAA,IAAG,CAAA,GACtM3I,kCAAA,IAACqO;MAA0BH,OAAO;AAAA,MAAuBxD,QAAQpD;AAAAA,MAAqB6G,SAASA,MAAM5G,uBAAuB,KAAK;AAAA,MAAG+G,UAAUjF,aAAa;MAAI0C,UAAWlD,WAAUD,oBAAoBC,KAAK;AAAA,MAAG6D,MAAK;AAAA,IAAQ,CAAA,GAC7N1M,kCAAA,IAACqO;MAA0BH,OAAO;AAAA,MAA0BxD,QAAQlD;AAAAA,MAAoB2G,SAASA,MAAM1G,sBAAsB,KAAK;AAAA,MAAG6G,UAAUrF,gBAAgB;MAAI8C,UAAWhD,cAAaD,mBAAmBC,QAAQ;AAAA,MAAG2D,MAAK;AAAA,IAAO,CAAA,CAAA;AAAA,EAE3O,CAAA;AAGZ;", "x_google_ignoreList": [0]}