{"version": 3, "file": "index-BTdCMChR.js", "sources": ["../../../node_modules/@radix-ui/react-direction/dist/index.mjs"], "sourcesContent": ["// packages/react/direction/src/Direction.tsx\nimport * as React from \"react\";\nimport { jsx } from \"react/jsx-runtime\";\nvar DirectionContext = React.createContext(void 0);\nvar DirectionProvider = (props) => {\n  const { dir, children } = props;\n  return /* @__PURE__ */ jsx(DirectionContext.Provider, { value: dir, children });\n};\nfunction useDirection(localDir) {\n  const globalDir = React.useContext(DirectionContext);\n  return localDir || globalDir || \"ltr\";\n}\nvar Provider = DirectionProvider;\nexport {\n  DirectionProvider,\n  Provider,\n  useDirection\n};\n//# sourceMappingURL=index.mjs.map\n"], "names": ["React.createContext", "React.useContext"], "mappings": ";AAGA,IAAI,mBAAmBA,aAAmB,cAAC,MAAM;AAKjD,SAAS,aAAa,UAAU;AAC9B,QAAM,YAAYC,aAAgB,WAAC,gBAAgB;AACnD,SAAO,YAAY,aAAa;AAClC;", "x_google_ignoreList": [0]}