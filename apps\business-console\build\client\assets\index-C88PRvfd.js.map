{"version": 3, "file": "index-C88PRvfd.js", "sources": ["../../../node_modules/@radix-ui/react-visually-hidden/dist/index.mjs"], "sourcesContent": ["// packages/react/visually-hidden/src/visually-hidden.tsx\nimport * as React from \"react\";\nimport { Primitive } from \"@radix-ui/react-primitive\";\nimport { jsx } from \"react/jsx-runtime\";\nvar NAME = \"VisuallyHidden\";\nvar VisuallyHidden = React.forwardRef(\n  (props, forwardedRef) => {\n    return /* @__PURE__ */ jsx(\n      Primitive.span,\n      {\n        ...props,\n        ref: forwardedRef,\n        style: {\n          // See: https://github.com/twbs/bootstrap/blob/main/scss/mixins/_visually-hidden.scss\n          position: \"absolute\",\n          border: 0,\n          width: 1,\n          height: 1,\n          padding: 0,\n          margin: -1,\n          overflow: \"hidden\",\n          clip: \"rect(0, 0, 0, 0)\",\n          whiteSpace: \"nowrap\",\n          wordWrap: \"normal\",\n          ...props.style\n        }\n      }\n    );\n  }\n);\nVisuallyHidden.displayName = NAME;\nvar Root = VisuallyHidden;\nexport {\n  Root,\n  VisuallyHidden\n};\n//# sourceMappingURL=index.mjs.map\n"], "names": ["React.forwardRef", "jsx"], "mappings": ";;AAIA,IAAI,OAAO;AACR,IAAC,iBAAiBA,aAAgB;AAAA,EACnC,CAAC,OAAO,iBAAiB;AACvB,WAAuBC,kCAAG;AAAA,MACxB,UAAU;AAAA,MACV;AAAA,QACE,GAAG;AAAA,QACH,KAAK;AAAA,QACL,OAAO;AAAA;AAAA,UAEL,UAAU;AAAA,UACV,QAAQ;AAAA,UACR,OAAO;AAAA,UACP,QAAQ;AAAA,UACR,SAAS;AAAA,UACT,QAAQ;AAAA,UACR,UAAU;AAAA,UACV,MAAM;AAAA,UACN,YAAY;AAAA,UACZ,UAAU;AAAA,UACV,GAAG,MAAM;AAAA,QACnB;AAAA,MACA;AAAA,IACK;AAAA,EACL;AACA;AACA,eAAe,cAAc;AAC1B,IAAC,OAAO;", "x_google_ignoreList": [0]}