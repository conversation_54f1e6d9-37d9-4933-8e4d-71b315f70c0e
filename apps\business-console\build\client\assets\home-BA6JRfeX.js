import { r as reactExports, j as jsxRuntimeExports } from "./jsx-runtime-bB2y7OuD.js";
import { S as Slot, u as useComposedRefs, a as Slottable } from "./index-z_byfFrQ.js";
import { c as cva } from "./index-ImHKLo0a.js";
import { c as cn } from "./utils-GkgzjW3c.js";
import { B as Button, b as buttonVariants } from "./button-ByAXMyvk.js";
import { I as Input } from "./input-3v87qohQ.js";
import { S as Separator } from "./separator-35EEKp-L.js";
import { S as Sheet, b as SheetContent } from "./sheet-BP4xlyNq.js";
import { T as TooltipProvider, a as Tooltip, b as TooltipTrigger, c as TooltipContent } from "./tooltip-CmSNYR5K.js";
import { c as createLucideIcon } from "./createLucideIcon-uwkRm45G.js";
import { c as createContextScope, b as composeEventHandlers } from "./index-D7VH9Fc8.js";
import { f as createDialogScope, R as Root, T as Trigger, P as Portal, W as WarningProvider, C as Content, b as Title, D as Description, a as Close, O as Overlay } from "./index-DdafHWkt.js";
import { u as useLoaderData, c as useSubmit, L as Link } from "./components-D7UvGag_.js";
import { c as useLocation, N as Navigate, O as Outlet } from "./index-DhHTcibu.js";
import { T as Truck } from "./truck-ypDO_-A_.js";
import { M as MessageCircle } from "./message-circle-VDmkNDXi.js";
import { M as MapPin } from "./map-pin-BWBTUiG2.js";
import { L as LayoutDashboard, a as LogOut } from "./log-out-C7033MWA.js";
import { S as Store } from "./store-CHb1Nw9Z.js";
import "./index-CzZ6EYkN.js";
import "./index-Vp2vNLNM.js";
import "./x-CCG_WJDF.js";
import "./index-BXzPK7u0.js";
import "./index-DVTNuYOr.js";
import "./index-DscYByPT.js";
import "./index-CkL5tk39.js";
import "./index-QLGF6kQx.js";
import "./index-dIGjFc0I.js";
import "./index-C88PRvfd.js";
import "./index-CEHS9zzk.js";
/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */
const ChartNetwork = createLucideIcon("ChartNetwork", [
  ["path", { d: "m13.11 7.664 1.78 2.672", key: "go2gg9" }],
  ["path", { d: "m14.162 12.788-3.324 1.424", key: "11x848" }],
  ["path", { d: "m20 4-6.06 1.515", key: "1wxxh7" }],
  ["path", { d: "M3 3v16a2 2 0 0 0 2 2h16", key: "c24i48" }],
  ["circle", { cx: "12", cy: "6", r: "2", key: "1jj5th" }],
  ["circle", { cx: "16", cy: "12", r: "2", key: "4ma0v8" }],
  ["circle", { cx: "9", cy: "15", r: "2", key: "lf2ghp" }]
]);
/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */
const ChartNoAxesCombined = createLucideIcon("ChartNoAxesCombined", [
  ["path", { d: "M12 16v5", key: "zza2cw" }],
  ["path", { d: "M16 14v7", key: "1g90b9" }],
  ["path", { d: "M20 10v11", key: "1iqoj0" }],
  [
    "path",
    { d: "m22 3-8.646 8.646a.5.5 0 0 1-.708 0L9.354 8.354a.5.5 0 0 0-.707 0L2 15", key: "1fw8x9" }
  ],
  ["path", { d: "M4 18v3", key: "1yp0dc" }],
  ["path", { d: "M8 14v7", key: "n3cwzv" }]
]);
/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */
const ChartPie = createLucideIcon("ChartPie", [
  [
    "path",
    {
      d: "M21 12c.552 0 1.005-.449.95-.998a10 10 0 0 0-8.953-8.951c-.55-.055-.998.398-.998.95v8a1 1 0 0 0 1 1z",
      key: "pzmjnu"
    }
  ],
  ["path", { d: "M21.21 15.89A10 10 0 1 1 8 2.83", key: "k2fpak" }]
]);
/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */
const Cherry = createLucideIcon("Cherry", [
  ["path", { d: "M2 17a5 5 0 0 0 10 0c0-2.76-2.5-5-5-3-2.5-2-5 .24-5 3Z", key: "cvxqlc" }],
  ["path", { d: "M12 17a5 5 0 0 0 10 0c0-2.76-2.5-5-5-3-2.5-2-5 .24-5 3Z", key: "1ostrc" }],
  ["path", { d: "M7 14c3.22-2.91 4.29-8.75 5-12 1.66 2.38 4.94 9 5 12", key: "hqx58h" }],
  ["path", { d: "M22 9c-4.29 0-7.14-2.33-10-7 5.71 0 10 4.67 10 7Z", key: "eykp1o" }]
]);
/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */
const Contact = createLucideIcon("Contact", [
  ["path", { d: "M16 2v2", key: "scm5qe" }],
  ["path", { d: "M7 22v-2a2 2 0 0 1 2-2h6a2 2 0 0 1 2 2v2", key: "1waht3" }],
  ["path", { d: "M8 2v2", key: "pbkmx" }],
  ["circle", { cx: "12", cy: "11", r: "3", key: "itu57m" }],
  ["rect", { x: "3", y: "4", width: "18", height: "18", rx: "2", key: "12vinp" }]
]);
/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */
const EarthLock = createLucideIcon("EarthLock", [
  ["path", { d: "M7 3.34V5a3 3 0 0 0 3 3", key: "w732o8" }],
  ["path", { d: "M11 21.95V18a2 2 0 0 0-2-2 2 2 0 0 1-2-2v-1a2 2 0 0 0-2-2H2.05", key: "f02343" }],
  ["path", { d: "M21.54 15H17a2 2 0 0 0-2 2v4.54", key: "1djwo0" }],
  ["path", { d: "M12 2a10 10 0 1 0 9.54 13", key: "zjsr6q" }],
  ["path", { d: "M20 6V4a2 2 0 1 0-4 0v2", key: "1of5e8" }],
  ["rect", { width: "8", height: "5", x: "14", y: "6", rx: "1", key: "1fmf51" }]
]);
/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */
const Grape = createLucideIcon("Grape", [
  ["path", { d: "M22 5V2l-5.89 5.89", key: "1eenpo" }],
  ["circle", { cx: "16.6", cy: "15.89", r: "3", key: "xjtalx" }],
  ["circle", { cx: "8.11", cy: "7.4", r: "3", key: "u2fv6i" }],
  ["circle", { cx: "12.35", cy: "11.65", r: "3", key: "i6i8g7" }],
  ["circle", { cx: "13.91", cy: "5.85", r: "3", key: "6ye0dv" }],
  ["circle", { cx: "18.15", cy: "10.09", r: "3", key: "snx9no" }],
  ["circle", { cx: "6.56", cy: "13.2", r: "3", key: "17x4xg" }],
  ["circle", { cx: "10.8", cy: "17.44", r: "3", key: "1hogw9" }],
  ["circle", { cx: "5", cy: "19", r: "3", key: "1sn6vo" }]
]);
/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */
const Headset = createLucideIcon("Headset", [
  [
    "path",
    {
      d: "M3 11h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-5Zm0 0a9 9 0 1 1 18 0m0 0v5a2 2 0 0 1-2 2h-1a2 2 0 0 1-2-2v-3a2 2 0 0 1 2-2h3Z",
      key: "12oyoe"
    }
  ],
  ["path", { d: "M21 16v2a4 4 0 0 1-4 4h-5", key: "1x7m43" }]
]);
/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */
const House = createLucideIcon("House", [
  ["path", { d: "M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8", key: "5wwlr5" }],
  [
    "path",
    {
      d: "M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",
      key: "1d0kgt"
    }
  ]
]);
/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */
const Landmark = createLucideIcon("Landmark", [
  ["line", { x1: "3", x2: "21", y1: "22", y2: "22", key: "j8o0r" }],
  ["line", { x1: "6", x2: "6", y1: "18", y2: "11", key: "10tf0k" }],
  ["line", { x1: "10", x2: "10", y1: "18", y2: "11", key: "54lgf6" }],
  ["line", { x1: "14", x2: "14", y1: "18", y2: "11", key: "380y" }],
  ["line", { x1: "18", x2: "18", y1: "18", y2: "11", key: "1kevvc" }],
  ["polygon", { points: "12 2 20 7 4 7", key: "jkujk7" }]
]);
/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */
const LayoutList = createLucideIcon("LayoutList", [
  ["rect", { width: "7", height: "7", x: "3", y: "3", rx: "1", key: "1g98yp" }],
  ["rect", { width: "7", height: "7", x: "3", y: "14", rx: "1", key: "1bb6yr" }],
  ["path", { d: "M14 4h7", key: "3xa0d5" }],
  ["path", { d: "M14 9h7", key: "1icrd9" }],
  ["path", { d: "M14 15h7", key: "1mj8o2" }],
  ["path", { d: "M14 20h7", key: "11slyb" }]
]);
/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */
const PanelLeft = createLucideIcon("PanelLeft", [
  ["rect", { width: "18", height: "18", x: "3", y: "3", rx: "2", key: "afitv7" }],
  ["path", { d: "M9 3v18", key: "fh3hqa" }]
]);
/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */
const PlaneTakeoff = createLucideIcon("PlaneTakeoff", [
  ["path", { d: "M2 22h20", key: "272qi7" }],
  [
    "path",
    {
      d: "M6.36 17.4 4 17l-2-4 1.1-.55a2 2 0 0 1 1.8 0l.17.1a2 2 0 0 0 1.8 0L8 12 5 6l.9-.45a2 2 0 0 1 2.09.2l4.02 3a2 2 0 0 0 2.1.2l4.19-2.06a2.41 2.41 0 0 1 1.73-.17L21 7a1.4 1.4 0 0 1 .87 1.99l-.38.76c-.23.46-.6.84-1.07 1.08L7.58 17.2a2 2 0 0 1-1.22.18Z",
      key: "fkigj9"
    }
  ]
]);
/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */
const Warehouse = createLucideIcon("Warehouse", [
  [
    "path",
    {
      d: "M22 8.35V20a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V8.35A2 2 0 0 1 3.26 6.5l8-3.2a2 2 0 0 1 1.48 0l8 3.2A2 2 0 0 1 22 8.35Z",
      key: "gksnxg"
    }
  ],
  ["path", { d: "M6 18h12", key: "9pbo8z" }],
  ["path", { d: "M6 14h12", key: "4cwo0f" }],
  ["rect", { width: "12", height: "12", x: "6", y: "10", key: "apd30q" }]
]);
const MOBILE_BREAKPOINT = 768;
function useIsMobile() {
  const [isMobile, setIsMobile] = reactExports.useState(void 0);
  reactExports.useEffect(() => {
    const mql = window.matchMedia(`(max-width: ${MOBILE_BREAKPOINT - 1}px)`);
    const onChange = () => {
      setIsMobile(window.innerWidth < MOBILE_BREAKPOINT);
    };
    mql.addEventListener("change", onChange);
    setIsMobile(window.innerWidth < MOBILE_BREAKPOINT);
    return () => mql.removeEventListener("change", onChange);
  }, []);
  return !!isMobile;
}
function Skeleton({
  className,
  ...props
}) {
  return /* @__PURE__ */ jsxRuntimeExports.jsx(
    "div",
    {
      className: cn("animate-pulse rounded-md bg-muted", className),
      ...props
    }
  );
}
const SIDEBAR_COOKIE_NAME = "sidebar:state";
const SIDEBAR_COOKIE_MAX_AGE = 60 * 60 * 24 * 7;
const SIDEBAR_WIDTH = "16rem";
const SIDEBAR_WIDTH_MOBILE = "18rem";
const SIDEBAR_WIDTH_ICON = "3rem";
const SIDEBAR_KEYBOARD_SHORTCUT = "b";
const SidebarContext = reactExports.createContext(null);
function useSidebar() {
  const context = reactExports.useContext(SidebarContext);
  if (!context) {
    throw new Error("useSidebar must be used within a SidebarProvider.");
  }
  return context;
}
const SidebarProvider = reactExports.forwardRef(
  ({
    defaultOpen = true,
    open: openProp,
    onOpenChange: setOpenProp,
    className,
    style,
    children,
    ...props
  }, ref) => {
    const isMobile = useIsMobile();
    const [openMobile, setOpenMobile] = reactExports.useState(false);
    const [_open, _setOpen] = reactExports.useState(defaultOpen);
    const open = openProp ?? _open;
    const setOpen = reactExports.useCallback(
      (value) => {
        if (setOpenProp) {
          return setOpenProp == null ? void 0 : setOpenProp(
            typeof value === "function" ? value(open) : value
          );
        }
        _setOpen(value);
        document.cookie = `${SIDEBAR_COOKIE_NAME}=${open}; path=/; max-age=${SIDEBAR_COOKIE_MAX_AGE}`;
      },
      [setOpenProp, open]
    );
    const toggleSidebar = reactExports.useCallback(() => {
      return isMobile ? setOpenMobile((open2) => !open2) : setOpen((open2) => !open2);
    }, [isMobile, setOpen, setOpenMobile]);
    reactExports.useEffect(() => {
      const handleKeyDown = (event) => {
        if (event.key === SIDEBAR_KEYBOARD_SHORTCUT && (event.metaKey || event.ctrlKey)) {
          event.preventDefault();
          toggleSidebar();
        }
      };
      window.addEventListener("keydown", handleKeyDown);
      return () => window.removeEventListener("keydown", handleKeyDown);
    }, [toggleSidebar]);
    const state = open ? "expanded" : "collapsed";
    const contextValue = reactExports.useMemo(
      () => ({
        state,
        open,
        setOpen,
        isMobile,
        openMobile,
        setOpenMobile,
        toggleSidebar
      }),
      [state, open, setOpen, isMobile, openMobile, setOpenMobile, toggleSidebar]
    );
    return /* @__PURE__ */ jsxRuntimeExports.jsx(SidebarContext.Provider, { value: contextValue, children: /* @__PURE__ */ jsxRuntimeExports.jsx(TooltipProvider, { delayDuration: 0, children: /* @__PURE__ */ jsxRuntimeExports.jsx(
      "div",
      {
        style: {
          "--sidebar-width": SIDEBAR_WIDTH,
          "--sidebar-width-icon": SIDEBAR_WIDTH_ICON,
          ...style
        },
        className: cn(
          "group/sidebar-wrapper flex min-h-svh w-full has-[[data-variant=inset]]:bg-sidebar",
          className
        ),
        ref,
        ...props,
        children
      }
    ) }) });
  }
);
SidebarProvider.displayName = "SidebarProvider";
const Sidebar = reactExports.forwardRef(
  ({
    side = "left",
    variant = "sidebar",
    collapsible = "offcanvas",
    className,
    children,
    ...props
  }, ref) => {
    const { isMobile, state, openMobile, setOpenMobile } = useSidebar();
    if (collapsible === "none") {
      return /* @__PURE__ */ jsxRuntimeExports.jsx(
        "div",
        {
          className: cn(
            "flex h-full w-[--sidebar-width] flex-col bg-sidebar text-sidebar-foreground",
            className
          ),
          ref,
          ...props,
          children
        }
      );
    }
    if (isMobile) {
      return /* @__PURE__ */ jsxRuntimeExports.jsx(Sheet, { open: openMobile, onOpenChange: setOpenMobile, ...props, children: /* @__PURE__ */ jsxRuntimeExports.jsx(
        SheetContent,
        {
          "data-sidebar": "sidebar",
          "data-mobile": "true",
          className: "w-[--sidebar-width] bg-sidebar p-0 text-sidebar-foreground [&>button]:hidden",
          style: {
            "--sidebar-width": SIDEBAR_WIDTH_MOBILE
          },
          side,
          children: /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "flex h-full w-full flex-col", children })
        }
      ) });
    }
    return /* @__PURE__ */ jsxRuntimeExports.jsxs(
      "div",
      {
        ref,
        className: "group peer hidden md:block text-sidebar-foreground",
        "data-state": state,
        "data-collapsible": state === "collapsed" ? collapsible : "",
        "data-variant": variant,
        "data-side": side,
        children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx(
            "div",
            {
              className: cn(
                "duration-200 relative h-svh w-[--sidebar-width] bg-transparent transition-[width] ease-linear",
                "group-data-[collapsible=offcanvas]:w-0",
                "group-data-[side=right]:rotate-180",
                variant === "floating" || variant === "inset" ? "group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)_+_theme(spacing.4))]" : "group-data-[collapsible=icon]:w-[--sidebar-width-icon]"
              )
            }
          ),
          /* @__PURE__ */ jsxRuntimeExports.jsx(
            "div",
            {
              className: cn(
                "duration-200 fixed inset-y-0 z-10 hidden h-svh w-[--sidebar-width] transition-[left,right,width] ease-linear md:flex",
                side === "left" ? "left-0 group-data-[collapsible=offcanvas]:left-[calc(var(--sidebar-width)*-1)]" : "right-0 group-data-[collapsible=offcanvas]:right-[calc(var(--sidebar-width)*-1)]",
                // Adjust the padding for floating and inset variants.
                variant === "floating" || variant === "inset" ? "p-2 group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)_+_theme(spacing.4)_+2px)]" : "group-data-[collapsible=icon]:w-[--sidebar-width-icon] group-data-[side=left]:border-r group-data-[side=right]:border-l",
                className
              ),
              ...props,
              children: /* @__PURE__ */ jsxRuntimeExports.jsx(
                "div",
                {
                  "data-sidebar": "sidebar",
                  className: "flex h-full w-full flex-col bg-sidebar group-data-[variant=floating]:rounded-lg group-data-[variant=floating]:border group-data-[variant=floating]:border-sidebar-border group-data-[variant=floating]:shadow",
                  children
                }
              )
            }
          )
        ]
      }
    );
  }
);
Sidebar.displayName = "Sidebar";
const SidebarTrigger = reactExports.forwardRef(({ className, onClick, ...props }, ref) => {
  const { toggleSidebar } = useSidebar();
  return /* @__PURE__ */ jsxRuntimeExports.jsxs(
    Button,
    {
      ref,
      "data-sidebar": "trigger",
      variant: "ghost",
      size: "icon",
      className: cn("h-7 w-7", className),
      onClick: (event) => {
        onClick == null ? void 0 : onClick(event);
        toggleSidebar();
      },
      ...props,
      children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx(PanelLeft, {}),
        /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "sr-only", children: "Toggle Sidebar" })
      ]
    }
  );
});
SidebarTrigger.displayName = "SidebarTrigger";
const SidebarRail = reactExports.forwardRef(({ className, ...props }, ref) => {
  const { toggleSidebar } = useSidebar();
  return /* @__PURE__ */ jsxRuntimeExports.jsx(
    "button",
    {
      ref,
      "data-sidebar": "rail",
      "aria-label": "Toggle Sidebar",
      tabIndex: -1,
      onClick: toggleSidebar,
      title: "Toggle Sidebar",
      className: cn(
        "absolute inset-y-0 z-20 hidden w-4 -translate-x-1/2 transition-all ease-linear after:absolute after:inset-y-0 after:left-1/2 after:w-[2px] hover:after:bg-sidebar-border group-data-[side=left]:-right-4 group-data-[side=right]:left-0 sm:flex",
        "[[data-side=left]_&]:cursor-w-resize [[data-side=right]_&]:cursor-e-resize",
        "[[data-side=left][data-state=collapsed]_&]:cursor-e-resize [[data-side=right][data-state=collapsed]_&]:cursor-w-resize",
        "group-data-[collapsible=offcanvas]:translate-x-0 group-data-[collapsible=offcanvas]:after:left-full group-data-[collapsible=offcanvas]:hover:bg-sidebar",
        "[[data-side=left][data-collapsible=offcanvas]_&]:-right-2",
        "[[data-side=right][data-collapsible=offcanvas]_&]:-left-2",
        className
      ),
      ...props
    }
  );
});
SidebarRail.displayName = "SidebarRail";
const SidebarInset = reactExports.forwardRef(({ className, ...props }, ref) => {
  return /* @__PURE__ */ jsxRuntimeExports.jsx(
    "main",
    {
      ref,
      className: cn(
        "relative flex min-h-svh flex-1 flex-col bg-background",
        "peer-data-[variant=inset]:min-h-[calc(100svh-theme(spacing.4))] md:peer-data-[variant=inset]:m-2 md:peer-data-[state=collapsed]:peer-data-[variant=inset]:ml-2 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow",
        className
      ),
      ...props
    }
  );
});
SidebarInset.displayName = "SidebarInset";
const SidebarInput = reactExports.forwardRef(({ className, ...props }, ref) => {
  return /* @__PURE__ */ jsxRuntimeExports.jsx(
    Input,
    {
      ref,
      "data-sidebar": "input",
      className: cn(
        "h-8 w-full bg-background shadow-none focus-visible:ring-2 focus-visible:ring-sidebar-ring",
        className
      ),
      ...props
    }
  );
});
SidebarInput.displayName = "SidebarInput";
const SidebarHeader = reactExports.forwardRef(({ className, ...props }, ref) => {
  return /* @__PURE__ */ jsxRuntimeExports.jsx(
    "div",
    {
      ref,
      "data-sidebar": "header",
      className: cn("flex flex-col gap-2 p-2", className),
      ...props
    }
  );
});
SidebarHeader.displayName = "SidebarHeader";
const SidebarFooter = reactExports.forwardRef(({ className, ...props }, ref) => {
  return /* @__PURE__ */ jsxRuntimeExports.jsx(
    "div",
    {
      ref,
      "data-sidebar": "footer",
      className: cn("flex flex-col gap-2 p-2", className),
      ...props
    }
  );
});
SidebarFooter.displayName = "SidebarFooter";
const SidebarSeparator = reactExports.forwardRef(({ className, ...props }, ref) => {
  return /* @__PURE__ */ jsxRuntimeExports.jsx(
    Separator,
    {
      ref,
      "data-sidebar": "separator",
      className: cn("mx-2 w-auto bg-sidebar-border", className),
      ...props
    }
  );
});
SidebarSeparator.displayName = "SidebarSeparator";
const SidebarContent = reactExports.forwardRef(({ className, ...props }, ref) => {
  return /* @__PURE__ */ jsxRuntimeExports.jsx(
    "div",
    {
      ref,
      "data-sidebar": "content",
      className: cn(
        "flex min-h-0 flex-1 flex-col gap-2 overflow-auto group-data-[collapsible=icon]:overflow-hidden",
        className
      ),
      ...props
    }
  );
});
SidebarContent.displayName = "SidebarContent";
const SidebarGroup = reactExports.forwardRef(({ className, ...props }, ref) => {
  return /* @__PURE__ */ jsxRuntimeExports.jsx(
    "div",
    {
      ref,
      "data-sidebar": "group",
      className: cn("relative flex w-full min-w-0 flex-col p-2", className),
      ...props
    }
  );
});
SidebarGroup.displayName = "SidebarGroup";
const SidebarGroupLabel = reactExports.forwardRef(({ className, asChild = false, ...props }, ref) => {
  const Comp = asChild ? Slot : "div";
  return /* @__PURE__ */ jsxRuntimeExports.jsx(
    Comp,
    {
      ref,
      "data-sidebar": "group-label",
      className: cn(
        "duration-200 flex h-8 shrink-0 items-center rounded-md px-2 text-xs font-medium text-sidebar-foreground/70 outline-none ring-sidebar-ring transition-[margin,opa] ease-linear focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0",
        "group-data-[collapsible=icon]:-mt-8 group-data-[collapsible=icon]:opacity-0",
        className
      ),
      ...props
    }
  );
});
SidebarGroupLabel.displayName = "SidebarGroupLabel";
const SidebarGroupAction = reactExports.forwardRef(({ className, asChild = false, ...props }, ref) => {
  const Comp = asChild ? Slot : "button";
  return /* @__PURE__ */ jsxRuntimeExports.jsx(
    Comp,
    {
      ref,
      "data-sidebar": "group-action",
      className: cn(
        "absolute right-3 top-3.5 flex aspect-square w-5 items-center justify-center rounded-md p-0 text-sidebar-foreground outline-none ring-sidebar-ring transition-transform hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0",
        // Increases the hit area of the button on mobile.
        "after:absolute after:-inset-2 after:md:hidden",
        "group-data-[collapsible=icon]:hidden",
        className
      ),
      ...props
    }
  );
});
SidebarGroupAction.displayName = "SidebarGroupAction";
const SidebarGroupContent = reactExports.forwardRef(({ className, ...props }, ref) => /* @__PURE__ */ jsxRuntimeExports.jsx(
  "div",
  {
    ref,
    "data-sidebar": "group-content",
    className: cn("w-full text-sm", className),
    ...props
  }
));
SidebarGroupContent.displayName = "SidebarGroupContent";
const SidebarMenu = reactExports.forwardRef(({ className, ...props }, ref) => /* @__PURE__ */ jsxRuntimeExports.jsx(
  "ul",
  {
    ref,
    "data-sidebar": "menu",
    className: cn("flex w-full min-w-0 flex-col gap-1", className),
    ...props
  }
));
SidebarMenu.displayName = "SidebarMenu";
const SidebarMenuItem = reactExports.forwardRef(({ className, ...props }, ref) => /* @__PURE__ */ jsxRuntimeExports.jsx(
  "li",
  {
    ref,
    "data-sidebar": "menu-item",
    className: cn("group/menu-item relative", className),
    ...props
  }
));
SidebarMenuItem.displayName = "SidebarMenuItem";
const sidebarMenuButtonVariants = cva(
  "peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left text-sm outline-none ring-sidebar-ring transition-[width,height,padding] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-[[data-sidebar=menu-action]]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[active=true]:bg-sidebar-accent data-[active=true]:font-medium data-[active=true]:text-sidebar-accent-foreground data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground group-data-[collapsible=icon]:!size-8 group-data-[collapsible=icon]:!p-2 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0",
  {
    variants: {
      variant: {
        default: "hover:bg-sidebar-accent hover:text-sidebar-accent-foreground",
        outline: "bg-background shadow-[0_0_0_1px_hsl(var(--sidebar-border))] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:shadow-[0_0_0_1px_hsl(var(--sidebar-accent))]"
      },
      size: {
        default: "h-8 text-sm",
        sm: "h-7 text-xs",
        lg: "h-12 text-sm group-data-[collapsible=icon]:!p-0"
      }
    },
    defaultVariants: {
      variant: "default",
      size: "default"
    }
  }
);
const SidebarMenuButton = reactExports.forwardRef(
  ({
    asChild = false,
    isActive = false,
    variant = "default",
    size = "default",
    tooltip,
    className,
    ...props
  }, ref) => {
    const Comp = asChild ? Slot : "button";
    const { isMobile, state } = useSidebar();
    const button = /* @__PURE__ */ jsxRuntimeExports.jsx(
      Comp,
      {
        ref,
        "data-sidebar": "menu-button",
        "data-size": size,
        "data-active": isActive,
        className: cn(sidebarMenuButtonVariants({ variant, size }), className),
        ...props
      }
    );
    if (!tooltip) {
      return button;
    }
    if (typeof tooltip === "string") {
      tooltip = {
        children: tooltip
      };
    }
    return /* @__PURE__ */ jsxRuntimeExports.jsxs(Tooltip, { children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx(TooltipTrigger, { asChild: true, children: button }),
      /* @__PURE__ */ jsxRuntimeExports.jsx(
        TooltipContent,
        {
          side: "right",
          align: "center",
          hidden: state !== "collapsed" || isMobile,
          ...tooltip
        }
      )
    ] });
  }
);
SidebarMenuButton.displayName = "SidebarMenuButton";
const SidebarMenuAction = reactExports.forwardRef(({ className, asChild = false, showOnHover = false, ...props }, ref) => {
  const Comp = asChild ? Slot : "button";
  return /* @__PURE__ */ jsxRuntimeExports.jsx(
    Comp,
    {
      ref,
      "data-sidebar": "menu-action",
      className: cn(
        "absolute right-1 top-1.5 flex aspect-square w-5 items-center justify-center rounded-md p-0 text-sidebar-foreground outline-none ring-sidebar-ring transition-transform hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 peer-hover/menu-button:text-sidebar-accent-foreground [&>svg]:size-4 [&>svg]:shrink-0",
        // Increases the hit area of the button on mobile.
        "after:absolute after:-inset-2 after:md:hidden",
        "peer-data-[size=sm]/menu-button:top-1",
        "peer-data-[size=default]/menu-button:top-1.5",
        "peer-data-[size=lg]/menu-button:top-2.5",
        "group-data-[collapsible=icon]:hidden",
        showOnHover && "group-focus-within/menu-item:opacity-100 group-hover/menu-item:opacity-100 data-[state=open]:opacity-100 peer-data-[active=true]/menu-button:text-sidebar-accent-foreground md:opacity-0",
        className
      ),
      ...props
    }
  );
});
SidebarMenuAction.displayName = "SidebarMenuAction";
const SidebarMenuBadge = reactExports.forwardRef(({ className, ...props }, ref) => /* @__PURE__ */ jsxRuntimeExports.jsx(
  "div",
  {
    ref,
    "data-sidebar": "menu-badge",
    className: cn(
      "absolute right-1 flex h-5 min-w-5 items-center justify-center rounded-md px-1 text-xs font-medium tabular-nums text-sidebar-foreground select-none pointer-events-none",
      "peer-hover/menu-button:text-sidebar-accent-foreground peer-data-[active=true]/menu-button:text-sidebar-accent-foreground",
      "peer-data-[size=sm]/menu-button:top-1",
      "peer-data-[size=default]/menu-button:top-1.5",
      "peer-data-[size=lg]/menu-button:top-2.5",
      "group-data-[collapsible=icon]:hidden",
      className
    ),
    ...props
  }
));
SidebarMenuBadge.displayName = "SidebarMenuBadge";
const SidebarMenuSkeleton = reactExports.forwardRef(({ className, showIcon = false, ...props }, ref) => {
  const width = reactExports.useMemo(() => {
    return `${Math.floor(Math.random() * 40) + 50}%`;
  }, []);
  return /* @__PURE__ */ jsxRuntimeExports.jsxs(
    "div",
    {
      ref,
      "data-sidebar": "menu-skeleton",
      className: cn("rounded-md h-8 flex gap-2 px-2 items-center", className),
      ...props,
      children: [
        showIcon && /* @__PURE__ */ jsxRuntimeExports.jsx(
          Skeleton,
          {
            className: "size-4 rounded-md",
            "data-sidebar": "menu-skeleton-icon"
          }
        ),
        /* @__PURE__ */ jsxRuntimeExports.jsx(
          Skeleton,
          {
            className: "h-4 flex-1 max-w-[--skeleton-width]",
            "data-sidebar": "menu-skeleton-text",
            style: {
              "--skeleton-width": width
            }
          }
        )
      ]
    }
  );
});
SidebarMenuSkeleton.displayName = "SidebarMenuSkeleton";
const SidebarMenuSub = reactExports.forwardRef(({ className, ...props }, ref) => /* @__PURE__ */ jsxRuntimeExports.jsx(
  "ul",
  {
    ref,
    "data-sidebar": "menu-sub",
    className: cn(
      "mx-3.5 flex min-w-0 translate-x-px flex-col gap-1 border-l border-sidebar-border px-2.5 py-0.5",
      "group-data-[collapsible=icon]:hidden",
      className
    ),
    ...props
  }
));
SidebarMenuSub.displayName = "SidebarMenuSub";
const SidebarMenuSubItem = reactExports.forwardRef(({ ...props }, ref) => /* @__PURE__ */ jsxRuntimeExports.jsx("li", { ref, ...props }));
SidebarMenuSubItem.displayName = "SidebarMenuSubItem";
const SidebarMenuSubButton = reactExports.forwardRef(({ asChild = false, size = "md", isActive, className, ...props }, ref) => {
  const Comp = asChild ? Slot : "a";
  return /* @__PURE__ */ jsxRuntimeExports.jsx(
    Comp,
    {
      ref,
      "data-sidebar": "menu-sub-button",
      "data-size": size,
      "data-active": isActive,
      className: cn(
        "flex h-7 min-w-0 -translate-x-px items-center gap-2 overflow-hidden rounded-md px-2 text-sidebar-foreground outline-none ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 aria-disabled:pointer-events-none aria-disabled:opacity-50 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0 [&>svg]:text-sidebar-accent-foreground",
        "data-[active=true]:bg-sidebar-accent data-[active=true]:text-sidebar-accent-foreground",
        size === "sm" && "text-xs",
        size === "md" && "text-sm",
        "group-data-[collapsible=icon]:hidden",
        className
      ),
      ...props
    }
  );
});
SidebarMenuSubButton.displayName = "SidebarMenuSubButton";
var ROOT_NAME = "AlertDialog";
var [createAlertDialogContext, createAlertDialogScope] = createContextScope(ROOT_NAME, [
  createDialogScope
]);
var useDialogScope = createDialogScope();
var AlertDialog$1 = (props) => {
  const { __scopeAlertDialog, ...alertDialogProps } = props;
  const dialogScope = useDialogScope(__scopeAlertDialog);
  return /* @__PURE__ */ jsxRuntimeExports.jsx(Root, { ...dialogScope, ...alertDialogProps, modal: true });
};
AlertDialog$1.displayName = ROOT_NAME;
var TRIGGER_NAME = "AlertDialogTrigger";
var AlertDialogTrigger$1 = reactExports.forwardRef(
  (props, forwardedRef) => {
    const { __scopeAlertDialog, ...triggerProps } = props;
    const dialogScope = useDialogScope(__scopeAlertDialog);
    return /* @__PURE__ */ jsxRuntimeExports.jsx(Trigger, { ...dialogScope, ...triggerProps, ref: forwardedRef });
  }
);
AlertDialogTrigger$1.displayName = TRIGGER_NAME;
var PORTAL_NAME = "AlertDialogPortal";
var AlertDialogPortal$1 = (props) => {
  const { __scopeAlertDialog, ...portalProps } = props;
  const dialogScope = useDialogScope(__scopeAlertDialog);
  return /* @__PURE__ */ jsxRuntimeExports.jsx(Portal, { ...dialogScope, ...portalProps });
};
AlertDialogPortal$1.displayName = PORTAL_NAME;
var OVERLAY_NAME = "AlertDialogOverlay";
var AlertDialogOverlay$1 = reactExports.forwardRef(
  (props, forwardedRef) => {
    const { __scopeAlertDialog, ...overlayProps } = props;
    const dialogScope = useDialogScope(__scopeAlertDialog);
    return /* @__PURE__ */ jsxRuntimeExports.jsx(Overlay, { ...dialogScope, ...overlayProps, ref: forwardedRef });
  }
);
AlertDialogOverlay$1.displayName = OVERLAY_NAME;
var CONTENT_NAME = "AlertDialogContent";
var [AlertDialogContentProvider, useAlertDialogContentContext] = createAlertDialogContext(CONTENT_NAME);
var AlertDialogContent$1 = reactExports.forwardRef(
  (props, forwardedRef) => {
    const { __scopeAlertDialog, children, ...contentProps } = props;
    const dialogScope = useDialogScope(__scopeAlertDialog);
    const contentRef = reactExports.useRef(null);
    const composedRefs = useComposedRefs(forwardedRef, contentRef);
    const cancelRef = reactExports.useRef(null);
    return /* @__PURE__ */ jsxRuntimeExports.jsx(
      WarningProvider,
      {
        contentName: CONTENT_NAME,
        titleName: TITLE_NAME,
        docsSlug: "alert-dialog",
        children: /* @__PURE__ */ jsxRuntimeExports.jsx(AlertDialogContentProvider, { scope: __scopeAlertDialog, cancelRef, children: /* @__PURE__ */ jsxRuntimeExports.jsxs(
          Content,
          {
            role: "alertdialog",
            ...dialogScope,
            ...contentProps,
            ref: composedRefs,
            onOpenAutoFocus: composeEventHandlers(contentProps.onOpenAutoFocus, (event) => {
              var _a;
              event.preventDefault();
              (_a = cancelRef.current) == null ? void 0 : _a.focus({ preventScroll: true });
            }),
            onPointerDownOutside: (event) => event.preventDefault(),
            onInteractOutside: (event) => event.preventDefault(),
            children: [
              /* @__PURE__ */ jsxRuntimeExports.jsx(Slottable, { children }),
              /* @__PURE__ */ jsxRuntimeExports.jsx(DescriptionWarning, { contentRef })
            ]
          }
        ) })
      }
    );
  }
);
AlertDialogContent$1.displayName = CONTENT_NAME;
var TITLE_NAME = "AlertDialogTitle";
var AlertDialogTitle$1 = reactExports.forwardRef(
  (props, forwardedRef) => {
    const { __scopeAlertDialog, ...titleProps } = props;
    const dialogScope = useDialogScope(__scopeAlertDialog);
    return /* @__PURE__ */ jsxRuntimeExports.jsx(Title, { ...dialogScope, ...titleProps, ref: forwardedRef });
  }
);
AlertDialogTitle$1.displayName = TITLE_NAME;
var DESCRIPTION_NAME = "AlertDialogDescription";
var AlertDialogDescription$1 = reactExports.forwardRef((props, forwardedRef) => {
  const { __scopeAlertDialog, ...descriptionProps } = props;
  const dialogScope = useDialogScope(__scopeAlertDialog);
  return /* @__PURE__ */ jsxRuntimeExports.jsx(Description, { ...dialogScope, ...descriptionProps, ref: forwardedRef });
});
AlertDialogDescription$1.displayName = DESCRIPTION_NAME;
var ACTION_NAME = "AlertDialogAction";
var AlertDialogAction$1 = reactExports.forwardRef(
  (props, forwardedRef) => {
    const { __scopeAlertDialog, ...actionProps } = props;
    const dialogScope = useDialogScope(__scopeAlertDialog);
    return /* @__PURE__ */ jsxRuntimeExports.jsx(Close, { ...dialogScope, ...actionProps, ref: forwardedRef });
  }
);
AlertDialogAction$1.displayName = ACTION_NAME;
var CANCEL_NAME = "AlertDialogCancel";
var AlertDialogCancel$1 = reactExports.forwardRef(
  (props, forwardedRef) => {
    const { __scopeAlertDialog, ...cancelProps } = props;
    const { cancelRef } = useAlertDialogContentContext(CANCEL_NAME, __scopeAlertDialog);
    const dialogScope = useDialogScope(__scopeAlertDialog);
    const ref = useComposedRefs(forwardedRef, cancelRef);
    return /* @__PURE__ */ jsxRuntimeExports.jsx(Close, { ...dialogScope, ...cancelProps, ref });
  }
);
AlertDialogCancel$1.displayName = CANCEL_NAME;
var DescriptionWarning = ({ contentRef }) => {
  const MESSAGE = `\`${CONTENT_NAME}\` requires a description for the component to be accessible for screen reader users.

You can add a description to the \`${CONTENT_NAME}\` by passing a \`${DESCRIPTION_NAME}\` component as a child, which also benefits sighted users by adding visible context to the dialog.

Alternatively, you can use your own component as a description by assigning it an \`id\` and passing the same value to the \`aria-describedby\` prop in \`${CONTENT_NAME}\`. If the description is confusing or duplicative for sighted users, you can use the \`@radix-ui/react-visually-hidden\` primitive as a wrapper around your description component.

For more information, see https://radix-ui.com/primitives/docs/components/alert-dialog`;
  reactExports.useEffect(() => {
    var _a;
    const hasDescription = document.getElementById(
      (_a = contentRef.current) == null ? void 0 : _a.getAttribute("aria-describedby")
    );
    if (!hasDescription) console.warn(MESSAGE);
  }, [MESSAGE, contentRef]);
  return null;
};
var Root2 = AlertDialog$1;
var Trigger2 = AlertDialogTrigger$1;
var Portal2 = AlertDialogPortal$1;
var Overlay2 = AlertDialogOverlay$1;
var Content2 = AlertDialogContent$1;
var Action = AlertDialogAction$1;
var Cancel = AlertDialogCancel$1;
var Title2 = AlertDialogTitle$1;
var Description2 = AlertDialogDescription$1;
const AlertDialog = Root2;
const AlertDialogTrigger = Trigger2;
const AlertDialogPortal = Portal2;
const AlertDialogOverlay = reactExports.forwardRef(({ className, ...props }, ref) => /* @__PURE__ */ jsxRuntimeExports.jsx(
  Overlay2,
  {
    className: cn(
      "fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",
      className
    ),
    ...props,
    ref
  }
));
AlertDialogOverlay.displayName = Overlay2.displayName;
const AlertDialogContent = reactExports.forwardRef(({ className, ...props }, ref) => /* @__PURE__ */ jsxRuntimeExports.jsxs(AlertDialogPortal, { children: [
  /* @__PURE__ */ jsxRuntimeExports.jsx(AlertDialogOverlay, {}),
  /* @__PURE__ */ jsxRuntimeExports.jsx(
    Content2,
    {
      ref,
      className: cn(
        "fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",
        className
      ),
      ...props
    }
  )
] }));
AlertDialogContent.displayName = Content2.displayName;
const AlertDialogHeader = ({
  className,
  ...props
}) => /* @__PURE__ */ jsxRuntimeExports.jsx(
  "div",
  {
    className: cn(
      "flex flex-col space-y-2 text-center sm:text-left",
      className
    ),
    ...props
  }
);
AlertDialogHeader.displayName = "AlertDialogHeader";
const AlertDialogFooter = ({
  className,
  ...props
}) => /* @__PURE__ */ jsxRuntimeExports.jsx(
  "div",
  {
    className: cn(
      "flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",
      className
    ),
    ...props
  }
);
AlertDialogFooter.displayName = "AlertDialogFooter";
const AlertDialogTitle = reactExports.forwardRef(({ className, ...props }, ref) => /* @__PURE__ */ jsxRuntimeExports.jsx(
  Title2,
  {
    ref,
    className: cn("text-lg font-semibold", className),
    ...props
  }
));
AlertDialogTitle.displayName = Title2.displayName;
const AlertDialogDescription = reactExports.forwardRef(({ className, ...props }, ref) => /* @__PURE__ */ jsxRuntimeExports.jsx(
  Description2,
  {
    ref,
    className: cn("text-sm text-muted-foreground", className),
    ...props
  }
));
AlertDialogDescription.displayName = Description2.displayName;
const AlertDialogAction = reactExports.forwardRef(({ className, ...props }, ref) => /* @__PURE__ */ jsxRuntimeExports.jsx(
  Action,
  {
    ref,
    className: cn(buttonVariants(), className),
    ...props
  }
));
AlertDialogAction.displayName = Action.displayName;
const AlertDialogCancel = reactExports.forwardRef(({ className, ...props }, ref) => /* @__PURE__ */ jsxRuntimeExports.jsx(
  Cancel,
  {
    ref,
    className: cn(
      buttonVariants({ variant: "outline" }),
      "mt-2 sm:mt-0",
      className
    ),
    ...props
  }
));
AlertDialogCancel.displayName = Cancel.displayName;
function Home() {
  var _a, _b, _c, _d;
  const {
    userPermissions,
    userDetails
  } = useLoaderData();
  const {
    pathname
  } = useLocation();
  const submit = useSubmit();
  const activeSection = pathname.split("/")[2];
  const [isNetWorkManagerBasic, setIsNetWorkManagerBasic] = reactExports.useState(false);
  const [isSalesManagerBasic, setIsSalesManagerBasic] = reactExports.useState(false);
  const [adminBasic, setAdminBasic] = reactExports.useState(false);
  const [isSellerBasic, setIsSellerBasic] = reactExports.useState(false);
  reactExports.useEffect(() => {
    if (userPermissions) {
      setIsNetWorkManagerBasic(userPermissions.includes("OC_Manager"));
      setIsSalesManagerBasic(userPermissions.includes("FmSalesManager"));
      setIsSellerBasic(userPermissions.includes("SC_Basic"));
      setAdminBasic(userPermissions.includes("AC_Basic"));
    }
  }, [userPermissions]);
  const handleLogout = () => submit(null, {
    method: "post"
  });
  if (pathname === "/home") {
    return /* @__PURE__ */ jsxRuntimeExports.jsx(Navigate, {
      to: "/home/<USER>",
      replace: true
    });
  }
  return /* @__PURE__ */ jsxRuntimeExports.jsx(SidebarProvider, {
    children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
      className: "flex h-screen w-screen bg-white",
      children: [/* @__PURE__ */ jsxRuntimeExports.jsxs(Sidebar, {
        className: "bg-white border-r ",
        children: [/* @__PURE__ */ jsxRuntimeExports.jsx(SidebarHeader, {
          className: "px-6 py-4 bg-white",
          children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
            className: "flex flex-col items-start gap-2",
            children: [/* @__PURE__ */ jsxRuntimeExports.jsx("img", {
              src: "/mnet-logo.svg",
              alt: "mNet Logo",
              className: "h-12 w-auto"
            }), /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
              className: "flex items-center mt-8 w-full px-4 py-2 border rounded-md cursor-pointer hover:bg-gray-100 transition-colors",
              children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                className: "flex items-center space-x-3",
                children: [/* @__PURE__ */ jsxRuntimeExports.jsx("div", {
                  className: "h-6 w-6 rounded-full bg-primary flex items-center justify-center text-sm font-bold text-white",
                  children: ((_c = (_b = (_a = userDetails == null ? void 0 : userDetails.userDetails) == null ? void 0 : _a.businessName) == null ? void 0 : _b[0]) == null ? void 0 : _c.toUpperCase()) || "B"
                }), /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
                  className: "text-gray-900 text-sm font-medium",
                  children: ((_d = userDetails == null ? void 0 : userDetails.userDetails) == null ? void 0 : _d.businessName) || "Business Name"
                })]
              })
            })]
          })
        }), /* @__PURE__ */ jsxRuntimeExports.jsx(SidebarContent, {
          className: "bg-white",
          children: /* @__PURE__ */ jsxRuntimeExports.jsxs(SidebarMenu, {
            className: "mt-2 space-y-1",
            children: [isSellerBasic && /* @__PURE__ */ jsxRuntimeExports.jsx(SidebarMenuItem, {
              children: /* @__PURE__ */ jsxRuntimeExports.jsx(Link, {
                to: "/home/<USER>",
                children: /* @__PURE__ */ jsxRuntimeExports.jsxs(SidebarMenuButton, {
                  isActive: activeSection === "dashboard",
                  className: `px-6 py-3 text-gray-900 hover:bg-gray-100 transition-colors rounded-md ${activeSection === "dashboard" ? "bg-blue-100 text-blue-800 font-semibold" : ""}`,
                  children: [/* @__PURE__ */ jsxRuntimeExports.jsx(House, {
                    className: "h-5 w-5 mr-3"
                  }), "Dashboard"]
                })
              })
            }), isSellerBasic && /* @__PURE__ */ jsxRuntimeExports.jsx(SidebarMenuItem, {
              children: /* @__PURE__ */ jsxRuntimeExports.jsx(Link, {
                to: "/home/<USER>",
                children: /* @__PURE__ */ jsxRuntimeExports.jsxs(SidebarMenuButton, {
                  isActive: activeSection === "customers",
                  className: `px-6 py-3 text-gray-900 hover:bg-gray-100 transition-colors rounded-md ${activeSection === "customers" ? "bg-blue-100 text-blue-800 font-semibold" : ""}`,
                  children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Contact, {
                    className: "h-5 w-5 mr-3"
                  }), "My Customers"]
                })
              })
            }), isSellerBasic && /* @__PURE__ */ jsxRuntimeExports.jsx(SidebarMenuItem, {
              children: /* @__PURE__ */ jsxRuntimeExports.jsx(Link, {
                to: "/home/<USER>",
                children: /* @__PURE__ */ jsxRuntimeExports.jsxs(SidebarMenuButton, {
                  isActive: activeSection === "myTrips",
                  className: `px-6 py-3 text-gray-900 hover:bg-gray-100 transition-colors rounded-md ${activeSection === "myTrips" ? "bg-blue-100 text-blue-800 font-semibold" : ""}`,
                  children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Truck, {
                    className: "h-5 w-5 mr-3"
                  }), "My Trips"]
                })
              })
            }), isSellerBasic && /* @__PURE__ */ jsxRuntimeExports.jsx(SidebarMenuItem, {
              children: /* @__PURE__ */ jsxRuntimeExports.jsx(Link, {
                to: "/home/<USER>",
                children: /* @__PURE__ */ jsxRuntimeExports.jsxs(SidebarMenuButton, {
                  isActive: activeSection === "myItems",
                  className: `px-6 py-3 text-gray-900 hover:bg-gray-100 transition-colors rounded-md ${activeSection === "myItems" ? "bg-blue-100 text-blue-800 font-semibold" : ""}`,
                  children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Grape, {
                    className: "h-5 w-5 mr-3"
                  }), "My Items"]
                })
              })
            }), isNetWorkManagerBasic && /* @__PURE__ */ jsxRuntimeExports.jsx(SidebarMenuItem, {
              children: /* @__PURE__ */ jsxRuntimeExports.jsx(Link, {
                to: "/home/<USER>",
                children: /* @__PURE__ */ jsxRuntimeExports.jsxs(SidebarMenuButton, {
                  isActive: activeSection === "whatsappconnect",
                  className: `px-6 py-3 text-gray-900 hover:bg-gray-100 transition-colors rounded-md ${activeSection === "whatsappconnect" ? "bg-blue-100 text-blue-800 font-semibold" : ""}`,
                  children: [/* @__PURE__ */ jsxRuntimeExports.jsx(MessageCircle, {
                    className: "h-5 w-5 mr-3"
                  }), "Whatsapp"]
                })
              })
            }), (isNetWorkManagerBasic || adminBasic) && /* @__PURE__ */ jsxRuntimeExports.jsx(SidebarMenuItem, {
              children: /* @__PURE__ */ jsxRuntimeExports.jsx(Link, {
                to: "/home/<USER>",
                children: /* @__PURE__ */ jsxRuntimeExports.jsxs(SidebarMenuButton, {
                  isActive: activeSection === "masterItemCategory",
                  className: `px-6 py-3 text-gray-900 hover:bg-gray-100 transition-colors rounded-md ${activeSection === "masterItemCategory" ? "bg-blue-100 text-blue-800 font-semibold" : ""}`,
                  children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Cherry, {
                    className: "h-5 w-5 mr-3"
                  }), "LiveOrderDashBoard"]
                })
              })
            }), isNetWorkManagerBasic && /* @__PURE__ */ jsxRuntimeExports.jsx(SidebarMenuItem, {
              children: /* @__PURE__ */ jsxRuntimeExports.jsx(Link, {
                to: "/home/<USER>",
                children: /* @__PURE__ */ jsxRuntimeExports.jsxs(SidebarMenuButton, {
                  isActive: activeSection === "masterItemCategory",
                  className: `px-6 py-3 text-gray-900 hover:bg-gray-100 transition-colors rounded-md ${activeSection === "masterItemCategory" ? "bg-blue-100 text-blue-800 font-semibold" : ""}`,
                  children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Cherry, {
                    className: "h-5 w-5 mr-3"
                  }), "Master Item Category"]
                })
              })
            }), isNetWorkManagerBasic && /* @__PURE__ */ jsxRuntimeExports.jsx(SidebarMenuItem, {
              children: /* @__PURE__ */ jsxRuntimeExports.jsx(Link, {
                to: "/home/<USER>",
                children: /* @__PURE__ */ jsxRuntimeExports.jsxs(SidebarMenuButton, {
                  isActive: activeSection === "masterItems",
                  className: `px-6 py-3 text-gray-900 hover:bg-gray-100 transition-colors rounded-md ${activeSection === "masterItems" ? "bg-blue-100 text-blue-800 font-semibold" : ""}`,
                  children: [/* @__PURE__ */ jsxRuntimeExports.jsx(LayoutList, {
                    className: "h-5 w-5 mr-3"
                  }), "Master Items"]
                })
              })
            }), isSellerBasic && /* @__PURE__ */ jsxRuntimeExports.jsx(SidebarMenuItem, {
              children: /* @__PURE__ */ jsxRuntimeExports.jsx(Link, {
                to: "/home/<USER>",
                children: /* @__PURE__ */ jsxRuntimeExports.jsxs(SidebarMenuButton, {
                  isActive: activeSection === "sellerWiseSales",
                  className: `px-6 py-3 text-gray-900 hover:bg-gray-100 transition-colors rounded-md ${activeSection === "sellerWiseSales" ? "bg-blue-100 text-blue-800 font-semibold" : ""}`,
                  children: [/* @__PURE__ */ jsxRuntimeExports.jsx(ChartNoAxesCombined, {
                    className: "h-5 w-5 mr-3"
                  }), "Sales Report"]
                })
              })
            }), isSellerBasic && /* @__PURE__ */ jsxRuntimeExports.jsx(SidebarMenuItem, {
              children: /* @__PURE__ */ jsxRuntimeExports.jsx(Link, {
                to: "/home/<USER>",
                children: /* @__PURE__ */ jsxRuntimeExports.jsxs(SidebarMenuButton, {
                  isActive: activeSection === "metaSalesReports",
                  className: `px-6 py-3 text-gray-900 hover:bg-gray-100 transition-colors rounded-md ${activeSection === "metaSalesReports" ? "bg-blue-100 text-blue-800 font-semibold" : ""}`,
                  children: [/* @__PURE__ */ jsxRuntimeExports.jsx(ChartNetwork, {
                    className: "h-5 w-5 mr-3"
                  }), "Sales Dashboard"]
                })
              })
            }), isSellerBasic && /* @__PURE__ */ jsxRuntimeExports.jsxs(SidebarMenuItem, {
              children: [/* @__PURE__ */ jsxRuntimeExports.jsxs(SidebarMenuButton, {
                className: "px-6 py-3 hover:bg-gray-100 text-gray-900 font-semibold rounded-md",
                children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Warehouse, {
                  className: "h-5 w-5 mr-3"
                }), "StockManagement"]
              }), /* @__PURE__ */ jsxRuntimeExports.jsxs(SidebarMenuSub, {
                children: [/* @__PURE__ */ jsxRuntimeExports.jsx(SidebarMenuSubItem, {
                  children: /* @__PURE__ */ jsxRuntimeExports.jsx(Link, {
                    to: "/home/<USER>",
                    children: /* @__PURE__ */ jsxRuntimeExports.jsx(SidebarMenuSubButton, {
                      isActive: activeSection === "mystock",
                      className: `px-6 py-2 text-gray-900 hover:bg-gray-100 transition-colors rounded-md ${activeSection === "salesAnalysis" ? "bg-blue-100 text-blue-800 font-medium" : ""}`,
                      children: "My Stock"
                    })
                  })
                }), /* @__PURE__ */ jsxRuntimeExports.jsx(SidebarMenuSubItem, {
                  children: /* @__PURE__ */ jsxRuntimeExports.jsx(Link, {
                    to: "/home/<USER>",
                    children: /* @__PURE__ */ jsxRuntimeExports.jsx(SidebarMenuSubButton, {
                      isActive: activeSection === "stockWithMe",
                      className: `px-6 py-2 text-gray-900 hover:bg-gray-100 transition-colors rounded-md ${activeSection === "sellerWiseSales" ? "bg-blue-100 text-blue-800 font-medium" : ""}`,
                      children: "Stock With Me"
                    })
                  })
                })]
              })]
            }), (isSalesManagerBasic || adminBasic) && /* @__PURE__ */ jsxRuntimeExports.jsxs(SidebarMenuItem, {
              children: [/* @__PURE__ */ jsxRuntimeExports.jsxs(SidebarMenuButton, {
                className: "px-6 py-3 hover:bg-gray-100 text-gray-900 font-semibold rounded-md",
                children: [/* @__PURE__ */ jsxRuntimeExports.jsx(ChartPie, {
                  className: "h-5 w-5 mr-3"
                }), "Sales Related"]
              }), /* @__PURE__ */ jsxRuntimeExports.jsxs(SidebarMenuSub, {
                children: [/* @__PURE__ */ jsxRuntimeExports.jsx(SidebarMenuSubItem, {
                  children: /* @__PURE__ */ jsxRuntimeExports.jsx(Link, {
                    to: "/home/<USER>",
                    children: /* @__PURE__ */ jsxRuntimeExports.jsx(SidebarMenuSubButton, {
                      isActive: activeSection === "salesAnalysis",
                      className: `px-6 py-2 text-gray-900 hover:bg-gray-100 transition-colors rounded-md ${activeSection === "salesAnalysis" ? "bg-blue-100 text-blue-800 font-medium" : ""}`,
                      children: "Sales Analysis"
                    })
                  })
                }), /* @__PURE__ */ jsxRuntimeExports.jsx(SidebarMenuSubItem, {
                  children: /* @__PURE__ */ jsxRuntimeExports.jsx(Link, {
                    to: "/home/<USER>",
                    children: /* @__PURE__ */ jsxRuntimeExports.jsx(SidebarMenuSubButton, {
                      isActive: activeSection === "sellerWiseSales",
                      className: `px-6 py-2 text-gray-900 hover:bg-gray-100 transition-colors rounded-md ${activeSection === "sellerWiseSales" ? "bg-blue-100 text-blue-800 font-medium" : ""}`,
                      children: "Sales Report"
                    })
                  })
                }), /* @__PURE__ */ jsxRuntimeExports.jsx(SidebarMenuSubItem, {
                  children: /* @__PURE__ */ jsxRuntimeExports.jsx(Link, {
                    to: "/home/<USER>",
                    children: /* @__PURE__ */ jsxRuntimeExports.jsx(SidebarMenuSubButton, {
                      isActive: activeSection === "fmMetaHourlySales",
                      className: `px-6 py-2 text-gray-900 hover:bg-gray-100 transition-colors rounded-md ${activeSection === "fmMetaHourlySales" ? "bg-blue-100 text-blue-800 font-medium" : ""}`,
                      children: "FOS HourlyWise"
                    })
                  })
                }), /* @__PURE__ */ jsxRuntimeExports.jsx(SidebarMenuSubItem, {
                  children: /* @__PURE__ */ jsxRuntimeExports.jsx(Link, {
                    to: "/home/<USER>",
                    children: /* @__PURE__ */ jsxRuntimeExports.jsx(SidebarMenuSubButton, {
                      isActive: activeSection === "sellerLevelreturns",
                      className: `px-6 py-2 text-gray-900 hover:bg-gray-100 transition-colors rounded-md ${activeSection === "sellerLevelreturns" ? "bg-blue-100 text-blue-800 font-medium" : ""}`,
                      children: "Seller Level Returns"
                    })
                  })
                }), /* @__PURE__ */ jsxRuntimeExports.jsx(SidebarMenuSubItem, {
                  children: /* @__PURE__ */ jsxRuntimeExports.jsx(Link, {
                    to: "/home/<USER>",
                    children: /* @__PURE__ */ jsxRuntimeExports.jsx(SidebarMenuSubButton, {
                      isActive: activeSection === "fosLevelReturns",
                      className: `px-6 py-2 text-gray-900 hover:bg-gray-100 transition-colors rounded-md ${activeSection === "fosLevelReturns" ? "bg-blue-100 text-blue-800 font-medium" : ""}`,
                      children: "FOS Level Returns"
                    })
                  })
                })]
              })]
            }), (isNetWorkManagerBasic || adminBasic) && /* @__PURE__ */ jsxRuntimeExports.jsx(SidebarMenuItem, {
              children: /* @__PURE__ */ jsxRuntimeExports.jsx(Link, {
                to: "/home/<USER>",
                children: /* @__PURE__ */ jsxRuntimeExports.jsxs(SidebarMenuButton, {
                  isActive: activeSection === "bankTransactions",
                  className: `px-6 py-3 text-gray-900 hover:bg-gray-100 transition-colors rounded-md ${activeSection === "bankTransactions" ? "bg-blue-100 text-blue-800 font-semibold" : ""}`,
                  children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Landmark, {
                    className: "h-5 w-5 mr-3"
                  }), "Bank Transactions"]
                })
              })
            }), isNetWorkManagerBasic && /* @__PURE__ */ jsxRuntimeExports.jsx(SidebarMenuItem, {
              children: /* @__PURE__ */ jsxRuntimeExports.jsx(Link, {
                to: "/home/<USER>",
                children: /* @__PURE__ */ jsxRuntimeExports.jsxs(SidebarMenuButton, {
                  isActive: activeSection === "localities",
                  className: `px-6 py-3 text-gray-900 hover:bg-gray-100 transition-colors rounded-md ${activeSection === "localities" ? "bg-blue-100 text-blue-800 font-semibold" : ""}`,
                  children: [/* @__PURE__ */ jsxRuntimeExports.jsx(MapPin, {
                    className: "h-5 w-5 mr-3"
                  }), "Master Localities"]
                })
              })
            }), (isNetWorkManagerBasic || adminBasic) && /* @__PURE__ */ jsxRuntimeExports.jsx(SidebarMenuItem, {
              children: /* @__PURE__ */ jsxRuntimeExports.jsx(Link, {
                to: "/home/<USER>",
                children: /* @__PURE__ */ jsxRuntimeExports.jsxs(SidebarMenuButton, {
                  isActive: activeSection === "tickets",
                  className: "px-6 py-6 hover:bg-gray-100 text-gray-900",
                  children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Headset, {
                    className: "h-6 w-6 mr-3"
                  }), "Support Tickets"]
                })
              })
            }), (isNetWorkManagerBasic || isSalesManagerBasic || adminBasic) && /* @__PURE__ */ jsxRuntimeExports.jsx(SidebarMenuItem, {
              children: /* @__PURE__ */ jsxRuntimeExports.jsx(Link, {
                to: "/home/<USER>",
                children: /* @__PURE__ */ jsxRuntimeExports.jsxs(SidebarMenuButton, {
                  isActive: activeSection === "networkManagement",
                  className: `px-6 py-3 text-gray-900 hover:bg-gray-100 transition-colors rounded-md ${activeSection === "networkManagement" ? "bg-blue-100 text-blue-800 font-semibold" : ""}`,
                  children: [/* @__PURE__ */ jsxRuntimeExports.jsx(EarthLock, {
                    className: "h-5 w-5 mr-3"
                  }), "Network Management"]
                })
              })
            }), (isNetWorkManagerBasic || adminBasic) && /* @__PURE__ */ jsxRuntimeExports.jsx(SidebarMenuItem, {
              children: /* @__PURE__ */ jsxRuntimeExports.jsx(Link, {
                to: "/home/<USER>",
                children: /* @__PURE__ */ jsxRuntimeExports.jsxs(SidebarMenuButton, {
                  isActive: activeSection === "sellerManagement",
                  className: `px-6 py-3 text-gray-900 hover:bg-gray-100 transition-colors rounded-md ${activeSection === "sellerManagement" ? "bg-blue-100 text-blue-800 font-semibold" : ""}`,
                  children: [/* @__PURE__ */ jsxRuntimeExports.jsx(PlaneTakeoff, {
                    className: "h-5 w-5 mr-3"
                  }), "Seller Management"]
                })
              })
            }), (isNetWorkManagerBasic || adminBasic) && /* @__PURE__ */ jsxRuntimeExports.jsx(SidebarMenuItem, {
              children: /* @__PURE__ */ jsxRuntimeExports.jsx(Link, {
                to: "/home/<USER>",
                children: /* @__PURE__ */ jsxRuntimeExports.jsxs(SidebarMenuButton, {
                  isActive: activeSection === "metaRestaurantDashboard",
                  className: `px-6 py-3 text-gray-900 hover:bg-gray-100 transition-colors rounded-md ${activeSection === "metaRestaurantDashboard" ? "bg-blue-100 text-blue-800 font-semibold" : ""}`,
                  children: [/* @__PURE__ */ jsxRuntimeExports.jsx(LayoutDashboard, {
                    className: "h-5 w-5 mr-3"
                  }), "Restaurant Dashboard"]
                })
              })
            }), (isNetWorkManagerBasic || adminBasic) && /* @__PURE__ */ jsxRuntimeExports.jsx(SidebarMenuItem, {
              children: /* @__PURE__ */ jsxRuntimeExports.jsx(Link, {
                to: "/home/<USER>",
                children: /* @__PURE__ */ jsxRuntimeExports.jsxs(SidebarMenuButton, {
                  isActive: activeSection === "buyerManagement",
                  className: `px-6 py-3 text-gray-900 hover:bg-gray-100 transition-colors rounded-md ${activeSection === "buyerManagement" ? "bg-blue-100 text-blue-800 font-semibold" : ""}`,
                  children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Store, {
                    className: "h-5 w-5 mr-3"
                  }), "Buyer Management"]
                })
              })
            }), (isNetWorkManagerBasic || adminBasic) && /* @__PURE__ */ jsxRuntimeExports.jsx(SidebarMenuItem, {
              children: /* @__PURE__ */ jsxRuntimeExports.jsx(Link, {
                to: "/home/<USER>",
                children: /* @__PURE__ */ jsxRuntimeExports.jsxs(SidebarMenuButton, {
                  isActive: activeSection === "tripManagement",
                  className: `px-6 py-3 text-gray-900 hover:bg-gray-100 transition-colors rounded-md ${activeSection === "tripManagement" ? "bg-blue-100 text-blue-800 font-semibold" : ""}`,
                  children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Truck, {
                    className: "h-5 w-5 mr-3"
                  }), "Trip Management"]
                })
              })
            }), /* @__PURE__ */ jsxRuntimeExports.jsx(SidebarMenuItem, {
              className: "mt-auto",
              children: /* @__PURE__ */ jsxRuntimeExports.jsxs(AlertDialog, {
                children: [/* @__PURE__ */ jsxRuntimeExports.jsx(AlertDialogTrigger, {
                  asChild: true,
                  children: /* @__PURE__ */ jsxRuntimeExports.jsxs(SidebarMenuButton, {
                    className: "px-6 py-3 hover:bg-gray-100 text-gray-900 transition-colors rounded-md",
                    children: [/* @__PURE__ */ jsxRuntimeExports.jsx(LogOut, {
                      className: "h-5 w-5 mr-3"
                    }), "Logout"]
                  })
                }), /* @__PURE__ */ jsxRuntimeExports.jsxs(AlertDialogContent, {
                  children: [/* @__PURE__ */ jsxRuntimeExports.jsxs(AlertDialogHeader, {
                    children: [/* @__PURE__ */ jsxRuntimeExports.jsx(AlertDialogTitle, {
                      children: "Are you sure you want to logout?"
                    }), /* @__PURE__ */ jsxRuntimeExports.jsx(AlertDialogDescription, {
                      children: "You will be redirected to the login page."
                    })]
                  }), /* @__PURE__ */ jsxRuntimeExports.jsxs(AlertDialogFooter, {
                    children: [/* @__PURE__ */ jsxRuntimeExports.jsx(AlertDialogCancel, {
                      children: "Cancel"
                    }), /* @__PURE__ */ jsxRuntimeExports.jsx(AlertDialogAction, {
                      onClick: handleLogout,
                      children: "Logout"
                    })]
                  })]
                })]
              })
            })]
          })
        }), /* @__PURE__ */ jsxRuntimeExports.jsx(SidebarRail, {})]
      }), /* @__PURE__ */ jsxRuntimeExports.jsxs("main", {
        className: "flex-1 overflow-y-auto p-4",
        children: [/* @__PURE__ */ jsxRuntimeExports.jsx(SidebarTrigger, {
          className: "mb-4 lg:hidden text-gray-900 hover:bg-gray-100 p-2 rounded-md"
        }), /* @__PURE__ */ jsxRuntimeExports.jsx(Outlet, {})]
      })]
    })
  });
}
export {
  Home as default
};
//# sourceMappingURL=home-BA6JRfeX.js.map
