{"version": 3, "file": "home.myAgents-Bpfucaju.js", "sources": ["../../../app/routes/home.myAgents.tsx"], "sourcesContent": ["import { RefreshCcw } from \"lucide-react\"\r\nimport { useState } from \"react\"\r\nimport { Button } from \"~/components/ui/button\"\r\nimport { Input } from \"~/components/ui/input\"\r\nimport { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from \"@components/ui/table\"\r\n\r\n\r\n\r\n\r\nexport default function MyAgents() {\r\n      const [searchTerm, setSearchTerm] = useState('')\r\n      return (\r\n            <div className=\"container mx-auto p-6 \">\r\n                  <div className=\"flex justify-between items-center mb-6\">\r\n                        <h1 className=\"text-2xl font-bold\">My Agents</h1>\r\n                        <div className=\"flex gap-2\">\r\n\r\n\r\n                              <Button variant=\"outline\" size=\"icon\">\r\n                                    <RefreshCcw className=\"h-4 w-4\" />\r\n                              </Button>\r\n                        </div>\r\n                  </div>\r\n                  <div className=\"flex justify-between mb-4\">\r\n                        <Input\r\n                              placeholder=\"Search by name\"\r\n                              value={searchTerm}\r\n                              onChange={(e) => setSearchTerm(e.target.value)}\r\n                              className=\"max-w-sm\"\r\n                        />\r\n                  </div>\r\n                  <Table >\r\n                        <TableHeader>\r\n                              <TableRow>\r\n                                    <TableHead className=\"cursor-pointer\">Id</TableHead>\r\n                                    <TableHead className=\"cursor-pointer\">Name</TableHead>\r\n                                    <TableHead className=\"cursor-pointer\">Mobile Number</TableHead>\r\n                                    <TableHead className=\"cursor-pointer\"> Manager</TableHead>\r\n                                    <TableHead className=\"cursor-pointer\"> Is Active</TableHead>\r\n                              </TableRow>\r\n                        </TableHeader>\r\n                        <TableBody>\r\n                              <TableRow>\r\n                                    <TableCell>12312</TableCell>\r\n                              </TableRow>\r\n                        </TableBody>\r\n                  </Table>\r\n\r\n\r\n            </div>\r\n      )\r\n}"], "names": ["MyAgents", "searchTerm", "setSearchTerm", "useState", "jsxs", "className", "children", "jsx", "<PERSON><PERSON>", "variant", "size", "RefreshCcw", "Input", "placeholder", "value", "onChange", "e", "target", "Table", "TableHeader", "TableRow", "TableHead", "TableBody", "TableCell"], "mappings": ";;;;;;;;;AASA,SAAwBA,WAAW;AAC7B,QAAM,CAACC,YAAYC,aAAa,IAAIC,aAAAA,SAAS,EAAE;AAEzC,SAAAC,kCAAAA,KAAC,OAAI;AAAA,IAAAC,WAAU;AAAA,IACTC,UAAA,CAACF,kCAAA,KAAA,OAAA;AAAA,MAAIC,WAAU;AAAA,MACTC,UAAA,CAACC,kCAAA,IAAA,MAAA;AAAA,QAAGF,WAAU;AAAA,QAAqBC,UAAS;AAAA,MAAA,CAAA,GAC3CC,kCAAA,IAAA,OAAA;AAAA,QAAIF,WAAU;AAAA,QAGTC,gDAACE,QAAO;AAAA,UAAAC,SAAQ;AAAA,UAAUC,MAAK;AAAA,UACzBJ,UAACC,kCAAA,IAAAI,YAAA;AAAA,YAAWN,WAAU;AAAA,UAAU,CAAA;AAAA,QACtC,CAAA;AAAA,MACN,CAAA,CAAA;AAAA,IACN,CAAA,GACAE,kCAAA,IAAC,OAAI;AAAA,MAAAF,WAAU;AAAA,MACTC,UAAAC,kCAAA,IAACK,OAAA;AAAA,QACKC,aAAY;AAAA,QACZC,OAAOb;AAAAA,QACPc,UAAWC,OAAMd,cAAcc,EAAEC,OAAOH,KAAK;AAAA,QAC7CT,WAAU;AAAA,MAChB,CAAA;AAAA,IACN,CAAA,0CACCa,OACK;AAAA,MAAAZ,UAAA,CAACC,kCAAA,IAAAY,aAAA;AAAA,QACKb,iDAACc,UACK;AAAA,UAAAd,UAAA,CAACC,kCAAA,IAAAc,WAAA;AAAA,YAAUhB,WAAU;AAAA,YAAiBC,UAAE;AAAA,UAAA,CAAA,GACvCC,kCAAA,IAAAc,WAAA;AAAA,YAAUhB,WAAU;AAAA,YAAiBC,UAAI;AAAA,UAAA,CAAA,GACzCC,kCAAA,IAAAc,WAAA;AAAA,YAAUhB,WAAU;AAAA,YAAiBC,UAAa;AAAA,UAAA,CAAA,GAClDC,kCAAA,IAAAc,WAAA;AAAA,YAAUhB,WAAU;AAAA,YAAiBC,UAAQ;AAAA,UAAA,CAAA,GAC7CC,kCAAA,IAAAc,WAAA;AAAA,YAAUhB,WAAU;AAAA,YAAiBC,UAAU;AAAA,UAAA,CAAA,CAAA;AAAA,QACtD,CAAA;AAAA,MACN,CAAA,GACAC,kCAAA,IAACe;QACKhB,UAACC,kCAAA,IAAAa,UAAA;AAAA,UACKd,gDAACiB,WAAU;AAAA,YAAAjB,UAAA;AAAA,UAAK,CAAA;AAAA,QACtB,CAAA;AAAA,MACN,CAAA,CAAA;AAAA,IACN,CAAA,CAAA;AAAA,EAGN,CAAA;AAEZ;"}