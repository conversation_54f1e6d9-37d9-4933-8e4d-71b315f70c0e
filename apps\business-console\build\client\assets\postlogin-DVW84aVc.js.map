{"version": 3, "file": "postlogin-DVW84aVc.js", "sources": ["../../../app/routes/postlogin.tsx"], "sourcesContent": ["// app/routes/postlogin.tsx\r\n\r\nimport { LoaderFunction, redirect, json } from '@remix-run/node';\r\nimport { useLoaderData } from '@remix-run/react';\r\nimport { getSession } from '~/utils/session.server'; // Ensure this path is correct\r\nimport type { User } from '~/types';\r\n\r\n// Define the shape of the loader data\r\ninterface LoaderData {\r\n    access_token: string;\r\n    user: User;\r\n}\r\n\r\n// Loader Function: Fetches access_token and user from the session\r\nexport const loader: LoaderFunction = async ({ request }) => {\r\n    // Retrieve the session from the request's cookies\r\n    const session = await getSession(request.headers.get('Cookie'));\r\n\r\n    // Extract access_token and user from the session\r\n    const access_token = session.get('access_token');\r\n    const user: User | null = session.get('user');\r\n\r\n    // If access_token or user is missing, redirect to the login page\r\n    if (!access_token || !user) {\r\n        return redirect('/login');\r\n    }\r\n\r\n    // Return the access_token and user data as JSO<PERSON>\r\n    return json<LoaderData>({ access_token: access_token, user });\r\n};\r\n\r\n// PostLogin Component: Displays user information\r\nexport default function PostLogin() {\r\n    // Access the data returned by the loader\r\n    const { access_token, user } = useLoaderData<LoaderData>();\r\n\r\n    return (\r\n        <div className=\"flex flex-col items-center justify-center min-h-screen bg-gray-100\">\r\n            <div className=\"bg-white p-8 rounded shadow-md text-center\">\r\n                <h1 className=\"text-2xl font-bold mb-4\">Welcome, {user.userName}!</h1>\r\n                <p className=\"text-gray-700 mb-2\">\r\n                    <strong>Business Name:</strong> {user.businessName}\r\n                </p>\r\n\r\n                <p className=\"text-gray-700\">\r\n                    <strong>Authorized with token:</strong> {access_token}\r\n                </p>\r\n            </div>\r\n        </div>\r\n    );\r\n}\r\n"], "names": ["PostLogin", "access_token", "user", "useLoaderData", "className", "children", "jsxs", "userName", "jsx", "businessName"], "mappings": ";;;;AAgCA,SAAwBA,YAAY;AAEhC,QAAM;AAAA,IAAEC;AAAAA,IAAcC;AAAAA,EAAK,IAAIC,cAA0B;AAEzD,+CACK,OAAI;AAAA,IAAAC,WAAU;AAAA,IACXC,UAACC,kCAAA,KAAA,OAAA;AAAA,MAAIF,WAAU;AAAA,MACXC,UAAA,CAACC,kCAAA,KAAA,MAAA;AAAA,QAAGF,WAAU;AAAA,QAA0BC,UAAA,CAAA,aAAUH,KAAKK,UAAS,GAAA;AAAA,MAAC,CAAA,GACjED,kCAAA,KAAC,KAAE;AAAA,QAAAF,WAAU;AAAA,QACTC,UAAA,CAAAG,kCAAA,IAAC;UAAOH,UAAc;AAAA,QAAA,CAAA,GAAS,KAAEH,KAAKO,YAAA;AAAA,MAC1C,CAAA,GAEAH,kCAAA,KAAC,KAAE;AAAA,QAAAF,WAAU;AAAA,QACTC,UAAA,CAAAG,kCAAA,IAAC;UAAOH,UAAsB;AAAA,QAAA,CAAA,GAAS,KAAEJ,YAAA;AAAA,MAC7C,CAAA,CAAA;AAAA,IACJ,CAAA;AAAA,EACJ,CAAA;AAER;"}