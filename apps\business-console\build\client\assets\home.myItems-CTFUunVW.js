import { r as reactExports, j as jsxRuntimeExports } from "./jsx-runtime-bB2y7OuD.js";
import { B as Button } from "./button-ByAXMyvk.js";
import { I as Input } from "./input-3v87qohQ.js";
import { T as Table, a as TableHeader, b as TableRow, c as TableHead, d as TableBody, e as TableCell } from "./table-CAxdJZsY.js";
import { S as Select, a as SelectTrigger, b as SelectValue, c as SelectContent, d as SelectItem } from "./select-BFGSXKcr.js";
import { a as useNavigate } from "./index-DhHTcibu.js";
import { u as useLoaderData, L as Link, F as Form } from "./components-D7UvGag_.js";
import "./index-z_byfFrQ.js";
import "./index-ImHKLo0a.js";
import "./utils-GkgzjW3c.js";
import "./index-Vp2vNLNM.js";
import "./index-IXOTxK3N.js";
import "./index-D7VH9Fc8.js";
import "./index-qCtcpOcW.js";
import "./index-BTdCMChR.js";
import "./index-BXzPK7u0.js";
import "./index-CzZ6EYkN.js";
import "./index-CEHS9zzk.js";
import "./index-DVTNuYOr.js";
import "./index-DscYByPT.js";
import "./index-CkL5tk39.js";
import "./index-dIGjFc0I.js";
import "./index-CpKiYcZd.js";
import "./index-C88PRvfd.js";
import "./chevron-down-pCP5jmjX.js";
import "./createLucideIcon-uwkRm45G.js";
import "./check-_dbWxIzT.js";
function MyItems() {
  const navigate = useNavigate();
  const {
    data: sellerItems,
    currentPage,
    hasNextPage,
    totalPages
  } = useLoaderData();
  const [searchTerm, setSearchTerm] = reactExports.useState("");
  const [pageSize, setPageSize] = reactExports.useState("50");
  const handlePageSizeChange = (newPageSize) => {
    setPageSize(newPageSize);
    navigate(`/home/<USER>
  };
  const filterSellerItems = sellerItems.filter((item) => item.name.toLowerCase().includes(searchTerm.toLowerCase()));
  const handlePageSearch = (value) => {
    if (value.length >= 3) {
      setSearchTerm(value);
      navigate(`/home/<USER>
    } else {
      setSearchTerm(value);
    }
  };
  return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
    className: "container mx-auto p-6",
    children: [/* @__PURE__ */ jsxRuntimeExports.jsx("div", {
      className: "flex justify-between items-center mb-6",
      children: /* @__PURE__ */ jsxRuntimeExports.jsx("h1", {
        className: "text-2xl font-bold",
        children: "My Items"
      })
    }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
      className: "flex justify-between mb-4",
      children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Input, {
        placeholder: "Search by Item Name",
        value: searchTerm,
        onChange: (e) => handlePageSearch(e.target.value),
        className: "max-w-sm"
      }), /* @__PURE__ */ jsxRuntimeExports.jsxs(Select, {
        value: pageSize,
        onValueChange: handlePageSizeChange,
        children: [/* @__PURE__ */ jsxRuntimeExports.jsx(SelectTrigger, {
          className: "w-[180px]",
          children: /* @__PURE__ */ jsxRuntimeExports.jsx(SelectValue, {
            placeholder: "Items per page"
          })
        }), /* @__PURE__ */ jsxRuntimeExports.jsxs(SelectContent, {
          children: [/* @__PURE__ */ jsxRuntimeExports.jsx(SelectItem, {
            value: "5",
            children: "5 per page"
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(SelectItem, {
            value: "10",
            children: "10 per page"
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(SelectItem, {
            value: "20",
            children: "20 per page"
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(SelectItem, {
            value: "50",
            children: "50 per page"
          })]
        })]
      })]
    }), /* @__PURE__ */ jsxRuntimeExports.jsxs(Table, {
      children: [/* @__PURE__ */ jsxRuntimeExports.jsx(TableHeader, {
        children: /* @__PURE__ */ jsxRuntimeExports.jsxs(TableRow, {
          children: [/* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
            children: "Item Image"
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
            children: "Item Name"
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
            children: "Item Unit"
          })]
        })
      }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableBody, {
        children: filterSellerItems.sort((a, b) => a.name.localeCompare(b.name)).map((item) => /* @__PURE__ */ jsxRuntimeExports.jsxs(TableRow, {
          children: [/* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
            children: /* @__PURE__ */ jsxRuntimeExports.jsx("img", {
              src: item.picture,
              alt: "ItemImage",
              className: "h-10 w-10"
            })
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
            children: /* @__PURE__ */ jsxRuntimeExports.jsx(Link, {
              to: `/home/<USER>/${item.Id}`,
              className: "text-blue-600 hover:underline",
              children: /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
                children: /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
                  children: item.name
                })
              })
            })
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
            children: item.unit
          })]
        }, item.Id))
      })]
    }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
      className: "flex items-center justify-between space-x-2 py-4",
      children: [/* @__PURE__ */ jsxRuntimeExports.jsx("div", {
        className: "flex-1 text-sm text-muted-foreground"
      }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
        className: "flex items-center space-x-2",
        children: [/* @__PURE__ */ jsxRuntimeExports.jsxs(Form, {
          method: "get",
          children: [/* @__PURE__ */ jsxRuntimeExports.jsx("input", {
            type: "hidden",
            name: "page",
            value: currentPage - 1
          }), /* @__PURE__ */ jsxRuntimeExports.jsx("input", {
            type: "hidden",
            name: "pageSize",
            value: pageSize
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
            variant: "outline",
            size: "sm",
            type: "submit",
            disabled: currentPage <= 0,
            children: "Previous"
          })]
        }), /* @__PURE__ */ jsxRuntimeExports.jsxs(Form, {
          method: "get",
          children: [/* @__PURE__ */ jsxRuntimeExports.jsx("input", {
            type: "hidden",
            name: "page",
            value: currentPage + 1
          }), /* @__PURE__ */ jsxRuntimeExports.jsx("input", {
            type: "hidden",
            name: "pageSize",
            value: pageSize
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
            variant: "outline",
            size: "sm",
            type: "submit",
            disabled: !hasNextPage,
            children: "Next"
          })]
        })]
      })]
    })]
  });
}
export {
  MyItems as default
};
//# sourceMappingURL=home.myItems-CTFUunVW.js.map
