{"version": 3, "file": "sellerSetting.coupon.add-BzpabJaC.js", "sources": ["../../../app/routes/sellerSetting.coupon.add.tsx"], "sourcesContent": ["import type { LoaderFunction } from \"@remix-run/node\";\r\nimport { json, use<PERSON><PERSON>cher, use<PERSON>oader<PERSON><PERSON>, useNavigate } from \"@remix-run/react\";\r\nimport { CalendarIcon, PlusIcon, Trash2Icon } from \"lucide-react\";\r\nimport { useEffect, useMemo, useState } from \"react\";\r\nimport { Button } from \"~/components/ui/button\";\r\nimport { Calendar } from \"~/components/ui/calendar\";\r\nimport { Input } from \"~/components/ui/input\";\r\nimport {\r\n  Popover,\r\n  PopoverContent,\r\n  PopoverTrigger,\r\n} from \"~/components/ui/popover\";\r\nimport {\r\n  Select,\r\n  SelectTrigger,\r\n  SelectValue,\r\n  SelectContent,\r\n  SelectItem,\r\n} from \"~/components/ui/select\";\r\nimport { addCoupon, getCouponPreload } from \"~/services/coupons\";\r\nimport { withAuth, withResponse } from \"~/utils/auth-utils\";\r\nimport { format } from \"date-fns\";\r\nimport { useToast } from \"~/components/ui/ToastProvider\";\r\nimport { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from \"~/components/ui/tabs\";\r\nimport {\r\n  CouponPreloadType,\r\n  CouponPayloadType,\r\n  AddCouponType,\r\n  IdConfigItem,\r\n  ConfigKey,\r\n  ConfigAdditionalKey,\r\n  configKeysEnum,\r\n  DISCOUNT_UNIT_MAP,\r\n  CouponDetailsType,\r\n} from \"~/types/api/businessConsoleService/coupons\";\r\n\r\nexport const loader: LoaderFunction = withAuth(async ({ request }) => {\r\n  try {\r\n    const response = await getCouponPreload(request);\r\n    return withResponse({ data: response.data }, response.headers);\r\n  } catch (error) {\r\n    console.error(\"Error in loader:\", error);\r\n    throw new Response(\"Failed to fetch coupon data\", {\r\n      status: 500,\r\n    });\r\n  }\r\n});\r\n\r\nexport const action = withAuth(async ({ request }) => {\r\n  const formData = await request.formData();\r\n  const actionType = formData.get(\"actionType\");\r\n  const payloadRaw = formData.get(\"payload\");\r\n\r\n  if (actionType === \"addCoupon\") {\r\n    try {\r\n      const parsedPayload = JSON.parse(payloadRaw as string);\r\n      const requestPayload: AddCouponType = transformToAddCoupon(parsedPayload);\r\n      const response = await addCoupon(requestPayload, request);\r\n      return withResponse(\r\n        { data: response.data, success: true },\r\n        response.headers\r\n      );\r\n    } catch (error) {\r\n      console.error(\"Error in action:\", error);\r\n      return json({ data: null, success: false }, { status: 500 });\r\n    }\r\n  }\r\n\r\n  console.log(\"Invalid action type:\", actionType);\r\n  return json({ data: null, success: false }, { status: 400 });\r\n});\r\n\r\nfunction transformToAddCoupon(payload: CouponPayloadType): AddCouponType {\r\n  const convertItems = (\r\n    base?: IdConfigItem[],\r\n    additional?: IdConfigItem[]\r\n  ): { property: string; operator: string; value: string }[] => {\r\n    const allItems = [...(base || []), ...(additional || [])];\r\n\r\n    return allItems\r\n      .filter((item) => item.value !== undefined && item.value !== \"\")\r\n      .map((item) => ({\r\n        property: item.property,\r\n        operator: item.operator?.trim() || \"EQ\",\r\n        value: item.value,\r\n      }));\r\n  };\r\n\r\n  return {\r\n    code: payload.couponCode || \"\",\r\n    name: payload.couponType,\r\n    description: payload.description || \"\",\r\n    discountConfiguration: convertItems(\r\n      payload.customConfiguration?.discount,\r\n      payload.customConfiguration?.discountAdditional\r\n    ),\r\n    filterConfiguration: convertItems(\r\n      payload.customConfiguration?.filter,\r\n      payload.customConfiguration?.filterAdditional\r\n    ),\r\n    validityConfiguration: convertItems(\r\n      payload.customConfiguration?.validity,\r\n      payload.customConfiguration?.validityAdditional\r\n    ),\r\n  };\r\n}\r\n\r\n// Helper function to generate unique IDs\r\nconst generateId = () =>\r\n  Date.now().toString(36) + Math.random().toString(36).substring(2, 9);\r\n\r\nexport default function AddCoupon() {\r\n  const { data: couponPreloadData } = useLoaderData<{\r\n    data: CouponPreloadType;\r\n  }>();\r\n  const fetcher = useFetcher<{ data: CouponDetailsType; success: boolean }>();\r\n  const navigate = useNavigate();\r\n  const { showToast } = useToast();\r\n\r\n  useEffect(() => {\r\n    if (fetcher.state === \"idle\" && fetcher.data) {\r\n      if (fetcher.data.success === true) {\r\n        showToast(\"Coupon created successfully\", \"success\");\r\n        navigate(\"/sellerSetting/coupons\");\r\n      } else if (fetcher.data.success === false) {\r\n        showToast(\"Failed to create coupon\", \"error\");\r\n      }\r\n    }\r\n  }, [fetcher.state, fetcher.data]);\r\n\r\n  const [payload, setPayload] = useState<CouponPayloadType>({\r\n    couponType: \"\",\r\n    customConfiguration: {\r\n      discount: [],\r\n      filter: [],\r\n      validity: [],\r\n      discountAdditional: [],\r\n      filterAdditional: [],\r\n      validityAdditional: [],\r\n    },\r\n  });\r\n\r\n  // Get the selected coupon type data\r\n  const selectedTypeData = payload.couponType\r\n    ? couponPreloadData.couponTypesConfigurations.find(\r\n      (type) => type.value === payload.couponType\r\n    )\r\n    : null;\r\n\r\n  // Initialize configuration when coupon type changes\r\n  useEffect(() => {\r\n    if (!selectedTypeData) return;\r\n\r\n    const initSection = (section: ConfigKey): IdConfigItem[] => {\r\n      const props = selectedTypeData.configuration[section].properties;\r\n      if (props.length === 0) return [];\r\n\r\n      return [\r\n        {\r\n          id: generateId(),\r\n          property: props[0].value,\r\n          operator: props[0].operatorConfig?.options?.[0]?.value || \"\",\r\n          value: \"\",\r\n        },\r\n      ];\r\n    };\r\n\r\n    // Initialize fixed inputs from propertyConfig\r\n    const initFixedInputs = (section: ConfigKey): IdConfigItem[] => {\r\n      const fixedInputs: IdConfigItem[] = [];\r\n\r\n      // Check if section has propertyConfig\r\n      if (selectedTypeData.configuration[section].propertyConfig) {\r\n        selectedTypeData.configuration[section].propertyConfig.forEach(\r\n          (config) => {\r\n            if (config.type === \"fixed\") {\r\n              fixedInputs.push({\r\n                id: generateId(),\r\n                property: config.value,\r\n                value: \"\",\r\n              });\r\n            }\r\n          }\r\n        );\r\n      }\r\n\r\n      return fixedInputs;\r\n    };\r\n\r\n    setPayload((prev) => ({\r\n      ...prev,\r\n      customConfiguration: {\r\n        discount: initSection(\"discount\"),\r\n        filter: initSection(\"filter\"),\r\n        validity: initSection(\"validity\"),\r\n        discountAdditional: initFixedInputs(\"discount\"),\r\n        filterAdditional: initFixedInputs(\"filter\"),\r\n        validityAdditional: initFixedInputs(\"validity\"),\r\n      },\r\n    }));\r\n  }, [payload.couponType, selectedTypeData]);\r\n\r\n  // Get available properties for a section (excluding already selected ones)\r\n  const getAvailableProperties = (section: ConfigKey) => {\r\n    if (!selectedTypeData) return [];\r\n\r\n    if (section === \"filter\") {\r\n      // For filter section, return all properties\r\n      return selectedTypeData.configuration[section].properties;\r\n    }\r\n\r\n    // For discount and validity sections, filter out already selected properties\r\n    const selectedProperties =\r\n      payload.customConfiguration?.[section].map((item) => item.property) || [];\r\n\r\n    return selectedTypeData.configuration[section].properties.filter(\r\n      (prop) => !selectedProperties.includes(prop.value)\r\n    );\r\n  };\r\n\r\n  // Handle property change\r\n  const handlePropertyChange = (\r\n    section: ConfigKey,\r\n    id: string,\r\n    value: string\r\n  ) => {\r\n    if (!payload.customConfiguration) return;\r\n\r\n    // Find the current item\r\n    const currentItem = payload.customConfiguration[section].find(\r\n      (item) => item.id === id\r\n    );\r\n\r\n    // If the property is already set to this value, no need to change\r\n    if (currentItem?.property === value) {\r\n      return;\r\n    }\r\n\r\n    // For discount and validity sections, check if the property is already selected in other rows\r\n    if (section === \"discount\" || section === \"validity\") {\r\n      const isAlreadySelected = payload.customConfiguration[section].some(\r\n        (item) => item.id !== id && item.property === value\r\n      );\r\n\r\n      if (isAlreadySelected) {\r\n        console.warn(\r\n          `Property ${value} is already selected in ${section} section`\r\n        );\r\n        return;\r\n      }\r\n    }\r\n\r\n    const sectionConfig = [...payload.customConfiguration[section]];\r\n    const itemIndex = sectionConfig.findIndex((item) => item.id === id);\r\n\r\n    if (itemIndex === -1) return;\r\n\r\n    // Find the property configuration\r\n    const propertyConfig = selectedTypeData?.configuration[\r\n      section\r\n    ].properties.find((prop) => prop.value === value);\r\n\r\n    // Update the property and reset operator and value\r\n    sectionConfig[itemIndex] = {\r\n      ...sectionConfig[itemIndex],\r\n      property: value,\r\n      operator:\r\n        propertyConfig?.operatorConfig?.options?.[0]?.value || undefined,\r\n      value: \"\",\r\n    };\r\n\r\n    setPayload({\r\n      ...payload,\r\n      customConfiguration: {\r\n        ...payload.customConfiguration,\r\n        [section]: sectionConfig,\r\n      },\r\n    });\r\n  };\r\n\r\n  // Handle operator change\r\n  const handleOperatorChange = (\r\n    section: ConfigKey,\r\n    id: string,\r\n    value: string\r\n  ) => {\r\n    if (!payload.customConfiguration) return;\r\n\r\n    const sectionConfig = [...payload.customConfiguration[section]];\r\n    const itemIndex = sectionConfig.findIndex((item) => item.id === id);\r\n\r\n    if (itemIndex === -1) return;\r\n\r\n    sectionConfig[itemIndex] = {\r\n      ...sectionConfig[itemIndex],\r\n      operator: value,\r\n    };\r\n\r\n    setPayload({\r\n      ...payload,\r\n      customConfiguration: {\r\n        ...payload.customConfiguration,\r\n        [section]: sectionConfig,\r\n      },\r\n    });\r\n  };\r\n\r\n  // Handle value change\r\n  const handleValueChange = (\r\n    section: ConfigKey | ConfigAdditionalKey,\r\n    id: string,\r\n    value: string\r\n  ) => {\r\n    if (!payload.customConfiguration) return;\r\n\r\n    const sectionConfig = [...(payload.customConfiguration[section] || [])];\r\n    const itemIndex = sectionConfig.findIndex((item) => item.id === id);\r\n\r\n    if (itemIndex === -1) return;\r\n\r\n    sectionConfig[itemIndex] = {\r\n      ...sectionConfig[itemIndex],\r\n      value: value,\r\n    };\r\n\r\n    setPayload({\r\n      ...payload,\r\n      customConfiguration: {\r\n        ...payload.customConfiguration,\r\n        [section]: sectionConfig,\r\n      },\r\n    });\r\n  };\r\n\r\n  // Handle date change\r\n  const handleDateChange = (\r\n    section: ConfigKey,\r\n    id: string,\r\n    date: Date | undefined\r\n  ) => {\r\n    if (!payload.customConfiguration || !date) return;\r\n\r\n    const sectionConfig = [...payload.customConfiguration[section]];\r\n    const itemIndex = sectionConfig.findIndex((item) => item.id === id);\r\n\r\n    if (itemIndex === -1) return;\r\n\r\n    sectionConfig[itemIndex] = {\r\n      ...sectionConfig[itemIndex],\r\n      value: format(date, \"yyyy-MM-dd HH:mm:ss\"),\r\n    };\r\n\r\n    setPayload({\r\n      ...payload,\r\n      customConfiguration: {\r\n        ...payload.customConfiguration,\r\n        [section]: sectionConfig,\r\n      },\r\n    });\r\n  };\r\n\r\n  // Add a new row to a section\r\n  const addRow = (section: ConfigKey) => {\r\n    if (!payload.customConfiguration || !selectedTypeData) return;\r\n\r\n    const properties = selectedTypeData.configuration[section].properties;\r\n    if (properties.length === 0) return;\r\n\r\n    // For discount and validity sections, check if we've reached the maximum number of rows\r\n    if (section === \"discount\" || section === \"validity\") {\r\n      const currentRows = payload.customConfiguration[section].length;\r\n      const totalProperties = properties.length;\r\n\r\n      if (currentRows >= totalProperties) {\r\n        console.warn(\r\n          `Cannot add more rows to ${section} section. Maximum reached.`\r\n        );\r\n        return;\r\n      }\r\n\r\n      // Get the first available property that's not already selected\r\n      const availableProperties = getAvailableProperties(section);\r\n      if (availableProperties.length === 0) {\r\n        console.warn(`No more available properties for ${section} section`);\r\n        return;\r\n      }\r\n\r\n      const newProperty = availableProperties[0].value;\r\n      const newOperator =\r\n        availableProperties[0].operatorConfig?.options?.[0]?.value;\r\n\r\n      const newItem: IdConfigItem = {\r\n        id: generateId(),\r\n        property: newProperty,\r\n        operator: newOperator,\r\n        value: \"\",\r\n      };\r\n\r\n      setPayload({\r\n        ...payload,\r\n        customConfiguration: {\r\n          ...payload.customConfiguration,\r\n          [section]: [...payload.customConfiguration[section], newItem],\r\n        },\r\n      });\r\n    } else {\r\n      // For filter section, no restrictions\r\n      const newItem: IdConfigItem = {\r\n        id: generateId(),\r\n        property: properties[0].value,\r\n        operator: properties[0].operatorConfig?.options?.[0]?.value,\r\n        value: \"\",\r\n      };\r\n\r\n      setPayload({\r\n        ...payload,\r\n        customConfiguration: {\r\n          ...payload.customConfiguration,\r\n          [section]: [...payload.customConfiguration[section], newItem],\r\n        },\r\n      });\r\n    }\r\n  };\r\n\r\n  // Remove a row from a section\r\n  const removeRow = (section: ConfigKey, id: string) => {\r\n    if (!payload.customConfiguration) return;\r\n\r\n    const sectionConfig = payload.customConfiguration[section].filter(\r\n      (item) => item.id !== id\r\n    );\r\n\r\n    if (sectionConfig.length === 0) {\r\n      // Prevent removing the last row, or re-add a default one\r\n      addRow(section);\r\n      return;\r\n    }\r\n\r\n    setPayload({\r\n      ...payload,\r\n      customConfiguration: {\r\n        ...payload.customConfiguration,\r\n        [section]: sectionConfig,\r\n      },\r\n    });\r\n  };\r\n\r\n  // Get the unit for a property\r\n  const getPropertyUnit = (property: string) => {\r\n    return DISCOUNT_UNIT_MAP[property] || \"\";\r\n  };\r\n\r\n  // Render operator input based on input type\r\n  const renderOperatorInput = (\r\n    section: ConfigKey,\r\n    item: IdConfigItem,\r\n    property: any\r\n  ) => {\r\n    if (!property?.operatorConfig) return null;\r\n\r\n    const inputType = property.operatorConfig.input;\r\n\r\n    switch (inputType) {\r\n      case \"dropdown\":\r\n        return (\r\n          <Select\r\n            value={item.operator || \"\"}\r\n            onValueChange={(value) =>\r\n              handleOperatorChange(section, item.id, value)\r\n            }\r\n          >\r\n            <SelectTrigger className=\"rounded-lg\">\r\n              <SelectValue placeholder=\"Operator\" />\r\n            </SelectTrigger>\r\n            <SelectContent className=\"rounded-lg\">\r\n              {property.operatorConfig.options?.map((option: any) => (\r\n                <SelectItem key={option.value} value={option.value}>\r\n                  {option.label}\r\n                </SelectItem>\r\n              ))}\r\n            </SelectContent>\r\n          </Select>\r\n        );\r\n      case \"text\":\r\n        return (\r\n          <Input\r\n            type=\"text\"\r\n            value={item.operator || \"\"}\r\n            onChange={(e) =>\r\n              handleOperatorChange(section, item.id, e.target.value)\r\n            }\r\n            placeholder=\"Operator\"\r\n          />\r\n        );\r\n      case \"itemSearch\":\r\n        return (\r\n          <Input\r\n            type=\"text\"\r\n            value={item.operator || \"\"}\r\n            onChange={(e) =>\r\n              handleOperatorChange(section, item.id, e.target.value)\r\n            }\r\n            placeholder=\"Search item...\"\r\n          />\r\n        );\r\n      default:\r\n        return (\r\n          <Input\r\n            type=\"text\"\r\n            value={item.operator || \"\"}\r\n            onChange={(e) =>\r\n              handleOperatorChange(section, item.id, e.target.value)\r\n            }\r\n            placeholder=\"Operator\"\r\n          />\r\n        );\r\n    }\r\n  };\r\n\r\n  // Render input based on input type\r\n  const renderValueInput = (\r\n    section: ConfigKey,\r\n    item: IdConfigItem,\r\n    inputType: string\r\n  ) => {\r\n    const property = selectedTypeData?.configuration[section].properties.find(\r\n      (prop) => prop.value === item.property\r\n    );\r\n\r\n    switch (inputType) {\r\n      case \"text\":\r\n        return (\r\n          <div className=\"relative\">\r\n            <Input\r\n              type=\"text\"\r\n              value={item.value || \"\"}\r\n              onChange={(e) =>\r\n                handleValueChange(section, item.id, e.target.value)\r\n              }\r\n              className=\"pr-7\"\r\n            />\r\n            {getPropertyUnit(item.property) && (\r\n              <div className=\"absolute right-3 top-1/2 -translate-y-1/2 text-sm pointer-events-none text-muted-foreground\">\r\n                {getPropertyUnit(item.property)}\r\n              </div>\r\n            )}\r\n          </div>\r\n        );\r\n      case \"date\":\r\n        return (\r\n          <Popover>\r\n            <PopoverTrigger asChild>\r\n              <Button\r\n                type=\"button\"\r\n                variant=\"outline\"\r\n                className=\"rounded-lg w-full justify-between overflow-hidden\"\r\n              >\r\n                {item.value\r\n                  ? format(new Date(item.value), \"PPP\")\r\n                  : \"Pick a date\"}\r\n                <CalendarIcon className=\"h-4 w-4\" />\r\n              </Button>\r\n            </PopoverTrigger>\r\n            <PopoverContent className=\"w-auto p-0\">\r\n              <Calendar\r\n                mode=\"single\"\r\n                selected={item.value ? new Date(item.value) : undefined}\r\n                onSelect={(date) => handleDateChange(section, item.id, date)}\r\n                initialFocus\r\n              />\r\n            </PopoverContent>\r\n          </Popover>\r\n        );\r\n      case \"userSearch\":\r\n        return (\r\n          <Input\r\n            type=\"text\"\r\n            value={item.operator || \"\"}\r\n            onChange={(e) =>\r\n              handleOperatorChange(section, item.id, e.target.value)\r\n            }\r\n            placeholder=\"Search user...\"\r\n          />\r\n        );\r\n      case \"location\":\r\n        // For simplicity, we'll use a text input for these special types\r\n        return (\r\n          <Input\r\n            type=\"text\"\r\n            value={item.value || \"\"}\r\n            onChange={(e) =>\r\n              handleValueChange(section, item.id, e.target.value)\r\n            }\r\n            placeholder={`Search ${inputType.replace(\"Search\", \"\")}`}\r\n          />\r\n        );\r\n      case \"dropdown\":\r\n        return (\r\n          <Select\r\n            value={item.value || \"\"}\r\n            onValueChange={(value) =>\r\n              handleValueChange(section, item.id, value)\r\n            }\r\n          >\r\n            <SelectTrigger className=\"rounded-lg\">\r\n              <SelectValue placeholder=\"Select\" />\r\n            </SelectTrigger>\r\n            <SelectContent className=\"rounded-lg\">\r\n              {property?.valueConfig?.options?.map((option) => (\r\n                <SelectItem key={option.value} value={option.value}>\r\n                  {option.label}\r\n                </SelectItem>\r\n              ))}\r\n            </SelectContent>\r\n          </Select>\r\n        );\r\n      default:\r\n        return (\r\n          <Input\r\n            type=\"text\"\r\n            value={item.value || \"\"}\r\n            onChange={(e) =>\r\n              handleValueChange(section, item.id, e.target.value)\r\n            }\r\n          />\r\n        );\r\n    }\r\n  };\r\n\r\n  // Render fixed inputs from propertyConfig\r\n  const renderFixedInputs = (section: ConfigKey) => {\r\n    if (!selectedTypeData || !payload.customConfiguration) return null;\r\n\r\n    const propertyConfig =\r\n      selectedTypeData.configuration[section].propertyConfig;\r\n    if (!propertyConfig) return null;\r\n\r\n    const fixedInputs = propertyConfig.filter(\r\n      (input) => input.type === \"fixed\"\r\n    );\r\n    if (fixedInputs.length === 0) return null;\r\n\r\n    return (\r\n      <div className=\"mt-4\">\r\n        {fixedInputs.map((input) => {\r\n          // Find the corresponding item in payload Additionals\r\n          const item = payload.customConfiguration?.[\r\n            configKeysEnum[section]\r\n          ]?.find((item) => item.property === input.value);\r\n\r\n          if (!item) return null;\r\n\r\n          return (\r\n            <div key={item.id} className=\"mt-2\">\r\n              <div className=\"flex flex-row gap-2 items-center\">\r\n                <label className=\"text-sm font-medium\">{input.label}:</label>\r\n                <div>\r\n                  <Input\r\n                    type=\"text\"\r\n                    value={item.value || \"\"}\r\n                    onChange={(e) =>\r\n                      handleValueChange(\r\n                        configKeysEnum[section],\r\n                        item.id,\r\n                        e.target.value\r\n                      )\r\n                    }\r\n                  />\r\n                </div>\r\n              </div>\r\n            </div>\r\n          );\r\n        })}\r\n      </div>\r\n    );\r\n  };\r\n\r\n  // Render a configuration section\r\n  const renderConfigSection = (section: ConfigKey) => {\r\n    if (!selectedTypeData || !payload.customConfiguration) return null;\r\n\r\n    const sectionConfig = selectedTypeData.configuration[section];\r\n    const items = payload.customConfiguration[section];\r\n\r\n    // Find property method config if it exists\r\n    const propertyMethodInput = sectionConfig.propertyConfig?.find(\r\n      (config) => config.type === \"propertyMethod\"\r\n    );\r\n\r\n    // Check if we can add more rows for this section\r\n    const canAddMoreRows =\r\n      section === \"filter\" || items.length < sectionConfig.properties.length;\r\n\r\n    return (\r\n      <div className=\"p-3 pr-8 rounded-lg border mb-3\">\r\n        <p className=\"text-typography-800 font-semibold\">\r\n          {sectionConfig.label} Configurations\r\n        </p>\r\n        {sectionConfig.description && (\r\n          <p className=\"text-sm font-medium text-blue-600\">\r\n            {sectionConfig.description}\r\n          </p>\r\n        )}\r\n\r\n        {items.map((item, index) => {\r\n          const property = sectionConfig.properties.find(\r\n            (prop) => prop.value === item.property\r\n          );\r\n\r\n          const hasOperator = !!property?.operatorConfig;\r\n          const valueInputType = property?.valueConfig?.input || \"text\";\r\n\r\n          // For discount and validity sections, only show properties that aren't already selected\r\n          // or the current property of this row\r\n          const availableProperties =\r\n            section === \"filter\"\r\n              ? sectionConfig.properties\r\n              : sectionConfig.properties.filter(\r\n                (prop) =>\r\n                  prop.value === item.property ||\r\n                  !payload.customConfiguration?.[section].some(\r\n                    (i) => i.id !== item.id && i.property === prop.value\r\n                  )\r\n              );\r\n\r\n          return (\r\n            <div key={item.id} className=\"mt-3\">\r\n              <div\r\n                className={`relative grid ${hasOperator ? \"grid-cols-3\" : \"grid-cols-2\"\r\n                  } gap-2 items-center`}\r\n              >\r\n                <Select\r\n                  value={item.property}\r\n                  onValueChange={(value) =>\r\n                    handlePropertyChange(section, item.id, value)\r\n                  }\r\n                >\r\n                  <SelectTrigger className=\"rounded-lg\">\r\n                    <SelectValue placeholder=\"Property\" />\r\n                  </SelectTrigger>\r\n                  <SelectContent className=\"rounded-lg\">\r\n                    {availableProperties.map((property) => (\r\n                      <SelectItem key={property.value} value={property.value}>\r\n                        {property.label}\r\n                      </SelectItem>\r\n                    ))}\r\n                  </SelectContent>\r\n                </Select>\r\n\r\n                {hasOperator && renderOperatorInput(section, item, property)}\r\n\r\n                {renderValueInput(section, item, valueInputType)}\r\n\r\n                {items.length > 1 && (\r\n                  <div\r\n                    className=\"absolute -right-7 top-1/2 -translate-y-1/2 px-1 py-2 cursor-pointer hover:bg-accent rounded-md\"\r\n                    onClick={() => removeRow(section, item.id)}\r\n                  >\r\n                    <Trash2Icon className=\"h-5 w-5 text-red-500\" />\r\n                  </div>\r\n                )}\r\n              </div>\r\n              {index === 0 && propertyMethodInput && (\r\n                <PropertyMethodInput\r\n                  section={section}\r\n                  propertyInput={propertyMethodInput}\r\n                  payload={payload}\r\n                  setPayload={setPayload}\r\n                  selectedTypeData={selectedTypeData}\r\n                />\r\n              )}\r\n            </div>\r\n          );\r\n        })}\r\n\r\n        {canAddMoreRows && (\r\n          <Button\r\n            type=\"button\"\r\n            className=\"mt-3 underline\"\r\n            variant=\"link\"\r\n            onClick={() => addRow(section)}\r\n          >\r\n            <PlusIcon className=\"w-4 h-4 mr-1\" /> Add another{\" \"}\r\n            {section.toLowerCase()}\r\n          </Button>\r\n        )}\r\n\r\n        {/* Render fixed inputs at the bottom of the section */}\r\n        {renderFixedInputs(section)}\r\n      </div>\r\n    );\r\n  };\r\n\r\n  return (\r\n    <div>\r\n      <div>\r\n        <div\r\n          aria-labelledby=\"add-coupon-heading\"\r\n          className=\"flex flex-row gap-2 items-center p-2\"\r\n        >\r\n          <div\r\n            className=\"w-5 h-5 cursor-pointer\"\r\n            onClick={() => navigate(\"/sellerSetting/coupons\")}\r\n          >\r\n            <svg\r\n              width=\"100%\"\r\n              height=\"100%\"\r\n              viewBox=\"0 0 16 16\"\r\n              fill=\"none\"\r\n              xmlns=\"http://www.w3.org/2000/svg\"\r\n            >\r\n              <path\r\n                fillRule=\"evenodd\"\r\n                clipRule=\"evenodd\"\r\n                d=\"M7.13805 3.36193C7.3984 3.62228 7.3984 4.04439 7.13805 4.30474L3.60946 7.83333H14C14.3682 7.83333 14.6666 8.13181 14.6666 8.5C14.6666 8.86819 14.3682 9.16667 14 9.16667H3.60946L7.13805 12.6953C7.3984 12.9556 7.3984 13.3777 7.13805 13.6381C6.8777 13.8984 6.45559 13.8984 6.19524 13.6381L1.52858 8.97141C1.26823 8.71106 1.26823 8.28895 1.52858 8.0286L6.19524 3.36193C6.45559 3.10158 6.8777 3.10158 7.13805 3.36193Z\"\r\n                fill=\"#1F2A37\"\r\n              />\r\n            </svg>\r\n          </div>\r\n          <h2 className=\"text-lg font-semibold text-typography-700\">\r\n            Add new coupon\r\n          </h2>\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"pt-3 pb-20\">\r\n        <fetcher.Form method=\"post\">\r\n          <input type=\"hidden\" name=\"actionType\" value=\"addCoupon\" />\r\n          <input type=\"hidden\" name=\"payload\" value={JSON.stringify(payload)} />\r\n          <div aria-labelledby=\"add-coupon\" className=\"p-2\">\r\n            <div className=\"p-3 rounded-lg border mb-3\">\r\n              <p className=\"font-semibold text-typography-800\">\r\n                Coupon Details\r\n              </p>\r\n              <p className=\"text-sm font-medium text-blue-600\">\r\n                These details would be shown to the customers.\r\n              </p>\r\n              <div className=\"relative mt-3 flex flex-row gap-5 items-center justify-between\">\r\n                <p className=\"whitespace-nowrap\">Coupon type:</p>\r\n                <Select\r\n                  value={payload.couponType || \"\"}\r\n                  onValueChange={(value) => {\r\n                    setPayload({\r\n                      ...payload,\r\n                      couponType: value,\r\n                    });\r\n                  }}\r\n                >\r\n                  <SelectTrigger className=\"rounded-lg\">\r\n                    <SelectValue placeholder=\"Select\" />\r\n                  </SelectTrigger>\r\n                  <SelectContent className=\"rounded-lg\">\r\n                    {couponPreloadData.couponTypes.map((type) => (\r\n                      <SelectItem key={type.value} value={type.value}>\r\n                        {type.label}\r\n                      </SelectItem>\r\n                    ))}\r\n                  </SelectContent>\r\n                </Select>\r\n              </div>\r\n              <Input\r\n                type=\"text\"\r\n                placeholder=\"Coupon Code\"\r\n                className=\"mt-3 rounded-lg\"\r\n                value={payload.couponCode || \"\"}\r\n                onChange={(e) => {\r\n                  setPayload({\r\n                    ...payload,\r\n                    couponCode: e.target.value,\r\n                  });\r\n                }}\r\n              />\r\n              <Input\r\n                type=\"text\"\r\n                placeholder=\"Description\"\r\n                className=\"mt-3 rounded-lg\"\r\n                value={payload.description || \"\"}\r\n                onChange={(e) => {\r\n                  setPayload({\r\n                    ...payload,\r\n                    description: e.target.value,\r\n                  });\r\n                }}\r\n              />\r\n            </div>\r\n\r\n            {selectedTypeData && (\r\n              <>\r\n                {renderConfigSection(\"discount\")}\r\n                {renderConfigSection(\"filter\")}\r\n                {renderConfigSection(\"validity\")}\r\n              </>\r\n            )}\r\n          </div>\r\n\r\n          <div className=\"fixed bottom-2 left-0 right-0 z-30 flex justify-center md:static md:mt-6 md:px-4 md:justify-end\">\r\n            <Button\r\n              type=\"submit\"\r\n              disabled={fetcher.state === \"submitting\" || fetcher.state === \"loading\"}\r\n              className=\"w-[80%] md:w-auto md:px-12 pointer-events-auto font-bold flex items-center justify-center rounded-lg shadow-xl bg-primary p-3 hover:bg-primary\"\r\n            >\r\n              Save\r\n            </Button>\r\n          </div>\r\n        </fetcher.Form>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\ninterface PropertyMethodInputProps {\r\n  section: ConfigKey;\r\n  propertyInput: {\r\n    input: string;\r\n    type: string;\r\n    label: string;\r\n    value: string;\r\n    options?: { label: string; value: string }[];\r\n  };\r\n  payload: CouponPayloadType;\r\n  setPayload: React.Dispatch<React.SetStateAction<CouponPayloadType>>;\r\n  selectedTypeData?: CouponPreloadType[\"couponTypesConfigurations\"][number];\r\n}\r\n\r\nconst PropertyMethodInput: React.FC<PropertyMethodInputProps> = ({\r\n  section,\r\n  propertyInput,\r\n  payload,\r\n  setPayload,\r\n  selectedTypeData,\r\n}) => {\r\n  const shouldRender =\r\n    section === \"discount\" &&\r\n    propertyInput.type === \"propertyMethod\" &&\r\n    payload.customConfiguration?.discount?.length === 2;\r\n\r\n  const configItem = useMemo(() => {\r\n    return (\r\n      payload.customConfiguration?.discountAdditional?.find(\r\n        (item) => item.property === propertyInput.value\r\n      ) || {\r\n        id: generateId(),\r\n        property: propertyInput.value,\r\n        value: propertyInput.options?.[0]?.value || \"\",\r\n      }\r\n    );\r\n  }, [payload, propertyInput]);\r\n\r\n  const handleValueChange = (newValue: string) => {\r\n    const updatedAdditional = [\r\n      ...(payload.customConfiguration?.discountAdditional || []).filter(\r\n        (item) => item.property !== propertyInput.value\r\n      ),\r\n      {\r\n        id: configItem.id,\r\n        property: propertyInput.value,\r\n        value: newValue,\r\n      },\r\n    ];\r\n\r\n    setPayload({\r\n      ...payload,\r\n      customConfiguration: {\r\n        discount: payload.customConfiguration?.discount || [],\r\n        filter: payload.customConfiguration?.filter || [],\r\n        validity: payload.customConfiguration?.validity || [],\r\n        discountAdditional: updatedAdditional,\r\n      },\r\n    });\r\n  };\r\n\r\n  // Manage add/remove logic on mount/unmount or discount length change\r\n  useEffect(() => {\r\n    if (!payload.customConfiguration) return;\r\n\r\n    const discountItems = payload.customConfiguration.discount;\r\n    const propertyMethodConfig =\r\n      selectedTypeData?.configuration.discount.propertyConfig?.find(\r\n        (config) => config.type === \"propertyMethod\"\r\n      );\r\n    if (!propertyMethodConfig) return;\r\n\r\n    const currentAdditional = [\r\n      ...(payload.customConfiguration.discountAdditional || []),\r\n    ];\r\n    const hybDiscStrategyIndex = currentAdditional.findIndex(\r\n      (item) => item.property === propertyMethodConfig.value\r\n    );\r\n\r\n    // Add if not present and condition met\r\n    if (discountItems.length === 2 && hybDiscStrategyIndex === -1) {\r\n      currentAdditional.push({\r\n        id: generateId(),\r\n        property: propertyMethodConfig.value,\r\n        value: propertyMethodConfig.options?.[0]?.value || \"\",\r\n      });\r\n      setPayload((prev) => ({\r\n        ...prev,\r\n        customConfiguration: {\r\n          ...prev.customConfiguration,\r\n          discount: prev.customConfiguration?.discount || [],\r\n          filter: prev.customConfiguration?.filter || [],\r\n          validity: prev.customConfiguration?.validity || [],\r\n          discountAdditional: currentAdditional,\r\n        },\r\n      }));\r\n    }\r\n\r\n    // Remove if condition fails\r\n    if (discountItems.length !== 2 && hybDiscStrategyIndex !== -1) {\r\n      currentAdditional.splice(hybDiscStrategyIndex, 1);\r\n      setPayload((prev) => ({\r\n        ...prev,\r\n        customConfiguration: {\r\n          ...prev.customConfiguration,\r\n          discount: prev.customConfiguration?.discount || [],\r\n          filter: prev.customConfiguration?.filter || [],\r\n          validity: prev.customConfiguration?.validity || [],\r\n          discountAdditional: currentAdditional,\r\n        },\r\n      }));\r\n    }\r\n  }, [payload.customConfiguration?.discount?.length]);\r\n\r\n  if (!shouldRender) return null;\r\n\r\n  return (\r\n    <div className=\"my-2\">\r\n      <Tabs value={configItem.value} onValueChange={handleValueChange}>\r\n        <TabsList>\r\n          {propertyInput.options?.map((option) => (\r\n            <TabsTrigger\r\n              key={option.value}\r\n              value={option.value}\r\n              className=\"flex-1\"\r\n            >\r\n              {option.label}\r\n            </TabsTrigger>\r\n          ))}\r\n        </TabsList>\r\n      </Tabs>\r\n    </div>\r\n  );\r\n};\r\n"], "names": ["generateId", "Date", "now", "toString", "Math", "random", "substring", "AddCoupon", "data", "couponPreloadData", "useLoaderData", "fetcher", "useFetcher", "navigate", "useNavigate", "showToast", "useToast", "useEffect", "state", "success", "payload", "setPayload", "useState", "couponType", "customConfiguration", "discount", "filter", "validity", "discountAdditional", "filterAdditional", "validityAdditional", "selectedTypeData", "couponTypesConfigurations", "find", "type", "value", "initSection", "section", "props", "configuration", "properties", "length", "id", "property", "operator", "operatorConfig", "options", "initFixedInputs", "fixedInputs", "propertyConfig", "for<PERSON>ach", "config", "push", "prev", "getAvailableProperties", "selectedProperties", "map", "item", "prop", "includes", "handlePropertyChange", "currentItem", "isAlreadySelected", "some", "console", "warn", "sectionConfig", "itemIndex", "findIndex", "handleOperatorChange", "handleValueChange", "handleDateChange", "date", "format", "addRow", "currentRows", "totalProperties", "availableProperties", "newProperty", "newOperator", "newItem", "removeRow", "getPropertyUnit", "DISCOUNT_UNIT_MAP", "renderOperatorInput", "inputType", "input", "jsxs", "Select", "onValueChange", "children", "jsx", "SelectTrigger", "className", "SelectValue", "placeholder", "SelectContent", "option", "SelectItem", "label", "Input", "onChange", "e", "target", "renderValueInput", "Popover", "PopoverTrigger", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "variant", "CalendarIcon", "PopoverC<PERSON>nt", "Calendar", "mode", "selected", "onSelect", "initialFocus", "replace", "valueConfig", "renderFixedInputs", "configKeysEnum", "renderConfigSection", "items", "propertyMethodInput", "canAddMoreRows", "description", "index", "hasOperator", "valueInputType", "i", "onClick", "Trash2Icon", "PropertyMethodInput", "propertyInput", "PlusIcon", "toLowerCase", "width", "height", "viewBox", "fill", "xmlns", "fillRule", "clipRule", "d", "Form", "method", "name", "JSON", "stringify", "couponTypes", "couponCode", "Fragment", "disabled", "shouldRender", "configItem", "useMemo", "newValue", "updatedAdditional", "discountItems", "propertyMethodConfig", "currentAdditional", "hybDiscStrategyIndex", "splice", "Tabs", "TabsList", "TabsTrigger"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4GA,MAAMA,aAAaA,MACjBC,KAAKC,IAAI,EAAEC,SAAS,EAAE,IAAIC,KAAKC,SAASF,SAAS,EAAE,EAAEG,UAAU,GAAG,CAAC;AAErE,SAAwBC,YAAY;AAClC,QAAM;AAAA,IAAEC,MAAMC;AAAAA,EAAkB,IAAIC,cAEjC;AACH,QAAMC,UAAUC,WAA0D;AAC1E,QAAMC,WAAWC,YAAY;AACvB,QAAA;AAAA,IAAEC;AAAAA,EAAU,IAAIC,SAAS;AAE/BC,eAAAA,UAAU,MAAM;AACd,QAAIN,QAAQO,UAAU,UAAUP,QAAQH,MAAM;AACxC,UAAAG,QAAQH,KAAKW,YAAY,MAAM;AACjCJ,kBAAU,+BAA+B,SAAS;AAClDF,iBAAS,wBAAwB;AAAA,MACxB,WAAAF,QAAQH,KAAKW,YAAY,OAAO;AACzCJ,kBAAU,2BAA2B,OAAO;AAAA,MAC9C;AAAA,IACF;AAAA,KACC,CAACJ,QAAQO,OAAOP,QAAQH,IAAI,CAAC;AAEhC,QAAM,CAACY,SAASC,UAAU,IAAIC,sBAA4B;AAAA,IACxDC,YAAY;AAAA,IACZC,qBAAqB;AAAA,MACnBC,UAAU,CAAC;AAAA,MACXC,QAAQ,CAAC;AAAA,MACTC,UAAU,CAAC;AAAA,MACXC,oBAAoB,CAAC;AAAA,MACrBC,kBAAkB,CAAC;AAAA,MACnBC,oBAAoB,CAAA;AAAA,IACtB;AAAA,EACF,CAAC;AAGD,QAAMC,mBAAmBX,QAAQG,aAC7Bd,kBAAkBuB,0BAA0BC,KAC3CC,UAASA,KAAKC,UAAUf,QAAQG,UACnC,IACE;AAGJN,eAAAA,UAAU,MAAM;AACd,QAAI,CAACc,iBAAkB;AAEjB,UAAAK,cAAeC,aAAuC;;AAC1D,YAAMC,QAAQP,iBAAiBQ,cAAcF,OAAO,EAAEG;AACtD,UAAIF,MAAMG,WAAW,EAAG,QAAO,CAAC;AAEzB,aAAA,CACL;AAAA,QACEC,IAAI1C,WAAW;AAAA,QACf2C,UAAUL,MAAM,CAAC,EAAEH;AAAAA,QACnBS,YAAUN,uBAAM,CAAC,EAAEO,mBAATP,mBAAyBQ,YAAzBR,mBAAmC,OAAnCA,mBAAuCH,UAAS;AAAA,QAC1DA,OAAO;AAAA,MACT,CAAA;AAAA,IAEJ;AAGM,UAAAY,kBAAmBV,aAAuC;AAC9D,YAAMW,cAA8B,CAAC;AAGrC,UAAIjB,iBAAiBQ,cAAcF,OAAO,EAAEY,gBAAgB;AACzClB,yBAAAQ,cAAcF,OAAO,EAAEY,eAAeC,QACpDC,YAAW;AACN,cAAAA,OAAOjB,SAAS,SAAS;AAC3Bc,wBAAYI,KAAK;AAAA,cACfV,IAAI1C,WAAW;AAAA,cACf2C,UAAUQ,OAAOhB;AAAAA,cACjBA,OAAO;AAAA,YACT,CAAC;AAAA,UACH;AAAA,QACF,CACF;AAAA,MACF;AAEO,aAAAa;AAAAA,IACT;AAEA3B,eAAYgC,WAAU;AAAA,MACpB,GAAGA;AAAAA,MACH7B,qBAAqB;AAAA,QACnBC,UAAUW,YAAY,UAAU;AAAA,QAChCV,QAAQU,YAAY,QAAQ;AAAA,QAC5BT,UAAUS,YAAY,UAAU;AAAA,QAChCR,oBAAoBmB,gBAAgB,UAAU;AAAA,QAC9ClB,kBAAkBkB,gBAAgB,QAAQ;AAAA,QAC1CjB,oBAAoBiB,gBAAgB,UAAU;AAAA,MAChD;AAAA,IACF,EAAE;AAAA,EACD,GAAA,CAAC3B,QAAQG,YAAYQ,gBAAgB,CAAC;AAGnC,QAAAuB,yBAA0BjB,aAAuB;;AACjD,QAAA,CAACN,iBAAkB,QAAO,CAAC;AAE/B,QAAIM,YAAY,UAAU;AAEjB,aAAAN,iBAAiBQ,cAAcF,OAAO,EAAEG;AAAAA,IACjD;AAGM,UAAAe,uBACJnC,aAAQI,wBAARJ,mBAA8BiB,SAASmB,IAAKC,UAASA,KAAKd,cAAa,CAAC;AAE1E,WAAOZ,iBAAiBQ,cAAcF,OAAO,EAAEG,WAAWd,OACvDgC,UAAS,CAACH,mBAAmBI,SAASD,KAAKvB,KAAK,CACnD;AAAA,EACF;AAGA,QAAMyB,uBAAuBA,CAC3BvB,SACAK,IACAP,UACG;;AACC,QAAA,CAACf,QAAQI,oBAAqB;AAGlC,UAAMqC,cAAczC,QAAQI,oBAAoBa,OAAO,EAAEJ,KACtDwB,UAASA,KAAKf,OAAOA,EACxB;AAGI,SAAAmB,2CAAalB,cAAaR,OAAO;AACnC;AAAA,IACF;AAGI,QAAAE,YAAY,cAAcA,YAAY,YAAY;AACpD,YAAMyB,oBAAoB1C,QAAQI,oBAAoBa,OAAO,EAAE0B,KAC5DN,UAASA,KAAKf,OAAOA,MAAMe,KAAKd,aAAaR,KAChD;AAEA,UAAI2B,mBAAmB;AACbE,gBAAAC,KACN,YAAY9B,KAAK,2BAA2BE,OAAO,UACrD;AACA;AAAA,MACF;AAAA,IACF;AAEA,UAAM6B,gBAAgB,CAAC,GAAG9C,QAAQI,oBAAoBa,OAAO,CAAC;AAC9D,UAAM8B,YAAYD,cAAcE,UAAWX,UAASA,KAAKf,OAAOA,EAAE;AAElE,QAAIyB,cAAc,GAAI;AAGhB,UAAAlB,iBAAiBlB,qDAAkBQ,cACvCF,SACAG,WAAWP,KAAMyB,UAASA,KAAKvB,UAAUA;AAG3C+B,kBAAcC,SAAS,IAAI;AAAA,MACzB,GAAGD,cAAcC,SAAS;AAAA,MAC1BxB,UAAUR;AAAAA,MACVS,YACEK,kEAAgBJ,mBAAhBI,mBAAgCH,YAAhCG,mBAA0C,OAA1CA,mBAA8Cd,UAAS;AAAA,MACzDA,OAAO;AAAA,IACT;AAEWd,eAAA;AAAA,MACT,GAAGD;AAAAA,MACHI,qBAAqB;AAAA,QACnB,GAAGJ,QAAQI;AAAAA,QACX,CAACa,OAAO,GAAG6B;AAAAA,MACb;AAAA,IACF,CAAC;AAAA,EACH;AAGA,QAAMG,uBAAuBA,CAC3BhC,SACAK,IACAP,UACG;AACC,QAAA,CAACf,QAAQI,oBAAqB;AAElC,UAAM0C,gBAAgB,CAAC,GAAG9C,QAAQI,oBAAoBa,OAAO,CAAC;AAC9D,UAAM8B,YAAYD,cAAcE,UAAWX,UAASA,KAAKf,OAAOA,EAAE;AAElE,QAAIyB,cAAc,GAAI;AAEtBD,kBAAcC,SAAS,IAAI;AAAA,MACzB,GAAGD,cAAcC,SAAS;AAAA,MAC1BvB,UAAUT;AAAAA,IACZ;AAEWd,eAAA;AAAA,MACT,GAAGD;AAAAA,MACHI,qBAAqB;AAAA,QACnB,GAAGJ,QAAQI;AAAAA,QACX,CAACa,OAAO,GAAG6B;AAAAA,MACb;AAAA,IACF,CAAC;AAAA,EACH;AAGA,QAAMI,oBAAoBA,CACxBjC,SACAK,IACAP,UACG;AACC,QAAA,CAACf,QAAQI,oBAAqB;AAE5B,UAAA0C,gBAAgB,CAAC,GAAI9C,QAAQI,oBAAoBa,OAAO,KAAK,CAAA,CAAG;AACtE,UAAM8B,YAAYD,cAAcE,UAAWX,UAASA,KAAKf,OAAOA,EAAE;AAElE,QAAIyB,cAAc,GAAI;AAEtBD,kBAAcC,SAAS,IAAI;AAAA,MACzB,GAAGD,cAAcC,SAAS;AAAA,MAC1BhC;AAAAA,IACF;AAEWd,eAAA;AAAA,MACT,GAAGD;AAAAA,MACHI,qBAAqB;AAAA,QACnB,GAAGJ,QAAQI;AAAAA,QACX,CAACa,OAAO,GAAG6B;AAAAA,MACb;AAAA,IACF,CAAC;AAAA,EACH;AAGA,QAAMK,mBAAmBA,CACvBlC,SACAK,IACA8B,SACG;AACH,QAAI,CAACpD,QAAQI,uBAAuB,CAACgD,KAAM;AAE3C,UAAMN,gBAAgB,CAAC,GAAG9C,QAAQI,oBAAoBa,OAAO,CAAC;AAC9D,UAAM8B,YAAYD,cAAcE,UAAWX,UAASA,KAAKf,OAAOA,EAAE;AAElE,QAAIyB,cAAc,GAAI;AAEtBD,kBAAcC,SAAS,IAAI;AAAA,MACzB,GAAGD,cAAcC,SAAS;AAAA,MAC1BhC,OAAOsC,OAAOD,MAAM,qBAAqB;AAAA,IAC3C;AAEWnD,eAAA;AAAA,MACT,GAAGD;AAAAA,MACHI,qBAAqB;AAAA,QACnB,GAAGJ,QAAQI;AAAAA,QACX,CAACa,OAAO,GAAG6B;AAAAA,MACb;AAAA,IACF,CAAC;AAAA,EACH;AAGM,QAAAQ,SAAUrC,aAAuB;;AACrC,QAAI,CAACjB,QAAQI,uBAAuB,CAACO,iBAAkB;AAEvD,UAAMS,aAAaT,iBAAiBQ,cAAcF,OAAO,EAAEG;AACvD,QAAAA,WAAWC,WAAW,EAAG;AAGzB,QAAAJ,YAAY,cAAcA,YAAY,YAAY;AACpD,YAAMsC,cAAcvD,QAAQI,oBAAoBa,OAAO,EAAEI;AACzD,YAAMmC,kBAAkBpC,WAAWC;AAEnC,UAAIkC,eAAeC,iBAAiB;AAC1BZ,gBAAAC,KACN,2BAA2B5B,OAAO,4BACpC;AACA;AAAA,MACF;AAGM,YAAAwC,sBAAsBvB,uBAAuBjB,OAAO;AACtD,UAAAwC,oBAAoBpC,WAAW,GAAG;AAC5BuB,gBAAAC,KAAK,oCAAoC5B,OAAO,UAAU;AAClE;AAAA,MACF;AAEM,YAAAyC,cAAcD,oBAAoB,CAAC,EAAE1C;AAC3C,YAAM4C,eACJF,qCAAoB,CAAC,EAAEhC,mBAAvBgC,mBAAuC/B,YAAvC+B,mBAAiD,OAAjDA,mBAAqD1C;AAEvD,YAAM6C,UAAwB;AAAA,QAC5BtC,IAAI1C,WAAW;AAAA,QACf2C,UAAUmC;AAAAA,QACVlC,UAAUmC;AAAAA,QACV5C,OAAO;AAAA,MACT;AAEWd,iBAAA;AAAA,QACT,GAAGD;AAAAA,QACHI,qBAAqB;AAAA,UACnB,GAAGJ,QAAQI;AAAAA,UACX,CAACa,OAAO,GAAG,CAAC,GAAGjB,QAAQI,oBAAoBa,OAAO,GAAG2C,OAAO;AAAA,QAC9D;AAAA,MACF,CAAC;AAAA,IACH,OAAO;AAEL,YAAMA,UAAwB;AAAA,QAC5BtC,IAAI1C,WAAW;AAAA,QACf2C,UAAUH,WAAW,CAAC,EAAEL;AAAAA,QACxBS,WAAUJ,4BAAW,CAAC,EAAEK,mBAAdL,mBAA8BM,YAA9BN,mBAAwC,OAAxCA,mBAA4CL;AAAAA,QACtDA,OAAO;AAAA,MACT;AAEWd,iBAAA;AAAA,QACT,GAAGD;AAAAA,QACHI,qBAAqB;AAAA,UACnB,GAAGJ,QAAQI;AAAAA,UACX,CAACa,OAAO,GAAG,CAAC,GAAGjB,QAAQI,oBAAoBa,OAAO,GAAG2C,OAAO;AAAA,QAC9D;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAGM,QAAAC,YAAYA,CAAC5C,SAAoBK,OAAe;AAChD,QAAA,CAACtB,QAAQI,oBAAqB;AAElC,UAAM0C,gBAAgB9C,QAAQI,oBAAoBa,OAAO,EAAEX,OACxD+B,UAASA,KAAKf,OAAOA,EACxB;AAEI,QAAAwB,cAAczB,WAAW,GAAG;AAE9BiC,aAAOrC,OAAO;AACd;AAAA,IACF;AAEWhB,eAAA;AAAA,MACT,GAAGD;AAAAA,MACHI,qBAAqB;AAAA,QACnB,GAAGJ,QAAQI;AAAAA,QACX,CAACa,OAAO,GAAG6B;AAAAA,MACb;AAAA,IACF,CAAC;AAAA,EACH;AAGM,QAAAgB,kBAAmBvC,cAAqB;AACrC,WAAAwC,kBAAkBxC,QAAQ,KAAK;AAAA,EACxC;AAGA,QAAMyC,sBAAsBA,CAC1B/C,SACAoB,MACAd,aACG;;AACC,QAAA,EAACA,qCAAUE,gBAAuB,QAAA;AAEhC,UAAAwC,YAAY1C,SAASE,eAAeyC;AAE1C,YAAQD,WAAW;AAAA,MACjB,KAAK;AAED,eAAAE,kCAAAA,KAACC,QAAA;AAAA,UACCrD,OAAOsB,KAAKb,YAAY;AAAA,UACxB6C,eAAgBtD,WACdkC,qBAAqBhC,SAASoB,KAAKf,IAAIP,KAAK;AAAA,UAG9CuD,UAAA,CAAAC,kCAAA,IAACC;YAAcC,WAAU;AAAA,YACvBH,gDAACI,aAAY;AAAA,cAAAC,aAAY;AAAA,YAAW,CAAA;AAAA,UACtC,CAAA,GACAJ,kCAAA,IAACK;YAAcH,WAAU;AAAA,YACtBH,yBAAS7C,eAAeC,+BAASU,IAAKyC,kDACpCC,YAA8B;AAAA,cAAA/D,OAAO8D,OAAO9D;AAAAA,cAC1CuD,UAAAO,OAAOE;AAAAA,eADOF,OAAO9D,KAExB;AAAA,UAEJ,CAAA,CAAA;AAAA,QAAA,CACF;AAAA,MAEJ,KAAK;AAED,eAAAwD,kCAAAA,IAACS,OAAA;AAAA,UACClE,MAAK;AAAA,UACLC,OAAOsB,KAAKb,YAAY;AAAA,UACxByD,UAAWC,OACTjC,qBAAqBhC,SAASoB,KAAKf,IAAI4D,EAAEC,OAAOpE,KAAK;AAAA,UAEvD4D,aAAY;AAAA,QAAA,CACd;AAAA,MAEJ,KAAK;AAED,eAAAJ,kCAAAA,IAACS,OAAA;AAAA,UACClE,MAAK;AAAA,UACLC,OAAOsB,KAAKb,YAAY;AAAA,UACxByD,UAAWC,OACTjC,qBAAqBhC,SAASoB,KAAKf,IAAI4D,EAAEC,OAAOpE,KAAK;AAAA,UAEvD4D,aAAY;AAAA,QAAA,CACd;AAAA,MAEJ;AAEI,eAAAJ,kCAAAA,IAACS,OAAA;AAAA,UACClE,MAAK;AAAA,UACLC,OAAOsB,KAAKb,YAAY;AAAA,UACxByD,UAAWC,OACTjC,qBAAqBhC,SAASoB,KAAKf,IAAI4D,EAAEC,OAAOpE,KAAK;AAAA,UAEvD4D,aAAY;AAAA,QAAA,CACd;AAAA,IAEN;AAAA,EACF;AAGA,QAAMS,mBAAmBA,CACvBnE,SACAoB,MACA4B,cACG;;AACH,UAAM1C,WAAWZ,qDAAkBQ,cAAcF,SAASG,WAAWP,KAClEyB,UAASA,KAAKvB,UAAUsB,KAAKd;AAGhC,YAAQ0C,WAAW;AAAA,MACjB,KAAK;AAED,eAAAE,kCAAAA,KAAC,OAAI;AAAA,UAAAM,WAAU;AAAA,UACbH,UAAA,CAAAC,kCAAA,IAACS,OAAA;AAAA,YACClE,MAAK;AAAA,YACLC,OAAOsB,KAAKtB,SAAS;AAAA,YACrBkE,UAAWC,OACThC,kBAAkBjC,SAASoB,KAAKf,IAAI4D,EAAEC,OAAOpE,KAAK;AAAA,YAEpD0D,WAAU;AAAA,UAAA,CACZ,GACCX,gBAAgBzB,KAAKd,QAAQ,KAC5BgD,kCAAAA,IAAC,OAAI;AAAA,YAAAE,WAAU;AAAA,YACZH,UAAAR,gBAAgBzB,KAAKd,QAAQ;AAAA,UAChC,CAAA,CAAA;AAAA,QAEJ,CAAA;AAAA,MAEJ,KAAK;AACH,sDACG8D,SACC;AAAA,UAAAf,UAAA,CAACC,kCAAA,IAAAe,gBAAA;AAAA,YAAeC,SAAO;AAAA,YACrBjB,UAAAH,kCAAA,KAACqB,QAAA;AAAA,cACC1E,MAAK;AAAA,cACL2E,SAAQ;AAAA,cACRhB,WAAU;AAAA,cAETH,UAAA,CAAKjC,KAAAtB,QACFsC,OAAO,IAAIxE,KAAKwD,KAAKtB,KAAK,GAAG,KAAK,IAClC,eACJwD,kCAAAA,IAACmB,UAAa;AAAA,gBAAAjB,WAAU;AAAA,cAAU,CAAA,CAAA;AAAA,YACpC,CAAA;AAAA,UACF,CAAA,GACAF,kCAAA,IAACoB,gBAAe;AAAA,YAAAlB,WAAU;AAAA,YACxBH,UAAAC,kCAAA,IAACqB,YAAA;AAAA,cACCC,MAAK;AAAA,cACLC,UAAUzD,KAAKtB,QAAQ,IAAIlC,KAAKwD,KAAKtB,KAAK,IAAI;AAAA,cAC9CgF,UAAW3C,UAASD,iBAAiBlC,SAASoB,KAAKf,IAAI8B,IAAI;AAAA,cAC3D4C,cAAY;AAAA,YACd,CAAA;AAAA,UACF,CAAA,CAAA;AAAA,QACF,CAAA;AAAA,MAEJ,KAAK;AAED,eAAAzB,kCAAAA,IAACS,OAAA;AAAA,UACClE,MAAK;AAAA,UACLC,OAAOsB,KAAKb,YAAY;AAAA,UACxByD,UAAWC,OACTjC,qBAAqBhC,SAASoB,KAAKf,IAAI4D,EAAEC,OAAOpE,KAAK;AAAA,UAEvD4D,aAAY;AAAA,QAAA,CACd;AAAA,MAEJ,KAAK;AAGD,eAAAJ,kCAAAA,IAACS,OAAA;AAAA,UACClE,MAAK;AAAA,UACLC,OAAOsB,KAAKtB,SAAS;AAAA,UACrBkE,UAAWC,OACThC,kBAAkBjC,SAASoB,KAAKf,IAAI4D,EAAEC,OAAOpE,KAAK;AAAA,UAEpD4D,aAAa,UAAUV,UAAUgC,QAAQ,UAAU,EAAE,CAAC;AAAA,QAAA,CACxD;AAAA,MAEJ,KAAK;AAED,eAAA9B,kCAAAA,KAACC,QAAA;AAAA,UACCrD,OAAOsB,KAAKtB,SAAS;AAAA,UACrBsD,eAAgBtD,WACdmC,kBAAkBjC,SAASoB,KAAKf,IAAIP,KAAK;AAAA,UAG3CuD,UAAA,CAAAC,kCAAA,IAACC;YAAcC,WAAU;AAAA,YACvBH,gDAACI,aAAY;AAAA,cAAAC,aAAY;AAAA,YAAS,CAAA;AAAA,UACpC,CAAA,GACAJ,kCAAA,IAACK;YAAcH,WAAU;AAAA,YACtBH,2DAAU4B,mCAAaxE,+BAASU,IAAKyC,kDACnCC,YAA8B;AAAA,cAAA/D,OAAO8D,OAAO9D;AAAAA,cAC1CuD,UAAAO,OAAOE;AAAAA,eADOF,OAAO9D,KAExB;AAAA,UAEJ,CAAA,CAAA;AAAA,QAAA,CACF;AAAA,MAEJ;AAEI,eAAAwD,kCAAAA,IAACS,OAAA;AAAA,UACClE,MAAK;AAAA,UACLC,OAAOsB,KAAKtB,SAAS;AAAA,UACrBkE,UAAWC,OACThC,kBAAkBjC,SAASoB,KAAKf,IAAI4D,EAAEC,OAAOpE,KAAK;AAAA,QAAA,CAEtD;AAAA,IAEN;AAAA,EACF;AAGM,QAAAoF,oBAAqBlF,aAAuB;AAChD,QAAI,CAACN,oBAAoB,CAACX,QAAQI,oBAA4B,QAAA;AAE9D,UAAMyB,iBACJlB,iBAAiBQ,cAAcF,OAAO,EAAEY;AACtC,QAAA,CAACA,eAAuB,QAAA;AAE5B,UAAMD,cAAcC,eAAevB,OAChC4D,WAAUA,MAAMpD,SAAS,OAC5B;AACI,QAAAc,YAAYP,WAAW,EAAU,QAAA;AAErC,iDACG,OAAI;AAAA,MAAAoD,WAAU;AAAA,MACZH,UAAY1C,YAAAQ,IAAK8B,WAAU;;AAE1B,cAAM7B,QAAOrC,mBAAQI,wBAARJ,mBACXoG,eAAenF,OAAO,OADXjB,mBAEVa,KAAMwB,WAASA,MAAKd,aAAa2C,MAAMnD;AAEtC,YAAA,CAACsB,KAAa,QAAA;AAElB,qDACG,OAAkB;AAAA,UAAAoC,WAAU;AAAA,UAC3BH,UAACH,kCAAA,KAAA,OAAA;AAAA,YAAIM,WAAU;AAAA,YACbH,UAAA,CAACH,kCAAA,KAAA,SAAA;AAAA,cAAMM,WAAU;AAAA,cAAuBH,UAAA,CAAMJ,MAAAa,OAAM,GAAA;AAAA,YAAC,CAAA,yCACpD,OACC;AAAA,cAAAT,UAAAC,kCAAA,IAACS,OAAA;AAAA,gBACClE,MAAK;AAAA,gBACLC,OAAOsB,KAAKtB,SAAS;AAAA,gBACrBkE,UAAWC,OACThC,kBACEkD,eAAenF,OAAO,GACtBoB,KAAKf,IACL4D,EAAEC,OAAOpE,KACX;AAAA,cAEJ,CAAA;AAAA,YACF,CAAA,CAAA;AAAA,UACF,CAAA;AAAA,QAAA,GAhBQsB,KAAKf,EAiBf;AAAA,MAEH,CAAA;AAAA,IACH,CAAA;AAAA,EAEJ;AAGM,QAAA+E,sBAAuBpF,aAAuB;;AAClD,QAAI,CAACN,oBAAoB,CAACX,QAAQI,oBAA4B,QAAA;AAExD,UAAA0C,gBAAgBnC,iBAAiBQ,cAAcF,OAAO;AACtD,UAAAqF,QAAQtG,QAAQI,oBAAoBa,OAAO;AAG3C,UAAAsF,uBAAsBzD,mBAAcjB,mBAAdiB,mBAA8BjC,KACvDkB,YAAWA,OAAOjB,SAAS;AAI9B,UAAM0F,iBACJvF,YAAY,YAAYqF,MAAMjF,SAASyB,cAAc1B,WAAWC;AAGhE,WAAA8C,kCAAAA,KAAC,OAAI;AAAA,MAAAM,WAAU;AAAA,MACbH,UAAA,CAACH,kCAAA,KAAA,KAAA;AAAA,QAAEM,WAAU;AAAA,QACVH,UAAA,CAAcxB,cAAAiC,OAAM,iBAAA;AAAA,MACvB,CAAA,GACCjC,cAAc2D,eACblC,kCAAAA,IAAC;QAAEE,WAAU;AAAA,QACVH,wBAAcmC;AAAAA,MACjB,CAAA,GAGDH,MAAMlE,IAAI,CAACC,MAAMqE,UAAU;;AACpB,cAAAnF,WAAWuB,cAAc1B,WAAWP,KACvCyB,UAASA,KAAKvB,UAAUsB,KAAKd,QAChC;AAEM,cAAAoF,cAAc,CAAC,EAACpF,qCAAUE;AAC1B,cAAAmF,mBAAiBrF,MAAAA,qCAAU2E,gBAAV3E,gBAAAA,IAAuB2C,UAAS;AAIvD,cAAMT,sBACJxC,YAAY,WACR6B,cAAc1B,aACd0B,cAAc1B,WAAWd,OACxBgC,UACC;;AAAAA,sBAAKvB,UAAUsB,KAAKd,YACpB,GAACvB,MAAAA,QAAQI,wBAARJ,gBAAAA,IAA8BiB,SAAS0B,KACrCkE,OAAMA,EAAEvF,OAAOe,KAAKf,MAAMuF,EAAEtF,aAAae,KAAKvB;AAAAA,SAErD;AAGF,eAAAoD,kCAAAA,KAAC,OAAkB;AAAA,UAAAM,WAAU;AAAA,UAC3BH,UAAA,CAAAH,kCAAA,KAAC,OAAA;AAAA,YACCM,WAAW,iBAAiBkC,cAAc,gBAAgB,aACxD;AAAA,YAEFrC,UAAA,CAAAH,kCAAA,KAACC,QAAA;AAAA,cACCrD,OAAOsB,KAAKd;AAAAA,cACZ8C,eAAgBtD,WACdyB,qBAAqBvB,SAASoB,KAAKf,IAAIP,KAAK;AAAA,cAG9CuD,UAAA,CAAAC,kCAAA,IAACC;gBAAcC,WAAU;AAAA,gBACvBH,gDAACI,aAAY;AAAA,kBAAAC,aAAY;AAAA,gBAAW,CAAA;AAAA,cACtC,CAAA,yCACCC,eAAc;AAAA,gBAAAH,WAAU;AAAA,gBACtBH,UAAoBb,oBAAArB,IAAKb,eACxBgD,kCAAAA,IAACO;kBAAgC/D,OAAOQ,UAASR;AAAAA,kBAC9CuD,UAAA/C,UAASwD;AAAAA,mBADKxD,UAASR,KAE1B,CACD;AAAA,cACH,CAAA,CAAA;AAAA,YAAA,CACF,GAEC4F,eAAe3C,oBAAoB/C,SAASoB,MAAMd,QAAQ,GAE1D6D,iBAAiBnE,SAASoB,MAAMuE,cAAc,GAE9CN,MAAMjF,SAAS,KACdkD,kCAAA,IAAC,OAAA;AAAA,cACCE,WAAU;AAAA,cACVqC,SAASA,MAAMjD,UAAU5C,SAASoB,KAAKf,EAAE;AAAA,cAEzCgD,UAAAC,kCAAA,IAACwC,QAAW;AAAA,gBAAAtC,WAAU;AAAA,cAAuB,CAAA;AAAA,YAAA,CAC/C,CAAA;AAAA,UAEJ,CAAA,GACCiC,UAAU,KAAKH,uBACdhC,kCAAAA,IAACyC,qBAAA;AAAA,YACC/F;AAAAA,YACAgG,eAAeV;AAAAA,YACfvG;AAAAA,YACAC;AAAAA,YACAU;AAAAA,UAAA,CACF,CAAA;AAAA,QAAA,GA3CM0B,KAAKf,EA6Cf;AAAA,OAEH,GAEAkF,kBACCrC,kCAAA,KAACqB,QAAA;AAAA,QACC1E,MAAK;AAAA,QACL2D,WAAU;AAAA,QACVgB,SAAQ;AAAA,QACRqB,SAASA,MAAMxD,OAAOrC,OAAO;AAAA,QAE7BqD,UAAA,CAACC,kCAAA,IAAA2C,MAAA;AAAA,UAASzC,WAAU;AAAA,QAAe,CAAA,GAAE,gBAAa,KACjDxD,QAAQkG,YAAY,CAAA;AAAA,MAAA,CACvB,GAIDhB,kBAAkBlF,OAAO,CAAA;AAAA,IAC5B,CAAA;AAAA,EAEJ;AAEA,gDACG,OACC;AAAA,IAAAqD,UAAA,CAAAC,kCAAA,IAAC,OACC;AAAA,MAAAD,UAAAH,kCAAA,KAAC,OAAA;AAAA,QACC,mBAAgB;AAAA,QAChBM,WAAU;AAAA,QAEVH,UAAA,CAAAC,kCAAA,IAAC,OAAA;AAAA,UACCE,WAAU;AAAA,UACVqC,SAASA,MAAMrH,SAAS,wBAAwB;AAAA,UAEhD6E,UAAAC,kCAAA,IAAC,OAAA;AAAA,YACC6C,OAAM;AAAA,YACNC,QAAO;AAAA,YACPC,SAAQ;AAAA,YACRC,MAAK;AAAA,YACLC,OAAM;AAAA,YAENlD,UAAAC,kCAAA,IAAC,QAAA;AAAA,cACCkD,UAAS;AAAA,cACTC,UAAS;AAAA,cACTC,GAAE;AAAA,cACFJ,MAAK;AAAA,YACP,CAAA;AAAA,UACF,CAAA;AAAA,QAAA,CACF,GACChD,kCAAA,IAAA,MAAA;AAAA,UAAGE,WAAU;AAAA,UAA4CH,UAE1D;AAAA,QAAA,CAAA,CAAA;AAAA,MACF,CAAA;AAAA,IACF,CAAA,GAEAC,kCAAA,IAAC;MAAIE,WAAU;AAAA,MACbH,iDAAC/E,QAAQqI,MAAR;AAAA,QAAaC,QAAO;AAAA,QACnBvD,UAAA,CAAAC,kCAAA,IAAC;UAAMzD,MAAK;AAAA,UAASgH,MAAK;AAAA,UAAa/G,OAAM;AAAA,QAAY,CAAA,GACzDwD,kCAAA,IAAC,SAAM;AAAA,UAAAzD,MAAK;AAAA,UAASgH,MAAK;AAAA,UAAU/G,OAAOgH,KAAKC,UAAUhI,OAAO;AAAA,QAAG,CAAA,GACnEmE,kCAAA,KAAA,OAAA;AAAA,UAAI,mBAAgB;AAAA,UAAaM,WAAU;AAAA,UAC1CH,UAAA,CAACH,kCAAA,KAAA,OAAA;AAAA,YAAIM,WAAU;AAAA,YACbH,UAAA,CAACC,kCAAA,IAAA,KAAA;AAAA,cAAEE,WAAU;AAAA,cAAoCH,UAEjD;AAAA,YAAA,CAAA,GACCC,kCAAA,IAAA,KAAA;AAAA,cAAEE,WAAU;AAAA,cAAoCH,UAEjD;AAAA,YAAA,CAAA,GACAH,kCAAA,KAAC,OAAI;AAAA,cAAAM,WAAU;AAAA,cACbH,UAAA,CAACC,kCAAA,IAAA,KAAA;AAAA,gBAAEE,WAAU;AAAA,gBAAoBH,UAAY;AAAA,cAAA,CAAA,GAC7CH,kCAAA,KAACC,QAAA;AAAA,gBACCrD,OAAOf,QAAQG,cAAc;AAAA,gBAC7BkE,eAAgBtD,WAAU;AACbd,6BAAA;AAAA,oBACT,GAAGD;AAAAA,oBACHG,YAAYY;AAAAA,kBACd,CAAC;AAAA,gBACH;AAAA,gBAEAuD,UAAA,CAAAC,kCAAA,IAACC;kBAAcC,WAAU;AAAA,kBACvBH,gDAACI,aAAY;AAAA,oBAAAC,aAAY;AAAA,kBAAS,CAAA;AAAA,gBACpC,CAAA,yCACCC,eAAc;AAAA,kBAAAH,WAAU;AAAA,kBACtBH,UAAkBjF,kBAAA4I,YAAY7F,IAAKtB,gDACjCgE,YAA4B;AAAA,oBAAA/D,OAAOD,KAAKC;AAAAA,oBACtCuD,UAAAxD,KAAKiE;AAAAA,qBADSjE,KAAKC,KAEtB,CACD;AAAA,gBACH,CAAA,CAAA;AAAA,cAAA,CACF,CAAA;AAAA,YACF,CAAA,GACAwD,kCAAA,IAACS,OAAA;AAAA,cACClE,MAAK;AAAA,cACL6D,aAAY;AAAA,cACZF,WAAU;AAAA,cACV1D,OAAOf,QAAQkI,cAAc;AAAA,cAC7BjD,UAAWC,OAAM;AACJjF,2BAAA;AAAA,kBACT,GAAGD;AAAAA,kBACHkI,YAAYhD,EAAEC,OAAOpE;AAAAA,gBACvB,CAAC;AAAA,cACH;AAAA,YAAA,CACF,GACAwD,kCAAA,IAACS,OAAA;AAAA,cACClE,MAAK;AAAA,cACL6D,aAAY;AAAA,cACZF,WAAU;AAAA,cACV1D,OAAOf,QAAQyG,eAAe;AAAA,cAC9BxB,UAAWC,OAAM;AACJjF,2BAAA;AAAA,kBACT,GAAGD;AAAAA,kBACHyG,aAAavB,EAAEC,OAAOpE;AAAAA,gBACxB,CAAC;AAAA,cACH;AAAA,YAAA,CACF,CAAA;AAAA,WACF,GAECJ,oBAEIwD,kCAAA,KAAAgE,4BAAA;AAAA,YAAA7D,UAAA,CAAA+B,oBAAoB,UAAU,GAC9BA,oBAAoB,QAAQ,GAC5BA,oBAAoB,UAAU,CAAA;AAAA,UACjC,CAAA,CAAA;AAAA,QAEJ,CAAA,GAEA9B,kCAAA,IAAC,OAAI;AAAA,UAAAE,WAAU;AAAA,UACbH,UAAAC,kCAAA,IAACiB,QAAA;AAAA,YACC1E,MAAK;AAAA,YACLsH,UAAU7I,QAAQO,UAAU,gBAAgBP,QAAQO,UAAU;AAAA,YAC9D2E,WAAU;AAAA,YACXH,UAAA;AAAA,UAED,CAAA;AAAA,QACF,CAAA,CAAA;AAAA,MACF,CAAA;AAAA,IACF,CAAA,CAAA;AAAA,EACF,CAAA;AAEJ;AAgBA,MAAM0C,sBAA0DA,CAAC;AAAA,EAC/D/F;AAAAA,EACAgG;AAAAA,EACAjH;AAAAA,EACAC;AAAAA,EACAU;AACF,MAAM;;AACE,QAAA0H,eACJpH,YAAY,cACZgG,cAAcnG,SAAS,sBACvBd,mBAAQI,wBAARJ,mBAA6BK,aAA7BL,mBAAuCqB,YAAW;AAE9C,QAAAiH,aAAaC,aAAAA,QAAQ,MAAM;;AAE7B,aAAAvI,OAAAA,MAAAA,QAAQI,wBAARJ,gBAAAA,IAA6BQ,uBAA7BR,gBAAAA,IAAiDa,KAC9CwB,UAASA,KAAKd,aAAa0F,cAAclG,WACvC;AAAA,MACHO,IAAI1C,WAAW;AAAA,MACf2C,UAAU0F,cAAclG;AAAAA,MACxBA,SAAOkG,OAAAA,MAAAA,cAAcvF,YAAduF,gBAAAA,IAAwB,OAAxBA,gBAAAA,IAA4BlG,UAAS;AAAA,IAC9C;AAAA,EAEJ,GAAG,CAACf,SAASiH,aAAa,CAAC;AAErB,QAAA/D,oBAAqBsF,cAAqB;;AAC9C,UAAMC,oBAAoB,CACxB,MAAIzI,MAAAA,QAAQI,wBAARJ,gBAAAA,IAA6BQ,uBAAsB,CAAA,GAAIF,OACxD+B,UAASA,KAAKd,aAAa0F,cAAclG,KAC5C,GACA;AAAA,MACEO,IAAIgH,WAAWhH;AAAAA,MACfC,UAAU0F,cAAclG;AAAAA,MACxBA,OAAOyH;AAAAA,IACT,CAAA;AAGSvI,eAAA;AAAA,MACT,GAAGD;AAAAA,MACHI,qBAAqB;AAAA,QACnBC,YAAUL,MAAAA,QAAQI,wBAARJ,gBAAAA,IAA6BK,aAAY,CAAC;AAAA,QACpDC,UAAQN,MAAAA,QAAQI,wBAARJ,gBAAAA,IAA6BM,WAAU,CAAC;AAAA,QAChDC,YAAUP,MAAAA,QAAQI,wBAARJ,gBAAAA,IAA6BO,aAAY,CAAC;AAAA,QACpDC,oBAAoBiI;AAAAA,MACtB;AAAA,IACF,CAAC;AAAA,EACH;AAGA5I,eAAAA,UAAU,MAAM;;AACV,QAAA,CAACG,QAAQI,oBAAqB;AAE5B,UAAAsI,gBAAgB1I,QAAQI,oBAAoBC;AAClD,UAAMsI,wBACJhI,MAAAA,qDAAkBQ,cAAcd,SAASwB,mBAAzClB,gBAAAA,IAAyDE,KACtDkB,YAAWA,OAAOjB,SAAS;AAEhC,QAAI,CAAC6H,qBAAsB;AAE3B,UAAMC,oBAAoB,CACxB,GAAI5I,QAAQI,oBAAoBI,sBAAsB,CAAA,CACxD;AACA,UAAMqI,uBAAuBD,kBAAkB5F,UAC5CX,UAASA,KAAKd,aAAaoH,qBAAqB5H,KACnD;AAGA,QAAI2H,cAAcrH,WAAW,KAAKwH,yBAAyB,IAAI;AAC7DD,wBAAkB5G,KAAK;AAAA,QACrBV,IAAI1C,WAAW;AAAA,QACf2C,UAAUoH,qBAAqB5H;AAAAA,QAC/BA,SAAO4H,OAAAA,MAAAA,qBAAqBjH,YAArBiH,gBAAAA,IAA+B,OAA/BA,gBAAAA,IAAmC5H,UAAS;AAAA,MACrD,CAAC;AACDd,iBAAYgC,UAAU;;AAAA;AAAA,UACpB,GAAGA;AAAAA,UACH7B,qBAAqB;AAAA,YACnB,GAAG6B,KAAK7B;AAAAA,YACRC,YAAU4B,MAAAA,KAAK7B,wBAAL6B,gBAAAA,IAA0B5B,aAAY,CAAC;AAAA,YACjDC,UAAQ2B,MAAAA,KAAK7B,wBAAL6B,gBAAAA,IAA0B3B,WAAU,CAAC;AAAA,YAC7CC,YAAU0B,MAAAA,KAAK7B,wBAAL6B,gBAAAA,IAA0B1B,aAAY,CAAC;AAAA,YACjDC,oBAAoBoI;AAAAA,UACtB;AAAA,QACF;AAAA,OAAE;AAAA,IACJ;AAGA,QAAIF,cAAcrH,WAAW,KAAKwH,yBAAyB,IAAI;AAC3CD,wBAAAE,OAAOD,sBAAsB,CAAC;AAChD5I,iBAAYgC,UAAU;;AAAA;AAAA,UACpB,GAAGA;AAAAA,UACH7B,qBAAqB;AAAA,YACnB,GAAG6B,KAAK7B;AAAAA,YACRC,YAAU4B,MAAAA,KAAK7B,wBAAL6B,gBAAAA,IAA0B5B,aAAY,CAAC;AAAA,YACjDC,UAAQ2B,MAAAA,KAAK7B,wBAAL6B,gBAAAA,IAA0B3B,WAAU,CAAC;AAAA,YAC7CC,YAAU0B,MAAAA,KAAK7B,wBAAL6B,gBAAAA,IAA0B1B,aAAY,CAAC;AAAA,YACjDC,oBAAoBoI;AAAAA,UACtB;AAAA,QACF;AAAA,OAAE;AAAA,IACJ;AAAA,KACC,EAAC5I,mBAAQI,wBAARJ,mBAA6BK,aAA7BL,mBAAuCqB,MAAM,CAAC;AAE9C,MAAA,CAACgH,aAAqB,QAAA;AAE1B,+CACG,OAAI;AAAA,IAAA5D,WAAU;AAAA,IACbH,UAAAC,kCAAA,IAACwE;MAAKhI,OAAOuH,WAAWvH;AAAAA,MAAOsD,eAAenB;AAAAA,MAC5CoB,UAACC,kCAAA,IAAAyE,UAAA;AAAA,QACE1E,8BAAc5C,+BAASU,IAAKyC,YAC3BN,kCAAA,IAAC0E,aAAA;AAAA,UAEClI,OAAO8D,OAAO9D;AAAAA,UACd0D,WAAU;AAAA,UAETH,UAAOO,OAAAE;AAAAA,QAAA,GAJHF,OAAO9D,KAKd;AAAA,MAEJ,CAAA;AAAA,IACF,CAAA;AAAA,EACF,CAAA;AAEJ;"}