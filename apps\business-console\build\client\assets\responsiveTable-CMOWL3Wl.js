import { j as jsxRuntimeExports } from "./jsx-runtime-bB2y7OuD.js";
const ResponsiveTable = ({
  headers,
  data,
  renderRow,
  footerTotals,
  emptyMessage = "No results found."
}) => {
  return /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "rounded-md border overflow-x-auto", children: /* @__PURE__ */ jsxRuntimeExports.jsx(
    "div",
    {
      className: "overflow-y-auto",
      style: { maxHeight: "400px" },
      children: /* @__PURE__ */ jsxRuntimeExports.jsxs("table", { className: "w-full min-w-[600px] border-collapse", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx("thead", { className: "sticky top-0 z-10", children: /* @__PURE__ */ jsxRuntimeExports.jsx("tr", { className: "bg-gradient-to-r from-blue-50 to-indigo-50", children: headers.map((header, index) => /* @__PURE__ */ jsxRuntimeExports.jsx(
          "th",
          {
            className: "py-2 px-7 text-center font-semibold whitespace-normal break-words",
            children: header
          },
          index
        )) }) }),
        /* @__PURE__ */ jsxRuntimeExports.jsx("tbody", { className: " divide-gray-200", children: Array.isArray(data) && data.length > 0 ? data.map(
          (item, index) => (
            // Use a debugging step if needed to check what `item` looks like
            renderRow(item)
          )
        ) : /* @__PURE__ */ jsxRuntimeExports.jsx("tr", { children: /* @__PURE__ */ jsxRuntimeExports.jsx(
          "td",
          {
            colSpan: headers.length,
            className: "py-2 px-3 text-center whitespace-normal break-words",
            children: emptyMessage
          }
        ) }) }),
        footerTotals && /* @__PURE__ */ jsxRuntimeExports.jsx("tfoot", { children: /* @__PURE__ */ jsxRuntimeExports.jsx("tr", { className: "bg-gray-50 font-medium", children: footerTotals.map((total, index) => /* @__PURE__ */ jsxRuntimeExports.jsx(
          "td",
          {
            className: `py-2 px-3 ${index === 0 ? "text-left" : "text-right"}`,
            children: total
          },
          index
        )) }) })
      ] })
    }
  ) });
};
export {
  ResponsiveTable as R
};
//# sourceMappingURL=responsiveTable-CMOWL3Wl.js.map
