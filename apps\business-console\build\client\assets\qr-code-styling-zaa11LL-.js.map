{"version": 3, "file": "qr-code-styling-zaa11LL-.js", "sources": ["../../../node_modules/qr-code-styling/lib/qr-code-styling.js"], "sourcesContent": ["!function(t,e){\"object\"==typeof exports&&\"object\"==typeof module?module.exports=e():\"function\"==typeof define&&define.amd?define([],e):\"object\"==typeof exports?exports.QRCodeStyling=e():t.QRCodeStyling=e()}(this,(()=>(()=>{var t={873:(t,e)=>{var i,r,n=function(){var t=function(t,e){var i=t,r=s[e],n=null,o=0,h=null,p=[],v={},m=function(t,e){n=function(t){for(var e=new Array(t),i=0;i<t;i+=1){e[i]=new Array(t);for(var r=0;r<t;r+=1)e[i][r]=null}return e}(o=4*i+17),b(0,0),b(o-7,0),b(0,o-7),x(),y(),C(t,e),i>=7&&S(t),null==h&&(h=M(i,r,p)),A(h,e)},b=function(t,e){for(var i=-1;i<=7;i+=1)if(!(t+i<=-1||o<=t+i))for(var r=-1;r<=7;r+=1)e+r<=-1||o<=e+r||(n[t+i][e+r]=0<=i&&i<=6&&(0==r||6==r)||0<=r&&r<=6&&(0==i||6==i)||2<=i&&i<=4&&2<=r&&r<=4)},y=function(){for(var t=8;t<o-8;t+=1)null==n[t][6]&&(n[t][6]=t%2==0);for(var e=8;e<o-8;e+=1)null==n[6][e]&&(n[6][e]=e%2==0)},x=function(){for(var t=a.getPatternPosition(i),e=0;e<t.length;e+=1)for(var r=0;r<t.length;r+=1){var o=t[e],s=t[r];if(null==n[o][s])for(var h=-2;h<=2;h+=1)for(var d=-2;d<=2;d+=1)n[o+h][s+d]=-2==h||2==h||-2==d||2==d||0==h&&0==d}},S=function(t){for(var e=a.getBCHTypeNumber(i),r=0;r<18;r+=1){var s=!t&&1==(e>>r&1);n[Math.floor(r/3)][r%3+o-8-3]=s}for(r=0;r<18;r+=1)s=!t&&1==(e>>r&1),n[r%3+o-8-3][Math.floor(r/3)]=s},C=function(t,e){for(var i=r<<3|e,s=a.getBCHTypeInfo(i),h=0;h<15;h+=1){var d=!t&&1==(s>>h&1);h<6?n[h][8]=d:h<8?n[h+1][8]=d:n[o-15+h][8]=d}for(h=0;h<15;h+=1)d=!t&&1==(s>>h&1),h<8?n[8][o-h-1]=d:h<9?n[8][15-h-1+1]=d:n[8][15-h-1]=d;n[o-8][8]=!t},A=function(t,e){for(var i=-1,r=o-1,s=7,h=0,d=a.getMaskFunction(e),u=o-1;u>0;u-=2)for(6==u&&(u-=1);;){for(var c=0;c<2;c+=1)if(null==n[r][u-c]){var l=!1;h<t.length&&(l=1==(t[h]>>>s&1)),d(r,u-c)&&(l=!l),n[r][u-c]=l,-1==(s-=1)&&(h+=1,s=7)}if((r+=i)<0||o<=r){r-=i,i=-i;break}}},M=function(t,e,i){for(var r=u.getRSBlocks(t,e),n=c(),o=0;o<i.length;o+=1){var s=i[o];n.put(s.getMode(),4),n.put(s.getLength(),a.getLengthInBits(s.getMode(),t)),s.write(n)}var h=0;for(o=0;o<r.length;o+=1)h+=r[o].dataCount;if(n.getLengthInBits()>8*h)throw\"code length overflow. (\"+n.getLengthInBits()+\">\"+8*h+\")\";for(n.getLengthInBits()+4<=8*h&&n.put(0,4);n.getLengthInBits()%8!=0;)n.putBit(!1);for(;!(n.getLengthInBits()>=8*h||(n.put(236,8),n.getLengthInBits()>=8*h));)n.put(17,8);return function(t,e){for(var i=0,r=0,n=0,o=new Array(e.length),s=new Array(e.length),h=0;h<e.length;h+=1){var u=e[h].dataCount,c=e[h].totalCount-u;r=Math.max(r,u),n=Math.max(n,c),o[h]=new Array(u);for(var l=0;l<o[h].length;l+=1)o[h][l]=255&t.getBuffer()[l+i];i+=u;var g=a.getErrorCorrectPolynomial(c),f=d(o[h],g.getLength()-1).mod(g);for(s[h]=new Array(g.getLength()-1),l=0;l<s[h].length;l+=1){var w=l+f.getLength()-s[h].length;s[h][l]=w>=0?f.getAt(w):0}}var p=0;for(l=0;l<e.length;l+=1)p+=e[l].totalCount;var v=new Array(p),_=0;for(l=0;l<r;l+=1)for(h=0;h<e.length;h+=1)l<o[h].length&&(v[_]=o[h][l],_+=1);for(l=0;l<n;l+=1)for(h=0;h<e.length;h+=1)l<s[h].length&&(v[_]=s[h][l],_+=1);return v}(n,r)};v.addData=function(t,e){var i=null;switch(e=e||\"Byte\"){case\"Numeric\":i=l(t);break;case\"Alphanumeric\":i=g(t);break;case\"Byte\":i=f(t);break;case\"Kanji\":i=w(t);break;default:throw\"mode:\"+e}p.push(i),h=null},v.isDark=function(t,e){if(t<0||o<=t||e<0||o<=e)throw t+\",\"+e;return n[t][e]},v.getModuleCount=function(){return o},v.make=function(){if(i<1){for(var t=1;t<40;t++){for(var e=u.getRSBlocks(t,r),n=c(),o=0;o<p.length;o++){var s=p[o];n.put(s.getMode(),4),n.put(s.getLength(),a.getLengthInBits(s.getMode(),t)),s.write(n)}var h=0;for(o=0;o<e.length;o++)h+=e[o].dataCount;if(n.getLengthInBits()<=8*h)break}i=t}m(!1,function(){for(var t=0,e=0,i=0;i<8;i+=1){m(!0,i);var r=a.getLostPoint(v);(0==i||t>r)&&(t=r,e=i)}return e}())},v.createTableTag=function(t,e){t=t||2;var i=\"\";i+='<table style=\"',i+=\" border-width: 0px; border-style: none;\",i+=\" border-collapse: collapse;\",i+=\" padding: 0px; margin: \"+(e=void 0===e?4*t:e)+\"px;\",i+='\">',i+=\"<tbody>\";for(var r=0;r<v.getModuleCount();r+=1){i+=\"<tr>\";for(var n=0;n<v.getModuleCount();n+=1)i+='<td style=\"',i+=\" border-width: 0px; border-style: none;\",i+=\" border-collapse: collapse;\",i+=\" padding: 0px; margin: 0px;\",i+=\" width: \"+t+\"px;\",i+=\" height: \"+t+\"px;\",i+=\" background-color: \",i+=v.isDark(r,n)?\"#000000\":\"#ffffff\",i+=\";\",i+='\"/>';i+=\"</tr>\"}return(i+=\"</tbody>\")+\"</table>\"},v.createSvgTag=function(t,e,i,r){var n={};\"object\"==typeof arguments[0]&&(t=(n=arguments[0]).cellSize,e=n.margin,i=n.alt,r=n.title),t=t||2,e=void 0===e?4*t:e,(i=\"string\"==typeof i?{text:i}:i||{}).text=i.text||null,i.id=i.text?i.id||\"qrcode-description\":null,(r=\"string\"==typeof r?{text:r}:r||{}).text=r.text||null,r.id=r.text?r.id||\"qrcode-title\":null;var o,s,a,h,d=v.getModuleCount()*t+2*e,u=\"\";for(h=\"l\"+t+\",0 0,\"+t+\" -\"+t+\",0 0,-\"+t+\"z \",u+='<svg version=\"1.1\" xmlns=\"http://www.w3.org/2000/svg\"',u+=n.scalable?\"\":' width=\"'+d+'px\" height=\"'+d+'px\"',u+=' viewBox=\"0 0 '+d+\" \"+d+'\" ',u+=' preserveAspectRatio=\"xMinYMin meet\"',u+=r.text||i.text?' role=\"img\" aria-labelledby=\"'+$([r.id,i.id].join(\" \").trim())+'\"':\"\",u+=\">\",u+=r.text?'<title id=\"'+$(r.id)+'\">'+$(r.text)+\"</title>\":\"\",u+=i.text?'<description id=\"'+$(i.id)+'\">'+$(i.text)+\"</description>\":\"\",u+='<rect width=\"100%\" height=\"100%\" fill=\"white\" cx=\"0\" cy=\"0\"/>',u+='<path d=\"',s=0;s<v.getModuleCount();s+=1)for(a=s*t+e,o=0;o<v.getModuleCount();o+=1)v.isDark(s,o)&&(u+=\"M\"+(o*t+e)+\",\"+a+h);return(u+='\" stroke=\"transparent\" fill=\"black\"/>')+\"</svg>\"},v.createDataURL=function(t,e){t=t||2,e=void 0===e?4*t:e;var i=v.getModuleCount()*t+2*e,r=e,n=i-e;return _(i,i,(function(e,i){if(r<=e&&e<n&&r<=i&&i<n){var o=Math.floor((e-r)/t),s=Math.floor((i-r)/t);return v.isDark(s,o)?0:1}return 1}))},v.createImgTag=function(t,e,i){t=t||2,e=void 0===e?4*t:e;var r=v.getModuleCount()*t+2*e,n=\"\";return n+=\"<img\",n+=' src=\"',n+=v.createDataURL(t,e),n+='\"',n+=' width=\"',n+=r,n+='\"',n+=' height=\"',n+=r,n+='\"',i&&(n+=' alt=\"',n+=$(i),n+='\"'),n+\"/>\"};var $=function(t){for(var e=\"\",i=0;i<t.length;i+=1){var r=t.charAt(i);switch(r){case\"<\":e+=\"&lt;\";break;case\">\":e+=\"&gt;\";break;case\"&\":e+=\"&amp;\";break;case'\"':e+=\"&quot;\";break;default:e+=r}}return e};return v.createASCII=function(t,e){if((t=t||1)<2)return function(t){t=void 0===t?2:t;var e,i,r,n,o,s=1*v.getModuleCount()+2*t,a=t,h=s-t,d={\"██\":\"█\",\"█ \":\"▀\",\" █\":\"▄\",\"  \":\" \"},u={\"██\":\"▀\",\"█ \":\"▀\",\" █\":\" \",\"  \":\" \"},c=\"\";for(e=0;e<s;e+=2){for(r=Math.floor((e-a)/1),n=Math.floor((e+1-a)/1),i=0;i<s;i+=1)o=\"█\",a<=i&&i<h&&a<=e&&e<h&&v.isDark(r,Math.floor((i-a)/1))&&(o=\" \"),a<=i&&i<h&&a<=e+1&&e+1<h&&v.isDark(n,Math.floor((i-a)/1))?o+=\" \":o+=\"█\",c+=t<1&&e+1>=h?u[o]:d[o];c+=\"\\n\"}return s%2&&t>0?c.substring(0,c.length-s-1)+Array(s+1).join(\"▀\"):c.substring(0,c.length-1)}(e);t-=1,e=void 0===e?2*t:e;var i,r,n,o,s=v.getModuleCount()*t+2*e,a=e,h=s-e,d=Array(t+1).join(\"██\"),u=Array(t+1).join(\"  \"),c=\"\",l=\"\";for(i=0;i<s;i+=1){for(n=Math.floor((i-a)/t),l=\"\",r=0;r<s;r+=1)o=1,a<=r&&r<h&&a<=i&&i<h&&v.isDark(n,Math.floor((r-a)/t))&&(o=0),l+=o?d:u;for(n=0;n<t;n+=1)c+=l+\"\\n\"}return c.substring(0,c.length-1)},v.renderTo2dContext=function(t,e){e=e||2;for(var i=v.getModuleCount(),r=0;r<i;r++)for(var n=0;n<i;n++)t.fillStyle=v.isDark(r,n)?\"black\":\"white\",t.fillRect(r*e,n*e,e,e)},v};t.stringToBytes=(t.stringToBytesFuncs={default:function(t){for(var e=[],i=0;i<t.length;i+=1){var r=t.charCodeAt(i);e.push(255&r)}return e}}).default,t.createStringToBytes=function(t,e){var i=function(){for(var i=v(t),r=function(){var t=i.read();if(-1==t)throw\"eof\";return t},n=0,o={};;){var s=i.read();if(-1==s)break;var a=r(),h=r()<<8|r();o[String.fromCharCode(s<<8|a)]=h,n+=1}if(n!=e)throw n+\" != \"+e;return o}(),r=\"?\".charCodeAt(0);return function(t){for(var e=[],n=0;n<t.length;n+=1){var o=t.charCodeAt(n);if(o<128)e.push(o);else{var s=i[t.charAt(n)];\"number\"==typeof s?(255&s)==s?e.push(s):(e.push(s>>>8),e.push(255&s)):e.push(r)}}return e}};var e,i,r,n,o,s={L:1,M:0,Q:3,H:2},a=(e=[[],[6,18],[6,22],[6,26],[6,30],[6,34],[6,22,38],[6,24,42],[6,26,46],[6,28,50],[6,30,54],[6,32,58],[6,34,62],[6,26,46,66],[6,26,48,70],[6,26,50,74],[6,30,54,78],[6,30,56,82],[6,30,58,86],[6,34,62,90],[6,28,50,72,94],[6,26,50,74,98],[6,30,54,78,102],[6,28,54,80,106],[6,32,58,84,110],[6,30,58,86,114],[6,34,62,90,118],[6,26,50,74,98,122],[6,30,54,78,102,126],[6,26,52,78,104,130],[6,30,56,82,108,134],[6,34,60,86,112,138],[6,30,58,86,114,142],[6,34,62,90,118,146],[6,30,54,78,102,126,150],[6,24,50,76,102,128,154],[6,28,54,80,106,132,158],[6,32,58,84,110,136,162],[6,26,54,82,110,138,166],[6,30,58,86,114,142,170]],i=1335,r=7973,o=function(t){for(var e=0;0!=t;)e+=1,t>>>=1;return e},(n={}).getBCHTypeInfo=function(t){for(var e=t<<10;o(e)-o(i)>=0;)e^=i<<o(e)-o(i);return 21522^(t<<10|e)},n.getBCHTypeNumber=function(t){for(var e=t<<12;o(e)-o(r)>=0;)e^=r<<o(e)-o(r);return t<<12|e},n.getPatternPosition=function(t){return e[t-1]},n.getMaskFunction=function(t){switch(t){case 0:return function(t,e){return(t+e)%2==0};case 1:return function(t,e){return t%2==0};case 2:return function(t,e){return e%3==0};case 3:return function(t,e){return(t+e)%3==0};case 4:return function(t,e){return(Math.floor(t/2)+Math.floor(e/3))%2==0};case 5:return function(t,e){return t*e%2+t*e%3==0};case 6:return function(t,e){return(t*e%2+t*e%3)%2==0};case 7:return function(t,e){return(t*e%3+(t+e)%2)%2==0};default:throw\"bad maskPattern:\"+t}},n.getErrorCorrectPolynomial=function(t){for(var e=d([1],0),i=0;i<t;i+=1)e=e.multiply(d([1,h.gexp(i)],0));return e},n.getLengthInBits=function(t,e){if(1<=e&&e<10)switch(t){case 1:return 10;case 2:return 9;case 4:case 8:return 8;default:throw\"mode:\"+t}else if(e<27)switch(t){case 1:return 12;case 2:return 11;case 4:return 16;case 8:return 10;default:throw\"mode:\"+t}else{if(!(e<41))throw\"type:\"+e;switch(t){case 1:return 14;case 2:return 13;case 4:return 16;case 8:return 12;default:throw\"mode:\"+t}}},n.getLostPoint=function(t){for(var e=t.getModuleCount(),i=0,r=0;r<e;r+=1)for(var n=0;n<e;n+=1){for(var o=0,s=t.isDark(r,n),a=-1;a<=1;a+=1)if(!(r+a<0||e<=r+a))for(var h=-1;h<=1;h+=1)n+h<0||e<=n+h||0==a&&0==h||s==t.isDark(r+a,n+h)&&(o+=1);o>5&&(i+=3+o-5)}for(r=0;r<e-1;r+=1)for(n=0;n<e-1;n+=1){var d=0;t.isDark(r,n)&&(d+=1),t.isDark(r+1,n)&&(d+=1),t.isDark(r,n+1)&&(d+=1),t.isDark(r+1,n+1)&&(d+=1),0!=d&&4!=d||(i+=3)}for(r=0;r<e;r+=1)for(n=0;n<e-6;n+=1)t.isDark(r,n)&&!t.isDark(r,n+1)&&t.isDark(r,n+2)&&t.isDark(r,n+3)&&t.isDark(r,n+4)&&!t.isDark(r,n+5)&&t.isDark(r,n+6)&&(i+=40);for(n=0;n<e;n+=1)for(r=0;r<e-6;r+=1)t.isDark(r,n)&&!t.isDark(r+1,n)&&t.isDark(r+2,n)&&t.isDark(r+3,n)&&t.isDark(r+4,n)&&!t.isDark(r+5,n)&&t.isDark(r+6,n)&&(i+=40);var u=0;for(n=0;n<e;n+=1)for(r=0;r<e;r+=1)t.isDark(r,n)&&(u+=1);return i+Math.abs(100*u/e/e-50)/5*10},n),h=function(){for(var t=new Array(256),e=new Array(256),i=0;i<8;i+=1)t[i]=1<<i;for(i=8;i<256;i+=1)t[i]=t[i-4]^t[i-5]^t[i-6]^t[i-8];for(i=0;i<255;i+=1)e[t[i]]=i;return{glog:function(t){if(t<1)throw\"glog(\"+t+\")\";return e[t]},gexp:function(e){for(;e<0;)e+=255;for(;e>=256;)e-=255;return t[e]}}}();function d(t,e){if(void 0===t.length)throw t.length+\"/\"+e;var i=function(){for(var i=0;i<t.length&&0==t[i];)i+=1;for(var r=new Array(t.length-i+e),n=0;n<t.length-i;n+=1)r[n]=t[n+i];return r}(),r={getAt:function(t){return i[t]},getLength:function(){return i.length},multiply:function(t){for(var e=new Array(r.getLength()+t.getLength()-1),i=0;i<r.getLength();i+=1)for(var n=0;n<t.getLength();n+=1)e[i+n]^=h.gexp(h.glog(r.getAt(i))+h.glog(t.getAt(n)));return d(e,0)},mod:function(t){if(r.getLength()-t.getLength()<0)return r;for(var e=h.glog(r.getAt(0))-h.glog(t.getAt(0)),i=new Array(r.getLength()),n=0;n<r.getLength();n+=1)i[n]=r.getAt(n);for(n=0;n<t.getLength();n+=1)i[n]^=h.gexp(h.glog(t.getAt(n))+e);return d(i,0).mod(t)}};return r}var u=function(){var t=[[1,26,19],[1,26,16],[1,26,13],[1,26,9],[1,44,34],[1,44,28],[1,44,22],[1,44,16],[1,70,55],[1,70,44],[2,35,17],[2,35,13],[1,100,80],[2,50,32],[2,50,24],[4,25,9],[1,134,108],[2,67,43],[2,33,15,2,34,16],[2,33,11,2,34,12],[2,86,68],[4,43,27],[4,43,19],[4,43,15],[2,98,78],[4,49,31],[2,32,14,4,33,15],[4,39,13,1,40,14],[2,121,97],[2,60,38,2,61,39],[4,40,18,2,41,19],[4,40,14,2,41,15],[2,146,116],[3,58,36,2,59,37],[4,36,16,4,37,17],[4,36,12,4,37,13],[2,86,68,2,87,69],[4,69,43,1,70,44],[6,43,19,2,44,20],[6,43,15,2,44,16],[4,101,81],[1,80,50,4,81,51],[4,50,22,4,51,23],[3,36,12,8,37,13],[2,116,92,2,117,93],[6,58,36,2,59,37],[4,46,20,6,47,21],[7,42,14,4,43,15],[4,133,107],[8,59,37,1,60,38],[8,44,20,4,45,21],[12,33,11,4,34,12],[3,145,115,1,146,116],[4,64,40,5,65,41],[11,36,16,5,37,17],[11,36,12,5,37,13],[5,109,87,1,110,88],[5,65,41,5,66,42],[5,54,24,7,55,25],[11,36,12,7,37,13],[5,122,98,1,123,99],[7,73,45,3,74,46],[15,43,19,2,44,20],[3,45,15,13,46,16],[1,135,107,5,136,108],[10,74,46,1,75,47],[1,50,22,15,51,23],[2,42,14,17,43,15],[5,150,120,1,151,121],[9,69,43,4,70,44],[17,50,22,1,51,23],[2,42,14,19,43,15],[3,141,113,4,142,114],[3,70,44,11,71,45],[17,47,21,4,48,22],[9,39,13,16,40,14],[3,135,107,5,136,108],[3,67,41,13,68,42],[15,54,24,5,55,25],[15,43,15,10,44,16],[4,144,116,4,145,117],[17,68,42],[17,50,22,6,51,23],[19,46,16,6,47,17],[2,139,111,7,140,112],[17,74,46],[7,54,24,16,55,25],[34,37,13],[4,151,121,5,152,122],[4,75,47,14,76,48],[11,54,24,14,55,25],[16,45,15,14,46,16],[6,147,117,4,148,118],[6,73,45,14,74,46],[11,54,24,16,55,25],[30,46,16,2,47,17],[8,132,106,4,133,107],[8,75,47,13,76,48],[7,54,24,22,55,25],[22,45,15,13,46,16],[10,142,114,2,143,115],[19,74,46,4,75,47],[28,50,22,6,51,23],[33,46,16,4,47,17],[8,152,122,4,153,123],[22,73,45,3,74,46],[8,53,23,26,54,24],[12,45,15,28,46,16],[3,147,117,10,148,118],[3,73,45,23,74,46],[4,54,24,31,55,25],[11,45,15,31,46,16],[7,146,116,7,147,117],[21,73,45,7,74,46],[1,53,23,37,54,24],[19,45,15,26,46,16],[5,145,115,10,146,116],[19,75,47,10,76,48],[15,54,24,25,55,25],[23,45,15,25,46,16],[13,145,115,3,146,116],[2,74,46,29,75,47],[42,54,24,1,55,25],[23,45,15,28,46,16],[17,145,115],[10,74,46,23,75,47],[10,54,24,35,55,25],[19,45,15,35,46,16],[17,145,115,1,146,116],[14,74,46,21,75,47],[29,54,24,19,55,25],[11,45,15,46,46,16],[13,145,115,6,146,116],[14,74,46,23,75,47],[44,54,24,7,55,25],[59,46,16,1,47,17],[12,151,121,7,152,122],[12,75,47,26,76,48],[39,54,24,14,55,25],[22,45,15,41,46,16],[6,151,121,14,152,122],[6,75,47,34,76,48],[46,54,24,10,55,25],[2,45,15,64,46,16],[17,152,122,4,153,123],[29,74,46,14,75,47],[49,54,24,10,55,25],[24,45,15,46,46,16],[4,152,122,18,153,123],[13,74,46,32,75,47],[48,54,24,14,55,25],[42,45,15,32,46,16],[20,147,117,4,148,118],[40,75,47,7,76,48],[43,54,24,22,55,25],[10,45,15,67,46,16],[19,148,118,6,149,119],[18,75,47,31,76,48],[34,54,24,34,55,25],[20,45,15,61,46,16]],e=function(t,e){var i={};return i.totalCount=t,i.dataCount=e,i},i={getRSBlocks:function(i,r){var n=function(e,i){switch(i){case s.L:return t[4*(e-1)+0];case s.M:return t[4*(e-1)+1];case s.Q:return t[4*(e-1)+2];case s.H:return t[4*(e-1)+3];default:return}}(i,r);if(void 0===n)throw\"bad rs block @ typeNumber:\"+i+\"/errorCorrectionLevel:\"+r;for(var o=n.length/3,a=[],h=0;h<o;h+=1)for(var d=n[3*h+0],u=n[3*h+1],c=n[3*h+2],l=0;l<d;l+=1)a.push(e(u,c));return a}};return i}(),c=function(){var t=[],e=0,i={getBuffer:function(){return t},getAt:function(e){var i=Math.floor(e/8);return 1==(t[i]>>>7-e%8&1)},put:function(t,e){for(var r=0;r<e;r+=1)i.putBit(1==(t>>>e-r-1&1))},getLengthInBits:function(){return e},putBit:function(i){var r=Math.floor(e/8);t.length<=r&&t.push(0),i&&(t[r]|=128>>>e%8),e+=1}};return i},l=function(t){var e=t,i={getMode:function(){return 1},getLength:function(t){return e.length},write:function(t){for(var i=e,n=0;n+2<i.length;)t.put(r(i.substring(n,n+3)),10),n+=3;n<i.length&&(i.length-n==1?t.put(r(i.substring(n,n+1)),4):i.length-n==2&&t.put(r(i.substring(n,n+2)),7))}},r=function(t){for(var e=0,i=0;i<t.length;i+=1)e=10*e+n(t.charAt(i));return e},n=function(t){if(\"0\"<=t&&t<=\"9\")return t.charCodeAt(0)-\"0\".charCodeAt(0);throw\"illegal char :\"+t};return i},g=function(t){var e=t,i={getMode:function(){return 2},getLength:function(t){return e.length},write:function(t){for(var i=e,n=0;n+1<i.length;)t.put(45*r(i.charAt(n))+r(i.charAt(n+1)),11),n+=2;n<i.length&&t.put(r(i.charAt(n)),6)}},r=function(t){if(\"0\"<=t&&t<=\"9\")return t.charCodeAt(0)-\"0\".charCodeAt(0);if(\"A\"<=t&&t<=\"Z\")return t.charCodeAt(0)-\"A\".charCodeAt(0)+10;switch(t){case\" \":return 36;case\"$\":return 37;case\"%\":return 38;case\"*\":return 39;case\"+\":return 40;case\"-\":return 41;case\".\":return 42;case\"/\":return 43;case\":\":return 44;default:throw\"illegal char :\"+t}};return i},f=function(e){var i=t.stringToBytes(e);return{getMode:function(){return 4},getLength:function(t){return i.length},write:function(t){for(var e=0;e<i.length;e+=1)t.put(i[e],8)}}},w=function(e){var i=t.stringToBytesFuncs.SJIS;if(!i)throw\"sjis not supported.\";!function(){var t=i(\"友\");if(2!=t.length||38726!=(t[0]<<8|t[1]))throw\"sjis not supported.\"}();var r=i(e),n={getMode:function(){return 8},getLength:function(t){return~~(r.length/2)},write:function(t){for(var e=r,i=0;i+1<e.length;){var n=(255&e[i])<<8|255&e[i+1];if(33088<=n&&n<=40956)n-=33088;else{if(!(57408<=n&&n<=60351))throw\"illegal char at \"+(i+1)+\"/\"+n;n-=49472}n=192*(n>>>8&255)+(255&n),t.put(n,13),i+=2}if(i<e.length)throw\"illegal char at \"+(i+1)}};return n},p=function(){var t=[],e={writeByte:function(e){t.push(255&e)},writeShort:function(t){e.writeByte(t),e.writeByte(t>>>8)},writeBytes:function(t,i,r){i=i||0,r=r||t.length;for(var n=0;n<r;n+=1)e.writeByte(t[n+i])},writeString:function(t){for(var i=0;i<t.length;i+=1)e.writeByte(t.charCodeAt(i))},toByteArray:function(){return t},toString:function(){var e=\"\";e+=\"[\";for(var i=0;i<t.length;i+=1)i>0&&(e+=\",\"),e+=t[i];return e+\"]\"}};return e},v=function(t){var e=t,i=0,r=0,n=0,o={read:function(){for(;n<8;){if(i>=e.length){if(0==n)return-1;throw\"unexpected end of file./\"+n}var t=e.charAt(i);if(i+=1,\"=\"==t)return n=0,-1;t.match(/^\\s$/)||(r=r<<6|s(t.charCodeAt(0)),n+=6)}var o=r>>>n-8&255;return n-=8,o}},s=function(t){if(65<=t&&t<=90)return t-65;if(97<=t&&t<=122)return t-97+26;if(48<=t&&t<=57)return t-48+52;if(43==t)return 62;if(47==t)return 63;throw\"c:\"+t};return o},_=function(t,e,i){for(var r=function(t,e){var i=t,r=e,n=new Array(t*e),o={setPixel:function(t,e,r){n[e*i+t]=r},write:function(t){t.writeString(\"GIF87a\"),t.writeShort(i),t.writeShort(r),t.writeByte(128),t.writeByte(0),t.writeByte(0),t.writeByte(0),t.writeByte(0),t.writeByte(0),t.writeByte(255),t.writeByte(255),t.writeByte(255),t.writeString(\",\"),t.writeShort(0),t.writeShort(0),t.writeShort(i),t.writeShort(r),t.writeByte(0);var e=s(2);t.writeByte(2);for(var n=0;e.length-n>255;)t.writeByte(255),t.writeBytes(e,n,255),n+=255;t.writeByte(e.length-n),t.writeBytes(e,n,e.length-n),t.writeByte(0),t.writeString(\";\")}},s=function(t){for(var e=1<<t,i=1+(1<<t),r=t+1,o=a(),s=0;s<e;s+=1)o.add(String.fromCharCode(s));o.add(String.fromCharCode(e)),o.add(String.fromCharCode(i));var h,d,u,c=p(),l=(h=c,d=0,u=0,{write:function(t,e){if(t>>>e!=0)throw\"length over\";for(;d+e>=8;)h.writeByte(255&(t<<d|u)),e-=8-d,t>>>=8-d,u=0,d=0;u|=t<<d,d+=e},flush:function(){d>0&&h.writeByte(u)}});l.write(e,r);var g=0,f=String.fromCharCode(n[g]);for(g+=1;g<n.length;){var w=String.fromCharCode(n[g]);g+=1,o.contains(f+w)?f+=w:(l.write(o.indexOf(f),r),o.size()<4095&&(o.size()==1<<r&&(r+=1),o.add(f+w)),f=w)}return l.write(o.indexOf(f),r),l.write(i,r),l.flush(),c.toByteArray()},a=function(){var t={},e=0,i={add:function(r){if(i.contains(r))throw\"dup key:\"+r;t[r]=e,e+=1},size:function(){return e},indexOf:function(e){return t[e]},contains:function(e){return void 0!==t[e]}};return i};return o}(t,e),n=0;n<e;n+=1)for(var o=0;o<t;o+=1)r.setPixel(o,n,i(o,n));var s=p();r.write(s);for(var a=function(){var t=0,e=0,i=0,r=\"\",n={},o=function(t){r+=String.fromCharCode(s(63&t))},s=function(t){if(t<0);else{if(t<26)return 65+t;if(t<52)return t-26+97;if(t<62)return t-52+48;if(62==t)return 43;if(63==t)return 47}throw\"n:\"+t};return n.writeByte=function(r){for(t=t<<8|255&r,e+=8,i+=1;e>=6;)o(t>>>e-6),e-=6},n.flush=function(){if(e>0&&(o(t<<6-e),t=0,e=0),i%3!=0)for(var n=3-i%3,s=0;s<n;s+=1)r+=\"=\"},n.toString=function(){return r},n}(),h=s.toByteArray(),d=0;d<h.length;d+=1)a.writeByte(h[d]);return a.flush(),\"data:image/gif;base64,\"+a};return t}();n.stringToBytesFuncs[\"UTF-8\"]=function(t){return function(t){for(var e=[],i=0;i<t.length;i++){var r=t.charCodeAt(i);r<128?e.push(r):r<2048?e.push(192|r>>6,128|63&r):r<55296||r>=57344?e.push(224|r>>12,128|r>>6&63,128|63&r):(i++,r=65536+((1023&r)<<10|1023&t.charCodeAt(i)),e.push(240|r>>18,128|r>>12&63,128|r>>6&63,128|63&r))}return e}(t)},void 0===(r=\"function\"==typeof(i=function(){return n})?i.apply(e,[]):i)||(t.exports=r)}},e={};function i(r){var n=e[r];if(void 0!==n)return n.exports;var o=e[r]={exports:{}};return t[r](o,o.exports,i),o.exports}i.n=t=>{var e=t&&t.__esModule?()=>t.default:()=>t;return i.d(e,{a:e}),e},i.d=(t,e)=>{for(var r in e)i.o(e,r)&&!i.o(t,r)&&Object.defineProperty(t,r,{enumerable:!0,get:e[r]})},i.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e);var r={};return(()=>{\"use strict\";i.d(r,{default:()=>$});const t=t=>!!t&&\"object\"==typeof t&&!Array.isArray(t);function e(i,...r){if(!r.length)return i;const n=r.shift();return void 0!==n&&t(i)&&t(n)?(i=Object.assign({},i),Object.keys(n).forEach((r=>{const o=i[r],s=n[r];Array.isArray(o)&&Array.isArray(s)?i[r]=s:t(o)&&t(s)?i[r]=e(Object.assign({},o),s):i[r]=s})),e(i,...r)):i}function n(t,e){const i=document.createElement(\"a\");i.download=e,i.href=t,document.body.appendChild(i),i.click(),document.body.removeChild(i)}const o={L:.07,M:.15,Q:.25,H:.3};class s{constructor({svg:t,type:e,window:i}){this._svg=t,this._type=e,this._window=i}draw(t,e,i,r){let n;switch(this._type){case\"dots\":n=this._drawDot;break;case\"classy\":n=this._drawClassy;break;case\"classy-rounded\":n=this._drawClassyRounded;break;case\"rounded\":n=this._drawRounded;break;case\"extra-rounded\":n=this._drawExtraRounded;break;default:n=this._drawSquare}n.call(this,{x:t,y:e,size:i,getNeighbor:r})}_rotateFigure({x:t,y:e,size:i,rotation:r=0,draw:n}){var o;const s=t+i/2,a=e+i/2;n(),null===(o=this._element)||void 0===o||o.setAttribute(\"transform\",`rotate(${180*r/Math.PI},${s},${a})`)}_basicDot(t){const{size:e,x:i,y:r}=t;this._rotateFigure(Object.assign(Object.assign({},t),{draw:()=>{this._element=this._window.document.createElementNS(\"http://www.w3.org/2000/svg\",\"circle\"),this._element.setAttribute(\"cx\",String(i+e/2)),this._element.setAttribute(\"cy\",String(r+e/2)),this._element.setAttribute(\"r\",String(e/2))}}))}_basicSquare(t){const{size:e,x:i,y:r}=t;this._rotateFigure(Object.assign(Object.assign({},t),{draw:()=>{this._element=this._window.document.createElementNS(\"http://www.w3.org/2000/svg\",\"rect\"),this._element.setAttribute(\"x\",String(i)),this._element.setAttribute(\"y\",String(r)),this._element.setAttribute(\"width\",String(e)),this._element.setAttribute(\"height\",String(e))}}))}_basicSideRounded(t){const{size:e,x:i,y:r}=t;this._rotateFigure(Object.assign(Object.assign({},t),{draw:()=>{this._element=this._window.document.createElementNS(\"http://www.w3.org/2000/svg\",\"path\"),this._element.setAttribute(\"d\",`M ${i} ${r}v ${e}h `+e/2+`a ${e/2} ${e/2}, 0, 0, 0, 0 ${-e}`)}}))}_basicCornerRounded(t){const{size:e,x:i,y:r}=t;this._rotateFigure(Object.assign(Object.assign({},t),{draw:()=>{this._element=this._window.document.createElementNS(\"http://www.w3.org/2000/svg\",\"path\"),this._element.setAttribute(\"d\",`M ${i} ${r}v ${e}h ${e}v `+-e/2+`a ${e/2} ${e/2}, 0, 0, 0, ${-e/2} ${-e/2}`)}}))}_basicCornerExtraRounded(t){const{size:e,x:i,y:r}=t;this._rotateFigure(Object.assign(Object.assign({},t),{draw:()=>{this._element=this._window.document.createElementNS(\"http://www.w3.org/2000/svg\",\"path\"),this._element.setAttribute(\"d\",`M ${i} ${r}v ${e}h ${e}a ${e} ${e}, 0, 0, 0, ${-e} ${-e}`)}}))}_basicCornersRounded(t){const{size:e,x:i,y:r}=t;this._rotateFigure(Object.assign(Object.assign({},t),{draw:()=>{this._element=this._window.document.createElementNS(\"http://www.w3.org/2000/svg\",\"path\"),this._element.setAttribute(\"d\",`M ${i} ${r}v `+e/2+`a ${e/2} ${e/2}, 0, 0, 0, ${e/2} ${e/2}h `+e/2+\"v \"+-e/2+`a ${e/2} ${e/2}, 0, 0, 0, ${-e/2} ${-e/2}`)}}))}_drawDot({x:t,y:e,size:i}){this._basicDot({x:t,y:e,size:i,rotation:0})}_drawSquare({x:t,y:e,size:i}){this._basicSquare({x:t,y:e,size:i,rotation:0})}_drawRounded({x:t,y:e,size:i,getNeighbor:r}){const n=r?+r(-1,0):0,o=r?+r(1,0):0,s=r?+r(0,-1):0,a=r?+r(0,1):0,h=n+o+s+a;if(0!==h)if(h>2||n&&o||s&&a)this._basicSquare({x:t,y:e,size:i,rotation:0});else{if(2===h){let r=0;return n&&s?r=Math.PI/2:s&&o?r=Math.PI:o&&a&&(r=-Math.PI/2),void this._basicCornerRounded({x:t,y:e,size:i,rotation:r})}if(1===h){let r=0;return s?r=Math.PI/2:o?r=Math.PI:a&&(r=-Math.PI/2),void this._basicSideRounded({x:t,y:e,size:i,rotation:r})}}else this._basicDot({x:t,y:e,size:i,rotation:0})}_drawExtraRounded({x:t,y:e,size:i,getNeighbor:r}){const n=r?+r(-1,0):0,o=r?+r(1,0):0,s=r?+r(0,-1):0,a=r?+r(0,1):0,h=n+o+s+a;if(0!==h)if(h>2||n&&o||s&&a)this._basicSquare({x:t,y:e,size:i,rotation:0});else{if(2===h){let r=0;return n&&s?r=Math.PI/2:s&&o?r=Math.PI:o&&a&&(r=-Math.PI/2),void this._basicCornerExtraRounded({x:t,y:e,size:i,rotation:r})}if(1===h){let r=0;return s?r=Math.PI/2:o?r=Math.PI:a&&(r=-Math.PI/2),void this._basicSideRounded({x:t,y:e,size:i,rotation:r})}}else this._basicDot({x:t,y:e,size:i,rotation:0})}_drawClassy({x:t,y:e,size:i,getNeighbor:r}){const n=r?+r(-1,0):0,o=r?+r(1,0):0,s=r?+r(0,-1):0,a=r?+r(0,1):0;0!==n+o+s+a?n||s?o||a?this._basicSquare({x:t,y:e,size:i,rotation:0}):this._basicCornerRounded({x:t,y:e,size:i,rotation:Math.PI/2}):this._basicCornerRounded({x:t,y:e,size:i,rotation:-Math.PI/2}):this._basicCornersRounded({x:t,y:e,size:i,rotation:Math.PI/2})}_drawClassyRounded({x:t,y:e,size:i,getNeighbor:r}){const n=r?+r(-1,0):0,o=r?+r(1,0):0,s=r?+r(0,-1):0,a=r?+r(0,1):0;0!==n+o+s+a?n||s?o||a?this._basicSquare({x:t,y:e,size:i,rotation:0}):this._basicCornerExtraRounded({x:t,y:e,size:i,rotation:Math.PI/2}):this._basicCornerExtraRounded({x:t,y:e,size:i,rotation:-Math.PI/2}):this._basicCornersRounded({x:t,y:e,size:i,rotation:Math.PI/2})}}const a={dot:\"dot\",square:\"square\",extraRounded:\"extra-rounded\"},h=Object.values(a);class d{constructor({svg:t,type:e,window:i}){this._svg=t,this._type=e,this._window=i}draw(t,e,i,r){let n;switch(this._type){case a.square:n=this._drawSquare;break;case a.extraRounded:n=this._drawExtraRounded;break;default:n=this._drawDot}n.call(this,{x:t,y:e,size:i,rotation:r})}_rotateFigure({x:t,y:e,size:i,rotation:r=0,draw:n}){var o;const s=t+i/2,a=e+i/2;n(),null===(o=this._element)||void 0===o||o.setAttribute(\"transform\",`rotate(${180*r/Math.PI},${s},${a})`)}_basicDot(t){const{size:e,x:i,y:r}=t,n=e/7;this._rotateFigure(Object.assign(Object.assign({},t),{draw:()=>{this._element=this._window.document.createElementNS(\"http://www.w3.org/2000/svg\",\"path\"),this._element.setAttribute(\"clip-rule\",\"evenodd\"),this._element.setAttribute(\"d\",`M ${i+e/2} ${r}a ${e/2} ${e/2} 0 1 0 0.1 0zm 0 ${n}a ${e/2-n} ${e/2-n} 0 1 1 -0.1 0Z`)}}))}_basicSquare(t){const{size:e,x:i,y:r}=t,n=e/7;this._rotateFigure(Object.assign(Object.assign({},t),{draw:()=>{this._element=this._window.document.createElementNS(\"http://www.w3.org/2000/svg\",\"path\"),this._element.setAttribute(\"clip-rule\",\"evenodd\"),this._element.setAttribute(\"d\",`M ${i} ${r}v ${e}h ${e}v `+-e+\"z\"+`M ${i+n} ${r+n}h `+(e-2*n)+\"v \"+(e-2*n)+\"h \"+(2*n-e)+\"z\")}}))}_basicExtraRounded(t){const{size:e,x:i,y:r}=t,n=e/7;this._rotateFigure(Object.assign(Object.assign({},t),{draw:()=>{this._element=this._window.document.createElementNS(\"http://www.w3.org/2000/svg\",\"path\"),this._element.setAttribute(\"clip-rule\",\"evenodd\"),this._element.setAttribute(\"d\",`M ${i} ${r+2.5*n}v `+2*n+`a ${2.5*n} ${2.5*n}, 0, 0, 0, ${2.5*n} ${2.5*n}h `+2*n+`a ${2.5*n} ${2.5*n}, 0, 0, 0, ${2.5*n} ${2.5*-n}v `+-2*n+`a ${2.5*n} ${2.5*n}, 0, 0, 0, ${2.5*-n} ${2.5*-n}h `+-2*n+`a ${2.5*n} ${2.5*n}, 0, 0, 0, ${2.5*-n} ${2.5*n}`+`M ${i+2.5*n} ${r+n}h `+2*n+`a ${1.5*n} ${1.5*n}, 0, 0, 1, ${1.5*n} ${1.5*n}v `+2*n+`a ${1.5*n} ${1.5*n}, 0, 0, 1, ${1.5*-n} ${1.5*n}h `+-2*n+`a ${1.5*n} ${1.5*n}, 0, 0, 1, ${1.5*-n} ${1.5*-n}v `+-2*n+`a ${1.5*n} ${1.5*n}, 0, 0, 1, ${1.5*n} ${1.5*-n}`)}}))}_drawDot({x:t,y:e,size:i,rotation:r}){this._basicDot({x:t,y:e,size:i,rotation:r})}_drawSquare({x:t,y:e,size:i,rotation:r}){this._basicSquare({x:t,y:e,size:i,rotation:r})}_drawExtraRounded({x:t,y:e,size:i,rotation:r}){this._basicExtraRounded({x:t,y:e,size:i,rotation:r})}}const u={dot:\"dot\",square:\"square\"},c=Object.values(u);class l{constructor({svg:t,type:e,window:i}){this._svg=t,this._type=e,this._window=i}draw(t,e,i,r){let n;n=this._type===u.square?this._drawSquare:this._drawDot,n.call(this,{x:t,y:e,size:i,rotation:r})}_rotateFigure({x:t,y:e,size:i,rotation:r=0,draw:n}){var o;const s=t+i/2,a=e+i/2;n(),null===(o=this._element)||void 0===o||o.setAttribute(\"transform\",`rotate(${180*r/Math.PI},${s},${a})`)}_basicDot(t){const{size:e,x:i,y:r}=t;this._rotateFigure(Object.assign(Object.assign({},t),{draw:()=>{this._element=this._window.document.createElementNS(\"http://www.w3.org/2000/svg\",\"circle\"),this._element.setAttribute(\"cx\",String(i+e/2)),this._element.setAttribute(\"cy\",String(r+e/2)),this._element.setAttribute(\"r\",String(e/2))}}))}_basicSquare(t){const{size:e,x:i,y:r}=t;this._rotateFigure(Object.assign(Object.assign({},t),{draw:()=>{this._element=this._window.document.createElementNS(\"http://www.w3.org/2000/svg\",\"rect\"),this._element.setAttribute(\"x\",String(i)),this._element.setAttribute(\"y\",String(r)),this._element.setAttribute(\"width\",String(e)),this._element.setAttribute(\"height\",String(e))}}))}_drawDot({x:t,y:e,size:i,rotation:r}){this._basicDot({x:t,y:e,size:i,rotation:r})}_drawSquare({x:t,y:e,size:i,rotation:r}){this._basicSquare({x:t,y:e,size:i,rotation:r})}}const g=\"circle\",f=[[1,1,1,1,1,1,1],[1,0,0,0,0,0,1],[1,0,0,0,0,0,1],[1,0,0,0,0,0,1],[1,0,0,0,0,0,1],[1,0,0,0,0,0,1],[1,1,1,1,1,1,1]],w=[[0,0,0,0,0,0,0],[0,0,0,0,0,0,0],[0,0,1,1,1,0,0],[0,0,1,1,1,0,0],[0,0,1,1,1,0,0],[0,0,0,0,0,0,0],[0,0,0,0,0,0,0]];class p{constructor(t,e){this._roundSize=t=>this._options.dotsOptions.roundSize?Math.floor(t):t,this._window=e,this._element=this._window.document.createElementNS(\"http://www.w3.org/2000/svg\",\"svg\"),this._element.setAttribute(\"width\",String(t.width)),this._element.setAttribute(\"height\",String(t.height)),this._element.setAttribute(\"xmlns:xlink\",\"http://www.w3.org/1999/xlink\"),t.dotsOptions.roundSize||this._element.setAttribute(\"shape-rendering\",\"crispEdges\"),this._element.setAttribute(\"viewBox\",`0 0 ${t.width} ${t.height}`),this._defs=this._window.document.createElementNS(\"http://www.w3.org/2000/svg\",\"defs\"),this._element.appendChild(this._defs),this._imageUri=t.image,this._instanceId=p.instanceCount++,this._options=t}get width(){return this._options.width}get height(){return this._options.height}getElement(){return this._element}async drawQR(t){const e=t.getModuleCount(),i=Math.min(this._options.width,this._options.height)-2*this._options.margin,r=this._options.shape===g?i/Math.sqrt(2):i,n=this._roundSize(r/e);let s={hideXDots:0,hideYDots:0,width:0,height:0};if(this._qr=t,this._options.image){if(await this.loadImage(),!this._image)return;const{imageOptions:t,qrOptions:i}=this._options,r=t.imageSize*o[i.errorCorrectionLevel],a=Math.floor(r*e*e);s=function({originalHeight:t,originalWidth:e,maxHiddenDots:i,maxHiddenAxisDots:r,dotSize:n}){const o={x:0,y:0},s={x:0,y:0};if(t<=0||e<=0||i<=0||n<=0)return{height:0,width:0,hideYDots:0,hideXDots:0};const a=t/e;return o.x=Math.floor(Math.sqrt(i/a)),o.x<=0&&(o.x=1),r&&r<o.x&&(o.x=r),o.x%2==0&&o.x--,s.x=o.x*n,o.y=1+2*Math.ceil((o.x*a-1)/2),s.y=Math.round(s.x*a),(o.y*o.x>i||r&&r<o.y)&&(r&&r<o.y?(o.y=r,o.y%2==0&&o.x--):o.y-=2,s.y=o.y*n,o.x=1+2*Math.ceil((o.y/a-1)/2),s.x=Math.round(s.y/a)),{height:s.y,width:s.x,hideYDots:o.y,hideXDots:o.x}}({originalWidth:this._image.width,originalHeight:this._image.height,maxHiddenDots:a,maxHiddenAxisDots:e-14,dotSize:n})}this.drawBackground(),this.drawDots(((t,i)=>{var r,n,o,a,h,d;return!(this._options.imageOptions.hideBackgroundDots&&t>=(e-s.hideYDots)/2&&t<(e+s.hideYDots)/2&&i>=(e-s.hideXDots)/2&&i<(e+s.hideXDots)/2||(null===(r=f[t])||void 0===r?void 0:r[i])||(null===(n=f[t-e+7])||void 0===n?void 0:n[i])||(null===(o=f[t])||void 0===o?void 0:o[i-e+7])||(null===(a=w[t])||void 0===a?void 0:a[i])||(null===(h=w[t-e+7])||void 0===h?void 0:h[i])||(null===(d=w[t])||void 0===d?void 0:d[i-e+7]))})),this.drawCorners(),this._options.image&&await this.drawImage({width:s.width,height:s.height,count:e,dotSize:n})}drawBackground(){var t,e,i;const r=this._element,n=this._options;if(r){const r=null===(t=n.backgroundOptions)||void 0===t?void 0:t.gradient,o=null===(e=n.backgroundOptions)||void 0===e?void 0:e.color;let s=n.height,a=n.width;if(r||o){const t=this._window.document.createElementNS(\"http://www.w3.org/2000/svg\",\"rect\");this._backgroundClipPath=this._window.document.createElementNS(\"http://www.w3.org/2000/svg\",\"clipPath\"),this._backgroundClipPath.setAttribute(\"id\",`clip-path-background-color-${this._instanceId}`),this._defs.appendChild(this._backgroundClipPath),(null===(i=n.backgroundOptions)||void 0===i?void 0:i.round)&&(s=a=Math.min(n.width,n.height),t.setAttribute(\"rx\",String(s/2*n.backgroundOptions.round))),t.setAttribute(\"x\",String(this._roundSize((n.width-a)/2))),t.setAttribute(\"y\",String(this._roundSize((n.height-s)/2))),t.setAttribute(\"width\",String(a)),t.setAttribute(\"height\",String(s)),this._backgroundClipPath.appendChild(t),this._createColor({options:r,color:o,additionalRotation:0,x:0,y:0,height:n.height,width:n.width,name:`background-color-${this._instanceId}`})}}}drawDots(t){var e,i;if(!this._qr)throw\"QR code is not defined\";const r=this._options,n=this._qr.getModuleCount();if(n>r.width||n>r.height)throw\"The canvas is too small.\";const o=Math.min(r.width,r.height)-2*r.margin,a=r.shape===g?o/Math.sqrt(2):o,h=this._roundSize(a/n),d=this._roundSize((r.width-n*h)/2),u=this._roundSize((r.height-n*h)/2),c=new s({svg:this._element,type:r.dotsOptions.type,window:this._window});this._dotsClipPath=this._window.document.createElementNS(\"http://www.w3.org/2000/svg\",\"clipPath\"),this._dotsClipPath.setAttribute(\"id\",`clip-path-dot-color-${this._instanceId}`),this._defs.appendChild(this._dotsClipPath),this._createColor({options:null===(e=r.dotsOptions)||void 0===e?void 0:e.gradient,color:r.dotsOptions.color,additionalRotation:0,x:0,y:0,height:r.height,width:r.width,name:`dot-color-${this._instanceId}`});for(let e=0;e<n;e++)for(let r=0;r<n;r++)t&&!t(e,r)||(null===(i=this._qr)||void 0===i?void 0:i.isDark(e,r))&&(c.draw(d+r*h,u+e*h,h,((i,o)=>!(r+i<0||e+o<0||r+i>=n||e+o>=n)&&!(t&&!t(e+o,r+i))&&!!this._qr&&this._qr.isDark(e+o,r+i))),c._element&&this._dotsClipPath&&this._dotsClipPath.appendChild(c._element));if(r.shape===g){const t=this._roundSize((o/h-n)/2),e=n+2*t,i=d-t*h,r=u-t*h,s=[],a=this._roundSize(e/2);for(let i=0;i<e;i++){s[i]=[];for(let r=0;r<e;r++)i>=t-1&&i<=e-t&&r>=t-1&&r<=e-t||Math.sqrt((i-a)*(i-a)+(r-a)*(r-a))>a?s[i][r]=0:s[i][r]=this._qr.isDark(r-2*t<0?r:r>=n?r-2*t:r-t,i-2*t<0?i:i>=n?i-2*t:i-t)?1:0}for(let t=0;t<e;t++)for(let n=0;n<e;n++)s[t][n]&&(c.draw(i+n*h,r+t*h,h,((e,i)=>{var r;return!!(null===(r=s[t+i])||void 0===r?void 0:r[n+e])})),c._element&&this._dotsClipPath&&this._dotsClipPath.appendChild(c._element))}}drawCorners(){if(!this._qr)throw\"QR code is not defined\";const t=this._element,e=this._options;if(!t)throw\"Element code is not defined\";const i=this._qr.getModuleCount(),r=Math.min(e.width,e.height)-2*e.margin,n=e.shape===g?r/Math.sqrt(2):r,o=this._roundSize(n/i),a=7*o,u=3*o,p=this._roundSize((e.width-i*o)/2),v=this._roundSize((e.height-i*o)/2);[[0,0,0],[1,0,Math.PI/2],[0,1,-Math.PI/2]].forEach((([t,r,n])=>{var g,_,m,b,y,x,S,C,A,M,$,O,D,k;const z=p+t*o*(i-7),B=v+r*o*(i-7);let P=this._dotsClipPath,I=this._dotsClipPath;if(((null===(g=e.cornersSquareOptions)||void 0===g?void 0:g.gradient)||(null===(_=e.cornersSquareOptions)||void 0===_?void 0:_.color))&&(P=this._window.document.createElementNS(\"http://www.w3.org/2000/svg\",\"clipPath\"),P.setAttribute(\"id\",`clip-path-corners-square-color-${t}-${r}-${this._instanceId}`),this._defs.appendChild(P),this._cornersSquareClipPath=this._cornersDotClipPath=I=P,this._createColor({options:null===(m=e.cornersSquareOptions)||void 0===m?void 0:m.gradient,color:null===(b=e.cornersSquareOptions)||void 0===b?void 0:b.color,additionalRotation:n,x:z,y:B,height:a,width:a,name:`corners-square-color-${t}-${r}-${this._instanceId}`})),(null===(y=e.cornersSquareOptions)||void 0===y?void 0:y.type)&&h.includes(e.cornersSquareOptions.type)){const t=new d({svg:this._element,type:e.cornersSquareOptions.type,window:this._window});t.draw(z,B,a,n),t._element&&P&&P.appendChild(t._element)}else{const t=new s({svg:this._element,type:(null===(x=e.cornersSquareOptions)||void 0===x?void 0:x.type)||e.dotsOptions.type,window:this._window});for(let e=0;e<f.length;e++)for(let i=0;i<f[e].length;i++)(null===(S=f[e])||void 0===S?void 0:S[i])&&(t.draw(z+i*o,B+e*o,o,((t,r)=>{var n;return!!(null===(n=f[e+r])||void 0===n?void 0:n[i+t])})),t._element&&P&&P.appendChild(t._element))}if(((null===(C=e.cornersDotOptions)||void 0===C?void 0:C.gradient)||(null===(A=e.cornersDotOptions)||void 0===A?void 0:A.color))&&(I=this._window.document.createElementNS(\"http://www.w3.org/2000/svg\",\"clipPath\"),I.setAttribute(\"id\",`clip-path-corners-dot-color-${t}-${r}-${this._instanceId}`),this._defs.appendChild(I),this._cornersDotClipPath=I,this._createColor({options:null===(M=e.cornersDotOptions)||void 0===M?void 0:M.gradient,color:null===($=e.cornersDotOptions)||void 0===$?void 0:$.color,additionalRotation:n,x:z+2*o,y:B+2*o,height:u,width:u,name:`corners-dot-color-${t}-${r}-${this._instanceId}`})),(null===(O=e.cornersDotOptions)||void 0===O?void 0:O.type)&&c.includes(e.cornersDotOptions.type)){const t=new l({svg:this._element,type:e.cornersDotOptions.type,window:this._window});t.draw(z+2*o,B+2*o,u,n),t._element&&I&&I.appendChild(t._element)}else{const t=new s({svg:this._element,type:(null===(D=e.cornersDotOptions)||void 0===D?void 0:D.type)||e.dotsOptions.type,window:this._window});for(let e=0;e<w.length;e++)for(let i=0;i<w[e].length;i++)(null===(k=w[e])||void 0===k?void 0:k[i])&&(t.draw(z+i*o,B+e*o,o,((t,r)=>{var n;return!!(null===(n=w[e+r])||void 0===n?void 0:n[i+t])})),t._element&&I&&I.appendChild(t._element))}}))}loadImage(){return new Promise(((t,e)=>{var i;const r=this._options;if(!r.image)return e(\"Image is not defined\");if(null===(i=r.nodeCanvas)||void 0===i?void 0:i.loadImage)r.nodeCanvas.loadImage(r.image).then((e=>{var i,n;if(this._image=e,this._options.imageOptions.saveAsBlob){const t=null===(i=r.nodeCanvas)||void 0===i?void 0:i.createCanvas(this._image.width,this._image.height);null===(n=null==t?void 0:t.getContext(\"2d\"))||void 0===n||n.drawImage(e,0,0),this._imageUri=null==t?void 0:t.toDataURL()}t()})).catch(e);else{const e=new this._window.Image;\"string\"==typeof r.imageOptions.crossOrigin&&(e.crossOrigin=r.imageOptions.crossOrigin),this._image=e,e.onload=async()=>{this._options.imageOptions.saveAsBlob&&(this._imageUri=await async function(t,e){return new Promise((i=>{const r=new e.XMLHttpRequest;r.onload=function(){const t=new e.FileReader;t.onloadend=function(){i(t.result)},t.readAsDataURL(r.response)},r.open(\"GET\",t),r.responseType=\"blob\",r.send()}))}(r.image||\"\",this._window)),t()},e.src=r.image}}))}async drawImage({width:t,height:e,count:i,dotSize:r}){const n=this._options,o=this._roundSize((n.width-i*r)/2),s=this._roundSize((n.height-i*r)/2),a=o+this._roundSize(n.imageOptions.margin+(i*r-t)/2),h=s+this._roundSize(n.imageOptions.margin+(i*r-e)/2),d=t-2*n.imageOptions.margin,u=e-2*n.imageOptions.margin,c=this._window.document.createElementNS(\"http://www.w3.org/2000/svg\",\"image\");c.setAttribute(\"href\",this._imageUri||\"\"),c.setAttribute(\"xlink:href\",this._imageUri||\"\"),c.setAttribute(\"x\",String(a)),c.setAttribute(\"y\",String(h)),c.setAttribute(\"width\",`${d}px`),c.setAttribute(\"height\",`${u}px`),this._element.appendChild(c)}_createColor({options:t,color:e,additionalRotation:i,x:r,y:n,height:o,width:s,name:a}){const h=s>o?s:o,d=this._window.document.createElementNS(\"http://www.w3.org/2000/svg\",\"rect\");if(d.setAttribute(\"x\",String(r)),d.setAttribute(\"y\",String(n)),d.setAttribute(\"height\",String(o)),d.setAttribute(\"width\",String(s)),d.setAttribute(\"clip-path\",`url('#clip-path-${a}')`),t){let e;if(\"radial\"===t.type)e=this._window.document.createElementNS(\"http://www.w3.org/2000/svg\",\"radialGradient\"),e.setAttribute(\"id\",a),e.setAttribute(\"gradientUnits\",\"userSpaceOnUse\"),e.setAttribute(\"fx\",String(r+s/2)),e.setAttribute(\"fy\",String(n+o/2)),e.setAttribute(\"cx\",String(r+s/2)),e.setAttribute(\"cy\",String(n+o/2)),e.setAttribute(\"r\",String(h/2));else{const h=((t.rotation||0)+i)%(2*Math.PI),d=(h+2*Math.PI)%(2*Math.PI);let u=r+s/2,c=n+o/2,l=r+s/2,g=n+o/2;d>=0&&d<=.25*Math.PI||d>1.75*Math.PI&&d<=2*Math.PI?(u-=s/2,c-=o/2*Math.tan(h),l+=s/2,g+=o/2*Math.tan(h)):d>.25*Math.PI&&d<=.75*Math.PI?(c-=o/2,u-=s/2/Math.tan(h),g+=o/2,l+=s/2/Math.tan(h)):d>.75*Math.PI&&d<=1.25*Math.PI?(u+=s/2,c+=o/2*Math.tan(h),l-=s/2,g-=o/2*Math.tan(h)):d>1.25*Math.PI&&d<=1.75*Math.PI&&(c+=o/2,u+=s/2/Math.tan(h),g-=o/2,l-=s/2/Math.tan(h)),e=this._window.document.createElementNS(\"http://www.w3.org/2000/svg\",\"linearGradient\"),e.setAttribute(\"id\",a),e.setAttribute(\"gradientUnits\",\"userSpaceOnUse\"),e.setAttribute(\"x1\",String(Math.round(u))),e.setAttribute(\"y1\",String(Math.round(c))),e.setAttribute(\"x2\",String(Math.round(l))),e.setAttribute(\"y2\",String(Math.round(g)))}t.colorStops.forEach((({offset:t,color:i})=>{const r=this._window.document.createElementNS(\"http://www.w3.org/2000/svg\",\"stop\");r.setAttribute(\"offset\",100*t+\"%\"),r.setAttribute(\"stop-color\",i),e.appendChild(r)})),d.setAttribute(\"fill\",`url('#${a}')`),this._defs.appendChild(e)}else e&&d.setAttribute(\"fill\",e);this._element.appendChild(d)}}p.instanceCount=0;const v=p,_=\"canvas\",m={};for(let t=0;t<=40;t++)m[t]=t;const b={type:_,shape:\"square\",width:300,height:300,data:\"\",margin:0,qrOptions:{typeNumber:m[0],mode:void 0,errorCorrectionLevel:\"Q\"},imageOptions:{saveAsBlob:!0,hideBackgroundDots:!0,imageSize:.4,crossOrigin:void 0,margin:0},dotsOptions:{type:\"square\",color:\"#000\",roundSize:!0},backgroundOptions:{round:0,color:\"#fff\"}};function y(t){const e=Object.assign({},t);if(!e.colorStops||!e.colorStops.length)throw\"Field 'colorStops' is required in gradient\";return e.rotation?e.rotation=Number(e.rotation):e.rotation=0,e.colorStops=e.colorStops.map((t=>Object.assign(Object.assign({},t),{offset:Number(t.offset)}))),e}function x(t){const e=Object.assign({},t);return e.width=Number(e.width),e.height=Number(e.height),e.margin=Number(e.margin),e.imageOptions=Object.assign(Object.assign({},e.imageOptions),{hideBackgroundDots:Boolean(e.imageOptions.hideBackgroundDots),imageSize:Number(e.imageOptions.imageSize),margin:Number(e.imageOptions.margin)}),e.margin>Math.min(e.width,e.height)&&(e.margin=Math.min(e.width,e.height)),e.dotsOptions=Object.assign({},e.dotsOptions),e.dotsOptions.gradient&&(e.dotsOptions.gradient=y(e.dotsOptions.gradient)),e.cornersSquareOptions&&(e.cornersSquareOptions=Object.assign({},e.cornersSquareOptions),e.cornersSquareOptions.gradient&&(e.cornersSquareOptions.gradient=y(e.cornersSquareOptions.gradient))),e.cornersDotOptions&&(e.cornersDotOptions=Object.assign({},e.cornersDotOptions),e.cornersDotOptions.gradient&&(e.cornersDotOptions.gradient=y(e.cornersDotOptions.gradient))),e.backgroundOptions&&(e.backgroundOptions=Object.assign({},e.backgroundOptions),e.backgroundOptions.gradient&&(e.backgroundOptions.gradient=y(e.backgroundOptions.gradient))),e}var S=i(873),C=i.n(S);function A(t){if(!t)throw new Error(\"Extension must be defined\");\".\"===t[0]&&(t=t.substring(1));const e={bmp:\"image/bmp\",gif:\"image/gif\",ico:\"image/vnd.microsoft.icon\",jpeg:\"image/jpeg\",jpg:\"image/jpeg\",png:\"image/png\",svg:\"image/svg+xml\",tif:\"image/tiff\",tiff:\"image/tiff\",webp:\"image/webp\",pdf:\"application/pdf\"}[t.toLowerCase()];if(!e)throw new Error(`Extension \"${t}\" is not supported`);return e}class M{constructor(t){(null==t?void 0:t.jsdom)?this._window=new t.jsdom(\"\",{resources:\"usable\"}).window:this._window=window,this._options=t?x(e(b,t)):b,this.update()}static _clearContainer(t){t&&(t.innerHTML=\"\")}_setupSvg(){if(!this._qr)return;const t=new v(this._options,this._window);this._svg=t.getElement(),this._svgDrawingPromise=t.drawQR(this._qr).then((()=>{var e;this._svg&&(null===(e=this._extension)||void 0===e||e.call(this,t.getElement(),this._options))}))}_setupCanvas(){var t,e;this._qr&&((null===(t=this._options.nodeCanvas)||void 0===t?void 0:t.createCanvas)?(this._nodeCanvas=this._options.nodeCanvas.createCanvas(this._options.width,this._options.height),this._nodeCanvas.width=this._options.width,this._nodeCanvas.height=this._options.height):(this._domCanvas=document.createElement(\"canvas\"),this._domCanvas.width=this._options.width,this._domCanvas.height=this._options.height),this._setupSvg(),this._canvasDrawingPromise=null===(e=this._svgDrawingPromise)||void 0===e?void 0:e.then((()=>{var t;if(!this._svg)return;const e=this._svg,i=(new this._window.XMLSerializer).serializeToString(e),r=btoa(i),n=`data:${A(\"svg\")};base64,${r}`;if(null===(t=this._options.nodeCanvas)||void 0===t?void 0:t.loadImage)return this._options.nodeCanvas.loadImage(n).then((t=>{var e,i;t.width=this._options.width,t.height=this._options.height,null===(i=null===(e=this._nodeCanvas)||void 0===e?void 0:e.getContext(\"2d\"))||void 0===i||i.drawImage(t,0,0)}));{const t=new this._window.Image;return new Promise((e=>{t.onload=()=>{var i,r;null===(r=null===(i=this._domCanvas)||void 0===i?void 0:i.getContext(\"2d\"))||void 0===r||r.drawImage(t,0,0),e()},t.src=n}))}})))}async _getElement(t=\"png\"){if(!this._qr)throw\"QR code is empty\";return\"svg\"===t.toLowerCase()?(this._svg&&this._svgDrawingPromise||this._setupSvg(),await this._svgDrawingPromise,this._svg):((this._domCanvas||this._nodeCanvas)&&this._canvasDrawingPromise||this._setupCanvas(),await this._canvasDrawingPromise,this._domCanvas||this._nodeCanvas)}update(t){M._clearContainer(this._container),this._options=t?x(e(this._options,t)):this._options,this._options.data&&(this._qr=C()(this._options.qrOptions.typeNumber,this._options.qrOptions.errorCorrectionLevel),this._qr.addData(this._options.data,this._options.qrOptions.mode||function(t){switch(!0){case/^[0-9]*$/.test(t):return\"Numeric\";case/^[0-9A-Z $%*+\\-./:]*$/.test(t):return\"Alphanumeric\";default:return\"Byte\"}}(this._options.data)),this._qr.make(),this._options.type===_?this._setupCanvas():this._setupSvg(),this.append(this._container))}append(t){if(t){if(\"function\"!=typeof t.appendChild)throw\"Container should be a single DOM node\";this._options.type===_?this._domCanvas&&t.appendChild(this._domCanvas):this._svg&&t.appendChild(this._svg),this._container=t}}applyExtension(t){if(!t)throw\"Extension function should be defined.\";this._extension=t,this.update()}deleteExtension(){this._extension=void 0,this.update()}async getRawData(t=\"png\"){if(!this._qr)throw\"QR code is empty\";const e=await this._getElement(t),i=A(t);if(!e)return null;if(\"svg\"===t.toLowerCase()){const t=`<?xml version=\"1.0\" standalone=\"no\"?>\\r\\n${(new this._window.XMLSerializer).serializeToString(e)}`;return\"undefined\"==typeof Blob||this._options.jsdom?Buffer.from(t):new Blob([t],{type:i})}return new Promise((t=>{const r=e;if(\"toBuffer\"in r)if(\"image/png\"===i)t(r.toBuffer(i));else if(\"image/jpeg\"===i)t(r.toBuffer(i));else{if(\"application/pdf\"!==i)throw Error(\"Unsupported extension\");t(r.toBuffer(i))}else\"toBlob\"in r&&r.toBlob(t,i,1)}))}async download(t){if(!this._qr)throw\"QR code is empty\";if(\"undefined\"==typeof Blob)throw\"Cannot download in Node.js, call getRawData instead.\";let e=\"png\",i=\"qr\";\"string\"==typeof t?(e=t,console.warn(\"Extension is deprecated as argument for 'download' method, please pass object { name: '...', extension: '...' } as argument\")):\"object\"==typeof t&&null!==t&&(t.name&&(i=t.name),t.extension&&(e=t.extension));const r=await this._getElement(e);if(r)if(\"svg\"===e.toLowerCase()){let t=(new XMLSerializer).serializeToString(r);t='<?xml version=\"1.0\" standalone=\"no\"?>\\r\\n'+t,n(`data:${A(e)};charset=utf-8,${encodeURIComponent(t)}`,`${i}.svg`)}else n(r.toDataURL(A(e)),`${i}.${e}`)}}const $=M})(),r.default})()));\n//# sourceMappingURL=qr-code-styling.js.map"], "names": ["this", "t", "e", "i", "r", "n", "o", "h", "p", "v", "s", "d", "u", "c", "l", "g", "f", "w", "_", "a", "m", "b", "y", "x", "S", "C", "A", "M", "$"], "mappings": ";;;AAAA,GAAC,SAAS,GAAE,GAAE;AAAmD,WAAe,UAAA,EAAC;AAAA,EAA4H,EAAEA,gBAAM,OAAK,MAAI;AAAC,QAAI,IAAE,EAAC,KAAI,CAACC,IAAEC,OAAI;AAAC,UAAIC,IAAEC,IAAE,IAAE,WAAU;AAAC,YAAIH,KAAE,SAASA,IAAEC,IAAE;AAAC,cAAIC,KAAEF,IAAEG,KAAE,EAAEF,EAAC,GAAEG,KAAE,MAAKC,KAAE,GAAEC,KAAE,MAAKC,KAAE,CAAA,GAAGC,KAAE,IAAG,IAAE,SAASR,IAAEC,IAAE;AAAC,YAAAG,KAAE,SAASJ,IAAE;AAAC,uBAAQC,KAAE,IAAI,MAAMD,EAAC,GAAEE,KAAE,GAAEA,KAAEF,IAAEE,MAAG,GAAE;AAAC,gBAAAD,GAAEC,EAAC,IAAE,IAAI,MAAMF,EAAC;AAAE,yBAAQG,KAAE,GAAEA,KAAEH,IAAEG,MAAG,EAAE,CAAAF,GAAEC,EAAC,EAAEC,EAAC,IAAE;AAAA,cAAI;AAAC,qBAAOF;AAAA,YAAC,EAAEI,KAAE,IAAEH,KAAE,EAAE,GAAE,EAAE,GAAE,CAAC,GAAE,EAAEG,KAAE,GAAE,CAAC,GAAE,EAAE,GAAEA,KAAE,CAAC,GAAE,EAAG,GAAC,EAAG,GAAC,EAAEL,IAAEC,EAAC,GAAEC,MAAG,KAAG,EAAEF,EAAC,GAAE,QAAMM,OAAIA,KAAE,EAAEJ,IAAEC,IAAEI,EAAC,IAAG,EAAED,IAAEL,EAAC;AAAA,UAAC,GAAE,IAAE,SAASD,IAAEC,IAAE;AAAC,qBAAQC,KAAE,IAAGA,MAAG,GAAEA,MAAG,EAAE,KAAG,EAAEF,KAAEE,MAAG,MAAIG,MAAGL,KAAEE,IAAG,UAAQC,KAAE,IAAGA,MAAG,GAAEA,MAAG,EAAE,CAAAF,KAAEE,MAAG,MAAIE,MAAGJ,KAAEE,OAAIC,GAAEJ,KAAEE,EAAC,EAAED,KAAEE,EAAC,IAAE,KAAGD,MAAGA,MAAG,MAAI,KAAGC,MAAG,KAAGA,OAAI,KAAGA,MAAGA,MAAG,MAAI,KAAGD,MAAG,KAAGA,OAAI,KAAGA,MAAGA,MAAG,KAAG,KAAGC,MAAGA,MAAG;AAAA,UAAE,GAAE,IAAE,WAAU;AAAC,qBAAQH,KAAE,GAAEA,KAAEK,KAAE,GAAEL,MAAG,EAAE,SAAMI,GAAEJ,EAAC,EAAE,CAAC,MAAII,GAAEJ,EAAC,EAAE,CAAC,IAAEA,KAAE,KAAG;AAAG,qBAAQC,KAAE,GAAEA,KAAEI,KAAE,GAAEJ,MAAG,EAAE,SAAMG,GAAE,CAAC,EAAEH,EAAC,MAAIG,GAAE,CAAC,EAAEH,EAAC,IAAEA,KAAE,KAAG;AAAA,UAAE,GAAE,IAAE,WAAU;AAAC,qBAAQD,KAAE,EAAE,mBAAmBE,EAAC,GAAED,KAAE,GAAEA,KAAED,GAAE,QAAOC,MAAG,EAAE,UAAQE,KAAE,GAAEA,KAAEH,GAAE,QAAOG,MAAG,GAAE;AAAC,kBAAIE,KAAEL,GAAEC,EAAC,GAAEQ,KAAET,GAAEG,EAAC;AAAE,kBAAG,QAAMC,GAAEC,EAAC,EAAEI,EAAC,EAAE,UAAQH,KAAE,IAAGA,MAAG,GAAEA,MAAG,EAAE,UAAQI,KAAE,IAAGA,MAAG,GAAEA,MAAG,EAAE,CAAAN,GAAEC,KAAEC,EAAC,EAAEG,KAAEC,EAAC,IAAE,MAAIJ,MAAG,KAAGA,MAAG,MAAII,MAAG,KAAGA,MAAG,KAAGJ,MAAG,KAAGI;AAAA,YAAC;AAAA,UAAC,GAAE,IAAE,SAASV,IAAE;AAAC,qBAAQC,KAAE,EAAE,iBAAiBC,EAAC,GAAEC,KAAE,GAAEA,KAAE,IAAGA,MAAG,GAAE;AAAC,kBAAIM,KAAE,CAACT,MAAG,MAAIC,MAAGE,KAAE;AAAG,cAAAC,GAAE,KAAK,MAAMD,KAAE,CAAC,CAAC,EAAEA,KAAE,IAAEE,KAAE,IAAE,CAAC,IAAEI;AAAA,YAAC;AAAC,iBAAIN,KAAE,GAAEA,KAAE,IAAGA,MAAG,EAAE,CAAAM,KAAE,CAACT,MAAG,MAAIC,MAAGE,KAAE,IAAGC,GAAED,KAAE,IAAEE,KAAE,IAAE,CAAC,EAAE,KAAK,MAAMF,KAAE,CAAC,CAAC,IAAEM;AAAA,UAAC,GAAE,IAAE,SAAST,IAAEC,IAAE;AAAC,qBAAQC,KAAEC,MAAG,IAAEF,IAAEQ,KAAE,EAAE,eAAeP,EAAC,GAAEI,KAAE,GAAEA,KAAE,IAAGA,MAAG,GAAE;AAAC,kBAAII,KAAE,CAACV,MAAG,MAAIS,MAAGH,KAAE;AAAG,cAAAA,KAAE,IAAEF,GAAEE,EAAC,EAAE,CAAC,IAAEI,KAAEJ,KAAE,IAAEF,GAAEE,KAAE,CAAC,EAAE,CAAC,IAAEI,KAAEN,GAAEC,KAAE,KAAGC,EAAC,EAAE,CAAC,IAAEI;AAAA,YAAC;AAAC,iBAAIJ,KAAE,GAAEA,KAAE,IAAGA,MAAG,EAAE,CAAAI,KAAE,CAACV,MAAG,MAAIS,MAAGH,KAAE,IAAGA,KAAE,IAAEF,GAAE,CAAC,EAAEC,KAAEC,KAAE,CAAC,IAAEI,KAAEJ,KAAE,IAAEF,GAAE,CAAC,EAAE,KAAGE,KAAE,IAAE,CAAC,IAAEI,KAAEN,GAAE,CAAC,EAAE,KAAGE,KAAE,CAAC,IAAEI;AAAE,YAAAN,GAAEC,KAAE,CAAC,EAAE,CAAC,IAAE,CAACL;AAAA,UAAC,GAAE,IAAE,SAASA,IAAEC,IAAE;AAAC,qBAAQC,KAAE,IAAGC,KAAEE,KAAE,GAAEI,KAAE,GAAEH,KAAE,GAAEI,KAAE,EAAE,gBAAgBT,EAAC,GAAEU,KAAEN,KAAE,GAAEM,KAAE,GAAEA,MAAG,EAAE,MAAI,KAAGA,OAAIA,MAAG,QAAK;AAAC,uBAAQC,KAAE,GAAEA,KAAE,GAAEA,MAAG,EAAE,KAAG,QAAMR,GAAED,EAAC,EAAEQ,KAAEC,EAAC,GAAE;AAAC,oBAAIC,KAAE;AAAG,gBAAAP,KAAEN,GAAE,WAASa,KAAE,MAAIb,GAAEM,EAAC,MAAIG,KAAE,KAAIC,GAAEP,IAAEQ,KAAEC,EAAC,MAAIC,KAAE,CAACA,KAAGT,GAAED,EAAC,EAAEQ,KAAEC,EAAC,IAAEC,IAAE,OAAKJ,MAAG,OAAKH,MAAG,GAAEG,KAAE;AAAA,cAAE;AAAC,mBAAIN,MAAGD,MAAG,KAAGG,MAAGF,IAAE;AAAC,gBAAAA,MAAGD,IAAEA,KAAE,CAACA;AAAE;AAAA,cAAK;AAAA,YAAC;AAAA,UAAC,GAAE,IAAE,SAASF,IAAEC,IAAEC,IAAE;AAAC,qBAAQC,KAAE,EAAE,YAAYH,IAAEC,EAAC,GAAEG,KAAE,EAAC,GAAGC,KAAE,GAAEA,KAAEH,GAAE,QAAOG,MAAG,GAAE;AAAC,kBAAII,KAAEP,GAAEG,EAAC;AAAE,cAAAD,GAAE,IAAIK,GAAE,QAAO,GAAG,CAAC,GAAEL,GAAE,IAAIK,GAAE,UAAW,GAAC,EAAE,gBAAgBA,GAAE,QAAO,GAAGT,EAAC,CAAC,GAAES,GAAE,MAAML,EAAC;AAAA,YAAC;AAAC,gBAAIE,KAAE;AAAE,iBAAID,KAAE,GAAEA,KAAEF,GAAE,QAAOE,MAAG,EAAE,CAAAC,MAAGH,GAAEE,EAAC,EAAE;AAAU,gBAAGD,GAAE,oBAAkB,IAAEE,GAAE,OAAK,4BAA0BF,GAAE,gBAAiB,IAAC,MAAI,IAAEE,KAAE;AAAI,iBAAIF,GAAE,gBAAiB,IAAC,KAAG,IAAEE,MAAGF,GAAE,IAAI,GAAE,CAAC,GAAEA,GAAE,oBAAkB,KAAG,IAAG,CAAAA,GAAE,OAAO,KAAE;AAAE,mBAAK,EAAEA,GAAE,gBAAe,KAAI,IAAEE,OAAIF,GAAE,IAAI,KAAI,CAAC,GAAEA,GAAE,gBAAiB,KAAE,IAAEE,OAAK,CAAAF,GAAE,IAAI,IAAG,CAAC;AAAE,mBAAO,SAASJ,IAAEC,IAAE;AAAC,uBAAQC,KAAE,GAAEC,KAAE,GAAEC,KAAE,GAAEC,KAAE,IAAI,MAAMJ,GAAE,MAAM,GAAEQ,KAAE,IAAI,MAAMR,GAAE,MAAM,GAAEK,KAAE,GAAEA,KAAEL,GAAE,QAAOK,MAAG,GAAE;AAAC,oBAAIK,KAAEV,GAAEK,EAAC,EAAE,WAAUM,KAAEX,GAAEK,EAAC,EAAE,aAAWK;AAAE,gBAAAR,KAAE,KAAK,IAAIA,IAAEQ,EAAC,GAAEP,KAAE,KAAK,IAAIA,IAAEQ,EAAC,GAAEP,GAAEC,EAAC,IAAE,IAAI,MAAMK,EAAC;AAAE,yBAAQE,KAAE,GAAEA,KAAER,GAAEC,EAAC,EAAE,QAAOO,MAAG,EAAE,CAAAR,GAAEC,EAAC,EAAEO,EAAC,IAAE,MAAIb,GAAE,YAAYa,KAAEX,EAAC;AAAE,gBAAAA,MAAGS;AAAE,oBAAIG,KAAE,EAAE,0BAA0BF,EAAC,GAAEG,KAAE,EAAEV,GAAEC,EAAC,GAAEQ,GAAE,UAAW,IAAC,CAAC,EAAE,IAAIA,EAAC;AAAE,qBAAIL,GAAEH,EAAC,IAAE,IAAI,MAAMQ,GAAE,UAAS,IAAG,CAAC,GAAED,KAAE,GAAEA,KAAEJ,GAAEH,EAAC,EAAE,QAAOO,MAAG,GAAE;AAAC,sBAAIG,KAAEH,KAAEE,GAAE,cAAYN,GAAEH,EAAC,EAAE;AAAO,kBAAAG,GAAEH,EAAC,EAAEO,EAAC,IAAEG,MAAG,IAAED,GAAE,MAAMC,EAAC,IAAE;AAAA,gBAAC;AAAA,cAAC;AAAC,kBAAIT,KAAE;AAAE,mBAAIM,KAAE,GAAEA,KAAEZ,GAAE,QAAOY,MAAG,EAAE,CAAAN,MAAGN,GAAEY,EAAC,EAAE;AAAW,kBAAIL,KAAE,IAAI,MAAMD,EAAC,GAAEU,KAAE;AAAE,mBAAIJ,KAAE,GAAEA,KAAEV,IAAEU,MAAG,EAAE,MAAIP,KAAE,GAAEA,KAAEL,GAAE,QAAOK,MAAG,EAAE,CAAAO,KAAER,GAAEC,EAAC,EAAE,WAASE,GAAES,EAAC,IAAEZ,GAAEC,EAAC,EAAEO,EAAC,GAAEI,MAAG;AAAG,mBAAIJ,KAAE,GAAEA,KAAET,IAAES,MAAG,EAAE,MAAIP,KAAE,GAAEA,KAAEL,GAAE,QAAOK,MAAG,EAAE,CAAAO,KAAEJ,GAAEH,EAAC,EAAE,WAASE,GAAES,EAAC,IAAER,GAAEH,EAAC,EAAEO,EAAC,GAAEI,MAAG;AAAG,qBAAOT;AAAA,YAAC,EAAEJ,IAAED,EAAC;AAAA,UAAC;AAAE,UAAAK,GAAE,UAAQ,SAASR,IAAEC,IAAE;AAAC,gBAAIC,KAAE;AAAK,oBAAOD,KAAEA,MAAG,QAAQ;AAAA,cAAA,KAAI;AAAU,gBAAAC,KAAE,EAAEF,EAAC;AAAE;AAAA,cAAM,KAAI;AAAe,gBAAAE,KAAE,EAAEF,EAAC;AAAE;AAAA,cAAM,KAAI;AAAO,gBAAAE,KAAE,EAAEF,EAAC;AAAE;AAAA,cAAM,KAAI;AAAQ,gBAAAE,KAAE,EAAEF,EAAC;AAAE;AAAA,cAAM;AAAQ,sBAAK,UAAQC;AAAA,YAAC;AAAC,YAAAM,GAAE,KAAKL,EAAC,GAAEI,KAAE;AAAA,UAAI,GAAEE,GAAE,SAAO,SAASR,IAAEC,IAAE;AAAC,gBAAGD,KAAE,KAAGK,MAAGL,MAAGC,KAAE,KAAGI,MAAGJ,GAAE,OAAMD,KAAE,MAAIC;AAAE,mBAAOG,GAAEJ,EAAC,EAAEC,EAAC;AAAA,UAAC,GAAEO,GAAE,iBAAe,WAAU;AAAC,mBAAOH;AAAA,UAAC,GAAEG,GAAE,OAAK,WAAU;AAAC,gBAAGN,KAAE,GAAE;AAAC,uBAAQF,KAAE,GAAEA,KAAE,IAAGA,MAAI;AAAC,yBAAQC,KAAE,EAAE,YAAYD,IAAEG,EAAC,GAAEC,KAAE,EAAC,GAAGC,KAAE,GAAEA,KAAEE,GAAE,QAAOF,MAAI;AAAC,sBAAII,KAAEF,GAAEF,EAAC;AAAE,kBAAAD,GAAE,IAAIK,GAAE,QAAS,GAAC,CAAC,GAAEL,GAAE,IAAIK,GAAE,UAAS,GAAG,EAAE,gBAAgBA,GAAE,QAAO,GAAGT,EAAC,CAAC,GAAES,GAAE,MAAML,EAAC;AAAA,gBAAC;AAAC,oBAAIE,KAAE;AAAE,qBAAID,KAAE,GAAEA,KAAEJ,GAAE,QAAOI,KAAI,CAAAC,MAAGL,GAAEI,EAAC,EAAE;AAAU,oBAAGD,GAAE,gBAAe,KAAI,IAAEE,GAAE;AAAA,cAAK;AAAC,cAAAJ,KAAEF;AAAA,YAAC;AAAC,cAAE,OAAG,WAAU;AAAC,uBAAQA,KAAE,GAAEC,KAAE,GAAEC,KAAE,GAAEA,KAAE,GAAEA,MAAG,GAAE;AAAC,kBAAE,MAAGA,EAAC;AAAE,oBAAIC,KAAE,EAAE,aAAaK,EAAC;AAAE,iBAAC,KAAGN,MAAGF,KAAEG,QAAKH,KAAEG,IAAEF,KAAEC;AAAA,cAAE;AAAC,qBAAOD;AAAA,YAAC,EAAC,CAAE;AAAA,UAAC,GAAEO,GAAE,iBAAe,SAASR,IAAEC,IAAE;AAAC,YAAAD,KAAEA,MAAG;AAAE,gBAAIE,KAAE;AAAG,YAAAA,MAAG,kBAAiBA,MAAG,2CAA0CA,MAAG,+BAA8BA,MAAG,6BAA2BD,KAAE,WAASA,KAAE,IAAED,KAAEC,MAAG,OAAMC,MAAG,MAAKA,MAAG;AAAU,qBAAQC,KAAE,GAAEA,KAAEK,GAAE,eAAc,GAAGL,MAAG,GAAE;AAAC,cAAAD,MAAG;AAAO,uBAAQE,KAAE,GAAEA,KAAEI,GAAE,eAAc,GAAGJ,MAAG,EAAE,CAAAF,MAAG,eAAcA,MAAG,2CAA0CA,MAAG,+BAA8BA,MAAG,+BAA8BA,MAAG,aAAWF,KAAE,OAAME,MAAG,cAAYF,KAAE,OAAME,MAAG,uBAAsBA,MAAGM,GAAE,OAAOL,IAAEC,EAAC,IAAE,YAAU,WAAUF,MAAG,KAAIA,MAAG;AAAM,cAAAA,MAAG;AAAA,YAAO;AAAC,oBAAOA,MAAG,cAAY;AAAA,UAAU,GAAEM,GAAE,eAAa,SAASR,IAAEC,IAAEC,IAAEC,IAAE;AAAC,gBAAIC,KAAE;AAAG,wBAAU,OAAO,UAAU,CAAC,MAAIJ,MAAGI,KAAE,UAAU,CAAC,GAAG,UAASH,KAAEG,GAAE,QAAOF,KAAEE,GAAE,KAAID,KAAEC,GAAE,QAAOJ,KAAEA,MAAG,GAAEC,KAAE,WAASA,KAAE,IAAED,KAAEC,KAAGC,KAAE,YAAU,OAAOA,KAAE,EAAC,MAAKA,GAAC,IAAEA,MAAG,IAAI,OAAKA,GAAE,QAAM,MAAKA,GAAE,KAAGA,GAAE,OAAKA,GAAE,MAAI,uBAAqB,OAAMC,KAAE,YAAU,OAAOA,KAAE,EAAC,MAAKA,GAAC,IAAEA,MAAG,CAAE,GAAE,OAAKA,GAAE,QAAM,MAAKA,GAAE,KAAGA,GAAE,OAAKA,GAAE,MAAI,iBAAe;AAAK,gBAAIE,IAAEI,IAAES,IAAEZ,IAAEI,KAAEF,GAAE,mBAAiBR,KAAE,IAAEC,IAAEU,KAAE;AAAG,iBAAIL,KAAE,MAAIN,KAAE,UAAQA,KAAE,OAAKA,KAAE,WAASA,KAAE,MAAKW,MAAG,yDAAwDA,MAAGP,GAAE,WAAS,KAAG,aAAWM,KAAE,iBAAeA,KAAE,OAAMC,MAAG,mBAAiBD,KAAE,MAAIA,KAAE,MAAKC,MAAG,wCAAuCA,MAAGR,GAAE,QAAMD,GAAE,OAAK,kCAAgC,EAAE,CAACC,GAAE,IAAGD,GAAE,EAAE,EAAE,KAAK,GAAG,EAAE,KAAI,CAAE,IAAE,MAAI,IAAGS,MAAG,KAAIA,MAAGR,GAAE,OAAK,gBAAc,EAAEA,GAAE,EAAE,IAAE,OAAK,EAAEA,GAAE,IAAI,IAAE,aAAW,IAAGQ,MAAGT,GAAE,OAAK,sBAAoB,EAAEA,GAAE,EAAE,IAAE,OAAK,EAAEA,GAAE,IAAI,IAAE,mBAAiB,IAAGS,MAAG,iEAAgEA,MAAG,aAAYF,KAAE,GAAEA,KAAED,GAAE,eAAc,GAAGC,MAAG,EAAE,MAAIS,KAAET,KAAET,KAAEC,IAAEI,KAAE,GAAEA,KAAEG,GAAE,kBAAiBH,MAAG,EAAE,CAAAG,GAAE,OAAOC,IAAEJ,EAAC,MAAIM,MAAG,OAAKN,KAAEL,KAAEC,MAAG,MAAIiB,KAAEZ;AAAG,oBAAOK,MAAG,2CAAyC;AAAA,UAAQ,GAAEH,GAAE,gBAAc,SAASR,IAAEC,IAAE;AAAC,YAAAD,KAAEA,MAAG,GAAEC,KAAE,WAASA,KAAE,IAAED,KAAEC;AAAE,gBAAIC,KAAEM,GAAE,mBAAiBR,KAAE,IAAEC,IAAEE,KAAEF,IAAEG,KAAEF,KAAED;AAAE,mBAAO,EAAEC,IAAEA,IAAG,SAASD,IAAEC,IAAE;AAAC,kBAAGC,MAAGF,MAAGA,KAAEG,MAAGD,MAAGD,MAAGA,KAAEE,IAAE;AAAC,oBAAIC,KAAE,KAAK,OAAOJ,KAAEE,MAAGH,EAAC,GAAES,KAAE,KAAK,OAAOP,KAAEC,MAAGH,EAAC;AAAE,uBAAOQ,GAAE,OAAOC,IAAEJ,EAAC,IAAE,IAAE;AAAA,cAAC;AAAC,qBAAO;AAAA,YAAC,CAAG;AAAA,UAAA,GAAEG,GAAE,eAAa,SAASR,IAAEC,IAAEC,IAAE;AAAC,YAAAF,KAAEA,MAAG,GAAEC,KAAE,WAASA,KAAE,IAAED,KAAEC;AAAE,gBAAIE,KAAEK,GAAE,eAAc,IAAGR,KAAE,IAAEC,IAAEG,KAAE;AAAG,mBAAOA,MAAG,QAAOA,MAAG,UAASA,MAAGI,GAAE,cAAcR,IAAEC,EAAC,GAAEG,MAAG,KAAIA,MAAG,YAAWA,MAAGD,IAAEC,MAAG,KAAIA,MAAG,aAAYA,MAAGD,IAAEC,MAAG,KAAIF,OAAIE,MAAG,UAASA,MAAG,EAAEF,EAAC,GAAEE,MAAG,MAAKA,KAAE;AAAA,UAAI;AAAE,cAAI,IAAE,SAASJ,IAAE;AAAC,qBAAQC,KAAE,IAAGC,KAAE,GAAEA,KAAEF,GAAE,QAAOE,MAAG,GAAE;AAAC,kBAAIC,KAAEH,GAAE,OAAOE,EAAC;AAAE,sBAAOC,IAAC;AAAA,gBAAE,KAAI;AAAI,kBAAAF,MAAG;AAAO;AAAA,gBAAM,KAAI;AAAI,kBAAAA,MAAG;AAAO;AAAA,gBAAM,KAAI;AAAI,kBAAAA,MAAG;AAAQ;AAAA,gBAAM,KAAI;AAAI,kBAAAA,MAAG;AAAS;AAAA,gBAAM;AAAQ,kBAAAA,MAAGE;AAAA,cAAC;AAAA,YAAC;AAAC,mBAAOF;AAAA,UAAC;AAAE,iBAAOO,GAAE,cAAY,SAASR,IAAEC,IAAE;AAAC,iBAAID,KAAEA,MAAG,KAAG,EAAE,QAAO,SAASA,IAAE;AAAC,cAAAA,KAAE,WAASA,KAAE,IAAEA;AAAE,kBAAIC,IAAEC,IAAEC,IAAEC,IAAEC,IAAEI,KAAE,IAAED,GAAE,eAAgB,IAAC,IAAER,IAAEkB,KAAElB,IAAEM,KAAEG,KAAET,IAAEU,KAAE,EAAC,MAAK,KAAI,MAAK,KAAI,MAAK,KAAI,MAAK,IAAG,GAAEC,KAAE,EAAC,MAAK,KAAI,MAAK,KAAI,MAAK,KAAI,MAAK,IAAG,GAAEC,KAAE;AAAG,mBAAIX,KAAE,GAAEA,KAAEQ,IAAER,MAAG,GAAE;AAAC,qBAAIE,KAAE,KAAK,OAAOF,KAAEiB,MAAG,CAAC,GAAEd,KAAE,KAAK,OAAOH,KAAE,IAAEiB,MAAG,CAAC,GAAEhB,KAAE,GAAEA,KAAEO,IAAEP,MAAG,EAAE,CAAAG,KAAE,KAAIa,MAAGhB,MAAGA,KAAEI,MAAGY,MAAGjB,MAAGA,KAAEK,MAAGE,GAAE,OAAOL,IAAE,KAAK,OAAOD,KAAEgB,MAAG,CAAC,CAAC,MAAIb,KAAE,MAAKa,MAAGhB,MAAGA,KAAEI,MAAGY,MAAGjB,KAAE,KAAGA,KAAE,IAAEK,MAAGE,GAAE,OAAOJ,IAAE,KAAK,OAAOF,KAAEgB,MAAG,CAAC,CAAC,IAAEb,MAAG,MAAIA,MAAG,KAAIO,MAAGZ,KAAE,KAAGC,KAAE,KAAGK,KAAEK,GAAEN,EAAC,IAAEK,GAAEL,EAAC;AAAE,gBAAAO,MAAG;AAAA,cAAI;AAAC,qBAAOH,KAAE,KAAGT,KAAE,IAAEY,GAAE,UAAU,GAAEA,GAAE,SAAOH,KAAE,CAAC,IAAE,MAAMA,KAAE,CAAC,EAAE,KAAK,GAAG,IAAEG,GAAE,UAAU,GAAEA,GAAE,SAAO,CAAC;AAAA,YAAC,EAAEX,EAAC;AAAE,YAAAD,MAAG,GAAEC,KAAE,WAASA,KAAE,IAAED,KAAEC;AAAE,gBAAIC,IAAEC,IAAEC,IAAEC,IAAEI,KAAED,GAAE,mBAAiBR,KAAE,IAAEC,IAAEiB,KAAEjB,IAAEK,KAAEG,KAAER,IAAES,KAAE,MAAMV,KAAE,CAAC,EAAE,KAAK,IAAI,GAAEW,KAAE,MAAMX,KAAE,CAAC,EAAE,KAAK,IAAI,GAAEY,KAAE,IAAGC,KAAE;AAAG,iBAAIX,KAAE,GAAEA,KAAEO,IAAEP,MAAG,GAAE;AAAC,mBAAIE,KAAE,KAAK,OAAOF,KAAEgB,MAAGlB,EAAC,GAAEa,KAAE,IAAGV,KAAE,GAAEA,KAAEM,IAAEN,MAAG,EAAE,CAAAE,KAAE,GAAEa,MAAGf,MAAGA,KAAEG,MAAGY,MAAGhB,MAAGA,KAAEI,MAAGE,GAAE,OAAOJ,IAAE,KAAK,OAAOD,KAAEe,MAAGlB,EAAC,CAAC,MAAIK,KAAE,IAAGQ,MAAGR,KAAEK,KAAEC;AAAE,mBAAIP,KAAE,GAAEA,KAAEJ,IAAEI,MAAG,EAAE,CAAAQ,MAAGC,KAAE;AAAA,YAAI;AAAC,mBAAOD,GAAE,UAAU,GAAEA,GAAE,SAAO,CAAC;AAAA,UAAC,GAAEJ,GAAE,oBAAkB,SAASR,IAAEC,IAAE;AAAC,YAAAA,KAAEA,MAAG;AAAE,qBAAQC,KAAEM,GAAE,kBAAiBL,KAAE,GAAEA,KAAED,IAAEC,KAAI,UAAQC,KAAE,GAAEA,KAAEF,IAAEE,KAAI,CAAAJ,GAAE,YAAUQ,GAAE,OAAOL,IAAEC,EAAC,IAAE,UAAQ,SAAQJ,GAAE,SAASG,KAAEF,IAAEG,KAAEH,IAAEA,IAAEA,EAAC;AAAA,UAAC,GAAEO;AAAA,QAAC;AAAE,QAAAR,GAAE,iBAAeA,GAAE,qBAAmB,EAAC,SAAQ,SAASA,IAAE;AAAC,mBAAQC,KAAE,CAAE,GAACC,KAAE,GAAEA,KAAEF,GAAE,QAAOE,MAAG,GAAE;AAAC,gBAAIC,KAAEH,GAAE,WAAWE,EAAC;AAAE,YAAAD,GAAE,KAAK,MAAIE,EAAC;AAAA,UAAC;AAAC,iBAAOF;AAAA,QAAC,EAAC,GAAG,SAAQD,GAAE,sBAAoB,SAASA,IAAEC,IAAE;AAAC,cAAIC,KAAE,WAAU;AAAC,qBAAQA,KAAE,EAAEF,EAAC,GAAEG,KAAE,WAAU;AAAC,kBAAIH,KAAEE,GAAE,KAAI;AAAG,kBAAG,MAAIF,GAAE,OAAK;AAAM,qBAAOA;AAAA,YAAC,GAAEI,KAAE,GAAEC,KAAE,QAAK;AAAC,kBAAII,KAAEP,GAAE,KAAM;AAAC,kBAAG,MAAIO,GAAE;AAAM,kBAAIS,KAAEf,MAAIG,KAAEH,GAAC,KAAI,IAAEA,GAAC;AAAG,cAAAE,GAAE,OAAO,aAAaI,MAAG,IAAES,EAAC,CAAC,IAAEZ,IAAEF,MAAG;AAAA,YAAC;AAAC,gBAAGA,MAAGH,GAAE,OAAMG,KAAE,SAAOH;AAAE,mBAAOI;AAAA,UAAC,EAAG,GAACF,KAAE,IAAI,WAAW,CAAC;AAAE,iBAAO,SAASH,IAAE;AAAC,qBAAQC,KAAE,CAAA,GAAGG,KAAE,GAAEA,KAAEJ,GAAE,QAAOI,MAAG,GAAE;AAAC,kBAAIC,KAAEL,GAAE,WAAWI,EAAC;AAAE,kBAAGC,KAAE,IAAI,CAAAJ,GAAE,KAAKI,EAAC;AAAA,mBAAM;AAAC,oBAAII,KAAEP,GAAEF,GAAE,OAAOI,EAAC,CAAC;AAAE,4BAAU,OAAOK,MAAG,MAAIA,OAAIA,KAAER,GAAE,KAAKQ,EAAC,KAAGR,GAAE,KAAKQ,OAAI,CAAC,GAAER,GAAE,KAAK,MAAIQ,EAAC,KAAGR,GAAE,KAAKE,EAAC;AAAA,cAAC;AAAA,YAAC;AAAC,mBAAOF;AAAA,UAAC;AAAA,QAAC;AAAE,YAAIA,IAAEC,IAAEC,IAAEC,IAAE,GAAE,IAAE,EAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,EAAC,GAAE,KAAGH,KAAE,CAAC,CAAE,GAAC,CAAC,GAAE,EAAE,GAAE,CAAC,GAAE,EAAE,GAAE,CAAC,GAAE,EAAE,GAAE,CAAC,GAAE,EAAE,GAAE,CAAC,GAAE,EAAE,GAAE,CAAC,GAAE,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,IAAG,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,IAAG,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,IAAG,IAAG,GAAG,GAAE,CAAC,GAAE,IAAG,IAAG,IAAG,GAAG,GAAE,CAAC,GAAE,IAAG,IAAG,IAAG,GAAG,GAAE,CAAC,GAAE,IAAG,IAAG,IAAG,GAAG,GAAE,CAAC,GAAE,IAAG,IAAG,IAAG,GAAG,GAAE,CAAC,GAAE,IAAG,IAAG,IAAG,IAAG,GAAG,GAAE,CAAC,GAAE,IAAG,IAAG,IAAG,KAAI,GAAG,GAAE,CAAC,GAAE,IAAG,IAAG,IAAG,KAAI,GAAG,GAAE,CAAC,GAAE,IAAG,IAAG,IAAG,KAAI,GAAG,GAAE,CAAC,GAAE,IAAG,IAAG,IAAG,KAAI,GAAG,GAAE,CAAC,GAAE,IAAG,IAAG,IAAG,KAAI,GAAG,GAAE,CAAC,GAAE,IAAG,IAAG,IAAG,KAAI,GAAG,GAAE,CAAC,GAAE,IAAG,IAAG,IAAG,KAAI,KAAI,GAAG,GAAE,CAAC,GAAE,IAAG,IAAG,IAAG,KAAI,KAAI,GAAG,GAAE,CAAC,GAAE,IAAG,IAAG,IAAG,KAAI,KAAI,GAAG,GAAE,CAAC,GAAE,IAAG,IAAG,IAAG,KAAI,KAAI,GAAG,GAAE,CAAC,GAAE,IAAG,IAAG,IAAG,KAAI,KAAI,GAAG,GAAE,CAAC,GAAE,IAAG,IAAG,IAAG,KAAI,KAAI,GAAG,CAAC,GAAEC,KAAE,MAAKC,KAAE,MAAK,IAAE,SAASH,IAAE;AAAC,mBAAQC,KAAE,GAAE,KAAGD,KAAG,CAAAC,MAAG,GAAED,QAAK;AAAE,iBAAOC;AAAA,QAAC,IAAGG,KAAE,CAAA,GAAI,iBAAe,SAASJ,IAAE;AAAC,mBAAQC,KAAED,MAAG,IAAG,EAAEC,EAAC,IAAE,EAAEC,EAAC,KAAG,IAAG,CAAAD,MAAGC,MAAG,EAAED,EAAC,IAAE,EAAEC,EAAC;AAAE,iBAAO,SAAOF,MAAG,KAAGC;AAAA,QAAE,GAAEG,GAAE,mBAAiB,SAASJ,IAAE;AAAC,mBAAQC,KAAED,MAAG,IAAG,EAAEC,EAAC,IAAE,EAAEE,EAAC,KAAG,IAAG,CAAAF,MAAGE,MAAG,EAAEF,EAAC,IAAE,EAAEE,EAAC;AAAE,iBAAOH,MAAG,KAAGC;AAAA,QAAC,GAAEG,GAAE,qBAAmB,SAASJ,IAAE;AAAC,iBAAOC,GAAED,KAAE,CAAC;AAAA,QAAC,GAAEI,GAAE,kBAAgB,SAASJ,IAAE;AAAC,kBAAOA,IAAG;AAAA,YAAA,KAAK;AAAE,qBAAO,SAASA,IAAEC,IAAE;AAAC,wBAAOD,KAAEC,MAAG,KAAG;AAAA,cAAC;AAAA,YAAE,KAAK;AAAE,qBAAO,SAASD,IAAEC,IAAE;AAAC,uBAAOD,KAAE,KAAG;AAAA,cAAC;AAAA,YAAE,KAAK;AAAE,qBAAO,SAASA,IAAEC,IAAE;AAAC,uBAAOA,KAAE,KAAG;AAAA,cAAC;AAAA,YAAE,KAAK;AAAE,qBAAO,SAASD,IAAEC,IAAE;AAAC,wBAAOD,KAAEC,MAAG,KAAG;AAAA,cAAC;AAAA,YAAE,KAAK;AAAE,qBAAO,SAASD,IAAEC,IAAE;AAAC,wBAAO,KAAK,MAAMD,KAAE,CAAC,IAAE,KAAK,MAAMC,KAAE,CAAC,KAAG,KAAG;AAAA,cAAC;AAAA,YAAE,KAAK;AAAE,qBAAO,SAASD,IAAEC,IAAE;AAAC,uBAAOD,KAAEC,KAAE,IAAED,KAAEC,KAAE,KAAG;AAAA,cAAC;AAAA,YAAE,KAAK;AAAE,qBAAO,SAASD,IAAEC,IAAE;AAAC,wBAAOD,KAAEC,KAAE,IAAED,KAAEC,KAAE,KAAG,KAAG;AAAA,cAAC;AAAA,YAAE,KAAK;AAAE,qBAAO,SAASD,IAAEC,IAAE;AAAC,wBAAOD,KAAEC,KAAE,KAAGD,KAAEC,MAAG,KAAG,KAAG;AAAA,cAAC;AAAA,YAAE;AAAQ,oBAAK,qBAAmBD;AAAA,UAAC;AAAA,QAAC,GAAEI,GAAE,4BAA0B,SAASJ,IAAE;AAAC,mBAAQC,KAAE,EAAE,CAAC,CAAC,GAAE,CAAC,GAAEC,KAAE,GAAEA,KAAEF,IAAEE,MAAG,EAAE,CAAAD,KAAEA,GAAE,SAAS,EAAE,CAAC,GAAE,EAAE,KAAKC,EAAC,CAAC,GAAE,CAAC,CAAC;AAAE,iBAAOD;AAAA,QAAC,GAAEG,GAAE,kBAAgB,SAASJ,IAAEC,IAAE;AAAC,cAAG,KAAGA,MAAGA,KAAE,GAAG,SAAOD,IAAC;AAAA,YAAE,KAAK;AAAE,qBAAO;AAAA,YAAG,KAAK;AAAE,qBAAO;AAAA,YAAE,KAAK;AAAA,YAAE,KAAK;AAAE,qBAAO;AAAA,YAAE;AAAQ,oBAAK,UAAQA;AAAA,UAAC;AAAA,mBAASC,KAAE,GAAG,SAAOD,IAAG;AAAA,YAAA,KAAK;AAAE,qBAAO;AAAA,YAAG,KAAK;AAAE,qBAAO;AAAA,YAAG,KAAK;AAAE,qBAAO;AAAA,YAAG,KAAK;AAAE,qBAAO;AAAA,YAAG;AAAQ,oBAAK,UAAQA;AAAA,UAAC;AAAA,eAAK;AAAC,gBAAG,EAAEC,KAAE,IAAI,OAAK,UAAQA;AAAE,oBAAOD,IAAC;AAAA,cAAE,KAAK;AAAE,uBAAO;AAAA,cAAG,KAAK;AAAE,uBAAO;AAAA,cAAG,KAAK;AAAE,uBAAO;AAAA,cAAG,KAAK;AAAE,uBAAO;AAAA,cAAG;AAAQ,sBAAK,UAAQA;AAAA,YAAC;AAAA,UAAC;AAAA,QAAC,GAAEI,GAAE,eAAa,SAASJ,IAAE;AAAC,mBAAQC,KAAED,GAAE,eAAc,GAAGE,KAAE,GAAEC,KAAE,GAAEA,KAAEF,IAAEE,MAAG,EAAE,UAAQC,KAAE,GAAEA,KAAEH,IAAEG,MAAG,GAAE;AAAC,qBAAQC,KAAE,GAAEI,KAAET,GAAE,OAAOG,IAAEC,EAAC,GAAEc,KAAE,IAAGA,MAAG,GAAEA,MAAG,EAAE,KAAG,EAAEf,KAAEe,KAAE,KAAGjB,MAAGE,KAAEe,IAAG,UAAQZ,KAAE,IAAGA,MAAG,GAAEA,MAAG,EAAE,CAAAF,KAAEE,KAAE,KAAGL,MAAGG,KAAEE,MAAG,KAAGY,MAAG,KAAGZ,MAAGG,MAAGT,GAAE,OAAOG,KAAEe,IAAEd,KAAEE,EAAC,MAAID,MAAG;AAAG,YAAAA,KAAE,MAAIH,MAAG,IAAEG,KAAE;AAAA,UAAE;AAAC,eAAIF,KAAE,GAAEA,KAAEF,KAAE,GAAEE,MAAG,EAAE,MAAIC,KAAE,GAAEA,KAAEH,KAAE,GAAEG,MAAG,GAAE;AAAC,gBAAIM,KAAE;AAAE,YAAAV,GAAE,OAAOG,IAAEC,EAAC,MAAIM,MAAG,IAAGV,GAAE,OAAOG,KAAE,GAAEC,EAAC,MAAIM,MAAG,IAAGV,GAAE,OAAOG,IAAEC,KAAE,CAAC,MAAIM,MAAG,IAAGV,GAAE,OAAOG,KAAE,GAAEC,KAAE,CAAC,MAAIM,MAAG,IAAG,KAAGA,MAAG,KAAGA,OAAIR,MAAG;AAAA,UAAE;AAAC,eAAIC,KAAE,GAAEA,KAAEF,IAAEE,MAAG,EAAE,MAAIC,KAAE,GAAEA,KAAEH,KAAE,GAAEG,MAAG,EAAE,CAAAJ,GAAE,OAAOG,IAAEC,EAAC,KAAG,CAACJ,GAAE,OAAOG,IAAEC,KAAE,CAAC,KAAGJ,GAAE,OAAOG,IAAEC,KAAE,CAAC,KAAGJ,GAAE,OAAOG,IAAEC,KAAE,CAAC,KAAGJ,GAAE,OAAOG,IAAEC,KAAE,CAAC,KAAG,CAACJ,GAAE,OAAOG,IAAEC,KAAE,CAAC,KAAGJ,GAAE,OAAOG,IAAEC,KAAE,CAAC,MAAIF,MAAG;AAAI,eAAIE,KAAE,GAAEA,KAAEH,IAAEG,MAAG,EAAE,MAAID,KAAE,GAAEA,KAAEF,KAAE,GAAEE,MAAG,EAAE,CAAAH,GAAE,OAAOG,IAAEC,EAAC,KAAG,CAACJ,GAAE,OAAOG,KAAE,GAAEC,EAAC,KAAGJ,GAAE,OAAOG,KAAE,GAAEC,EAAC,KAAGJ,GAAE,OAAOG,KAAE,GAAEC,EAAC,KAAGJ,GAAE,OAAOG,KAAE,GAAEC,EAAC,KAAG,CAACJ,GAAE,OAAOG,KAAE,GAAEC,EAAC,KAAGJ,GAAE,OAAOG,KAAE,GAAEC,EAAC,MAAIF,MAAG;AAAI,cAAIS,KAAE;AAAE,eAAIP,KAAE,GAAEA,KAAEH,IAAEG,MAAG,EAAE,MAAID,KAAE,GAAEA,KAAEF,IAAEE,MAAG,EAAE,CAAAH,GAAE,OAAOG,IAAEC,EAAC,MAAIO,MAAG;AAAG,iBAAOT,KAAE,KAAK,IAAI,MAAIS,KAAEV,KAAEA,KAAE,EAAE,IAAE,IAAE;AAAA,QAAE,GAAEG,KAAG,IAAE,WAAU;AAAC,mBAAQJ,KAAE,IAAI,MAAM,GAAG,GAAEC,KAAE,IAAI,MAAM,GAAG,GAAEC,KAAE,GAAEA,KAAE,GAAEA,MAAG,EAAE,CAAAF,GAAEE,EAAC,IAAE,KAAGA;AAAE,eAAIA,KAAE,GAAEA,KAAE,KAAIA,MAAG,EAAE,CAAAF,GAAEE,EAAC,IAAEF,GAAEE,KAAE,CAAC,IAAEF,GAAEE,KAAE,CAAC,IAAEF,GAAEE,KAAE,CAAC,IAAEF,GAAEE,KAAE,CAAC;AAAE,eAAIA,KAAE,GAAEA,KAAE,KAAIA,MAAG,EAAE,CAAAD,GAAED,GAAEE,EAAC,CAAC,IAAEA;AAAE,iBAAM,EAAC,MAAK,SAASF,IAAE;AAAC,gBAAGA,KAAE,EAAE,OAAK,UAAQA,KAAE;AAAI,mBAAOC,GAAED,EAAC;AAAA,UAAC,GAAE,MAAK,SAASC,IAAE;AAAC,mBAAKA,KAAE,IAAG,CAAAA,MAAG;AAAI,mBAAKA,MAAG,MAAK,CAAAA,MAAG;AAAI,mBAAOD,GAAEC,EAAC;AAAA,UAAC,EAAC;AAAA,QAAC,EAAC;AAAG,iBAAS,EAAED,IAAEC,IAAE;AAAC,cAAG,WAASD,GAAE,OAAO,OAAMA,GAAE,SAAO,MAAIC;AAAE,cAAIC,KAAE,WAAU;AAAC,qBAAQA,KAAE,GAAEA,KAAEF,GAAE,UAAQ,KAAGA,GAAEE,EAAC,IAAG,CAAAA,MAAG;AAAE,qBAAQC,KAAE,IAAI,MAAMH,GAAE,SAAOE,KAAED,EAAC,GAAEG,KAAE,GAAEA,KAAEJ,GAAE,SAAOE,IAAEE,MAAG,EAAE,CAAAD,GAAEC,EAAC,IAAEJ,GAAEI,KAAEF,EAAC;AAAE,mBAAOC;AAAA,UAAC,EAAC,GAAGA,KAAE,EAAC,OAAM,SAASH,IAAE;AAAC,mBAAOE,GAAEF,EAAC;AAAA,UAAC,GAAE,WAAU,WAAU;AAAC,mBAAOE,GAAE;AAAA,UAAM,GAAE,UAAS,SAASF,IAAE;AAAC,qBAAQC,KAAE,IAAI,MAAME,GAAE,UAAW,IAACH,GAAE,UAAW,IAAC,CAAC,GAAEE,KAAE,GAAEA,KAAEC,GAAE,UAAW,GAACD,MAAG,EAAE,UAAQE,KAAE,GAAEA,KAAEJ,GAAE,UAAS,GAAGI,MAAG,EAAE,CAAAH,GAAEC,KAAEE,EAAC,KAAG,EAAE,KAAK,EAAE,KAAKD,GAAE,MAAMD,EAAC,CAAC,IAAE,EAAE,KAAKF,GAAE,MAAMI,EAAC,CAAC,CAAC;AAAE,mBAAO,EAAEH,IAAE,CAAC;AAAA,UAAC,GAAE,KAAI,SAASD,IAAE;AAAC,gBAAGG,GAAE,UAAS,IAAGH,GAAE,UAAW,IAAC,EAAE,QAAOG;AAAE,qBAAQF,KAAE,EAAE,KAAKE,GAAE,MAAM,CAAC,CAAC,IAAE,EAAE,KAAKH,GAAE,MAAM,CAAC,CAAC,GAAEE,KAAE,IAAI,MAAMC,GAAE,UAAW,CAAA,GAAEC,KAAE,GAAEA,KAAED,GAAE,UAAS,GAAGC,MAAG,EAAE,CAAAF,GAAEE,EAAC,IAAED,GAAE,MAAMC,EAAC;AAAE,iBAAIA,KAAE,GAAEA,KAAEJ,GAAE,UAAW,GAACI,MAAG,EAAE,CAAAF,GAAEE,EAAC,KAAG,EAAE,KAAK,EAAE,KAAKJ,GAAE,MAAMI,EAAC,CAAC,IAAEH,EAAC;AAAE,mBAAO,EAAEC,IAAE,CAAC,EAAE,IAAIF,EAAC;AAAA,UAAC,EAAC;AAAE,iBAAOG;AAAA,QAAC;AAAC,YAAI,IAAE,2BAAU;AAAC,cAAIH,KAAE,CAAC,CAAC,GAAE,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,CAAC,GAAE,CAAC,GAAE,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,EAAE,GAAE,CAAC,GAAE,KAAI,EAAE,GAAE,CAAC,GAAE,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,CAAC,GAAE,CAAC,GAAE,KAAI,GAAG,GAAE,CAAC,GAAE,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,IAAG,GAAE,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,IAAG,GAAE,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,IAAG,GAAE,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,IAAG,GAAE,IAAG,EAAE,GAAE,CAAC,GAAE,KAAI,EAAE,GAAE,CAAC,GAAE,IAAG,IAAG,GAAE,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,IAAG,GAAE,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,IAAG,GAAE,IAAG,EAAE,GAAE,CAAC,GAAE,KAAI,GAAG,GAAE,CAAC,GAAE,IAAG,IAAG,GAAE,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,IAAG,GAAE,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,IAAG,GAAE,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,IAAG,GAAE,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,IAAG,GAAE,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,IAAG,GAAE,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,IAAG,GAAE,IAAG,EAAE,GAAE,CAAC,GAAE,KAAI,EAAE,GAAE,CAAC,GAAE,IAAG,IAAG,GAAE,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,IAAG,GAAE,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,IAAG,GAAE,IAAG,EAAE,GAAE,CAAC,GAAE,KAAI,IAAG,GAAE,KAAI,EAAE,GAAE,CAAC,GAAE,IAAG,IAAG,GAAE,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,IAAG,GAAE,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,IAAG,GAAE,IAAG,EAAE,GAAE,CAAC,GAAE,KAAI,GAAG,GAAE,CAAC,GAAE,IAAG,IAAG,GAAE,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,IAAG,GAAE,IAAG,EAAE,GAAE,CAAC,IAAG,IAAG,IAAG,GAAE,IAAG,EAAE,GAAE,CAAC,GAAE,KAAI,KAAI,GAAE,KAAI,GAAG,GAAE,CAAC,GAAE,IAAG,IAAG,GAAE,IAAG,EAAE,GAAE,CAAC,IAAG,IAAG,IAAG,GAAE,IAAG,EAAE,GAAE,CAAC,IAAG,IAAG,IAAG,GAAE,IAAG,EAAE,GAAE,CAAC,GAAE,KAAI,IAAG,GAAE,KAAI,EAAE,GAAE,CAAC,GAAE,IAAG,IAAG,GAAE,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,IAAG,GAAE,IAAG,EAAE,GAAE,CAAC,IAAG,IAAG,IAAG,GAAE,IAAG,EAAE,GAAE,CAAC,GAAE,KAAI,IAAG,GAAE,KAAI,EAAE,GAAE,CAAC,GAAE,IAAG,IAAG,GAAE,IAAG,EAAE,GAAE,CAAC,IAAG,IAAG,IAAG,GAAE,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,CAAC,GAAE,KAAI,KAAI,GAAE,KAAI,GAAG,GAAE,CAAC,IAAG,IAAG,IAAG,GAAE,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,CAAC,GAAE,KAAI,KAAI,GAAE,KAAI,GAAG,GAAE,CAAC,GAAE,IAAG,IAAG,GAAE,IAAG,EAAE,GAAE,CAAC,IAAG,IAAG,IAAG,GAAE,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,CAAC,GAAE,KAAI,KAAI,GAAE,KAAI,GAAG,GAAE,CAAC,GAAE,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,CAAC,IAAG,IAAG,IAAG,GAAE,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,CAAC,GAAE,KAAI,KAAI,GAAE,KAAI,GAAG,GAAE,CAAC,GAAE,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,CAAC,IAAG,IAAG,IAAG,GAAE,IAAG,EAAE,GAAE,CAAC,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,CAAC,GAAE,KAAI,KAAI,GAAE,KAAI,GAAG,GAAE,CAAC,IAAG,IAAG,EAAE,GAAE,CAAC,IAAG,IAAG,IAAG,GAAE,IAAG,EAAE,GAAE,CAAC,IAAG,IAAG,IAAG,GAAE,IAAG,EAAE,GAAE,CAAC,GAAE,KAAI,KAAI,GAAE,KAAI,GAAG,GAAE,CAAC,IAAG,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,CAAC,IAAG,IAAG,EAAE,GAAE,CAAC,GAAE,KAAI,KAAI,GAAE,KAAI,GAAG,GAAE,CAAC,GAAE,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,CAAC,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,CAAC,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,CAAC,GAAE,KAAI,KAAI,GAAE,KAAI,GAAG,GAAE,CAAC,GAAE,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,CAAC,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,CAAC,IAAG,IAAG,IAAG,GAAE,IAAG,EAAE,GAAE,CAAC,GAAE,KAAI,KAAI,GAAE,KAAI,GAAG,GAAE,CAAC,GAAE,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,CAAC,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,CAAC,IAAG,KAAI,KAAI,GAAE,KAAI,GAAG,GAAE,CAAC,IAAG,IAAG,IAAG,GAAE,IAAG,EAAE,GAAE,CAAC,IAAG,IAAG,IAAG,GAAE,IAAG,EAAE,GAAE,CAAC,IAAG,IAAG,IAAG,GAAE,IAAG,EAAE,GAAE,CAAC,GAAE,KAAI,KAAI,GAAE,KAAI,GAAG,GAAE,CAAC,IAAG,IAAG,IAAG,GAAE,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,CAAC,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,CAAC,GAAE,KAAI,KAAI,IAAG,KAAI,GAAG,GAAE,CAAC,GAAE,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,CAAC,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,CAAC,GAAE,KAAI,KAAI,GAAE,KAAI,GAAG,GAAE,CAAC,IAAG,IAAG,IAAG,GAAE,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,CAAC,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,CAAC,GAAE,KAAI,KAAI,IAAG,KAAI,GAAG,GAAE,CAAC,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,CAAC,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,CAAC,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,CAAC,IAAG,KAAI,KAAI,GAAE,KAAI,GAAG,GAAE,CAAC,GAAE,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,CAAC,IAAG,IAAG,IAAG,GAAE,IAAG,EAAE,GAAE,CAAC,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,CAAC,IAAG,KAAI,GAAG,GAAE,CAAC,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,CAAC,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,CAAC,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,CAAC,IAAG,KAAI,KAAI,GAAE,KAAI,GAAG,GAAE,CAAC,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,CAAC,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,CAAC,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,CAAC,IAAG,KAAI,KAAI,GAAE,KAAI,GAAG,GAAE,CAAC,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,CAAC,IAAG,IAAG,IAAG,GAAE,IAAG,EAAE,GAAE,CAAC,IAAG,IAAG,IAAG,GAAE,IAAG,EAAE,GAAE,CAAC,IAAG,KAAI,KAAI,GAAE,KAAI,GAAG,GAAE,CAAC,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,CAAC,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,CAAC,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,CAAC,GAAE,KAAI,KAAI,IAAG,KAAI,GAAG,GAAE,CAAC,GAAE,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,CAAC,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,CAAC,IAAG,KAAI,KAAI,GAAE,KAAI,GAAG,GAAE,CAAC,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,CAAC,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,CAAC,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,CAAC,GAAE,KAAI,KAAI,IAAG,KAAI,GAAG,GAAE,CAAC,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,CAAC,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,CAAC,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,CAAC,IAAG,KAAI,KAAI,GAAE,KAAI,GAAG,GAAE,CAAC,IAAG,IAAG,IAAG,GAAE,IAAG,EAAE,GAAE,CAAC,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,CAAC,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,CAAC,IAAG,KAAI,KAAI,GAAE,KAAI,GAAG,GAAE,CAAC,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,CAAC,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,CAAC,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,CAAC,GAAEC,KAAE,SAASD,IAAEC,IAAE;AAAC,gBAAIC,KAAE,CAAA;AAAG,mBAAOA,GAAE,aAAWF,IAAEE,GAAE,YAAUD,IAAEC;AAAA,UAAC,GAAEA,KAAE,EAAC,aAAY,SAASA,IAAEC,IAAE;AAAC,gBAAIC,KAAE,SAASH,IAAEC,IAAE;AAAC,sBAAOA,IAAG;AAAA,gBAAA,KAAK,EAAE;AAAE,yBAAOF,GAAE,KAAGC,KAAE,KAAG,CAAC;AAAA,gBAAE,KAAK,EAAE;AAAE,yBAAOD,GAAE,KAAGC,KAAE,KAAG,CAAC;AAAA,gBAAE,KAAK,EAAE;AAAE,yBAAOD,GAAE,KAAGC,KAAE,KAAG,CAAC;AAAA,gBAAE,KAAK,EAAE;AAAE,yBAAOD,GAAE,KAAGC,KAAE,KAAG,CAAC;AAAA,gBAAE;AAAQ;AAAA,cAAM;AAAA,YAAC,EAAEC,IAAEC,EAAC;AAAE,gBAAG,WAASC,GAAE,OAAK,+BAA6BF,KAAE,2BAAyBC;AAAE,qBAAQE,KAAED,GAAE,SAAO,GAAEc,KAAE,CAAA,GAAGZ,KAAE,GAAEA,KAAED,IAAEC,MAAG,EAAE,UAAQI,KAAEN,GAAE,IAAEE,KAAE,CAAC,GAAEK,KAAEP,GAAE,IAAEE,KAAE,CAAC,GAAEM,KAAER,GAAE,IAAEE,KAAE,CAAC,GAAEO,KAAE,GAAEA,KAAEH,IAAEG,MAAG,EAAE,CAAAK,GAAE,KAAKjB,GAAEU,IAAEC,EAAC,CAAC;AAAE,mBAAOM;AAAA,UAAC,EAAC;AAAE,iBAAOhB;AAAA,QAAC,EAAC,GAAG,IAAE,WAAU;AAAC,cAAIF,KAAE,IAAGC,KAAE,GAAEC,KAAE,EAAC,WAAU,WAAU;AAAC,mBAAOF;AAAA,UAAC,GAAE,OAAM,SAASC,IAAE;AAAC,gBAAIC,KAAE,KAAK,MAAMD,KAAE,CAAC;AAAE,mBAAO,MAAID,GAAEE,EAAC,MAAI,IAAED,KAAE,IAAE;AAAA,UAAE,GAAE,KAAI,SAASD,IAAEC,IAAE;AAAC,qBAAQE,KAAE,GAAEA,KAAEF,IAAEE,MAAG,EAAE,CAAAD,GAAE,OAAO,MAAIF,OAAIC,KAAEE,KAAE,IAAE,EAAE;AAAA,UAAC,GAAE,iBAAgB,WAAU;AAAC,mBAAOF;AAAA,UAAC,GAAE,QAAO,SAASC,IAAE;AAAC,gBAAIC,KAAE,KAAK,MAAMF,KAAE,CAAC;AAAE,YAAAD,GAAE,UAAQG,MAAGH,GAAE,KAAK,CAAC,GAAEE,OAAIF,GAAEG,EAAC,KAAG,QAAMF,KAAE,IAAGA,MAAG;AAAA,UAAC,EAAC;AAAE,iBAAOC;AAAA,QAAC,GAAE,IAAE,SAASF,IAAE;AAAC,cAAIC,KAAED,IAAEE,KAAE,EAAC,SAAQ,WAAU;AAAC,mBAAO;AAAA,UAAC,GAAE,WAAU,SAASF,IAAE;AAAC,mBAAOC,GAAE;AAAA,UAAM,GAAE,OAAM,SAASD,IAAE;AAAC,qBAAQE,KAAED,IAAEG,KAAE,GAAEA,KAAE,IAAEF,GAAE,SAAQ,CAAAF,GAAE,IAAIG,GAAED,GAAE,UAAUE,IAAEA,KAAE,CAAC,CAAC,GAAE,EAAE,GAAEA,MAAG;AAAE,YAAAA,KAAEF,GAAE,WAASA,GAAE,SAAOE,MAAG,IAAEJ,GAAE,IAAIG,GAAED,GAAE,UAAUE,IAAEA,KAAE,CAAC,CAAC,GAAE,CAAC,IAAEF,GAAE,SAAOE,MAAG,KAAGJ,GAAE,IAAIG,GAAED,GAAE,UAAUE,IAAEA,KAAE,CAAC,CAAC,GAAE,CAAC;AAAA,UAAE,EAAC,GAAED,KAAE,SAASH,IAAE;AAAC,qBAAQC,KAAE,GAAEC,KAAE,GAAEA,KAAEF,GAAE,QAAOE,MAAG,EAAE,CAAAD,KAAE,KAAGA,KAAEG,GAAEJ,GAAE,OAAOE,EAAC,CAAC;AAAE,mBAAOD;AAAA,UAAC,GAAEG,KAAE,SAASJ,IAAE;AAAC,gBAAG,OAAKA,MAAGA,MAAG,IAAI,QAAOA,GAAE,WAAW,CAAC,IAAE,IAAI,WAAW,CAAC;AAAE,kBAAK,mBAAiBA;AAAA,UAAC;AAAE,iBAAOE;AAAA,QAAC,GAAE,IAAE,SAASF,IAAE;AAAC,cAAIC,KAAED,IAAEE,KAAE,EAAC,SAAQ,WAAU;AAAC,mBAAO;AAAA,UAAC,GAAE,WAAU,SAASF,IAAE;AAAC,mBAAOC,GAAE;AAAA,UAAM,GAAE,OAAM,SAASD,IAAE;AAAC,qBAAQE,KAAED,IAAEG,KAAE,GAAEA,KAAE,IAAEF,GAAE,SAAQ,CAAAF,GAAE,IAAI,KAAGG,GAAED,GAAE,OAAOE,EAAC,CAAC,IAAED,GAAED,GAAE,OAAOE,KAAE,CAAC,CAAC,GAAE,EAAE,GAAEA,MAAG;AAAE,YAAAA,KAAEF,GAAE,UAAQF,GAAE,IAAIG,GAAED,GAAE,OAAOE,EAAC,CAAC,GAAE,CAAC;AAAA,UAAC,EAAC,GAAED,KAAE,SAASH,IAAE;AAAC,gBAAG,OAAKA,MAAGA,MAAG,IAAI,QAAOA,GAAE,WAAW,CAAC,IAAE,IAAI,WAAW,CAAC;AAAE,gBAAG,OAAKA,MAAGA,MAAG,IAAI,QAAOA,GAAE,WAAW,CAAC,IAAE,IAAI,WAAW,CAAC,IAAE;AAAG,oBAAOA,IAAG;AAAA,cAAA,KAAI;AAAI,uBAAO;AAAA,cAAG,KAAI;AAAI,uBAAO;AAAA,cAAG,KAAI;AAAI,uBAAO;AAAA,cAAG,KAAI;AAAI,uBAAO;AAAA,cAAG,KAAI;AAAI,uBAAO;AAAA,cAAG,KAAI;AAAI,uBAAO;AAAA,cAAG,KAAI;AAAI,uBAAO;AAAA,cAAG,KAAI;AAAI,uBAAO;AAAA,cAAG,KAAI;AAAI,uBAAO;AAAA,cAAG;AAAQ,sBAAK,mBAAiBA;AAAA,YAAC;AAAA,UAAC;AAAE,iBAAOE;AAAA,QAAC,GAAE,IAAE,SAASD,IAAE;AAAC,cAAIC,KAAEF,GAAE,cAAcC,EAAC;AAAE,iBAAM,EAAC,SAAQ,WAAU;AAAC,mBAAO;AAAA,UAAC,GAAE,WAAU,SAASD,IAAE;AAAC,mBAAOE,GAAE;AAAA,UAAM,GAAE,OAAM,SAASF,IAAE;AAAC,qBAAQC,KAAE,GAAEA,KAAEC,GAAE,QAAOD,MAAG,EAAE,CAAAD,GAAE,IAAIE,GAAED,EAAC,GAAE,CAAC;AAAA,UAAC,EAAC;AAAA,QAAC,GAAE,IAAE,SAASA,IAAE;AAAC,cAAIC,KAAEF,GAAE,mBAAmB;AAAK,cAAG,CAACE,GAAE,OAAK;AAAsB,WAAC,WAAU;AAAC,gBAAIF,KAAEE,GAAE,GAAG;AAAE,gBAAG,KAAGF,GAAE,UAAQ,UAAQA,GAAE,CAAC,KAAG,IAAEA,GAAE,CAAC,GAAG,OAAK;AAAA,UAAqB,EAAC;AAAG,cAAIG,KAAED,GAAED,EAAC,GAAEG,KAAE,EAAC,SAAQ,WAAU;AAAC,mBAAO;AAAA,UAAC,GAAE,WAAU,SAASJ,IAAE;AAAC,mBAAM,CAAC,EAAEG,GAAE,SAAO;AAAA,UAAE,GAAE,OAAM,SAASH,IAAE;AAAC,qBAAQC,KAAEE,IAAED,KAAE,GAAEA,KAAE,IAAED,GAAE,UAAQ;AAAC,kBAAIG,MAAG,MAAIH,GAAEC,EAAC,MAAI,IAAE,MAAID,GAAEC,KAAE,CAAC;AAAE,kBAAG,SAAOE,MAAGA,MAAG,MAAM,CAAAA,MAAG;AAAA,mBAAU;AAAC,oBAAG,EAAE,SAAOA,MAAGA,MAAG,OAAO,OAAK,sBAAoBF,KAAE,KAAG,MAAIE;AAAE,gBAAAA,MAAG;AAAA,cAAK;AAAC,cAAAA,KAAE,OAAKA,OAAI,IAAE,QAAM,MAAIA,KAAGJ,GAAE,IAAII,IAAE,EAAE,GAAEF,MAAG;AAAA,YAAC;AAAC,gBAAGA,KAAED,GAAE,OAAO,OAAK,sBAAoBC,KAAE;AAAA,UAAE,EAAC;AAAE,iBAAOE;AAAA,QAAC,GAAE,IAAE,WAAU;AAAC,cAAIJ,KAAE,CAAA,GAAGC,KAAE,EAAC,WAAU,SAASA,IAAE;AAAC,YAAAD,GAAE,KAAK,MAAIC,EAAC;AAAA,UAAC,GAAE,YAAW,SAASD,IAAE;AAAC,YAAAC,GAAE,UAAUD,EAAC,GAAEC,GAAE,UAAUD,OAAI,CAAC;AAAA,UAAC,GAAE,YAAW,SAASA,IAAEE,IAAEC,IAAE;AAAC,YAAAD,KAAEA,MAAG,GAAEC,KAAEA,MAAGH,GAAE;AAAO,qBAAQI,KAAE,GAAEA,KAAED,IAAEC,MAAG,EAAE,CAAAH,GAAE,UAAUD,GAAEI,KAAEF,EAAC,CAAC;AAAA,UAAC,GAAE,aAAY,SAASF,IAAE;AAAC,qBAAQE,KAAE,GAAEA,KAAEF,GAAE,QAAOE,MAAG,EAAE,CAAAD,GAAE,UAAUD,GAAE,WAAWE,EAAC,CAAC;AAAA,UAAC,GAAE,aAAY,WAAU;AAAC,mBAAOF;AAAA,UAAC,GAAE,UAAS,WAAU;AAAC,gBAAIC,KAAE;AAAG,YAAAA,MAAG;AAAI,qBAAQC,KAAE,GAAEA,KAAEF,GAAE,QAAOE,MAAG,EAAE,CAAAA,KAAE,MAAID,MAAG,MAAKA,MAAGD,GAAEE,EAAC;AAAE,mBAAOD,KAAE;AAAA,UAAG,EAAC;AAAE,iBAAOA;AAAA,QAAC,GAAE,IAAE,SAASD,IAAE;AAAC,cAAIC,KAAED,IAAEE,KAAE,GAAEC,KAAE,GAAEC,KAAE,GAAEC,KAAE,EAAC,MAAK,WAAU;AAAC,mBAAKD,KAAE,KAAG;AAAC,kBAAGF,MAAGD,GAAE,QAAO;AAAC,oBAAG,KAAGG,GAAE;AAAS,sBAAK,6BAA2BA;AAAA,cAAC;AAAC,kBAAIJ,KAAEC,GAAE,OAAOC,EAAC;AAAE,kBAAGA,MAAG,GAAE,OAAKF,GAAE,QAAOI,KAAE,GAAE;AAAG,cAAAJ,GAAE,MAAM,MAAM,MAAIG,KAAEA,MAAG,IAAEM,GAAET,GAAE,WAAW,CAAC,CAAC,GAAEI,MAAG;AAAA,YAAE;AAAC,gBAAIC,KAAEF,OAAIC,KAAE,IAAE;AAAI,mBAAOA,MAAG,GAAEC;AAAA,UAAC,EAAC,GAAEI,KAAE,SAAST,IAAE;AAAC,gBAAG,MAAIA,MAAGA,MAAG,GAAG,QAAOA,KAAE;AAAG,gBAAG,MAAIA,MAAGA,MAAG,IAAI,QAAOA,KAAE,KAAG;AAAG,gBAAG,MAAIA,MAAGA,MAAG,GAAG,QAAOA,KAAE,KAAG;AAAG,gBAAG,MAAIA,GAAE,QAAO;AAAG,gBAAG,MAAIA,GAAE,QAAO;AAAG,kBAAK,OAAKA;AAAA,UAAC;AAAE,iBAAOK;AAAA,QAAC,GAAE,IAAE,SAASL,IAAEC,IAAEC,IAAE;AAAC,mBAAQC,KAAE,SAASH,IAAEC,IAAE;AAAC,gBAAIC,KAAEF,IAAEG,KAAEF,IAAEG,KAAE,IAAI,MAAMJ,KAAEC,EAAC,GAAEI,KAAE,EAAC,UAAS,SAASL,IAAEC,IAAEE,IAAE;AAAC,cAAAC,GAAEH,KAAEC,KAAEF,EAAC,IAAEG;AAAA,YAAC,GAAE,OAAM,SAASH,IAAE;AAAC,cAAAA,GAAE,YAAY,QAAQ,GAAEA,GAAE,WAAWE,EAAC,GAAEF,GAAE,WAAWG,EAAC,GAAEH,GAAE,UAAU,GAAG,GAAEA,GAAE,UAAU,CAAC,GAAEA,GAAE,UAAU,CAAC,GAAEA,GAAE,UAAU,CAAC,GAAEA,GAAE,UAAU,CAAC,GAAEA,GAAE,UAAU,CAAC,GAAEA,GAAE,UAAU,GAAG,GAAEA,GAAE,UAAU,GAAG,GAAEA,GAAE,UAAU,GAAG,GAAEA,GAAE,YAAY,GAAG,GAAEA,GAAE,WAAW,CAAC,GAAEA,GAAE,WAAW,CAAC,GAAEA,GAAE,WAAWE,EAAC,GAAEF,GAAE,WAAWG,EAAC,GAAEH,GAAE,UAAU,CAAC;AAAE,kBAAIC,KAAEQ,GAAE,CAAC;AAAE,cAAAT,GAAE,UAAU,CAAC;AAAE,uBAAQI,KAAE,GAAEH,GAAE,SAAOG,KAAE,MAAK,CAAAJ,GAAE,UAAU,GAAG,GAAEA,GAAE,WAAWC,IAAEG,IAAE,GAAG,GAAEA,MAAG;AAAI,cAAAJ,GAAE,UAAUC,GAAE,SAAOG,EAAC,GAAEJ,GAAE,WAAWC,IAAEG,IAAEH,GAAE,SAAOG,EAAC,GAAEJ,GAAE,UAAU,CAAC,GAAEA,GAAE,YAAY,GAAG;AAAA,YAAC,EAAC,GAAES,KAAE,SAAST,IAAE;AAAC,uBAAQC,KAAE,KAAGD,IAAEE,KAAE,KAAG,KAAGF,KAAGG,KAAEH,KAAE,GAAEK,KAAEa,GAAG,GAACT,KAAE,GAAEA,KAAER,IAAEQ,MAAG,EAAE,CAAAJ,GAAE,IAAI,OAAO,aAAaI,EAAC,CAAC;AAAE,cAAAJ,GAAE,IAAI,OAAO,aAAaJ,EAAC,CAAC,GAAEI,GAAE,IAAI,OAAO,aAAaH,EAAC,CAAC;AAAE,kBAAII,IAAEI,IAAEC,IAAEC,KAAE,EAAC,GAAGC,MAAGP,KAAEM,IAAEF,KAAE,GAAEC,KAAE,GAAE,EAAC,OAAM,SAASX,IAAEC,IAAE;AAAC,oBAAGD,OAAIC,MAAG,EAAE,OAAK;AAAc,uBAAKS,KAAET,MAAG,IAAG,CAAAK,GAAE,UAAU,OAAKN,MAAGU,KAAEC,GAAE,GAAEV,MAAG,IAAES,IAAEV,QAAK,IAAEU,IAAEC,KAAE,GAAED,KAAE;AAAE,gBAAAC,MAAGX,MAAGU,IAAEA,MAAGT;AAAA,cAAC,GAAE,OAAM,WAAU;AAAC,gBAAAS,KAAE,KAAGJ,GAAE,UAAUK,EAAC;AAAA,cAAC,EAAC;AAAG,cAAAE,GAAE,MAAMZ,IAAEE,EAAC;AAAE,kBAAIW,KAAE,GAAEC,KAAE,OAAO,aAAaX,GAAEU,EAAC,CAAC;AAAE,mBAAIA,MAAG,GAAEA,KAAEV,GAAE,UAAQ;AAAC,oBAAIY,KAAE,OAAO,aAAaZ,GAAEU,EAAC,CAAC;AAAE,gBAAAA,MAAG,GAAET,GAAE,SAASU,KAAEC,EAAC,IAAED,MAAGC,MAAGH,GAAE,MAAMR,GAAE,QAAQU,EAAC,GAAEZ,EAAC,GAAEE,GAAE,SAAO,SAAOA,GAAE,KAAI,KAAI,KAAGF,OAAIA,MAAG,IAAGE,GAAE,IAAIU,KAAEC,EAAC,IAAGD,KAAEC;AAAA,cAAE;AAAC,qBAAOH,GAAE,MAAMR,GAAE,QAAQU,EAAC,GAAEZ,EAAC,GAAEU,GAAE,MAAMX,IAAEC,EAAC,GAAEU,GAAE,MAAO,GAACD,GAAE,YAAa;AAAA,YAAA,GAAEM,KAAE,WAAU;AAAC,kBAAIlB,KAAE,IAAGC,KAAE,GAAEC,KAAE,EAAC,KAAI,SAASC,IAAE;AAAC,oBAAGD,GAAE,SAASC,EAAC,EAAE,OAAK,aAAWA;AAAE,gBAAAH,GAAEG,EAAC,IAAEF,IAAEA,MAAG;AAAA,cAAC,GAAE,MAAK,WAAU;AAAC,uBAAOA;AAAA,cAAC,GAAE,SAAQ,SAASA,IAAE;AAAC,uBAAOD,GAAEC,EAAC;AAAA,cAAC,GAAE,UAAS,SAASA,IAAE;AAAC,uBAAO,WAASD,GAAEC,EAAC;AAAA,cAAC,EAAC;AAAE,qBAAOC;AAAA,YAAC;AAAE,mBAAOG;AAAA,UAAC,EAAEL,IAAEC,EAAC,GAAEG,KAAE,GAAEA,KAAEH,IAAEG,MAAG,EAAE,UAAQC,KAAE,GAAEA,KAAEL,IAAEK,MAAG,EAAE,CAAAF,GAAE,SAASE,IAAED,IAAEF,GAAEG,IAAED,EAAC,CAAC;AAAE,cAAIK,KAAE,EAAC;AAAG,UAAAN,GAAE,MAAMM,EAAC;AAAE,mBAAQS,KAAE,WAAU;AAAC,gBAAIlB,KAAE,GAAEC,KAAE,GAAEC,KAAE,GAAEC,KAAE,IAAGC,KAAE,CAAE,GAACC,KAAE,SAASL,IAAE;AAAC,cAAAG,MAAG,OAAO,aAAaM,GAAE,KAAGT,EAAC,CAAC;AAAA,YAAC,GAAES,KAAE,SAAST,IAAE;AAAC,kBAAGA,KAAE,EAAE;AAAA,mBAAK;AAAC,oBAAGA,KAAE,GAAG,QAAO,KAAGA;AAAE,oBAAGA,KAAE,GAAG,QAAOA,KAAE,KAAG;AAAG,oBAAGA,KAAE,GAAG,QAAOA,KAAE,KAAG;AAAG,oBAAG,MAAIA,GAAE,QAAO;AAAG,oBAAG,MAAIA,GAAE,QAAO;AAAA,cAAE;AAAC,oBAAK,OAAKA;AAAA,YAAC;AAAE,mBAAOI,GAAE,YAAU,SAASD,IAAE;AAAC,mBAAIH,KAAEA,MAAG,IAAE,MAAIG,IAAEF,MAAG,GAAEC,MAAG,GAAED,MAAG,IAAG,CAAAI,GAAEL,OAAIC,KAAE,CAAC,GAAEA,MAAG;AAAA,YAAC,GAAEG,GAAE,QAAM,WAAU;AAAC,kBAAGH,KAAE,MAAII,GAAEL,MAAG,IAAEC,EAAC,GAAED,KAAE,GAAEC,KAAE,IAAGC,KAAE,KAAG,EAAE,UAAQE,KAAE,IAAEF,KAAE,GAAEO,KAAE,GAAEA,KAAEL,IAAEK,MAAG,EAAE,CAAAN,MAAG;AAAA,YAAG,GAAEC,GAAE,WAAS,WAAU;AAAC,qBAAOD;AAAA,YAAC,GAAEC;AAAA,UAAC,EAAG,GAACE,KAAEG,GAAE,YAAW,GAAGC,KAAE,GAAEA,KAAEJ,GAAE,QAAOI,MAAG,EAAE,CAAAQ,GAAE,UAAUZ,GAAEI,EAAC,CAAC;AAAE,iBAAOQ,GAAE,MAAK,GAAG,2BAAyBA;AAAA,QAAC;AAAE,eAAOlB;AAAA,MAAC,EAAC;AAAG,QAAE,mBAAmB,OAAO,IAAE,SAASA,IAAE;AAAC,eAAO,SAASA,IAAE;AAAC,mBAAQC,KAAE,CAAA,GAAGC,KAAE,GAAEA,KAAEF,GAAE,QAAOE,MAAI;AAAC,gBAAIC,KAAEH,GAAE,WAAWE,EAAC;AAAE,YAAAC,KAAE,MAAIF,GAAE,KAAKE,EAAC,IAAEA,KAAE,OAAKF,GAAE,KAAK,MAAIE,MAAG,GAAE,MAAI,KAAGA,EAAC,IAAEA,KAAE,SAAOA,MAAG,QAAMF,GAAE,KAAK,MAAIE,MAAG,IAAG,MAAIA,MAAG,IAAE,IAAG,MAAI,KAAGA,EAAC,KAAGD,MAAIC,KAAE,UAAQ,OAAKA,OAAI,KAAG,OAAKH,GAAE,WAAWE,EAAC,IAAGD,GAAE,KAAK,MAAIE,MAAG,IAAG,MAAIA,MAAG,KAAG,IAAG,MAAIA,MAAG,IAAE,IAAG,MAAI,KAAGA,EAAC;AAAA,UAAE;AAAC,iBAAOF;AAAA,QAAC,EAAED,EAAC;AAAA,MAAC,GAAE,YAAUG,KAAE,cAAY,QAAOD,KAAE,WAAU;AAAC,eAAO;AAAA,MAAC,KAAGA,GAAE,MAAMD,IAAE,CAAA,CAAE,IAAEC,QAAKF,GAAE,UAAQG;AAAA,IAAE,EAAC,GAAE,IAAE,CAAE;AAAC,aAAS,EAAEA,IAAE;AAAC,UAAI,IAAE,EAAEA,EAAC;AAAE,UAAG,WAAS,EAAE,QAAO,EAAE;AAAQ,UAAI,IAAE,EAAEA,EAAC,IAAE,EAAC,SAAQ,CAAE,EAAA;AAAE,aAAO,EAAEA,EAAC,EAAE,GAAE,EAAE,SAAQ,CAAC,GAAE,EAAE;AAAA,IAAO;AAAC,MAAE,IAAE,CAAAH,OAAG;AAAC,UAAIC,KAAED,MAAGA,GAAE,aAAW,MAAIA,GAAE,UAAQ,MAAIA;AAAE,aAAO,EAAE,EAAEC,IAAE,EAAC,GAAEA,GAAC,CAAC,GAAEA;AAAA,IAAC,GAAE,EAAE,IAAE,CAACD,IAAEC,OAAI;AAAC,eAAQE,MAAKF,GAAE,GAAE,EAAEA,IAAEE,EAAC,KAAG,CAAC,EAAE,EAAEH,IAAEG,EAAC,KAAG,OAAO,eAAeH,IAAEG,IAAE,EAAC,YAAW,MAAG,KAAIF,GAAEE,EAAC,EAAC,CAAC;AAAA,IAAC,GAAE,EAAE,IAAE,CAACH,IAAEC,OAAI,OAAO,UAAU,eAAe,KAAKD,IAAEC,EAAC;AAAE,QAAI,IAAE,CAAE;AAAC,YAAO,MAAI;AAAc,QAAE,EAAE,GAAE,EAAC,SAAQ,MAAI,EAAC,CAAC;AAAE,YAAMD,KAAE,CAAAA,OAAG,CAAC,CAACA,MAAG,YAAU,OAAOA,MAAG,CAAC,MAAM,QAAQA,EAAC;AAAE,eAASC,GAAEC,OAAKC,IAAE;AAAC,YAAG,CAACA,GAAE,OAAO,QAAOD;AAAE,cAAME,KAAED,GAAE,MAAK;AAAG,eAAO,WAASC,MAAGJ,GAAEE,EAAC,KAAGF,GAAEI,EAAC,KAAGF,KAAE,OAAO,OAAO,IAAGA,EAAC,GAAE,OAAO,KAAKE,EAAC,EAAE,QAAS,CAAAD,OAAG;AAAC,gBAAME,KAAEH,GAAEC,EAAC,GAAEM,KAAEL,GAAED,EAAC;AAAE,gBAAM,QAAQE,EAAC,KAAG,MAAM,QAAQI,EAAC,IAAEP,GAAEC,EAAC,IAAEM,KAAET,GAAEK,EAAC,KAAGL,GAAES,EAAC,IAAEP,GAAEC,EAAC,IAAEF,GAAE,OAAO,OAAO,CAAA,GAAGI,EAAC,GAAEI,EAAC,IAAEP,GAAEC,EAAC,IAAEM;AAAA,QAAC,CAAG,GAACR,GAAEC,IAAE,GAAGC,EAAC,KAAGD;AAAA,MAAC;AAAC,eAAS,EAAEF,IAAEC,IAAE;AAAC,cAAMC,KAAE,SAAS,cAAc,GAAG;AAAE,QAAAA,GAAE,WAASD,IAAEC,GAAE,OAAKF,IAAE,SAAS,KAAK,YAAYE,EAAC,GAAEA,GAAE,MAAO,GAAC,SAAS,KAAK,YAAYA,EAAC;AAAA,MAAC;AAAC,YAAM,IAAE,EAAC,GAAE,MAAI,GAAE,MAAI,GAAE,MAAI,GAAE,IAAE;AAAA,MAAE,MAAM,EAAC;AAAA,QAAC,YAAY,EAAC,KAAIF,IAAE,MAAKC,IAAE,QAAOC,GAAC,GAAE;AAAC,eAAK,OAAKF,IAAE,KAAK,QAAMC,IAAE,KAAK,UAAQC;AAAA,QAAC;AAAA,QAAC,KAAKF,IAAEC,IAAEC,IAAEC,IAAE;AAAC,cAAIC;AAAE,kBAAO,KAAK;YAAO,KAAI;AAAO,cAAAA,KAAE,KAAK;AAAS;AAAA,YAAM,KAAI;AAAS,cAAAA,KAAE,KAAK;AAAY;AAAA,YAAM,KAAI;AAAiB,cAAAA,KAAE,KAAK;AAAmB;AAAA,YAAM,KAAI;AAAU,cAAAA,KAAE,KAAK;AAAa;AAAA,YAAM,KAAI;AAAgB,cAAAA,KAAE,KAAK;AAAkB;AAAA,YAAM;AAAQ,cAAAA,KAAE,KAAK;AAAA,UAAW;AAAC,UAAAA,GAAE,KAAK,MAAK,EAAC,GAAEJ,IAAE,GAAEC,IAAE,MAAKC,IAAE,aAAYC,GAAC,CAAC;AAAA,QAAC;AAAA,QAAC,cAAc,EAAC,GAAEH,IAAE,GAAEC,IAAE,MAAKC,IAAE,UAASC,KAAE,GAAE,MAAKC,GAAC,GAAE;AAAC,cAAIC;AAAE,gBAAMI,KAAET,KAAEE,KAAE,GAAEgB,KAAEjB,KAAEC,KAAE;AAAE,UAAAE,GAAG,GAAC,UAAQC,KAAE,KAAK,aAAW,WAASA,MAAGA,GAAE,aAAa,aAAY,UAAU,MAAIF,KAAE,KAAK,EAAE,IAAIM,EAAC,IAAIS,EAAC,GAAG;AAAA,QAAC;AAAA,QAAC,UAAUlB,IAAE;AAAC,gBAAK,EAAC,MAAKC,IAAE,GAAEC,IAAE,GAAEC,GAAC,IAAEH;AAAE,eAAK,cAAc,OAAO,OAAO,OAAO,OAAO,CAAE,GAACA,EAAC,GAAE,EAAC,MAAK,MAAI;AAAC,iBAAK,WAAS,KAAK,QAAQ,SAAS,gBAAgB,8BAA6B,QAAQ,GAAE,KAAK,SAAS,aAAa,MAAK,OAAOE,KAAED,KAAE,CAAC,CAAC,GAAE,KAAK,SAAS,aAAa,MAAK,OAAOE,KAAEF,KAAE,CAAC,CAAC,GAAE,KAAK,SAAS,aAAa,KAAI,OAAOA,KAAE,CAAC,CAAC;AAAA,UAAC,EAAC,CAAC,CAAC;AAAA,QAAC;AAAA,QAAC,aAAaD,IAAE;AAAC,gBAAK,EAAC,MAAKC,IAAE,GAAEC,IAAE,GAAEC,GAAC,IAAEH;AAAE,eAAK,cAAc,OAAO,OAAO,OAAO,OAAO,IAAGA,EAAC,GAAE,EAAC,MAAK,MAAI;AAAC,iBAAK,WAAS,KAAK,QAAQ,SAAS,gBAAgB,8BAA6B,MAAM,GAAE,KAAK,SAAS,aAAa,KAAI,OAAOE,EAAC,CAAC,GAAE,KAAK,SAAS,aAAa,KAAI,OAAOC,EAAC,CAAC,GAAE,KAAK,SAAS,aAAa,SAAQ,OAAOF,EAAC,CAAC,GAAE,KAAK,SAAS,aAAa,UAAS,OAAOA,EAAC,CAAC;AAAA,UAAC,EAAC,CAAC,CAAC;AAAA,QAAC;AAAA,QAAC,kBAAkBD,IAAE;AAAC,gBAAK,EAAC,MAAKC,IAAE,GAAEC,IAAE,GAAEC,GAAC,IAAEH;AAAE,eAAK,cAAc,OAAO,OAAO,OAAO,OAAO,CAAA,GAAGA,EAAC,GAAE,EAAC,MAAK,MAAI;AAAC,iBAAK,WAAS,KAAK,QAAQ,SAAS,gBAAgB,8BAA6B,MAAM,GAAE,KAAK,SAAS,aAAa,KAAI,KAAKE,EAAC,IAAIC,EAAC,KAAKF,EAAC,OAAKA,KAAE,IAAE,KAAKA,KAAE,CAAC,IAAIA,KAAE,CAAC,gBAAgB,CAACA,EAAC,EAAE;AAAA,UAAC,EAAC,CAAC,CAAC;AAAA,QAAC;AAAA,QAAC,oBAAoBD,IAAE;AAAC,gBAAK,EAAC,MAAKC,IAAE,GAAEC,IAAE,GAAEC,GAAC,IAAEH;AAAE,eAAK,cAAc,OAAO,OAAO,OAAO,OAAO,IAAGA,EAAC,GAAE,EAAC,MAAK,MAAI;AAAC,iBAAK,WAAS,KAAK,QAAQ,SAAS,gBAAgB,8BAA6B,MAAM,GAAE,KAAK,SAAS,aAAa,KAAI,KAAKE,EAAC,IAAIC,EAAC,KAAKF,EAAC,KAAKA,EAAC,OAAK,CAACA,KAAE,IAAE,KAAKA,KAAE,CAAC,IAAIA,KAAE,CAAC,cAAc,CAACA,KAAE,CAAC,IAAI,CAACA,KAAE,CAAC,EAAE;AAAA,UAAC,EAAC,CAAC,CAAC;AAAA,QAAC;AAAA,QAAC,yBAAyBD,IAAE;AAAC,gBAAK,EAAC,MAAKC,IAAE,GAAEC,IAAE,GAAEC,GAAC,IAAEH;AAAE,eAAK,cAAc,OAAO,OAAO,OAAO,OAAO,CAAA,GAAGA,EAAC,GAAE,EAAC,MAAK,MAAI;AAAC,iBAAK,WAAS,KAAK,QAAQ,SAAS,gBAAgB,8BAA6B,MAAM,GAAE,KAAK,SAAS,aAAa,KAAI,KAAKE,EAAC,IAAIC,EAAC,KAAKF,EAAC,KAAKA,EAAC,KAAKA,EAAC,IAAIA,EAAC,cAAc,CAACA,EAAC,IAAI,CAACA,EAAC,EAAE;AAAA,UAAC,EAAC,CAAC,CAAC;AAAA,QAAC;AAAA,QAAC,qBAAqBD,IAAE;AAAC,gBAAK,EAAC,MAAKC,IAAE,GAAEC,IAAE,GAAEC,GAAC,IAAEH;AAAE,eAAK,cAAc,OAAO,OAAO,OAAO,OAAO,CAAA,GAAGA,EAAC,GAAE,EAAC,MAAK,MAAI;AAAC,iBAAK,WAAS,KAAK,QAAQ,SAAS,gBAAgB,8BAA6B,MAAM,GAAE,KAAK,SAAS,aAAa,KAAI,KAAKE,EAAC,IAAIC,EAAC,OAAKF,KAAE,IAAE,KAAKA,KAAE,CAAC,IAAIA,KAAE,CAAC,cAAcA,KAAE,CAAC,IAAIA,KAAE,CAAC,OAAKA,KAAE,IAAE,OAAK,CAACA,KAAE,IAAE,KAAKA,KAAE,CAAC,IAAIA,KAAE,CAAC,cAAc,CAACA,KAAE,CAAC,IAAI,CAACA,KAAE,CAAC,EAAE;AAAA,UAAC,EAAC,CAAC,CAAC;AAAA,QAAC;AAAA,QAAC,SAAS,EAAC,GAAED,IAAE,GAAEC,IAAE,MAAKC,GAAC,GAAE;AAAC,eAAK,UAAU,EAAC,GAAEF,IAAE,GAAEC,IAAE,MAAKC,IAAE,UAAS,EAAC,CAAC;AAAA,QAAC;AAAA,QAAC,YAAY,EAAC,GAAEF,IAAE,GAAEC,IAAE,MAAKC,GAAC,GAAE;AAAC,eAAK,aAAa,EAAC,GAAEF,IAAE,GAAEC,IAAE,MAAKC,IAAE,UAAS,EAAC,CAAC;AAAA,QAAC;AAAA,QAAC,aAAa,EAAC,GAAEF,IAAE,GAAEC,IAAE,MAAKC,IAAE,aAAYC,GAAC,GAAE;AAAC,gBAAMC,KAAED,KAAE,CAACA,GAAE,IAAG,CAAC,IAAE,GAAEE,KAAEF,KAAE,CAACA,GAAE,GAAE,CAAC,IAAE,GAAEM,KAAEN,KAAE,CAACA,GAAE,GAAE,EAAE,IAAE,GAAEe,KAAEf,KAAE,CAACA,GAAE,GAAE,CAAC,IAAE,GAAEG,KAAEF,KAAEC,KAAEI,KAAES;AAAE,cAAG,MAAIZ,GAAE,KAAGA,KAAE,KAAGF,MAAGC,MAAGI,MAAGS,GAAE,MAAK,aAAa,EAAC,GAAElB,IAAE,GAAEC,IAAE,MAAKC,IAAE,UAAS,EAAC,CAAC;AAAA,eAAM;AAAC,gBAAG,MAAII,IAAE;AAAC,kBAAIH,KAAE;AAAE,qBAAOC,MAAGK,KAAEN,KAAE,KAAK,KAAG,IAAEM,MAAGJ,KAAEF,KAAE,KAAK,KAAGE,MAAGa,OAAIf,KAAE,CAAC,KAAK,KAAG,IAAG,KAAK,KAAK,oBAAoB,EAAC,GAAEH,IAAE,GAAEC,IAAE,MAAKC,IAAE,UAASC,GAAC,CAAC;AAAA,YAAC;AAAC,gBAAG,MAAIG,IAAE;AAAC,kBAAIH,KAAE;AAAE,qBAAOM,KAAEN,KAAE,KAAK,KAAG,IAAEE,KAAEF,KAAE,KAAK,KAAGe,OAAIf,KAAE,CAAC,KAAK,KAAG,IAAG,KAAK,KAAK,kBAAkB,EAAC,GAAEH,IAAE,GAAEC,IAAE,MAAKC,IAAE,UAASC,GAAC,CAAC;AAAA,YAAC;AAAA,UAAC;AAAA,cAAM,MAAK,UAAU,EAAC,GAAEH,IAAE,GAAEC,IAAE,MAAKC,IAAE,UAAS,EAAC,CAAC;AAAA,QAAC;AAAA,QAAC,kBAAkB,EAAC,GAAEF,IAAE,GAAEC,IAAE,MAAKC,IAAE,aAAYC,GAAC,GAAE;AAAC,gBAAMC,KAAED,KAAE,CAACA,GAAE,IAAG,CAAC,IAAE,GAAEE,KAAEF,KAAE,CAACA,GAAE,GAAE,CAAC,IAAE,GAAEM,KAAEN,KAAE,CAACA,GAAE,GAAE,EAAE,IAAE,GAAEe,KAAEf,KAAE,CAACA,GAAE,GAAE,CAAC,IAAE,GAAEG,KAAEF,KAAEC,KAAEI,KAAES;AAAE,cAAG,MAAIZ,GAAE,KAAGA,KAAE,KAAGF,MAAGC,MAAGI,MAAGS,GAAE,MAAK,aAAa,EAAC,GAAElB,IAAE,GAAEC,IAAE,MAAKC,IAAE,UAAS,EAAC,CAAC;AAAA,eAAM;AAAC,gBAAG,MAAII,IAAE;AAAC,kBAAIH,KAAE;AAAE,qBAAOC,MAAGK,KAAEN,KAAE,KAAK,KAAG,IAAEM,MAAGJ,KAAEF,KAAE,KAAK,KAAGE,MAAGa,OAAIf,KAAE,CAAC,KAAK,KAAG,IAAG,KAAK,KAAK,yBAAyB,EAAC,GAAEH,IAAE,GAAEC,IAAE,MAAKC,IAAE,UAASC,GAAC,CAAC;AAAA,YAAC;AAAC,gBAAG,MAAIG,IAAE;AAAC,kBAAIH,KAAE;AAAE,qBAAOM,KAAEN,KAAE,KAAK,KAAG,IAAEE,KAAEF,KAAE,KAAK,KAAGe,OAAIf,KAAE,CAAC,KAAK,KAAG,IAAG,KAAK,KAAK,kBAAkB,EAAC,GAAEH,IAAE,GAAEC,IAAE,MAAKC,IAAE,UAASC,GAAC,CAAC;AAAA,YAAC;AAAA,UAAC;AAAA,cAAM,MAAK,UAAU,EAAC,GAAEH,IAAE,GAAEC,IAAE,MAAKC,IAAE,UAAS,EAAC,CAAC;AAAA,QAAC;AAAA,QAAC,YAAY,EAAC,GAAEF,IAAE,GAAEC,IAAE,MAAKC,IAAE,aAAYC,GAAC,GAAE;AAAC,gBAAMC,KAAED,KAAE,CAACA,GAAE,IAAG,CAAC,IAAE,GAAEE,KAAEF,KAAE,CAACA,GAAE,GAAE,CAAC,IAAE,GAAEM,KAAEN,KAAE,CAACA,GAAE,GAAE,EAAE,IAAE,GAAEe,KAAEf,KAAE,CAACA,GAAE,GAAE,CAAC,IAAE;AAAE,gBAAIC,KAAEC,KAAEI,KAAES,KAAEd,MAAGK,KAAEJ,MAAGa,KAAE,KAAK,aAAa,EAAC,GAAElB,IAAE,GAAEC,IAAE,MAAKC,IAAE,UAAS,EAAC,CAAC,IAAE,KAAK,oBAAoB,EAAC,GAAEF,IAAE,GAAEC,IAAE,MAAKC,IAAE,UAAS,KAAK,KAAG,EAAC,CAAC,IAAE,KAAK,oBAAoB,EAAC,GAAEF,IAAE,GAAEC,IAAE,MAAKC,IAAE,UAAS,CAAC,KAAK,KAAG,EAAC,CAAC,IAAE,KAAK,qBAAqB,EAAC,GAAEF,IAAE,GAAEC,IAAE,MAAKC,IAAE,UAAS,KAAK,KAAG,EAAC,CAAC;AAAA,QAAC;AAAA,QAAC,mBAAmB,EAAC,GAAEF,IAAE,GAAEC,IAAE,MAAKC,IAAE,aAAYC,GAAC,GAAE;AAAC,gBAAMC,KAAED,KAAE,CAACA,GAAE,IAAG,CAAC,IAAE,GAAEE,KAAEF,KAAE,CAACA,GAAE,GAAE,CAAC,IAAE,GAAEM,KAAEN,KAAE,CAACA,GAAE,GAAE,EAAE,IAAE,GAAEe,KAAEf,KAAE,CAACA,GAAE,GAAE,CAAC,IAAE;AAAE,gBAAIC,KAAEC,KAAEI,KAAES,KAAEd,MAAGK,KAAEJ,MAAGa,KAAE,KAAK,aAAa,EAAC,GAAElB,IAAE,GAAEC,IAAE,MAAKC,IAAE,UAAS,EAAC,CAAC,IAAE,KAAK,yBAAyB,EAAC,GAAEF,IAAE,GAAEC,IAAE,MAAKC,IAAE,UAAS,KAAK,KAAG,EAAC,CAAC,IAAE,KAAK,yBAAyB,EAAC,GAAEF,IAAE,GAAEC,IAAE,MAAKC,IAAE,UAAS,CAAC,KAAK,KAAG,EAAC,CAAC,IAAE,KAAK,qBAAqB,EAAC,GAAEF,IAAE,GAAEC,IAAE,MAAKC,IAAE,UAAS,KAAK,KAAG,EAAC,CAAC;AAAA,QAAC;AAAA,MAAC;AAAC,YAAM,IAAE,EAAC,KAAI,OAAM,QAAO,UAAS,cAAa,gBAAe,GAAE,IAAE,OAAO,OAAO,CAAC;AAAA,MAAE,MAAM,EAAC;AAAA,QAAC,YAAY,EAAC,KAAIF,IAAE,MAAKC,IAAE,QAAOC,GAAC,GAAE;AAAC,eAAK,OAAKF,IAAE,KAAK,QAAMC,IAAE,KAAK,UAAQC;AAAA,QAAC;AAAA,QAAC,KAAKF,IAAEC,IAAEC,IAAEC,IAAE;AAAC,cAAIC;AAAE,kBAAO,KAAK,OAAK;AAAA,YAAE,KAAK,EAAE;AAAO,cAAAA,KAAE,KAAK;AAAY;AAAA,YAAM,KAAK,EAAE;AAAa,cAAAA,KAAE,KAAK;AAAkB;AAAA,YAAM;AAAQ,cAAAA,KAAE,KAAK;AAAA,UAAQ;AAAC,UAAAA,GAAE,KAAK,MAAK,EAAC,GAAEJ,IAAE,GAAEC,IAAE,MAAKC,IAAE,UAASC,GAAC,CAAC;AAAA,QAAC;AAAA,QAAC,cAAc,EAAC,GAAEH,IAAE,GAAEC,IAAE,MAAKC,IAAE,UAASC,KAAE,GAAE,MAAKC,GAAC,GAAE;AAAC,cAAIC;AAAE,gBAAMI,KAAET,KAAEE,KAAE,GAAEgB,KAAEjB,KAAEC,KAAE;AAAE,UAAAE,GAAG,GAAC,UAAQC,KAAE,KAAK,aAAW,WAASA,MAAGA,GAAE,aAAa,aAAY,UAAU,MAAIF,KAAE,KAAK,EAAE,IAAIM,EAAC,IAAIS,EAAC,GAAG;AAAA,QAAC;AAAA,QAAC,UAAUlB,IAAE;AAAC,gBAAK,EAAC,MAAKC,IAAE,GAAEC,IAAE,GAAEC,GAAC,IAAEH,IAAEI,KAAEH,KAAE;AAAE,eAAK,cAAc,OAAO,OAAO,OAAO,OAAO,CAAA,GAAGD,EAAC,GAAE,EAAC,MAAK,MAAI;AAAC,iBAAK,WAAS,KAAK,QAAQ,SAAS,gBAAgB,8BAA6B,MAAM,GAAE,KAAK,SAAS,aAAa,aAAY,SAAS,GAAE,KAAK,SAAS,aAAa,KAAI,KAAKE,KAAED,KAAE,CAAC,IAAIE,EAAC,KAAKF,KAAE,CAAC,IAAIA,KAAE,CAAC,oBAAoBG,EAAC,KAAKH,KAAE,IAAEG,EAAC,IAAIH,KAAE,IAAEG,EAAC,gBAAgB;AAAA,UAAC,EAAC,CAAC,CAAC;AAAA,QAAC;AAAA,QAAC,aAAaJ,IAAE;AAAC,gBAAK,EAAC,MAAKC,IAAE,GAAEC,IAAE,GAAEC,GAAC,IAAEH,IAAEI,KAAEH,KAAE;AAAE,eAAK,cAAc,OAAO,OAAO,OAAO,OAAO,CAAE,GAACD,EAAC,GAAE,EAAC,MAAK,MAAI;AAAC,iBAAK,WAAS,KAAK,QAAQ,SAAS,gBAAgB,8BAA6B,MAAM,GAAE,KAAK,SAAS,aAAa,aAAY,SAAS,GAAE,KAAK,SAAS,aAAa,KAAI,KAAKE,EAAC,IAAIC,EAAC,KAAKF,EAAC,KAAKA,EAAC,OAAK,CAACA,KAAE,MAASC,KAAEE,EAAC,IAAID,KAAEC,EAAC,QAAMH,KAAE,IAAEG,MAAG,QAAMH,KAAE,IAAEG,MAAG,QAAM,IAAEA,KAAEH,MAAG,GAAG;AAAA,UAAC,EAAC,CAAC,CAAC;AAAA,QAAC;AAAA,QAAC,mBAAmBD,IAAE;AAAC,gBAAK,EAAC,MAAKC,IAAE,GAAEC,IAAE,GAAEC,GAAC,IAAEH,IAAEI,KAAEH,KAAE;AAAE,eAAK,cAAc,OAAO,OAAO,OAAO,OAAO,CAAE,GAACD,EAAC,GAAE,EAAC,MAAK,MAAI;AAAC,iBAAK,WAAS,KAAK,QAAQ,SAAS,gBAAgB,8BAA6B,MAAM,GAAE,KAAK,SAAS,aAAa,aAAY,SAAS,GAAE,KAAK,SAAS,aAAa,KAAI,KAAKE,EAAC,IAAIC,KAAE,MAAIC,EAAC,OAAK,IAAEA,KAAE,KAAK,MAAIA,EAAC,IAAI,MAAIA,EAAC,cAAc,MAAIA,EAAC,IAAI,MAAIA,EAAC,OAAK,IAAEA,KAAE,KAAK,MAAIA,EAAC,IAAI,MAAIA,EAAC,cAAc,MAAIA,EAAC,IAAI,MAAI,CAACA,EAAC,OAAK,KAAGA,KAAE,KAAK,MAAIA,EAAC,IAAI,MAAIA,EAAC,cAAc,MAAI,CAACA,EAAC,IAAI,MAAI,CAACA,EAAC,OAAK,KAAGA,KAAE,KAAK,MAAIA,EAAC,IAAI,MAAIA,EAAC,cAAc,MAAI,CAACA,EAAC,IAAI,MAAIA,EAAC,KAAQF,KAAE,MAAIE,EAAC,IAAID,KAAEC,EAAC,OAAK,IAAEA,KAAE,KAAK,MAAIA,EAAC,IAAI,MAAIA,EAAC,cAAc,MAAIA,EAAC,IAAI,MAAIA,EAAC,OAAK,IAAEA,KAAE,KAAK,MAAIA,EAAC,IAAI,MAAIA,EAAC,cAAc,MAAI,CAACA,EAAC,IAAI,MAAIA,EAAC,OAAK,KAAGA,KAAE,KAAK,MAAIA,EAAC,IAAI,MAAIA,EAAC,cAAc,MAAI,CAACA,EAAC,IAAI,MAAI,CAACA,EAAC,OAAK,KAAGA,KAAE,KAAK,MAAIA,EAAC,IAAI,MAAIA,EAAC,cAAc,MAAIA,EAAC,IAAI,MAAI,CAACA,EAAC,EAAE;AAAA,UAAC,EAAC,CAAC,CAAC;AAAA,QAAC;AAAA,QAAC,SAAS,EAAC,GAAEJ,IAAE,GAAEC,IAAE,MAAKC,IAAE,UAASC,GAAC,GAAE;AAAC,eAAK,UAAU,EAAC,GAAEH,IAAE,GAAEC,IAAE,MAAKC,IAAE,UAASC,GAAC,CAAC;AAAA,QAAC;AAAA,QAAC,YAAY,EAAC,GAAEH,IAAE,GAAEC,IAAE,MAAKC,IAAE,UAASC,GAAC,GAAE;AAAC,eAAK,aAAa,EAAC,GAAEH,IAAE,GAAEC,IAAE,MAAKC,IAAE,UAASC,GAAC,CAAC;AAAA,QAAC;AAAA,QAAC,kBAAkB,EAAC,GAAEH,IAAE,GAAEC,IAAE,MAAKC,IAAE,UAASC,GAAC,GAAE;AAAC,eAAK,mBAAmB,EAAC,GAAEH,IAAE,GAAEC,IAAE,MAAKC,IAAE,UAASC,GAAC,CAAC;AAAA,QAAC;AAAA,MAAC;AAAC,YAAM,IAAE,EAAC,KAAI,OAAM,QAAO,SAAQ,GAAE,IAAE,OAAO,OAAO,CAAC;AAAA,MAAE,MAAM,EAAC;AAAA,QAAC,YAAY,EAAC,KAAIH,IAAE,MAAKC,IAAE,QAAOC,GAAC,GAAE;AAAC,eAAK,OAAKF,IAAE,KAAK,QAAMC,IAAE,KAAK,UAAQC;AAAA,QAAC;AAAA,QAAC,KAAKF,IAAEC,IAAEC,IAAEC,IAAE;AAAC,cAAIC;AAAE,UAAAA,KAAE,KAAK,UAAQ,EAAE,SAAO,KAAK,cAAY,KAAK,UAASA,GAAE,KAAK,MAAK,EAAC,GAAEJ,IAAE,GAAEC,IAAE,MAAKC,IAAE,UAASC,GAAC,CAAC;AAAA,QAAC;AAAA,QAAC,cAAc,EAAC,GAAEH,IAAE,GAAEC,IAAE,MAAKC,IAAE,UAASC,KAAE,GAAE,MAAKC,GAAC,GAAE;AAAC,cAAIC;AAAE,gBAAMI,KAAET,KAAEE,KAAE,GAAEgB,KAAEjB,KAAEC,KAAE;AAAE,UAAAE,GAAG,GAAC,UAAQC,KAAE,KAAK,aAAW,WAASA,MAAGA,GAAE,aAAa,aAAY,UAAU,MAAIF,KAAE,KAAK,EAAE,IAAIM,EAAC,IAAIS,EAAC,GAAG;AAAA,QAAC;AAAA,QAAC,UAAUlB,IAAE;AAAC,gBAAK,EAAC,MAAKC,IAAE,GAAEC,IAAE,GAAEC,GAAC,IAAEH;AAAE,eAAK,cAAc,OAAO,OAAO,OAAO,OAAO,CAAA,GAAGA,EAAC,GAAE,EAAC,MAAK,MAAI;AAAC,iBAAK,WAAS,KAAK,QAAQ,SAAS,gBAAgB,8BAA6B,QAAQ,GAAE,KAAK,SAAS,aAAa,MAAK,OAAOE,KAAED,KAAE,CAAC,CAAC,GAAE,KAAK,SAAS,aAAa,MAAK,OAAOE,KAAEF,KAAE,CAAC,CAAC,GAAE,KAAK,SAAS,aAAa,KAAI,OAAOA,KAAE,CAAC,CAAC;AAAA,UAAC,EAAC,CAAC,CAAC;AAAA,QAAC;AAAA,QAAC,aAAaD,IAAE;AAAC,gBAAK,EAAC,MAAKC,IAAE,GAAEC,IAAE,GAAEC,GAAC,IAAEH;AAAE,eAAK,cAAc,OAAO,OAAO,OAAO,OAAO,CAAA,GAAGA,EAAC,GAAE,EAAC,MAAK,MAAI;AAAC,iBAAK,WAAS,KAAK,QAAQ,SAAS,gBAAgB,8BAA6B,MAAM,GAAE,KAAK,SAAS,aAAa,KAAI,OAAOE,EAAC,CAAC,GAAE,KAAK,SAAS,aAAa,KAAI,OAAOC,EAAC,CAAC,GAAE,KAAK,SAAS,aAAa,SAAQ,OAAOF,EAAC,CAAC,GAAE,KAAK,SAAS,aAAa,UAAS,OAAOA,EAAC,CAAC;AAAA,UAAC,EAAC,CAAC,CAAC;AAAA,QAAC;AAAA,QAAC,SAAS,EAAC,GAAED,IAAE,GAAEC,IAAE,MAAKC,IAAE,UAASC,GAAC,GAAE;AAAC,eAAK,UAAU,EAAC,GAAEH,IAAE,GAAEC,IAAE,MAAKC,IAAE,UAASC,GAAC,CAAC;AAAA,QAAC;AAAA,QAAC,YAAY,EAAC,GAAEH,IAAE,GAAEC,IAAE,MAAKC,IAAE,UAASC,GAAC,GAAE;AAAC,eAAK,aAAa,EAAC,GAAEH,IAAE,GAAEC,IAAE,MAAKC,IAAE,UAASC,GAAC,CAAC;AAAA,QAAC;AAAA,MAAC;AAAC,YAAM,IAAE,UAAS,IAAE,CAAC,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,GAAE,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,GAAE,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,GAAE,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,GAAE,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,GAAE,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,GAAE,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,CAAC,GAAE,IAAE,CAAC,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,GAAE,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,GAAE,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,GAAE,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,GAAE,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,GAAE,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,GAAE,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,CAAC;AAAA,MAAE,MAAM,EAAC;AAAA,QAAC,YAAYH,IAAEC,IAAE;AAAC,eAAK,aAAW,CAAAD,OAAG,KAAK,SAAS,YAAY,YAAU,KAAK,MAAMA,EAAC,IAAEA,IAAE,KAAK,UAAQC,IAAE,KAAK,WAAS,KAAK,QAAQ,SAAS,gBAAgB,8BAA6B,KAAK,GAAE,KAAK,SAAS,aAAa,SAAQ,OAAOD,GAAE,KAAK,CAAC,GAAE,KAAK,SAAS,aAAa,UAAS,OAAOA,GAAE,MAAM,CAAC,GAAE,KAAK,SAAS,aAAa,eAAc,8BAA8B,GAAEA,GAAE,YAAY,aAAW,KAAK,SAAS,aAAa,mBAAkB,YAAY,GAAE,KAAK,SAAS,aAAa,WAAU,OAAOA,GAAE,KAAK,IAAIA,GAAE,MAAM,EAAE,GAAE,KAAK,QAAM,KAAK,QAAQ,SAAS,gBAAgB,8BAA6B,MAAM,GAAE,KAAK,SAAS,YAAY,KAAK,KAAK,GAAE,KAAK,YAAUA,GAAE,OAAM,KAAK,cAAY,EAAE,iBAAgB,KAAK,WAASA;AAAA,QAAC;AAAA,QAAC,IAAI,QAAO;AAAC,iBAAO,KAAK,SAAS;AAAA,QAAK;AAAA,QAAC,IAAI,SAAQ;AAAC,iBAAO,KAAK,SAAS;AAAA,QAAM;AAAA,QAAC,aAAY;AAAC,iBAAO,KAAK;AAAA,QAAQ;AAAA,QAAC,MAAM,OAAOA,IAAE;AAAC,gBAAMC,KAAED,GAAE,eAAgB,GAACE,KAAE,KAAK,IAAI,KAAK,SAAS,OAAM,KAAK,SAAS,MAAM,IAAE,IAAE,KAAK,SAAS,QAAOC,KAAE,KAAK,SAAS,UAAQ,IAAED,KAAE,KAAK,KAAK,CAAC,IAAEA,IAAEE,KAAE,KAAK,WAAWD,KAAEF,EAAC;AAAE,cAAIQ,KAAE,EAAC,WAAU,GAAE,WAAU,GAAE,OAAM,GAAE,QAAO,EAAC;AAAE,cAAG,KAAK,MAAIT,IAAE,KAAK,SAAS,OAAM;AAAC,gBAAG,MAAM,KAAK,UAAW,GAAC,CAAC,KAAK,OAAO;AAAO,kBAAK,EAAC,cAAaA,IAAE,WAAUE,GAAC,IAAE,KAAK,UAASC,KAAEH,GAAE,YAAU,EAAEE,GAAE,oBAAoB,GAAEgB,KAAE,KAAK,MAAMf,KAAEF,KAAEA,EAAC;AAAE,YAAAQ,KAAE,SAAS,EAAC,gBAAeT,IAAE,eAAcC,IAAE,eAAcC,IAAE,mBAAkBC,IAAE,SAAQC,GAAC,GAAE;AAAC,oBAAMC,KAAE,EAAC,GAAE,GAAE,GAAE,EAAC,GAAEI,KAAE,EAAC,GAAE,GAAE,GAAE,EAAC;AAAE,kBAAGT,MAAG,KAAGC,MAAG,KAAGC,MAAG,KAAGE,MAAG,EAAE,QAAM,EAAC,QAAO,GAAE,OAAM,GAAE,WAAU,GAAE,WAAU,EAAC;AAAE,oBAAMc,KAAElB,KAAEC;AAAE,qBAAOI,GAAE,IAAE,KAAK,MAAM,KAAK,KAAKH,KAAEgB,EAAC,CAAC,GAAEb,GAAE,KAAG,MAAIA,GAAE,IAAE,IAAGF,MAAGA,KAAEE,GAAE,MAAIA,GAAE,IAAEF,KAAGE,GAAE,IAAE,KAAG,KAAGA,GAAE,KAAII,GAAE,IAAEJ,GAAE,IAAED,IAAEC,GAAE,IAAE,IAAE,IAAE,KAAK,MAAMA,GAAE,IAAEa,KAAE,KAAG,CAAC,GAAET,GAAE,IAAE,KAAK,MAAMA,GAAE,IAAES,EAAC,IAAGb,GAAE,IAAEA,GAAE,IAAEH,MAAGC,MAAGA,KAAEE,GAAE,OAAKF,MAAGA,KAAEE,GAAE,KAAGA,GAAE,IAAEF,IAAEE,GAAE,IAAE,KAAG,KAAGA,GAAE,OAAKA,GAAE,KAAG,GAAEI,GAAE,IAAEJ,GAAE,IAAED,IAAEC,GAAE,IAAE,IAAE,IAAE,KAAK,MAAMA,GAAE,IAAEa,KAAE,KAAG,CAAC,GAAET,GAAE,IAAE,KAAK,MAAMA,GAAE,IAAES,EAAC,IAAG,EAAC,QAAOT,GAAE,GAAE,OAAMA,GAAE,GAAE,WAAUJ,GAAE,GAAE,WAAUA,GAAE,EAAC;AAAA,YAAC,EAAE,EAAC,eAAc,KAAK,OAAO,OAAM,gBAAe,KAAK,OAAO,QAAO,eAAca,IAAE,mBAAkBjB,KAAE,IAAG,SAAQG,GAAC,CAAC;AAAA,UAAC;AAAC,eAAK,eAAc,GAAG,KAAK,SAAU,CAACJ,IAAEE,OAAI;AAAC,gBAAIC,IAAEC,IAAEC,IAAEa,IAAEZ,IAAEI;AAAE,mBAAM,EAAE,KAAK,SAAS,aAAa,sBAAoBV,OAAIC,KAAEQ,GAAE,aAAW,KAAGT,MAAGC,KAAEQ,GAAE,aAAW,KAAGP,OAAID,KAAEQ,GAAE,aAAW,KAAGP,MAAGD,KAAEQ,GAAE,aAAW,MAAI,UAAQN,KAAE,EAAEH,EAAC,MAAI,WAASG,KAAE,SAAOA,GAAED,EAAC,OAAK,UAAQE,KAAE,EAAEJ,KAAEC,KAAE,CAAC,MAAI,WAASG,KAAE,SAAOA,GAAEF,EAAC,OAAK,UAAQG,KAAE,EAAEL,EAAC,MAAI,WAASK,KAAE,SAAOA,GAAEH,KAAED,KAAE,CAAC,OAAK,UAAQiB,KAAE,EAAElB,EAAC,MAAI,WAASkB,KAAE,SAAOA,GAAEhB,EAAC,OAAK,UAAQI,KAAE,EAAEN,KAAEC,KAAE,CAAC,MAAI,WAASK,KAAE,SAAOA,GAAEJ,EAAC,OAAK,UAAQQ,KAAE,EAAEV,EAAC,MAAI,WAASU,KAAE,SAAOA,GAAER,KAAED,KAAE,CAAC;AAAA,UAAG,CAAC,GAAG,KAAK,YAAa,GAAC,KAAK,SAAS,SAAO,MAAM,KAAK,UAAU,EAAC,OAAMQ,GAAE,OAAM,QAAOA,GAAE,QAAO,OAAMR,IAAE,SAAQG,GAAC,CAAC;AAAA,QAAC;AAAA,QAAC,iBAAgB;AAAC,cAAIJ,IAAEC,IAAEC;AAAE,gBAAMC,KAAE,KAAK,UAASC,KAAE,KAAK;AAAS,cAAGD,IAAE;AAAC,kBAAMA,KAAE,UAAQH,KAAEI,GAAE,sBAAoB,WAASJ,KAAE,SAAOA,GAAE,UAASK,KAAE,UAAQJ,KAAEG,GAAE,sBAAoB,WAASH,KAAE,SAAOA,GAAE;AAAM,gBAAIQ,KAAEL,GAAE,QAAOc,KAAEd,GAAE;AAAM,gBAAGD,MAAGE,IAAE;AAAC,oBAAML,KAAE,KAAK,QAAQ,SAAS,gBAAgB,8BAA6B,MAAM;AAAE,mBAAK,sBAAoB,KAAK,QAAQ,SAAS,gBAAgB,8BAA6B,UAAU,GAAE,KAAK,oBAAoB,aAAa,MAAK,8BAA8B,KAAK,WAAW,EAAE,GAAE,KAAK,MAAM,YAAY,KAAK,mBAAmB,IAAG,UAAQE,KAAEE,GAAE,sBAAoB,WAASF,KAAE,SAAOA,GAAE,WAASO,KAAES,KAAE,KAAK,IAAId,GAAE,OAAMA,GAAE,MAAM,GAAEJ,GAAE,aAAa,MAAK,OAAOS,KAAE,IAAEL,GAAE,kBAAkB,KAAK,CAAC,IAAGJ,GAAE,aAAa,KAAI,OAAO,KAAK,YAAYI,GAAE,QAAMc,MAAG,CAAC,CAAC,CAAC,GAAElB,GAAE,aAAa,KAAI,OAAO,KAAK,YAAYI,GAAE,SAAOK,MAAG,CAAC,CAAC,CAAC,GAAET,GAAE,aAAa,SAAQ,OAAOkB,EAAC,CAAC,GAAElB,GAAE,aAAa,UAAS,OAAOS,EAAC,CAAC,GAAE,KAAK,oBAAoB,YAAYT,EAAC,GAAE,KAAK,aAAa,EAAC,SAAQG,IAAE,OAAME,IAAE,oBAAmB,GAAE,GAAE,GAAE,GAAE,GAAE,QAAOD,GAAE,QAAO,OAAMA,GAAE,OAAM,MAAK,oBAAoB,KAAK,WAAW,GAAE,CAAC;AAAA,YAAC;AAAA,UAAC;AAAA,QAAC;AAAA,QAAC,SAASJ,IAAE;AAAC,cAAIC,IAAEC;AAAE,cAAG,CAAC,KAAK,IAAI,OAAK;AAAyB,gBAAMC,KAAE,KAAK,UAASC,KAAE,KAAK,IAAI,eAAc;AAAG,cAAGA,KAAED,GAAE,SAAOC,KAAED,GAAE,OAAO,OAAK;AAA2B,gBAAME,KAAE,KAAK,IAAIF,GAAE,OAAMA,GAAE,MAAM,IAAE,IAAEA,GAAE,QAAOe,KAAEf,GAAE,UAAQ,IAAEE,KAAE,KAAK,KAAK,CAAC,IAAEA,IAAEC,KAAE,KAAK,WAAWY,KAAEd,EAAC,GAAEM,KAAE,KAAK,YAAYP,GAAE,QAAMC,KAAEE,MAAG,CAAC,GAAEK,KAAE,KAAK,YAAYR,GAAE,SAAOC,KAAEE,MAAG,CAAC,GAAEM,KAAE,IAAI,EAAE,EAAC,KAAI,KAAK,UAAS,MAAKT,GAAE,YAAY,MAAK,QAAO,KAAK,QAAO,CAAC;AAAE,eAAK,gBAAc,KAAK,QAAQ,SAAS,gBAAgB,8BAA6B,UAAU,GAAE,KAAK,cAAc,aAAa,MAAK,uBAAuB,KAAK,WAAW,EAAE,GAAE,KAAK,MAAM,YAAY,KAAK,aAAa,GAAE,KAAK,aAAa,EAAC,SAAQ,UAAQF,KAAEE,GAAE,gBAAc,WAASF,KAAE,SAAOA,GAAE,UAAS,OAAME,GAAE,YAAY,OAAM,oBAAmB,GAAE,GAAE,GAAE,GAAE,GAAE,QAAOA,GAAE,QAAO,OAAMA,GAAE,OAAM,MAAK,aAAa,KAAK,WAAW,GAAE,CAAC;AAAE,mBAAQF,KAAE,GAAEA,KAAEG,IAAEH,KAAI,UAAQE,KAAE,GAAEA,KAAEC,IAAED,KAAI,CAAAH,MAAG,CAACA,GAAEC,IAAEE,EAAC,MAAI,UAAQD,KAAE,KAAK,QAAM,WAASA,KAAE,SAAOA,GAAE,OAAOD,IAAEE,EAAC,OAAKS,GAAE,KAAKF,KAAEP,KAAEG,IAAEK,KAAEV,KAAEK,IAAEA,IAAG,CAACJ,IAAEG,OAAI,EAAEF,KAAED,KAAE,KAAGD,KAAEI,KAAE,KAAGF,KAAED,MAAGE,MAAGH,KAAEI,MAAGD,OAAI,EAAEJ,MAAG,CAACA,GAAEC,KAAEI,IAAEF,KAAED,EAAC,MAAI,CAAC,CAAC,KAAK,OAAK,KAAK,IAAI,OAAOD,KAAEI,IAAEF,KAAED,EAAC,IAAIU,GAAE,YAAU,KAAK,iBAAe,KAAK,cAAc,YAAYA,GAAE,QAAQ;AAAG,cAAGT,GAAE,UAAQ,GAAE;AAAC,kBAAMH,KAAE,KAAK,YAAYK,KAAEC,KAAEF,MAAG,CAAC,GAAEH,KAAEG,KAAE,IAAEJ,IAAEE,KAAEQ,KAAEV,KAAEM,IAAEH,KAAEQ,KAAEX,KAAEM,IAAEG,KAAE,IAAGS,KAAE,KAAK,WAAWjB,KAAE,CAAC;AAAE,qBAAQC,KAAE,GAAEA,KAAED,IAAEC,MAAI;AAAC,cAAAO,GAAEP,EAAC,IAAE;AAAG,uBAAQC,KAAE,GAAEA,KAAEF,IAAEE,KAAI,CAAAD,MAAGF,KAAE,KAAGE,MAAGD,KAAED,MAAGG,MAAGH,KAAE,KAAGG,MAAGF,KAAED,MAAG,KAAK,MAAME,KAAEgB,OAAIhB,KAAEgB,OAAIf,KAAEe,OAAIf,KAAEe,GAAE,IAAEA,KAAET,GAAEP,EAAC,EAAEC,EAAC,IAAE,IAAEM,GAAEP,EAAC,EAAEC,EAAC,IAAE,KAAK,IAAI,OAAOA,KAAE,IAAEH,KAAE,IAAEG,KAAEA,MAAGC,KAAED,KAAE,IAAEH,KAAEG,KAAEH,IAAEE,KAAE,IAAEF,KAAE,IAAEE,KAAEA,MAAGE,KAAEF,KAAE,IAAEF,KAAEE,KAAEF,EAAC,IAAE,IAAE;AAAA,YAAC;AAAC,qBAAQA,KAAE,GAAEA,KAAEC,IAAED,KAAI,UAAQI,KAAE,GAAEA,KAAEH,IAAEG,KAAI,CAAAK,GAAET,EAAC,EAAEI,EAAC,MAAIQ,GAAE,KAAKV,KAAEE,KAAEE,IAAEH,KAAEH,KAAEM,IAAEA,IAAG,CAACL,IAAEC,OAAI;AAAC,kBAAIC;AAAE,qBAAM,CAAC,EAAE,UAAQA,KAAEM,GAAET,KAAEE,EAAC,MAAI,WAASC,KAAE,SAAOA,GAAEC,KAAEH,EAAC;AAAA,YAAE,CAAG,GAACW,GAAE,YAAU,KAAK,iBAAe,KAAK,cAAc,YAAYA,GAAE,QAAQ;AAAA,UAAE;AAAA,QAAC;AAAA,QAAC,cAAa;AAAC,cAAG,CAAC,KAAK,IAAI,OAAK;AAAyB,gBAAMZ,KAAE,KAAK,UAASC,KAAE,KAAK;AAAS,cAAG,CAACD,GAAE,OAAK;AAA8B,gBAAME,KAAE,KAAK,IAAI,eAAc,GAAGC,KAAE,KAAK,IAAIF,GAAE,OAAMA,GAAE,MAAM,IAAE,IAAEA,GAAE,QAAOG,KAAEH,GAAE,UAAQ,IAAEE,KAAE,KAAK,KAAK,CAAC,IAAEA,IAAEE,KAAE,KAAK,WAAWD,KAAEF,EAAC,GAAEgB,KAAE,IAAEb,IAAEM,KAAE,IAAEN,IAAEE,KAAE,KAAK,YAAYN,GAAE,QAAMC,KAAEG,MAAG,CAAC,GAAEG,KAAE,KAAK,YAAYP,GAAE,SAAOC,KAAEG,MAAG,CAAC;AAAE,WAAC,CAAC,GAAE,GAAE,CAAC,GAAE,CAAC,GAAE,GAAE,KAAK,KAAG,CAAC,GAAE,CAAC,GAAE,GAAE,CAAC,KAAK,KAAG,CAAC,CAAC,EAAE,QAAS,CAAC,CAACL,IAAEG,IAAEC,EAAC,MAAI;AAAC,gBAAIU,IAAEG,IAAEE,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAE,GAAE,GAAE;AAAE,kBAAM,IAAEpB,KAAEP,KAAEK,MAAGH,KAAE,IAAG,IAAEM,KAAEL,KAAEE,MAAGH,KAAE;AAAG,gBAAI,IAAE,KAAK,eAAc,IAAE,KAAK;AAAc,kBAAK,UAAQY,KAAEb,GAAE,yBAAuB,WAASa,KAAE,SAAOA,GAAE,cAAY,UAAQG,KAAEhB,GAAE,yBAAuB,WAASgB,KAAE,SAAOA,GAAE,YAAU,IAAE,KAAK,QAAQ,SAAS,gBAAgB,8BAA6B,UAAU,GAAE,EAAE,aAAa,MAAK,kCAAkCjB,EAAC,IAAIG,EAAC,IAAI,KAAK,WAAW,EAAE,GAAE,KAAK,MAAM,YAAY,CAAC,GAAE,KAAK,yBAAuB,KAAK,sBAAoB,IAAE,GAAE,KAAK,aAAa,EAAC,SAAQ,UAAQgB,KAAElB,GAAE,yBAAuB,WAASkB,KAAE,SAAOA,GAAE,UAAS,OAAM,UAAQC,KAAEnB,GAAE,yBAAuB,WAASmB,KAAE,SAAOA,GAAE,OAAM,oBAAmBhB,IAAE,GAAE,GAAE,GAAE,GAAE,QAAOc,IAAE,OAAMA,IAAE,MAAK,wBAAwBlB,EAAC,IAAIG,EAAC,IAAI,KAAK,WAAW,GAAE,CAAC,KAAI,UAAQkB,KAAEpB,GAAE,yBAAuB,WAASoB,KAAE,SAAOA,GAAE,SAAO,EAAE,SAASpB,GAAE,qBAAqB,IAAI,GAAE;AAAC,oBAAMD,KAAE,IAAI,EAAE,EAAC,KAAI,KAAK,UAAS,MAAKC,GAAE,qBAAqB,MAAK,QAAO,KAAK,QAAO,CAAC;AAAE,cAAAD,GAAE,KAAK,GAAE,GAAEkB,IAAEd,EAAC,GAAEJ,GAAE,YAAU,KAAG,EAAE,YAAYA,GAAE,QAAQ;AAAA,YAAC,OAAK;AAAC,oBAAMA,KAAE,IAAI,EAAE,EAAC,KAAI,KAAK,UAAS,OAAM,UAAQsB,KAAErB,GAAE,yBAAuB,WAASqB,KAAE,SAAOA,GAAE,SAAOrB,GAAE,YAAY,MAAK,QAAO,KAAK,QAAO,CAAC;AAAE,uBAAQA,KAAE,GAAEA,KAAE,EAAE,QAAOA,KAAI,UAAQC,KAAE,GAAEA,KAAE,EAAED,EAAC,EAAE,QAAOC,KAAI,EAAC,UAAQqB,KAAE,EAAEtB,EAAC,MAAI,WAASsB,KAAE,SAAOA,GAAErB,EAAC,OAAKF,GAAE,KAAK,IAAEE,KAAEG,IAAE,IAAEJ,KAAEI,IAAEA,IAAG,CAACL,IAAEG,OAAI;AAAC,oBAAIC;AAAE,uBAAM,CAAC,EAAE,UAAQA,KAAE,EAAEH,KAAEE,EAAC,MAAI,WAASC,KAAE,SAAOA,GAAEF,KAAEF,EAAC;AAAA,cAAE,IAAIA,GAAE,YAAU,KAAG,EAAE,YAAYA,GAAE,QAAQ;AAAA,YAAE;AAAC,kBAAK,UAAQwB,KAAEvB,GAAE,sBAAoB,WAASuB,KAAE,SAAOA,GAAE,cAAY,UAAQC,KAAExB,GAAE,sBAAoB,WAASwB,KAAE,SAAOA,GAAE,YAAU,IAAE,KAAK,QAAQ,SAAS,gBAAgB,8BAA6B,UAAU,GAAE,EAAE,aAAa,MAAK,+BAA+BzB,EAAC,IAAIG,EAAC,IAAI,KAAK,WAAW,EAAE,GAAE,KAAK,MAAM,YAAY,CAAC,GAAE,KAAK,sBAAoB,GAAE,KAAK,aAAa,EAAC,SAAQ,UAAQuB,KAAEzB,GAAE,sBAAoB,WAASyB,KAAE,SAAOA,GAAE,UAAS,OAAM,UAAQC,KAAE1B,GAAE,sBAAoB,WAAS0B,KAAE,SAAOA,GAAE,OAAM,oBAAmBvB,IAAE,GAAE,IAAE,IAAEC,IAAE,GAAE,IAAE,IAAEA,IAAE,QAAOM,IAAE,OAAMA,IAAE,MAAK,qBAAqBX,EAAC,IAAIG,EAAC,IAAI,KAAK,WAAW,GAAE,CAAC,KAAI,UAAQ,IAAEF,GAAE,sBAAoB,WAAS,IAAE,SAAO,EAAE,SAAO,EAAE,SAASA,GAAE,kBAAkB,IAAI,GAAE;AAAC,oBAAMD,KAAE,IAAI,EAAE,EAAC,KAAI,KAAK,UAAS,MAAKC,GAAE,kBAAkB,MAAK,QAAO,KAAK,QAAO,CAAC;AAAE,cAAAD,GAAE,KAAK,IAAE,IAAEK,IAAE,IAAE,IAAEA,IAAEM,IAAEP,EAAC,GAAEJ,GAAE,YAAU,KAAG,EAAE,YAAYA,GAAE,QAAQ;AAAA,YAAC,OAAK;AAAC,oBAAMA,KAAE,IAAI,EAAE,EAAC,KAAI,KAAK,UAAS,OAAM,UAAQ,IAAEC,GAAE,sBAAoB,WAAS,IAAE,SAAO,EAAE,SAAOA,GAAE,YAAY,MAAK,QAAO,KAAK,QAAO,CAAC;AAAE,uBAAQA,KAAE,GAAEA,KAAE,EAAE,QAAOA,KAAI,UAAQC,KAAE,GAAEA,KAAE,EAAED,EAAC,EAAE,QAAOC,KAAI,EAAC,UAAQ,IAAE,EAAED,EAAC,MAAI,WAAS,IAAE,SAAO,EAAEC,EAAC,OAAKF,GAAE,KAAK,IAAEE,KAAEG,IAAE,IAAEJ,KAAEI,IAAEA,IAAG,CAACL,IAAEG,OAAI;AAAC,oBAAIC;AAAE,uBAAM,CAAC,EAAE,UAAQA,KAAE,EAAEH,KAAEE,EAAC,MAAI,WAASC,KAAE,SAAOA,GAAEF,KAAEF,EAAC;AAAA,cAAE,CAAC,GAAGA,GAAE,YAAU,KAAG,EAAE,YAAYA,GAAE,QAAQ;AAAA,YAAE;AAAA,UAAC,CAAC;AAAA,QAAE;AAAA,QAAC,YAAW;AAAC,iBAAO,IAAI,QAAS,CAACA,IAAEC,OAAI;AAAC,gBAAIC;AAAE,kBAAMC,KAAE,KAAK;AAAS,gBAAG,CAACA,GAAE,MAAM,QAAOF,GAAE,sBAAsB;AAAE,gBAAG,UAAQC,KAAEC,GAAE,eAAa,WAASD,KAAE,SAAOA,GAAE,UAAU,CAAAC,GAAE,WAAW,UAAUA,GAAE,KAAK,EAAE,KAAM,CAAAF,OAAG;AAAC,kBAAIC,IAAEE;AAAE,kBAAG,KAAK,SAAOH,IAAE,KAAK,SAAS,aAAa,YAAW;AAAC,sBAAMD,KAAE,UAAQE,KAAEC,GAAE,eAAa,WAASD,KAAE,SAAOA,GAAE,aAAa,KAAK,OAAO,OAAM,KAAK,OAAO,MAAM;AAAE,0BAAQE,KAAE,QAAMJ,KAAE,SAAOA,GAAE,WAAW,IAAI,MAAI,WAASI,MAAGA,GAAE,UAAUH,IAAE,GAAE,CAAC,GAAE,KAAK,YAAU,QAAMD,KAAE,SAAOA,GAAE,UAAS;AAAA,cAAE;AAAC,cAAAA,GAAC;AAAA,YAAE,CAAG,EAAC,MAAMC,EAAC;AAAA,iBAAM;AAAC,oBAAMA,KAAE,IAAI,KAAK,QAAQ;AAAM,0BAAU,OAAOE,GAAE,aAAa,gBAAcF,GAAE,cAAYE,GAAE,aAAa,cAAa,KAAK,SAAOF,IAAEA,GAAE,SAAO,YAAS;AAAC,qBAAK,SAAS,aAAa,eAAa,KAAK,YAAU,MAAM,eAAeD,IAAEC,IAAE;AAAC,yBAAO,IAAI,QAAS,CAAAC,OAAG;AAAC,0BAAMC,KAAE,IAAIF,GAAE;AAAe,oBAAAE,GAAE,SAAO,WAAU;AAAC,4BAAMH,KAAE,IAAIC,GAAE;AAAW,sBAAAD,GAAE,YAAU,WAAU;AAAC,wBAAAE,GAAEF,GAAE,MAAM;AAAA,sBAAC,GAAEA,GAAE,cAAcG,GAAE,QAAQ;AAAA,oBAAC,GAAEA,GAAE,KAAK,OAAMH,EAAC,GAAEG,GAAE,eAAa,QAAOA,GAAE,KAAI;AAAA,kBAAE,CAAC;AAAA,gBAAE,EAAEA,GAAE,SAAO,IAAG,KAAK,OAAO,IAAGH,GAAG;AAAA,cAAA,GAAEC,GAAE,MAAIE,GAAE;AAAA,YAAK;AAAA,UAAC;QAAG;AAAA,QAAC,MAAM,UAAU,EAAC,OAAMH,IAAE,QAAOC,IAAE,OAAMC,IAAE,SAAQC,GAAC,GAAE;AAAC,gBAAMC,KAAE,KAAK,UAASC,KAAE,KAAK,YAAYD,GAAE,QAAMF,KAAEC,MAAG,CAAC,GAAEM,KAAE,KAAK,YAAYL,GAAE,SAAOF,KAAEC,MAAG,CAAC,GAAEe,KAAEb,KAAE,KAAK,WAAWD,GAAE,aAAa,UAAQF,KAAEC,KAAEH,MAAG,CAAC,GAAEM,KAAEG,KAAE,KAAK,WAAWL,GAAE,aAAa,UAAQF,KAAEC,KAAEF,MAAG,CAAC,GAAES,KAAEV,KAAE,IAAEI,GAAE,aAAa,QAAOO,KAAEV,KAAE,IAAEG,GAAE,aAAa,QAAOQ,KAAE,KAAK,QAAQ,SAAS,gBAAgB,8BAA6B,OAAO;AAAE,UAAAA,GAAE,aAAa,QAAO,KAAK,aAAW,EAAE,GAAEA,GAAE,aAAa,cAAa,KAAK,aAAW,EAAE,GAAEA,GAAE,aAAa,KAAI,OAAOM,EAAC,CAAC,GAAEN,GAAE,aAAa,KAAI,OAAON,EAAC,CAAC,GAAEM,GAAE,aAAa,SAAQ,GAAGF,EAAC,IAAI,GAAEE,GAAE,aAAa,UAAS,GAAGD,EAAC,IAAI,GAAE,KAAK,SAAS,YAAYC,EAAC;AAAA,QAAC;AAAA,QAAC,aAAa,EAAC,SAAQZ,IAAE,OAAMC,IAAE,oBAAmBC,IAAE,GAAEC,IAAE,GAAEC,IAAE,QAAOC,IAAE,OAAMI,IAAE,MAAKS,GAAC,GAAE;AAAC,gBAAMZ,KAAEG,KAAEJ,KAAEI,KAAEJ,IAAEK,KAAE,KAAK,QAAQ,SAAS,gBAAgB,8BAA6B,MAAM;AAAE,cAAGA,GAAE,aAAa,KAAI,OAAOP,EAAC,CAAC,GAAEO,GAAE,aAAa,KAAI,OAAON,EAAC,CAAC,GAAEM,GAAE,aAAa,UAAS,OAAOL,EAAC,CAAC,GAAEK,GAAE,aAAa,SAAQ,OAAOD,EAAC,CAAC,GAAEC,GAAE,aAAa,aAAY,mBAAmBQ,EAAC,IAAI,GAAElB,IAAE;AAAC,gBAAIC;AAAE,gBAAG,aAAWD,GAAE,KAAK,CAAAC,KAAE,KAAK,QAAQ,SAAS,gBAAgB,8BAA6B,gBAAgB,GAAEA,GAAE,aAAa,MAAKiB,EAAC,GAAEjB,GAAE,aAAa,iBAAgB,gBAAgB,GAAEA,GAAE,aAAa,MAAK,OAAOE,KAAEM,KAAE,CAAC,CAAC,GAAER,GAAE,aAAa,MAAK,OAAOG,KAAEC,KAAE,CAAC,CAAC,GAAEJ,GAAE,aAAa,MAAK,OAAOE,KAAEM,KAAE,CAAC,CAAC,GAAER,GAAE,aAAa,MAAK,OAAOG,KAAEC,KAAE,CAAC,CAAC,GAAEJ,GAAE,aAAa,KAAI,OAAOK,KAAE,CAAC,CAAC;AAAA,iBAAM;AAAC,oBAAMA,OAAIN,GAAE,YAAU,KAAGE,OAAI,IAAE,KAAK,KAAIQ,MAAGJ,KAAE,IAAE,KAAK,OAAK,IAAE,KAAK;AAAI,kBAAIK,KAAER,KAAEM,KAAE,GAAEG,KAAER,KAAEC,KAAE,GAAEQ,KAAEV,KAAEM,KAAE,GAAEK,KAAEV,KAAEC,KAAE;AAAE,cAAAK,MAAG,KAAGA,MAAG,OAAI,KAAK,MAAIA,KAAE,OAAK,KAAK,MAAIA,MAAG,IAAE,KAAK,MAAIC,MAAGF,KAAE,GAAEG,MAAGP,KAAE,IAAE,KAAK,IAAIC,EAAC,GAAEO,MAAGJ,KAAE,GAAEK,MAAGT,KAAE,IAAE,KAAK,IAAIC,EAAC,KAAGI,KAAE,OAAI,KAAK,MAAIA,MAAG,OAAI,KAAK,MAAIE,MAAGP,KAAE,GAAEM,MAAGF,KAAE,IAAE,KAAK,IAAIH,EAAC,GAAEQ,MAAGT,KAAE,GAAEQ,MAAGJ,KAAE,IAAE,KAAK,IAAIH,EAAC,KAAGI,KAAE,OAAI,KAAK,MAAIA,MAAG,OAAK,KAAK,MAAIC,MAAGF,KAAE,GAAEG,MAAGP,KAAE,IAAE,KAAK,IAAIC,EAAC,GAAEO,MAAGJ,KAAE,GAAEK,MAAGT,KAAE,IAAE,KAAK,IAAIC,EAAC,KAAGI,KAAE,OAAK,KAAK,MAAIA,MAAG,OAAK,KAAK,OAAKE,MAAGP,KAAE,GAAEM,MAAGF,KAAE,IAAE,KAAK,IAAIH,EAAC,GAAEQ,MAAGT,KAAE,GAAEQ,MAAGJ,KAAE,IAAE,KAAK,IAAIH,EAAC,IAAGL,KAAE,KAAK,QAAQ,SAAS,gBAAgB,8BAA6B,gBAAgB,GAAEA,GAAE,aAAa,MAAKiB,EAAC,GAAEjB,GAAE,aAAa,iBAAgB,gBAAgB,GAAEA,GAAE,aAAa,MAAK,OAAO,KAAK,MAAMU,EAAC,CAAC,CAAC,GAAEV,GAAE,aAAa,MAAK,OAAO,KAAK,MAAMW,EAAC,CAAC,CAAC,GAAEX,GAAE,aAAa,MAAK,OAAO,KAAK,MAAMY,EAAC,CAAC,CAAC,GAAEZ,GAAE,aAAa,MAAK,OAAO,KAAK,MAAMa,EAAC,CAAC,CAAC;AAAA,YAAC;AAAC,YAAAd,GAAE,WAAW,QAAS,CAAC,EAAC,QAAOA,IAAE,OAAME,GAAC,MAAI;AAAC,oBAAMC,KAAE,KAAK,QAAQ,SAAS,gBAAgB,8BAA6B,MAAM;AAAE,cAAAA,GAAE,aAAa,UAAS,MAAIH,KAAE,GAAG,GAAEG,GAAE,aAAa,cAAaD,EAAC,GAAED,GAAE,YAAYE,EAAC;AAAA,YAAC,CAAG,GAACO,GAAE,aAAa,QAAO,SAASQ,EAAC,IAAI,GAAE,KAAK,MAAM,YAAYjB,EAAC;AAAA,UAAC,MAAM,CAAAA,MAAGS,GAAE,aAAa,QAAOT,EAAC;AAAE,eAAK,SAAS,YAAYS,EAAC;AAAA,QAAC;AAAA,MAAC;AAAC,QAAE,gBAAc;AAAE,YAAM,IAAE,GAAE,IAAE,UAAS,IAAE,CAAE;AAAC,eAAQV,KAAE,GAAEA,MAAG,IAAGA,KAAI,GAAEA,EAAC,IAAEA;AAAE,YAAM,IAAE,EAAC,MAAK,GAAE,OAAM,UAAS,OAAM,KAAI,QAAO,KAAI,MAAK,IAAG,QAAO,GAAE,WAAU,EAAC,YAAW,EAAE,CAAC,GAAE,MAAK,QAAO,sBAAqB,IAAG,GAAE,cAAa,EAAC,YAAW,MAAG,oBAAmB,MAAG,WAAU,KAAG,aAAY,QAAO,QAAO,EAAC,GAAE,aAAY,EAAC,MAAK,UAAS,OAAM,QAAO,WAAU,KAAE,GAAE,mBAAkB,EAAC,OAAM,GAAE,OAAM,OAAM,EAAC;AAAE,eAAS,EAAEA,IAAE;AAAC,cAAMC,KAAE,OAAO,OAAO,CAAA,GAAGD,EAAC;AAAE,YAAG,CAACC,GAAE,cAAY,CAACA,GAAE,WAAW,OAAO,OAAK;AAA6C,eAAOA,GAAE,WAASA,GAAE,WAAS,OAAOA,GAAE,QAAQ,IAAEA,GAAE,WAAS,GAAEA,GAAE,aAAWA,GAAE,WAAW,IAAK,CAAAD,OAAG,OAAO,OAAO,OAAO,OAAO,CAAA,GAAGA,EAAC,GAAE,EAAC,QAAO,OAAOA,GAAE,MAAM,EAAC,CAAC,CAAC,GAAGC;AAAA,MAAC;AAAC,eAAS,EAAED,IAAE;AAAC,cAAMC,KAAE,OAAO,OAAO,CAAE,GAACD,EAAC;AAAE,eAAOC,GAAE,QAAM,OAAOA,GAAE,KAAK,GAAEA,GAAE,SAAO,OAAOA,GAAE,MAAM,GAAEA,GAAE,SAAO,OAAOA,GAAE,MAAM,GAAEA,GAAE,eAAa,OAAO,OAAO,OAAO,OAAO,IAAGA,GAAE,YAAY,GAAE,EAAC,oBAAmB,QAAQA,GAAE,aAAa,kBAAkB,GAAE,WAAU,OAAOA,GAAE,aAAa,SAAS,GAAE,QAAO,OAAOA,GAAE,aAAa,MAAM,EAAC,CAAC,GAAEA,GAAE,SAAO,KAAK,IAAIA,GAAE,OAAMA,GAAE,MAAM,MAAIA,GAAE,SAAO,KAAK,IAAIA,GAAE,OAAMA,GAAE,MAAM,IAAGA,GAAE,cAAY,OAAO,OAAO,IAAGA,GAAE,WAAW,GAAEA,GAAE,YAAY,aAAWA,GAAE,YAAY,WAAS,EAAEA,GAAE,YAAY,QAAQ,IAAGA,GAAE,yBAAuBA,GAAE,uBAAqB,OAAO,OAAO,CAAE,GAACA,GAAE,oBAAoB,GAAEA,GAAE,qBAAqB,aAAWA,GAAE,qBAAqB,WAAS,EAAEA,GAAE,qBAAqB,QAAQ,KAAIA,GAAE,sBAAoBA,GAAE,oBAAkB,OAAO,OAAO,IAAGA,GAAE,iBAAiB,GAAEA,GAAE,kBAAkB,aAAWA,GAAE,kBAAkB,WAAS,EAAEA,GAAE,kBAAkB,QAAQ,KAAIA,GAAE,sBAAoBA,GAAE,oBAAkB,OAAO,OAAO,CAAA,GAAGA,GAAE,iBAAiB,GAAEA,GAAE,kBAAkB,aAAWA,GAAE,kBAAkB,WAAS,EAAEA,GAAE,kBAAkB,QAAQ,KAAIA;AAAA,MAAC;AAAC,UAAI,IAAE,EAAE,GAAG,GAAE,IAAE,EAAE,EAAE,CAAC;AAAE,eAAS,EAAED,IAAE;AAAC,YAAG,CAACA,GAAE,OAAM,IAAI,MAAM,2BAA2B;AAAE,gBAAMA,GAAE,CAAC,MAAIA,KAAEA,GAAE,UAAU,CAAC;AAAG,cAAMC,KAAE,EAAC,KAAI,aAAY,KAAI,aAAY,KAAI,4BAA2B,MAAK,cAAa,KAAI,cAAa,KAAI,aAAY,KAAI,iBAAgB,KAAI,cAAa,MAAK,cAAa,MAAK,cAAa,KAAI,kBAAiB,EAAED,GAAE,YAAa,CAAA;AAAE,YAAG,CAACC,GAAE,OAAM,IAAI,MAAM,cAAcD,EAAC,oBAAoB;AAAE,eAAOC;AAAA,MAAC;AAAA,MAAC,MAAM,EAAC;AAAA,QAAC,YAAYD,IAAE;AAAC,WAAC,QAAMA,KAAE,SAAOA,GAAE,SAAO,KAAK,UAAQ,IAAIA,GAAE,MAAM,IAAG,EAAC,WAAU,SAAQ,CAAC,EAAE,SAAO,KAAK,UAAQ,QAAO,KAAK,WAASA,KAAE,EAAEC,GAAE,GAAED,EAAC,CAAC,IAAE,GAAE,KAAK,OAAM;AAAA,QAAE;AAAA,QAAC,OAAO,gBAAgBA,IAAE;AAAC,UAAAA,OAAIA,GAAE,YAAU;AAAA,QAAG;AAAA,QAAC,YAAW;AAAC,cAAG,CAAC,KAAK,IAAI;AAAO,gBAAMA,KAAE,IAAI,EAAE,KAAK,UAAS,KAAK,OAAO;AAAE,eAAK,OAAKA,GAAE,WAAU,GAAG,KAAK,qBAAmBA,GAAE,OAAO,KAAK,GAAG,EAAE,KAAM,MAAI;AAAC,gBAAIC;AAAE,iBAAK,SAAO,UAAQA,KAAE,KAAK,eAAa,WAASA,MAAGA,GAAE,KAAK,MAAKD,GAAE,WAAU,GAAG,KAAK,QAAQ;AAAA,UAAE,CAAG;AAAA,QAAA;AAAA,QAAC,eAAc;AAAC,cAAIA,IAAEC;AAAE,eAAK,SAAO,UAAQD,KAAE,KAAK,SAAS,eAAa,WAASA,KAAE,SAAOA,GAAE,iBAAe,KAAK,cAAY,KAAK,SAAS,WAAW,aAAa,KAAK,SAAS,OAAM,KAAK,SAAS,MAAM,GAAE,KAAK,YAAY,QAAM,KAAK,SAAS,OAAM,KAAK,YAAY,SAAO,KAAK,SAAS,WAAS,KAAK,aAAW,SAAS,cAAc,QAAQ,GAAE,KAAK,WAAW,QAAM,KAAK,SAAS,OAAM,KAAK,WAAW,SAAO,KAAK,SAAS,SAAQ,KAAK,UAAW,GAAC,KAAK,wBAAsB,UAAQC,KAAE,KAAK,uBAAqB,WAASA,KAAE,SAAOA,GAAE,KAAM,MAAI;AAAC,gBAAID;AAAE,gBAAG,CAAC,KAAK,KAAK;AAAO,kBAAMC,KAAE,KAAK,MAAKC,KAAG,IAAI,KAAK,QAAQ,gBAAe,kBAAkBD,EAAC,GAAEE,KAAE,KAAKD,EAAC,GAAEE,KAAE,QAAQ,EAAE,KAAK,CAAC,WAAWD,EAAC;AAAG,gBAAG,UAAQH,KAAE,KAAK,SAAS,eAAa,WAASA,KAAE,SAAOA,GAAE,UAAU,QAAO,KAAK,SAAS,WAAW,UAAUI,EAAC,EAAE,KAAM,CAAAJ,OAAG;AAAC,kBAAIC,IAAEC;AAAE,cAAAF,GAAE,QAAM,KAAK,SAAS,OAAMA,GAAE,SAAO,KAAK,SAAS,QAAO,UAAQE,KAAE,UAAQD,KAAE,KAAK,gBAAc,WAASA,KAAE,SAAOA,GAAE,WAAW,IAAI,MAAI,WAASC,MAAGA,GAAE,UAAUF,IAAE,GAAE,CAAC;AAAA,YAAC,CAAG;AAAC;AAAC,oBAAMA,KAAE,IAAI,KAAK,QAAQ;AAAM,qBAAO,IAAI,QAAS,CAAAC,OAAG;AAAC,gBAAAD,GAAE,SAAO,MAAI;AAAC,sBAAIE,IAAEC;AAAE,4BAAQA,KAAE,UAAQD,KAAE,KAAK,eAAa,WAASA,KAAE,SAAOA,GAAE,WAAW,IAAI,MAAI,WAASC,MAAGA,GAAE,UAAUH,IAAE,GAAE,CAAC,GAAEC,GAAC;AAAA,gBAAE,GAAED,GAAE,MAAII;AAAA,cAAC;YAAG;AAAA,UAAC;QAAI;AAAA,QAAC,MAAM,YAAYJ,KAAE,OAAM;AAAC,cAAG,CAAC,KAAK,IAAI,OAAK;AAAmB,iBAAM,UAAQA,GAAE,YAAW,KAAI,KAAK,QAAM,KAAK,sBAAoB,KAAK,UAAW,GAAC,MAAM,KAAK,oBAAmB,KAAK,UAAQ,KAAK,cAAY,KAAK,gBAAc,KAAK,yBAAuB,KAAK,aAAc,GAAC,MAAM,KAAK,uBAAsB,KAAK,cAAY,KAAK;AAAA,QAAY;AAAA,QAAC,OAAOA,IAAE;AAAC,YAAE,gBAAgB,KAAK,UAAU,GAAE,KAAK,WAASA,KAAE,EAAEC,GAAE,KAAK,UAASD,EAAC,CAAC,IAAE,KAAK,UAAS,KAAK,SAAS,SAAO,KAAK,MAAI,IAAI,KAAK,SAAS,UAAU,YAAW,KAAK,SAAS,UAAU,oBAAoB,GAAE,KAAK,IAAI,QAAQ,KAAK,SAAS,MAAK,KAAK,SAAS,UAAU,QAAM,SAASA,IAAE;AAAC,oBAAO,MAAE;AAAA,cAAE,KAAI,WAAW,KAAKA,EAAC;AAAE,uBAAM;AAAA,cAAU,KAAI,wBAAwB,KAAKA,EAAC;AAAE,uBAAM;AAAA,cAAe;AAAQ,uBAAM;AAAA,YAAM;AAAA,UAAC,EAAE,KAAK,SAAS,IAAI,CAAC,GAAE,KAAK,IAAI,KAAM,GAAC,KAAK,SAAS,SAAO,IAAE,KAAK,aAAc,IAAC,KAAK,aAAY,KAAK,OAAO,KAAK,UAAU;AAAA,QAAE;AAAA,QAAC,OAAOA,IAAE;AAAC,cAAGA,IAAE;AAAC,gBAAG,cAAY,OAAOA,GAAE,YAAY,OAAK;AAAwC,iBAAK,SAAS,SAAO,IAAE,KAAK,cAAYA,GAAE,YAAY,KAAK,UAAU,IAAE,KAAK,QAAMA,GAAE,YAAY,KAAK,IAAI,GAAE,KAAK,aAAWA;AAAA,UAAC;AAAA,QAAC;AAAA,QAAC,eAAeA,IAAE;AAAC,cAAG,CAACA,GAAE,OAAK;AAAwC,eAAK,aAAWA,IAAE,KAAK;QAAQ;AAAA,QAAC,kBAAiB;AAAC,eAAK,aAAW,QAAO,KAAK;QAAQ;AAAA,QAAC,MAAM,WAAWA,KAAE,OAAM;AAAC,cAAG,CAAC,KAAK,IAAI,OAAK;AAAmB,gBAAMC,KAAE,MAAM,KAAK,YAAYD,EAAC,GAAEE,KAAE,EAAEF,EAAC;AAAE,cAAG,CAACC,GAAE,QAAO;AAAK,cAAG,UAAQD,GAAE,YAAW,GAAG;AAAC,kBAAMA,KAAE;AAAA,EAA6C,IAAI,KAAK,QAAQ,gBAAe,kBAAkBC,EAAC,CAAC;AAAG,mBAAM,eAAa,OAAO,QAAM,KAAK,SAAS,QAAM,OAAO,KAAKD,EAAC,IAAE,IAAI,KAAK,CAACA,EAAC,GAAE,EAAC,MAAKE,GAAC,CAAC;AAAA,UAAC;AAAC,iBAAO,IAAI,QAAS,CAAAF,OAAG;AAAC,kBAAMG,KAAEF;AAAE,gBAAG,cAAaE,GAAE,KAAG,gBAAcD,GAAE,CAAAF,GAAEG,GAAE,SAASD,EAAC,CAAC;AAAA,qBAAU,iBAAeA,GAAE,CAAAF,GAAEG,GAAE,SAASD,EAAC,CAAC;AAAA,iBAAM;AAAC,kBAAG,sBAAoBA,GAAE,OAAM,MAAM,uBAAuB;AAAE,cAAAF,GAAEG,GAAE,SAASD,EAAC,CAAC;AAAA,YAAC;AAAA,gBAAK,aAAWC,MAAGA,GAAE,OAAOH,IAAEE,IAAE,CAAC;AAAA,UAAC;QAAG;AAAA,QAAC,MAAM,SAASF,IAAE;AAAC,cAAG,CAAC,KAAK,IAAI,OAAK;AAAmB,cAAG,eAAa,OAAO,KAAK,OAAK;AAAuD,cAAIC,KAAE,OAAMC,KAAE;AAAK,sBAAU,OAAOF,MAAGC,KAAED,IAAE,QAAQ,KAAK,6HAA6H,KAAG,YAAU,OAAOA,MAAG,SAAOA,OAAIA,GAAE,SAAOE,KAAEF,GAAE,OAAMA,GAAE,cAAYC,KAAED,GAAE;AAAY,gBAAMG,KAAE,MAAM,KAAK,YAAYF,EAAC;AAAE,cAAGE,GAAE,KAAG,UAAQF,GAAE,YAAa,GAAC;AAAC,gBAAID,KAAG,IAAI,gBAAe,kBAAkBG,EAAC;AAAE,YAAAH,KAAE,8CAA4CA,IAAE,EAAE,QAAQ,EAAEC,EAAC,CAAC,kBAAkB,mBAAmBD,EAAC,CAAC,IAAG,GAAGE,EAAC,MAAM;AAAA,UAAC,MAAM,GAAEC,GAAE,UAAU,EAAEF,EAAC,CAAC,GAAE,GAAGC,EAAC,IAAID,EAAC,EAAE;AAAA,QAAC;AAAA,MAAC;AAAC,YAAM,IAAE;AAAA,IAAC,MAAK,EAAE;AAAA,EAAO,IAAM;;;;;;;;", "x_google_ignoreList": [0]}