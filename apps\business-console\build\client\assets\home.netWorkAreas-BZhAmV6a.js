import { r as reactExports, j as jsxRuntimeExports } from "./jsx-runtime-bB2y7OuD.js";
import { B as Button } from "./button-ByAXMyvk.js";
import { I as Input } from "./input-3v87qohQ.js";
import { S as Select, a as SelectTrigger, b as SelectValue, c as SelectContent, d as SelectItem } from "./select-BFGSXKcr.js";
import { T as Table, a as TableHeader, c as TableHead, d as TableBody, b as TableRow, e as TableCell } from "./table-CAxdJZsY.js";
import { u as useLoaderData, d as useActionData, F as Form } from "./components-D7UvGag_.js";
import { S as SquarePen } from "./square-pen-BXxSi9JH.js";
import "./index-z_byfFrQ.js";
import "./index-ImHKLo0a.js";
import "./utils-GkgzjW3c.js";
import "./index-Vp2vNLNM.js";
import "./index-IXOTxK3N.js";
import "./index-D7VH9Fc8.js";
import "./index-qCtcpOcW.js";
import "./index-BTdCMChR.js";
import "./index-BXzPK7u0.js";
import "./index-CzZ6EYkN.js";
import "./index-CEHS9zzk.js";
import "./index-DVTNuYOr.js";
import "./index-DscYByPT.js";
import "./index-CkL5tk39.js";
import "./index-dIGjFc0I.js";
import "./index-CpKiYcZd.js";
import "./index-C88PRvfd.js";
import "./chevron-down-pCP5jmjX.js";
import "./createLucideIcon-uwkRm45G.js";
import "./check-_dbWxIzT.js";
import "./index-DhHTcibu.js";
function NetWorkAreas() {
  const initialData = useLoaderData();
  const areaData = useActionData();
  const [netWorkAreas, setNetWorkAreas] = reactExports.useState((areaData == null ? void 0 : areaData.data) || []);
  const [searchTerm, setSearchTerm] = reactExports.useState("");
  const [netWorkId, setNetWorkId] = reactExports.useState("");
  const [pageSize, setPageSize] = reactExports.useState("50");
  const [currentPage, setCurrentPage] = reactExports.useState(1);
  reactExports.useEffect(() => {
    if (areaData == null ? void 0 : areaData.data) {
      setNetWorkAreas(areaData.data);
    }
  }, [areaData == null ? void 0 : areaData.data]);
  const filterNetWorks = netWorkAreas == null ? void 0 : netWorkAreas.filter((x) => x.agentName.toLowerCase().includes(searchTerm.toLowerCase()) || x.networkAreaName.toLowerCase().includes(searchTerm.toLowerCase()));
  const paginatedData = reactExports.useMemo(() => {
    const start = (currentPage - 1) * Number(pageSize);
    const end = start + Number(pageSize);
    return [...filterNetWorks].sort((a, b) => a.networkAreaId > b.networkAreaId ? -1 : 1).slice(start, end);
  }, [filterNetWorks, currentPage, pageSize]);
  const totalPages = Math.ceil(filterNetWorks.length / Number(pageSize));
  return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
    className: "container mx-auto w-full",
    children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
      className: "flex my-7 space-x-10",
      children: [/* @__PURE__ */ jsxRuntimeExports.jsxs(Form, {
        method: "post",
        className: "flex space-x-8",
        children: [/* @__PURE__ */ jsxRuntimeExports.jsxs(Select, {
          value: netWorkId,
          onValueChange: setNetWorkId,
          name: "netWorkId",
          children: [/* @__PURE__ */ jsxRuntimeExports.jsx(SelectTrigger, {
            className: "w-[180px]",
            children: /* @__PURE__ */ jsxRuntimeExports.jsx(SelectValue, {
              placeholder: "Select NetWork "
            })
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(SelectContent, {
            children: initialData.data.sort((a, b) => a.managerName.localeCompare(b.managerName)).map((x) => {
              return /* @__PURE__ */ jsxRuntimeExports.jsx(SelectItem, {
                value: x.id.toString(),
                children: x.name
              });
            })
          })]
        }), /* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
          type: "submit",
          children: "Get NetWork Areas"
        })]
      }), /* @__PURE__ */ jsxRuntimeExports.jsx(Input, {
        placeholder: "Search Area/Agent",
        value: searchTerm,
        onChange: (e) => setSearchTerm(e.target.value),
        className: "max-w-sm"
      }), /* @__PURE__ */ jsxRuntimeExports.jsxs(Select, {
        value: pageSize,
        onValueChange: setPageSize,
        children: [/* @__PURE__ */ jsxRuntimeExports.jsx(SelectTrigger, {
          className: "w-[180px]",
          children: /* @__PURE__ */ jsxRuntimeExports.jsx(SelectValue, {
            placeholder: "Rows per page"
          })
        }), /* @__PURE__ */ jsxRuntimeExports.jsxs(SelectContent, {
          children: [/* @__PURE__ */ jsxRuntimeExports.jsx(SelectItem, {
            value: "5",
            children: "5 per page"
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(SelectItem, {
            value: "10",
            children: "10 per page"
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(SelectItem, {
            value: "20",
            children: "20 per page"
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(SelectItem, {
            value: "50",
            children: "50 per page"
          })]
        })]
      })]
    }), /* @__PURE__ */ jsxRuntimeExports.jsxs(Table, {
      children: [/* @__PURE__ */ jsxRuntimeExports.jsxs(TableHeader, {
        className: "bg-gray-100",
        children: [/* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
          children: "Area Name"
        }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
          children: "Agent Name "
        }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
          children: "      "
        })]
      }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableBody, {
        children: paginatedData.length > 0 ? paginatedData == null ? void 0 : paginatedData.sort((a, b) => a.networkAreaName.localeCompare(b.networkAreaName)).map((area) => /* @__PURE__ */ jsxRuntimeExports.jsxs(TableRow, {
          children: [/* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
            children: area.networkAreaName
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
            children: area.agentName
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
            children: /* @__PURE__ */ jsxRuntimeExports.jsx(SquarePen, {
              className: "h-4 w-4"
            })
          })]
        }, area.networkAreaId)) : /* @__PURE__ */ jsxRuntimeExports.jsx(TableRow, {
          children: /* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
            colSpan: 9,
            className: "h-24 text-center",
            children: "No results."
          })
        })
      })]
    }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
      className: "flex items-center space-x-2",
      children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
        variant: "outline",
        size: "sm",
        onClick: () => setCurrentPage((prev) => Math.max(prev - 1, 1)),
        disabled: currentPage === 1,
        children: "Previous"
      }), /* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
        variant: "outline",
        size: "sm",
        onClick: () => setCurrentPage((prev) => Math.min(prev + 1, totalPages)),
        disabled: currentPage === totalPages,
        children: "Next"
      })]
    })]
  });
}
export {
  NetWorkAreas as default
};
//# sourceMappingURL=home.netWorkAreas-BZhAmV6a.js.map
