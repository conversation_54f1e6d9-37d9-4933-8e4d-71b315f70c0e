import { r as reactExports, j as jsxRuntimeExports } from "./jsx-runtime-bB2y7OuD.js";
import { B as Button } from "./button-ByAXMyvk.js";
import { C as Card, a as CardContent } from "./card-BJQMSLe_.js";
import { S as Switch } from "./switch-DQ_qW6ch.js";
import { L as Label } from "./label-cSASrwzW.js";
import { c as constructFrom, f as format } from "./format-82yT_5--.js";
import { i as isSameDay } from "./isSameDay-BQMn9z7h.js";
import { a as addDays } from "./addDays-CyH8qBoF.js";
import { T as Truck } from "./truck-ypDO_-A_.js";
import { T as TriangleAlert } from "./triangle-alert-BYC5ld_8.js";
import { p as parseISO } from "./parseISO-COJrHI78.js";
import "./index-z_byfFrQ.js";
import "./index-ImHKLo0a.js";
import "./utils-GkgzjW3c.js";
import "./index-D7VH9Fc8.js";
import "./index-dIGjFc0I.js";
import "./index-CpKiYcZd.js";
import "./index-CkL5tk39.js";
import "./index-CzZ6EYkN.js";
import "./index-Vp2vNLNM.js";
import "./createLucideIcon-uwkRm45G.js";
function constructNow(date) {
  return constructFrom(date, Date.now());
}
function isToday(date) {
  return isSameDay(date, constructNow(date));
}
function isTomorrow(date) {
  return isSameDay(date, addDays(constructNow(date), 1));
}
const mockData = [{
  date: "2024-10-23",
  timeSlot: "07:00 AM - 10:00 AM",
  orders: 50,
  tonnage: 5e3,
  trucks: 5,
  deliveryPercentage: 55,
  credit: 7600,
  cashCollected: 76e3,
  isOrdering: false
}, {
  date: "2024-10-24",
  timeSlot: "10:00 AM - 01:00 PM",
  orders: 30,
  tonnage: 3e3,
  trucks: 3,
  deliveryPercentage: 60,
  credit: 5e3,
  cashCollected: 5e4,
  isOrdering: false
}, {
  date: "2024-10-25",
  timeSlot: "01:00 PM - 04:00 PM",
  orders: 40,
  tonnage: 4e3,
  trucks: 4,
  deliveryPercentage: 70,
  credit: 6e3,
  cashCollected: 6e4,
  routePlanningPending: true,
  isOrdering: false
}];
function HomeDelivery() {
  const [slots, setSlots] = reactExports.useState(mockData);
  const toggleOrdering = (index) => {
    setSlots((prevSlots) => prevSlots.map((slot, i) => i === index ? {
      ...slot,
      isOrdering: !slot.isOrdering
    } : slot));
  };
  if (slots.length === 0) {
    return /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
      children: "No delivery slots available."
    });
  }
  return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
    className: "container mx-auto p-4",
    children: [/* @__PURE__ */ jsxRuntimeExports.jsx("h1", {
      className: "text-2xl font-bold mb-6",
      children: "HomeDelivery Slots"
    }), /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
      className: "space-y-6",
      children: slots.map((slot, index) => /* @__PURE__ */ jsxRuntimeExports.jsx(DeliverySlot, {
        slot,
        onToggleOrdering: () => toggleOrdering(index)
      }, index))
    })]
  });
}
function DeliverySlot({
  slot,
  onToggleOrdering
}) {
  const slotDate = parseISO(slot.date);
  let dateDisplay;
  if (isToday(slotDate)) {
    dateDisplay = "Today";
  } else if (isTomorrow(slotDate)) {
    dateDisplay = "Tomorrow";
  } else {
    dateDisplay = format(slotDate, "MMM d, yyyy");
  }
  return /* @__PURE__ */ jsxRuntimeExports.jsx(Card, {
    children: /* @__PURE__ */ jsxRuntimeExports.jsxs(CardContent, {
      className: "p-4",
      children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
        className: "flex flex-wrap justify-between items-center mb-4",
        children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("h2", {
          className: "text-lg font-semibold",
          children: ["Slot: ", dateDisplay, " ", slot.timeSlot]
        }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
          className: "flex items-center space-x-2 mt-2 sm:mt-0",
          children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Switch, {
            id: `ordering-mode-${slot.date}`,
            checked: slot.isOrdering,
            onCheckedChange: onToggleOrdering
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(Label, {
            htmlFor: `ordering-mode-${slot.date}`,
            children: "Ordering"
          })]
        })]
      }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
        className: "grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-4",
        children: [/* @__PURE__ */ jsxRuntimeExports.jsx(StatSquare, {
          label: "Orders",
          value: slot.orders
        }), /* @__PURE__ */ jsxRuntimeExports.jsx(StatSquare, {
          label: "Tonnage",
          value: `${slot.tonnage} KG`
        }), /* @__PURE__ */ jsxRuntimeExports.jsx(StatSquare, {
          label: "Trucks",
          value: slot.trucks,
          icon: /* @__PURE__ */ jsxRuntimeExports.jsx(Truck, {
            className: "w-4 h-4"
          })
        }), /* @__PURE__ */ jsxRuntimeExports.jsx(StatSquare, {
          label: "HomeDelivery%",
          value: `${slot.deliveryPercentage}%`
        }), /* @__PURE__ */ jsxRuntimeExports.jsx(StatSquare, {
          label: "Credit",
          value: `₹${slot.credit.toLocaleString()}`
        }), /* @__PURE__ */ jsxRuntimeExports.jsx(StatSquare, {
          label: "Cash collected",
          value: `₹${slot.cashCollected.toLocaleString()}`
        })]
      }), slot.routePlanningPending && /* @__PURE__ */ jsxRuntimeExports.jsx(WarningSquare, {
        label: "Route planning Pending",
        className: "mt-4"
      }), /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
        className: "flex justify-end mt-4",
        children: /* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
          variant: "outline",
          children: "View/Edit Items"
        })
      })]
    })
  });
}
function StatSquare({
  label,
  value,
  icon = null
}) {
  return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
    className: "bg-muted p-3 rounded",
    children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
      className: "text-sm text-muted-foreground mb-1 flex items-center gap-1",
      children: [icon, label]
    }), /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
      className: "font-semibold",
      children: value
    })]
  });
}
function WarningSquare({
  label,
  className = ""
}) {
  return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
    className: `bg-red-100 p-3 rounded flex items-center space-x-2 ${className}`,
    children: [/* @__PURE__ */ jsxRuntimeExports.jsx(TriangleAlert, {
      className: "text-red-500 w-5 h-5"
    }), /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
      className: "text-sm font-medium text-red-700",
      children: label
    })]
  });
}
export {
  HomeDelivery as default
};
//# sourceMappingURL=home.delivery-XchKanLj.js.map
