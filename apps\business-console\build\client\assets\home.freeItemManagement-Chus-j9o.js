import { j as jsxRuntimeExports, R as React, r as reactExports } from "./jsx-runtime-bB2y7OuD.js";
import { B as Button } from "./button-ByAXMyvk.js";
import { I as Input } from "./input-3v87qohQ.js";
import { C as Card, b as CardHeader, a as CardContent, e as CardFooter } from "./card-BJQMSLe_.js";
import { B as Badge } from "./badge-BsHDHlRV.js";
import { D as Dialog, a as DialogContent, c as DialogHeader, b as DialogTitle } from "./dialog-BqKosxNq.js";
import { t as toast } from "./use-toast-EUd7m8UG.js";
import { u as useLoaderData, a as useFetcher } from "./components-D7UvGag_.js";
import { S as Search } from "./search-DzDJ71yc.js";
import "./index-z_byfFrQ.js";
import "./index-ImHKLo0a.js";
import "./utils-GkgzjW3c.js";
import "./index-DdafHWkt.js";
import "./index-D7VH9Fc8.js";
import "./index-DVTNuYOr.js";
import "./index-dIGjFc0I.js";
import "./index-BXzPK7u0.js";
import "./index-CzZ6EYkN.js";
import "./index-Vp2vNLNM.js";
import "./index-CEHS9zzk.js";
import "./index-QLGF6kQx.js";
import "./x-CCG_WJDF.js";
import "./createLucideIcon-uwkRm45G.js";
import "./index-DhHTcibu.js";
const DISCOUNT_TYPES = [
  { value: "percentage", label: "Percentage Discount" },
  { value: "flat", label: "Flat Discount" },
  { value: "freeitem", label: "Free Item" },
  { value: "buyonegetone", label: "Buy One Get One" }
];
const formatDate = (date) => {
  const d = typeof date === "string" ? new Date(date) : date;
  return d.toLocaleDateString(void 0, {
    day: "2-digit",
    month: "short",
    year: "numeric"
  });
};
const FreeItemCard = ({ item, onEdit }) => {
  const getDiscountTypeLabel = (type) => {
    var _a;
    return ((_a = DISCOUNT_TYPES.find((t) => t.value === type)) == null ? void 0 : _a.label) || type;
  };
  const getDiscountValue = () => {
    switch (item.discountType) {
      case "percentage":
        return `${item.discountPercentage}% Off`;
      case "flat":
        return `₹${item.discountFlat} Off`;
      case "freeitem":
        return `Free Item x${item.freeItemQty}`;
      case "buyonegetone":
        return "Buy 1 Get 1 Free";
      default:
        return "Special Offer";
    }
  };
  const now = /* @__PURE__ */ new Date();
  const isActive = now >= item.validFrom && now <= item.validTo && !item.discountDisabled;
  return /* @__PURE__ */ jsxRuntimeExports.jsxs(Card, { className: "h-full overflow-hidden shadow-md hover:shadow-lg transition-shadow duration-300", children: [
    /* @__PURE__ */ jsxRuntimeExports.jsx(CardHeader, { className: "p-4", children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex justify-between items-start", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex flex-col", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx("p", { className: "text-lg font-semibold", children: getDiscountValue() }),
        /* @__PURE__ */ jsxRuntimeExports.jsx("p", { className: "text-sm text-muted-foreground", children: getDiscountTypeLabel(item.discountType) })
      ] }),
      /* @__PURE__ */ jsxRuntimeExports.jsx(Badge, { variant: isActive ? "default" : "outline", className: isActive ? "bg-green-500" : "", children: isActive ? "Active" : "Inactive" })
    ] }) }),
    /* @__PURE__ */ jsxRuntimeExports.jsx(CardContent, { className: "p-4 pt-0", children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "space-y-2", children: [
      item.discountMinOrderQty && /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center justify-between", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "text-sm text-muted-foreground", children: "Min. Order" }),
        /* @__PURE__ */ jsxRuntimeExports.jsxs("span", { className: "font-medium", children: [
          "₹",
          item.discountMinOrderQty
        ] })
      ] }),
      item.discountUpto && /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center justify-between", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "text-sm text-muted-foreground", children: "Max Discount" }),
        /* @__PURE__ */ jsxRuntimeExports.jsxs("span", { className: "font-medium", children: [
          "₹",
          item.discountUpto
        ] })
      ] }),
      /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center justify-between", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "text-sm text-muted-foreground", children: "Valid From" }),
        /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "font-medium", children: formatDate(item.validFrom) })
      ] }),
      /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center justify-between", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "text-sm text-muted-foreground", children: "Valid Until" }),
        /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "font-medium", children: formatDate(item.validTo) })
      ] })
    ] }) }),
    /* @__PURE__ */ jsxRuntimeExports.jsx(CardFooter, { className: "p-4 pt-0", children: /* @__PURE__ */ jsxRuntimeExports.jsx(
      Button,
      {
        variant: "outline",
        className: "w-full",
        onClick: () => onEdit(item),
        children: "Edit Offer"
      }
    ) })
  ] });
};
const defaultValues = {
  discountType: "percentage",
  discountPercentage: null,
  discountFlat: null,
  discountUpto: null,
  discountMinOrderQty: null,
  validFrom: /* @__PURE__ */ new Date(),
  validTo: new Date((/* @__PURE__ */ new Date()).setMonth((/* @__PURE__ */ new Date()).getMonth() + 1)),
  discountDisabled: false,
  freeItemId: null,
  freeItemQty: null
};
const FreeItemForm = ({ initialData, onSubmit }) => {
  const [form, setForm] = React.useState(initialData || defaultValues);
  reactExports.useEffect(() => {
    if (initialData) setForm(initialData);
  }, [initialData]);
  const handleChange = (e) => {
    const target = e.target;
    const { name, value, type } = target;
    setForm((prev) => ({
      ...prev,
      [name]: type === "checkbox" ? target.checked : value
    }));
  };
  const handleDateChange = (name, value) => {
    setForm((prev) => ({ ...prev, [name]: new Date(value) }));
  };
  const handleSubmit = (e) => {
    e.preventDefault();
    onSubmit(form);
  };
  return /* @__PURE__ */ jsxRuntimeExports.jsxs("form", { onSubmit: handleSubmit, className: "space-y-6", children: [
    /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx("label", { className: "block font-medium mb-1", children: "Discount Type" }),
      /* @__PURE__ */ jsxRuntimeExports.jsxs(
        "select",
        {
          name: "discountType",
          value: form.discountType,
          onChange: handleChange,
          className: "w-full border rounded px-3 py-2",
          children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx("option", { value: "percentage", children: "Percentage" }),
            /* @__PURE__ */ jsxRuntimeExports.jsx("option", { value: "flat", children: "Flat" }),
            /* @__PURE__ */ jsxRuntimeExports.jsx("option", { value: "freeitem", children: "Free Item" })
          ]
        }
      )
    ] }),
    form.discountType === "percentage" && /* @__PURE__ */ jsxRuntimeExports.jsxs(jsxRuntimeExports.Fragment, { children: [
      /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx("label", { className: "block font-medium mb-1", children: "Discount Percentage (%)" }),
        /* @__PURE__ */ jsxRuntimeExports.jsx(
          "input",
          {
            type: "number",
            name: "discountPercentage",
            min: 0,
            max: 100,
            value: form.discountPercentage ?? "",
            onChange: handleChange,
            className: "w-full border rounded px-3 py-2",
            placeholder: "Enter percentage"
          }
        )
      ] }),
      /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx("label", { className: "block font-medium mb-1", children: "Discount Up To (₹)" }),
        /* @__PURE__ */ jsxRuntimeExports.jsx(
          "input",
          {
            type: "number",
            name: "discountUpto",
            min: 0,
            value: form.discountUpto ?? "",
            onChange: handleChange,
            className: "w-full border rounded px-3 py-2",
            placeholder: "Enter maximum discount amount"
          }
        )
      ] })
    ] }),
    form.discountType === "flat" && /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx("label", { className: "block font-medium mb-1", children: "Flat Discount Amount (₹)" }),
      /* @__PURE__ */ jsxRuntimeExports.jsx(
        "input",
        {
          type: "number",
          name: "discountFlat",
          min: 0,
          value: form.discountFlat ?? "",
          onChange: handleChange,
          className: "w-full border rounded px-3 py-2",
          placeholder: "Enter flat discount amount"
        }
      )
    ] }),
    form.discountType === "freeitem" && /* @__PURE__ */ jsxRuntimeExports.jsxs(jsxRuntimeExports.Fragment, { children: [
      /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx("label", { className: "block font-medium mb-1", children: "Free Item" }),
        /* @__PURE__ */ jsxRuntimeExports.jsx(
          "input",
          {
            type: "text",
            name: "freeItemId",
            value: form.freeItemId ?? "",
            onChange: handleChange,
            className: "w-full border rounded px-3 py-2",
            placeholder: "Enter free item ID"
          }
        )
      ] }),
      /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx("label", { className: "block font-medium mb-1", children: "Free Item Quantity" }),
        /* @__PURE__ */ jsxRuntimeExports.jsx(
          "input",
          {
            type: "number",
            name: "freeItemQty",
            min: 1,
            value: form.freeItemQty ?? "",
            onChange: handleChange,
            className: "w-full border rounded px-3 py-2",
            placeholder: "Enter quantity"
          }
        )
      ] })
    ] }),
    /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx("label", { className: "block font-medium mb-1", children: "Minimum Order Amount (₹)" }),
      /* @__PURE__ */ jsxRuntimeExports.jsx(
        "input",
        {
          type: "number",
          name: "discountMinOrderQty",
          min: 0,
          value: form.discountMinOrderQty ?? "",
          onChange: handleChange,
          className: "w-full border rounded px-3 py-2",
          placeholder: "Enter minimum order amount"
        }
      )
    ] }),
    /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "grid grid-cols-1 md:grid-cols-2 gap-6", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx("label", { className: "block font-medium mb-1", children: "Valid From" }),
        /* @__PURE__ */ jsxRuntimeExports.jsx(
          "input",
          {
            type: "date",
            name: "validFrom",
            value: form.validFrom ? new Date(form.validFrom).toISOString().split("T")[0] : "",
            onChange: (e) => handleDateChange("validFrom", e.target.value),
            className: "w-full border rounded px-3 py-2"
          }
        )
      ] }),
      /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx("label", { className: "block font-medium mb-1", children: "Valid To" }),
        /* @__PURE__ */ jsxRuntimeExports.jsx(
          "input",
          {
            type: "date",
            name: "validTo",
            value: form.validTo ? new Date(form.validTo).toISOString().split("T")[0] : "",
            onChange: (e) => handleDateChange("validTo", e.target.value),
            className: "w-full border rounded px-3 py-2"
          }
        )
      ] })
    ] }),
    /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center gap-2", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx(
        "input",
        {
          type: "checkbox",
          name: "discountDisabled",
          checked: form.discountDisabled,
          onChange: handleChange,
          className: "h-4 w-4"
        }
      ),
      /* @__PURE__ */ jsxRuntimeExports.jsx("label", { className: "font-medium", children: "Disable Discount" })
    ] }),
    /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "flex justify-end space-x-4 pt-4", children: /* @__PURE__ */ jsxRuntimeExports.jsxs("button", { type: "submit", className: "bg-blue-600 text-white px-4 py-2 rounded", children: [
      (initialData == null ? void 0 : initialData.id) ? "Update" : "Create",
      " Free Item Offer"
    ] }) })
  ] });
};
const FreeItemModal = ({
  isOpen,
  onClose,
  onSubmit,
  initialData
}) => {
  const handleSubmit = (data) => {
    onSubmit(data);
    toast({
      description: (initialData == null ? void 0 : initialData.id) ? "Free item offer updated!" : "Free item offer created!"
    });
    onClose();
  };
  return /* @__PURE__ */ jsxRuntimeExports.jsx(Dialog, { open: isOpen, onOpenChange: onClose, children: /* @__PURE__ */ jsxRuntimeExports.jsxs(DialogContent, { className: "sm:max-w-[600px]", children: [
    /* @__PURE__ */ jsxRuntimeExports.jsx(DialogHeader, { children: /* @__PURE__ */ jsxRuntimeExports.jsxs(DialogTitle, { children: [
      (initialData == null ? void 0 : initialData.id) ? "Edit" : "Create",
      " Free Item Offer"
    ] }) }),
    /* @__PURE__ */ jsxRuntimeExports.jsx(FreeItemForm, { initialData, onSubmit: handleSubmit })
  ] }) });
};
const Index = () => {
  const {
    items: initialItems
  } = useLoaderData();
  const fetcher = useFetcher();
  const [items, setItems] = reactExports.useState(initialItems);
  const [searchQuery, setSearchQuery] = reactExports.useState("");
  const [modalOpen, setModalOpen] = reactExports.useState(false);
  const [currentItem, setCurrentItem] = reactExports.useState(void 0);
  reactExports.useEffect(() => {
    var _a;
    if (((_a = fetcher.data) == null ? void 0 : _a.success) && fetcher.data.item) {
      setItems((prev) => {
        const exists = prev.some((i) => i.id === fetcher.data.item.id);
        if (exists) {
          return prev.map((i) => i.id === fetcher.data.item.id ? fetcher.data.item : i);
        } else {
          return [...prev, fetcher.data.item];
        }
      });
      setModalOpen(false);
    }
  }, [fetcher.data]);
  const filteredItems = reactExports.useMemo(() => {
    return items.filter((item) => {
      const searchLower = searchQuery.toLowerCase();
      if (item.discountType && item.discountType.toLowerCase().includes(searchLower)) return true;
      if (item.discountPercentage && item.discountPercentage.toString().includes(searchQuery)) return true;
      if (item.discountFlat && item.discountFlat.toString().includes(searchQuery)) return true;
      if (item.discountMinOrderQty && item.discountMinOrderQty.toString().includes(searchQuery)) return true;
      return false;
    });
  }, [items, searchQuery]);
  const handleCreateClick = () => {
    setCurrentItem(void 0);
    setModalOpen(true);
  };
  const handleEditItem = (item) => {
    setCurrentItem(item);
    setModalOpen(true);
  };
  const handleSubmitItem = (data) => {
    const formData = new FormData();
    Object.entries(data).forEach(([key, value]) => {
      if (value !== void 0 && value !== null) formData.append(key, String(value));
    });
    formData.append("_intent", data.id ? "update" : "create");
    fetcher.submit(formData, {
      method: "post"
    });
  };
  return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
    className: "container mx-auto py-8 px-4",
    children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
      className: "flex flex-col md:flex-row justify-between items-center mb-8",
      children: [/* @__PURE__ */ jsxRuntimeExports.jsx("h1", {
        className: "text-3xl font-bold mb-4 md:mb-0",
        children: "Free Item Offers"
      }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
        className: "w-full md:w-auto flex flex-col md:flex-row space-y-4 md:space-y-0 md:space-x-4",
        children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
          className: "relative w-full md:w-64",
          children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Search, {
            className: "absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(Input, {
            placeholder: "Search offers...",
            className: "pl-10",
            value: searchQuery,
            onChange: (e) => setSearchQuery(e.target.value)
          })]
        }), /* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
          onClick: handleCreateClick,
          className: "whitespace-nowrap",
          children: "Create Free Item Offer"
        })]
      })]
    }), filteredItems.length > 0 ? /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
      className: "grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6",
      children: filteredItems.map((item) => /* @__PURE__ */ jsxRuntimeExports.jsx(FreeItemCard, {
        item,
        onEdit: handleEditItem
      }, item.id))
    }) : /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
      className: "text-center py-20 bg-muted bg-opacity-50 rounded-lg",
      children: [/* @__PURE__ */ jsxRuntimeExports.jsx("h3", {
        className: "text-xl font-medium text-gray-600",
        children: "No offers found"
      }), /* @__PURE__ */ jsxRuntimeExports.jsx("p", {
        className: "text-gray-500 mt-2",
        children: searchQuery ? "Try adjusting your search" : "Create your first free item offer"
      }), !searchQuery && /* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
        onClick: handleCreateClick,
        className: "mt-4",
        variant: "default",
        children: "Create Offer"
      })]
    }), /* @__PURE__ */ jsxRuntimeExports.jsx(FreeItemModal, {
      isOpen: modalOpen,
      onClose: () => setModalOpen(false),
      onSubmit: handleSubmitItem,
      initialData: currentItem
    })]
  });
};
export {
  Index as default
};
//# sourceMappingURL=home.freeItemManagement-Chus-j9o.js.map
