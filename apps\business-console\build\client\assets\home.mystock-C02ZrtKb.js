import { r as reactExports, j as jsxRuntimeExports } from "./jsx-runtime-bB2y7OuD.js";
import { u as useToast } from "./use-toast-EUd7m8UG.js";
import { S as Select, a as SelectTrigger, b as SelectValue, c as SelectContent, d as SelectItem } from "./select-BFGSXKcr.js";
import { I as Input } from "./input-3v87qohQ.js";
import { L as Layout } from "./root-BYpxmD3T.js";
import { T as Table, a as TableHeader, b as TableRow, c as TableHead, d as TableBody, e as TableCell } from "./table-CAxdJZsY.js";
import { B as Button } from "./button-ByAXMyvk.js";
import { S as Switch } from "./switch-DQ_qW6ch.js";
import { u as useLoaderData, a as useFetcher, L as Link } from "./components-D7UvGag_.js";
import "./index-Vp2vNLNM.js";
import "./index-IXOTxK3N.js";
import "./index-D7VH9Fc8.js";
import "./index-qCtcpOcW.js";
import "./index-z_byfFrQ.js";
import "./index-BTdCMChR.js";
import "./index-BXzPK7u0.js";
import "./index-CzZ6EYkN.js";
import "./index-CEHS9zzk.js";
import "./index-DVTNuYOr.js";
import "./index-DscYByPT.js";
import "./index-CkL5tk39.js";
import "./index-dIGjFc0I.js";
import "./index-CpKiYcZd.js";
import "./index-C88PRvfd.js";
import "./utils-GkgzjW3c.js";
import "./chevron-down-pCP5jmjX.js";
import "./createLucideIcon-uwkRm45G.js";
import "./check-_dbWxIzT.js";
import "./index-DhHTcibu.js";
import "./ToastProvider-rciVe3-g.js";
import "./index-QLGF6kQx.js";
import "./index-ImHKLo0a.js";
import "./x-CCG_WJDF.js";
function MyStock() {
  const {
    stockItems,
    pageNo,
    pageSize,
    sellerId
  } = useLoaderData();
  const [items, setItems] = reactExports.useState(stockItems);
  const [searchTerm, setSearchTerm] = reactExports.useState("");
  const [sortBy, setSortBy] = reactExports.useState("itemName");
  const {
    toast
  } = useToast();
  reactExports.useEffect(() => {
    if (stockItems) {
      setItems(stockItems);
    }
  }, [stockItems]);
  const fetcher = useFetcher();
  reactExports.useEffect(() => {
    var _a, _b, _c;
    if (fetcher.state === "idle" && ((_a = fetcher.data) == null ? void 0 : _a.success) && fetcher.data.stockData) {
      setItems((prevItems) => prevItems.map((item) => {
        var _a2, _b2, _c2;
        return item.stockId === ((_b2 = (_a2 = fetcher.data) == null ? void 0 : _a2.stockData) == null ? void 0 : _b2.stockId) ? (_c2 = fetcher.data) == null ? void 0 : _c2.stockData : item;
      }));
      toast({
        title: "Stock updated successfully",
        variant: "default",
        description: "Stock updated successfully"
      });
    } else if (fetcher.state === "idle" && ((_b = fetcher.data) == null ? void 0 : _b.error)) {
      toast({
        title: "Something went wrong",
        variant: "destructive",
        description: (_c = fetcher.data) == null ? void 0 : _c.error
      });
    }
  }, [fetcher.state, fetcher.data]);
  const filteredItems = items.filter((item) => item.itemName.toLowerCase().includes(searchTerm.toLowerCase()) || item.distributor.toLowerCase().includes(searchTerm.toLowerCase()));
  const sortedItems = [...filteredItems].sort((a, b) => {
    switch (sortBy) {
      case "itemName":
        return a.itemName.localeCompare(b.itemName);
      case "distributerName":
        return a.distributor.localeCompare(b.distributor);
      case "myPrice":
        return b.pricePerUnit - a.pricePerUnit;
      case "newOrders":
        return b.maxAvailableQty - a.maxAvailableQty;
      case "active":
        return a.active === b.active ? 0 : a.active ? -1 : 1;
      default:
        return 0;
    }
  });
  return /* @__PURE__ */ jsxRuntimeExports.jsxs(Layout, {
    children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
      className: "mb-8",
      children: [/* @__PURE__ */ jsxRuntimeExports.jsx("h1", {
        className: "text-3xl font-bold tracking-tight",
        children: "My Stock"
      }), /* @__PURE__ */ jsxRuntimeExports.jsx("p", {
        className: "text-muted-foreground mt-2",
        children: "Manage your inventory items and track stock levels"
      })]
    }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
      className: "flex flex-col md:flex-row gap-4 mb-6",
      children: [/* @__PURE__ */ jsxRuntimeExports.jsx("div", {
        className: "flex-1",
        children: /* @__PURE__ */ jsxRuntimeExports.jsx(Input, {
          placeholder: "Search by item name or distributer...",
          value: searchTerm,
          onChange: (e) => setSearchTerm(e.target.value),
          className: "max-w-md"
        })
      }), /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
        className: "w-full md:w-48",
        children: /* @__PURE__ */ jsxRuntimeExports.jsxs(Select, {
          value: sortBy,
          onValueChange: setSortBy,
          children: [/* @__PURE__ */ jsxRuntimeExports.jsx(SelectTrigger, {
            children: /* @__PURE__ */ jsxRuntimeExports.jsx(SelectValue, {
              placeholder: "Sort by"
            })
          }), /* @__PURE__ */ jsxRuntimeExports.jsxs(SelectContent, {
            children: [/* @__PURE__ */ jsxRuntimeExports.jsx(SelectItem, {
              value: "itemName",
              children: "Item Name"
            }), /* @__PURE__ */ jsxRuntimeExports.jsx(SelectItem, {
              value: "distributerName",
              children: "Distributer"
            }), /* @__PURE__ */ jsxRuntimeExports.jsx(SelectItem, {
              value: "inStock",
              children: "Stock Level"
            }), /* @__PURE__ */ jsxRuntimeExports.jsx(SelectItem, {
              value: "myPrice",
              children: "Price"
            }), /* @__PURE__ */ jsxRuntimeExports.jsx(SelectItem, {
              value: "newOrders",
              children: "New Orders"
            }), /* @__PURE__ */ jsxRuntimeExports.jsx(SelectItem, {
              value: "active",
              children: "Active Status"
            })]
          })]
        })
      })]
    }), /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
      className: "rounded-md border",
      children: /* @__PURE__ */ jsxRuntimeExports.jsxs(Table, {
        children: [/* @__PURE__ */ jsxRuntimeExports.jsx(TableHeader, {
          children: /* @__PURE__ */ jsxRuntimeExports.jsxs(TableRow, {
            children: [/* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
              children: "Item Name"
            }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
              children: "Distributer"
            }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
              children: "Avail QTY"
            }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
              children: "My Price"
            }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
              children: "Active"
            })]
          })
        }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableBody, {
          children: sortedItems.length > 0 ? sortedItems.map((item) => /* @__PURE__ */ jsxRuntimeExports.jsxs(TableRow, {
            children: [/* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
              children: /* @__PURE__ */ jsxRuntimeExports.jsx(Link, {
                to: `/home/<USER>/${item.stockId}/transactions?sellerId=${sellerId}`,
                className: "font-medium text-primary hover:underline",
                children: item.itemName
              })
            }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
              children: item.distributor
            }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
              children: item.maxAvailableQty > 0 ? /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                className: "flex items-center gap-2",
                children: [/* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                  className: "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800",
                  children: item.maxAvailableQty
                }), /* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
                  variant: "ghost",
                  size: "sm",
                  onClick: () => {
                    const input = prompt("Enter new Qty:", item.maxAvailableQty.toString());
                    if (input === null) return;
                    const newQty = parseFloat(input);
                    if (isNaN(newQty)) return;
                    const formData = new FormData();
                    formData.append("stockId", item.stockId.toString());
                    formData.append("maxAvailableQty", newQty.toString());
                    if (sellerId !== void 0) {
                      formData.append("sellerId", sellerId.toString());
                    }
                    fetcher.submit(formData, {
                      method: "POST"
                    });
                  },
                  className: "h-7 px-2 text-xs",
                  children: "Edit"
                })]
              }) : "-"
            }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
              children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                className: "flex items-center gap-2",
                children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("span", {
                  children: ["₹ ", item.pricePerUnit.toFixed(2)]
                }), /* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
                  variant: "ghost",
                  size: "sm",
                  onClick: () => {
                    const input = prompt("Enter new price:", item.pricePerUnit.toString());
                    const formData = new FormData();
                    if (input === null) return;
                    const newPrice = parseFloat(input);
                    if (sellerId !== void 0) {
                      formData.append("sellerId", sellerId.toString());
                    }
                    formData.append("stockId", item.stockId.toString());
                    formData.append("pricePerUnit", newPrice.toString());
                    if (isNaN(newPrice)) return;
                    fetcher.submit(formData, {
                      method: "POST"
                    });
                  },
                  className: "h-7 px-2 text-xs",
                  children: "Edit"
                })]
              })
            }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
              children: /* @__PURE__ */ jsxRuntimeExports.jsx(Switch, {
                checked: item.active,
                onCheckedChange: (checked) => {
                  const formData = new FormData();
                  if (sellerId !== void 0) {
                    formData.append("sellerId", sellerId.toString());
                  }
                  formData.append("stockId", item.stockId.toString());
                  formData.append("active", checked.toString());
                  fetcher.submit(formData, {
                    method: "POST"
                  });
                }
              })
            })]
          }, item.stockId)) : /* @__PURE__ */ jsxRuntimeExports.jsx(TableRow, {
            children: /* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
              colSpan: 6,
              className: "text-center py-6 text-muted-foreground",
              children: "No items found. Try adjusting your search criteria."
            })
          })
        })]
      })
    })]
  });
}
export {
  MyStock as default
};
//# sourceMappingURL=home.mystock-C02ZrtKb.js.map
