import { j as jsxRuntimeExports } from "./jsx-runtime-bB2y7OuD.js";
import { B as Badge } from "./badge-BsHDHlRV.js";
import { B as Button } from "./button-ByAXMyvk.js";
import { C as Card, a as CardContent } from "./card-BJQMSLe_.js";
import { L as Layout } from "./root-BYpxmD3T.js";
import { u as useLoaderData } from "./components-D7UvGag_.js";
import { a as useNavigate, O as Outlet } from "./index-DhHTcibu.js";
import { A as ArrowLeft } from "./arrow-left-D_Ztdrp5.js";
import "./index-ImHKLo0a.js";
import "./utils-GkgzjW3c.js";
import "./index-z_byfFrQ.js";
import "./ToastProvider-rciVe3-g.js";
import "./use-toast-EUd7m8UG.js";
import "./index-Vp2vNLNM.js";
import "./index-D7VH9Fc8.js";
import "./index-qCtcpOcW.js";
import "./index-BXzPK7u0.js";
import "./index-CzZ6EYkN.js";
import "./index-QLGF6kQx.js";
import "./index-dIGjFc0I.js";
import "./index-C88PRvfd.js";
import "./x-CCG_WJDF.js";
import "./createLucideIcon-uwkRm45G.js";
function ItemDetail() {
  const {
    stock
  } = useLoaderData();
  const navigate = useNavigate();
  const handleBackClick = () => {
    navigate(-1);
  };
  return /* @__PURE__ */ jsxRuntimeExports.jsxs(Layout, {
    children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
      className: "flex flex-col gap-6",
      children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
        className: "flex items-center justify-between no-print",
        children: [/* @__PURE__ */ jsxRuntimeExports.jsxs(Button, {
          variant: "outline",
          size: "sm",
          onClick: handleBackClick,
          children: [/* @__PURE__ */ jsxRuntimeExports.jsx(ArrowLeft, {
            className: "mr-2 h-4 w-4"
          }), " Back", /* @__PURE__ */ jsxRuntimeExports.jsx("h1", {
            children: "MyStock"
          })]
        }), /* @__PURE__ */ jsxRuntimeExports.jsx("div", {})]
      }), /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
        className: "printable-area",
        children: /* @__PURE__ */ jsxRuntimeExports.jsx(Card, {
          className: "mb-6",
          children: /* @__PURE__ */ jsxRuntimeExports.jsx(CardContent, {
            className: "p-6",
            children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
              className: "flex flex-col md:flex-row justify-between gap-4",
              children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                children: [/* @__PURE__ */ jsxRuntimeExports.jsx("h1", {
                  className: "text-2xl font-bold",
                  children: stock.itemName
                }), /* @__PURE__ */ jsxRuntimeExports.jsxs("p", {
                  className: "text-muted-foreground",
                  children: ["Distributer: ", stock.distributor]
                })]
              }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                className: "flex flex-col gap-2",
                children: [/* @__PURE__ */ jsxRuntimeExports.jsx("div", {
                  className: "flex items-center gap-2",
                  children: /* @__PURE__ */ jsxRuntimeExports.jsx(Badge, {
                    variant: stock.active ? "default" : "secondary",
                    className: "font-normal",
                    children: stock.active ? "Active" : "Inactive"
                  })
                }), /* @__PURE__ */ jsxRuntimeExports.jsxs("p", {
                  className: "text-sm",
                  children: [/* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                    className: "font-medium",
                    children: "My Price:"
                  }), "₹ ", stock.pricePerUnit.toFixed(2)]
                }), stock.maxAvailableQty > 0 && /* @__PURE__ */ jsxRuntimeExports.jsxs("p", {
                  className: "text-sm text-blue-600",
                  children: [/* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                    className: "font-medium",
                    children: "MaxAvail Qty:"
                  }), " ", stock.maxAvailableQty]
                })]
              })]
            })
          })
        })
      })]
    }), /* @__PURE__ */ jsxRuntimeExports.jsx(Outlet, {})]
  });
}
export {
  ItemDetail as default
};
//# sourceMappingURL=home.selectedMyStock._stockId-SNCdhVSJ.js.map
