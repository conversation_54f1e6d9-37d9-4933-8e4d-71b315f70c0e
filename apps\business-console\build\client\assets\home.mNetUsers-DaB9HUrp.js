import { r as reactExports, j as jsxRuntimeExports } from "./jsx-runtime-bB2y7OuD.js";
import { I as Input } from "./input-3v87qohQ.js";
import { T as Table, a as TableHeader, b as TableRow, c as TableHead, d as TableBody, e as TableCell } from "./table-CAxdJZsY.js";
import "./utils-GkgzjW3c.js";
function MnetUsers() {
  const [searchTerm, setSearchTerm] = reactExports.useState("");
  return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
    className: "container mx-auto p-6",
    children: [/* @__PURE__ */ jsxRuntimeExports.jsx("div", {
      className: "flex justify-between items-center mb-6",
      children: /* @__PURE__ */ jsxRuntimeExports.jsx("h1", {
        className: "text-2xl font-bold",
        children: "mNet Users"
      })
    }), /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
      className: "flex justify-between mb-4",
      children: /* @__PURE__ */ jsxRuntimeExports.jsx(Input, {
        placeholder: "Search by name or owner",
        value: searchTerm,
        onChange: (e) => setSearchTerm(e.target.value),
        className: "max-w-sm"
      })
    }), /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
      className: "rounded-md border",
      children: /* @__PURE__ */ jsxRuntimeExports.jsxs(Table, {
        children: [/* @__PURE__ */ jsxRuntimeExports.jsx(TableHeader, {
          children: /* @__PURE__ */ jsxRuntimeExports.jsxs(TableRow, {
            children: [/* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
              children: "ID"
            }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
              children: "User"
            }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
              children: " Roles"
            }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
              children: "Enabled"
            })]
          })
        }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableBody, {}), /* @__PURE__ */ jsxRuntimeExports.jsx(TableRow, {
          children: /* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
            colSpan: 9,
            className: "h-24 text-center",
            children: "No results."
          })
        })]
      })
    })]
  });
}
export {
  MnetUsers as default
};
//# sourceMappingURL=home.mNetUsers-DaB9HUrp.js.map
