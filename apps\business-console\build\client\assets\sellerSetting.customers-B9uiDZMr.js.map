{"version": 3, "file": "sellerSetting.customers-B9uiDZMr.js", "sources": ["../../../node_modules/recharts/es6/cartesian/Line.js", "../../../node_modules/recharts/es6/chart/LineChart.js", "../../../app/components/common/ConversionRate.tsx", "../../../app/components/common/CustomerAcquisation.tsx", "../../../app/components/common/CustomerSales.tsx", "../../../app/routes/sellerSetting.customers.tsx"], "sourcesContent": ["var _excluded = [\"type\", \"layout\", \"connectNulls\", \"ref\"],\n  _excluded2 = [\"key\"];\nfunction _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nfunction _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }\nfunction _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } } return target; }\nfunction _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _toConsumableArray(arr) { return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread(); }\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\nfunction _iterableToArray(iter) { if (typeof Symbol !== \"undefined\" && iter[Symbol.iterator] != null || iter[\"@@iterator\"] != null) return Array.from(iter); }\nfunction _arrayWithoutHoles(arr) { if (Array.isArray(arr)) return _arrayLikeToArray(arr); }\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor); } }\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, \"prototype\", { writable: false }); return Constructor; }\nfunction _callSuper(t, o, e) { return o = _getPrototypeOf(o), _possibleConstructorReturn(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], _getPrototypeOf(t).constructor) : o.apply(t, e)); }\nfunction _possibleConstructorReturn(self, call) { if (call && (_typeof(call) === \"object\" || typeof call === \"function\")) { return call; } else if (call !== void 0) { throw new TypeError(\"Derived constructors may only return object or undefined\"); } return _assertThisInitialized(self); }\nfunction _assertThisInitialized(self) { if (self === void 0) { throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); } return self; }\nfunction _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }\nfunction _getPrototypeOf(o) { _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function _getPrototypeOf(o) { return o.__proto__ || Object.getPrototypeOf(o); }; return _getPrototypeOf(o); }\nfunction _inherits(subClass, superClass) { if (typeof superClass !== \"function\" && superClass !== null) { throw new TypeError(\"Super expression must either be null or a function\"); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, writable: true, configurable: true } }); Object.defineProperty(subClass, \"prototype\", { writable: false }); if (superClass) _setPrototypeOf(subClass, superClass); }\nfunction _setPrototypeOf(o, p) { _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) { o.__proto__ = p; return o; }; return _setPrototypeOf(o, p); }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == _typeof(i) ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != _typeof(i)) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\n/**\n * @fileOverview Line\n */\nimport React, { PureComponent } from 'react';\nimport Animate from 'react-smooth';\nimport isFunction from 'lodash/isFunction';\nimport isNil from 'lodash/isNil';\nimport isEqual from 'lodash/isEqual';\nimport clsx from 'clsx';\nimport { Curve } from '../shape/Curve';\nimport { Dot } from '../shape/Dot';\nimport { Layer } from '../container/Layer';\nimport { LabelList } from '../component/LabelList';\nimport { ErrorBar } from './ErrorBar';\nimport { uniqueId, interpolateNumber } from '../util/DataUtils';\nimport { findAllByType, filterProps, hasClipDot } from '../util/ReactUtils';\nimport { Global } from '../util/Global';\nimport { getCateCoordinateOfLine, getValueByDataKey } from '../util/ChartUtils';\nexport var Line = /*#__PURE__*/function (_PureComponent) {\n  function Line() {\n    var _this;\n    _classCallCheck(this, Line);\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    _this = _callSuper(this, Line, [].concat(args));\n    _defineProperty(_this, \"state\", {\n      isAnimationFinished: true,\n      totalLength: 0\n    });\n    _defineProperty(_this, \"generateSimpleStrokeDasharray\", function (totalLength, length) {\n      return \"\".concat(length, \"px \").concat(totalLength - length, \"px\");\n    });\n    _defineProperty(_this, \"getStrokeDasharray\", function (length, totalLength, lines) {\n      var lineLength = lines.reduce(function (pre, next) {\n        return pre + next;\n      });\n\n      // if lineLength is 0 return the default when no strokeDasharray is provided\n      if (!lineLength) {\n        return _this.generateSimpleStrokeDasharray(totalLength, length);\n      }\n      var count = Math.floor(length / lineLength);\n      var remainLength = length % lineLength;\n      var restLength = totalLength - length;\n      var remainLines = [];\n      for (var i = 0, sum = 0; i < lines.length; sum += lines[i], ++i) {\n        if (sum + lines[i] > remainLength) {\n          remainLines = [].concat(_toConsumableArray(lines.slice(0, i)), [remainLength - sum]);\n          break;\n        }\n      }\n      var emptyLines = remainLines.length % 2 === 0 ? [0, restLength] : [restLength];\n      return [].concat(_toConsumableArray(Line.repeat(lines, count)), _toConsumableArray(remainLines), emptyLines).map(function (line) {\n        return \"\".concat(line, \"px\");\n      }).join(', ');\n    });\n    _defineProperty(_this, \"id\", uniqueId('recharts-line-'));\n    _defineProperty(_this, \"pathRef\", function (node) {\n      _this.mainCurve = node;\n    });\n    _defineProperty(_this, \"handleAnimationEnd\", function () {\n      _this.setState({\n        isAnimationFinished: true\n      });\n      if (_this.props.onAnimationEnd) {\n        _this.props.onAnimationEnd();\n      }\n    });\n    _defineProperty(_this, \"handleAnimationStart\", function () {\n      _this.setState({\n        isAnimationFinished: false\n      });\n      if (_this.props.onAnimationStart) {\n        _this.props.onAnimationStart();\n      }\n    });\n    return _this;\n  }\n  _inherits(Line, _PureComponent);\n  return _createClass(Line, [{\n    key: \"componentDidMount\",\n    value: function componentDidMount() {\n      if (!this.props.isAnimationActive) {\n        return;\n      }\n      var totalLength = this.getTotalLength();\n      this.setState({\n        totalLength: totalLength\n      });\n    }\n  }, {\n    key: \"componentDidUpdate\",\n    value: function componentDidUpdate() {\n      if (!this.props.isAnimationActive) {\n        return;\n      }\n      var totalLength = this.getTotalLength();\n      if (totalLength !== this.state.totalLength) {\n        this.setState({\n          totalLength: totalLength\n        });\n      }\n    }\n  }, {\n    key: \"getTotalLength\",\n    value: function getTotalLength() {\n      var curveDom = this.mainCurve;\n      try {\n        return curveDom && curveDom.getTotalLength && curveDom.getTotalLength() || 0;\n      } catch (err) {\n        return 0;\n      }\n    }\n  }, {\n    key: \"renderErrorBar\",\n    value: function renderErrorBar(needClip, clipPathId) {\n      if (this.props.isAnimationActive && !this.state.isAnimationFinished) {\n        return null;\n      }\n      var _this$props = this.props,\n        points = _this$props.points,\n        xAxis = _this$props.xAxis,\n        yAxis = _this$props.yAxis,\n        layout = _this$props.layout,\n        children = _this$props.children;\n      var errorBarItems = findAllByType(children, ErrorBar);\n      if (!errorBarItems) {\n        return null;\n      }\n      var dataPointFormatter = function dataPointFormatter(dataPoint, dataKey) {\n        return {\n          x: dataPoint.x,\n          y: dataPoint.y,\n          value: dataPoint.value,\n          errorVal: getValueByDataKey(dataPoint.payload, dataKey)\n        };\n      };\n      var errorBarProps = {\n        clipPath: needClip ? \"url(#clipPath-\".concat(clipPathId, \")\") : null\n      };\n      return /*#__PURE__*/React.createElement(Layer, errorBarProps, errorBarItems.map(function (item) {\n        return /*#__PURE__*/React.cloneElement(item, {\n          key: \"bar-\".concat(item.props.dataKey),\n          data: points,\n          xAxis: xAxis,\n          yAxis: yAxis,\n          layout: layout,\n          dataPointFormatter: dataPointFormatter\n        });\n      }));\n    }\n  }, {\n    key: \"renderDots\",\n    value: function renderDots(needClip, clipDot, clipPathId) {\n      var isAnimationActive = this.props.isAnimationActive;\n      if (isAnimationActive && !this.state.isAnimationFinished) {\n        return null;\n      }\n      var _this$props2 = this.props,\n        dot = _this$props2.dot,\n        points = _this$props2.points,\n        dataKey = _this$props2.dataKey;\n      var lineProps = filterProps(this.props, false);\n      var customDotProps = filterProps(dot, true);\n      var dots = points.map(function (entry, i) {\n        var dotProps = _objectSpread(_objectSpread(_objectSpread({\n          key: \"dot-\".concat(i),\n          r: 3\n        }, lineProps), customDotProps), {}, {\n          value: entry.value,\n          dataKey: dataKey,\n          cx: entry.x,\n          cy: entry.y,\n          index: i,\n          payload: entry.payload\n        });\n        return Line.renderDotItem(dot, dotProps);\n      });\n      var dotsProps = {\n        clipPath: needClip ? \"url(#clipPath-\".concat(clipDot ? '' : 'dots-').concat(clipPathId, \")\") : null\n      };\n      return /*#__PURE__*/React.createElement(Layer, _extends({\n        className: \"recharts-line-dots\",\n        key: \"dots\"\n      }, dotsProps), dots);\n    }\n  }, {\n    key: \"renderCurveStatically\",\n    value: function renderCurveStatically(points, needClip, clipPathId, props) {\n      var _this$props3 = this.props,\n        type = _this$props3.type,\n        layout = _this$props3.layout,\n        connectNulls = _this$props3.connectNulls,\n        ref = _this$props3.ref,\n        others = _objectWithoutProperties(_this$props3, _excluded);\n      var curveProps = _objectSpread(_objectSpread(_objectSpread({}, filterProps(others, true)), {}, {\n        fill: 'none',\n        className: 'recharts-line-curve',\n        clipPath: needClip ? \"url(#clipPath-\".concat(clipPathId, \")\") : null,\n        points: points\n      }, props), {}, {\n        type: type,\n        layout: layout,\n        connectNulls: connectNulls\n      });\n      return /*#__PURE__*/React.createElement(Curve, _extends({}, curveProps, {\n        pathRef: this.pathRef\n      }));\n    }\n  }, {\n    key: \"renderCurveWithAnimation\",\n    value: function renderCurveWithAnimation(needClip, clipPathId) {\n      var _this2 = this;\n      var _this$props4 = this.props,\n        points = _this$props4.points,\n        strokeDasharray = _this$props4.strokeDasharray,\n        isAnimationActive = _this$props4.isAnimationActive,\n        animationBegin = _this$props4.animationBegin,\n        animationDuration = _this$props4.animationDuration,\n        animationEasing = _this$props4.animationEasing,\n        animationId = _this$props4.animationId,\n        animateNewValues = _this$props4.animateNewValues,\n        width = _this$props4.width,\n        height = _this$props4.height;\n      var _this$state = this.state,\n        prevPoints = _this$state.prevPoints,\n        totalLength = _this$state.totalLength;\n      return /*#__PURE__*/React.createElement(Animate, {\n        begin: animationBegin,\n        duration: animationDuration,\n        isActive: isAnimationActive,\n        easing: animationEasing,\n        from: {\n          t: 0\n        },\n        to: {\n          t: 1\n        },\n        key: \"line-\".concat(animationId),\n        onAnimationEnd: this.handleAnimationEnd,\n        onAnimationStart: this.handleAnimationStart\n      }, function (_ref) {\n        var t = _ref.t;\n        if (prevPoints) {\n          var prevPointsDiffFactor = prevPoints.length / points.length;\n          var stepData = points.map(function (entry, index) {\n            var prevPointIndex = Math.floor(index * prevPointsDiffFactor);\n            if (prevPoints[prevPointIndex]) {\n              var prev = prevPoints[prevPointIndex];\n              var interpolatorX = interpolateNumber(prev.x, entry.x);\n              var interpolatorY = interpolateNumber(prev.y, entry.y);\n              return _objectSpread(_objectSpread({}, entry), {}, {\n                x: interpolatorX(t),\n                y: interpolatorY(t)\n              });\n            }\n\n            // magic number of faking previous x and y location\n            if (animateNewValues) {\n              var _interpolatorX = interpolateNumber(width * 2, entry.x);\n              var _interpolatorY = interpolateNumber(height / 2, entry.y);\n              return _objectSpread(_objectSpread({}, entry), {}, {\n                x: _interpolatorX(t),\n                y: _interpolatorY(t)\n              });\n            }\n            return _objectSpread(_objectSpread({}, entry), {}, {\n              x: entry.x,\n              y: entry.y\n            });\n          });\n          return _this2.renderCurveStatically(stepData, needClip, clipPathId);\n        }\n        var interpolator = interpolateNumber(0, totalLength);\n        var curLength = interpolator(t);\n        var currentStrokeDasharray;\n        if (strokeDasharray) {\n          var lines = \"\".concat(strokeDasharray).split(/[,\\s]+/gim).map(function (num) {\n            return parseFloat(num);\n          });\n          currentStrokeDasharray = _this2.getStrokeDasharray(curLength, totalLength, lines);\n        } else {\n          currentStrokeDasharray = _this2.generateSimpleStrokeDasharray(totalLength, curLength);\n        }\n        return _this2.renderCurveStatically(points, needClip, clipPathId, {\n          strokeDasharray: currentStrokeDasharray\n        });\n      });\n    }\n  }, {\n    key: \"renderCurve\",\n    value: function renderCurve(needClip, clipPathId) {\n      var _this$props5 = this.props,\n        points = _this$props5.points,\n        isAnimationActive = _this$props5.isAnimationActive;\n      var _this$state2 = this.state,\n        prevPoints = _this$state2.prevPoints,\n        totalLength = _this$state2.totalLength;\n      if (isAnimationActive && points && points.length && (!prevPoints && totalLength > 0 || !isEqual(prevPoints, points))) {\n        return this.renderCurveWithAnimation(needClip, clipPathId);\n      }\n      return this.renderCurveStatically(points, needClip, clipPathId);\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _filterProps;\n      var _this$props6 = this.props,\n        hide = _this$props6.hide,\n        dot = _this$props6.dot,\n        points = _this$props6.points,\n        className = _this$props6.className,\n        xAxis = _this$props6.xAxis,\n        yAxis = _this$props6.yAxis,\n        top = _this$props6.top,\n        left = _this$props6.left,\n        width = _this$props6.width,\n        height = _this$props6.height,\n        isAnimationActive = _this$props6.isAnimationActive,\n        id = _this$props6.id;\n      if (hide || !points || !points.length) {\n        return null;\n      }\n      var isAnimationFinished = this.state.isAnimationFinished;\n      var hasSinglePoint = points.length === 1;\n      var layerClass = clsx('recharts-line', className);\n      var needClipX = xAxis && xAxis.allowDataOverflow;\n      var needClipY = yAxis && yAxis.allowDataOverflow;\n      var needClip = needClipX || needClipY;\n      var clipPathId = isNil(id) ? this.id : id;\n      var _ref2 = (_filterProps = filterProps(dot, false)) !== null && _filterProps !== void 0 ? _filterProps : {\n          r: 3,\n          strokeWidth: 2\n        },\n        _ref2$r = _ref2.r,\n        r = _ref2$r === void 0 ? 3 : _ref2$r,\n        _ref2$strokeWidth = _ref2.strokeWidth,\n        strokeWidth = _ref2$strokeWidth === void 0 ? 2 : _ref2$strokeWidth;\n      var _ref3 = hasClipDot(dot) ? dot : {},\n        _ref3$clipDot = _ref3.clipDot,\n        clipDot = _ref3$clipDot === void 0 ? true : _ref3$clipDot;\n      var dotSize = r * 2 + strokeWidth;\n      return /*#__PURE__*/React.createElement(Layer, {\n        className: layerClass\n      }, needClipX || needClipY ? /*#__PURE__*/React.createElement(\"defs\", null, /*#__PURE__*/React.createElement(\"clipPath\", {\n        id: \"clipPath-\".concat(clipPathId)\n      }, /*#__PURE__*/React.createElement(\"rect\", {\n        x: needClipX ? left : left - width / 2,\n        y: needClipY ? top : top - height / 2,\n        width: needClipX ? width : width * 2,\n        height: needClipY ? height : height * 2\n      })), !clipDot && /*#__PURE__*/React.createElement(\"clipPath\", {\n        id: \"clipPath-dots-\".concat(clipPathId)\n      }, /*#__PURE__*/React.createElement(\"rect\", {\n        x: left - dotSize / 2,\n        y: top - dotSize / 2,\n        width: width + dotSize,\n        height: height + dotSize\n      }))) : null, !hasSinglePoint && this.renderCurve(needClip, clipPathId), this.renderErrorBar(needClip, clipPathId), (hasSinglePoint || dot) && this.renderDots(needClip, clipDot, clipPathId), (!isAnimationActive || isAnimationFinished) && LabelList.renderCallByParent(this.props, points));\n    }\n  }], [{\n    key: \"getDerivedStateFromProps\",\n    value: function getDerivedStateFromProps(nextProps, prevState) {\n      if (nextProps.animationId !== prevState.prevAnimationId) {\n        return {\n          prevAnimationId: nextProps.animationId,\n          curPoints: nextProps.points,\n          prevPoints: prevState.curPoints\n        };\n      }\n      if (nextProps.points !== prevState.curPoints) {\n        return {\n          curPoints: nextProps.points\n        };\n      }\n      return null;\n    }\n  }, {\n    key: \"repeat\",\n    value: function repeat(lines, count) {\n      var linesUnit = lines.length % 2 !== 0 ? [].concat(_toConsumableArray(lines), [0]) : lines;\n      var result = [];\n      for (var i = 0; i < count; ++i) {\n        result = [].concat(_toConsumableArray(result), _toConsumableArray(linesUnit));\n      }\n      return result;\n    }\n  }, {\n    key: \"renderDotItem\",\n    value: function renderDotItem(option, props) {\n      var dotItem;\n      if ( /*#__PURE__*/React.isValidElement(option)) {\n        dotItem = /*#__PURE__*/React.cloneElement(option, props);\n      } else if (isFunction(option)) {\n        dotItem = option(props);\n      } else {\n        var key = props.key,\n          dotProps = _objectWithoutProperties(props, _excluded2);\n        var className = clsx('recharts-line-dot', typeof option !== 'boolean' ? option.className : '');\n        dotItem = /*#__PURE__*/React.createElement(Dot, _extends({\n          key: key\n        }, dotProps, {\n          className: className\n        }));\n      }\n      return dotItem;\n    }\n  }]);\n}(PureComponent);\n_defineProperty(Line, \"displayName\", 'Line');\n_defineProperty(Line, \"defaultProps\", {\n  xAxisId: 0,\n  yAxisId: 0,\n  connectNulls: false,\n  activeDot: true,\n  dot: true,\n  legendType: 'line',\n  stroke: '#3182bd',\n  strokeWidth: 1,\n  fill: '#fff',\n  points: [],\n  isAnimationActive: !Global.isSsr,\n  animateNewValues: true,\n  animationBegin: 0,\n  animationDuration: 1500,\n  animationEasing: 'ease',\n  hide: false,\n  label: false\n});\n/**\n * Compose the data of each group\n * @param {Object} props The props from the component\n * @param  {Object} xAxis   The configuration of x-axis\n * @param  {Object} yAxis   The configuration of y-axis\n * @param  {String} dataKey The unique key of a group\n * @return {Array}  Composed data\n */\n_defineProperty(Line, \"getComposedData\", function (_ref4) {\n  var props = _ref4.props,\n    xAxis = _ref4.xAxis,\n    yAxis = _ref4.yAxis,\n    xAxisTicks = _ref4.xAxisTicks,\n    yAxisTicks = _ref4.yAxisTicks,\n    dataKey = _ref4.dataKey,\n    bandSize = _ref4.bandSize,\n    displayedData = _ref4.displayedData,\n    offset = _ref4.offset;\n  var layout = props.layout;\n  var points = displayedData.map(function (entry, index) {\n    var value = getValueByDataKey(entry, dataKey);\n    if (layout === 'horizontal') {\n      return {\n        x: getCateCoordinateOfLine({\n          axis: xAxis,\n          ticks: xAxisTicks,\n          bandSize: bandSize,\n          entry: entry,\n          index: index\n        }),\n        y: isNil(value) ? null : yAxis.scale(value),\n        value: value,\n        payload: entry\n      };\n    }\n    return {\n      x: isNil(value) ? null : xAxis.scale(value),\n      y: getCateCoordinateOfLine({\n        axis: yAxis,\n        ticks: yAxisTicks,\n        bandSize: bandSize,\n        entry: entry,\n        index: index\n      }),\n      value: value,\n      payload: entry\n    };\n  });\n  return _objectSpread({\n    points: points,\n    layout: layout\n  }, offset);\n});", "/**\n * @fileOverview Line Chart\n */\nimport { generateCategoricalChart } from './generateCategoricalChart';\nimport { Line } from '../cartesian/Line';\nimport { XAxis } from '../cartesian/XAxis';\nimport { YAxis } from '../cartesian/YAxis';\nimport { formatAxisMap } from '../util/CartesianUtils';\nexport var LineChart = generateCategoricalChart({\n  chartName: 'LineChart',\n  GraphicalChild: Line,\n  axisComponents: [{\n    axisType: 'xAxis',\n    AxisComp: XAxis\n  }, {\n    axisType: 'yAxis',\n    AxisComp: YAxis\n  }],\n  formatAxisMap: formatAxisMap\n});", "import {\r\n      <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,\r\n      <PERSON><PERSON><PERSON>,\r\n      <PERSON><PERSON>ianG<PERSON>,\r\n      <PERSON><PERSON><PERSON><PERSON>,\r\n      <PERSON><PERSON><PERSON><PERSON>,\r\n      <PERSON><PERSON><PERSON>,\r\n      Line\r\n} from \"recharts\";\r\nimport { CustomerConversionRate } from \"~/types/api/businessConsoleService/BuyerAccountingResponse\";\r\n\r\ninterface ConversionRateProps {\r\n      conversionRates: CustomerConversionRate;\r\n      week?: number\r\n\r\n}\r\n\r\ninterface CustomTickProps {\r\n      x?: number;\r\n      y?: number;\r\n      payload?: { value: string };\r\n      index?: number;\r\n      chartData?: { week: string; range: string; ordered: number; searched: number; delivered: number }[];\r\n}\r\n\r\nconst legendData = [\r\n      { label: \"Ordered\", color: \"#6366F1\" },\r\n      { label: \"Searched\", color: \"#A855F7\" },\r\n      { label: \"Delivered\", color: \"#F472B6\" },\r\n];\r\nconst CustomTick = ({\r\n      x = 0,\r\n      y = 0,\r\n      payload,\r\n      index = 0,\r\n      chartData = []\r\n}: CustomTickProps) => {\r\n      const week = payload?.value || \"\";\r\n      const range = chartData[index]?.range || \"\";\r\n      return (\r\n            <g transform={`translate(${x},${y})`}>\r\n                  <text\r\n                        x={0}\r\n                        y={0}\r\n                        dy={8}\r\n                        textAnchor=\"middle\"\r\n                        fill=\"#6b7280\"\r\n                        fontSize={12}\r\n                        className=\"font-medium\"\r\n                  >\r\n                        {week}\r\n                  </text>\r\n                  <text\r\n                        x={0}\r\n                        y={20}\r\n                        dy={8}\r\n                        textAnchor=\"middle\"\r\n                        fill=\"#9ca3af\"\r\n                        fontSize={8}\r\n                        className=\"font-light\"\r\n                  >\r\n                        {range}\r\n                  </text>\r\n            </g>\r\n      );\r\n};\r\n\r\nexport default function ConversionRate({ conversionRates, week }: ConversionRateProps) {\r\n      const chartData = (conversionRates?.weeklySales || []).map((convRate) => ({\r\n            week: convRate?.week || \"N/A\",\r\n            range: convRate?.dateRange || \"\",\r\n            ordered: convRate?.ordered || 0,\r\n            searched: convRate?.searched || 0,\r\n            delivered: convRate?.delivered || 0,\r\n      }));\r\n\r\n      return (\r\n            <div className=\"w-full my-2 bg-white rounded-xl shadow-lg p-6 \">\r\n                  <div className=\"space-y-4\">\r\n                        <h3 className=\"text-lg font-semibold text-gray-800\">\r\n                              Conversion Rate\r\n                        </h3>\r\n                        <div className=\"flex items-baseline gap-4\">\r\n                              <p className=\"text-2xl font-bold text-indigo-600\">\r\n                                    {conversionRates?.conversionRate > 0\r\n                                          ? `${conversionRates.conversionRate.toFixed(1)}%`\r\n                                          : \"0.0%\"}\r\n                              </p>\r\n                              <span className=\"text-sm text-gray-500\">Last {week} weeks</span>\r\n                        </div>\r\n\r\n                        <div className=\"flex flex-col sm:flex-row gap-6 items-start \">\r\n\r\n\r\n                              <div className=\"w-full sm:w-2/3 -mx-6\" >\r\n                                    <ResponsiveContainer width=\"100%\" height={250}>\r\n                                          <LineChart\r\n                                                data={chartData}\r\n                                                margin={{ top: 20, right: 20, bottom: 20, left: 0 }}\r\n                                          >\r\n                                                <CartesianGrid\r\n                                                      vertical={false}\r\n                                                      stroke=\"#e5e7eb\"\r\n                                                      strokeDasharray=\"3 3\"\r\n                                                />\r\n                                                <XAxis\r\n                                                      dataKey=\"week\"\r\n                                                      tickLine={false}\r\n                                                      tickMargin={15}\r\n                                                      axisLine={{ stroke: \"#e5e7eb\" }}\r\n                                                      height={60}\r\n                                                      tick={<CustomTick chartData={chartData} />}\r\n                                                />\r\n                                                <YAxis\r\n                                                      dataKey=\"searched\"\r\n                                                      allowDecimals={false}\r\n                                                      tickLine={false}\r\n                                                      axisLine={{ stroke: \"#e5e7eb\" }}\r\n                                                      tickFormatter={(value) => `${value}`}\r\n                                                      className=\"text-gray-600\"\r\n                                                />\r\n                                                <Tooltip\r\n                                                      formatter={(value: number, name: string) => [\r\n                                                            value,\r\n                                                            name.charAt(0).toUpperCase() + name.slice(1)\r\n                                                      ]}\r\n                                                      contentStyle={{\r\n                                                            backgroundColor: \"#fff\",\r\n                                                            borderRadius: \"8px\",\r\n                                                            border: \"1px solid #e5e7eb\",\r\n                                                            boxShadow: \"0 2px 4px rgba(0,0,0,0.1)\",\r\n                                                      }}\r\n                                                />\r\n                                                {legendData.map((item) => (\r\n                                                      <Line\r\n                                                            key={item.label}\r\n                                                            dataKey={item.label.toLowerCase()}\r\n                                                            type=\"monotone\"\r\n                                                            stroke={item.color}\r\n                                                            strokeWidth={2}\r\n                                                            dot={{ r: 4 }}\r\n                                                            activeDot={{ r: 6 }}\r\n                                                      />\r\n                                                ))}\r\n                                          </LineChart>\r\n                                    </ResponsiveContainer>\r\n                              </div>\r\n\r\n                              <div className=\"flex  sm: flex-row md:flex-col gap-3 self-center\">\r\n                                    {legendData.map((item) => (\r\n                                          <div key={item.label} className=\"flex items-center gap-2\">\r\n                                                <span\r\n                                                      className=\"w-3 h-3 rounded-full\"\r\n                                                      style={{ backgroundColor: item.color }}\r\n                                                />\r\n                                                <span className=\"text-sm font-medium text-gray-700\">\r\n                                                      {item.label}\r\n                                                </span>\r\n                                          </div>\r\n                                    ))}\r\n                              </div>\r\n                        </div>\r\n                  </div>\r\n            </div>\r\n      );\r\n}", "import {\r\n      <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,\r\n      <PERSON><PERSON><PERSON>,\r\n      <PERSON><PERSON>ianG<PERSON>,\r\n      <PERSON><PERSON><PERSON><PERSON>,\r\n      <PERSON><PERSON><PERSON><PERSON>,\r\n      <PERSON><PERSON><PERSON>,\r\n      <PERSON>\r\n} from \"recharts\";\r\nimport { CustomerAcquisition } from \"~/types/api/businessConsoleService/BuyerAccountingResponse\";\r\n\r\ninterface CustomerAcquisitionProps {\r\n      customerAcquisitionRate: CustomerAcquisition;\r\n}\r\n\r\ninterface CustomTickProps {\r\n      x?: number;\r\n      y?: number;\r\n      payload?: { value: string };\r\n      index?: number;\r\n      chartData?: { week: string; range: string; ordered: number; new: number; repeat: number }[];\r\n}\r\nconst legendData = [\r\n      { label: \"Ordered\", color: \"#7987FF\" },\r\n      { label: \"New\", color: \"#E697FF\" },\r\n      { label: \"Repeat\", color: \"#FFA5CB\" },\r\n];\r\n\r\n\r\nconst CustomTick = ({\r\n      x = 0,\r\n      y = 0,\r\n      payload,\r\n      index = 0,\r\n      chartData = []\r\n}: CustomTickProps) => {\r\n      const week = payload?.value || \"\";\r\n      const range = chartData[index]?.range || \"\";\r\n      return (\r\n            <g transform={`translate(${x},${y})`}>\r\n                  <text\r\n                        x={0}\r\n                        y={0}\r\n                        dy={8}\r\n                        textAnchor=\"middle\"\r\n                        fill=\"#6b7280\"\r\n                        fontSize={12}\r\n                        className=\"font-medium\"\r\n                  >\r\n                        {week}\r\n                  </text>\r\n                  <text\r\n                        x={0}\r\n                        y={20}\r\n                        dy={8}\r\n                        textAnchor=\"middle\"\r\n                        fill=\"#9ca3af\"\r\n                        fontSize={8}\r\n                        className=\"font-light\"\r\n                  >\r\n                        {range}\r\n                  </text>\r\n            </g>\r\n      );\r\n};\r\nexport default function CustomersAcquisition({ customerAcquisitionRate }: CustomerAcquisitionProps) {\r\n      const chartData = (customerAcquisitionRate?.weeklyAcquisition || []).map((acquisition) => ({\r\n            week: acquisition?.week || \"N/A\",\r\n            range: acquisition?.dateRange || \"\",\r\n            ordered: acquisition?.ordered || 0,\r\n            new: acquisition?.newCustomers || 0,\r\n            repeat: acquisition?.repeat || 0,\r\n      }));\r\n\r\n      const totalCustomers = customerAcquisitionRate?.totalCustomers;\r\n      // const avgNewCustomers = customerAcquisitionRate?.weeklyAcquisition\r\n      //       ? Math.round(\r\n      //             customerAcquisitionRate.weeklyAcquisition.reduce((sum, week) => sum + (week.new || 0), 0) /\r\n      //             customerAcquisitionRate.weeklyAcquisition.length\r\n      //       )\r\n      //       : 80;\r\n\r\n      const avgNewCustomers = customerAcquisitionRate.newCustomers\r\n\r\n\r\n\r\n      return (\r\n            <div className=\"w-full my-3 bg-white rounded-xl shadow-lg p-6\">\r\n                  <div className=\"space-y-4\">\r\n                        <h3 className=\"text-lg font-semibold text-gray-800\">\r\n                              Customer Acquisition\r\n                        </h3>\r\n\r\n                        <div className=\"flex items-baseline gap-4\">\r\n                              <p className=\"text-2xl font-bold text-purple-600\">\r\n                                    {totalCustomers || \"\"}\r\n                              </p>\r\n                              <span className=\"text-sm text-gray-500\">\r\n                                    {avgNewCustomers} new customers per week\r\n                              </span>\r\n                        </div>\r\n\r\n                        <div className=\"flex flex-col sm:flex-row gap-6 items-start\">\r\n                              <div className=\"w-full sm:w-2/3\">\r\n                                    <ResponsiveContainer width=\"100%\" height={250}>\r\n                                          <LineChart\r\n                                                data={chartData}\r\n                                                margin={{ top: 20, right: 20, bottom: 20, left: 0 }}\r\n                                          >\r\n                                                <CartesianGrid\r\n                                                      vertical={false}\r\n                                                      stroke=\"#e5e7eb\"\r\n                                                      strokeDasharray=\"3 3\"\r\n                                                />\r\n                                                <XAxis\r\n                                                      dataKey=\"week\"\r\n                                                      tickLine={false}\r\n                                                      tickMargin={15}\r\n                                                      axisLine={{ stroke: \"#e5e7eb\" }}\r\n                                                      height={60}\r\n                                                      tick={<CustomTick chartData={chartData} />}\r\n                                                />\r\n                                                <YAxis\r\n                                                      dataKey=\"new\"\r\n\r\n                                                      allowDecimals={false}\r\n                                                      tickLine={false}\r\n                                                      axisLine={{ stroke: \"#e5e7eb\" }}\r\n                                                      tickFormatter={(value) => `${value}`}\r\n                                                      className=\"text-gray-600\"\r\n                                                />\r\n                                                <Tooltip\r\n                                                      formatter={(value: number, name: string) => [\r\n                                                            value,\r\n                                                            name.charAt(0).toUpperCase() + name.slice(1)\r\n                                                      ]}\r\n                                                      contentStyle={{\r\n                                                            backgroundColor: \"#fff\",\r\n                                                            borderRadius: \"8px\",\r\n                                                            border: \"1px solid #e5e7eb\",\r\n                                                            boxShadow: \"0 2px 4px rgba(0,0,0,0.1)\",\r\n                                                      }}\r\n                                                />\r\n                                                {legendData.map((item) => (\r\n                                                      <Line\r\n                                                            key={item.label}\r\n                                                            dataKey={item.label.toLowerCase()}\r\n                                                            type=\"monotone\"\r\n                                                            stroke={item.color}\r\n                                                            strokeWidth={2}\r\n                                                            dot={{ r: 4 }}\r\n                                                            activeDot={{ r: 6 }}\r\n                                                      />\r\n                                                ))}\r\n                                          </LineChart>\r\n                                    </ResponsiveContainer>\r\n                              </div>\r\n                              <div className=\"flex  sm:flex-row md:flex-col gap-3 self-center\">\r\n                                    {legendData.map((item) => (\r\n                                          <div key={item.label} className=\"flex items-center gap-2\">\r\n                                                <span\r\n                                                      className=\"w-3 h-3 rounded-full\"\r\n                                                      style={{ backgroundColor: item.color }}\r\n                                                />\r\n                                                <span className=\"text-sm font-medium text-gray-700\">\r\n                                                      {item.label}\r\n                                                </span>\r\n                                          </div>\r\n                                    ))}\r\n                              </div>\r\n                        </div>\r\n                  </div>\r\n            </div>\r\n      );\r\n}", "import {\r\n  <PERSON>,\r\n  <PERSON><PERSON><PERSON>,\r\n  <PERSON><PERSON><PERSON>,\r\n  <PERSON><PERSON>ons<PERSON><PERSON><PERSON><PERSON>,\r\n  <PERSON><PERSON><PERSON>,\r\n  <PERSON><PERSON><PERSON><PERSON>,\r\n  <PERSON><PERSON><PERSON><PERSON>\r\n} from \"recharts\";\r\nimport { SellerSales } from \"~/types/api/businessConsoleService/BuyerAccountingResponse\";\r\nimport { formatCurrency } from \"~/utils/format\";\r\n\r\ninterface CustomerSalesProps {\r\n  sales: SellerSales;\r\n}\r\n\r\ninterface CustomTickProps {\r\n  x?: number;\r\n  y?: number;\r\n  payload?: { value: string };\r\n  index?: number;\r\n  chartData?: { week: string; range: string; sales: number }[];\r\n}\r\n\r\nconst CustomTick = ({\r\n  x = 0,\r\n  y = 0,\r\n  payload,\r\n  index = 0,\r\n  chartData = []\r\n}: CustomTickProps) => {\r\n  const week = payload?.value || \"\";\r\n  const range = chartData[index]?.range || \"\";\r\n\r\n  return (\r\n    <g transform={`translate(${x},${y})`}>\r\n      <text\r\n        x={0}\r\n        y={0}\r\n        dy={8}\r\n        textAnchor=\"middle\"\r\n        fill=\"#666\"\r\n        fontSize={12}\r\n        className=\"font-medium\"\r\n      >\r\n        {week}\r\n      </text>\r\n      <text\r\n        x={0}\r\n        y={20}\r\n        dy={8}\r\n        textAnchor=\"middle\"\r\n        fill=\"#999\"\r\n        fontSize={8}\r\n        className=\"font-light\"\r\n      >\r\n        {range}\r\n      </text>\r\n    </g>\r\n  );\r\n};\r\n\r\nexport default function CustomerSales({ sales }: CustomerSalesProps) {\r\n  const chartData = (sales?.weeklySales || []).map((weekData) => ({\r\n    week: weekData?.week || \"N/A\",\r\n    range: weekData?.dateRange || \"\",\r\n    sales: weekData?.totalDeliveredOrders || 0,\r\n  }));\r\n\r\n  return (\r\n    <div className=\"w-full my-3 bg-white rounded-xl shadow-lg p-6  \">\r\n      <div className=\"space-y-3\">\r\n        <h3 className=\"text-lg font-semibold text-gray-800\">\r\n          Sales Overview\r\n        </h3>\r\n\r\n        <div className=\"flex items-center gap-6\">\r\n          <div>\r\n            <p className=\"text-2xl font-bold text-blue-600\">\r\n              {formatCurrency(sales?.totalRevenue || 0)}\r\n            </p>\r\n            <p className=\"text-sm text-gray-600\">\r\n              {sales?.totalOrders?.toLocaleString() || 0} Orders\r\n            </p>\r\n          </div>\r\n        </div>\r\n        <div className=\"mt-6 -mx-6\"> {/* Negative margin to offset parent padding */}          <ResponsiveContainer width=\"100%\" height={250}>\r\n          <BarChart\r\n            data={chartData}\r\n            margin={{ top: 20, right: 20, bottom: 20, left: 0 }}\r\n          >\r\n            <CartesianGrid\r\n              vertical={false}\r\n              stroke=\"#e5e7eb\"\r\n              strokeDasharray=\"3 3\"\r\n            />\r\n            <XAxis\r\n              dataKey=\"week\"\r\n              tickLine={false}\r\n              tickMargin={15}\r\n              axisLine={{ stroke: \"#e5e7eb\" }}\r\n              height={60}\r\n              tick={<CustomTick chartData={chartData} />}\r\n            />\r\n            <YAxis\r\n              dataKey=\"sales\"\r\n              allowDecimals={false}\r\n              tickLine={false}\r\n              axisLine={{ stroke: \"#e5e7eb\" }}\r\n              tickFormatter={(value) => `${value}`}\r\n              className=\"text-gray-600\"\r\n            />\r\n            <Tooltip\r\n              formatter={(value: number) => (value)}\r\n              contentStyle={{\r\n                backgroundColor: \"#fff\",\r\n                borderRadius: \"8px\",\r\n                border: \"1px solid #e5e7eb\",\r\n                boxShadow: \"0 2px 4px rgba(0,0,0,0.1)\",\r\n              }}\r\n            />\r\n            <Bar\r\n              dataKey=\"sales\"\r\n              fill=\"#6B8EF1\"\r\n              barSize={20}\r\n              radius={[4, 4, 0, 0]}\r\n            />\r\n          </BarChart>\r\n        </ResponsiveContainer>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}", "import type React from \"react\"\r\n\r\nimport { useState, useEffect } from \"react\"\r\nimport { <PERSON><PERSON> } from \"../components/ui/button\"\r\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"../components/ui/card\"\r\nimport { Input } from \"../components/ui/input\"\r\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from \"../components/ui/select\"\r\nimport { Phone, MapPin, User, ArrowUp, ArrowDown, Info, Pencil, Save, X, Search, Filter, CalendarIcon } from \"lucide-react\"\r\nimport { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from \"../components/ui/dialog\"\r\nimport { json, ActionFunction, LoaderFunction } from \"@remix-run/node\"\r\nimport { useFetcher, useLoaderData, useSearchParams } from \"@remix-run/react\"\r\nimport { withAuth, withResponse } from \"../utils/auth-utils\"\r\nimport { useToast } from \"../hooks/use-toast\"\r\nimport { <PERSON>Rang<PERSON> } from \"react-day-picker\"\r\nimport { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from \"../components/ui/tabs\"\r\nimport { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from \"../components/ui/table\"\r\nimport { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from \"../components/ui/tooltip\"\r\nimport SpinnerLoader from \"../components/loader/SpinnerLoader\"\r\nimport { Popover, PopoverContent, PopoverTrigger, PopoverClose } from \"../components/ui/popover\"\r\nimport { cn } from \"../lib/utils\"\r\nimport { format } from \"date-fns\"\r\nimport { Calendar } from \"../components/ui/calendar\"\r\n// Customer Analysis Components\r\nimport ConversionRate from \"../components/common/ConversionRate\";\r\nimport CustomersAcquisition from \"../components/common/CustomerAcquisation\";\r\nimport CustomerSales from \"../components/common/CustomerSales\";\r\nimport { getAcquiSitionRates, getConversionRate, getSellerSales } from \"../services/buyerSetting\";\r\nimport { getBuyerSummary, updateFbDiscountPrice } from \"../services/businessConsoleService\";\r\nimport { CustomerAcquisition, CustomerConversionRate, SellerSales } from \"../types/api/businessConsoleService/BuyerAccountingResponse\";\r\nimport { BuyerSummaryDetailsResponseItem } from \"../types/api/businessConsoleService/BuyerSummaryDetailsResponseItem\";\r\n\r\ntype ActionIntent = \"Customer Overview\" | \"Customer List\" | \"Customer Loyalty\" | \"UpdateFbDiscount\";\r\n\r\nexport interface BuyerDetails {\r\n  buyerId: number;\r\n  buyerName: string;\r\n  ownerName: string;\r\n  address: string;\r\n  mobileNumber: string;\r\n  totalOrders: number;\r\n  totalAmount: number;\r\n  pendingAmount: number;\r\n  lastOrderedDate: string;\r\n}\r\n\r\ninterface LoaderData {\r\n  sales: SellerSales,\r\n  customerConversionRates: CustomerConversionRate,\r\n  customerAcquisitionRate: CustomerAcquisition,\r\n  week: number,\r\n  // Customer List Data\r\n  customers: BuyerSummaryDetailsResponseItem[];\r\n  currentPage: number;\r\n  hasNextPage: boolean;\r\n  hasMoreData: boolean;\r\n  tabValue: string;\r\n  sortByvalue: string;\r\n  searchBy: string;\r\n  sortByOrder: string;\r\n}\r\n\r\ninterface ActionData {\r\n  intent: ActionIntent;\r\n  errorMessage: string;\r\n  success: boolean;\r\n  data: { customers: BuyerDetails[], totalElements: number, pageSize: number, currentPage: number };\r\n}\r\n\r\nexport const loader: LoaderFunction = withAuth(async ({ request, user }) => {\r\n  try {\r\n    const week = 4;\r\n    const url = new URL(request.url);\r\n    const page = parseInt(url.searchParams.get(\"page\") || \"0\");\r\n    const tabValue = url.searchParams.get(\"tabValue\") || \"all\";\r\n    const sortBy = url.searchParams.get(\"sortBy\") || \"buyerName\";\r\n    const searchBy = url.searchParams.get(\"searchBy\") || \"\";\r\n    const sortByOrder = url.searchParams.get(\"sortByOrder\") || \"asc\";\r\n    const validSearchBy = searchBy && searchBy.length >= 3 ? searchBy : \"\";\r\n    const pageSize = 50;\r\n\r\n    // Make parallel API calls\r\n    const [salesResponse, conversionResponse, acquisitionResponse, customersResponse] = await Promise.all([\r\n      getSellerSales(week, request),\r\n      getConversionRate(3, request),\r\n      getAcquiSitionRates(week, request),\r\n      getBuyerSummary(user.userId, page, pageSize, tabValue, sortBy, validSearchBy, sortByOrder, request)\r\n    ]);\r\n\r\n    const hasNextPage = (customersResponse.data?.length ?? 0) >= pageSize;\r\n    const nextPageResponse = await getBuyerSummary(user.userId, page + 1, pageSize, tabValue, sortBy, validSearchBy, sortByOrder, request);\r\n    const hasMoreData = (nextPageResponse.data ?? []).length > 0;\r\n\r\n    const responseHeaders = new Headers();\r\n    [salesResponse, conversionResponse, acquisitionResponse, customersResponse].forEach(response => {\r\n      if (response.headers?.has('Set-Cookie')) {\r\n        responseHeaders.set('Set-Cookie', response.headers.get('Set-Cookie')!);\r\n      }\r\n    });\r\n\r\n    return withResponse({\r\n      sales: salesResponse.data,\r\n      customerConversionRates: conversionResponse.data,\r\n      customerAcquisitionRate: acquisitionResponse.data,\r\n      week: week,\r\n      // Customer List Data\r\n      customers: customersResponse.data,\r\n      currentPage: page,\r\n      hasNextPage,\r\n      hasMoreData,\r\n      tabValue,\r\n      sortByvalue: sortBy,\r\n      searchBy,\r\n      sortByOrder\r\n    }, responseHeaders);\r\n  }\r\n  catch (error) {\r\n    throw new Response(\"Failed to fetch customer analysis data\", {\r\n      status: 500,\r\n      statusText: error instanceof Error ? error.message : \"Unknown error\"\r\n    });\r\n  }\r\n});\r\n\r\nexport const action: ActionFunction = withAuth(async ({ request, user }) => {\r\n  const formData = await request.formData();\r\n  const intent = formData.get(\"intent\") as ActionIntent;\r\n\r\n  if (!intent) {\r\n    return json({ success: false, errorMessage: \"Invalid request\", intent: intent }, { status: 400 });\r\n  }\r\n\r\n  if (!user.sellerId) {\r\n    return json({ success: false, errorMessage: \"Seller ID not found\", intent: intent }, { status: 400 });\r\n  }\r\n\r\n  if (intent === \"UpdateFbDiscount\") {\r\n    const nBuyerId = Number(formData.get(\"nBuyerId\"))\r\n    const fBDiscount = Number(formData.get(\"fbDiscount\"))\r\n\r\n    try {\r\n      const response = await updateFbDiscountPrice(nBuyerId, fBDiscount, request);\r\n      return withResponse({ success: true, intent: intent, data: response?.data }, response?.headers);\r\n    }\r\n    catch (error) {\r\n      return json({ success: false, intent: intent, errorMessage: \"Failed to update discount\" }, { status: 400 })\r\n    }\r\n  }\r\n\r\n  return json({ success: false, intent: intent, errorMessage: \"Invalid intent\" }, { status: 400 });\r\n});\r\n\r\n\r\nexport default function SellerCustomers() {\r\n  const { sales, customerConversionRates, customerAcquisitionRate, week, customers, currentPage, tabValue, sortByvalue, searchBy, sortByOrder } = useLoaderData<LoaderData>();\r\n  const [selectedCustomer, setSelectedCustomer] = useState<BuyerDetails | null>(null)\r\n  const [activeTab, setActiveTab] = useState<\"Customer Overview\" | \"Customer List\" | \"Customer Loyalty\">(\"Customer Overview\")\r\n  const fetcher = useFetcher<ActionData>()\r\n  const [searchParams, setSearchParams] = useSearchParams()\r\n\r\n  // action\r\n  const [actionType, setActionType] = useState<string>(\"\")\r\n  const [actionSelectedCustomer, setActionSelectedCustomer] = useState<BuyerDetails | null>(null)\r\n  const [isSubmitting, setIsSubmitting] = useState(false)\r\n  const { toast } = useToast()\r\n\r\n  // Search and Filters\r\n  const [searchType, setSearchType] = useState<\"ID\" | \"Mobile\" | \"Name\">(\"ID\")\r\n  const [searchTerm, setSearchTerm] = useState<string>(\"\")\r\n  const [filterDate, setFilterDate] = useState<DateRange | undefined>(undefined);\r\n  const [pageSize, setPageSize] = useState(20)\r\n\r\n  // local state\r\n  const [dateRange, setDateRange] = useState<DateRange | undefined>(undefined);\r\n\r\n  // Customer List specific state\r\n  const [customerListSearchTerm, setCustomerListSearchTerm] = useState(searchBy)\r\n  const [customerListSortBy, setCustomerListSortBy] = useState(sortByvalue)\r\n  const [customerListSortOrder, setCustomerListSortOrder] = useState(sortByOrder)\r\n  const [customerListActiveTab, setCustomerListActiveTab] = useState(tabValue)\r\n  const [fbDiscounts, setFbDiscounts] = useState<{ [key: number]: string }>({});\r\n  const [isFbDis, setIsFbDis] = useState<{ [key: number]: boolean }>({});\r\n\r\n  // debounce on search term\r\n  useEffect(() => {\r\n    const timer = setTimeout(() => {\r\n      // This effect is now used for the search functionality\r\n    }, 500);\r\n\r\n    return () => {\r\n      clearTimeout(timer);\r\n    };\r\n  }, [searchTerm]);\r\n\r\n  // Navigation function to update URL params and trigger reload\r\n  const updateCustomerListParams = (params: Record<string, string>) => {\r\n    const newSearchParams = new URLSearchParams(searchParams);\r\n    Object.entries(params).forEach(([key, value]) => {\r\n      if (value) {\r\n        newSearchParams.set(key, value);\r\n      } else {\r\n        newSearchParams.delete(key);\r\n      }\r\n    });\r\n    setSearchParams(newSearchParams);\r\n  };\r\n\r\n  const handleTabChange = (newTab: string) => {\r\n    setSearchType(\"ID\")\r\n    setSearchTerm(\"\")\r\n    setFilterDate(undefined)\r\n    setDateRange(undefined)\r\n    setPageSize(20)\r\n    setActiveTab(newTab as \"Customer Overview\" | \"Customer List\" | \"Customer Loyalty\")\r\n  }\r\n\r\n  const searchTypeFilters = [\r\n    { label: \"Customer ID\", value: \"ID\" },\r\n    { label: \"Customer Mobile\", value: \"Mobile\" },\r\n    { label: \"Customer Name\", value: \"Name\" },\r\n  ]\r\n\r\n  const handleAction = (customer: BuyerDetails, action: string) => {\r\n    setActionSelectedCustomer(customer)\r\n    setActionType(action)\r\n  }\r\n\r\n  const handleSubmitAction = (formData: Record<string, unknown>) => {\r\n    const actionData = new FormData();\r\n    actionData.append(\"intent\", actionType);\r\n    actionData.append(\"data\", JSON.stringify({ customer: actionSelectedCustomer, formData }))\r\n    fetcher.submit(actionData, { method: \"post\" })\r\n    setIsSubmitting(true)\r\n  }\r\n\r\n  // Customer List specific handlers\r\n  const handleCustomerListTabChange = (newTab: string) => {\r\n    const tabMap: { [key: string]: string } = {\r\n      all: \"all\",\r\n      oneOrder: \"one_order\",\r\n      frequent: \"frequent_orders\",\r\n      zero_orders: \"zero_orders\",\r\n    };\r\n    const validTabValue = tabMap[newTab] || \"all\";\r\n    setCustomerListActiveTab(newTab);\r\n\r\n    // Update URL params to trigger API call\r\n    updateCustomerListParams({\r\n      tabValue: validTabValue,\r\n      page: \"0\", // Reset to first page when changing tabs\r\n      sortBy: customerListSortBy,\r\n      sortByOrder: customerListSortOrder,\r\n      searchBy: customerListSearchTerm\r\n    });\r\n  };\r\n\r\n  const handleCustomerListSort = (newSort: string) => {\r\n    setCustomerListSortBy((prevSortBy) => {\r\n      const isSameSort = prevSortBy === newSort;\r\n      setCustomerListSortOrder((prevSortOrder) => {\r\n        const newOrder = isSameSort && prevSortOrder === \"asc\" ? \"desc\" : \"asc\";\r\n\r\n        // Update URL params to trigger API call\r\n        updateCustomerListParams({\r\n          tabValue: customerListActiveTab,\r\n          page: \"0\", // Reset to first page when sorting\r\n          sortBy: newSort,\r\n          sortByOrder: newOrder,\r\n          searchBy: customerListSearchTerm\r\n        });\r\n\r\n        return newOrder;\r\n      });\r\n      return newSort;\r\n    });\r\n  };\r\n\r\n  const getSortIcon = (column: string) => {\r\n    if (customerListSortBy !== column) return null;\r\n    return customerListSortOrder === \"asc\" ? <ArrowUp className=\"w-4 h-4 ml-1\" /> : <ArrowDown className=\"w-4 h-4 ml-1\" />;\r\n  };\r\n\r\n  const handleCustomerListSearch = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    const value = e.target.value;\r\n    setCustomerListSearchTerm(value);\r\n\r\n    // Update URL params to trigger API call (with debounce)\r\n    setTimeout(() => {\r\n      updateCustomerListParams({\r\n        tabValue: customerListActiveTab,\r\n        page: \"0\", // Reset to first page when searching\r\n        sortBy: customerListSortBy,\r\n        sortByOrder: customerListSortOrder,\r\n        searchBy: value.length >= 3 ? value : \"\"\r\n      });\r\n    }, 300);\r\n  };\r\n\r\n  const handleChangeFbDiscount = (nBuyerId: number, val: string) => {\r\n    if (/^\\d*\\.?\\d*$/.test(val) || val === \"\") {\r\n      setFbDiscounts((prev) => ({ ...prev, [nBuyerId]: val }));\r\n    }\r\n  };\r\n\r\n  const fbFetcher = useFetcher<BuyerSummaryDetailsResponseItem>()\r\n\r\n  const handleSave = (nBuyerId: number) => {\r\n    const formData = new FormData()\r\n    formData.append(\"intent\", \"UpdateFbDiscount\");\r\n    formData.append(\"nBuyerId\", nBuyerId.toString());\r\n    formData.append(\"fbDiscount\", fbDiscounts[nBuyerId]);\r\n    fbFetcher.submit(formData, { method: \"POST\" })\r\n    setIsFbDis((prev) => ({ ...prev, [nBuyerId]: false }));\r\n  };\r\n\r\n  // Handle pagination changes\r\n  const handlePageSizeChange = (newPageSize: number) => {\r\n    setPageSize(newPageSize);\r\n    updateCustomerListParams({\r\n      page: \"0\", // Reset to first page when changing page size\r\n      pageSize: newPageSize.toString()\r\n    });\r\n  };\r\n\r\n  const handlePageChange = (newPage: number) => {\r\n    updateCustomerListParams({\r\n      page: newPage.toString()\r\n    });\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (fetcher.data?.intent === \"UpdateFbDiscount\") {\r\n      if (fetcher.data?.success) {\r\n        toast({\r\n          title: \"Success\",\r\n          description: \"Discount updated successfully\",\r\n        })\r\n      } else if (fetcher.data?.success === false) {\r\n        toast({\r\n          title: \"Error\",\r\n          description: fetcher.data?.errorMessage,\r\n          variant: \"destructive\"\r\n        })\r\n      }\r\n    }\r\n  }, [fetcher.data, toast])\r\n\r\n  return (\r\n    <div className=\"min-h-screen p-6\">\r\n      <div className=\"mx-auto mb-6\">\r\n\r\n        {/* Header */}\r\n        <div className=\"mb-4\">\r\n          <div className=\"flex flex-col gap-2 sm:flex-row sm:items-center sm:justify-between\">\r\n            <div>\r\n              <h1 className=\"text-xl md:text-3xl font-bold text-gray-900\">Customers</h1>\r\n              <p className=\"mt-2 text-gray-600\">Manage your customer relationships and loyalty programs</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <Tabs value={activeTab} onValueChange={handleTabChange}>\r\n          <TabsList className=\"w-full h-10 mb-1\">\r\n            <TabsTrigger value=\"Customer Overview\" className=\"w-1/2 h-8 py-1\">Customer Overview</TabsTrigger>\r\n            <TabsTrigger value=\"Customer List\" className=\"w-1/2 h-8 py-1\">Customer List</TabsTrigger>\r\n            <TabsTrigger value=\"Customer Loyalty\" className=\"w-1/2 h-8 py-1\">Customer Loyalty</TabsTrigger>\r\n          </TabsList>\r\n\r\n          <TabsContent value=\"Customer Overview\">\r\n            <div className=\"w-full min-h-screen bg-slate-100\">\r\n              <main className=\"p-2\">\r\n                <section>\r\n                  <CustomerSales sales={sales} />\r\n                </section>\r\n                <section>\r\n                  <ConversionRate conversionRates={customerConversionRates} week={week} />\r\n                </section>\r\n                <section>\r\n                  <CustomersAcquisition customerAcquisitionRate={customerAcquisitionRate} />\r\n                </section>\r\n              </main>\r\n            </div>\r\n          </TabsContent>\r\n\r\n          <TabsContent value=\"Customer List\">\r\n            {/* Search and Filters */}\r\n            {/* <Card className=\"mb-1 bg-gray-100\">\r\n              <CardContent className=\"p-2\">\r\n                <div className=\"space-y-1\">\r\n                  <div className=\"grid sm:grid-cols-2 lg:grid-cols-4 gap-2\">\r\n                    <div className=\"relative lg:col-span-1\">\r\n                      <Select value={searchType} onValueChange={(value: typeof searchType) => setSearchType(value)}>\r\n                        <SelectTrigger>\r\n                          <SelectValue placeholder=\"Search by\" />\r\n                        </SelectTrigger>\r\n                        <SelectContent>\r\n                          {searchTypeFilters.map((filter) => (\r\n                            <SelectItem key={filter.value} value={filter.value}>\r\n                              {filter.label}\r\n                            </SelectItem>\r\n                          ))}\r\n                        </SelectContent>\r\n                      </Select>\r\n                    </div>\r\n\r\n                    <div className=\"relative lg:col-span-2\">\r\n                      <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4\" />\r\n                      <Input\r\n                        placeholder={searchType === \"Name\" ? \"Search name\" : (searchType === \"Mobile\") ? \"Search by Mobile\" : \"Search by ID\"}\r\n                        value={searchTerm}\r\n                        onChange={(e) => setSearchTerm(e.target.value)}\r\n                        className=\"pl-10\"\r\n                      />\r\n                    </div>\r\n\r\n                    <div className=\"relative sm:col-span-2 lg:col-span-1 \">\r\n                      <Popover>\r\n                        <PopoverTrigger asChild>\r\n                          <Button\r\n                            id=\"filterDate\"\r\n                            variant=\"outline\"\r\n                            className={cn(\r\n                              \"w-full justify-start text-left font-normal\",\r\n                              !filterDate && \"text-muted-foreground\"\r\n                            )}\r\n                          >\r\n                            <CalendarIcon />\r\n                            {filterDate?.from ? (\r\n                              filterDate.to ? (\r\n                                <>\r\n                                  {format(filterDate.from, \"LLL dd, y\")} - {format(filterDate.to, \"LLL dd, y\")}\r\n                                </>\r\n                              ) : (\r\n                                format(filterDate.from, \"LLL dd, y\")\r\n                              )\r\n                            ) : (\r\n                              <span>Pick a date</span>\r\n                            )}\r\n                          </Button>\r\n                        </PopoverTrigger>\r\n                        <PopoverContent className=\"w-auto p-0\" align=\"start\">\r\n                          <Calendar\r\n                            initialFocus\r\n                            selected={dateRange}\r\n                            mode=\"range\"\r\n                            onSelect={(range: DateRange | undefined) => {\r\n                              if (!range?.from) return;\r\n                              setDateRange({\r\n                                from: range.from,\r\n                                to: range.to || undefined,\r\n                              });\r\n                            }}\r\n                          />\r\n                          <PopoverClose className=\"w-full\">\r\n                            <Button\r\n                              variant=\"ghost\"\r\n                              className=\"w-full text-blue-500 hover:text-blue-500 justify-center\"\r\n                              onClick={() => setFilterDate(dateRange)}\r\n                            >\r\n                              Set\r\n                            </Button>\r\n                          </PopoverClose>\r\n                        </PopoverContent>\r\n                      </Popover>\r\n                    </div>\r\n                  </div>\r\n\r\n                 \r\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-2\">\r\n                    <div className=\"space-y-1 lg:col-start-4 flex items-end\">\r\n                      <Button\r\n                        onClick={() => {\r\n                          setSearchType(\"ID\")\r\n                          setSearchTerm(\"\")\r\n                          setFilterDate(undefined)\r\n                          setDateRange(undefined)\r\n                          setPageSize(20)\r\n                        }}\r\n                        className=\"w-full\"\r\n                      >\r\n                        <Filter className=\"w-4 h-4 mr-2\" />\r\n                        Clear Filters\r\n                      </Button>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </CardContent>\r\n            </Card> */}\r\n\r\n            {/* Customer List Tabs */}\r\n            <Card className=\"mb-1\">\r\n              <CardContent className=\"p-2\">\r\n                <Tabs value={customerListActiveTab} onValueChange={handleCustomerListTabChange} className=\"mb-4\">\r\n                  <TabsList className=\"w-full h-10 mb-1\">\r\n                    <TabsTrigger value=\"all\" className=\"w-1/2 h-8 py-1\"> All Customers</TabsTrigger>\r\n                    <TabsTrigger value=\"frequent\" className=\"w-1/2 h-8 py-1\"> Frequent Customers</TabsTrigger>\r\n                    <TabsTrigger value=\"oneOrder\" className=\"w-1/2 h-8 py-1\"> One Order Customers</TabsTrigger>\r\n                    <TabsTrigger value=\"zero_orders\" className=\"w-1/2 h-8 py-1\"> New Customers</TabsTrigger>\r\n                  </TabsList>\r\n                </Tabs>\r\n\r\n                {/* Customer Search */}\r\n                <div className=\"flex justify-between mb-4\">\r\n                  <Input\r\n                    placeholder=\"Search by name or owner\"\r\n                    value={customerListSearchTerm}\r\n                    onChange={handleCustomerListSearch}\r\n                    className=\"max-w-sm\"\r\n                  />\r\n                  <Select value={customerListSortBy} onValueChange={handleCustomerListSort}>\r\n                    <SelectTrigger className=\"w-[180px]\">\r\n                      <SelectValue placeholder=\"Sort by\" />\r\n                    </SelectTrigger>\r\n                    <SelectContent>\r\n                      <SelectItem value=\"buyerName\">Name</SelectItem>\r\n                      <SelectItem value=\"totalOrders\">Number of Orders</SelectItem>\r\n                      <SelectItem value=\"pendingAmount\">Pending Balance</SelectItem>\r\n                      <SelectItem value=\"lastOrderedDate\">Order Duration Days</SelectItem>\r\n                      <SelectItem value=\"fbDiscount\">F.B.Discount</SelectItem>\r\n                    </SelectContent>\r\n                  </Select>\r\n                </div>\r\n\r\n                {/* Customer Table */}\r\n                <Table>\r\n                  {fbFetcher.state !== \"idle\" && <SpinnerLoader loading={true} />}\r\n                  <TableHeader>\r\n                    <TableRow>\r\n                      <TableHead className=\"cursor-pointer\" onClick={() => handleCustomerListSort(\"buyerName\")}>\r\n                        <span className='flex flex-row items-center gap-1'>\r\n                          <span>Name</span>\r\n                          <span>{getSortIcon(\"buyerName\")}</span>\r\n                        </span>\r\n                      </TableHead>\r\n                      <TableHead className=\"cursor-pointer\" onClick={() => handleCustomerListSort(\"totalOrders\")}>\r\n                        <span className='flex flex-row items-center gap-1'>\r\n                          <span>Num of Orders</span>\r\n                          {getSortIcon(\"totalOrders\")}\r\n                        </span>\r\n                      </TableHead>\r\n                      <TableHead className=\"cursor-pointer\" onClick={() => handleCustomerListSort(\"pendingAmount\")}>\r\n                        <span className='flex flex-row items-center gap-1'>\r\n                          <span>Pending Balance</span>\r\n                          {getSortIcon(\"pendingAmount\")}\r\n                        </span>\r\n                      </TableHead>\r\n                      <TableHead className=\"cursor-pointer\" onClick={() => handleCustomerListSort(\"lastOrderedDate\")}>\r\n                        <span className='flex flex-row items-center gap-1'>\r\n                          <span>Order Duration Days</span>\r\n                          {getSortIcon(\"lastOrderedDate\")}\r\n                        </span>\r\n                      </TableHead>\r\n                      <TableHead className=\"cursor-pointer\" onClick={() => handleCustomerListSort(\"fbDiscount\")}>\r\n                        <span className='flex flex-row items-center gap-1'><span>Freq Buyer Discount</span> {getSortIcon(\"fbDiscount\")} </span>\r\n                      </TableHead>\r\n                    </TableRow>\r\n                  </TableHeader>\r\n\r\n                  <TableBody>\r\n                    {customers.map((customer) => (\r\n                      <TableRow key={customer.buyerId}>\r\n                        <TableCell>\r\n                          <div className='flex flex-row gap-2 items-center'>\r\n                            <div className=\"text-blue-600 hover:underline cursor-pointer\">\r\n                              <div>{customer.buyerName !== \"\" ? customer.buyerName : \"( Name not given )\"}</div>\r\n                            </div>\r\n                            <Dialog>\r\n                              <DialogTrigger asChild>\r\n                                <Info size={18} className=\"cursor-pointer text-gray-600 hover:text-purple-600 transition-all\" />\r\n                              </DialogTrigger>\r\n                              <DialogContent className=\"sm:max-w-fit p-5 rounded-lg\">\r\n                                <DialogHeader>\r\n                                  <DialogTitle className=\"text-lg font-semibold text-gray-800\">About Customer</DialogTitle>\r\n                                </DialogHeader>\r\n                                <div className=\"flex flex-col gap-3 mt-2\">\r\n                                  <div className=\"flex flex-row gap-2\">\r\n                                    <span className=\"text-sm text-gray-600\">Buyer Name:</span>\r\n                                    <span className=\"text-base text-purple-800 font-semibold\">{customer.buyerName}</span>\r\n                                  </div>\r\n                                  <div className=\"flex flex-row gap-2\">\r\n                                    <span className=\"text-sm text-gray-600\">Mobile Number:</span>\r\n                                    <span className=\"text-base text-purple-800 font-semibold\">{customer.mobileNumber}</span>\r\n                                  </div>\r\n                                </div>\r\n                              </DialogContent>\r\n                            </Dialog>\r\n                          </div>\r\n                        </TableCell>\r\n                        <TableCell>{customer.totalOrders}</TableCell>\r\n                        <TableCell>₹ {customer.pendingAmount.toLocaleString('en-IN')}</TableCell>\r\n                        <TableCell>\r\n                          {customer.lastOrderedDate\r\n                            ? (isNaN(new Date(customer.lastOrderedDate).getTime())\r\n                              ? '-'\r\n                              : Math.floor((new Date().getTime() - new Date(customer.lastOrderedDate).getTime()) / (1000 * 60 * 60 * 24)) + ' days')\r\n                            : '-'}\r\n                        </TableCell>\r\n                        <TableCell className='flex flex-row items-center gap-2'>\r\n                          {isFbDis[customer.nBuyerId] ? (\r\n                            <div className=\"flex flex-row justify-center items-center gap-3\">\r\n                              <Input\r\n                                value={String(fbDiscounts[customer.nBuyerId] ?? customer.fbDiscount ?? \"\")}\r\n                                onChange={(e) => {\r\n                                  const val = e.target.value;\r\n                                  if (!/^\\d*\\.?\\d*$/.test(val)) return;\r\n                                  let numVal = parseFloat(val);\r\n                                  if (numVal > 100) numVal = 100;\r\n                                  if (numVal < 0 || isNaN(numVal)) numVal = 0;\r\n                                  handleChangeFbDiscount(customer.nBuyerId, String(numVal));\r\n                                }}\r\n                                disabled={!isFbDis[customer.nBuyerId]}\r\n                                type=\"number\"\r\n                                min=\"0\"\r\n                                max=\"100\"\r\n                                step=\"0.01\"\r\n                              />\r\n                              <Save\r\n                                size={24}\r\n                                onClick={() => handleSave(customer.nBuyerId)}\r\n                                className=\"cursor-pointer text-green-500\"\r\n                              />\r\n                              <X\r\n                                color=\"red\"\r\n                                size={24}\r\n                                className=\"cursor-pointer text-red-500\"\r\n                                onClick={() => setIsFbDis({})}\r\n                              />\r\n                            </div>\r\n                          ) : (\r\n                            <div className='flex flex-row gap-3 items-center justify-center'>\r\n                              <span>{customer.fbDiscount > 0 ? `${customer.fbDiscount} %` : \"-\"}</span>\r\n                              <Pencil\r\n                                size={15}\r\n                                onClick={() => setIsFbDis({ [customer.nBuyerId]: true })}\r\n                                className=\"cursor-pointer text-blue-500\"\r\n                              />\r\n                            </div>\r\n                          )}\r\n                        </TableCell>\r\n                      </TableRow>\r\n                    ))}\r\n                  </TableBody>\r\n                </Table>\r\n\r\n                {/* Pagination */}\r\n                <Card className=\"p-0 mb-1\">\r\n                  <CardContent className=\"px-1.5 py-1\">\r\n                    <div className=\"flex flex-row items-center justify-end gap-1.5\">\r\n                      <Select value={pageSize.toString()} onValueChange={(value) => handlePageSizeChange(Number(value))}>\r\n                        <SelectTrigger className=\"w-[140px] h-[36px]\">\r\n                          <SelectValue />\r\n                        </SelectTrigger>\r\n                        <SelectContent>\r\n                          <SelectItem value=\"20\">20 per Page</SelectItem>\r\n                          <SelectItem value=\"50\">50 per Page</SelectItem>\r\n                          <SelectItem value=\"100\">100 per Page</SelectItem>\r\n                        </SelectContent>\r\n                      </Select>\r\n                      <Select value={currentPage.toString()} onValueChange={(value) => handlePageChange(Number(value))}>\r\n                        <SelectTrigger className=\"w-[140px] h-[36px]\">\r\n                          <SelectValue />\r\n                        </SelectTrigger>\r\n                        <SelectContent>\r\n                          {Array.from({ length: Math.ceil((customers.length || 1) / pageSize) }, (_, i) => (\r\n                            <SelectItem key={i} value={i.toString()}>\r\n                              Page {i + 1}\r\n                            </SelectItem>\r\n                          ))}\r\n                        </SelectContent>\r\n                      </Select>\r\n                    </div>\r\n                  </CardContent>\r\n                </Card>\r\n              </CardContent>\r\n            </Card>\r\n          </TabsContent>\r\n\r\n          <TabsContent value=\"Customer Loyalty\">\r\n            <div className=\"flex items-center justify-center min-h-[400px]\">\r\n              <Card className=\"w-full max-w-md\">\r\n                <CardHeader className=\"text-center\">\r\n                  <div className=\"mx-auto mb-4 w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center\">\r\n                    <svg className=\"w-8 h-8 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\" />\r\n                    </svg>\r\n                  </div>\r\n                  <CardTitle className=\"text-2xl\">Loyalty Program Coming Soon</CardTitle>\r\n                  <CardDescription className=\"text-lg\">\r\n                    We&apos;re working hard to bring you comprehensive restaurant analytics and insights.\r\n                  </CardDescription>\r\n                </CardHeader>\r\n                <CardContent className=\"text-center\">\r\n                  <p className=\"text-gray-500 mb-4\">\r\n                    Get ready for powerful metrics, real-time data, and actionable insights to grow your business.\r\n                  </p>\r\n                  <div className=\"inline-flex items-center px-4 py-2 bg-gradient-to-r from-blue-500 to-purple-600 text-black rounded-lg\">\r\n                    <span className=\"mr-2\">🚀</span>\r\n                    Launching Soon\r\n                  </div>\r\n                </CardContent>\r\n              </Card>\r\n            </div>\r\n          </TabsContent>\r\n        </Tabs>\r\n\r\n        {/* Customer Details Modal */}\r\n        <CustomerDetailsModal customer={selectedCustomer} onClose={() => setSelectedCustomer(null)} onAction={handleAction} />\r\n\r\n        {/* Action Modal */}\r\n        <ActionModal\r\n          customer={actionSelectedCustomer}\r\n          actionType={actionType}\r\n          onClose={() => {\r\n            setActionSelectedCustomer(null)\r\n            setActionType(\"\")\r\n          }}\r\n          isSubmitting={isSubmitting}\r\n          onSubmit={handleSubmitAction}\r\n        />\r\n      </div>\r\n    </div >\r\n  )\r\n}\r\n\r\n\r\n// Customer Details Modal\r\ninterface CustomerDetailsModalProps {\r\n  customer: BuyerDetails | null\r\n  onClose: () => void\r\n  onAction: (customer: BuyerDetails, action: string) => void\r\n}\r\n\r\nexport function CustomerDetailsModal({ customer, onClose }: CustomerDetailsModalProps) {\r\n  if (!customer) return null\r\n\r\n  const handlePhoneClick = (phoneNumber: string) => {\r\n    window.open(`tel:${phoneNumber}`, \"_self\")\r\n  }\r\n\r\n  return (\r\n    <Dialog open={!!customer} onOpenChange={onClose}>\r\n      <DialogContent className=\"max-w-4xl max-h-[90vh] overflow-y-auto w-[95vw] sm:w-full rounded-md\">\r\n        <DialogHeader>\r\n          <DialogTitle className=\"text-lg sm:text-xl flex items-center gap-2\">\r\n            Customer Details - #{customer.buyerId}\r\n          </DialogTitle>\r\n        </DialogHeader>\r\n\r\n        <div className=\"space-y-4\">\r\n          <div className=\"space-y-2\">\r\n            <div className=\"flex items-center gap-2\">\r\n              <User className=\"w-4 h-4\" />\r\n              <span className=\"font-medium\">{customer.buyerName}</span>\r\n            </div>\r\n            <div className=\"flex items-center gap-2\">\r\n              <Phone className=\"w-4 h-4\" />\r\n              <span>{customer.mobileNumber}</span>\r\n              <Button\r\n                size=\"sm\"\r\n                variant=\"outline\"\r\n                onClick={() => handlePhoneClick(customer.mobileNumber)}\r\n                className=\"h-6 w-6 p-0\"\r\n              >\r\n                <Phone className=\"w-3 h-3\" />\r\n              </Button>\r\n            </div>\r\n            <div className=\"flex items-center gap-2\">\r\n              <MapPin className=\"w-4 h-4\" />\r\n              <span>{customer.address}</span>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Action Buttons */}\r\n          <div className=\"flex gap-2 pt-4 border-t\">\r\n            <Button variant=\"outline\" onClick={onClose}>\r\n              Close\r\n            </Button>\r\n          </div>\r\n        </div>\r\n      </DialogContent>\r\n    </Dialog>\r\n  )\r\n}\r\n\r\n\r\n// Action Modal\r\ninterface ActionModalProps {\r\n  customer: BuyerDetails | null\r\n  actionType: string\r\n  onClose: () => void\r\n  isSubmitting: boolean\r\n  onSubmit: (formData: Record<string, unknown>) => void\r\n}\r\n\r\nexport function ActionModal({ customer, actionType, onClose, isSubmitting, onSubmit }: ActionModalProps) {\r\n  const [formData] = useState({\r\n    reason: \"\"\r\n  })\r\n\r\n  const handleSubmit = (e: React.FormEvent) => {\r\n    e.preventDefault()\r\n    onSubmit(formData)\r\n  }\r\n\r\n  if (!customer || !actionType) return null\r\n\r\n  return (\r\n    <Dialog open={true} onOpenChange={onClose}>\r\n      <DialogContent className=\"max-w-2xl rounded-md\">\r\n        <DialogHeader>\r\n          <DialogTitle>\r\n            Action Modal\r\n          </DialogTitle>\r\n        </DialogHeader>\r\n        <div className=\"space-y-4\">\r\n          <div className=\"flex gap-2 pt-4\">\r\n            <Button variant=\"outline\" onClick={onClose} disabled={isSubmitting}>\r\n              Cancel\r\n            </Button>\r\n            <Button onClick={handleSubmit} disabled={isSubmitting}>\r\n              {isSubmitting ? \"Processing...\" : \"Confirm\"}\r\n            </Button>\r\n          </div>\r\n        </div>\r\n      </DialogContent>\r\n    </Dialog>\r\n  )\r\n}"], "names": ["o", "r", "t", "_isNativeReflectConstruct", "_getPrototypeOf", "_setPrototypeOf", "p", "Line", "dataPointFormatter", "PureComponent", "legendData", "CustomTick", "jsx", "jsxs", "SellerCustomers", "sales", "customerConversionRates", "customerAcquisitionRate", "week", "customers", "currentPage", "tabValue", "sortByvalue", "searchBy", "sortByOrder", "useLoaderData", "selectedCustomer", "setSelectedCustomer", "useState", "activeTab", "setActiveTab", "fetcher", "useFetcher", "searchParams", "setSearchParams", "useSearchParams", "actionType", "setActionType", "actionSelectedCustomer", "setActionSelectedCustomer", "isSubmitting", "setIsSubmitting", "toast", "useToast", "searchType", "setSearchType", "searchTerm", "setSearchTerm", "filterDate", "setFilterDate", "pageSize", "setPageSize", "date<PERSON><PERSON><PERSON>", "setDateRange", "customerListSearchTerm", "setCustomerListSearchTerm", "customerListSortBy", "setCustomerListSortBy", "customerListSortOrder", "setCustomerListSortOrder", "customerListActiveTab", "setCustomerListActiveTab", "fbDiscounts", "setFbDiscounts", "isFbDis", "setIsFbDis", "useEffect", "timer", "setTimeout", "clearTimeout", "updateCustomerListParams", "params", "newSearchParams", "URLSearchParams", "Object", "entries", "for<PERSON>ach", "key", "value", "set", "delete", "handleTabChange", "newTab", "handleAction", "customer", "action", "handleSubmitAction", "formData", "actionData", "FormData", "append", "JSON", "stringify", "submit", "method", "handleCustomerListTabChange", "tabMap", "all", "oneOrder", "frequent", "zero_orders", "validTabValue", "page", "sortBy", "handleCustomerListSort", "newSort", "prevSortBy", "isSameSort", "prevSortOrder", "newOrder", "getSortIcon", "column", "ArrowUp", "className", "ArrowDown", "handleCustomerListSearch", "e", "target", "length", "handleChangeFbDiscount", "nBuyerId", "val", "test", "prev", "fb<PERSON><PERSON><PERSON>", "handleSave", "toString", "handlePageSizeChange", "newPageSize", "handlePageChange", "newPage", "data", "intent", "success", "title", "description", "errorMessage", "variant", "children", "Tabs", "onValueChange", "TabsList", "TabsTrigger", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "CustomerSales", "ConversionRate", "conversionRates", "CustomersAcquisition", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Input", "placeholder", "onChange", "Select", "SelectTrigger", "SelectValue", "SelectContent", "SelectItem", "Table", "state", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "loading", "TableHeader", "TableRow", "TableHead", "onClick", "TableBody", "map", "TableCell", "buyerName", "Dialog", "DialogTrigger", "<PERSON><PERSON><PERSON><PERSON>", "Info", "size", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogHeader", "DialogTitle", "mobileNumber", "totalOrders", "pendingAmount", "toLocaleString", "lastOrderedDate", "isNaN", "Date", "getTime", "Math", "floor", "String", "fbDiscount", "numVal", "parseFloat", "disabled", "type", "min", "max", "step", "Save", "X", "color", "Pencil", "buyerId", "Number", "Array", "from", "ceil", "_", "i", "<PERSON><PERSON><PERSON><PERSON>", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "CardTitle", "CardDescription", "CustomerDetailsModal", "onClose", "onAction", "ActionModal", "onSubmit", "handlePhoneClick", "phoneNumber", "window", "open", "onOpenChange", "User", "Phone", "<PERSON><PERSON>", "MapPin", "address", "reason", "handleSubmit", "preventDefault"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAI,YAAY,CAAC,QAAQ,UAAU,gBAAgB,KAAK,GACtD,aAAa,CAAC,KAAK;AACrB,SAAS,QAAQ,GAAG;AAAE;AAA2B,SAAO,UAAU,cAAc,OAAO,UAAU,YAAY,OAAO,OAAO,WAAW,SAAUA,IAAG;AAAE,WAAO,OAAOA;AAAA,MAAO,SAAUA,IAAG;AAAE,WAAOA,MAAK,cAAc,OAAO,UAAUA,GAAE,gBAAgB,UAAUA,OAAM,OAAO,YAAY,WAAW,OAAOA;AAAA,EAAE,GAAI,QAAQ,CAAC;AAAE;AAC5T,SAAS,yBAAyB,QAAQ,UAAU;AAAE,MAAI,UAAU,KAAM,QAAO;AAAI,MAAI,SAAS,8BAA8B,QAAQ,QAAQ;AAAG,MAAI,KAAK;AAAG,MAAI,OAAO,uBAAuB;AAAE,QAAI,mBAAmB,OAAO,sBAAsB,MAAM;AAAG,SAAK,IAAI,GAAG,IAAI,iBAAiB,QAAQ,KAAK;AAAE,YAAM,iBAAiB,CAAC;AAAG,UAAI,SAAS,QAAQ,GAAG,KAAK,EAAG;AAAU,UAAI,CAAC,OAAO,UAAU,qBAAqB,KAAK,QAAQ,GAAG,EAAG;AAAU,aAAO,GAAG,IAAI,OAAO,GAAG;AAAA,IAAE;AAAA,EAAI;AAAC,SAAO;AAAO;AAC1e,SAAS,8BAA8B,QAAQ,UAAU;AAAE,MAAI,UAAU,KAAM,QAAO;AAAI,MAAI,SAAS,CAAA;AAAI,WAAS,OAAO,QAAQ;AAAE,QAAI,OAAO,UAAU,eAAe,KAAK,QAAQ,GAAG,GAAG;AAAE,UAAI,SAAS,QAAQ,GAAG,KAAK,EAAG;AAAU,aAAO,GAAG,IAAI,OAAO,GAAG;AAAA,IAAI;AAAA,EAAA;AAAG,SAAO;AAAO;AACrR,SAAS,WAAW;AAAE,aAAW,OAAO,SAAS,OAAO,OAAO,KAAM,IAAG,SAAU,QAAQ;AAAE,aAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AAAE,UAAI,SAAS,UAAU,CAAC;AAAG,eAAS,OAAO,QAAQ;AAAE,YAAI,OAAO,UAAU,eAAe,KAAK,QAAQ,GAAG,GAAG;AAAE,iBAAO,GAAG,IAAI,OAAO,GAAG;AAAA,QAAI;AAAA,MAAA;AAAA;AAAK,WAAO;AAAA,EAAO;AAAI,SAAO,SAAS,MAAM,MAAM,SAAS;AAAE;AACjV,SAAS,QAAQ,GAAG,GAAG;AAAE,MAAI,IAAI,OAAO,KAAK,CAAC;AAAG,MAAI,OAAO,uBAAuB;AAAE,QAAI,IAAI,OAAO,sBAAsB,CAAC;AAAG,UAAM,IAAI,EAAE,OAAO,SAAUC,IAAG;AAAE,aAAO,OAAO,yBAAyB,GAAGA,EAAC,EAAE;AAAA,IAAa,CAAA,IAAI,EAAE,KAAK,MAAM,GAAG,CAAC;AAAA;AAAK,SAAO;AAAE;AAC7P,SAAS,cAAc,GAAG;AAAE,WAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AAAE,QAAI,IAAI,QAAQ,UAAU,CAAC,IAAI,UAAU,CAAC,IAAI,CAAA;AAAI,QAAI,IAAI,QAAQ,OAAO,CAAC,GAAG,IAAE,EAAE,QAAQ,SAAUA,IAAG;AAAE,sBAAgB,GAAGA,IAAG,EAAEA,EAAC,CAAC;AAAA,IAAI,CAAA,IAAI,OAAO,4BAA4B,OAAO,iBAAiB,GAAG,OAAO,0BAA0B,CAAC,CAAC,IAAI,QAAQ,OAAO,CAAC,CAAC,EAAE,QAAQ,SAAUA,IAAG;AAAE,aAAO,eAAe,GAAGA,IAAG,OAAO,yBAAyB,GAAGA,EAAC,CAAC;AAAA,IAAI,CAAA;AAAA,EAAE;AAAG,SAAO;AAAE;AACrb,SAAS,mBAAmB,KAAK;AAAE,SAAO,mBAAmB,GAAG,KAAK,iBAAiB,GAAG,KAAK,4BAA4B,GAAG,KAAK,mBAAkB;AAAG;AACvJ,SAAS,qBAAqB;AAAE,QAAM,IAAI,UAAU,sIAAsI;AAAE;AAC5L,SAAS,4BAA4B,GAAG,QAAQ;AAAE,MAAI,CAAC,EAAG;AAAQ,MAAI,OAAO,MAAM,SAAU,QAAO,kBAAkB,GAAG,MAAM;AAAG,MAAI,IAAI,OAAO,UAAU,SAAS,KAAK,CAAC,EAAE,MAAM,GAAG,EAAE;AAAG,MAAI,MAAM,YAAY,EAAE,YAAa,KAAI,EAAE,YAAY;AAAM,MAAI,MAAM,SAAS,MAAM,MAAO,QAAO,MAAM,KAAK,CAAC;AAAG,MAAI,MAAM,eAAe,2CAA2C,KAAK,CAAC,EAAG,QAAO,kBAAkB,GAAG,MAAM;AAAE;AAC9Z,SAAS,iBAAiB,MAAM;AAAE,MAAI,OAAO,WAAW,eAAe,KAAK,OAAO,QAAQ,KAAK,QAAQ,KAAK,YAAY,KAAK,KAAM,QAAO,MAAM,KAAK,IAAI;AAAE;AAC5J,SAAS,mBAAmB,KAAK;AAAE,MAAI,MAAM,QAAQ,GAAG,EAAG,QAAO,kBAAkB,GAAG;AAAE;AACzF,SAAS,kBAAkB,KAAK,KAAK;AAAE,MAAI,OAAO,QAAQ,MAAM,IAAI,OAAQ,OAAM,IAAI;AAAQ,WAAS,IAAI,GAAG,OAAO,IAAI,MAAM,GAAG,GAAG,IAAI,KAAK,IAAK,MAAK,CAAC,IAAI,IAAI,CAAC;AAAG,SAAO;AAAK;AACjL,SAAS,gBAAgB,UAAU,aAAa;AAAE,MAAI,EAAE,oBAAoB,cAAc;AAAE,UAAM,IAAI,UAAU,mCAAmC;AAAA,EAAI;AAAA;AACvJ,SAAS,kBAAkB,QAAQ,OAAO;AAAE,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AAAE,QAAI,aAAa,MAAM,CAAC;AAAG,eAAW,aAAa,WAAW,cAAc;AAAO,eAAW,eAAe;AAAM,QAAI,WAAW,WAAY,YAAW,WAAW;AAAM,WAAO,eAAe,QAAQ,eAAe,WAAW,GAAG,GAAG,UAAU;AAAA,EAAI;AAAA;AAC3U,SAAS,aAAa,aAAa,YAAY,aAAa;AAAE,MAAI,WAAY,mBAAkB,YAAY,WAAW,UAAU;AAAG,MAAI,YAAa,mBAAkB,aAAa,WAAW;AAAG,SAAO,eAAe,aAAa,aAAa,EAAE,UAAU,MAAK,CAAE;AAAG,SAAO;AAAY;AAC3R,SAAS,WAAW,GAAG,GAAG,GAAG;AAAE,SAAO,IAAI,gBAAgB,CAAC,GAAG,2BAA2B,GAAG,0BAAyB,IAAK,QAAQ,UAAU,GAAG,KAAK,IAAI,gBAAgB,CAAC,EAAE,WAAW,IAAI,EAAE,MAAM,GAAG,CAAC,CAAC;AAAE;AACzM,SAAS,2BAA2B,MAAM,MAAM;AAAE,MAAI,SAAS,QAAQ,IAAI,MAAM,YAAY,OAAO,SAAS,aAAa;AAAE,WAAO;AAAA,aAAiB,SAAS,QAAQ;AAAE,UAAM,IAAI,UAAU,0DAA0D;AAAA,EAAI;AAAC,SAAO,uBAAuB,IAAI;AAAE;AAC9R,SAAS,uBAAuB,MAAM;AAAE,MAAI,SAAS,QAAQ;AAAE,UAAM,IAAI,eAAe,2DAA2D;AAAA,EAAI;AAAC,SAAO;AAAK;AACpK,SAAS,4BAA4B;AAAE,MAAI;AAAE,QAAI,IAAI,CAAC,QAAQ,UAAU,QAAQ,KAAK,QAAQ,UAAU,SAAS,CAAA,GAAI,WAAY;AAAA,KAAE,CAAC;AAAA,EAAE,SAAUC,IAAG;AAAA,EAAA;AAAG,UAAQ,4BAA4B,SAASC,6BAA4B;AAAE,WAAO,CAAC,CAAC;AAAA,EAAI,GAAA;AAAI;AACjP,SAAS,gBAAgB,GAAG;AAAE,oBAAkB,OAAO,iBAAiB,OAAO,eAAe,SAAS,SAASC,iBAAgBJ,IAAG;AAAE,WAAOA,GAAE,aAAa,OAAO,eAAeA,EAAC;AAAA,EAAE;AAAI,SAAO,gBAAgB,CAAC;AAAE;AAClN,SAAS,UAAU,UAAU,YAAY;AAAE,MAAI,OAAO,eAAe,cAAc,eAAe,MAAM;AAAE,UAAM,IAAI,UAAU,oDAAoD;AAAA;AAAK,WAAS,YAAY,OAAO,OAAO,cAAc,WAAW,WAAW,EAAE,aAAa,EAAE,OAAO,UAAU,UAAU,MAAM,cAAc,OAAQ,CAAA;AAAG,SAAO,eAAe,UAAU,aAAa,EAAE,UAAU,OAAO;AAAG,MAAI,WAAY,iBAAgB,UAAU,UAAU;AAAE;AAClc,SAAS,gBAAgB,GAAG,GAAG;AAAE,oBAAkB,OAAO,iBAAiB,OAAO,eAAe,KAAM,IAAG,SAASK,iBAAgBL,IAAGM,IAAG;AAAE,IAAAN,GAAE,YAAYM;AAAG,WAAON;AAAA,EAAI;AAAE,SAAO,gBAAgB,GAAG,CAAC;AAAE;AACtM,SAAS,gBAAgB,KAAK,KAAK,OAAO;AAAE,QAAM,eAAe,GAAG;AAAG,MAAI,OAAO,KAAK;AAAE,WAAO,eAAe,KAAK,KAAK,EAAE,OAAc,YAAY,MAAM,cAAc,MAAM,UAAU,KAAM,CAAA;AAAA,EAAE,OAAQ;AAAE,QAAI,GAAG,IAAI;AAAA,EAAM;AAAG,SAAO;AAAI;AAC1O,SAAS,eAAe,GAAG;AAAE,MAAI,IAAI,aAAa,GAAG,QAAQ;AAAG,SAAO,YAAY,QAAQ,CAAC,IAAI,IAAI,IAAI;AAAG;AAC3G,SAAS,aAAa,GAAG,GAAG;AAAE,MAAI,YAAY,QAAQ,CAAC,KAAK,CAAC,EAAG,QAAO;AAAG,MAAI,IAAI,EAAE,OAAO,WAAW;AAAG,MAAI,WAAW,GAAG;AAAE,QAAI,IAAI,EAAE,KAAK,GAAG,CAAc;AAAG,QAAI,YAAY,QAAQ,CAAC,EAAG,QAAO;AAAG,UAAM,IAAI,UAAU,8CAA8C;AAAA,EAAI;AAAC,SAAyB,OAAiB,CAAC;AAAE;AAmBnT,IAAI,OAAoB,yBAAU,gBAAgB;AACvD,WAASO,QAAO;AACd,QAAI;AACJ,oBAAgB,MAAMA,KAAI;AAC1B,aAAS,OAAO,UAAU,QAAQ,OAAO,IAAI,MAAM,IAAI,GAAG,OAAO,GAAG,OAAO,MAAM,QAAQ;AACvF,WAAK,IAAI,IAAI,UAAU,IAAI;AAAA,IACjC;AACI,YAAQ,WAAW,MAAMA,OAAM,CAAA,EAAG,OAAO,IAAI,CAAC;AAC9C,oBAAgB,OAAO,SAAS;AAAA,MAC9B,qBAAqB;AAAA,MACrB,aAAa;AAAA,IACnB,CAAK;AACD,oBAAgB,OAAO,iCAAiC,SAAU,aAAa,QAAQ;AACrF,aAAO,GAAG,OAAO,QAAQ,KAAK,EAAE,OAAO,cAAc,QAAQ,IAAI;AAAA,IACvE,CAAK;AACD,oBAAgB,OAAO,sBAAsB,SAAU,QAAQ,aAAa,OAAO;AACjF,UAAI,aAAa,MAAM,OAAO,SAAU,KAAK,MAAM;AACjD,eAAO,MAAM;AAAA,MACrB,CAAO;AAGD,UAAI,CAAC,YAAY;AACf,eAAO,MAAM,8BAA8B,aAAa,MAAM;AAAA,MACtE;AACM,UAAI,QAAQ,KAAK,MAAM,SAAS,UAAU;AAC1C,UAAI,eAAe,SAAS;AAC5B,UAAI,aAAa,cAAc;AAC/B,UAAI,cAAc,CAAE;AACpB,eAAS,IAAI,GAAG,MAAM,GAAG,IAAI,MAAM,QAAQ,OAAO,MAAM,CAAC,GAAG,EAAE,GAAG;AAC/D,YAAI,MAAM,MAAM,CAAC,IAAI,cAAc;AACjC,wBAAc,CAAE,EAAC,OAAO,mBAAmB,MAAM,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,eAAe,GAAG,CAAC;AACnF;AAAA,QACV;AAAA,MACA;AACM,UAAI,aAAa,YAAY,SAAS,MAAM,IAAI,CAAC,GAAG,UAAU,IAAI,CAAC,UAAU;AAC7E,aAAO,CAAA,EAAG,OAAO,mBAAmBA,MAAK,OAAO,OAAO,KAAK,CAAC,GAAG,mBAAmB,WAAW,GAAG,UAAU,EAAE,IAAI,SAAU,MAAM;AAC/H,eAAO,GAAG,OAAO,MAAM,IAAI;AAAA,MACnC,CAAO,EAAE,KAAK,IAAI;AAAA,IAClB,CAAK;AACD,oBAAgB,OAAO,MAAM,SAAS,gBAAgB,CAAC;AACvD,oBAAgB,OAAO,WAAW,SAAU,MAAM;AAChD,YAAM,YAAY;AAAA,IACxB,CAAK;AACD,oBAAgB,OAAO,sBAAsB,WAAY;AACvD,YAAM,SAAS;AAAA,QACb,qBAAqB;AAAA,MAC7B,CAAO;AACD,UAAI,MAAM,MAAM,gBAAgB;AAC9B,cAAM,MAAM,eAAgB;AAAA,MACpC;AAAA,IACA,CAAK;AACD,oBAAgB,OAAO,wBAAwB,WAAY;AACzD,YAAM,SAAS;AAAA,QACb,qBAAqB;AAAA,MAC7B,CAAO;AACD,UAAI,MAAM,MAAM,kBAAkB;AAChC,cAAM,MAAM,iBAAkB;AAAA,MACtC;AAAA,IACA,CAAK;AACD,WAAO;AAAA,EACX;AACE,YAAUA,OAAM,cAAc;AAC9B,SAAO,aAAaA,OAAM,CAAC;AAAA,IACzB,KAAK;AAAA,IACL,OAAO,SAAS,oBAAoB;AAClC,UAAI,CAAC,KAAK,MAAM,mBAAmB;AACjC;AAAA,MACR;AACM,UAAI,cAAc,KAAK,eAAgB;AACvC,WAAK,SAAS;AAAA,QACZ;AAAA,MACR,CAAO;AAAA,IACP;AAAA,EACA,GAAK;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,qBAAqB;AACnC,UAAI,CAAC,KAAK,MAAM,mBAAmB;AACjC;AAAA,MACR;AACM,UAAI,cAAc,KAAK,eAAgB;AACvC,UAAI,gBAAgB,KAAK,MAAM,aAAa;AAC1C,aAAK,SAAS;AAAA,UACZ;AAAA,QACV,CAAS;AAAA,MACT;AAAA,IACA;AAAA,EACA,GAAK;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,iBAAiB;AAC/B,UAAI,WAAW,KAAK;AACpB,UAAI;AACF,eAAO,YAAY,SAAS,kBAAkB,SAAS,eAAgB,KAAI;AAAA,MAC5E,SAAQ,KAAK;AACZ,eAAO;AAAA,MACf;AAAA,IACA;AAAA,EACA,GAAK;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,eAAe,UAAU,YAAY;AACnD,UAAI,KAAK,MAAM,qBAAqB,CAAC,KAAK,MAAM,qBAAqB;AACnE,eAAO;AAAA,MACf;AACM,UAAI,cAAc,KAAK,OACrB,SAAS,YAAY,QACrB,QAAQ,YAAY,OACpB,QAAQ,YAAY,OACpB,SAAS,YAAY,QACrB,WAAW,YAAY;AACzB,UAAI,gBAAgB,cAAc,UAAU,QAAQ;AACpD,UAAI,CAAC,eAAe;AAClB,eAAO;AAAA,MACf;AACM,UAAI,qBAAqB,SAASC,oBAAmB,WAAW,SAAS;AACvE,eAAO;AAAA,UACL,GAAG,UAAU;AAAA,UACb,GAAG,UAAU;AAAA,UACb,OAAO,UAAU;AAAA,UACjB,UAAU,kBAAkB,UAAU,SAAS,OAAO;AAAA,QACvD;AAAA,MACF;AACD,UAAI,gBAAgB;AAAA,QAClB,UAAU,WAAW,iBAAiB,OAAO,YAAY,GAAG,IAAI;AAAA,MACjE;AACD,aAAoB,sBAAM,cAAc,OAAO,eAAe,cAAc,IAAI,SAAU,MAAM;AAC9F,eAAoB,sBAAM,aAAa,MAAM;AAAA,UAC3C,KAAK,OAAO,OAAO,KAAK,MAAM,OAAO;AAAA,UACrC,MAAM;AAAA,UACN;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACV,CAAS;AAAA,MACT,CAAO,CAAC;AAAA,IACR;AAAA,EACA,GAAK;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,WAAW,UAAU,SAAS,YAAY;AACxD,UAAI,oBAAoB,KAAK,MAAM;AACnC,UAAI,qBAAqB,CAAC,KAAK,MAAM,qBAAqB;AACxD,eAAO;AAAA,MACf;AACM,UAAI,eAAe,KAAK,OACtB,MAAM,aAAa,KACnB,SAAS,aAAa,QACtB,UAAU,aAAa;AACzB,UAAI,YAAY,YAAY,KAAK,OAAO,KAAK;AAC7C,UAAI,iBAAiB,YAAY,KAAK,IAAI;AAC1C,UAAI,OAAO,OAAO,IAAI,SAAU,OAAO,GAAG;AACxC,YAAI,WAAW,cAAc,cAAc,cAAc;AAAA,UACvD,KAAK,OAAO,OAAO,CAAC;AAAA,UACpB,GAAG;AAAA,QACJ,GAAE,SAAS,GAAG,cAAc,GAAG,IAAI;AAAA,UAClC,OAAO,MAAM;AAAA,UACb;AAAA,UACA,IAAI,MAAM;AAAA,UACV,IAAI,MAAM;AAAA,UACV,OAAO;AAAA,UACP,SAAS,MAAM;AAAA,QACzB,CAAS;AACD,eAAOD,MAAK,cAAc,KAAK,QAAQ;AAAA,MAC/C,CAAO;AACD,UAAI,YAAY;AAAA,QACd,UAAU,WAAW,iBAAiB,OAAO,UAAU,KAAK,OAAO,EAAE,OAAO,YAAY,GAAG,IAAI;AAAA,MAChG;AACD,aAAoB,sBAAM,cAAc,OAAO,SAAS;AAAA,QACtD,WAAW;AAAA,QACX,KAAK;AAAA,MACb,GAAS,SAAS,GAAG,IAAI;AAAA,IACzB;AAAA,EACA,GAAK;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,sBAAsB,QAAQ,UAAU,YAAY,OAAO;AACtE,UAAC,eAAe,KAAK,OACtB,OAAO,aAAa,MACpB,SAAS,aAAa,QACtB,eAAe,aAAa;AACtB,mBAAa;AAC3B,UAAQ,SAAS,yBAAyB,cAAc,SAAS;AAC3D,UAAI,aAAa,cAAc,cAAc,cAAc,CAAA,GAAI,YAAY,QAAQ,IAAI,CAAC,GAAG,IAAI;AAAA,QAC7F,MAAM;AAAA,QACN,WAAW;AAAA,QACX,UAAU,WAAW,iBAAiB,OAAO,YAAY,GAAG,IAAI;AAAA,QAChE;AAAA,MACR,GAAS,KAAK,GAAG,IAAI;AAAA,QACb;AAAA,QACA;AAAA,QACA;AAAA,MACR,CAAO;AACD,aAAoB,sBAAM,cAAc,OAAO,SAAS,CAAA,GAAI,YAAY;AAAA,QACtE,SAAS,KAAK;AAAA,MACtB,CAAO,CAAC;AAAA,IACR;AAAA,EACA,GAAK;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,yBAAyB,UAAU,YAAY;AAC7D,UAAI,SAAS;AACb,UAAI,eAAe,KAAK,OACtB,SAAS,aAAa,QACtB,kBAAkB,aAAa,iBAC/B,oBAAoB,aAAa,mBACjC,iBAAiB,aAAa,gBAC9B,oBAAoB,aAAa,mBACjC,kBAAkB,aAAa,iBAC/B,cAAc,aAAa,aAC3B,mBAAmB,aAAa,kBAChC,QAAQ,aAAa,OACrB,SAAS,aAAa;AACxB,UAAI,cAAc,KAAK,OACrB,aAAa,YAAY,YACzB,cAAc,YAAY;AAC5B,aAAoB,sBAAM,cAAc,SAAS;AAAA,QAC/C,OAAO;AAAA,QACP,UAAU;AAAA,QACV,UAAU;AAAA,QACV,QAAQ;AAAA,QACR,MAAM;AAAA,UACJ,GAAG;AAAA,QACJ;AAAA,QACD,IAAI;AAAA,UACF,GAAG;AAAA,QACJ;AAAA,QACD,KAAK,QAAQ,OAAO,WAAW;AAAA,QAC/B,gBAAgB,KAAK;AAAA,QACrB,kBAAkB,KAAK;AAAA,MACxB,GAAE,SAAU,MAAM;AACjB,YAAI,IAAI,KAAK;AACb,YAAI,YAAY;AACd,cAAI,uBAAuB,WAAW,SAAS,OAAO;AACtD,cAAI,WAAW,OAAO,IAAI,SAAU,OAAO,OAAO;AAChD,gBAAI,iBAAiB,KAAK,MAAM,QAAQ,oBAAoB;AAC5D,gBAAI,WAAW,cAAc,GAAG;AAC9B,kBAAI,OAAO,WAAW,cAAc;AACpC,kBAAI,gBAAgB,kBAAkB,KAAK,GAAG,MAAM,CAAC;AACrD,kBAAI,gBAAgB,kBAAkB,KAAK,GAAG,MAAM,CAAC;AACrD,qBAAO,cAAc,cAAc,CAAE,GAAE,KAAK,GAAG,CAAA,GAAI;AAAA,gBACjD,GAAG,cAAc,CAAC;AAAA,gBAClB,GAAG,cAAc,CAAC;AAAA,cAClC,CAAe;AAAA,YACf;AAGY,gBAAI,kBAAkB;AACpB,kBAAI,iBAAiB,kBAAkB,QAAQ,GAAG,MAAM,CAAC;AACzD,kBAAI,iBAAiB,kBAAkB,SAAS,GAAG,MAAM,CAAC;AAC1D,qBAAO,cAAc,cAAc,CAAE,GAAE,KAAK,GAAG,CAAA,GAAI;AAAA,gBACjD,GAAG,eAAe,CAAC;AAAA,gBACnB,GAAG,eAAe,CAAC;AAAA,cACnC,CAAe;AAAA,YACf;AACY,mBAAO,cAAc,cAAc,CAAE,GAAE,KAAK,GAAG,CAAA,GAAI;AAAA,cACjD,GAAG,MAAM;AAAA,cACT,GAAG,MAAM;AAAA,YACvB,CAAa;AAAA,UACb,CAAW;AACD,iBAAO,OAAO,sBAAsB,UAAU,UAAU,UAAU;AAAA,QAC5E;AACQ,YAAI,eAAe,kBAAkB,GAAG,WAAW;AACnD,YAAI,YAAY,aAAa,CAAC;AAC9B,YAAI;AACJ,YAAI,iBAAiB;AACnB,cAAI,QAAQ,GAAG,OAAO,eAAe,EAAE,MAAM,WAAW,EAAE,IAAI,SAAU,KAAK;AAC3E,mBAAO,WAAW,GAAG;AAAA,UACjC,CAAW;AACD,mCAAyB,OAAO,mBAAmB,WAAW,aAAa,KAAK;AAAA,QAC1F,OAAe;AACL,mCAAyB,OAAO,8BAA8B,aAAa,SAAS;AAAA,QAC9F;AACQ,eAAO,OAAO,sBAAsB,QAAQ,UAAU,YAAY;AAAA,UAChE,iBAAiB;AAAA,QAC3B,CAAS;AAAA,MACT,CAAO;AAAA,IACP;AAAA,EACA,GAAK;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,YAAY,UAAU,YAAY;AAChD,UAAI,eAAe,KAAK,OACtB,SAAS,aAAa,QACtB,oBAAoB,aAAa;AACnC,UAAI,eAAe,KAAK,OACtB,aAAa,aAAa,YAC1B,cAAc,aAAa;AAC7B,UAAI,qBAAqB,UAAU,OAAO,WAAW,CAAC,cAAc,cAAc,KAAK,CAAC,QAAQ,YAAY,MAAM,IAAI;AACpH,eAAO,KAAK,yBAAyB,UAAU,UAAU;AAAA,MACjE;AACM,aAAO,KAAK,sBAAsB,QAAQ,UAAU,UAAU;AAAA,IACpE;AAAA,EACA,GAAK;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,SAAS;AACvB,UAAI;AACJ,UAAI,eAAe,KAAK,OACtB,OAAO,aAAa,MACpB,MAAM,aAAa,KACnB,SAAS,aAAa,QACtB,YAAY,aAAa,WACzB,QAAQ,aAAa,OACrB,QAAQ,aAAa,OACrB,MAAM,aAAa,KACnB,OAAO,aAAa,MACpB,QAAQ,aAAa,OACrB,SAAS,aAAa,QACtB,oBAAoB,aAAa,mBACjC,KAAK,aAAa;AACpB,UAAI,QAAQ,CAAC,UAAU,CAAC,OAAO,QAAQ;AACrC,eAAO;AAAA,MACf;AACM,UAAI,sBAAsB,KAAK,MAAM;AACrC,UAAI,iBAAiB,OAAO,WAAW;AACvC,UAAI,aAAa,KAAK,iBAAiB,SAAS;AAChD,UAAI,YAAY,SAAS,MAAM;AAC/B,UAAI,YAAY,SAAS,MAAM;AAC/B,UAAI,WAAW,aAAa;AAC5B,UAAI,aAAa,MAAM,EAAE,IAAI,KAAK,KAAK;AACvC,UAAI,SAAS,eAAe,YAAY,KAAK,KAAK,OAAO,QAAQ,iBAAiB,SAAS,eAAe;AAAA,QACtG,GAAG;AAAA,QACH,aAAa;AAAA,MACd,GACD,UAAU,MAAM,GAChB,IAAI,YAAY,SAAS,IAAI,SAC7B,oBAAoB,MAAM,aAC1B,cAAc,sBAAsB,SAAS,IAAI;AACnD,UAAI,QAAQ,WAAW,GAAG,IAAI,MAAM,CAAE,GACpC,gBAAgB,MAAM,SACtB,UAAU,kBAAkB,SAAS,OAAO;AAC9C,UAAI,UAAU,IAAI,IAAI;AACtB,aAAoB,sBAAM,cAAc,OAAO;AAAA,QAC7C,WAAW;AAAA,MACnB,GAAS,aAAa,YAAyB,sBAAM,cAAc,QAAQ,MAAmB,sBAAM,cAAc,YAAY;AAAA,QACtH,IAAI,YAAY,OAAO,UAAU;AAAA,MACzC,GAAsB,sBAAM,cAAc,QAAQ;AAAA,QAC1C,GAAG,YAAY,OAAO,OAAO,QAAQ;AAAA,QACrC,GAAG,YAAY,MAAM,MAAM,SAAS;AAAA,QACpC,OAAO,YAAY,QAAQ,QAAQ;AAAA,QACnC,QAAQ,YAAY,SAAS,SAAS;AAAA,MACvC,CAAA,CAAC,GAAG,CAAC,WAAwB,sBAAM,cAAc,YAAY;AAAA,QAC5D,IAAI,iBAAiB,OAAO,UAAU;AAAA,MAC9C,GAAsB,sBAAM,cAAc,QAAQ;AAAA,QAC1C,GAAG,OAAO,UAAU;AAAA,QACpB,GAAG,MAAM,UAAU;AAAA,QACnB,OAAO,QAAQ;AAAA,QACf,QAAQ,SAAS;AAAA,MAClB,CAAA,CAAC,CAAC,IAAI,MAAM,CAAC,kBAAkB,KAAK,YAAY,UAAU,UAAU,GAAG,KAAK,eAAe,UAAU,UAAU,IAAI,kBAAkB,QAAQ,KAAK,WAAW,UAAU,SAAS,UAAU,IAAI,CAAC,qBAAqB,wBAAwB,UAAU,mBAAmB,KAAK,OAAO,MAAM,CAAC;AAAA,IACnS;AAAA,EACG,CAAA,GAAG,CAAC;AAAA,IACH,KAAK;AAAA,IACL,OAAO,SAAS,yBAAyB,WAAW,WAAW;AAC7D,UAAI,UAAU,gBAAgB,UAAU,iBAAiB;AACvD,eAAO;AAAA,UACL,iBAAiB,UAAU;AAAA,UAC3B,WAAW,UAAU;AAAA,UACrB,YAAY,UAAU;AAAA,QACvB;AAAA,MACT;AACM,UAAI,UAAU,WAAW,UAAU,WAAW;AAC5C,eAAO;AAAA,UACL,WAAW,UAAU;AAAA,QACtB;AAAA,MACT;AACM,aAAO;AAAA,IACb;AAAA,EACA,GAAK;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,OAAO,OAAO,OAAO;AACnC,UAAI,YAAY,MAAM,SAAS,MAAM,IAAI,CAAA,EAAG,OAAO,mBAAmB,KAAK,GAAG,CAAC,CAAC,CAAC,IAAI;AACrF,UAAI,SAAS,CAAE;AACf,eAAS,IAAI,GAAG,IAAI,OAAO,EAAE,GAAG;AAC9B,iBAAS,CAAE,EAAC,OAAO,mBAAmB,MAAM,GAAG,mBAAmB,SAAS,CAAC;AAAA,MACpF;AACM,aAAO;AAAA,IACb;AAAA,EACA,GAAK;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,cAAc,QAAQ,OAAO;AAC3C,UAAI;AACJ,UAAkB,sBAAM,eAAe,MAAM,GAAG;AAC9C,kBAAuB,sBAAM,aAAa,QAAQ,KAAK;AAAA,MAC/D,WAAiB,WAAW,MAAM,GAAG;AAC7B,kBAAU,OAAO,KAAK;AAAA,MAC9B,OAAa;AACL,YAAI,MAAM,MAAM,KACd,WAAW,yBAAyB,OAAO,UAAU;AACvD,YAAI,YAAY,KAAK,qBAAqB,OAAO,WAAW,YAAY,OAAO,YAAY,EAAE;AAC7F,kBAAuB,sBAAM,cAAc,KAAK,SAAS;AAAA,UACvD;AAAA,QACD,GAAE,UAAU;AAAA,UACX;AAAA,QACV,CAAS,CAAC;AAAA,MACV;AACM,aAAO;AAAA,IACb;AAAA,EACA,CAAG,CAAC;AACJ,EAAEE,0BAAa;AACf,gBAAgB,MAAM,eAAe,MAAM;AAC3C,gBAAgB,MAAM,gBAAgB;AAAA,EACpC,SAAS;AAAA,EACT,SAAS;AAAA,EACT,cAAc;AAAA,EACd,WAAW;AAAA,EACX,KAAK;AAAA,EACL,YAAY;AAAA,EACZ,QAAQ;AAAA,EACR,aAAa;AAAA,EACb,MAAM;AAAA,EACN,QAAQ,CAAE;AAAA,EACV,mBAAmB,CAAC,OAAO;AAAA,EAC3B,kBAAkB;AAAA,EAClB,gBAAgB;AAAA,EAChB,mBAAmB;AAAA,EACnB,iBAAiB;AAAA,EACjB,MAAM;AAAA,EACN,OAAO;AACT,CAAC;AASD,gBAAgB,MAAM,mBAAmB,SAAU,OAAO;AACxD,MAAI,QAAQ,MAAM,OAChB,QAAQ,MAAM,OACd,QAAQ,MAAM,OACd,aAAa,MAAM,YACnB,aAAa,MAAM,YACnB,UAAU,MAAM,SAChB,WAAW,MAAM,UACjB,gBAAgB,MAAM,eACtB,SAAS,MAAM;AACjB,MAAI,SAAS,MAAM;AACnB,MAAI,SAAS,cAAc,IAAI,SAAU,OAAO,OAAO;AACrD,QAAI,QAAQ,kBAAkB,OAAO,OAAO;AAC5C,QAAI,WAAW,cAAc;AAC3B,aAAO;AAAA,QACL,GAAG,wBAAwB;AAAA,UACzB,MAAM;AAAA,UACN,OAAO;AAAA,UACP;AAAA,UACA;AAAA,UACA;AAAA,QACV,CAAS;AAAA,QACD,GAAG,MAAM,KAAK,IAAI,OAAO,MAAM,MAAM,KAAK;AAAA,QAC1C;AAAA,QACA,SAAS;AAAA,MACV;AAAA,IACP;AACI,WAAO;AAAA,MACL,GAAG,MAAM,KAAK,IAAI,OAAO,MAAM,MAAM,KAAK;AAAA,MAC1C,GAAG,wBAAwB;AAAA,QACzB,MAAM;AAAA,QACN,OAAO;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,MACR,CAAO;AAAA,MACD;AAAA,MACA,SAAS;AAAA,IACV;AAAA,EACL,CAAG;AACD,SAAO,cAAc;AAAA,IACnB;AAAA,IACA;AAAA,EACD,GAAE,MAAM;AACX,CAAC;ACrfM,IAAI,YAAY,yBAAyB;AAAA,EAC9C,WAAW;AAAA,EACX,gBAAgB;AAAA,EAChB,gBAAgB,CAAC;AAAA,IACf,UAAU;AAAA,IACV,UAAU;AAAA,EACd,GAAK;AAAA,IACD,UAAU;AAAA,IACV,UAAU;AAAA,EACd,CAAG;AAAA,EACD;AACF,CAAC;ACMD,MAAMC,eAAa;AAAA,EACb,EAAE,OAAO,WAAW,OAAO,UAAU;AAAA,EACrC,EAAE,OAAO,YAAY,OAAO,UAAU;AAAA,EACtC,EAAE,OAAO,aAAa,OAAO,UAAU;AAC7C;AACA,MAAMC,eAAa,CAAC;AAAA,EACd,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ;AAAA,EACA,QAAQ;AAAA,EACR,YAAY,CAAA;AAClB,MAAuB;;AACX,QAAA,QAAO,mCAAS,UAAS;AAC/B,QAAM,UAAQ,eAAU,KAAK,MAAf,mBAAkB,UAAS;AACzC,gDACO,KAAE,EAAA,WAAW,aAAa,CAAC,IAAI,CAAC,KAC3B,UAAA;AAAA,IAAAC,kCAAA;AAAA,MAAC;AAAA,MAAA;AAAA,QACK,GAAG;AAAA,QACH,GAAG;AAAA,QACH,IAAI;AAAA,QACJ,YAAW;AAAA,QACX,MAAK;AAAA,QACL,UAAU;AAAA,QACV,WAAU;AAAA,QAET,UAAA;AAAA,MAAA;AAAA,IACP;AAAA,IACAA,kCAAA;AAAA,MAAC;AAAA,MAAA;AAAA,QACK,GAAG;AAAA,QACH,GAAG;AAAA,QACH,IAAI;AAAA,QACJ,YAAW;AAAA,QACX,MAAK;AAAA,QACL,UAAU;AAAA,QACV,WAAU;AAAA,QAET,UAAA;AAAA,MAAA;AAAA,IAAA;AAAA,EACP,GACN;AAEZ;AAEA,SAAwB,eAAe,EAAE,iBAAiB,QAA6B;AACjF,QAAM,cAAa,mDAAiB,gBAAe,CAAA,GAAI,IAAI,CAAC,cAAc;AAAA,IACpE,OAAM,qCAAU,SAAQ;AAAA,IACxB,QAAO,qCAAU,cAAa;AAAA,IAC9B,UAAS,qCAAU,YAAW;AAAA,IAC9B,WAAU,qCAAU,aAAY;AAAA,IAChC,YAAW,qCAAU,cAAa;AAAA,EAAA,EACtC;AAEF,+CACO,OAAI,EAAA,WAAU,kDACT,UAACC,kCAAA,KAAA,OAAA,EAAI,WAAU,aACT,UAAA;AAAA,IAACD,kCAAA,IAAA,MAAA,EAAG,WAAU,uCAAsC,UAEpD,mBAAA;AAAA,IACAC,kCAAAA,KAAC,OAAI,EAAA,WAAU,6BACT,UAAA;AAAA,MAAAD,kCAAA,IAAC,KAAE,EAAA,WAAU,sCACN,WAAA,mDAAiB,kBAAiB,IAC3B,GAAG,gBAAgB,eAAe,QAAQ,CAAC,CAAC,MAC5C,QACd;AAAA,MACAC,kCAAAA,KAAC,QAAK,EAAA,WAAU,yBAAwB,UAAA;AAAA,QAAA;AAAA,QAAM;AAAA,QAAK;AAAA,MAAA,EAAM,CAAA;AAAA,IAAA,GAC/D;AAAA,IAEAA,kCAAAA,KAAC,OAAI,EAAA,WAAU,gDAGT,UAAA;AAAA,MAACD,kCAAAA,IAAA,OAAA,EAAI,WAAU,yBACT,UAAAA,kCAAA,IAAC,uBAAoB,OAAM,QAAO,QAAQ,KACpC,UAAAC,kCAAA;AAAA,QAAC;AAAA,QAAA;AAAA,UACK,MAAM;AAAA,UACN,QAAQ,EAAE,KAAK,IAAI,OAAO,IAAI,QAAQ,IAAI,MAAM,EAAE;AAAA,UAElD,UAAA;AAAA,YAAAD,kCAAA;AAAA,cAAC;AAAA,cAAA;AAAA,gBACK,UAAU;AAAA,gBACV,QAAO;AAAA,gBACP,iBAAgB;AAAA,cAAA;AAAA,YACtB;AAAA,YACAA,kCAAA;AAAA,cAAC;AAAA,cAAA;AAAA,gBACK,SAAQ;AAAA,gBACR,UAAU;AAAA,gBACV,YAAY;AAAA,gBACZ,UAAU,EAAE,QAAQ,UAAU;AAAA,gBAC9B,QAAQ;AAAA,gBACR,MAAOA,kCAAA,IAAAD,cAAA,EAAW,UAAsB,CAAA;AAAA,cAAA;AAAA,YAC9C;AAAA,YACAC,kCAAA;AAAA,cAAC;AAAA,cAAA;AAAA,gBACK,SAAQ;AAAA,gBACR,eAAe;AAAA,gBACf,UAAU;AAAA,gBACV,UAAU,EAAE,QAAQ,UAAU;AAAA,gBAC9B,eAAe,CAAC,UAAU,GAAG,KAAK;AAAA,gBAClC,WAAU;AAAA,cAAA;AAAA,YAChB;AAAA,YACAA,kCAAA;AAAA,cAAC;AAAA,cAAA;AAAA,gBACK,WAAW,CAAC,OAAe,SAAiB;AAAA,kBACtC;AAAA,kBACA,KAAK,OAAO,CAAC,EAAE,gBAAgB,KAAK,MAAM,CAAC;AAAA,gBACjD;AAAA,gBACA,cAAc;AAAA,kBACR,iBAAiB;AAAA,kBACjB,cAAc;AAAA,kBACd,QAAQ;AAAA,kBACR,WAAW;AAAA,gBAAA;AAAA,cACjB;AAAA,YACN;AAAA,YACCF,aAAW,IAAI,CAAC,SACXE,kCAAA;AAAA,cAAC;AAAA,cAAA;AAAA,gBAEK,SAAS,KAAK,MAAM,YAAY;AAAA,gBAChC,MAAK;AAAA,gBACL,QAAQ,KAAK;AAAA,gBACb,aAAa;AAAA,gBACb,KAAK,EAAE,GAAG,EAAE;AAAA,gBACZ,WAAW,EAAE,GAAG,EAAE;AAAA,cAAA;AAAA,cANb,KAAK;AAAA,YAQrB,CAAA;AAAA,UAAA;AAAA,QAAA;AAAA,SAEb,EACN,CAAA;AAAA,MAEAA,kCAAA,IAAC,OAAI,EAAA,WAAU,oDACR,UAAAF,aAAW,IAAI,CAAC,SACXG,kCAAA,KAAC,OAAqB,EAAA,WAAU,2BAC1B,UAAA;AAAA,QAAAD,kCAAA;AAAA,UAAC;AAAA,UAAA;AAAA,YACK,WAAU;AAAA,YACV,OAAO,EAAE,iBAAiB,KAAK,MAAM;AAAA,UAAA;AAAA,QAC3C;AAAA,QACCA,kCAAA,IAAA,QAAA,EAAK,WAAU,qCACT,eAAK,MACZ,CAAA;AAAA,MAAA,KAPI,KAAK,KAQf,CACL,EACP,CAAA;AAAA,IAAA,EACN,CAAA;AAAA,EAAA,EAAA,CACN,EACN,CAAA;AAEZ;AC/IA,MAAM,aAAa;AAAA,EACb,EAAE,OAAO,WAAW,OAAO,UAAU;AAAA,EACrC,EAAE,OAAO,OAAO,OAAO,UAAU;AAAA,EACjC,EAAE,OAAO,UAAU,OAAO,UAAU;AAC1C;AAGA,MAAMD,eAAa,CAAC;AAAA,EACd,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ;AAAA,EACA,QAAQ;AAAA,EACR,YAAY,CAAA;AAClB,MAAuB;;AACX,QAAA,QAAO,mCAAS,UAAS;AAC/B,QAAM,UAAQ,eAAU,KAAK,MAAf,mBAAkB,UAAS;AACzC,gDACO,KAAE,EAAA,WAAW,aAAa,CAAC,IAAI,CAAC,KAC3B,UAAA;AAAA,IAAAC,kCAAA;AAAA,MAAC;AAAA,MAAA;AAAA,QACK,GAAG;AAAA,QACH,GAAG;AAAA,QACH,IAAI;AAAA,QACJ,YAAW;AAAA,QACX,MAAK;AAAA,QACL,UAAU;AAAA,QACV,WAAU;AAAA,QAET,UAAA;AAAA,MAAA;AAAA,IACP;AAAA,IACAA,kCAAA;AAAA,MAAC;AAAA,MAAA;AAAA,QACK,GAAG;AAAA,QACH,GAAG;AAAA,QACH,IAAI;AAAA,QACJ,YAAW;AAAA,QACX,MAAK;AAAA,QACL,UAAU;AAAA,QACV,WAAU;AAAA,QAET,UAAA;AAAA,MAAA;AAAA,IAAA;AAAA,EACP,GACN;AAEZ;AACwB,SAAA,qBAAqB,EAAE,2BAAqD;AAC9F,QAAM,cAAa,mEAAyB,sBAAqB,CAAA,GAAI,IAAI,CAAC,iBAAiB;AAAA,IACrF,OAAM,2CAAa,SAAQ;AAAA,IAC3B,QAAO,2CAAa,cAAa;AAAA,IACjC,UAAS,2CAAa,YAAW;AAAA,IACjC,MAAK,2CAAa,iBAAgB;AAAA,IAClC,SAAQ,2CAAa,WAAU;AAAA,EAAA,EACnC;AAEF,QAAM,iBAAiB,mEAAyB;AAQhD,QAAM,kBAAkB,wBAAwB;AAIhD,+CACO,OAAI,EAAA,WAAU,iDACT,UAACC,kCAAA,KAAA,OAAA,EAAI,WAAU,aACT,UAAA;AAAA,IAACD,kCAAA,IAAA,MAAA,EAAG,WAAU,uCAAsC,UAEpD,wBAAA;AAAA,IAEAC,kCAAAA,KAAC,OAAI,EAAA,WAAU,6BACT,UAAA;AAAA,MAAAD,kCAAA,IAAC,KAAE,EAAA,WAAU,sCACN,UAAA,kBAAkB,IACzB;AAAA,MACAC,kCAAAA,KAAC,QAAK,EAAA,WAAU,yBACT,UAAA;AAAA,QAAA;AAAA,QAAgB;AAAA,MAAA,EACvB,CAAA;AAAA,IAAA,GACN;AAAA,IAEAA,kCAAAA,KAAC,OAAI,EAAA,WAAU,+CACT,UAAA;AAAA,MAACD,kCAAAA,IAAA,OAAA,EAAI,WAAU,mBACT,UAAAA,kCAAA,IAAC,uBAAoB,OAAM,QAAO,QAAQ,KACpC,UAAAC,kCAAA;AAAA,QAAC;AAAA,QAAA;AAAA,UACK,MAAM;AAAA,UACN,QAAQ,EAAE,KAAK,IAAI,OAAO,IAAI,QAAQ,IAAI,MAAM,EAAE;AAAA,UAElD,UAAA;AAAA,YAAAD,kCAAA;AAAA,cAAC;AAAA,cAAA;AAAA,gBACK,UAAU;AAAA,gBACV,QAAO;AAAA,gBACP,iBAAgB;AAAA,cAAA;AAAA,YACtB;AAAA,YACAA,kCAAA;AAAA,cAAC;AAAA,cAAA;AAAA,gBACK,SAAQ;AAAA,gBACR,UAAU;AAAA,gBACV,YAAY;AAAA,gBACZ,UAAU,EAAE,QAAQ,UAAU;AAAA,gBAC9B,QAAQ;AAAA,gBACR,MAAOA,kCAAA,IAAAD,cAAA,EAAW,UAAsB,CAAA;AAAA,cAAA;AAAA,YAC9C;AAAA,YACAC,kCAAA;AAAA,cAAC;AAAA,cAAA;AAAA,gBACK,SAAQ;AAAA,gBAER,eAAe;AAAA,gBACf,UAAU;AAAA,gBACV,UAAU,EAAE,QAAQ,UAAU;AAAA,gBAC9B,eAAe,CAAC,UAAU,GAAG,KAAK;AAAA,gBAClC,WAAU;AAAA,cAAA;AAAA,YAChB;AAAA,YACAA,kCAAA;AAAA,cAAC;AAAA,cAAA;AAAA,gBACK,WAAW,CAAC,OAAe,SAAiB;AAAA,kBACtC;AAAA,kBACA,KAAK,OAAO,CAAC,EAAE,gBAAgB,KAAK,MAAM,CAAC;AAAA,gBACjD;AAAA,gBACA,cAAc;AAAA,kBACR,iBAAiB;AAAA,kBACjB,cAAc;AAAA,kBACd,QAAQ;AAAA,kBACR,WAAW;AAAA,gBAAA;AAAA,cACjB;AAAA,YACN;AAAA,YACC,WAAW,IAAI,CAAC,SACXA,kCAAA;AAAA,cAAC;AAAA,cAAA;AAAA,gBAEK,SAAS,KAAK,MAAM,YAAY;AAAA,gBAChC,MAAK;AAAA,gBACL,QAAQ,KAAK;AAAA,gBACb,aAAa;AAAA,gBACb,KAAK,EAAE,GAAG,EAAE;AAAA,gBACZ,WAAW,EAAE,GAAG,EAAE;AAAA,cAAA;AAAA,cANb,KAAK;AAAA,YAQrB,CAAA;AAAA,UAAA;AAAA,QAAA;AAAA,SAEb,EACN,CAAA;AAAA,MACAA,kCAAA,IAAC,OAAI,EAAA,WAAU,mDACR,UAAA,WAAW,IAAI,CAAC,SACXC,kCAAA,KAAC,OAAqB,EAAA,WAAU,2BAC1B,UAAA;AAAA,QAAAD,kCAAA;AAAA,UAAC;AAAA,UAAA;AAAA,YACK,WAAU;AAAA,YACV,OAAO,EAAE,iBAAiB,KAAK,MAAM;AAAA,UAAA;AAAA,QAC3C;AAAA,QACCA,kCAAA,IAAA,QAAA,EAAK,WAAU,qCACT,eAAK,MACZ,CAAA;AAAA,MAAA,KAPI,KAAK,KAQf,CACL,EACP,CAAA;AAAA,IAAA,EACN,CAAA;AAAA,EAAA,EAAA,CACN,EACN,CAAA;AAEZ;ACtJA,MAAM,aAAa,CAAC;AAAA,EAClB,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ;AAAA,EACA,QAAQ;AAAA,EACR,YAAY,CAAA;AACd,MAAuB;;AACf,QAAA,QAAO,mCAAS,UAAS;AAC/B,QAAM,UAAQ,eAAU,KAAK,MAAf,mBAAkB,UAAS;AAEzC,gDACG,KAAE,EAAA,WAAW,aAAa,CAAC,IAAI,CAAC,KAC/B,UAAA;AAAA,IAAAA,kCAAA;AAAA,MAAC;AAAA,MAAA;AAAA,QACC,GAAG;AAAA,QACH,GAAG;AAAA,QACH,IAAI;AAAA,QACJ,YAAW;AAAA,QACX,MAAK;AAAA,QACL,UAAU;AAAA,QACV,WAAU;AAAA,QAET,UAAA;AAAA,MAAA;AAAA,IACH;AAAA,IACAA,kCAAA;AAAA,MAAC;AAAA,MAAA;AAAA,QACC,GAAG;AAAA,QACH,GAAG;AAAA,QACH,IAAI;AAAA,QACJ,YAAW;AAAA,QACX,MAAK;AAAA,QACL,UAAU;AAAA,QACV,WAAU;AAAA,QAET,UAAA;AAAA,MAAA;AAAA,IAAA;AAAA,EACH,GACF;AAEJ;AAEwB,SAAA,cAAc,EAAE,SAA6B;;AACnE,QAAM,cAAa,+BAAO,gBAAe,CAAA,GAAI,IAAI,CAAC,cAAc;AAAA,IAC9D,OAAM,qCAAU,SAAQ;AAAA,IACxB,QAAO,qCAAU,cAAa;AAAA,IAC9B,QAAO,qCAAU,yBAAwB;AAAA,EAAA,EACzC;AAEF,+CACG,OAAI,EAAA,WAAU,mDACb,UAACC,kCAAA,KAAA,OAAA,EAAI,WAAU,aACb,UAAA;AAAA,IAACD,kCAAA,IAAA,MAAA,EAAG,WAAU,uCAAsC,UAEpD,kBAAA;AAAA,IAECA,sCAAA,OAAA,EAAI,WAAU,2BACb,iDAAC,OACC,EAAA,UAAA;AAAA,MAAAA,kCAAAA,IAAC,OAAE,WAAU,oCACV,0BAAe,+BAAO,iBAAgB,CAAC,GAC1C;AAAA,MACAC,kCAAAA,KAAC,KAAE,EAAA,WAAU,yBACV,UAAA;AAAA,UAAO,oCAAA,gBAAA,mBAAa,qBAAoB;AAAA,QAAE;AAAA,MAAA,EAC7C,CAAA;AAAA,IAAA,EAAA,CACF,EACF,CAAA;AAAA,IACAA,kCAAAA,KAAC,OAAI,EAAA,WAAU,cAAa,UAAA;AAAA,MAAA;AAAA,MAAiD;AAAA,MAAWD,kCAAA,IAAA,qBAAA,EAAoB,OAAM,QAAO,QAAQ,KAC/H,UAAAC,kCAAA;AAAA,QAAC;AAAA,QAAA;AAAA,UACC,MAAM;AAAA,UACN,QAAQ,EAAE,KAAK,IAAI,OAAO,IAAI,QAAQ,IAAI,MAAM,EAAE;AAAA,UAElD,UAAA;AAAA,YAAAD,kCAAA;AAAA,cAAC;AAAA,cAAA;AAAA,gBACC,UAAU;AAAA,gBACV,QAAO;AAAA,gBACP,iBAAgB;AAAA,cAAA;AAAA,YAClB;AAAA,YACAA,kCAAA;AAAA,cAAC;AAAA,cAAA;AAAA,gBACC,SAAQ;AAAA,gBACR,UAAU;AAAA,gBACV,YAAY;AAAA,gBACZ,UAAU,EAAE,QAAQ,UAAU;AAAA,gBAC9B,QAAQ;AAAA,gBACR,MAAOA,kCAAA,IAAA,YAAA,EAAW,UAAsB,CAAA;AAAA,cAAA;AAAA,YAC1C;AAAA,YACAA,kCAAA;AAAA,cAAC;AAAA,cAAA;AAAA,gBACC,SAAQ;AAAA,gBACR,eAAe;AAAA,gBACf,UAAU;AAAA,gBACV,UAAU,EAAE,QAAQ,UAAU;AAAA,gBAC9B,eAAe,CAAC,UAAU,GAAG,KAAK;AAAA,gBAClC,WAAU;AAAA,cAAA;AAAA,YACZ;AAAA,YACAA,kCAAA;AAAA,cAAC;AAAA,cAAA;AAAA,gBACC,WAAW,CAAC,UAAmB;AAAA,gBAC/B,cAAc;AAAA,kBACZ,iBAAiB;AAAA,kBACjB,cAAc;AAAA,kBACd,QAAQ;AAAA,kBACR,WAAW;AAAA,gBAAA;AAAA,cACb;AAAA,YACF;AAAA,YACAA,kCAAA;AAAA,cAAC;AAAA,cAAA;AAAA,gBACC,SAAQ;AAAA,gBACR,MAAK;AAAA,gBACL,SAAS;AAAA,gBACT,QAAQ,CAAC,GAAG,GAAG,GAAG,CAAC;AAAA,cAAA;AAAA,YAAA;AAAA,UACrB;AAAA,QAAA;AAAA,MAAA,EAEJ,CAAA;AAAA,IAAA,EACA,CAAA;AAAA,EAAA,EAAA,CACF,EACF,CAAA;AAEJ;ACmBA,SAAwBE,kBAAkB;AACxC,QAAM;AAAA,IAAEC;AAAAA,IAAOC;AAAAA,IAAyBC;AAAAA,IAAyBC;AAAAA,IAAMC;AAAAA,IAAWC;AAAAA,IAAaC;AAAAA,IAAUC;AAAAA,IAAaC;AAAAA,IAAUC;AAAAA,EAAY,IAAIC,cAA0B;AAC1K,QAAM,CAACC,kBAAkBC,mBAAmB,IAAIC,aAAAA,SAA8B,IAAI;AAClF,QAAM,CAACC,WAAWC,YAAY,IAAIF,aAAAA,SAAqE,mBAAmB;AAC1H,QAAMG,UAAUC,WAAuB;AACvC,QAAM,CAACC,cAAcC,eAAe,IAAIC,gBAAgB;AAGxD,QAAM,CAACC,YAAYC,aAAa,IAAIT,aAAAA,SAAiB,EAAE;AACvD,QAAM,CAACU,wBAAwBC,yBAAyB,IAAIX,aAAAA,SAA8B,IAAI;AAC9F,QAAM,CAACY,cAAcC,eAAe,IAAIb,aAAAA,SAAS,KAAK;AAChD,QAAA;AAAA,IAAEc;AAAAA,EAAM,IAAIC,SAAS;AAG3B,QAAM,CAACC,YAAYC,aAAa,IAAIjB,aAAAA,SAAmC,IAAI;AAC3E,QAAM,CAACkB,YAAYC,aAAa,IAAInB,aAAAA,SAAiB,EAAE;AACvD,QAAM,CAACoB,YAAYC,aAAa,IAAIrB,aAAAA,SAAgC,MAAS;AAC7E,QAAM,CAACsB,UAAUC,WAAW,IAAIvB,aAAAA,SAAS,EAAE;AAG3C,QAAM,CAACwB,WAAWC,YAAY,IAAIzB,aAAAA,SAAgC,MAAS;AAG3E,QAAM,CAAC0B,wBAAwBC,yBAAyB,IAAI3B,aAAAA,SAASL,QAAQ;AAC7E,QAAM,CAACiC,oBAAoBC,qBAAqB,IAAI7B,aAAAA,SAASN,WAAW;AACxE,QAAM,CAACoC,uBAAuBC,wBAAwB,IAAI/B,aAAAA,SAASJ,WAAW;AAC9E,QAAM,CAACoC,uBAAuBC,wBAAwB,IAAIjC,aAAAA,SAASP,QAAQ;AAC3E,QAAM,CAACyC,aAAaC,cAAc,IAAInC,aAAAA,SAAoC,CAAA,CAAE;AAC5E,QAAM,CAACoC,SAASC,UAAU,IAAIrC,aAAAA,SAAqC,CAAA,CAAE;AAGrEsC,eAAAA,UAAU,MAAM;AACR,UAAAC,QAAQC,WAAW,MAAM;AAAA,IAAA,GAE5B,GAAG;AAEN,WAAO,MAAM;AACXC,mBAAaF,KAAK;AAAA,IACpB;AAAA,EACF,GAAG,CAACrB,UAAU,CAAC;AAGT,QAAAwB,2BAA4BC,YAAmC;AAC7D,UAAAC,kBAAkB,IAAIC,gBAAgBxC,YAAY;AACjDyC,WAAAC,QAAQJ,MAAM,EAAEK,QAAQ,CAAC,CAACC,KAAKC,KAAK,MAAM;AAC/C,UAAIA,OAAO;AACON,wBAAAO,IAAIF,KAAKC,KAAK;AAAA,MAChC,OAAO;AACLN,wBAAgBQ,OAAOH,GAAG;AAAA,MAC5B;AAAA,IACF,CAAC;AACD3C,oBAAgBsC,eAAe;AAAA,EACjC;AAEM,QAAAS,kBAAmBC,YAAmB;AAC1CrC,kBAAc,IAAI;AAClBE,kBAAc,EAAE;AAChBE,kBAAc,MAAS;AACvBI,iBAAa,MAAS;AACtBF,gBAAY,EAAE;AACdrB,iBAAaoD,MAAoE;AAAA,EACnF;AAQM,QAAAC,eAAeA,CAACC,UAAwBC,YAAmB;AAC/D9C,8BAA0B6C,QAAQ;AAClC/C,kBAAcgD,OAAM;AAAA,EACtB;AAEM,QAAAC,qBAAsBC,cAAsC;AAC1D,UAAAC,aAAa,IAAIC,SAAS;AACrBD,eAAAE,OAAO,UAAUtD,UAAU;AAC3BoD,eAAAE,OAAO,QAAQC,KAAKC,UAAU;AAAA,MAAER,UAAU9C;AAAAA,MAAwBiD;AAAAA,IAAS,CAAC,CAAC;AACxFxD,YAAQ8D,OAAOL,YAAY;AAAA,MAAEM,QAAQ;AAAA,IAAO,CAAC;AAC7CrD,oBAAgB,IAAI;AAAA,EACtB;AAGM,QAAAsD,8BAA+Bb,YAAmB;AACtD,UAAMc,SAAoC;AAAA,MACxCC,KAAK;AAAA,MACLC,UAAU;AAAA,MACVC,UAAU;AAAA,MACVC,aAAa;AAAA,IACf;AACM,UAAAC,gBAAgBL,OAAOd,MAAM,KAAK;AACxCrB,6BAAyBqB,MAAM;AAGNZ,6BAAA;AAAA,MACvBjD,UAAUgF;AAAAA,MACVC,MAAM;AAAA;AAAA,MACNC,QAAQ/C;AAAAA,MACRhC,aAAakC;AAAAA,MACbnC,UAAU+B;AAAAA,IACZ,CAAC;AAAA,EACH;AAEM,QAAAkD,yBAA0BC,aAAoB;AAClDhD,0BAAuBiD,gBAAe;AACpC,YAAMC,aAAaD,eAAeD;AAClC9C,+BAA0BiD,mBAAkB;AAC1C,cAAMC,WAAWF,cAAcC,kBAAkB,QAAQ,SAAS;AAGzCtC,iCAAA;AAAA,UACvBjD,UAAUuC;AAAAA,UACV0C,MAAM;AAAA;AAAA,UACNC,QAAQE;AAAAA,UACRjF,aAAaqF;AAAAA,UACbtF,UAAU+B;AAAAA,QACZ,CAAC;AAEM,eAAAuD;AAAAA,MACT,CAAC;AACM,aAAAJ;AAAAA,IACT,CAAC;AAAA,EACH;AAEM,QAAAK,cAAeC,YAAmB;AAClC,QAAAvD,uBAAuBuD,OAAe,QAAA;AACnC,WAAArD,0BAA0B,QAAQ9C,kCAAA,IAACoG,SAAQ;AAAA,MAAAC,WAAU;AAAA,KAAe,IAAKrG,kCAAA,IAACsG,WAAU;AAAA,MAAAD,WAAU;AAAA,IAAe,CAAA;AAAA,EACtH;AAEM,QAAAE,2BAA4BC,OAA2C;AACrE,UAAAtC,QAAQsC,EAAEC,OAAOvC;AACvBvB,8BAA0BuB,KAAK;AAG/BV,eAAW,MAAM;AACUE,+BAAA;AAAA,QACvBjD,UAAUuC;AAAAA,QACV0C,MAAM;AAAA;AAAA,QACNC,QAAQ/C;AAAAA,QACRhC,aAAakC;AAAAA,QACbnC,UAAUuD,MAAMwC,UAAU,IAAIxC,QAAQ;AAAA,MACxC,CAAC;AAAA,OACA,GAAG;AAAA,EACR;AAEM,QAAAyC,yBAAyBA,CAACC,UAAkBC,QAAgB;AAChE,QAAI,cAAcC,KAAKD,GAAG,KAAKA,QAAQ,IAAI;AAC1B1D,qBAAC4D,WAAU;AAAA,QAAE,GAAGA;AAAAA,QAAM,CAACH,QAAQ,GAAGC;AAAAA,MAAI,EAAE;AAAA,IACzD;AAAA,EACF;AAEA,QAAMG,YAAY5F,WAA4C;AAExD,QAAA6F,aAAcL,cAAqB;AACjC,UAAAjC,WAAW,IAAIE,SAAS;AACrBF,aAAAG,OAAO,UAAU,kBAAkB;AAC5CH,aAASG,OAAO,YAAY8B,SAASM,SAAA,CAAU;AAC/CvC,aAASG,OAAO,cAAc5B,YAAY0D,QAAQ,CAAC;AACnDI,cAAU/B,OAAON,UAAU;AAAA,MAAEO,QAAQ;AAAA,IAAO,CAAC;AAClC7B,eAAC0D,WAAU;AAAA,MAAE,GAAGA;AAAAA,MAAM,CAACH,QAAQ,GAAG;AAAA,IAAM,EAAE;AAAA,EACvD;AAGM,QAAAO,uBAAwBC,iBAAwB;AACpD7E,gBAAY6E,WAAW;AACE1D,6BAAA;AAAA,MACvBgC,MAAM;AAAA;AAAA,MACNpD,UAAU8E,YAAYF,SAAS;AAAA,IACjC,CAAC;AAAA,EACH;AAEM,QAAAG,mBAAoBC,aAAoB;AACnB5D,6BAAA;AAAA,MACvBgC,MAAM4B,QAAQJ,SAAS;AAAA,IACzB,CAAC;AAAA,EACH;AAEA5D,eAAAA,UAAU,MAAM;;AACV,UAAAnC,aAAQoG,SAARpG,mBAAcqG,YAAW,oBAAoB;AAC3C,WAAArG,aAAQoG,SAARpG,mBAAcsG,SAAS;AACnB3F,cAAA;AAAA,UACJ4F,OAAO;AAAA,UACPC,aAAa;AAAA,QACf,CAAC;AAAA,MACQ,aAAAxG,aAAQoG,SAARpG,mBAAcsG,aAAY,OAAO;AACpC3F,cAAA;AAAA,UACJ4F,OAAO;AAAA,UACPC,cAAaxG,aAAQoG,SAARpG,mBAAcyG;AAAAA,UAC3BC,SAAS;AAAA,QACX,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACC,GAAA,CAAC1G,QAAQoG,MAAMzF,KAAK,CAAC;AAExB,+CACG,OAAI;AAAA,IAAAuE,WAAU;AAAA,IACbyB,UAAC7H,kCAAA,KAAA,OAAA;AAAA,MAAIoG,WAAU;AAAA,MAGbyB,UAAA,CAAC9H,kCAAA,IAAA,OAAA;AAAA,QAAIqG,WAAU;AAAA,QACbyB,UAAA9H,kCAAA,IAAC;UAAIqG,WAAU;AAAA,UACbyB,iDAAC,OACC;AAAA,YAAAA,UAAA,CAAC9H,kCAAA,IAAA,MAAA;AAAA,cAAGqG,WAAU;AAAA,cAA8CyB,UAAS;AAAA,YAAA,CAAA,GACpE9H,kCAAA,IAAA,KAAA;AAAA,cAAEqG,WAAU;AAAA,cAAqByB,UAAuD;AAAA,YAAA,CAAA,CAAA;AAAA,UAC3F,CAAA;AAAA,QACF,CAAA;AAAA,MACF,CAAA,GAEC7H,kCAAA,KAAA8H,MAAA;AAAA,QAAK7D,OAAOjD;AAAAA,QAAW+G,eAAe3D;AAAAA,QACrCyD,UAAA,CAAC7H,kCAAA,KAAAgI,UAAA;AAAA,UAAS5B,WAAU;AAAA,UAClByB,UAAA,CAAA9H,kCAAA,IAACkI,aAAY;AAAA,YAAAhE,OAAM;AAAA,YAAoBmC,WAAU;AAAA,YAAiByB,UAAiB;AAAA,UAAA,CAAA,yCAClFI,aAAY;AAAA,YAAAhE,OAAM;AAAA,YAAgBmC,WAAU;AAAA,YAAiByB,UAAa;AAAA,UAAA,CAAA,yCAC1EI,aAAY;AAAA,YAAAhE,OAAM;AAAA,YAAmBmC,WAAU;AAAA,YAAiByB,UAAgB;AAAA,UAAA,CAAA,CAAA;AAAA,QACnF,CAAA,GAEA9H,kCAAA,IAACmI,aAAY;AAAA,UAAAjE,OAAM;AAAA,UACjB4D,UAAA9H,kCAAA,IAAC,OAAI;AAAA,YAAAqG,WAAU;AAAA,YACbyB,UAAA7H,kCAAA,KAAC,QAAK;AAAA,cAAAoG,WAAU;AAAA,cACdyB,UAAA,CAAA9H,kCAAA,IAAC,WACC;AAAA,gBAAA8H,UAAA9H,kCAAA,IAACoI,eAAc;AAAA,kBAAAjI;AAAAA,gBAAc,CAAA;AAAA,cAC/B,CAAA,yCACC,WACC;AAAA,gBAAA2H,UAAA9H,kCAAA,IAACqI;kBAAeC,iBAAiBlI;AAAAA,kBAAyBE;AAAAA,gBAAY,CAAA;AAAA,cACxE,CAAA,GACCN,kCAAA,IAAA,WAAA;AAAA,gBACC8H,UAAC9H,kCAAA,IAAAuI,sBAAA;AAAA,kBAAqBlI;AAAAA,gBAAkD,CAAA;AAAA,cAC1E,CAAA,CAAA;AAAA,YACF,CAAA;AAAA,UACF,CAAA;AAAA,QACF,CAAA,GAEAL,kCAAA,IAACmI,aAAY;AAAA,UAAAjE,OAAM;AAAA,UA0GjB4D,UAAA9H,kCAAA,IAACwI,MAAK;AAAA,YAAAnC,WAAU;AAAA,YACdyB,UAAA7H,kCAAA,KAACwI,aAAY;AAAA,cAAApC,WAAU;AAAA,cACrByB,UAAA,CAAC9H,kCAAA,IAAA+H,MAAA;AAAA,gBAAK7D,OAAOlB;AAAAA,gBAAuBgF,eAAe7C;AAAAA,gBAA6BkB,WAAU;AAAA,gBACxFyB,UAAA7H,kCAAA,KAACgI,UAAS;AAAA,kBAAA5B,WAAU;AAAA,kBAClByB,UAAA,CAAA9H,kCAAA,IAACkI,aAAY;AAAA,oBAAAhE,OAAM;AAAA,oBAAMmC,WAAU;AAAA,oBAAiByB,UAAc;AAAA,kBAAA,CAAA,yCACjEI,aAAY;AAAA,oBAAAhE,OAAM;AAAA,oBAAWmC,WAAU;AAAA,oBAAiByB,UAAmB;AAAA,kBAAA,CAAA,yCAC3EI,aAAY;AAAA,oBAAAhE,OAAM;AAAA,oBAAWmC,WAAU;AAAA,oBAAiByB,UAAoB;AAAA,kBAAA,CAAA,yCAC5EI,aAAY;AAAA,oBAAAhE,OAAM;AAAA,oBAAcmC,WAAU;AAAA,oBAAiByB,UAAc;AAAA,kBAAA,CAAA,CAAA;AAAA,gBAC5E,CAAA;AAAA,cACF,CAAA,GAGA7H,kCAAA,KAAC,OAAI;AAAA,gBAAAoG,WAAU;AAAA,gBACbyB,UAAA,CAAA9H,kCAAA,IAAC0I,OAAA;AAAA,kBACCC,aAAY;AAAA,kBACZzE,OAAOxB;AAAAA,kBACPkG,UAAUrC;AAAAA,kBACVF,WAAU;AAAA,gBAAA,CACZ,GACCpG,kCAAA,KAAA4I,QAAA;AAAA,kBAAO3E,OAAOtB;AAAAA,kBAAoBoF,eAAepC;AAAAA,kBAChDkC,UAAA,CAAA9H,kCAAA,IAAC8I;oBAAczC,WAAU;AAAA,oBACvByB,gDAACiB,aAAY;AAAA,sBAAAJ,aAAY;AAAA,oBAAU,CAAA;AAAA,kBACrC,CAAA,0CACCK,eACC;AAAA,oBAAAlB,UAAA,CAAC9H,kCAAA,IAAAiJ,YAAA;AAAA,sBAAW/E,OAAM;AAAA,sBAAY4D,UAAI;AAAA,oBAAA,CAAA,GACjC9H,kCAAA,IAAAiJ,YAAA;AAAA,sBAAW/E,OAAM;AAAA,sBAAc4D,UAAgB;AAAA,oBAAA,CAAA,GAC/C9H,kCAAA,IAAAiJ,YAAA;AAAA,sBAAW/E,OAAM;AAAA,sBAAgB4D,UAAe;AAAA,oBAAA,CAAA,GAChD9H,kCAAA,IAAAiJ,YAAA;AAAA,sBAAW/E,OAAM;AAAA,sBAAkB4D,UAAmB;AAAA,oBAAA,CAAA,GACtD9H,kCAAA,IAAAiJ,YAAA;AAAA,sBAAW/E,OAAM;AAAA,sBAAa4D,UAAY;AAAA,oBAAA,CAAA,CAAA;AAAA,kBAC7C,CAAA,CAAA;AAAA,gBACF,CAAA,CAAA;AAAA,cACF,CAAA,0CAGCoB,OACE;AAAA,gBAAApB,UAAA,CAAAd,UAAUmC,UAAU,UAAWnJ,kCAAAA,IAAAoJ,eAAA;AAAA,kBAAcC,SAAS;AAAA,gBAAM,CAAA,GAC7DrJ,kCAAA,IAACsJ,aACC;AAAA,kBAAAxB,UAAA7H,kCAAA,KAACsJ,UACC;AAAA,oBAAAzB,UAAA,CAAC9H,kCAAA,IAAAwJ,WAAA;AAAA,sBAAUnD,WAAU;AAAA,sBAAiBoD,SAASA,MAAM7D,uBAAuB,WAAW;AAAA,sBACrFkC,UAAA7H,kCAAA,KAAC,QAAK;AAAA,wBAAAoG,WAAU;AAAA,wBACdyB,UAAA,CAAA9H,kCAAA,IAAC;0BAAK8H,UAAI;AAAA,wBAAA,CAAA,GACT9H,kCAAA,IAAA,QAAA;AAAA,0BAAM8H,UAAY5B,YAAA,WAAW;AAAA,wBAAE,CAAA,CAAA;AAAA,sBAClC,CAAA;AAAA,oBACF,CAAA,GACClG,kCAAA,IAAAwJ,WAAA;AAAA,sBAAUnD,WAAU;AAAA,sBAAiBoD,SAASA,MAAM7D,uBAAuB,aAAa;AAAA,sBACvFkC,UAAA7H,kCAAA,KAAC,QAAK;AAAA,wBAAAoG,WAAU;AAAA,wBACdyB,UAAA,CAAA9H,kCAAA,IAAC;0BAAK8H,UAAa;AAAA,wBAAA,CAAA,GAClB5B,YAAY,aAAa,CAAA;AAAA,sBAC5B,CAAA;AAAA,oBACF,CAAA,GACClG,kCAAA,IAAAwJ,WAAA;AAAA,sBAAUnD,WAAU;AAAA,sBAAiBoD,SAASA,MAAM7D,uBAAuB,eAAe;AAAA,sBACzFkC,UAAA7H,kCAAA,KAAC,QAAK;AAAA,wBAAAoG,WAAU;AAAA,wBACdyB,UAAA,CAAA9H,kCAAA,IAAC;0BAAK8H,UAAe;AAAA,wBAAA,CAAA,GACpB5B,YAAY,eAAe,CAAA;AAAA,sBAC9B,CAAA;AAAA,oBACF,CAAA,GACClG,kCAAA,IAAAwJ,WAAA;AAAA,sBAAUnD,WAAU;AAAA,sBAAiBoD,SAASA,MAAM7D,uBAAuB,iBAAiB;AAAA,sBAC3FkC,UAAA7H,kCAAA,KAAC,QAAK;AAAA,wBAAAoG,WAAU;AAAA,wBACdyB,UAAA,CAAA9H,kCAAA,IAAC;0BAAK8H,UAAmB;AAAA,wBAAA,CAAA,GACxB5B,YAAY,iBAAiB,CAAA;AAAA,sBAChC,CAAA;AAAA,oBACF,CAAA,GACClG,kCAAA,IAAAwJ,WAAA;AAAA,sBAAUnD,WAAU;AAAA,sBAAiBoD,SAASA,MAAM7D,uBAAuB,YAAY;AAAA,sBACtFkC,UAAA7H,kCAAA,KAAC,QAAK;AAAA,wBAAAoG,WAAU;AAAA,wBAAmCyB,UAAA,CAAA9H,kCAAA,IAAC;0BAAK8H,UAAmB;AAAA,wBAAA,CAAA,GAAO,KAAE5B,YAAY,YAAY,GAAE,GAAA;AAAA,sBAAC,CAAA;AAAA,oBAClH,CAAA,CAAA;AAAA,kBACF,CAAA;AAAA,gBACF,CAAA,yCAECwD,WACE;AAAA,kBAAA5B,UAAAvH,UAAUoJ,IAAKnF,qDACb+E,UACC;AAAA,oBAAAzB,UAAA,CAAA9H,kCAAA,IAAC4J,WACC;AAAA,sBAAA9B,UAAA7H,kCAAA,KAAC,OAAI;AAAA,wBAAAoG,WAAU;AAAA,wBACbyB,UAAA,CAAC9H,kCAAA,IAAA,OAAA;AAAA,0BAAIqG,WAAU;AAAA,0BACbyB,UAAC9H,kCAAA,IAAA,OAAA;AAAA,4BAAK8H,UAAStD,SAAAqF,cAAc,KAAKrF,SAASqF,YAAY;AAAA,0BAAqB,CAAA;AAAA,wBAC9E,CAAA,0CACCC,QACC;AAAA,0BAAAhC,UAAA,CAAC9H,kCAAA,IAAA+J,eAAA;AAAA,4BAAcC,SAAO;AAAA,4BACpBlC,UAAA9H,kCAAA,IAACiK;8BAAKC,MAAM;AAAA,8BAAI7D,WAAU;AAAA,4BAAoE,CAAA;AAAA,0BAChG,CAAA,GACApG,kCAAA,KAACkK,eAAc;AAAA,4BAAA9D,WAAU;AAAA,4BACvByB,UAAA,CAAA9H,kCAAA,IAACoK;8BACCtC,UAAC9H,kCAAA,IAAAqK,aAAA;AAAA,gCAAYhE,WAAU;AAAA,gCAAsCyB;8BAAc,CAAA;AAAA,4BAC7E,CAAA,GACA7H,kCAAA,KAAC,OAAI;AAAA,8BAAAoG,WAAU;AAAA,8BACbyB,UAAA,CAAC7H,kCAAA,KAAA,OAAA;AAAA,gCAAIoG,WAAU;AAAA,gCACbyB,UAAA,CAAC9H,kCAAA,IAAA,QAAA;AAAA,kCAAKqG,WAAU;AAAA,kCAAwByB,UAAW;AAAA,gCAAA,CAAA,GAClD9H,kCAAA,IAAA,QAAA;AAAA,kCAAKqG,WAAU;AAAA,kCAA2CyB,mBAAS+B;AAAAA,gCAAU,CAAA,CAAA;AAAA,8BAChF,CAAA,GACA5J,kCAAA,KAAC,OAAI;AAAA,gCAAAoG,WAAU;AAAA,gCACbyB,UAAA,CAAC9H,kCAAA,IAAA,QAAA;AAAA,kCAAKqG,WAAU;AAAA,kCAAwByB,UAAc;AAAA,gCAAA,CAAA,GACrD9H,kCAAA,IAAA,QAAA;AAAA,kCAAKqG,WAAU;AAAA,kCAA2CyB,mBAASwC;AAAAA,gCAAa,CAAA,CAAA;AAAA,8BACnF,CAAA,CAAA;AAAA,4BACF,CAAA,CAAA;AAAA,0BACF,CAAA,CAAA;AAAA,wBACF,CAAA,CAAA;AAAA,sBACF,CAAA;AAAA,oBACF,CAAA,GACAtK,kCAAA,IAAC4J,WAAW;AAAA,sBAAA9B,UAAAtD,SAAS+F;AAAAA,oBAAY,CAAA,0CAChCX,WAAU;AAAA,sBAAA9B,UAAA,CAAA,MAAGtD,SAASgG,cAAcC,eAAe,OAAO,CAAA;AAAA,oBAAE,CAAA,GAC5DzK,kCAAA,IAAA4J,WAAA;AAAA,sBACE9B,UAAStD,SAAAkG,kBACLC,MAAM,IAAIC,KAAKpG,SAASkG,eAAe,EAAEG,QAAQ,CAAC,IACjD,MACAC,KAAKC,QAAO,oBAAIH,KAAK,GAAEC,QAAQ,IAAI,IAAID,KAAKpG,SAASkG,eAAe,EAAEG,QAAQ,MAAM,MAAO,KAAK,KAAK,GAAG,IAAI,UAC9G;AAAA,oBACN,CAAA,GACA7K,kCAAA,IAAC4J,WAAU;AAAA,sBAAAvD,WAAU;AAAA,sBAClByB,UAAA1E,QAAQoB,SAASoC,QAAQ,IACxB3G,kCAAAA,KAAC,OAAI;AAAA,wBAAAoG,WAAU;AAAA,wBACbyB,UAAA,CAAA9H,kCAAA,IAAC0I,OAAA;AAAA,0BACCxE,OAAO8G,OAAO9H,YAAYsB,SAASoC,QAAQ,KAAKpC,SAASyG,cAAc,EAAE;AAAA,0BACzErC,UAAWpC,OAAM;AACT,kCAAAK,MAAML,EAAEC,OAAOvC;AACrB,gCAAI,CAAC,cAAc4C,KAAKD,GAAG,EAAG;AAC1B,gCAAAqE,SAASC,WAAWtE,GAAG;AACvB,gCAAAqE,SAAS,IAAcA,UAAA;AAC3B,gCAAIA,SAAS,KAAKP,MAAMO,MAAM,EAAYA,UAAA;AAC1CvE,mDAAuBnC,SAASoC,UAAUoE,OAAOE,MAAM,CAAC;AAAA,0BAC1D;AAAA,0BACAE,UAAU,CAAChI,QAAQoB,SAASoC,QAAQ;AAAA,0BACpCyE,MAAK;AAAA,0BACLC,KAAI;AAAA,0BACJC,KAAI;AAAA,0BACJC,MAAK;AAAA,wBAAA,CACP,GACAxL,kCAAA,IAACyL,MAAA;AAAA,0BACCvB,MAAM;AAAA,0BACNT,SAASA,MAAMxC,WAAWzC,SAASoC,QAAQ;AAAA,0BAC3CP,WAAU;AAAA,wBAAA,CACZ,GACArG,kCAAA,IAAC0L,GAAA;AAAA,0BACCC,OAAM;AAAA,0BACNzB,MAAM;AAAA,0BACN7D,WAAU;AAAA,0BACVoD,SAASA,MAAMpG,WAAW,CAAE,CAAA;AAAA,wBAAA,CAC9B,CAAA;AAAA,sBAAA,CACF,IAEApD,kCAAA,KAAC,OAAI;AAAA,wBAAAoG,WAAU;AAAA,wBACbyB,UAAA,CAAC9H,kCAAA,IAAA,QAAA;AAAA,0BAAM8H,mBAASmD,aAAa,IAAI,GAAGzG,SAASyG,UAAU,OAAO;AAAA,wBAAI,CAAA,GAClEjL,kCAAA,IAAC4L,QAAA;AAAA,0BACC1B,MAAM;AAAA,0BACNT,SAASA,MAAMpG,WAAW;AAAA,4BAAE,CAACmB,SAASoC,QAAQ,GAAG;AAAA,0BAAK,CAAC;AAAA,0BACvDP,WAAU;AAAA,wBAAA,CACZ,CAAA;AAAA,sBACF,CAAA;AAAA,oBAEJ,CAAA,CAAA;AAAA,kBA9Ea,GAAA7B,SAASqH,OA+ExB,CACD;AAAA,gBACH,CAAA,CAAA;AAAA,cACF,CAAA,GAGA7L,kCAAA,IAACwI,MAAK;AAAA,gBAAAnC,WAAU;AAAA,gBACdyB,UAAA9H,kCAAA,IAACyI,aAAY;AAAA,kBAAApC,WAAU;AAAA,kBACrByB,UAAA7H,kCAAA,KAAC,OAAI;AAAA,oBAAAoG,WAAU;AAAA,oBACbyB,UAAA,CAAA7H,kCAAA,KAAC4I,QAAO;AAAA,sBAAA3E,OAAO5B,SAAS4E,SAAY;AAAA,sBAAAc,eAAgB9D,WAAUiD,qBAAqB2E,OAAO5H,KAAK,CAAC;AAAA,sBAC9F4D,UAAA,CAAA9H,kCAAA,IAAC8I,eAAc;AAAA,wBAAAzC,WAAU;AAAA,wBACvByB,UAAA9H,kCAAAA,IAAC+I,cAAY,CAAA;AAAA,sBACf,CAAA,0CACCC,eACC;AAAA,wBAAAlB,UAAA,CAAC9H,kCAAA,IAAAiJ,YAAA;AAAA,0BAAW/E,OAAM;AAAA,0BAAK4D,UAAW;AAAA,wBAAA,CAAA,GACjC9H,kCAAA,IAAAiJ,YAAA;AAAA,0BAAW/E,OAAM;AAAA,0BAAK4D,UAAW;AAAA,wBAAA,CAAA,GACjC9H,kCAAA,IAAAiJ,YAAA;AAAA,0BAAW/E,OAAM;AAAA,0BAAM4D,UAAY;AAAA,wBAAA,CAAA,CAAA;AAAA,sBACtC,CAAA,CAAA;AAAA,oBACF,CAAA,GACC7H,kCAAA,KAAA4I,QAAA;AAAA,sBAAO3E,OAAO1D,YAAY0G,SAAS;AAAA,sBAAGc,eAAgB9D,WAAUmD,iBAAiByE,OAAO5H,KAAK,CAAC;AAAA,sBAC7F4D,UAAA,CAAA9H,kCAAA,IAAC8I,eAAc;AAAA,wBAAAzC,WAAU;AAAA,wBACvByB,UAAA9H,kCAAAA,IAAC+I,cAAY,CAAA;AAAA,sBACf,CAAA,GACA/I,kCAAA,IAACgJ;wBACElB,UAAMiE,MAAAC,KAAK;AAAA,0BAAEtF,QAAQoE,KAAKmB,MAAM1L,UAAUmG,UAAU,KAAKpE,QAAQ;AAAA,wBAAE,GAAG,CAAC4J,GAAGC,6CACxElD,YAAmB;AAAA,0BAAA/E,OAAOiI,EAAEjF,SAAY;AAAA,0BAAAY,UAAA,CAAA,SACjCqE,IAAI,CAAA;AAAA,wBADK,GAAAA,CAEjB,CACD;AAAA,sBACH,CAAA,CAAA;AAAA,oBACF,CAAA,CAAA;AAAA,kBACF,CAAA;AAAA,gBACF,CAAA;AAAA,cACF,CAAA,CAAA;AAAA,YACF,CAAA;AAAA,UACF,CAAA;AAAA,QACF,CAAA,GAEAnM,kCAAA,IAACmI,aAAY;AAAA,UAAAjE,OAAM;AAAA,UACjB4D,UAAA9H,kCAAA,IAAC,OAAI;AAAA,YAAAqG,WAAU;AAAA,YACbyB,UAAA7H,kCAAA,KAACuI,MAAK;AAAA,cAAAnC,WAAU;AAAA,cACdyB,UAAA,CAAC7H,kCAAA,KAAAmM,YAAA;AAAA,gBAAW/F,WAAU;AAAA,gBACpByB,UAAA,CAAC9H,kCAAA,IAAA,OAAA;AAAA,kBAAIqG,WAAU;AAAA,kBACbyB,UAAC9H,kCAAA,IAAA,OAAA;AAAA,oBAAIqG,WAAU;AAAA,oBAAqBgG,MAAK;AAAA,oBAAOC,QAAO;AAAA,oBAAeC,SAAQ;AAAA,oBAC5EzE,UAAC9H,kCAAA,IAAA,QAAA;AAAA,sBAAKwM,eAAc;AAAA,sBAAQC,gBAAe;AAAA,sBAAQC,aAAa;AAAA,sBAAGC,GAAE;AAAA,oBAAuM,CAAA;AAAA,kBAC9Q,CAAA;AAAA,gBACF,CAAA,GACC3M,kCAAA,IAAA4M,WAAA;AAAA,kBAAUvG,WAAU;AAAA,kBAAWyB,UAA2B;AAAA,gBAAA,CAAA,GAC1D9H,kCAAA,IAAA6M,iBAAA;AAAA,kBAAgBxG,WAAU;AAAA,kBAAUyB,UAErC;AAAA,gBAAA,CAAA,CAAA;AAAA,cACF,CAAA,GACA7H,kCAAA,KAACwI,aAAY;AAAA,gBAAApC,WAAU;AAAA,gBACrByB,UAAA,CAAC9H,kCAAA,IAAA,KAAA;AAAA,kBAAEqG,WAAU;AAAA,kBAAqByB,UAElC;AAAA,gBAAA,CAAA,GACA7H,kCAAA,KAAC,OAAI;AAAA,kBAAAoG,WAAU;AAAA,kBACbyB,UAAA,CAAC9H,kCAAA,IAAA,QAAA;AAAA,oBAAKqG,WAAU;AAAA,oBAAOyB,UAAE;AAAA,kBAAA,CAAA,GAAO,gBAAA;AAAA,gBAElC,CAAA,CAAA;AAAA,cACF,CAAA,CAAA;AAAA,YACF,CAAA;AAAA,UACF,CAAA;AAAA,QACF,CAAA,CAAA;AAAA,MACF,CAAA,GAGA9H,kCAAA,IAAC8M,sBAAqB;AAAA,QAAAtI,UAAU1D;AAAAA,QAAkBiM,SAASA,MAAMhM,oBAAoB,IAAI;AAAA,QAAGiM,UAAUzI;AAAAA,MAAc,CAAA,GAGpHvE,kCAAA,IAACiN,aAAA;AAAA,QACCzI,UAAU9C;AAAAA,QACVF;AAAAA,QACAuL,SAASA,MAAM;AACbpL,oCAA0B,IAAI;AAC9BF,wBAAc,EAAE;AAAA,QAClB;AAAA,QACAG;AAAAA,QACAsL,UAAUxI;AAAAA,MAAA,CACZ,CAAA;AAAA,IACF,CAAA;AAAA,EACF,CAAA;AAEJ;AAUO,SAASoI,qBAAqB;AAAA,EAAEtI;AAAAA,EAAUuI;AAAQ,GAA8B;AACjF,MAAA,CAACvI,SAAiB,QAAA;AAEhB,QAAA2I,mBAAoBC,iBAAwB;AAChDC,WAAOC,KAAK,OAAOF,WAAW,IAAI,OAAO;AAAA,EAC3C;AAGE,SAAApN,kCAAAA,IAAC8J,QAAO;AAAA,IAAAwD,MAAM,CAAC,CAAC9I;AAAAA,IAAU+I,cAAcR;AAAAA,IACtCjF,UAAA7H,kCAAA,KAACkK,eAAc;AAAA,MAAA9D,WAAU;AAAA,MACvByB,UAAA,CAAA9H,kCAAA,IAACoK,cACC;AAAA,QAAAtC,UAAA7H,kCAAA,KAACoK,aAAY;AAAA,UAAAhE,WAAU;AAAA,UAA6CyB,UAAA,CAAA,wBAC7CtD,SAASqH,OAAA;AAAA,QAChC,CAAA;AAAA,MACF,CAAA,GAEA5L,kCAAA,KAAC,OAAI;AAAA,QAAAoG,WAAU;AAAA,QACbyB,UAAA,CAAC7H,kCAAA,KAAA,OAAA;AAAA,UAAIoG,WAAU;AAAA,UACbyB,UAAA,CAAC7H,kCAAA,KAAA,OAAA;AAAA,YAAIoG,WAAU;AAAA,YACbyB,UAAA,CAAC9H,kCAAA,IAAAwN,MAAA;AAAA,cAAKnH,WAAU;AAAA,YAAU,CAAA,GACzBrG,kCAAA,IAAA,QAAA;AAAA,cAAKqG,WAAU;AAAA,cAAeyB,mBAAS+B;AAAAA,YAAU,CAAA,CAAA;AAAA,UACpD,CAAA,GACA5J,kCAAA,KAAC,OAAI;AAAA,YAAAoG,WAAU;AAAA,YACbyB,UAAA,CAAC9H,kCAAA,IAAAyN,OAAA;AAAA,cAAMpH,WAAU;AAAA,YAAU,CAAA,GAC3BrG,kCAAA,IAAC,QAAM;AAAA,cAAA8H,UAAAtD,SAAS8F;AAAAA,YAAa,CAAA,GAC7BtK,kCAAA,IAAC0N,QAAA;AAAA,cACCxD,MAAK;AAAA,cACLrC,SAAQ;AAAA,cACR4B,SAASA,MAAM0D,iBAAiB3I,SAAS8F,YAAY;AAAA,cACrDjE,WAAU;AAAA,cAEVyB,UAAA9H,kCAAA,IAACyN,OAAM;AAAA,gBAAApH,WAAU;AAAA,cAAU,CAAA;AAAA,YAAA,CAC7B,CAAA;AAAA,UACF,CAAA,GACApG,kCAAA,KAAC,OAAI;AAAA,YAAAoG,WAAU;AAAA,YACbyB,UAAA,CAAC9H,kCAAA,IAAA2N,QAAA;AAAA,cAAOtH,WAAU;AAAA,YAAU,CAAA,GAC5BrG,kCAAA,IAAC,QAAM;AAAA,cAAA8H,UAAAtD,SAASoJ;AAAAA,YAAQ,CAAA,CAAA;AAAA,UAC1B,CAAA,CAAA;AAAA,QACF,CAAA,GAGA5N,kCAAA,IAAC,OAAI;AAAA,UAAAqG,WAAU;AAAA,UACbyB,UAAA9H,kCAAA,IAAC0N,QAAO;AAAA,YAAA7F,SAAQ;AAAA,YAAU4B,SAASsD;AAAAA,YAASjF,UAAA;AAAA,UAE5C,CAAA;AAAA,QACF,CAAA,CAAA;AAAA,MACF,CAAA,CAAA;AAAA,IACF,CAAA;AAAA,EACF,CAAA;AAEJ;AAYO,SAASmF,YAAY;AAAA,EAAEzI;AAAAA,EAAUhD;AAAAA,EAAYuL;AAAAA,EAASnL;AAAAA,EAAcsL;AAAS,GAAqB;AACjG,QAAA,CAACvI,QAAQ,IAAI3D,sBAAS;AAAA,IAC1B6M,QAAQ;AAAA,EACV,CAAC;AAEK,QAAAC,eAAgBtH,OAAuB;AAC3CA,MAAEuH,eAAe;AACjBb,aAASvI,QAAQ;AAAA,EACnB;AAEA,MAAI,CAACH,YAAY,CAAChD,WAAmB,QAAA;AAGnC,SAAAxB,kCAAAA,IAAC8J;IAAOwD,MAAM;AAAA,IAAMC,cAAcR;AAAAA,IAChCjF,UAAA7H,kCAAA,KAACkK,eAAc;AAAA,MAAA9D,WAAU;AAAA,MACvByB,UAAA,CAAA9H,kCAAA,IAACoK,cACC;AAAA,QAAAtC,UAAA9H,kCAAA,IAACqK,aAAY;AAAA,UAAAvC,UAAA;AAAA,QAEb,CAAA;AAAA,MACF,CAAA,yCACC,OAAI;AAAA,QAAAzB,WAAU;AAAA,QACbyB,UAAC7H,kCAAA,KAAA,OAAA;AAAA,UAAIoG,WAAU;AAAA,UACbyB,UAAA,CAAA9H,kCAAA,IAAC0N;YAAO7F,SAAQ;AAAA,YAAU4B,SAASsD;AAAAA,YAAS3B,UAAUxJ;AAAAA,YAAckG,UAEpE;AAAA,UAAA,CAAA,GACA9H,kCAAA,IAAC0N;YAAOjE,SAASqE;AAAAA,YAAc1C,UAAUxJ;AAAAA,YACtCkG,UAAAlG,eAAe,kBAAkB;AAAA,UACpC,CAAA,CAAA;AAAA,QACF,CAAA;AAAA,MACF,CAAA,CAAA;AAAA,IACF,CAAA;AAAA,EACF,CAAA;AAEJ;", "x_google_ignoreList": [0, 1]}