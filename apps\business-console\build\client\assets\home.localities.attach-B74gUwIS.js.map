{"version": 3, "file": "home.localities.attach-B74gUwIS.js", "sources": ["../../../app/routes/home.localities.attach.tsx"], "sourcesContent": ["import {useEffect, useState, use<PERSON><PERSON>back, useMemo} from 'react'\r\nimport {json, LoaderFunction, ActionFunction, redirect} from '@remix-run/node'\r\nimport {useLoaderData, useNavigate, Form, useSearchParams} from '@remix-run/react'\r\nimport {ArrowLeft, ArrowUpDown} from 'lucide-react'\r\nimport {Card, CardContent, CardDescription, CardHeader, CardTitle} from \"@components/ui/card\"\r\nimport {Select, SelectContent, SelectItem, SelectTrigger, SelectValue} from \"@components/ui/select\"\r\nimport {Button} from \"@components/ui/button\"\r\nimport {Checkbox} from \"@components/ui/checkbox\"\r\nimport {getDistrictsAndStates, activateSellerArea, getGlobalAreas} from '~/services/businessConsoleService'\r\nimport {getSession} from \"@utils/session.server\"\r\nimport {GoogleMap, LoadScript, Polygon} from '@react-google-maps/api'\r\nimport {decodePolygon} from \"@utils/polyline-utils\"\r\nimport type {User} from \"~/types\"\r\nimport {Seller<PERSON><PERSON>} from \"~/types/api/businessConsoleService/Areas\"\r\nimport {withAuth, withResponse} from \"@utils/auth-utils\";\r\n\r\ninterface LoaderData {\r\n    states: string[]\r\n    districts: { [state: string]: string[] }\r\n    googleMapsApiKey: string\r\n    globalAreas: SellerArea[]\r\n}\r\n\r\nexport const loader: LoaderFunction = withAuth(async ({request, user}) => {\r\n        try {\r\n            const url = new URL(request.url)\r\n            const state = url.searchParams.get('state')\r\n            const district = url.searchParams.get('district')\r\n\r\n            const [areasResponse, globalAreasResponse] = await Promise.all([\r\n                getDistrictsAndStates(user.userId, request),\r\n                (state && district)\r\n                    ? getGlobalAreas(user.userId, state, district, request)\r\n                    : Promise.resolve({data: []})\r\n            ])\r\n\r\n            const areas = areasResponse.data\r\n            const statesSet = new Set<string>()\r\n            const districtsMap: { [state: string]: Set<string> } = {}\r\n\r\n            areas.forEach((area: SellerArea) => {\r\n                statesSet.add(area.state)\r\n                if (!districtsMap[area.state]) {\r\n                    districtsMap[area.state] = new Set<string>()\r\n                }\r\n                districtsMap[area.state].add(area.district)\r\n            })\r\n            return withResponse({\r\n                states: Array.from(statesSet).sort(),\r\n                districts: Object.fromEntries(\r\n                    Object.entries(districtsMap).map(([state, districtsSet]) => [\r\n                        state,\r\n                        Array.from(districtsSet).sort()\r\n                    ])\r\n                ),\r\n                googleMapsApiKey: process.env.GOOGLE_MAPS_API_KEY || '',\r\n                globalAreas: globalAreasResponse.data || []\r\n            })\r\n        } catch (error) {\r\n            console.error('Failed to fetch areas:', error)\r\n            return json({error: \"Failed to fetch areas\"}, {status: 500})\r\n        }\r\n    }\r\n)\r\n\r\nexport const action: ActionFunction = withAuth(async ({request}) => {\r\n        const session = await getSession(request.headers.get(\"Cookie\"))\r\n        const user = session.get(\"user\") as User\r\n        const formData = await request.formData()\r\n\r\n        if (request.method === \"POST\") {\r\n            const areaIds = formData.getAll('areaIds')\r\n\r\n            await Promise.all(\r\n                areaIds.map(id => activateSellerArea(user.userId, parseInt(id.toString()), request))\r\n            )\r\n            return redirect('/home/<USER>')\r\n        }\r\n\r\n        return null\r\n    }\r\n)\r\nconst BANGALORE_CENTER = {lat: 12.9716, lng: 77.5946}\r\n\r\nexport default function AttachLocality() {\r\n    const {states, districts, googleMapsApiKey, globalAreas = []} = useLoaderData<LoaderData>()\r\n    const [searchParams, setSearchParams] = useSearchParams()\r\n    const [selectedState, setSelectedState] = useState(searchParams.get('state') || '')\r\n    const [selectedDistrict, setSelectedDistrict] = useState(searchParams.get('district') || '')\r\n    const [selectedAreaIds, setSelectedAreaIds] = useState(new Set<number>())\r\n    const [map, setMap] = useState<google.maps.Map | null>(null)\r\n    const [mapLoaded, setMapLoaded] = useState(false)\r\n    const [sortBy, setSortBy] = useState<'name' | 'state' | 'district'>('name')\r\n    const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc')\r\n    const navigate = useNavigate()\r\n\r\n    const areasWithPolygons = useMemo(() => globalAreas.filter(area => area.polygon), [globalAreas])\r\n\r\n    const handleStateChange = (state: string) => {\r\n        setSelectedState(state)\r\n        setSelectedDistrict('')\r\n        setSelectedAreaIds(new Set())\r\n        setSearchParams({state})\r\n    }\r\n\r\n    const handleDistrictChange = (district: string) => {\r\n        setSelectedDistrict(district)\r\n        setSearchParams({state: selectedState, district})\r\n        setSelectedAreaIds(new Set())\r\n    }\r\n\r\n    const handleSubmit = (e: React.FormEvent) => {\r\n        e.preventDefault()\r\n        const formData = new FormData()\r\n        selectedAreaIds.forEach(id => formData.append('areaIds', id.toString()))\r\n        fetch('/home/<USER>/attach', {\r\n            method: 'POST',\r\n            body: formData\r\n        }).then(() => {\r\n            navigate('/home/<USER>')\r\n        })\r\n    }\r\n\r\n    const toggleAreaSelection = useCallback((areaId: number) => {\r\n        setSelectedAreaIds(prev => {\r\n            const newSet = new Set(prev)\r\n            if (newSet.has(areaId)) {\r\n                newSet.delete(areaId)\r\n            } else {\r\n                newSet.add(areaId)\r\n            }\r\n            return newSet\r\n        })\r\n    }, [])\r\n\r\n    const fitBounds = useCallback(() => {\r\n        if (map && areasWithPolygons.length > 0) {\r\n            const bounds = new google.maps.LatLngBounds()\r\n            const areasToFit = selectedAreaIds.size > 0\r\n                ? areasWithPolygons.filter(area => selectedAreaIds.has(area.id))\r\n                : areasWithPolygons\r\n\r\n            areasToFit.forEach(area => {\r\n                if (area.polygon) {\r\n                    decodePolygon(area.polygon).forEach(coord => bounds.extend(coord))\r\n                }\r\n            })\r\n            map.fitBounds(bounds)\r\n        }\r\n    }, [map, areasWithPolygons, selectedAreaIds])\r\n\r\n    useEffect(() => {\r\n        if (mapLoaded) {\r\n            fitBounds()\r\n        }\r\n    }, [mapLoaded, fitBounds, selectedDistrict, selectedAreaIds])\r\n\r\n    const handleSort = (column: 'name' | 'state' | 'district') => {\r\n        if (sortBy === column) {\r\n            setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')\r\n        } else {\r\n            setSortBy(column)\r\n            setSortOrder('asc')\r\n        }\r\n    }\r\n\r\n    const sortedAreas = useMemo(() => {\r\n        return [...areasWithPolygons].sort((a, b) => {\r\n            if (sortBy === 'name') {\r\n                return sortOrder === 'asc' ? a.name.localeCompare(b.name) : b.name.localeCompare(a.name)\r\n            } else if (sortBy === 'state') {\r\n                return sortOrder === 'asc' ? a.state.localeCompare(b.state) : b.state.localeCompare(a.state)\r\n            } else {\r\n                return sortOrder === 'asc' ? a.district.localeCompare(b.district) : b.district.localeCompare(a.district)\r\n            }\r\n        })\r\n    }, [areasWithPolygons, sortBy, sortOrder])\r\n\r\n    return (\r\n        <div className=\"h-[calc(100vh-64px)] bg-white p-6 overflow-y-auto\">\r\n            <div className=\"flex justify-between items-center mb-6\">\r\n                <Button\r\n                    onClick={() => navigate('/home/<USER>')}\r\n                    variant=\"ghost\"\r\n                    size=\"icon\"\r\n                >\r\n                    <ArrowLeft size={24}/>\r\n                </Button>\r\n            </div>\r\n\r\n            <Card className=\"w-full mx-auto\">\r\n                <CardHeader>\r\n                    <CardTitle>Select Areas to Attach</CardTitle>\r\n                    <CardDescription>Choose areas from available localities</CardDescription>\r\n                </CardHeader>\r\n                <CardContent className=\"space-y-4\">\r\n                    <div className=\"flex gap-4 mb-4\">\r\n                        <Select value={selectedState} onValueChange={handleStateChange}>\r\n                            <SelectTrigger className=\"w-[180px]\">\r\n                                <SelectValue placeholder=\"Select state\"/>\r\n                            </SelectTrigger>\r\n                            <SelectContent>\r\n                                {states.map(state => (\r\n                                    <SelectItem key={state} value={state}>{state}</SelectItem>\r\n                                ))}\r\n                            </SelectContent>\r\n                        </Select>\r\n\r\n                        <Select\r\n                            value={selectedDistrict}\r\n                            onValueChange={handleDistrictChange}\r\n                            disabled={!selectedState}\r\n                        >\r\n                            <SelectTrigger className=\"w-[180px]\">\r\n                                <SelectValue placeholder=\"Select district\"/>\r\n                            </SelectTrigger>\r\n                            <SelectContent>\r\n                                {selectedState && districts[selectedState]?.map(district => (\r\n                                    <SelectItem key={district} value={district}>{district}</SelectItem>\r\n                                ))}\r\n                            </SelectContent>\r\n                        </Select>\r\n                    </div>\r\n\r\n                    <div className=\"flex gap-4 h-[calc(100vh-300px)]\">\r\n                        <div className=\"w-1/2 h-full\">\r\n                            <LoadScript googleMapsApiKey={googleMapsApiKey}>\r\n                                <GoogleMap\r\n                                    mapContainerClassName=\"w-full h-full\"\r\n                                    center={BANGALORE_CENTER}\r\n                                    zoom={11}\r\n                                    onLoad={(map) => {\r\n                                        setMap(map)\r\n                                        setMapLoaded(true)\r\n                                    }}\r\n                                    options={{\r\n                                        streetViewControl: false,\r\n                                        mapTypeControl: false,\r\n                                        fullscreenControl: false,\r\n                                        zoomControl: true,\r\n                                        clickableIcons: false\r\n                                    }}\r\n                                >\r\n                                    {mapLoaded && areasWithPolygons.map((area) => (\r\n                                        <Polygon\r\n                                            key={area.id}\r\n                                            paths={decodePolygon(area.polygon)}\r\n                                            options={{\r\n                                                fillColor: selectedAreaIds.has(area.id) ? '#4F46E5' : '#9CA3AF',\r\n                                                fillOpacity: selectedAreaIds.has(area.id) ? 0.4 : 0.2,\r\n                                                strokeWeight: selectedAreaIds.has(area.id) ? 2 : 1,\r\n                                                strokeColor: selectedAreaIds.has(area.id) ? '#4F46E5' : '#9CA3AF',\r\n                                                clickable: true\r\n                                            }}\r\n                                            onClick={() => toggleAreaSelection(area.id)}\r\n                                        />\r\n                                    ))}\r\n                                </GoogleMap>\r\n                            </LoadScript>\r\n                        </div>\r\n                        <div className=\"w-1/2 h-full overflow-auto\">\r\n                            <table className=\"min-w-full divide-y divide-gray-200\">\r\n                                <thead className=\"bg-gray-50 sticky top-0\">\r\n                                <tr>\r\n                                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n                                        Select\r\n                                    </th>\r\n                                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n                                        <Button variant=\"ghost\" onClick={() => handleSort('name')}\r\n                                                className=\"flex items-center\">\r\n                                            Name\r\n                                            <ArrowUpDown size={14} className=\"ml-1\"/>\r\n                                        </Button>\r\n                                    </th>\r\n                                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n                                        <Button variant=\"ghost\" onClick={() => handleSort('state')}\r\n                                                className=\"flex items-center\">\r\n                                            State\r\n                                            <ArrowUpDown size={14} className=\"ml-1\"/>\r\n                                        </Button>\r\n                                    </th>\r\n                                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n                                        <Button variant=\"ghost\" onClick={() => handleSort('district')}\r\n                                                className=\"flex items-center\">\r\n                                            District\r\n                                            <ArrowUpDown size={14} className=\"ml-1\"/>\r\n                                        </Button>\r\n                                    </th>\r\n                                </tr>\r\n                                </thead>\r\n                                <tbody className=\"bg-white divide-y divide-gray-200\">\r\n                                {sortedAreas.map((area) => (\r\n                                    <tr key={area.id} className=\"hover:bg-gray-50\">\r\n                                        <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                                            <Checkbox\r\n                                                checked={selectedAreaIds.has(area.id)}\r\n                                                onCheckedChange={() => toggleAreaSelection(area.id)}\r\n                                                id={`area-${area.id}`}\r\n                                            />\r\n                                        </td>\r\n                                        <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                                            <label htmlFor={`area-${area.id}`} className=\"text-sm text-gray-900\">\r\n                                                {area.name}\r\n                                            </label>\r\n                                        </td>\r\n                                        <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\r\n                                            {area.state}\r\n                                        </td>\r\n                                        <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\r\n                                            {area.district}\r\n                                        </td>\r\n                                    </tr>\r\n                                ))}\r\n                                </tbody>\r\n                            </table>\r\n                        </div>\r\n                    </div>\r\n\r\n                    <Form onSubmit={handleSubmit}>\r\n                        <Button\r\n                            type=\"submit\"\r\n                            disabled={selectedAreaIds.size === 0}\r\n                            className=\"w-full\"\r\n                        >\r\n                            Attach Selected Areas\r\n                        </Button>\r\n                    </Form>\r\n                </CardContent>\r\n            </Card>\r\n        </div>\r\n    )\r\n}\r\n"], "names": ["BANGALORE_CENTER", "lat", "lng", "AttachLocality", "states", "districts", "googleMapsApiKey", "globalAreas", "useLoaderData", "searchParams", "setSearchParams", "useSearchParams", "selectedState", "setSelectedState", "useState", "get", "selectedDistrict", "setSelectedDistrict", "selectedAreaIds", "setSelectedAreaIds", "Set", "map", "setMap", "mapLoaded", "setMapLoaded", "sortBy", "setSortBy", "sortOrder", "setSortOrder", "navigate", "useNavigate", "areasWithPolygons", "useMemo", "filter", "area", "polygon", "handleStateChange", "state", "handleDistrictChange", "district", "handleSubmit", "e", "preventDefault", "formData", "FormData", "for<PERSON>ach", "append", "id", "toString", "fetch", "method", "body", "then", "toggleAreaSelection", "useCallback", "areaId", "prev", "newSet", "has", "delete", "add", "fitBounds", "length", "bounds", "google", "maps", "LatLngBounds", "areasToFit", "size", "decodePolygon", "extend", "coord", "useEffect", "handleSort", "column", "sorted<PERSON>reas", "sort", "a", "b", "name", "localeCompare", "jsxs", "className", "children", "jsx", "<PERSON><PERSON>", "onClick", "variant", "ArrowLeft", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardTitle", "CardDescription", "<PERSON><PERSON><PERSON><PERSON>", "Select", "value", "onValueChange", "SelectTrigger", "SelectValue", "placeholder", "SelectContent", "SelectItem", "disabled", "LoadScript", "GoogleMap", "mapContainerClassName", "center", "zoom", "onLoad", "options", "streetViewControl", "mapTypeControl", "fullscreenControl", "zoomControl", "clickableIcons", "Polygon", "paths", "fillColor", "fillOpacity", "strokeWeight", "strokeColor", "clickable", "ArrowUpDown", "Checkbox", "checked", "onCheckedChange", "htmlFor", "Form", "onSubmit", "type"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkFA,MAAMA,mBAAmB;AAAA,EAACC,KAAK;AAAA,EAASC,KAAK;AAAO;AAEpD,SAAwBC,iBAAiB;;AAC/B,QAAA;AAAA,IAACC;AAAAA,IAAQC;AAAAA,IAAWC;AAAAA,IAAkBC,cAAc,CAAA;AAAA,MAAMC,cAA0B;AAC1F,QAAM,CAACC,cAAcC,eAAe,IAAIC,gBAAgB;AAClD,QAAA,CAACC,eAAeC,gBAAgB,IAAIC,aAAAA,SAASL,aAAaM,IAAI,OAAO,KAAK,EAAE;AAC5E,QAAA,CAACC,kBAAkBC,mBAAmB,IAAIH,aAAAA,SAASL,aAAaM,IAAI,UAAU,KAAK,EAAE;AAC3F,QAAM,CAACG,iBAAiBC,kBAAkB,IAAIL,aAAAA,SAAS,oBAAIM,KAAa;AACxE,QAAM,CAACC,KAAKC,MAAM,IAAIR,aAAAA,SAAiC,IAAI;AAC3D,QAAM,CAACS,WAAWC,YAAY,IAAIV,aAAAA,SAAS,KAAK;AAChD,QAAM,CAACW,QAAQC,SAAS,IAAIZ,aAAAA,SAAwC,MAAM;AAC1E,QAAM,CAACa,WAAWC,YAAY,IAAId,aAAAA,SAAyB,KAAK;AAChE,QAAMe,WAAWC,YAAY;AAEvB,QAAAC,oBAAoBC,aAAAA,QAAQ,MAAMzB,YAAY0B,OAAeC,UAAAA,KAAKC,OAAO,GAAG,CAAC5B,WAAW,CAAC;AAEzF,QAAA6B,oBAAqBC,WAAkB;AACzCxB,qBAAiBwB,KAAK;AACtBpB,wBAAoB,EAAE;AACHE,uBAAA,oBAAIC,KAAK;AACZV,oBAAA;AAAA,MAAC2B;AAAAA,IAAK,CAAC;AAAA,EAC3B;AAEM,QAAAC,uBAAwBC,cAAqB;AAC/CtB,wBAAoBsB,QAAQ;AAC5B7B,oBAAgB;AAAA,MAAC2B,OAAOzB;AAAAA,MAAe2B;AAAAA,IAAQ,CAAC;AAC7BpB,uBAAA,oBAAIC,KAAK;AAAA,EAChC;AAEM,QAAAoB,eAAgBC,OAAuB;AACzCA,MAAEC,eAAe;AACX,UAAAC,WAAW,IAAIC,SAAS;AACd1B,oBAAA2B,gBAAcF,SAASG,OAAO,WAAWC,GAAGC,SAAA,CAAU,CAAC;AACvEC,UAAM,2BAA2B;AAAA,MAC7BC,QAAQ;AAAA,MACRC,MAAMR;AAAAA,IACV,CAAC,EAAES,KAAK,MAAM;AACVvB,eAAS,kBAAkB;AAAA,IAC/B,CAAC;AAAA,EACL;AAEM,QAAAwB,sBAAsBC,aAAY,YAACC,YAAmB;AACxDpC,uBAA2BqC,UAAA;AACjB,YAAAC,SAAS,IAAIrC,IAAIoC,IAAI;AACvB,UAAAC,OAAOC,IAAIH,MAAM,GAAG;AACpBE,eAAOE,OAAOJ,MAAM;AAAA,MACxB,OAAO;AACHE,eAAOG,IAAIL,MAAM;AAAA,MACrB;AACO,aAAAE;AAAAA,IACX,CAAC;AAAA,EACL,GAAG,EAAE;AAEC,QAAAI,YAAYP,aAAAA,YAAY,MAAM;AAC5B,QAAAjC,OAAOU,kBAAkB+B,SAAS,GAAG;AACrC,YAAMC,SAAS,IAAIC,OAAOC,KAAKC,aAAa;AAC5C,YAAMC,aAAajD,gBAAgBkD,OAAO,IACpCrC,kBAAkBE,OAAeC,UAAAhB,gBAAgBwC,IAAIxB,KAAKa,EAAE,CAAC,IAC7DhB;AAENoC,iBAAWtB,QAAgBX,UAAA;AACvB,YAAIA,KAAKC,SAAS;AACAkC,wBAAAnC,KAAKC,OAAO,EAAEU,mBAAiBkB,OAAOO,OAAOC,KAAK,CAAC;AAAA,QACrE;AAAA,MACJ,CAAC;AACDlD,UAAIwC,UAAUE,MAAM;AAAA,IACxB;AAAA,EACD,GAAA,CAAC1C,KAAKU,mBAAmBb,eAAe,CAAC;AAE5CsD,eAAAA,UAAU,MAAM;AACZ,QAAIjD,WAAW;AACDsC,gBAAA;AAAA,IACd;AAAA,KACD,CAACtC,WAAWsC,WAAW7C,kBAAkBE,eAAe,CAAC;AAEtD,QAAAuD,aAAcC,YAA0C;AAC1D,QAAIjD,WAAWiD,QAAQ;AACN9C,mBAAAD,cAAc,QAAQ,SAAS,KAAK;AAAA,IACrD,OAAO;AACHD,gBAAUgD,MAAM;AAChB9C,mBAAa,KAAK;AAAA,IACtB;AAAA,EACJ;AAEM,QAAA+C,cAAc3C,aAAAA,QAAQ,MAAM;AAC9B,WAAO,CAAC,GAAGD,iBAAiB,EAAE6C,KAAK,CAACC,GAAGC,MAAM;AACzC,UAAIrD,WAAW,QAAQ;AACnB,eAAOE,cAAc,QAAQkD,EAAEE,KAAKC,cAAcF,EAAEC,IAAI,IAAID,EAAEC,KAAKC,cAAcH,EAAEE,IAAI;AAAA,MAC3F,WAAWtD,WAAW,SAAS;AAC3B,eAAOE,cAAc,QAAQkD,EAAExC,MAAM2C,cAAcF,EAAEzC,KAAK,IAAIyC,EAAEzC,MAAM2C,cAAcH,EAAExC,KAAK;AAAA,MAC/F,OAAO;AACH,eAAOV,cAAc,QAAQkD,EAAEtC,SAASyC,cAAcF,EAAEvC,QAAQ,IAAIuC,EAAEvC,SAASyC,cAAcH,EAAEtC,QAAQ;AAAA,MAC3G;AAAA,IACJ,CAAC;AAAA,EACF,GAAA,CAACR,mBAAmBN,QAAQE,SAAS,CAAC;AAGrC,SAAAsD,kCAAAA,KAAC,OAAI;AAAA,IAAAC,WAAU;AAAA,IACXC,UAAA,CAACC,kCAAA,IAAA,OAAA;AAAA,MAAIF,WAAU;AAAA,MACXC,UAAAC,kCAAA,IAACC,QAAA;AAAA,QACGC,SAASA,MAAMzD,SAAS,kBAAkB;AAAA,QAC1C0D,SAAQ;AAAA,QACRnB,MAAK;AAAA,QAELe,UAAAC,kCAAA,IAACI,WAAU;AAAA,UAAApB,MAAM;AAAA,QAAG,CAAA;AAAA,MACxB,CAAA;AAAA,IACJ,CAAA,GAEAa,kCAAA,KAACQ,MAAK;AAAA,MAAAP,WAAU;AAAA,MACZC,UAAA,CAAAF,kCAAA,KAACS,YACG;AAAA,QAAAP,UAAA,CAAAC,kCAAA,IAACO;UAAUR,UAAsB;AAAA,QAAA,CAAA,GACjCC,kCAAA,IAACQ;UAAgBT,UAAsC;AAAA,QAAA,CAAA,CAAA;AAAA,MAC3D,CAAA,GACAF,kCAAA,KAACY,aAAY;AAAA,QAAAX,WAAU;AAAA,QACnBC,UAAA,CAACF,kCAAA,KAAA,OAAA;AAAA,UAAIC,WAAU;AAAA,UACXC,UAAA,CAAAF,kCAAA,KAACa,QAAO;AAAA,YAAAC,OAAOnF;AAAAA,YAAeoF,eAAe5D;AAAAA,YACzC+C,UAAA,CAAAC,kCAAA,IAACa;cAAcf,WAAU;AAAA,cACrBC,gDAACe,aAAY;AAAA,gBAAAC,aAAY;AAAA,cAAc,CAAA;AAAA,YAC3C,CAAA,GACCf,kCAAA,IAAAgB,eAAA;AAAA,cACIjB,UAAO/E,OAAAiB,IACJgB,WAAA+C,kCAAAA,IAACiB,YAAuB;AAAA,gBAAAN,OAAO1D;AAAAA,gBAAQ8C,UAAA9C;AAAAA,cAAA,GAAtBA,KAA4B,CAChD;AAAA,YACL,CAAA,CAAA;AAAA,UACJ,CAAA,GAEA4C,kCAAA,KAACa,QAAA;AAAA,YACGC,OAAO/E;AAAAA,YACPgF,eAAe1D;AAAAA,YACfgE,UAAU,CAAC1F;AAAAA,YAEXuE,UAAA,CAAAC,kCAAA,IAACa;cAAcf,WAAU;AAAA,cACrBC,gDAACe,aAAY;AAAA,gBAAAC,aAAY;AAAA,cAAiB,CAAA;AAAA,YAC9C,CAAA,GACCf,kCAAA,IAAAgB,eAAA;AAAA,cACIjB,UAAiBvE,mBAAAP,eAAUO,aAAa,MAAvBP,mBAA0BgB,IACxCkB,cAAA6C,kCAAA,IAACiB;gBAA0BN,OAAOxD;AAAAA,gBAAW4C,UAA5B5C;AAAAA,cAAA,GAAAA,QAAqC;AAAA,YAE9D,CAAA,CAAA;AAAA,UAAA,CACJ,CAAA;AAAA,QACJ,CAAA,GAEA0C,kCAAA,KAAC,OAAI;AAAA,UAAAC,WAAU;AAAA,UACXC,UAAA,CAAAC,kCAAA,IAAC,OAAI;AAAA,YAAAF,WAAU;AAAA,YACXC,UAAAC,kCAAA,IAACmB;cAAWjG;AAAAA,cACR6E,UAAAC,kCAAA,IAACoB,WAAA;AAAA,gBACGC,uBAAsB;AAAA,gBACtBC,QAAQ1G;AAAAA,gBACR2G,MAAM;AAAA,gBACNC,QAASvF,UAAQ;AACbC,yBAAOD,IAAG;AACVG,+BAAa,IAAI;AAAA,gBACrB;AAAA,gBACAqF,SAAS;AAAA,kBACLC,mBAAmB;AAAA,kBACnBC,gBAAgB;AAAA,kBAChBC,mBAAmB;AAAA,kBACnBC,aAAa;AAAA,kBACbC,gBAAgB;AAAA,gBACpB;AAAA,gBAEC/B,UAAa5D,aAAAQ,kBAAkBV,IAAKa,UACjCkD,kCAAA,IAAC+B,SAAA;AAAA,kBAEGC,OAAO/C,cAAcnC,KAAKC,OAAO;AAAA,kBACjC0E,SAAS;AAAA,oBACLQ,WAAWnG,gBAAgBwC,IAAIxB,KAAKa,EAAE,IAAI,YAAY;AAAA,oBACtDuE,aAAapG,gBAAgBwC,IAAIxB,KAAKa,EAAE,IAAI,MAAM;AAAA,oBAClDwE,cAAcrG,gBAAgBwC,IAAIxB,KAAKa,EAAE,IAAI,IAAI;AAAA,oBACjDyE,aAAatG,gBAAgBwC,IAAIxB,KAAKa,EAAE,IAAI,YAAY;AAAA,oBACxD0E,WAAW;AAAA,kBACf;AAAA,kBACAnC,SAASA,MAAMjC,oBAAoBnB,KAAKa,EAAE;AAAA,gBAAA,GATrCb,KAAKa,EAUd,CACH;AAAA,cACL,CAAA;AAAA,YACJ,CAAA;AAAA,UACJ,CAAA,yCACC,OAAI;AAAA,YAAAmC,WAAU;AAAA,YACXC,UAACF,kCAAA,KAAA,SAAA;AAAA,cAAMC,WAAU;AAAA,cACbC,UAAA,CAAAC,kCAAA,IAAC,SAAM;AAAA,gBAAAF,WAAU;AAAA,gBACjBC,UAAAF,kCAAA,KAAC,MACG;AAAA,kBAAAE,UAAA,CAACC,kCAAA,IAAA,MAAA;AAAA,oBAAGF,WAAU;AAAA,oBAAiFC,UAE/F;AAAA,kBAAA,CAAA,GACAC,kCAAA,IAAC,MAAG;AAAA,oBAAAF,WAAU;AAAA,oBACVC,UAAAF,kCAAA,KAACI,QAAA;AAAA,sBAAOE,SAAQ;AAAA,sBAAQD,SAASA,MAAMb,WAAW,MAAM;AAAA,sBAChDS,WAAU;AAAA,sBAAoBC,UAAA,CAAA,QAEjCC,kCAAA,IAAAsC,aAAA;AAAA,wBAAYtD,MAAM;AAAA,wBAAIc,WAAU;AAAA,sBAAM,CAAA,CAAA;AAAA,oBAC3C,CAAA;AAAA,kBACJ,CAAA,GACAE,kCAAA,IAAC,MAAG;AAAA,oBAAAF,WAAU;AAAA,oBACVC,UAAAF,kCAAA,KAACI,QAAA;AAAA,sBAAOE,SAAQ;AAAA,sBAAQD,SAASA,MAAMb,WAAW,OAAO;AAAA,sBACjDS,WAAU;AAAA,sBAAoBC,UAAA,CAAA,SAEjCC,kCAAA,IAAAsC,aAAA;AAAA,wBAAYtD,MAAM;AAAA,wBAAIc,WAAU;AAAA,sBAAM,CAAA,CAAA;AAAA,oBAC3C,CAAA;AAAA,kBACJ,CAAA,GACAE,kCAAA,IAAC,MAAG;AAAA,oBAAAF,WAAU;AAAA,oBACVC,UAAAF,kCAAA,KAACI,QAAA;AAAA,sBAAOE,SAAQ;AAAA,sBAAQD,SAASA,MAAMb,WAAW,UAAU;AAAA,sBACpDS,WAAU;AAAA,sBAAoBC,UAAA,CAAA,YAEjCC,kCAAA,IAAAsC,aAAA;AAAA,wBAAYtD,MAAM;AAAA,wBAAIc,WAAU;AAAA,sBAAM,CAAA,CAAA;AAAA,oBAC3C,CAAA;AAAA,kBACJ,CAAA,CAAA;AAAA,gBACJ,CAAA;AAAA,cACA,CAAA,GACAE,kCAAA,IAAC,SAAM;AAAA,gBAAAF,WAAU;AAAA,gBAChBC,UAAAR,YAAYtD,IAAKa,UACd+C,kCAAAA,KAAC,MAAiB;AAAA,kBAAAC,WAAU;AAAA,kBACxBC,UAAA,CAACC,kCAAA,IAAA,MAAA;AAAA,oBAAGF,WAAU;AAAA,oBACVC,UAAAC,kCAAA,IAACuC,UAAA;AAAA,sBACGC,SAAS1G,gBAAgBwC,IAAIxB,KAAKa,EAAE;AAAA,sBACpC8E,iBAAiBA,MAAMxE,oBAAoBnB,KAAKa,EAAE;AAAA,sBAClDA,IAAI,QAAQb,KAAKa,EAAE;AAAA,oBACvB,CAAA;AAAA,kBACJ,CAAA,GACCqC,kCAAA,IAAA,MAAA;AAAA,oBAAGF,WAAU;AAAA,oBACVC,gDAAC,SAAM;AAAA,sBAAA2C,SAAS,QAAQ5F,KAAKa,EAAE;AAAA,sBAAImC,WAAU;AAAA,sBACxCC,UAAAjD,KAAK6C;AAAAA,oBACV,CAAA;AAAA,kBACJ,CAAA,GACCK,kCAAA,IAAA,MAAA;AAAA,oBAAGF,WAAU;AAAA,oBACTC,eAAK9C;AAAAA,kBACV,CAAA,GACC+C,kCAAA,IAAA,MAAA;AAAA,oBAAGF,WAAU;AAAA,oBACTC,eAAK5C;AAAAA,kBACV,CAAA,CAAA;AAAA,gBAlBK,GAAAL,KAAKa,EAmBd,CACH;AAAA,cACD,CAAA,CAAA;AAAA,YACJ,CAAA;AAAA,UACJ,CAAA,CAAA;AAAA,QACJ,CAAA,GAEAqC,kCAAA,IAAC2C,MAAK;AAAA,UAAAC,UAAUxF;AAAAA,UACZ2C,UAAAC,kCAAA,IAACC,QAAA;AAAA,YACG4C,MAAK;AAAA,YACL3B,UAAUpF,gBAAgBkD,SAAS;AAAA,YACnCc,WAAU;AAAA,YACbC,UAAA;AAAA,UAED,CAAA;AAAA,QACJ,CAAA,CAAA;AAAA,MACJ,CAAA,CAAA;AAAA,IACJ,CAAA,CAAA;AAAA,EACJ,CAAA;AAER;"}