import { r as reactExports, j as jsxRuntimeExports } from "./jsx-runtime-bB2y7OuD.js";
import { R as ResponsiveTable } from "./responsiveTable-CMOWL3Wl.js";
import { B as Button } from "./button-ByAXMyvk.js";
import { D as Dialog, a as DialogContent, b as DialogTitle } from "./dialog-BqKosxNq.js";
import { u as useToast } from "./ToastProvider-rciVe3-g.js";
import { a as useFetcher, F as Form, u as useLoaderData, d as useActionData } from "./components-D7UvGag_.js";
import { S as SquareX } from "./square-x-CN_sBqMN.js";
import { a as useNavigate } from "./index-DhHTcibu.js";
import { T as Trash } from "./trash-cZnr6Uhr.js";
import { P as Pencil } from "./pencil-C1goHmP8.js";
import "./index-z_byfFrQ.js";
import "./index-ImHKLo0a.js";
import "./utils-GkgzjW3c.js";
import "./index-DdafHWkt.js";
import "./index-D7VH9Fc8.js";
import "./index-DVTNuYOr.js";
import "./index-dIGjFc0I.js";
import "./index-BXzPK7u0.js";
import "./index-CzZ6EYkN.js";
import "./index-Vp2vNLNM.js";
import "./index-CEHS9zzk.js";
import "./index-QLGF6kQx.js";
import "./x-CCG_WJDF.js";
import "./createLucideIcon-uwkRm45G.js";
const AddselectedGroupAddons = ({
  isOpen,
  items,
  onClose,
  header,
  groupData,
  sellerId,
  groupId
}) => {
  var _a, _b, _c;
  const [selectedId, setSelectedId] = reactExports.useState(null);
  const [searchTerm, setSearchTerm] = reactExports.useState("");
  const [filteredAddon, setFilteredAddon] = reactExports.useState(items);
  const [choosenAddon, setChoosenAddon] = reactExports.useState(false);
  const [choossenAddonName, setChoosenAddonName] = reactExports.useState("");
  const [formData, setFormData] = reactExports.useState({
    price: (groupData == null ? void 0 : groupData.price.toString()) || "0",
    seq: (groupData == null ? void 0 : groupData.seq.toString()) || "0",
    active: (groupData == null ? void 0 : groupData.active) ?? true
  });
  const { showToast } = useToast();
  reactExports.useEffect(() => {
    if (searchTerm.length >= 3 && searchTerm !== "") {
      setFilteredAddon(items == null ? void 0 : items.filter((addon) => addon == null ? void 0 : addon.name.toLowerCase().includes(searchTerm.toLowerCase())));
    } else {
      setFilteredAddon(items);
    }
  }, [searchTerm, items]);
  reactExports.useEffect(() => {
    if (!isOpen) {
      setSelectedId(null);
      setSearchTerm("");
      setChoosenAddon(false);
      setFormData({
        price: "0",
        seq: "0",
        active: false
      });
    }
  }, [isOpen]);
  reactExports.useEffect(() => {
    if (groupData) {
      setChoosenAddon(true);
      setSelectedId(groupData.myAddOnId);
      setChoosenAddonName(groupData.myAddOnName);
      setFormData((prev) => ({
        ...prev,
        price: groupData.price.toString(),
        seq: groupData.seq.toString(),
        active: groupData.active ?? true
      }));
    }
  }, [groupData]);
  const handleSelect = (addon) => {
    setSelectedId(addon.id);
    setChoosenAddon(true);
    setChoosenAddonName(addon.name);
  };
  const deselectAddon = () => {
    setSelectedId(null);
    setChoosenAddon(false);
  };
  const groupMapfetcher = useFetcher();
  reactExports.useEffect(() => {
    var _a2, _b2;
    if (groupMapfetcher.data) {
      if ((_a2 = groupMapfetcher.data) == null ? void 0 : _a2.sucess) {
        showToast("sucess to Map GroupData", "success");
        onClose();
        setFormData({
          price: "0",
          seq: "0",
          active: false
        });
      } else if (((_b2 = groupMapfetcher.data) == null ? void 0 : _b2.sucess) === false) {
        showToast("failed to  Map  GroupData", "success");
      }
    }
  }, [groupMapfetcher == null ? void 0 : groupMapfetcher.data]);
  if (!isOpen) return null;
  return /* @__PURE__ */ jsxRuntimeExports.jsx(Dialog, { open: isOpen, onOpenChange: onClose, children: /* @__PURE__ */ jsxRuntimeExports.jsxs(DialogContent, { className: "w-full max-w-md sm:max-w-lg bg-white rounded-2xl shadow-2xl", children: [
    /* @__PURE__ */ jsxRuntimeExports.jsx(DialogTitle, { className: "text-2xl font-bold text-gray-900 mb-4", children: header }),
    /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "space-y-6", children: [
      choosenAddon === false && selectedId === null && /* @__PURE__ */ jsxRuntimeExports.jsxs(jsxRuntimeExports.Fragment, { children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx("div", { children: /* @__PURE__ */ jsxRuntimeExports.jsx(
          "input",
          {
            placeholder: "Search by Addon Name",
            type: "search",
            className: "w-full p-3 rounded-full border-2 border-gray-200 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-colors",
            autoFocus: true,
            value: searchTerm,
            onChange: (e) => setSearchTerm(e.target.value)
          }
        ) }),
        /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "mt-4 max-h-[40vh] overflow-y-auto pr-2 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100", children: /* @__PURE__ */ jsxRuntimeExports.jsx("ul", { className: "space-y-2", children: filteredAddon.length === 0 ? /* @__PURE__ */ jsxRuntimeExports.jsx("p", { className: "p-4 text-gray-500 text-center", children: "No add-ons found" }) : filteredAddon.map((item) => /* @__PURE__ */ jsxRuntimeExports.jsxs("li", { className: "flex items-center gap-3", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx(
            "input",
            {
              type: "checkbox",
              id: `item-${item.id}`,
              name: "selectedItem",
              value: item.id,
              checked: selectedId === item.id,
              onChange: () => handleSelect(item),
              className: "h-5 w-5 text-blue-600 focus:ring-blue-500 rounded"
            }
          ),
          /* @__PURE__ */ jsxRuntimeExports.jsxs(
            "label",
            {
              htmlFor: `item-${item.id}`,
              className: `cursor-pointer p-3 w-full rounded-lg border ${selectedId === item.id ? "bg-blue-50 border-blue-200" : "border-gray-200"} text-gray-800 hover:bg-gray-50 transition-colors`,
              children: [
                item == null ? void 0 : item.name,
                " ",
                /* @__PURE__ */ jsxRuntimeExports.jsxs("span", { className: "text-gray-500", children: [
                  "(",
                  item == null ? void 0 : item.diet,
                  ")"
                ] })
              ]
            }
          )
        ] }, item.id)) }) })
      ] }),
      choosenAddon && selectedId && /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "space-y-4", children: [
        selectedId && /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center justify-between bg-blue-50 p-3 rounded-lg", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx("p", { className: "font-medium text-gray-800 truncate max-w-[80%]", children: choossenAddonName }),
          /* @__PURE__ */ jsxRuntimeExports.jsx(
            SquareX,
            {
              color: "red",
              className: "cursor-pointer hover:scale-110 transition-transform",
              onClick: () => deselectAddon()
            }
          )
        ] }),
        /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "grid grid-cols-1 sm:grid-cols-2 gap-4", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx("label", { htmlFor: "price", className: "block text-sm font-medium text-gray-700 mb-1", children: "Price" }),
            /* @__PURE__ */ jsxRuntimeExports.jsx(
              "input",
              {
                type: "number",
                id: "price",
                name: "price",
                value: formData.price,
                onChange: (e) => setFormData({ ...formData, price: e.target.value }),
                min: "0",
                step: "0.01",
                required: true,
                className: "w-full p-2 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none"
              }
            )
          ] }),
          /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx("label", { htmlFor: "seq", className: "block text-sm font-medium text-gray-700 mb-1", children: "Sequence" }),
            /* @__PURE__ */ jsxRuntimeExports.jsx(
              "input",
              {
                type: "number",
                id: "seq",
                name: "seq",
                value: formData.seq,
                onChange: (e) => setFormData({ ...formData, seq: e.target.value }),
                min: "0",
                required: true,
                className: "w-full p-2 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none"
              }
            )
          ] }),
          /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: " flex gap-2", children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx("label", { htmlFor: "active", className: "block text-sm font-medium text-gray-700 mb-1", children: "Active :" }),
            /* @__PURE__ */ jsxRuntimeExports.jsx(
              "input",
              {
                type: "checkbox",
                id: "active",
                name: "active",
                checked: formData.active,
                onChange: (e) => setFormData({ ...formData, active: e.target.checked }),
                className: "h-5 w-5 text-blue-600 rounded focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
              }
            )
          ] })
        ] })
      ] })
    ] }),
    /* @__PURE__ */ jsxRuntimeExports.jsxs(Form, { method: "POST", className: "mt-6 flex flex-col sm:flex-row gap-3 justify-end", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx("input", { type: "hidden", name: "addonId", value: selectedId == null ? void 0 : selectedId.toString() }),
      /* @__PURE__ */ jsxRuntimeExports.jsx("input", { type: "hidden", name: "addonGroupId", value: groupId == null ? void 0 : groupId.toString() }),
      /* @__PURE__ */ jsxRuntimeExports.jsx("input", { type: "hidden", name: "price", value: (_a = formData.price) == null ? void 0 : _a.toString() }),
      /* @__PURE__ */ jsxRuntimeExports.jsx("input", { type: "hidden", name: "sequence", value: (_b = formData.seq) == null ? void 0 : _b.toString() }),
      /* @__PURE__ */ jsxRuntimeExports.jsx("input", { type: "hidden", name: "active", value: (_c = formData.active) == null ? void 0 : _c.toString() }),
      /* @__PURE__ */ jsxRuntimeExports.jsx("input", { type: "hidden", name: "sellerId", value: sellerId == null ? void 0 : sellerId.toString() }),
      /* @__PURE__ */ jsxRuntimeExports.jsx("input", { type: "hidden", name: "addonName", value: choossenAddonName == null ? void 0 : choossenAddonName.toString() }),
      /* @__PURE__ */ jsxRuntimeExports.jsx("input", { type: "hidden", name: "actionType", value: "actionAddonforGroup" }),
      /* @__PURE__ */ jsxRuntimeExports.jsx(
        "button",
        {
          onClick: onClose,
          className: "w-full sm:w-auto px-4 py-2 rounded-lg bg-gray-100 text-gray-700 hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-300 text-sm font-medium",
          children: "Cancel"
        }
      ),
      /* @__PURE__ */ jsxRuntimeExports.jsx(
        "button",
        {
          type: "submit",
          disabled: selectedId === null,
          className: `w-full sm:w-auto px-4 py-2 rounded-lg text-sm font-medium text-white focus:outline-none focus:ring-2 focus:ring-blue-500 ${selectedId === null ? "bg-gray-400 cursor-not-allowed" : "bg-blue-600 hover:bg-blue-700"}`,
          children: "Confirm"
        }
      )
    ] })
  ] }) });
};
const AddonsGroupMap = () => {
  const {
    selectedGruoupData,
    groupName,
    sellerId,
    groupId
  } = useLoaderData();
  const navigate = useNavigate();
  const addonMapfetcher = useFetcher();
  const [isAddselectedGroupAddonsOpen, setIsAddselectedGroupAddonsOpen] = reactExports.useState(false);
  const [selectedGdata, setSelectedGdata] = reactExports.useState();
  const actionData = useActionData();
  const [isEditopen, setIsEditOpen] = reactExports.useState(false);
  const [selectedAddonsData, setSelectedAddonsData] = reactExports.useState();
  const selectedGroupHeader = ["Id", "myAddOnId", "myAddOnName", "price", "seq", "active", "", ""];
  reactExports.useEffect(() => {
    if (addonMapfetcher.state === "idle") {
      if (actionData == null ? void 0 : actionData.selectedAddonsData) {
        setSelectedAddonsData(actionData.selectedAddonsData);
        setIsAddselectedGroupAddonsOpen(true);
        setIsEditOpen(false);
      } else {
        setSelectedAddonsData([]);
        setIsAddselectedGroupAddonsOpen(false);
        setIsEditOpen(false);
      }
    }
  }, [actionData]);
  const handleSelectedGroupData = (row) => {
    setSelectedGdata(row);
    setIsEditOpen(true);
    setIsAddselectedGroupAddonsOpen(true);
  };
  const handleDelete = (addonsmapData) => {
    const formData = new FormData();
    formData.append("actionType", "addonsMapdelete");
    formData.append("addonmapId", addonsmapData.id.toString());
    formData.append("sellerId", sellerId.toString());
    addonMapfetcher.submit(formData, {
      method: "post"
    });
  };
  return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
    className: "h-full",
    children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("h1", {
      className: " mb-4 font-bold cursor-pointer",
      onClick: () => navigate(-1),
      children: [" ", /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
        className: "text-2xl",
        children: "MyAddonsGroup / "
      }), " ", /* @__PURE__ */ jsxRuntimeExports.jsxs("span", {
        className: "text-xl",
        children: [groupName, " "]
      }), " "]
    }), /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
      className: "flex flex-wrap gap-4 border-2 "
    }), /* @__PURE__ */ jsxRuntimeExports.jsx(ResponsiveTable, {
      headers: selectedGroupHeader,
      data: selectedGruoupData,
      renderRow: (row) => /* @__PURE__ */ jsxRuntimeExports.jsxs("tr", {
        className: "border-b",
        children: [/* @__PURE__ */ jsxRuntimeExports.jsx("td", {
          className: "py-2 px-3 text-center whitespace-normal break-words ",
          children: row.id
        }), /* @__PURE__ */ jsxRuntimeExports.jsx("td", {
          className: "py-2 px-3 text-center whitespace-normal break-words ",
          children: row == null ? void 0 : row.myAddOnId
        }), /* @__PURE__ */ jsxRuntimeExports.jsx("td", {
          className: "py-2 px-3 text-center whitespace-normal break-words",
          children: row == null ? void 0 : row.myAddOnName
        }), /* @__PURE__ */ jsxRuntimeExports.jsx("td", {
          className: "py-2 px-3 text-center whitespace-normal break-words",
          children: row == null ? void 0 : row.price
        }), /* @__PURE__ */ jsxRuntimeExports.jsx("td", {
          className: "py-2 px-3 text-center whitespace-normal break-words",
          children: row == null ? void 0 : row.seq
        }), /* @__PURE__ */ jsxRuntimeExports.jsx("td", {
          className: "py-2 px-3 text-center whitespace-normal break-words",
          children: (row == null ? void 0 : row.active) ? /* @__PURE__ */ jsxRuntimeExports.jsxs("span", {
            className: "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800",
            children: [/* @__PURE__ */ jsxRuntimeExports.jsx("svg", {
              className: "w-4 h-4 mr-1 text-green-500",
              fill: "currentColor",
              viewBox: "0 0 20 20",
              xmlns: "http://www.w3.org/2000/svg",
              children: /* @__PURE__ */ jsxRuntimeExports.jsx("path", {
                fillRule: "evenodd",
                d: "M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",
                clipRule: "evenodd"
              })
            }), "Active"]
          }) : /* @__PURE__ */ jsxRuntimeExports.jsxs("span", {
            className: "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800",
            children: [/* @__PURE__ */ jsxRuntimeExports.jsx("svg", {
              className: "w-4 h-4 mr-1 text-red-500",
              fill: "currentColor",
              viewBox: "0 0 20 20",
              xmlns: "http://www.w3.org/2000/svg",
              children: /* @__PURE__ */ jsxRuntimeExports.jsx("path", {
                fillRule: "evenodd",
                d: "M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 001.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",
                clipRule: "evenodd"
              })
            }), "Inactive"]
          })
        }), /* @__PURE__ */ jsxRuntimeExports.jsx("td", {
          className: "py-2 px-3 text-center cursor-pointer",
          children: /* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
            variant: "ghost",
            size: "sm",
            className: "text-red-500 hover:text-red-900",
            onClick: () => {
              if (confirm("Are you sure you want to delete this Addon Group Map?")) {
                handleDelete(row);
              }
            },
            style: {
              alignSelf: "flex-end"
            },
            children: /* @__PURE__ */ jsxRuntimeExports.jsx(Trash, {
              size: 20
            })
          })
        }), /* @__PURE__ */ jsxRuntimeExports.jsx("td", {
          className: "py-2 px-3 text-center cursor-pointer",
          children: /* @__PURE__ */ jsxRuntimeExports.jsx(Pencil, {
            color: "blue",
            size: 20,
            onClick: () => handleSelectedGroupData(row)
          })
        })]
      }, row.id)
    }), /* @__PURE__ */ jsxRuntimeExports.jsxs(Form, {
      method: "post",
      children: [/* @__PURE__ */ jsxRuntimeExports.jsx("input", {
        name: "sellerId",
        value: sellerId,
        hidden: true
      }), /* @__PURE__ */ jsxRuntimeExports.jsx("input", {
        name: "matchBy",
        value: "",
        hidden: true
      }), /* @__PURE__ */ jsxRuntimeExports.jsx("input", {
        name: "actionType",
        value: "getAddons",
        hidden: true
      }), /* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
        className: "fixed bottom-5 right-5 rounded-full cursor-pointer",
        type: "submit",
        children: "+ Create Addon from Group"
      })]
    }), /* @__PURE__ */ jsxRuntimeExports.jsx(AddselectedGroupAddons, {
      isOpen: isAddselectedGroupAddonsOpen,
      items: selectedAddonsData || [],
      onClose: () => setIsAddselectedGroupAddonsOpen(false),
      header: isEditopen ? `Edit Addon for${groupName == null ? void 0 : groupName.slice(0, 15)}` : `Create Addon for ${groupName == null ? void 0 : groupName.slice(0, 15)} `,
      groupData: selectedGdata,
      sellerId,
      groupId
    })]
  });
};
export {
  AddonsGroupMap as default
};
//# sourceMappingURL=home.addonsGroupMap-Cguwvgqh.js.map
