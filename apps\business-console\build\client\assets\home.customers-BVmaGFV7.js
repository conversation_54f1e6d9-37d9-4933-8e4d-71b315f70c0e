import { j as jsxRuntimeExports, r as reactExports } from "./jsx-runtime-bB2y7OuD.js";
import { I as Input } from "./input-3v87qohQ.js";
import { T as Table, a as TableHeader, b as TableRow, c as TableHead, d as TableBody, e as TableCell } from "./table-CAxdJZsY.js";
import { T as Tabs, a as TabsList, b as TabsTrigger } from "./tabs-CfSdyzWr.js";
import { S as Select, a as SelectTrigger, b as SelectValue, c as SelectContent, d as SelectItem } from "./select-BFGSXKcr.js";
import { D as Dialog, d as DialogTrigger, a as DialogContent, c as DialogHeader, b as DialogTitle } from "./dialog-BqKosxNq.js";
import { F as Form, u as useLoaderData, a as useFetcher, L as Link } from "./components-D7UvGag_.js";
import { T as TooltipProvider, a as Tooltip, b as TooltipTrigger, c as TooltipContent } from "./tooltip-CmSNYR5K.js";
import { S as SpinnerLoader } from "./SpinnerLoader-CAZRmIJX.js";
import { a as useNavigate } from "./index-DhHTcibu.js";
import { I as Info, A as ArrowUp, a as ArrowDown } from "./info-WIlFwAxF.js";
import { S as Save } from "./save-xzNIILKr.js";
import { X } from "./x-CCG_WJDF.js";
import { P as Pencil } from "./pencil-C1goHmP8.js";
import "./utils-GkgzjW3c.js";
import "./index-D7VH9Fc8.js";
import "./index-CG37gmC0.js";
import "./index-qCtcpOcW.js";
import "./index-z_byfFrQ.js";
import "./index-DVTNuYOr.js";
import "./index-CzZ6EYkN.js";
import "./index-Vp2vNLNM.js";
import "./index-dIGjFc0I.js";
import "./index-BTdCMChR.js";
import "./index-QLGF6kQx.js";
import "./index-IXOTxK3N.js";
import "./index-BXzPK7u0.js";
import "./index-CEHS9zzk.js";
import "./index-DscYByPT.js";
import "./index-CkL5tk39.js";
import "./index-CpKiYcZd.js";
import "./index-C88PRvfd.js";
import "./chevron-down-pCP5jmjX.js";
import "./createLucideIcon-uwkRm45G.js";
import "./check-_dbWxIzT.js";
import "./index-DdafHWkt.js";
const Pagination = ({ currentPage, hasMoreData, tabValue, sortBy, searchBy, sortByOrder }) => {
  const displayPage = currentPage + 1;
  const maxVisiblePages = 5;
  const lastPage = hasMoreData ? displayPage + 2 : displayPage;
  const getPageNumbers = () => {
    const pageNumbers = [];
    if (currentPage > 2) {
      pageNumbers.push(1);
      if (currentPage > 3) pageNumbers.push("...");
    }
    for (let i = Math.max(1, displayPage - 2); i <= Math.min(displayPage + 2, currentPage + maxVisiblePages, lastPage); i++) {
      pageNumbers.push(i);
    }
    if (hasMoreData) {
      if (currentPage + 3 < displayPage + maxVisiblePages) pageNumbers.push("...");
    }
    return pageNumbers;
  };
  return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex justify-center items-center gap-2 mt-4", children: [
    currentPage > 0 && /* @__PURE__ */ jsxRuntimeExports.jsxs(Form, { method: "get", className: "inline", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx("input", { type: "hidden", name: "page", value: currentPage - 1 }),
      /* @__PURE__ */ jsxRuntimeExports.jsx("input", { type: "hidden", name: "tabValue", value: tabValue }),
      /* @__PURE__ */ jsxRuntimeExports.jsx("input", { type: "hidden", name: "sortBy", value: sortBy }),
      /* @__PURE__ */ jsxRuntimeExports.jsx("input", { type: "hidden", name: "search", value: searchBy }),
      /* @__PURE__ */ jsxRuntimeExports.jsx("input", { type: "hidden", name: "search", value: sortByOrder }),
      /* @__PURE__ */ jsxRuntimeExports.jsx(
        "button",
        {
          type: "submit",
          className: "px-3 py-1  rounded-full border border-grey-200 hover:bg-primary-50",
          children: "<"
        }
      )
    ] }),
    getPageNumbers().map(
      (page, index) => page === "..." ? /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "px-3 py-1 text-gray-500", children: "..." }, index) : /* @__PURE__ */ jsxRuntimeExports.jsxs(Form, { method: "get", className: "inline", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx("input", { type: "hidden", name: "page", value: page - 1 }),
        /* @__PURE__ */ jsxRuntimeExports.jsx("input", { type: "hidden", name: "tabValue", value: tabValue }),
        /* @__PURE__ */ jsxRuntimeExports.jsx("input", { type: "hidden", name: "sortBy", value: sortBy }),
        /* @__PURE__ */ jsxRuntimeExports.jsx("input", { type: "hidden", name: "search", value: searchBy }),
        /* @__PURE__ */ jsxRuntimeExports.jsx("input", { type: "hidden", name: "search", value: sortByOrder }),
        /* @__PURE__ */ jsxRuntimeExports.jsx(
          "button",
          {
            type: "submit",
            className: `px-3 py-1 rounded-full ${page === displayPage ? "bg-primary text-white" : "border border-grey-200 hover:bg-primary-50"}`,
            children: page
          }
        )
      ] }, index)
    ),
    hasMoreData && /* @__PURE__ */ jsxRuntimeExports.jsxs(Form, { method: "get", className: "inline", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx("input", { type: "hidden", name: "page", value: currentPage + 1 }),
      /* @__PURE__ */ jsxRuntimeExports.jsx("input", { type: "hidden", name: "tabValue", value: tabValue }),
      /* @__PURE__ */ jsxRuntimeExports.jsx("input", { type: "hidden", name: "sortBy", value: sortBy }),
      /* @__PURE__ */ jsxRuntimeExports.jsx("input", { type: "hidden", name: "search", value: searchBy }),
      /* @__PURE__ */ jsxRuntimeExports.jsx("input", { type: "hidden", name: "search", value: sortByOrder }),
      /* @__PURE__ */ jsxRuntimeExports.jsx(
        "button",
        {
          type: "submit",
          className: "px-3 py-1 rounded-full border border-grey-200 hover:bg-primary-50",
          children: ">"
        }
      )
    ] })
  ] });
};
function CustomersSection() {
  const {
    data: customers,
    currentPage,
    hasNextPage,
    hasMoreData,
    sortByvalue
  } = useLoaderData();
  const [searchTerm, setSearchTerm] = reactExports.useState("");
  const [sortBy, setSortBy] = reactExports.useState("buyerName");
  const [sortOrder, setSortOrder] = reactExports.useState("asc");
  const [activeTab, setActiveTab] = reactExports.useState("all");
  const navigate = useNavigate();
  const [hiddenCustomers, setHiddenCustomers] = reactExports.useState([]);
  const [selectedCustomer, setSelectedCustomer] = reactExports.useState(null);
  const [selectedTemplate, setSelectedTemplate] = reactExports.useState("");
  const [selectedBuyerData, setSelectedBuyerData] = reactExports.useState(null);
  const handleTabChange = (newTab) => {
    const tabMap = {
      all: "all",
      oneOrder: "one_order",
      frequent: "frequent_orders",
      zero_orders: "zero_orders"
    };
    const validTabValue = tabMap[newTab] || "all";
    setActiveTab(newTab);
    console.log(newTab, "444444444");
    navigate(`?tabValue=${validTabValue}&page=0&sortBy=${sortBy}&sortByOrder=${sortOrder}`);
  };
  const handleSort = (newSort) => {
    setSortBy((prevSortBy) => {
      const isSameSort = prevSortBy === newSort;
      console.log(activeTab, "444444444");
      setSortOrder((prevSortOrder) => {
        const newOrder = isSameSort && prevSortOrder === "asc" ? "desc" : "asc";
        navigate(`?tabValue=${activeTab}&page=0&sortBy=${newSort}&sortByOrder=${newOrder}&searchBy=${searchTerm}`);
        return newOrder;
      });
      return newSort;
    });
  };
  const getSortIcon = (column) => {
    if (sortBy !== column) return null;
    return sortOrder === "asc" ? /* @__PURE__ */ jsxRuntimeExports.jsx(ArrowUp, {
      className: "w-4 h-4 ml-1"
    }) : /* @__PURE__ */ jsxRuntimeExports.jsx(ArrowDown, {
      className: "w-4 h-4 ml-1"
    });
  };
  const handleSearch = (e) => {
    const value = e.target.value;
    setSearchTerm(value);
    const searchParam = value.length >= 3 ? `${value}` : "";
    navigate(`?tabValue=${activeTab}&page=0&sortBy=${sortBy}&sortByOrder=${sortOrder}&searchBy=${searchParam}`);
  };
  const filteredCustomers = customers;
  const [fbDiscounts, setFbDiscounts] = reactExports.useState({});
  const [isFbDis, setIsFbDis] = reactExports.useState({});
  const handleChangeFbDiscount = (nBuyerId, val) => {
    if (/^\d*\.?\d*$/.test(val) || val === "") {
      setFbDiscounts((prev) => ({
        ...prev,
        [nBuyerId]: val
      }));
    }
  };
  const fbFetcher = useFetcher();
  const handleSave = (nBuyerId) => {
    const formData = new FormData();
    formData.append("_intent", "UpdateFbDiscount");
    formData.append("nBuyerId", nBuyerId.toString());
    formData.append("fbDiscount", fbDiscounts[nBuyerId]);
    fbFetcher.submit(formData, {
      method: "POST"
    });
    console.log(`Saving discount for Buyer ID ${nBuyerId}:`, fbDiscounts[nBuyerId]);
    const updatedBuyer = customers.find((customer) => customer.nBuyerId === nBuyerId);
    if (updatedBuyer) {
      setSelectedBuyerData({
        ...updatedBuyer,
        discount: Number(fbDiscounts[nBuyerId])
      });
    }
    setIsFbDis((prev) => ({
      ...prev,
      [nBuyerId]: false
    }));
  };
  const [infoOpen, setInfoOpen] = reactExports.useState(false);
  const handleInfoOpen = () => {
    setInfoOpen(true);
  };
  return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
    className: "container mx-auto p-6",
    children: [/* @__PURE__ */ jsxRuntimeExports.jsx("div", {
      className: "flex justify-between items-center mb-6",
      children: /* @__PURE__ */ jsxRuntimeExports.jsx("h1", {
        className: "text-2xl font-bold",
        children: "My Customers"
      })
    }), /* @__PURE__ */ jsxRuntimeExports.jsx(TooltipProvider, {
      children: /* @__PURE__ */ jsxRuntimeExports.jsx(Tabs, {
        value: activeTab,
        onValueChange: handleTabChange,
        className: "mb-6",
        children: /* @__PURE__ */ jsxRuntimeExports.jsxs(TabsList, {
          children: [/* @__PURE__ */ jsxRuntimeExports.jsxs(Tooltip, {
            children: [/* @__PURE__ */ jsxRuntimeExports.jsx(TooltipTrigger, {
              asChild: true,
              children: /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
                className: "relative",
                children: /* @__PURE__ */ jsxRuntimeExports.jsx(TabsTrigger, {
                  value: "all",
                  children: "All"
                })
              })
            }), /* @__PURE__ */ jsxRuntimeExports.jsx(TooltipContent, {
              children: "All customers"
            })]
          }), /* @__PURE__ */ jsxRuntimeExports.jsxs(Tooltip, {
            children: [/* @__PURE__ */ jsxRuntimeExports.jsx(TooltipTrigger, {
              asChild: true,
              children: /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
                className: "relative",
                children: /* @__PURE__ */ jsxRuntimeExports.jsx(TabsTrigger, {
                  value: "frequent",
                  children: "Frequent customers"
                })
              })
            }), /* @__PURE__ */ jsxRuntimeExports.jsx(TooltipContent, {
              children: "Customers who have placed more than 10 orders in the last 3months."
            })]
          }), /* @__PURE__ */ jsxRuntimeExports.jsxs(Tooltip, {
            children: [/* @__PURE__ */ jsxRuntimeExports.jsx(TooltipTrigger, {
              asChild: true,
              children: /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
                className: "relative",
                children: /* @__PURE__ */ jsxRuntimeExports.jsx(TabsTrigger, {
                  value: "oneOrder",
                  children: "One order customers"
                })
              })
            }), /* @__PURE__ */ jsxRuntimeExports.jsx(TooltipContent, {
              children: "Customers who have placed only one order in the last year "
            })]
          }), /* @__PURE__ */ jsxRuntimeExports.jsxs(Tooltip, {
            children: [/* @__PURE__ */ jsxRuntimeExports.jsx(TooltipTrigger, {
              asChild: true,
              children: /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
                className: "relative",
                children: /* @__PURE__ */ jsxRuntimeExports.jsx(TabsTrigger, {
                  value: "zero_orders",
                  children: " New Customers"
                })
              })
            }), /* @__PURE__ */ jsxRuntimeExports.jsx(TooltipContent, {
              children: "Customers with no orders in the past 3 months"
            })]
          })]
        })
      })
    }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
      className: "flex justify-between mb-4",
      children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Input, {
        placeholder: "Search by name or owner",
        value: searchTerm,
        onChange: searchTerm.length >= 3 ? handleSearch : (e) => setSearchTerm(e.target.value),
        className: "max-w-sm"
      }), /* @__PURE__ */ jsxRuntimeExports.jsxs(Select, {
        value: sortBy,
        onValueChange: handleSort,
        children: [/* @__PURE__ */ jsxRuntimeExports.jsx(SelectTrigger, {
          className: "w-[180px]",
          children: /* @__PURE__ */ jsxRuntimeExports.jsx(SelectValue, {
            placeholder: "Sort by"
          })
        }), /* @__PURE__ */ jsxRuntimeExports.jsxs(SelectContent, {
          children: [/* @__PURE__ */ jsxRuntimeExports.jsx(SelectItem, {
            value: "buyerName",
            children: "Name"
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(SelectItem, {
            value: "totalOrders",
            children: "Number of Orders"
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(SelectItem, {
            value: "pendingAmount",
            children: "Pending Balance"
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(SelectItem, {
            value: "lastOrderedDate",
            children: "Order Duration Days"
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(SelectItem, {
            value: "fbDiscount",
            children: "Contract Price ExpDate"
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(SelectItem, {
            value: "fbDiscount",
            children: "F.B.Discount"
          })]
        })]
      })]
    }), /* @__PURE__ */ jsxRuntimeExports.jsxs(Table, {
      children: [fbFetcher.state !== "idle" && /* @__PURE__ */ jsxRuntimeExports.jsx(SpinnerLoader, {
        loading: true
      }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableHeader, {
        children: /* @__PURE__ */ jsxRuntimeExports.jsxs(TableRow, {
          children: [/* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
            className: "cursor-pointer ",
            onClick: () => handleSort("buyerName"),
            children: /* @__PURE__ */ jsxRuntimeExports.jsxs("span", {
              className: "flex flex-row items-center gap-1",
              children: [" ", /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                children: "Name"
              }), " ", /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                children: getSortIcon("buyerName")
              })]
            })
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
            className: "cursor-pointer   ",
            onClick: () => handleSort("totalOrders"),
            children: /* @__PURE__ */ jsxRuntimeExports.jsxs("span", {
              className: "flex flex-row items-center gap-1",
              children: [" ", /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                children: "Num of Orders"
              }), " ", getSortIcon("totalOrders"), " "]
            })
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
            className: "cursor-pointer ",
            onClick: () => handleSort("pendingAmount"),
            children: /* @__PURE__ */ jsxRuntimeExports.jsxs("span", {
              className: "flex flex-row items-center gap-1",
              children: ["  ", /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                children: "Pending Balance"
              }), " ", getSortIcon("pendingAmount"), " "]
            })
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
            className: "cursor-pointer    ",
            onClick: () => handleSort("lastOrderedDate"),
            children: /* @__PURE__ */ jsxRuntimeExports.jsxs("span", {
              className: "flex flex-row items-center gap-1",
              children: ["  ", /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                children: "Order Duration Days"
              }), " ", getSortIcon("lastOrderedDate"), " "]
            })
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
            className: "cursor-pointer    ",
            onClick: () => handleSort("lastOrderedDate"),
            children: /* @__PURE__ */ jsxRuntimeExports.jsxs("span", {
              className: "flex flex-row items-center gap-1",
              children: ["  ", /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                children: "Contract Price ExpDate"
              }), " ", getSortIcon("lastOrderedDate"), " "]
            })
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
            children: /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
              className: "items-center gap-1",
              onClick: () => handleSort("fbDiscount"),
              children: /* @__PURE__ */ jsxRuntimeExports.jsxs("span", {
                className: "flex flex-row items-center gap-1",
                children: [/* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                  children: "Freq Buyer Discount"
                }), " ", getSortIcon("fbDiscount"), " "]
              })
            })
          })]
        })
      }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableBody, {
        children: filteredCustomers.map((customer) => {
          hiddenCustomers.includes(customer.buyerId);
          return /* @__PURE__ */ jsxRuntimeExports.jsxs(TableRow, {
            children: [/* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
              children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                className: "flex flex-row gap-2  items-center",
                children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Link, {
                  to: `/home/<USER>/${customer.buyerId}`,
                  className: "text-blue-600 hover:underline",
                  children: /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
                    children: customer.buyerName !== "" ? customer.buyerName : "( Name not given )"
                  })
                }), /* @__PURE__ */ jsxRuntimeExports.jsxs(Dialog, {
                  children: [/* @__PURE__ */ jsxRuntimeExports.jsx(DialogTrigger, {
                    asChild: true,
                    children: /* @__PURE__ */ jsxRuntimeExports.jsx(Info, {
                      size: 18,
                      onClick: handleInfoOpen,
                      className: "cursor-pointer text-gray-600 hover:text-purple-600 transition-all"
                    })
                  }), /* @__PURE__ */ jsxRuntimeExports.jsxs(DialogContent, {
                    className: "sm:max-w-fit p-5 rounded-lg",
                    children: [/* @__PURE__ */ jsxRuntimeExports.jsx(DialogHeader, {
                      children: /* @__PURE__ */ jsxRuntimeExports.jsx(DialogTitle, {
                        className: "text-lg font-semibold text-gray-800",
                        children: "About Customer"
                      })
                    }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                      className: "flex flex-col gap-3 mt-2",
                      children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                        className: "flex flex-row gap-2",
                        children: [/* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                          className: "text-sm text-gray-600",
                          children: "Buyer Name:"
                        }), /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                          className: "text-base text-purple-800 font-semibold",
                          children: customer.buyerName
                        })]
                      }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                        className: "flex flex-row gap-2",
                        children: [/* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                          className: "text-sm text-gray-600",
                          children: "Mobile Number:"
                        }), /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                          className: "text-base text-purple-800 font-semibold",
                          children: customer.mobileNumber
                        })]
                      })]
                    })]
                  })]
                })]
              })
            }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
              children: customer.totalOrders
            }), /* @__PURE__ */ jsxRuntimeExports.jsxs(TableCell, {
              children: ["₹ ", customer.pendingAmount.toLocaleString("en-IN")]
            }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
              children: customer.lastOrderedDate ? isNaN(new Date(customer.lastOrderedDate).getTime()) ? "-" : Math.floor(((/* @__PURE__ */ new Date()).getTime() - new Date(customer.lastOrderedDate).getTime()) / (1e3 * 60 * 60 * 24)) + " days" : "-"
            }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
              className: `${customer.contractPriceExpDate && new Date(customer.contractPriceExpDate.split("-").reverse().join("-")) < /* @__PURE__ */ new Date() ? "bg-orange-500" : ""} ${customer.contractPriceExpDate && new Date(customer.contractPriceExpDate.split("-").reverse().join("-")) < /* @__PURE__ */ new Date() ? "text-blue-500" : "text-gray-500"} ${customer.contractPriceExpDate && new Date(customer.contractPriceExpDate.split("-").reverse().join("-")) < /* @__PURE__ */ new Date() ? "rounded-full" : ""}`,
              children: customer.contractPriceExpDate || "-"
            }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
              className: "flex flex-row items-center gap-2",
              children: isFbDis[customer.nBuyerId] ? /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                className: "flex flex-row justify-center items-center gap-3",
                children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Input, {
                  value: String(fbDiscounts[customer.nBuyerId] ?? customer.fbDiscount ?? ""),
                  onChange: (e) => {
                    let val = e.target.value;
                    if (!/^\d*\.?\d*$/.test(val)) return;
                    let numVal = parseFloat(val);
                    if (numVal > 100) numVal = 100;
                    if (numVal < 0 || isNaN(numVal)) numVal = 0;
                    handleChangeFbDiscount(customer.nBuyerId, String(numVal));
                  },
                  disabled: !isFbDis[customer.nBuyerId],
                  type: "number",
                  min: "0",
                  max: "100",
                  step: "0.01"
                }), /* @__PURE__ */ jsxRuntimeExports.jsx(Save, {
                  size: 24,
                  onClick: () => handleSave(customer.nBuyerId),
                  className: "cursor-pointer text-green-500"
                }), /* @__PURE__ */ jsxRuntimeExports.jsx(X, {
                  color: "red",
                  size: 24,
                  className: "cursor-pointer text-red-500",
                  onClick: () => setIsFbDis({})
                })]
              }) : /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                className: "flex flex-row gap-3 items-center justify-center",
                children: [/* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                  children: customer.fbDiscount > 0 ? `${customer.fbDiscount} %` : "-"
                }), /* @__PURE__ */ jsxRuntimeExports.jsx(Pencil, {
                  size: 15,
                  onClick: () => setIsFbDis({
                    [customer.nBuyerId]: true
                  }),
                  className: "cursor-pointer text-blue-500"
                })]
              })
            })]
          }, customer.buyerId);
        })
      })]
    }), /* @__PURE__ */ jsxRuntimeExports.jsx(Pagination, {
      currentPage,
      hasMoreData,
      tabValue: activeTab,
      sortBy,
      searchBy: searchTerm,
      sortByOrder: sortOrder
    })]
  });
}
export {
  CustomersSection as default
};
//# sourceMappingURL=home.customers-BVmaGFV7.js.map
