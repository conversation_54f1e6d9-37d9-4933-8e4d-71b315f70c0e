{"version": 3, "file": "home.networkDetails-BmwHh8ux.js", "sources": ["../../../node_modules/lucide-react/dist/esm/icons/circle-check-big.js", "../../../app/components/ui/customNetworkAreaCard.tsx", "../../../app/components/ui/createAgent.tsx", "../../../app/components/ui/netWorkModal.tsx", "../../../app/components/common/NetworkBanneModal.tsx", "../../../app/routes/home.networkDetails.tsx"], "sourcesContent": ["/**\n * @license lucide-react v0.453.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst CircleCheckBig = createLucideIcon(\"CircleCheckBig\", [\n  [\"path\", { d: \"M21.801 10A10 10 0 1 1 17 3.335\", key: \"yps3ct\" }],\n  [\"path\", { d: \"m9 11 3 3L22 4\", key: \"1pflzl\" }]\n]);\n\nexport { CircleCheckBig as default };\n//# sourceMappingURL=circle-check-big.js.map\n", "import { <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>ci<PERSON>, Trash2, X } from \"lucide-react\";\r\nimport { NetworkAreas } from \"~/types/api/businessConsoleService/Network\";\r\nimport { Switch } from \"./switch\";\r\nimport { useCallback, useState } from \"react\";\r\nimport { useFetcher } from \"@remix-run/react\";\r\nimport { But<PERSON> } from \"./button\";\r\nimport { networkAgents } from \"~/types/api/businessConsoleService/SellerManagement\";\r\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from \"@components/ui/select\"\r\n\r\ninterface NetworkAreaCardProps {\r\n    networkAreaDetail: NetworkAreas;\r\n    agentDetails?: networkAgents[];\r\n    networkId?: number;\r\n    color?: string;\r\n    netWorks: NetworkAreas[],\r\n    isSalesPermission: boolean\r\n}\r\n\r\nconst NetworkAreaCard: React.FC<NetworkAreaCardProps> = ({\r\n    networkAreaDetail,\r\n    agentDetails,\r\n    networkId,\r\n    color,\r\n    netWorks,\r\n    isSalesPermission\r\n\r\n\r\n}) => {\r\n    const [networkStatus, setNetworkStatus] = useState<Record<number, boolean>>(() =>\r\n        (netWorks ?? []).reduce((acc, network) => {\r\n            if (network?.networkAreaId !== undefined) {\r\n                acc[network.networkAreaId] = network?.disabled ?? false;\r\n            }\r\n            return acc;\r\n        }, {} as Record<number, boolean>)\r\n    );\r\n    const [areaId, setAreaId] = useState<number>(0);\r\n    const [editNetworkId, setEditNetworkId] = useState<number>(0);\r\n    const [agentUserId, setAgentUserId] = useState<number>(0);\r\n    const handleNewLocalities2 = useCallback(\r\n        (id?: number, networkId?: number, agentUserId?: string) => {\r\n            if (id && networkId && agentUserId) {\r\n                setAreaId(id);\r\n                setEditNetworkId(networkId);\r\n                setAgentUserId(parseFloat(agentUserId));\r\n            }\r\n        }, []);\r\n\r\n    const [editLocality, setEditLocality] = useState<number>(0);\r\n    const fetcher = useFetcher()\r\n\r\n    const handleSwitchNetwork = async (networkAreaId: number) => {\r\n        setNetworkStatus((prev) => ({\r\n            ...prev,\r\n            [networkAreaId]: !prev[networkAreaId],\r\n        }));\r\n        const formData = new FormData();\r\n        formData.append(\"nAreaId\", networkAreaId.toString());\r\n        formData.append(\"actionType\", \"updateNetWorkAreaStatus\");\r\n        fetcher.submit(formData, { method: \"put\" });\r\n    }\r\n\r\n    const handleEditNetwork = async () => {\r\n\r\n        const formData = new FormData();\r\n        formData.append(\"masterAreaId\", areaId.toString());\r\n        formData.append(\"editNetworkId\", editNetworkId.toString());\r\n        formData.append(\"agentUserId\", agentUserId.toString());\r\n        formData.append(\"actionType\", \"updateNetWorkAreaEdit\");\r\n        console.log(\"handleEditNetwork formData.....\", formData);\r\n        for (const [key, value] of formData.entries()) {\r\n            console.log(key, value);\r\n        }\r\n        fetcher.submit(formData, { method: \"POST\" });\r\n        setEditLocality(0);\r\n    }\r\n\r\n    const editLocalityClicked = useCallback((areaId: number) => {\r\n        setEditLocality(areaId);\r\n    }, []);\r\n    const resetEditLocalityClicked = useCallback(() => {\r\n        setEditLocality(0);\r\n    }, []);\r\n\r\n    return (\r\n        <div className=\"flex flex-col w-full gap-3 bg-white p-2 rounded-lg shadow\">\r\n            <div className=\"flex gap-2  justify-between align-center items-center\">\r\n                <div className=\"flex flex-col gap-0\">\r\n                    <div className=\"text-sm text-typography-400 leading-tight\" >\r\n                        id:  {networkAreaDetail.networkAreaId}\r\n                    </div>\r\n                    <div className=\"text-lg text-typography-800 leading-tight\">\r\n                        {networkAreaDetail.networkAreaName}\r\n                    </div>\r\n                </div>\r\n                {color && (<Circle size={\"1rem\"} fill={`${color}`} color={`${color}`} />)}\r\n            </div>\r\n            <div className=\"text-md text-typography-600 flex gap-4 w-full items-center\">\r\n                {networkAreaDetail.agentName && (<div className=\"w-full\"> {\r\n                    editLocality === networkAreaDetail.networkAreaId ? (\r\n                        <>\r\n                            <Select\r\n                                value={agentUserId.toString()}\r\n                                onValueChange={(newDistrict) => {\r\n                                    handleNewLocalities2(networkAreaDetail.areaId, networkId, newDistrict);\r\n\r\n                                }} >\r\n                                <SelectTrigger>\r\n                                    <SelectValue placeholder=\"Please select an agent\" />\r\n                                </SelectTrigger>\r\n                                <SelectContent>\r\n                                    {agentDetails?.map((district) => (\r\n                                        <SelectItem key={district.agentUserId} value={district.agentUserId.toString()}>\r\n                                            {district.agentUserId} - {district.fullName}\r\n                                        </SelectItem>\r\n                                    ))}\r\n                                </SelectContent>\r\n                            </Select>\r\n                        </>\r\n                    ) : (<> <span className=\"text-typography-300 font-thin text-sm\">Sales Agent : </span>\r\n                        {networkAreaDetail.agentName} </>)}</div>)}\r\n                {!isSalesPermission && <div className=\"flex gap-0\">\r\n                    {editLocality === networkAreaDetail.networkAreaId ? (<>\r\n                        <Button\r\n                            variant=\"ghost\"\r\n                            size=\"sm\"\r\n                            className=\"text-primary hover:text-primary-800\"\r\n                            onClick={() => handleEditNetwork()}\r\n                            disabled={agentUserId === 0}\r\n                        >\r\n                            <CircleCheckBig size={16} />\r\n                        </Button>\r\n                        <Button\r\n                            variant=\"ghost\"\r\n                            size=\"sm\"\r\n                            className=\"text-typography-300 hover:text-typography-600\"\r\n                            onClick={() => resetEditLocalityClicked()}\r\n                        >\r\n                            <X size={16} />\r\n                        </Button> </>\r\n                    ) :\r\n                        (<>\r\n                            <Button\r\n                                variant=\"ghost\"\r\n                                size=\"sm\"\r\n                                className=\"text-indigo-600 hover:text-indigo-900\"\r\n                                onClick={() => editLocalityClicked(networkAreaDetail.networkAreaId)}\r\n                            >\r\n                                <Pencil size={16} />\r\n                            </Button>\r\n                            <Button\r\n                                variant=\"ghost\"\r\n                                size=\"sm\"\r\n                                className=\"text-red-500 hover:text-red-900\"\r\n                                onClick={() => handleSwitchNetwork(networkAreaDetail.networkAreaId)}\r\n                            >\r\n                                <Trash2 size={16} />\r\n                            </Button></>)}\r\n                    {/* <Switch\r\n                    checked={networkStatus[networkAreaDetail.networkAreaId]}  // Use sellerStates to track toggle status\r\n                    onCheckedChange={() => handleSwitchNetwork(networkAreaDetail.networkAreaId)}\r\n                /> */}\r\n                </div>}\r\n            </div>\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default NetworkAreaCard;", "import { useEffect, useState } from \"react\";\r\nimport { useFetcher } from \"@remix-run/react\";\r\nimport { Dialog, DialogContent, DialogTitle } from \"./dialog\";\r\nimport { Input } from \"./input\";\r\nimport { <PERSON><PERSON> } from \"./button\";\r\nimport { useToast } from \"./ToastProvider\";\r\nimport { Seller } from \"~/types/api/businessConsoleService/MasterItemCategory\";\r\nimport SpinnerLoader from \"../loader/SpinnerLoader\";\r\n\r\nexport interface RoleOption {\r\n      [x: string]: any;\r\n      value: string;\r\n      label: string;\r\n}\r\n\r\ninterface NetWorkModalProps {\r\n      isOpen: boolean;\r\n      onClose: () => void;\r\n      NetWorkId: number;\r\n      roles: RoleOption[],\r\n      sellerList: Seller[]\r\n}\r\n\r\nexport default function CreateAgent({ isOpen, onClose, NetWorkId, roles, sellerList }: NetWorkModalProps) {\r\n      const fetcher = useFetcher();\r\n      const [managerId, setManagerId] = useState<number | \"\">(\"\");\r\n\r\n      const [formData, setFormData] = useState({\r\n            firstName: \"\",\r\n            lastName: \"\",\r\n            email: \"\",\r\n            mobileNumber: \"\",\r\n            address: \"\",\r\n            password: \"\",\r\n            businessId: managerId,\r\n            roles: [] as string[],\r\n      });\r\n\r\n      const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n\r\n\r\n            const { name, value } = e.target;\r\n            setFormData((prev) => ({\r\n                  ...prev,\r\n                  [name]: value,\r\n            }));\r\n      };\r\n\r\n      const handleManagerChange = (e: React.ChangeEvent<HTMLSelectElement>) => {\r\n            const selectedManagerId = e.target.value ? Number(e.target.value) : 0;\r\n            setManagerId(selectedManagerId);\r\n\r\n            setFormData((prev) => ({\r\n                  ...prev,\r\n                  businessId: selectedManagerId,\r\n            }));\r\n      };\r\n\r\n\r\n      const isLoading = fetcher.state !== \"idle\";\r\n      const { showToast } = useToast()\r\n\r\n      useEffect(() => {\r\n            if (fetcher.data && isOpen === true) {\r\n\r\n\r\n                  if (fetcher.data !== undefined && isOpen === true) {\r\n                        showToast(\"AgentCreated SuccessFully\", \"success\")\r\n                        onClose()\r\n                        setFormData({\r\n                              firstName: \"\",\r\n                              lastName: \"\",\r\n                              email: \"\",\r\n                              mobileNumber: \"\",\r\n                              address: \"\",\r\n                              password: \"\",\r\n                              businessId: managerId,\r\n                              roles: [] as string[],\r\n                        })\r\n                  }\r\n                  else if (fetcher.data === undefined) {\r\n                        showToast(\"Fail To create Agent\", \"error\"); // Show error in parent\r\n                  }\r\n            }\r\n\r\n\r\n      }, [fetcher.data])\r\n\r\n\r\n      const handleSubmit = (e: React.FormEvent) => {\r\n            e.preventDefault();\r\n            const data = new FormData();\r\n            Object.entries(formData).forEach(([key, value]) => {\r\n                  data.append(key, Array.isArray(value) ? value.join(\",\") : value.toString());\r\n            });\r\n            data.append(\"netWorkId\", NetWorkId.toString());\r\n            data.append(\"_intent\", \"createAgent\");\r\n            data.append(\"roles\", \"AgentFull\")\r\n            fetcher.submit(data, { method: \"POST\" });\r\n      };\r\n\r\n      return (\r\n            <Dialog open={isOpen} onOpenChange={onClose}>\r\n                  <SpinnerLoader loading={isLoading} />\r\n                  <DialogContent>\r\n                        {isLoading && (\r\n                              <div className=\"absolute inset-0 flex items-center justify-center bg-white/10 backdrop-blur-sm\">\r\n                                    <SpinnerLoader loading={isLoading} />\r\n                              </div>\r\n                        )}\r\n                        <DialogTitle>Add New Agent</DialogTitle>\r\n                        <form onSubmit={handleSubmit} className=\"space-y-4\">\r\n\r\n                              <div className=\"flex gap-2\">\r\n                                    <div className=\"w-full flex-col flex gap-y-2\">\r\n                                          <label className=\"block text-sm font-medium text-gray-700\">First Name</label>\r\n                                          <Input value={formData.firstName} name=\"firstName\" placeholder=\"First Name\" onChange={handleChange} required />\r\n                                    </div>\r\n                                    <div className=\"w-full flex-col flex gap-y-2\">\r\n                                          <label className=\"block text-sm font-medium text-gray-700\">Last Name</label>\r\n                                          <Input name=\"lastName\" value={formData.lastName} placeholder=\"Last Name\" onChange={handleChange} required />\r\n                                    </div>\r\n                              </div>\r\n\r\n                              <div className=\"flex gap-2\">\r\n                                    <div className=\"w-full flex-col flex gap-y-2\">\r\n                                          <label className=\"block text-sm font-medium text-gray-700\">Email</label>\r\n                                          <Input type=\"email\" name=\"email\" placeholder=\"Email\" onChange={handleChange} required\r\n                                                value={formData.email}\r\n                                          />\r\n                                    </div>\r\n                                    <div className=\"w-full flex-col flex gap-y-2\">\r\n                                          <label className=\"block text-sm font-medium text-gray-700\">Mobile Number</label>\r\n                                          <Input name=\"mobileNumber\" placeholder=\"Mobile Number\" type=\"number\" onChange={(e) => {\r\n                                                const value = e.target.value.replace(/\\D/g, \"\");\r\n                                                if (value.length <= 10) {\r\n                                                      setFormData((prev) => ({\r\n                                                            ...prev,\r\n                                                            mobileNumber: value\r\n                                                      }\r\n                                                      ))\r\n                                                }\r\n                                          }}\r\n\r\n                                                value={formData.mobileNumber}\r\n\r\n                                                required />\r\n                                    </div>\r\n                              </div>\r\n\r\n                              <div className=\"w-full flex-col flex gap-y-2\">\r\n                                    <label className=\"block text-sm font-medium text-gray-700\">Address</label>\r\n                                    <Input name=\"address\" placeholder=\"Address\" onChange={handleChange} required\r\n                                          value={formData.address}\r\n                                    />\r\n                              </div>\r\n\r\n                              {/* <div className=\"flex gap-2\">\r\n                                    <div className=\"w-full flex-col flex gap-y-2\">\r\n                                          <label className=\"block text-sm font-medium text-gray-700\">Select Seller</label>\r\n                                          <select\r\n                                                className=\"border p-2 rounded w-full\"\r\n                                                value={managerId}\r\n                                                onChange={handleManagerChange}\r\n                                          >\r\n                                                <option value=\"\">Select Seller</option>\r\n                                                {sellerList?.map((seller) => (\r\n                                                      <option key={seller.businessId} value={seller.businessId}>\r\n                                                            {seller.name}\r\n                                                      </option>\r\n                                                ))}\r\n                                          </select>\r\n                                    </div>\r\n                              </div> */}\r\n                              <div className=\" flex justify-end items-end\">\r\n                                    <Button type=\"submit\" loading={isLoading} >Save Agent</Button>\r\n                              </div>\r\n                        </form>\r\n                  </DialogContent>\r\n            </Dialog>\r\n      );\r\n}", "\r\nimport { useEffect, useState } from \"react\";\r\nimport { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>Header, DialogTitle } from \"./dialog\";\r\nimport { Input } from \"./input\";\r\nimport { Button } from \"./button\";\r\nimport { useFetcher } from \"@remix-run/react\";\r\nimport { useToast } from \"./ToastProvider\";\r\nimport SpinnerLoader from \"../loader/SpinnerLoader\";\r\n\r\ninterface NetWorkModalProps {\r\n      isOpen: boolean;\r\n      onClose: () => void;\r\n      sellerList: { id: number; name: string }[];\r\n      netWorkId: number\r\n}\r\nexport function NetWorkModal({ isOpen, onClose, sellerList, netWorkId }: NetWorkModalProps) {\r\n      const [sellerId, setSellerId] = useState<number | \"\">(\"\");\r\n\r\n      const fetcher = useFetcher()\r\n      const handleSave = () => {\r\n            const formData = new FormData()\r\n            formData.append(\"_intent\", \"createNetWork\");\r\n            formData.append(\"netWorkId\", netWorkId as unknown as string)\r\n            formData.append(\"sellerId\", sellerId as unknown as string)\r\n            fetcher.submit(formData, { method: \"POST\" })\r\n      };\r\n\r\n      const { showToast } = useToast()\r\n      const isLoading = fetcher.state !== \"idle\"\r\n\r\n      useEffect(() => {\r\n            if (fetcher.data && isOpen === true) {\r\n                  if (fetcher.data) {\r\n                        showToast(\"seller to Network SuccessFully\", \"success\")\r\n                        onClose()\r\n                  }\r\n                  else {\r\n                        showToast(\"sellerMapping Failed\", \"error\")\r\n\r\n                  }\r\n\r\n            }\r\n\r\n      }, [fetcher.data])\r\n\r\n      return (\r\n            <Dialog open={isOpen} onOpenChange={onClose}>\r\n                  <DialogContent className=\"max-h-[50vh]  max-w-sm overflow-y-auto\">\r\n                        {isLoading && (\r\n                              <div className=\"absolute inset-0 flex items-center justify-center bg-white/10 backdrop-blur-sm\">\r\n                                    <SpinnerLoader loading={isLoading} />\r\n                              </div>\r\n                        )}\r\n                        <DialogHeader>\r\n                              <DialogTitle>Map Seller to Network</DialogTitle>\r\n                        </DialogHeader>\r\n                        <div className=\"flex max-w-s flex-col gap-4\">\r\n\r\n                              <select\r\n                                    className=\"border p-2 rounded\"\r\n                                    value={sellerId}\r\n                                    onChange={(e) => setSellerId(e.target.value ? Number(e.target.value) : \"\")}\r\n                              >\r\n                                    <option value=\"\">Select Seller</option>\r\n                                    {sellerList.map((seller) => (\r\n                                          <option key={seller.id} value={seller.id}>\r\n                                                {seller.name}\r\n                                          </option>\r\n                                    ))}\r\n                              </select>\r\n\r\n                              {/* Buttons */}\r\n                              <div className=\"flex justify-end gap-2\">\r\n                                    <Button variant=\"outline\" onClick={onClose}>\r\n                                          Cancel\r\n                                    </Button>\r\n                                    <Button onClick={handleSave} disabled={!sellerId} loading={isLoading}>\r\n                                          Save\r\n                                    </Button>\r\n                              </div>\r\n                        </div>\r\n                  </DialogContent>\r\n            </Dialog>\r\n      );\r\n}\r\n", "import { useState, useRef, useEffect } from \"react\";\r\n\r\nimport { Save, X } from \"lucide-react\";\r\nimport { Button } from \"../ui/button\";\r\nimport { Input } from \"../ui/input\";\r\nimport { Dialog, DialogContent, DialogTitle } from \"../ui/dialog\";\r\nimport { useFetcher, useSearchParams } from \"@remix-run/react\";\r\nimport { useToast } from \"../ui/ToastProvider\";\r\n\r\ninterface CreateBannerModalProps {\r\n      isOpen: boolean;\r\n      onClose: () => void;\r\n      networkId: number\r\n\r\n}\r\n\r\nexport default function CreateNetworkBannerModal({ isOpen, onClose, networkId }: CreateBannerModalProps) {\r\n      const [bannerUrl, setBannerUrl] = useState<string>(\"\");\r\n      const [previewUrl, setPreviewUrl] = useState<string>(\"\");\r\n      const [sequenceId, setSequenceId] = useState<number | \"\">(\"\");\r\n      const fileInputRef = useRef<HTMLInputElement>(null);\r\n      const [uploadError, setUploadError] = useState<string | null>(null);\r\n\r\n      const uploadFetcher = useFetcher<{ success: boolean; fileUrl: string; error?: string }>();\r\n\r\n      const bannerFetcher = useFetcher();\r\n\r\n      const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {\r\n            const files = Array.from(event.target.files || []);\r\n            if (files.length === 0) return;\r\n\r\n            setUploadError(null);\r\n\r\n            const MAX_FILE_SIZE = 500 * 1024\r\n            const allowedTypes = [\"image/jpeg\", \"image/png\", \"image/gif\", \"image/webp\"];\r\n\r\n            const validFile = files.find((file) => {\r\n                  if (file.size > MAX_FILE_SIZE) {\r\n                        setUploadError(\"File size exceeds 500Kb limit.\");\r\n                        return false;\r\n                  }\r\n                  if (!allowedTypes.includes(file.type)) {\r\n                        setUploadError(\"Only JPEG, PNG, GIF, and WEBP images are allowed.\");\r\n                        return false;\r\n                  }\r\n                  return true;\r\n            });\r\n\r\n            if (!validFile) return;\r\n\r\n            const uploadFormData = new FormData();\r\n            uploadFormData.append(\"_action\", \"uploadImage\");\r\n            uploadFormData.append(\"file\", validFile, validFile.name);\r\n\r\n            uploadFetcher.submit(uploadFormData, {\r\n                  method: \"post\",\r\n                  action: \"/home/<USER>\",\r\n                  encType: \"multipart/form-data\",\r\n            });\r\n      };\r\n      const handleSave = () => {\r\n            if (bannerUrl && sequenceId !== \"\") {\r\n                  const formData = new FormData();\r\n                  formData.append(\"bannerUrl\", bannerUrl);\r\n                  formData.append(\"sequenceId\", sequenceId.toString());\r\n                  formData.append(\"networkId\", networkId.toString());\r\n\r\n                  formData.append(\"actionType\", \"createNetworkBanner\");\r\n                  bannerFetcher.submit(formData, { method: \"POST\" })\r\n            }\r\n      };\r\n      const handleClose = () => {\r\n            onClose()\r\n\r\n            setBannerUrl(\"\");\r\n            setPreviewUrl(\"\");\r\n            setSequenceId(\"\")\r\n      }\r\n\r\n      const { showToast } = useToast()\r\n\r\n      useEffect(() => {\r\n            if (bannerFetcher?.data) {\r\n                  if (bannerFetcher.data?.sucess) {\r\n                        showToast(\"Network banner Created Successfully\", \"success\");\r\n                        onClose(); // Close only after success\r\n                  } else {\r\n                        if (bannerFetcher.data?.sucess == false) {\r\n                              showToast(\"Network banner Failed\", \"error\");\r\n\r\n                        }\r\n                  }\r\n            }\r\n      }, [bannerFetcher.data]);\r\n\r\n      useEffect(() => {\r\n            if (uploadFetcher.data) { // Ensure currentRowId is not null\r\n                  if (uploadFetcher.data.error) {\r\n                        setUploadError(uploadFetcher.data.error);\r\n                  } else if (uploadFetcher.data.fileUrl) {\r\n                        const uploadedUrl = uploadFetcher.data.fileUrl;\r\n\r\n                        setBannerUrl(\r\n                              uploadedUrl, // `!` asserts it's not null\r\n                        );\r\n\r\n                        setPreviewUrl(\r\n                              uploadedUrl,\r\n                        );\r\n\r\n                        setUploadError(null);\r\n                        if (fileInputRef.current) fileInputRef.current.value = \"\";\r\n                  }\r\n            }\r\n      }, [uploadFetcher.data]); // Ensure currentRowId is included in dependencies\r\n\r\n\r\n      return (\r\n            <Dialog open={isOpen} onOpenChange={handleClose}>\r\n                  <DialogContent>\r\n                        <DialogTitle>Create Network Banner</DialogTitle>\r\n                        <div className=\"flex flex-col gap-4\">\r\n                              {/* File Upload */}\r\n                              <input\r\n                                    ref={fileInputRef}\r\n                                    type=\"file\"\r\n                                    accept=\"image/*\"\r\n                                    onChange={handleFileSelect}\r\n                                    className=\"file:py-2 file:px-4 file:rounded-lg file:border-0 file:bg-blue-100 file:text-blue-700 hover:file:bg-blue-200 transition\"\r\n                              />\r\n                              {/* Image Preview */}\r\n                              {previewUrl && (\r\n                                    <div className=\"flex flex-col items-center p-2 border border-gray-300 rounded-lg bg-white shadow-sm\">\r\n                                          <span className=\"text-md font-bold text-red-600\">Upload Image 1000*400 Size</span>\r\n                                          <span className=\"text-sm text-gray-600\">Image Preview</span>\r\n                                          <img src={previewUrl} alt=\"Preview\" className=\"mt-2 rounded-md w-[250px] h-[100px] object-cover\" />\r\n                                    </div>\r\n                              )}\r\n\r\n\r\n                              {uploadError !== \"\" &&\r\n                                    <span className=\"text-md font-bold text-red-600\">{uploadError}</span>\r\n                              }\r\n\r\n                              {/* Sequence Input */}\r\n                              <Input\r\n                                    type=\"number\"\r\n                                    placeholder=\"Enter Sequence\"\r\n                                    value={sequenceId}\r\n                                    onChange={(e) => setSequenceId(e.target.value ? Number(e.target.value) : \"\")}\r\n                              />\r\n                        </div>\r\n                        <div className=\" flex flex-row justify-center gap-5\">\r\n                              <Button onClick={handleClose} variant=\"outline\"><X size={16} /> Cancel</Button>\r\n                              <Button onClick={handleSave} disabled={!bannerUrl || sequenceId === \"\"}>\r\n                                    <Save size={16} /> Save\r\n                              </Button>\r\n                        </div>\r\n                  </DialogContent>\r\n            </Dialog>\r\n      );\r\n}\r\n", "import { useState, useCallback, useEffect, useRef } from 'react'\r\nimport { json, useF<PERSON>cher, use<PERSON>oaderD<PERSON>, useNavigate } from \"@remix-run/react\";\r\nimport { useSubmit } from \"@remix-run/react\";\r\nimport { addNetworkLocalities, getNetworkAreas, getNetworkBanners, getNetworkConfig, getNeworkAgents, getsmDistrictsAndStates } from \"~/services/businessConsoleService\";\r\nimport { NetworkAreas, NetworkBanner, NetworkConfig } from \"~/types/api/businessConsoleService/Network\";\r\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from \"@components/ui/select\"\r\nimport { withAuth, withResponse } from \"~/utils/auth-utils\";\r\nimport { Pencil, Save, Trash2, X } from \"lucide-react\";\r\nimport NetworkAreaCard from \"~/components/ui/customNetworkAreaCard\";\r\nimport { Tabs, TabsContent, TabsList, TabsTrigger } from \"~/components/ui/tabs\";\r\nimport { GoogleMap, InfoWindow, LoadScript, Marker, Polygon } from '@react-google-maps/api';\r\nimport { decodePolygon } from '~/utils/polyline-utils';\r\nimport { MasterLocalities, networkAgents, StateAndDistricts } from '~/types/api/businessConsoleService/SellerManagement';\r\nimport { Checkbox } from '~/components/ui/checkbox';\r\nimport { createAgent, getSelectedNetWorkAgent, getSellerList, updateAttributes } from '~/services/masterItemCategories';\r\nimport { Button } from '~/components/ui/button';\r\nimport CreateAgent, { RoleOption } from '~/components/ui/createAgent';\r\nimport { ResponsiveTable } from '~/components/ui/responsiveTable';\r\nimport { createBanner, createNetworkSeller, deleteBanner, getNetWorkSeller, updateBannerSeq, updateBannerStatus, updateBannerUrl, updateNetWorkAgentStatus, updateNetWorkArea, updateNetWorkStatus } from '~/services/netWorks';\r\nimport { NetWorkDetails } from '~/types/api/businessConsoleService/netWorkinfo';\r\nimport { NetWorkModal } from '~/components/ui/netWorkModal';\r\nimport { Switch } from '~/components/ui/switch';\r\nimport { useToast } from '~/components/ui/ToastProvider';\r\nimport { Seller } from '~/types/api/businessConsoleService/MasterItemCategory';\r\nimport NetWorkConfig from './netWorkConfig';\r\nimport { Input } from '~/components/ui/input';\r\nimport CreateNetworkBannerModal from '~/components/common/NetworkBanneModal';\r\nimport SpinnerLoader from '~/components/loader/SpinnerLoader';\r\nexport interface newLocal {\r\n  id: number | null;\r\n  networkId: number | null;\r\n  agentUserId: string | null;\r\n  localities: MasterLocalities | null;\r\n}\r\ninterface LoaderData {\r\n  googleMapsApiKey: string\r\n  networkId: number,\r\n  networkName: string,\r\n  activeTab: string,\r\n  networkAreas?: NetworkAreas[],\r\n  networkConfig?: NetworkConfig[],\r\n  networkBanners?: NetworkBanner[],\r\n  statesAndDistricts?: StateAndDistricts[],\r\n  networkAgents?: networkAgents[],\r\n  url: string,\r\n  userId: number,\r\n  roles: RoleOption[],\r\n  networkSeller: NetWorkDetails[],\r\n  sellerList: Seller[],\r\n  permissions: string[]\r\n\r\n}\r\n\r\nexport interface ActionData {\r\n  sucess?: boolean,\r\n  error?: string\r\n}\r\n\r\nconst BANGALORE_CENTER = {\r\n  lat: 12.9716,\r\n  lng: 77.5946\r\n}\r\n\r\nconst MAP_CONTAINER_STYLE = {\r\n  width: '100%',\r\n  height: '100%'\r\n}\r\nconst getPolygonColor = (index: number) => {\r\n  const colors = ['#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6', '#ec4899', '#06b6d4', '#f97316']\r\n  return colors[index % colors.length]\r\n}\r\nexport const loader = withAuth(async ({ user, request }) => {\r\n  const permissions = user?.userDetails?.roles\r\n  const isSales = permissions?.includes('FmSalesManager')\r\n\r\n  const googleMapsApiKey = process.env.GOOGLE_MAPS_API_KEY || ''\r\n  const url = new URL(request.url);\r\n  const networkId = Number(url.searchParams.get(\"networkId\"));\r\n  const networkName = (url.searchParams.get(\"networkName\"));\r\n  type ActiveTab = \"NetworkConfig\" | \"NetworkAreas\" | \"NetworkAgents\" | \"NetworkSeller\" | \"NetworkBanners\";\r\n\r\n  const activeTab: ActiveTab = url.searchParams.get(\"activeTab\") as ActiveTab || (!isSales ? \"NetworkConfig\" : \"NetworkAgents\");\r\n  let networkAreas: NetworkAreas[] | [] = [];\r\n  let statesAndDistricts: StateAndDistricts[] | [] = [];\r\n  let networkConfig: NetworkConfig[] | [] = [];\r\n  let networkBanners: NetworkBanner[] | [] = [];\r\n\r\n  let networkAgents: networkAgents[] | [] = [];\r\n  let networkSeller: NetWorkDetails[] | [] = []\r\n  let sellerList: Seller[] | [] = []\r\n  let roles: RoleOption[] | [] = [];\r\n  let userId = user.userId;\r\n\r\n  let response;\r\n  try {\r\n    switch (activeTab) {\r\n      case \"NetworkAreas\":\r\n        const [areasResponse, districtsAndStatesResponse, networkAgentsResponse] = await Promise.all([getNetworkAreas(networkId, request),\r\n        getsmDistrictsAndStates(user.userId, request), getNeworkAgents(networkId, request)]);\r\n        networkAreas = areasResponse?.data || [];\r\n        statesAndDistricts = districtsAndStatesResponse?.data || [];\r\n        networkAgents = networkAgentsResponse?.data || [];\r\n        break;\r\n      case \"NetworkConfig\":\r\n        response = await getNetworkConfig(\"networkDomain\", networkId, request);\r\n        networkConfig = response?.data || [];\r\n        console.log(networkConfig, \"66666666666666\", response.data, \"111111\")\r\n        break;\r\n      case \"NetworkAgents\":\r\n        response = await getSelectedNetWorkAgent(networkId, request);\r\n        networkAgents = response?.data || [];\r\n        break;\r\n      case \"NetworkBanners\":\r\n        response = await getNetworkBanners(networkId, request);\r\n        networkBanners = response?.data || [];\r\n        console.log(networkBanners, \"66666666666666\", response.data, \"111111\")\r\n        break;\r\n      case \"NetworkSeller\":\r\n        console.log(networkId, \"999999999999999\")\r\n        const [networkSellerResponse, masterSellerResponse] = await Promise.all([getNetWorkSeller(networkId, request), getSellerList(request)])\r\n        //response = await getNetWorkSeller(networkId, request);        \r\n        networkSeller = networkSellerResponse?.data || [];\r\n        sellerList = masterSellerResponse?.data || [];\r\n        break;\r\n\r\n\r\n    }\r\n    return withResponse({\r\n      googleMapsApiKey,\r\n      networkId,\r\n      networkName,\r\n      activeTab,\r\n      networkAreas,\r\n      statesAndDistricts,\r\n      networkConfig,\r\n      networkBanners,\r\n      networkAgents,\r\n      url,\r\n      userId,\r\n      roles,\r\n      networkSeller,\r\n      sellerList,\r\n      permissions\r\n\r\n\r\n    }, response?.headers);\r\n\r\n\r\n  } catch (error) {\r\n    console.log(\"loader failed\");\r\n    console.error(\"Error in loader:\", error);\r\n    // Return a JSON-based error shape\r\n    return [];\r\n  }\r\n});\r\n\r\nexport const action = async ({ request }: { request: Request }) => {\r\n  const formData = await request.formData();\r\n  const intent = formData.get(\"_intent\");\r\n  const netWorkId = formData.get(\"netWorkId\") as unknown as number;\r\n  const sellerId = formData.get(\"sellerId\") as unknown as number;\r\n  const actionType = formData.get(\"actionType\");\r\n  const nAreaId = formData.get(\"nAreaId\") as unknown as number;\r\n  const attribute = formData.get(\"attribute\") as string;\r\n  const updateValue = formData.get(\"value\");\r\n  const type = formData.get(\"updateType\") as string;\r\n  const domainId = formData.get(\"domainId\") as unknown as number;\r\n  const BannerId = Number(formData.get(\"bannerId\"));\r\n\r\n  if (actionType === \"updateNetWorkAreaEdit\") {\r\n    const agentUserId = formData.get(\"agentUserId\") as unknown as number;\r\n    const networkId = formData.get(\"editNetworkId\") as unknown as number;\r\n    const area = formData.get(\"masterAreaId\") as unknown as number;\r\n    const areas: number[] = [area];\r\n    try {\r\n      console.log(`📡 Sending API request for agentUserId: ${agentUserId}`);\r\n      const apiResponse = await addNetworkLocalities(networkId, agentUserId, areas, request);\r\n      console.log(\"✅ API Response:\", apiResponse);\r\n      return apiResponse\r\n    } catch (error) {\r\n      console.error(`❌ Error in API call for agentUserId ${agentUserId}:`, error);\r\n      throw new Response(\"Something Went Wrong\", { status: 500 });\r\n    }\r\n  }\r\n  if (actionType === \"updateNetWorkAreaStatus\") {\r\n    try {\r\n      const response = await updateNetWorkArea(nAreaId, request)\r\n      return withResponse({\r\n        NetWorkAreas: response.data,\r\n        netWorkId: netWorkId,\r\n        sellerId: sellerId,\r\n        nAreaId: nAreaId\r\n      }, response.headers);\r\n\r\n    }\r\n    catch (error) {\r\n      if (error instanceof Response && error.status === 404) {\r\n\r\n        throw json({ error: \"update NetWork Status page Not found\" }, { status: 404 });\r\n      }\r\n      else throw new Response(\"Something Went Wrong\", { status: 500 });\r\n\r\n    }\r\n  }\r\n  if (actionType === \"updateNetWorkAreaStatus\") {\r\n    try {\r\n      const response = await updateNetWorkArea(nAreaId, request)\r\n      return withResponse({\r\n        NetWorkAreas: response.data,\r\n        netWorkId: netWorkId,\r\n        sellerId: sellerId,\r\n        nAreaId: nAreaId\r\n      }, response.headers);\r\n\r\n    }\r\n    catch (error) {\r\n      if (error instanceof Response && error.status === 404) {\r\n\r\n        throw json({ error: \"update NetWork Status page Not found\" }, { status: 404 });\r\n      }\r\n      else throw new Response(\"Something Went Wrong\", { status: 500 });\r\n\r\n    }\r\n  }\r\n  if (actionType === \"updateBanner\") {\r\n\r\n    console.log(\"deleteBanner.......\")\r\n\r\n    try {\r\n      const response = await deleteBanner(netWorkId, BannerId, request)\r\n      return withResponse({\r\n        networkBanners: response.data,\r\n\r\n      }, response.headers);\r\n\r\n    }\r\n    catch (error) {\r\n      if (error instanceof Response && error.status === 404) {\r\n\r\n        throw json({ error: \"update NetWork Status page Not found\" }, { status: 404 });\r\n      }\r\n      throw new Response(\"Something Went Wrong\", { status: 500 });\r\n\r\n    }\r\n  }\r\n  if (actionType === \"bannerSequence\") {\r\n    const bannerData = JSON.parse(formData.get(\"bannerData\") as string || \"{}\");\r\n    const seqId = Number(formData.get(\"seQuenceId\"))\r\n\r\n    try {\r\n      const response = await updateBannerSeq(seqId, bannerData, request)\r\n      return withResponse({\r\n        networkBanners: response.data,\r\n\r\n      }, response.headers);\r\n\r\n    }\r\n    catch (error) {\r\n      if (error instanceof Response && error.status === 404) {\r\n\r\n        throw json({ error: \"update NetWork Status page Not found\" }, { status: 404 });\r\n      }\r\n      throw new Response(\"Something Went Wrong\", { status: 500 });\r\n    }\r\n  }\r\n\r\n  if (actionType === \"updateBannerUrl\") {\r\n    const bannerData = JSON.parse(formData.get(\"bannerData\") as string || \"{}\");\r\n    const bannerUrl = formData.get(\"bannerUrl\") as string\r\n    try {\r\n      const response = await updateBannerUrl(bannerUrl, bannerData, request)\r\n      return withResponse({\r\n        networkBanners: response.data,\r\n\r\n      }, response.headers);\r\n\r\n    }\r\n    catch (error) {\r\n      if (error instanceof Response && error.status === 404) {\r\n\r\n        throw json({ error: \"update NetWork Status page Not found\" }, { status: 404 });\r\n      }\r\n      throw new Response(\"Something Went Wrong\", { status: 500 });\r\n    }\r\n  }\r\n  if (actionType === \"createNetworkBanner\") {\r\n    const networkId = Number(formData.get(\"networkId\"));\r\n    const sequenceId = Number(formData.get(\"sequenceId\"));\r\n    const bannerUrl = formData.get(\"bannerUrl\") as string\r\n    try {\r\n      const response = await createBanner(networkId, bannerUrl, sequenceId, request)\r\n      return withResponse({\r\n        sucess: response.statusCode,\r\n      }, response.headers);\r\n\r\n    }\r\n    catch (error) {\r\n      if (error instanceof Response && error.status === 404) {\r\n\r\n        throw json({ error: \"update NetWork Status page Not found\" }, { status: 404 });\r\n      }\r\n      throw new Response(\"Something Went Wrong\", { status: 500 });\r\n    }\r\n  }\r\n\r\n\r\n\r\n  const supportedAttributes = [\r\n    \"business_logo\",\r\n    \"home_page_banner\", \"pwa_app_icon\", \"footer_app_icon\", \"multi_seller\", \"default_seller_id\",\r\n    \"wab_enabled\", \"wab_mobile_number\", \"default_start_page\", \"domain\", \"wabDatasetId\"\r\n  ];\r\n  if (supportedAttributes.includes(attribute)) {\r\n\r\n    try {\r\n      const updatedResponse = await updateAttributes(type, domainId, attribute, updateValue, request);\r\n\r\n\r\n      console.log(\"Updating Network details:\", { attribute, updateValue });\r\n\r\n      return withResponse(\r\n        {\r\n          sellerConfig: updatedResponse.data,\r\n          sellerId: sellerId,\r\n        },\r\n        updatedResponse.headers\r\n      );\r\n    }\r\n    catch (error) {\r\n      throw new Response(\"Something Went Wrong\", { status: 500 });\r\n    }\r\n  }\r\n\r\n  if (actionType === \"updateNetWorkStatus\") {\r\n    try {\r\n      const response = await updateNetWorkStatus(netWorkId, sellerId, request)\r\n      return withResponse({\r\n        networkSeller: response.data,\r\n\r\n        netWorkId: netWorkId,\r\n        sellerId: sellerId\r\n      }, response.headers);\r\n    }\r\n    catch (error) {\r\n      if (error instanceof Response && error.status === 404) {\r\n\r\n        throw json({ error: \"update NetWork Status page Not found\" }, { status: 404 });\r\n      }\r\n      throw new Response(\"Failed to NetWork Seller Status\", { status: 500 });\r\n    }\r\n\r\n  }\r\n\r\n  if (actionType === \"updateAgentStatus\") {\r\n    const agentUserId = formData.get(\"agentUserId\") as unknown as number\r\n\r\n    try {\r\n      const response = await updateNetWorkAgentStatus(netWorkId, agentUserId, request)\r\n\r\n      return withResponse({\r\n        networkAgents: response.data,\r\n        netWorkId: netWorkId,\r\n        sellerId: sellerId,\r\n        agentUserId: agentUserId\r\n      }, response.headers);\r\n\r\n    }\r\n    catch (error) {\r\n      if (error instanceof Response && error.status === 404) {\r\n\r\n        throw json({ error: \"update NetWork Status page Not found\" }, { status: 404 });\r\n      }\r\n      throw new Response(\"Failed to NetWork Seller Status\", { status: 500 });\r\n    }\r\n\r\n  }\r\n  if (actionType === \"updateNetWorkStatus\") {\r\n    try {\r\n      const response = await updateNetWorkStatus(netWorkId, sellerId, request)\r\n\r\n\r\n      return withResponse({\r\n        networkSeller: response.data,\r\n\r\n        netWorkId: netWorkId,\r\n        sellerId: sellerId\r\n      }, response.headers);\r\n\r\n    }\r\n    catch (error) {\r\n      if (error instanceof Response && error.status === 404) {\r\n\r\n        throw json({ error: \"update NetWork Status page Not found\" }, { status: 404 });\r\n      }\r\n      throw new Response(\"Failed to NetWork Seller Status\", { status: 500 });\r\n    }\r\n\r\n  }\r\n  if (intent === \"createAgent\") {\r\n    const newAgent = {\r\n      firstName: formData.get(\"firstName\") as string,\r\n      lastName: formData.get(\"lastName\") as string,\r\n      email: formData.get(\"email\") as string,\r\n      mobileNumber: formData.get(\"mobileNumber\") as string,\r\n      address: formData.get(\"address\") as string,\r\n      password: formData.get(\"password\") as string,\r\n      businessId: Number(formData.get(\"businessId\")),\r\n      roles: (formData.get(\"roles\") as string)?.split(\",\"),\r\n    };\r\n    try {\r\n      await createAgent(Number(netWorkId), newAgent, request);\r\n      return json({ success: true });\r\n    } catch (error) {\r\n      throw new Response(\"Something Went Wrong\", { status: 500 });\r\n    }\r\n  }\r\n\r\n  if (intent === \"createNetWork\") {\r\n\r\n    try {\r\n      const response = await createNetworkSeller(netWorkId, sellerId, request);\r\n      return withResponse({\r\n        networkSeller: response.data,\r\n        netWorkId: netWorkId\r\n\r\n      }, response.headers);\r\n\r\n    } catch (error) {\r\n      throw new Response(\"Something Went Wrong\", { status: 500 });\r\n    }\r\n  }\r\n  if (request.method !== \"POST\") {\r\n    return json({ error: \"Method Not Allowed\" }, { status: 405 });\r\n  }\r\n\r\n  if (actionType === \"addLocalities\") {\r\n    try {\r\n      console.log(\"📩 Received formData:\", Object.fromEntries(formData));\r\n      console.log(\"✅ Call `addNetworkLocalities`\");\r\n      const newLocalities = JSON.parse(formData.get(\"localities\") as string || \"[]\");\r\n      console.log(`newLocalities : `, newLocalities);\r\n      if (!newLocalities || !Array.isArray(newLocalities) || newLocalities.length === 0) {\r\n        console.error(\"❌ Invalid request payload\");\r\n        return json({ error: \"Invalid request payload\" }, { status: 400 });\r\n      }\r\n\r\n      const apiResponses = [];\r\n      for (const locality of newLocalities) {\r\n        const { agentUserId, networkId, areas } = locality;\r\n\r\n        try {\r\n          console.log(`📡 Sending API request for agentUserId: ${agentUserId}`);\r\n          const apiResponse = await addNetworkLocalities(networkId, agentUserId, areas, request);\r\n          console.log(\"✅ API Response:\", apiResponse);\r\n          apiResponses.push(apiResponse);\r\n        } catch (error) {\r\n          console.error(`❌ Error in API call for agentUserId ${agentUserId}:`, error);\r\n          apiResponses.push({ error: `Failed for agentUserId ${agentUserId}` });\r\n        }\r\n      }\r\n      return json({ success: true, data: apiResponses });\r\n    }\r\n\r\n    catch (error) {\r\n      console.error(\"❌ Error processing submission:\", error);\r\n      return json({ error: \"Internal Server Error\" }, { status: 500 });\r\n    }\r\n  }\r\n};\r\nexport default function NetworkDetailsPage() {\r\n  const { googleMapsApiKey, networkId, networkName, activeTab, networkAreas, statesAndDistricts, networkConfig, networkBanners, networkAgents, url, userId, roles, networkSeller, sellerList, permissions } = useLoaderData<LoaderData>()\r\n  // If no networks, show a \"No results\" row\r\n  const dataleng = networkAreas ? networkAreas.length : networkConfig;\r\n  const fetchfor = useNavigate();\r\n  const handleTabChange = (newTab: string) => {\r\n    console.log(newTab, \"333333333\")\r\n\r\n    fetchfor(`?networkId=${networkId}&networkName=${networkName}&activeTab=${newTab}`);\r\n  };\r\n\r\n  console.log(networkBanners, \"09890989098989\")\r\n  const [visibleAreas, setVisibleAreas] = useState<Set<number>>(new Set())\r\n  const [isLoading, setIsLoading] = useState(true); // Controls loader visibility\r\n  const [map, setMap] = useState<google.maps.Map | null>(null)\r\n  const [mapLoaded, setMapLoaded] = useState(false)\r\n  const onLoad = useCallback((mapInstance: google.maps.Map) => {\r\n    setMap(mapInstance)\r\n    setMapLoaded(true)\r\n  }, [])\r\n  let stateList: string[] = [];\r\n  statesAndDistricts?.forEach((area) => stateList.push(area.state));\r\n  interface InfoWindowState {\r\n    areaId: number | null;\r\n    isShown: boolean;\r\n    selectedAreaDetails: NetworkAreas | null;\r\n  }\r\n  interface pointerLocation {\r\n    latitude: number | null;\r\n    longitude: number | null;\r\n  }\r\n  const [latitude, setLatitude] = useState(0);\r\n  const [longitude, setLongitude] = useState(0);\r\n  const [showMarker, setShowMarker] = useState(false);\r\n  const [isLocateShopClicked, setisLocateShopClicked] = useState(false);\r\n  const [isAddLocalityClicked, setisAddLocalityClicked] = useState(false);\r\n  const [localityEditMode, setlocalityEditMode] = useState(false);\r\n  const [selectedState, setSelectedState] = useState('');\r\n  const [selectedDistrict, setSelectedDistrict] = useState('');\r\n  const [masterLocalities, setMasterLocalities] = useState<MasterLocalities[]>([]);\r\n  const [pointerLocation, setPointerLocation] = useState<pointerLocation>({\r\n    latitude: null,\r\n    longitude: null\r\n  });\r\n\r\n  const [infoWindowShown, setInfoWindowShown] = useState<InfoWindowState>({\r\n    areaId: null,\r\n    isShown: false,\r\n    selectedAreaDetails: null\r\n  });\r\n  const [newLocalities, setNewLocalities] = useState<MasterLocalities[]>([])\r\n  const [newLocalities2, setNewLocalities2] = useState<newLocal[]>([])\r\n  const handleFindLocation = useCallback((\r\n    lat: number,\r\n    long: number,\r\n    showMarker: boolean\r\n  ) => {\r\n    if (lat && long) {\r\n      setPointerLocation({ latitude: lat, longitude: long });\r\n      setShowMarker(showMarker);\r\n    }\r\n    else {\r\n      alert(\"Please enter valid numeric values for latitude and longitude.\");\r\n    }\r\n  }, []);\r\n\r\n  const salesPermissions = permissions.includes(\"FmSalesManager\")\r\n\r\n\r\n  const handleNewLocalities = useCallback((id: number, localityDetails: MasterLocalities, agentUserId?: number) => {\r\n    setNewLocalities((prevState) => {\r\n      if (prevState.some((abc) => abc.id === id)) {\r\n        // Remove the locality if it exists\r\n        return prevState.filter((abc) => abc.id !== id);\r\n      } else {\r\n        // Add the new locality\r\n        return [...prevState, localityDetails];\r\n      }\r\n    });\r\n  }, []);\r\n\r\n\r\n  const resetNewLocalities = useCallback(() => {\r\n    setNewLocalities([]);\r\n    setNewLocalities2([]);\r\n    setlocalityEditMode(false);\r\n  }, []);\r\n\r\n  const handleNewLocalities2 = useCallback(\r\n    (id: number, localityDetails: MasterLocalities, networkId: number, agentUserId?: string) => {\r\n      setNewLocalities2((prevState) => {\r\n        const existingIndex = prevState.findIndex((loc) => loc.id === id);\r\n        console.log(\"prevState.....\", prevState);\r\n\r\n        if (existingIndex !== -1) {\r\n          // Update agentUserId instead of removing\r\n          console.log(\"id found... updating agentUserId \");\r\n          return prevState.map((loc) =>\r\n            loc.id === id ? { ...loc, agentUserId: agentUserId ?? null } : loc\r\n          );\r\n        } else {\r\n          // Add new locality\r\n          console.log(\"id not found... adding it \");\r\n          return [\r\n            ...prevState,\r\n            {\r\n              id,\r\n              networkId: networkId, // Update as per requirement\r\n              agentUserId: agentUserId ?? null,\r\n              localities: localityDetails,\r\n            },\r\n          ];\r\n        }\r\n      });\r\n    },\r\n    [setNewLocalities2]\r\n  );\r\n\r\n  const handleUpdate = async (attribute: string, value: any, domainId: number) => {\r\n    try {\r\n      const formData = new FormData();\r\n      formData.append(\"updateType\", \"network_domain\");\r\n      formData.append(\"domainId\", domainId.toString());\r\n      formData.append(\"attribute\", attribute);\r\n      formData.append(\"value\", value.toString());\r\n      console.log(value, \"11111111111111111\")\r\n      console.log(attribute, \"11111111111111111\")\r\n      await fetcher.submit(formData, { method: \"POST\" });\r\n\r\n      showToast(`${attribute.replace(/([A-Z])/g, \" $1\")} updated successfully`, \"success\");\r\n      // Update UI only after successful response\r\n    } catch (error) {\r\n      showToast(`Failed to update ${attribute.replace(/([A-Z])/g, \" $1\")}`, \"error\");\r\n    }\r\n  };\r\n  const handleMarkerClick = useCallback(\r\n    (id: number,\r\n      areaDetails: NetworkAreas\r\n    ) => {\r\n      setInfoWindowShown((prevState) => {\r\n        if (prevState.isShown) {\r\n          if (prevState.areaId === id) {\r\n\r\n            return { areaId: null, isShown: false, selectedAreaDetails: null };\r\n          } else {\r\n\r\n            return { areaId: id, isShown: true, selectedAreaDetails: areaDetails };\r\n          }\r\n        } else {\r\n\r\n          return { areaId: id, isShown: true, selectedAreaDetails: areaDetails };\r\n        }\r\n      });\r\n    },\r\n    []\r\n  );\r\n\r\n  const { showToast } = useToast()\r\n\r\n\r\n  const updateToggle = (sellerId: number) => {\r\n    const formData = new FormData()\r\n    formData.append(\"sellerId\", sellerId as unknown as string)\r\n    formData.append(\"netWorkId\", networkId as unknown as string)\r\n    formData.append(\"actionType\", \"updateNetWorkStatus\")\r\n    fetcher.submit(formData, { method: \"POST\" })\r\n    if (fetcher.state === \"idle\" && fetcher.data !== undefined\r\n    ) {\r\n      showToast(\"NetWorkStatus updated Successfully\", \"success\")\r\n    }\r\n  }\r\n  const handleLocateShopClicked = useCallback((state: boolean) => { setisLocateShopClicked(state); }, []\r\n  );\r\n\r\n  const handleAddLocalityClicked = useCallback((state: boolean) => {\r\n    setisAddLocalityClicked(state);\r\n  }, []\r\n  );\r\n\r\n  const submit = useSubmit(); // ✅ Ensure useSubmit() is defined inside the component\r\n  const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {\r\n    console.log(\"handle Submit Called......\")\r\n    event.preventDefault();\r\n    console.log(\"🚀 handleSubmit Called!\"); // ✅ Debugging log\r\n    const formData = new FormData();\r\n    formData.append(\"actionType\", \"addLocalities\"); // or \"createAgent\"\r\n    const transformed = newLocalities2.map(item => ({\r\n      networkId: item.networkId ?? 0,\r\n      agentUserId: parseInt(item.agentUserId ?? \"0\"),     // Use the item's agentUserId or default to 0 if null\r\n      areas: [item.id ?? 0]\r\n    }));\r\n    formData.append(\"localities\", JSON.stringify(transformed));\r\n    console.log(\"📩 Form Data Before Submit:\", Object.fromEntries(formData));\r\n\r\n    submit(formData, { method: \"post\", encType: \"multipart/form-data\" });\r\n    handleAddLocalityClicked(false); resetNewLocalities();\r\n  };\r\n\r\n  const handleMasterLocalitiesClicked = useCallback(async (userId: number, state: string, district: string) => {\r\n    if (!state || !district) {\r\n      alert(\"Please select a state and district to proceed...\");\r\n      return;\r\n    }\r\n\r\n    try {\r\n      const response = await fetch(`./api/masterLocalities?userId=${userId}&state=${state}&district=${district}`);\r\n      //apps/business-console/app/routes/api/masterLocalities.ts\r\n      const data = await response.json();\r\n\r\n\r\n      if (!response.ok) {\r\n        throw new Error(data.error || \"Unknown error\");\r\n      }\r\n      setMasterLocalities(data.masterLocalities.data);\r\n    } catch (error) {\r\n      console.error(\"Error fetching Master Localities:\", error);\r\n      alert(\"Fetching Master Localities failed. Please try again.\");\r\n    }\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    if (map && networkAreas) {\r\n      const decodedPolygons = networkAreas.map((area) => ({\r\n        id: area.networkAreaId,\r\n        paths: area.encodedPolygon ? decodePolygon(area.encodedPolygon) : [],\r\n      }));\r\n      setVisibleAreas(new Set(decodedPolygons.map((polygon) => polygon.id))); // Track which polygons to show\r\n    }\r\n  }, [map, networkAreas]);\r\n\r\n  useEffect(() => {\r\n    // Set loading to false only when both API data and map are ready\r\n    if (googleMapsApiKey && networkAreas) {\r\n      if (mapLoaded) {\r\n        setIsLoading(false); // Everything is ready\r\n      }\r\n    }\r\n  }, [googleMapsApiKey, networkAreas, mapLoaded]);\r\n\r\n  const onUnmount = useCallback(() => {\r\n    setMap(null)\r\n    setMapLoaded(false)\r\n  }, [])\r\n\r\n  const getPolygonCenter = (paths: google.maps.LatLngLiteral[]) => {\r\n    const latitudes = paths.map((path) => path.lat);\r\n    const longitudes = paths.map((path) => path.lng);\r\n\r\n    const latSum = latitudes.reduce((a, b) => a + b, 0);\r\n    const lngSum = longitudes.reduce((a, b) => a + b, 0);\r\n\r\n    return {\r\n      lat: latSum / latitudes.length,\r\n      lng: lngSum / longitudes.length,\r\n    };\r\n  };\r\n\r\n  const NetworkHeaders = [\r\n    \"Id\",\r\n    \"Name\",\r\n    \"BusinessName\",\r\n    \"Status\"\r\n  ];\r\n  const NetworkBannerHeaders = [\r\n    \"Id\",\r\n    \"Banner Image\",\r\n    \"Sequence\",\r\n    \"Action\"\r\n  ];\r\n\r\n  const [isModalOpen, setIsModalOpen] = useState(false);\r\n  const [isModalBannerOpen, setIsModalBannerOpen] = useState(false);\r\n\r\n  const navigation = useNavigate();\r\n  const NetWorkSeller = [\r\n    \"Seller Id\",\r\n    \"Seller Name\",\r\n    \"Actions\",\r\n  ]\r\n  const fetcher = useFetcher();\r\n\r\n\r\n  const [toggledNetWork, setToggleNetwork] = useState<Record<number, boolean>>(() =>\r\n    networkSeller?.reduce((acc, seller) => {\r\n      acc[seller.networkSellerId] = seller.disabled;\r\n      return acc;\r\n    }, {} as Record<number, boolean>)\r\n\r\n  )\r\n\r\n  const handleSwitch = async (networkSellerId: number) => {\r\n\r\n    setToggleNetwork((prev) => ({\r\n      ...prev,\r\n      [networkSellerId]: !prev[networkSellerId]\r\n    }))\r\n    updateToggle(networkSellerId);\r\n\r\n\r\n  }\r\n  const handleSwitchBanner = (bannerId: number) => {\r\n    const formData = new FormData()\r\n    formData.append(\"bannerId\", bannerId as unknown as string)\r\n    formData.append(\"actionType\", \"updateBanner\")\r\n    formData.append(\"netWorkId\", networkId.toString())\r\n\r\n\r\n    fetcher.submit(formData, { method: \"POST\" })\r\n  }\r\n  const [agentStauts, setAgentStatus] = useState<{ [key: number]: boolean }>({})\r\n  const handleToggleStatus = (agentId: number, currentStatus: boolean) => {\r\n    const newStatus = !currentStatus;\r\n    setAgentStatus((prev) => ({ ...prev, [agentId]: newStatus }));\r\n    const formData = new FormData();\r\n    formData.append(\"_intent\", \"updateAgentStatus\");\r\n    formData.append(\"agentUserId\", agentId.toString());\r\n    formData.append(\"networkId\", networkId.toString());\r\n    formData.append(\"status\", newStatus.toString()); // Convert boolean to string\r\n    fetcher.submit(formData, { method: \"put\" });\r\n  };\r\n\r\n  const [isnBannerUpdate, setIsBannerUpdate] = useState<{ [key: number]: boolean }>({});\r\n  const [nBannerSeq, setNBannerSeq] = useState<{ [key: number]: string }>({})\r\n  const [isnBannerUpdateurl, setIsBannerUpdateurl] = useState<{ [key: number]: boolean }>({});\r\n  const [nBannerurl, setNBannerurl] = useState<{ [key: number]: string }>({})\r\n  const [currentRowId, setCurrentRowId] = useState<number | null>(null);\r\n\r\n  const uploadFetcher = useFetcher<{ success: boolean; fileUrl: string; error?: string }>();\r\n  const [previewUrls, setPreviewUrls] = useState<string[]>([]);\r\n  const [uploadError, setUploadError] = useState<string | null>(null);\r\n  const fileInputRef = useRef<HTMLInputElement>(null);\r\n  const handleUpdateSeq = (id: number, val: string) => {\r\n    setNBannerSeq((prev) => ({ ...prev, [id]: val }));\r\n  };\r\n\r\n\r\n  const handleSaveSeq = (seqId: number, row: NetworkBanner) => {\r\n    const formData = new FormData();\r\n    formData.append(\"actionType\", \"bannerSequence\")\r\n    formData.append(\"bannerData\", JSON.stringify(row))\r\n    formData.append(\"seQuenceId\", nBannerSeq[seqId].toString())\r\n    fetcher.submit(formData, { method: \"PUT\" })\r\n    setIsBannerUpdate((prev) => ({ ...prev, [seqId]: false }));\r\n  }\r\n  const handleSaveUrl = (seqId: number, row: NetworkBanner) => {\r\n    const formData = new FormData();\r\n    formData.append(\"actionType\", \"updateBannerUrl\")\r\n    formData.append(\"bannerData\", JSON.stringify(row))\r\n    formData.append(\"bannerUrl\", nBannerurl[seqId].toString())\r\n    fetcher.submit(formData, { method: \"PUT\" })\r\n    setIsBannerUpdate((prev) => ({ ...prev, [seqId]: false }));\r\n  }\r\n\r\n  useEffect(() => {\r\n    if (uploadFetcher.data && currentRowId !== null) { // Ensure currentRowId is not null\r\n      if (uploadFetcher.data.error) {\r\n        setUploadError(uploadFetcher.data.error);\r\n      } else if (uploadFetcher.data.fileUrl) {\r\n        const uploadedUrl = uploadFetcher.data.fileUrl;\r\n\r\n        setNBannerurl((prev) => ({\r\n          ...prev,\r\n          [currentRowId!]: uploadedUrl, // `!` asserts it's not null\r\n        }));\r\n\r\n        setPreviewUrls((prev) => ({\r\n          ...prev,\r\n          [currentRowId!]: uploadedUrl, // `!` asserts it's not null\r\n        }));\r\n\r\n        setUploadError(null);\r\n        if (fileInputRef.current) fileInputRef.current.value = \"\";\r\n      }\r\n    }\r\n  }, [uploadFetcher.data]); // Ensure currentRowId is included in dependencies\r\n\r\n\r\n\r\n\r\n\r\n  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>, rowId: number) => {\r\n    setCurrentRowId(rowId); // Track the row being updated\r\n    const files = Array.from(event.target.files || []);\r\n    if (files.length === 0) return;\r\n\r\n    setUploadError(null);\r\n\r\n    const MAX_FILE_SIZE = 500 * 1024; // 5MB limit\r\n    const allowedTypes = [\"image/jpeg\", \"image/png\", \"image/gif\", \"image/webp\"];\r\n\r\n    const validFile = files.find((file) => {\r\n      if (file.size > MAX_FILE_SIZE) {\r\n        setUploadError(\"File size exceeds 500kb limit.\");\r\n        return false;\r\n      }\r\n      if (!allowedTypes.includes(file.type)) {\r\n        setUploadError(\"Only JPEG, PNG, GIF, and WEBP images are allowed.\");\r\n        return false;\r\n      }\r\n      return true;\r\n    });\r\n\r\n    if (!validFile) return;\r\n\r\n    const uploadFormData = new FormData();\r\n    uploadFormData.append(\"_action\", \"uploadImage\");\r\n    uploadFormData.append(\"file\", validFile, validFile.name);\r\n\r\n    uploadFetcher.submit(uploadFormData, {\r\n      method: \"post\",\r\n      action: \"/home/<USER>\",\r\n      encType: \"multipart/form-data\",\r\n    });\r\n  };\r\n\r\n  const loading = fetcher.state !== \"idle\";\r\n\r\n\r\n  return (\r\n\r\n    <div className=\"h-full\">\r\n      <h1 className=\" mb-4 font-bold cursor-pointer\" onClick={() => navigation(\"/home/<USER>\")}> <span className=\"text-2xl\">Network Management / </span> <span className=\"text-xl\">{networkName} </span> </h1>\r\n      <Tabs value={activeTab} onValueChange={handleTabChange}>\r\n        {loading && <SpinnerLoader loading={loading} />}\r\n        <TabsList>\r\n          {!salesPermissions && <TabsTrigger value=\"NetworkConfig\">Configurations</TabsTrigger>\r\n          }\r\n          {!salesPermissions && <TabsTrigger value=\"NetworkSeller\">Sellers</TabsTrigger>\r\n          }\r\n          <TabsTrigger value=\"NetworkAreas\">Areas</TabsTrigger>\r\n          <TabsTrigger value=\"NetworkAgents\">Agents</TabsTrigger>\r\n          <TabsTrigger value=\"NetworkBanners\">NetworkBanners</TabsTrigger>\r\n\r\n\r\n        </TabsList>\r\n        <TabsContent value='NetworkSeller'>\r\n          <ResponsiveTable\r\n            headers={NetWorkSeller}\r\n            data={\r\n              networkSeller.filter(a => !a.disabled)\r\n            }\r\n            renderRow={(row) => {\r\n              return (\r\n                <tr key={row.sellerId} className=\"border-b\">\r\n                  <td className=\"py-2 px-3 text-center  whitespace-normal break-words\">{row.sellerId}</td>\r\n                  <td className=\"py-2 px-3 text-center  whitespace-normal break-words\">\r\n                    {row?.seller || \"-\"}\r\n                  </td>\r\n                  <td className=\"py-2 px-3 text-center  whitespace-normal break-words\">\r\n                    <Button\r\n                      variant=\"ghost\"\r\n                      size=\"sm\"\r\n                      className=\"text-red-500 hover:text-red-900\"\r\n                      onClick={() => handleSwitch(row?.networkSellerId)}\r\n                    >\r\n                      <Trash2 size={16} />\r\n                    </Button>\r\n                  </td>\r\n\r\n                </tr>\r\n              )\r\n            }\r\n            }\r\n          />\r\n          <Button className=\"fixed bottom-5 right-5 rounded-full\" onClick={() => setIsModalOpen(true)}>\r\n            + Add Seller\r\n          </Button>\r\n          <NetWorkModal isOpen={isModalOpen} onClose={() => setIsModalOpen(false)\r\n          } sellerList={sellerList ? sellerList.filter(a => !networkSeller.filter(a => !a.disabled).some(ns => ns.sellerId === a.id)) : []} netWorkId={networkId}\r\n          />\r\n        </TabsContent>\r\n        <TabsContent value='NetworkBanners'>\r\n          <ResponsiveTable\r\n            headers={NetworkBannerHeaders}\r\n            data={\r\n              networkBanners || []\r\n            }\r\n            renderRow={(row) => (\r\n              <tr key={row.id} className=\"border-b\">\r\n                <td className=\"py-2 px-3 text-center  whitespace-normal break-words\">\r\n                  {row?.id || \"-\"}\r\n                </td>\r\n                <td className=\"py-2 px-4 text-center whitespace-normal break-words\">\r\n                  <div className=\"flex flex-col items-center gap-3\">\r\n\r\n                    {isnBannerUpdateurl[row.id] ? (\r\n                      <div className=\"flex flex-col items-center gap-3 p-4 bg-gray-100 rounded-lg shadow-md \">\r\n\r\n                        {/* File Upload Input */}\r\n                        <input\r\n                          ref={fileInputRef}\r\n                          type=\"file\"\r\n                          accept=\"image/*\"\r\n                          onChange={(e) => handleFileSelect(e, row.id)}\r\n                          className=\"file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm \r\n                     file:font-semibold file:bg-blue-100 file:text-blue-700 \r\n                     hover:file:bg-blue-200 transition duration-200\"\r\n                        />\r\n\r\n                        {/* Image Preview Section */}\r\n                        <div className='flex flex-row gap-2'>\r\n                          {previewUrls[row.id] && (\r\n                            <div className=\"flex flex-col items-center p-2 border border-gray-300 rounded-lg bg-white shadow-sm\">\r\n                              <span className=\"text-md font-bold text-red-600\">Upload Image 1000*400 Size</span>\r\n                              <span className=\"text-sm text-gray-600\">Image Preview</span>\r\n                              <img\r\n                                src={previewUrls[row.id]}\r\n                                alt=\"Preview\"\r\n                                className=\"mt-2 rounded-md w-[250px] h-[100px] object-cover\"\r\n                              />\r\n                            </div>\r\n                          )}\r\n\r\n                          {/* Action Buttons */}\r\n                          <div className=\"flex  flex-row items-center gap-3\">\r\n                            <Save\r\n                              size={24}\r\n                              className=\"cursor-pointer text-green-600 hover:text-green-700 transition duration-200\"\r\n                              onClick={() => handleSaveUrl(row.id, row)}\r\n                            />\r\n                            <X\r\n                              size={24}\r\n                              className=\"cursor-pointer text-red-500 hover:text-red-600 transition duration-200\"\r\n                              onClick={() => {\r\n                                setIsBannerUpdateurl((prev) => ({ ...prev, [row.id]: false })); // Close input\r\n                                setPreviewUrls((prev) => ({ ...prev, [row.id]: row.bannerUrl })); // Reset preview\r\n                                setNBannerurl((prev) => ({ ...prev, [row.id]: row.bannerUrl })); // Reset stored image\r\n                                if (fileInputRef.current) fileInputRef.current.value = \"\"; // Clear file input\r\n                              }}\r\n                            />\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                    ) : (\r\n                      <div className=\"flex flex-row items-center gap-2\">\r\n                        {/* Display Image */}\r\n                        <img\r\n                          alt=\"img\"\r\n                          src={nBannerurl[row.id] || row.bannerUrl}\r\n                          className=\"rounded-lg shadow-md w-[250px] h-[100px] object-cover\"\r\n                        />\r\n\r\n                        {/* Edit Button */}\r\n                        <Pencil\r\n                          className=\"cursor-pointer text-blue-600 hover:text-blue-700 transition duration-200\"\r\n                          onClick={() => setIsBannerUpdateurl({ [row.id]: true })}\r\n                        />\r\n                      </div>\r\n                    )}\r\n\r\n                  </div>\r\n                </td>\r\n\r\n\r\n\r\n                <td className=\"py-2 px-3 text-center  whitespace-normal break-words max-w-20\">\r\n                  <div className='flex flex-row items-center justify-center gap-2'>\r\n\r\n                    {isnBannerUpdate[row.id] ?\r\n                      <>\r\n                        <Input\r\n                          type=\"number\"\r\n                          value={nBannerSeq[row.id] ?? row.sequenceId}\r\n                          onChange={(e) => handleUpdateSeq(row.id, e.target.value)}\r\n                          className=\" px-2 py-1 border border-gray-300 rounded-md\"\r\n\r\n                        />\r\n                        <Save size={24} onClick={() => handleSaveSeq(row.id, row)} className='cursor-pointer' />\r\n                        <X\r\n                          color=\"red\"\r\n                          size={24}\r\n                          className=\"cursor-pointer text-red-500\"\r\n                          onClick={() => setIsBannerUpdate({})} // Close all inputs\r\n                        />\r\n                      </> :\r\n                      <>\r\n                        {row?.sequenceId || \"-\"}\r\n                        <Pencil size={20} className='cursor-pointer self-center' onClick={() => setIsBannerUpdate({ [row.id]: true })} />\r\n                        {row.active == false && <span className='bg-orange-100 p-1 rounded-full px-2  text-red-600 font-bold'>disabled</span>}\r\n\r\n                      </>\r\n                    }\r\n\r\n\r\n                  </div>\r\n                </td>\r\n                <td className=\"py-2 px-3 text-center  whitespace-normal break-words\">\r\n\r\n                  <Button\r\n                    variant=\"ghost\"\r\n                    size=\"sm\"\r\n                    className=\"text-red-500 hover:text-red-900\"\r\n                    onClick={() => {\r\n                      if (confirm(\"Are you sure you want to delete this Network Banner?\")) {\r\n                        handleSwitchBanner(row?.id);\r\n                      }\r\n                    }}\r\n                    style={{ alignSelf: \"flex-end\" }}\r\n                  >\r\n                    <Trash2 size={16} />\r\n                  </Button>\r\n                </td>\r\n              </tr>\r\n            )}\r\n          />\r\n          {!salesPermissions && <Button className=\"fixed bottom-5 right-5 rounded-full\" onClick={() => setIsModalBannerOpen(true)}\r\n          >\r\n            + Add Banner\r\n          </Button>}\r\n\r\n          <CreateNetworkBannerModal\r\n            isOpen={isModalBannerOpen}\r\n            onClose={() => setIsModalBannerOpen(false)}\r\n            networkId={networkId}\r\n\r\n          />\r\n          {/* <CreateAgent isOpen={isModalOpen} onClose={() => setIsModalOpen(false)} NetWorkId={networkId}\r\n            sellerList={sellerList}\r\n            roles={roles}\r\n          /> */}\r\n        </TabsContent>\r\n        <TabsContent value='NetworkAgents'>\r\n          <ResponsiveTable\r\n            headers={NetworkHeaders}\r\n            data={\r\n              networkAgents || []\r\n            }\r\n            renderRow={(row) => (\r\n              <tr key={row.agentUserId} className=\"border-b\">\r\n                <td className=\"py-2 px-3 text-center  whitespace-normal break-words\">{row.agentUserId}</td>\r\n\r\n\r\n                <td className=\"py-2 px-3 text-center  whitespace-normal break-words\">\r\n                  {row?.fullName || \"-\"}\r\n                </td>\r\n                <td className=\"py-2 px-3 text-center  whitespace-normal break-words\">\r\n                  {row?.businessName || \"-\"}\r\n                </td>\r\n                <td className=\"py-2 px-3 text-center\">\r\n                  <div className=\"flex items-center justify-center space-x-2\">\r\n                    <Switch\r\n                      checked={!(agentStauts[row.agentUserId] || row.status)}\r\n                      onCheckedChange={() => handleToggleStatus(row.agentUserId, agentStauts[row.agentUserId])}\r\n                    />\r\n\r\n\r\n                  </div>\r\n                </td>\r\n              </tr>\r\n            )}\r\n          />\r\n          {!salesPermissions && <Button className=\"fixed bottom-5 right-5 rounded-full\" onClick={() => setIsModalOpen(true)}>\r\n            + Add Agent\r\n          </Button>}\r\n          <CreateAgent isOpen={isModalOpen} onClose={() => setIsModalOpen(false)} NetWorkId={networkId}\r\n            sellerList={sellerList}\r\n            roles={roles}\r\n          />\r\n        </TabsContent>\r\n      </Tabs>\r\n      {activeTab === \"NetworkConfig\" && (networkConfig\r\n        ? (<NetWorkConfig networkConfig={networkConfig} networkName={networkName} onAttributeUpdate={handleUpdate} />\r\n        ) : (<div className=\"flex items-center justify-center h-full\">\r\n          <p className=\"text-red-500\">Unable to find network configurations.</p>\r\n        </div>))}\r\n      {activeTab === \"NetworkAreas\" && (\r\n        <LoadScript googleMapsApiKey={googleMapsApiKey}>\r\n          <div className=\"flex w-full h-[87vh] border rounded-xl border-neutral-200 my-4\">\r\n            <div className=\"flex flex-col gap-4 w-[20vw] overflow-auto bg-white shadow rounded-xl p-3\">\r\n              {dataleng && networkAreas ? (\r\n                networkAreas?.map((network, index) => (\r\n                  <NetworkAreaCard\r\n                    networkAreaDetail={network}\r\n                    agentDetails={networkAgents}\r\n                    networkId={networkId}\r\n                    color={getPolygonColor(index)} netWorks={networkAreas}\r\n                    isSalesPermission={salesPermissions}\r\n\r\n                  />\r\n                ))\r\n              ) : (\r\n                <div className=\"h-24 text-center\">\r\n                  No Network Areas found.\r\n                </div>\r\n              )}\r\n            </div>\r\n\r\n            <div className=\"relative flex-1 h-full bg-white\">\r\n              <div className='absolute flex z-10 top-0 left-0 w-full justify-between p-3 max-h-full'>\r\n                {!salesPermissions && !isAddLocalityClicked ? (<div className='p-3 h-fit bg-white rounded-xl flex gap-2 text-blue-600 items-center shadow'>\r\n                  <button\r\n                    onClick={() => { handleAddLocalityClicked(true); resetNewLocalities() }}\r\n                  >\r\n                    + &nbsp; Add New Locality\r\n                  </button>\r\n                </div>) : (!salesPermissions && <div className='p-3 bg-white rounded-xl flex flex-col gap-2  items-center shadow '>\r\n\r\n                  <div className='flex w-full justify-between'>\r\n                    Add Localities\r\n                    <button onClick={() => { handleAddLocalityClicked(false); resetNewLocalities(); }}><X size={16} /></button>\r\n                  </div>\r\n                  <div className='flex w-full p-1 items-center gap-3'>\r\n\r\n                    <Select value={selectedState} onValueChange={setSelectedState}>\r\n                      <SelectTrigger className=\"w-[180px]\">\r\n                        <SelectValue placeholder=\"Select state\" />\r\n                      </SelectTrigger>\r\n                      <SelectContent>\r\n                        {stateList.map((state) => (\r\n                          <SelectItem key={state} value={state}>{state}</SelectItem>\r\n                        ))}\r\n                      </SelectContent>\r\n                    </Select>\r\n                    <Select value={selectedDistrict} onValueChange={(newDistrict) => {\r\n                      setSelectedDistrict(newDistrict); // Update selected district\r\n                      handleMasterLocalitiesClicked(userId, selectedState, newDistrict); // Fetch data\r\n                      resetNewLocalities(); // Reset localities\r\n                    }} disabled={!selectedState}>\r\n                      <SelectTrigger className=\"w-[180px]\">\r\n                        <SelectValue placeholder=\"Select District\" />\r\n                      </SelectTrigger>\r\n                      <SelectContent>\r\n                        {statesAndDistricts\r\n                          ?.filter((abc) => abc.state === selectedState)\r\n                          .map((district) => (\r\n                            <SelectItem key={district.district} value={district.district}>\r\n                              {district.district}\r\n                            </SelectItem>\r\n                          ))}\r\n                      </SelectContent>\r\n                    </Select>\r\n\r\n\r\n                  </div>\r\n                  <div className='flex flex-col gap-2 self-start max-h-full w-full overflow-auto p-2 '>\r\n                    {masterLocalities && masterLocalities.length > 0 ? (\r\n                      (masterLocalities.filter(a => networkAreas?.some(na => na.areaId != a.id)).length > 0 ? masterLocalities.filter(a => !networkAreas?.some(na => na.areaId === a.id)) : masterLocalities).map((locality, index) => (\r\n                        <>\r\n                          <div key={locality.id}>\r\n                            <label className=\"cursor-pointer flex items-center gap-2 p-1\" htmlFor={`locality-${locality.id}`}>\r\n                              <Checkbox\r\n                                id={`locality-${locality.id}`}\r\n                                checked={newLocalities.some((abc) => abc.id === locality.id)}\r\n                                onClick={() => handleNewLocalities(locality.id, locality)}\r\n                              />\r\n                              <div className='flex flex-col gap-2'>\r\n                                <span>{locality.id} - {locality.name}</span>\r\n                                {newLocalities.some((abc) => abc.id === locality.id) && (\r\n                                  <Select\r\n\r\n                                    value={\r\n                                      newLocalities2.find((loc) => loc.id === locality.id)?.agentUserId || \"\"\r\n                                    }\r\n\r\n                                    onValueChange={(newDistrict) => {\r\n                                      handleNewLocalities2(locality.id, locality, networkId, newDistrict);\r\n\r\n                                    }} disabled={!selectedState}>\r\n                                    <SelectTrigger className=\"w-[280px]\">\r\n                                      <SelectValue placeholder=\"Select sales agent\" />\r\n                                    </SelectTrigger>\r\n                                    <SelectContent>\r\n                                      {networkAgents?.map((district) => (\r\n                                        <SelectItem key={district.agentUserId} value={district.agentUserId.toString()}>\r\n                                          {district.agentUserId} - {district.fullName}\r\n                                        </SelectItem>\r\n                                      ))}\r\n                                    </SelectContent>\r\n                                  </Select>\r\n                                )}\r\n                              </div>\r\n                            </label>\r\n                          </div>\r\n                          {index < masterLocalities.length - 1 && (<div className='border-b border-neutral-200'></div>)} </>\r\n                      ))\r\n                    ) : (\r\n                      <div>No Localities Fetched yet</div>\r\n                    )} </div>\r\n                  <form method=\"post\" encType=\"multipart/form-data\" onSubmit={handleSubmit}>\r\n\r\n                    <input type=\"hidden\" name=\"localities\" value={JSON.stringify(newLocalities2)} />\r\n\r\n                    <button\r\n                      type=\"submit\"\r\n                      disabled={newLocalities2.length === 0}\r\n                      className=\"px-4 py-2 bg-primary text-white rounded disabled:bg-neutral-500\"\r\n                    >\r\n                      Submit Localities\r\n                    </button>\r\n                  </form>\r\n                </div>)}\r\n                {!isLocateShopClicked ? (<div className='p-3 bg-white  h-fit rounded-xl flex gap-2 text-blue-600 items-center shadow'>\r\n                  <button\r\n                    onClick={() => handleLocateShopClicked(true)}\r\n                  >\r\n                    📍 &nbsp; Locate a shop\r\n                  </button>\r\n                </div>) :\r\n                  (<div className='p-3 bg-white rounded-xl flex flex-col gap-2  items-center shadow'>\r\n\r\n                    <div className='flex w-full justify-between'>\r\n                      Locate a shop\r\n                      <button onClick={() => handleLocateShopClicked(false)}><X size={16} /></button>\r\n                    </div>\r\n                    <div className='flex w-full p-1 items-center justify-between'>\r\n                      <p>Latitude : </p>\r\n                      <input type='text' className='border border-neutral-400 rounded-md p-1'\r\n\r\n                        onChange={(e) => setLatitude(parseFloat(e.target.value))} />\r\n                    </div>\r\n                    <div className='flex gap-1 p-1 items-center'>\r\n                      <p>Longitute : </p>\r\n                      <input type='text' className='border border-neutral-400 rounded-md p-1'\r\n\r\n                        onChange={(e) => setLongitude(parseFloat(e.target.value))}\r\n                      />\r\n                    </div>\r\n                    <button className='text-primary border border-primary p-2 rounded-md'\r\n                      onClick={() => handleFindLocation(latitude, longitude, true)}\r\n                    >\r\n                      📍 &nbsp; Find Location\r\n                    </button>\r\n                  </div>)\r\n                }\r\n              </div>\r\n              {!mapLoaded && (\r\n                <div className=\"flex items-center justify-center h-full\">\r\n                  <div className=\"loader\">Loading Map...</div>\r\n                </div>\r\n              )}\r\n              {googleMapsApiKey ? (\r\n                <GoogleMap\r\n                  mapContainerStyle={MAP_CONTAINER_STYLE}\r\n                  center={BANGALORE_CENTER}\r\n                  zoom={11}\r\n                  onLoad={(mapInstance) => {\r\n                    onLoad(mapInstance);\r\n                    setMapLoaded(true); // Set map as loaded when onLoad is called\r\n                  }}\r\n                  onUnmount={() => {\r\n                    onUnmount();\r\n                    setMapLoaded(false); // Reset when the map is unmounted\r\n                  }}\r\n                  options={{\r\n                    styles: [{\r\n                      featureType: \"all\",\r\n                      elementType: \"geometry.fill\",\r\n                      stylers: [{ visibility: \"on\" }]\r\n                    }],\r\n                    mapTypeControl: false,\r\n                    streetViewControl: false,\r\n                    fullscreenControl: false,\r\n                    clickableIcons: false,        // Disable default map icons (e.g., POIs)\r\n                    gestureHandling: \"auto\",      // Allow zoom and scroll interactions\r\n                  }}\r\n                >\r\n                  {mapLoaded ? (networkAreas?.filter((abc) => visibleAreas.has(abc.networkAreaId))?.map((area, index) => (\r\n                    area.encodedPolygon\r\n                    && (\r\n\r\n                      <Polygon\r\n                        key={area.networkAreaId}\r\n                        paths={decodePolygon(area.encodedPolygon)}\r\n                        options={{\r\n                          fillColor: isAddLocalityClicked ? \"#3b82f6\" : getPolygonColor(index),\r\n                          fillOpacity: 0.2,\r\n                          strokeColor: isAddLocalityClicked ? \"#3b82f6\" : getPolygonColor(index),\r\n                          strokeOpacity: 1,\r\n                          strokeWeight: 2,\r\n                          draggable: false,\r\n                          editable: false,\r\n                          geodesic: false,\r\n                          zIndex: 1,\r\n                          clickable: true, // Ensure polygons remain clickable\r\n                        }}\r\n                        onClick={() => handleMarkerClick(area.networkAreaId, area)}\r\n\r\n                      />\r\n                    )\r\n                  ))) : null}\r\n                  {mapLoaded ? (newLocalities?.map((area, index) => (\r\n                    area.polygon\r\n                    && (\r\n\r\n                      <Polygon\r\n                        key={area.id}\r\n                        paths={decodePolygon(area.polygon)}\r\n                        options={{\r\n                          fillColor: \"#10b981\",\r\n                          fillOpacity: 0.4,\r\n                          strokeColor: \"#10b981\",\r\n                          strokeOpacity: 1,\r\n                          strokeWeight: 2,\r\n                          draggable: false,\r\n                          editable: false,\r\n                          geodesic: false,\r\n                          zIndex: 10,\r\n                          clickable: false, // Ensure polygons remain clickable\r\n                        }}\r\n                      />\r\n                    )\r\n                  ))) : null}\r\n\r\n                  {infoWindowShown && infoWindowShown.areaId && infoWindowShown.isShown && infoWindowShown.selectedAreaDetails && (\r\n\r\n                    <>\r\n                      {console.log(\"entered infowindow params with ##########\", infoWindowShown)}\r\n                      <InfoWindow\r\n                        position={getPolygonCenter(decodePolygon(infoWindowShown.selectedAreaDetails.encodedPolygon))}\r\n                        onCloseClick={() => setInfoWindowShown({ isShown: false, areaId: null, selectedAreaDetails: null })}\r\n                        options={{\r\n                          headerDisabled: true,\r\n                          minWidth: 200,\r\n                          disableAutoPan: true,\r\n                        }}\r\n                      >\r\n                        <div className=\"flex flex-col gap-2 overflow-hidden \">\r\n                          <div className='flex justify-between w-full align-middle items-center'>\r\n                            <h2 className='text-md font-semibold text-typography-300'>Locality Info</h2>\r\n                            <button className=\"inline-flex items-center gap-1 hover:text-blue-800\"\r\n                              onClick={() => setInfoWindowShown({ isShown: false, areaId: null, selectedAreaDetails: null })}>\r\n                              <X className=\"h-5 w-5\" />\r\n                            </button>\r\n                          </div>\r\n                          <div className=\"flex flex-col gap-1 text-sm font-medium text-typography-700 w-[calc(100%-1rem)]\">\r\n                            <p><span className='text-sm text-typography-300 font-thin'>Area id:</span> {infoWindowShown.selectedAreaDetails?.networkAreaId}</p>\r\n                            <p><span className='text-sm text-typography-300 font-thin'>Area Name: </span> {infoWindowShown.selectedAreaDetails?.networkAreaName}</p>\r\n                            <p><span className='text-sm text-typography-300 font-thin'>Sales Agent:</span> {infoWindowShown.selectedAreaDetails?.agentName}</p>\r\n                          </div>\r\n                        </div>\r\n                      </InfoWindow>\r\n                      <style>\r\n                        {`.info-window-wrapper {\r\n                                  padding: 10px; \r\n                                }\r\n                              \r\n                                .gm-style-iw { \r\n                                  padding: 12px !important; \r\n                                }\r\n                                .gm-style-iw-d { \r\n                                  padding: 0px !important; \r\n                                  overflow:hidden !important;\r\n                                }`}\r\n                      </style> </>\r\n                  )}\r\n                  {showMarker && pointerLocation && pointerLocation.latitude !== null &&\r\n                    pointerLocation.longitude !== null && (\r\n                      <><Marker\r\n                        position={{\r\n                          \"lat\": pointerLocation.latitude,\r\n                          \"lng\": pointerLocation.longitude\r\n                        }}\r\n                      /></>)}\r\n                </GoogleMap>\r\n              ) : (\r\n                <div className=\"flex items-center justify-center h-full\">\r\n                  <p className=\"text-red-500\">Google Maps API key is missing.</p>\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n        </LoadScript>)}\r\n    </div>\r\n  );\r\n}\r\n\r\n"], "names": ["useState", "useCallback", "networkId", "agentUserId", "areaId", "jsxs", "jsx", "Fragment", "useEffect", "useRef", "BANGALORE_CENTER", "lat", "lng", "MAP_CONTAINER_STYLE", "width", "height", "getPolygonColor", "index", "colors", "length", "NetworkDetailsPage", "googleMapsApiKey", "networkName", "activeTab", "networkAreas", "statesAndDistricts", "networkConfig", "networkBanners", "networkAgents", "url", "userId", "roles", "networkSeller", "sellerList", "permissions", "useLoaderData", "dataleng", "fetchfor", "useNavigate", "handleTabChange", "newTab", "console", "log", "visible<PERSON><PERSON><PERSON>", "setVisibleAreas", "Set", "isLoading", "setIsLoading", "map", "setMap", "mapLoaded", "setMapLoaded", "onLoad", "mapInstance", "stateList", "for<PERSON>ach", "area", "push", "state", "latitude", "setLatitude", "longitude", "setLongitude", "showMarker", "setShowMarker", "isLocateShopClicked", "setisLocateShopClicked", "isAddLocalityClicked", "setisAddLocalityClicked", "localityEditMode", "setlocalityEditMode", "selectedState", "setSelectedState", "selectedDistrict", "setSelectedDistrict", "masterLocalities", "setMasterLocalities", "pointerLocation", "setPointerLocation", "infoWindowShown", "setInfoWindowShown", "isShown", "selectedAreaDetails", "newLocalities", "setNewLocalities", "newLocalities2", "setNewLocalities2", "handleFindLocation", "long", "alert", "salesPermissions", "includes", "handleNewLocalities", "id", "localityDetails", "prevState", "some", "abc", "filter", "resetNewLocalities", "handleNewLocalities2", "existingIndex", "findIndex", "loc", "localities", "handleUpdate", "attribute", "value", "domainId", "formData", "FormData", "append", "toString", "fetcher", "submit", "method", "showToast", "replace", "error", "handleMarkerClick", "areaDetails", "useToast", "updateToggle", "sellerId", "data", "handleLocateShopClicked", "handleAddLocalityClicked", "useSubmit", "handleSubmit", "event", "preventDefault", "transformed", "item", "parseInt", "areas", "JSON", "stringify", "Object", "fromEntries", "encType", "handleMasterLocalitiesClicked", "district", "response", "fetch", "json", "ok", "Error", "decodedPolygons", "networkAreaId", "paths", "encodedPolygon", "decodePolygon", "polygon", "onUnmount", "getPolygonCenter", "latitudes", "path", "longitudes", "latSum", "reduce", "a", "b", "lngSum", "NetworkHeaders", "NetworkBannerHeaders", "isModalOpen", "setIsModalOpen", "isModalBannerOpen", "setIsModalBannerOpen", "navigation", "NetWorkSeller", "useFetcher", "toggledNetWork", "setToggleNetwork", "acc", "seller", "networkSellerId", "disabled", "handleSwitch", "prev", "handleSwitchBanner", "bannerId", "agentStauts", "setAgentStatus", "handleToggleStatus", "agentId", "currentStatus", "newStatus", "isnBannerUpdate", "setIsBannerUpdate", "nBannerSeq", "setNBannerSeq", "isnBannerUpdateurl", "setIsBannerUpdateurl", "nBannerurl", "setNBannerurl", "currentRowId", "setCurrentRowId", "uploadFetcher", "previewUrls", "setPreviewUrls", "uploadError", "setUploadError", "fileInputRef", "handleUpdateSeq", "val", "handleSaveSeq", "seqId", "row", "handleSaveUrl", "fileUrl", "uploadedUrl", "current", "handleFileSelect", "rowId", "files", "Array", "from", "target", "MAX_FILE_SIZE", "allowedTypes", "validFile", "find", "file", "size", "type", "uploadFormData", "name", "action", "loading", "className", "children", "onClick", "Tabs", "onValueChange", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TabsList", "TabsTrigger", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ResponsiveTable", "headers", "renderRow", "<PERSON><PERSON>", "variant", "Trash2", "NetWorkModal", "isOpen", "onClose", "ns", "netWorkId", "ref", "accept", "onChange", "e", "src", "alt", "Save", "X", "bannerUrl", "Pencil", "Input", "sequenceId", "color", "active", "confirm", "style", "alignSelf", "CreateNetworkBannerModal", "fullName", "businessName", "Switch", "checked", "status", "onCheckedChange", "CreateAgent", "NetWorkId", "NetWorkConfig", "onAttributeUpdate", "LoadScript", "network", "NetworkAreaCard", "networkAreaDetail", "agentDetails", "netWorks", "isSalesPermission", "Select", "SelectTrigger", "SelectValue", "placeholder", "SelectContent", "SelectItem", "newDistrict", "na", "locality", "htmlFor", "Checkbox", "onSubmit", "parseFloat", "GoogleMap", "mapContainerStyle", "center", "zoom", "options", "styles", "featureType", "elementType", "stylers", "visibility", "mapTypeControl", "streetViewControl", "fullscreenControl", "clickableIcons", "<PERSON><PERSON><PERSON><PERSON>", "has", "Polygon", "fillColor", "fillOpacity", "strokeColor", "strokeOpacity", "strokeWeight", "draggable", "editable", "geodesic", "zIndex", "clickable", "InfoWindow", "position", "onCloseClick", "headerDisabled", "min<PERSON><PERSON><PERSON>", "disableAutoPan", "networkAreaName", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA,MAAM,iBAAiB,iBAAiB,kBAAkB;AAAA,EACxD,CAAC,QAAQ,EAAE,GAAG,mCAAmC,KAAK,SAAQ,CAAE;AAAA,EAChE,CAAC,QAAQ,EAAE,GAAG,kBAAkB,KAAK,SAAU,CAAA;AACjD,CAAC;ACMD,MAAM,kBAAkD,CAAC;AAAA,EACrD;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAGJ,MAAM;AACI,QAAA,CAAC,eAAe,gBAAgB,IAAIA,aAAA;AAAA,IAAkC,OACvE,YAAY,IAAI,OAAO,CAAC,KAAK,YAAY;AAClC,WAAA,mCAAS,mBAAkB,QAAW;AACtC,YAAI,QAAQ,aAAa,KAAI,mCAAS,aAAY;AAAA,MAAA;AAE/C,aAAA;AAAA,IAAA,GACR,CAA6B,CAAA;AAAA,EACpC;AACA,QAAM,CAAC,QAAQ,SAAS,IAAIA,aAAAA,SAAiB,CAAC;AAC9C,QAAM,CAAC,eAAe,gBAAgB,IAAIA,aAAAA,SAAiB,CAAC;AAC5D,QAAM,CAAC,aAAa,cAAc,IAAIA,aAAAA,SAAiB,CAAC;AACxD,QAAM,uBAAuBC,aAAA;AAAA,IACzB,CAAC,IAAaC,YAAoBC,iBAAyB;AACnD,UAAA,MAAMD,cAAaC,cAAa;AAChC,kBAAU,EAAE;AACZ,yBAAiBD,UAAS;AACX,uBAAA,WAAWC,YAAW,CAAC;AAAA,MAAA;AAAA,IAE9C;AAAA,IAAG,CAAA;AAAA,EAAE;AAET,QAAM,CAAC,cAAc,eAAe,IAAIH,aAAAA,SAAiB,CAAC;AAC1D,QAAM,UAAU,WAAW;AAErB,QAAA,sBAAsB,OAAO,kBAA0B;AACzD,qBAAiB,CAAC,UAAU;AAAA,MACxB,GAAG;AAAA,MACH,CAAC,aAAa,GAAG,CAAC,KAAK,aAAa;AAAA,IAAA,EACtC;AACI,UAAA,WAAW,IAAI,SAAS;AAC9B,aAAS,OAAO,WAAW,cAAc,SAAA,CAAU;AAC1C,aAAA,OAAO,cAAc,yBAAyB;AACvD,YAAQ,OAAO,UAAU,EAAE,QAAQ,OAAO;AAAA,EAC9C;AAEA,QAAM,oBAAoB,YAAY;AAE5B,UAAA,WAAW,IAAI,SAAS;AAC9B,aAAS,OAAO,gBAAgB,OAAO,SAAA,CAAU;AACjD,aAAS,OAAO,iBAAiB,cAAc,SAAA,CAAU;AACzD,aAAS,OAAO,eAAe,YAAY,SAAA,CAAU;AAC5C,aAAA,OAAO,cAAc,uBAAuB;AAC7C,YAAA,IAAI,mCAAmC,QAAQ;AACvD,eAAW,CAAC,KAAK,KAAK,KAAK,SAAS,WAAW;AACnC,cAAA,IAAI,KAAK,KAAK;AAAA,IAAA;AAE1B,YAAQ,OAAO,UAAU,EAAE,QAAQ,QAAQ;AAC3C,oBAAgB,CAAC;AAAA,EACrB;AAEM,QAAA,sBAAsBC,yBAAY,CAACG,YAAmB;AACxD,oBAAgBA,OAAM;AAAA,EAC1B,GAAG,EAAE;AACC,QAAA,2BAA2BH,aAAAA,YAAY,MAAM;AAC/C,oBAAgB,CAAC;AAAA,EACrB,GAAG,EAAE;AAGD,SAAAI,kCAAA,KAAC,OAAI,EAAA,WAAU,6DACX,UAAA;AAAA,IAACA,kCAAAA,KAAA,OAAA,EAAI,WAAU,yDACX,UAAA;AAAA,MAACA,kCAAAA,KAAA,OAAA,EAAI,WAAU,uBACX,UAAA;AAAA,QAACA,kCAAAA,KAAA,OAAA,EAAI,WAAU,6CAA6C,UAAA;AAAA,UAAA;AAAA,UAClD,kBAAkB;AAAA,QAAA,GAC5B;AAAA,QACCC,kCAAA,IAAA,OAAA,EAAI,WAAU,6CACV,4BAAkB,gBACvB,CAAA;AAAA,MAAA,GACJ;AAAA,MACC,SAAUA,kCAAAA,IAAC,QAAO,EAAA,MAAM,QAAQ,MAAM,GAAG,KAAK,IAAI,OAAO,GAAG,KAAK,GAAI,CAAA;AAAA,IAAA,GAC1E;AAAA,IACAD,kCAAAA,KAAC,OAAI,EAAA,WAAU,8DACV,UAAA;AAAA,MAAA,kBAAkB,aAAcA,uCAAC,OAAI,EAAA,WAAU,UAAS,UAAA;AAAA,QAAA;AAAA,QACrD,iBAAiB,kBAAkB,gBAE3BC,kCAAA,IAAAC,kBAAA,UAAA,EAAA,UAAAF,kCAAA;AAAA,UAAC;AAAA,UAAA;AAAA,YACG,OAAO,YAAY,SAAS;AAAA,YAC5B,eAAe,CAAC,gBAAgB;AACP,mCAAA,kBAAkB,QAAQ,WAAW,WAAW;AAAA,YAEzE;AAAA,YACA,UAAA;AAAA,cAAAC,sCAAC,eACG,EAAA,UAAAA,kCAAA,IAAC,aAAY,EAAA,aAAY,yBAAyB,CAAA,GACtD;AAAA,cACCA,kCAAA,IAAA,eAAA,EACI,UAAc,6CAAA,IAAI,CAAC,aACfD,kCAAA,KAAA,YAAA,EAAsC,OAAO,SAAS,YAAY,SAC9D,GAAA,UAAA;AAAA,gBAAS,SAAA;AAAA,gBAAY;AAAA,gBAAI,SAAS;AAAA,cAAA,KADtB,SAAS,WAE1B,GAER,CAAA;AAAA,YAAA;AAAA,UAAA;AAAA,QACJ,EAAA,CACJ,IACGA,kCAAAA,KAAAE,kBAAA,UAAA,EAAA,UAAA;AAAA,UAAA;AAAA,UAAED,kCAAA,IAAA,QAAA,EAAK,WAAU,yCAAwC,UAAc,kBAAA;AAAA,UACzE,kBAAkB;AAAA,UAAU;AAAA,QAAA,EAAC,CAAA;AAAA,MAAA,GAAK;AAAA,MAC1C,CAAC,qBAAsBA,kCAAA,IAAA,OAAA,EAAI,WAAU,cACjC,UAAA,iBAAiB,kBAAkB,gBAChCD,kCAAA,KAAAE,kBAAA,UAAA,EAAA,UAAA;AAAA,QAAAD,kCAAA;AAAA,UAAC;AAAA,UAAA;AAAA,YACG,SAAQ;AAAA,YACR,MAAK;AAAA,YACL,WAAU;AAAA,YACV,SAAS,MAAM,kBAAkB;AAAA,YACjC,UAAU,gBAAgB;AAAA,YAE1B,UAAAA,kCAAAA,IAAC,gBAAe,EAAA,MAAM,GAAI,CAAA;AAAA,UAAA;AAAA,QAC9B;AAAA,QACAA,kCAAA;AAAA,UAAC;AAAA,UAAA;AAAA,YACG,SAAQ;AAAA,YACR,MAAK;AAAA,YACL,WAAU;AAAA,YACV,SAAS,MAAM,yBAAyB;AAAA,YAExC,UAAAA,kCAAAA,IAAC,GAAE,EAAA,MAAM,GAAI,CAAA;AAAA,UAAA;AAAA,QACjB;AAAA,QAAS;AAAA,MAAA,EAAA,CAAC,IAGND,kCAAAA,KAAAE,kBAAA,UAAA,EAAA,UAAA;AAAA,QAAAD,kCAAA;AAAA,UAAC;AAAA,UAAA;AAAA,YACG,SAAQ;AAAA,YACR,MAAK;AAAA,YACL,WAAU;AAAA,YACV,SAAS,MAAM,oBAAoB,kBAAkB,aAAa;AAAA,YAElE,UAAAA,kCAAAA,IAAC,QAAO,EAAA,MAAM,GAAI,CAAA;AAAA,UAAA;AAAA,QACtB;AAAA,QACAA,kCAAA;AAAA,UAAC;AAAA,UAAA;AAAA,YACG,SAAQ;AAAA,YACR,MAAK;AAAA,YACL,WAAU;AAAA,YACV,SAAS,MAAM,oBAAoB,kBAAkB,aAAa;AAAA,YAElE,UAAAA,kCAAAA,IAAC,QAAO,EAAA,MAAM,GAAI,CAAA;AAAA,UAAA;AAAA,QAAA;AAAA,MACtB,EAAA,CAAS,EAKrB,CAAA;AAAA,IAAA,EACJ,CAAA;AAAA,EAAA,GACJ;AAER;AC/IA,SAAwB,YAAY,EAAE,QAAQ,SAAS,WAAW,OAAO,cAAiC;AACpG,QAAM,UAAU,WAAW;AAC3B,QAAM,CAAC,WAAW,YAAY,IAAIN,aAAAA,SAAsB,EAAE;AAE1D,QAAM,CAAC,UAAU,WAAW,IAAIA,sBAAS;AAAA,IACnC,WAAW;AAAA,IACX,UAAU;AAAA,IACV,OAAO;AAAA,IACP,cAAc;AAAA,IACd,SAAS;AAAA,IACT,UAAU;AAAA,IACV,YAAY;AAAA,IACZ,OAAO,CAAA;AAAA,EAAC,CACb;AAEK,QAAA,eAAe,CAAC,MAA2C;AAG3D,UAAM,EAAE,MAAM,MAAM,IAAI,EAAE;AAC1B,gBAAY,CAAC,UAAU;AAAA,MACjB,GAAG;AAAA,MACH,CAAC,IAAI,GAAG;AAAA,IAAA,EACZ;AAAA,EACR;AAaM,QAAA,YAAY,QAAQ,UAAU;AAC9B,QAAA,EAAE,UAAU,IAAI,SAAS;AAE/BQ,eAAAA,UAAU,MAAM;AACN,QAAA,QAAQ,QAAQ,WAAW,MAAM;AAG/B,UAAI,QAAQ,SAAS,UAAa,WAAW,MAAM;AAC7C,kBAAU,6BAA6B,SAAS;AACxC,gBAAA;AACI,oBAAA;AAAA,UACN,WAAW;AAAA,UACX,UAAU;AAAA,UACV,OAAO;AAAA,UACP,cAAc;AAAA,UACd,SAAS;AAAA,UACT,UAAU;AAAA,UACV,YAAY;AAAA,UACZ,OAAO,CAAA;AAAA,QAAC,CACb;AAAA,MAAA,WAEE,QAAQ,SAAS,QAAW;AAC/B,kBAAU,wBAAwB,OAAO;AAAA,MAAA;AAAA,IAC/C;AAAA,EACN,GAGH,CAAC,QAAQ,IAAI,CAAC;AAGX,QAAA,eAAe,CAAC,MAAuB;AACvC,MAAE,eAAe;AACX,UAAA,OAAO,IAAI,SAAS;AACnB,WAAA,QAAQ,QAAQ,EAAE,QAAQ,CAAC,CAAC,KAAK,KAAK,MAAM;AAC7C,WAAK,OAAO,KAAK,MAAM,QAAQ,KAAK,IAAI,MAAM,KAAK,GAAG,IAAI,MAAM,SAAA,CAAU;AAAA,IAAA,CAC/E;AACD,SAAK,OAAO,aAAa,UAAU,SAAA,CAAU;AACxC,SAAA,OAAO,WAAW,aAAa;AAC/B,SAAA,OAAO,SAAS,WAAW;AAChC,YAAQ,OAAO,MAAM,EAAE,QAAQ,QAAQ;AAAA,EAC7C;AAEA,SACOH,kCAAAA,KAAA,QAAA,EAAO,MAAM,QAAQ,cAAc,SAC9B,UAAA;AAAA,IAACC,kCAAAA,IAAA,eAAA,EAAc,SAAS,UAAW,CAAA;AAAA,2CAClC,eACM,EAAA,UAAA;AAAA,MACK,aAAAA,kCAAAA,IAAC,SAAI,WAAU,kFACT,gDAAC,eAAc,EAAA,SAAS,WAAW,EACzC,CAAA;AAAA,MAENA,kCAAAA,IAAC,eAAY,UAAa,gBAAA,CAAA;AAAA,MACzBD,kCAAA,KAAA,QAAA,EAAK,UAAU,cAAc,WAAU,aAElC,UAAA;AAAA,QAACA,kCAAAA,KAAA,OAAA,EAAI,WAAU,cACT,UAAA;AAAA,UAACA,kCAAAA,KAAA,OAAA,EAAI,WAAU,gCACT,UAAA;AAAA,YAACC,kCAAA,IAAA,SAAA,EAAM,WAAU,2CAA0C,UAAU,cAAA;AAAA,YACpEA,kCAAAA,IAAA,OAAA,EAAM,OAAO,SAAS,WAAW,MAAK,aAAY,aAAY,cAAa,UAAU,cAAc,UAAQ,KAAC,CAAA;AAAA,UAAA,GACnH;AAAA,UACAD,kCAAAA,KAAC,OAAI,EAAA,WAAU,gCACT,UAAA;AAAA,YAACC,kCAAA,IAAA,SAAA,EAAM,WAAU,2CAA0C,UAAS,aAAA;AAAA,YACnEA,kCAAAA,IAAA,OAAA,EAAM,MAAK,YAAW,OAAO,SAAS,UAAU,aAAY,aAAY,UAAU,cAAc,UAAQ,KAAC,CAAA;AAAA,UAAA,EAChH,CAAA;AAAA,QAAA,GACN;AAAA,QAEAD,kCAAAA,KAAC,OAAI,EAAA,WAAU,cACT,UAAA;AAAA,UAACA,kCAAAA,KAAA,OAAA,EAAI,WAAU,gCACT,UAAA;AAAA,YAACC,kCAAA,IAAA,SAAA,EAAM,WAAU,2CAA0C,UAAK,SAAA;AAAA,YAChEA,kCAAA;AAAA,cAAC;AAAA,cAAA;AAAA,gBAAM,MAAK;AAAA,gBAAQ,MAAK;AAAA,gBAAQ,aAAY;AAAA,gBAAQ,UAAU;AAAA,gBAAc,UAAQ;AAAA,gBAC/E,OAAO,SAAS;AAAA,cAAA;AAAA,YAAA;AAAA,UACtB,GACN;AAAA,UACAD,kCAAAA,KAAC,OAAI,EAAA,WAAU,gCACT,UAAA;AAAA,YAACC,kCAAA,IAAA,SAAA,EAAM,WAAU,2CAA0C,UAAa,iBAAA;AAAA,YACxEA,kCAAA;AAAA,cAAC;AAAA,cAAA;AAAA,gBAAM,MAAK;AAAA,gBAAe,aAAY;AAAA,gBAAgB,MAAK;AAAA,gBAAS,UAAU,CAAC,MAAM;AAChF,wBAAM,QAAQ,EAAE,OAAO,MAAM,QAAQ,OAAO,EAAE;AAC1C,sBAAA,MAAM,UAAU,IAAI;AAClB,gCAAY,CAAC,UAAU;AAAA,sBACjB,GAAG;AAAA,sBACH,cAAc;AAAA,oBAAA,EAEnB;AAAA,kBAAA;AAAA,gBAEb;AAAA,gBAEM,OAAO,SAAS;AAAA,gBAEhB,UAAQ;AAAA,cAAA;AAAA,YAAA;AAAA,UAAC,EACrB,CAAA;AAAA,QAAA,GACN;AAAA,QAEAD,kCAAAA,KAAC,OAAI,EAAA,WAAU,gCACT,UAAA;AAAA,UAACC,kCAAA,IAAA,SAAA,EAAM,WAAU,2CAA0C,UAAO,WAAA;AAAA,UAClEA,kCAAA;AAAA,YAAC;AAAA,YAAA;AAAA,cAAM,MAAK;AAAA,cAAU,aAAY;AAAA,cAAU,UAAU;AAAA,cAAc,UAAQ;AAAA,cACtE,OAAO,SAAS;AAAA,YAAA;AAAA,UAAA;AAAA,QACtB,GACN;AAAA,QAmBAA,kCAAA,IAAC,OAAI,EAAA,WAAU,+BACT,UAAAA,kCAAAA,IAAC,QAAO,EAAA,MAAK,UAAS,SAAS,WAAY,UAAA,aAAA,CAAU,EAC3D,CAAA;AAAA,MAAA,EACN,CAAA;AAAA,IAAA,EACN,CAAA;AAAA,EAAA,GACN;AAEZ;ACtKO,SAAS,aAAa,EAAE,QAAQ,SAAS,YAAY,aAAgC;AACtF,QAAM,CAAC,UAAU,WAAW,IAAIN,aAAAA,SAAsB,EAAE;AAExD,QAAM,UAAU,WAAW;AAC3B,QAAM,aAAa,MAAM;AACb,UAAA,WAAW,IAAI,SAAS;AACrB,aAAA,OAAO,WAAW,eAAe;AACjC,aAAA,OAAO,aAAa,SAA8B;AAClD,aAAA,OAAO,YAAY,QAA6B;AACzD,YAAQ,OAAO,UAAU,EAAE,QAAQ,QAAQ;AAAA,EACjD;AAEM,QAAA,EAAE,UAAU,IAAI,SAAS;AACzB,QAAA,YAAY,QAAQ,UAAU;AAEpCQ,eAAAA,UAAU,MAAM;AACN,QAAA,QAAQ,QAAQ,WAAW,MAAM;AAC/B,UAAI,QAAQ,MAAM;AACZ,kBAAU,kCAAkC,SAAS;AAC7C,gBAAA;AAAA,MAAA,OAET;AACC,kBAAU,wBAAwB,OAAO;AAAA,MAAA;AAAA,IAE/C;AAAA,EAEN,GAEH,CAAC,QAAQ,IAAI,CAAC;AAGX,SAAAF,kCAAA,IAAC,UAAO,MAAM,QAAQ,cAAc,SAC9B,UAAAD,kCAAA,KAAC,eAAc,EAAA,WAAU,0CAClB,UAAA;AAAA,IACK,aAAAC,kCAAAA,IAAC,SAAI,WAAU,kFACT,gDAAC,eAAc,EAAA,SAAS,WAAW,EACzC,CAAA;AAAA,IAELA,sCAAA,cAAA,EACK,UAACA,kCAAA,IAAA,aAAA,EAAY,kCAAqB,CAAA,GACxC;AAAA,IACAD,kCAAAA,KAAC,OAAI,EAAA,WAAU,+BAET,UAAA;AAAA,MAAAA,kCAAA;AAAA,QAAC;AAAA,QAAA;AAAA,UACK,WAAU;AAAA,UACV,OAAO;AAAA,UACP,UAAU,CAAC,MAAM,YAAY,EAAE,OAAO,QAAQ,OAAO,EAAE,OAAO,KAAK,IAAI,EAAE;AAAA,UAEzE,UAAA;AAAA,YAACC,kCAAA,IAAA,UAAA,EAAO,OAAM,IAAG,UAAa,iBAAA;AAAA,YAC7B,WAAW,IAAI,CAAC,WACVA,kCAAAA,IAAA,UAAA,EAAuB,OAAO,OAAO,IAC/B,UAAA,OAAO,KADD,GAAA,OAAO,EAEpB,CACL;AAAA,UAAA;AAAA,QAAA;AAAA,MACP;AAAA,MAGAD,kCAAAA,KAAC,OAAI,EAAA,WAAU,0BACT,UAAA;AAAA,QAAAC,sCAAC,QAAO,EAAA,SAAQ,WAAU,SAAS,SAAS,UAE5C,UAAA;AAAA,QACAA,kCAAAA,IAAC,UAAO,SAAS,YAAY,UAAU,CAAC,UAAU,SAAS,WAAW,UAEtE,OAAA,CAAA;AAAA,MAAA,EACN,CAAA;AAAA,IAAA,EACN,CAAA;AAAA,EAAA,EAAA,CACN,EACN,CAAA;AAEZ;ACpEA,SAAwB,yBAAyB,EAAE,QAAQ,SAAS,aAAqC;AACnG,QAAM,CAAC,WAAW,YAAY,IAAIN,aAAAA,SAAiB,EAAE;AACrD,QAAM,CAAC,YAAY,aAAa,IAAIA,aAAAA,SAAiB,EAAE;AACvD,QAAM,CAAC,YAAY,aAAa,IAAIA,aAAAA,SAAsB,EAAE;AACtD,QAAA,eAAeS,oBAAyB,IAAI;AAClD,QAAM,CAAC,aAAa,cAAc,IAAIT,aAAAA,SAAwB,IAAI;AAElE,QAAM,gBAAgB,WAAkE;AAExF,QAAM,gBAAgB,WAAW;AAE3B,QAAA,mBAAmB,OAAO,UAA+C;AACzE,UAAM,QAAQ,MAAM,KAAK,MAAM,OAAO,SAAS,EAAE;AAC7C,QAAA,MAAM,WAAW,EAAG;AAExB,mBAAe,IAAI;AAEnB,UAAM,gBAAgB,MAAM;AAC5B,UAAM,eAAe,CAAC,cAAc,aAAa,aAAa,YAAY;AAE1E,UAAM,YAAY,MAAM,KAAK,CAAC,SAAS;AAC7B,UAAA,KAAK,OAAO,eAAe;AACzB,uBAAe,gCAAgC;AACxC,eAAA;AAAA,MAAA;AAEb,UAAI,CAAC,aAAa,SAAS,KAAK,IAAI,GAAG;AACjC,uBAAe,mDAAmD;AAC3D,eAAA;AAAA,MAAA;AAEN,aAAA;AAAA,IAAA,CACZ;AAED,QAAI,CAAC,UAAW;AAEV,UAAA,iBAAiB,IAAI,SAAS;AACrB,mBAAA,OAAO,WAAW,aAAa;AAC9C,mBAAe,OAAO,QAAQ,WAAW,UAAU,IAAI;AAEvD,kBAAc,OAAO,gBAAgB;AAAA,MAC/B,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,SAAS;AAAA,IAAA,CACd;AAAA,EACP;AACA,QAAM,aAAa,MAAM;AACf,QAAA,aAAa,eAAe,IAAI;AACxB,YAAA,WAAW,IAAI,SAAS;AACrB,eAAA,OAAO,aAAa,SAAS;AACtC,eAAS,OAAO,cAAc,WAAW,SAAA,CAAU;AACnD,eAAS,OAAO,aAAa,UAAU,SAAA,CAAU;AAExC,eAAA,OAAO,cAAc,qBAAqB;AACnD,oBAAc,OAAO,UAAU,EAAE,QAAQ,QAAQ;AAAA,IAAA;AAAA,EAE7D;AACA,QAAM,cAAc,MAAM;AACZ,YAAA;AAER,iBAAa,EAAE;AACf,kBAAc,EAAE;AAChB,kBAAc,EAAE;AAAA,EACtB;AAEM,QAAA,EAAE,UAAU,IAAI,SAAS;AAE/BQ,eAAAA,UAAU,MAAM;;AACV,QAAI,+CAAe,MAAM;AACf,WAAA,mBAAc,SAAd,mBAAoB,QAAQ;AAC1B,kBAAU,uCAAuC,SAAS;AAClD,gBAAA;AAAA,MAAA,OACP;AACG,cAAA,mBAAc,SAAd,mBAAoB,WAAU,OAAO;AACnC,oBAAU,yBAAyB,OAAO;AAAA,QAAA;AAAA,MAEhD;AAAA,IACN;AAAA,EACN,GACH,CAAC,cAAc,IAAI,CAAC;AAEvBA,eAAAA,UAAU,MAAM;AACV,QAAI,cAAc,MAAM;AACd,UAAA,cAAc,KAAK,OAAO;AACT,uBAAA,cAAc,KAAK,KAAK;AAAA,MAAA,WAClC,cAAc,KAAK,SAAS;AAC3B,cAAA,cAAc,cAAc,KAAK;AAEvC;AAAA,UACM;AAAA;AAAA,QACN;AAEA;AAAA,UACM;AAAA,QACN;AAEA,uBAAe,IAAI;AACnB,YAAI,aAAa,QAAsB,cAAA,QAAQ,QAAQ;AAAA,MAAA;AAAA,IAC7D;AAAA,EACN,GACH,CAAC,cAAc,IAAI,CAAC;AAGvB,+CACO,QAAO,EAAA,MAAM,QAAQ,cAAc,aAC9B,iDAAC,eACK,EAAA,UAAA;AAAA,IAAAF,kCAAAA,IAAC,eAAY,UAAqB,wBAAA,CAAA;AAAA,IAClCD,kCAAAA,KAAC,OAAI,EAAA,WAAU,uBAET,UAAA;AAAA,MAAAC,kCAAA;AAAA,QAAC;AAAA,QAAA;AAAA,UACK,KAAK;AAAA,UACL,MAAK;AAAA,UACL,QAAO;AAAA,UACP,UAAU;AAAA,UACV,WAAU;AAAA,QAAA;AAAA,MAChB;AAAA,MAEC,cACKD,kCAAA,KAAC,OAAI,EAAA,WAAU,uFACT,UAAA;AAAA,QAACC,kCAAA,IAAA,QAAA,EAAK,WAAU,kCAAiC,UAA0B,8BAAA;AAAA,QAC1EA,kCAAA,IAAA,QAAA,EAAK,WAAU,yBAAwB,UAAa,iBAAA;AAAA,8CACpD,OAAI,EAAA,KAAK,YAAY,KAAI,WAAU,WAAU,mDAAmD,CAAA;AAAA,MAAA,GACvG;AAAA,MAIL,gBAAgB,MACXA,kCAAAA,IAAC,QAAK,EAAA,WAAU,kCAAkC,UAAY,aAAA;AAAA,MAIpEA,kCAAA;AAAA,QAAC;AAAA,QAAA;AAAA,UACK,MAAK;AAAA,UACL,aAAY;AAAA,UACZ,OAAO;AAAA,UACP,UAAU,CAAC,MAAM,cAAc,EAAE,OAAO,QAAQ,OAAO,EAAE,OAAO,KAAK,IAAI,EAAE;AAAA,QAAA;AAAA,MAAA;AAAA,IACjF,GACN;AAAA,IACAD,kCAAAA,KAAC,OAAI,EAAA,WAAU,uCACT,UAAA;AAAA,MAAAA,kCAAA,KAAC,QAAO,EAAA,SAAS,aAAa,SAAQ,WAAU,UAAA;AAAA,QAACC,kCAAAA,IAAA,GAAA,EAAE,MAAM,GAAI,CAAA;AAAA,QAAE;AAAA,MAAA,GAAO;AAAA,MACtED,uCAAC,UAAO,SAAS,YAAY,UAAU,CAAC,aAAa,eAAe,IAC9D,UAAA;AAAA,QAACC,kCAAAA,IAAA,MAAA,EAAK,MAAM,GAAI,CAAA;AAAA,QAAE;AAAA,MAAA,EACxB,CAAA;AAAA,IAAA,EACN,CAAA;AAAA,EAAA,EAAA,CACN,EACN,CAAA;AAEZ;ACvGA,MAAMI,mBAAmB;AAAA,EACvBC,KAAK;AAAA,EACLC,KAAK;AACP;AAEA,MAAMC,sBAAsB;AAAA,EAC1BC,OAAO;AAAA,EACPC,QAAQ;AACV;AACA,MAAMC,kBAAmBC,WAAkB;AACnC,QAAAC,SAAS,CAAC,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,SAAS;AAC/F,SAAAA,OAAOD,QAAQC,OAAOC,MAAM;AACrC;AA+YA,SAAwBC,qBAAqB;;AAC3C,QAAM;AAAA,IAAEC;AAAAA,IAAkBnB;AAAAA,IAAWoB;AAAAA,IAAaC;AAAAA,IAAWC;AAAAA,IAAcC;AAAAA,IAAoBC;AAAAA,IAAeC;AAAAA,IAAgBC,eAAAA;AAAAA,IAAeC;AAAAA,IAAKC;AAAAA,IAAQC;AAAAA,IAAOC;AAAAA,IAAeC;AAAAA,IAAYC;AAAAA,MAAgBC,cAA0B;AAEhO,QAAAC,WAAWZ,eAAeA,aAAaL,SAASO;AACtD,QAAMW,WAAWC,YAAY;AACvB,QAAAC,kBAAmBC,YAAmB;AAClCC,YAAAC,IAAIF,QAAQ,WAAW;AAE/BH,aAAS,cAAcnC,SAAS,gBAAgBoB,WAAW,cAAckB,MAAM,EAAE;AAAA,EACnF;AAEQC,UAAAC,IAAIf,gBAAgB,gBAAgB;AAC5C,QAAM,CAACgB,cAAcC,eAAe,IAAI5C,aAAAA,SAAsB,oBAAI6C,KAAK;AACvE,QAAM,CAACC,WAAWC,YAAY,IAAI/C,aAAAA,SAAS,IAAI;AAC/C,QAAM,CAACgD,KAAKC,MAAM,IAAIjD,aAAAA,SAAiC,IAAI;AAC3D,QAAM,CAACkD,WAAWC,YAAY,IAAInD,aAAAA,SAAS,KAAK;AAC1C,QAAAoD,SAASnD,aAAY,YAACoD,iBAAiC;AAC3DJ,WAAOI,WAAW;AAClBF,iBAAa,IAAI;AAAA,EACnB,GAAG,EAAE;AACL,MAAIG,YAAsB,CAAC;AAC3B7B,2DAAoB8B,QAASC,UAASF,UAAUG,KAAKD,KAAKE,KAAK;AAU/D,QAAM,CAACC,UAAUC,WAAW,IAAI5D,aAAAA,SAAS,CAAC;AAC1C,QAAM,CAAC6D,WAAWC,YAAY,IAAI9D,aAAAA,SAAS,CAAC;AAC5C,QAAM,CAAC+D,YAAYC,aAAa,IAAIhE,aAAAA,SAAS,KAAK;AAClD,QAAM,CAACiE,qBAAqBC,sBAAsB,IAAIlE,aAAAA,SAAS,KAAK;AACpE,QAAM,CAACmE,sBAAsBC,uBAAuB,IAAIpE,aAAAA,SAAS,KAAK;AACtE,QAAM,CAACqE,kBAAkBC,mBAAmB,IAAItE,aAAAA,SAAS,KAAK;AAC9D,QAAM,CAACuE,eAAeC,gBAAgB,IAAIxE,aAAAA,SAAS,EAAE;AACrD,QAAM,CAACyE,kBAAkBC,mBAAmB,IAAI1E,aAAAA,SAAS,EAAE;AAC3D,QAAM,CAAC2E,kBAAkBC,mBAAmB,IAAI5E,aAAAA,SAA6B,CAAA,CAAE;AAC/E,QAAM,CAAC6E,iBAAiBC,kBAAkB,IAAI9E,sBAA0B;AAAA,IACtE2D,UAAU;AAAA,IACVE,WAAW;AAAA,EACb,CAAC;AAED,QAAM,CAACkB,iBAAiBC,kBAAkB,IAAIhF,sBAA0B;AAAA,IACtEI,QAAQ;AAAA,IACR6E,SAAS;AAAA,IACTC,qBAAqB;AAAA,EACvB,CAAC;AACD,QAAM,CAACC,eAAeC,gBAAgB,IAAIpF,aAAAA,SAA6B,CAAA,CAAE;AACzE,QAAM,CAACqF,gBAAgBC,iBAAiB,IAAItF,aAAAA,SAAqB,CAAA,CAAE;AACnE,QAAMuF,qBAAqBtF,aAAA,YAAY,CACrCU,KACA6E,MACAzB,gBACG;AACH,QAAIpD,OAAO6E,MAAM;AACfV,yBAAmB;AAAA,QAAEnB,UAAUhD;AAAAA,QAAKkD,WAAW2B;AAAAA,MAAK,CAAC;AACrDxB,oBAAcD,WAAU;AAAA,IAC1B,OACK;AACH0B,YAAM,+DAA+D;AAAA,IACvE;AAAA,EACF,GAAG,EAAE;AAEC,QAAAC,mBAAmBxD,YAAYyD,SAAS,gBAAgB;AAG9D,QAAMC,sBAAsB3F,aAAA,YAAY,CAAC4F,IAAYC,iBAAmC3F,gBAAyB;AAC/GiF,qBAAkBW,eAAc;AAC9B,UAAIA,UAAUC,KAAMC,SAAQA,IAAIJ,OAAOA,EAAE,GAAG;AAE1C,eAAOE,UAAUG,OAAQD,SAAQA,IAAIJ,OAAOA,EAAE;AAAA,MAChD,OAAO;AAEE,eAAA,CAAC,GAAGE,WAAWD,eAAe;AAAA,MACvC;AAAA,IACF,CAAC;AAAA,EACH,GAAG,EAAE;AAGC,QAAAK,qBAAqBlG,aAAAA,YAAY,MAAM;AAC3CmF,qBAAiB,CAAA,CAAE;AACnBE,sBAAkB,CAAA,CAAE;AACpBhB,wBAAoB,KAAK;AAAA,EAC3B,GAAG,EAAE;AAEL,QAAM8B,uBAAuBnG,aAAAA,YAC3B,CAAC4F,IAAYC,iBAAmC5F,YAAmBC,gBAAyB;AAC1FmF,sBAAmBS,eAAc;AAC/B,YAAMM,gBAAgBN,UAAUO,UAAWC,SAAQA,IAAIV,OAAOA,EAAE;AACxDpD,cAAAC,IAAI,kBAAkBqD,SAAS;AAEvC,UAAIM,kBAAkB,IAAI;AAExB5D,gBAAQC,IAAI,mCAAmC;AAC/C,eAAOqD,UAAU/C,IAAKuD,SACpBA,IAAIV,OAAOA,KAAK;AAAA,UAAE,GAAGU;AAAAA,UAAKpG,aAAaA,eAAe;AAAA,QAAS,IAAAoG,GACjE;AAAA,MACF,OAAO;AAEL9D,gBAAQC,IAAI,4BAA4B;AACjC,eAAA,CACL,GAAGqD,WACH;AAAA,UACEF;AAAAA,UACA3F,WAAWA;AAAAA;AAAAA,UACXC,aAAaA,eAAe;AAAA,UAC5BqG,YAAYV;AAAAA,QACd,CAAA;AAAA,MAEJ;AAAA,IACF,CAAC;AAAA,EACH,GACA,CAACR,iBAAiB,CACpB;AAEA,QAAMmB,eAAe,OAAOC,WAAmBC,OAAYC,aAAqB;AAC1E,QAAA;AACI,YAAAC,WAAW,IAAIC,SAAS;AACrBD,eAAAE,OAAO,cAAc,gBAAgB;AAC9CF,eAASE,OAAO,YAAYH,SAASI,SAAA,CAAU;AACtCH,eAAAE,OAAO,aAAaL,SAAS;AACtCG,eAASE,OAAO,SAASJ,MAAMK,SAAA,CAAU;AACjCvE,cAAAC,IAAIiE,OAAO,mBAAmB;AAC9BlE,cAAAC,IAAIgE,WAAW,mBAAmB;AAC1C,YAAMO,QAAQC,OAAOL,UAAU;AAAA,QAAEM,QAAQ;AAAA,MAAO,CAAC;AAEjDC,gBAAU,GAAGV,UAAUW,QAAQ,YAAY,KAAK,CAAC,yBAAyB,SAAS;AAAA,aAE5EC,OAAO;AACdF,gBAAU,oBAAoBV,UAAUW,QAAQ,YAAY,KAAK,CAAC,IAAI,OAAO;AAAA,IAC/E;AAAA,EACF;AACA,QAAME,oBAAoBtH,aAAAA,YACxB,CAAC4F,IACC2B,gBACG;AACHxC,uBAAoBe,eAAc;AAChC,UAAIA,UAAUd,SAAS;AACjB,YAAAc,UAAU3F,WAAWyF,IAAI;AAE3B,iBAAO;AAAA,YAAEzF,QAAQ;AAAA,YAAM6E,SAAS;AAAA,YAAOC,qBAAqB;AAAA,UAAK;AAAA,QACnE,OAAO;AAEL,iBAAO;AAAA,YAAE9E,QAAQyF;AAAAA,YAAIZ,SAAS;AAAA,YAAMC,qBAAqBsC;AAAAA,UAAY;AAAA,QACvE;AAAA,MACF,OAAO;AAEL,eAAO;AAAA,UAAEpH,QAAQyF;AAAAA,UAAIZ,SAAS;AAAA,UAAMC,qBAAqBsC;AAAAA,QAAY;AAAA,MACvE;AAAA,IACF,CAAC;AAAA,EACH,GACA,EACF;AAEM,QAAA;AAAA,IAAEJ;AAAAA,EAAU,IAAIK,SAAS;AAGzB,QAAAC,eAAgBC,cAAqB;AACnC,UAAAd,WAAW,IAAIC,SAAS;AACrBD,aAAAE,OAAO,YAAYY,QAA6B;AAChDd,aAAAE,OAAO,aAAa7G,SAA8B;AAClD2G,aAAAE,OAAO,cAAc,qBAAqB;AACnDE,YAAQC,OAAOL,UAAU;AAAA,MAAEM,QAAQ;AAAA,IAAO,CAAC;AAC3C,QAAIF,QAAQvD,UAAU,UAAUuD,QAAQW,SAAS,QAC/C;AACAR,gBAAU,sCAAsC,SAAS;AAAA,IAC3D;AAAA,EACF;AACA,QAAMS,0BAA0B5H,aAAA,YAAayD,WAAmB;AAAEQ,2BAAuBR,KAAK;AAAA,EAAG,GAAG,EACpG;AAEA,QAAMoE,2BAA2B7H,aAAA,YAAayD,WAAmB;AAC/DU,4BAAwBV,KAAK;AAAA,EAC/B,GAAG,EACH;AAEA,QAAMwD,SAASa,UAAU;AACnB,QAAAC,eAAe,OAAOC,UAA4C;AACtExF,YAAQC,IAAI,4BAA4B;AACxCuF,UAAMC,eAAe;AACrBzF,YAAQC,IAAI,yBAAyB;AAC/B,UAAAmE,WAAW,IAAIC,SAAS;AACrBD,aAAAE,OAAO,cAAc,eAAe;AACvC,UAAAoB,cAAc9C,eAAerC,IAAaoF,WAAA;AAAA,MAC9ClI,WAAWkI,KAAKlI,aAAa;AAAA,MAC7BC,aAAakI,SAASD,KAAKjI,eAAe,GAAG;AAAA;AAAA,MAC7CmI,OAAO,CAACF,KAAKvC,MAAM,CAAC;AAAA,IACtB,EAAE;AACFgB,aAASE,OAAO,cAAcwB,KAAKC,UAAUL,WAAW,CAAC;AACzD1F,YAAQC,IAAI,+BAA+B+F,OAAOC,YAAY7B,QAAQ,CAAC;AAEvEK,WAAOL,UAAU;AAAA,MAAEM,QAAQ;AAAA,MAAQwB,SAAS;AAAA,IAAsB,CAAC;AACnEb,6BAAyB,KAAK;AAAsB3B,uBAAA;AAAA,EACtD;AAEA,QAAMyC,gCAAgC3I,aAAA,YAAY,OAAO6B,SAAgB4B,OAAemF,aAAqB;AACvG,QAAA,CAACnF,SAAS,CAACmF,UAAU;AACvBpD,YAAM,kDAAkD;AACxD;AAAA,IACF;AAEI,QAAA;AACI,YAAAqD,WAAW,MAAMC,MAAM,iCAAiCjH,OAAM,UAAU4B,KAAK,aAAamF,QAAQ,EAAE;AAEpG,YAAAjB,OAAO,MAAMkB,SAASE,KAAK;AAG7B,UAAA,CAACF,SAASG,IAAI;AAChB,cAAM,IAAIC,MAAMtB,KAAKN,SAAS,eAAe;AAAA,MAC/C;AACoB1C,0BAAAgD,KAAKjD,iBAAiBiD,IAAI;AAAA,aACvCN,OAAO;AACN7E,cAAA6E,MAAM,qCAAqCA,KAAK;AACxD7B,YAAM,sDAAsD;AAAA,IAC9D;AAAA,EACF,GAAG,EAAE;AAELjF,eAAAA,UAAU,MAAM;AACd,QAAIwC,OAAOxB,cAAc;AACvB,YAAM2H,kBAAkB3H,aAAawB,IAAKQ,WAAU;AAAA,QAClDqC,IAAIrC,KAAK4F;AAAAA,QACTC,OAAO7F,KAAK8F,iBAAiBC,cAAc/F,KAAK8F,cAAc,IAAI,CAAA;AAAA,MACpE,EAAE;AACc1G,sBAAA,IAAIC,IAAIsG,gBAAgBnG,IAAKwG,aAAYA,QAAQ3D,EAAE,CAAC,CAAC;AAAA,IACvE;AAAA,EACF,GAAG,CAAC7C,KAAKxB,YAAY,CAAC;AAEtBhB,eAAAA,UAAU,MAAM;AAEd,QAAIa,oBAAoBG,cAAc;AACpC,UAAI0B,WAAW;AACbH,qBAAa,KAAK;AAAA,MACpB;AAAA,IACF;AAAA,EACC,GAAA,CAAC1B,kBAAkBG,cAAc0B,SAAS,CAAC;AAExC,QAAAuG,YAAYxJ,aAAAA,YAAY,MAAM;AAClCgD,WAAO,IAAI;AACXE,iBAAa,KAAK;AAAA,EACpB,GAAG,EAAE;AAEC,QAAAuG,mBAAoBL,WAAuC;AAC/D,UAAMM,YAAYN,MAAMrG,IAAK4G,UAASA,KAAKjJ,GAAG;AAC9C,UAAMkJ,aAAaR,MAAMrG,IAAK4G,UAASA,KAAKhJ,GAAG;AAEzC,UAAAkJ,SAASH,UAAUI,OAAO,CAACC,GAAGC,MAAMD,IAAIC,GAAG,CAAC;AAC5C,UAAAC,SAASL,WAAWE,OAAO,CAACC,GAAGC,MAAMD,IAAIC,GAAG,CAAC;AAE5C,WAAA;AAAA,MACLtJ,KAAKmJ,SAASH,UAAUxI;AAAAA,MACxBP,KAAKsJ,SAASL,WAAW1I;AAAAA,IAC3B;AAAA,EACF;AAEA,QAAMgJ,iBAAiB,CACrB,MACA,QACA,gBACA,QAAA;AAEF,QAAMC,uBAAuB,CAC3B,MACA,gBACA,YACA,QAAA;AAGF,QAAM,CAACC,aAAaC,cAAc,IAAItK,aAAAA,SAAS,KAAK;AACpD,QAAM,CAACuK,mBAAmBC,oBAAoB,IAAIxK,aAAAA,SAAS,KAAK;AAEhE,QAAMyK,aAAanI,YAAY;AAC/B,QAAMoI,gBAAgB,CACpB,aACA,eACA,SAAA;AAEF,QAAMzD,UAAU0D,WAAW;AAGrB,QAAA,CAACC,gBAAgBC,gBAAgB,IAAI7K,aAAA,SAAkC,MAC3EgC,+CAAe+H,OAAO,CAACe,KAAKC,WAAW;AACjCD,QAAAC,OAAOC,eAAe,IAAID,OAAOE;AAC9B,WAAAH;AAAAA,EACT,GAAG,CAA6B,EAElC;AAEM,QAAAI,eAAe,OAAOF,oBAA4B;AAEtDH,qBAAkBM,WAAU;AAAA,MAC1B,GAAGA;AAAAA,MACH,CAACH,eAAe,GAAG,CAACG,KAAKH,eAAe;AAAA,IAC1C,EAAE;AACFtD,iBAAasD,eAAe;AAAA,EAG9B;AACM,QAAAI,qBAAsBC,cAAqB;AACzC,UAAAxE,WAAW,IAAIC,SAAS;AACrBD,aAAAE,OAAO,YAAYsE,QAA6B;AAChDxE,aAAAE,OAAO,cAAc,cAAc;AAC5CF,aAASE,OAAO,aAAa7G,UAAU8G,SAAA,CAAU;AAGjDC,YAAQC,OAAOL,UAAU;AAAA,MAAEM,QAAQ;AAAA,IAAO,CAAC;AAAA,EAC7C;AACA,QAAM,CAACmE,aAAaC,cAAc,IAAIvL,aAAAA,SAAqC,CAAA,CAAE;AACvE,QAAAwL,qBAAqBA,CAACC,SAAiBC,kBAA2B;AACtE,UAAMC,YAAY,CAACD;AACJH,mBAACJ,WAAU;AAAA,MAAE,GAAGA;AAAAA,MAAM,CAACM,OAAO,GAAGE;AAAAA,IAAU,EAAE;AACtD,UAAA9E,WAAW,IAAIC,SAAS;AACrBD,aAAAE,OAAO,WAAW,mBAAmB;AAC9CF,aAASE,OAAO,eAAe0E,QAAQzE,SAAA,CAAU;AACjDH,aAASE,OAAO,aAAa7G,UAAU8G,SAAA,CAAU;AACjDH,aAASE,OAAO,UAAU4E,UAAU3E,SAAA,CAAU;AAC9CC,YAAQC,OAAOL,UAAU;AAAA,MAAEM,QAAQ;AAAA,IAAM,CAAC;AAAA,EAC5C;AAEA,QAAM,CAACyE,iBAAiBC,iBAAiB,IAAI7L,aAAAA,SAAqC,CAAA,CAAE;AACpF,QAAM,CAAC8L,YAAYC,aAAa,IAAI/L,aAAAA,SAAoC,CAAA,CAAE;AAC1E,QAAM,CAACgM,oBAAoBC,oBAAoB,IAAIjM,aAAAA,SAAqC,CAAA,CAAE;AAC1F,QAAM,CAACkM,YAAYC,aAAa,IAAInM,aAAAA,SAAoC,CAAA,CAAE;AAC1E,QAAM,CAACoM,cAAcC,eAAe,IAAIrM,aAAAA,SAAwB,IAAI;AAEpE,QAAMsM,gBAAgB3B,WAAkE;AACxF,QAAM,CAAC4B,aAAaC,cAAc,IAAIxM,aAAAA,SAAmB,CAAA,CAAE;AAC3D,QAAM,CAACyM,aAAaC,cAAc,IAAI1M,aAAAA,SAAwB,IAAI;AAC5D,QAAA2M,eAAelM,oBAAyB,IAAI;AAC5C,QAAAmM,kBAAkBA,CAAC/G,IAAYgH,QAAgB;AACrCd,kBAACZ,WAAU;AAAA,MAAE,GAAGA;AAAAA,MAAM,CAACtF,EAAE,GAAGgH;AAAAA,IAAI,EAAE;AAAA,EAClD;AAGM,QAAAC,gBAAgBA,CAACC,OAAeC,QAAuB;AACrD,UAAAnG,WAAW,IAAIC,SAAS;AACrBD,aAAAE,OAAO,cAAc,gBAAgB;AAC9CF,aAASE,OAAO,cAAcwB,KAAKC,UAAUwE,GAAG,CAAC;AACjDnG,aAASE,OAAO,cAAc+E,WAAWiB,KAAK,EAAE/F,UAAU;AAC1DC,YAAQC,OAAOL,UAAU;AAAA,MAAEM,QAAQ;AAAA,IAAM,CAAC;AACxB0E,sBAACV,WAAU;AAAA,MAAE,GAAGA;AAAAA,MAAM,CAAC4B,KAAK,GAAG;AAAA,IAAM,EAAE;AAAA,EAC3D;AACM,QAAAE,gBAAgBA,CAACF,OAAeC,QAAuB;AACrD,UAAAnG,WAAW,IAAIC,SAAS;AACrBD,aAAAE,OAAO,cAAc,iBAAiB;AAC/CF,aAASE,OAAO,cAAcwB,KAAKC,UAAUwE,GAAG,CAAC;AACjDnG,aAASE,OAAO,aAAamF,WAAWa,KAAK,EAAE/F,UAAU;AACzDC,YAAQC,OAAOL,UAAU;AAAA,MAAEM,QAAQ;AAAA,IAAM,CAAC;AACxB0E,sBAACV,WAAU;AAAA,MAAE,GAAGA;AAAAA,MAAM,CAAC4B,KAAK,GAAG;AAAA,IAAM,EAAE;AAAA,EAC3D;AAEAvM,eAAAA,UAAU,MAAM;AACV,QAAA8L,cAAc1E,QAAQwE,iBAAiB,MAAM;AAC3C,UAAAE,cAAc1E,KAAKN,OAAO;AACboF,uBAAAJ,cAAc1E,KAAKN,KAAK;AAAA,MACzC,WAAWgF,cAAc1E,KAAKsF,SAAS;AAC/B,cAAAC,cAAcb,cAAc1E,KAAKsF;AAEvCf,sBAAehB,WAAU;AAAA,UACvB,GAAGA;AAAAA,UACH,CAACiB,YAAa,GAAGe;AAAAA;AAAAA,QACnB,EAAE;AAEFX,uBAAgBrB,WAAU;AAAA,UACxB,GAAGA;AAAAA,UACH,CAACiB,YAAa,GAAGe;AAAAA;AAAAA,QACnB,EAAE;AAEFT,uBAAe,IAAI;AACnB,YAAIC,aAAaS,QAAsBT,cAAAS,QAAQzG,QAAQ;AAAA,MACzD;AAAA,IACF;AAAA,EACF,GAAG,CAAC2F,cAAc1E,IAAI,CAAC;AAMjB,QAAAyF,mBAAmB,OAAOpF,OAA4CqF,UAAkB;AAC5FjB,oBAAgBiB,KAAK;AACrB,UAAMC,QAAQC,MAAMC,KAAKxF,MAAMyF,OAAOH,SAAS,EAAE;AAC7C,QAAAA,MAAMpM,WAAW,EAAG;AAExBuL,mBAAe,IAAI;AAEnB,UAAMiB,gBAAgB,MAAM;AAC5B,UAAMC,eAAe,CAAC,cAAc,aAAa,aAAa,YAAY;AAE1E,UAAMC,YAAYN,MAAMO,KAAMC,UAAS;AACjC,UAAAA,KAAKC,OAAOL,eAAe;AAC7BjB,uBAAe,gCAAgC;AACxC,eAAA;AAAA,MACT;AACA,UAAI,CAACkB,aAAajI,SAASoI,KAAKE,IAAI,GAAG;AACrCvB,uBAAe,mDAAmD;AAC3D,eAAA;AAAA,MACT;AACO,aAAA;AAAA,IACT,CAAC;AAED,QAAI,CAACmB,UAAW;AAEV,UAAAK,iBAAiB,IAAIpH,SAAS;AACrBoH,mBAAAnH,OAAO,WAAW,aAAa;AAC9CmH,mBAAenH,OAAO,QAAQ8G,WAAWA,UAAUM,IAAI;AAEvD7B,kBAAcpF,OAAOgH,gBAAgB;AAAA,MACnC/G,QAAQ;AAAA,MACRiH,QAAQ;AAAA,MACRzF,SAAS;AAAA,IACX,CAAC;AAAA,EACH;AAEM,QAAA0F,UAAUpH,QAAQvD,UAAU;AAKhC,SAAArD,kCAAAA,KAAC,OAAI;AAAA,IAAAiO,WAAU;AAAA,IACbC,UAAA,CAAAlO,kCAAA,KAAC;MAAGiO,WAAU;AAAA,MAAiCE,SAASA,MAAM/D,WAAW,yBAAyB;AAAA,MAAG8D,UAAA,CAAA,KAAEjO,kCAAA,IAAA,QAAA;AAAA,QAAKgO,WAAU;AAAA,QAAWC,UAAqB;AAAA,OAAA,GAAO,KAAClO,kCAAA,KAAC,QAAK;AAAA,QAAAiO,WAAU;AAAA,QAAWC,UAAA,CAAAjN,aAAY,GAAA;AAAA,MAAC,CAAA,GAAO,GAAA;AAAA,IAAC,CAAA,GAC7MjB,kCAAA,KAAAoO,MAAA;AAAA,MAAK9H,OAAOpF;AAAAA,MAAWmN,eAAenM;AAAAA,MACpCgM,UAAA,CAAWF,WAAA/N,kCAAA,IAACqO;QAAcN;AAAAA,MAAkB,CAAA,0CAC5CO,UACE;AAAA,QAAAL,UAAA,CAAA,CAAC7I,oBAAoBpF,kCAAAA,IAACuO,aAAY;AAAA,UAAAlI,OAAM;AAAA,UAAgB4H,UAAc;AAAA,QAAA,CAAA,GAEtE,CAAC7I,oBAAoBpF,kCAAA,IAACuO,aAAY;AAAA,UAAAlI,OAAM;AAAA,UAAgB4H,UAAO;AAAA,QAAA,CAAA,GAE/DjO,kCAAA,IAAAuO,aAAA;AAAA,UAAYlI,OAAM;AAAA,UAAe4H,UAAK;AAAA,QAAA,CAAA,GACtCjO,kCAAA,IAAAuO,aAAA;AAAA,UAAYlI,OAAM;AAAA,UAAgB4H,UAAM;AAAA,QAAA,CAAA,GACxCjO,kCAAA,IAAAuO,aAAA;AAAA,UAAYlI,OAAM;AAAA,UAAiB4H,UAAc;AAAA,QAAA,CAAA,CAAA;AAAA,MAGpD,CAAA,GACAlO,kCAAA,KAACyO,aAAY;AAAA,QAAAnI,OAAM;AAAA,QACjB4H,UAAA,CAAAjO,kCAAA,IAACyO,iBAAA;AAAA,UACCC,SAAStE;AAAAA,UACT9C,MACE5F,cAAckE,OAAY8D,OAAA,CAACA,EAAEiB,QAAQ;AAAA,UAEvCgE,WAAYjC,SAAQ;AAEhB,mBAAA3M,kCAAAA,KAAC,MAAsB;AAAA,cAAAiO,WAAU;AAAA,cAC/BC,UAAA,CAAAjO,kCAAA,IAAC,MAAG;AAAA,gBAAAgO,WAAU;AAAA,gBAAwDC,UAAAvB,IAAIrF;AAAAA,cAAS,CAAA,yCAClF,MAAG;AAAA,gBAAA2G,WAAU;AAAA,gBACXC,WAAAvB,2BAAKjC,WAAU;AAAA,cAClB,CAAA,GACAzK,kCAAA,IAAC,MAAG;AAAA,gBAAAgO,WAAU;AAAA,gBACZC,UAAAjO,kCAAA,IAAC4O,QAAA;AAAA,kBACCC,SAAQ;AAAA,kBACRnB,MAAK;AAAA,kBACLM,WAAU;AAAA,kBACVE,SAASA,MAAMtD,aAAa8B,2BAAKhC,eAAe;AAAA,kBAEhDuD,UAAAjO,kCAAA,IAAC8O,QAAO;AAAA,oBAAApB,MAAM;AAAA,kBAAI,CAAA;AAAA,gBACpB,CAAA;AAAA,cACF,CAAA,CAAA;AAAA,YAAA,GAdOhB,IAAIrF,QAgBb;AAAA,UAEJ;AAAA,QAAA,CAEF,GACArH,kCAAA,IAAC4O;UAAOZ,WAAU;AAAA,UAAsCE,SAASA,MAAMlE,eAAe,IAAI;AAAA,UAAGiE,UAE7F;AAAA,QAAA,CAAA,GACAjO,kCAAA,IAAC+O,cAAA;AAAA,UAAaC,QAAQjF;AAAAA,UAAakF,SAASA,MAAMjF,eAAe,KAAK;AAAA,UACpErI,YAAYA,aAAaA,WAAWiE,cAAY,CAAClE,cAAckE,OAAO8D,QAAK,CAACA,GAAEiB,QAAQ,EAAEjF,KAAWwJ,QAAAA,GAAG7H,aAAaqC,EAAEnE,EAAE,CAAC,IAAI,CAAC;AAAA,UAAG4J,WAAWvP;AAAAA,QAAA,CAC7I,CAAA;AAAA,MACF,CAAA,GACAG,kCAAA,KAACyO,aAAY;AAAA,QAAAnI,OAAM;AAAA,QACjB4H,UAAA,CAAAjO,kCAAA,IAACyO,iBAAA;AAAA,UACCC,SAAS5E;AAAAA,UACTxC,MACEjG,kBAAkB,CAAC;AAAA,UAErBsN,WAAYjC,SACT3M,kCAAA,KAAA,MAAA;AAAA,YAAgBiO,WAAU;AAAA,YACzBC,UAAA,CAAAjO,kCAAA,IAAC,MAAG;AAAA,cAAAgO,WAAU;AAAA,cACXC,WAAAvB,2BAAKnH,OAAM;AAAA,YACd,CAAA,GACCvF,kCAAA,IAAA,MAAA;AAAA,cAAGgO,WAAU;AAAA,cACZC,gDAAC,OAAI;AAAA,gBAAAD,WAAU;AAAA,gBAEZC,UAAAvC,mBAAmBgB,IAAInH,EAAE,IACvBxF,kCAAAA,KAAA,OAAA;AAAA,kBAAIiO,WAAU;AAAA,kBAGbC,UAAA,CAAAjO,kCAAA,IAAC,SAAA;AAAA,oBACCoP,KAAK/C;AAAAA,oBACLsB,MAAK;AAAA,oBACL0B,QAAO;AAAA,oBACPC,UAAWC,OAAMxC,iBAAiBwC,GAAG7C,IAAInH,EAAE;AAAA,oBAC3CyI,WAAU;AAAA,kBAAA,CAGZ,GAGAjO,kCAAA,KAAC,OAAI;AAAA,oBAAAiO,WAAU;AAAA,oBACZC,UAAA,CAAAhC,YAAYS,IAAInH,EAAE,KAChBxF,kCAAA,KAAA,OAAA;AAAA,sBAAIiO,WAAU;AAAA,sBACbC,UAAA,CAACjO,kCAAA,IAAA,QAAA;AAAA,wBAAKgO,WAAU;AAAA,wBAAiCC,UAA0B;AAAA,sBAAA,CAAA,GAC1EjO,kCAAA,IAAA,QAAA;AAAA,wBAAKgO,WAAU;AAAA,wBAAwBC,UAAa;AAAA,sBAAA,CAAA,GACrDjO,kCAAA,IAAC,OAAA;AAAA,wBACCwP,KAAKvD,YAAYS,IAAInH,EAAE;AAAA,wBACvBkK,KAAI;AAAA,wBACJzB,WAAU;AAAA,sBAAA,CACZ,CAAA;AAAA,oBACF,CAAA,GAIFjO,kCAAA,KAAC,OAAI;AAAA,sBAAAiO,WAAU;AAAA,sBACbC,UAAA,CAAAjO,kCAAA,IAAC0P,MAAA;AAAA,wBACChC,MAAM;AAAA,wBACNM,WAAU;AAAA,wBACVE,SAASA,MAAMvB,cAAcD,IAAInH,IAAImH,GAAG;AAAA,sBAAA,CAC1C,GACA1M,kCAAA,IAAC2P,GAAA;AAAA,wBACCjC,MAAM;AAAA,wBACNM,WAAU;AAAA,wBACVE,SAASA,MAAM;AACQvC,+CAACd,WAAU;AAAA,4BAAE,GAAGA;AAAAA,4BAAM,CAAC6B,IAAInH,EAAE,GAAG;AAAA,0BAAM,EAAE;AAC9C2G,yCAACrB,WAAU;AAAA,4BAAE,GAAGA;AAAAA,4BAAM,CAAC6B,IAAInH,EAAE,GAAGmH,IAAIkD;AAAAA,0BAAU,EAAE;AACjD/D,wCAAChB,WAAU;AAAA,4BAAE,GAAGA;AAAAA,4BAAM,CAAC6B,IAAInH,EAAE,GAAGmH,IAAIkD;AAAAA,0BAAU,EAAE;AAC9D,8BAAIvD,aAAaS,QAAsBT,cAAAS,QAAQzG,QAAQ;AAAA,wBACzD;AAAA,sBAAA,CACF,CAAA;AAAA,oBACF,CAAA,CAAA;AAAA,kBACF,CAAA,CAAA;AAAA,gBAAA,CACF,IAEAtG,kCAAA,KAAC,OAAI;AAAA,kBAAAiO,WAAU;AAAA,kBAEbC,UAAA,CAAAjO,kCAAA,IAAC,OAAA;AAAA,oBACCyP,KAAI;AAAA,oBACJD,KAAK5D,WAAWc,IAAInH,EAAE,KAAKmH,IAAIkD;AAAAA,oBAC/B5B,WAAU;AAAA,kBAAA,CACZ,GAGAhO,kCAAA,IAAC6P,QAAA;AAAA,oBACC7B,WAAU;AAAA,oBACVE,SAASA,MAAMvC,qBAAqB;AAAA,sBAAE,CAACe,IAAInH,EAAE,GAAG;AAAA,oBAAM,CAAA;AAAA,kBAAA,CACxD,CAAA;AAAA,gBACF,CAAA;AAAA,cAGJ,CAAA;AAAA,YACF,CAAA,GAICvF,kCAAA,IAAA,MAAA;AAAA,cAAGgO,WAAU;AAAA,cACZC,UAACjO,kCAAA,IAAA,OAAA;AAAA,gBAAIgO,WAAU;AAAA,gBAEZC,UAAgB3C,gBAAAoB,IAAInH,EAAE,IAEnBxF,kCAAAA,KAAAE,kBAAAA,UAAA;AAAA,kBAAAgO,UAAA,CAAAjO,kCAAA,IAAC8P,OAAA;AAAA,oBACCnC,MAAK;AAAA,oBACLtH,OAAOmF,WAAWkB,IAAInH,EAAE,KAAKmH,IAAIqD;AAAAA,oBACjCT,UAAWC,OAAMjD,gBAAgBI,IAAInH,IAAIgK,EAAEnC,OAAO/G,KAAK;AAAA,oBACvD2H,WAAU;AAAA,kBAAA,CAEZ,GACChO,kCAAA,IAAA0P,MAAA;AAAA,oBAAKhC,MAAM;AAAA,oBAAIQ,SAASA,MAAM1B,cAAcE,IAAInH,IAAImH,GAAG;AAAA,oBAAGsB,WAAU;AAAA,kBAAiB,CAAA,GACtFhO,kCAAA,IAAC2P,GAAA;AAAA,oBACCK,OAAM;AAAA,oBACNtC,MAAM;AAAA,oBACNM,WAAU;AAAA,oBACVE,SAASA,MAAM3C,kBAAkB,CAAE,CAAA;AAAA,kBAAA,CACrC,CAAA;AAAA,gBAAA,CACF,IAEGxL,kCAAA,KAAAE,4BAAA;AAAA,kBAAAgO,UAAA,EAAAvB,2BAAKqD,eAAc,2CACnBF,QAAO;AAAA,oBAAAnC,MAAM;AAAA,oBAAIM,WAAU;AAAA,oBAA6BE,SAASA,MAAM3C,kBAAkB;AAAA,sBAAE,CAACmB,IAAInH,EAAE,GAAG;AAAA,oBAAM,CAAA;AAAA,kBAAG,CAAA,GAC9GmH,IAAIuD,UAAU,+CAAU,QAAK;AAAA,oBAAAjC,WAAU;AAAA,oBAA8DC,UAAQ;AAAA,kBAAA,CAAA,CAAA;AAAA,gBAEhH,CAAA;AAAA,cAIJ,CAAA;AAAA,YACF,CAAA,GACAjO,kCAAA,IAAC,MAAG;AAAA,cAAAgO,WAAU;AAAA,cAEZC,UAAAjO,kCAAA,IAAC4O,QAAA;AAAA,gBACCC,SAAQ;AAAA,gBACRnB,MAAK;AAAA,gBACLM,WAAU;AAAA,gBACVE,SAASA,MAAM;AACT,sBAAAgC,QAAQ,sDAAsD,GAAG;AACnEpF,uCAAmB4B,2BAAKnH,EAAE;AAAA,kBAC5B;AAAA,gBACF;AAAA,gBACA4K,OAAO;AAAA,kBAAEC,WAAW;AAAA,gBAAW;AAAA,gBAE/BnC,UAAAjO,kCAAA,IAAC8O,QAAO;AAAA,kBAAApB,MAAM;AAAA,gBAAI,CAAA;AAAA,cACpB,CAAA;AAAA,YACF,CAAA,CAAA;AAAA,UAAA,GA3HOhB,IAAInH,EA4Hb;AAAA,QAEJ,CAAA,GACC,CAACH,oBAAoBpF,kCAAA,IAAC4O,QAAA;AAAA,UAAOZ,WAAU;AAAA,UAAsCE,SAASA,MAAMhE,qBAAqB,IAAI;AAAA,UACrH+D,UAAA;AAAA,QAAA,CAED,GAEAjO,kCAAA,IAACqQ,0BAAA;AAAA,UACCrB,QAAQ/E;AAAAA,UACRgF,SAASA,MAAM/E,qBAAqB,KAAK;AAAA,UACzCtK;AAAAA,QAAA,CAEF,CAAA;AAAA,MAKF,CAAA,GACAG,kCAAA,KAACyO,aAAY;AAAA,QAAAnI,OAAM;AAAA,QACjB4H,UAAA,CAAAjO,kCAAA,IAACyO,iBAAA;AAAA,UACCC,SAAS7E;AAAAA,UACTvC,MACEhG,kBAAiB,CAAC;AAAA,UAEpBqN,WAAYjC,SACT3M,kCAAA,KAAA,MAAA;AAAA,YAAyBiO,WAAU;AAAA,YAClCC,UAAA,CAAAjO,kCAAA,IAAC,MAAG;AAAA,cAAAgO,WAAU;AAAA,cAAwDC,UAAAvB,IAAI7M;AAAAA,YAAY,CAAA,yCAGrF,MAAG;AAAA,cAAAmO,WAAU;AAAA,cACXC,WAAAvB,2BAAK4D,aAAY;AAAA,YACpB,CAAA,yCACC,MAAG;AAAA,cAAAtC,WAAU;AAAA,cACXC,WAAAvB,2BAAK6D,iBAAgB;AAAA,YACxB,CAAA,yCACC,MAAG;AAAA,cAAAvC,WAAU;AAAA,cACZC,UAACjO,kCAAA,IAAA,OAAA;AAAA,gBAAIgO,WAAU;AAAA,gBACbC,UAAAjO,kCAAA,IAACwQ,QAAA;AAAA,kBACCC,SAAS,EAAEzF,YAAY0B,IAAI7M,WAAW,KAAK6M,IAAIgE;AAAAA,kBAC/CC,iBAAiBA,MAAMzF,mBAAmBwB,IAAI7M,aAAamL,YAAY0B,IAAI7M,WAAW,CAAC;AAAA,gBACzF,CAAA;AAAA,cAGF,CAAA;AAAA,YACF,CAAA,CAAA;AAAA,UAAA,GAnBO6M,IAAI7M,WAoBb;AAAA,QAEJ,CAAA,GACC,CAACuF,oBAAoBpF,kCAAA,IAAC4O,QAAO;AAAA,UAAAZ,WAAU;AAAA,UAAsCE,SAASA,MAAMlE,eAAe,IAAI;AAAA,UAAGiE,UAEnH;AAAA,QAAA,CAAA,GACAjO,kCAAA,IAAC4Q,aAAA;AAAA,UAAY5B,QAAQjF;AAAAA,UAAakF,SAASA,MAAMjF,eAAe,KAAK;AAAA,UAAG6G,WAAWjR;AAAAA,UACjF+B;AAAAA,UACAF;AAAAA,QAAA,CACF,CAAA;AAAA,MACF,CAAA,CAAA;AAAA,IACF,CAAA,GACCR,cAAc,oBAAoBG,sDAC7B0P,eAAc;AAAA,MAAA1P;AAAAA,MAA8BJ;AAAAA,MAA0B+P,mBAAmB5K;AAAAA,IAAA,CAAc,IACrGnG,kCAAA,IAAA,OAAA;AAAA,MAAIgO,WAAU;AAAA,MAClBC,UAAAjO,kCAAA,IAAC;QAAEgO,WAAU;AAAA,QAAeC;MAAsC,CAAA;AAAA,IACpE,CAAA,IACDhN,cAAc,kBACZjB,kCAAAA,IAAAgR,YAAA;AAAA,MAAWjQ;AAAAA,MACVkN,UAAClO,kCAAA,KAAA,OAAA;AAAA,QAAIiO,WAAU;AAAA,QACbC,UAAA,CAACjO,kCAAA,IAAA,OAAA;AAAA,UAAIgO,WAAU;AAAA,UACZC,UAAAnM,YAAYZ,eACXA,6CAAcwB,IAAI,CAACuO,SAAStQ,UAC1BX,kCAAA,IAACkR,iBAAA;AAAA,YACCC,mBAAmBF;AAAAA,YACnBG,cAAc9P;AAAAA,YACd1B;AAAAA,YACAoQ,OAAOtP,gBAAgBC,KAAK;AAAA,YAAG0Q,UAAUnQ;AAAAA,YACzCoQ,mBAAmBlM;AAAAA,UAAA,CAErB,KAGFpF,kCAAA,IAAC;YAAIgO,WAAU;AAAA,YAAmBC;UAElC,CAAA;AAAA,QAEJ,CAAA,GAEAlO,kCAAA,KAAC,OAAI;AAAA,UAAAiO,WAAU;AAAA,UACbC,UAAA,CAAClO,kCAAA,KAAA,OAAA;AAAA,YAAIiO,WAAU;AAAA,YACZC,UAAA,CAAA,CAAC7I,oBAAoB,CAACvB,uBAAyB7D,kCAAAA,IAAA,OAAA;AAAA,cAAIgO,WAAU;AAAA,cAC5DC,UAAAjO,kCAAA,IAAC,UAAA;AAAA,gBACCkO,SAASA,MAAM;AAAE1G,2CAAyB,IAAI;AAAsB3B,qCAAA;AAAA,gBAAE;AAAA,gBACvEoI,UAAA;AAAA,cAED,CAAA;AAAA,YACF,CAAA,IAAW,CAAC7I,oBAAqBrF,kCAAA,KAAA,OAAA;AAAA,cAAIiO,WAAU;AAAA,cAE7CC,UAAA,CAAClO,kCAAA,KAAA,OAAA;AAAA,gBAAIiO,WAAU;AAAA,gBAA8BC,UAAA,CAAA,kBAE3CjO,kCAAA,IAAC,UAAO;AAAA,kBAAAkO,SAASA,MAAM;AAAE1G,6CAAyB,KAAK;AAAsB3B,uCAAA;AAAA,kBAAM;AAAA,kBAAAoI,UAAAjO,kCAAA,IAAC2P,GAAE;AAAA,oBAAAjC,MAAM;AAAA,kBAAI,CAAA;AAAA,gBAAE,CAAA,CAAA;AAAA,cACpG,CAAA,GACA3N,kCAAA,KAAC,OAAI;AAAA,gBAAAiO,WAAU;AAAA,gBAEbC,UAAA,CAAAlO,kCAAA,KAACwR,QAAO;AAAA,kBAAAlL,OAAOpC;AAAAA,kBAAemK,eAAelK;AAAAA,kBAC3C+J,UAAA,CAAAjO,kCAAA,IAACwR;oBAAcxD,WAAU;AAAA,oBACvBC,gDAACwD,aAAY;AAAA,sBAAAC,aAAY;AAAA,oBAAe,CAAA;AAAA,kBAC1C,CAAA,GACC1R,kCAAA,IAAA2R,eAAA;AAAA,oBACE1D,UAAUjL,UAAAN,IAAKU,WACbpD,kCAAAA,IAAA4R,YAAA;AAAA,sBAAuBvL,OAAOjD;AAAAA,sBAAQ6K,UAAtB7K;AAAAA,oBAAA,GAAAA,KAA4B,CAC9C;AAAA,kBACH,CAAA,CAAA;AAAA,gBACF,CAAA,0CACCmO,QAAO;AAAA,kBAAAlL,OAAOlC;AAAAA,kBAAkBiK,eAAgByD,iBAAgB;AAC/DzN,wCAAoByN,WAAW;AACDvJ,kDAAA9G,QAAQyC,eAAe4N,WAAW;AAC7ChM,uCAAA;AAAA,kBACrB;AAAA,kBAAG8E,UAAU,CAAC1G;AAAAA,kBACZgK,UAAA,CAAAjO,kCAAA,IAACwR;oBAAcxD,WAAU;AAAA,oBACvBC,gDAACwD,aAAY;AAAA,sBAAAC,aAAY;AAAA,oBAAkB,CAAA;AAAA,kBAC7C,CAAA,GACA1R,kCAAA,IAAC2R;oBACE1D,UACG9M,yDAAAyE,OAAQD,SAAQA,IAAIvC,UAAUa,eAC/BvB,IAAK6F,cACJvI,kCAAAA,IAAC4R,YAAmC;AAAA,sBAAAvL,OAAOkC,SAASA;AAAAA,sBACjD0F,mBAAS1F;AAAAA,oBADK,GAAAA,SAASA,QAE1B;AAAA,kBAEN,CAAA,CAAA;AAAA,gBACF,CAAA,CAAA;AAAA,cAGF,CAAA,GACAxI,kCAAA,KAAC,OAAI;AAAA,gBAAAiO,WAAU;AAAA,gBACZC,UAAA,CAAA5J,oBAAoBA,iBAAiBxD,SAAS,KAC5CwD,iBAAiBuB,OAAY8D,OAAAxI,6CAAcwE,KAAKoM,QAAMA,GAAGhS,UAAU4J,EAAEnE,GAAG,EAAE1E,SAAS,IAAIwD,iBAAiBuB,cAAY,EAAC1E,6CAAcwE,KAAKoM,QAAMA,GAAGhS,WAAW4J,EAAEnE,IAAG,IAAIlB,kBAAkB3B,IAAI,CAACqP,UAAUpR;;AAEnMZ,2DAAA,KAAAE,4BAAA;AAAA,oBAAAgO,UAAA,CAACjO,kCAAA,IAAA,OAAA;AAAA,sBACCiO,iDAAC,SAAM;AAAA,wBAAAD,WAAU;AAAA,wBAA6CgE,SAAS,YAAYD,SAASxM,EAAE;AAAA,wBAC5F0I,UAAA,CAAAjO,kCAAA,IAACiS,UAAA;AAAA,0BACC1M,IAAI,YAAYwM,SAASxM,EAAE;AAAA,0BAC3BkL,SAAS5L,cAAca,KAAMC,SAAQA,IAAIJ,OAAOwM,SAASxM,EAAE;AAAA,0BAC3D2I,SAASA,MAAM5I,oBAAoByM,SAASxM,IAAIwM,QAAQ;AAAA,wBAAA,CAC1D,GACAhS,kCAAA,KAAC,OAAI;AAAA,0BAAAiO,WAAU;AAAA,0BACbC,UAAA,CAAAlO,kCAAA,KAAC,QAAM;AAAA,4BAAAkO,UAAA,CAAS8D,SAAAxM,IAAG,OAAIwM,SAASlE,IAAA;AAAA,0BAAK,CAAA,GACpChJ,cAAca,KAAMC,SAAQA,IAAIJ,OAAOwM,SAASxM,EAAE,KACjDxF,kCAAA,KAACwR,QAAA;AAAA,4BAEClL,SACEtB,MAAAA,eAAeyI,KAAMvH,SAAQA,IAAIV,OAAOwM,SAASxM,EAAE,MAAnDR,gBAAAA,IAAsDlF,gBAAe;AAAA,4BAGvEuO,eAAgByD,iBAAgB;AAC9B/L,mDAAqBiM,SAASxM,IAAIwM,UAAUnS,WAAWiS,WAAW;AAAA,4BAEpE;AAAA,4BAAGlH,UAAU,CAAC1G;AAAAA,4BACdgK,UAAA,CAAAjO,kCAAA,IAACwR;8BAAcxD,WAAU;AAAA,8BACvBC,gDAACwD,aAAY;AAAA,gCAAAC,aAAY;AAAA,8BAAqB,CAAA;AAAA,4BAChD,CAAA,GACC1R,kCAAA,IAAA2R,eAAA;AAAA,8BACE1D,UAAA3M,iDAAeoB,IAAK6F,cACnBxI,kCAAAA,KAAC6R,YAAsC;AAAA,gCAAAvL,OAAOkC,SAAS1I,YAAY6G,SAChE;AAAA,gCAAAuH,UAAA,CAAS1F,SAAA1I,aAAY,OAAI0I,SAAS+H,QAAA;AAAA,8BADpB,GAAA/H,SAAS1I,WAE1B;AAAA,4BAEJ,CAAA,CAAA;AAAA,0BAAA,CACF,CAAA;AAAA,wBAEJ,CAAA,CAAA;AAAA,sBACF,CAAA;AAAA,oBAAA,GAjCQkS,SAASxM,EAkCnB,GACC5E,QAAQ0D,iBAAiBxD,SAAS,KAAOb,kCAAA,IAAA,OAAA;AAAA,sBAAIgO,WAAU;AAAA,oBAA8B,CAAA,GAAQ,GAAA;AAAA,kBAAA,CAAC;AAAA,iBAClG,IAEAhO,kCAAA,IAAA,OAAA;AAAA,kBAAIiO,UAAyB;AAAA,gBAAA,CAAA,GAC9B,GAAA;AAAA,cAAC,CAAA,0CACJ,QAAK;AAAA,gBAAApH,QAAO;AAAA,gBAAOwB,SAAQ;AAAA,gBAAsB6J,UAAUxK;AAAAA,gBAE1DuG,UAAA,CAACjO,kCAAA,IAAA,SAAA;AAAA,kBAAM2N,MAAK;AAAA,kBAASE,MAAK;AAAA,kBAAaxH,OAAO4B,KAAKC,UAAUnD,cAAc;AAAA,gBAAG,CAAA,GAE9E/E,kCAAA,IAAC,UAAA;AAAA,kBACC2N,MAAK;AAAA,kBACLhD,UAAU5F,eAAelE,WAAW;AAAA,kBACpCmN,WAAU;AAAA,kBACXC,UAAA;AAAA,gBAAA,CAED,CAAA;AAAA,cACF,CAAA,CAAA;AAAA,YACF,CAAA,GACC,CAACtK,sBAAwB3D,kCAAA,IAAA,OAAA;AAAA,cAAIgO,WAAU;AAAA,cACtCC,UAAAjO,kCAAA,IAAC,UAAA;AAAA,gBACCkO,SAASA,MAAM3G,wBAAwB,IAAI;AAAA,gBAC5C0G,UAAA;AAAA,cAED,CAAA;AAAA,YACF,CAAA,IACIlO,kCAAA,KAAA,OAAA;AAAA,cAAIiO,WAAU;AAAA,cAEdC,UAAA,CAAClO,kCAAA,KAAA,OAAA;AAAA,gBAAIiO,WAAU;AAAA,gBAA8BC,UAAA,CAAA,iBAE3CjO,kCAAA,IAAC,UAAO;AAAA,kBAAAkO,SAASA,MAAM3G,wBAAwB,KAAK;AAAA,kBAAG0G,UAACjO,kCAAA,IAAA2P,GAAA;AAAA,oBAAEjC,MAAM;AAAA,kBAAI,CAAA;AAAA,gBAAE,CAAA,CAAA;AAAA,cACxE,CAAA,GACA3N,kCAAA,KAAC,OAAI;AAAA,gBAAAiO,WAAU;AAAA,gBACbC,UAAA,CAAAjO,kCAAA,IAAC;kBAAEiO,UAAW;AAAA,gBAAA,CAAA,GACdjO,kCAAA,IAAC,SAAA;AAAA,kBAAM2N,MAAK;AAAA,kBAAOK,WAAU;AAAA,kBAE3BsB,UAAWC,OAAMjM,YAAY6O,WAAW5C,EAAEnC,OAAO/G,KAAK,CAAC;AAAA,gBAAA,CAAG,CAAA;AAAA,cAC9D,CAAA,GACAtG,kCAAA,KAAC,OAAI;AAAA,gBAAAiO,WAAU;AAAA,gBACbC,UAAA,CAAAjO,kCAAA,IAAC;kBAAEiO,UAAY;AAAA,gBAAA,CAAA,GACfjO,kCAAA,IAAC,SAAA;AAAA,kBAAM2N,MAAK;AAAA,kBAAOK,WAAU;AAAA,kBAE3BsB,UAAWC,OAAM/L,aAAa2O,WAAW5C,EAAEnC,OAAO/G,KAAK,CAAC;AAAA,gBAAA,CAC1D,CAAA;AAAA,cACF,CAAA,GACArG,kCAAA,IAAC,UAAA;AAAA,gBAAOgO,WAAU;AAAA,gBAChBE,SAASA,MAAMjJ,mBAAmB5B,UAAUE,WAAW,IAAI;AAAA,gBAC5D0K,UAAA;AAAA,cAAA,CAED,CAAA;AAAA,YACF,CAAA,CAAA;AAAA,UAEJ,CAAA,GACC,CAACrL,aACA5C,kCAAA,IAAC,OAAI;AAAA,YAAAgO,WAAU;AAAA,YACbC,UAAAjO,kCAAA,IAAC,OAAI;AAAA,cAAAgO,WAAU;AAAA,cAASC,UAAA;AAAA,YAAc,CAAA;AAAA,WACxC,GAEDlN,mBACChB,kCAAA,KAACqS,WAAA;AAAA,YACCC,mBAAmB9R;AAAAA,YACnB+R,QAAQlS;AAAAA,YACRmS,MAAM;AAAA,YACNzP,QAASC,iBAAgB;AACvBD,qBAAOC,WAAW;AAClBF,2BAAa,IAAI;AAAA,YACnB;AAAA,YACAsG,WAAWA,MAAM;AACLA,wBAAA;AACVtG,2BAAa,KAAK;AAAA,YACpB;AAAA,YACA2P,SAAS;AAAA,cACPC,QAAQ,CAAC;AAAA,gBACPC,aAAa;AAAA,gBACbC,aAAa;AAAA,gBACbC,SAAS,CAAC;AAAA,kBAAEC,YAAY;AAAA,gBAAM,CAAA;AAAA,cAChC,CAAC;AAAA,cACDC,gBAAgB;AAAA,cAChBC,mBAAmB;AAAA,cACnBC,mBAAmB;AAAA,cACnBC,gBAAgB;AAAA;AAAA,cAChBC,iBAAiB;AAAA;AAAA,YACnB;AAAA,YAECjF,UAAA,CAAArL,aAAa1B,kDAAc0E,OAAQD,SAAQtD,aAAa8Q,IAAIxN,IAAImD,aAAa,OAAhE5H,mBAAoEwB,IAAI,CAACQ,MAAMvC,UAC3FuC,KAAK8F,kBAGHhJ,kCAAA,IAACoT,SAAA;AAAA,cAECrK,OAAOE,cAAc/F,KAAK8F,cAAc;AAAA,cACxCwJ,SAAS;AAAA,gBACPa,WAAWxP,uBAAuB,YAAYnD,gBAAgBC,KAAK;AAAA,gBACnE2S,aAAa;AAAA,gBACbC,aAAa1P,uBAAuB,YAAYnD,gBAAgBC,KAAK;AAAA,gBACrE6S,eAAe;AAAA,gBACfC,cAAc;AAAA,gBACdC,WAAW;AAAA,gBACXC,UAAU;AAAA,gBACVC,UAAU;AAAA,gBACVC,QAAQ;AAAA,gBACRC,WAAW;AAAA;AAAA,cACb;AAAA,cACA5F,SAASA,MAAMjH,kBAAkB/D,KAAK4F,eAAe5F,IAAI;AAAA,YAAA,GAdpDA,KAAK4F,aAgBZ,KAEE,MACLlG,YAAaiC,+CAAenC,IAAI,CAACQ,MAAMvC,UACtCuC,KAAKgG,WAGHlJ,kCAAAA,IAACoT,SAAA;AAAA,cAECrK,OAAOE,cAAc/F,KAAKgG,OAAO;AAAA,cACjCsJ,SAAS;AAAA,gBACPa,WAAW;AAAA,gBACXC,aAAa;AAAA,gBACbC,aAAa;AAAA,gBACbC,eAAe;AAAA,gBACfC,cAAc;AAAA,gBACdC,WAAW;AAAA,gBACXC,UAAU;AAAA,gBACVC,UAAU;AAAA,gBACVC,QAAQ;AAAA,gBACRC,WAAW;AAAA;AAAA,cACb;AAAA,YAAA,GAbK5Q,KAAKqC,EAcZ,KAEE,MAELd,mBAAmBA,gBAAgB3E,UAAU2E,gBAAgBE,WAAWF,gBAAgBG,uBAGpF7E,kCAAAA,KAAAE,kBAAAA,UAAA;AAAA,cAAAgO,UAAA,CAAQ9L,QAAAC,IAAI,6CAA6CqC,eAAe,GACzEzE,kCAAA,IAAC+T,YAAA;AAAA,gBACCC,UAAU5K,iBAAiBH,cAAcxE,gBAAgBG,oBAAoBoE,cAAc,CAAC;AAAA,gBAC5FiL,cAAcA,MAAMvP,mBAAmB;AAAA,kBAAEC,SAAS;AAAA,kBAAO7E,QAAQ;AAAA,kBAAM8E,qBAAqB;AAAA,gBAAK,CAAC;AAAA,gBAClG4N,SAAS;AAAA,kBACP0B,gBAAgB;AAAA,kBAChBC,UAAU;AAAA,kBACVC,gBAAgB;AAAA,gBAClB;AAAA,gBAEAnG,UAAAlO,kCAAA,KAAC,OAAI;AAAA,kBAAAiO,WAAU;AAAA,kBACbC,UAAA,CAAClO,kCAAA,KAAA,OAAA;AAAA,oBAAIiO,WAAU;AAAA,oBACbC,UAAA,CAACjO,kCAAA,IAAA,MAAA;AAAA,sBAAGgO,WAAU;AAAA,sBAA4CC,UAAa;AAAA,oBAAA,CAAA,GACvEjO,kCAAA,IAAC,UAAA;AAAA,sBAAOgO,WAAU;AAAA,sBAChBE,SAASA,MAAMxJ,mBAAmB;AAAA,wBAAEC,SAAS;AAAA,wBAAO7E,QAAQ;AAAA,wBAAM8E,qBAAqB;AAAA,sBAAK,CAAC;AAAA,sBAC7FqJ,UAAAjO,kCAAA,IAAC2P,GAAE;AAAA,wBAAA3B,WAAU;AAAA,sBAAU,CAAA;AAAA,oBAAA,CACzB,CAAA;AAAA,kBACF,CAAA,GACAjO,kCAAA,KAAC,OAAI;AAAA,oBAAAiO,WAAU;AAAA,oBACbC,UAAA,CAAAlO,kCAAA,KAAC,KAAE;AAAA,sBAAAkO,UAAA,CAACjO,kCAAA,IAAA,QAAA;AAAA,wBAAKgO,WAAU;AAAA,wBAAwCC,UAAQ;AAAA,sBAAA,CAAA,GAAO,MAAExJ,qBAAgBG,wBAAhBH,mBAAqCqE,aAAA;AAAA,oBAAc,CAAA,0CAC9H,KAAE;AAAA,sBAAAmF,UAAA,CAACjO,kCAAA,IAAA,QAAA;AAAA,wBAAKgO,WAAU;AAAA,wBAAwCC,UAAW;AAAA,sBAAA,CAAA,GAAO,MAAExJ,qBAAgBG,wBAAhBH,mBAAqC4P,eAAA;AAAA,oBAAgB,CAAA,0CACnI,KAAE;AAAA,sBAAApG,UAAA,CAACjO,kCAAA,IAAA,QAAA;AAAA,wBAAKgO,WAAU;AAAA,wBAAwCC,UAAY;AAAA,sBAAA,CAAA,GAAO,MAAExJ,qBAAgBG,wBAAhBH,mBAAqC6P,SAAA;AAAA,oBAAU,CAAA,CAAA;AAAA,kBACjI,CAAA,CAAA;AAAA,gBACF,CAAA;AAAA,cAAA,CACF,yCACC,SACE;AAAA,gBAAArG,UAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,cAWH,CAAA,GAAQ,GAAA;AAAA,YAAC,CAAA,GAEZxK,cAAcc,mBAAmBA,gBAAgBlB,aAAa,QAC7DkB,gBAAgBhB,cAAc,QAC1BvD,kCAAAA,IAAAC,kBAAAA,UAAA;AAAA,cAAAgO,UAAAjO,kCAAA,IAACuU,QAAA;AAAA,gBACDP,UAAU;AAAA,kBACR,OAAOzP,gBAAgBlB;AAAAA,kBACvB,OAAOkB,gBAAgBhB;AAAAA,gBACzB;AAAA,cACF,CAAA;AAAA,YAAE,CAAA,CAAA;AAAA,UAAA,CACR,IAECvD,kCAAA,IAAA,OAAA;AAAA,YAAIgO,WAAU;AAAA,YACbC,gDAAC,KAAE;AAAA,cAAAD,WAAU;AAAA,cAAeC,UAAA;AAAA,YAA+B,CAAA;AAAA,UAC7D,CAAA,CAAA;AAAA,QAEJ,CAAA,CAAA;AAAA,MACF,CAAA;AAAA,IACF,CAAA,CAAA;AAAA,EACJ,CAAA;AAEJ;", "x_google_ignoreList": [0]}