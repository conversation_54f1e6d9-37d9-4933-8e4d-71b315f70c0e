{"version": 3, "file": "table-CAxdJZsY.js", "sources": ["../../../app/components/ui/table.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"~/lib/utils\"\r\n\r\nconst Table = React.forwardRef<\r\n  HTMLTableElement,\r\n  React.HTMLAttributes<HTMLTableElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div className=\"relative w-full overflow-auto\">\r\n    <table\r\n      ref={ref}\r\n      className={cn(\"w-full caption-bottom text-sm\", className)}\r\n      {...props}\r\n    />\r\n  </div>\r\n))\r\nTable.displayName = \"Table\"\r\n\r\nconst TableHeader = React.forwardRef<\r\n  HTMLTableSectionElement,\r\n  React.HTMLAttributes<HTMLTableSectionElement>\r\n>(({ className, ...props }, ref) => (\r\n  <thead ref={ref} className={cn(\"[&_tr]:border-b\", className)} {...props} />\r\n))\r\nTableHeader.displayName = \"TableHeader\"\r\n\r\nconst TableBody = React.forwardRef<\r\n  HTMLTableSectionElement,\r\n  React.HTMLAttributes<HTMLTableSectionElement>\r\n>(({ className, ...props }, ref) => (\r\n  <tbody\r\n    ref={ref}\r\n    className={cn(\"[&_tr:last-child]:border-0\", className)}\r\n    {...props}\r\n  />\r\n))\r\nTableBody.displayName = \"TableBody\"\r\n\r\nconst TableFooter = React.forwardRef<\r\n  HTMLTableSectionElement,\r\n  React.HTMLAttributes<HTMLTableSectionElement>\r\n>(({ className, ...props }, ref) => (\r\n  <tfoot\r\n    ref={ref}\r\n    className={cn(\r\n      \"border-t bg-muted/50 font-medium [&>tr]:last:border-b-0\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nTableFooter.displayName = \"TableFooter\"\r\n\r\nconst TableRow = React.forwardRef<\r\n  HTMLTableRowElement,\r\n  React.HTMLAttributes<HTMLTableRowElement>\r\n>(({ className, ...props }, ref) => (\r\n  <tr\r\n    ref={ref}\r\n    className={cn(\r\n      \"border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nTableRow.displayName = \"TableRow\"\r\n\r\nconst TableHead = React.forwardRef<\r\n  HTMLTableCellElement,\r\n  React.ThHTMLAttributes<HTMLTableCellElement>\r\n>(({ className, ...props }, ref) => (\r\n  <th\r\n    ref={ref}\r\n    className={cn(\r\n      \"h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nTableHead.displayName = \"TableHead\"\r\n\r\nconst TableCell = React.forwardRef<\r\n  HTMLTableCellElement,\r\n  React.TdHTMLAttributes<HTMLTableCellElement>\r\n>(({ className, ...props }, ref) => (\r\n  <td\r\n    ref={ref}\r\n    className={cn(\"p-4 align-middle [&:has([role=checkbox])]:pr-0\", className)}\r\n    {...props}\r\n  />\r\n))\r\nTableCell.displayName = \"TableCell\"\r\n\r\nconst TableCaption = React.forwardRef<\r\n  HTMLTableCaptionElement,\r\n  React.HTMLAttributes<HTMLTableCaptionElement>\r\n>(({ className, ...props }, ref) => (\r\n  <caption\r\n    ref={ref}\r\n    className={cn(\"mt-4 text-sm text-muted-foreground\", className)}\r\n    {...props}\r\n  />\r\n))\r\nTableCaption.displayName = \"TableCaption\"\r\n\r\nexport {\r\n  Table,\r\n  TableHeader,\r\n  TableBody,\r\n  TableFooter,\r\n  TableHead,\r\n  TableRow,\r\n  TableCell,\r\n  TableCaption,\r\n}\r\n"], "names": ["React.forwardRef", "jsx"], "mappings": ";;AAIA,MAAM,QAAQA,aAAAA,WAGZ,CAAC,EAAE,WAAW,GAAG,MAAS,GAAA,QACzBC,kCAAAA,IAAA,OAAA,EAAI,WAAU,iCACb,UAAAA,kCAAA;AAAA,EAAC;AAAA,EAAA;AAAA,IACC;AAAA,IACA,WAAW,GAAG,iCAAiC,SAAS;AAAA,IACvD,GAAG;AAAA,EAAA;AACN,GACF,CACD;AACD,MAAM,cAAc;AAEd,MAAA,cAAcD,aAGlB,WAAA,CAAC,EAAE,WAAW,GAAG,SAAS,8CACzB,SAAM,EAAA,KAAU,WAAW,GAAG,mBAAmB,SAAS,GAAI,GAAG,OAAO,CAC1E;AACD,YAAY,cAAc;AAEpB,MAAA,YAAYA,aAGhB,WAAA,CAAC,EAAE,WAAW,GAAG,MAAM,GAAG,QAC1BC,kCAAA;AAAA,EAAC;AAAA,EAAA;AAAA,IACC;AAAA,IACA,WAAW,GAAG,8BAA8B,SAAS;AAAA,IACpD,GAAG;AAAA,EAAA;AACN,CACD;AACD,UAAU,cAAc;AAExB,MAAM,cAAcD,aAGlB,WAAA,CAAC,EAAE,WAAW,GAAG,MAAM,GAAG,QAC1BC,kCAAA;AAAA,EAAC;AAAA,EAAA;AAAA,IACC;AAAA,IACA,WAAW;AAAA,MACT;AAAA,MACA;AAAA,IACF;AAAA,IACC,GAAG;AAAA,EAAA;AACN,CACD;AACD,YAAY,cAAc;AAEpB,MAAA,WAAWD,aAGf,WAAA,CAAC,EAAE,WAAW,GAAG,MAAM,GAAG,QAC1BC,kCAAA;AAAA,EAAC;AAAA,EAAA;AAAA,IACC;AAAA,IACA,WAAW;AAAA,MACT;AAAA,MACA;AAAA,IACF;AAAA,IACC,GAAG;AAAA,EAAA;AACN,CACD;AACD,SAAS,cAAc;AAEjB,MAAA,YAAYD,aAGhB,WAAA,CAAC,EAAE,WAAW,GAAG,MAAM,GAAG,QAC1BC,kCAAA;AAAA,EAAC;AAAA,EAAA;AAAA,IACC;AAAA,IACA,WAAW;AAAA,MACT;AAAA,MACA;AAAA,IACF;AAAA,IACC,GAAG;AAAA,EAAA;AACN,CACD;AACD,UAAU,cAAc;AAElB,MAAA,YAAYD,aAGhB,WAAA,CAAC,EAAE,WAAW,GAAG,MAAM,GAAG,QAC1BC,kCAAA;AAAA,EAAC;AAAA,EAAA;AAAA,IACC;AAAA,IACA,WAAW,GAAG,kDAAkD,SAAS;AAAA,IACxE,GAAG;AAAA,EAAA;AACN,CACD;AACD,UAAU,cAAc;AAExB,MAAM,eAAeD,aAGnB,WAAA,CAAC,EAAE,WAAW,GAAG,MAAM,GAAG,QAC1BC,kCAAA;AAAA,EAAC;AAAA,EAAA;AAAA,IACC;AAAA,IACA,WAAW,GAAG,sCAAsC,SAAS;AAAA,IAC5D,GAAG;AAAA,EAAA;AACN,CACD;AACD,aAAa,cAAc;"}