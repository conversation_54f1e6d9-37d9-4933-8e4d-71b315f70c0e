import { r as reactExports, j as jsxRuntimeExports } from "./jsx-runtime-bB2y7OuD.js";
import { I as Input } from "./input-3v87qohQ.js";
import { T as Table, a as TableHeader, b as TableRow, c as TableHead, d as TableBody, e as TableCell } from "./table-CAxdJZsY.js";
import { u as useLoaderData } from "./components-D7UvGag_.js";
import "./utils-GkgzjW3c.js";
import "./index-Vp2vNLNM.js";
import "./index-DhHTcibu.js";
function Permision() {
  var _a, _b;
  const loaderData = useLoaderData();
  const [searchTerm, setSearchTerm] = reactExports.useState("");
  function handleSearch(x) {
    return x.name.toLowerCase().includes(searchTerm.toLowerCase()) || x.resource.toLowerCase().includes(searchTerm.toLowerCase()) || x.id.toString().toLowerCase().includes(searchTerm.toLowerCase());
  }
  return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
    className: "container mx-auto p-6",
    children: [/* @__PURE__ */ jsxRuntimeExports.jsx("div", {
      className: "flex justify-between items-center mb-6",
      children: /* @__PURE__ */ jsxRuntimeExports.jsx("h1", {
        className: "text-2xl font-bold",
        children: "Permission "
      })
    }), /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
      className: "flex justify-between mb-4",
      children: /* @__PURE__ */ jsxRuntimeExports.jsx(Input, {
        placeholder: "Search By Id, Permission ,Type",
        value: searchTerm,
        onChange: (e) => setSearchTerm(e.target.value),
        className: "max-w-sm"
      })
    }), /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
      className: "rounded-md border",
      children: /* @__PURE__ */ jsxRuntimeExports.jsxs(Table, {
        children: [/* @__PURE__ */ jsxRuntimeExports.jsx(TableHeader, {
          children: /* @__PURE__ */ jsxRuntimeExports.jsxs(TableRow, {
            children: [/* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
              children: "ID"
            }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
              children: "PerMission"
            }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
              children: "Resource"
            })]
          })
        }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableBody, {
          children: ((_a = loaderData == null ? void 0 : loaderData.data) == null ? void 0 : _a.length) > 0 ? (_b = loaderData == null ? void 0 : loaderData.data.filter((x) => handleSearch(x))) == null ? void 0 : _b.map((x) => {
            return /* @__PURE__ */ jsxRuntimeExports.jsxs(TableRow, {
              children: [/* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
                children: x == null ? void 0 : x.id
              }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
                children: x == null ? void 0 : x.name
              }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
                children: x == null ? void 0 : x.resource
              })]
            }, x.id);
          }) : /* @__PURE__ */ jsxRuntimeExports.jsx(TableRow, {
            children: /* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
              colSpan: 9,
              className: "h-24 text-center",
              children: "No results."
            })
          })
        })]
      })
    })]
  });
}
export {
  Permision as default
};
//# sourceMappingURL=home.Permission-CY-hQPrE.js.map
