{"version": 3, "file": "home.sellerList-sYQA_7mj.js", "sources": ["../../../app/routes/home.sellerList.tsx"], "sourcesContent": ["import { json, <PERSON>aderFunction } from \"@remix-run/node\";\r\nimport { useLoaderData, useNavigate } from \"@remix-run/react\";\r\nimport { useState } from \"react\";\r\nimport { But<PERSON> } from \"~/components/ui/button\";\r\nimport { Input } from \"~/components/ui/input\";\r\nimport { ResponsiveTable } from \"~/components/ui/responsiveTable\";\r\nimport { Switch } from \"~/components/ui/switch\";\r\nimport { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from \"~/components/ui/table\";\r\nimport { getSellerList } from \"~/services/masterItemCategories\";\r\nimport { Seller } from \"~/types/api/businessConsoleService/MasterItemCategory\";\r\nimport { NetWorkDetails } from \"~/types/api/businessConsoleService/netWorkinfo\";\r\nimport { withAuth, withResponse } from \"~/utils/auth-utils\";\r\nexport const loader = withAuth(async ({ request }) => {\r\n      try {\r\n            const response = await getSellerList(request);\r\n            return withResponse({ data: response.data }, response.headers);\r\n      }\r\n      catch (error) {\r\n            if (error instanceof Response && error.status === 404) {\r\n                  throw json({ error: \"SellerList pg Not found\" }, { status: 404 });\r\n            }\r\n            throw new Response(\"Failed to fetch SellerList \", { status: 500 });\r\n      }\r\n\r\n})\r\n\r\nexport default function SellerList() {\r\n\r\n      const sellerList = useLoaderData<{ data: Seller[] }>()\r\n      const [searchTerm, setSearchTerm] = useState('')\r\n\r\n      const handleSearch = (x: Seller) => {\r\n            return (x.name.toLowerCase().includes(searchTerm.toLowerCase())\r\n\r\n            )\r\n      }\r\n      const navigate = useNavigate()\r\n\r\n      const SellerHeaders = [\r\n            \"Id\",\r\n            \"Seller Name\",\r\n            \"Status\",\r\n            \"\",\r\n            \"\",\r\n\r\n      ];\r\n      return (\r\n            <div className=\"container mx-auto p-6\">\r\n                  <div className=\"flex justify-between items-center mb-6\">\r\n                        <h1 className=\"text-2xl font-bold\">Sellers</h1>\r\n                  </div>\r\n\r\n                  <ResponsiveTable\r\n                        headers={SellerHeaders}\r\n                        data={\r\n                              sellerList?.data\r\n                        }\r\n                        renderRow={(row) => (\r\n                              <tr key={row.id} className=\"border-b\">\r\n                                    <td className=\"py-2 px-3 font-medium text-left\">{row.id}</td>\r\n                                    <td className=\"py-2  text-right text-blue-500 cursor-pointer flex-wrap\">\r\n                                          <span\r\n                                                onClick={() =>\r\n                                                      navigate(`/home/<USER>\n                                                }\r\n                                          >\r\n                                                {row.name}\r\n                                          </span>  {row?.name || \"-\"}\r\n                                    </td>\r\n\r\n                                    <td className=\"py-2 px-3 text-right\">\r\n                                          <span className={`px-2 py-1 rounded-full text-xs ${row.enabled === true ? 'bg-red-100 text-red-800' : 'bg-green-200 text-green-800'}`}>{row?.enabled === true ? \"Disabled\" : \"Active\"}</span>\r\n                                    </td>\r\n                                    <td className=\"py-2 px-20 text-right\">\r\n\r\n                                    </td>\r\n\r\n\r\n                              </tr>\r\n                        )}\r\n                  />\r\n                  {/* <div className=\"flex justify-between mb-4\">\r\n                        <Input\r\n                              placeholder=\"Search By Seller Name\"\r\n                              value={searchTerm}\r\n                              onChange={(e) => setSearchTerm(e.target.value)}\r\n                              className=\"max-w-sm\"\r\n                        />\r\n                  </div>\r\n                  <Table>\r\n                        <TableHeader>\r\n                              <TableRow>\r\n                                    <TableHead className=\"cursor-pointer\" >Id</TableHead>\r\n                                    <TableHead className=\"cursor-pointer\" >Seller Name </TableHead>\r\n                                    <TableHead className=\"cursor-pointer\" >Status</TableHead>\r\n                              </TableRow>\r\n                        </TableHeader>\r\n                        <TableBody>\r\n                              {sellerList.data?.filter((x) => handleSearch(x)).sort((a, b) => a.name.localeCompare(b.name)).map((item) => {\r\n\r\n                                    return (\r\n                                          <TableRow key={item.id}>\r\n                                                <TableCell>{item.id}</TableCell>\r\n                                                <TableCell className=\"cursor-pointer text-blue-500 font-bold text-md\" onClick={() => navigate(`/home/<USER>/TableCell>\r\n                                                <TableCell> <Switch checked={item.enabled} /></TableCell>\r\n                                          </TableRow>\r\n                                    )\r\n                              })}\r\n                        </TableBody>\r\n                  </Table> */}\r\n                  <Button className=\"fixed bottom-5 right-5 rounded-full\" onClick={() => navigate(\"/home/<USER>\")}>+ Add Seller</Button>\r\n\r\n\r\n            </div>\r\n      )\r\n\r\n\r\n\r\n}"], "names": ["SellerList", "sellerList", "useLoaderData", "searchTerm", "setSearchTerm", "useState", "navigate", "useNavigate", "SellerHeaders", "jsxs", "className", "children", "jsx", "ResponsiveTable", "headers", "data", "renderRow", "row", "id", "onClick", "name", "enabled", "<PERSON><PERSON>"], "mappings": ";;;;;;;;;AA0BA,SAAwBA,aAAa;AAE/B,QAAMC,aAAaC,cAAkC;AACrD,QAAM,CAACC,YAAYC,aAAa,IAAIC,aAAAA,SAAS,EAAE;AAO/C,QAAMC,WAAWC,YAAY;AAE7B,QAAMC,gBAAgB,CAChB,MACA,eACA,UACA,IACA,EAAA;AAIA,SAAAC,kCAAAA,KAAC,OAAI;AAAA,IAAAC,WAAU;AAAA,IACTC,UAAA,CAACC,kCAAA,IAAA,OAAA;AAAA,MAAIF,WAAU;AAAA,MACTC,UAAAC,kCAAA,IAAC;QAAGF,WAAU;AAAA,QAAqBC;MAAO,CAAA;AAAA,IAChD,CAAA,GAEAC,kCAAA,IAACC,iBAAA;AAAA,MACKC,SAASN;AAAAA,MACTO,MACMd,yCAAYc;AAAAA,MAElBC,WAAYC,SACLR,kCAAA,KAAA,MAAA;AAAA,QAAgBC,WAAU;AAAA,QACrBC,UAAA,CAAAC,kCAAA,IAAC,MAAG;AAAA,UAAAF,WAAU;AAAA,UAAmCC,UAAAM,IAAIC;AAAAA,QAAG,CAAA,GACxDT,kCAAA,KAAC,MAAG;AAAA,UAAAC,WAAU;AAAA,UACRC,UAAA,CAAAC,kCAAA,IAAC,QAAA;AAAA,YACKO,SAASA,MACHb,SAAS,uCAAuCW,IAAIC,EAAE,UAAUD,IAAIG,IAAI,EAAE;AAAA,YAG/ET,UAAIM,IAAAG;AAAAA,UACX,CAAA,GAAO,OAAGH,2BAAKG,SAAQ,GAAA;AAAA,QAC7B,CAAA,yCAEC,MAAG;AAAA,UAAAV,WAAU;AAAA,UACRC,UAACC,kCAAA,IAAA,QAAA;AAAA,YAAKF,WAAW,kCAAkCO,IAAII,YAAY,OAAO,4BAA4B,6BAA6B;AAAA,YAAKV,WAAAM,2BAAKI,aAAY,OAAO,aAAa;AAAA,UAAS,CAAA;AAAA,QAC5L,CAAA,GACAT,kCAAA,IAAC,MAAG;AAAA,UAAAF,WAAU;AAAA,QAEd,CAAA,CAAA;AAAA,MAAA,GAjBGO,IAAIC,EAoBb;AAAA,IAAA,CAEZ,GA8BAN,kCAAA,IAACU;MAAOZ,WAAU;AAAA,MAAsCS,SAASA,MAAMb,SAAS,oBAAoB;AAAA,MAAGK,UAAY;AAAA,IAAA,CAAA,CAAA;AAAA,EAGzH,CAAA;AAKZ;"}