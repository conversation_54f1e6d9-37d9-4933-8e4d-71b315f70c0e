import { r as reactExports, j as jsxRuntimeExports } from "./jsx-runtime-bB2y7OuD.js";
import { B as Button } from "./button-ByAXMyvk.js";
import { I as Input } from "./input-3v87qohQ.js";
import { T as Table, a as TableHeader, b as TableRow, c as TableHead, d as TableBody, e as TableCell } from "./table-CAxdJZsY.js";
import { P as Popover, a as PopoverTrigger, b as PopoverContent } from "./popover-CD2vRFIm.js";
import { C as Calendar, a as Calendar$1 } from "./calendar-_8-DqkPN.js";
import { C as Checkbox } from "./checkbox-DoRUSdrQ.js";
import { S as Select, a as SelectTrigger, b as SelectValue, c as SelectContent, d as SelectItem } from "./select-BFGSXKcr.js";
import { c as Dialog, d as DialogContent, e as DialogTitle } from "./index-DdafHWkt.js";
import { u as useToast } from "./ToastProvider-rciVe3-g.js";
import { S as SpinnerLoader } from "./SpinnerLoader-CAZRmIJX.js";
import { a as useFetcher, u as useLoaderData, F as Form } from "./components-D7UvGag_.js";
import { f as format } from "./format-82yT_5--.js";
import "./index-z_byfFrQ.js";
import "./index-ImHKLo0a.js";
import "./utils-GkgzjW3c.js";
import "./index-D7VH9Fc8.js";
import "./index-BXzPK7u0.js";
import "./index-CzZ6EYkN.js";
import "./index-Vp2vNLNM.js";
import "./index-CEHS9zzk.js";
import "./index-DVTNuYOr.js";
import "./index-DscYByPT.js";
import "./index-CkL5tk39.js";
import "./index-QLGF6kQx.js";
import "./index-dIGjFc0I.js";
import "./createLucideIcon-uwkRm45G.js";
import "./addMonths-Dj4hq91A.js";
import "./isSameDay-BQMn9z7h.js";
import "./addDays-CyH8qBoF.js";
import "./chevron-right-B-tR7Kir.js";
import "./chevron-left-CLqBlTg1.js";
import "./index-CpKiYcZd.js";
import "./check-_dbWxIzT.js";
import "./index-IXOTxK3N.js";
import "./index-qCtcpOcW.js";
import "./index-BTdCMChR.js";
import "./index-C88PRvfd.js";
import "./chevron-down-pCP5jmjX.js";
import "./index-DhHTcibu.js";
function MarkAsPaid({ heading, isOpen, content, isClosed, row }) {
  const fetcher = useFetcher();
  const { showToast } = useToast();
  const isLoading = fetcher.state !== "idle";
  reactExports.useEffect(() => {
    if (fetcher.data) {
      if (fetcher.data !== void 0 && isOpen === true) {
        showToast("Mark As Paid", "success");
        isClosed();
      } else if (fetcher.data === void 0) {
        showToast("Fail To create Agent", "error");
      }
    }
  }, [fetcher.data, isOpen, isClosed, showToast]);
  const handleSubmit = () => {
    const formData = new FormData();
    formData.append("intent", "markAsPaid");
    formData.append("amount", row == null ? void 0 : row.amount);
    formData.append("depositId", row == null ? void 0 : row.id);
    formData.append("note", "Mark as Paid");
    fetcher.submit(formData, { method: "post" });
  };
  return /* @__PURE__ */ jsxRuntimeExports.jsxs(Dialog, { open: isOpen, onOpenChange: isClosed, children: [
    isLoading && /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "absolute inset-0 flex items-center justify-center bg-white/10 backdrop-blur-sm", children: /* @__PURE__ */ jsxRuntimeExports.jsx(SpinnerLoader, { loading: isLoading }) }),
    /* @__PURE__ */ jsxRuntimeExports.jsx(DialogContent, { className: "fixed inset-0 flex items-center justify-center", children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "bg-orange-50 p-6 rounded-lg shadow-lg w-[400px]", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx(DialogTitle, { className: "text-lg font-bold", children: heading }),
      /* @__PURE__ */ jsxRuntimeExports.jsxs("form", { onSubmit: () => handleSubmit(), className: "space-y-4", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx("p", { className: "text-red-400 font-bold text-sm", children: content }),
        /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex justify-end space-x-2", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx(
            "button",
            {
              type: "button",
              onClick: isClosed,
              className: "px-4 py-2 bg-gray-300 rounded",
              children: "Cancel"
            }
          ),
          /* @__PURE__ */ jsxRuntimeExports.jsx(
            "button",
            {
              type: "submit",
              className: "px-4 py-2 bg-blue-600 text-white rounded",
              children: isLoading ? "Submitting..." : "Submit"
            }
          )
        ] })
      ] })
    ] }) })
  ] });
}
function BankTransactions() {
  const {
    bankData,
    adminPermission
  } = useLoaderData();
  const fetcher = useFetcher();
  const [data, setData] = reactExports.useState(bankData);
  const [date, setDate] = reactExports.useState(/* @__PURE__ */ new Date());
  const [filters, setFilters] = reactExports.useState(["ALL"]);
  const [searchTerm, setSearchTerm] = reactExports.useState("");
  const [loading, setLoading] = reactExports.useState(false);
  const [pageSize, setPageSize] = reactExports.useState("100");
  const [currentPage, setCurrentPage] = reactExports.useState(1);
  reactExports.useEffect(() => {
    var _a;
    if ((_a = fetcher.data) == null ? void 0 : _a.bankData) {
      applyFilters(fetcher.data.bankData);
    }
  }, [fetcher.data, filters, searchTerm]);
  const applyFilters = (newData) => {
    if (!Array.isArray(newData)) {
      return [];
    } else {
      const filteredData = newData == null ? void 0 : newData.filter((trans) => {
        var _a, _b, _c, _d;
        return (filters.includes("ALL") || filters.includes(trans.status)) && (((_a = trans.businessName) == null ? void 0 : _a.toLowerCase().includes(searchTerm.toLowerCase())) || ((_b = trans.orderGroupId) == null ? void 0 : _b.toString().includes(searchTerm)) || ((_c = trans.amount) == null ? void 0 : _c.toString().includes(searchTerm)) || ((_d = trans.id) == null ? void 0 : _d.toString().includes(searchTerm)));
      });
      setData(filteredData);
      setCurrentPage(1);
    }
  };
  reactExports.useEffect(() => {
    var _a;
    if ((_a = fetcher.data) == null ? void 0 : _a.bankData) {
      applyFilters(fetcher.data.bankData);
    }
  }, [filters, searchTerm]);
  const handleCheckboxChange = (status) => {
    setFilters((prev) => {
      let newFilters;
      if (status === "ALL") {
        newFilters = ["ALL"];
      } else {
        if (prev.includes(status)) {
          newFilters = prev.filter((x) => x !== status);
        } else {
          newFilters = prev.filter((x) => x !== "ALL").concat(status);
        }
      }
      return newFilters;
    });
  };
  const formatDate = (dateString) => {
    const date2 = new Date(dateString);
    const options = {
      day: "2-digit",
      month: "short",
      hour: "2-digit",
      minute: "2-digit",
      hour12: true
    };
    return date2.toLocaleString("en-US", options);
  };
  const handleSubmit = (date2) => {
    if (!date2) return;
    const formattedDate = new Date(date2);
    fetcher.load(`/home/<USER>"yyyy-MM-dd")}`);
  };
  const paginatedData = reactExports.useMemo(() => {
    const start = (currentPage - 1) * Number(pageSize);
    const end = start + Number(pageSize);
    return [...data].sort((a, b) => a.id > b.id ? -1 : 1).slice(start, end);
  }, [data, currentPage, pageSize]);
  const totalPages = Math.ceil(data.length / Number(pageSize));
  return /* @__PURE__ */ jsxRuntimeExports.jsx(reactExports.Suspense, {
    fallback: /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
      children: "Loading..."
    }),
    children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
      children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
        className: "flex flex-col sm:flex-row sm:space-x-3 my-3",
        children: [/* @__PURE__ */ jsxRuntimeExports.jsxs(Form, {
          className: "flex sm:space-x-2 mb-3 sm:mb-0",
          children: [/* @__PURE__ */ jsxRuntimeExports.jsxs(Popover, {
            children: [/* @__PURE__ */ jsxRuntimeExports.jsx(PopoverTrigger, {
              asChild: true,
              children: /* @__PURE__ */ jsxRuntimeExports.jsxs(Button, {
                variant: "outline",
                className: "w-[280px]",
                children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Calendar, {
                  className: "mr-2 h-4 w-4"
                }), date ? format(date, "PPP") : "Pick a date"]
              })
            }), /* @__PURE__ */ jsxRuntimeExports.jsx(PopoverContent, {
              className: "w-auto p-0",
              children: /* @__PURE__ */ jsxRuntimeExports.jsx(Calendar$1, {
                mode: "single",
                selected: date,
                onSelect: (newDate) => {
                  if (newDate) {
                    setDate(newDate);
                  }
                },
                initialFocus: true
              })
            })]
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
            onClick: () => date && handleSubmit(date),
            children: loading ? "Submitting" : "Get Transactions"
          })]
        }), /* @__PURE__ */ jsxRuntimeExports.jsx(Input, {
          placeholder: "Search by name, id, or order id...",
          value: searchTerm,
          onChange: (e) => setSearchTerm(e.target.value),
          className: "max-w-sm"
        }), /* @__PURE__ */ jsxRuntimeExports.jsxs(Select, {
          value: pageSize,
          onValueChange: setPageSize,
          children: [/* @__PURE__ */ jsxRuntimeExports.jsx(SelectTrigger, {
            className: "w-[180px]",
            children: /* @__PURE__ */ jsxRuntimeExports.jsx(SelectValue, {
              placeholder: "Rows per page"
            })
          }), /* @__PURE__ */ jsxRuntimeExports.jsxs(SelectContent, {
            children: [/* @__PURE__ */ jsxRuntimeExports.jsx(SelectItem, {
              value: "5",
              children: "5 per page"
            }), /* @__PURE__ */ jsxRuntimeExports.jsx(SelectItem, {
              value: "10",
              children: "10 per page"
            }), /* @__PURE__ */ jsxRuntimeExports.jsx(SelectItem, {
              value: "20",
              children: "20 per page"
            }), /* @__PURE__ */ jsxRuntimeExports.jsx(SelectItem, {
              value: "50",
              children: "50 per page"
            }), /* @__PURE__ */ jsxRuntimeExports.jsx(SelectItem, {
              value: "100",
              children: "100 per page"
            })]
          })]
        })]
      }), /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
        className: "flex flex-wrap sm:flex-nowrap sm:space-x-8",
        children: ["ALL", "INITIATED", "PENDING", "PAID", "FAILED"].map((status) => /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
          className: "flex items-center space-x-2 my-2",
          children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Checkbox, {
            id: status,
            checked: filters.includes(status),
            onCheckedChange: () => handleCheckboxChange(status)
          }), /* @__PURE__ */ jsxRuntimeExports.jsx("label", {
            htmlFor: status,
            className: "text-sm font-medium leading-none",
            children: status
          })]
        }, status))
      }), /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
        className: "overflow-x-auto rounded-md border",
        children: /* @__PURE__ */ jsxRuntimeExports.jsxs(Table, {
          children: [/* @__PURE__ */ jsxRuntimeExports.jsx(TableHeader, {
            children: /* @__PURE__ */ jsxRuntimeExports.jsxs(TableRow, {
              children: [/* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
                children: "Id"
              }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
                children: "Name"
              }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
                children: "Amount"
              }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
                children: "Int Time"
              }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
                children: "Status"
              }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
                children: "Last Updated"
              }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
                children: "Paid Time"
              }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
                children: "Order Details"
              }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
                children: "Bank Details"
              })]
            })
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableBody, {
            children: paginatedData.length > 0 ? paginatedData.map((row) => /* @__PURE__ */ jsxRuntimeExports.jsx(TransactionRow, {
              row,
              formatDate,
              adminPermission
            }, row.id)) : /* @__PURE__ */ jsxRuntimeExports.jsx(TableRow, {
              children: /* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
                colSpan: 9,
                className: "h-24 text-center",
                children: "No results."
              })
            })
          })]
        })
      }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
        className: "flex items-center justify-between px-2 py-4",
        children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
          className: "text-sm text-gray-500",
          children: ["Showing ", (currentPage - 1) * Number(pageSize) + 1, " to", " ", Math.min(currentPage * Number(pageSize), data.length), " of", " ", data.length, " results"]
        }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
          className: "flex items-center space-x-2",
          children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
            variant: "outline",
            size: "sm",
            onClick: () => setCurrentPage((prev) => Math.max(prev - 1, 1)),
            disabled: currentPage === 1,
            children: "Previous"
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
            variant: "outline",
            size: "sm",
            onClick: () => setCurrentPage((prev) => Math.min(prev + 1, totalPages)),
            disabled: currentPage === totalPages,
            children: "Next"
          })]
        })]
      })]
    })
  });
}
function TransactionRow({
  row,
  formatDate,
  adminPermission
}) {
  const [showModal, setShowModal] = reactExports.useState(false);
  const [selectedTransaction, setSelectedTransaction] = reactExports.useState(null);
  const fetcher = useFetcher();
  const [loadingRowId, setLoadingRowId] = reactExports.useState(null);
  const handleUpdateTransaction = async (rowId) => {
    setLoadingRowId(rowId);
    const formData = new FormData();
    formData.append("intent", "updateTransaction");
    formData.append("rowId", rowId);
    await fetcher.submit(formData, {
      method: "PUT"
    });
    setLoadingRowId(null);
  };
  const handleShowModal = () => {
    setSelectedTransaction(row);
    setShowModal(true);
  };
  const isLoading = fetcher.state !== "idle";
  return /* @__PURE__ */ jsxRuntimeExports.jsxs(jsxRuntimeExports.Fragment, {
    children: [/* @__PURE__ */ jsxRuntimeExports.jsxs(TableRow, {
      children: [/* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
        children: row.id
      }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
        children: row.businessName
      }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
        children: new Intl.NumberFormat("en-US", {
          style: "currency",
          currency: "INR"
        }).format(parseFloat(row.amount.toString()))
      }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
        children: formatDate(row.initiatedTime)
      }), /* @__PURE__ */ jsxRuntimeExports.jsxs(TableCell, {
        className: "flex flex-col gap-2 mt-2",
        children: [/* @__PURE__ */ jsxRuntimeExports.jsx("button", {
          className: `${row.status === "PENDING" ? "bg-orange-400 hover:bg-orange-200" : row.status === "INITIATED" ? "bg-blue-600 hover:bg-blue-400" : ""} flex-1 cursor-pointer px-2 py-1 rounded flex items-center justify-center`,
          onClick: () => handleUpdateTransaction(row.id.toString()),
          disabled: row.status === "PAID" || row.status === "FAILED" || loadingRowId === row.id.toString(),
          children: loadingRowId === row.id.toString() ? /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
            className: "animate-spin border-2 border-red-200 border-t-transparent rounded-full w-5 h-5"
          }) : /* @__PURE__ */ jsxRuntimeExports.jsx("p", {
            className: row.status === "PAID" ? "font-bold text-green-600" : row.status === "FAILED" ? "font-bold text-red-600" : "",
            children: isLoading ? "updating..." : row.status
          })
        }), (row.status === "FAILED" || row.status === "PENDING") && adminPermission === true && /* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
          className: "bg-sky-400 text-white font-bold",
          onClick: handleShowModal,
          children: "Mark As Paid"
        })]
      }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
        children: formatDate(row.lastUpdatedTime)
      }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
        children: formatDate(row.completedTime)
      }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
        children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
          className: "space-y-1",
          children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("p", {
            children: ["Id: ", row.orderGroupId]
          }), /* @__PURE__ */ jsxRuntimeExports.jsx("p", {
            children: row.orderStatus
          }), /* @__PURE__ */ jsxRuntimeExports.jsx("p", {
            children: row.sellerName
          }), /* @__PURE__ */ jsxRuntimeExports.jsx("p", {
            children: row.deliveryDate
          })]
        })
      }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
        children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
          className: "space-y-1",
          children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("p", {
            children: ["Id: ", row.bankRRN]
          }), /* @__PURE__ */ jsxRuntimeExports.jsxs("p", {
            children: ["Ch: ", row.channel]
          }), /* @__PURE__ */ jsxRuntimeExports.jsxs("p", {
            children: ["Note: ", row.note]
          })]
        })
      })]
    }, row.id), /* @__PURE__ */ jsxRuntimeExports.jsx(MarkAsPaid, {
      isClosed: () => setShowModal(false),
      heading: `Transaction from ${(selectedTransaction == null ? void 0 : selectedTransaction.businessName) ?? ""}`,
      isOpen: showModal,
      content: "You are about to mark the payment status as paid. This is an irreversible action.",
      row: selectedTransaction
    })]
  });
}
export {
  BankTransactions as default
};
//# sourceMappingURL=home.bankTransactions-wl7n8wS-.js.map
