import { j as jsxRuntimeExports } from "./jsx-runtime-bB2y7OuD.js";
import { u as useLoaderData } from "./components-D7UvGag_.js";
import "./index-Vp2vNLNM.js";
import "./index-DhHTcibu.js";
function Dashboard() {
  const {
    embedUrl
  } = useLoaderData();
  return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
    className: "p-6",
    children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
      className: "mb-6",
      children: [/* @__PURE__ */ jsxRuntimeExports.jsx("h1", {
        className: "text-3xl font-bold text-gray-900",
        children: "Dashboard"
      }), /* @__PURE__ */ jsxRuntimeExports.jsx("p", {
        className: "text-gray-600 mt-2",
        children: "Overview of your restaurant performance"
      })]
    }), /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
      className: "w-full h-screen",
      children: embedUrl ? /* @__PURE__ */ jsxRuntimeExports.jsx("iframe", {
        id: "metabase-iframe",
        src: embedUrl,
        title: "Restaurant Dashboard",
        className: "w-full h-full border-0"
      }) : /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
        className: "flex items-center justify-center min-h-[400px]",
        children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
          className: "text-center",
          children: [/* @__PURE__ */ jsxRuntimeExports.jsx("div", {
            className: "mx-auto mb-4 w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center",
            children: /* @__PURE__ */ jsxRuntimeExports.jsx("svg", {
              className: "w-8 h-8 text-white",
              fill: "none",
              stroke: "currentColor",
              viewBox: "0 0 24 24",
              children: /* @__PURE__ */ jsxRuntimeExports.jsx("path", {
                strokeLinecap: "round",
                strokeLinejoin: "round",
                strokeWidth: 2,
                d: "M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
              })
            })
          }), /* @__PURE__ */ jsxRuntimeExports.jsx("h2", {
            className: "text-2xl font-bold mb-2",
            children: "Dashboard Loading"
          }), /* @__PURE__ */ jsxRuntimeExports.jsx("p", {
            className: "text-gray-500",
            children: "Loading your restaurant analytics..."
          })]
        })
      })
    })]
  });
}
export {
  Dashboard as default
};
//# sourceMappingURL=sellerSetting.dashboard-DJMAwsYp.js.map
