{"version": 3, "file": "home.sellerDetails-CnMRRK_f.js", "sources": ["../../../app/components/ui/multiSelect.tsx", "../../../app/components/ui/creatUser.tsx", "../../../app/components/ui/sellerConfigDetails.tsx", "../../../app/components/ui/mapComponet.tsx", "../../../app/components/ui/billingConfi.tsx", "../../../app/components/common/EditModal.tsx", "../../../app/components/common/AddonsGropModal.tsx", "../../../app/components/common/AddonsGroupTab.tsx", "../../../app/components/common/AddonsModal.tsx", "../../../app/components/common/AddonsTab.tsx", "../../../app/components/common/AddVariation.tsx", "../../../app/components/common/VariationTab.tsx", "../../../app/routes/home.sellerDetails.tsx"], "sourcesContent": ["import * as React from \"react\";\r\nimport { Check, ChevronDown } from \"lucide-react\";\r\nimport { cn } from \"~/lib/utils\";\r\n\r\ninterface MultiSelectProps {\r\n      options: { value: string; label: string }[];\r\n      selectedValues: string[];\r\n      onChange: (values: string[]) => void;\r\n      placeholder?: string;\r\n      className?: string;\r\n      height?: boolean\r\n}\r\n\r\nconst MultiSelect: React.FC<MultiSelectProps> = ({\r\n      options,\r\n      selectedValues,\r\n      onChange,\r\n      placeholder = \"Select...\",\r\n      className,\r\n      height\r\n}) => {\r\n      const [isOpen, setIsOpen] = React.useState(false);\r\n\r\n      const toggleValue = (value: string) => {\r\n            onChange(\r\n                  selectedValues?.includes(value)\r\n                        ? selectedValues.filter((v) => v !== value)\r\n                        : [...selectedValues, value]\r\n            );\r\n      };\r\n\r\n      return (\r\n            <div className=\"relative w-full\">\r\n                  {/* Trigger Button */}\r\n                  <button\r\n                        type=\"button\"\r\n                        onClick={() => setIsOpen(!isOpen)}\r\n                        className={cn(\r\n                              \"flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\r\n                              className\r\n                        )}\r\n                  >\r\n                        <span className=\"truncate\">\r\n                              {selectedValues?.length > 2\r\n                                    ? `${options?.find((opt) => opt?.value === selectedValues[0])?.label}, ${options?.find((opt) => opt?.value === selectedValues[1])?.label\r\n                                    } +${selectedValues?.length - 2} more`\r\n                                    : selectedValues\r\n                                          .map((value) => options?.find((opt) => opt?.value === value)?.label)\r\n                                          .join(\", \") || placeholder}\r\n                        </span>\r\n                        <ChevronDown className=\"h-4 w-4 opacity-50\" />\r\n                  </button>\r\n\r\n                  {/* Dropdown Menu with Scroll */}\r\n                  {isOpen && (\r\n  <div\r\n    className={cn(\r\n      \"absolute z-50 mt-1 w-full max-h-60 overflow-y-auto rounded-md border bg-white shadow-md\",\r\n      className\r\n    )}\r\n    onWheel={e => e.stopPropagation()}\r\n    onTouchMove={e => e.stopPropagation()}\r\n  >\r\n    {options?.map((option) => (\r\n      <div\r\n        key={option?.value}\r\n        className={cn(\r\n          \"flex cursor-pointer items-center px-4 py-2 text-sm hover:bg-gray-100\",\r\n          selectedValues?.includes(option?.value) && \"bg-gray-200\"\r\n        )}\r\n        onClick={() => toggleValue(option.value)}\r\n      >\r\n        {selectedValues?.includes(option.value) && (\r\n          <Check className=\"h-4 w-4 text-primary mr-2\" />\r\n        )}\r\n        {option?.label}\r\n      </div>\r\n    ))}\r\n  </div>\r\n)}\r\n            </div>\r\n      );\r\n};\r\n\r\nexport default MultiSelect;\r\n", "import { useEffect, useState } from \"react\";\r\nimport { useFetcher } from \"@remix-run/react\";\r\nimport { Input } from \"./input\";\r\nimport { Button } from \"./button\";\r\nimport { useToast } from \"./ToastProvider\";\r\nimport MultiSelect from \"./multiSelect\";\r\nimport { Label } from \"./label\";\r\nimport { Dialog, DialogContent, DialogTitle } from \"./dialog\";\r\nimport SpinnerLoader from \"../loader/SpinnerLoader\";\r\n\r\ninterface RoleOption {\r\n      value: string;\r\n      label: string;\r\n}\r\n\r\nexport interface User {\r\n      id: number;\r\n      firstName: string;\r\n      lastName: string;\r\n      email: string;\r\n      mobileNumber: string;\r\n      address: string;\r\n      roles: string[];\r\n      businessId: number;\r\n}\r\n\r\ninterface CreateUserProps {\r\n      isOpen: boolean;\r\n      onClose: () => void;\r\n      sellerId: number;\r\n      roles: RoleOption[];\r\n      sellerBId: number;\r\n      user?: any\r\n\r\n\r\n}\r\n\r\nexport default function CreateUser({\r\n      isOpen,\r\n      onClose,\r\n      sellerId,\r\n      roles,\r\n      user,\r\n      sellerBId\r\n}: CreateUserProps) {\r\n      const fetcher = useFetcher();\r\n      const { showToast } = useToast();\r\n\r\n      // State to manage the form data\r\n      const [formData, setFormData] = useState({\r\n            firstName: \"\",\r\n            lastName: \"\",\r\n            email: \"\",\r\n            mobileNumber: \"\",\r\n            address: \"\",\r\n            password: \"\",\r\n            businessId: sellerBId, // Default to sellerId\r\n            roles: [] as string[],\r\n      });\r\n\r\n      // Multi-select state for roles\r\n      const [selectedRoles, setSelectedRoles] = useState<string[]>([]);\r\n\r\n      // Prefill the form when in edit mode\r\n      useEffect(() => {\r\n            if (user) {\r\n                  console.log(user, \"2222222222222222222\")\r\n                  setFormData({\r\n                        firstName: user?.firstName,\r\n                        lastName: user?.lastName,\r\n                        email: user?.email,\r\n                        mobileNumber: user?.mobileNumber,\r\n                        address: user?.address,\r\n                        password: \"\", // Keep it empty when editing\r\n                        businessId: user?.businessId, // Use user's business ID\r\n                        roles: user?.roles,\r\n                  });\r\n                  setSelectedRoles(user.roles);\r\n            }\r\n      }, [user]);\r\n\r\n      const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n            const { name, value } = e.target;\r\n            setFormData((prev) => ({ ...prev, [name]: value }));\r\n      };\r\n\r\n\r\n      const handleSubmit = async (e: React.FormEvent) => {\r\n            e.preventDefault();\r\n\r\n            const userData = {\r\n                  ...formData,\r\n                  roles: selectedRoles,\r\n                  businessId: sellerBId, // Send sellerBId as businessId\r\n            };\r\n\r\n            const formDataToSend = new FormData();\r\n            console.log(sellerBId, sellerId, \"999999999999999999\")\r\n            formDataToSend.append(\"sellerId\", sellerId as unknown as string);\r\n            formDataToSend.append(\"userId\", user?.userId as unknown as string);\r\n\r\n            formDataToSend.append(\"intent\", user ? \"editUser\" : \"createUser\");\r\n            formDataToSend.append(\"userData\", JSON.stringify(userData));\r\n\r\n            fetcher.submit(formDataToSend, {\r\n                  method: \"POST\",\r\n                  encType: \"multipart/form-data\",\r\n            });\r\n\r\n            // showToast(user ? \"User successfully updated\" : \"User successfully created\", \"success\");\r\n            // onClose();\r\n      };\r\n\r\n      const isLoading = fetcher.state !== \"idle\"\r\n\r\n      useEffect(() => {\r\n\r\n            if (fetcher.data && isOpen === true) {\r\n                  if (fetcher.data) {\r\n                        showToast(user ? \"User successfully updated\" : \"User successfully created\", \"success\");\r\n                        onClose();\r\n                        setFormData({\r\n                              firstName: \"\",\r\n                              lastName: \"\",\r\n                              email: \"\",\r\n                              mobileNumber: \"\",\r\n                              address: \"\",\r\n                              password: \"\",\r\n                              businessId: sellerBId, // Default to sellerId\r\n                              roles: [] as string[]\r\n                        })\r\n                  }\r\n                  else {\r\n                        showToast(user ? \"User  update Failed\" : \"User creation failed \", \"error\");\r\n\r\n                  }\r\n            }\r\n      }, [fetcher.data])\r\n\r\n\r\n      return (\r\n            <Dialog open={isOpen} onOpenChange={onClose}>\r\n                  <DialogContent className=\"max-h-[60vh] overflow-y-auto\">\r\n                        {isLoading && (\r\n                              <div className=\"absolute inset-0 flex items-center justify-center bg-white/10 backdrop-blur-sm\">\r\n                                    <SpinnerLoader loading={isLoading} />\r\n                              </div>\r\n                        )}\r\n\r\n                        <DialogTitle className=\"font-bold\">\r\n                              {user ? \"Edit User\" : \"Add New User\"}\r\n                        </DialogTitle>\r\n                        <form onSubmit={handleSubmit} className=\"space-y-4 \">\r\n                              <div className=\"flex flex-col md:flex-row gap-2\">\r\n                                    <Label className=\"flex-1 flex flex-col gap-y-2 font-semibold\">\r\n                                          First Name\r\n                                          <Input\r\n                                                name=\"firstName\"\r\n                                                placeholder=\"First Name\"\r\n                                                onChange={handleChange}\r\n                                                value={formData.firstName}\r\n                                                required\r\n                                          />\r\n                                    </Label>\r\n                                    <Label className=\"flex-1 flex flex-col gap-y-2\">\r\n                                          Last Name\r\n                                          <Input\r\n                                                name=\"lastName\"\r\n                                                placeholder=\"Last Name\"\r\n                                                onChange={handleChange}\r\n                                                value={formData.lastName}\r\n                                                required\r\n                                          />\r\n                                    </Label>\r\n                              </div>\r\n\r\n                              <div className=\"flex flex-col md:flex-row gap-2\">\r\n                                    {!user && <Label className=\"flex-1 flex flex-col gap-y-2\">\r\n                                          Email\r\n                                          <Input\r\n                                                type=\"email\"\r\n                                                name=\"email\"\r\n                                                placeholder=\"Email\"\r\n                                                onChange={handleChange}\r\n                                                value={formData.email}\r\n                                                required\r\n                                          />\r\n                                    </Label>}\r\n                                    <Label className=\"flex-1 flex flex-col gap-y-2\">\r\n                                          Mobile Number\r\n                                          <Input\r\n                                                type=\"number\"\r\n                                                name=\"mobileNumber\"\r\n                                                placeholder=\"Mobile Number\"\r\n                                                onChange={(e) => {\r\n                                                      const value = e.target.value.replace(/\\D/g, \"\");\r\n                                                      if (value.length <= 10) {\r\n                                                            setFormData((prev) => ({\r\n                                                                  ...prev,\r\n                                                                  mobileNumber: value\r\n\r\n\r\n                                                            }))\r\n                                                      }\r\n\r\n                                                }}\r\n                                                value={formData.mobileNumber}\r\n                                                required\r\n                                          />\r\n                                    </Label>\r\n                              </div>\r\n\r\n                              <div className=\"flex flex-col md:flex-row gap-2\">\r\n                                    {!user && <Label className=\"flex-1 flex flex-col gap-y-2\">\r\n                                          Address\r\n                                          <Input\r\n                                                name=\"address\"\r\n                                                placeholder=\"Address\"\r\n                                                onChange={handleChange}\r\n                                                value={formData.address}\r\n                                                required\r\n                                          />\r\n                                    </Label>}\r\n                                    <Label className=\"flex-1 flex flex-col gap-y-2\">\r\n                                          Select Roles\r\n                                          <MultiSelect\r\n                                                options={roles}\r\n                                                selectedValues={selectedRoles ? selectedRoles : []}\r\n                                                onChange={setSelectedRoles}\r\n                                                placeholder=\"Select Roles\"\r\n                                                className=\"w-full\"\r\n                                                height={true}\r\n                                          />\r\n                                    </Label>\r\n                              </div>\r\n\r\n                              <div className=\"flex justify-end\">\r\n                                    <Button type=\"submit\" className=\"rounded-full\">\r\n                                          {user ? \"Update User\" : \"Save User\"}\r\n                                    </Button>\r\n                              </div>\r\n                        </form>\r\n                  </DialogContent>\r\n            </Dialog>\r\n      );\r\n}\r\n", "import { Edit, Save, X } from \"lucide-react\";\r\nimport { RadioGroup, RadioGroupItem } from \"./radio-group\";\r\nimport { Label } from \"./label\";\r\nimport { SellerConfig } from \"~/types/api/businessConsoleService/SellerManagement\";\r\nimport { useEffect, useState } from \"react\";\r\nimport { Input } from \"./input\";\r\n\r\ninterface SellerProps {\r\n      sellerConfig: SellerConfig;\r\n      onAttributeUpdate: (data: Record<string, string>) => Promise<void>;\r\n}\r\n\r\nexport default function SellerConfigDetails({ sellerConfig, onAttributeUpdate }: SellerProps) {\r\n      const [config, setConfig] = useState<SellerConfig>(sellerConfig);\r\n      const [error, setError] = useState<string | null>(null);\r\n\r\n      // Pending state for each section:\r\n      const [isEditingOperation, setIsEditingOperation] = useState(false);\r\n      const [pendingOperation, setPendingOperation] = useState<Partial<SellerConfig>>({});\r\n\r\n      const [isEditingDelivery, setIsEditingDelivery] = useState(false);\r\n      const [pendingDelivery, setPendingDelivery] = useState<Partial<SellerConfig>>({});\r\n\r\n      const [isEditingBusiness, setIsEditingBusiness] = useState(false);\r\n      const [pendingBusiness, setPendingBusiness] = useState<Partial<SellerConfig>>({});\r\n\r\n      const [isEditingPackaging, setIsEditingPackaging] = useState(false);\r\n      const [pendingPackaging, setPendingPackaging] = useState<Partial<SellerConfig>>({});\r\n\r\n      const [isEditingInventory, setIsEditingInventory] = useState(false);\r\n      const [pendingInventory, setPendingInventory] = useState<Partial<SellerConfig>>({});\r\n\r\n      useEffect(() => {\r\n            if (sellerConfig) {\r\n                  setConfig(sellerConfig);\r\n            }\r\n      }, [sellerConfig]);\r\n\r\n      // Map internal keys to camelCase names expected by backend\r\n      const keyMapping: Record<keyof SellerConfig, string> = {\r\n            id: \"id\",\r\n            name: \"name\",\r\n            enabled: \"enabled\",\r\n            auto_accept: \"autoAccept\",\r\n            auto_pack: \"autoPack\",\r\n            auto_pickup: \"autoPickup\",\r\n            auto_dispatch: \"autoDispatch\",\r\n            listing_seq: \"listingSequence\",\r\n            mininum_required_balance: \"minimumRequiredBalance\",\r\n            allow_cod: \"allowCoD\",\r\n            minimum_order_qty: \"minimumOrderQty\",\r\n            minimum_order_value: \"minimumOrderValue\",\r\n            wa_enable: \"waEnable\",\r\n            approx_pricing: \"approxPricing\",\r\n            approxPriceVisibility: \"approxPriceVisibility\",\r\n            strikeoff_enabled: \"strikeoffEnabled\",\r\n            item_pick_enabled: \"itemPickEnabled\",\r\n            contract_price_enabled: \"contractPriceEnabled\",\r\n            delivery_type: \"deliveryType\",\r\n            fav_items_enabled: \"favItemsEnabled\",\r\n            category_level: \"categoryLevel\",\r\n            business_id: \"businessId\",\r\n            dispatch_time: \"dispatchTime\",\r\n            delivery_time: \"deliveryTime\",\r\n            approxDelDateVisibility: \"approxDelDateVisibility\",\r\n            instantDeliveryTime: \"instantDeliveryTime\",\r\n            booking_close_time: \"bookingCloseTime\",\r\n            booking_open_time: \"bookingOpenTime\",\r\n            is_pay_later_enabled: \"isPayLaterEnabled\",\r\n            distanceBasedDel: \"distanceBasedDel\",\r\n            deliveryDistance: \"deliveryDistance\",\r\n            auto_activate: \"autoActivate\",\r\n            advance_booking_days: \"advanceBookingDays\",\r\n            miSources: \"miSources\",\r\n            t1Open: \"t1Open\",\r\n            t1Close: \"t1Close\",\r\n            t2Open: \"t2Open\",\r\n            t2Close: \"t2Close\",\r\n            t3Open: \"t3Open\",\r\n            t3Close: \"t3Close\",\r\n            menuId: \"menuId\",\r\n            pos: \"pos\",\r\n            posCustId: \"posCustId\",\r\n            posRetName: \"posRetName\",\r\n            posRetContactNo: \"posRetContactNo\",\r\n            posRetAddress: \"posRetAddress\",\r\n            outletId: \"outletId\",\r\n            ondcDomain: \"ondcDomain\",\r\n            defaultOrderPrepTime: \"defaultOrderPrepTime\",\r\n            spCustId: \"spCustId\",\r\n            spId: \"spId\",\r\n            sourceSystem: \"sourceSystem\",\r\n            logisticProvider: \"logisticProvider\",\r\n            packagingCharge: \"packagingCharge\",\r\n            packagingChargeType: \"packagingChargeType\",\r\n            packagingApplicableOn: \"packagingApplicableOn\",\r\n            platformFee: \"platformFee\",\r\n            platformFeePerc: \"platformFeePerc\",\r\n            platformFeePkg: \"platformFeePkg\",\r\n            sytem: \"sytem\"\r\n      };\r\n\r\n      const [validationErrors, setValidationErrors] = useState<Partial<Record<keyof SellerConfig, string>>>();\r\n\r\n      // Timing Helpers for Booking Timings\r\n      const getDisplayedBookingTime = (bookingTime: number | string): string => {\r\n            console.log(\"getDisplayedBookingTime Trigger for\", bookingTime);\r\n            const dispatch = Number(config.dispatch_time.split(\":\")[0]);\r\n            let bookingInHours: number;\r\n            if (typeof bookingTime === \"string\") {\r\n                  const [h, m] = bookingTime.split(\":\").map(Number);\r\n                  bookingInHours = h + m / 60;\r\n            } else {\r\n                  bookingInHours = bookingTime / 60;\r\n            }\r\n            if (dispatch < bookingInHours) bookingInHours += 24;\r\n            const displayedHours = dispatch - bookingInHours;\r\n            const hours = Math.floor(displayedHours);\r\n            const minutes = Math.round((displayedHours - hours) * 60);\r\n            const displayedString = `${hours.toString().padStart(2, \"0\")}:${minutes.toString().padStart(2, \"0\")}`;\r\n            console.log(\"getDisplayedBookingTime return value\", displayedString);\r\n            return displayedString;\r\n      };\r\n\r\n      const convertDisplayedBookingToBE = (displayed: string): number => {\r\n            const [h, m] = displayed.split(\":\").map(Number);\r\n            if (isNaN(h) || isNaN(m)) {\r\n                  throw new Error(\"Invalid displayed time format\");\r\n            }\r\n            const inputHours = h + m / 60;\r\n            const dispatch = Number(config.dispatch_time.split(\":\")[0]);\r\n            let beValue = (dispatch - inputHours) * 60;\r\n            if (beValue < 0) beValue += 24 * 60;\r\n            const fixedBeValue = Math.round(beValue * 100) / 100;\r\n            console.log(\"convertDisplayedBookingToBE return value\", fixedBeValue);\r\n            return fixedBeValue;\r\n      };\r\n\r\n      // Helper to update pending state for a section\r\n      const updatePending = (\r\n            section: \"operation\" | \"delivery\" | \"business\" | \"packaging\" | \"inventory\",\r\n            key: keyof SellerConfig,\r\n            value: any\r\n      ) => {\r\n            console.log(`updatePending: section=${section}, key=${key}, value=`, value);\r\n            if (section === \"operation\") {\r\n                  setPendingOperation(prev => ({ ...prev, [key]: value }));\r\n            } else if (section === \"delivery\") {\r\n                  setPendingDelivery(prev => ({ ...prev, [key]: value }));\r\n            } else if (section === \"business\") {\r\n                  setPendingBusiness(prev => ({ ...prev, [key]: value }));\r\n            } else if (section === \"packaging\") {\r\n                  setPendingPackaging(prev => ({ ...prev, [key]: value }));\r\n            } else if (section === \"inventory\") {\r\n                  setPendingInventory(prev => ({ ...prev, [key]: value }));\r\n            }\r\n      };\r\n\r\n      // Field definitions\r\n      const basicFields = [\r\n            { label: \"ID\", key: \"id\" },\r\n            { label: \"Name\", key: \"name\" },\r\n      ];\r\n\r\n      const operationalConfigFields = [\r\n            { label: \"Are the orders required to be accepted manually?\", key: \"auto_accept\" },\r\n            { label: \"Are the orders required to be packed manually?\", key: \"auto_pack\" },\r\n            { label: \"Are the orders required to be picked manually?\", key: \"auto_pickup\" },\r\n            { label: \"Are the orders required to be dispatched manually?\", key: \"auto_dispatch\" },\r\n            { label: \"Are the items required to be picked individually?\", key: \"item_pick_enabled\" },\r\n      ];\r\n\r\n      const deliveryConfigRadioFields = [\r\n            { label: \"Is Cash on delivery allowed?\", key: \"allow_cod\" },\r\n            { label: \"Is Buy now pay later (credit) allowed?\", key: \"is_pay_later_enabled\" },\r\n            { label: \"Distance based delivery restriction:\", key: \"distanceBasedDel\" },\r\n            { label: \"Display Approx Delivery Date?\", key: \"approxDelDateVisibility\" },\r\n      ];\r\n\r\n      const deliveryConfigOptions = [\r\n            {\r\n                  label: \"Delivery Type\",\r\n                  key: \"delivery_type\",\r\n                  options: [\r\n                        { id: \"delivery_type-daily\", value: \"daily\", label: \"🚚 Daily\" },\r\n                        { id: \"delivery_type-instant\", value: \"instant\", label: \"🏍️ Instant\" },\r\n                  ],\r\n            },\r\n            {\r\n                  label: \"Point of Sale\",\r\n                  key: \"pos\",\r\n                  options: [\r\n                        { id: \"pos-none\", value: \"none\", label: \"None\" },\r\n                        { id: \"pos-petpooja\", value: \"petpooja\", label: \"Petpooja\" },\r\n                  ],\r\n            },\r\n            {\r\n                  label: \"Source System\",\r\n                  key: \"sourceSystem\",\r\n                  options: [\r\n                        { id: \"sourceSystem-mnet\", value: \"MNET\", label: \"MNET\" },\r\n                        { id: \"sourceSystem-ondc\", value: \"ONDC\", label: \"ONDC\" },\r\n                  ],\r\n            },\r\n            {\r\n                  label: \" Platform\",\r\n                  key: \"platform\",\r\n                  options: [\r\n                        { id: \"platform-mNET\", value: \"mnet\", label: \"mNET\" },\r\n                        { id: \"platform-fM\", value: \"fm\", label: \"fM\" },\r\n                  ],\r\n            },\r\n            {\r\n                  label: \"ONDC Domain\",\r\n                  key: \"ondcDomain\",\r\n                  options: [\r\n                        { id: \"ondcDomain-ret11\", value: \"RET11\", label: \"Restaurants (RET11)\" },\r\n                        { id: \"ondcDomain-ret10\", value: \"RET10\", label: \"Others (RET10)\" },\r\n                  ],\r\n            },\r\n      ];\r\n\r\n      const businessConfigFields = [\r\n            { label: \"Business Listing Sequence:\", key: \"listing_seq\", type: \"number\", prefix: \"\" },\r\n            { label: \"Minimum Required Balance:\", key: \"mininum_required_balance\", type: \"number\", prefix: \"₹\" },\r\n            { label: \"Platform Fee (Fixed):\", key: \"platformFee\", type: \"number\", prefix: \"₹\" },\r\n            { label: \"Platform Fee (%)\", key: \"platformFeePerc\", type: \"number\", prefix: \"%\" },\r\n            { label: \"Platform Fee per Kg:\", key: \"platformFeePkg\", type: \"number\", prefix: \"/kg\" },\r\n      ];\r\n\r\n      const businessConfigRadioFields = [\r\n            { label: \"Is WhatsApp Enabled?\", key: \"wa_enable\" },\r\n      ];\r\n\r\n      const inventoryConfigRadioFields = [\r\n            { label: \"Is the Inventory required to be opened manually?\", key: \"auto_activate\" },\r\n            { label: \"Is item pricing approximate?\", key: \"approx_pricing\" },\r\n            { label: \"Display Prices?\", key: \"approxPriceVisibility\" },\r\n            { label: \"Is item level discounting allowed?\", key: \"strikeoff_enabled\" },\r\n            { label: \"Is item x buyer level prices fixed? (Contract Pricing)\", key: \"contract_price_enabled\" },\r\n            { label: \"Are previously bought items recommended?\", key: \"fav_items_enabled\" },\r\n      ];\r\n\r\n      const inventoryConfigFields = [\r\n            { label: \"Minimum Order Qty:\", key: \"minimum_order_qty\", type: \"number\", prefix: \"\", suffix: \"kg\" },\r\n            { label: \"Advance Booking duration:\", key: \"advance_booking_days\", type: \"number\", prefix: \"\", suffix: \"days\" },\r\n            { label: \"Minimum Order Value:\", key: \"minimum_order_value\", type: \"number\", prefix: \"₹\", suffix: \"\" },\r\n            { label: \"MI Sources:\", key: \"miSources\", type: \"text\", prefix: \"\", suffix: \"\" },\r\n            {\r\n                  label: \"Logistic Partner:\",\r\n                  key: \"logisticProvider\",\r\n                  type: \"select\", // Change type to \"select\" to indicate a dropdown\r\n                  prefix: \"\",\r\n                  suffix: \"\",\r\n                  options: [\r\n                        { value: \"MP2\", label: \"MP2\" },\r\n                        { value: \"SELF\", label: \"SELF\" },\r\n                  ],\r\n            },\r\n      ];\r\n      // Save handler\r\n      const handleSaveSection = async (section: \"operation\" | \"delivery\" | \"business\" | \"packaging\" | \"inventory\") => {\r\n            let pending: Partial<SellerConfig> = {};\r\n            if (section === \"operation\") pending = pendingOperation;\r\n            else if (section === \"delivery\") pending = pendingDelivery;\r\n            else if (section === \"business\") pending = pendingBusiness;\r\n            else if (section === \"packaging\") pending = pendingPackaging;\r\n            else if (section === \"inventory\") pending = pendingInventory;\r\n\r\n            //validations\r\n            let errors: Partial<Record<keyof SellerConfig, string>> = {};\r\n            if (section === \"delivery\") {\r\n                  if (pending.pos !== undefined && pending.pos === \"petpooja\") {\r\n                        if (!pending.menuId) errors.menuId = \"Menu ID is required\";\r\n                        if (!pending.posCustId) errors.posCustId = \"POS Customer ID is required\";\r\n                        if (!pending.posRetName) errors.posRetName = \"POS Restaurant Name is required\";\r\n                        if (!pending.posRetContactNo) errors.posRetContactNo = \"POS Restaurant Contact Number is required\";\r\n                        if (!pending.posRetAddress) errors.posRetAddress = \"POS Restaurant Address is required\";\r\n                  }\r\n                  if (pending.ondcDomain !== undefined && pending.ondcDomain === \"RET11\") {\r\n                        if (!pending.defaultOrderPrepTime) errors.defaultOrderPrepTime = \"Default Order Preparation Time is required\";\r\n                  }\r\n            }\r\n            if (section === \"packaging\") {\r\n                  if (pending.packagingCharge === undefined || pending.packagingCharge === null || String(pending.packagingCharge) === \"\") errors.packagingCharge = \"Packaging Charge is required\";\r\n                  if (!pending.packagingChargeType) errors.packagingChargeType = \"Packaging Charge Type is required\";\r\n                  if (!pending.packagingApplicableOn) errors.packagingApplicableOn = \"Packaging Applicable On is required\";\r\n            }\r\n            if (errors && Object.keys(errors).length > 0) {\r\n                  setValidationErrors(errors);\r\n                  return;\r\n            } else {\r\n                  setValidationErrors({});\r\n            }\r\n\r\n            // Filter out null or undefined values\r\n            const updateArray = Object.entries(pending)\r\n                  .filter(([_, value]) => value !== null && value !== undefined)\r\n                  .map(([key, value]) => {\r\n                        const convertedValue = typeof value === \"boolean\" ? (value ? true : false) : value;\r\n                        return { attribute: keyMapping[key as keyof SellerConfig], value: convertedValue };\r\n                  });\r\n\r\n            if (updateArray.length === 0) {\r\n                  setError(\"No changes to save\");\r\n                  return;\r\n            }\r\n\r\n            console.log(\"Prepared update array:\", updateArray);\r\n            try {\r\n                  await onAttributeUpdate({ updates: JSON.stringify(updateArray) });\r\n                  console.log(\"handleSaveSection: updates sent\", updateArray);\r\n                  setConfig(prev => ({ ...prev, ...pending }));\r\n                  setError(null);\r\n\r\n                  if (section === \"operation\") {\r\n                        setPendingOperation({});\r\n                        setIsEditingOperation(false);\r\n                  } else if (section === \"delivery\") {\r\n                        setPendingDelivery({});\r\n                        setIsEditingDelivery(false);\r\n                  } else if (section === \"business\") {\r\n                        setPendingBusiness({});\r\n                        setIsEditingBusiness(false);\r\n                  } else if (section === \"packaging\") {\r\n                        setPendingPackaging({});\r\n                        setIsEditingPackaging(false);\r\n                  } else if (section === \"inventory\") {\r\n                        setPendingInventory({});\r\n                        setIsEditingInventory(false);\r\n                  }\r\n            } catch (error) {\r\n                  console.error(\"Error in handleSaveSection:\", error);\r\n                  setError(error instanceof Error ? error.message : \"Failed to save changes\");\r\n            }\r\n      };\r\n\r\n      const handleCancelSection = (section: \"operation\" | \"delivery\" | \"business\" | \"packaging\" | \"inventory\") => {\r\n            if (section === \"operation\") {\r\n                  setPendingOperation({});\r\n                  setIsEditingOperation(false);\r\n            } else if (section === \"delivery\") {\r\n                  setPendingDelivery({});\r\n                  setIsEditingDelivery(false);\r\n            } else if (section === \"business\") {\r\n                  setPendingBusiness({});\r\n                  setIsEditingBusiness(false);\r\n            } else if (section === \"packaging\") {\r\n                  setPendingPackaging({});\r\n                  setIsEditingPackaging(false);\r\n            } else if (section === \"inventory\") {\r\n                  setPendingInventory({});\r\n                  setIsEditingInventory(false);\r\n            }\r\n            setError(null);\r\n            console.log(`Cancelled editing for ${section}`);\r\n      };\r\n\r\n      return (\r\n            <div className=\"flex flex-col gap-4 border bg-white rounded-xl border-neutral-200 my-4 p-4\">\r\n                  {error && (\r\n                        <div className=\"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative\" role=\"alert\">\r\n                              <span className=\"block sm:inline\">{error}</span>\r\n                        </div>\r\n                  )}\r\n                  {/* Basic Configurations */}\r\n                  <div className=\"flex flex-col gap-4 border rounded-xl border-neutral-400 p-4 shadow\">\r\n                        <div className=\"text-lg font-semibold text-typography-700\">Basic Configurations</div>\r\n                        <div className=\"flex flex-col gap-1\">\r\n                              {basicFields.map(({ label, key }) => (\r\n                                    <div key={key} className=\"text-sm text-typography-300\">\r\n                                          {label}: {config[key as keyof SellerConfig]}\r\n                                    </div>\r\n                              ))}\r\n                        </div>\r\n                  </div>\r\n\r\n                  {/* Operational Configurations */}\r\n                  <div className=\"flex flex-col gap-4 border rounded-xl border-neutral-400 p-4 shadow\">\r\n                        <div className=\"flex justify-between\">\r\n                              <div className=\"text-lg font-semibold text-typography-700\">Operational Configurations</div>\r\n                              <button\r\n                                    onClick={() => {\r\n                                          if (!isEditingOperation) {\r\n                                                setPendingOperation({\r\n                                                      auto_accept: config.auto_accept,\r\n                                                      auto_pack: config.auto_pack,\r\n                                                      auto_pickup: config.auto_pickup,\r\n                                                      auto_dispatch: config.auto_dispatch,\r\n                                                      item_pick_enabled: config.item_pick_enabled,\r\n                                                });\r\n                                                console.log(\"Entering edit mode for Operational\");\r\n                                          }\r\n                                          setIsEditingOperation(prev => !prev);\r\n                                    }}\r\n                                    className=\"inline-flex items-center gap-1 text-blue-600 hover:text-blue-800\"\r\n                              >\r\n                                    {isEditingOperation ? (\r\n                                          <>\r\n                                                <X className=\"h-4 w-4\" /> Cancel\r\n                                          </>\r\n                                    ) : (\r\n                                          <>\r\n                                                <Edit className=\"h-4 w-4\" /> Edit\r\n                                          </>\r\n                                    )}\r\n                              </button>\r\n                        </div>\r\n                        {operationalConfigFields.map(({ label, key }) => {\r\n                              const k = key as keyof SellerConfig;\r\n                              const currentValue = isEditingOperation\r\n                                    ? (pendingOperation[k] !== undefined ? pendingOperation[k] : config[k])\r\n                                    : config[k];\r\n                              return (\r\n                                    <div key={key} className=\"flex gap-4 items-center text-md\">\r\n                                          <Label className=\"w-[400px] text-md text-typography-400\">{label}</Label>\r\n                                          <RadioGroup\r\n                                                value={currentValue ? [\"item_pick_enabled\"].includes(key) ? \"yes\" : \"no\" : [\"item_pick_enabled\"].includes(key) ? \"no\" : \"yes\"}\r\n                                                onValueChange={(val) => {\r\n                                                      if (isEditingOperation) {\r\n                                                            if (k === \"item_pick_enabled\") {\r\n                                                                  updatePending(\"operation\", \"item_pick_enabled\", val === \"yes\");\r\n                                                                  console.log(`Operational onChange: ${key} set to ${val === \"yes\"}`);\r\n                                                            } else {\r\n                                                                  updatePending(\"operation\", k, val === \"no\");\r\n                                                                  console.log(`Operational onChange: ${key} set to ${val === \"no\"}`);\r\n                                                            }\r\n                                                      }\r\n                                                }}\r\n                                                disabled={!isEditingOperation}\r\n                                                className=\"flex gap-4 items-center text-md font-semibold text-typography-800\"\r\n                                          >\r\n                                                <div className=\"flex items-center gap-2\">\r\n                                                      <RadioGroupItem id={`${key}-yes`} value=\"yes\" />\r\n                                                      <Label htmlFor={`${key}-yes`}>Yes</Label>\r\n                                                </div>\r\n                                                <div className=\"flex items-center gap-2\">\r\n                                                      <RadioGroupItem id={`${key}-no`} value=\"no\" />\r\n                                                      <Label htmlFor={`${key}-no`}>No</Label>\r\n                                                </div>\r\n                                          </RadioGroup>\r\n                                    </div>\r\n                              );\r\n                        })}\r\n                        {isEditingOperation && (\r\n                              <div className=\"flex gap-4 justify-center\">\r\n                                    <button onClick={() => handleCancelSection(\"operation\")} className=\"px-4 py-2 border rounded-md\">\r\n                                          Cancel\r\n                                    </button>\r\n                                    <button onClick={() => handleSaveSection(\"operation\")} className=\"px-6 py-2 bg-primary text-white rounded-md\">\r\n                                          Save\r\n                                    </button>\r\n                              </div>\r\n                        )}\r\n                  </div>\r\n\r\n                  {/* Delivery Configurations */}\r\n                  <div className=\"flex flex-col gap-4 border rounded-xl border-neutral-400 p-4 shadow\">\r\n                        <div className=\"flex justify-between\">\r\n                              <div className=\"text-lg font-semibold text-typography-700\">Delivery Configurations</div>\r\n                              <button\r\n                                    onClick={() => {\r\n                                          if (!isEditingDelivery) {\r\n                                                setPendingDelivery({\r\n                                                      allow_cod: config.allow_cod,\r\n                                                      is_pay_later_enabled: config.is_pay_later_enabled,\r\n                                                      distanceBasedDel: config.distanceBasedDel,\r\n                                                      deliveryDistance: config.deliveryDistance,\r\n                                                      delivery_type: config.delivery_type,\r\n                                                      dispatch_time: config.dispatch_time,\r\n                                                      delivery_time: config.delivery_time,\r\n                                                      approxDelDateVisibility: config.approxDelDateVisibility,\r\n                                                      instantDeliveryTime: config.instantDeliveryTime,\r\n                                                      booking_open_time: config.booking_open_time,\r\n                                                      booking_close_time: config.booking_close_time,\r\n                                                      t1Open: config.t1Open,\r\n                                                      t1Close: config.t1Close,\r\n                                                      t2Open: config.t2Open,\r\n                                                      t2Close: config.t2Close,\r\n                                                      t3Open: config.t3Open,\r\n                                                      t3Close: config.t3Close,\r\n                                                      pos: config.pos,\r\n                                                      menuId: config.menuId,\r\n                                                      posCustId: config.posCustId,\r\n                                                      posRetName: config.posRetName,\r\n                                                      posRetContactNo: config.posRetContactNo,\r\n                                                      posRetAddress: config.posRetAddress,\r\n                                                      sourceSystem: config.sourceSystem,\r\n                                                      ondcDomain: config.ondcDomain,\r\n                                                      defaultOrderPrepTime: config.defaultOrderPrepTime,\r\n                                                });\r\n                                                console.log(\"Entering edit mode for Delivery\");\r\n                                          }\r\n                                          setIsEditingDelivery(prev => !prev);\r\n                                    }}\r\n                                    className=\"inline-flex items-center gap-1 text-blue-600 hover:text-blue-800\"\r\n                              >\r\n                                    {isEditingDelivery ? (\r\n                                          <>\r\n                                                <X className=\"h-4 w-4\" /> Cancel\r\n                                          </>\r\n                                    ) : (\r\n                                          <>\r\n                                                <Edit className=\"h-4 w-4\" /> Edit\r\n                                          </>\r\n                                    )}\r\n                              </button>\r\n                        </div>\r\n                        {deliveryConfigRadioFields.map(({ label, key }) => {\r\n                              const k = key as keyof SellerConfig;\r\n                              const currentValue = isEditingDelivery\r\n                                    ? (pendingDelivery[k] !== undefined ? pendingDelivery[k] : config[k])\r\n                                    : config[k];\r\n                              return (\r\n                                    <>\r\n                                          <div key={key} className=\"flex gap-4 items-center text-md\">\r\n                                                <Label className=\"w-[400px] text-md text-typography-400\">{label}</Label>\r\n                                                <RadioGroup\r\n                                                      value={currentValue ? \"yes\" : \"no\"}\r\n                                                      onValueChange={(val) => {\r\n                                                            if (isEditingDelivery) {\r\n                                                                  if (k === \"distanceBasedDel\" && val === \"no\") {\r\n                                                                        updatePending(\"delivery\", \"deliveryDistance\", 0);\r\n                                                                  }\r\n                                                                  updatePending(\"delivery\", k, val === \"yes\");\r\n                                                                  console.log(`Delivery onChange: ${key} set to ${val === \"yes\"}`);\r\n                                                            }\r\n                                                      }}\r\n                                                      disabled={!isEditingDelivery}\r\n                                                      className=\"flex gap-4 items-center text-md font-semibold text-typography-800\"\r\n                                                >\r\n                                                      <div className=\"flex items-center gap-2\">\r\n                                                            <RadioGroupItem id={`${key}-yes`} value=\"yes\" />\r\n                                                            <Label htmlFor={`${key}-yes`}>Yes</Label>\r\n                                                      </div>\r\n                                                      <div className=\"flex items-center gap-2\">\r\n                                                            <RadioGroupItem id={`${key}-no`} value=\"no\" />\r\n                                                            <Label htmlFor={`${key}-no`}>No</Label>\r\n                                                      </div>\r\n                                                </RadioGroup>\r\n                                          </div>\r\n                                          {key === \"distanceBasedDel\" && currentValue === true && (\r\n                                                <div className=\"flex gap-4 items-center text-md\">\r\n                                                      <Label className=\"w-[400px] text-md text-typography-400\">Delivery Distance(km):</Label>\r\n                                                      <input\r\n                                                            type=\"number\"\r\n                                                            min={0}\r\n                                                            value={\r\n                                                                  pendingDelivery.deliveryDistance !== undefined\r\n                                                                        ? pendingDelivery.deliveryDistance ?? \"\"\r\n                                                                        : config.deliveryDistance ?? \"\"\r\n                                                            }\r\n                                                            onKeyDown={(e) => {\r\n                                                                  if (e.key === \"-\" || e.key === \"e\") {\r\n                                                                        e.preventDefault();\r\n                                                                  }\r\n                                                            }}\r\n                                                            onChange={(e) => {\r\n                                                                  const value = parseFloat(e.target.value);\r\n                                                                  if (value >= 0) {\r\n                                                                        updatePending(\"delivery\", \"deliveryDistance\", value);\r\n                                                                        console.log(`Delivery Distance set to ${value}`);\r\n                                                                  }\r\n                                                            }}\r\n                                                            disabled={!isEditingDelivery}\r\n                                                            className=\"border border-neutral-400 rounded-md p-1 px-2\"\r\n                                                      />\r\n                                                </div>\r\n                                          )}\r\n                                    </>\r\n                              );\r\n                        })}\r\n                        <div className=\"flex gap-4 items-center text-md\">\r\n                              <Label className=\"w-[400px] text-md text-typography-400\">Instant/Restaurant Delivery time(mins):</Label>\r\n                              <input\r\n                                    type=\"number\"\r\n                                    min={0}\r\n                                    value={\r\n                                          pendingDelivery.instantDeliveryTime !== undefined\r\n                                                ? pendingDelivery.instantDeliveryTime ?? \"\"\r\n                                                : config.instantDeliveryTime ?? \"\"\r\n                                    }\r\n                                    onKeyDown={(e) => {\r\n                                          if (e.key === \"-\" || e.key === \"e\") {\r\n                                                e.preventDefault();\r\n                                          }\r\n                                    }}\r\n                                    onChange={(e) => {\r\n                                          const value = parseFloat(e.target.value);\r\n                                          updatePending(\"delivery\", \"instantDeliveryTime\", value);\r\n                                          console.log(`Instant Delivery Time set to ${value}`);\r\n\r\n                                    }}\r\n                                    disabled={!isEditingDelivery}\r\n                                    className=\"border border-neutral-400 rounded-md p-1 px-2\"\r\n                              />\r\n                        </div>\r\n                        {deliveryConfigOptions.map(({ label, key, options }) => {\r\n                              const k = key as keyof SellerConfig;\r\n                              const currentValue = isEditingDelivery\r\n                                    ? (pendingDelivery[k] !== undefined ? pendingDelivery[k] : config[k])\r\n                                    : config[k];\r\n                              return (\r\n                                    <>\r\n                                          <div key={key} className=\"flex gap-4 items-center text-md\">\r\n                                                <Label className=\"w-[400px] text-md text-typography-400\">{label}:</Label>\r\n                                                <RadioGroup\r\n                                                      value={currentValue || \"\"}\r\n                                                      onValueChange={(val) => {\r\n                                                            if (isEditingDelivery) {\r\n                                                                  if (k === \"pos\" && val === \"none\") {\r\n                                                                        updatePending(\"delivery\", \"menuId\", null);\r\n                                                                        updatePending(\"delivery\", \"posCustId\", null);\r\n                                                                        updatePending(\"delivery\", \"posRetName\", null);\r\n                                                                        updatePending(\"delivery\", \"posRetContactNo\", null);\r\n                                                                        updatePending(\"delivery\", \"posRetAddress\", null);\r\n                                                                  }\r\n                                                                  if (k === \"ondcDomain\" && val === \"RET10\") {\r\n                                                                        updatePending(\"delivery\", \"defaultOrderPrepTime\", null);\r\n                                                                  }\r\n                                                                  updatePending(\"delivery\", k, val);\r\n                                                                  console.log(`Delivery onChange: ${key} set to ${val}`);\r\n                                                            }\r\n                                                      }}\r\n                                                      disabled={!isEditingDelivery}\r\n                                                      className=\"flex gap-4 items-center text-md font-semibold text-typography-800\"\r\n                                                >\r\n                                                      {options.map(({ id, value, label }) => (\r\n                                                            <div key={id} className=\"flex items-center gap-2\">\r\n                                                                  <RadioGroupItem id={id} value={String(value)} />\r\n                                                                  <Label htmlFor={id}>{label}</Label>\r\n                                                            </div>\r\n                                                      ))}\r\n                                                </RadioGroup>\r\n                                          </div>\r\n                                          {key === \"pos\" && currentValue === \"petpooja\" && (\r\n                                                <>\r\n                                                      <div className=\"flex gap-4 items-center text-md\">\r\n                                                            <Label className=\"w-[400px] text-md text-typography-400\">Petpooja Menu ID:</Label>\r\n                                                            <input\r\n                                                                  type=\"text\"\r\n                                                                  value={pendingDelivery.menuId !== undefined ? (pendingDelivery.menuId ?? \"\") : (config.menuId ?? \"\")}\r\n                                                                  onChange={(e) => {\r\n                                                                        if (isEditingDelivery) {\r\n                                                                              updatePending(\"delivery\", \"menuId\", e.target.value);\r\n                                                                              console.log(`Menu ID set to ${e.target.value}`);\r\n                                                                        }\r\n                                                                  }}\r\n                                                                  placeholder=\"eg. otsmbu6q4n\"\r\n                                                                  disabled={!isEditingDelivery}\r\n                                                                  className=\"border border-neutral-400 rounded-md p-1 px-2\"\r\n                                                            />\r\n                                                            {validationErrors?.menuId && (\r\n                                                                  <p className=\"text-red-500 text-sm\">{validationErrors.menuId}</p>\r\n                                                            )}\r\n                                                      </div>\r\n                                                      <div className=\"flex gap-4 items-center text-md\">\r\n                                                            <Label className=\"w-[400px] text-md text-typography-400\">Petpooja Customer ID:</Label>\r\n                                                            <input\r\n                                                                  type=\"text\"\r\n                                                                  value={pendingDelivery.posCustId !== undefined ? (pendingDelivery.posCustId ?? \"\") : (config.posCustId ?? \"\")}\r\n                                                                  onChange={(e) => {\r\n                                                                        if (isEditingDelivery) {\r\n                                                                              updatePending(\"delivery\", \"posCustId\", e.target.value);\r\n                                                                              console.log(`Pos Customer ID set to ${e.target.value}`);\r\n                                                                        }\r\n                                                                  }}\r\n                                                                  placeholder=\"eg. 4818\"\r\n                                                                  disabled={!isEditingDelivery}\r\n                                                                  className=\"border border-neutral-400 rounded-md p-1 px-2\"\r\n                                                            />\r\n                                                            {validationErrors?.posCustId && (\r\n                                                                  <p className=\"text-red-500 text-sm\">{validationErrors.posCustId}</p>\r\n                                                            )}\r\n                                                      </div>\r\n                                                      <div className=\"flex gap-4 items-center text-md\">\r\n                                                            <Label className=\"w-[400px] text-md text-typography-400\">Petpooja Restaurant Name:</Label>\r\n                                                            <input\r\n                                                                  type=\"text\"\r\n                                                                  value={pendingDelivery.posRetName !== undefined ? (pendingDelivery.posRetName ?? \"\") : (config.posRetName ?? \"\")}\r\n                                                                  onChange={(e) => {\r\n                                                                        if (isEditingDelivery) {\r\n                                                                              updatePending(\"delivery\", \"posRetName\", e.target.value);\r\n                                                                              console.log(`Pos Restaurant Name set to ${e.target.value}`);\r\n                                                                        }\r\n                                                                  }}\r\n                                                                  placeholder=\"\"\r\n                                                                  disabled={!isEditingDelivery}\r\n                                                                  className=\"border border-neutral-400 rounded-md p-1 px-2\"\r\n                                                            />\r\n                                                            {validationErrors?.posRetName && (\r\n                                                                  <p className=\"text-red-500 text-sm\">{validationErrors.posRetName}</p>\r\n                                                            )}\r\n                                                      </div>\r\n                                                      <div className=\"flex gap-4 items-center text-md\">\r\n                                                            <Label className=\"w-[400px] text-md text-typography-400\">Petpooja Restaurant Contact No:</Label>\r\n                                                            <input\r\n                                                                  type=\"text\"\r\n                                                                  value={pendingDelivery.posRetContactNo !== undefined ? (pendingDelivery.posRetContactNo ?? \"\") : (config.posRetContactNo ?? \"\")}\r\n                                                                  onChange={(e) => {\r\n                                                                        if (isEditingDelivery) {\r\n                                                                              updatePending(\"delivery\", \"posRetContactNo\", e.target.value);\r\n                                                                              console.log(`Pos Restaurant Contact No set to ${e.target.value}`);\r\n                                                                        }\r\n                                                                  }}\r\n                                                                  placeholder=\"\"\r\n                                                                  disabled={!isEditingDelivery}\r\n                                                                  className=\"border border-neutral-400 rounded-md p-1 px-2\"\r\n                                                            />\r\n                                                            {validationErrors?.posRetContactNo && (\r\n                                                                  <p className=\"text-red-500 text-sm\">{validationErrors.posRetContactNo}</p>\r\n                                                            )}\r\n                                                      </div>\r\n                                                      <div className=\"flex gap-4 items-center text-md\">\r\n                                                            <Label className=\"w-[400px] text-md text-typography-400\">Petpooja Restaurant Address:</Label>\r\n                                                            <input\r\n                                                                  type=\"text\"\r\n                                                                  value={pendingDelivery.posRetAddress !== undefined ? (pendingDelivery.posRetAddress ?? \"\") : (config.posRetAddress ?? \"\")}\r\n                                                                  onChange={(e) => {\r\n                                                                        if (isEditingDelivery) {\r\n                                                                              updatePending(\"delivery\", \"posRetAddress\", e.target.value);\r\n                                                                              console.log(`Pos Restaurant Address set to ${e.target.value}`);\r\n                                                                        }\r\n                                                                  }}\r\n                                                                  placeholder=\"\"\r\n                                                                  disabled={!isEditingDelivery}\r\n                                                                  className=\"border border-neutral-400 rounded-md p-1 px-2\"\r\n                                                            />\r\n                                                            {validationErrors?.posRetAddress && (\r\n                                                                  <p className=\"text-red-500 text-sm\">{validationErrors.posRetAddress}</p>\r\n                                                            )}\r\n                                                      </div>\r\n                                                </>\r\n                                          )\r\n                                          }\r\n                                          {key == \"ondcDomain\" && currentValue == \"RET11\" && (\r\n                                                <>\r\n                                                      <div className=\"flex gap-4 items-center text-md\">\r\n                                                            <Label className=\"w-[400px] text-md text-typography-400\">Default Order Prep Time (mins):</Label>\r\n                                                            <input\r\n                                                                  type=\"number\"\r\n                                                                  min={0}\r\n                                                                  value={pendingDelivery.defaultOrderPrepTime !== undefined ? (pendingDelivery.defaultOrderPrepTime ?? \"\") : (config.defaultOrderPrepTime ?? \"\")}\r\n                                                                  onChange={(e) => {\r\n                                                                        if (isEditingDelivery) {\r\n                                                                              updatePending(\"delivery\", \"defaultOrderPrepTime\", e.target.value);\r\n                                                                              console.log(`Default Order Prep Time set to ${e.target.value}`);\r\n                                                                        }\r\n                                                                  }}\r\n                                                                  disabled={!isEditingDelivery}\r\n                                                                  className=\"border border-neutral-400 rounded-md p-1 px-2\"\r\n                                                            />\r\n                                                            {validationErrors?.defaultOrderPrepTime && (\r\n                                                                  <p className=\"text-red-500 text-sm\">{validationErrors.defaultOrderPrepTime}</p>\r\n                                                            )}\r\n                                                      </div>\r\n                                                </>\r\n                                          )\r\n                                          }\r\n                                    </>\r\n                              );\r\n                        })}\r\n                        <div className=\"flex flex-col gap-2 mt-4\">\r\n                              <div className=\"text-lg text-typography-600\">Timings:</div>\r\n                              <div className=\"flex gap-4 items-center\">\r\n                                    <div className=\"flex gap-2 items-center w-[400px]\">\r\n                                          <Label className=\"w-[160px] text-md text-typography-400\">Dispatch Time:</Label>\r\n                                          <input\r\n                                                type=\"time\"\r\n                                                value={pendingDelivery.dispatch_time !== undefined ? String(pendingDelivery.dispatch_time) : String(config.dispatch_time || \"\")}\r\n                                                onChange={(e) => {\r\n                                                      updatePending(\"delivery\", \"dispatch_time\", e.target.value);\r\n                                                      console.log(`Dispatch Time set to ${e.target.value}`);\r\n                                                }}\r\n                                                disabled={!isEditingDelivery}\r\n                                                className=\"border border-neutral-400 rounded-md p-1 px-2\"\r\n                                          />\r\n                                    </div>\r\n                                    <div className=\"flex gap-2 items-center\">\r\n                                          <Label className=\"w-[160px] text-md text-typography-400\">Delivery Time:</Label>\r\n                                          <input\r\n                                                type=\"time\"\r\n                                                value={pendingDelivery.delivery_time !== undefined ? String(pendingDelivery.delivery_time) : String(config.delivery_time || \"\")}\r\n                                                onChange={(e) => {\r\n                                                      updatePending(\"delivery\", \"delivery_time\", e.target.value);\r\n                                                      console.log(`Delivery Time set to ${e.target.value}`);\r\n                                                }}\r\n                                                disabled={!isEditingDelivery}\r\n                                                className=\"border border-neutral-400 rounded-md p-1 px-2\"\r\n                                          />\r\n                                    </div>\r\n                              </div>\r\n                              <div className=\"flex gap-4 items-center\">\r\n                                    <div className=\"flex gap-2 items-center w-[400px]\">\r\n                                          <Label className=\"w-[160px] text-md text-typography-400\">Booking Open Time:</Label>\r\n                                          <input\r\n                                                type=\"number\"\r\n                                                min={0}\r\n                                                value={\r\n                                                      pendingDelivery.booking_open_time !== undefined\r\n                                                            ? pendingDelivery.booking_open_time\r\n                                                            : config.booking_open_time || 0\r\n                                                }\r\n                                                onKeyDown={(e) => {\r\n                                                      if (e.key === \"-\" || e.key === \"e\") {\r\n                                                            e.preventDefault();\r\n                                                      }\r\n                                                }}\r\n                                                onChange={(e) => {\r\n                                                      const value = parseFloat(e.target.value);\r\n                                                      if (value >= 0) {\r\n                                                            updatePending(\"delivery\", \"booking_open_time\", value);\r\n                                                            console.log(`Booking Open Time set to ${value} minutes`);\r\n                                                      }\r\n                                                }}\r\n                                                disabled={!isEditingDelivery}\r\n                                                className=\"border border-neutral-400 rounded-md p-1 px-2\"\r\n                                          />\r\n                                    </div>\r\n                                    <div className=\"flex gap-2 items-center\">\r\n                                          <Label className=\"w-[160px] text-md text-typography-400\">Booking Close Time:</Label>\r\n                                          <input\r\n                                                type=\"number\"\r\n                                                min={0}\r\n                                                value={\r\n                                                      pendingDelivery.booking_close_time !== undefined\r\n                                                            ? pendingDelivery.booking_close_time\r\n                                                            : config.booking_close_time || 0\r\n                                                }\r\n                                                onKeyDown={(e) => {\r\n                                                      if (e.key === \"-\" || e.key === \"e\") {\r\n                                                            e.preventDefault();\r\n                                                      }\r\n                                                }}\r\n                                                onChange={(e) => {\r\n                                                      const value = parseFloat(e.target.value);\r\n                                                      if (value >= 0) {\r\n                                                            updatePending(\"delivery\", \"booking_close_time\", value);\r\n                                                            console.log(`Booking Close Time set to ${value} minutes`);\r\n                                                      }\r\n                                                }}\r\n                                                disabled={!isEditingDelivery}\r\n                                                className=\"border border-neutral-400 rounded-md p-1 px-2\"\r\n                                          />\r\n                                    </div>\r\n                              </div>\r\n                              <div className=\"flex gap-4 items-center\">\r\n                                    <div className=\"flex gap-2 items-center w-[400px]\">\r\n                                          <Label className=\"w-[160px] text-md text-typography-400\">Breakfast Open (t1Open):</Label>\r\n                                          <input\r\n                                                type=\"number\"\r\n                                                min={0}\r\n                                                value={\r\n                                                      pendingDelivery.t1Open !== undefined\r\n                                                            ? pendingDelivery.t1Open\r\n                                                            : config.t1Open || 0\r\n                                                }\r\n                                                onKeyDown={(e) => {\r\n                                                      if (e.key === \"-\" || e.key === \"e\") {\r\n                                                            e.preventDefault();\r\n                                                      }\r\n                                                }}\r\n                                                onChange={(e) => {\r\n                                                      const value = parseFloat(e.target.value);\r\n                                                      if (value >= 0) {\r\n                                                            updatePending(\"delivery\", \"t1Open\", value);\r\n                                                            console.log(`t1Open set to ${value} minutes`);\r\n                                                      }\r\n                                                }}\r\n                                                disabled={!isEditingDelivery}\r\n                                                className=\"border border-neutral-400 rounded-md p-1 px-2\"\r\n                                          />\r\n                                    </div>\r\n                                    <div className=\"flex gap-2 items-center\">\r\n                                          <Label className=\"w-[160px] text-md text-typography-400\">Breakfast Close (t1Close):</Label>\r\n                                          <input\r\n                                                type=\"number\"\r\n                                                min={0}\r\n                                                value={\r\n                                                      pendingDelivery.t1Close !== undefined\r\n                                                            ? pendingDelivery.t1Close\r\n                                                            : config.t1Close || 0\r\n                                                }\r\n                                                onKeyDown={(e) => {\r\n                                                      if (e.key === \"-\" || e.key === \"e\") {\r\n                                                            e.preventDefault();\r\n                                                      }\r\n                                                }}\r\n                                                onChange={(e) => {\r\n                                                      const value = parseFloat(e.target.value);\r\n                                                      if (value >= 0) {\r\n                                                            updatePending(\"delivery\", \"t1Close\", value);\r\n                                                            console.log(`t1Close set to ${value} minutes`);\r\n                                                      }\r\n                                                }}\r\n                                                disabled={!isEditingDelivery}\r\n                                                className=\"border border-neutral-400 rounded-md p-1 px-2\"\r\n                                          />\r\n                                    </div>\r\n                              </div>\r\n                              <div className=\"flex gap-4 items-center\">\r\n                                    <div className=\"flex gap-2 items-center w-[400px]\">\r\n                                          <Label className=\"w-[160px] text-md text-typography-400\">Lunch Open (t2Open):</Label>\r\n                                          <input\r\n                                                type=\"number\"\r\n                                                min={0}\r\n                                                value={\r\n                                                      pendingDelivery.t2Open !== undefined\r\n                                                            ? pendingDelivery.t2Open\r\n                                                            : config.t2Open || 0\r\n                                                }\r\n                                                onKeyDown={(e) => {\r\n                                                      if (e.key === \"-\" || e.key === \"e\") {\r\n                                                            e.preventDefault();\r\n                                                      }\r\n                                                }}\r\n                                                onChange={(e) => {\r\n                                                      const value = parseFloat(e.target.value);\r\n                                                      if (value >= 0) {\r\n                                                            updatePending(\"delivery\", \"t2Open\", value);\r\n                                                            console.log(`t2Open set to ${value} minutes`);\r\n                                                      }\r\n                                                }}\r\n                                                disabled={!isEditingDelivery}\r\n                                                className=\"border border-neutral-400 rounded-md p-1 px-2\"\r\n                                          />\r\n                                    </div>\r\n                                    <div className=\"flex gap-2 items-center\">\r\n                                          <Label className=\"w-[160px] text-md text-typography-400\">Lunch Close (t2Close):</Label>\r\n                                          <input\r\n                                                type=\"number\"\r\n                                                min={0}\r\n                                                value={\r\n                                                      pendingDelivery.t2Close !== undefined\r\n                                                            ? pendingDelivery.t2Close\r\n                                                            : config.t2Close || 0\r\n                                                }\r\n                                                onKeyDown={(e) => {\r\n                                                      if (e.key === \"-\" || e.key === \"e\") {\r\n                                                            e.preventDefault();\r\n                                                      }\r\n                                                }}\r\n                                                onChange={(e) => {\r\n                                                      const value = parseFloat(e.target.value);\r\n                                                      if (value >= 0) {\r\n                                                            updatePending(\"delivery\", \"t2Close\", value);\r\n                                                            console.log(`t2Close set to ${value} minutes`);\r\n                                                      }\r\n                                                }}\r\n                                                disabled={!isEditingDelivery}\r\n                                                className=\"border border-neutral-400 rounded-md p-1 px-2\"\r\n                                          />\r\n                                    </div>\r\n                              </div>\r\n                              <div className=\"flex gap-4 items-center\">\r\n                                    <div className=\"flex gap-2 items-center w-[400px]\">\r\n                                          <Label className=\"w-[160px] text-md text-typography-400\">Dinner Open (t3Open):</Label>\r\n                                          <input\r\n                                                type=\"number\"\r\n                                                min={0}\r\n                                                value={\r\n                                                      pendingDelivery.t3Open !== undefined\r\n                                                            ? pendingDelivery.t3Open\r\n                                                            : config.t3Open || 0\r\n                                                }\r\n                                                onKeyDown={(e) => {\r\n                                                      if (e.key === \"-\" || e.key === \"e\") {\r\n                                                            e.preventDefault();\r\n                                                      }\r\n                                                }}\r\n                                                onChange={(e) => {\r\n                                                      const value = parseFloat(e.target.value);\r\n                                                      if (value >= 0) {\r\n                                                            updatePending(\"delivery\", \"t3Open\", value);\r\n                                                            console.log(`t3Open set to ${value} minutes`);\r\n                                                      }\r\n                                                }}\r\n                                                disabled={!isEditingDelivery}\r\n                                                className=\"border border-neutral-400 rounded-md p-1 px-2\"\r\n                                          />\r\n                                    </div>\r\n                                    <div className=\"flex gap-2 items-center\">\r\n                                          <Label className=\"w-[160px] text-md text-typography-400\">Dinner Close (t3Close):</Label>\r\n                                          <input\r\n                                                type=\"number\"\r\n                                                min={0}\r\n                                                value={\r\n                                                      pendingDelivery.t3Close !== undefined\r\n                                                            ? pendingDelivery.t3Close\r\n                                                            : config.t3Close || 0\r\n                                                }\r\n                                                onKeyDown={(e) => {\r\n                                                      if (e.key === \"-\" || e.key === \"e\") {\r\n                                                            e.preventDefault();\r\n                                                      }\r\n                                                }}\r\n                                                onChange={(e) => {\r\n                                                      const value = parseFloat(e.target.value);\r\n                                                      if (value >= 0) {\r\n                                                            updatePending(\"delivery\", \"t3Close\", value);\r\n                                                            console.log(`t3Close set to ${value} minutes`);\r\n                                                      }\r\n                                                }}\r\n                                                disabled={!isEditingDelivery}\r\n                                                className=\"border border-neutral-400 rounded-md p-1 px-2\"\r\n                                          />\r\n                                    </div>\r\n                              </div>\r\n                        </div>\r\n                        {isEditingDelivery && (\r\n                              <div className=\"flex gap-4 justify-center mt-4\">\r\n                                    <button onClick={() => handleCancelSection(\"delivery\")} className=\"px-4 py-2 border rounded-md\">\r\n                                          Cancel\r\n                                    </button>\r\n                                    <button onClick={() => handleSaveSection(\"delivery\")} className=\"px-6 py-2 bg-primary text-white rounded-md\">\r\n                                          Save\r\n                                    </button>\r\n                              </div>\r\n                        )}\r\n                  </div>\r\n\r\n                  {/* Business Configurations */}\r\n                  <div className=\"flex flex-col gap-4 border rounded-xl border-neutral-400 p-4 shadow\">\r\n                        <div className=\"flex justify-between\">\r\n                              <div className=\"text-lg font-semibold text-typography-700\">Business Configurations</div>\r\n                              <button\r\n                                    onClick={() => {\r\n                                          if (!isEditingBusiness) {\r\n                                                setPendingBusiness({\r\n                                                      listing_seq: config.listing_seq,\r\n                                                      mininum_required_balance: config.mininum_required_balance,\r\n                                                      wa_enable: config.wa_enable,\r\n                                                });\r\n                                                console.log(\"Entering edit mode for Business\");\r\n                                          }\r\n                                          setIsEditingBusiness(prev => !prev);\r\n                                    }}\r\n                                    className=\"inline-flex items-center gap-1 text-blue-600 hover:text-blue-800\"\r\n                              >\r\n                                    {isEditingBusiness ? (\r\n                                          <>\r\n                                                <X className=\"h-4 w-4\" /> Cancel\r\n                                          </>\r\n                                    ) : (\r\n                                          <>\r\n                                                <Edit className=\"h-4 w-4\" /> Edit\r\n                                          </>\r\n                                    )}\r\n                              </button>\r\n                        </div>\r\n                        <div className=\"flex gap-4 items-center\">\r\n                              <Label className=\"w-[400px] text-md text-typography-400\">Business ID:</Label>\r\n                              <input\r\n                                    type=\"text\"\r\n                                    value={String(config.business_id)}\r\n                                    disabled\r\n                                    className=\"border border-neutral-400 rounded-md p-1 px-2 bg-gray-100\"\r\n                              />\r\n                        </div>\r\n                        {businessConfigFields.map(({ label, key, type, prefix }) => {\r\n      const k = key as keyof SellerConfig;\r\n      const currentValue = isEditingBusiness\r\n            ? (pendingBusiness[k] !== undefined ? pendingBusiness[k] : config[k])\r\n            : config[k];\r\n      return (\r\n            <div key={key} className=\"flex gap-4 items-center text-md\">\r\n                  <Label className=\"w-[400px] text-md text-typography-400\">{label}</Label>\r\n                  <span className=\"font-semibold flex gap-2 items-center\">\r\n                        {prefix}\r\n                        <input\r\n                              type={type}\r\n                              value={String(isEditingBusiness ? (pendingBusiness[k] ?? config[k]) : config[k] || \"\")}\r\n                              onChange={(e) => {\r\n                                    if (isEditingBusiness) {\r\n                                          let value = e.target.value;\r\n                                          // Only allow 0-100 for platformFeePerc\r\n                                          if (key === \"platformFeePerc\") {\r\n                                                // Remove non-numeric except dot\r\n                                                value = value.replace(/[^0-9.]/g, \"\");\r\n                                                // Clamp between 0 and 100\r\n                                                let num = Number(value);\r\n                                                if (num > 100) num = 100;\r\n                                                if (num < 0) num = 0;\r\n                                                value = value === \"\" ? \"\" : String(num);\r\n                                          }\r\n                                          updatePending(\"business\", k, value);\r\n                                          console.log(`Business onChange: ${key} set to ${value}`);\r\n                                    }\r\n                              }}\r\n                              disabled={!isEditingBusiness}\r\n                              min={key === \"platformFeePerc\" ? 0 : undefined}\r\n                              max={key === \"platformFeePerc\" ? 100 : undefined}\r\n                              className=\"border border-neutral-400 rounded-md p-1 px-2\"\r\n                        />\r\n                  </span>\r\n            </div>\r\n      );\r\n})}\r\n                        {businessConfigRadioFields.map(({ label, key }) => {\r\n                              const k = key as keyof SellerConfig;\r\n                              const currentValue = isEditingBusiness\r\n                                    ? (pendingBusiness[k] !== undefined ? pendingBusiness[k] : config[k])\r\n                                    : config[k];\r\n                              return (\r\n                                    <div key={key} className=\"flex gap-4 items-center text-md\">\r\n                                          <Label className=\"w-[400px] text-md text-typography-400\">{label}</Label>\r\n                                          <RadioGroup\r\n                                                value={currentValue ? \"yes\" : \"no\"}\r\n                                                onValueChange={(val) => {\r\n                                                      if (isEditingBusiness) {\r\n                                                            updatePending(\"business\", k, val === \"yes\");\r\n                                                            console.log(`Business onChange: ${key} set to ${val === \"yes\"}`);\r\n                                                      }\r\n                                                }}\r\n                                                disabled={!isEditingBusiness}\r\n                                                className=\"flex gap-4 items-center text-md font-semibold text-typography-800\"\r\n                                          >\r\n                                                <div className=\"flex items-center gap-2\">\r\n                                                      <RadioGroupItem id={`${key}-yes`} value=\"yes\" />\r\n                                                      <Label htmlFor={`${key}-yes`}>Yes</Label>\r\n                                                </div>\r\n                                                <div className=\"flex items-center gap-2\">\r\n                                                      <RadioGroupItem id={`${key}-no`} value=\"no\" />\r\n                                                      <Label htmlFor={`${key}-no`}>No</Label>\r\n                                                </div>\r\n                                          </RadioGroup>\r\n                                    </div>\r\n                              );\r\n                        })}\r\n                        {isEditingBusiness && (\r\n                              <div className=\"flex gap-4 justify-center\">\r\n                                    <button onClick={() => handleCancelSection(\"business\")} className=\"px-4 py-2 border rounded-md\">\r\n                                          Cancel\r\n                                    </button>\r\n                                    <button onClick={() => handleSaveSection(\"business\")} className=\"px-6 py-2 bg-primary text-white rounded-md\">\r\n                                          Save\r\n                                    </button>\r\n                              </div>\r\n                        )}\r\n                  </div>\r\n\r\n                  {/* Packaging Configurations */}\r\n                  <div className=\"flex flex-col gap-4 border rounded-xl border-neutral-400 p-4 shadow\">\r\n                        <div className=\"flex justify-between\">\r\n                              <div className=\"text-lg font-semibold text-typography-700\">Packaging Configurations</div>\r\n                              <button\r\n                                    onClick={() => {\r\n                                          if (!isEditingPackaging) {\r\n                                                setPendingPackaging({\r\n                                                      packagingCharge: config.packagingCharge,\r\n                                                      packagingChargeType: config.packagingChargeType,\r\n                                                      packagingApplicableOn: config.packagingApplicableOn,\r\n                                                });\r\n                                                console.log(\"Entering edit mode for Packaging\");\r\n                                          }\r\n                                          setIsEditingPackaging(prev => !prev);\r\n                                    }}\r\n                                    className=\"inline-flex items-center gap-1 text-blue-600 hover:text-blue-800\"\r\n                              >\r\n                                    {isEditingPackaging ? (\r\n                                          <>\r\n                                                <X className=\"h-4 w-4\" /> Cancel\r\n                                          </>\r\n                                    ) : (\r\n                                          <>\r\n                                                <Edit className=\"h-4 w-4\" /> Edit\r\n                                          </>\r\n                                    )}\r\n                              </button>\r\n                        </div>\r\n\r\n                        <div className=\"flex gap-4 items-center text-md\">\r\n                              <Label className=\"w-[400px] text-md text-typography-400\">Packaging Charge:</Label>\r\n                              <input\r\n                                    type=\"text\"\r\n                                    value={pendingPackaging.packagingCharge !== undefined ? (pendingPackaging.packagingCharge ?? \"\") : (config.packagingCharge ?? \"\")}\r\n                                    onChange={(e) => {\r\n                                          if (isEditingPackaging) {\r\n                                                updatePending(\"packaging\", \"packagingCharge\", e.target.value);\r\n                                                console.log(`Packaging Charge set to ${e.target.value}`);\r\n                                          }\r\n                                    }}\r\n                                    disabled={!isEditingPackaging}\r\n                                    className=\"border border-neutral-400 rounded-md p-1 px-2\"\r\n                              />\r\n                              {validationErrors?.packagingCharge && (\r\n                                    <p className=\"text-red-500 text-sm\">{validationErrors.packagingCharge}</p>\r\n                              )}\r\n                        </div>\r\n\r\n                        <div className=\"flex gap-4 items-center text-md\">\r\n                              <Label className=\"w-[400px] text-md text-typography-400\">Packaging Charge Type:</Label>\r\n                              <select\r\n                                    value={pendingPackaging.packagingChargeType !== undefined ? pendingPackaging.packagingChargeType : config.packagingChargeType}\r\n                                    onChange={(e) => {\r\n                                          if (isEditingPackaging) {\r\n                                                updatePending(\"packaging\", \"packagingChargeType\", e.target.value);\r\n                                                console.log(`Packaging Charge Type set to ${e.target.value}`);\r\n                                          }\r\n                                    }}\r\n                                    disabled={!isEditingPackaging}\r\n                                    className=\"border border-neutral-400 rounded-md p-1 px-2\"\r\n                              >\r\n                                    <option value=\"PERCENTAGE\">Percentage</option>\r\n                                    <option value=\"FIXED\">Fixed</option>\r\n                              </select>\r\n                              {validationErrors?.packagingChargeType && (\r\n                                    <p className=\"text-red-500 text-sm\">{validationErrors.packagingChargeType}</p>\r\n                              )}\r\n                        </div>\r\n\r\n                        <div className=\"flex gap-4 items-center text-md\">\r\n                              <Label className=\"w-[400px] text-md text-typography-400\">Packaging Applicable On:</Label>\r\n                              <select\r\n                                    value={pendingPackaging.packagingApplicableOn !== undefined ? pendingPackaging.packagingApplicableOn : config.packagingApplicableOn}\r\n                                    onChange={(e) => {\r\n                                          if (isEditingPackaging) {\r\n                                                updatePending(\"packaging\", \"packagingApplicableOn\", e.target.value);\r\n                                                console.log(`Packaging Applicable On set to ${e.target.value}`);\r\n                                          }\r\n                                    }}\r\n                                    disabled={!isEditingPackaging}\r\n                                    className=\"border border-neutral-400 rounded-md p-1 px-2\"\r\n                              >\r\n                                    <option value=\"NONE\">None</option>\r\n                                    <option value=\"ITEM\">Item</option>\r\n                                    <option value=\"ORDER\">Order</option>\r\n                              </select>\r\n                              {validationErrors?.packagingApplicableOn && (\r\n                                    <p className=\"text-red-500 text-sm\">{validationErrors.packagingApplicableOn}</p>\r\n                              )}\r\n                        </div>\r\n\r\n                        {isEditingPackaging && (\r\n                              <div className=\"flex gap-4 justify-center\">\r\n                                    <button onClick={() => handleCancelSection(\"packaging\")} className=\"px-4 py-2 border rounded-md\">\r\n                                          Cancel\r\n                                    </button>\r\n                                    <button onClick={() => handleSaveSection(\"packaging\")} className=\"px-6 py-2 bg-primary text-white rounded-md\">\r\n                                          Save\r\n                                    </button>\r\n                              </div>\r\n                        )}\r\n                  </div>\r\n\r\n\r\n                  {/* Inventory Configurations */}\r\n                  <div className=\"flex flex-col gap-4 border rounded-xl border-neutral-400 p-4 shadow\">\r\n                        <div className=\"flex justify-between\">\r\n                              <div className=\"text-lg font-semibold text-typography-700\">Inventory Configurations</div>\r\n                              <button\r\n                                    onClick={() => {\r\n                                          if (!isEditingInventory) {\r\n                                                setPendingInventory({\r\n                                                      auto_activate: config.auto_activate,\r\n                                                      approx_pricing: config.approx_pricing,\r\n                                                      approxPriceVisibility: config.approxPriceVisibility,\r\n                                                      strikeoff_enabled: config.strikeoff_enabled,\r\n                                                      contract_price_enabled: config.contract_price_enabled,\r\n                                                      fav_items_enabled: config.fav_items_enabled,\r\n                                                      minimum_order_qty: config.minimum_order_qty,\r\n                                                      advance_booking_days: config.advance_booking_days,\r\n                                                      minimum_order_value: config.minimum_order_value,\r\n                                                      category_level: config.category_level,\r\n                                                      miSources: config.miSources,\r\n                                                      logisticProvider: config.logisticProvider,\r\n                                                });\r\n                                                console.log(\"Entering edit mode for Inventory\");\r\n                                          }\r\n                                          setIsEditingInventory(prev => !prev);\r\n                                    }}\r\n                                    className=\"inline-flex items-center gap-1 text-blue-600 hover:text-blue-800\"\r\n                              >\r\n                                    {isEditingInventory ? (\r\n                                          <>\r\n                                                <X className=\"h-4 w-4\" /> Cancel\r\n                                          </>\r\n                                    ) : (\r\n                                          <>\r\n                                                <Edit className=\"h-4 w-4\" /> Edit\r\n                                          </>\r\n                                    )}\r\n                              </button>\r\n                        </div>\r\n                        {inventoryConfigRadioFields.map(({ label, key }) => {\r\n                              const k = key as keyof SellerConfig;\r\n                              const currentValue = isEditingInventory\r\n                                    ? (pendingInventory[k] !== undefined ? pendingInventory[k] : config[k])\r\n                                    : config[k];\r\n                              return (\r\n                                    <div key={key} className=\"flex gap-4 items-center text-md\">\r\n                                          <Label className=\"w-[400px] text-md text-typography-400\">{label}</Label>\r\n                                          <RadioGroup\r\n                                                value={currentValue ? [\"auto_activate\"].includes(key) ? \"no\" : \"yes\" : [\"auto_activate\"].includes(key) ? \"yes\" : \"no\"}\r\n                                                onValueChange={(val) => {\r\n                                                      if (isEditingInventory) {\r\n                                                            if (k === \"auto_activate\") {\r\n                                                                  updatePending(\"inventory\", \"auto_activate\", val === \"no\");\r\n                                                                  console.log(`Inventory onChange: ${key} set to ${val === \"no\"}`);\r\n                                                            } else {\r\n                                                                  updatePending(\"inventory\", k, val === \"yes\");\r\n                                                                  console.log(`Inventory onChange: ${key} set to ${val === \"yes\"}`);\r\n                                                            }\r\n                                                      }\r\n                                                }}\r\n                                                disabled={!isEditingInventory}\r\n                                                className=\"flex gap-4 items-center text-md font-semibold text-typography-800\"\r\n                                          >\r\n                                                <div className=\"flex items-center gap-2\">\r\n                                                      <RadioGroupItem id={`${key}-yes`} value=\"yes\" />\r\n                                                      <Label htmlFor={`${key}-yes`}>Yes</Label>\r\n                                                </div>\r\n                                                <div className=\"flex items-center gap-2\">\r\n                                                      <RadioGroupItem id={`${key}-no`} value=\"no\" />\r\n                                                      <Label htmlFor={`${key}-no`}>No</Label>\r\n                                                </div>\r\n                                          </RadioGroup>\r\n                                    </div>\r\n                              );\r\n                        })}\r\n                        <div className=\"flex gap-4 items-center text-md\">\r\n                              <Label className=\"w-[400px] text-md text-typography-400\">Category Level:</Label>\r\n                              <RadioGroup\r\n                                    value={isEditingInventory\r\n                                          ? (pendingInventory.category_level !== undefined ? String(pendingInventory.category_level) : String(config.category_level))\r\n                                          : String(config.category_level)}\r\n                                    onValueChange={(val) => {\r\n                                          if (isEditingInventory) {\r\n                                                updatePending(\"inventory\", \"category_level\", Number(val));\r\n                                                console.log(`Inventory onChange: category_level set to ${val}`);\r\n                                          }\r\n                                    }}\r\n                                    disabled={!isEditingInventory}\r\n                                    className=\"flex gap-4 items-center text-md font-semibold text-typography-800\"\r\n                              >\r\n                                    <div className=\"flex items-center gap-2\">\r\n                                          <RadioGroupItem id=\"category_level-0\" value=\"0\" />\r\n                                          <Label htmlFor=\"category_level-0\">No Categories</Label>\r\n                                    </div>\r\n                                    <div className=\"flex items-center gap-2\">\r\n                                          <RadioGroupItem id=\"category_level-1\" value=\"1\" />\r\n                                          <Label htmlFor=\"category_level-1\">Level 1</Label>\r\n                                    </div>\r\n                                    <div className=\"flex items-center gap-2\">\r\n                                          <RadioGroupItem id=\"category_level-2\" value=\"2\" />\r\n                                          <Label htmlFor=\"category_level-2\">Level 2</Label>\r\n                                    </div>\r\n                                    <div className=\"flex items-center gap-2\">\r\n                                          <RadioGroupItem id=\"category_level-3\" value=\"3\" />\r\n                                          <Label htmlFor=\"category_level-3\">Level 3</Label>\r\n                                    </div>\r\n                              </RadioGroup>\r\n                        </div>\r\n                        {inventoryConfigFields.map(({ label, key, type, prefix, suffix, options }) => {\r\n                              const k = key as keyof SellerConfig;\r\n                              const currentValue = isEditingInventory\r\n                                    ? (pendingInventory[k] !== undefined ? pendingInventory[k] : config[k])\r\n                                    : config[k];\r\n\r\n                              return (\r\n                                    <div key={key} className=\"flex gap-4 items-center text-md\">\r\n                                          <Label className=\"w-[400px] text-md text-typography-400\">{label}</Label>\r\n                                          <span className=\"font-semibold flex gap-2 items-center\">\r\n                                                {prefix}\r\n                                                {type === \"select\" ? (\r\n                                                      <select\r\n                                                            value={String(isEditingInventory ? (pendingInventory[k] ?? config[k]) : config[k] || \"\")}\r\n                                                            onChange={(e) => {\r\n                                                                  if (isEditingInventory) {\r\n                                                                        updatePending(\"inventory\", k, e.target.value);\r\n                                                                        console.log(`Inventory onChange: ${key} set to ${e.target.value}`);\r\n                                                                  }\r\n                                                            }}\r\n                                                            disabled={!isEditingInventory}\r\n                                                            className=\"border border-neutral-400 rounded-md p-1 px-2\"\r\n                                                      >\r\n                                                            <option value=\"\" hidden>\r\n                                                                  Select an option\r\n                                                            </option>\r\n                                                            {options?.map((option) => (\r\n                                                                  <option key={option.value} value={option.value}>\r\n                                                                        {option.label}\r\n                                                                  </option>\r\n                                                            ))}\r\n                                                      </select>\r\n                                                ) : (\r\n                                                      <input\r\n                                                            type={type}\r\n                                                            value={String(isEditingInventory ? (pendingInventory[k] ?? config[k]) : config[k] || \"\")}\r\n                                                            onChange={(e) => {\r\n                                                                  if (isEditingInventory) {\r\n                                                                        updatePending(\"inventory\", k, e.target.value);\r\n                                                                        console.log(`Inventory onChange: ${key} set to ${e.target.value}`);\r\n                                                                  }\r\n                                                            }}\r\n                                                            disabled={!isEditingInventory}\r\n                                                            className=\"border border-neutral-400 rounded-md p-1 px-2\"\r\n                                                      />\r\n                                                )}\r\n                                                {suffix}\r\n                                          </span>\r\n                                    </div>\r\n                              );\r\n                        })}\r\n                        {isEditingInventory && (\r\n                              <div className=\"flex gap-4 justify-center\">\r\n                                    <button onClick={() => handleCancelSection(\"inventory\")} className=\"px-4 py-2 border rounded-md\">\r\n                                          Cancel\r\n                                    </button>\r\n                                    <button onClick={() => handleSaveSection(\"inventory\")} className=\"px-6 py-2 bg-primary text-white rounded-md\">\r\n                                          Save\r\n                                    </button>\r\n                              </div>\r\n                        )}\r\n                  </div>\r\n            </div>\r\n      );\r\n}", "import { GoogleMap, InfoWindow, Load<PERSON>, <PERSON>er, Polygon } from \"@react-google-maps/api\";\r\nimport { useCallback, useState } from \"react\";\r\nimport { Circle, Edit, Trash2, X } from \"lucide-react\";\r\nimport { MasterLocalities, smSellerArea, StateAndDistricts } from \"~/types/api/businessConsoleService/SellerManagement\";\r\nimport { Switch } from \"./switch\";\r\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from \"./select\";\r\nimport { Checkbox } from \"./checkbox\";\r\nimport { newLocal } from \"~/routes/home.networkDetails\";\r\nimport { number, string } from \"zod\";\r\nimport { useFetcher } from \"@remix-run/react\";\r\nimport SpinnerLoader from \"../loader/SpinnerLoader\";\r\nimport { Button } from \"./button\";\r\ninterface PointerLocation {\r\n      latitude: number | null;\r\n      longitude: number | null;\r\n}\r\n\r\ninterface InfoWindowState {\r\n      isShown: boolean;\r\n      areaId: number | null;\r\n      selectedAreaDetails: smSellerArea | null;\r\n}\r\n\r\ninterface MapComponentProps {\r\n      googleMapsApiKey: string;\r\n      sellerAreas: smSellerArea[];\r\n      visibleAreas: Set<number>;\r\n      getPolygonColor: (index: number) => string;\r\n      decodePolygon: (polygon: string) => google.maps.LatLngLiteral[];\r\n      getPolygonCenter: (polygon: google.maps.LatLngLiteral[]) => google.maps.LatLngLiteral;\r\n      handleLocateShopClicked: (state: boolean) => void;\r\n      onLoad: (mapInstance: google.maps.Map) => void;\r\n      onUnmount: () => void;\r\n      isLocateShopClicked: boolean;\r\n      updateToggle: (areaId: number) => void,\r\n      statesAndDistricts: StateAndDistricts[],\r\n      userId: number,\r\n      sellerId: number,\r\n      handleSubmit: (selectedLocalites: MasterLocalities[]) => void\r\n\r\n}\r\nconst MapComponent: React.FC<MapComponentProps> = ({\r\n      googleMapsApiKey,\r\n      sellerAreas,\r\n      visibleAreas,\r\n      getPolygonColor,\r\n      decodePolygon,\r\n      getPolygonCenter,\r\n      handleLocateShopClicked,\r\n      onLoad,\r\n      onUnmount,\r\n      isLocateShopClicked,\r\n      updateToggle,\r\n      statesAndDistricts,\r\n      userId,\r\n      sellerId,\r\n      handleSubmit\r\n}) => {\r\n      const [mapLoaded, setMapLoaded] = useState(false);\r\n      const [latitude, setLatitude] = useState<number | null>(null);\r\n      const [longitude, setLongitude] = useState<number | null>(null);\r\n      const [infoWindowShown, setInfoWindowShown] = useState<InfoWindowState>({\r\n            isShown: false,\r\n            areaId: null,\r\n            selectedAreaDetails: null,\r\n      });\r\n      const [pointerLocation, setPointerLocation] = useState<PointerLocation>({ latitude: null, longitude: null });\r\n      const [showMarker, setShowMarker] = useState(false);\r\n      const [isAddLocalityClicked, setisAddLocalityClicked] = useState(false);\r\n      const [selectedState, setSelectedState] = useState('');\r\n      const [masterLocalities, setMasterLocalities] = useState<MasterLocalities[]>([]);\r\n      const [newLocalities, setNewLocalities] = useState<MasterLocalities[]>([])\r\n      let stateList: string[] = [];\r\n      statesAndDistricts?.forEach((area) => stateList.push(area.state));\r\n      const [selectedDistrict, setSelectedDistrict] = useState('');\r\n      const handleFindLocations = useCallback((\r\n            lat: number | null,\r\n            long: number | null,\r\n            showMarker: boolean\r\n      ) => {\r\n            if (lat && long) {\r\n                  setPointerLocation({ latitude: lat, longitude: long });\r\n                  setShowMarker(showMarker);\r\n            }\r\n            else {\r\n                  alert(\"Please enter valid numeric values for latitude and longitude.\");\r\n            }\r\n      }, []);\r\n\r\n\r\n      const handleSwitch = (areaId: number) => {\r\n\r\n            updateToggle(areaId);\r\n      };\r\n      const handleAddLocalityClicked = useCallback((state: boolean) => {\r\n            setisAddLocalityClicked(state);\r\n      }, []\r\n      );\r\n\r\n      const handleMasterLocalitiesClicked = useCallback(async (userId: number, state: string, district: string) => {\r\n            if (!state || !district) {\r\n                  alert(\"Please select a state and district to proceed...\");\r\n                  return;\r\n            }\r\n\r\n            try {\r\n                  const response = await fetch(`./api/masterLocalities?userId=${userId}&state=${state}&district=${district}`);\r\n                  //apps/business-console/app/routes/api/masterLocalities.ts\r\n                  const data = await response.json();\r\n                  if (!response.ok) {\r\n                        throw new Error(data.error || \"Unknown error\");\r\n                  }\r\n                  setMasterLocalities(data.masterLocalities.data);\r\n            } catch (error) {\r\n                  console.error(\"Error fetching Master Localities:\", error);\r\n                  alert(\"Fetching Master Localities failed. Please try again.\");\r\n            }\r\n      }, []);\r\n\r\n      const [selectedLocalities, setSelectedLocalities] = useState<MasterLocalities[]>([]);\r\n\r\n      const [selectedLocalityId, setSelectedLocalityId] = useState<number | null>(null);\r\n      const handleNewLocalities = useCallback((id: number, localityDetails: MasterLocalities, agentUserId?: number) => {\r\n            setNewLocalities((prevState) => {\r\n                  let updatedLocalities;\r\n                  if (prevState.some((abc) => abc.id === id)) {\r\n                        updatedLocalities = prevState.filter((abc) => abc.id !== id);\r\n                  } else {\r\n                        updatedLocalities = [...prevState, localityDetails];\r\n                  }\r\n\r\n                  setSelectedLocalities(updatedLocalities);\r\n                  return updatedLocalities;\r\n            });\r\n      }, []);\r\n\r\n      // const handleNewLocalities = useCallback(\r\n      //       (id: number, localityDetails: MasterLocalities) => {\r\n      //             setNewLocalities((prevState) => {\r\n      //                   if (prevState.some((abc) => abc.id === id)) {\r\n      //                         // Remove the locality if it exists\r\n      //                         return prevState.filter((abc) => abc.id !== id);\r\n      //                   } else {\r\n      //                         // Add the new locality\r\n      //                         return [...prevState, localityDetails];\r\n      //                   }\r\n      //             });\r\n\r\n      //             setSelectedLocalityId((prev) => (prev === id ? null : id)); // Toggle selection\r\n      //       },\r\n      //       []\r\n      // );\r\n      const fetcher = useFetcher()\r\n      const isLoading = fetcher.state !== \"idle\";\r\n      const handleSubmitLocalities = () => {\r\n            handleSubmit(selectedLocalities)\r\n            setisAddLocalityClicked(false)\r\n            setSelectedDistrict('');\r\n            setSelectedState('');\r\n            stateList = [];\r\n            setMasterLocalities([]);\r\n            setSelectedLocalityId(null);\r\n            setSelectedLocalities([]);\r\n\r\n      }\r\n\r\n\r\n      const handleMarkerClick = useCallback(\r\n            (id: number,\r\n                  areaDetails: smSellerArea\r\n            ) => {\r\n                  setInfoWindowShown((prevState) => {\r\n                        if (prevState.isShown) {\r\n                              if (prevState.areaId === id) {\r\n\r\n                                    return { areaId: null, isShown: false, selectedAreaDetails: null };\r\n                              } else {\r\n\r\n                                    return { areaId: id, isShown: true, selectedAreaDetails: areaDetails };\r\n                              }\r\n                        } else {\r\n\r\n                              return { areaId: id, isShown: true, selectedAreaDetails: areaDetails };\r\n                        }\r\n                  });\r\n            },\r\n            []\r\n      );\r\n\r\n\r\n\r\n      return (\r\n            <LoadScript googleMapsApiKey={googleMapsApiKey}>\r\n\r\n                  <div className=\"flex w-full h-[87vh] border rounded-xl border-neutral-200 my-4\">\r\n                        <div className=\"flex flex-col gap-4 w-[20vw] overflow-auto bg-white shadow rounded-xl p-3\">\r\n                              {sellerAreas.length ? (\r\n\r\n                                    sellerAreas.map((seller, index) => {\r\n\r\n                                          return (\r\n                                                <div key={seller.sellerAreaId} className=\"flex flex-col w-full gap-3 bg-white p-2 rounded-lg shadow\">\r\n                                                      <div className=\"flex gap-2 justify-between items-center\">\r\n                                                            <div>\r\n                                                                  <div className=\"text-sm text-typography-400\">id: {seller.sellerAreaId}</div>\r\n                                                                  <div className=\"text-lg text-typography-800\">{seller.area.name}</div>\r\n                                                            </div>\r\n                                                            <Circle size={\"1rem\"} fill={getPolygonColor(index)} color={getPolygonColor(index)} />\r\n                                                      </div>\r\n\r\n\r\n                                                      <Button\r\n                                                            variant=\"ghost\"\r\n                                                            size=\"sm\"\r\n                                                            className=\"text-red-500 hover:text-red-900\"\r\n                                                            onClick={() => {\r\n                                                                  if (confirm(\"Are you sure you want to delete this area?\")) {\r\n                                                                        handleSwitch(seller?.sellerAreaId);\r\n                                                                  }\r\n                                                            }}\r\n                                                            style={{ alignSelf: \"flex-end\" }}\r\n                                                      >\r\n                                                            <Trash2 size={16} />\r\n                                                      </Button>\r\n\r\n\r\n                                                </div>\r\n                                          )\r\n                                    })\r\n                              ) : (\r\n                                    <div className=\"h-24 text-center\">No Network Areas found.</div>\r\n                              )}\r\n                        </div>\r\n                        <div className=\"relative flex-1 h-full bg-white\">\r\n                              <div className='absolute flex z-10 top-0 left-0 w-full justify-between p-3 max-h-full'>\r\n\r\n                                    {!isAddLocalityClicked ? (<div className='p-3 h-fit bg-white rounded-xl flex gap-2 text-blue-600 items-center shadow '>\r\n                                          <button\r\n                                                onClick={() => { handleAddLocalityClicked(true); }}\r\n                                          >\r\n                                                + &nbsp; Add New Locality\r\n                                          </button>\r\n                                    </div>) : (<div className='p-3 bg-white rounded-xl flex flex-col gap-2  items-center shadow '>\r\n\r\n                                          <div className='flex w-full justify-between'>\r\n                                                Add Localities\r\n                                                <button onClick={() => {\r\n\r\n                                                      handleAddLocalityClicked(false)\r\n                                                }}><X size={16} /></button>\r\n                                          </div>\r\n                                          <div className='flex w-full p-1 items-center gap-3'>\r\n                                                <Select value={selectedState} onValueChange={setSelectedState}>\r\n                                                      <SelectTrigger className=\"w-[180px]\">\r\n                                                            <SelectValue placeholder=\"Select state\" />\r\n                                                      </SelectTrigger>\r\n                                                      <SelectContent>\r\n                                                            {stateList.map((state) => (\r\n                                                                  <SelectItem key={state} value={state}>{state}</SelectItem>\r\n                                                            ))}\r\n                                                      </SelectContent>\r\n                                                </Select>\r\n                                                <Select value={selectedDistrict} onValueChange={(newDistrict) => {\r\n                                                      setSelectedDistrict(newDistrict); // Update selected district\r\n                                                      handleMasterLocalitiesClicked(userId, selectedState, newDistrict); // Fetch data\r\n                                                }} disabled={!selectedState}>\r\n                                                      <SelectTrigger className=\"w-[180px]\">\r\n                                                            <SelectValue placeholder=\"Select District\" />\r\n                                                      </SelectTrigger>\r\n                                                      <SelectContent>\r\n                                                            {statesAndDistricts\r\n                                                                  ?.filter((abc) => abc.state === selectedState)\r\n                                                                  .map((district) => (\r\n                                                                        <SelectItem key={district.district} value={district.district}>\r\n                                                                              {district.district}\r\n                                                                        </SelectItem>\r\n                                                                  ))}\r\n                                                      </SelectContent>\r\n                                                </Select>\r\n                                          </div>\r\n                                          <div className='flex flex-col gap-2 self-start max-h-full w-full overflow-auto p-2 '>\r\n                                                {masterLocalities && masterLocalities.length > 0 ? (\r\n                                                      (masterLocalities.filter(a => !sellerAreas?.some(na => na.area.id === a.id)).length > 0 ? masterLocalities.filter(a => !sellerAreas?.some(na => na.area.id === a.id)) : masterLocalities).map((locality, index) => (\r\n                                                            <>\r\n                                                                  <div key={locality.id}>\r\n                                                                        <label className=\"cursor-pointer flex items-center gap-2 p-1\" htmlFor={`locality-${locality.id}`}>\r\n                                                                              <Checkbox\r\n                                                                                    id={`locality-${locality.id}`}\r\n                                                                                    checked={newLocalities.some((abc) => abc.id === locality.id)}\r\n                                                                                    onCheckedChange={() => handleNewLocalities(locality.id, locality)}\r\n                                                                              />\r\n                                                                              <div className='flex flex-col gap-2'>\r\n                                                                                    <span>{locality.id} - {locality.name}</span>\r\n\r\n                                                                              </div>\r\n                                                                        </label>\r\n                                                                  </div>\r\n                                                                  {index < masterLocalities.length - 1 && (<div className='border-b border-neutral-200'></div>)} </>\r\n                                                      ))\r\n                                                ) : (\r\n                                                      <div>No Localities Fetched yet</div>\r\n                                                )} </div>\r\n\r\n\r\n                                          <button\r\n                                                type=\"submit\"\r\n                                                className=\"px-4 py-2 bg-blue-500 text-white rounded disabled:bg-gray-400\"\r\n                                                onClick={() => handleSubmitLocalities()}\r\n                                                disabled={selectedLocalities.length === 0}\r\n                                          >\r\n\r\n                                                {isLoading ? \"Submitting...\" : \"Submit Localities\"\r\n                                                }\r\n\r\n                                          </button>\r\n                                    </div>)\r\n                                    }\r\n                                    {!isLocateShopClicked ? (\r\n                                          <div className=\"p-3 absolute z-20 top-2 right-2 bg-white rounded-xl flex gap-2 text-blue-600 items-center shadow\">\r\n                                                <button onClick={() => handleLocateShopClicked(true)}>\r\n                                                      📍 &nbsp; Locate a shop\r\n                                                </button>\r\n                                          </div>\r\n                                    ) : (\r\n                                          <div className=\"p-3 absolute z-20 top-2 right-2 bg-white rounded-xl flex flex-col gap-2 items-center shadow\">\r\n                                                <div className=\"flex w-full justify-between\">\r\n                                                      Locate a shop\r\n                                                      <button onClick={() => handleLocateShopClicked(false)}>\r\n                                                            <X size={16} />\r\n                                                      </button>\r\n                                                </div>\r\n                                                <div className=\"flex w-full p-1 items-center justify-between\">\r\n                                                      <p>Latitude : </p>\r\n                                                      <input\r\n                                                            type=\"text\"\r\n                                                            className=\"border border-neutral-400 rounded-md p-1\"\r\n                                                            onChange={(e) => setLatitude(parseFloat(e.target.value))}\r\n                                                      />\r\n                                                </div>\r\n                                                <div className=\"flex gap-1 p-1 items-center\">\r\n                                                      <p>Longitude : </p>\r\n                                                      <input\r\n                                                            type=\"text\"\r\n                                                            className=\"border border-neutral-400 rounded-md p-1\"\r\n                                                            onChange={(e) => setLongitude(parseFloat(e.target.value))}\r\n                                                      />\r\n                                                </div>\r\n                                                <button\r\n                                                      className=\"text-primary border border-primary p-2 rounded-md\"\r\n                                                      onClick={() => handleFindLocations(latitude ?? 0, longitude ?? 0, true)}\r\n                                                >\r\n                                                      📍 &nbsp; Find Location\r\n                                                </button>\r\n                                          </div>\r\n                                    )}\r\n                              </div>\r\n                              {!mapLoaded && (\r\n                                    <div className=\"flex items-center justify-center h-full\">\r\n                                          <div className=\"loader\">Loading Map...</div>\r\n                                    </div>\r\n                              )}\r\n\r\n\r\n                              {googleMapsApiKey ? (\r\n                                    <GoogleMap\r\n                                          mapContainerStyle={{ width: \"100%\", height: \"100%\" }}\r\n                                          center={{ lat: 12.9716, lng: 77.5946 }}\r\n                                          zoom={11}\r\n                                          onLoad={(mapInstance) => {\r\n                                                onLoad(mapInstance); // Ensure this is always a valid function\r\n                                                setMapLoaded(true);\r\n                                          }}\r\n                                          onUnmount={() => {\r\n                                                onUnmount();\r\n                                                setMapLoaded(false);\r\n                                          }}\r\n                                          options={{\r\n                                                mapTypeControl: false,\r\n                                                streetViewControl: false,\r\n                                                fullscreenControl: false,\r\n                                                clickableIcons: false,\r\n                                                gestureHandling: \"auto\",\r\n                                          }}\r\n                                    >\r\n                                          {mapLoaded ? (sellerAreas?.filter((abc) => visibleAreas.has(abc.sellerAreaId))?.map((area, index) => (\r\n                                                area.area.polygon\r\n                                                && (\r\n\r\n                                                      <Polygon\r\n                                                            key={area.sellerAreaId}\r\n                                                            paths={decodePolygon(area.area.polygon)}\r\n                                                            options={{\r\n                                                                  fillColor: isAddLocalityClicked ? \"#3b82f6\" : getPolygonColor(index),\r\n                                                                  fillOpacity: 0.2,\r\n                                                                  strokeColor: isAddLocalityClicked ? \"#3b82f6\" : getPolygonColor(index),\r\n                                                                  strokeOpacity: 1,\r\n                                                                  strokeWeight: 2,\r\n                                                                  draggable: false,\r\n                                                                  editable: false,\r\n                                                                  geodesic: false,\r\n                                                                  zIndex: 1,\r\n                                                                  clickable: true, // Ensure polygons remain clickable\r\n                                                            }}\r\n                                                            onClick={() => handleMarkerClick(area.sellerAreaId, area)}\r\n\r\n                                                      />\r\n                                                )\r\n                                          ))) : null}\r\n\r\n                                          {mapLoaded ? (newLocalities?.map((area, index) => (\r\n                                                area.polygon\r\n                                                && (\r\n\r\n                                                      <Polygon\r\n                                                            key={area.id}\r\n                                                            paths={decodePolygon(area.polygon)}\r\n                                                            options={{\r\n                                                                  fillColor: \"#10b981\",\r\n                                                                  fillOpacity: 0.4,\r\n                                                                  strokeColor: \"#10b981\",\r\n                                                                  strokeOpacity: 1,\r\n                                                                  strokeWeight: 2,\r\n                                                                  draggable: false,\r\n                                                                  editable: false,\r\n                                                                  geodesic: false,\r\n                                                                  zIndex: 10,\r\n                                                                  clickable: false, // Ensure polygons remain clickable\r\n                                                            }}\r\n                                                      />\r\n                                                )\r\n                                          ))) : null}\r\n\r\n\r\n\r\n                                          {infoWindowShown && infoWindowShown.areaId && infoWindowShown.isShown && infoWindowShown.selectedAreaDetails && (\r\n\r\n                                                <>\r\n                                                      {console.log(\"entered infowindow params with ##########\", infoWindowShown)}\r\n                                                      <InfoWindow\r\n                                                            position={getPolygonCenter(decodePolygon(infoWindowShown.selectedAreaDetails?.area?.polygon || \"\"))}\r\n                                                            onCloseClick={() => setInfoWindowShown({ isShown: false, areaId: null, selectedAreaDetails: null })}\r\n                                                            options={{\r\n                                                                  headerDisabled: true,\r\n                                                                  minWidth: 200,\r\n                                                                  disableAutoPan: true,\r\n                                                            }}\r\n                                                      >\r\n                                                            <div className=\"flex flex-col gap-2 overflow-hidden \">\r\n                                                                  <div className='flex justify-between w-full align-middle items-center'>\r\n                                                                        <h2 className='text-md font-semibold text-typography-300'>Locality Info</h2>\r\n                                                                        <button className=\"inline-flex items-center gap-1 hover:text-blue-800\"\r\n                                                                              onClick={() => setInfoWindowShown({ isShown: false, areaId: null, selectedAreaDetails: null })}>\r\n                                                                              <X className=\"h-5 w-5\" />\r\n                                                                        </button>\r\n                                                                  </div>\r\n                                                                  <div className=\"flex flex-col gap-1 text-sm font-medium text-typography-700 w-[calc(100%-1rem)]\">\r\n                                                                        <p><span className='text-sm text-typography-300 font-thin'>Area id:</span> {infoWindowShown.selectedAreaDetails?.sellerAreaId}</p>\r\n                                                                        <p><span className='text-sm text-typography-300 font-thin'>Area Name: </span> {infoWindowShown.selectedAreaDetails?.area?.name}</p>\r\n                                                                  </div>\r\n                                                            </div>\r\n                                                      </InfoWindow>\r\n                                                      <style>\r\n                                                            {`.info-window-wrapper {\r\n                                  padding: 10px; \r\n                                }\r\n                              \r\n                                .gm-style-iw { \r\n                                  padding: 12px !important; \r\n                                }\r\n                                .gm-style-iw-d { \r\n                                  padding: 0px !important; \r\n                                  overflow:hidden !important;\r\n                                }`}\r\n                                                      </style> </>\r\n                                          )}\r\n                                          {showMarker && pointerLocation.latitude !== null && pointerLocation.longitude !== null && (\r\n                                                <Marker position={{ lat: pointerLocation.latitude, lng: pointerLocation.longitude }} />\r\n                                          )}\r\n                                    </GoogleMap>\r\n                              ) : (\r\n                                    <div className=\"flex items-center justify-center h-full\">\r\n                                          <p className=\"text-red-500\">Google Maps API key is missing.</p>\r\n                                    </div>\r\n                              )}\r\n                        </div>\r\n                  </div>\r\n            </LoadScript>\r\n      );\r\n};\r\n\r\nexport default MapComponent;\r\n", "import { Dayjs } from \"dayjs\";\r\nimport { Edit, Save, X } from \"lucide-react\";\r\nimport { useCallback, useState } from \"react\";\r\nimport { preview } from \"vite\";\r\nimport { BankConfig, BusinessData } from \"~/types/api/businessConsoleService/SellerManagement\";\r\n\r\ninterface BillingConfigProps {\r\n      billingConfig: BusinessData; // Make it optional\r\n      onSellerAttributeUpdate: (attribute: string, value: any, isSeller?: boolean) => void;\r\n}\r\n\r\n\r\n\r\n\r\n\r\nexport default function BillingConfig({ billingConfig, onSellerAttributeUpdate }: BillingConfigProps) {\r\n\r\n      const bankConfigFields = [\r\n            { label: \"Branch\", key: \"baBranch\", type: \"text\", prefix: \"\" },\r\n            { label: \"Account Number\", key: \"baNumber\", type: \"number\", prefix: \"\" },\r\n            { label: \"Verified By\", key: \"baVerifiedBy\", type: \"text\", prefix: \"\" },\r\n            { label: \"IFSC Code\", key: \"baIfsc\", type: \"text\", prefix: \"\" },\r\n            { label: \"Account Name\", key: \"baName\", type: \"text\", prefix: \"\" },\r\n            { label: \"Bank Name\", key: \"baBank\", type: \"text\", prefix: \"\" },\r\n            { label: \"Bank Account Verified\", key: \"baVerified\", type: \"checkbox\", prefix: \"\" }\r\n      ];\r\n      const plaFormConfigFields = [\r\n\r\n            { label: \"Platform Commission Percentage:  \", key: \"pcBasicPc\", type: \"number\", prefix: \"%\" },\r\n            { label: \"Fixed Platform Commission per month :  \", key: \"pcBasicPmFixed\", type: \"number\", prefix: \"₹\" },\r\n            { label: \"Minimum Platform Commission per month:  \", key: \"pcBasicPmMin\", type: \"number\", prefix: \"₹\" },\r\n            { label: \"Sales Commission Percentage:  \", key: \"salesCommPc\", type: \"number\", prefix: \"%\" },\r\n            { label: \".Sales Commission per Kg:  \", key: \"salesCommPkg\", type: \"number\", prefix: \"₹\" },\r\n            { label: \"Fixed Sales Commission per month:  \", key: \"salesCommPmFixed\", type: \"number\", prefix: \"₹\" },\r\n            { label: \"Minimum Sales Commission per month:  \", key: \"salesCommPmMin\", type: \"number\", prefix: \"₹\" },\r\n            { label: \"Agent Commision Percentage :  \", key: \"agentCommPc\", type: \"number\", prefix: \"%\" },\r\n            { label: \"Agent Commision per Kg:  \", key: \"agentCommPkg\", type: \"number\", prefix: \"₹\" },\r\n\r\n\r\n\r\n      ]\r\n\r\n      const [bankConfigEditable, setBankConfigEditable] = useState(false);\r\n      const [platFormEditable, setPlatFormEditable] = useState(false);\r\n\r\n      const [config, setConfig] = useState(billingConfig);\r\n\r\n      const setBankEditableClick = useCallback(() => {\r\n\r\n            setBankConfigEditable((prevState) => {\r\n                  if (prevState) {\r\n\r\n                        return false;\r\n\r\n                  } else {\r\n                        return true;\r\n                  }\r\n            })\r\n      }, []);\r\n      const setPlatformEditableClick = useCallback(() => {\r\n            setPlatFormEditable((prevState) => {\r\n                  if (prevState) {\r\n                        return false;\r\n                  } else {\r\n                        return true;\r\n                  }\r\n            })\r\n      }, []);\r\n\r\n      const [pendingConfig, setPendingConfig] = useState<Partial<BusinessData>>({});\r\n\r\n      const [visibleSaveButtons, setVisibleSaveButtons] = useState<Record<string, boolean>>({});\r\n      const keyMappingPlatform: Record<keyof BusinessData, string> = {\r\n            businessId: \"businessId\",\r\n            bankConfig: \"bankConfig\",\r\n            pcBasicPc: \"pcBasicPc\",\r\n            pcBasicPmFixed: \"pcBasicPmFixed\",\r\n            pcBasicPmMin: \"pcBasicPmMin\",\r\n            salesCommPc: \"salesCommPc\",\r\n            salesCommPkg: \"salesCommPkg\",\r\n            salesCommPmFixed: \"salesCommPmFixed\",\r\n            salesCommPmMin: \"salesCommPmMin\",\r\n            agentCommPc: \"agentCommPc\",\r\n            agentCommPkg: \"agentCommPkg\",\r\n      };\r\n      const bankConfigKeyMapping: Record<keyof BankConfig, string> = {\r\n            lubyName: \"lubyName\",\r\n            lubyId: \"lubyId\",\r\n            bid: \"bid\",\r\n            baVerified: \"baVerified\",\r\n            baBranch: \"baBranch\",\r\n            baNumber: \"baNumber\",\r\n            baVerifiedBy: \"baVerifiedBy\",\r\n            baIfsc: \"baIfsc\",\r\n            baName: \"baName\",\r\n            baBank: \"baBank\",\r\n      };\r\n\r\n      const handleConfigChange = (key: keyof BusinessData | keyof BankConfig, value: string | number | boolean | null) => {\r\n            console.log(value, key, \"Config Change Triggered\");\r\n\r\n            setPendingConfig((prev: any) => {\r\n                  let newValue: string | number | boolean | null = value;\r\n\r\n                  if (typeof value === \"string\") {\r\n                        if (value.toLowerCase() === \"yes\") {\r\n                              newValue = true;\r\n                        } else if (value.toLowerCase() === \"no\") {\r\n                              newValue = false;\r\n                        } else if (!isNaN(Number(value))) {\r\n                              newValue = Number(value);\r\n                        } else if (value === \"0\") {\r\n                              newValue = \"\"; // Prevent showing 0\r\n                        }\r\n                  }\r\n\r\n                  if (key in bankConfigKeyMapping) {\r\n                        return {\r\n                              ...prev,\r\n                              bankConfig: {\r\n                                    ...prev.bankConfig,\r\n                                    [key]: newValue,\r\n                              },\r\n                        };\r\n                  } else {\r\n                        return {\r\n                              ...prev,\r\n                              [key]: newValue,\r\n                        };\r\n                  }\r\n            });\r\n\r\n            setVisibleSaveButtons((prev) => ({\r\n                  ...prev,\r\n                  [key]: true,\r\n            }));\r\n      };\r\n\r\n\r\n      const handleSave = (key: keyof BusinessData | keyof BankConfig) => {\r\n            let updatedValue;\r\n\r\n            if (key in bankConfigKeyMapping) {\r\n                  updatedValue = pendingConfig.bankConfig?.[key as keyof BankConfig];\r\n                  onSellerAttributeUpdate(`${key}`, updatedValue);\r\n            } else {\r\n                  updatedValue = pendingConfig[key as keyof BusinessData];\r\n                  onSellerAttributeUpdate(key, updatedValue);\r\n            }\r\n\r\n            setConfig((prevConfig) => ({\r\n                  ...prevConfig,\r\n                  ...(key in bankConfigKeyMapping\r\n                        ? {\r\n                              bankConfig: {\r\n                                    ...prevConfig.bankConfig,\r\n                                    [key]: updatedValue,\r\n                              },\r\n                        }\r\n                        : { [key]: updatedValue }),\r\n            }));\r\n\r\n            setVisibleSaveButtons((prev) => ({\r\n                  ...prev,\r\n                  [key]: false,\r\n            }));\r\n\r\n            // Clear only the saved key from pendingConfig\r\n            setPendingConfig((prev) => {\r\n                  const updatedConfig = { ...prev };\r\n                  if (key in bankConfigKeyMapping) {\r\n                        delete updatedConfig.bankConfig?.[key as keyof BankConfig];\r\n                  } else {\r\n                        delete updatedConfig[key as keyof BusinessData];\r\n                  }\r\n                  return updatedConfig;\r\n            });\r\n      };\r\n\r\n      return (\r\n            <div className='flex flex-col gap-4 border bg-white rounded-xl border-neutral-200 my-4 p-4'>\r\n                  <div className=\"flex flex-col gap-4 border rounded-xl border-neutral-400 p-4 shadow\">\r\n                        <div className=\"flex justify-between w-full\">\r\n                              <div className=\"text-lg font-semibold text-typography-700\">Bank Configurations</div>\r\n                              <button\r\n                                    className=\"inline-flex items-center gap-1 text-blue-600 hover:text-blue-800\"\r\n                                    onClick={() => {\r\n                                          if (!bankConfigEditable) {\r\n\r\n                                                setPendingConfig({\r\n                                                      bankConfig: { ...billingConfig.bankConfig }\r\n                                                });\r\n                                          }\r\n\r\n\r\n                                          setBankEditableClick()\r\n                                    }\r\n                                    }\r\n                              >\r\n                                    {bankConfigEditable ? (\r\n                                          <>\r\n                                                <X className=\"h-4 w-4\" />\r\n                                                Cancel\r\n                                          </>\r\n                                    ) : (\r\n                                          <>\r\n                                                <Edit className=\"h-4 w-4\" />\r\n                                                Edit\r\n                                          </>\r\n                                    )}\r\n                              </button>\r\n                        </div>\r\n                        {/* Business ID Display */}\r\n                        <div className=\"flex gap-4 items-center\">\r\n                              <div className=\"text-md text-typography-500 w-[400px]\">Business ID:</div>\r\n                              {billingConfig.businessId}\r\n                        </div>\r\n\r\n                        {/* Bank Config Section */}\r\n                        <div>\r\n                              {bankConfigFields.map(({ label, key, type, prefix }) => (\r\n\r\n                                    <div key={key} className=\"flex flex-col gap-2 text-md text-typography-400\">\r\n                                          <div className=\"flex gap-4 items-center\">\r\n                                                <div className=\"text-md text-typography-500 w-[400px] mt-2\">{label}:</div>\r\n                                                <span className=\"font-semibold text-typography-800 flex items-center\">\r\n                                                      {prefix}\r\n\r\n\r\n                                                      {/* Render input based on type */}\r\n\r\n\r\n\r\n                                                      {type === 'checkbox' ? (\r\n                                                            <input\r\n                                                                  type=\"checkbox\"\r\n                                                                  checked={Boolean(\r\n                                                                        (pendingConfig?.bankConfig?.[key as keyof BankConfig] ?? billingConfig?.bankConfig?.[key as keyof BankConfig]) ?? false\r\n                                                                  )} onChange={(e) =>\r\n                                                                        handleConfigChange(\r\n                                                                              key as keyof BankConfig,\r\n                                                                              e.target.checked\r\n                                                                        )\r\n                                                                  }\r\n                                                                  className=\"border border-neutral-400 rounded-md p-1 mt-2\"\r\n                                                                  disabled={!bankConfigEditable}\r\n                                                            />\r\n                                                      ) : type === 'number' ? (\r\n                                                            <input\r\n                                                                  type=\"number\"\r\n                                                                  value={\r\n                                                                        pendingConfig?.bankConfig?.[key as keyof BankConfig] ??\r\n                                                                              billingConfig?.bankConfig?.[key as keyof BankConfig]\r\n                                                                              ? String(\r\n                                                                                    pendingConfig?.bankConfig?.[key as keyof BankConfig] ??\r\n                                                                                    billingConfig?.bankConfig?.[key as keyof BankConfig]\r\n                                                                              )\r\n                                                                              : ''\r\n                                                                  }\r\n                                                                  onChange={(e) => {\r\n                                                                        const value = e.target.value.trim();\r\n                                                                        if (value === '') {\r\n                                                                              handleConfigChange(key as keyof BankConfig, '');\r\n                                                                              return;\r\n                                                                        }\r\n                                                                        const numericValue = Math.max(0, Number(value)); // Ensures no negative numbers\r\n                                                                        handleConfigChange(key as keyof BankConfig, numericValue);\r\n                                                                  }}\r\n                                                                  className=\"border border-neutral-400 rounded-md p-1 px-2 mt-2\"\r\n                                                                  disabled={!bankConfigEditable}\r\n                                                            />\r\n                                                      ) : (\r\n                                                            <input\r\n                                                                  type=\"text\"\r\n\r\n                                                                  value={\r\n                                                                        pendingConfig?.bankConfig?.[key as keyof BankConfig] ??\r\n                                                                              billingConfig?.bankConfig?.[key as keyof BankConfig]\r\n                                                                              ? String(\r\n                                                                                    pendingConfig?.bankConfig?.[key as keyof BankConfig] ??\r\n                                                                                    billingConfig?.bankConfig?.[key as keyof BankConfig]\r\n                                                                              )\r\n                                                                              : ''\r\n                                                                  }\r\n                                                                  onChange={(e) => {\r\n                                                                        const value = e.target.value;\r\n\r\n                                                                        // Ensure empty string is passed when cleared\r\n                                                                        handleConfigChange(key as keyof BankConfig, value === \"0\" || value.trim() === \"\" ? \"\" : value);\r\n                                                                  }}\r\n                                                                  className=\"border border-neutral-400 rounded-md p-1 px-2 mt-2\"\r\n                                                                  disabled={!bankConfigEditable}\r\n                                                            />\r\n                                                      )}\r\n                                                </span>\r\n\r\n                                                {bankConfigEditable && visibleSaveButtons[key] && (\r\n                                                      <button\r\n                                                            className=\"flex flex-row text-blue-500 items-center gap-1 cursor-pointer text-lg font-bold\"\r\n                                                            onClick={() => handleSave(key as keyof BankConfig)}\r\n                                                      >\r\n                                                            <Save className=\"h-5 w-5\" />\r\n                                                      </button>\r\n                                                )}\r\n                                          </div>\r\n                                    </div>\r\n                              ))}\r\n\r\n                        </div>\r\n                  </div>\r\n                  <div className='flex flex-col gap-4 border rounded-xl border-neutral-400 p-4 shadow'>\r\n                        <div className='flex justify-between w-full'>\r\n                              <div className='text-lg font-semibold text-typography-700'>\r\n                                    PlatForm Configurations\r\n                              </div>\r\n                              <button\r\n                                    className=\"inline-flex items-center gap-1 text-blue-600 hover:text-blue-800\"\r\n                                    onClick={() => setPlatformEditableClick()}\r\n                              >\r\n                                    {platFormEditable ? <>\r\n                                          <X className='h-4 w-4' />\r\n                                          Cancel\r\n                                    </> : (\r\n                                          <>\r\n\r\n                                                <Edit className=\"h-4 w-4\" />\r\n                                                Edit\r\n                                          </>\r\n                                    )}\r\n                              </button>\r\n                        </div>\r\n\r\n\r\n                        {plaFormConfigFields.map(({ label, key, type, prefix }) => (\r\n\r\n                              <div key={key} className=\"flex flex-col gap-2 text-md text-typography-400\">\r\n                                    <div className=\"flex gap-4 items-center\">\r\n                                          <div className=\"text-md text-typography-500 w-[400px]\">{label}\r\n                                          </div>\r\n                                          <span className=\"font-semibold text-typography-800 flex gap-2 items-center\">\r\n\r\n                                                <input\r\n                                                      type={type}\r\n                                                      value={String(pendingConfig[key as keyof BusinessData] ?? config[key as keyof BusinessData])}\r\n\r\n\r\n                                                      // Convert to string\r\n                                                      onChange={(e) => {\r\n                                                            const numericValue = Math.max(0, Number(e.target.value)); // Ensures no negative numbers\r\n\r\n\r\n                                                            handleConfigChange(key as keyof BusinessData, numericValue)\r\n                                                      }}\r\n                                                      className=\"border border-neutral-400 rounded-md p-1 px-2\"\r\n\r\n                                                      disabled={!platFormEditable}\r\n                                                />\r\n                                                {prefix}\r\n                                          </span>\r\n                                          {plaFormConfigFields && visibleSaveButtons[key] && (\r\n                                                <button\r\n                                                      className=\"flex flex-row text-blue-500 items-center gap-1 cursor-pointer text-lg font-bold\"\r\n                                                      onClick={() => handleSave(key as keyof BusinessData)}\r\n                                                >\r\n                                                      <Save className=\"h-5 w-5\" />\r\n                                                </button>\r\n                                          )}\r\n                                    </div>\r\n\r\n                              </div>\r\n                        ))}\r\n                  </div>\r\n            </div>\r\n      )\r\n}", "import { use<PERSON><PERSON>cher } from \"@remix-run/react\";\r\nimport React, { useState, useEffect, useRef } from \"react\";\r\nimport { Dialog, DialogContent, DialogTitle } from \"../ui/dialog\";\r\nimport { Suppliers } from \"~/types/api/businessConsoleService/SellerManagement\";\r\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from \"../ui/select\";\r\nimport { SellerItem } from \"~/types/api/businessConsoleService/MyItemList\";\r\n\r\ninterface EditModalProps {\r\n  isOpen: boolean;\r\n  data: Partial<SellerItem>;\r\n  onClose: () => void;\r\n  multiple: boolean;\r\n  supplierList: Suppliers[];\r\n  onSave: (updatedData: any) => void;\r\n}\r\n\r\nconst fields = [\r\n  { name: \"name\", type: \"text\", label: \"Name\" },\r\n  { name: \"unit\", type: \"text\", label: \"Unit\" },\r\n  { name: \"packaging\", type: \"text\", label: \"Packaging\" },\r\n  { name: \"weightFactor\", type: \"number\", label: \"Weight Factor\" },\r\n  { name: \"maxAvailableQty\", type: \"number\", label: \"Max Available Quantity\" },\r\n  { name: \"minOrderQty\", type: \"number\", label: \"Min Order Quantity\" },\r\n  { name: \"maxOrderQty\", type: \"number\", label: \"Max Order Quantity\" },\r\n  { name: \"incrementOrderQty\", type: \"number\", label: \"Increment Order Quantity\" },\r\n  { name: \"pricePerUnit\", type: \"number\", label: \"Price per Unit\" },\r\n  { name: \"displayOrder\", type: \"number\", label: \"Display Order\" },\r\n  { name: \"description\", type: \"text\", label: \"Description\" },\r\n  { name: \"strikeOffPrice\", type: \"number\", label: \"StrikeOffPrice\" },\r\n  { name: \"distChargePu\", type: \"number\", label: \"DistChargePu\" },\r\n  { name: \"distChargePc\", type: \"number\", label: \"DistChargePc\" },\r\n  { name: \"minAcPkg\", type: \"number\", label: \"MinAcPkg\" },\r\n  { name: \"maxAcPkg\", type: \"number\", label: \"MaxAcPkg\" },\r\n  { name: \"minDistcPkg\", type: \"number\", label: \"MinDistcPkg\" },\r\n  { name: \"maxDistcPkg\", type: \"number\", label: \"MaxDistcPkg\" },\r\n  { name: \"packagingCharge\", type: \"number\", label: \"Packaging Charge\" },\r\n];\r\n\r\nconst EditModal: React.FC<EditModalProps> = ({\r\n  isOpen,\r\n  data,\r\n  onClose,\r\n  onSave,\r\n  multiple,\r\n  supplierList,\r\n}) => {\r\n  const [formData, setFormData] = useState<Partial<SellerItem>>(data);\r\n  const uploadFetcher = useFetcher<{ success: boolean; fileUrl: string; error?: string }>();\r\n  const [uploadError, setUploadError] = useState<string | null>(null);\r\n  const [uploading, setUploading] = useState(false);\r\n  const fileInputRef = useRef<HTMLInputElement>(null);\r\n\r\n  useEffect(() => {\r\n    setFormData(data);\r\n  }, [data]);\r\n\r\n  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {\r\n    const files = Array.from(event.target.files || []);\r\n    if (files.length === 0) return;\r\n\r\n    setUploadError(null);\r\n\r\n    const MAX_FILE_SIZE = 500 * 1024; // 500kb\r\n    const allowedTypes = [\"image/jpeg\", \"image/png\", \"image/gif\", \"image/webp\"];\r\n\r\n    const validFile = files.find((file) => {\r\n      if (file.size > MAX_FILE_SIZE) {\r\n        setUploadError(\"File size exceeds 500kb limit.\");\r\n        return false;\r\n      }\r\n      if (!allowedTypes.includes(file.type)) {\r\n        setUploadError(\"Only JPEG, PNG, GIF, and WEBP images are allowed.\");\r\n        return false;\r\n      }\r\n      return true;\r\n    });\r\n\r\n    if (!validFile) return;\r\n\r\n    const uploadFormData = new FormData();\r\n    uploadFormData.append(\"_action\", \"uploadImage\");\r\n    uploadFormData.append(\"file\", validFile, validFile.name);\r\n\r\n    setUploading(true);\r\n\r\n    uploadFetcher.submit(uploadFormData, {\r\n      method: \"post\",\r\n      action: \"/home/<USER>\",\r\n      encType: \"multipart/form-data\",\r\n    });\r\n  };\r\n\r\n  // Only update the changed field, and handle number fields properly\r\n  const handleChange = (\r\n    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>\r\n  ) => {\r\n    const { name, value, type } = e.target;\r\n    setFormData((prev: any) => ({\r\n      ...prev,\r\n      [name]:\r\n        type === \"number\"\r\n          ? value === \"\" ? \"\" : Number(value)\r\n          : type === \"radio\" && (name === \"taxExempt\" || name === \"freeItem\")\r\n            ? value === \"true\"\r\n            : value,\r\n    }));\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (uploadFetcher.data) {\r\n      if (uploadFetcher.data.error) {\r\n        setUploadError(uploadFetcher.data.error);\r\n      } else if (uploadFetcher.data.fileUrl) {\r\n        const uploadedUrl = uploadFetcher.data.fileUrl;\r\n        setFormData((prev: any) => ({\r\n          ...prev,\r\n          picture: uploadedUrl,\r\n        }));\r\n        setUploadError(null);\r\n        if (fileInputRef.current) fileInputRef.current.value = \"\";\r\n      }\r\n      setUploading(false);\r\n    }\r\n  }, [uploadFetcher.data]);\r\n\r\n  const handleSave = () => {\r\n    // if selectedItem is present then compare with changed formData and remove unchanged fields\r\n    const updatedData: Partial<SellerItem> = { ...formData };\r\n    if (Object.keys(data).length > 0) {\r\n      for (const key in formData) {\r\n        const typedKey = key as keyof SellerItem;\r\n        if (updatedData[typedKey] === data[typedKey]) {\r\n          delete updatedData[typedKey];\r\n        }\r\n      }\r\n      updatedData.Id = data.Id;\r\n    }\r\n\r\n    onSave(updatedData);\r\n  };\r\n\r\n  if (!isOpen) return null;\r\n\r\n  return (\r\n    <Dialog open={isOpen} onOpenChange={onClose}>\r\n      <DialogContent\r\n        className=\"p-4 bg-white rounded-lg shadow-lg w-full max-w-md sm:max-w-lg md:max-w-xl max-h-[80vh] flex flex-col\"\r\n        style={{ width: \"95vw\" }}\r\n      >\r\n        <DialogTitle className=\"text-xl font-bold mb-2 text-center\">Edit Seller Item</DialogTitle>\r\n        <div className=\"flex-1 overflow-y-auto space-y-4\">\r\n          {/* Image Upload */}\r\n          <div className=\"flex flex-col items-center gap-2\">\r\n            <h3 className=\"text-base font-semibold text-gray-800\">Upload Image</h3>\r\n            <div className=\"flex items-center gap-4\">\r\n              {formData && 'picture' in formData && formData.picture && (\r\n                <img\r\n                  src={formData.picture}\r\n                  alt=\"Preview\"\r\n                  className=\"h-20 w-20 object-cover rounded-full border-2 border-gray-300\"\r\n                />\r\n              )}\r\n              <input\r\n                ref={fileInputRef}\r\n                type=\"file\"\r\n                accept=\"image/*\"\r\n                onChange={handleFileSelect}\r\n                className=\"file:mr-4 file:py-1 file:px-3 file:rounded-full file:border-0 file:text-xs file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100\"\r\n              />\r\n            </div>\r\n            {uploadError && <p className=\"text-red-500 text-xs\">{uploadError}</p>}\r\n          </div>\r\n\r\n          {/* Editable Fields */}\r\n          <div className=\"grid grid-cols-1 sm:grid-cols-2 gap-3\">\r\n            {fields.map((field) => (\r\n              <div key={field.name} className=\"flex flex-col\">\r\n                <label className=\"text-xs font-medium text-gray-700 mb-1\">\r\n                  {field.label}\r\n                </label>\r\n                <input\r\n                  type={field.type}\r\n                  name={field.name}\r\n                  value={\r\n                    field.type === \"number\"\r\n                      ? (formData as any)?.[field.name] ?? \"\"\r\n                      : (formData as any)?.[field.name] || \"\"\r\n                  }\r\n                  onChange={handleChange}\r\n                  className=\"p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-400 text-sm\"\r\n                  placeholder={field.label}\r\n                  min={field.type === \"number\" ? 0 : undefined}\r\n                />\r\n              </div>\r\n            ))}\r\n          </div>\r\n\r\n          {/* Supplier Select */}\r\n          <div>\r\n            <label className=\"block text-xs font-medium text-gray-700 mb-1\">\r\n              Select Supplier\r\n            </label>\r\n            <select\r\n              name=\"supplierItemId\"\r\n              value={formData.supplierItemId}\r\n              onChange={handleChange}\r\n              required\r\n              className=\"w-full rounded border border-gray-300 p-2 focus:border-blue-400 focus:ring focus:ring-blue-200 focus:ring-opacity-50 text-sm\"\r\n            >\r\n              <option value=\"\">Select Supplier</option>\r\n              {supplierList?.map((supplier) => (\r\n                <option\r\n                  key={supplier?.supplierItemId}\r\n                  value={supplier?.supplierItemId?.toString()}\r\n                >\r\n                  {supplier?.supplierName}\r\n                </option>\r\n              ))}\r\n            </select>\r\n          </div>\r\n\r\n          {/* Radio Groups */}\r\n          <div className=\"grid grid-cols-1 sm:grid-cols-2 gap-3\">\r\n            <div className=\"flex flex-col\">\r\n              <label className=\"text-xs font-medium text-gray-700 mb-1\">Exclude Tax:</label>\r\n              <div className=\"flex gap-4\">\r\n                <label className=\"flex items-center gap-1 text-xs\">\r\n                  <input\r\n                    type=\"radio\"\r\n                    name=\"taxExempt\"\r\n                    value=\"true\"\r\n                    checked={formData?.taxExempt === true}\r\n                    onChange={handleChange}\r\n                  />\r\n                  Yes\r\n                </label>\r\n                <label className=\"flex items-center gap-1 text-xs\">\r\n                  <input\r\n                    type=\"radio\"\r\n                    name=\"taxExempt\"\r\n                    value=\"false\"\r\n                    checked={formData?.taxExempt === false}\r\n                    onChange={handleChange}\r\n                  />\r\n                  No\r\n                </label>\r\n              </div>\r\n            </div>\r\n            <div className=\"flex flex-col\">\r\n              <label className=\"text-xs font-medium text-gray-700 mb-1\">Free Item:</label>\r\n              <div className=\"flex gap-4\">\r\n                <label className=\"flex items-center gap-1 text-xs\">\r\n                  <input\r\n                    type=\"radio\"\r\n                    name=\"freeItem\"\r\n                    value=\"true\"\r\n                    checked={formData?.freeItem === true}\r\n                    onChange={handleChange}\r\n                  />\r\n                  Yes\r\n                </label>\r\n                <label className=\"flex items-center gap-1 text-xs\">\r\n                  <input\r\n                    type=\"radio\"\r\n                    name=\"freeItem\"\r\n                    value=\"false\"\r\n                    checked={formData?.freeItem === false}\r\n                    onChange={handleChange}\r\n                  />\r\n                  No\r\n                </label>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Diet Select */}\r\n          <div>\r\n            <label className=\"text-xs font-medium text-gray-700 mb-1\">Diet:</label>\r\n            <Select\r\n              value={formData?.diet}\r\n              onValueChange={(value) =>\r\n                setFormData((prev: any) => ({ ...prev, diet: value }))\r\n              }\r\n            >\r\n              <SelectTrigger className=\"w-full\">\r\n                <SelectValue placeholder=\"Select Diet\" />\r\n              </SelectTrigger>\r\n              <SelectContent>\r\n                <SelectItem value=\"veg\">Veg</SelectItem>\r\n                <SelectItem value=\"nonveg\">Nonveg</SelectItem>\r\n                <SelectItem value=\"egg\">Egg</SelectItem>\r\n              </SelectContent>\r\n            </Select>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Buttons */}\r\n        <div className=\"flex flex-col sm:flex-row justify-end gap-2 mt-6\">\r\n          <button\r\n            onClick={onClose}\r\n            className=\"w-full sm:w-auto px-5 py-2 bg-gray-400 text-white rounded hover:bg-gray-500 transition\"\r\n          >\r\n            Cancel\r\n          </button>\r\n          <button\r\n            onClick={handleSave}\r\n            className=\"w-full sm:w-auto px-5 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition\"\r\n          >\r\n            Save\r\n          </button>\r\n        </div>\r\n      </DialogContent>\r\n    </Dialog>\r\n  );\r\n};\r\n\r\nexport default EditModal;", "import { MyAddonGroupData } from \"~/types/api/businessConsoleService/SellerManagement\";\r\nimport { Dialog, DialogContent, DialogTitle } from \"../ui/dialog\";\r\nimport React, { useState, useEffect } from \"react\";\r\nimport { useFetcher } from \"@remix-run/react\";\r\nimport { useToast } from \"../ui/ToastProvider\";\r\n\r\ninterface AddonsGroupProps {\r\n      isOpen: boolean;\r\n      data?: MyAddonGroupData | undefined;\r\n      onClose: () => void;\r\n      header: string;\r\n      sellerId: number\r\n}\r\n\r\nconst AddonsGroupModal: React.FC<AddonsGroupProps> = ({ isOpen, data, onClose, header, sellerId }) => {\r\n      const [formData, setFormData] = useState<MyAddonGroupData>(data ||\r\n      {\r\n\r\n            internalName: \"\",\r\n            displayName: \"\",\r\n            description: \"\",\r\n            active: false,\r\n\r\n      }\r\n      );\r\n      const addonsGroupFetcher = useFetcher()\r\n\r\n      const loading = addonsGroupFetcher.state != \"idle\";\r\n\r\n      const { showToast } = useToast();\r\n\r\n      useEffect(() => {\r\n            if (addonsGroupFetcher.data) {\r\n\r\n                  if (addonsGroupFetcher.data?.sucess) {\r\n\r\n                        showToast(\"sucessFully added\", \"success\")\r\n                        onClose();\r\n                        setFormData({\r\n                              internalName: \"\",\r\n                              displayName: \"\",\r\n                              description: \"\",\r\n                              active: false,\r\n                        })\r\n                  }\r\n\r\n                  else if (!addonsGroupFetcher.data?.sucess) {\r\n                        showToast(\"Failed to add. Please try again.\", \"error\");\r\n                  }\r\n            }\r\n\r\n      }, [addonsGroupFetcher.data])\r\n\r\n      useEffect(() => {\r\n            if (data && isOpen) {\r\n                  setFormData(data)\r\n            }\r\n            else if (!isOpen) {\r\n                  setFormData({\r\n                        internalName: \"\",\r\n                        displayName: \"\",\r\n                        description: \"\",\r\n                        active: false,\r\n                  })\r\n            }\r\n\r\n      }, [data, isOpen])\r\n\r\n\r\n\r\n\r\n      const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {\r\n            const { name, value } = e.target;\r\n            setFormData((prevData) => ({\r\n                  ...prevData,\r\n                  [name]: name === \"active\" ? value === \"true\" : value // Convert active to boolean\r\n            }));\r\n      };\r\n\r\n      const handleSave = () => {\r\n            const fetcherData = new FormData();\r\n            fetcherData.append(\"actionType\", \"addonGroupAdd\");\r\n            fetcherData.append(\"mode\", \"editMode\");\r\n            fetcherData.append(\"AddonGId\", data?.id?.toString())\r\n            fetcherData.append(\"sellerId\", sellerId as unknown as string);\r\n            fetcherData.append(\"addonGroupData\", JSON.stringify(formData))\r\n            addonsGroupFetcher.submit(fetcherData, { method: \"POST\" })\r\n            onClose()\r\n      };\r\n      if (!isOpen) return null;\r\n\r\n\r\n\r\n\r\n      return (\r\n            <Dialog open={isOpen} onOpenChange={onClose}>\r\n                  <DialogContent className=\"max-w-md p-6 bg-white rounded-xl shadow-2xl sm:max-w-lg\">\r\n                        <DialogTitle className=\"text-xl font-bold text-gray-900 sm:text-2xl\">{header}</DialogTitle>\r\n\r\n                        <div className=\"mt-4 space-y-5 max-h-[60vh] overflow-y-auto pr-2 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100\">\r\n                              {/* Editable Fields */}\r\n                              <div className=\"space-y-4\">\r\n                                    {[\r\n                                          { id: 1, name: \"internalName\" as const, type: \"text\", label: \"InternalName\" },\r\n                                          { id: 2, name: \"displayName\" as const, type: \"text\", label: \"DisplayName\" },\r\n                                          { id: 3, name: \"description\" as const, type: \"text\", label: \"Description\" },\r\n\r\n                                    ].map((field) => (\r\n                                          <div key={field.name} className=\"flex flex-col gap-2 sm:flex-row sm:items-center\">\r\n                                                <label className=\"w-full text-sm font-medium text-gray-700 sm:w-1/3\">{field.label}</label>\r\n\r\n\r\n                                                <input\r\n                                                      type={field.type}\r\n                                                      name={field.name}\r\n                                                      value={formData?.[field.name as keyof MyAddonGroupData] ?? \"\"} onChange={handleChange}\r\n                                                      className=\"w-full rounded-lg border-gray-300 bg-gray-50 p-2.5 text-sm text-gray-900 shadow-sm focus:border-blue-500 focus:ring-2 focus:ring-blue-500 sm:w-2/3\"\r\n                                                      placeholder={`Enter ${field.label}`}\r\n                                                />\r\n\r\n                                          </div>\r\n                                    ))}\r\n                              </div>\r\n\r\n                              {/* Radio Button Group */}\r\n                              <div className=\"flex flex-col gap-2 sm:flex-row sm:items-center\">\r\n                                    <label className=\"w-full text-sm font-medium text-gray-700 sm:w-1/3\">Active Status</label>\r\n                                    <div className=\"flex gap-6 w-full sm:w-2/3\">\r\n                                          <label className=\"flex items-center gap-2 text-sm text-gray-600 cursor-pointer\">\r\n                                                <input\r\n                                                      type=\"radio\"\r\n                                                      name=\"active\"\r\n                                                      value=\"true\"\r\n                                                      checked={formData?.active === true}\r\n                                                      onChange={handleChange}\r\n                                                      className=\"h-4 w-4 text-blue-600 focus:ring-blue-500\"\r\n                                                />\r\n                                                Yes\r\n                                          </label>\r\n                                          <label className=\"flex items-center gap-2 text-sm text-gray-600 cursor-pointer\">\r\n                                                <input\r\n                                                      type=\"radio\"\r\n                                                      name=\"active\"\r\n                                                      value=\"false\"\r\n                                                      checked={formData?.active === false}\r\n                                                      onChange={handleChange}\r\n                                                      className=\"h-4 w-4 text-blue-600 focus:ring-blue-500\"\r\n                                                />\r\n                                                No\r\n                                          </label>\r\n                                    </div>\r\n                              </div>\r\n                        </div>\r\n\r\n                        {/* Action Buttons */}\r\n                        <div className=\"mt-6 flex flex-col gap-3 sm:flex-row sm:justify-end\">\r\n                              <button\r\n                                    onClick={onClose}\r\n                                    className=\"w-full rounded-lg bg-gray-100 px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-300 sm:w-auto\"\r\n                              >\r\n                                    Cancel\r\n                              </button>\r\n                              <button\r\n                                    onClick={handleSave}\r\n                                    disabled={loading} // Disable button while loading\r\n                                    className={`w-full rounded-lg ${loading ? 'bg-gray-400' : 'bg-blue-600'} px-4 py-2 text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 sm:w-auto`}\r\n                              >\r\n                                    {loading ? 'Saving...' : 'Save'} {/* Show loader text */}\r\n                              </button>\r\n                        </div>\r\n                  </DialogContent>\r\n            </Dialog>\r\n      );\r\n};\r\n\r\nexport default AddonsGroupModal;", "import React, { useEffect, useState } from 'react';\r\nimport { Input } from '~/components/ui/input';\r\nimport { Pencil, Search, Trash } from 'lucide-react';\r\nimport { Button } from '~/components/ui/button';\r\nimport { ResponsiveTable } from '../ui/responsiveTable';\r\nimport { MyAddonGroupData, MyVariationData } from '~/types/api/businessConsoleService/SellerManagement';\r\nimport AddonsGroupModal from './AddonsGropModal';\r\nimport { useFetcher, useNavigate } from '@remix-run/react';\r\nimport { useDebounce } from '~/hooks/useDebounce';\r\nimport SpinnerLoader from '../loader/SpinnerLoader';\r\n\r\n\r\ninterface MyAddonsGroupTabProps {\r\n      addonGroupTabData: MyAddonGroupData[];\r\n      sellerId: number\r\n}\r\n\r\nconst myAddonGroupHeader = [\r\n      \"Id\",\r\n      \"InternalName\",\r\n      \"DisplayName\",\r\n      \"Description\",\r\n      \"Active\",\r\n      \"\",\r\n      \"\"\r\n];\r\n\r\nconst MyAddonsGroupTab: React.FC<MyAddonsGroupTabProps> = ({\r\n      addonGroupTabData,\r\n      sellerId\r\n}) => {\r\n      const [searchTerm, setSearchTerm] = useState('');\r\n\r\n\r\n      // const handlePageSearch = (value: string) => {\r\n      //       setSearch(value);\r\n      // };\r\n      // const handlePageSizeChange = (newPageSize: string) => {\r\n      //       setPageSize(newPageSize);\r\n      // };\r\n      // const handlePageChange = (newPageNum: number) => {\r\n      //       setPageNum(newPageNum);\r\n      // };\r\n\r\n      const addOnGroupFetcher = useFetcher()\r\n      const handleDelete = (addgroupData: MyAddonGroupData) => {\r\n            const formData = new FormData();\r\n            formData.append(\"actionType\", \"addonsgroupDelete\");\r\n            formData.append(\"addonGId\", addgroupData?.id.toString())\r\n            formData.append(\"sellerId\", sellerId.toString())\r\n            addOnGroupFetcher.submit(formData, { method: 'post' })\r\n      };\r\n      const navigate = useNavigate();\r\n      const [selectedAddonsGData, setSelectedAddonsGData] = useState<MyAddonGroupData>();\r\n      const [isAddonsGEdit, setIsAddonsGEdit] = useState(false);\r\n      const [addonGroupModalOpen, setAddonGroupModalOpen] = useState(false);\r\n\r\n      const handleEditModal = (row: MyAddonGroupData) => {\r\n            setSelectedAddonsGData(row);\r\n            setIsAddonsGEdit(true)\r\n            setAddonGroupModalOpen(true)\r\n      }\r\n      const handleAddModal = () => {\r\n            setSelectedAddonsGData(undefined);\r\n            setIsAddonsGEdit(false)\r\n            setAddonGroupModalOpen(true)\r\n      }\r\n\r\n\r\n      const [filteredGroupData, setFilteredGroupData] = useState<MyAddonGroupData[]>(addonGroupTabData);\r\n\r\n      const debouncedSearchTerm = useDebounce(searchTerm, 300);\r\n\r\n      useEffect(() => {\r\n            if (debouncedSearchTerm.length >= 3 && debouncedSearchTerm !== \"\") {\r\n                  const filtered = addonGroupTabData.filter((item) =>\r\n                        [item.displayName, item.internalName].some(\r\n                              (field) => field?.toLowerCase().includes(debouncedSearchTerm.toLowerCase())\r\n                        )\r\n                  )\r\n                  setFilteredGroupData(filtered);\r\n            }\r\n            else {\r\n                  setFilteredGroupData(addonGroupTabData)\r\n            }\r\n      }, [debouncedSearchTerm, addonGroupTabData]);\r\n\r\n\r\n      const loading = addOnGroupFetcher.state !== \"idle\"\r\n\r\n\r\n      return (\r\n            <>\r\n                  <div className=\"flex justify-between mb-4\">\r\n                        {loading && <SpinnerLoader loading={loading} size={20} />}\r\n\r\n                        <Input\r\n                              placeholder=\"Search by Internal Name && display Name\"\r\n                              value={searchTerm}\r\n\r\n                              type='search'\r\n                              onChange={(e) => setSearchTerm(e.target.value)}\r\n                              className=\"max-w-sm mt-2 rounded-full \"\r\n                        />\r\n                        {/* <Select value={pageSize} onValueChange={handlePageSizeChange}>\r\n                              <SelectTrigger className=\"w-[180px]\">\r\n                                    <SelectValue placeholder=\"Items per page\" />\r\n                              </SelectTrigger>\r\n                              <SelectContent>\r\n                                    <SelectItem value=\"5\">5 per page</SelectItem>\r\n                                    <SelectItem value=\"10\">10 per page</SelectItem>\r\n                                    <SelectItem value=\"20\">20 per page</SelectItem>\r\n                                    <SelectItem value=\"50\">50 per page</SelectItem>\r\n                              </SelectContent>\r\n                        </Select> */}\r\n                  </div>\r\n                  {filteredGroupData.length === 0 ? (\r\n                        <div className=\"text-center py-4\">No results found</div>\r\n                  ) : (\r\n                        <ResponsiveTable\r\n                              headers={myAddonGroupHeader}\r\n                              data={filteredGroupData}\r\n                              renderRow={(row) => (\r\n                                    <tr key={row.id} className=\"border-b\">\r\n                                          <td className=\"py-2 px-3 text-center whitespace-normal break-words \" >{row.id}</td>\r\n                                          <td className=\"py-2 px-3 text-center whitespace-normal break-words text-blue-600 cursor-pointer \" onClick={() => navigate(`/home/<USER>/td>\r\n                                          <td className=\"py-2 px-3 text-center whitespace-normal break-words\">{row?.displayName}</td>\r\n                                          <td className=\"py-2 px-3 text-center whitespace-normal break-words\">{row?.description}</td>\r\n\r\n                                          <td className=\"py-2 px-3 text-center whitespace-normal break-words\">\r\n                                                {row?.active ? (\r\n                                                      <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800\">\r\n                                                            <svg\r\n                                                                  className=\"w-4 h-4 mr-1 text-green-500\"\r\n                                                                  fill=\"currentColor\"\r\n                                                                  viewBox=\"0 0 20 20\"\r\n                                                                  xmlns=\"http://www.w3.org/2000/svg\"\r\n                                                            >\r\n                                                                  <path\r\n                                                                        fillRule=\"evenodd\"\r\n                                                                        d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\"\r\n                                                                        clipRule=\"evenodd\"\r\n                                                                  />\r\n                                                            </svg>\r\n                                                            Active\r\n                                                      </span>\r\n                                                ) : (\r\n                                                      <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800\">\r\n                                                            <svg\r\n                                                                  className=\"w-4 h-4 mr-1 text-red-500\"\r\n                                                                  fill=\"currentColor\"\r\n                                                                  viewBox=\"0 0 20 20\"\r\n                                                                  xmlns=\"http://www.w3.org/2000/svg\"\r\n                                                            >\r\n                                                                  <path\r\n                                                                        fillRule=\"evenodd\"\r\n                                                                        d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 001.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\"\r\n                                                                        clipRule=\"evenodd\"\r\n                                                                  />\r\n                                                            </svg>\r\n                                                            Inactive\r\n                                                      </span>\r\n                                                )}\r\n                                          </td>\r\n                                          <td className=\"py-2 px-3 text-center cursor-pointer\">\r\n                                                <Button\r\n                                                      variant=\"ghost\"\r\n                                                      size=\"sm\"\r\n                                                      className=\"text-red-500 hover:text-red-900\"\r\n                                                      onClick={() => {\r\n                                                            if (confirm(\"Are you sure you want to delete this addonsGroup?\")) {\r\n                                                                  handleDelete(row)\r\n                                                            }\r\n                                                      }}\r\n                                                      style={{ alignSelf: \"flex-end\" }}\r\n                                                >\r\n                                                      <Trash size={20} />\r\n                                                </Button>\r\n                                          </td>\r\n                                          <td className=\"py-2 px-3 text-center cursor-pointer\">\r\n                                                <Pencil color='blue' size={20} onClick={() => handleEditModal(row)} />\r\n                                          </td>\r\n                                    </tr>\r\n                              )}\r\n                        />)}\r\n                  {/* <div className=\"flex items-center justify-center space-x-2 py-4 overflow-x-auto whitespace-nowrap\">\r\n                        <h2 className=\"shrink-0\">Current Page: {pageNum + 1}</h2>\r\n                        <div className=\"overflow-x-auto\">\r\n                              <ResponsivePagination\r\n                                    totalPages={Number(pageSize)}\r\n                                    currentPage={pageNum}\r\n                                    onPageChange={handlePageChange}\r\n                              />\r\n                        </div>\r\n                  </div> */}\r\n                  <AddonsGroupModal\r\n                        isOpen={addonGroupModalOpen}\r\n                        data={isAddonsGEdit ? selectedAddonsGData : undefined}\r\n                        onClose={() => setAddonGroupModalOpen(false)}\r\n                        sellerId={sellerId} header={isAddonsGEdit ? 'Edit AddonGroup' : 'Add AddonGroup'} />\r\n                  <Button className=\"fixed bottom-5 right-5 rounded-full cursor-pointer\" onClick={() => handleAddModal()}>+ Add AddonsGroup</Button>\r\n            </>\r\n      );\r\n};\r\n\r\nexport default MyAddonsGroupTab;\r\n", "import { MyAddonData } from \"~/types/api/businessConsoleService/SellerManagement\";\r\nimport { Dialog, DialogContent, DialogTitle } from \"../ui/dialog\";\r\nimport React, { useState, useEffect } from \"react\";\r\nimport { useFetcher, useSearchParams } from \"@remix-run/react\";\r\n\r\ninterface EditModalProps {\r\n      isOpen: boolean;\r\n      data: MyAddonData;\r\n      onClose: () => void;\r\n      onSave: (updatedData: MyAddonData) => void;\r\n      header: string;\r\n      sellerId: number\r\n}\r\n\r\nconst AddonsModal: React.FC<EditModalProps> = ({ isOpen, data, onClose, onSave, header, sellerId }) => {\r\n      const [formData, setFormData] = useState<MyAddonData>(data); // Ensure correct typing\r\n      const [loading, setLoading] = useState(false); // Loading state\r\n\r\n      useEffect(() => {\r\n            setFormData(data);\r\n      }, [data]);\r\n\r\n      const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {\r\n            const { name, value } = e.target;\r\n            setFormData((prevData) => ({\r\n                  ...prevData,\r\n                  [name]: name === \"active\" ? value === \"true\" : value // Convert active to boolean\r\n            }));\r\n      };\r\n      const addonFetcher = useFetcher()\r\n\r\n      const handleSave = () => {\r\n            const fetcherData = new FormData();\r\n            console.log(formData, \"kkkkkkkkkkkkkkkkk\")\r\n            fetcherData.append(\"actionType\", \"addonsAdd\");\r\n            fetcherData.append(\"sellerId\", sellerId as unknown as string);\r\n\r\n            fetcherData.append(\"addonData\", JSON.stringify(formData))\r\n            addonFetcher.submit(fetcherData, { method: \"POST\" })\r\n            onClose()\r\n      };\r\n\r\n      if (!isOpen) return null;\r\n\r\n      return (\r\n            <Dialog open={isOpen} onOpenChange={onClose}>\r\n                  <DialogContent className=\"max-w-md p-6 bg-white rounded-xl shadow-2xl sm:max-w-lg\">\r\n                        <DialogTitle className=\"text-xl font-bold text-gray-900 sm:text-2xl\">{header}</DialogTitle>\r\n\r\n                        <div className=\"mt-4 space-y-5 max-h-[60vh] overflow-y-auto pr-2 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100\">\r\n                              {/* Editable Fields */}\r\n                              <div className=\"space-y-4\">\r\n                                    {[\r\n                                          { name: \"name\", type: \"text\", label: \"Name\" },\r\n                                          { name: \"diet\", type: \"select\", label: \"Diet\" },\r\n                                    ].map((field) => (\r\n                                          <div key={field.name} className=\"flex flex-col gap-2 sm:flex-row sm:items-center\">\r\n                                                <label className=\"w-full text-sm font-medium text-gray-700 sm:w-1/3\">{field.label}</label>\r\n                                                {field.name === \"diet\" ? (\r\n                                                      <select\r\n                                                            name={field.name}\r\n                                                            value={formData?.[field.name] || \"\"}\r\n                                                            onChange={handleChange}\r\n                                                            className=\"w-full rounded-lg border-gray-300 bg-gray-50 p-2.5 text-sm text-gray-900 shadow-sm focus:border-blue-500 focus:ring-2 focus:ring-blue-500 sm:w-2/3\"\r\n                                                      >\r\n                                                            <option value=\"\">Select Diet</option>\r\n                                                            <option value=\"veg\">Veg</option>\r\n                                                            <option value=\"nonveg\">Nonveg</option>\r\n                                                            <option value=\"egg\">Egg</option>\r\n                                                            <option value=\"vegan\">vegan</option>\r\n                                                            <option value=\"na\">Na</option>\r\n\r\n                                                      </select>\r\n                                                ) : (\r\n                                                      <input\r\n                                                            type={field.type}\r\n                                                            name={field.name}\r\n                                                            value={formData?.[field.name] || \"\"}\r\n                                                            onChange={handleChange}\r\n                                                            className=\"w-full rounded-lg border-gray-300 bg-gray-50 p-2.5 text-sm text-gray-900 shadow-sm focus:border-blue-500 focus:ring-2 focus:ring-blue-500 sm:w-2/3\"\r\n                                                            placeholder={`Enter ${field.label}`}\r\n                                                      />\r\n                                                )}\r\n                                          </div>\r\n                                    ))}\r\n                              </div>\r\n\r\n                              {/* Radio Button Group */}\r\n                              <div className=\"flex flex-col gap-2 sm:flex-row sm:items-center\">\r\n                                    <label className=\"w-full text-sm font-medium text-gray-700 sm:w-1/3\">Active Status</label>\r\n                                    <div className=\"flex gap-6 w-full sm:w-2/3\">\r\n                                          <label className=\"flex items-center gap-2 text-sm text-gray-600 cursor-pointer\">\r\n                                                <input\r\n                                                      type=\"radio\"\r\n                                                      name=\"active\"\r\n                                                      value=\"true\"\r\n                                                      checked={formData?.active === true}\r\n                                                      onChange={handleChange}\r\n                                                      className=\"h-4 w-4 text-blue-600 focus:ring-blue-500\"\r\n                                                />\r\n                                                Yes\r\n                                          </label>\r\n                                          <label className=\"flex items-center gap-2 text-sm text-gray-600 cursor-pointer\">\r\n                                                <input\r\n                                                      type=\"radio\"\r\n                                                      name=\"active\"\r\n                                                      value=\"false\"\r\n                                                      checked={formData?.active === false}\r\n                                                      onChange={handleChange}\r\n                                                      className=\"h-4 w-4 text-blue-600 focus:ring-blue-500\"\r\n                                                />\r\n                                                No\r\n                                          </label>\r\n                                    </div>\r\n                              </div>\r\n                        </div>\r\n\r\n                        {/* Action Buttons */}\r\n                        <div className=\"mt-6 flex flex-col gap-3 sm:flex-row sm:justify-end\">\r\n                              <button\r\n                                    onClick={onClose}\r\n                                    className=\"w-full rounded-lg bg-gray-100 px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-300 sm:w-auto\"\r\n                              >\r\n                                    Cancel\r\n                              </button>\r\n                              <button\r\n                                    onClick={handleSave}\r\n                                    disabled={loading} // Disable button while loading\r\n                                    className={`w-full rounded-lg ${loading ? 'bg-gray-400' : 'bg-blue-600'} px-4 py-2 text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 sm:w-auto`}\r\n                              >\r\n                                    {loading ? 'Saving...' : 'Save'} {/* Show loader text */}\r\n                              </button>\r\n                        </div>\r\n                  </DialogContent>\r\n            </Dialog>\r\n      );\r\n};\r\n\r\nexport default AddonsModal;", "import React, { useState } from 'react';\r\nimport { Input } from '~/components/ui/input';\r\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '~/components/ui/select';\r\nimport ResponsivePagination from '~/components/ui/responsivePagination';\r\nimport { DeleteIcon, Pencil, Trash } from 'lucide-react';\r\nimport { Button } from '~/components/ui/button';\r\nimport { ResponsiveTable } from '../ui/responsiveTable';\r\nimport AddonsModal from './AddonsModal';\r\nimport { useFetcher } from '@remix-run/react';\r\nimport { MyAddonData } from '~/types/api/businessConsoleService/SellerManagement';\r\n\r\ninterface MyAddonsTabProps {\r\n      addonsList: MyAddonData[];\r\n      searchTerm: string;\r\n      setSearchTerm: (value: string) => void;\r\n      pageSize: string;\r\n      setPageSize: (value: string) => void;\r\n      pageNum: number;\r\n      setPageNum: (value: number) => void;\r\n      isItemModalOpen: boolean;\r\n      setIsItemModalOpen: (open: boolean) => void;\r\n      selectedItem: any;\r\n      setSelectedItem: (item: any) => void;\r\n      sellerId: number\r\n}\r\n\r\nconst myAddonsHeaders = [\r\n      \"Id\",\r\n      \"Name\",\r\n      \"Diet\",\r\n      \"Active\",\r\n      \"\",\r\n      \"\"\r\n];\r\nconst MyAddonsTab: React.FC<MyAddonsTabProps> = ({\r\n      addonsList,\r\n      searchTerm,\r\n      setSearchTerm,\r\n      pageSize,\r\n      setPageSize,\r\n      pageNum,\r\n      setPageNum,\r\n      isItemModalOpen,\r\n      setIsItemModalOpen,\r\n      selectedItem,\r\n      setSelectedItem,\r\n      sellerId\r\n}) => {\r\n      const handlePageSearch = (value: string) => {\r\n            setSearchTerm(value);\r\n      };\r\n      const handlePageSizeChange = (newPageSize: string) => {\r\n            setPageSize(newPageSize);\r\n      };\r\n      const handlePageChange = (newPageNum: number) => {\r\n            setPageNum(newPageNum);\r\n      };\r\n      const addonFetcher = useFetcher()\r\n      const handleSave = (addonsData: MyAddonData) => {\r\n            const formData = new FormData();\r\n            console.log(\"kkkkkkkkkkkkkkkkk\")\r\n            formData.append(\"actionType\", \"addonsAdd\");\r\n            formData.append(\"addonData\", JSON.stringify(addonsData))\r\n            addonFetcher.submit(formData, { method: \"POST\" })\r\n      }\r\n      const handleDelete = (addonsData: MyAddonData) => {\r\n            const formData = new FormData();\r\n            formData.append(\"actionType\", \"addonsdelete\");\r\n            formData.append(\"addonId\", addonsData.id.toString())\r\n            formData.append(\"sellerId\", sellerId.toString())\r\n            addonFetcher.submit(formData, { method: 'post' })\r\n      }\r\n\r\n      return (\r\n            <>\r\n                  <div className=\"flex justify-between mb-4\">\r\n                        <Input\r\n                              placeholder=\"Search by Item Name\"\r\n                              value={searchTerm}\r\n                              onChange={(e) => handlePageSearch(e.target.value)}\r\n                              className=\"max-w-sm rounded-full\"\r\n                        />\r\n                        <Select value={pageSize} onValueChange={handlePageSizeChange}>\r\n                              <SelectTrigger className=\"w-[180px]\">\r\n                                    <SelectValue placeholder=\"Items per page\" />\r\n                              </SelectTrigger>\r\n                              <SelectContent>\r\n                                    <SelectItem value=\"5\">5 per page</SelectItem>\r\n                                    <SelectItem value=\"10\">10 per page</SelectItem>\r\n                                    <SelectItem value=\"20\">20 per page</SelectItem>\r\n                                    <SelectItem value=\"50\">50 per page</SelectItem>\r\n                              </SelectContent>\r\n                        </Select>\r\n                  </div>\r\n                  <ResponsiveTable\r\n                        headers={myAddonsHeaders}\r\n                        data={addonsList}\r\n                        renderRow={(row) => (\r\n                              <tr key={row.id} className=\"border-b\">\r\n                                    <td className=\"py-2 px-3 text-center whitespace-normal break-words \">{row.id}</td>\r\n                                    <td className=\"py-2 px-3 text-center whitespace-normal break-words \">{row?.name}</td>\r\n                                    <td className=\"py-2 px-3 text-center whitespace-normal break-words\">{row?.diet}</td>\r\n                                    <td className=\"py-2 px-3 text-center whitespace-normal break-words\">\r\n                                          {row?.active ? (\r\n                                                <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800\">\r\n                                                      <svg\r\n                                                            className=\"w-4 h-4 mr-1 text-green-500\"\r\n                                                            fill=\"currentColor\"\r\n                                                            viewBox=\"0 0 20 20\"\r\n                                                            xmlns=\"http://www.w3.org/2000/svg\"\r\n                                                      >\r\n                                                            <path\r\n                                                                  fillRule=\"evenodd\"\r\n                                                                  d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\"\r\n                                                                  clipRule=\"evenodd\"\r\n                                                            />\r\n                                                      </svg>\r\n                                                      Active\r\n                                                </span>\r\n                                          ) : (\r\n                                                <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800\">\r\n                                                      <svg\r\n                                                            className=\"w-4 h-4 mr-1 text-red-500\"\r\n                                                            fill=\"currentColor\"\r\n                                                            viewBox=\"0 0 20 20\"\r\n                                                            xmlns=\"http://www.w3.org/2000/svg\"\r\n                                                      >\r\n                                                            <path\r\n                                                                  fillRule=\"evenodd\"\r\n                                                                  d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 001.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\"\r\n                                                                  clipRule=\"evenodd\"\r\n                                                            />\r\n                                                      </svg>\r\n                                                      Inactive\r\n                                                </span>\r\n                                          )}\r\n                                    </td>\r\n                                    <td className=\"py-2 px-3 text-center cursor-pointer\">\r\n                                          <Button\r\n                                                variant=\"ghost\"\r\n                                                size=\"sm\"\r\n                                                className=\"text-red-500 hover:text-red-900\"\r\n                                                onClick={() => {\r\n                                                      if (confirm(\"Are you sure you want to delete this area?\")) {\r\n                                                            handleDelete(row)\r\n                                                      }\r\n                                                }}\r\n                                                style={{ alignSelf: \"flex-end\" }}\r\n                                          >\r\n                                                <Trash size={20} />\r\n                                          </Button>\r\n                                    </td>\r\n                                    <td className=\"py-2 px-3 text-center cursor-pointer\">\r\n                                          <Pencil color='blue' size={20} onClick={() => {\r\n                                                setSelectedItem(row);\r\n                                                setIsItemModalOpen(true);\r\n                                          }} />\r\n                                    </td>\r\n                              </tr>\r\n                        )}\r\n                  />\r\n                  <div className=\"flex items-center justify-center space-x-2 py-4 overflow-x-auto whitespace-nowrap\">\r\n                        <h2 className=\"shrink-0\">Current Page: {pageNum + 1}</h2>\r\n                        <div className=\"overflow-x-auto\">\r\n                              <ResponsivePagination\r\n                                    totalPages={Number(pageSize)}\r\n                                    currentPage={pageNum}\r\n                                    onPageChange={handlePageChange}\r\n                              />\r\n                        </div>\r\n                  </div>\r\n                  <AddonsModal\r\n                        isOpen={isItemModalOpen}\r\n                        data={selectedItem || {}}\r\n                        onClose={() => {\r\n                              setIsItemModalOpen(false)\r\n                              setSelectedItem({});\r\n                        }}\r\n                        onSave={() => handleSave}\r\n                        sellerId={sellerId}\r\n                        header={selectedItem ? 'Edit Addons' : \"Add Addons\"}\r\n                  />\r\n                  <Button className=\"fixed bottom-5 right-5 rounded-full cursor-pointer\" onClick={() => setIsItemModalOpen(true)}>+ Add Addons</Button>\r\n            </>\r\n      );\r\n};\r\n\r\nexport default MyAddonsTab;\r\n", "import { MyVariationData } from \"~/types/api/businessConsoleService/SellerManagement\";\r\nimport { Dialog, DialogContent, DialogTitle } from \"../ui/dialog\";\r\nimport React, { useState, useEffect } from \"react\";\r\nimport { useFetcher } from \"@remix-run/react\";\r\nimport SpinnerLoader from \"../loader/SpinnerLoader\";\r\nimport { useToast } from \"../ui/ToastProvider\";\r\n\r\ninterface VariationProps {\r\n      isOpen: boolean;\r\n      data?: MyVariationData | undefined;\r\n      onClose: () => void;\r\n      header: string;\r\n      sellerId: number,\r\n      isEdit: boolean\r\n}\r\nconst AddVariation: React.FC<VariationProps> = ({ isOpen, data, onClose, header, sellerId, isEdit }) => {\r\n      const [formData, setFormData] = useState<MyVariationData>(\r\n            data\r\n            || {\r\n                  internalName: \"\",\r\n                  displayName: \"\",\r\n                  groupName: \"\",\r\n                  active: false,\r\n            }\r\n      );\r\n      useEffect(() => {\r\n            if (data && isEdit) {\r\n                  setFormData(data)\r\n            }\r\n            else {\r\n                  setFormData({\r\n                        internalName: \"\",\r\n                        displayName: \"\",\r\n                        groupName: \"\",\r\n                        active: false,\r\n                  })\r\n            }\r\n      }, [data, isOpen])\r\n\r\n\r\n      const { showToast } = useToast()\r\n      const variationFetcher = useFetcher()\r\n      useEffect(() => {\r\n            if (variationFetcher.data && variationFetcher.data?.sucess) {\r\n                  showToast(\"updatedSucessFully\", \"success\")\r\n                  onClose()\r\n                  setFormData({\r\n                        internalName: \"\",\r\n                        displayName: \"\",\r\n                        groupName: \"\",\r\n                        active: false,\r\n                  })\r\n            }\r\n            else {\r\n                  if (variationFetcher.data?.error) {\r\n                        showToast(variationFetcher.data?.error, \"error\")\r\n                        onClose()\r\n                  }\r\n            }\r\n      }, [variationFetcher.data])\r\n\r\n\r\n\r\n\r\n      const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {\r\n            const { name, value } = e.target;\r\n            setFormData((prevData) => ({\r\n                  ...prevData,\r\n                  [name]: name === \"active\" ? value === \"true\" : value,\r\n            }));\r\n      };\r\n      const handleSave = () => {\r\n            const fetcherData = new FormData();\r\n            fetcherData.append(\"actionType\", \"variationAdd\");\r\n            fetcherData.append(\"sellerId\", sellerId as unknown as string);\r\n            fetcherData.append(\"VariationData\", JSON.stringify(formData))\r\n            fetcherData.append(\"isCreating\", header === \"Add Variation\" ? \"true\" : \"false\")\r\n            variationFetcher.submit(fetcherData, { method: \"POST\" })\r\n      };\r\n      if (!isOpen) return null;\r\n\r\n      const loading = variationFetcher.state !== \"idle\";\r\n\r\n      const isFormValid = () => {\r\n            return (\r\n                  formData.internalName.trim() !== \"\" &&\r\n                  formData.displayName.trim() !== \"\" &&\r\n                  formData.groupName.trim() !== \"\"\r\n            );\r\n      };\r\n\r\n\r\n\r\n\r\n      return (\r\n            <Dialog open={isOpen} onOpenChange={onClose}>\r\n\r\n                  {loading && <SpinnerLoader loading={loading} size={20} />}\r\n                  <DialogContent className=\"max-w-md p-6 bg-white rounded-xl shadow-2xl sm:max-w-lg\">\r\n                        <DialogTitle className=\"text-xl font-bold text-gray-900 sm:text-2xl\">{header}</DialogTitle>\r\n                        <div className=\"mt-4 space-y-5 max-h-[60vh] overflow-y-auto pr-2 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100\">\r\n                              {/* Editable Fields */}\r\n                              <div className=\"space-y-4\">\r\n                                    {[\r\n                                          { id: 1, name: \"internalName\" as const, type: \"text\", label: \"InternalName\" },\r\n                                          { id: 2, name: \"displayName\" as const, type: \"text\", label: \"DisplayName\" },\r\n                                          { id: 3, name: \"groupName\" as const, type: \"text\", label: \"GroupName\" },\r\n\r\n                                    ].map((field) => (\r\n                                          <div key={field.name} className=\"flex flex-col gap-2 sm:flex-row sm:items-center\">\r\n                                                <label className=\"w-full text-sm font-medium text-gray-700 sm:w-1/3\">{field.label}</label>\r\n\r\n\r\n                                                <input\r\n                                                      type={field.type}\r\n                                                      name={field.name}\r\n                                                      value={formData?.[field.name as keyof MyVariationData] ?? \"\"} onChange={handleChange}\r\n                                                      className=\"w-full rounded-lg border-gray-300 bg-gray-50 p-2.5 text-sm text-gray-900 shadow-sm focus:border-blue-500 focus:ring-2 focus:ring-blue-500 sm:w-2/3 border\"\r\n                                                      placeholder={`Enter ${field.label}`}\r\n                                                />\r\n\r\n                                          </div>\r\n                                    ))}\r\n                              </div>\r\n\r\n                              {/* Radio Button Group */}\r\n                              <div className=\"flex flex-col gap-2 sm:flex-row sm:items-center\">\r\n                                    <label className=\"w-full text-sm font-medium text-gray-700 sm:w-1/3\">Active Status</label>\r\n                                    <div className=\"flex gap-6 w-full sm:w-2/3\">\r\n                                          <label className=\"flex items-center gap-2 text-sm text-gray-600 cursor-pointer\">\r\n                                                <input\r\n                                                      type=\"radio\"\r\n                                                      name=\"active\"\r\n                                                      value=\"true\"\r\n                                                      checked={formData?.active === true}\r\n                                                      onChange={handleChange}\r\n                                                      className=\"h-4 w-4 text-blue-600 focus:ring-blue-500\"\r\n                                                      autoFocus\r\n                                                />\r\n                                                Yes\r\n                                          </label>\r\n                                          <label className=\"flex items-center gap-2 text-sm text-gray-600 cursor-pointer\">\r\n                                                <input\r\n                                                      type=\"radio\"\r\n                                                      name=\"active\"\r\n                                                      value=\"false\"\r\n                                                      checked={formData?.active === false}\r\n                                                      onChange={handleChange}\r\n                                                      className=\"h-4 w-4 text-blue-600 focus:ring-blue-500\"\r\n                                                />\r\n                                                No\r\n                                          </label>\r\n                                    </div>\r\n                              </div>\r\n                        </div>\r\n\r\n                        {/* Action Buttons */}\r\n                        <div className=\"mt-6 flex flex-col gap-3 sm:flex-row sm:justify-end\">\r\n                              <button\r\n                                    onClick={onClose}\r\n                                    className=\"w-full rounded-lg bg-gray-100 px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-300 sm:w-auto\"\r\n                              >\r\n                                    Cancel\r\n                              </button>\r\n                              <button\r\n                                    onClick={handleSave}\r\n                                    disabled={loading || !isFormValid()}\r\n                                    className={`w-full rounded-lg px-4 py-2 text-sm font-medium text-white focus:outline-none sm:w-auto ${loading || !isFormValid()\r\n                                          ? \"bg-gray-400 cursor-not-allowed\"\r\n                                          : \"bg-blue-600 hover:bg-blue-700 focus:ring-2 focus:ring-blue-500\"\r\n                                          }`}\r\n                              >\r\n                                    {loading ? \"Saving...\" : \"Save\"}\r\n                              </button>\r\n                        </div>\r\n                  </DialogContent>\r\n            </Dialog>\r\n      );\r\n};\r\n\r\nexport default AddVariation;", "import React, { useCallback, useEffect, useState } from 'react';\r\nimport { Pencil, Trash } from 'lucide-react';\r\nimport { Button } from '~/components/ui/button';\r\nimport { ResponsiveTable } from '../ui/responsiveTable';\r\nimport AddVariation from './AddVariation';\r\nimport { MyVariationData } from '~/types/api/businessConsoleService/SellerManagement';\r\nimport { useFetcher, useNavigate } from '@remix-run/react';\r\nimport GlobalSpinnerLoader from '../loader/GlobalSpinnerLoader';\r\nimport SpinnerLoader from '../loader/SpinnerLoader';\r\nimport ResponsiveSpinnnerLoader from '../loader/ResponsiveSpinnerLoader';\r\nimport { Input } from '../ui/input';\r\nimport { useDebounce } from '~/hooks/useDebounce';\r\n\r\ninterface MyVariationTabProps {\r\n      variationData: MyVariationData[] | [];\r\n      sellerId: number\r\n}\r\n\r\nconst myVariationHeader = [\r\n      \"Id\",\r\n      \"InternalName\",\r\n      \"DisplayName\",\r\n      \"Group\",\r\n      \"Active\",\r\n      \"\",\r\n      \"\"\r\n]\r\nconst VariationTab: React.FC<MyVariationTabProps> = ({\r\n      variationData,\r\n      sellerId\r\n}) => {\r\n      const variationFetcher = useFetcher()\r\n      const [isItemModalOpen, setIsItemModalOpen] = useState(false)\r\n      const [selectedVarData, setSelectedVarData] = useState<MyVariationData>();\r\n      const [isVarEdit, setIsVarEdit] = useState(false);\r\n      const [searchTerm, setSearchTerm] = useState('');\r\n      const [filteredVariations, setFilteredVariations] = useState<MyVariationData[]>([])\r\n      const handleDelete = (variation: MyVariationData) => {\r\n            const formData = new FormData();\r\n            formData.append(\"actionType\", \"vardelete\");\r\n            formData.append(\"varId\", variation?.id?.toString() ?? \"\"); variationFetcher.submit(formData, { method: 'post' })\r\n      }\r\n      const handleEditModal = (row: MyVariationData) => {\r\n            setSelectedVarData(row);\r\n            setIsVarEdit(true)\r\n            setIsItemModalOpen(true)\r\n\r\n      }\r\n      const handleAddModal = () => {\r\n            setSelectedVarData(undefined);\r\n            setIsVarEdit(false)\r\n            setIsItemModalOpen(true)\r\n\r\n      }\r\n      const loading = variationFetcher.state !== \"idle\"\r\n      const debouncedSearchTerm = useDebounce(searchTerm, 500);\r\n\r\n\r\n\r\n\r\n\r\n      useEffect(() => {\r\n            if (debouncedSearchTerm.length >= 2 && debouncedSearchTerm !== \"\") {\r\n                  const filtered = variationData.filter((item) =>\r\n                        [item.displayName, item.internalName].some(\r\n                              (field) => field?.toLowerCase().includes(debouncedSearchTerm.toLowerCase())\r\n                        )\r\n                  )\r\n                  setFilteredVariations(filtered)\r\n            }\r\n            else {\r\n                  setFilteredVariations(variationData)\r\n            }\r\n\r\n      }, [debouncedSearchTerm, variationData]);\r\n\r\n      const navigate = useNavigate()\r\n\r\n      return (\r\n            <>\r\n                  <div className=\"flex justify-between mb-4\">\r\n                        {loading && <SpinnerLoader loading={loading} size={20} />}\r\n                        <Input\r\n                              placeholder=\"Search by Internal Name or display Name\"\r\n                              value={searchTerm}\r\n\r\n                              type='search'\r\n                              onChange={(e) => setSearchTerm(e.target.value)}\r\n                              className=\"max-w-sm mt-2 rounded-full \"\r\n                        />\r\n\r\n                        {/* <Select value={pageSize} onValueChange={handlePageSizeChange}>\r\n                              <SelectTrigger className=\"w-[180px]\">\r\n                                    <SelectValue placeholder=\"Items per page\" />\r\n                              </SelectTrigger>\r\n                              <SelectContent>\r\n                                    <SelectItem value=\"5\">5 per page</SelectItem>\r\n                                    <SelectItem value=\"10\">10 per page</SelectItem>\r\n                                    <SelectItem value=\"20\">20 per page</SelectItem>\r\n                                    <SelectItem value=\"50\">50 per page</SelectItem>\r\n                              </SelectContent>\r\n                        </Select> */}\r\n                  </div>\r\n                  <ResponsiveTable\r\n                        headers={myVariationHeader}\r\n                        data={filteredVariations}\r\n                        renderRow={(row) => (\r\n                              <tr key={row.id} className=\"border-b\">\r\n                                    <td className=\"py-2 px-3 text-center whitespace-normal break-words \">{row.id}</td>\r\n                                    <td className=\"py-2 px-3 text-center whitespace-normal break-words text-blue-300 cursor-pointer\" >{row?.internalName || \"-\"}</td>\r\n                                    <td className=\"py-2 px-3 text-center whitespace-normal break-words\">{row?.displayName || \"-\"}</td>\r\n                                    <td className=\"py-2 px-3 text-center whitespace-normal break-words\">{row?.groupName}</td>\r\n                                    <td className=\"py-2 px-3 text-center whitespace-normal break-words\">\r\n                                          {row?.active ? (\r\n                                                <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800\">\r\n                                                      <svg\r\n                                                            className=\"w-4 h-4 mr-1 text-green-500\"\r\n                                                            fill=\"currentColor\"\r\n                                                            viewBox=\"0 0 20 20\"\r\n                                                            xmlns=\"http://www.w3.org/2000/svg\"\r\n                                                      >\r\n                                                            <path\r\n                                                                  fillRule=\"evenodd\"\r\n                                                                  d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\"\r\n                                                                  clipRule=\"evenodd\"\r\n                                                            />\r\n                                                      </svg>\r\n                                                      Active\r\n                                                </span>\r\n                                          ) : (\r\n                                                <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800\">\r\n                                                      <svg\r\n                                                            className=\"w-4 h-4 mr-1 text-red-500\"\r\n                                                            fill=\"currentColor\"\r\n                                                            viewBox=\"0 0 20 20\"\r\n                                                            xmlns=\"http://www.w3.org/2000/svg\"\r\n                                                      >\r\n                                                            <path\r\n                                                                  fillRule=\"evenodd\"\r\n                                                                  d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 001.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\"\r\n                                                                  clipRule=\"evenodd\"\r\n                                                            />\r\n                                                      </svg>\r\n                                                      Inactive\r\n                                                </span>\r\n                                          )}\r\n                                    </td>\r\n                                    <td className=\"py-2 px-3 text-center cursor-pointer\">\r\n                                          <Button\r\n                                                variant=\"ghost\"\r\n                                                size=\"sm\"\r\n                                                className=\"text-red-500 hover:text-red-900\"\r\n                                                onClick={() => {\r\n                                                      if (confirm(`Are you sure you want to delete this Variation? ${row.displayName}`)) {\r\n                                                            handleDelete(row)\r\n                                                      }\r\n                                                }}\r\n                                                style={{ alignSelf: \"flex-end\" }}\r\n                                          >\r\n                                                <Trash size={20} />\r\n                                          </Button>\r\n                                    </td>\r\n                                    <td className=\"py-2 px-3 text-center cursor-pointer\">\r\n                                          <Pencil color='blue' size={20} onClick={() => handleEditModal(row)} />\r\n                                    </td>\r\n                              </tr>\r\n                        )}\r\n                  />\r\n                  {/* <div className=\"flex items-center justify-center space-x-2 py-4 overflow-x-auto whitespace-nowrap\">\r\n                        <h2 className=\"shrink-0\">Current Page: {pageNum + 1}</h2>\r\n                        <div className=\"overflow-x-auto\">\r\n                              <ResponsivePagination\r\n                                    totalPages={Number(pageSize)}\r\n                                    currentPage={pageNum}\r\n                                    onPageChange={handlePageChange}\r\n                              />\r\n                        </div>\r\n                  </div> */}\r\n                  <AddVariation\r\n                        isOpen={isItemModalOpen}\r\n                        isEdit={isVarEdit}\r\n                        data={isVarEdit ? selectedVarData : undefined}\r\n                        onClose={() => setIsItemModalOpen(false)}\r\n                        header={isVarEdit ? 'Edit Variation' : 'Add Variation'} sellerId={sellerId} />\r\n                  <Button className=\"fixed bottom-5 right-5 rounded-full cursor-pointer\" onClick={() => handleAddModal()}>+ Add Variation</Button>\r\n            </>\r\n      );\r\n};\r\nexport default VariationTab;\r\n", "\r\nimport { useState, useCallback, useEffect } from 'react'\r\nimport { json, Outlet, useFetcher, useLoaderData, useNavigate } from \"@remix-run/react\";\r\nimport { createAddon, createAddonGroup, createVariation, deleteAddon, deleteAddonGroup, deleteVariation, getAddons, getAddonsGroups, getSeItems, getSellerBusinessConfig, getsmDistrictsAndStates, getsmSellerArea, getsmSellerConfig, getVariation, updateEditItem } from \"~/services/businessConsoleService\";\r\nimport { withAuth, withResponse } from \"~/utils/auth-utils\";\r\nimport { Pencil } from \"lucide-react\";\r\nimport { Tabs, TabsContent, TabsList, TabsTrigger } from \"~/components/ui/tabs\";\r\nimport { decodePolygon } from '~/utils/polyline-utils';\r\nimport { BusinessData, MasterLocalities, MyAddonData, MyAddOnGroupAddOn, MyAddonGroupData, MyVariationData, SellerConfig, smSellerArea, StateAndDistricts, Suppliers } from '~/types/api/businessConsoleService/SellerManagement';\r\nimport { SellerUser } from '~/types/api/businessConsoleService/netWorkinfo';\r\nimport { createSellerArea, createUser, editUser, getSellerList, getSellerUser, getUserRoles, updateArea, updateAttributes, updateSellerAttributes, updateUser } from '~/services/masterItemCategories';\r\nimport { ResponsiveTable } from '~/components/ui/responsiveTable';\r\nimport CreateUser, { User } from '~/components/ui/creatUser';\r\nimport SellerConfigDetails from '~/components/ui/sellerConfigDetails';\r\nimport { Button } from '~/components/ui/button';\r\nimport { useToast } from '~/components/ui/ToastProvider';\r\nimport MapComponent from '~/components/ui/mapComponet';\r\nimport { Switch } from '~/components/ui/switch';\r\nimport BillingConfig from '~/components/ui/billingConfi';\r\nimport SpinnerLoader from '~/components/loader/SpinnerLoader';\r\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '~/components/ui/select';\r\nimport { Input } from '~/components/ui/input';\r\nimport { SellerItem } from '~/types/api/businessConsoleService/MyItemList';\r\nimport ResponsivePagination from '~/components/ui/responsivePagination';\r\nimport EditModal from '~/components/common/EditModal';\r\nimport MyAddonsGroupTab from '~/components/common/AddonsGroupTab';\r\nimport MyAddonsTab from '~/components/common/AddonsTab';\r\nimport VariationTab from '~/components/common/VariationTab';\r\nimport { useDebounce } from '~/hooks/useDebounce';\r\nimport { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '~/components/ui/tooltip';\r\nimport { apiRequest } from '~/utils/api';\r\ninterface LoaderData {\r\n  googleMapsApiKey: string\r\n  sellerId: number,\r\n  sellerName: string,\r\n  activeTab: string,\r\n  sellerAreas?: smSellerArea[],\r\n  sellerConfig?: SellerConfig,\r\n  url: string,\r\n  sellerUser: SellerUser[],\r\n  roles: { value: string; label: string }[],\r\n  sellerBId: number;\r\n  businessConfig: BusinessData,\r\n  sellerItems: SellerItem[],\r\n  statesAndDistricts: StateAndDistricts[],\r\n  userId: number;\r\n  permission: string[];\r\n  addonGroupList: MyAddonGroupData,\r\n  addonsList: MyAddonData,\r\n  variationList: MyVariationData,\r\n  currentPage: number;\r\n\r\n}\r\n\r\n\r\nexport interface ActionData {\r\n  sucess?: boolean,\r\n  error?: string\r\n}\r\n\r\n\r\nexport const getPolygonColor = (index: number) => {\r\n  const colors = ['#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6', '#ec4899', '#06b6d4', '#f97316']\r\n  return colors[index % colors.length]\r\n}\r\nexport const loader = withAuth(async ({ request, user }) => {\r\n  const googleMapsApiKey = process.env.GOOGLE_MAPS_API_KEY || ''\r\n  const url = new URL(request.url);\r\n  const sellerId = Number(url.searchParams.get(\"sellerId\"));\r\n  const sellerName = (url.searchParams.get(\"sellerName\"));\r\n  const sellerBId = Number(url.searchParams.get(\"sellerBId\"));\r\n  const userId = user.userId;\r\n  const permission = user.userDetails.roles;\r\n  const activeTab = url.searchParams.get(\"activeTab\") || 'SellerConfig';\r\n  let sellerAreas: smSellerArea[] | [] = [];\r\n  let sellerConfig: SellerConfig | {} = {};\r\n  let sellerUser: SellerUser[] | [] = []\r\n  let businessConfig: BusinessData | {} = {};\r\n  let sellerItems: SellerItem[] | [] = [];\r\n  let addonGroupList: MyAddonGroupData[] | [] = [];\r\n  let addonsList: MyAddonData[] | [] = [];\r\n  let variationList: MyVariationData[] | [] = [];\r\n  let statesAndDistricts: StateAndDistricts[] | [] = [];\r\n  const page = parseInt(url.searchParams.get(\"page\") || \"0\");\r\n  const pageSize = parseInt(url.searchParams.get(\"pageSize\") || \"50\");\r\n  const matchBy = url.searchParams.get(\"matchBy\") || \"\";\r\n  let response;\r\n  try {\r\n    switch (activeTab) {\r\n      case \"SellerAreas\":\r\n        const [areasResponse, districtsAndStatesResponse] = await Promise.all([getsmSellerArea(sellerId, request),\r\n        getsmDistrictsAndStates(user.userId, request)]);\r\n        sellerAreas = areasResponse?.data || [];\r\n        statesAndDistricts = districtsAndStatesResponse?.data || [];\r\n        break;\r\n      case \"SellerConfig\":\r\n        response = await getsmSellerConfig(sellerId, request);\r\n        sellerConfig = response?.data || {};\r\n\r\n        break;\r\n      case \"SellerUser\":\r\n        response = await getSellerUser(sellerId, request);\r\n        sellerUser = response?.data || [];\r\n        break;\r\n      case \"BillingConfig\":\r\n        response = await getSellerBusinessConfig(\"billingConfig\", sellerId, request)\r\n        businessConfig = response?.data || {}\r\n        break;\r\n      case \"SellerItems\":\r\n        response = await getSeItems(sellerId, page, pageSize, matchBy, request)\r\n        sellerItems = response?.data || []\r\n        break;\r\n      case \"MyAddonsGroup\":\r\n        response = await getAddonsGroups(sellerId, page, pageSize, matchBy, request)\r\n        addonGroupList = response?.data || []\r\n        break;\r\n      case \"MyAddons\":\r\n        response = await getAddons(sellerId, page, pageSize, matchBy, request)\r\n        addonsList = response?.data || []\r\n        break;\r\n      case \"MyVariation\":\r\n        response = await getVariation(sellerId, page, pageSize, matchBy, request)\r\n        variationList = response?.data || []\r\n        break;\r\n    }\r\n    const roleResponse = await getUserRoles(request);\r\n    const roleData = roleResponse.data as string[];\r\n    // Mapping fetched roles to the correct labels\r\n    const roleLabels: Record<string, string> = {\r\n      SellerOwner: \"Owner\",\r\n      DriverFull: \"Driver\",\r\n      AgentFull: \"Sales Agent\",\r\n      SellerManager: \"Manager\",\r\n      SellerSupervisor: \"Supervisor\",\r\n      AdvancedBuyer: \"Buyer\",\r\n      PickerFull: \"Warehouse Helper/Picker\",\r\n      ContractPriceFull: \"ContractPriceFull\",\r\n      NetworkManager: \"NetworkManager(mNET)\",\r\n      MnetManager: \"MnetManager(mNET)\",\r\n      MnetAdmin: \"MnetAdmin(mNET)\",\r\n      SalesManager: \"SalesManager(mNET)\",\r\n      MnetAgent: \"Agent(mNET)\",\r\n      WhatsappFull: \"WhatsappFull(mNET)\",\r\n      FmSalesManager: \"FmSalesManager\",\r\n      SC_Basic: \"SellerBasic\",\r\n      OC_Manager: \"OperationManager\",\r\n      AC_Basic: \"AccountManager\",\r\n      SupplierBasic: 'SupplierBasic'\r\n\r\n    };\r\n    // Transform roleData into an array of `{ value, label }` objects\r\n    const roles = roleData\r\n      .filter((role) => roleLabels[role]) // Filter only the valid roles\r\n      .map((role) => ({ value: role, label: roleLabels[role] }));\r\n    return withResponse({\r\n      googleMapsApiKey,\r\n      sellerId,\r\n      sellerName,\r\n      activeTab,\r\n      sellerAreas,\r\n      sellerConfig,\r\n      url,\r\n      sellerUser,\r\n      roles,\r\n      sellerBId,\r\n      businessConfig,\r\n      sellerItems,\r\n      statesAndDistricts,\r\n      userId,\r\n      permission,\r\n      addonGroupList,\r\n      addonsList,\r\n      variationList,\r\n      page,\r\n    }, response?.headers);\r\n  } catch (error) {\r\n    console.log(\"loader failed\");\r\n    console.error(\"Error in loader:\", error);\r\n    // Return a JSON-based error shape\r\n    return [];\r\n  }\r\n});\r\nexport const action = withAuth(async ({ request }) => {\r\n\r\n  const formData = await request.formData();\r\n  const intent = formData.get(\"intent\");\r\n  const attribute = formData.get(\"attribute\") as string;\r\n\r\n  const sellerId = Number(formData.get(\"sellerId\"));\r\n  const itemId = Number(formData.get(\"itemId\"));\r\n\r\n  const areaId = Number(formData.get(\"areaId\"));\r\n  const type = formData.get(\"updateType\") as string;\r\n  const updateValue = formData.get(\"value\");\r\n  const userId = Number(formData.get(\"userId\"));\r\n  const actionType = formData.get(\"actionType\");\r\n  if (actionType === \"addonGroupAdd\") {\r\n\r\n    const mode = formData.get(\"mode\");\r\n    const addonGId = Number(formData.get(\"AddonGId\"));\r\n    const addonGroupData = formData.get(\"addonGroupData\");\r\n    if (!addonGroupData || typeof addonGroupData !== \"string\") {\r\n      return json({ error: \"Invalid request payload\" }, { status: 400 });\r\n    }\r\n    let addonGroup;\r\n\r\n    try {\r\n      addonGroup = JSON.parse(addonGroupData);\r\n    } catch (error) {\r\n      return json({ error: \"Invalid JSON format\" }, { status: 400 });\r\n    }\r\n    const payload = {\r\n      id: addonGId,\r\n      internalName: addonGroup?.internalName,\r\n      displayName: addonGroup?.displayName,\r\n      active: addonGroup?.active,\r\n      description: addonGroup?.description\r\n\r\n    }\r\n    const finalpayload = mode === \"editMode\" ? payload : addonGroup;\r\n    try {\r\n      const response = await createAddonGroup(sellerId, finalpayload, request);\r\n      return withResponse({ sucess: response.statusCode === 200 }, response.headers);\r\n    }\r\n    catch (error) {\r\n      return new Response(\"Error while Updating SellerItem \", { status: 500 })\r\n    }\r\n  }\r\n  if (actionType === \"addonsAdd\") {\r\n    const addonData = formData.get(\"addonData\");\r\n    if (!addonData || typeof addonData !== \"string\") {\r\n      return json({ error: \"Invalid request payload\" }, { status: 400 });\r\n    }\r\n    let addons;\r\n    try {\r\n      addons = JSON.parse(addonData);\r\n\r\n      console.log(\"creating.............\")\r\n    } catch (error) {\r\n      return json({ error: \"Invalid JSON format\" }, { status: 400 });\r\n    }\r\n    try {\r\n      const response = await createAddon(sellerId, addons, request);\r\n      return withResponse({ sucess: response.statusCode === 200 ? true : false }, response.headers);\r\n    }\r\n    catch (error) {\r\n      return new Response(\"Error while Updating SellerItem \", { status: 500 })\r\n    }\r\n  }\r\n  if (actionType === \"variationAdd\") {\r\n    const varData = formData.get(\"VariationData\");\r\n    const isCreating = formData.get(\"isCreating\") as unknown as Boolean;\r\n\r\n    if (!varData || typeof varData !== \"string\") {\r\n      return json({ error: \"Invalid request payload\" }, { status: 400 });\r\n    }\r\n    let variation;\r\n    try {\r\n      variation = JSON.parse(varData);\r\n    } catch (error) {\r\n      return json({ error: \"Invalid JSON format\" }, { status: 400 });\r\n    }\r\n    try {\r\n      const response = await createVariation(sellerId, variation, request);\r\n      if (response.statusCode)\r\n        return withResponse({ sucess: response.statusCode === 200 }, response.headers);\r\n    }\r\n    catch (error) {\r\n      return new Response(\"Error while creating Variations \", { status: 500 })\r\n    }\r\n  }\r\n  if (actionType === \"addonsgroupDelete\") {\r\n    const addonGId = formData.get(\"addonGId\");\r\n    if (!addonGId || isNaN(Number(addonGId))) {\r\n      return new Response(\"Invalid or missing addonId\", { status: 400 });\r\n    }\r\n    try {\r\n      const response = await deleteAddonGroup(sellerId, Number(addonGId), request);\r\n      if (response.statusCode === 200) {\r\n        return json({ message: \"Successfully deleted\" }, { headers: response.headers });\r\n      } else if (response.statusCode === 400) {\r\n        return new Response(\"Bad request: Invalid addon ID or seller ID\", { status: 400 });\r\n      } else {\r\n        return new Response(`Unexpected response status: ${response.statusCode}`, { status: response.statusCode });\r\n      }\r\n    } catch (error) {\r\n      return new Response(`Error while deleting addon: ${error.message}`, { status: 500 });\r\n    }\r\n  }\r\n  if (actionType === \"addonsdelete\") {\r\n    const addonId = formData.get(\"addonId\");\r\n    if (!addonId || isNaN(Number(addonId))) {\r\n      return new Response(\"Invalid or missing addonId\", { status: 400 });\r\n    }\r\n    try {\r\n      const response = await deleteAddon(sellerId, Number(addonId), request);\r\n      if (response.statusCode === 200) {\r\n        return json({ message: \"Successfully deleted\" }, { headers: response.headers });\r\n      } else if (response.statusCode === 400) {\r\n        return new Response(\"Bad request: Invalid addon ID or seller ID\", { status: 400 });\r\n      } else {\r\n        return new Response(`Unexpected response status: ${response.statusCode}`, { status: response.statusCode });\r\n      }\r\n    } catch (error) {\r\n      return new Response(`Error while deleting addon: ${error.message}`, { status: 500 });\r\n    }\r\n  }\r\n  if (actionType === \"vardelete\") {\r\n    console.log(\"deleteCalled..............\")\r\n    const varId = formData.get(\"varId\");\r\n\r\n    // Validate addonId\r\n    if (!varId || isNaN(Number(varId))) {\r\n      return new Response(\"Invalid or missing varId\", { status: 400 });\r\n    }\r\n    try {\r\n      const response = await deleteVariation(sellerId, Number(varId), request);\r\n      if (response?.statusCode === 200) {\r\n        return json({ message: \"Successfully deleted\" }, { headers: response.headers });\r\n      } else if (response?.statusCode === 400) {\r\n        return new Response(\"Bad request: Invalid addon ID or seller ID\", { status: 400 });\r\n      } else {\r\n        return new Response(`Unexpected response status: ${response?.statusCode}`, { status: response.statusCode });\r\n      }\r\n    } catch (error) {\r\n      return new Response(`Error while deleting addon: ${error?.message}`, { status: 500 });\r\n    }\r\n  }\r\n  if (intent === \"updateUser\") {\r\n    const response = await updateUser(userId, request);\r\n    return withResponse({ intent, sellerUser: response.data, userId }, response.headers);\r\n  }\r\n  if (intent === \"updateArea\") {\r\n    const response = await updateArea(areaId, request);\r\n    return withResponse({ intent, sellerAreas: response.data, sellerId }, response.headers);\r\n  }\r\n  if (intent === \"updateBillingConfig\") {\r\n    const response = await updateSellerAttributes(type, sellerId, attribute, updateValue, request);\r\n    return withResponse(\r\n      {\r\n        intent: intent,\r\n        businessConfig: response.data,\r\n        sellerId: sellerId,\r\n      },\r\n      response.headers\r\n    );\r\n  }\r\n  if (intent === \"createSellerArea\") {\r\n    try {\r\n      console.log(\"📩 Received formData:\", Object.fromEntries(formData));\r\n      console.log(\"✅ Call `addNetworkLocalities`\");\r\n\r\n      const newLocalities = JSON.parse(formData.get(\"areaIds\") as string || \"[]\");\r\n\r\n      console.log(`newLocalities : `, newLocalities);\r\n\r\n      if (!Array.isArray(newLocalities) || newLocalities.length === 0) {\r\n        console.error(\"❌ Invalid request payload\");\r\n        return json({ error: \"Invalid request payload\" }, { status: 400 });\r\n      }\r\n\r\n      // Extract only `id` values from `newLocalities`\r\n      const areaIds = newLocalities.map((locality) => locality.id);\r\n\r\n      console.log(\"Extracted areaIds:\", areaIds);\r\n\r\n      // Map over the extracted IDs and create an array of promises\r\n      const apiPromises = areaIds.map(async (areaId) => {\r\n        try {\r\n          console.log(`📡 Sending API request for areaId: ${areaId}`);\r\n          return await createSellerArea(sellerId, areaId, request);\r\n        }\r\n        catch (error) {\r\n          console.error(`❌ Error in API call for areaId ${areaId}:`, error);\r\n          return { error: `Failed for areaId ${areaId}` };\r\n        }\r\n\r\n      });\r\n\r\n      // Wait for all API calls to complete\r\n      const apiResponses = await Promise.all(apiPromises);\r\n\r\n      console.log(\"✅ All API Responses:\", apiResponses);\r\n      return json({ success: true, data: apiResponses });\r\n    }\r\n    catch (error) {\r\n      console.error(`❌ Error in API call for agentUserId ${areaId}:`, error);\r\n      return json({ error: \"Failed to createArea\" }, { status: 500 });\r\n    }\r\n  }\r\n  // const userDataString = formData.get(\"userData\");\r\n  if (intent === \"editSellerItem\") {\r\n    const sellerItemData = formData.get(\"sellerItem\");\r\n    if (!sellerItemData || typeof sellerItemData !== \"string\") {\r\n      return json({ error: \"Invalid request payload\" }, { status: 400 });\r\n    }\r\n    let sellerData;\r\n    try {\r\n      sellerData = JSON.parse(sellerItemData);\r\n    } catch (error) {\r\n      return json({ error: \"Invalid JSON format\" }, { status: 400 });\r\n    }\r\n\r\n    try {\r\n      const response = await updateEditItem(sellerId, itemId, sellerData, request);\r\n      return withResponse({ intent, sellerItems: response.data, sellerId, itemId }, response.headers);\r\n\r\n    }\r\n    catch (error) {\r\n      return new Response(\"Error while Updating SellerItem \", { status: 500 })\r\n    }\r\n  }\r\n\r\n  if (intent === \"editUser\") {\r\n    const userDataString = formData.get(\"userData\");\r\n    if (!userDataString || typeof userDataString !== \"string\") {\r\n      return json({ error: \"Invalid request payload\" }, { status: 400 });\r\n    }\r\n    let userData;\r\n    try {\r\n      userData = JSON.parse(userDataString);\r\n    } catch (error) {\r\n      return json({ error: \"Invalid JSON format\" }, { status: 400 });\r\n    }\r\n    if (!Array.isArray(userData.roles) || userData.roles.length === 0) {\r\n      return json({ error: \"Roles must be an array with at least one value\" }, { status: 400 });\r\n    }\r\n    const response = await editUser(userId, userData, request);\r\n    return withResponse({ intent, sellerUser: response.data, userId }, response.headers);\r\n  }\r\n  if (intent === \"createUser\") {\r\n    const userDataString = formData.get(\"userData\");\r\n    if (!userDataString || typeof userDataString !== \"string\") {\r\n      return json({ error: \"Invalid request payload\" }, { status: 400 });\r\n    }\r\n    let userData;\r\n    try {\r\n      userData = JSON.parse(userDataString);\r\n      console.log(userData, \"User data parsed\");\r\n    } catch (error) {\r\n      return json({ error: \"Invalid JSON format\" }, { status: 400 });\r\n    }\r\n    if (!Array.isArray(userData.roles) || userData.roles.length === 0) {\r\n      return json({ error: \"Roles must be an array with at least one value\" }, { status: 400 });\r\n    }\r\n    const response = await createUser(sellerId, userData, request);\r\n    return withResponse({ intent, SellerConfig: response.data, sellerId }, response.headers);\r\n  }\r\n  if (type === \"seller\") {\r\n    // Supported attributes as expected by your backend.\r\n    const supportedAttributes = [\r\n      \"autoAccept\", \"autoPack\", \"autoPickup\", \"autoDispatch\", \"approxPricing\",\r\n      \"strikeoffEnabled\", \"itemPickEnabled\", \"contractPriceEnabled\", \"isPayLaterEnabled\",\r\n      \"waEnable\", \"autoActivate\", \"allowCoD\", \"favItemsEnabled\", \"name\", \"minimumRequiredBalance\",\r\n      \"deliveryTime\", \"approxDelDateVisibility\", \"instantDeliveryTime\", \"approxPriceVisibility\", \"minimumOrderValue\", \"categoryLevel\", \"bookingCloseTime\", \"bookingOpenTime\",\r\n      \"dispatchTime\", \"deliveryType\", \"minimumOrderQty\", \"listingSequence\", \"advanceBookingDays\", \"t1Open\",\r\n      \"t1Close\", \"t2Open\", \"t2Close\", \"t3Open\", \"t3Close\", \"menuId\", \"pos\", \"posCustId\", \"posRetName\",\r\n      \"posRetContactNo\", \"posRetAddress\", \"outletId\", \"ondcDomain\", \"defaultOrderPrepTime\", \"spId\", \"sourceSystem\", \"miSources\", \"logisticProvider\", \"distanceBasedDel\", \"deliveryDistance\",\r\n      \"packagingCharge\", \"packagingChargeType\", \"packagingApplicableOn\",\"platformFee\",\"platformFeePerc\",\"platformFeePkg\"\r\n    ];\r\n    // Retrieve the \"updates\" field from the FormData.\r\n    const updatesField = formData.get(\"updates\");\r\n    console.log(\"updatesField\", updatesField);\r\n    if (!updatesField) {\r\n      return json({ error: \"Missing updates\" }, { status: 400 });\r\n    }\r\n    // First, parse the JSON string from the updates field.\r\n    let parsedUpdates: any;\r\n    try {\r\n      parsedUpdates = JSON.parse(updatesField as string);\r\n    } catch (error) {\r\n      return json({ error: \"Invalid JSON format in updates\" }, { status: 400 });\r\n    }\r\n    console.log(\"parsedUpdates\", parsedUpdates);\r\n\r\n    // Check if we directly got an array.\r\n    if (Array.isArray(parsedUpdates)) {\r\n      // Do nothing, we already have an array.\r\n    }\r\n    // If it's an object with a property \"updates\", try to parse that.\r\n    else if (parsedUpdates && typeof parsedUpdates === \"object\" && parsedUpdates.updates) {\r\n      if (typeof parsedUpdates.updates === \"string\") {\r\n        try {\r\n          parsedUpdates = JSON.parse(parsedUpdates.updates);\r\n        } catch (error) {\r\n          return json({ error: \"Invalid JSON format in nested updates\" }, { status: 400 });\r\n        }\r\n      } else {\r\n        parsedUpdates = parsedUpdates.updates;\r\n      }\r\n    } else {\r\n      // Not an array and not an object with an \"updates\" property.\r\n      return json({ error: \"Updates must be an array\" }, { status: 400 });\r\n    }\r\n\r\n    // Final check: Ensure parsedUpdates is now an array.\r\n    if (!Array.isArray(parsedUpdates)) {\r\n      return json({ error: \"Updates must be an array\" }, { status: 400 });\r\n    }\r\n\r\n    console.log(\"Parsed updates:\", parsedUpdates);\r\n\r\n    // separate pos updates from parsedUpdates\r\n    if (parsedUpdates.find(item => item.attribute === \"pos\")?.value === \"petpooja\") {\r\n      let requestBody = {\r\n        menuId: parsedUpdates.find(item => item.attribute === \"menuId\")?.value,\r\n        posCustId: parsedUpdates.find(item => item.attribute === \"posCustId\")?.value,\r\n        posRetName: parsedUpdates.find(item => item.attribute === \"posRetName\")?.value,\r\n        posRetContactNo: parsedUpdates.find(item => item.attribute === \"posRetContactNo\")?.value,\r\n        posRetAddress: parsedUpdates.find(item => item.attribute === \"posRetAddress\")?.value,\r\n      };\r\n      parsedUpdates = parsedUpdates.filter(\r\n        item => ![\"pos\", \"menuId\", \"posCustId\", \"posRetName\", \"posRetContactNo\", \"posRetAddress\"].includes(item.attribute)\r\n      );\r\n      try {\r\n        const response = await apiRequest<SellerConfig>(\r\n          `${process.env.API_BASE_URL}/bc/mnetadmin/config/${type}/${sellerId}/attr/pos/petpooja`,\r\n          \"POST\",\r\n          requestBody,\r\n          {},\r\n          true,\r\n          request\r\n        );\r\n      }\r\n      catch (error) {\r\n        console.log(\"Error while updating POS PETPOOJA:\", error);\r\n      }\r\n    }\r\n\r\n    // separate packaging updates from parsedUpdates\r\n    if (parsedUpdates.find(item => item.attribute === \"packagingCharge\")?.value) {\r\n      let requestBody = {\r\n        packagingCharge: parsedUpdates.find(item => item.attribute === \"packagingCharge\")?.value,\r\n        packagingChargeType: parsedUpdates.find(item => item.attribute === \"packagingChargeType\")?.value,\r\n        packagingApplicableOn: parsedUpdates.find(item => item.attribute === \"packagingApplicableOn\")?.value,\r\n      };\r\n      parsedUpdates = parsedUpdates.filter(\r\n        item => ![\"packagingCharge\", \"packagingChargeType\", \"packagingApplicableOn\"].includes(item.attribute)\r\n      );\r\n      try {\r\n        const response = await apiRequest<SellerConfig>(\r\n          `${process.env.API_BASE_URL}/bc/mnetadmin/config/${type}/${sellerId}/attr/packagingCharge/packagingCharge`,\r\n          \"POST\",\r\n          requestBody,\r\n          {},\r\n          true,\r\n          request\r\n        );\r\n      }\r\n      catch (error) {\r\n        console.log(\"Error while updating packaging:\", error);\r\n      }\r\n    }\r\n\r\n    let sellerConfigUpdated: any = {};\r\n    // Process each update.\r\n    for (const { attribute, value } of parsedUpdates) {\r\n      if (!supportedAttributes.includes(attribute)) {\r\n        console.log(`Skipping unsupported attribute: ${attribute}`);\r\n        continue;\r\n      }\r\n      console.log(\"Action: Updating seller detail:\", { attribute, value });\r\n      const updatedResponse = await updateAttributes(type, sellerId, attribute, value, request);\r\n      console.log(`Action: Updated ${attribute} with value ${value}`);\r\n      sellerConfigUpdated = { ...sellerConfigUpdated, ...updatedResponse.data };\r\n    }\r\n    console.log(\"Action: All update API calls processed.\");\r\n    return withResponse({ sellerConfig: sellerConfigUpdated, sellerId }, new Headers());\r\n  }\r\n  return json({ error: \"Invalid intent or updateType\" }, { status: 400 });\r\n});\r\nexport default function SellerDetailsPage() {\r\n  const { googleMapsApiKey, sellerId, sellerName, activeTab, sellerAreas, sellerConfig, url, sellerUser, roles, sellerBId, businessConfig, sellerItems, statesAndDistricts, userId, permission, currentPage, addonGroupList, addonsList, variationList } = useLoaderData<LoaderData>()\r\n  // If no networks, show a \"No results\" row\r\n  const dataleng = sellerAreas ? sellerAreas.length : \"\"\r\n  const fetchfor = useNavigate();\r\n  const adminBasic = permission && Array.isArray(permission) ? permission.includes(\"AC_Basic\") : false;\r\n\r\n  const handleTabChange = (newTab: string) => {\r\n    if (newTab === \"SellerCategories\") {\r\n      // Navigate to the nested route for SellerCategories\r\n      fetchfor(`/home/<USER>/sellerCategories?sellerId=${sellerId}&sellerName=${sellerName}&activeTab=${newTab}&sellerBId=${sellerBId}`);\r\n    }\r\n    else if (newTab === \"DeliveryConfig\") {\r\n      fetchfor(`/home/<USER>/deliveryconfig?sellerId=${sellerId}&sellerName=${sellerName}&activeTab=${newTab}&sellerBId=${sellerBId}`)\r\n    }\r\n    else {\r\n      // For other tabs, stay on the main route\r\n      fetchfor(`/home/<USER>\n    }\r\n  };\r\n  const { showToast } = useToast()\r\n  const [visibleAreas, setVisibleAreas] = useState<Set<number>>(new Set())\r\n  const [isLoading, setIsLoading] = useState(true); // Controls loader visibility\r\n  const [isModalOpen, setIsModalOpen] = useState(false);\r\n  const [isModalOpenEdit, setIsModalOpenEdit] = useState(false);\r\n  const [map, setMap] = useState<google.maps.Map | null>(null)\r\n  const [mapLoaded, setMapLoaded] = useState(false)\r\n  const onLoad = useCallback((mapInstance: google.maps.Map) => {\r\n    setMap(mapInstance)\r\n    setMapLoaded(true)\r\n  }, [])\r\n  interface pointerLocation {\r\n    latitude: number | null;\r\n    longitude: number | null;\r\n  }\r\n  const [isLocateShopClicked, setisLocateShopClicked] = useState(false);\r\n  const [pointerLocation, setPointerLocation] = useState<pointerLocation>({\r\n    latitude: null,\r\n    longitude: null\r\n  })\r\n  const fetcher = useFetcher()\r\n  const handleLocateShopClicked = useCallback((state: boolean) => { setisLocateShopClicked(state); }, []\r\n  );\r\n\r\n  useEffect(() => {\r\n    if (map && sellerAreas) {\r\n      const decodedPolygons = sellerAreas.map((area) => ({\r\n        id: area.sellerAreaId,\r\n        paths: area.area.polygon ? decodePolygon(area.area.polygon) : [],\r\n      }));\r\n      setVisibleAreas(new Set(decodedPolygons.map((polygon) => polygon.id))); // Track which polygons to show\r\n    }\r\n  }, [map, sellerAreas]);\r\n\r\n  useEffect(() => {\r\n    // Set loading to false only when both API data and map are ready\r\n    if (googleMapsApiKey && sellerAreas) {\r\n      if (mapLoaded) {\r\n        setIsLoading(false); // Everything is ready\r\n      }\r\n    }\r\n  }, [googleMapsApiKey, sellerAreas, mapLoaded]);\r\n\r\n  const onUnmount = useCallback(() => {\r\n    setMap(null)\r\n    setMapLoaded(false)\r\n  }, [])\r\n  const getPolygonCenter = (paths: google.maps.LatLngLiteral[]) => {\r\n    const latitudes = paths.map((path) => path.lat);\r\n    const longitudes = paths.map((path) => path.lng);\r\n\r\n    const latSum = latitudes.reduce((a, b) => a + b, 0);\r\n    const lngSum = longitudes.reduce((a, b) => a + b, 0);\r\n    return {\r\n      lat: latSum / latitudes.length,\r\n      lng: lngSum / longitudes.length,\r\n    };\r\n  };\r\n  const UserHeaders = [\r\n    \"Id\",\r\n    \"Name\",\r\n    \"Business Name\",\r\n    \"Mobile Number\",\r\n    \"Cash With User\",\r\n    \"Roles\",\r\n    \"Status\",\r\n    \"\"\r\n  ];\r\n  const sellerItemHeader = [\r\n    \"Name\",\r\n    \"WtFactor\",\r\n    \"MaxAvQty\",\r\n    \"MinMax\",\r\n    \"Inc-MaxOrd\",\r\n    \"Price\",\r\n    \"Supp\",\r\n    \"Distcharg(PC-PU)\",\r\n    \"AcPkg(min-max)\",\r\n    \"DistcPkg(min-max)\",\r\n    \"\"\r\n  ];\r\n  const navigate = useNavigate()\r\n  const handleUpdate = async (data: Array<{ attribute: string; value: any }>) => {\r\n    try {\r\n      const formData = new FormData();\r\n      formData.append(\"updateType\", \"seller\");\r\n      formData.append(\"sellerId\", sellerId.toString());\r\n      // Send all updates as a single JSON string.\r\n      formData.append(\"updates\", JSON.stringify(data));\r\n      console.log(\"handleUpdate called with data:\", data);\r\n      for (const [key, value] of formData.entries()) {\r\n        console.log(key, value);\r\n      }\r\n\r\n      fetcher.submit(formData, { method: \"POST\" });\r\n      console.log(\"handleUpdate: API call submitted\");\r\n      showToast(\"Seller configuration updated successfully\", \"success\");\r\n    } catch (error) {\r\n      console.error(\"Error in handleUpdate:\", error);\r\n      showToast(\"Failed to update seller configuration\", \"error\");\r\n    }\r\n  };\r\n  const onSellerAttributeUpdate = async (attribute: string, value: any) => {\r\n    try {\r\n      const formData = new FormData();\r\n      formData.append(\"updateType\", \"billingConfig\");\r\n      formData.append(\"intent\", \"updateBillingConfig\")\r\n      formData.append(\"sellerId\", sellerId.toString());\r\n      formData.append(\"attribute\", attribute);\r\n      formData.append(\"value\", value.toString());\r\n      await fetcher.submit(formData, { method: \"POST\" });\r\n\r\n      showToast(`${attribute.replace(/([A-Z])/g, \" $1\")} updated successfully`, \"success\");\r\n      // Update UI only after successful response\r\n    } catch (error) {\r\n      showToast(`Failed to update ${attribute.replace(/([A-Z])/g, \" $1\")}`, \"error\");\r\n    }\r\n  };\r\n  const [toggleUserStatus, setToggleUserStatus] = useState<Record<number, boolean>>(() =>\r\n\r\n    sellerUser?.reduce((acc, user) => {\r\n      acc[user?.userId] = user?.disabled;\r\n      return acc;\r\n\r\n    }, {} as Record<number, boolean>)\r\n\r\n  );\r\n  const updateToggleUser = (userId: number) => {\r\n    const formData = new FormData()\r\n    formData.append(\"userId\", userId as unknown as string)\r\n    formData.append(\"intent\", \"updateUser\")\r\n    fetcher.submit(formData, { method: \"put\" })\r\n    if (fetcher.state === \"idle\") {\r\n      showToast(\"User Status Updated Success \", \"success\")\r\n    }\r\n  }\r\n  const handleSwitchUser = async (userId: number) => {\r\n    // Optimistically toggle the switch\r\n    setToggleUserStatus((prev) => ({\r\n      ...prev,\r\n      [userId]: !prev[userId],\r\n    }))\r\n\r\n    updateToggleUser(userId)\r\n  }\r\n  const updateToggle = (areaId: number) => {\r\n    const formData = new FormData()\r\n    formData.append(\"sellerId\", sellerId as unknown as string)\r\n    formData.append(\"areaId\", areaId as unknown as string)\r\n    formData.append(\"intent\", \"updateArea\")\r\n    fetcher.submit(formData, { method: \"POST\" })\r\n  }\r\n  const [user, setUser] = useState<User | undefined>()\r\n  const handleEdit = (row: any) => {\r\n    setUser(row)\r\n    setIsModalOpenEdit(true);\r\n  }\r\n  const handleCreateAreas = (areaIds: MasterLocalities[]) => {\r\n    const formData = new FormData();\r\n    formData.append(\"intent\", \"createSellerArea\");\r\n    formData.append(\"areaIds\", JSON.stringify(areaIds)); // Properly stringify the array\r\n    formData.append(\"sellerId\", sellerId.toString());\r\n\r\n    fetcher.submit(formData, { method: \"post\" });\r\n  };\r\n  const loading = fetcher.state !== \"idle\";\r\n  const [searchTerm, setSearchTerm] = useState('')\r\n  const [pageSize, setPageSize] = useState(\"50\");\r\n  const [pageNum, setPageNum] = useState(0)\r\n\r\n  const handlePageSizeChange = (newPageSize: string) => {\r\n    setPageSize(newPageSize)\r\n\r\n    navigate(`?sellerId=${sellerId}&sellerName=${sellerName}&activeTab=${activeTab}&sellerBId=${sellerBId}&page=${currentPage}&pageSize=${newPageSize}&matchBy=${searchTerm}`)\r\n  }\r\n  const debounceSearchTerm = useDebounce(searchTerm, 300);\r\n  const handlePageSearch = (value: string) => {\r\n    setSearchTerm(value);\r\n\r\n  }\r\n  useEffect(() => {\r\n    if (debounceSearchTerm.length >= 3) {\r\n      // Perform search when the input has 3 or more characters\r\n      navigate(\r\n        `?sellerId=${sellerId}&sellerName=${sellerName}&activeTab=${activeTab}&sellerBId=${sellerBId}&page=${currentPage}&pageSize=${pageSize}&matchBy=${encodeURIComponent(debounceSearchTerm)}`\r\n      );\r\n    } else {\r\n      // Reset search when input is less than 3 characters\r\n      navigate(\r\n        `?sellerId=${sellerId}&sellerName=${sellerName}&activeTab=${activeTab}&sellerBId=${sellerBId}&page=${currentPage}&pageSize=${pageSize}`\r\n      );\r\n    }\r\n\r\n  }, [debounceSearchTerm])\r\n\r\n  const handlePageChange = (newPageSize: string) => {\r\n    setPageNum(Number(newPageSize))\r\n    navigate(`?sellerId=${sellerId}&sellerName=${sellerName}&activeTab=${activeTab}&sellerBId=${sellerBId}&page=${Number(newPageSize)}&pageSize=${pageSize}&matchBy=${searchTerm}`)\r\n  }\r\n  const [selectedItem, setSelectedItem] = useState({});\r\n  const [isItemModalOpen, setIsItemModalOpen] = useState(false);\r\n  const [supplierList, setSupplierList] = useState<Suppliers[]>([]);\r\n  const handleEditModal = useCallback(async (row: SellerItem) => {\r\n    try {\r\n      const response = await fetch(`./api/supplierItems?itemId=${row.Id}`)\r\n\r\n      if (!response.ok) {\r\n        throw new Error(\"Failed to fetch item details\");\r\n      }\r\n\r\n      const supplier = await response.json();\r\n      setSupplierList(supplier?.supplierData?.data)\r\n      setSelectedItem(row);\r\n      setIsItemModalOpen(true);\r\n    } catch (error) {\r\n      console.error(\"Error fetching item:\", error);\r\n    }\r\n  }, [])\r\n  const handleSave = (updatedData: any) => {\r\n    console.log(\"Updated Data:\", updatedData);\r\n    const formData = new FormData()\r\n    formData.append(\"intent\", \"editSellerItem\");\r\n    formData.append(\"sellerItem\", JSON.stringify(updatedData));\r\n    formData.append(\"itemId\", updatedData.Id);\r\n    formData.append(\"sellerId\", sellerId.toString());\r\n    fetcher.submit(formData, { method: \"put\" })\r\n    setIsItemModalOpen(false);\r\n  };\r\n\r\n\r\n  return (\r\n    <div className=\"h-full\">\r\n      {loading && (\r\n        <SpinnerLoader loading={loading} />\r\n      )}\r\n      <h1 className=\" mb-4 font-bold cursor-pointer\" onClick={() => navigate(\"/home/<USER>\")}> <span className=\"text-2xl\">Seller Management / </span> <span className=\"text-xl\">{sellerName} </span> </h1>\r\n      <Tabs value={activeTab} onValueChange={handleTabChange}>\r\n        <TabsList>\r\n          <TabsTrigger value=\"SellerConfig\">Configurations</TabsTrigger>\r\n          <TabsTrigger value=\"SellerAreas\">Areas</TabsTrigger>\r\n          <TabsTrigger value=\"SellerUser\">Users</TabsTrigger>\r\n          <TabsTrigger value=\"SellerItems\">SellerItems</TabsTrigger>\r\n          <TabsTrigger value=\"SellerCategories\">Seller Categories</TabsTrigger>\r\n          <TabsTrigger value=\"MyAddonsGroup\">My AddonsGroups</TabsTrigger>\r\n          <TabsTrigger value=\"MyAddons\">My Addons</TabsTrigger>\r\n          <TabsTrigger value=\"MyVariation\">My Variations</TabsTrigger>\r\n          <TabsTrigger value=\"DeliveryConfig\">DeliveryConfig</TabsTrigger>\r\n          {adminBasic && <TabsTrigger value=\"BillingConfig\">Bank Details</TabsTrigger>}\r\n        </TabsList>\r\n        <Outlet />\r\n        <TabsContent value=\"MyAddonsGroup\">\r\n          <MyAddonsGroupTab\r\n            addonGroupTabData={Array.isArray(addonGroupList) ? addonGroupList : addonGroupList ? [addonGroupList] : []}\r\n            sellerId={sellerId}\r\n\r\n          />\r\n        </TabsContent>\r\n        <TabsContent value=\"MyAddons\">\r\n          <MyAddonsTab\r\n            addonsList={Array.isArray(addonsList) ? addonsList : addonsList ? [addonsList] : []}\r\n            searchTerm={searchTerm}\r\n            setSearchTerm={setSearchTerm}\r\n            pageSize={pageSize}\r\n            setPageSize={setPageSize}\r\n            pageNum={pageNum}\r\n            setPageNum={setPageNum}\r\n            isItemModalOpen={isItemModalOpen}\r\n            setIsItemModalOpen={setIsItemModalOpen}\r\n            selectedItem={selectedItem}\r\n            setSelectedItem={setSelectedItem}\r\n            sellerId={sellerId}\r\n          />\r\n        </TabsContent>\r\n        <TabsContent value=\"MyVariation\">\r\n          <VariationTab\r\n            variationData={Array.isArray(variationList) ? variationList : variationList ? [variationList] : []}\r\n            sellerId={sellerId}\r\n          />\r\n        </TabsContent>\r\n        <TabsContent value=\"SellerItems\">\r\n          <div className=\"flex justify-between mb-4\">\r\n            <Input\r\n              placeholder=\"Search by Item Name\"\r\n              value={searchTerm}\r\n              type='search'\r\n              onChange={(e) => handlePageSearch(e.target.value)}\r\n              className=\"max-w-sm  rounded-full\"\r\n            />\r\n\r\n            <Select value={pageSize} onValueChange={handlePageSizeChange}>\r\n              <SelectTrigger className=\"w-[180px]\">\r\n                <SelectValue placeholder=\"Items per page\" />\r\n              </SelectTrigger>\r\n              <SelectContent>\r\n                <SelectItem value=\"5\">5 per page</SelectItem>\r\n                <SelectItem value=\"10\">10 per page</SelectItem>\r\n                <SelectItem value=\"20\">20 per page</SelectItem>\r\n                <SelectItem value=\"50\">50 per page</SelectItem>\r\n              </SelectContent>\r\n            </Select>\r\n          </div>\r\n\r\n          <ResponsiveTable\r\n            headers={sellerItemHeader}\r\n            data={\r\n              sellerItems\r\n            }\r\n            renderRow={(row) => {\r\n              return (\r\n                <tr\r\n                  key={row.Id}\r\n                  className={`border-b transition-all duration-200 ${row.freeItem\r\n                    ? 'bg-[linear-gradient(88deg,#F0FFF0_0%,#FFF_100%)]'\r\n                    : 'hover:bg-gray-100 hover:shadow-sm'\r\n                    } rounded-lg`}\r\n                >\r\n                  <td\r\n                    className=\"py-2 cursor-pointer \"\r\n                    onClick={() =>\r\n                      navigate(`/home/<USER>\n                    }\r\n                  >\r\n                    <div className=\"flex items-center gap-1 justify-start p-1 rounded-lg bg-white/50 shadow-sm hover:shadow-md transition-shadow duration-150 w-fit\">\r\n                      <img\r\n                        src={row?.picture}\r\n                        alt={row?.name}\r\n                        className=\"h-12 w-12 rounded-full object-cover flex-shrink-0 border border-gray-200\"\r\n                      />\r\n                      <div className=\"flex flex-col items-start text-left\">\r\n                        <span className=\"text-sm font-medium text-blue-600 hover:underline max-w-32 break-words\">\r\n                          {row?.name} {row?.unit}\r\n                        </span>\r\n                        {row?.freeItem && (\r\n                          <span className=\"text-xs font-semibold text-teal-500 bg-teal-100 px-2 py-1 rounded-full mt-1\">\r\n                            Free\r\n                          </span>\r\n                        )}\r\n                      </div>\r\n                    </div>\r\n\r\n                    {row.description && (\r\n                      <TooltipProvider>\r\n                        <Tooltip>\r\n                          <TooltipTrigger asChild>\r\n                            <div className=\"mt-2 flex justify-center\">\r\n                              <span className=\"text-xs font-semibold text-orange-500 hover:text-orange-600 cursor-pointer underline\">\r\n                                View Description\r\n                              </span>\r\n                            </div>\r\n                          </TooltipTrigger>\r\n                          <TooltipContent className=\"bg-gray-800 text-white p-3 rounded-md max-w-xs\">\r\n                            <p className=\"text-sm\">Description: {row?.description || \"-\"}</p>\r\n                          </TooltipContent>\r\n                        </Tooltip>\r\n                      </TooltipProvider>\r\n                    )}\r\n                  </td>\r\n                  <td className=\"py-2 px-2 text-center  whitespace-normal break-words\"\r\n                  >\r\n                    {row?.weightFactor}\r\n                  </td>\r\n                  <td className=\"py-2 px-2 text-center  whitespace-normal break-words\"\r\n                  >\r\n                    {row?.maxAvailableQty}\r\n                  </td>\r\n                  <td className=\"py-2 px-2 text-center  whitespace-normal break-words\"\r\n                  >\r\n                    {row?.minOrderQty}-{row?.maxOrderQty}\r\n                  </td>\r\n                  <td className=\"py-2 px-3 text-center  whitespace-normal break-words\"\r\n                  >\r\n                    {row?.maxOrderQty}-{row?.incrementOrderQty}\r\n                  </td>\r\n\r\n                  <td className=\"py-2 px-3 text-center  whitespace-normal break-words\"\r\n                  >\r\n                    {row?.pricePerUnit}\r\n                  </td>\r\n                  <td className=\"py-2 px-3 text-center  whitespace-normal break-words\"\r\n                  >\r\n                    {row?.supplierName}\r\n                  </td>\r\n                  <td className=\"py-2 px-3 text-center  whitespace-normal break-words\"\r\n                  >\r\n                    {row?.distChargePc}-{row?.distChargePu}\r\n                  </td>\r\n                  <td className=\"py-2 px-3 text-center  whitespace-normal break-words\"\r\n                  >\r\n                    {row?.minAcPkg}-{row?.maxAcPkg}\r\n                  </td>\r\n                  <td className=\"py-2 px-3 text-center  whitespace-normal break-words\"\r\n                  >\r\n                    {row?.minDistcPkg}-{row?.maxDistcPkg}\r\n                  </td>\r\n                  <td className=\"py-2 px-3 text-center cursor-pointer\">\r\n                    <Pencil onClick={() => handleEditModal(row)} />\r\n                  </td>\r\n                </tr>\r\n              )\r\n            }}\r\n          />\r\n          <div className=\"flex items-center justify-center space-x-2 py-4 overflow-x-auto whitespace-nowrap\">\r\n            <h2 className=\"shrink-0\">Current Page: {pageNum + 1}</h2>\r\n            <div className=\"overflow-x-auto\">\r\n              <ResponsivePagination\r\n                totalPages={Number(pageSize)}\r\n                currentPage={pageNum}\r\n                onPageChange={(pageNum) => handlePageChange(pageNum.toString())}\r\n              />\r\n            </div>\r\n          </div>\r\n          {/* edit modal flexible for both create and edit seller item*/}\r\n          <EditModal\r\n\r\n            isOpen={isItemModalOpen}\r\n            data={selectedItem || {}}\r\n            onClose={() => {\r\n              setIsItemModalOpen(false)\r\n              setSelectedItem({});\r\n            }}\r\n            onSave={handleSave} multiple={false} supplierList={supplierList} />\r\n          {/* <Button className=\"fixed bottom-5 right-5 rounded-full cursor-pointer\" onClick={() => setIsModalOpen(true)}>+ Add SellerItem</Button> */}\r\n        </TabsContent>\r\n        <TabsContent value=\"SellerUser\">\r\n          <ResponsiveTable\r\n            headers={UserHeaders}\r\n            data={\r\n              sellerUser\r\n            }\r\n            renderRow={(row) => {\r\n              return (\r\n                <tr key={row.userId} className=\"border-b\">\r\n                  <td className=\"py-2 px-3 text-center  whitespace-normal break-words\"\r\n                  >{row.userId}</td>\r\n                  <td className=\"py-2 px-3 text-center whitespace-normal break-words\"\r\n                  >\r\n                    {row?.userName}\r\n                  </td>\r\n                  <td className=\"py-2 px-3 text-center  whitespace-normal break-words\"\r\n                  >\r\n                    {row?.businessName}\r\n                  </td>\r\n                  <td className=\"py-2 px-3 text-center whitespace-normal break-words\"\r\n                  >\r\n                    {row?.mobileNumber}\r\n                  </td>\r\n                  <td className=\"py-2 px-3 text-center  whitespace-normal break-words\"\r\n                  >\r\n                    {row?.cashWithUser}\r\n                  </td>\r\n                  <td className=\"py-2 px-3 text-center  whitespace-normal break-words\"\r\n                  >\r\n                    {row?.roles?.map(x => <div> {x === \"SellerOwner\" ? \"Owner\" : x === \"DriverFull\" ? \"Driver\" : x === \"SupplierBasic\" ? \"SupplierBasic\" : x === \"AgentFull\" ? \"Agent\" : x === \"SellerManager\" ? \"Manager\" : x === \"SellerSupervisor\" ?\r\n                      \"Supervisor\" : x === \"PickerFull\" ? \" Warehouse Helper/ Picker\" : x === \"AdvancedBuyer\" ? \"Buyer\" :\r\n                        x === \"ContractPriceFull\" ? \"ContractPrice\" : x === \"NetworkManager\" ? \"NetworkManager(mNET)\" : x === \"MnetManager\" ? \"MnetManager(mNET)\" : x === \"MnetAdmin\" ? \"MnetAdmin(mNET)\" : x === \"SalesManager\" ? \"SalesManager(mNET)\" : x === \"WhatsappFull\" ? \"WhatsappFull(mNET)\" : x === \"MnetAgent\" ? \"Agent(mNET)\" :\r\n                          x === \"SC_Basic\" ? \"SellerBasic\" : x === \"OC_Manager\" ? \"OperationManager\" : x === \"AC_Basic\" ? \"AccountManager\" : x === \"FmSalesManager\" ? \"FmSalesManager\" : \"\"}</div>)}\r\n\r\n                  </td>\r\n                  <td className=\"py-2 px-3 text-center  whitespace-normal break-words\"\r\n                  >\r\n                    <Switch\r\n                      checked={!toggleUserStatus[row.userId]}\r\n                      onClick={() => handleSwitchUser(row.userId)}\r\n\r\n                    />\r\n                  </td>\r\n                  <td className=\"py-2 px-3 text-center\">\r\n                    <Pencil onClick={() => handleEdit(row)} />\r\n                  </td>\r\n                </tr>\r\n              )\r\n            }}\r\n          />\r\n          <CreateUser isOpen={isModalOpenEdit} onClose={() => setIsModalOpenEdit(false)} sellerId={sellerId}\r\n            roles={roles ? roles : []} sellerBId={sellerBId} user={user}\r\n          />\r\n          <CreateUser isOpen={isModalOpen} onClose={() => setIsModalOpen(false)} sellerId={sellerId}\r\n            roles={roles ? roles : []} sellerBId={sellerBId}\r\n          />\r\n          <Button className=\"fixed bottom-5 right-5 rounded-full cursor-pointer\" onClick={() => setIsModalOpen(true)}>+ Add User</Button>\r\n\r\n        </TabsContent>\r\n        <TabsContent value={\"BillingConfig\"}>\r\n          <BillingConfig billingConfig={businessConfig} onSellerAttributeUpdate={onSellerAttributeUpdate} />\r\n\r\n        </TabsContent>\r\n      </Tabs>\r\n      {activeTab === \"SellerConfig\" && (sellerConfig\r\n        ? (<SellerConfigDetails sellerConfig={sellerConfig} onAttributeUpdate={handleUpdate} />\r\n        ) : (<div className=\"flex items-center justify-center h-full\">\r\n          <p className=\"text-red-500\">Unable to find network configurations.</p>\r\n        </div>))}\r\n      {activeTab === \"SellerAreas\" && (sellerAreas ? (\r\n        <MapComponent googleMapsApiKey={googleMapsApiKey} sellerAreas={sellerAreas} visibleAreas={visibleAreas}\r\n          decodePolygon={decodePolygon}\r\n          getPolygonCenter={getPolygonCenter}\r\n          getPolygonColor={getPolygonColor}\r\n          handleLocateShopClicked={handleLocateShopClicked}\r\n          updateToggle={updateToggle}\r\n          onLoad={onLoad} onUnmount={onUnmount} isLocateShopClicked={isLocateShopClicked}\r\n          statesAndDistricts={statesAndDistricts}\r\n          userId={userId}\r\n          sellerId={sellerId}\r\n          handleSubmit={(areaIds: MasterLocalities[]) => handleCreateAreas(areaIds)}\r\n        />\r\n      )\r\n        : (<div className=\"flex items-center justify-center h-full\">\r\n          <p className=\"text-red-500\">Unable to find sellerAreas .</p>\r\n        </div>)\r\n      )\r\n      }\r\n    </div >\r\n  );\r\n\r\n}\r\n"], "names": ["React.useState", "jsxs", "jsx", "_a", "useState", "useEffect", "error", "Fragment", "Edit", "label", "getPolygonColor", "decodePolygon", "useCallback", "showMarker", "userId", "useRef", "index", "colors", "length", "SellerDetailsPage", "googleMapsApiKey", "sellerId", "sellerName", "activeTab", "sellerAreas", "sellerConfig", "url", "sellerUser", "roles", "sellerBId", "businessConfig", "sellerItems", "statesAndDistricts", "permission", "currentPage", "addonGroupList", "addonsList", "variationList", "useLoaderData", "fetchfor", "useNavigate", "adminBasic", "Array", "isArray", "includes", "handleTabChange", "newTab", "showToast", "useToast", "visible<PERSON><PERSON><PERSON>", "setVisibleAreas", "Set", "isLoading", "setIsLoading", "isModalOpen", "setIsModalOpen", "isModalOpenEdit", "setIsModalOpenEdit", "map", "setMap", "mapLoaded", "setMapLoaded", "onLoad", "mapInstance", "isLocateShopClicked", "setisLocateShopClicked", "pointerLocation", "setPointerLocation", "latitude", "longitude", "fetcher", "useFetcher", "handleLocateShopClicked", "state", "decodedPolygons", "area", "id", "sellerAreaId", "paths", "polygon", "onUnmount", "getPolygonCenter", "latitudes", "path", "lat", "longitudes", "lng", "latSum", "reduce", "a", "b", "lngSum", "UserHeaders", "sellerItemHeader", "navigate", "handleUpdate", "data", "formData", "FormData", "append", "toString", "JSON", "stringify", "console", "log", "key", "value", "entries", "submit", "method", "onSellerAttributeUpdate", "attribute", "replace", "toggleUserStatus", "setToggleUserStatus", "acc", "user", "disabled", "updateToggleUser", "handleSwitchUser", "prev", "updateToggle", "areaId", "setUser", "handleEdit", "row", "handleCreateAreas", "areaIds", "loading", "searchTerm", "setSearchTerm", "pageSize", "setPageSize", "pageNum", "setPageNum", "handlePageSizeChange", "newPageSize", "debounceSearchTerm", "useDebounce", "handlePageSearch", "encodeURIComponent", "handlePageChange", "Number", "selectedItem", "setSelectedItem", "isItemModalOpen", "setIsItemModalOpen", "supplierList", "setSupplierList", "handleEditModal", "response", "fetch", "Id", "ok", "Error", "supplier", "json", "supplierData", "handleSave", "updatedData", "className", "children", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "onClick", "Tabs", "onValueChange", "TabsList", "TabsTrigger", "Outlet", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "MyAddonsGroupTab", "addonGroupTabData", "MyAddonsTab", "VariationTab", "variationData", "Input", "placeholder", "type", "onChange", "e", "target", "Select", "SelectTrigger", "SelectValue", "SelectContent", "SelectItem", "ResponsiveTable", "headers", "renderRow", "freeItem", "name", "src", "picture", "alt", "unit", "description", "TooltipProvider", "<PERSON><PERSON><PERSON>", "TooltipTrigger", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "weightFactor", "maxAvailableQty", "minOrder<PERSON>ty", "max<PERSON><PERSON><PERSON><PERSON><PERSON>", "incrementOrderQty", "pricePerUnit", "supplierName", "distChargePc", "distChargePu", "minAcPkg", "maxAcPkg", "minDistcPkg", "maxDistcPkg", "Pencil", "ResponsivePagination", "totalPages", "onPageChange", "EditModal", "isOpen", "onClose", "onSave", "multiple", "userName", "businessName", "mobileNumber", "cashWithUser", "x", "Switch", "checked", "CreateUser", "<PERSON><PERSON>", "BillingConfig", "billingConfig", "SellerConfigDetails", "onAttributeUpdate", "MapComponent", "handleSubmit"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAaA,MAAM,cAA0C,CAAC;AAAA,EAC3C;AAAA,EACA;AAAA,EACA;AAAA,EACA,cAAc;AAAA,EACd;AAAA,EACA;AACN,MAAM;;AACA,QAAM,CAAC,QAAQ,SAAS,IAAIA,aAAAA,SAAe,KAAK;AAE1C,QAAA,cAAc,CAAC,UAAkB;AACjC;AAAA,OACM,iDAAgB,SAAS,UACjB,eAAe,OAAO,CAAC,MAAM,MAAM,KAAK,IACxC,CAAC,GAAG,gBAAgB,KAAK;AAAA,IACvC;AAAA,EACN;AAGM,SAAAC,kCAAA,KAAC,OAAI,EAAA,WAAU,mBAET,UAAA;AAAA,IAAAA,kCAAA;AAAA,MAAC;AAAA,MAAA;AAAA,QACK,MAAK;AAAA,QACL,SAAS,MAAM,UAAU,CAAC,MAAM;AAAA,QAChC,WAAW;AAAA,UACL;AAAA,UACA;AAAA,QACN;AAAA,QAEA,UAAA;AAAA,UAACC,kCAAAA,IAAA,QAAA,EAAK,WAAU,YACT,WAAgB,iDAAA,UAAS,IAClB,IAAG,wCAAS,KAAK,CAAC,SAAQ,2BAAK,WAAU,eAAe,CAAC,OAAtD,mBAA0D,KAAK,MAAK,wCAAS,KAAK,CAAC,SAAQ,2BAAK,WAAU,eAAe,CAAC,OAAtD,mBAA0D,KACnI,MAAK,iDAAgB,UAAS,CAAC,UAC7B,eACK,IAAI,CAAC;;AAAU,oBAAAC,MAAA,mCAAS,KAAK,CAAC,SAAQ,2BAAK,WAAU,WAAtC,gBAAAA,IAA8C;AAAA,WAAK,EAClE,KAAK,IAAI,KAAK,YACjC,CAAA;AAAA,UACAD,kCAAAA,IAAC,aAAY,EAAA,WAAU,qBAAqB,CAAA;AAAA,QAAA;AAAA,MAAA;AAAA,IAClD;AAAA,IAGC,UACjBA,kCAAA;AAAA,MAAC;AAAA,MAAA;AAAA,QACC,WAAW;AAAA,UACT;AAAA,UACA;AAAA,QACF;AAAA,QACA,SAAS,CAAK,MAAA,EAAE,gBAAgB;AAAA,QAChC,aAAa,CAAK,MAAA,EAAE,gBAAgB;AAAA,QAEnC,UAAA,mCAAS,IAAI,CAAC,WACbD,kCAAA;AAAA,UAAC;AAAA,UAAA;AAAA,YAEC,WAAW;AAAA,cACT;AAAA,eACA,iDAAgB,SAAS,iCAAQ,WAAU;AAAA,YAC7C;AAAA,YACA,SAAS,MAAM,YAAY,OAAO,KAAK;AAAA,YAEtC,UAAA;AAAA,eAAA,iDAAgB,SAAS,OAAO,WAC9BC,kCAAA,IAAA,OAAA,EAAM,WAAU,6BAA4B;AAAA,cAE9C,iCAAQ;AAAA,YAAA;AAAA,UAAA;AAAA,UAVJ,iCAAQ;AAAA,QAYhB;AAAA,MAAA;AAAA,IAAA;AAAA,EACH,GAEU;AAEZ;AC7CA,SAAwB,WAAW;AAAA,EAC7B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACN,GAAoB;AACd,QAAM,UAAU,WAAW;AACrB,QAAA,EAAE,UAAU,IAAI,SAAS;AAG/B,QAAM,CAAC,UAAU,WAAW,IAAIE,sBAAS;AAAA,IACnC,WAAW;AAAA,IACX,UAAU;AAAA,IACV,OAAO;AAAA,IACP,cAAc;AAAA,IACd,SAAS;AAAA,IACT,UAAU;AAAA,IACV,YAAY;AAAA;AAAA,IACZ,OAAO,CAAA;AAAA,EAAC,CACb;AAGD,QAAM,CAAC,eAAe,gBAAgB,IAAIA,aAAAA,SAAmB,CAAA,CAAE;AAG/DC,eAAAA,UAAU,MAAM;AACV,QAAI,MAAM;AACI,cAAA,IAAI,MAAM,qBAAqB;AAC3B,kBAAA;AAAA,QACN,WAAW,6BAAM;AAAA,QACjB,UAAU,6BAAM;AAAA,QAChB,OAAO,6BAAM;AAAA,QACb,cAAc,6BAAM;AAAA,QACpB,SAAS,6BAAM;AAAA,QACf,UAAU;AAAA;AAAA,QACV,YAAY,6BAAM;AAAA;AAAA,QAClB,OAAO,6BAAM;AAAA,MAAA,CAClB;AACD,uBAAiB,KAAK,KAAK;AAAA,IAAA;AAAA,EACjC,GACH,CAAC,IAAI,CAAC;AAEH,QAAA,eAAe,CAAC,MAA2C;AAC3D,UAAM,EAAE,MAAM,MAAM,IAAI,EAAE;AACd,gBAAA,CAAC,UAAU,EAAE,GAAG,MAAM,CAAC,IAAI,GAAG,MAAA,EAAQ;AAAA,EACxD;AAGM,QAAA,eAAe,OAAO,MAAuB;AAC7C,MAAE,eAAe;AAEjB,UAAM,WAAW;AAAA,MACX,GAAG;AAAA,MACH,OAAO;AAAA,MACP,YAAY;AAAA;AAAA,IAClB;AAEM,UAAA,iBAAiB,IAAI,SAAS;AAC5B,YAAA,IAAI,WAAW,UAAU,oBAAoB;AACtC,mBAAA,OAAO,YAAY,QAA6B;AAChD,mBAAA,OAAO,UAAU,6BAAM,MAA2B;AAEjE,mBAAe,OAAO,UAAU,OAAO,aAAa,YAAY;AAChE,mBAAe,OAAO,YAAY,KAAK,UAAU,QAAQ,CAAC;AAE1D,YAAQ,OAAO,gBAAgB;AAAA,MACzB,QAAQ;AAAA,MACR,SAAS;AAAA,IAAA,CACd;AAAA,EAIP;AAEM,QAAA,YAAY,QAAQ,UAAU;AAEpCA,eAAAA,UAAU,MAAM;AAEN,QAAA,QAAQ,QAAQ,WAAW,MAAM;AAC/B,UAAI,QAAQ,MAAM;AACF,kBAAA,OAAO,8BAA8B,6BAA6B,SAAS;AAC7E,gBAAA;AACI,oBAAA;AAAA,UACN,WAAW;AAAA,UACX,UAAU;AAAA,UACV,OAAO;AAAA,UACP,cAAc;AAAA,UACd,SAAS;AAAA,UACT,UAAU;AAAA,UACV,YAAY;AAAA;AAAA,UACZ,OAAO,CAAA;AAAA,QAAC,CACb;AAAA,MAAA,OAEF;AACW,kBAAA,OAAO,wBAAwB,yBAAyB,OAAO;AAAA,MAAA;AAAA,IAE/E;AAAA,EACN,GACH,CAAC,QAAQ,IAAI,CAAC;AAIX,SAAAH,kCAAA,IAAC,UAAO,MAAM,QAAQ,cAAc,SAC9B,UAAAD,kCAAA,KAAC,eAAc,EAAA,WAAU,gCAClB,UAAA;AAAA,IACK,aAAAC,kCAAAA,IAAC,SAAI,WAAU,kFACT,gDAAC,eAAc,EAAA,SAAS,WAAW,EACzC,CAAA;AAAA,0CAGL,aAAY,EAAA,WAAU,aAChB,UAAA,OAAO,cAAc,gBAC5B;AAAA,IACCD,kCAAA,KAAA,QAAA,EAAK,UAAU,cAAc,WAAU,cAClC,UAAA;AAAA,MAACA,kCAAAA,KAAA,OAAA,EAAI,WAAU,mCACT,UAAA;AAAA,QAACA,kCAAAA,KAAA,OAAA,EAAM,WAAU,8CAA6C,UAAA;AAAA,UAAA;AAAA,UAExDC,kCAAA;AAAA,YAAC;AAAA,YAAA;AAAA,cACK,MAAK;AAAA,cACL,aAAY;AAAA,cACZ,UAAU;AAAA,cACV,OAAO,SAAS;AAAA,cAChB,UAAQ;AAAA,YAAA;AAAA,UAAA;AAAA,QACd,GACN;AAAA,QACAD,kCAAAA,KAAC,OAAM,EAAA,WAAU,gCAA+B,UAAA;AAAA,UAAA;AAAA,UAE1CC,kCAAA;AAAA,YAAC;AAAA,YAAA;AAAA,cACK,MAAK;AAAA,cACL,aAAY;AAAA,cACZ,UAAU;AAAA,cACV,OAAO,SAAS;AAAA,cAChB,UAAQ;AAAA,YAAA;AAAA,UAAA;AAAA,QACd,EACN,CAAA;AAAA,MAAA,GACN;AAAA,MAEAD,kCAAAA,KAAC,OAAI,EAAA,WAAU,mCACR,UAAA;AAAA,QAAA,CAAC,QAAQA,kCAAAA,KAAC,OAAM,EAAA,WAAU,gCAA+B,UAAA;AAAA,UAAA;AAAA,UAEpDC,kCAAA;AAAA,YAAC;AAAA,YAAA;AAAA,cACK,MAAK;AAAA,cACL,MAAK;AAAA,cACL,aAAY;AAAA,cACZ,UAAU;AAAA,cACV,OAAO,SAAS;AAAA,cAChB,UAAQ;AAAA,YAAA;AAAA,UAAA;AAAA,QACd,GACN;AAAA,QACAD,kCAAAA,KAAC,OAAM,EAAA,WAAU,gCAA+B,UAAA;AAAA,UAAA;AAAA,UAE1CC,kCAAA;AAAA,YAAC;AAAA,YAAA;AAAA,cACK,MAAK;AAAA,cACL,MAAK;AAAA,cACL,aAAY;AAAA,cACZ,UAAU,CAAC,MAAM;AACX,sBAAM,QAAQ,EAAE,OAAO,MAAM,QAAQ,OAAO,EAAE;AAC1C,oBAAA,MAAM,UAAU,IAAI;AAClB,8BAAY,CAAC,UAAU;AAAA,oBACjB,GAAG;AAAA,oBACH,cAAc;AAAA,kBAAA,EAGlB;AAAA,gBAAA;AAAA,cAGd;AAAA,cACA,OAAO,SAAS;AAAA,cAChB,UAAQ;AAAA,YAAA;AAAA,UAAA;AAAA,QACd,EACN,CAAA;AAAA,MAAA,GACN;AAAA,MAEAD,kCAAAA,KAAC,OAAI,EAAA,WAAU,mCACR,UAAA;AAAA,QAAA,CAAC,QAAQA,kCAAAA,KAAC,OAAM,EAAA,WAAU,gCAA+B,UAAA;AAAA,UAAA;AAAA,UAEpDC,kCAAA;AAAA,YAAC;AAAA,YAAA;AAAA,cACK,MAAK;AAAA,cACL,aAAY;AAAA,cACZ,UAAU;AAAA,cACV,OAAO,SAAS;AAAA,cAChB,UAAQ;AAAA,YAAA;AAAA,UAAA;AAAA,QACd,GACN;AAAA,QACAD,kCAAAA,KAAC,OAAM,EAAA,WAAU,gCAA+B,UAAA;AAAA,UAAA;AAAA,UAE1CC,kCAAA;AAAA,YAAC;AAAA,YAAA;AAAA,cACK,SAAS;AAAA,cACT,gBAAgB,gBAAgB,gBAAgB,CAAC;AAAA,cACjD,UAAU;AAAA,cACV,aAAY;AAAA,cACZ,WAAU;AAAA,cACV,QAAQ;AAAA,YAAA;AAAA,UAAA;AAAA,QACd,EACN,CAAA;AAAA,MAAA,GACN;AAAA,MAECA,kCAAA,IAAA,OAAA,EAAI,WAAU,oBACT,UAACA,kCAAA,IAAA,QAAA,EAAO,MAAK,UAAS,WAAU,gBACzB,UAAO,OAAA,gBAAgB,aAC9B,EACN,CAAA;AAAA,IAAA,EACN,CAAA;AAAA,EAAA,EAAA,CACN,EACN,CAAA;AAEZ;ACzOA,SAAwB,oBAAoB,EAAE,cAAc,qBAAkC;AACxF,QAAM,CAAC,QAAQ,SAAS,IAAIE,aAAAA,SAAuB,YAAY;AAC/D,QAAM,CAAC,OAAO,QAAQ,IAAIA,aAAAA,SAAwB,IAAI;AAGtD,QAAM,CAAC,oBAAoB,qBAAqB,IAAIA,aAAAA,SAAS,KAAK;AAClE,QAAM,CAAC,kBAAkB,mBAAmB,IAAIA,aAAAA,SAAgC,CAAA,CAAE;AAElF,QAAM,CAAC,mBAAmB,oBAAoB,IAAIA,aAAAA,SAAS,KAAK;AAChE,QAAM,CAAC,iBAAiB,kBAAkB,IAAIA,aAAAA,SAAgC,CAAA,CAAE;AAEhF,QAAM,CAAC,mBAAmB,oBAAoB,IAAIA,aAAAA,SAAS,KAAK;AAChE,QAAM,CAAC,iBAAiB,kBAAkB,IAAIA,aAAAA,SAAgC,CAAA,CAAE;AAEhF,QAAM,CAAC,oBAAoB,qBAAqB,IAAIA,aAAAA,SAAS,KAAK;AAClE,QAAM,CAAC,kBAAkB,mBAAmB,IAAIA,aAAAA,SAAgC,CAAA,CAAE;AAElF,QAAM,CAAC,oBAAoB,qBAAqB,IAAIA,aAAAA,SAAS,KAAK;AAClE,QAAM,CAAC,kBAAkB,mBAAmB,IAAIA,aAAAA,SAAgC,CAAA,CAAE;AAElFC,eAAAA,UAAU,MAAM;AACV,QAAI,cAAc;AACZ,gBAAU,YAAY;AAAA,IAAA;AAAA,EAC5B,GACH,CAAC,YAAY,CAAC;AAGjB,QAAM,aAAiD;AAAA,IACjD,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,SAAS;AAAA,IACT,aAAa;AAAA,IACb,WAAW;AAAA,IACX,aAAa;AAAA,IACb,eAAe;AAAA,IACf,aAAa;AAAA,IACb,0BAA0B;AAAA,IAC1B,WAAW;AAAA,IACX,mBAAmB;AAAA,IACnB,qBAAqB;AAAA,IACrB,WAAW;AAAA,IACX,gBAAgB;AAAA,IAChB,uBAAuB;AAAA,IACvB,mBAAmB;AAAA,IACnB,mBAAmB;AAAA,IACnB,wBAAwB;AAAA,IACxB,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,gBAAgB;AAAA,IAChB,aAAa;AAAA,IACb,eAAe;AAAA,IACf,eAAe;AAAA,IACf,yBAAyB;AAAA,IACzB,qBAAqB;AAAA,IACrB,oBAAoB;AAAA,IACpB,mBAAmB;AAAA,IACnB,sBAAsB;AAAA,IACtB,kBAAkB;AAAA,IAClB,kBAAkB;AAAA,IAClB,eAAe;AAAA,IACf,sBAAsB;AAAA,IACtB,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,QAAQ;AAAA,IACR,KAAK;AAAA,IACL,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,iBAAiB;AAAA,IACjB,eAAe;AAAA,IACf,UAAU;AAAA,IACV,YAAY;AAAA,IACZ,sBAAsB;AAAA,IACtB,UAAU;AAAA,IACV,MAAM;AAAA,IACN,cAAc;AAAA,IACd,kBAAkB;AAAA,IAClB,iBAAiB;AAAA,IACjB,qBAAqB;AAAA,IACrB,uBAAuB;AAAA,IACvB,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,gBAAgB;AAAA,IAChB,OAAO;AAAA,EACb;AAEA,QAAM,CAAC,kBAAkB,mBAAmB,IAAID,sBAAsD;AAqCtG,QAAM,gBAAgB,CAChB,SACA,KACA,UACD;AACC,YAAQ,IAAI,0BAA0B,OAAO,SAAS,GAAG,YAAY,KAAK;AAC1E,QAAI,YAAY,aAAa;AACH,0BAAA,CAAA,UAAS,EAAE,GAAG,MAAM,CAAC,GAAG,GAAG,QAAQ;AAAA,IAAA,WAClD,YAAY,YAAY;AACV,yBAAA,CAAA,UAAS,EAAE,GAAG,MAAM,CAAC,GAAG,GAAG,QAAQ;AAAA,IAAA,WACjD,YAAY,YAAY;AACV,yBAAA,CAAA,UAAS,EAAE,GAAG,MAAM,CAAC,GAAG,GAAG,QAAQ;AAAA,IAAA,WACjD,YAAY,aAAa;AACV,0BAAA,CAAA,UAAS,EAAE,GAAG,MAAM,CAAC,GAAG,GAAG,QAAQ;AAAA,IAAA,WAClD,YAAY,aAAa;AACV,0BAAA,CAAA,UAAS,EAAE,GAAG,MAAM,CAAC,GAAG,GAAG,QAAQ;AAAA,IAAA;AAAA,EAEnE;AAGA,QAAM,cAAc;AAAA,IACd,EAAE,OAAO,MAAM,KAAK,KAAK;AAAA,IACzB,EAAE,OAAO,QAAQ,KAAK,OAAO;AAAA,EACnC;AAEA,QAAM,0BAA0B;AAAA,IAC1B,EAAE,OAAO,oDAAoD,KAAK,cAAc;AAAA,IAChF,EAAE,OAAO,kDAAkD,KAAK,YAAY;AAAA,IAC5E,EAAE,OAAO,kDAAkD,KAAK,cAAc;AAAA,IAC9E,EAAE,OAAO,sDAAsD,KAAK,gBAAgB;AAAA,IACpF,EAAE,OAAO,qDAAqD,KAAK,oBAAoB;AAAA,EAC7F;AAEA,QAAM,4BAA4B;AAAA,IAC5B,EAAE,OAAO,gCAAgC,KAAK,YAAY;AAAA,IAC1D,EAAE,OAAO,0CAA0C,KAAK,uBAAuB;AAAA,IAC/E,EAAE,OAAO,wCAAwC,KAAK,mBAAmB;AAAA,IACzE,EAAE,OAAO,iCAAiC,KAAK,0BAA0B;AAAA,EAC/E;AAEA,QAAM,wBAAwB;AAAA,IACxB;AAAA,MACM,OAAO;AAAA,MACP,KAAK;AAAA,MACL,SAAS;AAAA,QACH,EAAE,IAAI,uBAAuB,OAAO,SAAS,OAAO,WAAW;AAAA,QAC/D,EAAE,IAAI,yBAAyB,OAAO,WAAW,OAAO,cAAc;AAAA,MAAA;AAAA,IAElF;AAAA,IACA;AAAA,MACM,OAAO;AAAA,MACP,KAAK;AAAA,MACL,SAAS;AAAA,QACH,EAAE,IAAI,YAAY,OAAO,QAAQ,OAAO,OAAO;AAAA,QAC/C,EAAE,IAAI,gBAAgB,OAAO,YAAY,OAAO,WAAW;AAAA,MAAA;AAAA,IAEvE;AAAA,IACA;AAAA,MACM,OAAO;AAAA,MACP,KAAK;AAAA,MACL,SAAS;AAAA,QACH,EAAE,IAAI,qBAAqB,OAAO,QAAQ,OAAO,OAAO;AAAA,QACxD,EAAE,IAAI,qBAAqB,OAAO,QAAQ,OAAO,OAAO;AAAA,MAAA;AAAA,IAEpE;AAAA,IACA;AAAA,MACM,OAAO;AAAA,MACP,KAAK;AAAA,MACL,SAAS;AAAA,QACH,EAAE,IAAI,iBAAiB,OAAO,QAAQ,OAAO,OAAO;AAAA,QACpD,EAAE,IAAI,eAAe,OAAO,MAAM,OAAO,KAAK;AAAA,MAAA;AAAA,IAE1D;AAAA,IACA;AAAA,MACM,OAAO;AAAA,MACP,KAAK;AAAA,MACL,SAAS;AAAA,QACH,EAAE,IAAI,oBAAoB,OAAO,SAAS,OAAO,sBAAsB;AAAA,QACvE,EAAE,IAAI,oBAAoB,OAAO,SAAS,OAAO,iBAAiB;AAAA,MAAA;AAAA,IACxE;AAAA,EAEZ;AAEA,QAAM,uBAAuB;AAAA,IACvB,EAAE,OAAO,8BAA8B,KAAK,eAAe,MAAM,UAAU,QAAQ,GAAG;AAAA,IACtF,EAAE,OAAO,6BAA6B,KAAK,4BAA4B,MAAM,UAAU,QAAQ,IAAI;AAAA,IACnG,EAAE,OAAO,yBAAyB,KAAK,eAAe,MAAM,UAAU,QAAQ,IAAI;AAAA,IAClF,EAAE,OAAO,oBAAoB,KAAK,mBAAmB,MAAM,UAAU,QAAQ,IAAI;AAAA,IACjF,EAAE,OAAO,wBAAwB,KAAK,kBAAkB,MAAM,UAAU,QAAQ,MAAM;AAAA,EAC5F;AAEA,QAAM,4BAA4B;AAAA,IAC5B,EAAE,OAAO,wBAAwB,KAAK,YAAY;AAAA,EACxD;AAEA,QAAM,6BAA6B;AAAA,IAC7B,EAAE,OAAO,oDAAoD,KAAK,gBAAgB;AAAA,IAClF,EAAE,OAAO,gCAAgC,KAAK,iBAAiB;AAAA,IAC/D,EAAE,OAAO,mBAAmB,KAAK,wBAAwB;AAAA,IACzD,EAAE,OAAO,sCAAsC,KAAK,oBAAoB;AAAA,IACxE,EAAE,OAAO,0DAA0D,KAAK,yBAAyB;AAAA,IACjG,EAAE,OAAO,4CAA4C,KAAK,oBAAoB;AAAA,EACpF;AAEA,QAAM,wBAAwB;AAAA,IACxB,EAAE,OAAO,sBAAsB,KAAK,qBAAqB,MAAM,UAAU,QAAQ,IAAI,QAAQ,KAAK;AAAA,IAClG,EAAE,OAAO,6BAA6B,KAAK,wBAAwB,MAAM,UAAU,QAAQ,IAAI,QAAQ,OAAO;AAAA,IAC9G,EAAE,OAAO,wBAAwB,KAAK,uBAAuB,MAAM,UAAU,QAAQ,KAAK,QAAQ,GAAG;AAAA,IACrG,EAAE,OAAO,eAAe,KAAK,aAAa,MAAM,QAAQ,QAAQ,IAAI,QAAQ,GAAG;AAAA,IAC/E;AAAA,MACM,OAAO;AAAA,MACP,KAAK;AAAA,MACL,MAAM;AAAA;AAAA,MACN,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,SAAS;AAAA,QACH,EAAE,OAAO,OAAO,OAAO,MAAM;AAAA,QAC7B,EAAE,OAAO,QAAQ,OAAO,OAAO;AAAA,MAAA;AAAA,IACrC;AAAA,EAEZ;AAEM,QAAA,oBAAoB,OAAO,YAA+E;AAC1G,QAAI,UAAiC,CAAC;AAClC,QAAA,YAAY,YAAuB,WAAA;AAAA,aAC9B,YAAY,WAAsB,WAAA;AAAA,aAClC,YAAY,WAAsB,WAAA;AAAA,aAClC,YAAY,YAAuB,WAAA;AAAA,aACnC,YAAY,YAAuB,WAAA;AAG5C,QAAI,SAAsD,CAAC;AAC3D,QAAI,YAAY,YAAY;AACtB,UAAI,QAAQ,QAAQ,UAAa,QAAQ,QAAQ,YAAY;AACvD,YAAI,CAAC,QAAQ,OAAQ,QAAO,SAAS;AACrC,YAAI,CAAC,QAAQ,UAAW,QAAO,YAAY;AAC3C,YAAI,CAAC,QAAQ,WAAY,QAAO,aAAa;AAC7C,YAAI,CAAC,QAAQ,gBAAiB,QAAO,kBAAkB;AACvD,YAAI,CAAC,QAAQ,cAAe,QAAO,gBAAgB;AAAA,MAAA;AAEzD,UAAI,QAAQ,eAAe,UAAa,QAAQ,eAAe,SAAS;AAClE,YAAI,CAAC,QAAQ,qBAAsB,QAAO,uBAAuB;AAAA,MAAA;AAAA,IACvE;AAEN,QAAI,YAAY,aAAa;AACvB,UAAI,QAAQ,oBAAoB,UAAa,QAAQ,oBAAoB,QAAQ,OAAO,QAAQ,eAAe,MAAM,GAAI,QAAO,kBAAkB;AAClJ,UAAI,CAAC,QAAQ,oBAAqB,QAAO,sBAAsB;AAC/D,UAAI,CAAC,QAAQ,sBAAuB,QAAO,wBAAwB;AAAA,IAAA;AAEzE,QAAI,UAAU,OAAO,KAAK,MAAM,EAAE,SAAS,GAAG;AACxC,0BAAoB,MAAM;AAC1B;AAAA,IAAA,OACC;AACD,0BAAoB,CAAA,CAAE;AAAA,IAAA;AAItB,UAAA,cAAc,OAAO,QAAQ,OAAO,EACnC,OAAO,CAAC,CAAC,GAAG,KAAK,MAAM,UAAU,QAAQ,UAAU,MAAS,EAC5D,IAAI,CAAC,CAAC,KAAK,KAAK,MAAM;AACjB,YAAM,iBAAiB,OAAO,UAAU,YAAa,QAAQ,OAAO,QAAS;AAC7E,aAAO,EAAE,WAAW,WAAW,GAAyB,GAAG,OAAO,eAAe;AAAA,IAAA,CACtF;AAEH,QAAA,YAAY,WAAW,GAAG;AACxB,eAAS,oBAAoB;AAC7B;AAAA,IAAA;AAGE,YAAA,IAAI,0BAA0B,WAAW;AAC7C,QAAA;AACE,YAAM,kBAAkB,EAAE,SAAS,KAAK,UAAU,WAAW,GAAG;AACxD,cAAA,IAAI,mCAAmC,WAAW;AAC1D,gBAAU,WAAS,EAAE,GAAG,MAAM,GAAG,UAAU;AAC3C,eAAS,IAAI;AAEb,UAAI,YAAY,aAAa;AACvB,4BAAoB,CAAA,CAAE;AACtB,8BAAsB,KAAK;AAAA,MAAA,WACtB,YAAY,YAAY;AAC7B,2BAAmB,CAAA,CAAE;AACrB,6BAAqB,KAAK;AAAA,MAAA,WACrB,YAAY,YAAY;AAC7B,2BAAmB,CAAA,CAAE;AACrB,6BAAqB,KAAK;AAAA,MAAA,WACrB,YAAY,aAAa;AAC9B,4BAAoB,CAAA,CAAE;AACtB,8BAAsB,KAAK;AAAA,MAAA,WACtB,YAAY,aAAa;AAC9B,4BAAoB,CAAA,CAAE;AACtB,8BAAsB,KAAK;AAAA,MAAA;AAAA,aAE9BE,QAAO;AACF,cAAA,MAAM,+BAA+BA,MAAK;AAClD,eAASA,kBAAiB,QAAQA,OAAM,UAAU,wBAAwB;AAAA,IAAA;AAAA,EAEtF;AAEM,QAAA,sBAAsB,CAAC,YAA+E;AACtG,QAAI,YAAY,aAAa;AACvB,0BAAoB,CAAA,CAAE;AACtB,4BAAsB,KAAK;AAAA,IAAA,WACtB,YAAY,YAAY;AAC7B,yBAAmB,CAAA,CAAE;AACrB,2BAAqB,KAAK;AAAA,IAAA,WACrB,YAAY,YAAY;AAC7B,yBAAmB,CAAA,CAAE;AACrB,2BAAqB,KAAK;AAAA,IAAA,WACrB,YAAY,aAAa;AAC9B,0BAAoB,CAAA,CAAE;AACtB,4BAAsB,KAAK;AAAA,IAAA,WACtB,YAAY,aAAa;AAC9B,0BAAoB,CAAA,CAAE;AACtB,4BAAsB,KAAK;AAAA,IAAA;AAEjC,aAAS,IAAI;AACL,YAAA,IAAI,yBAAyB,OAAO,EAAE;AAAA,EACpD;AAGM,SAAAL,kCAAA,KAAC,OAAI,EAAA,WAAU,8EACR,UAAA;AAAA,IACK,SAAAC,kCAAA,IAAC,OAAI,EAAA,WAAU,4EAA2E,MAAK,SACzF,UAAAA,kCAAAA,IAAC,QAAK,EAAA,WAAU,mBAAmB,UAAA,MAAM,CAAA,GAC/C;AAAA,IAGND,kCAAAA,KAAC,OAAI,EAAA,WAAU,uEACT,UAAA;AAAA,MAACC,kCAAA,IAAA,OAAA,EAAI,WAAU,6CAA4C,UAAoB,wBAAA;AAAA,MAC9EA,kCAAA,IAAA,OAAA,EAAI,WAAU,uBACR,sBAAY,IAAI,CAAC,EAAE,OAAO,IAAI,MACxBD,uCAAA,OAAA,EAAc,WAAU,+BAClB,UAAA;AAAA,QAAA;AAAA,QAAM;AAAA,QAAG,OAAO,GAAyB;AAAA,MAAA,EADtC,GAAA,GAEV,CACL,EACP,CAAA;AAAA,IAAA,GACN;AAAA,IAGAA,kCAAAA,KAAC,OAAI,EAAA,WAAU,uEACT,UAAA;AAAA,MAACA,kCAAAA,KAAA,OAAA,EAAI,WAAU,wBACT,UAAA;AAAA,QAACC,kCAAA,IAAA,OAAA,EAAI,WAAU,6CAA4C,UAA0B,8BAAA;AAAA,QACrFA,kCAAA;AAAA,UAAC;AAAA,UAAA;AAAA,YACK,SAAS,MAAM;AACT,kBAAI,CAAC,oBAAoB;AACC,oCAAA;AAAA,kBACd,aAAa,OAAO;AAAA,kBACpB,WAAW,OAAO;AAAA,kBAClB,aAAa,OAAO;AAAA,kBACpB,eAAe,OAAO;AAAA,kBACtB,mBAAmB,OAAO;AAAA,gBAAA,CAC/B;AACD,wBAAQ,IAAI,oCAAoC;AAAA,cAAA;AAEhC,oCAAA,CAAA,SAAQ,CAAC,IAAI;AAAA,YACzC;AAAA,YACA,WAAU;AAAA,YAET,+BAEWD,kCAAAA,KAAAM,kBAAA,UAAA,EAAA,UAAA;AAAA,cAACL,kCAAAA,IAAA,GAAA,EAAE,WAAU,UAAU,CAAA;AAAA,cAAE;AAAA,YAAA,EAAA,CAC/B,IAGMD,kCAAAA,KAAAM,kBAAA,UAAA,EAAA,UAAA;AAAA,cAACL,kCAAAA,IAAAM,WAAA,EAAK,WAAU,UAAU,CAAA;AAAA,cAAE;AAAA,YAAA,EAClC,CAAA;AAAA,UAAA;AAAA,QAAA;AAAA,MAEZ,GACN;AAAA,MACC,wBAAwB,IAAI,CAAC,EAAE,OAAO,UAAU;AAC3C,cAAM,IAAI;AACV,cAAM,eAAe,qBACZ,iBAAiB,CAAC,MAAM,SAAY,iBAAiB,CAAC,IAAI,OAAO,CAAC,IACnE,OAAO,CAAC;AAEV,eAAAP,kCAAA,KAAC,OAAc,EAAA,WAAU,mCACnB,UAAA;AAAA,UAACC,kCAAA,IAAA,OAAA,EAAM,WAAU,yCAAyC,UAAM,OAAA;AAAA,UAChED,kCAAA;AAAA,YAAC;AAAA,YAAA;AAAA,cACK,OAAO,eAAe,CAAC,mBAAmB,EAAE,SAAS,GAAG,IAAI,QAAQ,OAAO,CAAC,mBAAmB,EAAE,SAAS,GAAG,IAAI,OAAO;AAAA,cACxH,eAAe,CAAC,QAAQ;AAClB,oBAAI,oBAAoB;AAClB,sBAAI,MAAM,qBAAqB;AACX,kCAAA,aAAa,qBAAqB,QAAQ,KAAK;AAC7D,4BAAQ,IAAI,yBAAyB,GAAG,WAAW,QAAQ,KAAK,EAAE;AAAA,kBAAA,OACjE;AACa,kCAAA,aAAa,GAAG,QAAQ,IAAI;AAC1C,4BAAQ,IAAI,yBAAyB,GAAG,WAAW,QAAQ,IAAI,EAAE;AAAA,kBAAA;AAAA,gBACvE;AAAA,cAEZ;AAAA,cACA,UAAU,CAAC;AAAA,cACX,WAAU;AAAA,cAEV,UAAA;AAAA,gBAACA,kCAAAA,KAAA,OAAA,EAAI,WAAU,2BACT,UAAA;AAAA,kBAAAC,sCAAC,kBAAe,IAAI,GAAG,GAAG,QAAQ,OAAM,OAAM;AAAA,wDAC7C,OAAM,EAAA,SAAS,GAAG,GAAG,QAAQ,UAAG,MAAA,CAAA;AAAA,gBAAA,GACvC;AAAA,gBACAD,kCAAAA,KAAC,OAAI,EAAA,WAAU,2BACT,UAAA;AAAA,kBAAAC,sCAAC,kBAAe,IAAI,GAAG,GAAG,OAAO,OAAM,MAAK;AAAA,wDAC3C,OAAM,EAAA,SAAS,GAAG,GAAG,OAAO,UAAE,KAAA,CAAA;AAAA,gBAAA,EACrC,CAAA;AAAA,cAAA;AAAA,YAAA;AAAA,UAAA;AAAA,QACN,EAAA,GA1BI,GA2BV;AAAA,MAAA,CAEX;AAAA,MACA,sBACKD,kCAAA,KAAC,OAAI,EAAA,WAAU,6BACT,UAAA;AAAA,QAACC,kCAAAA,IAAA,UAAA,EAAO,SAAS,MAAM,oBAAoB,WAAW,GAAG,WAAU,+BAA8B,UAEjG,SAAA,CAAA;AAAA,QACAA,kCAAAA,IAAC,YAAO,SAAS,MAAM,kBAAkB,WAAW,GAAG,WAAU,8CAA6C,UAE9G,OAAA,CAAA;AAAA,MAAA,EACN,CAAA;AAAA,IAAA,GAEZ;AAAA,IAGAD,kCAAAA,KAAC,OAAI,EAAA,WAAU,uEACT,UAAA;AAAA,MAACA,kCAAAA,KAAA,OAAA,EAAI,WAAU,wBACT,UAAA;AAAA,QAACC,kCAAA,IAAA,OAAA,EAAI,WAAU,6CAA4C,UAAuB,2BAAA;AAAA,QAClFA,kCAAA;AAAA,UAAC;AAAA,UAAA;AAAA,YACK,SAAS,MAAM;AACT,kBAAI,CAAC,mBAAmB;AACC,mCAAA;AAAA,kBACb,WAAW,OAAO;AAAA,kBAClB,sBAAsB,OAAO;AAAA,kBAC7B,kBAAkB,OAAO;AAAA,kBACzB,kBAAkB,OAAO;AAAA,kBACzB,eAAe,OAAO;AAAA,kBACtB,eAAe,OAAO;AAAA,kBACtB,eAAe,OAAO;AAAA,kBACtB,yBAAyB,OAAO;AAAA,kBAChC,qBAAqB,OAAO;AAAA,kBAC5B,mBAAmB,OAAO;AAAA,kBAC1B,oBAAoB,OAAO;AAAA,kBAC3B,QAAQ,OAAO;AAAA,kBACf,SAAS,OAAO;AAAA,kBAChB,QAAQ,OAAO;AAAA,kBACf,SAAS,OAAO;AAAA,kBAChB,QAAQ,OAAO;AAAA,kBACf,SAAS,OAAO;AAAA,kBAChB,KAAK,OAAO;AAAA,kBACZ,QAAQ,OAAO;AAAA,kBACf,WAAW,OAAO;AAAA,kBAClB,YAAY,OAAO;AAAA,kBACnB,iBAAiB,OAAO;AAAA,kBACxB,eAAe,OAAO;AAAA,kBACtB,cAAc,OAAO;AAAA,kBACrB,YAAY,OAAO;AAAA,kBACnB,sBAAsB,OAAO;AAAA,gBAAA,CAClC;AACD,wBAAQ,IAAI,iCAAiC;AAAA,cAAA;AAE9B,mCAAA,CAAA,SAAQ,CAAC,IAAI;AAAA,YACxC;AAAA,YACA,WAAU;AAAA,YAET,8BAEWD,kCAAAA,KAAAM,kBAAA,UAAA,EAAA,UAAA;AAAA,cAACL,kCAAAA,IAAA,GAAA,EAAE,WAAU,UAAU,CAAA;AAAA,cAAE;AAAA,YAAA,EAAA,CAC/B,IAGMD,kCAAAA,KAAAM,kBAAA,UAAA,EAAA,UAAA;AAAA,cAACL,kCAAAA,IAAAM,WAAA,EAAK,WAAU,UAAU,CAAA;AAAA,cAAE;AAAA,YAAA,EAClC,CAAA;AAAA,UAAA;AAAA,QAAA;AAAA,MAEZ,GACN;AAAA,MACC,0BAA0B,IAAI,CAAC,EAAE,OAAO,UAAU;AAC7C,cAAM,IAAI;AACV,cAAM,eAAe,oBACZ,gBAAgB,CAAC,MAAM,SAAY,gBAAgB,CAAC,IAAI,OAAO,CAAC,IACjE,OAAO,CAAC;AAChB,eAEYP,kCAAA,KAAAM,4BAAA,EAAA,UAAA;AAAA,UAACN,kCAAAA,KAAA,OAAA,EAAc,WAAU,mCACnB,UAAA;AAAA,YAACC,kCAAA,IAAA,OAAA,EAAM,WAAU,yCAAyC,UAAM,OAAA;AAAA,YAChED,kCAAA;AAAA,cAAC;AAAA,cAAA;AAAA,gBACK,OAAO,eAAe,QAAQ;AAAA,gBAC9B,eAAe,CAAC,QAAQ;AAClB,sBAAI,mBAAmB;AACb,wBAAA,MAAM,sBAAsB,QAAQ,MAAM;AAC1B,oCAAA,YAAY,oBAAoB,CAAC;AAAA,oBAAA;AAEvC,kCAAA,YAAY,GAAG,QAAQ,KAAK;AAC1C,4BAAQ,IAAI,sBAAsB,GAAG,WAAW,QAAQ,KAAK,EAAE;AAAA,kBAAA;AAAA,gBAE3E;AAAA,gBACA,UAAU,CAAC;AAAA,gBACX,WAAU;AAAA,gBAEV,UAAA;AAAA,kBAACA,kCAAAA,KAAA,OAAA,EAAI,WAAU,2BACT,UAAA;AAAA,oBAAAC,sCAAC,kBAAe,IAAI,GAAG,GAAG,QAAQ,OAAM,OAAM;AAAA,0DAC7C,OAAM,EAAA,SAAS,GAAG,GAAG,QAAQ,UAAG,MAAA,CAAA;AAAA,kBAAA,GACvC;AAAA,kBACAD,kCAAAA,KAAC,OAAI,EAAA,WAAU,2BACT,UAAA;AAAA,oBAAAC,sCAAC,kBAAe,IAAI,GAAG,GAAG,OAAO,OAAM,MAAK;AAAA,0DAC3C,OAAM,EAAA,SAAS,GAAG,GAAG,OAAO,UAAE,KAAA,CAAA;AAAA,kBAAA,EACrC,CAAA;AAAA,gBAAA;AAAA,cAAA;AAAA,YAAA;AAAA,UACN,EAAA,GAxBI,GAyBV;AAAA,UACC,QAAQ,sBAAsB,iBAAiB,QACzCD,kCAAAA,KAAA,OAAA,EAAI,WAAU,mCACT,UAAA;AAAA,YAACC,kCAAA,IAAA,OAAA,EAAM,WAAU,yCAAwC,UAAsB,0BAAA;AAAA,YAC/EA,kCAAA;AAAA,cAAC;AAAA,cAAA;AAAA,gBACK,MAAK;AAAA,gBACL,KAAK;AAAA,gBACL,OACM,gBAAgB,qBAAqB,SAC7B,gBAAgB,oBAAoB,KACpC,OAAO,oBAAoB;AAAA,gBAEzC,WAAW,CAAC,MAAM;AACZ,sBAAI,EAAE,QAAQ,OAAO,EAAE,QAAQ,KAAK;AAC9B,sBAAE,eAAe;AAAA,kBAAA;AAAA,gBAE7B;AAAA,gBACA,UAAU,CAAC,MAAM;AACX,wBAAM,QAAQ,WAAW,EAAE,OAAO,KAAK;AACvC,sBAAI,SAAS,GAAG;AACI,kCAAA,YAAY,oBAAoB,KAAK;AAC3C,4BAAA,IAAI,4BAA4B,KAAK,EAAE;AAAA,kBAAA;AAAA,gBAE3D;AAAA,gBACA,UAAU,CAAC;AAAA,gBACX,WAAU;AAAA,cAAA;AAAA,YAAA;AAAA,UAChB,EACN,CAAA;AAAA,QAAA,GAEZ;AAAA,MAAA,CAEX;AAAA,MACDD,kCAAAA,KAAC,OAAI,EAAA,WAAU,mCACT,UAAA;AAAA,QAACC,kCAAA,IAAA,OAAA,EAAM,WAAU,yCAAwC,UAAuC,2CAAA;AAAA,QAChGA,kCAAA;AAAA,UAAC;AAAA,UAAA;AAAA,YACK,MAAK;AAAA,YACL,KAAK;AAAA,YACL,OACM,gBAAgB,wBAAwB,SAChC,gBAAgB,uBAAuB,KACvC,OAAO,uBAAuB;AAAA,YAE5C,WAAW,CAAC,MAAM;AACZ,kBAAI,EAAE,QAAQ,OAAO,EAAE,QAAQ,KAAK;AAC9B,kBAAE,eAAe;AAAA,cAAA;AAAA,YAE7B;AAAA,YACA,UAAU,CAAC,MAAM;AACX,oBAAM,QAAQ,WAAW,EAAE,OAAO,KAAK;AACzB,4BAAA,YAAY,uBAAuB,KAAK;AAC9C,sBAAA,IAAI,gCAAgC,KAAK,EAAE;AAAA,YAEzD;AAAA,YACA,UAAU,CAAC;AAAA,YACX,WAAU;AAAA,UAAA;AAAA,QAAA;AAAA,MAChB,GACN;AAAA,MACC,sBAAsB,IAAI,CAAC,EAAE,OAAO,KAAK,cAAc;AAClD,cAAM,IAAI;AACV,cAAM,eAAe,oBACZ,gBAAgB,CAAC,MAAM,SAAY,gBAAgB,CAAC,IAAI,OAAO,CAAC,IACjE,OAAO,CAAC;AAChB,eAEYD,kCAAA,KAAAM,4BAAA,EAAA,UAAA;AAAA,UAACN,kCAAAA,KAAA,OAAA,EAAc,WAAU,mCACnB,UAAA;AAAA,YAACA,kCAAAA,KAAA,OAAA,EAAM,WAAU,yCAAyC,UAAA;AAAA,cAAA;AAAA,cAAM;AAAA,YAAA,GAAC;AAAA,YACjEC,kCAAA;AAAA,cAAC;AAAA,cAAA;AAAA,gBACK,OAAO,gBAAgB;AAAA,gBACvB,eAAe,CAAC,QAAQ;AAClB,sBAAI,mBAAmB;AACb,wBAAA,MAAM,SAAS,QAAQ,QAAQ;AACf,oCAAA,YAAY,UAAU,IAAI;AAC1B,oCAAA,YAAY,aAAa,IAAI;AAC7B,oCAAA,YAAY,cAAc,IAAI;AAC9B,oCAAA,YAAY,mBAAmB,IAAI;AACnC,oCAAA,YAAY,iBAAiB,IAAI;AAAA,oBAAA;AAEjD,wBAAA,MAAM,gBAAgB,QAAQ,SAAS;AACvB,oCAAA,YAAY,wBAAwB,IAAI;AAAA,oBAAA;AAE9C,kCAAA,YAAY,GAAG,GAAG;AAChC,4BAAQ,IAAI,sBAAsB,GAAG,WAAW,GAAG,EAAE;AAAA,kBAAA;AAAA,gBAEjE;AAAA,gBACA,UAAU,CAAC;AAAA,gBACX,WAAU;AAAA,gBAET,UAAQ,QAAA,IAAI,CAAC,EAAE,IAAI,OAAO,OAAAO,OACrB,MAAAR,kCAAA,KAAC,OAAa,EAAA,WAAU,2BAClB,UAAA;AAAA,kBAAAC,kCAAA,IAAC,gBAAe,EAAA,IAAQ,OAAO,OAAO,KAAK,GAAG;AAAA,kBAC7CA,kCAAA,IAAA,OAAA,EAAM,SAAS,IAAK,UAAAO,OAAM,CAAA;AAAA,gBAAA,EAAA,GAFvB,EAGV,CACL;AAAA,cAAA;AAAA,YAAA;AAAA,UACP,EAAA,GA7BI,GA8BV;AAAA,UACC,QAAQ,SAAS,iBAAiB,cAEvBR,kCAAAA,KAAAM,kBAAAA,UAAA,EAAA,UAAA;AAAA,YAACN,kCAAAA,KAAA,OAAA,EAAI,WAAU,mCACT,UAAA;AAAA,cAACC,kCAAA,IAAA,OAAA,EAAM,WAAU,yCAAwC,UAAiB,qBAAA;AAAA,cAC1EA,kCAAA;AAAA,gBAAC;AAAA,gBAAA;AAAA,kBACK,MAAK;AAAA,kBACL,OAAO,gBAAgB,WAAW,SAAa,gBAAgB,UAAU,KAAO,OAAO,UAAU;AAAA,kBACjG,UAAU,CAAC,MAAM;AACX,wBAAI,mBAAmB;AACjB,oCAAc,YAAY,UAAU,EAAE,OAAO,KAAK;AAClD,8BAAQ,IAAI,kBAAkB,EAAE,OAAO,KAAK,EAAE;AAAA,oBAAA;AAAA,kBAE1D;AAAA,kBACA,aAAY;AAAA,kBACZ,UAAU,CAAC;AAAA,kBACX,WAAU;AAAA,gBAAA;AAAA,cAChB;AAAA,eACC,qDAAkB,WACbA,kCAAA,IAAC,OAAE,WAAU,wBAAwB,2BAAiB,OAAO,CAAA;AAAA,YAAA,GAEzE;AAAA,YACAD,kCAAAA,KAAC,OAAI,EAAA,WAAU,mCACT,UAAA;AAAA,cAACC,kCAAA,IAAA,OAAA,EAAM,WAAU,yCAAwC,UAAqB,yBAAA;AAAA,cAC9EA,kCAAA;AAAA,gBAAC;AAAA,gBAAA;AAAA,kBACK,MAAK;AAAA,kBACL,OAAO,gBAAgB,cAAc,SAAa,gBAAgB,aAAa,KAAO,OAAO,aAAa;AAAA,kBAC1G,UAAU,CAAC,MAAM;AACX,wBAAI,mBAAmB;AACjB,oCAAc,YAAY,aAAa,EAAE,OAAO,KAAK;AACrD,8BAAQ,IAAI,0BAA0B,EAAE,OAAO,KAAK,EAAE;AAAA,oBAAA;AAAA,kBAElE;AAAA,kBACA,aAAY;AAAA,kBACZ,UAAU,CAAC;AAAA,kBACX,WAAU;AAAA,gBAAA;AAAA,cAChB;AAAA,eACC,qDAAkB,cACbA,kCAAA,IAAC,OAAE,WAAU,wBAAwB,2BAAiB,UAAU,CAAA;AAAA,YAAA,GAE5E;AAAA,YACAD,kCAAAA,KAAC,OAAI,EAAA,WAAU,mCACT,UAAA;AAAA,cAACC,kCAAA,IAAA,OAAA,EAAM,WAAU,yCAAwC,UAAyB,6BAAA;AAAA,cAClFA,kCAAA;AAAA,gBAAC;AAAA,gBAAA;AAAA,kBACK,MAAK;AAAA,kBACL,OAAO,gBAAgB,eAAe,SAAa,gBAAgB,cAAc,KAAO,OAAO,cAAc;AAAA,kBAC7G,UAAU,CAAC,MAAM;AACX,wBAAI,mBAAmB;AACjB,oCAAc,YAAY,cAAc,EAAE,OAAO,KAAK;AACtD,8BAAQ,IAAI,8BAA8B,EAAE,OAAO,KAAK,EAAE;AAAA,oBAAA;AAAA,kBAEtE;AAAA,kBACA,aAAY;AAAA,kBACZ,UAAU,CAAC;AAAA,kBACX,WAAU;AAAA,gBAAA;AAAA,cAChB;AAAA,eACC,qDAAkB,eACbA,kCAAA,IAAC,OAAE,WAAU,wBAAwB,2BAAiB,WAAW,CAAA;AAAA,YAAA,GAE7E;AAAA,YACAD,kCAAAA,KAAC,OAAI,EAAA,WAAU,mCACT,UAAA;AAAA,cAACC,kCAAA,IAAA,OAAA,EAAM,WAAU,yCAAwC,UAA+B,mCAAA;AAAA,cACxFA,kCAAA;AAAA,gBAAC;AAAA,gBAAA;AAAA,kBACK,MAAK;AAAA,kBACL,OAAO,gBAAgB,oBAAoB,SAAa,gBAAgB,mBAAmB,KAAO,OAAO,mBAAmB;AAAA,kBAC5H,UAAU,CAAC,MAAM;AACX,wBAAI,mBAAmB;AACjB,oCAAc,YAAY,mBAAmB,EAAE,OAAO,KAAK;AAC3D,8BAAQ,IAAI,oCAAoC,EAAE,OAAO,KAAK,EAAE;AAAA,oBAAA;AAAA,kBAE5E;AAAA,kBACA,aAAY;AAAA,kBACZ,UAAU,CAAC;AAAA,kBACX,WAAU;AAAA,gBAAA;AAAA,cAChB;AAAA,eACC,qDAAkB,oBACbA,kCAAA,IAAC,OAAE,WAAU,wBAAwB,2BAAiB,gBAAgB,CAAA;AAAA,YAAA,GAElF;AAAA,YACAD,kCAAAA,KAAC,OAAI,EAAA,WAAU,mCACT,UAAA;AAAA,cAACC,kCAAA,IAAA,OAAA,EAAM,WAAU,yCAAwC,UAA4B,gCAAA;AAAA,cACrFA,kCAAA;AAAA,gBAAC;AAAA,gBAAA;AAAA,kBACK,MAAK;AAAA,kBACL,OAAO,gBAAgB,kBAAkB,SAAa,gBAAgB,iBAAiB,KAAO,OAAO,iBAAiB;AAAA,kBACtH,UAAU,CAAC,MAAM;AACX,wBAAI,mBAAmB;AACjB,oCAAc,YAAY,iBAAiB,EAAE,OAAO,KAAK;AACzD,8BAAQ,IAAI,iCAAiC,EAAE,OAAO,KAAK,EAAE;AAAA,oBAAA;AAAA,kBAEzE;AAAA,kBACA,aAAY;AAAA,kBACZ,UAAU,CAAC;AAAA,kBACX,WAAU;AAAA,gBAAA;AAAA,cAChB;AAAA,eACC,qDAAkB,kBACbA,kCAAA,IAAC,OAAE,WAAU,wBAAwB,2BAAiB,cAAc,CAAA;AAAA,YAAA,EAEhF,CAAA;AAAA,UAAA,GACN;AAAA,UAGL,OAAO,gBAAgB,gBAAgB,+EAE5B,UAACD,kCAAA,KAAA,OAAA,EAAI,WAAU,mCACT,UAAA;AAAA,YAACC,kCAAA,IAAA,OAAA,EAAM,WAAU,yCAAwC,UAA+B,mCAAA;AAAA,YACxFA,kCAAA;AAAA,cAAC;AAAA,cAAA;AAAA,gBACK,MAAK;AAAA,gBACL,KAAK;AAAA,gBACL,OAAO,gBAAgB,yBAAyB,SAAa,gBAAgB,wBAAwB,KAAO,OAAO,wBAAwB;AAAA,gBAC3I,UAAU,CAAC,MAAM;AACX,sBAAI,mBAAmB;AACjB,kCAAc,YAAY,wBAAwB,EAAE,OAAO,KAAK;AAChE,4BAAQ,IAAI,kCAAkC,EAAE,OAAO,KAAK,EAAE;AAAA,kBAAA;AAAA,gBAE1E;AAAA,gBACA,UAAU,CAAC;AAAA,gBACX,WAAU;AAAA,cAAA;AAAA,YAChB;AAAA,aACC,qDAAkB,yBACbA,kCAAA,IAAC,OAAE,WAAU,wBAAwB,2BAAiB,qBAAqB,CAAA;AAAA,UAAA,EAAA,CAEvF,EACN,CAAA;AAAA,QAAA,GAGZ;AAAA,MAAA,CAEX;AAAA,MACDD,kCAAAA,KAAC,OAAI,EAAA,WAAU,4BACT,UAAA;AAAA,QAACC,kCAAA,IAAA,OAAA,EAAI,WAAU,+BAA8B,UAAQ,YAAA;AAAA,QACrDD,kCAAAA,KAAC,OAAI,EAAA,WAAU,2BACT,UAAA;AAAA,UAACA,kCAAAA,KAAA,OAAA,EAAI,WAAU,qCACT,UAAA;AAAA,YAACC,kCAAA,IAAA,OAAA,EAAM,WAAU,yCAAwC,UAAc,kBAAA;AAAA,YACvEA,kCAAA;AAAA,cAAC;AAAA,cAAA;AAAA,gBACK,MAAK;AAAA,gBACL,OAAO,gBAAgB,kBAAkB,SAAY,OAAO,gBAAgB,aAAa,IAAI,OAAO,OAAO,iBAAiB,EAAE;AAAA,gBAC9H,UAAU,CAAC,MAAM;AACX,gCAAc,YAAY,iBAAiB,EAAE,OAAO,KAAK;AACzD,0BAAQ,IAAI,wBAAwB,EAAE,OAAO,KAAK,EAAE;AAAA,gBAC1D;AAAA,gBACA,UAAU,CAAC;AAAA,gBACX,WAAU;AAAA,cAAA;AAAA,YAAA;AAAA,UAChB,GACN;AAAA,UACAD,kCAAAA,KAAC,OAAI,EAAA,WAAU,2BACT,UAAA;AAAA,YAACC,kCAAA,IAAA,OAAA,EAAM,WAAU,yCAAwC,UAAc,kBAAA;AAAA,YACvEA,kCAAA;AAAA,cAAC;AAAA,cAAA;AAAA,gBACK,MAAK;AAAA,gBACL,OAAO,gBAAgB,kBAAkB,SAAY,OAAO,gBAAgB,aAAa,IAAI,OAAO,OAAO,iBAAiB,EAAE;AAAA,gBAC9H,UAAU,CAAC,MAAM;AACX,gCAAc,YAAY,iBAAiB,EAAE,OAAO,KAAK;AACzD,0BAAQ,IAAI,wBAAwB,EAAE,OAAO,KAAK,EAAE;AAAA,gBAC1D;AAAA,gBACA,UAAU,CAAC;AAAA,gBACX,WAAU;AAAA,cAAA;AAAA,YAAA;AAAA,UAChB,EACN,CAAA;AAAA,QAAA,GACN;AAAA,QACAD,kCAAAA,KAAC,OAAI,EAAA,WAAU,2BACT,UAAA;AAAA,UAACA,kCAAAA,KAAA,OAAA,EAAI,WAAU,qCACT,UAAA;AAAA,YAACC,kCAAA,IAAA,OAAA,EAAM,WAAU,yCAAwC,UAAkB,sBAAA;AAAA,YAC3EA,kCAAA;AAAA,cAAC;AAAA,cAAA;AAAA,gBACK,MAAK;AAAA,gBACL,KAAK;AAAA,gBACL,OACM,gBAAgB,sBAAsB,SAC9B,gBAAgB,oBAChB,OAAO,qBAAqB;AAAA,gBAE1C,WAAW,CAAC,MAAM;AACZ,sBAAI,EAAE,QAAQ,OAAO,EAAE,QAAQ,KAAK;AAC9B,sBAAE,eAAe;AAAA,kBAAA;AAAA,gBAE7B;AAAA,gBACA,UAAU,CAAC,MAAM;AACX,wBAAM,QAAQ,WAAW,EAAE,OAAO,KAAK;AACvC,sBAAI,SAAS,GAAG;AACI,kCAAA,YAAY,qBAAqB,KAAK;AAC5C,4BAAA,IAAI,4BAA4B,KAAK,UAAU;AAAA,kBAAA;AAAA,gBAEnE;AAAA,gBACA,UAAU,CAAC;AAAA,gBACX,WAAU;AAAA,cAAA;AAAA,YAAA;AAAA,UAChB,GACN;AAAA,UACAD,kCAAAA,KAAC,OAAI,EAAA,WAAU,2BACT,UAAA;AAAA,YAACC,kCAAA,IAAA,OAAA,EAAM,WAAU,yCAAwC,UAAmB,uBAAA;AAAA,YAC5EA,kCAAA;AAAA,cAAC;AAAA,cAAA;AAAA,gBACK,MAAK;AAAA,gBACL,KAAK;AAAA,gBACL,OACM,gBAAgB,uBAAuB,SAC/B,gBAAgB,qBAChB,OAAO,sBAAsB;AAAA,gBAE3C,WAAW,CAAC,MAAM;AACZ,sBAAI,EAAE,QAAQ,OAAO,EAAE,QAAQ,KAAK;AAC9B,sBAAE,eAAe;AAAA,kBAAA;AAAA,gBAE7B;AAAA,gBACA,UAAU,CAAC,MAAM;AACX,wBAAM,QAAQ,WAAW,EAAE,OAAO,KAAK;AACvC,sBAAI,SAAS,GAAG;AACI,kCAAA,YAAY,sBAAsB,KAAK;AAC7C,4BAAA,IAAI,6BAA6B,KAAK,UAAU;AAAA,kBAAA;AAAA,gBAEpE;AAAA,gBACA,UAAU,CAAC;AAAA,gBACX,WAAU;AAAA,cAAA;AAAA,YAAA;AAAA,UAChB,EACN,CAAA;AAAA,QAAA,GACN;AAAA,QACAD,kCAAAA,KAAC,OAAI,EAAA,WAAU,2BACT,UAAA;AAAA,UAACA,kCAAAA,KAAA,OAAA,EAAI,WAAU,qCACT,UAAA;AAAA,YAACC,kCAAA,IAAA,OAAA,EAAM,WAAU,yCAAwC,UAAwB,4BAAA;AAAA,YACjFA,kCAAA;AAAA,cAAC;AAAA,cAAA;AAAA,gBACK,MAAK;AAAA,gBACL,KAAK;AAAA,gBACL,OACM,gBAAgB,WAAW,SACnB,gBAAgB,SAChB,OAAO,UAAU;AAAA,gBAE/B,WAAW,CAAC,MAAM;AACZ,sBAAI,EAAE,QAAQ,OAAO,EAAE,QAAQ,KAAK;AAC9B,sBAAE,eAAe;AAAA,kBAAA;AAAA,gBAE7B;AAAA,gBACA,UAAU,CAAC,MAAM;AACX,wBAAM,QAAQ,WAAW,EAAE,OAAO,KAAK;AACvC,sBAAI,SAAS,GAAG;AACI,kCAAA,YAAY,UAAU,KAAK;AACjC,4BAAA,IAAI,iBAAiB,KAAK,UAAU;AAAA,kBAAA;AAAA,gBAExD;AAAA,gBACA,UAAU,CAAC;AAAA,gBACX,WAAU;AAAA,cAAA;AAAA,YAAA;AAAA,UAChB,GACN;AAAA,UACAD,kCAAAA,KAAC,OAAI,EAAA,WAAU,2BACT,UAAA;AAAA,YAACC,kCAAA,IAAA,OAAA,EAAM,WAAU,yCAAwC,UAA0B,8BAAA;AAAA,YACnFA,kCAAA;AAAA,cAAC;AAAA,cAAA;AAAA,gBACK,MAAK;AAAA,gBACL,KAAK;AAAA,gBACL,OACM,gBAAgB,YAAY,SACpB,gBAAgB,UAChB,OAAO,WAAW;AAAA,gBAEhC,WAAW,CAAC,MAAM;AACZ,sBAAI,EAAE,QAAQ,OAAO,EAAE,QAAQ,KAAK;AAC9B,sBAAE,eAAe;AAAA,kBAAA;AAAA,gBAE7B;AAAA,gBACA,UAAU,CAAC,MAAM;AACX,wBAAM,QAAQ,WAAW,EAAE,OAAO,KAAK;AACvC,sBAAI,SAAS,GAAG;AACI,kCAAA,YAAY,WAAW,KAAK;AAClC,4BAAA,IAAI,kBAAkB,KAAK,UAAU;AAAA,kBAAA;AAAA,gBAEzD;AAAA,gBACA,UAAU,CAAC;AAAA,gBACX,WAAU;AAAA,cAAA;AAAA,YAAA;AAAA,UAChB,EACN,CAAA;AAAA,QAAA,GACN;AAAA,QACAD,kCAAAA,KAAC,OAAI,EAAA,WAAU,2BACT,UAAA;AAAA,UAACA,kCAAAA,KAAA,OAAA,EAAI,WAAU,qCACT,UAAA;AAAA,YAACC,kCAAA,IAAA,OAAA,EAAM,WAAU,yCAAwC,UAAoB,wBAAA;AAAA,YAC7EA,kCAAA;AAAA,cAAC;AAAA,cAAA;AAAA,gBACK,MAAK;AAAA,gBACL,KAAK;AAAA,gBACL,OACM,gBAAgB,WAAW,SACnB,gBAAgB,SAChB,OAAO,UAAU;AAAA,gBAE/B,WAAW,CAAC,MAAM;AACZ,sBAAI,EAAE,QAAQ,OAAO,EAAE,QAAQ,KAAK;AAC9B,sBAAE,eAAe;AAAA,kBAAA;AAAA,gBAE7B;AAAA,gBACA,UAAU,CAAC,MAAM;AACX,wBAAM,QAAQ,WAAW,EAAE,OAAO,KAAK;AACvC,sBAAI,SAAS,GAAG;AACI,kCAAA,YAAY,UAAU,KAAK;AACjC,4BAAA,IAAI,iBAAiB,KAAK,UAAU;AAAA,kBAAA;AAAA,gBAExD;AAAA,gBACA,UAAU,CAAC;AAAA,gBACX,WAAU;AAAA,cAAA;AAAA,YAAA;AAAA,UAChB,GACN;AAAA,UACAD,kCAAAA,KAAC,OAAI,EAAA,WAAU,2BACT,UAAA;AAAA,YAACC,kCAAA,IAAA,OAAA,EAAM,WAAU,yCAAwC,UAAsB,0BAAA;AAAA,YAC/EA,kCAAA;AAAA,cAAC;AAAA,cAAA;AAAA,gBACK,MAAK;AAAA,gBACL,KAAK;AAAA,gBACL,OACM,gBAAgB,YAAY,SACpB,gBAAgB,UAChB,OAAO,WAAW;AAAA,gBAEhC,WAAW,CAAC,MAAM;AACZ,sBAAI,EAAE,QAAQ,OAAO,EAAE,QAAQ,KAAK;AAC9B,sBAAE,eAAe;AAAA,kBAAA;AAAA,gBAE7B;AAAA,gBACA,UAAU,CAAC,MAAM;AACX,wBAAM,QAAQ,WAAW,EAAE,OAAO,KAAK;AACvC,sBAAI,SAAS,GAAG;AACI,kCAAA,YAAY,WAAW,KAAK;AAClC,4BAAA,IAAI,kBAAkB,KAAK,UAAU;AAAA,kBAAA;AAAA,gBAEzD;AAAA,gBACA,UAAU,CAAC;AAAA,gBACX,WAAU;AAAA,cAAA;AAAA,YAAA;AAAA,UAChB,EACN,CAAA;AAAA,QAAA,GACN;AAAA,QACAD,kCAAAA,KAAC,OAAI,EAAA,WAAU,2BACT,UAAA;AAAA,UAACA,kCAAAA,KAAA,OAAA,EAAI,WAAU,qCACT,UAAA;AAAA,YAACC,kCAAA,IAAA,OAAA,EAAM,WAAU,yCAAwC,UAAqB,yBAAA;AAAA,YAC9EA,kCAAA;AAAA,cAAC;AAAA,cAAA;AAAA,gBACK,MAAK;AAAA,gBACL,KAAK;AAAA,gBACL,OACM,gBAAgB,WAAW,SACnB,gBAAgB,SAChB,OAAO,UAAU;AAAA,gBAE/B,WAAW,CAAC,MAAM;AACZ,sBAAI,EAAE,QAAQ,OAAO,EAAE,QAAQ,KAAK;AAC9B,sBAAE,eAAe;AAAA,kBAAA;AAAA,gBAE7B;AAAA,gBACA,UAAU,CAAC,MAAM;AACX,wBAAM,QAAQ,WAAW,EAAE,OAAO,KAAK;AACvC,sBAAI,SAAS,GAAG;AACI,kCAAA,YAAY,UAAU,KAAK;AACjC,4BAAA,IAAI,iBAAiB,KAAK,UAAU;AAAA,kBAAA;AAAA,gBAExD;AAAA,gBACA,UAAU,CAAC;AAAA,gBACX,WAAU;AAAA,cAAA;AAAA,YAAA;AAAA,UAChB,GACN;AAAA,UACAD,kCAAAA,KAAC,OAAI,EAAA,WAAU,2BACT,UAAA;AAAA,YAACC,kCAAA,IAAA,OAAA,EAAM,WAAU,yCAAwC,UAAuB,2BAAA;AAAA,YAChFA,kCAAA;AAAA,cAAC;AAAA,cAAA;AAAA,gBACK,MAAK;AAAA,gBACL,KAAK;AAAA,gBACL,OACM,gBAAgB,YAAY,SACpB,gBAAgB,UAChB,OAAO,WAAW;AAAA,gBAEhC,WAAW,CAAC,MAAM;AACZ,sBAAI,EAAE,QAAQ,OAAO,EAAE,QAAQ,KAAK;AAC9B,sBAAE,eAAe;AAAA,kBAAA;AAAA,gBAE7B;AAAA,gBACA,UAAU,CAAC,MAAM;AACX,wBAAM,QAAQ,WAAW,EAAE,OAAO,KAAK;AACvC,sBAAI,SAAS,GAAG;AACI,kCAAA,YAAY,WAAW,KAAK;AAClC,4BAAA,IAAI,kBAAkB,KAAK,UAAU;AAAA,kBAAA;AAAA,gBAEzD;AAAA,gBACA,UAAU,CAAC;AAAA,gBACX,WAAU;AAAA,cAAA;AAAA,YAAA;AAAA,UAChB,EACN,CAAA;AAAA,QAAA,EACN,CAAA;AAAA,MAAA,GACN;AAAA,MACC,qBACKD,kCAAA,KAAC,OAAI,EAAA,WAAU,kCACT,UAAA;AAAA,QAACC,kCAAAA,IAAA,UAAA,EAAO,SAAS,MAAM,oBAAoB,UAAU,GAAG,WAAU,+BAA8B,UAEhG,SAAA,CAAA;AAAA,QACAA,kCAAAA,IAAC,YAAO,SAAS,MAAM,kBAAkB,UAAU,GAAG,WAAU,8CAA6C,UAE7G,OAAA,CAAA;AAAA,MAAA,EACN,CAAA;AAAA,IAAA,GAEZ;AAAA,IAGAD,kCAAAA,KAAC,OAAI,EAAA,WAAU,uEACT,UAAA;AAAA,MAACA,kCAAAA,KAAA,OAAA,EAAI,WAAU,wBACT,UAAA;AAAA,QAACC,kCAAA,IAAA,OAAA,EAAI,WAAU,6CAA4C,UAAuB,2BAAA;AAAA,QAClFA,kCAAA;AAAA,UAAC;AAAA,UAAA;AAAA,YACK,SAAS,MAAM;AACT,kBAAI,CAAC,mBAAmB;AACC,mCAAA;AAAA,kBACb,aAAa,OAAO;AAAA,kBACpB,0BAA0B,OAAO;AAAA,kBACjC,WAAW,OAAO;AAAA,gBAAA,CACvB;AACD,wBAAQ,IAAI,iCAAiC;AAAA,cAAA;AAE9B,mCAAA,CAAA,SAAQ,CAAC,IAAI;AAAA,YACxC;AAAA,YACA,WAAU;AAAA,YAET,8BAEWD,kCAAAA,KAAAM,kBAAA,UAAA,EAAA,UAAA;AAAA,cAACL,kCAAAA,IAAA,GAAA,EAAE,WAAU,UAAU,CAAA;AAAA,cAAE;AAAA,YAAA,EAAA,CAC/B,IAGMD,kCAAAA,KAAAM,kBAAA,UAAA,EAAA,UAAA;AAAA,cAACL,kCAAAA,IAAAM,WAAA,EAAK,WAAU,UAAU,CAAA;AAAA,cAAE;AAAA,YAAA,EAClC,CAAA;AAAA,UAAA;AAAA,QAAA;AAAA,MAEZ,GACN;AAAA,MACAP,kCAAAA,KAAC,OAAI,EAAA,WAAU,2BACT,UAAA;AAAA,QAACC,kCAAA,IAAA,OAAA,EAAM,WAAU,yCAAwC,UAAY,gBAAA;AAAA,QACrEA,kCAAA;AAAA,UAAC;AAAA,UAAA;AAAA,YACK,MAAK;AAAA,YACL,OAAO,OAAO,OAAO,WAAW;AAAA,YAChC,UAAQ;AAAA,YACR,WAAU;AAAA,UAAA;AAAA,QAAA;AAAA,MAChB,GACN;AAAA,MACC,qBAAqB,IAAI,CAAC,EAAE,OAAO,KAAK,MAAM,aAAa;AAC9E,cAAM,IAAI;AACW,4BACZ,gBAAgB,CAAC,MAAM,SAAY,gBAAgB,CAAC,IAAI,OAAO,CAAC,IACjE,OAAO,CAAC;AAEV,eAAAD,kCAAA,KAAC,OAAc,EAAA,WAAU,mCACnB,UAAA;AAAA,UAACC,kCAAA,IAAA,OAAA,EAAM,WAAU,yCAAyC,UAAM,OAAA;AAAA,UAChED,kCAAAA,KAAC,QAAK,EAAA,WAAU,yCACT,UAAA;AAAA,YAAA;AAAA,YACDC,kCAAA;AAAA,cAAC;AAAA,cAAA;AAAA,gBACK;AAAA,gBACA,OAAO,OAAO,oBAAqB,gBAAgB,CAAC,KAAK,OAAO,CAAC,IAAK,OAAO,CAAC,KAAK,EAAE;AAAA,gBACrF,UAAU,CAAC,MAAM;AACX,sBAAI,mBAAmB;AACb,wBAAA,QAAQ,EAAE,OAAO;AAErB,wBAAI,QAAQ,mBAAmB;AAEjB,8BAAA,MAAM,QAAQ,YAAY,EAAE;AAEhC,0BAAA,MAAM,OAAO,KAAK;AAClB,0BAAA,MAAM,IAAW,OAAA;AACjB,0BAAA,MAAM,EAAS,OAAA;AACnB,8BAAQ,UAAU,KAAK,KAAK,OAAO,GAAG;AAAA,oBAAA;AAE9B,kCAAA,YAAY,GAAG,KAAK;AAClC,4BAAQ,IAAI,sBAAsB,GAAG,WAAW,KAAK,EAAE;AAAA,kBAAA;AAAA,gBAEnE;AAAA,gBACA,UAAU,CAAC;AAAA,gBACX,KAAK,QAAQ,oBAAoB,IAAI;AAAA,gBACrC,KAAK,QAAQ,oBAAoB,MAAM;AAAA,gBACvC,WAAU;AAAA,cAAA;AAAA,YAAA;AAAA,UAChB,EACN,CAAA;AAAA,QAAA,EAAA,GA7BI,GA8BV;AAAA,MAAA,CAEX;AAAA,MACwB,0BAA0B,IAAI,CAAC,EAAE,OAAO,UAAU;AAC7C,cAAM,IAAI;AACV,cAAM,eAAe,oBACZ,gBAAgB,CAAC,MAAM,SAAY,gBAAgB,CAAC,IAAI,OAAO,CAAC,IACjE,OAAO,CAAC;AAEV,eAAAD,kCAAA,KAAC,OAAc,EAAA,WAAU,mCACnB,UAAA;AAAA,UAACC,kCAAA,IAAA,OAAA,EAAM,WAAU,yCAAyC,UAAM,OAAA;AAAA,UAChED,kCAAA;AAAA,YAAC;AAAA,YAAA;AAAA,cACK,OAAO,eAAe,QAAQ;AAAA,cAC9B,eAAe,CAAC,QAAQ;AAClB,oBAAI,mBAAmB;AACH,gCAAA,YAAY,GAAG,QAAQ,KAAK;AAC1C,0BAAQ,IAAI,sBAAsB,GAAG,WAAW,QAAQ,KAAK,EAAE;AAAA,gBAAA;AAAA,cAE3E;AAAA,cACA,UAAU,CAAC;AAAA,cACX,WAAU;AAAA,cAEV,UAAA;AAAA,gBAACA,kCAAAA,KAAA,OAAA,EAAI,WAAU,2BACT,UAAA;AAAA,kBAAAC,sCAAC,kBAAe,IAAI,GAAG,GAAG,QAAQ,OAAM,OAAM;AAAA,wDAC7C,OAAM,EAAA,SAAS,GAAG,GAAG,QAAQ,UAAG,MAAA,CAAA;AAAA,gBAAA,GACvC;AAAA,gBACAD,kCAAAA,KAAC,OAAI,EAAA,WAAU,2BACT,UAAA;AAAA,kBAAAC,sCAAC,kBAAe,IAAI,GAAG,GAAG,OAAO,OAAM,MAAK;AAAA,wDAC3C,OAAM,EAAA,SAAS,GAAG,GAAG,OAAO,UAAE,KAAA,CAAA;AAAA,gBAAA,EACrC,CAAA;AAAA,cAAA;AAAA,YAAA;AAAA,UAAA;AAAA,QACN,EAAA,GArBI,GAsBV;AAAA,MAAA,CAEX;AAAA,MACA,qBACKD,kCAAA,KAAC,OAAI,EAAA,WAAU,6BACT,UAAA;AAAA,QAACC,kCAAAA,IAAA,UAAA,EAAO,SAAS,MAAM,oBAAoB,UAAU,GAAG,WAAU,+BAA8B,UAEhG,SAAA,CAAA;AAAA,QACAA,kCAAAA,IAAC,YAAO,SAAS,MAAM,kBAAkB,UAAU,GAAG,WAAU,8CAA6C,UAE7G,OAAA,CAAA;AAAA,MAAA,EACN,CAAA;AAAA,IAAA,GAEZ;AAAA,IAGAD,kCAAAA,KAAC,OAAI,EAAA,WAAU,uEACT,UAAA;AAAA,MAACA,kCAAAA,KAAA,OAAA,EAAI,WAAU,wBACT,UAAA;AAAA,QAACC,kCAAA,IAAA,OAAA,EAAI,WAAU,6CAA4C,UAAwB,4BAAA;AAAA,QACnFA,kCAAA;AAAA,UAAC;AAAA,UAAA;AAAA,YACK,SAAS,MAAM;AACT,kBAAI,CAAC,oBAAoB;AACC,oCAAA;AAAA,kBACd,iBAAiB,OAAO;AAAA,kBACxB,qBAAqB,OAAO;AAAA,kBAC5B,uBAAuB,OAAO;AAAA,gBAAA,CACnC;AACD,wBAAQ,IAAI,kCAAkC;AAAA,cAAA;AAE9B,oCAAA,CAAA,SAAQ,CAAC,IAAI;AAAA,YACzC;AAAA,YACA,WAAU;AAAA,YAET,+BAEWD,kCAAAA,KAAAM,kBAAA,UAAA,EAAA,UAAA;AAAA,cAACL,kCAAAA,IAAA,GAAA,EAAE,WAAU,UAAU,CAAA;AAAA,cAAE;AAAA,YAAA,EAAA,CAC/B,IAGMD,kCAAAA,KAAAM,kBAAA,UAAA,EAAA,UAAA;AAAA,cAACL,kCAAAA,IAAAM,WAAA,EAAK,WAAU,UAAU,CAAA;AAAA,cAAE;AAAA,YAAA,EAClC,CAAA;AAAA,UAAA;AAAA,QAAA;AAAA,MAEZ,GACN;AAAA,MAEAP,kCAAAA,KAAC,OAAI,EAAA,WAAU,mCACT,UAAA;AAAA,QAACC,kCAAA,IAAA,OAAA,EAAM,WAAU,yCAAwC,UAAiB,qBAAA;AAAA,QAC1EA,kCAAA;AAAA,UAAC;AAAA,UAAA;AAAA,YACK,MAAK;AAAA,YACL,OAAO,iBAAiB,oBAAoB,SAAa,iBAAiB,mBAAmB,KAAO,OAAO,mBAAmB;AAAA,YAC9H,UAAU,CAAC,MAAM;AACX,kBAAI,oBAAoB;AAClB,8BAAc,aAAa,mBAAmB,EAAE,OAAO,KAAK;AAC5D,wBAAQ,IAAI,2BAA2B,EAAE,OAAO,KAAK,EAAE;AAAA,cAAA;AAAA,YAEnE;AAAA,YACA,UAAU,CAAC;AAAA,YACX,WAAU;AAAA,UAAA;AAAA,QAChB;AAAA,SACC,qDAAkB,oBACbA,kCAAA,IAAC,OAAE,WAAU,wBAAwB,2BAAiB,gBAAgB,CAAA;AAAA,MAAA,GAElF;AAAA,MAEAD,kCAAAA,KAAC,OAAI,EAAA,WAAU,mCACT,UAAA;AAAA,QAACC,kCAAA,IAAA,OAAA,EAAM,WAAU,yCAAwC,UAAsB,0BAAA;AAAA,QAC/ED,kCAAA;AAAA,UAAC;AAAA,UAAA;AAAA,YACK,OAAO,iBAAiB,wBAAwB,SAAY,iBAAiB,sBAAsB,OAAO;AAAA,YAC1G,UAAU,CAAC,MAAM;AACX,kBAAI,oBAAoB;AAClB,8BAAc,aAAa,uBAAuB,EAAE,OAAO,KAAK;AAChE,wBAAQ,IAAI,gCAAgC,EAAE,OAAO,KAAK,EAAE;AAAA,cAAA;AAAA,YAExE;AAAA,YACA,UAAU,CAAC;AAAA,YACX,WAAU;AAAA,YAEV,UAAA;AAAA,cAACC,kCAAA,IAAA,UAAA,EAAO,OAAM,cAAa,UAAU,cAAA;AAAA,cACpCA,kCAAA,IAAA,UAAA,EAAO,OAAM,SAAQ,UAAK,QAAA,CAAA;AAAA,YAAA;AAAA,UAAA;AAAA,QACjC;AAAA,SACC,qDAAkB,wBACbA,kCAAA,IAAC,OAAE,WAAU,wBAAwB,2BAAiB,oBAAoB,CAAA;AAAA,MAAA,GAEtF;AAAA,MAEAD,kCAAAA,KAAC,OAAI,EAAA,WAAU,mCACT,UAAA;AAAA,QAACC,kCAAA,IAAA,OAAA,EAAM,WAAU,yCAAwC,UAAwB,4BAAA;AAAA,QACjFD,kCAAA;AAAA,UAAC;AAAA,UAAA;AAAA,YACK,OAAO,iBAAiB,0BAA0B,SAAY,iBAAiB,wBAAwB,OAAO;AAAA,YAC9G,UAAU,CAAC,MAAM;AACX,kBAAI,oBAAoB;AAClB,8BAAc,aAAa,yBAAyB,EAAE,OAAO,KAAK;AAClE,wBAAQ,IAAI,kCAAkC,EAAE,OAAO,KAAK,EAAE;AAAA,cAAA;AAAA,YAE1E;AAAA,YACA,UAAU,CAAC;AAAA,YACX,WAAU;AAAA,YAEV,UAAA;AAAA,cAACC,kCAAA,IAAA,UAAA,EAAO,OAAM,QAAO,UAAI,QAAA;AAAA,cACxBA,kCAAA,IAAA,UAAA,EAAO,OAAM,QAAO,UAAI,QAAA;AAAA,cACxBA,kCAAA,IAAA,UAAA,EAAO,OAAM,SAAQ,UAAK,QAAA,CAAA;AAAA,YAAA;AAAA,UAAA;AAAA,QACjC;AAAA,SACC,qDAAkB,0BACbA,kCAAA,IAAC,OAAE,WAAU,wBAAwB,2BAAiB,sBAAsB,CAAA;AAAA,MAAA,GAExF;AAAA,MAEC,sBACKD,kCAAA,KAAC,OAAI,EAAA,WAAU,6BACT,UAAA;AAAA,QAACC,kCAAAA,IAAA,UAAA,EAAO,SAAS,MAAM,oBAAoB,WAAW,GAAG,WAAU,+BAA8B,UAEjG,SAAA,CAAA;AAAA,QACAA,kCAAAA,IAAC,YAAO,SAAS,MAAM,kBAAkB,WAAW,GAAG,WAAU,8CAA6C,UAE9G,OAAA,CAAA;AAAA,MAAA,EACN,CAAA;AAAA,IAAA,GAEZ;AAAA,IAIAD,kCAAAA,KAAC,OAAI,EAAA,WAAU,uEACT,UAAA;AAAA,MAACA,kCAAAA,KAAA,OAAA,EAAI,WAAU,wBACT,UAAA;AAAA,QAACC,kCAAA,IAAA,OAAA,EAAI,WAAU,6CAA4C,UAAwB,4BAAA;AAAA,QACnFA,kCAAA;AAAA,UAAC;AAAA,UAAA;AAAA,YACK,SAAS,MAAM;AACT,kBAAI,CAAC,oBAAoB;AACC,oCAAA;AAAA,kBACd,eAAe,OAAO;AAAA,kBACtB,gBAAgB,OAAO;AAAA,kBACvB,uBAAuB,OAAO;AAAA,kBAC9B,mBAAmB,OAAO;AAAA,kBAC1B,wBAAwB,OAAO;AAAA,kBAC/B,mBAAmB,OAAO;AAAA,kBAC1B,mBAAmB,OAAO;AAAA,kBAC1B,sBAAsB,OAAO;AAAA,kBAC7B,qBAAqB,OAAO;AAAA,kBAC5B,gBAAgB,OAAO;AAAA,kBACvB,WAAW,OAAO;AAAA,kBAClB,kBAAkB,OAAO;AAAA,gBAAA,CAC9B;AACD,wBAAQ,IAAI,kCAAkC;AAAA,cAAA;AAE9B,oCAAA,CAAA,SAAQ,CAAC,IAAI;AAAA,YACzC;AAAA,YACA,WAAU;AAAA,YAET,+BAEWD,kCAAAA,KAAAM,kBAAA,UAAA,EAAA,UAAA;AAAA,cAACL,kCAAAA,IAAA,GAAA,EAAE,WAAU,UAAU,CAAA;AAAA,cAAE;AAAA,YAAA,EAAA,CAC/B,IAGMD,kCAAAA,KAAAM,kBAAA,UAAA,EAAA,UAAA;AAAA,cAACL,kCAAAA,IAAAM,WAAA,EAAK,WAAU,UAAU,CAAA;AAAA,cAAE;AAAA,YAAA,EAClC,CAAA;AAAA,UAAA;AAAA,QAAA;AAAA,MAEZ,GACN;AAAA,MACC,2BAA2B,IAAI,CAAC,EAAE,OAAO,UAAU;AAC9C,cAAM,IAAI;AACV,cAAM,eAAe,qBACZ,iBAAiB,CAAC,MAAM,SAAY,iBAAiB,CAAC,IAAI,OAAO,CAAC,IACnE,OAAO,CAAC;AAEV,eAAAP,kCAAA,KAAC,OAAc,EAAA,WAAU,mCACnB,UAAA;AAAA,UAACC,kCAAA,IAAA,OAAA,EAAM,WAAU,yCAAyC,UAAM,OAAA;AAAA,UAChED,kCAAA;AAAA,YAAC;AAAA,YAAA;AAAA,cACK,OAAO,eAAe,CAAC,eAAe,EAAE,SAAS,GAAG,IAAI,OAAO,QAAQ,CAAC,eAAe,EAAE,SAAS,GAAG,IAAI,QAAQ;AAAA,cACjH,eAAe,CAAC,QAAQ;AAClB,oBAAI,oBAAoB;AAClB,sBAAI,MAAM,iBAAiB;AACP,kCAAA,aAAa,iBAAiB,QAAQ,IAAI;AACxD,4BAAQ,IAAI,uBAAuB,GAAG,WAAW,QAAQ,IAAI,EAAE;AAAA,kBAAA,OAC9D;AACa,kCAAA,aAAa,GAAG,QAAQ,KAAK;AAC3C,4BAAQ,IAAI,uBAAuB,GAAG,WAAW,QAAQ,KAAK,EAAE;AAAA,kBAAA;AAAA,gBACtE;AAAA,cAEZ;AAAA,cACA,UAAU,CAAC;AAAA,cACX,WAAU;AAAA,cAEV,UAAA;AAAA,gBAACA,kCAAAA,KAAA,OAAA,EAAI,WAAU,2BACT,UAAA;AAAA,kBAAAC,sCAAC,kBAAe,IAAI,GAAG,GAAG,QAAQ,OAAM,OAAM;AAAA,wDAC7C,OAAM,EAAA,SAAS,GAAG,GAAG,QAAQ,UAAG,MAAA,CAAA;AAAA,gBAAA,GACvC;AAAA,gBACAD,kCAAAA,KAAC,OAAI,EAAA,WAAU,2BACT,UAAA;AAAA,kBAAAC,sCAAC,kBAAe,IAAI,GAAG,GAAG,OAAO,OAAM,MAAK;AAAA,wDAC3C,OAAM,EAAA,SAAS,GAAG,GAAG,OAAO,UAAE,KAAA,CAAA;AAAA,gBAAA,EACrC,CAAA;AAAA,cAAA;AAAA,YAAA;AAAA,UAAA;AAAA,QACN,EAAA,GA1BI,GA2BV;AAAA,MAAA,CAEX;AAAA,MACDD,kCAAAA,KAAC,OAAI,EAAA,WAAU,mCACT,UAAA;AAAA,QAACC,kCAAA,IAAA,OAAA,EAAM,WAAU,yCAAwC,UAAe,mBAAA;AAAA,QACxED,kCAAA;AAAA,UAAC;AAAA,UAAA;AAAA,YACK,OAAO,qBACE,iBAAiB,mBAAmB,SAAY,OAAO,iBAAiB,cAAc,IAAI,OAAO,OAAO,cAAc,IACvH,OAAO,OAAO,cAAc;AAAA,YACpC,eAAe,CAAC,QAAQ;AAClB,kBAAI,oBAAoB;AAClB,8BAAc,aAAa,kBAAkB,OAAO,GAAG,CAAC;AAChD,wBAAA,IAAI,6CAA6C,GAAG,EAAE;AAAA,cAAA;AAAA,YAE1E;AAAA,YACA,UAAU,CAAC;AAAA,YACX,WAAU;AAAA,YAEV,UAAA;AAAA,cAACA,kCAAAA,KAAA,OAAA,EAAI,WAAU,2BACT,UAAA;AAAA,gBAAAC,kCAAA,IAAC,gBAAe,EAAA,IAAG,oBAAmB,OAAM,KAAI;AAAA,gBAC/CA,kCAAA,IAAA,OAAA,EAAM,SAAQ,oBAAmB,UAAa,gBAAA,CAAA;AAAA,cAAA,GACrD;AAAA,cACAD,kCAAAA,KAAC,OAAI,EAAA,WAAU,2BACT,UAAA;AAAA,gBAAAC,kCAAA,IAAC,gBAAe,EAAA,IAAG,oBAAmB,OAAM,KAAI;AAAA,gBAC/CA,kCAAA,IAAA,OAAA,EAAM,SAAQ,oBAAmB,UAAO,UAAA,CAAA;AAAA,cAAA,GAC/C;AAAA,cACAD,kCAAAA,KAAC,OAAI,EAAA,WAAU,2BACT,UAAA;AAAA,gBAAAC,kCAAA,IAAC,gBAAe,EAAA,IAAG,oBAAmB,OAAM,KAAI;AAAA,gBAC/CA,kCAAA,IAAA,OAAA,EAAM,SAAQ,oBAAmB,UAAO,UAAA,CAAA;AAAA,cAAA,GAC/C;AAAA,cACAD,kCAAAA,KAAC,OAAI,EAAA,WAAU,2BACT,UAAA;AAAA,gBAAAC,kCAAA,IAAC,gBAAe,EAAA,IAAG,oBAAmB,OAAM,KAAI;AAAA,gBAC/CA,kCAAA,IAAA,OAAA,EAAM,SAAQ,oBAAmB,UAAO,UAAA,CAAA;AAAA,cAAA,EAC/C,CAAA;AAAA,YAAA;AAAA,UAAA;AAAA,QAAA;AAAA,MACN,GACN;AAAA,MACC,sBAAsB,IAAI,CAAC,EAAE,OAAO,KAAK,MAAM,QAAQ,QAAQ,cAAc;AACxE,cAAM,IAAI;AACW,6BACZ,iBAAiB,CAAC,MAAM,SAAY,iBAAiB,CAAC,IAAI,OAAO,CAAC,IACnE,OAAO,CAAC;AAGV,eAAAD,kCAAA,KAAC,OAAc,EAAA,WAAU,mCACnB,UAAA;AAAA,UAACC,kCAAA,IAAA,OAAA,EAAM,WAAU,yCAAyC,UAAM,OAAA;AAAA,UAChED,kCAAAA,KAAC,QAAK,EAAA,WAAU,yCACT,UAAA;AAAA,YAAA;AAAA,YACA,SAAS,WACJA,kCAAA;AAAA,cAAC;AAAA,cAAA;AAAA,gBACK,OAAO,OAAO,qBAAsB,iBAAiB,CAAC,KAAK,OAAO,CAAC,IAAK,OAAO,CAAC,KAAK,EAAE;AAAA,gBACvF,UAAU,CAAC,MAAM;AACX,sBAAI,oBAAoB;AAClB,kCAAc,aAAa,GAAG,EAAE,OAAO,KAAK;AAC5C,4BAAQ,IAAI,uBAAuB,GAAG,WAAW,EAAE,OAAO,KAAK,EAAE;AAAA,kBAAA;AAAA,gBAE7E;AAAA,gBACA,UAAU,CAAC;AAAA,gBACX,WAAU;AAAA,gBAEV,UAAA;AAAA,kBAAAC,sCAAC,UAAO,EAAA,OAAM,IAAG,QAAM,MAAC,UAExB,oBAAA;AAAA,kBACC,mCAAS,IAAI,CAAC,WACRA,kCAAAA,IAAA,UAAA,EAA0B,OAAO,OAAO,OAClC,UAAA,OAAO,MADD,GAAA,OAAO,KAEpB;AAAA,gBACL;AAAA,cAAA;AAAA,YAAA,IAGPA,kCAAA;AAAA,cAAC;AAAA,cAAA;AAAA,gBACK;AAAA,gBACA,OAAO,OAAO,qBAAsB,iBAAiB,CAAC,KAAK,OAAO,CAAC,IAAK,OAAO,CAAC,KAAK,EAAE;AAAA,gBACvF,UAAU,CAAC,MAAM;AACX,sBAAI,oBAAoB;AAClB,kCAAc,aAAa,GAAG,EAAE,OAAO,KAAK;AAC5C,4BAAQ,IAAI,uBAAuB,GAAG,WAAW,EAAE,OAAO,KAAK,EAAE;AAAA,kBAAA;AAAA,gBAE7E;AAAA,gBACA,UAAU,CAAC;AAAA,gBACX,WAAU;AAAA,cAAA;AAAA,YAChB;AAAA,YAEL;AAAA,UAAA,EACP,CAAA;AAAA,QAAA,EAAA,GAxCI,GAyCV;AAAA,MAAA,CAEX;AAAA,MACA,sBACKD,kCAAA,KAAC,OAAI,EAAA,WAAU,6BACT,UAAA;AAAA,QAACC,kCAAAA,IAAA,UAAA,EAAO,SAAS,MAAM,oBAAoB,WAAW,GAAG,WAAU,+BAA8B,UAEjG,SAAA,CAAA;AAAA,QACAA,kCAAAA,IAAC,YAAO,SAAS,MAAM,kBAAkB,WAAW,GAAG,WAAU,8CAA6C,UAE9G,OAAA,CAAA;AAAA,MAAA,EACN,CAAA;AAAA,IAAA,EAEZ,CAAA;AAAA,EAAA,GACN;AAEZ;ACh2CA,MAAM,eAA4C,CAAC;AAAA,EAC7C;AAAA,EACA;AAAA,EACA;AAAA,EACA,iBAAAQ;AAAA,EACA,eAAAC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACN,MAAM;;AACA,QAAM,CAAC,WAAW,YAAY,IAAIP,aAAAA,SAAS,KAAK;AAChD,QAAM,CAAC,UAAU,WAAW,IAAIA,aAAAA,SAAwB,IAAI;AAC5D,QAAM,CAAC,WAAW,YAAY,IAAIA,aAAAA,SAAwB,IAAI;AAC9D,QAAM,CAAC,iBAAiB,kBAAkB,IAAIA,sBAA0B;AAAA,IAClE,SAAS;AAAA,IACT,QAAQ;AAAA,IACR,qBAAqB;AAAA,EAAA,CAC1B;AACK,QAAA,CAAC,iBAAiB,kBAAkB,IAAIA,aAAA,SAA0B,EAAE,UAAU,MAAM,WAAW,MAAM;AAC3G,QAAM,CAAC,YAAY,aAAa,IAAIA,aAAAA,SAAS,KAAK;AAClD,QAAM,CAAC,sBAAsB,uBAAuB,IAAIA,aAAAA,SAAS,KAAK;AACtE,QAAM,CAAC,eAAe,gBAAgB,IAAIA,aAAAA,SAAS,EAAE;AACrD,QAAM,CAAC,kBAAkB,mBAAmB,IAAIA,aAAAA,SAA6B,CAAA,CAAE;AAC/E,QAAM,CAAC,eAAe,gBAAgB,IAAIA,aAAAA,SAA6B,CAAA,CAAE;AACzE,MAAI,YAAsB,CAAC;AAC3B,2DAAoB,QAAQ,CAAC,SAAS,UAAU,KAAK,KAAK,KAAK;AAC/D,QAAM,CAAC,kBAAkB,mBAAmB,IAAIA,aAAAA,SAAS,EAAE;AAC3D,QAAM,sBAAsBQ,aAAA,YAAY,CAClC,KACA,MACAC,gBACD;AACC,QAAI,OAAO,MAAM;AACX,yBAAmB,EAAE,UAAU,KAAK,WAAW,MAAM;AACrD,oBAAcA,WAAU;AAAA,IAAA,OAEzB;AACC,YAAM,+DAA+D;AAAA,IAAA;AAAA,EAEjF,GAAG,EAAE;AAGC,QAAA,eAAe,CAAC,WAAmB;AAEnC,iBAAa,MAAM;AAAA,EACzB;AACA,QAAM,2BAA2BD,aAAA;AAAA,IAAY,CAAC,UAAmB;AAC3D,8BAAwB,KAAK;AAAA,IACnC;AAAA,IAAG,CAAA;AAAA,EACH;AAEA,QAAM,gCAAgCA,aAAA,YAAY,OAAOE,SAAgB,OAAe,aAAqB;AACnG,QAAA,CAAC,SAAS,CAAC,UAAU;AACnB,YAAM,kDAAkD;AACxD;AAAA,IAAA;AAGF,QAAA;AACQ,YAAA,WAAW,MAAM,MAAM,iCAAiCA,OAAM,UAAU,KAAK,aAAa,QAAQ,EAAE;AAEpG,YAAA,OAAO,MAAM,SAAS,KAAK;AAC7B,UAAA,CAAC,SAAS,IAAI;AACZ,cAAM,IAAI,MAAM,KAAK,SAAS,eAAe;AAAA,MAAA;AAE/B,0BAAA,KAAK,iBAAiB,IAAI;AAAA,aAC3C,OAAO;AACF,cAAA,MAAM,qCAAqC,KAAK;AACxD,YAAM,sDAAsD;AAAA,IAAA;AAAA,EAExE,GAAG,EAAE;AAEL,QAAM,CAAC,oBAAoB,qBAAqB,IAAIV,aAAAA,SAA6B,CAAA,CAAE;AAEnF,QAAM,CAAC,oBAAoB,qBAAqB,IAAIA,aAAAA,SAAwB,IAAI;AAChF,QAAM,sBAAsBQ,aAAA,YAAY,CAAC,IAAY,iBAAmC,gBAAyB;AAC3G,qBAAiB,CAAC,cAAc;AACtB,UAAA;AACJ,UAAI,UAAU,KAAK,CAAC,QAAQ,IAAI,OAAO,EAAE,GAAG;AACtC,4BAAoB,UAAU,OAAO,CAAC,QAAQ,IAAI,OAAO,EAAE;AAAA,MAAA,OAC1D;AACmB,4BAAA,CAAC,GAAG,WAAW,eAAe;AAAA,MAAA;AAGxD,4BAAsB,iBAAiB;AAChC,aAAA;AAAA,IAAA,CACZ;AAAA,EACP,GAAG,EAAE;AAkBL,QAAM,UAAU,WAAW;AACrB,QAAA,YAAY,QAAQ,UAAU;AACpC,QAAM,yBAAyB,MAAM;AAC/B,iBAAa,kBAAkB;AAC/B,4BAAwB,KAAK;AAC7B,wBAAoB,EAAE;AACtB,qBAAiB,EAAE;AACnB,gBAAY,CAAC;AACb,wBAAoB,CAAA,CAAE;AACtB,0BAAsB,IAAI;AAC1B,0BAAsB,CAAA,CAAE;AAAA,EAE9B;AAGA,QAAM,oBAAoBA,aAAA;AAAA,IACpB,CAAC,IACK,gBACD;AACC,yBAAmB,CAAC,cAAc;AAC5B,YAAI,UAAU,SAAS;AACb,cAAA,UAAU,WAAW,IAAI;AAEvB,mBAAO,EAAE,QAAQ,MAAM,SAAS,OAAO,qBAAqB,KAAK;AAAA,UAAA,OAChE;AAED,mBAAO,EAAE,QAAQ,IAAI,SAAS,MAAM,qBAAqB,YAAY;AAAA,UAAA;AAAA,QAC3E,OACC;AAED,iBAAO,EAAE,QAAQ,IAAI,SAAS,MAAM,qBAAqB,YAAY;AAAA,QAAA;AAAA,MAC3E,CACL;AAAA,IACP;AAAA,IACA,CAAA;AAAA,EACN;AAIA,+CACO,YAAW,EAAA,kBAEN,UAACX,uCAAA,OAAA,EAAI,WAAU,kEACT,UAAA;AAAA,IAACC,kCAAAA,IAAA,OAAA,EAAI,WAAU,6EACR,UAAA,YAAY,SAEP,YAAY,IAAI,CAAC,QAAQ,UAAU;AAGvB,aAAAD,kCAAA,KAAC,OAA8B,EAAA,WAAU,6DACnC,UAAA;AAAA,QAACA,kCAAAA,KAAA,OAAA,EAAI,WAAU,2CACT,UAAA;AAAA,UAAAA,uCAAC,OACK,EAAA,UAAA;AAAA,YAACA,kCAAAA,KAAA,OAAA,EAAI,WAAU,+BAA8B,UAAA;AAAA,cAAA;AAAA,cAAK,OAAO;AAAA,YAAA,GAAa;AAAA,kDACrE,OAAI,EAAA,WAAU,+BAA+B,UAAA,OAAO,KAAK,KAAK,CAAA;AAAA,UAAA,GACrE;AAAA,UACAC,kCAAAA,IAAC,QAAO,EAAA,MAAM,QAAQ,MAAMQ,iBAAgB,KAAK,GAAG,OAAOA,iBAAgB,KAAK,EAAG,CAAA;AAAA,QAAA,GACzF;AAAA,QAGAR,kCAAA;AAAA,UAAC;AAAA,UAAA;AAAA,YACK,SAAQ;AAAA,YACR,MAAK;AAAA,YACL,WAAU;AAAA,YACV,SAAS,MAAM;AACL,kBAAA,QAAQ,4CAA4C,GAAG;AACrD,6BAAa,iCAAQ,YAAY;AAAA,cAAA;AAAA,YAE7C;AAAA,YACA,OAAO,EAAE,WAAW,WAAW;AAAA,YAE/B,UAAAA,kCAAAA,IAAC,QAAO,EAAA,MAAM,GAAI,CAAA;AAAA,UAAA;AAAA,QAAA;AAAA,MACxB,EAAA,GAtBI,OAAO,YAyBjB;AAAA,IAAA,CAEX,IAEDA,kCAAAA,IAAC,SAAI,WAAU,oBAAmB,oCAAuB,CAAA,GAErE;AAAA,IACAD,kCAAAA,KAAC,OAAI,EAAA,WAAU,mCACT,UAAA;AAAA,MAACA,kCAAAA,KAAA,OAAA,EAAI,WAAU,yEAER,UAAA;AAAA,QAAA,CAAC,uBAAwBC,kCAAAA,IAAC,OAAI,EAAA,WAAU,+EACnC,UAAAA,kCAAA;AAAA,UAAC;AAAA,UAAA;AAAA,YACK,SAAS,MAAM;AAAE,uCAAyB,IAAI;AAAA,YAAG;AAAA,YACtD,UAAA;AAAA,UAAA;AAAA,QAAA,EAGP,CAAA,IAAYD,kCAAAA,KAAA,OAAA,EAAI,WAAU,qEAEpB,UAAA;AAAA,UAACA,kCAAAA,KAAA,OAAA,EAAI,WAAU,+BAA8B,UAAA;AAAA,YAAA;AAAA,YAEvCC,sCAAC,UAAO,EAAA,SAAS,MAAM;AAEjB,uCAAyB,KAAK;AAAA,YAAA,GACjC,UAAAA,kCAAAA,IAAC,GAAE,EAAA,MAAM,IAAI,EAAE,CAAA;AAAA,UAAA,GACxB;AAAA,UACAD,kCAAAA,KAAC,OAAI,EAAA,WAAU,sCACT,UAAA;AAAA,YAAAA,kCAAA,KAAC,QAAO,EAAA,OAAO,eAAe,eAAe,kBACvC,UAAA;AAAA,cAAAC,kCAAAA,IAAC,iBAAc,WAAU,aACnB,gDAAC,aAAY,EAAA,aAAY,gBAAe,EAC9C,CAAA;AAAA,cACCA,kCAAA,IAAA,eAAA,EACM,UAAU,UAAA,IAAI,CAAC,UACTA,kCAAAA,IAAA,YAAA,EAAuB,OAAO,OAAQ,UAAtB,MAAA,GAAA,KAA4B,CAClD,EACP,CAAA;AAAA,YAAA,GACN;AAAA,mDACC,QAAO,EAAA,OAAO,kBAAkB,eAAe,CAAC,gBAAgB;AAC3D,kCAAoB,WAAW;AACD,4CAAA,QAAQ,eAAe,WAAW;AAAA,YAAA,GACnE,UAAU,CAAC,eACR,UAAA;AAAA,cAAAA,kCAAAA,IAAC,iBAAc,WAAU,aACnB,gDAAC,aAAY,EAAA,aAAY,mBAAkB,EACjD,CAAA;AAAA,cACAA,kCAAAA,IAAC,iBACM,UACO,yDAAA,OAAO,CAAC,QAAQ,IAAI,UAAU,eAC/B,IAAI,CAAC,aACAA,kCAAAA,IAAC,YAAmC,EAAA,OAAO,SAAS,UAC7C,mBAAS,SADC,GAAA,SAAS,QAE1B,GAElB,CAAA;AAAA,YAAA,EACN,CAAA;AAAA,UAAA,GACN;AAAA,UACAD,kCAAAA,KAAC,OAAI,EAAA,WAAU,uEACR,UAAA;AAAA,YAAA,oBAAoB,iBAAiB,SAAS,KACxC,iBAAiB,OAAO,OAAK,EAAC,2CAAa,KAAK,CAAA,OAAM,GAAG,KAAK,OAAO,EAAE,IAAG,EAAE,SAAS,IAAI,iBAAiB,OAAO,OAAK,EAAC,2CAAa,KAAK,CAAA,OAAM,GAAG,KAAK,OAAO,EAAE,IAAG,IAAI,kBAAkB,IAAI,CAAC,UAAU,UAE7LA,kCAAA,KAAAM,4BAAA,EAAA,UAAA;AAAA,cAACL,kCAAA,IAAA,OAAA,EACK,iDAAC,SAAM,EAAA,WAAU,8CAA6C,SAAS,YAAY,SAAS,EAAE,IACxF,UAAA;AAAA,gBAAAA,kCAAA;AAAA,kBAAC;AAAA,kBAAA;AAAA,oBACK,IAAI,YAAY,SAAS,EAAE;AAAA,oBAC3B,SAAS,cAAc,KAAK,CAAC,QAAQ,IAAI,OAAO,SAAS,EAAE;AAAA,oBAC3D,iBAAiB,MAAM,oBAAoB,SAAS,IAAI,QAAQ;AAAA,kBAAA;AAAA,gBACtE;AAAA,gBACCA,sCAAA,OAAA,EAAI,WAAU,uBACT,iDAAC,QAAM,EAAA,UAAA;AAAA,kBAAS,SAAA;AAAA,kBAAG;AAAA,kBAAI,SAAS;AAAA,gBAAA,EAAA,CAAK,EAE3C,CAAA;AAAA,cAAA,GACN,EAAA,GAXI,SAAS,EAYnB;AAAA,cACC,QAAQ,iBAAiB,SAAS,KAAOA,kCAAA,IAAA,OAAA,EAAI,WAAU,+BAA8B;AAAA,cAAQ;AAAA,YAAA,EAAC,CAAA,CAC1G,IAEAA,kCAAA,IAAA,OAAA,EAAI,UAAyB,6BAAA;AAAA,YAClC;AAAA,UAAA,GAAC;AAAA,UAGTA,kCAAA;AAAA,YAAC;AAAA,YAAA;AAAA,cACK,MAAK;AAAA,cACL,WAAU;AAAA,cACV,SAAS,MAAM,uBAAuB;AAAA,cACtC,UAAU,mBAAmB,WAAW;AAAA,cAGvC,sBAAY,kBAAkB;AAAA,YAAA;AAAA,UAAA;AAAA,QAGrC,GACN;AAAA,QAEC,CAAC,sBACIA,kCAAAA,IAAC,SAAI,WAAU,oGACT,gDAAC,UAAO,EAAA,SAAS,MAAM,wBAAwB,IAAI,GAAG,UAEtD,qBAAA,CAAA,EACN,CAAA,IAECD,kCAAA,KAAA,OAAA,EAAI,WAAU,+FACT,UAAA;AAAA,UAACA,kCAAAA,KAAA,OAAA,EAAI,WAAU,+BAA8B,UAAA;AAAA,YAAA;AAAA,YAEvCC,kCAAAA,IAAC,UAAO,EAAA,SAAS,MAAM,wBAAwB,KAAK,GAC9C,UAACA,kCAAA,IAAA,GAAA,EAAE,MAAM,GAAI,CAAA,EACnB,CAAA;AAAA,UAAA,GACN;AAAA,UACAD,kCAAAA,KAAC,OAAI,EAAA,WAAU,gDACT,UAAA;AAAA,YAAAC,kCAAAA,IAAC,OAAE,UAAW,cAAA,CAAA;AAAA,YACdA,kCAAA;AAAA,cAAC;AAAA,cAAA;AAAA,gBACK,MAAK;AAAA,gBACL,WAAU;AAAA,gBACV,UAAU,CAAC,MAAM,YAAY,WAAW,EAAE,OAAO,KAAK,CAAC;AAAA,cAAA;AAAA,YAAA;AAAA,UAC7D,GACN;AAAA,UACAD,kCAAAA,KAAC,OAAI,EAAA,WAAU,+BACT,UAAA;AAAA,YAAAC,kCAAAA,IAAC,OAAE,UAAY,eAAA,CAAA;AAAA,YACfA,kCAAA;AAAA,cAAC;AAAA,cAAA;AAAA,gBACK,MAAK;AAAA,gBACL,WAAU;AAAA,gBACV,UAAU,CAAC,MAAM,aAAa,WAAW,EAAE,OAAO,KAAK,CAAC;AAAA,cAAA;AAAA,YAAA;AAAA,UAC9D,GACN;AAAA,UACAA,kCAAA;AAAA,YAAC;AAAA,YAAA;AAAA,cACK,WAAU;AAAA,cACV,SAAS,MAAM,oBAAoB,YAAY,GAAG,aAAa,GAAG,IAAI;AAAA,cAC3E,UAAA;AAAA,YAAA;AAAA,UAAA;AAAA,QAED,EACN,CAAA;AAAA,MAAA,GAEZ;AAAA,MACC,CAAC,aACIA,kCAAAA,IAAC,OAAI,EAAA,WAAU,2CACT,UAAAA,kCAAA,IAAC,OAAI,EAAA,WAAU,UAAS,UAAA,iBAAc,CAAA,GAC5C;AAAA,MAIL,mBACKD,kCAAA;AAAA,QAAC;AAAA,QAAA;AAAA,UACK,mBAAmB,EAAE,OAAO,QAAQ,QAAQ,OAAO;AAAA,UACnD,QAAQ,EAAE,KAAK,SAAS,KAAK,QAAQ;AAAA,UACrC,MAAM;AAAA,UACN,QAAQ,CAAC,gBAAgB;AACnB,mBAAO,WAAW;AAClB,yBAAa,IAAI;AAAA,UACvB;AAAA,UACA,WAAW,MAAM;AACD,sBAAA;AACV,yBAAa,KAAK;AAAA,UACxB;AAAA,UACA,SAAS;AAAA,YACH,gBAAgB;AAAA,YAChB,mBAAmB;AAAA,YACnB,mBAAmB;AAAA,YACnB,gBAAgB;AAAA,YAChB,iBAAiB;AAAA,UACvB;AAAA,UAEC,UAAA;AAAA,YAAA,aAAa,gDAAa,OAAO,CAAC,QAAQ,aAAa,IAAI,IAAI,YAAY,OAA9D,mBAAkE,IAAI,CAAC,MAAM,UACrF,KAAK,KAAK,WAGJC,kCAAA;AAAA,cAAC;AAAA,cAAA;AAAA,gBAEK,OAAOS,eAAc,KAAK,KAAK,OAAO;AAAA,gBACtC,SAAS;AAAA,kBACH,WAAW,uBAAuB,YAAYD,iBAAgB,KAAK;AAAA,kBACnE,aAAa;AAAA,kBACb,aAAa,uBAAuB,YAAYA,iBAAgB,KAAK;AAAA,kBACrE,eAAe;AAAA,kBACf,cAAc;AAAA,kBACd,WAAW;AAAA,kBACX,UAAU;AAAA,kBACV,UAAU;AAAA,kBACV,QAAQ;AAAA,kBACR,WAAW;AAAA;AAAA,gBACjB;AAAA,gBACA,SAAS,MAAM,kBAAkB,KAAK,cAAc,IAAI;AAAA,cAAA;AAAA,cAdnD,KAAK;AAAA,YAkB3B,KAAK;AAAA,YAEL,YAAa,+CAAe,IAAI,CAAC,MAAM,UAClC,KAAK,WAGCR,kCAAA;AAAA,cAAC;AAAA,cAAA;AAAA,gBAEK,OAAOS,eAAc,KAAK,OAAO;AAAA,gBACjC,SAAS;AAAA,kBACH,WAAW;AAAA,kBACX,aAAa;AAAA,kBACb,aAAa;AAAA,kBACb,eAAe;AAAA,kBACf,cAAc;AAAA,kBACd,WAAW;AAAA,kBACX,UAAU;AAAA,kBACV,UAAU;AAAA,kBACV,QAAQ;AAAA,kBACR,WAAW;AAAA;AAAA,gBAAA;AAAA,cACjB;AAAA,cAbK,KAAK;AAAA,YAgB3B,KAAK;AAAA,YAIL,mBAAmB,gBAAgB,UAAU,gBAAgB,WAAW,gBAAgB,uBAG5EV,kCAAA,KAAAM,4BAAA,EAAA,UAAA;AAAA,cAAQ,QAAA,IAAI,6CAA6C,eAAe;AAAA,cACzEL,kCAAA;AAAA,gBAAC;AAAA,gBAAA;AAAA,kBACK,UAAU,iBAAiBS,iBAAc,2BAAgB,wBAAhB,mBAAqC,SAArC,mBAA2C,YAAW,EAAE,CAAC;AAAA,kBAClG,cAAc,MAAM,mBAAmB,EAAE,SAAS,OAAO,QAAQ,MAAM,qBAAqB,MAAM;AAAA,kBAClG,SAAS;AAAA,oBACH,gBAAgB;AAAA,oBAChB,UAAU;AAAA,oBACV,gBAAgB;AAAA,kBACtB;AAAA,kBAEA,UAAAV,kCAAA,KAAC,OAAI,EAAA,WAAU,wCACT,UAAA;AAAA,oBAACA,kCAAAA,KAAA,OAAA,EAAI,WAAU,yDACT,UAAA;AAAA,sBAACC,kCAAA,IAAA,MAAA,EAAG,WAAU,6CAA4C,UAAa,iBAAA;AAAA,sBACvEA,kCAAA;AAAA,wBAAC;AAAA,wBAAA;AAAA,0BAAO,WAAU;AAAA,0BACZ,SAAS,MAAM,mBAAmB,EAAE,SAAS,OAAO,QAAQ,MAAM,qBAAqB,MAAM;AAAA,0BAC7F,UAAAA,kCAAAA,IAAC,GAAE,EAAA,WAAU,UAAU,CAAA;AAAA,wBAAA;AAAA,sBAAA;AAAA,oBAC7B,GACN;AAAA,oBACAD,kCAAAA,KAAC,OAAI,EAAA,WAAU,mFACT,UAAA;AAAA,sBAAAA,uCAAC,KAAE,EAAA,UAAA;AAAA,wBAACC,kCAAA,IAAA,QAAA,EAAK,WAAU,yCAAwC,UAAQ,YAAA;AAAA,wBAAO;AAAA,yBAAE,qBAAgB,wBAAhB,mBAAqC;AAAA,sBAAA,GAAa;AAAA,6DAC7H,KAAE,EAAA,UAAA;AAAA,wBAACA,kCAAA,IAAA,QAAA,EAAK,WAAU,yCAAwC,UAAW,eAAA;AAAA,wBAAO;AAAA,yBAAE,2BAAgB,wBAAhB,mBAAqC,SAArC,mBAA2C;AAAA,sBAAA,EAAK,CAAA;AAAA,oBAAA,EACrI,CAAA;AAAA,kBAAA,EACN,CAAA;AAAA,gBAAA;AAAA,cACN;AAAA,oDACC,SACM,EAAA,UAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,oCAWP;AAAA,cAAQ;AAAA,YAAA,GAAC;AAAA,YAEpB,cAAc,gBAAgB,aAAa,QAAQ,gBAAgB,cAAc,QAC5EA,sCAAC,QAAO,EAAA,UAAU,EAAE,KAAK,gBAAgB,UAAU,KAAK,gBAAgB,YAAa,CAAA;AAAA,UAAA;AAAA,QAAA;AAAA,MAEjG,IAECA,kCAAA,IAAA,OAAA,EAAI,WAAU,2CACT,gDAAC,KAAE,EAAA,WAAU,gBAAe,UAAA,kCAA+B,CAAA,EACjE,CAAA;AAAA,IAAA,EAEZ,CAAA;AAAA,EAAA,EAAA,CACN,EACN,CAAA;AAEZ;ACzdA,SAAwB,cAAc,EAAE,eAAe,2BAA+C;AAEhG,QAAM,mBAAmB;AAAA,IACnB,EAAE,OAAO,UAAU,KAAK,YAAY,MAAM,QAAQ,QAAQ,GAAG;AAAA,IAC7D,EAAE,OAAO,kBAAkB,KAAK,YAAY,MAAM,UAAU,QAAQ,GAAG;AAAA,IACvE,EAAE,OAAO,eAAe,KAAK,gBAAgB,MAAM,QAAQ,QAAQ,GAAG;AAAA,IACtE,EAAE,OAAO,aAAa,KAAK,UAAU,MAAM,QAAQ,QAAQ,GAAG;AAAA,IAC9D,EAAE,OAAO,gBAAgB,KAAK,UAAU,MAAM,QAAQ,QAAQ,GAAG;AAAA,IACjE,EAAE,OAAO,aAAa,KAAK,UAAU,MAAM,QAAQ,QAAQ,GAAG;AAAA,IAC9D,EAAE,OAAO,yBAAyB,KAAK,cAAc,MAAM,YAAY,QAAQ,GAAG;AAAA,EACxF;AACA,QAAM,sBAAsB;AAAA,IAEtB,EAAE,OAAO,qCAAqC,KAAK,aAAa,MAAM,UAAU,QAAQ,IAAI;AAAA,IAC5F,EAAE,OAAO,2CAA2C,KAAK,kBAAkB,MAAM,UAAU,QAAQ,IAAI;AAAA,IACvG,EAAE,OAAO,4CAA4C,KAAK,gBAAgB,MAAM,UAAU,QAAQ,IAAI;AAAA,IACtG,EAAE,OAAO,kCAAkC,KAAK,eAAe,MAAM,UAAU,QAAQ,IAAI;AAAA,IAC3F,EAAE,OAAO,+BAA+B,KAAK,gBAAgB,MAAM,UAAU,QAAQ,IAAI;AAAA,IACzF,EAAE,OAAO,uCAAuC,KAAK,oBAAoB,MAAM,UAAU,QAAQ,IAAI;AAAA,IACrG,EAAE,OAAO,yCAAyC,KAAK,kBAAkB,MAAM,UAAU,QAAQ,IAAI;AAAA,IACrG,EAAE,OAAO,kCAAkC,KAAK,eAAe,MAAM,UAAU,QAAQ,IAAI;AAAA,IAC3F,EAAE,OAAO,6BAA6B,KAAK,gBAAgB,MAAM,UAAU,QAAQ,IAAI;AAAA,EAI7F;AAEA,QAAM,CAAC,oBAAoB,qBAAqB,IAAIE,aAAAA,SAAS,KAAK;AAClE,QAAM,CAAC,kBAAkB,mBAAmB,IAAIA,aAAAA,SAAS,KAAK;AAE9D,QAAM,CAAC,QAAQ,SAAS,IAAIA,aAAAA,SAAS,aAAa;AAE5C,QAAA,uBAAuBQ,aAAAA,YAAY,MAAM;AAEzC,0BAAsB,CAAC,cAAc;AAC/B,UAAI,WAAW;AAEF,eAAA;AAAA,MAAA,OAEN;AACM,eAAA;AAAA,MAAA;AAAA,IACb,CACL;AAAA,EACP,GAAG,EAAE;AACC,QAAA,2BAA2BA,aAAAA,YAAY,MAAM;AAC7C,wBAAoB,CAAC,cAAc;AAC7B,UAAI,WAAW;AACF,eAAA;AAAA,MAAA,OACN;AACM,eAAA;AAAA,MAAA;AAAA,IACb,CACL;AAAA,EACP,GAAG,EAAE;AAEL,QAAM,CAAC,eAAe,gBAAgB,IAAIR,aAAAA,SAAgC,CAAA,CAAE;AAE5E,QAAM,CAAC,oBAAoB,qBAAqB,IAAIA,aAAAA,SAAkC,CAAA,CAAE;AAcxF,QAAM,uBAAyD;AAAA,IACzD,UAAU;AAAA,IACV,QAAQ;AAAA,IACR,KAAK;AAAA,IACL,YAAY;AAAA,IACZ,UAAU;AAAA,IACV,UAAU;AAAA,IACV,cAAc;AAAA,IACd,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,EACd;AAEM,QAAA,qBAAqB,CAAC,KAA4C,UAA4C;AACtG,YAAA,IAAI,OAAO,KAAK,yBAAyB;AAEjD,qBAAiB,CAAC,SAAc;AAC1B,UAAI,WAA6C;AAE7C,UAAA,OAAO,UAAU,UAAU;AACrB,YAAA,MAAM,YAAY,MAAM,OAAO;AAClB,qBAAA;AAAA,QACN,WAAA,MAAM,YAAY,MAAM,MAAM;AACxB,qBAAA;AAAA,mBACN,CAAC,MAAM,OAAO,KAAK,CAAC,GAAG;AAC5B,qBAAW,OAAO,KAAK;AAAA,QAAA,WAClB,UAAU,KAAK;AACT,qBAAA;AAAA,QAAA;AAAA,MACjB;AAGN,UAAI,OAAO,sBAAsB;AACpB,eAAA;AAAA,UACD,GAAG;AAAA,UACH,YAAY;AAAA,YACN,GAAG,KAAK;AAAA,YACR,CAAC,GAAG,GAAG;AAAA,UAAA;AAAA,QAEnB;AAAA,MAAA,OACC;AACM,eAAA;AAAA,UACD,GAAG;AAAA,UACH,CAAC,GAAG,GAAG;AAAA,QACb;AAAA,MAAA;AAAA,IACN,CACL;AAED,0BAAsB,CAAC,UAAU;AAAA,MAC3B,GAAG;AAAA,MACH,CAAC,GAAG,GAAG;AAAA,IAAA,EACX;AAAA,EACR;AAGM,QAAA,aAAa,CAAC,QAA+C;;AACzD,QAAA;AAEJ,QAAI,OAAO,sBAAsB;AACZ,sBAAA,mBAAc,eAAd,mBAA2B;AAClB,8BAAA,GAAG,GAAG,IAAI,YAAY;AAAA,IAAA,OAC7C;AACD,qBAAe,cAAc,GAAyB;AACtD,8BAAwB,KAAK,YAAY;AAAA,IAAA;AAG/C,cAAU,CAAC,gBAAgB;AAAA,MACrB,GAAG;AAAA,MACH,GAAI,OAAO,uBACH;AAAA,QACI,YAAY;AAAA,UACN,GAAG,WAAW;AAAA,UACd,CAAC,GAAG,GAAG;AAAA,QAAA;AAAA,MACb,IAEJ,EAAE,CAAC,GAAG,GAAG,aAAa;AAAA,IAAA,EAClC;AAEF,0BAAsB,CAAC,UAAU;AAAA,MAC3B,GAAG;AAAA,MACH,CAAC,GAAG,GAAG;AAAA,IAAA,EACX;AAGF,qBAAiB,CAAC,SAAS;;AACf,YAAA,gBAAgB,EAAE,GAAG,KAAK;AAChC,UAAI,OAAO,sBAAsB;AACpB,SAAAD,MAAA,cAAc,eAAd,qBAAAA,IAA2B;AAAA,MAAuB,OACxD;AACD,eAAO,cAAc,GAAyB;AAAA,MAAA;AAE7C,aAAA;AAAA,IAAA,CACZ;AAAA,EACP;AAGM,SAAAF,kCAAA,KAAC,OAAI,EAAA,WAAU,8EACT,UAAA;AAAA,IAACA,kCAAAA,KAAA,OAAA,EAAI,WAAU,uEACT,UAAA;AAAA,MAACA,kCAAAA,KAAA,OAAA,EAAI,WAAU,+BACT,UAAA;AAAA,QAACC,kCAAA,IAAA,OAAA,EAAI,WAAU,6CAA4C,UAAmB,uBAAA;AAAA,QAC9EA,kCAAA;AAAA,UAAC;AAAA,UAAA;AAAA,YACK,WAAU;AAAA,YACV,SAAS,MAAM;AACT,kBAAI,CAAC,oBAAoB;AAEF,iCAAA;AAAA,kBACX,YAAY,EAAE,GAAG,cAAc,WAAW;AAAA,gBAAA,CAC/C;AAAA,cAAA;AAIc,mCAAA;AAAA,YAC3B;AAAA,YAGC,+BAEWD,kCAAAA,KAAAM,kBAAA,UAAA,EAAA,UAAA;AAAA,cAACL,kCAAAA,IAAA,GAAA,EAAE,WAAU,UAAU,CAAA;AAAA,cAAE;AAAA,YAAA,EAAA,CAE/B,IAGMD,kCAAAA,KAAAM,kBAAA,UAAA,EAAA,UAAA;AAAA,cAACL,kCAAAA,IAAAM,WAAA,EAAK,WAAU,UAAU,CAAA;AAAA,cAAE;AAAA,YAAA,EAElC,CAAA;AAAA,UAAA;AAAA,QAAA;AAAA,MAEZ,GACN;AAAA,MAEAP,kCAAAA,KAAC,OAAI,EAAA,WAAU,2BACT,UAAA;AAAA,QAACC,kCAAA,IAAA,OAAA,EAAI,WAAU,yCAAwC,UAAY,gBAAA;AAAA,QAClE,cAAc;AAAA,MAAA,GACrB;AAAA,4CAGC,OACM,EAAA,UAAA,iBAAiB,IAAI,CAAC,EAAE,OAAO,KAAK,MAAM,OAAO;;qDAE3C,OAAc,EAAA,WAAU,mDACnB,UAACD,uCAAA,OAAA,EAAI,WAAU,2BACT,UAAA;AAAA,UAACA,kCAAAA,KAAA,OAAA,EAAI,WAAU,8CAA8C,UAAA;AAAA,YAAA;AAAA,YAAM;AAAA,UAAA,GAAC;AAAA,UACpEA,kCAAAA,KAAC,QAAK,EAAA,WAAU,uDACT,UAAA;AAAA,YAAA;AAAA,YAOA,SAAS,aACJC,kCAAA;AAAA,cAAC;AAAA,cAAA;AAAA,gBACK,MAAK;AAAA,gBACL,SAAS;AAAA,oBACF,oDAAe,eAAf,mBAA4B,WAA4B,oDAAe,eAAf,mBAA4B,SAA6B;AAAA,gBACxH;AAAA,gBAAG,UAAU,CAAC,MACR;AAAA,kBACM;AAAA,kBACA,EAAE,OAAO;AAAA,gBACf;AAAA,gBAEN,WAAU;AAAA,gBACV,UAAU,CAAC;AAAA,cAAA;AAAA,YAAA,IAEnB,SAAS,WACPA,kCAAA;AAAA,cAAC;AAAA,cAAA;AAAA,gBACK,MAAK;AAAA,gBACL,SACM,oDAAe,eAAf,mBAA4B,WACtB,oDAAe,eAAf,mBAA4B,QAC1B;AAAA,oBACI,oDAAe,eAAf,mBAA4B,WAC5B,oDAAe,eAAf,mBAA4B;AAAA,gBAAuB,IAEvD;AAAA,gBAEd,UAAU,CAAC,MAAM;AACX,wBAAM,QAAQ,EAAE,OAAO,MAAM,KAAK;AAClC,sBAAI,UAAU,IAAI;AACZ,uCAAmB,KAAyB,EAAE;AAC9C;AAAA,kBAAA;AAEN,wBAAM,eAAe,KAAK,IAAI,GAAG,OAAO,KAAK,CAAC;AAC9C,qCAAmB,KAAyB,YAAY;AAAA,gBAC9D;AAAA,gBACA,WAAU;AAAA,gBACV,UAAU,CAAC;AAAA,cAAA;AAAA,YAAA,IAGjBA,kCAAA;AAAA,cAAC;AAAA,cAAA;AAAA,gBACK,MAAK;AAAA,gBAEL,SACM,oDAAe,eAAf,mBAA4B,WACtB,oDAAe,eAAf,mBAA4B,QAC1B;AAAA,oBACI,oDAAe,eAAf,mBAA4B,WAC5B,oDAAe,eAAf,mBAA4B;AAAA,gBAAuB,IAEvD;AAAA,gBAEd,UAAU,CAAC,MAAM;AACL,wBAAA,QAAQ,EAAE,OAAO;AAGJ,qCAAA,KAAyB,UAAU,OAAO,MAAM,WAAW,KAAK,KAAK,KAAK;AAAA,gBACnG;AAAA,gBACA,WAAU;AAAA,gBACV,UAAU,CAAC;AAAA,cAAA;AAAA,YAAA;AAAA,UACjB,GAEZ;AAAA,UAEC,sBAAsB,mBAAmB,GAAG,KACvCA,kCAAA;AAAA,YAAC;AAAA,YAAA;AAAA,cACK,WAAU;AAAA,cACV,SAAS,MAAM,WAAW,GAAuB;AAAA,cAEjD,UAAAA,kCAAAA,IAAC,MAAK,EAAA,WAAU,UAAU,CAAA;AAAA,YAAA;AAAA,UAAA;AAAA,QAChC,GAEZ,EAAA,GAlFI,GAmFV;AAAA,OACL,EAEP,CAAA;AAAA,IAAA,GACN;AAAA,IACAD,kCAAAA,KAAC,OAAI,EAAA,WAAU,uEACT,UAAA;AAAA,MAACA,kCAAAA,KAAA,OAAA,EAAI,WAAU,+BACT,UAAA;AAAA,QAACC,kCAAA,IAAA,OAAA,EAAI,WAAU,6CAA4C,UAE3D,2BAAA;AAAA,QACAA,kCAAA;AAAA,UAAC;AAAA,UAAA;AAAA,YACK,WAAU;AAAA,YACV,SAAS,MAAM,yBAAyB;AAAA,YAEvC,6BACKD,kCAAAA,KAAAM,kBAAA,UAAA,EAAA,UAAA;AAAA,cAACL,kCAAAA,IAAA,GAAA,EAAE,WAAU,UAAU,CAAA;AAAA,cAAE;AAAA,YAAA,EAAA,CAE/B,IAGYD,kCAAAA,KAAAM,kBAAA,UAAA,EAAA,UAAA;AAAA,cAACL,kCAAAA,IAAAM,WAAA,EAAK,WAAU,UAAU,CAAA;AAAA,cAAE;AAAA,YAAA,EAElC,CAAA;AAAA,UAAA;AAAA,QAAA;AAAA,MAEZ,GACN;AAAA,MAGC,oBAAoB,IAAI,CAAC,EAAE,OAAO,KAAK,MAAM,OAExC,MAAAN,sCAAC,SAAc,WAAU,mDACnB,UAACD,kCAAA,KAAA,OAAA,EAAI,WAAU,2BACT,UAAA;AAAA,QAACC,kCAAA,IAAA,OAAA,EAAI,WAAU,yCAAyC,UACxD,OAAA;AAAA,QACAD,kCAAAA,KAAC,QAAK,EAAA,WAAU,6DAEV,UAAA;AAAA,UAAAC,kCAAA;AAAA,YAAC;AAAA,YAAA;AAAA,cACK;AAAA,cACA,OAAO,OAAO,cAAc,GAAyB,KAAK,OAAO,GAAyB,CAAC;AAAA,cAI3F,UAAU,CAAC,MAAM;AACL,sBAAA,eAAe,KAAK,IAAI,GAAG,OAAO,EAAE,OAAO,KAAK,CAAC;AAGvD,mCAAmB,KAA2B,YAAY;AAAA,cAChE;AAAA,cACA,WAAU;AAAA,cAEV,UAAU,CAAC;AAAA,YAAA;AAAA,UACjB;AAAA,UACC;AAAA,QAAA,GACP;AAAA,QACC,uBAAuB,mBAAmB,GAAG,KACxCA,kCAAA;AAAA,UAAC;AAAA,UAAA;AAAA,YACK,WAAU;AAAA,YACV,SAAS,MAAM,WAAW,GAAyB;AAAA,YAEnD,UAAAA,kCAAAA,IAAC,MAAK,EAAA,WAAU,UAAU,CAAA;AAAA,UAAA;AAAA,QAAA;AAAA,MAChC,EAEZ,CAAA,EAAA,GAhCI,GAkCV,CACL;AAAA,IAAA,EACP,CAAA;AAAA,EAAA,GACN;AAEZ;ACtWA,MAAM,SAAS;AAAA,EACb,EAAE,MAAM,QAAQ,MAAM,QAAQ,OAAO,OAAO;AAAA,EAC5C,EAAE,MAAM,QAAQ,MAAM,QAAQ,OAAO,OAAO;AAAA,EAC5C,EAAE,MAAM,aAAa,MAAM,QAAQ,OAAO,YAAY;AAAA,EACtD,EAAE,MAAM,gBAAgB,MAAM,UAAU,OAAO,gBAAgB;AAAA,EAC/D,EAAE,MAAM,mBAAmB,MAAM,UAAU,OAAO,yBAAyB;AAAA,EAC3E,EAAE,MAAM,eAAe,MAAM,UAAU,OAAO,qBAAqB;AAAA,EACnE,EAAE,MAAM,eAAe,MAAM,UAAU,OAAO,qBAAqB;AAAA,EACnE,EAAE,MAAM,qBAAqB,MAAM,UAAU,OAAO,2BAA2B;AAAA,EAC/E,EAAE,MAAM,gBAAgB,MAAM,UAAU,OAAO,iBAAiB;AAAA,EAChE,EAAE,MAAM,gBAAgB,MAAM,UAAU,OAAO,gBAAgB;AAAA,EAC/D,EAAE,MAAM,eAAe,MAAM,QAAQ,OAAO,cAAc;AAAA,EAC1D,EAAE,MAAM,kBAAkB,MAAM,UAAU,OAAO,iBAAiB;AAAA,EAClE,EAAE,MAAM,gBAAgB,MAAM,UAAU,OAAO,eAAe;AAAA,EAC9D,EAAE,MAAM,gBAAgB,MAAM,UAAU,OAAO,eAAe;AAAA,EAC9D,EAAE,MAAM,YAAY,MAAM,UAAU,OAAO,WAAW;AAAA,EACtD,EAAE,MAAM,YAAY,MAAM,UAAU,OAAO,WAAW;AAAA,EACtD,EAAE,MAAM,eAAe,MAAM,UAAU,OAAO,cAAc;AAAA,EAC5D,EAAE,MAAM,eAAe,MAAM,UAAU,OAAO,cAAc;AAAA,EAC5D,EAAE,MAAM,mBAAmB,MAAM,UAAU,OAAO,mBAAmB;AACvE;AAEA,MAAM,YAAsC,CAAC;AAAA,EAC3C;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,MAAM;AACJ,QAAM,CAAC,UAAU,WAAW,IAAIE,aAAAA,SAA8B,IAAI;AAClE,QAAM,gBAAgB,WAAkE;AACxF,QAAM,CAAC,aAAa,cAAc,IAAIA,aAAAA,SAAwB,IAAI;AAClE,QAAM,CAAC,WAAW,YAAY,IAAIA,aAAAA,SAAS,KAAK;AAC1C,QAAA,eAAeW,oBAAyB,IAAI;AAElDV,eAAAA,UAAU,MAAM;AACd,gBAAY,IAAI;AAAA,EAAA,GACf,CAAC,IAAI,CAAC;AAEH,QAAA,mBAAmB,OAAO,UAA+C;AAC7E,UAAM,QAAQ,MAAM,KAAK,MAAM,OAAO,SAAS,EAAE;AAC7C,QAAA,MAAM,WAAW,EAAG;AAExB,mBAAe,IAAI;AAEnB,UAAM,gBAAgB,MAAM;AAC5B,UAAM,eAAe,CAAC,cAAc,aAAa,aAAa,YAAY;AAE1E,UAAM,YAAY,MAAM,KAAK,CAAC,SAAS;AACjC,UAAA,KAAK,OAAO,eAAe;AAC7B,uBAAe,gCAAgC;AACxC,eAAA;AAAA,MAAA;AAET,UAAI,CAAC,aAAa,SAAS,KAAK,IAAI,GAAG;AACrC,uBAAe,mDAAmD;AAC3D,eAAA;AAAA,MAAA;AAEF,aAAA;AAAA,IAAA,CACR;AAED,QAAI,CAAC,UAAW;AAEV,UAAA,iBAAiB,IAAI,SAAS;AACrB,mBAAA,OAAO,WAAW,aAAa;AAC9C,mBAAe,OAAO,QAAQ,WAAW,UAAU,IAAI;AAEvD,iBAAa,IAAI;AAEjB,kBAAc,OAAO,gBAAgB;AAAA,MACnC,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,SAAS;AAAA,IAAA,CACV;AAAA,EACH;AAGM,QAAA,eAAe,CACnB,MACG;AACH,UAAM,EAAE,MAAM,OAAO,SAAS,EAAE;AAChC,gBAAY,CAAC,UAAe;AAAA,MAC1B,GAAG;AAAA,MACH,CAAC,IAAI,GACH,SAAS,WACL,UAAU,KAAK,KAAK,OAAO,KAAK,IAChC,SAAS,YAAY,SAAS,eAAe,SAAS,cACpD,UAAU,SACV;AAAA,IAAA,EACR;AAAA,EACJ;AAEAA,eAAAA,UAAU,MAAM;AACd,QAAI,cAAc,MAAM;AAClB,UAAA,cAAc,KAAK,OAAO;AACb,uBAAA,cAAc,KAAK,KAAK;AAAA,MAAA,WAC9B,cAAc,KAAK,SAAS;AAC/B,cAAA,cAAc,cAAc,KAAK;AACvC,oBAAY,CAAC,UAAe;AAAA,UAC1B,GAAG;AAAA,UACH,SAAS;AAAA,QAAA,EACT;AACF,uBAAe,IAAI;AACnB,YAAI,aAAa,QAAsB,cAAA,QAAQ,QAAQ;AAAA,MAAA;AAEzD,mBAAa,KAAK;AAAA,IAAA;AAAA,EACpB,GACC,CAAC,cAAc,IAAI,CAAC;AAEvB,QAAM,aAAa,MAAM;AAEjB,UAAA,cAAmC,EAAE,GAAG,SAAS;AACvD,QAAI,OAAO,KAAK,IAAI,EAAE,SAAS,GAAG;AAChC,iBAAW,OAAO,UAAU;AAC1B,cAAM,WAAW;AACjB,YAAI,YAAY,QAAQ,MAAM,KAAK,QAAQ,GAAG;AAC5C,iBAAO,YAAY,QAAQ;AAAA,QAAA;AAAA,MAC7B;AAEF,kBAAY,KAAK,KAAK;AAAA,IAAA;AAGxB,WAAO,WAAW;AAAA,EACpB;AAEI,MAAA,CAAC,OAAe,QAAA;AAEpB,SACGH,kCAAA,IAAA,QAAA,EAAO,MAAM,QAAQ,cAAc,SAClC,UAAAD,kCAAA;AAAA,IAAC;AAAA,IAAA;AAAA,MACC,WAAU;AAAA,MACV,OAAO,EAAE,OAAO,OAAO;AAAA,MAEvB,UAAA;AAAA,QAACC,kCAAA,IAAA,aAAA,EAAY,WAAU,sCAAqC,UAAgB,oBAAA;AAAA,QAC5ED,kCAAAA,KAAC,OAAI,EAAA,WAAU,oCAEb,UAAA;AAAA,UAACA,kCAAAA,KAAA,OAAA,EAAI,WAAU,oCACb,UAAA;AAAA,YAACC,kCAAA,IAAA,MAAA,EAAG,WAAU,yCAAwC,UAAY,gBAAA;AAAA,YAClED,kCAAAA,KAAC,OAAI,EAAA,WAAU,2BACZ,UAAA;AAAA,cAAY,YAAA,aAAa,YAAY,SAAS,WAC7CC,kCAAA;AAAA,gBAAC;AAAA,gBAAA;AAAA,kBACC,KAAK,SAAS;AAAA,kBACd,KAAI;AAAA,kBACJ,WAAU;AAAA,gBAAA;AAAA,cACZ;AAAA,cAEFA,kCAAA;AAAA,gBAAC;AAAA,gBAAA;AAAA,kBACC,KAAK;AAAA,kBACL,MAAK;AAAA,kBACL,QAAO;AAAA,kBACP,UAAU;AAAA,kBACV,WAAU;AAAA,gBAAA;AAAA,cAAA;AAAA,YACZ,GACF;AAAA,YACC,eAAeA,kCAAA,IAAC,KAAE,EAAA,WAAU,wBAAwB,UAAY,YAAA,CAAA;AAAA,UAAA,GACnE;AAAA,UAGAA,kCAAA,IAAC,OAAI,EAAA,WAAU,yCACZ,UAAA,OAAO,IAAI,CAAC,UACXD,kCAAA,KAAC,OAAqB,EAAA,WAAU,iBAC9B,UAAA;AAAA,YAAAC,kCAAA,IAAC,SAAM,EAAA,WAAU,0CACd,UAAA,MAAM,OACT;AAAA,YACAA,kCAAA;AAAA,cAAC;AAAA,cAAA;AAAA,gBACC,MAAM,MAAM;AAAA,gBACZ,MAAM,MAAM;AAAA,gBACZ,OACE,MAAM,SAAS,YACV,qCAAmB,MAAM,UAAS,MAClC,qCAAmB,MAAM,UAAS;AAAA,gBAEzC,UAAU;AAAA,gBACV,WAAU;AAAA,gBACV,aAAa,MAAM;AAAA,gBACnB,KAAK,MAAM,SAAS,WAAW,IAAI;AAAA,cAAA;AAAA,YAAA;AAAA,UAf7B,EAAA,GAAA,MAAM,IAiBhB,CACD,GACH;AAAA,iDAGC,OACC,EAAA,UAAA;AAAA,YAACA,kCAAA,IAAA,SAAA,EAAM,WAAU,gDAA+C,UAEhE,mBAAA;AAAA,YACAD,kCAAA;AAAA,cAAC;AAAA,cAAA;AAAA,gBACC,MAAK;AAAA,gBACL,OAAO,SAAS;AAAA,gBAChB,UAAU;AAAA,gBACV,UAAQ;AAAA,gBACR,WAAU;AAAA,gBAEV,UAAA;AAAA,kBAACC,kCAAA,IAAA,UAAA,EAAO,OAAM,IAAG,UAAe,mBAAA;AAAA,kBAC/B,6CAAc,IAAI,CAAC;;AAClBA,6DAAA;AAAA,sBAAC;AAAA,sBAAA;AAAA,wBAEC,QAAO,0CAAU,mBAAV,mBAA0B;AAAA,wBAEhC,UAAU,qCAAA;AAAA,sBAAA;AAAA,sBAHN,qCAAU;AAAA,oBAKlB;AAAA;AAAA,gBAAA;AAAA,cAAA;AAAA,YAAA;AAAA,UACH,GACF;AAAA,UAGAD,kCAAAA,KAAC,OAAI,EAAA,WAAU,yCACb,UAAA;AAAA,YAACA,kCAAAA,KAAA,OAAA,EAAI,WAAU,iBACb,UAAA;AAAA,cAACC,kCAAA,IAAA,SAAA,EAAM,WAAU,0CAAyC,UAAY,gBAAA;AAAA,cACtED,kCAAAA,KAAC,OAAI,EAAA,WAAU,cACb,UAAA;AAAA,gBAACA,kCAAAA,KAAA,SAAA,EAAM,WAAU,mCACf,UAAA;AAAA,kBAAAC,kCAAA;AAAA,oBAAC;AAAA,oBAAA;AAAA,sBACC,MAAK;AAAA,sBACL,MAAK;AAAA,sBACL,OAAM;AAAA,sBACN,UAAS,qCAAU,eAAc;AAAA,sBACjC,UAAU;AAAA,oBAAA;AAAA,kBACZ;AAAA,kBAAE;AAAA,gBAAA,GAEJ;AAAA,gBACAD,kCAAAA,KAAC,SAAM,EAAA,WAAU,mCACf,UAAA;AAAA,kBAAAC,kCAAA;AAAA,oBAAC;AAAA,oBAAA;AAAA,sBACC,MAAK;AAAA,sBACL,MAAK;AAAA,sBACL,OAAM;AAAA,sBACN,UAAS,qCAAU,eAAc;AAAA,sBACjC,UAAU;AAAA,oBAAA;AAAA,kBACZ;AAAA,kBAAE;AAAA,gBAAA,EAEJ,CAAA;AAAA,cAAA,EACF,CAAA;AAAA,YAAA,GACF;AAAA,YACAD,kCAAAA,KAAC,OAAI,EAAA,WAAU,iBACb,UAAA;AAAA,cAACC,kCAAA,IAAA,SAAA,EAAM,WAAU,0CAAyC,UAAU,cAAA;AAAA,cACpED,kCAAAA,KAAC,OAAI,EAAA,WAAU,cACb,UAAA;AAAA,gBAACA,kCAAAA,KAAA,SAAA,EAAM,WAAU,mCACf,UAAA;AAAA,kBAAAC,kCAAA;AAAA,oBAAC;AAAA,oBAAA;AAAA,sBACC,MAAK;AAAA,sBACL,MAAK;AAAA,sBACL,OAAM;AAAA,sBACN,UAAS,qCAAU,cAAa;AAAA,sBAChC,UAAU;AAAA,oBAAA;AAAA,kBACZ;AAAA,kBAAE;AAAA,gBAAA,GAEJ;AAAA,gBACAD,kCAAAA,KAAC,SAAM,EAAA,WAAU,mCACf,UAAA;AAAA,kBAAAC,kCAAA;AAAA,oBAAC;AAAA,oBAAA;AAAA,sBACC,MAAK;AAAA,sBACL,MAAK;AAAA,sBACL,OAAM;AAAA,sBACN,UAAS,qCAAU,cAAa;AAAA,sBAChC,UAAU;AAAA,oBAAA;AAAA,kBACZ;AAAA,kBAAE;AAAA,gBAAA,EAEJ,CAAA;AAAA,cAAA,EACF,CAAA;AAAA,YAAA,EACF,CAAA;AAAA,UAAA,GACF;AAAA,iDAGC,OACC,EAAA,UAAA;AAAA,YAACA,kCAAA,IAAA,SAAA,EAAM,WAAU,0CAAyC,UAAK,SAAA;AAAA,YAC/DD,kCAAA;AAAA,cAAC;AAAA,cAAA;AAAA,gBACC,OAAO,qCAAU;AAAA,gBACjB,eAAe,CAAC,UACd,YAAY,CAAC,UAAe,EAAE,GAAG,MAAM,MAAM,MAAA,EAAQ;AAAA,gBAGvD,UAAA;AAAA,kBAAAC,kCAAAA,IAAC,iBAAc,WAAU,UACvB,gDAAC,aAAY,EAAA,aAAY,eAAc,EACzC,CAAA;AAAA,yDACC,eACC,EAAA,UAAA;AAAA,oBAACA,kCAAA,IAAA,YAAA,EAAW,OAAM,OAAM,UAAG,OAAA;AAAA,oBAC1BA,kCAAA,IAAA,YAAA,EAAW,OAAM,UAAS,UAAM,UAAA;AAAA,oBAChCA,kCAAA,IAAA,YAAA,EAAW,OAAM,OAAM,UAAG,MAAA,CAAA;AAAA,kBAAA,EAC7B,CAAA;AAAA,gBAAA;AAAA,cAAA;AAAA,YAAA;AAAA,UACF,EACF,CAAA;AAAA,QAAA,GACF;AAAA,QAGAD,kCAAAA,KAAC,OAAI,EAAA,WAAU,oDACb,UAAA;AAAA,UAAAC,kCAAA;AAAA,YAAC;AAAA,YAAA;AAAA,cACC,SAAS;AAAA,cACT,WAAU;AAAA,cACX,UAAA;AAAA,YAAA;AAAA,UAED;AAAA,UACAA,kCAAA;AAAA,YAAC;AAAA,YAAA;AAAA,cACC,SAAS;AAAA,cACT,WAAU;AAAA,cACX,UAAA;AAAA,YAAA;AAAA,UAAA;AAAA,QAED,EACF,CAAA;AAAA,MAAA;AAAA,IAAA;AAAA,EAAA,GAEJ;AAEJ;AC5SA,MAAM,mBAA+C,CAAC,EAAE,QAAQ,MAAM,SAAS,QAAQ,eAAe;AAC1F,QAAA,CAAC,UAAU,WAAW,IAAIE,aAAA;AAAA,IAA2B,QAC3D;AAAA,MAEM,cAAc;AAAA,MACd,aAAa;AAAA,MACb,aAAa;AAAA,MACb,QAAQ;AAAA,IAAA;AAAA,EAGd;AACA,QAAM,qBAAqB,WAAW;AAEhC,QAAA,UAAU,mBAAmB,SAAS;AAEtC,QAAA,EAAE,UAAU,IAAI,SAAS;AAE/BC,eAAAA,UAAU,MAAM;;AACV,QAAI,mBAAmB,MAAM;AAEnB,WAAA,wBAAmB,SAAnB,mBAAyB,QAAQ;AAE/B,kBAAU,qBAAqB,SAAS;AAChC,gBAAA;AACI,oBAAA;AAAA,UACN,cAAc;AAAA,UACd,aAAa;AAAA,UACb,aAAa;AAAA,UACb,QAAQ;AAAA,QAAA,CACb;AAAA,MAGE,WAAA,GAAC,wBAAmB,SAAnB,mBAAyB,SAAQ;AACrC,kBAAU,oCAAoC,OAAO;AAAA,MAAA;AAAA,IAC3D;AAAA,EACN,GAEH,CAAC,mBAAmB,IAAI,CAAC;AAE5BA,eAAAA,UAAU,MAAM;AACV,QAAI,QAAQ,QAAQ;AACd,kBAAY,IAAI;AAAA,IAAA,WAEb,CAAC,QAAQ;AACA,kBAAA;AAAA,QACN,cAAc;AAAA,QACd,aAAa;AAAA,QACb,aAAa;AAAA,QACb,QAAQ;AAAA,MAAA,CACb;AAAA,IAAA;AAAA,EACP,GAEH,CAAC,MAAM,MAAM,CAAC;AAKX,QAAA,eAAe,CAAC,MAA+D;AAC/E,UAAM,EAAE,MAAM,MAAM,IAAI,EAAE;AAC1B,gBAAY,CAAC,cAAc;AAAA,MACrB,GAAG;AAAA,MACH,CAAC,IAAI,GAAG,SAAS,WAAW,UAAU,SAAS;AAAA;AAAA,IAAA,EACnD;AAAA,EACR;AAEA,QAAM,aAAa,MAAM;;AACb,UAAA,cAAc,IAAI,SAAS;AACrB,gBAAA,OAAO,cAAc,eAAe;AACpC,gBAAA,OAAO,QAAQ,UAAU;AACrC,gBAAY,OAAO,aAAY,kCAAM,OAAN,mBAAU,UAAU;AACvC,gBAAA,OAAO,YAAY,QAA6B;AAC5D,gBAAY,OAAO,kBAAkB,KAAK,UAAU,QAAQ,CAAC;AAC7D,uBAAmB,OAAO,aAAa,EAAE,QAAQ,QAAQ;AACjD,YAAA;AAAA,EACd;AACI,MAAA,CAAC,OAAe,QAAA;AAMd,SAAAH,kCAAA,IAAC,UAAO,MAAM,QAAQ,cAAc,SAC9B,UAAAD,kCAAA,KAAC,eAAc,EAAA,WAAU,2DACnB,UAAA;AAAA,IAACC,kCAAA,IAAA,aAAA,EAAY,WAAU,+CAA+C,UAAO,QAAA;AAAA,IAE7ED,kCAAAA,KAAC,OAAI,EAAA,WAAU,qHAET,UAAA;AAAA,MAACC,kCAAAA,IAAA,OAAA,EAAI,WAAU,aACR,UAAA;AAAA,QACK,EAAE,IAAI,GAAG,MAAM,gBAAyB,MAAM,QAAQ,OAAO,eAAe;AAAA,QAC5E,EAAE,IAAI,GAAG,MAAM,eAAwB,MAAM,QAAQ,OAAO,cAAc;AAAA,QAC1E,EAAE,IAAI,GAAG,MAAM,eAAwB,MAAM,QAAQ,OAAO,cAAc;AAAA,MAAA,EAE9E,IAAI,CAAC,UACAD,kCAAA,KAAA,OAAA,EAAqB,WAAU,mDAC1B,UAAA;AAAA,QAAAC,kCAAA,IAAC,SAAM,EAAA,WAAU,qDAAqD,UAAA,MAAM,OAAM;AAAA,QAGlFA,kCAAA;AAAA,UAAC;AAAA,UAAA;AAAA,YACK,MAAM,MAAM;AAAA,YACZ,MAAM,MAAM;AAAA,YACZ,QAAO,qCAAW,MAAM,UAAmC;AAAA,YAAI,UAAU;AAAA,YACzE,WAAU;AAAA,YACV,aAAa,SAAS,MAAM,KAAK;AAAA,UAAA;AAAA,QAAA;AAAA,MATnC,EAAA,GAAA,MAAM,IAYhB,CACL,GACP;AAAA,MAGAD,kCAAAA,KAAC,OAAI,EAAA,WAAU,mDACT,UAAA;AAAA,QAACC,kCAAA,IAAA,SAAA,EAAM,WAAU,qDAAoD,UAAa,iBAAA;AAAA,QAClFD,kCAAAA,KAAC,OAAI,EAAA,WAAU,8BACT,UAAA;AAAA,UAACA,kCAAAA,KAAA,SAAA,EAAM,WAAU,gEACX,UAAA;AAAA,YAAAC,kCAAA;AAAA,cAAC;AAAA,cAAA;AAAA,gBACK,MAAK;AAAA,gBACL,MAAK;AAAA,gBACL,OAAM;AAAA,gBACN,UAAS,qCAAU,YAAW;AAAA,gBAC9B,UAAU;AAAA,gBACV,WAAU;AAAA,cAAA;AAAA,YAChB;AAAA,YAAE;AAAA,UAAA,GAER;AAAA,UACAD,kCAAAA,KAAC,SAAM,EAAA,WAAU,gEACX,UAAA;AAAA,YAAAC,kCAAA;AAAA,cAAC;AAAA,cAAA;AAAA,gBACK,MAAK;AAAA,gBACL,MAAK;AAAA,gBACL,OAAM;AAAA,gBACN,UAAS,qCAAU,YAAW;AAAA,gBAC9B,UAAU;AAAA,gBACV,WAAU;AAAA,cAAA;AAAA,YAChB;AAAA,YAAE;AAAA,UAAA,EAER,CAAA;AAAA,QAAA,EACN,CAAA;AAAA,MAAA,EACN,CAAA;AAAA,IAAA,GACN;AAAA,IAGAD,kCAAAA,KAAC,OAAI,EAAA,WAAU,uDACT,UAAA;AAAA,MAAAC,kCAAA;AAAA,QAAC;AAAA,QAAA;AAAA,UACK,SAAS;AAAA,UACT,WAAU;AAAA,UACf,UAAA;AAAA,QAAA;AAAA,MAED;AAAA,MACAD,kCAAA;AAAA,QAAC;AAAA,QAAA;AAAA,UACK,SAAS;AAAA,UACT,UAAU;AAAA,UACV,WAAW,qBAAqB,UAAU,gBAAgB,aAAa;AAAA,UAEtE,UAAA;AAAA,YAAA,UAAU,cAAc;AAAA,YAAO;AAAA,UAAA;AAAA,QAAA;AAAA,MAAA;AAAA,IACtC,EACN,CAAA;AAAA,EAAA,EAAA,CACN,EACN,CAAA;AAEZ;AC5JA,MAAM,qBAAqB;AAAA,EACrB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACN;AAEA,MAAM,mBAAoD,CAAC;AAAA,EACrD;AAAA,EACA;AACN,MAAM;AACA,QAAM,CAAC,YAAY,aAAa,IAAIG,aAAAA,SAAS,EAAE;AAa/C,QAAM,oBAAoB,WAAW;AAC/B,QAAA,eAAe,CAAC,iBAAmC;AAC7C,UAAA,WAAW,IAAI,SAAS;AACrB,aAAA,OAAO,cAAc,mBAAmB;AACjD,aAAS,OAAO,YAAY,6CAAc,GAAG,UAAU;AACvD,aAAS,OAAO,YAAY,SAAS,SAAA,CAAU;AAC/C,sBAAkB,OAAO,UAAU,EAAE,QAAQ,QAAQ;AAAA,EAC3D;AACA,QAAM,WAAW,YAAY;AAC7B,QAAM,CAAC,qBAAqB,sBAAsB,IAAIA,sBAA2B;AACjF,QAAM,CAAC,eAAe,gBAAgB,IAAIA,aAAAA,SAAS,KAAK;AACxD,QAAM,CAAC,qBAAqB,sBAAsB,IAAIA,aAAAA,SAAS,KAAK;AAE9D,QAAA,kBAAkB,CAAC,QAA0B;AAC7C,2BAAuB,GAAG;AAC1B,qBAAiB,IAAI;AACrB,2BAAuB,IAAI;AAAA,EACjC;AACA,QAAM,iBAAiB,MAAM;AACvB,2BAAuB,MAAS;AAChC,qBAAiB,KAAK;AACtB,2BAAuB,IAAI;AAAA,EACjC;AAGA,QAAM,CAAC,mBAAmB,oBAAoB,IAAIA,aAAAA,SAA6B,iBAAiB;AAE1F,QAAA,sBAAsB,YAAY,YAAY,GAAG;AAEvDC,eAAAA,UAAU,MAAM;AACV,QAAI,oBAAoB,UAAU,KAAK,wBAAwB,IAAI;AAC7D,YAAM,WAAW,kBAAkB;AAAA,QAAO,CAAC,SACrC,CAAC,KAAK,aAAa,KAAK,YAAY,EAAE;AAAA,UAChC,CAAC,UAAU,+BAAO,cAAc,SAAS,oBAAoB,YAAa;AAAA,QAAA;AAAA,MAEtF;AACA,2BAAqB,QAAQ;AAAA,IAAA,OAE9B;AACC,2BAAqB,iBAAiB;AAAA,IAAA;AAAA,EAC5C,GACH,CAAC,qBAAqB,iBAAiB,CAAC;AAGrC,QAAA,UAAU,kBAAkB,UAAU;AAG5C,SAEYJ,kCAAA,KAAAM,4BAAA,EAAA,UAAA;AAAA,IAACN,kCAAAA,KAAA,OAAA,EAAI,WAAU,6BACR,UAAA;AAAA,MAAA,WAAYC,kCAAA,IAAA,eAAA,EAAc,SAAkB,MAAM,IAAI;AAAA,MAEvDA,kCAAA;AAAA,QAAC;AAAA,QAAA;AAAA,UACK,aAAY;AAAA,UACZ,OAAO;AAAA,UAEP,MAAK;AAAA,UACL,UAAU,CAAC,MAAM,cAAc,EAAE,OAAO,KAAK;AAAA,UAC7C,WAAU;AAAA,QAAA;AAAA,MAAA;AAAA,IAChB,GAYN;AAAA,IACC,kBAAkB,WAAW,IACxBA,kCAAAA,IAAC,SAAI,WAAU,oBAAmB,8BAAgB,IAElDA,kCAAA;AAAA,MAAC;AAAA,MAAA;AAAA,QACK,SAAS;AAAA,QACT,MAAM;AAAA,QACN,WAAW,CAAC,QACLD,kCAAA,KAAA,MAAA,EAAgB,WAAU,YACrB,UAAA;AAAA,UAAAC,kCAAA,IAAC,MAAG,EAAA,WAAU,wDAAyD,UAAA,IAAI,IAAG;AAAA,gDAC7E,MAAG,EAAA,WAAU,qFAAoF,SAAS,MAAM,SAAS,iCAAiC,QAAQ,cAAc,2BAAK,YAAY,YAAY,IAAI,EAAE,EAAE,GAAI,qCAAK,cAAa;AAAA,UAC3OA,kCAAA,IAAA,MAAA,EAAG,WAAU,uDAAuD,qCAAK,aAAY;AAAA,UACrFA,kCAAA,IAAA,MAAA,EAAG,WAAU,uDAAuD,qCAAK,aAAY;AAAA,UAEtFA,kCAAAA,IAAC,QAAG,WAAU,uDACP,sCAAK,UACAD,kCAAA,KAAC,QAAK,EAAA,WAAU,uGACV,UAAA;AAAA,YAAAC,kCAAA;AAAA,cAAC;AAAA,cAAA;AAAA,gBACK,WAAU;AAAA,gBACV,MAAK;AAAA,gBACL,SAAQ;AAAA,gBACR,OAAM;AAAA,gBAEN,UAAAA,kCAAA;AAAA,kBAAC;AAAA,kBAAA;AAAA,oBACK,UAAS;AAAA,oBACT,GAAE;AAAA,oBACF,UAAS;AAAA,kBAAA;AAAA,gBAAA;AAAA,cACf;AAAA,YACN;AAAA,YAAM;AAAA,UAAA,EAEZ,CAAA,IAEAD,kCAAAA,KAAC,QAAK,EAAA,WAAU,mGACV,UAAA;AAAA,YAAAC,kCAAA;AAAA,cAAC;AAAA,cAAA;AAAA,gBACK,WAAU;AAAA,gBACV,MAAK;AAAA,gBACL,SAAQ;AAAA,gBACR,OAAM;AAAA,gBAEN,UAAAA,kCAAA;AAAA,kBAAC;AAAA,kBAAA;AAAA,oBACK,UAAS;AAAA,oBACT,GAAE;AAAA,oBACF,UAAS;AAAA,kBAAA;AAAA,gBAAA;AAAA,cACf;AAAA,YACN;AAAA,YAAM;AAAA,UAAA,EAAA,CAEZ,EAEZ,CAAA;AAAA,UACAA,kCAAAA,IAAC,MAAG,EAAA,WAAU,wCACR,UAAAA,kCAAA;AAAA,YAAC;AAAA,YAAA;AAAA,cACK,SAAQ;AAAA,cACR,MAAK;AAAA,cACL,WAAU;AAAA,cACV,SAAS,MAAM;AACL,oBAAA,QAAQ,mDAAmD,GAAG;AAC5D,+BAAa,GAAG;AAAA,gBAAA;AAAA,cAE5B;AAAA,cACA,OAAO,EAAE,WAAW,WAAW;AAAA,cAE/B,UAAAA,kCAAAA,IAAC,OAAM,EAAA,MAAM,GAAI,CAAA;AAAA,YAAA;AAAA,UAAA,GAE7B;AAAA,UACCA,kCAAA,IAAA,MAAA,EAAG,WAAU,wCACR,gDAAC,QAAO,EAAA,OAAM,QAAO,MAAM,IAAI,SAAS,MAAM,gBAAgB,GAAG,GAAG,EAC1E,CAAA;AAAA,QAAA,EAAA,GA1DG,IAAI,EA2Db;AAAA,MAAA;AAAA,IAEZ;AAAA,IAWNA,kCAAA;AAAA,MAAC;AAAA,MAAA;AAAA,QACK,QAAQ;AAAA,QACR,MAAM,gBAAgB,sBAAsB;AAAA,QAC5C,SAAS,MAAM,uBAAuB,KAAK;AAAA,QAC3C;AAAA,QAAoB,QAAQ,gBAAgB,oBAAoB;AAAA,MAAA;AAAA,IAAkB;AAAA,IACxFA,sCAAC,UAAO,WAAU,sDAAqD,SAAS,MAAM,eAAA,GAAkB,UAAiB,oBAAA,CAAA;AAAA,EAAA,GAC/H;AAEZ;AC7LA,MAAM,cAAwC,CAAC,EAAE,QAAQ,MAAM,SAAS,QAAQ,QAAQ,eAAe;AACjG,QAAM,CAAC,UAAU,WAAW,IAAIE,aAAAA,SAAsB,IAAI;AAC1D,QAAM,CAAC,SAAS,UAAU,IAAIA,aAAAA,SAAS,KAAK;AAE5CC,eAAAA,UAAU,MAAM;AACV,gBAAY,IAAI;AAAA,EAAA,GACnB,CAAC,IAAI,CAAC;AAEH,QAAA,eAAe,CAAC,MAA+D;AAC/E,UAAM,EAAE,MAAM,MAAM,IAAI,EAAE;AAC1B,gBAAY,CAAC,cAAc;AAAA,MACrB,GAAG;AAAA,MACH,CAAC,IAAI,GAAG,SAAS,WAAW,UAAU,SAAS;AAAA;AAAA,IAAA,EACnD;AAAA,EACR;AACA,QAAM,eAAe,WAAW;AAEhC,QAAM,aAAa,MAAM;AACb,UAAA,cAAc,IAAI,SAAS;AACzB,YAAA,IAAI,UAAU,mBAAmB;AAC7B,gBAAA,OAAO,cAAc,WAAW;AAChC,gBAAA,OAAO,YAAY,QAA6B;AAE5D,gBAAY,OAAO,aAAa,KAAK,UAAU,QAAQ,CAAC;AACxD,iBAAa,OAAO,aAAa,EAAE,QAAQ,QAAQ;AAC3C,YAAA;AAAA,EACd;AAEI,MAAA,CAAC,OAAe,QAAA;AAGd,SAAAH,kCAAA,IAAC,UAAO,MAAM,QAAQ,cAAc,SAC9B,UAAAD,kCAAA,KAAC,eAAc,EAAA,WAAU,2DACnB,UAAA;AAAA,IAACC,kCAAA,IAAA,aAAA,EAAY,WAAU,+CAA+C,UAAO,QAAA;AAAA,IAE7ED,kCAAAA,KAAC,OAAI,EAAA,WAAU,qHAET,UAAA;AAAA,MAACC,kCAAAA,IAAA,OAAA,EAAI,WAAU,aACR,UAAA;AAAA,QACK,EAAE,MAAM,QAAQ,MAAM,QAAQ,OAAO,OAAO;AAAA,QAC5C,EAAE,MAAM,QAAQ,MAAM,UAAU,OAAO,OAAO;AAAA,MAAA,EAClD,IAAI,CAAC,UACAD,kCAAA,KAAA,OAAA,EAAqB,WAAU,mDAC1B,UAAA;AAAA,QAAAC,kCAAA,IAAC,SAAM,EAAA,WAAU,qDAAqD,UAAA,MAAM,OAAM;AAAA,QACjF,MAAM,SAAS,SACVD,kCAAA;AAAA,UAAC;AAAA,UAAA;AAAA,YACK,MAAM,MAAM;AAAA,YACZ,QAAO,qCAAW,MAAM,UAAS;AAAA,YACjC,UAAU;AAAA,YACV,WAAU;AAAA,YAEV,UAAA;AAAA,cAACC,kCAAA,IAAA,UAAA,EAAO,OAAM,IAAG,UAAW,eAAA;AAAA,cAC3BA,kCAAA,IAAA,UAAA,EAAO,OAAM,OAAM,UAAG,OAAA;AAAA,cACtBA,kCAAA,IAAA,UAAA,EAAO,OAAM,UAAS,UAAM,UAAA;AAAA,cAC5BA,kCAAA,IAAA,UAAA,EAAO,OAAM,OAAM,UAAG,OAAA;AAAA,cACtBA,kCAAA,IAAA,UAAA,EAAO,OAAM,SAAQ,UAAK,SAAA;AAAA,cAC1BA,kCAAA,IAAA,UAAA,EAAO,OAAM,MAAK,UAAE,KAAA,CAAA;AAAA,YAAA;AAAA,UAAA;AAAA,QAAA,IAI3BA,kCAAA;AAAA,UAAC;AAAA,UAAA;AAAA,YACK,MAAM,MAAM;AAAA,YACZ,MAAM,MAAM;AAAA,YACZ,QAAO,qCAAW,MAAM,UAAS;AAAA,YACjC,UAAU;AAAA,YACV,WAAU;AAAA,YACV,aAAa,SAAS,MAAM,KAAK;AAAA,UAAA;AAAA,QAAA;AAAA,MAxBzC,EAAA,GAAA,MAAM,IA2BhB,CACL,GACP;AAAA,MAGAD,kCAAAA,KAAC,OAAI,EAAA,WAAU,mDACT,UAAA;AAAA,QAACC,kCAAA,IAAA,SAAA,EAAM,WAAU,qDAAoD,UAAa,iBAAA;AAAA,QAClFD,kCAAAA,KAAC,OAAI,EAAA,WAAU,8BACT,UAAA;AAAA,UAACA,kCAAAA,KAAA,SAAA,EAAM,WAAU,gEACX,UAAA;AAAA,YAAAC,kCAAA;AAAA,cAAC;AAAA,cAAA;AAAA,gBACK,MAAK;AAAA,gBACL,MAAK;AAAA,gBACL,OAAM;AAAA,gBACN,UAAS,qCAAU,YAAW;AAAA,gBAC9B,UAAU;AAAA,gBACV,WAAU;AAAA,cAAA;AAAA,YAChB;AAAA,YAAE;AAAA,UAAA,GAER;AAAA,UACAD,kCAAAA,KAAC,SAAM,EAAA,WAAU,gEACX,UAAA;AAAA,YAAAC,kCAAA;AAAA,cAAC;AAAA,cAAA;AAAA,gBACK,MAAK;AAAA,gBACL,MAAK;AAAA,gBACL,OAAM;AAAA,gBACN,UAAS,qCAAU,YAAW;AAAA,gBAC9B,UAAU;AAAA,gBACV,WAAU;AAAA,cAAA;AAAA,YAChB;AAAA,YAAE;AAAA,UAAA,EAER,CAAA;AAAA,QAAA,EACN,CAAA;AAAA,MAAA,EACN,CAAA;AAAA,IAAA,GACN;AAAA,IAGAD,kCAAAA,KAAC,OAAI,EAAA,WAAU,uDACT,UAAA;AAAA,MAAAC,kCAAA;AAAA,QAAC;AAAA,QAAA;AAAA,UACK,SAAS;AAAA,UACT,WAAU;AAAA,UACf,UAAA;AAAA,QAAA;AAAA,MAED;AAAA,MACAD,kCAAA;AAAA,QAAC;AAAA,QAAA;AAAA,UACK,SAAS;AAAA,UACT,UAAU;AAAA,UACV,WAAW,qBAAqB,UAAU,gBAAgB,aAAa;AAAA,UAEtE,UAAA;AAAA,YAAA,UAAU,cAAc;AAAA,YAAO;AAAA,UAAA;AAAA,QAAA;AAAA,MAAA;AAAA,IACtC,EACN,CAAA;AAAA,EAAA,EAAA,CACN,EACN,CAAA;AAEZ;AC9GA,MAAM,kBAAkB;AAAA,EAClB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACN;AACA,MAAM,cAA0C,CAAC;AAAA,EAC3C;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACN,MAAM;AACM,QAAA,mBAAmB,CAAC,UAAkB;AACtC,kBAAc,KAAK;AAAA,EACzB;AACM,QAAA,uBAAuB,CAAC,gBAAwB;AAChD,gBAAY,WAAW;AAAA,EAC7B;AACM,QAAA,mBAAmB,CAAC,eAAuB;AAC3C,eAAW,UAAU;AAAA,EAC3B;AACA,QAAM,eAAe,WAAW;AAC1B,QAAA,aAAa,CAAC,eAA4B;AACpC,UAAA,WAAW,IAAI,SAAS;AAC9B,YAAQ,IAAI,mBAAmB;AACtB,aAAA,OAAO,cAAc,WAAW;AACzC,aAAS,OAAO,aAAa,KAAK,UAAU,UAAU,CAAC;AACvD,iBAAa,OAAO,UAAU,EAAE,QAAQ,QAAQ;AAAA,EACtD;AACM,QAAA,eAAe,CAAC,eAA4B;AACtC,UAAA,WAAW,IAAI,SAAS;AACrB,aAAA,OAAO,cAAc,cAAc;AAC5C,aAAS,OAAO,WAAW,WAAW,GAAG,UAAU;AACnD,aAAS,OAAO,YAAY,SAAS,SAAA,CAAU;AAC/C,iBAAa,OAAO,UAAU,EAAE,QAAQ,QAAQ;AAAA,EACtD;AAEA,SAEYA,kCAAA,KAAAM,4BAAA,EAAA,UAAA;AAAA,IAACN,kCAAAA,KAAA,OAAA,EAAI,WAAU,6BACT,UAAA;AAAA,MAAAC,kCAAA;AAAA,QAAC;AAAA,QAAA;AAAA,UACK,aAAY;AAAA,UACZ,OAAO;AAAA,UACP,UAAU,CAAC,MAAM,iBAAiB,EAAE,OAAO,KAAK;AAAA,UAChD,WAAU;AAAA,QAAA;AAAA,MAChB;AAAA,MACCD,kCAAA,KAAA,QAAA,EAAO,OAAO,UAAU,eAAe,sBAClC,UAAA;AAAA,QAAAC,kCAAAA,IAAC,iBAAc,WAAU,aACnB,gDAAC,aAAY,EAAA,aAAY,kBAAiB,EAChD,CAAA;AAAA,+CACC,eACK,EAAA,UAAA;AAAA,UAACA,kCAAA,IAAA,YAAA,EAAW,OAAM,KAAI,UAAU,cAAA;AAAA,UAC/BA,kCAAA,IAAA,YAAA,EAAW,OAAM,MAAK,UAAW,eAAA;AAAA,UACjCA,kCAAA,IAAA,YAAA,EAAW,OAAM,MAAK,UAAW,eAAA;AAAA,UACjCA,kCAAA,IAAA,YAAA,EAAW,OAAM,MAAK,UAAW,cAAA,CAAA;AAAA,QAAA,EACxC,CAAA;AAAA,MAAA,EACN,CAAA;AAAA,IAAA,GACN;AAAA,IACAA,kCAAA;AAAA,MAAC;AAAA,MAAA;AAAA,QACK,SAAS;AAAA,QACT,MAAM;AAAA,QACN,WAAW,CAAC,QACLD,kCAAA,KAAA,MAAA,EAAgB,WAAU,YACrB,UAAA;AAAA,UAAAC,kCAAA,IAAC,MAAG,EAAA,WAAU,wDAAwD,UAAA,IAAI,IAAG;AAAA,UAC5EA,kCAAA,IAAA,MAAA,EAAG,WAAU,wDAAwD,qCAAK,MAAK;AAAA,UAC/EA,kCAAA,IAAA,MAAA,EAAG,WAAU,uDAAuD,qCAAK,MAAK;AAAA,UAC/EA,kCAAAA,IAAC,QAAG,WAAU,uDACP,sCAAK,UACAD,kCAAA,KAAC,QAAK,EAAA,WAAU,uGACV,UAAA;AAAA,YAAAC,kCAAA;AAAA,cAAC;AAAA,cAAA;AAAA,gBACK,WAAU;AAAA,gBACV,MAAK;AAAA,gBACL,SAAQ;AAAA,gBACR,OAAM;AAAA,gBAEN,UAAAA,kCAAA;AAAA,kBAAC;AAAA,kBAAA;AAAA,oBACK,UAAS;AAAA,oBACT,GAAE;AAAA,oBACF,UAAS;AAAA,kBAAA;AAAA,gBAAA;AAAA,cACf;AAAA,YACN;AAAA,YAAM;AAAA,UAAA,EAEZ,CAAA,IAEAD,kCAAAA,KAAC,QAAK,EAAA,WAAU,mGACV,UAAA;AAAA,YAAAC,kCAAA;AAAA,cAAC;AAAA,cAAA;AAAA,gBACK,WAAU;AAAA,gBACV,MAAK;AAAA,gBACL,SAAQ;AAAA,gBACR,OAAM;AAAA,gBAEN,UAAAA,kCAAA;AAAA,kBAAC;AAAA,kBAAA;AAAA,oBACK,UAAS;AAAA,oBACT,GAAE;AAAA,oBACF,UAAS;AAAA,kBAAA;AAAA,gBAAA;AAAA,cACf;AAAA,YACN;AAAA,YAAM;AAAA,UAAA,EAAA,CAEZ,EAEZ,CAAA;AAAA,UACAA,kCAAAA,IAAC,MAAG,EAAA,WAAU,wCACR,UAAAA,kCAAA;AAAA,YAAC;AAAA,YAAA;AAAA,cACK,SAAQ;AAAA,cACR,MAAK;AAAA,cACL,WAAU;AAAA,cACV,SAAS,MAAM;AACL,oBAAA,QAAQ,4CAA4C,GAAG;AACrD,+BAAa,GAAG;AAAA,gBAAA;AAAA,cAE5B;AAAA,cACA,OAAO,EAAE,WAAW,WAAW;AAAA,cAE/B,UAAAA,kCAAAA,IAAC,OAAM,EAAA,MAAM,GAAI,CAAA;AAAA,YAAA;AAAA,UAAA,GAE7B;AAAA,UACAA,kCAAAA,IAAC,MAAG,EAAA,WAAU,wCACR,UAAAA,kCAAA,IAAC,QAAO,EAAA,OAAM,QAAO,MAAM,IAAI,SAAS,MAAM;AACxC,4BAAgB,GAAG;AACnB,+BAAmB,IAAI;AAAA,aAC1B,EACT,CAAA;AAAA,QAAA,EAAA,GA3DG,IAAI,EA4Db;AAAA,MAAA;AAAA,IAEZ;AAAA,IACAD,kCAAAA,KAAC,OAAI,EAAA,WAAU,qFACT,UAAA;AAAA,MAACA,kCAAAA,KAAA,MAAA,EAAG,WAAU,YAAW,UAAA;AAAA,QAAA;AAAA,QAAe,UAAU;AAAA,MAAA,GAAE;AAAA,MACpDC,kCAAAA,IAAC,OAAI,EAAA,WAAU,mBACT,UAAAA,kCAAA;AAAA,QAAC;AAAA,QAAA;AAAA,UACK,YAAY,OAAO,QAAQ;AAAA,UAC3B,aAAa;AAAA,UACb,cAAc;AAAA,QAAA;AAAA,MAAA,EAE1B,CAAA;AAAA,IAAA,GACN;AAAA,IACAA,kCAAA;AAAA,MAAC;AAAA,MAAA;AAAA,QACK,QAAQ;AAAA,QACR,MAAM,gBAAgB,CAAC;AAAA,QACvB,SAAS,MAAM;AACT,6BAAmB,KAAK;AACxB,0BAAgB,CAAA,CAAE;AAAA,QACxB;AAAA,QACA,QAAQ,MAAM;AAAA,QACd;AAAA,QACA,QAAQ,eAAe,gBAAgB;AAAA,MAAA;AAAA,IAC7C;AAAA,IACAA,kCAAAA,IAAC,UAAO,WAAU,sDAAqD,SAAS,MAAM,mBAAmB,IAAI,GAAG,UAAY,eAAA,CAAA;AAAA,EAAA,GAClI;AAEZ;AC1KA,MAAM,eAAyC,CAAC,EAAE,QAAQ,MAAM,SAAS,QAAQ,UAAU,aAAa;AAC5F,QAAA,CAAC,UAAU,WAAW,IAAIE,aAAA;AAAA,IAC1B,QACG;AAAA,MACG,cAAc;AAAA,MACd,aAAa;AAAA,MACb,WAAW;AAAA,MACX,QAAQ;AAAA,IAAA;AAAA,EAEpB;AACAC,eAAAA,UAAU,MAAM;AACV,QAAI,QAAQ,QAAQ;AACd,kBAAY,IAAI;AAAA,IAAA,OAEjB;AACa,kBAAA;AAAA,QACN,cAAc;AAAA,QACd,aAAa;AAAA,QACb,WAAW;AAAA,QACX,QAAQ;AAAA,MAAA,CACb;AAAA,IAAA;AAAA,EACP,GACH,CAAC,MAAM,MAAM,CAAC;AAGX,QAAA,EAAE,UAAU,IAAI,SAAS;AAC/B,QAAM,mBAAmB,WAAW;AACpCA,eAAAA,UAAU,MAAM;;AACV,QAAI,iBAAiB,UAAQ,sBAAiB,SAAjB,mBAAuB,SAAQ;AACtD,gBAAU,sBAAsB,SAAS;AACjC,cAAA;AACI,kBAAA;AAAA,QACN,cAAc;AAAA,QACd,aAAa;AAAA,QACb,WAAW;AAAA,QACX,QAAQ;AAAA,MAAA,CACb;AAAA,IAAA,OAEF;AACK,WAAA,sBAAiB,SAAjB,mBAAuB,OAAO;AAClB,mBAAA,sBAAiB,SAAjB,mBAAuB,OAAO,OAAO;AACvC,gBAAA;AAAA,MAAA;AAAA,IACd;AAAA,EACN,GACH,CAAC,iBAAiB,IAAI,CAAC;AAKpB,QAAA,eAAe,CAAC,MAA+D;AAC/E,UAAM,EAAE,MAAM,MAAM,IAAI,EAAE;AAC1B,gBAAY,CAAC,cAAc;AAAA,MACrB,GAAG;AAAA,MACH,CAAC,IAAI,GAAG,SAAS,WAAW,UAAU,SAAS;AAAA,IAAA,EACnD;AAAA,EACR;AACA,QAAM,aAAa,MAAM;AACb,UAAA,cAAc,IAAI,SAAS;AACrB,gBAAA,OAAO,cAAc,cAAc;AACnC,gBAAA,OAAO,YAAY,QAA6B;AAC5D,gBAAY,OAAO,iBAAiB,KAAK,UAAU,QAAQ,CAAC;AAC5D,gBAAY,OAAO,cAAc,WAAW,kBAAkB,SAAS,OAAO;AAC9E,qBAAiB,OAAO,aAAa,EAAE,QAAQ,QAAQ;AAAA,EAC7D;AACI,MAAA,CAAC,OAAe,QAAA;AAEd,QAAA,UAAU,iBAAiB,UAAU;AAE3C,QAAM,cAAc,MAAM;AACpB,WACM,SAAS,aAAa,KAAK,MAAM,MACjC,SAAS,YAAY,KAAA,MAAW,MAChC,SAAS,UAAU,KAAW,MAAA;AAAA,EAE1C;AAKA,SACOJ,kCAAAA,KAAA,QAAA,EAAO,MAAM,QAAQ,cAAc,SAE7B,UAAA;AAAA,IAAA,WAAYC,kCAAA,IAAA,eAAA,EAAc,SAAkB,MAAM,IAAI;AAAA,IACvDD,kCAAAA,KAAC,eAAc,EAAA,WAAU,2DACnB,UAAA;AAAA,MAACC,kCAAA,IAAA,aAAA,EAAY,WAAU,+CAA+C,UAAO,QAAA;AAAA,MAC7ED,kCAAAA,KAAC,OAAI,EAAA,WAAU,qHAET,UAAA;AAAA,QAACC,kCAAAA,IAAA,OAAA,EAAI,WAAU,aACR,UAAA;AAAA,UACK,EAAE,IAAI,GAAG,MAAM,gBAAyB,MAAM,QAAQ,OAAO,eAAe;AAAA,UAC5E,EAAE,IAAI,GAAG,MAAM,eAAwB,MAAM,QAAQ,OAAO,cAAc;AAAA,UAC1E,EAAE,IAAI,GAAG,MAAM,aAAsB,MAAM,QAAQ,OAAO,YAAY;AAAA,QAAA,EAE1E,IAAI,CAAC,UACAD,kCAAA,KAAA,OAAA,EAAqB,WAAU,mDAC1B,UAAA;AAAA,UAAAC,kCAAA,IAAC,SAAM,EAAA,WAAU,qDAAqD,UAAA,MAAM,OAAM;AAAA,UAGlFA,kCAAA;AAAA,YAAC;AAAA,YAAA;AAAA,cACK,MAAM,MAAM;AAAA,cACZ,MAAM,MAAM;AAAA,cACZ,QAAO,qCAAW,MAAM,UAAkC;AAAA,cAAI,UAAU;AAAA,cACxE,WAAU;AAAA,cACV,aAAa,SAAS,MAAM,KAAK;AAAA,YAAA;AAAA,UAAA;AAAA,QATnC,EAAA,GAAA,MAAM,IAYhB,CACL,GACP;AAAA,QAGAD,kCAAAA,KAAC,OAAI,EAAA,WAAU,mDACT,UAAA;AAAA,UAACC,kCAAA,IAAA,SAAA,EAAM,WAAU,qDAAoD,UAAa,iBAAA;AAAA,UAClFD,kCAAAA,KAAC,OAAI,EAAA,WAAU,8BACT,UAAA;AAAA,YAACA,kCAAAA,KAAA,SAAA,EAAM,WAAU,gEACX,UAAA;AAAA,cAAAC,kCAAA;AAAA,gBAAC;AAAA,gBAAA;AAAA,kBACK,MAAK;AAAA,kBACL,MAAK;AAAA,kBACL,OAAM;AAAA,kBACN,UAAS,qCAAU,YAAW;AAAA,kBAC9B,UAAU;AAAA,kBACV,WAAU;AAAA,kBACV,WAAS;AAAA,gBAAA;AAAA,cACf;AAAA,cAAE;AAAA,YAAA,GAER;AAAA,YACAD,kCAAAA,KAAC,SAAM,EAAA,WAAU,gEACX,UAAA;AAAA,cAAAC,kCAAA;AAAA,gBAAC;AAAA,gBAAA;AAAA,kBACK,MAAK;AAAA,kBACL,MAAK;AAAA,kBACL,OAAM;AAAA,kBACN,UAAS,qCAAU,YAAW;AAAA,kBAC9B,UAAU;AAAA,kBACV,WAAU;AAAA,gBAAA;AAAA,cAChB;AAAA,cAAE;AAAA,YAAA,EAER,CAAA;AAAA,UAAA,EACN,CAAA;AAAA,QAAA,EACN,CAAA;AAAA,MAAA,GACN;AAAA,MAGAD,kCAAAA,KAAC,OAAI,EAAA,WAAU,uDACT,UAAA;AAAA,QAAAC,kCAAA;AAAA,UAAC;AAAA,UAAA;AAAA,YACK,SAAS;AAAA,YACT,WAAU;AAAA,YACf,UAAA;AAAA,UAAA;AAAA,QAED;AAAA,QACAA,kCAAA;AAAA,UAAC;AAAA,UAAA;AAAA,YACK,SAAS;AAAA,YACT,UAAU,WAAW,CAAC,YAAY;AAAA,YAClC,WAAW,2FAA2F,WAAW,CAAC,gBAC1G,mCACA,gEACF;AAAA,YAEL,oBAAU,cAAc;AAAA,UAAA;AAAA,QAAA;AAAA,MAC/B,EACN,CAAA;AAAA,IAAA,EACN,CAAA;AAAA,EAAA,GACN;AAEZ;AChKA,MAAM,oBAAoB;AAAA,EACpB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACN;AACA,MAAM,eAA8C,CAAC;AAAA,EAC/C;AAAA,EACA;AACN,MAAM;AACA,QAAM,mBAAmB,WAAW;AACpC,QAAM,CAAC,iBAAiB,kBAAkB,IAAIE,aAAAA,SAAS,KAAK;AAC5D,QAAM,CAAC,iBAAiB,kBAAkB,IAAIA,sBAA0B;AACxE,QAAM,CAAC,WAAW,YAAY,IAAIA,aAAAA,SAAS,KAAK;AAChD,QAAM,CAAC,YAAY,aAAa,IAAIA,aAAAA,SAAS,EAAE;AAC/C,QAAM,CAAC,oBAAoB,qBAAqB,IAAIA,aAAAA,SAA4B,CAAA,CAAE;AAC5E,QAAA,eAAe,CAAC,cAA+B;;AACzC,UAAA,WAAW,IAAI,SAAS;AACrB,aAAA,OAAO,cAAc,WAAW;AACzC,aAAS,OAAO,WAAS,4CAAW,OAAX,mBAAe,eAAc,EAAE;AAAG,qBAAiB,OAAO,UAAU,EAAE,QAAQ,QAAQ;AAAA,EACrH;AACM,QAAA,kBAAkB,CAAC,QAAyB;AAC5C,uBAAmB,GAAG;AACtB,iBAAa,IAAI;AACjB,uBAAmB,IAAI;AAAA,EAE7B;AACA,QAAM,iBAAiB,MAAM;AACvB,uBAAmB,MAAS;AAC5B,iBAAa,KAAK;AAClB,uBAAmB,IAAI;AAAA,EAE7B;AACM,QAAA,UAAU,iBAAiB,UAAU;AACrC,QAAA,sBAAsB,YAAY,YAAY,GAAG;AAMvDC,eAAAA,UAAU,MAAM;AACV,QAAI,oBAAoB,UAAU,KAAK,wBAAwB,IAAI;AAC7D,YAAM,WAAW,cAAc;AAAA,QAAO,CAAC,SACjC,CAAC,KAAK,aAAa,KAAK,YAAY,EAAE;AAAA,UAChC,CAAC,UAAU,+BAAO,cAAc,SAAS,oBAAoB,YAAa;AAAA,QAAA;AAAA,MAEtF;AACA,4BAAsB,QAAQ;AAAA,IAAA,OAE/B;AACC,4BAAsB,aAAa;AAAA,IAAA;AAAA,EACzC,GAEH,CAAC,qBAAqB,aAAa,CAAC;AAEtB,cAAY;AAE7B,SAEYJ,kCAAA,KAAAM,4BAAA,EAAA,UAAA;AAAA,IAACN,kCAAAA,KAAA,OAAA,EAAI,WAAU,6BACR,UAAA;AAAA,MAAA,WAAYC,kCAAA,IAAA,eAAA,EAAc,SAAkB,MAAM,IAAI;AAAA,MACvDA,kCAAA;AAAA,QAAC;AAAA,QAAA;AAAA,UACK,aAAY;AAAA,UACZ,OAAO;AAAA,UAEP,MAAK;AAAA,UACL,UAAU,CAAC,MAAM,cAAc,EAAE,OAAO,KAAK;AAAA,UAC7C,WAAU;AAAA,QAAA;AAAA,MAAA;AAAA,IAChB,GAaN;AAAA,IACAA,kCAAA;AAAA,MAAC;AAAA,MAAA;AAAA,QACK,SAAS;AAAA,QACT,MAAM;AAAA,QACN,WAAW,CAAC,QACLD,kCAAA,KAAA,MAAA,EAAgB,WAAU,YACrB,UAAA;AAAA,UAAAC,kCAAA,IAAC,MAAG,EAAA,WAAU,wDAAwD,UAAA,IAAI,IAAG;AAAA,gDAC5E,MAAG,EAAA,WAAU,oFAAqF,WAAA,2BAAK,iBAAgB,KAAI;AAAA,gDAC3H,MAAG,EAAA,WAAU,uDAAuD,WAAA,2BAAK,gBAAe,KAAI;AAAA,UAC5FA,kCAAA,IAAA,MAAA,EAAG,WAAU,uDAAuD,qCAAK,WAAU;AAAA,UACpFA,kCAAAA,IAAC,QAAG,WAAU,uDACP,sCAAK,UACAD,kCAAA,KAAC,QAAK,EAAA,WAAU,uGACV,UAAA;AAAA,YAAAC,kCAAA;AAAA,cAAC;AAAA,cAAA;AAAA,gBACK,WAAU;AAAA,gBACV,MAAK;AAAA,gBACL,SAAQ;AAAA,gBACR,OAAM;AAAA,gBAEN,UAAAA,kCAAA;AAAA,kBAAC;AAAA,kBAAA;AAAA,oBACK,UAAS;AAAA,oBACT,GAAE;AAAA,oBACF,UAAS;AAAA,kBAAA;AAAA,gBAAA;AAAA,cACf;AAAA,YACN;AAAA,YAAM;AAAA,UAAA,EAEZ,CAAA,IAEAD,kCAAAA,KAAC,QAAK,EAAA,WAAU,mGACV,UAAA;AAAA,YAAAC,kCAAA;AAAA,cAAC;AAAA,cAAA;AAAA,gBACK,WAAU;AAAA,gBACV,MAAK;AAAA,gBACL,SAAQ;AAAA,gBACR,OAAM;AAAA,gBAEN,UAAAA,kCAAA;AAAA,kBAAC;AAAA,kBAAA;AAAA,oBACK,UAAS;AAAA,oBACT,GAAE;AAAA,oBACF,UAAS;AAAA,kBAAA;AAAA,gBAAA;AAAA,cACf;AAAA,YACN;AAAA,YAAM;AAAA,UAAA,EAAA,CAEZ,EAEZ,CAAA;AAAA,UACAA,kCAAAA,IAAC,MAAG,EAAA,WAAU,wCACR,UAAAA,kCAAA;AAAA,YAAC;AAAA,YAAA;AAAA,cACK,SAAQ;AAAA,cACR,MAAK;AAAA,cACL,WAAU;AAAA,cACV,SAAS,MAAM;AACT,oBAAI,QAAQ,mDAAmD,IAAI,WAAW,EAAE,GAAG;AAC7E,+BAAa,GAAG;AAAA,gBAAA;AAAA,cAE5B;AAAA,cACA,OAAO,EAAE,WAAW,WAAW;AAAA,cAE/B,UAAAA,kCAAAA,IAAC,OAAM,EAAA,MAAM,GAAI,CAAA;AAAA,YAAA;AAAA,UAAA,GAE7B;AAAA,UACCA,kCAAA,IAAA,MAAA,EAAG,WAAU,wCACR,gDAAC,QAAO,EAAA,OAAM,QAAO,MAAM,IAAI,SAAS,MAAM,gBAAgB,GAAG,GAAG,EAC1E,CAAA;AAAA,QAAA,EAAA,GAzDG,IAAI,EA0Db;AAAA,MAAA;AAAA,IAEZ;AAAA,IAWAA,kCAAA;AAAA,MAAC;AAAA,MAAA;AAAA,QACK,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,MAAM,YAAY,kBAAkB;AAAA,QACpC,SAAS,MAAM,mBAAmB,KAAK;AAAA,QACvC,QAAQ,YAAY,mBAAmB;AAAA,QAAiB;AAAA,MAAA;AAAA,IAAoB;AAAA,IAClFA,sCAAC,UAAO,WAAU,sDAAqD,SAAS,MAAM,eAAA,GAAkB,UAAe,kBAAA,CAAA;AAAA,EAAA,GAC7H;AAEZ;AC9Ha,MAAAQ,kBAAmBM,WAAkB;AAC1C,QAAAC,SAAS,CAAC,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,SAAS;AAC/F,SAAAA,OAAOD,QAAQC,OAAOC,MAAM;AACrC;AA2fA,SAAwBC,oBAAoB;AACpC,QAAA;AAAA,IAAEC;AAAAA,IAAkBC;AAAAA,IAAUC;AAAAA,IAAYC;AAAAA,IAAWC;AAAAA,IAAaC;AAAAA,IAAcC;AAAAA,IAAKC;AAAAA,IAAYC;AAAAA,IAAOC;AAAAA,IAAWC;AAAAA,IAAgBC;AAAAA,IAAaC;AAAAA,IAAoBlB;AAAAA,IAAQmB;AAAAA,IAAYC;AAAAA,IAAaC;AAAAA,IAAgBC;AAAAA,IAAYC;AAAAA,EAAc,IAAIC,cAA0B;AAElQd,gBAAcA,YAAYN,SAAS;AACpD,QAAMqB,WAAWC,YAAY;AACvB,QAAAC,aAAaR,cAAcS,MAAMC,QAAQV,UAAU,IAAIA,WAAWW,SAAS,UAAU,IAAI;AAEzF,QAAAC,kBAAmBC,YAAmB;AAC1C,QAAIA,WAAW,oBAAoB;AAExBP,eAAA,iDAAiDlB,QAAQ,eAAeC,UAAU,cAAcwB,MAAM,cAAcjB,SAAS,EAAE;AAAA,IAC1I,WACSiB,WAAW,kBAAkB;AAC3BP,eAAA,+CAA+ClB,QAAQ,eAAeC,UAAU,cAAcwB,MAAM,cAAcjB,SAAS,EAAE;AAAA,IACxI,OACK;AAEMU,eAAA,gCAAgClB,QAAQ,eAAeC,UAAU,cAAcwB,MAAM,cAAcjB,SAAS,EAAE;AAAA,IACzH;AAAA,EACF;AACM,QAAA;AAAA,IAAEkB;AAAAA,EAAU,IAAIC,SAAS;AAC/B,QAAM,CAACC,cAAcC,eAAe,IAAI9C,aAAAA,SAAsB,oBAAI+C,KAAK;AACvE,QAAM,CAACC,WAAWC,YAAY,IAAIjD,aAAAA,SAAS,IAAI;AAC/C,QAAM,CAACkD,aAAaC,cAAc,IAAInD,aAAAA,SAAS,KAAK;AACpD,QAAM,CAACoD,iBAAiBC,kBAAkB,IAAIrD,aAAAA,SAAS,KAAK;AAC5D,QAAM,CAACsD,KAAKC,MAAM,IAAIvD,aAAAA,SAAiC,IAAI;AAC3D,QAAM,CAACwD,WAAWC,YAAY,IAAIzD,aAAAA,SAAS,KAAK;AAC1C,QAAA0D,SAASlD,aAAY,YAACmD,iBAAiC;AAC3DJ,WAAOI,WAAW;AAClBF,iBAAa,IAAI;AAAA,EACnB,GAAG,EAAE;AAKL,QAAM,CAACG,qBAAqBC,sBAAsB,IAAI7D,aAAAA,SAAS,KAAK;AACpE,QAAM,CAAC8D,iBAAiBC,kBAAkB,IAAI/D,sBAA0B;AAAA,IACtEgE,UAAU;AAAA,IACVC,WAAW;AAAA,EACb,CAAC;AACD,QAAMC,UAAUC,WAAW;AAC3B,QAAMC,0BAA0B5D,aAAA,YAAa6D,WAAmB;AAAER,2BAAuBQ,KAAK;AAAA,EAAG,GAAG,EACpG;AAEApE,eAAAA,UAAU,MAAM;AACd,QAAIqD,OAAOlC,aAAa;AACtB,YAAMkD,kBAAkBlD,YAAYkC,IAAKiB,WAAU;AAAA,QACjDC,IAAID,KAAKE;AAAAA,QACTC,OAAOH,KAAKA,KAAKI,UAAUpE,cAAcgE,KAAKA,KAAKI,OAAO,IAAI,CAAA;AAAA,MAChE,EAAE;AACc7B,sBAAA,IAAIC,IAAIuB,gBAAgBhB,IAAKqB,aAAYA,QAAQH,EAAE,CAAC,CAAC;AAAA,IACvE;AAAA,EACF,GAAG,CAAClB,KAAKlC,WAAW,CAAC;AAErBnB,eAAAA,UAAU,MAAM;AAEd,QAAIe,oBAAoBI,aAAa;AACnC,UAAIoC,WAAW;AACbP,qBAAa,KAAK;AAAA,MACpB;AAAA,IACF;AAAA,EACC,GAAA,CAACjC,kBAAkBI,aAAaoC,SAAS,CAAC;AAEvC,QAAAoB,YAAYpE,aAAAA,YAAY,MAAM;AAClC+C,WAAO,IAAI;AACXE,iBAAa,KAAK;AAAA,EACpB,GAAG,EAAE;AACC,QAAAoB,mBAAoBH,WAAuC;AAC/D,UAAMI,YAAYJ,MAAMpB,IAAKyB,UAASA,KAAKC,GAAG;AAC9C,UAAMC,aAAaP,MAAMpB,IAAKyB,UAASA,KAAKG,GAAG;AAEzC,UAAAC,SAASL,UAAUM,OAAO,CAACC,GAAGC,MAAMD,IAAIC,GAAG,CAAC;AAC5C,UAAAC,SAASN,WAAWG,OAAO,CAACC,GAAGC,MAAMD,IAAIC,GAAG,CAAC;AAC5C,WAAA;AAAA,MACLN,KAAKG,SAASL,UAAUhE;AAAAA,MACxBoE,KAAKK,SAASN,WAAWnE;AAAAA,IAC3B;AAAA,EACF;AACA,QAAM0E,cAAc,CAClB,MACA,QACA,iBACA,iBACA,kBACA,SACA,UACA,EAAA;AAEF,QAAMC,mBAAmB,CACvB,QACA,YACA,YACA,UACA,cACA,SACA,QACA,oBACA,kBACA,qBACA,EAAA;AAEF,QAAMC,WAAWtD,YAAY;AACvB,QAAAuD,eAAe,OAAOC,SAAmD;AACzE,QAAA;AACI,YAAAC,WAAW,IAAIC,SAAS;AACrBD,eAAAE,OAAO,cAAc,QAAQ;AACtCF,eAASE,OAAO,YAAY9E,SAAS+E,SAAA,CAAU;AAE/CH,eAASE,OAAO,WAAWE,KAAKC,UAAUN,IAAI,CAAC;AACvCO,cAAAC,IAAI,kCAAkCR,IAAI;AAClD,iBAAW,CAACS,KAAKC,KAAK,KAAKT,SAASU,WAAW;AACrCJ,gBAAAC,IAAIC,KAAKC,KAAK;AAAA,MACxB;AAEApC,cAAQsC,OAAOX,UAAU;AAAA,QAAEY,QAAQ;AAAA,MAAO,CAAC;AAC3CN,cAAQC,IAAI,kCAAkC;AAC9CzD,gBAAU,6CAA6C,SAAS;AAAA,aACzDzC,OAAO;AACNiG,cAAAjG,MAAM,0BAA0BA,KAAK;AAC7CyC,gBAAU,yCAAyC,OAAO;AAAA,IAC5D;AAAA,EACF;AACM,QAAA+D,0BAA0B,OAAOC,WAAmBL,UAAe;AACnE,QAAA;AACI,YAAAT,WAAW,IAAIC,SAAS;AACrBD,eAAAE,OAAO,cAAc,eAAe;AACpCF,eAAAE,OAAO,UAAU,qBAAqB;AAC/CF,eAASE,OAAO,YAAY9E,SAAS+E,SAAA,CAAU;AACtCH,eAAAE,OAAO,aAAaY,SAAS;AACtCd,eAASE,OAAO,SAASO,MAAMN,SAAA,CAAU;AACzC,YAAM9B,QAAQsC,OAAOX,UAAU;AAAA,QAAEY,QAAQ;AAAA,MAAO,CAAC;AAEjD9D,gBAAU,GAAGgE,UAAUC,QAAQ,YAAY,KAAK,CAAC,yBAAyB,SAAS;AAAA,aAE5E1G,OAAO;AACdyC,gBAAU,oBAAoBgE,UAAUC,QAAQ,YAAY,KAAK,CAAC,IAAI,OAAO;AAAA,IAC/E;AAAA,EACF;AACM,QAAA,CAACC,kBAAkBC,mBAAmB,IAAI9G,aAAA,SAAkC,MAEhFuB,yCAAY6D,OAAO,CAAC2B,KAAKC,UAAS;AAC5BA,QAAAA,+BAAMtG,MAAM,IAAIsG,+BAAMC;AACnB,WAAAF;AAAAA,EAET,GAAG,CAA6B,EAElC;AACM,QAAAG,mBAAoBxG,aAAmB;AACrC,UAAAmF,WAAW,IAAIC,SAAS;AACrBD,aAAAE,OAAO,UAAUrF,OAA2B;AAC5CmF,aAAAE,OAAO,UAAU,YAAY;AACtC7B,YAAQsC,OAAOX,UAAU;AAAA,MAAEY,QAAQ;AAAA,IAAM,CAAC;AACtC,QAAAvC,QAAQG,UAAU,QAAQ;AAC5B1B,gBAAU,gCAAgC,SAAS;AAAA,IACrD;AAAA,EACF;AACM,QAAAwE,mBAAmB,OAAOzG,YAAmB;AAEjDoG,wBAAqBM,WAAU;AAAA,MAC7B,GAAGA;AAAAA,MACH,CAAC1G,OAAM,GAAG,CAAC0G,KAAK1G,OAAM;AAAA,IACxB,EAAE;AAEFwG,qBAAiBxG,OAAM;AAAA,EACzB;AACM,QAAA2G,eAAgBC,YAAmB;AACjC,UAAAzB,WAAW,IAAIC,SAAS;AACrBD,aAAAE,OAAO,YAAY9E,QAA6B;AAChD4E,aAAAE,OAAO,UAAUuB,MAA2B;AAC5CzB,aAAAE,OAAO,UAAU,YAAY;AACtC7B,YAAQsC,OAAOX,UAAU;AAAA,MAAEY,QAAQ;AAAA,IAAO,CAAC;AAAA,EAC7C;AACA,QAAM,CAACO,MAAMO,OAAO,IAAIvH,sBAA2B;AAC7C,QAAAwH,aAAcC,SAAa;AAC/BF,YAAQE,GAAG;AACXpE,uBAAmB,IAAI;AAAA,EACzB;AACM,QAAAqE,oBAAqBC,aAAgC;AACnD,UAAA9B,WAAW,IAAIC,SAAS;AACrBD,aAAAE,OAAO,UAAU,kBAAkB;AAC5CF,aAASE,OAAO,WAAWE,KAAKC,UAAUyB,OAAO,CAAC;AAClD9B,aAASE,OAAO,YAAY9E,SAAS+E,SAAA,CAAU;AAE/C9B,YAAQsC,OAAOX,UAAU;AAAA,MAAEY,QAAQ;AAAA,IAAO,CAAC;AAAA,EAC7C;AACM,QAAAmB,UAAU1D,QAAQG,UAAU;AAClC,QAAM,CAACwD,YAAYC,aAAa,IAAI9H,aAAAA,SAAS,EAAE;AAC/C,QAAM,CAAC+H,UAAUC,WAAW,IAAIhI,aAAAA,SAAS,IAAI;AAC7C,QAAM,CAACiI,SAASC,UAAU,IAAIlI,aAAAA,SAAS,CAAC;AAElC,QAAAmI,uBAAwBC,iBAAwB;AACpDJ,gBAAYI,WAAW;AAEvB1C,aAAS,aAAazE,QAAQ,eAAeC,UAAU,cAAcC,SAAS,cAAcM,SAAS,SAASK,WAAW,aAAasG,WAAW,YAAYP,UAAU,EAAE;AAAA,EAC3K;AACM,QAAAQ,qBAAqBC,YAAYT,YAAY,GAAG;AAChD,QAAAU,mBAAoBjC,WAAkB;AAC1CwB,kBAAcxB,KAAK;AAAA,EAErB;AACArG,eAAAA,UAAU,MAAM;AACV,QAAAoI,mBAAmBvH,UAAU,GAAG;AAElC4E,eACE,aAAazE,QAAQ,eAAeC,UAAU,cAAcC,SAAS,cAAcM,SAAS,SAASK,WAAW,aAAaiG,QAAQ,YAAYS,mBAAmBH,kBAAkB,CAAC,EACzL;AAAA,IACF,OAAO;AAEL3C,eACE,aAAazE,QAAQ,eAAeC,UAAU,cAAcC,SAAS,cAAcM,SAAS,SAASK,WAAW,aAAaiG,QAAQ,EACvI;AAAA,IACF;AAAA,EAEF,GAAG,CAACM,kBAAkB,CAAC;AAEjB,QAAAI,mBAAoBL,iBAAwB;AACrCF,eAAAQ,OAAON,WAAW,CAAC;AAC9B1C,aAAS,aAAazE,QAAQ,eAAeC,UAAU,cAAcC,SAAS,cAAcM,SAAS,SAASiH,OAAON,WAAW,CAAC,aAAaL,QAAQ,YAAYF,UAAU,EAAE;AAAA,EAChL;AACA,QAAM,CAACc,cAAcC,eAAe,IAAI5I,aAAAA,SAAS,CAAA,CAAE;AACnD,QAAM,CAAC6I,iBAAiBC,kBAAkB,IAAI9I,aAAAA,SAAS,KAAK;AAC5D,QAAM,CAAC+I,cAAcC,eAAe,IAAIhJ,aAAAA,SAAsB,CAAA,CAAE;AAC1D,QAAAiJ,kBAAkBzI,yBAAY,OAAOiH,QAAoB;;AACzD,QAAA;AACF,YAAMyB,WAAW,MAAMC,MAAM,8BAA8B1B,IAAI2B,EAAE,EAAE;AAE/D,UAAA,CAACF,SAASG,IAAI;AACV,cAAA,IAAIC,MAAM,8BAA8B;AAAA,MAChD;AAEM,YAAAC,WAAW,MAAML,SAASM,KAAK;AACrBR,uBAAAO,0CAAUE,iBAAVF,mBAAwB3D,IAAI;AAC5CgD,sBAAgBnB,GAAG;AACnBqB,yBAAmB,IAAI;AAAA,aAChB5I,OAAO;AACNiG,cAAAjG,MAAM,wBAAwBA,KAAK;AAAA,IAC7C;AAAA,EACF,GAAG,EAAE;AACC,QAAAwJ,aAAcC,iBAAqB;AAC/BxD,YAAAC,IAAI,iBAAiBuD,WAAW;AAClC,UAAA9D,WAAW,IAAIC,SAAS;AACrBD,aAAAE,OAAO,UAAU,gBAAgB;AAC1CF,aAASE,OAAO,cAAcE,KAAKC,UAAUyD,WAAW,CAAC;AAChD9D,aAAAE,OAAO,UAAU4D,YAAYP,EAAE;AACxCvD,aAASE,OAAO,YAAY9E,SAAS+E,SAAA,CAAU;AAC/C9B,YAAQsC,OAAOX,UAAU;AAAA,MAAEY,QAAQ;AAAA,IAAM,CAAC;AAC1CqC,uBAAmB,KAAK;AAAA,EAC1B;AAIE,SAAAjJ,kCAAAA,KAAC,OAAI;AAAA,IAAA+J,WAAU;AAAA,IACZC,UAAA,CACCjC,WAAA9H,kCAAA,IAACgK;MAAclC;AAAAA,IAAkB,CAAA,GAEnC/H,kCAAA,KAAC;MAAG+J,WAAU;AAAA,MAAiCG,SAASA,MAAMrE,SAAS,wBAAwB;AAAA,MAAGmE,UAAA,CAAA,KAAE/J,kCAAA,IAAA,QAAA;AAAA,QAAK8J,WAAU;AAAA,QAAWC,UAAoB;AAAA,OAAA,GAAO,KAAChK,kCAAA,KAAC,QAAK;AAAA,QAAA+J,WAAU;AAAA,QAAWC,UAAA,CAAA3I,YAAW,GAAA;AAAA,MAAC,CAAA,GAAO,GAAA;AAAA,IAAC,CAAA,GACxMrB,kCAAA,KAAAmK,MAAA;AAAA,MAAK1D,OAAOnF;AAAAA,MAAW8I,eAAexH;AAAAA,MACrCoH,UAAA,CAAAhK,kCAAA,KAACqK,UACC;AAAA,QAAAL,UAAA,CAAC/J,kCAAA,IAAAqK,aAAA;AAAA,UAAY7D,OAAM;AAAA,UAAeuD,UAAc;AAAA,QAAA,CAAA,GAC/C/J,kCAAA,IAAAqK,aAAA;AAAA,UAAY7D,OAAM;AAAA,UAAcuD,UAAK;AAAA,QAAA,CAAA,GACrC/J,kCAAA,IAAAqK,aAAA;AAAA,UAAY7D,OAAM;AAAA,UAAauD,UAAK;AAAA,QAAA,CAAA,GACpC/J,kCAAA,IAAAqK,aAAA;AAAA,UAAY7D,OAAM;AAAA,UAAcuD,UAAW;AAAA,QAAA,CAAA,GAC3C/J,kCAAA,IAAAqK,aAAA;AAAA,UAAY7D,OAAM;AAAA,UAAmBuD,UAAiB;AAAA,QAAA,CAAA,GACtD/J,kCAAA,IAAAqK,aAAA;AAAA,UAAY7D,OAAM;AAAA,UAAgBuD,UAAe;AAAA,QAAA,CAAA,GACjD/J,kCAAA,IAAAqK,aAAA;AAAA,UAAY7D,OAAM;AAAA,UAAWuD,UAAS;AAAA,QAAA,CAAA,GACtC/J,kCAAA,IAAAqK,aAAA;AAAA,UAAY7D,OAAM;AAAA,UAAcuD,UAAa;AAAA,QAAA,CAAA,GAC7C/J,kCAAA,IAAAqK,aAAA;AAAA,UAAY7D,OAAM;AAAA,UAAiBuD,UAAc;AAAA,SAAA,GACjDxH,cAAcvC,kCAAA,IAACqK,aAAY;AAAA,UAAA7D,OAAM;AAAA,UAAgBuD,UAAY;AAAA,QAAA,CAAA,CAAA;AAAA,MAChE,CAAA,yCACCO,QAAO,CAAA,CAAA,GACRtK,kCAAAA,IAACuK,aAAY;AAAA,QAAA/D,OAAM;AAAA,QACjBuD,UAAA/J,kCAAA,IAACwK,kBAAA;AAAA,UACCC,mBAAmBjI,MAAMC,QAAQR,cAAc,IAAIA,iBAAiBA,iBAAiB,CAACA,cAAc,IAAI,CAAC;AAAA,UACzGd;AAAAA,QAEF,CAAA;AAAA,MACF,CAAA,GACAnB,kCAAA,IAACuK,aAAY;AAAA,QAAA/D,OAAM;AAAA,QACjBuD,UAAA/J,kCAAA,IAAC0K,aAAA;AAAA,UACCxI,YAAYM,MAAMC,QAAQP,UAAU,IAAIA,aAAaA,aAAa,CAACA,UAAU,IAAI,CAAC;AAAA,UAClF6F;AAAAA,UACAC;AAAAA,UACAC;AAAAA,UACAC;AAAAA,UACAC;AAAAA,UACAC;AAAAA,UACAW;AAAAA,UACAC;AAAAA,UACAH;AAAAA,UACAC;AAAAA,UACA3H;AAAAA,QACF,CAAA;AAAA,MACF,CAAA,GACAnB,kCAAA,IAACuK,aAAY;AAAA,QAAA/D,OAAM;AAAA,QACjBuD,UAAA/J,kCAAA,IAAC2K,cAAA;AAAA,UACCC,eAAepI,MAAMC,QAAQN,aAAa,IAAIA,gBAAgBA,gBAAgB,CAACA,aAAa,IAAI,CAAC;AAAA,UACjGhB;AAAAA,QACF,CAAA;AAAA,MACF,CAAA,GACApB,kCAAA,KAACwK,aAAY;AAAA,QAAA/D,OAAM;AAAA,QACjBuD,UAAA,CAAChK,kCAAA,KAAA,OAAA;AAAA,UAAI+J,WAAU;AAAA,UACbC,UAAA,CAAA/J,kCAAA,IAAC6K,OAAA;AAAA,YACCC,aAAY;AAAA,YACZtE,OAAOuB;AAAAA,YACPgD,MAAK;AAAA,YACLC,UAAWC,OAAMxC,iBAAiBwC,EAAEC,OAAO1E,KAAK;AAAA,YAChDsD,WAAU;AAAA,UAAA,CACZ,GAEC/J,kCAAA,KAAAoL,QAAA;AAAA,YAAO3E,OAAOyB;AAAAA,YAAUkC,eAAe9B;AAAAA,YACtC0B,UAAA,CAAA/J,kCAAA,IAACoL;cAActB,WAAU;AAAA,cACvBC,gDAACsB,aAAY;AAAA,gBAAAP,aAAY;AAAA,cAAiB,CAAA;AAAA,YAC5C,CAAA,0CACCQ,eACC;AAAA,cAAAvB,UAAA,CAAC/J,kCAAA,IAAAuL,YAAA;AAAA,gBAAW/E,OAAM;AAAA,gBAAIuD,UAAU;AAAA,cAAA,CAAA,GAC/B/J,kCAAA,IAAAuL,YAAA;AAAA,gBAAW/E,OAAM;AAAA,gBAAKuD,UAAW;AAAA,cAAA,CAAA,GACjC/J,kCAAA,IAAAuL,YAAA;AAAA,gBAAW/E,OAAM;AAAA,gBAAKuD,UAAW;AAAA,cAAA,CAAA,GACjC/J,kCAAA,IAAAuL,YAAA;AAAA,gBAAW/E,OAAM;AAAA,gBAAKuD,UAAW;AAAA,cAAA,CAAA,CAAA;AAAA,YACpC,CAAA,CAAA;AAAA,UACF,CAAA,CAAA;AAAA,QACF,CAAA,GAEA/J,kCAAA,IAACwL,iBAAA;AAAA,UACCC,SAAS9F;AAAAA,UACTG,MACEjE;AAAAA,UAEF6J,WAAY/D,SAAQ;AAEhB,mBAAA5H,kCAAAA,KAAC,MAAA;AAAA,cAEC+J,WAAW,wCAAwCnC,IAAIgE,WACnD,qDACA,mCACF;AAAA,cAEF5B,UAAA,CAAAhK,kCAAA,KAAC,MAAA;AAAA,gBACC+J,WAAU;AAAA,gBACVG,SAASA,MACPrE,SAAS,qCAAqCzE,QAAQ,WAAWwG,2BAAK2B,EAAE,aAAa3B,2BAAKiE,IAAI,EAAE;AAAA,gBAGlG7B,UAAA,CAAChK,kCAAA,KAAA,OAAA;AAAA,kBAAI+J,WAAU;AAAA,kBACbC,UAAA,CAAA/J,kCAAA,IAAC,OAAA;AAAA,oBACC6L,KAAKlE,2BAAKmE;AAAAA,oBACVC,KAAKpE,2BAAKiE;AAAAA,oBACV9B,WAAU;AAAA,kBAAA,CACZ,GACA/J,kCAAA,KAAC,OAAI;AAAA,oBAAA+J,WAAU;AAAA,oBACbC,UAAA,CAAChK,kCAAA,KAAA,QAAA;AAAA,sBAAK+J,WAAU;AAAA,sBACbC,UAAA,CAAKpC,2BAAAiE,MAAK,KAAEjE,2BAAKqE,IAAA;AAAA,oBACpB,CAAA,IACCrE,2BAAKgE,aACJ3L,kCAAAA,IAAC,QAAK;AAAA,sBAAA8J,WAAU;AAAA,sBAA8EC,UAE9F;AAAA,oBAAA,CAAA,CAAA;AAAA,kBAEJ,CAAA,CAAA;AAAA,gBACF,CAAA,GAECpC,IAAIsE,eACFjM,kCAAAA,IAAAkM,iBAAA;AAAA,kBACCnC,iDAACoC,SACC;AAAA,oBAAApC,UAAA,CAAA/J,kCAAA,IAACoM,gBAAe;AAAA,sBAAAC,SAAO;AAAA,sBACrBtC,UAAA/J,kCAAA,IAAC,OAAI;AAAA,wBAAA8J,WAAU;AAAA,wBACbC,UAAA/J,kCAAA,IAAC,QAAK;AAAA,0BAAA8J,WAAU;AAAA,0BAAuFC,UAAA;AAAA,wBAEvG,CAAA;AAAA,sBACF,CAAA;AAAA,oBACF,CAAA,yCACCuC,gBAAe;AAAA,sBAAAxC,WAAU;AAAA,sBACxBC,UAAChK,kCAAA,KAAA,KAAA;AAAA,wBAAE+J,WAAU;AAAA,wBAAUC,UAAA,CAAA,kBAAcpC,2BAAKsE,gBAAe,GAAA;AAAA,sBAAI,CAAA;AAAA,oBAC/D,CAAA,CAAA;AAAA,kBACF,CAAA;AAAA,gBACF,CAAA,CAAA;AAAA,cAAA,CAEJ,GACAjM,kCAAA,IAAC,MAAA;AAAA,gBAAG8J,WAAU;AAAA,gBAEXC,UAAKpC,2BAAA4E;AAAAA,cAAA,CACR,GACAvM,kCAAA,IAAC,MAAA;AAAA,gBAAG8J,WAAU;AAAA,gBAEXC,UAAKpC,2BAAA6E;AAAAA,cAAA,CACR,GACAzM,kCAAA,KAAC,MAAA;AAAA,gBAAG+J,WAAU;AAAA,gBAEXC,UAAA,CAAKpC,2BAAA8E,aAAY,KAAE9E,2BAAK+E,WAAA;AAAA,cAAA,CAC3B,GACA3M,kCAAA,KAAC,MAAA;AAAA,gBAAG+J,WAAU;AAAA,gBAEXC,UAAA,CAAKpC,2BAAA+E,aAAY,KAAE/E,2BAAKgF,iBAAA;AAAA,cAAA,CAC3B,GAEA3M,kCAAA,IAAC,MAAA;AAAA,gBAAG8J,WAAU;AAAA,gBAEXC,UAAKpC,2BAAAiF;AAAAA,cAAA,CACR,GACA5M,kCAAA,IAAC,MAAA;AAAA,gBAAG8J,WAAU;AAAA,gBAEXC,UAAKpC,2BAAAkF;AAAAA,cAAA,CACR,GACA9M,kCAAA,KAAC,MAAA;AAAA,gBAAG+J,WAAU;AAAA,gBAEXC,UAAA,CAAKpC,2BAAAmF,cAAa,KAAEnF,2BAAKoF,YAAA;AAAA,cAAA,CAC5B,GACAhN,kCAAA,KAAC,MAAA;AAAA,gBAAG+J,WAAU;AAAA,gBAEXC,UAAA,CAAKpC,2BAAAqF,UAAS,KAAErF,2BAAKsF,QAAA;AAAA,cAAA,CACxB,GACAlN,kCAAA,KAAC,MAAA;AAAA,gBAAG+J,WAAU;AAAA,gBAEXC,UAAA,CAAKpC,2BAAAuF,aAAY,KAAEvF,2BAAKwF,WAAA;AAAA,cAAA,CAC3B,GACAnN,kCAAA,IAAC,MAAG;AAAA,gBAAA8J,WAAU;AAAA,gBACZC,UAAA/J,kCAAA,IAACoN,QAAO;AAAA,kBAAAnD,SAASA,MAAMd,gBAAgBxB,GAAG;AAAA,gBAAG,CAAA;AAAA,cAC/C,CAAA,CAAA;AAAA,YAAA,GAtFKA,IAAI2B,EAuFX;AAAA,UAEJ;AAAA,QAAA,CACF,GACAvJ,kCAAA,KAAC,OAAI;AAAA,UAAA+J,WAAU;AAAA,UACbC,UAAA,CAAChK,kCAAA,KAAA,MAAA;AAAA,YAAG+J,WAAU;AAAA,YAAWC,UAAA,CAAA,kBAAe5B,UAAU,CAAA;AAAA,UAAE,CAAA,GACpDnI,kCAAA,IAAC,OAAI;AAAA,YAAA8J,WAAU;AAAA,YACbC,UAAA/J,kCAAA,IAACqN,sBAAA;AAAA,cACCC,YAAY1E,OAAOX,QAAQ;AAAA,cAC3BjG,aAAamG;AAAAA,cACboF,cAAepF,cAAYQ,iBAAiBR,SAAQjC,SAAU,CAAA;AAAA,YAChE,CAAA;AAAA,UACF,CAAA,CAAA;AAAA,QACF,CAAA,GAEAlG,kCAAA,IAACwN,WAAA;AAAA,UAECC,QAAQ1E;AAAAA,UACRjD,MAAM+C,gBAAgB,CAAC;AAAA,UACvB6E,SAASA,MAAM;AACb1E,+BAAmB,KAAK;AACxBF,4BAAgB,CAAA,CAAE;AAAA,UACpB;AAAA,UACA6E,QAAQ/D;AAAAA,UAAYgE,UAAU;AAAA,UAAO3E;AAAAA,QAAA,CAA4B,CAAA;AAAA,MAErE,CAAA,GACAlJ,kCAAA,KAACwK,aAAY;AAAA,QAAA/D,OAAM;AAAA,QACjBuD,UAAA,CAAA/J,kCAAA,IAACwL,iBAAA;AAAA,UACCC,SAAS/F;AAAAA,UACTI,MACErE;AAAAA,UAEFiK,WAAY/D,SAAQ;;AAEhB,mBAAA5H,kCAAAA,KAAC,MAAoB;AAAA,cAAA+J,WAAU;AAAA,cAC7BC,UAAA,CAAA/J,kCAAA,IAAC,MAAA;AAAA,gBAAG8J,WAAU;AAAA,gBACZC,UAAIpC,IAAA/G;AAAAA,cAAA,CAAO,GACbZ,kCAAA,IAAC,MAAA;AAAA,gBAAG8J,WAAU;AAAA,gBAEXC,UAAKpC,2BAAAkG;AAAAA,cAAA,CACR,GACA7N,kCAAA,IAAC,MAAA;AAAA,gBAAG8J,WAAU;AAAA,gBAEXC,UAAKpC,2BAAAmG;AAAAA,cAAA,CACR,GACA9N,kCAAA,IAAC,MAAA;AAAA,gBAAG8J,WAAU;AAAA,gBAEXC,UAAKpC,2BAAAoG;AAAAA,cAAA,CACR,GACA/N,kCAAA,IAAC,MAAA;AAAA,gBAAG8J,WAAU;AAAA,gBAEXC,UAAKpC,2BAAAqG;AAAAA,cAAA,CACR,GACAhO,kCAAA,IAAC,MAAA;AAAA,gBAAG8J,WAAU;AAAA,gBAEXC,WAAKpC,gCAAAjG,UAAAiG,mBAAOnE,IAAIyK,8CAAM,OAAI;AAAA,kBAAAlE,UAAA,CAAA,KAAEkE,MAAM,gBAAgB,UAAUA,MAAM,eAAe,WAAWA,MAAM,kBAAkB,kBAAkBA,MAAM,cAAc,UAAUA,MAAM,kBAAkB,YAAYA,MAAM,qBAC7M,eAAeA,MAAM,eAAe,8BAA8BA,MAAM,kBAAkB,UACxFA,MAAM,sBAAsB,kBAAkBA,MAAM,mBAAmB,yBAAyBA,MAAM,gBAAgB,sBAAsBA,MAAM,cAAc,oBAAoBA,MAAM,iBAAiB,uBAAuBA,MAAM,iBAAiB,uBAAuBA,MAAM,cAAc,gBAClSA,MAAM,aAAa,gBAAgBA,MAAM,eAAe,qBAAqBA,MAAM,aAAa,mBAAmBA,MAAM,mBAAmB,mBAAmB,EAAA;AAAA,gBAAA,CAAG;AAAA,cAAM,CAEhL,GACAjO,kCAAA,IAAC,MAAA;AAAA,gBAAG8J,WAAU;AAAA,gBAEZC,UAAA/J,kCAAA,IAACkO,QAAA;AAAA,kBACCC,SAAS,CAACpH,iBAAiBY,IAAI/G,MAAM;AAAA,kBACrCqJ,SAASA,MAAM5C,iBAAiBM,IAAI/G,MAAM;AAAA,gBAE5C,CAAA;AAAA,cAAA,CACF,GACAZ,kCAAA,IAAC,MAAG;AAAA,gBAAA8J,WAAU;AAAA,gBACZC,UAAA/J,kCAAA,IAACoN,QAAO;AAAA,kBAAAnD,SAASA,MAAMvC,WAAWC,GAAG;AAAA,gBAAG,CAAA;AAAA,cAC1C,CAAA,CAAA;AAAA,YAAA,GArCOA,IAAI/G,MAsCb;AAAA,UAEJ;AAAA,QAAA,CACF,GACAZ,kCAAA,IAACoO,YAAA;AAAA,UAAWX,QAAQnK;AAAAA,UAAiBoK,SAASA,MAAMnK,mBAAmB,KAAK;AAAA,UAAGpC;AAAAA,UAC7EO,OAAOA,QAAQA,QAAQ,CAAC;AAAA,UAAGC;AAAAA,UAAsBuF;AAAAA,QAAA,CACnD,GACAlH,kCAAA,IAACoO,YAAA;AAAA,UAAWX,QAAQrK;AAAAA,UAAasK,SAASA,MAAMrK,eAAe,KAAK;AAAA,UAAGlC;AAAAA,UACrEO,OAAOA,QAAQA,QAAQ,CAAC;AAAA,UAAGC;AAAAA,QAAA,CAC7B,GACA3B,kCAAA,IAACqO;UAAOvE,WAAU;AAAA,UAAqDG,SAASA,MAAM5G,eAAe,IAAI;AAAA,UAAG0G,UAAU;AAAA,QAAA,CAAA,CAAA;AAAA,MAExH,CAAA,GACA/J,kCAAA,IAACuK;QAAY/D,OAAO;AAAA,QAClBuD,gDAACuE,eAAc;AAAA,UAAAC,eAAe3M;AAAAA,UAAgBgF;AAAAA,QAAkD,CAAA;AAAA,MAElG,CAAA,CAAA;AAAA,IACF,CAAA,GACCvF,cAAc,mBAAmBE,qDAC5BiN,qBAAoB;AAAA,MAAAjN;AAAAA,MAA4BkN,mBAAmB5I;AAAAA,IAAc,CAAA,IAC/E7F,kCAAA,IAAA,OAAA;AAAA,MAAI8J,WAAU;AAAA,MAClBC,UAAA/J,kCAAA,IAAC;QAAE8J,WAAU;AAAA,QAAeC;MAAsC,CAAA;AAAA,IACpE,CAAA,IACD1I,cAAc,kBAAkBC,cAC/BtB,kCAAAA,IAAC0O,cAAA;AAAA,MAAaxN;AAAAA,MAAoCI;AAAAA,MAA0ByB;AAAAA,MAC1EtC;AAAAA,MACAsE;AAAAA,MACAvE;AAAAA,MACA8D;AAAAA,MACAiD;AAAAA,MACA3D;AAAAA,MAAgBkB;AAAAA,MAAsBhB;AAAAA,MACtChC;AAAAA,MACAlB;AAAAA,MACAO;AAAAA,MACAwN,cAAe9G,aAAgCD,kBAAkBC,OAAO;AAAA,IAAA,CAC1E,IAEI7H,kCAAA,IAAA,OAAA;AAAA,MAAI8J,WAAU;AAAA,MAChBC,gDAAC,KAAE;AAAA,QAAAD,WAAU;AAAA,QAAeC,UAAA;AAAA,MAA4B,CAAA;AAAA,IAC1D,CAAA,EAAA;AAAA,EAGJ,CAAA;AAGJ;"}