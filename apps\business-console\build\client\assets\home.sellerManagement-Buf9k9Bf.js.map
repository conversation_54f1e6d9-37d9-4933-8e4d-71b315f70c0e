{"version": 3, "file": "home.sellerManagement-Buf9k9Bf.js", "sources": ["../../../app/components/ui/createSeller.tsx", "../../../app/routes/home.sellerManagement.tsx"], "sourcesContent": ["import { useEffect, useState } from \"react\";\r\nimport { Form, useActionData, useFetcher } from \"@remix-run/react\";\r\nimport { Input } from \"~/components/ui/input\";\r\nimport { Button } from \"./button\";\r\nimport { Dialog, DialogContent, DialogHeader, DialogTitle } from \"./dialog\";\r\nimport { Label } from \"./label\";\r\nimport { useToast } from \"./ToastProvider\";\r\nimport SpinnerLoader from \"../loader/SpinnerLoader\";\r\nimport { RadioGroup, RadioGroupItem } from \"./radio-group\";\r\ninterface SellerData {\r\n      id?: number;\r\n      name: string;\r\n      address: string;\r\n      email: string;\r\n      customerSupportNumber: string;\r\n      owner: {\r\n            firstName: string;\r\n            lastName: string;\r\n            email: string;\r\n            mobileNumber: string;\r\n            address: string;\r\n      };\r\n      latitude: string;\r\n      longitude: string;\r\n      ondcDomain: \"RET10\" | \"RET11\";\r\n      pincode: string;\r\n}\r\nconst initialFormState: SellerData = {\r\n      name: \"\",\r\n      address: \"\",\r\n      email: \"\",\r\n      customerSupportNumber: \"\",\r\n      owner: {\r\n            firstName: \"\",\r\n            lastName: \"\",\r\n            email: \"\",\r\n            mobileNumber: \"\",\r\n            address: \"\",\r\n      },\r\n      latitude: \"\",\r\n      longitude: \"\",\r\n      ondcDomain: \"RET10\",\r\n      pincode: \"\",\r\n};\r\ninterface createSellerProps {\r\n      isOpen: boolean;\r\n      onClose: () => void;\r\n      seller?: SellerData\r\n\r\n}\r\nexport default function CreateSeller({ isOpen, onClose, seller }: createSellerProps) {\r\n      const [formData, setFormData] = useState<SellerData>({\r\n            id: seller?.id || undefined,\r\n            name: seller?.name || \"\",\r\n            address: seller?.address || \"\",\r\n            email: seller?.email || \"\",\r\n            customerSupportNumber: seller?.customerSupportNumber || \"\",\r\n            owner: {\r\n                  firstName: seller?.owner.firstName || \"\",\r\n                  lastName: seller?.owner.lastName || \"\",\r\n                  email: seller?.owner.email || \"\",\r\n                  mobileNumber: seller?.owner.mobileNumber || \"\",\r\n                  address: seller?.owner.address || \"\",\r\n            },\r\n            latitude: seller?.latitude || \"\",\r\n            longitude: seller?.longitude || \"\",\r\n            ondcDomain: seller?.ondcDomain || \"RET10\",\r\n            pincode: seller?.pincode || \"\",\r\n      });\r\n\r\n      const fetcher = useFetcher()\r\n      const [errors, setErrors] = useState<Record<string, string>>({});\r\n      const isEditMode = !!seller?.id;\r\n      const isLoading = fetcher.state !== \"idle\";\r\n      const { showToast } = useToast()\r\n      useEffect(() => {\r\n            if (fetcher.state === \"idle\" && fetcher.data) {\r\n                  if (fetcher?.data?.success === true) {\r\n                        showToast(\"Seller created successfully\", \"success\");\r\n                        setFormData(initialFormState);\r\n                        setErrors({});\r\n                        onClose();\r\n                  } else if (fetcher?.data?.success === false && fetcher?.data?.error) {\r\n                        showToast(fetcher.data?.error, \"error\");\r\n                  } else {\r\n                        showToast(\"Unexpected response from server\", \"error\");\r\n                  }\r\n            }\r\n      }, [fetcher.state, fetcher.data, onClose]);\r\n      useEffect(() => {\r\n            if (!isOpen) {\r\n                  setFormData(initialFormState);\r\n            }\r\n      }, [isOpen])\r\n\r\n      const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n            const { name, value } = e.target;\r\n            if (name.startsWith(\"owner.\")) {\r\n                  const field = name.split(\".\")[1];\r\n                  setFormData((prev) => ({\r\n                        ...prev,\r\n                        owner: { ...prev.owner, [field]: value },\r\n                  }));\r\n            }\r\n            else {\r\n                  setFormData((prev) => ({ ...prev, [name]: value }));\r\n            }\r\n      };\r\n      const validateForm = () => {\r\n            let newErrors: Record<string, string> = {};\r\n\r\n            if (!formData.name) newErrors.name = \"Name is required\";\r\n            if (!formData.address) {\r\n                  newErrors.address = \"Address is required\";\r\n            } else if (!(/^[A-Za-z0-9\\s]{25,}$/).test(formData.address)) {\r\n                  newErrors.address = \"Address should be atleast 25 characters and contains only 0-9 and A-Z\";\r\n            }\r\n            if (!formData.email) newErrors.email = \"Email is required\";\r\n            if (!formData.customerSupportNumber) newErrors.customerSupportNumber = \"Customer Support Number is required\";\r\n            if (!formData.owner.firstName) newErrors[\"owner.firstName\"] = \"First Name is required\";\r\n            if (!formData.owner.lastName) newErrors[\"owner.lastName\"] = \"Last Name is required\";\r\n            if (!formData.owner.email) newErrors[\"owner.email\"] = \"Owner Email is required\";\r\n            if (!formData.owner.mobileNumber) {\r\n                  newErrors[\"owner.mobileNumber\"] = \"Mobile Number is required\";\r\n            } else if (!/^\\d{10}$/.test(formData.owner.mobileNumber)) {\r\n                  newErrors[\"owner.mobileNumber\"] = \"Mobile Number must be 10 digits\";\r\n            }\r\n            if (formData.ondcDomain === \"RET11\") {\r\n                  if (!formData.pincode) {\r\n                        newErrors.pincode = \"Pincode is required\"\r\n                  } else if (formData.pincode.length !== 6) {\r\n                        newErrors.pincode = \"Pincode must be 6 digits\";\r\n                  }\r\n            }\r\n            if (!formData.latitude) newErrors.latitude = \"Latitude is required\";\r\n            if (!formData.longitude) newErrors.longitude = \"Longitude is required\";\r\n\r\n            setErrors(newErrors);\r\n            return Object.keys(newErrors).length === 0;\r\n      };\r\n      const handleSubmit = (e: React.FormEvent) => {\r\n            e.preventDefault();\r\n            if (!validateForm()) return;\r\n\r\n            const data = new FormData();\r\n            Object.entries(formData).forEach(([key, value]) => {\r\n                  if (typeof value === \"object\") {\r\n                        Object.entries(value).forEach(([subKey, subValue]) => {\r\n                              data.append(`owner.${subKey}`, String(subValue));\r\n                        });\r\n                  } else {\r\n                        data.append(key, String(value));\r\n                  }\r\n            });\r\n            data.append(\"roles\", \"SellerOwner\");\r\n            data.append(\"actionType\", \"createNewSeller\");\r\n            fetcher.submit(data, { method: isEditMode ? \"put\" : \"post\" });\r\n      };\r\n      return (\r\n            <Dialog open={isOpen} onOpenChange={onClose}>\r\n                  <DialogContent className=\"max-h-[90vh] max-w-screen-md flex flex-col p-0\">\r\n                        {isLoading && (\r\n                              <div className=\"absolute inset-0 flex items-center justify-center bg-white/10 backdrop-blur-sm z-50\">\r\n                                    <SpinnerLoader loading={isLoading} />\r\n                              </div>\r\n                        )}\r\n                        <DialogHeader className=\"sticky top-0 bg-white z-10 px-6 py-4 border-b\">\r\n                              <DialogTitle className=\"font-bold text-lg\">Create Seller</DialogTitle>\r\n                        </DialogHeader>\r\n                        <Form onSubmit={handleSubmit} className=\"flex-1 overflow-y-auto space-y-4 p-6\">\r\n\r\n                              <h3 className=\"font-semibold\">Business Type</h3>\r\n                              <RadioGroup\r\n                                    value={formData.ondcDomain}\r\n                                    onValueChange={(val: \"RET10\" | \"RET11\") => setFormData((prev) => ({ ...prev, ondcDomain: val }))}\r\n                                    className=\"flex flex-row gap-5\"\r\n                              >\r\n                                    <div className=\"flex items-center space-x-2\">\r\n                                          <RadioGroupItem id=\"type-RET10\" value=\"RET10\" />\r\n                                          <Label htmlFor=\"type-RET10\">Non-Restaurant</Label>\r\n                                    </div>\r\n                                    <div className=\"flex items-center space-x-2\">\r\n                                          <RadioGroupItem id=\"type-RET11\" value=\"RET11\" />\r\n                                          <Label htmlFor=\"type-RET11\">Restaurant</Label>\r\n                                    </div>\r\n                              </RadioGroup>\r\n\r\n                              <h3 className=\"font-semibold\">Business Details</h3>\r\n                              <div className=\"space-y-3 \">\r\n                                    <div className=\"flex  flex-col md:flex-row  gap-3\">\r\n                                          <Label className=\" flex-1 flex flex-col gap-y-2\">\r\n                                                Name\r\n                                                <Input type=\"text\" name=\"name\" placeholder=\"Name\" value={formData.name} onChange={handleChange} className={errors.name ? \"border-red-500\" : \"\"} />\r\n                                                {errors.name && <span className=\"text-red-500 text-sm\">{errors.name}</span>}\r\n                                          </Label>\r\n                                          <Label className=\" flex-1 flex flex-col gap-y-2\">\r\n                                                Address\r\n                                                <Input type=\"text\" name=\"address\" placeholder=\"Address\" value={formData.address} onChange={handleChange} required className={errors.address ? \"border-red-500\" : \"\"} />\r\n                                                {errors.address && <span className=\"text-red-500 text-sm\">{errors.address}</span>}\r\n\r\n                                          </Label>\r\n                                    </div>\r\n\r\n                                    <div className=\"flex flex-col md:flex-row gap-3\">\r\n                                          <Label className=\"flex-1 flex flex-col gap-y-2\">\r\n                                                Email\r\n                                                <Input type=\"email\" name=\"email\" placeholder=\"Email\" onChange={handleChange} required className={errors.email ? \"border-red-500\" : \"\"} />\r\n                                                {errors.email && <span className=\"text-red-500 text-sm\">{errors.email}</span>}\r\n                                          </Label>\r\n                                          <Label className=\" flex-1  flex flex-col gap-y-2\">\r\n                                                Customer Support Number\r\n                                                <Input\r\n                                                      type=\"number\"\r\n                                                      name=\"customerSupportNumber\"\r\n                                                      value={formData.customerSupportNumber}\r\n                                                      onChange={(e) => {\r\n                                                            const value = e.target.value.replace(/\\D/g, \"\"); // Only allow numbers\r\n                                                            if (value.length <= 10) {\r\n                                                                  setFormData((prev) => ({ ...prev, customerSupportNumber: value }));\r\n                                                            }\r\n                                                      }}\r\n                                                      className={errors.customerSupportNumber ? \"border-red-500\" : \"\"}\r\n                                                      placeholder=\"Enter 10-digit number\"\r\n                                                />\r\n                                                {errors.customerSupportNumber && (\r\n                                                      <span className=\"text-red-500 text-sm\">{errors.customerSupportNumber}</span>\r\n                                                )}\r\n                                          </Label>\r\n                                    </div>\r\n                              </div>\r\n                              <h3 className=\"font-semibold\">Owner Details</h3>\r\n                              <div className=\"space-y-3\">\r\n                                    <div className=\"flex flex-col md:flex-row gap-3\">\r\n                                          <Label className=\" flex-1 flex flex-col gap-y-2\">\r\n                                                First Name\r\n                                                <Input type=\"text\" name=\"owner.firstName\" placeholder=\"First Name\" onChange={handleChange} required />\r\n                                          </Label>\r\n                                          <Label className=\"flex-1  flex flex-col gap-y-2\">\r\n                                                Last Name\r\n                                                <Input type=\"text\" name=\"owner.lastName\" placeholder=\"Last Name\" onChange={handleChange} required />\r\n                                          </Label>\r\n                                    </div>\r\n\r\n                                    <div className=\" flex flex-col md:flex-row gap-3\">\r\n                                          <Label className=\" flex-1 flex flex-col gap-y-2\">\r\n                                                Owner Email\r\n                                                <Input type=\"email\" name=\"owner.email\" placeholder=\"Owner Email\" onChange={handleChange} required />\r\n                                          </Label>\r\n                                          <Label className=\" flex-1  flex flex-col gap-y-2\">\r\n                                                Owner Mobile Number\r\n                                                <Input type=\"number\" name=\"owner.mobileNumber\" value={formData.owner.mobileNumber}\r\n\r\n                                                      onChange={(e) => {\r\n                                                            const value = e.target.value.replace(/\\D/g, \"\"); // Only allow numbers\r\n                                                            if (value.length <= 10) {\r\n                                                                  setFormData((prev) => ({\r\n                                                                        ...prev,\r\n                                                                        owner: {\r\n                                                                              ...prev.owner,\r\n                                                                              mobileNumber: value\r\n                                                                        },\r\n                                                                  }));\r\n                                                            }\r\n                                                      }}\r\n\r\n                                                      placeholder=\"Owner MobileNumber\"\r\n                                                      className={errors[\"owner.mobileNumber\"] ? \"border-red-500\" : \"\"} />\r\n                                                {errors[\"owner.mobileNumber\"] && <span className=\"text-red-500 text-sm\">{errors[\"owner.mobileNumber\"]}</span>}\r\n                                          </Label>\r\n                                    </div>\r\n                                    <Label>\r\n                                          Owner Address\r\n                                          <Input type=\"text\" name=\"owner.address\" placeholder=\"Owner Address\" onChange={handleChange} required />\r\n                                    </Label>\r\n                              </div>\r\n\r\n                              <h3 className=\"font-semibold\">Location</h3>\r\n                              {formData.ondcDomain === \"RET11\" && <Label className=\"flex-1 flex flex-col  gap-y-3 w-fit\">\r\n                                    Pincode\r\n                                    <Input type=\"text\" name=\"pincode\" placeholder=\"Pincode\" onChange={handleChange} required />\r\n                                    {errors.pincode && <span className=\"text-red-500 text-sm\">{errors.pincode}</span>}\r\n                              </Label>}\r\n                              <div className=\" flex flex-col md:flex-row gap-3\">\r\n\r\n                                    <Label className=\"flex-1 flex flex-col  gap-y-3 tex-\">\r\n                                          Latitude\r\n                                          <Input type=\"text\" name=\"latitude\" placeholder=\"Latitude\" onChange={handleChange} required />\r\n                                    </Label>\r\n                                    <Label className=\"flex-1 flex  flex-col gap-y-3\">\r\n                                          Longitude\r\n                                          <Input type=\"text\" name=\"longitude\" placeholder=\"Longitude\" onChange={handleChange} required />\r\n                                    </Label>\r\n                              </div>\r\n                              <div className=\"flex justify-end\">\r\n                                    <Button type=\"submit\" className=\"rounded-full hover:bg-blue-400  bg-blue-300\" loading={isLoading} disabled={isLoading}>{isEditMode ? \"Update Seller\" : \"Create Seller\"}</Button>\r\n                              </div>\r\n                        </Form>\r\n                  </DialogContent>\r\n            </Dialog>\r\n      );\r\n}\r\n", "'use client'\r\n\r\nimport { ActionFunction, json } from \"@remix-run/node\";\r\nimport { use<PERSON><PERSON><PERSON>, useLoaderData, useNavigate, Link } from \"@remix-run/react\";\r\nimport { getSellers } from \"~/services/businessConsoleService\";\r\nimport { Seller } from \"~/types/api/businessConsoleService/SellerManagement\";\r\nimport { withAuth, withResponse } from \"~/utils/auth-utils\";\r\nimport { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from \"~/components/ui/table\";\r\nimport { Button } from \"~/components/ui/button\";\r\nimport { useEffect, useState } from \"react\";\r\nimport { createSeller, createSellerPushMenu, updateSellerStatus } from \"~/services/masterItemCategories\";\r\nimport CreateSeller from \"~/components/ui/createSeller\";\r\nimport { useToast } from \"~/components/ui/ToastProvider\";\r\nimport { Switch } from \"~/components/ui/switch\";\r\nimport { Input } from \"~/components/ui/input\";\r\nimport { useDebounce } from \"~/hooks/useDebounce\";\r\nimport { Search } from \"lucide-react\";\r\nimport SpinnerLoader from \"~/components/loader/SpinnerLoader\";\r\ninterface LoaderData {\r\n  sellerData: Seller[],\r\n}\r\nexport interface ActionData {\r\n  success?: boolean,\r\n  error?: string\r\n}\r\nexport const loader = withAuth(async ({ request }) => {\r\n  try {\r\n    const response = await getSellers(request);\r\n    // Transform roleData into an array of `{ value, label }` objects\r\n    return withResponse({\r\n      sellerData: response.data,\r\n    }, response.headers);\r\n  } catch (error) {\r\n    console.error(\"Error in loader:\", error);\r\n    // Return a JSON-based error shape\r\n    throw new Response(\"failed to get sellers\", { status: 500 })\r\n  }\r\n});\r\nexport const action: ActionFunction = async ({ request }) => {\r\n  const formData = await request.formData();\r\n  const actionType = formData.get(\"actionType\");\r\n  const sellerId = Number(formData.get(\"sellerId\"));\r\n\r\n  if (actionType === \"updateSellerStatus\") {\r\n    try {\r\n      const response = await updateSellerStatus(sellerId, request);\r\n      return withResponse({ data: response.data }, response.headers);\r\n    } catch (error) {\r\n      if (error instanceof Response && error.status === 404) {\r\n        throw json({ error: \"update Seller Status page Not found\" }, { status: 404 });\r\n      }\r\n      throw new Response(\"Failed to update Seller Status\", { status: 500 });\r\n    }\r\n  } else if (actionType === \"createNewSeller\") {\r\n    const seller = {\r\n      name: formData.get(\"name\"),\r\n      address: formData.get(\"address\"),\r\n      email: formData.get(\"email\"),\r\n      customerSupportNumber: formData.get(\"customerSupportNumber\"),\r\n      owner: {\r\n        firstName: formData.get(\"owner.firstName\"),\r\n        lastName: formData.get(\"owner.lastName\"),\r\n        email: formData.get(\"owner.email\"),\r\n        mobileNumber: formData.get(\"owner.mobileNumber\"),\r\n        address: formData.get(\"owner.address\"),\r\n        roles: formData.getAll(\"roles\"),\r\n      },\r\n      latitude: formData.get(\"latitude\"),\r\n      longitude: formData.get(\"longitude\"),\r\n      ondcDomain: formData.get(\"ondcDomain\") as \"RET10\" | \"RET11\",\r\n      pincode: formData.get(\"ondcDomain\") === \"RET11\" ? formData.get(\"pincode\") : \"\",\r\n      miSource: formData.get(\"ondcDomain\") === \"RET11\" ? \"rnet\" : \"mnet\",\r\n    };\r\n    try {\r\n      const response = await createSeller(seller, request);\r\n      return withResponse({ success: response.statusCode === 200 }, response.headers);\r\n    } catch (error) {\r\n      if (error instanceof Response && error.status === 404) {\r\n        throw json({ error: \"create Seller page Not found\", success: false }, { status: 404 });\r\n      }\r\n      return json({ success: false, error: \"Failed to create Seller\" }, { status: 500 });\r\n    }\r\n  }\r\n  else if (actionType === \"pushMenu\") {\r\n    console.log(\"ooooooooooooooo\")\r\n\r\n    try {\r\n      const response = await createSellerPushMenu(sellerId, request);\r\n      return withResponse({ success: response.statusCode === 200 }, response.headers);\r\n    } catch (error) {\r\n      if (error instanceof Response && error.status === 404) {\r\n        throw json({ error: \"create Seller page Not found\", success: false }, { status: 404 });\r\n      }\r\n      return json({ success: false, error: \"Failed to create Seller\" }, { status: 500 });\r\n    }\r\n  }\r\n  return json({ success: false, error: \"Invalid action type\" });\r\n};\r\nexport default function SellerManagementPage() {\r\n  const { showToast } = useToast()\r\n  const { sellerData } = useLoaderData<LoaderData>();\r\n  const dataleng = sellerData.length;\r\n  const [isModalOpen, setIsModalOpen] = useState(false);\r\n  const goTo = useNavigate();\r\n  const [isSuccess, setIsSuccess] = useState(false);\r\n\r\n  const [sellerStates, setSellerStates] = useState<Record<number, boolean>>(() => {\r\n    return Array.isArray(sellerData)\r\n      ? sellerData.reduce((acc, seller) => {\r\n        acc[seller.id] = seller.enabled;\r\n        return acc;\r\n      }, {} as Record<number, boolean>)\r\n      : {}; // Return an empty object if sellerData is not an array\r\n  });\r\n\r\n  const fetcher = useFetcher<{ sellerData: Seller[] }>();\r\n\r\n  const handleSwitch = async (sellerId: number) => {\r\n    // Optimistically toggle the switch\r\n    setSellerStates((prev) => ({\r\n      ...prev,\r\n      [sellerId]: !prev[sellerId], // Toggle status before request completes\r\n    }));\r\n\r\n    const formData = new FormData();\r\n    formData.append(\"sellerId\", sellerId.toString());\r\n    formData.append(\"actionType\", \"updateSellerStatus\");\r\n\r\n    try {\r\n      await fetcher.submit(formData, { method: \"put\" });\r\n      if (fetcher.state === \"idle\") {\r\n        showToast(\"sellerStatus Updated SuccessFully\", \"success\")\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Failed to update status:\", error);\r\n\r\n      // ❌ Revert the toggle if the update fails\r\n      setSellerStates((prev) => ({\r\n        ...prev,\r\n        [sellerId]: !prev[sellerId],\r\n      }));\r\n    }\r\n  };\r\n\r\n\r\n  const handleSubmitPushMenu = (row: number) => {\r\n    console.log(\",,,,,,,,,,,,,,,,,,\")\r\n    const formData = new FormData();\r\n    setUniQId(row)\r\n    formData.append(\"actionType\", \"pushMenu\");\r\n    formData.append(\"sellerId\", row.toString());\r\n    fetcher.submit(formData, { method: 'POST' })\r\n  }\r\n  useEffect(() => {\r\n    if (fetcher.state === \"idle\" && fetcher.data && \"sellerData\" in fetcher.data) {\r\n      const data = fetcher.data as { sellerData: Seller[] };\r\n      setSellerStates((prev) =>\r\n        (data.sellerData || []).reduce((acc, seller) => {\r\n          return acc;\r\n        }, {} as Record<number, boolean>)\r\n      );\r\n    }\r\n  }, [fetcher.state, fetcher.data]);\r\n  const [searchTerm, setSearchTerm] = useState('');\r\n  const [sellerFilter, setSellerFilter] = useState<Seller[]>([]);\r\n  const handleSearch = (val: string) => {\r\n    setSearchTerm(val)\r\n  }\r\n\r\n  const [uniQId, setUniQId] = useState(0);\r\n\r\n  useEffect(() => {\r\n    if (sellerData) {\r\n      setSellerFilter(sellerData);\r\n    }\r\n  }, [sellerData]);\r\n\r\n  useEffect(() => {\r\n    if (typeof searchTerm === \"string\" && searchTerm.length >= 3) {\r\n      const filteredSellers = sellerData?.filter(\r\n        (item) =>\r\n          item?.name &&\r\n          typeof item.name === \"string\" &&\r\n          item.name.toLowerCase().includes(searchTerm.toLowerCase())\r\n      );\r\n      setSellerFilter(filteredSellers || []);\r\n      console.log(filteredSellers, \"Filtered Sellers\");\r\n    } else {\r\n      setSellerFilter(sellerData || []);\r\n      console.log(sellerData, \"All Sellers\");\r\n    }\r\n  }, [searchTerm, sellerData]);\r\n\r\n\r\n  useEffect(() => {\r\n\r\n    if (fetcher?.data?.success) {\r\n\r\n      setIsSuccess(true)\r\n\r\n      console.log(fetcher.data, \"fetcherData.......\")\r\n      showToast(\"sucessFully Pushed Menu\", \"success\")\r\n    }\r\n    else if (fetcher.data?.success === false) {\r\n      setIsSuccess(false)\r\n      showToast(\"Failed to Push Menu\", \"error\")\r\n    }\r\n\r\n  }, [fetcher.data])\r\n\r\n\r\n  const loading = fetcher.state !== \"idle\" \r\n  \r\n\r\n\r\n  return (\r\n    <div className=\"container mx-auto p-6\">\r\n                {loading && <SpinnerLoader loading={loading} />}\r\n\r\n\r\n      <h1 className=\"text-2xl font-bold mb-4\">Seller Management</h1>\r\n      <div className=\"flex flex-col my-3 relative \">\r\n        <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400\" />\r\n        <Input\r\n          type='search'\r\n          placeholder=\"Search by Seller Name\"\r\n          value={searchTerm}\r\n          onChange={(e: { target: { value: string; }; }) => handleSearch(e.target.value)}\r\n          className=\"max-w-sm rounded-full pl-10  \"\r\n          autoFocus\r\n        />\r\n      </div>\r\n      <Table>\r\n        <TableHeader className=\"bg-gray-100\">\r\n          <TableRow>\r\n            <TableHead>ID</TableHead>\r\n            <TableHead>Name</TableHead>\r\n            <TableHead>Wallet Balance</TableHead>\r\n            <TableHead>Stock Management</TableHead>\r\n            <TableHead>Status</TableHead>\r\n            <TableHead></TableHead>\r\n\r\n          </TableRow>\r\n        </TableHeader>\r\n\r\n        <TableBody>\r\n\r\n          {sellerFilter.length > 0 ? (\r\n            sellerFilter.map((seller) => (\r\n              <TableRow key={seller.id}>\r\n                <TableCell>{seller.id}</TableCell>\r\n                <TableCell className={`cursor-pointer text-blue-500 font-bold  items-center`}\r\n                  onClick={() =>\r\n                    goTo(`/home/<USER>\n                  }  >{seller.name}</TableCell>\r\n\r\n                <TableCell>{seller.walletBalance > 0 ? seller.walletBalance : \"-\"}</TableCell>\r\n\r\n               <TableCell>\r\n  <div className=\"flex gap-2\">\r\n    <Link to={`/home/<USER>\n      <Button variant=\"outline\" size=\"sm\" className=\"hover:bg-primary hover:text-white transition-colors\">\r\n        MyStock\r\n      </Button>\r\n    </Link>\r\n    <Link to={`/home/<USER>\n      <Button variant=\"outline\" size=\"sm\" className=\"hover:bg-primary hover:text-white transition-colors\">\r\n        StockWithMe\r\n      </Button>\r\n    </Link>\r\n  </div>\r\n</TableCell>\r\n                <TableCell>\r\n                  <Switch\r\n                    checked={sellerStates[seller.id]}  // Use sellerStates to track toggle status\r\n                    onCheckedChange={() => handleSwitch(seller.id)}\r\n                  />\r\n                </TableCell>\r\n                \r\n                \r\n                <TableCell>\r\n                  <button\r\n                    className={`\r\n          px-6 py-2 rounded-full font-bold text-white\r\n          ${(isSuccess && seller.id === uniQId) ? 'bg-green-600' : 'bg-blue-600'}\r\n          hover:bg-blue-700\r\n          disabled:bg-gray-400 disabled:cursor-not-allowed\r\n        `}\r\n                    onClick={() => handleSubmitPushMenu(seller?.id)}\r\n                    disabled={loading}\r\n                  >\r\n                    <span className=\"flex items-center justify-center\">\r\n                      {(loading && seller.id === uniQId) ? (\r\n                        <svg\r\n                          className=\"h-5 w-5 mr-2 text-white\"\r\n                          xmlns=\"http://www.w3.org/2000/svg\"\r\n                          fill=\"none\"\r\n                          viewBox=\"0 0 24 24\"\r\n                        >\r\n                          <circle\r\n                            className=\"opacity-25\"\r\n                            cx=\"12\"\r\n                            cy=\"12\"\r\n                            r=\"10\"\r\n                            stroke=\"currentColor\"\r\n                            strokeWidth=\"4\"\r\n                          />\r\n                          <path\r\n                            className=\"opacity-75\"\r\n                            fill=\"currentColor\"\r\n                            d=\"M4 12a8 8 0 018-8v8h8a8 8 0 01-16 0z\"\r\n                          />\r\n                        </svg>\r\n                      ) : (isSuccess && seller.id === uniQId) ? (\r\n                        <svg\r\n                          className=\"h-5 w-5 mr-2\"\r\n                          xmlns=\"http://www.w3.org/2000/svg\"\r\n                          fill=\"none\"\r\n                          viewBox=\"0 0 24 24\"\r\n                          stroke=\"currentColor\"\r\n                        >\r\n                          <path\r\n                            strokeLinecap=\"round\"\r\n                            strokeLinejoin=\"round\"\r\n                            strokeWidth=\"2\"\r\n                            d=\"M5 13l4 4L19 7\"\r\n                          />\r\n                        </svg>\r\n                      ) : null}\r\n                      {(loading && seller.id === uniQId) ? 'Pushing...' : (isSuccess && seller.id === uniQId) ? 'Pushed!' : 'Push Menu'}\r\n                    </span>\r\n                  </button>\r\n                </TableCell>\r\n              </TableRow>\r\n            ))\r\n          ) : (\r\n            <TableRow>\r\n              <TableCell colSpan={6} className=\"h-24 text-center\">\r\n                No results.\r\n              </TableCell>\r\n            </TableRow>\r\n          )}\r\n        </TableBody>\r\n      </Table>\r\n      <Button className=\"fixed bottom-5 right-5 rounded-full cursor-pointer\" onClick={() => setIsModalOpen(true)}>+ Add Seller</Button>\r\n\r\n      <CreateSeller isOpen={isModalOpen} onClose={() => setIsModalOpen(false)} />\r\n    </div>\r\n  );\r\n}"], "names": ["useState", "useEffect", "jsx", "jsxs", "SellerManagementPage", "showToast", "useToast", "sellerData", "useLoaderData", "length", "isModalOpen", "setIsModalOpen", "goTo", "useNavigate", "isSuccess", "setIsSuccess", "sellerStates", "setSellerStates", "Array", "isArray", "reduce", "acc", "seller", "id", "enabled", "fetcher", "useFetcher", "handleSwitch", "sellerId", "prev", "formData", "FormData", "append", "toString", "submit", "method", "state", "error", "console", "handleSubmitPushMenu", "row", "log", "setUniQId", "data", "searchTerm", "setSearchTerm", "sellerFilter", "setSeller<PERSON><PERSON>er", "handleSearch", "val", "uniQId", "filteredSellers", "filter", "item", "name", "toLowerCase", "includes", "success", "loading", "className", "children", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Search", "Input", "type", "placeholder", "value", "onChange", "e", "target", "autoFocus", "Table", "TableHeader", "TableRow", "TableHead", "TableBody", "map", "TableCell", "onClick", "businessId", "walletBalance", "Link", "to", "<PERSON><PERSON>", "variant", "size", "Switch", "checked", "onCheckedChange", "disabled", "xmlns", "fill", "viewBox", "cx", "cy", "r", "stroke", "strokeWidth", "d", "strokeLinecap", "strokeLinejoin", "colSpan", "CreateSeller", "isOpen", "onClose"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2BA,MAAM,mBAA+B;AAAA,EAC/B,MAAM;AAAA,EACN,SAAS;AAAA,EACT,OAAO;AAAA,EACP,uBAAuB;AAAA,EACvB,OAAO;AAAA,IACD,WAAW;AAAA,IACX,UAAU;AAAA,IACV,OAAO;AAAA,IACP,cAAc;AAAA,IACd,SAAS;AAAA,EACf;AAAA,EACA,UAAU;AAAA,EACV,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,SAAS;AACf;AAOA,SAAwB,aAAa,EAAE,QAAQ,SAAS,UAA6B;AAC/E,QAAM,CAAC,UAAU,WAAW,IAAIA,sBAAqB;AAAA,IAC/C,KAAI,iCAAQ,OAAM;AAAA,IAClB,OAAM,iCAAQ,SAAQ;AAAA,IACtB,UAAS,iCAAQ,YAAW;AAAA,IAC5B,QAAO,iCAAQ,UAAS;AAAA,IACxB,wBAAuB,iCAAQ,0BAAyB;AAAA,IACxD,OAAO;AAAA,MACD,YAAW,iCAAQ,MAAM,cAAa;AAAA,MACtC,WAAU,iCAAQ,MAAM,aAAY;AAAA,MACpC,QAAO,iCAAQ,MAAM,UAAS;AAAA,MAC9B,eAAc,iCAAQ,MAAM,iBAAgB;AAAA,MAC5C,UAAS,iCAAQ,MAAM,YAAW;AAAA,IACxC;AAAA,IACA,WAAU,iCAAQ,aAAY;AAAA,IAC9B,YAAW,iCAAQ,cAAa;AAAA,IAChC,aAAY,iCAAQ,eAAc;AAAA,IAClC,UAAS,iCAAQ,YAAW;AAAA,EAAA,CACjC;AAED,QAAM,UAAU,WAAW;AAC3B,QAAM,CAAC,QAAQ,SAAS,IAAIA,aAAAA,SAAiC,CAAA,CAAE;AACzD,QAAA,aAAa,CAAC,EAAC,iCAAQ;AACvB,QAAA,YAAY,QAAQ,UAAU;AAC9B,QAAA,EAAE,UAAU,IAAI,SAAS;AAC/BC,eAAAA,UAAU,MAAM;;AACV,QAAI,QAAQ,UAAU,UAAU,QAAQ,MAAM;AACpC,YAAA,wCAAS,SAAT,mBAAe,aAAY,MAAM;AAC/B,kBAAU,+BAA+B,SAAS;AAClD,oBAAY,gBAAgB;AAC5B,kBAAU,CAAA,CAAE;AACJ,gBAAA;AAAA,MAAA,aACH,wCAAS,SAAT,mBAAe,aAAY,WAAS,wCAAS,SAAT,mBAAe,QAAO;AACrD,mBAAA,aAAQ,SAAR,mBAAc,OAAO,OAAO;AAAA,MAAA,OACrC;AACD,kBAAU,mCAAmC,OAAO;AAAA,MAAA;AAAA,IAC1D;AAAA,EACN,GACH,CAAC,QAAQ,OAAO,QAAQ,MAAM,OAAO,CAAC;AACzCA,eAAAA,UAAU,MAAM;AACV,QAAI,CAAC,QAAQ;AACP,kBAAY,gBAAgB;AAAA,IAAA;AAAA,EAClC,GACH,CAAC,MAAM,CAAC;AAEL,QAAA,eAAe,CAAC,MAA2C;AAC3D,UAAM,EAAE,MAAM,MAAM,IAAI,EAAE;AACtB,QAAA,KAAK,WAAW,QAAQ,GAAG;AACzB,YAAM,QAAQ,KAAK,MAAM,GAAG,EAAE,CAAC;AAC/B,kBAAY,CAAC,UAAU;AAAA,QACjB,GAAG;AAAA,QACH,OAAO,EAAE,GAAG,KAAK,OAAO,CAAC,KAAK,GAAG,MAAM;AAAA,MAAA,EAC3C;AAAA,IAAA,OAEH;AACa,kBAAA,CAAC,UAAU,EAAE,GAAG,MAAM,CAAC,IAAI,GAAG,MAAA,EAAQ;AAAA,IAAA;AAAA,EAE9D;AACA,QAAM,eAAe,MAAM;AACrB,QAAI,YAAoC,CAAC;AAEzC,QAAI,CAAC,SAAS,KAAM,WAAU,OAAO;AACjC,QAAA,CAAC,SAAS,SAAS;AACjB,gBAAU,UAAU;AAAA,eACf,CAAE,uBAAwB,KAAK,SAAS,OAAO,GAAG;AACvD,gBAAU,UAAU;AAAA,IAAA;AAE1B,QAAI,CAAC,SAAS,MAAO,WAAU,QAAQ;AACvC,QAAI,CAAC,SAAS,sBAAuB,WAAU,wBAAwB;AACvE,QAAI,CAAC,SAAS,MAAM,UAAW,WAAU,iBAAiB,IAAI;AAC9D,QAAI,CAAC,SAAS,MAAM,SAAU,WAAU,gBAAgB,IAAI;AAC5D,QAAI,CAAC,SAAS,MAAM,MAAO,WAAU,aAAa,IAAI;AAClD,QAAA,CAAC,SAAS,MAAM,cAAc;AAC5B,gBAAU,oBAAoB,IAAI;AAAA,IAAA,WAC7B,CAAC,WAAW,KAAK,SAAS,MAAM,YAAY,GAAG;AACpD,gBAAU,oBAAoB,IAAI;AAAA,IAAA;AAEpC,QAAA,SAAS,eAAe,SAAS;AAC3B,UAAA,CAAC,SAAS,SAAS;AACjB,kBAAU,UAAU;AAAA,MACf,WAAA,SAAS,QAAQ,WAAW,GAAG;AACpC,kBAAU,UAAU;AAAA,MAAA;AAAA,IAC1B;AAEN,QAAI,CAAC,SAAS,SAAU,WAAU,WAAW;AAC7C,QAAI,CAAC,SAAS,UAAW,WAAU,YAAY;AAE/C,cAAU,SAAS;AACnB,WAAO,OAAO,KAAK,SAAS,EAAE,WAAW;AAAA,EAC/C;AACM,QAAA,eAAe,CAAC,MAAuB;AACvC,MAAE,eAAe;AACb,QAAA,CAAC,eAAgB;AAEf,UAAA,OAAO,IAAI,SAAS;AACnB,WAAA,QAAQ,QAAQ,EAAE,QAAQ,CAAC,CAAC,KAAK,KAAK,MAAM;AACzC,UAAA,OAAO,UAAU,UAAU;AAClB,eAAA,QAAQ,KAAK,EAAE,QAAQ,CAAC,CAAC,QAAQ,QAAQ,MAAM;AAChD,eAAK,OAAO,SAAS,MAAM,IAAI,OAAO,QAAQ,CAAC;AAAA,QAAA,CACpD;AAAA,MAAA,OACA;AACD,aAAK,OAAO,KAAK,OAAO,KAAK,CAAC;AAAA,MAAA;AAAA,IACpC,CACL;AACI,SAAA,OAAO,SAAS,aAAa;AAC7B,SAAA,OAAO,cAAc,iBAAiB;AAC3C,YAAQ,OAAO,MAAM,EAAE,QAAQ,aAAa,QAAQ,QAAQ;AAAA,EAClE;AAEM,SAAAC,kCAAA,IAAC,UAAO,MAAM,QAAQ,cAAc,SAC9B,UAAAC,kCAAA,KAAC,eAAc,EAAA,WAAU,kDAClB,UAAA;AAAA,IACK,aAAAD,kCAAAA,IAAC,SAAI,WAAU,uFACT,gDAAC,eAAc,EAAA,SAAS,WAAW,EACzC,CAAA;AAAA,IAENA,kCAAAA,IAAC,gBAAa,WAAU,iDAClB,gDAAC,aAAY,EAAA,WAAU,qBAAoB,UAAA,gBAAA,CAAa,EAC9D,CAAA;AAAA,IACCC,kCAAA,KAAA,MAAA,EAAK,UAAU,cAAc,WAAU,wCAElC,UAAA;AAAA,MAACD,kCAAA,IAAA,MAAA,EAAG,WAAU,iBAAgB,UAAa,iBAAA;AAAA,MAC3CC,kCAAA;AAAA,QAAC;AAAA,QAAA;AAAA,UACK,OAAO,SAAS;AAAA,UAChB,eAAe,CAAC,QAA2B,YAAY,CAAC,UAAU,EAAE,GAAG,MAAM,YAAY,IAAA,EAAM;AAAA,UAC/F,WAAU;AAAA,UAEV,UAAA;AAAA,YAACA,kCAAAA,KAAA,OAAA,EAAI,WAAU,+BACT,UAAA;AAAA,cAAAD,kCAAA,IAAC,gBAAe,EAAA,IAAG,cAAa,OAAM,SAAQ;AAAA,cAC7CA,kCAAA,IAAA,OAAA,EAAM,SAAQ,cAAa,UAAc,iBAAA,CAAA;AAAA,YAAA,GAChD;AAAA,YACAC,kCAAAA,KAAC,OAAI,EAAA,WAAU,+BACT,UAAA;AAAA,cAAAD,kCAAA,IAAC,gBAAe,EAAA,IAAG,cAAa,OAAM,SAAQ;AAAA,cAC7CA,kCAAA,IAAA,OAAA,EAAM,SAAQ,cAAa,UAAU,aAAA,CAAA;AAAA,YAAA,EAC5C,CAAA;AAAA,UAAA;AAAA,QAAA;AAAA,MACN;AAAA,MAECA,kCAAA,IAAA,MAAA,EAAG,WAAU,iBAAgB,UAAgB,oBAAA;AAAA,MAC9CC,kCAAAA,KAAC,OAAI,EAAA,WAAU,cACT,UAAA;AAAA,QAACA,kCAAAA,KAAA,OAAA,EAAI,WAAU,qCACT,UAAA;AAAA,UAACA,kCAAAA,KAAA,OAAA,EAAM,WAAU,iCAAgC,UAAA;AAAA,YAAA;AAAA,kDAE1C,OAAM,EAAA,MAAK,QAAO,MAAK,QAAO,aAAY,QAAO,OAAO,SAAS,MAAM,UAAU,cAAc,WAAW,OAAO,OAAO,mBAAmB,IAAI;AAAA,YAC/I,OAAO,QAAQD,kCAAA,IAAC,UAAK,WAAU,wBAAwB,iBAAO,KAAK,CAAA;AAAA,UAAA,GAC1E;AAAA,UACAC,kCAAAA,KAAC,OAAM,EAAA,WAAU,iCAAgC,UAAA;AAAA,YAAA;AAAA,kDAE1C,OAAM,EAAA,MAAK,QAAO,MAAK,WAAU,aAAY,WAAU,OAAO,SAAS,SAAS,UAAU,cAAc,UAAQ,MAAC,WAAW,OAAO,UAAU,mBAAmB,IAAI;AAAA,YACpK,OAAO,WAAWD,kCAAA,IAAC,UAAK,WAAU,wBAAwB,iBAAO,QAAQ,CAAA;AAAA,UAAA,EAEhF,CAAA;AAAA,QAAA,GACN;AAAA,QAEAC,kCAAAA,KAAC,OAAI,EAAA,WAAU,mCACT,UAAA;AAAA,UAACA,kCAAAA,KAAA,OAAA,EAAM,WAAU,gCAA+B,UAAA;AAAA,YAAA;AAAA,kDAEzC,OAAM,EAAA,MAAK,SAAQ,MAAK,SAAQ,aAAY,SAAQ,UAAU,cAAc,UAAQ,MAAC,WAAW,OAAO,QAAQ,mBAAmB,IAAI;AAAA,YACtI,OAAO,SAASD,kCAAA,IAAC,UAAK,WAAU,wBAAwB,iBAAO,MAAM,CAAA;AAAA,UAAA,GAC5E;AAAA,UACAC,kCAAAA,KAAC,OAAM,EAAA,WAAU,kCAAiC,UAAA;AAAA,YAAA;AAAA,YAE5CD,kCAAA;AAAA,cAAC;AAAA,cAAA;AAAA,gBACK,MAAK;AAAA,gBACL,MAAK;AAAA,gBACL,OAAO,SAAS;AAAA,gBAChB,UAAU,CAAC,MAAM;AACX,wBAAM,QAAQ,EAAE,OAAO,MAAM,QAAQ,OAAO,EAAE;AAC1C,sBAAA,MAAM,UAAU,IAAI;AAClB,gCAAY,CAAC,UAAU,EAAE,GAAG,MAAM,uBAAuB,QAAQ;AAAA,kBAAA;AAAA,gBAE7E;AAAA,gBACA,WAAW,OAAO,wBAAwB,mBAAmB;AAAA,gBAC7D,aAAY;AAAA,cAAA;AAAA,YAClB;AAAA,YACC,OAAO,yBACFA,kCAAA,IAAC,UAAK,WAAU,wBAAwB,iBAAO,sBAAsB,CAAA;AAAA,UAAA,EAEjF,CAAA;AAAA,QAAA,EACN,CAAA;AAAA,MAAA,GACN;AAAA,MACCA,kCAAA,IAAA,MAAA,EAAG,WAAU,iBAAgB,UAAa,iBAAA;AAAA,MAC3CC,kCAAAA,KAAC,OAAI,EAAA,WAAU,aACT,UAAA;AAAA,QAACA,kCAAAA,KAAA,OAAA,EAAI,WAAU,mCACT,UAAA;AAAA,UAACA,kCAAAA,KAAA,OAAA,EAAM,WAAU,iCAAgC,UAAA;AAAA,YAAA;AAAA,YAE3CD,kCAAAA,IAAC,OAAM,EAAA,MAAK,QAAO,MAAK,mBAAkB,aAAY,cAAa,UAAU,cAAc,UAAQ,KAAC,CAAA;AAAA,UAAA,GAC1G;AAAA,UACAC,kCAAAA,KAAC,OAAM,EAAA,WAAU,iCAAgC,UAAA;AAAA,YAAA;AAAA,YAE3CD,kCAAAA,IAAC,OAAM,EAAA,MAAK,QAAO,MAAK,kBAAiB,aAAY,aAAY,UAAU,cAAc,UAAQ,KAAC,CAAA;AAAA,UAAA,EACxG,CAAA;AAAA,QAAA,GACN;AAAA,QAEAC,kCAAAA,KAAC,OAAI,EAAA,WAAU,oCACT,UAAA;AAAA,UAACA,kCAAAA,KAAA,OAAA,EAAM,WAAU,iCAAgC,UAAA;AAAA,YAAA;AAAA,YAE3CD,kCAAAA,IAAC,OAAM,EAAA,MAAK,SAAQ,MAAK,eAAc,aAAY,eAAc,UAAU,cAAc,UAAQ,KAAC,CAAA;AAAA,UAAA,GACxG;AAAA,UACAC,kCAAAA,KAAC,OAAM,EAAA,WAAU,kCAAiC,UAAA;AAAA,YAAA;AAAA,YAE5CD,kCAAA;AAAA,cAAC;AAAA,cAAA;AAAA,gBAAM,MAAK;AAAA,gBAAS,MAAK;AAAA,gBAAqB,OAAO,SAAS,MAAM;AAAA,gBAE/D,UAAU,CAAC,MAAM;AACX,wBAAM,QAAQ,EAAE,OAAO,MAAM,QAAQ,OAAO,EAAE;AAC1C,sBAAA,MAAM,UAAU,IAAI;AAClB,gCAAY,CAAC,UAAU;AAAA,sBACjB,GAAG;AAAA,sBACH,OAAO;AAAA,wBACD,GAAG,KAAK;AAAA,wBACR,cAAc;AAAA,sBAAA;AAAA,oBACpB,EACJ;AAAA,kBAAA;AAAA,gBAEd;AAAA,gBAEA,aAAY;AAAA,gBACZ,WAAW,OAAO,oBAAoB,IAAI,mBAAmB;AAAA,cAAA;AAAA,YAAI;AAAA,YACtE,OAAO,oBAAoB,KAAKA,kCAAA,IAAC,UAAK,WAAU,wBAAwB,UAAO,OAAA,oBAAoB,EAAE,CAAA;AAAA,UAAA,EAC5G,CAAA;AAAA,QAAA,GACN;AAAA,+CACC,OAAM,EAAA,UAAA;AAAA,UAAA;AAAA,UAEDA,kCAAAA,IAAC,OAAM,EAAA,MAAK,QAAO,MAAK,iBAAgB,aAAY,iBAAgB,UAAU,cAAc,UAAQ,KAAC,CAAA;AAAA,QAAA,EAC3G,CAAA;AAAA,MAAA,GACN;AAAA,MAECA,kCAAA,IAAA,MAAA,EAAG,WAAU,iBAAgB,UAAQ,YAAA;AAAA,MACrC,SAAS,eAAe,WAAYC,kCAAA,KAAA,OAAA,EAAM,WAAU,uCAAsC,UAAA;AAAA,QAAA;AAAA,QAErFD,kCAAAA,IAAC,OAAM,EAAA,MAAK,QAAO,MAAK,WAAU,aAAY,WAAU,UAAU,cAAc,UAAQ,KAAC,CAAA;AAAA,QACxF,OAAO,WAAWA,kCAAA,IAAC,UAAK,WAAU,wBAAwB,iBAAO,QAAQ,CAAA;AAAA,MAAA,GAChF;AAAA,MACAC,kCAAAA,KAAC,OAAI,EAAA,WAAU,oCAET,UAAA;AAAA,QAACA,kCAAAA,KAAA,OAAA,EAAM,WAAU,sCAAqC,UAAA;AAAA,UAAA;AAAA,UAEhDD,kCAAAA,IAAC,OAAM,EAAA,MAAK,QAAO,MAAK,YAAW,aAAY,YAAW,UAAU,cAAc,UAAQ,KAAC,CAAA;AAAA,QAAA,GACjG;AAAA,QACAC,kCAAAA,KAAC,OAAM,EAAA,WAAU,iCAAgC,UAAA;AAAA,UAAA;AAAA,UAE3CD,kCAAAA,IAAC,OAAM,EAAA,MAAK,QAAO,MAAK,aAAY,aAAY,aAAY,UAAU,cAAc,UAAQ,KAAC,CAAA;AAAA,QAAA,EACnG,CAAA;AAAA,MAAA,GACN;AAAA,4CACC,OAAI,EAAA,WAAU,oBACT,UAAAA,sCAAC,UAAO,MAAK,UAAS,WAAU,+CAA8C,SAAS,WAAW,UAAU,WAAY,UAAa,aAAA,kBAAkB,iBAAgB,EAC7K,CAAA;AAAA,IAAA,EACN,CAAA;AAAA,EAAA,EAAA,CACN,EACN,CAAA;AAEZ;AC1MA,SAAwBE,uBAAuB;AACvC,QAAA;AAAA,IAAEC;AAAAA,EAAU,IAAIC,SAAS;AACzB,QAAA;AAAA,IAAEC;AAAAA,EAAW,IAAIC,cAA0B;AAChCD,aAAWE;AAC5B,QAAM,CAACC,aAAaC,cAAc,IAAIX,aAAAA,SAAS,KAAK;AACpD,QAAMY,OAAOC,YAAY;AACzB,QAAM,CAACC,WAAWC,YAAY,IAAIf,aAAAA,SAAS,KAAK;AAEhD,QAAM,CAACgB,cAAcC,eAAe,IAAIjB,sBAAkC,MAAM;AACvE,WAAAkB,MAAMC,QAAQZ,UAAU,IAC3BA,WAAWa,OAAO,CAACC,KAAKC,WAAW;AAC/BD,UAAAC,OAAOC,EAAE,IAAID,OAAOE;AACjB,aAAAH;AAAAA,IACT,GAAG,CAAA,CAA6B,IAC9B,CAAC;AAAA,EACP,CAAC;AAED,QAAMI,UAAUC,WAAqC;AAE/C,QAAAC,eAAe,OAAOC,aAAqB;AAE/CX,oBAAiBY,WAAU;AAAA,MACzB,GAAGA;AAAAA,MACH,CAACD,QAAQ,GAAG,CAACC,KAAKD,QAAQ;AAAA;AAAA,IAC5B,EAAE;AAEI,UAAAE,WAAW,IAAIC,SAAS;AAC9BD,aAASE,OAAO,YAAYJ,SAASK,SAAA,CAAU;AACtCH,aAAAE,OAAO,cAAc,oBAAoB;AAE9C,QAAA;AACF,YAAMP,QAAQS,OAAOJ,UAAU;AAAA,QAAEK,QAAQ;AAAA,MAAM,CAAC;AAC5C,UAAAV,QAAQW,UAAU,QAAQ;AAC5B/B,kBAAU,qCAAqC,SAAS;AAAA,MAC1D;AAAA,aACOgC,OAAO;AACNC,cAAAD,MAAM,4BAA4BA,KAAK;AAG/CpB,sBAAiBY,WAAU;AAAA,QACzB,GAAGA;AAAAA,QACH,CAACD,QAAQ,GAAG,CAACC,KAAKD,QAAQ;AAAA,MAC5B,EAAE;AAAA,IACJ;AAAA,EACF;AAGM,QAAAW,uBAAwBC,SAAgB;AAC5CF,YAAQG,IAAI,oBAAoB;AAC1B,UAAAX,WAAW,IAAIC,SAAS;AAC9BW,cAAUF,GAAG;AACJV,aAAAE,OAAO,cAAc,UAAU;AACxCF,aAASE,OAAO,YAAYQ,IAAIP,SAAA,CAAU;AAC1CR,YAAQS,OAAOJ,UAAU;AAAA,MAAEK,QAAQ;AAAA,IAAO,CAAC;AAAA,EAC7C;AACAlC,eAAAA,UAAU,MAAM;AACd,QAAIwB,QAAQW,UAAU,UAAUX,QAAQkB,QAAQ,gBAAgBlB,QAAQkB,MAAM;AAC5E,YAAMA,OAAOlB,QAAQkB;AACrB1B,sBAAiBY,WACdc,KAAKpC,cAAc,CAAA,GAAIa,OAAO,CAACC,KAAKC,WAAW;AACvC,eAAAD;AAAAA,MACT,GAAG,CAA6B,CAAA,CAClC;AAAA,IACF;AAAA,KACC,CAACI,QAAQW,OAAOX,QAAQkB,IAAI,CAAC;AAChC,QAAM,CAACC,YAAYC,aAAa,IAAI7C,aAAAA,SAAS,EAAE;AAC/C,QAAM,CAAC8C,cAAcC,eAAe,IAAI/C,aAAAA,SAAmB,CAAA,CAAE;AACvD,QAAAgD,eAAgBC,SAAgB;AACpCJ,kBAAcI,GAAG;AAAA,EACnB;AAEA,QAAM,CAACC,QAAQR,SAAS,IAAI1C,aAAAA,SAAS,CAAC;AAEtCC,eAAAA,UAAU,MAAM;AACd,QAAIM,YAAY;AACdwC,sBAAgBxC,UAAU;AAAA,IAC5B;AAAA,EACF,GAAG,CAACA,UAAU,CAAC;AAEfN,eAAAA,UAAU,MAAM;AACd,QAAI,OAAO2C,eAAe,YAAYA,WAAWnC,UAAU,GAAG;AAC5D,YAAM0C,kBAAkB5C,yCAAY6C,OACjCC,WACCA,6BAAMC,SACN,OAAOD,KAAKC,SAAS,YACrBD,KAAKC,KAAKC,YAAY,EAAEC,SAASZ,WAAWW,aAAa;AAE7CR,sBAAAI,mBAAmB,EAAE;AAC7Bb,cAAAG,IAAIU,iBAAiB,kBAAkB;AAAA,IACjD,OAAO;AACWJ,sBAAAxC,cAAc,EAAE;AACxB+B,cAAAG,IAAIlC,YAAY,aAAa;AAAA,IACvC;AAAA,EACF,GAAG,CAACqC,YAAYrC,UAAU,CAAC;AAG3BN,eAAAA,UAAU,MAAM;;AAEV,SAAAwB,wCAASkB,SAATlB,mBAAegC,SAAS;AAE1B1C,mBAAa,IAAI;AAETuB,cAAAG,IAAIhB,QAAQkB,MAAM,oBAAoB;AAC9CtC,gBAAU,2BAA2B,SAAS;AAAA,IAEvC,aAAAoB,aAAQkB,SAARlB,mBAAcgC,aAAY,OAAO;AACxC1C,mBAAa,KAAK;AAClBV,gBAAU,uBAAuB,OAAO;AAAA,IAC1C;AAAA,EAEF,GAAG,CAACoB,QAAQkB,IAAI,CAAC;AAGX,QAAAe,UAAUjC,QAAQW,UAAU;AAKhC,SAAAjC,kCAAAA,KAAC,OAAI;AAAA,IAAAwD,WAAU;AAAA,IACFC,UAAA,CAAWF,WAAAxD,kCAAA,IAAC2D;MAAcH;AAAAA,IAAkB,CAAA,GAGtDxD,kCAAA,IAAA,MAAA;AAAA,MAAGyD,WAAU;AAAA,MAA0BC,UAAiB;AAAA,IAAA,CAAA,GACzDzD,kCAAA,KAAC,OAAI;AAAA,MAAAwD,WAAU;AAAA,MACbC,UAAA,CAAC1D,kCAAA,IAAA4D,QAAA;AAAA,QAAOH,WAAU;AAAA,MAAmE,CAAA,GACrFzD,kCAAA,IAAC6D,OAAA;AAAA,QACCC,MAAK;AAAA,QACLC,aAAY;AAAA,QACZC,OAAOtB;AAAAA,QACPuB,UAAWC,OAAuCpB,aAAaoB,EAAEC,OAAOH,KAAK;AAAA,QAC7EP,WAAU;AAAA,QACVW,WAAS;AAAA,MAAA,CACX,CAAA;AAAA,IACF,CAAA,0CACCC,OACC;AAAA,MAAAX,UAAA,CAAA1D,kCAAA,IAACsE,aAAY;AAAA,QAAAb,WAAU;AAAA,QACrBC,UAAAzD,kCAAA,KAACsE,UACC;AAAA,UAAAb,UAAA,CAAA1D,kCAAA,IAACwE;YAAUd,UAAE;AAAA,UAAA,CAAA,GACb1D,kCAAA,IAACwE;YAAUd,UAAI;AAAA,UAAA,CAAA,GACf1D,kCAAA,IAACwE;YAAUd,UAAc;AAAA,UAAA,CAAA,GACzB1D,kCAAA,IAACwE;YAAUd,UAAgB;AAAA,UAAA,CAAA,GAC3B1D,kCAAA,IAACwE;YAAUd,UAAM;AAAA,WAAA,yCAChBc,WAAU,EAAA,CAAA;AAAA,QAEb,CAAA;AAAA,MACF,CAAA,GAEAxE,kCAAA,IAACyE,WAEE;AAAA,QAAAf,UAAAd,aAAarC,SAAS,IACrBqC,aAAa8B,IAAKtD,YAChBnB,kCAAA,KAACsE,UACC;AAAA,UAAAb,UAAA,CAAC1D,kCAAA,IAAA2E,WAAA;AAAA,YAAWjB,iBAAOrC;AAAAA,UAAG,CAAA,GACtBrB,kCAAA,IAAC2E,WAAA;AAAA,YAAUlB,WAAW;AAAA,YACpBmB,SAASA,MACPlE,KAAK,gCAAgCU,OAAOC,EAAE,eAAeD,OAAOgC,IAAI,cAAchC,iCAAQyD,UAAU,EAAE;AAAA,YACvGnB,UAAOtC,OAAAgC;AAAAA,UAAA,CAAK,yCAElBuB,WAAW;AAAA,YAAAjB,UAAAtC,OAAO0D,gBAAgB,IAAI1D,OAAO0D,gBAAgB;AAAA,UAAI,CAAA,GAElE9E,kCAAA,IAAA2E,WAAA;AAAA,YACdjB,UAACzD,kCAAA,KAAA,OAAA;AAAA,cAAIwD,WAAU;AAAA,cACbC,UAAA,CAAA1D,kCAAA,IAAC+E,MAAK;AAAA,gBAAAC,IAAI,0BAA0B5D,OAAOC,EAAE;AAAA,gBAC3CqC,UAAA1D,kCAAA,IAACiF,QAAO;AAAA,kBAAAC,SAAQ;AAAA,kBAAUC,MAAK;AAAA,kBAAK1B,WAAU;AAAA,kBAAsDC;gBAEpG,CAAA;AAAA,cACF,CAAA,yCACCqB,MAAK;AAAA,gBAAAC,IAAI,8BAA8B5D,OAAOC,EAAE;AAAA,gBAC/CqC,UAAA1D,kCAAA,IAACiF,QAAO;AAAA,kBAAAC,SAAQ;AAAA,kBAAUC,MAAK;AAAA,kBAAK1B,WAAU;AAAA,kBAAsDC;gBAEpG,CAAA;AAAA,cACF,CAAA,CAAA;AAAA,YACF,CAAA;AAAA,UACF,CAAA,yCACiBiB,WACC;AAAA,YAAAjB,UAAA1D,kCAAA,IAACoF,QAAA;AAAA,cACCC,SAASvE,aAAaM,OAAOC,EAAE;AAAA,cAC/BiE,iBAAiBA,MAAM7D,aAAaL,OAAOC,EAAE;AAAA,YAC/C,CAAA;AAAA,UACF,CAAA,yCAGCsD,WACC;AAAA,YAAAjB,UAAA1D,kCAAA,IAAC,UAAA;AAAA,cACCyD,WAAW;AAAA;AAAA,YAElB7C,aAAaQ,OAAOC,OAAO2B,SAAU,iBAAiB,aAAa;AAAA;AAAA;AAAA;AAAA,cAI5D4B,SAASA,MAAMvC,qBAAqBjB,iCAAQC,EAAE;AAAA,cAC9CkE,UAAU/B;AAAAA,cAEVE,UAAAzD,kCAAA,KAAC,QAAK;AAAA,gBAAAwD,WAAU;AAAA,gBACZC,UAAA,CAAWF,WAAApC,OAAOC,OAAO2B,SACzB/C,kCAAA,KAAC,OAAA;AAAA,kBACCwD,WAAU;AAAA,kBACV+B,OAAM;AAAA,kBACNC,MAAK;AAAA,kBACLC,SAAQ;AAAA,kBAERhC,UAAA,CAAA1D,kCAAA,IAAC,UAAA;AAAA,oBACCyD,WAAU;AAAA,oBACVkC,IAAG;AAAA,oBACHC,IAAG;AAAA,oBACHC,GAAE;AAAA,oBACFC,QAAO;AAAA,oBACPC,aAAY;AAAA,kBAAA,CACd,GACA/F,kCAAA,IAAC,QAAA;AAAA,oBACCyD,WAAU;AAAA,oBACVgC,MAAK;AAAA,oBACLO,GAAE;AAAA,kBAAA,CACJ,CAAA;AAAA,gBAAA,CACF,IACGpF,aAAaQ,OAAOC,OAAO2B,SAC9BhD,kCAAA,IAAC,OAAA;AAAA,kBACCyD,WAAU;AAAA,kBACV+B,OAAM;AAAA,kBACNC,MAAK;AAAA,kBACLC,SAAQ;AAAA,kBACRI,QAAO;AAAA,kBAEPpC,UAAA1D,kCAAA,IAAC,QAAA;AAAA,oBACCiG,eAAc;AAAA,oBACdC,gBAAe;AAAA,oBACfH,aAAY;AAAA,oBACZC,GAAE;AAAA,kBACJ,CAAA;AAAA,gBACF,CAAA,IACE,MACFxC,WAAWpC,OAAOC,OAAO2B,SAAU,eAAgBpC,aAAaQ,OAAOC,OAAO2B,SAAU,YAAY,WAAA;AAAA,cACxG,CAAA;AAAA,YACF,CAAA;AAAA,UACF,CAAA,CAAA;AAAA,QAAA,GAnFa5B,OAAOC,EAoFtB,CACD,0CAEAkD,UACC;AAAA,UAAAb,UAAA1D,kCAAA,IAAC2E,WAAU;AAAA,YAAAwB,SAAS;AAAA,YAAG1C,WAAU;AAAA,YAAmBC,UAAA;AAAA,UAEpD,CAAA;AAAA,QACF,CAAA;AAAA,MAEJ,CAAA,CAAA;AAAA,IACF,CAAA,GACA1D,kCAAA,IAACiF;MAAOxB,WAAU;AAAA,MAAqDmB,SAASA,MAAMnE,eAAe,IAAI;AAAA,MAAGiD,UAAY;AAAA,IAAA,CAAA,GAExH1D,kCAAA,IAACoG;MAAaC,QAAQ7F;AAAAA,MAAa8F,SAASA,MAAM7F,eAAe,KAAK;AAAA,IAAG,CAAA,CAAA;AAAA,EAC3E,CAAA;AAEJ;"}