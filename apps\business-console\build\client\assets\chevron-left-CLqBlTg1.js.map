{"version": 3, "file": "chevron-left-CLqBlTg1.js", "sources": ["../../../node_modules/lucide-react/dist/esm/icons/chevron-left.js"], "sourcesContent": ["/**\n * @license lucide-react v0.453.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst ChevronLeft = createLucideIcon(\"ChevronLeft\", [\n  [\"path\", { d: \"m15 18-6-6 6-6\", key: \"1wnfg3\" }]\n]);\n\nexport { ChevronLeft as default };\n//# sourceMappingURL=chevron-left.js.map\n"], "names": [], "mappings": ";AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASK,MAAC,cAAc,iBAAiB,eAAe;AAAA,EAClD,CAAC,QAAQ,EAAE,GAAG,kBAAkB,KAAK,SAAU,CAAA;AACjD,CAAC;", "x_google_ignoreList": [0]}