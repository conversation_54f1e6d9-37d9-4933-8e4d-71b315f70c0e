import { r as reactExports, j as jsxRuntimeExports } from "./jsx-runtime-bB2y7OuD.js";
import { S as Select, a as SelectTrigger, b as SelectValue, c as SelectContent, d as SelectItem } from "./select-BFGSXKcr.js";
import { B as Button } from "./button-ByAXMyvk.js";
import { a as useFetcher, u as useLoaderData, c as useSubmit } from "./components-D7UvGag_.js";
import { C as Circle } from "./radio-group-ChzooXbR.js";
import { c as createLucideIcon } from "./createLucideIcon-uwkRm45G.js";
import { X } from "./x-CCG_WJDF.js";
import { P as Pencil } from "./pencil-C1goHmP8.js";
import { T as Trash2 } from "./trash-2-DjkfFIB-.js";
import { T as Tabs, a as TabsList, b as TabsTrigger, c as TabsContent } from "./tabs-CfSdyzWr.js";
import { d as decodePolygon, L as LoadScript, G as GoogleMap, P as Polygon, I as InfoWindow, M as Marker } from "./polyline-utils-DkQLXyiU.js";
import { C as Checkbox } from "./checkbox-DoRUSdrQ.js";
import { D as Dialog, a as DialogContent, b as DialogTitle, c as DialogHeader } from "./dialog-BqKosxNq.js";
import { I as Input } from "./input-3v87qohQ.js";
import { u as useToast } from "./ToastProvider-rciVe3-g.js";
import { S as SpinnerLoader } from "./SpinnerLoader-CAZRmIJX.js";
import { R as ResponsiveTable } from "./responsiveTable-CMOWL3Wl.js";
import { S as Switch } from "./switch-DQ_qW6ch.js";
import NetWorkConfig from "./netWorkConfig-DCee6agm.js";
import { S as Save } from "./save-xzNIILKr.js";
import { a as useNavigate } from "./index-DhHTcibu.js";
import "./index-Vp2vNLNM.js";
import "./index-IXOTxK3N.js";
import "./index-D7VH9Fc8.js";
import "./index-qCtcpOcW.js";
import "./index-z_byfFrQ.js";
import "./index-BTdCMChR.js";
import "./index-BXzPK7u0.js";
import "./index-CzZ6EYkN.js";
import "./index-CEHS9zzk.js";
import "./index-DVTNuYOr.js";
import "./index-DscYByPT.js";
import "./index-CkL5tk39.js";
import "./index-dIGjFc0I.js";
import "./index-CpKiYcZd.js";
import "./index-C88PRvfd.js";
import "./utils-GkgzjW3c.js";
import "./chevron-down-pCP5jmjX.js";
import "./check-_dbWxIzT.js";
import "./index-ImHKLo0a.js";
import "./index-CG37gmC0.js";
import "./index-QLGF6kQx.js";
import "./index-DdafHWkt.js";
import "./label-cSASrwzW.js";
import "./trash-cZnr6Uhr.js";
import "./loader-circle-BLZgch8Y.js";
import "./square-pen-BXxSi9JH.js";
/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */
const CircleCheckBig = createLucideIcon("CircleCheckBig", [
  ["path", { d: "M21.801 10A10 10 0 1 1 17 3.335", key: "yps3ct" }],
  ["path", { d: "m9 11 3 3L22 4", key: "1pflzl" }]
]);
const NetworkAreaCard = ({
  networkAreaDetail,
  agentDetails,
  networkId,
  color,
  netWorks,
  isSalesPermission
}) => {
  const [networkStatus, setNetworkStatus] = reactExports.useState(
    () => (netWorks ?? []).reduce((acc, network) => {
      if ((network == null ? void 0 : network.networkAreaId) !== void 0) {
        acc[network.networkAreaId] = (network == null ? void 0 : network.disabled) ?? false;
      }
      return acc;
    }, {})
  );
  const [areaId, setAreaId] = reactExports.useState(0);
  const [editNetworkId, setEditNetworkId] = reactExports.useState(0);
  const [agentUserId, setAgentUserId] = reactExports.useState(0);
  const handleNewLocalities2 = reactExports.useCallback(
    (id, networkId2, agentUserId2) => {
      if (id && networkId2 && agentUserId2) {
        setAreaId(id);
        setEditNetworkId(networkId2);
        setAgentUserId(parseFloat(agentUserId2));
      }
    },
    []
  );
  const [editLocality, setEditLocality] = reactExports.useState(0);
  const fetcher = useFetcher();
  const handleSwitchNetwork = async (networkAreaId) => {
    setNetworkStatus((prev) => ({
      ...prev,
      [networkAreaId]: !prev[networkAreaId]
    }));
    const formData = new FormData();
    formData.append("nAreaId", networkAreaId.toString());
    formData.append("actionType", "updateNetWorkAreaStatus");
    fetcher.submit(formData, { method: "put" });
  };
  const handleEditNetwork = async () => {
    const formData = new FormData();
    formData.append("masterAreaId", areaId.toString());
    formData.append("editNetworkId", editNetworkId.toString());
    formData.append("agentUserId", agentUserId.toString());
    formData.append("actionType", "updateNetWorkAreaEdit");
    console.log("handleEditNetwork formData.....", formData);
    for (const [key, value] of formData.entries()) {
      console.log(key, value);
    }
    fetcher.submit(formData, { method: "POST" });
    setEditLocality(0);
  };
  const editLocalityClicked = reactExports.useCallback((areaId2) => {
    setEditLocality(areaId2);
  }, []);
  const resetEditLocalityClicked = reactExports.useCallback(() => {
    setEditLocality(0);
  }, []);
  return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex flex-col w-full gap-3 bg-white p-2 rounded-lg shadow", children: [
    /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex gap-2  justify-between align-center items-center", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex flex-col gap-0", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "text-sm text-typography-400 leading-tight", children: [
          "id:  ",
          networkAreaDetail.networkAreaId
        ] }),
        /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "text-lg text-typography-800 leading-tight", children: networkAreaDetail.networkAreaName })
      ] }),
      color && /* @__PURE__ */ jsxRuntimeExports.jsx(Circle, { size: "1rem", fill: `${color}`, color: `${color}` })
    ] }),
    /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "text-md text-typography-600 flex gap-4 w-full items-center", children: [
      networkAreaDetail.agentName && /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "w-full", children: [
        " ",
        editLocality === networkAreaDetail.networkAreaId ? /* @__PURE__ */ jsxRuntimeExports.jsx(jsxRuntimeExports.Fragment, { children: /* @__PURE__ */ jsxRuntimeExports.jsxs(
          Select,
          {
            value: agentUserId.toString(),
            onValueChange: (newDistrict) => {
              handleNewLocalities2(networkAreaDetail.areaId, networkId, newDistrict);
            },
            children: [
              /* @__PURE__ */ jsxRuntimeExports.jsx(SelectTrigger, { children: /* @__PURE__ */ jsxRuntimeExports.jsx(SelectValue, { placeholder: "Please select an agent" }) }),
              /* @__PURE__ */ jsxRuntimeExports.jsx(SelectContent, { children: agentDetails == null ? void 0 : agentDetails.map((district) => /* @__PURE__ */ jsxRuntimeExports.jsxs(SelectItem, { value: district.agentUserId.toString(), children: [
                district.agentUserId,
                " - ",
                district.fullName
              ] }, district.agentUserId)) })
            ]
          }
        ) }) : /* @__PURE__ */ jsxRuntimeExports.jsxs(jsxRuntimeExports.Fragment, { children: [
          " ",
          /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "text-typography-300 font-thin text-sm", children: "Sales Agent : " }),
          networkAreaDetail.agentName,
          " "
        ] })
      ] }),
      !isSalesPermission && /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "flex gap-0", children: editLocality === networkAreaDetail.networkAreaId ? /* @__PURE__ */ jsxRuntimeExports.jsxs(jsxRuntimeExports.Fragment, { children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx(
          Button,
          {
            variant: "ghost",
            size: "sm",
            className: "text-primary hover:text-primary-800",
            onClick: () => handleEditNetwork(),
            disabled: agentUserId === 0,
            children: /* @__PURE__ */ jsxRuntimeExports.jsx(CircleCheckBig, { size: 16 })
          }
        ),
        /* @__PURE__ */ jsxRuntimeExports.jsx(
          Button,
          {
            variant: "ghost",
            size: "sm",
            className: "text-typography-300 hover:text-typography-600",
            onClick: () => resetEditLocalityClicked(),
            children: /* @__PURE__ */ jsxRuntimeExports.jsx(X, { size: 16 })
          }
        ),
        " "
      ] }) : /* @__PURE__ */ jsxRuntimeExports.jsxs(jsxRuntimeExports.Fragment, { children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx(
          Button,
          {
            variant: "ghost",
            size: "sm",
            className: "text-indigo-600 hover:text-indigo-900",
            onClick: () => editLocalityClicked(networkAreaDetail.networkAreaId),
            children: /* @__PURE__ */ jsxRuntimeExports.jsx(Pencil, { size: 16 })
          }
        ),
        /* @__PURE__ */ jsxRuntimeExports.jsx(
          Button,
          {
            variant: "ghost",
            size: "sm",
            className: "text-red-500 hover:text-red-900",
            onClick: () => handleSwitchNetwork(networkAreaDetail.networkAreaId),
            children: /* @__PURE__ */ jsxRuntimeExports.jsx(Trash2, { size: 16 })
          }
        )
      ] }) })
    ] })
  ] });
};
function CreateAgent({ isOpen, onClose, NetWorkId, roles, sellerList }) {
  const fetcher = useFetcher();
  const [managerId, setManagerId] = reactExports.useState("");
  const [formData, setFormData] = reactExports.useState({
    firstName: "",
    lastName: "",
    email: "",
    mobileNumber: "",
    address: "",
    password: "",
    businessId: managerId,
    roles: []
  });
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value
    }));
  };
  const isLoading = fetcher.state !== "idle";
  const { showToast } = useToast();
  reactExports.useEffect(() => {
    if (fetcher.data && isOpen === true) {
      if (fetcher.data !== void 0 && isOpen === true) {
        showToast("AgentCreated SuccessFully", "success");
        onClose();
        setFormData({
          firstName: "",
          lastName: "",
          email: "",
          mobileNumber: "",
          address: "",
          password: "",
          businessId: managerId,
          roles: []
        });
      } else if (fetcher.data === void 0) {
        showToast("Fail To create Agent", "error");
      }
    }
  }, [fetcher.data]);
  const handleSubmit = (e) => {
    e.preventDefault();
    const data = new FormData();
    Object.entries(formData).forEach(([key, value]) => {
      data.append(key, Array.isArray(value) ? value.join(",") : value.toString());
    });
    data.append("netWorkId", NetWorkId.toString());
    data.append("_intent", "createAgent");
    data.append("roles", "AgentFull");
    fetcher.submit(data, { method: "POST" });
  };
  return /* @__PURE__ */ jsxRuntimeExports.jsxs(Dialog, { open: isOpen, onOpenChange: onClose, children: [
    /* @__PURE__ */ jsxRuntimeExports.jsx(SpinnerLoader, { loading: isLoading }),
    /* @__PURE__ */ jsxRuntimeExports.jsxs(DialogContent, { children: [
      isLoading && /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "absolute inset-0 flex items-center justify-center bg-white/10 backdrop-blur-sm", children: /* @__PURE__ */ jsxRuntimeExports.jsx(SpinnerLoader, { loading: isLoading }) }),
      /* @__PURE__ */ jsxRuntimeExports.jsx(DialogTitle, { children: "Add New Agent" }),
      /* @__PURE__ */ jsxRuntimeExports.jsxs("form", { onSubmit: handleSubmit, className: "space-y-4", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex gap-2", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "w-full flex-col flex gap-y-2", children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx("label", { className: "block text-sm font-medium text-gray-700", children: "First Name" }),
            /* @__PURE__ */ jsxRuntimeExports.jsx(Input, { value: formData.firstName, name: "firstName", placeholder: "First Name", onChange: handleChange, required: true })
          ] }),
          /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "w-full flex-col flex gap-y-2", children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx("label", { className: "block text-sm font-medium text-gray-700", children: "Last Name" }),
            /* @__PURE__ */ jsxRuntimeExports.jsx(Input, { name: "lastName", value: formData.lastName, placeholder: "Last Name", onChange: handleChange, required: true })
          ] })
        ] }),
        /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex gap-2", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "w-full flex-col flex gap-y-2", children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx("label", { className: "block text-sm font-medium text-gray-700", children: "Email" }),
            /* @__PURE__ */ jsxRuntimeExports.jsx(
              Input,
              {
                type: "email",
                name: "email",
                placeholder: "Email",
                onChange: handleChange,
                required: true,
                value: formData.email
              }
            )
          ] }),
          /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "w-full flex-col flex gap-y-2", children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx("label", { className: "block text-sm font-medium text-gray-700", children: "Mobile Number" }),
            /* @__PURE__ */ jsxRuntimeExports.jsx(
              Input,
              {
                name: "mobileNumber",
                placeholder: "Mobile Number",
                type: "number",
                onChange: (e) => {
                  const value = e.target.value.replace(/\D/g, "");
                  if (value.length <= 10) {
                    setFormData((prev) => ({
                      ...prev,
                      mobileNumber: value
                    }));
                  }
                },
                value: formData.mobileNumber,
                required: true
              }
            )
          ] })
        ] }),
        /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "w-full flex-col flex gap-y-2", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx("label", { className: "block text-sm font-medium text-gray-700", children: "Address" }),
          /* @__PURE__ */ jsxRuntimeExports.jsx(
            Input,
            {
              name: "address",
              placeholder: "Address",
              onChange: handleChange,
              required: true,
              value: formData.address
            }
          )
        ] }),
        /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: " flex justify-end items-end", children: /* @__PURE__ */ jsxRuntimeExports.jsx(Button, { type: "submit", loading: isLoading, children: "Save Agent" }) })
      ] })
    ] })
  ] });
}
function NetWorkModal({ isOpen, onClose, sellerList, netWorkId }) {
  const [sellerId, setSellerId] = reactExports.useState("");
  const fetcher = useFetcher();
  const handleSave = () => {
    const formData = new FormData();
    formData.append("_intent", "createNetWork");
    formData.append("netWorkId", netWorkId);
    formData.append("sellerId", sellerId);
    fetcher.submit(formData, { method: "POST" });
  };
  const { showToast } = useToast();
  const isLoading = fetcher.state !== "idle";
  reactExports.useEffect(() => {
    if (fetcher.data && isOpen === true) {
      if (fetcher.data) {
        showToast("seller to Network SuccessFully", "success");
        onClose();
      } else {
        showToast("sellerMapping Failed", "error");
      }
    }
  }, [fetcher.data]);
  return /* @__PURE__ */ jsxRuntimeExports.jsx(Dialog, { open: isOpen, onOpenChange: onClose, children: /* @__PURE__ */ jsxRuntimeExports.jsxs(DialogContent, { className: "max-h-[50vh]  max-w-sm overflow-y-auto", children: [
    isLoading && /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "absolute inset-0 flex items-center justify-center bg-white/10 backdrop-blur-sm", children: /* @__PURE__ */ jsxRuntimeExports.jsx(SpinnerLoader, { loading: isLoading }) }),
    /* @__PURE__ */ jsxRuntimeExports.jsx(DialogHeader, { children: /* @__PURE__ */ jsxRuntimeExports.jsx(DialogTitle, { children: "Map Seller to Network" }) }),
    /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex max-w-s flex-col gap-4", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsxs(
        "select",
        {
          className: "border p-2 rounded",
          value: sellerId,
          onChange: (e) => setSellerId(e.target.value ? Number(e.target.value) : ""),
          children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx("option", { value: "", children: "Select Seller" }),
            sellerList.map((seller) => /* @__PURE__ */ jsxRuntimeExports.jsx("option", { value: seller.id, children: seller.name }, seller.id))
          ]
        }
      ),
      /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex justify-end gap-2", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx(Button, { variant: "outline", onClick: onClose, children: "Cancel" }),
        /* @__PURE__ */ jsxRuntimeExports.jsx(Button, { onClick: handleSave, disabled: !sellerId, loading: isLoading, children: "Save" })
      ] })
    ] })
  ] }) });
}
function CreateNetworkBannerModal({ isOpen, onClose, networkId }) {
  const [bannerUrl, setBannerUrl] = reactExports.useState("");
  const [previewUrl, setPreviewUrl] = reactExports.useState("");
  const [sequenceId, setSequenceId] = reactExports.useState("");
  const fileInputRef = reactExports.useRef(null);
  const [uploadError, setUploadError] = reactExports.useState(null);
  const uploadFetcher = useFetcher();
  const bannerFetcher = useFetcher();
  const handleFileSelect = async (event) => {
    const files = Array.from(event.target.files || []);
    if (files.length === 0) return;
    setUploadError(null);
    const MAX_FILE_SIZE = 500 * 1024;
    const allowedTypes = ["image/jpeg", "image/png", "image/gif", "image/webp"];
    const validFile = files.find((file) => {
      if (file.size > MAX_FILE_SIZE) {
        setUploadError("File size exceeds 500Kb limit.");
        return false;
      }
      if (!allowedTypes.includes(file.type)) {
        setUploadError("Only JPEG, PNG, GIF, and WEBP images are allowed.");
        return false;
      }
      return true;
    });
    if (!validFile) return;
    const uploadFormData = new FormData();
    uploadFormData.append("_action", "uploadImage");
    uploadFormData.append("file", validFile, validFile.name);
    uploadFetcher.submit(uploadFormData, {
      method: "post",
      action: "/home/<USER>",
      encType: "multipart/form-data"
    });
  };
  const handleSave = () => {
    if (bannerUrl && sequenceId !== "") {
      const formData = new FormData();
      formData.append("bannerUrl", bannerUrl);
      formData.append("sequenceId", sequenceId.toString());
      formData.append("networkId", networkId.toString());
      formData.append("actionType", "createNetworkBanner");
      bannerFetcher.submit(formData, { method: "POST" });
    }
  };
  const handleClose = () => {
    onClose();
    setBannerUrl("");
    setPreviewUrl("");
    setSequenceId("");
  };
  const { showToast } = useToast();
  reactExports.useEffect(() => {
    var _a, _b;
    if (bannerFetcher == null ? void 0 : bannerFetcher.data) {
      if ((_a = bannerFetcher.data) == null ? void 0 : _a.sucess) {
        showToast("Network banner Created Successfully", "success");
        onClose();
      } else {
        if (((_b = bannerFetcher.data) == null ? void 0 : _b.sucess) == false) {
          showToast("Network banner Failed", "error");
        }
      }
    }
  }, [bannerFetcher.data]);
  reactExports.useEffect(() => {
    if (uploadFetcher.data) {
      if (uploadFetcher.data.error) {
        setUploadError(uploadFetcher.data.error);
      } else if (uploadFetcher.data.fileUrl) {
        const uploadedUrl = uploadFetcher.data.fileUrl;
        setBannerUrl(
          uploadedUrl
          // `!` asserts it's not null
        );
        setPreviewUrl(
          uploadedUrl
        );
        setUploadError(null);
        if (fileInputRef.current) fileInputRef.current.value = "";
      }
    }
  }, [uploadFetcher.data]);
  return /* @__PURE__ */ jsxRuntimeExports.jsx(Dialog, { open: isOpen, onOpenChange: handleClose, children: /* @__PURE__ */ jsxRuntimeExports.jsxs(DialogContent, { children: [
    /* @__PURE__ */ jsxRuntimeExports.jsx(DialogTitle, { children: "Create Network Banner" }),
    /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex flex-col gap-4", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx(
        "input",
        {
          ref: fileInputRef,
          type: "file",
          accept: "image/*",
          onChange: handleFileSelect,
          className: "file:py-2 file:px-4 file:rounded-lg file:border-0 file:bg-blue-100 file:text-blue-700 hover:file:bg-blue-200 transition"
        }
      ),
      previewUrl && /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex flex-col items-center p-2 border border-gray-300 rounded-lg bg-white shadow-sm", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "text-md font-bold text-red-600", children: "Upload Image 1000*400 Size" }),
        /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "text-sm text-gray-600", children: "Image Preview" }),
        /* @__PURE__ */ jsxRuntimeExports.jsx("img", { src: previewUrl, alt: "Preview", className: "mt-2 rounded-md w-[250px] h-[100px] object-cover" })
      ] }),
      uploadError !== "" && /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "text-md font-bold text-red-600", children: uploadError }),
      /* @__PURE__ */ jsxRuntimeExports.jsx(
        Input,
        {
          type: "number",
          placeholder: "Enter Sequence",
          value: sequenceId,
          onChange: (e) => setSequenceId(e.target.value ? Number(e.target.value) : "")
        }
      )
    ] }),
    /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: " flex flex-row justify-center gap-5", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsxs(Button, { onClick: handleClose, variant: "outline", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx(X, { size: 16 }),
        " Cancel"
      ] }),
      /* @__PURE__ */ jsxRuntimeExports.jsxs(Button, { onClick: handleSave, disabled: !bannerUrl || sequenceId === "", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx(Save, { size: 16 }),
        " Save"
      ] })
    ] })
  ] }) });
}
const BANGALORE_CENTER = {
  lat: 12.9716,
  lng: 77.5946
};
const MAP_CONTAINER_STYLE = {
  width: "100%",
  height: "100%"
};
const getPolygonColor = (index) => {
  const colors = ["#3b82f6", "#10b981", "#f59e0b", "#ef4444", "#8b5cf6", "#ec4899", "#06b6d4", "#f97316"];
  return colors[index % colors.length];
};
function NetworkDetailsPage() {
  var _a, _b, _c, _d;
  const {
    googleMapsApiKey,
    networkId,
    networkName,
    activeTab,
    networkAreas,
    statesAndDistricts,
    networkConfig,
    networkBanners,
    networkAgents: networkAgents2,
    url,
    userId,
    roles,
    networkSeller,
    sellerList,
    permissions
  } = useLoaderData();
  const dataleng = networkAreas ? networkAreas.length : networkConfig;
  const fetchfor = useNavigate();
  const handleTabChange = (newTab) => {
    console.log(newTab, "333333333");
    fetchfor(`?networkId=${networkId}&networkName=${networkName}&activeTab=${newTab}`);
  };
  console.log(networkBanners, "09890989098989");
  const [visibleAreas, setVisibleAreas] = reactExports.useState(/* @__PURE__ */ new Set());
  const [isLoading, setIsLoading] = reactExports.useState(true);
  const [map, setMap] = reactExports.useState(null);
  const [mapLoaded, setMapLoaded] = reactExports.useState(false);
  const onLoad = reactExports.useCallback((mapInstance) => {
    setMap(mapInstance);
    setMapLoaded(true);
  }, []);
  let stateList = [];
  statesAndDistricts == null ? void 0 : statesAndDistricts.forEach((area) => stateList.push(area.state));
  const [latitude, setLatitude] = reactExports.useState(0);
  const [longitude, setLongitude] = reactExports.useState(0);
  const [showMarker, setShowMarker] = reactExports.useState(false);
  const [isLocateShopClicked, setisLocateShopClicked] = reactExports.useState(false);
  const [isAddLocalityClicked, setisAddLocalityClicked] = reactExports.useState(false);
  const [localityEditMode, setlocalityEditMode] = reactExports.useState(false);
  const [selectedState, setSelectedState] = reactExports.useState("");
  const [selectedDistrict, setSelectedDistrict] = reactExports.useState("");
  const [masterLocalities, setMasterLocalities] = reactExports.useState([]);
  const [pointerLocation, setPointerLocation] = reactExports.useState({
    latitude: null,
    longitude: null
  });
  const [infoWindowShown, setInfoWindowShown] = reactExports.useState({
    areaId: null,
    isShown: false,
    selectedAreaDetails: null
  });
  const [newLocalities, setNewLocalities] = reactExports.useState([]);
  const [newLocalities2, setNewLocalities2] = reactExports.useState([]);
  const handleFindLocation = reactExports.useCallback((lat, long, showMarker2) => {
    if (lat && long) {
      setPointerLocation({
        latitude: lat,
        longitude: long
      });
      setShowMarker(showMarker2);
    } else {
      alert("Please enter valid numeric values for latitude and longitude.");
    }
  }, []);
  const salesPermissions = permissions.includes("FmSalesManager");
  const handleNewLocalities = reactExports.useCallback((id, localityDetails, agentUserId) => {
    setNewLocalities((prevState) => {
      if (prevState.some((abc) => abc.id === id)) {
        return prevState.filter((abc) => abc.id !== id);
      } else {
        return [...prevState, localityDetails];
      }
    });
  }, []);
  const resetNewLocalities = reactExports.useCallback(() => {
    setNewLocalities([]);
    setNewLocalities2([]);
    setlocalityEditMode(false);
  }, []);
  const handleNewLocalities2 = reactExports.useCallback((id, localityDetails, networkId2, agentUserId) => {
    setNewLocalities2((prevState) => {
      const existingIndex = prevState.findIndex((loc) => loc.id === id);
      console.log("prevState.....", prevState);
      if (existingIndex !== -1) {
        console.log("id found... updating agentUserId ");
        return prevState.map((loc) => loc.id === id ? {
          ...loc,
          agentUserId: agentUserId ?? null
        } : loc);
      } else {
        console.log("id not found... adding it ");
        return [...prevState, {
          id,
          networkId: networkId2,
          // Update as per requirement
          agentUserId: agentUserId ?? null,
          localities: localityDetails
        }];
      }
    });
  }, [setNewLocalities2]);
  const handleUpdate = async (attribute, value, domainId) => {
    try {
      const formData = new FormData();
      formData.append("updateType", "network_domain");
      formData.append("domainId", domainId.toString());
      formData.append("attribute", attribute);
      formData.append("value", value.toString());
      console.log(value, "11111111111111111");
      console.log(attribute, "11111111111111111");
      await fetcher.submit(formData, {
        method: "POST"
      });
      showToast(`${attribute.replace(/([A-Z])/g, " $1")} updated successfully`, "success");
    } catch (error) {
      showToast(`Failed to update ${attribute.replace(/([A-Z])/g, " $1")}`, "error");
    }
  };
  const handleMarkerClick = reactExports.useCallback((id, areaDetails) => {
    setInfoWindowShown((prevState) => {
      if (prevState.isShown) {
        if (prevState.areaId === id) {
          return {
            areaId: null,
            isShown: false,
            selectedAreaDetails: null
          };
        } else {
          return {
            areaId: id,
            isShown: true,
            selectedAreaDetails: areaDetails
          };
        }
      } else {
        return {
          areaId: id,
          isShown: true,
          selectedAreaDetails: areaDetails
        };
      }
    });
  }, []);
  const {
    showToast
  } = useToast();
  const updateToggle = (sellerId) => {
    const formData = new FormData();
    formData.append("sellerId", sellerId);
    formData.append("netWorkId", networkId);
    formData.append("actionType", "updateNetWorkStatus");
    fetcher.submit(formData, {
      method: "POST"
    });
    if (fetcher.state === "idle" && fetcher.data !== void 0) {
      showToast("NetWorkStatus updated Successfully", "success");
    }
  };
  const handleLocateShopClicked = reactExports.useCallback((state) => {
    setisLocateShopClicked(state);
  }, []);
  const handleAddLocalityClicked = reactExports.useCallback((state) => {
    setisAddLocalityClicked(state);
  }, []);
  const submit = useSubmit();
  const handleSubmit = async (event) => {
    console.log("handle Submit Called......");
    event.preventDefault();
    console.log("🚀 handleSubmit Called!");
    const formData = new FormData();
    formData.append("actionType", "addLocalities");
    const transformed = newLocalities2.map((item) => ({
      networkId: item.networkId ?? 0,
      agentUserId: parseInt(item.agentUserId ?? "0"),
      // Use the item's agentUserId or default to 0 if null
      areas: [item.id ?? 0]
    }));
    formData.append("localities", JSON.stringify(transformed));
    console.log("📩 Form Data Before Submit:", Object.fromEntries(formData));
    submit(formData, {
      method: "post",
      encType: "multipart/form-data"
    });
    handleAddLocalityClicked(false);
    resetNewLocalities();
  };
  const handleMasterLocalitiesClicked = reactExports.useCallback(async (userId2, state, district) => {
    if (!state || !district) {
      alert("Please select a state and district to proceed...");
      return;
    }
    try {
      const response = await fetch(`./api/masterLocalities?userId=${userId2}&state=${state}&district=${district}`);
      const data = await response.json();
      if (!response.ok) {
        throw new Error(data.error || "Unknown error");
      }
      setMasterLocalities(data.masterLocalities.data);
    } catch (error) {
      console.error("Error fetching Master Localities:", error);
      alert("Fetching Master Localities failed. Please try again.");
    }
  }, []);
  reactExports.useEffect(() => {
    if (map && networkAreas) {
      const decodedPolygons = networkAreas.map((area) => ({
        id: area.networkAreaId,
        paths: area.encodedPolygon ? decodePolygon(area.encodedPolygon) : []
      }));
      setVisibleAreas(new Set(decodedPolygons.map((polygon) => polygon.id)));
    }
  }, [map, networkAreas]);
  reactExports.useEffect(() => {
    if (googleMapsApiKey && networkAreas) {
      if (mapLoaded) {
        setIsLoading(false);
      }
    }
  }, [googleMapsApiKey, networkAreas, mapLoaded]);
  const onUnmount = reactExports.useCallback(() => {
    setMap(null);
    setMapLoaded(false);
  }, []);
  const getPolygonCenter = (paths) => {
    const latitudes = paths.map((path) => path.lat);
    const longitudes = paths.map((path) => path.lng);
    const latSum = latitudes.reduce((a, b) => a + b, 0);
    const lngSum = longitudes.reduce((a, b) => a + b, 0);
    return {
      lat: latSum / latitudes.length,
      lng: lngSum / longitudes.length
    };
  };
  const NetworkHeaders = ["Id", "Name", "BusinessName", "Status"];
  const NetworkBannerHeaders = ["Id", "Banner Image", "Sequence", "Action"];
  const [isModalOpen, setIsModalOpen] = reactExports.useState(false);
  const [isModalBannerOpen, setIsModalBannerOpen] = reactExports.useState(false);
  const navigation = useNavigate();
  const NetWorkSeller = ["Seller Id", "Seller Name", "Actions"];
  const fetcher = useFetcher();
  const [toggledNetWork, setToggleNetwork] = reactExports.useState(() => networkSeller == null ? void 0 : networkSeller.reduce((acc, seller) => {
    acc[seller.networkSellerId] = seller.disabled;
    return acc;
  }, {}));
  const handleSwitch = async (networkSellerId) => {
    setToggleNetwork((prev) => ({
      ...prev,
      [networkSellerId]: !prev[networkSellerId]
    }));
    updateToggle(networkSellerId);
  };
  const handleSwitchBanner = (bannerId) => {
    const formData = new FormData();
    formData.append("bannerId", bannerId);
    formData.append("actionType", "updateBanner");
    formData.append("netWorkId", networkId.toString());
    fetcher.submit(formData, {
      method: "POST"
    });
  };
  const [agentStauts, setAgentStatus] = reactExports.useState({});
  const handleToggleStatus = (agentId, currentStatus) => {
    const newStatus = !currentStatus;
    setAgentStatus((prev) => ({
      ...prev,
      [agentId]: newStatus
    }));
    const formData = new FormData();
    formData.append("_intent", "updateAgentStatus");
    formData.append("agentUserId", agentId.toString());
    formData.append("networkId", networkId.toString());
    formData.append("status", newStatus.toString());
    fetcher.submit(formData, {
      method: "put"
    });
  };
  const [isnBannerUpdate, setIsBannerUpdate] = reactExports.useState({});
  const [nBannerSeq, setNBannerSeq] = reactExports.useState({});
  const [isnBannerUpdateurl, setIsBannerUpdateurl] = reactExports.useState({});
  const [nBannerurl, setNBannerurl] = reactExports.useState({});
  const [currentRowId, setCurrentRowId] = reactExports.useState(null);
  const uploadFetcher = useFetcher();
  const [previewUrls, setPreviewUrls] = reactExports.useState([]);
  const [uploadError, setUploadError] = reactExports.useState(null);
  const fileInputRef = reactExports.useRef(null);
  const handleUpdateSeq = (id, val) => {
    setNBannerSeq((prev) => ({
      ...prev,
      [id]: val
    }));
  };
  const handleSaveSeq = (seqId, row) => {
    const formData = new FormData();
    formData.append("actionType", "bannerSequence");
    formData.append("bannerData", JSON.stringify(row));
    formData.append("seQuenceId", nBannerSeq[seqId].toString());
    fetcher.submit(formData, {
      method: "PUT"
    });
    setIsBannerUpdate((prev) => ({
      ...prev,
      [seqId]: false
    }));
  };
  const handleSaveUrl = (seqId, row) => {
    const formData = new FormData();
    formData.append("actionType", "updateBannerUrl");
    formData.append("bannerData", JSON.stringify(row));
    formData.append("bannerUrl", nBannerurl[seqId].toString());
    fetcher.submit(formData, {
      method: "PUT"
    });
    setIsBannerUpdate((prev) => ({
      ...prev,
      [seqId]: false
    }));
  };
  reactExports.useEffect(() => {
    if (uploadFetcher.data && currentRowId !== null) {
      if (uploadFetcher.data.error) {
        setUploadError(uploadFetcher.data.error);
      } else if (uploadFetcher.data.fileUrl) {
        const uploadedUrl = uploadFetcher.data.fileUrl;
        setNBannerurl((prev) => ({
          ...prev,
          [currentRowId]: uploadedUrl
          // `!` asserts it's not null
        }));
        setPreviewUrls((prev) => ({
          ...prev,
          [currentRowId]: uploadedUrl
          // `!` asserts it's not null
        }));
        setUploadError(null);
        if (fileInputRef.current) fileInputRef.current.value = "";
      }
    }
  }, [uploadFetcher.data]);
  const handleFileSelect = async (event, rowId) => {
    setCurrentRowId(rowId);
    const files = Array.from(event.target.files || []);
    if (files.length === 0) return;
    setUploadError(null);
    const MAX_FILE_SIZE = 500 * 1024;
    const allowedTypes = ["image/jpeg", "image/png", "image/gif", "image/webp"];
    const validFile = files.find((file) => {
      if (file.size > MAX_FILE_SIZE) {
        setUploadError("File size exceeds 500kb limit.");
        return false;
      }
      if (!allowedTypes.includes(file.type)) {
        setUploadError("Only JPEG, PNG, GIF, and WEBP images are allowed.");
        return false;
      }
      return true;
    });
    if (!validFile) return;
    const uploadFormData = new FormData();
    uploadFormData.append("_action", "uploadImage");
    uploadFormData.append("file", validFile, validFile.name);
    uploadFetcher.submit(uploadFormData, {
      method: "post",
      action: "/home/<USER>",
      encType: "multipart/form-data"
    });
  };
  const loading = fetcher.state !== "idle";
  return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
    className: "h-full",
    children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("h1", {
      className: " mb-4 font-bold cursor-pointer",
      onClick: () => navigation("/home/<USER>"),
      children: [" ", /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
        className: "text-2xl",
        children: "Network Management / "
      }), " ", /* @__PURE__ */ jsxRuntimeExports.jsxs("span", {
        className: "text-xl",
        children: [networkName, " "]
      }), " "]
    }), /* @__PURE__ */ jsxRuntimeExports.jsxs(Tabs, {
      value: activeTab,
      onValueChange: handleTabChange,
      children: [loading && /* @__PURE__ */ jsxRuntimeExports.jsx(SpinnerLoader, {
        loading
      }), /* @__PURE__ */ jsxRuntimeExports.jsxs(TabsList, {
        children: [!salesPermissions && /* @__PURE__ */ jsxRuntimeExports.jsx(TabsTrigger, {
          value: "NetworkConfig",
          children: "Configurations"
        }), !salesPermissions && /* @__PURE__ */ jsxRuntimeExports.jsx(TabsTrigger, {
          value: "NetworkSeller",
          children: "Sellers"
        }), /* @__PURE__ */ jsxRuntimeExports.jsx(TabsTrigger, {
          value: "NetworkAreas",
          children: "Areas"
        }), /* @__PURE__ */ jsxRuntimeExports.jsx(TabsTrigger, {
          value: "NetworkAgents",
          children: "Agents"
        }), /* @__PURE__ */ jsxRuntimeExports.jsx(TabsTrigger, {
          value: "NetworkBanners",
          children: "NetworkBanners"
        })]
      }), /* @__PURE__ */ jsxRuntimeExports.jsxs(TabsContent, {
        value: "NetworkSeller",
        children: [/* @__PURE__ */ jsxRuntimeExports.jsx(ResponsiveTable, {
          headers: NetWorkSeller,
          data: networkSeller.filter((a) => !a.disabled),
          renderRow: (row) => {
            return /* @__PURE__ */ jsxRuntimeExports.jsxs("tr", {
              className: "border-b",
              children: [/* @__PURE__ */ jsxRuntimeExports.jsx("td", {
                className: "py-2 px-3 text-center  whitespace-normal break-words",
                children: row.sellerId
              }), /* @__PURE__ */ jsxRuntimeExports.jsx("td", {
                className: "py-2 px-3 text-center  whitespace-normal break-words",
                children: (row == null ? void 0 : row.seller) || "-"
              }), /* @__PURE__ */ jsxRuntimeExports.jsx("td", {
                className: "py-2 px-3 text-center  whitespace-normal break-words",
                children: /* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
                  variant: "ghost",
                  size: "sm",
                  className: "text-red-500 hover:text-red-900",
                  onClick: () => handleSwitch(row == null ? void 0 : row.networkSellerId),
                  children: /* @__PURE__ */ jsxRuntimeExports.jsx(Trash2, {
                    size: 16
                  })
                })
              })]
            }, row.sellerId);
          }
        }), /* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
          className: "fixed bottom-5 right-5 rounded-full",
          onClick: () => setIsModalOpen(true),
          children: "+ Add Seller"
        }), /* @__PURE__ */ jsxRuntimeExports.jsx(NetWorkModal, {
          isOpen: isModalOpen,
          onClose: () => setIsModalOpen(false),
          sellerList: sellerList ? sellerList.filter((a) => !networkSeller.filter((a2) => !a2.disabled).some((ns) => ns.sellerId === a.id)) : [],
          netWorkId: networkId
        })]
      }), /* @__PURE__ */ jsxRuntimeExports.jsxs(TabsContent, {
        value: "NetworkBanners",
        children: [/* @__PURE__ */ jsxRuntimeExports.jsx(ResponsiveTable, {
          headers: NetworkBannerHeaders,
          data: networkBanners || [],
          renderRow: (row) => /* @__PURE__ */ jsxRuntimeExports.jsxs("tr", {
            className: "border-b",
            children: [/* @__PURE__ */ jsxRuntimeExports.jsx("td", {
              className: "py-2 px-3 text-center  whitespace-normal break-words",
              children: (row == null ? void 0 : row.id) || "-"
            }), /* @__PURE__ */ jsxRuntimeExports.jsx("td", {
              className: "py-2 px-4 text-center whitespace-normal break-words",
              children: /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
                className: "flex flex-col items-center gap-3",
                children: isnBannerUpdateurl[row.id] ? /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                  className: "flex flex-col items-center gap-3 p-4 bg-gray-100 rounded-lg shadow-md ",
                  children: [/* @__PURE__ */ jsxRuntimeExports.jsx("input", {
                    ref: fileInputRef,
                    type: "file",
                    accept: "image/*",
                    onChange: (e) => handleFileSelect(e, row.id),
                    className: "file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm \r\n                     file:font-semibold file:bg-blue-100 file:text-blue-700 \r\n                     hover:file:bg-blue-200 transition duration-200"
                  }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                    className: "flex flex-row gap-2",
                    children: [previewUrls[row.id] && /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                      className: "flex flex-col items-center p-2 border border-gray-300 rounded-lg bg-white shadow-sm",
                      children: [/* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                        className: "text-md font-bold text-red-600",
                        children: "Upload Image 1000*400 Size"
                      }), /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                        className: "text-sm text-gray-600",
                        children: "Image Preview"
                      }), /* @__PURE__ */ jsxRuntimeExports.jsx("img", {
                        src: previewUrls[row.id],
                        alt: "Preview",
                        className: "mt-2 rounded-md w-[250px] h-[100px] object-cover"
                      })]
                    }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                      className: "flex  flex-row items-center gap-3",
                      children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Save, {
                        size: 24,
                        className: "cursor-pointer text-green-600 hover:text-green-700 transition duration-200",
                        onClick: () => handleSaveUrl(row.id, row)
                      }), /* @__PURE__ */ jsxRuntimeExports.jsx(X, {
                        size: 24,
                        className: "cursor-pointer text-red-500 hover:text-red-600 transition duration-200",
                        onClick: () => {
                          setIsBannerUpdateurl((prev) => ({
                            ...prev,
                            [row.id]: false
                          }));
                          setPreviewUrls((prev) => ({
                            ...prev,
                            [row.id]: row.bannerUrl
                          }));
                          setNBannerurl((prev) => ({
                            ...prev,
                            [row.id]: row.bannerUrl
                          }));
                          if (fileInputRef.current) fileInputRef.current.value = "";
                        }
                      })]
                    })]
                  })]
                }) : /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                  className: "flex flex-row items-center gap-2",
                  children: [/* @__PURE__ */ jsxRuntimeExports.jsx("img", {
                    alt: "img",
                    src: nBannerurl[row.id] || row.bannerUrl,
                    className: "rounded-lg shadow-md w-[250px] h-[100px] object-cover"
                  }), /* @__PURE__ */ jsxRuntimeExports.jsx(Pencil, {
                    className: "cursor-pointer text-blue-600 hover:text-blue-700 transition duration-200",
                    onClick: () => setIsBannerUpdateurl({
                      [row.id]: true
                    })
                  })]
                })
              })
            }), /* @__PURE__ */ jsxRuntimeExports.jsx("td", {
              className: "py-2 px-3 text-center  whitespace-normal break-words max-w-20",
              children: /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
                className: "flex flex-row items-center justify-center gap-2",
                children: isnBannerUpdate[row.id] ? /* @__PURE__ */ jsxRuntimeExports.jsxs(jsxRuntimeExports.Fragment, {
                  children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Input, {
                    type: "number",
                    value: nBannerSeq[row.id] ?? row.sequenceId,
                    onChange: (e) => handleUpdateSeq(row.id, e.target.value),
                    className: " px-2 py-1 border border-gray-300 rounded-md"
                  }), /* @__PURE__ */ jsxRuntimeExports.jsx(Save, {
                    size: 24,
                    onClick: () => handleSaveSeq(row.id, row),
                    className: "cursor-pointer"
                  }), /* @__PURE__ */ jsxRuntimeExports.jsx(X, {
                    color: "red",
                    size: 24,
                    className: "cursor-pointer text-red-500",
                    onClick: () => setIsBannerUpdate({})
                  })]
                }) : /* @__PURE__ */ jsxRuntimeExports.jsxs(jsxRuntimeExports.Fragment, {
                  children: [(row == null ? void 0 : row.sequenceId) || "-", /* @__PURE__ */ jsxRuntimeExports.jsx(Pencil, {
                    size: 20,
                    className: "cursor-pointer self-center",
                    onClick: () => setIsBannerUpdate({
                      [row.id]: true
                    })
                  }), row.active == false && /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                    className: "bg-orange-100 p-1 rounded-full px-2  text-red-600 font-bold",
                    children: "disabled"
                  })]
                })
              })
            }), /* @__PURE__ */ jsxRuntimeExports.jsx("td", {
              className: "py-2 px-3 text-center  whitespace-normal break-words",
              children: /* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
                variant: "ghost",
                size: "sm",
                className: "text-red-500 hover:text-red-900",
                onClick: () => {
                  if (confirm("Are you sure you want to delete this Network Banner?")) {
                    handleSwitchBanner(row == null ? void 0 : row.id);
                  }
                },
                style: {
                  alignSelf: "flex-end"
                },
                children: /* @__PURE__ */ jsxRuntimeExports.jsx(Trash2, {
                  size: 16
                })
              })
            })]
          }, row.id)
        }), !salesPermissions && /* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
          className: "fixed bottom-5 right-5 rounded-full",
          onClick: () => setIsModalBannerOpen(true),
          children: "+ Add Banner"
        }), /* @__PURE__ */ jsxRuntimeExports.jsx(CreateNetworkBannerModal, {
          isOpen: isModalBannerOpen,
          onClose: () => setIsModalBannerOpen(false),
          networkId
        })]
      }), /* @__PURE__ */ jsxRuntimeExports.jsxs(TabsContent, {
        value: "NetworkAgents",
        children: [/* @__PURE__ */ jsxRuntimeExports.jsx(ResponsiveTable, {
          headers: NetworkHeaders,
          data: networkAgents2 || [],
          renderRow: (row) => /* @__PURE__ */ jsxRuntimeExports.jsxs("tr", {
            className: "border-b",
            children: [/* @__PURE__ */ jsxRuntimeExports.jsx("td", {
              className: "py-2 px-3 text-center  whitespace-normal break-words",
              children: row.agentUserId
            }), /* @__PURE__ */ jsxRuntimeExports.jsx("td", {
              className: "py-2 px-3 text-center  whitespace-normal break-words",
              children: (row == null ? void 0 : row.fullName) || "-"
            }), /* @__PURE__ */ jsxRuntimeExports.jsx("td", {
              className: "py-2 px-3 text-center  whitespace-normal break-words",
              children: (row == null ? void 0 : row.businessName) || "-"
            }), /* @__PURE__ */ jsxRuntimeExports.jsx("td", {
              className: "py-2 px-3 text-center",
              children: /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
                className: "flex items-center justify-center space-x-2",
                children: /* @__PURE__ */ jsxRuntimeExports.jsx(Switch, {
                  checked: !(agentStauts[row.agentUserId] || row.status),
                  onCheckedChange: () => handleToggleStatus(row.agentUserId, agentStauts[row.agentUserId])
                })
              })
            })]
          }, row.agentUserId)
        }), !salesPermissions && /* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
          className: "fixed bottom-5 right-5 rounded-full",
          onClick: () => setIsModalOpen(true),
          children: "+ Add Agent"
        }), /* @__PURE__ */ jsxRuntimeExports.jsx(CreateAgent, {
          isOpen: isModalOpen,
          onClose: () => setIsModalOpen(false),
          NetWorkId: networkId,
          sellerList,
          roles
        })]
      })]
    }), activeTab === "NetworkConfig" && (networkConfig ? /* @__PURE__ */ jsxRuntimeExports.jsx(NetWorkConfig, {
      networkConfig,
      networkName,
      onAttributeUpdate: handleUpdate
    }) : /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
      className: "flex items-center justify-center h-full",
      children: /* @__PURE__ */ jsxRuntimeExports.jsx("p", {
        className: "text-red-500",
        children: "Unable to find network configurations."
      })
    })), activeTab === "NetworkAreas" && /* @__PURE__ */ jsxRuntimeExports.jsx(LoadScript, {
      googleMapsApiKey,
      children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
        className: "flex w-full h-[87vh] border rounded-xl border-neutral-200 my-4",
        children: [/* @__PURE__ */ jsxRuntimeExports.jsx("div", {
          className: "flex flex-col gap-4 w-[20vw] overflow-auto bg-white shadow rounded-xl p-3",
          children: dataleng && networkAreas ? networkAreas == null ? void 0 : networkAreas.map((network, index) => /* @__PURE__ */ jsxRuntimeExports.jsx(NetworkAreaCard, {
            networkAreaDetail: network,
            agentDetails: networkAgents2,
            networkId,
            color: getPolygonColor(index),
            netWorks: networkAreas,
            isSalesPermission: salesPermissions
          })) : /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
            className: "h-24 text-center",
            children: "No Network Areas found."
          })
        }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
          className: "relative flex-1 h-full bg-white",
          children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
            className: "absolute flex z-10 top-0 left-0 w-full justify-between p-3 max-h-full",
            children: [!salesPermissions && !isAddLocalityClicked ? /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
              className: "p-3 h-fit bg-white rounded-xl flex gap-2 text-blue-600 items-center shadow",
              children: /* @__PURE__ */ jsxRuntimeExports.jsx("button", {
                onClick: () => {
                  handleAddLocalityClicked(true);
                  resetNewLocalities();
                },
                children: "+   Add New Locality"
              })
            }) : !salesPermissions && /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
              className: "p-3 bg-white rounded-xl flex flex-col gap-2  items-center shadow ",
              children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                className: "flex w-full justify-between",
                children: ["Add Localities", /* @__PURE__ */ jsxRuntimeExports.jsx("button", {
                  onClick: () => {
                    handleAddLocalityClicked(false);
                    resetNewLocalities();
                  },
                  children: /* @__PURE__ */ jsxRuntimeExports.jsx(X, {
                    size: 16
                  })
                })]
              }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                className: "flex w-full p-1 items-center gap-3",
                children: [/* @__PURE__ */ jsxRuntimeExports.jsxs(Select, {
                  value: selectedState,
                  onValueChange: setSelectedState,
                  children: [/* @__PURE__ */ jsxRuntimeExports.jsx(SelectTrigger, {
                    className: "w-[180px]",
                    children: /* @__PURE__ */ jsxRuntimeExports.jsx(SelectValue, {
                      placeholder: "Select state"
                    })
                  }), /* @__PURE__ */ jsxRuntimeExports.jsx(SelectContent, {
                    children: stateList.map((state) => /* @__PURE__ */ jsxRuntimeExports.jsx(SelectItem, {
                      value: state,
                      children: state
                    }, state))
                  })]
                }), /* @__PURE__ */ jsxRuntimeExports.jsxs(Select, {
                  value: selectedDistrict,
                  onValueChange: (newDistrict) => {
                    setSelectedDistrict(newDistrict);
                    handleMasterLocalitiesClicked(userId, selectedState, newDistrict);
                    resetNewLocalities();
                  },
                  disabled: !selectedState,
                  children: [/* @__PURE__ */ jsxRuntimeExports.jsx(SelectTrigger, {
                    className: "w-[180px]",
                    children: /* @__PURE__ */ jsxRuntimeExports.jsx(SelectValue, {
                      placeholder: "Select District"
                    })
                  }), /* @__PURE__ */ jsxRuntimeExports.jsx(SelectContent, {
                    children: statesAndDistricts == null ? void 0 : statesAndDistricts.filter((abc) => abc.state === selectedState).map((district) => /* @__PURE__ */ jsxRuntimeExports.jsx(SelectItem, {
                      value: district.district,
                      children: district.district
                    }, district.district))
                  })]
                })]
              }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                className: "flex flex-col gap-2 self-start max-h-full w-full overflow-auto p-2 ",
                children: [masterLocalities && masterLocalities.length > 0 ? (masterLocalities.filter((a) => networkAreas == null ? void 0 : networkAreas.some((na) => na.areaId != a.id)).length > 0 ? masterLocalities.filter((a) => !(networkAreas == null ? void 0 : networkAreas.some((na) => na.areaId === a.id))) : masterLocalities).map((locality, index) => {
                  var _a2;
                  return /* @__PURE__ */ jsxRuntimeExports.jsxs(jsxRuntimeExports.Fragment, {
                    children: [/* @__PURE__ */ jsxRuntimeExports.jsx("div", {
                      children: /* @__PURE__ */ jsxRuntimeExports.jsxs("label", {
                        className: "cursor-pointer flex items-center gap-2 p-1",
                        htmlFor: `locality-${locality.id}`,
                        children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Checkbox, {
                          id: `locality-${locality.id}`,
                          checked: newLocalities.some((abc) => abc.id === locality.id),
                          onClick: () => handleNewLocalities(locality.id, locality)
                        }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                          className: "flex flex-col gap-2",
                          children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("span", {
                            children: [locality.id, " - ", locality.name]
                          }), newLocalities.some((abc) => abc.id === locality.id) && /* @__PURE__ */ jsxRuntimeExports.jsxs(Select, {
                            value: ((_a2 = newLocalities2.find((loc) => loc.id === locality.id)) == null ? void 0 : _a2.agentUserId) || "",
                            onValueChange: (newDistrict) => {
                              handleNewLocalities2(locality.id, locality, networkId, newDistrict);
                            },
                            disabled: !selectedState,
                            children: [/* @__PURE__ */ jsxRuntimeExports.jsx(SelectTrigger, {
                              className: "w-[280px]",
                              children: /* @__PURE__ */ jsxRuntimeExports.jsx(SelectValue, {
                                placeholder: "Select sales agent"
                              })
                            }), /* @__PURE__ */ jsxRuntimeExports.jsx(SelectContent, {
                              children: networkAgents2 == null ? void 0 : networkAgents2.map((district) => /* @__PURE__ */ jsxRuntimeExports.jsxs(SelectItem, {
                                value: district.agentUserId.toString(),
                                children: [district.agentUserId, " - ", district.fullName]
                              }, district.agentUserId))
                            })]
                          })]
                        })]
                      })
                    }, locality.id), index < masterLocalities.length - 1 && /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
                      className: "border-b border-neutral-200"
                    }), " "]
                  });
                }) : /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
                  children: "No Localities Fetched yet"
                }), " "]
              }), /* @__PURE__ */ jsxRuntimeExports.jsxs("form", {
                method: "post",
                encType: "multipart/form-data",
                onSubmit: handleSubmit,
                children: [/* @__PURE__ */ jsxRuntimeExports.jsx("input", {
                  type: "hidden",
                  name: "localities",
                  value: JSON.stringify(newLocalities2)
                }), /* @__PURE__ */ jsxRuntimeExports.jsx("button", {
                  type: "submit",
                  disabled: newLocalities2.length === 0,
                  className: "px-4 py-2 bg-primary text-white rounded disabled:bg-neutral-500",
                  children: "Submit Localities"
                })]
              })]
            }), !isLocateShopClicked ? /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
              className: "p-3 bg-white  h-fit rounded-xl flex gap-2 text-blue-600 items-center shadow",
              children: /* @__PURE__ */ jsxRuntimeExports.jsx("button", {
                onClick: () => handleLocateShopClicked(true),
                children: "📍   Locate a shop"
              })
            }) : /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
              className: "p-3 bg-white rounded-xl flex flex-col gap-2  items-center shadow",
              children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                className: "flex w-full justify-between",
                children: ["Locate a shop", /* @__PURE__ */ jsxRuntimeExports.jsx("button", {
                  onClick: () => handleLocateShopClicked(false),
                  children: /* @__PURE__ */ jsxRuntimeExports.jsx(X, {
                    size: 16
                  })
                })]
              }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                className: "flex w-full p-1 items-center justify-between",
                children: [/* @__PURE__ */ jsxRuntimeExports.jsx("p", {
                  children: "Latitude : "
                }), /* @__PURE__ */ jsxRuntimeExports.jsx("input", {
                  type: "text",
                  className: "border border-neutral-400 rounded-md p-1",
                  onChange: (e) => setLatitude(parseFloat(e.target.value))
                })]
              }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                className: "flex gap-1 p-1 items-center",
                children: [/* @__PURE__ */ jsxRuntimeExports.jsx("p", {
                  children: "Longitute : "
                }), /* @__PURE__ */ jsxRuntimeExports.jsx("input", {
                  type: "text",
                  className: "border border-neutral-400 rounded-md p-1",
                  onChange: (e) => setLongitude(parseFloat(e.target.value))
                })]
              }), /* @__PURE__ */ jsxRuntimeExports.jsx("button", {
                className: "text-primary border border-primary p-2 rounded-md",
                onClick: () => handleFindLocation(latitude, longitude, true),
                children: "📍   Find Location"
              })]
            })]
          }), !mapLoaded && /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
            className: "flex items-center justify-center h-full",
            children: /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
              className: "loader",
              children: "Loading Map..."
            })
          }), googleMapsApiKey ? /* @__PURE__ */ jsxRuntimeExports.jsxs(GoogleMap, {
            mapContainerStyle: MAP_CONTAINER_STYLE,
            center: BANGALORE_CENTER,
            zoom: 11,
            onLoad: (mapInstance) => {
              onLoad(mapInstance);
              setMapLoaded(true);
            },
            onUnmount: () => {
              onUnmount();
              setMapLoaded(false);
            },
            options: {
              styles: [{
                featureType: "all",
                elementType: "geometry.fill",
                stylers: [{
                  visibility: "on"
                }]
              }],
              mapTypeControl: false,
              streetViewControl: false,
              fullscreenControl: false,
              clickableIcons: false,
              // Disable default map icons (e.g., POIs)
              gestureHandling: "auto"
              // Allow zoom and scroll interactions
            },
            children: [mapLoaded ? (_a = networkAreas == null ? void 0 : networkAreas.filter((abc) => visibleAreas.has(abc.networkAreaId))) == null ? void 0 : _a.map((area, index) => area.encodedPolygon && /* @__PURE__ */ jsxRuntimeExports.jsx(Polygon, {
              paths: decodePolygon(area.encodedPolygon),
              options: {
                fillColor: isAddLocalityClicked ? "#3b82f6" : getPolygonColor(index),
                fillOpacity: 0.2,
                strokeColor: isAddLocalityClicked ? "#3b82f6" : getPolygonColor(index),
                strokeOpacity: 1,
                strokeWeight: 2,
                draggable: false,
                editable: false,
                geodesic: false,
                zIndex: 1,
                clickable: true
                // Ensure polygons remain clickable
              },
              onClick: () => handleMarkerClick(area.networkAreaId, area)
            }, area.networkAreaId)) : null, mapLoaded ? newLocalities == null ? void 0 : newLocalities.map((area, index) => area.polygon && /* @__PURE__ */ jsxRuntimeExports.jsx(Polygon, {
              paths: decodePolygon(area.polygon),
              options: {
                fillColor: "#10b981",
                fillOpacity: 0.4,
                strokeColor: "#10b981",
                strokeOpacity: 1,
                strokeWeight: 2,
                draggable: false,
                editable: false,
                geodesic: false,
                zIndex: 10,
                clickable: false
                // Ensure polygons remain clickable
              }
            }, area.id)) : null, infoWindowShown && infoWindowShown.areaId && infoWindowShown.isShown && infoWindowShown.selectedAreaDetails && /* @__PURE__ */ jsxRuntimeExports.jsxs(jsxRuntimeExports.Fragment, {
              children: [console.log("entered infowindow params with ##########", infoWindowShown), /* @__PURE__ */ jsxRuntimeExports.jsx(InfoWindow, {
                position: getPolygonCenter(decodePolygon(infoWindowShown.selectedAreaDetails.encodedPolygon)),
                onCloseClick: () => setInfoWindowShown({
                  isShown: false,
                  areaId: null,
                  selectedAreaDetails: null
                }),
                options: {
                  headerDisabled: true,
                  minWidth: 200,
                  disableAutoPan: true
                },
                children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                  className: "flex flex-col gap-2 overflow-hidden ",
                  children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                    className: "flex justify-between w-full align-middle items-center",
                    children: [/* @__PURE__ */ jsxRuntimeExports.jsx("h2", {
                      className: "text-md font-semibold text-typography-300",
                      children: "Locality Info"
                    }), /* @__PURE__ */ jsxRuntimeExports.jsx("button", {
                      className: "inline-flex items-center gap-1 hover:text-blue-800",
                      onClick: () => setInfoWindowShown({
                        isShown: false,
                        areaId: null,
                        selectedAreaDetails: null
                      }),
                      children: /* @__PURE__ */ jsxRuntimeExports.jsx(X, {
                        className: "h-5 w-5"
                      })
                    })]
                  }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                    className: "flex flex-col gap-1 text-sm font-medium text-typography-700 w-[calc(100%-1rem)]",
                    children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("p", {
                      children: [/* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                        className: "text-sm text-typography-300 font-thin",
                        children: "Area id:"
                      }), " ", (_b = infoWindowShown.selectedAreaDetails) == null ? void 0 : _b.networkAreaId]
                    }), /* @__PURE__ */ jsxRuntimeExports.jsxs("p", {
                      children: [/* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                        className: "text-sm text-typography-300 font-thin",
                        children: "Area Name: "
                      }), " ", (_c = infoWindowShown.selectedAreaDetails) == null ? void 0 : _c.networkAreaName]
                    }), /* @__PURE__ */ jsxRuntimeExports.jsxs("p", {
                      children: [/* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                        className: "text-sm text-typography-300 font-thin",
                        children: "Sales Agent:"
                      }), " ", (_d = infoWindowShown.selectedAreaDetails) == null ? void 0 : _d.agentName]
                    })]
                  })]
                })
              }), /* @__PURE__ */ jsxRuntimeExports.jsx("style", {
                children: `.info-window-wrapper {
                                  padding: 10px; 
                                }
                              
                                .gm-style-iw { 
                                  padding: 12px !important; 
                                }
                                .gm-style-iw-d { 
                                  padding: 0px !important; 
                                  overflow:hidden !important;
                                }`
              }), " "]
            }), showMarker && pointerLocation && pointerLocation.latitude !== null && pointerLocation.longitude !== null && /* @__PURE__ */ jsxRuntimeExports.jsx(jsxRuntimeExports.Fragment, {
              children: /* @__PURE__ */ jsxRuntimeExports.jsx(Marker, {
                position: {
                  "lat": pointerLocation.latitude,
                  "lng": pointerLocation.longitude
                }
              })
            })]
          }) : /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
            className: "flex items-center justify-center h-full",
            children: /* @__PURE__ */ jsxRuntimeExports.jsx("p", {
              className: "text-red-500",
              children: "Google Maps API key is missing."
            })
          })]
        })]
      })
    })]
  });
}
export {
  NetworkDetailsPage as default
};
//# sourceMappingURL=home.networkDetails-BmwHh8ux.js.map
