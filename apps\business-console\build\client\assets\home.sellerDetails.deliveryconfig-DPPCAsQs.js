import { r as reactExports, j as jsxRuntimeExports } from "./jsx-runtime-bB2y7OuD.js";
import { C as ConfigType } from "./DeliveryConfig-Dk5cNfWo.js";
import { D as Dialog, a as DialogContent, c as <PERSON><PERSON>Header, b as <PERSON><PERSON><PERSON>it<PERSON>, e as <PERSON>alogFooter, g as DialogClose } from "./dialog-BqKosxNq.js";
import { I as Input } from "./input-3v87qohQ.js";
import { S as Select, a as SelectTrigger, b as SelectValue, c as SelectContent, d as SelectItem } from "./select-BFGSXKcr.js";
import { S as Switch } from "./switch-DQ_qW6ch.js";
import { B as Button } from "./button-ByAXMyvk.js";
import { S as SpinnerLoader } from "./SpinnerLoader-CAZRmIJX.js";
import { u as useLoaderData, a as useFetcher } from "./components-D7UvGag_.js";
import { c as createLucideIcon } from "./createLucideIcon-uwkRm45G.js";
import { F as Filter } from "./filter-QRp_VNWc.js";
import { P as Pen } from "./pen-NKWZhTNQ.js";
import "./index-DdafHWkt.js";
import "./index-D7VH9Fc8.js";
import "./index-z_byfFrQ.js";
import "./index-DVTNuYOr.js";
import "./index-dIGjFc0I.js";
import "./index-BXzPK7u0.js";
import "./index-CzZ6EYkN.js";
import "./index-Vp2vNLNM.js";
import "./index-CEHS9zzk.js";
import "./index-QLGF6kQx.js";
import "./utils-GkgzjW3c.js";
import "./x-CCG_WJDF.js";
import "./index-IXOTxK3N.js";
import "./index-qCtcpOcW.js";
import "./index-BTdCMChR.js";
import "./index-DscYByPT.js";
import "./index-CkL5tk39.js";
import "./index-CpKiYcZd.js";
import "./index-C88PRvfd.js";
import "./chevron-down-pCP5jmjX.js";
import "./check-_dbWxIzT.js";
import "./index-ImHKLo0a.js";
import "./index-DhHTcibu.js";
/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */
const BadgeCheck = createLucideIcon("BadgeCheck", [
  [
    "path",
    {
      d: "M3.85 8.62a4 4 0 0 1 4.78-4.77 4 4 0 0 1 6.74 0 4 4 0 0 1 4.78 4.78 4 4 0 0 1 0 6.74 4 4 0 0 1-4.77 4.78 4 4 0 0 1-6.75 0 4 4 0 0 1-4.78-4.77 4 4 0 0 1 0-6.76Z",
      key: "3c2336"
    }
  ],
  ["path", { d: "m9 12 2 2 4-4", key: "dzmm74" }]
]);
/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */
const BadgeX = createLucideIcon("BadgeX", [
  [
    "path",
    {
      d: "M3.85 8.62a4 4 0 0 1 4.78-4.77 4 4 0 0 1 6.74 0 4 4 0 0 1 4.78 4.78 4 4 0 0 1 0 6.74 4 4 0 0 1-4.77 4.78 4 4 0 0 1-6.75 0 4 4 0 0 1-4.78-4.77 4 4 0 0 1 0-6.76Z",
      key: "3c2336"
    }
  ],
  ["line", { x1: "15", x2: "9", y1: "9", y2: "15", key: "f7djnv" }],
  ["line", { x1: "9", x2: "15", y1: "9", y2: "15", key: "1shsy8" }]
]);
/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */
const CirclePercent = createLucideIcon("CirclePercent", [
  ["circle", { cx: "12", cy: "12", r: "10", key: "1mglay" }],
  ["path", { d: "m15 9-6 6", key: "1uzhvr" }],
  ["path", { d: "M9 9h.01", key: "1q5me6" }],
  ["path", { d: "M15 15h.01", key: "lqbp3k" }]
]);
const emptyConfig = {
  sellerId: 0,
  configType: ConfigType.PERCENTAGE_BASED,
  buyerPercentage: 0,
  sellerPercentage: 0,
  minOrderValue: 0,
  maxOrderValue: 0,
  maxBuyerDeliveryCharge: 0,
  maxSellerDeliveryCharge: 0,
  active: true
};
function CreateDeliveryConfig({ isOpen, onSave, onClose, initialConfig }) {
  const [form, setForm] = reactExports.useState(initialConfig ?? emptyConfig);
  reactExports.useEffect(() => {
    if (isOpen) {
      setForm(initialConfig ?? emptyConfig);
    }
  }, [isOpen, initialConfig]);
  const handleChange = (field) => (e) => {
    const value = e.target.type === "number" ? Number(e.target.value) : e.target.value;
    setForm((prev) => ({
      ...prev,
      [field]: value
    }));
  };
  const handleClose = () => {
    setForm(initialConfig ?? emptyConfig);
    onClose();
  };
  const handleSave = () => {
    onSave(form);
    handleClose();
  };
  const isPercentage = form.configType === ConfigType.PERCENTAGE_BASED;
  const isOrderBased = form.configType === ConfigType.ORDER_VALUE_BASED;
  return /* @__PURE__ */ jsxRuntimeExports.jsx(Dialog, { open: isOpen, onOpenChange: handleClose, children: /* @__PURE__ */ jsxRuntimeExports.jsxs(DialogContent, { className: "max-w-xl", children: [
    /* @__PURE__ */ jsxRuntimeExports.jsx(DialogHeader, { children: /* @__PURE__ */ jsxRuntimeExports.jsx(DialogTitle, { children: "Create Delivery Configuration" }) }),
    /* @__PURE__ */ jsxRuntimeExports.jsxs(
      "form",
      {
        onSubmit: (e) => {
          e.preventDefault();
          handleSave();
        },
        children: [
          /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "grid grid-cols-1 md:grid-cols-2 gap-4", children: [
            /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex flex-col", children: [
              /* @__PURE__ */ jsxRuntimeExports.jsx("label", { className: "text-xs font-medium mb-1", children: "Config Type" }),
              /* @__PURE__ */ jsxRuntimeExports.jsxs(
                Select,
                {
                  value: form.configType,
                  onValueChange: (v) => setForm((p) => ({ ...p, configType: v })),
                  children: [
                    /* @__PURE__ */ jsxRuntimeExports.jsx(SelectTrigger, { children: /* @__PURE__ */ jsxRuntimeExports.jsx(SelectValue, { placeholder: "Config type" }) }),
                    /* @__PURE__ */ jsxRuntimeExports.jsx(SelectContent, { children: Object.values(ConfigType).map((ct) => /* @__PURE__ */ jsxRuntimeExports.jsx(SelectItem, { value: ct, children: ct }, ct)) })
                  ]
                }
              )
            ] }),
            /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex flex-col", children: [
              /* @__PURE__ */ jsxRuntimeExports.jsx("label", { className: "text-xs font-medium mb-1", children: "Buyer Percentage (%)" }),
              /* @__PURE__ */ jsxRuntimeExports.jsx(
                Input,
                {
                  type: "number",
                  value: form.buyerPercentage,
                  onChange: handleChange("buyerPercentage"),
                  min: 0,
                  max: 100,
                  required: true
                }
              )
            ] }),
            /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex flex-col", children: [
              /* @__PURE__ */ jsxRuntimeExports.jsx("label", { className: "text-xs font-medium mb-1", children: "Seller Percentage (%)" }),
              /* @__PURE__ */ jsxRuntimeExports.jsx(
                Input,
                {
                  type: "number",
                  value: form.sellerPercentage,
                  onChange: handleChange("sellerPercentage"),
                  min: 0,
                  max: 100,
                  required: true
                }
              )
            ] }),
            isOrderBased && /* @__PURE__ */ jsxRuntimeExports.jsxs(jsxRuntimeExports.Fragment, { children: [
              /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex flex-col", children: [
                /* @__PURE__ */ jsxRuntimeExports.jsx("label", { className: "text-xs font-medium mb-1", children: "Min Order Value" }),
                /* @__PURE__ */ jsxRuntimeExports.jsx(
                  Input,
                  {
                    type: "number",
                    value: form.minOrderValue,
                    onChange: handleChange("minOrderValue"),
                    min: 0
                  }
                )
              ] }),
              /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex flex-col", children: [
                /* @__PURE__ */ jsxRuntimeExports.jsx("label", { className: "text-xs font-medium mb-1", children: "Max Order Value" }),
                /* @__PURE__ */ jsxRuntimeExports.jsx(
                  Input,
                  {
                    type: "number",
                    value: form.maxOrderValue,
                    onChange: handleChange("maxOrderValue"),
                    min: 0
                  }
                )
              ] })
            ] }),
            isPercentage && /* @__PURE__ */ jsxRuntimeExports.jsxs(jsxRuntimeExports.Fragment, { children: [
              /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex flex-col", children: [
                /* @__PURE__ */ jsxRuntimeExports.jsx("label", { className: "text-xs font-medium mb-1", children: "Max Buyer Delivery Charge" }),
                /* @__PURE__ */ jsxRuntimeExports.jsx(
                  Input,
                  {
                    type: "number",
                    value: form.maxBuyerDeliveryCharge,
                    onChange: handleChange("maxBuyerDeliveryCharge"),
                    min: 0
                  }
                )
              ] }),
              /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex flex-col", children: [
                /* @__PURE__ */ jsxRuntimeExports.jsx("label", { className: "text-xs font-medium mb-1", children: "Max Seller Delivery Charge" }),
                /* @__PURE__ */ jsxRuntimeExports.jsx(
                  Input,
                  {
                    type: "number",
                    value: form.maxSellerDeliveryCharge,
                    onChange: handleChange("maxSellerDeliveryCharge"),
                    min: 0
                  }
                )
              ] })
            ] })
          ] }),
          /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center gap-2 mt-6", children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx(
              Switch,
              {
                id: "active",
                checked: form.active,
                onCheckedChange: (checked) => setForm((p) => ({ ...p, active: checked }))
              }
            ),
            /* @__PURE__ */ jsxRuntimeExports.jsx("label", { htmlFor: "active", className: "text-sm font-medium", children: "Active" })
          ] }),
          /* @__PURE__ */ jsxRuntimeExports.jsxs(DialogFooter, { className: "mt-6", children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx(DialogClose, { asChild: true, children: /* @__PURE__ */ jsxRuntimeExports.jsx(Button, { variant: "secondary", type: "button", onClick: handleClose, children: "Cancel" }) }),
            /* @__PURE__ */ jsxRuntimeExports.jsx(Button, { type: "submit", children: "Save" })
          ] })
        ]
      }
    )
  ] }) });
}
const CONFIG_TYPE_LABELS = {
  PERCENTAGE_BASED: "Percentage",
  ORDER_VALUE_BASED: "OrderValue"
};
function DeliveryConfig() {
  var _a, _b, _c;
  const {
    dcConfig,
    sellerId
  } = useLoaderData();
  const [showError, setShowError] = reactExports.useState(true);
  const [isModalOpen, setIsModalOpen] = reactExports.useState(false);
  const [configTypeFilter, setConfigTypeFilter] = reactExports.useState("");
  const [editConfig, setEditConfig] = reactExports.useState(null);
  const fetcher = useFetcher();
  const filteredData = configTypeFilter ? (_a = dcConfig == null ? void 0 : dcConfig.data) == null ? void 0 : _a.filter((item) => item.configType === configTypeFilter) : dcConfig == null ? void 0 : dcConfig.data;
  const handleSave = (formData) => {
    const fetcherData = new FormData();
    fetcherData.append("actionType", "createDcConfig");
    fetcherData.append("sellerId", sellerId.toString());
    fetcherData.append("dcreqBody", JSON.stringify(formData));
    if (editConfig == null ? void 0 : editConfig.id) {
      fetcherData.append("configId", editConfig.id.toString());
    }
    fetcher.submit(fetcherData, {
      method: "POST"
    });
    setEditConfig(null);
    setIsModalOpen(false);
  };
  const handleEdit = (config) => {
    setEditConfig(config);
    setIsModalOpen(true);
  };
  const handleModalClose = () => {
    setIsModalOpen(false);
    setEditConfig(null);
  };
  const loading = fetcher.state !== "idle";
  return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
    className: "min-h-screen bg-gradient-to-br from-blue-50 to-white flex flex-col items-center py-8 px-2",
    children: [loading && /* @__PURE__ */ jsxRuntimeExports.jsx(SpinnerLoader, {
      loading,
      size: 20
    }), ((_b = dcConfig == null ? void 0 : dcConfig.error) == null ? void 0 : _b.message) && showError && /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
      className: "fixed top-6 left-1/2 -translate-x-1/2 z-50 bg-red-100 border border-red-400 text-red-700 px-6 py-4 rounded shadow-lg flex items-center gap-4",
      children: [/* @__PURE__ */ jsxRuntimeExports.jsx("span", {
        className: "font-semibold",
        children: "Error:"
      }), /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
        children: dcConfig.error.message
      }), /* @__PURE__ */ jsxRuntimeExports.jsx("button", {
        className: "ml-4 px-2 py-1 bg-red-400 text-white rounded hover:bg-red-500",
        onClick: () => setShowError(false),
        children: "Close"
      })]
    }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
      className: "w-full max-w-4xl",
      children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
        className: "flex flex-col sm:flex-row items-center justify-between mb-6 gap-4",
        children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("h1", {
          className: "text-2xl font-bold text-blue-900 flex items-center gap-2",
          children: [/* @__PURE__ */ jsxRuntimeExports.jsx(CirclePercent, {
            className: "h-7 w-7 text-blue-500"
          }), "Delivery Configurations"]
        }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
          className: "flex items-center gap-2",
          children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Filter, {
            className: "h-5 w-5 text-blue-400"
          }), /* @__PURE__ */ jsxRuntimeExports.jsxs("select", {
            value: configTypeFilter,
            onChange: (e) => setConfigTypeFilter(e.target.value),
            className: "rounded border border-gray-300 p-2 text-sm focus:border-blue-400 focus:ring focus:ring-blue-200 focus:ring-opacity-50",
            children: [/* @__PURE__ */ jsxRuntimeExports.jsx("option", {
              value: "",
              children: "All Config Types"
            }), Array.from(new Set((_c = dcConfig == null ? void 0 : dcConfig.data) == null ? void 0 : _c.map((item) => item.configType))).map((type) => /* @__PURE__ */ jsxRuntimeExports.jsx("option", {
              value: type,
              children: CONFIG_TYPE_LABELS[type] || type
            }, type))]
          })]
        })]
      }), (filteredData == null ? void 0 : filteredData.length) > 0 ? /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
        className: "grid grid-cols-1 md:grid-cols-2 gap-6",
        children: filteredData.map((item) => /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
          className: "relative bg-gradient-to-br from-blue-50 via-white to-blue-100 rounded-2xl shadow-xl border border-blue-200 p-6 flex flex-col gap-4 hover:shadow-2xl hover:scale-[1.01] transition-all duration-200",
          children: [/* @__PURE__ */ jsxRuntimeExports.jsx("span", {
            className: "absolute top-3 left-3 px-3 py-1 rounded-full bg-blue-100 text-blue-700 text-xs font-semibold border border-blue-200 shadow z-10",
            style: {
              pointerEvents: "none"
            },
            children: CONFIG_TYPE_LABELS[item.configType] || item.configType
          }), /* @__PURE__ */ jsxRuntimeExports.jsxs("button", {
            className: "absolute top-3 right-3 bg-white border border-blue-200 hover:bg-blue-100 text-blue-700 rounded-full p-2 shadow transition group z-20",
            title: "Edit",
            onClick: () => handleEdit(item),
            children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Pen, {
              className: "h-5 w-5 group-hover:scale-110 transition-transform"
            }), /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
              className: "sr-only",
              children: "Edit"
            })]
          }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
            className: "flex items-center justify-between mt-6 mb-2",
            children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
              className: "flex items-center gap-3",
              children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("span", {
                className: "text-xl font-extrabold text-blue-800 tracking-wide",
                children: ["#", item.id]
              }), /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                className: `flex items-center gap-1 px-2 py-1 rounded-full text-xs font-bold shadow-sm
                      ${item.active ? "bg-green-100 text-green-700" : "bg-gray-200 text-gray-500"}`,
                children: item.active ? /* @__PURE__ */ jsxRuntimeExports.jsxs(jsxRuntimeExports.Fragment, {
                  children: [/* @__PURE__ */ jsxRuntimeExports.jsx(BadgeCheck, {
                    className: "h-4 w-4"
                  }), " Active"]
                }) : /* @__PURE__ */ jsxRuntimeExports.jsxs(jsxRuntimeExports.Fragment, {
                  children: [/* @__PURE__ */ jsxRuntimeExports.jsx(BadgeX, {
                    className: "h-4 w-4"
                  }), " Inactive"]
                })
              })]
            }), /* @__PURE__ */ jsxRuntimeExports.jsxs("span", {
              className: "text-xs text-blue-500 font-semibold",
              children: ["v", item.version]
            })]
          }), /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
            className: "border-t border-blue-100 my-2"
          }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
            className: "grid grid-cols-2 gap-x-6 gap-y-2 text-sm text-gray-700",
            children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
              className: "flex items-center gap-2",
              children: [/* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                className: "font-medium text-gray-900",
                children: "Buyer %:"
              }), /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                children: item.buyerPercentage || 0
              })]
            }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
              className: "flex items-center gap-2",
              children: [/* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                className: "font-medium text-gray-900",
                children: "Seller %:"
              }), /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                children: item.sellerPercentage || 0
              })]
            }), item.configType === "ORDER_VALUE_BASED" && /* @__PURE__ */ jsxRuntimeExports.jsxs(jsxRuntimeExports.Fragment, {
              children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                className: "flex items-center gap-2",
                children: [/* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                  className: "font-medium text-gray-900",
                  children: "Min Order Value:"
                }), /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                  children: item.minOrderValue || 0
                })]
              }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                className: "flex items-center gap-2",
                children: [/* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                  className: "font-medium text-gray-900",
                  children: "Max Order Value:"
                }), /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                  children: item.maxOrderValue || 0
                })]
              })]
            }), item.configType === "PERCENTAGE_BASED" && /* @__PURE__ */ jsxRuntimeExports.jsxs(jsxRuntimeExports.Fragment, {
              children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                className: "flex items-center gap-2",
                children: [/* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                  className: "font-medium text-gray-900",
                  children: "Max Buyer D.C:"
                }), /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                  children: item.maxBuyerDeliveryCharge || 0
                })]
              }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                className: "flex items-center gap-2",
                children: [/* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                  className: "font-medium text-gray-900",
                  children: "Max Seller D.C:"
                }), /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                  children: item.maxSellerDeliveryCharge || 0
                })]
              })]
            })]
          }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
            className: "flex flex-row items-center justify-between mt-4 text-xs text-gray-500 gap-2",
            children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
              className: "flex items-center gap-2",
              children: [/* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                className: "inline-block bg-blue-100 text-blue-700 rounded px-2 py-0.5 font-semibold",
                children: "Created"
              }), /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                className: "font-medium text-gray-700",
                children: new Date(item.createdAt).toLocaleString()
              })]
            }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
              className: "flex items-center gap-2",
              children: [/* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                className: "inline-block bg-blue-100 text-blue-700 rounded px-2 py-0.5 font-semibold",
                children: "Updated"
              }), /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                className: "font-medium text-gray-700",
                children: new Date(item.updatedAt).toLocaleString()
              })]
            })]
          })]
        }, item.id))
      }) : /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
        className: "text-center text-gray-500 py-12",
        children: "No delivery configs found."
      }), /* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
        className: "fixed bottom-5 right-5 rounded-full shadow-lg bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 text-lg",
        onClick: () => {
          setEditConfig(null);
          setIsModalOpen(true);
        },
        children: "+ Create Delivery Config"
      })]
    }), /* @__PURE__ */ jsxRuntimeExports.jsx(CreateDeliveryConfig, {
      isOpen: isModalOpen,
      onClose: handleModalClose,
      onSave: handleSave,
      initialConfig: editConfig ? {
        ...editConfig
        // Remove fields not in DcBody if needed
        // id, createdAt, updatedAt, version, etc. are not part of DcBody
      } : void 0
    })]
  });
}
export {
  DeliveryConfig as default
};
//# sourceMappingURL=home.sellerDetails.deliveryconfig-DPPCAsQs.js.map
