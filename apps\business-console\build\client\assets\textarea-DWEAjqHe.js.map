{"version": 3, "file": "textarea-DWEAjqHe.js", "sources": ["../../../node_modules/@react-aria/utils/dist/useLayoutEffect.mjs", "../../../node_modules/@react-aria/utils/dist/useEffectEvent.mjs", "../../../node_modules/@react-aria/utils/dist/domHelpers.mjs", "../../../node_modules/@react-aria/utils/dist/platform.mjs", "../../../node_modules/@react-aria/utils/dist/isVirtualEvent.mjs", "../../../node_modules/@react-aria/interactions/dist/utils.mjs", "../../../node_modules/@react-aria/interactions/dist/useFocus.mjs", "../../../node_modules/@react-aria/interactions/dist/useFocusVisible.mjs", "../../../node_modules/@react-aria/interactions/dist/useFocusWithin.mjs", "../../../node_modules/@react-aria/interactions/dist/useHover.mjs", "../../../node_modules/@react-aria/focus/dist/useFocusRing.mjs", "../../../node_modules/@headlessui/react/dist/internal/disabled.js", "../../../node_modules/@headlessui/react/dist/internal/id.js", "../../../node_modules/@headlessui/react/dist/components/description/description.js", "../../../node_modules/@headlessui/react/dist/components/label/label.js", "../../../node_modules/@headlessui/react/dist/components/textarea/textarea.js"], "sourcesContent": ["import $HgANd$react from \"react\";\n\n/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */ \nconst $f0a04ccd8dbdd83b$export$e5c5a5f917a5871c = typeof document !== 'undefined' ? (0, $HgANd$react).useLayoutEffect : ()=>{};\n\n\nexport {$f0a04ccd8dbdd83b$export$e5c5a5f917a5871c as useLayoutEffect};\n//# sourceMappingURL=useLayoutEffect.module.js.map\n", "import {useLayoutEffect as $f0a04ccd8dbdd83b$export$e5c5a5f917a5871c} from \"./useLayoutEffect.mjs\";\nimport {useRef as $lmaYr$useRef, useCallback as $lmaYr$useCallback} from \"react\";\n\n/*\n * Copyright 2023 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */ \n\nfunction $8ae05eaa5c114e9c$export$7f54fc3180508a52(fn) {\n    const ref = (0, $lmaYr$useRef)(null);\n    (0, $f0a04ccd8dbdd83b$export$e5c5a5f917a5871c)(()=>{\n        ref.current = fn;\n    }, [\n        fn\n    ]);\n    // @ts-ignore\n    return (0, $lmaYr$useCallback)((...args)=>{\n        const f = ref.current;\n        return f === null || f === void 0 ? void 0 : f(...args);\n    }, []);\n}\n\n\nexport {$8ae05eaa5c114e9c$export$7f54fc3180508a52 as useEffectEvent};\n//# sourceMappingURL=useEffectEvent.module.js.map\n", "const $431fbd86ca7dc216$export$b204af158042fbac = (el)=>{\n    var _el_ownerDocument;\n    return (_el_ownerDocument = el === null || el === void 0 ? void 0 : el.ownerDocument) !== null && _el_ownerDocument !== void 0 ? _el_ownerDocument : document;\n};\nconst $431fbd86ca7dc216$export$f21a1ffae260145a = (el)=>{\n    if (el && 'window' in el && el.window === el) return el;\n    const doc = $431fbd86ca7dc216$export$b204af158042fbac(el);\n    return doc.defaultView || window;\n};\n\n\nexport {$431fbd86ca7dc216$export$b204af158042fbac as getOwnerDocument, $431fbd86ca7dc216$export$f21a1ffae260145a as getOwnerWindow};\n//# sourceMappingURL=domHelpers.module.js.map\n", "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */ function $c87311424ea30a05$var$testUserAgent(re) {\n    var _window_navigator_userAgentData;\n    if (typeof window === 'undefined' || window.navigator == null) return false;\n    return ((_window_navigator_userAgentData = window.navigator['userAgentData']) === null || _window_navigator_userAgentData === void 0 ? void 0 : _window_navigator_userAgentData.brands.some((brand)=>re.test(brand.brand))) || re.test(window.navigator.userAgent);\n}\nfunction $c87311424ea30a05$var$testPlatform(re) {\n    var _window_navigator_userAgentData;\n    return typeof window !== 'undefined' && window.navigator != null ? re.test(((_window_navigator_userAgentData = window.navigator['userAgentData']) === null || _window_navigator_userAgentData === void 0 ? void 0 : _window_navigator_userAgentData.platform) || window.navigator.platform) : false;\n}\nfunction $c87311424ea30a05$var$cached(fn) {\n    let res = null;\n    return ()=>{\n        if (res == null) res = fn();\n        return res;\n    };\n}\nconst $c87311424ea30a05$export$9ac100e40613ea10 = $c87311424ea30a05$var$cached(function() {\n    return $c87311424ea30a05$var$testPlatform(/^Mac/i);\n});\nconst $c87311424ea30a05$export$186c6964ca17d99 = $c87311424ea30a05$var$cached(function() {\n    return $c87311424ea30a05$var$testPlatform(/^iPhone/i);\n});\nconst $c87311424ea30a05$export$7bef049ce92e4224 = $c87311424ea30a05$var$cached(function() {\n    return $c87311424ea30a05$var$testPlatform(/^iPad/i) || // iPadOS 13 lies and says it's a Mac, but we can distinguish by detecting touch support.\n    $c87311424ea30a05$export$9ac100e40613ea10() && navigator.maxTouchPoints > 1;\n});\nconst $c87311424ea30a05$export$fedb369cb70207f1 = $c87311424ea30a05$var$cached(function() {\n    return $c87311424ea30a05$export$186c6964ca17d99() || $c87311424ea30a05$export$7bef049ce92e4224();\n});\nconst $c87311424ea30a05$export$e1865c3bedcd822b = $c87311424ea30a05$var$cached(function() {\n    return $c87311424ea30a05$export$9ac100e40613ea10() || $c87311424ea30a05$export$fedb369cb70207f1();\n});\nconst $c87311424ea30a05$export$78551043582a6a98 = $c87311424ea30a05$var$cached(function() {\n    return $c87311424ea30a05$var$testUserAgent(/AppleWebKit/i) && !$c87311424ea30a05$export$6446a186d09e379e();\n});\nconst $c87311424ea30a05$export$6446a186d09e379e = $c87311424ea30a05$var$cached(function() {\n    return $c87311424ea30a05$var$testUserAgent(/Chrome/i);\n});\nconst $c87311424ea30a05$export$a11b0059900ceec8 = $c87311424ea30a05$var$cached(function() {\n    return $c87311424ea30a05$var$testUserAgent(/Android/i);\n});\nconst $c87311424ea30a05$export$b7d78993b74f766d = $c87311424ea30a05$var$cached(function() {\n    return $c87311424ea30a05$var$testUserAgent(/Firefox/i);\n});\n\n\nexport {$c87311424ea30a05$export$9ac100e40613ea10 as isMac, $c87311424ea30a05$export$186c6964ca17d99 as isIPhone, $c87311424ea30a05$export$7bef049ce92e4224 as isIPad, $c87311424ea30a05$export$fedb369cb70207f1 as isIOS, $c87311424ea30a05$export$e1865c3bedcd822b as isAppleDevice, $c87311424ea30a05$export$78551043582a6a98 as isWebKit, $c87311424ea30a05$export$6446a186d09e379e as isChrome, $c87311424ea30a05$export$a11b0059900ceec8 as isAndroid, $c87311424ea30a05$export$b7d78993b74f766d as isFirefox};\n//# sourceMappingURL=platform.module.js.map\n", "import {isAndroid as $c87311424ea30a05$export$a11b0059900ceec8} from \"./platform.mjs\";\n\n/*\n * Copyright 2022 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */ \nfunction $6a7db85432448f7f$export$60278871457622de(event) {\n    // JAWS/NVDA with Firefox.\n    if (event.mozInputSource === 0 && event.isTrusted) return true;\n    // Android TalkBack's detail value varies depending on the event listener providing the event so we have specific logic here instead\n    // If pointerType is defined, event is from a click listener. For events from mousedown listener, detail === 0 is a sufficient check\n    // to detect TalkBack virtual clicks.\n    if ((0, $c87311424ea30a05$export$a11b0059900ceec8)() && event.pointerType) return event.type === 'click' && event.buttons === 1;\n    return event.detail === 0 && !event.pointerType;\n}\nfunction $6a7db85432448f7f$export$29bf1b5f2c56cf63(event) {\n    // If the pointer size is zero, then we assume it's from a screen reader.\n    // Android TalkBack double tap will sometimes return a event with width and height of 1\n    // and pointerType === 'mouse' so we need to check for a specific combination of event attributes.\n    // Cannot use \"event.pressure === 0\" as the sole check due to Safari pointer events always returning pressure === 0\n    // instead of .5, see https://bugs.webkit.org/show_bug.cgi?id=206216. event.pointerType === 'mouse' is to distingush\n    // Talkback double tap from Windows Firefox touch screen press\n    return !(0, $c87311424ea30a05$export$a11b0059900ceec8)() && event.width === 0 && event.height === 0 || event.width === 1 && event.height === 1 && event.pressure === 0 && event.detail === 0 && event.pointerType === 'mouse';\n}\n\n\nexport {$6a7db85432448f7f$export$60278871457622de as isVirtualClick, $6a7db85432448f7f$export$29bf1b5f2c56cf63 as isVirtualPointerEvent};\n//# sourceMappingURL=isVirtualEvent.module.js.map\n", "import {useRef as $6dfIe$useRef, useCallback as $6dfIe$useCallback} from \"react\";\nimport {useLayoutEffect as $6dfIe$useLayoutEffect, useEffectEvent as $6dfIe$useEffectEvent} from \"@react-aria/utils\";\n\n/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */ \n\nclass $8a9cb279dc87e130$export$905e7fc544a71f36 {\n    isDefaultPrevented() {\n        return this.nativeEvent.defaultPrevented;\n    }\n    preventDefault() {\n        this.defaultPrevented = true;\n        this.nativeEvent.preventDefault();\n    }\n    stopPropagation() {\n        this.nativeEvent.stopPropagation();\n        this.isPropagationStopped = ()=>true;\n    }\n    isPropagationStopped() {\n        return false;\n    }\n    persist() {}\n    constructor(type, nativeEvent){\n        this.nativeEvent = nativeEvent;\n        this.target = nativeEvent.target;\n        this.currentTarget = nativeEvent.currentTarget;\n        this.relatedTarget = nativeEvent.relatedTarget;\n        this.bubbles = nativeEvent.bubbles;\n        this.cancelable = nativeEvent.cancelable;\n        this.defaultPrevented = nativeEvent.defaultPrevented;\n        this.eventPhase = nativeEvent.eventPhase;\n        this.isTrusted = nativeEvent.isTrusted;\n        this.timeStamp = nativeEvent.timeStamp;\n        this.type = type;\n    }\n}\nfunction $8a9cb279dc87e130$export$715c682d09d639cc(onBlur) {\n    let stateRef = (0, $6dfIe$useRef)({\n        isFocused: false,\n        observer: null\n    });\n    // Clean up MutationObserver on unmount. See below.\n    (0, $6dfIe$useLayoutEffect)(()=>{\n        const state = stateRef.current;\n        return ()=>{\n            if (state.observer) {\n                state.observer.disconnect();\n                state.observer = null;\n            }\n        };\n    }, []);\n    let dispatchBlur = (0, $6dfIe$useEffectEvent)((e)=>{\n        onBlur === null || onBlur === void 0 ? void 0 : onBlur(e);\n    });\n    // This function is called during a React onFocus event.\n    return (0, $6dfIe$useCallback)((e)=>{\n        // React does not fire onBlur when an element is disabled. https://github.com/facebook/react/issues/9142\n        // Most browsers fire a native focusout event in this case, except for Firefox. In that case, we use a\n        // MutationObserver to watch for the disabled attribute, and dispatch these events ourselves.\n        // For browsers that do, focusout fires before the MutationObserver, so onBlur should not fire twice.\n        if (e.target instanceof HTMLButtonElement || e.target instanceof HTMLInputElement || e.target instanceof HTMLTextAreaElement || e.target instanceof HTMLSelectElement) {\n            stateRef.current.isFocused = true;\n            let target = e.target;\n            let onBlurHandler = (e)=>{\n                stateRef.current.isFocused = false;\n                if (target.disabled) // For backward compatibility, dispatch a (fake) React synthetic event.\n                dispatchBlur(new $8a9cb279dc87e130$export$905e7fc544a71f36('blur', e));\n                // We no longer need the MutationObserver once the target is blurred.\n                if (stateRef.current.observer) {\n                    stateRef.current.observer.disconnect();\n                    stateRef.current.observer = null;\n                }\n            };\n            target.addEventListener('focusout', onBlurHandler, {\n                once: true\n            });\n            stateRef.current.observer = new MutationObserver(()=>{\n                if (stateRef.current.isFocused && target.disabled) {\n                    var _stateRef_current_observer;\n                    (_stateRef_current_observer = stateRef.current.observer) === null || _stateRef_current_observer === void 0 ? void 0 : _stateRef_current_observer.disconnect();\n                    let relatedTargetEl = target === document.activeElement ? null : document.activeElement;\n                    target.dispatchEvent(new FocusEvent('blur', {\n                        relatedTarget: relatedTargetEl\n                    }));\n                    target.dispatchEvent(new FocusEvent('focusout', {\n                        bubbles: true,\n                        relatedTarget: relatedTargetEl\n                    }));\n                }\n            });\n            stateRef.current.observer.observe(target, {\n                attributes: true,\n                attributeFilter: [\n                    'disabled'\n                ]\n            });\n        }\n    }, [\n        dispatchBlur\n    ]);\n}\n\n\nexport {$8a9cb279dc87e130$export$905e7fc544a71f36 as SyntheticFocusEvent, $8a9cb279dc87e130$export$715c682d09d639cc as useSyntheticBlurEvent};\n//# sourceMappingURL=utils.module.js.map\n", "import {useSyntheticBlurEvent as $8a9cb279dc87e130$export$715c682d09d639cc} from \"./utils.mjs\";\nimport {useCallback as $hf0lj$useCallback} from \"react\";\nimport {getOwnerDocument as $hf0lj$getOwnerDocument} from \"@react-aria/utils\";\n\n/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */ // Portions of the code in this file are based on code from react.\n// Original licensing for the following can be found in the\n// NOTICE file in the root directory of this source tree.\n// See https://github.com/facebook/react/tree/cc7c1aece46a6b69b41958d731e0fd27c94bfc6c/packages/react-interactions\n\n\n\nfunction $a1ea59d68270f0dd$export$f8168d8dd8fd66e6(props) {\n    let { isDisabled: isDisabled, onFocus: onFocusProp, onBlur: onBlurProp, onFocusChange: onFocusChange } = props;\n    const onBlur = (0, $hf0lj$useCallback)((e)=>{\n        if (e.target === e.currentTarget) {\n            if (onBlurProp) onBlurProp(e);\n            if (onFocusChange) onFocusChange(false);\n            return true;\n        }\n    }, [\n        onBlurProp,\n        onFocusChange\n    ]);\n    const onSyntheticFocus = (0, $8a9cb279dc87e130$export$715c682d09d639cc)(onBlur);\n    const onFocus = (0, $hf0lj$useCallback)((e)=>{\n        // Double check that document.activeElement actually matches e.target in case a previously chained\n        // focus handler already moved focus somewhere else.\n        const ownerDocument = (0, $hf0lj$getOwnerDocument)(e.target);\n        if (e.target === e.currentTarget && ownerDocument.activeElement === e.target) {\n            if (onFocusProp) onFocusProp(e);\n            if (onFocusChange) onFocusChange(true);\n            onSyntheticFocus(e);\n        }\n    }, [\n        onFocusChange,\n        onFocusProp,\n        onSyntheticFocus\n    ]);\n    return {\n        focusProps: {\n            onFocus: !isDisabled && (onFocusProp || onFocusChange || onBlurProp) ? onFocus : undefined,\n            onBlur: !isDisabled && (onBlurProp || onFocusChange) ? onBlur : undefined\n        }\n    };\n}\n\n\nexport {$a1ea59d68270f0dd$export$f8168d8dd8fd66e6 as useFocus};\n//# sourceMappingURL=useFocus.module.js.map\n", "import {isMac as $28AnR$isMac, isVirtualClick as $28AnR$isVirtualClick, getOwnerWindow as $28AnR$getOwnerWindow, getOwnerDocument as $28AnR$getOwnerDocument} from \"@react-aria/utils\";\nimport {useState as $28AnR$useState, useEffect as $28AnR$useEffect} from \"react\";\nimport {useIsSSR as $28AnR$useIsSSR} from \"@react-aria/ssr\";\n\n/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */ // Portions of the code in this file are based on code from react.\n// Original licensing for the following can be found in the\n// NOTICE file in the root directory of this source tree.\n// See https://github.com/facebook/react/tree/cc7c1aece46a6b69b41958d731e0fd27c94bfc6c/packages/react-interactions\n\n\n\nlet $507fabe10e71c6fb$var$currentModality = null;\nlet $507fabe10e71c6fb$var$changeHandlers = new Set();\nlet $507fabe10e71c6fb$export$d90243b58daecda7 = new Map(); // We use a map here to support setting event listeners across multiple document objects.\nlet $507fabe10e71c6fb$var$hasEventBeforeFocus = false;\nlet $507fabe10e71c6fb$var$hasBlurredWindowRecently = false;\n// Only Tab or Esc keys will make focus visible on text input elements\nconst $507fabe10e71c6fb$var$FOCUS_VISIBLE_INPUT_KEYS = {\n    Tab: true,\n    Escape: true\n};\nfunction $507fabe10e71c6fb$var$triggerChangeHandlers(modality, e) {\n    for (let handler of $507fabe10e71c6fb$var$changeHandlers)handler(modality, e);\n}\n/**\n * Helper function to determine if a KeyboardEvent is unmodified and could make keyboard focus styles visible.\n */ function $507fabe10e71c6fb$var$isValidKey(e) {\n    // Control and Shift keys trigger when navigating back to the tab with keyboard.\n    return !(e.metaKey || !(0, $28AnR$isMac)() && e.altKey || e.ctrlKey || e.key === 'Control' || e.key === 'Shift' || e.key === 'Meta');\n}\nfunction $507fabe10e71c6fb$var$handleKeyboardEvent(e) {\n    $507fabe10e71c6fb$var$hasEventBeforeFocus = true;\n    if ($507fabe10e71c6fb$var$isValidKey(e)) {\n        $507fabe10e71c6fb$var$currentModality = 'keyboard';\n        $507fabe10e71c6fb$var$triggerChangeHandlers('keyboard', e);\n    }\n}\nfunction $507fabe10e71c6fb$var$handlePointerEvent(e) {\n    $507fabe10e71c6fb$var$currentModality = 'pointer';\n    if (e.type === 'mousedown' || e.type === 'pointerdown') {\n        $507fabe10e71c6fb$var$hasEventBeforeFocus = true;\n        $507fabe10e71c6fb$var$triggerChangeHandlers('pointer', e);\n    }\n}\nfunction $507fabe10e71c6fb$var$handleClickEvent(e) {\n    if ((0, $28AnR$isVirtualClick)(e)) {\n        $507fabe10e71c6fb$var$hasEventBeforeFocus = true;\n        $507fabe10e71c6fb$var$currentModality = 'virtual';\n    }\n}\nfunction $507fabe10e71c6fb$var$handleFocusEvent(e) {\n    // Firefox fires two extra focus events when the user first clicks into an iframe:\n    // first on the window, then on the document. We ignore these events so they don't\n    // cause keyboard focus rings to appear.\n    if (e.target === window || e.target === document) return;\n    // If a focus event occurs without a preceding keyboard or pointer event, switch to virtual modality.\n    // This occurs, for example, when navigating a form with the next/previous buttons on iOS.\n    if (!$507fabe10e71c6fb$var$hasEventBeforeFocus && !$507fabe10e71c6fb$var$hasBlurredWindowRecently) {\n        $507fabe10e71c6fb$var$currentModality = 'virtual';\n        $507fabe10e71c6fb$var$triggerChangeHandlers('virtual', e);\n    }\n    $507fabe10e71c6fb$var$hasEventBeforeFocus = false;\n    $507fabe10e71c6fb$var$hasBlurredWindowRecently = false;\n}\nfunction $507fabe10e71c6fb$var$handleWindowBlur() {\n    // When the window is blurred, reset state. This is necessary when tabbing out of the window,\n    // for example, since a subsequent focus event won't be fired.\n    $507fabe10e71c6fb$var$hasEventBeforeFocus = false;\n    $507fabe10e71c6fb$var$hasBlurredWindowRecently = true;\n}\n/**\n * Setup global event listeners to control when keyboard focus style should be visible.\n */ function $507fabe10e71c6fb$var$setupGlobalFocusEvents(element) {\n    if (typeof window === 'undefined' || $507fabe10e71c6fb$export$d90243b58daecda7.get((0, $28AnR$getOwnerWindow)(element))) return;\n    const windowObject = (0, $28AnR$getOwnerWindow)(element);\n    const documentObject = (0, $28AnR$getOwnerDocument)(element);\n    // Programmatic focus() calls shouldn't affect the current input modality.\n    // However, we need to detect other cases when a focus event occurs without\n    // a preceding user event (e.g. screen reader focus). Overriding the focus\n    // method on HTMLElement.prototype is a bit hacky, but works.\n    let focus = windowObject.HTMLElement.prototype.focus;\n    windowObject.HTMLElement.prototype.focus = function() {\n        $507fabe10e71c6fb$var$hasEventBeforeFocus = true;\n        focus.apply(this, arguments);\n    };\n    documentObject.addEventListener('keydown', $507fabe10e71c6fb$var$handleKeyboardEvent, true);\n    documentObject.addEventListener('keyup', $507fabe10e71c6fb$var$handleKeyboardEvent, true);\n    documentObject.addEventListener('click', $507fabe10e71c6fb$var$handleClickEvent, true);\n    // Register focus events on the window so they are sure to happen\n    // before React's event listeners (registered on the document).\n    windowObject.addEventListener('focus', $507fabe10e71c6fb$var$handleFocusEvent, true);\n    windowObject.addEventListener('blur', $507fabe10e71c6fb$var$handleWindowBlur, false);\n    if (typeof PointerEvent !== 'undefined') {\n        documentObject.addEventListener('pointerdown', $507fabe10e71c6fb$var$handlePointerEvent, true);\n        documentObject.addEventListener('pointermove', $507fabe10e71c6fb$var$handlePointerEvent, true);\n        documentObject.addEventListener('pointerup', $507fabe10e71c6fb$var$handlePointerEvent, true);\n    } else {\n        documentObject.addEventListener('mousedown', $507fabe10e71c6fb$var$handlePointerEvent, true);\n        documentObject.addEventListener('mousemove', $507fabe10e71c6fb$var$handlePointerEvent, true);\n        documentObject.addEventListener('mouseup', $507fabe10e71c6fb$var$handlePointerEvent, true);\n    }\n    // Add unmount handler\n    windowObject.addEventListener('beforeunload', ()=>{\n        $507fabe10e71c6fb$var$tearDownWindowFocusTracking(element);\n    }, {\n        once: true\n    });\n    $507fabe10e71c6fb$export$d90243b58daecda7.set(windowObject, {\n        focus: focus\n    });\n}\nconst $507fabe10e71c6fb$var$tearDownWindowFocusTracking = (element, loadListener)=>{\n    const windowObject = (0, $28AnR$getOwnerWindow)(element);\n    const documentObject = (0, $28AnR$getOwnerDocument)(element);\n    if (loadListener) documentObject.removeEventListener('DOMContentLoaded', loadListener);\n    if (!$507fabe10e71c6fb$export$d90243b58daecda7.has(windowObject)) return;\n    windowObject.HTMLElement.prototype.focus = $507fabe10e71c6fb$export$d90243b58daecda7.get(windowObject).focus;\n    documentObject.removeEventListener('keydown', $507fabe10e71c6fb$var$handleKeyboardEvent, true);\n    documentObject.removeEventListener('keyup', $507fabe10e71c6fb$var$handleKeyboardEvent, true);\n    documentObject.removeEventListener('click', $507fabe10e71c6fb$var$handleClickEvent, true);\n    windowObject.removeEventListener('focus', $507fabe10e71c6fb$var$handleFocusEvent, true);\n    windowObject.removeEventListener('blur', $507fabe10e71c6fb$var$handleWindowBlur, false);\n    if (typeof PointerEvent !== 'undefined') {\n        documentObject.removeEventListener('pointerdown', $507fabe10e71c6fb$var$handlePointerEvent, true);\n        documentObject.removeEventListener('pointermove', $507fabe10e71c6fb$var$handlePointerEvent, true);\n        documentObject.removeEventListener('pointerup', $507fabe10e71c6fb$var$handlePointerEvent, true);\n    } else {\n        documentObject.removeEventListener('mousedown', $507fabe10e71c6fb$var$handlePointerEvent, true);\n        documentObject.removeEventListener('mousemove', $507fabe10e71c6fb$var$handlePointerEvent, true);\n        documentObject.removeEventListener('mouseup', $507fabe10e71c6fb$var$handlePointerEvent, true);\n    }\n    $507fabe10e71c6fb$export$d90243b58daecda7.delete(windowObject);\n};\nfunction $507fabe10e71c6fb$export$2f1888112f558a7d(element) {\n    const documentObject = (0, $28AnR$getOwnerDocument)(element);\n    let loadListener;\n    if (documentObject.readyState !== 'loading') $507fabe10e71c6fb$var$setupGlobalFocusEvents(element);\n    else {\n        loadListener = ()=>{\n            $507fabe10e71c6fb$var$setupGlobalFocusEvents(element);\n        };\n        documentObject.addEventListener('DOMContentLoaded', loadListener);\n    }\n    return ()=>$507fabe10e71c6fb$var$tearDownWindowFocusTracking(element, loadListener);\n}\n// Server-side rendering does not have the document object defined\n// eslint-disable-next-line no-restricted-globals\nif (typeof document !== 'undefined') $507fabe10e71c6fb$export$2f1888112f558a7d();\nfunction $507fabe10e71c6fb$export$b9b3dfddab17db27() {\n    return $507fabe10e71c6fb$var$currentModality !== 'pointer';\n}\nfunction $507fabe10e71c6fb$export$630ff653c5ada6a9() {\n    return $507fabe10e71c6fb$var$currentModality;\n}\nfunction $507fabe10e71c6fb$export$8397ddfc504fdb9a(modality) {\n    $507fabe10e71c6fb$var$currentModality = modality;\n    $507fabe10e71c6fb$var$triggerChangeHandlers(modality, null);\n}\nfunction $507fabe10e71c6fb$export$98e20ec92f614cfe() {\n    $507fabe10e71c6fb$var$setupGlobalFocusEvents();\n    let [modality, setModality] = (0, $28AnR$useState)($507fabe10e71c6fb$var$currentModality);\n    (0, $28AnR$useEffect)(()=>{\n        let handler = ()=>{\n            setModality($507fabe10e71c6fb$var$currentModality);\n        };\n        $507fabe10e71c6fb$var$changeHandlers.add(handler);\n        return ()=>{\n            $507fabe10e71c6fb$var$changeHandlers.delete(handler);\n        };\n    }, []);\n    return (0, $28AnR$useIsSSR)() ? null : modality;\n}\nconst $507fabe10e71c6fb$var$nonTextInputTypes = new Set([\n    'checkbox',\n    'radio',\n    'range',\n    'color',\n    'file',\n    'image',\n    'button',\n    'submit',\n    'reset'\n]);\n/**\n * If this is attached to text input component, return if the event is a focus event (Tab/Escape keys pressed) so that\n * focus visible style can be properly set.\n */ function $507fabe10e71c6fb$var$isKeyboardFocusEvent(isTextInput, modality, e) {\n    var _e_target;\n    const IHTMLInputElement = typeof window !== 'undefined' ? (0, $28AnR$getOwnerWindow)(e === null || e === void 0 ? void 0 : e.target).HTMLInputElement : HTMLInputElement;\n    const IHTMLTextAreaElement = typeof window !== 'undefined' ? (0, $28AnR$getOwnerWindow)(e === null || e === void 0 ? void 0 : e.target).HTMLTextAreaElement : HTMLTextAreaElement;\n    const IHTMLElement = typeof window !== 'undefined' ? (0, $28AnR$getOwnerWindow)(e === null || e === void 0 ? void 0 : e.target).HTMLElement : HTMLElement;\n    const IKeyboardEvent = typeof window !== 'undefined' ? (0, $28AnR$getOwnerWindow)(e === null || e === void 0 ? void 0 : e.target).KeyboardEvent : KeyboardEvent;\n    isTextInput = isTextInput || (e === null || e === void 0 ? void 0 : e.target) instanceof IHTMLInputElement && !$507fabe10e71c6fb$var$nonTextInputTypes.has(e === null || e === void 0 ? void 0 : (_e_target = e.target) === null || _e_target === void 0 ? void 0 : _e_target.type) || (e === null || e === void 0 ? void 0 : e.target) instanceof IHTMLTextAreaElement || (e === null || e === void 0 ? void 0 : e.target) instanceof IHTMLElement && (e === null || e === void 0 ? void 0 : e.target.isContentEditable);\n    return !(isTextInput && modality === 'keyboard' && e instanceof IKeyboardEvent && !$507fabe10e71c6fb$var$FOCUS_VISIBLE_INPUT_KEYS[e.key]);\n}\nfunction $507fabe10e71c6fb$export$ffd9e5021c1fb2d6(props = {}) {\n    let { isTextInput: isTextInput, autoFocus: autoFocus } = props;\n    let [isFocusVisibleState, setFocusVisible] = (0, $28AnR$useState)(autoFocus || $507fabe10e71c6fb$export$b9b3dfddab17db27());\n    $507fabe10e71c6fb$export$ec71b4b83ac08ec3((isFocusVisible)=>{\n        setFocusVisible(isFocusVisible);\n    }, [\n        isTextInput\n    ], {\n        isTextInput: isTextInput\n    });\n    return {\n        isFocusVisible: isFocusVisibleState\n    };\n}\nfunction $507fabe10e71c6fb$export$ec71b4b83ac08ec3(fn, deps, opts) {\n    $507fabe10e71c6fb$var$setupGlobalFocusEvents();\n    (0, $28AnR$useEffect)(()=>{\n        let handler = (modality, e)=>{\n            if (!$507fabe10e71c6fb$var$isKeyboardFocusEvent(!!(opts === null || opts === void 0 ? void 0 : opts.isTextInput), modality, e)) return;\n            fn($507fabe10e71c6fb$export$b9b3dfddab17db27());\n        };\n        $507fabe10e71c6fb$var$changeHandlers.add(handler);\n        return ()=>{\n            $507fabe10e71c6fb$var$changeHandlers.delete(handler);\n        };\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, deps);\n}\n\n\nexport {$507fabe10e71c6fb$export$d90243b58daecda7 as hasSetupGlobalListeners, $507fabe10e71c6fb$export$2f1888112f558a7d as addWindowFocusTracking, $507fabe10e71c6fb$export$b9b3dfddab17db27 as isFocusVisible, $507fabe10e71c6fb$export$630ff653c5ada6a9 as getInteractionModality, $507fabe10e71c6fb$export$8397ddfc504fdb9a as setInteractionModality, $507fabe10e71c6fb$export$98e20ec92f614cfe as useInteractionModality, $507fabe10e71c6fb$export$ffd9e5021c1fb2d6 as useFocusVisible, $507fabe10e71c6fb$export$ec71b4b83ac08ec3 as useFocusVisibleListener};\n//# sourceMappingURL=useFocusVisible.module.js.map\n", "import {useSyntheticBlurEvent as $8a9cb279dc87e130$export$715c682d09d639cc} from \"./utils.mjs\";\nimport {useRef as $3b9Q0$useRef, useCallback as $3b9Q0$useCallback} from \"react\";\n\n/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */ // Portions of the code in this file are based on code from react.\n// Original licensing for the following can be found in the\n// NOTICE file in the root directory of this source tree.\n// See https://github.com/facebook/react/tree/cc7c1aece46a6b69b41958d731e0fd27c94bfc6c/packages/react-interactions\n\n\nfunction $9ab94262bd0047c7$export$420e68273165f4ec(props) {\n    let { isDisabled: isDisabled, onBlurWithin: onBlurWithin, onFocusWithin: onFocusWithin, onFocusWithinChange: onFocusWithinChange } = props;\n    let state = (0, $3b9Q0$useRef)({\n        isFocusWithin: false\n    });\n    let onBlur = (0, $3b9Q0$useCallback)((e)=>{\n        // We don't want to trigger onBlurWithin and then immediately onFocusWithin again\n        // when moving focus inside the element. Only trigger if the currentTarget doesn't\n        // include the relatedTarget (where focus is moving).\n        if (state.current.isFocusWithin && !e.currentTarget.contains(e.relatedTarget)) {\n            state.current.isFocusWithin = false;\n            if (onBlurWithin) onBlurWithin(e);\n            if (onFocusWithinChange) onFocusWithinChange(false);\n        }\n    }, [\n        onBlurWithin,\n        onFocusWithinChange,\n        state\n    ]);\n    let onSyntheticFocus = (0, $8a9cb279dc87e130$export$715c682d09d639cc)(onBlur);\n    let onFocus = (0, $3b9Q0$useCallback)((e)=>{\n        // Double check that document.activeElement actually matches e.target in case a previously chained\n        // focus handler already moved focus somewhere else.\n        if (!state.current.isFocusWithin && document.activeElement === e.target) {\n            if (onFocusWithin) onFocusWithin(e);\n            if (onFocusWithinChange) onFocusWithinChange(true);\n            state.current.isFocusWithin = true;\n            onSyntheticFocus(e);\n        }\n    }, [\n        onFocusWithin,\n        onFocusWithinChange,\n        onSyntheticFocus\n    ]);\n    if (isDisabled) return {\n        focusWithinProps: {\n            // These should not have been null, that would conflict in mergeProps\n            onFocus: undefined,\n            onBlur: undefined\n        }\n    };\n    return {\n        focusWithinProps: {\n            onFocus: onFocus,\n            onBlur: onBlur\n        }\n    };\n}\n\n\nexport {$9ab94262bd0047c7$export$420e68273165f4ec as useFocusWithin};\n//# sourceMappingURL=useFocusWithin.module.js.map\n", "import {useState as $AWxnT$useState, useRef as $AWxnT$useRef, useEffect as $AWxnT$useEffect, useMemo as $AWxnT$useMemo} from \"react\";\n\n/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */ // Portions of the code in this file are based on code from react.\n// Original licensing for the following can be found in the\n// NOTICE file in the root directory of this source tree.\n// See https://github.com/facebook/react/tree/cc7c1aece46a6b69b41958d731e0fd27c94bfc6c/packages/react-interactions\n\n// iOS fires onPointerEnter twice: once with pointerType=\"touch\" and again with pointerType=\"mouse\".\n// We want to ignore these emulated events so they do not trigger hover behavior.\n// See https://bugs.webkit.org/show_bug.cgi?id=214609.\nlet $6179b936705e76d3$var$globalIgnoreEmulatedMouseEvents = false;\nlet $6179b936705e76d3$var$hoverCount = 0;\nfunction $6179b936705e76d3$var$setGlobalIgnoreEmulatedMouseEvents() {\n    $6179b936705e76d3$var$globalIgnoreEmulatedMouseEvents = true;\n    // Clear globalIgnoreEmulatedMouseEvents after a short timeout. iOS fires onPointerEnter\n    // with pointerType=\"mouse\" immediately after onPointerUp and before onFocus. On other\n    // devices that don't have this quirk, we don't want to ignore a mouse hover sometime in\n    // the distant future because a user previously touched the element.\n    setTimeout(()=>{\n        $6179b936705e76d3$var$globalIgnoreEmulatedMouseEvents = false;\n    }, 50);\n}\nfunction $6179b936705e76d3$var$handleGlobalPointerEvent(e) {\n    if (e.pointerType === 'touch') $6179b936705e76d3$var$setGlobalIgnoreEmulatedMouseEvents();\n}\nfunction $6179b936705e76d3$var$setupGlobalTouchEvents() {\n    if (typeof document === 'undefined') return;\n    if (typeof PointerEvent !== 'undefined') document.addEventListener('pointerup', $6179b936705e76d3$var$handleGlobalPointerEvent);\n    else document.addEventListener('touchend', $6179b936705e76d3$var$setGlobalIgnoreEmulatedMouseEvents);\n    $6179b936705e76d3$var$hoverCount++;\n    return ()=>{\n        $6179b936705e76d3$var$hoverCount--;\n        if ($6179b936705e76d3$var$hoverCount > 0) return;\n        if (typeof PointerEvent !== 'undefined') document.removeEventListener('pointerup', $6179b936705e76d3$var$handleGlobalPointerEvent);\n        else document.removeEventListener('touchend', $6179b936705e76d3$var$setGlobalIgnoreEmulatedMouseEvents);\n    };\n}\nfunction $6179b936705e76d3$export$ae780daf29e6d456(props) {\n    let { onHoverStart: onHoverStart, onHoverChange: onHoverChange, onHoverEnd: onHoverEnd, isDisabled: isDisabled } = props;\n    let [isHovered, setHovered] = (0, $AWxnT$useState)(false);\n    let state = (0, $AWxnT$useRef)({\n        isHovered: false,\n        ignoreEmulatedMouseEvents: false,\n        pointerType: '',\n        target: null\n    }).current;\n    (0, $AWxnT$useEffect)($6179b936705e76d3$var$setupGlobalTouchEvents, []);\n    let { hoverProps: hoverProps, triggerHoverEnd: triggerHoverEnd } = (0, $AWxnT$useMemo)(()=>{\n        let triggerHoverStart = (event, pointerType)=>{\n            state.pointerType = pointerType;\n            if (isDisabled || pointerType === 'touch' || state.isHovered || !event.currentTarget.contains(event.target)) return;\n            state.isHovered = true;\n            let target = event.currentTarget;\n            state.target = target;\n            if (onHoverStart) onHoverStart({\n                type: 'hoverstart',\n                target: target,\n                pointerType: pointerType\n            });\n            if (onHoverChange) onHoverChange(true);\n            setHovered(true);\n        };\n        let triggerHoverEnd = (event, pointerType)=>{\n            state.pointerType = '';\n            state.target = null;\n            if (pointerType === 'touch' || !state.isHovered) return;\n            state.isHovered = false;\n            let target = event.currentTarget;\n            if (onHoverEnd) onHoverEnd({\n                type: 'hoverend',\n                target: target,\n                pointerType: pointerType\n            });\n            if (onHoverChange) onHoverChange(false);\n            setHovered(false);\n        };\n        let hoverProps = {};\n        if (typeof PointerEvent !== 'undefined') {\n            hoverProps.onPointerEnter = (e)=>{\n                if ($6179b936705e76d3$var$globalIgnoreEmulatedMouseEvents && e.pointerType === 'mouse') return;\n                triggerHoverStart(e, e.pointerType);\n            };\n            hoverProps.onPointerLeave = (e)=>{\n                if (!isDisabled && e.currentTarget.contains(e.target)) triggerHoverEnd(e, e.pointerType);\n            };\n        } else {\n            hoverProps.onTouchStart = ()=>{\n                state.ignoreEmulatedMouseEvents = true;\n            };\n            hoverProps.onMouseEnter = (e)=>{\n                if (!state.ignoreEmulatedMouseEvents && !$6179b936705e76d3$var$globalIgnoreEmulatedMouseEvents) triggerHoverStart(e, 'mouse');\n                state.ignoreEmulatedMouseEvents = false;\n            };\n            hoverProps.onMouseLeave = (e)=>{\n                if (!isDisabled && e.currentTarget.contains(e.target)) triggerHoverEnd(e, 'mouse');\n            };\n        }\n        return {\n            hoverProps: hoverProps,\n            triggerHoverEnd: triggerHoverEnd\n        };\n    }, [\n        onHoverStart,\n        onHoverChange,\n        onHoverEnd,\n        isDisabled,\n        state\n    ]);\n    (0, $AWxnT$useEffect)(()=>{\n        // Call the triggerHoverEnd as soon as isDisabled changes to true\n        // Safe to call triggerHoverEnd, it will early return if we aren't currently hovering\n        if (isDisabled) triggerHoverEnd({\n            currentTarget: state.target\n        }, state.pointerType);\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [\n        isDisabled\n    ]);\n    return {\n        hoverProps: hoverProps,\n        isHovered: isHovered\n    };\n}\n\n\nexport {$6179b936705e76d3$export$ae780daf29e6d456 as useHover};\n//# sourceMappingURL=useHover.module.js.map\n", "import {isFocusVisible as $isWE5$isFocusVisible, useFocusVisibleListener as $isWE5$useFocusVisibleListener, useFocus as $isWE5$useFocus, useFocusWithin as $isWE5$useFocusWithin} from \"@react-aria/interactions\";\nimport {useRef as $isWE5$useRef, useState as $isWE5$useState, useCallback as $isWE5$useCallback} from \"react\";\n\n\n\nfunction $f7dceffc5ad7768b$export$4e328f61c538687f(props = {}) {\n    let { autoFocus: autoFocus = false, isTextInput: isTextInput, within: within } = props;\n    let state = (0, $isWE5$useRef)({\n        isFocused: false,\n        isFocusVisible: autoFocus || (0, $isWE5$isFocusVisible)()\n    });\n    let [isFocused, setFocused] = (0, $isWE5$useState)(false);\n    let [isFocusVisibleState, setFocusVisible] = (0, $isWE5$useState)(()=>state.current.isFocused && state.current.isFocusVisible);\n    let updateState = (0, $isWE5$useCallback)(()=>setFocusVisible(state.current.isFocused && state.current.isFocusVisible), []);\n    let onFocusChange = (0, $isWE5$useCallback)((isFocused)=>{\n        state.current.isFocused = isFocused;\n        setFocused(isFocused);\n        updateState();\n    }, [\n        updateState\n    ]);\n    (0, $isWE5$useFocusVisibleListener)((isFocusVisible)=>{\n        state.current.isFocusVisible = isFocusVisible;\n        updateState();\n    }, [], {\n        isTextInput: isTextInput\n    });\n    let { focusProps: focusProps } = (0, $isWE5$useFocus)({\n        isDisabled: within,\n        onFocusChange: onFocusChange\n    });\n    let { focusWithinProps: focusWithinProps } = (0, $isWE5$useFocusWithin)({\n        isDisabled: !within,\n        onFocusWithinChange: onFocusChange\n    });\n    return {\n        isFocused: isFocused,\n        isFocusVisible: isFocusVisibleState,\n        focusProps: within ? focusWithinProps : focusProps\n    };\n}\n\n\nexport {$f7dceffc5ad7768b$export$4e328f61c538687f as useFocusRing};\n//# sourceMappingURL=useFocusRing.module.js.map\n", "import n,{createContext as r,useContext as i}from\"react\";let e=r(void 0);function a(){return i(e)}function l({value:t,children:o}){return n.createElement(e.Provider,{value:t},o)}export{l as DisabledProvider,a as useDisabled};\n", "import n,{createContext as d,useContext as i}from\"react\";let e=d(void 0);function u(){return i(e)}function f({id:t,children:r}){return n.createElement(e.Provider,{value:t},r)}export{f as IdProvider,u as useProvidedId};\n", "\"use client\";import m,{createContext as T,useContext as u,useMemo as c,useState as P}from\"react\";import{useEvent as g}from'../../hooks/use-event.js';import{useId as x}from'../../hooks/use-id.js';import{useIsoMorphicEffect as y}from'../../hooks/use-iso-morphic-effect.js';import{useSyncRefs as E}from'../../hooks/use-sync-refs.js';import{useDisabled as v}from'../../internal/disabled.js';import{forwardRefWithAs as R,useRender as I}from'../../utils/render.js';let a=T(null);a.displayName=\"DescriptionContext\";function f(){let r=u(a);if(r===null){let e=new Error(\"You used a <Description /> component, but it is not inside a relevant parent.\");throw Error.captureStackTrace&&Error.captureStackTrace(e,f),e}return r}function U(){var r,e;return(e=(r=u(a))==null?void 0:r.value)!=null?e:void 0}function w(){let[r,e]=P([]);return[r.length>0?r.join(\" \"):void 0,c(()=>function(t){let i=g(n=>(e(s=>[...s,n]),()=>e(s=>{let o=s.slice(),p=o.indexOf(n);return p!==-1&&o.splice(p,1),o}))),l=c(()=>({register:i,slot:t.slot,name:t.name,props:t.props,value:t.value}),[i,t.slot,t.name,t.props,t.value]);return m.createElement(a.Provider,{value:l},t.children)},[e])]}let S=\"p\";function C(r,e){let d=x(),t=v(),{id:i=`headlessui-description-${d}`,...l}=r,n=f(),s=E(e);y(()=>n.register(i),[i,n.register]);let o=t||!1,p=c(()=>({...n.slot,disabled:o}),[n.slot,o]),D={ref:s,...n.props,id:i};return I()({ourProps:D,theirProps:l,slot:p,defaultTag:S,name:n.name||\"Description\"})}let _=R(C),H=Object.assign(_,{});export{H as Description,U as useDescribedBy,w as useDescriptions};\n", "\"use client\";import R,{createContext as k,useContext as h,useMemo as T,useState as D}from\"react\";import{useEvent as v}from'../../hooks/use-event.js';import{useId as _}from'../../hooks/use-id.js';import{useIsoMorphicEffect as A}from'../../hooks/use-iso-morphic-effect.js';import{useSyncRefs as B}from'../../hooks/use-sync-refs.js';import{useDisabled as F}from'../../internal/disabled.js';import{useProvidedId as S}from'../../internal/id.js';import{forwardRefWithAs as M,useRender as H}from'../../utils/render.js';let c=k(null);c.displayName=\"LabelContext\";function P(){let r=h(c);if(r===null){let l=new Error(\"You used a <Label /> component, but it is not inside a relevant parent.\");throw Error.captureStackTrace&&Error.captureStackTrace(l,P),l}return r}function I(r){var a,e,o;let l=(e=(a=h(c))==null?void 0:a.value)!=null?e:void 0;return((o=r==null?void 0:r.length)!=null?o:0)>0?[l,...r].filter(Boolean).join(\" \"):l}function K({inherit:r=!1}={}){let l=I(),[a,e]=D([]),o=r?[l,...a].filter(Boolean):a;return[o.length>0?o.join(\" \"):void 0,T(()=>function(t){let s=v(i=>(e(p=>[...p,i]),()=>e(p=>{let u=p.slice(),d=u.indexOf(i);return d!==-1&&u.splice(d,1),u}))),m=T(()=>({register:s,slot:t.slot,name:t.name,props:t.props,value:t.value}),[s,t.slot,t.name,t.props,t.value]);return R.createElement(c.Provider,{value:m},t.children)},[e])]}let N=\"label\";function G(r,l){var y;let a=_(),e=P(),o=S(),g=F(),{id:t=`headlessui-label-${a}`,htmlFor:s=o!=null?o:(y=e.props)==null?void 0:y.htmlFor,passive:m=!1,...i}=r,p=B(l);A(()=>e.register(t),[t,e.register]);let u=v(L=>{let b=L.currentTarget;if(b instanceof HTMLLabelElement&&L.preventDefault(),e.props&&\"onClick\"in e.props&&typeof e.props.onClick==\"function\"&&e.props.onClick(L),b instanceof HTMLLabelElement){let n=document.getElementById(b.htmlFor);if(n){let E=n.getAttribute(\"disabled\");if(E===\"true\"||E===\"\")return;let x=n.getAttribute(\"aria-disabled\");if(x===\"true\"||x===\"\")return;(n instanceof HTMLInputElement&&(n.type===\"radio\"||n.type===\"checkbox\")||n.role===\"radio\"||n.role===\"checkbox\"||n.role===\"switch\")&&n.click(),n.focus({preventScroll:!0})}}}),d=g||!1,C=T(()=>({...e.slot,disabled:d}),[e.slot,d]),f={ref:p,...e.props,id:t,htmlFor:s,onClick:u};return m&&(\"onClick\"in f&&(delete f.htmlFor,delete f.onClick),\"onClick\"in i&&delete i.onClick),H()({ourProps:f,theirProps:i,slot:C,defaultTag:s?N:\"div\",name:e.name||\"Label\"})}let U=M(G),Q=Object.assign(U,{});export{Q as Label,P as useLabelContext,I as useLabelledBy,K as useLabels};\n", "\"use client\";import{useFocusRing as A}from\"@react-aria/focus\";import{useHover as c}from\"@react-aria/interactions\";import{useMemo as E}from\"react\";import{useId as P}from'../../hooks/use-id.js';import{useDisabled as R}from'../../internal/disabled.js';import{useProvidedId as g}from'../../internal/id.js';import{forwardRefWithAs as v,mergeProps as _,useRender as D}from'../../utils/render.js';import{useDescribedBy as F}from'../description/description.js';import{useLabelledBy as h}from'../label/label.js';let L=\"textarea\";function H(s,l){let i=P(),d=g(),n=R(),{id:p=d||`headlessui-textarea-${i}`,disabled:e=n||!1,autoFocus:r=!1,invalid:a=!1,...T}=s,f=h(),m=F(),{isFocused:o,focusProps:u}=A({autoFocus:r}),{isHovered:t,hoverProps:b}=c({isDisabled:e}),y=_({ref:l,id:p,\"aria-labelledby\":f,\"aria-describedby\":m,\"aria-invalid\":a?\"\":void 0,disabled:e||void 0,autoFocus:r},u,b),x=E(()=>({disabled:e,invalid:a,hover:t,focus:o,autofocus:r}),[e,a,t,o,r]);return D()({ourProps:y,theirProps:T,slot:x,defaultTag:L,name:\"Textarea\"})}let J=v(H);export{J as Textarea};\n"], "names": ["$HgANd$react", "$lmaYr$useRef", "$lmaYr$useCallback", "f", "$6dfIe$useRef", "$6dfIe$useLayoutEffect", "$6dfIe$useEffectEvent", "e", "$6dfIe$useCallback", "$hf0lj$useCallback", "$hf0lj$getOwnerDocument", "$28AnR$isMac", "$28AnR$isVirtualClick", "$28AnR$getOwnerWindow", "$28AnR$getOwnerDocument", "$28AnR$useEffect", "$3b9Q0$useRef", "$3b9Q0$useCallback", "$AWxnT$useState", "$AWxnT$useRef", "$AWxnT$useEffect", "$AWxnT$useMemo", "triggerHoverEnd", "hoverProps", "$isWE5$useRef", "$isWE5$isFocusVisible", "$isWE5$useState", "$isWE5$useCallback", "isFocused", "$isWE5$useFocusVisibleListener", "$isWE5$useFocus", "$isWE5$useFocusWithin", "r", "a", "i", "d", "T", "u", "U", "x", "v", "n", "E", "y", "o", "c", "I", "R", "k", "h", "_", "S", "F", "B", "A", "L", "C", "H", "M", "P", "g", "D"], "mappings": ";;AAaA,MAAM,4CAA4C,OAAO,aAAa,cAAkBA,MAAc,kBAAkB,MAAI;AAAE;ACE9H,SAAS,0CAA0C,IAAI;AACnD,QAAM,MAAUC,aAAa,OAAE,IAAI;AACnC,EAAI,0CAA2C,MAAI;AAC/C,QAAI,UAAU;AAAA,EACtB,GAAO;AAAA,IACC;AAAA,EACR,CAAK;AAED,SAAWC,aAAAA,YAAoB,IAAI,SAAO;AACtC,UAAMC,KAAI,IAAI;AACd,WAAOA,OAAM,QAAQA,OAAM,SAAS,SAASA,GAAE,GAAG,IAAI;AAAA,EACzD,GAAE,EAAE;AACT;AC3BA,MAAM,4CAA4C,CAAC,OAAK;AACpD,MAAI;AACJ,UAAQ,oBAAoB,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,mBAAmB,QAAQ,sBAAsB,SAAS,oBAAoB;AACzJ;AACA,MAAM,4CAA4C,CAAC,OAAK;AACpD,MAAI,MAAM,YAAY,MAAM,GAAG,WAAW,GAAI,QAAO;AACrD,QAAM,MAAM,0CAA0C,EAAE;AACxD,SAAO,IAAI,eAAe;AAC9B;ACEI,SAAS,oCAAoC,IAAI;AACjD,MAAI;AACJ,MAAI,OAAO,WAAW,eAAe,OAAO,aAAa,KAAM,QAAO;AACtE,WAAS,kCAAkC,OAAO,UAAU,eAAe,OAAO,QAAQ,oCAAoC,SAAS,SAAS,gCAAgC,OAAO,KAAK,CAAC,UAAQ,GAAG,KAAK,MAAM,KAAK,CAAC,MAAM,GAAG,KAAK,OAAO,UAAU,SAAS;AACrQ;AACA,SAAS,mCAAmC,IAAI;AAC5C,MAAI;AACJ,SAAO,OAAO,WAAW,eAAe,OAAO,aAAa,OAAO,GAAG,OAAO,kCAAkC,OAAO,UAAU,eAAe,OAAO,QAAQ,oCAAoC,SAAS,SAAS,gCAAgC,aAAa,OAAO,UAAU,QAAQ,IAAI;AAClS;AACA,SAAS,6BAA6B,IAAI;AACtC,MAAI,MAAM;AACV,SAAO,MAAI;AACP,QAAI,OAAO,KAAM,OAAM,GAAI;AAC3B,WAAO;AAAA,EACV;AACL;AACA,MAAM,4CAA4C,6BAA6B,WAAW;AACtF,SAAO,mCAAmC,OAAO;AACrD,CAAC;AAoBD,MAAM,4CAA4C,6BAA6B,WAAW;AACtF,SAAO,oCAAoC,UAAU;AACzD,CAAC;ACrCD,SAAS,0CAA0C,OAAO;AAEtD,MAAI,MAAM,mBAAmB,KAAK,MAAM,UAAW,QAAO;AAI1D,MAAQ,0CAAyC,KAAO,MAAM,YAAa,QAAO,MAAM,SAAS,WAAW,MAAM,YAAY;AAC9H,SAAO,MAAM,WAAW,KAAK,CAAC,MAAM;AACxC;ACNA,MAAM,0CAA0C;AAAA,EAC5C,qBAAqB;AACjB,WAAO,KAAK,YAAY;AAAA,EAChC;AAAA,EACI,iBAAiB;AACb,SAAK,mBAAmB;AACxB,SAAK,YAAY,eAAgB;AAAA,EACzC;AAAA,EACI,kBAAkB;AACd,SAAK,YAAY,gBAAiB;AAClC,SAAK,uBAAuB,MAAI;AAAA,EACxC;AAAA,EACI,uBAAuB;AACnB,WAAO;AAAA,EACf;AAAA,EACI,UAAU;AAAA,EAAA;AAAA,EACV,YAAY,MAAM,aAAY;AAC1B,SAAK,cAAc;AACnB,SAAK,SAAS,YAAY;AAC1B,SAAK,gBAAgB,YAAY;AACjC,SAAK,gBAAgB,YAAY;AACjC,SAAK,UAAU,YAAY;AAC3B,SAAK,aAAa,YAAY;AAC9B,SAAK,mBAAmB,YAAY;AACpC,SAAK,aAAa,YAAY;AAC9B,SAAK,YAAY,YAAY;AAC7B,SAAK,YAAY,YAAY;AAC7B,SAAK,OAAO;AAAA,EACpB;AACA;AACA,SAAS,0CAA0C,QAAQ;AACvD,MAAI,WAAeC,oBAAe;AAAA,IAC9B,WAAW;AAAA,IACX,UAAU;AAAA,EAClB,CAAK;AAED,EAAIC,0CAAwB,MAAI;AAC5B,UAAM,QAAQ,SAAS;AACvB,WAAO,MAAI;AACP,UAAI,MAAM,UAAU;AAChB,cAAM,SAAS,WAAY;AAC3B,cAAM,WAAW;AAAA,MACjC;AAAA,IACS;AAAA,EACJ,GAAE,EAAE;AACL,MAAI,eAAmBC,0CAAuB,CAACC,OAAI;AAC/C,eAAW,QAAQ,WAAW,SAAS,SAAS,OAAOA,EAAC;AAAA,EAChE,CAAK;AAED,SAAWC,aAAAA,YAAoB,CAACD,OAAI;AAKhC,QAAIA,GAAE,kBAAkB,qBAAqBA,GAAE,kBAAkB,oBAAoBA,GAAE,kBAAkB,uBAAuBA,GAAE,kBAAkB,mBAAmB;AACnK,eAAS,QAAQ,YAAY;AAC7B,UAAI,SAASA,GAAE;AACf,UAAI,gBAAgB,CAACA,OAAI;AACrB,iBAAS,QAAQ,YAAY;AAC7B,YAAI,OAAO;AACX,uBAAa,IAAI,0CAA0C,QAAQA,EAAC,CAAC;AAErE,YAAI,SAAS,QAAQ,UAAU;AAC3B,mBAAS,QAAQ,SAAS,WAAY;AACtC,mBAAS,QAAQ,WAAW;AAAA,QAChD;AAAA,MACa;AACD,aAAO,iBAAiB,YAAY,eAAe;AAAA,QAC/C,MAAM;AAAA,MACtB,CAAa;AACD,eAAS,QAAQ,WAAW,IAAI,iBAAiB,MAAI;AACjD,YAAI,SAAS,QAAQ,aAAa,OAAO,UAAU;AAC/C,cAAI;AACJ,WAAC,6BAA6B,SAAS,QAAQ,cAAc,QAAQ,+BAA+B,SAAS,SAAS,2BAA2B,WAAY;AAC7J,cAAI,kBAAkB,WAAW,SAAS,gBAAgB,OAAO,SAAS;AAC1E,iBAAO,cAAc,IAAI,WAAW,QAAQ;AAAA,YACxC,eAAe;AAAA,UACvC,CAAqB,CAAC;AACF,iBAAO,cAAc,IAAI,WAAW,YAAY;AAAA,YAC5C,SAAS;AAAA,YACT,eAAe;AAAA,UACvC,CAAqB,CAAC;AAAA,QACtB;AAAA,MACA,CAAa;AACD,eAAS,QAAQ,SAAS,QAAQ,QAAQ;AAAA,QACtC,YAAY;AAAA,QACZ,iBAAiB;AAAA,UACb;AAAA,QACpB;AAAA,MACA,CAAa;AAAA,IACb;AAAA,EACA,GAAO;AAAA,IACC;AAAA,EACR,CAAK;AACL;ACxFA,SAAS,0CAA0C,OAAO;AACtD,MAAI,EAAE,YAAwB,SAAS,aAAa,QAAQ,YAAY,cAA4B,IAAK;AACzG,QAAM,SAAaE,yBAAoB,CAACF,OAAI;AACxC,QAAIA,GAAE,WAAWA,GAAE,eAAe;AAC9B,UAAI,WAAY,YAAWA,EAAC;AAC5B,UAAI,cAAe,eAAc,KAAK;AACtC,aAAO;AAAA,IACnB;AAAA,EACA,GAAO;AAAA,IACC;AAAA,IACA;AAAA,EACR,CAAK;AACD,QAAM,mBAAuB,0CAA2C,MAAM;AAC9E,QAAM,UAAcE,yBAAoB,CAACF,OAAI;AAGzC,UAAM,gBAAoBG,0CAAyBH,GAAE,MAAM;AAC3D,QAAIA,GAAE,WAAWA,GAAE,iBAAiB,cAAc,kBAAkBA,GAAE,QAAQ;AAC1E,UAAI,YAAa,aAAYA,EAAC;AAC9B,UAAI,cAAe,eAAc,IAAI;AACrC,uBAAiBA,EAAC;AAAA,IAC9B;AAAA,EACA,GAAO;AAAA,IACC;AAAA,IACA;AAAA,IACA;AAAA,EACR,CAAK;AACD,SAAO;AAAA,IACH,YAAY;AAAA,MACR,SAAS,CAAC,eAAe,eAAe,iBAAiB,cAAc,UAAU;AAAA,MACjF,QAAQ,CAAC,eAAe,cAAc,iBAAiB,SAAS;AAAA,IAC5E;AAAA,EACK;AACL;ACjCA,IAAI,wCAAwC;AAC5C,IAAI,uCAAuC,oBAAI,IAAK;AACpD,IAAI,4CAA4C,oBAAI;AACpD,IAAI,4CAA4C;AAChD,IAAI,iDAAiD;AAErD,MAAM,iDAAiD;AAAA,EACnD,KAAK;AAAA,EACL,QAAQ;AACZ;AACA,SAAS,4CAA4C,UAAUA,IAAG;AAC9D,WAAS,WAAW,qCAAqC,SAAQ,UAAUA,EAAC;AAChF;AAGI,SAAS,iCAAiCA,IAAG;AAE7C,SAAO,EAAEA,GAAE,WAAW,CAAKI,0CAAY,KAAOJ,GAAE,UAAUA,GAAE,WAAWA,GAAE,QAAQ,aAAaA,GAAE,QAAQ,WAAWA,GAAE,QAAQ;AACjI;AACA,SAAS,0CAA0CA,IAAG;AAClD,8CAA4C;AAC5C,MAAI,iCAAiCA,EAAC,GAAG;AACrC,4CAAwC;AACxC,gDAA4C,YAAYA,EAAC;AAAA,EACjE;AACA;AACA,SAAS,yCAAyCA,IAAG;AACjD,0CAAwC;AACxC,MAAIA,GAAE,SAAS,eAAeA,GAAE,SAAS,eAAe;AACpD,gDAA4C;AAC5C,gDAA4C,WAAWA,EAAC;AAAA,EAChE;AACA;AACA,SAAS,uCAAuCA,IAAG;AAC/C,MAAQK,0CAAuBL,EAAC,GAAG;AAC/B,gDAA4C;AAC5C,4CAAwC;AAAA,EAChD;AACA;AACA,SAAS,uCAAuCA,IAAG;AAI/C,MAAIA,GAAE,WAAW,UAAUA,GAAE,WAAW,SAAU;AAGlD,MAAI,CAAC,6CAA6C,CAAC,gDAAgD;AAC/F,4CAAwC;AACxC,gDAA4C,WAAWA,EAAC;AAAA,EAChE;AACI,8CAA4C;AAC5C,mDAAiD;AACrD;AACA,SAAS,yCAAyC;AAG9C,8CAA4C;AAC5C,mDAAiD;AACrD;AAGI,SAAS,6CAA6C,SAAS;AAC/D,MAAI,OAAO,WAAW,eAAe,0CAA0C,IAAQM,0CAAuB,OAAO,CAAC,EAAG;AACzH,QAAM,eAAmBA,0CAAuB,OAAO;AACvD,QAAM,iBAAqBC,0CAAyB,OAAO;AAK3D,MAAI,QAAQ,aAAa,YAAY,UAAU;AAC/C,eAAa,YAAY,UAAU,QAAQ,WAAW;AAClD,gDAA4C;AAC5C,UAAM,MAAM,MAAM,SAAS;AAAA,EAC9B;AACD,iBAAe,iBAAiB,WAAW,2CAA2C,IAAI;AAC1F,iBAAe,iBAAiB,SAAS,2CAA2C,IAAI;AACxF,iBAAe,iBAAiB,SAAS,wCAAwC,IAAI;AAGrF,eAAa,iBAAiB,SAAS,wCAAwC,IAAI;AACnF,eAAa,iBAAiB,QAAQ,wCAAwC,KAAK;AACnF,MAAI,OAAO,iBAAiB,aAAa;AACrC,mBAAe,iBAAiB,eAAe,0CAA0C,IAAI;AAC7F,mBAAe,iBAAiB,eAAe,0CAA0C,IAAI;AAC7F,mBAAe,iBAAiB,aAAa,0CAA0C,IAAI;AAAA,EACnG,OAAW;AACH,mBAAe,iBAAiB,aAAa,0CAA0C,IAAI;AAC3F,mBAAe,iBAAiB,aAAa,0CAA0C,IAAI;AAC3F,mBAAe,iBAAiB,WAAW,0CAA0C,IAAI;AAAA,EACjG;AAEI,eAAa,iBAAiB,gBAAgB,MAAI;AAC9C,sDAAkD,OAAO;AAAA,EACjE,GAAO;AAAA,IACC,MAAM;AAAA,EACd,CAAK;AACD,4CAA0C,IAAI,cAAc;AAAA,IACxD;AAAA,EACR,CAAK;AACL;AACA,MAAM,oDAAoD,CAAC,SAAS,iBAAe;AAC/E,QAAM,eAAmBD,0CAAuB,OAAO;AACvD,QAAM,iBAAqBC,0CAAyB,OAAO;AAC3D,MAAI,aAAc,gBAAe,oBAAoB,oBAAoB,YAAY;AACrF,MAAI,CAAC,0CAA0C,IAAI,YAAY,EAAG;AAClE,eAAa,YAAY,UAAU,QAAQ,0CAA0C,IAAI,YAAY,EAAE;AACvG,iBAAe,oBAAoB,WAAW,2CAA2C,IAAI;AAC7F,iBAAe,oBAAoB,SAAS,2CAA2C,IAAI;AAC3F,iBAAe,oBAAoB,SAAS,wCAAwC,IAAI;AACxF,eAAa,oBAAoB,SAAS,wCAAwC,IAAI;AACtF,eAAa,oBAAoB,QAAQ,wCAAwC,KAAK;AACtF,MAAI,OAAO,iBAAiB,aAAa;AACrC,mBAAe,oBAAoB,eAAe,0CAA0C,IAAI;AAChG,mBAAe,oBAAoB,eAAe,0CAA0C,IAAI;AAChG,mBAAe,oBAAoB,aAAa,0CAA0C,IAAI;AAAA,EACtG,OAAW;AACH,mBAAe,oBAAoB,aAAa,0CAA0C,IAAI;AAC9F,mBAAe,oBAAoB,aAAa,0CAA0C,IAAI;AAC9F,mBAAe,oBAAoB,WAAW,0CAA0C,IAAI;AAAA,EACpG;AACI,4CAA0C,OAAO,YAAY;AACjE;AACA,SAAS,0CAA0C,SAAS;AACxD,QAAM,iBAAqBA,0CAAyB,OAAO;AAC3D,MAAI;AACJ,MAAI,eAAe,eAAe,UAAW,8CAA6C,OAAO;AAAA,OAC5F;AACD,mBAAe,MAAI;AACf,mDAA6C,OAAO;AAAA,IACvD;AACD,mBAAe,iBAAiB,oBAAoB,YAAY;AAAA,EACxE;AACI,SAAO,MAAI,kDAAkD,SAAS,YAAY;AACtF;AAGA,IAAI,OAAO,aAAa,YAAa,2CAA2C;AAChF,SAAS,4CAA4C;AACjD,SAAO,0CAA0C;AACrD;AAsBA,MAAM,0CAA0C,oBAAI,IAAI;AAAA,EACpD;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACJ,CAAC;AAIG,SAAS,2CAA2C,aAAa,UAAUP,IAAG;AAC9E,MAAI;AACJ,QAAM,oBAAoB,OAAO,WAAW,cAAkBM,0CAAuBN,OAAM,QAAQA,OAAM,SAAS,SAASA,GAAE,MAAM,EAAE,mBAAmB;AACxJ,QAAM,uBAAuB,OAAO,WAAW,cAAkBM,0CAAuBN,OAAM,QAAQA,OAAM,SAAS,SAASA,GAAE,MAAM,EAAE,sBAAsB;AAC9J,QAAM,eAAe,OAAO,WAAW,cAAkBM,0CAAuBN,OAAM,QAAQA,OAAM,SAAS,SAASA,GAAE,MAAM,EAAE,cAAc;AAC9I,QAAM,iBAAiB,OAAO,WAAW,cAAkBM,0CAAuBN,OAAM,QAAQA,OAAM,SAAS,SAASA,GAAE,MAAM,EAAE,gBAAgB;AAClJ,gBAAc,gBAAgBA,OAAM,QAAQA,OAAM,SAAS,SAASA,GAAE,mBAAmB,qBAAqB,CAAC,wCAAwC,IAAIA,OAAM,QAAQA,OAAM,SAAS,UAAU,YAAYA,GAAE,YAAY,QAAQ,cAAc,SAAS,SAAS,UAAU,IAAI,MAAMA,OAAM,QAAQA,OAAM,SAAS,SAASA,GAAE,mBAAmB,yBAAyBA,OAAM,QAAQA,OAAM,SAAS,SAASA,GAAE,mBAAmB,iBAAiBA,OAAM,QAAQA,OAAM,SAAS,SAASA,GAAE,OAAO;AACve,SAAO,EAAE,eAAe,aAAa,cAAcA,cAAa,kBAAkB,CAAC,+CAA+CA,GAAE,GAAG;AAC3I;AAeA,SAAS,0CAA0C,IAAI,MAAM,MAAM;AAC/D,+CAA8C;AAC9C,EAAIQ,aAAgB,UAAE,MAAI;AACtB,QAAI,UAAU,CAAC,UAAUR,OAAI;AACzB,UAAI,CAAC,2CAA2C,CAAC,EAAE,SAAS,QAAQ,SAAS,SAAS,SAAS,KAAK,cAAc,UAAUA,EAAC,EAAG;AAChI,SAAG,0CAAyC,CAAE;AAAA,IACjD;AACD,yCAAqC,IAAI,OAAO;AAChD,WAAO,MAAI;AACP,2CAAqC,OAAO,OAAO;AAAA,IACtD;AAAA,EAEJ,GAAE,IAAI;AACX;ACrNA,SAAS,0CAA0C,OAAO;AACtD,MAAI,EAAE,YAAwB,cAA4B,eAA8B,oBAAwC,IAAK;AACrI,MAAI,QAAYS,oBAAe;AAAA,IAC3B,eAAe;AAAA,EACvB,CAAK;AACD,MAAI,SAAaC,yBAAoB,CAACV,OAAI;AAItC,QAAI,MAAM,QAAQ,iBAAiB,CAACA,GAAE,cAAc,SAASA,GAAE,aAAa,GAAG;AAC3E,YAAM,QAAQ,gBAAgB;AAC9B,UAAI,aAAc,cAAaA,EAAC;AAChC,UAAI,oBAAqB,qBAAoB,KAAK;AAAA,IAC9D;AAAA,EACA,GAAO;AAAA,IACC;AAAA,IACA;AAAA,IACA;AAAA,EACR,CAAK;AACD,MAAI,mBAAuB,0CAA2C,MAAM;AAC5E,MAAI,UAAcU,yBAAoB,CAACV,OAAI;AAGvC,QAAI,CAAC,MAAM,QAAQ,iBAAiB,SAAS,kBAAkBA,GAAE,QAAQ;AACrE,UAAI,cAAe,eAAcA,EAAC;AAClC,UAAI,oBAAqB,qBAAoB,IAAI;AACjD,YAAM,QAAQ,gBAAgB;AAC9B,uBAAiBA,EAAC;AAAA,IAC9B;AAAA,EACA,GAAO;AAAA,IACC;AAAA,IACA;AAAA,IACA;AAAA,EACR,CAAK;AACD,MAAI,WAAY,QAAO;AAAA,IACnB,kBAAkB;AAAA;AAAA,MAEd,SAAS;AAAA,MACT,QAAQ;AAAA,IACpB;AAAA,EACK;AACD,SAAO;AAAA,IACH,kBAAkB;AAAA,MACd;AAAA,MACA;AAAA,IACZ;AAAA,EACK;AACL;AC9CA,IAAI,wDAAwD;AAC5D,IAAI,mCAAmC;AACvC,SAAS,2DAA2D;AAChE,0DAAwD;AAKxD,aAAW,MAAI;AACX,4DAAwD;AAAA,EAC3D,GAAE,EAAE;AACT;AACA,SAAS,+CAA+CA,IAAG;AACvD,MAAIA,GAAE,gBAAgB,QAAS,0DAA0D;AAC7F;AACA,SAAS,+CAA+C;AACpD,MAAI,OAAO,aAAa,YAAa;AACrC,MAAI,OAAO,iBAAiB,YAAa,UAAS,iBAAiB,aAAa,8CAA8C;AAAA,MACzH,UAAS,iBAAiB,YAAY,wDAAwD;AACnG;AACA,SAAO,MAAI;AACP;AACA,QAAI,mCAAmC,EAAG;AAC1C,QAAI,OAAO,iBAAiB,YAAa,UAAS,oBAAoB,aAAa,8CAA8C;AAAA,QAC5H,UAAS,oBAAoB,YAAY,wDAAwD;AAAA,EACzG;AACL;AACA,SAAS,0CAA0C,OAAO;AACtD,MAAI,EAAE,cAA4B,eAA8B,YAAwB,WAAsB,IAAK;AACnH,MAAI,CAAC,WAAW,UAAU,IAAQW,aAAAA,SAAiB,KAAK;AACxD,MAAI,QAAYC,oBAAe;AAAA,IAC3B,WAAW;AAAA,IACX,2BAA2B;AAAA,IAC3B,aAAa;AAAA,IACb,QAAQ;AAAA,EACX,CAAA,EAAE;AACH,EAAIC,aAAgB,UAAE,8CAA8C,EAAE;AACtE,MAAI,EAAE,YAAwB,gBAAkC,IAAOC,aAAAA,QAAgB,MAAI;AACvF,QAAI,oBAAoB,CAAC,OAAO,gBAAc;AAC1C,YAAM,cAAc;AACpB,UAAI,cAAc,gBAAgB,WAAW,MAAM,aAAa,CAAC,MAAM,cAAc,SAAS,MAAM,MAAM,EAAG;AAC7G,YAAM,YAAY;AAClB,UAAI,SAAS,MAAM;AACnB,YAAM,SAAS;AACf,UAAI,aAAc,cAAa;AAAA,QAC3B,MAAM;AAAA,QACN;AAAA,QACA;AAAA,MAChB,CAAa;AACD,UAAI,cAAe,eAAc,IAAI;AACrC,iBAAW,IAAI;AAAA,IAClB;AACD,QAAIC,mBAAkB,CAAC,OAAO,gBAAc;AACxC,YAAM,cAAc;AACpB,YAAM,SAAS;AACf,UAAI,gBAAgB,WAAW,CAAC,MAAM,UAAW;AACjD,YAAM,YAAY;AAClB,UAAI,SAAS,MAAM;AACnB,UAAI,WAAY,YAAW;AAAA,QACvB,MAAM;AAAA,QACN;AAAA,QACA;AAAA,MAChB,CAAa;AACD,UAAI,cAAe,eAAc,KAAK;AACtC,iBAAW,KAAK;AAAA,IACnB;AACD,QAAIC,cAAa,CAAE;AACnB,QAAI,OAAO,iBAAiB,aAAa;AACrC,MAAAA,YAAW,iBAAiB,CAAChB,OAAI;AAC7B,YAAI,yDAAyDA,GAAE,gBAAgB,QAAS;AACxF,0BAAkBA,IAAGA,GAAE,WAAW;AAAA,MACrC;AACD,MAAAgB,YAAW,iBAAiB,CAAChB,OAAI;AAC7B,YAAI,CAAC,cAAcA,GAAE,cAAc,SAASA,GAAE,MAAM,EAAG,CAAAe,iBAAgBf,IAAGA,GAAE,WAAW;AAAA,MAC1F;AAAA,IACb,OAAe;AACH,MAAAgB,YAAW,eAAe,MAAI;AAC1B,cAAM,4BAA4B;AAAA,MACrC;AACD,MAAAA,YAAW,eAAe,CAAChB,OAAI;AAC3B,YAAI,CAAC,MAAM,6BAA6B,CAAC,sDAAuD,mBAAkBA,IAAG,OAAO;AAC5H,cAAM,4BAA4B;AAAA,MACrC;AACD,MAAAgB,YAAW,eAAe,CAAChB,OAAI;AAC3B,YAAI,CAAC,cAAcA,GAAE,cAAc,SAASA,GAAE,MAAM,EAAG,CAAAe,iBAAgBf,IAAG,OAAO;AAAA,MACpF;AAAA,IACb;AACQ,WAAO;AAAA,MACH,YAAYgB;AAAA,MACZ,iBAAiBD;AAAA,IACpB;AAAA,EACT,GAAO;AAAA,IACC;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACR,CAAK;AACD,EAAIF,aAAgB,UAAE,MAAI;AAGtB,QAAI,WAAY,iBAAgB;AAAA,MAC5B,eAAe,MAAM;AAAA,IACjC,GAAW,MAAM,WAAW;AAAA,EAE5B,GAAO;AAAA,IACC;AAAA,EACR,CAAK;AACD,SAAO;AAAA,IACH;AAAA,IACA;AAAA,EACH;AACL;AC/HA,SAAS,0CAA0C,QAAQ,IAAI;AAC3D,MAAI,EAAE,YAAuB,OAAO,aAA0B,OAAc,IAAK;AACjF,MAAI,QAAYI,oBAAe;AAAA,IAC3B,WAAW;AAAA,IACX,gBAAgB,aAAiBC,0CAAqB;AAAA,EAC9D,CAAK;AACD,MAAI,CAAC,WAAW,UAAU,IAAQC,aAAAA,SAAiB,KAAK;AACxD,MAAI,CAAC,qBAAqB,eAAe,IAAQA,aAAAA,SAAiB,MAAI,MAAM,QAAQ,aAAa,MAAM,QAAQ,cAAc;AAC7H,MAAI,cAAkBC,yBAAoB,MAAI,gBAAgB,MAAM,QAAQ,aAAa,MAAM,QAAQ,cAAc,GAAG,CAAA,CAAE;AAC1H,MAAI,gBAAoBA,yBAAoB,CAACC,eAAY;AACrD,UAAM,QAAQ,YAAYA;AAC1B,eAAWA,UAAS;AACpB,gBAAa;AAAA,EACrB,GAAO;AAAA,IACC;AAAA,EACR,CAAK;AACD,EAAIC,0CAAgC,CAAC,mBAAiB;AAClD,UAAM,QAAQ,iBAAiB;AAC/B,gBAAa;AAAA,EAChB,GAAE,IAAI;AAAA,IACH;AAAA,EACR,CAAK;AACD,MAAI,EAAE,WAAwB,IAAOC,0CAAiB;AAAA,IAClD,YAAY;AAAA,IACZ;AAAA,EACR,CAAK;AACD,MAAI,EAAE,iBAAoC,IAAOC,0CAAuB;AAAA,IACpE,YAAY,CAAC;AAAA,IACb,qBAAqB;AAAA,EAC7B,CAAK;AACD,SAAO;AAAA,IACH;AAAA,IACA,gBAAgB;AAAA,IAChB,YAAY,SAAS,mBAAmB;AAAA,EAC3C;AACL;ACxCyD,IAAIxB,MAAEyB,aAAC,cAAC,MAAM;AAAE,SAASC,MAAG;AAAC,SAAOC,aAAC,WAAC3B,GAAC;AAAC;ACAxC,IAAI,IAAE4B,aAAC,cAAC,MAAM;AAAE,SAAS,IAAG;AAAC,SAAOD,aAAC,WAAC,CAAC;AAAC;ACA0W,IAAI,IAAEE,aAAC,cAAC,IAAI;AAAE,EAAE,cAAY;AAAqB,SAAS,IAAG;AAAC,MAAI,IAAEC,aAAC,WAAC,CAAC;AAAE,MAAG,MAAI,MAAK;AAAC,QAAI9B,KAAE,IAAI,MAAM,+EAA+E;AAAE,UAAM,MAAM,qBAAmB,MAAM,kBAAkBA,IAAE,CAAC,GAAEA;AAAA,EAAC;AAAC,SAAO;AAAC;AAAC,SAAS+B,MAAG;AAAC,MAAI,GAAE/B;AAAE,UAAOA,MAAG,IAAE8B,aAAAA,WAAE,CAAC,MAAI,OAAK,SAAO,EAAE,UAAQ,OAAK9B,KAAE;AAAM;AAAwW,IAAI,IAAE;AAAI,SAAS,EAAE,GAAEA,IAAE;AAAC,MAAI,IAAEgC,aAAAA,SAAI,IAAEC,IAAG,GAAC,EAAC,IAAG,IAAE,0BAA0B,CAAC,IAAG,GAAG,EAAC,IAAE,GAAEC,MAAE,EAAG,GAAC,IAAEC,EAAEnC,EAAC;AAAEoC,IAAE,MAAIF,IAAE,SAAS,CAAC,GAAE,CAAC,GAAEA,IAAE,QAAQ,CAAC;AAAE,MAAIG,KAAE,KAAG,OAAG,IAAEC,aAAC,QAAC,OAAK,EAAC,GAAGJ,IAAE,MAAK,UAASG,GAAC,IAAG,CAACH,IAAE,MAAKG,EAAC,CAAC,GAAE,IAAE,EAAC,KAAI,GAAE,GAAGH,IAAE,OAAM,IAAG,EAAC;AAAE,SAAOK,IAAG,EAAC,EAAC,UAAS,GAAE,YAAW,GAAE,MAAK,GAAE,YAAW,GAAE,MAAKL,IAAE,QAAM,cAAa,CAAC;AAAC;AAAI,IAAC,IAAEM,EAAE,CAAC;AAAI,OAAO,OAAO,GAAE,CAAE,CAAA;ACA18B,IAAI,IAAEC,aAAC,cAAC,IAAI;AAAE,EAAE,cAAY;AAAe,SAAS,IAAG;AAAC,MAAI,IAAEC,aAAAA,WAAE,CAAC;AAAE,MAAG,MAAI,MAAK;AAAC,QAAI,IAAE,IAAI,MAAM,yEAAyE;AAAE,UAAM,MAAM,qBAAmB,MAAM,kBAAkB,GAAE,CAAC,GAAE;AAAA,EAAC;AAAC,SAAO;AAAC;AAAC,SAAS,EAAE,GAAE;AAAC,MAAIhB,IAAE1B,IAAEqC;AAAE,MAAI,KAAGrC,MAAG0B,KAAEgB,wBAAE,CAAC,MAAI,OAAK,SAAOhB,GAAE,UAAQ,OAAK1B,KAAE;AAAO,WAAQqC,KAAU,WAAkB,OAAKA,KAAE,KAAG,IAAE,CAAC,GAAE,GAAG,CAAC,EAAE,OAAO,OAAO,EAAE,KAAK,GAAG,IAAE;AAAC;AAA+Z,IAAI,IAAE;AAAQ,SAAS,EAAE,GAAE,GAAE;AAAC,MAAID;AAAE,MAAIV,KAAEiB,aAAC,MAAA,GAAG3C,KAAE,EAAG,GAACqC,MAAEO,EAAC,GAAG,IAAEC,IAAC,GAAG,EAAC,IAAG,IAAE,oBAAoBnB,EAAC,IAAG,SAAQ,IAAEW,OAAG,OAAKA,OAAGD,MAAEpC,GAAE,UAAQ,OAAK,SAAOoC,IAAE,SAAQ,SAAQ,IAAE,OAAG,GAAG,EAAC,IAAE,GAAE,IAAEU,EAAE,CAAC;AAAEC,IAAE,MAAI/C,GAAE,SAAS,CAAC,GAAE,CAAC,GAAEA,GAAE,QAAQ,CAAC;AAAE,MAAI8B,MAAEG,EAAE,CAAAe,OAAG;AAAC,QAAI,IAAEA,GAAE;AAAc,QAAG,aAAa,oBAAkBA,GAAE,eAAgB,GAAChD,GAAE,SAAO,aAAYA,GAAE,SAAO,OAAOA,GAAE,MAAM,WAAS,cAAYA,GAAE,MAAM,QAAQgD,EAAC,GAAE,aAAa,kBAAiB;AAAC,UAAId,KAAE,SAAS,eAAe,EAAE,OAAO;AAAE,UAAGA,IAAE;AAAC,YAAI,IAAEA,GAAE,aAAa,UAAU;AAAE,YAAG,MAAI,UAAQ,MAAI,GAAG;AAAO,YAAI,IAAEA,GAAE,aAAa,eAAe;AAAE,YAAG,MAAI,UAAQ,MAAI,GAAG;AAAO,SAACA,cAAa,qBAAmBA,GAAE,SAAO,WAASA,GAAE,SAAO,eAAaA,GAAE,SAAO,WAASA,GAAE,SAAO,cAAYA,GAAE,SAAO,aAAWA,GAAE,MAAK,GAAGA,GAAE,MAAM,EAAC,eAAc,KAAE,CAAC;AAAA,MAAC;AAAA,IAAC;AAAA,EAAC,CAAC,GAAE,IAAE,KAAG,OAAGe,KAAEpB,aAAC,QAAC,OAAK,EAAC,GAAG7B,GAAE,MAAK,UAAS,EAAC,IAAG,CAACA,GAAE,MAAK,CAAC,CAAC,GAAEJ,KAAE,EAAC,KAAI,GAAE,GAAGI,GAAE,OAAM,IAAG,GAAE,SAAQ,GAAE,SAAQ8B,IAAC;AAAE,SAAO,MAAI,aAAYlC,OAAI,OAAOA,GAAE,SAAQ,OAAOA,GAAE,UAAS,aAAY,KAAG,OAAO,EAAE,UAASsD,IAAG,EAAC,EAAC,UAAStD,IAAE,YAAW,GAAE,MAAKqD,IAAE,YAAW,IAAE,IAAE,OAAM,MAAKjD,GAAE,QAAM,QAAO,CAAC;AAAC;AAAI,IAAC,IAAEmD,EAAE,CAAC;AAAI,OAAO,OAAO,GAAE,CAAE,CAAA;ACA52D,IAAI,IAAE;AAAW,SAAS,EAAE,GAAE,GAAE;AAAC,MAAI,IAAEC,aAAC,MAAA,GAAG,IAAEC,EAAG,GAACnB,KAAEM,IAAC,GAAG,EAAC,IAAG,IAAE,KAAG,uBAAuB,CAAC,IAAG,UAASxC,KAAEkC,MAAG,OAAG,WAAU,IAAE,OAAG,SAAQR,KAAE,OAAG,GAAG,EAAC,IAAE,GAAE9B,KAAE8C,EAAC,GAAG,IAAEG,IAAC,GAAG,EAAC,WAAUR,IAAE,YAAWP,IAAC,IAAEiB,0CAAE,EAAC,WAAU,EAAC,CAAC,GAAE,EAAC,WAAU,GAAE,YAAW,EAAC,IAAET,0CAAE,EAAC,YAAWtC,GAAC,CAAC,GAAEoC,KAAEO,IAAE,EAAC,KAAI,GAAE,IAAG,GAAE,mBAAkB/C,IAAE,oBAAmB,GAAE,gBAAe8B,KAAE,KAAG,QAAO,UAAS1B,MAAG,QAAO,WAAU,EAAC,GAAE8B,KAAE,CAAC,GAAE,IAAEK,aAAAA,QAAE,OAAK,EAAC,UAASnC,IAAE,SAAQ0B,IAAE,OAAM,GAAE,OAAMW,IAAE,WAAU,EAAC,IAAG,CAACrC,IAAE0B,IAAE,GAAEW,IAAE,CAAC,CAAC;AAAE,SAAOiB,IAAG,EAAC,EAAC,UAASlB,IAAE,YAAW,GAAE,MAAK,GAAE,YAAW,GAAE,MAAK,WAAU,CAAC;AAAC;AAAI,IAAC,IAAEH,EAAE,CAAC;", "x_google_ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]}