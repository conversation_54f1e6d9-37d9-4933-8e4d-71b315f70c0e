{"version": 3, "file": "home.liveOrderDashBoard-Bk76UvjB.js", "sources": ["../../../app/routes/home.liveOrderDashBoard.tsx"], "sourcesContent": ["import type React from \"react\"\r\n\r\nimport { useState, useEffect, useMemo, useCallback } from \"react\"\r\nimport { Badge } from \"~/components/ui/badge\"\r\nimport { But<PERSON> } from \"~/components/ui/button\"\r\nimport { Card, CardContent } from \"~/components/ui/card\"\r\nimport { Input } from \"~/components/ui/input\"\r\nimport { Label } from \"~/components/ui/label\"\r\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from \"~/components/ui/select\"\r\nimport { Clock, AlertCircle, Search, Filter, Phone, Store, Truck, MapPin, User, Eye, Package, AlertTriangle, MessageCircleQuestionIcon, RefreshCw, ArrowRight, CalendarIcon, Timer } from \"lucide-react\"\r\nimport { usePolling } from \"~/hooks/usePolling\"\r\nimport type { rNETOrder, OrderStatus, LogisticStatus } from \"~/types/api/businessConsoleService/rNETOrder\"\r\nimport { Dialog, DialogContent, DialogHeader, DialogTitle } from \"~/components/ui/dialog\"\r\nimport { Separator } from \"~/components/ui/separator\"\r\nimport { Textarea } from \"@headlessui/react\"\r\nimport { json, ActionFunction } from \"@remix-run/node\"\r\nimport { useFetcher } from \"@remix-run/react\"\r\nimport { withAuth, withResponse } from \"~/utils/auth-utils\"\r\nimport { getrNETOrders, cancelrNETOrder, updateLiveOrderStatus, markDelivered, updateLogisticProvider, updateLogisticDetails } from \"~/services/rNETOrders\"\r\nimport dayjs from \"dayjs\"\r\nimport { useToast } from \"~/hooks/use-toast\"\r\nimport { DateRange } from \"react-day-picker\"\r\nimport { Popover, PopoverContent, PopoverTrigger, PopoverClose } from \"~/components/ui/popover\"\r\nimport { cn } from \"lib/utils\"\r\nimport { format } from \"date-fns\"\r\nimport { Calendar } from \"~/components/ui/calendar\"\r\n\r\ntype ActionIntent = \"Fetch Orders\" | \"Cancel Order\" | \"Mark Delivered\" | \"Update LiveOrder Status\" | \"Manual Fulfillment\";\r\n\r\ninterface OrderStatusCount {\r\n  status: string;\r\n  count: number;\r\n}\r\n\r\ninterface ActionData {\r\n  intent: ActionIntent;\r\n  errorMessage: string;\r\n  success: boolean;\r\n  data: { orders: rNETOrder[], totalElements: number, pageSize: number, currentPage: number, orderStatusCounts: OrderStatusCount[] };\r\n}\r\nexport const action: ActionFunction = withAuth(async ({ request }) => {\r\n  const formData = await request.formData();\r\n  const intent = formData.get(\"intent\") as ActionIntent;\r\n  const data = formData.get(\"data\") as any;\r\n\r\n  if (!intent) {\r\n    return json({ success: false, errorMessage: \"Invalid request\", intent: intent }, { status: 400 });\r\n  }\r\n  if (intent === \"Fetch Orders\") {\r\n    const { searchType, searchTerm, filterDate, businessType, filterLogisticProvider, filterStatus, filterLogisticStatus, pageSize, currentPage } = JSON.parse(data);\r\n\r\n    // Convert dates to IST (UTC+5:30)\r\n    const fromDate = filterDate.from\r\n      ? dayjs(filterDate.from).startOf('day').add(5, 'hours').add(30, 'minutes').toISOString()\r\n      : dayjs().startOf('day').add(5, 'hours').add(30, 'minutes').toISOString();\r\n    const toDate = filterDate.to\r\n      ? dayjs(filterDate.to).endOf(\"day\").add(5, 'hours').add(30, 'minutes').toISOString()\r\n      : filterDate.from ? dayjs(filterDate.from).endOf(\"day\").add(5, 'hours').add(30, 'minutes').toISOString() : dayjs().endOf(\"day\").add(5, 'hours').add(30, 'minutes').toISOString();\r\n    const searchQuery = searchType === \"OrderID\" ? `&orderGroupId=${searchTerm}` : searchType === \"SellerID\" ? `&sellerId=${searchTerm}` : searchType === \"BuyerID\" ? `&buyerId=${searchTerm}` : searchType === \"SellerMobile\" ? `&sellerMobile=${searchTerm}` : searchType === \"BuyerMobile\" ? `&buyerMobile=${searchTerm}` : searchType === \"Name\" ? `&searchTerm=${searchTerm}` : \"\";\r\n    const queryParams = `&fromDate=${fromDate}&toDate=${toDate}${searchQuery}${businessType ? `&ondcDomain=${businessType}` : \"\"}${filterLogisticProvider ? `&logisticProvider=${filterLogisticProvider}` : \"\"}${filterStatus ? `&status=${filterStatus}` : \"\"}${filterLogisticStatus ? `&logStatus=${filterLogisticStatus}` : \"\"}&pageSize=${pageSize}&currentPage=${currentPage}`;\r\n\r\n    try {\r\n      const response = await getrNETOrders(request, queryParams, \"mnetadmin\");\r\n      return withResponse({ success: true, intent: intent, data: response?.data }, response?.headers);\r\n    }\r\n    catch (error) {\r\n      return json({ success: false, intent: intent, errorMessage: \"Failed to fetch orders\" }, { status: 400 })\r\n    }\r\n  }\r\n  if (intent === \"Cancel Order\") {\r\n    try {\r\n      const { order, formData } = JSON.parse(data);\r\n\r\n      const response = await cancelrNETOrder(request, order.tripId, order.orderGroupId);\r\n      return withResponse({ success: true, intent: intent }, response.headers);\r\n    }\r\n    catch (error) {\r\n      return json({ success: false, intent: intent, errorMessage: \"Failed to cancel order\" }, { status: 400 })\r\n    }\r\n  }\r\n  if (intent === \"Update LiveOrder Status\") {\r\n    try {\r\n      const { order, formData } = JSON.parse(data);\r\n      const status = order.orderStatus === \"Created\" ? \"Accepted\" : order.orderStatus === \"Accepted\" ? \"Packed\" : order.orderStatus === \"Packed\" ? \"Assigned\" : order.orderStatus === \"Assigned\" ? \"PickedUp\" : \"Dispatched\";\r\n      const queryParams = status === \"Packed\" ? `${formData.boxes !== undefined ? `&boxes=${formData.boxes}` : \"\"}${formData.bags !== undefined ? `&bags=${formData.bags}` : \"\"}` : \"\";\r\n\r\n      const response = await updateLiveOrderStatus(request, order.orderGroupId, status);\r\n      return withResponse({ success: true, intent: intent }, response.headers);\r\n    }\r\n    catch (error) {\r\n      return json({ success: false, intent: intent, errorMessage: \"Failed to update order status\" }, { status: 400 })\r\n    }\r\n  }\r\n  if (intent === \"Mark Delivered\") {\r\n    try {\r\n      const { order, formData } = JSON.parse(data);\r\n      const queryParams = `${formData.deliveryCode !== undefined ? `&deliveryCode=${formData.deliveryCode}` : \"\"}${formData.creditAmount !== undefined ? `&creditAmount=${formData.creditAmount}` : \"\"}${formData.boxesGiven !== undefined ? `&boxesGiven=${formData.boxesGiven}` : \"\"}${formData.boxesTaken !== undefined ? `&boxesTaken=${formData.boxesTaken}` : \"\"}`;\r\n\r\n      const response = await markDelivered(request, order.tripId, order.orderGroupId, queryParams);\r\n      return withResponse({ success: true, intent: intent }, response.headers);\r\n    }\r\n    catch (error) {\r\n      return json({ success: false, intent: intent, errorMessage: \"Failed to mark order as delivered\" }, { status: 400 })\r\n    }\r\n  }\r\n  if (intent === \"Manual Fulfillment\") {\r\n    try {\r\n      const { order, formData } = JSON.parse(data);\r\n      const logisticDetails = {\r\n        lRiderName: formData.lRiderName,\r\n        lRiderPhone: formData.lRiderPhone,\r\n        lPrice: formData.lPrice,\r\n        lPotp: formData.lPotp,\r\n        lDotp: formData.lDotp\r\n      }\r\n\r\n      const response = await updateLogisticDetails(request, order.orderGroupId, logisticDetails);\r\n      await updateLogisticProvider(request, order.orderGroupId, \"MANUAL\");\r\n      return withResponse({ success: true, intent: intent }, response.headers);\r\n    }\r\n    catch (error) {\r\n      return json({ success: false, intent: intent, errorMessage: \"Failed to switch order to manual fulfillment\" }, { status: 400 })\r\n    }\r\n  }\r\n\r\n  return json({ success: false, intent: intent, errorMessage: \"Invalid intent\" }, { status: 400 });\r\n});\r\n\r\n\r\nexport default function LiveOrderDashboard() {\r\n  const [allOrders, setAllOrders] = useState<rNETOrder[]>()\r\n  const [selectedOrder, setSelectedOrder] = useState<rNETOrder | null>(null)\r\n  const [orderStatusCounts, setOrderStatusCounts] = useState<OrderStatusCount[]>([])\r\n  const fetcher = useFetcher<ActionData>()\r\n\r\n  // action\r\n  const [actionType, setActionType] = useState<string>(\"\")\r\n  const [actionSelectedOrder, setActionSelectedOrder] = useState<rNETOrder | null>(null)\r\n  const [isSubmitting, setIsSubmitting] = useState(false)\r\n  const { toast } = useToast()\r\n\r\n  // Search and Filters\r\n  const [searchType, setSearchType] = useState<\"OrderID\" | \"SellerID\" | \"BuyerID\" | \"SellerMobile\" | \"BuyerMobile\" | \"Name\">(\"OrderID\")\r\n  const [searchTerm, setSearchTerm] = useState<string>(\"\")\r\n  const [filterDate, setFilterDate] = useState<DateRange>({ from: new Date(), to: new Date() });  // date object\r\n  const [businessType, setBusinessType] = useState<\"RET10\" | \"RET11\">(\"RET11\")\r\n  const [filterLogisticProvider, setFilterLogisticProvider] = useState<\"MP2\" | \"SELF\" | \"\">(\"\")\r\n  const [filterStatus, setFilterStatus] = useState<OrderStatus | \"\">(\"\")\r\n  const [filterLogisticStatus, setFilterLogisticStatus] = useState<LogisticStatus | \"\">(\"\")\r\n  const [pageSize, setPageSize] = useState(20)\r\n  const [currentPage, setCurrentPage] = useState(0)\r\n  const [totalElements, setTotalElements] = useState(0)\r\n\r\n  // local state\r\n  const [dateRange, setDateRange] = useState<DateRange>({ from: new Date(), to: new Date() });  //date object\r\n  const [selectedSeller, setSelectedSeller] = useState<string>(\"\")\r\n\r\n  // debounce on search term\r\n  const [debounceSearchTerm, setDebounceSearchTerm] = useState<string>(\"\");\r\n  useEffect(() => {\r\n    const timer = setTimeout(() => {\r\n      setDebounceSearchTerm(searchTerm);\r\n    }, 500);\r\n\r\n    return () => {\r\n      clearTimeout(timer);\r\n    };\r\n  }, [searchTerm]);\r\n\r\n  // Animation state for smooth transitions\r\n  const [animationKey, setAnimationKey] = useState(0)\r\n\r\n  // polling for auto-refresh\r\n  const { isPolling, startPolling, stopPolling } = usePolling(() => refreshOrders(true), 59000);\r\n\r\n  const refreshOrders = useCallback((isAutoRefresh?: boolean) => {\r\n    console.log(\"Refreshing orders...\")\r\n\r\n    const formData = new FormData();\r\n    formData.append(\"intent\", \"Fetch Orders\");\r\n    const data = { searchType, searchTerm: debounceSearchTerm, filterDate, businessType, filterLogisticProvider, filterStatus, filterLogisticStatus, pageSize, currentPage }\r\n    formData.append(\"data\", JSON.stringify(data))\r\n    fetcher.submit(formData, { method: \"post\" })\r\n  }, [debounceSearchTerm, filterDate, businessType, filterLogisticProvider, filterStatus, filterLogisticStatus, pageSize, currentPage])\r\n\r\n  useEffect(() => {\r\n    refreshOrders()\r\n  }, [debounceSearchTerm, filterDate, businessType, filterLogisticProvider, filterStatus, filterLogisticStatus, pageSize, currentPage])\r\n\r\n  useEffect(() => {\r\n    startPolling()\r\n    return () => stopPolling()\r\n  }, [startPolling, stopPolling])\r\n\r\n  // pause and resume polling when the tab is inactive\r\n  useEffect(() => {\r\n    const handleVisibilityChange = () => {\r\n      if (document.visibilityState === \"visible\") {\r\n        startPolling()\r\n      } else {\r\n        stopPolling()\r\n      }\r\n    }\r\n    document.addEventListener(\"visibilitychange\", handleVisibilityChange)\r\n    return () => {\r\n      document.removeEventListener(\"visibilitychange\", handleVisibilityChange)\r\n    }\r\n  }, [startPolling, stopPolling])\r\n\r\n  const searchTypeFilters = [\r\n    { label: \"Order ID\", value: \"OrderID\" },\r\n    { label: \"Seller ID\", value: \"SellerID\" },\r\n    { label: \"Buyer ID\", value: \"BuyerID\" },\r\n    { label: \"Seller Mobile\", value: \"SellerMobile\" },\r\n    { label: \"Buyer Mobile\", value: \"BuyerMobile\" },\r\n    { label: \"Name\", value: \"Name\" },\r\n  ]\r\n\r\n  const statusFilters = [\r\n    { label: \"Created\", value: \"Created\" },\r\n    { label: \"Accepted\", value: \"Accepted\" },\r\n    { label: \"Packed\", value: \"Packed\" },\r\n    { label: \"Assigned\", value: \"Assigned\" },\r\n    { label: \"Picked Up\", value: \"PickedUp\" },\r\n    { label: \"Dispatched\", value: \"Dispatched\" },\r\n    { label: \"Delivered\", value: \"Delivered\" },\r\n    { label: \"Cancelled\", value: \"Cancelled\" },\r\n  ]\r\n\r\n  const logisticStatusFilters = [\r\n    { label: \"LOG_PACKED\", value: \"LOG_PACKED\" },\r\n    { label: \"LOG_ASSIGNED\", value: \"LOG_ASSIGNED\" },\r\n    { label: \"LOG_PICKED_UP\", value: \"LOG_PICKED_UP\" },\r\n    { label: \"LOG_DISPATCHED\", value: \"LOG_DISPATCHED\" },\r\n    { label: \"LOG_DELIVERED\", value: \"LOG_DELIVERED\" },\r\n    { label: \"LOG_CREATED\", value: \"LOG_CREATED\" },\r\n    { label: \"LOG_PENDING\", value: \"LOG_PENDING\" },\r\n    { label: \"LOG_SEARCHING_AGENT\", value: \"LOG_SEARCHING_AGENT\" },\r\n    { label: \"LOG_RTO_INITIATED\", value: \"LOG_RTO_INITIATED\" },\r\n    { label: \"LOG_CANCELLED\", value: \"LOG_CANCELLED\" },\r\n  ]\r\n\r\n  // Separate live and completed orders\r\n  const { liveOrders, completedOrders } = useMemo(() => {\r\n    const live = allOrders?.filter((order) => ![\"Delivered\", \"Cancelled\"].includes(order.orderStatus))\r\n      .sort((a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime())\r\n\r\n    const completed = allOrders?.filter((order) => [\"Delivered\", \"Cancelled\"].includes(order.orderStatus))\r\n\r\n    return { liveOrders: live, completedOrders: completed }\r\n  }, [allOrders])\r\n\r\n  // Get unique sellers\r\n  const uniqueSellers = Array.from(\r\n    new Map(allOrders?.map((order) => [order.sellerId, { id: order.sellerId, name: order.sellerName }])).values(),\r\n  )\r\n\r\n  // Statistics from API response\r\n  const stats = {\r\n    total: totalElements,\r\n    created: orderStatusCounts.find((s) => s.status === \"Created\")?.count || 0,\r\n    accepted: orderStatusCounts.find((s) => s.status === \"Accepted\")?.count || 0,\r\n    packed: orderStatusCounts.find((s) => s.status === \"Packed\")?.count || 0,\r\n    assigned: orderStatusCounts.find((s) => s.status === \"Assigned\")?.count || 0,\r\n    dispatched: orderStatusCounts.find((s) => s.status === \"Dispatched\")?.count || 0,\r\n    delivered: orderStatusCounts.find((s) => s.status === \"Delivered\")?.count || 0,\r\n    cancelled: orderStatusCounts.find((s) => s.status === \"Cancelled\")?.count || 0,\r\n  }\r\n\r\n  const handleAction = (order: rNETOrder, action: string) => {\r\n    setActionSelectedOrder(order)\r\n    setActionType(action)\r\n  }\r\n\r\n  const handleSubmitAction = (formData: any) => {\r\n    const actionData = new FormData();\r\n    actionData.append(\"intent\", actionType);\r\n    actionData.append(\"data\", JSON.stringify({ order: actionSelectedOrder, formData }))\r\n    fetcher.submit(actionData, { method: \"post\" })\r\n    setIsSubmitting(true)\r\n  }\r\n\r\n  useEffect(() => {\r\n    if (fetcher.data?.intent === \"Fetch Orders\") {\r\n      if (fetcher.data?.success) {\r\n        fetcher.data?.data?.orders ? setAllOrders(fetcher.data.data.orders) : setAllOrders([])\r\n        fetcher.data?.data?.totalElements ? setTotalElements(fetcher.data.data.totalElements) : setTotalElements(0)\r\n        fetcher.data?.data?.orderStatusCounts ? setOrderStatusCounts(fetcher.data.data.orderStatusCounts) : setOrderStatusCounts([])\r\n        setSelectedSeller(\"\")\r\n      } else if (fetcher.data?.success === false) {\r\n        console.log(fetcher.data?.errorMessage)\r\n        toast({\r\n          title: \"Error\",\r\n          description: fetcher.data?.errorMessage,\r\n          variant: \"destructive\"\r\n        })\r\n      }\r\n    }\r\n    if (fetcher.data?.intent === \"Cancel Order\") {\r\n      if (fetcher.data?.success) {\r\n        toast({\r\n          title: \"Order Cancelled\",\r\n          description: \"Order cancelled successfully\",\r\n          style: {\r\n            backgroundColor: \"#00A33F\",\r\n            color: \"#ffffff\"\r\n          }\r\n        })\r\n        setActionSelectedOrder(null)\r\n        setActionType(\"\")\r\n        setIsSubmitting(false)\r\n        refreshOrders(false)\r\n      } else if (fetcher.data?.success === false) {\r\n        console.log(fetcher.data?.errorMessage)\r\n        setIsSubmitting(false)\r\n        toast({\r\n          title: \"Error\",\r\n          description: fetcher.data?.errorMessage,\r\n          variant: \"destructive\"\r\n        })\r\n      }\r\n    }\r\n    if (fetcher.data?.intent === \"Update LiveOrder Status\") {\r\n      if (fetcher.data?.success) {\r\n        toast({\r\n          title: \"Order Status Updated\",\r\n          description: \"Order status updated successfully\",\r\n          style: {\r\n            backgroundColor: \"#00A33F\",\r\n            color: \"#ffffff\"\r\n          }\r\n        })\r\n        setActionSelectedOrder(null)\r\n        setActionType(\"\")\r\n        setIsSubmitting(false)\r\n        refreshOrders(false)\r\n      } else if (fetcher.data?.success === false) {\r\n        console.log(fetcher.data?.errorMessage)\r\n        setIsSubmitting(false)\r\n        toast({\r\n          title: \"Error\",\r\n          description: fetcher.data?.errorMessage,\r\n          variant: \"destructive\"\r\n        })\r\n      }\r\n    }\r\n    if (fetcher.data?.intent === \"Mark Delivered\") {\r\n      if (fetcher.data?.success) {\r\n        toast({\r\n          title: \"Order Marked as Delivered\",\r\n          description: \"Order marked as delivered successfully\",\r\n          style: {\r\n            backgroundColor: \"#00A33F\",\r\n            color: \"#ffffff\"\r\n          }\r\n        })\r\n        setActionSelectedOrder(null)\r\n        setActionType(\"\")\r\n        setIsSubmitting(false)\r\n        refreshOrders(false)\r\n      } else if (fetcher.data?.success === false) {\r\n        console.log(fetcher.data?.errorMessage)\r\n        setIsSubmitting(false)\r\n        toast({\r\n          title: \"Error\",\r\n          description: fetcher.data?.errorMessage,\r\n          variant: \"destructive\"\r\n        })\r\n      }\r\n    }\r\n    if (fetcher.data?.intent === \"Manual Fulfillment\") {\r\n      if (fetcher.data?.success) {\r\n        toast({\r\n          title: \"Order Switched to Manual Fulfillment\",\r\n          description: \"Order switched to manual fulfillment successfully\",\r\n          style: {\r\n            backgroundColor: \"#00A33F\",\r\n            color: \"#ffffff\"\r\n          }\r\n        })\r\n        setActionSelectedOrder(null)\r\n        setActionType(\"\")\r\n        setIsSubmitting(false)\r\n        refreshOrders(false)\r\n      } else if (fetcher.data?.success === false) {\r\n        console.log(fetcher.data?.errorMessage)\r\n        setIsSubmitting(false)\r\n        toast({\r\n          title: \"Error\",\r\n          description: fetcher.data?.errorMessage,\r\n          variant: \"destructive\"\r\n        })\r\n      }\r\n    }\r\n  }, [fetcher.data])\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-gray-50\">\r\n      <div className=\"container mx-auto px-2 md:px-4 max-w-7xl\">\r\n        {/* Header */}\r\n        <div className=\"p-4 py-3 -mx-4 mb-2 sticky top-0 z-10 rounded-md bg-white/20 backdrop-blur-lg shadow-sm\">\r\n          <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2\">\r\n            <h1 className=\"text-xl md:text-2xl font-bold text-gray-900\">Live Order Dashboard</h1>\r\n            <div className=\"flex flex-row items-center gap-2\">\r\n              <Button\r\n                variant=\"outline\"\r\n                size=\"sm\"\r\n                onClick={() => refreshOrders(false)}\r\n                disabled={(fetcher.state === \"loading\" || fetcher.state === \"submitting\") && fetcher.data?.intent === \"Fetch Orders\"}\r\n                className=\"w-fit sm:ml-auto flex items-center gap-2\"\r\n              >\r\n                <RefreshCw className={`w-4 h-4 ${(fetcher.state === \"loading\" || fetcher.state === \"submitting\") && fetcher.data?.intent === \"Fetch Orders\" ? \"animate-spin\" : \"\"}`} />\r\n                {(fetcher.state === \"loading\" || fetcher.state === \"submitting\") && fetcher.data?.intent === \"Fetch Orders\" ? \"Refreshing...\" : \"Refresh\"}\r\n              </Button>\r\n              <div className=\"flex items-center gap-2 text-sm text-gray-600\">\r\n                <div className={`w-2 h-2 rounded-full ${isPolling ? \"bg-green-500 animate-pulse\" : \"bg-gray-400\"}`} />\r\n                Auto-refresh {isPolling ? \"ON\" : \"OFF\"}\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Search and Filters */}\r\n        <Card className=\"mb-1\">\r\n          <CardContent className=\"p-2\">\r\n            <div className=\"space-y-1\">\r\n              {/* Search */}\r\n              <div className=\"grid sm:grid-cols-2 lg:grid-cols-4 gap-2\">\r\n                <div className=\"relative lg:col-span-1\">\r\n                  <Select value={searchType} onValueChange={(value: typeof searchType) => setSearchType(value)}>\r\n                    <SelectTrigger>\r\n                      <SelectValue placeholder=\"Search by\" />\r\n                    </SelectTrigger>\r\n                    <SelectContent>\r\n                      {searchTypeFilters.map((filter) => (\r\n                        <SelectItem key={filter.value} value={filter.value}>\r\n                          {filter.label}\r\n                        </SelectItem>\r\n                      ))}\r\n                    </SelectContent>\r\n                  </Select>\r\n                </div>\r\n\r\n                <div className=\"relative lg:col-span-2\">\r\n                  <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4\" />\r\n                  <Input\r\n                    placeholder={searchType === \"Name\" ? \"Search Buyer, Seller name\" : (searchType === \"BuyerMobile\" || searchType === \"SellerMobile\") ? \"Search by Mobile\" : \"Search by ID\"}\r\n                    value={searchTerm}\r\n                    onChange={(e) => setSearchTerm(e.target.value)}\r\n                    className=\"pl-10\"\r\n                  />\r\n                </div>\r\n\r\n                <div className=\"relative sm:col-span-2 lg:col-span-1 \">\r\n                  <Popover>\r\n                    <PopoverTrigger asChild>\r\n                      <Button\r\n                        id=\"filterDate\"\r\n                        variant=\"outline\"\r\n                        className={cn(\r\n                          \"w-full justify-start text-left font-normal\",\r\n                          !filterDate.from && \"text-muted-foreground\"\r\n                        )}\r\n                      >\r\n                        <CalendarIcon />\r\n                        {filterDate?.from ? (\r\n                          filterDate.to ? (\r\n                            <>\r\n                              {format(filterDate.from, \"LLL dd, y\")} - {format(filterDate.to, \"LLL dd, y\")}\r\n                            </>\r\n                          ) : (\r\n                            format(filterDate.from, \"LLL dd, y\")\r\n                          )\r\n                        ) : (\r\n                          <span>Pick a date</span>\r\n                        )}\r\n                      </Button>\r\n                    </PopoverTrigger>\r\n                    <PopoverContent className=\"w-auto p-0\" align=\"start\">\r\n                      <Calendar\r\n                        initialFocus\r\n                        selected={dateRange}\r\n                        mode=\"range\"\r\n                        onSelect={(range: DateRange | undefined) => {\r\n                          if (!range?.from) return;\r\n                          setDateRange({\r\n                            from: range.from,\r\n                            to: range.to || undefined,\r\n                          });\r\n                        }}\r\n                      />\r\n                      <PopoverClose className=\"w-full\">\r\n                        <Button\r\n                          variant=\"ghost\"\r\n                          className=\"w-full text-blue-500 hover:text-blue-500 justify-center\"\r\n                          onClick={() => setFilterDate(dateRange)}\r\n                        >\r\n                          Set\r\n                        </Button>\r\n                      </PopoverClose>\r\n                    </PopoverContent>\r\n                  </Popover>\r\n                </div>\r\n              </div>\r\n\r\n              {/* Filters */}\r\n              <div className=\"grid grid-cols-2 lg:grid-cols-5 gap-2\">\r\n                <div className=\"space-y-1\">\r\n                  <Label className=\"text-sm font-medium\">Business Type</Label>\r\n                  <Select value={businessType} onValueChange={(value: \"RET10\" | \"RET11\") => setBusinessType(value)}>\r\n                    <SelectTrigger>\r\n                      <SelectValue placeholder=\"Select business type\" />\r\n                    </SelectTrigger>\r\n                    <SelectContent>\r\n                      <SelectItem key=\"RET10\" value=\"RET10\">Non-Restaurant</SelectItem>\r\n                      <SelectItem key=\"RET11\" value=\"RET11\">Restaurant</SelectItem>\r\n                    </SelectContent>\r\n                  </Select>\r\n                </div>\r\n\r\n                <div className=\"space-y-1\">\r\n                  <Label className=\"text-sm font-medium\">Logistic Provider</Label>\r\n                  <Select\r\n                    value={filterLogisticProvider}\r\n                    onValueChange={(value: \"MP2\" | \"SELF\") => setFilterLogisticProvider(value)}\r\n                  >\r\n                    <SelectTrigger>\r\n                      <SelectValue placeholder=\"Select provider\" />\r\n                    </SelectTrigger>\r\n                    <SelectContent>\r\n                      <SelectItem key=\"MP2\" value=\"MP2\">MP2</SelectItem>\r\n                      <SelectItem key=\"SELF\" value=\"SELF\">SELF</SelectItem>\r\n                    </SelectContent>\r\n                  </Select>\r\n                </div>\r\n\r\n                <div className=\"space-y-1\">\r\n                  <Label className=\"text-sm font-medium\">Status</Label>\r\n                  <Select value={filterStatus} onValueChange={(value: OrderStatus) => setFilterStatus(value)}>\r\n                    <SelectTrigger>\r\n                      <SelectValue placeholder=\"Select status\" />\r\n                    </SelectTrigger>\r\n                    <SelectContent>\r\n                      {statusFilters.map((filter) => (\r\n                        <SelectItem key={filter.value} value={filter.value}>\r\n                          {filter.label}\r\n                        </SelectItem>\r\n                      ))}\r\n                    </SelectContent>\r\n                  </Select>\r\n                </div>\r\n\r\n                <div className=\"space-y-1\">\r\n                  <Label className=\"text-sm font-medium\">Logistic Status</Label>\r\n                  <Select\r\n                    value={filterLogisticStatus}\r\n                    onValueChange={(value: LogisticStatus) => setFilterLogisticStatus(value)}\r\n                  >\r\n                    <SelectTrigger>\r\n                      <SelectValue placeholder=\"Select status\" />\r\n                    </SelectTrigger>\r\n                    <SelectContent>\r\n                      {logisticStatusFilters.map((filter) => (\r\n                        <SelectItem key={filter.value} value={filter.value}>\r\n                          {filter.label}\r\n                        </SelectItem>\r\n                      ))}\r\n                    </SelectContent>\r\n                  </Select>\r\n                </div>\r\n\r\n                <div className=\"space-y-1 col-span-2 lg:col-span-1 flex items-end\">\r\n                  <Button\r\n                    onClick={() => {\r\n                      setSearchType(\"OrderID\")\r\n                      setSearchTerm(\"\")\r\n                      setDebounceSearchTerm(\"\")\r\n                      setFilterDate({ from: new Date(), to: new Date() })\r\n                      setDateRange({ from: new Date(), to: new Date() })\r\n                      setBusinessType(\"RET11\")\r\n                      setFilterLogisticProvider(\"\")\r\n                      setFilterStatus(\"\")\r\n                      setFilterLogisticStatus(\"\")\r\n                      setPageSize(20)\r\n                      setCurrentPage(0)\r\n                      setSelectedSeller(\"\")\r\n                    }}\r\n                    className=\"w-full\"\r\n                  >\r\n                    <Filter className=\"w-4 h-4 mr-2\" />\r\n                    Clear Filters\r\n                  </Button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </CardContent>\r\n        </Card>\r\n\r\n        {/* Pagination */}\r\n        <Card className=\"p-0 mb-1\">\r\n          <CardContent className=\"px-1.5 py-1\">\r\n            <div className=\"flex flex-row items-center justify-end gap-1.5\">\r\n              <Select\r\n                value={selectedSeller}\r\n                onValueChange={(value) => {\r\n                  setSelectedSeller(value);\r\n                  setAllOrders(allOrders?.filter((order) => order.sellerId === Number(value)))\r\n                  setTotalElements(allOrders?.filter((order) => order.sellerId === Number(value)).length || 0)\r\n                }}\r\n              >\r\n                <SelectTrigger className=\"w-[140px] h-[36px]\">\r\n                  <SelectValue placeholder=\"Select seller\" />\r\n                </SelectTrigger>\r\n                <SelectContent>\r\n                  {uniqueSellers.map((seller) => (\r\n                    <SelectItem key={seller.id} value={seller.id.toString()}>\r\n                      {seller.id}{\" - \"}{seller.name}\r\n                    </SelectItem>\r\n                  ))}\r\n                </SelectContent>\r\n              </Select>\r\n              <Select value={pageSize.toString()} onValueChange={(value) => setPageSize(Number(value))}>\r\n                <SelectTrigger className=\"w-[140px] h-[36px]\">\r\n                  <SelectValue />\r\n                </SelectTrigger>\r\n                <SelectContent>\r\n                  <SelectItem value=\"20\">20 per Page</SelectItem>\r\n                  <SelectItem value=\"50\">50 per Page</SelectItem>\r\n                  <SelectItem value=\"100\">100 per Page</SelectItem>\r\n                </SelectContent>\r\n              </Select>\r\n              <Select value={currentPage.toString()} onValueChange={(value) => setCurrentPage(Number(value))}>\r\n                <SelectTrigger className=\"w-[140px] h-[36px]\">\r\n                  <SelectValue />\r\n                </SelectTrigger>\r\n                <SelectContent>\r\n                  {Array.from({ length: Math.ceil(totalElements / pageSize) }, (_, i) => (\r\n                    <SelectItem key={i} value={i.toString()}>\r\n                      Page {i + 1}\r\n                    </SelectItem>\r\n                  ))}\r\n                </SelectContent>\r\n              </Select>\r\n            </div>\r\n          </CardContent>\r\n        </Card>\r\n\r\n        {/* Statistics Cards */}\r\n        <div className=\"grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-8 gap-1 sm:gap-2 mb-4\">\r\n          <Card className=\"shadow-sm\">\r\n            <CardContent className=\"p-1 sm:p-2 text-center\">\r\n              <div className=\"text-xs sm:text-sm text-gray-800\">Total</div>\r\n              <div className=\"text-lg leading-6 font-semibold text-gray-900\">{stats.total}</div>\r\n            </CardContent>\r\n          </Card>\r\n          <Card className=\"shadow-sm\">\r\n            <CardContent className=\"p-1 sm:p-2 text-center\">\r\n              <div className=\"text-xs sm:text-sm text-gray-800\">Created</div>\r\n              <div className=\"text-lg leading-6 font-semibold text-purple-600\">{stats.created}</div>\r\n            </CardContent>\r\n          </Card>\r\n          <Card className=\"shadow-sm\">\r\n            <CardContent className=\"p-1 sm:p-2 text-center\">\r\n              <div className=\"text-xs sm:text-sm text-gray-800\">Accepted</div>\r\n              <div className=\"text-lg leading-6 font-semibold text-purple-600\">{stats.accepted}</div>\r\n            </CardContent>\r\n          </Card>\r\n          <Card className=\"shadow-sm\">\r\n            <CardContent className=\"p-1 sm:p-2 text-center\">\r\n              <div className=\"text-xs sm:text-sm text-gray-800\">Packed</div>\r\n              <div className=\"text-lg leading-6 font-semibold text-orange-600\">{stats.packed}</div>\r\n            </CardContent>\r\n          </Card>\r\n          <Card className=\"shadow-sm\">\r\n            <CardContent className=\"p-1 sm:p-2 text-center\">\r\n              <div className=\"text-xs sm:text-sm text-gray-800\">Assigned</div>\r\n              <div className=\"text-lg leading-6 font-semibold text-orange-600\">{stats.assigned}</div>\r\n            </CardContent>\r\n          </Card>\r\n          <Card className=\"shadow-sm\">\r\n            <CardContent className=\"p-1 sm:p-2 text-center\">\r\n              <div className=\"text-xs sm:text-sm text-gray-800\">Dispatched</div>\r\n              <div className=\"text-lg leading-6 font-semibold text-yellow-600\">{stats.dispatched}</div>\r\n            </CardContent>\r\n          </Card>\r\n          <Card className=\"shadow-sm\">\r\n            <CardContent className=\"p-1 sm:p-2 text-center\">\r\n              <div className=\"text-xs sm:text-sm text-gray-800\">Delivered</div>\r\n              <div className=\"text-lg leading-6 font-semibold text-green-600\">{stats.delivered}</div>\r\n            </CardContent>\r\n          </Card>\r\n          <Card className=\"shadow-sm\">\r\n            <CardContent className=\"p-1 sm:p-2 text-center\">\r\n              <div className=\"text-xs sm:text-sm text-gray-800\">Cancelled</div>\r\n              <div className=\"text-lg leading-6 font-semibold text-red-600\">{stats.cancelled}</div>\r\n            </CardContent>\r\n          </Card>\r\n        </div>\r\n\r\n        {/* Live Orders Section */}\r\n        <div className=\"mb-8\">\r\n          <div className=\"flex items-center justify-between mb-4\">\r\n            <h2 className=\"text-xl sm:text-2xl font-bold flex items-center gap-2\">\r\n              <div className=\"w-3 h-3 bg-green-500 rounded-full animate-pulse\" />\r\n              Live Orders\r\n              <Badge variant=\"secondary\" className=\"ml-2\">\r\n                {totalElements - (stats.delivered + stats.cancelled) || 0}\r\n              </Badge>\r\n            </h2>\r\n          </div>\r\n\r\n          {liveOrders?.length === 0 ? (\r\n            <Card>\r\n              <CardContent className=\"p-8 text-center\">\r\n                <div className=\"text-gray-500\">\r\n                  <Clock className=\"w-12 h-12 mx-auto mb-4 opacity-50\" />\r\n                  <p className=\"text-lg font-medium\">No live orders</p>\r\n                  <p className=\"text-sm\">No active orders for the selected date and filters.</p>\r\n                </div>\r\n              </CardContent>\r\n            </Card>\r\n          ) : (\r\n            <div\r\n              key={`live-orders-${animationKey}`}\r\n              className=\"grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-4 transition-all duration-300 ease-out\"\r\n              style={{\r\n                transform: 'translateY(0)',\r\n                transition: 'transform 0.3s ease-out'\r\n              }}\r\n            >\r\n              {liveOrders?.map((order, index) => (\r\n                <div\r\n                  key={`${order.orderGroupId}-${animationKey}`}\r\n                  className=\"animate-in fade-in slide-in-from-bottom-4 duration-500 ease-out transform transition-all hover:scale-[1.01]\"\r\n                  style={{\r\n                    animationDelay: `${index * 50}ms`,\r\n                    animationFillMode: 'both',\r\n                    willChange: 'transform'\r\n                  }}\r\n                >\r\n                  <OrderCard\r\n                    order={order}\r\n                    onViewDetails={setSelectedOrder}\r\n                    onAction={handleAction}\r\n                  />\r\n                </div>\r\n              ))}\r\n            </div>\r\n          )}\r\n        </div>\r\n\r\n        {/* Completed Orders Section */}\r\n        <div className=\"mb-8\">\r\n          <div className=\"flex items-center justify-between mb-4\">\r\n            <h2 className=\"text-xl sm:text-2xl font-bold flex items-center gap-2\">\r\n              <div className=\"w-3 h-3 bg-gray-500 rounded-full\" />\r\n              Completed Orders\r\n              <Badge variant=\"secondary\" className=\"ml-2\">\r\n                {stats.delivered + stats.cancelled || 0}\r\n              </Badge>\r\n            </h2>\r\n          </div>\r\n\r\n          {completedOrders?.length === 0 ? (\r\n            <Card>\r\n              <CardContent className=\"p-8 text-center\">\r\n                <div className=\"text-gray-500\">\r\n                  <AlertCircle className=\"w-12 h-12 mx-auto mb-4 opacity-50\" />\r\n                  <p className=\"text-lg font-medium\">No completed orders</p>\r\n                  <p className=\"text-sm\">No completed orders for the selected date and filters.</p>\r\n                </div>\r\n              </CardContent>\r\n            </Card>\r\n          ) : (\r\n            <div className=\"overflow-x-auto\">\r\n              <div\r\n                key={`completed-orders-${animationKey}`}\r\n                className=\"flex gap-4 pb-4 min-w-max transition-all duration-300 ease-out\"\r\n                style={{\r\n                  transform: 'translateX(0)',\r\n                  transition: 'transform 0.3s ease-out'\r\n                }}\r\n              >\r\n                {completedOrders?.map((order, index) => (\r\n                  <div\r\n                    key={`${order.orderGroupId}-${animationKey}`}\r\n                    className=\"flex-shrink-0 w-72 sm:w-80 animate-in fade-in slide-in-from-right-4 duration-500 ease-out transform transition-all hover:scale-[1.01]\"\r\n                    style={{\r\n                      animationDelay: `${index * 50}ms`,\r\n                      animationFillMode: 'both',\r\n                      willChange: 'transform'\r\n                    }}\r\n                  >\r\n                    <OrderCard order={order} onViewDetails={setSelectedOrder} onAction={handleAction} />\r\n                  </div>\r\n                ))}\r\n              </div>\r\n            </div>\r\n          )}\r\n        </div>\r\n\r\n        {/* Order Details Modal */}\r\n        <OrderDetailsModal order={selectedOrder} onClose={() => setSelectedOrder(null)} onAction={handleAction} />\r\n\r\n        {/* Action Modal */}\r\n        <ActionModal\r\n          order={actionSelectedOrder}\r\n          actionType={actionType}\r\n          onClose={() => {\r\n            setActionSelectedOrder(null)\r\n            setActionType(\"\")\r\n          }}\r\n          isSubmitting={isSubmitting}\r\n          onSubmit={handleSubmitAction}\r\n        />\r\n      </div>\r\n    </div >\r\n  )\r\n}\r\n\r\n\r\n// Order Card\r\ninterface OrderCardProps {\r\n  order: rNETOrder\r\n  onViewDetails: (order: rNETOrder) => void\r\n  onAction: (order: rNETOrder, action: string) => void\r\n}\r\n\r\nexport function OrderCard({ order, onViewDetails, onAction }: OrderCardProps) {\r\n  const [currentTime, setCurrentTime] = useState(Date.now())\r\n\r\n  // Update timer every second for live orders\r\n  useEffect(() => {\r\n    if (![\"Delivered\", \"Cancelled\"].includes(order.orderStatus)) {\r\n      const interval = setInterval(() => {\r\n        setCurrentTime(Date.now())\r\n      }, 1000)\r\n      return () => clearInterval(interval)\r\n    }\r\n  }, [order.orderStatus])\r\n\r\n  const styling = getOrderCardStyling(order)\r\n  const timeDiff = getTimeDifferenceInSeconds(order.createdAt)\r\n  const isNewOrder = timeDiff <= 60\r\n\r\n  //play sound if it is new order\r\n  useEffect(() => {\r\n    if (isNewOrder) {\r\n      const audio = new Audio(\"/new-order.mp3\")\r\n      audio.play().catch(console.warn)\r\n      return () => {\r\n        audio.pause()\r\n        audio.src = \"\"\r\n      }\r\n    }\r\n  }, [isNewOrder])\r\n\r\n  const handlePhoneClick = (phoneNumber: string, e: React.MouseEvent) => {\r\n    e.stopPropagation()\r\n    window.open(`tel:${phoneNumber}`, \"_self\")\r\n  }\r\n\r\n  const getActionButtons = () => {\r\n    const buttons = []\r\n\r\n    if (order.orderStatus === \"Created\") {\r\n      buttons.push(\r\n        <Button\r\n          key=\"cancel\"\r\n          variant=\"destructive\"\r\n          size=\"sm\"\r\n          onClick={(e) => {\r\n            e.stopPropagation()\r\n            onAction(order, \"Cancel Order\")\r\n          }}\r\n          className=\"text-xs\"\r\n        >\r\n          Cancel Order\r\n        </Button>,\r\n      )\r\n    }\r\n    if ((order.orderStatus !== \"Delivered\" && order.orderStatus !== \"Cancelled\") && order.logisticProvider === \"MP2\") {\r\n      buttons.push(\r\n        <Button\r\n          key=\"manual\"\r\n          variant=\"default\"\r\n          size=\"sm\"\r\n          onClick={(e) => {\r\n            e.stopPropagation()\r\n            onAction(order, \"Manual Fulfillment\")\r\n          }}\r\n          className=\"text-xs\"\r\n        >\r\n          Manual Fulfillment\r\n        </Button>,\r\n      )\r\n    }\r\n    if (showUpdateStatusButton(order)) {\r\n      buttons.push(\r\n        <Button\r\n          key=\"updateStatus\"\r\n          variant=\"default\"\r\n          size=\"sm\"\r\n          onClick={(e) => {\r\n            e.stopPropagation()\r\n            onAction(order, \"Update LiveOrder Status\")\r\n          }}\r\n          className=\"text-xs\"\r\n        >\r\n          Update to {order.orderStatus === \"Created\" ? \"Accepted\" : order.orderStatus === \"Accepted\" ? \"Packed\" : order.orderStatus === \"Packed\" ? \"Assigned\" : order.orderStatus === \"Assigned\" ? \"PickedUp\" : \"Dispatched\"} <ArrowRight className=\"w-3 h-3\" />\r\n        </Button>,\r\n      )\r\n    }\r\n    if (order.orderStatus === \"Dispatched\") {\r\n      buttons.push(\r\n        <Button\r\n          key=\"markDelivered\"\r\n          variant=\"default\"\r\n          size=\"sm\"\r\n          onClick={(e) => {\r\n            e.stopPropagation()\r\n            onAction(order, \"Mark Delivered\")\r\n          }}\r\n          className=\"text-xs bg-blue-500 hover:bg-blue-500\"\r\n        >\r\n          Mark Delivered\r\n        </Button>,\r\n      )\r\n    }\r\n\r\n    return buttons\r\n  }\r\n\r\n  return (\r\n    <Card\r\n      className={`${styling.className} will-change-transform cursor-pointer transition-all duration-500 h-full hover:shadow-lg`}\r\n      onClick={() => onViewDetails(order)}\r\n      onMouseEnter={(e) => {\r\n        e.currentTarget.style.animationPlayState = \"paused\"\r\n      }}\r\n      onMouseLeave={(e) => {\r\n        e.currentTarget.style.animationPlayState = \"running\"\r\n      }}\r\n    >\r\n      <CardContent className=\"p-3 sm:p-4\">\r\n        {/* Header with Order ID and Time */}\r\n        <div className=\"flex flex-col sm:flex-row sm:justify-between sm:items-start gap-2 mb-3\">\r\n          <div className=\"flex flex-wrap items-center gap-2\">\r\n            <div className=\"font-semibold text-base sm:text-lg\">#{order.orderGroupId}</div>\r\n            {isNewOrder && (\r\n              <Badge variant=\"secondary\" className=\"bg-gradient-to-r from-emerald-100 to-green-100 text-emerald-800 text-xs border border-emerald-200 shadow-sm font-semibold animate-bounce\">\r\n                ✨ NEW\r\n              </Badge>\r\n            )}\r\n            {order.supportTickets && order.supportTickets.length > 0 && (\r\n              <Badge variant=\"destructive\" className=\"flex items-center gap-1 text-xs\">\r\n                <MessageCircleQuestionIcon className=\"w-3 h-3\" />\r\n                {order.supportTickets.length}\r\n              </Badge>\r\n            )}\r\n          </div>\r\n          <div className=\"text-xs sm:text-sm text-gray-500 flex items-center gap-1\">\r\n            <Clock className=\"w-3 h-3 sm:w-4 sm:h-4\" />\r\n            {[\"Cancelled\"].includes(order.orderStatus)\r\n              ? formatTimeElapsed(getTimeDifferenceInSeconds(order.createdAt, order.deliveredTime || order.updatedAt))\r\n              : [\"Delivered\"].includes(order.orderStatus) ? formatTimeElapsed(getTimeDifferenceInSeconds(order.createdAt, order.deliveredTime || order.updatedAt)) : formatTimeElapsed(timeDiff)}\r\n          </div>\r\n        </div>\r\n\r\n        {/* Customer Info */}\r\n        <div className=\"mb-3\">\r\n          <div className=\"flex items-center gap-2 mb-1\">\r\n            <Timer className=\"w-3 h-3 sm:w-4 sm:h-4 text-gray-500 flex-shrink-0\" />\r\n            <span className=\"text-xs sm:text-sm text-gray-500\">\r\n              {dayjs(order.createdAt).format(\"DD MMM YY - h:mm A\")}\r\n            </span>\r\n          </div>\r\n          <div className=\"flex items-center gap-2 mb-1\">\r\n            <User className=\"w-3 h-3 sm:w-4 sm:h-4 text-gray-500 flex-shrink-0\" />\r\n            <span className=\"font-medium text-sm sm:text-base truncate\">{order.buyerName}</span>\r\n            <button\r\n              onClick={(e) => handlePhoneClick(order.buyerMobile, e)}\r\n              className=\"text-blue-600 hover:text-blue-800 flex-shrink-0 p-1 rounded hover:bg-blue-50\"\r\n            >\r\n              <Phone className=\"w-3 h-3 sm:w-4 sm:h-4\" />\r\n            </button>\r\n          </div>\r\n          <div className=\"text-xs sm:text-sm text-gray-600 flex items-start gap-2\">\r\n            <MapPin className=\"w-3 h-3 sm:w-4 sm:h-4 text-gray-500 mt-0.5 flex-shrink-0\" />\r\n            <span className=\"line-clamp-2\">{order.bAddress}</span>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Restaurant Info */}\r\n        <div className=\"mb-3\">\r\n          <div className=\"flex items-center gap-2 flex-wrap\">\r\n            <Store className=\"w-3 h-3 sm:w-4 sm:h-4 text-gray-500 flex-shrink-0\" />\r\n            <span className=\"font-medium text-sm sm:text-base truncate\">{order.sellerName}</span>\r\n            <button\r\n              onClick={(e) => handlePhoneClick(order.sellerMobile, e)}\r\n              className=\"text-blue-600 hover:text-blue-800 flex-shrink-0 p-1 rounded hover:bg-blue-50\"\r\n            >\r\n              <Phone className=\"w-3 h-3 sm:w-4 sm:h-4\" />\r\n            </button>\r\n            <Badge variant=\"outline\" className=\"text-xs\">\r\n              {order.logisticProvider}\r\n            </Badge>\r\n            {(order.pos !== \"none\" && order.pos !== undefined) && (\r\n              <Badge variant=\"outline\" className=\"text-xs\">\r\n                {order.pos}\r\n              </Badge>\r\n            )}\r\n          </div>\r\n        </div>\r\n\r\n        {/* Delivery Info */}\r\n        {order.logisticProvider === \"MP2\" && order.logisticDetails && (\r\n          <div className=\"mb-3\">\r\n            <div className=\"flex items-center gap-2 flex-wrap\">\r\n              <Truck className=\"w-3 h-3 sm:w-4 sm:h-4 text-gray-500 flex-shrink-0\" />\r\n              <span className=\"text-xs sm:text-sm truncate\">{order.logisticDetails.riderName}</span>\r\n              {order.logisticDetails.riderPhone && (\r\n                <button\r\n                  onClick={(e) => handlePhoneClick(order.logisticDetails?.riderPhone.toString() || \"\", e)}\r\n                  className=\"text-blue-600 hover:text-blue-800 flex-shrink-0 p-1 rounded hover:bg-blue-50\"\r\n                >\r\n                  <Phone className=\"w-3 h-3 sm:w-4 sm:h-4\" />\r\n                </button>\r\n              )}\r\n              {order.logStatus && (\r\n                <Badge variant=\"outline\" className=\"text-xs\">\r\n                  {getLogisticStatusDisplayName(order.logStatus)}\r\n                </Badge>\r\n              )}\r\n            </div>\r\n          </div>\r\n        )}\r\n\r\n        {/* Order Summary */}\r\n        <div className=\"mb-3\">\r\n          <div className=\"text-sm sm:text-base font-medium text-gray-900 mb-1\">\r\n            {order.totalItems} item(s) • ₹{order.totalOrderGroupAmount}\r\n          </div>\r\n          {order.orderDetails && (\r\n            <div className=\"text-xs text-gray-500 line-clamp-2\">\r\n              {order.orderDetails.slice(0, 3).map((item, idx) => (\r\n                <span key={item.orderId}>\r\n                  {item.itemName} x{item.qty}\r\n                  {idx < Math.min(order.orderDetails!.length, 3) - 1 ? \", \" : \"\"}\r\n                </span>\r\n              ))}\r\n              {order.orderDetails.length > 3 && \"...\"}\r\n            </div>\r\n          )}\r\n        </div>\r\n\r\n        {/* Status and Actions */}\r\n        <div className=\"flex flex-col gap-2\">\r\n          <div className=\"flex justify-between items-center\">\r\n            <Badge\r\n              variant={\r\n                order.orderStatus === \"Delivered\"\r\n                  ? \"default\"\r\n                  : order.orderStatus === \"Cancelled\"\r\n                    ? \"destructive\"\r\n                    : \"secondary\"\r\n              }\r\n              className=\"capitalize text-xs\"\r\n            >\r\n              {getStatusDisplayName(order.orderStatus)}\r\n            </Badge>\r\n            <Button\r\n              variant=\"outline\"\r\n              size=\"sm\"\r\n              onClick={(e) => {\r\n                e.stopPropagation()\r\n                onViewDetails(order)\r\n              }}\r\n              className=\"flex items-center gap-1 text-xs sm:text-sm\"\r\n            >\r\n              <Eye className=\"w-3 h-3 sm:w-4 sm:h-4\" />\r\n              Details\r\n            </Button>\r\n          </div>\r\n\r\n          {/* Action Buttons */}\r\n          {getActionButtons().length > 0 && <div className=\"flex gap-2 flex-wrap\">{getActionButtons()}</div>}\r\n        </div>\r\n\r\n        {/* Helper Text */}\r\n        {styling.helperText && (\r\n          <div className=\"mt-3 p-3 bg-gradient-to-r from-amber-50 via-amber-25 to-amber-50 border border-amber-200 rounded-lg text-xs text-amber-900 shadow-sm backdrop-blur-sm\">\r\n            <div className=\"flex items-start gap-2\">\r\n              <AlertCircle className=\"w-4 h-4 text-amber-600 mt-0.5 flex-shrink-0\" />\r\n              <span className=\"leading-relaxed font-medium\">{styling.helperText}</span>\r\n            </div>\r\n          </div>\r\n        )}\r\n      </CardContent>\r\n    </Card>\r\n  )\r\n}\r\n\r\n\r\n// Order Details Modal\r\ninterface OrderDetailsModalProps {\r\n  order: rNETOrder | null\r\n  onClose: () => void\r\n  onAction: (order: rNETOrder, action: string) => void\r\n}\r\n\r\nexport function OrderDetailsModal({ order, onClose, onAction }: OrderDetailsModalProps) {\r\n  if (!order) return null\r\n\r\n  const handlePhoneClick = (phoneNumber: string) => {\r\n    window.open(`tel:${phoneNumber}`, \"_self\")\r\n  }\r\n\r\n  const timeDiff = getTimeDifferenceInSeconds(order.createdAt)\r\n\r\n  return (\r\n    <Dialog open={!!order} onOpenChange={onClose}>\r\n      <DialogContent className=\"max-w-4xl max-h-[90vh] overflow-y-auto w-[95vw] sm:w-full rounded-md\">\r\n        <DialogHeader>\r\n          <DialogTitle className=\"text-lg sm:text-xl flex items-center gap-2\">\r\n            Order Details - #{order.orderGroupId}\r\n            <Badge\r\n              variant={\r\n                order.orderStatus === \"Delivered\"\r\n                  ? \"default\"\r\n                  : order.orderStatus === \"Cancelled\"\r\n                    ? \"destructive\"\r\n                    : \"secondary\"\r\n              }\r\n              className=\"capitalize\"\r\n            >\r\n              {getStatusDisplayName(order.orderStatus)}\r\n            </Badge>\r\n          </DialogTitle>\r\n        </DialogHeader>\r\n\r\n        <div className=\"space-y-6\">\r\n          {/* Order Timeline */}\r\n          <div>\r\n            <h3 className=\"font-semibold mb-3 flex items-center gap-2\">\r\n              <Clock className=\"w-4 h-4\" />\r\n              Order Timeline\r\n            </h3>\r\n            <div className=\"space-y-2 text-sm\">\r\n              <div className=\"flex justify-between\">\r\n                <span>Order Placed:</span>\r\n                <span>{dayjs(order.createdAt).format(\"ddd, DD MMM YYYY - h:mm:ss A\")}</span>\r\n              </div>\r\n              {order.acceptedTime && (\r\n                <div className=\"flex justify-between\">\r\n                  <span>Accepted:</span>\r\n                  <span>{dayjs(order.acceptedTime).format(\"ddd, DD MMM YYYY - h:mm:ss A\")}</span>\r\n                </div>\r\n              )}\r\n              {order.packedTime && (\r\n                <div className=\"flex justify-between\">\r\n                  <span>Packed:</span>\r\n                  <span>{dayjs(order.packedTime).format(\"ddd, DD MMM YYYY - h:mm:ss A\")}</span>\r\n                </div>\r\n              )}\r\n              {order.assignedTime && (\r\n                <div className=\"flex justify-between\">\r\n                  <span>Assigned:</span>\r\n                  <span>{dayjs(order.assignedTime).format(\"ddd, DD MMM YYYY - h:mm:ss A\")}</span>\r\n                </div>\r\n              )}\r\n              {order.pickedUpTime && (\r\n                <div className=\"flex justify-between\">\r\n                  <span>Picked Up:</span>\r\n                  <span>{dayjs(order.pickedUpTime).format(\"ddd, DD MMM YYYY - h:mm:ss A\")}</span>\r\n                </div>\r\n              )}\r\n              {order.deliveryStartTime && (\r\n                <div className=\"flex justify-between\">\r\n                  <span>Dispatched:</span>\r\n                  <span>{dayjs(order.deliveryStartTime).format(\"ddd, DD MMM YYYY - h:mm:ss A\")}</span>\r\n                </div>\r\n              )}\r\n              {order.deliveredTime && (\r\n                <div className=\"flex justify-between\">\r\n                  <span>Delivered:</span>\r\n                  <span>{dayjs(order.deliveredTime).format(\"ddd, DD MMM YYYY - h:mm:ss A\")}</span>\r\n                </div>\r\n              )}\r\n              <div className=\"flex justify-between font-medium\">\r\n                <span>Total Time:</span>\r\n                <span>\r\n                  {[\"Delivered\", \"Cancelled\"].includes(order.orderStatus)\r\n                    ? formatTimeElapsed(\r\n                      getTimeDifferenceInSeconds(order.createdAt, order.deliveredTime || order.updatedAt),\r\n                    )\r\n                    : formatTimeElapsed(timeDiff)}\r\n                </span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <Separator />\r\n\r\n          {/* Basic Order Info */}\r\n          <div className=\"grid grid-cols-1 sm:grid-cols-2 gap-6\">\r\n            <div>\r\n              <h3 className=\"font-semibold mb-3 flex items-center gap-2\">\r\n                <Package className=\"w-4 h-4\" />\r\n                Order Information\r\n              </h3>\r\n              <div className=\"space-y-2 text-sm\">\r\n                <div>\r\n                  <strong>Order ID:</strong> #{order.orderGroupId}\r\n                </div>\r\n                <div>\r\n                  <strong>Network:</strong> {order.networkName}\r\n                </div>\r\n                <div>\r\n                  <strong>Delivery Type:</strong> {order.deliveryType}\r\n                </div>\r\n                <div>\r\n                  <strong>Logistics Provider:</strong> {order.logisticProvider}\r\n                </div>\r\n                {order.pos !== \"none\" && order.pos !== undefined && (\r\n                  <div>\r\n                    <strong>POS:</strong> {order.pos}\r\n                  </div>\r\n                )}\r\n                {order.deliveryOtp && (\r\n                  <div>\r\n                    <strong>Delivery OTP:</strong> {order.deliveryOtp}\r\n                  </div>\r\n                )}\r\n              </div>\r\n            </div>\r\n\r\n            <div>\r\n              <h3 className=\"font-semibold mb-3 flex items-center gap-2\">\r\n                <User className=\"w-4 h-4\" />\r\n                Customer Information\r\n              </h3>\r\n              <div className=\"space-y-2 text-sm\">\r\n                <div>\r\n                  <strong>Name:</strong> {order.buyerName}\r\n                </div>\r\n                <div className=\"flex items-center gap-2\">\r\n                  <strong>Phone:</strong>\r\n                  <span>{order.buyerMobile}</span>\r\n                  <Button\r\n                    size=\"sm\"\r\n                    variant=\"outline\"\r\n                    onClick={() => handlePhoneClick(order.buyerMobile)}\r\n                    className=\"h-6 w-6 p-0\"\r\n                  >\r\n                    <Phone className=\"w-3 h-3\" />\r\n                  </Button>\r\n                </div>\r\n                <div>\r\n                  <strong>Address:</strong> {order.bAddress}\r\n                </div>\r\n                <div>\r\n                  <strong>Area:</strong> {order.bAreaName}\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <Separator />\r\n\r\n          {/* Restaurant Info */}\r\n          <div>\r\n            <h3 className=\"font-semibold mb-3 flex items-center gap-2\">\r\n              <Store className=\"w-4 h-4\" />\r\n              Restaurant Information\r\n            </h3>\r\n            <div className=\"grid grid-cols-1 sm:grid-cols-2 gap-4 text-sm\">\r\n              <div>\r\n                <div>\r\n                  <strong>Name:</strong> {order.sellerName}\r\n                </div>\r\n                <div className=\"flex items-center gap-2\">\r\n                  <strong>Phone:</strong>\r\n                  <span>{order.sellerMobile}</span>\r\n                  <Button\r\n                    size=\"sm\"\r\n                    variant=\"outline\"\r\n                    onClick={() => handlePhoneClick(order.sellerMobile)}\r\n                    className=\"h-6 w-6 p-0\"\r\n                  >\r\n                    <Phone className=\"w-3 h-3\" />\r\n                  </Button>\r\n                </div>\r\n              </div>\r\n              <div>\r\n                {order.agentName && (\r\n                  <div>\r\n                    <strong>Agent:</strong> {order.agentName}\r\n                  </div>\r\n                )}\r\n                {order.sellerMessage && (\r\n                  <div>\r\n                    <strong>Message:</strong> {order.sellerMessage}\r\n                  </div>\r\n                )}\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Delivery Partner Info */}\r\n          {order.logisticProvider === \"MP2\" && order.logisticDetails?.riderName && (\r\n            <>\r\n              <Separator />\r\n              <div>\r\n                <h3 className=\"font-semibold mb-3 flex items-center gap-2\">\r\n                  <Truck className=\"w-4 h-4\" />\r\n                  Delivery Partner\r\n                </h3>\r\n                <div className=\"text-sm space-y-2\">\r\n                  <div>\r\n                    <strong>Name:</strong> {order.logisticDetails.riderName}\r\n                  </div>\r\n                  {order.logisticDetails.riderPhone && (\r\n                    <div className=\"flex items-center gap-2\">\r\n                      <strong>Phone:</strong>\r\n                      <span>{order.logisticDetails.riderPhone}</span>\r\n                      <Button\r\n                        size=\"sm\"\r\n                        variant=\"outline\"\r\n                        onClick={() => handlePhoneClick(order.logisticDetails!.riderPhone.toString())}\r\n                        className=\"h-6 w-6 p-0\"\r\n                      >\r\n                        <Phone className=\"w-3 h-3\" />\r\n                      </Button>\r\n                    </div>\r\n                  )}\r\n                  {order.logStatus && (\r\n                    <div>\r\n                      <strong>Logistics Status:</strong>\r\n                      <Badge className=\"ml-2\">{getLogisticStatusDisplayName(order.logStatus)}</Badge>\r\n                    </div>\r\n                  )}\r\n                  {order.logisticDetails.trackingUrl && (\r\n                    <div>\r\n                      <strong>Tracking:</strong>\r\n                      <a\r\n                        href={order.logisticDetails.trackingUrl}\r\n                        target=\"_blank\"\r\n                        rel=\"noopener noreferrer\"\r\n                        className=\"ml-2 text-blue-600 hover:underline\"\r\n                      >\r\n                        Track Order\r\n                      </a>\r\n                    </div>\r\n                  )}\r\n                </div>\r\n              </div>\r\n            </>\r\n          )}\r\n\r\n          {/* Order Items */}\r\n          {order.orderDetails && order.orderDetails.length > 0 && (\r\n            <>\r\n              <Separator />\r\n              <div>\r\n                <h3 className=\"font-semibold mb-3\">Order Items</h3>\r\n                <div className=\"space-y-3\">\r\n                  {order.orderDetails.map((item) => (\r\n                    <div key={item.orderId} className=\"border rounded p-3 bg-gray-50\">\r\n                      <div className=\"flex justify-between items-start\">\r\n                        <div className=\"flex-1\">\r\n                          <div className=\"font-medium\">{item.itemName}</div>\r\n                          {item.itemRegionalLanguageName && <div className=\"text-sm text-gray-600\">{item.itemRegionalLanguageName}</div>}\r\n                          {item.variationName && (\r\n                            <div className=\"text-sm text-gray-600\">Variation: {item.variationName}</div>\r\n                          )}\r\n                          {item.addOns && (\r\n                            <div className=\"text-sm text-gray-600\">\r\n                              Add-ons:{\" \"}\r\n                              {item.addOns\r\n                                .flatMap((aog) => aog.addOnItemList.map((addon) => `${addon.name} (+₹${addon.price})`))\r\n                                .join(\", \")}\r\n                            </div>\r\n                          )}\r\n                          <div className=\"text-xs text-gray-500 mt-1\">\r\n                            {item.diet && (\r\n                              <span\r\n                                className={`inline-block w-2 h-2 rounded-full mr-1 ${item.diet === \"veg\"\r\n                                  ? \"bg-green-500\"\r\n                                  : item.diet === \"nonveg\"\r\n                                    ? \"bg-red-500\"\r\n                                    : \"bg-yellow-500\"\r\n                                  }`}\r\n                              />\r\n                            )}\r\n                            {item.diet} • {item.unit}\r\n                          </div>\r\n                        </div>\r\n                        <div className=\"text-right ml-4\">\r\n                          <div className=\"text-sm\">Qty: {item.qty}</div>\r\n                          <div className=\"text-sm\">\r\n                            ₹{item.pricePerUnit} per {item.unit}\r\n                          </div>\r\n                          <div className=\"font-semibold\">\r\n                            ₹{item.amount}\r\n                            {(item.strikeOffAmount && item.strikeOffAmount !== item.amount) && (\r\n                              <span className=\"ml-0.5 text-sm text-gray-600 line-through\">\r\n                                ₹{item.strikeOffAmount}\r\n                              </span>\r\n                            )}\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  ))}\r\n                </div>\r\n              </div>\r\n            </>\r\n          )}\r\n\r\n          <Separator />\r\n\r\n          {/* Payment Breakdown */}\r\n          <div>\r\n            <h3 className=\"font-semibold mb-3\">Payment Breakdown</h3>\r\n            <div className=\"bg-gray-50 rounded p-4 space-y-2 text-sm\">\r\n              {(order.totalItemsStrikeoffAmount && (order.totalItemsStrikeoffAmount !== order.itemsTotalAmount)) && (\r\n                <div className=\"flex justify-between\">\r\n                  <span>Item Total (strikeoff):</span>\r\n                  <span>₹{order.totalItemsStrikeoffAmount}</span>\r\n                </div>\r\n              )}\r\n              <div className=\"flex justify-between\">\r\n                <span>Item Total:</span>\r\n                <span>₹{order.itemsTotalAmount}</span>\r\n              </div>\r\n              <div className=\"flex justify-between\">\r\n                <span>Delivery Charge:</span>\r\n                <span>₹{order.totalDeliveryCharge}</span>\r\n              </div>\r\n              <div className=\"flex justify-between\">\r\n                <span>Packaging Charges:</span>\r\n                <span>₹{order.packagingCharges}</span>\r\n              </div>\r\n              <div className=\"flex justify-between\">\r\n                <span>Platform Fee:</span>\r\n                <span>₹{order.platformFee}</span>\r\n              </div>\r\n              <div className=\"flex justify-between\">\r\n                <span>Tax Amount:</span>\r\n                <span>₹{order.totalTaxAmount}</span>\r\n              </div>\r\n              <div className=\"flex justify-between text-red-600\">\r\n                <span>Discount:</span>\r\n                <span>-₹{order.totalDiscountAmount}</span>\r\n              </div>\r\n              <Separator />\r\n              <div className=\"flex justify-between font-semibold text-lg\">\r\n                <span>Total Amount:</span>\r\n                <span>₹{order.totalOrderGroupAmount}</span>\r\n              </div>\r\n              <div className=\"flex justify-between text-sm text-gray-600\">\r\n                <span>COD Amount:</span>\r\n                <span>₹{order.codAmount}</span>\r\n              </div>\r\n              {order.walletAmount > 0 && (\r\n                <div className=\"flex justify-between text-sm text-gray-600\">\r\n                  <span>Wallet Amount:</span>\r\n                  <span>₹{order.walletAmount}</span>\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n\r\n          {/* Support Tickets */}\r\n          {order.supportTickets && order.supportTickets.length > 0 && (\r\n            <>\r\n              <Separator />\r\n              <div>\r\n                <h3 className=\"font-semibold mb-3\">Support Tickets</h3>\r\n                <div className=\"space-y-3\">\r\n                  {order.supportTickets.map((ticket) => (\r\n                    <div key={ticket.ticketId} className=\"border rounded p-3\">\r\n                      <div className=\"flex justify-between items-start mb-2\">\r\n                        <div className=\"font-medium\">Ticket #{ticket.ticketId}</div>\r\n                        <Badge\r\n                          variant={\r\n                            ticket.status === \"OPEN\" ? \"destructive\" : ticket.status === \"WIP\" ? \"default\" : \"secondary\"\r\n                          }\r\n                        >\r\n                          {ticket.status}\r\n                        </Badge>\r\n                      </div>\r\n                      <div className=\"text-sm text-gray-600 mb-2\">{ticket.description}</div>\r\n                      <div className=\"text-xs text-gray-500\">\r\n                        Created: {new Date(ticket.createdDate).toLocaleString()}\r\n                      </div>\r\n                    </div>\r\n                  ))}\r\n                </div>\r\n              </div>\r\n            </>\r\n          )}\r\n\r\n          {/* Action Buttons */}\r\n          <div className=\"flex gap-2 pt-4 border-t\">\r\n            <Button variant=\"outline\" onClick={onClose}>\r\n              Close\r\n            </Button>\r\n            {order.orderStatus === \"Created\" && (\r\n              <Button variant=\"destructive\" onClick={() => onAction(order, \"Cancel Order\")}>\r\n                Cancel Order\r\n              </Button>\r\n            )}\r\n            {((order.orderStatus === \"Accepted\" || order.orderStatus === \"Packed\") && order.logisticProvider === \"MP2\") && (\r\n              <Button variant=\"default\" onClick={() => onAction(order, \"Manual Fulfillment\")}>\r\n                Switch to Manual Fulfillment\r\n              </Button>\r\n            )}\r\n            {showUpdateStatusButton(order) && (\r\n              <Button variant=\"default\" onClick={() => onAction(order, \"Update LiveOrder Status\")}>\r\n                Update to {order.orderStatus === \"Created\" ? \"Accepted\" : order.orderStatus === \"Accepted\" ? \"Packed\" : order.orderStatus === \"Packed\" ? \"Assigned\" : order.orderStatus === \"Assigned\" ? \"PickedUp\" : \"Dispatched\"} <ArrowRight className=\"w-3 h-3\" />\r\n              </Button>\r\n            )}\r\n            {(order.orderStatus === \"Dispatched\") && (\r\n              <Button variant=\"default\" className=\"bg-blue-500 hover:bg-blue-500\" onClick={() => onAction(order, \"Mark Delivered\")}>\r\n                Mark Delivered\r\n              </Button>\r\n            )}\r\n          </div>\r\n        </div>\r\n      </DialogContent>\r\n    </Dialog>\r\n  )\r\n}\r\n\r\n\r\n// Action Modal\r\ninterface ActionModalProps {\r\n  order: rNETOrder | null\r\n  actionType: string\r\n  onClose: () => void\r\n  isSubmitting: boolean\r\n  onSubmit: (formData: any) => void\r\n}\r\n\r\nexport function ActionModal({ order, actionType, onClose, isSubmitting, onSubmit }: ActionModalProps) {\r\n  const [formData, setFormData] = useState({\r\n    // manual fullfilment\r\n    lRiderName: \"\",\r\n    lRiderPhone: \"\",\r\n    lPrice: \"\",\r\n    lPotp: \"\",\r\n    lDotp: \"\",\r\n    // order cancel\r\n    reason: \"\",\r\n    // update status to packed\r\n    boxes: \"\",\r\n    bags: \"\",\r\n    // mark delivered\r\n    deliveryCode: \"\",\r\n    creditAmount: \"\",\r\n    boxesGiven: \"\",\r\n    boxesTaken: \"\"\r\n  })\r\n\r\n  const handleSubmit = (e: React.FormEvent) => {\r\n    e.preventDefault()\r\n    onSubmit(formData)\r\n  }\r\n\r\n  if (!order || !actionType) return null\r\n\r\n  const renderCancelModal = () => (\r\n    <div className=\"space-y-4\">\r\n      <div className=\"flex items-center gap-2 p-4 bg-red-50 border border-red-200 rounded\">\r\n        <AlertTriangle className=\"w-5 h-5 text-red-600\" />\r\n        <div>\r\n          <p className=\"font-medium text-red-800\">Cancel Order</p>\r\n          <p className=\"text-sm text-red-600\">\r\n            This action is irreversible. The order will be cancelled from all systems.\r\n          </p>\r\n        </div>\r\n      </div>\r\n\r\n      <div>\r\n        <Label htmlFor=\"reason\">Cancellation Reason</Label>\r\n        <Textarea\r\n          id=\"reason\"\r\n          value={formData.reason}\r\n          onChange={(e) => setFormData((prev) => ({ ...prev, reason: e.target.value }))}\r\n          placeholder=\"Enter reason for cancellation...\"\r\n          className=\"mt-1 w-72 block border-2 rounded-md\"\r\n        />\r\n      </div>\r\n\r\n      <div className=\"flex gap-2 pt-4\">\r\n        <Button variant=\"outline\" onClick={onClose} disabled={isSubmitting}>\r\n          Cancel\r\n        </Button>\r\n        <Button variant=\"destructive\" onClick={handleSubmit} disabled={isSubmitting}>\r\n          {isSubmitting ? \"Cancelling...\" : \"Confirm Cancellation\"}\r\n        </Button>\r\n      </div>\r\n    </div>\r\n  )\r\n\r\n  const renderManualFulfillmentModal = () => (\r\n    <div className=\"space-y-4\">\r\n      <div className=\"flex items-center gap-2 p-4 bg-yellow-50 border border-yellow-200 rounded\">\r\n        <AlertTriangle className=\"w-5 h-5 text-yellow-600\" />\r\n        <div>\r\n          <p className=\"font-medium text-yellow-800\">Switch to Manual Fulfillment</p>\r\n          <p className=\"text-sm text-yellow-600\">This will cancel the MP2 order and switch to manual delivery.</p>\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"grid grid-cols-1 sm:grid-cols-2 gap-4\">\r\n        <div>\r\n          <Label htmlFor=\"partnerName\">Delivery Partner Name *</Label>\r\n          <Input\r\n            id=\"partnerName\"\r\n            value={formData.lRiderName}\r\n            onChange={(e) => setFormData((prev) => ({ ...prev, lRiderName: e.target.value }))}\r\n            placeholder=\"Enter partner name\"\r\n            required\r\n          />\r\n        </div>\r\n\r\n        <div>\r\n          <Label htmlFor=\"partnerPhone\">Delivery Partner Phone *</Label>\r\n          <Input\r\n            id=\"partnerPhone\"\r\n            value={formData.lRiderPhone}\r\n            onChange={(e) => setFormData((prev) => ({ ...prev, lRiderPhone: e.target.value }))}\r\n            placeholder=\"Enter phone number\"\r\n            required\r\n          />\r\n        </div>\r\n\r\n        <div>\r\n          <Label htmlFor=\"charges\">Delivery Charges</Label>\r\n          <Input\r\n            id=\"charges\"\r\n            type=\"number\"\r\n            min={0}\r\n            value={formData.lPrice}\r\n            onChange={(e) => setFormData((prev) => ({ ...prev, lPrice: e.target.value }))}\r\n            placeholder=\"Enter charges\"\r\n          />\r\n        </div>\r\n\r\n        <div>\r\n          <Label htmlFor=\"lPotp\">Logistic Provider OTP</Label>\r\n          <Input\r\n            id=\"lPotp\"\r\n            type=\"number\"\r\n            min={0}\r\n            value={formData.lPotp}\r\n            onChange={(e) => setFormData((prev) => ({ ...prev, lPotp: e.target.value }))}\r\n            placeholder=\"Enter OTP\"\r\n          />\r\n        </div>\r\n\r\n        <div>\r\n          <Label htmlFor=\"lDotp\">Logistic Delivery OTP</Label>\r\n          <Input\r\n            id=\"lDotp\"\r\n            type=\"number\"\r\n            min={0}\r\n            value={formData.lDotp}\r\n            onChange={(e) => setFormData((prev) => ({ ...prev, lDotp: e.target.value }))}\r\n            placeholder=\"Enter OTP\"\r\n          />\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"flex gap-2 pt-4\">\r\n        <Button variant=\"outline\" onClick={onClose} disabled={isSubmitting}>\r\n          Cancel\r\n        </Button>\r\n        <Button\r\n          onClick={handleSubmit}\r\n          disabled={isSubmitting || !formData.lRiderName || !formData.lRiderPhone || !(formData.lRiderPhone.length === 10) || !Number.isInteger(Number(formData.lPrice)) || !formData.lPotp || !formData.lDotp}\r\n        >\r\n          {isSubmitting ? \"Switching...\" : \"Confirm Switch\"}\r\n        </Button>\r\n      </div>\r\n    </div>\r\n  )\r\n\r\n  const renderUpdateStatusModal = () => (\r\n    <div className=\"space-y-4\">\r\n      <div className=\"flex items-center gap-2 p-4 bg-yellow-50 border border-yellow-200 rounded\">\r\n        <AlertTriangle className=\"w-5 h-5 text-yellow-600\" />\r\n        <div>\r\n          <p className=\"text-yellow-700\">This will update the status of the order to {order.orderStatus === \"Created\" ? \"Accepted\" : order.orderStatus === \"Accepted\" ? \"Packed\" : order.orderStatus === \"Packed\" ? \"Assigned\" : order.orderStatus === \"Assigned\" ? \"PickedUp\" : \"Dispatched\"}.</p>\r\n        </div>\r\n      </div>\r\n      {/* {order.orderStatus === \"Accepted\" && (\r\n        <div className=\"grid grid-cols-1 sm:grid-cols-2 gap-4\">\r\n          <div>\r\n            <Label htmlFor=\"boxes\">Number of Boxes</Label>\r\n            <Input\r\n              id=\"boxes\"\r\n              type=\"number\"\r\n              min={0}\r\n              value={formData.boxes}\r\n              onChange={(e) => setFormData((prev) => ({ ...prev, boxes: e.target.value }))}\r\n              placeholder=\"Enter number of boxes\"\r\n            />\r\n          </div>\r\n\r\n          <div>\r\n            <Label htmlFor=\"bags\">Number of Bags</Label>\r\n            <Input\r\n              id=\"bags\"\r\n              type=\"number\"\r\n              min={0}\r\n              value={formData.bags}\r\n              onChange={(e) => setFormData((prev) => ({ ...prev, bags: e.target.value }))}\r\n              placeholder=\"Enter number of bags\"\r\n            />\r\n          </div>\r\n        </div>\r\n      )} */}\r\n\r\n      <div className=\"flex gap-2 pt-4\">\r\n        <Button variant=\"outline\" onClick={onClose} disabled={isSubmitting}>\r\n          Cancel\r\n        </Button>\r\n        <Button\r\n          onClick={handleSubmit}\r\n          disabled={isSubmitting}\r\n        >\r\n          {isSubmitting ? \"Updating...\" : \"Confirm Update\"}\r\n        </Button>\r\n      </div>\r\n    </div>\r\n  )\r\n\r\n  const renderMarkDeliveredModal = () => (\r\n    <div className=\"space-y-4\">\r\n      <div className=\"flex items-center gap-2 p-4 bg-yellow-50 border border-yellow-200 rounded\">\r\n        <AlertTriangle className=\"w-5 h-5 text-yellow-600\" />\r\n        <div>\r\n          <p className=\"font-medium text-yellow-800\">Mark Order as Delivered</p>\r\n          <p className=\"text-sm text-yellow-600\">This will mark the order as delivered.</p>\r\n        </div>\r\n      </div>\r\n      {/*\r\n      <div className=\"grid grid-cols-1 sm:grid-cols-2 gap-4\">\r\n        <div>\r\n          <Label htmlFor=\"deliveryCode\">Delivery Code</Label>\r\n          <Input\r\n            id=\"deliveryCode\"\r\n            value={formData.deliveryCode}\r\n            onChange={(e) => setFormData((prev) => ({ ...prev, deliveryCode: e.target.value }))}\r\n            placeholder=\"Enter delivery code\"\r\n          />\r\n        </div>\r\n\r\n        <div>\r\n          <Label htmlFor=\"creditAmount\">Credit Amount</Label>\r\n          <Input\r\n            id=\"creditAmount\"\r\n            type=\"number\"\r\n            value={formData.creditAmount}\r\n            onChange={(e) => setFormData((prev) => ({ ...prev, creditAmount: e.target.value }))}\r\n            placeholder=\"Enter credit amount\"\r\n          />\r\n        </div>\r\n\r\n        <div>\r\n          <Label htmlFor=\"boxesGiven\">Number of Boxes Given</Label>\r\n          <Input\r\n            id=\"boxesGiven\"\r\n            type=\"number\"\r\n            min={0}\r\n            value={formData.boxesGiven}\r\n            onChange={(e) => setFormData((prev) => ({ ...prev, boxesGiven: e.target.value }))}\r\n            placeholder=\"Enter number of boxes given\"\r\n          />\r\n        </div>\r\n\r\n        <div>\r\n          <Label htmlFor=\"boxesTaken\">Number of Boxes Taken</Label>\r\n          <Input\r\n            id=\"boxesTaken\"\r\n            type=\"number\"\r\n            min={0}\r\n            value={formData.boxesTaken}\r\n            onChange={(e) => setFormData((prev) => ({ ...prev, boxesTaken: e.target.value }))}\r\n            placeholder=\"Enter number of boxes taken\"\r\n          />\r\n        </div>\r\n      </div> */}\r\n\r\n      <div className=\"flex gap-2 pt-4\">\r\n        <Button variant=\"outline\" onClick={onClose} disabled={isSubmitting}>\r\n          Cancel\r\n        </Button>\r\n        <Button\r\n          onClick={handleSubmit}\r\n          disabled={isSubmitting}\r\n          className=\"bg-blue-500 hover:bg-blue-500\"\r\n        >\r\n          {isSubmitting ? \"Marking...\" : \"Confirm Delivery\"}\r\n        </Button>\r\n      </div>\r\n    </div>\r\n  )\r\n\r\n  return (\r\n    <Dialog open={true} onOpenChange={onClose}>\r\n      <DialogContent className=\"max-w-2xl rounded-md\">\r\n        <DialogHeader>\r\n          <DialogTitle>\r\n            {actionType === \"Cancel Order\" && \"Cancel Order\"}\r\n            {actionType === \"Manual Fulfillment\" && \"Switch to Manual Fulfillment\"}\r\n            {actionType === \"Update LiveOrder Status\" && \"Update LiveOrder Status\"}\r\n            {actionType === \"Mark Delivered\" && \"Mark Delivered\"}\r\n          </DialogTitle>\r\n        </DialogHeader>\r\n\r\n        {actionType === \"Cancel Order\" && renderCancelModal()}\r\n        {actionType === \"Manual Fulfillment\" && renderManualFulfillmentModal()}\r\n        {actionType === \"Update LiveOrder Status\" && renderUpdateStatusModal()}\r\n        {actionType === \"Mark Delivered\" && renderMarkDeliveredModal()}\r\n      </DialogContent>\r\n    </Dialog>\r\n  )\r\n}\r\n\r\n\r\nexport const getTimeDifferenceInSeconds = (orderTime: string, endTime?: string): number => {\r\n  const orderDate = new Date(orderTime)\r\n  const compareDate = endTime ? new Date(endTime) : new Date()\r\n  return Math.floor((compareDate.getTime() - orderDate.getTime()) / 1000)\r\n}\r\n\r\n\r\nexport const formatTimeElapsed = (seconds: number): string => {\r\n  const minutes = Math.floor(seconds / 60)\r\n  const hours = Math.floor(minutes / 60)\r\n  const days = Math.floor(hours / 24)\r\n\r\n  if (days > 0) {\r\n    return `${days}d ${hours % 24}h`\r\n  }\r\n  if (hours > 0) {\r\n    return `${hours}h ${minutes % 60}m`\r\n  }\r\n  return `${minutes}m`\r\n}\r\n\r\n\r\nexport const getOrderCardStyling = (order: rNETOrder) => {\r\n  const timeDiff = getTimeDifferenceInSeconds(order.createdAt)\r\n  const baseClasses = \"border rounded-lg transition-all duration-500 \"\r\n  let pulseClasses = \"\"\r\n  let bgClasses = \"bg-white hover:shadow-md \"\r\n  let helperText = \"\"\r\n\r\n  // Status-based logic according to PRD with modern animations\r\n  switch (order.orderStatus) {\r\n    case \"Created\":\r\n      if (timeDiff > 180) {\r\n        bgClasses = \"bg-gradient-to-br from-red-200 to-red-50 \"\r\n        pulseClasses = \"card-alert-breathing \"\r\n        helperText = \"🚨 URGENT: Order not yet accepted. Please inform the seller to either accept or cancel the order.\"\r\n      } else if (timeDiff > 120) {\r\n        bgClasses = \"bg-gradient-to-br from-amber-200 to-amber-50 \"\r\n        pulseClasses = \"card-alert-warning \"\r\n        helperText = \"⚠️ Order yet to be accepted. Please inform the seller again.\"\r\n      }\r\n      break\r\n\r\n    case \"Accepted\":\r\n      if (order.logisticProvider === \"MP2\") {\r\n        if (\r\n          timeDiff > 900 &&\r\n          order.logStatus &&\r\n          [\"LOG_CREATED\", \"LOG_PENDING\", \"LOG_SEARCHING_AGENT\"].includes(order.logStatus)\r\n        ) {\r\n          bgClasses = \"bg-gradient-to-br from-red-200 to-red-50 \"\r\n          pulseClasses = \"card-alert-animated \"\r\n          helperText = \"🚨 CRITICAL: Please highlight the order with mp2 team or start manual fulfilment of the order.\"\r\n        } else if (\r\n          timeDiff > 600 &&\r\n          order.logStatus &&\r\n          [\"LOG_CREATED\", \"LOG_PENDING\", \"LOG_SEARCHING_AGENT\"].includes(order.logStatus)\r\n        ) {\r\n          bgClasses = \"bg-gradient-to-br from-orange-200 to-orange-50 \"\r\n          pulseClasses = \"card-alert-warning \"\r\n          helperText = \"⚡ Please highlight the order to mp2 team and start preparing for manual deliveries.\"\r\n        } else if (timeDiff >= 600 && order.logStatus === \"LOG_AGENT_ASSIGNED\") {\r\n          pulseClasses = \"card-alert-warning \"\r\n          helperText = \"📞 Please follow up with the rider and check if he is moving toward the restaurant.\"\r\n        }\r\n      } else if (order.logisticProvider === \"SELF\" && timeDiff > 900) {\r\n        pulseClasses = \"card-alert-warning \"\r\n        helperText = \"🏪 Please follow up with the seller for self delivery.\"\r\n      }\r\n      break\r\n\r\n    case \"Packed\":\r\n      if (order.logisticProvider === \"MP2\") {\r\n        if (timeDiff > 1200 && [\"LOG_CREATED\", \"LOG_PENDING\", \"LOG_SEARCHING_AGENT\"].includes(order.logStatus)) {\r\n          bgClasses = \"bg-gradient-to-br from-red-200 to-red-50 \"\r\n          pulseClasses = \"card-alert-breathing \"\r\n          helperText = \"🚨 URGENT: Please check with the delivery partner and raise the issue with the mp2 team.\"\r\n        } else if (timeDiff > 1200 && order.logStatus === \"LOG_AGENT_ASSIGNED\") {\r\n          bgClasses = \"bg-gradient-to-br from-red-200 to-red-50 \"\r\n          pulseClasses = \"card-alert-animated \"\r\n          helperText = \"🏃‍♂️ Please follow up with the rider and check if he is moving toward the restaurant.\"\r\n        }\r\n      } else if (order.logisticProvider === \"SELF\" && timeDiff > 1200) {\r\n        pulseClasses = \"card-alert-warning \"\r\n        helperText = \"🏃‍♂️ Please follow up with the seller for self delivery.\"\r\n      }\r\n      break\r\n  }\r\n\r\n  return {\r\n    className: baseClasses + bgClasses + pulseClasses,\r\n    helperText,\r\n  }\r\n}\r\n\r\n\r\nexport const getStatusDisplayName = (status: string): string => {\r\n  switch (status) {\r\n    case \"Created\":\r\n      return \"Acceptance Pending\"\r\n    case \"Packed\":\r\n      return \"Ready for Pickup\"\r\n    default:\r\n      return status\r\n  }\r\n}\r\n\r\n\r\nexport const getLogisticStatusDisplayName = (logStatus: string): string => {\r\n  return logStatus\r\n    .replace(\"LOG_\", \"\")\r\n    .replace(\"_\", \" \")\r\n    .toLowerCase()\r\n    .split(\" \")\r\n    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))\r\n    .join(\" \")\r\n}\r\n\r\nexport const showUpdateStatusButton = (order: rNETOrder) => {\r\n  const isPos = order.pos !== \"none\" && order.pos !== undefined\r\n  const isLogisticProvider = order.logisticProvider !== \"SELF\" && order.logisticProvider !== undefined\r\n  if (isPos && isLogisticProvider) {\r\n    return (order.orderStatus === \"Created\" || order.orderStatus === \"Accepted\" || order.orderStatus === \"Packed\" || order.orderStatus === \"Assigned\" || order.orderStatus === \"PickedUp\")\r\n    // return false\r\n  }\r\n  if (!isPos && !isLogisticProvider) {\r\n    return (order.orderStatus === \"Created\" || order.orderStatus === \"Accepted\" || order.orderStatus === \"Packed\" || order.orderStatus === \"Assigned\" || order.orderStatus === \"PickedUp\")\r\n    // return (order.orderStatus === \"Created\" || order.orderStatus === \"Accepted\" || order.orderStatus === \"Assigned\" || order.orderStatus === \"PickedUp\")\r\n  }\r\n  if (isPos && !isLogisticProvider) {\r\n    return (order.orderStatus === \"Created\" || order.orderStatus === \"Accepted\" || order.orderStatus === \"Packed\" || order.orderStatus === \"Assigned\" || order.orderStatus === \"PickedUp\")\r\n    // return (order.orderStatus === \"Assigned\")\r\n  }\r\n  if (!isPos && isLogisticProvider) {\r\n    return (order.orderStatus === \"Created\" || order.orderStatus === \"Accepted\" || order.orderStatus === \"Packed\" || order.orderStatus === \"Assigned\" || order.orderStatus === \"PickedUp\")\r\n    // return (order.orderStatus === \"Created\" || order.orderStatus === \"Accepted\" || order.orderStatus === \"PickedUp\")\r\n  }\r\n  return (order.orderStatus === \"Created\" || order.orderStatus === \"Accepted\" || order.orderStatus === \"Packed\" || order.orderStatus === \"Assigned\" || order.orderStatus === \"PickedUp\")\r\n}"], "names": ["LiveOrderDashboard", "allOrders", "setAllOrders", "useState", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedOrder", "orderStatusCounts", "setOrderStatusCounts", "fetcher", "useFetcher", "actionType", "setActionType", "actionSelectedOrder", "setActionSelectedOrder", "isSubmitting", "setIsSubmitting", "toast", "useToast", "searchType", "setSearchType", "searchTerm", "setSearchTerm", "filterDate", "setFilterDate", "from", "Date", "to", "businessType", "setBusinessType", "filterL<PERSON><PERSON><PERSON><PERSON><PERSON>", "set<PERSON>ilter<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "filterStatus", "setFilterStatus", "filterLogisticStatus", "setFilterLogisticStatus", "pageSize", "setPageSize", "currentPage", "setCurrentPage", "totalElements", "setTotalElements", "date<PERSON><PERSON><PERSON>", "setDateRange", "selectedSeller", "setSelectedSeller", "debounceSearchTerm", "setDebounceSearchTerm", "useEffect", "timer", "setTimeout", "clearTimeout", "animationKey", "setAnimationKey", "isPolling", "startPolling", "stopPolling", "usePolling", "refreshOrders", "useCallback", "isAutoRefresh", "console", "log", "formData", "FormData", "append", "data", "JSON", "stringify", "submit", "method", "handleVisibilityChange", "document", "visibilityState", "addEventListener", "removeEventListener", "searchTypeFilters", "label", "value", "statusFilters", "logisticStatusFilters", "liveOrders", "completedOrders", "useMemo", "live", "filter", "order", "includes", "orderStatus", "sort", "a", "b", "createdAt", "getTime", "completed", "uniqueSellers", "Array", "Map", "map", "sellerId", "id", "name", "sellerName", "values", "stats", "total", "created", "find", "s", "status", "count", "accepted", "packed", "assigned", "dispatched", "delivered", "cancelled", "handleAction", "action", "handleSubmitAction", "actionData", "intent", "success", "orders", "errorMessage", "title", "description", "variant", "style", "backgroundColor", "color", "className", "children", "jsxs", "jsx", "<PERSON><PERSON>", "size", "onClick", "disabled", "state", "RefreshCw", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Select", "onValueChange", "SelectTrigger", "SelectValue", "placeholder", "SelectContent", "SelectItem", "Search", "Input", "onChange", "e", "target", "Popover", "PopoverTrigger", "<PERSON><PERSON><PERSON><PERSON>", "cn", "CalendarIcon", "Fragment", "format", "PopoverC<PERSON>nt", "align", "Calendar", "initialFocus", "selected", "mode", "onSelect", "range", "PopoverClose", "Label", "Filter", "Number", "length", "seller", "toString", "Math", "ceil", "_", "i", "Badge", "Clock", "transform", "transition", "index", "animationDelay", "animationFillMode", "<PERSON><PERSON><PERSON><PERSON>", "OrderCard", "onViewDetails", "onAction", "orderGroupId", "AlertCircle", "OrderDetailsModal", "onClose", "ActionModal", "onSubmit", "currentTime", "setCurrentTime", "now", "interval", "setInterval", "clearInterval", "styling", "getOrderCardStyling", "timeDiff", "getTimeDifferenceInSeconds", "isNewOrder", "audio", "Audio", "play", "catch", "warn", "pause", "src", "handlePhoneClick", "phoneNumber", "stopPropagation", "window", "open", "getActionButtons", "buttons", "push", "logisticProvider", "showUpdateStatusButton", "ArrowRight", "onMouseEnter", "currentTarget", "animationPlayState", "onMouseLeave", "supportTickets", "MessageCircleQuestionIcon", "formatTimeElapsed", "deliveredTime", "updatedAt", "Timer", "dayjs", "User", "buyerName", "buyerMobile", "Phone", "MapPin", "b<PERSON><PERSON><PERSON>", "Store", "sellerMobile", "pos", "logisticDetails", "Truck", "<PERSON><PERSON><PERSON>", "riderPhone", "logStatus", "getLogisticStatusDisplayName", "totalItems", "totalOrderGroupAmount", "orderDetails", "slice", "item", "idx", "itemName", "qty", "min", "orderId", "getStatusDisplayName", "Eye", "helperText", "Dialog", "onOpenChange", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogHeader", "DialogTitle", "acceptedTime", "packedTime", "assignedTime", "pickedUpTime", "deliveryStartTime", "Separator", "Package", "networkName", "deliveryType", "deliveryOtp", "bAreaName", "<PERSON><PERSON><PERSON>", "sellerMessage", "trackingUrl", "href", "rel", "itemRegionalLanguageName", "variationName", "addOns", "flatMap", "aog", "addOnItemList", "addon", "price", "join", "diet", "unit", "pricePerUnit", "amount", "strikeOffAmount", "totalItemsStrikeoffAmount", "itemsTotalAmount", "totalDeliveryCharge", "packagingCharges", "platformFee", "totalTaxAmount", "totalDiscountAmount", "codAmount", "walletAmount", "ticket", "ticketId", "createdDate", "toLocaleString", "setFormData", "lRiderName", "lRiderPhone", "lPrice", "lPotp", "lDotp", "reason", "boxes", "bags", "deliveryCode", "creditAmount", "boxesGiven", "boxesTaken", "handleSubmit", "preventDefault", "renderCancelModal", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "htmlFor", "Textarea", "prev", "renderManualFulfillmentModal", "required", "type", "isInteger", "renderUpdateStatusModal", "renderMarkDeliveredModal", "orderTime", "endTime", "orderDate", "compareDate", "floor", "seconds", "minutes", "hours", "days", "baseClasses", "pulseClasses", "bgClasses", "replace", "toLowerCase", "split", "word", "char<PERSON>t", "toUpperCase", "isPos", "is<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiIA,SAAwBA,qBAAqB;;AAC3C,QAAM,CAACC,WAAWC,YAAY,IAAIC,sBAAsB;AACxD,QAAM,CAACC,eAAeC,gBAAgB,IAAIF,aAAAA,SAA2B,IAAI;AACzE,QAAM,CAACG,mBAAmBC,oBAAoB,IAAIJ,aAAAA,SAA6B,CAAA,CAAE;AACjF,QAAMK,UAAUC,WAAuB;AAGvC,QAAM,CAACC,YAAYC,aAAa,IAAIR,aAAAA,SAAiB,EAAE;AACvD,QAAM,CAACS,qBAAqBC,sBAAsB,IAAIV,aAAAA,SAA2B,IAAI;AACrF,QAAM,CAACW,cAAcC,eAAe,IAAIZ,aAAAA,SAAS,KAAK;AAChD,QAAA;AAAA,IAAEa;AAAAA,EAAM,IAAIC,SAAS;AAG3B,QAAM,CAACC,YAAYC,aAAa,IAAIhB,aAAAA,SAAuF,SAAS;AACpI,QAAM,CAACiB,YAAYC,aAAa,IAAIlB,aAAAA,SAAiB,EAAE;AACvD,QAAM,CAACmB,YAAYC,aAAa,IAAIpB,sBAAoB;AAAA,IAAEqB,MAAU,oBAAAC,KAAQ;AAAA,IAAAC,IAAQ,oBAAAD,KAAA;AAAA,EAAO,CAAC;AAC5F,QAAM,CAACE,cAAcC,eAAe,IAAIzB,aAAAA,SAA4B,OAAO;AAC3E,QAAM,CAAC0B,wBAAwBC,yBAAyB,IAAI3B,aAAAA,SAA8B,EAAE;AAC5F,QAAM,CAAC4B,cAAcC,eAAe,IAAI7B,aAAAA,SAA2B,EAAE;AACrE,QAAM,CAAC8B,sBAAsBC,uBAAuB,IAAI/B,aAAAA,SAA8B,EAAE;AACxF,QAAM,CAACgC,UAAUC,WAAW,IAAIjC,aAAAA,SAAS,EAAE;AAC3C,QAAM,CAACkC,aAAaC,cAAc,IAAInC,aAAAA,SAAS,CAAC;AAChD,QAAM,CAACoC,eAAeC,gBAAgB,IAAIrC,aAAAA,SAAS,CAAC;AAGpD,QAAM,CAACsC,WAAWC,YAAY,IAAIvC,sBAAoB;AAAA,IAAEqB,MAAU,oBAAAC,KAAQ;AAAA,IAAAC,IAAQ,oBAAAD,KAAA;AAAA,EAAO,CAAC;AAC1F,QAAM,CAACkB,gBAAgBC,iBAAiB,IAAIzC,aAAAA,SAAiB,EAAE;AAG/D,QAAM,CAAC0C,oBAAoBC,qBAAqB,IAAI3C,aAAAA,SAAiB,EAAE;AACvE4C,eAAAA,UAAU,MAAM;AACR,UAAAC,QAAQC,WAAW,MAAM;AAC7BH,4BAAsB1B,UAAU;AAAA,OAC/B,GAAG;AAEN,WAAO,MAAM;AACX8B,mBAAaF,KAAK;AAAA,IACpB;AAAA,EACF,GAAG,CAAC5B,UAAU,CAAC;AAGf,QAAM,CAAC+B,cAAcC,eAAe,IAAIjD,aAAAA,SAAS,CAAC;AAG5C,QAAA;AAAA,IAAEkD;AAAAA,IAAWC;AAAAA,IAAcC;AAAAA,EAAY,IAAIC,WAAW,MAAMC,cAAc,IAAI,GAAG,IAAK;AAEtF,QAAAA,gBAAgBC,aAAY,YAACC,mBAA4B;AAC7DC,YAAQC,IAAI,sBAAsB;AAE5B,UAAAC,WAAW,IAAIC,SAAS;AACrBD,aAAAE,OAAO,UAAU,cAAc;AAClC,UAAAC,OAAO;AAAA,MAAE/C;AAAAA,MAAYE,YAAYyB;AAAAA,MAAoBvB;AAAAA,MAAYK;AAAAA,MAAcE;AAAAA,MAAwBE;AAAAA,MAAcE;AAAAA,MAAsBE;AAAAA,MAAUE;AAAAA,IAAY;AACvKyB,aAASE,OAAO,QAAQE,KAAKC,UAAUF,IAAI,CAAC;AAC5CzD,YAAQ4D,OAAON,UAAU;AAAA,MAAEO,QAAQ;AAAA,IAAO,CAAC;AAAA,EAC7C,GAAG,CAACxB,oBAAoBvB,YAAYK,cAAcE,wBAAwBE,cAAcE,sBAAsBE,UAAUE,WAAW,CAAC;AAEpIU,eAAAA,UAAU,MAAM;AACAU,kBAAA;AAAA,EAChB,GAAG,CAACZ,oBAAoBvB,YAAYK,cAAcE,wBAAwBE,cAAcE,sBAAsBE,UAAUE,WAAW,CAAC;AAEpIU,eAAAA,UAAU,MAAM;AACDO,iBAAA;AACb,WAAO,MAAMC,YAAY;AAAA,EAC3B,GAAG,CAACD,cAAcC,WAAW,CAAC;AAG9BR,eAAAA,UAAU,MAAM;AACd,UAAMuB,yBAAyBA,MAAM;AAC/B,UAAAC,SAASC,oBAAoB,WAAW;AAC7BlB,qBAAA;AAAA,MACf,OAAO;AACOC,oBAAA;AAAA,MACd;AAAA,IACF;AACSgB,aAAAE,iBAAiB,oBAAoBH,sBAAsB;AACpE,WAAO,MAAM;AACFC,eAAAG,oBAAoB,oBAAoBJ,sBAAsB;AAAA,IACzE;AAAA,EACF,GAAG,CAAChB,cAAcC,WAAW,CAAC;AAE9B,QAAMoB,oBAAoB,CACxB;AAAA,IAAEC,OAAO;AAAA,IAAYC,OAAO;AAAA,EAAU,GACtC;AAAA,IAAED,OAAO;AAAA,IAAaC,OAAO;AAAA,EAAW,GACxC;AAAA,IAAED,OAAO;AAAA,IAAYC,OAAO;AAAA,EAAU,GACtC;AAAA,IAAED,OAAO;AAAA,IAAiBC,OAAO;AAAA,EAAe,GAChD;AAAA,IAAED,OAAO;AAAA,IAAgBC,OAAO;AAAA,EAAc,GAC9C;AAAA,IAAED,OAAO;AAAA,IAAQC,OAAO;AAAA,EAAO,CAAA;AAGjC,QAAMC,gBAAgB,CACpB;AAAA,IAAEF,OAAO;AAAA,IAAWC,OAAO;AAAA,EAAU,GACrC;AAAA,IAAED,OAAO;AAAA,IAAYC,OAAO;AAAA,EAAW,GACvC;AAAA,IAAED,OAAO;AAAA,IAAUC,OAAO;AAAA,EAAS,GACnC;AAAA,IAAED,OAAO;AAAA,IAAYC,OAAO;AAAA,EAAW,GACvC;AAAA,IAAED,OAAO;AAAA,IAAaC,OAAO;AAAA,EAAW,GACxC;AAAA,IAAED,OAAO;AAAA,IAAcC,OAAO;AAAA,EAAa,GAC3C;AAAA,IAAED,OAAO;AAAA,IAAaC,OAAO;AAAA,EAAY,GACzC;AAAA,IAAED,OAAO;AAAA,IAAaC,OAAO;AAAA,EAAY,CAAA;AAG3C,QAAME,wBAAwB,CAC5B;AAAA,IAAEH,OAAO;AAAA,IAAcC,OAAO;AAAA,EAAa,GAC3C;AAAA,IAAED,OAAO;AAAA,IAAgBC,OAAO;AAAA,EAAe,GAC/C;AAAA,IAAED,OAAO;AAAA,IAAiBC,OAAO;AAAA,EAAgB,GACjD;AAAA,IAAED,OAAO;AAAA,IAAkBC,OAAO;AAAA,EAAiB,GACnD;AAAA,IAAED,OAAO;AAAA,IAAiBC,OAAO;AAAA,EAAgB,GACjD;AAAA,IAAED,OAAO;AAAA,IAAeC,OAAO;AAAA,EAAc,GAC7C;AAAA,IAAED,OAAO;AAAA,IAAeC,OAAO;AAAA,EAAc,GAC7C;AAAA,IAAED,OAAO;AAAA,IAAuBC,OAAO;AAAA,EAAsB,GAC7D;AAAA,IAAED,OAAO;AAAA,IAAqBC,OAAO;AAAA,EAAoB,GACzD;AAAA,IAAED,OAAO;AAAA,IAAiBC,OAAO;AAAA,EAAgB,CAAA;AAInD,QAAM;AAAA,IAAEG;AAAAA,IAAYC;AAAAA,EAAgB,IAAIC,qBAAQ,MAAM;AACpD,UAAMC,OAAOlF,uCAAWmF,OAAQC,WAAU,CAAC,CAAC,aAAa,WAAW,EAAEC,SAASD,MAAME,WAAW,GAC7FC,KAAK,CAACC,GAAGC,MAAM,IAAIjE,KAAKgE,EAAEE,SAAS,EAAEC,YAAY,IAAInE,KAAKiE,EAAEC,SAAS,EAAEC;AAE1E,UAAMC,YAAY5F,uCAAWmF,OAAQC,WAAU,CAAC,aAAa,WAAW,EAAEC,SAASD,MAAME,WAAW;AAEpG,WAAO;AAAA,MAAEP,YAAYG;AAAAA,MAAMF,iBAAiBY;AAAAA,IAAU;AAAA,EACxD,GAAG,CAAC5F,SAAS,CAAC;AAGd,QAAM6F,gBAAgBC,MAAMvE,KAC1B,IAAIwE,IAAI/F,uCAAWgG,IAAKZ,WAAU,CAACA,MAAMa,UAAU;AAAA,IAAEC,IAAId,MAAMa;AAAAA,IAAUE,MAAMf,MAAMgB;AAAAA,EAAW,CAAC,EAAE,EAAEC,QACvG;AAGA,QAAMC,QAAQ;AAAA,IACZC,OAAOjE;AAAAA,IACPkE,WAASnG,uBAAkBoG,KAAMC,OAAMA,EAAEC,WAAW,SAAS,MAApDtG,mBAAuDuG,UAAS;AAAA,IACzEC,YAAUxG,uBAAkBoG,KAAMC,OAAMA,EAAEC,WAAW,UAAU,MAArDtG,mBAAwDuG,UAAS;AAAA,IAC3EE,UAAQzG,uBAAkBoG,KAAMC,OAAMA,EAAEC,WAAW,QAAQ,MAAnDtG,mBAAsDuG,UAAS;AAAA,IACvEG,YAAU1G,uBAAkBoG,KAAMC,OAAMA,EAAEC,WAAW,UAAU,MAArDtG,mBAAwDuG,UAAS;AAAA,IAC3EI,cAAY3G,uBAAkBoG,KAAMC,OAAMA,EAAEC,WAAW,YAAY,MAAvDtG,mBAA0DuG,UAAS;AAAA,IAC/EK,aAAW5G,uBAAkBoG,KAAMC,OAAMA,EAAEC,WAAW,WAAW,MAAtDtG,mBAAyDuG,UAAS;AAAA,IAC7EM,aAAW7G,uBAAkBoG,KAAMC,OAAMA,EAAEC,WAAW,WAAW,MAAtDtG,mBAAyDuG,UAAS;AAAA,EAC/E;AAEM,QAAAO,eAAeA,CAAC/B,OAAkBgC,YAAmB;AACzDxG,2BAAuBwE,KAAK;AAC5B1E,kBAAc0G,OAAM;AAAA,EACtB;AAEM,QAAAC,qBAAsBxD,cAAkB;AACtC,UAAAyD,aAAa,IAAIxD,SAAS;AACrBwD,eAAAvD,OAAO,UAAUtD,UAAU;AAC3B6G,eAAAvD,OAAO,QAAQE,KAAKC,UAAU;AAAA,MAAEkB,OAAOzE;AAAAA,MAAqBkD;AAAAA,IAAS,CAAC,CAAC;AAClFtD,YAAQ4D,OAAOmD,YAAY;AAAA,MAAElD,QAAQ;AAAA,IAAO,CAAC;AAC7CtD,oBAAgB,IAAI;AAAA,EACtB;AAEAgC,eAAAA,UAAU,MAAM;;AACV,UAAAvC,MAAAA,QAAQyD,SAARzD,gBAAAA,IAAcgH,YAAW,gBAAgB;AACvC,WAAAhH,MAAAA,QAAQyD,SAARzD,gBAAAA,IAAciH,SAAS;AACjBjH,UAAAA,OAAAA,MAAAA,QAAAyD,SAAAzD,gBAAAA,IAAMyD,SAANzD,gBAAAA,IAAYkH,UAASxH,aAAaM,QAAQyD,KAAKA,KAAKyD,MAAM,IAAIxH,aAAa,CAAA,CAAE;AAC7EM,UAAAA,OAAAA,MAAAA,QAAAyD,SAAAzD,gBAAAA,IAAMyD,SAANzD,gBAAAA,IAAY+B,iBAAgBC,iBAAiBhC,QAAQyD,KAAKA,KAAK1B,aAAa,IAAIC,iBAAiB,CAAC;AAClGhC,UAAAA,OAAAA,MAAAA,QAAAyD,SAAAzD,gBAAAA,IAAMyD,SAANzD,gBAAAA,IAAYF,qBAAoBC,qBAAqBC,QAAQyD,KAAKA,KAAK3D,iBAAiB,IAAIC,qBAAqB,CAAA,CAAE;AAC3HqC,0BAAkB,EAAE;AAAA,MACX,aAAApC,MAAAA,QAAQyD,SAARzD,gBAAAA,IAAciH,aAAY,OAAO;AAClC7D,gBAAAC,KAAIrD,MAAAA,QAAQyD,SAARzD,gBAAAA,IAAcmH,YAAY;AAChC3G,cAAA;AAAA,UACJ4G,OAAO;AAAA,UACPC,cAAarH,aAAQyD,SAARzD,mBAAcmH;AAAAA,UAC3BG,SAAS;AAAA,QACX,CAAC;AAAA,MACH;AAAA,IACF;AACI,UAAAtH,aAAQyD,SAARzD,mBAAcgH,YAAW,gBAAgB;AACvC,WAAAhH,aAAQyD,SAARzD,mBAAciH,SAAS;AACnBzG,cAAA;AAAA,UACJ4G,OAAO;AAAA,UACPC,aAAa;AAAA,UACbE,OAAO;AAAA,YACLC,iBAAiB;AAAA,YACjBC,OAAO;AAAA,UACT;AAAA,QACF,CAAC;AACDpH,+BAAuB,IAAI;AAC3BF,sBAAc,EAAE;AAChBI,wBAAgB,KAAK;AACrB0C,sBAAc,KAAK;AAAA,MACV,aAAAjD,aAAQyD,SAARzD,mBAAciH,aAAY,OAAO;AAClC7D,gBAAAC,KAAIrD,aAAQyD,SAARzD,mBAAcmH,YAAY;AACtC5G,wBAAgB,KAAK;AACfC,cAAA;AAAA,UACJ4G,OAAO;AAAA,UACPC,cAAarH,aAAQyD,SAARzD,mBAAcmH;AAAAA,UAC3BG,SAAS;AAAA,QACX,CAAC;AAAA,MACH;AAAA,IACF;AACI,UAAAtH,aAAQyD,SAARzD,mBAAcgH,YAAW,2BAA2B;AAClD,WAAAhH,aAAQyD,SAARzD,mBAAciH,SAAS;AACnBzG,cAAA;AAAA,UACJ4G,OAAO;AAAA,UACPC,aAAa;AAAA,UACbE,OAAO;AAAA,YACLC,iBAAiB;AAAA,YACjBC,OAAO;AAAA,UACT;AAAA,QACF,CAAC;AACDpH,+BAAuB,IAAI;AAC3BF,sBAAc,EAAE;AAChBI,wBAAgB,KAAK;AACrB0C,sBAAc,KAAK;AAAA,MACV,aAAAjD,aAAQyD,SAARzD,mBAAciH,aAAY,OAAO;AAClC7D,gBAAAC,KAAIrD,aAAQyD,SAARzD,mBAAcmH,YAAY;AACtC5G,wBAAgB,KAAK;AACfC,cAAA;AAAA,UACJ4G,OAAO;AAAA,UACPC,cAAarH,aAAQyD,SAARzD,mBAAcmH;AAAAA,UAC3BG,SAAS;AAAA,QACX,CAAC;AAAA,MACH;AAAA,IACF;AACI,UAAAtH,aAAQyD,SAARzD,mBAAcgH,YAAW,kBAAkB;AACzC,WAAAhH,aAAQyD,SAARzD,mBAAciH,SAAS;AACnBzG,cAAA;AAAA,UACJ4G,OAAO;AAAA,UACPC,aAAa;AAAA,UACbE,OAAO;AAAA,YACLC,iBAAiB;AAAA,YACjBC,OAAO;AAAA,UACT;AAAA,QACF,CAAC;AACDpH,+BAAuB,IAAI;AAC3BF,sBAAc,EAAE;AAChBI,wBAAgB,KAAK;AACrB0C,sBAAc,KAAK;AAAA,MACV,aAAAjD,aAAQyD,SAARzD,mBAAciH,aAAY,OAAO;AAClC7D,gBAAAC,KAAIrD,aAAQyD,SAARzD,mBAAcmH,YAAY;AACtC5G,wBAAgB,KAAK;AACfC,cAAA;AAAA,UACJ4G,OAAO;AAAA,UACPC,cAAarH,aAAQyD,SAARzD,mBAAcmH;AAAAA,UAC3BG,SAAS;AAAA,QACX,CAAC;AAAA,MACH;AAAA,IACF;AACI,UAAAtH,aAAQyD,SAARzD,mBAAcgH,YAAW,sBAAsB;AAC7C,WAAAhH,aAAQyD,SAARzD,mBAAciH,SAAS;AACnBzG,cAAA;AAAA,UACJ4G,OAAO;AAAA,UACPC,aAAa;AAAA,UACbE,OAAO;AAAA,YACLC,iBAAiB;AAAA,YACjBC,OAAO;AAAA,UACT;AAAA,QACF,CAAC;AACDpH,+BAAuB,IAAI;AAC3BF,sBAAc,EAAE;AAChBI,wBAAgB,KAAK;AACrB0C,sBAAc,KAAK;AAAA,MACV,aAAAjD,aAAQyD,SAARzD,mBAAciH,aAAY,OAAO;AAClC7D,gBAAAC,KAAIrD,aAAQyD,SAARzD,mBAAcmH,YAAY;AACtC5G,wBAAgB,KAAK;AACfC,cAAA;AAAA,UACJ4G,OAAO;AAAA,UACPC,cAAarH,aAAQyD,SAARzD,mBAAcmH;AAAAA,UAC3BG,SAAS;AAAA,QACX,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF,GAAG,CAACtH,QAAQyD,IAAI,CAAC;AAEjB,+CACG,OAAI;AAAA,IAAAiE,WAAU;AAAA,IACbC,UAACC,kCAAA,KAAA,OAAA;AAAA,MAAIF,WAAU;AAAA,MAEbC,UAAA,CAAAE,kCAAA,IAAC;QAAIH,WAAU;AAAA,QACbC,UAACC,kCAAA,KAAA,OAAA;AAAA,UAAIF,WAAU;AAAA,UACbC,UAAA,CAACE,kCAAA,IAAA,MAAA;AAAA,YAAGH,WAAU;AAAA,YAA8CC,UAAoB;AAAA,UAAA,CAAA,GAChFC,kCAAA,KAAC,OAAI;AAAA,YAAAF,WAAU;AAAA,YACbC,UAAA,CAAAC,kCAAA,KAACE,QAAA;AAAA,cACCR,SAAQ;AAAA,cACRS,MAAK;AAAA,cACLC,SAASA,MAAM/E,cAAc,KAAK;AAAA,cAClCgF,WAAWjI,QAAQkI,UAAU,aAAalI,QAAQkI,UAAU,mBAAiBlI,aAAQyD,SAARzD,mBAAcgH,YAAW;AAAA,cACtGU,WAAU;AAAA,cAEVC,UAAA,CAAAE,kCAAA,IAACM,WAAU;AAAA,gBAAAT,WAAW,YAAY1H,QAAQkI,UAAU,aAAalI,QAAQkI,UAAU,mBAAiBlI,aAAQyD,SAARzD,mBAAcgH,YAAW,iBAAiB,iBAAiB,EAAE;AAAA,cAAI,CAAA,IACnKhH,QAAQkI,UAAU,aAAalI,QAAQkI,UAAU,mBAAiBlI,aAAQyD,SAARzD,mBAAcgH,YAAW,iBAAiB,kBAAkB,SAAA;AAAA,YAAA,CAClI,GACAY,kCAAA,KAAC,OAAI;AAAA,cAAAF,WAAU;AAAA,cACbC,UAAA,CAAAE,kCAAA,IAAC;gBAAIH,WAAW,wBAAwB7E,YAAY,+BAA+B,aAAa;AAAA,cAAI,CAAA,GAAE,iBACxFA,YAAY,OAAO,KAAA;AAAA,YACnC,CAAA,CAAA;AAAA,UACF,CAAA,CAAA;AAAA,QACF,CAAA;AAAA,MACF,CAAA,GAGAgF,kCAAA,IAACO,MAAK;AAAA,QAAAV,WAAU;AAAA,QACdC,UAAAE,kCAAA,IAACQ,aAAY;AAAA,UAAAX,WAAU;AAAA,UACrBC,UAAAC,kCAAA,KAAC,OAAI;AAAA,YAAAF,WAAU;AAAA,YAEbC,UAAA,CAACC,kCAAA,KAAA,OAAA;AAAA,cAAIF,WAAU;AAAA,cACbC,UAAA,CAAAE,kCAAA,IAAC,OAAI;AAAA,gBAAAH,WAAU;AAAA,gBACbC,UAAAC,kCAAA,KAACU,QAAO;AAAA,kBAAAjE,OAAO3D;AAAAA,kBAAY6H,eAAgBlE,WAA6B1D,cAAc0D,KAAK;AAAA,kBACzFsD,UAAA,CAAAE,kCAAA,IAACW,eACC;AAAA,oBAAAb,UAAAE,kCAAA,IAACY,aAAY;AAAA,sBAAAC,aAAY;AAAA,oBAAY,CAAA;AAAA,kBACvC,CAAA,yCACCC,eACE;AAAA,oBAAAhB,UAAAxD,kBAAkBsB,IAAKb,YACrBiD,kCAAAA,IAAAe,YAAA;AAAA,sBAA8BvE,OAAOO,OAAOP;AAAAA,sBAC1CsD,UAAO/C,OAAAR;AAAAA,oBAAA,GADOQ,OAAOP,KAExB,CACD;AAAA,kBACH,CAAA,CAAA;AAAA,gBACF,CAAA;AAAA,cACF,CAAA,GAEAuD,kCAAA,KAAC,OAAI;AAAA,gBAAAF,WAAU;AAAA,gBACbC,UAAA,CAACE,kCAAA,IAAAgB,QAAA;AAAA,kBAAOnB,WAAU;AAAA,gBAA2E,CAAA,GAC7FG,kCAAA,IAACiB,OAAA;AAAA,kBACCJ,aAAahI,eAAe,SAAS,8BAA+BA,eAAe,iBAAiBA,eAAe,iBAAkB,qBAAqB;AAAA,kBAC1J2D,OAAOzD;AAAAA,kBACPmI,UAAWC,OAAMnI,cAAcmI,EAAEC,OAAO5E,KAAK;AAAA,kBAC7CqD,WAAU;AAAA,gBAAA,CACZ,CAAA;AAAA,cACF,CAAA,GAECG,kCAAA,IAAA,OAAA;AAAA,gBAAIH,WAAU;AAAA,gBACbC,iDAACuB,SACC;AAAA,kBAAAvB,UAAA,CAACE,kCAAA,IAAAsB,gBAAA;AAAA,oBAAeC,SAAO;AAAA,oBACrBzB,UAAAC,kCAAA,KAACE,QAAA;AAAA,sBACCnC,IAAG;AAAA,sBACH2B,SAAQ;AAAA,sBACRI,WAAW2B,GACT,8CACA,CAACvI,WAAWE,QAAQ,uBACtB;AAAA,sBAEA2G,UAAA,CAAAE,kCAAAA,IAACyB,UAAa,EAAA,IACbxI,yCAAYE,QACXF,WAAWI,KAEN0G,kCAAAA,KAAA2B,kBAAAA,UAAA;AAAA,wBAAA5B,UAAA,CAAO6B,OAAA1I,WAAWE,MAAM,WAAW,GAAE,OAAIwI,OAAO1I,WAAWI,IAAI,WAAW,CAAA;AAAA,sBAC7E,CAAA,IAEAsI,OAAO1I,WAAWE,MAAM,WAAW,IAGrC6G,kCAAA,IAAC;wBAAKF,UAAW;AAAA,sBAAA,CAAA,CAAA;AAAA,oBAErB,CAAA;AAAA,kBACF,CAAA,GACCC,kCAAA,KAAA6B,gBAAA;AAAA,oBAAe/B,WAAU;AAAA,oBAAagC,OAAM;AAAA,oBAC3C/B,UAAA,CAAAE,kCAAA,IAAC8B,YAAA;AAAA,sBACCC,cAAY;AAAA,sBACZC,UAAU5H;AAAAA,sBACV6H,MAAK;AAAA,sBACLC,UAAWC,WAAiC;AACtC,4BAAA,EAACA,+BAAOhJ,MAAM;AACLkB,qCAAA;AAAA,0BACXlB,MAAMgJ,MAAMhJ;AAAAA,0BACZE,IAAI8I,MAAM9I,MAAM;AAAA,wBAClB,CAAC;AAAA,sBACH;AAAA,oBAAA,CACF,GACA2G,kCAAA,IAACoC,cAAa;AAAA,sBAAAvC,WAAU;AAAA,sBACtBC,UAAAE,kCAAA,IAACC,QAAA;AAAA,wBACCR,SAAQ;AAAA,wBACRI,WAAU;AAAA,wBACVM,SAASA,MAAMjH,cAAckB,SAAS;AAAA,wBACvC0F,UAAA;AAAA,sBAED,CAAA;AAAA,oBACF,CAAA,CAAA;AAAA,kBACF,CAAA,CAAA;AAAA,gBACF,CAAA;AAAA,cACF,CAAA,CAAA;AAAA,YACF,CAAA,GAGAC,kCAAA,KAAC,OAAI;AAAA,cAAAF,WAAU;AAAA,cACbC,UAAA,CAACC,kCAAA,KAAA,OAAA;AAAA,gBAAIF,WAAU;AAAA,gBACbC,UAAA,CAACE,kCAAA,IAAAqC,OAAA;AAAA,kBAAMxC,WAAU;AAAA,kBAAsBC,UAAa;AAAA,gBAAA,CAAA,GACpDC,kCAAA,KAACU;kBAAOjE,OAAOlD;AAAAA,kBAAcoH,eAAgBlE,WAA6BjD,gBAAgBiD,KAAK;AAAA,kBAC7FsD,UAAA,CAAAE,kCAAA,IAACW,eACC;AAAA,oBAAAb,UAAAE,kCAAA,IAACY,aAAY;AAAA,sBAAAC,aAAY;AAAA,oBAAuB,CAAA;AAAA,kBAClD,CAAA,0CACCC,eACC;AAAA,oBAAAhB,UAAA,CAAAE,kCAAA,IAACe,YAAuB;AAAA,sBAAAvE,OAAM;AAAA,sBAAQsD,UAAA;AAAA,uBAAtB,OAAoC,GACnDE,kCAAA,IAAAe,YAAA;AAAA,sBAAuBvE,OAAM;AAAA,sBAAQsD;uBAAtB,OAAgC,CAAA;AAAA,kBAClD,CAAA,CAAA;AAAA,gBACF,CAAA,CAAA;AAAA,cACF,CAAA,GAEAC,kCAAA,KAAC,OAAI;AAAA,gBAAAF,WAAU;AAAA,gBACbC,UAAA,CAACE,kCAAA,IAAAqC,OAAA;AAAA,kBAAMxC,WAAU;AAAA,kBAAsBC,UAAiB;AAAA,gBAAA,CAAA,GACxDC,kCAAA,KAACU,QAAA;AAAA,kBACCjE,OAAOhD;AAAAA,kBACPkH,eAAgBlE,WAA0B/C,0BAA0B+C,KAAK;AAAA,kBAEzEsD,UAAA,CAAAE,kCAAA,IAACW,eACC;AAAA,oBAAAb,UAAAE,kCAAA,IAACY,aAAY;AAAA,sBAAAC,aAAY;AAAA,oBAAkB,CAAA;AAAA,kBAC7C,CAAA,0CACCC,eACC;AAAA,oBAAAhB,UAAA,CAAAE,kCAAA,IAACe,YAAqB;AAAA,sBAAAvE,OAAM;AAAA,sBAAMsD,UAAA;AAAA,uBAAlB,KAAqB,GACpCE,kCAAA,IAAAe,YAAA;AAAA,sBAAsBvE,OAAM;AAAA,sBAAOsD;uBAApB,MAAwB,CAAA;AAAA,kBAC1C,CAAA,CAAA;AAAA,gBAAA,CACF,CAAA;AAAA,cACF,CAAA,GAEAC,kCAAA,KAAC,OAAI;AAAA,gBAAAF,WAAU;AAAA,gBACbC,UAAA,CAACE,kCAAA,IAAAqC,OAAA;AAAA,kBAAMxC,WAAU;AAAA,kBAAsBC,UAAM;AAAA,gBAAA,CAAA,GAC7CC,kCAAA,KAACU;kBAAOjE,OAAO9C;AAAAA,kBAAcgH,eAAgBlE,WAAuB7C,gBAAgB6C,KAAK;AAAA,kBACvFsD,UAAA,CAAAE,kCAAA,IAACW,eACC;AAAA,oBAAAb,UAAAE,kCAAA,IAACY,aAAY;AAAA,sBAAAC,aAAY;AAAA,oBAAgB,CAAA;AAAA,kBAC3C,CAAA,yCACCC,eACE;AAAA,oBAAAhB,UAAArD,cAAcmB,IAAKb,YACjBiD,kCAAAA,IAAAe,YAAA;AAAA,sBAA8BvE,OAAOO,OAAOP;AAAAA,sBAC1CsD,UAAO/C,OAAAR;AAAAA,oBAAA,GADOQ,OAAOP,KAExB,CACD;AAAA,kBACH,CAAA,CAAA;AAAA,gBACF,CAAA,CAAA;AAAA,cACF,CAAA,GAEAuD,kCAAA,KAAC,OAAI;AAAA,gBAAAF,WAAU;AAAA,gBACbC,UAAA,CAACE,kCAAA,IAAAqC,OAAA;AAAA,kBAAMxC,WAAU;AAAA,kBAAsBC,UAAe;AAAA,gBAAA,CAAA,GACtDC,kCAAA,KAACU,QAAA;AAAA,kBACCjE,OAAO5C;AAAAA,kBACP8G,eAAgBlE,WAA0B3C,wBAAwB2C,KAAK;AAAA,kBAEvEsD,UAAA,CAAAE,kCAAA,IAACW,eACC;AAAA,oBAAAb,UAAAE,kCAAA,IAACY,aAAY;AAAA,sBAAAC,aAAY;AAAA,oBAAgB,CAAA;AAAA,kBAC3C,CAAA,yCACCC,eACE;AAAA,oBAAAhB,UAAApD,sBAAsBkB,IAAKb,YACzBiD,kCAAAA,IAAAe,YAAA;AAAA,sBAA8BvE,OAAOO,OAAOP;AAAAA,sBAC1CsD,UAAO/C,OAAAR;AAAAA,oBAAA,GADOQ,OAAOP,KAExB,CACD;AAAA,kBACH,CAAA,CAAA;AAAA,gBAAA,CACF,CAAA;AAAA,cACF,CAAA,GAEAwD,kCAAA,IAAC,OAAI;AAAA,gBAAAH,WAAU;AAAA,gBACbC,UAAAC,kCAAA,KAACE,QAAA;AAAA,kBACCE,SAASA,MAAM;AACbrH,kCAAc,SAAS;AACvBE,kCAAc,EAAE;AAChByB,0CAAsB,EAAE;AACVvB,kCAAA;AAAA,sBAAEC,MAAU,oBAAAC;sBAAQC,IAAI,oBAAID,KAAK;AAAA,oBAAE,CAAC;AACrCiB,iCAAA;AAAA,sBAAElB,MAAU,oBAAAC;sBAAQC,IAAI,oBAAID,KAAK;AAAA,oBAAE,CAAC;AACjDG,oCAAgB,OAAO;AACvBE,8CAA0B,EAAE;AAC5BE,oCAAgB,EAAE;AAClBE,4CAAwB,EAAE;AAC1BE,gCAAY,EAAE;AACdE,mCAAe,CAAC;AAChBM,sCAAkB,EAAE;AAAA,kBACtB;AAAA,kBACAsF,WAAU;AAAA,kBAEVC,UAAA,CAACE,kCAAA,IAAAsC,QAAA;AAAA,oBAAOzC,WAAU;AAAA,kBAAe,CAAA,GAAE,eAAA;AAAA,gBAErC,CAAA;AAAA,cACF,CAAA,CAAA;AAAA,YACF,CAAA,CAAA;AAAA,UACF,CAAA;AAAA,QACF,CAAA;AAAA,MACF,CAAA,GAGAG,kCAAA,IAACO,MAAK;AAAA,QAAAV,WAAU;AAAA,QACdC,UAAAE,kCAAA,IAACQ,aAAY;AAAA,UAAAX,WAAU;AAAA,UACrBC,UAAAC,kCAAA,KAAC,OAAI;AAAA,YAAAF,WAAU;AAAA,YACbC,UAAA,CAAAC,kCAAA,KAACU,QAAA;AAAA,cACCjE,OAAOlC;AAAAA,cACPoG,eAAgBlE,WAAU;AACxBjC,kCAAkBiC,KAAK;AACV3E,6BAAAD,uCAAWmF,OAAQC,WAAUA,MAAMa,aAAa0E,OAAO/F,KAAK,EAAE;AAC1DrC,kCAAAvC,uCAAWmF,OAAQC,WAAUA,MAAMa,aAAa0E,OAAO/F,KAAK,GAAGgG,WAAU,CAAC;AAAA,cAC7F;AAAA,cAEA1C,UAAA,CAAAE,kCAAA,IAACW;gBAAcd,WAAU;AAAA,gBACvBC,gDAACc,aAAY;AAAA,kBAAAC,aAAY;AAAA,gBAAgB,CAAA;AAAA,cAC3C,CAAA,GACCb,kCAAA,IAAAc,eAAA;AAAA,gBACEhB,UAAcrC,cAAAG,IAAK6E,YACjB1C,kCAAAA,KAAAgB,YAAA;AAAA,kBAA2BvE,OAAOiG,OAAO3E,GAAG4E,SAC1C;AAAA,kBAAA5C,UAAA,CAAO2C,OAAA3E,IAAI,OAAO2E,OAAO1E,IAAA;AAAA,gBADX,GAAA0E,OAAO3E,EAExB,CACD;AAAA,cACH,CAAA,CAAA;AAAA,YAAA,CACF,GACCiC,kCAAA,KAAAU,QAAA;AAAA,cAAOjE,OAAO1C,SAAS4I,SAAS;AAAA,cAAGhC,eAAgBlE,WAAUzC,YAAYwI,OAAO/F,KAAK,CAAC;AAAA,cACrFsD,UAAA,CAAAE,kCAAA,IAACW,eAAc;AAAA,gBAAAd,WAAU;AAAA,gBACvBC,UAAAE,kCAAAA,IAACY,cAAY,CAAA;AAAA,cACf,CAAA,0CACCE,eACC;AAAA,gBAAAhB,UAAA,CAACE,kCAAA,IAAAe,YAAA;AAAA,kBAAWvE,OAAM;AAAA,kBAAKsD,UAAW;AAAA,gBAAA,CAAA,GACjCE,kCAAA,IAAAe,YAAA;AAAA,kBAAWvE,OAAM;AAAA,kBAAKsD,UAAW;AAAA,gBAAA,CAAA,GACjCE,kCAAA,IAAAe,YAAA;AAAA,kBAAWvE,OAAM;AAAA,kBAAMsD,UAAY;AAAA,gBAAA,CAAA,CAAA;AAAA,cACtC,CAAA,CAAA;AAAA,YACF,CAAA,GACCC,kCAAA,KAAAU,QAAA;AAAA,cAAOjE,OAAOxC,YAAY0I,SAAS;AAAA,cAAGhC,eAAgBlE,WAAUvC,eAAesI,OAAO/F,KAAK,CAAC;AAAA,cAC3FsD,UAAA,CAAAE,kCAAA,IAACW,eAAc;AAAA,gBAAAd,WAAU;AAAA,gBACvBC,UAAAE,kCAAAA,IAACY,cAAY,CAAA;AAAA,cACf,CAAA,GACAZ,kCAAA,IAACc;gBACEhB,UAAMpC,MAAAvE,KAAK;AAAA,kBAAEqJ,QAAQG,KAAKC,KAAK1I,gBAAgBJ,QAAQ;AAAA,gBAAK,GAAA,CAAC+I,GAAGC,MAC/D/C,kCAAAA,KAACgB;kBAAmBvE,OAAOsG,EAAEJ,SAAY;AAAA,kBAAA5C,UAAA,CAAA,SACjCgD,IAAI,CAAA;AAAA,gBADK,GAAAA,CAEjB,CACD;AAAA,cACH,CAAA,CAAA;AAAA,YACF,CAAA,CAAA;AAAA,UACF,CAAA;AAAA,QACF,CAAA;AAAA,MACF,CAAA,GAGA/C,kCAAA,KAAC,OAAI;AAAA,QAAAF,WAAU;AAAA,QACbC,UAAA,CAAAE,kCAAA,IAACO;UAAKV,WAAU;AAAA,UACdC,UAACC,kCAAA,KAAAS,aAAA;AAAA,YAAYX,WAAU;AAAA,YACrBC,UAAA,CAACE,kCAAA,IAAA,OAAA;AAAA,cAAIH,WAAU;AAAA,cAAmCC,UAAK;AAAA,YAAA,CAAA,GACtDE,kCAAA,IAAA,OAAA;AAAA,cAAIH,WAAU;AAAA,cAAiDC,gBAAM3B;AAAAA,YAAM,CAAA,CAAA;AAAA,UAC9E,CAAA;AAAA,QACF,CAAA,yCACCoC,MAAK;AAAA,UAAAV,WAAU;AAAA,UACdC,UAACC,kCAAA,KAAAS,aAAA;AAAA,YAAYX,WAAU;AAAA,YACrBC,UAAA,CAACE,kCAAA,IAAA,OAAA;AAAA,cAAIH,WAAU;AAAA,cAAmCC,UAAO;AAAA,YAAA,CAAA,GACxDE,kCAAA,IAAA,OAAA;AAAA,cAAIH,WAAU;AAAA,cAAmDC,gBAAM1B;AAAAA,YAAQ,CAAA,CAAA;AAAA,UAClF,CAAA;AAAA,QACF,CAAA,yCACCmC,MAAK;AAAA,UAAAV,WAAU;AAAA,UACdC,UAACC,kCAAA,KAAAS,aAAA;AAAA,YAAYX,WAAU;AAAA,YACrBC,UAAA,CAACE,kCAAA,IAAA,OAAA;AAAA,cAAIH,WAAU;AAAA,cAAmCC,UAAQ;AAAA,YAAA,CAAA,GACzDE,kCAAA,IAAA,OAAA;AAAA,cAAIH,WAAU;AAAA,cAAmDC,gBAAMrB;AAAAA,YAAS,CAAA,CAAA;AAAA,UACnF,CAAA;AAAA,QACF,CAAA,yCACC8B,MAAK;AAAA,UAAAV,WAAU;AAAA,UACdC,UAACC,kCAAA,KAAAS,aAAA;AAAA,YAAYX,WAAU;AAAA,YACrBC,UAAA,CAACE,kCAAA,IAAA,OAAA;AAAA,cAAIH,WAAU;AAAA,cAAmCC,UAAM;AAAA,YAAA,CAAA,GACvDE,kCAAA,IAAA,OAAA;AAAA,cAAIH,WAAU;AAAA,cAAmDC,gBAAMpB;AAAAA,YAAO,CAAA,CAAA;AAAA,UACjF,CAAA;AAAA,QACF,CAAA,yCACC6B,MAAK;AAAA,UAAAV,WAAU;AAAA,UACdC,UAACC,kCAAA,KAAAS,aAAA;AAAA,YAAYX,WAAU;AAAA,YACrBC,UAAA,CAACE,kCAAA,IAAA,OAAA;AAAA,cAAIH,WAAU;AAAA,cAAmCC,UAAQ;AAAA,YAAA,CAAA,GACzDE,kCAAA,IAAA,OAAA;AAAA,cAAIH,WAAU;AAAA,cAAmDC,gBAAMnB;AAAAA,YAAS,CAAA,CAAA;AAAA,UACnF,CAAA;AAAA,QACF,CAAA,yCACC4B,MAAK;AAAA,UAAAV,WAAU;AAAA,UACdC,UAACC,kCAAA,KAAAS,aAAA;AAAA,YAAYX,WAAU;AAAA,YACrBC,UAAA,CAACE,kCAAA,IAAA,OAAA;AAAA,cAAIH,WAAU;AAAA,cAAmCC,UAAU;AAAA,YAAA,CAAA,GAC3DE,kCAAA,IAAA,OAAA;AAAA,cAAIH,WAAU;AAAA,cAAmDC,gBAAMlB;AAAAA,YAAW,CAAA,CAAA;AAAA,UACrF,CAAA;AAAA,QACF,CAAA,yCACC2B,MAAK;AAAA,UAAAV,WAAU;AAAA,UACdC,UAACC,kCAAA,KAAAS,aAAA;AAAA,YAAYX,WAAU;AAAA,YACrBC,UAAA,CAACE,kCAAA,IAAA,OAAA;AAAA,cAAIH,WAAU;AAAA,cAAmCC,UAAS;AAAA,YAAA,CAAA,GAC1DE,kCAAA,IAAA,OAAA;AAAA,cAAIH,WAAU;AAAA,cAAkDC,gBAAMjB;AAAAA,YAAU,CAAA,CAAA;AAAA,UACnF,CAAA;AAAA,QACF,CAAA,yCACC0B,MAAK;AAAA,UAAAV,WAAU;AAAA,UACdC,UAACC,kCAAA,KAAAS,aAAA;AAAA,YAAYX,WAAU;AAAA,YACrBC,UAAA,CAACE,kCAAA,IAAA,OAAA;AAAA,cAAIH,WAAU;AAAA,cAAmCC,UAAS;AAAA,YAAA,CAAA,GAC1DE,kCAAA,IAAA,OAAA;AAAA,cAAIH,WAAU;AAAA,cAAgDC,gBAAMhB;AAAAA,YAAU,CAAA,CAAA;AAAA,UACjF,CAAA;AAAA,QACF,CAAA,CAAA;AAAA,MACF,CAAA,GAGAiB,kCAAA,KAAC,OAAI;AAAA,QAAAF,WAAU;AAAA,QACbC,UAAA,CAAAE,kCAAA,IAAC;UAAIH,WAAU;AAAA,UACbC,UAACC,kCAAA,KAAA,MAAA;AAAA,YAAGF,WAAU;AAAA,YACZC,UAAA,CAACE,kCAAA,IAAA,OAAA;AAAA,cAAIH,WAAU;AAAA,aAAkD,GAAE,eAEnEG,kCAAA,IAAC+C,OAAM;AAAA,cAAAtD,SAAQ;AAAA,cAAYI,WAAU;AAAA,cAClCC,UAAA5F,iBAAiBgE,MAAMW,YAAYX,MAAMY,cAAc;AAAA,YAC1D,CAAA,CAAA;AAAA,UACF,CAAA;AAAA,QACF,CAAA,IAECnC,yCAAY6F,YAAW,IACtBxC,kCAAAA,IAACO,MACC;AAAA,UAAAT,UAAAE,kCAAA,IAACQ,aAAY;AAAA,YAAAX,WAAU;AAAA,YACrBC,UAAAC,kCAAA,KAAC,OAAI;AAAA,cAAAF,WAAU;AAAA,cACbC,UAAA,CAACE,kCAAA,IAAAgD,OAAA;AAAA,gBAAMnD,WAAU;AAAA,cAAoC,CAAA,GACpDG,kCAAA,IAAA,KAAA;AAAA,gBAAEH,WAAU;AAAA,gBAAsBC,UAAc;AAAA,cAAA,CAAA,GAChDE,kCAAA,IAAA,KAAA;AAAA,gBAAEH,WAAU;AAAA,gBAAUC,UAAmD;AAAA,cAAA,CAAA,CAAA;AAAA,YAC5E,CAAA;AAAA,UACF,CAAA;AAAA,SACF,IAEAE,kCAAA,IAAC,OAAA;AAAA,UAECH,WAAU;AAAA,UACVH,OAAO;AAAA,YACLuD,WAAW;AAAA,YACXC,YAAY;AAAA,UACd;AAAA,UAECpD,UAAYnD,yCAAAiB,IAAI,CAACZ,OAAOmG,UACvBnD,kCAAA,IAAC,OAAA;AAAA,YAECH,WAAU;AAAA,YACVH,OAAO;AAAA,cACL0D,gBAAgB,GAAGD,QAAQ,EAAE;AAAA,cAC7BE,mBAAmB;AAAA,cACnBC,YAAY;AAAA,YACd;AAAA,YAEAxD,UAAAE,kCAAA,IAACuD,WAAA;AAAA,cACCvG;AAAAA,cACAwG,eAAexL;AAAAA,cACfyL,UAAU1E;AAAAA,YACZ,CAAA;AAAA,aAZK,GAAG/B,MAAM0G,YAAY,IAAI5I,YAAY,EAa5C;AAAA,QACD,GAvBI,eAAeA,YAAY,EAwBlC,CAAA;AAAA,MAEJ,CAAA,GAGAiF,kCAAA,KAAC,OAAI;AAAA,QAAAF,WAAU;AAAA,QACbC,UAAA,CAAAE,kCAAA,IAAC;UAAIH,WAAU;AAAA,UACbC,UAACC,kCAAA,KAAA,MAAA;AAAA,YAAGF,WAAU;AAAA,YACZC,UAAA,CAACE,kCAAA,IAAA,OAAA;AAAA,cAAIH,WAAU;AAAA,aAAmC,GAAE,oBAEpDG,kCAAA,IAAC+C,OAAM;AAAA,cAAAtD,SAAQ;AAAA,cAAYI,WAAU;AAAA,cAClCC,UAAM5B,MAAAW,YAAYX,MAAMY,aAAa;AAAA,YACxC,CAAA,CAAA;AAAA,UACF,CAAA;AAAA,QACF,CAAA,IAEClC,mDAAiB4F,YAAW,IAC3BxC,kCAAAA,IAACO,MACC;AAAA,UAAAT,UAAAE,kCAAA,IAACQ,aAAY;AAAA,YAAAX,WAAU;AAAA,YACrBC,UAAAC,kCAAA,KAAC,OAAI;AAAA,cAAAF,WAAU;AAAA,cACbC,UAAA,CAACE,kCAAA,IAAA2D,aAAA;AAAA,gBAAY9D,WAAU;AAAA,cAAoC,CAAA,GAC1DG,kCAAA,IAAA,KAAA;AAAA,gBAAEH,WAAU;AAAA,gBAAsBC,UAAmB;AAAA,cAAA,CAAA,GACrDE,kCAAA,IAAA,KAAA;AAAA,gBAAEH,WAAU;AAAA,gBAAUC,UAAsD;AAAA,cAAA,CAAA,CAAA;AAAA,YAC/E,CAAA;AAAA,UACF,CAAA;AAAA,QACF,CAAA,IAECE,kCAAA,IAAA,OAAA;AAAA,UAAIH,WAAU;AAAA,UACbC,UAAAE,kCAAA,IAAC,OAAA;AAAA,YAECH,WAAU;AAAA,YACVH,OAAO;AAAA,cACLuD,WAAW;AAAA,cACXC,YAAY;AAAA,YACd;AAAA,YAECpD,UAAiBlD,mDAAAgB,IAAI,CAACZ,OAAOmG,UAC5BnD,kCAAA,IAAC,OAAA;AAAA,cAECH,WAAU;AAAA,cACVH,OAAO;AAAA,gBACL0D,gBAAgB,GAAGD,QAAQ,EAAE;AAAA,gBAC7BE,mBAAmB;AAAA,gBACnBC,YAAY;AAAA,cACd;AAAA,cAEAxD,gDAACyD,WAAU;AAAA,gBAAAvG;AAAAA,gBAAcwG,eAAexL;AAAAA,gBAAkByL,UAAU1E;AAAAA,cAAc,CAAA;AAAA,eAR7E,GAAG/B,MAAM0G,YAAY,IAAI5I,YAAY,EAS5C;AAAA,aAlBG,oBAAoBA,YAAY,EAoBvC;AAAA,QACF,CAAA,CAAA;AAAA,MAEJ,CAAA,GAGAkF,kCAAA,IAAC4D,mBAAkB;AAAA,QAAA5G,OAAOjF;AAAAA,QAAe8L,SAASA,MAAM7L,iBAAiB,IAAI;AAAA,QAAGyL,UAAU1E;AAAAA,MAAc,CAAA,GAGxGiB,kCAAA,IAAC8D,aAAA;AAAA,QACC9G,OAAOzE;AAAAA,QACPF;AAAAA,QACAwL,SAASA,MAAM;AACbrL,iCAAuB,IAAI;AAC3BF,wBAAc,EAAE;AAAA,QAClB;AAAA,QACAG;AAAAA,QACAsL,UAAU9E;AAAAA,MAAA,CACZ,CAAA;AAAA,IACF,CAAA;AAAA,EACF,CAAA;AAEJ;AAUO,SAASsE,UAAU;AAAA,EAAEvG;AAAAA,EAAOwG;AAAAA,EAAeC;AAAS,GAAmB;AAC5E,QAAM,CAACO,aAAaC,cAAc,IAAInM,aAAAA,SAASsB,KAAK8K,KAAK;AAGzDxJ,eAAAA,UAAU,MAAM;AACV,QAAA,CAAC,CAAC,aAAa,WAAW,EAAEuC,SAASD,MAAME,WAAW,GAAG;AACrD,YAAAiH,WAAWC,YAAY,MAAM;AAClBH,uBAAA7K,KAAK8K,KAAK;AAAA,SACxB,GAAI;AACA,aAAA,MAAMG,cAAcF,QAAQ;AAAA,IACrC;AAAA,EACF,GAAG,CAACnH,MAAME,WAAW,CAAC;AAEhB,QAAAoH,UAAUC,oBAAoBvH,KAAK;AACnC,QAAAwH,WAAWC,2BAA2BzH,MAAMM,SAAS;AAC3D,QAAMoH,aAAaF,YAAY;AAG/B9J,eAAAA,UAAU,MAAM;AACd,QAAIgK,YAAY;AACR,YAAAC,QAAQ,IAAIC,MAAM,gBAAgB;AACxCD,YAAME,KAAK,EAAEC,MAAMvJ,QAAQwJ,IAAI;AAC/B,aAAO,MAAM;AACXJ,cAAMK,MAAM;AACZL,cAAMM,MAAM;AAAA,MACd;AAAA,IACF;AAAA,EACF,GAAG,CAACP,UAAU,CAAC;AAET,QAAAQ,mBAAmBA,CAACC,aAAqBhE,MAAwB;AACrEA,MAAEiE,gBAAgB;AAClBC,WAAOC,KAAK,OAAOH,WAAW,IAAI,OAAO;AAAA,EAC3C;AAEA,QAAMI,mBAAmBA,MAAM;AAC7B,UAAMC,UAAU,CAAC;AAEb,QAAAxI,MAAME,gBAAgB,WAAW;AAC3BsI,cAAAC,KACNzF,kCAAA,IAACC,QAAA;AAAA,QAECR,SAAQ;AAAA,QACRS,MAAK;AAAA,QACLC,SAAUgB,OAAM;AACdA,YAAEiE,gBAAgB;AAClB3B,mBAASzG,OAAO,cAAc;AAAA,QAChC;AAAA,QACA6C,WAAU;AAAA,QACXC,UAAA;AAAA,MAAA,GARK,QAUN,CACF;AAAA,IACF;AACK,QAAA9C,MAAME,gBAAgB,eAAeF,MAAME,gBAAgB,eAAgBF,MAAM0I,qBAAqB,OAAO;AACxGF,cAAAC,KACNzF,kCAAA,IAACC,QAAA;AAAA,QAECR,SAAQ;AAAA,QACRS,MAAK;AAAA,QACLC,SAAUgB,OAAM;AACdA,YAAEiE,gBAAgB;AAClB3B,mBAASzG,OAAO,oBAAoB;AAAA,QACtC;AAAA,QACA6C,WAAU;AAAA,QACXC,UAAA;AAAA,MAAA,GARK,QAUN,CACF;AAAA,IACF;AACI,QAAA6F,uBAAuB3I,KAAK,GAAG;AACzBwI,cAAAC,KACN1F,kCAAA,KAACE,QAAA;AAAA,QAECR,SAAQ;AAAA,QACRS,MAAK;AAAA,QACLC,SAAUgB,OAAM;AACdA,YAAEiE,gBAAgB;AAClB3B,mBAASzG,OAAO,yBAAyB;AAAA,QAC3C;AAAA,QACA6C,WAAU;AAAA,QACXC,UAAA,CAAA,cACY9C,MAAME,gBAAgB,YAAY,aAAaF,MAAME,gBAAgB,aAAa,WAAWF,MAAME,gBAAgB,WAAW,aAAaF,MAAME,gBAAgB,aAAa,aAAa,cAAa,KAAC8C,kCAAA,IAAC4F,YAAW;AAAA,UAAA/F,WAAU;AAAA,QAAU,CAAA,CAAA;AAAA,MAAA,GAThP,cAUN,CACF;AAAA,IACF;AACI,QAAA7C,MAAME,gBAAgB,cAAc;AAC9BsI,cAAAC,KACNzF,kCAAA,IAACC,QAAA;AAAA,QAECR,SAAQ;AAAA,QACRS,MAAK;AAAA,QACLC,SAAUgB,OAAM;AACdA,YAAEiE,gBAAgB;AAClB3B,mBAASzG,OAAO,gBAAgB;AAAA,QAClC;AAAA,QACA6C,WAAU;AAAA,QACXC,UAAA;AAAA,MAAA,GARK,eAUN,CACF;AAAA,IACF;AAEO,WAAA0F;AAAAA,EACT;AAGE,SAAAxF,kCAAAA,IAACO,MAAA;AAAA,IACCV,WAAW,GAAGyE,QAAQzE,SAAS;AAAA,IAC/BM,SAASA,MAAMqD,cAAcxG,KAAK;AAAA,IAClC6I,cAAe1E,OAAM;AACjBA,QAAA2E,cAAcpG,MAAMqG,qBAAqB;AAAA,IAC7C;AAAA,IACAC,cAAe7E,OAAM;AACjBA,QAAA2E,cAAcpG,MAAMqG,qBAAqB;AAAA,IAC7C;AAAA,IAEAjG,UAAAC,kCAAA,KAACS,aAAY;AAAA,MAAAX,WAAU;AAAA,MAErBC,UAAA,CAACC,kCAAA,KAAA,OAAA;AAAA,QAAIF,WAAU;AAAA,QACbC,UAAA,CAACC,kCAAA,KAAA,OAAA;AAAA,UAAIF,WAAU;AAAA,UACbC,UAAA,CAACC,kCAAA,KAAA,OAAA;AAAA,YAAIF,WAAU;AAAA,YAAqCC,UAAA,CAAA,KAAE9C,MAAM0G,YAAA;AAAA,WAAa,GACxEgB,cACE1E,kCAAA,IAAA+C,OAAA;AAAA,YAAMtD,SAAQ;AAAA,YAAYI,WAAU;AAAA,YAA2IC,UAEhL;AAAA,UAAA,CAAA,GAED9C,MAAMiJ,kBAAkBjJ,MAAMiJ,eAAezD,SAAS,KACrDzC,kCAAA,KAACgD,OAAM;AAAA,YAAAtD,SAAQ;AAAA,YAAcI,WAAU;AAAA,YACrCC,UAAA,CAACE,kCAAA,IAAAkG,uBAAA;AAAA,cAA0BrG,WAAU;AAAA,YAAU,CAAA,GAC9C7C,MAAMiJ,eAAezD,MAAA;AAAA,UACxB,CAAA,CAAA;AAAA,QAEJ,CAAA,GACAzC,kCAAA,KAAC,OAAI;AAAA,UAAAF,WAAU;AAAA,UACbC,UAAA,CAACE,kCAAA,IAAAgD,OAAA;AAAA,YAAMnD,WAAU;AAAA,UAAwB,CAAA,GACxC,CAAC,WAAW,EAAE5C,SAASD,MAAME,WAAW,IACrCiJ,kBAAkB1B,2BAA2BzH,MAAMM,WAAWN,MAAMoJ,iBAAiBpJ,MAAMqJ,SAAS,CAAC,IACrG,CAAC,WAAW,EAAEpJ,SAASD,MAAME,WAAW,IAAIiJ,kBAAkB1B,2BAA2BzH,MAAMM,WAAWN,MAAMoJ,iBAAiBpJ,MAAMqJ,SAAS,CAAC,IAAIF,kBAAkB3B,QAAQ,CAAA;AAAA,QACrL,CAAA,CAAA;AAAA,MACF,CAAA,GAGAzE,kCAAA,KAAC,OAAI;AAAA,QAAAF,WAAU;AAAA,QACbC,UAAA,CAACC,kCAAA,KAAA,OAAA;AAAA,UAAIF,WAAU;AAAA,UACbC,UAAA,CAACE,kCAAA,IAAAsG,OAAA;AAAA,YAAMzG,WAAU;AAAA,UAAoD,CAAA,GACrEG,kCAAA,IAAC,QAAK;AAAA,YAAAH,WAAU;AAAA,YACbC,UAAAyG,MAAMvJ,MAAMM,SAAS,EAAEqE,OAAO,oBAAoB;AAAA,UACrD,CAAA,CAAA;AAAA,QACF,CAAA,GACA5B,kCAAA,KAAC,OAAI;AAAA,UAAAF,WAAU;AAAA,UACbC,UAAA,CAACE,kCAAA,IAAAwG,MAAA;AAAA,YAAK3G,WAAU;AAAA,UAAoD,CAAA,GACnEG,kCAAA,IAAA,QAAA;AAAA,YAAKH,WAAU;AAAA,YAA6CC,gBAAM2G;AAAAA,UAAU,CAAA,GAC7EzG,kCAAA,IAAC,UAAA;AAAA,YACCG,SAAUgB,OAAM+D,iBAAiBlI,MAAM0J,aAAavF,CAAC;AAAA,YACrDtB,WAAU;AAAA,YAEVC,UAAAE,kCAAA,IAAC2G,OAAM;AAAA,cAAA9G,WAAU;AAAA,YAAwB,CAAA;AAAA,UAAA,CAC3C,CAAA;AAAA,QACF,CAAA,GACAE,kCAAA,KAAC,OAAI;AAAA,UAAAF,WAAU;AAAA,UACbC,UAAA,CAACE,kCAAA,IAAA4G,QAAA;AAAA,YAAO/G,WAAU;AAAA,UAA2D,CAAA,GAC5EG,kCAAA,IAAA,QAAA;AAAA,YAAKH,WAAU;AAAA,YAAgBC,gBAAM+G;AAAAA,UAAS,CAAA,CAAA;AAAA,QACjD,CAAA,CAAA;AAAA,MACF,CAAA,yCAGC,OAAI;AAAA,QAAAhH,WAAU;AAAA,QACbC,UAACC,kCAAA,KAAA,OAAA;AAAA,UAAIF,WAAU;AAAA,UACbC,UAAA,CAACE,kCAAA,IAAA8G,OAAA;AAAA,YAAMjH,WAAU;AAAA,UAAoD,CAAA,GACpEG,kCAAA,IAAA,QAAA;AAAA,YAAKH,WAAU;AAAA,YAA6CC,gBAAM9B;AAAAA,UAAW,CAAA,GAC9EgC,kCAAA,IAAC,UAAA;AAAA,YACCG,SAAUgB,OAAM+D,iBAAiBlI,MAAM+J,cAAc5F,CAAC;AAAA,YACtDtB,WAAU;AAAA,YAEVC,UAAAE,kCAAA,IAAC2G,OAAM;AAAA,cAAA9G,WAAU;AAAA,YAAwB,CAAA;AAAA,UAAA,CAC3C,yCACCkD,OAAM;AAAA,YAAAtD,SAAQ;AAAA,YAAUI,WAAU;AAAA,YAChCC,gBAAM4F;AAAAA,UACT,CAAA,GACE1I,MAAMgK,QAAQ,UAAUhK,MAAMgK,QAAQ,UACrChH,kCAAA,IAAA+C,OAAA;AAAA,YAAMtD,SAAQ;AAAA,YAAUI,WAAU;AAAA,YAChCC,gBAAMkH;AAAAA,UACT,CAAA,CAAA;AAAA,QAEJ,CAAA;AAAA,MACF,CAAA,GAGChK,MAAM0I,qBAAqB,SAAS1I,MAAMiK,mBACzCjH,kCAAA,IAAC,OAAI;AAAA,QAAAH,WAAU;AAAA,QACbC,UAAAC,kCAAA,KAAC,OAAI;AAAA,UAAAF,WAAU;AAAA,UACbC,UAAA,CAACE,kCAAA,IAAAkH,OAAA;AAAA,YAAMrH,WAAU;AAAA,UAAoD,CAAA,yCACpE,QAAK;AAAA,YAAAA,WAAU;AAAA,YAA+BC,UAAA9C,MAAMiK,gBAAgBE;AAAAA,UAAU,CAAA,GAC9EnK,MAAMiK,gBAAgBG,cACrBpH,kCAAAA,IAAC,UAAA;AAAA,YACCG,SAAUgB;;AAAM+D,wCAAiBlI,WAAMiK,oBAANjK,mBAAuBoK,WAAW1E,eAAc,IAAIvB,CAAC;AAAA;AAAA,YACtFtB,WAAU;AAAA,YAEVC,UAAAE,kCAAA,IAAC2G,OAAM;AAAA,cAAA9G,WAAU;AAAA,YAAwB,CAAA;AAAA,UAC3C,CAAA,GAED7C,MAAMqK,aACLrH,kCAAAA,IAAC+C,OAAM;AAAA,YAAAtD,SAAQ;AAAA,YAAUI,WAAU;AAAA,YAChCC,UAAAwH,6BAA6BtK,MAAMqK,SAAS;AAAA,UAC/C,CAAA,CAAA;AAAA,QAEJ,CAAA;AAAA,MACF,CAAA,GAIFtH,kCAAA,KAAC,OAAI;AAAA,QAAAF,WAAU;AAAA,QACbC,UAAA,CAACC,kCAAA,KAAA,OAAA;AAAA,UAAIF,WAAU;AAAA,UACZC,UAAA,CAAM9C,MAAAuK,YAAW,gBAAavK,MAAMwK,qBAAA;AAAA,QACvC,CAAA,GACCxK,MAAMyK,gBACJ1H,kCAAAA,KAAA,OAAA;AAAA,UAAIF,WAAU;AAAA,UACZC,UAAA,CAAM9C,MAAAyK,aAAaC,MAAM,GAAG,CAAC,EAAE9J,IAAI,CAAC+J,MAAMC,QACzC7H,kCAAAA,KAAC,QACE;AAAA,YAAAD,UAAA,CAAK6H,KAAAE,UAAS,MAAGF,KAAKG,KACtBF,MAAMjF,KAAKoF,IAAI/K,MAAMyK,aAAcjF,QAAQ,CAAC,IAAI,IAAI,OAAO,EAAA;AAAA,UAFnD,GAAAmF,KAAKK,OAGhB,CACD,GACAhL,MAAMyK,aAAajF,SAAS,KAAK,KAAA;AAAA,QACpC,CAAA,CAAA;AAAA,MAEJ,CAAA,GAGAzC,kCAAA,KAAC,OAAI;AAAA,QAAAF,WAAU;AAAA,QACbC,UAAA,CAACC,kCAAA,KAAA,OAAA;AAAA,UAAIF,WAAU;AAAA,UACbC,UAAA,CAAAE,kCAAA,IAAC+C,OAAA;AAAA,YACCtD,SACEzC,MAAME,gBAAgB,cAClB,YACAF,MAAME,gBAAgB,cACpB,gBACA;AAAA,YAER2C,WAAU;AAAA,YAETC,UAAAmI,qBAAqBjL,MAAME,WAAW;AAAA,UAAA,CACzC,GACA6C,kCAAA,KAACE,QAAA;AAAA,YACCR,SAAQ;AAAA,YACRS,MAAK;AAAA,YACLC,SAAUgB,OAAM;AACdA,gBAAEiE,gBAAgB;AAClB5B,4BAAcxG,KAAK;AAAA,YACrB;AAAA,YACA6C,WAAU;AAAA,YAEVC,UAAA,CAACE,kCAAA,IAAAkI,KAAA;AAAA,cAAIrI,WAAU;AAAA,YAAwB,CAAA,GAAE,SAAA;AAAA,UAAA,CAE3C,CAAA;AAAA,QACF,CAAA,GAGC0F,iBAAA,EAAmB/C,SAAS,2CAAM,OAAI;AAAA,UAAA3C,WAAU;AAAA,UAAwBC,UAAAyF,iBAAA;AAAA,QAAmB,CAAA,CAAA;AAAA,MAC9F,CAAA,GAGCjB,QAAQ6D,cACNnI,kCAAAA,IAAA,OAAA;AAAA,QAAIH,WAAU;AAAA,QACbC,UAAAC,kCAAA,KAAC,OAAI;AAAA,UAAAF,WAAU;AAAA,UACbC,UAAA,CAACE,kCAAA,IAAA2D,aAAA;AAAA,YAAY9D,WAAU;AAAA,UAA8C,CAAA,GACpEG,kCAAA,IAAA,QAAA;AAAA,YAAKH,WAAU;AAAA,YAA+BC,kBAAQqI;AAAAA,UAAW,CAAA,CAAA;AAAA,QACpE,CAAA;AAAA,MACF,CAAA,CAAA;AAAA,IAEJ,CAAA;AAAA,EAAA,CACF;AAEJ;AAUO,SAASvE,kBAAkB;AAAA,EAAE5G;AAAAA,EAAO6G;AAAAA,EAASJ;AAAS,GAA2B;;AAClF,MAAA,CAACzG,MAAc,QAAA;AAEb,QAAAkI,mBAAoBC,iBAAwB;AAChDE,WAAOC,KAAK,OAAOH,WAAW,IAAI,OAAO;AAAA,EAC3C;AAEM,QAAAX,WAAWC,2BAA2BzH,MAAMM,SAAS;AAGzD,SAAA0C,kCAAAA,IAACoI,QAAO;AAAA,IAAA9C,MAAM,CAAC,CAACtI;AAAAA,IAAOqL,cAAcxE;AAAAA,IACnC/D,UAAAC,kCAAA,KAACuI,eAAc;AAAA,MAAAzI,WAAU;AAAA,MACvBC,UAAA,CAAAE,kCAAA,IAACuI,cACC;AAAA,QAAAzI,UAAAC,kCAAA,KAACyI,aAAY;AAAA,UAAA3I,WAAU;AAAA,UAA6CC,UAAA,CAAA,qBAChD9C,MAAM0G,cACxB1D,kCAAAA,IAAC+C,OAAA;AAAA,YACCtD,SACEzC,MAAME,gBAAgB,cAClB,YACAF,MAAME,gBAAgB,cACpB,gBACA;AAAA,YAER2C,WAAU;AAAA,YAETC,UAAAmI,qBAAqBjL,MAAME,WAAW;AAAA,UAAA,CACzC,CAAA;AAAA,QACF,CAAA;AAAA,MACF,CAAA,GAEA6C,kCAAA,KAAC,OAAI;AAAA,QAAAF,WAAU;AAAA,QAEbC,UAAA,CAAAC,kCAAA,KAAC,OACC;AAAA,UAAAD,UAAA,CAACC,kCAAA,KAAA,MAAA;AAAA,YAAGF,WAAU;AAAA,YACZC,UAAA,CAACE,kCAAA,IAAAgD,OAAA;AAAA,cAAMnD,WAAU;AAAA,YAAU,CAAA,GAAE,gBAAA;AAAA,UAE/B,CAAA,GACAE,kCAAA,KAAC,OAAI;AAAA,YAAAF,WAAU;AAAA,YACbC,UAAA,CAACC,kCAAA,KAAA,OAAA;AAAA,cAAIF,WAAU;AAAA,cACbC,UAAA,CAAAE,kCAAA,IAAC;gBAAKF,UAAa;AAAA,cAAA,CAAA,GACnBE,kCAAA,IAAC;gBAAMF,UAAMyG,MAAAvJ,MAAMM,SAAS,EAAEqE,OAAO,8BAA8B;AAAA,cAAE,CAAA,CAAA;AAAA,YACvE,CAAA,GACC3E,MAAMyL,gBACJ1I,kCAAAA,KAAA,OAAA;AAAA,cAAIF,WAAU;AAAA,cACbC,UAAA,CAAAE,kCAAA,IAAC;gBAAKF,UAAS;AAAA,cAAA,CAAA,GACfE,kCAAA,IAAC;gBAAMF,UAAMyG,MAAAvJ,MAAMyL,YAAY,EAAE9G,OAAO,8BAA8B;AAAA,cAAE,CAAA,CAAA;AAAA,YAC1E,CAAA,GAED3E,MAAM0L,cACJ3I,kCAAAA,KAAA,OAAA;AAAA,cAAIF,WAAU;AAAA,cACbC,UAAA,CAAAE,kCAAA,IAAC;gBAAKF,UAAO;AAAA,cAAA,CAAA,GACbE,kCAAA,IAAC;gBAAMF,UAAMyG,MAAAvJ,MAAM0L,UAAU,EAAE/G,OAAO,8BAA8B;AAAA,cAAE,CAAA,CAAA;AAAA,YACxE,CAAA,GAED3E,MAAM2L,gBACJ5I,kCAAAA,KAAA,OAAA;AAAA,cAAIF,WAAU;AAAA,cACbC,UAAA,CAAAE,kCAAA,IAAC;gBAAKF,UAAS;AAAA,cAAA,CAAA,GACfE,kCAAA,IAAC;gBAAMF,UAAMyG,MAAAvJ,MAAM2L,YAAY,EAAEhH,OAAO,8BAA8B;AAAA,cAAE,CAAA,CAAA;AAAA,YAC1E,CAAA,GAED3E,MAAM4L,gBACJ7I,kCAAAA,KAAA,OAAA;AAAA,cAAIF,WAAU;AAAA,cACbC,UAAA,CAAAE,kCAAA,IAAC;gBAAKF,UAAU;AAAA,cAAA,CAAA,GAChBE,kCAAA,IAAC;gBAAMF,UAAMyG,MAAAvJ,MAAM4L,YAAY,EAAEjH,OAAO,8BAA8B;AAAA,cAAE,CAAA,CAAA;AAAA,YAC1E,CAAA,GAED3E,MAAM6L,qBACJ9I,kCAAAA,KAAA,OAAA;AAAA,cAAIF,WAAU;AAAA,cACbC,UAAA,CAAAE,kCAAA,IAAC;gBAAKF,UAAW;AAAA,cAAA,CAAA,GACjBE,kCAAA,IAAC;gBAAMF,UAAMyG,MAAAvJ,MAAM6L,iBAAiB,EAAElH,OAAO,8BAA8B;AAAA,cAAE,CAAA,CAAA;AAAA,YAC/E,CAAA,GAED3E,MAAMoJ,iBACJrG,kCAAAA,KAAA,OAAA;AAAA,cAAIF,WAAU;AAAA,cACbC,UAAA,CAAAE,kCAAA,IAAC;gBAAKF,UAAU;AAAA,cAAA,CAAA,GAChBE,kCAAA,IAAC;gBAAMF,UAAMyG,MAAAvJ,MAAMoJ,aAAa,EAAEzE,OAAO,8BAA8B;AAAA,cAAE,CAAA,CAAA;AAAA,YAC3E,CAAA,GAEF5B,kCAAA,KAAC,OAAI;AAAA,cAAAF,WAAU;AAAA,cACbC,UAAA,CAAAE,kCAAA,IAAC;gBAAKF,UAAW;AAAA,cAAA,CAAA,GACjBE,kCAAA,IAAC;gBACEF,UAAC,CAAA,aAAa,WAAW,EAAE7C,SAASD,MAAME,WAAW,IAClDiJ,kBACA1B,2BAA2BzH,MAAMM,WAAWN,MAAMoJ,iBAAiBpJ,MAAMqJ,SAAS,CACpF,IACEF,kBAAkB3B,QAAQ;AAAA,cAChC,CAAA,CAAA;AAAA,YACF,CAAA,CAAA;AAAA,UACF,CAAA,CAAA;AAAA,QACF,CAAA,yCAECsE,WAAU,CAAA,CAAA,GAGX/I,kCAAAA,KAAC,OAAI;AAAA,UAAAF,WAAU;AAAA,UACbC,UAAA,CAAAC,kCAAA,KAAC,OACC;AAAA,YAAAD,UAAA,CAACC,kCAAA,KAAA,MAAA;AAAA,cAAGF,WAAU;AAAA,cACZC,UAAA,CAACE,kCAAA,IAAA+I,SAAA;AAAA,gBAAQlJ,WAAU;AAAA,cAAU,CAAA,GAAE,mBAAA;AAAA,YAEjC,CAAA,GACAE,kCAAA,KAAC,OAAI;AAAA,cAAAF,WAAU;AAAA,cACbC,UAAA,CAAAC,kCAAA,KAAC,OACC;AAAA,gBAAAD,UAAA,CAAAE,kCAAA,IAAC;kBAAOF,UAAS;AAAA,gBAAA,CAAA,GAAS,MAAG9C,MAAM0G,YAAA;AAAA,cACrC,CAAA,0CACC,OACC;AAAA,gBAAA5D,UAAA,CAAAE,kCAAA,IAAC;kBAAOF,UAAQ;AAAA,gBAAA,CAAA,GAAS,KAAE9C,MAAMgM,WAAA;AAAA,cACnC,CAAA,0CACC,OACC;AAAA,gBAAAlJ,UAAA,CAAAE,kCAAA,IAAC;kBAAOF,UAAc;AAAA,gBAAA,CAAA,GAAS,KAAE9C,MAAMiM,YAAA;AAAA,cACzC,CAAA,0CACC,OACC;AAAA,gBAAAnJ,UAAA,CAAAE,kCAAA,IAAC;kBAAOF,UAAmB;AAAA,gBAAA,CAAA,GAAS,KAAE9C,MAAM0I,gBAAA;AAAA,cAC9C,CAAA,GACC1I,MAAMgK,QAAQ,UAAUhK,MAAMgK,QAAQ,iDACpC,OACC;AAAA,gBAAAlH,UAAA,CAAAE,kCAAA,IAAC;kBAAOF,UAAI;AAAA,gBAAA,CAAA,GAAS,KAAE9C,MAAMgK,GAAA;AAAA,cAC/B,CAAA,GAEDhK,MAAMkM,eACLnJ,kCAAAA,KAAC,OACC;AAAA,gBAAAD,UAAA,CAAAE,kCAAA,IAAC;kBAAOF,UAAa;AAAA,gBAAA,CAAA,GAAS,KAAE9C,MAAMkM,WAAA;AAAA,cACxC,CAAA,CAAA;AAAA,YAEJ,CAAA,CAAA;AAAA,UACF,CAAA,0CAEC,OACC;AAAA,YAAApJ,UAAA,CAACC,kCAAA,KAAA,MAAA;AAAA,cAAGF,WAAU;AAAA,cACZC,UAAA,CAACE,kCAAA,IAAAwG,MAAA;AAAA,gBAAK3G,WAAU;AAAA,cAAU,CAAA,GAAE,sBAAA;AAAA,YAE9B,CAAA,GACAE,kCAAA,KAAC,OAAI;AAAA,cAAAF,WAAU;AAAA,cACbC,UAAA,CAAAC,kCAAA,KAAC,OACC;AAAA,gBAAAD,UAAA,CAAAE,kCAAA,IAAC;kBAAOF,UAAK;AAAA,gBAAA,CAAA,GAAS,KAAE9C,MAAMyJ,SAAA;AAAA,cAChC,CAAA,GACA1G,kCAAA,KAAC,OAAI;AAAA,gBAAAF,WAAU;AAAA,gBACbC,UAAA,CAAAE,kCAAA,IAAC;kBAAOF,UAAM;AAAA,gBAAA,CAAA,GACdE,kCAAA,IAAC,QAAM;AAAA,kBAAAF,UAAA9C,MAAM0J;AAAAA,gBAAY,CAAA,GACzB1G,kCAAA,IAACC,QAAA;AAAA,kBACCC,MAAK;AAAA,kBACLT,SAAQ;AAAA,kBACRU,SAASA,MAAM+E,iBAAiBlI,MAAM0J,WAAW;AAAA,kBACjD7G,WAAU;AAAA,kBAEVC,UAAAE,kCAAA,IAAC2G,OAAM;AAAA,oBAAA9G,WAAU;AAAA,kBAAU,CAAA;AAAA,gBAAA,CAC7B,CAAA;AAAA,cACF,CAAA,0CACC,OACC;AAAA,gBAAAC,UAAA,CAAAE,kCAAA,IAAC;kBAAOF,UAAQ;AAAA,gBAAA,CAAA,GAAS,KAAE9C,MAAM6J,QAAA;AAAA,cACnC,CAAA,0CACC,OACC;AAAA,gBAAA/G,UAAA,CAAAE,kCAAA,IAAC;kBAAOF,UAAK;AAAA,gBAAA,CAAA,GAAS,KAAE9C,MAAMmM,SAAA;AAAA,cAChC,CAAA,CAAA;AAAA,YACF,CAAA,CAAA;AAAA,UACF,CAAA,CAAA;AAAA,QACF,CAAA,yCAECL,WAAU,CAAA,CAAA,0CAGV,OACC;AAAA,UAAAhJ,UAAA,CAACC,kCAAA,KAAA,MAAA;AAAA,YAAGF,WAAU;AAAA,YACZC,UAAA,CAACE,kCAAA,IAAA8G,OAAA;AAAA,cAAMjH,WAAU;AAAA,YAAU,CAAA,GAAE,wBAAA;AAAA,UAE/B,CAAA,GACAE,kCAAA,KAAC,OAAI;AAAA,YAAAF,WAAU;AAAA,YACbC,UAAA,CAAAC,kCAAA,KAAC,OACC;AAAA,cAAAD,UAAA,CAAAC,kCAAA,KAAC,OACC;AAAA,gBAAAD,UAAA,CAAAE,kCAAA,IAAC;kBAAOF,UAAK;AAAA,gBAAA,CAAA,GAAS,KAAE9C,MAAMgB,UAAA;AAAA,cAChC,CAAA,GACA+B,kCAAA,KAAC,OAAI;AAAA,gBAAAF,WAAU;AAAA,gBACbC,UAAA,CAAAE,kCAAA,IAAC;kBAAOF,UAAM;AAAA,gBAAA,CAAA,GACdE,kCAAA,IAAC,QAAM;AAAA,kBAAAF,UAAA9C,MAAM+J;AAAAA,gBAAa,CAAA,GAC1B/G,kCAAA,IAACC,QAAA;AAAA,kBACCC,MAAK;AAAA,kBACLT,SAAQ;AAAA,kBACRU,SAASA,MAAM+E,iBAAiBlI,MAAM+J,YAAY;AAAA,kBAClDlH,WAAU;AAAA,kBAEVC,UAAAE,kCAAA,IAAC2G,OAAM;AAAA,oBAAA9G,WAAU;AAAA,kBAAU,CAAA;AAAA,gBAAA,CAC7B,CAAA;AAAA,cACF,CAAA,CAAA;AAAA,YACF,CAAA,0CACC,OACE;AAAA,cAAAC,UAAA,CAAM9C,MAAAoM,oDACJ,OACC;AAAA,gBAAAtJ,UAAA,CAAAE,kCAAA,IAAC;kBAAOF,UAAM;AAAA,gBAAA,CAAA,GAAS,KAAE9C,MAAMoM,SAAA;AAAA,cACjC,CAAA,GAEDpM,MAAMqM,iBACLtJ,kCAAAA,KAAC,OACC;AAAA,gBAAAD,UAAA,CAAAE,kCAAA,IAAC;kBAAOF,UAAQ;AAAA,gBAAA,CAAA,GAAS,KAAE9C,MAAMqM,aAAA;AAAA,cACnC,CAAA,CAAA;AAAA,YAEJ,CAAA,CAAA;AAAA,UACF,CAAA,CAAA;AAAA,QACF,CAAA,GAGCrM,MAAM0I,qBAAqB,WAAS1I,WAAMiK,oBAANjK,mBAAuBmK,cAExDpH,kCAAA,KAAA2B,4BAAA;AAAA,UAAA5B,UAAA,CAAAE,kCAAAA,IAAC8I,WAAU,CAAA,CAAA,0CACV,OACC;AAAA,YAAAhJ,UAAA,CAACC,kCAAA,KAAA,MAAA;AAAA,cAAGF,WAAU;AAAA,cACZC,UAAA,CAACE,kCAAA,IAAAkH,OAAA;AAAA,gBAAMrH,WAAU;AAAA,cAAU,CAAA,GAAE,kBAAA;AAAA,YAE/B,CAAA,GACAE,kCAAA,KAAC,OAAI;AAAA,cAAAF,WAAU;AAAA,cACbC,UAAA,CAAAC,kCAAA,KAAC,OACC;AAAA,gBAAAD,UAAA,CAAAE,kCAAA,IAAC;kBAAOF,UAAK;AAAA,gBAAA,CAAA,GAAS,KAAE9C,MAAMiK,gBAAgBE,SAAA;AAAA,cAChD,CAAA,GACCnK,MAAMiK,gBAAgBG,cACpBrH,kCAAAA,KAAA,OAAA;AAAA,gBAAIF,WAAU;AAAA,gBACbC,UAAA,CAAAE,kCAAA,IAAC;kBAAOF,UAAM;AAAA,gBAAA,CAAA,GACbE,kCAAA,IAAA,QAAA;AAAA,kBAAMF,UAAM9C,MAAAiK,gBAAgBG;AAAAA,gBAAW,CAAA,GACxCpH,kCAAA,IAACC,QAAA;AAAA,kBACCC,MAAK;AAAA,kBACLT,SAAQ;AAAA,kBACRU,SAASA,MAAM+E,iBAAiBlI,MAAMiK,gBAAiBG,WAAW1E,UAAU;AAAA,kBAC5E7C,WAAU;AAAA,kBAEVC,UAAAE,kCAAA,IAAC2G,OAAM;AAAA,oBAAA9G,WAAU;AAAA,kBAAU,CAAA;AAAA,gBAAA,CAC7B,CAAA;AAAA,cACF,CAAA,GAED7C,MAAMqK,aACLtH,kCAAAA,KAAC,OACC;AAAA,gBAAAD,UAAA,CAAAE,kCAAA,IAAC;kBAAOF,UAAiB;AAAA,gBAAA,CAAA,yCACxBiD,OAAM;AAAA,kBAAAlD,WAAU;AAAA,kBAAQC,UAA6BwH,6BAAAtK,MAAMqK,SAAS;AAAA,gBAAE,CAAA,CAAA;AAAA,cACzE,CAAA,GAEDrK,MAAMiK,gBAAgBqC,eACrBvJ,kCAAAA,KAAC,OACC;AAAA,gBAAAD,UAAA,CAAAE,kCAAA,IAAC;kBAAOF,UAAS;AAAA,gBAAA,CAAA,GACjBE,kCAAA,IAAC,KAAA;AAAA,kBACCuJ,MAAMvM,MAAMiK,gBAAgBqC;AAAAA,kBAC5BlI,QAAO;AAAA,kBACPoI,KAAI;AAAA,kBACJ3J,WAAU;AAAA,kBACXC,UAAA;AAAA,gBAAA,CAED,CAAA;AAAA,cACF,CAAA,CAAA;AAAA,YAEJ,CAAA,CAAA;AAAA,UACF,CAAA,CAAA;AAAA,QACF,CAAA,GAID9C,MAAMyK,gBAAgBzK,MAAMyK,aAAajF,SAAS,KAE/CzC,kCAAA,KAAA2B,4BAAA;AAAA,UAAA5B,UAAA,CAAAE,kCAAAA,IAAC8I,WAAU,CAAA,CAAA,0CACV,OACC;AAAA,YAAAhJ,UAAA,CAACE,kCAAA,IAAA,MAAA;AAAA,cAAGH,WAAU;AAAA,cAAqBC,UAAW;AAAA,YAAA,CAAA,yCAC7C,OAAI;AAAA,cAAAD,WAAU;AAAA,cACZC,UAAA9C,MAAMyK,aAAa7J,IAAK+J,UACvB3H,kCAAA,IAAC;gBAAuBH,WAAU;AAAA,gBAChCC,UAACC,kCAAA,KAAA,OAAA;AAAA,kBAAIF,WAAU;AAAA,kBACbC,UAAA,CAACC,kCAAA,KAAA,OAAA;AAAA,oBAAIF,WAAU;AAAA,oBACbC,UAAA,CAAAE,kCAAA,IAAC,OAAI;AAAA,sBAAAH,WAAU;AAAA,sBAAeC,UAAA6H,KAAKE;AAAAA,oBAAS,CAAA,GAC3CF,KAAK8B,4BAA4BzJ,kCAAAA,IAAC;sBAAIH,WAAU;AAAA,sBAAyBC,eAAK2J;AAAAA,oBAAyB,CAAA,GACvG9B,KAAK+B,iBACH3J,kCAAAA,KAAA,OAAA;AAAA,sBAAIF,WAAU;AAAA,sBAAwBC,UAAA,CAAA,eAAY6H,KAAK+B,aAAA;AAAA,oBAAc,CAAA,GAEvE/B,KAAKgC,UACH5J,kCAAAA,KAAA,OAAA;AAAA,sBAAIF,WAAU;AAAA,sBAAwBC,UAAA,CAAA,YAC5B,KACR6H,KAAKgC,OACHC,QAASC,SAAQA,IAAIC,cAAclM,IAAKmM,WAAU,GAAGA,MAAMhM,IAAI,OAAOgM,MAAMC,KAAK,GAAG,CAAC,EACrFC,KAAK,IAAI,CAAA;AAAA,oBACd,CAAA,GAEFlK,kCAAA,KAAC,OAAI;AAAA,sBAAAF,WAAU;AAAA,sBACZC,UAAA,CAAA6H,KAAKuC,QACJlK,kCAAAA,IAAC,QAAA;AAAA,wBACCH,WAAW,0CAA0C8H,KAAKuC,SAAS,QAC/D,iBACAvC,KAAKuC,SAAS,WACZ,eACA,eACJ;AAAA,sBACJ,CAAA,GAEDvC,KAAKuC,MAAK,OAAIvC,KAAKwC,IAAA;AAAA,oBACtB,CAAA,CAAA;AAAA,kBACF,CAAA,GACApK,kCAAA,KAAC,OAAI;AAAA,oBAAAF,WAAU;AAAA,oBACbC,UAAA,CAACC,kCAAA,KAAA,OAAA;AAAA,sBAAIF,WAAU;AAAA,sBAAUC,UAAA,CAAA,SAAM6H,KAAKG,GAAA;AAAA,oBAAI,CAAA,GACxC/H,kCAAA,KAAC,OAAI;AAAA,sBAAAF,WAAU;AAAA,sBAAUC,UAAA,CAAA,KACrB6H,KAAKyC,cAAa,SAAMzC,KAAKwC,IAAA;AAAA,oBACjC,CAAA,GACApK,kCAAA,KAAC,OAAI;AAAA,sBAAAF,WAAU;AAAA,sBAAgBC,UAAA,CAAA,KAC3B6H,KAAK0C,QACL1C,KAAK2C,mBAAmB3C,KAAK2C,oBAAoB3C,KAAK0C,UACtDtK,kCAAAA,KAAC,QAAK;AAAA,wBAAAF,WAAU;AAAA,wBAA4CC,UAAA,CAAA,KACxD6H,KAAK2C,eAAA;AAAA,sBACT,CAAA,CAAA;AAAA,oBAEJ,CAAA,CAAA;AAAA,kBACF,CAAA,CAAA;AAAA,gBACF,CAAA;AAAA,cA5CQ,GAAA3C,KAAKK,OA6Cf,CACD;AAAA,YACH,CAAA,CAAA;AAAA,UACF,CAAA,CAAA;AAAA,QACF,CAAA,yCAGDc,WAAU,CAAA,CAAA,0CAGV,OACC;AAAA,UAAAhJ,UAAA,CAACE,kCAAA,IAAA,MAAA;AAAA,YAAGH,WAAU;AAAA,YAAqBC,UAAiB;AAAA,UAAA,CAAA,GACpDC,kCAAA,KAAC,OAAI;AAAA,YAAAF,WAAU;AAAA,YACXC,UAAA,CAAM9C,MAAAuN,6BAA8BvN,MAAMuN,8BAA8BvN,MAAMwN,oBAC7EzK,kCAAA,KAAA,OAAA;AAAA,cAAIF,WAAU;AAAA,cACbC,UAAA,CAAAE,kCAAA,IAAC;gBAAKF,UAAuB;AAAA,cAAA,CAAA,0CAC5B,QAAK;AAAA,gBAAAA,UAAA,CAAA,KAAE9C,MAAMuN,yBAAA;AAAA,cAA0B,CAAA,CAAA;AAAA,YAC1C,CAAA,GAEFxK,kCAAA,KAAC,OAAI;AAAA,cAAAF,WAAU;AAAA,cACbC,UAAA,CAAAE,kCAAA,IAAC;gBAAKF,UAAW;AAAA,cAAA,CAAA,0CAChB,QAAK;AAAA,gBAAAA,UAAA,CAAA,KAAE9C,MAAMwN,gBAAA;AAAA,cAAiB,CAAA,CAAA;AAAA,YACjC,CAAA,GACAzK,kCAAA,KAAC,OAAI;AAAA,cAAAF,WAAU;AAAA,cACbC,UAAA,CAAAE,kCAAA,IAAC;gBAAKF,UAAgB;AAAA,cAAA,CAAA,0CACrB,QAAK;AAAA,gBAAAA,UAAA,CAAA,KAAE9C,MAAMyN,mBAAA;AAAA,cAAoB,CAAA,CAAA;AAAA,YACpC,CAAA,GACA1K,kCAAA,KAAC,OAAI;AAAA,cAAAF,WAAU;AAAA,cACbC,UAAA,CAAAE,kCAAA,IAAC;gBAAKF,UAAkB;AAAA,cAAA,CAAA,0CACvB,QAAK;AAAA,gBAAAA,UAAA,CAAA,KAAE9C,MAAM0N,gBAAA;AAAA,cAAiB,CAAA,CAAA;AAAA,YACjC,CAAA,GACA3K,kCAAA,KAAC,OAAI;AAAA,cAAAF,WAAU;AAAA,cACbC,UAAA,CAAAE,kCAAA,IAAC;gBAAKF,UAAa;AAAA,cAAA,CAAA,0CAClB,QAAK;AAAA,gBAAAA,UAAA,CAAA,KAAE9C,MAAM2N,WAAA;AAAA,cAAY,CAAA,CAAA;AAAA,YAC5B,CAAA,GACA5K,kCAAA,KAAC,OAAI;AAAA,cAAAF,WAAU;AAAA,cACbC,UAAA,CAAAE,kCAAA,IAAC;gBAAKF,UAAW;AAAA,cAAA,CAAA,0CAChB,QAAK;AAAA,gBAAAA,UAAA,CAAA,KAAE9C,MAAM4N,cAAA;AAAA,cAAe,CAAA,CAAA;AAAA,YAC/B,CAAA,GACA7K,kCAAA,KAAC,OAAI;AAAA,cAAAF,WAAU;AAAA,cACbC,UAAA,CAAAE,kCAAA,IAAC;gBAAKF,UAAS;AAAA,cAAA,CAAA,0CACd,QAAK;AAAA,gBAAAA,UAAA,CAAA,MAAG9C,MAAM6N,mBAAA;AAAA,cAAoB,CAAA,CAAA;AAAA,YACrC,CAAA,yCACC/B,WAAU,CAAA,CAAA,GACX/I,kCAAAA,KAAC,OAAI;AAAA,cAAAF,WAAU;AAAA,cACbC,UAAA,CAAAE,kCAAA,IAAC;gBAAKF,UAAa;AAAA,cAAA,CAAA,0CAClB,QAAK;AAAA,gBAAAA,UAAA,CAAA,KAAE9C,MAAMwK,qBAAA;AAAA,cAAsB,CAAA,CAAA;AAAA,YACtC,CAAA,GACAzH,kCAAA,KAAC,OAAI;AAAA,cAAAF,WAAU;AAAA,cACbC,UAAA,CAAAE,kCAAA,IAAC;gBAAKF,UAAW;AAAA,cAAA,CAAA,0CAChB,QAAK;AAAA,gBAAAA,UAAA,CAAA,KAAE9C,MAAM8N,SAAA;AAAA,cAAU,CAAA,CAAA;AAAA,YAC1B,CAAA,GACC9N,MAAM+N,eAAe,KACnBhL,kCAAAA,KAAA,OAAA;AAAA,cAAIF,WAAU;AAAA,cACbC,UAAA,CAAAE,kCAAA,IAAC;gBAAKF,UAAc;AAAA,cAAA,CAAA,0CACnB,QAAK;AAAA,gBAAAA,UAAA,CAAA,KAAE9C,MAAM+N,YAAA;AAAA,cAAa,CAAA,CAAA;AAAA,YAC7B,CAAA,CAAA;AAAA,UAEJ,CAAA,CAAA;AAAA,QACF,CAAA,GAGC/N,MAAMiJ,kBAAkBjJ,MAAMiJ,eAAezD,SAAS,KAEnDzC,kCAAA,KAAA2B,4BAAA;AAAA,UAAA5B,UAAA,CAAAE,kCAAAA,IAAC8I,WAAU,CAAA,CAAA,0CACV,OACC;AAAA,YAAAhJ,UAAA,CAACE,kCAAA,IAAA,MAAA;AAAA,cAAGH,WAAU;AAAA,cAAqBC,UAAe;AAAA,YAAA,CAAA,GACjDE,kCAAA,IAAA,OAAA;AAAA,cAAIH,WAAU;AAAA,cACZC,UAAM9C,MAAAiJ,eAAerI,IAAKoN,YACzBjL,kCAAA,KAAC,OAA0B;AAAA,gBAAAF,WAAU;AAAA,gBACnCC,UAAA,CAACC,kCAAA,KAAA,OAAA;AAAA,kBAAIF,WAAU;AAAA,kBACbC,UAAA,CAACC,kCAAA,KAAA,OAAA;AAAA,oBAAIF,WAAU;AAAA,oBAAcC,UAAA,CAAA,YAASkL,OAAOC,QAAA;AAAA,kBAAS,CAAA,GACtDjL,kCAAA,IAAC+C,OAAA;AAAA,oBACCtD,SACEuL,OAAOzM,WAAW,SAAS,gBAAgByM,OAAOzM,WAAW,QAAQ,YAAY;AAAA,oBAGlFuB,UAAOkL,OAAAzM;AAAAA,kBAAA,CACV,CAAA;AAAA,gBACF,CAAA,GACCyB,kCAAA,IAAA,OAAA;AAAA,kBAAIH,WAAU;AAAA,kBAA8BC,iBAAON;AAAAA,gBAAY,CAAA,GAChEO,kCAAA,KAAC,OAAI;AAAA,kBAAAF,WAAU;AAAA,kBAAwBC,UAAA,CAAA,aAC3B,IAAI1G,KAAK4R,OAAOE,WAAW,EAAEC,eAAe,CAAA;AAAA,gBACxD,CAAA,CAAA;AAAA,cAdQ,GAAAH,OAAOC,QAejB,CACD;AAAA,YACH,CAAA,CAAA;AAAA,UACF,CAAA,CAAA;AAAA,QACF,CAAA,GAIFlL,kCAAA,KAAC,OAAI;AAAA,UAAAF,WAAU;AAAA,UACbC,UAAA,CAAAE,kCAAA,IAACC,QAAO;AAAA,YAAAR,SAAQ;AAAA,YAAUU,SAAS0D;AAAAA,YAAS/D,UAE5C;AAAA,UAAA,CAAA,GACC9C,MAAME,gBAAgB,aACrB8C,kCAAAA,IAACC,QAAO;AAAA,YAAAR,SAAQ;AAAA,YAAcU,SAASA,MAAMsD,SAASzG,OAAO,cAAc;AAAA,YAAG8C,UAE9E;AAAA,UAAA,CAAA,IAEC9C,MAAME,gBAAgB,cAAcF,MAAME,gBAAgB,aAAaF,MAAM0I,qBAAqB,+CAClGzF,QAAO;AAAA,YAAAR,SAAQ;AAAA,YAAUU,SAASA,MAAMsD,SAASzG,OAAO,oBAAoB;AAAA,YAAG8C,UAEhF;AAAA,UAAA,CAAA,GAED6F,uBAAuB3I,KAAK,KAC3B+C,kCAAAA,KAACE,QAAO;AAAA,YAAAR,SAAQ;AAAA,YAAUU,SAASA,MAAMsD,SAASzG,OAAO,yBAAyB;AAAA,YAAG8C,UAAA,CAAA,cACxE9C,MAAME,gBAAgB,YAAY,aAAaF,MAAME,gBAAgB,aAAa,WAAWF,MAAME,gBAAgB,WAAW,aAAaF,MAAME,gBAAgB,aAAa,aAAa,cAAa,KAAC8C,kCAAA,IAAC4F,YAAW;AAAA,cAAA/F,WAAU;AAAA,YAAU,CAAA,CAAA;AAAA,UACtP,CAAA,GAEA7C,MAAME,gBAAgB,gBACtB8C,kCAAAA,IAACC;YAAOR,SAAQ;AAAA,YAAUI,WAAU;AAAA,YAAgCM,SAASA,MAAMsD,SAASzG,OAAO,gBAAgB;AAAA,YAAG8C,UAEtH;AAAA,UAAA,CAAA,CAAA;AAAA,QAEJ,CAAA,CAAA;AAAA,MACF,CAAA,CAAA;AAAA,IACF,CAAA;AAAA,EACF,CAAA;AAEJ;AAYO,SAASgE,YAAY;AAAA,EAAE9G;AAAAA,EAAO3E;AAAAA,EAAYwL;AAAAA,EAASpL;AAAAA,EAAcsL;AAAS,GAAqB;AACpG,QAAM,CAACtI,UAAU2P,WAAW,IAAItT,sBAAS;AAAA;AAAA,IAEvCuT,YAAY;AAAA,IACZC,aAAa;AAAA,IACbC,QAAQ;AAAA,IACRC,OAAO;AAAA,IACPC,OAAO;AAAA;AAAA,IAEPC,QAAQ;AAAA;AAAA,IAERC,OAAO;AAAA,IACPC,MAAM;AAAA;AAAA,IAENC,cAAc;AAAA,IACdC,cAAc;AAAA,IACdC,YAAY;AAAA,IACZC,YAAY;AAAA,EACd,CAAC;AAEK,QAAAC,eAAgB9K,OAAuB;AAC3CA,MAAE+K,eAAe;AACjBnI,aAAStI,QAAQ;AAAA,EACnB;AAEA,MAAI,CAACuB,SAAS,CAAC3E,WAAmB,QAAA;AAElC,QAAM8T,oBAAoBA,MACvBpM,kCAAA,KAAA,OAAA;AAAA,IAAIF,WAAU;AAAA,IACbC,UAAA,CAACC,kCAAA,KAAA,OAAA;AAAA,MAAIF,WAAU;AAAA,MACbC,UAAA,CAACE,kCAAA,IAAAoM,eAAA;AAAA,QAAcvM,WAAU;AAAA,MAAuB,CAAA,0CAC/C,OACC;AAAA,QAAAC,UAAA,CAACE,kCAAA,IAAA,KAAA;AAAA,UAAEH,WAAU;AAAA,UAA2BC,UAAY;AAAA,QAAA,CAAA,GACnDE,kCAAA,IAAA,KAAA;AAAA,UAAEH,WAAU;AAAA,UAAuBC,UAEpC;AAAA,QAAA,CAAA,CAAA;AAAA,MACF,CAAA,CAAA;AAAA,IACF,CAAA,0CAEC,OACC;AAAA,MAAAA,UAAA,CAACE,kCAAA,IAAAqC,OAAA;AAAA,QAAMgK,SAAQ;AAAA,QAASvM,UAAmB;AAAA,MAAA,CAAA,GAC3CE,kCAAA,IAACsM,GAAA;AAAA,QACCxO,IAAG;AAAA,QACHtB,OAAOf,SAASiQ;AAAAA,QAChBxK,UAAWC,OAAMiK,YAAamB,WAAU;AAAA,UAAE,GAAGA;AAAAA,UAAMb,QAAQvK,EAAEC,OAAO5E;AAAAA,QAAM,EAAE;AAAA,QAC5EqE,aAAY;AAAA,QACZhB,WAAU;AAAA,MAAA,CACZ,CAAA;AAAA,IACF,CAAA,GAEAE,kCAAA,KAAC,OAAI;AAAA,MAAAF,WAAU;AAAA,MACbC,UAAA,CAAAE,kCAAA,IAACC;QAAOR,SAAQ;AAAA,QAAUU,SAAS0D;AAAAA,QAASzD,UAAU3H;AAAAA,QAAcqH,UAEpE;AAAA,MAAA,CAAA,GACAE,kCAAA,IAACC,QAAO;AAAA,QAAAR,SAAQ;AAAA,QAAcU,SAAS8L;AAAAA,QAAc7L,UAAU3H;AAAAA,QAC5DqH,UAAerH,eAAA,kBAAkB;AAAA,MACpC,CAAA,CAAA;AAAA,IACF,CAAA,CAAA;AAAA,EACF,CAAA;AAGF,QAAM+T,+BAA+BA,MAClCzM,kCAAA,KAAA,OAAA;AAAA,IAAIF,WAAU;AAAA,IACbC,UAAA,CAACC,kCAAA,KAAA,OAAA;AAAA,MAAIF,WAAU;AAAA,MACbC,UAAA,CAACE,kCAAA,IAAAoM,eAAA;AAAA,QAAcvM,WAAU;AAAA,MAA0B,CAAA,0CAClD,OACC;AAAA,QAAAC,UAAA,CAACE,kCAAA,IAAA,KAAA;AAAA,UAAEH,WAAU;AAAA,UAA8BC,UAA4B;AAAA,QAAA,CAAA,GACtEE,kCAAA,IAAA,KAAA;AAAA,UAAEH,WAAU;AAAA,UAA0BC,UAA6D;AAAA,QAAA,CAAA,CAAA;AAAA,MACtG,CAAA,CAAA;AAAA,IACF,CAAA,GAEAC,kCAAA,KAAC,OAAI;AAAA,MAAAF,WAAU;AAAA,MACbC,UAAA,CAAAC,kCAAA,KAAC,OACC;AAAA,QAAAD,UAAA,CAACE,kCAAA,IAAAqC,OAAA;AAAA,UAAMgK,SAAQ;AAAA,UAAcvM,UAAuB;AAAA,QAAA,CAAA,GACpDE,kCAAA,IAACiB,OAAA;AAAA,UACCnD,IAAG;AAAA,UACHtB,OAAOf,SAAS4P;AAAAA,UAChBnK,UAAWC,OAAMiK,YAAamB,WAAU;AAAA,YAAE,GAAGA;AAAAA,YAAMlB,YAAYlK,EAAEC,OAAO5E;AAAAA,UAAM,EAAE;AAAA,UAChFqE,aAAY;AAAA,UACZ4L,UAAQ;AAAA,QAAA,CACV,CAAA;AAAA,MACF,CAAA,0CAEC,OACC;AAAA,QAAA3M,UAAA,CAACE,kCAAA,IAAAqC,OAAA;AAAA,UAAMgK,SAAQ;AAAA,UAAevM,UAAwB;AAAA,QAAA,CAAA,GACtDE,kCAAA,IAACiB,OAAA;AAAA,UACCnD,IAAG;AAAA,UACHtB,OAAOf,SAAS6P;AAAAA,UAChBpK,UAAWC,OAAMiK,YAAamB,WAAU;AAAA,YAAE,GAAGA;AAAAA,YAAMjB,aAAanK,EAAEC,OAAO5E;AAAAA,UAAM,EAAE;AAAA,UACjFqE,aAAY;AAAA,UACZ4L,UAAQ;AAAA,QAAA,CACV,CAAA;AAAA,MACF,CAAA,0CAEC,OACC;AAAA,QAAA3M,UAAA,CAACE,kCAAA,IAAAqC,OAAA;AAAA,UAAMgK,SAAQ;AAAA,UAAUvM,UAAgB;AAAA,QAAA,CAAA,GACzCE,kCAAA,IAACiB,OAAA;AAAA,UACCnD,IAAG;AAAA,UACH4O,MAAK;AAAA,UACL3E,KAAK;AAAA,UACLvL,OAAOf,SAAS8P;AAAAA,UAChBrK,UAAWC,OAAMiK,YAAamB,WAAU;AAAA,YAAE,GAAGA;AAAAA,YAAMhB,QAAQpK,EAAEC,OAAO5E;AAAAA,UAAM,EAAE;AAAA,UAC5EqE,aAAY;AAAA,QAAA,CACd,CAAA;AAAA,MACF,CAAA,0CAEC,OACC;AAAA,QAAAf,UAAA,CAACE,kCAAA,IAAAqC,OAAA;AAAA,UAAMgK,SAAQ;AAAA,UAAQvM,UAAqB;AAAA,QAAA,CAAA,GAC5CE,kCAAA,IAACiB,OAAA;AAAA,UACCnD,IAAG;AAAA,UACH4O,MAAK;AAAA,UACL3E,KAAK;AAAA,UACLvL,OAAOf,SAAS+P;AAAAA,UAChBtK,UAAWC,OAAMiK,YAAamB,WAAU;AAAA,YAAE,GAAGA;AAAAA,YAAMf,OAAOrK,EAAEC,OAAO5E;AAAAA,UAAM,EAAE;AAAA,UAC3EqE,aAAY;AAAA,QAAA,CACd,CAAA;AAAA,MACF,CAAA,0CAEC,OACC;AAAA,QAAAf,UAAA,CAACE,kCAAA,IAAAqC,OAAA;AAAA,UAAMgK,SAAQ;AAAA,UAAQvM,UAAqB;AAAA,QAAA,CAAA,GAC5CE,kCAAA,IAACiB,OAAA;AAAA,UACCnD,IAAG;AAAA,UACH4O,MAAK;AAAA,UACL3E,KAAK;AAAA,UACLvL,OAAOf,SAASgQ;AAAAA,UAChBvK,UAAWC,OAAMiK,YAAamB,WAAU;AAAA,YAAE,GAAGA;AAAAA,YAAMd,OAAOtK,EAAEC,OAAO5E;AAAAA,UAAM,EAAE;AAAA,UAC3EqE,aAAY;AAAA,QAAA,CACd,CAAA;AAAA,MACF,CAAA,CAAA;AAAA,IACF,CAAA,GAEAd,kCAAA,KAAC,OAAI;AAAA,MAAAF,WAAU;AAAA,MACbC,UAAA,CAAAE,kCAAA,IAACC;QAAOR,SAAQ;AAAA,QAAUU,SAAS0D;AAAAA,QAASzD,UAAU3H;AAAAA,QAAcqH,UAEpE;AAAA,MAAA,CAAA,GACAE,kCAAA,IAACC,QAAA;AAAA,QACCE,SAAS8L;AAAAA,QACT7L,UAAU3H,gBAAgB,CAACgD,SAAS4P,cAAc,CAAC5P,SAAS6P,eAAe,EAAE7P,SAAS6P,YAAY9I,WAAW,OAAO,CAACD,OAAOoK,UAAUpK,OAAO9G,SAAS8P,MAAM,CAAC,KAAK,CAAC9P,SAAS+P,SAAS,CAAC/P,SAASgQ;AAAAA,QAE9L3L,yBAAe,iBAAiB;AAAA,MAAA,CACnC,CAAA;AAAA,IACF,CAAA,CAAA;AAAA,EACF,CAAA;AAGF,QAAM8M,0BAA0BA,MAC7B7M,kCAAA,KAAA,OAAA;AAAA,IAAIF,WAAU;AAAA,IACbC,UAAA,CAACC,kCAAA,KAAA,OAAA;AAAA,MAAIF,WAAU;AAAA,MACbC,UAAA,CAACE,kCAAA,IAAAoM,eAAA;AAAA,QAAcvM,WAAU;AAAA,MAA0B,CAAA,GAClDG,kCAAA,IAAA,OAAA;AAAA,QACCF,UAACC,kCAAA,KAAA,KAAA;AAAA,UAAEF,WAAU;AAAA,UAAkBC,UAAA,CAAA,gDAA6C9C,MAAME,gBAAgB,YAAY,aAAaF,MAAME,gBAAgB,aAAa,WAAWF,MAAME,gBAAgB,WAAW,aAAaF,MAAME,gBAAgB,aAAa,aAAa,cAAa,GAAA;AAAA,QAAC,CAAA;AAAA,MACvR,CAAA,CAAA;AAAA,IACF,CAAA,GA6BA6C,kCAAA,KAAC,OAAI;AAAA,MAAAF,WAAU;AAAA,MACbC,UAAA,CAAAE,kCAAA,IAACC;QAAOR,SAAQ;AAAA,QAAUU,SAAS0D;AAAAA,QAASzD,UAAU3H;AAAAA,QAAcqH,UAEpE;AAAA,MAAA,CAAA,GACAE,kCAAA,IAACC,QAAA;AAAA,QACCE,SAAS8L;AAAAA,QACT7L,UAAU3H;AAAAA,QAETqH,yBAAe,gBAAgB;AAAA,MAAA,CAClC,CAAA;AAAA,IACF,CAAA,CAAA;AAAA,EACF,CAAA;AAGF,QAAM+M,2BAA2BA,MAC9B9M,kCAAA,KAAA,OAAA;AAAA,IAAIF,WAAU;AAAA,IACbC,UAAA,CAACC,kCAAA,KAAA,OAAA;AAAA,MAAIF,WAAU;AAAA,MACbC,UAAA,CAACE,kCAAA,IAAAoM,eAAA;AAAA,QAAcvM,WAAU;AAAA,MAA0B,CAAA,0CAClD,OACC;AAAA,QAAAC,UAAA,CAACE,kCAAA,IAAA,KAAA;AAAA,UAAEH,WAAU;AAAA,UAA8BC,UAAuB;AAAA,QAAA,CAAA,GACjEE,kCAAA,IAAA,KAAA;AAAA,UAAEH,WAAU;AAAA,UAA0BC,UAAsC;AAAA,QAAA,CAAA,CAAA;AAAA,MAC/E,CAAA,CAAA;AAAA,IACF,CAAA,GAiDAC,kCAAA,KAAC,OAAI;AAAA,MAAAF,WAAU;AAAA,MACbC,UAAA,CAAAE,kCAAA,IAACC;QAAOR,SAAQ;AAAA,QAAUU,SAAS0D;AAAAA,QAASzD,UAAU3H;AAAAA,QAAcqH,UAEpE;AAAA,MAAA,CAAA,GACAE,kCAAA,IAACC,QAAA;AAAA,QACCE,SAAS8L;AAAAA,QACT7L,UAAU3H;AAAAA,QACVoH,WAAU;AAAA,QAETC,yBAAe,eAAe;AAAA,MAAA,CACjC,CAAA;AAAA,IACF,CAAA,CAAA;AAAA,EACF,CAAA;AAIA,SAAAE,kCAAAA,IAACoI;IAAO9C,MAAM;AAAA,IAAM+C,cAAcxE;AAAAA,IAChC/D,UAAAC,kCAAA,KAACuI,eAAc;AAAA,MAAAzI,WAAU;AAAA,MACvBC,UAAA,CAACE,kCAAA,IAAAuI,cAAA;AAAA,QACCzI,iDAAC0I,aACE;AAAA,UAAA1I,UAAA,CAAAzH,eAAe,kBAAkB,gBACjCA,eAAe,wBAAwB,gCACvCA,eAAe,6BAA6B,2BAC5CA,eAAe,oBAAoB,gBAAA;AAAA,QACtC,CAAA;AAAA,MACF,CAAA,GAECA,eAAe,kBAAkB8T,kBAAkB,GACnD9T,eAAe,wBAAwBmU,6BAA6B,GACpEnU,eAAe,6BAA6BuU,wBAAwB,GACpEvU,eAAe,oBAAoBwU,yBAAyB,CAAA;AAAA,IAC/D,CAAA;AAAA,EACF,CAAA;AAEJ;AAGa,MAAApI,6BAA6BA,CAACqI,WAAmBC,YAA6B;AACnF,QAAAC,YAAY,IAAI5T,KAAK0T,SAAS;AACpC,QAAMG,cAAcF,UAAU,IAAI3T,KAAK2T,OAAO,wBAAQ3T,KAAK;AACpD,SAAAuJ,KAAKuK,OAAOD,YAAY1P,QAAA,IAAYyP,UAAUzP,aAAa,GAAI;AACxE;AAGa,MAAA4I,oBAAqBgH,aAA4B;AAC5D,QAAMC,UAAUzK,KAAKuK,MAAMC,UAAU,EAAE;AACvC,QAAME,QAAQ1K,KAAKuK,MAAME,UAAU,EAAE;AACrC,QAAME,OAAO3K,KAAKuK,MAAMG,QAAQ,EAAE;AAElC,MAAIC,OAAO,GAAG;AACZ,WAAO,GAAGA,IAAI,KAAKD,QAAQ,EAAE;AAAA,EAC/B;AACA,MAAIA,QAAQ,GAAG;AACb,WAAO,GAAGA,KAAK,KAAKD,UAAU,EAAE;AAAA,EAClC;AACA,SAAO,GAAGA,OAAO;AACnB;AAGa,MAAA7I,sBAAuBvH,WAAqB;AACjD,QAAAwH,WAAWC,2BAA2BzH,MAAMM,SAAS;AAC3D,QAAMiQ,cAAc;AACpB,MAAIC,eAAe;AACnB,MAAIC,YAAY;AAChB,MAAItF,aAAa;AAGjB,UAAQnL,MAAME,aAAa;AAAA,IACzB,KAAK;AACH,UAAIsH,WAAW,KAAK;AACNiJ,oBAAA;AACGD,uBAAA;AACFrF,qBAAA;AAAA,MACf,WAAW3D,WAAW,KAAK;AACbiJ,oBAAA;AACGD,uBAAA;AACFrF,qBAAA;AAAA,MACf;AACA;AAAA,IAEF,KAAK;AACC,UAAAnL,MAAM0I,qBAAqB,OAAO;AACpC,YACElB,WAAW,OACXxH,MAAMqK,aACN,CAAC,eAAe,eAAe,qBAAqB,EAAEpK,SAASD,MAAMqK,SAAS,GAC9E;AACYoG,sBAAA;AACGD,yBAAA;AACFrF,uBAAA;AAAA,QAEb,WAAA3D,WAAW,OACXxH,MAAMqK,aACN,CAAC,eAAe,eAAe,qBAAqB,EAAEpK,SAASD,MAAMqK,SAAS,GAC9E;AACYoG,sBAAA;AACGD,yBAAA;AACFrF,uBAAA;AAAA,QACJ,WAAA3D,YAAY,OAAOxH,MAAMqK,cAAc,sBAAsB;AACvDmG,yBAAA;AACFrF,uBAAA;AAAA,QACf;AAAA,MACS,WAAAnL,MAAM0I,qBAAqB,UAAUlB,WAAW,KAAK;AAC/CgJ,uBAAA;AACFrF,qBAAA;AAAA,MACf;AACA;AAAA,IAEF,KAAK;AACC,UAAAnL,MAAM0I,qBAAqB,OAAO;AAChC,YAAAlB,WAAW,QAAQ,CAAC,eAAe,eAAe,qBAAqB,EAAEvH,SAASD,MAAMqK,SAAS,GAAG;AAC1FoG,sBAAA;AACGD,yBAAA;AACFrF,uBAAA;AAAA,QACJ,WAAA3D,WAAW,QAAQxH,MAAMqK,cAAc,sBAAsB;AAC1DoG,sBAAA;AACGD,yBAAA;AACFrF,uBAAA;AAAA,QACf;AAAA,MACS,WAAAnL,MAAM0I,qBAAqB,UAAUlB,WAAW,MAAM;AAChDgJ,uBAAA;AACFrF,qBAAA;AAAA,MACf;AACA;AAAA,EACJ;AAEO,SAAA;AAAA,IACLtI,WAAW0N,cAAcE,YAAYD;AAAAA,IACrCrF;AAAAA,EACF;AACF;AAGa,MAAAF,uBAAwB1J,YAA2B;AAC9D,UAAQA,QAAQ;AAAA,IACd,KAAK;AACI,aAAA;AAAA,IACT,KAAK;AACI,aAAA;AAAA,IACT;AACS,aAAAA;AAAAA,EACX;AACF;AAGa,MAAA+I,+BAAgCD,eAA8B;AACzE,SAAOA,UACJqG,QAAQ,QAAQ,EAAE,EAClBA,QAAQ,KAAK,GAAG,EAChBC,cACAC,MAAM,GAAG,EACThQ,IAAKiQ,UAASA,KAAKC,OAAO,CAAC,EAAEC,YAAA,IAAgBF,KAAKnG,MAAM,CAAC,CAAC,EAC1DuC,KAAK,GAAG;AACb;AAEa,MAAAtE,yBAA0B3I,WAAqB;AAC1D,QAAMgR,QAAQhR,MAAMgK,QAAQ,UAAUhK,MAAMgK,QAAQ;AACpD,QAAMiH,qBAAqBjR,MAAM0I,qBAAqB,UAAU1I,MAAM0I,qBAAqB;AAC3F,MAAIsI,SAASC,oBAAoB;AAC/B,WAAQjR,MAAME,gBAAgB,aAAaF,MAAME,gBAAgB,cAAcF,MAAME,gBAAgB,YAAYF,MAAME,gBAAgB,cAAcF,MAAME,gBAAgB;AAAA,EAE7K;AACI,MAAA,CAAC8Q,SAAS,CAACC,oBAAoB;AACjC,WAAQjR,MAAME,gBAAgB,aAAaF,MAAME,gBAAgB,cAAcF,MAAME,gBAAgB,YAAYF,MAAME,gBAAgB,cAAcF,MAAME,gBAAgB;AAAA,EAE7K;AACI,MAAA8Q,SAAS,CAACC,oBAAoB;AAChC,WAAQjR,MAAME,gBAAgB,aAAaF,MAAME,gBAAgB,cAAcF,MAAME,gBAAgB,YAAYF,MAAME,gBAAgB,cAAcF,MAAME,gBAAgB;AAAA,EAE7K;AACI,MAAA,CAAC8Q,SAASC,oBAAoB;AAChC,WAAQjR,MAAME,gBAAgB,aAAaF,MAAME,gBAAgB,cAAcF,MAAME,gBAAgB,YAAYF,MAAME,gBAAgB,cAAcF,MAAME,gBAAgB;AAAA,EAE7K;AACA,SAAQF,MAAME,gBAAgB,aAAaF,MAAME,gBAAgB,cAAcF,MAAME,gBAAgB,YAAYF,MAAME,gBAAgB,cAAcF,MAAME,gBAAgB;AAC7K;"}