import { useLoaderData } from "@remix-run/react";
import { metabaseService } from "../utils/metabase";
import { withAuth, withResponse } from "../utils/auth-utils";

export const loader = withAuth(async ({ user }) => {
  const embedUrl = metabaseService.generateDashboardUrl(10, {
    id: user.sellerId,
  });
  
  return withResponse({ embedUrl });
});

export default function Dashboard() {
  const { embedUrl } = useLoaderData<typeof loader>();

  return (
    <div className="p-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
        <p className="text-gray-600 mt-2">Overview of your restaurant performance</p>
      </div>
      
      <div className="w-full h-screen">
        {embedUrl ? (
          <iframe
            id="metabase-iframe"
            src={embedUrl}
            title="Restaurant Dashboard"
            className="w-full h-full border-0"
          />
        ) : (
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="text-center">
              <div className="mx-auto mb-4 w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
              </div>
              <h2 className="text-2xl font-bold mb-2">Dashboard Loading</h2>
              <p className="text-gray-500">
                Loading your restaurant analytics...
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
} 