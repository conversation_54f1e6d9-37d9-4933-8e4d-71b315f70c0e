{"version": 3, "file": "home.customer-details._customerId-BRCn1ZRx.js", "sources": ["../../../node_modules/lucide-react/dist/esm/icons/bell.js", "../../../app/components/ui/alert.tsx", "../../../app/routes/home.customer-details.$customerId.tsx"], "sourcesContent": ["/**\n * @license lucide-react v0.453.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst Bell = createLucideIcon(\"Bell\", [\n  [\"path\", { d: \"M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9\", key: \"1qo2s2\" }],\n  [\"path\", { d: \"M10.3 21a1.94 1.94 0 0 0 3.4 0\", key: \"qgo35s\" }]\n]);\n\nexport { Bell as default };\n//# sourceMappingURL=bell.js.map\n", "import * as React from \"react\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"~/lib/utils\"\r\n\r\nconst alertVariants = cva(\r\n  \"relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: \"bg-background text-foreground\",\r\n        destructive:\r\n          \"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nconst Alert = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement> & VariantProps<typeof alertVariants>\r\n>(({ className, variant, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    role=\"alert\"\r\n    className={cn(alertVariants({ variant }), className)}\r\n    {...props}\r\n  />\r\n))\r\nAlert.displayName = \"Alert\"\r\n\r\nconst AlertTitle = React.forwardRef<\r\n  HTMLParagraphElement,\r\n  React.HTMLAttributes<HTMLHeadingElement>\r\n>(({ className, ...props }, ref) => (\r\n  <h5\r\n    ref={ref}\r\n    className={cn(\"mb-1 font-medium leading-none tracking-tight\", className)}\r\n    {...props}\r\n  />\r\n))\r\nAlertTitle.displayName = \"AlertTitle\"\r\n\r\nconst AlertDescription = React.forwardRef<\r\n  HTMLParagraphElement,\r\n  React.HTMLAttributes<HTMLParagraphElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"text-sm [&_p]:leading-relaxed\", className)}\r\n    {...props}\r\n  />\r\n))\r\nAlertDescription.displayName = \"AlertDescription\"\r\n\r\nexport { Alert, AlertTitle, AlertDescription }\r\n", "import { PlaceOrder } from './../components/PlaceOrder';\r\n'use client'\r\n\r\nimport React, { useEffect, useState } from 'react'\r\nimport { ArrowLeft, Bell, Edit, EditIcon, ImageOff, MapPin, Phone, Save, SaveIcon, Search, Send, ShieldOff, Star, Truck } from 'lucide-react'\r\nimport { Button } from \"@components/ui/button\"\r\nimport { Input } from \"@components/ui/input\"\r\nimport { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from \"@components/ui/table\"\r\nimport { Tabs, TabsContent, TabsList, TabsTrigger } from \"@components/ui/tabs\"\r\nimport { Card, CardContent, CardHeader, CardTitle } from \"@components/ui/card\"\r\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from \"@components/ui/select\"\r\nimport { Badge } from \"@components/ui/badge\"\r\nimport { Form, Outlet, useActionData, useFetcher, useNavigate, useParams } from \"@remix-run/react\"\r\nimport { ActionFunction, json, LoaderFunction, TypedResponse } from \"@remix-run/node\"\r\nimport { useLoaderData } from \"@remix-run/react\"\r\nimport { getSession } from \"@utils/session.server\"\r\nimport type { User } from \"~/types\"\r\nimport { addContractPrice, getAllDashboardDataFor, getBuyerDetails, getBuyerOrders, getContractPricesForBuyer, getDashboardDataForBuyer, updateContractPrice, updateContractStatus, updateContractValidity } from \"@services/businessConsoleService\"\r\nimport type { BuyerDetailsResponse } from \"~/types/api/businessConsoleService/BuyerDetailsResponse\"\r\nimport { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from \"@components/ui/dialog\";\r\nimport { Alert, AlertDescription } from \"@components/ui/alert\";\r\nimport { ContractPrice, OrderSummary } from \"~/types/api/businessConsoleService/BuyerOrdersResponse\";\r\nimport { Switch } from '~/components/ui/switch'\r\nimport { withAuth, withResponse } from \"@utils/auth-utils\";\r\nimport SpinnerLoader from '~/components/loader/SpinnerLoader'\r\nimport CustomerDashboard, { calculateTargetDate } from '~/components/ui/bar-dashboard'\r\nimport { DashboardGroupBy } from '~/types/home'\r\nimport { SellerConsoleDataResponse } from '~/types/api/businessConsoleService/SellerConsoleDataResponse'\r\n\r\ninterface LoaderData {\r\n    user: User;\r\n    customerDetails: BuyerDetailsResponse;\r\n    orders: OrderSummary[];\r\n    contractPrices: ContractPrice[];\r\n    currentPage: number;\r\n    userPerMissions: string[],\r\n    customerId: number;\r\n    customerDashBoardData: SellerConsoleDataResponse[];\r\n    dashBoardGroupBy: DashboardGroupBy;\r\n    totalPages: number;\r\n}\r\n\r\ninterface SendTemplateMessageRequest {\r\n    phoneNo: string;\r\n    templateName: string;\r\n    templateValues: string[];\r\n}\r\n\r\ninterface ActionData {\r\n    success?: boolean;\r\n    error?: string;\r\n    data?: unknown;\r\n}\r\n\r\nexport const loader = withAuth(async ({ user, request }) => {\r\n    const params = new URL(request.url).pathname.split('/');\r\n    const customerId = parseInt(params[params.length - 1]);\r\n    const permisson = user?.userPermissions || []\r\n    if (isNaN(customerId)) {\r\n        throw json({ error: \"Invalid customer ID\" }, { status: 400 });\r\n    }\r\n    const url = new URL(request.url);\r\n    const page = parseInt(url.searchParams.get(\"page\") || \"0\");\r\n    const pageSize = parseInt(url.searchParams.get(\"pageSize\") || \"10\");\r\n    const activeTab = url.searchParams.get(\"activeTab\") || \"orders\";\r\n    const dashboardGroupBy = DashboardGroupBy.Daily;\r\n\r\n    try {\r\n        const [customerDetailsResponse, ordersResponse, contractPricesResponse] = await Promise.all([\r\n            getBuyerDetails(user.userId, customerId, request),\r\n            getBuyerOrders(user.userId, customerId, page, pageSize, request),\r\n            getContractPricesForBuyer(customerId, page, pageSize, request),\r\n        ]);\r\n\r\n\r\n        const responseHeaders = new Headers();\r\n        // [customerDetailsResponse, ordersResponse, contractPricesResponse, customerDashBoardReponse].forEach(response => {\r\n        //     if (response.headers?.has('Set-Cookie')) {\r\n        //         responseHeaders.set('Set-Cookie', response.headers.get('Set-Cookie')!);\r\n        //     }\r\n        // });\r\n\r\n        const responseData = {\r\n            user,\r\n            customerDetails: customerDetailsResponse.data,\r\n            orders: ordersResponse.data,\r\n            contractPrices: contractPricesResponse.data,\r\n            currentPage: page,\r\n            userPerMissions: permisson,\r\n            customerId: customerId,\r\n            customerDashBoardData: null,\r\n            dashBoardGroupBy: dashboardGroupBy,\r\n            totalPages: Math.ceil(\r\n                (activeTab === \"orders\"\r\n                    ? ordersResponse.data.length\r\n                    : contractPricesResponse.data.length) / pageSize\r\n            ),\r\n            responseHeaders\r\n        };\r\n\r\n        return withResponse(responseData);\r\n    } catch (error) {\r\n        console.error(\"Customer details error:\", error);\r\n        if (error instanceof Response && error.status === 404) {\r\n            throw json({ error: \"Customer not found\" }, { status: 404 });\r\n        }\r\n        throw new Response(\"Failed to fetch customer data\", { status: 500 });\r\n    }\r\n});\r\n\r\nexport const action = withAuth(async ({ user, request }) => {\r\n    const formData = await request.formData();\r\n    const intent = formData.get(\"intent\") as string;\r\n\r\n\r\n    if (intent === \"ContractPrice\") {\r\n        {\r\n            const sellerItemId = formData.get(\"siItemId\") as string;\r\n            const buyerId = formData.get(\"buyerId\") as string;\r\n            try {\r\n                const response = await addContractPrice(\r\n                    Number(sellerItemId),\r\n                    Number(buyerId),\r\n                    request\r\n                );\r\n                return withResponse({ success: true, data: response });\r\n            } catch (error) {\r\n                throw json(\r\n                    { error: \"Failed to create addContractPrice\" },\r\n                    { status: 500 }\r\n                );\r\n            }\r\n        }\r\n    } else if (intent === \"ContractUpdateStatus\") {\r\n        {\r\n            const cPriceId = formData.get(\"cPriceId\") as string;\r\n            const cPriceEnable = formData.get(\"cPriceEnable\") as unknown as boolean;\r\n\r\n            try {\r\n                const response = await updateContractStatus(\r\n                    cPriceEnable,\r\n                    Number(cPriceId),\r\n                    request\r\n                );\r\n                return withResponse({ success: true, data: response });\r\n            } catch (error) {\r\n                throw json(\r\n                    { error: \"Failed to Updating ContractPriceStatus\" },\r\n                    { status: 500 }\r\n                );\r\n            }\r\n        }\r\n    } else if (intent === \"ContractUpdatePrice\") {\r\n        {\r\n            const cPriceId = formData.get(\"cPriceId\") as string;\r\n            const cPrice = formData.get(\"cPrice\") as unknown as number;\r\n\r\n            try {\r\n                const response = await updateContractPrice(\r\n                    Number(cPriceId),\r\n                    cPrice,\r\n                    request\r\n                );\r\n                return withResponse({ success: true, data: response });\r\n            } catch (error) {\r\n                throw json(\r\n                    { error: \"Failed to update contract price\" },\r\n                    { status: 500 }\r\n                );\r\n            }\r\n        }\r\n    }\r\n    else if (intent === \"UpdateContractPriceValidity\") {\r\n        {\r\n            const buyerId = formData.get(\"buyerId\") as unknown as number;\r\n            const selectedCDate = formData.get(\"selectedCDate\") as string;\r\n\r\n            try {\r\n                const response = await updateContractValidity(\r\n                    buyerId,\r\n                    selectedCDate,\r\n                    request\r\n                );\r\n                return withResponse({ success: true, data: response });\r\n            } catch (error) {\r\n                throw new Response(\"Failed to update contract price\", { status: 500 });\r\n\r\n            }\r\n        }\r\n    }\r\n\r\n    else if (intent === \"customerdashboard\") {\r\n\r\n        const dashboardGroupBy = formData.get(\"dashboardGroupBy\") as DashboardGroupBy;\r\n        const summaryDate = new Date(formData.get(\"summaryDate\") as string);\r\n\r\n        const BuyerId = formData.get(\"BuyerId\");\r\n        if (!Object.values(DashboardGroupBy).includes(dashboardGroupBy)) {\r\n            throw json({ error: \"Invalid groupBy parameter\" }, { status: 400 });\r\n        }\r\n\r\n\r\n\r\n        const response = await getDashboardDataForBuyer(Number(BuyerId), summaryDate, dashboardGroupBy, request);\r\n        return withResponse(\r\n            {\r\n                data: response.data,\r\n                dashboardGroupBy: dashboardGroupBy\r\n            },\r\n            response.headers\r\n        );\r\n    }\r\n    else {\r\n        {\r\n            const phoneNo = formData.get(\"phoneNo\");\r\n            const amount = formData.get(\"amount\");\r\n            const customerName = formData.get(\"customerName\");\r\n\r\n            if (!phoneNo || !amount || !customerName) {\r\n                throw json(\r\n                    { error: \"Missing required fields\" },\r\n                    { status: 400 }\r\n                );\r\n            }\r\n\r\n            try {\r\n                const url = new URL(request.url);\r\n                const messageRequest: SendTemplateMessageRequest = {\r\n                    phoneNo: phoneNo.toString(),\r\n                    templateName: 'pending_dues_buyer_1',\r\n                    templateValues: [customerName.toString()]\r\n                };\r\n\r\n                const response = await fetch(\r\n                    `https://console.mnetlive.com/api/whatsapp/send-template-message`,\r\n                    {\r\n                        method: 'POST',\r\n                        headers: {\r\n                            'Content-Type': 'application/json',\r\n                            'Authorization': `Bearer ${process.env.AUTH_TOKEN}`\r\n                        },\r\n                        body: JSON.stringify(messageRequest)\r\n                    }\r\n                );\r\n\r\n                if (!response.ok) {\r\n                    const errorData = await response.json()\r\n                        .catch(() => ({ message: 'Failed to send message' }));\r\n                    throw new Error(errorData.message || 'Failed to send message');\r\n                }\r\n\r\n                const responseData = await response.json();\r\n                return withResponse({ success: true, data: responseData });\r\n            } catch (error) {\r\n                console.error('WhatsApp message error:', error);\r\n                throw json(\r\n                    { error: \"Failed to send payment reminder\" },\r\n                    { status: 500 }\r\n                );\r\n            }\r\n        }\r\n    }\r\n});\r\n\r\n\r\nexport default function CustomerDetailsPage() {\r\n    const params = useParams()\r\n    const buyerId = parseInt(params.customerId as string);\r\n\r\n    const { user, customerDetails, orders, contractPrices, currentPage, userPerMissions, customerId, customerDashBoardData, dashBoardGroupBy, totalPages } = useLoaderData<LoaderData>()\r\n    const actionData = useActionData() as ActionData\r\n    const [searchTerm, setSearchTerm] = useState('')\r\n    const [sortBy, setSortBy] = useState('id')\r\n    const [sortOrder, setSortOrder] = useState('asc')\r\n    const [activeTab, setActiveTab] = useState('dashboard')\r\n    const [isDialogOpen, setIsDialogOpen] = useState(false)\r\n    const [isSubmitting, setIsSubmitting] = useState(false)\r\n    const [uniqueId, setUnqueId] = useState(0);\r\n    const [isEdit, setIsEdit] = useState(false);\r\n    const [cPrice, setCPrice] = useState(0)\r\n    const fetcher = useFetcher<{ data: SellerConsoleDataResponse[], dashboardGroupBy: DashboardGroupBy }>()\r\n    const navigate = useNavigate()\r\n    const [cPriceEnable, setCPriceEnable] = useState(false)\r\n    const [cPriceEditable, setCPriceEditable] = useState(false)\r\n    const previewMessage = `Hi ${customerDetails.ownerName},\r\n        You have pending dues with us. Please open the app to review and clear them as soon as possible. Ignore if already paid.\r\n        Thank you !`;\r\n\r\n    const [customergraphData, setCustomerGraphData] = useState<SellerConsoleDataResponse[]>(customerDashBoardData);\r\n    const [dashboardGroup, setIsDashBoardGroup] = useState<DashboardGroupBy>(dashBoardGroupBy)\r\n    const [pageSize, setPageSize] = useState(\"10\")\r\n    const [selectedCDate, setSelectedCDate] = useState<Date>(new Date())\r\n\r\n    useEffect(() => {\r\n        if (fetcher.data?.data && activeTab === \"dashboard\") {\r\n            setCustomerGraphData(fetcher.data.data);\r\n            setIsDashBoardGroup(fetcher.data.dashboardGroupBy)\r\n        }\r\n    }, [fetcher.state, activeTab === \"dashboard\"])\r\n\r\n    useEffect(() => {\r\n        if (userPerMissions) {\r\n            const isContractPrice = userPerMissions.includes(\"seller_app.contracPriceEnabled\")\r\n            const isContractPriceEditable = userPerMissions.includes(\"seller_app.contracPriceEditable\")\r\n\r\n            setCPriceEditable(isContractPriceEditable);\r\n            setCPriceEnable(isContractPrice)\r\n        }\r\n    }, [userPerMissions])\r\n    const handlefilter = (contract: ContractPrice) => {\r\n        return contract.itemName.toLowerCase().includes(searchTerm.toLowerCase())\r\n    }\r\n\r\n    const handlePageSizeChange = (newPageSize: string) => {\r\n        navigate(`/home/<USER>/${customerId}?page=${currentPage}&pageSize=${newPageSize}&activeTab=${activeTab}`);\r\n        setPageSize(newPageSize);\r\n    }\r\n\r\n    const filteredOrders = orders\r\n        .filter(order =>\r\n            order.orderGroupId.toString().includes(searchTerm) ||\r\n            order.orderStatus.toLowerCase().includes(searchTerm.toLowerCase())\r\n        )\r\n        .sort((a, b) => {\r\n            const getValue = (order: typeof orders[0]) => {\r\n                switch (sortBy) {\r\n                    case 'id':\r\n                        return order.orderGroupId\r\n                    case 'deliveryDate':\r\n                        return order.deliveryDate\r\n                    case 'status':\r\n                        return order.orderStatus\r\n                    case 'totalAmount':\r\n                        return order.orderAmount\r\n                    default:\r\n                        return order.orderGroupId\r\n                }\r\n            }\r\n\r\n            const aValue = getValue(a)\r\n            const bValue = getValue(b)\r\n\r\n            if (aValue < bValue) return sortOrder === 'asc' ? -1 : 1\r\n            if (aValue > bValue) return sortOrder === 'asc' ? 1 : -1\r\n            return 0\r\n        })\r\n\r\n    const handleAddContractPrice = (siItemId: number, buyerId: number) => {\r\n        setUnqueId(siItemId)\r\n        const formData = new FormData();\r\n        formData.append(\"intent\", \"ContractPrice\")\r\n        formData.append(\"siItemId\", siItemId.toString())\r\n        formData.append(\"buyerId\", buyerId.toString())\r\n        fetcher.submit(formData, { method: \"POST\" });\r\n    }\r\n    const handleUpdatePrice = (siItemId: number, cPriceId: number, cPrice: number) => {\r\n        setUnqueId(siItemId)\r\n        const formData = new FormData();\r\n        formData.append(\"intent\", \"ContractUpdatePrice\")\r\n        formData.append(\"cPriceId\", cPriceId.toString())\r\n        formData.append(\"cPrice\", cPrice.toString())\r\n        fetcher.submit(formData, { method: \"POST\" });\r\n        setIsEdit(false)\r\n        setCPrice(0)\r\n    }\r\n    const handleUpdateStatus = (siItemId: number, contractPriceId: number, contractPriceEnabled: boolean) => {\r\n        setUnqueId(siItemId)\r\n        const formData = new FormData();\r\n        formData.append(\"intent\", \"ContractUpdateStatus\")\r\n        formData.append(\"cPriceId\", contractPriceId.toString())\r\n        formData.append(\"cPriceEnable\", contractPriceEnabled.toString())\r\n\r\n        fetcher.submit(formData, { method: \"POST\" });\r\n        setIsEdit(false);\r\n    }\r\n\r\n    const handleEdit = (itemId: number) => {\r\n        setUnqueId(itemId)\r\n        setIsEdit(true)\r\n    }\r\n\r\n    const handleGraph = (value: string, summaryDate: Date) => {\r\n        fetcher.submit(\r\n            {\r\n                dashboardGroupBy: value,\r\n                summaryDate: summaryDate.toISOString(),\r\n                intent: \"customerdashboard\",\r\n                BuyerId: customerId.toString()\r\n            },\r\n            { method: \"post\" }\r\n        );\r\n    }\r\n\r\n    const handleTabChange = (value: string) => {\r\n        setActiveTab(value);\r\n        navigate(`/home/<USER>/${customerId}?page=0&pageSize=${pageSize}&activeTab=${value}`);\r\n    }\r\n\r\n    const renderPagination = (currentPage: number, totalPages: number) => {\r\n        return (\r\n            <div className=\"flex items-center justify-between space-x-2 py-4\">\r\n                <div className=\"flex-1 text-sm text-muted-foreground\">\r\n                    {/* Page {currentPage + 1} of {totalPages} */}\r\n                </div>\r\n                <div className=\"flex items-center space-x-2\">\r\n                    <Form method=\"get\">\r\n                        <input type=\"hidden\" name=\"page\" value={currentPage - 1} />\r\n                        <input type=\"hidden\" name=\"pageSize\" value={pageSize} />\r\n                        <input type=\"hidden\" name=\"activeTab\" value={activeTab} />\r\n                        <Button\r\n                            variant=\"outline\"\r\n                            size=\"sm\"\r\n                            type=\"submit\"\r\n                            disabled={currentPage <= 0}\r\n                        >\r\n                            Previous\r\n                        </Button>\r\n                    </Form>\r\n                    <Form method=\"get\">\r\n                        <input type=\"hidden\" name=\"page\" value={currentPage + 1} />\r\n                        <input type=\"hidden\" name=\"pageSize\" value={pageSize} />\r\n                        <input type=\"hidden\" name=\"activeTab\" value={activeTab} />\r\n                        <Button\r\n                            variant=\"outline\"\r\n                            size=\"sm\"\r\n                            type=\"submit\"\r\n                        >\r\n                            Next\r\n                        </Button>\r\n                    </Form>\r\n                </div>\r\n            </div>\r\n        )\r\n    }\r\n\r\n    return (\r\n        <div className=\"container mx-auto p-6\">\r\n            <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>\r\n                <DialogContent>\r\n                    {actionData?.success ? (\r\n                        <>\r\n                            <Alert className=\"mt-4\">\r\n                                <AlertDescription className=\"text-green-600\">\r\n                                    Payment reminder sent successfully!\r\n                                </AlertDescription>\r\n                            </Alert>\r\n                            <div className=\"flex justify-end mt-4\">\r\n                                <Button\r\n                                    onClick={() => setIsDialogOpen(false)}\r\n                                >\r\n                                    Dismiss\r\n                                </Button>\r\n                            </div>\r\n                        </>\r\n                    ) : (\r\n                        <>\r\n                            <DialogHeader>\r\n                                <DialogTitle>Send Payment Reminder</DialogTitle>\r\n                                <DialogDescription>\r\n                                    Preview of the message that will be sent:\r\n                                </DialogDescription>\r\n                            </DialogHeader>\r\n\r\n                            <div className=\"bg-muted p-4 rounded-lg my-4\">\r\n                                <p className=\"whitespace-pre-wrap\">{previewMessage}</p>\r\n                            </div>\r\n\r\n                            <Form\r\n                                method=\"post\"\r\n                                onSubmit={() => setIsSubmitting(true)}\r\n                                onChange={() => {\r\n                                    if (actionData) {\r\n                                        setIsSubmitting(false)\r\n                                    }\r\n                                }}\r\n                            >\r\n                                <input type=\"hidden\" name=\"phoneNo\" value={customerDetails.mobileNumber} />\r\n                                <input type=\"hidden\" name=\"amount\" value={customerDetails.pendingAmount.toString()} />\r\n                                <input type=\"hidden\" name=\"customerName\" value={customerDetails.ownerName} />\r\n\r\n                                <div className=\"flex justify-end gap-2\">\r\n                                    <Button\r\n                                        variant=\"outline\"\r\n                                        onClick={() => setIsDialogOpen(false)}\r\n                                        type=\"button\"\r\n                                    >\r\n                                        Cancel\r\n                                    </Button>\r\n                                    <Button\r\n                                        type=\"submit\"\r\n                                        disabled={isSubmitting}\r\n                                    >\r\n                                        {isSubmitting ? 'Sending...' : 'Send Reminder'}\r\n                                    </Button>\r\n                                </div>\r\n                            </Form>\r\n\r\n                            {actionData?.error && (\r\n                                <Alert className=\"mt-4\">\r\n                                    <AlertDescription className=\"text-red-600\">\r\n                                        {actionData.error}\r\n                                    </AlertDescription>\r\n                                </Alert>\r\n                            )}\r\n                        </>\r\n                    )}\r\n                </DialogContent>\r\n            </Dialog>\r\n\r\n            <div className=\"flex items-center gap-2 mb-6\">\r\n                <Button variant=\"ghost\" size=\"sm\" onClick={() => navigate(-1)}>\r\n                    <ArrowLeft className=\"h-4 w-4 mr-2\" />\r\n                    Back to Customers\r\n                </Button>\r\n                <span className=\"text-muted-foreground\">/</span>\r\n                <span className=\"font-semibold\">{customerDetails.buyerName}</span>\r\n            </div>\r\n\r\n            <Card className=\"mb-6\">\r\n                <CardHeader>\r\n                    <CardTitle className=\"flex items-center justify-between\">\r\n                        <span>{customerDetails.buyerName}</span>\r\n                    </CardTitle>\r\n                </CardHeader>\r\n                <CardContent>\r\n                    <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\r\n                        <div>\r\n                            <p className=\"font-semibold\">{customerDetails.ownerName}</p>\r\n                            <p className=\"flex items-center mt-1 text-sm\">\r\n                                <Phone className=\"h-4 w-4 mr-2\" />\r\n                                {customerDetails.mobileNumber}\r\n                            </p>\r\n                            <p className=\"flex items-start mt-1 text-sm\">\r\n                                <MapPin className=\"h-4 w-4 mr-2 mt-1\" />\r\n                                {customerDetails.address}\r\n                            </p>\r\n                        </div>\r\n                        <div className=\"flex flex-col gap-2 md:col-span-2 justify-end\">\r\n                            <div className=\"flex w-full flex-wrap gap-2 justify-end\">\r\n                                {/* <div\r\n                                tabIndex={0}\r\n                                role='button'\r\n                                onKeyDown={() => {}}\r\n                                onClick={() => {\r\n                                    setActiveTab(\"orders\")\r\n                                }}>\r\n                                <PlaceOrder   buyerDetails={{buyerId: customerDetails.buyerId, mobileNumber: customerDetails.mobileNumber}}  />\r\n                                </div> */}\r\n                                {user?.userDetails?.mobileNumber === \"5555555550\" ? <Button variant=\"outline\" size=\"sm\"\r\n                                    onClick={() => setIsDialogOpen(true)}\r\n                                >\r\n                                    <Bell className=\"h-4 w-4 mr-2\" />\r\n                                    Send Payment reminder\r\n                                </Button> : null}\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </CardContent>\r\n            </Card>\r\n\r\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6\">\r\n                <Card>\r\n                    <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\r\n                        <CardTitle className=\"text-sm font-medium\">Revenue</CardTitle>\r\n                    </CardHeader>\r\n                    <CardContent>\r\n                        <div className=\"text-2xl font-bold\">₹ {customerDetails.totalAmount.toLocaleString()}</div>\r\n                        <p className=\"text-xs text-muted-foreground\">from {customerDetails.totalOrders} orders</p>\r\n                    </CardContent>\r\n                </Card>\r\n                <Card>\r\n                    <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\r\n                        <CardTitle className=\"text-sm font-medium\">Pending Payments</CardTitle>\r\n                    </CardHeader>\r\n                    <CardContent>\r\n                        <div className=\"text-2xl font-bold\">₹ {customerDetails.pendingAmount.toLocaleString()}</div>\r\n                        <p className=\"text-xs text-muted-foreground\">total pending amount</p>\r\n                    </CardContent>\r\n                </Card>\r\n            </div>\r\n\r\n            <Tabs value={activeTab} onValueChange={handleTabChange}>\r\n                <TabsList>\r\n                    <TabsTrigger value=\"dashboard\">Dashboard</TabsTrigger>\r\n                    <TabsTrigger value=\"orders\">Orders</TabsTrigger>\r\n                    {/* <TabsTrigger value=\"payments\">Payments</TabsTrigger> */}\r\n                    {/* <TabsTrigger value=\"notifications\">Notifications</TabsTrigger>\r\n                    <TabsTrigger value=\"support\">Support Tickets</TabsTrigger> */}\r\n                    {cPriceEnable && <TabsTrigger value=\"contractprice\">Contract Prices</TabsTrigger>}\r\n\r\n\r\n                </TabsList>\r\n                {activeTab !== \"contractprice\" && <div className=\"flex justify-between items-center my-4\">\r\n                    <Input\r\n                        placeholder=\"Search by order ID, status, or seller\"\r\n                        value={searchTerm}\r\n                        onChange={(e) => setSearchTerm(e.target.value)}\r\n                        className=\"max-w-sm\"\r\n                    />\r\n\r\n                    <Select value={pageSize} onValueChange={handlePageSizeChange}>\r\n                        <SelectTrigger className=\"w-[180px]\">\r\n                            <SelectValue placeholder=\"Items per page\" />\r\n                        </SelectTrigger>\r\n                        <SelectContent>\r\n                            <SelectItem value=\"5\">5 per page</SelectItem>\r\n                            <SelectItem value=\"10\">10 per page</SelectItem>\r\n                            <SelectItem value=\"20\">20 per page</SelectItem>\r\n                            <SelectItem value=\"50\">50 per page</SelectItem>\r\n                        </SelectContent>\r\n                    </Select>\r\n                </div>}\r\n                {/* {activeTab === \"contractprice\" && <div className=\"flex justify-between items-center my-4\">\r\n                    <Input\r\n                        placeholder=\"Search by item name\"\r\n                        value={searchTerm}\r\n                        onChange={(e) => setSearchTerm(e.target.value)}\r\n                        className=\"max-w-sm\"\r\n                    />\r\n                </div>} */}\r\n                {/* <TabsContent value=\"dashboard\">\r\n                    <CustomerDashboard data={customergraphData || []} handleGraph={handleGraph} dashboardGroupBy={dashboardGroup} />\r\n                </TabsContent> */}\r\n                <TabsContent value=\"orders\">\r\n                    <Table>\r\n                        <TableHeader>\r\n                            <TableRow>\r\n                                <TableHead>Order ID</TableHead>\r\n                                <TableHead>Order Date</TableHead>\r\n                                <TableHead>Status</TableHead>\r\n                                <TableHead>Delivery Date</TableHead>\r\n                                <TableHead>Amount</TableHead>\r\n                                <TableHead>Payment Status</TableHead>\r\n                                {/* <TableHead>Actions</TableHead> */}\r\n                            </TableRow>\r\n                        </TableHeader>\r\n                        <TableBody>\r\n                            {orders.map((order) => (\r\n                                <TableRow key={order.orderGroupId}>\r\n                                    <TableCell>{order.orderGroupId}</TableCell>\r\n                                    <TableCell>\r\n                                        {new Date(order.orderDateTime).toLocaleDateString('en-IN', {\r\n                                            day: 'numeric',\r\n                                            month: 'short',\r\n                                            year: '2-digit'\r\n                                        })}\r\n                                    </TableCell>\r\n                                    <TableCell>\r\n                                        <Badge variant={order.orderStatus === 'Delivered' ? 'default' : 'secondary'}>\r\n                                            {order.orderStatus}\r\n                                        </Badge>\r\n\r\n                                    </TableCell>\r\n                                    <TableCell>{order.deliveryDate}</TableCell>\r\n                                    <TableCell>₹ {order.orderAmount.toLocaleString()}</TableCell>\r\n                                    <TableCell>\r\n                                        {order.paymentStatus ? 'Paid' : 'Pending'}\r\n                                    </TableCell>\r\n                                    {/* <TableCell>\r\n                                        <Button variant=\"ghost\" size=\"sm\">View</Button>\r\n\r\n                                    </TableCell> */}\r\n                                </TableRow>\r\n                            ))}\r\n                        </TableBody>\r\n                    </Table>\r\n                    {renderPagination(currentPage, totalPages)}\r\n                </TabsContent>\r\n                <TabsContent value=\"payments\">\r\n                    <p>Payments content (to be implemented with payment data)</p>\r\n                </TabsContent>\r\n                <TabsContent value=\"notifications\">\r\n                    <p>Notifications content (to be implemented)</p>\r\n                </TabsContent>\r\n                <TabsContent value=\"support\">\r\n                    <p>Support Tickets content (to be implemented)</p>\r\n                </TabsContent>\r\n\r\n                {cPriceEnable && <TabsContent value=\"contractprice\">\r\n                    {/* {fetcher.state !== \"idle\" && <SpinnerLoader size={8} loading={true} />} */}\r\n\r\n                    <div className=\"flex flex-col md:flex-row justify-between items-center gap-4 my-4\">\r\n                        {/* Search Input */}\r\n                        <Input\r\n                            placeholder=\"Search by item name\"\r\n                            value={searchTerm}\r\n                            onChange={(e) => setSearchTerm(e.target.value)}\r\n                            className=\"w-full md:max-w-xs rounded-full\"\r\n                        />\r\n\r\n                        {/* Contract Price Validity Form */}\r\n                        <Form className=\"flex flex-col md:flex-row items-center gap-4 w-full md:w-auto\" method=\"post\">\r\n                            <input type=\"hidden\" name=\"intent\" value=\"UpdateContractPriceValidity\" />\r\n                            <input type=\"hidden\" name=\"buyerId\" value={buyerId.toString()} />\r\n\r\n                            <input type=\"hidden\" name=\"selectedDate\" value={selectedCDate.toString()} />\r\n\r\n                            <div className=\"flex flex-col md:flex-row items-center gap-2\">\r\n                                <label htmlFor=\"dashboardGroupBy\" className=\"text-sm font-medium text-gray-700\">\r\n                                    Contract Price Validity:\r\n                                </label>\r\n                                <Input\r\n                                    value={selectedCDate.toISOString().split('T')[0]}\r\n                                    onChange={(e) => {\r\n                                        const date = new Date(e.target.value);\r\n                                        setSelectedCDate(date);\r\n                                    }}\r\n                                    type=\"date\"\r\n                                    className=\"w-full md:max-w-xs bg-orange-100 border border-orange-300 rounded-md focus:ring-2 focus:ring-orange-400 focus:outline-none custom-date-input cursor-pointer\"\r\n                                />\r\n                            </div>\r\n                            <Button\r\n                                className=\"bg-orange-400 hover:bg-orange-500 text-white font-bold px-4 py-2 rounded-md transition-all\"\r\n                                size=\"sm\"\r\n                                type=\"submit\"\r\n                            >\r\n                                SAVE\r\n                            </Button>\r\n                        </Form>\r\n\r\n                        {/* Items Per Page Select */}\r\n                        <Select value={pageSize} onValueChange={handlePageSizeChange}>\r\n                            <SelectTrigger className=\"w-full md:w-[180px] border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-400 focus:outline-none\">\r\n                                <SelectValue placeholder=\"Items per page\" />\r\n                            </SelectTrigger>\r\n                            <SelectContent>\r\n                                <SelectItem value=\"5\">5 per page</SelectItem>\r\n                                <SelectItem value=\"10\">10 per page</SelectItem>\r\n                                <SelectItem value=\"20\">20 per page</SelectItem>\r\n                                <SelectItem value=\"50\">50 per page</SelectItem>\r\n                            </SelectContent>\r\n                        </Select>\r\n                    </div>\r\n                    <Table>\r\n                        <TableHeader>\r\n                            <TableRow>\r\n                                <TableHead>Item Image</TableHead>\r\n                                <TableHead>Item Name</TableHead>\r\n                                <TableHead>Price</TableHead>\r\n                                <TableHead>Enabled</TableHead>\r\n                            </TableRow>\r\n                        </TableHeader>\r\n                        <TableBody>\r\n                            {contractPrices.filter((contract) => handlefilter(contract)).map((price) => (\r\n                                <TableRow key={price.sellerItemId}>\r\n                                    <TableCell>\r\n                                        <img\r\n                                            src={price.picture}\r\n                                            alt={price.itemName}\r\n                                            className=\"h-10 w-10\"\r\n                                        />\r\n                                    </TableCell>\r\n                                    <TableCell>{price.itemName}</TableCell>\r\n                                    {price.newItem === false && <TableCell className='flex gap-2'>\r\n\r\n                                        {isEdit && uniqueId === price.sellerItemId ? <>\r\n                                            <Input placeholder={price.cbItemPrice.toString()}\r\n\r\n\r\n                                                onChange={(e) => {\r\n                                                    const value = Number(e.target.value);\r\n                                                    if (value > 0 || e.target.value === \"\") {\r\n\r\n                                                        setCPrice(value);\r\n                                                    }\r\n                                                }}\r\n                                                type=\"number\"\r\n                                                className='w-20 h-10 ' />\r\n                                            <Save height={20} width={20} onClick={() => handleUpdatePrice(price.sellerItemId, price.contractPriceId, cPrice)} className='my-3 cursor-pointer ' />\r\n                                        </>\r\n\r\n\r\n                                            :\r\n                                            <> {`₹ ${price.cbItemPrice}`}\r\n\r\n                                                {cPriceEditable && <EditIcon height={20} width={20} onClick={() => handleEdit(price.sellerItemId)} />}\r\n\r\n                                            </>\r\n                                        }\r\n                                    </TableCell>}\r\n                                    <TableCell>{price.newItem ? <Button type=\"button\" onClick={() => handleAddContractPrice(price.sellerItemId, buyerId)}>\r\n                                        {price.sellerItemId === uniqueId && fetcher.state !== \"idle\"\r\n                                            ? \"ADDING...\"\r\n                                            : \"ADD\"}\r\n                                    </Button> : <Switch checked={price.enabled}\r\n                                        onCheckedChange={() => handleUpdateStatus(price.sellerItemId, price.contractPriceId, !(price.enabled))} />}\r\n                                    </TableCell>\r\n\r\n\r\n                                </TableRow>\r\n                            ))}\r\n                        </TableBody>\r\n                    </Table>\r\n                    {renderPagination(currentPage, totalPages)}\r\n                </TabsContent>}\r\n            </Tabs>\r\n        </div>\r\n    )\r\n}\r\n\r\n"], "names": ["React.forwardRef", "jsx", "CustomerDetailsPage", "params", "useParams", "buyerId", "parseInt", "customerId", "user", "customerDetails", "orders", "contractPrices", "currentPage", "userPerMissions", "customerDashBoardData", "dashBoardGroupBy", "totalPages", "useLoaderData", "actionData", "useActionData", "searchTerm", "setSearchTerm", "useState", "sortBy", "setSortBy", "sortOrder", "setSortOrder", "activeTab", "setActiveTab", "isDialogOpen", "setIsDialogOpen", "isSubmitting", "setIsSubmitting", "uniqueId", "setUnqueId", "isEdit", "setIsEdit", "cPrice", "setCPrice", "fetcher", "useFetcher", "navigate", "useNavigate", "cPriceEnable", "setCPriceEnable", "cPriceEditable", "setCPriceEditable", "previewMessage", "ownerName", "customergraphData", "setCustomerGraphData", "dashboardGroup", "setIsDashBoardGroup", "pageSize", "setPageSize", "selectedCDate", "setSelectedCDate", "Date", "useEffect", "data", "dashboardGroupBy", "state", "isContractPrice", "includes", "isContractPriceEditable", "handlefilter", "contract", "itemName", "toLowerCase", "handlePageSizeChange", "newPageSize", "filter", "order", "orderGroupId", "toString", "orderStatus", "sort", "a", "b", "getValue", "deliveryDate", "orderAmount", "aValue", "bValue", "handleAddContractPrice", "siItemId", "formData", "FormData", "append", "submit", "method", "handleUpdatePrice", "cPriceId", "handleUpdateStatus", "contractPriceId", "contractPriceEnabled", "handleEdit", "itemId", "handleTabChange", "value", "renderPagination", "jsxs", "className", "children", "Form", "type", "name", "<PERSON><PERSON>", "variant", "size", "disabled", "Dialog", "open", "onOpenChange", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "success", "Fragment", "<PERSON><PERSON>", "AlertDescription", "onClick", "DialogHeader", "DialogTitle", "DialogDescription", "onSubmit", "onChange", "mobileNumber", "pendingAmount", "error", "ArrowLeft", "buyerName", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardTitle", "<PERSON><PERSON><PERSON><PERSON>", "Phone", "MapPin", "address", "userDetails", "Bell", "totalAmount", "toLocaleString", "totalOrders", "Tabs", "onValueChange", "TabsList", "TabsTrigger", "Input", "placeholder", "e", "target", "Select", "SelectTrigger", "SelectValue", "SelectContent", "SelectItem", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Table", "TableHeader", "TableRow", "TableHead", "TableBody", "map", "TableCell", "orderDateTime", "toLocaleDateString", "day", "month", "year", "Badge", "paymentStatus", "htmlFor", "toISOString", "split", "date", "price", "src", "picture", "alt", "newItem", "sellerItemId", "cbItemPrice", "Number", "Save", "height", "width", "EditIcon", "Switch", "checked", "enabled", "onCheckedChange"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA,MAAM,OAAO,iBAAiB,QAAQ;AAAA,EACpC,CAAC,QAAQ,EAAE,GAAG,6CAA6C,KAAK,SAAQ,CAAE;AAAA,EAC1E,CAAC,QAAQ,EAAE,GAAG,kCAAkC,KAAK,SAAU,CAAA;AACjE,CAAC;ACPD,MAAM,gBAAgB;AAAA,EACpB;AAAA,EACA;AAAA,IACE,UAAU;AAAA,MACR,SAAS;AAAA,QACP,SAAS;AAAA,QACT,aACE;AAAA,MAAA;AAAA,IAEN;AAAA,IACA,iBAAiB;AAAA,MACf,SAAS;AAAA,IAAA;AAAA,EACX;AAEJ;AAEA,MAAM,QAAQA,aAAAA,WAGZ,CAAC,EAAE,WAAW,SAAS,GAAG,MAAM,GAAG,QACnCC,kCAAA;AAAA,EAAC;AAAA,EAAA;AAAA,IACC;AAAA,IACA,MAAK;AAAA,IACL,WAAW,GAAG,cAAc,EAAE,QAAS,CAAA,GAAG,SAAS;AAAA,IAClD,GAAG;AAAA,EAAA;AACN,CACD;AACD,MAAM,cAAc;AAEpB,MAAM,aAAaD,aAGjB,WAAA,CAAC,EAAE,WAAW,GAAG,MAAM,GAAG,QAC1BC,kCAAA;AAAA,EAAC;AAAA,EAAA;AAAA,IACC;AAAA,IACA,WAAW,GAAG,gDAAgD,SAAS;AAAA,IACtE,GAAG;AAAA,EAAA;AACN,CACD;AACD,WAAW,cAAc;AAEzB,MAAM,mBAAmBD,aAGvB,WAAA,CAAC,EAAE,WAAW,GAAG,MAAM,GAAG,QAC1BC,kCAAA;AAAA,EAAC;AAAA,EAAA;AAAA,IACC;AAAA,IACA,WAAW,GAAG,iCAAiC,SAAS;AAAA,IACvD,GAAG;AAAA,EAAA;AACN,CACD;AACD,iBAAiB,cAAc;ACiN/B,SAAwBC,sBAAsB;;AAC1C,QAAMC,SAASC,UAAU;AACnB,QAAAC,UAAUC,SAASH,OAAOI,UAAoB;AAEpD,QAAM;AAAA,IAAEC;AAAAA,IAAMC;AAAAA,IAAiBC;AAAAA,IAAQC;AAAAA,IAAgBC;AAAAA,IAAaC;AAAAA,IAAiBN;AAAAA,IAAYO;AAAAA,IAAuBC;AAAAA,IAAkBC;AAAAA,EAAW,IAAIC,cAA0B;AACnL,QAAMC,aAAaC,cAAc;AACjC,QAAM,CAACC,YAAYC,aAAa,IAAIC,aAAAA,SAAS,EAAE;AAC/C,QAAM,CAACC,QAAQC,SAAS,IAAIF,aAAAA,SAAS,IAAI;AACzC,QAAM,CAACG,WAAWC,YAAY,IAAIJ,aAAAA,SAAS,KAAK;AAChD,QAAM,CAACK,WAAWC,YAAY,IAAIN,aAAAA,SAAS,WAAW;AACtD,QAAM,CAACO,cAAcC,eAAe,IAAIR,aAAAA,SAAS,KAAK;AACtD,QAAM,CAACS,cAAcC,eAAe,IAAIV,aAAAA,SAAS,KAAK;AACtD,QAAM,CAACW,UAAUC,UAAU,IAAIZ,aAAAA,SAAS,CAAC;AACzC,QAAM,CAACa,QAAQC,SAAS,IAAId,aAAAA,SAAS,KAAK;AAC1C,QAAM,CAACe,QAAQC,SAAS,IAAIhB,aAAAA,SAAS,CAAC;AACtC,QAAMiB,UAAUC,WAAsF;AACtG,QAAMC,WAAWC,YAAY;AAC7B,QAAM,CAACC,cAAcC,eAAe,IAAItB,aAAAA,SAAS,KAAK;AACtD,QAAM,CAACuB,gBAAgBC,iBAAiB,IAAIxB,aAAAA,SAAS,KAAK;AACpD,QAAAyB,iBAAiB,MAAMtC,gBAAgBuC,SAAS;AAAA;AAAA;AAItD,QAAM,CAACC,mBAAmBC,oBAAoB,IAAI5B,aAAAA,SAAsCR,qBAAqB;AAC7G,QAAM,CAACqC,gBAAgBC,mBAAmB,IAAI9B,aAAAA,SAA2BP,gBAAgB;AACzF,QAAM,CAACsC,UAAUC,WAAW,IAAIhC,aAAAA,SAAS,IAAI;AAC7C,QAAM,CAACiC,eAAeC,gBAAgB,IAAIlC,aAAAA,SAAe,oBAAImC,MAAM;AAEnEC,eAAAA,UAAU,MAAM;;AACZ,UAAInB,MAAAA,QAAQoB,SAARpB,gBAAAA,IAAcoB,SAAQhC,cAAc,aAAa;AAC5BuB,2BAAAX,QAAQoB,KAAKA,IAAI;AAClBP,0BAAAb,QAAQoB,KAAKC,gBAAgB;AAAA,IACrD;AAAA,KACD,CAACrB,QAAQsB,OAAOlC,cAAc,WAAW,CAAC;AAE7C+B,eAAAA,UAAU,MAAM;AACZ,QAAI7C,iBAAiB;AACX,YAAAiD,kBAAkBjD,gBAAgBkD,SAAS,gCAAgC;AAC3E,YAAAC,0BAA0BnD,gBAAgBkD,SAAS,iCAAiC;AAE1FjB,wBAAkBkB,uBAAuB;AACzCpB,sBAAgBkB,eAAe;AAAA,IACnC;AAAA,EACJ,GAAG,CAACjD,eAAe,CAAC;AACd,QAAAoD,eAAgBC,cAA4B;AAC9C,WAAOA,SAASC,SAASC,YAAA,EAAcL,SAAS3C,WAAWgD,aAAa;AAAA,EAC5E;AAEM,QAAAC,uBAAwBC,iBAAwB;AACzC7B,aAAA,0BAA0BlC,UAAU,SAASK,WAAW,aAAa0D,WAAW,cAAc3C,SAAS,EAAE;AAClH2B,gBAAYgB,WAAW;AAAA,EAC3B;AAEuB5D,SAClB6D,OACGC,WAAAA,MAAMC,aAAaC,WAAWX,SAAS3C,UAAU,KACjDoD,MAAMG,YAAYP,YAAY,EAAEL,SAAS3C,WAAWgD,aAAa,CACrE,EACCQ,KAAK,CAACC,GAAGC,MAAM;AACN,UAAAC,WAAYP,WAA4B;AAC1C,cAAQjD,QAAQ;AAAA,QACZ,KAAK;AACD,iBAAOiD,MAAMC;AAAAA,QACjB,KAAK;AACD,iBAAOD,MAAMQ;AAAAA,QACjB,KAAK;AACD,iBAAOR,MAAMG;AAAAA,QACjB,KAAK;AACD,iBAAOH,MAAMS;AAAAA,QACjB;AACI,iBAAOT,MAAMC;AAAAA,MACrB;AAAA,IACJ;AAEM,UAAAS,SAASH,SAASF,CAAC;AACnB,UAAAM,SAASJ,SAASD,CAAC;AAEzB,QAAII,SAASC,OAAe,QAAA1D,cAAc,QAAQ,KAAK;AACvD,QAAIyD,SAASC,OAAe,QAAA1D,cAAc,QAAQ,IAAI;AAC/C,WAAA;AAAA,EACV,CAAA;AAEC,QAAA2D,yBAAyBA,CAACC,UAAkBhF,aAAoB;AAClE6B,eAAWmD,QAAQ;AACb,UAAAC,WAAW,IAAIC,SAAS;AACrBD,aAAAE,OAAO,UAAU,eAAe;AACzCF,aAASE,OAAO,YAAYH,SAASX,SAAA,CAAU;AAC/CY,aAASE,OAAO,WAAWnF,SAAQqE,SAAA,CAAU;AAC7CnC,YAAQkD,OAAOH,UAAU;AAAA,MAAEI,QAAQ;AAAA,IAAO,CAAC;AAAA,EAC/C;AACA,QAAMC,oBAAoBA,CAACN,UAAkBO,UAAkBvD,YAAmB;AAC9EH,eAAWmD,QAAQ;AACb,UAAAC,WAAW,IAAIC,SAAS;AACrBD,aAAAE,OAAO,UAAU,qBAAqB;AAC/CF,aAASE,OAAO,YAAYI,SAASlB,SAAA,CAAU;AAC/CY,aAASE,OAAO,UAAUnD,QAAOqC,SAAA,CAAU;AAC3CnC,YAAQkD,OAAOH,UAAU;AAAA,MAAEI,QAAQ;AAAA,IAAO,CAAC;AAC3CtD,cAAU,KAAK;AACfE,cAAU,CAAC;AAAA,EACf;AACA,QAAMuD,qBAAqBA,CAACR,UAAkBS,iBAAyBC,yBAAkC;AACrG7D,eAAWmD,QAAQ;AACb,UAAAC,WAAW,IAAIC,SAAS;AACrBD,aAAAE,OAAO,UAAU,sBAAsB;AAChDF,aAASE,OAAO,YAAYM,gBAAgBpB,SAAA,CAAU;AACtDY,aAASE,OAAO,gBAAgBO,qBAAqBrB,SAAA,CAAU;AAE/DnC,YAAQkD,OAAOH,UAAU;AAAA,MAAEI,QAAQ;AAAA,IAAO,CAAC;AAC3CtD,cAAU,KAAK;AAAA,EACnB;AAEM,QAAA4D,aAAcC,YAAmB;AACnC/D,eAAW+D,MAAM;AACjB7D,cAAU,IAAI;AAAA,EAClB;AAcM,QAAA8D,kBAAmBC,WAAkB;AACvCvE,iBAAauE,KAAK;AAClB1D,aAAS,0BAA0BlC,UAAU,oBAAoB8C,QAAQ,cAAc8C,KAAK,EAAE;AAAA,EAClG;AAEM,QAAAC,mBAAmBA,CAACxF,cAAqBI,gBAAuB;AAE9D,WAAAqF,kCAAAA,KAAC,OAAI;AAAA,MAAAC,WAAU;AAAA,MACXC,UAAA,CAACtG,kCAAA,IAAA,OAAA;AAAA,QAAIqG,WAAU;AAAA,MAEf,CAAA,GACAD,kCAAA,KAAC,OAAI;AAAA,QAAAC,WAAU;AAAA,QACXC,UAAA,CAACF,kCAAA,KAAAG,MAAA;AAAA,UAAKd,QAAO;AAAA,UACTa,UAAA,CAAAtG,kCAAA,IAAC;YAAMwG,MAAK;AAAA,YAASC,MAAK;AAAA,YAAOP,OAAOvF,eAAc;AAAA,UAAG,CAAA,yCACxD,SAAM;AAAA,YAAA6F,MAAK;AAAA,YAASC,MAAK;AAAA,YAAWP,OAAO9C;AAAAA,UAAU,CAAA,yCACrD,SAAM;AAAA,YAAAoD,MAAK;AAAA,YAASC,MAAK;AAAA,YAAYP,OAAOxE;AAAAA,UAAW,CAAA,GACxD1B,kCAAA,IAAC0G,QAAA;AAAA,YACGC,SAAQ;AAAA,YACRC,MAAK;AAAA,YACLJ,MAAK;AAAA,YACLK,UAAUlG,gBAAe;AAAA,YAC5B2F,UAAA;AAAA,UAAA,CAED,CAAA;AAAA,QACJ,CAAA,GACAF,kCAAA,KAACG,MAAK;AAAA,UAAAd,QAAO;AAAA,UACTa,UAAA,CAAAtG,kCAAA,IAAC;YAAMwG,MAAK;AAAA,YAASC,MAAK;AAAA,YAAOP,OAAOvF,eAAc;AAAA,UAAG,CAAA,yCACxD,SAAM;AAAA,YAAA6F,MAAK;AAAA,YAASC,MAAK;AAAA,YAAWP,OAAO9C;AAAAA,UAAU,CAAA,yCACrD,SAAM;AAAA,YAAAoD,MAAK;AAAA,YAASC,MAAK;AAAA,YAAYP,OAAOxE;AAAAA,UAAW,CAAA,GACxD1B,kCAAA,IAAC0G,QAAA;AAAA,YACGC,SAAQ;AAAA,YACRC,MAAK;AAAA,YACLJ,MAAK;AAAA,YACRF,UAAA;AAAA,UAAA,CAED,CAAA;AAAA,QACJ,CAAA,CAAA;AAAA,MACJ,CAAA,CAAA;AAAA,IACJ,CAAA;AAAA,EAER;AAGI,SAAAF,kCAAAA,KAAC,OAAI;AAAA,IAAAC,WAAU;AAAA,IACXC,UAAA,CAACtG,kCAAA,IAAA8G,QAAA;AAAA,MAAOC,MAAMnF;AAAAA,MAAcoF,cAAcnF;AAAAA,MACtCyE,UAACtG,kCAAA,IAAAiH,eAAA;AAAA,QACIX,WAAYrF,yCAAAiG,WAELd,kCAAAA,KAAAe,kBAAAA,UAAA;AAAA,UAAAb,UAAA,CAACtG,kCAAA,IAAAoH,OAAA;AAAA,YAAMf,WAAU;AAAA,YACbC,UAAAtG,kCAAA,IAACqH;cAAiBhB,WAAU;AAAA,cAAiBC;YAE7C,CAAA;AAAA,UACJ,CAAA,GACAtG,kCAAA,IAAC,OAAI;AAAA,YAAAqG,WAAU;AAAA,YACXC,UAAAtG,kCAAA,IAAC0G,QAAA;AAAA,cACGY,SAASA,MAAMzF,gBAAgB,KAAK;AAAA,cACvCyE,UAAA;AAAA,YAED,CAAA;AAAA,UACJ,CAAA,CAAA;AAAA,QAAA,CACJ,IAGIF,kCAAA,KAAAe,4BAAA;AAAA,UAAAb,UAAA,CAAAF,kCAAA,KAACmB,cACG;AAAA,YAAAjB,UAAA,CAAAtG,kCAAA,IAACwH;cAAYlB,UAAqB;AAAA,YAAA,CAAA,GAClCtG,kCAAA,IAACyH;cAAkBnB,UAEnB;AAAA,YAAA,CAAA,CAAA;AAAA,UACJ,CAAA,GAEAtG,kCAAA,IAAC;YAAIqG,WAAU;AAAA,YACXC,gDAAC,KAAE;AAAA,cAAAD,WAAU;AAAA,cAAuBC,UAAAxD;AAAAA,YAAe,CAAA;AAAA,UACvD,CAAA,GAEAsD,kCAAA,KAACG,MAAA;AAAA,YACGd,QAAO;AAAA,YACPiC,UAAUA,MAAM3F,gBAAgB,IAAI;AAAA,YACpC4F,UAAUA,MAAM;AACZ,kBAAI1G,YAAY;AACZc,gCAAgB,KAAK;AAAA,cACzB;AAAA,YACJ;AAAA,YAEAuE,UAAA,CAAAtG,kCAAA,IAAC;cAAMwG,MAAK;AAAA,cAASC,MAAK;AAAA,cAAUP,OAAO1F,gBAAgBoH;AAAAA,YAAc,CAAA,GACzE5H,kCAAA,IAAC,SAAM;AAAA,cAAAwG,MAAK;AAAA,cAASC,MAAK;AAAA,cAASP,OAAO1F,gBAAgBqH,cAAcpD,SAAA;AAAA,YAAY,CAAA,GACpFzE,kCAAA,IAAC;cAAMwG,MAAK;AAAA,cAASC,MAAK;AAAA,cAAeP,OAAO1F,gBAAgBuC;AAAAA,YAAW,CAAA,GAE3EqD,kCAAA,KAAC,OAAI;AAAA,cAAAC,WAAU;AAAA,cACXC,UAAA,CAAAtG,kCAAA,IAAC0G,QAAA;AAAA,gBACGC,SAAQ;AAAA,gBACRW,SAASA,MAAMzF,gBAAgB,KAAK;AAAA,gBACpC2E,MAAK;AAAA,gBACRF,UAAA;AAAA,cAAA,CAED,GACAtG,kCAAA,IAAC0G,QAAA;AAAA,gBACGF,MAAK;AAAA,gBACLK,UAAU/E;AAAAA,gBAETwE,yBAAe,eAAe;AAAA,cAAA,CACnC,CAAA;AAAA,YACJ,CAAA,CAAA;AAAA,UACJ,CAAA,IAECrF,yCAAY6G,UACR9H,kCAAAA,IAAAoH,OAAA;AAAA,YAAMf,WAAU;AAAA,YACbC,UAACtG,kCAAA,IAAAqH,kBAAA;AAAA,cAAiBhB,WAAU;AAAA,cACvBC,UAAWrF,WAAA6G;AAAAA,YAChB,CAAA;AAAA,UACJ,CAAA,CAAA;AAAA,QAER,CAAA;AAAA,MAER,CAAA;AAAA,IACJ,CAAA,GAEA1B,kCAAA,KAAC,OAAI;AAAA,MAAAC,WAAU;AAAA,MACXC,UAAA,CAACF,kCAAA,KAAAM,QAAA;AAAA,QAAOC,SAAQ;AAAA,QAAQC,MAAK;AAAA,QAAKU,SAASA,MAAM9E,SAAS,EAAE;AAAA,QACxD8D,UAAA,CAACtG,kCAAA,IAAA+H,WAAA;AAAA,UAAU1B,WAAU;AAAA,QAAe,CAAA,GAAE,mBAAA;AAAA,MAE1C,CAAA,GACCrG,kCAAA,IAAA,QAAA;AAAA,QAAKqG,WAAU;AAAA,QAAwBC,UAAC;AAAA,MAAA,CAAA,GACxCtG,kCAAA,IAAA,QAAA;AAAA,QAAKqG,WAAU;AAAA,QAAiBC,0BAAgB0B;AAAAA,MAAU,CAAA,CAAA;AAAA,IAC/D,CAAA,GAEA5B,kCAAA,KAAC6B,MAAK;AAAA,MAAA5B,WAAU;AAAA,MACZC,UAAA,CAACtG,kCAAA,IAAAkI,YAAA;AAAA,QACG5B,UAACtG,kCAAA,IAAAmI,WAAA;AAAA,UAAU9B,WAAU;AAAA,UACjBC,gDAAC,QAAM;AAAA,YAAAA,UAAA9F,gBAAgBwH;AAAAA,UAAU,CAAA;AAAA,QACrC,CAAA;AAAA,MACJ,CAAA,GACChI,kCAAA,IAAAoI,aAAA;AAAA,QACG9B,UAACF,kCAAA,KAAA,OAAA;AAAA,UAAIC,WAAU;AAAA,UACXC,UAAA,CAAAF,kCAAA,KAAC,OACG;AAAA,YAAAE,UAAA,CAAAtG,kCAAA,IAAC,KAAE;AAAA,cAAAqG,WAAU;AAAA,cAAiBC,UAAA9F,gBAAgBuC;AAAAA,YAAU,CAAA,GACxDqD,kCAAA,KAAC,KAAE;AAAA,cAAAC,WAAU;AAAA,cACTC,UAAA,CAACtG,kCAAA,IAAAqI,OAAA;AAAA,gBAAMhC,WAAU;AAAA,cAAe,CAAA,GAC/B7F,gBAAgBoH,YAAA;AAAA,YACrB,CAAA,GACAxB,kCAAA,KAAC,KAAE;AAAA,cAAAC,WAAU;AAAA,cACTC,UAAA,CAACtG,kCAAA,IAAAsI,QAAA;AAAA,gBAAOjC,WAAU;AAAA,cAAoB,CAAA,GACrC7F,gBAAgB+H,OAAA;AAAA,YACrB,CAAA,CAAA;AAAA,UACJ,CAAA,GACAvI,kCAAA,IAAC,OAAI;AAAA,YAAAqG,WAAU;AAAA,YACXC,UAAAtG,kCAAA,IAAC,OAAI;AAAA,cAAAqG,WAAU;AAAA,cAUVC,YAAA/F,kCAAMiI,gBAANjI,mBAAmBqH,kBAAiB,eAAexB,kCAAA,KAACM,QAAA;AAAA,gBAAOC,SAAQ;AAAA,gBAAUC,MAAK;AAAA,gBAC/EU,SAASA,MAAMzF,gBAAgB,IAAI;AAAA,gBAEnCyE,UAAA,CAACtG,kCAAA,IAAAyI,MAAA;AAAA,kBAAKpC,WAAU;AAAA,gBAAe,CAAA,GAAE,uBAAA;AAAA,cAAA,CAErC,IAAY;AAAA,YAChB,CAAA;AAAA,UACJ,CAAA,CAAA;AAAA,QACJ,CAAA;AAAA,MACJ,CAAA,CAAA;AAAA,IACJ,CAAA,GAEAD,kCAAA,KAAC,OAAI;AAAA,MAAAC,WAAU;AAAA,MACXC,UAAA,CAAAF,kCAAA,KAAC6B,MACG;AAAA,QAAA3B,UAAA,CAACtG,kCAAA,IAAAkI,YAAA;AAAA,UAAW7B,WAAU;AAAA,UAClBC,UAAAtG,kCAAA,IAACmI;YAAU9B,WAAU;AAAA,YAAsBC;UAAO,CAAA;AAAA,QACtD,CAAA,0CACC8B,aACG;AAAA,UAAA9B,UAAA,CAACF,kCAAA,KAAA,OAAA;AAAA,YAAIC,WAAU;AAAA,YAAqBC,UAAA,CAAA,MAAG9F,gBAAgBkI,YAAYC,eAAe,CAAA;AAAA,UAAE,CAAA,GACpFvC,kCAAA,KAAC,KAAE;AAAA,YAAAC,WAAU;AAAA,YAAgCC,UAAA,CAAA,SAAM9F,gBAAgBoI,aAAY,SAAA;AAAA,UAAO,CAAA,CAAA;AAAA,QAC1F,CAAA,CAAA;AAAA,MACJ,CAAA,0CACCX,MACG;AAAA,QAAA3B,UAAA,CAACtG,kCAAA,IAAAkI,YAAA;AAAA,UAAW7B,WAAU;AAAA,UAClBC,UAAAtG,kCAAA,IAACmI;YAAU9B,WAAU;AAAA,YAAsBC;UAAgB,CAAA;AAAA,QAC/D,CAAA,0CACC8B,aACG;AAAA,UAAA9B,UAAA,CAACF,kCAAA,KAAA,OAAA;AAAA,YAAIC,WAAU;AAAA,YAAqBC,UAAA,CAAA,MAAG9F,gBAAgBqH,cAAcc,eAAe,CAAA;AAAA,UAAE,CAAA,GACrF3I,kCAAA,IAAA,KAAA;AAAA,YAAEqG,WAAU;AAAA,YAAgCC,UAAoB;AAAA,UAAA,CAAA,CAAA;AAAA,QACrE,CAAA,CAAA;AAAA,MACJ,CAAA,CAAA;AAAA,IACJ,CAAA,GAECF,kCAAA,KAAAyC,MAAA;AAAA,MAAK3C,OAAOxE;AAAAA,MAAWoH,eAAe7C;AAAAA,MACnCK,UAAA,CAAAF,kCAAA,KAAC2C,UACG;AAAA,QAAAzC,UAAA,CAACtG,kCAAA,IAAAgJ,aAAA;AAAA,UAAY9C,OAAM;AAAA,UAAYI,UAAS;AAAA,QAAA,CAAA,GACvCtG,kCAAA,IAAAgJ,aAAA;AAAA,UAAY9C,OAAM;AAAA,UAASI,UAAM;AAAA,SAAA,GAIjC5D,gBAAgB1C,kCAAA,IAACgJ,aAAY;AAAA,UAAA9C,OAAM;AAAA,UAAgBI,UAAe;AAAA,QAAA,CAAA,CAAA;AAAA,MAGvE,CAAA,GACC5E,cAAc,mBAAoB0E,kCAAAA,KAAA,OAAA;AAAA,QAAIC,WAAU;AAAA,QAC7CC,UAAA,CAAAtG,kCAAA,IAACiJ,OAAA;AAAA,UACGC,aAAY;AAAA,UACZhD,OAAO/E;AAAAA,UACPwG,UAAWwB,OAAM/H,cAAc+H,EAAEC,OAAOlD,KAAK;AAAA,UAC7CG,WAAU;AAAA,QAAA,CACd,GAECD,kCAAA,KAAAiD,QAAA;AAAA,UAAOnD,OAAO9C;AAAAA,UAAU0F,eAAe1E;AAAAA,UACpCkC,UAAA,CAAAtG,kCAAA,IAACsJ;YAAcjD,WAAU;AAAA,YACrBC,gDAACiD,aAAY;AAAA,cAAAL,aAAY;AAAA,YAAiB,CAAA;AAAA,UAC9C,CAAA,0CACCM,eACG;AAAA,YAAAlD,UAAA,CAACtG,kCAAA,IAAAyJ,YAAA;AAAA,cAAWvD,OAAM;AAAA,cAAII,UAAU;AAAA,YAAA,CAAA,GAC/BtG,kCAAA,IAAAyJ,YAAA;AAAA,cAAWvD,OAAM;AAAA,cAAKI,UAAW;AAAA,YAAA,CAAA,GACjCtG,kCAAA,IAAAyJ,YAAA;AAAA,cAAWvD,OAAM;AAAA,cAAKI,UAAW;AAAA,YAAA,CAAA,GACjCtG,kCAAA,IAAAyJ,YAAA;AAAA,cAAWvD,OAAM;AAAA,cAAKI,UAAW;AAAA,YAAA,CAAA,CAAA;AAAA,UACtC,CAAA,CAAA;AAAA,QACJ,CAAA,CAAA;AAAA,MACJ,CAAA,GAYAF,kCAAA,KAACsD,aAAY;AAAA,QAAAxD,OAAM;AAAA,QACfI,UAAA,CAAAF,kCAAA,KAACuD,OACG;AAAA,UAAArD,UAAA,CAACtG,kCAAA,IAAA4J,aAAA;AAAA,YACGtD,iDAACuD,UACG;AAAA,cAAAvD,UAAA,CAAAtG,kCAAA,IAAC8J;gBAAUxD,UAAQ;AAAA,cAAA,CAAA,GACnBtG,kCAAA,IAAC8J;gBAAUxD,UAAU;AAAA,cAAA,CAAA,GACrBtG,kCAAA,IAAC8J;gBAAUxD,UAAM;AAAA,cAAA,CAAA,GACjBtG,kCAAA,IAAC8J;gBAAUxD,UAAa;AAAA,cAAA,CAAA,GACxBtG,kCAAA,IAAC8J;gBAAUxD,UAAM;AAAA,cAAA,CAAA,GACjBtG,kCAAA,IAAC8J;gBAAUxD,UAAc;AAAA,cAAA,CAAA,CAAA;AAAA,YAE7B,CAAA;AAAA,UACJ,CAAA,yCACCyD,WACI;AAAA,YAAAzD,UAAA7F,OAAOuJ,IAAKzF,kDACRsF,UACG;AAAA,cAAAvD,UAAA,CAACtG,kCAAA,IAAAiK,WAAA;AAAA,gBAAW3D,gBAAM9B;AAAAA,cAAa,CAAA,GAC/BxE,kCAAA,IAACiK;gBACI3D,UAAI,IAAA9C,KAAKe,MAAM2F,aAAa,EAAEC,mBAAmB,SAAS;AAAA,kBACvDC,KAAK;AAAA,kBACLC,OAAO;AAAA,kBACPC,MAAM;AAAA,gBACT,CAAA;AAAA,cACL,CAAA,GACCtK,kCAAA,IAAAiK,WAAA;AAAA,gBACG3D,UAACtG,kCAAA,IAAAuK,OAAA;AAAA,kBAAM5D,SAASpC,MAAMG,gBAAgB,cAAc,YAAY;AAAA,kBAC3D4B,UAAM/B,MAAAG;AAAAA,gBACX,CAAA;AAAA,cAEJ,CAAA,GACA1E,kCAAA,IAACiK,WAAW;AAAA,gBAAA3D,UAAA/B,MAAMQ;AAAAA,cAAa,CAAA,0CAC9BkF,WAAU;AAAA,gBAAA3D,UAAA,CAAA,MAAG/B,MAAMS,YAAY2D,eAAe,CAAA;AAAA,cAAE,CAAA,GAChD3I,kCAAA,IAAAiK,WAAA;AAAA,gBACI3D,UAAM/B,MAAAiG,gBAAgB,SAAS;AAAA,cACpC,CAAA,CAAA;AAAA,YAnBW,GAAAjG,MAAMC,YAwBrB,CACH;AAAA,UACL,CAAA,CAAA;AAAA,QACJ,CAAA,GACC2B,iBAAiBxF,WAAuB,CAAA;AAAA,MAC7C,CAAA,yCACC+I,aAAY;AAAA,QAAAxD,OAAM;AAAA,QACfI,UAACtG,kCAAA,IAAA,KAAA;AAAA,UAAEsG;QAAsD,CAAA;AAAA,MAC7D,CAAA,yCACCoD,aAAY;AAAA,QAAAxD,OAAM;AAAA,QACfI,UAACtG,kCAAA,IAAA,KAAA;AAAA,UAAEsG;QAAyC,CAAA;AAAA,MAChD,CAAA,yCACCoD,aAAY;AAAA,QAAAxD,OAAM;AAAA,QACfI,UAACtG,kCAAA,IAAA,KAAA;AAAA,UAAEsG;QAA2C,CAAA;AAAA,OAClD,GAEC5D,gBAAgB0D,kCAAA,KAACsD,aAAY;AAAA,QAAAxD,OAAM;AAAA,QAGhCI,UAAA,CAACF,kCAAA,KAAA,OAAA;AAAA,UAAIC,WAAU;AAAA,UAEXC,UAAA,CAAAtG,kCAAA,IAACiJ,OAAA;AAAA,YACGC,aAAY;AAAA,YACZhD,OAAO/E;AAAAA,YACPwG,UAAWwB,OAAM/H,cAAc+H,EAAEC,OAAOlD,KAAK;AAAA,YAC7CG,WAAU;AAAA,UAAA,CACd,GAGCD,kCAAA,KAAAG,MAAA;AAAA,YAAKF,WAAU;AAAA,YAAgEZ,QAAO;AAAA,YACnFa,UAAA,CAAAtG,kCAAA,IAAC;cAAMwG,MAAK;AAAA,cAASC,MAAK;AAAA,cAASP,OAAM;AAAA,YAA8B,CAAA,GACvElG,kCAAA,IAAC;cAAMwG,MAAK;AAAA,cAASC,MAAK;AAAA,cAAUP,OAAO9F,QAAQqE,SAAA;AAAA,YAAY,CAAA,GAE/DzE,kCAAA,IAAC;cAAMwG,MAAK;AAAA,cAASC,MAAK;AAAA,cAAeP,OAAO5C,cAAcmB,SAAA;AAAA,YAAY,CAAA,GAE1E2B,kCAAA,KAAC,OAAI;AAAA,cAAAC,WAAU;AAAA,cACXC,UAAA,CAAAtG,kCAAA,IAAC,SAAM;AAAA,gBAAAyK,SAAQ;AAAA,gBAAmBpE,WAAU;AAAA,gBAAoCC,UAEhF;AAAA,cAAA,CAAA,GACAtG,kCAAA,IAACiJ,OAAA;AAAA,gBACG/C,OAAO5C,cAAcoH,YAAA,EAAcC,MAAM,GAAG,EAAE,CAAC;AAAA,gBAC/ChD,UAAWwB,OAAM;AACb,wBAAMyB,OAAO,IAAIpH,KAAK2F,EAAEC,OAAOlD,KAAK;AACpC3C,mCAAiBqH,IAAI;AAAA,gBACzB;AAAA,gBACApE,MAAK;AAAA,gBACLH,WAAU;AAAA,cAAA,CACd,CAAA;AAAA,YACJ,CAAA,GACArG,kCAAA,IAAC0G,QAAA;AAAA,cACGL,WAAU;AAAA,cACVO,MAAK;AAAA,cACLJ,MAAK;AAAA,cACRF,UAAA;AAAA,YAAA,CAED,CAAA;AAAA,UACJ,CAAA,GAGCF,kCAAA,KAAAiD,QAAA;AAAA,YAAOnD,OAAO9C;AAAAA,YAAU0F,eAAe1E;AAAAA,YACpCkC,UAAA,CAAAtG,kCAAA,IAACsJ;cAAcjD,WAAU;AAAA,cACrBC,gDAACiD,aAAY;AAAA,gBAAAL,aAAY;AAAA,cAAiB,CAAA;AAAA,YAC9C,CAAA,0CACCM,eACG;AAAA,cAAAlD,UAAA,CAACtG,kCAAA,IAAAyJ,YAAA;AAAA,gBAAWvD,OAAM;AAAA,gBAAII,UAAU;AAAA,cAAA,CAAA,GAC/BtG,kCAAA,IAAAyJ,YAAA;AAAA,gBAAWvD,OAAM;AAAA,gBAAKI,UAAW;AAAA,cAAA,CAAA,GACjCtG,kCAAA,IAAAyJ,YAAA;AAAA,gBAAWvD,OAAM;AAAA,gBAAKI,UAAW;AAAA,cAAA,CAAA,GACjCtG,kCAAA,IAAAyJ,YAAA;AAAA,gBAAWvD,OAAM;AAAA,gBAAKI,UAAW;AAAA,cAAA,CAAA,CAAA;AAAA,YACtC,CAAA,CAAA;AAAA,UACJ,CAAA,CAAA;AAAA,QACJ,CAAA,0CACCqD,OACG;AAAA,UAAArD,UAAA,CAACtG,kCAAA,IAAA4J,aAAA;AAAA,YACGtD,iDAACuD,UACG;AAAA,cAAAvD,UAAA,CAAAtG,kCAAA,IAAC8J;gBAAUxD,UAAU;AAAA,cAAA,CAAA,GACrBtG,kCAAA,IAAC8J;gBAAUxD,UAAS;AAAA,cAAA,CAAA,GACpBtG,kCAAA,IAAC8J;gBAAUxD,UAAK;AAAA,cAAA,CAAA,GAChBtG,kCAAA,IAAC8J;gBAAUxD,UAAO;AAAA,cAAA,CAAA,CAAA;AAAA,YACtB,CAAA;AAAA,UACJ,CAAA,GACCtG,kCAAA,IAAA+J,WAAA;AAAA,YACIzD,UAAe5F,eAAA4D,OAAQL,cAAaD,aAAaC,QAAQ,CAAC,EAAE+F,IAAKa,kDAC7DhB,UACG;AAAA,cAAAvD,UAAA,CAAAtG,kCAAA,IAACiK,WACG;AAAA,gBAAA3D,UAAAtG,kCAAA,IAAC,OAAA;AAAA,kBACG8K,KAAKD,MAAME;AAAAA,kBACXC,KAAKH,MAAM3G;AAAAA,kBACXmC,WAAU;AAAA,gBACd,CAAA;AAAA,cACJ,CAAA,GACArG,kCAAA,IAACiK,WAAW;AAAA,gBAAA3D,UAAAuE,MAAM3G;AAAAA,cAAS,CAAA,GAC1B2G,MAAMI,YAAY,SAAUjL,kCAAAA,IAAAiK,WAAA;AAAA,gBAAU5D,WAAU;AAAA,gBAE5CC,UAAUpE,UAAAF,aAAa6I,MAAMK,eAC1B9E,kCAAA,KAAAe,4BAAA;AAAA,kBAAAb,UAAA,CAAAtG,kCAAA,IAACiJ,OAAA;AAAA,oBAAMC,aAAa2B,MAAMM,YAAY1G,SAAS;AAAA,oBAG3CkD,UAAWwB,OAAM;AACb,4BAAMjD,QAAQkF,OAAOjC,EAAEC,OAAOlD,KAAK;AACnC,0BAAIA,QAAQ,KAAKiD,EAAEC,OAAOlD,UAAU,IAAI;AAEpC7D,kCAAU6D,KAAK;AAAA,sBACnB;AAAA,oBACJ;AAAA,oBACAM,MAAK;AAAA,oBACLH,WAAU;AAAA,kBAAA,CAAa,yCAC1BgF,MAAK;AAAA,oBAAAC,QAAQ;AAAA,oBAAIC,OAAO;AAAA,oBAAIjE,SAASA,MAAM5B,kBAAkBmF,MAAMK,cAAcL,MAAMhF,iBAAiBzD,MAAM;AAAA,oBAAGiE,WAAU;AAAA,kBAAuB,CAAA,CAAA;AAAA,gBAAA,CACvJ,IAIMD,kCAAA,KAAAe,4BAAA;AAAA,kBAAAb,UAAA,CAAA,KAAE,KAAKuE,MAAMM,WAAW,IAErBvI,kBAAkB5C,kCAAA,IAACwL,WAAS;AAAA,oBAAAF,QAAQ;AAAA,oBAAIC,OAAO;AAAA,oBAAIjE,SAASA,MAAMvB,WAAW8E,MAAMK,YAAY;AAAA,kBAAG,CAAA,CAAA;AAAA,gBAEvG,CAAA;AAAA,cAER,CAAA,GACAlL,kCAAA,IAACiK;gBAAW3D,UAAMuE,MAAAI,gDAAWvE,QAAO;AAAA,kBAAAF,MAAK;AAAA,kBAASc,SAASA,MAAMnC,uBAAuB0F,MAAMK,cAAc9K,OAAO;AAAA,kBAC9GkG,UAAAuE,MAAMK,iBAAiBlJ,YAAYM,QAAQsB,UAAU,SAChD,cACA;AAAA,gBAAA,CACV,IAAY5D,kCAAA,IAACyL,QAAA;AAAA,kBAAOC,SAASb,MAAMc;AAAAA,kBAC/BC,iBAAiBA,MAAMhG,mBAAmBiF,MAAMK,cAAcL,MAAMhF,iBAAiB,CAAEgF,MAAMc,OAAQ;AAAA,gBAAG,CAAA;AAAA,cAC5G,CAAA,CAAA;AAAA,YA1CW,GAAAd,MAAMK,YA6CrB,CACH;AAAA,UACL,CAAA,CAAA;AAAA,QACJ,CAAA,GACC/E,iBAAiBxF,WAAuB,CAAA;AAAA,MAC7C,CAAA,CAAA;AAAA,IACJ,CAAA,CAAA;AAAA,EACJ,CAAA;AAER;", "x_google_ignoreList": [0]}