{"version": 3, "file": "home.localities._areaId.edit-BPUHB0ho.js", "sources": ["../../../app/routes/home.localities.$areaId.edit.tsx"], "sourcesContent": ["import { useEffect, useState, useCallback, useRef } from 'react'\r\nimport { json, LoaderFunction, ActionFunction, redirect } from '@remix-run/node'\r\nimport { useLoaderData, useNavigate, Form } from '@remix-run/react'\r\nimport { ArrowLeft } from 'lucide-react'\r\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@components/ui/card\"\r\nimport { Input } from \"@components/ui/input\"\r\nimport { Button } from \"@components/ui/button\"\r\nimport { updateArea, getAreaById } from '~/services/businessConsoleService'\r\nimport { getSession } from \"@utils/session.server\"\r\nimport { GoogleMap, LoadScript, Polygon, DrawingManager, StandaloneSearchBox } from '@react-google-maps/api'\r\nimport { decodePolygon } from \"@utils/polyline-utils\"\r\n\r\n// Correctly import from a CommonJS module:\r\nimport * as polylineCodec from '@googlemaps/polyline-codec';\r\n\r\nimport type { User } from \"~/types\"\r\nimport { MasterLocalities } from '~/types/api/businessConsoleService/SellerManagement'\r\n\r\ninterface LoaderData {\r\n  area: MasterLocalities\r\n  googleMapsApiKey: string\r\n}\r\n\r\nexport const loader: LoaderFunction = async ({ params, request }) => {\r\n  try {\r\n    const session = await getSession(request.headers.get(\"Cookie\"))\r\n    const user = session.get(\"user\") as User\r\n    const areaId = params.areaId\r\n\r\n    if (!areaId) {\r\n      throw new Error(\"Area ID is required\")\r\n    }\r\n\r\n    const areaResponse = await getAreaById(parseInt(areaId, 10), request)\r\n\r\n    return json<LoaderData>({\r\n      area: areaResponse.data,\r\n      googleMapsApiKey: process.env.GOOGLE_MAPS_API_KEY || '',\r\n    })\r\n  } catch (error) {\r\n    console.error('Failed to fetch area:', error)\r\n    return json({ error: \"Failed to fetch area\" }, { status: 500 })\r\n  }\r\n}\r\n\r\nexport const action: ActionFunction = async ({ request, params }) => {\r\n  const session = await getSession(request.headers.get(\"Cookie\"))\r\n  const user = session.get(\"user\") as User\r\n  const formData = await request.formData()\r\n  const areaId = params.areaId\r\n\r\n  if (!areaId) {\r\n    throw new Error(\"Area ID is required\")\r\n  }\r\n\r\n  if (request.method.toUpperCase() === \"PUT\") {\r\n    const name = formData.get('name') as string\r\n    const polygon = formData.get('polygon') as string\r\n\r\n    try {\r\n      await updateArea(\r\n        user.userId,\r\n        {\r\n          id: parseInt(areaId, 10),\r\n          name,\r\n          polygon,\r\n        },\r\n        request\r\n      )\r\n      return redirect('/home/<USER>')\r\n    } catch (error) {\r\n      return json({ error: \"Failed to update area\" }, { status: 500 })\r\n    }\r\n  }\r\n\r\n  return null\r\n}\r\n\r\nconst BANGALORE_CENTER = { lat: 12.9716, lng: 77.5946 }\r\n\r\nexport default function EditLocality() {\r\n  const { area, googleMapsApiKey } = useLoaderData<LoaderData>()\r\n  // Ensure we only render on the client.\r\n  const [isClient, setIsClient] = useState(false)\r\n  // Track when the Google Maps script is loaded.\r\n  const [scriptLoaded, setScriptLoaded] = useState(false)\r\n  const [name, setName] = useState(area.name)\r\n  const [polygon, setPolygon] = useState<google.maps.LatLngLiteral[]>(\r\n    area.polygon ? decodePolygon(area.polygon) : []\r\n  )\r\n  const [map, setMap] = useState<google.maps.Map | null>(null)\r\n  const [searchBox, setSearchBox] = useState<google.maps.places.SearchBox | null>(null)\r\n  const polygonRef = useRef<google.maps.Polygon | null>(null)\r\n  const polygonListenersRef = useRef<google.maps.MapsEventListener[]>([])\r\n  const navigate = useNavigate()\r\n\r\n  // Set client flag on mount.\r\n  useEffect(() => {\r\n    setIsClient(true)\r\n  }, [])\r\n\r\n  const onLoad = useCallback((mapInstance: google.maps.Map) => {\r\n    setMap(mapInstance)\r\n  }, [])\r\n\r\n  const onPolygonComplete = useCallback((poly: google.maps.Polygon) => {\r\n    // Remove any previously drawn polygon and clear its listeners.\r\n    if (polygonRef.current) {\r\n      polygonRef.current.setMap(null)\r\n      polygonListenersRef.current.forEach(listener =>\r\n        google.maps.event.removeListener(listener)\r\n      )\r\n      polygonListenersRef.current = []\r\n    }\r\n    polygonRef.current = poly\r\n\r\n    // Convert LatLng objects to LatLngLiteral objects.\r\n    const polyArray = poly.getPath().getArray().map((latlng) => ({\r\n      lat: latlng.lat(),\r\n      lng: latlng.lng(),\r\n    }))\r\n    setPolygon(polyArray)\r\n\r\n    const path = poly.getPath()\r\n    const updatePolygon = () => {\r\n      setPolygon(\r\n        path.getArray().map((latlng) => ({\r\n          lat: latlng.lat(),\r\n          lng: latlng.lng(),\r\n        }))\r\n      )\r\n    }\r\n\r\n    const setAtListener = google.maps.event.addListener(path, 'set_at', updatePolygon)\r\n    const insertAtListener = google.maps.event.addListener(path, 'insert_at', updatePolygon)\r\n    const removeAtListener = google.maps.event.addListener(path, 'remove_at', updatePolygon)\r\n\r\n    polygonListenersRef.current.push(setAtListener, insertAtListener, removeAtListener)\r\n  }, [])\r\n\r\n  const onSearchBoxLoad = (ref: google.maps.places.SearchBox) => {\r\n    setSearchBox(ref)\r\n  }\r\n\r\n  const onPlacesChanged = () => {\r\n    if (searchBox && map) {\r\n      const places = searchBox.getPlaces()\r\n      if (places && places.length > 0) {\r\n        const place = places[0]\r\n        if (place.geometry && place.geometry.location) {\r\n          map.panTo(place.geometry.location)\r\n          map.setZoom(15)\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  useEffect(() => {\r\n    if (map && polygon.length > 0) {\r\n      const bounds = new google.maps.LatLngBounds()\r\n      polygon.forEach(coord => bounds.extend(coord))\r\n      map.fitBounds(bounds)\r\n    }\r\n  }, [map, polygon])\r\n\r\n  // Cleanup polygon listeners on unmount.\r\n  useEffect(() => {\r\n    return () => {\r\n      polygonListenersRef.current.forEach(listener =>\r\n        google.maps.event.removeListener(listener)\r\n      )\r\n      polygonListenersRef.current = []\r\n    }\r\n  }, [])\r\n\r\n  // Until we are on the client, display a loading message.\r\n  if (!isClient) {\r\n    return <div>Loading...</div>\r\n  }\r\n\r\n  return (\r\n    <div className=\"h-[calc(100vh-64px)] bg-white p-6 overflow-y-auto\">\r\n      <div className=\"flex justify-between items-center mb-6\">\r\n        <Button onClick={() => navigate('/home/<USER>')} variant=\"ghost\" size=\"icon\">\r\n          <ArrowLeft size={24} />\r\n        </Button>\r\n      </div>\r\n\r\n      <Card className=\"w-full mx-auto\">\r\n        <CardHeader>\r\n          <CardTitle>Edit Area: {area.name}</CardTitle>\r\n          <CardDescription>Update the area name and boundary</CardDescription>\r\n        </CardHeader>\r\n        <CardContent className=\"space-y-4\">\r\n          <Form method=\"PUT\">\r\n            <div className=\"space-y-4\">\r\n              <div>\r\n                <label htmlFor=\"name\" className=\"block text-sm font-medium text-gray-700\">\r\n                  Area Name\r\n                </label>\r\n                <Input\r\n                  id=\"name\"\r\n                  name=\"name\"\r\n                  value={name}\r\n                  onChange={(e) => setName(e.target.value)}\r\n                  required\r\n                  className=\"mt-1\"\r\n                />\r\n              </div>\r\n\r\n              {/* Hidden input to store the encoded polygon */}\r\n              <input type=\"hidden\" name=\"polygon\" value={encode(polygon)} />\r\n\r\n              <div className=\"h-[400px] relative\">\r\n                <LoadScript\r\n                  googleMapsApiKey={googleMapsApiKey}\r\n                  libraries={[\"drawing\", \"places\"]}\r\n                  onLoad={() => setScriptLoaded(true)}\r\n                >\r\n                  <GoogleMap\r\n                    mapContainerClassName=\"w-full h-full\"\r\n                    center={BANGALORE_CENTER}\r\n                    zoom={11}\r\n                    onLoad={onLoad}\r\n                  >\r\n                    <StandaloneSearchBox onLoad={onSearchBoxLoad} onPlacesChanged={onPlacesChanged}>\r\n                      <Input\r\n                        type=\"text\"\r\n                        placeholder=\"Search for a location\"\r\n                        className=\"absolute top-2 left-1/2 transform -translate-x-1/2 w-1/3 z-10\"\r\n                      />\r\n                    </StandaloneSearchBox>\r\n\r\n                    {polygon.length > 0 && (\r\n                      <Polygon\r\n                        path={polygon}\r\n                        options={{\r\n                          fillColor: \"#4F46E5\",\r\n                          fillOpacity: 0.4,\r\n                          strokeColor: \"#4F46E5\",\r\n                          strokeWeight: 2,\r\n                        }}\r\n                      />\r\n                    )}\r\n\r\n                    {/* Render DrawingManager only after the script has loaded */}\r\n                    {scriptLoaded &&\r\n                      typeof window !== \"undefined\" &&\r\n                      window.google && (\r\n                        <DrawingManager\r\n                          onPolygonComplete={onPolygonComplete}\r\n                          options={{\r\n                            polygonOptions: {\r\n                              fillColor: \"#4F46E5\",\r\n                              fillOpacity: 0.4,\r\n                              strokeColor: \"#4F46E5\",\r\n                              strokeWeight: 2,\r\n                              clickable: true,\r\n                              editable: true,\r\n                              draggable: true,\r\n                            },\r\n                            drawingControl: true,\r\n                            drawingControlOptions: {\r\n                              position: window.google.maps.ControlPosition.TOP_CENTER,\r\n                              drawingModes: [window.google.maps.drawing.OverlayType.POLYGON],\r\n                            },\r\n                          }}\r\n                        />\r\n                      )}\r\n                  </GoogleMap>\r\n                </LoadScript>\r\n              </div>\r\n\r\n              <Button type=\"submit\" className=\"w-full\">\r\n                Update Area\r\n              </Button>\r\n            </div>\r\n          </Form>\r\n        </CardContent>\r\n      </Card>\r\n    </div>\r\n  )\r\n}"], "names": ["BANGALORE_CENTER", "lat", "lng", "EditLocality", "area", "googleMapsApiKey", "useLoaderData", "isClient", "setIsClient", "useState", "scriptLoaded", "setScriptLoaded", "name", "setName", "polygon", "setPolygon", "decodePolygon", "map", "setMap", "searchBox", "setSearchBox", "polygonRef", "useRef", "polygonListenersRef", "navigate", "useNavigate", "useEffect", "onLoad", "useCallback", "mapInstance", "onPolygonComplete", "poly", "current", "for<PERSON>ach", "listener", "google", "maps", "event", "removeListener", "polyArray", "<PERSON><PERSON><PERSON>", "getArray", "latlng", "path", "updatePolygon", "setAtListener", "addListener", "insertAtListener", "removeAtListener", "push", "onSearchBoxLoad", "ref", "onPlacesChanged", "places", "getPlaces", "length", "place", "geometry", "location", "panTo", "setZoom", "bounds", "LatLngBounds", "coord", "extend", "fitBounds", "jsx", "children", "jsxs", "className", "<PERSON><PERSON>", "onClick", "variant", "size", "ArrowLeft", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardTitle", "CardDescription", "<PERSON><PERSON><PERSON><PERSON>", "Form", "method", "htmlFor", "Input", "id", "value", "onChange", "e", "target", "required", "type", "encode", "LoadScript", "libraries", "GoogleMap", "mapContainerClassName", "center", "zoom", "StandaloneSearchBox", "placeholder", "Polygon", "options", "fillColor", "fillOpacity", "strokeColor", "strokeWeight", "window", "DrawingManager", "polygonOptions", "clickable", "editable", "draggable", "drawingControl", "drawingControlOptions", "position", "ControlPosition", "TOP_CENTER", "drawingModes", "drawing", "OverlayType", "POLYGON"], "mappings": ";;;;;;;;;;;;;AA8EA,MAAMA,mBAAmB;AAAA,EAAEC,KAAK;AAAA,EAASC,KAAK;AAAQ;AAEtD,SAAwBC,eAAe;AACrC,QAAM;AAAA,IAAEC;AAAAA,IAAMC;AAAAA,EAAiB,IAAIC,cAA0B;AAE7D,QAAM,CAACC,UAAUC,WAAW,IAAIC,aAAAA,SAAS,KAAK;AAE9C,QAAM,CAACC,cAAcC,eAAe,IAAIF,aAAAA,SAAS,KAAK;AACtD,QAAM,CAACG,MAAMC,OAAO,IAAIJ,aAAAA,SAASL,KAAKQ,IAAI;AACpC,QAAA,CAACE,SAASC,UAAU,IAAIN,sBAC5BL,KAAKU,UAAUE,cAAcZ,KAAKU,OAAO,IAAI,CAAA,CAC/C;AACA,QAAM,CAACG,KAAKC,MAAM,IAAIT,aAAAA,SAAiC,IAAI;AAC3D,QAAM,CAACU,WAAWC,YAAY,IAAIX,aAAAA,SAA8C,IAAI;AAC9E,QAAAY,aAAaC,oBAAmC,IAAI;AACpD,QAAAC,sBAAsBD,aAAwC,OAAA,EAAE;AACtE,QAAME,WAAWC,YAAY;AAG7BC,eAAAA,UAAU,MAAM;AACdlB,gBAAY,IAAI;AAAA,EAClB,GAAG,EAAE;AAEC,QAAAmB,SAASC,aAAY,YAACC,iBAAiC;AAC3DX,WAAOW,WAAW;AAAA,EACpB,GAAG,EAAE;AAEC,QAAAC,oBAAoBF,aAAY,YAACG,UAA8B;AAEnE,QAAIV,WAAWW,SAAS;AACXX,iBAAAW,QAAQd,OAAO,IAAI;AAC9BK,0BAAoBS,QAAQC,QAC1BC,cAAAC,OAAOC,KAAKC,MAAMC,eAAeJ,QAAQ,CAC3C;AACAX,0BAAoBS,UAAU,CAAC;AAAA,IACjC;AACAX,eAAWW,UAAUD;AAGf,UAAAQ,YAAYR,KAAKS,QAAQ,EAAEC,SAAW,EAAAxB,IAAKyB,aAAY;AAAA,MAC3DzC,KAAKyC,OAAOzC,IAAI;AAAA,MAChBC,KAAKwC,OAAOxC,IAAI;AAAA,IAClB,EAAE;AACFa,eAAWwB,SAAS;AAEd,UAAAI,OAAOZ,KAAKS,QAAQ;AAC1B,UAAMI,gBAAgBA,MAAM;AAC1B7B,iBACE4B,KAAKF,WAAWxB,IAAKyB,aAAY;AAAA,QAC/BzC,KAAKyC,OAAOzC,IAAI;AAAA,QAChBC,KAAKwC,OAAOxC,IAAI;AAAA,MAChB,EAAA,CACJ;AAAA,IACF;AAEA,UAAM2C,gBAAgBV,OAAOC,KAAKC,MAAMS,YAAYH,MAAM,UAAUC,aAAa;AACjF,UAAMG,mBAAmBZ,OAAOC,KAAKC,MAAMS,YAAYH,MAAM,aAAaC,aAAa;AACvF,UAAMI,mBAAmBb,OAAOC,KAAKC,MAAMS,YAAYH,MAAM,aAAaC,aAAa;AAEvFrB,wBAAoBS,QAAQiB,KAAKJ,eAAeE,kBAAkBC,gBAAgB;AAAA,EACpF,GAAG,EAAE;AAEC,QAAAE,kBAAmBC,SAAsC;AAC7D/B,iBAAa+B,GAAG;AAAA,EAClB;AAEA,QAAMC,kBAAkBA,MAAM;AAC5B,QAAIjC,aAAaF,KAAK;AACd,YAAAoC,SAASlC,UAAUmC,UAAU;AAC/B,UAAAD,UAAUA,OAAOE,SAAS,GAAG;AACzB,cAAAC,QAAQH,OAAO,CAAC;AACtB,YAAIG,MAAMC,YAAYD,MAAMC,SAASC,UAAU;AACzCzC,cAAA0C,MAAMH,MAAMC,SAASC,QAAQ;AACjCzC,cAAI2C,QAAQ,EAAE;AAAA,QAChB;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAEAlC,eAAAA,UAAU,MAAM;AACV,QAAAT,OAAOH,QAAQyC,SAAS,GAAG;AAC7B,YAAMM,SAAS,IAAI1B,OAAOC,KAAK0B,aAAa;AAC5ChD,cAAQmB,QAAQ8B,WAASF,OAAOG,OAAOD,KAAK,CAAC;AAC7C9C,UAAIgD,UAAUJ,MAAM;AAAA,IACtB;AAAA,EACF,GAAG,CAAC5C,KAAKH,OAAO,CAAC;AAGjBY,eAAAA,UAAU,MAAM;AACd,WAAO,MAAM;AACXH,0BAAoBS,QAAQC,QAC1BC,cAAAC,OAAOC,KAAKC,MAAMC,eAAeJ,QAAQ,CAC3C;AACAX,0BAAoBS,UAAU,CAAC;AAAA,IACjC;AAAA,EACF,GAAG,EAAE;AAGL,MAAI,CAACzB,UAAU;AACN,WAAA2D,kCAAAA,IAAC;MAAIC,UAAU;AAAA,IAAA,CAAA;AAAA,EACxB;AAGE,SAAAC,kCAAAA,KAAC,OAAI;AAAA,IAAAC,WAAU;AAAA,IACbF,UAAA,CAAAD,kCAAA,IAAC;MAAIG,WAAU;AAAA,MACbF,gDAACG,QAAO;AAAA,QAAAC,SAASA,MAAM/C,SAAS,kBAAkB;AAAA,QAAGgD,SAAQ;AAAA,QAAQC,MAAK;AAAA,QACxEN,UAAAD,kCAAA,IAACQ;UAAUD,MAAM;AAAA,QAAI,CAAA;AAAA,MACvB,CAAA;AAAA,IACF,CAAA,GAEAL,kCAAA,KAACO,MAAK;AAAA,MAAAN,WAAU;AAAA,MACdF,UAAA,CAAAC,kCAAA,KAACQ,YACC;AAAA,QAAAT,UAAA,CAAAC,kCAAA,KAACS,WAAU;AAAA,UAAAV,UAAA,CAAA,eAAY/D,KAAKQ,IAAA;AAAA,QAAK,CAAA,GACjCsD,kCAAA,IAACY;UAAgBX,UAAiC;AAAA,QAAA,CAAA,CAAA;AAAA,MACpD,CAAA,GACAD,kCAAA,IAACa,aAAY;AAAA,QAAAV,WAAU;AAAA,QACrBF,UAAAD,kCAAA,IAACc,MAAK;AAAA,UAAAC,QAAO;AAAA,UACXd,UAAAC,kCAAA,KAAC,OAAI;AAAA,YAAAC,WAAU;AAAA,YACbF,UAAA,CAAAC,kCAAA,KAAC,OACC;AAAA,cAAAD,UAAA,CAAAD,kCAAA,IAAC,SAAM;AAAA,gBAAAgB,SAAQ;AAAA,gBAAOb,WAAU;AAAA,gBAA0CF,UAE1E;AAAA,cAAA,CAAA,GACAD,kCAAA,IAACiB,OAAA;AAAA,gBACCC,IAAG;AAAA,gBACHxE,MAAK;AAAA,gBACLyE,OAAOzE;AAAAA,gBACP0E,UAAWC,OAAM1E,QAAQ0E,EAAEC,OAAOH,KAAK;AAAA,gBACvCI,UAAQ;AAAA,gBACRpB,WAAU;AAAA,cAAA,CACZ,CAAA;AAAA,YACF,CAAA,GAGAH,kCAAA,IAAC;cAAMwB,MAAK;AAAA,cAAS9E,MAAK;AAAA,cAAUyE,OAAOM,OAAO7E,OAAO;AAAA,YAAG,CAAA,GAE5DoD,kCAAA,IAAC,OAAI;AAAA,cAAAG,WAAU;AAAA,cACbF,UAAAD,kCAAA,IAAC0B,YAAA;AAAA,gBACCvF;AAAAA,gBACAwF,WAAW,CAAC,WAAW,QAAQ;AAAA,gBAC/BlE,QAAQA,MAAMhB,gBAAgB,IAAI;AAAA,gBAElCwD,UAAAC,kCAAA,KAAC0B,WAAA;AAAA,kBACCC,uBAAsB;AAAA,kBACtBC,QAAQhG;AAAAA,kBACRiG,MAAM;AAAA,kBACNtE;AAAAA,kBAEAwC,UAAA,CAACD,kCAAA,IAAAgC,qBAAA;AAAA,oBAAoBvE,QAAQuB;AAAAA,oBAAiBE;AAAAA,oBAC5Ce,UAAAD,kCAAA,IAACiB,OAAA;AAAA,sBACCO,MAAK;AAAA,sBACLS,aAAY;AAAA,sBACZ9B,WAAU;AAAA,oBACZ,CAAA;AAAA,kBACF,CAAA,GAECvD,QAAQyC,SAAS,KAChBW,kCAAAA,IAACkC,SAAA;AAAA,oBACCzD,MAAM7B;AAAAA,oBACNuF,SAAS;AAAA,sBACPC,WAAW;AAAA,sBACXC,aAAa;AAAA,sBACbC,aAAa;AAAA,sBACbC,cAAc;AAAA,oBAChB;AAAA,kBAAA,CACF,GAID/F,gBACC,OAAOgG,WAAW,eAClBA,OAAOvE,UACL+B,kCAAA,IAACyC,gBAAA;AAAA,oBACC7E;AAAAA,oBACAuE,SAAS;AAAA,sBACPO,gBAAgB;AAAA,wBACdN,WAAW;AAAA,wBACXC,aAAa;AAAA,wBACbC,aAAa;AAAA,wBACbC,cAAc;AAAA,wBACdI,WAAW;AAAA,wBACXC,UAAU;AAAA,wBACVC,WAAW;AAAA,sBACb;AAAA,sBACAC,gBAAgB;AAAA,sBAChBC,uBAAuB;AAAA,wBACrBC,UAAUR,OAAOvE,OAAOC,KAAK+E,gBAAgBC;AAAAA,wBAC7CC,cAAc,CAACX,OAAOvE,OAAOC,KAAKkF,QAAQC,YAAYC,OAAO;AAAA,sBAC/D;AAAA,oBACF;AAAA,kBAAA,CACF,CAAA;AAAA,gBAEN,CAAA;AAAA,cACF,CAAA;AAAA,YACF,CAAA,yCAEClD,QAAO;AAAA,cAAAoB,MAAK;AAAA,cAASrB,WAAU;AAAA,cAASF,UAEzC;AAAA,YAAA,CAAA,CAAA;AAAA,UACF,CAAA;AAAA,QACF,CAAA;AAAA,MACF,CAAA,CAAA;AAAA,IACF,CAAA,CAAA;AAAA,EACF,CAAA;AAEJ;"}