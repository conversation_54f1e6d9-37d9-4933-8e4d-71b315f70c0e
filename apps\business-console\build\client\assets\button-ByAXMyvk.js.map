{"version": 3, "file": "button-ByAXMyvk.js", "sources": ["../../../app/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\nimport { cn } from \"~/lib/utils\"\r\n\r\n// Add the \"loading\" variant\r\nconst buttonVariants = cva(\r\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\r\n        destructive:\r\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\r\n        outline:\r\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\r\n        secondary:\r\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\r\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\r\n        link: \"text-primary underline-offset-4 hover:underline\",\r\n      },\r\n      size: {\r\n        default: \"h-10 px-4 py-2\",\r\n        sm: \"h-9 rounded-md px-3\",\r\n        lg: \"h-11 rounded-md px-8\",\r\n        icon: \"h-10 w-10\",\r\n      },\r\n      loading: {\r\n        true: \"opacity-70 pointer-events-none\", // Dim and disable interaction when loading\r\n        false: \"\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n      loading: false,\r\n    },\r\n  }\r\n)\r\n\r\nexport interface ButtonProps\r\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\r\n  VariantProps<typeof buttonVariants> {\r\n  asChild?: boolean\r\n  loading?: boolean\r\n}\r\n\r\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\r\n  ({ className, variant, size, loading = false, asChild = false, children, ...props }, ref) => {\r\n    const Comp = asChild ? Slot : \"button\"\r\n\r\n    return (\r\n      <Comp\r\n        className={cn(buttonVariants({ variant, size, loading, className }))}\r\n        ref={ref}\r\n        disabled={loading || props.disabled} // Disable when loading\r\n        {...props}\r\n      >\r\n        {loading ? (\r\n          <>\r\n            <svg\r\n              className=\"h-4 w-4 animate-spin text-white\"\r\n              xmlns=\"http://www.w3.org/2000/svg\"\r\n              fill=\"none\"\r\n              viewBox=\"0 0 24 24\"\r\n            >\r\n              <circle\r\n                className=\"opacity-25\"\r\n                cx=\"12\"\r\n                cy=\"12\"\r\n                r=\"10\"\r\n                stroke=\"currentColor\"\r\n                strokeWidth=\"4\"\r\n              ></circle>\r\n              <path\r\n                className=\"opacity-75\"\r\n                fill=\"currentColor\"\r\n                d=\"M4 12a8 8 0 018-8v4a4 4 0 00-4 4H4z\"\r\n              ></path>\r\n            </svg>\r\n            Loading...\r\n          </>\r\n        ) : (\r\n          children\r\n        )}\r\n      </Comp>\r\n    )\r\n  }\r\n)\r\n\r\nButton.displayName = \"Button\"\r\n\r\nexport { Button, buttonVariants }\r\n"], "names": ["React.forwardRef", "jsx", "jsxs", "Fragment"], "mappings": ";;;;AAMA,MAAM,iBAAiB;AAAA,EACrB;AAAA,EACA;AAAA,IACE,UAAU;AAAA,MACR,SAAS;AAAA,QACP,SAAS;AAAA,QACT,aACE;AAAA,QACF,SACE;AAAA,QACF,WACE;AAAA,QACF,OAAO;AAAA,QACP,MAAM;AAAA,MACR;AAAA,MACA,MAAM;AAAA,QACJ,SAAS;AAAA,QACT,IAAI;AAAA,QACJ,IAAI;AAAA,QACJ,MAAM;AAAA,MACR;AAAA,MACA,SAAS;AAAA,QACP,MAAM;AAAA;AAAA,QACN,OAAO;AAAA,MAAA;AAAA,IAEX;AAAA,IACA,iBAAiB;AAAA,MACf,SAAS;AAAA,MACT,MAAM;AAAA,MACN,SAAS;AAAA,IAAA;AAAA,EACX;AAEJ;AASA,MAAM,SAASA,aAAM;AAAA,EACnB,CAAC,EAAE,WAAW,SAAS,MAAM,UAAU,OAAO,UAAU,OAAO,UAAU,GAAG,MAAA,GAAS,QAAQ;AACrF,UAAA,OAAO,UAAU,OAAO;AAG5B,WAAAC,kCAAA;AAAA,MAAC;AAAA,MAAA;AAAA,QACC,WAAW,GAAG,eAAe,EAAE,SAAS,MAAM,SAAS,UAAU,CAAC,CAAC;AAAA,QACnE;AAAA,QACA,UAAU,WAAW,MAAM;AAAA,QAC1B,GAAG;AAAA,QAEH,oBAEGC,kCAAAA,KAAAC,kBAAA,UAAA,EAAA,UAAA;AAAA,UAAAD,kCAAA;AAAA,YAAC;AAAA,YAAA;AAAA,cACC,WAAU;AAAA,cACV,OAAM;AAAA,cACN,MAAK;AAAA,cACL,SAAQ;AAAA,cAER,UAAA;AAAA,gBAAAD,kCAAA;AAAA,kBAAC;AAAA,kBAAA;AAAA,oBACC,WAAU;AAAA,oBACV,IAAG;AAAA,oBACH,IAAG;AAAA,oBACH,GAAE;AAAA,oBACF,QAAO;AAAA,oBACP,aAAY;AAAA,kBAAA;AAAA,gBACb;AAAA,gBACDA,kCAAA;AAAA,kBAAC;AAAA,kBAAA;AAAA,oBACC,WAAU;AAAA,oBACV,MAAK;AAAA,oBACL,GAAE;AAAA,kBAAA;AAAA,gBAAA;AAAA,cACH;AAAA,YAAA;AAAA,UACH;AAAA,UAAM;AAAA,QAAA,EAAA,CAER,IAEA;AAAA,MAAA;AAAA,IAEJ;AAAA,EAAA;AAGN;AAEA,OAAO,cAAc;"}