{"version": 3, "file": "home.localities.new-UAyIxODY.js", "sources": ["../../../app/routes/home.localities.new.tsx"], "sourcesContent": ["import { useState, useRef, useEffect, useCallback } from 'react'\r\nimport { useActionData, useLoaderData, useNavigate, useSubmit } from '@remix-run/react'\r\nimport { ActionFunction, json, LoaderFunction, redirect } from '@remix-run/node'\r\nimport { activateSeller<PERSON><PERSON>, createArea, getDistrictsAndStates } from '~/services/businessConsoleService'\r\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@components/ui/card\"\r\nimport { Button } from \"@components/ui/button\"\r\nimport { SellerArea } from '~/types/api/businessConsoleService/Areas'\r\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from \"@components/ui/select\"\r\nimport { getSession } from \"@utils/session.server\"\r\nimport type { User } from \"~/types\"\r\nimport { ArrowLeft } from 'lucide-react'\r\nimport { useToast } from \"@hooks/use-toast\";\r\nimport { Input } from \"@components/ui/input\";\r\nimport { withAuth, withResponse } from \"@utils/auth-utils\";\r\n\r\ninterface LoaderData {\r\n    states: string[];\r\n    districts: { [state: string]: string[] };\r\n    googleMapsApiKey: string;\r\n}\r\n\r\nexport const loader: LoaderFunction = withAuth(async ({ request, user }) => {\r\n    try {\r\n\r\n        const areas = (await getDistrictsAndStates(user.userId, request)).data\r\n        const statesSet = new Set<string>()\r\n        const districtsMap: { [state: string]: Set<string> } = {}\r\n\r\n        areas.forEach((area: SellerArea) => {\r\n            statesSet.add(area.state)\r\n            if (!districtsMap[area.state]) {\r\n                districtsMap[area.state] = new Set<string>()\r\n            }\r\n            districtsMap[area.state].add(area.district)\r\n        })\r\n\r\n        const states = Array.from(statesSet).sort()\r\n        const districts = Object.fromEntries(\r\n            Object.entries(districtsMap).map(([state, districtsSet]) => [\r\n                state,\r\n                Array.from(districtsSet).sort()\r\n            ])\r\n        )\r\n\r\n        const googleMapsApiKey = process.env.GOOGLE_MAPS_API_KEY || ''\r\n\r\n        return withResponse({ states, districts, googleMapsApiKey })\r\n    } catch (error) {\r\n        console.error('Failed to fetch districts and states:', error)\r\n        return withResponse({ states: [], districts: {}, googleMapsApiKey: '' })\r\n    }\r\n}\r\n)\r\nconst googleMapsPromise = {\r\n    promise: null as Promise<void> | null,\r\n    resolve: null as ((value: void) => void) | null,\r\n    reject: null as ((reason?: any) => void) | null,\r\n};\r\n\r\n// Modified script loading function\r\nconst loadGoogleMapsScript = (apiKey: string) => {\r\n    if (googleMapsPromise.promise) {\r\n        return googleMapsPromise.promise;\r\n    }\r\n\r\n    googleMapsPromise.promise = new Promise((resolve, reject) => {\r\n        googleMapsPromise.resolve = resolve;\r\n        googleMapsPromise.reject = reject;\r\n\r\n        if (window.google?.maps?.drawing) {\r\n            resolve();\r\n            return;\r\n        }\r\n\r\n        // Create callback function\r\n        window.initMap = () => {\r\n            if (googleMapsPromise.resolve) {\r\n                googleMapsPromise.resolve();\r\n            }\r\n        };\r\n\r\n        const script = document.createElement('script');\r\n        script.src = `https://maps.googleapis.com/maps/api/js?key=${apiKey}&libraries=drawing,geometry&v=weekly&callback=initMap`;\r\n        script.async = true;\r\n        script.onerror = () => {\r\n            if (googleMapsPromise.reject) {\r\n                googleMapsPromise.reject(new Error('Failed to load Google Maps script'));\r\n            }\r\n        };\r\n        document.head.appendChild(script);\r\n    });\r\n\r\n    return googleMapsPromise.promise;\r\n};\r\n\r\ninterface MapProps {\r\n    center: google.maps.LatLngLiteral;\r\n    zoom: number;\r\n    onPolygonComplete: (coordinates: google.maps.LatLngLiteral[]) => void;\r\n    apiKey: string;\r\n    existingCoordinates?: google.maps.LatLngLiteral[];\r\n}\r\n\r\nfunction Map({ center, zoom, onPolygonComplete, apiKey, existingCoordinates }: MapProps) {\r\n    const ref = useRef<HTMLDivElement>(null);\r\n    const [isLibraryLoaded, setIsLibraryLoaded] = useState(false);\r\n    const mapRef = useRef<google.maps.Map | null>(null);\r\n    const polygonRef = useRef<google.maps.Polygon | null>(null);\r\n    const drawingManagerRef = useRef<google.maps.drawing.DrawingManager | null>(null);\r\n\r\n    // Load the Google Maps script\r\n    useEffect(() => {\r\n        const loadMap = async () => {\r\n            try {\r\n                await loadGoogleMapsScript(apiKey);\r\n                setIsLibraryLoaded(true);\r\n            } catch (error) {\r\n                console.error('Error loading Google Maps:', error);\r\n            }\r\n        };\r\n        loadMap();\r\n    }, [apiKey]);\r\n\r\n    // Function to create or update polygon\r\n    const updatePolygon = useCallback((coordinates: google.maps.LatLngLiteral[]) => {\r\n        if (!mapRef.current || !google?.maps) return;\r\n\r\n        // Remove existing polygon if it exists\r\n        if (polygonRef.current) {\r\n            polygonRef.current.setMap(null);\r\n        }\r\n\r\n        // Create new polygon\r\n        polygonRef.current = new google.maps.Polygon({\r\n            paths: coordinates,\r\n            fillColor: \"#4F46E5\",\r\n            fillOpacity: 0.3,\r\n            strokeWeight: 2,\r\n            strokeColor: \"#4F46E5\",\r\n            editable: true,\r\n            draggable: true,\r\n            map: mapRef.current\r\n        });\r\n\r\n        // Add listeners for editing\r\n        google.maps.event.addListener(polygonRef.current.getPath(), 'set_at', () => {\r\n            if (!polygonRef.current) return;\r\n            const newCoords = polygonRef.current.getPath().getArray().map(latLng => ({\r\n                lat: latLng.lat(),\r\n                lng: latLng.lng(),\r\n            }));\r\n            onPolygonComplete(newCoords);\r\n        });\r\n\r\n        google.maps.event.addListener(polygonRef.current.getPath(), 'insert_at', () => {\r\n            if (!polygonRef.current) return;\r\n            const newCoords = polygonRef.current.getPath().getArray().map(latLng => ({\r\n                lat: latLng.lat(),\r\n                lng: latLng.lng(),\r\n            }));\r\n            onPolygonComplete(newCoords);\r\n        });\r\n    }, [onPolygonComplete]);\r\n\r\n    // Initialize map and drawing manager after library is loaded\r\n    useEffect(() => {\r\n        if (!isLibraryLoaded || !ref.current || !google?.maps) return;\r\n\r\n        const map = new google.maps.Map(ref.current, {\r\n            center,\r\n            zoom,\r\n            streetViewControl: false,\r\n            mapTypeControl: false,\r\n        });\r\n\r\n        mapRef.current = map;\r\n\r\n        // Set Bangalore bounds\r\n        const bangaloreBounds = new google.maps.LatLngBounds(\r\n            new google.maps.LatLng(12.864162, 77.438610),\r\n            new google.maps.LatLng(13.139784, 77.711895)\r\n        );\r\n        map.fitBounds(bangaloreBounds);\r\n\r\n        // Initialize DrawingManager\r\n        const drawingManager = new google.maps.drawing.DrawingManager({\r\n            drawingMode: google.maps.drawing.OverlayType.POLYGON,\r\n            drawingControl: true,\r\n            drawingControlOptions: {\r\n                position: google.maps.ControlPosition.TOP_CENTER,\r\n                drawingModes: [google.maps.drawing.OverlayType.POLYGON],\r\n            },\r\n            polygonOptions: {\r\n                fillColor: \"#4F46E5\",\r\n                fillOpacity: 0.3,\r\n                strokeWeight: 2,\r\n                strokeColor: \"#4F46E5\",\r\n                editable: true,\r\n                draggable: true,\r\n            },\r\n        });\r\n\r\n        drawingManagerRef.current = drawingManager;\r\n        drawingManager.setMap(map);\r\n\r\n        // Add overlay complete listener\r\n        google.maps.event.addListener(drawingManager, 'overlaycomplete', (event) => {\r\n            if (event.type === google.maps.drawing.OverlayType.POLYGON) {\r\n                const polygon = event.overlay as google.maps.Polygon;\r\n\r\n                // Remove the temporary polygon\r\n                polygon.setMap(null);\r\n\r\n                drawingManager.setDrawingMode(null);\r\n\r\n                const coordinates = polygon.getPath().getArray().map(latLng => ({\r\n                    lat: latLng.lat(),\r\n                    lng: latLng.lng(),\r\n                }));\r\n\r\n                // Create the persistent polygon\r\n                updatePolygon(coordinates);\r\n                onPolygonComplete(coordinates);\r\n            }\r\n        });\r\n\r\n        // If there are existing coordinates, show them\r\n        if (existingCoordinates && existingCoordinates.length > 0) {\r\n            updatePolygon(existingCoordinates);\r\n        }\r\n\r\n        return () => {\r\n            if (drawingManagerRef.current) {\r\n                drawingManagerRef.current.setMap(null);\r\n            }\r\n            if (polygonRef.current) {\r\n                polygonRef.current.setMap(null);\r\n            }\r\n        };\r\n    }, [isLibraryLoaded, center, zoom, onPolygonComplete, existingCoordinates, updatePolygon]);\r\n\r\n    return <div ref={ref} className=\"w-full h-96 rounded-lg\" />;\r\n}\r\n\r\ninterface ActionData {\r\n    error?: string;\r\n    success?: boolean;\r\n}\r\n\r\nexport const action: ActionFunction = withAuth(async ({ request, user }) => {\r\n\r\n\r\n    try {\r\n        const formData = await request.formData();\r\n        const name = formData.get(\"name\") as string;\r\n        const state = formData.get(\"state\") as string;\r\n        const district = formData.get(\"district\") as string;\r\n        const polygon = formData.get(\"polygon\") as string;\r\n        const radius = Number(formData.get(\"radius\"));\r\n        const latitude = Number(formData.get(\"latitude\"))\r\n        const longitude = Number(formData.get(\"longitude\"))\r\n\r\n\r\n\r\n\r\n\r\n        if (!name || !state || !district || !polygon) {\r\n            return json({ error: \"All fields are required\" }, { status: 400 });\r\n        }\r\n\r\n        // First create the area\r\n        const createAreaResponse = await createArea(user.userId, {\r\n            name,\r\n            state,\r\n            district,\r\n            polygon,\r\n            radius: 0,\r\n            latitude: 0,\r\n            longitude: 0\r\n        }, request);\r\n\r\n        if (!createAreaResponse.data?.id) {\r\n            throw new Error('Failed to get area ID from creation response');\r\n        }\r\n\r\n        // Then activate the seller area\r\n        await activateSellerArea(user.userId, createAreaResponse.data.id, request);\r\n\r\n        return redirect('/home/<USER>');\r\n    } catch (error) {\r\n        console.error('Error creating locality:', error);\r\n        return json({\r\n            error: \"Failed to create locality. Please try again.\"\r\n        }, { status: 500 });\r\n    }\r\n});\r\n\r\nexport default function NewLocality() {\r\n    const navigate = useNavigate();\r\n    const submit = useSubmit();\r\n    const { states, districts, googleMapsApiKey } = useLoaderData<LoaderData>();\r\n    const actionData = useActionData<ActionData>();\r\n    const { toast } = useToast();\r\n\r\n    const [selectedState, setSelectedState] = useState<string>('');\r\n    const [selectedDistrict, setSelectedDistrict] = useState<string>('');\r\n    const [localityName, setLocalityName] = useState<string>('');\r\n    const [showMap, setShowMap] = useState(false);\r\n    const [polygonCoordinates, setPolygonCoordinates] = useState<google.maps.LatLngLiteral[]>([]);\r\n    const [isSubmitting, setIsSubmitting] = useState(false);\r\n\r\n    const handleStateChange = (value: string) => {\r\n        setSelectedState(value);\r\n        setSelectedDistrict('');\r\n        setShowMap(false);\r\n        setPolygonCoordinates([]);\r\n    };\r\n\r\n    const handleDistrictChange = (value: string) => {\r\n        setSelectedDistrict(value);\r\n        setShowMap(true);\r\n    };\r\n\r\n    const handlePolygonComplete = useCallback((coordinates: google.maps.LatLngLiteral[]) => {\r\n        setPolygonCoordinates(coordinates);\r\n    }, []);\r\n\r\n    const handleSave = () => {\r\n        if (!localityName.trim()) {\r\n            toast({\r\n                title: \"Error\",\r\n                description: \"Please enter a locality name\",\r\n                variant: \"destructive\",\r\n            });\r\n            return;\r\n        }\r\n\r\n        if (!selectedState || !selectedDistrict) {\r\n            toast({\r\n                title: \"Error\",\r\n                description: \"Please select both state and district\",\r\n                variant: \"destructive\",\r\n            });\r\n            return;\r\n        }\r\n\r\n        if (polygonCoordinates.length < 3) {\r\n            toast({\r\n                title: \"Error\",\r\n                description: \"Please draw a valid polygon on the map\",\r\n                variant: \"destructive\",\r\n            });\r\n            return;\r\n        }\r\n\r\n        // Make sure the polygon is closed\r\n        if (polygonCoordinates[0].lat !== polygonCoordinates[polygonCoordinates.length - 1].lat ||\r\n            polygonCoordinates[0].lng !== polygonCoordinates[polygonCoordinates.length - 1].lng) {\r\n            polygonCoordinates.push(polygonCoordinates[0]);\r\n        }\r\n\r\n        // Convert coordinates to path\r\n        const path = new google.maps.MVCArray(\r\n            polygonCoordinates.map(coord => new google.maps.LatLng(coord.lat, coord.lng))\r\n        );\r\n\r\n        // Encode the path using Google's utility\r\n        const encodedPolyline = encodeURIComponent(google.maps.geometry.encoding.encodePath(path));\r\n\r\n        const formData = new FormData();\r\n        formData.set(\"name\", localityName);\r\n        formData.set(\"state\", selectedState);\r\n        formData.set(\"district\", selectedDistrict);\r\n        formData.set(\"polygon\", encodedPolyline); // Send the encoded polyline directly\r\n\r\n        setIsSubmitting(true);\r\n        submit(formData, { method: \"post\" });\r\n    };\r\n\r\n\r\n    // Show error toast if action returns an error\r\n    useEffect(() => {\r\n        if (actionData?.error) {\r\n            toast({\r\n                title: \"Error\",\r\n                description: actionData.error,\r\n                variant: \"destructive\",\r\n            });\r\n        }\r\n    }, [actionData, toast]);\r\n\r\n    return (\r\n        <div className=\"h-[calc(100vh-64px)] bg-white p-6 overflow-y-auto\">\r\n            <div className=\"flex justify-between items-center mb-6\">\r\n                <button\r\n                    onClick={() => navigate('/home/<USER>')}\r\n                    className=\"p-2 hover:bg-gray-100 rounded-full\"\r\n                >\r\n                    <ArrowLeft size={24} />\r\n                </button>\r\n            </div>\r\n\r\n            <Card className=\"w-full max-w-2xl mx-auto\">\r\n                <CardHeader>\r\n                    <CardTitle>Add New Locality</CardTitle>\r\n                    <CardDescription>\r\n                        Enter locality details and draw the boundary on the map\r\n                    </CardDescription>\r\n                </CardHeader>\r\n                <CardContent>\r\n                    <div className=\"space-y-4\">\r\n                        <div className=\"space-y-2\">\r\n                            <label className=\"text-sm font-medium\">Locality Name</label>\r\n                            <Input\r\n                                placeholder=\"Enter locality name\"\r\n                                value={localityName}\r\n                                onChange={(e) => setLocalityName(e.target.value)}\r\n                            />\r\n                        </div>\r\n\r\n\r\n                        <div className=\"space-y-2\">\r\n                            <label className=\"text-sm font-medium\">State</label>\r\n                            <Select value={selectedState} onValueChange={handleStateChange}>\r\n                                <SelectTrigger>\r\n                                    <SelectValue placeholder=\"Select a state\" />\r\n                                </SelectTrigger>\r\n                                <SelectContent>\r\n                                    {states.map((state) => (\r\n                                        <SelectItem key={state} value={state}>{state}</SelectItem>\r\n                                    ))}\r\n                                </SelectContent>\r\n                            </Select>\r\n                        </div>\r\n\r\n                        <div className=\"space-y-2\">\r\n                            <label className=\"text-sm font-medium\">District</label>\r\n                            <Select\r\n                                value={selectedDistrict}\r\n                                onValueChange={handleDistrictChange}\r\n                                disabled={!selectedState}\r\n                            >\r\n                                <SelectTrigger>\r\n                                    <SelectValue placeholder=\"Select a district\" />\r\n                                </SelectTrigger>\r\n                                <SelectContent>\r\n                                    {selectedState && districts[selectedState]?.map((district) => (\r\n                                        <SelectItem key={district} value={district}>{district}</SelectItem>\r\n                                    ))}\r\n                                </SelectContent>\r\n                            </Select>\r\n                        </div>\r\n\r\n                        {showMap && (\r\n                            <div className=\"space-y-4\">\r\n                                <Map\r\n                                    center={{ lat: 12.9716, lng: 77.5946 }}\r\n                                    zoom={11}\r\n                                    onPolygonComplete={handlePolygonComplete}\r\n                                    apiKey={googleMapsApiKey}\r\n                                    existingCoordinates={polygonCoordinates}\r\n                                />\r\n                                <Button\r\n                                    onClick={handleSave}\r\n                                    disabled={isSubmitting || polygonCoordinates.length < 3 || !localityName.trim()}\r\n                                    className=\"w-full\"\r\n                                >\r\n                                    {isSubmitting ? \"Creating...\" : \"Save Locality\"}\r\n                                </Button>\r\n                            </div>\r\n                        )}\r\n                    </div>\r\n                </CardContent>\r\n            </Card>\r\n        </div>\r\n    );\r\n}\r\n\r\n\r\n"], "names": ["googleMapsPromise", "promise", "resolve", "reject", "loadGoogleMapsScript", "<PERSON><PERSON><PERSON><PERSON>", "Promise", "window", "google", "maps", "drawing", "initMap", "script", "document", "createElement", "src", "async", "onerror", "Error", "head", "append<PERSON><PERSON><PERSON>", "Map", "center", "zoom", "onPolygonComplete", "existingCoordinates", "ref", "useRef", "isLibraryLoaded", "setIsLibraryLoaded", "useState", "mapRef", "polygonRef", "drawingManagerRef", "useEffect", "loadMap", "error", "console", "updatePolygon", "useCallback", "coordinates", "current", "setMap", "Polygon", "paths", "fillColor", "fillOpacity", "strokeWeight", "strokeColor", "editable", "draggable", "map", "event", "addListener", "<PERSON><PERSON><PERSON>", "newCoords", "getArray", "latLng", "lat", "lng", "streetViewControl", "mapTypeControl", "bangaloreBounds", "LatLngBounds", "LatLng", "fitBounds", "drawingManager", "DrawingManager", "drawingMode", "OverlayType", "POLYGON", "drawingControl", "drawingControlOptions", "position", "ControlPosition", "TOP_CENTER", "drawingModes", "polygonOptions", "type", "polygon", "overlay", "setDrawingMode", "length", "jsx", "className", "NewLocality", "navigate", "useNavigate", "submit", "useSubmit", "states", "districts", "googleMapsApiKey", "useLoaderData", "actionData", "useActionData", "toast", "useToast", "selectedState", "setSelectedState", "selectedDistrict", "setSelectedDistrict", "localityName", "setLocalityName", "showMap", "setShowMap", "polygonCoordinates", "setPolygonCoordinates", "isSubmitting", "setIsSubmitting", "handleStateChange", "value", "handleDistrictChange", "handlePolygonComplete", "handleSave", "trim", "title", "description", "variant", "push", "path", "MVCArray", "coord", "encodedPolyline", "encodeURIComponent", "geometry", "encoding", "encodePath", "formData", "FormData", "set", "method", "jsxs", "children", "onClick", "ArrowLeft", "size", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardTitle", "CardDescription", "<PERSON><PERSON><PERSON><PERSON>", "Input", "placeholder", "onChange", "e", "target", "Select", "onValueChange", "SelectTrigger", "SelectValue", "SelectContent", "state", "SelectItem", "disabled", "district", "<PERSON><PERSON>"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqDA,MAAMA,oBAAoB;AAAA,EACtBC,SAAS;AAAA,EACTC,SAAS;AAAA,EACTC,QAAQ;AACZ;AAGA,MAAMC,uBAAwBC,YAAmB;AAC7C,MAAIL,kBAAkBC,SAAS;AAC3B,WAAOD,kBAAkBC;AAAAA,EAC7B;AAEAD,oBAAkBC,UAAU,IAAIK,QAAQ,CAACJ,SAASC,WAAW;;AACzDH,sBAAkBE,UAAUA;AAC5BF,sBAAkBG,SAASA;AAEvB,SAAAI,kBAAOC,WAAPD,mBAAeE,SAAfF,mBAAqBG,SAAS;AACtBR,cAAA;AACR;AAAA,IACJ;AAGAK,WAAOI,UAAU,MAAM;AACnB,UAAIX,kBAAkBE,SAAS;AAC3BF,0BAAkBE,QAAQ;AAAA,MAC9B;AAAA,IACJ;AAEM,UAAAU,SAASC,SAASC,cAAc,QAAQ;AACvCF,WAAAG,MAAM,+CAA+CV,MAAM;AAClEO,WAAOI,QAAQ;AACfJ,WAAOK,UAAU,MAAM;AACnB,UAAIjB,kBAAkBG,QAAQ;AAC1BH,0BAAkBG,OAAO,IAAIe,MAAM,mCAAmC,CAAC;AAAA,MAC3E;AAAA,IACJ;AACSL,aAAAM,KAAKC,YAAYR,MAAM;AAAA,EACpC,CAAC;AAED,SAAOZ,kBAAkBC;AAC7B;AAUA,SAASoB,IAAI;AAAA,EAAEC;AAAAA,EAAQC;AAAAA,EAAMC;AAAAA,EAAmBnB;AAAAA,EAAQoB;AAAoB,GAAa;AAC/E,QAAAC,MAAMC,oBAAuB,IAAI;AACvC,QAAM,CAACC,iBAAiBC,kBAAkB,IAAIC,aAAAA,SAAS,KAAK;AACtD,QAAAC,SAASJ,oBAA+B,IAAI;AAC5C,QAAAK,aAAaL,oBAAmC,IAAI;AACpD,QAAAM,oBAAoBN,oBAAkD,IAAI;AAGhFO,eAAAA,UAAU,MAAM;AACZ,UAAMC,UAAU,YAAY;AACpB,UAAA;AACA,cAAM/B,qBAAqBC,MAAM;AACjCwB,2BAAmB,IAAI;AAAA,eAClBO,OAAO;AACJC,gBAAAD,MAAM,8BAA8BA,KAAK;AAAA,MACrD;AAAA,IACJ;AACQD,YAAA;AAAA,EACZ,GAAG,CAAC9B,MAAM,CAAC;AAGL,QAAAiC,gBAAgBC,aAAY,YAACC,iBAA6C;AAC5E,QAAI,CAACT,OAAOU,WAAW,EAACjC,iCAAQC,MAAM;AAGtC,QAAIuB,WAAWS,SAAS;AACTT,iBAAAS,QAAQC,OAAO,IAAI;AAAA,IAClC;AAGAV,eAAWS,UAAU,IAAIjC,OAAOC,KAAKkC,QAAQ;AAAA,MACzCC,OAAOJ;AAAAA,MACPK,WAAW;AAAA,MACXC,aAAa;AAAA,MACbC,cAAc;AAAA,MACdC,aAAa;AAAA,MACbC,UAAU;AAAA,MACVC,WAAW;AAAA,MACXC,KAAKpB,OAAOU;AAAAA,IAChB,CAAC;AAGMjC,WAAAC,KAAK2C,MAAMC,YAAYrB,WAAWS,QAAQa,WAAW,UAAU,MAAM;AACpE,UAAA,CAACtB,WAAWS,QAAS;AACnB,YAAAc,YAAYvB,WAAWS,QAAQa,UAAUE,SAAS,EAAEL,IAAeM,aAAA;AAAA,QACrEC,KAAKD,OAAOC,IAAI;AAAA,QAChBC,KAAKF,OAAOE,IAAI;AAAA,MACpB,EAAE;AACFnC,wBAAkB+B,SAAS;AAAA,IAC/B,CAAC;AAEM/C,WAAAC,KAAK2C,MAAMC,YAAYrB,WAAWS,QAAQa,WAAW,aAAa,MAAM;AACvE,UAAA,CAACtB,WAAWS,QAAS;AACnB,YAAAc,YAAYvB,WAAWS,QAAQa,UAAUE,SAAS,EAAEL,IAAeM,aAAA;AAAA,QACrEC,KAAKD,OAAOC,IAAI;AAAA,QAChBC,KAAKF,OAAOE,IAAI;AAAA,MACpB,EAAE;AACFnC,wBAAkB+B,SAAS;AAAA,IAC/B,CAAC;AAAA,EACL,GAAG,CAAC/B,iBAAiB,CAAC;AAGtBU,eAAAA,UAAU,MAAM;AACZ,QAAI,CAACN,mBAAmB,CAACF,IAAIe,WAAW,EAACjC,iCAAQC,MAAM;AAEvD,UAAM0C,MAAM,IAAI3C,OAAOC,KAAKY,IAAIK,IAAIe,SAAS;AAAA,MACzCnB;AAAAA,MACAC;AAAAA,MACAqC,mBAAmB;AAAA,MACnBC,gBAAgB;AAAA,IACpB,CAAC;AAED9B,WAAOU,UAAUU;AAGX,UAAAW,kBAAkB,IAAItD,OAAOC,KAAKsD,aACpC,IAAIvD,OAAOC,KAAKuD,OAAO,WAAW,QAAS,GAC3C,IAAIxD,OAAOC,KAAKuD,OAAO,WAAW,SAAS,CAC/C;AACAb,QAAIc,UAAUH,eAAe;AAG7B,UAAMI,iBAAiB,IAAI1D,OAAOC,KAAKC,QAAQyD,eAAe;AAAA,MAC1DC,aAAa5D,OAAOC,KAAKC,QAAQ2D,YAAYC;AAAAA,MAC7CC,gBAAgB;AAAA,MAChBC,uBAAuB;AAAA,QACnBC,UAAUjE,OAAOC,KAAKiE,gBAAgBC;AAAAA,QACtCC,cAAc,CAACpE,OAAOC,KAAKC,QAAQ2D,YAAYC,OAAO;AAAA,MAC1D;AAAA,MACAO,gBAAgB;AAAA,QACZhC,WAAW;AAAA,QACXC,aAAa;AAAA,QACbC,cAAc;AAAA,QACdC,aAAa;AAAA,QACbC,UAAU;AAAA,QACVC,WAAW;AAAA,MACf;AAAA,IACJ,CAAC;AAEDjB,sBAAkBQ,UAAUyB;AAC5BA,mBAAexB,OAAOS,GAAG;AAGzB3C,WAAOC,KAAK2C,MAAMC,YAAYa,gBAAgB,mBAAoBd,WAAU;AACxE,UAAIA,MAAM0B,SAAStE,OAAOC,KAAKC,QAAQ2D,YAAYC,SAAS;AACxD,cAAMS,UAAU3B,MAAM4B;AAGtBD,gBAAQrC,OAAO,IAAI;AAEnBwB,uBAAee,eAAe,IAAI;AAElC,cAAMzC,cAAcuC,QAAQzB,QAAA,EAAUE,SAAS,EAAEL,IAAeM,aAAA;AAAA,UAC5DC,KAAKD,OAAOC,IAAI;AAAA,UAChBC,KAAKF,OAAOE,IAAI;AAAA,QACpB,EAAE;AAGFrB,sBAAcE,WAAW;AACzBhB,0BAAkBgB,WAAW;AAAA,MACjC;AAAA,IACJ,CAAC;AAGG,QAAAf,uBAAuBA,oBAAoByD,SAAS,GAAG;AACvD5C,oBAAcb,mBAAmB;AAAA,IACrC;AAEA,WAAO,MAAM;AACT,UAAIQ,kBAAkBQ,SAAS;AACTR,0BAAAQ,QAAQC,OAAO,IAAI;AAAA,MACzC;AACA,UAAIV,WAAWS,SAAS;AACTT,mBAAAS,QAAQC,OAAO,IAAI;AAAA,MAClC;AAAA,IACJ;AAAA,EACJ,GAAG,CAACd,iBAAiBN,QAAQC,MAAMC,mBAAmBC,qBAAqBa,aAAa,CAAC;AAEzF,SAAQ6C,kCAAAA,IAAA,OAAA;AAAA,IAAIzD;AAAAA,IAAU0D,WAAU;AAAA,EAAyB,CAAA;AAC7D;AAuDA,SAAwBC,cAAc;;AAClC,QAAMC,WAAWC,YAAY;AAC7B,QAAMC,SAASC,UAAU;AACzB,QAAM;AAAA,IAAEC;AAAAA,IAAQC;AAAAA,IAAWC;AAAAA,MAAqBC,cAA0B;AAC1E,QAAMC,aAAaC,cAA0B;AACvC,QAAA;AAAA,IAAEC;AAAAA,EAAM,IAAIC,SAAS;AAE3B,QAAM,CAACC,eAAeC,gBAAgB,IAAIrE,aAAAA,SAAiB,EAAE;AAC7D,QAAM,CAACsE,kBAAkBC,mBAAmB,IAAIvE,aAAAA,SAAiB,EAAE;AACnE,QAAM,CAACwE,cAAcC,eAAe,IAAIzE,aAAAA,SAAiB,EAAE;AAC3D,QAAM,CAAC0E,SAASC,UAAU,IAAI3E,aAAAA,SAAS,KAAK;AAC5C,QAAM,CAAC4E,oBAAoBC,qBAAqB,IAAI7E,aAAAA,SAAsC,CAAA,CAAE;AAC5F,QAAM,CAAC8E,cAAcC,eAAe,IAAI/E,aAAAA,SAAS,KAAK;AAEhD,QAAAgF,oBAAqBC,WAAkB;AACzCZ,qBAAiBY,KAAK;AACtBV,wBAAoB,EAAE;AACtBI,eAAW,KAAK;AAChBE,0BAAsB,CAAA,CAAE;AAAA,EAC5B;AAEM,QAAAK,uBAAwBD,WAAkB;AAC5CV,wBAAoBU,KAAK;AACzBN,eAAW,IAAI;AAAA,EACnB;AAEM,QAAAQ,wBAAwB1E,aAAY,YAACC,iBAA6C;AACpFmE,0BAAsBnE,WAAW;AAAA,EACrC,GAAG,EAAE;AAEL,QAAM0E,aAAaA,MAAM;AACjB,QAAA,CAACZ,aAAaa,QAAQ;AAChBnB,YAAA;AAAA,QACFoB,OAAO;AAAA,QACPC,aAAa;AAAA,QACbC,SAAS;AAAA,MACb,CAAC;AACD;AAAA,IACJ;AAEI,QAAA,CAACpB,iBAAiB,CAACE,kBAAkB;AAC/BJ,YAAA;AAAA,QACFoB,OAAO;AAAA,QACPC,aAAa;AAAA,QACbC,SAAS;AAAA,MACb,CAAC;AACD;AAAA,IACJ;AAEI,QAAAZ,mBAAmBxB,SAAS,GAAG;AACzBc,YAAA;AAAA,QACFoB,OAAO;AAAA,QACPC,aAAa;AAAA,QACbC,SAAS;AAAA,MACb,CAAC;AACD;AAAA,IACJ;AAGA,QAAIZ,mBAAmB,CAAC,EAAEhD,QAAQgD,mBAAmBA,mBAAmBxB,SAAS,CAAC,EAAExB,OAChFgD,mBAAmB,CAAC,EAAE/C,QAAQ+C,mBAAmBA,mBAAmBxB,SAAS,CAAC,EAAEvB,KAAK;AAClE+C,yBAAAa,KAAKb,mBAAmB,CAAC,CAAC;AAAA,IACjD;AAGM,UAAAc,OAAO,IAAIhH,OAAOC,KAAKgH,SACzBf,mBAAmBvD,IAAIuE,WAAS,IAAIlH,OAAOC,KAAKuD,OAAO0D,MAAMhE,KAAKgE,MAAM/D,GAAG,CAAC,CAChF;AAGM,UAAAgE,kBAAkBC,mBAAmBpH,OAAOC,KAAKoH,SAASC,SAASC,WAAWP,IAAI,CAAC;AAEnF,UAAAQ,WAAW,IAAIC,SAAS;AACrBD,aAAAE,IAAI,QAAQ5B,YAAY;AACxB0B,aAAAE,IAAI,SAAShC,aAAa;AAC1B8B,aAAAE,IAAI,YAAY9B,gBAAgB;AAChC4B,aAAAE,IAAI,WAAWP,eAAe;AAEvCd,oBAAgB,IAAI;AACpBrB,WAAOwC,UAAU;AAAA,MAAEG,QAAQ;AAAA,IAAO,CAAC;AAAA,EACvC;AAIAjG,eAAAA,UAAU,MAAM;AACZ,QAAI4D,yCAAY1D,OAAO;AACb4D,YAAA;AAAA,QACFoB,OAAO;AAAA,QACPC,aAAavB,WAAW1D;AAAAA,QACxBkF,SAAS;AAAA,MACb,CAAC;AAAA,IACL;AAAA,EACJ,GAAG,CAACxB,YAAYE,KAAK,CAAC;AAGlB,SAAAoC,kCAAAA,KAAC,OAAI;AAAA,IAAAhD,WAAU;AAAA,IACXiD,UAAA,CAAClD,kCAAA,IAAA,OAAA;AAAA,MAAIC,WAAU;AAAA,MACXiD,UAAAlD,kCAAA,IAAC,UAAA;AAAA,QACGmD,SAASA,MAAMhD,SAAS,kBAAkB;AAAA,QAC1CF,WAAU;AAAA,QAEViD,UAAAlD,kCAAA,IAACoD,WAAU;AAAA,UAAAC,MAAM;AAAA,QAAI,CAAA;AAAA,MACzB,CAAA;AAAA,IACJ,CAAA,GAEAJ,kCAAA,KAACK,MAAK;AAAA,MAAArD,WAAU;AAAA,MACZiD,UAAA,CAAAD,kCAAA,KAACM,YACG;AAAA,QAAAL,UAAA,CAAAlD,kCAAA,IAACwD;UAAUN,UAAgB;AAAA,QAAA,CAAA,GAC3BlD,kCAAA,IAACyD;UAAgBP,UAEjB;AAAA,QAAA,CAAA,CAAA;AAAA,MACJ,CAAA,GACClD,kCAAA,IAAA0D,aAAA;AAAA,QACGR,UAACD,kCAAA,KAAA,OAAA;AAAA,UAAIhD,WAAU;AAAA,UACXiD,UAAA,CAACD,kCAAA,KAAA,OAAA;AAAA,YAAIhD,WAAU;AAAA,YACXiD,UAAA,CAAClD,kCAAA,IAAA,SAAA;AAAA,cAAMC,WAAU;AAAA,cAAsBiD,UAAa;AAAA,YAAA,CAAA,GACpDlD,kCAAA,IAAC2D,OAAA;AAAA,cACGC,aAAY;AAAA,cACZhC,OAAOT;AAAAA,cACP0C,UAAWC,OAAM1C,gBAAgB0C,EAAEC,OAAOnC,KAAK;AAAA,YAAA,CACnD,CAAA;AAAA,UACJ,CAAA,GAGAqB,kCAAA,KAAC,OAAI;AAAA,YAAAhD,WAAU;AAAA,YACXiD,UAAA,CAAClD,kCAAA,IAAA,SAAA;AAAA,cAAMC,WAAU;AAAA,cAAsBiD,UAAK;AAAA,YAAA,CAAA,GAC3CD,kCAAA,KAAAe,QAAA;AAAA,cAAOpC,OAAOb;AAAAA,cAAekD,eAAetC;AAAAA,cACzCuB,UAAA,CAAAlD,kCAAA,IAACkE,eACG;AAAA,gBAAAhB,UAAAlD,kCAAA,IAACmE,aAAY;AAAA,kBAAAP,aAAY;AAAA,gBAAiB,CAAA;AAAA,cAC9C,CAAA,GACC5D,kCAAA,IAAAoE,eAAA;AAAA,gBACIlB,UAAO3C,OAAAvC,IAAKqG,WACRrE,kCAAAA,IAAAsE,YAAA;AAAA,kBAAuB1C,OAAOyC;AAAAA,kBAAQnB,UAAtBmB;AAAAA,gBAAA,GAAAA,KAA4B,CAChD;AAAA,cACL,CAAA,CAAA;AAAA,YACJ,CAAA,CAAA;AAAA,UACJ,CAAA,GAEApB,kCAAA,KAAC,OAAI;AAAA,YAAAhD,WAAU;AAAA,YACXiD,UAAA,CAAClD,kCAAA,IAAA,SAAA;AAAA,cAAMC,WAAU;AAAA,cAAsBiD,UAAQ;AAAA,YAAA,CAAA,GAC/CD,kCAAA,KAACe,QAAA;AAAA,cACGpC,OAAOX;AAAAA,cACPgD,eAAepC;AAAAA,cACf0C,UAAU,CAACxD;AAAAA,cAEXmC,UAAA,CAAAlD,kCAAA,IAACkE,eACG;AAAA,gBAAAhB,UAAAlD,kCAAA,IAACmE,aAAY;AAAA,kBAAAP,aAAY;AAAA,gBAAoB,CAAA;AAAA,cACjD,CAAA,yCACCQ,eACI;AAAA,gBAAAlB,UAAAnC,mBAAiBP,eAAUO,aAAa,MAAvBP,mBAA0BxC,IAAKwG,cAC7CxE,kCAAA,IAACsE;kBAA0B1C,OAAO4C;AAAAA,kBAAWtB,UAA5BsB;AAAAA,gBAAA,GAAAA,QAAqC;AAAA,cAE9D,CAAA,CAAA;AAAA,YAAA,CACJ,CAAA;AAAA,WACJ,GAECnD,WACG4B,kCAAA,KAAC,OAAI;AAAA,YAAAhD,WAAU;AAAA,YACXiD,UAAA,CAAAlD,kCAAA,IAAC9D,KAAA;AAAA,cACGC,QAAQ;AAAA,gBAAEoC,KAAK;AAAA,gBAASC,KAAK;AAAA,cAAQ;AAAA,cACrCpC,MAAM;AAAA,cACNC,mBAAmByF;AAAAA,cACnB5G,QAAQuF;AAAAA,cACRnE,qBAAqBiF;AAAAA,YAAA,CACzB,GACAvB,kCAAA,IAACyE,QAAA;AAAA,cACGtB,SAASpB;AAAAA,cACTwC,UAAU9C,gBAAgBF,mBAAmBxB,SAAS,KAAK,CAACoB,aAAaa,KAAK;AAAA,cAC9E/B,WAAU;AAAA,cAETiD,yBAAe,gBAAgB;AAAA,YAAA,CACpC,CAAA;AAAA,UACJ,CAAA,CAAA;AAAA,QAER,CAAA;AAAA,MACJ,CAAA,CAAA;AAAA,IACJ,CAAA,CAAA;AAAA,EACJ,CAAA;AAER;"}