{"version": 3, "file": "index-DhHTcibu.js", "sources": ["../../../node_modules/@remix-run/router/dist/router.js", "../../../node_modules/react-router/dist/index.js"], "sourcesContent": ["/**\n * @remix-run/router v1.22.0\n *\n * Copyright (c) Remix Software Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE.md file in the root directory of this source tree.\n *\n * @license MIT\n */\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\n\n////////////////////////////////////////////////////////////////////////////////\n//#region Types and Constants\n////////////////////////////////////////////////////////////////////////////////\n/**\n * Actions represent the type of change to a location value.\n */\nvar Action;\n(function (Action) {\n  /**\n   * A POP indicates a change to an arbitrary index in the history stack, such\n   * as a back or forward navigation. It does not describe the direction of the\n   * navigation, only that the current index changed.\n   *\n   * Note: This is the default action for newly created history objects.\n   */\n  Action[\"Pop\"] = \"POP\";\n  /**\n   * A PUSH indicates a new entry being added to the history stack, such as when\n   * a link is clicked and a new page loads. When this happens, all subsequent\n   * entries in the stack are lost.\n   */\n  Action[\"Push\"] = \"PUSH\";\n  /**\n   * A REPLACE indicates the entry at the current index in the history stack\n   * being replaced by a new one.\n   */\n  Action[\"Replace\"] = \"REPLACE\";\n})(Action || (Action = {}));\nconst PopStateEventType = \"popstate\";\n/**\n * Memory history stores the current location in memory. It is designed for use\n * in stateful non-browser environments like tests and React Native.\n */\nfunction createMemoryHistory(options) {\n  if (options === void 0) {\n    options = {};\n  }\n  let {\n    initialEntries = [\"/\"],\n    initialIndex,\n    v5Compat = false\n  } = options;\n  let entries; // Declare so we can access from createMemoryLocation\n  entries = initialEntries.map((entry, index) => createMemoryLocation(entry, typeof entry === \"string\" ? null : entry.state, index === 0 ? \"default\" : undefined));\n  let index = clampIndex(initialIndex == null ? entries.length - 1 : initialIndex);\n  let action = Action.Pop;\n  let listener = null;\n  function clampIndex(n) {\n    return Math.min(Math.max(n, 0), entries.length - 1);\n  }\n  function getCurrentLocation() {\n    return entries[index];\n  }\n  function createMemoryLocation(to, state, key) {\n    if (state === void 0) {\n      state = null;\n    }\n    let location = createLocation(entries ? getCurrentLocation().pathname : \"/\", to, state, key);\n    warning(location.pathname.charAt(0) === \"/\", \"relative pathnames are not supported in memory history: \" + JSON.stringify(to));\n    return location;\n  }\n  function createHref(to) {\n    return typeof to === \"string\" ? to : createPath(to);\n  }\n  let history = {\n    get index() {\n      return index;\n    },\n    get action() {\n      return action;\n    },\n    get location() {\n      return getCurrentLocation();\n    },\n    createHref,\n    createURL(to) {\n      return new URL(createHref(to), \"http://localhost\");\n    },\n    encodeLocation(to) {\n      let path = typeof to === \"string\" ? parsePath(to) : to;\n      return {\n        pathname: path.pathname || \"\",\n        search: path.search || \"\",\n        hash: path.hash || \"\"\n      };\n    },\n    push(to, state) {\n      action = Action.Push;\n      let nextLocation = createMemoryLocation(to, state);\n      index += 1;\n      entries.splice(index, entries.length, nextLocation);\n      if (v5Compat && listener) {\n        listener({\n          action,\n          location: nextLocation,\n          delta: 1\n        });\n      }\n    },\n    replace(to, state) {\n      action = Action.Replace;\n      let nextLocation = createMemoryLocation(to, state);\n      entries[index] = nextLocation;\n      if (v5Compat && listener) {\n        listener({\n          action,\n          location: nextLocation,\n          delta: 0\n        });\n      }\n    },\n    go(delta) {\n      action = Action.Pop;\n      let nextIndex = clampIndex(index + delta);\n      let nextLocation = entries[nextIndex];\n      index = nextIndex;\n      if (listener) {\n        listener({\n          action,\n          location: nextLocation,\n          delta\n        });\n      }\n    },\n    listen(fn) {\n      listener = fn;\n      return () => {\n        listener = null;\n      };\n    }\n  };\n  return history;\n}\n/**\n * Browser history stores the location in regular URLs. This is the standard for\n * most web apps, but it requires some configuration on the server to ensure you\n * serve the same app at multiple URLs.\n *\n * @see https://github.com/remix-run/history/tree/main/docs/api-reference.md#createbrowserhistory\n */\nfunction createBrowserHistory(options) {\n  if (options === void 0) {\n    options = {};\n  }\n  function createBrowserLocation(window, globalHistory) {\n    let {\n      pathname,\n      search,\n      hash\n    } = window.location;\n    return createLocation(\"\", {\n      pathname,\n      search,\n      hash\n    },\n    // state defaults to `null` because `window.history.state` does\n    globalHistory.state && globalHistory.state.usr || null, globalHistory.state && globalHistory.state.key || \"default\");\n  }\n  function createBrowserHref(window, to) {\n    return typeof to === \"string\" ? to : createPath(to);\n  }\n  return getUrlBasedHistory(createBrowserLocation, createBrowserHref, null, options);\n}\n/**\n * Hash history stores the location in window.location.hash. This makes it ideal\n * for situations where you don't want to send the location to the server for\n * some reason, either because you do cannot configure it or the URL space is\n * reserved for something else.\n *\n * @see https://github.com/remix-run/history/tree/main/docs/api-reference.md#createhashhistory\n */\nfunction createHashHistory(options) {\n  if (options === void 0) {\n    options = {};\n  }\n  function createHashLocation(window, globalHistory) {\n    let {\n      pathname = \"/\",\n      search = \"\",\n      hash = \"\"\n    } = parsePath(window.location.hash.substr(1));\n    // Hash URL should always have a leading / just like window.location.pathname\n    // does, so if an app ends up at a route like /#something then we add a\n    // leading slash so all of our path-matching behaves the same as if it would\n    // in a browser router.  This is particularly important when there exists a\n    // root splat route (<Route path=\"*\">) since that matches internally against\n    // \"/*\" and we'd expect /#something to 404 in a hash router app.\n    if (!pathname.startsWith(\"/\") && !pathname.startsWith(\".\")) {\n      pathname = \"/\" + pathname;\n    }\n    return createLocation(\"\", {\n      pathname,\n      search,\n      hash\n    },\n    // state defaults to `null` because `window.history.state` does\n    globalHistory.state && globalHistory.state.usr || null, globalHistory.state && globalHistory.state.key || \"default\");\n  }\n  function createHashHref(window, to) {\n    let base = window.document.querySelector(\"base\");\n    let href = \"\";\n    if (base && base.getAttribute(\"href\")) {\n      let url = window.location.href;\n      let hashIndex = url.indexOf(\"#\");\n      href = hashIndex === -1 ? url : url.slice(0, hashIndex);\n    }\n    return href + \"#\" + (typeof to === \"string\" ? to : createPath(to));\n  }\n  function validateHashLocation(location, to) {\n    warning(location.pathname.charAt(0) === \"/\", \"relative pathnames are not supported in hash history.push(\" + JSON.stringify(to) + \")\");\n  }\n  return getUrlBasedHistory(createHashLocation, createHashHref, validateHashLocation, options);\n}\nfunction invariant(value, message) {\n  if (value === false || value === null || typeof value === \"undefined\") {\n    throw new Error(message);\n  }\n}\nfunction warning(cond, message) {\n  if (!cond) {\n    // eslint-disable-next-line no-console\n    if (typeof console !== \"undefined\") console.warn(message);\n    try {\n      // Welcome to debugging history!\n      //\n      // This error is thrown as a convenience, so you can more easily\n      // find the source for a warning that appears in the console by\n      // enabling \"pause on exceptions\" in your JavaScript debugger.\n      throw new Error(message);\n      // eslint-disable-next-line no-empty\n    } catch (e) {}\n  }\n}\nfunction createKey() {\n  return Math.random().toString(36).substr(2, 8);\n}\n/**\n * For browser-based histories, we combine the state and key into an object\n */\nfunction getHistoryState(location, index) {\n  return {\n    usr: location.state,\n    key: location.key,\n    idx: index\n  };\n}\n/**\n * Creates a Location object with a unique key from the given Path\n */\nfunction createLocation(current, to, state, key) {\n  if (state === void 0) {\n    state = null;\n  }\n  let location = _extends({\n    pathname: typeof current === \"string\" ? current : current.pathname,\n    search: \"\",\n    hash: \"\"\n  }, typeof to === \"string\" ? parsePath(to) : to, {\n    state,\n    // TODO: This could be cleaned up.  push/replace should probably just take\n    // full Locations now and avoid the need to run through this flow at all\n    // But that's a pretty big refactor to the current test suite so going to\n    // keep as is for the time being and just let any incoming keys take precedence\n    key: to && to.key || key || createKey()\n  });\n  return location;\n}\n/**\n * Creates a string URL path from the given pathname, search, and hash components.\n */\nfunction createPath(_ref) {\n  let {\n    pathname = \"/\",\n    search = \"\",\n    hash = \"\"\n  } = _ref;\n  if (search && search !== \"?\") pathname += search.charAt(0) === \"?\" ? search : \"?\" + search;\n  if (hash && hash !== \"#\") pathname += hash.charAt(0) === \"#\" ? hash : \"#\" + hash;\n  return pathname;\n}\n/**\n * Parses a string URL path into its separate pathname, search, and hash components.\n */\nfunction parsePath(path) {\n  let parsedPath = {};\n  if (path) {\n    let hashIndex = path.indexOf(\"#\");\n    if (hashIndex >= 0) {\n      parsedPath.hash = path.substr(hashIndex);\n      path = path.substr(0, hashIndex);\n    }\n    let searchIndex = path.indexOf(\"?\");\n    if (searchIndex >= 0) {\n      parsedPath.search = path.substr(searchIndex);\n      path = path.substr(0, searchIndex);\n    }\n    if (path) {\n      parsedPath.pathname = path;\n    }\n  }\n  return parsedPath;\n}\nfunction getUrlBasedHistory(getLocation, createHref, validateLocation, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  let {\n    window = document.defaultView,\n    v5Compat = false\n  } = options;\n  let globalHistory = window.history;\n  let action = Action.Pop;\n  let listener = null;\n  let index = getIndex();\n  // Index should only be null when we initialize. If not, it's because the\n  // user called history.pushState or history.replaceState directly, in which\n  // case we should log a warning as it will result in bugs.\n  if (index == null) {\n    index = 0;\n    globalHistory.replaceState(_extends({}, globalHistory.state, {\n      idx: index\n    }), \"\");\n  }\n  function getIndex() {\n    let state = globalHistory.state || {\n      idx: null\n    };\n    return state.idx;\n  }\n  function handlePop() {\n    action = Action.Pop;\n    let nextIndex = getIndex();\n    let delta = nextIndex == null ? null : nextIndex - index;\n    index = nextIndex;\n    if (listener) {\n      listener({\n        action,\n        location: history.location,\n        delta\n      });\n    }\n  }\n  function push(to, state) {\n    action = Action.Push;\n    let location = createLocation(history.location, to, state);\n    if (validateLocation) validateLocation(location, to);\n    index = getIndex() + 1;\n    let historyState = getHistoryState(location, index);\n    let url = history.createHref(location);\n    // try...catch because iOS limits us to 100 pushState calls :/\n    try {\n      globalHistory.pushState(historyState, \"\", url);\n    } catch (error) {\n      // If the exception is because `state` can't be serialized, let that throw\n      // outwards just like a replace call would so the dev knows the cause\n      // https://html.spec.whatwg.org/multipage/nav-history-apis.html#shared-history-push/replace-state-steps\n      // https://html.spec.whatwg.org/multipage/structured-data.html#structuredserializeinternal\n      if (error instanceof DOMException && error.name === \"DataCloneError\") {\n        throw error;\n      }\n      // They are going to lose state here, but there is no real\n      // way to warn them about it since the page will refresh...\n      window.location.assign(url);\n    }\n    if (v5Compat && listener) {\n      listener({\n        action,\n        location: history.location,\n        delta: 1\n      });\n    }\n  }\n  function replace(to, state) {\n    action = Action.Replace;\n    let location = createLocation(history.location, to, state);\n    if (validateLocation) validateLocation(location, to);\n    index = getIndex();\n    let historyState = getHistoryState(location, index);\n    let url = history.createHref(location);\n    globalHistory.replaceState(historyState, \"\", url);\n    if (v5Compat && listener) {\n      listener({\n        action,\n        location: history.location,\n        delta: 0\n      });\n    }\n  }\n  function createURL(to) {\n    // window.location.origin is \"null\" (the literal string value) in Firefox\n    // under certain conditions, notably when serving from a local HTML file\n    // See https://bugzilla.mozilla.org/show_bug.cgi?id=878297\n    let base = window.location.origin !== \"null\" ? window.location.origin : window.location.href;\n    let href = typeof to === \"string\" ? to : createPath(to);\n    // Treating this as a full URL will strip any trailing spaces so we need to\n    // pre-encode them since they might be part of a matching splat param from\n    // an ancestor route\n    href = href.replace(/ $/, \"%20\");\n    invariant(base, \"No window.location.(origin|href) available to create URL for href: \" + href);\n    return new URL(href, base);\n  }\n  let history = {\n    get action() {\n      return action;\n    },\n    get location() {\n      return getLocation(window, globalHistory);\n    },\n    listen(fn) {\n      if (listener) {\n        throw new Error(\"A history only accepts one active listener\");\n      }\n      window.addEventListener(PopStateEventType, handlePop);\n      listener = fn;\n      return () => {\n        window.removeEventListener(PopStateEventType, handlePop);\n        listener = null;\n      };\n    },\n    createHref(to) {\n      return createHref(window, to);\n    },\n    createURL,\n    encodeLocation(to) {\n      // Encode a Location the same way window.location would\n      let url = createURL(to);\n      return {\n        pathname: url.pathname,\n        search: url.search,\n        hash: url.hash\n      };\n    },\n    push,\n    replace,\n    go(n) {\n      return globalHistory.go(n);\n    }\n  };\n  return history;\n}\n//#endregion\n\nvar ResultType;\n(function (ResultType) {\n  ResultType[\"data\"] = \"data\";\n  ResultType[\"deferred\"] = \"deferred\";\n  ResultType[\"redirect\"] = \"redirect\";\n  ResultType[\"error\"] = \"error\";\n})(ResultType || (ResultType = {}));\nconst immutableRouteKeys = new Set([\"lazy\", \"caseSensitive\", \"path\", \"id\", \"index\", \"children\"]);\nfunction isIndexRoute(route) {\n  return route.index === true;\n}\n// Walk the route tree generating unique IDs where necessary, so we are working\n// solely with AgnosticDataRouteObject's within the Router\nfunction convertRoutesToDataRoutes(routes, mapRouteProperties, parentPath, manifest) {\n  if (parentPath === void 0) {\n    parentPath = [];\n  }\n  if (manifest === void 0) {\n    manifest = {};\n  }\n  return routes.map((route, index) => {\n    let treePath = [...parentPath, String(index)];\n    let id = typeof route.id === \"string\" ? route.id : treePath.join(\"-\");\n    invariant(route.index !== true || !route.children, \"Cannot specify children on an index route\");\n    invariant(!manifest[id], \"Found a route id collision on id \\\"\" + id + \"\\\".  Route \" + \"id's must be globally unique within Data Router usages\");\n    if (isIndexRoute(route)) {\n      let indexRoute = _extends({}, route, mapRouteProperties(route), {\n        id\n      });\n      manifest[id] = indexRoute;\n      return indexRoute;\n    } else {\n      let pathOrLayoutRoute = _extends({}, route, mapRouteProperties(route), {\n        id,\n        children: undefined\n      });\n      manifest[id] = pathOrLayoutRoute;\n      if (route.children) {\n        pathOrLayoutRoute.children = convertRoutesToDataRoutes(route.children, mapRouteProperties, treePath, manifest);\n      }\n      return pathOrLayoutRoute;\n    }\n  });\n}\n/**\n * Matches the given routes to a location and returns the match data.\n *\n * @see https://reactrouter.com/v6/utils/match-routes\n */\nfunction matchRoutes(routes, locationArg, basename) {\n  if (basename === void 0) {\n    basename = \"/\";\n  }\n  return matchRoutesImpl(routes, locationArg, basename, false);\n}\nfunction matchRoutesImpl(routes, locationArg, basename, allowPartial) {\n  let location = typeof locationArg === \"string\" ? parsePath(locationArg) : locationArg;\n  let pathname = stripBasename(location.pathname || \"/\", basename);\n  if (pathname == null) {\n    return null;\n  }\n  let branches = flattenRoutes(routes);\n  rankRouteBranches(branches);\n  let matches = null;\n  for (let i = 0; matches == null && i < branches.length; ++i) {\n    // Incoming pathnames are generally encoded from either window.location\n    // or from router.navigate, but we want to match against the unencoded\n    // paths in the route definitions.  Memory router locations won't be\n    // encoded here but there also shouldn't be anything to decode so this\n    // should be a safe operation.  This avoids needing matchRoutes to be\n    // history-aware.\n    let decoded = decodePath(pathname);\n    matches = matchRouteBranch(branches[i], decoded, allowPartial);\n  }\n  return matches;\n}\nfunction convertRouteMatchToUiMatch(match, loaderData) {\n  let {\n    route,\n    pathname,\n    params\n  } = match;\n  return {\n    id: route.id,\n    pathname,\n    params,\n    data: loaderData[route.id],\n    handle: route.handle\n  };\n}\nfunction flattenRoutes(routes, branches, parentsMeta, parentPath) {\n  if (branches === void 0) {\n    branches = [];\n  }\n  if (parentsMeta === void 0) {\n    parentsMeta = [];\n  }\n  if (parentPath === void 0) {\n    parentPath = \"\";\n  }\n  let flattenRoute = (route, index, relativePath) => {\n    let meta = {\n      relativePath: relativePath === undefined ? route.path || \"\" : relativePath,\n      caseSensitive: route.caseSensitive === true,\n      childrenIndex: index,\n      route\n    };\n    if (meta.relativePath.startsWith(\"/\")) {\n      invariant(meta.relativePath.startsWith(parentPath), \"Absolute route path \\\"\" + meta.relativePath + \"\\\" nested under path \" + (\"\\\"\" + parentPath + \"\\\" is not valid. An absolute child route path \") + \"must start with the combined path of all its parent routes.\");\n      meta.relativePath = meta.relativePath.slice(parentPath.length);\n    }\n    let path = joinPaths([parentPath, meta.relativePath]);\n    let routesMeta = parentsMeta.concat(meta);\n    // Add the children before adding this route to the array, so we traverse the\n    // route tree depth-first and child routes appear before their parents in\n    // the \"flattened\" version.\n    if (route.children && route.children.length > 0) {\n      invariant(\n      // Our types know better, but runtime JS may not!\n      // @ts-expect-error\n      route.index !== true, \"Index routes must not have child routes. Please remove \" + (\"all child routes from route path \\\"\" + path + \"\\\".\"));\n      flattenRoutes(route.children, branches, routesMeta, path);\n    }\n    // Routes without a path shouldn't ever match by themselves unless they are\n    // index routes, so don't add them to the list of possible branches.\n    if (route.path == null && !route.index) {\n      return;\n    }\n    branches.push({\n      path,\n      score: computeScore(path, route.index),\n      routesMeta\n    });\n  };\n  routes.forEach((route, index) => {\n    var _route$path;\n    // coarse-grain check for optional params\n    if (route.path === \"\" || !((_route$path = route.path) != null && _route$path.includes(\"?\"))) {\n      flattenRoute(route, index);\n    } else {\n      for (let exploded of explodeOptionalSegments(route.path)) {\n        flattenRoute(route, index, exploded);\n      }\n    }\n  });\n  return branches;\n}\n/**\n * Computes all combinations of optional path segments for a given path,\n * excluding combinations that are ambiguous and of lower priority.\n *\n * For example, `/one/:two?/three/:four?/:five?` explodes to:\n * - `/one/three`\n * - `/one/:two/three`\n * - `/one/three/:four`\n * - `/one/three/:five`\n * - `/one/:two/three/:four`\n * - `/one/:two/three/:five`\n * - `/one/three/:four/:five`\n * - `/one/:two/three/:four/:five`\n */\nfunction explodeOptionalSegments(path) {\n  let segments = path.split(\"/\");\n  if (segments.length === 0) return [];\n  let [first, ...rest] = segments;\n  // Optional path segments are denoted by a trailing `?`\n  let isOptional = first.endsWith(\"?\");\n  // Compute the corresponding required segment: `foo?` -> `foo`\n  let required = first.replace(/\\?$/, \"\");\n  if (rest.length === 0) {\n    // Intepret empty string as omitting an optional segment\n    // `[\"one\", \"\", \"three\"]` corresponds to omitting `:two` from `/one/:two?/three` -> `/one/three`\n    return isOptional ? [required, \"\"] : [required];\n  }\n  let restExploded = explodeOptionalSegments(rest.join(\"/\"));\n  let result = [];\n  // All child paths with the prefix.  Do this for all children before the\n  // optional version for all children, so we get consistent ordering where the\n  // parent optional aspect is preferred as required.  Otherwise, we can get\n  // child sections interspersed where deeper optional segments are higher than\n  // parent optional segments, where for example, /:two would explode _earlier_\n  // then /:one.  By always including the parent as required _for all children_\n  // first, we avoid this issue\n  result.push(...restExploded.map(subpath => subpath === \"\" ? required : [required, subpath].join(\"/\")));\n  // Then, if this is an optional value, add all child versions without\n  if (isOptional) {\n    result.push(...restExploded);\n  }\n  // for absolute paths, ensure `/` instead of empty segment\n  return result.map(exploded => path.startsWith(\"/\") && exploded === \"\" ? \"/\" : exploded);\n}\nfunction rankRouteBranches(branches) {\n  branches.sort((a, b) => a.score !== b.score ? b.score - a.score // Higher score first\n  : compareIndexes(a.routesMeta.map(meta => meta.childrenIndex), b.routesMeta.map(meta => meta.childrenIndex)));\n}\nconst paramRe = /^:[\\w-]+$/;\nconst dynamicSegmentValue = 3;\nconst indexRouteValue = 2;\nconst emptySegmentValue = 1;\nconst staticSegmentValue = 10;\nconst splatPenalty = -2;\nconst isSplat = s => s === \"*\";\nfunction computeScore(path, index) {\n  let segments = path.split(\"/\");\n  let initialScore = segments.length;\n  if (segments.some(isSplat)) {\n    initialScore += splatPenalty;\n  }\n  if (index) {\n    initialScore += indexRouteValue;\n  }\n  return segments.filter(s => !isSplat(s)).reduce((score, segment) => score + (paramRe.test(segment) ? dynamicSegmentValue : segment === \"\" ? emptySegmentValue : staticSegmentValue), initialScore);\n}\nfunction compareIndexes(a, b) {\n  let siblings = a.length === b.length && a.slice(0, -1).every((n, i) => n === b[i]);\n  return siblings ?\n  // If two routes are siblings, we should try to match the earlier sibling\n  // first. This allows people to have fine-grained control over the matching\n  // behavior by simply putting routes with identical paths in the order they\n  // want them tried.\n  a[a.length - 1] - b[b.length - 1] :\n  // Otherwise, it doesn't really make sense to rank non-siblings by index,\n  // so they sort equally.\n  0;\n}\nfunction matchRouteBranch(branch, pathname, allowPartial) {\n  if (allowPartial === void 0) {\n    allowPartial = false;\n  }\n  let {\n    routesMeta\n  } = branch;\n  let matchedParams = {};\n  let matchedPathname = \"/\";\n  let matches = [];\n  for (let i = 0; i < routesMeta.length; ++i) {\n    let meta = routesMeta[i];\n    let end = i === routesMeta.length - 1;\n    let remainingPathname = matchedPathname === \"/\" ? pathname : pathname.slice(matchedPathname.length) || \"/\";\n    let match = matchPath({\n      path: meta.relativePath,\n      caseSensitive: meta.caseSensitive,\n      end\n    }, remainingPathname);\n    let route = meta.route;\n    if (!match && end && allowPartial && !routesMeta[routesMeta.length - 1].route.index) {\n      match = matchPath({\n        path: meta.relativePath,\n        caseSensitive: meta.caseSensitive,\n        end: false\n      }, remainingPathname);\n    }\n    if (!match) {\n      return null;\n    }\n    Object.assign(matchedParams, match.params);\n    matches.push({\n      // TODO: Can this as be avoided?\n      params: matchedParams,\n      pathname: joinPaths([matchedPathname, match.pathname]),\n      pathnameBase: normalizePathname(joinPaths([matchedPathname, match.pathnameBase])),\n      route\n    });\n    if (match.pathnameBase !== \"/\") {\n      matchedPathname = joinPaths([matchedPathname, match.pathnameBase]);\n    }\n  }\n  return matches;\n}\n/**\n * Returns a path with params interpolated.\n *\n * @see https://reactrouter.com/v6/utils/generate-path\n */\nfunction generatePath(originalPath, params) {\n  if (params === void 0) {\n    params = {};\n  }\n  let path = originalPath;\n  if (path.endsWith(\"*\") && path !== \"*\" && !path.endsWith(\"/*\")) {\n    warning(false, \"Route path \\\"\" + path + \"\\\" will be treated as if it were \" + (\"\\\"\" + path.replace(/\\*$/, \"/*\") + \"\\\" because the `*` character must \") + \"always follow a `/` in the pattern. To get rid of this warning, \" + (\"please change the route path to \\\"\" + path.replace(/\\*$/, \"/*\") + \"\\\".\"));\n    path = path.replace(/\\*$/, \"/*\");\n  }\n  // ensure `/` is added at the beginning if the path is absolute\n  const prefix = path.startsWith(\"/\") ? \"/\" : \"\";\n  const stringify = p => p == null ? \"\" : typeof p === \"string\" ? p : String(p);\n  const segments = path.split(/\\/+/).map((segment, index, array) => {\n    const isLastSegment = index === array.length - 1;\n    // only apply the splat if it's the last segment\n    if (isLastSegment && segment === \"*\") {\n      const star = \"*\";\n      // Apply the splat\n      return stringify(params[star]);\n    }\n    const keyMatch = segment.match(/^:([\\w-]+)(\\??)$/);\n    if (keyMatch) {\n      const [, key, optional] = keyMatch;\n      let param = params[key];\n      invariant(optional === \"?\" || param != null, \"Missing \\\":\" + key + \"\\\" param\");\n      return stringify(param);\n    }\n    // Remove any optional markers from optional static segments\n    return segment.replace(/\\?$/g, \"\");\n  })\n  // Remove empty segments\n  .filter(segment => !!segment);\n  return prefix + segments.join(\"/\");\n}\n/**\n * Performs pattern matching on a URL pathname and returns information about\n * the match.\n *\n * @see https://reactrouter.com/v6/utils/match-path\n */\nfunction matchPath(pattern, pathname) {\n  if (typeof pattern === \"string\") {\n    pattern = {\n      path: pattern,\n      caseSensitive: false,\n      end: true\n    };\n  }\n  let [matcher, compiledParams] = compilePath(pattern.path, pattern.caseSensitive, pattern.end);\n  let match = pathname.match(matcher);\n  if (!match) return null;\n  let matchedPathname = match[0];\n  let pathnameBase = matchedPathname.replace(/(.)\\/+$/, \"$1\");\n  let captureGroups = match.slice(1);\n  let params = compiledParams.reduce((memo, _ref, index) => {\n    let {\n      paramName,\n      isOptional\n    } = _ref;\n    // We need to compute the pathnameBase here using the raw splat value\n    // instead of using params[\"*\"] later because it will be decoded then\n    if (paramName === \"*\") {\n      let splatValue = captureGroups[index] || \"\";\n      pathnameBase = matchedPathname.slice(0, matchedPathname.length - splatValue.length).replace(/(.)\\/+$/, \"$1\");\n    }\n    const value = captureGroups[index];\n    if (isOptional && !value) {\n      memo[paramName] = undefined;\n    } else {\n      memo[paramName] = (value || \"\").replace(/%2F/g, \"/\");\n    }\n    return memo;\n  }, {});\n  return {\n    params,\n    pathname: matchedPathname,\n    pathnameBase,\n    pattern\n  };\n}\nfunction compilePath(path, caseSensitive, end) {\n  if (caseSensitive === void 0) {\n    caseSensitive = false;\n  }\n  if (end === void 0) {\n    end = true;\n  }\n  warning(path === \"*\" || !path.endsWith(\"*\") || path.endsWith(\"/*\"), \"Route path \\\"\" + path + \"\\\" will be treated as if it were \" + (\"\\\"\" + path.replace(/\\*$/, \"/*\") + \"\\\" because the `*` character must \") + \"always follow a `/` in the pattern. To get rid of this warning, \" + (\"please change the route path to \\\"\" + path.replace(/\\*$/, \"/*\") + \"\\\".\"));\n  let params = [];\n  let regexpSource = \"^\" + path.replace(/\\/*\\*?$/, \"\") // Ignore trailing / and /*, we'll handle it below\n  .replace(/^\\/*/, \"/\") // Make sure it has a leading /\n  .replace(/[\\\\.*+^${}|()[\\]]/g, \"\\\\$&\") // Escape special regex chars\n  .replace(/\\/:([\\w-]+)(\\?)?/g, (_, paramName, isOptional) => {\n    params.push({\n      paramName,\n      isOptional: isOptional != null\n    });\n    return isOptional ? \"/?([^\\\\/]+)?\" : \"/([^\\\\/]+)\";\n  });\n  if (path.endsWith(\"*\")) {\n    params.push({\n      paramName: \"*\"\n    });\n    regexpSource += path === \"*\" || path === \"/*\" ? \"(.*)$\" // Already matched the initial /, just match the rest\n    : \"(?:\\\\/(.+)|\\\\/*)$\"; // Don't include the / in params[\"*\"]\n  } else if (end) {\n    // When matching to the end, ignore trailing slashes\n    regexpSource += \"\\\\/*$\";\n  } else if (path !== \"\" && path !== \"/\") {\n    // If our path is non-empty and contains anything beyond an initial slash,\n    // then we have _some_ form of path in our regex, so we should expect to\n    // match only if we find the end of this path segment.  Look for an optional\n    // non-captured trailing slash (to match a portion of the URL) or the end\n    // of the path (if we've matched to the end).  We used to do this with a\n    // word boundary but that gives false positives on routes like\n    // /user-preferences since `-` counts as a word boundary.\n    regexpSource += \"(?:(?=\\\\/|$))\";\n  } else ;\n  let matcher = new RegExp(regexpSource, caseSensitive ? undefined : \"i\");\n  return [matcher, params];\n}\nfunction decodePath(value) {\n  try {\n    return value.split(\"/\").map(v => decodeURIComponent(v).replace(/\\//g, \"%2F\")).join(\"/\");\n  } catch (error) {\n    warning(false, \"The URL path \\\"\" + value + \"\\\" could not be decoded because it is is a \" + \"malformed URL segment. This is probably due to a bad percent \" + (\"encoding (\" + error + \").\"));\n    return value;\n  }\n}\n/**\n * @private\n */\nfunction stripBasename(pathname, basename) {\n  if (basename === \"/\") return pathname;\n  if (!pathname.toLowerCase().startsWith(basename.toLowerCase())) {\n    return null;\n  }\n  // We want to leave trailing slash behavior in the user's control, so if they\n  // specify a basename with a trailing slash, we should support it\n  let startIndex = basename.endsWith(\"/\") ? basename.length - 1 : basename.length;\n  let nextChar = pathname.charAt(startIndex);\n  if (nextChar && nextChar !== \"/\") {\n    // pathname does not start with basename/\n    return null;\n  }\n  return pathname.slice(startIndex) || \"/\";\n}\n/**\n * Returns a resolved path object relative to the given pathname.\n *\n * @see https://reactrouter.com/v6/utils/resolve-path\n */\nfunction resolvePath(to, fromPathname) {\n  if (fromPathname === void 0) {\n    fromPathname = \"/\";\n  }\n  let {\n    pathname: toPathname,\n    search = \"\",\n    hash = \"\"\n  } = typeof to === \"string\" ? parsePath(to) : to;\n  let pathname = toPathname ? toPathname.startsWith(\"/\") ? toPathname : resolvePathname(toPathname, fromPathname) : fromPathname;\n  return {\n    pathname,\n    search: normalizeSearch(search),\n    hash: normalizeHash(hash)\n  };\n}\nfunction resolvePathname(relativePath, fromPathname) {\n  let segments = fromPathname.replace(/\\/+$/, \"\").split(\"/\");\n  let relativeSegments = relativePath.split(\"/\");\n  relativeSegments.forEach(segment => {\n    if (segment === \"..\") {\n      // Keep the root \"\" segment so the pathname starts at /\n      if (segments.length > 1) segments.pop();\n    } else if (segment !== \".\") {\n      segments.push(segment);\n    }\n  });\n  return segments.length > 1 ? segments.join(\"/\") : \"/\";\n}\nfunction getInvalidPathError(char, field, dest, path) {\n  return \"Cannot include a '\" + char + \"' character in a manually specified \" + (\"`to.\" + field + \"` field [\" + JSON.stringify(path) + \"].  Please separate it out to the \") + (\"`to.\" + dest + \"` field. Alternatively you may provide the full path as \") + \"a string in <Link to=\\\"...\\\"> and the router will parse it for you.\";\n}\n/**\n * @private\n *\n * When processing relative navigation we want to ignore ancestor routes that\n * do not contribute to the path, such that index/pathless layout routes don't\n * interfere.\n *\n * For example, when moving a route element into an index route and/or a\n * pathless layout route, relative link behavior contained within should stay\n * the same.  Both of the following examples should link back to the root:\n *\n *   <Route path=\"/\">\n *     <Route path=\"accounts\" element={<Link to=\"..\"}>\n *   </Route>\n *\n *   <Route path=\"/\">\n *     <Route path=\"accounts\">\n *       <Route element={<AccountsLayout />}>       // <-- Does not contribute\n *         <Route index element={<Link to=\"..\"} />  // <-- Does not contribute\n *       </Route\n *     </Route>\n *   </Route>\n */\nfunction getPathContributingMatches(matches) {\n  return matches.filter((match, index) => index === 0 || match.route.path && match.route.path.length > 0);\n}\n// Return the array of pathnames for the current route matches - used to\n// generate the routePathnames input for resolveTo()\nfunction getResolveToMatches(matches, v7_relativeSplatPath) {\n  let pathMatches = getPathContributingMatches(matches);\n  // When v7_relativeSplatPath is enabled, use the full pathname for the leaf\n  // match so we include splat values for \".\" links.  See:\n  // https://github.com/remix-run/react-router/issues/11052#issuecomment-**********\n  if (v7_relativeSplatPath) {\n    return pathMatches.map((match, idx) => idx === pathMatches.length - 1 ? match.pathname : match.pathnameBase);\n  }\n  return pathMatches.map(match => match.pathnameBase);\n}\n/**\n * @private\n */\nfunction resolveTo(toArg, routePathnames, locationPathname, isPathRelative) {\n  if (isPathRelative === void 0) {\n    isPathRelative = false;\n  }\n  let to;\n  if (typeof toArg === \"string\") {\n    to = parsePath(toArg);\n  } else {\n    to = _extends({}, toArg);\n    invariant(!to.pathname || !to.pathname.includes(\"?\"), getInvalidPathError(\"?\", \"pathname\", \"search\", to));\n    invariant(!to.pathname || !to.pathname.includes(\"#\"), getInvalidPathError(\"#\", \"pathname\", \"hash\", to));\n    invariant(!to.search || !to.search.includes(\"#\"), getInvalidPathError(\"#\", \"search\", \"hash\", to));\n  }\n  let isEmptyPath = toArg === \"\" || to.pathname === \"\";\n  let toPathname = isEmptyPath ? \"/\" : to.pathname;\n  let from;\n  // Routing is relative to the current pathname if explicitly requested.\n  //\n  // If a pathname is explicitly provided in `to`, it should be relative to the\n  // route context. This is explained in `Note on `<Link to>` values` in our\n  // migration guide from v5 as a means of disambiguation between `to` values\n  // that begin with `/` and those that do not. However, this is problematic for\n  // `to` values that do not provide a pathname. `to` can simply be a search or\n  // hash string, in which case we should assume that the navigation is relative\n  // to the current location's pathname and *not* the route pathname.\n  if (toPathname == null) {\n    from = locationPathname;\n  } else {\n    let routePathnameIndex = routePathnames.length - 1;\n    // With relative=\"route\" (the default), each leading .. segment means\n    // \"go up one route\" instead of \"go up one URL segment\".  This is a key\n    // difference from how <a href> works and a major reason we call this a\n    // \"to\" value instead of a \"href\".\n    if (!isPathRelative && toPathname.startsWith(\"..\")) {\n      let toSegments = toPathname.split(\"/\");\n      while (toSegments[0] === \"..\") {\n        toSegments.shift();\n        routePathnameIndex -= 1;\n      }\n      to.pathname = toSegments.join(\"/\");\n    }\n    from = routePathnameIndex >= 0 ? routePathnames[routePathnameIndex] : \"/\";\n  }\n  let path = resolvePath(to, from);\n  // Ensure the pathname has a trailing slash if the original \"to\" had one\n  let hasExplicitTrailingSlash = toPathname && toPathname !== \"/\" && toPathname.endsWith(\"/\");\n  // Or if this was a link to the current path which has a trailing slash\n  let hasCurrentTrailingSlash = (isEmptyPath || toPathname === \".\") && locationPathname.endsWith(\"/\");\n  if (!path.pathname.endsWith(\"/\") && (hasExplicitTrailingSlash || hasCurrentTrailingSlash)) {\n    path.pathname += \"/\";\n  }\n  return path;\n}\n/**\n * @private\n */\nfunction getToPathname(to) {\n  // Empty strings should be treated the same as / paths\n  return to === \"\" || to.pathname === \"\" ? \"/\" : typeof to === \"string\" ? parsePath(to).pathname : to.pathname;\n}\n/**\n * @private\n */\nconst joinPaths = paths => paths.join(\"/\").replace(/\\/\\/+/g, \"/\");\n/**\n * @private\n */\nconst normalizePathname = pathname => pathname.replace(/\\/+$/, \"\").replace(/^\\/*/, \"/\");\n/**\n * @private\n */\nconst normalizeSearch = search => !search || search === \"?\" ? \"\" : search.startsWith(\"?\") ? search : \"?\" + search;\n/**\n * @private\n */\nconst normalizeHash = hash => !hash || hash === \"#\" ? \"\" : hash.startsWith(\"#\") ? hash : \"#\" + hash;\n/**\n * This is a shortcut for creating `application/json` responses. Converts `data`\n * to JSON and sets the `Content-Type` header.\n *\n * @deprecated The `json` method is deprecated in favor of returning raw objects.\n * This method will be removed in v7.\n */\nconst json = function json(data, init) {\n  if (init === void 0) {\n    init = {};\n  }\n  let responseInit = typeof init === \"number\" ? {\n    status: init\n  } : init;\n  let headers = new Headers(responseInit.headers);\n  if (!headers.has(\"Content-Type\")) {\n    headers.set(\"Content-Type\", \"application/json; charset=utf-8\");\n  }\n  return new Response(JSON.stringify(data), _extends({}, responseInit, {\n    headers\n  }));\n};\nclass DataWithResponseInit {\n  constructor(data, init) {\n    this.type = \"DataWithResponseInit\";\n    this.data = data;\n    this.init = init || null;\n  }\n}\n/**\n * Create \"responses\" that contain `status`/`headers` without forcing\n * serialization into an actual `Response` - used by Remix single fetch\n */\nfunction data(data, init) {\n  return new DataWithResponseInit(data, typeof init === \"number\" ? {\n    status: init\n  } : init);\n}\nclass AbortedDeferredError extends Error {}\nclass DeferredData {\n  constructor(data, responseInit) {\n    this.pendingKeysSet = new Set();\n    this.subscribers = new Set();\n    this.deferredKeys = [];\n    invariant(data && typeof data === \"object\" && !Array.isArray(data), \"defer() only accepts plain objects\");\n    // Set up an AbortController + Promise we can race against to exit early\n    // cancellation\n    let reject;\n    this.abortPromise = new Promise((_, r) => reject = r);\n    this.controller = new AbortController();\n    let onAbort = () => reject(new AbortedDeferredError(\"Deferred data aborted\"));\n    this.unlistenAbortSignal = () => this.controller.signal.removeEventListener(\"abort\", onAbort);\n    this.controller.signal.addEventListener(\"abort\", onAbort);\n    this.data = Object.entries(data).reduce((acc, _ref2) => {\n      let [key, value] = _ref2;\n      return Object.assign(acc, {\n        [key]: this.trackPromise(key, value)\n      });\n    }, {});\n    if (this.done) {\n      // All incoming values were resolved\n      this.unlistenAbortSignal();\n    }\n    this.init = responseInit;\n  }\n  trackPromise(key, value) {\n    if (!(value instanceof Promise)) {\n      return value;\n    }\n    this.deferredKeys.push(key);\n    this.pendingKeysSet.add(key);\n    // We store a little wrapper promise that will be extended with\n    // _data/_error props upon resolve/reject\n    let promise = Promise.race([value, this.abortPromise]).then(data => this.onSettle(promise, key, undefined, data), error => this.onSettle(promise, key, error));\n    // Register rejection listeners to avoid uncaught promise rejections on\n    // errors or aborted deferred values\n    promise.catch(() => {});\n    Object.defineProperty(promise, \"_tracked\", {\n      get: () => true\n    });\n    return promise;\n  }\n  onSettle(promise, key, error, data) {\n    if (this.controller.signal.aborted && error instanceof AbortedDeferredError) {\n      this.unlistenAbortSignal();\n      Object.defineProperty(promise, \"_error\", {\n        get: () => error\n      });\n      return Promise.reject(error);\n    }\n    this.pendingKeysSet.delete(key);\n    if (this.done) {\n      // Nothing left to abort!\n      this.unlistenAbortSignal();\n    }\n    // If the promise was resolved/rejected with undefined, we'll throw an error as you\n    // should always resolve with a value or null\n    if (error === undefined && data === undefined) {\n      let undefinedError = new Error(\"Deferred data for key \\\"\" + key + \"\\\" resolved/rejected with `undefined`, \" + \"you must resolve/reject with a value or `null`.\");\n      Object.defineProperty(promise, \"_error\", {\n        get: () => undefinedError\n      });\n      this.emit(false, key);\n      return Promise.reject(undefinedError);\n    }\n    if (data === undefined) {\n      Object.defineProperty(promise, \"_error\", {\n        get: () => error\n      });\n      this.emit(false, key);\n      return Promise.reject(error);\n    }\n    Object.defineProperty(promise, \"_data\", {\n      get: () => data\n    });\n    this.emit(false, key);\n    return data;\n  }\n  emit(aborted, settledKey) {\n    this.subscribers.forEach(subscriber => subscriber(aborted, settledKey));\n  }\n  subscribe(fn) {\n    this.subscribers.add(fn);\n    return () => this.subscribers.delete(fn);\n  }\n  cancel() {\n    this.controller.abort();\n    this.pendingKeysSet.forEach((v, k) => this.pendingKeysSet.delete(k));\n    this.emit(true);\n  }\n  async resolveData(signal) {\n    let aborted = false;\n    if (!this.done) {\n      let onAbort = () => this.cancel();\n      signal.addEventListener(\"abort\", onAbort);\n      aborted = await new Promise(resolve => {\n        this.subscribe(aborted => {\n          signal.removeEventListener(\"abort\", onAbort);\n          if (aborted || this.done) {\n            resolve(aborted);\n          }\n        });\n      });\n    }\n    return aborted;\n  }\n  get done() {\n    return this.pendingKeysSet.size === 0;\n  }\n  get unwrappedData() {\n    invariant(this.data !== null && this.done, \"Can only unwrap data on initialized and settled deferreds\");\n    return Object.entries(this.data).reduce((acc, _ref3) => {\n      let [key, value] = _ref3;\n      return Object.assign(acc, {\n        [key]: unwrapTrackedPromise(value)\n      });\n    }, {});\n  }\n  get pendingKeys() {\n    return Array.from(this.pendingKeysSet);\n  }\n}\nfunction isTrackedPromise(value) {\n  return value instanceof Promise && value._tracked === true;\n}\nfunction unwrapTrackedPromise(value) {\n  if (!isTrackedPromise(value)) {\n    return value;\n  }\n  if (value._error) {\n    throw value._error;\n  }\n  return value._data;\n}\n/**\n * @deprecated The `defer` method is deprecated in favor of returning raw\n * objects. This method will be removed in v7.\n */\nconst defer = function defer(data, init) {\n  if (init === void 0) {\n    init = {};\n  }\n  let responseInit = typeof init === \"number\" ? {\n    status: init\n  } : init;\n  return new DeferredData(data, responseInit);\n};\n/**\n * A redirect response. Sets the status code and the `Location` header.\n * Defaults to \"302 Found\".\n */\nconst redirect = function redirect(url, init) {\n  if (init === void 0) {\n    init = 302;\n  }\n  let responseInit = init;\n  if (typeof responseInit === \"number\") {\n    responseInit = {\n      status: responseInit\n    };\n  } else if (typeof responseInit.status === \"undefined\") {\n    responseInit.status = 302;\n  }\n  let headers = new Headers(responseInit.headers);\n  headers.set(\"Location\", url);\n  return new Response(null, _extends({}, responseInit, {\n    headers\n  }));\n};\n/**\n * A redirect response that will force a document reload to the new location.\n * Sets the status code and the `Location` header.\n * Defaults to \"302 Found\".\n */\nconst redirectDocument = (url, init) => {\n  let response = redirect(url, init);\n  response.headers.set(\"X-Remix-Reload-Document\", \"true\");\n  return response;\n};\n/**\n * A redirect response that will perform a `history.replaceState` instead of a\n * `history.pushState` for client-side navigation redirects.\n * Sets the status code and the `Location` header.\n * Defaults to \"302 Found\".\n */\nconst replace = (url, init) => {\n  let response = redirect(url, init);\n  response.headers.set(\"X-Remix-Replace\", \"true\");\n  return response;\n};\n/**\n * @private\n * Utility class we use to hold auto-unwrapped 4xx/5xx Response bodies\n *\n * We don't export the class for public use since it's an implementation\n * detail, but we export the interface above so folks can build their own\n * abstractions around instances via isRouteErrorResponse()\n */\nclass ErrorResponseImpl {\n  constructor(status, statusText, data, internal) {\n    if (internal === void 0) {\n      internal = false;\n    }\n    this.status = status;\n    this.statusText = statusText || \"\";\n    this.internal = internal;\n    if (data instanceof Error) {\n      this.data = data.toString();\n      this.error = data;\n    } else {\n      this.data = data;\n    }\n  }\n}\n/**\n * Check if the given error is an ErrorResponse generated from a 4xx/5xx\n * Response thrown from an action/loader\n */\nfunction isRouteErrorResponse(error) {\n  return error != null && typeof error.status === \"number\" && typeof error.statusText === \"string\" && typeof error.internal === \"boolean\" && \"data\" in error;\n}\n\nconst validMutationMethodsArr = [\"post\", \"put\", \"patch\", \"delete\"];\nconst validMutationMethods = new Set(validMutationMethodsArr);\nconst validRequestMethodsArr = [\"get\", ...validMutationMethodsArr];\nconst validRequestMethods = new Set(validRequestMethodsArr);\nconst redirectStatusCodes = new Set([301, 302, 303, 307, 308]);\nconst redirectPreserveMethodStatusCodes = new Set([307, 308]);\nconst IDLE_NAVIGATION = {\n  state: \"idle\",\n  location: undefined,\n  formMethod: undefined,\n  formAction: undefined,\n  formEncType: undefined,\n  formData: undefined,\n  json: undefined,\n  text: undefined\n};\nconst IDLE_FETCHER = {\n  state: \"idle\",\n  data: undefined,\n  formMethod: undefined,\n  formAction: undefined,\n  formEncType: undefined,\n  formData: undefined,\n  json: undefined,\n  text: undefined\n};\nconst IDLE_BLOCKER = {\n  state: \"unblocked\",\n  proceed: undefined,\n  reset: undefined,\n  location: undefined\n};\nconst ABSOLUTE_URL_REGEX = /^(?:[a-z][a-z0-9+.-]*:|\\/\\/)/i;\nconst defaultMapRouteProperties = route => ({\n  hasErrorBoundary: Boolean(route.hasErrorBoundary)\n});\nconst TRANSITIONS_STORAGE_KEY = \"remix-router-transitions\";\n//#endregion\n////////////////////////////////////////////////////////////////////////////////\n//#region createRouter\n////////////////////////////////////////////////////////////////////////////////\n/**\n * Create a router and listen to history POP navigations\n */\nfunction createRouter(init) {\n  const routerWindow = init.window ? init.window : typeof window !== \"undefined\" ? window : undefined;\n  const isBrowser = typeof routerWindow !== \"undefined\" && typeof routerWindow.document !== \"undefined\" && typeof routerWindow.document.createElement !== \"undefined\";\n  const isServer = !isBrowser;\n  invariant(init.routes.length > 0, \"You must provide a non-empty routes array to createRouter\");\n  let mapRouteProperties;\n  if (init.mapRouteProperties) {\n    mapRouteProperties = init.mapRouteProperties;\n  } else if (init.detectErrorBoundary) {\n    // If they are still using the deprecated version, wrap it with the new API\n    let detectErrorBoundary = init.detectErrorBoundary;\n    mapRouteProperties = route => ({\n      hasErrorBoundary: detectErrorBoundary(route)\n    });\n  } else {\n    mapRouteProperties = defaultMapRouteProperties;\n  }\n  // Routes keyed by ID\n  let manifest = {};\n  // Routes in tree format for matching\n  let dataRoutes = convertRoutesToDataRoutes(init.routes, mapRouteProperties, undefined, manifest);\n  let inFlightDataRoutes;\n  let basename = init.basename || \"/\";\n  let dataStrategyImpl = init.dataStrategy || defaultDataStrategy;\n  let patchRoutesOnNavigationImpl = init.patchRoutesOnNavigation;\n  // Config driven behavior flags\n  let future = _extends({\n    v7_fetcherPersist: false,\n    v7_normalizeFormMethod: false,\n    v7_partialHydration: false,\n    v7_prependBasename: false,\n    v7_relativeSplatPath: false,\n    v7_skipActionErrorRevalidation: false\n  }, init.future);\n  // Cleanup function for history\n  let unlistenHistory = null;\n  // Externally-provided functions to call on all state changes\n  let subscribers = new Set();\n  // Externally-provided object to hold scroll restoration locations during routing\n  let savedScrollPositions = null;\n  // Externally-provided function to get scroll restoration keys\n  let getScrollRestorationKey = null;\n  // Externally-provided function to get current scroll position\n  let getScrollPosition = null;\n  // One-time flag to control the initial hydration scroll restoration.  Because\n  // we don't get the saved positions from <ScrollRestoration /> until _after_\n  // the initial render, we need to manually trigger a separate updateState to\n  // send along the restoreScrollPosition\n  // Set to true if we have `hydrationData` since we assume we were SSR'd and that\n  // SSR did the initial scroll restoration.\n  let initialScrollRestored = init.hydrationData != null;\n  let initialMatches = matchRoutes(dataRoutes, init.history.location, basename);\n  let initialMatchesIsFOW = false;\n  let initialErrors = null;\n  if (initialMatches == null && !patchRoutesOnNavigationImpl) {\n    // If we do not match a user-provided-route, fall back to the root\n    // to allow the error boundary to take over\n    let error = getInternalRouterError(404, {\n      pathname: init.history.location.pathname\n    });\n    let {\n      matches,\n      route\n    } = getShortCircuitMatches(dataRoutes);\n    initialMatches = matches;\n    initialErrors = {\n      [route.id]: error\n    };\n  }\n  // In SPA apps, if the user provided a patchRoutesOnNavigation implementation and\n  // our initial match is a splat route, clear them out so we run through lazy\n  // discovery on hydration in case there's a more accurate lazy route match.\n  // In SSR apps (with `hydrationData`), we expect that the server will send\n  // up the proper matched routes so we don't want to run lazy discovery on\n  // initial hydration and want to hydrate into the splat route.\n  if (initialMatches && !init.hydrationData) {\n    let fogOfWar = checkFogOfWar(initialMatches, dataRoutes, init.history.location.pathname);\n    if (fogOfWar.active) {\n      initialMatches = null;\n    }\n  }\n  let initialized;\n  if (!initialMatches) {\n    initialized = false;\n    initialMatches = [];\n    // If partial hydration and fog of war is enabled, we will be running\n    // `patchRoutesOnNavigation` during hydration so include any partial matches as\n    // the initial matches so we can properly render `HydrateFallback`'s\n    if (future.v7_partialHydration) {\n      let fogOfWar = checkFogOfWar(null, dataRoutes, init.history.location.pathname);\n      if (fogOfWar.active && fogOfWar.matches) {\n        initialMatchesIsFOW = true;\n        initialMatches = fogOfWar.matches;\n      }\n    }\n  } else if (initialMatches.some(m => m.route.lazy)) {\n    // All initialMatches need to be loaded before we're ready.  If we have lazy\n    // functions around still then we'll need to run them in initialize()\n    initialized = false;\n  } else if (!initialMatches.some(m => m.route.loader)) {\n    // If we've got no loaders to run, then we're good to go\n    initialized = true;\n  } else if (future.v7_partialHydration) {\n    // If partial hydration is enabled, we're initialized so long as we were\n    // provided with hydrationData for every route with a loader, and no loaders\n    // were marked for explicit hydration\n    let loaderData = init.hydrationData ? init.hydrationData.loaderData : null;\n    let errors = init.hydrationData ? init.hydrationData.errors : null;\n    // If errors exist, don't consider routes below the boundary\n    if (errors) {\n      let idx = initialMatches.findIndex(m => errors[m.route.id] !== undefined);\n      initialized = initialMatches.slice(0, idx + 1).every(m => !shouldLoadRouteOnHydration(m.route, loaderData, errors));\n    } else {\n      initialized = initialMatches.every(m => !shouldLoadRouteOnHydration(m.route, loaderData, errors));\n    }\n  } else {\n    // Without partial hydration - we're initialized if we were provided any\n    // hydrationData - which is expected to be complete\n    initialized = init.hydrationData != null;\n  }\n  let router;\n  let state = {\n    historyAction: init.history.action,\n    location: init.history.location,\n    matches: initialMatches,\n    initialized,\n    navigation: IDLE_NAVIGATION,\n    // Don't restore on initial updateState() if we were SSR'd\n    restoreScrollPosition: init.hydrationData != null ? false : null,\n    preventScrollReset: false,\n    revalidation: \"idle\",\n    loaderData: init.hydrationData && init.hydrationData.loaderData || {},\n    actionData: init.hydrationData && init.hydrationData.actionData || null,\n    errors: init.hydrationData && init.hydrationData.errors || initialErrors,\n    fetchers: new Map(),\n    blockers: new Map()\n  };\n  // -- Stateful internal variables to manage navigations --\n  // Current navigation in progress (to be committed in completeNavigation)\n  let pendingAction = Action.Pop;\n  // Should the current navigation prevent the scroll reset if scroll cannot\n  // be restored?\n  let pendingPreventScrollReset = false;\n  // AbortController for the active navigation\n  let pendingNavigationController;\n  // Should the current navigation enable document.startViewTransition?\n  let pendingViewTransitionEnabled = false;\n  // Store applied view transitions so we can apply them on POP\n  let appliedViewTransitions = new Map();\n  // Cleanup function for persisting applied transitions to sessionStorage\n  let removePageHideEventListener = null;\n  // We use this to avoid touching history in completeNavigation if a\n  // revalidation is entirely uninterrupted\n  let isUninterruptedRevalidation = false;\n  // Use this internal flag to force revalidation of all loaders:\n  //  - submissions (completed or interrupted)\n  //  - useRevalidator()\n  //  - X-Remix-Revalidate (from redirect)\n  let isRevalidationRequired = false;\n  // Use this internal array to capture routes that require revalidation due\n  // to a cancelled deferred on action submission\n  let cancelledDeferredRoutes = [];\n  // Use this internal array to capture fetcher loads that were cancelled by an\n  // action navigation and require revalidation\n  let cancelledFetcherLoads = new Set();\n  // AbortControllers for any in-flight fetchers\n  let fetchControllers = new Map();\n  // Track loads based on the order in which they started\n  let incrementingLoadId = 0;\n  // Track the outstanding pending navigation data load to be compared against\n  // the globally incrementing load when a fetcher load lands after a completed\n  // navigation\n  let pendingNavigationLoadId = -1;\n  // Fetchers that triggered data reloads as a result of their actions\n  let fetchReloadIds = new Map();\n  // Fetchers that triggered redirect navigations\n  let fetchRedirectIds = new Set();\n  // Most recent href/match for fetcher.load calls for fetchers\n  let fetchLoadMatches = new Map();\n  // Ref-count mounted fetchers so we know when it's ok to clean them up\n  let activeFetchers = new Map();\n  // Fetchers that have requested a delete when using v7_fetcherPersist,\n  // they'll be officially removed after they return to idle\n  let deletedFetchers = new Set();\n  // Store DeferredData instances for active route matches.  When a\n  // route loader returns defer() we stick one in here.  Then, when a nested\n  // promise resolves we update loaderData.  If a new navigation starts we\n  // cancel active deferreds for eliminated routes.\n  let activeDeferreds = new Map();\n  // Store blocker functions in a separate Map outside of router state since\n  // we don't need to update UI state if they change\n  let blockerFunctions = new Map();\n  // Flag to ignore the next history update, so we can revert the URL change on\n  // a POP navigation that was blocked by the user without touching router state\n  let unblockBlockerHistoryUpdate = undefined;\n  // Initialize the router, all side effects should be kicked off from here.\n  // Implemented as a Fluent API for ease of:\n  //   let router = createRouter(init).initialize();\n  function initialize() {\n    // If history informs us of a POP navigation, start the navigation but do not update\n    // state.  We'll update our own state once the navigation completes\n    unlistenHistory = init.history.listen(_ref => {\n      let {\n        action: historyAction,\n        location,\n        delta\n      } = _ref;\n      // Ignore this event if it was just us resetting the URL from a\n      // blocked POP navigation\n      if (unblockBlockerHistoryUpdate) {\n        unblockBlockerHistoryUpdate();\n        unblockBlockerHistoryUpdate = undefined;\n        return;\n      }\n      warning(blockerFunctions.size === 0 || delta != null, \"You are trying to use a blocker on a POP navigation to a location \" + \"that was not created by @remix-run/router. This will fail silently in \" + \"production. This can happen if you are navigating outside the router \" + \"via `window.history.pushState`/`window.location.hash` instead of using \" + \"router navigation APIs.  This can also happen if you are using \" + \"createHashRouter and the user manually changes the URL.\");\n      let blockerKey = shouldBlockNavigation({\n        currentLocation: state.location,\n        nextLocation: location,\n        historyAction\n      });\n      if (blockerKey && delta != null) {\n        // Restore the URL to match the current UI, but don't update router state\n        let nextHistoryUpdatePromise = new Promise(resolve => {\n          unblockBlockerHistoryUpdate = resolve;\n        });\n        init.history.go(delta * -1);\n        // Put the blocker into a blocked state\n        updateBlocker(blockerKey, {\n          state: \"blocked\",\n          location,\n          proceed() {\n            updateBlocker(blockerKey, {\n              state: \"proceeding\",\n              proceed: undefined,\n              reset: undefined,\n              location\n            });\n            // Re-do the same POP navigation we just blocked, after the url\n            // restoration is also complete.  See:\n            // https://github.com/remix-run/react-router/issues/11613\n            nextHistoryUpdatePromise.then(() => init.history.go(delta));\n          },\n          reset() {\n            let blockers = new Map(state.blockers);\n            blockers.set(blockerKey, IDLE_BLOCKER);\n            updateState({\n              blockers\n            });\n          }\n        });\n        return;\n      }\n      return startNavigation(historyAction, location);\n    });\n    if (isBrowser) {\n      // FIXME: This feels gross.  How can we cleanup the lines between\n      // scrollRestoration/appliedTransitions persistance?\n      restoreAppliedTransitions(routerWindow, appliedViewTransitions);\n      let _saveAppliedTransitions = () => persistAppliedTransitions(routerWindow, appliedViewTransitions);\n      routerWindow.addEventListener(\"pagehide\", _saveAppliedTransitions);\n      removePageHideEventListener = () => routerWindow.removeEventListener(\"pagehide\", _saveAppliedTransitions);\n    }\n    // Kick off initial data load if needed.  Use Pop to avoid modifying history\n    // Note we don't do any handling of lazy here.  For SPA's it'll get handled\n    // in the normal navigation flow.  For SSR it's expected that lazy modules are\n    // resolved prior to router creation since we can't go into a fallbackElement\n    // UI for SSR'd apps\n    if (!state.initialized) {\n      startNavigation(Action.Pop, state.location, {\n        initialHydration: true\n      });\n    }\n    return router;\n  }\n  // Clean up a router and it's side effects\n  function dispose() {\n    if (unlistenHistory) {\n      unlistenHistory();\n    }\n    if (removePageHideEventListener) {\n      removePageHideEventListener();\n    }\n    subscribers.clear();\n    pendingNavigationController && pendingNavigationController.abort();\n    state.fetchers.forEach((_, key) => deleteFetcher(key));\n    state.blockers.forEach((_, key) => deleteBlocker(key));\n  }\n  // Subscribe to state updates for the router\n  function subscribe(fn) {\n    subscribers.add(fn);\n    return () => subscribers.delete(fn);\n  }\n  // Update our state and notify the calling context of the change\n  function updateState(newState, opts) {\n    if (opts === void 0) {\n      opts = {};\n    }\n    state = _extends({}, state, newState);\n    // Prep fetcher cleanup so we can tell the UI which fetcher data entries\n    // can be removed\n    let completedFetchers = [];\n    let deletedFetchersKeys = [];\n    if (future.v7_fetcherPersist) {\n      state.fetchers.forEach((fetcher, key) => {\n        if (fetcher.state === \"idle\") {\n          if (deletedFetchers.has(key)) {\n            // Unmounted from the UI and can be totally removed\n            deletedFetchersKeys.push(key);\n          } else {\n            // Returned to idle but still mounted in the UI, so semi-remains for\n            // revalidations and such\n            completedFetchers.push(key);\n          }\n        }\n      });\n    }\n    // Remove any lingering deleted fetchers that have already been removed\n    // from state.fetchers\n    deletedFetchers.forEach(key => {\n      if (!state.fetchers.has(key) && !fetchControllers.has(key)) {\n        deletedFetchersKeys.push(key);\n      }\n    });\n    // Iterate over a local copy so that if flushSync is used and we end up\n    // removing and adding a new subscriber due to the useCallback dependencies,\n    // we don't get ourselves into a loop calling the new subscriber immediately\n    [...subscribers].forEach(subscriber => subscriber(state, {\n      deletedFetchers: deletedFetchersKeys,\n      viewTransitionOpts: opts.viewTransitionOpts,\n      flushSync: opts.flushSync === true\n    }));\n    // Remove idle fetchers from state since we only care about in-flight fetchers.\n    if (future.v7_fetcherPersist) {\n      completedFetchers.forEach(key => state.fetchers.delete(key));\n      deletedFetchersKeys.forEach(key => deleteFetcher(key));\n    } else {\n      // We already called deleteFetcher() on these, can remove them from this\n      // Set now that we've handed the keys off to the data layer\n      deletedFetchersKeys.forEach(key => deletedFetchers.delete(key));\n    }\n  }\n  // Complete a navigation returning the state.navigation back to the IDLE_NAVIGATION\n  // and setting state.[historyAction/location/matches] to the new route.\n  // - Location is a required param\n  // - Navigation will always be set to IDLE_NAVIGATION\n  // - Can pass any other state in newState\n  function completeNavigation(location, newState, _temp) {\n    var _location$state, _location$state2;\n    let {\n      flushSync\n    } = _temp === void 0 ? {} : _temp;\n    // Deduce if we're in a loading/actionReload state:\n    // - We have committed actionData in the store\n    // - The current navigation was a mutation submission\n    // - We're past the submitting state and into the loading state\n    // - The location being loaded is not the result of a redirect\n    let isActionReload = state.actionData != null && state.navigation.formMethod != null && isMutationMethod(state.navigation.formMethod) && state.navigation.state === \"loading\" && ((_location$state = location.state) == null ? void 0 : _location$state._isRedirect) !== true;\n    let actionData;\n    if (newState.actionData) {\n      if (Object.keys(newState.actionData).length > 0) {\n        actionData = newState.actionData;\n      } else {\n        // Empty actionData -> clear prior actionData due to an action error\n        actionData = null;\n      }\n    } else if (isActionReload) {\n      // Keep the current data if we're wrapping up the action reload\n      actionData = state.actionData;\n    } else {\n      // Clear actionData on any other completed navigations\n      actionData = null;\n    }\n    // Always preserve any existing loaderData from re-used routes\n    let loaderData = newState.loaderData ? mergeLoaderData(state.loaderData, newState.loaderData, newState.matches || [], newState.errors) : state.loaderData;\n    // On a successful navigation we can assume we got through all blockers\n    // so we can start fresh\n    let blockers = state.blockers;\n    if (blockers.size > 0) {\n      blockers = new Map(blockers);\n      blockers.forEach((_, k) => blockers.set(k, IDLE_BLOCKER));\n    }\n    // Always respect the user flag.  Otherwise don't reset on mutation\n    // submission navigations unless they redirect\n    let preventScrollReset = pendingPreventScrollReset === true || state.navigation.formMethod != null && isMutationMethod(state.navigation.formMethod) && ((_location$state2 = location.state) == null ? void 0 : _location$state2._isRedirect) !== true;\n    // Commit any in-flight routes at the end of the HMR revalidation \"navigation\"\n    if (inFlightDataRoutes) {\n      dataRoutes = inFlightDataRoutes;\n      inFlightDataRoutes = undefined;\n    }\n    if (isUninterruptedRevalidation) ; else if (pendingAction === Action.Pop) ; else if (pendingAction === Action.Push) {\n      init.history.push(location, location.state);\n    } else if (pendingAction === Action.Replace) {\n      init.history.replace(location, location.state);\n    }\n    let viewTransitionOpts;\n    // On POP, enable transitions if they were enabled on the original navigation\n    if (pendingAction === Action.Pop) {\n      // Forward takes precedence so they behave like the original navigation\n      let priorPaths = appliedViewTransitions.get(state.location.pathname);\n      if (priorPaths && priorPaths.has(location.pathname)) {\n        viewTransitionOpts = {\n          currentLocation: state.location,\n          nextLocation: location\n        };\n      } else if (appliedViewTransitions.has(location.pathname)) {\n        // If we don't have a previous forward nav, assume we're popping back to\n        // the new location and enable if that location previously enabled\n        viewTransitionOpts = {\n          currentLocation: location,\n          nextLocation: state.location\n        };\n      }\n    } else if (pendingViewTransitionEnabled) {\n      // Store the applied transition on PUSH/REPLACE\n      let toPaths = appliedViewTransitions.get(state.location.pathname);\n      if (toPaths) {\n        toPaths.add(location.pathname);\n      } else {\n        toPaths = new Set([location.pathname]);\n        appliedViewTransitions.set(state.location.pathname, toPaths);\n      }\n      viewTransitionOpts = {\n        currentLocation: state.location,\n        nextLocation: location\n      };\n    }\n    updateState(_extends({}, newState, {\n      actionData,\n      loaderData,\n      historyAction: pendingAction,\n      location,\n      initialized: true,\n      navigation: IDLE_NAVIGATION,\n      revalidation: \"idle\",\n      restoreScrollPosition: getSavedScrollPosition(location, newState.matches || state.matches),\n      preventScrollReset,\n      blockers\n    }), {\n      viewTransitionOpts,\n      flushSync: flushSync === true\n    });\n    // Reset stateful navigation vars\n    pendingAction = Action.Pop;\n    pendingPreventScrollReset = false;\n    pendingViewTransitionEnabled = false;\n    isUninterruptedRevalidation = false;\n    isRevalidationRequired = false;\n    cancelledDeferredRoutes = [];\n  }\n  // Trigger a navigation event, which can either be a numerical POP or a PUSH\n  // replace with an optional submission\n  async function navigate(to, opts) {\n    if (typeof to === \"number\") {\n      init.history.go(to);\n      return;\n    }\n    let normalizedPath = normalizeTo(state.location, state.matches, basename, future.v7_prependBasename, to, future.v7_relativeSplatPath, opts == null ? void 0 : opts.fromRouteId, opts == null ? void 0 : opts.relative);\n    let {\n      path,\n      submission,\n      error\n    } = normalizeNavigateOptions(future.v7_normalizeFormMethod, false, normalizedPath, opts);\n    let currentLocation = state.location;\n    let nextLocation = createLocation(state.location, path, opts && opts.state);\n    // When using navigate as a PUSH/REPLACE we aren't reading an already-encoded\n    // URL from window.location, so we need to encode it here so the behavior\n    // remains the same as POP and non-data-router usages.  new URL() does all\n    // the same encoding we'd get from a history.pushState/window.location read\n    // without having to touch history\n    nextLocation = _extends({}, nextLocation, init.history.encodeLocation(nextLocation));\n    let userReplace = opts && opts.replace != null ? opts.replace : undefined;\n    let historyAction = Action.Push;\n    if (userReplace === true) {\n      historyAction = Action.Replace;\n    } else if (userReplace === false) ; else if (submission != null && isMutationMethod(submission.formMethod) && submission.formAction === state.location.pathname + state.location.search) {\n      // By default on submissions to the current location we REPLACE so that\n      // users don't have to double-click the back button to get to the prior\n      // location.  If the user redirects to a different location from the\n      // action/loader this will be ignored and the redirect will be a PUSH\n      historyAction = Action.Replace;\n    }\n    let preventScrollReset = opts && \"preventScrollReset\" in opts ? opts.preventScrollReset === true : undefined;\n    let flushSync = (opts && opts.flushSync) === true;\n    let blockerKey = shouldBlockNavigation({\n      currentLocation,\n      nextLocation,\n      historyAction\n    });\n    if (blockerKey) {\n      // Put the blocker into a blocked state\n      updateBlocker(blockerKey, {\n        state: \"blocked\",\n        location: nextLocation,\n        proceed() {\n          updateBlocker(blockerKey, {\n            state: \"proceeding\",\n            proceed: undefined,\n            reset: undefined,\n            location: nextLocation\n          });\n          // Send the same navigation through\n          navigate(to, opts);\n        },\n        reset() {\n          let blockers = new Map(state.blockers);\n          blockers.set(blockerKey, IDLE_BLOCKER);\n          updateState({\n            blockers\n          });\n        }\n      });\n      return;\n    }\n    return await startNavigation(historyAction, nextLocation, {\n      submission,\n      // Send through the formData serialization error if we have one so we can\n      // render at the right error boundary after we match routes\n      pendingError: error,\n      preventScrollReset,\n      replace: opts && opts.replace,\n      enableViewTransition: opts && opts.viewTransition,\n      flushSync\n    });\n  }\n  // Revalidate all current loaders.  If a navigation is in progress or if this\n  // is interrupted by a navigation, allow this to \"succeed\" by calling all\n  // loaders during the next loader round\n  function revalidate() {\n    interruptActiveLoads();\n    updateState({\n      revalidation: \"loading\"\n    });\n    // If we're currently submitting an action, we don't need to start a new\n    // navigation, we'll just let the follow up loader execution call all loaders\n    if (state.navigation.state === \"submitting\") {\n      return;\n    }\n    // If we're currently in an idle state, start a new navigation for the current\n    // action/location and mark it as uninterrupted, which will skip the history\n    // update in completeNavigation\n    if (state.navigation.state === \"idle\") {\n      startNavigation(state.historyAction, state.location, {\n        startUninterruptedRevalidation: true\n      });\n      return;\n    }\n    // Otherwise, if we're currently in a loading state, just start a new\n    // navigation to the navigation.location but do not trigger an uninterrupted\n    // revalidation so that history correctly updates once the navigation completes\n    startNavigation(pendingAction || state.historyAction, state.navigation.location, {\n      overrideNavigation: state.navigation,\n      // Proxy through any rending view transition\n      enableViewTransition: pendingViewTransitionEnabled === true\n    });\n  }\n  // Start a navigation to the given action/location.  Can optionally provide a\n  // overrideNavigation which will override the normalLoad in the case of a redirect\n  // navigation\n  async function startNavigation(historyAction, location, opts) {\n    // Abort any in-progress navigations and start a new one. Unset any ongoing\n    // uninterrupted revalidations unless told otherwise, since we want this\n    // new navigation to update history normally\n    pendingNavigationController && pendingNavigationController.abort();\n    pendingNavigationController = null;\n    pendingAction = historyAction;\n    isUninterruptedRevalidation = (opts && opts.startUninterruptedRevalidation) === true;\n    // Save the current scroll position every time we start a new navigation,\n    // and track whether we should reset scroll on completion\n    saveScrollPosition(state.location, state.matches);\n    pendingPreventScrollReset = (opts && opts.preventScrollReset) === true;\n    pendingViewTransitionEnabled = (opts && opts.enableViewTransition) === true;\n    let routesToUse = inFlightDataRoutes || dataRoutes;\n    let loadingNavigation = opts && opts.overrideNavigation;\n    let matches = opts != null && opts.initialHydration && state.matches && state.matches.length > 0 && !initialMatchesIsFOW ?\n    // `matchRoutes()` has already been called if we're in here via `router.initialize()`\n    state.matches : matchRoutes(routesToUse, location, basename);\n    let flushSync = (opts && opts.flushSync) === true;\n    let fogOfWar = checkFogOfWar(matches, routesToUse, location.pathname);\n    if (fogOfWar.active && fogOfWar.matches) {\n      matches = fogOfWar.matches;\n    }\n    // Short circuit with a 404 on the root error boundary if we match nothing\n    if (!matches) {\n      let {\n        error,\n        notFoundMatches,\n        route\n      } = handleNavigational404(location.pathname);\n      completeNavigation(location, {\n        matches: notFoundMatches,\n        loaderData: {},\n        errors: {\n          [route.id]: error\n        }\n      }, {\n        flushSync\n      });\n      return;\n    }\n    // Short circuit if it's only a hash change and not a revalidation or\n    // mutation submission.\n    //\n    // Ignore on initial page loads because since the initial hydration will always\n    // be \"same hash\".  For example, on /page#hash and submit a <Form method=\"post\">\n    // which will default to a navigation to /page\n    if (state.initialized && !isRevalidationRequired && isHashChangeOnly(state.location, location) && !(opts && opts.submission && isMutationMethod(opts.submission.formMethod))) {\n      completeNavigation(location, {\n        matches\n      }, {\n        flushSync\n      });\n      return;\n    }\n    // Create a controller/Request for this navigation\n    pendingNavigationController = new AbortController();\n    let request = createClientSideRequest(init.history, location, pendingNavigationController.signal, opts && opts.submission);\n    let pendingActionResult;\n    if (opts && opts.pendingError) {\n      // If we have a pendingError, it means the user attempted a GET submission\n      // with binary FormData so assign here and skip to handleLoaders.  That\n      // way we handle calling loaders above the boundary etc.  It's not really\n      // different from an actionError in that sense.\n      pendingActionResult = [findNearestBoundary(matches).route.id, {\n        type: ResultType.error,\n        error: opts.pendingError\n      }];\n    } else if (opts && opts.submission && isMutationMethod(opts.submission.formMethod)) {\n      // Call action if we received an action submission\n      let actionResult = await handleAction(request, location, opts.submission, matches, fogOfWar.active, {\n        replace: opts.replace,\n        flushSync\n      });\n      if (actionResult.shortCircuited) {\n        return;\n      }\n      // If we received a 404 from handleAction, it's because we couldn't lazily\n      // discover the destination route so we don't want to call loaders\n      if (actionResult.pendingActionResult) {\n        let [routeId, result] = actionResult.pendingActionResult;\n        if (isErrorResult(result) && isRouteErrorResponse(result.error) && result.error.status === 404) {\n          pendingNavigationController = null;\n          completeNavigation(location, {\n            matches: actionResult.matches,\n            loaderData: {},\n            errors: {\n              [routeId]: result.error\n            }\n          });\n          return;\n        }\n      }\n      matches = actionResult.matches || matches;\n      pendingActionResult = actionResult.pendingActionResult;\n      loadingNavigation = getLoadingNavigation(location, opts.submission);\n      flushSync = false;\n      // No need to do fog of war matching again on loader execution\n      fogOfWar.active = false;\n      // Create a GET request for the loaders\n      request = createClientSideRequest(init.history, request.url, request.signal);\n    }\n    // Call loaders\n    let {\n      shortCircuited,\n      matches: updatedMatches,\n      loaderData,\n      errors\n    } = await handleLoaders(request, location, matches, fogOfWar.active, loadingNavigation, opts && opts.submission, opts && opts.fetcherSubmission, opts && opts.replace, opts && opts.initialHydration === true, flushSync, pendingActionResult);\n    if (shortCircuited) {\n      return;\n    }\n    // Clean up now that the action/loaders have completed.  Don't clean up if\n    // we short circuited because pendingNavigationController will have already\n    // been assigned to a new controller for the next navigation\n    pendingNavigationController = null;\n    completeNavigation(location, _extends({\n      matches: updatedMatches || matches\n    }, getActionDataForCommit(pendingActionResult), {\n      loaderData,\n      errors\n    }));\n  }\n  // Call the action matched by the leaf route for this navigation and handle\n  // redirects/errors\n  async function handleAction(request, location, submission, matches, isFogOfWar, opts) {\n    if (opts === void 0) {\n      opts = {};\n    }\n    interruptActiveLoads();\n    // Put us in a submitting state\n    let navigation = getSubmittingNavigation(location, submission);\n    updateState({\n      navigation\n    }, {\n      flushSync: opts.flushSync === true\n    });\n    if (isFogOfWar) {\n      let discoverResult = await discoverRoutes(matches, location.pathname, request.signal);\n      if (discoverResult.type === \"aborted\") {\n        return {\n          shortCircuited: true\n        };\n      } else if (discoverResult.type === \"error\") {\n        let boundaryId = findNearestBoundary(discoverResult.partialMatches).route.id;\n        return {\n          matches: discoverResult.partialMatches,\n          pendingActionResult: [boundaryId, {\n            type: ResultType.error,\n            error: discoverResult.error\n          }]\n        };\n      } else if (!discoverResult.matches) {\n        let {\n          notFoundMatches,\n          error,\n          route\n        } = handleNavigational404(location.pathname);\n        return {\n          matches: notFoundMatches,\n          pendingActionResult: [route.id, {\n            type: ResultType.error,\n            error\n          }]\n        };\n      } else {\n        matches = discoverResult.matches;\n      }\n    }\n    // Call our action and get the result\n    let result;\n    let actionMatch = getTargetMatch(matches, location);\n    if (!actionMatch.route.action && !actionMatch.route.lazy) {\n      result = {\n        type: ResultType.error,\n        error: getInternalRouterError(405, {\n          method: request.method,\n          pathname: location.pathname,\n          routeId: actionMatch.route.id\n        })\n      };\n    } else {\n      let results = await callDataStrategy(\"action\", state, request, [actionMatch], matches, null);\n      result = results[actionMatch.route.id];\n      if (request.signal.aborted) {\n        return {\n          shortCircuited: true\n        };\n      }\n    }\n    if (isRedirectResult(result)) {\n      let replace;\n      if (opts && opts.replace != null) {\n        replace = opts.replace;\n      } else {\n        // If the user didn't explicity indicate replace behavior, replace if\n        // we redirected to the exact same location we're currently at to avoid\n        // double back-buttons\n        let location = normalizeRedirectLocation(result.response.headers.get(\"Location\"), new URL(request.url), basename);\n        replace = location === state.location.pathname + state.location.search;\n      }\n      await startRedirectNavigation(request, result, true, {\n        submission,\n        replace\n      });\n      return {\n        shortCircuited: true\n      };\n    }\n    if (isDeferredResult(result)) {\n      throw getInternalRouterError(400, {\n        type: \"defer-action\"\n      });\n    }\n    if (isErrorResult(result)) {\n      // Store off the pending error - we use it to determine which loaders\n      // to call and will commit it when we complete the navigation\n      let boundaryMatch = findNearestBoundary(matches, actionMatch.route.id);\n      // By default, all submissions to the current location are REPLACE\n      // navigations, but if the action threw an error that'll be rendered in\n      // an errorElement, we fall back to PUSH so that the user can use the\n      // back button to get back to the pre-submission form location to try\n      // again\n      if ((opts && opts.replace) !== true) {\n        pendingAction = Action.Push;\n      }\n      return {\n        matches,\n        pendingActionResult: [boundaryMatch.route.id, result]\n      };\n    }\n    return {\n      matches,\n      pendingActionResult: [actionMatch.route.id, result]\n    };\n  }\n  // Call all applicable loaders for the given matches, handling redirects,\n  // errors, etc.\n  async function handleLoaders(request, location, matches, isFogOfWar, overrideNavigation, submission, fetcherSubmission, replace, initialHydration, flushSync, pendingActionResult) {\n    // Figure out the right navigation we want to use for data loading\n    let loadingNavigation = overrideNavigation || getLoadingNavigation(location, submission);\n    // If this was a redirect from an action we don't have a \"submission\" but\n    // we have it on the loading navigation so use that if available\n    let activeSubmission = submission || fetcherSubmission || getSubmissionFromNavigation(loadingNavigation);\n    // If this is an uninterrupted revalidation, we remain in our current idle\n    // state.  If not, we need to switch to our loading state and load data,\n    // preserving any new action data or existing action data (in the case of\n    // a revalidation interrupting an actionReload)\n    // If we have partialHydration enabled, then don't update the state for the\n    // initial data load since it's not a \"navigation\"\n    let shouldUpdateNavigationState = !isUninterruptedRevalidation && (!future.v7_partialHydration || !initialHydration);\n    // When fog of war is enabled, we enter our `loading` state earlier so we\n    // can discover new routes during the `loading` state.  We skip this if\n    // we've already run actions since we would have done our matching already.\n    // If the children() function threw then, we want to proceed with the\n    // partial matches it discovered.\n    if (isFogOfWar) {\n      if (shouldUpdateNavigationState) {\n        let actionData = getUpdatedActionData(pendingActionResult);\n        updateState(_extends({\n          navigation: loadingNavigation\n        }, actionData !== undefined ? {\n          actionData\n        } : {}), {\n          flushSync\n        });\n      }\n      let discoverResult = await discoverRoutes(matches, location.pathname, request.signal);\n      if (discoverResult.type === \"aborted\") {\n        return {\n          shortCircuited: true\n        };\n      } else if (discoverResult.type === \"error\") {\n        let boundaryId = findNearestBoundary(discoverResult.partialMatches).route.id;\n        return {\n          matches: discoverResult.partialMatches,\n          loaderData: {},\n          errors: {\n            [boundaryId]: discoverResult.error\n          }\n        };\n      } else if (!discoverResult.matches) {\n        let {\n          error,\n          notFoundMatches,\n          route\n        } = handleNavigational404(location.pathname);\n        return {\n          matches: notFoundMatches,\n          loaderData: {},\n          errors: {\n            [route.id]: error\n          }\n        };\n      } else {\n        matches = discoverResult.matches;\n      }\n    }\n    let routesToUse = inFlightDataRoutes || dataRoutes;\n    let [matchesToLoad, revalidatingFetchers] = getMatchesToLoad(init.history, state, matches, activeSubmission, location, future.v7_partialHydration && initialHydration === true, future.v7_skipActionErrorRevalidation, isRevalidationRequired, cancelledDeferredRoutes, cancelledFetcherLoads, deletedFetchers, fetchLoadMatches, fetchRedirectIds, routesToUse, basename, pendingActionResult);\n    // Cancel pending deferreds for no-longer-matched routes or routes we're\n    // about to reload.  Note that if this is an action reload we would have\n    // already cancelled all pending deferreds so this would be a no-op\n    cancelActiveDeferreds(routeId => !(matches && matches.some(m => m.route.id === routeId)) || matchesToLoad && matchesToLoad.some(m => m.route.id === routeId));\n    pendingNavigationLoadId = ++incrementingLoadId;\n    // Short circuit if we have no loaders to run\n    if (matchesToLoad.length === 0 && revalidatingFetchers.length === 0) {\n      let updatedFetchers = markFetchRedirectsDone();\n      completeNavigation(location, _extends({\n        matches,\n        loaderData: {},\n        // Commit pending error if we're short circuiting\n        errors: pendingActionResult && isErrorResult(pendingActionResult[1]) ? {\n          [pendingActionResult[0]]: pendingActionResult[1].error\n        } : null\n      }, getActionDataForCommit(pendingActionResult), updatedFetchers ? {\n        fetchers: new Map(state.fetchers)\n      } : {}), {\n        flushSync\n      });\n      return {\n        shortCircuited: true\n      };\n    }\n    if (shouldUpdateNavigationState) {\n      let updates = {};\n      if (!isFogOfWar) {\n        // Only update navigation/actionNData if we didn't already do it above\n        updates.navigation = loadingNavigation;\n        let actionData = getUpdatedActionData(pendingActionResult);\n        if (actionData !== undefined) {\n          updates.actionData = actionData;\n        }\n      }\n      if (revalidatingFetchers.length > 0) {\n        updates.fetchers = getUpdatedRevalidatingFetchers(revalidatingFetchers);\n      }\n      updateState(updates, {\n        flushSync\n      });\n    }\n    revalidatingFetchers.forEach(rf => {\n      abortFetcher(rf.key);\n      if (rf.controller) {\n        // Fetchers use an independent AbortController so that aborting a fetcher\n        // (via deleteFetcher) does not abort the triggering navigation that\n        // triggered the revalidation\n        fetchControllers.set(rf.key, rf.controller);\n      }\n    });\n    // Proxy navigation abort through to revalidation fetchers\n    let abortPendingFetchRevalidations = () => revalidatingFetchers.forEach(f => abortFetcher(f.key));\n    if (pendingNavigationController) {\n      pendingNavigationController.signal.addEventListener(\"abort\", abortPendingFetchRevalidations);\n    }\n    let {\n      loaderResults,\n      fetcherResults\n    } = await callLoadersAndMaybeResolveData(state, matches, matchesToLoad, revalidatingFetchers, request);\n    if (request.signal.aborted) {\n      return {\n        shortCircuited: true\n      };\n    }\n    // Clean up _after_ loaders have completed.  Don't clean up if we short\n    // circuited because fetchControllers would have been aborted and\n    // reassigned to new controllers for the next navigation\n    if (pendingNavigationController) {\n      pendingNavigationController.signal.removeEventListener(\"abort\", abortPendingFetchRevalidations);\n    }\n    revalidatingFetchers.forEach(rf => fetchControllers.delete(rf.key));\n    // If any loaders returned a redirect Response, start a new REPLACE navigation\n    let redirect = findRedirect(loaderResults);\n    if (redirect) {\n      await startRedirectNavigation(request, redirect.result, true, {\n        replace\n      });\n      return {\n        shortCircuited: true\n      };\n    }\n    redirect = findRedirect(fetcherResults);\n    if (redirect) {\n      // If this redirect came from a fetcher make sure we mark it in\n      // fetchRedirectIds so it doesn't get revalidated on the next set of\n      // loader executions\n      fetchRedirectIds.add(redirect.key);\n      await startRedirectNavigation(request, redirect.result, true, {\n        replace\n      });\n      return {\n        shortCircuited: true\n      };\n    }\n    // Process and commit output from loaders\n    let {\n      loaderData,\n      errors\n    } = processLoaderData(state, matches, loaderResults, pendingActionResult, revalidatingFetchers, fetcherResults, activeDeferreds);\n    // Wire up subscribers to update loaderData as promises settle\n    activeDeferreds.forEach((deferredData, routeId) => {\n      deferredData.subscribe(aborted => {\n        // Note: No need to updateState here since the TrackedPromise on\n        // loaderData is stable across resolve/reject\n        // Remove this instance if we were aborted or if promises have settled\n        if (aborted || deferredData.done) {\n          activeDeferreds.delete(routeId);\n        }\n      });\n    });\n    // Preserve SSR errors during partial hydration\n    if (future.v7_partialHydration && initialHydration && state.errors) {\n      errors = _extends({}, state.errors, errors);\n    }\n    let updatedFetchers = markFetchRedirectsDone();\n    let didAbortFetchLoads = abortStaleFetchLoads(pendingNavigationLoadId);\n    let shouldUpdateFetchers = updatedFetchers || didAbortFetchLoads || revalidatingFetchers.length > 0;\n    return _extends({\n      matches,\n      loaderData,\n      errors\n    }, shouldUpdateFetchers ? {\n      fetchers: new Map(state.fetchers)\n    } : {});\n  }\n  function getUpdatedActionData(pendingActionResult) {\n    if (pendingActionResult && !isErrorResult(pendingActionResult[1])) {\n      // This is cast to `any` currently because `RouteData`uses any and it\n      // would be a breaking change to use any.\n      // TODO: v7 - change `RouteData` to use `unknown` instead of `any`\n      return {\n        [pendingActionResult[0]]: pendingActionResult[1].data\n      };\n    } else if (state.actionData) {\n      if (Object.keys(state.actionData).length === 0) {\n        return null;\n      } else {\n        return state.actionData;\n      }\n    }\n  }\n  function getUpdatedRevalidatingFetchers(revalidatingFetchers) {\n    revalidatingFetchers.forEach(rf => {\n      let fetcher = state.fetchers.get(rf.key);\n      let revalidatingFetcher = getLoadingFetcher(undefined, fetcher ? fetcher.data : undefined);\n      state.fetchers.set(rf.key, revalidatingFetcher);\n    });\n    return new Map(state.fetchers);\n  }\n  // Trigger a fetcher load/submit for the given fetcher key\n  function fetch(key, routeId, href, opts) {\n    if (isServer) {\n      throw new Error(\"router.fetch() was called during the server render, but it shouldn't be. \" + \"You are likely calling a useFetcher() method in the body of your component. \" + \"Try moving it to a useEffect or a callback.\");\n    }\n    abortFetcher(key);\n    let flushSync = (opts && opts.flushSync) === true;\n    let routesToUse = inFlightDataRoutes || dataRoutes;\n    let normalizedPath = normalizeTo(state.location, state.matches, basename, future.v7_prependBasename, href, future.v7_relativeSplatPath, routeId, opts == null ? void 0 : opts.relative);\n    let matches = matchRoutes(routesToUse, normalizedPath, basename);\n    let fogOfWar = checkFogOfWar(matches, routesToUse, normalizedPath);\n    if (fogOfWar.active && fogOfWar.matches) {\n      matches = fogOfWar.matches;\n    }\n    if (!matches) {\n      setFetcherError(key, routeId, getInternalRouterError(404, {\n        pathname: normalizedPath\n      }), {\n        flushSync\n      });\n      return;\n    }\n    let {\n      path,\n      submission,\n      error\n    } = normalizeNavigateOptions(future.v7_normalizeFormMethod, true, normalizedPath, opts);\n    if (error) {\n      setFetcherError(key, routeId, error, {\n        flushSync\n      });\n      return;\n    }\n    let match = getTargetMatch(matches, path);\n    let preventScrollReset = (opts && opts.preventScrollReset) === true;\n    if (submission && isMutationMethod(submission.formMethod)) {\n      handleFetcherAction(key, routeId, path, match, matches, fogOfWar.active, flushSync, preventScrollReset, submission);\n      return;\n    }\n    // Store off the match so we can call it's shouldRevalidate on subsequent\n    // revalidations\n    fetchLoadMatches.set(key, {\n      routeId,\n      path\n    });\n    handleFetcherLoader(key, routeId, path, match, matches, fogOfWar.active, flushSync, preventScrollReset, submission);\n  }\n  // Call the action for the matched fetcher.submit(), and then handle redirects,\n  // errors, and revalidation\n  async function handleFetcherAction(key, routeId, path, match, requestMatches, isFogOfWar, flushSync, preventScrollReset, submission) {\n    interruptActiveLoads();\n    fetchLoadMatches.delete(key);\n    function detectAndHandle405Error(m) {\n      if (!m.route.action && !m.route.lazy) {\n        let error = getInternalRouterError(405, {\n          method: submission.formMethod,\n          pathname: path,\n          routeId: routeId\n        });\n        setFetcherError(key, routeId, error, {\n          flushSync\n        });\n        return true;\n      }\n      return false;\n    }\n    if (!isFogOfWar && detectAndHandle405Error(match)) {\n      return;\n    }\n    // Put this fetcher into it's submitting state\n    let existingFetcher = state.fetchers.get(key);\n    updateFetcherState(key, getSubmittingFetcher(submission, existingFetcher), {\n      flushSync\n    });\n    let abortController = new AbortController();\n    let fetchRequest = createClientSideRequest(init.history, path, abortController.signal, submission);\n    if (isFogOfWar) {\n      let discoverResult = await discoverRoutes(requestMatches, new URL(fetchRequest.url).pathname, fetchRequest.signal);\n      if (discoverResult.type === \"aborted\") {\n        return;\n      } else if (discoverResult.type === \"error\") {\n        setFetcherError(key, routeId, discoverResult.error, {\n          flushSync\n        });\n        return;\n      } else if (!discoverResult.matches) {\n        setFetcherError(key, routeId, getInternalRouterError(404, {\n          pathname: path\n        }), {\n          flushSync\n        });\n        return;\n      } else {\n        requestMatches = discoverResult.matches;\n        match = getTargetMatch(requestMatches, path);\n        if (detectAndHandle405Error(match)) {\n          return;\n        }\n      }\n    }\n    // Call the action for the fetcher\n    fetchControllers.set(key, abortController);\n    let originatingLoadId = incrementingLoadId;\n    let actionResults = await callDataStrategy(\"action\", state, fetchRequest, [match], requestMatches, key);\n    let actionResult = actionResults[match.route.id];\n    if (fetchRequest.signal.aborted) {\n      // We can delete this so long as we weren't aborted by our own fetcher\n      // re-submit which would have put _new_ controller is in fetchControllers\n      if (fetchControllers.get(key) === abortController) {\n        fetchControllers.delete(key);\n      }\n      return;\n    }\n    // When using v7_fetcherPersist, we don't want errors bubbling up to the UI\n    // or redirects processed for unmounted fetchers so we just revert them to\n    // idle\n    if (future.v7_fetcherPersist && deletedFetchers.has(key)) {\n      if (isRedirectResult(actionResult) || isErrorResult(actionResult)) {\n        updateFetcherState(key, getDoneFetcher(undefined));\n        return;\n      }\n      // Let SuccessResult's fall through for revalidation\n    } else {\n      if (isRedirectResult(actionResult)) {\n        fetchControllers.delete(key);\n        if (pendingNavigationLoadId > originatingLoadId) {\n          // A new navigation was kicked off after our action started, so that\n          // should take precedence over this redirect navigation.  We already\n          // set isRevalidationRequired so all loaders for the new route should\n          // fire unless opted out via shouldRevalidate\n          updateFetcherState(key, getDoneFetcher(undefined));\n          return;\n        } else {\n          fetchRedirectIds.add(key);\n          updateFetcherState(key, getLoadingFetcher(submission));\n          return startRedirectNavigation(fetchRequest, actionResult, false, {\n            fetcherSubmission: submission,\n            preventScrollReset\n          });\n        }\n      }\n      // Process any non-redirect errors thrown\n      if (isErrorResult(actionResult)) {\n        setFetcherError(key, routeId, actionResult.error);\n        return;\n      }\n    }\n    if (isDeferredResult(actionResult)) {\n      throw getInternalRouterError(400, {\n        type: \"defer-action\"\n      });\n    }\n    // Start the data load for current matches, or the next location if we're\n    // in the middle of a navigation\n    let nextLocation = state.navigation.location || state.location;\n    let revalidationRequest = createClientSideRequest(init.history, nextLocation, abortController.signal);\n    let routesToUse = inFlightDataRoutes || dataRoutes;\n    let matches = state.navigation.state !== \"idle\" ? matchRoutes(routesToUse, state.navigation.location, basename) : state.matches;\n    invariant(matches, \"Didn't find any matches after fetcher action\");\n    let loadId = ++incrementingLoadId;\n    fetchReloadIds.set(key, loadId);\n    let loadFetcher = getLoadingFetcher(submission, actionResult.data);\n    state.fetchers.set(key, loadFetcher);\n    let [matchesToLoad, revalidatingFetchers] = getMatchesToLoad(init.history, state, matches, submission, nextLocation, false, future.v7_skipActionErrorRevalidation, isRevalidationRequired, cancelledDeferredRoutes, cancelledFetcherLoads, deletedFetchers, fetchLoadMatches, fetchRedirectIds, routesToUse, basename, [match.route.id, actionResult]);\n    // Put all revalidating fetchers into the loading state, except for the\n    // current fetcher which we want to keep in it's current loading state which\n    // contains it's action submission info + action data\n    revalidatingFetchers.filter(rf => rf.key !== key).forEach(rf => {\n      let staleKey = rf.key;\n      let existingFetcher = state.fetchers.get(staleKey);\n      let revalidatingFetcher = getLoadingFetcher(undefined, existingFetcher ? existingFetcher.data : undefined);\n      state.fetchers.set(staleKey, revalidatingFetcher);\n      abortFetcher(staleKey);\n      if (rf.controller) {\n        fetchControllers.set(staleKey, rf.controller);\n      }\n    });\n    updateState({\n      fetchers: new Map(state.fetchers)\n    });\n    let abortPendingFetchRevalidations = () => revalidatingFetchers.forEach(rf => abortFetcher(rf.key));\n    abortController.signal.addEventListener(\"abort\", abortPendingFetchRevalidations);\n    let {\n      loaderResults,\n      fetcherResults\n    } = await callLoadersAndMaybeResolveData(state, matches, matchesToLoad, revalidatingFetchers, revalidationRequest);\n    if (abortController.signal.aborted) {\n      return;\n    }\n    abortController.signal.removeEventListener(\"abort\", abortPendingFetchRevalidations);\n    fetchReloadIds.delete(key);\n    fetchControllers.delete(key);\n    revalidatingFetchers.forEach(r => fetchControllers.delete(r.key));\n    let redirect = findRedirect(loaderResults);\n    if (redirect) {\n      return startRedirectNavigation(revalidationRequest, redirect.result, false, {\n        preventScrollReset\n      });\n    }\n    redirect = findRedirect(fetcherResults);\n    if (redirect) {\n      // If this redirect came from a fetcher make sure we mark it in\n      // fetchRedirectIds so it doesn't get revalidated on the next set of\n      // loader executions\n      fetchRedirectIds.add(redirect.key);\n      return startRedirectNavigation(revalidationRequest, redirect.result, false, {\n        preventScrollReset\n      });\n    }\n    // Process and commit output from loaders\n    let {\n      loaderData,\n      errors\n    } = processLoaderData(state, matches, loaderResults, undefined, revalidatingFetchers, fetcherResults, activeDeferreds);\n    // Since we let revalidations complete even if the submitting fetcher was\n    // deleted, only put it back to idle if it hasn't been deleted\n    if (state.fetchers.has(key)) {\n      let doneFetcher = getDoneFetcher(actionResult.data);\n      state.fetchers.set(key, doneFetcher);\n    }\n    abortStaleFetchLoads(loadId);\n    // If we are currently in a navigation loading state and this fetcher is\n    // more recent than the navigation, we want the newer data so abort the\n    // navigation and complete it with the fetcher data\n    if (state.navigation.state === \"loading\" && loadId > pendingNavigationLoadId) {\n      invariant(pendingAction, \"Expected pending action\");\n      pendingNavigationController && pendingNavigationController.abort();\n      completeNavigation(state.navigation.location, {\n        matches,\n        loaderData,\n        errors,\n        fetchers: new Map(state.fetchers)\n      });\n    } else {\n      // otherwise just update with the fetcher data, preserving any existing\n      // loaderData for loaders that did not need to reload.  We have to\n      // manually merge here since we aren't going through completeNavigation\n      updateState({\n        errors,\n        loaderData: mergeLoaderData(state.loaderData, loaderData, matches, errors),\n        fetchers: new Map(state.fetchers)\n      });\n      isRevalidationRequired = false;\n    }\n  }\n  // Call the matched loader for fetcher.load(), handling redirects, errors, etc.\n  async function handleFetcherLoader(key, routeId, path, match, matches, isFogOfWar, flushSync, preventScrollReset, submission) {\n    let existingFetcher = state.fetchers.get(key);\n    updateFetcherState(key, getLoadingFetcher(submission, existingFetcher ? existingFetcher.data : undefined), {\n      flushSync\n    });\n    let abortController = new AbortController();\n    let fetchRequest = createClientSideRequest(init.history, path, abortController.signal);\n    if (isFogOfWar) {\n      let discoverResult = await discoverRoutes(matches, new URL(fetchRequest.url).pathname, fetchRequest.signal);\n      if (discoverResult.type === \"aborted\") {\n        return;\n      } else if (discoverResult.type === \"error\") {\n        setFetcherError(key, routeId, discoverResult.error, {\n          flushSync\n        });\n        return;\n      } else if (!discoverResult.matches) {\n        setFetcherError(key, routeId, getInternalRouterError(404, {\n          pathname: path\n        }), {\n          flushSync\n        });\n        return;\n      } else {\n        matches = discoverResult.matches;\n        match = getTargetMatch(matches, path);\n      }\n    }\n    // Call the loader for this fetcher route match\n    fetchControllers.set(key, abortController);\n    let originatingLoadId = incrementingLoadId;\n    let results = await callDataStrategy(\"loader\", state, fetchRequest, [match], matches, key);\n    let result = results[match.route.id];\n    // Deferred isn't supported for fetcher loads, await everything and treat it\n    // as a normal load.  resolveDeferredData will return undefined if this\n    // fetcher gets aborted, so we just leave result untouched and short circuit\n    // below if that happens\n    if (isDeferredResult(result)) {\n      result = (await resolveDeferredData(result, fetchRequest.signal, true)) || result;\n    }\n    // We can delete this so long as we weren't aborted by our our own fetcher\n    // re-load which would have put _new_ controller is in fetchControllers\n    if (fetchControllers.get(key) === abortController) {\n      fetchControllers.delete(key);\n    }\n    if (fetchRequest.signal.aborted) {\n      return;\n    }\n    // We don't want errors bubbling up or redirects followed for unmounted\n    // fetchers, so short circuit here if it was removed from the UI\n    if (deletedFetchers.has(key)) {\n      updateFetcherState(key, getDoneFetcher(undefined));\n      return;\n    }\n    // If the loader threw a redirect Response, start a new REPLACE navigation\n    if (isRedirectResult(result)) {\n      if (pendingNavigationLoadId > originatingLoadId) {\n        // A new navigation was kicked off after our loader started, so that\n        // should take precedence over this redirect navigation\n        updateFetcherState(key, getDoneFetcher(undefined));\n        return;\n      } else {\n        fetchRedirectIds.add(key);\n        await startRedirectNavigation(fetchRequest, result, false, {\n          preventScrollReset\n        });\n        return;\n      }\n    }\n    // Process any non-redirect errors thrown\n    if (isErrorResult(result)) {\n      setFetcherError(key, routeId, result.error);\n      return;\n    }\n    invariant(!isDeferredResult(result), \"Unhandled fetcher deferred data\");\n    // Put the fetcher back into an idle state\n    updateFetcherState(key, getDoneFetcher(result.data));\n  }\n  /**\n   * Utility function to handle redirects returned from an action or loader.\n   * Normally, a redirect \"replaces\" the navigation that triggered it.  So, for\n   * example:\n   *\n   *  - user is on /a\n   *  - user clicks a link to /b\n   *  - loader for /b redirects to /c\n   *\n   * In a non-JS app the browser would track the in-flight navigation to /b and\n   * then replace it with /c when it encountered the redirect response.  In\n   * the end it would only ever update the URL bar with /c.\n   *\n   * In client-side routing using pushState/replaceState, we aim to emulate\n   * this behavior and we also do not update history until the end of the\n   * navigation (including processed redirects).  This means that we never\n   * actually touch history until we've processed redirects, so we just use\n   * the history action from the original navigation (PUSH or REPLACE).\n   */\n  async function startRedirectNavigation(request, redirect, isNavigation, _temp2) {\n    let {\n      submission,\n      fetcherSubmission,\n      preventScrollReset,\n      replace\n    } = _temp2 === void 0 ? {} : _temp2;\n    if (redirect.response.headers.has(\"X-Remix-Revalidate\")) {\n      isRevalidationRequired = true;\n    }\n    let location = redirect.response.headers.get(\"Location\");\n    invariant(location, \"Expected a Location header on the redirect Response\");\n    location = normalizeRedirectLocation(location, new URL(request.url), basename);\n    let redirectLocation = createLocation(state.location, location, {\n      _isRedirect: true\n    });\n    if (isBrowser) {\n      let isDocumentReload = false;\n      if (redirect.response.headers.has(\"X-Remix-Reload-Document\")) {\n        // Hard reload if the response contained X-Remix-Reload-Document\n        isDocumentReload = true;\n      } else if (ABSOLUTE_URL_REGEX.test(location)) {\n        const url = init.history.createURL(location);\n        isDocumentReload =\n        // Hard reload if it's an absolute URL to a new origin\n        url.origin !== routerWindow.location.origin ||\n        // Hard reload if it's an absolute URL that does not match our basename\n        stripBasename(url.pathname, basename) == null;\n      }\n      if (isDocumentReload) {\n        if (replace) {\n          routerWindow.location.replace(location);\n        } else {\n          routerWindow.location.assign(location);\n        }\n        return;\n      }\n    }\n    // There's no need to abort on redirects, since we don't detect the\n    // redirect until the action/loaders have settled\n    pendingNavigationController = null;\n    let redirectHistoryAction = replace === true || redirect.response.headers.has(\"X-Remix-Replace\") ? Action.Replace : Action.Push;\n    // Use the incoming submission if provided, fallback on the active one in\n    // state.navigation\n    let {\n      formMethod,\n      formAction,\n      formEncType\n    } = state.navigation;\n    if (!submission && !fetcherSubmission && formMethod && formAction && formEncType) {\n      submission = getSubmissionFromNavigation(state.navigation);\n    }\n    // If this was a 307/308 submission we want to preserve the HTTP method and\n    // re-submit the GET/POST/PUT/PATCH/DELETE as a submission navigation to the\n    // redirected location\n    let activeSubmission = submission || fetcherSubmission;\n    if (redirectPreserveMethodStatusCodes.has(redirect.response.status) && activeSubmission && isMutationMethod(activeSubmission.formMethod)) {\n      await startNavigation(redirectHistoryAction, redirectLocation, {\n        submission: _extends({}, activeSubmission, {\n          formAction: location\n        }),\n        // Preserve these flags across redirects\n        preventScrollReset: preventScrollReset || pendingPreventScrollReset,\n        enableViewTransition: isNavigation ? pendingViewTransitionEnabled : undefined\n      });\n    } else {\n      // If we have a navigation submission, we will preserve it through the\n      // redirect navigation\n      let overrideNavigation = getLoadingNavigation(redirectLocation, submission);\n      await startNavigation(redirectHistoryAction, redirectLocation, {\n        overrideNavigation,\n        // Send fetcher submissions through for shouldRevalidate\n        fetcherSubmission,\n        // Preserve these flags across redirects\n        preventScrollReset: preventScrollReset || pendingPreventScrollReset,\n        enableViewTransition: isNavigation ? pendingViewTransitionEnabled : undefined\n      });\n    }\n  }\n  // Utility wrapper for calling dataStrategy client-side without having to\n  // pass around the manifest, mapRouteProperties, etc.\n  async function callDataStrategy(type, state, request, matchesToLoad, matches, fetcherKey) {\n    let results;\n    let dataResults = {};\n    try {\n      results = await callDataStrategyImpl(dataStrategyImpl, type, state, request, matchesToLoad, matches, fetcherKey, manifest, mapRouteProperties);\n    } catch (e) {\n      // If the outer dataStrategy method throws, just return the error for all\n      // matches - and it'll naturally bubble to the root\n      matchesToLoad.forEach(m => {\n        dataResults[m.route.id] = {\n          type: ResultType.error,\n          error: e\n        };\n      });\n      return dataResults;\n    }\n    for (let [routeId, result] of Object.entries(results)) {\n      if (isRedirectDataStrategyResultResult(result)) {\n        let response = result.result;\n        dataResults[routeId] = {\n          type: ResultType.redirect,\n          response: normalizeRelativeRoutingRedirectResponse(response, request, routeId, matches, basename, future.v7_relativeSplatPath)\n        };\n      } else {\n        dataResults[routeId] = await convertDataStrategyResultToDataResult(result);\n      }\n    }\n    return dataResults;\n  }\n  async function callLoadersAndMaybeResolveData(state, matches, matchesToLoad, fetchersToLoad, request) {\n    let currentMatches = state.matches;\n    // Kick off loaders and fetchers in parallel\n    let loaderResultsPromise = callDataStrategy(\"loader\", state, request, matchesToLoad, matches, null);\n    let fetcherResultsPromise = Promise.all(fetchersToLoad.map(async f => {\n      if (f.matches && f.match && f.controller) {\n        let results = await callDataStrategy(\"loader\", state, createClientSideRequest(init.history, f.path, f.controller.signal), [f.match], f.matches, f.key);\n        let result = results[f.match.route.id];\n        // Fetcher results are keyed by fetcher key from here on out, not routeId\n        return {\n          [f.key]: result\n        };\n      } else {\n        return Promise.resolve({\n          [f.key]: {\n            type: ResultType.error,\n            error: getInternalRouterError(404, {\n              pathname: f.path\n            })\n          }\n        });\n      }\n    }));\n    let loaderResults = await loaderResultsPromise;\n    let fetcherResults = (await fetcherResultsPromise).reduce((acc, r) => Object.assign(acc, r), {});\n    await Promise.all([resolveNavigationDeferredResults(matches, loaderResults, request.signal, currentMatches, state.loaderData), resolveFetcherDeferredResults(matches, fetcherResults, fetchersToLoad)]);\n    return {\n      loaderResults,\n      fetcherResults\n    };\n  }\n  function interruptActiveLoads() {\n    // Every interruption triggers a revalidation\n    isRevalidationRequired = true;\n    // Cancel pending route-level deferreds and mark cancelled routes for\n    // revalidation\n    cancelledDeferredRoutes.push(...cancelActiveDeferreds());\n    // Abort in-flight fetcher loads\n    fetchLoadMatches.forEach((_, key) => {\n      if (fetchControllers.has(key)) {\n        cancelledFetcherLoads.add(key);\n      }\n      abortFetcher(key);\n    });\n  }\n  function updateFetcherState(key, fetcher, opts) {\n    if (opts === void 0) {\n      opts = {};\n    }\n    state.fetchers.set(key, fetcher);\n    updateState({\n      fetchers: new Map(state.fetchers)\n    }, {\n      flushSync: (opts && opts.flushSync) === true\n    });\n  }\n  function setFetcherError(key, routeId, error, opts) {\n    if (opts === void 0) {\n      opts = {};\n    }\n    let boundaryMatch = findNearestBoundary(state.matches, routeId);\n    deleteFetcher(key);\n    updateState({\n      errors: {\n        [boundaryMatch.route.id]: error\n      },\n      fetchers: new Map(state.fetchers)\n    }, {\n      flushSync: (opts && opts.flushSync) === true\n    });\n  }\n  function getFetcher(key) {\n    activeFetchers.set(key, (activeFetchers.get(key) || 0) + 1);\n    // If this fetcher was previously marked for deletion, unmark it since we\n    // have a new instance\n    if (deletedFetchers.has(key)) {\n      deletedFetchers.delete(key);\n    }\n    return state.fetchers.get(key) || IDLE_FETCHER;\n  }\n  function deleteFetcher(key) {\n    let fetcher = state.fetchers.get(key);\n    // Don't abort the controller if this is a deletion of a fetcher.submit()\n    // in it's loading phase since - we don't want to abort the corresponding\n    // revalidation and want them to complete and land\n    if (fetchControllers.has(key) && !(fetcher && fetcher.state === \"loading\" && fetchReloadIds.has(key))) {\n      abortFetcher(key);\n    }\n    fetchLoadMatches.delete(key);\n    fetchReloadIds.delete(key);\n    fetchRedirectIds.delete(key);\n    // If we opted into the flag we can clear this now since we're calling\n    // deleteFetcher() at the end of updateState() and we've already handed the\n    // deleted fetcher keys off to the data layer.\n    // If not, we're eagerly calling deleteFetcher() and we need to keep this\n    // Set populated until the next updateState call, and we'll clear\n    // `deletedFetchers` then\n    if (future.v7_fetcherPersist) {\n      deletedFetchers.delete(key);\n    }\n    cancelledFetcherLoads.delete(key);\n    state.fetchers.delete(key);\n  }\n  function deleteFetcherAndUpdateState(key) {\n    let count = (activeFetchers.get(key) || 0) - 1;\n    if (count <= 0) {\n      activeFetchers.delete(key);\n      deletedFetchers.add(key);\n      if (!future.v7_fetcherPersist) {\n        deleteFetcher(key);\n      }\n    } else {\n      activeFetchers.set(key, count);\n    }\n    updateState({\n      fetchers: new Map(state.fetchers)\n    });\n  }\n  function abortFetcher(key) {\n    let controller = fetchControllers.get(key);\n    if (controller) {\n      controller.abort();\n      fetchControllers.delete(key);\n    }\n  }\n  function markFetchersDone(keys) {\n    for (let key of keys) {\n      let fetcher = getFetcher(key);\n      let doneFetcher = getDoneFetcher(fetcher.data);\n      state.fetchers.set(key, doneFetcher);\n    }\n  }\n  function markFetchRedirectsDone() {\n    let doneKeys = [];\n    let updatedFetchers = false;\n    for (let key of fetchRedirectIds) {\n      let fetcher = state.fetchers.get(key);\n      invariant(fetcher, \"Expected fetcher: \" + key);\n      if (fetcher.state === \"loading\") {\n        fetchRedirectIds.delete(key);\n        doneKeys.push(key);\n        updatedFetchers = true;\n      }\n    }\n    markFetchersDone(doneKeys);\n    return updatedFetchers;\n  }\n  function abortStaleFetchLoads(landedId) {\n    let yeetedKeys = [];\n    for (let [key, id] of fetchReloadIds) {\n      if (id < landedId) {\n        let fetcher = state.fetchers.get(key);\n        invariant(fetcher, \"Expected fetcher: \" + key);\n        if (fetcher.state === \"loading\") {\n          abortFetcher(key);\n          fetchReloadIds.delete(key);\n          yeetedKeys.push(key);\n        }\n      }\n    }\n    markFetchersDone(yeetedKeys);\n    return yeetedKeys.length > 0;\n  }\n  function getBlocker(key, fn) {\n    let blocker = state.blockers.get(key) || IDLE_BLOCKER;\n    if (blockerFunctions.get(key) !== fn) {\n      blockerFunctions.set(key, fn);\n    }\n    return blocker;\n  }\n  function deleteBlocker(key) {\n    state.blockers.delete(key);\n    blockerFunctions.delete(key);\n  }\n  // Utility function to update blockers, ensuring valid state transitions\n  function updateBlocker(key, newBlocker) {\n    let blocker = state.blockers.get(key) || IDLE_BLOCKER;\n    // Poor mans state machine :)\n    // https://mermaid.live/edit#pako:eNqVkc9OwzAMxl8l8nnjAYrEtDIOHEBIgwvKJTReGy3_lDpIqO27k6awMG0XcrLlnz87nwdonESogKXXBuE79rq75XZO3-yHds0RJVuv70YrPlUrCEe2HfrORS3rubqZfuhtpg5C9wk5tZ4VKcRUq88q9Z8RS0-48cE1iHJkL0ugbHuFLus9L6spZy8nX9MP2CNdomVaposqu3fGayT8T8-jJQwhepo_UtpgBQaDEUom04dZhAN1aJBDlUKJBxE1ceB2Smj0Mln-IBW5AFU2dwUiktt_2Qaq2dBfaKdEup85UV7Yd-dKjlnkabl2Pvr0DTkTreM\n    invariant(blocker.state === \"unblocked\" && newBlocker.state === \"blocked\" || blocker.state === \"blocked\" && newBlocker.state === \"blocked\" || blocker.state === \"blocked\" && newBlocker.state === \"proceeding\" || blocker.state === \"blocked\" && newBlocker.state === \"unblocked\" || blocker.state === \"proceeding\" && newBlocker.state === \"unblocked\", \"Invalid blocker state transition: \" + blocker.state + \" -> \" + newBlocker.state);\n    let blockers = new Map(state.blockers);\n    blockers.set(key, newBlocker);\n    updateState({\n      blockers\n    });\n  }\n  function shouldBlockNavigation(_ref2) {\n    let {\n      currentLocation,\n      nextLocation,\n      historyAction\n    } = _ref2;\n    if (blockerFunctions.size === 0) {\n      return;\n    }\n    // We ony support a single active blocker at the moment since we don't have\n    // any compelling use cases for multi-blocker yet\n    if (blockerFunctions.size > 1) {\n      warning(false, \"A router only supports one blocker at a time\");\n    }\n    let entries = Array.from(blockerFunctions.entries());\n    let [blockerKey, blockerFunction] = entries[entries.length - 1];\n    let blocker = state.blockers.get(blockerKey);\n    if (blocker && blocker.state === \"proceeding\") {\n      // If the blocker is currently proceeding, we don't need to re-check\n      // it and can let this navigation continue\n      return;\n    }\n    // At this point, we know we're unblocked/blocked so we need to check the\n    // user-provided blocker function\n    if (blockerFunction({\n      currentLocation,\n      nextLocation,\n      historyAction\n    })) {\n      return blockerKey;\n    }\n  }\n  function handleNavigational404(pathname) {\n    let error = getInternalRouterError(404, {\n      pathname\n    });\n    let routesToUse = inFlightDataRoutes || dataRoutes;\n    let {\n      matches,\n      route\n    } = getShortCircuitMatches(routesToUse);\n    // Cancel all pending deferred on 404s since we don't keep any routes\n    cancelActiveDeferreds();\n    return {\n      notFoundMatches: matches,\n      route,\n      error\n    };\n  }\n  function cancelActiveDeferreds(predicate) {\n    let cancelledRouteIds = [];\n    activeDeferreds.forEach((dfd, routeId) => {\n      if (!predicate || predicate(routeId)) {\n        // Cancel the deferred - but do not remove from activeDeferreds here -\n        // we rely on the subscribers to do that so our tests can assert proper\n        // cleanup via _internalActiveDeferreds\n        dfd.cancel();\n        cancelledRouteIds.push(routeId);\n        activeDeferreds.delete(routeId);\n      }\n    });\n    return cancelledRouteIds;\n  }\n  // Opt in to capturing and reporting scroll positions during navigations,\n  // used by the <ScrollRestoration> component\n  function enableScrollRestoration(positions, getPosition, getKey) {\n    savedScrollPositions = positions;\n    getScrollPosition = getPosition;\n    getScrollRestorationKey = getKey || null;\n    // Perform initial hydration scroll restoration, since we miss the boat on\n    // the initial updateState() because we've not yet rendered <ScrollRestoration/>\n    // and therefore have no savedScrollPositions available\n    if (!initialScrollRestored && state.navigation === IDLE_NAVIGATION) {\n      initialScrollRestored = true;\n      let y = getSavedScrollPosition(state.location, state.matches);\n      if (y != null) {\n        updateState({\n          restoreScrollPosition: y\n        });\n      }\n    }\n    return () => {\n      savedScrollPositions = null;\n      getScrollPosition = null;\n      getScrollRestorationKey = null;\n    };\n  }\n  function getScrollKey(location, matches) {\n    if (getScrollRestorationKey) {\n      let key = getScrollRestorationKey(location, matches.map(m => convertRouteMatchToUiMatch(m, state.loaderData)));\n      return key || location.key;\n    }\n    return location.key;\n  }\n  function saveScrollPosition(location, matches) {\n    if (savedScrollPositions && getScrollPosition) {\n      let key = getScrollKey(location, matches);\n      savedScrollPositions[key] = getScrollPosition();\n    }\n  }\n  function getSavedScrollPosition(location, matches) {\n    if (savedScrollPositions) {\n      let key = getScrollKey(location, matches);\n      let y = savedScrollPositions[key];\n      if (typeof y === \"number\") {\n        return y;\n      }\n    }\n    return null;\n  }\n  function checkFogOfWar(matches, routesToUse, pathname) {\n    if (patchRoutesOnNavigationImpl) {\n      if (!matches) {\n        let fogMatches = matchRoutesImpl(routesToUse, pathname, basename, true);\n        return {\n          active: true,\n          matches: fogMatches || []\n        };\n      } else {\n        if (Object.keys(matches[0].params).length > 0) {\n          // If we matched a dynamic param or a splat, it might only be because\n          // we haven't yet discovered other routes that would match with a\n          // higher score.  Call patchRoutesOnNavigation just to be sure\n          let partialMatches = matchRoutesImpl(routesToUse, pathname, basename, true);\n          return {\n            active: true,\n            matches: partialMatches\n          };\n        }\n      }\n    }\n    return {\n      active: false,\n      matches: null\n    };\n  }\n  async function discoverRoutes(matches, pathname, signal) {\n    if (!patchRoutesOnNavigationImpl) {\n      return {\n        type: \"success\",\n        matches\n      };\n    }\n    let partialMatches = matches;\n    while (true) {\n      let isNonHMR = inFlightDataRoutes == null;\n      let routesToUse = inFlightDataRoutes || dataRoutes;\n      let localManifest = manifest;\n      try {\n        await patchRoutesOnNavigationImpl({\n          signal,\n          path: pathname,\n          matches: partialMatches,\n          patch: (routeId, children) => {\n            if (signal.aborted) return;\n            patchRoutesImpl(routeId, children, routesToUse, localManifest, mapRouteProperties);\n          }\n        });\n      } catch (e) {\n        return {\n          type: \"error\",\n          error: e,\n          partialMatches\n        };\n      } finally {\n        // If we are not in the middle of an HMR revalidation and we changed the\n        // routes, provide a new identity so when we `updateState` at the end of\n        // this navigation/fetch `router.routes` will be a new identity and\n        // trigger a re-run of memoized `router.routes` dependencies.\n        // HMR will already update the identity and reflow when it lands\n        // `inFlightDataRoutes` in `completeNavigation`\n        if (isNonHMR && !signal.aborted) {\n          dataRoutes = [...dataRoutes];\n        }\n      }\n      if (signal.aborted) {\n        return {\n          type: \"aborted\"\n        };\n      }\n      let newMatches = matchRoutes(routesToUse, pathname, basename);\n      if (newMatches) {\n        return {\n          type: \"success\",\n          matches: newMatches\n        };\n      }\n      let newPartialMatches = matchRoutesImpl(routesToUse, pathname, basename, true);\n      // Avoid loops if the second pass results in the same partial matches\n      if (!newPartialMatches || partialMatches.length === newPartialMatches.length && partialMatches.every((m, i) => m.route.id === newPartialMatches[i].route.id)) {\n        return {\n          type: \"success\",\n          matches: null\n        };\n      }\n      partialMatches = newPartialMatches;\n    }\n  }\n  function _internalSetRoutes(newRoutes) {\n    manifest = {};\n    inFlightDataRoutes = convertRoutesToDataRoutes(newRoutes, mapRouteProperties, undefined, manifest);\n  }\n  function patchRoutes(routeId, children) {\n    let isNonHMR = inFlightDataRoutes == null;\n    let routesToUse = inFlightDataRoutes || dataRoutes;\n    patchRoutesImpl(routeId, children, routesToUse, manifest, mapRouteProperties);\n    // If we are not in the middle of an HMR revalidation and we changed the\n    // routes, provide a new identity and trigger a reflow via `updateState`\n    // to re-run memoized `router.routes` dependencies.\n    // HMR will already update the identity and reflow when it lands\n    // `inFlightDataRoutes` in `completeNavigation`\n    if (isNonHMR) {\n      dataRoutes = [...dataRoutes];\n      updateState({});\n    }\n  }\n  router = {\n    get basename() {\n      return basename;\n    },\n    get future() {\n      return future;\n    },\n    get state() {\n      return state;\n    },\n    get routes() {\n      return dataRoutes;\n    },\n    get window() {\n      return routerWindow;\n    },\n    initialize,\n    subscribe,\n    enableScrollRestoration,\n    navigate,\n    fetch,\n    revalidate,\n    // Passthrough to history-aware createHref used by useHref so we get proper\n    // hash-aware URLs in DOM paths\n    createHref: to => init.history.createHref(to),\n    encodeLocation: to => init.history.encodeLocation(to),\n    getFetcher,\n    deleteFetcher: deleteFetcherAndUpdateState,\n    dispose,\n    getBlocker,\n    deleteBlocker,\n    patchRoutes,\n    _internalFetchControllers: fetchControllers,\n    _internalActiveDeferreds: activeDeferreds,\n    // TODO: Remove setRoutes, it's temporary to avoid dealing with\n    // updating the tree while validating the update algorithm.\n    _internalSetRoutes\n  };\n  return router;\n}\n//#endregion\n////////////////////////////////////////////////////////////////////////////////\n//#region createStaticHandler\n////////////////////////////////////////////////////////////////////////////////\nconst UNSAFE_DEFERRED_SYMBOL = Symbol(\"deferred\");\nfunction createStaticHandler(routes, opts) {\n  invariant(routes.length > 0, \"You must provide a non-empty routes array to createStaticHandler\");\n  let manifest = {};\n  let basename = (opts ? opts.basename : null) || \"/\";\n  let mapRouteProperties;\n  if (opts != null && opts.mapRouteProperties) {\n    mapRouteProperties = opts.mapRouteProperties;\n  } else if (opts != null && opts.detectErrorBoundary) {\n    // If they are still using the deprecated version, wrap it with the new API\n    let detectErrorBoundary = opts.detectErrorBoundary;\n    mapRouteProperties = route => ({\n      hasErrorBoundary: detectErrorBoundary(route)\n    });\n  } else {\n    mapRouteProperties = defaultMapRouteProperties;\n  }\n  // Config driven behavior flags\n  let future = _extends({\n    v7_relativeSplatPath: false,\n    v7_throwAbortReason: false\n  }, opts ? opts.future : null);\n  let dataRoutes = convertRoutesToDataRoutes(routes, mapRouteProperties, undefined, manifest);\n  /**\n   * The query() method is intended for document requests, in which we want to\n   * call an optional action and potentially multiple loaders for all nested\n   * routes.  It returns a StaticHandlerContext object, which is very similar\n   * to the router state (location, loaderData, actionData, errors, etc.) and\n   * also adds SSR-specific information such as the statusCode and headers\n   * from action/loaders Responses.\n   *\n   * It _should_ never throw and should report all errors through the\n   * returned context.errors object, properly associating errors to their error\n   * boundary.  Additionally, it tracks _deepestRenderedBoundaryId which can be\n   * used to emulate React error boundaries during SSr by performing a second\n   * pass only down to the boundaryId.\n   *\n   * The one exception where we do not return a StaticHandlerContext is when a\n   * redirect response is returned or thrown from any action/loader.  We\n   * propagate that out and return the raw Response so the HTTP server can\n   * return it directly.\n   *\n   * - `opts.requestContext` is an optional server context that will be passed\n   *   to actions/loaders in the `context` parameter\n   * - `opts.skipLoaderErrorBubbling` is an optional parameter that will prevent\n   *   the bubbling of errors which allows single-fetch-type implementations\n   *   where the client will handle the bubbling and we may need to return data\n   *   for the handling route\n   */\n  async function query(request, _temp3) {\n    let {\n      requestContext,\n      skipLoaderErrorBubbling,\n      dataStrategy\n    } = _temp3 === void 0 ? {} : _temp3;\n    let url = new URL(request.url);\n    let method = request.method;\n    let location = createLocation(\"\", createPath(url), null, \"default\");\n    let matches = matchRoutes(dataRoutes, location, basename);\n    // SSR supports HEAD requests while SPA doesn't\n    if (!isValidMethod(method) && method !== \"HEAD\") {\n      let error = getInternalRouterError(405, {\n        method\n      });\n      let {\n        matches: methodNotAllowedMatches,\n        route\n      } = getShortCircuitMatches(dataRoutes);\n      return {\n        basename,\n        location,\n        matches: methodNotAllowedMatches,\n        loaderData: {},\n        actionData: null,\n        errors: {\n          [route.id]: error\n        },\n        statusCode: error.status,\n        loaderHeaders: {},\n        actionHeaders: {},\n        activeDeferreds: null\n      };\n    } else if (!matches) {\n      let error = getInternalRouterError(404, {\n        pathname: location.pathname\n      });\n      let {\n        matches: notFoundMatches,\n        route\n      } = getShortCircuitMatches(dataRoutes);\n      return {\n        basename,\n        location,\n        matches: notFoundMatches,\n        loaderData: {},\n        actionData: null,\n        errors: {\n          [route.id]: error\n        },\n        statusCode: error.status,\n        loaderHeaders: {},\n        actionHeaders: {},\n        activeDeferreds: null\n      };\n    }\n    let result = await queryImpl(request, location, matches, requestContext, dataStrategy || null, skipLoaderErrorBubbling === true, null);\n    if (isResponse(result)) {\n      return result;\n    }\n    // When returning StaticHandlerContext, we patch back in the location here\n    // since we need it for React Context.  But this helps keep our submit and\n    // loadRouteData operating on a Request instead of a Location\n    return _extends({\n      location,\n      basename\n    }, result);\n  }\n  /**\n   * The queryRoute() method is intended for targeted route requests, either\n   * for fetch ?_data requests or resource route requests.  In this case, we\n   * are only ever calling a single action or loader, and we are returning the\n   * returned value directly.  In most cases, this will be a Response returned\n   * from the action/loader, but it may be a primitive or other value as well -\n   * and in such cases the calling context should handle that accordingly.\n   *\n   * We do respect the throw/return differentiation, so if an action/loader\n   * throws, then this method will throw the value.  This is important so we\n   * can do proper boundary identification in Remix where a thrown Response\n   * must go to the Catch Boundary but a returned Response is happy-path.\n   *\n   * One thing to note is that any Router-initiated Errors that make sense\n   * to associate with a status code will be thrown as an ErrorResponse\n   * instance which include the raw Error, such that the calling context can\n   * serialize the error as they see fit while including the proper response\n   * code.  Examples here are 404 and 405 errors that occur prior to reaching\n   * any user-defined loaders.\n   *\n   * - `opts.routeId` allows you to specify the specific route handler to call.\n   *   If not provided the handler will determine the proper route by matching\n   *   against `request.url`\n   * - `opts.requestContext` is an optional server context that will be passed\n   *    to actions/loaders in the `context` parameter\n   */\n  async function queryRoute(request, _temp4) {\n    let {\n      routeId,\n      requestContext,\n      dataStrategy\n    } = _temp4 === void 0 ? {} : _temp4;\n    let url = new URL(request.url);\n    let method = request.method;\n    let location = createLocation(\"\", createPath(url), null, \"default\");\n    let matches = matchRoutes(dataRoutes, location, basename);\n    // SSR supports HEAD requests while SPA doesn't\n    if (!isValidMethod(method) && method !== \"HEAD\" && method !== \"OPTIONS\") {\n      throw getInternalRouterError(405, {\n        method\n      });\n    } else if (!matches) {\n      throw getInternalRouterError(404, {\n        pathname: location.pathname\n      });\n    }\n    let match = routeId ? matches.find(m => m.route.id === routeId) : getTargetMatch(matches, location);\n    if (routeId && !match) {\n      throw getInternalRouterError(403, {\n        pathname: location.pathname,\n        routeId\n      });\n    } else if (!match) {\n      // This should never hit I don't think?\n      throw getInternalRouterError(404, {\n        pathname: location.pathname\n      });\n    }\n    let result = await queryImpl(request, location, matches, requestContext, dataStrategy || null, false, match);\n    if (isResponse(result)) {\n      return result;\n    }\n    let error = result.errors ? Object.values(result.errors)[0] : undefined;\n    if (error !== undefined) {\n      // If we got back result.errors, that means the loader/action threw\n      // _something_ that wasn't a Response, but it's not guaranteed/required\n      // to be an `instanceof Error` either, so we have to use throw here to\n      // preserve the \"error\" state outside of queryImpl.\n      throw error;\n    }\n    // Pick off the right state value to return\n    if (result.actionData) {\n      return Object.values(result.actionData)[0];\n    }\n    if (result.loaderData) {\n      var _result$activeDeferre;\n      let data = Object.values(result.loaderData)[0];\n      if ((_result$activeDeferre = result.activeDeferreds) != null && _result$activeDeferre[match.route.id]) {\n        data[UNSAFE_DEFERRED_SYMBOL] = result.activeDeferreds[match.route.id];\n      }\n      return data;\n    }\n    return undefined;\n  }\n  async function queryImpl(request, location, matches, requestContext, dataStrategy, skipLoaderErrorBubbling, routeMatch) {\n    invariant(request.signal, \"query()/queryRoute() requests must contain an AbortController signal\");\n    try {\n      if (isMutationMethod(request.method.toLowerCase())) {\n        let result = await submit(request, matches, routeMatch || getTargetMatch(matches, location), requestContext, dataStrategy, skipLoaderErrorBubbling, routeMatch != null);\n        return result;\n      }\n      let result = await loadRouteData(request, matches, requestContext, dataStrategy, skipLoaderErrorBubbling, routeMatch);\n      return isResponse(result) ? result : _extends({}, result, {\n        actionData: null,\n        actionHeaders: {}\n      });\n    } catch (e) {\n      // If the user threw/returned a Response in callLoaderOrAction for a\n      // `queryRoute` call, we throw the `DataStrategyResult` to bail out early\n      // and then return or throw the raw Response here accordingly\n      if (isDataStrategyResult(e) && isResponse(e.result)) {\n        if (e.type === ResultType.error) {\n          throw e.result;\n        }\n        return e.result;\n      }\n      // Redirects are always returned since they don't propagate to catch\n      // boundaries\n      if (isRedirectResponse(e)) {\n        return e;\n      }\n      throw e;\n    }\n  }\n  async function submit(request, matches, actionMatch, requestContext, dataStrategy, skipLoaderErrorBubbling, isRouteRequest) {\n    let result;\n    if (!actionMatch.route.action && !actionMatch.route.lazy) {\n      let error = getInternalRouterError(405, {\n        method: request.method,\n        pathname: new URL(request.url).pathname,\n        routeId: actionMatch.route.id\n      });\n      if (isRouteRequest) {\n        throw error;\n      }\n      result = {\n        type: ResultType.error,\n        error\n      };\n    } else {\n      let results = await callDataStrategy(\"action\", request, [actionMatch], matches, isRouteRequest, requestContext, dataStrategy);\n      result = results[actionMatch.route.id];\n      if (request.signal.aborted) {\n        throwStaticHandlerAbortedError(request, isRouteRequest, future);\n      }\n    }\n    if (isRedirectResult(result)) {\n      // Uhhhh - this should never happen, we should always throw these from\n      // callLoaderOrAction, but the type narrowing here keeps TS happy and we\n      // can get back on the \"throw all redirect responses\" train here should\n      // this ever happen :/\n      throw new Response(null, {\n        status: result.response.status,\n        headers: {\n          Location: result.response.headers.get(\"Location\")\n        }\n      });\n    }\n    if (isDeferredResult(result)) {\n      let error = getInternalRouterError(400, {\n        type: \"defer-action\"\n      });\n      if (isRouteRequest) {\n        throw error;\n      }\n      result = {\n        type: ResultType.error,\n        error\n      };\n    }\n    if (isRouteRequest) {\n      // Note: This should only be non-Response values if we get here, since\n      // isRouteRequest should throw any Response received in callLoaderOrAction\n      if (isErrorResult(result)) {\n        throw result.error;\n      }\n      return {\n        matches: [actionMatch],\n        loaderData: {},\n        actionData: {\n          [actionMatch.route.id]: result.data\n        },\n        errors: null,\n        // Note: statusCode + headers are unused here since queryRoute will\n        // return the raw Response or value\n        statusCode: 200,\n        loaderHeaders: {},\n        actionHeaders: {},\n        activeDeferreds: null\n      };\n    }\n    // Create a GET request for the loaders\n    let loaderRequest = new Request(request.url, {\n      headers: request.headers,\n      redirect: request.redirect,\n      signal: request.signal\n    });\n    if (isErrorResult(result)) {\n      // Store off the pending error - we use it to determine which loaders\n      // to call and will commit it when we complete the navigation\n      let boundaryMatch = skipLoaderErrorBubbling ? actionMatch : findNearestBoundary(matches, actionMatch.route.id);\n      let context = await loadRouteData(loaderRequest, matches, requestContext, dataStrategy, skipLoaderErrorBubbling, null, [boundaryMatch.route.id, result]);\n      // action status codes take precedence over loader status codes\n      return _extends({}, context, {\n        statusCode: isRouteErrorResponse(result.error) ? result.error.status : result.statusCode != null ? result.statusCode : 500,\n        actionData: null,\n        actionHeaders: _extends({}, result.headers ? {\n          [actionMatch.route.id]: result.headers\n        } : {})\n      });\n    }\n    let context = await loadRouteData(loaderRequest, matches, requestContext, dataStrategy, skipLoaderErrorBubbling, null);\n    return _extends({}, context, {\n      actionData: {\n        [actionMatch.route.id]: result.data\n      }\n    }, result.statusCode ? {\n      statusCode: result.statusCode\n    } : {}, {\n      actionHeaders: result.headers ? {\n        [actionMatch.route.id]: result.headers\n      } : {}\n    });\n  }\n  async function loadRouteData(request, matches, requestContext, dataStrategy, skipLoaderErrorBubbling, routeMatch, pendingActionResult) {\n    let isRouteRequest = routeMatch != null;\n    // Short circuit if we have no loaders to run (queryRoute())\n    if (isRouteRequest && !(routeMatch != null && routeMatch.route.loader) && !(routeMatch != null && routeMatch.route.lazy)) {\n      throw getInternalRouterError(400, {\n        method: request.method,\n        pathname: new URL(request.url).pathname,\n        routeId: routeMatch == null ? void 0 : routeMatch.route.id\n      });\n    }\n    let requestMatches = routeMatch ? [routeMatch] : pendingActionResult && isErrorResult(pendingActionResult[1]) ? getLoaderMatchesUntilBoundary(matches, pendingActionResult[0]) : matches;\n    let matchesToLoad = requestMatches.filter(m => m.route.loader || m.route.lazy);\n    // Short circuit if we have no loaders to run (query())\n    if (matchesToLoad.length === 0) {\n      return {\n        matches,\n        // Add a null for all matched routes for proper revalidation on the client\n        loaderData: matches.reduce((acc, m) => Object.assign(acc, {\n          [m.route.id]: null\n        }), {}),\n        errors: pendingActionResult && isErrorResult(pendingActionResult[1]) ? {\n          [pendingActionResult[0]]: pendingActionResult[1].error\n        } : null,\n        statusCode: 200,\n        loaderHeaders: {},\n        activeDeferreds: null\n      };\n    }\n    let results = await callDataStrategy(\"loader\", request, matchesToLoad, matches, isRouteRequest, requestContext, dataStrategy);\n    if (request.signal.aborted) {\n      throwStaticHandlerAbortedError(request, isRouteRequest, future);\n    }\n    // Process and commit output from loaders\n    let activeDeferreds = new Map();\n    let context = processRouteLoaderData(matches, results, pendingActionResult, activeDeferreds, skipLoaderErrorBubbling);\n    // Add a null for any non-loader matches for proper revalidation on the client\n    let executedLoaders = new Set(matchesToLoad.map(match => match.route.id));\n    matches.forEach(match => {\n      if (!executedLoaders.has(match.route.id)) {\n        context.loaderData[match.route.id] = null;\n      }\n    });\n    return _extends({}, context, {\n      matches,\n      activeDeferreds: activeDeferreds.size > 0 ? Object.fromEntries(activeDeferreds.entries()) : null\n    });\n  }\n  // Utility wrapper for calling dataStrategy server-side without having to\n  // pass around the manifest, mapRouteProperties, etc.\n  async function callDataStrategy(type, request, matchesToLoad, matches, isRouteRequest, requestContext, dataStrategy) {\n    let results = await callDataStrategyImpl(dataStrategy || defaultDataStrategy, type, null, request, matchesToLoad, matches, null, manifest, mapRouteProperties, requestContext);\n    let dataResults = {};\n    await Promise.all(matches.map(async match => {\n      if (!(match.route.id in results)) {\n        return;\n      }\n      let result = results[match.route.id];\n      if (isRedirectDataStrategyResultResult(result)) {\n        let response = result.result;\n        // Throw redirects and let the server handle them with an HTTP redirect\n        throw normalizeRelativeRoutingRedirectResponse(response, request, match.route.id, matches, basename, future.v7_relativeSplatPath);\n      }\n      if (isResponse(result.result) && isRouteRequest) {\n        // For SSR single-route requests, we want to hand Responses back\n        // directly without unwrapping\n        throw result;\n      }\n      dataResults[match.route.id] = await convertDataStrategyResultToDataResult(result);\n    }));\n    return dataResults;\n  }\n  return {\n    dataRoutes,\n    query,\n    queryRoute\n  };\n}\n//#endregion\n////////////////////////////////////////////////////////////////////////////////\n//#region Helpers\n////////////////////////////////////////////////////////////////////////////////\n/**\n * Given an existing StaticHandlerContext and an error thrown at render time,\n * provide an updated StaticHandlerContext suitable for a second SSR render\n */\nfunction getStaticContextFromError(routes, context, error) {\n  let newContext = _extends({}, context, {\n    statusCode: isRouteErrorResponse(error) ? error.status : 500,\n    errors: {\n      [context._deepestRenderedBoundaryId || routes[0].id]: error\n    }\n  });\n  return newContext;\n}\nfunction throwStaticHandlerAbortedError(request, isRouteRequest, future) {\n  if (future.v7_throwAbortReason && request.signal.reason !== undefined) {\n    throw request.signal.reason;\n  }\n  let method = isRouteRequest ? \"queryRoute\" : \"query\";\n  throw new Error(method + \"() call aborted: \" + request.method + \" \" + request.url);\n}\nfunction isSubmissionNavigation(opts) {\n  return opts != null && (\"formData\" in opts && opts.formData != null || \"body\" in opts && opts.body !== undefined);\n}\nfunction normalizeTo(location, matches, basename, prependBasename, to, v7_relativeSplatPath, fromRouteId, relative) {\n  let contextualMatches;\n  let activeRouteMatch;\n  if (fromRouteId) {\n    // Grab matches up to the calling route so our route-relative logic is\n    // relative to the correct source route\n    contextualMatches = [];\n    for (let match of matches) {\n      contextualMatches.push(match);\n      if (match.route.id === fromRouteId) {\n        activeRouteMatch = match;\n        break;\n      }\n    }\n  } else {\n    contextualMatches = matches;\n    activeRouteMatch = matches[matches.length - 1];\n  }\n  // Resolve the relative path\n  let path = resolveTo(to ? to : \".\", getResolveToMatches(contextualMatches, v7_relativeSplatPath), stripBasename(location.pathname, basename) || location.pathname, relative === \"path\");\n  // When `to` is not specified we inherit search/hash from the current\n  // location, unlike when to=\".\" and we just inherit the path.\n  // See https://github.com/remix-run/remix/issues/927\n  if (to == null) {\n    path.search = location.search;\n    path.hash = location.hash;\n  }\n  // Account for `?index` params when routing to the current location\n  if ((to == null || to === \"\" || to === \".\") && activeRouteMatch) {\n    let nakedIndex = hasNakedIndexQuery(path.search);\n    if (activeRouteMatch.route.index && !nakedIndex) {\n      // Add one when we're targeting an index route\n      path.search = path.search ? path.search.replace(/^\\?/, \"?index&\") : \"?index\";\n    } else if (!activeRouteMatch.route.index && nakedIndex) {\n      // Remove existing ones when we're not\n      let params = new URLSearchParams(path.search);\n      let indexValues = params.getAll(\"index\");\n      params.delete(\"index\");\n      indexValues.filter(v => v).forEach(v => params.append(\"index\", v));\n      let qs = params.toString();\n      path.search = qs ? \"?\" + qs : \"\";\n    }\n  }\n  // If we're operating within a basename, prepend it to the pathname.  If\n  // this is a root navigation, then just use the raw basename which allows\n  // the basename to have full control over the presence of a trailing slash\n  // on root actions\n  if (prependBasename && basename !== \"/\") {\n    path.pathname = path.pathname === \"/\" ? basename : joinPaths([basename, path.pathname]);\n  }\n  return createPath(path);\n}\n// Normalize navigation options by converting formMethod=GET formData objects to\n// URLSearchParams so they behave identically to links with query params\nfunction normalizeNavigateOptions(normalizeFormMethod, isFetcher, path, opts) {\n  // Return location verbatim on non-submission navigations\n  if (!opts || !isSubmissionNavigation(opts)) {\n    return {\n      path\n    };\n  }\n  if (opts.formMethod && !isValidMethod(opts.formMethod)) {\n    return {\n      path,\n      error: getInternalRouterError(405, {\n        method: opts.formMethod\n      })\n    };\n  }\n  let getInvalidBodyError = () => ({\n    path,\n    error: getInternalRouterError(400, {\n      type: \"invalid-body\"\n    })\n  });\n  // Create a Submission on non-GET navigations\n  let rawFormMethod = opts.formMethod || \"get\";\n  let formMethod = normalizeFormMethod ? rawFormMethod.toUpperCase() : rawFormMethod.toLowerCase();\n  let formAction = stripHashFromPath(path);\n  if (opts.body !== undefined) {\n    if (opts.formEncType === \"text/plain\") {\n      // text only support POST/PUT/PATCH/DELETE submissions\n      if (!isMutationMethod(formMethod)) {\n        return getInvalidBodyError();\n      }\n      let text = typeof opts.body === \"string\" ? opts.body : opts.body instanceof FormData || opts.body instanceof URLSearchParams ?\n      // https://html.spec.whatwg.org/multipage/form-control-infrastructure.html#plain-text-form-data\n      Array.from(opts.body.entries()).reduce((acc, _ref3) => {\n        let [name, value] = _ref3;\n        return \"\" + acc + name + \"=\" + value + \"\\n\";\n      }, \"\") : String(opts.body);\n      return {\n        path,\n        submission: {\n          formMethod,\n          formAction,\n          formEncType: opts.formEncType,\n          formData: undefined,\n          json: undefined,\n          text\n        }\n      };\n    } else if (opts.formEncType === \"application/json\") {\n      // json only supports POST/PUT/PATCH/DELETE submissions\n      if (!isMutationMethod(formMethod)) {\n        return getInvalidBodyError();\n      }\n      try {\n        let json = typeof opts.body === \"string\" ? JSON.parse(opts.body) : opts.body;\n        return {\n          path,\n          submission: {\n            formMethod,\n            formAction,\n            formEncType: opts.formEncType,\n            formData: undefined,\n            json,\n            text: undefined\n          }\n        };\n      } catch (e) {\n        return getInvalidBodyError();\n      }\n    }\n  }\n  invariant(typeof FormData === \"function\", \"FormData is not available in this environment\");\n  let searchParams;\n  let formData;\n  if (opts.formData) {\n    searchParams = convertFormDataToSearchParams(opts.formData);\n    formData = opts.formData;\n  } else if (opts.body instanceof FormData) {\n    searchParams = convertFormDataToSearchParams(opts.body);\n    formData = opts.body;\n  } else if (opts.body instanceof URLSearchParams) {\n    searchParams = opts.body;\n    formData = convertSearchParamsToFormData(searchParams);\n  } else if (opts.body == null) {\n    searchParams = new URLSearchParams();\n    formData = new FormData();\n  } else {\n    try {\n      searchParams = new URLSearchParams(opts.body);\n      formData = convertSearchParamsToFormData(searchParams);\n    } catch (e) {\n      return getInvalidBodyError();\n    }\n  }\n  let submission = {\n    formMethod,\n    formAction,\n    formEncType: opts && opts.formEncType || \"application/x-www-form-urlencoded\",\n    formData,\n    json: undefined,\n    text: undefined\n  };\n  if (isMutationMethod(submission.formMethod)) {\n    return {\n      path,\n      submission\n    };\n  }\n  // Flatten submission onto URLSearchParams for GET submissions\n  let parsedPath = parsePath(path);\n  // On GET navigation submissions we can drop the ?index param from the\n  // resulting location since all loaders will run.  But fetcher GET submissions\n  // only run a single loader so we need to preserve any incoming ?index params\n  if (isFetcher && parsedPath.search && hasNakedIndexQuery(parsedPath.search)) {\n    searchParams.append(\"index\", \"\");\n  }\n  parsedPath.search = \"?\" + searchParams;\n  return {\n    path: createPath(parsedPath),\n    submission\n  };\n}\n// Filter out all routes at/below any caught error as they aren't going to\n// render so we don't need to load them\nfunction getLoaderMatchesUntilBoundary(matches, boundaryId, includeBoundary) {\n  if (includeBoundary === void 0) {\n    includeBoundary = false;\n  }\n  let index = matches.findIndex(m => m.route.id === boundaryId);\n  if (index >= 0) {\n    return matches.slice(0, includeBoundary ? index + 1 : index);\n  }\n  return matches;\n}\nfunction getMatchesToLoad(history, state, matches, submission, location, initialHydration, skipActionErrorRevalidation, isRevalidationRequired, cancelledDeferredRoutes, cancelledFetcherLoads, deletedFetchers, fetchLoadMatches, fetchRedirectIds, routesToUse, basename, pendingActionResult) {\n  let actionResult = pendingActionResult ? isErrorResult(pendingActionResult[1]) ? pendingActionResult[1].error : pendingActionResult[1].data : undefined;\n  let currentUrl = history.createURL(state.location);\n  let nextUrl = history.createURL(location);\n  // Pick navigation matches that are net-new or qualify for revalidation\n  let boundaryMatches = matches;\n  if (initialHydration && state.errors) {\n    // On initial hydration, only consider matches up to _and including_ the boundary.\n    // This is inclusive to handle cases where a server loader ran successfully,\n    // a child server loader bubbled up to this route, but this route has\n    // `clientLoader.hydrate` so we want to still run the `clientLoader` so that\n    // we have a complete version of `loaderData`\n    boundaryMatches = getLoaderMatchesUntilBoundary(matches, Object.keys(state.errors)[0], true);\n  } else if (pendingActionResult && isErrorResult(pendingActionResult[1])) {\n    // If an action threw an error, we call loaders up to, but not including the\n    // boundary\n    boundaryMatches = getLoaderMatchesUntilBoundary(matches, pendingActionResult[0]);\n  }\n  // Don't revalidate loaders by default after action 4xx/5xx responses\n  // when the flag is enabled.  They can still opt-into revalidation via\n  // `shouldRevalidate` via `actionResult`\n  let actionStatus = pendingActionResult ? pendingActionResult[1].statusCode : undefined;\n  let shouldSkipRevalidation = skipActionErrorRevalidation && actionStatus && actionStatus >= 400;\n  let navigationMatches = boundaryMatches.filter((match, index) => {\n    let {\n      route\n    } = match;\n    if (route.lazy) {\n      // We haven't loaded this route yet so we don't know if it's got a loader!\n      return true;\n    }\n    if (route.loader == null) {\n      return false;\n    }\n    if (initialHydration) {\n      return shouldLoadRouteOnHydration(route, state.loaderData, state.errors);\n    }\n    // Always call the loader on new route instances and pending defer cancellations\n    if (isNewLoader(state.loaderData, state.matches[index], match) || cancelledDeferredRoutes.some(id => id === match.route.id)) {\n      return true;\n    }\n    // This is the default implementation for when we revalidate.  If the route\n    // provides it's own implementation, then we give them full control but\n    // provide this value so they can leverage it if needed after they check\n    // their own specific use cases\n    let currentRouteMatch = state.matches[index];\n    let nextRouteMatch = match;\n    return shouldRevalidateLoader(match, _extends({\n      currentUrl,\n      currentParams: currentRouteMatch.params,\n      nextUrl,\n      nextParams: nextRouteMatch.params\n    }, submission, {\n      actionResult,\n      actionStatus,\n      defaultShouldRevalidate: shouldSkipRevalidation ? false :\n      // Forced revalidation due to submission, useRevalidator, or X-Remix-Revalidate\n      isRevalidationRequired || currentUrl.pathname + currentUrl.search === nextUrl.pathname + nextUrl.search ||\n      // Search params affect all loaders\n      currentUrl.search !== nextUrl.search || isNewRouteInstance(currentRouteMatch, nextRouteMatch)\n    }));\n  });\n  // Pick fetcher.loads that need to be revalidated\n  let revalidatingFetchers = [];\n  fetchLoadMatches.forEach((f, key) => {\n    // Don't revalidate:\n    //  - on initial hydration (shouldn't be any fetchers then anyway)\n    //  - if fetcher won't be present in the subsequent render\n    //    - no longer matches the URL (v7_fetcherPersist=false)\n    //    - was unmounted but persisted due to v7_fetcherPersist=true\n    if (initialHydration || !matches.some(m => m.route.id === f.routeId) || deletedFetchers.has(key)) {\n      return;\n    }\n    let fetcherMatches = matchRoutes(routesToUse, f.path, basename);\n    // If the fetcher path no longer matches, push it in with null matches so\n    // we can trigger a 404 in callLoadersAndMaybeResolveData.  Note this is\n    // currently only a use-case for Remix HMR where the route tree can change\n    // at runtime and remove a route previously loaded via a fetcher\n    if (!fetcherMatches) {\n      revalidatingFetchers.push({\n        key,\n        routeId: f.routeId,\n        path: f.path,\n        matches: null,\n        match: null,\n        controller: null\n      });\n      return;\n    }\n    // Revalidating fetchers are decoupled from the route matches since they\n    // load from a static href.  They revalidate based on explicit revalidation\n    // (submission, useRevalidator, or X-Remix-Revalidate)\n    let fetcher = state.fetchers.get(key);\n    let fetcherMatch = getTargetMatch(fetcherMatches, f.path);\n    let shouldRevalidate = false;\n    if (fetchRedirectIds.has(key)) {\n      // Never trigger a revalidation of an actively redirecting fetcher\n      shouldRevalidate = false;\n    } else if (cancelledFetcherLoads.has(key)) {\n      // Always mark for revalidation if the fetcher was cancelled\n      cancelledFetcherLoads.delete(key);\n      shouldRevalidate = true;\n    } else if (fetcher && fetcher.state !== \"idle\" && fetcher.data === undefined) {\n      // If the fetcher hasn't ever completed loading yet, then this isn't a\n      // revalidation, it would just be a brand new load if an explicit\n      // revalidation is required\n      shouldRevalidate = isRevalidationRequired;\n    } else {\n      // Otherwise fall back on any user-defined shouldRevalidate, defaulting\n      // to explicit revalidations only\n      shouldRevalidate = shouldRevalidateLoader(fetcherMatch, _extends({\n        currentUrl,\n        currentParams: state.matches[state.matches.length - 1].params,\n        nextUrl,\n        nextParams: matches[matches.length - 1].params\n      }, submission, {\n        actionResult,\n        actionStatus,\n        defaultShouldRevalidate: shouldSkipRevalidation ? false : isRevalidationRequired\n      }));\n    }\n    if (shouldRevalidate) {\n      revalidatingFetchers.push({\n        key,\n        routeId: f.routeId,\n        path: f.path,\n        matches: fetcherMatches,\n        match: fetcherMatch,\n        controller: new AbortController()\n      });\n    }\n  });\n  return [navigationMatches, revalidatingFetchers];\n}\nfunction shouldLoadRouteOnHydration(route, loaderData, errors) {\n  // We dunno if we have a loader - gotta find out!\n  if (route.lazy) {\n    return true;\n  }\n  // No loader, nothing to initialize\n  if (!route.loader) {\n    return false;\n  }\n  let hasData = loaderData != null && loaderData[route.id] !== undefined;\n  let hasError = errors != null && errors[route.id] !== undefined;\n  // Don't run if we error'd during SSR\n  if (!hasData && hasError) {\n    return false;\n  }\n  // Explicitly opting-in to running on hydration\n  if (typeof route.loader === \"function\" && route.loader.hydrate === true) {\n    return true;\n  }\n  // Otherwise, run if we're not yet initialized with anything\n  return !hasData && !hasError;\n}\nfunction isNewLoader(currentLoaderData, currentMatch, match) {\n  let isNew =\n  // [a] -> [a, b]\n  !currentMatch ||\n  // [a, b] -> [a, c]\n  match.route.id !== currentMatch.route.id;\n  // Handle the case that we don't have data for a re-used route, potentially\n  // from a prior error or from a cancelled pending deferred\n  let isMissingData = currentLoaderData[match.route.id] === undefined;\n  // Always load if this is a net-new route or we don't yet have data\n  return isNew || isMissingData;\n}\nfunction isNewRouteInstance(currentMatch, match) {\n  let currentPath = currentMatch.route.path;\n  return (\n    // param change for this match, /users/123 -> /users/456\n    currentMatch.pathname !== match.pathname ||\n    // splat param changed, which is not present in match.path\n    // e.g. /files/images/avatar.jpg -> files/finances.xls\n    currentPath != null && currentPath.endsWith(\"*\") && currentMatch.params[\"*\"] !== match.params[\"*\"]\n  );\n}\nfunction shouldRevalidateLoader(loaderMatch, arg) {\n  if (loaderMatch.route.shouldRevalidate) {\n    let routeChoice = loaderMatch.route.shouldRevalidate(arg);\n    if (typeof routeChoice === \"boolean\") {\n      return routeChoice;\n    }\n  }\n  return arg.defaultShouldRevalidate;\n}\nfunction patchRoutesImpl(routeId, children, routesToUse, manifest, mapRouteProperties) {\n  var _childrenToPatch;\n  let childrenToPatch;\n  if (routeId) {\n    let route = manifest[routeId];\n    invariant(route, \"No route found to patch children into: routeId = \" + routeId);\n    if (!route.children) {\n      route.children = [];\n    }\n    childrenToPatch = route.children;\n  } else {\n    childrenToPatch = routesToUse;\n  }\n  // Don't patch in routes we already know about so that `patch` is idempotent\n  // to simplify user-land code. This is useful because we re-call the\n  // `patchRoutesOnNavigation` function for matched routes with params.\n  let uniqueChildren = children.filter(newRoute => !childrenToPatch.some(existingRoute => isSameRoute(newRoute, existingRoute)));\n  let newRoutes = convertRoutesToDataRoutes(uniqueChildren, mapRouteProperties, [routeId || \"_\", \"patch\", String(((_childrenToPatch = childrenToPatch) == null ? void 0 : _childrenToPatch.length) || \"0\")], manifest);\n  childrenToPatch.push(...newRoutes);\n}\nfunction isSameRoute(newRoute, existingRoute) {\n  // Most optimal check is by id\n  if (\"id\" in newRoute && \"id\" in existingRoute && newRoute.id === existingRoute.id) {\n    return true;\n  }\n  // Second is by pathing differences\n  if (!(newRoute.index === existingRoute.index && newRoute.path === existingRoute.path && newRoute.caseSensitive === existingRoute.caseSensitive)) {\n    return false;\n  }\n  // Pathless layout routes are trickier since we need to check children.\n  // If they have no children then they're the same as far as we can tell\n  if ((!newRoute.children || newRoute.children.length === 0) && (!existingRoute.children || existingRoute.children.length === 0)) {\n    return true;\n  }\n  // Otherwise, we look to see if every child in the new route is already\n  // represented in the existing route's children\n  return newRoute.children.every((aChild, i) => {\n    var _existingRoute$childr;\n    return (_existingRoute$childr = existingRoute.children) == null ? void 0 : _existingRoute$childr.some(bChild => isSameRoute(aChild, bChild));\n  });\n}\n/**\n * Execute route.lazy() methods to lazily load route modules (loader, action,\n * shouldRevalidate) and update the routeManifest in place which shares objects\n * with dataRoutes so those get updated as well.\n */\nasync function loadLazyRouteModule(route, mapRouteProperties, manifest) {\n  if (!route.lazy) {\n    return;\n  }\n  let lazyRoute = await route.lazy();\n  // If the lazy route function was executed and removed by another parallel\n  // call then we can return - first lazy() to finish wins because the return\n  // value of lazy is expected to be static\n  if (!route.lazy) {\n    return;\n  }\n  let routeToUpdate = manifest[route.id];\n  invariant(routeToUpdate, \"No route found in manifest\");\n  // Update the route in place.  This should be safe because there's no way\n  // we could yet be sitting on this route as we can't get there without\n  // resolving lazy() first.\n  //\n  // This is different than the HMR \"update\" use-case where we may actively be\n  // on the route being updated.  The main concern boils down to \"does this\n  // mutation affect any ongoing navigations or any current state.matches\n  // values?\".  If not, it should be safe to update in place.\n  let routeUpdates = {};\n  for (let lazyRouteProperty in lazyRoute) {\n    let staticRouteValue = routeToUpdate[lazyRouteProperty];\n    let isPropertyStaticallyDefined = staticRouteValue !== undefined &&\n    // This property isn't static since it should always be updated based\n    // on the route updates\n    lazyRouteProperty !== \"hasErrorBoundary\";\n    warning(!isPropertyStaticallyDefined, \"Route \\\"\" + routeToUpdate.id + \"\\\" has a static property \\\"\" + lazyRouteProperty + \"\\\" \" + \"defined but its lazy function is also returning a value for this property. \" + (\"The lazy route property \\\"\" + lazyRouteProperty + \"\\\" will be ignored.\"));\n    if (!isPropertyStaticallyDefined && !immutableRouteKeys.has(lazyRouteProperty)) {\n      routeUpdates[lazyRouteProperty] = lazyRoute[lazyRouteProperty];\n    }\n  }\n  // Mutate the route with the provided updates.  Do this first so we pass\n  // the updated version to mapRouteProperties\n  Object.assign(routeToUpdate, routeUpdates);\n  // Mutate the `hasErrorBoundary` property on the route based on the route\n  // updates and remove the `lazy` function so we don't resolve the lazy\n  // route again.\n  Object.assign(routeToUpdate, _extends({}, mapRouteProperties(routeToUpdate), {\n    lazy: undefined\n  }));\n}\n// Default implementation of `dataStrategy` which fetches all loaders in parallel\nasync function defaultDataStrategy(_ref4) {\n  let {\n    matches\n  } = _ref4;\n  let matchesToLoad = matches.filter(m => m.shouldLoad);\n  let results = await Promise.all(matchesToLoad.map(m => m.resolve()));\n  return results.reduce((acc, result, i) => Object.assign(acc, {\n    [matchesToLoad[i].route.id]: result\n  }), {});\n}\nasync function callDataStrategyImpl(dataStrategyImpl, type, state, request, matchesToLoad, matches, fetcherKey, manifest, mapRouteProperties, requestContext) {\n  let loadRouteDefinitionsPromises = matches.map(m => m.route.lazy ? loadLazyRouteModule(m.route, mapRouteProperties, manifest) : undefined);\n  let dsMatches = matches.map((match, i) => {\n    let loadRoutePromise = loadRouteDefinitionsPromises[i];\n    let shouldLoad = matchesToLoad.some(m => m.route.id === match.route.id);\n    // `resolve` encapsulates route.lazy(), executing the loader/action,\n    // and mapping return values/thrown errors to a `DataStrategyResult`.  Users\n    // can pass a callback to take fine-grained control over the execution\n    // of the loader/action\n    let resolve = async handlerOverride => {\n      if (handlerOverride && request.method === \"GET\" && (match.route.lazy || match.route.loader)) {\n        shouldLoad = true;\n      }\n      return shouldLoad ? callLoaderOrAction(type, request, match, loadRoutePromise, handlerOverride, requestContext) : Promise.resolve({\n        type: ResultType.data,\n        result: undefined\n      });\n    };\n    return _extends({}, match, {\n      shouldLoad,\n      resolve\n    });\n  });\n  // Send all matches here to allow for a middleware-type implementation.\n  // handler will be a no-op for unneeded routes and we filter those results\n  // back out below.\n  let results = await dataStrategyImpl({\n    matches: dsMatches,\n    request,\n    params: matches[0].params,\n    fetcherKey,\n    context: requestContext\n  });\n  // Wait for all routes to load here but 'swallow the error since we want\n  // it to bubble up from the `await loadRoutePromise` in `callLoaderOrAction` -\n  // called from `match.resolve()`\n  try {\n    await Promise.all(loadRouteDefinitionsPromises);\n  } catch (e) {\n    // No-op\n  }\n  return results;\n}\n// Default logic for calling a loader/action is the user has no specified a dataStrategy\nasync function callLoaderOrAction(type, request, match, loadRoutePromise, handlerOverride, staticContext) {\n  let result;\n  let onReject;\n  let runHandler = handler => {\n    // Setup a promise we can race against so that abort signals short circuit\n    let reject;\n    // This will never resolve so safe to type it as Promise<DataStrategyResult> to\n    // satisfy the function return value\n    let abortPromise = new Promise((_, r) => reject = r);\n    onReject = () => reject();\n    request.signal.addEventListener(\"abort\", onReject);\n    let actualHandler = ctx => {\n      if (typeof handler !== \"function\") {\n        return Promise.reject(new Error(\"You cannot call the handler for a route which defines a boolean \" + (\"\\\"\" + type + \"\\\" [routeId: \" + match.route.id + \"]\")));\n      }\n      return handler({\n        request,\n        params: match.params,\n        context: staticContext\n      }, ...(ctx !== undefined ? [ctx] : []));\n    };\n    let handlerPromise = (async () => {\n      try {\n        let val = await (handlerOverride ? handlerOverride(ctx => actualHandler(ctx)) : actualHandler());\n        return {\n          type: \"data\",\n          result: val\n        };\n      } catch (e) {\n        return {\n          type: \"error\",\n          result: e\n        };\n      }\n    })();\n    return Promise.race([handlerPromise, abortPromise]);\n  };\n  try {\n    let handler = match.route[type];\n    // If we have a route.lazy promise, await that first\n    if (loadRoutePromise) {\n      if (handler) {\n        // Run statically defined handler in parallel with lazy()\n        let handlerError;\n        let [value] = await Promise.all([\n        // If the handler throws, don't let it immediately bubble out,\n        // since we need to let the lazy() execution finish so we know if this\n        // route has a boundary that can handle the error\n        runHandler(handler).catch(e => {\n          handlerError = e;\n        }), loadRoutePromise]);\n        if (handlerError !== undefined) {\n          throw handlerError;\n        }\n        result = value;\n      } else {\n        // Load lazy route module, then run any returned handler\n        await loadRoutePromise;\n        handler = match.route[type];\n        if (handler) {\n          // Handler still runs even if we got interrupted to maintain consistency\n          // with un-abortable behavior of handler execution on non-lazy or\n          // previously-lazy-loaded routes\n          result = await runHandler(handler);\n        } else if (type === \"action\") {\n          let url = new URL(request.url);\n          let pathname = url.pathname + url.search;\n          throw getInternalRouterError(405, {\n            method: request.method,\n            pathname,\n            routeId: match.route.id\n          });\n        } else {\n          // lazy() route has no loader to run.  Short circuit here so we don't\n          // hit the invariant below that errors on returning undefined.\n          return {\n            type: ResultType.data,\n            result: undefined\n          };\n        }\n      }\n    } else if (!handler) {\n      let url = new URL(request.url);\n      let pathname = url.pathname + url.search;\n      throw getInternalRouterError(404, {\n        pathname\n      });\n    } else {\n      result = await runHandler(handler);\n    }\n    invariant(result.result !== undefined, \"You defined \" + (type === \"action\" ? \"an action\" : \"a loader\") + \" for route \" + (\"\\\"\" + match.route.id + \"\\\" but didn't return anything from your `\" + type + \"` \") + \"function. Please return a value or `null`.\");\n  } catch (e) {\n    // We should already be catching and converting normal handler executions to\n    // DataStrategyResults and returning them, so anything that throws here is an\n    // unexpected error we still need to wrap\n    return {\n      type: ResultType.error,\n      result: e\n    };\n  } finally {\n    if (onReject) {\n      request.signal.removeEventListener(\"abort\", onReject);\n    }\n  }\n  return result;\n}\nasync function convertDataStrategyResultToDataResult(dataStrategyResult) {\n  let {\n    result,\n    type\n  } = dataStrategyResult;\n  if (isResponse(result)) {\n    let data;\n    try {\n      let contentType = result.headers.get(\"Content-Type\");\n      // Check between word boundaries instead of startsWith() due to the last\n      // paragraph of https://httpwg.org/specs/rfc9110.html#field.content-type\n      if (contentType && /\\bapplication\\/json\\b/.test(contentType)) {\n        if (result.body == null) {\n          data = null;\n        } else {\n          data = await result.json();\n        }\n      } else {\n        data = await result.text();\n      }\n    } catch (e) {\n      return {\n        type: ResultType.error,\n        error: e\n      };\n    }\n    if (type === ResultType.error) {\n      return {\n        type: ResultType.error,\n        error: new ErrorResponseImpl(result.status, result.statusText, data),\n        statusCode: result.status,\n        headers: result.headers\n      };\n    }\n    return {\n      type: ResultType.data,\n      data,\n      statusCode: result.status,\n      headers: result.headers\n    };\n  }\n  if (type === ResultType.error) {\n    if (isDataWithResponseInit(result)) {\n      var _result$init3, _result$init4;\n      if (result.data instanceof Error) {\n        var _result$init, _result$init2;\n        return {\n          type: ResultType.error,\n          error: result.data,\n          statusCode: (_result$init = result.init) == null ? void 0 : _result$init.status,\n          headers: (_result$init2 = result.init) != null && _result$init2.headers ? new Headers(result.init.headers) : undefined\n        };\n      }\n      // Convert thrown data() to ErrorResponse instances\n      return {\n        type: ResultType.error,\n        error: new ErrorResponseImpl(((_result$init3 = result.init) == null ? void 0 : _result$init3.status) || 500, undefined, result.data),\n        statusCode: isRouteErrorResponse(result) ? result.status : undefined,\n        headers: (_result$init4 = result.init) != null && _result$init4.headers ? new Headers(result.init.headers) : undefined\n      };\n    }\n    return {\n      type: ResultType.error,\n      error: result,\n      statusCode: isRouteErrorResponse(result) ? result.status : undefined\n    };\n  }\n  if (isDeferredData(result)) {\n    var _result$init5, _result$init6;\n    return {\n      type: ResultType.deferred,\n      deferredData: result,\n      statusCode: (_result$init5 = result.init) == null ? void 0 : _result$init5.status,\n      headers: ((_result$init6 = result.init) == null ? void 0 : _result$init6.headers) && new Headers(result.init.headers)\n    };\n  }\n  if (isDataWithResponseInit(result)) {\n    var _result$init7, _result$init8;\n    return {\n      type: ResultType.data,\n      data: result.data,\n      statusCode: (_result$init7 = result.init) == null ? void 0 : _result$init7.status,\n      headers: (_result$init8 = result.init) != null && _result$init8.headers ? new Headers(result.init.headers) : undefined\n    };\n  }\n  return {\n    type: ResultType.data,\n    data: result\n  };\n}\n// Support relative routing in internal redirects\nfunction normalizeRelativeRoutingRedirectResponse(response, request, routeId, matches, basename, v7_relativeSplatPath) {\n  let location = response.headers.get(\"Location\");\n  invariant(location, \"Redirects returned/thrown from loaders/actions must have a Location header\");\n  if (!ABSOLUTE_URL_REGEX.test(location)) {\n    let trimmedMatches = matches.slice(0, matches.findIndex(m => m.route.id === routeId) + 1);\n    location = normalizeTo(new URL(request.url), trimmedMatches, basename, true, location, v7_relativeSplatPath);\n    response.headers.set(\"Location\", location);\n  }\n  return response;\n}\nfunction normalizeRedirectLocation(location, currentUrl, basename) {\n  if (ABSOLUTE_URL_REGEX.test(location)) {\n    // Strip off the protocol+origin for same-origin + same-basename absolute redirects\n    let normalizedLocation = location;\n    let url = normalizedLocation.startsWith(\"//\") ? new URL(currentUrl.protocol + normalizedLocation) : new URL(normalizedLocation);\n    let isSameBasename = stripBasename(url.pathname, basename) != null;\n    if (url.origin === currentUrl.origin && isSameBasename) {\n      return url.pathname + url.search + url.hash;\n    }\n  }\n  return location;\n}\n// Utility method for creating the Request instances for loaders/actions during\n// client-side navigations and fetches.  During SSR we will always have a\n// Request instance from the static handler (query/queryRoute)\nfunction createClientSideRequest(history, location, signal, submission) {\n  let url = history.createURL(stripHashFromPath(location)).toString();\n  let init = {\n    signal\n  };\n  if (submission && isMutationMethod(submission.formMethod)) {\n    let {\n      formMethod,\n      formEncType\n    } = submission;\n    // Didn't think we needed this but it turns out unlike other methods, patch\n    // won't be properly normalized to uppercase and results in a 405 error.\n    // See: https://fetch.spec.whatwg.org/#concept-method\n    init.method = formMethod.toUpperCase();\n    if (formEncType === \"application/json\") {\n      init.headers = new Headers({\n        \"Content-Type\": formEncType\n      });\n      init.body = JSON.stringify(submission.json);\n    } else if (formEncType === \"text/plain\") {\n      // Content-Type is inferred (https://fetch.spec.whatwg.org/#dom-request)\n      init.body = submission.text;\n    } else if (formEncType === \"application/x-www-form-urlencoded\" && submission.formData) {\n      // Content-Type is inferred (https://fetch.spec.whatwg.org/#dom-request)\n      init.body = convertFormDataToSearchParams(submission.formData);\n    } else {\n      // Content-Type is inferred (https://fetch.spec.whatwg.org/#dom-request)\n      init.body = submission.formData;\n    }\n  }\n  return new Request(url, init);\n}\nfunction convertFormDataToSearchParams(formData) {\n  let searchParams = new URLSearchParams();\n  for (let [key, value] of formData.entries()) {\n    // https://html.spec.whatwg.org/multipage/form-control-infrastructure.html#converting-an-entry-list-to-a-list-of-name-value-pairs\n    searchParams.append(key, typeof value === \"string\" ? value : value.name);\n  }\n  return searchParams;\n}\nfunction convertSearchParamsToFormData(searchParams) {\n  let formData = new FormData();\n  for (let [key, value] of searchParams.entries()) {\n    formData.append(key, value);\n  }\n  return formData;\n}\nfunction processRouteLoaderData(matches, results, pendingActionResult, activeDeferreds, skipLoaderErrorBubbling) {\n  // Fill in loaderData/errors from our loaders\n  let loaderData = {};\n  let errors = null;\n  let statusCode;\n  let foundError = false;\n  let loaderHeaders = {};\n  let pendingError = pendingActionResult && isErrorResult(pendingActionResult[1]) ? pendingActionResult[1].error : undefined;\n  // Process loader results into state.loaderData/state.errors\n  matches.forEach(match => {\n    if (!(match.route.id in results)) {\n      return;\n    }\n    let id = match.route.id;\n    let result = results[id];\n    invariant(!isRedirectResult(result), \"Cannot handle redirect results in processLoaderData\");\n    if (isErrorResult(result)) {\n      let error = result.error;\n      // If we have a pending action error, we report it at the highest-route\n      // that throws a loader error, and then clear it out to indicate that\n      // it was consumed\n      if (pendingError !== undefined) {\n        error = pendingError;\n        pendingError = undefined;\n      }\n      errors = errors || {};\n      if (skipLoaderErrorBubbling) {\n        errors[id] = error;\n      } else {\n        // Look upwards from the matched route for the closest ancestor error\n        // boundary, defaulting to the root match.  Prefer higher error values\n        // if lower errors bubble to the same boundary\n        let boundaryMatch = findNearestBoundary(matches, id);\n        if (errors[boundaryMatch.route.id] == null) {\n          errors[boundaryMatch.route.id] = error;\n        }\n      }\n      // Clear our any prior loaderData for the throwing route\n      loaderData[id] = undefined;\n      // Once we find our first (highest) error, we set the status code and\n      // prevent deeper status codes from overriding\n      if (!foundError) {\n        foundError = true;\n        statusCode = isRouteErrorResponse(result.error) ? result.error.status : 500;\n      }\n      if (result.headers) {\n        loaderHeaders[id] = result.headers;\n      }\n    } else {\n      if (isDeferredResult(result)) {\n        activeDeferreds.set(id, result.deferredData);\n        loaderData[id] = result.deferredData.data;\n        // Error status codes always override success status codes, but if all\n        // loaders are successful we take the deepest status code.\n        if (result.statusCode != null && result.statusCode !== 200 && !foundError) {\n          statusCode = result.statusCode;\n        }\n        if (result.headers) {\n          loaderHeaders[id] = result.headers;\n        }\n      } else {\n        loaderData[id] = result.data;\n        // Error status codes always override success status codes, but if all\n        // loaders are successful we take the deepest status code.\n        if (result.statusCode && result.statusCode !== 200 && !foundError) {\n          statusCode = result.statusCode;\n        }\n        if (result.headers) {\n          loaderHeaders[id] = result.headers;\n        }\n      }\n    }\n  });\n  // If we didn't consume the pending action error (i.e., all loaders\n  // resolved), then consume it here.  Also clear out any loaderData for the\n  // throwing route\n  if (pendingError !== undefined && pendingActionResult) {\n    errors = {\n      [pendingActionResult[0]]: pendingError\n    };\n    loaderData[pendingActionResult[0]] = undefined;\n  }\n  return {\n    loaderData,\n    errors,\n    statusCode: statusCode || 200,\n    loaderHeaders\n  };\n}\nfunction processLoaderData(state, matches, results, pendingActionResult, revalidatingFetchers, fetcherResults, activeDeferreds) {\n  let {\n    loaderData,\n    errors\n  } = processRouteLoaderData(matches, results, pendingActionResult, activeDeferreds, false // This method is only called client side so we always want to bubble\n  );\n  // Process results from our revalidating fetchers\n  revalidatingFetchers.forEach(rf => {\n    let {\n      key,\n      match,\n      controller\n    } = rf;\n    let result = fetcherResults[key];\n    invariant(result, \"Did not find corresponding fetcher result\");\n    // Process fetcher non-redirect errors\n    if (controller && controller.signal.aborted) {\n      // Nothing to do for aborted fetchers\n      return;\n    } else if (isErrorResult(result)) {\n      let boundaryMatch = findNearestBoundary(state.matches, match == null ? void 0 : match.route.id);\n      if (!(errors && errors[boundaryMatch.route.id])) {\n        errors = _extends({}, errors, {\n          [boundaryMatch.route.id]: result.error\n        });\n      }\n      state.fetchers.delete(key);\n    } else if (isRedirectResult(result)) {\n      // Should never get here, redirects should get processed above, but we\n      // keep this to type narrow to a success result in the else\n      invariant(false, \"Unhandled fetcher revalidation redirect\");\n    } else if (isDeferredResult(result)) {\n      // Should never get here, deferred data should be awaited for fetchers\n      // in resolveDeferredResults\n      invariant(false, \"Unhandled fetcher deferred data\");\n    } else {\n      let doneFetcher = getDoneFetcher(result.data);\n      state.fetchers.set(key, doneFetcher);\n    }\n  });\n  return {\n    loaderData,\n    errors\n  };\n}\nfunction mergeLoaderData(loaderData, newLoaderData, matches, errors) {\n  let mergedLoaderData = _extends({}, newLoaderData);\n  for (let match of matches) {\n    let id = match.route.id;\n    if (newLoaderData.hasOwnProperty(id)) {\n      if (newLoaderData[id] !== undefined) {\n        mergedLoaderData[id] = newLoaderData[id];\n      }\n    } else if (loaderData[id] !== undefined && match.route.loader) {\n      // Preserve existing keys not included in newLoaderData and where a loader\n      // wasn't removed by HMR\n      mergedLoaderData[id] = loaderData[id];\n    }\n    if (errors && errors.hasOwnProperty(id)) {\n      // Don't keep any loader data below the boundary\n      break;\n    }\n  }\n  return mergedLoaderData;\n}\nfunction getActionDataForCommit(pendingActionResult) {\n  if (!pendingActionResult) {\n    return {};\n  }\n  return isErrorResult(pendingActionResult[1]) ? {\n    // Clear out prior actionData on errors\n    actionData: {}\n  } : {\n    actionData: {\n      [pendingActionResult[0]]: pendingActionResult[1].data\n    }\n  };\n}\n// Find the nearest error boundary, looking upwards from the leaf route (or the\n// route specified by routeId) for the closest ancestor error boundary,\n// defaulting to the root match\nfunction findNearestBoundary(matches, routeId) {\n  let eligibleMatches = routeId ? matches.slice(0, matches.findIndex(m => m.route.id === routeId) + 1) : [...matches];\n  return eligibleMatches.reverse().find(m => m.route.hasErrorBoundary === true) || matches[0];\n}\nfunction getShortCircuitMatches(routes) {\n  // Prefer a root layout route if present, otherwise shim in a route object\n  let route = routes.length === 1 ? routes[0] : routes.find(r => r.index || !r.path || r.path === \"/\") || {\n    id: \"__shim-error-route__\"\n  };\n  return {\n    matches: [{\n      params: {},\n      pathname: \"\",\n      pathnameBase: \"\",\n      route\n    }],\n    route\n  };\n}\nfunction getInternalRouterError(status, _temp5) {\n  let {\n    pathname,\n    routeId,\n    method,\n    type,\n    message\n  } = _temp5 === void 0 ? {} : _temp5;\n  let statusText = \"Unknown Server Error\";\n  let errorMessage = \"Unknown @remix-run/router error\";\n  if (status === 400) {\n    statusText = \"Bad Request\";\n    if (method && pathname && routeId) {\n      errorMessage = \"You made a \" + method + \" request to \\\"\" + pathname + \"\\\" but \" + (\"did not provide a `loader` for route \\\"\" + routeId + \"\\\", \") + \"so there is no way to handle the request.\";\n    } else if (type === \"defer-action\") {\n      errorMessage = \"defer() is not supported in actions\";\n    } else if (type === \"invalid-body\") {\n      errorMessage = \"Unable to encode submission body\";\n    }\n  } else if (status === 403) {\n    statusText = \"Forbidden\";\n    errorMessage = \"Route \\\"\" + routeId + \"\\\" does not match URL \\\"\" + pathname + \"\\\"\";\n  } else if (status === 404) {\n    statusText = \"Not Found\";\n    errorMessage = \"No route matches URL \\\"\" + pathname + \"\\\"\";\n  } else if (status === 405) {\n    statusText = \"Method Not Allowed\";\n    if (method && pathname && routeId) {\n      errorMessage = \"You made a \" + method.toUpperCase() + \" request to \\\"\" + pathname + \"\\\" but \" + (\"did not provide an `action` for route \\\"\" + routeId + \"\\\", \") + \"so there is no way to handle the request.\";\n    } else if (method) {\n      errorMessage = \"Invalid request method \\\"\" + method.toUpperCase() + \"\\\"\";\n    }\n  }\n  return new ErrorResponseImpl(status || 500, statusText, new Error(errorMessage), true);\n}\n// Find any returned redirect errors, starting from the lowest match\nfunction findRedirect(results) {\n  let entries = Object.entries(results);\n  for (let i = entries.length - 1; i >= 0; i--) {\n    let [key, result] = entries[i];\n    if (isRedirectResult(result)) {\n      return {\n        key,\n        result\n      };\n    }\n  }\n}\nfunction stripHashFromPath(path) {\n  let parsedPath = typeof path === \"string\" ? parsePath(path) : path;\n  return createPath(_extends({}, parsedPath, {\n    hash: \"\"\n  }));\n}\nfunction isHashChangeOnly(a, b) {\n  if (a.pathname !== b.pathname || a.search !== b.search) {\n    return false;\n  }\n  if (a.hash === \"\") {\n    // /page -> /page#hash\n    return b.hash !== \"\";\n  } else if (a.hash === b.hash) {\n    // /page#hash -> /page#hash\n    return true;\n  } else if (b.hash !== \"\") {\n    // /page#hash -> /page#other\n    return true;\n  }\n  // If the hash is removed the browser will re-perform a request to the server\n  // /page#hash -> /page\n  return false;\n}\nfunction isDataStrategyResult(result) {\n  return result != null && typeof result === \"object\" && \"type\" in result && \"result\" in result && (result.type === ResultType.data || result.type === ResultType.error);\n}\nfunction isRedirectDataStrategyResultResult(result) {\n  return isResponse(result.result) && redirectStatusCodes.has(result.result.status);\n}\nfunction isDeferredResult(result) {\n  return result.type === ResultType.deferred;\n}\nfunction isErrorResult(result) {\n  return result.type === ResultType.error;\n}\nfunction isRedirectResult(result) {\n  return (result && result.type) === ResultType.redirect;\n}\nfunction isDataWithResponseInit(value) {\n  return typeof value === \"object\" && value != null && \"type\" in value && \"data\" in value && \"init\" in value && value.type === \"DataWithResponseInit\";\n}\nfunction isDeferredData(value) {\n  let deferred = value;\n  return deferred && typeof deferred === \"object\" && typeof deferred.data === \"object\" && typeof deferred.subscribe === \"function\" && typeof deferred.cancel === \"function\" && typeof deferred.resolveData === \"function\";\n}\nfunction isResponse(value) {\n  return value != null && typeof value.status === \"number\" && typeof value.statusText === \"string\" && typeof value.headers === \"object\" && typeof value.body !== \"undefined\";\n}\nfunction isRedirectResponse(result) {\n  if (!isResponse(result)) {\n    return false;\n  }\n  let status = result.status;\n  let location = result.headers.get(\"Location\");\n  return status >= 300 && status <= 399 && location != null;\n}\nfunction isValidMethod(method) {\n  return validRequestMethods.has(method.toLowerCase());\n}\nfunction isMutationMethod(method) {\n  return validMutationMethods.has(method.toLowerCase());\n}\nasync function resolveNavigationDeferredResults(matches, results, signal, currentMatches, currentLoaderData) {\n  let entries = Object.entries(results);\n  for (let index = 0; index < entries.length; index++) {\n    let [routeId, result] = entries[index];\n    let match = matches.find(m => (m == null ? void 0 : m.route.id) === routeId);\n    // If we don't have a match, then we can have a deferred result to do\n    // anything with.  This is for revalidating fetchers where the route was\n    // removed during HMR\n    if (!match) {\n      continue;\n    }\n    let currentMatch = currentMatches.find(m => m.route.id === match.route.id);\n    let isRevalidatingLoader = currentMatch != null && !isNewRouteInstance(currentMatch, match) && (currentLoaderData && currentLoaderData[match.route.id]) !== undefined;\n    if (isDeferredResult(result) && isRevalidatingLoader) {\n      // Note: we do not have to touch activeDeferreds here since we race them\n      // against the signal in resolveDeferredData and they'll get aborted\n      // there if needed\n      await resolveDeferredData(result, signal, false).then(result => {\n        if (result) {\n          results[routeId] = result;\n        }\n      });\n    }\n  }\n}\nasync function resolveFetcherDeferredResults(matches, results, revalidatingFetchers) {\n  for (let index = 0; index < revalidatingFetchers.length; index++) {\n    let {\n      key,\n      routeId,\n      controller\n    } = revalidatingFetchers[index];\n    let result = results[key];\n    let match = matches.find(m => (m == null ? void 0 : m.route.id) === routeId);\n    // If we don't have a match, then we can have a deferred result to do\n    // anything with.  This is for revalidating fetchers where the route was\n    // removed during HMR\n    if (!match) {\n      continue;\n    }\n    if (isDeferredResult(result)) {\n      // Note: we do not have to touch activeDeferreds here since we race them\n      // against the signal in resolveDeferredData and they'll get aborted\n      // there if needed\n      invariant(controller, \"Expected an AbortController for revalidating fetcher deferred result\");\n      await resolveDeferredData(result, controller.signal, true).then(result => {\n        if (result) {\n          results[key] = result;\n        }\n      });\n    }\n  }\n}\nasync function resolveDeferredData(result, signal, unwrap) {\n  if (unwrap === void 0) {\n    unwrap = false;\n  }\n  let aborted = await result.deferredData.resolveData(signal);\n  if (aborted) {\n    return;\n  }\n  if (unwrap) {\n    try {\n      return {\n        type: ResultType.data,\n        data: result.deferredData.unwrappedData\n      };\n    } catch (e) {\n      // Handle any TrackedPromise._error values encountered while unwrapping\n      return {\n        type: ResultType.error,\n        error: e\n      };\n    }\n  }\n  return {\n    type: ResultType.data,\n    data: result.deferredData.data\n  };\n}\nfunction hasNakedIndexQuery(search) {\n  return new URLSearchParams(search).getAll(\"index\").some(v => v === \"\");\n}\nfunction getTargetMatch(matches, location) {\n  let search = typeof location === \"string\" ? parsePath(location).search : location.search;\n  if (matches[matches.length - 1].route.index && hasNakedIndexQuery(search || \"\")) {\n    // Return the leaf index route when index is present\n    return matches[matches.length - 1];\n  }\n  // Otherwise grab the deepest \"path contributing\" match (ignoring index and\n  // pathless layout routes)\n  let pathMatches = getPathContributingMatches(matches);\n  return pathMatches[pathMatches.length - 1];\n}\nfunction getSubmissionFromNavigation(navigation) {\n  let {\n    formMethod,\n    formAction,\n    formEncType,\n    text,\n    formData,\n    json\n  } = navigation;\n  if (!formMethod || !formAction || !formEncType) {\n    return;\n  }\n  if (text != null) {\n    return {\n      formMethod,\n      formAction,\n      formEncType,\n      formData: undefined,\n      json: undefined,\n      text\n    };\n  } else if (formData != null) {\n    return {\n      formMethod,\n      formAction,\n      formEncType,\n      formData,\n      json: undefined,\n      text: undefined\n    };\n  } else if (json !== undefined) {\n    return {\n      formMethod,\n      formAction,\n      formEncType,\n      formData: undefined,\n      json,\n      text: undefined\n    };\n  }\n}\nfunction getLoadingNavigation(location, submission) {\n  if (submission) {\n    let navigation = {\n      state: \"loading\",\n      location,\n      formMethod: submission.formMethod,\n      formAction: submission.formAction,\n      formEncType: submission.formEncType,\n      formData: submission.formData,\n      json: submission.json,\n      text: submission.text\n    };\n    return navigation;\n  } else {\n    let navigation = {\n      state: \"loading\",\n      location,\n      formMethod: undefined,\n      formAction: undefined,\n      formEncType: undefined,\n      formData: undefined,\n      json: undefined,\n      text: undefined\n    };\n    return navigation;\n  }\n}\nfunction getSubmittingNavigation(location, submission) {\n  let navigation = {\n    state: \"submitting\",\n    location,\n    formMethod: submission.formMethod,\n    formAction: submission.formAction,\n    formEncType: submission.formEncType,\n    formData: submission.formData,\n    json: submission.json,\n    text: submission.text\n  };\n  return navigation;\n}\nfunction getLoadingFetcher(submission, data) {\n  if (submission) {\n    let fetcher = {\n      state: \"loading\",\n      formMethod: submission.formMethod,\n      formAction: submission.formAction,\n      formEncType: submission.formEncType,\n      formData: submission.formData,\n      json: submission.json,\n      text: submission.text,\n      data\n    };\n    return fetcher;\n  } else {\n    let fetcher = {\n      state: \"loading\",\n      formMethod: undefined,\n      formAction: undefined,\n      formEncType: undefined,\n      formData: undefined,\n      json: undefined,\n      text: undefined,\n      data\n    };\n    return fetcher;\n  }\n}\nfunction getSubmittingFetcher(submission, existingFetcher) {\n  let fetcher = {\n    state: \"submitting\",\n    formMethod: submission.formMethod,\n    formAction: submission.formAction,\n    formEncType: submission.formEncType,\n    formData: submission.formData,\n    json: submission.json,\n    text: submission.text,\n    data: existingFetcher ? existingFetcher.data : undefined\n  };\n  return fetcher;\n}\nfunction getDoneFetcher(data) {\n  let fetcher = {\n    state: \"idle\",\n    formMethod: undefined,\n    formAction: undefined,\n    formEncType: undefined,\n    formData: undefined,\n    json: undefined,\n    text: undefined,\n    data\n  };\n  return fetcher;\n}\nfunction restoreAppliedTransitions(_window, transitions) {\n  try {\n    let sessionPositions = _window.sessionStorage.getItem(TRANSITIONS_STORAGE_KEY);\n    if (sessionPositions) {\n      let json = JSON.parse(sessionPositions);\n      for (let [k, v] of Object.entries(json || {})) {\n        if (v && Array.isArray(v)) {\n          transitions.set(k, new Set(v || []));\n        }\n      }\n    }\n  } catch (e) {\n    // no-op, use default empty object\n  }\n}\nfunction persistAppliedTransitions(_window, transitions) {\n  if (transitions.size > 0) {\n    let json = {};\n    for (let [k, v] of transitions) {\n      json[k] = [...v];\n    }\n    try {\n      _window.sessionStorage.setItem(TRANSITIONS_STORAGE_KEY, JSON.stringify(json));\n    } catch (error) {\n      warning(false, \"Failed to save applied view transitions in sessionStorage (\" + error + \").\");\n    }\n  }\n}\n//#endregion\n\nexport { AbortedDeferredError, Action, IDLE_BLOCKER, IDLE_FETCHER, IDLE_NAVIGATION, UNSAFE_DEFERRED_SYMBOL, DeferredData as UNSAFE_DeferredData, ErrorResponseImpl as UNSAFE_ErrorResponseImpl, convertRouteMatchToUiMatch as UNSAFE_convertRouteMatchToUiMatch, convertRoutesToDataRoutes as UNSAFE_convertRoutesToDataRoutes, decodePath as UNSAFE_decodePath, getResolveToMatches as UNSAFE_getResolveToMatches, invariant as UNSAFE_invariant, warning as UNSAFE_warning, createBrowserHistory, createHashHistory, createMemoryHistory, createPath, createRouter, createStaticHandler, data, defer, generatePath, getStaticContextFromError, getToPathname, isDataWithResponseInit, isDeferredData, isRouteErrorResponse, joinPaths, json, matchPath, matchRoutes, normalizePathname, parsePath, redirect, redirectDocument, replace, resolvePath, resolveTo, stripBasename };\n//# sourceMappingURL=router.js.map\n", "/**\n * React Router v6.29.0\n *\n * Copyright (c) Remix Software Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE.md file in the root directory of this source tree.\n *\n * @license MIT\n */\nimport * as React from 'react';\nimport { UNSAFE_invariant, joinPaths, matchPath, UNSAFE_decodePath, UNSAFE_getResolveToMatches, UNSAFE_warning, resolveTo, parsePath, matchRoutes, Action, UNSAFE_convertRouteMatchToUiMatch, stripBasename, IDLE_BLOCKER, isRouteErrorResponse, createMemoryHistory, AbortedDeferredError, createRouter } from '@remix-run/router';\nexport { AbortedDeferredError, Action as NavigationType, createPath, defer, generatePath, isRouteErrorResponse, json, matchPath, matchRoutes, parsePath, redirect, redirectDocument, replace, resolvePath } from '@remix-run/router';\n\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\n\n// Create react-specific types from the agnostic types in @remix-run/router to\n// export from react-router\nconst DataRouterContext = /*#__PURE__*/React.createContext(null);\nif (process.env.NODE_ENV !== \"production\") {\n  DataRouterContext.displayName = \"DataRouter\";\n}\nconst DataRouterStateContext = /*#__PURE__*/React.createContext(null);\nif (process.env.NODE_ENV !== \"production\") {\n  DataRouterStateContext.displayName = \"DataRouterState\";\n}\nconst AwaitContext = /*#__PURE__*/React.createContext(null);\nif (process.env.NODE_ENV !== \"production\") {\n  AwaitContext.displayName = \"Await\";\n}\n\n/**\n * A Navigator is a \"location changer\"; it's how you get to different locations.\n *\n * Every history instance conforms to the Navigator interface, but the\n * distinction is useful primarily when it comes to the low-level `<Router>` API\n * where both the location and a navigator must be provided separately in order\n * to avoid \"tearing\" that may occur in a suspense-enabled app if the action\n * and/or location were to be read directly from the history instance.\n */\n\nconst NavigationContext = /*#__PURE__*/React.createContext(null);\nif (process.env.NODE_ENV !== \"production\") {\n  NavigationContext.displayName = \"Navigation\";\n}\nconst LocationContext = /*#__PURE__*/React.createContext(null);\nif (process.env.NODE_ENV !== \"production\") {\n  LocationContext.displayName = \"Location\";\n}\nconst RouteContext = /*#__PURE__*/React.createContext({\n  outlet: null,\n  matches: [],\n  isDataRoute: false\n});\nif (process.env.NODE_ENV !== \"production\") {\n  RouteContext.displayName = \"Route\";\n}\nconst RouteErrorContext = /*#__PURE__*/React.createContext(null);\nif (process.env.NODE_ENV !== \"production\") {\n  RouteErrorContext.displayName = \"RouteError\";\n}\n\n/**\n * Returns the full href for the given \"to\" value. This is useful for building\n * custom links that are also accessible and preserve right-click behavior.\n *\n * @see https://reactrouter.com/v6/hooks/use-href\n */\nfunction useHref(to, _temp) {\n  let {\n    relative\n  } = _temp === void 0 ? {} : _temp;\n  !useInRouterContext() ? process.env.NODE_ENV !== \"production\" ? UNSAFE_invariant(false, // TODO: This error is probably because they somehow have 2 versions of the\n  // router loaded. We can help them understand how to avoid that.\n  \"useHref() may be used only in the context of a <Router> component.\") : UNSAFE_invariant(false) : void 0;\n  let {\n    basename,\n    navigator\n  } = React.useContext(NavigationContext);\n  let {\n    hash,\n    pathname,\n    search\n  } = useResolvedPath(to, {\n    relative\n  });\n  let joinedPathname = pathname;\n\n  // If we're operating within a basename, prepend it to the pathname prior\n  // to creating the href.  If this is a root navigation, then just use the raw\n  // basename which allows the basename to have full control over the presence\n  // of a trailing slash on root links\n  if (basename !== \"/\") {\n    joinedPathname = pathname === \"/\" ? basename : joinPaths([basename, pathname]);\n  }\n  return navigator.createHref({\n    pathname: joinedPathname,\n    search,\n    hash\n  });\n}\n\n/**\n * Returns true if this component is a descendant of a `<Router>`.\n *\n * @see https://reactrouter.com/v6/hooks/use-in-router-context\n */\nfunction useInRouterContext() {\n  return React.useContext(LocationContext) != null;\n}\n\n/**\n * Returns the current location object, which represents the current URL in web\n * browsers.\n *\n * Note: If you're using this it may mean you're doing some of your own\n * \"routing\" in your app, and we'd like to know what your use case is. We may\n * be able to provide something higher-level to better suit your needs.\n *\n * @see https://reactrouter.com/v6/hooks/use-location\n */\nfunction useLocation() {\n  !useInRouterContext() ? process.env.NODE_ENV !== \"production\" ? UNSAFE_invariant(false, // TODO: This error is probably because they somehow have 2 versions of the\n  // router loaded. We can help them understand how to avoid that.\n  \"useLocation() may be used only in the context of a <Router> component.\") : UNSAFE_invariant(false) : void 0;\n  return React.useContext(LocationContext).location;\n}\n\n/**\n * Returns the current navigation action which describes how the router came to\n * the current location, either by a pop, push, or replace on the history stack.\n *\n * @see https://reactrouter.com/v6/hooks/use-navigation-type\n */\nfunction useNavigationType() {\n  return React.useContext(LocationContext).navigationType;\n}\n\n/**\n * Returns a PathMatch object if the given pattern matches the current URL.\n * This is useful for components that need to know \"active\" state, e.g.\n * `<NavLink>`.\n *\n * @see https://reactrouter.com/v6/hooks/use-match\n */\nfunction useMatch(pattern) {\n  !useInRouterContext() ? process.env.NODE_ENV !== \"production\" ? UNSAFE_invariant(false, // TODO: This error is probably because they somehow have 2 versions of the\n  // router loaded. We can help them understand how to avoid that.\n  \"useMatch() may be used only in the context of a <Router> component.\") : UNSAFE_invariant(false) : void 0;\n  let {\n    pathname\n  } = useLocation();\n  return React.useMemo(() => matchPath(pattern, UNSAFE_decodePath(pathname)), [pathname, pattern]);\n}\n\n/**\n * The interface for the navigate() function returned from useNavigate().\n */\n\nconst navigateEffectWarning = \"You should call navigate() in a React.useEffect(), not when \" + \"your component is first rendered.\";\n\n// Mute warnings for calls to useNavigate in SSR environments\nfunction useIsomorphicLayoutEffect(cb) {\n  let isStatic = React.useContext(NavigationContext).static;\n  if (!isStatic) {\n    // We should be able to get rid of this once react 18.3 is released\n    // See: https://github.com/facebook/react/pull/26395\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    React.useLayoutEffect(cb);\n  }\n}\n\n/**\n * Returns an imperative method for changing the location. Used by `<Link>`s, but\n * may also be used by other elements to change the location.\n *\n * @see https://reactrouter.com/v6/hooks/use-navigate\n */\nfunction useNavigate() {\n  let {\n    isDataRoute\n  } = React.useContext(RouteContext);\n  // Conditional usage is OK here because the usage of a data router is static\n  // eslint-disable-next-line react-hooks/rules-of-hooks\n  return isDataRoute ? useNavigateStable() : useNavigateUnstable();\n}\nfunction useNavigateUnstable() {\n  !useInRouterContext() ? process.env.NODE_ENV !== \"production\" ? UNSAFE_invariant(false, // TODO: This error is probably because they somehow have 2 versions of the\n  // router loaded. We can help them understand how to avoid that.\n  \"useNavigate() may be used only in the context of a <Router> component.\") : UNSAFE_invariant(false) : void 0;\n  let dataRouterContext = React.useContext(DataRouterContext);\n  let {\n    basename,\n    future,\n    navigator\n  } = React.useContext(NavigationContext);\n  let {\n    matches\n  } = React.useContext(RouteContext);\n  let {\n    pathname: locationPathname\n  } = useLocation();\n  let routePathnamesJson = JSON.stringify(UNSAFE_getResolveToMatches(matches, future.v7_relativeSplatPath));\n  let activeRef = React.useRef(false);\n  useIsomorphicLayoutEffect(() => {\n    activeRef.current = true;\n  });\n  let navigate = React.useCallback(function (to, options) {\n    if (options === void 0) {\n      options = {};\n    }\n    process.env.NODE_ENV !== \"production\" ? UNSAFE_warning(activeRef.current, navigateEffectWarning) : void 0;\n\n    // Short circuit here since if this happens on first render the navigate\n    // is useless because we haven't wired up our history listener yet\n    if (!activeRef.current) return;\n    if (typeof to === \"number\") {\n      navigator.go(to);\n      return;\n    }\n    let path = resolveTo(to, JSON.parse(routePathnamesJson), locationPathname, options.relative === \"path\");\n\n    // If we're operating within a basename, prepend it to the pathname prior\n    // to handing off to history (but only if we're not in a data router,\n    // otherwise it'll prepend the basename inside of the router).\n    // If this is a root navigation, then we navigate to the raw basename\n    // which allows the basename to have full control over the presence of a\n    // trailing slash on root links\n    if (dataRouterContext == null && basename !== \"/\") {\n      path.pathname = path.pathname === \"/\" ? basename : joinPaths([basename, path.pathname]);\n    }\n    (!!options.replace ? navigator.replace : navigator.push)(path, options.state, options);\n  }, [basename, navigator, routePathnamesJson, locationPathname, dataRouterContext]);\n  return navigate;\n}\nconst OutletContext = /*#__PURE__*/React.createContext(null);\n\n/**\n * Returns the context (if provided) for the child route at this level of the route\n * hierarchy.\n * @see https://reactrouter.com/v6/hooks/use-outlet-context\n */\nfunction useOutletContext() {\n  return React.useContext(OutletContext);\n}\n\n/**\n * Returns the element for the child route at this level of the route\n * hierarchy. Used internally by `<Outlet>` to render child routes.\n *\n * @see https://reactrouter.com/v6/hooks/use-outlet\n */\nfunction useOutlet(context) {\n  let outlet = React.useContext(RouteContext).outlet;\n  if (outlet) {\n    return /*#__PURE__*/React.createElement(OutletContext.Provider, {\n      value: context\n    }, outlet);\n  }\n  return outlet;\n}\n\n/**\n * Returns an object of key/value pairs of the dynamic params from the current\n * URL that were matched by the route path.\n *\n * @see https://reactrouter.com/v6/hooks/use-params\n */\nfunction useParams() {\n  let {\n    matches\n  } = React.useContext(RouteContext);\n  let routeMatch = matches[matches.length - 1];\n  return routeMatch ? routeMatch.params : {};\n}\n\n/**\n * Resolves the pathname of the given `to` value against the current location.\n *\n * @see https://reactrouter.com/v6/hooks/use-resolved-path\n */\nfunction useResolvedPath(to, _temp2) {\n  let {\n    relative\n  } = _temp2 === void 0 ? {} : _temp2;\n  let {\n    future\n  } = React.useContext(NavigationContext);\n  let {\n    matches\n  } = React.useContext(RouteContext);\n  let {\n    pathname: locationPathname\n  } = useLocation();\n  let routePathnamesJson = JSON.stringify(UNSAFE_getResolveToMatches(matches, future.v7_relativeSplatPath));\n  return React.useMemo(() => resolveTo(to, JSON.parse(routePathnamesJson), locationPathname, relative === \"path\"), [to, routePathnamesJson, locationPathname, relative]);\n}\n\n/**\n * Returns the element of the route that matched the current location, prepared\n * with the correct context to render the remainder of the route tree. Route\n * elements in the tree must render an `<Outlet>` to render their child route's\n * element.\n *\n * @see https://reactrouter.com/v6/hooks/use-routes\n */\nfunction useRoutes(routes, locationArg) {\n  return useRoutesImpl(routes, locationArg);\n}\n\n// Internal implementation with accept optional param for RouterProvider usage\nfunction useRoutesImpl(routes, locationArg, dataRouterState, future) {\n  !useInRouterContext() ? process.env.NODE_ENV !== \"production\" ? UNSAFE_invariant(false, // TODO: This error is probably because they somehow have 2 versions of the\n  // router loaded. We can help them understand how to avoid that.\n  \"useRoutes() may be used only in the context of a <Router> component.\") : UNSAFE_invariant(false) : void 0;\n  let {\n    navigator,\n    static: isStatic\n  } = React.useContext(NavigationContext);\n  let {\n    matches: parentMatches\n  } = React.useContext(RouteContext);\n  let routeMatch = parentMatches[parentMatches.length - 1];\n  let parentParams = routeMatch ? routeMatch.params : {};\n  let parentPathname = routeMatch ? routeMatch.pathname : \"/\";\n  let parentPathnameBase = routeMatch ? routeMatch.pathnameBase : \"/\";\n  let parentRoute = routeMatch && routeMatch.route;\n  if (process.env.NODE_ENV !== \"production\") {\n    // You won't get a warning about 2 different <Routes> under a <Route>\n    // without a trailing *, but this is a best-effort warning anyway since we\n    // cannot even give the warning unless they land at the parent route.\n    //\n    // Example:\n    //\n    // <Routes>\n    //   {/* This route path MUST end with /* because otherwise\n    //       it will never match /blog/post/123 */}\n    //   <Route path=\"blog\" element={<Blog />} />\n    //   <Route path=\"blog/feed\" element={<BlogFeed />} />\n    // </Routes>\n    //\n    // function Blog() {\n    //   return (\n    //     <Routes>\n    //       <Route path=\"post/:id\" element={<Post />} />\n    //     </Routes>\n    //   );\n    // }\n    let parentPath = parentRoute && parentRoute.path || \"\";\n    warningOnce(parentPathname, !parentRoute || parentPath.endsWith(\"*\"), \"You rendered descendant <Routes> (or called `useRoutes()`) at \" + (\"\\\"\" + parentPathname + \"\\\" (under <Route path=\\\"\" + parentPath + \"\\\">) but the \") + \"parent route path has no trailing \\\"*\\\". This means if you navigate \" + \"deeper, the parent won't match anymore and therefore the child \" + \"routes will never render.\\n\\n\" + (\"Please change the parent <Route path=\\\"\" + parentPath + \"\\\"> to <Route \") + (\"path=\\\"\" + (parentPath === \"/\" ? \"*\" : parentPath + \"/*\") + \"\\\">.\"));\n  }\n  let locationFromContext = useLocation();\n  let location;\n  if (locationArg) {\n    var _parsedLocationArg$pa;\n    let parsedLocationArg = typeof locationArg === \"string\" ? parsePath(locationArg) : locationArg;\n    !(parentPathnameBase === \"/\" || ((_parsedLocationArg$pa = parsedLocationArg.pathname) == null ? void 0 : _parsedLocationArg$pa.startsWith(parentPathnameBase))) ? process.env.NODE_ENV !== \"production\" ? UNSAFE_invariant(false, \"When overriding the location using `<Routes location>` or `useRoutes(routes, location)`, \" + \"the location pathname must begin with the portion of the URL pathname that was \" + (\"matched by all parent routes. The current pathname base is \\\"\" + parentPathnameBase + \"\\\" \") + (\"but pathname \\\"\" + parsedLocationArg.pathname + \"\\\" was given in the `location` prop.\")) : UNSAFE_invariant(false) : void 0;\n    location = parsedLocationArg;\n  } else {\n    location = locationFromContext;\n  }\n  let pathname = location.pathname || \"/\";\n  let remainingPathname = pathname;\n  if (parentPathnameBase !== \"/\") {\n    // Determine the remaining pathname by removing the # of URL segments the\n    // parentPathnameBase has, instead of removing based on character count.\n    // This is because we can't guarantee that incoming/outgoing encodings/\n    // decodings will match exactly.\n    // We decode paths before matching on a per-segment basis with\n    // decodeURIComponent(), but we re-encode pathnames via `new URL()` so they\n    // match what `window.location.pathname` would reflect.  Those don't 100%\n    // align when it comes to encoded URI characters such as % and &.\n    //\n    // So we may end up with:\n    //   pathname:           \"/descendant/a%25b/match\"\n    //   parentPathnameBase: \"/descendant/a%b\"\n    //\n    // And the direct substring removal approach won't work :/\n    let parentSegments = parentPathnameBase.replace(/^\\//, \"\").split(\"/\");\n    let segments = pathname.replace(/^\\//, \"\").split(\"/\");\n    remainingPathname = \"/\" + segments.slice(parentSegments.length).join(\"/\");\n  }\n  let matches = !isStatic && dataRouterState && dataRouterState.matches && dataRouterState.matches.length > 0 ? dataRouterState.matches : matchRoutes(routes, {\n    pathname: remainingPathname\n  });\n  if (process.env.NODE_ENV !== \"production\") {\n    process.env.NODE_ENV !== \"production\" ? UNSAFE_warning(parentRoute || matches != null, \"No routes matched location \\\"\" + location.pathname + location.search + location.hash + \"\\\" \") : void 0;\n    process.env.NODE_ENV !== \"production\" ? UNSAFE_warning(matches == null || matches[matches.length - 1].route.element !== undefined || matches[matches.length - 1].route.Component !== undefined || matches[matches.length - 1].route.lazy !== undefined, \"Matched leaf route at location \\\"\" + location.pathname + location.search + location.hash + \"\\\" \" + \"does not have an element or Component. This means it will render an <Outlet /> with a \" + \"null value by default resulting in an \\\"empty\\\" page.\") : void 0;\n  }\n  let renderedMatches = _renderMatches(matches && matches.map(match => Object.assign({}, match, {\n    params: Object.assign({}, parentParams, match.params),\n    pathname: joinPaths([parentPathnameBase,\n    // Re-encode pathnames that were decoded inside matchRoutes\n    navigator.encodeLocation ? navigator.encodeLocation(match.pathname).pathname : match.pathname]),\n    pathnameBase: match.pathnameBase === \"/\" ? parentPathnameBase : joinPaths([parentPathnameBase,\n    // Re-encode pathnames that were decoded inside matchRoutes\n    navigator.encodeLocation ? navigator.encodeLocation(match.pathnameBase).pathname : match.pathnameBase])\n  })), parentMatches, dataRouterState, future);\n\n  // When a user passes in a `locationArg`, the associated routes need to\n  // be wrapped in a new `LocationContext.Provider` in order for `useLocation`\n  // to use the scoped location instead of the global location.\n  if (locationArg && renderedMatches) {\n    return /*#__PURE__*/React.createElement(LocationContext.Provider, {\n      value: {\n        location: _extends({\n          pathname: \"/\",\n          search: \"\",\n          hash: \"\",\n          state: null,\n          key: \"default\"\n        }, location),\n        navigationType: Action.Pop\n      }\n    }, renderedMatches);\n  }\n  return renderedMatches;\n}\nfunction DefaultErrorComponent() {\n  let error = useRouteError();\n  let message = isRouteErrorResponse(error) ? error.status + \" \" + error.statusText : error instanceof Error ? error.message : JSON.stringify(error);\n  let stack = error instanceof Error ? error.stack : null;\n  let lightgrey = \"rgba(200,200,200, 0.5)\";\n  let preStyles = {\n    padding: \"0.5rem\",\n    backgroundColor: lightgrey\n  };\n  let codeStyles = {\n    padding: \"2px 4px\",\n    backgroundColor: lightgrey\n  };\n  let devInfo = null;\n  if (process.env.NODE_ENV !== \"production\") {\n    console.error(\"Error handled by React Router default ErrorBoundary:\", error);\n    devInfo = /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"p\", null, \"\\uD83D\\uDCBF Hey developer \\uD83D\\uDC4B\"), /*#__PURE__*/React.createElement(\"p\", null, \"You can provide a way better UX than this when your app throws errors by providing your own \", /*#__PURE__*/React.createElement(\"code\", {\n      style: codeStyles\n    }, \"ErrorBoundary\"), \" or\", \" \", /*#__PURE__*/React.createElement(\"code\", {\n      style: codeStyles\n    }, \"errorElement\"), \" prop on your route.\"));\n  }\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"h2\", null, \"Unexpected Application Error!\"), /*#__PURE__*/React.createElement(\"h3\", {\n    style: {\n      fontStyle: \"italic\"\n    }\n  }, message), stack ? /*#__PURE__*/React.createElement(\"pre\", {\n    style: preStyles\n  }, stack) : null, devInfo);\n}\nconst defaultErrorElement = /*#__PURE__*/React.createElement(DefaultErrorComponent, null);\nclass RenderErrorBoundary extends React.Component {\n  constructor(props) {\n    super(props);\n    this.state = {\n      location: props.location,\n      revalidation: props.revalidation,\n      error: props.error\n    };\n  }\n  static getDerivedStateFromError(error) {\n    return {\n      error: error\n    };\n  }\n  static getDerivedStateFromProps(props, state) {\n    // When we get into an error state, the user will likely click \"back\" to the\n    // previous page that didn't have an error. Because this wraps the entire\n    // application, that will have no effect--the error page continues to display.\n    // This gives us a mechanism to recover from the error when the location changes.\n    //\n    // Whether we're in an error state or not, we update the location in state\n    // so that when we are in an error state, it gets reset when a new location\n    // comes in and the user recovers from the error.\n    if (state.location !== props.location || state.revalidation !== \"idle\" && props.revalidation === \"idle\") {\n      return {\n        error: props.error,\n        location: props.location,\n        revalidation: props.revalidation\n      };\n    }\n\n    // If we're not changing locations, preserve the location but still surface\n    // any new errors that may come through. We retain the existing error, we do\n    // this because the error provided from the app state may be cleared without\n    // the location changing.\n    return {\n      error: props.error !== undefined ? props.error : state.error,\n      location: state.location,\n      revalidation: props.revalidation || state.revalidation\n    };\n  }\n  componentDidCatch(error, errorInfo) {\n    console.error(\"React Router caught the following error during render\", error, errorInfo);\n  }\n  render() {\n    return this.state.error !== undefined ? /*#__PURE__*/React.createElement(RouteContext.Provider, {\n      value: this.props.routeContext\n    }, /*#__PURE__*/React.createElement(RouteErrorContext.Provider, {\n      value: this.state.error,\n      children: this.props.component\n    })) : this.props.children;\n  }\n}\nfunction RenderedRoute(_ref) {\n  let {\n    routeContext,\n    match,\n    children\n  } = _ref;\n  let dataRouterContext = React.useContext(DataRouterContext);\n\n  // Track how deep we got in our render pass to emulate SSR componentDidCatch\n  // in a DataStaticRouter\n  if (dataRouterContext && dataRouterContext.static && dataRouterContext.staticContext && (match.route.errorElement || match.route.ErrorBoundary)) {\n    dataRouterContext.staticContext._deepestRenderedBoundaryId = match.route.id;\n  }\n  return /*#__PURE__*/React.createElement(RouteContext.Provider, {\n    value: routeContext\n  }, children);\n}\nfunction _renderMatches(matches, parentMatches, dataRouterState, future) {\n  var _dataRouterState;\n  if (parentMatches === void 0) {\n    parentMatches = [];\n  }\n  if (dataRouterState === void 0) {\n    dataRouterState = null;\n  }\n  if (future === void 0) {\n    future = null;\n  }\n  if (matches == null) {\n    var _future;\n    if (!dataRouterState) {\n      return null;\n    }\n    if (dataRouterState.errors) {\n      // Don't bail if we have data router errors so we can render them in the\n      // boundary.  Use the pre-matched (or shimmed) matches\n      matches = dataRouterState.matches;\n    } else if ((_future = future) != null && _future.v7_partialHydration && parentMatches.length === 0 && !dataRouterState.initialized && dataRouterState.matches.length > 0) {\n      // Don't bail if we're initializing with partial hydration and we have\n      // router matches.  That means we're actively running `patchRoutesOnNavigation`\n      // so we should render down the partial matches to the appropriate\n      // `HydrateFallback`.  We only do this if `parentMatches` is empty so it\n      // only impacts the root matches for `RouterProvider` and no descendant\n      // `<Routes>`\n      matches = dataRouterState.matches;\n    } else {\n      return null;\n    }\n  }\n  let renderedMatches = matches;\n\n  // If we have data errors, trim matches to the highest error boundary\n  let errors = (_dataRouterState = dataRouterState) == null ? void 0 : _dataRouterState.errors;\n  if (errors != null) {\n    let errorIndex = renderedMatches.findIndex(m => m.route.id && (errors == null ? void 0 : errors[m.route.id]) !== undefined);\n    !(errorIndex >= 0) ? process.env.NODE_ENV !== \"production\" ? UNSAFE_invariant(false, \"Could not find a matching route for errors on route IDs: \" + Object.keys(errors).join(\",\")) : UNSAFE_invariant(false) : void 0;\n    renderedMatches = renderedMatches.slice(0, Math.min(renderedMatches.length, errorIndex + 1));\n  }\n\n  // If we're in a partial hydration mode, detect if we need to render down to\n  // a given HydrateFallback while we load the rest of the hydration data\n  let renderFallback = false;\n  let fallbackIndex = -1;\n  if (dataRouterState && future && future.v7_partialHydration) {\n    for (let i = 0; i < renderedMatches.length; i++) {\n      let match = renderedMatches[i];\n      // Track the deepest fallback up until the first route without data\n      if (match.route.HydrateFallback || match.route.hydrateFallbackElement) {\n        fallbackIndex = i;\n      }\n      if (match.route.id) {\n        let {\n          loaderData,\n          errors\n        } = dataRouterState;\n        let needsToRunLoader = match.route.loader && loaderData[match.route.id] === undefined && (!errors || errors[match.route.id] === undefined);\n        if (match.route.lazy || needsToRunLoader) {\n          // We found the first route that's not ready to render (waiting on\n          // lazy, or has a loader that hasn't run yet).  Flag that we need to\n          // render a fallback and render up until the appropriate fallback\n          renderFallback = true;\n          if (fallbackIndex >= 0) {\n            renderedMatches = renderedMatches.slice(0, fallbackIndex + 1);\n          } else {\n            renderedMatches = [renderedMatches[0]];\n          }\n          break;\n        }\n      }\n    }\n  }\n  return renderedMatches.reduceRight((outlet, match, index) => {\n    // Only data routers handle errors/fallbacks\n    let error;\n    let shouldRenderHydrateFallback = false;\n    let errorElement = null;\n    let hydrateFallbackElement = null;\n    if (dataRouterState) {\n      error = errors && match.route.id ? errors[match.route.id] : undefined;\n      errorElement = match.route.errorElement || defaultErrorElement;\n      if (renderFallback) {\n        if (fallbackIndex < 0 && index === 0) {\n          warningOnce(\"route-fallback\", false, \"No `HydrateFallback` element provided to render during initial hydration\");\n          shouldRenderHydrateFallback = true;\n          hydrateFallbackElement = null;\n        } else if (fallbackIndex === index) {\n          shouldRenderHydrateFallback = true;\n          hydrateFallbackElement = match.route.hydrateFallbackElement || null;\n        }\n      }\n    }\n    let matches = parentMatches.concat(renderedMatches.slice(0, index + 1));\n    let getChildren = () => {\n      let children;\n      if (error) {\n        children = errorElement;\n      } else if (shouldRenderHydrateFallback) {\n        children = hydrateFallbackElement;\n      } else if (match.route.Component) {\n        // Note: This is a de-optimized path since React won't re-use the\n        // ReactElement since it's identity changes with each new\n        // React.createElement call.  We keep this so folks can use\n        // `<Route Component={...}>` in `<Routes>` but generally `Component`\n        // usage is only advised in `RouterProvider` when we can convert it to\n        // `element` ahead of time.\n        children = /*#__PURE__*/React.createElement(match.route.Component, null);\n      } else if (match.route.element) {\n        children = match.route.element;\n      } else {\n        children = outlet;\n      }\n      return /*#__PURE__*/React.createElement(RenderedRoute, {\n        match: match,\n        routeContext: {\n          outlet,\n          matches,\n          isDataRoute: dataRouterState != null\n        },\n        children: children\n      });\n    };\n    // Only wrap in an error boundary within data router usages when we have an\n    // ErrorBoundary/errorElement on this route.  Otherwise let it bubble up to\n    // an ancestor ErrorBoundary/errorElement\n    return dataRouterState && (match.route.ErrorBoundary || match.route.errorElement || index === 0) ? /*#__PURE__*/React.createElement(RenderErrorBoundary, {\n      location: dataRouterState.location,\n      revalidation: dataRouterState.revalidation,\n      component: errorElement,\n      error: error,\n      children: getChildren(),\n      routeContext: {\n        outlet: null,\n        matches,\n        isDataRoute: true\n      }\n    }) : getChildren();\n  }, null);\n}\nvar DataRouterHook = /*#__PURE__*/function (DataRouterHook) {\n  DataRouterHook[\"UseBlocker\"] = \"useBlocker\";\n  DataRouterHook[\"UseRevalidator\"] = \"useRevalidator\";\n  DataRouterHook[\"UseNavigateStable\"] = \"useNavigate\";\n  return DataRouterHook;\n}(DataRouterHook || {});\nvar DataRouterStateHook = /*#__PURE__*/function (DataRouterStateHook) {\n  DataRouterStateHook[\"UseBlocker\"] = \"useBlocker\";\n  DataRouterStateHook[\"UseLoaderData\"] = \"useLoaderData\";\n  DataRouterStateHook[\"UseActionData\"] = \"useActionData\";\n  DataRouterStateHook[\"UseRouteError\"] = \"useRouteError\";\n  DataRouterStateHook[\"UseNavigation\"] = \"useNavigation\";\n  DataRouterStateHook[\"UseRouteLoaderData\"] = \"useRouteLoaderData\";\n  DataRouterStateHook[\"UseMatches\"] = \"useMatches\";\n  DataRouterStateHook[\"UseRevalidator\"] = \"useRevalidator\";\n  DataRouterStateHook[\"UseNavigateStable\"] = \"useNavigate\";\n  DataRouterStateHook[\"UseRouteId\"] = \"useRouteId\";\n  return DataRouterStateHook;\n}(DataRouterStateHook || {});\nfunction getDataRouterConsoleError(hookName) {\n  return hookName + \" must be used within a data router.  See https://reactrouter.com/v6/routers/picking-a-router.\";\n}\nfunction useDataRouterContext(hookName) {\n  let ctx = React.useContext(DataRouterContext);\n  !ctx ? process.env.NODE_ENV !== \"production\" ? UNSAFE_invariant(false, getDataRouterConsoleError(hookName)) : UNSAFE_invariant(false) : void 0;\n  return ctx;\n}\nfunction useDataRouterState(hookName) {\n  let state = React.useContext(DataRouterStateContext);\n  !state ? process.env.NODE_ENV !== \"production\" ? UNSAFE_invariant(false, getDataRouterConsoleError(hookName)) : UNSAFE_invariant(false) : void 0;\n  return state;\n}\nfunction useRouteContext(hookName) {\n  let route = React.useContext(RouteContext);\n  !route ? process.env.NODE_ENV !== \"production\" ? UNSAFE_invariant(false, getDataRouterConsoleError(hookName)) : UNSAFE_invariant(false) : void 0;\n  return route;\n}\n\n// Internal version with hookName-aware debugging\nfunction useCurrentRouteId(hookName) {\n  let route = useRouteContext(hookName);\n  let thisRoute = route.matches[route.matches.length - 1];\n  !thisRoute.route.id ? process.env.NODE_ENV !== \"production\" ? UNSAFE_invariant(false, hookName + \" can only be used on routes that contain a unique \\\"id\\\"\") : UNSAFE_invariant(false) : void 0;\n  return thisRoute.route.id;\n}\n\n/**\n * Returns the ID for the nearest contextual route\n */\nfunction useRouteId() {\n  return useCurrentRouteId(DataRouterStateHook.UseRouteId);\n}\n\n/**\n * Returns the current navigation, defaulting to an \"idle\" navigation when\n * no navigation is in progress\n */\nfunction useNavigation() {\n  let state = useDataRouterState(DataRouterStateHook.UseNavigation);\n  return state.navigation;\n}\n\n/**\n * Returns a revalidate function for manually triggering revalidation, as well\n * as the current state of any manual revalidations\n */\nfunction useRevalidator() {\n  let dataRouterContext = useDataRouterContext(DataRouterHook.UseRevalidator);\n  let state = useDataRouterState(DataRouterStateHook.UseRevalidator);\n  return React.useMemo(() => ({\n    revalidate: dataRouterContext.router.revalidate,\n    state: state.revalidation\n  }), [dataRouterContext.router.revalidate, state.revalidation]);\n}\n\n/**\n * Returns the active route matches, useful for accessing loaderData for\n * parent/child routes or the route \"handle\" property\n */\nfunction useMatches() {\n  let {\n    matches,\n    loaderData\n  } = useDataRouterState(DataRouterStateHook.UseMatches);\n  return React.useMemo(() => matches.map(m => UNSAFE_convertRouteMatchToUiMatch(m, loaderData)), [matches, loaderData]);\n}\n\n/**\n * Returns the loader data for the nearest ancestor Route loader\n */\nfunction useLoaderData() {\n  let state = useDataRouterState(DataRouterStateHook.UseLoaderData);\n  let routeId = useCurrentRouteId(DataRouterStateHook.UseLoaderData);\n  if (state.errors && state.errors[routeId] != null) {\n    console.error(\"You cannot `useLoaderData` in an errorElement (routeId: \" + routeId + \")\");\n    return undefined;\n  }\n  return state.loaderData[routeId];\n}\n\n/**\n * Returns the loaderData for the given routeId\n */\nfunction useRouteLoaderData(routeId) {\n  let state = useDataRouterState(DataRouterStateHook.UseRouteLoaderData);\n  return state.loaderData[routeId];\n}\n\n/**\n * Returns the action data for the nearest ancestor Route action\n */\nfunction useActionData() {\n  let state = useDataRouterState(DataRouterStateHook.UseActionData);\n  let routeId = useCurrentRouteId(DataRouterStateHook.UseLoaderData);\n  return state.actionData ? state.actionData[routeId] : undefined;\n}\n\n/**\n * Returns the nearest ancestor Route error, which could be a loader/action\n * error or a render error.  This is intended to be called from your\n * ErrorBoundary/errorElement to display a proper error message.\n */\nfunction useRouteError() {\n  var _state$errors;\n  let error = React.useContext(RouteErrorContext);\n  let state = useDataRouterState(DataRouterStateHook.UseRouteError);\n  let routeId = useCurrentRouteId(DataRouterStateHook.UseRouteError);\n\n  // If this was a render error, we put it in a RouteError context inside\n  // of RenderErrorBoundary\n  if (error !== undefined) {\n    return error;\n  }\n\n  // Otherwise look for errors from our data router state\n  return (_state$errors = state.errors) == null ? void 0 : _state$errors[routeId];\n}\n\n/**\n * Returns the happy-path data from the nearest ancestor `<Await />` value\n */\nfunction useAsyncValue() {\n  let value = React.useContext(AwaitContext);\n  return value == null ? void 0 : value._data;\n}\n\n/**\n * Returns the error from the nearest ancestor `<Await />` value\n */\nfunction useAsyncError() {\n  let value = React.useContext(AwaitContext);\n  return value == null ? void 0 : value._error;\n}\nlet blockerId = 0;\n\n/**\n * Allow the application to block navigations within the SPA and present the\n * user a confirmation dialog to confirm the navigation.  Mostly used to avoid\n * using half-filled form data.  This does not handle hard-reloads or\n * cross-origin navigations.\n */\nfunction useBlocker(shouldBlock) {\n  let {\n    router,\n    basename\n  } = useDataRouterContext(DataRouterHook.UseBlocker);\n  let state = useDataRouterState(DataRouterStateHook.UseBlocker);\n  let [blockerKey, setBlockerKey] = React.useState(\"\");\n  let blockerFunction = React.useCallback(arg => {\n    if (typeof shouldBlock !== \"function\") {\n      return !!shouldBlock;\n    }\n    if (basename === \"/\") {\n      return shouldBlock(arg);\n    }\n\n    // If they provided us a function and we've got an active basename, strip\n    // it from the locations we expose to the user to match the behavior of\n    // useLocation\n    let {\n      currentLocation,\n      nextLocation,\n      historyAction\n    } = arg;\n    return shouldBlock({\n      currentLocation: _extends({}, currentLocation, {\n        pathname: stripBasename(currentLocation.pathname, basename) || currentLocation.pathname\n      }),\n      nextLocation: _extends({}, nextLocation, {\n        pathname: stripBasename(nextLocation.pathname, basename) || nextLocation.pathname\n      }),\n      historyAction\n    });\n  }, [basename, shouldBlock]);\n\n  // This effect is in charge of blocker key assignment and deletion (which is\n  // tightly coupled to the key)\n  React.useEffect(() => {\n    let key = String(++blockerId);\n    setBlockerKey(key);\n    return () => router.deleteBlocker(key);\n  }, [router]);\n\n  // This effect handles assigning the blockerFunction.  This is to handle\n  // unstable blocker function identities, and happens only after the prior\n  // effect so we don't get an orphaned blockerFunction in the router with a\n  // key of \"\".  Until then we just have the IDLE_BLOCKER.\n  React.useEffect(() => {\n    if (blockerKey !== \"\") {\n      router.getBlocker(blockerKey, blockerFunction);\n    }\n  }, [router, blockerKey, blockerFunction]);\n\n  // Prefer the blocker from `state` not `router.state` since DataRouterContext\n  // is memoized so this ensures we update on blocker state updates\n  return blockerKey && state.blockers.has(blockerKey) ? state.blockers.get(blockerKey) : IDLE_BLOCKER;\n}\n\n/**\n * Stable version of useNavigate that is used when we are in the context of\n * a RouterProvider.\n */\nfunction useNavigateStable() {\n  let {\n    router\n  } = useDataRouterContext(DataRouterHook.UseNavigateStable);\n  let id = useCurrentRouteId(DataRouterStateHook.UseNavigateStable);\n  let activeRef = React.useRef(false);\n  useIsomorphicLayoutEffect(() => {\n    activeRef.current = true;\n  });\n  let navigate = React.useCallback(function (to, options) {\n    if (options === void 0) {\n      options = {};\n    }\n    process.env.NODE_ENV !== \"production\" ? UNSAFE_warning(activeRef.current, navigateEffectWarning) : void 0;\n\n    // Short circuit here since if this happens on first render the navigate\n    // is useless because we haven't wired up our router subscriber yet\n    if (!activeRef.current) return;\n    if (typeof to === \"number\") {\n      router.navigate(to);\n    } else {\n      router.navigate(to, _extends({\n        fromRouteId: id\n      }, options));\n    }\n  }, [router, id]);\n  return navigate;\n}\nconst alreadyWarned$1 = {};\nfunction warningOnce(key, cond, message) {\n  if (!cond && !alreadyWarned$1[key]) {\n    alreadyWarned$1[key] = true;\n    process.env.NODE_ENV !== \"production\" ? UNSAFE_warning(false, message) : void 0;\n  }\n}\n\nconst alreadyWarned = {};\nfunction warnOnce(key, message) {\n  if (process.env.NODE_ENV !== \"production\" && !alreadyWarned[message]) {\n    alreadyWarned[message] = true;\n    console.warn(message);\n  }\n}\nconst logDeprecation = (flag, msg, link) => warnOnce(flag, \"\\u26A0\\uFE0F React Router Future Flag Warning: \" + msg + \". \" + (\"You can use the `\" + flag + \"` future flag to opt-in early. \") + (\"For more information, see \" + link + \".\"));\nfunction logV6DeprecationWarnings(renderFuture, routerFuture) {\n  if ((renderFuture == null ? void 0 : renderFuture.v7_startTransition) === undefined) {\n    logDeprecation(\"v7_startTransition\", \"React Router will begin wrapping state updates in `React.startTransition` in v7\", \"https://reactrouter.com/v6/upgrading/future#v7_starttransition\");\n  }\n  if ((renderFuture == null ? void 0 : renderFuture.v7_relativeSplatPath) === undefined && (!routerFuture || !routerFuture.v7_relativeSplatPath)) {\n    logDeprecation(\"v7_relativeSplatPath\", \"Relative route resolution within Splat routes is changing in v7\", \"https://reactrouter.com/v6/upgrading/future#v7_relativesplatpath\");\n  }\n  if (routerFuture) {\n    if (routerFuture.v7_fetcherPersist === undefined) {\n      logDeprecation(\"v7_fetcherPersist\", \"The persistence behavior of fetchers is changing in v7\", \"https://reactrouter.com/v6/upgrading/future#v7_fetcherpersist\");\n    }\n    if (routerFuture.v7_normalizeFormMethod === undefined) {\n      logDeprecation(\"v7_normalizeFormMethod\", \"Casing of `formMethod` fields is being normalized to uppercase in v7\", \"https://reactrouter.com/v6/upgrading/future#v7_normalizeformmethod\");\n    }\n    if (routerFuture.v7_partialHydration === undefined) {\n      logDeprecation(\"v7_partialHydration\", \"`RouterProvider` hydration behavior is changing in v7\", \"https://reactrouter.com/v6/upgrading/future#v7_partialhydration\");\n    }\n    if (routerFuture.v7_skipActionErrorRevalidation === undefined) {\n      logDeprecation(\"v7_skipActionErrorRevalidation\", \"The revalidation behavior after 4xx/5xx `action` responses is changing in v7\", \"https://reactrouter.com/v6/upgrading/future#v7_skipactionerrorrevalidation\");\n    }\n  }\n}\n\n/**\n  Webpack + React 17 fails to compile on any of the following because webpack\n  complains that `startTransition` doesn't exist in `React`:\n  * import { startTransition } from \"react\"\n  * import * as React from from \"react\";\n    \"startTransition\" in React ? React.startTransition(() => setState()) : setState()\n  * import * as React from from \"react\";\n    \"startTransition\" in React ? React[\"startTransition\"](() => setState()) : setState()\n\n  Moving it to a constant such as the following solves the Webpack/React 17 issue:\n  * import * as React from from \"react\";\n    const START_TRANSITION = \"startTransition\";\n    START_TRANSITION in React ? React[START_TRANSITION](() => setState()) : setState()\n\n  However, that introduces webpack/terser minification issues in production builds\n  in React 18 where minification/obfuscation ends up removing the call of\n  React.startTransition entirely from the first half of the ternary.  Grabbing\n  this exported reference once up front resolves that issue.\n\n  See https://github.com/remix-run/react-router/issues/10579\n*/\nconst START_TRANSITION = \"startTransition\";\nconst startTransitionImpl = React[START_TRANSITION];\n\n/**\n * Given a Remix Router instance, render the appropriate UI\n */\nfunction RouterProvider(_ref) {\n  let {\n    fallbackElement,\n    router,\n    future\n  } = _ref;\n  let [state, setStateImpl] = React.useState(router.state);\n  let {\n    v7_startTransition\n  } = future || {};\n  let setState = React.useCallback(newState => {\n    if (v7_startTransition && startTransitionImpl) {\n      startTransitionImpl(() => setStateImpl(newState));\n    } else {\n      setStateImpl(newState);\n    }\n  }, [setStateImpl, v7_startTransition]);\n\n  // Need to use a layout effect here so we are subscribed early enough to\n  // pick up on any render-driven redirects/navigations (useEffect/<Navigate>)\n  React.useLayoutEffect(() => router.subscribe(setState), [router, setState]);\n  React.useEffect(() => {\n    process.env.NODE_ENV !== \"production\" ? UNSAFE_warning(fallbackElement == null || !router.future.v7_partialHydration, \"`<RouterProvider fallbackElement>` is deprecated when using \" + \"`v7_partialHydration`, use a `HydrateFallback` component instead\") : void 0;\n    // Only log this once on initial mount\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n  let navigator = React.useMemo(() => {\n    return {\n      createHref: router.createHref,\n      encodeLocation: router.encodeLocation,\n      go: n => router.navigate(n),\n      push: (to, state, opts) => router.navigate(to, {\n        state,\n        preventScrollReset: opts == null ? void 0 : opts.preventScrollReset\n      }),\n      replace: (to, state, opts) => router.navigate(to, {\n        replace: true,\n        state,\n        preventScrollReset: opts == null ? void 0 : opts.preventScrollReset\n      })\n    };\n  }, [router]);\n  let basename = router.basename || \"/\";\n  let dataRouterContext = React.useMemo(() => ({\n    router,\n    navigator,\n    static: false,\n    basename\n  }), [router, navigator, basename]);\n  React.useEffect(() => logV6DeprecationWarnings(future, router.future), [router, future]);\n\n  // The fragment and {null} here are important!  We need them to keep React 18's\n  // useId happy when we are server-rendering since we may have a <script> here\n  // containing the hydrated server-side staticContext (from StaticRouterProvider).\n  // useId relies on the component tree structure to generate deterministic id's\n  // so we need to ensure it remains the same on the client even though\n  // we don't need the <script> tag\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(DataRouterContext.Provider, {\n    value: dataRouterContext\n  }, /*#__PURE__*/React.createElement(DataRouterStateContext.Provider, {\n    value: state\n  }, /*#__PURE__*/React.createElement(Router, {\n    basename: basename,\n    location: state.location,\n    navigationType: state.historyAction,\n    navigator: navigator,\n    future: {\n      v7_relativeSplatPath: router.future.v7_relativeSplatPath\n    }\n  }, state.initialized || router.future.v7_partialHydration ? /*#__PURE__*/React.createElement(DataRoutes, {\n    routes: router.routes,\n    future: router.future,\n    state: state\n  }) : fallbackElement))), null);\n}\nfunction DataRoutes(_ref2) {\n  let {\n    routes,\n    future,\n    state\n  } = _ref2;\n  return useRoutesImpl(routes, undefined, state, future);\n}\n/**\n * A `<Router>` that stores all entries in memory.\n *\n * @see https://reactrouter.com/v6/router-components/memory-router\n */\nfunction MemoryRouter(_ref3) {\n  let {\n    basename,\n    children,\n    initialEntries,\n    initialIndex,\n    future\n  } = _ref3;\n  let historyRef = React.useRef();\n  if (historyRef.current == null) {\n    historyRef.current = createMemoryHistory({\n      initialEntries,\n      initialIndex,\n      v5Compat: true\n    });\n  }\n  let history = historyRef.current;\n  let [state, setStateImpl] = React.useState({\n    action: history.action,\n    location: history.location\n  });\n  let {\n    v7_startTransition\n  } = future || {};\n  let setState = React.useCallback(newState => {\n    v7_startTransition && startTransitionImpl ? startTransitionImpl(() => setStateImpl(newState)) : setStateImpl(newState);\n  }, [setStateImpl, v7_startTransition]);\n  React.useLayoutEffect(() => history.listen(setState), [history, setState]);\n  React.useEffect(() => logV6DeprecationWarnings(future), [future]);\n  return /*#__PURE__*/React.createElement(Router, {\n    basename: basename,\n    children: children,\n    location: state.location,\n    navigationType: state.action,\n    navigator: history,\n    future: future\n  });\n}\n/**\n * Changes the current location.\n *\n * Note: This API is mostly useful in React.Component subclasses that are not\n * able to use hooks. In functional components, we recommend you use the\n * `useNavigate` hook instead.\n *\n * @see https://reactrouter.com/v6/components/navigate\n */\nfunction Navigate(_ref4) {\n  let {\n    to,\n    replace,\n    state,\n    relative\n  } = _ref4;\n  !useInRouterContext() ? process.env.NODE_ENV !== \"production\" ? UNSAFE_invariant(false, // TODO: This error is probably because they somehow have 2 versions of\n  // the router loaded. We can help them understand how to avoid that.\n  \"<Navigate> may be used only in the context of a <Router> component.\") : UNSAFE_invariant(false) : void 0;\n  let {\n    future,\n    static: isStatic\n  } = React.useContext(NavigationContext);\n  process.env.NODE_ENV !== \"production\" ? UNSAFE_warning(!isStatic, \"<Navigate> must not be used on the initial render in a <StaticRouter>. \" + \"This is a no-op, but you should modify your code so the <Navigate> is \" + \"only ever rendered in response to some user interaction or state change.\") : void 0;\n  let {\n    matches\n  } = React.useContext(RouteContext);\n  let {\n    pathname: locationPathname\n  } = useLocation();\n  let navigate = useNavigate();\n\n  // Resolve the path outside of the effect so that when effects run twice in\n  // StrictMode they navigate to the same place\n  let path = resolveTo(to, UNSAFE_getResolveToMatches(matches, future.v7_relativeSplatPath), locationPathname, relative === \"path\");\n  let jsonPath = JSON.stringify(path);\n  React.useEffect(() => navigate(JSON.parse(jsonPath), {\n    replace,\n    state,\n    relative\n  }), [navigate, jsonPath, relative, replace, state]);\n  return null;\n}\n/**\n * Renders the child route's element, if there is one.\n *\n * @see https://reactrouter.com/v6/components/outlet\n */\nfunction Outlet(props) {\n  return useOutlet(props.context);\n}\n/**\n * Declares an element that should be rendered at a certain URL path.\n *\n * @see https://reactrouter.com/v6/components/route\n */\nfunction Route(_props) {\n  process.env.NODE_ENV !== \"production\" ? UNSAFE_invariant(false, \"A <Route> is only ever to be used as the child of <Routes> element, \" + \"never rendered directly. Please wrap your <Route> in a <Routes>.\") : UNSAFE_invariant(false) ;\n}\n/**\n * Provides location context for the rest of the app.\n *\n * Note: You usually won't render a `<Router>` directly. Instead, you'll render a\n * router that is more specific to your environment such as a `<BrowserRouter>`\n * in web browsers or a `<StaticRouter>` for server rendering.\n *\n * @see https://reactrouter.com/v6/router-components/router\n */\nfunction Router(_ref5) {\n  let {\n    basename: basenameProp = \"/\",\n    children = null,\n    location: locationProp,\n    navigationType = Action.Pop,\n    navigator,\n    static: staticProp = false,\n    future\n  } = _ref5;\n  !!useInRouterContext() ? process.env.NODE_ENV !== \"production\" ? UNSAFE_invariant(false, \"You cannot render a <Router> inside another <Router>.\" + \" You should never have more than one in your app.\") : UNSAFE_invariant(false) : void 0;\n\n  // Preserve trailing slashes on basename, so we can let the user control\n  // the enforcement of trailing slashes throughout the app\n  let basename = basenameProp.replace(/^\\/*/, \"/\");\n  let navigationContext = React.useMemo(() => ({\n    basename,\n    navigator,\n    static: staticProp,\n    future: _extends({\n      v7_relativeSplatPath: false\n    }, future)\n  }), [basename, future, navigator, staticProp]);\n  if (typeof locationProp === \"string\") {\n    locationProp = parsePath(locationProp);\n  }\n  let {\n    pathname = \"/\",\n    search = \"\",\n    hash = \"\",\n    state = null,\n    key = \"default\"\n  } = locationProp;\n  let locationContext = React.useMemo(() => {\n    let trailingPathname = stripBasename(pathname, basename);\n    if (trailingPathname == null) {\n      return null;\n    }\n    return {\n      location: {\n        pathname: trailingPathname,\n        search,\n        hash,\n        state,\n        key\n      },\n      navigationType\n    };\n  }, [basename, pathname, search, hash, state, key, navigationType]);\n  process.env.NODE_ENV !== \"production\" ? UNSAFE_warning(locationContext != null, \"<Router basename=\\\"\" + basename + \"\\\"> is not able to match the URL \" + (\"\\\"\" + pathname + search + hash + \"\\\" because it does not start with the \") + \"basename, so the <Router> won't render anything.\") : void 0;\n  if (locationContext == null) {\n    return null;\n  }\n  return /*#__PURE__*/React.createElement(NavigationContext.Provider, {\n    value: navigationContext\n  }, /*#__PURE__*/React.createElement(LocationContext.Provider, {\n    children: children,\n    value: locationContext\n  }));\n}\n/**\n * A container for a nested tree of `<Route>` elements that renders the branch\n * that best matches the current location.\n *\n * @see https://reactrouter.com/v6/components/routes\n */\nfunction Routes(_ref6) {\n  let {\n    children,\n    location\n  } = _ref6;\n  return useRoutes(createRoutesFromChildren(children), location);\n}\n/**\n * Component to use for rendering lazily loaded data from returning defer()\n * in a loader function\n */\nfunction Await(_ref7) {\n  let {\n    children,\n    errorElement,\n    resolve\n  } = _ref7;\n  return /*#__PURE__*/React.createElement(AwaitErrorBoundary, {\n    resolve: resolve,\n    errorElement: errorElement\n  }, /*#__PURE__*/React.createElement(ResolveAwait, null, children));\n}\nvar AwaitRenderStatus = /*#__PURE__*/function (AwaitRenderStatus) {\n  AwaitRenderStatus[AwaitRenderStatus[\"pending\"] = 0] = \"pending\";\n  AwaitRenderStatus[AwaitRenderStatus[\"success\"] = 1] = \"success\";\n  AwaitRenderStatus[AwaitRenderStatus[\"error\"] = 2] = \"error\";\n  return AwaitRenderStatus;\n}(AwaitRenderStatus || {});\nconst neverSettledPromise = new Promise(() => {});\nclass AwaitErrorBoundary extends React.Component {\n  constructor(props) {\n    super(props);\n    this.state = {\n      error: null\n    };\n  }\n  static getDerivedStateFromError(error) {\n    return {\n      error\n    };\n  }\n  componentDidCatch(error, errorInfo) {\n    console.error(\"<Await> caught the following error during render\", error, errorInfo);\n  }\n  render() {\n    let {\n      children,\n      errorElement,\n      resolve\n    } = this.props;\n    let promise = null;\n    let status = AwaitRenderStatus.pending;\n    if (!(resolve instanceof Promise)) {\n      // Didn't get a promise - provide as a resolved promise\n      status = AwaitRenderStatus.success;\n      promise = Promise.resolve();\n      Object.defineProperty(promise, \"_tracked\", {\n        get: () => true\n      });\n      Object.defineProperty(promise, \"_data\", {\n        get: () => resolve\n      });\n    } else if (this.state.error) {\n      // Caught a render error, provide it as a rejected promise\n      status = AwaitRenderStatus.error;\n      let renderError = this.state.error;\n      promise = Promise.reject().catch(() => {}); // Avoid unhandled rejection warnings\n      Object.defineProperty(promise, \"_tracked\", {\n        get: () => true\n      });\n      Object.defineProperty(promise, \"_error\", {\n        get: () => renderError\n      });\n    } else if (resolve._tracked) {\n      // Already tracked promise - check contents\n      promise = resolve;\n      status = \"_error\" in promise ? AwaitRenderStatus.error : \"_data\" in promise ? AwaitRenderStatus.success : AwaitRenderStatus.pending;\n    } else {\n      // Raw (untracked) promise - track it\n      status = AwaitRenderStatus.pending;\n      Object.defineProperty(resolve, \"_tracked\", {\n        get: () => true\n      });\n      promise = resolve.then(data => Object.defineProperty(resolve, \"_data\", {\n        get: () => data\n      }), error => Object.defineProperty(resolve, \"_error\", {\n        get: () => error\n      }));\n    }\n    if (status === AwaitRenderStatus.error && promise._error instanceof AbortedDeferredError) {\n      // Freeze the UI by throwing a never resolved promise\n      throw neverSettledPromise;\n    }\n    if (status === AwaitRenderStatus.error && !errorElement) {\n      // No errorElement, throw to the nearest route-level error boundary\n      throw promise._error;\n    }\n    if (status === AwaitRenderStatus.error) {\n      // Render via our errorElement\n      return /*#__PURE__*/React.createElement(AwaitContext.Provider, {\n        value: promise,\n        children: errorElement\n      });\n    }\n    if (status === AwaitRenderStatus.success) {\n      // Render children with resolved value\n      return /*#__PURE__*/React.createElement(AwaitContext.Provider, {\n        value: promise,\n        children: children\n      });\n    }\n\n    // Throw to the suspense boundary\n    throw promise;\n  }\n}\n\n/**\n * @private\n * Indirection to leverage useAsyncValue for a render-prop API on `<Await>`\n */\nfunction ResolveAwait(_ref8) {\n  let {\n    children\n  } = _ref8;\n  let data = useAsyncValue();\n  let toRender = typeof children === \"function\" ? children(data) : children;\n  return /*#__PURE__*/React.createElement(React.Fragment, null, toRender);\n}\n\n///////////////////////////////////////////////////////////////////////////////\n// UTILS\n///////////////////////////////////////////////////////////////////////////////\n\n/**\n * Creates a route config from a React \"children\" object, which is usually\n * either a `<Route>` element or an array of them. Used internally by\n * `<Routes>` to create a route config from its children.\n *\n * @see https://reactrouter.com/v6/utils/create-routes-from-children\n */\nfunction createRoutesFromChildren(children, parentPath) {\n  if (parentPath === void 0) {\n    parentPath = [];\n  }\n  let routes = [];\n  React.Children.forEach(children, (element, index) => {\n    if (! /*#__PURE__*/React.isValidElement(element)) {\n      // Ignore non-elements. This allows people to more easily inline\n      // conditionals in their route config.\n      return;\n    }\n    let treePath = [...parentPath, index];\n    if (element.type === React.Fragment) {\n      // Transparently support React.Fragment and its children.\n      routes.push.apply(routes, createRoutesFromChildren(element.props.children, treePath));\n      return;\n    }\n    !(element.type === Route) ? process.env.NODE_ENV !== \"production\" ? UNSAFE_invariant(false, \"[\" + (typeof element.type === \"string\" ? element.type : element.type.name) + \"] is not a <Route> component. All component children of <Routes> must be a <Route> or <React.Fragment>\") : UNSAFE_invariant(false) : void 0;\n    !(!element.props.index || !element.props.children) ? process.env.NODE_ENV !== \"production\" ? UNSAFE_invariant(false, \"An index route cannot have child routes.\") : UNSAFE_invariant(false) : void 0;\n    let route = {\n      id: element.props.id || treePath.join(\"-\"),\n      caseSensitive: element.props.caseSensitive,\n      element: element.props.element,\n      Component: element.props.Component,\n      index: element.props.index,\n      path: element.props.path,\n      loader: element.props.loader,\n      action: element.props.action,\n      errorElement: element.props.errorElement,\n      ErrorBoundary: element.props.ErrorBoundary,\n      hasErrorBoundary: element.props.ErrorBoundary != null || element.props.errorElement != null,\n      shouldRevalidate: element.props.shouldRevalidate,\n      handle: element.props.handle,\n      lazy: element.props.lazy\n    };\n    if (element.props.children) {\n      route.children = createRoutesFromChildren(element.props.children, treePath);\n    }\n    routes.push(route);\n  });\n  return routes;\n}\n\n/**\n * Renders the result of `matchRoutes()` into a React element.\n */\nfunction renderMatches(matches) {\n  return _renderMatches(matches);\n}\n\nfunction mapRouteProperties(route) {\n  let updates = {\n    // Note: this check also occurs in createRoutesFromChildren so update\n    // there if you change this -- please and thank you!\n    hasErrorBoundary: route.ErrorBoundary != null || route.errorElement != null\n  };\n  if (route.Component) {\n    if (process.env.NODE_ENV !== \"production\") {\n      if (route.element) {\n        process.env.NODE_ENV !== \"production\" ? UNSAFE_warning(false, \"You should not include both `Component` and `element` on your route - \" + \"`Component` will be used.\") : void 0;\n      }\n    }\n    Object.assign(updates, {\n      element: /*#__PURE__*/React.createElement(route.Component),\n      Component: undefined\n    });\n  }\n  if (route.HydrateFallback) {\n    if (process.env.NODE_ENV !== \"production\") {\n      if (route.hydrateFallbackElement) {\n        process.env.NODE_ENV !== \"production\" ? UNSAFE_warning(false, \"You should not include both `HydrateFallback` and `hydrateFallbackElement` on your route - \" + \"`HydrateFallback` will be used.\") : void 0;\n      }\n    }\n    Object.assign(updates, {\n      hydrateFallbackElement: /*#__PURE__*/React.createElement(route.HydrateFallback),\n      HydrateFallback: undefined\n    });\n  }\n  if (route.ErrorBoundary) {\n    if (process.env.NODE_ENV !== \"production\") {\n      if (route.errorElement) {\n        process.env.NODE_ENV !== \"production\" ? UNSAFE_warning(false, \"You should not include both `ErrorBoundary` and `errorElement` on your route - \" + \"`ErrorBoundary` will be used.\") : void 0;\n      }\n    }\n    Object.assign(updates, {\n      errorElement: /*#__PURE__*/React.createElement(route.ErrorBoundary),\n      ErrorBoundary: undefined\n    });\n  }\n  return updates;\n}\nfunction createMemoryRouter(routes, opts) {\n  return createRouter({\n    basename: opts == null ? void 0 : opts.basename,\n    future: _extends({}, opts == null ? void 0 : opts.future, {\n      v7_prependBasename: true\n    }),\n    history: createMemoryHistory({\n      initialEntries: opts == null ? void 0 : opts.initialEntries,\n      initialIndex: opts == null ? void 0 : opts.initialIndex\n    }),\n    hydrationData: opts == null ? void 0 : opts.hydrationData,\n    routes,\n    mapRouteProperties,\n    dataStrategy: opts == null ? void 0 : opts.dataStrategy,\n    patchRoutesOnNavigation: opts == null ? void 0 : opts.patchRoutesOnNavigation\n  }).initialize();\n}\n\nexport { Await, MemoryRouter, Navigate, Outlet, Route, Router, RouterProvider, Routes, DataRouterContext as UNSAFE_DataRouterContext, DataRouterStateContext as UNSAFE_DataRouterStateContext, LocationContext as UNSAFE_LocationContext, NavigationContext as UNSAFE_NavigationContext, RouteContext as UNSAFE_RouteContext, logV6DeprecationWarnings as UNSAFE_logV6DeprecationWarnings, mapRouteProperties as UNSAFE_mapRouteProperties, useRouteId as UNSAFE_useRouteId, useRoutesImpl as UNSAFE_useRoutesImpl, createMemoryRouter, createRoutesFromChildren, createRoutesFromChildren as createRoutesFromElements, renderMatches, useActionData, useAsyncError, useAsyncValue, useBlocker, useHref, useInRouterContext, useLoaderData, useLocation, useMatch, useMatches, useNavigate, useNavigation, useNavigationType, useOutlet, useOutletContext, useParams, useResolvedPath, useRevalidator, useRouteError, useRouteLoaderData, useRoutes };\n//# sourceMappingURL=index.js.map\n"], "names": ["_extends", "Action", "window", "ResultType", "mapRouteProperties", "data", "aborted", "redirect", "location", "updatedFetchers", "existingFetcher", "state", "result", "React.createContext", "UNSAFE_invariant", "React.useContext", "React.useLayoutEffect", "UNSAFE_getResolveToMatches", "React.useRef", "React.useCallback", "UNSAFE_warning", "React.createElement", "React.useMemo", "React.Fragment", "React.Component", "errors", "matches", "DataRouterHook", "DataRouterStateHook", "UNSAFE_convertRouteMatchToUiMatch", "replace", "React.useEffect", "AwaitRenderStatus"], "mappings": ";AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA,SAASA,aAAW;AAClBA,eAAW,OAAO,SAAS,OAAO,OAAO,KAAI,IAAK,SAAU,QAAQ;AAClE,aAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,UAAI,SAAS,UAAU,CAAC;AACxB,eAAS,OAAO,QAAQ;AACtB,YAAI,OAAO,UAAU,eAAe,KAAK,QAAQ,GAAG,GAAG;AACrD,iBAAO,GAAG,IAAI,OAAO,GAAG;AAAA,QAClC;AAAA,MACA;AAAA,IACA;AACI,WAAO;AAAA,EACR;AACD,SAAOA,WAAS,MAAM,MAAM,SAAS;AACvC;AAQA,IAAI;AAAA,CACH,SAAUC,SAAQ;AAQjB,EAAAA,QAAO,KAAK,IAAI;AAMhB,EAAAA,QAAO,MAAM,IAAI;AAKjB,EAAAA,QAAO,SAAS,IAAI;AACtB,GAAG,WAAW,SAAS,CAAA,EAAG;AAC1B,MAAM,oBAAoB;AAgH1B,SAAS,qBAAqB,SAAS;AACrC,MAAI,YAAY,QAAQ;AACtB,cAAU,CAAE;AAAA,EAChB;AACE,WAAS,sBAAsBC,SAAQ,eAAe;AACpD,QAAI;AAAA,MACF;AAAA,MACA;AAAA,MACA;AAAA,IACD,IAAGA,QAAO;AACX,WAAO;AAAA,MAAe;AAAA,MAAI;AAAA,QACxB;AAAA,QACA;AAAA,QACA;AAAA,MACD;AAAA;AAAA,MAED,cAAc,SAAS,cAAc,MAAM,OAAO;AAAA,MAAM,cAAc,SAAS,cAAc,MAAM,OAAO;AAAA,IAAS;AAAA,EACvH;AACE,WAAS,kBAAkBA,SAAQ,IAAI;AACrC,WAAO,OAAO,OAAO,WAAW,KAAK,WAAW,EAAE;AAAA,EACtD;AACE,SAAO,mBAAmB,uBAAuB,mBAAmB,MAAM,OAAO;AACnF;AAmDA,SAAS,UAAU,OAAO,SAAS;AACjC,MAAI,UAAU,SAAS,UAAU,QAAQ,OAAO,UAAU,aAAa;AACrE,UAAM,IAAI,MAAM,OAAO;AAAA,EAC3B;AACA;AACA,SAAS,QAAQ,MAAM,SAAS;AAC9B,MAAI,CAAC,MAAM;AAET,QAAI,OAAO,YAAY,YAAa,SAAQ,KAAK,OAAO;AACxD,QAAI;AAMF,YAAM,IAAI,MAAM,OAAO;AAAA,IAExB,SAAQ,GAAG;AAAA,IAAA;AAAA,EAChB;AACA;AACA,SAAS,YAAY;AACnB,SAAO,KAAK,OAAM,EAAG,SAAS,EAAE,EAAE,OAAO,GAAG,CAAC;AAC/C;AAIA,SAAS,gBAAgB,UAAU,OAAO;AACxC,SAAO;AAAA,IACL,KAAK,SAAS;AAAA,IACd,KAAK,SAAS;AAAA,IACd,KAAK;AAAA,EACN;AACH;AAIA,SAAS,eAAe,SAAS,IAAI,OAAO,KAAK;AAC/C,MAAI,UAAU,QAAQ;AACpB,YAAQ;AAAA,EACZ;AACE,MAAI,WAAWF,WAAS;AAAA,IACtB,UAAU,OAAO,YAAY,WAAW,UAAU,QAAQ;AAAA,IAC1D,QAAQ;AAAA,IACR,MAAM;AAAA,EACV,GAAK,OAAO,OAAO,WAAW,UAAU,EAAE,IAAI,IAAI;AAAA,IAC9C;AAAA;AAAA;AAAA;AAAA;AAAA,IAKA,KAAK,MAAM,GAAG,OAAO,OAAO,UAAS;AAAA,EACzC,CAAG;AACD,SAAO;AACT;AAIA,SAAS,WAAW,MAAM;AACxB,MAAI;AAAA,IACF,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACX,IAAM;AACJ,MAAI,UAAU,WAAW,IAAK,aAAY,OAAO,OAAO,CAAC,MAAM,MAAM,SAAS,MAAM;AACpF,MAAI,QAAQ,SAAS,IAAK,aAAY,KAAK,OAAO,CAAC,MAAM,MAAM,OAAO,MAAM;AAC5E,SAAO;AACT;AAIA,SAAS,UAAU,MAAM;AACvB,MAAI,aAAa,CAAE;AACnB,MAAI,MAAM;AACR,QAAI,YAAY,KAAK,QAAQ,GAAG;AAChC,QAAI,aAAa,GAAG;AAClB,iBAAW,OAAO,KAAK,OAAO,SAAS;AACvC,aAAO,KAAK,OAAO,GAAG,SAAS;AAAA,IACrC;AACI,QAAI,cAAc,KAAK,QAAQ,GAAG;AAClC,QAAI,eAAe,GAAG;AACpB,iBAAW,SAAS,KAAK,OAAO,WAAW;AAC3C,aAAO,KAAK,OAAO,GAAG,WAAW;AAAA,IACvC;AACI,QAAI,MAAM;AACR,iBAAW,WAAW;AAAA,IAC5B;AAAA,EACA;AACE,SAAO;AACT;AACA,SAAS,mBAAmB,aAAa,YAAY,kBAAkB,SAAS;AAC9E,MAAI,YAAY,QAAQ;AACtB,cAAU,CAAE;AAAA,EAChB;AACE,MAAI;AAAA,IACF,QAAAE,UAAS,SAAS;AAAA,IAClB,WAAW;AAAA,EACf,IAAM;AACJ,MAAI,gBAAgBA,QAAO;AAC3B,MAAI,SAAS,OAAO;AACpB,MAAI,WAAW;AACf,MAAI,QAAQ,SAAU;AAItB,MAAI,SAAS,MAAM;AACjB,YAAQ;AACR,kBAAc,aAAaF,WAAS,CAAA,GAAI,cAAc,OAAO;AAAA,MAC3D,KAAK;AAAA,IACN,CAAA,GAAG,EAAE;AAAA,EACV;AACE,WAAS,WAAW;AAClB,QAAI,QAAQ,cAAc,SAAS;AAAA,MACjC,KAAK;AAAA,IACN;AACD,WAAO,MAAM;AAAA,EACjB;AACE,WAAS,YAAY;AACnB,aAAS,OAAO;AAChB,QAAI,YAAY,SAAU;AAC1B,QAAI,QAAQ,aAAa,OAAO,OAAO,YAAY;AACnD,YAAQ;AACR,QAAI,UAAU;AACZ,eAAS;AAAA,QACP;AAAA,QACA,UAAU,QAAQ;AAAA,QAClB;AAAA,MACR,CAAO;AAAA,IACP;AAAA,EACA;AACE,WAAS,KAAK,IAAI,OAAO;AACvB,aAAS,OAAO;AAChB,QAAI,WAAW,eAAe,QAAQ,UAAU,IAAI,KAAK;AAEzD,YAAQ,SAAQ,IAAK;AACrB,QAAI,eAAe,gBAAgB,UAAU,KAAK;AAClD,QAAI,MAAM,QAAQ,WAAW,QAAQ;AAErC,QAAI;AACF,oBAAc,UAAU,cAAc,IAAI,GAAG;AAAA,IAC9C,SAAQ,OAAO;AAKd,UAAI,iBAAiB,gBAAgB,MAAM,SAAS,kBAAkB;AACpE,cAAM;AAAA,MACd;AAGM,MAAAE,QAAO,SAAS,OAAO,GAAG;AAAA,IAChC;AACI,QAAI,YAAY,UAAU;AACxB,eAAS;AAAA,QACP;AAAA,QACA,UAAU,QAAQ;AAAA,QAClB,OAAO;AAAA,MACf,CAAO;AAAA,IACP;AAAA,EACA;AACE,WAAS,QAAQ,IAAI,OAAO;AAC1B,aAAS,OAAO;AAChB,QAAI,WAAW,eAAe,QAAQ,UAAU,IAAI,KAAK;AAEzD,YAAQ,SAAU;AAClB,QAAI,eAAe,gBAAgB,UAAU,KAAK;AAClD,QAAI,MAAM,QAAQ,WAAW,QAAQ;AACrC,kBAAc,aAAa,cAAc,IAAI,GAAG;AAChD,QAAI,YAAY,UAAU;AACxB,eAAS;AAAA,QACP;AAAA,QACA,UAAU,QAAQ;AAAA,QAClB,OAAO;AAAA,MACf,CAAO;AAAA,IACP;AAAA,EACA;AACE,WAAS,UAAU,IAAI;AAIrB,QAAI,OAAOA,QAAO,SAAS,WAAW,SAASA,QAAO,SAAS,SAASA,QAAO,SAAS;AACxF,QAAI,OAAO,OAAO,OAAO,WAAW,KAAK,WAAW,EAAE;AAItD,WAAO,KAAK,QAAQ,MAAM,KAAK;AAC/B,cAAU,MAAM,wEAAwE,IAAI;AAC5F,WAAO,IAAI,IAAI,MAAM,IAAI;AAAA,EAC7B;AACE,MAAI,UAAU;AAAA,IACZ,IAAI,SAAS;AACX,aAAO;AAAA,IACR;AAAA,IACD,IAAI,WAAW;AACb,aAAO,YAAYA,SAAQ,aAAa;AAAA,IACzC;AAAA,IACD,OAAO,IAAI;AACT,UAAI,UAAU;AACZ,cAAM,IAAI,MAAM,4CAA4C;AAAA,MACpE;AACM,MAAAA,QAAO,iBAAiB,mBAAmB,SAAS;AACpD,iBAAW;AACX,aAAO,MAAM;AACX,QAAAA,QAAO,oBAAoB,mBAAmB,SAAS;AACvD,mBAAW;AAAA,MACZ;AAAA,IACF;AAAA,IACD,WAAW,IAAI;AACb,aAAO,WAAWA,SAAQ,EAAE;AAAA,IAC7B;AAAA,IACD;AAAA,IACA,eAAe,IAAI;AAEjB,UAAI,MAAM,UAAU,EAAE;AACtB,aAAO;AAAA,QACL,UAAU,IAAI;AAAA,QACd,QAAQ,IAAI;AAAA,QACZ,MAAM,IAAI;AAAA,MACX;AAAA,IACF;AAAA,IACD;AAAA,IACA;AAAA,IACA,GAAG,GAAG;AACJ,aAAO,cAAc,GAAG,CAAC;AAAA,IAC/B;AAAA,EACG;AACD,SAAO;AACT;AAGA,IAAI;AAAA,CACH,SAAUC,aAAY;AACrB,EAAAA,YAAW,MAAM,IAAI;AACrB,EAAAA,YAAW,UAAU,IAAI;AACzB,EAAAA,YAAW,UAAU,IAAI;AACzB,EAAAA,YAAW,OAAO,IAAI;AACxB,GAAG,eAAe,aAAa,CAAA,EAAG;AAClC,MAAM,qBAAqB,oBAAI,IAAI,CAAC,QAAQ,iBAAiB,QAAQ,MAAM,SAAS,UAAU,CAAC;AAC/F,SAAS,aAAa,OAAO;AAC3B,SAAO,MAAM,UAAU;AACzB;AAGA,SAAS,0BAA0B,QAAQC,qBAAoB,YAAY,UAAU;AACnF,MAAI,eAAe,QAAQ;AACzB,iBAAa,CAAE;AAAA,EACnB;AACE,MAAI,aAAa,QAAQ;AACvB,eAAW,CAAE;AAAA,EACjB;AACE,SAAO,OAAO,IAAI,CAAC,OAAO,UAAU;AAClC,QAAI,WAAW,CAAC,GAAG,YAAY,OAAO,KAAK,CAAC;AAC5C,QAAI,KAAK,OAAO,MAAM,OAAO,WAAW,MAAM,KAAK,SAAS,KAAK,GAAG;AACpE,cAAU,MAAM,UAAU,QAAQ,CAAC,MAAM,UAAU,2CAA2C;AAC9F,cAAU,CAAC,SAAS,EAAE,GAAG,uCAAwC,KAAK,kEAAwE;AAC9I,QAAI,aAAa,KAAK,GAAG;AACvB,UAAI,aAAaJ,WAAS,CAAA,GAAI,OAAOI,oBAAmB,KAAK,GAAG;AAAA,QAC9D;AAAA,MACR,CAAO;AACD,eAAS,EAAE,IAAI;AACf,aAAO;AAAA,IACb,OAAW;AACL,UAAI,oBAAoBJ,WAAS,CAAA,GAAI,OAAOI,oBAAmB,KAAK,GAAG;AAAA,QACrE;AAAA,QACA,UAAU;AAAA,MAClB,CAAO;AACD,eAAS,EAAE,IAAI;AACf,UAAI,MAAM,UAAU;AAClB,0BAAkB,WAAW,0BAA0B,MAAM,UAAUA,qBAAoB,UAAU,QAAQ;AAAA,MACrH;AACM,aAAO;AAAA,IACb;AAAA,EACA,CAAG;AACH;AAMA,SAAS,YAAY,QAAQ,aAAa,UAAU;AAClD,MAAI,aAAa,QAAQ;AACvB,eAAW;AAAA,EACf;AACE,SAAO,gBAAgB,QAAQ,aAAa,UAAU,KAAK;AAC7D;AACA,SAAS,gBAAgB,QAAQ,aAAa,UAAU,cAAc;AACpE,MAAI,WAAW,OAAO,gBAAgB,WAAW,UAAU,WAAW,IAAI;AAC1E,MAAI,WAAW,cAAc,SAAS,YAAY,KAAK,QAAQ;AAC/D,MAAI,YAAY,MAAM;AACpB,WAAO;AAAA,EACX;AACE,MAAI,WAAW,cAAc,MAAM;AACnC,oBAAkB,QAAQ;AAC1B,MAAI,UAAU;AACd,WAAS,IAAI,GAAG,WAAW,QAAQ,IAAI,SAAS,QAAQ,EAAE,GAAG;AAO3D,QAAI,UAAU,WAAW,QAAQ;AACjC,cAAU,iBAAiB,SAAS,CAAC,GAAG,SAAS,YAAY;AAAA,EACjE;AACE,SAAO;AACT;AACA,SAAS,2BAA2B,OAAO,YAAY;AACrD,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,EACJ,IAAM;AACJ,SAAO;AAAA,IACL,IAAI,MAAM;AAAA,IACV;AAAA,IACA;AAAA,IACA,MAAM,WAAW,MAAM,EAAE;AAAA,IACzB,QAAQ,MAAM;AAAA,EACf;AACH;AACA,SAAS,cAAc,QAAQ,UAAU,aAAa,YAAY;AAChE,MAAI,aAAa,QAAQ;AACvB,eAAW,CAAE;AAAA,EACjB;AACE,MAAI,gBAAgB,QAAQ;AAC1B,kBAAc,CAAE;AAAA,EACpB;AACE,MAAI,eAAe,QAAQ;AACzB,iBAAa;AAAA,EACjB;AACE,MAAI,eAAe,CAAC,OAAO,OAAO,iBAAiB;AACjD,QAAI,OAAO;AAAA,MACT,cAAc,iBAAiB,SAAY,MAAM,QAAQ,KAAK;AAAA,MAC9D,eAAe,MAAM,kBAAkB;AAAA,MACvC,eAAe;AAAA,MACf;AAAA,IACD;AACD,QAAI,KAAK,aAAa,WAAW,GAAG,GAAG;AACrC,gBAAU,KAAK,aAAa,WAAW,UAAU,GAAG,0BAA2B,KAAK,eAAe,0BAA2B,MAAO,aAAa,mDAAoD,6DAA6D;AACnQ,WAAK,eAAe,KAAK,aAAa,MAAM,WAAW,MAAM;AAAA,IACnE;AACI,QAAI,OAAO,UAAU,CAAC,YAAY,KAAK,YAAY,CAAC;AACpD,QAAI,aAAa,YAAY,OAAO,IAAI;AAIxC,QAAI,MAAM,YAAY,MAAM,SAAS,SAAS,GAAG;AAC/C;AAAA;AAAA;AAAA,QAGA,MAAM,UAAU;AAAA,QAAM,6DAA6D,uCAAwC,OAAO;AAAA,MAAM;AACxI,oBAAc,MAAM,UAAU,UAAU,YAAY,IAAI;AAAA,IAC9D;AAGI,QAAI,MAAM,QAAQ,QAAQ,CAAC,MAAM,OAAO;AACtC;AAAA,IACN;AACI,aAAS,KAAK;AAAA,MACZ;AAAA,MACA,OAAO,aAAa,MAAM,MAAM,KAAK;AAAA,MACrC;AAAA,IACN,CAAK;AAAA,EACF;AACD,SAAO,QAAQ,CAAC,OAAO,UAAU;AAC/B,QAAI;AAEJ,QAAI,MAAM,SAAS,MAAM,GAAG,cAAc,MAAM,SAAS,QAAQ,YAAY,SAAS,GAAG,IAAI;AAC3F,mBAAa,OAAO,KAAK;AAAA,IAC/B,OAAW;AACL,eAAS,YAAY,wBAAwB,MAAM,IAAI,GAAG;AACxD,qBAAa,OAAO,OAAO,QAAQ;AAAA,MAC3C;AAAA,IACA;AAAA,EACA,CAAG;AACD,SAAO;AACT;AAeA,SAAS,wBAAwB,MAAM;AACrC,MAAI,WAAW,KAAK,MAAM,GAAG;AAC7B,MAAI,SAAS,WAAW,EAAG,QAAO,CAAE;AACpC,MAAI,CAAC,OAAO,GAAG,IAAI,IAAI;AAEvB,MAAI,aAAa,MAAM,SAAS,GAAG;AAEnC,MAAI,WAAW,MAAM,QAAQ,OAAO,EAAE;AACtC,MAAI,KAAK,WAAW,GAAG;AAGrB,WAAO,aAAa,CAAC,UAAU,EAAE,IAAI,CAAC,QAAQ;AAAA,EAClD;AACE,MAAI,eAAe,wBAAwB,KAAK,KAAK,GAAG,CAAC;AACzD,MAAI,SAAS,CAAE;AAQf,SAAO,KAAK,GAAG,aAAa,IAAI,aAAW,YAAY,KAAK,WAAW,CAAC,UAAU,OAAO,EAAE,KAAK,GAAG,CAAC,CAAC;AAErG,MAAI,YAAY;AACd,WAAO,KAAK,GAAG,YAAY;AAAA,EAC/B;AAEE,SAAO,OAAO,IAAI,cAAY,KAAK,WAAW,GAAG,KAAK,aAAa,KAAK,MAAM,QAAQ;AACxF;AACA,SAAS,kBAAkB,UAAU;AACnC,WAAS,KAAK,CAAC,GAAG,MAAM,EAAE,UAAU,EAAE,QAAQ,EAAE,QAAQ,EAAE,QACxD,eAAe,EAAE,WAAW,IAAI,UAAQ,KAAK,aAAa,GAAG,EAAE,WAAW,IAAI,UAAQ,KAAK,aAAa,CAAC,CAAC;AAC9G;AACA,MAAM,UAAU;AAChB,MAAM,sBAAsB;AAC5B,MAAM,kBAAkB;AACxB,MAAM,oBAAoB;AAC1B,MAAM,qBAAqB;AAC3B,MAAM,eAAe;AACrB,MAAM,UAAU,OAAK,MAAM;AAC3B,SAAS,aAAa,MAAM,OAAO;AACjC,MAAI,WAAW,KAAK,MAAM,GAAG;AAC7B,MAAI,eAAe,SAAS;AAC5B,MAAI,SAAS,KAAK,OAAO,GAAG;AAC1B,oBAAgB;AAAA,EACpB;AACE,MAAI,OAAO;AACT,oBAAgB;AAAA,EACpB;AACE,SAAO,SAAS,OAAO,OAAK,CAAC,QAAQ,CAAC,CAAC,EAAE,OAAO,CAAC,OAAO,YAAY,SAAS,QAAQ,KAAK,OAAO,IAAI,sBAAsB,YAAY,KAAK,oBAAoB,qBAAqB,YAAY;AACnM;AACA,SAAS,eAAe,GAAG,GAAG;AAC5B,MAAI,WAAW,EAAE,WAAW,EAAE,UAAU,EAAE,MAAM,GAAG,EAAE,EAAE,MAAM,CAAC,GAAG,MAAM,MAAM,EAAE,CAAC,CAAC;AACjF,SAAO;AAAA;AAAA;AAAA;AAAA;AAAA,IAKP,EAAE,EAAE,SAAS,CAAC,IAAI,EAAE,EAAE,SAAS,CAAC;AAAA;AAAA;AAAA;AAAA,IAGhC;AAAA;AACF;AACA,SAAS,iBAAiB,QAAQ,UAAU,cAAc;AACxD,MAAI,iBAAiB,QAAQ;AAC3B,mBAAe;AAAA,EACnB;AACE,MAAI;AAAA,IACF;AAAA,EACJ,IAAM;AACJ,MAAI,gBAAgB,CAAE;AACtB,MAAI,kBAAkB;AACtB,MAAI,UAAU,CAAE;AAChB,WAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,EAAE,GAAG;AAC1C,QAAI,OAAO,WAAW,CAAC;AACvB,QAAI,MAAM,MAAM,WAAW,SAAS;AACpC,QAAI,oBAAoB,oBAAoB,MAAM,WAAW,SAAS,MAAM,gBAAgB,MAAM,KAAK;AACvG,QAAI,QAAQ,UAAU;AAAA,MACpB,MAAM,KAAK;AAAA,MACX,eAAe,KAAK;AAAA,MACpB;AAAA,IACD,GAAE,iBAAiB;AACpB,QAAI,QAAQ,KAAK;AACjB,QAAI,CAAC,SAAS,OAAO,gBAAgB,CAAC,WAAW,WAAW,SAAS,CAAC,EAAE,MAAM,OAAO;AACnF,cAAQ,UAAU;AAAA,QAChB,MAAM,KAAK;AAAA,QACX,eAAe,KAAK;AAAA,QACpB,KAAK;AAAA,MACN,GAAE,iBAAiB;AAAA,IAC1B;AACI,QAAI,CAAC,OAAO;AACV,aAAO;AAAA,IACb;AACI,WAAO,OAAO,eAAe,MAAM,MAAM;AACzC,YAAQ,KAAK;AAAA;AAAA,MAEX,QAAQ;AAAA,MACR,UAAU,UAAU,CAAC,iBAAiB,MAAM,QAAQ,CAAC;AAAA,MACrD,cAAc,kBAAkB,UAAU,CAAC,iBAAiB,MAAM,YAAY,CAAC,CAAC;AAAA,MAChF;AAAA,IACN,CAAK;AACD,QAAI,MAAM,iBAAiB,KAAK;AAC9B,wBAAkB,UAAU,CAAC,iBAAiB,MAAM,YAAY,CAAC;AAAA,IACvE;AAAA,EACA;AACE,SAAO;AACT;AA8CA,SAAS,UAAU,SAAS,UAAU;AACpC,MAAI,OAAO,YAAY,UAAU;AAC/B,cAAU;AAAA,MACR,MAAM;AAAA,MACN,eAAe;AAAA,MACf,KAAK;AAAA,IACN;AAAA,EACL;AACE,MAAI,CAAC,SAAS,cAAc,IAAI,YAAY,QAAQ,MAAM,QAAQ,eAAe,QAAQ,GAAG;AAC5F,MAAI,QAAQ,SAAS,MAAM,OAAO;AAClC,MAAI,CAAC,MAAO,QAAO;AACnB,MAAI,kBAAkB,MAAM,CAAC;AAC7B,MAAI,eAAe,gBAAgB,QAAQ,WAAW,IAAI;AAC1D,MAAI,gBAAgB,MAAM,MAAM,CAAC;AACjC,MAAI,SAAS,eAAe,OAAO,CAAC,MAAM,MAAM,UAAU;AACxD,QAAI;AAAA,MACF;AAAA,MACA;AAAA,IACN,IAAQ;AAGJ,QAAI,cAAc,KAAK;AACrB,UAAI,aAAa,cAAc,KAAK,KAAK;AACzC,qBAAe,gBAAgB,MAAM,GAAG,gBAAgB,SAAS,WAAW,MAAM,EAAE,QAAQ,WAAW,IAAI;AAAA,IACjH;AACI,UAAM,QAAQ,cAAc,KAAK;AACjC,QAAI,cAAc,CAAC,OAAO;AACxB,WAAK,SAAS,IAAI;AAAA,IACxB,OAAW;AACL,WAAK,SAAS,KAAK,SAAS,IAAI,QAAQ,QAAQ,GAAG;AAAA,IACzD;AACI,WAAO;AAAA,EACR,GAAE,EAAE;AACL,SAAO;AAAA,IACL;AAAA,IACA,UAAU;AAAA,IACV;AAAA,IACA;AAAA,EACD;AACH;AACA,SAAS,YAAY,MAAM,eAAe,KAAK;AAC7C,MAAI,kBAAkB,QAAQ;AAC5B,oBAAgB;AAAA,EACpB;AACE,MAAI,QAAQ,QAAQ;AAClB,UAAM;AAAA,EACV;AACE,UAAQ,SAAS,OAAO,CAAC,KAAK,SAAS,GAAG,KAAK,KAAK,SAAS,IAAI,GAAG,iBAAkB,OAAO,sCAAuC,MAAO,KAAK,QAAQ,OAAO,IAAI,IAAI,uCAAwC,sEAAsE,sCAAuC,KAAK,QAAQ,OAAO,IAAI,IAAI,KAAM;AAC9V,MAAI,SAAS,CAAE;AACf,MAAI,eAAe,MAAM,KAAK,QAAQ,WAAW,EAAE,EAClD,QAAQ,QAAQ,GAAG,EACnB,QAAQ,sBAAsB,MAAM,EACpC,QAAQ,qBAAqB,CAAC,GAAG,WAAW,eAAe;AAC1D,WAAO,KAAK;AAAA,MACV;AAAA,MACA,YAAY,cAAc;AAAA,IAChC,CAAK;AACD,WAAO,aAAa,iBAAiB;AAAA,EACzC,CAAG;AACD,MAAI,KAAK,SAAS,GAAG,GAAG;AACtB,WAAO,KAAK;AAAA,MACV,WAAW;AAAA,IACjB,CAAK;AACD,oBAAgB,SAAS,OAAO,SAAS,OAAO,UAC9C;AAAA,EACH,WAAU,KAAK;AAEd,oBAAgB;AAAA,EACjB,WAAU,SAAS,MAAM,SAAS,KAAK;AAQtC,oBAAgB;AAAA,EACpB,MAAS;AACP,MAAI,UAAU,IAAI,OAAO,cAAc,gBAAgB,SAAY,GAAG;AACtE,SAAO,CAAC,SAAS,MAAM;AACzB;AACA,SAAS,WAAW,OAAO;AACzB,MAAI;AACF,WAAO,MAAM,MAAM,GAAG,EAAE,IAAI,OAAK,mBAAmB,CAAC,EAAE,QAAQ,OAAO,KAAK,CAAC,EAAE,KAAK,GAAG;AAAA,EACvF,SAAQ,OAAO;AACd,YAAQ,OAAO,mBAAoB,QAAQ,6GAAmH,eAAe,QAAQ,KAAK;AAC1L,WAAO;AAAA,EACX;AACA;AAIA,SAAS,cAAc,UAAU,UAAU;AACzC,MAAI,aAAa,IAAK,QAAO;AAC7B,MAAI,CAAC,SAAS,YAAa,EAAC,WAAW,SAAS,YAAW,CAAE,GAAG;AAC9D,WAAO;AAAA,EACX;AAGE,MAAI,aAAa,SAAS,SAAS,GAAG,IAAI,SAAS,SAAS,IAAI,SAAS;AACzE,MAAI,WAAW,SAAS,OAAO,UAAU;AACzC,MAAI,YAAY,aAAa,KAAK;AAEhC,WAAO;AAAA,EACX;AACE,SAAO,SAAS,MAAM,UAAU,KAAK;AACvC;AAMA,SAAS,YAAY,IAAI,cAAc;AACrC,MAAI,iBAAiB,QAAQ;AAC3B,mBAAe;AAAA,EACnB;AACE,MAAI;AAAA,IACF,UAAU;AAAA,IACV,SAAS;AAAA,IACT,OAAO;AAAA,EACR,IAAG,OAAO,OAAO,WAAW,UAAU,EAAE,IAAI;AAC7C,MAAI,WAAW,aAAa,WAAW,WAAW,GAAG,IAAI,aAAa,gBAAgB,YAAY,YAAY,IAAI;AAClH,SAAO;AAAA,IACL;AAAA,IACA,QAAQ,gBAAgB,MAAM;AAAA,IAC9B,MAAM,cAAc,IAAI;AAAA,EACzB;AACH;AACA,SAAS,gBAAgB,cAAc,cAAc;AACnD,MAAI,WAAW,aAAa,QAAQ,QAAQ,EAAE,EAAE,MAAM,GAAG;AACzD,MAAI,mBAAmB,aAAa,MAAM,GAAG;AAC7C,mBAAiB,QAAQ,aAAW;AAClC,QAAI,YAAY,MAAM;AAEpB,UAAI,SAAS,SAAS,EAAG,UAAS,IAAK;AAAA,IAC7C,WAAe,YAAY,KAAK;AAC1B,eAAS,KAAK,OAAO;AAAA,IAC3B;AAAA,EACA,CAAG;AACD,SAAO,SAAS,SAAS,IAAI,SAAS,KAAK,GAAG,IAAI;AACpD;AACA,SAAS,oBAAoB,MAAM,OAAO,MAAM,MAAM;AACpD,SAAO,uBAAuB,OAAO,0CAA0C,SAAS,QAAQ,cAAc,KAAK,UAAU,IAAI,IAAI,yCAAyC,SAAS,OAAO,8DAA8D;AAC9P;AAwBA,SAAS,2BAA2B,SAAS;AAC3C,SAAO,QAAQ,OAAO,CAAC,OAAO,UAAU,UAAU,KAAK,MAAM,MAAM,QAAQ,MAAM,MAAM,KAAK,SAAS,CAAC;AACxG;AAGA,SAAS,oBAAoB,SAAS,sBAAsB;AAC1D,MAAI,cAAc,2BAA2B,OAAO;AAIpD,MAAI,sBAAsB;AACxB,WAAO,YAAY,IAAI,CAAC,OAAO,QAAQ,QAAQ,YAAY,SAAS,IAAI,MAAM,WAAW,MAAM,YAAY;AAAA,EAC/G;AACE,SAAO,YAAY,IAAI,WAAS,MAAM,YAAY;AACpD;AAIA,SAAS,UAAU,OAAO,gBAAgB,kBAAkB,gBAAgB;AAC1E,MAAI,mBAAmB,QAAQ;AAC7B,qBAAiB;AAAA,EACrB;AACE,MAAI;AACJ,MAAI,OAAO,UAAU,UAAU;AAC7B,SAAK,UAAU,KAAK;AAAA,EACxB,OAAS;AACL,SAAKJ,WAAS,CAAE,GAAE,KAAK;AACvB,cAAU,CAAC,GAAG,YAAY,CAAC,GAAG,SAAS,SAAS,GAAG,GAAG,oBAAoB,KAAK,YAAY,UAAU,EAAE,CAAC;AACxG,cAAU,CAAC,GAAG,YAAY,CAAC,GAAG,SAAS,SAAS,GAAG,GAAG,oBAAoB,KAAK,YAAY,QAAQ,EAAE,CAAC;AACtG,cAAU,CAAC,GAAG,UAAU,CAAC,GAAG,OAAO,SAAS,GAAG,GAAG,oBAAoB,KAAK,UAAU,QAAQ,EAAE,CAAC;AAAA,EACpG;AACE,MAAI,cAAc,UAAU,MAAM,GAAG,aAAa;AAClD,MAAI,aAAa,cAAc,MAAM,GAAG;AACxC,MAAI;AAUJ,MAAI,cAAc,MAAM;AACtB,WAAO;AAAA,EACX,OAAS;AACL,QAAI,qBAAqB,eAAe,SAAS;AAKjD,QAAI,CAAC,kBAAkB,WAAW,WAAW,IAAI,GAAG;AAClD,UAAI,aAAa,WAAW,MAAM,GAAG;AACrC,aAAO,WAAW,CAAC,MAAM,MAAM;AAC7B,mBAAW,MAAO;AAClB,8BAAsB;AAAA,MAC9B;AACM,SAAG,WAAW,WAAW,KAAK,GAAG;AAAA,IACvC;AACI,WAAO,sBAAsB,IAAI,eAAe,kBAAkB,IAAI;AAAA,EAC1E;AACE,MAAI,OAAO,YAAY,IAAI,IAAI;AAE/B,MAAI,2BAA2B,cAAc,eAAe,OAAO,WAAW,SAAS,GAAG;AAE1F,MAAI,2BAA2B,eAAe,eAAe,QAAQ,iBAAiB,SAAS,GAAG;AAClG,MAAI,CAAC,KAAK,SAAS,SAAS,GAAG,MAAM,4BAA4B,0BAA0B;AACzF,SAAK,YAAY;AAAA,EACrB;AACE,SAAO;AACT;AAWK,MAAC,YAAY,WAAS,MAAM,KAAK,GAAG,EAAE,QAAQ,UAAU,GAAG;AAIhE,MAAM,oBAAoB,cAAY,SAAS,QAAQ,QAAQ,EAAE,EAAE,QAAQ,QAAQ,GAAG;AAItF,MAAM,kBAAkB,YAAU,CAAC,UAAU,WAAW,MAAM,KAAK,OAAO,WAAW,GAAG,IAAI,SAAS,MAAM;AAI3G,MAAM,gBAAgB,UAAQ,CAAC,QAAQ,SAAS,MAAM,KAAK,KAAK,WAAW,GAAG,IAAI,OAAO,MAAM;AAuB/F,MAAM,qBAAqB;AAAA,EACzB,YAAYK,OAAM,MAAM;AACtB,SAAK,OAAO;AACZ,SAAK,OAAOA;AACZ,SAAK,OAAO,QAAQ;AAAA,EACxB;AACA;AAKA,SAAS,KAAKA,OAAM,MAAM;AACxB,SAAO,IAAI,qBAAqBA,OAAM,OAAO,SAAS,WAAW;AAAA,IAC/D,QAAQ;AAAA,EACT,IAAG,IAAI;AACV;AACA,MAAM,6BAA6B,MAAM;AAAA;AACzC,MAAM,aAAa;AAAA,EACjB,YAAYA,OAAM,cAAc;AAC9B,SAAK,iBAAiB,oBAAI,IAAK;AAC/B,SAAK,cAAc,oBAAI,IAAK;AAC5B,SAAK,eAAe,CAAE;AACtB,cAAUA,SAAQ,OAAOA,UAAS,YAAY,CAAC,MAAM,QAAQA,KAAI,GAAG,oCAAoC;AAGxG,QAAI;AACJ,SAAK,eAAe,IAAI,QAAQ,CAAC,GAAG,MAAM,SAAS,CAAC;AACpD,SAAK,aAAa,IAAI,gBAAiB;AACvC,QAAI,UAAU,MAAM,OAAO,IAAI,qBAAqB,uBAAuB,CAAC;AAC5E,SAAK,sBAAsB,MAAM,KAAK,WAAW,OAAO,oBAAoB,SAAS,OAAO;AAC5F,SAAK,WAAW,OAAO,iBAAiB,SAAS,OAAO;AACxD,SAAK,OAAO,OAAO,QAAQA,KAAI,EAAE,OAAO,CAAC,KAAK,UAAU;AACtD,UAAI,CAAC,KAAK,KAAK,IAAI;AACnB,aAAO,OAAO,OAAO,KAAK;AAAA,QACxB,CAAC,GAAG,GAAG,KAAK,aAAa,KAAK,KAAK;AAAA,MAC3C,CAAO;AAAA,IACF,GAAE,EAAE;AACL,QAAI,KAAK,MAAM;AAEb,WAAK,oBAAqB;AAAA,IAChC;AACI,SAAK,OAAO;AAAA,EAChB;AAAA,EACE,aAAa,KAAK,OAAO;AACvB,QAAI,EAAE,iBAAiB,UAAU;AAC/B,aAAO;AAAA,IACb;AACI,SAAK,aAAa,KAAK,GAAG;AAC1B,SAAK,eAAe,IAAI,GAAG;AAG3B,QAAI,UAAU,QAAQ,KAAK,CAAC,OAAO,KAAK,YAAY,CAAC,EAAE,KAAK,CAAAA,UAAQ,KAAK,SAAS,SAAS,KAAK,QAAWA,KAAI,GAAG,WAAS,KAAK,SAAS,SAAS,KAAK,KAAK,CAAC;AAG7J,YAAQ,MAAM,MAAM;AAAA,KAAE;AACtB,WAAO,eAAe,SAAS,YAAY;AAAA,MACzC,KAAK,MAAM;AAAA,IACjB,CAAK;AACD,WAAO;AAAA,EACX;AAAA,EACE,SAAS,SAAS,KAAK,OAAOA,OAAM;AAClC,QAAI,KAAK,WAAW,OAAO,WAAW,iBAAiB,sBAAsB;AAC3E,WAAK,oBAAqB;AAC1B,aAAO,eAAe,SAAS,UAAU;AAAA,QACvC,KAAK,MAAM;AAAA,MACnB,CAAO;AACD,aAAO,QAAQ,OAAO,KAAK;AAAA,IACjC;AACI,SAAK,eAAe,OAAO,GAAG;AAC9B,QAAI,KAAK,MAAM;AAEb,WAAK,oBAAqB;AAAA,IAChC;AAGI,QAAI,UAAU,UAAaA,UAAS,QAAW;AAC7C,UAAI,iBAAiB,IAAI,MAAM,4BAA6B,MAAM,uFAA6F;AAC/J,aAAO,eAAe,SAAS,UAAU;AAAA,QACvC,KAAK,MAAM;AAAA,MACnB,CAAO;AACD,WAAK,KAAK,OAAO,GAAG;AACpB,aAAO,QAAQ,OAAO,cAAc;AAAA,IAC1C;AACI,QAAIA,UAAS,QAAW;AACtB,aAAO,eAAe,SAAS,UAAU;AAAA,QACvC,KAAK,MAAM;AAAA,MACnB,CAAO;AACD,WAAK,KAAK,OAAO,GAAG;AACpB,aAAO,QAAQ,OAAO,KAAK;AAAA,IACjC;AACI,WAAO,eAAe,SAAS,SAAS;AAAA,MACtC,KAAK,MAAMA;AAAA,IACjB,CAAK;AACD,SAAK,KAAK,OAAO,GAAG;AACpB,WAAOA;AAAA,EACX;AAAA,EACE,KAAK,SAAS,YAAY;AACxB,SAAK,YAAY,QAAQ,gBAAc,WAAW,SAAS,UAAU,CAAC;AAAA,EAC1E;AAAA,EACE,UAAU,IAAI;AACZ,SAAK,YAAY,IAAI,EAAE;AACvB,WAAO,MAAM,KAAK,YAAY,OAAO,EAAE;AAAA,EAC3C;AAAA,EACE,SAAS;AACP,SAAK,WAAW,MAAO;AACvB,SAAK,eAAe,QAAQ,CAAC,GAAG,MAAM,KAAK,eAAe,OAAO,CAAC,CAAC;AACnE,SAAK,KAAK,IAAI;AAAA,EAClB;AAAA,EACE,MAAM,YAAY,QAAQ;AACxB,QAAI,UAAU;AACd,QAAI,CAAC,KAAK,MAAM;AACd,UAAI,UAAU,MAAM,KAAK,OAAQ;AACjC,aAAO,iBAAiB,SAAS,OAAO;AACxC,gBAAU,MAAM,IAAI,QAAQ,aAAW;AACrC,aAAK,UAAU,CAAAC,aAAW;AACxB,iBAAO,oBAAoB,SAAS,OAAO;AAC3C,cAAIA,YAAW,KAAK,MAAM;AACxB,oBAAQA,QAAO;AAAA,UAC3B;AAAA,QACA,CAAS;AAAA,MACT,CAAO;AAAA,IACP;AACI,WAAO;AAAA,EACX;AAAA,EACE,IAAI,OAAO;AACT,WAAO,KAAK,eAAe,SAAS;AAAA,EACxC;AAAA,EACE,IAAI,gBAAgB;AAClB,cAAU,KAAK,SAAS,QAAQ,KAAK,MAAM,2DAA2D;AACtG,WAAO,OAAO,QAAQ,KAAK,IAAI,EAAE,OAAO,CAAC,KAAK,UAAU;AACtD,UAAI,CAAC,KAAK,KAAK,IAAI;AACnB,aAAO,OAAO,OAAO,KAAK;AAAA,QACxB,CAAC,GAAG,GAAG,qBAAqB,KAAK;AAAA,MACzC,CAAO;AAAA,IACF,GAAE,EAAE;AAAA,EACT;AAAA,EACE,IAAI,cAAc;AAChB,WAAO,MAAM,KAAK,KAAK,cAAc;AAAA,EACzC;AACA;AACA,SAAS,iBAAiB,OAAO;AAC/B,SAAO,iBAAiB,WAAW,MAAM,aAAa;AACxD;AACA,SAAS,qBAAqB,OAAO;AACnC,MAAI,CAAC,iBAAiB,KAAK,GAAG;AAC5B,WAAO;AAAA,EACX;AACE,MAAI,MAAM,QAAQ;AAChB,UAAM,MAAM;AAAA,EAChB;AACE,SAAO,MAAM;AACf;AAkBK,MAAC,WAAW,SAASC,UAAS,KAAK,MAAM;AAC5C,MAAI,SAAS,QAAQ;AACnB,WAAO;AAAA,EACX;AACE,MAAI,eAAe;AACnB,MAAI,OAAO,iBAAiB,UAAU;AACpC,mBAAe;AAAA,MACb,QAAQ;AAAA,IACT;AAAA,EACF,WAAU,OAAO,aAAa,WAAW,aAAa;AACrD,iBAAa,SAAS;AAAA,EAC1B;AACE,MAAI,UAAU,IAAI,QAAQ,aAAa,OAAO;AAC9C,UAAQ,IAAI,YAAY,GAAG;AAC3B,SAAO,IAAI,SAAS,MAAMP,WAAS,CAAA,GAAI,cAAc;AAAA,IACnD;AAAA,EACJ,CAAG,CAAC;AACJ;AA8BA,MAAM,kBAAkB;AAAA,EACtB,YAAY,QAAQ,YAAYK,OAAM,UAAU;AAC9C,QAAI,aAAa,QAAQ;AACvB,iBAAW;AAAA,IACjB;AACI,SAAK,SAAS;AACd,SAAK,aAAa,cAAc;AAChC,SAAK,WAAW;AAChB,QAAIA,iBAAgB,OAAO;AACzB,WAAK,OAAOA,MAAK,SAAU;AAC3B,WAAK,QAAQA;AAAA,IACnB,OAAW;AACL,WAAK,OAAOA;AAAA,IAClB;AAAA,EACA;AACA;AAKA,SAAS,qBAAqB,OAAO;AACnC,SAAO,SAAS,QAAQ,OAAO,MAAM,WAAW,YAAY,OAAO,MAAM,eAAe,YAAY,OAAO,MAAM,aAAa,aAAa,UAAU;AACvJ;AAEA,MAAM,0BAA0B,CAAC,QAAQ,OAAO,SAAS,QAAQ;AACjE,MAAM,uBAAuB,IAAI,IAAI,uBAAuB;AAC5D,MAAM,yBAAyB,CAAC,OAAO,GAAG,uBAAuB;AACjE,MAAM,sBAAsB,IAAI,IAAI,sBAAsB;AAC1D,MAAM,sBAAsB,oBAAI,IAAI,CAAC,KAAK,KAAK,KAAK,KAAK,GAAG,CAAC;AAC7D,MAAM,oCAAoC,oBAAI,IAAI,CAAC,KAAK,GAAG,CAAC;AAC5D,MAAM,kBAAkB;AAAA,EACtB,OAAO;AAAA,EACP,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,aAAa;AAAA,EACb,UAAU;AAAA,EACV,MAAM;AAAA,EACN,MAAM;AACR;AACK,MAAC,eAAe;AAAA,EACnB,OAAO;AAAA,EACP,MAAM;AAAA,EACN,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,aAAa;AAAA,EACb,UAAU;AAAA,EACV,MAAM;AAAA,EACN,MAAM;AACR;AACA,MAAM,eAAe;AAAA,EACnB,OAAO;AAAA,EACP,SAAS;AAAA,EACT,OAAO;AAAA,EACP,UAAU;AACZ;AACA,MAAM,qBAAqB;AAC3B,MAAM,4BAA4B,YAAU;AAAA,EAC1C,kBAAkB,QAAQ,MAAM,gBAAgB;AAClD;AACA,MAAM,0BAA0B;AAQhC,SAAS,aAAa,MAAM;AAC1B,QAAM,eAAe,KAAK,SAAS,KAAK,SAAS,OAAO,WAAW,cAAc,SAAS;AAC1F,QAAM,YAAY,OAAO,iBAAiB,eAAe,OAAO,aAAa,aAAa,eAAe,OAAO,aAAa,SAAS,kBAAkB;AACxJ,QAAM,WAAW,CAAC;AAClB,YAAU,KAAK,OAAO,SAAS,GAAG,2DAA2D;AAC7F,MAAID;AACJ,MAAI,KAAK,oBAAoB;AAC3B,IAAAA,sBAAqB,KAAK;AAAA,EAC9B,WAAa,KAAK,qBAAqB;AAEnC,QAAI,sBAAsB,KAAK;AAC/B,IAAAA,sBAAqB,YAAU;AAAA,MAC7B,kBAAkB,oBAAoB,KAAK;AAAA,IACjD;AAAA,EACA,OAAS;AACL,IAAAA,sBAAqB;AAAA,EACzB;AAEE,MAAI,WAAW,CAAE;AAEjB,MAAI,aAAa,0BAA0B,KAAK,QAAQA,qBAAoB,QAAW,QAAQ;AAC/F,MAAI;AACJ,MAAI,WAAW,KAAK,YAAY;AAChC,MAAI,mBAAmB,KAAK,gBAAgB;AAC5C,MAAI,8BAA8B,KAAK;AAEvC,MAAI,SAASJ,WAAS;AAAA,IACpB,mBAAmB;AAAA,IACnB,wBAAwB;AAAA,IACxB,qBAAqB;AAAA,IACrB,oBAAoB;AAAA,IACpB,sBAAsB;AAAA,IACtB,gCAAgC;AAAA,EACpC,GAAK,KAAK,MAAM;AAEd,MAAI,kBAAkB;AAEtB,MAAI,cAAc,oBAAI,IAAK;AAE3B,MAAI,uBAAuB;AAE3B,MAAI,0BAA0B;AAE9B,MAAI,oBAAoB;AAOxB,MAAI,wBAAwB,KAAK,iBAAiB;AAClD,MAAI,iBAAiB,YAAY,YAAY,KAAK,QAAQ,UAAU,QAAQ;AAC5E,MAAI,sBAAsB;AAC1B,MAAI,gBAAgB;AACpB,MAAI,kBAAkB,QAAQ,CAAC,6BAA6B;AAG1D,QAAI,QAAQ,uBAAuB,KAAK;AAAA,MACtC,UAAU,KAAK,QAAQ,SAAS;AAAA,IACtC,CAAK;AACD,QAAI;AAAA,MACF;AAAA,MACA;AAAA,IACN,IAAQ,uBAAuB,UAAU;AACrC,qBAAiB;AACjB,oBAAgB;AAAA,MACd,CAAC,MAAM,EAAE,GAAG;AAAA,IACb;AAAA,EACL;AAOE,MAAI,kBAAkB,CAAC,KAAK,eAAe;AACzC,QAAI,WAAW,cAAc,gBAAgB,YAAY,KAAK,QAAQ,SAAS,QAAQ;AACvF,QAAI,SAAS,QAAQ;AACnB,uBAAiB;AAAA,IACvB;AAAA,EACA;AACE,MAAI;AACJ,MAAI,CAAC,gBAAgB;AACnB,kBAAc;AACd,qBAAiB,CAAE;AAInB,QAAI,OAAO,qBAAqB;AAC9B,UAAI,WAAW,cAAc,MAAM,YAAY,KAAK,QAAQ,SAAS,QAAQ;AAC7E,UAAI,SAAS,UAAU,SAAS,SAAS;AACvC,8BAAsB;AACtB,yBAAiB,SAAS;AAAA,MAClC;AAAA,IACA;AAAA,EACA,WAAa,eAAe,KAAK,OAAK,EAAE,MAAM,IAAI,GAAG;AAGjD,kBAAc;AAAA,EAClB,WAAa,CAAC,eAAe,KAAK,OAAK,EAAE,MAAM,MAAM,GAAG;AAEpD,kBAAc;AAAA,EAClB,WAAa,OAAO,qBAAqB;AAIrC,QAAI,aAAa,KAAK,gBAAgB,KAAK,cAAc,aAAa;AACtE,QAAI,SAAS,KAAK,gBAAgB,KAAK,cAAc,SAAS;AAE9D,QAAI,QAAQ;AACV,UAAI,MAAM,eAAe,UAAU,OAAK,OAAO,EAAE,MAAM,EAAE,MAAM,MAAS;AACxE,oBAAc,eAAe,MAAM,GAAG,MAAM,CAAC,EAAE,MAAM,OAAK,CAAC,2BAA2B,EAAE,OAAO,YAAY,MAAM,CAAC;AAAA,IACxH,OAAW;AACL,oBAAc,eAAe,MAAM,OAAK,CAAC,2BAA2B,EAAE,OAAO,YAAY,MAAM,CAAC;AAAA,IACtG;AAAA,EACA,OAAS;AAGL,kBAAc,KAAK,iBAAiB;AAAA,EACxC;AACE,MAAI;AACJ,MAAI,QAAQ;AAAA,IACV,eAAe,KAAK,QAAQ;AAAA,IAC5B,UAAU,KAAK,QAAQ;AAAA,IACvB,SAAS;AAAA,IACT;AAAA,IACA,YAAY;AAAA;AAAA,IAEZ,uBAAuB,KAAK,iBAAiB,OAAO,QAAQ;AAAA,IAC5D,oBAAoB;AAAA,IACpB,cAAc;AAAA,IACd,YAAY,KAAK,iBAAiB,KAAK,cAAc,cAAc,CAAE;AAAA,IACrE,YAAY,KAAK,iBAAiB,KAAK,cAAc,cAAc;AAAA,IACnE,QAAQ,KAAK,iBAAiB,KAAK,cAAc,UAAU;AAAA,IAC3D,UAAU,oBAAI,IAAK;AAAA,IACnB,UAAU,oBAAI,IAAG;AAAA,EAClB;AAGD,MAAI,gBAAgB,OAAO;AAG3B,MAAI,4BAA4B;AAEhC,MAAI;AAEJ,MAAI,+BAA+B;AAEnC,MAAI,yBAAyB,oBAAI,IAAK;AAEtC,MAAI,8BAA8B;AAGlC,MAAI,8BAA8B;AAKlC,MAAI,yBAAyB;AAG7B,MAAI,0BAA0B,CAAE;AAGhC,MAAI,wBAAwB,oBAAI,IAAK;AAErC,MAAI,mBAAmB,oBAAI,IAAK;AAEhC,MAAI,qBAAqB;AAIzB,MAAI,0BAA0B;AAE9B,MAAI,iBAAiB,oBAAI,IAAK;AAE9B,MAAI,mBAAmB,oBAAI,IAAK;AAEhC,MAAI,mBAAmB,oBAAI,IAAK;AAEhC,MAAI,iBAAiB,oBAAI,IAAK;AAG9B,MAAI,kBAAkB,oBAAI,IAAK;AAK/B,MAAI,kBAAkB,oBAAI,IAAK;AAG/B,MAAI,mBAAmB,oBAAI,IAAK;AAGhC,MAAI,8BAA8B;AAIlC,WAAS,aAAa;AAGpB,sBAAkB,KAAK,QAAQ,OAAO,UAAQ;AAC5C,UAAI;AAAA,QACF,QAAQ;AAAA,QACR;AAAA,QACA;AAAA,MACR,IAAU;AAGJ,UAAI,6BAA6B;AAC/B,oCAA6B;AAC7B,sCAA8B;AAC9B;AAAA,MACR;AACM,cAAQ,iBAAiB,SAAS,KAAK,SAAS,MAAM,4YAAqa;AAC3d,UAAI,aAAa,sBAAsB;AAAA,QACrC,iBAAiB,MAAM;AAAA,QACvB,cAAc;AAAA,QACd;AAAA,MACR,CAAO;AACD,UAAI,cAAc,SAAS,MAAM;AAE/B,YAAI,2BAA2B,IAAI,QAAQ,aAAW;AACpD,wCAA8B;AAAA,QACxC,CAAS;AACD,aAAK,QAAQ,GAAG,QAAQ,EAAE;AAE1B,sBAAc,YAAY;AAAA,UACxB,OAAO;AAAA,UACP;AAAA,UACA,UAAU;AACR,0BAAc,YAAY;AAAA,cACxB,OAAO;AAAA,cACP,SAAS;AAAA,cACT,OAAO;AAAA,cACP;AAAA,YACd,CAAa;AAID,qCAAyB,KAAK,MAAM,KAAK,QAAQ,GAAG,KAAK,CAAC;AAAA,UAC3D;AAAA,UACD,QAAQ;AACN,gBAAI,WAAW,IAAI,IAAI,MAAM,QAAQ;AACrC,qBAAS,IAAI,YAAY,YAAY;AACrC,wBAAY;AAAA,cACV;AAAA,YACd,CAAa;AAAA,UACb;AAAA,QACA,CAAS;AACD;AAAA,MACR;AACM,aAAO,gBAAgB,eAAe,QAAQ;AAAA,IACpD,CAAK;AACD,QAAI,WAAW;AAGb,gCAA0B,cAAc,sBAAsB;AAC9D,UAAI,0BAA0B,MAAM,0BAA0B,cAAc,sBAAsB;AAClG,mBAAa,iBAAiB,YAAY,uBAAuB;AACjE,oCAA8B,MAAM,aAAa,oBAAoB,YAAY,uBAAuB;AAAA,IAC9G;AAMI,QAAI,CAAC,MAAM,aAAa;AACtB,sBAAgB,OAAO,KAAK,MAAM,UAAU;AAAA,QAC1C,kBAAkB;AAAA,MAC1B,CAAO;AAAA,IACP;AACI,WAAO;AAAA,EACX;AAEE,WAAS,UAAU;AACjB,QAAI,iBAAiB;AACnB,sBAAiB;AAAA,IACvB;AACI,QAAI,6BAA6B;AAC/B,kCAA6B;AAAA,IACnC;AACI,gBAAY,MAAO;AACnB,mCAA+B,4BAA4B,MAAO;AAClE,UAAM,SAAS,QAAQ,CAAC,GAAG,QAAQ,cAAc,GAAG,CAAC;AACrD,UAAM,SAAS,QAAQ,CAAC,GAAG,QAAQ,cAAc,GAAG,CAAC;AAAA,EACzD;AAEE,WAAS,UAAU,IAAI;AACrB,gBAAY,IAAI,EAAE;AAClB,WAAO,MAAM,YAAY,OAAO,EAAE;AAAA,EACtC;AAEE,WAAS,YAAY,UAAU,MAAM;AACnC,QAAI,SAAS,QAAQ;AACnB,aAAO,CAAE;AAAA,IACf;AACI,YAAQA,WAAS,IAAI,OAAO,QAAQ;AAGpC,QAAI,oBAAoB,CAAE;AAC1B,QAAI,sBAAsB,CAAE;AAC5B,QAAI,OAAO,mBAAmB;AAC5B,YAAM,SAAS,QAAQ,CAAC,SAAS,QAAQ;AACvC,YAAI,QAAQ,UAAU,QAAQ;AAC5B,cAAI,gBAAgB,IAAI,GAAG,GAAG;AAE5B,gCAAoB,KAAK,GAAG;AAAA,UACxC,OAAiB;AAGL,8BAAkB,KAAK,GAAG;AAAA,UACtC;AAAA,QACA;AAAA,MACA,CAAO;AAAA,IACP;AAGI,oBAAgB,QAAQ,SAAO;AAC7B,UAAI,CAAC,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,iBAAiB,IAAI,GAAG,GAAG;AAC1D,4BAAoB,KAAK,GAAG;AAAA,MACpC;AAAA,IACA,CAAK;AAID,KAAC,GAAG,WAAW,EAAE,QAAQ,gBAAc,WAAW,OAAO;AAAA,MACvD,iBAAiB;AAAA,MACjB,oBAAoB,KAAK;AAAA,MACzB,WAAW,KAAK,cAAc;AAAA,IACpC,CAAK,CAAC;AAEF,QAAI,OAAO,mBAAmB;AAC5B,wBAAkB,QAAQ,SAAO,MAAM,SAAS,OAAO,GAAG,CAAC;AAC3D,0BAAoB,QAAQ,SAAO,cAAc,GAAG,CAAC;AAAA,IAC3D,OAAW;AAGL,0BAAoB,QAAQ,SAAO,gBAAgB,OAAO,GAAG,CAAC;AAAA,IACpE;AAAA,EACA;AAME,WAAS,mBAAmB,UAAU,UAAU,OAAO;AACrD,QAAI,iBAAiB;AACrB,QAAI;AAAA,MACF;AAAA,IACN,IAAQ,UAAU,SAAS,CAAA,IAAK;AAM5B,QAAI,iBAAiB,MAAM,cAAc,QAAQ,MAAM,WAAW,cAAc,QAAQ,iBAAiB,MAAM,WAAW,UAAU,KAAK,MAAM,WAAW,UAAU,eAAe,kBAAkB,SAAS,UAAU,OAAO,SAAS,gBAAgB,iBAAiB;AACzQ,QAAI;AACJ,QAAI,SAAS,YAAY;AACvB,UAAI,OAAO,KAAK,SAAS,UAAU,EAAE,SAAS,GAAG;AAC/C,qBAAa,SAAS;AAAA,MAC9B,OAAa;AAEL,qBAAa;AAAA,MACrB;AAAA,IACK,WAAU,gBAAgB;AAEzB,mBAAa,MAAM;AAAA,IACzB,OAAW;AAEL,mBAAa;AAAA,IACnB;AAEI,QAAI,aAAa,SAAS,aAAa,gBAAgB,MAAM,YAAY,SAAS,YAAY,SAAS,WAAW,CAAE,GAAE,SAAS,MAAM,IAAI,MAAM;AAG/I,QAAI,WAAW,MAAM;AACrB,QAAI,SAAS,OAAO,GAAG;AACrB,iBAAW,IAAI,IAAI,QAAQ;AAC3B,eAAS,QAAQ,CAAC,GAAG,MAAM,SAAS,IAAI,GAAG,YAAY,CAAC;AAAA,IAC9D;AAGI,QAAI,qBAAqB,8BAA8B,QAAQ,MAAM,WAAW,cAAc,QAAQ,iBAAiB,MAAM,WAAW,UAAU,OAAO,mBAAmB,SAAS,UAAU,OAAO,SAAS,iBAAiB,iBAAiB;AAEjP,QAAI,oBAAoB;AACtB,mBAAa;AACb,2BAAqB;AAAA,IAC3B;AACI,QAAI,4BAA6B;AAAA,aAAW,kBAAkB,OAAO,IAAK;AAAA,aAAW,kBAAkB,OAAO,MAAM;AAClH,WAAK,QAAQ,KAAK,UAAU,SAAS,KAAK;AAAA,IAChD,WAAe,kBAAkB,OAAO,SAAS;AAC3C,WAAK,QAAQ,QAAQ,UAAU,SAAS,KAAK;AAAA,IACnD;AACI,QAAI;AAEJ,QAAI,kBAAkB,OAAO,KAAK;AAEhC,UAAI,aAAa,uBAAuB,IAAI,MAAM,SAAS,QAAQ;AACnE,UAAI,cAAc,WAAW,IAAI,SAAS,QAAQ,GAAG;AACnD,6BAAqB;AAAA,UACnB,iBAAiB,MAAM;AAAA,UACvB,cAAc;AAAA,QACf;AAAA,MACF,WAAU,uBAAuB,IAAI,SAAS,QAAQ,GAAG;AAGxD,6BAAqB;AAAA,UACnB,iBAAiB;AAAA,UACjB,cAAc,MAAM;AAAA,QACrB;AAAA,MACT;AAAA,IACK,WAAU,8BAA8B;AAEvC,UAAI,UAAU,uBAAuB,IAAI,MAAM,SAAS,QAAQ;AAChE,UAAI,SAAS;AACX,gBAAQ,IAAI,SAAS,QAAQ;AAAA,MACrC,OAAa;AACL,kBAAU,oBAAI,IAAI,CAAC,SAAS,QAAQ,CAAC;AACrC,+BAAuB,IAAI,MAAM,SAAS,UAAU,OAAO;AAAA,MACnE;AACM,2BAAqB;AAAA,QACnB,iBAAiB,MAAM;AAAA,QACvB,cAAc;AAAA,MACf;AAAA,IACP;AACI,gBAAYA,WAAS,CAAE,GAAE,UAAU;AAAA,MACjC;AAAA,MACA;AAAA,MACA,eAAe;AAAA,MACf;AAAA,MACA,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,uBAAuB,uBAAuB,UAAU,SAAS,WAAW,MAAM,OAAO;AAAA,MACzF;AAAA,MACA;AAAA,IACN,CAAK,GAAG;AAAA,MACF;AAAA,MACA,WAAW,cAAc;AAAA,IAC/B,CAAK;AAED,oBAAgB,OAAO;AACvB,gCAA4B;AAC5B,mCAA+B;AAC/B,kCAA8B;AAC9B,6BAAyB;AACzB,8BAA0B,CAAE;AAAA,EAChC;AAGE,iBAAe,SAAS,IAAI,MAAM;AAChC,QAAI,OAAO,OAAO,UAAU;AAC1B,WAAK,QAAQ,GAAG,EAAE;AAClB;AAAA,IACN;AACI,QAAI,iBAAiB,YAAY,MAAM,UAAU,MAAM,SAAS,UAAU,OAAO,oBAAoB,IAAI,OAAO,sBAAsB,QAAQ,OAAO,SAAS,KAAK,aAAa,QAAQ,OAAO,SAAS,KAAK,QAAQ;AACrN,QAAI;AAAA,MACF;AAAA,MACA;AAAA,MACA;AAAA,IACN,IAAQ,yBAAyB,OAAO,wBAAwB,OAAO,gBAAgB,IAAI;AACvF,QAAI,kBAAkB,MAAM;AAC5B,QAAI,eAAe,eAAe,MAAM,UAAU,MAAM,QAAQ,KAAK,KAAK;AAM1E,mBAAeA,WAAS,CAAA,GAAI,cAAc,KAAK,QAAQ,eAAe,YAAY,CAAC;AACnF,QAAI,cAAc,QAAQ,KAAK,WAAW,OAAO,KAAK,UAAU;AAChE,QAAI,gBAAgB,OAAO;AAC3B,QAAI,gBAAgB,MAAM;AACxB,sBAAgB,OAAO;AAAA,IAC7B,WAAe,gBAAgB,MAAO;AAAA,aAAW,cAAc,QAAQ,iBAAiB,WAAW,UAAU,KAAK,WAAW,eAAe,MAAM,SAAS,WAAW,MAAM,SAAS,QAAQ;AAKvL,sBAAgB,OAAO;AAAA,IAC7B;AACI,QAAI,qBAAqB,QAAQ,wBAAwB,OAAO,KAAK,uBAAuB,OAAO;AACnG,QAAI,aAAa,QAAQ,KAAK,eAAe;AAC7C,QAAI,aAAa,sBAAsB;AAAA,MACrC;AAAA,MACA;AAAA,MACA;AAAA,IACN,CAAK;AACD,QAAI,YAAY;AAEd,oBAAc,YAAY;AAAA,QACxB,OAAO;AAAA,QACP,UAAU;AAAA,QACV,UAAU;AACR,wBAAc,YAAY;AAAA,YACxB,OAAO;AAAA,YACP,SAAS;AAAA,YACT,OAAO;AAAA,YACP,UAAU;AAAA,UACtB,CAAW;AAED,mBAAS,IAAI,IAAI;AAAA,QAClB;AAAA,QACD,QAAQ;AACN,cAAI,WAAW,IAAI,IAAI,MAAM,QAAQ;AACrC,mBAAS,IAAI,YAAY,YAAY;AACrC,sBAAY;AAAA,YACV;AAAA,UACZ,CAAW;AAAA,QACX;AAAA,MACA,CAAO;AACD;AAAA,IACN;AACI,WAAO,MAAM,gBAAgB,eAAe,cAAc;AAAA,MACxD;AAAA;AAAA;AAAA,MAGA,cAAc;AAAA,MACd;AAAA,MACA,SAAS,QAAQ,KAAK;AAAA,MACtB,sBAAsB,QAAQ,KAAK;AAAA,MACnC;AAAA,IACN,CAAK;AAAA,EACL;AAIE,WAAS,aAAa;AACpB,yBAAsB;AACtB,gBAAY;AAAA,MACV,cAAc;AAAA,IACpB,CAAK;AAGD,QAAI,MAAM,WAAW,UAAU,cAAc;AAC3C;AAAA,IACN;AAII,QAAI,MAAM,WAAW,UAAU,QAAQ;AACrC,sBAAgB,MAAM,eAAe,MAAM,UAAU;AAAA,QACnD,gCAAgC;AAAA,MACxC,CAAO;AACD;AAAA,IACN;AAII,oBAAgB,iBAAiB,MAAM,eAAe,MAAM,WAAW,UAAU;AAAA,MAC/E,oBAAoB,MAAM;AAAA;AAAA,MAE1B,sBAAsB,iCAAiC;AAAA,IAC7D,CAAK;AAAA,EACL;AAIE,iBAAe,gBAAgB,eAAe,UAAU,MAAM;AAI5D,mCAA+B,4BAA4B,MAAO;AAClE,kCAA8B;AAC9B,oBAAgB;AAChB,mCAA+B,QAAQ,KAAK,oCAAoC;AAGhF,uBAAmB,MAAM,UAAU,MAAM,OAAO;AAChD,iCAA6B,QAAQ,KAAK,wBAAwB;AAClE,oCAAgC,QAAQ,KAAK,0BAA0B;AACvE,QAAI,cAAc,sBAAsB;AACxC,QAAI,oBAAoB,QAAQ,KAAK;AACrC,QAAI,UAAU,QAAQ,QAAQ,KAAK,oBAAoB,MAAM,WAAW,MAAM,QAAQ,SAAS,KAAK,CAAC;AAAA;AAAA,MAErG,MAAM;AAAA,QAAU,YAAY,aAAa,UAAU,QAAQ;AAC3D,QAAI,aAAa,QAAQ,KAAK,eAAe;AAC7C,QAAI,WAAW,cAAc,SAAS,aAAa,SAAS,QAAQ;AACpE,QAAI,SAAS,UAAU,SAAS,SAAS;AACvC,gBAAU,SAAS;AAAA,IACzB;AAEI,QAAI,CAAC,SAAS;AACZ,UAAI;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,MACR,IAAU,sBAAsB,SAAS,QAAQ;AAC3C,yBAAmB,UAAU;AAAA,QAC3B,SAAS;AAAA,QACT,YAAY,CAAE;AAAA,QACd,QAAQ;AAAA,UACN,CAAC,MAAM,EAAE,GAAG;AAAA,QACtB;AAAA,MACA,GAAS;AAAA,QACD;AAAA,MACR,CAAO;AACD;AAAA,IACN;AAOI,QAAI,MAAM,eAAe,CAAC,0BAA0B,iBAAiB,MAAM,UAAU,QAAQ,KAAK,EAAE,QAAQ,KAAK,cAAc,iBAAiB,KAAK,WAAW,UAAU,IAAI;AAC5K,yBAAmB,UAAU;AAAA,QAC3B;AAAA,MACR,GAAS;AAAA,QACD;AAAA,MACR,CAAO;AACD;AAAA,IACN;AAEI,kCAA8B,IAAI,gBAAiB;AACnD,QAAI,UAAU,wBAAwB,KAAK,SAAS,UAAU,4BAA4B,QAAQ,QAAQ,KAAK,UAAU;AACzH,QAAI;AACJ,QAAI,QAAQ,KAAK,cAAc;AAK7B,4BAAsB,CAAC,oBAAoB,OAAO,EAAE,MAAM,IAAI;AAAA,QAC5D,MAAM,WAAW;AAAA,QACjB,OAAO,KAAK;AAAA,MACpB,CAAO;AAAA,IACP,WAAe,QAAQ,KAAK,cAAc,iBAAiB,KAAK,WAAW,UAAU,GAAG;AAElF,UAAI,eAAe,MAAM,aAAa,SAAS,UAAU,KAAK,YAAY,SAAS,SAAS,QAAQ;AAAA,QAClG,SAAS,KAAK;AAAA,QACd;AAAA,MACR,CAAO;AACD,UAAI,aAAa,gBAAgB;AAC/B;AAAA,MACR;AAGM,UAAI,aAAa,qBAAqB;AACpC,YAAI,CAAC,SAAS,MAAM,IAAI,aAAa;AACrC,YAAI,cAAc,MAAM,KAAK,qBAAqB,OAAO,KAAK,KAAK,OAAO,MAAM,WAAW,KAAK;AAC9F,wCAA8B;AAC9B,6BAAmB,UAAU;AAAA,YAC3B,SAAS,aAAa;AAAA,YACtB,YAAY,CAAE;AAAA,YACd,QAAQ;AAAA,cACN,CAAC,OAAO,GAAG,OAAO;AAAA,YAChC;AAAA,UACA,CAAW;AACD;AAAA,QACV;AAAA,MACA;AACM,gBAAU,aAAa,WAAW;AAClC,4BAAsB,aAAa;AACnC,0BAAoB,qBAAqB,UAAU,KAAK,UAAU;AAClE,kBAAY;AAEZ,eAAS,SAAS;AAElB,gBAAU,wBAAwB,KAAK,SAAS,QAAQ,KAAK,QAAQ,MAAM;AAAA,IACjF;AAEI,QAAI;AAAA,MACF;AAAA,MACA,SAAS;AAAA,MACT;AAAA,MACA;AAAA,IACD,IAAG,MAAM,cAAc,SAAS,UAAU,SAAS,SAAS,QAAQ,mBAAmB,QAAQ,KAAK,YAAY,QAAQ,KAAK,mBAAmB,QAAQ,KAAK,SAAS,QAAQ,KAAK,qBAAqB,MAAM,WAAW,mBAAmB;AAC7O,QAAI,gBAAgB;AAClB;AAAA,IACN;AAII,kCAA8B;AAC9B,uBAAmB,UAAUA,WAAS;AAAA,MACpC,SAAS,kBAAkB;AAAA,IACjC,GAAO,uBAAuB,mBAAmB,GAAG;AAAA,MAC9C;AAAA,MACA;AAAA,IACN,CAAK,CAAC;AAAA,EACN;AAGE,iBAAe,aAAa,SAAS,UAAU,YAAY,SAAS,YAAY,MAAM;AACpF,QAAI,SAAS,QAAQ;AACnB,aAAO,CAAE;AAAA,IACf;AACI,yBAAsB;AAEtB,QAAI,aAAa,wBAAwB,UAAU,UAAU;AAC7D,gBAAY;AAAA,MACV;AAAA,IACN,GAAO;AAAA,MACD,WAAW,KAAK,cAAc;AAAA,IACpC,CAAK;AACD,QAAI,YAAY;AACd,UAAI,iBAAiB,MAAM,eAAe,SAAS,SAAS,UAAU,QAAQ,MAAM;AACpF,UAAI,eAAe,SAAS,WAAW;AACrC,eAAO;AAAA,UACL,gBAAgB;AAAA,QACjB;AAAA,MACT,WAAiB,eAAe,SAAS,SAAS;AAC1C,YAAI,aAAa,oBAAoB,eAAe,cAAc,EAAE,MAAM;AAC1E,eAAO;AAAA,UACL,SAAS,eAAe;AAAA,UACxB,qBAAqB,CAAC,YAAY;AAAA,YAChC,MAAM,WAAW;AAAA,YACjB,OAAO,eAAe;AAAA,UACvB,CAAA;AAAA,QACF;AAAA,MACT,WAAiB,CAAC,eAAe,SAAS;AAClC,YAAI;AAAA,UACF;AAAA,UACA;AAAA,UACA;AAAA,QACV,IAAY,sBAAsB,SAAS,QAAQ;AAC3C,eAAO;AAAA,UACL,SAAS;AAAA,UACT,qBAAqB,CAAC,MAAM,IAAI;AAAA,YAC9B,MAAM,WAAW;AAAA,YACjB;AAAA,UACD,CAAA;AAAA,QACF;AAAA,MACT,OAAa;AACL,kBAAU,eAAe;AAAA,MACjC;AAAA,IACA;AAEI,QAAI;AACJ,QAAI,cAAc,eAAe,SAAS,QAAQ;AAClD,QAAI,CAAC,YAAY,MAAM,UAAU,CAAC,YAAY,MAAM,MAAM;AACxD,eAAS;AAAA,QACP,MAAM,WAAW;AAAA,QACjB,OAAO,uBAAuB,KAAK;AAAA,UACjC,QAAQ,QAAQ;AAAA,UAChB,UAAU,SAAS;AAAA,UACnB,SAAS,YAAY,MAAM;AAAA,QAC5B,CAAA;AAAA,MACF;AAAA,IACP,OAAW;AACL,UAAI,UAAU,MAAM,iBAAiB,UAAU,OAAO,SAAS,CAAC,WAAW,GAAG,SAAS,IAAI;AAC3F,eAAS,QAAQ,YAAY,MAAM,EAAE;AACrC,UAAI,QAAQ,OAAO,SAAS;AAC1B,eAAO;AAAA,UACL,gBAAgB;AAAA,QACjB;AAAA,MACT;AAAA,IACA;AACI,QAAI,iBAAiB,MAAM,GAAG;AAC5B,UAAI;AACJ,UAAI,QAAQ,KAAK,WAAW,MAAM;AAChC,kBAAU,KAAK;AAAA,MACvB,OAAa;AAIL,YAAIQ,YAAW,0BAA0B,OAAO,SAAS,QAAQ,IAAI,UAAU,GAAG,IAAI,IAAI,QAAQ,GAAG,GAAG,QAAQ;AAChH,kBAAUA,cAAa,MAAM,SAAS,WAAW,MAAM,SAAS;AAAA,MACxE;AACM,YAAM,wBAAwB,SAAS,QAAQ,MAAM;AAAA,QACnD;AAAA,QACA;AAAA,MACR,CAAO;AACD,aAAO;AAAA,QACL,gBAAgB;AAAA,MACjB;AAAA,IACP;AACI,QAAI,iBAAiB,MAAM,GAAG;AAC5B,YAAM,uBAAuB,KAAK;AAAA,QAChC,MAAM;AAAA,MACd,CAAO;AAAA,IACP;AACI,QAAI,cAAc,MAAM,GAAG;AAGzB,UAAI,gBAAgB,oBAAoB,SAAS,YAAY,MAAM,EAAE;AAMrE,WAAK,QAAQ,KAAK,aAAa,MAAM;AACnC,wBAAgB,OAAO;AAAA,MAC/B;AACM,aAAO;AAAA,QACL;AAAA,QACA,qBAAqB,CAAC,cAAc,MAAM,IAAI,MAAM;AAAA,MACrD;AAAA,IACP;AACI,WAAO;AAAA,MACL;AAAA,MACA,qBAAqB,CAAC,YAAY,MAAM,IAAI,MAAM;AAAA,IACnD;AAAA,EACL;AAGE,iBAAe,cAAc,SAAS,UAAU,SAAS,YAAY,oBAAoB,YAAY,mBAAmB,SAAS,kBAAkB,WAAW,qBAAqB;AAEjL,QAAI,oBAAoB,sBAAsB,qBAAqB,UAAU,UAAU;AAGvF,QAAI,mBAAmB,cAAc,qBAAqB,4BAA4B,iBAAiB;AAOvG,QAAI,8BAA8B,CAAC,gCAAgC,CAAC,OAAO,uBAAuB,CAAC;AAMnG,QAAI,YAAY;AACd,UAAI,6BAA6B;AAC/B,YAAI,aAAa,qBAAqB,mBAAmB;AACzD,oBAAYR,WAAS;AAAA,UACnB,YAAY;AAAA,QACtB,GAAW,eAAe,SAAY;AAAA,UAC5B;AAAA,QACD,IAAG,CAAE,CAAA,GAAG;AAAA,UACP;AAAA,QACV,CAAS;AAAA,MACT;AACM,UAAI,iBAAiB,MAAM,eAAe,SAAS,SAAS,UAAU,QAAQ,MAAM;AACpF,UAAI,eAAe,SAAS,WAAW;AACrC,eAAO;AAAA,UACL,gBAAgB;AAAA,QACjB;AAAA,MACT,WAAiB,eAAe,SAAS,SAAS;AAC1C,YAAI,aAAa,oBAAoB,eAAe,cAAc,EAAE,MAAM;AAC1E,eAAO;AAAA,UACL,SAAS,eAAe;AAAA,UACxB,YAAY,CAAE;AAAA,UACd,QAAQ;AAAA,YACN,CAAC,UAAU,GAAG,eAAe;AAAA,UACzC;AAAA,QACS;AAAA,MACT,WAAiB,CAAC,eAAe,SAAS;AAClC,YAAI;AAAA,UACF;AAAA,UACA;AAAA,UACA;AAAA,QACV,IAAY,sBAAsB,SAAS,QAAQ;AAC3C,eAAO;AAAA,UACL,SAAS;AAAA,UACT,YAAY,CAAE;AAAA,UACd,QAAQ;AAAA,YACN,CAAC,MAAM,EAAE,GAAG;AAAA,UACxB;AAAA,QACS;AAAA,MACT,OAAa;AACL,kBAAU,eAAe;AAAA,MACjC;AAAA,IACA;AACI,QAAI,cAAc,sBAAsB;AACxC,QAAI,CAAC,eAAe,oBAAoB,IAAI,iBAAiB,KAAK,SAAS,OAAO,SAAS,kBAAkB,UAAU,OAAO,uBAAuB,qBAAqB,MAAM,OAAO,gCAAgC,wBAAwB,yBAAyB,uBAAuB,iBAAiB,kBAAkB,kBAAkB,aAAa,UAAU,mBAAmB;AAI9X,0BAAsB,aAAW,EAAE,WAAW,QAAQ,KAAK,OAAK,EAAE,MAAM,OAAO,OAAO,MAAM,iBAAiB,cAAc,KAAK,OAAK,EAAE,MAAM,OAAO,OAAO,CAAC;AAC5J,8BAA0B,EAAE;AAE5B,QAAI,cAAc,WAAW,KAAK,qBAAqB,WAAW,GAAG;AACnE,UAAIS,mBAAkB,uBAAwB;AAC9C,yBAAmB,UAAUT,WAAS;AAAA,QACpC;AAAA,QACA,YAAY,CAAE;AAAA;AAAA,QAEd,QAAQ,uBAAuB,cAAc,oBAAoB,CAAC,CAAC,IAAI;AAAA,UACrE,CAAC,oBAAoB,CAAC,CAAC,GAAG,oBAAoB,CAAC,EAAE;AAAA,QAC3D,IAAY;AAAA,MACZ,GAAS,uBAAuB,mBAAmB,GAAGS,mBAAkB;AAAA,QAChE,UAAU,IAAI,IAAI,MAAM,QAAQ;AAAA,MACjC,IAAG,CAAE,CAAA,GAAG;AAAA,QACP;AAAA,MACR,CAAO;AACD,aAAO;AAAA,QACL,gBAAgB;AAAA,MACjB;AAAA,IACP;AACI,QAAI,6BAA6B;AAC/B,UAAI,UAAU,CAAE;AAChB,UAAI,CAAC,YAAY;AAEf,gBAAQ,aAAa;AACrB,YAAI,aAAa,qBAAqB,mBAAmB;AACzD,YAAI,eAAe,QAAW;AAC5B,kBAAQ,aAAa;AAAA,QAC/B;AAAA,MACA;AACM,UAAI,qBAAqB,SAAS,GAAG;AACnC,gBAAQ,WAAW,+BAA+B,oBAAoB;AAAA,MAC9E;AACM,kBAAY,SAAS;AAAA,QACnB;AAAA,MACR,CAAO;AAAA,IACP;AACI,yBAAqB,QAAQ,QAAM;AACjC,mBAAa,GAAG,GAAG;AACnB,UAAI,GAAG,YAAY;AAIjB,yBAAiB,IAAI,GAAG,KAAK,GAAG,UAAU;AAAA,MAClD;AAAA,IACA,CAAK;AAED,QAAI,iCAAiC,MAAM,qBAAqB,QAAQ,OAAK,aAAa,EAAE,GAAG,CAAC;AAChG,QAAI,6BAA6B;AAC/B,kCAA4B,OAAO,iBAAiB,SAAS,8BAA8B;AAAA,IACjG;AACI,QAAI;AAAA,MACF;AAAA,MACA;AAAA,IACN,IAAQ,MAAM,+BAA+B,OAAO,SAAS,eAAe,sBAAsB,OAAO;AACrG,QAAI,QAAQ,OAAO,SAAS;AAC1B,aAAO;AAAA,QACL,gBAAgB;AAAA,MACjB;AAAA,IACP;AAII,QAAI,6BAA6B;AAC/B,kCAA4B,OAAO,oBAAoB,SAAS,8BAA8B;AAAA,IACpG;AACI,yBAAqB,QAAQ,QAAM,iBAAiB,OAAO,GAAG,GAAG,CAAC;AAElE,QAAIF,YAAW,aAAa,aAAa;AACzC,QAAIA,WAAU;AACZ,YAAM,wBAAwB,SAASA,UAAS,QAAQ,MAAM;AAAA,QAC5D;AAAA,MACR,CAAO;AACD,aAAO;AAAA,QACL,gBAAgB;AAAA,MACjB;AAAA,IACP;AACI,IAAAA,YAAW,aAAa,cAAc;AACtC,QAAIA,WAAU;AAIZ,uBAAiB,IAAIA,UAAS,GAAG;AACjC,YAAM,wBAAwB,SAASA,UAAS,QAAQ,MAAM;AAAA,QAC5D;AAAA,MACR,CAAO;AACD,aAAO;AAAA,QACL,gBAAgB;AAAA,MACjB;AAAA,IACP;AAEI,QAAI;AAAA,MACF;AAAA,MACA;AAAA,IACN,IAAQ,kBAAkB,OAAO,SAAS,eAAe,qBAAqB,sBAAsB,gBAAgB,eAAe;AAE/H,oBAAgB,QAAQ,CAAC,cAAc,YAAY;AACjD,mBAAa,UAAU,aAAW;AAIhC,YAAI,WAAW,aAAa,MAAM;AAChC,0BAAgB,OAAO,OAAO;AAAA,QACxC;AAAA,MACA,CAAO;AAAA,IACP,CAAK;AAED,QAAI,OAAO,uBAAuB,oBAAoB,MAAM,QAAQ;AAClE,eAASP,WAAS,CAAA,GAAI,MAAM,QAAQ,MAAM;AAAA,IAChD;AACI,QAAI,kBAAkB,uBAAwB;AAC9C,QAAI,qBAAqB,qBAAqB,uBAAuB;AACrE,QAAI,uBAAuB,mBAAmB,sBAAsB,qBAAqB,SAAS;AAClG,WAAOA,WAAS;AAAA,MACd;AAAA,MACA;AAAA,MACA;AAAA,IACD,GAAE,uBAAuB;AAAA,MACxB,UAAU,IAAI,IAAI,MAAM,QAAQ;AAAA,IACjC,IAAG,EAAE;AAAA,EACV;AACE,WAAS,qBAAqB,qBAAqB;AACjD,QAAI,uBAAuB,CAAC,cAAc,oBAAoB,CAAC,CAAC,GAAG;AAIjE,aAAO;AAAA,QACL,CAAC,oBAAoB,CAAC,CAAC,GAAG,oBAAoB,CAAC,EAAE;AAAA,MAClD;AAAA,IACP,WAAe,MAAM,YAAY;AAC3B,UAAI,OAAO,KAAK,MAAM,UAAU,EAAE,WAAW,GAAG;AAC9C,eAAO;AAAA,MACf,OAAa;AACL,eAAO,MAAM;AAAA,MACrB;AAAA,IACA;AAAA,EACA;AACE,WAAS,+BAA+B,sBAAsB;AAC5D,yBAAqB,QAAQ,QAAM;AACjC,UAAI,UAAU,MAAM,SAAS,IAAI,GAAG,GAAG;AACvC,UAAI,sBAAsB,kBAAkB,QAAW,UAAU,QAAQ,OAAO,MAAS;AACzF,YAAM,SAAS,IAAI,GAAG,KAAK,mBAAmB;AAAA,IACpD,CAAK;AACD,WAAO,IAAI,IAAI,MAAM,QAAQ;AAAA,EACjC;AAEE,WAAS,MAAM,KAAK,SAAS,MAAM,MAAM;AACvC,QAAI,UAAU;AACZ,YAAM,IAAI,MAAM,kMAA4M;AAAA,IAClO;AACI,iBAAa,GAAG;AAChB,QAAI,aAAa,QAAQ,KAAK,eAAe;AAC7C,QAAI,cAAc,sBAAsB;AACxC,QAAI,iBAAiB,YAAY,MAAM,UAAU,MAAM,SAAS,UAAU,OAAO,oBAAoB,MAAM,OAAO,sBAAsB,SAAS,QAAQ,OAAO,SAAS,KAAK,QAAQ;AACtL,QAAI,UAAU,YAAY,aAAa,gBAAgB,QAAQ;AAC/D,QAAI,WAAW,cAAc,SAAS,aAAa,cAAc;AACjE,QAAI,SAAS,UAAU,SAAS,SAAS;AACvC,gBAAU,SAAS;AAAA,IACzB;AACI,QAAI,CAAC,SAAS;AACZ,sBAAgB,KAAK,SAAS,uBAAuB,KAAK;AAAA,QACxD,UAAU;AAAA,MAClB,CAAO,GAAG;AAAA,QACF;AAAA,MACR,CAAO;AACD;AAAA,IACN;AACI,QAAI;AAAA,MACF;AAAA,MACA;AAAA,MACA;AAAA,IACN,IAAQ,yBAAyB,OAAO,wBAAwB,MAAM,gBAAgB,IAAI;AACtF,QAAI,OAAO;AACT,sBAAgB,KAAK,SAAS,OAAO;AAAA,QACnC;AAAA,MACR,CAAO;AACD;AAAA,IACN;AACI,QAAI,QAAQ,eAAe,SAAS,IAAI;AACxC,QAAI,sBAAsB,QAAQ,KAAK,wBAAwB;AAC/D,QAAI,cAAc,iBAAiB,WAAW,UAAU,GAAG;AACzD,0BAAoB,KAAK,SAAS,MAAM,OAAO,SAAS,SAAS,QAAQ,WAAW,oBAAoB,UAAU;AAClH;AAAA,IACN;AAGI,qBAAiB,IAAI,KAAK;AAAA,MACxB;AAAA,MACA;AAAA,IACN,CAAK;AACD,wBAAoB,KAAK,SAAS,MAAM,OAAO,SAAS,SAAS,QAAQ,WAAW,oBAAoB,UAAU;AAAA,EACtH;AAGE,iBAAe,oBAAoB,KAAK,SAAS,MAAM,OAAO,gBAAgB,YAAY,WAAW,oBAAoB,YAAY;AACnI,yBAAsB;AACtB,qBAAiB,OAAO,GAAG;AAC3B,aAAS,wBAAwB,GAAG;AAClC,UAAI,CAAC,EAAE,MAAM,UAAU,CAAC,EAAE,MAAM,MAAM;AACpC,YAAI,QAAQ,uBAAuB,KAAK;AAAA,UACtC,QAAQ,WAAW;AAAA,UACnB,UAAU;AAAA,UACV;AAAA,QACV,CAAS;AACD,wBAAgB,KAAK,SAAS,OAAO;AAAA,UACnC;AAAA,QACV,CAAS;AACD,eAAO;AAAA,MACf;AACM,aAAO;AAAA,IACb;AACI,QAAI,CAAC,cAAc,wBAAwB,KAAK,GAAG;AACjD;AAAA,IACN;AAEI,QAAI,kBAAkB,MAAM,SAAS,IAAI,GAAG;AAC5C,uBAAmB,KAAK,qBAAqB,YAAY,eAAe,GAAG;AAAA,MACzE;AAAA,IACN,CAAK;AACD,QAAI,kBAAkB,IAAI,gBAAiB;AAC3C,QAAI,eAAe,wBAAwB,KAAK,SAAS,MAAM,gBAAgB,QAAQ,UAAU;AACjG,QAAI,YAAY;AACd,UAAI,iBAAiB,MAAM,eAAe,gBAAgB,IAAI,IAAI,aAAa,GAAG,EAAE,UAAU,aAAa,MAAM;AACjH,UAAI,eAAe,SAAS,WAAW;AACrC;AAAA,MACR,WAAiB,eAAe,SAAS,SAAS;AAC1C,wBAAgB,KAAK,SAAS,eAAe,OAAO;AAAA,UAClD;AAAA,QACV,CAAS;AACD;AAAA,MACR,WAAiB,CAAC,eAAe,SAAS;AAClC,wBAAgB,KAAK,SAAS,uBAAuB,KAAK;AAAA,UACxD,UAAU;AAAA,QACpB,CAAS,GAAG;AAAA,UACF;AAAA,QACV,CAAS;AACD;AAAA,MACR,OAAa;AACL,yBAAiB,eAAe;AAChC,gBAAQ,eAAe,gBAAgB,IAAI;AAC3C,YAAI,wBAAwB,KAAK,GAAG;AAClC;AAAA,QACV;AAAA,MACA;AAAA,IACA;AAEI,qBAAiB,IAAI,KAAK,eAAe;AACzC,QAAI,oBAAoB;AACxB,QAAI,gBAAgB,MAAM,iBAAiB,UAAU,OAAO,cAAc,CAAC,KAAK,GAAG,gBAAgB,GAAG;AACtG,QAAI,eAAe,cAAc,MAAM,MAAM,EAAE;AAC/C,QAAI,aAAa,OAAO,SAAS;AAG/B,UAAI,iBAAiB,IAAI,GAAG,MAAM,iBAAiB;AACjD,yBAAiB,OAAO,GAAG;AAAA,MACnC;AACM;AAAA,IACN;AAII,QAAI,OAAO,qBAAqB,gBAAgB,IAAI,GAAG,GAAG;AACxD,UAAI,iBAAiB,YAAY,KAAK,cAAc,YAAY,GAAG;AACjE,2BAAmB,KAAK,eAAe,MAAS,CAAC;AACjD;AAAA,MACR;AAAA,IAEA,OAAW;AACL,UAAI,iBAAiB,YAAY,GAAG;AAClC,yBAAiB,OAAO,GAAG;AAC3B,YAAI,0BAA0B,mBAAmB;AAK/C,6BAAmB,KAAK,eAAe,MAAS,CAAC;AACjD;AAAA,QACV,OAAe;AACL,2BAAiB,IAAI,GAAG;AACxB,6BAAmB,KAAK,kBAAkB,UAAU,CAAC;AACrD,iBAAO,wBAAwB,cAAc,cAAc,OAAO;AAAA,YAChE,mBAAmB;AAAA,YACnB;AAAA,UACZ,CAAW;AAAA,QACX;AAAA,MACA;AAEM,UAAI,cAAc,YAAY,GAAG;AAC/B,wBAAgB,KAAK,SAAS,aAAa,KAAK;AAChD;AAAA,MACR;AAAA,IACA;AACI,QAAI,iBAAiB,YAAY,GAAG;AAClC,YAAM,uBAAuB,KAAK;AAAA,QAChC,MAAM;AAAA,MACd,CAAO;AAAA,IACP;AAGI,QAAI,eAAe,MAAM,WAAW,YAAY,MAAM;AACtD,QAAI,sBAAsB,wBAAwB,KAAK,SAAS,cAAc,gBAAgB,MAAM;AACpG,QAAI,cAAc,sBAAsB;AACxC,QAAI,UAAU,MAAM,WAAW,UAAU,SAAS,YAAY,aAAa,MAAM,WAAW,UAAU,QAAQ,IAAI,MAAM;AACxH,cAAU,SAAS,8CAA8C;AACjE,QAAI,SAAS,EAAE;AACf,mBAAe,IAAI,KAAK,MAAM;AAC9B,QAAI,cAAc,kBAAkB,YAAY,aAAa,IAAI;AACjE,UAAM,SAAS,IAAI,KAAK,WAAW;AACnC,QAAI,CAAC,eAAe,oBAAoB,IAAI,iBAAiB,KAAK,SAAS,OAAO,SAAS,YAAY,cAAc,OAAO,OAAO,gCAAgC,wBAAwB,yBAAyB,uBAAuB,iBAAiB,kBAAkB,kBAAkB,aAAa,UAAU,CAAC,MAAM,MAAM,IAAI,YAAY,CAAC;AAIrV,yBAAqB,OAAO,QAAM,GAAG,QAAQ,GAAG,EAAE,QAAQ,QAAM;AAC9D,UAAI,WAAW,GAAG;AAClB,UAAIU,mBAAkB,MAAM,SAAS,IAAI,QAAQ;AACjD,UAAI,sBAAsB,kBAAkB,QAAWA,mBAAkBA,iBAAgB,OAAO,MAAS;AACzG,YAAM,SAAS,IAAI,UAAU,mBAAmB;AAChD,mBAAa,QAAQ;AACrB,UAAI,GAAG,YAAY;AACjB,yBAAiB,IAAI,UAAU,GAAG,UAAU;AAAA,MACpD;AAAA,IACA,CAAK;AACD,gBAAY;AAAA,MACV,UAAU,IAAI,IAAI,MAAM,QAAQ;AAAA,IACtC,CAAK;AACD,QAAI,iCAAiC,MAAM,qBAAqB,QAAQ,QAAM,aAAa,GAAG,GAAG,CAAC;AAClG,oBAAgB,OAAO,iBAAiB,SAAS,8BAA8B;AAC/E,QAAI;AAAA,MACF;AAAA,MACA;AAAA,IACN,IAAQ,MAAM,+BAA+B,OAAO,SAAS,eAAe,sBAAsB,mBAAmB;AACjH,QAAI,gBAAgB,OAAO,SAAS;AAClC;AAAA,IACN;AACI,oBAAgB,OAAO,oBAAoB,SAAS,8BAA8B;AAClF,mBAAe,OAAO,GAAG;AACzB,qBAAiB,OAAO,GAAG;AAC3B,yBAAqB,QAAQ,OAAK,iBAAiB,OAAO,EAAE,GAAG,CAAC;AAChE,QAAIH,YAAW,aAAa,aAAa;AACzC,QAAIA,WAAU;AACZ,aAAO,wBAAwB,qBAAqBA,UAAS,QAAQ,OAAO;AAAA,QAC1E;AAAA,MACR,CAAO;AAAA,IACP;AACI,IAAAA,YAAW,aAAa,cAAc;AACtC,QAAIA,WAAU;AAIZ,uBAAiB,IAAIA,UAAS,GAAG;AACjC,aAAO,wBAAwB,qBAAqBA,UAAS,QAAQ,OAAO;AAAA,QAC1E;AAAA,MACR,CAAO;AAAA,IACP;AAEI,QAAI;AAAA,MACF;AAAA,MACA;AAAA,IACN,IAAQ,kBAAkB,OAAO,SAAS,eAAe,QAAW,sBAAsB,gBAAgB,eAAe;AAGrH,QAAI,MAAM,SAAS,IAAI,GAAG,GAAG;AAC3B,UAAI,cAAc,eAAe,aAAa,IAAI;AAClD,YAAM,SAAS,IAAI,KAAK,WAAW;AAAA,IACzC;AACI,yBAAqB,MAAM;AAI3B,QAAI,MAAM,WAAW,UAAU,aAAa,SAAS,yBAAyB;AAC5E,gBAAU,eAAe,yBAAyB;AAClD,qCAA+B,4BAA4B,MAAO;AAClE,yBAAmB,MAAM,WAAW,UAAU;AAAA,QAC5C;AAAA,QACA;AAAA,QACA;AAAA,QACA,UAAU,IAAI,IAAI,MAAM,QAAQ;AAAA,MACxC,CAAO;AAAA,IACP,OAAW;AAIL,kBAAY;AAAA,QACV;AAAA,QACA,YAAY,gBAAgB,MAAM,YAAY,YAAY,SAAS,MAAM;AAAA,QACzE,UAAU,IAAI,IAAI,MAAM,QAAQ;AAAA,MACxC,CAAO;AACD,+BAAyB;AAAA,IAC/B;AAAA,EACA;AAEE,iBAAe,oBAAoB,KAAK,SAAS,MAAM,OAAO,SAAS,YAAY,WAAW,oBAAoB,YAAY;AAC5H,QAAI,kBAAkB,MAAM,SAAS,IAAI,GAAG;AAC5C,uBAAmB,KAAK,kBAAkB,YAAY,kBAAkB,gBAAgB,OAAO,MAAS,GAAG;AAAA,MACzG;AAAA,IACN,CAAK;AACD,QAAI,kBAAkB,IAAI,gBAAiB;AAC3C,QAAI,eAAe,wBAAwB,KAAK,SAAS,MAAM,gBAAgB,MAAM;AACrF,QAAI,YAAY;AACd,UAAI,iBAAiB,MAAM,eAAe,SAAS,IAAI,IAAI,aAAa,GAAG,EAAE,UAAU,aAAa,MAAM;AAC1G,UAAI,eAAe,SAAS,WAAW;AACrC;AAAA,MACR,WAAiB,eAAe,SAAS,SAAS;AAC1C,wBAAgB,KAAK,SAAS,eAAe,OAAO;AAAA,UAClD;AAAA,QACV,CAAS;AACD;AAAA,MACR,WAAiB,CAAC,eAAe,SAAS;AAClC,wBAAgB,KAAK,SAAS,uBAAuB,KAAK;AAAA,UACxD,UAAU;AAAA,QACpB,CAAS,GAAG;AAAA,UACF;AAAA,QACV,CAAS;AACD;AAAA,MACR,OAAa;AACL,kBAAU,eAAe;AACzB,gBAAQ,eAAe,SAAS,IAAI;AAAA,MAC5C;AAAA,IACA;AAEI,qBAAiB,IAAI,KAAK,eAAe;AACzC,QAAI,oBAAoB;AACxB,QAAI,UAAU,MAAM,iBAAiB,UAAU,OAAO,cAAc,CAAC,KAAK,GAAG,SAAS,GAAG;AACzF,QAAI,SAAS,QAAQ,MAAM,MAAM,EAAE;AAKnC,QAAI,iBAAiB,MAAM,GAAG;AAC5B,eAAU,MAAM,oBAAoB,QAAQ,aAAa,QAAQ,IAAI,KAAM;AAAA,IACjF;AAGI,QAAI,iBAAiB,IAAI,GAAG,MAAM,iBAAiB;AACjD,uBAAiB,OAAO,GAAG;AAAA,IACjC;AACI,QAAI,aAAa,OAAO,SAAS;AAC/B;AAAA,IACN;AAGI,QAAI,gBAAgB,IAAI,GAAG,GAAG;AAC5B,yBAAmB,KAAK,eAAe,MAAS,CAAC;AACjD;AAAA,IACN;AAEI,QAAI,iBAAiB,MAAM,GAAG;AAC5B,UAAI,0BAA0B,mBAAmB;AAG/C,2BAAmB,KAAK,eAAe,MAAS,CAAC;AACjD;AAAA,MACR,OAAa;AACL,yBAAiB,IAAI,GAAG;AACxB,cAAM,wBAAwB,cAAc,QAAQ,OAAO;AAAA,UACzD;AAAA,QACV,CAAS;AACD;AAAA,MACR;AAAA,IACA;AAEI,QAAI,cAAc,MAAM,GAAG;AACzB,sBAAgB,KAAK,SAAS,OAAO,KAAK;AAC1C;AAAA,IACN;AACI,cAAU,CAAC,iBAAiB,MAAM,GAAG,iCAAiC;AAEtE,uBAAmB,KAAK,eAAe,OAAO,IAAI,CAAC;AAAA,EACvD;AAoBE,iBAAe,wBAAwB,SAASA,WAAU,cAAc,QAAQ;AAC9E,QAAI;AAAA,MACF;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACN,IAAQ,WAAW,SAAS,CAAA,IAAK;AAC7B,QAAIA,UAAS,SAAS,QAAQ,IAAI,oBAAoB,GAAG;AACvD,+BAAyB;AAAA,IAC/B;AACI,QAAI,WAAWA,UAAS,SAAS,QAAQ,IAAI,UAAU;AACvD,cAAU,UAAU,qDAAqD;AACzE,eAAW,0BAA0B,UAAU,IAAI,IAAI,QAAQ,GAAG,GAAG,QAAQ;AAC7E,QAAI,mBAAmB,eAAe,MAAM,UAAU,UAAU;AAAA,MAC9D,aAAa;AAAA,IACnB,CAAK;AACD,QAAI,WAAW;AACb,UAAI,mBAAmB;AACvB,UAAIA,UAAS,SAAS,QAAQ,IAAI,yBAAyB,GAAG;AAE5D,2BAAmB;AAAA,MACpB,WAAU,mBAAmB,KAAK,QAAQ,GAAG;AAC5C,cAAM,MAAM,KAAK,QAAQ,UAAU,QAAQ;AAC3C;AAAA,QAEA,IAAI,WAAW,aAAa,SAAS;AAAA,QAErC,cAAc,IAAI,UAAU,QAAQ,KAAK;AAAA,MACjD;AACM,UAAI,kBAAkB;AACpB,YAAI,SAAS;AACX,uBAAa,SAAS,QAAQ,QAAQ;AAAA,QAChD,OAAe;AACL,uBAAa,SAAS,OAAO,QAAQ;AAAA,QAC/C;AACQ;AAAA,MACR;AAAA,IACA;AAGI,kCAA8B;AAC9B,QAAI,wBAAwB,YAAY,QAAQA,UAAS,SAAS,QAAQ,IAAI,iBAAiB,IAAI,OAAO,UAAU,OAAO;AAG3H,QAAI;AAAA,MACF;AAAA,MACA;AAAA,MACA;AAAA,IACD,IAAG,MAAM;AACV,QAAI,CAAC,cAAc,CAAC,qBAAqB,cAAc,cAAc,aAAa;AAChF,mBAAa,4BAA4B,MAAM,UAAU;AAAA,IAC/D;AAII,QAAI,mBAAmB,cAAc;AACrC,QAAI,kCAAkC,IAAIA,UAAS,SAAS,MAAM,KAAK,oBAAoB,iBAAiB,iBAAiB,UAAU,GAAG;AACxI,YAAM,gBAAgB,uBAAuB,kBAAkB;AAAA,QAC7D,YAAYP,WAAS,CAAE,GAAE,kBAAkB;AAAA,UACzC,YAAY;AAAA,QACtB,CAAS;AAAA;AAAA,QAED,oBAAoB,sBAAsB;AAAA,QAC1C,sBAAsB,eAAe,+BAA+B;AAAA,MAC5E,CAAO;AAAA,IACP,OAAW;AAGL,UAAI,qBAAqB,qBAAqB,kBAAkB,UAAU;AAC1E,YAAM,gBAAgB,uBAAuB,kBAAkB;AAAA,QAC7D;AAAA;AAAA,QAEA;AAAA;AAAA,QAEA,oBAAoB,sBAAsB;AAAA,QAC1C,sBAAsB,eAAe,+BAA+B;AAAA,MAC5E,CAAO;AAAA,IACP;AAAA,EACA;AAGE,iBAAe,iBAAiB,MAAMW,QAAO,SAAS,eAAe,SAAS,YAAY;AACxF,QAAI;AACJ,QAAI,cAAc,CAAE;AACpB,QAAI;AACF,gBAAU,MAAM,qBAAqB,kBAAkB,MAAMA,QAAO,SAAS,eAAe,SAAS,YAAY,UAAUP,mBAAkB;AAAA,IAC9I,SAAQ,GAAG;AAGV,oBAAc,QAAQ,OAAK;AACzB,oBAAY,EAAE,MAAM,EAAE,IAAI;AAAA,UACxB,MAAM,WAAW;AAAA,UACjB,OAAO;AAAA,QACR;AAAA,MACT,CAAO;AACD,aAAO;AAAA,IACb;AACI,aAAS,CAAC,SAAS,MAAM,KAAK,OAAO,QAAQ,OAAO,GAAG;AACrD,UAAI,mCAAmC,MAAM,GAAG;AAC9C,YAAI,WAAW,OAAO;AACtB,oBAAY,OAAO,IAAI;AAAA,UACrB,MAAM,WAAW;AAAA,UACjB,UAAU,yCAAyC,UAAU,SAAS,SAAS,SAAS,UAAU,OAAO,oBAAoB;AAAA,QAC9H;AAAA,MACT,OAAa;AACL,oBAAY,OAAO,IAAI,MAAM,sCAAsC,MAAM;AAAA,MACjF;AAAA,IACA;AACI,WAAO;AAAA,EACX;AACE,iBAAe,+BAA+BO,QAAO,SAAS,eAAe,gBAAgB,SAAS;AACpG,QAAI,iBAAiBA,OAAM;AAE3B,QAAI,uBAAuB,iBAAiB,UAAUA,QAAO,SAAS,eAAe,SAAS,IAAI;AAClG,QAAI,wBAAwB,QAAQ,IAAI,eAAe,IAAI,OAAM,MAAK;AACpE,UAAI,EAAE,WAAW,EAAE,SAAS,EAAE,YAAY;AACxC,YAAI,UAAU,MAAM,iBAAiB,UAAUA,QAAO,wBAAwB,KAAK,SAAS,EAAE,MAAM,EAAE,WAAW,MAAM,GAAG,CAAC,EAAE,KAAK,GAAG,EAAE,SAAS,EAAE,GAAG;AACrJ,YAAI,SAAS,QAAQ,EAAE,MAAM,MAAM,EAAE;AAErC,eAAO;AAAA,UACL,CAAC,EAAE,GAAG,GAAG;AAAA,QACV;AAAA,MACT,OAAa;AACL,eAAO,QAAQ,QAAQ;AAAA,UACrB,CAAC,EAAE,GAAG,GAAG;AAAA,YACP,MAAM,WAAW;AAAA,YACjB,OAAO,uBAAuB,KAAK;AAAA,cACjC,UAAU,EAAE;AAAA,YACb,CAAA;AAAA,UACb;AAAA,QACA,CAAS;AAAA,MACT;AAAA,IACA,CAAK,CAAC;AACF,QAAI,gBAAgB,MAAM;AAC1B,QAAI,kBAAkB,MAAM,uBAAuB,OAAO,CAAC,KAAK,MAAM,OAAO,OAAO,KAAK,CAAC,GAAG,CAAA,CAAE;AAC/F,UAAM,QAAQ,IAAI,CAAC,iCAAiC,SAAS,eAAe,QAAQ,QAAQ,gBAAgBA,OAAM,UAAU,GAAG,8BAA8B,SAAS,gBAAgB,cAAc,CAAC,CAAC;AACtM,WAAO;AAAA,MACL;AAAA,MACA;AAAA,IACD;AAAA,EACL;AACE,WAAS,uBAAuB;AAE9B,6BAAyB;AAGzB,4BAAwB,KAAK,GAAG,uBAAuB;AAEvD,qBAAiB,QAAQ,CAAC,GAAG,QAAQ;AACnC,UAAI,iBAAiB,IAAI,GAAG,GAAG;AAC7B,8BAAsB,IAAI,GAAG;AAAA,MACrC;AACM,mBAAa,GAAG;AAAA,IACtB,CAAK;AAAA,EACL;AACE,WAAS,mBAAmB,KAAK,SAAS,MAAM;AAC9C,QAAI,SAAS,QAAQ;AACnB,aAAO,CAAE;AAAA,IACf;AACI,UAAM,SAAS,IAAI,KAAK,OAAO;AAC/B,gBAAY;AAAA,MACV,UAAU,IAAI,IAAI,MAAM,QAAQ;AAAA,IACtC,GAAO;AAAA,MACD,YAAY,QAAQ,KAAK,eAAe;AAAA,IAC9C,CAAK;AAAA,EACL;AACE,WAAS,gBAAgB,KAAK,SAAS,OAAO,MAAM;AAClD,QAAI,SAAS,QAAQ;AACnB,aAAO,CAAE;AAAA,IACf;AACI,QAAI,gBAAgB,oBAAoB,MAAM,SAAS,OAAO;AAC9D,kBAAc,GAAG;AACjB,gBAAY;AAAA,MACV,QAAQ;AAAA,QACN,CAAC,cAAc,MAAM,EAAE,GAAG;AAAA,MAC3B;AAAA,MACD,UAAU,IAAI,IAAI,MAAM,QAAQ;AAAA,IACtC,GAAO;AAAA,MACD,YAAY,QAAQ,KAAK,eAAe;AAAA,IAC9C,CAAK;AAAA,EACL;AACE,WAAS,WAAW,KAAK;AACvB,mBAAe,IAAI,MAAM,eAAe,IAAI,GAAG,KAAK,KAAK,CAAC;AAG1D,QAAI,gBAAgB,IAAI,GAAG,GAAG;AAC5B,sBAAgB,OAAO,GAAG;AAAA,IAChC;AACI,WAAO,MAAM,SAAS,IAAI,GAAG,KAAK;AAAA,EACtC;AACE,WAAS,cAAc,KAAK;AAC1B,QAAI,UAAU,MAAM,SAAS,IAAI,GAAG;AAIpC,QAAI,iBAAiB,IAAI,GAAG,KAAK,EAAE,WAAW,QAAQ,UAAU,aAAa,eAAe,IAAI,GAAG,IAAI;AACrG,mBAAa,GAAG;AAAA,IACtB;AACI,qBAAiB,OAAO,GAAG;AAC3B,mBAAe,OAAO,GAAG;AACzB,qBAAiB,OAAO,GAAG;AAO3B,QAAI,OAAO,mBAAmB;AAC5B,sBAAgB,OAAO,GAAG;AAAA,IAChC;AACI,0BAAsB,OAAO,GAAG;AAChC,UAAM,SAAS,OAAO,GAAG;AAAA,EAC7B;AACE,WAAS,4BAA4B,KAAK;AACxC,QAAI,SAAS,eAAe,IAAI,GAAG,KAAK,KAAK;AAC7C,QAAI,SAAS,GAAG;AACd,qBAAe,OAAO,GAAG;AACzB,sBAAgB,IAAI,GAAG;AACvB,UAAI,CAAC,OAAO,mBAAmB;AAC7B,sBAAc,GAAG;AAAA,MACzB;AAAA,IACA,OAAW;AACL,qBAAe,IAAI,KAAK,KAAK;AAAA,IACnC;AACI,gBAAY;AAAA,MACV,UAAU,IAAI,IAAI,MAAM,QAAQ;AAAA,IACtC,CAAK;AAAA,EACL;AACE,WAAS,aAAa,KAAK;AACzB,QAAI,aAAa,iBAAiB,IAAI,GAAG;AACzC,QAAI,YAAY;AACd,iBAAW,MAAO;AAClB,uBAAiB,OAAO,GAAG;AAAA,IACjC;AAAA,EACA;AACE,WAAS,iBAAiB,MAAM;AAC9B,aAAS,OAAO,MAAM;AACpB,UAAI,UAAU,WAAW,GAAG;AAC5B,UAAI,cAAc,eAAe,QAAQ,IAAI;AAC7C,YAAM,SAAS,IAAI,KAAK,WAAW;AAAA,IACzC;AAAA,EACA;AACE,WAAS,yBAAyB;AAChC,QAAI,WAAW,CAAE;AACjB,QAAI,kBAAkB;AACtB,aAAS,OAAO,kBAAkB;AAChC,UAAI,UAAU,MAAM,SAAS,IAAI,GAAG;AACpC,gBAAU,SAAS,uBAAuB,GAAG;AAC7C,UAAI,QAAQ,UAAU,WAAW;AAC/B,yBAAiB,OAAO,GAAG;AAC3B,iBAAS,KAAK,GAAG;AACjB,0BAAkB;AAAA,MAC1B;AAAA,IACA;AACI,qBAAiB,QAAQ;AACzB,WAAO;AAAA,EACX;AACE,WAAS,qBAAqB,UAAU;AACtC,QAAI,aAAa,CAAE;AACnB,aAAS,CAAC,KAAK,EAAE,KAAK,gBAAgB;AACpC,UAAI,KAAK,UAAU;AACjB,YAAI,UAAU,MAAM,SAAS,IAAI,GAAG;AACpC,kBAAU,SAAS,uBAAuB,GAAG;AAC7C,YAAI,QAAQ,UAAU,WAAW;AAC/B,uBAAa,GAAG;AAChB,yBAAe,OAAO,GAAG;AACzB,qBAAW,KAAK,GAAG;AAAA,QAC7B;AAAA,MACA;AAAA,IACA;AACI,qBAAiB,UAAU;AAC3B,WAAO,WAAW,SAAS;AAAA,EAC/B;AACE,WAAS,WAAW,KAAK,IAAI;AAC3B,QAAI,UAAU,MAAM,SAAS,IAAI,GAAG,KAAK;AACzC,QAAI,iBAAiB,IAAI,GAAG,MAAM,IAAI;AACpC,uBAAiB,IAAI,KAAK,EAAE;AAAA,IAClC;AACI,WAAO;AAAA,EACX;AACE,WAAS,cAAc,KAAK;AAC1B,UAAM,SAAS,OAAO,GAAG;AACzB,qBAAiB,OAAO,GAAG;AAAA,EAC/B;AAEE,WAAS,cAAc,KAAK,YAAY;AACtC,QAAI,UAAU,MAAM,SAAS,IAAI,GAAG,KAAK;AAGzC,cAAU,QAAQ,UAAU,eAAe,WAAW,UAAU,aAAa,QAAQ,UAAU,aAAa,WAAW,UAAU,aAAa,QAAQ,UAAU,aAAa,WAAW,UAAU,gBAAgB,QAAQ,UAAU,aAAa,WAAW,UAAU,eAAe,QAAQ,UAAU,gBAAgB,WAAW,UAAU,aAAa,uCAAuC,QAAQ,QAAQ,SAAS,WAAW,KAAK;AACza,QAAI,WAAW,IAAI,IAAI,MAAM,QAAQ;AACrC,aAAS,IAAI,KAAK,UAAU;AAC5B,gBAAY;AAAA,MACV;AAAA,IACN,CAAK;AAAA,EACL;AACE,WAAS,sBAAsB,OAAO;AACpC,QAAI;AAAA,MACF;AAAA,MACA;AAAA,MACA;AAAA,IACN,IAAQ;AACJ,QAAI,iBAAiB,SAAS,GAAG;AAC/B;AAAA,IACN;AAGI,QAAI,iBAAiB,OAAO,GAAG;AAC7B,cAAQ,OAAO,8CAA8C;AAAA,IACnE;AACI,QAAI,UAAU,MAAM,KAAK,iBAAiB,QAAO,CAAE;AACnD,QAAI,CAAC,YAAY,eAAe,IAAI,QAAQ,QAAQ,SAAS,CAAC;AAC9D,QAAI,UAAU,MAAM,SAAS,IAAI,UAAU;AAC3C,QAAI,WAAW,QAAQ,UAAU,cAAc;AAG7C;AAAA,IACN;AAGI,QAAI,gBAAgB;AAAA,MAClB;AAAA,MACA;AAAA,MACA;AAAA,IACN,CAAK,GAAG;AACF,aAAO;AAAA,IACb;AAAA,EACA;AACE,WAAS,sBAAsB,UAAU;AACvC,QAAI,QAAQ,uBAAuB,KAAK;AAAA,MACtC;AAAA,IACN,CAAK;AACD,QAAI,cAAc,sBAAsB;AACxC,QAAI;AAAA,MACF;AAAA,MACA;AAAA,IACN,IAAQ,uBAAuB,WAAW;AAEtC,0BAAuB;AACvB,WAAO;AAAA,MACL,iBAAiB;AAAA,MACjB;AAAA,MACA;AAAA,IACD;AAAA,EACL;AACE,WAAS,sBAAsB,WAAW;AACxC,QAAI,oBAAoB,CAAE;AAC1B,oBAAgB,QAAQ,CAAC,KAAK,YAAY;AACxC,UAAI,CAAC,aAAa,UAAU,OAAO,GAAG;AAIpC,YAAI,OAAQ;AACZ,0BAAkB,KAAK,OAAO;AAC9B,wBAAgB,OAAO,OAAO;AAAA,MACtC;AAAA,IACA,CAAK;AACD,WAAO;AAAA,EACX;AAGE,WAAS,wBAAwB,WAAW,aAAa,QAAQ;AAC/D,2BAAuB;AACvB,wBAAoB;AACpB,8BAA0B,UAAU;AAIpC,QAAI,CAAC,yBAAyB,MAAM,eAAe,iBAAiB;AAClE,8BAAwB;AACxB,UAAI,IAAI,uBAAuB,MAAM,UAAU,MAAM,OAAO;AAC5D,UAAI,KAAK,MAAM;AACb,oBAAY;AAAA,UACV,uBAAuB;AAAA,QACjC,CAAS;AAAA,MACT;AAAA,IACA;AACI,WAAO,MAAM;AACX,6BAAuB;AACvB,0BAAoB;AACpB,gCAA0B;AAAA,IAC3B;AAAA,EACL;AACE,WAAS,aAAa,UAAU,SAAS;AACvC,QAAI,yBAAyB;AAC3B,UAAI,MAAM,wBAAwB,UAAU,QAAQ,IAAI,OAAK,2BAA2B,GAAG,MAAM,UAAU,CAAC,CAAC;AAC7G,aAAO,OAAO,SAAS;AAAA,IAC7B;AACI,WAAO,SAAS;AAAA,EACpB;AACE,WAAS,mBAAmB,UAAU,SAAS;AAC7C,QAAI,wBAAwB,mBAAmB;AAC7C,UAAI,MAAM,aAAa,UAAU,OAAO;AACxC,2BAAqB,GAAG,IAAI,kBAAmB;AAAA,IACrD;AAAA,EACA;AACE,WAAS,uBAAuB,UAAU,SAAS;AACjD,QAAI,sBAAsB;AACxB,UAAI,MAAM,aAAa,UAAU,OAAO;AACxC,UAAI,IAAI,qBAAqB,GAAG;AAChC,UAAI,OAAO,MAAM,UAAU;AACzB,eAAO;AAAA,MACf;AAAA,IACA;AACI,WAAO;AAAA,EACX;AACE,WAAS,cAAc,SAAS,aAAa,UAAU;AACrD,QAAI,6BAA6B;AAC/B,UAAI,CAAC,SAAS;AACZ,YAAI,aAAa,gBAAgB,aAAa,UAAU,UAAU,IAAI;AACtE,eAAO;AAAA,UACL,QAAQ;AAAA,UACR,SAAS,cAAc,CAAA;AAAA,QACxB;AAAA,MACT,OAAa;AACL,YAAI,OAAO,KAAK,QAAQ,CAAC,EAAE,MAAM,EAAE,SAAS,GAAG;AAI7C,cAAI,iBAAiB,gBAAgB,aAAa,UAAU,UAAU,IAAI;AAC1E,iBAAO;AAAA,YACL,QAAQ;AAAA,YACR,SAAS;AAAA,UACV;AAAA,QACX;AAAA,MACA;AAAA,IACA;AACI,WAAO;AAAA,MACL,QAAQ;AAAA,MACR,SAAS;AAAA,IACV;AAAA,EACL;AACE,iBAAe,eAAe,SAAS,UAAU,QAAQ;AACvD,QAAI,CAAC,6BAA6B;AAChC,aAAO;AAAA,QACL,MAAM;AAAA,QACN;AAAA,MACD;AAAA,IACP;AACI,QAAI,iBAAiB;AACrB,WAAO,MAAM;AACX,UAAI,WAAW,sBAAsB;AACrC,UAAI,cAAc,sBAAsB;AACxC,UAAI,gBAAgB;AACpB,UAAI;AACF,cAAM,4BAA4B;AAAA,UAChC;AAAA,UACA,MAAM;AAAA,UACN,SAAS;AAAA,UACT,OAAO,CAAC,SAAS,aAAa;AAC5B,gBAAI,OAAO,QAAS;AACpB,4BAAgB,SAAS,UAAU,aAAa,eAAeP,mBAAkB;AAAA,UAC7F;AAAA,QACA,CAAS;AAAA,MACF,SAAQ,GAAG;AACV,eAAO;AAAA,UACL,MAAM;AAAA,UACN,OAAO;AAAA,UACP;AAAA,QACD;AAAA,MACT,UAAgB;AAOR,YAAI,YAAY,CAAC,OAAO,SAAS;AAC/B,uBAAa,CAAC,GAAG,UAAU;AAAA,QACrC;AAAA,MACA;AACM,UAAI,OAAO,SAAS;AAClB,eAAO;AAAA,UACL,MAAM;AAAA,QACP;AAAA,MACT;AACM,UAAI,aAAa,YAAY,aAAa,UAAU,QAAQ;AAC5D,UAAI,YAAY;AACd,eAAO;AAAA,UACL,MAAM;AAAA,UACN,SAAS;AAAA,QACV;AAAA,MACT;AACM,UAAI,oBAAoB,gBAAgB,aAAa,UAAU,UAAU,IAAI;AAE7E,UAAI,CAAC,qBAAqB,eAAe,WAAW,kBAAkB,UAAU,eAAe,MAAM,CAAC,GAAG,MAAM,EAAE,MAAM,OAAO,kBAAkB,CAAC,EAAE,MAAM,EAAE,GAAG;AAC5J,eAAO;AAAA,UACL,MAAM;AAAA,UACN,SAAS;AAAA,QACV;AAAA,MACT;AACM,uBAAiB;AAAA,IACvB;AAAA,EACA;AACE,WAAS,mBAAmB,WAAW;AACrC,eAAW,CAAE;AACb,yBAAqB,0BAA0B,WAAWA,qBAAoB,QAAW,QAAQ;AAAA,EACrG;AACE,WAAS,YAAY,SAAS,UAAU;AACtC,QAAI,WAAW,sBAAsB;AACrC,QAAI,cAAc,sBAAsB;AACxC,oBAAgB,SAAS,UAAU,aAAa,UAAUA,mBAAkB;AAM5E,QAAI,UAAU;AACZ,mBAAa,CAAC,GAAG,UAAU;AAC3B,kBAAY,CAAA,CAAE;AAAA,IACpB;AAAA,EACA;AACE,WAAS;AAAA,IACP,IAAI,WAAW;AACb,aAAO;AAAA,IACR;AAAA,IACD,IAAI,SAAS;AACX,aAAO;AAAA,IACR;AAAA,IACD,IAAI,QAAQ;AACV,aAAO;AAAA,IACR;AAAA,IACD,IAAI,SAAS;AACX,aAAO;AAAA,IACR;AAAA,IACD,IAAI,SAAS;AACX,aAAO;AAAA,IACR;AAAA,IACD;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA;AAAA;AAAA,IAGA,YAAY,QAAM,KAAK,QAAQ,WAAW,EAAE;AAAA,IAC5C,gBAAgB,QAAM,KAAK,QAAQ,eAAe,EAAE;AAAA,IACpD;AAAA,IACA,eAAe;AAAA,IACf;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,2BAA2B;AAAA,IAC3B,0BAA0B;AAAA;AAAA;AAAA,IAG1B;AAAA,EACD;AACD,SAAO;AACT;AAqbA,SAAS,uBAAuB,MAAM;AACpC,SAAO,QAAQ,SAAS,cAAc,QAAQ,KAAK,YAAY,QAAQ,UAAU,QAAQ,KAAK,SAAS;AACzG;AACA,SAAS,YAAY,UAAU,SAAS,UAAU,iBAAiB,IAAI,sBAAsB,aAAa,UAAU;AAClH,MAAI;AACJ,MAAI;AACJ,MAAI,aAAa;AAGf,wBAAoB,CAAE;AACtB,aAAS,SAAS,SAAS;AACzB,wBAAkB,KAAK,KAAK;AAC5B,UAAI,MAAM,MAAM,OAAO,aAAa;AAClC,2BAAmB;AACnB;AAAA,MACR;AAAA,IACA;AAAA,EACA,OAAS;AACL,wBAAoB;AACpB,uBAAmB,QAAQ,QAAQ,SAAS,CAAC;AAAA,EACjD;AAEE,MAAI,OAAO,UAAU,KAAK,KAAK,KAAK,oBAAoB,mBAAmB,oBAAoB,GAAG,cAAc,SAAS,UAAU,QAAQ,KAAK,SAAS,UAAU,aAAa,MAAM;AAItL,MAAI,MAAM,MAAM;AACd,SAAK,SAAS,SAAS;AACvB,SAAK,OAAO,SAAS;AAAA,EACzB;AAEE,OAAK,MAAM,QAAQ,OAAO,MAAM,OAAO,QAAQ,kBAAkB;AAC/D,QAAI,aAAa,mBAAmB,KAAK,MAAM;AAC/C,QAAI,iBAAiB,MAAM,SAAS,CAAC,YAAY;AAE/C,WAAK,SAAS,KAAK,SAAS,KAAK,OAAO,QAAQ,OAAO,SAAS,IAAI;AAAA,IACrE,WAAU,CAAC,iBAAiB,MAAM,SAAS,YAAY;AAEtD,UAAI,SAAS,IAAI,gBAAgB,KAAK,MAAM;AAC5C,UAAI,cAAc,OAAO,OAAO,OAAO;AACvC,aAAO,OAAO,OAAO;AACrB,kBAAY,OAAO,OAAK,CAAC,EAAE,QAAQ,OAAK,OAAO,OAAO,SAAS,CAAC,CAAC;AACjE,UAAI,KAAK,OAAO,SAAU;AAC1B,WAAK,SAAS,KAAK,MAAM,KAAK;AAAA,IACpC;AAAA,EACA;AAKE,MAAI,mBAAmB,aAAa,KAAK;AACvC,SAAK,WAAW,KAAK,aAAa,MAAM,WAAW,UAAU,CAAC,UAAU,KAAK,QAAQ,CAAC;AAAA,EAC1F;AACE,SAAO,WAAW,IAAI;AACxB;AAGA,SAAS,yBAAyB,qBAAqB,WAAW,MAAM,MAAM;AAE5E,MAAI,CAAC,QAAQ,CAAC,uBAAuB,IAAI,GAAG;AAC1C,WAAO;AAAA,MACL;AAAA,IACD;AAAA,EACL;AACE,MAAI,KAAK,cAAc,CAAC,cAAc,KAAK,UAAU,GAAG;AACtD,WAAO;AAAA,MACL;AAAA,MACA,OAAO,uBAAuB,KAAK;AAAA,QACjC,QAAQ,KAAK;AAAA,MACd,CAAA;AAAA,IACF;AAAA,EACL;AACE,MAAI,sBAAsB,OAAO;AAAA,IAC/B;AAAA,IACA,OAAO,uBAAuB,KAAK;AAAA,MACjC,MAAM;AAAA,IACP,CAAA;AAAA,EACL;AAEE,MAAI,gBAAgB,KAAK,cAAc;AACvC,MAAI,aAAa,sBAAsB,cAAc,YAAa,IAAG,cAAc,YAAa;AAChG,MAAI,aAAa,kBAAkB,IAAI;AACvC,MAAI,KAAK,SAAS,QAAW;AAC3B,QAAI,KAAK,gBAAgB,cAAc;AAErC,UAAI,CAAC,iBAAiB,UAAU,GAAG;AACjC,eAAO,oBAAqB;AAAA,MACpC;AACM,UAAI,OAAO,OAAO,KAAK,SAAS,WAAW,KAAK,OAAO,KAAK,gBAAgB,YAAY,KAAK,gBAAgB;AAAA;AAAA,QAE7G,MAAM,KAAK,KAAK,KAAK,QAAS,CAAA,EAAE,OAAO,CAAC,KAAK,UAAU;AACrD,cAAI,CAAC,MAAM,KAAK,IAAI;AACpB,iBAAO,KAAK,MAAM,OAAO,MAAM,QAAQ;AAAA,QACxC,GAAE,EAAE;AAAA,UAAI,OAAO,KAAK,IAAI;AACzB,aAAO;AAAA,QACL;AAAA,QACA,YAAY;AAAA,UACV;AAAA,UACA;AAAA,UACA,aAAa,KAAK;AAAA,UAClB,UAAU;AAAA,UACV,MAAM;AAAA,UACN;AAAA,QACV;AAAA,MACO;AAAA,IACP,WAAe,KAAK,gBAAgB,oBAAoB;AAElD,UAAI,CAAC,iBAAiB,UAAU,GAAG;AACjC,eAAO,oBAAqB;AAAA,MACpC;AACM,UAAI;AACF,YAAI,OAAO,OAAO,KAAK,SAAS,WAAW,KAAK,MAAM,KAAK,IAAI,IAAI,KAAK;AACxE,eAAO;AAAA,UACL;AAAA,UACA,YAAY;AAAA,YACV;AAAA,YACA;AAAA,YACA,aAAa,KAAK;AAAA,YAClB,UAAU;AAAA,YACV;AAAA,YACA,MAAM;AAAA,UAClB;AAAA,QACS;AAAA,MACF,SAAQ,GAAG;AACV,eAAO,oBAAqB;AAAA,MACpC;AAAA,IACA;AAAA,EACA;AACE,YAAU,OAAO,aAAa,YAAY,+CAA+C;AACzF,MAAI;AACJ,MAAI;AACJ,MAAI,KAAK,UAAU;AACjB,mBAAe,8BAA8B,KAAK,QAAQ;AAC1D,eAAW,KAAK;AAAA,EACpB,WAAa,KAAK,gBAAgB,UAAU;AACxC,mBAAe,8BAA8B,KAAK,IAAI;AACtD,eAAW,KAAK;AAAA,EACpB,WAAa,KAAK,gBAAgB,iBAAiB;AAC/C,mBAAe,KAAK;AACpB,eAAW,8BAA8B,YAAY;AAAA,EACzD,WAAa,KAAK,QAAQ,MAAM;AAC5B,mBAAe,IAAI,gBAAiB;AACpC,eAAW,IAAI,SAAU;AAAA,EAC7B,OAAS;AACL,QAAI;AACF,qBAAe,IAAI,gBAAgB,KAAK,IAAI;AAC5C,iBAAW,8BAA8B,YAAY;AAAA,IACtD,SAAQ,GAAG;AACV,aAAO,oBAAqB;AAAA,IAClC;AAAA,EACA;AACE,MAAI,aAAa;AAAA,IACf;AAAA,IACA;AAAA,IACA,aAAa,QAAQ,KAAK,eAAe;AAAA,IACzC;AAAA,IACA,MAAM;AAAA,IACN,MAAM;AAAA,EACP;AACD,MAAI,iBAAiB,WAAW,UAAU,GAAG;AAC3C,WAAO;AAAA,MACL;AAAA,MACA;AAAA,IACD;AAAA,EACL;AAEE,MAAI,aAAa,UAAU,IAAI;AAI/B,MAAI,aAAa,WAAW,UAAU,mBAAmB,WAAW,MAAM,GAAG;AAC3E,iBAAa,OAAO,SAAS,EAAE;AAAA,EACnC;AACE,aAAW,SAAS,MAAM;AAC1B,SAAO;AAAA,IACL,MAAM,WAAW,UAAU;AAAA,IAC3B;AAAA,EACD;AACH;AAGA,SAAS,8BAA8B,SAAS,YAAY,iBAAiB;AAC3E,MAAI,oBAAoB,QAAQ;AAC9B,sBAAkB;AAAA,EACtB;AACE,MAAI,QAAQ,QAAQ,UAAU,OAAK,EAAE,MAAM,OAAO,UAAU;AAC5D,MAAI,SAAS,GAAG;AACd,WAAO,QAAQ,MAAM,GAAG,kBAAkB,QAAQ,IAAI,KAAK;AAAA,EAC/D;AACE,SAAO;AACT;AACA,SAAS,iBAAiB,SAAS,OAAO,SAAS,YAAY,UAAU,kBAAkB,6BAA6B,wBAAwB,yBAAyB,uBAAuB,iBAAiB,kBAAkB,kBAAkB,aAAa,UAAU,qBAAqB;AAC/R,MAAI,eAAe,sBAAsB,cAAc,oBAAoB,CAAC,CAAC,IAAI,oBAAoB,CAAC,EAAE,QAAQ,oBAAoB,CAAC,EAAE,OAAO;AAC9I,MAAI,aAAa,QAAQ,UAAU,MAAM,QAAQ;AACjD,MAAI,UAAU,QAAQ,UAAU,QAAQ;AAExC,MAAI,kBAAkB;AACtB,MAAI,oBAAoB,MAAM,QAAQ;AAMpC,sBAAkB,8BAA8B,SAAS,OAAO,KAAK,MAAM,MAAM,EAAE,CAAC,GAAG,IAAI;AAAA,EAC5F,WAAU,uBAAuB,cAAc,oBAAoB,CAAC,CAAC,GAAG;AAGvE,sBAAkB,8BAA8B,SAAS,oBAAoB,CAAC,CAAC;AAAA,EACnF;AAIE,MAAI,eAAe,sBAAsB,oBAAoB,CAAC,EAAE,aAAa;AAC7E,MAAI,yBAAyB,+BAA+B,gBAAgB,gBAAgB;AAC5F,MAAI,oBAAoB,gBAAgB,OAAO,CAAC,OAAO,UAAU;AAC/D,QAAI;AAAA,MACF;AAAA,IACN,IAAQ;AACJ,QAAI,MAAM,MAAM;AAEd,aAAO;AAAA,IACb;AACI,QAAI,MAAM,UAAU,MAAM;AACxB,aAAO;AAAA,IACb;AACI,QAAI,kBAAkB;AACpB,aAAO,2BAA2B,OAAO,MAAM,YAAY,MAAM,MAAM;AAAA,IAC7E;AAEI,QAAI,YAAY,MAAM,YAAY,MAAM,QAAQ,KAAK,GAAG,KAAK,KAAK,wBAAwB,KAAK,QAAM,OAAO,MAAM,MAAM,EAAE,GAAG;AAC3H,aAAO;AAAA,IACb;AAKI,QAAI,oBAAoB,MAAM,QAAQ,KAAK;AAC3C,QAAI,iBAAiB;AACrB,WAAO,uBAAuB,OAAOJ,WAAS;AAAA,MAC5C;AAAA,MACA,eAAe,kBAAkB;AAAA,MACjC;AAAA,MACA,YAAY,eAAe;AAAA,IAC5B,GAAE,YAAY;AAAA,MACb;AAAA,MACA;AAAA,MACA,yBAAyB,yBAAyB;AAAA;AAAA,QAElD,0BAA0B,WAAW,WAAW,WAAW,WAAW,QAAQ,WAAW,QAAQ;AAAA,QAEjG,WAAW,WAAW,QAAQ,UAAU,mBAAmB,mBAAmB,cAAc;AAAA;AAAA,IAClG,CAAK,CAAC;AAAA,EACN,CAAG;AAED,MAAI,uBAAuB,CAAE;AAC7B,mBAAiB,QAAQ,CAAC,GAAG,QAAQ;AAMnC,QAAI,oBAAoB,CAAC,QAAQ,KAAK,OAAK,EAAE,MAAM,OAAO,EAAE,OAAO,KAAK,gBAAgB,IAAI,GAAG,GAAG;AAChG;AAAA,IACN;AACI,QAAI,iBAAiB,YAAY,aAAa,EAAE,MAAM,QAAQ;AAK9D,QAAI,CAAC,gBAAgB;AACnB,2BAAqB,KAAK;AAAA,QACxB;AAAA,QACA,SAAS,EAAE;AAAA,QACX,MAAM,EAAE;AAAA,QACR,SAAS;AAAA,QACT,OAAO;AAAA,QACP,YAAY;AAAA,MACpB,CAAO;AACD;AAAA,IACN;AAII,QAAI,UAAU,MAAM,SAAS,IAAI,GAAG;AACpC,QAAI,eAAe,eAAe,gBAAgB,EAAE,IAAI;AACxD,QAAI,mBAAmB;AACvB,QAAI,iBAAiB,IAAI,GAAG,GAAG;AAE7B,yBAAmB;AAAA,IACpB,WAAU,sBAAsB,IAAI,GAAG,GAAG;AAEzC,4BAAsB,OAAO,GAAG;AAChC,yBAAmB;AAAA,IACzB,WAAe,WAAW,QAAQ,UAAU,UAAU,QAAQ,SAAS,QAAW;AAI5E,yBAAmB;AAAA,IACzB,OAAW;AAGL,yBAAmB,uBAAuB,cAAcA,WAAS;AAAA,QAC/D;AAAA,QACA,eAAe,MAAM,QAAQ,MAAM,QAAQ,SAAS,CAAC,EAAE;AAAA,QACvD;AAAA,QACA,YAAY,QAAQ,QAAQ,SAAS,CAAC,EAAE;AAAA,MACzC,GAAE,YAAY;AAAA,QACb;AAAA,QACA;AAAA,QACA,yBAAyB,yBAAyB,QAAQ;AAAA,MAClE,CAAO,CAAC;AAAA,IACR;AACI,QAAI,kBAAkB;AACpB,2BAAqB,KAAK;AAAA,QACxB;AAAA,QACA,SAAS,EAAE;AAAA,QACX,MAAM,EAAE;AAAA,QACR,SAAS;AAAA,QACT,OAAO;AAAA,QACP,YAAY,IAAI,gBAAe;AAAA,MACvC,CAAO;AAAA,IACP;AAAA,EACA,CAAG;AACD,SAAO,CAAC,mBAAmB,oBAAoB;AACjD;AACA,SAAS,2BAA2B,OAAO,YAAY,QAAQ;AAE7D,MAAI,MAAM,MAAM;AACd,WAAO;AAAA,EACX;AAEE,MAAI,CAAC,MAAM,QAAQ;AACjB,WAAO;AAAA,EACX;AACE,MAAI,UAAU,cAAc,QAAQ,WAAW,MAAM,EAAE,MAAM;AAC7D,MAAI,WAAW,UAAU,QAAQ,OAAO,MAAM,EAAE,MAAM;AAEtD,MAAI,CAAC,WAAW,UAAU;AACxB,WAAO;AAAA,EACX;AAEE,MAAI,OAAO,MAAM,WAAW,cAAc,MAAM,OAAO,YAAY,MAAM;AACvE,WAAO;AAAA,EACX;AAEE,SAAO,CAAC,WAAW,CAAC;AACtB;AACA,SAAS,YAAY,mBAAmB,cAAc,OAAO;AAC3D,MAAI;AAAA;AAAA,IAEJ,CAAC;AAAA,IAED,MAAM,MAAM,OAAO,aAAa,MAAM;AAAA;AAGtC,MAAI,gBAAgB,kBAAkB,MAAM,MAAM,EAAE,MAAM;AAE1D,SAAO,SAAS;AAClB;AACA,SAAS,mBAAmB,cAAc,OAAO;AAC/C,MAAI,cAAc,aAAa,MAAM;AACrC;AAAA;AAAA,IAEE,aAAa,aAAa,MAAM;AAAA;AAAA,IAGhC,eAAe,QAAQ,YAAY,SAAS,GAAG,KAAK,aAAa,OAAO,GAAG,MAAM,MAAM,OAAO,GAAG;AAAA;AAErG;AACA,SAAS,uBAAuB,aAAa,KAAK;AAChD,MAAI,YAAY,MAAM,kBAAkB;AACtC,QAAI,cAAc,YAAY,MAAM,iBAAiB,GAAG;AACxD,QAAI,OAAO,gBAAgB,WAAW;AACpC,aAAO;AAAA,IACb;AAAA,EACA;AACE,SAAO,IAAI;AACb;AACA,SAAS,gBAAgB,SAAS,UAAU,aAAa,UAAUI,qBAAoB;AACrF,MAAI;AACJ,MAAI;AACJ,MAAI,SAAS;AACX,QAAI,QAAQ,SAAS,OAAO;AAC5B,cAAU,OAAO,sDAAsD,OAAO;AAC9E,QAAI,CAAC,MAAM,UAAU;AACnB,YAAM,WAAW,CAAE;AAAA,IACzB;AACI,sBAAkB,MAAM;AAAA,EAC5B,OAAS;AACL,sBAAkB;AAAA,EACtB;AAIE,MAAI,iBAAiB,SAAS,OAAO,cAAY,CAAC,gBAAgB,KAAK,mBAAiB,YAAY,UAAU,aAAa,CAAC,CAAC;AAC7H,MAAI,YAAY,0BAA0B,gBAAgBA,qBAAoB,CAAC,WAAW,KAAK,SAAS,SAAS,mBAAmB,oBAAoB,OAAO,SAAS,iBAAiB,WAAW,GAAG,CAAC,GAAG,QAAQ;AACnN,kBAAgB,KAAK,GAAG,SAAS;AACnC;AACA,SAAS,YAAY,UAAU,eAAe;AAE5C,MAAI,QAAQ,YAAY,QAAQ,iBAAiB,SAAS,OAAO,cAAc,IAAI;AACjF,WAAO;AAAA,EACX;AAEE,MAAI,EAAE,SAAS,UAAU,cAAc,SAAS,SAAS,SAAS,cAAc,QAAQ,SAAS,kBAAkB,cAAc,gBAAgB;AAC/I,WAAO;AAAA,EACX;AAGE,OAAK,CAAC,SAAS,YAAY,SAAS,SAAS,WAAW,OAAO,CAAC,cAAc,YAAY,cAAc,SAAS,WAAW,IAAI;AAC9H,WAAO;AAAA,EACX;AAGE,SAAO,SAAS,SAAS,MAAM,CAAC,QAAQ,MAAM;AAC5C,QAAI;AACJ,YAAQ,wBAAwB,cAAc,aAAa,OAAO,SAAS,sBAAsB,KAAK,YAAU,YAAY,QAAQ,MAAM,CAAC;AAAA,EAC/I,CAAG;AACH;AAMA,eAAe,oBAAoB,OAAOA,qBAAoB,UAAU;AACtE,MAAI,CAAC,MAAM,MAAM;AACf;AAAA,EACJ;AACE,MAAI,YAAY,MAAM,MAAM,KAAM;AAIlC,MAAI,CAAC,MAAM,MAAM;AACf;AAAA,EACJ;AACE,MAAI,gBAAgB,SAAS,MAAM,EAAE;AACrC,YAAU,eAAe,4BAA4B;AASrD,MAAI,eAAe,CAAE;AACrB,WAAS,qBAAqB,WAAW;AACvC,QAAI,mBAAmB,cAAc,iBAAiB;AACtD,QAAI,8BAA8B,qBAAqB;AAAA;AAAA,IAGvD,sBAAsB;AACtB,YAAQ,CAAC,6BAA6B,YAAa,cAAc,KAAK,8BAAgC,oBAAoB,mFAAyF,8BAA+B,oBAAoB,qBAAsB;AAC5R,QAAI,CAAC,+BAA+B,CAAC,mBAAmB,IAAI,iBAAiB,GAAG;AAC9E,mBAAa,iBAAiB,IAAI,UAAU,iBAAiB;AAAA,IACnE;AAAA,EACA;AAGE,SAAO,OAAO,eAAe,YAAY;AAIzC,SAAO,OAAO,eAAeJ,WAAS,CAAA,GAAII,oBAAmB,aAAa,GAAG;AAAA,IAC3E,MAAM;AAAA,EACV,CAAG,CAAC;AACJ;AAEA,eAAe,oBAAoB,OAAO;AACxC,MAAI;AAAA,IACF;AAAA,EACJ,IAAM;AACJ,MAAI,gBAAgB,QAAQ,OAAO,OAAK,EAAE,UAAU;AACpD,MAAI,UAAU,MAAM,QAAQ,IAAI,cAAc,IAAI,OAAK,EAAE,QAAO,CAAE,CAAC;AACnE,SAAO,QAAQ,OAAO,CAAC,KAAK,QAAQ,MAAM,OAAO,OAAO,KAAK;AAAA,IAC3D,CAAC,cAAc,CAAC,EAAE,MAAM,EAAE,GAAG;AAAA,EAC9B,CAAA,GAAG,CAAA,CAAE;AACR;AACA,eAAe,qBAAqB,kBAAkB,MAAM,OAAO,SAAS,eAAe,SAAS,YAAY,UAAUA,qBAAoB,gBAAgB;AAC5J,MAAI,+BAA+B,QAAQ,IAAI,OAAK,EAAE,MAAM,OAAO,oBAAoB,EAAE,OAAOA,qBAAoB,QAAQ,IAAI,MAAS;AACzI,MAAI,YAAY,QAAQ,IAAI,CAAC,OAAO,MAAM;AACxC,QAAI,mBAAmB,6BAA6B,CAAC;AACrD,QAAI,aAAa,cAAc,KAAK,OAAK,EAAE,MAAM,OAAO,MAAM,MAAM,EAAE;AAKtE,QAAI,UAAU,OAAM,oBAAmB;AACrC,UAAI,mBAAmB,QAAQ,WAAW,UAAU,MAAM,MAAM,QAAQ,MAAM,MAAM,SAAS;AAC3F,qBAAa;AAAA,MACrB;AACM,aAAO,aAAa,mBAAmB,MAAM,SAAS,OAAO,kBAAkB,iBAAiB,cAAc,IAAI,QAAQ,QAAQ;AAAA,QAChI,MAAM,WAAW;AAAA,QACjB,QAAQ;AAAA,MAChB,CAAO;AAAA,IACF;AACD,WAAOJ,WAAS,CAAE,GAAE,OAAO;AAAA,MACzB;AAAA,MACA;AAAA,IACN,CAAK;AAAA,EACL,CAAG;AAID,MAAI,UAAU,MAAM,iBAAiB;AAAA,IACnC,SAAS;AAAA,IACT;AAAA,IACA,QAAQ,QAAQ,CAAC,EAAE;AAAA,IACnB;AAAA,IACA,SAAS;AAAA,EACb,CAAG;AAID,MAAI;AACF,UAAM,QAAQ,IAAI,4BAA4B;AAAA,EAC/C,SAAQ,GAAG;AAAA,EAEd;AACE,SAAO;AACT;AAEA,eAAe,mBAAmB,MAAM,SAAS,OAAO,kBAAkB,iBAAiB,eAAe;AACxG,MAAI;AACJ,MAAI;AACJ,MAAI,aAAa,aAAW;AAE1B,QAAI;AAGJ,QAAI,eAAe,IAAI,QAAQ,CAAC,GAAG,MAAM,SAAS,CAAC;AACnD,eAAW,MAAM,OAAQ;AACzB,YAAQ,OAAO,iBAAiB,SAAS,QAAQ;AACjD,QAAI,gBAAgB,SAAO;AACzB,UAAI,OAAO,YAAY,YAAY;AACjC,eAAO,QAAQ,OAAO,IAAI,MAAM,sEAAsE,MAAO,OAAO,iBAAkB,MAAM,MAAM,KAAK,IAAI,CAAC;AAAA,MACpK;AACM,aAAO,QAAQ;AAAA,QACb;AAAA,QACA,QAAQ,MAAM;AAAA,QACd,SAAS;AAAA,MACjB,GAAS,GAAI,QAAQ,SAAY,CAAC,GAAG,IAAI,CAAE,CAAC;AAAA,IACvC;AACD,QAAI,kBAAkB,YAAY;AAChC,UAAI;AACF,YAAI,MAAM,OAAO,kBAAkB,gBAAgB,SAAO,cAAc,GAAG,CAAC,IAAI;AAChF,eAAO;AAAA,UACL,MAAM;AAAA,UACN,QAAQ;AAAA,QACT;AAAA,MACF,SAAQ,GAAG;AACV,eAAO;AAAA,UACL,MAAM;AAAA,UACN,QAAQ;AAAA,QACT;AAAA,MACT;AAAA,IACA,GAAQ;AACJ,WAAO,QAAQ,KAAK,CAAC,gBAAgB,YAAY,CAAC;AAAA,EACnD;AACD,MAAI;AACF,QAAI,UAAU,MAAM,MAAM,IAAI;AAE9B,QAAI,kBAAkB;AACpB,UAAI,SAAS;AAEX,YAAI;AACJ,YAAI,CAAC,KAAK,IAAI,MAAM,QAAQ,IAAI;AAAA;AAAA;AAAA;AAAA,UAIhC,WAAW,OAAO,EAAE,MAAM,OAAK;AAC7B,2BAAe;AAAA,UACzB,CAAS;AAAA,UAAG;AAAA,QAAgB,CAAC;AACrB,YAAI,iBAAiB,QAAW;AAC9B,gBAAM;AAAA,QAChB;AACQ,iBAAS;AAAA,MACjB,OAAa;AAEL,cAAM;AACN,kBAAU,MAAM,MAAM,IAAI;AAC1B,YAAI,SAAS;AAIX,mBAAS,MAAM,WAAW,OAAO;AAAA,QAC3C,WAAmB,SAAS,UAAU;AAC5B,cAAI,MAAM,IAAI,IAAI,QAAQ,GAAG;AAC7B,cAAI,WAAW,IAAI,WAAW,IAAI;AAClC,gBAAM,uBAAuB,KAAK;AAAA,YAChC,QAAQ,QAAQ;AAAA,YAChB;AAAA,YACA,SAAS,MAAM,MAAM;AAAA,UACjC,CAAW;AAAA,QACX,OAAe;AAGL,iBAAO;AAAA,YACL,MAAM,WAAW;AAAA,YACjB,QAAQ;AAAA,UACT;AAAA,QACX;AAAA,MACA;AAAA,IACA,WAAe,CAAC,SAAS;AACnB,UAAI,MAAM,IAAI,IAAI,QAAQ,GAAG;AAC7B,UAAI,WAAW,IAAI,WAAW,IAAI;AAClC,YAAM,uBAAuB,KAAK;AAAA,QAChC;AAAA,MACR,CAAO;AAAA,IACP,OAAW;AACL,eAAS,MAAM,WAAW,OAAO;AAAA,IACvC;AACI,cAAU,OAAO,WAAW,QAAW,kBAAkB,SAAS,WAAW,cAAc,cAAc,iBAAiB,MAAO,MAAM,MAAM,KAAK,8CAA8C,OAAO,QAAQ,4CAA4C;AAAA,EAC5P,SAAQ,GAAG;AAIV,WAAO;AAAA,MACL,MAAM,WAAW;AAAA,MACjB,QAAQ;AAAA,IACT;AAAA,EACL,UAAY;AACR,QAAI,UAAU;AACZ,cAAQ,OAAO,oBAAoB,SAAS,QAAQ;AAAA,IAC1D;AAAA,EACA;AACE,SAAO;AACT;AACA,eAAe,sCAAsC,oBAAoB;AACvE,MAAI;AAAA,IACF;AAAA,IACA;AAAA,EACJ,IAAM;AACJ,MAAI,WAAW,MAAM,GAAG;AACtB,QAAIK;AACJ,QAAI;AACF,UAAI,cAAc,OAAO,QAAQ,IAAI,cAAc;AAGnD,UAAI,eAAe,wBAAwB,KAAK,WAAW,GAAG;AAC5D,YAAI,OAAO,QAAQ,MAAM;AACvB,UAAAA,QAAO;AAAA,QACjB,OAAe;AACL,UAAAA,QAAO,MAAM,OAAO,KAAM;AAAA,QACpC;AAAA,MACA,OAAa;AACL,QAAAA,QAAO,MAAM,OAAO,KAAM;AAAA,MAClC;AAAA,IACK,SAAQ,GAAG;AACV,aAAO;AAAA,QACL,MAAM,WAAW;AAAA,QACjB,OAAO;AAAA,MACR;AAAA,IACP;AACI,QAAI,SAAS,WAAW,OAAO;AAC7B,aAAO;AAAA,QACL,MAAM,WAAW;AAAA,QACjB,OAAO,IAAI,kBAAkB,OAAO,QAAQ,OAAO,YAAYA,KAAI;AAAA,QACnE,YAAY,OAAO;AAAA,QACnB,SAAS,OAAO;AAAA,MACjB;AAAA,IACP;AACI,WAAO;AAAA,MACL,MAAM,WAAW;AAAA,MACjB,MAAAA;AAAA,MACA,YAAY,OAAO;AAAA,MACnB,SAAS,OAAO;AAAA,IACjB;AAAA,EACL;AACE,MAAI,SAAS,WAAW,OAAO;AAC7B,QAAI,uBAAuB,MAAM,GAAG;AAClC,UAAI,eAAe;AACnB,UAAI,OAAO,gBAAgB,OAAO;AAChC,YAAI,cAAc;AAClB,eAAO;AAAA,UACL,MAAM,WAAW;AAAA,UACjB,OAAO,OAAO;AAAA,UACd,aAAa,eAAe,OAAO,SAAS,OAAO,SAAS,aAAa;AAAA,UACzE,UAAU,gBAAgB,OAAO,SAAS,QAAQ,cAAc,UAAU,IAAI,QAAQ,OAAO,KAAK,OAAO,IAAI;AAAA,QAC9G;AAAA,MACT;AAEM,aAAO;AAAA,QACL,MAAM,WAAW;AAAA,QACjB,OAAO,IAAI,oBAAoB,gBAAgB,OAAO,SAAS,OAAO,SAAS,cAAc,WAAW,KAAK,QAAW,OAAO,IAAI;AAAA,QACnI,YAAY,qBAAqB,MAAM,IAAI,OAAO,SAAS;AAAA,QAC3D,UAAU,gBAAgB,OAAO,SAAS,QAAQ,cAAc,UAAU,IAAI,QAAQ,OAAO,KAAK,OAAO,IAAI;AAAA,MAC9G;AAAA,IACP;AACI,WAAO;AAAA,MACL,MAAM,WAAW;AAAA,MACjB,OAAO;AAAA,MACP,YAAY,qBAAqB,MAAM,IAAI,OAAO,SAAS;AAAA,IAC5D;AAAA,EACL;AACE,MAAI,eAAe,MAAM,GAAG;AAC1B,QAAI,eAAe;AACnB,WAAO;AAAA,MACL,MAAM,WAAW;AAAA,MACjB,cAAc;AAAA,MACd,aAAa,gBAAgB,OAAO,SAAS,OAAO,SAAS,cAAc;AAAA,MAC3E,WAAW,gBAAgB,OAAO,SAAS,OAAO,SAAS,cAAc,YAAY,IAAI,QAAQ,OAAO,KAAK,OAAO;AAAA,IACrH;AAAA,EACL;AACE,MAAI,uBAAuB,MAAM,GAAG;AAClC,QAAI,eAAe;AACnB,WAAO;AAAA,MACL,MAAM,WAAW;AAAA,MACjB,MAAM,OAAO;AAAA,MACb,aAAa,gBAAgB,OAAO,SAAS,OAAO,SAAS,cAAc;AAAA,MAC3E,UAAU,gBAAgB,OAAO,SAAS,QAAQ,cAAc,UAAU,IAAI,QAAQ,OAAO,KAAK,OAAO,IAAI;AAAA,IAC9G;AAAA,EACL;AACE,SAAO;AAAA,IACL,MAAM,WAAW;AAAA,IACjB,MAAM;AAAA,EACP;AACH;AAEA,SAAS,yCAAyC,UAAU,SAAS,SAAS,SAAS,UAAU,sBAAsB;AACrH,MAAI,WAAW,SAAS,QAAQ,IAAI,UAAU;AAC9C,YAAU,UAAU,4EAA4E;AAChG,MAAI,CAAC,mBAAmB,KAAK,QAAQ,GAAG;AACtC,QAAI,iBAAiB,QAAQ,MAAM,GAAG,QAAQ,UAAU,OAAK,EAAE,MAAM,OAAO,OAAO,IAAI,CAAC;AACxF,eAAW,YAAY,IAAI,IAAI,QAAQ,GAAG,GAAG,gBAAgB,UAAU,MAAM,UAAU,oBAAoB;AAC3G,aAAS,QAAQ,IAAI,YAAY,QAAQ;AAAA,EAC7C;AACE,SAAO;AACT;AACA,SAAS,0BAA0B,UAAU,YAAY,UAAU;AACjE,MAAI,mBAAmB,KAAK,QAAQ,GAAG;AAErC,QAAI,qBAAqB;AACzB,QAAI,MAAM,mBAAmB,WAAW,IAAI,IAAI,IAAI,IAAI,WAAW,WAAW,kBAAkB,IAAI,IAAI,IAAI,kBAAkB;AAC9H,QAAI,iBAAiB,cAAc,IAAI,UAAU,QAAQ,KAAK;AAC9D,QAAI,IAAI,WAAW,WAAW,UAAU,gBAAgB;AACtD,aAAO,IAAI,WAAW,IAAI,SAAS,IAAI;AAAA,IAC7C;AAAA,EACA;AACE,SAAO;AACT;AAIA,SAAS,wBAAwB,SAAS,UAAU,QAAQ,YAAY;AACtE,MAAI,MAAM,QAAQ,UAAU,kBAAkB,QAAQ,CAAC,EAAE,SAAU;AACnE,MAAI,OAAO;AAAA,IACT;AAAA,EACD;AACD,MAAI,cAAc,iBAAiB,WAAW,UAAU,GAAG;AACzD,QAAI;AAAA,MACF;AAAA,MACA;AAAA,IACN,IAAQ;AAIJ,SAAK,SAAS,WAAW,YAAa;AACtC,QAAI,gBAAgB,oBAAoB;AACtC,WAAK,UAAU,IAAI,QAAQ;AAAA,QACzB,gBAAgB;AAAA,MACxB,CAAO;AACD,WAAK,OAAO,KAAK,UAAU,WAAW,IAAI;AAAA,IAChD,WAAe,gBAAgB,cAAc;AAEvC,WAAK,OAAO,WAAW;AAAA,IACxB,WAAU,gBAAgB,uCAAuC,WAAW,UAAU;AAErF,WAAK,OAAO,8BAA8B,WAAW,QAAQ;AAAA,IACnE,OAAW;AAEL,WAAK,OAAO,WAAW;AAAA,IAC7B;AAAA,EACA;AACE,SAAO,IAAI,QAAQ,KAAK,IAAI;AAC9B;AACA,SAAS,8BAA8B,UAAU;AAC/C,MAAI,eAAe,IAAI,gBAAiB;AACxC,WAAS,CAAC,KAAK,KAAK,KAAK,SAAS,QAAO,GAAI;AAE3C,iBAAa,OAAO,KAAK,OAAO,UAAU,WAAW,QAAQ,MAAM,IAAI;AAAA,EAC3E;AACE,SAAO;AACT;AACA,SAAS,8BAA8B,cAAc;AACnD,MAAI,WAAW,IAAI,SAAU;AAC7B,WAAS,CAAC,KAAK,KAAK,KAAK,aAAa,QAAO,GAAI;AAC/C,aAAS,OAAO,KAAK,KAAK;AAAA,EAC9B;AACE,SAAO;AACT;AACA,SAAS,uBAAuB,SAAS,SAAS,qBAAqB,iBAAiB,yBAAyB;AAE/G,MAAI,aAAa,CAAE;AACnB,MAAI,SAAS;AACb,MAAI;AACJ,MAAI,aAAa;AACjB,MAAI,gBAAgB,CAAE;AACtB,MAAI,eAAe,uBAAuB,cAAc,oBAAoB,CAAC,CAAC,IAAI,oBAAoB,CAAC,EAAE,QAAQ;AAEjH,UAAQ,QAAQ,WAAS;AACvB,QAAI,EAAE,MAAM,MAAM,MAAM,UAAU;AAChC;AAAA,IACN;AACI,QAAI,KAAK,MAAM,MAAM;AACrB,QAAI,SAAS,QAAQ,EAAE;AACvB,cAAU,CAAC,iBAAiB,MAAM,GAAG,qDAAqD;AAC1F,QAAI,cAAc,MAAM,GAAG;AACzB,UAAI,QAAQ,OAAO;AAInB,UAAI,iBAAiB,QAAW;AAC9B,gBAAQ;AACR,uBAAe;AAAA,MACvB;AACM,eAAS,UAAU,CAAE;AAGd;AAIL,YAAI,gBAAgB,oBAAoB,SAAS,EAAE;AACnD,YAAI,OAAO,cAAc,MAAM,EAAE,KAAK,MAAM;AAC1C,iBAAO,cAAc,MAAM,EAAE,IAAI;AAAA,QAC3C;AAAA,MACA;AAEM,iBAAW,EAAE,IAAI;AAGjB,UAAI,CAAC,YAAY;AACf,qBAAa;AACb,qBAAa,qBAAqB,OAAO,KAAK,IAAI,OAAO,MAAM,SAAS;AAAA,MAChF;AACM,UAAI,OAAO,SAAS;AAClB,sBAAc,EAAE,IAAI,OAAO;AAAA,MACnC;AAAA,IACA,OAAW;AACL,UAAI,iBAAiB,MAAM,GAAG;AAC5B,wBAAgB,IAAI,IAAI,OAAO,YAAY;AAC3C,mBAAW,EAAE,IAAI,OAAO,aAAa;AAGrC,YAAI,OAAO,cAAc,QAAQ,OAAO,eAAe,OAAO,CAAC,YAAY;AACzE,uBAAa,OAAO;AAAA,QAC9B;AACQ,YAAI,OAAO,SAAS;AAClB,wBAAc,EAAE,IAAI,OAAO;AAAA,QACrC;AAAA,MACA,OAAa;AACL,mBAAW,EAAE,IAAI,OAAO;AAGxB,YAAI,OAAO,cAAc,OAAO,eAAe,OAAO,CAAC,YAAY;AACjE,uBAAa,OAAO;AAAA,QAC9B;AACQ,YAAI,OAAO,SAAS;AAClB,wBAAc,EAAE,IAAI,OAAO;AAAA,QACrC;AAAA,MACA;AAAA,IACA;AAAA,EACA,CAAG;AAID,MAAI,iBAAiB,UAAa,qBAAqB;AACrD,aAAS;AAAA,MACP,CAAC,oBAAoB,CAAC,CAAC,GAAG;AAAA,IAC3B;AACD,eAAW,oBAAoB,CAAC,CAAC,IAAI;AAAA,EACzC;AACE,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA,YAAY,cAAc;AAAA,IAC1B;AAAA,EACD;AACH;AACA,SAAS,kBAAkB,OAAO,SAAS,SAAS,qBAAqB,sBAAsB,gBAAgB,iBAAiB;AAC9H,MAAI;AAAA,IACF;AAAA,IACA;AAAA,EACD,IAAG,uBAAuB,SAAS,SAAS,qBAAqB,eAClE;AAEA,uBAAqB,QAAQ,QAAM;AACjC,QAAI;AAAA,MACF;AAAA,MACA;AAAA,MACA;AAAA,IACN,IAAQ;AACJ,QAAI,SAAS,eAAe,GAAG;AAC/B,cAAU,QAAQ,2CAA2C;AAE7D,QAAI,cAAc,WAAW,OAAO,SAAS;AAE3C;AAAA,IACN,WAAe,cAAc,MAAM,GAAG;AAChC,UAAI,gBAAgB,oBAAoB,MAAM,SAAS,SAAS,OAAO,SAAS,MAAM,MAAM,EAAE;AAC9F,UAAI,EAAE,UAAU,OAAO,cAAc,MAAM,EAAE,IAAI;AAC/C,iBAASL,WAAS,CAAE,GAAE,QAAQ;AAAA,UAC5B,CAAC,cAAc,MAAM,EAAE,GAAG,OAAO;AAAA,QAC3C,CAAS;AAAA,MACT;AACM,YAAM,SAAS,OAAO,GAAG;AAAA,IAC/B,WAAe,iBAAiB,MAAM,GAAG;AAGnC,gBAAU,OAAO,yCAAyC;AAAA,IAChE,WAAe,iBAAiB,MAAM,GAAG;AAGnC,gBAAU,OAAO,iCAAiC;AAAA,IACxD,OAAW;AACL,UAAI,cAAc,eAAe,OAAO,IAAI;AAC5C,YAAM,SAAS,IAAI,KAAK,WAAW;AAAA,IACzC;AAAA,EACA,CAAG;AACD,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACD;AACH;AACA,SAAS,gBAAgB,YAAY,eAAe,SAAS,QAAQ;AACnE,MAAI,mBAAmBA,WAAS,CAAE,GAAE,aAAa;AACjD,WAAS,SAAS,SAAS;AACzB,QAAI,KAAK,MAAM,MAAM;AACrB,QAAI,cAAc,eAAe,EAAE,GAAG;AACpC,UAAI,cAAc,EAAE,MAAM,QAAW;AACnC,yBAAiB,EAAE,IAAI,cAAc,EAAE;AAAA,MAC/C;AAAA,IACA,WAAe,WAAW,EAAE,MAAM,UAAa,MAAM,MAAM,QAAQ;AAG7D,uBAAiB,EAAE,IAAI,WAAW,EAAE;AAAA,IAC1C;AACI,QAAI,UAAU,OAAO,eAAe,EAAE,GAAG;AAEvC;AAAA,IACN;AAAA,EACA;AACE,SAAO;AACT;AACA,SAAS,uBAAuB,qBAAqB;AACnD,MAAI,CAAC,qBAAqB;AACxB,WAAO,CAAE;AAAA,EACb;AACE,SAAO,cAAc,oBAAoB,CAAC,CAAC,IAAI;AAAA;AAAA,IAE7C,YAAY,CAAA;AAAA,EAChB,IAAM;AAAA,IACF,YAAY;AAAA,MACV,CAAC,oBAAoB,CAAC,CAAC,GAAG,oBAAoB,CAAC,EAAE;AAAA,IACvD;AAAA,EACG;AACH;AAIA,SAAS,oBAAoB,SAAS,SAAS;AAC7C,MAAI,kBAAkB,UAAU,QAAQ,MAAM,GAAG,QAAQ,UAAU,OAAK,EAAE,MAAM,OAAO,OAAO,IAAI,CAAC,IAAI,CAAC,GAAG,OAAO;AAClH,SAAO,gBAAgB,UAAU,KAAK,OAAK,EAAE,MAAM,qBAAqB,IAAI,KAAK,QAAQ,CAAC;AAC5F;AACA,SAAS,uBAAuB,QAAQ;AAEtC,MAAI,QAAQ,OAAO,WAAW,IAAI,OAAO,CAAC,IAAI,OAAO,KAAK,OAAK,EAAE,SAAS,CAAC,EAAE,QAAQ,EAAE,SAAS,GAAG,KAAK;AAAA,IACtG,IAAI;AAAA,EACL;AACD,SAAO;AAAA,IACL,SAAS,CAAC;AAAA,MACR,QAAQ,CAAE;AAAA,MACV,UAAU;AAAA,MACV,cAAc;AAAA,MACd;AAAA,IACN,CAAK;AAAA,IACD;AAAA,EACD;AACH;AACA,SAAS,uBAAuB,QAAQ,QAAQ;AAC9C,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ,IAAM,WAAW,SAAS,CAAA,IAAK;AAC7B,MAAI,aAAa;AACjB,MAAI,eAAe;AACnB,MAAI,WAAW,KAAK;AAClB,iBAAa;AACb,QAAI,UAAU,YAAY,SAAS;AACjC,qBAAe,gBAAgB,SAAS,kBAAmB,WAAW,YAAa,2CAA4C,UAAU,SAAU;AAAA,IACzJ,WAAe,SAAS,gBAAgB;AAClC,qBAAe;AAAA,IACrB,WAAe,SAAS,gBAAgB;AAClC,qBAAe;AAAA,IACrB;AAAA,EACA,WAAa,WAAW,KAAK;AACzB,iBAAa;AACb,mBAAe,YAAa,UAAU,2BAA6B,WAAW;AAAA,EAClF,WAAa,WAAW,KAAK;AACzB,iBAAa;AACb,mBAAe,2BAA4B,WAAW;AAAA,EAC1D,WAAa,WAAW,KAAK;AACzB,iBAAa;AACb,QAAI,UAAU,YAAY,SAAS;AACjC,qBAAe,gBAAgB,OAAO,YAAa,IAAG,kBAAmB,WAAW,YAAa,4CAA6C,UAAU,SAAU;AAAA,IACnK,WAAU,QAAQ;AACjB,qBAAe,6BAA8B,OAAO,YAAa,IAAG;AAAA,IAC1E;AAAA,EACA;AACE,SAAO,IAAI,kBAAkB,UAAU,KAAK,YAAY,IAAI,MAAM,YAAY,GAAG,IAAI;AACvF;AAEA,SAAS,aAAa,SAAS;AAC7B,MAAI,UAAU,OAAO,QAAQ,OAAO;AACpC,WAAS,IAAI,QAAQ,SAAS,GAAG,KAAK,GAAG,KAAK;AAC5C,QAAI,CAAC,KAAK,MAAM,IAAI,QAAQ,CAAC;AAC7B,QAAI,iBAAiB,MAAM,GAAG;AAC5B,aAAO;AAAA,QACL;AAAA,QACA;AAAA,MACD;AAAA,IACP;AAAA,EACA;AACA;AACA,SAAS,kBAAkB,MAAM;AAC/B,MAAI,aAAa,OAAO,SAAS,WAAW,UAAU,IAAI,IAAI;AAC9D,SAAO,WAAWA,WAAS,CAAE,GAAE,YAAY;AAAA,IACzC,MAAM;AAAA,EACV,CAAG,CAAC;AACJ;AACA,SAAS,iBAAiB,GAAG,GAAG;AAC9B,MAAI,EAAE,aAAa,EAAE,YAAY,EAAE,WAAW,EAAE,QAAQ;AACtD,WAAO;AAAA,EACX;AACE,MAAI,EAAE,SAAS,IAAI;AAEjB,WAAO,EAAE,SAAS;AAAA,EACnB,WAAU,EAAE,SAAS,EAAE,MAAM;AAE5B,WAAO;AAAA,EACX,WAAa,EAAE,SAAS,IAAI;AAExB,WAAO;AAAA,EACX;AAGE,SAAO;AACT;AAIA,SAAS,mCAAmC,QAAQ;AAClD,SAAO,WAAW,OAAO,MAAM,KAAK,oBAAoB,IAAI,OAAO,OAAO,MAAM;AAClF;AACA,SAAS,iBAAiB,QAAQ;AAChC,SAAO,OAAO,SAAS,WAAW;AACpC;AACA,SAAS,cAAc,QAAQ;AAC7B,SAAO,OAAO,SAAS,WAAW;AACpC;AACA,SAAS,iBAAiB,QAAQ;AAChC,UAAQ,UAAU,OAAO,UAAU,WAAW;AAChD;AACA,SAAS,uBAAuB,OAAO;AACrC,SAAO,OAAO,UAAU,YAAY,SAAS,QAAQ,UAAU,SAAS,UAAU,SAAS,UAAU,SAAS,MAAM,SAAS;AAC/H;AACA,SAAS,eAAe,OAAO;AAC7B,MAAI,WAAW;AACf,SAAO,YAAY,OAAO,aAAa,YAAY,OAAO,SAAS,SAAS,YAAY,OAAO,SAAS,cAAc,cAAc,OAAO,SAAS,WAAW,cAAc,OAAO,SAAS,gBAAgB;AAC/M;AACA,SAAS,WAAW,OAAO;AACzB,SAAO,SAAS,QAAQ,OAAO,MAAM,WAAW,YAAY,OAAO,MAAM,eAAe,YAAY,OAAO,MAAM,YAAY,YAAY,OAAO,MAAM,SAAS;AACjK;AASA,SAAS,cAAc,QAAQ;AAC7B,SAAO,oBAAoB,IAAI,OAAO,YAAW,CAAE;AACrD;AACA,SAAS,iBAAiB,QAAQ;AAChC,SAAO,qBAAqB,IAAI,OAAO,YAAW,CAAE;AACtD;AACA,eAAe,iCAAiC,SAAS,SAAS,QAAQ,gBAAgB,mBAAmB;AAC3G,MAAI,UAAU,OAAO,QAAQ,OAAO;AACpC,WAAS,QAAQ,GAAG,QAAQ,QAAQ,QAAQ,SAAS;AACnD,QAAI,CAAC,SAAS,MAAM,IAAI,QAAQ,KAAK;AACrC,QAAI,QAAQ,QAAQ,KAAK,QAAM,KAAK,OAAO,SAAS,EAAE,MAAM,QAAQ,OAAO;AAI3E,QAAI,CAAC,OAAO;AACV;AAAA,IACN;AACI,QAAI,eAAe,eAAe,KAAK,OAAK,EAAE,MAAM,OAAO,MAAM,MAAM,EAAE;AACzE,QAAI,uBAAuB,gBAAgB,QAAQ,CAAC,mBAAmB,cAAc,KAAK,MAAM,qBAAqB,kBAAkB,MAAM,MAAM,EAAE,OAAO;AAC5J,QAAI,iBAAiB,MAAM,KAAK,sBAAsB;AAIpD,YAAM,oBAAoB,QAAQ,QAAQ,KAAK,EAAE,KAAK,CAAAY,YAAU;AAC9D,YAAIA,SAAQ;AACV,kBAAQ,OAAO,IAAIA;AAAA,QAC7B;AAAA,MACA,CAAO;AAAA,IACP;AAAA,EACA;AACA;AACA,eAAe,8BAA8B,SAAS,SAAS,sBAAsB;AACnF,WAAS,QAAQ,GAAG,QAAQ,qBAAqB,QAAQ,SAAS;AAChE,QAAI;AAAA,MACF;AAAA,MACA;AAAA,MACA;AAAA,IACN,IAAQ,qBAAqB,KAAK;AAC9B,QAAI,SAAS,QAAQ,GAAG;AACxB,QAAI,QAAQ,QAAQ,KAAK,QAAM,KAAK,OAAO,SAAS,EAAE,MAAM,QAAQ,OAAO;AAI3E,QAAI,CAAC,OAAO;AACV;AAAA,IACN;AACI,QAAI,iBAAiB,MAAM,GAAG;AAI5B,gBAAU,YAAY,sEAAsE;AAC5F,YAAM,oBAAoB,QAAQ,WAAW,QAAQ,IAAI,EAAE,KAAK,CAAAA,YAAU;AACxE,YAAIA,SAAQ;AACV,kBAAQ,GAAG,IAAIA;AAAA,QACzB;AAAA,MACA,CAAO;AAAA,IACP;AAAA,EACA;AACA;AACA,eAAe,oBAAoB,QAAQ,QAAQ,QAAQ;AACzD,MAAI,WAAW,QAAQ;AACrB,aAAS;AAAA,EACb;AACE,MAAI,UAAU,MAAM,OAAO,aAAa,YAAY,MAAM;AAC1D,MAAI,SAAS;AACX;AAAA,EACJ;AACE,MAAI,QAAQ;AACV,QAAI;AACF,aAAO;AAAA,QACL,MAAM,WAAW;AAAA,QACjB,MAAM,OAAO,aAAa;AAAA,MAC3B;AAAA,IACF,SAAQ,GAAG;AAEV,aAAO;AAAA,QACL,MAAM,WAAW;AAAA,QACjB,OAAO;AAAA,MACR;AAAA,IACP;AAAA,EACA;AACE,SAAO;AAAA,IACL,MAAM,WAAW;AAAA,IACjB,MAAM,OAAO,aAAa;AAAA,EAC3B;AACH;AACA,SAAS,mBAAmB,QAAQ;AAClC,SAAO,IAAI,gBAAgB,MAAM,EAAE,OAAO,OAAO,EAAE,KAAK,OAAK,MAAM,EAAE;AACvE;AACA,SAAS,eAAe,SAAS,UAAU;AACzC,MAAI,SAAS,OAAO,aAAa,WAAW,UAAU,QAAQ,EAAE,SAAS,SAAS;AAClF,MAAI,QAAQ,QAAQ,SAAS,CAAC,EAAE,MAAM,SAAS,mBAAmB,UAAU,EAAE,GAAG;AAE/E,WAAO,QAAQ,QAAQ,SAAS,CAAC;AAAA,EACrC;AAGE,MAAI,cAAc,2BAA2B,OAAO;AACpD,SAAO,YAAY,YAAY,SAAS,CAAC;AAC3C;AACA,SAAS,4BAA4B,YAAY;AAC/C,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ,IAAM;AACJ,MAAI,CAAC,cAAc,CAAC,cAAc,CAAC,aAAa;AAC9C;AAAA,EACJ;AACE,MAAI,QAAQ,MAAM;AAChB,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA,UAAU;AAAA,MACV,MAAM;AAAA,MACN;AAAA,IACD;AAAA,EACL,WAAa,YAAY,MAAM;AAC3B,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,MAAM;AAAA,MACN,MAAM;AAAA,IACP;AAAA,EACL,WAAa,SAAS,QAAW;AAC7B,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA,UAAU;AAAA,MACV;AAAA,MACA,MAAM;AAAA,IACP;AAAA,EACL;AACA;AACA,SAAS,qBAAqB,UAAU,YAAY;AAClD,MAAI,YAAY;AACd,QAAI,aAAa;AAAA,MACf,OAAO;AAAA,MACP;AAAA,MACA,YAAY,WAAW;AAAA,MACvB,YAAY,WAAW;AAAA,MACvB,aAAa,WAAW;AAAA,MACxB,UAAU,WAAW;AAAA,MACrB,MAAM,WAAW;AAAA,MACjB,MAAM,WAAW;AAAA,IAClB;AACD,WAAO;AAAA,EACX,OAAS;AACL,QAAI,aAAa;AAAA,MACf,OAAO;AAAA,MACP;AAAA,MACA,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,UAAU;AAAA,MACV,MAAM;AAAA,MACN,MAAM;AAAA,IACP;AACD,WAAO;AAAA,EACX;AACA;AACA,SAAS,wBAAwB,UAAU,YAAY;AACrD,MAAI,aAAa;AAAA,IACf,OAAO;AAAA,IACP;AAAA,IACA,YAAY,WAAW;AAAA,IACvB,YAAY,WAAW;AAAA,IACvB,aAAa,WAAW;AAAA,IACxB,UAAU,WAAW;AAAA,IACrB,MAAM,WAAW;AAAA,IACjB,MAAM,WAAW;AAAA,EAClB;AACD,SAAO;AACT;AACA,SAAS,kBAAkB,YAAYP,OAAM;AAC3C,MAAI,YAAY;AACd,QAAI,UAAU;AAAA,MACZ,OAAO;AAAA,MACP,YAAY,WAAW;AAAA,MACvB,YAAY,WAAW;AAAA,MACvB,aAAa,WAAW;AAAA,MACxB,UAAU,WAAW;AAAA,MACrB,MAAM,WAAW;AAAA,MACjB,MAAM,WAAW;AAAA,MACjB,MAAAA;AAAA,IACD;AACD,WAAO;AAAA,EACX,OAAS;AACL,QAAI,UAAU;AAAA,MACZ,OAAO;AAAA,MACP,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,UAAU;AAAA,MACV,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAAA;AAAA,IACD;AACD,WAAO;AAAA,EACX;AACA;AACA,SAAS,qBAAqB,YAAY,iBAAiB;AACzD,MAAI,UAAU;AAAA,IACZ,OAAO;AAAA,IACP,YAAY,WAAW;AAAA,IACvB,YAAY,WAAW;AAAA,IACvB,aAAa,WAAW;AAAA,IACxB,UAAU,WAAW;AAAA,IACrB,MAAM,WAAW;AAAA,IACjB,MAAM,WAAW;AAAA,IACjB,MAAM,kBAAkB,gBAAgB,OAAO;AAAA,EAChD;AACD,SAAO;AACT;AACA,SAAS,eAAeA,OAAM;AAC5B,MAAI,UAAU;AAAA,IACZ,OAAO;AAAA,IACP,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,UAAU;AAAA,IACV,MAAM;AAAA,IACN,MAAM;AAAA,IACN,MAAAA;AAAA,EACD;AACD,SAAO;AACT;AACA,SAAS,0BAA0B,SAAS,aAAa;AACvD,MAAI;AACF,QAAI,mBAAmB,QAAQ,eAAe,QAAQ,uBAAuB;AAC7E,QAAI,kBAAkB;AACpB,UAAI,OAAO,KAAK,MAAM,gBAAgB;AACtC,eAAS,CAAC,GAAG,CAAC,KAAK,OAAO,QAAQ,QAAQ,CAAA,CAAE,GAAG;AAC7C,YAAI,KAAK,MAAM,QAAQ,CAAC,GAAG;AACzB,sBAAY,IAAI,GAAG,IAAI,IAAI,KAAK,CAAA,CAAE,CAAC;AAAA,QAC7C;AAAA,MACA;AAAA,IACA;AAAA,EACG,SAAQ,GAAG;AAAA,EAEd;AACA;AACA,SAAS,0BAA0B,SAAS,aAAa;AACvD,MAAI,YAAY,OAAO,GAAG;AACxB,QAAI,OAAO,CAAE;AACb,aAAS,CAAC,GAAG,CAAC,KAAK,aAAa;AAC9B,WAAK,CAAC,IAAI,CAAC,GAAG,CAAC;AAAA,IACrB;AACI,QAAI;AACF,cAAQ,eAAe,QAAQ,yBAAyB,KAAK,UAAU,IAAI,CAAC;AAAA,IAC7E,SAAQ,OAAO;AACd,cAAQ,OAAO,gEAAgE,QAAQ,IAAI;AAAA,IACjG;AAAA,EACA;AACA;ACx6JA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAcA,SAAS,WAAW;AAClB,aAAW,OAAO,SAAS,OAAO,OAAO,KAAK,IAAI,SAAU,QAAQ;AAClE,aAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACrC,UAAA,SAAS,UAAU,CAAC;AACxB,eAAS,OAAO,QAAQ;AACtB,YAAI,OAAO,UAAU,eAAe,KAAK,QAAQ,GAAG,GAAG;AAC9C,iBAAA,GAAG,IAAI,OAAO,GAAG;AAAA,QAAA;AAAA,MAC1B;AAAA,IACF;AAEK,WAAA;AAAA,EACT;AACO,SAAA,SAAS,MAAM,MAAM,SAAS;AACvC;AAIM,MAAA,oBAAuCQ,6BAAA,cAAc,IAAI;AACpB;AACzC,oBAAkB,cAAc;AAClC;AACM,MAAA,yBAA4CA,6BAAA,cAAc,IAAI;AACzB;AACzC,yBAAuB,cAAc;AACvC;AACA,MAAM,eAAkCA,6BAAA,cAAc,IAAI;AACf;AACzC,eAAa,cAAc;AAC7B;AAYM,MAAA,oBAAuCA,6BAAA,cAAc,IAAI;AACpB;AACzC,oBAAkB,cAAc;AAClC;AACA,MAAM,kBAAqCA,6BAAA,cAAc,IAAI;AAClB;AACzC,kBAAgB,cAAc;AAChC;AACM,MAAA,0DAAgD;AAAA,EACpD,QAAQ;AAAA,EACR,SAAS,CAAC;AAAA,EACV,aAAa;AACf,CAAC;AAC0C;AACzC,eAAa,cAAc;AAC7B;AACA,MAAM,oBAAuCA,6BAAA,cAAc,IAAI;AACpB;AACzC,oBAAkB,cAAc;AAClC;AAQA,SAAS,QAAQ,IAAI,OAAO;AACtB,MAAA;AAAA,IACF;AAAA,EAAA,IACE,UAAU,SAAS,CAAA,IAAK;AAC3B,GAAA,uBAA+DC;AAAAA,IAAiB;AAAA;AAAA;AAAA,IAEjF;AAAA,EAAoE,IAA8B;AAC9F,MAAA;AAAA,IACF;AAAA,IACA;AAAA,EAAA,IACEC,aAAAA,WAAiB,iBAAiB;AAClC,MAAA;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,EAAA,IACE,gBAAgB,IAAI;AAAA,IACtB;AAAA,EAAA,CACD;AACD,MAAI,iBAAiB;AAMrB,MAAI,aAAa,KAAK;AACpB,qBAAiB,aAAa,MAAM,WAAW,UAAU,CAAC,UAAU,QAAQ,CAAC;AAAA,EAAA;AAE/E,SAAO,UAAU,WAAW;AAAA,IAC1B,UAAU;AAAA,IACV;AAAA,IACA;AAAA,EAAA,CACD;AACH;AAOA,SAAS,qBAAqB;AACrB,SAAAA,aAAiB,WAAA,eAAe,KAAK;AAC9C;AAYA,SAAS,cAAc;AACpB,GAAA,uBAA+DD;AAAAA,IAAiB;AAAA;AAAA;AAAA,IAEjF;AAAA,EAAwE,IAA8B;AAC/F,SAAAC,aAAiB,WAAA,eAAe,EAAE;AAC3C;AAiCA,MAAM,wBAAwB;AAG9B,SAAS,0BAA0B,IAAI;AACrC,MAAI,WAAWA,aAAAA,WAAiB,iBAAiB,EAAE;AACnD,MAAI,CAAC,UAAU;AAIbC,iBAAAA,gBAAsB,EAAE;AAAA,EAAA;AAE5B;AAQA,SAAS,cAAc;AACjB,MAAA;AAAA,IACF;AAAA,EAAA,IACED,aAAAA,WAAiB,YAAY;AAG1B,SAAA,cAAc,kBAAkB,IAAI,oBAAoB;AACjE;AACA,SAAS,sBAAsB;AAC5B,GAAA,uBAA+DD;AAAAA,IAAiB;AAAA;AAAA;AAAA,IAEjF;AAAA,EAAwE,IAA8B;AAClG,MAAA,oBAAoBC,aAAM,WAAW,iBAAiB;AACtD,MAAA;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,EAAA,IACEA,aAAAA,WAAiB,iBAAiB;AAClC,MAAA;AAAA,IACF;AAAA,EAAA,IACEA,aAAAA,WAAiB,YAAY;AAC7B,MAAA;AAAA,IACF,UAAU;AAAA,MACR,YAAY;AAChB,MAAI,qBAAqB,KAAK,UAAUE,oBAA2B,SAAS,OAAO,oBAAoB,CAAC;AACpG,MAAA,YAAYC,aAAM,OAAO,KAAK;AAClC,4BAA0B,MAAM;AAC9B,cAAU,UAAU;AAAA,EAAA,CACrB;AACD,MAAI,WAAWC,aAAAA,YAAkB,SAAU,IAAI,SAAS;AACtD,QAAI,YAAY,QAAQ;AACtB,gBAAU,CAAC;AAAA,IAAA;AAE2BC,YAAe,UAAU,SAAS,qBAAqB;AAI3F,QAAA,CAAC,UAAU,QAAS;AACpB,QAAA,OAAO,OAAO,UAAU;AAC1B,gBAAU,GAAG,EAAE;AACf;AAAA,IAAA;AAEE,QAAA,OAAO,UAAU,IAAI,KAAK,MAAM,kBAAkB,GAAG,kBAAkB,QAAQ,aAAa,MAAM;AAQlG,QAAA,qBAAqB,QAAQ,aAAa,KAAK;AAC5C,WAAA,WAAW,KAAK,aAAa,MAAM,WAAW,UAAU,CAAC,UAAU,KAAK,QAAQ,CAAC;AAAA,IAAA;AAEvF,KAAA,CAAC,CAAC,QAAQ,UAAU,UAAU,UAAU,UAAU,MAAM,MAAM,QAAQ,OAAO,OAAO;AAAA,EAAA,GACpF,CAAC,UAAU,WAAW,oBAAoB,kBAAkB,iBAAiB,CAAC;AAC1E,SAAA;AACT;AACA,MAAM,gBAAmCP,6BAAA,cAAc,IAAI;AAiB3D,SAAS,UAAU,SAAS;AAC1B,MAAI,SAASE,aAAAA,WAAiB,YAAY,EAAE;AAC5C,MAAI,QAAQ;AACU,WAAAM,6BAAoB,cAAA,cAAc,UAAU;AAAA,MAC9D,OAAO;AAAA,OACN,MAAM;AAAA,EAAA;AAEJ,SAAA;AACT;AAQA,SAAS,YAAY;AACf,MAAA;AAAA,IACF;AAAA,EAAA,IACEN,aAAAA,WAAiB,YAAY;AACjC,MAAI,aAAa,QAAQ,QAAQ,SAAS,CAAC;AACpC,SAAA,aAAa,WAAW,SAAS,CAAC;AAC3C;AAOA,SAAS,gBAAgB,IAAI,QAAQ;AAC/B,MAAA;AAAA,IACF;AAAA,EAAA,IACE,WAAW,SAAS,CAAA,IAAK;AACzB,MAAA;AAAA,IACF;AAAA,EAAA,IACEA,aAAAA,WAAiB,iBAAiB;AAClC,MAAA;AAAA,IACF;AAAA,EAAA,IACEA,aAAAA,WAAiB,YAAY;AAC7B,MAAA;AAAA,IACF,UAAU;AAAA,MACR,YAAY;AAChB,MAAI,qBAAqB,KAAK,UAAUE,oBAA2B,SAAS,OAAO,oBAAoB,CAAC;AACxG,SAAOK,aAAAA,QAAc,MAAM,UAAU,IAAI,KAAK,MAAM,kBAAkB,GAAG,kBAAkB,aAAa,MAAM,GAAG,CAAC,IAAI,oBAAoB,kBAAkB,QAAQ,CAAC;AACvK;AAeA,SAAS,cAAc,QAAQ,aAAa,iBAAiB,QAAQ;AAClE,GAAA,uBAA+DR;AAAAA,IAAiB;AAAA;AAAA;AAAA,IAEjF;AAAA,EAAsE,IAA8B;AAChG,MAAA;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,EAAA,IACNC,aAAAA,WAAiB,iBAAiB;AAClC,MAAA;AAAA,IACF,SAAS;AAAA,EAAA,IACPA,aAAAA,WAAiB,YAAY;AACjC,MAAI,aAAa,cAAc,cAAc,SAAS,CAAC;AACvD,MAAI,eAAe,aAAa,WAAW,SAAS,CAAC;AACjD,MAAA,iBAAiB,aAAa,WAAW,WAAW;AACpD,MAAA,qBAAqB,aAAa,WAAW,eAAe;AAC5D,MAAA,cAAc,cAAc,WAAW;AACA;AAqBrC,QAAA,aAAa,eAAe,YAAY,QAAQ;AACpD,gBAAY,gBAAgB,CAAC,eAAe,WAAW,SAAS,GAAG,GAAG,oEAAoE,MAAO,iBAAiB,2BAA6B,aAAa,kBAAmB;AAAA;AAAA,KAAgL,2CAA4C,aAAa,oBAAqB,YAAa,eAAe,MAAM,MAAM,aAAa,QAAQ,MAAO;AAAA,EAAA;AAEniB,MAAI,sBAAsB,YAAY;AAClC,MAAA;AAMG;AACM,eAAA;AAAA,EAAA;AAET,MAAA,WAAW,SAAS,YAAY;AACpC,MAAI,oBAAoB;AACxB,MAAI,uBAAuB,KAAK;AAe9B,QAAI,iBAAiB,mBAAmB,QAAQ,OAAO,EAAE,EAAE,MAAM,GAAG;AACpE,QAAI,WAAW,SAAS,QAAQ,OAAO,EAAE,EAAE,MAAM,GAAG;AACpD,wBAAoB,MAAM,SAAS,MAAM,eAAe,MAAM,EAAE,KAAK,GAAG;AAAA,EAAA;AAE1E,MAAI,UAAU,CAAC,YAAY,mBAAmB,gBAAgB,WAAW,gBAAgB,QAAQ,SAAS,IAAI,gBAAgB,UAAU,YAAY,QAAQ;AAAA,IAC1J,UAAU;AAAA,EAAA,CACX;AAC0C;AACDK,YAAe,eAAe,WAAW,MAAM,iCAAkC,SAAS,WAAW,SAAS,SAAS,SAAS,OAAO,IAAK;AAC5IA,YAAe,WAAW,QAAQ,QAAQ,QAAQ,SAAS,CAAC,EAAE,MAAM,YAAY,UAAa,QAAQ,QAAQ,SAAS,CAAC,EAAE,MAAM,cAAc,UAAa,QAAQ,QAAQ,SAAS,CAAC,EAAE,MAAM,SAAS,QAAW,qCAAsC,SAAS,WAAW,SAAS,SAAS,SAAS,OAAO,6IAA0J;AAAA,EAAI;AAEhf,MAAA,kBAAkB,eAAe,WAAW,QAAQ,IAAI,WAAS,OAAO,OAAO,CAAA,GAAI,OAAO;AAAA,IAC5F,QAAQ,OAAO,OAAO,CAAI,GAAA,cAAc,MAAM,MAAM;AAAA,IACpD,UAAU,UAAU;AAAA,MAAC;AAAA;AAAA,MAErB,UAAU,iBAAiB,UAAU,eAAe,MAAM,QAAQ,EAAE,WAAW,MAAM;AAAA,IAAA,CAAS;AAAA,IAC9F,cAAc,MAAM,iBAAiB,MAAM,qBAAqB,UAAU;AAAA,MAAC;AAAA;AAAA,MAE3E,UAAU,iBAAiB,UAAU,eAAe,MAAM,YAAY,EAAE,WAAW,MAAM;AAAA,IAAa,CAAA;AAAA,EACvG,CAAA,CAAC,GAAG,eAAe,iBAAiB,MAAM;AAmBpC,SAAA;AACT;AACA,SAAS,wBAAwB;AAC/B,MAAI,QAAQ,cAAc;AAC1B,MAAI,UAAU,qBAAqB,KAAK,IAAI,MAAM,SAAS,MAAM,MAAM,aAAa,iBAAiB,QAAQ,MAAM,UAAU,KAAK,UAAU,KAAK;AACjJ,MAAI,QAAQ,iBAAiB,QAAQ,MAAM,QAAQ;AACnD,MAAI,YAAY;AAChB,MAAI,YAAY;AAAA,IACd,SAAS;AAAA,IACT,iBAAiB;AAAA,EACnB;AACA,MAAI,aAAa;AAAA,IACf,SAAS;AAAA,IACT,iBAAiB;AAAA,EACnB;AACA,MAAI,UAAU;AAC6B;AACjC,YAAA,MAAM,wDAAwD,KAAK;AAC3E,2CAA2C,cAAAG,uBAAgB,MAAmBF,2CAAoB,KAAK,MAAM,qBAAyC,8CAAoC,KAAK,MAAM,gGAA6GA,6BAAAA,cAAoB,QAAQ;AAAA,MAC5U,OAAO;AAAA,OACN,eAAe,GAAG,OAAO,KAAkBA,6BAAAA,cAAoB,QAAQ;AAAA,MACxE,OAAO;AAAA,IAAA,GACN,cAAc,GAAG,sBAAsB,CAAC;AAAA,EAAA;AAE7C,SAA0BA,6BAAA,cAAcE,uBAAgB,MAAmBF,6BAAoB,cAAA,MAAM,MAAM,+BAA+B,GAAgBA,2CAAoB,MAAM;AAAA,IAClL,OAAO;AAAA,MACL,WAAW;AAAA,IAAA;AAAA,KAEZ,OAAO,GAAG,QAAqBA,6BAAAA,cAAoB,OAAO;AAAA,IAC3D,OAAO;AAAA,EAAA,GACN,KAAK,IAAI,MAAM,OAAO;AAC3B;AACA,MAAM,sBAAmCA,6BAAAA,cAAoB,uBAAuB,IAAI;AACxF,MAAM,4BAA4BG,aAAAA,UAAgB;AAAA,EAChD,YAAY,OAAO;AACjB,UAAM,KAAK;AACX,SAAK,QAAQ;AAAA,MACX,UAAU,MAAM;AAAA,MAChB,cAAc,MAAM;AAAA,MACpB,OAAO,MAAM;AAAA,IACf;AAAA,EAAA;AAAA,EAEF,OAAO,yBAAyB,OAAO;AAC9B,WAAA;AAAA,MACL;AAAA,IACF;AAAA,EAAA;AAAA,EAEF,OAAO,yBAAyB,OAAO,OAAO;AASxC,QAAA,MAAM,aAAa,MAAM,YAAY,MAAM,iBAAiB,UAAU,MAAM,iBAAiB,QAAQ;AAChG,aAAA;AAAA,QACL,OAAO,MAAM;AAAA,QACb,UAAU,MAAM;AAAA,QAChB,cAAc,MAAM;AAAA,MACtB;AAAA,IAAA;AAOK,WAAA;AAAA,MACL,OAAO,MAAM,UAAU,SAAY,MAAM,QAAQ,MAAM;AAAA,MACvD,UAAU,MAAM;AAAA,MAChB,cAAc,MAAM,gBAAgB,MAAM;AAAA,IAC5C;AAAA,EAAA;AAAA,EAEF,kBAAkB,OAAO,WAAW;AAC1B,YAAA,MAAM,yDAAyD,OAAO,SAAS;AAAA,EAAA;AAAA,EAEzF,SAAS;AACP,WAAO,KAAK,MAAM,UAAU,SAA+BH,6BAAA,cAAc,aAAa,UAAU;AAAA,MAC9F,OAAO,KAAK,MAAM;AAAA,IAAA,GACJA,6BAAoB,cAAA,kBAAkB,UAAU;AAAA,MAC9D,OAAO,KAAK,MAAM;AAAA,MAClB,UAAU,KAAK,MAAM;AAAA,IAAA,CACtB,CAAC,IAAI,KAAK,MAAM;AAAA,EAAA;AAErB;AACA,SAAS,cAAc,MAAM;AACvB,MAAA;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,EAAA,IACE;AACA,MAAA,oBAAoBN,aAAM,WAAW,iBAAiB;AAItD,MAAA,qBAAqB,kBAAkB,UAAU,kBAAkB,kBAAkB,MAAM,MAAM,gBAAgB,MAAM,MAAM,gBAAgB;AAC7H,sBAAA,cAAc,6BAA6B,MAAM,MAAM;AAAA,EAAA;AAEvD,SAAAM,6BAAoB,cAAA,aAAa,UAAU;AAAA,IAC7D,OAAO;AAAA,KACN,QAAQ;AACb;AACA,SAAS,eAAe,SAAS,eAAe,iBAAiB,QAAQ;AACnE,MAAA;AACJ,MAAI,kBAAkB,QAAQ;AAC5B,oBAAgB,CAAC;AAAA,EAAA;AAEnB,MAAI,oBAAoB,QAAQ;AACZ,sBAAA;AAAA,EAAA;AAEpB,MAAI,WAAW,QAAQ;AACZ,aAAA;AAAA,EAAA;AAEX,MAAI,WAAW,MAAM;AACf,QAAA;AACJ,QAAI,CAAC,iBAAiB;AACb,aAAA;AAAA,IAAA;AAET,QAAI,gBAAgB,QAAQ;AAG1B,gBAAU,gBAAgB;AAAA,IAAA,YAChB,UAAU,WAAW,QAAQ,QAAQ,uBAAuB,cAAc,WAAW,KAAK,CAAC,gBAAgB,eAAe,gBAAgB,QAAQ,SAAS,GAAG;AAOxK,gBAAU,gBAAgB;AAAA,IAAA,OACrB;AACE,aAAA;AAAA,IAAA;AAAA,EACT;AAEF,MAAI,kBAAkB;AAGtB,MAAI,UAAU,mBAAmB,oBAAoB,OAAO,SAAS,iBAAiB;AACtF,MAAI,UAAU,MAAM;AAClB,QAAI,aAAa,gBAAgB,UAAU,CAAA,MAAK,EAAE,MAAM,OAAO,UAAU,OAAO,SAAS,OAAO,EAAE,MAAM,EAAE,OAAO,MAAS;AAC1H,MAAE,cAAc,KAA6CP,UAAiB,OAAO,8DAA8D,OAAO,KAAK,MAAM,EAAE,KAAK,GAAG,CAAC,IAA8B;AAC5L,sBAAA,gBAAgB,MAAM,GAAG,KAAK,IAAI,gBAAgB,QAAQ,aAAa,CAAC,CAAC;AAAA,EAAA;AAK7F,MAAI,iBAAiB;AACrB,MAAI,gBAAgB;AAChB,MAAA,mBAAmB,UAAU,OAAO,qBAAqB;AAC3D,aAAS,IAAI,GAAG,IAAI,gBAAgB,QAAQ,KAAK;AAC3C,UAAA,QAAQ,gBAAgB,CAAC;AAE7B,UAAI,MAAM,MAAM,mBAAmB,MAAM,MAAM,wBAAwB;AACrD,wBAAA;AAAA,MAAA;AAEd,UAAA,MAAM,MAAM,IAAI;AACd,YAAA;AAAA,UACF;AAAA,UACA,QAAAW;AAAAA,QAAA,IACE;AACJ,YAAI,mBAAmB,MAAM,MAAM,UAAU,WAAW,MAAM,MAAM,EAAE,MAAM,WAAc,CAACA,WAAUA,QAAO,MAAM,MAAM,EAAE,MAAM;AAC5H,YAAA,MAAM,MAAM,QAAQ,kBAAkB;AAIvB,2BAAA;AACjB,cAAI,iBAAiB,GAAG;AACtB,8BAAkB,gBAAgB,MAAM,GAAG,gBAAgB,CAAC;AAAA,UAAA,OACvD;AACa,8BAAA,CAAC,gBAAgB,CAAC,CAAC;AAAA,UAAA;AAEvC;AAAA,QAAA;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAEF,SAAO,gBAAgB,YAAY,CAAC,QAAQ,OAAO,UAAU;AAEvD,QAAA;AACJ,QAAI,8BAA8B;AAClC,QAAI,eAAe;AACnB,QAAI,yBAAyB;AAC7B,QAAI,iBAAiB;AACX,cAAA,UAAU,MAAM,MAAM,KAAK,OAAO,MAAM,MAAM,EAAE,IAAI;AAC7C,qBAAA,MAAM,MAAM,gBAAgB;AAC3C,UAAI,gBAAgB;AACd,YAAA,gBAAgB,KAAK,UAAU,GAAG;AACxB,sBAAA,kBAAkB,OAAO,0EAA0E;AACjF,wCAAA;AACL,mCAAA;AAAA,QAAA,WAChB,kBAAkB,OAAO;AACJ,wCAAA;AACL,mCAAA,MAAM,MAAM,0BAA0B;AAAA,QAAA;AAAA,MACjE;AAAA,IACF;AAEEC,QAAAA,WAAU,cAAc,OAAO,gBAAgB,MAAM,GAAG,QAAQ,CAAC,CAAC;AACtE,QAAI,cAAc,MAAM;AAClB,UAAA;AACJ,UAAI,OAAO;AACE,mBAAA;AAAA,iBACF,6BAA6B;AAC3B,mBAAA;AAAA,MAAA,WACF,MAAM,MAAM,WAAW;AAOhC,mBAA8BL,6BAAAA,cAAc,MAAM,MAAM,WAAW,IAAI;AAAA,MAAA,WAC9D,MAAM,MAAM,SAAS;AAC9B,mBAAW,MAAM,MAAM;AAAA,MAAA,OAClB;AACM,mBAAA;AAAA,MAAA;AAEO,aAAAA,6BAAAA,cAAoB,eAAe;AAAA,QACrD;AAAA,QACA,cAAc;AAAA,UACZ;AAAA,UACA,SAAAK;AAAAA,UACA,aAAa,mBAAmB;AAAA,QAClC;AAAA,QACA;AAAA,MAAA,CACD;AAAA,IACH;AAIO,WAAA,oBAAoB,MAAM,MAAM,iBAAiB,MAAM,MAAM,gBAAgB,UAAU,KAAwBL,6BAAAA,cAAc,qBAAqB;AAAA,MACvJ,UAAU,gBAAgB;AAAA,MAC1B,cAAc,gBAAgB;AAAA,MAC9B,WAAW;AAAA,MACX;AAAA,MACA,UAAU,YAAY;AAAA,MACtB,cAAc;AAAA,QACZ,QAAQ;AAAA,QACR,SAAAK;AAAAA,QACA,aAAa;AAAA,MAAA;AAAA,IAEhB,CAAA,IAAI,YAAY;AAAA,KAChB,IAAI;AACT;AACA,IAAI,0CAAwCC,iBAAgB;AAC1DA,kBAAe,YAAY,IAAI;AAC/BA,kBAAe,gBAAgB,IAAI;AACnCA,kBAAe,mBAAmB,IAAI;AAC/BA,SAAAA;AACT,EAAE,kBAAkB,CAAA,CAAE;AACtB,IAAI,+CAA6CC,sBAAqB;AACpEA,uBAAoB,YAAY,IAAI;AACpCA,uBAAoB,eAAe,IAAI;AACvCA,uBAAoB,eAAe,IAAI;AACvCA,uBAAoB,eAAe,IAAI;AACvCA,uBAAoB,eAAe,IAAI;AACvCA,uBAAoB,oBAAoB,IAAI;AAC5CA,uBAAoB,YAAY,IAAI;AACpCA,uBAAoB,gBAAgB,IAAI;AACxCA,uBAAoB,mBAAmB,IAAI;AAC3CA,uBAAoB,YAAY,IAAI;AAC7BA,SAAAA;AACT,EAAE,uBAAuB,CAAA,CAAE;AAC3B,SAAS,0BAA0B,UAAU;AAC3C,SAAO,WAAW;AACpB;AACA,SAAS,qBAAqB,UAAU;AAClC,MAAA,MAAMb,aAAM,WAAW,iBAAiB;AAC3C,GAAA,MAA8CD,UAAiB,OAAO,0BAA0B,QAAQ,CAAC,IAA8B;AACjI,SAAA;AACT;AACA,SAAS,mBAAmB,UAAU;AAChC,MAAA,QAAQC,aAAM,WAAW,sBAAsB;AAClD,GAAA,QAAgDD,UAAiB,OAAO,0BAA0B,QAAQ,CAAC,IAA8B;AACnI,SAAA;AACT;AACA,SAAS,gBAAgB,UAAU;AAC7B,MAAA,QAAQC,aAAM,WAAW,YAAY;AACxC,GAAA,QAAgDD,UAAiB,OAAO,0BAA0B,QAAQ,CAAC,IAA8B;AACnI,SAAA;AACT;AAGA,SAAS,kBAAkB,UAAU;AAC/B,MAAA,QAAQ,gBAAgB,QAAQ;AACpC,MAAI,YAAY,MAAM,QAAQ,MAAM,QAAQ,SAAS,CAAC;AACrD,GAAA,UAAU,MAAM,KAA6CA,UAAiB,OAAO,WAAW,wDAA0D,IAA8B;AACzL,SAAO,UAAU,MAAM;AACzB;AAKA,SAAS,aAAa;AACb,SAAA,kBAAkB,oBAAoB,UAAU;AACzD;AAMA,SAAS,gBAAgB;AACnB,MAAA,QAAQ,mBAAmB,oBAAoB,aAAa;AAChE,SAAO,MAAM;AACf;AAMA,SAAS,iBAAiB;AACpB,MAAA,oBAAoB,qBAAqB,eAAe,cAAc;AACtE,MAAA,QAAQ,mBAAmB,oBAAoB,cAAc;AAC1D,SAAAQ,qBAAc,OAAO;AAAA,IAC1B,YAAY,kBAAkB,OAAO;AAAA,IACrC,OAAO,MAAM;AAAA,EAAA,IACX,CAAC,kBAAkB,OAAO,YAAY,MAAM,YAAY,CAAC;AAC/D;AAMA,SAAS,aAAa;AAChB,MAAA;AAAA,IACF;AAAA,IACA;AAAA,EAAA,IACE,mBAAmB,oBAAoB,UAAU;AACrD,SAAOA,aAAM,QAAQ,MAAM,QAAQ,IAAI,CAAK,MAAAO,2BAAkC,GAAG,UAAU,CAAC,GAAG,CAAC,SAAS,UAAU,CAAC;AACtH;AAKA,SAAS,gBAAgB;AACnB,MAAA,QAAQ,mBAAmB,oBAAoB,aAAa;AAC5D,MAAA,UAAU,kBAAkB,oBAAoB,aAAa;AACjE,MAAI,MAAM,UAAU,MAAM,OAAO,OAAO,KAAK,MAAM;AACzC,YAAA,MAAM,6DAA6D,UAAU,GAAG;AACjF,WAAA;AAAA,EAAA;AAEF,SAAA,MAAM,WAAW,OAAO;AACjC;AAKA,SAAS,mBAAmB,SAAS;AAC/B,MAAA,QAAQ,mBAAmB,oBAAoB,kBAAkB;AAC9D,SAAA,MAAM,WAAW,OAAO;AACjC;AAKA,SAAS,gBAAgB;AACnB,MAAA,QAAQ,mBAAmB,oBAAoB,aAAa;AAC5D,MAAA,UAAU,kBAAkB,oBAAoB,aAAa;AACjE,SAAO,MAAM,aAAa,MAAM,WAAW,OAAO,IAAI;AACxD;AAOA,SAAS,gBAAgB;AACnB,MAAA;AACA,MAAA,QAAQd,aAAM,WAAW,iBAAiB;AAC1C,MAAA,QAAQ,mBAAmB,oBAAoB,aAAa;AAC5D,MAAA,UAAU,kBAAkB,oBAAoB,aAAa;AAIjE,MAAI,UAAU,QAAW;AAChB,WAAA;AAAA,EAAA;AAIT,UAAQ,gBAAgB,MAAM,WAAW,OAAO,SAAS,cAAc,OAAO;AAChF;AAKA,SAAS,gBAAgB;AACnB,MAAA,QAAQA,aAAM,WAAW,YAAY;AAClC,SAAA,SAAS,OAAO,SAAS,MAAM;AACxC;AAKA,SAAS,gBAAgB;AACnB,MAAA,QAAQA,aAAM,WAAW,YAAY;AAClC,SAAA,SAAS,OAAO,SAAS,MAAM;AACxC;AAsEA,SAAS,oBAAoB;AACvB,MAAA;AAAA,IACF;AAAA,EAAA,IACE,qBAAqB,eAAe,iBAAiB;AACrD,MAAA,KAAK,kBAAkB,oBAAoB,iBAAiB;AAC5D,MAAA,YAAYG,aAAM,OAAO,KAAK;AAClC,4BAA0B,MAAM;AAC9B,cAAU,UAAU;AAAA,EAAA,CACrB;AACD,MAAI,WAAWC,aAAAA,YAAkB,SAAU,IAAI,SAAS;AACtD,QAAI,YAAY,QAAQ;AACtB,gBAAU,CAAC;AAAA,IAAA;AAE2BC,YAAe,UAAU,SAAS,qBAAqB;AAI3F,QAAA,CAAC,UAAU,QAAS;AACpB,QAAA,OAAO,OAAO,UAAU;AAC1B,aAAO,SAAS,EAAE;AAAA,IAAA,OACb;AACE,aAAA,SAAS,IAAI,SAAS;AAAA,QAC3B,aAAa;AAAA,MACf,GAAG,OAAO,CAAC;AAAA,IAAA;AAAA,EACb,GACC,CAAC,QAAQ,EAAE,CAAC;AACR,SAAA;AACT;AACA,MAAM,kBAAkB,CAAC;AACzB,SAAS,YAAY,KAAK,MAAM,SAAS;AACvC,MAAI,CAAC,QAAQ,CAAC,gBAAgB,GAAG,GAAG;AAClC,oBAAgB,GAAG,IAAI;AACiBA,YAAe,OAAO,OAAO;AAAA,EAAI;AAE7E;AAEA,MAAM,gBAAgB,CAAC;AACvB,SAAS,SAAS,KAAK,SAAS;AACe,MAAA,CAAC,cAAc,OAAO,GAAG;AACpE,kBAAc,OAAO,IAAI;AACzB,YAAQ,KAAK,OAAO;AAAA,EAAA;AAExB;AACA,MAAM,iBAAiB,CAAC,MAAM,KAAK,SAAS,SAAS,MAAM,0CAAoD,MAAM,QAAQ,sBAAsB,OAAO,sCAAsC,+BAA+B,OAAO,IAAI;AAC1O,SAAS,yBAAyB,cAAc,cAAc;AAC5D,OAAK,gBAAgB,OAAO,SAAS,aAAa,wBAAwB,QAAW;AACpE,mBAAA,sBAAsB,mFAAmF,gEAAgE;AAAA,EAAA;AAErL,OAAA,gBAAgB,OAAO,SAAS,aAAa,0BAA0B,WAAc,CAAC,gBAAgB,CAAC,aAAa,uBAAuB;AAC/H,mBAAA,wBAAwB,mEAAmE,kEAAkE;AAAA,EAAA;AAE9K,MAAI,cAAc;AACZ,QAAA,aAAa,sBAAsB,QAAW;AACjC,qBAAA,qBAAqB,0DAA0D,+DAA+D;AAAA,IAAA;AAE3J,QAAA,aAAa,2BAA2B,QAAW;AACtC,qBAAA,0BAA0B,wEAAwE,oEAAoE;AAAA,IAAA;AAEnL,QAAA,aAAa,wBAAwB,QAAW;AACnC,qBAAA,uBAAuB,yDAAyD,iEAAiE;AAAA,IAAA;AAE9J,QAAA,aAAa,mCAAmC,QAAW;AAC9C,qBAAA,kCAAkC,gFAAgF,4EAA4E;AAAA,IAAA;AAAA,EAC/M;AAEJ;AAoKA,SAAS,SAAS,OAAO;AACnB,MAAA;AAAA,IACF;AAAA,IACA,SAAAU;AAAAA,IACA;AAAA,IACA;AAAA,EAAA,IACE;AACH,GAAA,uBAA+DhB;AAAAA,IAAiB;AAAA;AAAA;AAAA,IAEjF;AAAA,EAAqE,IAA8B;AAC/F,MAAA;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,EAAA,IACNC,aAAAA,WAAiB,iBAAiB;AACEK,UAAe,CAAC,UAAU,uNAAiO;AAC/R,MAAA;AAAA,IACF;AAAA,EAAA,IACEL,aAAAA,WAAiB,YAAY;AAC7B,MAAA;AAAA,IACF,UAAU;AAAA,MACR,YAAY;AAChB,MAAI,WAAW,YAAY;AAIvB,MAAA,OAAO,UAAU,IAAIE,oBAA2B,SAAS,OAAO,oBAAoB,GAAG,kBAAkB,aAAa,MAAM;AAC5H,MAAA,WAAW,KAAK,UAAU,IAAI;AAClCc,eAAAA,UAAgB,MAAM,SAAS,KAAK,MAAM,QAAQ,GAAG;AAAA,IACnD,SAAAD;AAAAA,IACA;AAAA,IACA;AAAA,EAAA,CACD,GAAG,CAAC,UAAU,UAAU,UAAUA,UAAS,KAAK,CAAC;AAC3C,SAAA;AACT;AAMA,SAAS,OAAO,OAAO;AACd,SAAA,UAAU,MAAM,OAAO;AAChC;AAkBA,SAAS,OAAO,OAAO;AACjB,MAAA;AAAA,IACF,UAAU,eAAe;AAAA,IACzB,WAAW;AAAA,IACX,UAAU;AAAA,IACV,iBAAiB,OAAO;AAAA,IACxB;AAAA,IACA,QAAQ,aAAa;AAAA,IACrB;AAAA,EAAA,IACE;AACH,GAAA,CAAC,mBAAmB,IAA4ChB,UAAiB,OAAO,wGAA6G,IAA8B;AAIpO,MAAI,WAAW,aAAa,QAAQ,QAAQ,GAAG;AAC3C,MAAA,oBAAoBQ,aAAAA,QAAc,OAAO;AAAA,IAC3C;AAAA,IACA;AAAA,IACA,QAAQ;AAAA,IACR,QAAQ,SAAS;AAAA,MACf,sBAAsB;AAAA,IAAA,GACrB,MAAM;AAAA,MACP,CAAC,UAAU,QAAQ,WAAW,UAAU,CAAC;AACzC,MAAA,OAAO,iBAAiB,UAAU;AACpC,mBAAe,UAAU,YAAY;AAAA,EAAA;AAEnC,MAAA;AAAA,IACF,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,MAAM;AAAA,EAAA,IACJ;AACA,MAAA,kBAAkBA,aAAAA,QAAc,MAAM;AACpC,QAAA,mBAAmB,cAAc,UAAU,QAAQ;AACvD,QAAI,oBAAoB,MAAM;AACrB,aAAA;AAAA,IAAA;AAEF,WAAA;AAAA,MACL,UAAU;AAAA,QACR,UAAU;AAAA,QACV;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA;AAAA,IACF;AAAA,EAAA,GACC,CAAC,UAAU,UAAU,QAAQ,MAAM,OAAO,KAAK,cAAc,CAAC;AACzBF,UAAe,mBAAmB,MAAM,uBAAwB,WAAW,sCAAuC,MAAO,WAAW,SAAS,OAAO,2CAA4C,kDAAkD;AAC1R,MAAI,mBAAmB,MAAM;AACpB,WAAA;AAAA,EAAA;AAEW,SAAAC,6BAAoB,cAAA,kBAAkB,UAAU;AAAA,IAClE,OAAO;AAAA,EAAA,GACOA,6BAAoB,cAAA,gBAAgB,UAAU;AAAA,IAC5D;AAAA,IACA,OAAO;AAAA,EAAA,CACR,CAAC;AACJ;AAkBA,SAAS,MAAM,OAAO;AAChB,MAAA;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,EAAA,IACE;AACgB,SAAAA,6BAAAA,cAAoB,oBAAoB;AAAA,IAC1D;AAAA,IACA;AAAA,KACoBA,2CAAc,cAAc,MAAM,QAAQ,CAAC;AACnE;AACA,IAAI,6CAA2CW,oBAAmB;AAChEA,qBAAkBA,mBAAkB,SAAS,IAAI,CAAC,IAAI;AACtDA,qBAAkBA,mBAAkB,SAAS,IAAI,CAAC,IAAI;AACtDA,qBAAkBA,mBAAkB,OAAO,IAAI,CAAC,IAAI;AAC7CA,SAAAA;AACT,EAAE,qBAAqB,CAAA,CAAE;AACzB,MAAM,sBAAsB,IAAI,QAAQ,MAAM;AAAC,CAAC;AAChD,MAAM,2BAA2BR,aAAAA,UAAgB;AAAA,EAC/C,YAAY,OAAO;AACjB,UAAM,KAAK;AACX,SAAK,QAAQ;AAAA,MACX,OAAO;AAAA,IACT;AAAA,EAAA;AAAA,EAEF,OAAO,yBAAyB,OAAO;AAC9B,WAAA;AAAA,MACL;AAAA,IACF;AAAA,EAAA;AAAA,EAEF,kBAAkB,OAAO,WAAW;AAC1B,YAAA,MAAM,oDAAoD,OAAO,SAAS;AAAA,EAAA;AAAA,EAEpF,SAAS;AACH,QAAA;AAAA,MACF;AAAA,MACA;AAAA,MACA;AAAA,QACE,KAAK;AACT,QAAI,UAAU;AACd,QAAI,SAAS,kBAAkB;AAC3B,QAAA,EAAE,mBAAmB,UAAU;AAEjC,eAAS,kBAAkB;AAC3B,gBAAU,QAAQ,QAAQ;AACnB,aAAA,eAAe,SAAS,YAAY;AAAA,QACzC,KAAK,MAAM;AAAA,MAAA,CACZ;AACM,aAAA,eAAe,SAAS,SAAS;AAAA,QACtC,KAAK,MAAM;AAAA,MAAA,CACZ;AAAA,IAAA,WACQ,KAAK,MAAM,OAAO;AAE3B,eAAS,kBAAkB;AACvB,UAAA,cAAc,KAAK,MAAM;AAC7B,gBAAU,QAAQ,OAAS,EAAA,MAAM,MAAM;AAAA,MAAA,CAAE;AAClC,aAAA,eAAe,SAAS,YAAY;AAAA,QACzC,KAAK,MAAM;AAAA,MAAA,CACZ;AACM,aAAA,eAAe,SAAS,UAAU;AAAA,QACvC,KAAK,MAAM;AAAA,MAAA,CACZ;AAAA,IAAA,WACQ,QAAQ,UAAU;AAEjB,gBAAA;AACD,eAAA,YAAY,UAAU,kBAAkB,QAAQ,WAAW,UAAU,kBAAkB,UAAU,kBAAkB;AAAA,IAAA,OACvH;AAEL,eAAS,kBAAkB;AACpB,aAAA,eAAe,SAAS,YAAY;AAAA,QACzC,KAAK,MAAM;AAAA,MAAA,CACZ;AACD,gBAAU,QAAQ,KAAK,CAAAnB,UAAQ,OAAO,eAAe,SAAS,SAAS;AAAA,QACrE,KAAK,MAAMA;AAAA,MAAA,CACZ,GAAG,CAAA,UAAS,OAAO,eAAe,SAAS,UAAU;AAAA,QACpD,KAAK,MAAM;AAAA,MAAA,CACZ,CAAC;AAAA,IAAA;AAEJ,QAAI,WAAW,kBAAkB,SAAS,QAAQ,kBAAkB,sBAAsB;AAElF,YAAA;AAAA,IAAA;AAER,QAAI,WAAW,kBAAkB,SAAS,CAAC,cAAc;AAEvD,YAAM,QAAQ;AAAA,IAAA;AAEZ,QAAA,WAAW,kBAAkB,OAAO;AAElB,aAAAgB,6BAAoB,cAAA,aAAa,UAAU;AAAA,QAC7D,OAAO;AAAA,QACP,UAAU;AAAA,MAAA,CACX;AAAA,IAAA;AAEC,QAAA,WAAW,kBAAkB,SAAS;AAEpB,aAAAA,6BAAoB,cAAA,aAAa,UAAU;AAAA,QAC7D,OAAO;AAAA,QACP;AAAA,MAAA,CACD;AAAA,IAAA;AAIG,UAAA;AAAA,EAAA;AAEV;AAMA,SAAS,aAAa,OAAO;AACvB,MAAA;AAAA,IACF;AAAA,EAAA,IACE;AACJ,MAAIhB,QAAO,cAAc;AACzB,MAAI,WAAW,OAAO,aAAa,aAAa,SAASA,KAAI,IAAI;AACjE,SAA0BgB,2CAAcE,aAAAA,UAAgB,MAAM,QAAQ;AACxE;AA+DA,SAAS,mBAAmB,OAAO;AACjC,MAAI,UAAU;AAAA;AAAA;AAAA,IAGZ,kBAAkB,MAAM,iBAAiB,QAAQ,MAAM,gBAAgB;AAAA,EACzE;AACA,MAAI,MAAM,WAAW;AACwB;AACzC,UAAI,MAAM,SAAS;AACuBH,gBAAe,OAAO,iGAAsG;AAAA,MAAI;AAAA,IAC1K;AAEF,WAAO,OAAO,SAAS;AAAA,MACrB,SAAsBC,6BAAAA,cAAoB,MAAM,SAAS;AAAA,MACzD,WAAW;AAAA,IAAA,CACZ;AAAA,EAAA;AAEH,MAAI,MAAM,iBAAiB;AACkB;AACzC,UAAI,MAAM,wBAAwB;AACQD,gBAAe,OAAO,4HAAiI;AAAA,MAAI;AAAA,IACrM;AAEF,WAAO,OAAO,SAAS;AAAA,MACrB,wBAAqCC,6BAAAA,cAAoB,MAAM,eAAe;AAAA,MAC9E,iBAAiB;AAAA,IAAA,CAClB;AAAA,EAAA;AAEH,MAAI,MAAM,eAAe;AACoB;AACzC,UAAI,MAAM,cAAc;AACkBD,gBAAe,OAAO,8GAAmH;AAAA,MAAI;AAAA,IACvL;AAEF,WAAO,OAAO,SAAS;AAAA,MACrB,cAA2BC,6BAAAA,cAAoB,MAAM,aAAa;AAAA,MAClE,eAAe;AAAA,IAAA,CAChB;AAAA,EAAA;AAEI,SAAA;AACT;", "x_google_ignoreList": [0, 1]}