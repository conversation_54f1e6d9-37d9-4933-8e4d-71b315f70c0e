{"version": 3, "file": "home.dashboard-DOwWzjSy.js", "sources": ["../../../node_modules/lucide-react/dist/esm/icons/target.js", "../../../node_modules/recharts/es6/shape/Polygon.js", "../../../node_modules/lodash/maxBy.js", "../../../node_modules/lodash/minBy.js", "../../../node_modules/recharts/es6/polar/PolarRadiusAxis.js", "../../../node_modules/recharts/es6/polar/PolarAngleAxis.js", "../../../node_modules/recharts/es6/polar/Pie.js", "../../../node_modules/recharts/es6/chart/PieChart.js", "../../../app/routes/home.dashboard.tsx"], "sourcesContent": ["/**\n * @license lucide-react v0.453.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst Target = createLucideIcon(\"Target\", [\n  [\"circle\", { cx: \"12\", cy: \"12\", r: \"10\", key: \"1mglay\" }],\n  [\"circle\", { cx: \"12\", cy: \"12\", r: \"6\", key: \"1vlfrh\" }],\n  [\"circle\", { cx: \"12\", cy: \"12\", r: \"2\", key: \"1c9p78\" }]\n]);\n\nexport { Target as default };\n//# sourceMappingURL=target.js.map\n", "var _excluded = [\"points\", \"className\", \"baseLinePoints\", \"connectNulls\"];\nfunction _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }\nfunction _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }\nfunction _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } } return target; }\nfunction _toConsumableArray(arr) { return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread(); }\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\nfunction _iterableToArray(iter) { if (typeof Symbol !== \"undefined\" && iter[Symbol.iterator] != null || iter[\"@@iterator\"] != null) return Array.from(iter); }\nfunction _arrayWithoutHoles(arr) { if (Array.isArray(arr)) return _arrayLikeToArray(arr); }\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }\n/**\n * @fileOverview Polygon\n */\nimport React from 'react';\nimport clsx from 'clsx';\nimport { filterProps } from '../util/ReactUtils';\nvar isValidatePoint = function isValidatePoint(point) {\n  return point && point.x === +point.x && point.y === +point.y;\n};\nvar getParsedPoints = function getParsedPoints() {\n  var points = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : [];\n  var segmentPoints = [[]];\n  points.forEach(function (entry) {\n    if (isValidatePoint(entry)) {\n      segmentPoints[segmentPoints.length - 1].push(entry);\n    } else if (segmentPoints[segmentPoints.length - 1].length > 0) {\n      // add another path\n      segmentPoints.push([]);\n    }\n  });\n  if (isValidatePoint(points[0])) {\n    segmentPoints[segmentPoints.length - 1].push(points[0]);\n  }\n  if (segmentPoints[segmentPoints.length - 1].length <= 0) {\n    segmentPoints = segmentPoints.slice(0, -1);\n  }\n  return segmentPoints;\n};\nvar getSinglePolygonPath = function getSinglePolygonPath(points, connectNulls) {\n  var segmentPoints = getParsedPoints(points);\n  if (connectNulls) {\n    segmentPoints = [segmentPoints.reduce(function (res, segPoints) {\n      return [].concat(_toConsumableArray(res), _toConsumableArray(segPoints));\n    }, [])];\n  }\n  var polygonPath = segmentPoints.map(function (segPoints) {\n    return segPoints.reduce(function (path, point, index) {\n      return \"\".concat(path).concat(index === 0 ? 'M' : 'L').concat(point.x, \",\").concat(point.y);\n    }, '');\n  }).join('');\n  return segmentPoints.length === 1 ? \"\".concat(polygonPath, \"Z\") : polygonPath;\n};\nvar getRanglePath = function getRanglePath(points, baseLinePoints, connectNulls) {\n  var outerPath = getSinglePolygonPath(points, connectNulls);\n  return \"\".concat(outerPath.slice(-1) === 'Z' ? outerPath.slice(0, -1) : outerPath, \"L\").concat(getSinglePolygonPath(baseLinePoints.reverse(), connectNulls).slice(1));\n};\nexport var Polygon = function Polygon(props) {\n  var points = props.points,\n    className = props.className,\n    baseLinePoints = props.baseLinePoints,\n    connectNulls = props.connectNulls,\n    others = _objectWithoutProperties(props, _excluded);\n  if (!points || !points.length) {\n    return null;\n  }\n  var layerClass = clsx('recharts-polygon', className);\n  if (baseLinePoints && baseLinePoints.length) {\n    var hasStroke = others.stroke && others.stroke !== 'none';\n    var rangePath = getRanglePath(points, baseLinePoints, connectNulls);\n    return /*#__PURE__*/React.createElement(\"g\", {\n      className: layerClass\n    }, /*#__PURE__*/React.createElement(\"path\", _extends({}, filterProps(others, true), {\n      fill: rangePath.slice(-1) === 'Z' ? others.fill : 'none',\n      stroke: \"none\",\n      d: rangePath\n    })), hasStroke ? /*#__PURE__*/React.createElement(\"path\", _extends({}, filterProps(others, true), {\n      fill: \"none\",\n      d: getSinglePolygonPath(points, connectNulls)\n    })) : null, hasStroke ? /*#__PURE__*/React.createElement(\"path\", _extends({}, filterProps(others, true), {\n      fill: \"none\",\n      d: getSinglePolygonPath(baseLinePoints, connectNulls)\n    })) : null);\n  }\n  var singlePath = getSinglePolygonPath(points, connectNulls);\n  return /*#__PURE__*/React.createElement(\"path\", _extends({}, filterProps(others, true), {\n    fill: singlePath.slice(-1) === 'Z' ? others.fill : 'none',\n    className: layerClass,\n    d: singlePath\n  }));\n};", "var baseExtremum = require('./_baseExtremum'),\n    baseGt = require('./_baseGt'),\n    baseIteratee = require('./_baseIteratee');\n\n/**\n * This method is like `_.max` except that it accepts `iteratee` which is\n * invoked for each element in `array` to generate the criterion by which\n * the value is ranked. The iteratee is invoked with one argument: (value).\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Math\n * @param {Array} array The array to iterate over.\n * @param {Function} [iteratee=_.identity] The iteratee invoked per element.\n * @returns {*} Returns the maximum value.\n * @example\n *\n * var objects = [{ 'n': 1 }, { 'n': 2 }];\n *\n * _.maxBy(objects, function(o) { return o.n; });\n * // => { 'n': 2 }\n *\n * // The `_.property` iteratee shorthand.\n * _.maxBy(objects, 'n');\n * // => { 'n': 2 }\n */\nfunction maxBy(array, iteratee) {\n  return (array && array.length)\n    ? baseExtremum(array, baseIteratee(iteratee, 2), baseGt)\n    : undefined;\n}\n\nmodule.exports = maxBy;\n", "var baseExtremum = require('./_baseExtremum'),\n    baseIteratee = require('./_baseIteratee'),\n    baseLt = require('./_baseLt');\n\n/**\n * This method is like `_.min` except that it accepts `iteratee` which is\n * invoked for each element in `array` to generate the criterion by which\n * the value is ranked. The iteratee is invoked with one argument: (value).\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Math\n * @param {Array} array The array to iterate over.\n * @param {Function} [iteratee=_.identity] The iteratee invoked per element.\n * @returns {*} Returns the minimum value.\n * @example\n *\n * var objects = [{ 'n': 1 }, { 'n': 2 }];\n *\n * _.minBy(objects, function(o) { return o.n; });\n * // => { 'n': 1 }\n *\n * // The `_.property` iteratee shorthand.\n * _.minBy(objects, 'n');\n * // => { 'n': 1 }\n */\nfunction minBy(array, iteratee) {\n  return (array && array.length)\n    ? baseExtremum(array, baseIteratee(iteratee, 2), baseLt)\n    : undefined;\n}\n\nmodule.exports = minBy;\n", "var _excluded = [\"cx\", \"cy\", \"angle\", \"ticks\", \"axisLine\"],\n  _excluded2 = [\"ticks\", \"tick\", \"angle\", \"tickFormatter\", \"stroke\"];\nfunction _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nfunction _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }\nfunction _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } } return target; }\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor); } }\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, \"prototype\", { writable: false }); return Constructor; }\nfunction _callSuper(t, o, e) { return o = _getPrototypeOf(o), _possibleConstructorReturn(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], _getPrototypeOf(t).constructor) : o.apply(t, e)); }\nfunction _possibleConstructorReturn(self, call) { if (call && (_typeof(call) === \"object\" || typeof call === \"function\")) { return call; } else if (call !== void 0) { throw new TypeError(\"Derived constructors may only return object or undefined\"); } return _assertThisInitialized(self); }\nfunction _assertThisInitialized(self) { if (self === void 0) { throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); } return self; }\nfunction _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }\nfunction _getPrototypeOf(o) { _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function _getPrototypeOf(o) { return o.__proto__ || Object.getPrototypeOf(o); }; return _getPrototypeOf(o); }\nfunction _inherits(subClass, superClass) { if (typeof superClass !== \"function\" && superClass !== null) { throw new TypeError(\"Super expression must either be null or a function\"); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, writable: true, configurable: true } }); Object.defineProperty(subClass, \"prototype\", { writable: false }); if (superClass) _setPrototypeOf(subClass, superClass); }\nfunction _setPrototypeOf(o, p) { _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) { o.__proto__ = p; return o; }; return _setPrototypeOf(o, p); }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == _typeof(i) ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != _typeof(i)) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\n/**\n * @fileOverview The axis of polar coordinate system\n */\nimport React, { PureComponent } from 'react';\nimport maxBy from 'lodash/maxBy';\nimport minBy from 'lodash/minBy';\nimport isFunction from 'lodash/isFunction';\nimport clsx from 'clsx';\nimport { Text } from '../component/Text';\nimport { Label } from '../component/Label';\nimport { Layer } from '../container/Layer';\nimport { getTickClassName, polarToCartesian } from '../util/PolarUtils';\nimport { adaptEventsOfChild } from '../util/types';\nimport { filterProps } from '../util/ReactUtils';\nexport var PolarRadiusAxis = /*#__PURE__*/function (_PureComponent) {\n  function PolarRadiusAxis() {\n    _classCallCheck(this, PolarRadiusAxis);\n    return _callSuper(this, PolarRadiusAxis, arguments);\n  }\n  _inherits(PolarRadiusAxis, _PureComponent);\n  return _createClass(PolarRadiusAxis, [{\n    key: \"getTickValueCoord\",\n    value:\n    /**\n     * Calculate the coordinate of tick\n     * @param  {Number} coordinate The radius of tick\n     * @return {Object} (x, y)\n     */\n    function getTickValueCoord(_ref) {\n      var coordinate = _ref.coordinate;\n      var _this$props = this.props,\n        angle = _this$props.angle,\n        cx = _this$props.cx,\n        cy = _this$props.cy;\n      return polarToCartesian(cx, cy, coordinate, angle);\n    }\n  }, {\n    key: \"getTickTextAnchor\",\n    value: function getTickTextAnchor() {\n      var orientation = this.props.orientation;\n      var textAnchor;\n      switch (orientation) {\n        case 'left':\n          textAnchor = 'end';\n          break;\n        case 'right':\n          textAnchor = 'start';\n          break;\n        default:\n          textAnchor = 'middle';\n          break;\n      }\n      return textAnchor;\n    }\n  }, {\n    key: \"getViewBox\",\n    value: function getViewBox() {\n      var _this$props2 = this.props,\n        cx = _this$props2.cx,\n        cy = _this$props2.cy,\n        angle = _this$props2.angle,\n        ticks = _this$props2.ticks;\n      var maxRadiusTick = maxBy(ticks, function (entry) {\n        return entry.coordinate || 0;\n      });\n      var minRadiusTick = minBy(ticks, function (entry) {\n        return entry.coordinate || 0;\n      });\n      return {\n        cx: cx,\n        cy: cy,\n        startAngle: angle,\n        endAngle: angle,\n        innerRadius: minRadiusTick.coordinate || 0,\n        outerRadius: maxRadiusTick.coordinate || 0\n      };\n    }\n  }, {\n    key: \"renderAxisLine\",\n    value: function renderAxisLine() {\n      var _this$props3 = this.props,\n        cx = _this$props3.cx,\n        cy = _this$props3.cy,\n        angle = _this$props3.angle,\n        ticks = _this$props3.ticks,\n        axisLine = _this$props3.axisLine,\n        others = _objectWithoutProperties(_this$props3, _excluded);\n      var extent = ticks.reduce(function (result, entry) {\n        return [Math.min(result[0], entry.coordinate), Math.max(result[1], entry.coordinate)];\n      }, [Infinity, -Infinity]);\n      var point0 = polarToCartesian(cx, cy, extent[0], angle);\n      var point1 = polarToCartesian(cx, cy, extent[1], angle);\n      var props = _objectSpread(_objectSpread(_objectSpread({}, filterProps(others, false)), {}, {\n        fill: 'none'\n      }, filterProps(axisLine, false)), {}, {\n        x1: point0.x,\n        y1: point0.y,\n        x2: point1.x,\n        y2: point1.y\n      });\n      return /*#__PURE__*/React.createElement(\"line\", _extends({\n        className: \"recharts-polar-radius-axis-line\"\n      }, props));\n    }\n  }, {\n    key: \"renderTicks\",\n    value: function renderTicks() {\n      var _this = this;\n      var _this$props4 = this.props,\n        ticks = _this$props4.ticks,\n        tick = _this$props4.tick,\n        angle = _this$props4.angle,\n        tickFormatter = _this$props4.tickFormatter,\n        stroke = _this$props4.stroke,\n        others = _objectWithoutProperties(_this$props4, _excluded2);\n      var textAnchor = this.getTickTextAnchor();\n      var axisProps = filterProps(others, false);\n      var customTickProps = filterProps(tick, false);\n      var items = ticks.map(function (entry, i) {\n        var coord = _this.getTickValueCoord(entry);\n        var tickProps = _objectSpread(_objectSpread(_objectSpread(_objectSpread({\n          textAnchor: textAnchor,\n          transform: \"rotate(\".concat(90 - angle, \", \").concat(coord.x, \", \").concat(coord.y, \")\")\n        }, axisProps), {}, {\n          stroke: 'none',\n          fill: stroke\n        }, customTickProps), {}, {\n          index: i\n        }, coord), {}, {\n          payload: entry\n        });\n        return /*#__PURE__*/React.createElement(Layer, _extends({\n          className: clsx('recharts-polar-radius-axis-tick', getTickClassName(tick)),\n          key: \"tick-\".concat(entry.coordinate)\n        }, adaptEventsOfChild(_this.props, entry, i)), PolarRadiusAxis.renderTickItem(tick, tickProps, tickFormatter ? tickFormatter(entry.value, i) : entry.value));\n      });\n      return /*#__PURE__*/React.createElement(Layer, {\n        className: \"recharts-polar-radius-axis-ticks\"\n      }, items);\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this$props5 = this.props,\n        ticks = _this$props5.ticks,\n        axisLine = _this$props5.axisLine,\n        tick = _this$props5.tick;\n      if (!ticks || !ticks.length) {\n        return null;\n      }\n      return /*#__PURE__*/React.createElement(Layer, {\n        className: clsx('recharts-polar-radius-axis', this.props.className)\n      }, axisLine && this.renderAxisLine(), tick && this.renderTicks(), Label.renderCallByParent(this.props, this.getViewBox()));\n    }\n  }], [{\n    key: \"renderTickItem\",\n    value: function renderTickItem(option, props, value) {\n      var tickItem;\n      if ( /*#__PURE__*/React.isValidElement(option)) {\n        tickItem = /*#__PURE__*/React.cloneElement(option, props);\n      } else if (isFunction(option)) {\n        tickItem = option(props);\n      } else {\n        tickItem = /*#__PURE__*/React.createElement(Text, _extends({}, props, {\n          className: \"recharts-polar-radius-axis-tick-value\"\n        }), value);\n      }\n      return tickItem;\n    }\n  }]);\n}(PureComponent);\n_defineProperty(PolarRadiusAxis, \"displayName\", 'PolarRadiusAxis');\n_defineProperty(PolarRadiusAxis, \"axisType\", 'radiusAxis');\n_defineProperty(PolarRadiusAxis, \"defaultProps\", {\n  type: 'number',\n  radiusAxisId: 0,\n  cx: 0,\n  cy: 0,\n  angle: 0,\n  orientation: 'right',\n  stroke: '#ccc',\n  axisLine: true,\n  tick: true,\n  tickCount: 5,\n  allowDataOverflow: false,\n  scale: 'auto',\n  allowDuplicatedCategory: true\n});", "function _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nfunction _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor); } }\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, \"prototype\", { writable: false }); return Constructor; }\nfunction _callSuper(t, o, e) { return o = _getPrototypeOf(o), _possibleConstructorReturn(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], _getPrototypeOf(t).constructor) : o.apply(t, e)); }\nfunction _possibleConstructorReturn(self, call) { if (call && (_typeof(call) === \"object\" || typeof call === \"function\")) { return call; } else if (call !== void 0) { throw new TypeError(\"Derived constructors may only return object or undefined\"); } return _assertThisInitialized(self); }\nfunction _assertThisInitialized(self) { if (self === void 0) { throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); } return self; }\nfunction _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }\nfunction _getPrototypeOf(o) { _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function _getPrototypeOf(o) { return o.__proto__ || Object.getPrototypeOf(o); }; return _getPrototypeOf(o); }\nfunction _inherits(subClass, superClass) { if (typeof superClass !== \"function\" && superClass !== null) { throw new TypeError(\"Super expression must either be null or a function\"); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, writable: true, configurable: true } }); Object.defineProperty(subClass, \"prototype\", { writable: false }); if (superClass) _setPrototypeOf(subClass, superClass); }\nfunction _setPrototypeOf(o, p) { _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) { o.__proto__ = p; return o; }; return _setPrototypeOf(o, p); }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == _typeof(i) ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != _typeof(i)) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\n/**\n * @fileOverview Axis of radial direction\n */\nimport React, { PureComponent } from 'react';\nimport isFunction from 'lodash/isFunction';\nimport clsx from 'clsx';\nimport { Layer } from '../container/Layer';\nimport { Dot } from '../shape/Dot';\nimport { Polygon } from '../shape/Polygon';\nimport { Text } from '../component/Text';\nimport { adaptEventsOfChild } from '../util/types';\nimport { filterProps } from '../util/ReactUtils';\nimport { getTickClassName, polarToCartesian } from '../util/PolarUtils';\nvar RADIAN = Math.PI / 180;\nvar eps = 1e-5;\nexport var PolarAngleAxis = /*#__PURE__*/function (_PureComponent) {\n  function PolarAngleAxis() {\n    _classCallCheck(this, PolarAngleAxis);\n    return _callSuper(this, PolarAngleAxis, arguments);\n  }\n  _inherits(PolarAngleAxis, _PureComponent);\n  return _createClass(PolarAngleAxis, [{\n    key: \"getTickLineCoord\",\n    value:\n    /**\n     * Calculate the coordinate of line endpoint\n     * @param  {Object} data The Data if ticks\n     * @return {Object} (x0, y0): The start point of text,\n     *                  (x1, y1): The end point close to text,\n     *                  (x2, y2): The end point close to axis\n     */\n    function getTickLineCoord(data) {\n      var _this$props = this.props,\n        cx = _this$props.cx,\n        cy = _this$props.cy,\n        radius = _this$props.radius,\n        orientation = _this$props.orientation,\n        tickSize = _this$props.tickSize;\n      var tickLineSize = tickSize || 8;\n      var p1 = polarToCartesian(cx, cy, radius, data.coordinate);\n      var p2 = polarToCartesian(cx, cy, radius + (orientation === 'inner' ? -1 : 1) * tickLineSize, data.coordinate);\n      return {\n        x1: p1.x,\n        y1: p1.y,\n        x2: p2.x,\n        y2: p2.y\n      };\n    }\n\n    /**\n     * Get the text-anchor of each tick\n     * @param  {Object} data Data of ticks\n     * @return {String} text-anchor\n     */\n  }, {\n    key: \"getTickTextAnchor\",\n    value: function getTickTextAnchor(data) {\n      var orientation = this.props.orientation;\n      var cos = Math.cos(-data.coordinate * RADIAN);\n      var textAnchor;\n      if (cos > eps) {\n        textAnchor = orientation === 'outer' ? 'start' : 'end';\n      } else if (cos < -eps) {\n        textAnchor = orientation === 'outer' ? 'end' : 'start';\n      } else {\n        textAnchor = 'middle';\n      }\n      return textAnchor;\n    }\n  }, {\n    key: \"renderAxisLine\",\n    value: function renderAxisLine() {\n      var _this$props2 = this.props,\n        cx = _this$props2.cx,\n        cy = _this$props2.cy,\n        radius = _this$props2.radius,\n        axisLine = _this$props2.axisLine,\n        axisLineType = _this$props2.axisLineType;\n      var props = _objectSpread(_objectSpread({}, filterProps(this.props, false)), {}, {\n        fill: 'none'\n      }, filterProps(axisLine, false));\n      if (axisLineType === 'circle') {\n        return /*#__PURE__*/React.createElement(Dot, _extends({\n          className: \"recharts-polar-angle-axis-line\"\n        }, props, {\n          cx: cx,\n          cy: cy,\n          r: radius\n        }));\n      }\n      var ticks = this.props.ticks;\n      var points = ticks.map(function (entry) {\n        return polarToCartesian(cx, cy, radius, entry.coordinate);\n      });\n      return /*#__PURE__*/React.createElement(Polygon, _extends({\n        className: \"recharts-polar-angle-axis-line\"\n      }, props, {\n        points: points\n      }));\n    }\n  }, {\n    key: \"renderTicks\",\n    value: function renderTicks() {\n      var _this = this;\n      var _this$props3 = this.props,\n        ticks = _this$props3.ticks,\n        tick = _this$props3.tick,\n        tickLine = _this$props3.tickLine,\n        tickFormatter = _this$props3.tickFormatter,\n        stroke = _this$props3.stroke;\n      var axisProps = filterProps(this.props, false);\n      var customTickProps = filterProps(tick, false);\n      var tickLineProps = _objectSpread(_objectSpread({}, axisProps), {}, {\n        fill: 'none'\n      }, filterProps(tickLine, false));\n      var items = ticks.map(function (entry, i) {\n        var lineCoord = _this.getTickLineCoord(entry);\n        var textAnchor = _this.getTickTextAnchor(entry);\n        var tickProps = _objectSpread(_objectSpread(_objectSpread({\n          textAnchor: textAnchor\n        }, axisProps), {}, {\n          stroke: 'none',\n          fill: stroke\n        }, customTickProps), {}, {\n          index: i,\n          payload: entry,\n          x: lineCoord.x2,\n          y: lineCoord.y2\n        });\n        return /*#__PURE__*/React.createElement(Layer, _extends({\n          className: clsx('recharts-polar-angle-axis-tick', getTickClassName(tick)),\n          key: \"tick-\".concat(entry.coordinate)\n        }, adaptEventsOfChild(_this.props, entry, i)), tickLine && /*#__PURE__*/React.createElement(\"line\", _extends({\n          className: \"recharts-polar-angle-axis-tick-line\"\n        }, tickLineProps, lineCoord)), tick && PolarAngleAxis.renderTickItem(tick, tickProps, tickFormatter ? tickFormatter(entry.value, i) : entry.value));\n      });\n      return /*#__PURE__*/React.createElement(Layer, {\n        className: \"recharts-polar-angle-axis-ticks\"\n      }, items);\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this$props4 = this.props,\n        ticks = _this$props4.ticks,\n        radius = _this$props4.radius,\n        axisLine = _this$props4.axisLine;\n      if (radius <= 0 || !ticks || !ticks.length) {\n        return null;\n      }\n      return /*#__PURE__*/React.createElement(Layer, {\n        className: clsx('recharts-polar-angle-axis', this.props.className)\n      }, axisLine && this.renderAxisLine(), this.renderTicks());\n    }\n  }], [{\n    key: \"renderTickItem\",\n    value: function renderTickItem(option, props, value) {\n      var tickItem;\n      if ( /*#__PURE__*/React.isValidElement(option)) {\n        tickItem = /*#__PURE__*/React.cloneElement(option, props);\n      } else if (isFunction(option)) {\n        tickItem = option(props);\n      } else {\n        tickItem = /*#__PURE__*/React.createElement(Text, _extends({}, props, {\n          className: \"recharts-polar-angle-axis-tick-value\"\n        }), value);\n      }\n      return tickItem;\n    }\n  }]);\n}(PureComponent);\n_defineProperty(PolarAngleAxis, \"displayName\", 'PolarAngleAxis');\n_defineProperty(PolarAngleAxis, \"axisType\", 'angleAxis');\n_defineProperty(PolarAngleAxis, \"defaultProps\", {\n  type: 'category',\n  angleAxisId: 0,\n  scale: 'auto',\n  cx: 0,\n  cy: 0,\n  orientation: 'outer',\n  axisLine: true,\n  tickLine: true,\n  tickSize: 8,\n  tick: true,\n  hide: false,\n  allowDuplicatedCategory: true\n});", "var _Pie;\nfunction _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nfunction _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor); } }\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, \"prototype\", { writable: false }); return Constructor; }\nfunction _callSuper(t, o, e) { return o = _getPrototypeOf(o), _possibleConstructorReturn(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], _getPrototypeOf(t).constructor) : o.apply(t, e)); }\nfunction _possibleConstructorReturn(self, call) { if (call && (_typeof(call) === \"object\" || typeof call === \"function\")) { return call; } else if (call !== void 0) { throw new TypeError(\"Derived constructors may only return object or undefined\"); } return _assertThisInitialized(self); }\nfunction _assertThisInitialized(self) { if (self === void 0) { throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); } return self; }\nfunction _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }\nfunction _getPrototypeOf(o) { _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function _getPrototypeOf(o) { return o.__proto__ || Object.getPrototypeOf(o); }; return _getPrototypeOf(o); }\nfunction _inherits(subClass, superClass) { if (typeof superClass !== \"function\" && superClass !== null) { throw new TypeError(\"Super expression must either be null or a function\"); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, writable: true, configurable: true } }); Object.defineProperty(subClass, \"prototype\", { writable: false }); if (superClass) _setPrototypeOf(subClass, superClass); }\nfunction _setPrototypeOf(o, p) { _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) { o.__proto__ = p; return o; }; return _setPrototypeOf(o, p); }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == _typeof(i) ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != _typeof(i)) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\n/**\n * @fileOverview Render sectors of a pie\n */\nimport React, { PureComponent } from 'react';\nimport Animate from 'react-smooth';\nimport get from 'lodash/get';\nimport isEqual from 'lodash/isEqual';\nimport isNil from 'lodash/isNil';\nimport isFunction from 'lodash/isFunction';\nimport clsx from 'clsx';\nimport { Layer } from '../container/Layer';\nimport { Curve } from '../shape/Curve';\nimport { Text } from '../component/Text';\nimport { Label } from '../component/Label';\nimport { LabelList } from '../component/LabelList';\nimport { Cell } from '../component/Cell';\nimport { findAllByType, filterProps } from '../util/ReactUtils';\nimport { Global } from '../util/Global';\nimport { polarToCartesian, getMaxRadius } from '../util/PolarUtils';\nimport { isNumber, getPercentValue, mathSign, interpolateNumber, uniqueId } from '../util/DataUtils';\nimport { getValueByDataKey } from '../util/ChartUtils';\nimport { warn } from '../util/LogUtils';\nimport { adaptEventsOfChild } from '../util/types';\nimport { Shape } from '../util/ActiveShapeUtils';\nexport var Pie = /*#__PURE__*/function (_PureComponent) {\n  function Pie(props) {\n    var _this;\n    _classCallCheck(this, Pie);\n    _this = _callSuper(this, Pie, [props]);\n    _defineProperty(_this, \"pieRef\", null);\n    _defineProperty(_this, \"sectorRefs\", []);\n    _defineProperty(_this, \"id\", uniqueId('recharts-pie-'));\n    _defineProperty(_this, \"handleAnimationEnd\", function () {\n      var onAnimationEnd = _this.props.onAnimationEnd;\n      _this.setState({\n        isAnimationFinished: true\n      });\n      if (isFunction(onAnimationEnd)) {\n        onAnimationEnd();\n      }\n    });\n    _defineProperty(_this, \"handleAnimationStart\", function () {\n      var onAnimationStart = _this.props.onAnimationStart;\n      _this.setState({\n        isAnimationFinished: false\n      });\n      if (isFunction(onAnimationStart)) {\n        onAnimationStart();\n      }\n    });\n    _this.state = {\n      isAnimationFinished: !props.isAnimationActive,\n      prevIsAnimationActive: props.isAnimationActive,\n      prevAnimationId: props.animationId,\n      sectorToFocus: 0\n    };\n    return _this;\n  }\n  _inherits(Pie, _PureComponent);\n  return _createClass(Pie, [{\n    key: \"isActiveIndex\",\n    value: function isActiveIndex(i) {\n      var activeIndex = this.props.activeIndex;\n      if (Array.isArray(activeIndex)) {\n        return activeIndex.indexOf(i) !== -1;\n      }\n      return i === activeIndex;\n    }\n  }, {\n    key: \"hasActiveIndex\",\n    value: function hasActiveIndex() {\n      var activeIndex = this.props.activeIndex;\n      return Array.isArray(activeIndex) ? activeIndex.length !== 0 : activeIndex || activeIndex === 0;\n    }\n  }, {\n    key: \"renderLabels\",\n    value: function renderLabels(sectors) {\n      var isAnimationActive = this.props.isAnimationActive;\n      if (isAnimationActive && !this.state.isAnimationFinished) {\n        return null;\n      }\n      var _this$props = this.props,\n        label = _this$props.label,\n        labelLine = _this$props.labelLine,\n        dataKey = _this$props.dataKey,\n        valueKey = _this$props.valueKey;\n      var pieProps = filterProps(this.props, false);\n      var customLabelProps = filterProps(label, false);\n      var customLabelLineProps = filterProps(labelLine, false);\n      var offsetRadius = label && label.offsetRadius || 20;\n      var labels = sectors.map(function (entry, i) {\n        var midAngle = (entry.startAngle + entry.endAngle) / 2;\n        var endPoint = polarToCartesian(entry.cx, entry.cy, entry.outerRadius + offsetRadius, midAngle);\n        var labelProps = _objectSpread(_objectSpread(_objectSpread(_objectSpread({}, pieProps), entry), {}, {\n          stroke: 'none'\n        }, customLabelProps), {}, {\n          index: i,\n          textAnchor: Pie.getTextAnchor(endPoint.x, entry.cx)\n        }, endPoint);\n        var lineProps = _objectSpread(_objectSpread(_objectSpread(_objectSpread({}, pieProps), entry), {}, {\n          fill: 'none',\n          stroke: entry.fill\n        }, customLabelLineProps), {}, {\n          index: i,\n          points: [polarToCartesian(entry.cx, entry.cy, entry.outerRadius, midAngle), endPoint]\n        });\n        var realDataKey = dataKey;\n        // TODO: compatible to lower versions\n        if (isNil(dataKey) && isNil(valueKey)) {\n          realDataKey = 'value';\n        } else if (isNil(dataKey)) {\n          realDataKey = valueKey;\n        }\n        return (\n          /*#__PURE__*/\n          // eslint-disable-next-line react/no-array-index-key\n          React.createElement(Layer, {\n            key: \"label-\".concat(entry.startAngle, \"-\").concat(entry.endAngle, \"-\").concat(entry.midAngle, \"-\").concat(i)\n          }, labelLine && Pie.renderLabelLineItem(labelLine, lineProps, 'line'), Pie.renderLabelItem(label, labelProps, getValueByDataKey(entry, realDataKey)))\n        );\n      });\n      return /*#__PURE__*/React.createElement(Layer, {\n        className: \"recharts-pie-labels\"\n      }, labels);\n    }\n  }, {\n    key: \"renderSectorsStatically\",\n    value: function renderSectorsStatically(sectors) {\n      var _this2 = this;\n      var _this$props2 = this.props,\n        activeShape = _this$props2.activeShape,\n        blendStroke = _this$props2.blendStroke,\n        inactiveShapeProp = _this$props2.inactiveShape;\n      return sectors.map(function (entry, i) {\n        if ((entry === null || entry === void 0 ? void 0 : entry.startAngle) === 0 && (entry === null || entry === void 0 ? void 0 : entry.endAngle) === 0 && sectors.length !== 1) return null;\n        var isActive = _this2.isActiveIndex(i);\n        var inactiveShape = inactiveShapeProp && _this2.hasActiveIndex() ? inactiveShapeProp : null;\n        var sectorOptions = isActive ? activeShape : inactiveShape;\n        var sectorProps = _objectSpread(_objectSpread({}, entry), {}, {\n          stroke: blendStroke ? entry.fill : entry.stroke,\n          tabIndex: -1\n        });\n        return /*#__PURE__*/React.createElement(Layer, _extends({\n          ref: function ref(_ref) {\n            if (_ref && !_this2.sectorRefs.includes(_ref)) {\n              _this2.sectorRefs.push(_ref);\n            }\n          },\n          tabIndex: -1,\n          className: \"recharts-pie-sector\"\n        }, adaptEventsOfChild(_this2.props, entry, i), {\n          // eslint-disable-next-line react/no-array-index-key\n          key: \"sector-\".concat(entry === null || entry === void 0 ? void 0 : entry.startAngle, \"-\").concat(entry === null || entry === void 0 ? void 0 : entry.endAngle, \"-\").concat(entry.midAngle, \"-\").concat(i)\n        }), /*#__PURE__*/React.createElement(Shape, _extends({\n          option: sectorOptions,\n          isActive: isActive,\n          shapeType: \"sector\"\n        }, sectorProps)));\n      });\n    }\n  }, {\n    key: \"renderSectorsWithAnimation\",\n    value: function renderSectorsWithAnimation() {\n      var _this3 = this;\n      var _this$props3 = this.props,\n        sectors = _this$props3.sectors,\n        isAnimationActive = _this$props3.isAnimationActive,\n        animationBegin = _this$props3.animationBegin,\n        animationDuration = _this$props3.animationDuration,\n        animationEasing = _this$props3.animationEasing,\n        animationId = _this$props3.animationId;\n      var _this$state = this.state,\n        prevSectors = _this$state.prevSectors,\n        prevIsAnimationActive = _this$state.prevIsAnimationActive;\n      return /*#__PURE__*/React.createElement(Animate, {\n        begin: animationBegin,\n        duration: animationDuration,\n        isActive: isAnimationActive,\n        easing: animationEasing,\n        from: {\n          t: 0\n        },\n        to: {\n          t: 1\n        },\n        key: \"pie-\".concat(animationId, \"-\").concat(prevIsAnimationActive),\n        onAnimationStart: this.handleAnimationStart,\n        onAnimationEnd: this.handleAnimationEnd\n      }, function (_ref2) {\n        var t = _ref2.t;\n        var stepData = [];\n        var first = sectors && sectors[0];\n        var curAngle = first.startAngle;\n        sectors.forEach(function (entry, index) {\n          var prev = prevSectors && prevSectors[index];\n          var paddingAngle = index > 0 ? get(entry, 'paddingAngle', 0) : 0;\n          if (prev) {\n            var angleIp = interpolateNumber(prev.endAngle - prev.startAngle, entry.endAngle - entry.startAngle);\n            var latest = _objectSpread(_objectSpread({}, entry), {}, {\n              startAngle: curAngle + paddingAngle,\n              endAngle: curAngle + angleIp(t) + paddingAngle\n            });\n            stepData.push(latest);\n            curAngle = latest.endAngle;\n          } else {\n            var endAngle = entry.endAngle,\n              startAngle = entry.startAngle;\n            var interpolatorAngle = interpolateNumber(0, endAngle - startAngle);\n            var deltaAngle = interpolatorAngle(t);\n            var _latest = _objectSpread(_objectSpread({}, entry), {}, {\n              startAngle: curAngle + paddingAngle,\n              endAngle: curAngle + deltaAngle + paddingAngle\n            });\n            stepData.push(_latest);\n            curAngle = _latest.endAngle;\n          }\n        });\n        return /*#__PURE__*/React.createElement(Layer, null, _this3.renderSectorsStatically(stepData));\n      });\n    }\n  }, {\n    key: \"attachKeyboardHandlers\",\n    value: function attachKeyboardHandlers(pieRef) {\n      var _this4 = this;\n      // eslint-disable-next-line no-param-reassign\n      pieRef.onkeydown = function (e) {\n        if (!e.altKey) {\n          switch (e.key) {\n            case 'ArrowLeft':\n              {\n                var next = ++_this4.state.sectorToFocus % _this4.sectorRefs.length;\n                _this4.sectorRefs[next].focus();\n                _this4.setState({\n                  sectorToFocus: next\n                });\n                break;\n              }\n            case 'ArrowRight':\n              {\n                var _next = --_this4.state.sectorToFocus < 0 ? _this4.sectorRefs.length - 1 : _this4.state.sectorToFocus % _this4.sectorRefs.length;\n                _this4.sectorRefs[_next].focus();\n                _this4.setState({\n                  sectorToFocus: _next\n                });\n                break;\n              }\n            case 'Escape':\n              {\n                _this4.sectorRefs[_this4.state.sectorToFocus].blur();\n                _this4.setState({\n                  sectorToFocus: 0\n                });\n                break;\n              }\n            default:\n              {\n                // There is nothing to do here\n              }\n          }\n        }\n      };\n    }\n  }, {\n    key: \"renderSectors\",\n    value: function renderSectors() {\n      var _this$props4 = this.props,\n        sectors = _this$props4.sectors,\n        isAnimationActive = _this$props4.isAnimationActive;\n      var prevSectors = this.state.prevSectors;\n      if (isAnimationActive && sectors && sectors.length && (!prevSectors || !isEqual(prevSectors, sectors))) {\n        return this.renderSectorsWithAnimation();\n      }\n      return this.renderSectorsStatically(sectors);\n    }\n  }, {\n    key: \"componentDidMount\",\n    value: function componentDidMount() {\n      if (this.pieRef) {\n        this.attachKeyboardHandlers(this.pieRef);\n      }\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this5 = this;\n      var _this$props5 = this.props,\n        hide = _this$props5.hide,\n        sectors = _this$props5.sectors,\n        className = _this$props5.className,\n        label = _this$props5.label,\n        cx = _this$props5.cx,\n        cy = _this$props5.cy,\n        innerRadius = _this$props5.innerRadius,\n        outerRadius = _this$props5.outerRadius,\n        isAnimationActive = _this$props5.isAnimationActive;\n      var isAnimationFinished = this.state.isAnimationFinished;\n      if (hide || !sectors || !sectors.length || !isNumber(cx) || !isNumber(cy) || !isNumber(innerRadius) || !isNumber(outerRadius)) {\n        return null;\n      }\n      var layerClass = clsx('recharts-pie', className);\n      return /*#__PURE__*/React.createElement(Layer, {\n        tabIndex: this.props.rootTabIndex,\n        className: layerClass,\n        ref: function ref(_ref3) {\n          _this5.pieRef = _ref3;\n        }\n      }, this.renderSectors(), label && this.renderLabels(sectors), Label.renderCallByParent(this.props, null, false), (!isAnimationActive || isAnimationFinished) && LabelList.renderCallByParent(this.props, sectors, false));\n    }\n  }], [{\n    key: \"getDerivedStateFromProps\",\n    value: function getDerivedStateFromProps(nextProps, prevState) {\n      if (prevState.prevIsAnimationActive !== nextProps.isAnimationActive) {\n        return {\n          prevIsAnimationActive: nextProps.isAnimationActive,\n          prevAnimationId: nextProps.animationId,\n          curSectors: nextProps.sectors,\n          prevSectors: [],\n          isAnimationFinished: true\n        };\n      }\n      if (nextProps.isAnimationActive && nextProps.animationId !== prevState.prevAnimationId) {\n        return {\n          prevAnimationId: nextProps.animationId,\n          curSectors: nextProps.sectors,\n          prevSectors: prevState.curSectors,\n          isAnimationFinished: true\n        };\n      }\n      if (nextProps.sectors !== prevState.curSectors) {\n        return {\n          curSectors: nextProps.sectors,\n          isAnimationFinished: true\n        };\n      }\n      return null;\n    }\n  }, {\n    key: \"getTextAnchor\",\n    value: function getTextAnchor(x, cx) {\n      if (x > cx) {\n        return 'start';\n      }\n      if (x < cx) {\n        return 'end';\n      }\n      return 'middle';\n    }\n  }, {\n    key: \"renderLabelLineItem\",\n    value: function renderLabelLineItem(option, props, key) {\n      if ( /*#__PURE__*/React.isValidElement(option)) {\n        return /*#__PURE__*/React.cloneElement(option, props);\n      }\n      if (isFunction(option)) {\n        return option(props);\n      }\n      var className = clsx('recharts-pie-label-line', typeof option !== 'boolean' ? option.className : '');\n      return /*#__PURE__*/React.createElement(Curve, _extends({}, props, {\n        key: key,\n        type: \"linear\",\n        className: className\n      }));\n    }\n  }, {\n    key: \"renderLabelItem\",\n    value: function renderLabelItem(option, props, value) {\n      if ( /*#__PURE__*/React.isValidElement(option)) {\n        return /*#__PURE__*/React.cloneElement(option, props);\n      }\n      var label = value;\n      if (isFunction(option)) {\n        label = option(props);\n        if ( /*#__PURE__*/React.isValidElement(label)) {\n          return label;\n        }\n      }\n      var className = clsx('recharts-pie-label-text', typeof option !== 'boolean' && !isFunction(option) ? option.className : '');\n      return /*#__PURE__*/React.createElement(Text, _extends({}, props, {\n        alignmentBaseline: \"middle\",\n        className: className\n      }), label);\n    }\n  }]);\n}(PureComponent);\n_Pie = Pie;\n_defineProperty(Pie, \"displayName\", 'Pie');\n_defineProperty(Pie, \"defaultProps\", {\n  stroke: '#fff',\n  fill: '#808080',\n  legendType: 'rect',\n  cx: '50%',\n  cy: '50%',\n  startAngle: 0,\n  endAngle: 360,\n  innerRadius: 0,\n  outerRadius: '80%',\n  paddingAngle: 0,\n  labelLine: true,\n  hide: false,\n  minAngle: 0,\n  isAnimationActive: !Global.isSsr,\n  animationBegin: 400,\n  animationDuration: 1500,\n  animationEasing: 'ease',\n  nameKey: 'name',\n  blendStroke: false,\n  rootTabIndex: 0\n});\n_defineProperty(Pie, \"parseDeltaAngle\", function (startAngle, endAngle) {\n  var sign = mathSign(endAngle - startAngle);\n  var deltaAngle = Math.min(Math.abs(endAngle - startAngle), 360);\n  return sign * deltaAngle;\n});\n_defineProperty(Pie, \"getRealPieData\", function (itemProps) {\n  var data = itemProps.data,\n    children = itemProps.children;\n  var presentationProps = filterProps(itemProps, false);\n  var cells = findAllByType(children, Cell);\n  if (data && data.length) {\n    return data.map(function (entry, index) {\n      return _objectSpread(_objectSpread(_objectSpread({\n        payload: entry\n      }, presentationProps), entry), cells && cells[index] && cells[index].props);\n    });\n  }\n  if (cells && cells.length) {\n    return cells.map(function (cell) {\n      return _objectSpread(_objectSpread({}, presentationProps), cell.props);\n    });\n  }\n  return [];\n});\n_defineProperty(Pie, \"parseCoordinateOfPie\", function (itemProps, offset) {\n  var top = offset.top,\n    left = offset.left,\n    width = offset.width,\n    height = offset.height;\n  var maxPieRadius = getMaxRadius(width, height);\n  var cx = left + getPercentValue(itemProps.cx, width, width / 2);\n  var cy = top + getPercentValue(itemProps.cy, height, height / 2);\n  var innerRadius = getPercentValue(itemProps.innerRadius, maxPieRadius, 0);\n  var outerRadius = getPercentValue(itemProps.outerRadius, maxPieRadius, maxPieRadius * 0.8);\n  var maxRadius = itemProps.maxRadius || Math.sqrt(width * width + height * height) / 2;\n  return {\n    cx: cx,\n    cy: cy,\n    innerRadius: innerRadius,\n    outerRadius: outerRadius,\n    maxRadius: maxRadius\n  };\n});\n_defineProperty(Pie, \"getComposedData\", function (_ref4) {\n  var item = _ref4.item,\n    offset = _ref4.offset;\n  var itemProps = item.type.defaultProps !== undefined ? _objectSpread(_objectSpread({}, item.type.defaultProps), item.props) : item.props;\n  var pieData = _Pie.getRealPieData(itemProps);\n  if (!pieData || !pieData.length) {\n    return null;\n  }\n  var cornerRadius = itemProps.cornerRadius,\n    startAngle = itemProps.startAngle,\n    endAngle = itemProps.endAngle,\n    paddingAngle = itemProps.paddingAngle,\n    dataKey = itemProps.dataKey,\n    nameKey = itemProps.nameKey,\n    valueKey = itemProps.valueKey,\n    tooltipType = itemProps.tooltipType;\n  var minAngle = Math.abs(itemProps.minAngle);\n  var coordinate = _Pie.parseCoordinateOfPie(itemProps, offset);\n  var deltaAngle = _Pie.parseDeltaAngle(startAngle, endAngle);\n  var absDeltaAngle = Math.abs(deltaAngle);\n  var realDataKey = dataKey;\n  if (isNil(dataKey) && isNil(valueKey)) {\n    warn(false, \"Use \\\"dataKey\\\" to specify the value of pie,\\n      the props \\\"valueKey\\\" will be deprecated in 1.1.0\");\n    realDataKey = 'value';\n  } else if (isNil(dataKey)) {\n    warn(false, \"Use \\\"dataKey\\\" to specify the value of pie,\\n      the props \\\"valueKey\\\" will be deprecated in 1.1.0\");\n    realDataKey = valueKey;\n  }\n  var notZeroItemCount = pieData.filter(function (entry) {\n    return getValueByDataKey(entry, realDataKey, 0) !== 0;\n  }).length;\n  var totalPadingAngle = (absDeltaAngle >= 360 ? notZeroItemCount : notZeroItemCount - 1) * paddingAngle;\n  var realTotalAngle = absDeltaAngle - notZeroItemCount * minAngle - totalPadingAngle;\n  var sum = pieData.reduce(function (result, entry) {\n    var val = getValueByDataKey(entry, realDataKey, 0);\n    return result + (isNumber(val) ? val : 0);\n  }, 0);\n  var sectors;\n  if (sum > 0) {\n    var prev;\n    sectors = pieData.map(function (entry, i) {\n      var val = getValueByDataKey(entry, realDataKey, 0);\n      var name = getValueByDataKey(entry, nameKey, i);\n      var percent = (isNumber(val) ? val : 0) / sum;\n      var tempStartAngle;\n      if (i) {\n        tempStartAngle = prev.endAngle + mathSign(deltaAngle) * paddingAngle * (val !== 0 ? 1 : 0);\n      } else {\n        tempStartAngle = startAngle;\n      }\n      var tempEndAngle = tempStartAngle + mathSign(deltaAngle) * ((val !== 0 ? minAngle : 0) + percent * realTotalAngle);\n      var midAngle = (tempStartAngle + tempEndAngle) / 2;\n      var middleRadius = (coordinate.innerRadius + coordinate.outerRadius) / 2;\n      var tooltipPayload = [{\n        name: name,\n        value: val,\n        payload: entry,\n        dataKey: realDataKey,\n        type: tooltipType\n      }];\n      var tooltipPosition = polarToCartesian(coordinate.cx, coordinate.cy, middleRadius, midAngle);\n      prev = _objectSpread(_objectSpread(_objectSpread({\n        percent: percent,\n        cornerRadius: cornerRadius,\n        name: name,\n        tooltipPayload: tooltipPayload,\n        midAngle: midAngle,\n        middleRadius: middleRadius,\n        tooltipPosition: tooltipPosition\n      }, entry), coordinate), {}, {\n        value: getValueByDataKey(entry, realDataKey),\n        startAngle: tempStartAngle,\n        endAngle: tempEndAngle,\n        payload: entry,\n        paddingAngle: mathSign(deltaAngle) * paddingAngle\n      });\n      return prev;\n    });\n  }\n  return _objectSpread(_objectSpread({}, coordinate), {}, {\n    sectors: sectors,\n    data: pieData\n  });\n});", "/**\n * @fileOverview Pie Chart\n */\nimport { generateCategoricalChart } from './generateCategoricalChart';\nimport { PolarAngleAxis } from '../polar/PolarAngleAxis';\nimport { PolarRadiusAxis } from '../polar/PolarRadiusAxis';\nimport { formatAxisMap } from '../util/PolarUtils';\nimport { Pie } from '../polar/Pie';\nexport var PieChart = generateCategoricalChart({\n  chartName: 'PieChart',\n  GraphicalChild: Pie,\n  validateTooltipEventTypes: ['item'],\n  defaultTooltipEventType: 'item',\n  legendContent: 'children',\n  axisComponents: [{\n    axisType: 'angleAxis',\n    AxisComp: PolarAngleAxis\n  }, {\n    axisType: 'radiusAxis',\n    AxisComp: PolarRadiusAxis\n  }],\n  formatAxisMap: formatAxisMap,\n  defaultProps: {\n    layout: 'centric',\n    startAngle: 0,\n    endAngle: 360,\n    cx: '50%',\n    cy: '50%',\n    innerRadius: 0,\n    outerRadius: '80%'\n  }\n});", "import { useEffect, useState } from \"react\"\r\nimport {\r\n    Bar,\r\n    BarChart,\r\n    Cell,\r\n    Pie,\r\n    <PERSON><PERSON>hart,\r\n    ResponsiveContainer,\r\n    Tooltip,\r\n    XAxis,\r\n    YAxis,\r\n    CartesianGrid\r\n} from \"recharts\"\r\nimport { Card, CardContent, CardHeader, CardTitle } from \"@components/ui/card\"\r\nimport { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from \"@components/ui/tabs\"\r\nimport { Link, useFetcher, useLoaderData } from \"@remix-run/react\"\r\nimport { ActionFunction, json, LoaderFunction, redirect, TypedResponse } from \"@remix-run/node\"\r\nimport { getAllDashboardDataFor, getDashBoardBuyerSummary, getOrderItemSummary } from \"@services/businessConsoleService\"\r\nimport { BuyerSummary, OrderItem, SellerConsoleDataResponse } from \"~/types/api/businessConsoleService/SellerConsoleDataResponse\"\r\nimport { getSession } from \"@utils/session.server\"\r\nimport { User } from \"~/types\"\r\nimport { DashboardGroupBy } from \"~/types/home\"\r\nimport { formatWeight, formatCurrency } from \"@utils/format\"\r\nimport { ScrollArea } from \"@components/ui/scroll-area\"\r\nimport { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from \"@components/ui/table\"\r\nimport { CodeSquare, Target, Users } from \"lucide-react\"\r\nimport { withAuth, withResponse } from \"@utils/auth-utils\";\r\nimport SpinnerLoader from \"~/components/loader/SpinnerLoader\"\r\nimport CustomerDashboard, { DataPoint } from \"~/components/ui/bar-dashboard\"\r\n\r\n// Define colors for the chart\r\nconst COLORS = ['#afded7', '#f8c8cd', '#cfc2ed', '#AFEFED', '#90c8e7', '#FDDFB4', '#E789B7', '#FFD384', '#00a390', '#7551ce']\r\n\r\ninterface LoaderData {\r\n    data: SellerConsoleDataResponse[];\r\n    dashboardGroupBy: DashboardGroupBy;\r\n    tonnageReportData: OrderItem[];\r\n    buyerOrderSummary: BuyerSummary[]\r\n\r\n}\r\n\r\nexport const loader = withAuth(async ({ user, request }) => {\r\n    const dashboardGroupBy = DashboardGroupBy.Daily;\r\n    const url = new URL(request.url);\r\n    const returnTo = url.searchParams.get('returnTo');\r\n\r\n    if (returnTo) {\r\n        return redirect(returnTo);\r\n    }\r\n\r\n    try {\r\n\r\n        const today = new Date();\r\n        const yesterday = new Date();\r\n        yesterday.setDate(today.getDate() - 1);\r\n        const [dashBoardData, orderItemResponse, orderBuyerSummary] = await Promise.all([\r\n            getAllDashboardDataFor(user.userId, today, dashboardGroupBy, request),\r\n            getOrderItemSummary(yesterday, today, request),\r\n            getDashBoardBuyerSummary(yesterday, today, request)\r\n        ])\r\n\r\n        const responseHeaders = new Headers();\r\n        [dashBoardData, orderItemResponse, orderBuyerSummary].forEach(response => {\r\n            if (response.headers?.has('Set-Cookie')) {\r\n                responseHeaders.set('Set-Cookie', response.headers.get('Set-Cookie')!);\r\n            }\r\n        });\r\n\r\n\r\n\r\n\r\n        // const data = await\r\n        // getAllDashboardDataFor(user.userId, today, dashboardGroupBy, request);\r\n        // const yesterday = new Date();\r\n        // yesterday.setDate(today.getDate() - 1);\r\n        // const OrderItemResponse = await getOrderItemSummary(yesterday, today, request)\r\n        // const OrderBuyerSummary = await getDashBoardBuyerSummary(yesterday, today, request)\r\n        // const responseHeaders = new Headers();\r\n\r\n\r\n        // if (data && data.headers?.has('Set-Cookie')) {\r\n        //     responseHeaders.set('Set-Cookie', data.headers.get('Set-Cookie')!);\r\n        // }\r\n\r\n\r\n        return withResponse({\r\n            data: dashBoardData.data,\r\n            dashboardGroupBy: dashboardGroupBy,\r\n            tonnageReportData: orderItemResponse.data,\r\n            buyerOrderSummary: orderBuyerSummary.data\r\n        }, responseHeaders);\r\n    }\r\n    catch (error) {\r\n        console.error(\"Customer details error:\", error);\r\n        if (error instanceof Response && error.status === 404) {\r\n            throw json({ error: \"Customer not found\" }, { status: 404 });\r\n        }\r\n        throw new Response(\"Failed to fetch customer data\", { status: 500 });\r\n    }\r\n})\r\nexport const action = withAuth(async ({ user, request }) => {\r\n    const formData = await request.formData();\r\n    const dashboardGroupBy = formData.get(\"dashboardGroupBy\") as DashboardGroupBy;\r\n    const summaryDate = new Date(formData.get(\"summaryDate\") as string);\r\n    const intent = formData.get(\"intent\") as string;\r\n\r\n    if (!Object.values(DashboardGroupBy).includes(dashboardGroupBy)) {\r\n        throw json({ error: \"Invalid groupBy parameter\" }, { status: 400 });\r\n    }\r\n    const startDate = new Date(formData.get(\"startDate\") as string);\r\n    const endDate = new Date(formData.get(\"endDate\") as string);\r\n\r\n    if (!startDate || !endDate) {\r\n        throw json({ error: \"Invalid date range\" }, { status: 400 });\r\n    }\r\n    let responseData: any = {};\r\n    let responseHeaders: Headers = new Headers();\r\n\r\n\r\n    if (intent === \"graphDashBoard\") {\r\n        const graphDashboardResponse = await getAllDashboardDataFor(user.userId, summaryDate, dashboardGroupBy, request);\r\n        responseData = graphDashboardResponse.data;\r\n        responseHeaders = graphDashboardResponse.headers ?? new Headers();\r\n    }\r\n\r\n    let tonnageResponse: any = {};\r\n    let buyerSummaryres: any = {};\r\n\r\n    if (intent === \"barSelected\") {\r\n        const graphDashboardResponse = await getAllDashboardDataFor(user.userId, summaryDate, dashboardGroupBy, request);\r\n        responseData = graphDashboardResponse.data;\r\n        const OrderItemResponse = await getOrderItemSummary(startDate, endDate, request)\r\n\r\n        const OrderBuyerSummary = await getDashBoardBuyerSummary(startDate, endDate, request)\r\n        tonnageResponse = OrderItemResponse.data;\r\n        buyerSummaryres = OrderBuyerSummary.data;\r\n\r\n    }\r\n    return withResponse({\r\n        data: responseData,\r\n        dashboardGroupBy: dashboardGroupBy,\r\n        tonnageReportData: tonnageResponse,\r\n        buyerOrderSummary: buyerSummaryres\r\n\r\n\r\n    }, responseHeaders ?? new Headers());\r\n});\r\n\r\nexport default function HomeDashboard() {\r\n\r\n\r\n    const { data, dashboardGroupBy, tonnageReportData, buyerOrderSummary } = useLoaderData<LoaderData>()\r\n\r\n    const fetcher = useFetcher<LoaderData>();\r\n    const [barGraphData, setBarGraphData] = useState<SellerConsoleDataResponse[]>(data)\r\n    const [dashboardGroup, setIsDashBoardGroup] = useState<DashboardGroupBy>(dashboardGroupBy)\r\n    const [tonnageReports, setTonnageReports] = useState<OrderItem[]>(tonnageReportData);\r\n    const [buyerSummaryReports, setBuyerSummaryReports] = useState<BuyerSummary[]>(buyerOrderSummary)\r\n    const [selectedGraphData, setSelectedGraphData] = useState<DataPoint | undefined>()\r\n\r\n\r\n    useEffect(() => {\r\n        if (fetcher.data) {\r\n            setBarGraphData(fetcher.data.data)\r\n            setBuyerSummaryReports(fetcher.data.buyerOrderSummary)\r\n            setTonnageReports(fetcher.data.tonnageReportData)\r\n            console.log(fetcher.data.tonnageReportData, \"ddddddddddd\")\r\n            setIsDashBoardGroup(fetcher.data.dashboardGroupBy)\r\n        }\r\n\r\n    }, [fetcher.data])\r\n\r\n\r\n\r\n    const consolidatePieData = (data: OrderItem[] = []) => {\r\n        if (!data || data.length === 0) return [];\r\n        const sortedData = [...data].sort((a, b) => b.totalWeight - a.totalWeight);\r\n        const topFive = sortedData.slice(0, 4);\r\n        const others = sortedData.slice(4).reduce((acc, curr) => {\r\n            acc.totalWeight += curr.totalWeight;\r\n            return acc;\r\n        }, { itemName: 'Others', totalWeight: 0 });\r\n        const unsortedcombined = [...topFive, others]\r\n        const combined = [...unsortedcombined].sort((a, b) => b.totalWeight - a.totalWeight);\r\n        // Alternate between topFive and others\r\n        const result: OrderItem[] = [];\r\n        let left = 0;\r\n        let right = combined.length - 1;\r\n\r\n        while (left <= right) {\r\n            if (left === right) {\r\n                result.push(combined[left]); // Add the remaining element if odd-length\r\n            } else {\r\n                result.push(combined[left], combined[right]);\r\n            }\r\n            left++;\r\n            right--;\r\n        }\r\n\r\n        return result.map((item, index) => ({\r\n            ...item,\r\n            color: COLORS[index % COLORS.length],\r\n        }));\r\n    };\r\n\r\n    const handleGraph = (value: string, summaryDate: Date) => {\r\n        setSelectedGraphData(undefined)\r\n        fetcher.submit(\r\n            {\r\n                dashboardGroupBy: value,\r\n                summaryDate: summaryDate.toISOString(),\r\n                intent: \"graphDashBoard\"\r\n            },\r\n            { method: \"post\" }\r\n        );\r\n    }\r\n\r\n\r\n\r\n    const handleBarSelected = (startDate: Date, endDate: Date, tab: string, summaryDate: Date, selectedSellerData: DataPoint) => {\r\n\r\n        console.log()\r\n\r\n        setSelectedGraphData(selectedSellerData ? selectedSellerData : undefined)\r\n        fetcher.submit(\r\n            {\r\n                startDate: startDate.toISOString(),\r\n                endDate: endDate.toISOString(),\r\n                intent: \"barSelected\",\r\n                dashboardGroupBy: tab,\r\n                summaryDate: summaryDate.toISOString()\r\n            },\r\n            { method: \"post\" }\r\n        )\r\n\r\n    }\r\n\r\n    const totalWeight = Array.isArray(tonnageReports) && tonnageReports.length > 0 && tonnageReports.map((x) => x.totalWeight).reduce((acc, cur) => acc + cur, 0)\r\n\r\n    const TotalPendingAmount = Array.isArray(buyerSummaryReports) && buyerSummaryReports.length > 0 && buyerSummaryReports.map((x) => x.PendingAmount).reduce((acc, cur) => acc + cur, 0)\r\n    const totalPeningCount = Array.isArray(buyerSummaryReports) && buyerSummaryReports.length > 0 && buyerSummaryReports.map((x) => x.PendingAmountCount).reduce((acc, cur) => acc + cur, 0)\r\n    {\r\n        fetcher.state !== \"idle\"\r\n            && <SpinnerLoader size={8} loading={true} />\r\n    }\r\n    const wrapText = (text: string, maxChars: number) => {\r\n        const words = text.split(\" \");\r\n        const lines: string[] = [];\r\n        let currentLine = \"\";\r\n\r\n        words.forEach((word) => {\r\n            if ((currentLine + word).length <= maxChars) {\r\n                currentLine += (currentLine ? \" \" : \"\") + word;\r\n            } else {\r\n                lines.push(currentLine);\r\n                currentLine = word;\r\n            }\r\n        });\r\n\r\n        if (currentLine) {\r\n            lines.push(currentLine);\r\n        }\r\n\r\n        return lines;\r\n    };\r\n\r\n    const TonnagePieChart = () => {\r\n        const [screenWidth, setScreenWidth] = useState(1024); // Default for SSR-safe value\r\n\r\n        useEffect(() => {\r\n            const updateWidth = () => setScreenWidth(window.innerWidth);\r\n            updateWidth(); // Set initial width\r\n            window.addEventListener(\"resize\", updateWidth);\r\n            return () => window.removeEventListener(\"resize\", updateWidth);\r\n        }, []);\r\n\r\n        return (\r\n            <Card>\r\n                <CardHeader>\r\n                    <CardTitle>Tonnage</CardTitle>\r\n                </CardHeader>\r\n                <CardContent>\r\n                    <div className=\"h-[250px] w-100% relative\">\r\n                        <ResponsiveContainer width=\"100%\" height=\"100%\">\r\n                            <PieChart>\r\n                                <Pie\r\n                                    data={consolidatePieData(Array.isArray(tonnageReports) ? tonnageReports : [])}\r\n                                    cx=\"50%\"\r\n                                    cy=\"50%\"\r\n                                    innerRadius={60}\r\n                                    outerRadius={80}\r\n                                    fill=\"#8884d8\"\r\n                                    paddingAngle={2}\r\n                                    dataKey=\"totalWeight\"\r\n                                    labelLine={false}\r\n                                    label={(props) => {\r\n                                        const {\r\n                                            cx, cy, midAngle, outerRadius, fill, payload, value,\r\n                                        } = props;\r\n                                        const RADIAN = Math.PI / 180;\r\n                                        const sin = Math.sin(-RADIAN * midAngle);\r\n                                        const cos = Math.cos(-RADIAN * midAngle);\r\n                                        const sx = cx + (outerRadius + 10) * cos;\r\n                                        const sy = cy + (outerRadius + 10) * sin;\r\n                                        const mx = cx + (outerRadius + 20) * cos;\r\n                                        const my = cy + (outerRadius + 30) * sin;\r\n                                        const ex = mx + (cos >= 0 ? 1 : -1) * 20;\r\n                                        const ey = my;\r\n                                        const textAnchor = cos >= 0 ? 'start' : 'end';\r\n                                        const totalValue = selectedGraphData?.Quantity ?? 0;\r\n                                        const percentage = parseFloat(((value / totalValue) * 100).toFixed(2));\r\n                                        const truncate = (str: string, maxLength: number) => {\r\n                                            return str.length > maxLength ? `${str.slice(0, maxLength)}...` : str;\r\n                                        };\r\n\r\n                                        if (percentage < 4) return null; // Skip labels for very small segments\r\n                                        return (\r\n                                            <g>\r\n                                                <path d={`M${sx},${sy}L${mx},${my}L${ex},${ey}`} stroke={fill} fill=\"none\" />\r\n                                                <circle cx={ex} cy={ey} r={2} fill={fill} stroke=\"none\" />\r\n                                                <text x={ex + (cos >= 0 ? 1 : -1) * 12} y={ey} textAnchor={textAnchor} fill=\"#333\">\r\n                                                    {`${truncate(payload.itemName, 20)}`}\r\n                                                </text>\r\n                                                <text x={ex + (cos >= 0 ? 1 : -1) * 12} y={ey} dy={16} textAnchor={textAnchor} fill=\"#999\">\r\n                                                    {`${formatWeight(value)}  | ${percentage}%`}\r\n                                                </text>\r\n                                            </g>\r\n                                        );\r\n                                    }}\r\n                                >\r\n                                    {consolidatePieData(Array.isArray(tonnageReports) ? tonnageReports : []).map((entry, index) => (\r\n                                        <Cell key={`cell-${index}`} fill={entry.color} />\r\n                                    ))}\r\n                                </Pie>\r\n                            </PieChart>\r\n                        </ResponsiveContainer>\r\n                        <div className=\"absolute inset-0 flex items-center justify-center flex-col\">\r\n                            <p className=\"text-sm font-semibold\">\r\n                                {'Total'}\r\n                            </p>\r\n                            <p className=\"text-xl font-bold\">\r\n                                {formatWeight(selectedGraphData?.Quantity > 0 ? selectedGraphData?.Quantity : totalWeight)}\r\n                            </p>\r\n                        </div>\r\n                    </div>\r\n                </CardContent>\r\n            </Card>\r\n        );\r\n    };\r\n\r\n\r\n    return (\r\n        <div className=\"flex flex-col h-screen bg-background\">\r\n\r\n            <div className=\"flex-1 p-4 space-y-4 overflow-auto border-3  border-red-200\">\r\n                <CustomerDashboard data={barGraphData || []} handleGraph={handleGraph} dashboardGroupBy={dashboardGroup} handleBarSelected={handleBarSelected} />\r\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4 \" >\r\n                    <TonnagePieChart />\r\n                    <Card>\r\n                        <CardHeader>\r\n                            <CardTitle>Orders</CardTitle>\r\n                        </CardHeader>\r\n                        <CardContent>\r\n                            <ScrollArea className=\"h-[250px]\">\r\n                                <Table>\r\n                                    <TableHeader>\r\n                                        <TableRow>\r\n                                            <TableHead>Business Name</TableHead>\r\n                                            <TableHead>Items</TableHead>\r\n                                            <TableHead>Weight</TableHead>\r\n                                        </TableRow>\r\n                                    </TableHeader>\r\n                                    <TableBody>\r\n                                        {Array.isArray(buyerSummaryReports) && buyerSummaryReports.length > 0 ? (\r\n                                            buyerSummaryReports.map((x) => (\r\n                                                <TableRow key={x.buyerId}>\r\n                                                    <TableCell className=\"font-medium\">{x.buyerName}</TableCell>\r\n                                                    <TableCell>{x.itemCount}</TableCell>\r\n                                                    <TableCell>{formatWeight(x.totalWeight)}</TableCell>\r\n                                                </TableRow>\r\n                                            ))\r\n                                        ) : (\r\n                                            <TableRow>\r\n                                                <TableCell colSpan={3} className=\"text-center\">\r\n                                                    No orders available\r\n                                                </TableCell>\r\n                                            </TableRow>\r\n                                        )}\r\n                                    </TableBody>\r\n                                </Table>\r\n                            </ScrollArea>\r\n                            <div className=\"mt-2 text-sm\">\r\n                                <Link to=\"/home/<USER>\" className=\"text-primary hover:underline\">\r\n                                    See all orders\r\n                                </Link>\r\n                            </div>\r\n                        </CardContent>\r\n                    </Card>\r\n                </div>\r\n\r\n                <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\r\n                    <Card>\r\n                        <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\r\n                            <CardTitle className=\"text-sm font-medium\">Returns</CardTitle>\r\n                        </CardHeader>\r\n                        <CardContent>\r\n                            <div className=\"text-xl font-bold\">{formatWeight(selectedGraphData?.returnweight || 0)} </div>\r\n                            <p className=\"text-xs text-destructive\"> Revenue Loss : {formatCurrency(selectedGraphData?.returnAmount || 0)}</p>\r\n                        </CardContent>\r\n                    </Card>\r\n\r\n                    <Card>\r\n                        <CardHeader>\r\n                            <CardTitle className=\"text-sm font-medium\">Pending Payments</CardTitle>\r\n                        </CardHeader>\r\n                        <CardContent>\r\n                            <div className=\"text-xl font-bold\">\r\n                                {formatCurrency((TotalPendingAmount || 0))}\r\n                            </div>\r\n                            <p className=\"text-xs text-muted-foreground\">\r\n                                from {totalPeningCount || 0} Customers\r\n                            </p>\r\n                        </CardContent>\r\n\r\n\r\n                    </Card>\r\n                    <Card>\r\n                        <CardHeader>\r\n                            <CardTitle className=\"text-sm font-medium\">Metrics</CardTitle>\r\n                        </CardHeader>\r\n                        <CardContent>\r\n                            <div className=\"space-y-2\">\r\n                                {[\r\n                                    { title: \"Items\", icon: Target, value: tonnageReports?.length },\r\n                                    // { title: \"Employees\", icon: Users, value: tonnageReportsL[0].employeeCount },\r\n                                ].map((item, index) => (\r\n                                    <div key={index} className=\"flex items-center\">\r\n                                        <item.icon className=\"h-4 w-4 mr-2 text-primary\" />\r\n                                        <div>\r\n                                            <p className=\"text-xs font-medium\">{item.title}</p>\r\n                                            <p className=\"text-lg font-bold\">{item.value}</p>\r\n                                        </div>\r\n                                    </div>\r\n                                ))}\r\n                            </div>\r\n                        </CardContent>\r\n                    </Card>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    )\r\n}\r\n"], "names": ["_excluded", "_extends", "_objectWithoutProperties", "_objectWithoutPropertiesLoose", "isValidatePoint", "getParsedPoints", "getSinglePolygonPath", "getRanglePath", "Polygon", "baseExtremum", "require$$0", "require$$1", "baseIteratee", "require$$2", "_typeof", "o", "ownKeys", "r", "_objectSpread", "_defineProperty", "_classCallCheck", "_defineProperties", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "_createClass", "_callSuper", "_getPrototypeOf", "_possibleConstructorReturn", "_isNativeReflectConstruct", "_assertThisInitialized", "t", "_inherits", "_setPrototypeOf", "p", "_toPrimitive", "PolarRadiusAxis", "maxBy", "minBy", "PureComponent", "PolarAngleAxis", "Pie", "COLORS", "HomeDashboard", "data", "dashboardGroupBy", "tonnageReportData", "buyerOrderSummary", "useLoaderData", "fetcher", "useFetcher", "barGraphData", "setBarGraphData", "useState", "dashboardGroup", "setIsDashBoardGroup", "tonnageReports", "setTonnageReports", "buyerSummaryReports", "setBuyerSummaryReports", "selectedGraphData", "setSelectedGraphData", "useEffect", "console", "log", "consolidatePieData", "length", "sortedData", "sort", "a", "b", "totalWeight", "topFive", "slice", "others", "reduce", "acc", "curr", "itemName", "unsortedcombined", "combined", "result", "left", "right", "push", "map", "item", "index", "color", "handleGraph", "value", "summaryDate", "submit", "toISOString", "intent", "method", "handleBarSelected", "startDate", "endDate", "tab", "selectedSellerData", "Array", "isArray", "x", "cur", "TotalPendingAmount", "PendingAmount", "totalPeningCount", "PendingAmountCount", "state", "jsx", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "size", "loading", "Tonnage<PERSON><PERSON><PERSON><PERSON>", "screenWidth", "setScreenWidth", "updateWidth", "window", "innerWidth", "addEventListener", "removeEventListener", "Card", "children", "<PERSON><PERSON><PERSON><PERSON>", "CardTitle", "<PERSON><PERSON><PERSON><PERSON>", "jsxs", "className", "ResponsiveContainer", "width", "height", "<PERSON><PERSON><PERSON>", "cx", "cy", "innerRadius", "outerRadius", "fill", "paddingAngle", "dataKey", "labelLine", "label", "props", "midAngle", "payload", "RADIAN", "Math", "PI", "sin", "cos", "sx", "sy", "mx", "my", "ex", "ey", "textAnchor", "totalValue", "Quantity", "percentage", "parseFloat", "toFixed", "truncate", "str", "max<PERSON><PERSON><PERSON>", "d", "stroke", "y", "dy", "formatWeight", "entry", "Cell", "CustomerDashboard", "ScrollArea", "Table", "TableHeader", "TableRow", "TableHead", "TableBody", "TableCell", "buyerName", "itemCount", "buyerId", "colSpan", "Link", "to", "returnweight", "formatCurrency", "returnAmount", "title", "icon", "Target"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA,MAAM,SAAS,iBAAiB,UAAU;AAAA,EACxC,CAAC,UAAU,EAAE,IAAI,MAAM,IAAI,MAAM,GAAG,MAAM,KAAK,UAAU;AAAA,EACzD,CAAC,UAAU,EAAE,IAAI,MAAM,IAAI,MAAM,GAAG,KAAK,KAAK,UAAU;AAAA,EACxD,CAAC,UAAU,EAAE,IAAI,MAAM,IAAI,MAAM,GAAG,KAAK,KAAK,SAAU,CAAA;AAC1D,CAAC;ACbD,IAAIA,cAAY,CAAC,UAAU,aAAa,kBAAkB,cAAc;AACxE,SAASC,aAAW;AAAEA,eAAW,OAAO,SAAS,OAAO,OAAO,KAAM,IAAG,SAAU,QAAQ;AAAE,aAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AAAE,UAAI,SAAS,UAAU,CAAC;AAAG,eAAS,OAAO,QAAQ;AAAE,YAAI,OAAO,UAAU,eAAe,KAAK,QAAQ,GAAG,GAAG;AAAE,iBAAO,GAAG,IAAI,OAAO,GAAG;AAAA,QAAI;AAAA,MAAA;AAAA;AAAK,WAAO;AAAA,EAAO;AAAI,SAAOA,WAAS,MAAM,MAAM,SAAS;AAAE;AACjV,SAASC,2BAAyB,QAAQ,UAAU;AAAE,MAAI,UAAU,KAAM,QAAO;AAAI,MAAI,SAASC,gCAA8B,QAAQ,QAAQ;AAAG,MAAI,KAAK;AAAG,MAAI,OAAO,uBAAuB;AAAE,QAAI,mBAAmB,OAAO,sBAAsB,MAAM;AAAG,SAAK,IAAI,GAAG,IAAI,iBAAiB,QAAQ,KAAK;AAAE,YAAM,iBAAiB,CAAC;AAAG,UAAI,SAAS,QAAQ,GAAG,KAAK,EAAG;AAAU,UAAI,CAAC,OAAO,UAAU,qBAAqB,KAAK,QAAQ,GAAG,EAAG;AAAU,aAAO,GAAG,IAAI,OAAO,GAAG;AAAA,IAAE;AAAA,EAAI;AAAC,SAAO;AAAO;AAC1e,SAASA,gCAA8B,QAAQ,UAAU;AAAE,MAAI,UAAU,KAAM,QAAO;AAAI,MAAI,SAAS,CAAA;AAAI,WAAS,OAAO,QAAQ;AAAE,QAAI,OAAO,UAAU,eAAe,KAAK,QAAQ,GAAG,GAAG;AAAE,UAAI,SAAS,QAAQ,GAAG,KAAK,EAAG;AAAU,aAAO,GAAG,IAAI,OAAO,GAAG;AAAA,IAAI;AAAA,EAAA;AAAG,SAAO;AAAO;AACrR,SAAS,mBAAmB,KAAK;AAAE,SAAO,mBAAmB,GAAG,KAAK,iBAAiB,GAAG,KAAK,4BAA4B,GAAG,KAAK,mBAAkB;AAAG;AACvJ,SAAS,qBAAqB;AAAE,QAAM,IAAI,UAAU,sIAAsI;AAAE;AAC5L,SAAS,4BAA4B,GAAG,QAAQ;AAAE,MAAI,CAAC,EAAG;AAAQ,MAAI,OAAO,MAAM,SAAU,QAAO,kBAAkB,GAAG,MAAM;AAAG,MAAI,IAAI,OAAO,UAAU,SAAS,KAAK,CAAC,EAAE,MAAM,GAAG,EAAE;AAAG,MAAI,MAAM,YAAY,EAAE,YAAa,KAAI,EAAE,YAAY;AAAM,MAAI,MAAM,SAAS,MAAM,MAAO,QAAO,MAAM,KAAK,CAAC;AAAG,MAAI,MAAM,eAAe,2CAA2C,KAAK,CAAC,EAAG,QAAO,kBAAkB,GAAG,MAAM;AAAE;AAC9Z,SAAS,iBAAiB,MAAM;AAAE,MAAI,OAAO,WAAW,eAAe,KAAK,OAAO,QAAQ,KAAK,QAAQ,KAAK,YAAY,KAAK,KAAM,QAAO,MAAM,KAAK,IAAI;AAAE;AAC5J,SAAS,mBAAmB,KAAK;AAAE,MAAI,MAAM,QAAQ,GAAG,EAAG,QAAO,kBAAkB,GAAG;AAAE;AACzF,SAAS,kBAAkB,KAAK,KAAK;AAAE,MAAI,OAAO,QAAQ,MAAM,IAAI,OAAQ,OAAM,IAAI;AAAQ,WAAS,IAAI,GAAG,OAAO,IAAI,MAAM,GAAG,GAAG,IAAI,KAAK,IAAK,MAAK,CAAC,IAAI,IAAI,CAAC;AAAG,SAAO;AAAK;AAOjL,IAAI,kBAAkB,SAASC,iBAAgB,OAAO;AACpD,SAAO,SAAS,MAAM,MAAM,CAAC,MAAM,KAAK,MAAM,MAAM,CAAC,MAAM;AAC7D;AACA,IAAI,kBAAkB,SAASC,mBAAkB;AAC/C,MAAI,SAAS,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAE;AACnF,MAAI,gBAAgB,CAAC,EAAE;AACvB,SAAO,QAAQ,SAAU,OAAO;AAC9B,QAAI,gBAAgB,KAAK,GAAG;AAC1B,oBAAc,cAAc,SAAS,CAAC,EAAE,KAAK,KAAK;AAAA,IACxD,WAAe,cAAc,cAAc,SAAS,CAAC,EAAE,SAAS,GAAG;AAE7D,oBAAc,KAAK,EAAE;AAAA,IAC3B;AAAA,EACA,CAAG;AACD,MAAI,gBAAgB,OAAO,CAAC,CAAC,GAAG;AAC9B,kBAAc,cAAc,SAAS,CAAC,EAAE,KAAK,OAAO,CAAC,CAAC;AAAA,EAC1D;AACE,MAAI,cAAc,cAAc,SAAS,CAAC,EAAE,UAAU,GAAG;AACvD,oBAAgB,cAAc,MAAM,GAAG,EAAE;AAAA,EAC7C;AACE,SAAO;AACT;AACA,IAAI,uBAAuB,SAASC,sBAAqB,QAAQ,cAAc;AAC7E,MAAI,gBAAgB,gBAAgB,MAAM;AAC1C,MAAI,cAAc;AAChB,oBAAgB,CAAC,cAAc,OAAO,SAAU,KAAK,WAAW;AAC9D,aAAO,CAAA,EAAG,OAAO,mBAAmB,GAAG,GAAG,mBAAmB,SAAS,CAAC;AAAA,IACxE,GAAE,CAAE,CAAA,CAAC;AAAA,EACV;AACE,MAAI,cAAc,cAAc,IAAI,SAAU,WAAW;AACvD,WAAO,UAAU,OAAO,SAAU,MAAM,OAAO,OAAO;AACpD,aAAO,GAAG,OAAO,IAAI,EAAE,OAAO,UAAU,IAAI,MAAM,GAAG,EAAE,OAAO,MAAM,GAAG,GAAG,EAAE,OAAO,MAAM,CAAC;AAAA,IAC3F,GAAE,EAAE;AAAA,EACT,CAAG,EAAE,KAAK,EAAE;AACV,SAAO,cAAc,WAAW,IAAI,GAAG,OAAO,aAAa,GAAG,IAAI;AACpE;AACA,IAAI,gBAAgB,SAASC,eAAc,QAAQ,gBAAgB,cAAc;AAC/E,MAAI,YAAY,qBAAqB,QAAQ,YAAY;AACzD,SAAO,GAAG,OAAO,UAAU,MAAM,EAAE,MAAM,MAAM,UAAU,MAAM,GAAG,EAAE,IAAI,WAAW,GAAG,EAAE,OAAO,qBAAqB,eAAe,QAAS,GAAE,YAAY,EAAE,MAAM,CAAC,CAAC;AACtK;AACO,IAAI,UAAU,SAASC,SAAQ,OAAO;AAC3C,MAAI,SAAS,MAAM,QACjB,YAAY,MAAM,WAClB,iBAAiB,MAAM,gBACvB,eAAe,MAAM,cACrB,SAASN,2BAAyB,OAAOF,WAAS;AACpD,MAAI,CAAC,UAAU,CAAC,OAAO,QAAQ;AAC7B,WAAO;AAAA,EACX;AACE,MAAI,aAAa,KAAK,oBAAoB,SAAS;AACnD,MAAI,kBAAkB,eAAe,QAAQ;AAC3C,QAAI,YAAY,OAAO,UAAU,OAAO,WAAW;AACnD,QAAI,YAAY,cAAc,QAAQ,gBAAgB,YAAY;AAClE,WAAoB,sBAAM,cAAc,KAAK;AAAA,MAC3C,WAAW;AAAA,IACjB,GAAoB,sBAAM,cAAc,QAAQC,WAAS,CAAA,GAAI,YAAY,QAAQ,IAAI,GAAG;AAAA,MAClF,MAAM,UAAU,MAAM,EAAE,MAAM,MAAM,OAAO,OAAO;AAAA,MAClD,QAAQ;AAAA,MACR,GAAG;AAAA,IACJ,CAAA,CAAC,GAAG,YAAyB,sBAAM,cAAc,QAAQA,WAAS,IAAI,YAAY,QAAQ,IAAI,GAAG;AAAA,MAChG,MAAM;AAAA,MACN,GAAG,qBAAqB,QAAQ,YAAY;AAAA,IAC7C,CAAA,CAAC,IAAI,MAAM,YAAyB,sBAAM,cAAc,QAAQA,WAAS,CAAA,GAAI,YAAY,QAAQ,IAAI,GAAG;AAAA,MACvG,MAAM;AAAA,MACN,GAAG,qBAAqB,gBAAgB,YAAY;AAAA,IAC1D,CAAK,CAAC,IAAI,IAAI;AAAA,EACd;AACE,MAAI,aAAa,qBAAqB,QAAQ,YAAY;AAC1D,SAAoB,sBAAM,cAAc,QAAQA,WAAS,CAAA,GAAI,YAAY,QAAQ,IAAI,GAAG;AAAA,IACtF,MAAM,WAAW,MAAM,EAAE,MAAM,MAAM,OAAO,OAAO;AAAA,IACnD,WAAW;AAAA,IACX,GAAG;AAAA,EACP,CAAG,CAAC;AACJ;ACzFA,IAAIQ,iBAAeC,eACf,SAASC,SACTC,iBAAeC;AAyBnB,SAAS,MAAM,OAAO,UAAU;AAC9B,SAAQ,SAAS,MAAM,SACnBJ,eAAa,OAAOG,eAAa,QAAW,GAAG,MAAM,IACrD;AACN;AAEA,IAAA,UAAiB;;ACjCjB,IAAI,eAAeF,eACf,eAAeC,eACf,SAASE;AAyBb,SAAS,MAAM,OAAO,UAAU;AAC9B,SAAQ,SAAS,MAAM,SACnB,aAAa,OAAO,aAAa,QAAW,GAAG,MAAM,IACrD;AACN;AAEA,IAAA,UAAiB;;ACjCjB,IAAI,YAAY,CAAC,MAAM,MAAM,SAAS,SAAS,UAAU,GACvD,aAAa,CAAC,SAAS,QAAQ,SAAS,iBAAiB,QAAQ;AACnE,SAASC,UAAQ,GAAG;AAAE;AAA2B,SAAOA,YAAU,cAAc,OAAO,UAAU,YAAY,OAAO,OAAO,WAAW,SAAUC,IAAG;AAAE,WAAO,OAAOA;AAAA,MAAO,SAAUA,IAAG;AAAE,WAAOA,MAAK,cAAc,OAAO,UAAUA,GAAE,gBAAgB,UAAUA,OAAM,OAAO,YAAY,WAAW,OAAOA;AAAA,EAAE,GAAID,UAAQ,CAAC;AAAE;AAC5T,SAASb,aAAW;AAAEA,eAAW,OAAO,SAAS,OAAO,OAAO,KAAM,IAAG,SAAU,QAAQ;AAAE,aAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AAAE,UAAI,SAAS,UAAU,CAAC;AAAG,eAAS,OAAO,QAAQ;AAAE,YAAI,OAAO,UAAU,eAAe,KAAK,QAAQ,GAAG,GAAG;AAAE,iBAAO,GAAG,IAAI,OAAO,GAAG;AAAA,QAAI;AAAA,MAAA;AAAA;AAAK,WAAO;AAAA,EAAO;AAAI,SAAOA,WAAS,MAAM,MAAM,SAAS;AAAE;AACjV,SAASe,UAAQ,GAAG,GAAG;AAAE,MAAI,IAAI,OAAO,KAAK,CAAC;AAAG,MAAI,OAAO,uBAAuB;AAAE,QAAI,IAAI,OAAO,sBAAsB,CAAC;AAAG,UAAM,IAAI,EAAE,OAAO,SAAUC,IAAG;AAAE,aAAO,OAAO,yBAAyB,GAAGA,EAAC,EAAE;AAAA,IAAa,CAAA,IAAI,EAAE,KAAK,MAAM,GAAG,CAAC;AAAA;AAAK,SAAO;AAAE;AAC7P,SAASC,gBAAc,GAAG;AAAE,WAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AAAE,QAAI,IAAI,QAAQ,UAAU,CAAC,IAAI,UAAU,CAAC,IAAI,CAAA;AAAI,QAAI,IAAIF,UAAQ,OAAO,CAAC,GAAG,IAAE,EAAE,QAAQ,SAAUC,IAAG;AAAEE,wBAAgB,GAAGF,IAAG,EAAEA,EAAC,CAAC;AAAA,IAAI,CAAA,IAAI,OAAO,4BAA4B,OAAO,iBAAiB,GAAG,OAAO,0BAA0B,CAAC,CAAC,IAAID,UAAQ,OAAO,CAAC,CAAC,EAAE,QAAQ,SAAUC,IAAG;AAAE,aAAO,eAAe,GAAGA,IAAG,OAAO,yBAAyB,GAAGA,EAAC,CAAC;AAAA,IAAI,CAAA;AAAA,EAAE;AAAG,SAAO;AAAE;AACrb,SAAS,yBAAyB,QAAQ,UAAU;AAAE,MAAI,UAAU,KAAM,QAAO;AAAI,MAAI,SAAS,8BAA8B,QAAQ,QAAQ;AAAG,MAAI,KAAK;AAAG,MAAI,OAAO,uBAAuB;AAAE,QAAI,mBAAmB,OAAO,sBAAsB,MAAM;AAAG,SAAK,IAAI,GAAG,IAAI,iBAAiB,QAAQ,KAAK;AAAE,YAAM,iBAAiB,CAAC;AAAG,UAAI,SAAS,QAAQ,GAAG,KAAK,EAAG;AAAU,UAAI,CAAC,OAAO,UAAU,qBAAqB,KAAK,QAAQ,GAAG,EAAG;AAAU,aAAO,GAAG,IAAI,OAAO,GAAG;AAAA,IAAE;AAAA,EAAI;AAAC,SAAO;AAAO;AAC1e,SAAS,8BAA8B,QAAQ,UAAU;AAAE,MAAI,UAAU,KAAM,QAAO;AAAI,MAAI,SAAS,CAAA;AAAI,WAAS,OAAO,QAAQ;AAAE,QAAI,OAAO,UAAU,eAAe,KAAK,QAAQ,GAAG,GAAG;AAAE,UAAI,SAAS,QAAQ,GAAG,KAAK,EAAG;AAAU,aAAO,GAAG,IAAI,OAAO,GAAG;AAAA,IAAI;AAAA,EAAA;AAAG,SAAO;AAAO;AACrR,SAASG,kBAAgB,UAAU,aAAa;AAAE,MAAI,EAAE,oBAAoB,cAAc;AAAE,UAAM,IAAI,UAAU,mCAAmC;AAAA,EAAI;AAAA;AACvJ,SAASC,oBAAkB,QAAQ,OAAO;AAAE,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AAAE,QAAI,aAAa,MAAM,CAAC;AAAG,eAAW,aAAa,WAAW,cAAc;AAAO,eAAW,eAAe;AAAM,QAAI,WAAW,WAAY,YAAW,WAAW;AAAM,WAAO,eAAe,QAAQC,iBAAe,WAAW,GAAG,GAAG,UAAU;AAAA,EAAI;AAAA;AAC3U,SAASC,eAAa,aAAa,YAAY,aAAa;AAAE,MAAI,WAAYF,qBAAkB,YAAY,WAAW,UAAU;AAAG,MAAI,YAAaA,qBAAkB,aAAa,WAAW;AAAG,SAAO,eAAe,aAAa,aAAa,EAAE,UAAU,MAAK,CAAE;AAAG,SAAO;AAAY;AAC3R,SAASG,aAAW,GAAG,GAAG,GAAG;AAAE,SAAO,IAAIC,kBAAgB,CAAC,GAAGC,6BAA2B,GAAGC,4BAAyB,IAAK,QAAQ,UAAU,GAAG,KAAK,IAAIF,kBAAgB,CAAC,EAAE,WAAW,IAAI,EAAE,MAAM,GAAG,CAAC,CAAC;AAAE;AACzM,SAASC,6BAA2B,MAAM,MAAM;AAAE,MAAI,SAASZ,UAAQ,IAAI,MAAM,YAAY,OAAO,SAAS,aAAa;AAAE,WAAO;AAAA,aAAiB,SAAS,QAAQ;AAAE,UAAM,IAAI,UAAU,0DAA0D;AAAA,EAAI;AAAC,SAAOc,yBAAuB,IAAI;AAAE;AAC9R,SAASA,yBAAuB,MAAM;AAAE,MAAI,SAAS,QAAQ;AAAE,UAAM,IAAI,eAAe,2DAA2D;AAAA,EAAI;AAAC,SAAO;AAAK;AACpK,SAASD,8BAA4B;AAAE,MAAI;AAAE,QAAI,IAAI,CAAC,QAAQ,UAAU,QAAQ,KAAK,QAAQ,UAAU,SAAS,CAAA,GAAI,WAAY;AAAA,KAAE,CAAC;AAAA,EAAE,SAAUE,IAAG;AAAA,EAAA;AAAG,UAAQF,8BAA4B,SAASA,6BAA4B;AAAE,WAAO,CAAC,CAAC;AAAA,EAAI,GAAA;AAAI;AACjP,SAASF,kBAAgB,GAAG;AAAEA,sBAAkB,OAAO,iBAAiB,OAAO,eAAe,SAAS,SAASA,iBAAgBV,IAAG;AAAE,WAAOA,GAAE,aAAa,OAAO,eAAeA,EAAC;AAAA,EAAE;AAAI,SAAOU,kBAAgB,CAAC;AAAE;AAClN,SAASK,YAAU,UAAU,YAAY;AAAE,MAAI,OAAO,eAAe,cAAc,eAAe,MAAM;AAAE,UAAM,IAAI,UAAU,oDAAoD;AAAA;AAAK,WAAS,YAAY,OAAO,OAAO,cAAc,WAAW,WAAW,EAAE,aAAa,EAAE,OAAO,UAAU,UAAU,MAAM,cAAc,OAAQ,CAAA;AAAG,SAAO,eAAe,UAAU,aAAa,EAAE,UAAU,OAAO;AAAG,MAAI,WAAYC,mBAAgB,UAAU,UAAU;AAAE;AAClc,SAASA,kBAAgB,GAAG,GAAG;AAAEA,sBAAkB,OAAO,iBAAiB,OAAO,eAAe,KAAM,IAAG,SAASA,iBAAgBhB,IAAGiB,IAAG;AAAE,IAAAjB,GAAE,YAAYiB;AAAG,WAAOjB;AAAA,EAAI;AAAE,SAAOgB,kBAAgB,GAAG,CAAC;AAAE;AACtM,SAASZ,kBAAgB,KAAK,KAAK,OAAO;AAAE,QAAMG,iBAAe,GAAG;AAAG,MAAI,OAAO,KAAK;AAAE,WAAO,eAAe,KAAK,KAAK,EAAE,OAAc,YAAY,MAAM,cAAc,MAAM,UAAU,KAAM,CAAA;AAAA,EAAE,OAAQ;AAAE,QAAI,GAAG,IAAI;AAAA,EAAM;AAAG,SAAO;AAAI;AAC1O,SAASA,iBAAe,GAAG;AAAE,MAAI,IAAIW,eAAa,GAAG,QAAQ;AAAG,SAAO,YAAYnB,UAAQ,CAAC,IAAI,IAAI,IAAI;AAAG;AAC3G,SAASmB,eAAa,GAAG,GAAG;AAAE,MAAI,YAAYnB,UAAQ,CAAC,KAAK,CAAC,EAAG,QAAO;AAAG,MAAI,IAAI,EAAE,OAAO,WAAW;AAAG,MAAI,WAAW,GAAG;AAAE,QAAI,IAAI,EAAE,KAAK,GAAG,CAAc;AAAG,QAAI,YAAYA,UAAQ,CAAC,EAAG,QAAO;AAAG,UAAM,IAAI,UAAU,8CAA8C;AAAA,EAAI;AAAC,SAAyB,OAAiB,CAAC;AAAE;AAenT,IAAI,kBAA+B,yBAAU,gBAAgB;AAClE,WAASoB,mBAAkB;AACzBd,sBAAgB,MAAMc,gBAAe;AACrC,WAAOV,aAAW,MAAMU,kBAAiB,SAAS;AAAA,EACtD;AACEJ,cAAUI,kBAAiB,cAAc;AACzC,SAAOX,eAAaW,kBAAiB,CAAC;AAAA,IACpC,KAAK;AAAA,IACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAMA,SAAS,kBAAkB,MAAM;AAC/B,YAAI,aAAa,KAAK;AACtB,YAAI,cAAc,KAAK,OACrB,QAAQ,YAAY,OACpB,KAAK,YAAY,IACjB,KAAK,YAAY;AACnB,eAAO,iBAAiB,IAAI,IAAI,YAAY,KAAK;AAAA,MACvD;AAAA;AAAA,EACA,GAAK;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,oBAAoB;AAClC,UAAI,cAAc,KAAK,MAAM;AAC7B,UAAI;AACJ,cAAQ,aAAW;AAAA,QACjB,KAAK;AACH,uBAAa;AACb;AAAA,QACF,KAAK;AACH,uBAAa;AACb;AAAA,QACF;AACE,uBAAa;AACb;AAAA,MACV;AACM,aAAO;AAAA,IACb;AAAA,EACA,GAAK;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,aAAa;AAC3B,UAAI,eAAe,KAAK,OACtB,KAAK,aAAa,IAClB,KAAK,aAAa,IAClB,QAAQ,aAAa,OACrB,QAAQ,aAAa;AACvB,UAAI,gBAAgBC,QAAM,OAAO,SAAU,OAAO;AAChD,eAAO,MAAM,cAAc;AAAA,MACnC,CAAO;AACD,UAAI,gBAAgBC,QAAM,OAAO,SAAU,OAAO;AAChD,eAAO,MAAM,cAAc;AAAA,MACnC,CAAO;AACD,aAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA,YAAY;AAAA,QACZ,UAAU;AAAA,QACV,aAAa,cAAc,cAAc;AAAA,QACzC,aAAa,cAAc,cAAc;AAAA,MAC1C;AAAA,IACP;AAAA,EACA,GAAK;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,iBAAiB;AAC/B,UAAI,eAAe,KAAK,OACtB,KAAK,aAAa,IAClB,KAAK,aAAa,IAClB,QAAQ,aAAa,OACrB,QAAQ,aAAa,OACrB,WAAW,aAAa,UACxB,SAAS,yBAAyB,cAAc,SAAS;AAC3D,UAAI,SAAS,MAAM,OAAO,SAAU,QAAQ,OAAO;AACjD,eAAO,CAAC,KAAK,IAAI,OAAO,CAAC,GAAG,MAAM,UAAU,GAAG,KAAK,IAAI,OAAO,CAAC,GAAG,MAAM,UAAU,CAAC;AAAA,MAC5F,GAAS,CAAC,UAAU,SAAS,CAAC;AACxB,UAAI,SAAS,iBAAiB,IAAI,IAAI,OAAO,CAAC,GAAG,KAAK;AACtD,UAAI,SAAS,iBAAiB,IAAI,IAAI,OAAO,CAAC,GAAG,KAAK;AACtD,UAAI,QAAQlB,gBAAcA,gBAAcA,gBAAc,CAAA,GAAI,YAAY,QAAQ,KAAK,CAAC,GAAG,IAAI;AAAA,QACzF,MAAM;AAAA,MACP,GAAE,YAAY,UAAU,KAAK,CAAC,GAAG,CAAA,GAAI;AAAA,QACpC,IAAI,OAAO;AAAA,QACX,IAAI,OAAO;AAAA,QACX,IAAI,OAAO;AAAA,QACX,IAAI,OAAO;AAAA,MACnB,CAAO;AACD,aAAoB,sBAAM,cAAc,QAAQjB,WAAS;AAAA,QACvD,WAAW;AAAA,MACZ,GAAE,KAAK,CAAC;AAAA,IACf;AAAA,EACA,GAAK;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,cAAc;AAC5B,UAAI,QAAQ;AACZ,UAAI,eAAe,KAAK,OACtB,QAAQ,aAAa,OACrB,OAAO,aAAa,MACpB,QAAQ,aAAa,OACrB,gBAAgB,aAAa,eAC7B,SAAS,aAAa,QACtB,SAAS,yBAAyB,cAAc,UAAU;AAC5D,UAAI,aAAa,KAAK,kBAAmB;AACzC,UAAI,YAAY,YAAY,QAAQ,KAAK;AACzC,UAAI,kBAAkB,YAAY,MAAM,KAAK;AAC7C,UAAI,QAAQ,MAAM,IAAI,SAAU,OAAO,GAAG;AACxC,YAAI,QAAQ,MAAM,kBAAkB,KAAK;AACzC,YAAI,YAAYiB,gBAAcA,gBAAcA,gBAAcA,gBAAc;AAAA,UACtE;AAAA,UACA,WAAW,UAAU,OAAO,KAAK,OAAO,IAAI,EAAE,OAAO,MAAM,GAAG,IAAI,EAAE,OAAO,MAAM,GAAG,GAAG;AAAA,QACjG,GAAW,SAAS,GAAG,IAAI;AAAA,UACjB,QAAQ;AAAA,UACR,MAAM;AAAA,QAChB,GAAW,eAAe,GAAG,IAAI;AAAA,UACvB,OAAO;AAAA,QACjB,GAAW,KAAK,GAAG,IAAI;AAAA,UACb,SAAS;AAAA,QACnB,CAAS;AACD,eAAoB,sBAAM,cAAc,OAAOjB,WAAS;AAAA,UACtD,WAAW,KAAK,mCAAmC,iBAAiB,IAAI,CAAC;AAAA,UACzE,KAAK,QAAQ,OAAO,MAAM,UAAU;AAAA,QAC9C,GAAW,mBAAmB,MAAM,OAAO,OAAO,CAAC,CAAC,GAAGiC,iBAAgB,eAAe,MAAM,WAAW,gBAAgB,cAAc,MAAM,OAAO,CAAC,IAAI,MAAM,KAAK,CAAC;AAAA,MACnK,CAAO;AACD,aAAoB,sBAAM,cAAc,OAAO;AAAA,QAC7C,WAAW;AAAA,MACZ,GAAE,KAAK;AAAA,IACd;AAAA,EACA,GAAK;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,SAAS;AACvB,UAAI,eAAe,KAAK,OACtB,QAAQ,aAAa,OACrB,WAAW,aAAa,UACxB,OAAO,aAAa;AACtB,UAAI,CAAC,SAAS,CAAC,MAAM,QAAQ;AAC3B,eAAO;AAAA,MACf;AACM,aAAoB,sBAAM,cAAc,OAAO;AAAA,QAC7C,WAAW,KAAK,8BAA8B,KAAK,MAAM,SAAS;AAAA,MAC1E,GAAS,YAAY,KAAK,eAAgB,GAAE,QAAQ,KAAK,YAAW,GAAI,MAAM,mBAAmB,KAAK,OAAO,KAAK,WAAY,CAAA,CAAC;AAAA,IAC/H;AAAA,EACG,CAAA,GAAG,CAAC;AAAA,IACH,KAAK;AAAA,IACL,OAAO,SAAS,eAAe,QAAQ,OAAO,OAAO;AACnD,UAAI;AACJ,UAAkB,sBAAM,eAAe,MAAM,GAAG;AAC9C,mBAAwB,sBAAM,aAAa,QAAQ,KAAK;AAAA,MAChE,WAAiB,WAAW,MAAM,GAAG;AAC7B,mBAAW,OAAO,KAAK;AAAA,MAC/B,OAAa;AACL,mBAAwB,sBAAM,cAAc,MAAMjC,WAAS,CAAE,GAAE,OAAO;AAAA,UACpE,WAAW;AAAA,QACZ,CAAA,GAAG,KAAK;AAAA,MACjB;AACM,aAAO;AAAA,IACb;AAAA,EACA,CAAG,CAAC;AACJ,EAAEoC,0BAAa;AACflB,kBAAgB,iBAAiB,eAAe,iBAAiB;AACjEA,kBAAgB,iBAAiB,YAAY,YAAY;AACzDA,kBAAgB,iBAAiB,gBAAgB;AAAA,EAC/C,MAAM;AAAA,EACN,cAAc;AAAA,EACd,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,OAAO;AAAA,EACP,aAAa;AAAA,EACb,QAAQ;AAAA,EACR,UAAU;AAAA,EACV,MAAM;AAAA,EACN,WAAW;AAAA,EACX,mBAAmB;AAAA,EACnB,OAAO;AAAA,EACP,yBAAyB;AAC3B,CAAC;AChND,SAASL,UAAQ,GAAG;AAAE;AAA2B,SAAOA,YAAU,cAAc,OAAO,UAAU,YAAY,OAAO,OAAO,WAAW,SAAUC,IAAG;AAAE,WAAO,OAAOA;AAAA,MAAO,SAAUA,IAAG;AAAE,WAAOA,MAAK,cAAc,OAAO,UAAUA,GAAE,gBAAgB,UAAUA,OAAM,OAAO,YAAY,WAAW,OAAOA;AAAA,EAAE,GAAID,UAAQ,CAAC;AAAE;AAC5T,SAASb,aAAW;AAAEA,eAAW,OAAO,SAAS,OAAO,OAAO,KAAM,IAAG,SAAU,QAAQ;AAAE,aAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AAAE,UAAI,SAAS,UAAU,CAAC;AAAG,eAAS,OAAO,QAAQ;AAAE,YAAI,OAAO,UAAU,eAAe,KAAK,QAAQ,GAAG,GAAG;AAAE,iBAAO,GAAG,IAAI,OAAO,GAAG;AAAA,QAAI;AAAA,MAAA;AAAA;AAAK,WAAO;AAAA,EAAO;AAAI,SAAOA,WAAS,MAAM,MAAM,SAAS;AAAE;AACjV,SAASe,UAAQ,GAAG,GAAG;AAAE,MAAI,IAAI,OAAO,KAAK,CAAC;AAAG,MAAI,OAAO,uBAAuB;AAAE,QAAI,IAAI,OAAO,sBAAsB,CAAC;AAAG,UAAM,IAAI,EAAE,OAAO,SAAUC,IAAG;AAAE,aAAO,OAAO,yBAAyB,GAAGA,EAAC,EAAE;AAAA,IAAa,CAAA,IAAI,EAAE,KAAK,MAAM,GAAG,CAAC;AAAA;AAAK,SAAO;AAAE;AAC7P,SAASC,gBAAc,GAAG;AAAE,WAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AAAE,QAAI,IAAI,QAAQ,UAAU,CAAC,IAAI,UAAU,CAAC,IAAI,CAAA;AAAI,QAAI,IAAIF,UAAQ,OAAO,CAAC,GAAG,IAAE,EAAE,QAAQ,SAAUC,IAAG;AAAEE,wBAAgB,GAAGF,IAAG,EAAEA,EAAC,CAAC;AAAA,IAAI,CAAA,IAAI,OAAO,4BAA4B,OAAO,iBAAiB,GAAG,OAAO,0BAA0B,CAAC,CAAC,IAAID,UAAQ,OAAO,CAAC,CAAC,EAAE,QAAQ,SAAUC,IAAG;AAAE,aAAO,eAAe,GAAGA,IAAG,OAAO,yBAAyB,GAAGA,EAAC,CAAC;AAAA,IAAI,CAAA;AAAA,EAAE;AAAG,SAAO;AAAE;AACrb,SAASG,kBAAgB,UAAU,aAAa;AAAE,MAAI,EAAE,oBAAoB,cAAc;AAAE,UAAM,IAAI,UAAU,mCAAmC;AAAA,EAAI;AAAA;AACvJ,SAASC,oBAAkB,QAAQ,OAAO;AAAE,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AAAE,QAAI,aAAa,MAAM,CAAC;AAAG,eAAW,aAAa,WAAW,cAAc;AAAO,eAAW,eAAe;AAAM,QAAI,WAAW,WAAY,YAAW,WAAW;AAAM,WAAO,eAAe,QAAQC,iBAAe,WAAW,GAAG,GAAG,UAAU;AAAA,EAAI;AAAA;AAC3U,SAASC,eAAa,aAAa,YAAY,aAAa;AAAE,MAAI,WAAYF,qBAAkB,YAAY,WAAW,UAAU;AAAG,MAAI,YAAaA,qBAAkB,aAAa,WAAW;AAAG,SAAO,eAAe,aAAa,aAAa,EAAE,UAAU,MAAK,CAAE;AAAG,SAAO;AAAY;AAC3R,SAASG,aAAW,GAAG,GAAG,GAAG;AAAE,SAAO,IAAIC,kBAAgB,CAAC,GAAGC,6BAA2B,GAAGC,4BAAyB,IAAK,QAAQ,UAAU,GAAG,KAAK,IAAIF,kBAAgB,CAAC,EAAE,WAAW,IAAI,EAAE,MAAM,GAAG,CAAC,CAAC;AAAE;AACzM,SAASC,6BAA2B,MAAM,MAAM;AAAE,MAAI,SAASZ,UAAQ,IAAI,MAAM,YAAY,OAAO,SAAS,aAAa;AAAE,WAAO;AAAA,aAAiB,SAAS,QAAQ;AAAE,UAAM,IAAI,UAAU,0DAA0D;AAAA,EAAI;AAAC,SAAOc,yBAAuB,IAAI;AAAE;AAC9R,SAASA,yBAAuB,MAAM;AAAE,MAAI,SAAS,QAAQ;AAAE,UAAM,IAAI,eAAe,2DAA2D;AAAA,EAAI;AAAC,SAAO;AAAK;AACpK,SAASD,8BAA4B;AAAE,MAAI;AAAE,QAAI,IAAI,CAAC,QAAQ,UAAU,QAAQ,KAAK,QAAQ,UAAU,SAAS,CAAA,GAAI,WAAY;AAAA,KAAE,CAAC;AAAA,EAAE,SAAUE,IAAG;AAAA,EAAA;AAAG,UAAQF,8BAA4B,SAASA,6BAA4B;AAAE,WAAO,CAAC,CAAC;AAAA,EAAI,GAAA;AAAI;AACjP,SAASF,kBAAgB,GAAG;AAAEA,sBAAkB,OAAO,iBAAiB,OAAO,eAAe,SAAS,SAASA,iBAAgBV,IAAG;AAAE,WAAOA,GAAE,aAAa,OAAO,eAAeA,EAAC;AAAA,EAAE;AAAI,SAAOU,kBAAgB,CAAC;AAAE;AAClN,SAASK,YAAU,UAAU,YAAY;AAAE,MAAI,OAAO,eAAe,cAAc,eAAe,MAAM;AAAE,UAAM,IAAI,UAAU,oDAAoD;AAAA;AAAK,WAAS,YAAY,OAAO,OAAO,cAAc,WAAW,WAAW,EAAE,aAAa,EAAE,OAAO,UAAU,UAAU,MAAM,cAAc,OAAQ,CAAA;AAAG,SAAO,eAAe,UAAU,aAAa,EAAE,UAAU,OAAO;AAAG,MAAI,WAAYC,mBAAgB,UAAU,UAAU;AAAE;AAClc,SAASA,kBAAgB,GAAG,GAAG;AAAEA,sBAAkB,OAAO,iBAAiB,OAAO,eAAe,KAAM,IAAG,SAASA,iBAAgBhB,IAAGiB,IAAG;AAAE,IAAAjB,GAAE,YAAYiB;AAAG,WAAOjB;AAAA,EAAI;AAAE,SAAOgB,kBAAgB,GAAG,CAAC;AAAE;AACtM,SAASZ,kBAAgB,KAAK,KAAK,OAAO;AAAE,QAAMG,iBAAe,GAAG;AAAG,MAAI,OAAO,KAAK;AAAE,WAAO,eAAe,KAAK,KAAK,EAAE,OAAc,YAAY,MAAM,cAAc,MAAM,UAAU,KAAM,CAAA;AAAA,EAAE,OAAQ;AAAE,QAAI,GAAG,IAAI;AAAA,EAAM;AAAG,SAAO;AAAI;AAC1O,SAASA,iBAAe,GAAG;AAAE,MAAI,IAAIW,eAAa,GAAG,QAAQ;AAAG,SAAO,YAAYnB,UAAQ,CAAC,IAAI,IAAI,IAAI;AAAG;AAC3G,SAASmB,eAAa,GAAG,GAAG;AAAE,MAAI,YAAYnB,UAAQ,CAAC,KAAK,CAAC,EAAG,QAAO;AAAG,MAAI,IAAI,EAAE,OAAO,WAAW;AAAG,MAAI,WAAW,GAAG;AAAE,QAAI,IAAI,EAAE,KAAK,GAAG,CAAc;AAAG,QAAI,YAAYA,UAAQ,CAAC,EAAG,QAAO;AAAG,UAAM,IAAI,UAAU,8CAA8C;AAAA,EAAI;AAAC,SAAyB,OAAiB,CAAC;AAAE;AAc1T,IAAI,SAAS,KAAK,KAAK;AACvB,IAAI,MAAM;AACH,IAAI,iBAA8B,yBAAU,gBAAgB;AACjE,WAASwB,kBAAiB;AACxBlB,sBAAgB,MAAMkB,eAAc;AACpC,WAAOd,aAAW,MAAMc,iBAAgB,SAAS;AAAA,EACrD;AACER,cAAUQ,iBAAgB,cAAc;AACxC,SAAOf,eAAae,iBAAgB,CAAC;AAAA,IACnC,KAAK;AAAA,IACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAQA,SAAS,iBAAiB,MAAM;AAC9B,YAAI,cAAc,KAAK,OACrB,KAAK,YAAY,IACjB,KAAK,YAAY,IACjB,SAAS,YAAY,QACrB,cAAc,YAAY,aAC1B,WAAW,YAAY;AACzB,YAAI,eAAe,YAAY;AAC/B,YAAI,KAAK,iBAAiB,IAAI,IAAI,QAAQ,KAAK,UAAU;AACzD,YAAI,KAAK,iBAAiB,IAAI,IAAI,UAAU,gBAAgB,UAAU,KAAK,KAAK,cAAc,KAAK,UAAU;AAC7G,eAAO;AAAA,UACL,IAAI,GAAG;AAAA,UACP,IAAI,GAAG;AAAA,UACP,IAAI,GAAG;AAAA,UACP,IAAI,GAAG;AAAA,QACR;AAAA,MACP;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,GAAK;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,kBAAkB,MAAM;AACtC,UAAI,cAAc,KAAK,MAAM;AAC7B,UAAI,MAAM,KAAK,IAAI,CAAC,KAAK,aAAa,MAAM;AAC5C,UAAI;AACJ,UAAI,MAAM,KAAK;AACb,qBAAa,gBAAgB,UAAU,UAAU;AAAA,MACzD,WAAiB,MAAM,OAAM;AACrB,qBAAa,gBAAgB,UAAU,QAAQ;AAAA,MACvD,OAAa;AACL,qBAAa;AAAA,MACrB;AACM,aAAO;AAAA,IACb;AAAA,EACA,GAAK;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,iBAAiB;AAC/B,UAAI,eAAe,KAAK,OACtB,KAAK,aAAa,IAClB,KAAK,aAAa,IAClB,SAAS,aAAa,QACtB,WAAW,aAAa,UACxB,eAAe,aAAa;AAC9B,UAAI,QAAQpB,gBAAcA,gBAAc,IAAI,YAAY,KAAK,OAAO,KAAK,CAAC,GAAG,IAAI;AAAA,QAC/E,MAAM;AAAA,MACd,GAAS,YAAY,UAAU,KAAK,CAAC;AAC/B,UAAI,iBAAiB,UAAU;AAC7B,eAAoB,sBAAM,cAAc,KAAKjB,WAAS;AAAA,UACpD,WAAW;AAAA,QACZ,GAAE,OAAO;AAAA,UACR;AAAA,UACA;AAAA,UACA,GAAG;AAAA,QACb,CAAS,CAAC;AAAA,MACV;AACM,UAAI,QAAQ,KAAK,MAAM;AACvB,UAAI,SAAS,MAAM,IAAI,SAAU,OAAO;AACtC,eAAO,iBAAiB,IAAI,IAAI,QAAQ,MAAM,UAAU;AAAA,MAChE,CAAO;AACD,aAAoB,sBAAM,cAAc,SAASA,WAAS;AAAA,QACxD,WAAW;AAAA,MACZ,GAAE,OAAO;AAAA,QACR;AAAA,MACR,CAAO,CAAC;AAAA,IACR;AAAA,EACA,GAAK;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,cAAc;AAC5B,UAAI,QAAQ;AACZ,UAAI,eAAe,KAAK,OACtB,QAAQ,aAAa,OACrB,OAAO,aAAa,MACpB,WAAW,aAAa,UACxB,gBAAgB,aAAa,eAC7B,SAAS,aAAa;AACxB,UAAI,YAAY,YAAY,KAAK,OAAO,KAAK;AAC7C,UAAI,kBAAkB,YAAY,MAAM,KAAK;AAC7C,UAAI,gBAAgBiB,gBAAcA,gBAAc,CAAA,GAAI,SAAS,GAAG,IAAI;AAAA,QAClE,MAAM;AAAA,MACd,GAAS,YAAY,UAAU,KAAK,CAAC;AAC/B,UAAI,QAAQ,MAAM,IAAI,SAAU,OAAO,GAAG;AACxC,YAAI,YAAY,MAAM,iBAAiB,KAAK;AAC5C,YAAI,aAAa,MAAM,kBAAkB,KAAK;AAC9C,YAAI,YAAYA,gBAAcA,gBAAcA,gBAAc;AAAA,UACxD;AAAA,QACV,GAAW,SAAS,GAAG,IAAI;AAAA,UACjB,QAAQ;AAAA,UACR,MAAM;AAAA,QAChB,GAAW,eAAe,GAAG,IAAI;AAAA,UACvB,OAAO;AAAA,UACP,SAAS;AAAA,UACT,GAAG,UAAU;AAAA,UACb,GAAG,UAAU;AAAA,QACvB,CAAS;AACD,eAAoB,sBAAM,cAAc,OAAOjB,WAAS;AAAA,UACtD,WAAW,KAAK,kCAAkC,iBAAiB,IAAI,CAAC;AAAA,UACxE,KAAK,QAAQ,OAAO,MAAM,UAAU;AAAA,QACrC,GAAE,mBAAmB,MAAM,OAAO,OAAO,CAAC,CAAC,GAAG,YAAyB,sBAAM,cAAc,QAAQA,WAAS;AAAA,UAC3G,WAAW;AAAA,QACrB,GAAW,eAAe,SAAS,CAAC,GAAG,QAAQqC,gBAAe,eAAe,MAAM,WAAW,gBAAgB,cAAc,MAAM,OAAO,CAAC,IAAI,MAAM,KAAK,CAAC;AAAA,MAC1J,CAAO;AACD,aAAoB,sBAAM,cAAc,OAAO;AAAA,QAC7C,WAAW;AAAA,MACZ,GAAE,KAAK;AAAA,IACd;AAAA,EACA,GAAK;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,SAAS;AACvB,UAAI,eAAe,KAAK,OACtB,QAAQ,aAAa,OACrB,SAAS,aAAa,QACtB,WAAW,aAAa;AAC1B,UAAI,UAAU,KAAK,CAAC,SAAS,CAAC,MAAM,QAAQ;AAC1C,eAAO;AAAA,MACf;AACM,aAAoB,sBAAM,cAAc,OAAO;AAAA,QAC7C,WAAW,KAAK,6BAA6B,KAAK,MAAM,SAAS;AAAA,MACzE,GAAS,YAAY,KAAK,eAAgB,GAAE,KAAK,YAAW,CAAE;AAAA,IAC9D;AAAA,EACG,CAAA,GAAG,CAAC;AAAA,IACH,KAAK;AAAA,IACL,OAAO,SAAS,eAAe,QAAQ,OAAO,OAAO;AACnD,UAAI;AACJ,UAAkB,sBAAM,eAAe,MAAM,GAAG;AAC9C,mBAAwB,sBAAM,aAAa,QAAQ,KAAK;AAAA,MAChE,WAAiB,WAAW,MAAM,GAAG;AAC7B,mBAAW,OAAO,KAAK;AAAA,MAC/B,OAAa;AACL,mBAAwB,sBAAM,cAAc,MAAMrC,WAAS,CAAE,GAAE,OAAO;AAAA,UACpE,WAAW;AAAA,QACZ,CAAA,GAAG,KAAK;AAAA,MACjB;AACM,aAAO;AAAA,IACb;AAAA,EACA,CAAG,CAAC;AACJ,EAAEoC,0BAAa;AACflB,kBAAgB,gBAAgB,eAAe,gBAAgB;AAC/DA,kBAAgB,gBAAgB,YAAY,WAAW;AACvDA,kBAAgB,gBAAgB,gBAAgB;AAAA,EAC9C,MAAM;AAAA,EACN,aAAa;AAAA,EACb,OAAO;AAAA,EACP,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,aAAa;AAAA,EACb,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,MAAM;AAAA,EACN,MAAM;AAAA,EACN,yBAAyB;AAC3B,CAAC;AC3MD,IAAI;AACJ,SAAS,QAAQ,GAAG;AAAE;AAA2B,SAAO,UAAU,cAAc,OAAO,UAAU,YAAY,OAAO,OAAO,WAAW,SAAUJ,IAAG;AAAE,WAAO,OAAOA;AAAA,MAAO,SAAUA,IAAG;AAAE,WAAOA,MAAK,cAAc,OAAO,UAAUA,GAAE,gBAAgB,UAAUA,OAAM,OAAO,YAAY,WAAW,OAAOA;AAAA,EAAE,GAAI,QAAQ,CAAC;AAAE;AAC5T,SAAS,WAAW;AAAE,aAAW,OAAO,SAAS,OAAO,OAAO,KAAM,IAAG,SAAU,QAAQ;AAAE,aAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AAAE,UAAI,SAAS,UAAU,CAAC;AAAG,eAAS,OAAO,QAAQ;AAAE,YAAI,OAAO,UAAU,eAAe,KAAK,QAAQ,GAAG,GAAG;AAAE,iBAAO,GAAG,IAAI,OAAO,GAAG;AAAA,QAAI;AAAA,MAAA;AAAA;AAAK,WAAO;AAAA,EAAO;AAAI,SAAO,SAAS,MAAM,MAAM,SAAS;AAAE;AACjV,SAAS,QAAQ,GAAG,GAAG;AAAE,MAAI,IAAI,OAAO,KAAK,CAAC;AAAG,MAAI,OAAO,uBAAuB;AAAE,QAAI,IAAI,OAAO,sBAAsB,CAAC;AAAG,UAAM,IAAI,EAAE,OAAO,SAAUE,IAAG;AAAE,aAAO,OAAO,yBAAyB,GAAGA,EAAC,EAAE;AAAA,IAAa,CAAA,IAAI,EAAE,KAAK,MAAM,GAAG,CAAC;AAAA;AAAK,SAAO;AAAE;AAC7P,SAAS,cAAc,GAAG;AAAE,WAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AAAE,QAAI,IAAI,QAAQ,UAAU,CAAC,IAAI,UAAU,CAAC,IAAI,CAAA;AAAI,QAAI,IAAI,QAAQ,OAAO,CAAC,GAAG,IAAE,EAAE,QAAQ,SAAUA,IAAG;AAAE,sBAAgB,GAAGA,IAAG,EAAEA,EAAC,CAAC;AAAA,IAAI,CAAA,IAAI,OAAO,4BAA4B,OAAO,iBAAiB,GAAG,OAAO,0BAA0B,CAAC,CAAC,IAAI,QAAQ,OAAO,CAAC,CAAC,EAAE,QAAQ,SAAUA,IAAG;AAAE,aAAO,eAAe,GAAGA,IAAG,OAAO,yBAAyB,GAAGA,EAAC,CAAC;AAAA,IAAI,CAAA;AAAA,EAAE;AAAG,SAAO;AAAE;AACrb,SAAS,gBAAgB,UAAU,aAAa;AAAE,MAAI,EAAE,oBAAoB,cAAc;AAAE,UAAM,IAAI,UAAU,mCAAmC;AAAA,EAAI;AAAA;AACvJ,SAAS,kBAAkB,QAAQ,OAAO;AAAE,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AAAE,QAAI,aAAa,MAAM,CAAC;AAAG,eAAW,aAAa,WAAW,cAAc;AAAO,eAAW,eAAe;AAAM,QAAI,WAAW,WAAY,YAAW,WAAW;AAAM,WAAO,eAAe,QAAQ,eAAe,WAAW,GAAG,GAAG,UAAU;AAAA,EAAI;AAAA;AAC3U,SAAS,aAAa,aAAa,YAAY,aAAa;AAAE,MAAI,WAAY,mBAAkB,YAAY,WAAW,UAAU;AAAG,MAAI,YAAa,mBAAkB,aAAa,WAAW;AAAG,SAAO,eAAe,aAAa,aAAa,EAAE,UAAU,MAAK,CAAE;AAAG,SAAO;AAAY;AAC3R,SAAS,WAAW,GAAG,GAAG,GAAG;AAAE,SAAO,IAAI,gBAAgB,CAAC,GAAG,2BAA2B,GAAG,0BAAyB,IAAK,QAAQ,UAAU,GAAG,KAAK,IAAI,gBAAgB,CAAC,EAAE,WAAW,IAAI,EAAE,MAAM,GAAG,CAAC,CAAC;AAAE;AACzM,SAAS,2BAA2B,MAAM,MAAM;AAAE,MAAI,SAAS,QAAQ,IAAI,MAAM,YAAY,OAAO,SAAS,aAAa;AAAE,WAAO;AAAA,aAAiB,SAAS,QAAQ;AAAE,UAAM,IAAI,UAAU,0DAA0D;AAAA,EAAI;AAAC,SAAO,uBAAuB,IAAI;AAAE;AAC9R,SAAS,uBAAuB,MAAM;AAAE,MAAI,SAAS,QAAQ;AAAE,UAAM,IAAI,eAAe,2DAA2D;AAAA,EAAI;AAAC,SAAO;AAAK;AACpK,SAAS,4BAA4B;AAAE,MAAI;AAAE,QAAI,IAAI,CAAC,QAAQ,UAAU,QAAQ,KAAK,QAAQ,UAAU,SAAS,CAAA,GAAI,WAAY;AAAA,KAAE,CAAC;AAAA,EAAE,SAAUY,IAAG;AAAA,EAAA;AAAG,UAAQ,4BAA4B,SAASF,6BAA4B;AAAE,WAAO,CAAC,CAAC;AAAA,EAAI,GAAA;AAAI;AACjP,SAAS,gBAAgB,GAAG;AAAE,oBAAkB,OAAO,iBAAiB,OAAO,eAAe,SAAS,SAASF,iBAAgBV,IAAG;AAAE,WAAOA,GAAE,aAAa,OAAO,eAAeA,EAAC;AAAA,EAAE;AAAI,SAAO,gBAAgB,CAAC;AAAE;AAClN,SAAS,UAAU,UAAU,YAAY;AAAE,MAAI,OAAO,eAAe,cAAc,eAAe,MAAM;AAAE,UAAM,IAAI,UAAU,oDAAoD;AAAA;AAAK,WAAS,YAAY,OAAO,OAAO,cAAc,WAAW,WAAW,EAAE,aAAa,EAAE,OAAO,UAAU,UAAU,MAAM,cAAc,OAAQ,CAAA;AAAG,SAAO,eAAe,UAAU,aAAa,EAAE,UAAU,OAAO;AAAG,MAAI,WAAY,iBAAgB,UAAU,UAAU;AAAE;AAClc,SAAS,gBAAgB,GAAG,GAAG;AAAE,oBAAkB,OAAO,iBAAiB,OAAO,eAAe,KAAM,IAAG,SAASgB,iBAAgBhB,IAAGiB,IAAG;AAAE,IAAAjB,GAAE,YAAYiB;AAAG,WAAOjB;AAAA,EAAI;AAAE,SAAO,gBAAgB,GAAG,CAAC;AAAE;AACtM,SAAS,gBAAgB,KAAK,KAAK,OAAO;AAAE,QAAM,eAAe,GAAG;AAAG,MAAI,OAAO,KAAK;AAAE,WAAO,eAAe,KAAK,KAAK,EAAE,OAAc,YAAY,MAAM,cAAc,MAAM,UAAU,KAAM,CAAA;AAAA,EAAE,OAAQ;AAAE,QAAI,GAAG,IAAI;AAAA,EAAM;AAAG,SAAO;AAAI;AAC1O,SAAS,eAAe,GAAG;AAAE,MAAI,IAAI,aAAa,GAAG,QAAQ;AAAG,SAAO,YAAY,QAAQ,CAAC,IAAI,IAAI,IAAI;AAAG;AAC3G,SAAS,aAAa,GAAG,GAAG;AAAE,MAAI,YAAY,QAAQ,CAAC,KAAK,CAAC,EAAG,QAAO;AAAG,MAAI,IAAI,EAAE,OAAO,WAAW;AAAG,MAAI,WAAW,GAAG;AAAE,QAAI,IAAI,EAAE,KAAK,GAAG,CAAc;AAAG,QAAI,YAAY,QAAQ,CAAC,EAAG,QAAO;AAAG,UAAM,IAAI,UAAU,8CAA8C;AAAA,EAAI;AAAC,SAAyB,OAAiB,CAAC;AAAE;AAyBnT,IAAI,MAAmB,yBAAU,gBAAgB;AACtD,WAASwB,KAAI,OAAO;AAClB,QAAI;AACJ,oBAAgB,MAAMA,IAAG;AACzB,YAAQ,WAAW,MAAMA,MAAK,CAAC,KAAK,CAAC;AACrC,oBAAgB,OAAO,UAAU,IAAI;AACrC,oBAAgB,OAAO,cAAc,EAAE;AACvC,oBAAgB,OAAO,MAAM,SAAS,eAAe,CAAC;AACtD,oBAAgB,OAAO,sBAAsB,WAAY;AACvD,UAAI,iBAAiB,MAAM,MAAM;AACjC,YAAM,SAAS;AAAA,QACb,qBAAqB;AAAA,MAC7B,CAAO;AACD,UAAI,WAAW,cAAc,GAAG;AAC9B,uBAAgB;AAAA,MACxB;AAAA,IACA,CAAK;AACD,oBAAgB,OAAO,wBAAwB,WAAY;AACzD,UAAI,mBAAmB,MAAM,MAAM;AACnC,YAAM,SAAS;AAAA,QACb,qBAAqB;AAAA,MAC7B,CAAO;AACD,UAAI,WAAW,gBAAgB,GAAG;AAChC,yBAAkB;AAAA,MAC1B;AAAA,IACA,CAAK;AACD,UAAM,QAAQ;AAAA,MACZ,qBAAqB,CAAC,MAAM;AAAA,MAC5B,uBAAuB,MAAM;AAAA,MAC7B,iBAAiB,MAAM;AAAA,MACvB,eAAe;AAAA,IAChB;AACD,WAAO;AAAA,EACX;AACE,YAAUA,MAAK,cAAc;AAC7B,SAAO,aAAaA,MAAK,CAAC;AAAA,IACxB,KAAK;AAAA,IACL,OAAO,SAAS,cAAc,GAAG;AAC/B,UAAI,cAAc,KAAK,MAAM;AAC7B,UAAI,MAAM,QAAQ,WAAW,GAAG;AAC9B,eAAO,YAAY,QAAQ,CAAC,MAAM;AAAA,MAC1C;AACM,aAAO,MAAM;AAAA,IACnB;AAAA,EACA,GAAK;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,iBAAiB;AAC/B,UAAI,cAAc,KAAK,MAAM;AAC7B,aAAO,MAAM,QAAQ,WAAW,IAAI,YAAY,WAAW,IAAI,eAAe,gBAAgB;AAAA,IACpG;AAAA,EACA,GAAK;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,aAAa,SAAS;AACpC,UAAI,oBAAoB,KAAK,MAAM;AACnC,UAAI,qBAAqB,CAAC,KAAK,MAAM,qBAAqB;AACxD,eAAO;AAAA,MACf;AACM,UAAI,cAAc,KAAK,OACrB,QAAQ,YAAY,OACpB,YAAY,YAAY,WACxB,UAAU,YAAY,SACtB,WAAW,YAAY;AACzB,UAAI,WAAW,YAAY,KAAK,OAAO,KAAK;AAC5C,UAAI,mBAAmB,YAAY,OAAO,KAAK;AAC/C,UAAI,uBAAuB,YAAY,WAAW,KAAK;AACvD,UAAI,eAAe,SAAS,MAAM,gBAAgB;AAClD,UAAI,SAAS,QAAQ,IAAI,SAAU,OAAO,GAAG;AAC3C,YAAI,YAAY,MAAM,aAAa,MAAM,YAAY;AACrD,YAAI,WAAW,iBAAiB,MAAM,IAAI,MAAM,IAAI,MAAM,cAAc,cAAc,QAAQ;AAC9F,YAAI,aAAa,cAAc,cAAc,cAAc,cAAc,CAAE,GAAE,QAAQ,GAAG,KAAK,GAAG,IAAI;AAAA,UAClG,QAAQ;AAAA,QAClB,GAAW,gBAAgB,GAAG,IAAI;AAAA,UACxB,OAAO;AAAA,UACP,YAAYA,KAAI,cAAc,SAAS,GAAG,MAAM,EAAE;AAAA,QACnD,GAAE,QAAQ;AACX,YAAI,YAAY,cAAc,cAAc,cAAc,cAAc,CAAE,GAAE,QAAQ,GAAG,KAAK,GAAG,IAAI;AAAA,UACjG,MAAM;AAAA,UACN,QAAQ,MAAM;AAAA,QACxB,GAAW,oBAAoB,GAAG,IAAI;AAAA,UAC5B,OAAO;AAAA,UACP,QAAQ,CAAC,iBAAiB,MAAM,IAAI,MAAM,IAAI,MAAM,aAAa,QAAQ,GAAG,QAAQ;AAAA,QAC9F,CAAS;AACD,YAAI,cAAc;AAElB,YAAI,MAAM,OAAO,KAAK,MAAM,QAAQ,GAAG;AACrC,wBAAc;AAAA,QACxB,WAAmB,MAAM,OAAO,GAAG;AACzB,wBAAc;AAAA,QACxB;AACQ;AAAA;AAAA,UAGE,sBAAM,cAAc,OAAO;AAAA,YACzB,KAAK,SAAS,OAAO,MAAM,YAAY,GAAG,EAAE,OAAO,MAAM,UAAU,GAAG,EAAE,OAAO,MAAM,UAAU,GAAG,EAAE,OAAO,CAAC;AAAA,UACxH,GAAa,aAAaA,KAAI,oBAAoB,WAAW,WAAW,MAAM,GAAGA,KAAI,gBAAgB,OAAO,YAAY,kBAAkB,OAAO,WAAW,CAAC,CAAC;AAAA;AAAA,MAE9J,CAAO;AACD,aAAoB,sBAAM,cAAc,OAAO;AAAA,QAC7C,WAAW;AAAA,MACZ,GAAE,MAAM;AAAA,IACf;AAAA,EACA,GAAK;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,wBAAwB,SAAS;AAC/C,UAAI,SAAS;AACb,UAAI,eAAe,KAAK,OACtB,cAAc,aAAa,aAC3B,cAAc,aAAa,aAC3B,oBAAoB,aAAa;AACnC,aAAO,QAAQ,IAAI,SAAU,OAAO,GAAG;AACrC,aAAK,UAAU,QAAQ,UAAU,SAAS,SAAS,MAAM,gBAAgB,MAAM,UAAU,QAAQ,UAAU,SAAS,SAAS,MAAM,cAAc,KAAK,QAAQ,WAAW,EAAG,QAAO;AACnL,YAAI,WAAW,OAAO,cAAc,CAAC;AACrC,YAAI,gBAAgB,qBAAqB,OAAO,eAAgB,IAAG,oBAAoB;AACvF,YAAI,gBAAgB,WAAW,cAAc;AAC7C,YAAI,cAAc,cAAc,cAAc,CAAA,GAAI,KAAK,GAAG,IAAI;AAAA,UAC5D,QAAQ,cAAc,MAAM,OAAO,MAAM;AAAA,UACzC,UAAU;AAAA,QACpB,CAAS;AACD,eAAoB,sBAAM,cAAc,OAAO,SAAS;AAAA,UACtD,KAAK,SAAS,IAAI,MAAM;AACtB,gBAAI,QAAQ,CAAC,OAAO,WAAW,SAAS,IAAI,GAAG;AAC7C,qBAAO,WAAW,KAAK,IAAI;AAAA,YACzC;AAAA,UACW;AAAA,UACD,UAAU;AAAA,UACV,WAAW;AAAA,QACZ,GAAE,mBAAmB,OAAO,OAAO,OAAO,CAAC,GAAG;AAAA;AAAA,UAE7C,KAAK,UAAU,OAAO,UAAU,QAAQ,UAAU,SAAS,SAAS,MAAM,YAAY,GAAG,EAAE,OAAO,UAAU,QAAQ,UAAU,SAAS,SAAS,MAAM,UAAU,GAAG,EAAE,OAAO,MAAM,UAAU,GAAG,EAAE,OAAO,CAAC;AAAA,QAC1M,CAAA,GAAgB,sBAAM,cAAc,OAAO,SAAS;AAAA,UACnD,QAAQ;AAAA,UACR;AAAA,UACA,WAAW;AAAA,QACrB,GAAW,WAAW,CAAC,CAAC;AAAA,MACxB,CAAO;AAAA,IACP;AAAA,EACA,GAAK;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,6BAA6B;AAC3C,UAAI,SAAS;AACb,UAAI,eAAe,KAAK,OACtB,UAAU,aAAa,SACvB,oBAAoB,aAAa,mBACjC,iBAAiB,aAAa,gBAC9B,oBAAoB,aAAa,mBACjC,kBAAkB,aAAa,iBAC/B,cAAc,aAAa;AAC7B,UAAI,cAAc,KAAK,OACrB,cAAc,YAAY,aAC1B,wBAAwB,YAAY;AACtC,aAAoB,sBAAM,cAAc,SAAS;AAAA,QAC/C,OAAO;AAAA,QACP,UAAU;AAAA,QACV,UAAU;AAAA,QACV,QAAQ;AAAA,QACR,MAAM;AAAA,UACJ,GAAG;AAAA,QACJ;AAAA,QACD,IAAI;AAAA,UACF,GAAG;AAAA,QACJ;AAAA,QACD,KAAK,OAAO,OAAO,aAAa,GAAG,EAAE,OAAO,qBAAqB;AAAA,QACjE,kBAAkB,KAAK;AAAA,QACvB,gBAAgB,KAAK;AAAA,MACtB,GAAE,SAAU,OAAO;AAClB,YAAI,IAAI,MAAM;AACd,YAAI,WAAW,CAAE;AACjB,YAAI,QAAQ,WAAW,QAAQ,CAAC;AAChC,YAAI,WAAW,MAAM;AACrB,gBAAQ,QAAQ,SAAU,OAAO,OAAO;AACtC,cAAI,OAAO,eAAe,YAAY,KAAK;AAC3C,cAAI,eAAe,QAAQ,IAAI,IAAI,OAAO,gBAAgB,CAAC,IAAI;AAC/D,cAAI,MAAM;AACR,gBAAI,UAAU,kBAAkB,KAAK,WAAW,KAAK,YAAY,MAAM,WAAW,MAAM,UAAU;AAClG,gBAAI,SAAS,cAAc,cAAc,CAAA,GAAI,KAAK,GAAG,IAAI;AAAA,cACvD,YAAY,WAAW;AAAA,cACvB,UAAU,WAAW,QAAQ,CAAC,IAAI;AAAA,YAChD,CAAa;AACD,qBAAS,KAAK,MAAM;AACpB,uBAAW,OAAO;AAAA,UAC9B,OAAiB;AACL,gBAAI,WAAW,MAAM,UACnB,aAAa,MAAM;AACrB,gBAAI,oBAAoB,kBAAkB,GAAG,WAAW,UAAU;AAClE,gBAAI,aAAa,kBAAkB,CAAC;AACpC,gBAAI,UAAU,cAAc,cAAc,CAAA,GAAI,KAAK,GAAG,IAAI;AAAA,cACxD,YAAY,WAAW;AAAA,cACvB,UAAU,WAAW,aAAa;AAAA,YAChD,CAAa;AACD,qBAAS,KAAK,OAAO;AACrB,uBAAW,QAAQ;AAAA,UAC/B;AAAA,QACA,CAAS;AACD,eAAoB,sBAAM,cAAc,OAAO,MAAM,OAAO,wBAAwB,QAAQ,CAAC;AAAA,MACrG,CAAO;AAAA,IACP;AAAA,EACA,GAAK;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,uBAAuB,QAAQ;AAC7C,UAAI,SAAS;AAEb,aAAO,YAAY,SAAU,GAAG;AAC9B,YAAI,CAAC,EAAE,QAAQ;AACb,kBAAQ,EAAE,KAAG;AAAA,YACX,KAAK,aACH;AACE,kBAAI,OAAO,EAAE,OAAO,MAAM,gBAAgB,OAAO,WAAW;AAC5D,qBAAO,WAAW,IAAI,EAAE,MAAO;AAC/B,qBAAO,SAAS;AAAA,gBACd,eAAe;AAAA,cACjC,CAAiB;AACD;AAAA,YAChB;AAAA,YACY,KAAK,cACH;AACE,kBAAI,QAAQ,EAAE,OAAO,MAAM,gBAAgB,IAAI,OAAO,WAAW,SAAS,IAAI,OAAO,MAAM,gBAAgB,OAAO,WAAW;AAC7H,qBAAO,WAAW,KAAK,EAAE,MAAO;AAChC,qBAAO,SAAS;AAAA,gBACd,eAAe;AAAA,cACjC,CAAiB;AACD;AAAA,YAChB;AAAA,YACY,KAAK,UACH;AACE,qBAAO,WAAW,OAAO,MAAM,aAAa,EAAE,KAAM;AACpD,qBAAO,SAAS;AAAA,gBACd,eAAe;AAAA,cACjC,CAAiB;AACD;AAAA,YAChB;AAAA,UAKA;AAAA,QACA;AAAA,MACO;AAAA,IACP;AAAA,EACA,GAAK;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,gBAAgB;AAC9B,UAAI,eAAe,KAAK,OACtB,UAAU,aAAa,SACvB,oBAAoB,aAAa;AACnC,UAAI,cAAc,KAAK,MAAM;AAC7B,UAAI,qBAAqB,WAAW,QAAQ,WAAW,CAAC,eAAe,CAAC,QAAQ,aAAa,OAAO,IAAI;AACtG,eAAO,KAAK,2BAA4B;AAAA,MAChD;AACM,aAAO,KAAK,wBAAwB,OAAO;AAAA,IACjD;AAAA,EACA,GAAK;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,oBAAoB;AAClC,UAAI,KAAK,QAAQ;AACf,aAAK,uBAAuB,KAAK,MAAM;AAAA,MAC/C;AAAA,IACA;AAAA,EACA,GAAK;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,SAAS;AACvB,UAAI,SAAS;AACb,UAAI,eAAe,KAAK,OACtB,OAAO,aAAa,MACpB,UAAU,aAAa,SACvB,YAAY,aAAa,WACzB,QAAQ,aAAa,OACrB,KAAK,aAAa,IAClB,KAAK,aAAa,IAClB,cAAc,aAAa,aAC3B,cAAc,aAAa,aAC3B,oBAAoB,aAAa;AACnC,UAAI,sBAAsB,KAAK,MAAM;AACrC,UAAI,QAAQ,CAAC,WAAW,CAAC,QAAQ,UAAU,CAAC,SAAS,EAAE,KAAK,CAAC,SAAS,EAAE,KAAK,CAAC,SAAS,WAAW,KAAK,CAAC,SAAS,WAAW,GAAG;AAC7H,eAAO;AAAA,MACf;AACM,UAAI,aAAa,KAAK,gBAAgB,SAAS;AAC/C,aAAoB,sBAAM,cAAc,OAAO;AAAA,QAC7C,UAAU,KAAK,MAAM;AAAA,QACrB,WAAW;AAAA,QACX,KAAK,SAAS,IAAI,OAAO;AACvB,iBAAO,SAAS;AAAA,QAC1B;AAAA,MACO,GAAE,KAAK,iBAAiB,SAAS,KAAK,aAAa,OAAO,GAAG,MAAM,mBAAmB,KAAK,OAAO,MAAM,KAAK,IAAI,CAAC,qBAAqB,wBAAwB,UAAU,mBAAmB,KAAK,OAAO,SAAS,KAAK,CAAC;AAAA,IAC9N;AAAA,EACG,CAAA,GAAG,CAAC;AAAA,IACH,KAAK;AAAA,IACL,OAAO,SAAS,yBAAyB,WAAW,WAAW;AAC7D,UAAI,UAAU,0BAA0B,UAAU,mBAAmB;AACnE,eAAO;AAAA,UACL,uBAAuB,UAAU;AAAA,UACjC,iBAAiB,UAAU;AAAA,UAC3B,YAAY,UAAU;AAAA,UACtB,aAAa,CAAE;AAAA,UACf,qBAAqB;AAAA,QACtB;AAAA,MACT;AACM,UAAI,UAAU,qBAAqB,UAAU,gBAAgB,UAAU,iBAAiB;AACtF,eAAO;AAAA,UACL,iBAAiB,UAAU;AAAA,UAC3B,YAAY,UAAU;AAAA,UACtB,aAAa,UAAU;AAAA,UACvB,qBAAqB;AAAA,QACtB;AAAA,MACT;AACM,UAAI,UAAU,YAAY,UAAU,YAAY;AAC9C,eAAO;AAAA,UACL,YAAY,UAAU;AAAA,UACtB,qBAAqB;AAAA,QACtB;AAAA,MACT;AACM,aAAO;AAAA,IACb;AAAA,EACA,GAAK;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,cAAc,GAAG,IAAI;AACnC,UAAI,IAAI,IAAI;AACV,eAAO;AAAA,MACf;AACM,UAAI,IAAI,IAAI;AACV,eAAO;AAAA,MACf;AACM,aAAO;AAAA,IACb;AAAA,EACA,GAAK;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,oBAAoB,QAAQ,OAAO,KAAK;AACtD,UAAkB,sBAAM,eAAe,MAAM,GAAG;AAC9C,eAAoB,sBAAM,aAAa,QAAQ,KAAK;AAAA,MAC5D;AACM,UAAI,WAAW,MAAM,GAAG;AACtB,eAAO,OAAO,KAAK;AAAA,MAC3B;AACM,UAAI,YAAY,KAAK,2BAA2B,OAAO,WAAW,YAAY,OAAO,YAAY,EAAE;AACnG,aAAoB,sBAAM,cAAc,OAAO,SAAS,CAAA,GAAI,OAAO;AAAA,QACjE;AAAA,QACA,MAAM;AAAA,QACN;AAAA,MACR,CAAO,CAAC;AAAA,IACR;AAAA,EACA,GAAK;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,gBAAgB,QAAQ,OAAO,OAAO;AACpD,UAAkB,sBAAM,eAAe,MAAM,GAAG;AAC9C,eAAoB,sBAAM,aAAa,QAAQ,KAAK;AAAA,MAC5D;AACM,UAAI,QAAQ;AACZ,UAAI,WAAW,MAAM,GAAG;AACtB,gBAAQ,OAAO,KAAK;AACpB,YAAkB,sBAAM,eAAe,KAAK,GAAG;AAC7C,iBAAO;AAAA,QACjB;AAAA,MACA;AACM,UAAI,YAAY,KAAK,2BAA2B,OAAO,WAAW,aAAa,CAAC,WAAW,MAAM,IAAI,OAAO,YAAY,EAAE;AAC1H,aAAoB,sBAAM,cAAc,MAAM,SAAS,CAAA,GAAI,OAAO;AAAA,QAChE,mBAAmB;AAAA,QACnB;AAAA,MACD,CAAA,GAAG,KAAK;AAAA,IACf;AAAA,EACA,CAAG,CAAC;AACJ,EAAEF,0BAAa;AACf,OAAO;AACP,gBAAgB,KAAK,eAAe,KAAK;AACzC,gBAAgB,KAAK,gBAAgB;AAAA,EACnC,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,YAAY;AAAA,EACZ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,aAAa;AAAA,EACb,aAAa;AAAA,EACb,cAAc;AAAA,EACd,WAAW;AAAA,EACX,MAAM;AAAA,EACN,UAAU;AAAA,EACV,mBAAmB,CAAC,OAAO;AAAA,EAC3B,gBAAgB;AAAA,EAChB,mBAAmB;AAAA,EACnB,iBAAiB;AAAA,EACjB,SAAS;AAAA,EACT,aAAa;AAAA,EACb,cAAc;AAChB,CAAC;AACD,gBAAgB,KAAK,mBAAmB,SAAU,YAAY,UAAU;AACtE,MAAI,OAAO,SAAS,WAAW,UAAU;AACzC,MAAI,aAAa,KAAK,IAAI,KAAK,IAAI,WAAW,UAAU,GAAG,GAAG;AAC9D,SAAO,OAAO;AAChB,CAAC;AACD,gBAAgB,KAAK,kBAAkB,SAAU,WAAW;AAC1D,MAAI,OAAO,UAAU,MACnB,WAAW,UAAU;AACvB,MAAI,oBAAoB,YAAY,WAAW,KAAK;AACpD,MAAI,QAAQ,cAAc,UAAU,IAAI;AACxC,MAAI,QAAQ,KAAK,QAAQ;AACvB,WAAO,KAAK,IAAI,SAAU,OAAO,OAAO;AACtC,aAAO,cAAc,cAAc,cAAc;AAAA,QAC/C,SAAS;AAAA,MACV,GAAE,iBAAiB,GAAG,KAAK,GAAG,SAAS,MAAM,KAAK,KAAK,MAAM,KAAK,EAAE,KAAK;AAAA,IAChF,CAAK;AAAA,EACL;AACE,MAAI,SAAS,MAAM,QAAQ;AACzB,WAAO,MAAM,IAAI,SAAU,MAAM;AAC/B,aAAO,cAAc,cAAc,CAAA,GAAI,iBAAiB,GAAG,KAAK,KAAK;AAAA,IAC3E,CAAK;AAAA,EACL;AACE,SAAO,CAAE;AACX,CAAC;AACD,gBAAgB,KAAK,wBAAwB,SAAU,WAAW,QAAQ;AACxE,MAAI,MAAM,OAAO,KACf,OAAO,OAAO,MACd,QAAQ,OAAO,OACf,SAAS,OAAO;AAClB,MAAI,eAAe,aAAa,OAAO,MAAM;AAC7C,MAAI,KAAK,OAAO,gBAAgB,UAAU,IAAI,OAAO,QAAQ,CAAC;AAC9D,MAAI,KAAK,MAAM,gBAAgB,UAAU,IAAI,QAAQ,SAAS,CAAC;AAC/D,MAAI,cAAc,gBAAgB,UAAU,aAAa,cAAc,CAAC;AACxE,MAAI,cAAc,gBAAgB,UAAU,aAAa,cAAc,eAAe,GAAG;AACzF,MAAI,YAAY,UAAU,aAAa,KAAK,KAAK,QAAQ,QAAQ,SAAS,MAAM,IAAI;AACpF,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACD;AACH,CAAC;AACD,gBAAgB,KAAK,mBAAmB,SAAU,OAAO;AACvD,MAAI,OAAO,MAAM,MACf,SAAS,MAAM;AACjB,MAAI,YAAY,KAAK,KAAK,iBAAiB,SAAY,cAAc,cAAc,CAAE,GAAE,KAAK,KAAK,YAAY,GAAG,KAAK,KAAK,IAAI,KAAK;AACnI,MAAI,UAAU,KAAK,eAAe,SAAS;AAC3C,MAAI,CAAC,WAAW,CAAC,QAAQ,QAAQ;AAC/B,WAAO;AAAA,EACX;AACE,MAAI,eAAe,UAAU,cAC3B,aAAa,UAAU,YACvB,WAAW,UAAU,UACrB,eAAe,UAAU,cACzB,UAAU,UAAU,SACpB,UAAU,UAAU,SACpB,WAAW,UAAU,UACrB,cAAc,UAAU;AAC1B,MAAI,WAAW,KAAK,IAAI,UAAU,QAAQ;AAC1C,MAAI,aAAa,KAAK,qBAAqB,WAAW,MAAM;AAC5D,MAAI,aAAa,KAAK,gBAAgB,YAAY,QAAQ;AAC1D,MAAI,gBAAgB,KAAK,IAAI,UAAU;AACvC,MAAI,cAAc;AAClB,MAAI,MAAM,OAAO,KAAK,MAAM,QAAQ,GAAG;AACrC,SAAK,OAAO,oGAAwG;AACpH,kBAAc;AAAA,EAClB,WAAa,MAAM,OAAO,GAAG;AACzB,SAAK,OAAO,oGAAwG;AACpH,kBAAc;AAAA,EAClB;AACE,MAAI,mBAAmB,QAAQ,OAAO,SAAU,OAAO;AACrD,WAAO,kBAAkB,OAAO,aAAa,CAAC,MAAM;AAAA,EACrD,CAAA,EAAE;AACH,MAAI,oBAAoB,iBAAiB,MAAM,mBAAmB,mBAAmB,KAAK;AAC1F,MAAI,iBAAiB,gBAAgB,mBAAmB,WAAW;AACnE,MAAI,MAAM,QAAQ,OAAO,SAAU,QAAQ,OAAO;AAChD,QAAI,MAAM,kBAAkB,OAAO,aAAa,CAAC;AACjD,WAAO,UAAU,SAAS,GAAG,IAAI,MAAM;AAAA,EACxC,GAAE,CAAC;AACJ,MAAI;AACJ,MAAI,MAAM,GAAG;AACX,QAAI;AACJ,cAAU,QAAQ,IAAI,SAAU,OAAO,GAAG;AACxC,UAAI,MAAM,kBAAkB,OAAO,aAAa,CAAC;AACjD,UAAI,OAAO,kBAAkB,OAAO,SAAS,CAAC;AAC9C,UAAI,WAAW,SAAS,GAAG,IAAI,MAAM,KAAK;AAC1C,UAAI;AACJ,UAAI,GAAG;AACL,yBAAiB,KAAK,WAAW,SAAS,UAAU,IAAI,gBAAgB,QAAQ,IAAI,IAAI;AAAA,MAChG,OAAa;AACL,yBAAiB;AAAA,MACzB;AACM,UAAI,eAAe,iBAAiB,SAAS,UAAU,MAAM,QAAQ,IAAI,WAAW,KAAK,UAAU;AACnG,UAAI,YAAY,iBAAiB,gBAAgB;AACjD,UAAI,gBAAgB,WAAW,cAAc,WAAW,eAAe;AACvE,UAAI,iBAAiB,CAAC;AAAA,QACpB;AAAA,QACA,OAAO;AAAA,QACP,SAAS;AAAA,QACT,SAAS;AAAA,QACT,MAAM;AAAA,MACd,CAAO;AACD,UAAI,kBAAkB,iBAAiB,WAAW,IAAI,WAAW,IAAI,cAAc,QAAQ;AAC3F,aAAO,cAAc,cAAc,cAAc;AAAA,QAC/C;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACD,GAAE,KAAK,GAAG,UAAU,GAAG,IAAI;AAAA,QAC1B,OAAO,kBAAkB,OAAO,WAAW;AAAA,QAC3C,YAAY;AAAA,QACZ,UAAU;AAAA,QACV,SAAS;AAAA,QACT,cAAc,SAAS,UAAU,IAAI;AAAA,MAC7C,CAAO;AACD,aAAO;AAAA,IACb,CAAK;AAAA,EACL;AACE,SAAO,cAAc,cAAc,CAAE,GAAE,UAAU,GAAG,CAAA,GAAI;AAAA,IACtD;AAAA,IACA,MAAM;AAAA,EACV,CAAG;AACH,CAAC;AChiBM,IAAI,WAAW,yBAAyB;AAAA,EAC7C,WAAW;AAAA,EACX,gBAAgB;AAAA,EAChB,2BAA2B,CAAC,MAAM;AAAA,EAClC,yBAAyB;AAAA,EACzB,eAAe;AAAA,EACf,gBAAgB,CAAC;AAAA,IACf,UAAU;AAAA,IACV,UAAU;AAAA,EACd,GAAK;AAAA,IACD,UAAU;AAAA,IACV,UAAU;AAAA,EACd,CAAG;AAAA,EACD;AAAA,EACA,cAAc;AAAA,IACZ,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,UAAU;AAAA,IACV,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,aAAa;AAAA,IACb,aAAa;AAAA,EACjB;AACA,CAAC;ACAD,MAAMG,SAAS,CAAC,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,SAAS;AAqH5H,SAAwBC,gBAAgB;AAGpC,QAAM;AAAA,IAAEC;AAAAA,IAAMC;AAAAA,IAAkBC;AAAAA,IAAmBC;AAAAA,MAAsBC,cAA0B;AAEnG,QAAMC,UAAUC,WAAuB;AACvC,QAAM,CAACC,cAAcC,eAAe,IAAIC,aAAAA,SAAsCT,IAAI;AAClF,QAAM,CAACU,gBAAgBC,mBAAmB,IAAIF,aAAAA,SAA2BR,gBAAgB;AACzF,QAAM,CAACW,gBAAgBC,iBAAiB,IAAIJ,aAAAA,SAAsBP,iBAAiB;AACnF,QAAM,CAACY,qBAAqBC,sBAAsB,IAAIN,aAAAA,SAAyBN,iBAAiB;AAChG,QAAM,CAACa,mBAAmBC,oBAAoB,IAAIR,sBAAgC;AAGlFS,eAAAA,UAAU,MAAM;AACZ,QAAIb,QAAQL,MAAM;AACEQ,sBAAAH,QAAQL,KAAKA,IAAI;AACVe,6BAAAV,QAAQL,KAAKG,iBAAiB;AACnCU,wBAAAR,QAAQL,KAAKE,iBAAiB;AAChDiB,cAAQC,IAAIf,QAAQL,KAAKE,mBAAmB,aAAa;AACrCS,0BAAAN,QAAQL,KAAKC,gBAAgB;AAAA,IACrD;AAAA,EAEJ,GAAG,CAACI,QAAQL,IAAI,CAAC;AAIjB,QAAMqB,qBAAqBA,CAACrB,QAAoB,OAAO;AACnD,QAAI,CAACA,SAAQA,MAAKsB,WAAW,UAAU,CAAC;AACxC,UAAMC,aAAa,CAAC,GAAGvB,KAAI,EAAEwB,KAAK,CAACC,GAAGC,MAAMA,EAAEC,cAAcF,EAAEE,WAAW;AACzE,UAAMC,UAAUL,WAAWM,MAAM,GAAG,CAAC;AAC/B,UAAAC,SAASP,WAAWM,MAAM,CAAC,EAAEE,OAAO,CAACC,KAAKC,SAAS;AACrDD,UAAIL,eAAeM,KAAKN;AACjB,aAAAK;AAAAA,IACX,GAAG;AAAA,MAAEE,UAAU;AAAA,MAAUP,aAAa;AAAA,IAAE,CAAC;AACzC,UAAMQ,mBAAmB,CAAC,GAAGP,SAASE,MAAM;AAC5C,UAAMM,WAAW,CAAC,GAAGD,gBAAgB,EAAEX,KAAK,CAACC,GAAGC,MAAMA,EAAEC,cAAcF,EAAEE,WAAW;AAEnF,UAAMU,SAAsB,CAAC;AAC7B,QAAIC,OAAO;AACP,QAAAC,QAAQH,SAASd,SAAS;AAE9B,WAAOgB,QAAQC,OAAO;AAClB,UAAID,SAASC,OAAO;AACTF,eAAAG,KAAKJ,SAASE,IAAI,CAAC;AAAA,MAC9B,OAAO;AACHD,eAAOG,KAAKJ,SAASE,IAAI,GAAGF,SAASG,KAAK,CAAC;AAAA,MAC/C;AACAD;AACAC;AAAAA,IACJ;AAEA,WAAOF,OAAOI,IAAI,CAACC,MAAMC,WAAW;AAAA,MAChC,GAAGD;AAAAA,MACHE,OAAO9C,OAAO6C,QAAQ7C,OAAOwB,MAAM;AAAA,IACvC,EAAE;AAAA,EACN;AAEM,QAAAuB,cAAcA,CAACC,OAAeC,gBAAsB;AACtD9B,yBAAqB,MAAS;AACtBZ,YAAA2C,OACJ;AAAA,MACI/C,kBAAkB6C;AAAAA,MAClBC,aAAaA,YAAYE,YAAY;AAAA,MACrCC,QAAQ;AAAA,IACZ,GACA;AAAA,MAAEC,QAAQ;AAAA,IAAO,CACrB;AAAA,EACJ;AAIA,QAAMC,oBAAoBA,CAACC,WAAiBC,SAAeC,KAAaR,aAAmBS,uBAAkC;AAEzHrC,YAAQC,IAAI;AAESH,yBAAAuC,qBAAqBA,qBAAqB,MAAS;AAChEnD,YAAA2C,OACJ;AAAA,MACIK,WAAWA,UAAUJ,YAAY;AAAA,MACjCK,SAASA,QAAQL,YAAY;AAAA,MAC7BC,QAAQ;AAAA,MACRjD,kBAAkBsD;AAAAA,MAClBR,aAAaA,YAAYE,YAAY;AAAA,IACzC,GACA;AAAA,MAAEE,QAAQ;AAAA,IAAO,CACrB;AAAA,EAEJ;AAEM,QAAAxB,cAAc8B,MAAMC,QAAQ9C,cAAc,KAAKA,eAAeU,SAAS,KAAKV,eAAe6B,IAAKkB,OAAMA,EAAEhC,WAAW,EAAEI,OAAO,CAACC,KAAK4B,QAAQ5B,MAAM4B,KAAK,CAAC;AAEtJ,QAAAC,qBAAqBJ,MAAMC,QAAQ5C,mBAAmB,KAAKA,oBAAoBQ,SAAS,KAAKR,oBAAoB2B,IAAKkB,OAAMA,EAAEG,aAAa,EAAE/B,OAAO,CAACC,KAAK4B,QAAQ5B,MAAM4B,KAAK,CAAC;AAC9K,QAAAG,mBAAmBN,MAAMC,QAAQ5C,mBAAmB,KAAKA,oBAAoBQ,SAAS,KAAKR,oBAAoB2B,IAAKkB,OAAMA,EAAEK,kBAAkB,EAAEjC,OAAO,CAACC,KAAK4B,QAAQ5B,MAAM4B,KAAK,CAAC;AACvL;AACIvD,YAAQ4D,UAAU,UACXC,kCAAAA,IAACC;MAAcC,MAAM;AAAA,MAAGC,SAAS;AAAA,IAAM,CAAA;AAAA,EAClD;AAsBA,QAAMC,kBAAkBA,MAAM;AAC1B,UAAM,CAACC,aAAaC,cAAc,IAAI/D,aAAAA,SAAS,IAAI;AAEnDS,iBAAAA,UAAU,MAAM;AACZ,YAAMuD,cAAcA,MAAMD,eAAeE,OAAOC,UAAU;AAC9CF,kBAAA;AACLC,aAAAE,iBAAiB,UAAUH,WAAW;AAC7C,aAAO,MAAMC,OAAOG,oBAAoB,UAAUJ,WAAW;AAAA,IACjE,GAAG,EAAE;AAEL,kDACKK,MACG;AAAA,MAAAC,UAAA,CAAAb,kCAAA,IAACc,YACG;AAAA,QAAAD,UAAAb,kCAAA,IAACe,WAAU;AAAA,UAAAF,UAAA;AAAA,QAAO,CAAA;AAAA,MACtB,CAAA,GACCb,kCAAA,IAAAgB,aAAA;AAAA,QACGH,UAACI,kCAAA,KAAA,OAAA;AAAA,UAAIC,WAAU;AAAA,UACXL,UAAA,CAAAb,kCAAA,IAACmB;YAAoBC,OAAM;AAAA,YAAOC,QAAO;AAAA,YACrCR,gDAACS,UACG;AAAA,cAAAT,UAAAb,kCAAA,IAACrE,KAAA;AAAA,gBACGG,MAAMqB,mBAAmBoC,MAAMC,QAAQ9C,cAAc,IAAIA,iBAAiB,EAAE;AAAA,gBAC5E6E,IAAG;AAAA,gBACHC,IAAG;AAAA,gBACHC,aAAa;AAAA,gBACbC,aAAa;AAAA,gBACbC,MAAK;AAAA,gBACLC,cAAc;AAAA,gBACdC,SAAQ;AAAA,gBACRC,WAAW;AAAA,gBACXC,OAAQC,WAAU;AACR,wBAAA;AAAA,oBACFT;AAAAA,oBAAIC;AAAAA,oBAAIS;AAAAA,oBAAUP;AAAAA,oBAAaC;AAAAA,oBAAMO;AAAAA,oBAAStD;AAAAA,kBAClD,IAAIoD;AACE,wBAAAG,UAASC,KAAKC,KAAK;AACzB,wBAAMC,MAAMF,KAAKE,IAAI,CAACH,UAASF,QAAQ;AACvC,wBAAMM,MAAMH,KAAKG,IAAI,CAACJ,UAASF,QAAQ;AACjC,wBAAAO,KAAKjB,MAAMG,cAAc,MAAMa;AAC/B,wBAAAE,KAAKjB,MAAME,cAAc,MAAMY;AAC/B,wBAAAI,KAAKnB,MAAMG,cAAc,MAAMa;AAC/B,wBAAAI,KAAKnB,MAAME,cAAc,MAAMY;AACrC,wBAAMM,KAAKF,MAAMH,OAAO,IAAI,IAAI,MAAM;AACtC,wBAAMM,KAAKF;AACL,wBAAAG,aAAaP,OAAO,IAAI,UAAU;AAClC,wBAAAQ,cAAajG,uDAAmBkG,aAAY;AAClD,wBAAMC,aAAaC,YAAatE,QAAQmE,aAAc,KAAKI,QAAQ,CAAC,CAAC;AAC/D,wBAAAC,WAAWA,CAACC,KAAaC,cAAsB;AAC1C,2BAAAD,IAAIjG,SAASkG,YAAY,GAAGD,IAAI1F,MAAM,GAAG2F,SAAS,CAAC,QAAQD;AAAAA,kBACtE;AAEI,sBAAAJ,aAAa,EAAU,QAAA;AAC3B,gEACK,KACG;AAAA,oBAAApC,UAAA,CAAAb,kCAAA,IAAC;sBAAKuD,GAAG,IAAIf,EAAE,IAAIC,EAAE,IAAIC,EAAE,IAAIC,EAAE,IAAIC,EAAE,IAAIC,EAAE;AAAA,sBAAIW,QAAQ7B;AAAAA,sBAAMA,MAAK;AAAA,oBAAO,CAAA,GAC3E3B,kCAAA,IAAC,UAAO;AAAA,sBAAAuB,IAAIqB;AAAAA,sBAAIpB,IAAIqB;AAAAA,sBAAIxI,GAAG;AAAA,sBAAGsH;AAAAA,sBAAY6B,QAAO;AAAA,oBAAO,CAAA,GACxDxD,kCAAA,IAAC;sBAAKP,GAAGmD,MAAML,OAAO,IAAI,IAAI,MAAM;AAAA,sBAAIkB,GAAGZ;AAAAA,sBAAIC;AAAAA,sBAAwBnB,MAAK;AAAA,sBACvEd,UAAG,GAAAuC,SAASlB,QAAQlE,UAAU,EAAE,CAAC;AAAA,oBACtC,CAAA,GACAgC,kCAAA,IAAC;sBAAKP,GAAGmD,MAAML,OAAO,IAAI,IAAI,MAAM;AAAA,sBAAIkB,GAAGZ;AAAAA,sBAAIa,IAAI;AAAA,sBAAIZ;AAAAA,sBAAwBnB,MAAK;AAAA,sBAC/Ed,UAAA,GAAG8C,aAAa/E,KAAK,CAAC,OAAOqE,UAAU;AAAA,oBAC5C,CAAA,CAAA;AAAA,kBACJ,CAAA;AAAA,gBAER;AAAA,gBAECpC,UAAA1D,mBAAmBoC,MAAMC,QAAQ9C,cAAc,IAAIA,iBAAiB,EAAE,EAAE6B,IAAI,CAACqF,OAAOnF,gDAChFoF,MAA2B;AAAA,kBAAAlC,MAAMiC,MAAMlF;AAAAA,gBAA7B,GAAA,QAAQD,KAAK,EAAuB,CAClD;AAAA,cACL,CAAA;AAAA,YACJ,CAAA;AAAA,UACJ,CAAA,GACAwC,kCAAA,KAAC,OAAI;AAAA,YAAAC,WAAU;AAAA,YACXL,UAAA,CAACb,kCAAA,IAAA,KAAA;AAAA,cAAEkB,WAAU;AAAA,cACRL,UACL;AAAA,YAAA,CAAA,GACAb,kCAAA,IAAC,KAAE;AAAA,cAAAkB,WAAU;AAAA,cACRL,UAAA8C,cAAa7G,uDAAmBkG,YAAW,IAAIlG,uDAAmBkG,WAAWvF,WAAW;AAAA,YAC7F,CAAA,CAAA;AAAA,UACJ,CAAA,CAAA;AAAA,QACJ,CAAA;AAAA,MACJ,CAAA,CAAA;AAAA,IACJ,CAAA;AAAA,EAER;AAGA,+CACK,OAAI;AAAA,IAAAyD,WAAU;AAAA,IAEXL,UAACI,kCAAA,KAAA,OAAA;AAAA,MAAIC,WAAU;AAAA,MACXL,UAAA,CAACb,kCAAA,IAAA8D,mBAAA;AAAA,QAAkBhI,MAAMO,gBAAgB;QAAIsC;AAAAA,QAA0B5C,kBAAkBS;AAAAA,QAAgB0C;AAAAA,MAAsC,CAAA,GAC/I+B,kCAAA,KAAC,OAAI;AAAA,QAAAC,WAAU;AAAA,QACXL,UAAA,CAAAb,kCAAAA,IAACI,iBAAgB,CAAA,CAAA,0CAChBQ,MACG;AAAA,UAAAC,UAAA,CAAAb,kCAAA,IAACc,YACG;AAAA,YAAAD,UAAAb,kCAAA,IAACe,WAAU;AAAA,cAAAF,UAAA;AAAA,YAAM,CAAA;AAAA,UACrB,CAAA,0CACCG,aACG;AAAA,YAAAH,UAAA,CAAAb,kCAAA,IAAC+D,YAAW;AAAA,cAAA7C,WAAU;AAAA,cAClBL,UAAAI,kCAAA,KAAC+C,OACG;AAAA,gBAAAnD,UAAA,CAACb,kCAAA,IAAAiE,aAAA;AAAA,kBACGpD,iDAACqD,UACG;AAAA,oBAAArD,UAAA,CAAAb,kCAAA,IAACmE;sBAAUtD,UAAa;AAAA,oBAAA,CAAA,GACxBb,kCAAA,IAACmE;sBAAUtD,UAAK;AAAA,oBAAA,CAAA,GAChBb,kCAAA,IAACmE;sBAAUtD,UAAM;AAAA,oBAAA,CAAA,CAAA;AAAA,kBACrB,CAAA;AAAA,gBACJ,CAAA,GACCb,kCAAA,IAAAoE,WAAA;AAAA,kBACIvD,UAAMtB,MAAAC,QAAQ5C,mBAAmB,KAAKA,oBAAoBQ,SAAS,IAChER,oBAAoB2B,IAAKkB,8CACpByE,UACG;AAAA,oBAAArD,UAAA,CAAAb,kCAAA,IAACqE,WAAU;AAAA,sBAAAnD,WAAU;AAAA,sBAAeL,UAAApB,EAAE6E;AAAAA,oBAAU,CAAA,GAChDtE,kCAAA,IAACqE,WAAW;AAAA,sBAAAxD,UAAApB,EAAE8E;AAAAA,oBAAU,CAAA,GACvBvE,kCAAA,IAAAqE,WAAA;AAAA,sBAAWxD,UAAa8C,aAAAlE,EAAEhC,WAAW;AAAA,oBAAE,CAAA,CAAA;AAAA,kBAAA,GAH7BgC,EAAE+E,OAIjB,CACH,0CAEAN,UACG;AAAA,oBAAArD,UAAAb,kCAAA,IAACqE,WAAU;AAAA,sBAAAI,SAAS;AAAA,sBAAGvD,WAAU;AAAA,sBAAcL,UAAA;AAAA,oBAE/C,CAAA;AAAA,kBACJ,CAAA;AAAA,gBAER,CAAA,CAAA;AAAA,cACJ,CAAA;AAAA,YACJ,CAAA,GACAb,kCAAA,IAAC,OAAI;AAAA,cAAAkB,WAAU;AAAA,cACXL,UAAAb,kCAAA,IAAC0E,MAAK;AAAA,gBAAAC,IAAG;AAAA,gBAAkBzD,WAAU;AAAA,gBAA+BL,UAAA;AAAA,cAEpE,CAAA;AAAA,YACJ,CAAA,CAAA;AAAA,UACJ,CAAA,CAAA;AAAA,QACJ,CAAA,CAAA;AAAA,MACJ,CAAA,GAEAI,kCAAA,KAAC,OAAI;AAAA,QAAAC,WAAU;AAAA,QACXL,UAAA,CAAAI,kCAAA,KAACL,MACG;AAAA,UAAAC,UAAA,CAACb,kCAAA,IAAAc,YAAA;AAAA,YAAWI,WAAU;AAAA,YAClBL,UAAAb,kCAAA,IAACe;cAAUG,WAAU;AAAA,cAAsBL;YAAO,CAAA;AAAA,UACtD,CAAA,0CACCG,aACG;AAAA,YAAAH,UAAA,CAACI,kCAAA,KAAA,OAAA;AAAA,cAAIC,WAAU;AAAA,cAAqBL,UAAA,CAAa8C,cAAA7G,uDAAmB8H,iBAAgB,CAAC,GAAE,GAAA;AAAA,YAAC,CAAA,GACxF3D,kCAAA,KAAC,KAAE;AAAA,cAAAC,WAAU;AAAA,cAA2BL,UAAA,CAAA,oBAAiBgE,gBAAe/H,uDAAmBgI,iBAAgB,CAAC,CAAA;AAAA,YAAE,CAAA,CAAA;AAAA,UAClH,CAAA,CAAA;AAAA,QACJ,CAAA,0CAEClE,MACG;AAAA,UAAAC,UAAA,CAAAb,kCAAA,IAACc;YACGD,UAACb,kCAAA,IAAAe,WAAA;AAAA,cAAUG,WAAU;AAAA,cAAsBL;YAAgB,CAAA;AAAA,UAC/D,CAAA,0CACCG,aACG;AAAA,YAAAH,UAAA,CAAAb,kCAAA,IAAC;cAAIkB,WAAU;AAAA,cACVL,UAAgBgE,eAAAlF,sBAAsB,CAAE;AAAA,YAC7C,CAAA,GACAsB,kCAAA,KAAC,KAAE;AAAA,cAAAC,WAAU;AAAA,cAAgCL,UAAA,CAAA,SACnChB,oBAAoB,GAAE,YAAA;AAAA,YAChC,CAAA,CAAA;AAAA,UACJ,CAAA,CAAA;AAAA,QAGJ,CAAA,0CACCe,MACG;AAAA,UAAAC,UAAA,CAAAb,kCAAA,IAACc;YACGD,UAACb,kCAAA,IAAAe,WAAA;AAAA,cAAUG,WAAU;AAAA,cAAsBL;YAAO,CAAA;AAAA,UACtD,CAAA,GACCb,kCAAA,IAAAgB,aAAA;AAAA,YACGH,UAACb,kCAAA,IAAA,OAAA;AAAA,cAAIkB,WAAU;AAAA,cACVL,UAAA;AAAA,gBACG;AAAA,kBAAEkE,OAAO;AAAA,kBAASC,MAAMC;AAAAA,kBAAQrG,OAAOlC,iDAAgBU;AAAAA,gBAAO;AAAA;AAAA,cAElE,EAAEmB,IAAI,CAACC,MAAMC,UACRwC,kCAAAA,KAAA,OAAA;AAAA,gBAAgBC,WAAU;AAAA,gBACvBL,UAAA,CAAAb,kCAAAA,IAACxB,KAAKwG,MAAL;AAAA,kBAAU9D,WAAU;AAAA,gBAA4B,CAAA,0CAChD,OACG;AAAA,kBAAAL,UAAA,CAAAb,kCAAA,IAAC,KAAE;AAAA,oBAAAkB,WAAU;AAAA,oBAAuBL,UAAArC,KAAKuG;AAAAA,kBAAM,CAAA,GAC9C/E,kCAAA,IAAA,KAAA;AAAA,oBAAEkB,WAAU;AAAA,oBAAqBL,eAAKjC;AAAAA,kBAAM,CAAA,CAAA;AAAA,gBACjD,CAAA,CAAA;AAAA,cALM,GAAAH,KAMV,CACH;AAAA,YACL,CAAA;AAAA,UACJ,CAAA,CAAA;AAAA,QACJ,CAAA,CAAA;AAAA,MACJ,CAAA,CAAA;AAAA,IACJ,CAAA;AAAA,EACJ,CAAA;AAER;", "x_google_ignoreList": [0, 1, 2, 3, 4, 5, 6, 7]}