{"version": 3, "file": "sellerSetting.coupons-BpL1CFRF.js", "sources": ["../../../app/routes/sellerSetting.coupons.tsx"], "sourcesContent": ["import { json, LoaderFunction } from \"@remix-run/node\";\r\nimport {\r\n  NavigateFunction,\r\n  useFetcher,\r\n  useLoaderData,\r\n  useNavigate,\r\n  useRevalidator,\r\n} from \"@remix-run/react\";\r\nimport { format, parseISO } from \"date-fns\";\r\nimport { Edit2Icon, Trash2Icon, SearchIcon, XCircleIcon } from \"lucide-react\";\r\nimport { useEffect, useState } from \"react\";\r\nimport { Button } from \"~/components/ui/button\";\r\nimport { Input } from \"~/components/ui/input\";\r\nimport { Separator } from \"~/components/ui/separator\";\r\nimport { useToast } from \"~/components/ui/ToastProvider\";\r\nimport { addCoupon, deleteCoupon, getCoupons } from \"~/services/coupons\";\r\nimport { CouponDetailsType } from \"~/types/api/businessConsoleService/coupons\";\r\nimport { withAuth, withResponse } from \"~/utils/auth-utils\";\r\nimport { camelCaseToWords } from \"~/utils/format\";\r\n\r\ninterface Loaderdata {\r\n  data: CouponDetailsType[];\r\n}\r\n\r\nexport const loader: LoaderFunction = withAuth(async ({ request }) => {\r\n  try {\r\n    const response = await getCoupons(request);\r\n    return withResponse({ data: response.data }, response.headers);\r\n  } catch (error) {\r\n    console.error(\"Error in loader:\", error);\r\n    throw new Response(\"Failed to fetch coupons data\", {\r\n      status: 500,\r\n    });\r\n  }\r\n});\r\n\r\nexport const action = withAuth(async ({ request }) => {\r\n  const formData = await request.formData();\r\n  const actionType = formData.get(\"actionType\");\r\n  const couponId = Number(formData.get(\"couponId\"));\r\n\r\n  if (actionType === \"deleteCoupon\") {\r\n    try {\r\n      const response = await deleteCoupon(couponId, request);\r\n      return withResponse(\r\n        { data: response.data, actionType: actionType, success: true },\r\n        response.headers\r\n      );\r\n    } catch (error) {\r\n      console.error(\"Error in action:\", error);\r\n      return json(\r\n        { data: null, actionType: actionType, success: false },\r\n        { status: 500 }\r\n      );\r\n    }\r\n  }\r\n  if (actionType === \"addCoupon\") {\r\n    const payload = JSON.parse(formData.get(\"payload\") as string);\r\n    try {\r\n      const response = await addCoupon(payload, request);\r\n      return withResponse(\r\n        { data: response.data, actionType: actionType, success: true },\r\n        response.headers\r\n      );\r\n    } catch (error) {\r\n      console.error(\"Error in action:\", error);\r\n      return json(\r\n        { data: null, actionType: actionType, success: false },\r\n        { status: 500 }\r\n      );\r\n    }\r\n  }\r\n\r\n  console.log(\"Invalid action type:\", actionType);\r\n  return json(\r\n    { data: null, actionType: actionType, success: false },\r\n    { status: 400 }\r\n  );\r\n});\r\n\r\nexport function CouponCard({\r\n  coupon,\r\n  fetcher,\r\n  navigate,\r\n}: {\r\n  coupon: CouponDetailsType;\r\n  fetcher: ReturnType<typeof useFetcher>;\r\n  navigate: NavigateFunction;\r\n}) {\r\n  return (\r\n    <div className=\"p-4 rounded-xl shadow-[0px_2px_8px_0px_rgba(0,0,0,0.1)] hover:shadow-[0px_4px_12px_0px_rgba(0,0,0,0.15)] transition-shadow duration-200 bg-white border border-neutral-100\">\r\n      <div className=\"mb-3\">\r\n        <div className=\"flex flex-row justify-between items-center\">\r\n          <div className=\"p-1 border border-neutral-600 rounded-md bg-[#FCFCFD]\">\r\n            <h3 className=\"text-base font-semibold text-typography-400\">\r\n              {coupon.couponCode}\r\n            </h3>\r\n          </div>\r\n\r\n          <span\r\n            className={`px-2 py-1 rounded-md text-xs font-medium ${coupon.active\r\n              ? \"bg-green-100 text-green-800\"\r\n              : \"bg-red-100 text-red-800\"\r\n              }`}\r\n          >\r\n            {coupon.active === true\r\n              ? \"Active\"\r\n              : coupon.active === false\r\n                ? \"Expired\"\r\n                : \"\"}\r\n          </span>\r\n        </div>\r\n        <p className=\"mt-2 text-sm text-typography-500 line-clamp-2\">\r\n          {coupon.description}\r\n        </p>\r\n      </div>\r\n\r\n      <div className=\"border-b border-neutral-200\" />\r\n\r\n      <div className=\"my-3\">\r\n        <div className=\"grid grid-cols-2 gap-4\">\r\n          <div>\r\n            <p className=\"text-xs text-typography-400 mb-1\">Coupon Type</p>\r\n            <p className=\"text-sm font-medium text-typography-700\">\r\n              {coupon.couponName\r\n                ? camelCaseToWords(coupon.couponName)\r\n                : \"Not specified\"}\r\n            </p>\r\n          </div>\r\n          <div className=\"text-right\">\r\n            <p className=\"text-xs text-typography-400 mb-1\">Valid Till</p>\r\n            <p className=\"text-sm font-medium text-typography-700\">\r\n              {coupon.validTo\r\n                ? format(parseISO(coupon.validTo), \"dd MMMM yyyy\")\r\n                : \"Not specified\"}\r\n            </p>\r\n          </div>\r\n        </div>\r\n        <div className=\"mt-4 grid grid-cols-2 gap-4\">\r\n          <div>\r\n            <p className=\"text-xs text-typography-400 mb-1\">Coupon Usage</p>\r\n            <div className=\"flex flex-row items-center text-sm font-medium text-typography-700\">\r\n              <span className=\"mr-1\">{coupon.totalUsers || \"0\"} Users</span>\r\n              <Separator\r\n                orientation=\"vertical\"\r\n                className=\"h-4 mx-1 bg-neutral-300\"\r\n              />\r\n              <span className=\"ml-1\">{coupon.totalOrders} Orders</span>\r\n            </div>\r\n          </div>\r\n          <div className=\"text-right\">\r\n            <p className=\"text-xs text-typography-400 mb-1\">Discount Amount</p>\r\n            <p className=\"text-sm font-medium text-typography-700\">\r\n              ₹ {coupon.discountValue}\r\n            </p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"border-b border-neutral-200\" />\r\n\r\n      <div className=\"relative mt-3 flex flex-row gap-3 justify-end\">\r\n        <button\r\n          className=\"border border-neutral-200 rounded-full p-2 cursor-pointer hover:bg-primary-50 hover:border-primary-200 transition-colors duration-200\"\r\n          onClick={() => navigate(`/sellerSetting/coupon/${coupon.id}`)}\r\n          aria-label=\"Edit coupon\"\r\n        >\r\n          <Edit2Icon className=\"w-4 h-4 text-primary-600\" />\r\n        </button>\r\n        <button\r\n          className=\"border border-neutral-200 rounded-full p-2 cursor-pointer hover:bg-red-50 hover:border-red-200 transition-colors duration-200\"\r\n          onClick={() => {\r\n            if (confirm(\"Are you sure you want to delete this coupon?\")) {\r\n              fetcher.submit(\r\n                { actionType: \"deleteCoupon\", couponId: String(coupon.id) },\r\n                { method: \"DELETE\" }\r\n              );\r\n            }\r\n          }}\r\n          aria-label=\"Delete coupon\"\r\n        >\r\n          <Trash2Icon className=\"w-4 h-4 text-red-500\" />\r\n        </button>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nfunction QuickCouponCard({\r\n  title,\r\n  description,\r\n  icon,\r\n  defaultValue,\r\n  onSubmit,\r\n  isLoading,\r\n}: {\r\n  title: string;\r\n  description: string;\r\n  icon: React.ReactNode;\r\n  defaultValue: string;\r\n  onSubmit: (value: string) => void;\r\n  isLoading: boolean;\r\n}) {\r\n  const [discountValue, setDiscountValue] = useState(defaultValue);\r\n\r\n  return (\r\n    <div className=\"p-6 rounded-xl border-2 border-dashed border-primary-200 bg-gradient-to-br from-primary-50 to-white hover:border-primary-300 transition-all duration-200\">\r\n      <div className=\"flex flex-col items-center text-center space-y-4\">\r\n        <div className=\"p-3 rounded-full bg-primary-100\">\r\n          {icon}\r\n        </div>\r\n        <div>\r\n          <h3 className=\"text-lg font-semibold text-typography-700 mb-1\">\r\n            {title}\r\n          </h3>\r\n          <p className=\"text-sm text-typography-500\">{description}</p>\r\n        </div>\r\n        <div className=\"flex items-center space-x-2 w-full max-w-xs\">\r\n          <Input\r\n            type=\"number\"\r\n            value={discountValue}\r\n            onChange={(e) => setDiscountValue(e.target.value)}\r\n            className=\"text-center font-medium\"\r\n            min=\"1\"\r\n            max=\"100\"\r\n          />\r\n          <span className=\"text-sm font-medium text-typography-600 whitespace-nowrap\">% off</span>\r\n        </div>\r\n        <Button\r\n          onClick={() => onSubmit(discountValue)}\r\n          disabled={isLoading || !discountValue || Number(discountValue) <= 0}\r\n          className=\"w-full bg-primary hover:bg-primary-600 text-white font-medium\"\r\n        >\r\n          {isLoading ? \"Creating...\" : \"Save Coupon\"}\r\n        </Button>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\n\r\nexport default function Coupons() {\r\n  const { data: coupons } = useLoaderData<Loaderdata>();\r\n  const navigate = useNavigate();\r\n  const { revalidate } = useRevalidator();\r\n  const [searchTerm, setSearchTerm] = useState(\"\");\r\n  const [filteredCoupons, setFilteredCoupons] = useState<CouponDetailsType[]>(\r\n    coupons || []\r\n  );\r\n\r\n  const fetcher = useFetcher<{\r\n    data: void;\r\n    success: boolean;\r\n    actionType: string;\r\n  }>();\r\n  const fetcher1 = useFetcher<{\r\n    data: void;\r\n    success: boolean;\r\n    actionType: string;\r\n  }>();\r\n  const { showToast } = useToast();\r\n\r\n  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    const value = e.target.value;\r\n    setSearchTerm(value);\r\n  };\r\n\r\n  const clearSearch = () => {\r\n    setSearchTerm(\"\");\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (!coupons) return;\r\n\r\n    if (searchTerm.trim() === \"\") {\r\n      setFilteredCoupons(coupons);\r\n    } else {\r\n      const term = searchTerm.toLowerCase().trim();\r\n      const filtered = coupons.filter((coupon) =>\r\n        coupon.couponCode.toLowerCase().includes(term)\r\n      );\r\n      setFilteredCoupons(filtered);\r\n    }\r\n  }, [searchTerm, coupons]);\r\n\r\n  useEffect(() => {\r\n    if (fetcher.state === \"idle\" && fetcher.data) {\r\n      if (\r\n        fetcher.data.success === true &&\r\n        fetcher.data.actionType === \"deleteCoupon\"\r\n      ) {\r\n        showToast(\"Coupon deleted successfully\", \"success\");\r\n        revalidate();\r\n      } else if (\r\n        fetcher.data.success === false &&\r\n        fetcher.data.actionType === \"deleteCoupon\"\r\n      ) {\r\n        showToast(\"Failed to delete coupon\", \"error\");\r\n      } else if (\r\n        fetcher.data.success === true &&\r\n        fetcher.data.actionType === \"addCoupon\"\r\n      ) {\r\n        showToast(\"Coupon created successfully\", \"success\");\r\n        revalidate();\r\n      } else if (\r\n        fetcher.data.success === false &&\r\n        fetcher.data.actionType === \"addCoupon\"\r\n      ) {\r\n        showToast(\"Failed to create coupon\", \"error\");\r\n      }\r\n    }\r\n  }, [fetcher.state, fetcher.data]);\r\n\r\n  useEffect(() => {\r\n    if (fetcher1.state === \"idle\" && fetcher1.data) {\r\n      if (\r\n        fetcher1.data.success === true &&\r\n        fetcher1.data.actionType === \"addCoupon\"\r\n      ) {\r\n        showToast(\"Coupon created successfully\", \"success\");\r\n        revalidate();\r\n      } else if (\r\n        fetcher1.data.success === false &&\r\n        fetcher1.data.actionType === \"addCoupon\"\r\n      ) {\r\n        showToast(\"Failed to create coupon\", \"error\");\r\n      }\r\n    }\r\n  }, [fetcher1.state, fetcher1.data]);\r\n\r\n  const handleQuickCoupon = async (type: 'newCustomer' | 'repeating', discountValue: string) => {\r\n    const configurations = {\r\n      newCustomer: {\r\n        code: \"WELCOME_\" + discountValue,\r\n        name: \"newCustomer\",\r\n        description: `Flat ${discountValue}% off for new customers`,\r\n        discountConfiguration: [\r\n          {\r\n            property: \"discountPercent\",\r\n            operator: \"EQ\",\r\n            value: discountValue + \"\"\r\n          }\r\n        ],\r\n        filterConfiguration: [],\r\n        validityConfiguration: [\r\n          {\r\n            property: \"firstNOrders\",\r\n            operator: \"EQ\",\r\n            value: \"1\"\r\n          },\r\n        ]\r\n      },\r\n      repeating: {\r\n        code: \"REPEAT_\" + discountValue,\r\n        name: \"highOrderValue\",\r\n        description: `Flat ${discountValue}% off for loyal customers`,\r\n        discountConfiguration: [\r\n          {\r\n            property: \"discountPercent\",\r\n            operator: \"EQ\",\r\n            value: discountValue + \"\"\r\n          }\r\n        ],\r\n        filterConfiguration: [\r\n          {\r\n            property: \"orderCount\",\r\n            operator: \"GTE\",\r\n            value: \"2\"\r\n          }\r\n        ],\r\n        validityConfiguration: []\r\n      }\r\n    };\r\n\r\n    if (type === \"newCustomer\") {\r\n      fetcher.submit(\r\n        {\r\n          actionType: \"addCoupon\",\r\n          payload: JSON.stringify(configurations[type])\r\n        },\r\n        { method: \"POST\" }\r\n      );\r\n    } else if (type === \"repeating\") {\r\n      fetcher1.submit(\r\n        {\r\n          actionType: \"addCoupon\",\r\n          payload: JSON.stringify(configurations[type])\r\n        },\r\n        { method: \"POST\" }\r\n      );\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"p-6\">\r\n      <div className=\"mb-6\">\r\n        <h1 className=\"text-3xl font-bold text-gray-900\">Coupons</h1>\r\n        <p className=\"text-gray-600 mt-2\">Manage your promotional offers and discounts</p>\r\n      </div>\r\n      \r\n      <div className=\"mb-6\">\r\n        <div className=\"relative max-w-md\">\r\n          <div className=\"absolute inset-y-0 left-3 flex items-center pointer-events-none\">\r\n            <SearchIcon className=\"h-4 w-4 text-muted-foreground\" />\r\n          </div>\r\n          <Input\r\n            type=\"text\"\r\n            placeholder=\"Search coupons by code...\"\r\n            className=\"pl-10 pr-10\"\r\n            value={searchTerm}\r\n            onChange={handleSearchChange}\r\n          />\r\n          {searchTerm && (\r\n            <div className=\"absolute inset-y-0 right-3 flex items-center\">\r\n              <button\r\n                onClick={clearSearch}\r\n                className=\"text-muted-foreground hover:text-foreground transition-colors\"\r\n                aria-label=\"Clear search\"\r\n              >\r\n                <XCircleIcon className=\"h-4 w-4\" />\r\n              </button>\r\n            </div>\r\n          )}\r\n        </div>\r\n      </div>\r\n\r\n      <div\r\n        aria-labelledby=\"coupons-list\"\r\n        className=\"pb-20 md:pb-5\"\r\n      >\r\n        {filteredCoupons.length > 0 ? (\r\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-4\">\r\n            {filteredCoupons.map((coupon) => (\r\n              <CouponCard\r\n                key={coupon.id}\r\n                coupon={coupon}\r\n                fetcher={fetcher}\r\n                navigate={navigate}\r\n              />\r\n            ))}\r\n          </div>\r\n        ) : (\r\n          <div className=\"flex flex-col items-center justify-center py-10 text-center\">\r\n            {searchTerm ? (\r\n              <>\r\n                <p className=\"text-muted-foreground mb-2\">\r\n                  No coupons found matching &quot;{searchTerm}&quot;\r\n                </p>\r\n                <p className=\"text-muted-foreground text-sm\">\r\n                  Try a different search term or clear the search\r\n                </p>\r\n                <Button\r\n                  variant=\"outline\"\r\n                  className=\"mt-4\"\r\n                  onClick={clearSearch}\r\n                >\r\n                  Clear Search\r\n                </Button>\r\n              </>\r\n            ) : (\r\n              <>\r\n                <div className=\"w-full max-w-4xl\">\r\n                  <div className=\"text-center mb-8\">\r\n                    <h3 className=\"text-2xl font-semibold text-foreground mb-2\">\r\n                      Create Your First Coupon\r\n                    </h3>\r\n                    <p className=\"text-muted-foreground\">\r\n                      Get started with these popular coupon templates or create a custom one\r\n                    </p>\r\n                  </div>\r\n\r\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6 mb-8\">\r\n                    <QuickCouponCard\r\n                      title=\"New Customer Discount\"\r\n                      description=\"Welcome first-time buyers with a special discount\"\r\n                      icon={\r\n                        <svg className=\"w-6 h-6 text-primary-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\" />\r\n                        </svg>\r\n                      }\r\n                      defaultValue=\"30\"\r\n                      onSubmit={(value) => handleQuickCoupon('newCustomer', value)}\r\n                      isLoading={fetcher.state === \"submitting\" || fetcher.state === \"loading\"}\r\n                    />\r\n\r\n                    <QuickCouponCard\r\n                      title=\"Repeating Customer Discount\"\r\n                      description=\"Reward your returning customers for their loyalty\"\r\n                      icon={\r\n                        <svg className=\"w-6 h-6 text-primary-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z\" />\r\n                        </svg>\r\n                      }\r\n                      defaultValue=\"20\"\r\n                      onSubmit={(value) => handleQuickCoupon('repeating', value)}\r\n                      isLoading={fetcher1.state === \"submitting\" || fetcher1.state === \"loading\"}\r\n                    />\r\n                  </div>\r\n\r\n                  <div className=\"text-center\">\r\n                    <p className=\"text-muted-foreground text-sm mb-4\">\r\n                      Need more customization?\r\n                    </p>\r\n                    <Button\r\n                      variant=\"outline\"\r\n                      onClick={() => navigate(\"/sellerSetting/coupon/add\")}\r\n                      className=\"font-medium\"\r\n                    >\r\n                      Create Custom Coupon\r\n                    </Button>\r\n                  </div>\r\n                </div>\r\n              </>\r\n            )}\r\n          </div>\r\n        )}\r\n      </div>\r\n\r\n      <div className=\"fixed bottom-0 left-0 right-0 z-30 p-4 flex justify-center md:static md:justify-end md:border-0\">\r\n        <Button\r\n          onClick={() => navigate(\"/sellerSetting/coupon/add\")}\r\n          className=\"w-full md:w-auto\"\r\n        >\r\n          Add New Coupon\r\n        </Button>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": ["CouponCard", "coupon", "fetcher", "navigate", "jsxs", "className", "children", "jsx", "couponCode", "active", "description", "couponName", "camelCaseToWords", "validTo", "format", "parseISO", "totalUsers", "Separator", "orientation", "totalOrders", "discountValue", "onClick", "id", "Edit2Icon", "confirm", "submit", "actionType", "couponId", "String", "method", "Trash2Icon", "QuickCouponCard", "title", "icon", "defaultValue", "onSubmit", "isLoading", "setDiscountValue", "useState", "Input", "type", "value", "onChange", "e", "target", "min", "max", "<PERSON><PERSON>", "disabled", "Number", "Coupons", "data", "coupons", "useLoaderData", "useNavigate", "revalidate", "useRevalidator", "searchTerm", "setSearchTerm", "filteredCoupons", "setFilteredCoupons", "useFetcher", "fetcher1", "showToast", "useToast", "handleSearchChange", "clearSearch", "useEffect", "trim", "term", "toLowerCase", "filtered", "filter", "includes", "state", "success", "handleQuickCoupon", "configurations", "newCustomer", "code", "name", "discountConfiguration", "property", "operator", "filterConfiguration", "validityConfiguration", "repeating", "payload", "JSON", "stringify", "SearchIcon", "placeholder", "XCircleIcon", "length", "map", "Fragment", "variant", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAgFO,SAASA,WAAW;AAAA,EACzBC;AAAAA,EACAC;AAAAA,EACAC;AACF,GAIG;AAEC,SAAAC,kCAAAA,KAAC,OAAI;AAAA,IAAAC,WAAU;AAAA,IACbC,UAAA,CAACF,kCAAA,KAAA,OAAA;AAAA,MAAIC,WAAU;AAAA,MACbC,UAAA,CAACF,kCAAA,KAAA,OAAA;AAAA,QAAIC,WAAU;AAAA,QACbC,UAAA,CAACC,kCAAA,IAAA,OAAA;AAAA,UAAIF,WAAU;AAAA,UACbC,UAAAC,kCAAA,IAAC;YAAGF,WAAU;AAAA,YACXC,UAAOL,OAAAO;AAAAA,UACV,CAAA;AAAA,QACF,CAAA,GAEAD,kCAAA,IAAC,QAAA;AAAA,UACCF,WAAW,4CAA4CJ,OAAOQ,SAC1D,gCACA,yBACF;AAAA,UAEDH,iBAAOG,WAAW,OACf,WACAR,OAAOQ,WAAW,QAChB,YACA;AAAA,QAAA,CACR,CAAA;AAAA,MACF,CAAA,GACCF,kCAAA,IAAA,KAAA;AAAA,QAAEF,WAAU;AAAA,QACVC,iBAAOI;AAAAA,MACV,CAAA,CAAA;AAAA,IACF,CAAA,GAEAH,kCAAA,IAAC,OAAI;AAAA,MAAAF,WAAU;AAAA,IAA8B,CAAA,GAE7CD,kCAAA,KAAC,OAAI;AAAA,MAAAC,WAAU;AAAA,MACbC,UAAA,CAACF,kCAAA,KAAA,OAAA;AAAA,QAAIC,WAAU;AAAA,QACbC,UAAA,CAAAF,kCAAA,KAAC,OACC;AAAA,UAAAE,UAAA,CAACC,kCAAA,IAAA,KAAA;AAAA,YAAEF,WAAU;AAAA,YAAmCC,UAAW;AAAA,UAAA,CAAA,GAC3DC,kCAAA,IAAC,KAAE;AAAA,YAAAF,WAAU;AAAA,YACVC,UAAAL,OAAOU,aACJC,iBAAiBX,OAAOU,UAAU,IAClC;AAAA,UACN,CAAA,CAAA;AAAA,QACF,CAAA,GACAP,kCAAA,KAAC,OAAI;AAAA,UAAAC,WAAU;AAAA,UACbC,UAAA,CAACC,kCAAA,IAAA,KAAA;AAAA,YAAEF,WAAU;AAAA,YAAmCC,UAAU;AAAA,UAAA,CAAA,GACzDC,kCAAA,IAAA,KAAA;AAAA,YAAEF,WAAU;AAAA,YACVC,UAAOL,OAAAY,UACJC,OAAOC,SAASd,OAAOY,OAAO,GAAG,cAAc,IAC/C;AAAA,UACN,CAAA,CAAA;AAAA,QACF,CAAA,CAAA;AAAA,MACF,CAAA,GACAT,kCAAA,KAAC,OAAI;AAAA,QAAAC,WAAU;AAAA,QACbC,UAAA,CAAAF,kCAAA,KAAC,OACC;AAAA,UAAAE,UAAA,CAACC,kCAAA,IAAA,KAAA;AAAA,YAAEF,WAAU;AAAA,YAAmCC,UAAY;AAAA,UAAA,CAAA,GAC5DF,kCAAA,KAAC,OAAI;AAAA,YAAAC,WAAU;AAAA,YACbC,UAAA,CAACF,kCAAA,KAAA,QAAA;AAAA,cAAKC,WAAU;AAAA,cAAQC,UAAA,CAAAL,OAAOe,cAAc,KAAI,QAAA;AAAA,YAAM,CAAA,GACvDT,kCAAA,IAACU,WAAA;AAAA,cACCC,aAAY;AAAA,cACZb,WAAU;AAAA,YAAA,CACZ,GACAD,kCAAA,KAAC,QAAK;AAAA,cAAAC,WAAU;AAAA,cAAQC,UAAA,CAAOL,OAAAkB,aAAY,SAAA;AAAA,YAAO,CAAA,CAAA;AAAA,UACpD,CAAA,CAAA;AAAA,QACF,CAAA,GACAf,kCAAA,KAAC,OAAI;AAAA,UAAAC,WAAU;AAAA,UACbC,UAAA,CAACC,kCAAA,IAAA,KAAA;AAAA,YAAEF,WAAU;AAAA,YAAmCC,UAAe;AAAA,UAAA,CAAA,GAC/DF,kCAAA,KAAC,KAAE;AAAA,YAAAC,WAAU;AAAA,YAA0CC,UAAA,CAAA,MAClDL,OAAOmB,aAAA;AAAA,UACZ,CAAA,CAAA;AAAA,QACF,CAAA,CAAA;AAAA,MACF,CAAA,CAAA;AAAA,IACF,CAAA,GAEAb,kCAAA,IAAC,OAAI;AAAA,MAAAF,WAAU;AAAA,IAA8B,CAAA,GAE7CD,kCAAA,KAAC,OAAI;AAAA,MAAAC,WAAU;AAAA,MACbC,UAAA,CAAAC,kCAAA,IAAC,UAAA;AAAA,QACCF,WAAU;AAAA,QACVgB,SAASA,MAAMlB,SAAS,yBAAyBF,OAAOqB,EAAE,EAAE;AAAA,QAC5D,cAAW;AAAA,QAEXhB,UAAAC,kCAAA,IAACgB,KAAU;AAAA,UAAAlB,WAAU;AAAA,QAA2B,CAAA;AAAA,MAAA,CAClD,GACAE,kCAAA,IAAC,UAAA;AAAA,QACCF,WAAU;AAAA,QACVgB,SAASA,MAAM;AACT,cAAAG,QAAQ,8CAA8C,GAAG;AACnDtB,oBAAAuB,OACN;AAAA,cAAEC,YAAY;AAAA,cAAgBC,UAAUC,OAAO3B,OAAOqB,EAAE;AAAA,YAAE,GAC1D;AAAA,cAAEO,QAAQ;AAAA,YAAS,CACrB;AAAA,UACF;AAAA,QACF;AAAA,QACA,cAAW;AAAA,QAEXvB,UAAAC,kCAAA,IAACuB,QAAW;AAAA,UAAAzB,WAAU;AAAA,QAAuB,CAAA;AAAA,MAAA,CAC/C,CAAA;AAAA,IACF,CAAA,CAAA;AAAA,EACF,CAAA;AAEJ;AAEA,SAAS0B,gBAAgB;AAAA,EACvBC;AAAAA,EACAtB;AAAAA,EACAuB;AAAAA,EACAC;AAAAA,EACAC;AAAAA,EACAC;AACF,GAOG;AACD,QAAM,CAAChB,eAAeiB,gBAAgB,IAAIC,aAAAA,SAASJ,YAAY;AAE/D,+CACG,OAAI;AAAA,IAAA7B,WAAU;AAAA,IACbC,UAACF,kCAAA,KAAA,OAAA;AAAA,MAAIC,WAAU;AAAA,MACbC,UAAA,CAACC,kCAAA,IAAA,OAAA;AAAA,QAAIF,WAAU;AAAA,QACZC,UACH2B;AAAAA,MAAA,CAAA,0CACC,OACC;AAAA,QAAA3B,UAAA,CAACC,kCAAA,IAAA,MAAA;AAAA,UAAGF,WAAU;AAAA,UACXC,UACH0B;AAAAA,QAAA,CAAA,GACCzB,kCAAA,IAAA,KAAA;AAAA,UAAEF,WAAU;AAAA,UAA+BC,UAAYI;AAAAA,QAAA,CAAA,CAAA;AAAA,MAC1D,CAAA,GACAN,kCAAA,KAAC,OAAI;AAAA,QAAAC,WAAU;AAAA,QACbC,UAAA,CAAAC,kCAAA,IAACgC,OAAA;AAAA,UACCC,MAAK;AAAA,UACLC,OAAOrB;AAAAA,UACPsB,UAAWC,OAAMN,iBAAiBM,EAAEC,OAAOH,KAAK;AAAA,UAChDpC,WAAU;AAAA,UACVwC,KAAI;AAAA,UACJC,KAAI;AAAA,QAAA,CACN,GACCvC,kCAAA,IAAA,QAAA;AAAA,UAAKF,WAAU;AAAA,UAA4DC,UAAK;AAAA,QAAA,CAAA,CAAA;AAAA,MACnF,CAAA,GACAC,kCAAA,IAACwC,QAAA;AAAA,QACC1B,SAASA,MAAMc,SAASf,aAAa;AAAA,QACrC4B,UAAUZ,aAAa,CAAChB,iBAAiB6B,OAAO7B,aAAa,KAAK;AAAA,QAClEf,WAAU;AAAA,QAETC,sBAAY,gBAAgB;AAAA,MAAA,CAC/B,CAAA;AAAA,IACF,CAAA;AAAA,EACF,CAAA;AAEJ;AAGA,SAAwB4C,UAAU;AAChC,QAAM;AAAA,IAAEC,MAAMC;AAAAA,EAAQ,IAAIC,cAA0B;AACpD,QAAMlD,WAAWmD,YAAY;AACvB,QAAA;AAAA,IAAEC;AAAAA,EAAW,IAAIC,eAAe;AACtC,QAAM,CAACC,YAAYC,aAAa,IAAIpB,aAAAA,SAAS,EAAE;AACzC,QAAA,CAACqB,iBAAiBC,kBAAkB,IAAItB,aAAAA,SAC5Cc,WAAW,CAAA,CACb;AAEA,QAAMlD,UAAU2D,WAIb;AACH,QAAMC,WAAWD,WAId;AACG,QAAA;AAAA,IAAEE;AAAAA,EAAU,IAAIC,SAAS;AAEzB,QAAAC,qBAAsBtB,OAA2C;AAC/D,UAAAF,QAAQE,EAAEC,OAAOH;AACvBiB,kBAAcjB,KAAK;AAAA,EACrB;AAEA,QAAMyB,cAAcA,MAAM;AACxBR,kBAAc,EAAE;AAAA,EAClB;AAEAS,eAAAA,UAAU,MAAM;AACd,QAAI,CAACf,QAAS;AAEV,QAAAK,WAAWW,KAAK,MAAM,IAAI;AAC5BR,yBAAmBR,OAAO;AAAA,IAC5B,OAAO;AACL,YAAMiB,OAAOZ,WAAWa,YAAY,EAAEF,KAAK;AAC3C,YAAMG,WAAWnB,QAAQoB,OAAQvE,YAC/BA,OAAOO,WAAW8D,YAAY,EAAEG,SAASJ,IAAI,CAC/C;AACAT,yBAAmBW,QAAQ;AAAA,IAC7B;AAAA,EACF,GAAG,CAACd,YAAYL,OAAO,CAAC;AAExBe,eAAAA,UAAU,MAAM;AACd,QAAIjE,QAAQwE,UAAU,UAAUxE,QAAQiD,MAAM;AAC5C,UACEjD,QAAQiD,KAAKwB,YAAY,QACzBzE,QAAQiD,KAAKzB,eAAe,gBAC5B;AACAqC,kBAAU,+BAA+B,SAAS;AACvCR,mBAAA;AAAA,MACb,WACErD,QAAQiD,KAAKwB,YAAY,SACzBzE,QAAQiD,KAAKzB,eAAe,gBAC5B;AACAqC,kBAAU,2BAA2B,OAAO;AAAA,MAC9C,WACE7D,QAAQiD,KAAKwB,YAAY,QACzBzE,QAAQiD,KAAKzB,eAAe,aAC5B;AACAqC,kBAAU,+BAA+B,SAAS;AACvCR,mBAAA;AAAA,MACb,WACErD,QAAQiD,KAAKwB,YAAY,SACzBzE,QAAQiD,KAAKzB,eAAe,aAC5B;AACAqC,kBAAU,2BAA2B,OAAO;AAAA,MAC9C;AAAA,IACF;AAAA,KACC,CAAC7D,QAAQwE,OAAOxE,QAAQiD,IAAI,CAAC;AAEhCgB,eAAAA,UAAU,MAAM;AACd,QAAIL,SAASY,UAAU,UAAUZ,SAASX,MAAM;AAC9C,UACEW,SAASX,KAAKwB,YAAY,QAC1Bb,SAASX,KAAKzB,eAAe,aAC7B;AACAqC,kBAAU,+BAA+B,SAAS;AACvCR,mBAAA;AAAA,MACb,WACEO,SAASX,KAAKwB,YAAY,SAC1Bb,SAASX,KAAKzB,eAAe,aAC7B;AACAqC,kBAAU,2BAA2B,OAAO;AAAA,MAC9C;AAAA,IACF;AAAA,KACC,CAACD,SAASY,OAAOZ,SAASX,IAAI,CAAC;AAE5B,QAAAyB,oBAAoB,OAAOpC,MAAmCpB,kBAA0B;AAC5F,UAAMyD,iBAAiB;AAAA,MACrBC,aAAa;AAAA,QACXC,MAAM,aAAa3D;AAAAA,QACnB4D,MAAM;AAAA,QACNtE,aAAa,QAAQU,aAAa;AAAA,QAClC6D,uBAAuB,CACrB;AAAA,UACEC,UAAU;AAAA,UACVC,UAAU;AAAA,UACV1C,OAAOrB,gBAAgB;AAAA,QACzB,CAAA;AAAA,QAEFgE,qBAAqB,CAAC;AAAA,QACtBC,uBAAuB,CACrB;AAAA,UACEH,UAAU;AAAA,UACVC,UAAU;AAAA,UACV1C,OAAO;AAAA,QACT,CAAA;AAAA,MAEJ;AAAA,MACA6C,WAAW;AAAA,QACTP,MAAM,YAAY3D;AAAAA,QAClB4D,MAAM;AAAA,QACNtE,aAAa,QAAQU,aAAa;AAAA,QAClC6D,uBAAuB,CACrB;AAAA,UACEC,UAAU;AAAA,UACVC,UAAU;AAAA,UACV1C,OAAOrB,gBAAgB;AAAA,QACzB,CAAA;AAAA,QAEFgE,qBAAqB,CACnB;AAAA,UACEF,UAAU;AAAA,UACVC,UAAU;AAAA,UACV1C,OAAO;AAAA,QACT,CAAA;AAAA,QAEF4C,uBAAuB,CAAA;AAAA,MACzB;AAAA,IACF;AAEA,QAAI7C,SAAS,eAAe;AAClBtC,cAAAuB,OACN;AAAA,QACEC,YAAY;AAAA,QACZ6D,SAASC,KAAKC,UAAUZ,eAAerC,IAAI,CAAC;AAAA,MAC9C,GACA;AAAA,QAAEX,QAAQ;AAAA,MAAO,CACnB;AAAA,IACF,WAAWW,SAAS,aAAa;AACtBsB,eAAArC,OACP;AAAA,QACEC,YAAY;AAAA,QACZ6D,SAASC,KAAKC,UAAUZ,eAAerC,IAAI,CAAC;AAAA,MAC9C,GACA;AAAA,QAAEX,QAAQ;AAAA,MAAO,CACnB;AAAA,IACF;AAAA,EACF;AAGE,SAAAzB,kCAAAA,KAAC,OAAI;AAAA,IAAAC,WAAU;AAAA,IACbC,UAAA,CAACF,kCAAA,KAAA,OAAA;AAAA,MAAIC,WAAU;AAAA,MACbC,UAAA,CAACC,kCAAA,IAAA,MAAA;AAAA,QAAGF,WAAU;AAAA,QAAmCC,UAAO;AAAA,MAAA,CAAA,GACvDC,kCAAA,IAAA,KAAA;AAAA,QAAEF,WAAU;AAAA,QAAqBC,UAA4C;AAAA,MAAA,CAAA,CAAA;AAAA,IAChF,CAAA,yCAEC,OAAI;AAAA,MAAAD,WAAU;AAAA,MACbC,UAACF,kCAAA,KAAA,OAAA;AAAA,QAAIC,WAAU;AAAA,QACbC,UAAA,CAAAC,kCAAA,IAAC;UAAIF,WAAU;AAAA,UACbC,gDAACoF,QAAW;AAAA,YAAArF,WAAU;AAAA,UAAgC,CAAA;AAAA,QACxD,CAAA,GACAE,kCAAA,IAACgC,OAAA;AAAA,UACCC,MAAK;AAAA,UACLmD,aAAY;AAAA,UACZtF,WAAU;AAAA,UACVoC,OAAOgB;AAAAA,UACPf,UAAUuB;AAAAA,SACZ,GACCR,cACClD,kCAAA,IAAC,OAAI;AAAA,UAAAF,WAAU;AAAA,UACbC,UAAAC,kCAAA,IAAC,UAAA;AAAA,YACCc,SAAS6C;AAAAA,YACT7D,WAAU;AAAA,YACV,cAAW;AAAA,YAEXC,UAAAC,kCAAA,IAACqF,SAAY;AAAA,cAAAvF,WAAU;AAAA,YAAU,CAAA;AAAA,UACnC,CAAA;AAAA,QACF,CAAA,CAAA;AAAA,MAEJ,CAAA;AAAA,IACF,CAAA,GAEAE,kCAAA,IAAC,OAAA;AAAA,MACC,mBAAgB;AAAA,MAChBF,WAAU;AAAA,MAETC,UAAAqD,gBAAgBkC,SAAS,IACvBtF,kCAAAA,IAAA,OAAA;AAAA,QAAIF,WAAU;AAAA,QACZC,UAAAqD,gBAAgBmC,IAAK7F,YACpBM,kCAAAA,IAACP,YAAA;AAAA,UAECC;AAAAA,UACAC;AAAAA,UACAC;AAAAA,QAAA,GAHKF,OAAOqB,EAId,CACD;AAAA,OACH,IAEAf,kCAAA,IAAC;QAAIF,WAAU;AAAA,QACZC,uBAEGF,kCAAA,KAAA2F,4BAAA;AAAA,UAAAzF,UAAA,CAACF,kCAAA,KAAA,KAAA;AAAA,YAAEC,WAAU;AAAA,YAA6BC,UAAA,CAAA,+BACPmD,YAAW,GAAA;AAAA,UAC9C,CAAA,GACClD,kCAAA,IAAA,KAAA;AAAA,YAAEF,WAAU;AAAA,YAAgCC,UAE7C;AAAA,UAAA,CAAA,GACAC,kCAAA,IAACwC,QAAA;AAAA,YACCiD,SAAQ;AAAA,YACR3F,WAAU;AAAA,YACVgB,SAAS6C;AAAAA,YACV5D,UAAA;AAAA,UAAA,CAED,CAAA;AAAA,QAAA,CACF,IAEAC,kCAAA,IAAAwF,4BAAA;AAAA,UACEzF,UAACF,kCAAA,KAAA,OAAA;AAAA,YAAIC,WAAU;AAAA,YACbC,UAAA,CAACF,kCAAA,KAAA,OAAA;AAAA,cAAIC,WAAU;AAAA,cACbC,UAAA,CAACC,kCAAA,IAAA,MAAA;AAAA,gBAAGF,WAAU;AAAA,gBAA8CC,UAE5D;AAAA,cAAA,CAAA,GACCC,kCAAA,IAAA,KAAA;AAAA,gBAAEF,WAAU;AAAA,gBAAwBC,UAErC;AAAA,cAAA,CAAA,CAAA;AAAA,YACF,CAAA,GAEAF,kCAAA,KAAC,OAAI;AAAA,cAAAC,WAAU;AAAA,cACbC,UAAA,CAAAC,kCAAA,IAACwB,iBAAA;AAAA,gBACCC,OAAM;AAAA,gBACNtB,aAAY;AAAA,gBACZuB,4CACG,OAAI;AAAA,kBAAA5B,WAAU;AAAA,kBAA2B4F,MAAK;AAAA,kBAAOC,QAAO;AAAA,kBAAeC,SAAQ;AAAA,kBAClF7F,UAACC,kCAAA,IAAA,QAAA;AAAA,oBAAK6F,eAAc;AAAA,oBAAQC,gBAAe;AAAA,oBAAQC,aAAa;AAAA,oBAAGC,GAAE;AAAA,kBAAsE,CAAA;AAAA,gBAC7I,CAAA;AAAA,gBAEFrE,cAAa;AAAA,gBACbC,UAAWM,WAAUmC,kBAAkB,eAAenC,KAAK;AAAA,gBAC3DL,WAAWlC,QAAQwE,UAAU,gBAAgBxE,QAAQwE,UAAU;AAAA,cAAA,CACjE,GAEAnE,kCAAA,IAACwB,iBAAA;AAAA,gBACCC,OAAM;AAAA,gBACNtB,aAAY;AAAA,gBACZuB,4CACG,OAAI;AAAA,kBAAA5B,WAAU;AAAA,kBAA2B4F,MAAK;AAAA,kBAAOC,QAAO;AAAA,kBAAeC,SAAQ;AAAA,kBAClF7F,UAACC,kCAAA,IAAA,QAAA;AAAA,oBAAK6F,eAAc;AAAA,oBAAQC,gBAAe;AAAA,oBAAQC,aAAa;AAAA,oBAAGC,GAAE;AAAA,kBAA8H,CAAA;AAAA,gBACrM,CAAA;AAAA,gBAEFrE,cAAa;AAAA,gBACbC,UAAWM,WAAUmC,kBAAkB,aAAanC,KAAK;AAAA,gBACzDL,WAAW0B,SAASY,UAAU,gBAAgBZ,SAASY,UAAU;AAAA,cAAA,CACnE,CAAA;AAAA,YACF,CAAA,GAEAtE,kCAAA,KAAC,OAAI;AAAA,cAAAC,WAAU;AAAA,cACbC,UAAA,CAACC,kCAAA,IAAA,KAAA;AAAA,gBAAEF,WAAU;AAAA,gBAAqCC,UAElD;AAAA,cAAA,CAAA,GACAC,kCAAA,IAACwC,QAAA;AAAA,gBACCiD,SAAQ;AAAA,gBACR3E,SAASA,MAAMlB,SAAS,2BAA2B;AAAA,gBACnDE,WAAU;AAAA,gBACXC,UAAA;AAAA,cAAA,CAED,CAAA;AAAA,YACF,CAAA,CAAA;AAAA,UACF,CAAA;AAAA,QACF,CAAA;AAAA,MAEJ,CAAA;AAAA,IAAA,CAEJ,GAEAC,kCAAA,IAAC,OAAI;AAAA,MAAAF,WAAU;AAAA,MACbC,UAAAC,kCAAA,IAACwC,QAAA;AAAA,QACC1B,SAASA,MAAMlB,SAAS,2BAA2B;AAAA,QACnDE,WAAU;AAAA,QACXC,UAAA;AAAA,MAED,CAAA;AAAA,IACF,CAAA,CAAA;AAAA,EACF,CAAA;AAEJ;"}