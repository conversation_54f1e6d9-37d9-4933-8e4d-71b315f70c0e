{"version": 3, "file": "netWorkConfig-DCee6agm.js", "sources": ["../../../node_modules/lucide-react/dist/esm/icons/upload.js", "../../../app/components/ui/imageUploadComponent.tsx", "../../../app/routes/netWorkConfig.tsx"], "sourcesContent": ["/**\n * @license lucide-react v0.453.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst Upload = createLucideIcon(\"Upload\", [\n  [\"path\", { d: \"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4\", key: \"ih7n3h\" }],\n  [\"polyline\", { points: \"17 8 12 3 7 8\", key: \"t8dd8p\" }],\n  [\"line\", { x1: \"12\", x2: \"12\", y1: \"3\", y2: \"15\", key: \"widbto\" }]\n]);\n\nexport { Upload as default };\n//# sourceMappingURL=upload.js.map\n", "import { Loader2, Trash, Upload } from \"lucide-react\";\r\nimport { Button } from \"./button\";\r\nimport { useEffect, useRef, useState } from \"react\";\r\nimport { useToast } from \"./ToastProvider\";\r\nimport { useFetcher } from \"@remix-run/react\";\r\nimport { Label } from \"./label\";\r\nimport { Input } from \"./input\";\r\n\r\ninterface ImageUploadProps {\r\n      onChange: (urls: string | string[]) => void;\r\n      multiple?: boolean;\r\n}\r\n\r\nconst ImageUploadComponent: React.FC<ImageUploadProps> = ({ onChange, multiple = false }) => {\r\n      const fileInputRef = useRef<HTMLInputElement>(null);\r\n      const [selectedFiles, setSelectedFiles] = useState<File[]>([]);\r\n      const [previewUrls, setPreviewUrls] = useState<string[]>([]);\r\n      const [uploadError, setUploadError] = useState<string | null>(null);\r\n      const [uploading, setUploading] = useState(false);\r\n      const { showToast } = useToast();\r\n      const uploadFetcher = useFetcher<{ success: boolean; fileUrl: string; error?: string }>();\r\n      const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {\r\n            const files = Array.from(event.target.files || []);\r\n            if (files.length === 0) return;\r\n            setUploadError(null);\r\n            const MAX_FILE_SIZE = 500 * 1024;\r\n            const allowedTypes = [\"image/jpeg\", \"image/png\", \"image/gif\", \"image/webp\"];\r\n            const validFiles = files.filter((file) => {\r\n                  if (file.size > MAX_FILE_SIZE) {\r\n                        setUploadError(\"File size exceeds 500Kb limit.\");\r\n                        return false;\r\n                  }\r\n                  if (!allowedTypes.includes(file.type)) {\r\n                        setUploadError(\"Only JPEG, PNG, GIF, and WEBP images are allowed.\");\r\n                        return false;\r\n                  }\r\n                  return true;\r\n            });\r\n\r\n            if (validFiles.length === 0) return;\r\n\r\n            setSelectedFiles(multiple ? [...selectedFiles, ...validFiles] : [validFiles[0]]);\r\n            setPreviewUrls(multiple ? [...previewUrls, ...validFiles.map((file) => URL.createObjectURL(file))] : [URL.createObjectURL(validFiles[0])]);\r\n      };\r\n\r\n      const handleUpload = () => {\r\n            if (selectedFiles.length === 0) return;\r\n\r\n            setUploading(true);\r\n            const formData = new FormData();\r\n            formData.append(\"_action\", \"uploadImage\");\r\n\r\n            selectedFiles.forEach((file) => {\r\n                  formData.append(\"file\", file, file.name);\r\n            });\r\n\r\n            uploadFetcher.submit(formData, {\r\n                  method: \"post\",\r\n                  action: \"/home/<USER>\",\r\n                  encType: \"multipart/form-data\",\r\n            });\r\n      };\r\n\r\n      useEffect(() => {\r\n            if (uploadFetcher.data) {\r\n                  if (uploadFetcher.data.error) {\r\n                        setUploadError(uploadFetcher.data.error);\r\n                  } else if (uploadFetcher.data.fileUrl) {\r\n                        const uploadedUrls = multiple ? [...previewUrls, uploadFetcher.data.fileUrl] : [uploadFetcher.data.fileUrl];\r\n\r\n                        onChange(multiple ? uploadedUrls : uploadFetcher.data.fileUrl);\r\n                        // showToast(\"Image Uploaded To S3 Success\", \"success\");\r\n\r\n                        setSelectedFiles([]);\r\n                        setPreviewUrls(uploadedUrls);\r\n                        setUploadError(null);\r\n                        if (fileInputRef.current) fileInputRef.current.value = \"\";\r\n                  }\r\n                  setUploading(false);\r\n            }\r\n      }, [uploadFetcher.data, onChange]);\r\n\r\n      const handleRemoveImage = (index: number, e?: React.MouseEvent) => {\r\n            e?.preventDefault();\r\n            e?.stopPropagation();\r\n\r\n            const updatedFiles = selectedFiles.filter((_, i) => i !== index);\r\n            const updatedPreviews = previewUrls.filter((_, i) => i !== index);\r\n\r\n            setSelectedFiles(updatedFiles);\r\n            setPreviewUrls(updatedPreviews);\r\n\r\n            onChange(multiple ? updatedPreviews : \"\");\r\n      };\r\n\r\n      return (\r\n            <div className=\"p-6 border border-gray-200 rounded-xl shadow-lg bg-gradient-to-br from-gray-50 to-gray-100 transition-all duration-300 hover:shadow-xl\">\r\n                  <Label className=\"block text-lg font-semibold text-gray-800 mb-3\">Upload Image</Label>\r\n\r\n                  <div className=\"mt-4 flex flex-col items-center gap-6\">\r\n                        {/* Image Previews */}\r\n                        {previewUrls.length > 0 && (\r\n                              <div className=\"flex flex-wrap gap-4 justify-center\">\r\n                                    {previewUrls.map((url, index) => (\r\n                                          <div\r\n                                                key={index}\r\n                                                className=\"relative w-44 h-44 group transition-transform duration-300 hover:scale-105\"\r\n                                          >\r\n                                                <img\r\n                                                      src={url}\r\n                                                      alt=\"Selected Preview\"\r\n                                                      className=\"w-full h-full object-cover rounded-lg border border-gray-200 shadow-md\"\r\n                                                />\r\n                                                <button\r\n                                                      onClick={(e) => handleRemoveImage(index, e)}\r\n                                                      className=\"absolute -top-3 -right-3 bg-red-600 text-white p-2 rounded-full shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 hover:bg-red-700\"\r\n                                                >\r\n                                                      <Trash className=\"h-5 w-5\" />\r\n                                                </button>\r\n                                          </div>\r\n                                    ))}\r\n                              </div>\r\n                        )}\r\n\r\n                        {/* Input and Buttons */}\r\n                        <div className=\"flex gap-3 items-center w-full max-w-lg\">\r\n                              <Input\r\n                                    type=\"text\"\r\n                                    readOnly\r\n                                    value={selectedFiles.length > 0 ? `${selectedFiles.length} file(s) selected` : \"No file selected\"}\r\n                                    className=\"flex-grow cursor-pointer bg-gray-50 border-gray-200 rounded-lg px-4 py-2 text-gray-600 focus:ring-2 focus:ring-blue-300 transition-all duration-200\"\r\n                                    onClick={() => fileInputRef.current?.click()}\r\n                              />\r\n                              <input\r\n                                    ref={fileInputRef}\r\n                                    type=\"file\"\r\n                                    accept=\"image/*\"\r\n                                    multiple={multiple}\r\n                                    onChange={handleFileSelect}\r\n                                    className=\"hidden\"\r\n                              />\r\n                              <Button\r\n                                    type=\"button\"\r\n                                    onClick={selectedFiles.length > 0 ? handleUpload : () => fileInputRef.current?.click()}\r\n                                    disabled={uploading}\r\n                                    className={`flex items-center gap-2 ${uploading\r\n                                          ? \"bg-gray-400 cursor-not-allowed\"\r\n                                          : \"bg-blue-600 hover:bg-blue-700\"\r\n                                          } text-white px-5 py-2 rounded-lg shadow-md transition-all duration-300 hover:shadow-lg`}\r\n                              >\r\n                                    {uploading ? (\r\n                                          <Loader2 className=\"h-5 w-5 animate-spin\" />\r\n                                    ) : (\r\n                                          <Upload className=\"h-5 w-5\" />\r\n                                    )}\r\n                                    {uploading ? \"Uploading...\" : selectedFiles.length > 0 ? \"Upload\" : \"Add Image\"}\r\n                              </Button>\r\n                        </div>\r\n\r\n                        {/* Error Message */}\r\n                        {uploadError && (\r\n                              <p className=\"text-red-500 mt-2 text-sm font-medium animate-pulse\">{uploadError}</p>\r\n                        )}\r\n                  </div>\r\n            </div>\r\n      );\r\n};\r\n\r\nexport default ImageUploadComponent;", "import { useFetcher } from \"@remix-run/react\";\r\nimport { Dayjs } from \"dayjs\";\r\nimport { Edit, Pencil, Save, Trash, X } from \"lucide-react\";\r\nimport { useCallback, useEffect, useState } from \"react\";\r\nimport SpinnerLoader from \"~/components/loader/SpinnerLoader\";\r\nimport { Button } from \"~/components/ui/button\";\r\nimport ImageUploadComponent from \"~/components/ui/imageUploadComponent\";\r\nimport { Label } from \"~/components/ui/label\";\r\nimport { RadioGroup, RadioGroupItem } from \"~/components/ui/radio-group\";\r\nimport { NetworkConfig } from \"~/types/api/businessConsoleService/Network\";\r\n\r\ninterface NetworkConfigProps {\r\n  networkConfig: NetworkConfig[];\r\n  networkName: string;\r\n  onAttributeUpdate: (attribute: string, value: any, domainId: number) => void;\r\n}\r\n\r\nexport default function NetWorkConfig({\r\n  networkConfig,\r\n  networkName,\r\n  onAttributeUpdate,\r\n}: NetworkConfigProps) {\r\n  const [businessConfigEditable, setBusinessConfigEditable] = useState(false);\r\n  const [editingStartPage, setEditingStartPage] = useState(false);\r\n  const [pendingConfig, setPendingConfig] = useState<Partial<NetworkConfig>>({});\r\n  const keyMapping: Record<keyof NetworkConfig, string> = {\r\n    id: \"id\",\r\n    domain: \"domain\",\r\n    businessLogo: \"business_logo\",\r\n    homePageBanner: \"home_page_banner\",\r\n    pwaAppIcon: \"pwa_app_icon\",\r\n    footerAppIcon: \"footer_app_icon\",\r\n    networkId: \"networkId\",\r\n    multiSeller: \"multi_seller\",\r\n    defaultSellerId: \"defaultSellerId\",\r\n    wabEnabled: \"wab_enabled\",\r\n    wabMobileNumber: \"wab_mobile_number\",\r\n    defaultStartPage: \"default_start_page\",\r\n    imageBaseUrl: \"imageBaseUrl\",\r\n    networkType: \"\",\r\n    wabDatasetId: \"wabDatasetId\",\r\n  };\r\n\r\n  // Modified: Reset states on cancel\r\n  const handleBusinessConfig = useCallback(() => {\r\n    setBusinessConfigEditable((prevState) => {\r\n      if (prevState) {\r\n        setPendingConfig({});\r\n        setVisibleSaveButtons({});\r\n      }\r\n      return !prevState;\r\n    });\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    setConfig(networkConfig);\r\n  }, [networkConfig]);\r\n\r\n  const [BasicConfig, setBasicConfig] = useState(false);\r\n\r\n  // Modified: Reset states on cancel\r\n  const BasicConFigEdit = useCallback(() => {\r\n    setBasicConfig((prev) => {\r\n      if (prev) {\r\n        setPendingConfig({});\r\n        setVisibleSaveButtons({});\r\n      }\r\n      return !prev;\r\n    });\r\n  }, []);\r\n\r\n  const [visibleSaveButtons, setVisibleSaveButtons] = useState<\r\n    Record<string, boolean>\r\n  >({});\r\n  const [config, setConfig] = useState<NetworkConfig[]>(networkConfig);\r\n  const [showsave, setShowsave] = useState(false);\r\n\r\n  const handleConfigChange = (\r\n    key: keyof NetworkConfig,\r\n    value: string | number | boolean | Dayjs | null\r\n  ) => {\r\n    console.log(value, key, \"Config Change Triggered\");\r\n\r\n    setPendingConfig((prev) => {\r\n      let newValue: string | number | boolean | \"\" = value as any;\r\n\r\n      if (typeof value === \"string\" && (value === \"yes\" || value === \"no\")) {\r\n        newValue = value === \"yes\";\r\n      } else if (typeof value === \"string\" && !isNaN(Number(value))) {\r\n        newValue = Number(value);\r\n      } else if (value === null || value === undefined) {\r\n        newValue = \"\";\r\n      }\r\n\r\n      return {\r\n        ...prev,\r\n        [key]: newValue,\r\n      };\r\n    });\r\n\r\n    setVisibleSaveButtons((prev) => ({\r\n      ...prev,\r\n      [key]: true,\r\n    }));\r\n  };\r\n\r\n  const fetcher = useFetcher();\r\n\r\n  const handleSave = async (\r\n    key: keyof NetworkConfig,\r\n    value?: string | number | boolean\r\n  ) => {\r\n    try {\r\n      setConfig((prevConfig) => ({\r\n        ...prevConfig,\r\n        [key]: value !== undefined ? value : pendingConfig[key] ?? \"\",\r\n      }));\r\n\r\n      setVisibleSaveButtons((prev) => ({\r\n        ...prev,\r\n        [key]: false,\r\n      }));\r\n      setShowsave(false);\r\n\r\n      let hasError = false;\r\n\r\n      const newValue: string | number | boolean =\r\n        value !== undefined ? value : pendingConfig[key] ?? \"\";\r\n\r\n      try {\r\n        await onAttributeUpdate(\r\n          keyMapping[key as keyof NetworkConfig],\r\n          typeof newValue === \"string\" ? encodeURIComponent(newValue) : newValue,\r\n          networkConfig[0]?.id ?? 0\r\n        );\r\n      } catch (error) {\r\n        console.error(`Failed to update ${key}:`, error);\r\n        hasError = true;\r\n      }\r\n\r\n      if (hasError) {\r\n        alert(\"Some settings could not be saved. Please try again.\");\r\n      } else {\r\n        setPendingConfig({});\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error saving config:\", error);\r\n      alert(\"Failed to save settings. Please try again later.\");\r\n    }\r\n  };\r\n\r\n  const handleImageUpload = (key: keyof NetworkConfig, url: string) => {\r\n    setPendingConfig((prev) => ({\r\n      ...prev,\r\n      [key]: url,\r\n    }));\r\n\r\n    setVisibleSaveButtons((prev) => ({\r\n      ...prev,\r\n      [key]: true,\r\n    }));\r\n  };\r\n\r\n  const [editingFooter, setEditingFooter] = useState(false);\r\n  const [editingBanners, setEditingBanners] = useState(false);\r\n  const [showSaveFooter, setShowSaveFooter] = useState(false);\r\n  const [showSaveBanners, setShowSaveBanners] = useState(false);\r\n  const [editingPlaystore, setEditingPlaystore] = useState(false);\r\n  const [showSavePlaystore, setShowSavePlaystore] = useState(false);\r\n  const [editingBusinessLogo, setEditingBusinessLogo] = useState(false);\r\n  const [showSaveBusinessLogo, setShowSaveBusinessLogo] = useState(false);\r\n\r\n  const Loading = fetcher.state !== \"idle\";\r\n\r\n  return (\r\n    <div className=\"flex flex-col gap-4 border bg-white rounded-xl border-neutral-200 my-4 p-4\">\r\n      {Loading && <SpinnerLoader loading={Loading} />}\r\n      <div className=\"flex flex-col gap-4 border rounded-xl border-neutral-400 p-4 shadow\">\r\n        <div className=\"flex flex-row justify-between\">\r\n          <div className=\"text-lg font-semibold text-typography-700\">\r\n            Basic Configurations\r\n          </div>\r\n          <button\r\n            className=\"inline-flex items-center gap-1 text-blue-600 hover:text-blue-800\"\r\n            onClick={() => BasicConFigEdit()}\r\n          >\r\n            {BasicConfig ? (\r\n              <>\r\n                <X className=\"h-4 w-4\" />\r\n                Cancel\r\n              </>\r\n            ) : (\r\n              <>\r\n                <Edit className=\"h-4 w-4\" />\r\n                Edit\r\n              </>\r\n            )}\r\n          </button>\r\n        </div>\r\n        <div className=\"flex gap-2\">\r\n          <div className=\"p-2 border border-neutral-50 rounded-sm\">\r\n            {!editingBusinessLogo ? (\r\n              networkConfig[0]?.businessLogo ? (\r\n                <div className=\"flex flex-row gap-1 items-center\">\r\n                  <img\r\n                    src={networkConfig[0]?.businessLogo}\r\n                    className=\"w-32 rounded-md border border-neutral-200 transition-transform hover:scale-105\"\r\n                    alt=\"Business Logo\"\r\n                  />\r\n                  <button onClick={() => setEditingBusinessLogo(true)}>\r\n                    <Pencil className=\"h-5 w-5 text-blue-600 hover:text-blue-800\" />\r\n                  </button>\r\n                  <button\r\n                    onClick={() => {\r\n                      handleSave(\"businessLogo\", '\"\"');\r\n                    }}\r\n                    className=\"text-red-500 hover:text-red-700\"\r\n                  >\r\n                    <Trash className=\"h-5 w-5\" />\r\n                  </button>\r\n                </div>\r\n              ) : (\r\n                <div className=\"text-gray-500 flex items-center gap-2\">\r\n                  No Business Logo\r\n                  <button onClick={() => setEditingBusinessLogo(true)}>\r\n                    <Pencil className=\"h-5 w-5 text-blue-600 hover:text-blue-800\" />\r\n                  </button>\r\n                </div>\r\n              )\r\n            ) : (\r\n              <div className=\"flex flex-col gap-4 bg-gray-50 p-4 rounded-lg shadow-sm\">\r\n                <div className=\"bg-white border border-dashed border-neutral-300 rounded-md p-4\">\r\n                  <ImageUploadComponent\r\n                    onChange={(url: string | string[]) => {\r\n                      setShowSaveBusinessLogo(true);\r\n                      const businessLogoUrl = Array.isArray(url) ? url[0] : url;\r\n                      handleImageUpload(\"businessLogo\", businessLogoUrl);\r\n                      setEditingBusinessLogo(false);\r\n                    }}\r\n                  />\r\n                </div>\r\n                <div className=\"flex flex-row items-center justify-center gap-4\">\r\n                  <Button\r\n                    className=\"bg-gray-500 hover:bg-gray-600 text-white flex items-center gap-2 px-4 py-2 rounded-md transition-all duration-200 shadow-sm\"\r\n                    onClick={() => {\r\n                      setEditingBusinessLogo(false);\r\n                      setShowSaveBusinessLogo(false);\r\n                      // Modified: Reset pendingConfig and visibleSaveButtons\r\n                      setPendingConfig((prev) => ({ ...prev, businessLogo: undefined }));\r\n                      setVisibleSaveButtons((prev) => ({ ...prev, businessLogo: false }));\r\n                    }}\r\n                  >\r\n                    <X className=\"h-5 w-5\" /> Cancel\r\n                  </Button>\r\n                </div>\r\n              </div>\r\n            )}\r\n            {showSaveBusinessLogo && (\r\n              <div className=\"flex flex-row items-center justify-center mt-2\">\r\n                <Button\r\n                  className=\"bg-blue-600 hover:bg-blue-700 text-white flex items-center gap-2 px-4 py-2 rounded-md transition-all duration-200 shadow-sm\"\r\n                  onClick={() => {\r\n                    handleSave(\"businessLogo\");\r\n                    setEditingBusinessLogo(false);\r\n                    setShowSaveBusinessLogo(false);\r\n                  }}\r\n                >\r\n                  <Save className=\"h-5 w-5\" /> Save\r\n                </Button>\r\n              </div>\r\n            )}\r\n          </div>\r\n          <div className=\"flex flex-col gap-0\">\r\n            <div className=\"text-sm text-typography-300\">\r\n              NetWork Id : {networkConfig[0]?.networkId}\r\n            </div>\r\n            <div className=\"text-md text-typography-600\">\r\n              NetWork Name : {networkName}\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div className=\"flex flex-col gap-0\">\r\n          <div className=\"text-sm text-typography-300\">\r\n            Domain ID : {networkConfig[0]?.id}\r\n          </div>\r\n          <div className=\"flex flex-col gap-2 text-md text-typography-400\">\r\n            <div className=\"flex flex-row gap-4 items-center\">\r\n              <div className=\"text-md text-typography-500 w-sm\">\r\n                Domain URL :\r\n              </div>\r\n              <span className=\"font-semibold text-typography-800 flex gap-2 items-center\">\r\n                <input\r\n                  type=\"text\"\r\n                  value={(pendingConfig.domain ?? networkConfig[0]?.domain) || \"\"}\r\n                  onChange={(e) => handleConfigChange(\"domain\", e.target.value)}\r\n                  className=\"border border-neutral-400 rounded-md p-1 px-2\"\r\n                  disabled={!BasicConfig}\r\n                />\r\n              </span>\r\n              {BasicConfig && visibleSaveButtons[\"domain\"] && (\r\n                <button\r\n                  className=\"flex flex-row text-blue-500 items-center gap-1 cursor-pointer text-lg font-bold\"\r\n                  onClick={() => handleSave(\"domain\" as keyof NetworkConfig)}\r\n                >\r\n                  <Save className=\"h-5 w-5\" />\r\n                </button>\r\n              )}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"flex flex-col gap-4 border rounded-xl border-neutral-400 p-4 shadow\">\r\n        <div className=\"flex w-full justify-between\">\r\n          <div className=\"abstractart-typography-700\">\r\n            Business Configurations\r\n          </div>\r\n          <button\r\n            className=\"inline-flex items-center gap-1 text-blue-600 hover:text-blue-800\"\r\n            onClick={() => handleBusinessConfig()}\r\n          >\r\n            {businessConfigEditable ? (\r\n              <>\r\n                <X className=\"h-4 w-4\" />\r\n                Cancel\r\n              </>\r\n            ) : (\r\n              <>\r\n                <Edit className=\"h-4 w-4\" />\r\n                Edit\r\n              </>\r\n            )}\r\n          </button>\r\n        </div>\r\n        {[\r\n          { label: \"Are Multiple Sellers Allowed?  :\", key: \"multiSeller\" },\r\n          { label: \"Is WhatsApp Enabled?  :\", key: \"wabEnabled\" },\r\n        ].map(({ label, key }) => (\r\n          <div\r\n            key={key}\r\n            className=\"flex gap-4 items-center text-md text-typography-400\"\r\n          >\r\n            <Label className=\"w-[400px] text-md text-typography-400\">\r\n              {label}\r\n            </Label>\r\n            <RadioGroup\r\n              value={\r\n                (pendingConfig[key as keyof NetworkConfig] ??\r\n                  config[0]?.[key as keyof NetworkConfig])\r\n                  ? \"yes\"\r\n                  : \"no\"\r\n              }\r\n              onValueChange={(val) =>\r\n                handleConfigChange(key as keyof NetworkConfig, val)\r\n              }\r\n              className=\"flex gap-4 items-center text-md font-semibold text-typography-800\"\r\n              disabled={!businessConfigEditable}\r\n            >\r\n              <div className=\"flex items-center gap-2\">\r\n                <RadioGroupItem id={`${key}-yes`} value=\"yes\" />\r\n                <Label htmlFor={`${key}-yes`}>Yes</Label>\r\n              </div>\r\n              <div className=\"flex items-center gap-2\">\r\n                <RadioGroupItem id={`${key}-no`} value=\"no\" />\r\n                <Label htmlFor={`${key}-no`}>No</Label>\r\n              </div>\r\n            </RadioGroup>\r\n            {businessConfigEditable && visibleSaveButtons[key] && (\r\n              <div className=\"flex items-center gap-2\">\r\n                <button\r\n                  className=\"flex flex-row text-blue-500 items-center gap-1 cursor-pointer text-lg font-bold\"\r\n                  onClick={() => handleSave(key as keyof NetworkConfig)}\r\n                >\r\n                  <Save className=\"h-5 w-5\" />\r\n                </button>\r\n              </div>\r\n            )}\r\n          </div>\r\n        ))}\r\n\r\n        {/* Modified: WhatsApp number input with 10-digit numeric validation */}\r\n        <div className=\"flex flex-col gap-2 text-md text-typography-400\">\r\n          <div className=\"flex gap-4 items-center\">\r\n            <div className=\"text-md text-typography-500 w-[400px]\">\r\n              WhatsApp Contact Number\r\n            </div>\r\n            <span className=\"font-semibold text-typography-800 flex gap-2 items-center\">\r\n              <input\r\n                type=\"tel\"\r\n                value={\r\n                  (pendingConfig.wabMobileNumber ??\r\n                    config[0]?.wabMobileNumber ??\r\n                    \"\")\r\n                }\r\n                onChange={(e) => {\r\n                  const value = e.target.value;\r\n                  if (/^\\d{0,10}$/.test(value)) {\r\n                    handleConfigChange(\"wabMobileNumber\", value);\r\n                  }\r\n                }}\r\n                className=\"border border-neutral-400 rounded-md p-1 px-2\"\r\n                disabled={!businessConfigEditable}\r\n                maxLength={10}\r\n                placeholder=\"Enter 10-digit number\"\r\n                pattern=\"[0-9]{10}\"\r\n                onKeyDown={(e) => {\r\n                  if (\r\n                    !/[\\d]/.test(e.key) &&\r\n                    ![\"Backspace\", \"ArrowLeft\", \"ArrowRight\", \"Delete\", \"Tab\"].includes(e.key)\r\n                  ) {\r\n                    e.preventDefault();\r\n                  }\r\n                }}\r\n              />\r\n            </span>\r\n            {businessConfigEditable && visibleSaveButtons[\"wabMobileNumber\"] && (\r\n              <button\r\n                className=\"flex flex-row text-blue-500 items-center gap-1 cursor-pointer text-lg font-bold\"\r\n                onClick={() => {\r\n                  const value = pendingConfig.wabMobileNumber ?? config[0]?.wabMobileNumber ?? \"\";\r\n                  if (/^\\d{10}$/.test(value)) {\r\n                    handleSave(\"wabMobileNumber\" as keyof NetworkConfig);\r\n                  } else {\r\n                    alert(\"Please enter a valid 10-digit WhatsApp number.\");\r\n                  }\r\n                }}\r\n              >\r\n                <Save className=\"h-5 w-5\" />\r\n              </button>\r\n            )}\r\n          </div>\r\n        </div>\r\n        <div className=\"flex flex-col gap-2 text-md text-typography-400\">\r\n          <div className=\"flex gap-4 items-center\">\r\n            <div className=\"text-md text-typography-500 w-[400px]\">\r\n              WhatsApp Pixel Id\r\n            </div>\r\n            <span className=\"font-semibold text-typography-800 flex gap-2 items-center\">\r\n              <input\r\n\r\n                value={\r\n                  (pendingConfig.wabDatasetId ??\r\n                    config[0]?.wabDatasetId ??\r\n                    \"\")\r\n                }\r\n                onChange={(e) => {\r\n                  const value = e.target.value;\r\n\r\n                  handleConfigChange(\"wabDatasetId\", value);\r\n\r\n                }}\r\n                className=\"border border-neutral-400 rounded-md p-1 px-2\"\r\n                disabled={!businessConfigEditable}\r\n                placeholder=\"Enter whatsapp pixel id\"\r\n                onKeyDown={(e) => {\r\n                  if (\r\n                    !/[\\d]/.test(e.key) &&\r\n                    ![\"Backspace\", \"ArrowLeft\", \"ArrowRight\", \"Delete\", \"Tab\"].includes(e.key)\r\n                  ) {\r\n                    e.preventDefault();\r\n                  }\r\n                }}\r\n              />\r\n            </span>\r\n            {businessConfigEditable && visibleSaveButtons[\"wabDatasetId\"] && (\r\n              <button\r\n                className=\"flex flex-row text-blue-500 items-center gap-1 cursor-pointer text-lg font-bold\"\r\n                onClick={() => {\r\n                  const value = pendingConfig.wabDatasetId ?? config[0]?.wabDatasetId ?? \"\";\r\n                  handleSave(\"wabDatasetId\" as keyof NetworkConfig);\r\n\r\n\r\n                }}\r\n              >\r\n                <Save className=\"h-5 w-5\" />\r\n              </button>\r\n            )}\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"flex flex-col gap-4 border rounded-xl border-neutral-400 p-4 shadow\">\r\n        <div className=\"flex flex-row justify-between\">\r\n          <div className=\"text-lg font-semibold text-typography-700\">\r\n            App Configurations\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"text-md text-typography-600 flex gap-4\">\r\n          Home Page:\r\n          {!editingStartPage ? (\r\n            <span className=\"font-semibold text-typography-800\">\r\n              {networkConfig[0]?.defaultStartPage === \"chooseitems\"\r\n                ? \"Item SRP Page\"\r\n                : \"Trips Page\"}\r\n            </span>\r\n          ) : (\r\n            <select\r\n              value={\r\n                (pendingConfig.defaultStartPage ??\r\n                  networkConfig[0]?.defaultStartPage) || \"chooseitems\"\r\n              }\r\n              onChange={(e) =>\r\n                handleConfigChange(\"defaultStartPage\", e.target.value)\r\n              }\r\n              className=\"border border-gray-300 rounded-md px-2 py-1\"\r\n            >\r\n              <option value=\"chooseitems\">Item SRP Page</option>\r\n              <option value=\"home\">Trips Page</option>\r\n            </select>\r\n          )}\r\n          <button onClick={() => setEditingStartPage(!editingStartPage)}>\r\n            {!editingStartPage ? (\r\n              <Pencil className=\"h-5 w-5 text-blue-600 hover:text-blue-800\" />\r\n            ) : (\r\n              <X className=\"h-5 w-5 text-gray-600 hover:text-gray-800\" />\r\n            )}\r\n          </button>\r\n          {visibleSaveButtons.defaultStartPage && (\r\n            <Button\r\n              className=\"bg-blue-600 hover:bg-blue-700 text-white flex items-center gap-2 px-4 py-2 rounded-md transition-all duration-200 shadow-sm\"\r\n              onClick={() => {\r\n                handleSave(\"defaultStartPage\");\r\n                setEditingStartPage(false);\r\n              }}\r\n            >\r\n              <Save className=\"h-5 w-5\" /> Save\r\n            </Button>\r\n          )}\r\n        </div>\r\n\r\n        <div className=\"text-md text-typography-600 flex flex-row gap-2\">\r\n          <div className=\"flex items-center gap-2\">LoginImage:</div>\r\n          {!editingBanners ? (\r\n            networkConfig[0]?.homePageBanner ? (\r\n              networkConfig[0]?.homePageBanner?.split(\",\").map((banner, index) => (\r\n                <div key={index} className=\"flex gap-4 items-center\">\r\n                  <div>{index + 1}</div>\r\n                  <div className=\"p-2 border border-neutral-50 rounded-md shadow-sm\">\r\n                    <img\r\n                      src={banner}\r\n                      className=\"h-32 rounded-md border border-neutral-200 transition-transform hover:scale-105\"\r\n                      alt={`Banner ${index + 1}`}\r\n                    />\r\n                  </div>\r\n                  <button onClick={() => setEditingBanners(true)}>\r\n                    <Pencil className=\"h-5 w-5 text-blue-600 hover:text-blue-800\" />\r\n                  </button>\r\n                  <button\r\n                    onClick={() => {\r\n                      handleSave(\"homePageBanner\", '\"\"');\r\n                    }}\r\n                    className=\"text-red-500 hover:text-red-700\"\r\n                  >\r\n                    <Trash className=\"h-5 w-5\" />\r\n                  </button>\r\n                </div>\r\n              ))\r\n            ) : (\r\n              <div className=\"flex flex-row gap-2\">\r\n                <div className=\"text-md text-typography-200\">No Banner Active</div>\r\n                <button onClick={() => setEditingBanners(true)}>\r\n                  <Pencil className=\"h-5 w-5 text-blue-600 hover:text-blue-800\" />\r\n                </button>\r\n              </div>\r\n            )\r\n          ) : (\r\n            <div className=\"flex flex-col gap-4 bg-gray-50 p-4 rounded-lg shadow-sm\">\r\n              <div className=\"bg-white border border-dashed border-neutral-300 rounded-md p-4\">\r\n                <ImageUploadComponent\r\n                  multiple={false}\r\n                  onChange={(urls: string | string[]) => {\r\n                    setShowSaveBanners(true);\r\n                    const bannerUrls = Array.isArray(urls) ? urls.join(\",\") : urls;\r\n                    handleImageUpload(\"homePageBanner\", bannerUrls);\r\n                    setEditingBanners(false);\r\n                  }}\r\n                />\r\n              </div>\r\n              <div className=\"flex flex-row justify-center items-center gap-4\">\r\n                <Button\r\n                  className=\"bg-gray-500 hover:bg-gray-600 text-white flex items-center gap-2 px-4 py-2 rounded-md transition-all duration-200 shadow-sm\"\r\n                  onClick={() => {\r\n                    setEditingBanners(false);\r\n                    setShowSaveBanners(false);\r\n                    // Modified: Reset pendingConfig and visibleSaveButtons\r\n                    setPendingConfig((prev) => ({ ...prev, homePageBanner: undefined }));\r\n                    setVisibleSaveButtons((prev) => ({ ...prev, homePageBanner: false }));\r\n                  }}\r\n                >\r\n                  <X className=\"h-5 w-5\" /> Cancel\r\n                </Button>\r\n              </div>\r\n            </div>\r\n          )}\r\n          {showSaveBanners && (\r\n            <Button\r\n              className=\"bg-blue-600 hover:bg-blue-700 text-white flex items-center gap-2 px-4 py-2 rounded-md transition-all duration-200 shadow-sm\"\r\n              onClick={() => {\r\n                handleSave(\"homePageBanner\");\r\n                setEditingBanners(false);\r\n                setShowSaveBanners(false);\r\n              }}\r\n            >\r\n              <Save className=\"h-5 w-5\" /> Save\r\n            </Button>\r\n          )}\r\n        </div>\r\n\r\n        <div className=\"text-md text-typography-600 flex gap-4\">\r\n          <div>Playstore App Icon :</div>\r\n          <div className=\"p-2 border border-neutral-50 rounded-md shadow-sm\">\r\n            {!editingPlaystore ? (\r\n              networkConfig[0]?.pwaAppIcon ? (\r\n                <div className=\"flex flex-row gap-1 items-center\">\r\n                  <img\r\n                    src={networkConfig[0]?.pwaAppIcon}\r\n                    className=\"h-32 rounded-md border border-neutral-200 transition-transform hover:scale-105\"\r\n                    alt=\"Playstore Icon\"\r\n                  />\r\n                  <button onClick={() => setEditingPlaystore(true)}>\r\n                    <Pencil className=\"h-5 w-5 text-blue-600 hover:text-blue-800\" />\r\n                  </button>\r\n                  <button\r\n                    onClick={() => {\r\n                      handleSave(\"pwaAppIcon\", '\"\"');\r\n                    }}\r\n                    className=\"text-red-500 hover:text-red-700\"\r\n                  >\r\n                    <Trash className=\"h-5 w-5\" />\r\n                  </button>\r\n                </div>\r\n              ) : (\r\n                <div className=\"text-gray-500 flex items-center gap-2\">\r\n                  No Playstore Icon\r\n                  <button onClick={() => setEditingPlaystore(true)}>\r\n                    <Pencil className=\"h-5 w-5 text-blue-600 hover:text-blue-800\" />\r\n                  </button>\r\n                </div>\r\n              )\r\n            ) : (\r\n              <div className=\"flex flex-col gap-4 bg-gray-50 p-4 rounded-lg shadow-sm\">\r\n                <div className=\"bg-white border border-dashed border-neutral-300 rounded-md p-4\">\r\n                  <ImageUploadComponent\r\n                    onChange={(url: string | string[]) => {\r\n                      setShowSavePlaystore(true);\r\n                      const playstoreUrl = Array.isArray(url) ? url[0] : url;\r\n                      handleImageUpload(\"pwaAppIcon\", playstoreUrl);\r\n                      setEditingPlaystore(false);\r\n                    }}\r\n                  />\r\n                </div>\r\n                <div className=\"flex flex-row justify-center items-center gap-4\">\r\n                  <Button\r\n                    className=\"bg-gray-500 hover:bg-gray-600 text-white flex items-center gap-2 px-4 py-2 rounded-md transition-all duration-200 shadow-sm\"\r\n                    onClick={() => {\r\n                      setEditingPlaystore(false);\r\n                      setShowSavePlaystore(false);\r\n                      // Modified: Reset pendingConfig and visibleSaveButtons\r\n                      setPendingConfig((prev) => ({ ...prev, pwaAppIcon: undefined }));\r\n                      setVisibleSaveButtons((prev) => ({ ...prev, pwaAppIcon: false }));\r\n                    }}\r\n                  >\r\n                    <X className=\"h-5 w-5\" /> Cancel\r\n                  </Button>\r\n                </div>\r\n              </div>\r\n            )}\r\n          </div>\r\n          {showSavePlaystore && (\r\n            <Button\r\n              className=\"bg-blue-600 hover:bg-blue-700 text-white flex items-center gap-2 px-4 py-2 rounded-md transition-all duration-200 shadow-sm\"\r\n              onClick={() => {\r\n                handleSave(\"pwaAppIcon\");\r\n                setEditingPlaystore(false);\r\n                setShowSavePlaystore(false);\r\n              }}\r\n            >\r\n              <Save className=\"h-5 w-5\" /> Save\r\n            </Button>\r\n          )}\r\n        </div>\r\n\r\n        <div className=\"text-md text-typography-600 flex gap-4\">\r\n          <div>In-App Footer Logo :</div>\r\n          <div className=\"p-2 border border-neutral-50 rounded-md shadow-sm\">\r\n            {!editingFooter ? (\r\n              networkConfig[0]?.footerAppIcon ? (\r\n                <div className=\"flex flex-row gap-1 items-center\">\r\n                  <img\r\n                    src={networkConfig[0]?.footerAppIcon}\r\n                    className=\"h-32 rounded-md border border-neutral-200 transition-transform hover:scale-105\"\r\n                    alt=\"Footer Logo\"\r\n                  />\r\n                  <button onClick={() => setEditingFooter(true)}>\r\n                    <Pencil className=\"h-5 w-5 text-blue-600 hover:text-blue-800\" />\r\n                  </button>\r\n                  <button\r\n                    onClick={() => {\r\n                      handleSave(\"footerAppIcon\", '\"\"');\r\n                    }}\r\n                    className=\"text-red-500 hover:text-red-700\"\r\n                  >\r\n                    <Trash className=\"h-5 w-5\" />\r\n                  </button>\r\n                </div>\r\n              ) : (\r\n                <div className=\"text-gray-500 flex items-center gap-2\">\r\n                  No Footer Logo\r\n                  <button onClick={() => setEditingFooter(true)}>\r\n                    <Pencil className=\"h-5 w-5 text-blue-600 hover:text-blue-800\" />\r\n                  </button>\r\n                </div>\r\n              )\r\n            ) : (\r\n              <div className=\"flex flex-col gap-4 bg-gray-50 p-4 rounded-lg shadow-sm\">\r\n                <div className=\"bg-white border border-dashed border-neutral-300 rounded-md p-4\">\r\n                  <ImageUploadComponent\r\n                    onChange={(url: string | string[]) => {\r\n                      setShowSaveFooter(true);\r\n                      const footerUrl = Array.isArray(url) ? url[0] : url;\r\n                      handleImageUpload(\"footerAppIcon\", footerUrl);\r\n                      setEditingFooter(false);\r\n                    }}\r\n                  />\r\n                </div>\r\n                <div className=\"flex flex-row justify-center items-center gap-4\">\r\n                  <Button\r\n                    className=\"bg-gray-500 hover:bg-gray-600 text-white flex items-center gap-2 px-4 py-2 rounded-md transition-all duration-200 shadow-sm\"\r\n                    onClick={() => {\r\n                      setEditingFooter(false);\r\n                      setShowSaveFooter(false);\r\n                      // Modified: Reset pendingConfig and visibleSaveButtons\r\n                      setPendingConfig((prev) => ({ ...prev, footerAppIcon: undefined }));\r\n                      setVisibleSaveButtons((prev) => ({ ...prev, footerAppIcon: false }));\r\n                    }}\r\n                  >\r\n                    <X className=\"h-5 w-5\" /> Cancel\r\n                  </Button>\r\n                </div>\r\n              </div>\r\n            )}\r\n          </div>\r\n          {showSaveFooter && (\r\n            <Button\r\n              className=\"bg-blue-600 hover:bg-blue-700 text-white flex items-center gap-2 px-4 py-2 rounded-md transition-all duration-200 shadow-sm\"\r\n              onClick={() => {\r\n                handleSave(\"footerAppIcon\");\r\n                setEditingFooter(false);\r\n                setShowSaveFooter(false);\r\n              }}\r\n            >\r\n              <Save className=\"h-5 w-5\" /> Save\r\n            </Button>\r\n          )}\r\n        </div>\r\n      </div>\r\n    </div >\r\n\r\n\r\n  )\r\n}"], "names": ["useRef", "useState", "useEffect", "jsxs", "jsx", "Loader2", "NetWorkConfig", "networkConfig", "networkName", "onAttributeUpdate", "businessConfigEditable", "setBusinessConfigEditable", "editingStartPage", "setEditingStartPage", "pendingConfig", "setPendingConfig", "keyMapping", "id", "domain", "businessLogo", "homePageBanner", "pwaAppIcon", "footerAppIcon", "networkId", "multiSeller", "defaultSellerId", "wabEnabled", "wabMobileNumber", "defaultStartPage", "imageBaseUrl", "networkType", "wabDatasetId", "handleBusinessConfig", "useCallback", "prevState", "setVisibleSaveButtons", "setConfig", "BasicConfig", "setBasicConfig", "BasicConFigEdit", "prev", "visibleSaveButtons", "config", "showsave", "setShowsave", "handleConfigChange", "key", "value", "console", "log", "newValue", "isNaN", "Number", "fetcher", "useFetcher", "handleSave", "prevConfig", "<PERSON><PERSON><PERSON><PERSON>", "encodeURIComponent", "error", "alert", "handleImageUpload", "url", "<PERSON><PERSON><PERSON>er", "setEdit<PERSON><PERSON><PERSON>er", "editingBanners", "setEditingBanners", "showSaveFooter", "setShowSave<PERSON>ooter", "showSaveBanners", "setShowSaveBanners", "editingPlaystore", "setEditingPlaystore", "showSavePlaystore", "setShowSavePlaystore", "editingBusinessLogo", "setEditingBusinessLogo", "showSaveBusinessLogo", "setShowSaveBusinessLogo", "Loading", "state", "className", "children", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "loading", "onClick", "Fragment", "X", "Edit", "src", "alt", "Pencil", "Trash", "ImageUploadComponent", "onChange", "businessLogoUrl", "Array", "isArray", "<PERSON><PERSON>", "Save", "type", "e", "target", "disabled", "label", "map", "Label", "RadioGroup", "onValueChange", "val", "RadioGroupItem", "htmlFor", "test", "max<PERSON><PERSON><PERSON>", "placeholder", "pattern", "onKeyDown", "includes", "preventDefault", "split", "banner", "index", "multiple", "urls", "bannerUrls", "join", "playstoreUrl", "footerUrl"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA,MAAM,SAAS,iBAAiB,UAAU;AAAA,EACxC,CAAC,QAAQ,EAAE,GAAG,6CAA6C,KAAK,SAAQ,CAAE;AAAA,EAC1E,CAAC,YAAY,EAAE,QAAQ,iBAAiB,KAAK,SAAQ,CAAE;AAAA,EACvD,CAAC,QAAQ,EAAE,IAAI,MAAM,IAAI,MAAM,IAAI,KAAK,IAAI,MAAM,KAAK,SAAU,CAAA;AACnE,CAAC;ACAD,MAAM,uBAAmD,CAAC,EAAE,UAAU,WAAW,YAAY;AACjF,QAAA,eAAeA,oBAAyB,IAAI;AAClD,QAAM,CAAC,eAAe,gBAAgB,IAAIC,aAAAA,SAAiB,CAAA,CAAE;AAC7D,QAAM,CAAC,aAAa,cAAc,IAAIA,aAAAA,SAAmB,CAAA,CAAE;AAC3D,QAAM,CAAC,aAAa,cAAc,IAAIA,aAAAA,SAAwB,IAAI;AAClE,QAAM,CAAC,WAAW,YAAY,IAAIA,aAAAA,SAAS,KAAK;AAC1C,QAAA,EAAE,UAAU,IAAI,SAAS;AAC/B,QAAM,gBAAgB,WAAkE;AAClF,QAAA,mBAAmB,CAAC,UAA+C;AACnE,UAAM,QAAQ,MAAM,KAAK,MAAM,OAAO,SAAS,EAAE;AAC7C,QAAA,MAAM,WAAW,EAAG;AACxB,mBAAe,IAAI;AACnB,UAAM,gBAAgB,MAAM;AAC5B,UAAM,eAAe,CAAC,cAAc,aAAa,aAAa,YAAY;AAC1E,UAAM,aAAa,MAAM,OAAO,CAAC,SAAS;AAChC,UAAA,KAAK,OAAO,eAAe;AACzB,uBAAe,gCAAgC;AACxC,eAAA;AAAA,MAAA;AAEb,UAAI,CAAC,aAAa,SAAS,KAAK,IAAI,GAAG;AACjC,uBAAe,mDAAmD;AAC3D,eAAA;AAAA,MAAA;AAEN,aAAA;AAAA,IAAA,CACZ;AAEG,QAAA,WAAW,WAAW,EAAG;AAEZ,qBAAA,WAAW,CAAC,GAAG,eAAe,GAAG,UAAU,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC;AAChE,mBAAA,WAAW,CAAC,GAAG,aAAa,GAAG,WAAW,IAAI,CAAC,SAAS,IAAI,gBAAgB,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,gBAAgB,WAAW,CAAC,CAAC,CAAC,CAAC;AAAA,EAC/I;AAEA,QAAM,eAAe,MAAM;AACjB,QAAA,cAAc,WAAW,EAAG;AAEhC,iBAAa,IAAI;AACX,UAAA,WAAW,IAAI,SAAS;AACrB,aAAA,OAAO,WAAW,aAAa;AAE1B,kBAAA,QAAQ,CAAC,SAAS;AAC1B,eAAS,OAAO,QAAQ,MAAM,KAAK,IAAI;AAAA,IAAA,CAC5C;AAED,kBAAc,OAAO,UAAU;AAAA,MACzB,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,SAAS;AAAA,IAAA,CACd;AAAA,EACP;AAEAC,eAAAA,UAAU,MAAM;AACV,QAAI,cAAc,MAAM;AACd,UAAA,cAAc,KAAK,OAAO;AACT,uBAAA,cAAc,KAAK,KAAK;AAAA,MAAA,WAClC,cAAc,KAAK,SAAS;AACjC,cAAM,eAAe,WAAW,CAAC,GAAG,aAAa,cAAc,KAAK,OAAO,IAAI,CAAC,cAAc,KAAK,OAAO;AAE1G,iBAAS,WAAW,eAAe,cAAc,KAAK,OAAO;AAG7D,yBAAiB,CAAA,CAAE;AACnB,uBAAe,YAAY;AAC3B,uBAAe,IAAI;AACnB,YAAI,aAAa,QAAsB,cAAA,QAAQ,QAAQ;AAAA,MAAA;AAE7D,mBAAa,KAAK;AAAA,IAAA;AAAA,EAE3B,GAAA,CAAC,cAAc,MAAM,QAAQ,CAAC;AAE3B,QAAA,oBAAoB,CAAC,OAAe,MAAyB;AAC7D,2BAAG;AACH,2BAAG;AAEH,UAAM,eAAe,cAAc,OAAO,CAAC,GAAG,MAAM,MAAM,KAAK;AAC/D,UAAM,kBAAkB,YAAY,OAAO,CAAC,GAAG,MAAM,MAAM,KAAK;AAEhE,qBAAiB,YAAY;AAC7B,mBAAe,eAAe;AAErB,aAAA,WAAW,kBAAkB,EAAE;AAAA,EAC9C;AAGM,SAAAC,kCAAA,KAAC,OAAI,EAAA,WAAU,0IACT,UAAA;AAAA,IAACC,kCAAA,IAAA,OAAA,EAAM,WAAU,kDAAiD,UAAY,gBAAA;AAAA,IAE9ED,kCAAAA,KAAC,OAAI,EAAA,WAAU,yCAER,UAAA;AAAA,MAAY,YAAA,SAAS,KAChBC,kCAAAA,IAAC,OAAI,EAAA,WAAU,uCACR,UAAY,YAAA,IAAI,CAAC,KAAK,UACjBD,kCAAA;AAAA,QAAC;AAAA,QAAA;AAAA,UAEK,WAAU;AAAA,UAEV,UAAA;AAAA,YAAAC,kCAAA;AAAA,cAAC;AAAA,cAAA;AAAA,gBACK,KAAK;AAAA,gBACL,KAAI;AAAA,gBACJ,WAAU;AAAA,cAAA;AAAA,YAChB;AAAA,YACAA,kCAAA;AAAA,cAAC;AAAA,cAAA;AAAA,gBACK,SAAS,CAAC,MAAM,kBAAkB,OAAO,CAAC;AAAA,gBAC1C,WAAU;AAAA,gBAEV,UAAAA,kCAAAA,IAAC,OAAM,EAAA,WAAU,UAAU,CAAA;AAAA,cAAA;AAAA,YAAA;AAAA,UACjC;AAAA,QAAA;AAAA,QAbK;AAAA,MAehB,CAAA,GACP;AAAA,MAIND,kCAAAA,KAAC,OAAI,EAAA,WAAU,2CACT,UAAA;AAAA,QAAAC,kCAAA;AAAA,UAAC;AAAA,UAAA;AAAA,YACK,MAAK;AAAA,YACL,UAAQ;AAAA,YACR,OAAO,cAAc,SAAS,IAAI,GAAG,cAAc,MAAM,sBAAsB;AAAA,YAC/E,WAAU;AAAA,YACV,SAAS,MAAA;;AAAM,wCAAa,YAAb,mBAAsB;AAAA;AAAA,UAAM;AAAA,QACjD;AAAA,QACAA,kCAAA;AAAA,UAAC;AAAA,UAAA;AAAA,YACK,KAAK;AAAA,YACL,MAAK;AAAA,YACL,QAAO;AAAA,YACP;AAAA,YACA,UAAU;AAAA,YACV,WAAU;AAAA,UAAA;AAAA,QAChB;AAAA,QACAD,kCAAA;AAAA,UAAC;AAAA,UAAA;AAAA,YACK,MAAK;AAAA,YACL,SAAS,cAAc,SAAS,IAAI,eAAe;;AAAM,wCAAa,YAAb,mBAAsB;AAAA;AAAA,YAC/E,UAAU;AAAA,YACV,WAAW,2BAA2B,YAC9B,mCACA,+BACF;AAAA,YAEL,UAAA;AAAA,cACK,YAAAC,kCAAA,IAACC,gBAAQ,WAAU,uBAAA,CAAuB,IAEzCD,kCAAAA,IAAA,QAAA,EAAO,WAAU,UAAU,CAAA;AAAA,cAEjC,YAAY,iBAAiB,cAAc,SAAS,IAAI,WAAW;AAAA,YAAA;AAAA,UAAA;AAAA,QAAA;AAAA,MAC1E,GACN;AAAA,MAGC,eACKA,kCAAA,IAAC,KAAE,EAAA,WAAU,uDAAuD,UAAY,YAAA,CAAA;AAAA,IAAA,EAE5F,CAAA;AAAA,EAAA,GACN;AAEZ;ACrJA,SAAwBE,cAAc;AAAA,EACpCC;AAAAA,EACAC;AAAAA,EACAC;AACF,GAAuB;;AACrB,QAAM,CAACC,wBAAwBC,yBAAyB,IAAIV,aAAAA,SAAS,KAAK;AAC1E,QAAM,CAACW,kBAAkBC,mBAAmB,IAAIZ,aAAAA,SAAS,KAAK;AAC9D,QAAM,CAACa,eAAeC,gBAAgB,IAAId,aAAAA,SAAiC,CAAA,CAAE;AAC7E,QAAMe,aAAkD;AAAA,IACtDC,IAAI;AAAA,IACJC,QAAQ;AAAA,IACRC,cAAc;AAAA,IACdC,gBAAgB;AAAA,IAChBC,YAAY;AAAA,IACZC,eAAe;AAAA,IACfC,WAAW;AAAA,IACXC,aAAa;AAAA,IACbC,iBAAiB;AAAA,IACjBC,YAAY;AAAA,IACZC,iBAAiB;AAAA,IACjBC,kBAAkB;AAAA,IAClBC,cAAc;AAAA,IACdC,aAAa;AAAA,IACbC,cAAc;AAAA,EAChB;AAGM,QAAAC,uBAAuBC,aAAAA,YAAY,MAAM;AAC7CtB,8BAA2BuB,eAAc;AACvC,UAAIA,WAAW;AACbnB,yBAAiB,CAAA,CAAE;AACnBoB,8BAAsB,CAAA,CAAE;AAAA,MAC1B;AACA,aAAO,CAACD;AAAAA,IACV,CAAC;AAAA,EACH,GAAG,EAAE;AAELhC,eAAAA,UAAU,MAAM;AACdkC,cAAU7B,aAAa;AAAA,EACzB,GAAG,CAACA,aAAa,CAAC;AAElB,QAAM,CAAC8B,aAAaC,cAAc,IAAIrC,aAAAA,SAAS,KAAK;AAG9C,QAAAsC,kBAAkBN,aAAAA,YAAY,MAAM;AACxCK,mBAAgBE,UAAS;AACvB,UAAIA,MAAM;AACRzB,yBAAiB,CAAA,CAAE;AACnBoB,8BAAsB,CAAA,CAAE;AAAA,MAC1B;AACA,aAAO,CAACK;AAAAA,IACV,CAAC;AAAA,EACH,GAAG,EAAE;AAEL,QAAM,CAACC,oBAAoBN,qBAAqB,IAAIlC,aAAAA,SAElD,CAAA,CAAE;AACJ,QAAM,CAACyC,QAAQN,SAAS,IAAInC,aAAAA,SAA0BM,aAAa;AACnE,QAAM,CAACoC,UAAUC,WAAW,IAAI3C,aAAAA,SAAS,KAAK;AAExC,QAAA4C,qBAAqBA,CACzBC,KACAC,UACG;AACKC,YAAAC,IAAIF,OAAOD,KAAK,yBAAyB;AAEjD/B,qBAAkByB,UAAS;AACzB,UAAIU,WAA2CH;AAE/C,UAAI,OAAOA,UAAU,aAAaA,UAAU,SAASA,UAAU,OAAO;AACpEG,mBAAWH,UAAU;AAAA,MACvB,WAAW,OAAOA,UAAU,YAAY,CAACI,MAAMC,OAAOL,KAAK,CAAC,GAAG;AAC7DG,mBAAWE,OAAOL,KAAK;AAAA,MACd,WAAAA,UAAU,QAAQA,UAAU,QAAW;AACrCG,mBAAA;AAAA,MACb;AAEO,aAAA;AAAA,QACL,GAAGV;AAAAA,QACH,CAACM,GAAG,GAAGI;AAAAA,MACT;AAAA,IACF,CAAC;AAEDf,0BAAuBK,WAAU;AAAA,MAC/B,GAAGA;AAAAA,MACH,CAACM,GAAG,GAAG;AAAA,IACT,EAAE;AAAA,EACJ;AAEA,QAAMO,UAAUC,WAAW;AAErB,QAAAC,aAAa,OACjBT,KACAC,UACG;;AACC,QAAA;AACFX,gBAAWoB,iBAAgB;AAAA,QACzB,GAAGA;AAAAA,QACH,CAACV,GAAG,GAAGC,UAAU,SAAYA,QAAQjC,cAAcgC,GAAG,KAAK;AAAA,MAC7D,EAAE;AAEFX,4BAAuBK,WAAU;AAAA,QAC/B,GAAGA;AAAAA,QACH,CAACM,GAAG,GAAG;AAAA,MACT,EAAE;AACFF,kBAAY,KAAK;AAEjB,UAAIa,WAAW;AAEf,YAAMP,WACJH,UAAU,SAAYA,QAAQjC,cAAcgC,GAAG,KAAK;AAElD,UAAA;AACI,cAAArC,kBACJO,WAAW8B,GAA0B,GACrC,OAAOI,aAAa,WAAWQ,mBAAmBR,QAAQ,IAAIA,YAC9D3C,MAAAA,cAAc,CAAC,MAAfA,gBAAAA,IAAkBU,OAAM,CAC1B;AAAA,eACO0C,OAAO;AACdX,gBAAQW,MAAM,oBAAoBb,GAAG,KAAKa,KAAK;AACpCF,mBAAA;AAAA,MACb;AAEA,UAAIA,UAAU;AACZG,cAAM,qDAAqD;AAAA,MAC7D,OAAO;AACL7C,yBAAiB,CAAA,CAAE;AAAA,MACrB;AAAA,aACO4C,OAAO;AACNX,cAAAW,MAAM,wBAAwBA,KAAK;AAC3CC,YAAM,kDAAkD;AAAA,IAC1D;AAAA,EACF;AAEM,QAAAC,oBAAoBA,CAACf,KAA0BgB,QAAgB;AACnE/C,qBAAkByB,WAAU;AAAA,MAC1B,GAAGA;AAAAA,MACH,CAACM,GAAG,GAAGgB;AAAAA,IACT,EAAE;AAEF3B,0BAAuBK,WAAU;AAAA,MAC/B,GAAGA;AAAAA,MACH,CAACM,GAAG,GAAG;AAAA,IACT,EAAE;AAAA,EACJ;AAEA,QAAM,CAACiB,eAAeC,gBAAgB,IAAI/D,aAAAA,SAAS,KAAK;AACxD,QAAM,CAACgE,gBAAgBC,iBAAiB,IAAIjE,aAAAA,SAAS,KAAK;AAC1D,QAAM,CAACkE,gBAAgBC,iBAAiB,IAAInE,aAAAA,SAAS,KAAK;AAC1D,QAAM,CAACoE,iBAAiBC,kBAAkB,IAAIrE,aAAAA,SAAS,KAAK;AAC5D,QAAM,CAACsE,kBAAkBC,mBAAmB,IAAIvE,aAAAA,SAAS,KAAK;AAC9D,QAAM,CAACwE,mBAAmBC,oBAAoB,IAAIzE,aAAAA,SAAS,KAAK;AAChE,QAAM,CAAC0E,qBAAqBC,sBAAsB,IAAI3E,aAAAA,SAAS,KAAK;AACpE,QAAM,CAAC4E,sBAAsBC,uBAAuB,IAAI7E,aAAAA,SAAS,KAAK;AAEhE,QAAA8E,UAAU1B,QAAQ2B,UAAU;AAGhC,SAAA7E,kCAAAA,KAAC,OAAI;AAAA,IAAA8E,WAAU;AAAA,IACZC,UAAA,CAAWH,WAAA3E,kCAAA,IAAC+E,eAAc;AAAA,MAAAC,SAASL;AAAAA,IAAS,CAAA,GAC7C5E,kCAAA,KAAC,OAAI;AAAA,MAAA8E,WAAU;AAAA,MACbC,UAAA,CAAC/E,kCAAA,KAAA,OAAA;AAAA,QAAI8E,WAAU;AAAA,QACbC,UAAA,CAAC9E,kCAAA,IAAA,OAAA;AAAA,UAAI6E,WAAU;AAAA,UAA4CC,UAE3D;AAAA,QAAA,CAAA,GACA9E,kCAAA,IAAC,UAAA;AAAA,UACC6E,WAAU;AAAA,UACVI,SAASA,MAAM9C,gBAAgB;AAAA,UAE9B2C,wBAEG/E,kCAAA,KAAAmF,4BAAA;AAAA,YAAAJ,UAAA,CAAC9E,kCAAA,IAAAmF,GAAA;AAAA,cAAEN,WAAU;AAAA,YAAU,CAAA,GAAE,QAAA;AAAA,UAAA,CAE3B,IAGE9E,kCAAA,KAAAmF,4BAAA;AAAA,YAAAJ,UAAA,CAAC9E,kCAAA,IAAAoF,WAAA;AAAA,cAAKP,WAAU;AAAA,YAAU,CAAA,GAAE,MAAA;AAAA,UAE9B,CAAA;AAAA,QAAA,CAEJ,CAAA;AAAA,MACF,CAAA,GACA9E,kCAAA,KAAC,OAAI;AAAA,QAAA8E,WAAU;AAAA,QACbC,UAAA,CAAC/E,kCAAA,KAAA,OAAA;AAAA,UAAI8E,WAAU;AAAA,UACZC,UAAA,CAAC,CAAAP,wBACApE,mBAAc,CAAC,MAAfA,mBAAkBY,gBACfhB,kCAAA,KAAA,OAAA;AAAA,YAAI8E,WAAU;AAAA,YACbC,UAAA,CAAA9E,kCAAA,IAAC,OAAA;AAAA,cACCqF,MAAKlF,mBAAc,CAAC,MAAfA,mBAAkBY;AAAAA,cACvB8D,WAAU;AAAA,cACVS,KAAI;AAAA,YAAA,CACN,GACAtF,kCAAA,IAAC,UAAO;AAAA,cAAAiF,SAASA,MAAMT,uBAAuB,IAAI;AAAA,cAChDM,UAAC9E,kCAAA,IAAAuF,QAAA;AAAA,gBAAOV,WAAU;AAAA,cAA4C,CAAA;AAAA,YAChE,CAAA,GACA7E,kCAAA,IAAC,UAAA;AAAA,cACCiF,SAASA,MAAM;AACb9B,2BAAW,gBAAgB,IAAI;AAAA,cACjC;AAAA,cACA0B,WAAU;AAAA,cAEVC,UAAA9E,kCAAA,IAACwF,OAAM;AAAA,gBAAAX,WAAU;AAAA,cAAU,CAAA;AAAA,YAAA,CAC7B,CAAA;AAAA,UAAA,CACF,IAEA9E,kCAAA,KAAC,OAAI;AAAA,YAAA8E,WAAU;AAAA,YAAwCC,UAAA,CAAA,oBAErD9E,kCAAA,IAAC,UAAO;AAAA,cAAAiF,SAASA,MAAMT,uBAAuB,IAAI;AAAA,cAChDM,UAAC9E,kCAAA,IAAAuF,QAAA;AAAA,gBAAOV,WAAU;AAAA,cAA4C,CAAA;AAAA,YAChE,CAAA,CAAA;AAAA,UAAA,CACF,IAGF9E,kCAAA,KAAC,OAAI;AAAA,YAAA8E,WAAU;AAAA,YACbC,UAAA,CAAC9E,kCAAA,IAAA,OAAA;AAAA,cAAI6E,WAAU;AAAA,cACbC,UAAA9E,kCAAA,IAACyF,sBAAA;AAAA,gBACCC,UAAWhC,SAA2B;AACpCgB,0CAAwB,IAAI;AAC5B,wBAAMiB,kBAAkBC,MAAMC,QAAQnC,GAAG,IAAIA,IAAI,CAAC,IAAIA;AACtDD,oCAAkB,gBAAgBkC,eAAe;AACjDnB,yCAAuB,KAAK;AAAA,gBAC9B;AAAA,cACF,CAAA;AAAA,YACF,CAAA,GACAxE,kCAAA,IAAC,OAAI;AAAA,cAAA6E,WAAU;AAAA,cACbC,UAAA/E,kCAAA,KAAC+F,QAAA;AAAA,gBACCjB,WAAU;AAAA,gBACVI,SAASA,MAAM;AACbT,yCAAuB,KAAK;AAC5BE,0CAAwB,KAAK;AAE7B/D,mCAAkByB,WAAU;AAAA,oBAAE,GAAGA;AAAAA,oBAAMrB,cAAc;AAAA,kBAAU,EAAE;AACjEgB,wCAAuBK,WAAU;AAAA,oBAAE,GAAGA;AAAAA,oBAAMrB,cAAc;AAAA,kBAAM,EAAE;AAAA,gBACpE;AAAA,gBAEA+D,UAAA,CAAC9E,kCAAA,IAAAmF,GAAA;AAAA,kBAAEN,WAAU;AAAA,gBAAU,CAAA,GAAE,SAAA;AAAA,cAC3B,CAAA;AAAA,YACF,CAAA,CAAA;AAAA,WACF,GAEDJ,wBACCzE,kCAAA,IAAC,OAAI;AAAA,YAAA6E,WAAU;AAAA,YACbC,UAAA/E,kCAAA,KAAC+F,QAAA;AAAA,cACCjB,WAAU;AAAA,cACVI,SAASA,MAAM;AACb9B,2BAAW,cAAc;AACzBqB,uCAAuB,KAAK;AAC5BE,wCAAwB,KAAK;AAAA,cAC/B;AAAA,cAEAI,UAAA,CAAC9E,kCAAA,IAAA+F,MAAA;AAAA,gBAAKlB,WAAU;AAAA,cAAU,CAAA,GAAE,OAAA;AAAA,YAC9B,CAAA;AAAA,UACF,CAAA,CAAA;AAAA,QAEJ,CAAA,GACA9E,kCAAA,KAAC,OAAI;AAAA,UAAA8E,WAAU;AAAA,UACbC,UAAA,CAAC/E,kCAAA,KAAA,OAAA;AAAA,YAAI8E,WAAU;AAAA,YAA8BC,UAAA,CAAA,kBAC7B3E,mBAAc,CAAC,MAAfA,mBAAkBgB,SAAA;AAAA,UAClC,CAAA,GACApB,kCAAA,KAAC,OAAI;AAAA,YAAA8E,WAAU;AAAA,YAA8BC,UAAA,CAAA,mBAC3B1E,WAAA;AAAA,UAClB,CAAA,CAAA;AAAA,QACF,CAAA,CAAA;AAAA,MACF,CAAA,GACAL,kCAAA,KAAC,OAAI;AAAA,QAAA8E,WAAU;AAAA,QACbC,UAAA,CAAC/E,kCAAA,KAAA,OAAA;AAAA,UAAI8E,WAAU;AAAA,UAA8BC,UAAA,CAAA,iBAC9B3E,mBAAc,CAAC,MAAfA,mBAAkBU,EAAA;AAAA,QACjC,CAAA,yCACC,OAAI;AAAA,UAAAgE,WAAU;AAAA,UACbC,UAAC/E,kCAAA,KAAA,OAAA;AAAA,YAAI8E,WAAU;AAAA,YACbC,UAAA,CAAC9E,kCAAA,IAAA,OAAA;AAAA,cAAI6E,WAAU;AAAA,cAAmCC,UAElD;AAAA,YAAA,CAAA,GACA9E,kCAAA,IAAC,QAAK;AAAA,cAAA6E,WAAU;AAAA,cACdC,UAAA9E,kCAAA,IAAC,SAAA;AAAA,gBACCgG,MAAK;AAAA,gBACLrD,QAAQjC,cAAcI,YAAUX,mBAAc,CAAC,MAAfA,mBAAkBW,YAAW;AAAA,gBAC7D4E,UAAWO,OAAMxD,mBAAmB,UAAUwD,EAAEC,OAAOvD,KAAK;AAAA,gBAC5DkC,WAAU;AAAA,gBACVsB,UAAU,CAAClE;AAAAA,cACb,CAAA;AAAA,YACF,CAAA,GACCA,eAAeI,mBAAmB,QAAQ,KACzCrC,kCAAAA,IAAC,UAAA;AAAA,cACC6E,WAAU;AAAA,cACVI,SAASA,MAAM9B,WAAW,QAA+B;AAAA,cAEzD2B,UAAA9E,kCAAA,IAAC+F,MAAK;AAAA,gBAAAlB,WAAU;AAAA,cAAU,CAAA;AAAA,YAAA,CAC5B,CAAA;AAAA,UAEJ,CAAA;AAAA,QACF,CAAA,CAAA;AAAA,MACF,CAAA,CAAA;AAAA,IACF,CAAA,GAEA9E,kCAAA,KAAC,OAAI;AAAA,MAAA8E,WAAU;AAAA,MACbC,UAAA,CAAC/E,kCAAA,KAAA,OAAA;AAAA,QAAI8E,WAAU;AAAA,QACbC,UAAA,CAAC9E,kCAAA,IAAA,OAAA;AAAA,UAAI6E,WAAU;AAAA,UAA6BC,UAE5C;AAAA,QAAA,CAAA,GACA9E,kCAAA,IAAC,UAAA;AAAA,UACC6E,WAAU;AAAA,UACVI,SAASA,MAAMrD,qBAAqB;AAAA,UAEnCkD,mCAEG/E,kCAAA,KAAAmF,4BAAA;AAAA,YAAAJ,UAAA,CAAC9E,kCAAA,IAAAmF,GAAA;AAAA,cAAEN,WAAU;AAAA,YAAU,CAAA,GAAE,QAAA;AAAA,UAAA,CAE3B,IAGE9E,kCAAA,KAAAmF,4BAAA;AAAA,YAAAJ,UAAA,CAAC9E,kCAAA,IAAAoF,WAAA;AAAA,cAAKP,WAAU;AAAA,YAAU,CAAA,GAAE,MAAA;AAAA,UAE9B,CAAA;AAAA,QAAA,CAEJ,CAAA;AAAA,MACF,CAAA,GACC,CACC;AAAA,QAAEuB,OAAO;AAAA,QAAoC1D,KAAK;AAAA,MAAc,GAChE;AAAA,QAAE0D,OAAO;AAAA,QAA2B1D,KAAK;AAAA,MAAa,CAAA,EACtD2D,IAAI,CAAC;AAAA,QAAED;AAAAA,QAAO1D;AAAAA,MAAI,MAClB3C;;AAAAA,iDAAAA,KAAC,OAAA;AAAA,UAEC8E,WAAU;AAAA,UAEVC,UAAA,CAAC9E,kCAAA,IAAAsG,OAAA;AAAA,YAAMzB,WAAU;AAAA,YACdC,UACHsB;AAAAA,UAAA,CAAA,GACArG,kCAAA,KAACwG,YAAA;AAAA,YACC5D,OACGjC,cAAcgC,GAA0B,OACvCJ,MAAAA,OAAO,CAAC,MAARA,gBAAAA,IAAYI,QACV,QACA;AAAA,YAEN8D,eAAgBC,SACdhE,mBAAmBC,KAA4B+D,GAAG;AAAA,YAEpD5B,WAAU;AAAA,YACVsB,UAAU,CAAC7F;AAAAA,YAEXwE,UAAA,CAAC/E,kCAAA,KAAA,OAAA;AAAA,cAAI8E,WAAU;AAAA,cACbC,UAAA,CAAA9E,kCAAA,IAAC0G;gBAAe7F,IAAI,GAAG6B,GAAG;AAAA,gBAAQC,OAAM;AAAA,cAAM,CAAA,yCAC7C2D,OAAM;AAAA,gBAAAK,SAAS,GAAGjE,GAAG;AAAA,gBAAQoC,UAAG;AAAA,cAAA,CAAA,CAAA;AAAA,YACnC,CAAA,GACA/E,kCAAA,KAAC,OAAI;AAAA,cAAA8E,WAAU;AAAA,cACbC,UAAA,CAAA9E,kCAAA,IAAC0G;gBAAe7F,IAAI,GAAG6B,GAAG;AAAA,gBAAOC,OAAM;AAAA,cAAK,CAAA,yCAC3C2D,OAAM;AAAA,gBAAAK,SAAS,GAAGjE,GAAG;AAAA,gBAAOoC,UAAE;AAAA,cAAA,CAAA,CAAA;AAAA,YACjC,CAAA,CAAA;AAAA,UAAA,CACF,GACCxE,0BAA0B+B,mBAAmBK,GAAG,KAC9C1C,kCAAAA,IAAA,OAAA;AAAA,YAAI6E,WAAU;AAAA,YACbC,UAAA9E,kCAAA,IAAC,UAAA;AAAA,cACC6E,WAAU;AAAA,cACVI,SAASA,MAAM9B,WAAWT,GAA0B;AAAA,cAEpDoC,UAAA9E,kCAAA,IAAC+F,MAAK;AAAA,gBAAAlB,WAAU;AAAA,cAAU,CAAA;AAAA,YAC5B,CAAA;AAAA,UACF,CAAA,CAAA;AAAA,QAAA,GApCGnC,GAsCP;AAAA,OACD,yCAGA,OAAI;AAAA,QAAAmC,WAAU;AAAA,QACbC,UAAC/E,kCAAA,KAAA,OAAA;AAAA,UAAI8E,WAAU;AAAA,UACbC,UAAA,CAAC9E,kCAAA,IAAA,OAAA;AAAA,YAAI6E,WAAU;AAAA,YAAwCC,UAEvD;AAAA,UAAA,CAAA,GACA9E,kCAAA,IAAC,QAAK;AAAA,YAAA6E,WAAU;AAAA,YACdC,UAAA9E,kCAAA,IAAC,SAAA;AAAA,cACCgG,MAAK;AAAA,cACLrD,OACGjC,cAAca,qBACbe,YAAO,CAAC,MAARA,mBAAWf,oBACX;AAAA,cAEJmE,UAAWO,OAAM;AACT,sBAAAtD,QAAQsD,EAAEC,OAAOvD;AACnB,oBAAA,aAAaiE,KAAKjE,KAAK,GAAG;AAC5BF,qCAAmB,mBAAmBE,KAAK;AAAA,gBAC7C;AAAA,cACF;AAAA,cACAkC,WAAU;AAAA,cACVsB,UAAU,CAAC7F;AAAAA,cACXuG,WAAW;AAAA,cACXC,aAAY;AAAA,cACZC,SAAQ;AAAA,cACRC,WAAYf,OAAM;AAChB,oBACE,CAAC,OAAOW,KAAKX,EAAEvD,GAAG,KAClB,CAAC,CAAC,aAAa,aAAa,cAAc,UAAU,KAAK,EAAEuE,SAAShB,EAAEvD,GAAG,GACzE;AACAuD,oBAAEiB,eAAe;AAAA,gBACnB;AAAA,cACF;AAAA,YACF,CAAA;AAAA,UACF,CAAA,GACC5G,0BAA0B+B,mBAAmB,iBAAiB,KAC7DrC,kCAAAA,IAAC,UAAA;AAAA,YACC6E,WAAU;AAAA,YACVI,SAASA,MAAM;;AACb,oBAAMtC,QAAQjC,cAAca,qBAAmBe,MAAAA,OAAO,CAAC,MAARA,gBAAAA,IAAWf,oBAAmB;AACzE,kBAAA,WAAWqF,KAAKjE,KAAK,GAAG;AAC1BQ,2BAAW,iBAAwC;AAAA,cACrD,OAAO;AACLK,sBAAM,gDAAgD;AAAA,cACxD;AAAA,YACF;AAAA,YAEAsB,UAAA9E,kCAAA,IAAC+F,MAAK;AAAA,cAAAlB,WAAU;AAAA,YAAU,CAAA;AAAA,UAAA,CAC5B,CAAA;AAAA,QAEJ,CAAA;AAAA,MACF,CAAA,yCACC,OAAI;AAAA,QAAAA,WAAU;AAAA,QACbC,UAAC/E,kCAAA,KAAA,OAAA;AAAA,UAAI8E,WAAU;AAAA,UACbC,UAAA,CAAC9E,kCAAA,IAAA,OAAA;AAAA,YAAI6E,WAAU;AAAA,YAAwCC,UAEvD;AAAA,UAAA,CAAA,GACA9E,kCAAA,IAAC,QAAK;AAAA,YAAA6E,WAAU;AAAA,YACdC,UAAA9E,kCAAA,IAAC,SAAA;AAAA,cAEC2C,OACGjC,cAAciB,kBACbW,YAAO,CAAC,MAARA,mBAAWX,iBACX;AAAA,cAEJ+D,UAAWO,OAAM;AACT,sBAAAtD,QAAQsD,EAAEC,OAAOvD;AAEvBF,mCAAmB,gBAAgBE,KAAK;AAAA,cAE1C;AAAA,cACAkC,WAAU;AAAA,cACVsB,UAAU,CAAC7F;AAAAA,cACXwG,aAAY;AAAA,cACZE,WAAYf,OAAM;AAChB,oBACE,CAAC,OAAOW,KAAKX,EAAEvD,GAAG,KAClB,CAAC,CAAC,aAAa,aAAa,cAAc,UAAU,KAAK,EAAEuE,SAAShB,EAAEvD,GAAG,GACzE;AACAuD,oBAAEiB,eAAe;AAAA,gBACnB;AAAA,cACF;AAAA,YACF,CAAA;AAAA,UACF,CAAA,GACC5G,0BAA0B+B,mBAAmB,cAAc,KAC1DrC,kCAAAA,IAAC,UAAA;AAAA,YACC6E,WAAU;AAAA,YACVI,SAASA,MAAM;;AACCvE,4BAAciB,kBAAgBW,MAAAA,OAAO,CAAC,MAARA,gBAAAA,IAAWX,iBAAgB;AACvEwB,yBAAW,cAAqC;AAAA,YAGlD;AAAA,YAEA2B,UAAA9E,kCAAA,IAAC+F,MAAK;AAAA,cAAAlB,WAAU;AAAA,YAAU,CAAA;AAAA,UAAA,CAC5B,CAAA;AAAA,QAEJ,CAAA;AAAA,MACF,CAAA,CAAA;AAAA,IACF,CAAA,GAEA9E,kCAAA,KAAC,OAAI;AAAA,MAAA8E,WAAU;AAAA,MACbC,UAAA,CAAC9E,kCAAA,IAAA,OAAA;AAAA,QAAI6E,WAAU;AAAA,QACbC,UAAA9E,kCAAA,IAAC;UAAI6E,WAAU;AAAA,UAA4CC;QAE3D,CAAA;AAAA,MACF,CAAA,GAEA/E,kCAAA,KAAC,OAAI;AAAA,QAAA8E,WAAU;AAAA,QAAyCC,UAAA,CAAA,cAErD,CAACtE,mBACCR,kCAAAA,IAAA,QAAA;AAAA,UAAK6E,WAAU;AAAA,UACbC,YAAc3E,mBAAA,CAAC,MAADA,mBAAIqB,sBAAqB,gBACpC,kBACA;AAAA,SACN,IAEAzB,kCAAA,KAAC,UAAA;AAAA,UACC4C,QACGjC,cAAcc,sBACbrB,mBAAc,CAAC,MAAfA,mBAAkBqB,sBAAqB;AAAA,UAE3CkE,UAAWO,OACTxD,mBAAmB,oBAAoBwD,EAAEC,OAAOvD,KAAK;AAAA,UAEvDkC,WAAU;AAAA,UAEVC,UAAA,CAAC9E,kCAAA,IAAA,UAAA;AAAA,YAAO2C,OAAM;AAAA,YAAcmC,UAAa;AAAA,UAAA,CAAA,GACxC9E,kCAAA,IAAA,UAAA;AAAA,YAAO2C,OAAM;AAAA,YAAOmC,UAAU;AAAA,UAAA,CAAA,CAAA;AAAA,QAAA,CACjC,yCAED,UAAO;AAAA,UAAAG,SAASA,MAAMxE,oBAAoB,CAACD,gBAAgB;AAAA,UACzDsE,UAAA,CAACtE,mBACCR,kCAAA,IAAAuF,QAAA;AAAA,YAAOV,WAAU;AAAA,UAA4C,CAAA,0CAE7DM,GAAE;AAAA,YAAAN,WAAU;AAAA,UAA4C,CAAA;AAAA,QAE7D,CAAA,GACCxC,mBAAmBb,oBAClBzB,kCAAAA,KAAC+F,QAAA;AAAA,UACCjB,WAAU;AAAA,UACVI,SAASA,MAAM;AACb9B,uBAAW,kBAAkB;AAC7B1C,gCAAoB,KAAK;AAAA,UAC3B;AAAA,UAEAqE,UAAA,CAAC9E,kCAAA,IAAA+F,MAAA;AAAA,YAAKlB,WAAU;AAAA,UAAU,CAAA,GAAE,OAAA;AAAA,QAAA,CAC9B,CAAA;AAAA,MAEJ,CAAA,GAEA9E,kCAAA,KAAC,OAAI;AAAA,QAAA8E,WAAU;AAAA,QACbC,UAAA,CAAC9E,kCAAA,IAAA,OAAA;AAAA,UAAI6E,WAAU;AAAA,UAA0BC,UAAW;AAAA,QAAA,CAAA,GACnD,CAACjB,mBACA1D,mBAAc,CAAC,MAAfA,mBAAkBa,mBAChBb,yBAAc,CAAC,MAAfA,mBAAkBa,mBAAlBb,mBAAkCgH,MAAM,KAAKd,IAAI,CAACe,QAAQC,UACvDtH,kCAAA,KAAA,OAAA;AAAA,UAAgB8E,WAAU;AAAA,UACzBC,UAAA,CAAC9E,kCAAA,IAAA,OAAA;AAAA,YAAK8E,kBAAQ;AAAA,UAAE,CAAA,GAChB9E,kCAAA,IAAC,OAAI;AAAA,YAAA6E,WAAU;AAAA,YACbC,UAAA9E,kCAAA,IAAC,OAAA;AAAA,cACCqF,KAAK+B;AAAAA,cACLvC,WAAU;AAAA,cACVS,KAAK,UAAU+B,QAAQ,CAAC;AAAA,YAC1B,CAAA;AAAA,UACF,CAAA,GACArH,kCAAA,IAAC,UAAO;AAAA,YAAAiF,SAASA,MAAMnB,kBAAkB,IAAI;AAAA,YAC3CgB,UAAC9E,kCAAA,IAAAuF,QAAA;AAAA,cAAOV,WAAU;AAAA,YAA4C,CAAA;AAAA,UAChE,CAAA,GACA7E,kCAAA,IAAC,UAAA;AAAA,YACCiF,SAASA,MAAM;AACb9B,yBAAW,kBAAkB,IAAI;AAAA,YACnC;AAAA,YACA0B,WAAU;AAAA,YAEVC,UAAA9E,kCAAA,IAACwF,OAAM;AAAA,cAAAX,WAAU;AAAA,YAAU,CAAA;AAAA,UAAA,CAC7B,CAAA;AAAA,QAAA,GAnBQwC,KAoBV,KAGDtH,kCAAA,KAAA,OAAA;AAAA,UAAI8E,WAAU;AAAA,UACbC,UAAA,CAAC9E,kCAAA,IAAA,OAAA;AAAA,YAAI6E,WAAU;AAAA,YAA8BC,UAAgB;AAAA,UAAA,CAAA,GAC7D9E,kCAAA,IAAC,UAAO;AAAA,YAAAiF,SAASA,MAAMnB,kBAAkB,IAAI;AAAA,YAC3CgB,UAAC9E,kCAAA,IAAAuF,QAAA;AAAA,cAAOV,WAAU;AAAA,YAA4C,CAAA;AAAA,UAChE,CAAA,CAAA;AAAA,QAAA,CACF,IAGF9E,kCAAA,KAAC,OAAI;AAAA,UAAA8E,WAAU;AAAA,UACbC,UAAA,CAAC9E,kCAAA,IAAA,OAAA;AAAA,YAAI6E,WAAU;AAAA,YACbC,UAAA9E,kCAAA,IAACyF,sBAAA;AAAA,cACC6B,UAAU;AAAA,cACV5B,UAAW6B,UAA4B;AACrCrD,mCAAmB,IAAI;AACjB,sBAAAsD,aAAa5B,MAAMC,QAAQ0B,IAAI,IAAIA,KAAKE,KAAK,GAAG,IAAIF;AAC1D9D,kCAAkB,kBAAkB+D,UAAU;AAC9C1D,kCAAkB,KAAK;AAAA,cACzB;AAAA,YACF,CAAA;AAAA,UACF,CAAA,GACA9D,kCAAA,IAAC,OAAI;AAAA,YAAA6E,WAAU;AAAA,YACbC,UAAA/E,kCAAA,KAAC+F,QAAA;AAAA,cACCjB,WAAU;AAAA,cACVI,SAASA,MAAM;AACbnB,kCAAkB,KAAK;AACvBI,mCAAmB,KAAK;AAExBvD,iCAAkByB,WAAU;AAAA,kBAAE,GAAGA;AAAAA,kBAAMpB,gBAAgB;AAAA,gBAAU,EAAE;AACnEe,sCAAuBK,WAAU;AAAA,kBAAE,GAAGA;AAAAA,kBAAMpB,gBAAgB;AAAA,gBAAM,EAAE;AAAA,cACtE;AAAA,cAEA8D,UAAA,CAAC9E,kCAAA,IAAAmF,GAAA;AAAA,gBAAEN,WAAU;AAAA,cAAU,CAAA,GAAE,SAAA;AAAA,YAC3B,CAAA;AAAA,UACF,CAAA,CAAA;AAAA,SACF,GAEDZ,mBACClE,kCAAA,KAAC+F,QAAA;AAAA,UACCjB,WAAU;AAAA,UACVI,SAASA,MAAM;AACb9B,uBAAW,gBAAgB;AAC3BW,8BAAkB,KAAK;AACvBI,+BAAmB,KAAK;AAAA,UAC1B;AAAA,UAEAY,UAAA,CAAC9E,kCAAA,IAAA+F,MAAA;AAAA,YAAKlB,WAAU;AAAA,UAAU,CAAA,GAAE,OAAA;AAAA,QAAA,CAC9B,CAAA;AAAA,MAEJ,CAAA,GAEA9E,kCAAA,KAAC,OAAI;AAAA,QAAA8E,WAAU;AAAA,QACbC,UAAA,CAAA9E,kCAAA,IAAC;UAAI8E,UAAoB;AAAA,QAAA,CAAA,GACxB9E,kCAAA,IAAA,OAAA;AAAA,UAAI6E,WAAU;AAAA,UACZC,UAAC,CAAAX,qBACAhE,mBAAc,CAAC,MAAfA,mBAAkBc,cACflB,kCAAA,KAAA,OAAA;AAAA,YAAI8E,WAAU;AAAA,YACbC,UAAA,CAAA9E,kCAAA,IAAC,OAAA;AAAA,cACCqF,MAAKlF,mBAAc,CAAC,MAAfA,mBAAkBc;AAAAA,cACvB4D,WAAU;AAAA,cACVS,KAAI;AAAA,YAAA,CACN,GACAtF,kCAAA,IAAC,UAAO;AAAA,cAAAiF,SAASA,MAAMb,oBAAoB,IAAI;AAAA,cAC7CU,UAAC9E,kCAAA,IAAAuF,QAAA;AAAA,gBAAOV,WAAU;AAAA,cAA4C,CAAA;AAAA,YAChE,CAAA,GACA7E,kCAAA,IAAC,UAAA;AAAA,cACCiF,SAASA,MAAM;AACb9B,2BAAW,cAAc,IAAI;AAAA,cAC/B;AAAA,cACA0B,WAAU;AAAA,cAEVC,UAAA9E,kCAAA,IAACwF,OAAM;AAAA,gBAAAX,WAAU;AAAA,cAAU,CAAA;AAAA,YAAA,CAC7B,CAAA;AAAA,UAAA,CACF,IAEA9E,kCAAA,KAAC,OAAI;AAAA,YAAA8E,WAAU;AAAA,YAAwCC,UAAA,CAAA,qBAErD9E,kCAAA,IAAC,UAAO;AAAA,cAAAiF,SAASA,MAAMb,oBAAoB,IAAI;AAAA,cAC7CU,UAAC9E,kCAAA,IAAAuF,QAAA;AAAA,gBAAOV,WAAU;AAAA,cAA4C,CAAA;AAAA,YAChE,CAAA,CAAA;AAAA,UAAA,CACF,IAGF9E,kCAAA,KAAC,OAAI;AAAA,YAAA8E,WAAU;AAAA,YACbC,UAAA,CAAC9E,kCAAA,IAAA,OAAA;AAAA,cAAI6E,WAAU;AAAA,cACbC,UAAA9E,kCAAA,IAACyF,sBAAA;AAAA,gBACCC,UAAWhC,SAA2B;AACpCY,uCAAqB,IAAI;AACzB,wBAAMoD,eAAe9B,MAAMC,QAAQnC,GAAG,IAAIA,IAAI,CAAC,IAAIA;AACnDD,oCAAkB,cAAciE,YAAY;AAC5CtD,sCAAoB,KAAK;AAAA,gBAC3B;AAAA,cACF,CAAA;AAAA,YACF,CAAA,GACApE,kCAAA,IAAC,OAAI;AAAA,cAAA6E,WAAU;AAAA,cACbC,UAAA/E,kCAAA,KAAC+F,QAAA;AAAA,gBACCjB,WAAU;AAAA,gBACVI,SAASA,MAAM;AACbb,sCAAoB,KAAK;AACzBE,uCAAqB,KAAK;AAE1B3D,mCAAkByB,WAAU;AAAA,oBAAE,GAAGA;AAAAA,oBAAMnB,YAAY;AAAA,kBAAU,EAAE;AAC/Dc,wCAAuBK,WAAU;AAAA,oBAAE,GAAGA;AAAAA,oBAAMnB,YAAY;AAAA,kBAAM,EAAE;AAAA,gBAClE;AAAA,gBAEA6D,UAAA,CAAC9E,kCAAA,IAAAmF,GAAA;AAAA,kBAAEN,WAAU;AAAA,gBAAU,CAAA,GAAE,SAAA;AAAA,cAC3B,CAAA;AAAA,YACF,CAAA,CAAA;AAAA,UACF,CAAA;AAAA,SAEJ,GACCR,qBACCtE,kCAAA,KAAC+F,QAAA;AAAA,UACCjB,WAAU;AAAA,UACVI,SAASA,MAAM;AACb9B,uBAAW,YAAY;AACvBiB,gCAAoB,KAAK;AACzBE,iCAAqB,KAAK;AAAA,UAC5B;AAAA,UAEAQ,UAAA,CAAC9E,kCAAA,IAAA+F,MAAA;AAAA,YAAKlB,WAAU;AAAA,UAAU,CAAA,GAAE,OAAA;AAAA,QAAA,CAC9B,CAAA;AAAA,MAEJ,CAAA,GAEA9E,kCAAA,KAAC,OAAI;AAAA,QAAA8E,WAAU;AAAA,QACbC,UAAA,CAAA9E,kCAAA,IAAC;UAAI8E,UAAoB;AAAA,QAAA,CAAA,GACxB9E,kCAAA,IAAA,OAAA;AAAA,UAAI6E,WAAU;AAAA,UACZC,UAAC,CAAAnB,kBACAxD,mBAAc,CAAC,MAAfA,mBAAkBe,iBACfnB,kCAAA,KAAA,OAAA;AAAA,YAAI8E,WAAU;AAAA,YACbC,UAAA,CAAA9E,kCAAA,IAAC,OAAA;AAAA,cACCqF,MAAKlF,mBAAc,CAAC,MAAfA,mBAAkBe;AAAAA,cACvB2D,WAAU;AAAA,cACVS,KAAI;AAAA,YAAA,CACN,GACAtF,kCAAA,IAAC,UAAO;AAAA,cAAAiF,SAASA,MAAMrB,iBAAiB,IAAI;AAAA,cAC1CkB,UAAC9E,kCAAA,IAAAuF,QAAA;AAAA,gBAAOV,WAAU;AAAA,cAA4C,CAAA;AAAA,YAChE,CAAA,GACA7E,kCAAA,IAAC,UAAA;AAAA,cACCiF,SAASA,MAAM;AACb9B,2BAAW,iBAAiB,IAAI;AAAA,cAClC;AAAA,cACA0B,WAAU;AAAA,cAEVC,UAAA9E,kCAAA,IAACwF,OAAM;AAAA,gBAAAX,WAAU;AAAA,cAAU,CAAA;AAAA,YAAA,CAC7B,CAAA;AAAA,UAAA,CACF,IAEA9E,kCAAA,KAAC,OAAI;AAAA,YAAA8E,WAAU;AAAA,YAAwCC,UAAA,CAAA,kBAErD9E,kCAAA,IAAC,UAAO;AAAA,cAAAiF,SAASA,MAAMrB,iBAAiB,IAAI;AAAA,cAC1CkB,UAAC9E,kCAAA,IAAAuF,QAAA;AAAA,gBAAOV,WAAU;AAAA,cAA4C,CAAA;AAAA,YAChE,CAAA,CAAA;AAAA,UAAA,CACF,IAGF9E,kCAAA,KAAC,OAAI;AAAA,YAAA8E,WAAU;AAAA,YACbC,UAAA,CAAC9E,kCAAA,IAAA,OAAA;AAAA,cAAI6E,WAAU;AAAA,cACbC,UAAA9E,kCAAA,IAACyF,sBAAA;AAAA,gBACCC,UAAWhC,SAA2B;AACpCM,oCAAkB,IAAI;AACtB,wBAAM2D,YAAY/B,MAAMC,QAAQnC,GAAG,IAAIA,IAAI,CAAC,IAAIA;AAChDD,oCAAkB,iBAAiBkE,SAAS;AAC5C/D,mCAAiB,KAAK;AAAA,gBACxB;AAAA,cACF,CAAA;AAAA,YACF,CAAA,GACA5D,kCAAA,IAAC,OAAI;AAAA,cAAA6E,WAAU;AAAA,cACbC,UAAA/E,kCAAA,KAAC+F,QAAA;AAAA,gBACCjB,WAAU;AAAA,gBACVI,SAASA,MAAM;AACbrB,mCAAiB,KAAK;AACtBI,oCAAkB,KAAK;AAEvBrD,mCAAkByB,WAAU;AAAA,oBAAE,GAAGA;AAAAA,oBAAMlB,eAAe;AAAA,kBAAU,EAAE;AAClEa,wCAAuBK,WAAU;AAAA,oBAAE,GAAGA;AAAAA,oBAAMlB,eAAe;AAAA,kBAAM,EAAE;AAAA,gBACrE;AAAA,gBAEA4D,UAAA,CAAC9E,kCAAA,IAAAmF,GAAA;AAAA,kBAAEN,WAAU;AAAA,gBAAU,CAAA,GAAE,SAAA;AAAA,cAC3B,CAAA;AAAA,YACF,CAAA,CAAA;AAAA,UACF,CAAA;AAAA,SAEJ,GACCd,kBACChE,kCAAA,KAAC+F,QAAA;AAAA,UACCjB,WAAU;AAAA,UACVI,SAASA,MAAM;AACb9B,uBAAW,eAAe;AAC1BS,6BAAiB,KAAK;AACtBI,8BAAkB,KAAK;AAAA,UACzB;AAAA,UAEAc,UAAA,CAAC9E,kCAAA,IAAA+F,MAAA;AAAA,YAAKlB,WAAU;AAAA,UAAU,CAAA,GAAE,OAAA;AAAA,QAAA,CAC9B,CAAA;AAAA,MAEJ,CAAA,CAAA;AAAA,IACF,CAAA,CAAA;AAAA,EACF,CAAA;AAIJ;", "x_google_ignoreList": [0]}