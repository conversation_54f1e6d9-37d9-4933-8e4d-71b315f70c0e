{"version": 3, "file": "sellerSetting.settings-CAz6o2Cx.js", "sources": ["../../../node_modules/lucide-react/dist/esm/icons/gallery-horizontal.js", "../../../app/routes/sellerSetting.settings.tsx"], "sourcesContent": ["/**\n * @license lucide-react v0.453.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst GalleryHorizontal = createLucideIcon(\"GalleryHorizontal\", [\n  [\"path\", { d: \"M2 3v18\", key: \"pzttux\" }],\n  [\"rect\", { width: \"12\", height: \"18\", x: \"6\", y: \"3\", rx: \"2\", key: \"btr8bg\" }],\n  [\"path\", { d: \"M22 3v18\", key: \"6jf3v\" }]\n]);\n\nexport { GalleryHorizontal as default };\n//# sourceMappingURL=gallery-horizontal.js.map\n", "import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, CardTitle } from \"~/components/ui/card\";\r\nimport { Link } from \"@remix-run/react\";\r\nimport { GalleryHorizontal } from \"lucide-react\";\r\nimport { FaWhatsapp } from \"react-icons/fa\";\r\n\r\nexport default function Settings() {\r\n  return (\r\n    <div className=\"p-6\">\r\n      <div className=\"mb-6\">\r\n        <h1 className=\"text-3xl font-bold text-gray-900\">Settings</h1>\r\n        <p className=\"text-gray-600 mt-2\">Manage your restaurant configuration</p>\r\n      </div>\r\n      \r\n      {/* Coming Soon Features */}\r\n      <div className=\"mt-8\">\r\n        <h2 className=\"text-xl font-semibold text-gray-900 mb-4\">Coming Soon Features</h2>\r\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\r\n          <Card className=\"border-dashed border-2 border-gray-300\">\r\n            <CardHeader>\r\n              <CardTitle className=\"text-gray-600\">Advanced Analytics</CardTitle>\r\n              <CardDescription>Detailed business insights</CardDescription>\r\n            </CardHeader>\r\n            <CardContent>\r\n              <div className=\"text-3xl font-bold text-gray-400\">📊</div>\r\n              <p className=\"text-sm text-gray-500 mt-2\">Launching soon</p>\r\n            </CardContent>\r\n          </Card>\r\n\r\n          <Card className=\"border-dashed border-2 border-gray-300\">\r\n            <CardHeader>\r\n              <CardTitle className=\"text-gray-600\">Inventory Management</CardTitle>\r\n              <CardDescription>Stock tracking system</CardDescription>\r\n            </CardHeader>\r\n            <CardContent>\r\n              <div className=\"text-3xl font-bold text-gray-400\">📦</div>\r\n              <p className=\"text-sm text-gray-500 mt-2\">Launching soon</p>\r\n            </CardContent>\r\n          </Card>\r\n\r\n          <Card className=\"border-dashed border-2 border-gray-300\">\r\n            <CardHeader>\r\n              <CardTitle className=\"text-gray-600\">Customer Loyalty</CardTitle>\r\n              <CardDescription>Rewards program</CardDescription>\r\n            </CardHeader>\r\n            <CardContent>\r\n              <div className=\"text-3xl font-bold text-gray-400\">🎁</div>\r\n              <p className=\"text-sm text-gray-500 mt-2\">Launching soon</p>\r\n            </CardContent>\r\n          </Card>\r\n\r\n          <Card className=\"border-dashed border-2 border-gray-300\">\r\n            <CardHeader>\r\n              <CardTitle className=\"text-gray-600\">Multi-location</CardTitle>\r\n              <CardDescription>Branch management</CardDescription>\r\n            </CardHeader>\r\n            <CardContent>\r\n              <div className=\"text-3xl font-bold text-gray-400\">🏢</div>\r\n              <p className=\"text-sm text-gray-500 mt-2\">Launching soon</p>\r\n            </CardContent>\r\n          </Card>\r\n\r\n          <Card className=\"border-dashed border-2 border-gray-300\">\r\n            <CardHeader>\r\n              <CardTitle className=\"text-gray-600\">API Integration</CardTitle>\r\n              <CardDescription>Third-party connections</CardDescription>\r\n            </CardHeader>\r\n            <CardContent>\r\n              <div className=\"text-3xl font-bold text-gray-400\">🔌</div>\r\n              <p className=\"text-sm text-gray-500 mt-2\">Launching soon</p>\r\n            </CardContent>\r\n          </Card>\r\n\r\n          <Card className=\"border-dashed border-2 border-gray-300\">\r\n            <CardHeader>\r\n              <CardTitle className=\"text-gray-600\">Advanced Reports</CardTitle>\r\n              <CardDescription>Custom reporting</CardDescription>\r\n            </CardHeader>\r\n            <CardContent>\r\n              <div className=\"text-3xl font-bold text-gray-400\">📈</div>\r\n              <p className=\"text-sm text-gray-500 mt-2\">Launching soon</p>\r\n            </CardContent>\r\n          </Card>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Sub-menu Section */}\r\n      <div className=\"mt-8\">\r\n        <h2 className=\"text-xl font-semibold text-gray-900 mb-4\">Additional Settings</h2>\r\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\r\n          <Link to=\"/sellerSetting/nbanners\">\r\n            <Card className=\"hover:shadow-lg transition-shadow cursor-pointer\">\r\n              <CardHeader>\r\n                <div className=\"flex items-center space-x-3\">\r\n                  <GalleryHorizontal className=\"text-primary\" size={20} />\r\n                  <CardTitle className=\"text-lg\">Banners & Sequence</CardTitle>\r\n                </div>\r\n                <CardDescription>Manage promotional banners</CardDescription>\r\n              </CardHeader>\r\n            </Card>\r\n          </Link>\r\n\r\n          <Link to=\"/sellerSetting/whatsappprofile\">\r\n            <Card className=\"hover:shadow-lg transition-shadow cursor-pointer\">\r\n              <CardHeader>\r\n                <div className=\"flex items-center space-x-3\">\r\n                  <FaWhatsapp className=\"text-primary\" size={20} />\r\n                  <CardTitle className=\"text-lg\">WhatsApp Settings</CardTitle>\r\n                </div>\r\n                <CardDescription>Configure WhatsApp integration</CardDescription>\r\n              </CardHeader>\r\n            </Card>\r\n          </Link>\r\n\r\n          <Link to=\"/sellerSetting/coupons\">\r\n            <Card className=\"hover:shadow-lg transition-shadow cursor-pointer\">\r\n              <CardHeader>\r\n                <div className=\"flex items-center space-x-3\">\r\n                  <span className=\"text-2xl\">🎉</span>\r\n                  <CardTitle className=\"text-lg\">Coupons</CardTitle>\r\n                </div>\r\n                <CardDescription>Manage discount offers</CardDescription>\r\n              </CardHeader>\r\n            </Card>\r\n          </Link>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n} "], "names": ["Settings", "jsxs", "className", "children", "jsx", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardTitle", "CardDescription", "<PERSON><PERSON><PERSON><PERSON>", "Link", "to", "GalleryHorizontal", "size", "FaWhatsapp"], "mappings": ";;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA,MAAM,oBAAoB,iBAAiB,qBAAqB;AAAA,EAC9D,CAAC,QAAQ,EAAE,GAAG,WAAW,KAAK,SAAQ,CAAE;AAAA,EACxC,CAAC,QAAQ,EAAE,OAAO,MAAM,QAAQ,MAAM,GAAG,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,SAAQ,CAAE;AAAA,EAC9E,CAAC,QAAQ,EAAE,GAAG,YAAY,KAAK,QAAS,CAAA;AAC1C,CAAC;ACRD,SAAwBA,WAAW;AAE/B,SAAAC,kCAAAA,KAAC,OAAI;AAAA,IAAAC,WAAU;AAAA,IACbC,UAAA,CAACF,kCAAA,KAAA,OAAA;AAAA,MAAIC,WAAU;AAAA,MACbC,UAAA,CAACC,kCAAA,IAAA,MAAA;AAAA,QAAGF,WAAU;AAAA,QAAmCC,UAAQ;AAAA,MAAA,CAAA,GACxDC,kCAAA,IAAA,KAAA;AAAA,QAAEF,WAAU;AAAA,QAAqBC,UAAoC;AAAA,MAAA,CAAA,CAAA;AAAA,IACxE,CAAA,GAGAF,kCAAA,KAAC,OAAI;AAAA,MAAAC,WAAU;AAAA,MACbC,UAAA,CAACC,kCAAA,IAAA,MAAA;AAAA,QAAGF,WAAU;AAAA,QAA2CC,UAAoB;AAAA,MAAA,CAAA,GAC7EF,kCAAA,KAAC,OAAI;AAAA,QAAAC,WAAU;AAAA,QACbC,UAAA,CAACF,kCAAA,KAAAI,MAAA;AAAA,UAAKH,WAAU;AAAA,UACdC,UAAA,CAAAF,kCAAA,KAACK,YACC;AAAA,YAAAH,UAAA,CAACC,kCAAA,IAAAG,WAAA;AAAA,cAAUL,WAAU;AAAA,cAAgBC,UAAkB;AAAA,YAAA,CAAA,GACvDC,kCAAA,IAACI;cAAgBL,UAA0B;AAAA,YAAA,CAAA,CAAA;AAAA,UAC7C,CAAA,0CACCM,aACC;AAAA,YAAAN,UAAA,CAACC,kCAAA,IAAA,OAAA;AAAA,cAAIF,WAAU;AAAA,cAAmCC,UAAE;AAAA,YAAA,CAAA,GACnDC,kCAAA,IAAA,KAAA;AAAA,cAAEF,WAAU;AAAA,cAA6BC,UAAc;AAAA,YAAA,CAAA,CAAA;AAAA,UAC1D,CAAA,CAAA;AAAA,QACF,CAAA,GAEAF,kCAAA,KAACI,MAAK;AAAA,UAAAH,WAAU;AAAA,UACdC,UAAA,CAAAF,kCAAA,KAACK,YACC;AAAA,YAAAH,UAAA,CAACC,kCAAA,IAAAG,WAAA;AAAA,cAAUL,WAAU;AAAA,cAAgBC,UAAoB;AAAA,YAAA,CAAA,GACzDC,kCAAA,IAACI;cAAgBL,UAAqB;AAAA,YAAA,CAAA,CAAA;AAAA,UACxC,CAAA,0CACCM,aACC;AAAA,YAAAN,UAAA,CAACC,kCAAA,IAAA,OAAA;AAAA,cAAIF,WAAU;AAAA,cAAmCC,UAAE;AAAA,YAAA,CAAA,GACnDC,kCAAA,IAAA,KAAA;AAAA,cAAEF,WAAU;AAAA,cAA6BC,UAAc;AAAA,YAAA,CAAA,CAAA;AAAA,UAC1D,CAAA,CAAA;AAAA,QACF,CAAA,GAEAF,kCAAA,KAACI,MAAK;AAAA,UAAAH,WAAU;AAAA,UACdC,UAAA,CAAAF,kCAAA,KAACK,YACC;AAAA,YAAAH,UAAA,CAACC,kCAAA,IAAAG,WAAA;AAAA,cAAUL,WAAU;AAAA,cAAgBC,UAAgB;AAAA,YAAA,CAAA,GACrDC,kCAAA,IAACI;cAAgBL,UAAe;AAAA,YAAA,CAAA,CAAA;AAAA,UAClC,CAAA,0CACCM,aACC;AAAA,YAAAN,UAAA,CAACC,kCAAA,IAAA,OAAA;AAAA,cAAIF,WAAU;AAAA,cAAmCC,UAAE;AAAA,YAAA,CAAA,GACnDC,kCAAA,IAAA,KAAA;AAAA,cAAEF,WAAU;AAAA,cAA6BC,UAAc;AAAA,YAAA,CAAA,CAAA;AAAA,UAC1D,CAAA,CAAA;AAAA,QACF,CAAA,GAEAF,kCAAA,KAACI,MAAK;AAAA,UAAAH,WAAU;AAAA,UACdC,UAAA,CAAAF,kCAAA,KAACK,YACC;AAAA,YAAAH,UAAA,CAACC,kCAAA,IAAAG,WAAA;AAAA,cAAUL,WAAU;AAAA,cAAgBC,UAAc;AAAA,YAAA,CAAA,GACnDC,kCAAA,IAACI;cAAgBL,UAAiB;AAAA,YAAA,CAAA,CAAA;AAAA,UACpC,CAAA,0CACCM,aACC;AAAA,YAAAN,UAAA,CAACC,kCAAA,IAAA,OAAA;AAAA,cAAIF,WAAU;AAAA,cAAmCC,UAAE;AAAA,YAAA,CAAA,GACnDC,kCAAA,IAAA,KAAA;AAAA,cAAEF,WAAU;AAAA,cAA6BC,UAAc;AAAA,YAAA,CAAA,CAAA;AAAA,UAC1D,CAAA,CAAA;AAAA,QACF,CAAA,GAEAF,kCAAA,KAACI,MAAK;AAAA,UAAAH,WAAU;AAAA,UACdC,UAAA,CAAAF,kCAAA,KAACK,YACC;AAAA,YAAAH,UAAA,CAACC,kCAAA,IAAAG,WAAA;AAAA,cAAUL,WAAU;AAAA,cAAgBC,UAAe;AAAA,YAAA,CAAA,GACpDC,kCAAA,IAACI;cAAgBL,UAAuB;AAAA,YAAA,CAAA,CAAA;AAAA,UAC1C,CAAA,0CACCM,aACC;AAAA,YAAAN,UAAA,CAACC,kCAAA,IAAA,OAAA;AAAA,cAAIF,WAAU;AAAA,cAAmCC,UAAE;AAAA,YAAA,CAAA,GACnDC,kCAAA,IAAA,KAAA;AAAA,cAAEF,WAAU;AAAA,cAA6BC,UAAc;AAAA,YAAA,CAAA,CAAA;AAAA,UAC1D,CAAA,CAAA;AAAA,QACF,CAAA,GAEAF,kCAAA,KAACI,MAAK;AAAA,UAAAH,WAAU;AAAA,UACdC,UAAA,CAAAF,kCAAA,KAACK,YACC;AAAA,YAAAH,UAAA,CAACC,kCAAA,IAAAG,WAAA;AAAA,cAAUL,WAAU;AAAA,cAAgBC,UAAgB;AAAA,YAAA,CAAA,GACrDC,kCAAA,IAACI;cAAgBL,UAAgB;AAAA,YAAA,CAAA,CAAA;AAAA,UACnC,CAAA,0CACCM,aACC;AAAA,YAAAN,UAAA,CAACC,kCAAA,IAAA,OAAA;AAAA,cAAIF,WAAU;AAAA,cAAmCC,UAAE;AAAA,YAAA,CAAA,GACnDC,kCAAA,IAAA,KAAA;AAAA,cAAEF,WAAU;AAAA,cAA6BC,UAAc;AAAA,YAAA,CAAA,CAAA;AAAA,UAC1D,CAAA,CAAA;AAAA,QACF,CAAA,CAAA;AAAA,MACF,CAAA,CAAA;AAAA,IACF,CAAA,GAGAF,kCAAA,KAAC,OAAI;AAAA,MAAAC,WAAU;AAAA,MACbC,UAAA,CAACC,kCAAA,IAAA,MAAA;AAAA,QAAGF,WAAU;AAAA,QAA2CC,UAAmB;AAAA,MAAA,CAAA,GAC5EF,kCAAA,KAAC,OAAI;AAAA,QAAAC,WAAU;AAAA,QACbC,UAAA,CAACC,kCAAA,IAAAM,MAAA;AAAA,UAAKC,IAAG;AAAA,UACPR,UAAAC,kCAAA,IAACC;YAAKH,WAAU;AAAA,YACdC,iDAACG,YACC;AAAA,cAAAH,UAAA,CAACF,kCAAA,KAAA,OAAA;AAAA,gBAAIC,WAAU;AAAA,gBACbC,UAAA,CAAAC,kCAAA,IAACQ,mBAAkB;AAAA,kBAAAV,WAAU;AAAA,kBAAeW,MAAM;AAAA,gBAAI,CAAA,GACrDT,kCAAA,IAAAG,WAAA;AAAA,kBAAUL,WAAU;AAAA,kBAAUC,UAAkB;AAAA,gBAAA,CAAA,CAAA;AAAA,cACnD,CAAA,GACAC,kCAAA,IAACI;gBAAgBL,UAA0B;AAAA,cAAA,CAAA,CAAA;AAAA,YAC7C,CAAA;AAAA,UACF,CAAA;AAAA,QACF,CAAA,GAEAC,kCAAA,IAACM;UAAKC,IAAG;AAAA,UACPR,gDAACE,MAAK;AAAA,YAAAH,WAAU;AAAA,YACdC,UAAAF,kCAAA,KAACK,YACC;AAAA,cAAAH,UAAA,CAACF,kCAAA,KAAA,OAAA;AAAA,gBAAIC,WAAU;AAAA,gBACbC,UAAA,CAAAC,kCAAA,IAACU,YAAW;AAAA,kBAAAZ,WAAU;AAAA,kBAAeW,MAAM;AAAA,gBAAI,CAAA,GAC9CT,kCAAA,IAAAG,WAAA;AAAA,kBAAUL,WAAU;AAAA,kBAAUC,UAAiB;AAAA,gBAAA,CAAA,CAAA;AAAA,cAClD,CAAA,GACAC,kCAAA,IAACI;gBAAgBL,UAA8B;AAAA,cAAA,CAAA,CAAA;AAAA,YACjD,CAAA;AAAA,UACF,CAAA;AAAA,QACF,CAAA,GAEAC,kCAAA,IAACM;UAAKC,IAAG;AAAA,UACPR,gDAACE,MAAK;AAAA,YAAAH,WAAU;AAAA,YACdC,UAAAF,kCAAA,KAACK,YACC;AAAA,cAAAH,UAAA,CAACF,kCAAA,KAAA,OAAA;AAAA,gBAAIC,WAAU;AAAA,gBACbC,UAAA,CAACC,kCAAA,IAAA,QAAA;AAAA,kBAAKF,WAAU;AAAA,kBAAWC,UAAE;AAAA,gBAAA,CAAA,GAC5BC,kCAAA,IAAAG,WAAA;AAAA,kBAAUL,WAAU;AAAA,kBAAUC,UAAO;AAAA,gBAAA,CAAA,CAAA;AAAA,cACxC,CAAA,GACAC,kCAAA,IAACI;gBAAgBL,UAAsB;AAAA,cAAA,CAAA,CAAA;AAAA,YACzC,CAAA;AAAA,UACF,CAAA;AAAA,QACF,CAAA,CAAA;AAAA,MACF,CAAA,CAAA;AAAA,IACF,CAAA,CAAA;AAAA,EACF,CAAA;AAEJ;", "x_google_ignoreList": [0]}