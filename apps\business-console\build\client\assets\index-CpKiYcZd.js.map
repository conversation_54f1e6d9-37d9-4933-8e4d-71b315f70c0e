{"version": 3, "file": "index-CpKiYcZd.js", "sources": ["../../../node_modules/@radix-ui/react-use-previous/dist/index.mjs"], "sourcesContent": ["// packages/react/use-previous/src/usePrevious.tsx\nimport * as React from \"react\";\nfunction usePrevious(value) {\n  const ref = React.useRef({ value, previous: value });\n  return React.useMemo(() => {\n    if (ref.current.value !== value) {\n      ref.current.previous = ref.current.value;\n      ref.current.value = value;\n    }\n    return ref.current.previous;\n  }, [value]);\n}\nexport {\n  usePrevious\n};\n//# sourceMappingURL=index.mjs.map\n"], "names": ["React.useRef", "React.useMemo"], "mappings": ";AAEA,SAAS,YAAY,OAAO;AAC1B,QAAM,MAAMA,aAAAA,OAAa,EAAE,OAAO,UAAU,MAAK,CAAE;AACnD,SAAOC,aAAa,QAAC,MAAM;AACzB,QAAI,IAAI,QAAQ,UAAU,OAAO;AAC/B,UAAI,QAAQ,WAAW,IAAI,QAAQ;AACnC,UAAI,QAAQ,QAAQ;AAAA,IAC1B;AACI,WAAO,IAAI,QAAQ;AAAA,EACvB,GAAK,CAAC,KAAK,CAAC;AACZ;", "x_google_ignoreList": [0]}