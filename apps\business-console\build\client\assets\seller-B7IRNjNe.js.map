{"version": 3, "file": "seller-B7IRNjNe.js", "sources": ["../../../app/routes/seller.tsx"], "sourcesContent": ["import { Megaphone, Menu, XCircle } from \"lucide-react\"\r\nimport {\r\n    <PERSON><PERSON>,\r\n    <PERSON><PERSON><PERSON>onte<PERSON>,\r\n    SheetTrigger,\r\n} from \"@components/ui/sheet\"\r\nimport { Button } from \"@components/ui/button\"\r\nimport { Link, Navigate, Outlet, useLoaderData, useLocation } from \"@remix-run/react\";\r\n// import {\r\n//     AlertDialog,\r\n//     AlertDialogAction,\r\n//     AlertDialogCancel,\r\n//     AlertDialogContent,\r\n//     AlertDialogDescription,\r\n//     AlertDialogFooter,\r\n//     AlertDialogHeader,\r\n//     AlertDialogTitle,\r\n//     AlertDialogTrigger,\r\n// } from \"@components/ui/alert-dialog\"\r\nimport { destroySession, getSession } from \"@utils/session.server\"\r\nimport { LoaderFunction, redirect } from \"@remix-run/node\"\r\nimport { withAuth, withResponse } from \"@utils/auth-utils\";\r\nimport { useState, useEffect } from \"react\";\r\n\r\ninterface LoaderData {\r\n    userPermissions: string[];\r\n    userDetails: {\r\n        userDetails: {\r\n            businessName?: string;\r\n        };\r\n    };\r\n}\r\n\r\nexport const loader: LoaderFunction = withAuth(\r\n    async ({ user }) => {\r\n        return withResponse({\r\n            userPermissions: user?.userPermissions || [],\r\n            userDetails: user || { userDetails: {} },\r\n        });\r\n    }\r\n)\r\n\r\nexport const action = withAuth(\r\n    async ({ request }) => {\r\n        const session = await getSession(request.headers.get(\"Cookie\"))\r\n        return redirect(\"/login\", {\r\n            headers: {\r\n                \"Set-Cookie\": await destroySession(session),\r\n            },\r\n        })\r\n    }\r\n);\r\n\r\nexport default function Seller() {\r\n    const loaderData = useLoaderData<LoaderData>();\r\n    const location = useLocation();\r\n    const [isOpen, setIsOpen] = useState(false);\r\n    const [shouldRedirect, setShouldRedirect] = useState(false);\r\n    // const isWhatsAppEnabled = loaderData.userPermissions.includes(\"seller_app.whatsappBasic\")\r\n    // TODO: Remove this after Demo\r\n    const isWhatsAppEnabled = true;\r\n    const activeSection = location.pathname.split(\"/\")[2];\r\n\r\n    useEffect(() => {\r\n        if (location.pathname === \"/seller\") {\r\n            setShouldRedirect(true);\r\n        } else {\r\n            setShouldRedirect(false);\r\n        }\r\n    }, [location.pathname]);\r\n\r\n    // First check if we should redirect to marketing\r\n    if (shouldRedirect && isWhatsAppEnabled) {\r\n        return <Navigate to=\"/seller/marketing\" replace />;\r\n    }\r\n\r\n    // Then check if either WhatsApp or NetworkManager is not enabled to show access denied\r\n    if (!isWhatsAppEnabled) {\r\n        return (\r\n            <div className=\"min-h-screen bg-gray-50 flex items-center justify-center p-4\">\r\n                <div className=\"max-w-md w-full bg-white rounded-lg shadow-lg p-6 text-center\">\r\n                    <XCircle className=\"h-16 w-16 text-red-500 mx-auto mb-4\" />\r\n                    <h1 className=\"text-2xl font-bold text-gray-900 mb-2\">Access Denied</h1>\r\n                    <p className=\"text-gray-600\">\r\n                        {!isWhatsAppEnabled\r\n                            ? \"WhatsApp functionality is not enabled for your account.\"\r\n                            : \"Please contact your administrator for access.\"\r\n                        }\r\n                    </p>\r\n                </div>\r\n            </div>\r\n        );\r\n    }\r\n\r\n    const NavContent = () => (\r\n        <div className=\"flex flex-col h-full\">\r\n            <div className=\"flex flex-col items-start space-y-1 gap-2 p-4\">\r\n                <img\r\n                    src=\"/mnet-logo.svg\"\r\n                    alt=\"mNet Logo\"\r\n                    className=\"h-12 w-auto\"\r\n                />\r\n                <div className=\"flex items-center mt-4 px-4 py-2 border rounded-md w-full\">\r\n                    <div className=\"flex items-center space-x-3\">\r\n                        <div className=\"h-6 w-6 rounded-full bg-primary flex items-center justify-center text-m font-bold text-white\">\r\n                            {loaderData?.userDetails?.userDetails?.businessName?.[0]?.toUpperCase() || \"B\"}\r\n                        </div>\r\n                        <div className=\"text-gray-900 text-m\">\r\n                            {loaderData?.userDetails?.userDetails?.businessName || \"Business Name\"}\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n\r\n            <nav className=\"flex-1 p-4\">\r\n\r\n\r\n                <div className=\"space-y-2\">\r\n                    {/* <Link to=\"/seller/customerAnalysis\" onClick={() => setIsOpen(false)}>\r\n                        <Button\r\n                            variant={activeSection === \"customers\" ? \"secondary\" : \"ghost\"}\r\n                            className=\"w-full justify-start\"\r\n                        >\r\n                            <Megaphone className=\"h-5 w-5 mr-3\" />\r\n                            CustomerAnalysis\r\n                        </Button>\r\n                    </Link> */}\r\n                    {isWhatsAppEnabled && (\r\n                        <Link to=\"/seller/marketing\" onClick={() => setIsOpen(false)}>\r\n                            <Button\r\n                                variant={activeSection === \"marketing\" ? \"secondary\" : \"ghost\"}\r\n                                className=\"w-full justify-start\"\r\n                            >\r\n                                <Megaphone className=\"h-5 w-5 mr-3\" />\r\n                                Marketing\r\n                            </Button>\r\n                        </Link>\r\n                    )}\r\n                </div>\r\n            </nav>\r\n\r\n            {/* <div className=\"p-4\">\r\n                <AlertDialog>\r\n                    <AlertDialogTrigger asChild>\r\n                        <Button variant=\"ghost\" className=\"w-full justify-start text-red-600 hover:text-red-700 hover:bg-red-50\">\r\n                            <LogOut className=\"h-5 w-5 mr-3\" />\r\n                            Logout\r\n                        </Button>\r\n                    </AlertDialogTrigger>\r\n                    <AlertDialogContent>\r\n                        <AlertDialogHeader>\r\n                            <AlertDialogTitle>Are you sure you want to logout?</AlertDialogTitle>\r\n                            <AlertDialogDescription>\r\n                                You will be redirected to the login page.\r\n                            </AlertDialogDescription>\r\n                        </AlertDialogHeader>\r\n                        <AlertDialogFooter>\r\n                            <AlertDialogCancel>Cancel</AlertDialogCancel>\r\n                            <AlertDialogAction onClick={handleLogout}>\r\n                                Logout\r\n                            </AlertDialogAction>\r\n                        </AlertDialogFooter>\r\n                    </AlertDialogContent>\r\n                </AlertDialog>\r\n            </div> */}\r\n        </div>\r\n    );\r\n\r\n    return (\r\n        <div className=\"min-h-screen bg-white\">\r\n            {/* Desktop Sidebar */}\r\n            <div className=\"hidden md:flex md:w-64 md:flex-col md:fixed md:inset-y-0 border-r\">\r\n                <NavContent />\r\n            </div>\r\n\r\n            {/* Mobile Header */}\r\n            <div className=\"md:hidden border-b\">\r\n                <div className=\"flex items-center justify-between p-4\">\r\n                    <img\r\n                        src=\"/mnet-logo.svg\"\r\n                        alt=\"mNet Logo\"\r\n                        className=\"h-8 w-auto\"\r\n                    />\r\n                    <Sheet open={isOpen} onOpenChange={setIsOpen}>\r\n                        <SheetTrigger asChild>\r\n                            <Button variant=\"ghost\" size=\"icon\">\r\n                                <Menu className=\"h-6 w-6\" />\r\n                            </Button>\r\n                        </SheetTrigger>\r\n                        <SheetContent side=\"left\" className=\"w-64 p-0\">\r\n                            <NavContent />\r\n                        </SheetContent>\r\n                    </Sheet>\r\n                </div>\r\n            </div>\r\n\r\n            {/* Main Content */}\r\n            <div className=\"md:pl-64\">\r\n                <main className=\"flex-1\">\r\n                    <Outlet />\r\n                </main>\r\n            </div>\r\n        </div>\r\n    );\r\n}\r\n"], "names": ["<PERSON><PERSON>", "loaderData", "useLoaderData", "location", "useLocation", "isOpen", "setIsOpen", "useState", "shouldRedirect", "setShouldRedirect", "isWhatsAppEnabled", "activeSection", "pathname", "split", "useEffect", "jsx", "Navigate", "to", "replace", "Nav<PERSON><PERSON><PERSON>", "jsxs", "className", "children", "src", "alt", "userDetails", "businessName", "toUpperCase", "Link", "onClick", "<PERSON><PERSON>", "variant", "Megaphone", "Sheet", "open", "onOpenChange", "Sheet<PERSON><PERSON>ger", "<PERSON><PERSON><PERSON><PERSON>", "size", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "side", "Outlet"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAqDA,SAAwBA,SAAS;AAC7B,QAAMC,aAAaC,cAA0B;AAC7C,QAAMC,WAAWC,YAAY;AAC7B,QAAM,CAACC,QAAQC,SAAS,IAAIC,aAAAA,SAAS,KAAK;AAC1C,QAAM,CAACC,gBAAgBC,iBAAiB,IAAIF,aAAAA,SAAS,KAAK;AAG1D,QAAMG,oBAAoB;AAC1B,QAAMC,gBAAgBR,SAASS,SAASC,MAAM,GAAG,EAAE,CAAC;AAEpDC,eAAAA,UAAU,MAAM;AACR,QAAAX,SAASS,aAAa,WAAW;AACjCH,wBAAkB,IAAI;AAAA,IAC1B,OAAO;AACHA,wBAAkB,KAAK;AAAA,IAC3B;AAAA,EACJ,GAAG,CAACN,SAASS,QAAQ,CAAC;AAGtB,MAAIJ,kBAAkBE,mBAAmB;AACrC,WAAQK,kCAAAA,IAAAC,UAAA;AAAA,MAASC,IAAG;AAAA,MAAoBC,SAAO;AAAA,IAAC,CAAA;AAAA,EACpD;AAoBA,QAAMC,aAAaA;;AACdC,6CAAA,KAAA,OAAA;AAAA,MAAIC,WAAU;AAAA,MACXC,UAAA,CAACF,kCAAA,KAAA,OAAA;AAAA,QAAIC,WAAU;AAAA,QACXC,UAAA,CAAAP,kCAAA,IAAC,OAAA;AAAA,UACGQ,KAAI;AAAA,UACJC,KAAI;AAAA,UACJH,WAAU;AAAA,QAAA,CACd,yCACC,OAAI;AAAA,UAAAA,WAAU;AAAA,UACXC,UAACF,kCAAA,KAAA,OAAA;AAAA,YAAIC,WAAU;AAAA,YACXC,UAAA,CAACP,kCAAA,IAAA,OAAA;AAAA,cAAIM,WAAU;AAAA,cACVC,YAAYrB,gEAAAwB,gBAAAxB,mBAAawB,gBAAbxB,mBAA0ByB,iBAA1BzB,mBAAyC,OAAzCA,mBAA6C0B,kBAAiB;AAAA,YAC/E,CAAA,GACAZ,kCAAA,IAAC;cAAIM,WAAU;AAAA,cACVC,gEAAYG,mCAAaA,mCAAaC,iBAAgB;AAAA,YAC3D,CAAA,CAAA;AAAA,UACJ,CAAA;AAAA,QACJ,CAAA,CAAA;AAAA,MACJ,CAAA,yCAEC,OAAI;AAAA,QAAAL,WAAU;AAAA,QAGXC,UAAAP,kCAAA,IAAC;UAAIM,WAAU;AAAA,UAUVC,UACGP,kCAAA,IAACa;YAAKX,IAAG;AAAA,YAAoBY,SAASA,MAAMvB,UAAU,KAAK;AAAA,YACvDgB,UAAAF,kCAAA,KAACU,QAAA;AAAA,cACGC,SAASpB,kBAAkB,cAAc,cAAc;AAAA,cACvDU,WAAU;AAAA,cAEVC,UAAA,CAACP,kCAAA,IAAAiB,WAAA;AAAA,gBAAUX,WAAU;AAAA,cAAe,CAAA,GAAE,WAAA;AAAA,YAE1C,CAAA;AAAA,UACJ,CAAA;AAAA,QAER,CAAA;AAAA,MACJ,CAAA,CAAA;AAAA,IA0BJ,CAAA;AAAA;AAIA,SAAAD,kCAAAA,KAAC,OAAI;AAAA,IAAAC,WAAU;AAAA,IAEXC,UAAA,CAAAP,kCAAA,IAAC,OAAI;AAAA,MAAAM,WAAU;AAAA,MACXC,UAAAP,kCAAAA,IAACI,aAAW,CAAA;AAAA,IAChB,CAAA,yCAGC,OAAI;AAAA,MAAAE,WAAU;AAAA,MACXC,UAACF,kCAAA,KAAA,OAAA;AAAA,QAAIC,WAAU;AAAA,QACXC,UAAA,CAAAP,kCAAA,IAAC,OAAA;AAAA,UACGQ,KAAI;AAAA,UACJC,KAAI;AAAA,UACJH,WAAU;AAAA,QAAA,CACd,GACCD,kCAAA,KAAAa,OAAA;AAAA,UAAMC,MAAM7B;AAAAA,UAAQ8B,cAAc7B;AAAAA,UAC/BgB,UAAA,CAAAP,kCAAA,IAACqB,cAAa;AAAA,YAAAC,SAAO;AAAA,YACjBf,UAAAP,kCAAA,IAACe;cAAOC,SAAQ;AAAA,cAAQO,MAAK;AAAA,cACzBhB,UAACP,kCAAA,IAAAwB,MAAA;AAAA,gBAAKlB,WAAU;AAAA,cAAU,CAAA;AAAA,YAC9B,CAAA;AAAA,UACJ,CAAA,GACAN,kCAAA,IAACyB;YAAaC,MAAK;AAAA,YAAOpB,WAAU;AAAA,YAChCC,UAAAP,kCAAAA,IAACI,aAAW,CAAA;AAAA,UAChB,CAAA,CAAA;AAAA,QACJ,CAAA,CAAA;AAAA,MACJ,CAAA;AAAA,IACJ,CAAA,GAGAJ,kCAAA,IAAC,OAAI;AAAA,MAAAM,WAAU;AAAA,MACXC,UAAAP,kCAAA,IAAC,QAAK;AAAA,QAAAM,WAAU;AAAA,QACZC,UAAAP,kCAAAA,IAAC2B,QAAO,CAAA,CAAA;AAAA,MACZ,CAAA;AAAA,IACJ,CAAA,CAAA;AAAA,EACJ,CAAA;AAER;"}