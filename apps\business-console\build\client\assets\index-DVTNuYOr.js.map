{"version": 3, "file": "index-DVTNuYOr.js", "sources": ["../../../node_modules/@radix-ui/react-id/dist/index.mjs"], "sourcesContent": ["// packages/react/id/src/id.tsx\nimport * as React from \"react\";\nimport { useLayoutEffect } from \"@radix-ui/react-use-layout-effect\";\nvar useReactId = React[\"useId\".toString()] || (() => void 0);\nvar count = 0;\nfunction useId(deterministicId) {\n  const [id, setId] = React.useState(useReactId());\n  useLayoutEffect(() => {\n    if (!deterministicId) setId((reactId) => reactId ?? String(count++));\n  }, [deterministicId]);\n  return deterministicId || (id ? `radix-${id}` : \"\");\n}\nexport {\n  useId\n};\n//# sourceMappingURL=index.mjs.map\n"], "names": ["React.useState", "useLayoutEffect"], "mappings": ";;AAGA,IAAI,aAAa,MAAM,QAAQ,SAAQ,CAAE,MAAM,MAAM;AACrD,IAAI,QAAQ;AACZ,SAAS,MAAM,iBAAiB;AAC9B,QAAM,CAAC,IAAI,KAAK,IAAIA,aAAc,SAAC,WAAU,CAAE;AAC/CC,mBAAgB,MAAM;AACE,UAAM,CAAC,YAAY,WAAW,OAAO,OAAO,CAAC;AAAA,EACvE,GAAK,CAAC,eAAe,CAAC;AACpB,SAAO,oBAAoB,KAAK,SAAS,EAAE,KAAK;AAClD;", "x_google_ignoreList": [0]}