{"version": 3, "file": "arrow-up-down-DLQ-Vrdr.js", "sources": ["../../../node_modules/lucide-react/dist/esm/icons/arrow-up-down.js"], "sourcesContent": ["/**\n * @license lucide-react v0.453.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst ArrowUpDown = createLucideIcon(\"ArrowUpDown\", [\n  [\"path\", { d: \"m21 16-4 4-4-4\", key: \"f6ql7i\" }],\n  [\"path\", { d: \"M17 20V4\", key: \"1ejh1v\" }],\n  [\"path\", { d: \"m3 8 4-4 4 4\", key: \"11wl7u\" }],\n  [\"path\", { d: \"M7 4v16\", key: \"1glfcx\" }]\n]);\n\nexport { ArrowUpDown as default };\n//# sourceMappingURL=arrow-up-down.js.map\n"], "names": [], "mappings": ";AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASK,MAAC,cAAc,iBAAiB,eAAe;AAAA,EAClD,CAAC,QAAQ,EAAE,GAAG,kBAAkB,KAAK,SAAQ,CAAE;AAAA,EAC/C,CAAC,QAAQ,EAAE,GAAG,YAAY,KAAK,SAAQ,CAAE;AAAA,EACzC,CAAC,QAAQ,EAAE,GAAG,gBAAgB,KAAK,SAAQ,CAAE;AAAA,EAC7C,CAAC,QAAQ,EAAE,GAAG,WAAW,KAAK,SAAU,CAAA;AAC1C,CAAC;", "x_google_ignoreList": [0]}