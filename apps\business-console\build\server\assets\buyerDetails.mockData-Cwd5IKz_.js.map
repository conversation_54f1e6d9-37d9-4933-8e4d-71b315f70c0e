{"version": 3, "file": "buyerDetails.mockData-Cwd5IKz_.js", "sources": ["../../../app/services/__mocks__/buyerDetails.mockData.ts"], "sourcesContent": ["import {BuyerDetailsResponse} from \"~/types/api/businessConsoleService/BuyerDetailsResponse\";\r\n\r\nexport const buyerDetailsMockData:BuyerDetailsResponse = {\r\n    \"buyerId\": 507,\r\n    \"buyerName\": \"Day by day fresh ( GDP)\",\r\n    \"ownerName\": \"Day by day fresh ( GDP) \",\r\n    \"Address\": \"Kodigehalli main road\",\r\n    \"mobileNumber\": \"8880268030\",\r\n    \"totalOrders\": 1,\r\n    \"totalAmount\": 1152,\r\n    \"pendingAmount\": 0,\r\n    \"lastOrderedDate\": \"2024-10-11\",\r\n    \"orderDetails\": [\r\n        {\r\n            \"id\": 262,\r\n            \"sellerName\": \"Jetta Agro\",\r\n            \"deliveryDate\": \"2024-10-12\",\r\n            \"deliveryTime\": \"06:00:00\",\r\n            \"estDeliveryTime\": \"06:00:00\",\r\n            \"status\": \"Delivered\",\r\n            \"totalItemCount\": 1,\r\n            \"deliveredItemCount\": 1,\r\n            \"cancelledItemCount\": 0,\r\n            \"totalWeight\": 36,\r\n            \"totalOrderAmount\": 1152,\r\n            \"deliveryCharges\": 0,\r\n            \"codAmount\": 1152,\r\n            \"discountAmount\": 0,\r\n            \"totalAmount\": 1152,\r\n            \"isPending\": false,\r\n            \"farmers\": [\r\n                {\r\n                    \"farmerId\": 1,\r\n                    \"farmerName\": \"Jetta Agro\",\r\n                    \"farmerRating\": 4,\r\n                    \"items\": [\r\n                        {\r\n                            \"orderId\": 456,\r\n                            \"itemName\": \"Banana Robusta Premium(18kg Crate)\",\r\n                            \"itemUrl\": \"https://fm-assets.s3.ap-south-1.amazonaws.com/prod/items/rob%20new%20%281%29_1720943354124.png\",\r\n                            \"qty\": 36,\r\n                            \"price\": 32,\r\n                            \"amount\": 1152,\r\n                            \"status\": \"Delivered\",\r\n                            \"unit\": \"KG\"\r\n                        }\r\n                    ]\r\n                }\r\n            ],\r\n            \"delayPaymentPendingAmount\": 0,\r\n            \"sellerId\": 1\r\n        }\r\n    ]\r\n}\r\n"], "names": [], "mappings": "AAEO,MAAM,uBAA4C;AAAA,EACrD,WAAW;AAAA,EACX,aAAa;AAAA,EACb,aAAa;AAAA,EACb,WAAW;AAAA,EACX,gBAAgB;AAAA,EAChB,eAAe;AAAA,EACf,eAAe;AAAA,EACf,iBAAiB;AAAA,EACjB,mBAAmB;AAAA,EACnB,gBAAgB;AAAA,IACZ;AAAA,MACI,MAAM;AAAA,MACN,cAAc;AAAA,MACd,gBAAgB;AAAA,MAChB,gBAAgB;AAAA,MAChB,mBAAmB;AAAA,MACnB,UAAU;AAAA,MACV,kBAAkB;AAAA,MAClB,sBAAsB;AAAA,MACtB,sBAAsB;AAAA,MACtB,eAAe;AAAA,MACf,oBAAoB;AAAA,MACpB,mBAAmB;AAAA,MACnB,aAAa;AAAA,MACb,kBAAkB;AAAA,MAClB,eAAe;AAAA,MACf,aAAa;AAAA,MACb,WAAW;AAAA,QACP;AAAA,UACI,YAAY;AAAA,UACZ,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,SAAS;AAAA,YACL;AAAA,cACI,WAAW;AAAA,cACX,YAAY;AAAA,cACZ,WAAW;AAAA,cACX,OAAO;AAAA,cACP,SAAS;AAAA,cACT,UAAU;AAAA,cACV,UAAU;AAAA,cACV,QAAQ;AAAA,YAAA;AAAA,UACZ;AAAA,QACJ;AAAA,MAER;AAAA,MACA,6BAA6B;AAAA,MAC7B,YAAY;AAAA,IAAA;AAAA,EAChB;AAER;"}