import { j as jsxRuntimeExports } from "./jsx-runtime-bB2y7OuD.js";
import { B as But<PERSON> } from "./button-ByAXMyvk.js";
import { T as Table, a as TableHeader, b as TableRow, c as TableHead, d as TableBody, e as TableCell } from "./table-CAxdJZsY.js";
import { L as Link } from "./components-D7UvGag_.js";
import { c as createLucideIcon } from "./createLucideIcon-uwkRm45G.js";
import "./index-z_byfFrQ.js";
import "./index-ImHKLo0a.js";
import "./utils-GkgzjW3c.js";
import "./index-Vp2vNLNM.js";
import "./index-DhHTcibu.js";
/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */
const ExternalLink = createLucideIcon("ExternalLink", [
  ["path", { d: "M15 3h6v6", key: "1q9fwt" }],
  ["path", { d: "M10 14 21 3", key: "gplh6r" }],
  ["path", { d: "M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6", key: "a6xqqp" }]
]);
const templates = [{
  name: "complete_order_cancellation",
  category: "Utility",
  language: "English",
  content: "Dear {{1}}, We're sorry, but your order #{{2}} couldn't be fulfilled due to stock unavailability.❌ We apologize for the inconvenience and hope to serve you again. 🙏 Best, farmersMandi",
  status: "Active – Quality pending",
  messagesDelivered: 0,
  messageReadRate: "1",
  topBlockReason: "––",
  lastEdited: "4 Sep 2024"
}, {
  name: "out_for_delivery",
  category: "Utility",
  language: "English",
  content: "Good morning {{1}}! 🌅 Your order #{{2}} is on the way and will be delivered soon. 🚚 Total payable on delivery: *₹{{3}}* Thanks for choosing us! 🙏 Best, farmersMandi",
  status: "Active – Quality pending",
  messagesDelivered: 0,
  messageReadRate: "2",
  topBlockReason: "––",
  lastEdited: "4 Sep 2024"
}, {
  name: "delivery_confirmation_with_credit",
  category: "Utility",
  language: "English",
  content: "Hi {{1}} 👋, Your order #{{2}} has been delivered. ✅ Order Amount : ₹{{3}} Total Amount due: *₹{{4}}*. 💰 Please pay soon. 💳 Best, farmersMandi",
  status: "Active – Quality pending",
  messagesDelivered: 0,
  messageReadRate: "0",
  topBlockReason: "––",
  lastEdited: "4 Sep 2024"
}, {
  name: "delivery_completed",
  category: "Utility",
  language: "English",
  content: "Hi {{1}} 👋, Your order *#{{2}}* has been delivered. ✅ We hope you're happy with the fresh produce! 🥗 Total amount paid: *₹{{3}}*. 💰 Thanks for choosing us! 🙏 Best, farmersMandi",
  status: "Active – Quality pending",
  messagesDelivered: 0,
  messageReadRate: "2",
  topBlockReason: "––",
  lastEdited: "4 Sep 2024"
}, {
  name: "f_business_order_confirm",
  category: "Utility",
  language: "English",
  content: "Hello {{1}} 👋, Thank you for your order! 🛒 We've received your request for the following items: {{2}} Your order *#{{3}}* will be delivered tomorrow morning. 🚚 Total payable: ₹{{4}}. 💰 Thanks for choosing farmersMandi ! 🙏 Best, farmersMandi",
  status: "Active – Quality pending",
  messagesDelivered: 0,
  messageReadRate: "1",
  topBlockReason: "––",
  lastEdited: "4 Sep 2024"
}, {
  name: "hello_world",
  category: "Utility",
  language: "English (US)",
  content: "Welcome and congratulations!! This message demonstrates your ability to send a WhatsApp message notification from the Cloud API, hosted by Meta. Thank you for taking the time to test with us.",
  status: "Active – Quality pending",
  messagesDelivered: 1,
  messageReadRate: "0% (0)",
  topBlockReason: "––",
  lastEdited: "31 Aug 2024"
}];
function WhatsAppTemplates() {
  return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
    className: "container mx-auto py-10",
    children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
      className: "flex justify-between items-center mb-6",
      children: [/* @__PURE__ */ jsxRuntimeExports.jsx("h1", {
        className: "text-2xl font-bold",
        children: "WhatsApp Templates"
      }), /* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
        asChild: true,
        children: /* @__PURE__ */ jsxRuntimeExports.jsxs(Link, {
          to: "https://business.facebook.com/latest/whatsapp_manager/message_templates",
          target: "_blank",
          rel: "noopener noreferrer",
          children: [/* @__PURE__ */ jsxRuntimeExports.jsx(ExternalLink, {
            className: "mr-2 h-4 w-4"
          }), " Templates Manager"]
        })
      })]
    }), /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
      className: "rounded-md border overflow-x-auto",
      children: /* @__PURE__ */ jsxRuntimeExports.jsxs(Table, {
        children: [/* @__PURE__ */ jsxRuntimeExports.jsx(TableHeader, {
          children: /* @__PURE__ */ jsxRuntimeExports.jsxs(TableRow, {
            children: [/* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
              children: "Template Name"
            }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
              children: "Category"
            }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
              children: "Language"
            }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
              children: "Status"
            }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
              children: "Messages Delivered"
            }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
              children: "Message Read Rate"
            }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
              children: "Top Block Reason"
            }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
              children: "Last Edited"
            })]
          })
        }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableBody, {
          children: templates.map((template) => /* @__PURE__ */ jsxRuntimeExports.jsxs(TableRow, {
            children: [/* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
              className: "font-medium",
              children: template.name
            }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
              children: template.category
            }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
              children: template.language
            }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
              children: /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                className: "px-2 py-1 rounded-full text-xs font-semibold bg-yellow-100 text-yellow-800",
                children: template.status
              })
            }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
              children: template.messagesDelivered
            }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
              children: template.messageReadRate
            }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
              children: template.topBlockReason
            }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
              children: template.lastEdited
            })]
          }, template.name))
        })]
      })
    })]
  });
}
export {
  WhatsAppTemplates as default
};
//# sourceMappingURL=home.whatsapp-MRy1eJ8v.js.map
