{"version": 3, "file": "_index--Zbm0e8U.js", "sources": ["../../../app/pages/LandingPage.tsx", "../../../app/routes/_index.tsx"], "sourcesContent": ["import { Link } from \"@remix-run/react\";\r\n\r\ninterface LandingPageProps {\r\n    brandName: string;\r\n    contactEmail: string;\r\n    websiteUrl: string;\r\n}\r\n\r\nexport default function LandingPage({ brandName, contactEmail, websiteUrl }: LandingPageProps) {\r\n    return (\r\n        <div className=\"max-w-[1200px] mx-auto text-gray-800 leading-relaxed\">\r\n            <header className=\"flex justify-between items-center py-6\">\r\n                <div className=\"flex items-center gap-2\">\r\n                    <div>\r\n                        <div className=\"text-xl font-semibold text-fm-green\">{brandName}</div>\r\n                        <div className=\"text-xs text-gray-500\">by Brih Solutions</div>\r\n                    </div>\r\n                </div>\r\n                <nav className=\"flex gap-6\">\r\n                    {[\"About\", \"Features\", \"Impact\", \"Contact\"].map((item) => (\r\n                        <a\r\n                            key={item}\r\n                            href={`#${item.toLowerCase()}`}\r\n                            className=\"text-gray-600 hover:text-fm-green transition-colors\"\r\n                        >\r\n                            {item}\r\n                        </a>\r\n                    ))}\r\n                </nav>\r\n            </header>\r\n\r\n            <main className=\"space-y-16\">\r\n                <section className=\"text-center bg-fm-light rounded-xl p-8 shadow-md transform hover:-translate-y-1 transition-all duration-300\">\r\n                    <h1 className=\"text-4xl font-bold mb-4 text-fm-green\">Revolutionizing Vegetable Distribution</h1>\r\n                    <p className=\"text-xl text-gray-600 max-w-2xl mx-auto\">Connecting wholesalers to retailers with cutting-edge technology</p>\r\n                </section>\r\n\r\n                <section className=\"grid md:grid-cols-2 gap-8\">\r\n                    <div className=\"bg-fm-light rounded-xl p-8 shadow-md transform hover:-translate-y-1 transition-all duration-300\">\r\n                        <h2 className=\"text-2xl font-semibold mb-4\">About {brandName}</h2>\r\n                        <p className=\"text-gray-600 mb-6\">\r\n                            {brandName} is a pioneering platform that streamlines the vegetable supply chain,\r\n                            connecting wholesalers with retailers efficiently and reducing waste.\r\n                        </p>\r\n                        <h3 className=\"text-xl font-semibold mb-4\">Our Customers</h3>\r\n                        <div className=\"grid grid-cols-4 gap-8 px-4\">\r\n                            {[...Array(8)].map((_, index) => (\r\n                                <div key={index}\r\n                                     className=\"bg-white p-3 rounded-full w-20 h-20 flex items-center justify-center shadow-sm hover:shadow-md transition-shadow\">\r\n                                    <img\r\n                                        src={`/customer${index + 1}.png?height=60&width=60`}\r\n                                        alt={`Customer logo ${index + 1}`}\r\n                                        className=\"w-14 h-14 object-contain\"\r\n                                    />\r\n                                </div>\r\n                            ))}\r\n                        </div>\r\n                    </div>\r\n                    <div className=\"relative group h-full\">\r\n                        <div\r\n                            className=\"absolute -inset-[1px] bg-gradient-to-r from-fm-green to-fm-hover rounded-xl opacity-75 group-hover:opacity-100 transition duration-300\"></div>\r\n                        <div\r\n                            className=\"relative bg-white rounded-xl p-8 shadow-lg h-full flex flex-col justify-between\">\r\n                            <div className=\"space-y-8\">\r\n                                <div className=\"flex justify-between items-center\">\r\n                                    <h2 className=\"text-2xl font-semibold\">Login to mNet</h2>\r\n                                    <img\r\n                                        src=\"/mnet-logo.svg\"\r\n                                        alt=\"mNet Logo\"\r\n                                        width={100}\r\n                                        height={100}\r\n                                    />\r\n                                </div>\r\n\r\n                                <ul className=\"space-y-4\">\r\n                                    <li className=\"flex items-center gap-2\">\r\n                                        <svg className=\"w-5 h-5 text-fm-green\" fill=\"none\" strokeLinecap=\"round\"\r\n                                             strokeLinejoin=\"round\" strokeWidth=\"2\" viewBox=\"0 0 24 24\"\r\n                                             stroke=\"currentColor\">\r\n                                            <path d=\"M5 13l4 4L19 7\"></path>\r\n                                        </svg>\r\n                                        <span>Launch your own app within a week</span>\r\n                                    </li>\r\n                                    <li className=\"flex items-center gap-2\">\r\n                                        <svg className=\"w-5 h-5 text-fm-green\" fill=\"none\" strokeLinecap=\"round\"\r\n                                             strokeLinejoin=\"round\" strokeWidth=\"2\" viewBox=\"0 0 24 24\"\r\n                                             stroke=\"currentColor\">\r\n                                            <path d=\"M5 13l4 4L19 7\"></path>\r\n                                        </svg>\r\n                                        <span>Go online and increase your sales</span>\r\n                                    </li>\r\n                                    <li className=\"flex items-center gap-2\">\r\n                                        <svg className=\"w-5 h-5 text-fm-green\" fill=\"none\" strokeLinecap=\"round\"\r\n                                             strokeLinejoin=\"round\" strokeWidth=\"2\" viewBox=\"0 0 24 24\"\r\n                                             stroke=\"currentColor\">\r\n                                            <path d=\"M5 13l4 4L19 7\"></path>\r\n                                        </svg>\r\n                                        <span>Build your own brand and gain more trust</span>\r\n                                    </li>\r\n                                    <li className=\"flex items-center gap-2\">\r\n                                        <svg className=\"w-5 h-5 text-fm-green\" fill=\"none\" strokeLinecap=\"round\"\r\n                                             strokeLinejoin=\"round\" strokeWidth=\"2\" viewBox=\"0 0 24 24\"\r\n                                             stroke=\"currentColor\">\r\n                                            <path d=\"M5 13l4 4L19 7\"></path>\r\n                                        </svg>\r\n                                        <span>Streamline customer communications</span>\r\n                                    </li>\r\n                                    <li className=\"flex items-center gap-2\">\r\n                                        <svg className=\"w-5 h-5 text-fm-green\" fill=\"none\" strokeLinecap=\"round\"\r\n                                             strokeLinejoin=\"round\" strokeWidth=\"2\" viewBox=\"0 0 24 24\"\r\n                                             stroke=\"currentColor\">\r\n                                            <path d=\"M5 13l4 4L19 7\"></path>\r\n                                        </svg>\r\n                                        <span>Send timely updates and reminders</span>\r\n                                    </li>\r\n                                    <li className=\"flex items-center gap-2\">\r\n                                        <svg className=\"w-5 h-5 text-fm-green\" fill=\"none\" strokeLinecap=\"round\"\r\n                                             strokeLinejoin=\"round\" strokeWidth=\"2\" viewBox=\"0 0 24 24\"\r\n                                             stroke=\"currentColor\">\r\n                                            <path d=\"M5 13l4 4L19 7\"></path>\r\n                                        </svg>\r\n                                        <span>Share promotional messages</span>\r\n                                    </li>\r\n                                </ul>\r\n                            </div>\r\n\r\n                            <Link\r\n                                to=\"/login\"\r\n                                className=\"block w-full text-center px-6 py-3 bg-fm-green text-white rounded-md hover:bg-fm-hover transition-colors font-semibold mt-8\"\r\n                            >\r\n                                Login to mNet\r\n                            </Link>\r\n                        </div>\r\n                    </div>\r\n                </section>\r\n\r\n                <section id=\"features\"\r\n                         className=\"bg-fm-light rounded-xl p-8 shadow-md transform hover:-translate-y-1 transition-all duration-300\">\r\n                    <h2 className=\"text-2xl font-semibold mb-6 text-center\">Empowering the Supply Chain</h2>\r\n                    <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\r\n                        <div className=\"bg-white p-6 rounded-lg shadow-sm\">\r\n                            <h3 className=\"text-lg font-medium text-fm-green mb-3\">Vast Network</h3>\r\n                            <p className=\"text-gray-600\">Access over 8,000 retailers, vegetable shops, hotels, and\r\n                                supermarkets.</p>\r\n                        </div>\r\n                        <div className=\"bg-white p-6 rounded-lg shadow-sm\">\r\n                            <h3 className=\"text-lg font-medium text-fm-green mb-3\">Dedicated Sales Force</h3>\r\n                            <p className=\"text-gray-600\">Benefit from our network of sales executives to maximize your\r\n                                sales.</p>\r\n                        </div>\r\n                        <div className=\"bg-white p-6 rounded-lg shadow-sm\">\r\n                            <h3 className=\"text-lg font-medium text-fm-green mb-3\">Tech-Driven Operations</h3>\r\n                            <p className=\"text-gray-600\">Utilize our tech tools to streamline and scale your business\r\n                                operations.</p>\r\n                        </div>\r\n                    </div>\r\n                </section>\r\n\r\n                <section id=\"impact\"\r\n                         className=\"bg-fm-light rounded-xl p-8 shadow-md transform hover:-translate-y-1 transition-all duration-300\">\r\n                    <h2 className=\"text-2xl font-semibold mb-6 text-center\">Our Impact</h2>\r\n                    <div className=\"flex flex-col md:flex-row justify-around text-center mb-6\">\r\n                        {[\r\n                            {number: \"10,000+\", label: \"Tonnes of Produce Sold\"},\r\n                            {number: \"8,000+\", label: \"Active Customers\"},\r\n                            {number: \"180,000+\", label: \"Successful Deliveries\"}\r\n                        ].map((stat, index) => (\r\n                            <div key={index} className=\"mb-4 md:mb-0\">\r\n                                <span className=\"block text-3xl font-semibold text-fm-green\">{stat.number}</span>\r\n                                <span className=\"text-gray-600\">{stat.label}</span>\r\n                            </div>\r\n                        ))}\r\n                    </div>\r\n                </section>\r\n\r\n                <section id=\"contact\"\r\n                         className=\"bg-fm-light rounded-xl p-8 shadow-md transform hover:-translate-y-1 transition-all duration-300\">\r\n                    <h2 className=\"text-2xl font-semibold mb-4 text-center\">Join the Revolution</h2>\r\n                    <p className=\"text-gray-600 mb-6 max-w-2xl mx-auto text-center\">\r\n                        Whether you're a retailer or wholesaler, {brandName} is here to propel your business forward.\r\n                    </p>\r\n                    <div className=\"flex justify-center gap-4\">\r\n                        <a\r\n                            href={`mailto:${contactEmail}`}\r\n                            className=\"px-6 py-3 bg-fm-green text-white rounded-md hover:bg-fm-hover transition-colors\"\r\n                        >\r\n                            Contact Us\r\n                        </a>\r\n                        <a\r\n                            href=\"tel:9036728830\"\r\n                            className=\"px-6 py-3 border border-fm-green text-fm-green rounded-md hover:bg-fm-light transition-colors\"\r\n                        >\r\n                            Call Us\r\n                        </a>\r\n                    </div>\r\n                </section>\r\n            </main>\r\n\r\n            <footer className=\"text-center py-8 text-gray-600\">\r\n                <p>&copy; 2024 {brandName} by Brih Solutions Private Limited</p>\r\n                <a\r\n                    href={`https://${websiteUrl}`}\r\n                    target=\"_blank\"\r\n                    rel=\"noopener noreferrer\"\r\n                    className=\"text-fm-green hover:underline\"\r\n                >\r\n                    {websiteUrl}\r\n                </a>\r\n            </footer>\r\n        </div>\r\n    );\r\n}\r\n", "// routes/index.tsx\r\nimport type { MetaFunction, LoaderFunction } from \"@remix-run/node\";\r\nimport { json, redirect } from \"@remix-run/node\";\r\nimport { getSession } from '~/utils/session.server';\r\nimport { useLoaderData } from \"@remix-run/react\";\r\nimport LandingPage from \"~/pages/LandingPage\";\r\n\r\ntype LoaderData = {\r\n  brandName: string;\r\n  contactEmail: string;\r\n  websiteUrl: string;\r\n};\r\n\r\nexport const meta: MetaFunction = () => {\r\n  return [\r\n    { title: \"mNet - Revolutionizing Vegetable Distribution\" },\r\n    { name: \"description\", content: \"Connecting wholesalers to retailers with cutting-edge technology\" },\r\n  ];\r\n};\r\n\r\nexport const loader: LoaderFunction = async ({ request }) => {\r\n  const host = request.headers.get(\"host\") || \"\";\r\n  \r\n \r\n  const session = await getSession(request.headers.get('Cookie'));\r\n  const access_token = session.get('access_token');\r\n  const url = new URL(request.url);\r\n  const returnTo = url.searchParams.get('returnTo');\r\n\r\n  if (access_token) {\r\n    return redirect(returnTo ? `/home/<USER>'/home/<USER>');\r\n  }\r\n\r\n  // const url = new URL(\"https://farmersmandi.in/\");\r\n\r\n  const landingPageDomains = [\"farmersmandi.in\", \"mnetlive.com\"]\r\n\r\n  if(!landingPageDomains.includes(url.hostname)) {\r\n    return redirect(returnTo ? `/login?returnTo=${returnTo}` : \"/login\");\r\n  }\r\n\r\n  const isMnetDomain = host.includes(\"mnetlive.com\");\r\n  const brandData: LoaderData = {\r\n    brandName: isMnetDomain ? \"mNet\" : \"farmersMandi\",\r\n    contactEmail: isMnetDomain ? \"<EMAIL>\" : \"<EMAIL>\",\r\n    websiteUrl: isMnetDomain ? \"www.mnetlive.com\" : \"www.farmersmandi.in\"\r\n  };\r\n\r\n  return json(brandData);\r\n};\r\n\r\nexport default function Index() {\r\n  const brandData = useLoaderData<LoaderData>();\r\n  return <LandingPage {...brandData} />;\r\n}\r\n"], "names": ["jsxs", "jsx", "meta", "title", "name", "content", "Index", "brandData", "useLoaderData", "LandingPage"], "mappings": ";;;;AAQA,SAAwB,YAAY,EAAE,WAAW,cAAc,cAAgC;AAEvF,SAAAA,kCAAA,KAAC,OAAI,EAAA,WAAU,wDACX,UAAA;AAAA,IAACA,kCAAAA,KAAA,UAAA,EAAO,WAAU,0CACd,UAAA;AAAA,MAAAC,sCAAC,OAAI,EAAA,WAAU,2BACX,UAAAD,kCAAA,KAAC,OACG,EAAA,UAAA;AAAA,QAACC,kCAAA,IAAA,OAAA,EAAI,WAAU,uCAAuC,UAAU,WAAA;AAAA,QAC/DA,kCAAA,IAAA,OAAA,EAAI,WAAU,yBAAwB,UAAiB,oBAAA,CAAA;AAAA,MAAA,EAAA,CAC5D,EACJ,CAAA;AAAA,MACCA,kCAAAA,IAAA,OAAA,EAAI,WAAU,cACV,UAAC,CAAA,SAAS,YAAY,UAAU,SAAS,EAAE,IAAI,CAAC,SAC7CA,kCAAA;AAAA,QAAC;AAAA,QAAA;AAAA,UAEG,MAAM,IAAI,KAAK,YAAa,CAAA;AAAA,UAC5B,WAAU;AAAA,UAET,UAAA;AAAA,QAAA;AAAA,QAJI;AAAA,MAAA,CAMZ,EACL,CAAA;AAAA,IAAA,GACJ;AAAA,IAEAD,kCAAAA,KAAC,QAAK,EAAA,WAAU,cACZ,UAAA;AAAA,MAACA,kCAAAA,KAAA,WAAA,EAAQ,WAAU,+GACf,UAAA;AAAA,QAACC,kCAAA,IAAA,MAAA,EAAG,WAAU,yCAAwC,UAAsC,0CAAA;AAAA,QAC3FA,kCAAA,IAAA,KAAA,EAAE,WAAU,2CAA0C,UAAgE,mEAAA,CAAA;AAAA,MAAA,GAC3H;AAAA,MAEAD,kCAAAA,KAAC,WAAQ,EAAA,WAAU,6BACf,UAAA;AAAA,QAACA,kCAAAA,KAAA,OAAA,EAAI,WAAU,mGACX,UAAA;AAAA,UAACA,kCAAAA,KAAA,MAAA,EAAG,WAAU,+BAA8B,UAAA;AAAA,YAAA;AAAA,YAAO;AAAA,UAAA,GAAU;AAAA,UAC7DA,kCAAAA,KAAC,KAAE,EAAA,WAAU,sBACR,UAAA;AAAA,YAAA;AAAA,YAAU;AAAA,UAAA,GAEf;AAAA,UACCC,kCAAA,IAAA,MAAA,EAAG,WAAU,8BAA6B,UAAa,iBAAA;AAAA,UACvDA,kCAAAA,IAAA,OAAA,EAAI,WAAU,+BACV,UAAC,CAAA,GAAG,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,UACnBA,kCAAA;AAAA,YAAC;AAAA,YAAA;AAAA,cACI,WAAU;AAAA,cACX,UAAAA,kCAAA;AAAA,gBAAC;AAAA,gBAAA;AAAA,kBACG,KAAK,YAAY,QAAQ,CAAC;AAAA,kBAC1B,KAAK,iBAAiB,QAAQ,CAAC;AAAA,kBAC/B,WAAU;AAAA,gBAAA;AAAA,cAAA;AAAA,YACd;AAAA,YANM;AAAA,UAAA,CAQb,EACL,CAAA;AAAA,QAAA,GACJ;AAAA,QACAD,kCAAAA,KAAC,OAAI,EAAA,WAAU,yBACX,UAAA;AAAA,UAAAC,kCAAA;AAAA,YAAC;AAAA,YAAA;AAAA,cACG,WAAU;AAAA,YAAA;AAAA,UAAyI;AAAA,UACvJD,kCAAA;AAAA,YAAC;AAAA,YAAA;AAAA,cACG,WAAU;AAAA,cACV,UAAA;AAAA,gBAACA,kCAAAA,KAAA,OAAA,EAAI,WAAU,aACX,UAAA;AAAA,kBAACA,kCAAAA,KAAA,OAAA,EAAI,WAAU,qCACX,UAAA;AAAA,oBAACC,kCAAA,IAAA,MAAA,EAAG,WAAU,0BAAyB,UAAa,iBAAA;AAAA,oBACpDA,kCAAA;AAAA,sBAAC;AAAA,sBAAA;AAAA,wBACG,KAAI;AAAA,wBACJ,KAAI;AAAA,wBACJ,OAAO;AAAA,wBACP,QAAQ;AAAA,sBAAA;AAAA,oBAAA;AAAA,kBACZ,GACJ;AAAA,kBAEAD,kCAAAA,KAAC,MAAG,EAAA,WAAU,aACV,UAAA;AAAA,oBAACA,kCAAAA,KAAA,MAAA,EAAG,WAAU,2BACV,UAAA;AAAA,sBAAAC,kCAAA;AAAA,wBAAC;AAAA,wBAAA;AAAA,0BAAI,WAAU;AAAA,0BAAwB,MAAK;AAAA,0BAAO,eAAc;AAAA,0BAC5D,gBAAe;AAAA,0BAAQ,aAAY;AAAA,0BAAI,SAAQ;AAAA,0BAC/C,QAAO;AAAA,0BACR,UAAAA,kCAAAA,IAAC,QAAK,EAAA,GAAE,iBAAiB,CAAA;AAAA,wBAAA;AAAA,sBAC7B;AAAA,sBACAA,kCAAAA,IAAC,UAAK,UAAiC,oCAAA,CAAA;AAAA,oBAAA,GAC3C;AAAA,oBACAD,kCAAAA,KAAC,MAAG,EAAA,WAAU,2BACV,UAAA;AAAA,sBAAAC,kCAAA;AAAA,wBAAC;AAAA,wBAAA;AAAA,0BAAI,WAAU;AAAA,0BAAwB,MAAK;AAAA,0BAAO,eAAc;AAAA,0BAC5D,gBAAe;AAAA,0BAAQ,aAAY;AAAA,0BAAI,SAAQ;AAAA,0BAC/C,QAAO;AAAA,0BACR,UAAAA,kCAAAA,IAAC,QAAK,EAAA,GAAE,iBAAiB,CAAA;AAAA,wBAAA;AAAA,sBAC7B;AAAA,sBACAA,kCAAAA,IAAC,UAAK,UAAiC,oCAAA,CAAA;AAAA,oBAAA,GAC3C;AAAA,oBACAD,kCAAAA,KAAC,MAAG,EAAA,WAAU,2BACV,UAAA;AAAA,sBAAAC,kCAAA;AAAA,wBAAC;AAAA,wBAAA;AAAA,0BAAI,WAAU;AAAA,0BAAwB,MAAK;AAAA,0BAAO,eAAc;AAAA,0BAC5D,gBAAe;AAAA,0BAAQ,aAAY;AAAA,0BAAI,SAAQ;AAAA,0BAC/C,QAAO;AAAA,0BACR,UAAAA,kCAAAA,IAAC,QAAK,EAAA,GAAE,iBAAiB,CAAA;AAAA,wBAAA;AAAA,sBAC7B;AAAA,sBACAA,kCAAAA,IAAC,UAAK,UAAwC,2CAAA,CAAA;AAAA,oBAAA,GAClD;AAAA,oBACAD,kCAAAA,KAAC,MAAG,EAAA,WAAU,2BACV,UAAA;AAAA,sBAAAC,kCAAA;AAAA,wBAAC;AAAA,wBAAA;AAAA,0BAAI,WAAU;AAAA,0BAAwB,MAAK;AAAA,0BAAO,eAAc;AAAA,0BAC5D,gBAAe;AAAA,0BAAQ,aAAY;AAAA,0BAAI,SAAQ;AAAA,0BAC/C,QAAO;AAAA,0BACR,UAAAA,kCAAAA,IAAC,QAAK,EAAA,GAAE,iBAAiB,CAAA;AAAA,wBAAA;AAAA,sBAC7B;AAAA,sBACAA,kCAAAA,IAAC,UAAK,UAAkC,qCAAA,CAAA;AAAA,oBAAA,GAC5C;AAAA,oBACAD,kCAAAA,KAAC,MAAG,EAAA,WAAU,2BACV,UAAA;AAAA,sBAAAC,kCAAA;AAAA,wBAAC;AAAA,wBAAA;AAAA,0BAAI,WAAU;AAAA,0BAAwB,MAAK;AAAA,0BAAO,eAAc;AAAA,0BAC5D,gBAAe;AAAA,0BAAQ,aAAY;AAAA,0BAAI,SAAQ;AAAA,0BAC/C,QAAO;AAAA,0BACR,UAAAA,kCAAAA,IAAC,QAAK,EAAA,GAAE,iBAAiB,CAAA;AAAA,wBAAA;AAAA,sBAC7B;AAAA,sBACAA,kCAAAA,IAAC,UAAK,UAAiC,oCAAA,CAAA;AAAA,oBAAA,GAC3C;AAAA,oBACAD,kCAAAA,KAAC,MAAG,EAAA,WAAU,2BACV,UAAA;AAAA,sBAAAC,kCAAA;AAAA,wBAAC;AAAA,wBAAA;AAAA,0BAAI,WAAU;AAAA,0BAAwB,MAAK;AAAA,0BAAO,eAAc;AAAA,0BAC5D,gBAAe;AAAA,0BAAQ,aAAY;AAAA,0BAAI,SAAQ;AAAA,0BAC/C,QAAO;AAAA,0BACR,UAAAA,kCAAAA,IAAC,QAAK,EAAA,GAAE,iBAAiB,CAAA;AAAA,wBAAA;AAAA,sBAC7B;AAAA,sBACAA,kCAAAA,IAAC,UAAK,UAA0B,6BAAA,CAAA;AAAA,oBAAA,EACpC,CAAA;AAAA,kBAAA,EACJ,CAAA;AAAA,gBAAA,GACJ;AAAA,gBAEAA,kCAAA;AAAA,kBAAC;AAAA,kBAAA;AAAA,oBACG,IAAG;AAAA,oBACH,WAAU;AAAA,oBACb,UAAA;AAAA,kBAAA;AAAA,gBAAA;AAAA,cAED;AAAA,YAAA;AAAA,UAAA;AAAA,QACJ,EACJ,CAAA;AAAA,MAAA,GACJ;AAAA,MAEAD,kCAAA;AAAA,QAAC;AAAA,QAAA;AAAA,UAAQ,IAAG;AAAA,UACH,WAAU;AAAA,UACf,UAAA;AAAA,YAACC,kCAAA,IAAA,MAAA,EAAG,WAAU,2CAA0C,UAA2B,+BAAA;AAAA,YACnFD,kCAAAA,KAAC,OAAI,EAAA,WAAU,yCACX,UAAA;AAAA,cAACA,kCAAAA,KAAA,OAAA,EAAI,WAAU,qCACX,UAAA;AAAA,gBAACC,kCAAA,IAAA,MAAA,EAAG,WAAU,0CAAyC,UAAY,gBAAA;AAAA,gBAClEA,kCAAA,IAAA,KAAA,EAAE,WAAU,iBAAgB,UACZ,0EAAA,CAAA;AAAA,cAAA,GACrB;AAAA,cACAD,kCAAAA,KAAC,OAAI,EAAA,WAAU,qCACX,UAAA;AAAA,gBAACC,kCAAA,IAAA,MAAA,EAAG,WAAU,0CAAyC,UAAqB,yBAAA;AAAA,gBAC3EA,kCAAA,IAAA,KAAA,EAAE,WAAU,iBAAgB,UACnB,uEAAA,CAAA;AAAA,cAAA,GACd;AAAA,cACAD,kCAAAA,KAAC,OAAI,EAAA,WAAU,qCACX,UAAA;AAAA,gBAACC,kCAAA,IAAA,MAAA,EAAG,WAAU,0CAAyC,UAAsB,0BAAA;AAAA,gBAC5EA,kCAAA,IAAA,KAAA,EAAE,WAAU,iBAAgB,UACd,2EAAA,CAAA;AAAA,cAAA,EACnB,CAAA;AAAA,YAAA,EACJ,CAAA;AAAA,UAAA;AAAA,QAAA;AAAA,MACJ;AAAA,MAEAD,kCAAA;AAAA,QAAC;AAAA,QAAA;AAAA,UAAQ,IAAG;AAAA,UACH,WAAU;AAAA,UACf,UAAA;AAAA,YAACC,kCAAA,IAAA,MAAA,EAAG,WAAU,2CAA0C,UAAU,cAAA;AAAA,YAClEA,kCAAAA,IAAC,OAAI,EAAA,WAAU,6DACV,UAAA;AAAA,cACG,EAAC,QAAQ,WAAW,OAAO,yBAAwB;AAAA,cACnD,EAAC,QAAQ,UAAU,OAAO,mBAAkB;AAAA,cAC5C,EAAC,QAAQ,YAAY,OAAO,wBAAuB;AAAA,YAAA,EACrD,IAAI,CAAC,MAAM,UACRD,kCAAAA,KAAA,OAAA,EAAgB,WAAU,gBACvB,UAAA;AAAA,cAAAC,kCAAA,IAAC,QAAK,EAAA,WAAU,8CAA8C,UAAA,KAAK,QAAO;AAAA,cACzEA,kCAAA,IAAA,QAAA,EAAK,WAAU,iBAAiB,eAAK,MAAM,CAAA;AAAA,YAAA,EAFtC,GAAA,KAGV,CACH,EACL,CAAA;AAAA,UAAA;AAAA,QAAA;AAAA,MACJ;AAAA,MAEAD,kCAAA;AAAA,QAAC;AAAA,QAAA;AAAA,UAAQ,IAAG;AAAA,UACH,WAAU;AAAA,UACf,UAAA;AAAA,YAACC,kCAAA,IAAA,MAAA,EAAG,WAAU,2CAA0C,UAAmB,uBAAA;AAAA,YAC3ED,kCAAAA,KAAC,KAAE,EAAA,WAAU,oDAAmD,UAAA;AAAA,cAAA;AAAA,cAClB;AAAA,cAAU;AAAA,YAAA,GACxD;AAAA,YACAA,kCAAAA,KAAC,OAAI,EAAA,WAAU,6BACX,UAAA;AAAA,cAAAC,kCAAA;AAAA,gBAAC;AAAA,gBAAA;AAAA,kBACG,MAAM,UAAU,YAAY;AAAA,kBAC5B,WAAU;AAAA,kBACb,UAAA;AAAA,gBAAA;AAAA,cAED;AAAA,cACAA,kCAAA;AAAA,gBAAC;AAAA,gBAAA;AAAA,kBACG,MAAK;AAAA,kBACL,WAAU;AAAA,kBACb,UAAA;AAAA,gBAAA;AAAA,cAAA;AAAA,YAED,EACJ,CAAA;AAAA,UAAA;AAAA,QAAA;AAAA,MAAA;AAAA,IACJ,GACJ;AAAA,IAEAD,kCAAAA,KAAC,UAAO,EAAA,WAAU,kCACd,UAAA;AAAA,MAAAA,uCAAC,KAAE,EAAA,UAAA;AAAA,QAAA;AAAA,QAAa;AAAA,QAAU;AAAA,MAAA,GAAkC;AAAA,MAC5DC,kCAAA;AAAA,QAAC;AAAA,QAAA;AAAA,UACG,MAAM,WAAW,UAAU;AAAA,UAC3B,QAAO;AAAA,UACP,KAAI;AAAA,UACJ,WAAU;AAAA,UAET,UAAA;AAAA,QAAA;AAAA,MAAA;AAAA,IACL,EACJ,CAAA;AAAA,EAAA,GACJ;AAER;ACtMaC,MAAAA,OAAqBA,MAAM;AAC/B,SAAA,CACL;AAAA,IAAEC,OAAO;AAAA,EAAgD,GACzD;AAAA,IAAEC,MAAM;AAAA,IAAeC,SAAS;AAAA,EAAmE,CAAA;AAEvG;AAiCA,SAAwBC,QAAQ;AAC9B,QAAMC,YAAYC,cAA0B;AACrC,SAAAP,kCAAAA,IAACQ,aAAa;AAAA,IAAA,GAAGF;AAAAA,EAAW,CAAA;AACrC;"}