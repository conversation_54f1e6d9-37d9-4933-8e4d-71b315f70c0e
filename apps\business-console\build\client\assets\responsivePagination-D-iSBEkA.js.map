{"version": 3, "file": "responsivePagination-D-iSBEkA.js", "sources": ["../../../app/components/ui/responsivePagination.tsx"], "sourcesContent": ["import React from \"react\";\r\n\r\ninterface PaginationProps {\r\n      totalPages: number;\r\n      currentPage: number;\r\n      onPageChange: (page: number) => void;\r\n      maxVisiblePages?: number;\r\n}\r\n\r\nconst ResponsivePagination: React.FC<PaginationProps> = ({\r\n      totalPages,\r\n      currentPage,\r\n      onPageChange,\r\n      maxVisiblePages = 5,\r\n}) => {\r\n      const getPageNumbers = () => {\r\n            const pages: Array<number | \"ellipsis\"> = [];\r\n            const halfVisible = Math.floor(maxVisiblePages / 2);\r\n\r\n            let startPage = Math.max(0, currentPage - halfVisible);\r\n            let endPage = Math.min(totalPages - 1, startPage + maxVisiblePages - 1);\r\n\r\n            if (endPage - startPage + 1 < maxVisiblePages) {\r\n                  startPage = Math.max(0, endPage - maxVisiblePages + 1);\r\n            }\r\n\r\n            if (startPage > 0) {\r\n                  pages.push(0);\r\n                  if (startPage > 1) pages.push(\"ellipsis\");\r\n            }\r\n\r\n            for (let i = startPage; i <= endPage; i++) {\r\n                  pages.push(i);\r\n            }\r\n\r\n            if (endPage < totalPages - 1) {\r\n                  if (endPage < totalPages - 2) pages.push(\"ellipsis\");\r\n                  pages.push(totalPages - 1);\r\n            }\r\n\r\n            return pages;\r\n      };\r\n\r\n      return (\r\n            <div className=\"flex items-center justify-end space-x-2 py-4 pr-5\">\r\n                  <button\r\n                        className=\"px-3 py-1 border rounded disabled:opacity-50\"\r\n                        onClick={() => onPageChange(currentPage - 1)}\r\n                        disabled={currentPage === 0}\r\n                  >\r\n                        Prev\r\n                  </button>\r\n\r\n                  {getPageNumbers().map((page, index) =>\r\n                        page === \"ellipsis\" ? (\r\n                              <span key={`ellipsis-${index}`} className=\"px-3\">...</span>\r\n                        ) : (\r\n                              <button\r\n                                    key={page}\r\n                                    className={`px-3 py-1 border rounded ${currentPage === page ? \"bg-blue-500 text-white\" : \"\"\r\n                                          }`}\r\n                                    onClick={() => onPageChange(page)}\r\n                              >\r\n                                    {page + 1}\r\n                              </button>\r\n                        )\r\n                  )}\r\n\r\n                  <button\r\n                        className=\"px-3 py-1 border rounded disabled:opacity-50\"\r\n                        onClick={() => onPageChange(currentPage + 1)}\r\n                        disabled={currentPage === totalPages - 1}\r\n                  >\r\n                        Next\r\n                  </button>\r\n            </div>\r\n      );\r\n};\r\n\r\nexport default ResponsivePagination;\r\n"], "names": ["jsxs", "jsx"], "mappings": ";AASA,MAAM,uBAAkD,CAAC;AAAA,EACnD;AAAA,EACA;AAAA,EACA;AAAA,EACA,kBAAkB;AACxB,MAAM;AACA,QAAM,iBAAiB,MAAM;AACvB,UAAM,QAAoC,CAAC;AAC3C,UAAM,cAAc,KAAK,MAAM,kBAAkB,CAAC;AAElD,QAAI,YAAY,KAAK,IAAI,GAAG,cAAc,WAAW;AACrD,QAAI,UAAU,KAAK,IAAI,aAAa,GAAG,YAAY,kBAAkB,CAAC;AAElE,QAAA,UAAU,YAAY,IAAI,iBAAiB;AACzC,kBAAY,KAAK,IAAI,GAAG,UAAU,kBAAkB,CAAC;AAAA,IAAA;AAG3D,QAAI,YAAY,GAAG;AACb,YAAM,KAAK,CAAC;AACZ,UAAI,YAAY,EAAS,OAAA,KAAK,UAAU;AAAA,IAAA;AAG9C,aAAS,IAAI,WAAW,KAAK,SAAS,KAAK;AACrC,YAAM,KAAK,CAAC;AAAA,IAAA;AAGd,QAAA,UAAU,aAAa,GAAG;AACxB,UAAI,UAAU,aAAa,EAAG,OAAM,KAAK,UAAU;AAC7C,YAAA,KAAK,aAAa,CAAC;AAAA,IAAA;AAGxB,WAAA;AAAA,EACb;AAGM,SAAAA,kCAAA,KAAC,OAAI,EAAA,WAAU,qDACT,UAAA;AAAA,IAAAC,kCAAA;AAAA,MAAC;AAAA,MAAA;AAAA,QACK,WAAU;AAAA,QACV,SAAS,MAAM,aAAa,cAAc,CAAC;AAAA,QAC3C,UAAU,gBAAgB;AAAA,QAC/B,UAAA;AAAA,MAAA;AAAA,IAED;AAAA,IAEC,eAAiB,EAAA;AAAA,MAAI,CAAC,MAAM,UACvB,SAAS,aACHA,kCAAAA,IAAC,QAA+B,EAAA,WAAU,QAAO,UAAA,SAAtC,YAAY,KAAK,EAAwB,IAEpDA,kCAAA;AAAA,QAAC;AAAA,QAAA;AAAA,UAEK,WAAW,4BAA4B,gBAAgB,OAAO,2BAA2B,EACnF;AAAA,UACN,SAAS,MAAM,aAAa,IAAI;AAAA,UAE/B,UAAO,OAAA;AAAA,QAAA;AAAA,QALH;AAAA,MAAA;AAAA,IAQvB;AAAA,IAEAA,kCAAA;AAAA,MAAC;AAAA,MAAA;AAAA,QACK,WAAU;AAAA,QACV,SAAS,MAAM,aAAa,cAAc,CAAC;AAAA,QAC3C,UAAU,gBAAgB,aAAa;AAAA,QAC5C,UAAA;AAAA,MAAA;AAAA,IAAA;AAAA,EAED,GACN;AAEZ;"}