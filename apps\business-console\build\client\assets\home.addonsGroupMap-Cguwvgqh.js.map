{"version": 3, "file": "home.addonsGroupMap-Cguwvgqh.js", "sources": ["../../../app/components/common/AddselectedGroupAddons.tsx", "../../../app/routes/home.addonsGroupMap.tsx"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\nimport { Dialog, DialogContent, DialogTitle } from \"../ui/dialog\";\r\nimport { MyAddonData, MyAddOnGroupAddOn, MyAddonGroupData } from \"~/types/api/businessConsoleService/SellerManagement\";\r\nimport { Form, useFetcher } from \"@remix-run/react\";\r\nimport { SquareX } from \"lucide-react\";\r\nimport { useToast } from \"../ui/ToastProvider\";\r\n\r\n\r\n\r\ninterface AddselectedGroupAddonsProps {\r\n      isOpen: boolean;\r\n      items: MyAddonData[];\r\n      onClose: () => void;\r\n      header: string;\r\n      groupData?: MyAddOnGroupAddOn,\r\n      sellerId?: number,\r\n      groupId?: number\r\n}\r\n\r\nconst AddselectedGroupAddons: React.FC<AddselectedGroupAddonsProps> = ({\r\n      isOpen,\r\n      items,\r\n      onClose,\r\n      header,\r\n      groupData,\r\n      sellerId,\r\n      groupId\r\n}) => {\r\n      const [selectedId, setSelectedId] = useState<string | number | null>(null);\r\n      const [searchTerm, setSearchTerm] = useState('');\r\n      const [filteredAddon, setFilteredAddon] = useState<MyAddonData[]>(items)\r\n      const [choosenAddon, setChoosenAddon] = useState<boolean>(false);\r\n      const [choossenAddonName, setChoosenAddonName] = useState<string>('');\r\n      const [formData, setFormData] = useState({\r\n            price: groupData?.price.toString() || \"0\",\r\n            seq: groupData?.seq.toString() || \"0\",\r\n            active: groupData?.active ?? true,\r\n      });\r\n      const { showToast } = useToast()\r\n      useEffect(() => {\r\n            if (searchTerm.length >= 3 && searchTerm !== \"\") {\r\n                  setFilteredAddon(items?.filter(addon => addon?.name.toLowerCase().includes(searchTerm.toLowerCase())))\r\n            }\r\n            else {\r\n                  setFilteredAddon(items)\r\n            } ``\r\n      }, [searchTerm, items]);\r\n\r\n      useEffect(() => {\r\n            if (!isOpen) {\r\n                  setSelectedId(null);\r\n                  setSearchTerm(\"\");\r\n                  setChoosenAddon(false);\r\n                  setFormData({\r\n                        price: \"0\",\r\n                        seq: \"0\",\r\n                        active: false,\r\n                  })\r\n            }\r\n\r\n      }, [isOpen]);\r\n      useEffect(() => {\r\n            if (groupData) {\r\n                  setChoosenAddon(true);\r\n                  setSelectedId(groupData.myAddOnId)\r\n                  setChoosenAddonName(groupData.myAddOnName)\r\n                  setFormData(prev => ({\r\n                        ...prev,\r\n                        price: groupData.price.toString(),\r\n                        seq: groupData.seq.toString(),\r\n                        active: groupData.active ?? true,\r\n                  }))\r\n            }\r\n\r\n      }, [groupData]);\r\n\r\n      const handleSelect = (addon: MyAddonData) => {\r\n            setSelectedId(addon.id);\r\n            setChoosenAddon(true)\r\n            setChoosenAddonName(addon.name)\r\n\r\n      }\r\n      const deselectAddon = () => {\r\n            setSelectedId(null)\r\n            setChoosenAddon(false)\r\n      }\r\n      const groupMapfetcher = useFetcher();\r\n      useEffect(() => {\r\n            if (groupMapfetcher.data) {\r\n                  if (groupMapfetcher.data?.sucess) {\r\n                        showToast(\"sucess to Map GroupData\", 'success')\r\n                        onClose()\r\n                        setFormData({\r\n                              price: \"0\",\r\n                              seq: \"0\",\r\n                              active: false,\r\n                        })\r\n\r\n                  }\r\n                  else if (groupMapfetcher.data?.sucess === false) {\r\n                        showToast(\"failed to  Map  GroupData\", 'success')\r\n                  }\r\n\r\n            }\r\n\r\n      }, [groupMapfetcher?.data])\r\n\r\n\r\n\r\n      if (!isOpen) return null;\r\n      return (\r\n            <Dialog open={isOpen} onOpenChange={onClose}>\r\n                  <DialogContent className=\"w-full max-w-md sm:max-w-lg bg-white rounded-2xl shadow-2xl\">\r\n                        <DialogTitle className=\"text-2xl font-bold text-gray-900 mb-4\">{header}</DialogTitle>\r\n                        <div className=\"space-y-6\">\r\n                              {choosenAddon === false && selectedId === null && <>\r\n                                    <div>\r\n                                          <input\r\n                                                placeholder=\"Search by Addon Name\"\r\n                                                type=\"search\"\r\n                                                className=\"w-full p-3 rounded-full border-2 border-gray-200 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-colors\"\r\n                                                autoFocus\r\n                                                value={searchTerm}\r\n                                                onChange={(e) => setSearchTerm(e.target.value)}\r\n                                          />\r\n                                    </div>\r\n                                    <div className=\"mt-4 max-h-[40vh] overflow-y-auto pr-2 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100\">\r\n                                          <ul className=\"space-y-2\">\r\n                                                {filteredAddon.length === 0 ? (\r\n                                                      <p className=\"p-4 text-gray-500 text-center\">No add-ons found</p>\r\n                                                ) : (\r\n                                                      filteredAddon.map((item) => (\r\n                                                            <li key={item.id} className=\"flex items-center gap-3\">\r\n                                                                  <input\r\n                                                                        type=\"checkbox\"\r\n                                                                        id={`item-${item.id}`}\r\n                                                                        name=\"selectedItem\"\r\n                                                                        value={item.id}\r\n                                                                        checked={selectedId === item.id}\r\n                                                                        onChange={() => handleSelect(item)}\r\n                                                                        className=\"h-5 w-5 text-blue-600 focus:ring-blue-500 rounded\"\r\n                                                                  />\r\n                                                                  <label\r\n                                                                        htmlFor={`item-${item.id}`}\r\n                                                                        className={`cursor-pointer p-3 w-full rounded-lg border ${selectedId === item.id ? 'bg-blue-50 border-blue-200' : 'border-gray-200'} text-gray-800 hover:bg-gray-50 transition-colors`}\r\n                                                                  >\r\n                                                                        {item?.name} <span className=\"text-gray-500\">({item?.diet})</span>\r\n                                                                  </label>\r\n                                                            </li>\r\n                                                      ))\r\n                                                )}\r\n                                          </ul>\r\n                                    </div>\r\n                              </>\r\n                              }\r\n\r\n                              {choosenAddon && selectedId && <div className=\"space-y-4\">\r\n                                    {selectedId && (\r\n                                          <div className=\"flex items-center justify-between bg-blue-50 p-3 rounded-lg\">\r\n                                                <p className=\"font-medium text-gray-800 truncate max-w-[80%]\">{choossenAddonName}</p>\r\n                                                <SquareX\r\n                                                      color=\"red\"\r\n                                                      className=\"cursor-pointer hover:scale-110 transition-transform\"\r\n                                                      onClick={() => deselectAddon()}\r\n                                                />\r\n                                          </div>\r\n                                    )}\r\n\r\n                                    <div className=\"grid grid-cols-1 sm:grid-cols-2 gap-4\">\r\n                                          <div>\r\n                                                <label htmlFor=\"price\" className=\"block text-sm font-medium text-gray-700 mb-1\">\r\n                                                      Price\r\n                                                </label>\r\n                                                <input\r\n                                                      type=\"number\"\r\n                                                      id=\"price\"\r\n                                                      name=\"price\"\r\n                                                      value={formData.price}\r\n                                                      onChange={(e) => setFormData({ ...formData, price: e.target.value })}\r\n                                                      min=\"0\"\r\n                                                      step=\"0.01\"\r\n                                                      required\r\n                                                      className=\"w-full p-2 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none\"\r\n                                                />\r\n                                          </div>\r\n                                          <div>\r\n                                                <label htmlFor=\"seq\" className=\"block text-sm font-medium text-gray-700 mb-1\">\r\n                                                      Sequence\r\n                                                </label>\r\n                                                <input\r\n                                                      type=\"number\"\r\n                                                      id=\"seq\"\r\n                                                      name=\"seq\"\r\n                                                      value={formData.seq}\r\n                                                      onChange={(e) => setFormData({ ...formData, seq: e.target.value })}\r\n                                                      min=\"0\"\r\n                                                      required\r\n                                                      className=\"w-full p-2 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none\"\r\n                                                />\r\n                                          </div>\r\n                                          <div className=\" flex gap-2\">\r\n                                                <label htmlFor=\"active\" className=\"block text-sm font-medium text-gray-700 mb-1\">\r\n                                                      Active :\r\n                                                </label>\r\n                                                <input\r\n                                                      type=\"checkbox\"\r\n                                                      id=\"active\"\r\n                                                      name=\"active\"\r\n                                                      checked={formData.active}\r\n                                                      onChange={(e) => setFormData({ ...formData, active: e.target.checked })}\r\n                                                      className=\"h-5 w-5 text-blue-600 rounded focus:ring-2 focus:ring-blue-500 focus:ring-offset-2\"\r\n                                                />\r\n                                          </div>\r\n                                    </div>\r\n                              </div>}\r\n                        </div>\r\n\r\n                        <Form method=\"POST\" className=\"mt-6 flex flex-col sm:flex-row gap-3 justify-end\">\r\n                              <input type=\"hidden\" name=\"addonId\" value={selectedId?.toString()} />\r\n                              <input type=\"hidden\" name=\"addonGroupId\" value={groupId?.toString()} />\r\n                              <input type=\"hidden\" name=\"price\" value={formData.price?.toString()} />\r\n                              <input type=\"hidden\" name=\"sequence\" value={formData.seq?.toString()} />\r\n                              <input type=\"hidden\" name=\"active\" value={formData.active?.toString()} />\r\n                              <input type=\"hidden\" name=\"sellerId\" value={sellerId?.toString()} />\r\n                              <input type=\"hidden\" name=\"addonName\" value={choossenAddonName?.toString()} />\r\n                              <input type=\"hidden\" name=\"actionType\" value={\"actionAddonforGroup\"} />\r\n                              <button\r\n                                    onClick={onClose}\r\n                                    className=\"w-full sm:w-auto px-4 py-2 rounded-lg bg-gray-100 text-gray-700 hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-300 text-sm font-medium\"\r\n                              >\r\n                                    Cancel\r\n                              </button>\r\n                              <button\r\n                                    type=\"submit\"\r\n                                    disabled={selectedId === null}\r\n                                    className={`w-full sm:w-auto px-4 py-2 rounded-lg text-sm font-medium text-white focus:outline-none focus:ring-2 focus:ring-blue-500 ${selectedId === null ? 'bg-gray-400 cursor-not-allowed' : 'bg-blue-600 hover:bg-blue-700'}`}\r\n                              >\r\n                                    Confirm\r\n                              </button>\r\n                        </Form>\r\n                  </DialogContent>\r\n            </Dialog>\r\n      );\r\n};\r\n\r\nexport default AddselectedGroupAddons;\r\n", "import type { LoaderFunction } from \"@remix-run/node\";\r\nimport { Form, useActionData, useFetcher, useLoaderData, useNavigate } from \"@remix-run/react\";\r\nimport type { MyAddonData, MyAddOnGroupAddOn, MyAddonGroupData } from \"~/types/api/businessConsoleService/SellerManagement\";\r\nimport { createAddonMap, deleteAddonMap, getAddons, getAddonsGroupsMap } from \"~/services/businessConsoleService\";\r\nimport { withAuth, withResponse } from \"~/utils/auth-utils\";\r\nimport { ResponsiveTable } from \"~/components/ui/responsiveTable\";\r\nimport { Button } from \"~/components/ui/button\";\r\nimport { Pencil, Trash } from \"lucide-react\";\r\nimport AddselectedGroupAddons from \"~/components/common/AddselectedGroupAddons\";\r\nimport React, { useEffect, useState } from \"react\";\r\ninterface LoaderData {\r\n  selectedGruoupData: MyAddOnGroupAddOn[],\r\n  groupName: string,\r\n  sellerId: number,\r\n  groupId: number\r\n}\r\ninterface ActionData {\r\n  selectedAddonsData: MyAddonData[],\r\n  sucessMessage: string,\r\n  ErrorMessage: string,\r\n  sucess?: boolean,\r\n  error?: string\r\n\r\n}\r\nexport const loader: LoaderFunction = withAuth(async ({ request }) => {\r\n  const url = new URL(request.url);\r\n  const page = parseInt(url.searchParams.get(\"page\") || \"0\");\r\n  const pageSize = parseInt(url.searchParams.get(\"pageSize\") || \"50\");\r\n  const matchBy = url.searchParams.get(\"matchBy\") || \"\";\r\n  const sellerId = Number(url.searchParams.get(\"sellerId\"));\r\n  const groupId = Number(url.searchParams.get(\"groupId\"));\r\n  const groupName = url.searchParams.get(\"groupName\");\r\n  try {\r\n    const addonsGroupsResponse = await getAddonsGroupsMap(sellerId, groupId, page, pageSize, matchBy, request);\r\n    const selectedGruoupData = addonsGroupsResponse.data;\r\n    return withResponse({ selectedGruoupData, groupName, sellerId, groupId }, addonsGroupsResponse.headers);\r\n  } catch (error) {\r\n    console.error(\"Error loading addons groups:\", error);\r\n    throw new Response('Failed to load addons', { status: 500 });\r\n  }\r\n});\r\n\r\nexport const action = withAuth(async ({ request }) => {\r\n  const formData = await request.formData();\r\n  const sellerId = Number(formData.get(\"sellerId\"))\r\n  const pageSize = Number(formData.get(\"pageSize\") || \"50\");\r\n  const page = Number(formData.get(\"page\") || \"0\");\r\n  const matchBy = formData.get(\"matchBy\") as string\r\n  const actionType = formData.get(\"actionType\") as string\r\n\r\n  if (actionType === \"getAddons\") {\r\n    try {\r\n      const addonsList = await getAddons(sellerId, page, pageSize, matchBy, request);\r\n      const selectedAddonsData = addonsList.data;\r\n      return withResponse({ selectedAddonsData }, addonsList.headers);\r\n    } catch (error) {\r\n      console.error(\"Error loading addons groups:\", error);\r\n      throw new Response('Failed to load addons', { status: 500 });\r\n    }\r\n  }\r\n  else if (\r\n    actionType === \"actionAddonforGroup\"\r\n  ) {\r\n\r\n    const addonGroupId = Number(formData.get(\"addonGroupId\"));\r\n    const price = Number(formData.get(\"price\"));\r\n    const active = (formData.get(\"active\")) as unknown as boolean;\r\n    const sequence = Number(formData.get(\"sequence\"));\r\n    const addonId = Number(formData.get(\"addonId\"));\r\n    const addonName = formData.get(\"addonName\") as string\r\n\r\n    const payload: MyAddOnGroupAddOn = {\r\n      myAddOnGroupId: addonGroupId,\r\n      myAddOnId: addonId,\r\n      myAddOnName: addonName,\r\n      price: price,\r\n      seq: sequence,\r\n      active: active,\r\n      groupName: \"\",\r\n\r\n    }\r\n    try {\r\n      const addonsList = await createAddonMap(sellerId, payload, request);\r\n      return withResponse({ sucess: addonsList.statusCode === 200 }, addonsList.headers);\r\n    } catch (error) {\r\n      console.error(\"Error loading addons groups:\", error);\r\n      throw new Response('Failed to load addons', { status: 500 });\r\n    }\r\n  }\r\n  else if (\r\n    actionType == \"addonsMapdelete\"\r\n  ) {\r\n    const addonmapId = Number(formData.get(\"addonmapId\"));\r\n\r\n    try {\r\n      const addonsList = await deleteAddonMap(sellerId, addonmapId, request);\r\n      const selectedAddonsData = addonsList.data;\r\n      return withResponse({ selectedAddonsData }, addonsList.headers);\r\n    } catch (error) {\r\n      console.error(\"Error loading addons groups:\", error);\r\n      throw new Response('Failed to load addons', { status: 500 });\r\n    }\r\n  }\r\n})\r\nconst AddonsGroupMap: React.FC = () => {\r\n  const { selectedGruoupData, groupName, sellerId, groupId } = useLoaderData<LoaderData>();\r\n  const navigate = useNavigate();\r\n  const addonMapfetcher = useFetcher<ActionData>()\r\n\r\n  const [isAddselectedGroupAddonsOpen, setIsAddselectedGroupAddonsOpen] = useState(false);\r\n  const [selectedGdata, setSelectedGdata] = useState<MyAddOnGroupAddOn>();\r\n  const actionData = useActionData<ActionData>();\r\n  const [isEditopen, setIsEditOpen] = useState(false)\r\n\r\n  const [selectedAddonsData, setSelectedAddonsData] = useState<MyAddonData[]>()\r\n  const selectedGroupHeader = [\r\n    \"Id\",\r\n    \"myAddOnId\",\r\n    \"myAddOnName\",\r\n    \"price\",\r\n    \"seq\",\r\n    \"active\",\r\n    \"\",\r\n    \"\"\r\n  ];\r\n  useEffect(() => {\r\n    if (addonMapfetcher.state === \"idle\") {\r\n\r\n      if (actionData?.selectedAddonsData) {\r\n        setSelectedAddonsData(actionData.selectedAddonsData)\r\n        setIsAddselectedGroupAddonsOpen(true)\r\n        setIsEditOpen(false)\r\n\r\n      }\r\n      else {\r\n        setSelectedAddonsData([])\r\n\r\n        setIsAddselectedGroupAddonsOpen(false)\r\n        setIsEditOpen(false)\r\n\r\n      }\r\n    }\r\n\r\n  }, [actionData])\r\n\r\n  const handleSelectedGroupData = (row: MyAddOnGroupAddOn) => {\r\n    setSelectedGdata(row);\r\n    setIsEditOpen(true);\r\n    setIsAddselectedGroupAddonsOpen(true)\r\n  }\r\n  const handleDelete = (addonsmapData: MyAddOnGroupAddOn) => {\r\n    const formData = new FormData();\r\n    formData.append(\"actionType\", \"addonsMapdelete\");\r\n    formData.append(\"addonmapId\", addonsmapData.id.toString())\r\n    formData.append(\"sellerId\", sellerId.toString())\r\n    addonMapfetcher.submit(formData, { method: 'post' })\r\n  }\r\n  return (\r\n    <div className=\"h-full\">\r\n      <h1 className=\" mb-4 font-bold cursor-pointer\" onClick={() => navigate(-1)}> <span className=\"text-2xl\">MyAddonsGroup / </span> <span className=\"text-xl\">{groupName} </span> </h1>\r\n      <div className=\"flex flex-wrap gap-4 border-2 \">\r\n      </div>\r\n\r\n      <ResponsiveTable\r\n        headers={selectedGroupHeader}\r\n        data={selectedGruoupData}\r\n        renderRow={(row) => (\r\n          <tr key={row.id} className=\"border-b\">\r\n            <td className=\"py-2 px-3 text-center whitespace-normal break-words \">{row.id}</td>\r\n            <td className=\"py-2 px-3 text-center whitespace-normal break-words \">{row?.myAddOnId}</td>\r\n            <td className=\"py-2 px-3 text-center whitespace-normal break-words\">{row?.myAddOnName}</td>\r\n            <td className=\"py-2 px-3 text-center whitespace-normal break-words\">{row?.price}</td>\r\n            <td className=\"py-2 px-3 text-center whitespace-normal break-words\">{row?.seq}</td>\r\n            <td className=\"py-2 px-3 text-center whitespace-normal break-words\">\r\n              {row?.active ? (\r\n                <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800\">\r\n                  <svg\r\n                    className=\"w-4 h-4 mr-1 text-green-500\"\r\n                    fill=\"currentColor\"\r\n                    viewBox=\"0 0 20 20\"\r\n                    xmlns=\"http://www.w3.org/2000/svg\"\r\n                  >\r\n                    <path\r\n                      fillRule=\"evenodd\"\r\n                      d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\"\r\n                      clipRule=\"evenodd\"\r\n                    />\r\n                  </svg>\r\n                  Active\r\n                </span>\r\n              ) : (\r\n                <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800\">\r\n                  <svg\r\n                    className=\"w-4 h-4 mr-1 text-red-500\"\r\n                    fill=\"currentColor\"\r\n                    viewBox=\"0 0 20 20\"\r\n                    xmlns=\"http://www.w3.org/2000/svg\"\r\n                  >\r\n                    <path\r\n                      fillRule=\"evenodd\"\r\n                      d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 001.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\"\r\n                      clipRule=\"evenodd\"\r\n                    />\r\n                  </svg>\r\n                  Inactive\r\n                </span>\r\n              )}\r\n            </td>\r\n            <td className=\"py-2 px-3 text-center cursor-pointer\">\r\n              <Button\r\n                variant=\"ghost\"\r\n                size=\"sm\"\r\n                className=\"text-red-500 hover:text-red-900\"\r\n                onClick={() => {\r\n                  if (confirm(\"Are you sure you want to delete this Addon Group Map?\")) {\r\n                    handleDelete(row)\r\n                  }\r\n                }}\r\n                style={{ alignSelf: \"flex-end\" }}\r\n              >\r\n                <Trash size={20} />\r\n              </Button>\r\n            </td>\r\n            <td className=\"py-2 px-3 text-center cursor-pointer\">\r\n              <Pencil color='blue' size={20} onClick={() => handleSelectedGroupData(row)} />\r\n            </td>\r\n          </tr>\r\n        )}\r\n      />\r\n      <Form method=\"post\" >\r\n        <input name=\"sellerId\" value={sellerId} hidden />\r\n        <input name=\"matchBy\" value={\"\"} hidden />\r\n        <input name=\"actionType\" value={\"getAddons\"} hidden />\r\n\r\n        <Button\r\n          className=\"fixed bottom-5 right-5 rounded-full cursor-pointer\"\r\n          type=\"submit\"\r\n        >\r\n          + Create Addon from Group\r\n        </Button>\r\n      </Form>\r\n      <AddselectedGroupAddons\r\n        isOpen={isAddselectedGroupAddonsOpen}\r\n        items={selectedAddonsData || []}\r\n        onClose={() => setIsAddselectedGroupAddonsOpen(false)}\r\n        header={isEditopen ? `Edit Addon for${groupName?.slice(0, 15)}` : `Create Addon for ${groupName?.slice(0, 15)} `} groupData={selectedGdata}\r\n        sellerId={sellerId}\r\n        groupId={groupId}\r\n      />\r\n    </div>\r\n  );\r\n};\r\nexport default AddonsGroupMap;\r\n"], "names": ["useState", "useEffect", "_a", "_b", "jsx", "jsxs", "Fragment", "AddonsGroupMap", "selectedGruoupData", "groupName", "sellerId", "groupId", "useLoaderData", "navigate", "useNavigate", "addon<PERSON><PERSON><PERSON><PERSON>er", "useFetcher", "isAddselectedGroupAddonsOpen", "setIsAddselectedGroupAddonsOpen", "selectedGdata", "setSelectedGdata", "actionData", "useActionData", "isEditopen", "setIsEditOpen", "selectedAddonsData", "setSelectedAddonsData", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "state", "handleSelectedGroupData", "row", "handleDelete", "addonsmapData", "formData", "FormData", "append", "id", "toString", "submit", "method", "className", "children", "onClick", "ResponsiveTable", "headers", "data", "renderRow", "myAddOnId", "myAddOnName", "price", "seq", "active", "fill", "viewBox", "xmlns", "fillRule", "d", "clipRule", "<PERSON><PERSON>", "variant", "size", "confirm", "style", "alignSelf", "Trash", "Pencil", "color", "Form", "name", "value", "hidden", "type", "AddselectedGroupAddons", "isOpen", "items", "onClose", "header", "slice", "groupData"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAmBA,MAAM,yBAAgE,CAAC;AAAA,EACjE;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACN,MAAM;;AACA,QAAM,CAAC,YAAY,aAAa,IAAIA,aAAAA,SAAiC,IAAI;AACzE,QAAM,CAAC,YAAY,aAAa,IAAIA,aAAAA,SAAS,EAAE;AAC/C,QAAM,CAAC,eAAe,gBAAgB,IAAIA,aAAAA,SAAwB,KAAK;AACvE,QAAM,CAAC,cAAc,eAAe,IAAIA,aAAAA,SAAkB,KAAK;AAC/D,QAAM,CAAC,mBAAmB,mBAAmB,IAAIA,aAAAA,SAAiB,EAAE;AACpE,QAAM,CAAC,UAAU,WAAW,IAAIA,sBAAS;AAAA,IACnC,QAAO,uCAAW,MAAM,eAAc;AAAA,IACtC,MAAK,uCAAW,IAAI,eAAc;AAAA,IAClC,SAAQ,uCAAW,WAAU;AAAA,EAAA,CAClC;AACK,QAAA,EAAE,UAAU,IAAI,SAAS;AAC/BC,eAAAA,UAAU,MAAM;AACV,QAAI,WAAW,UAAU,KAAK,eAAe,IAAI;AAC3C,uBAAiB,+BAAO,OAAO,CAAS,UAAA,+BAAO,KAAK,cAAc,SAAS,WAAW,YAAA,GAAe;AAAA,IAAA,OAEtG;AACC,uBAAiB,KAAK;AAAA,IAAA;AAAA,EAC1B,GACL,CAAC,YAAY,KAAK,CAAC;AAEtBA,eAAAA,UAAU,MAAM;AACV,QAAI,CAAC,QAAQ;AACP,oBAAc,IAAI;AAClB,oBAAc,EAAE;AAChB,sBAAgB,KAAK;AACT,kBAAA;AAAA,QACN,OAAO;AAAA,QACP,KAAK;AAAA,QACL,QAAQ;AAAA,MAAA,CACb;AAAA,IAAA;AAAA,EACP,GAEH,CAAC,MAAM,CAAC;AACXA,eAAAA,UAAU,MAAM;AACV,QAAI,WAAW;AACT,sBAAgB,IAAI;AACpB,oBAAc,UAAU,SAAS;AACjC,0BAAoB,UAAU,WAAW;AACzC,kBAAY,CAAS,UAAA;AAAA,QACf,GAAG;AAAA,QACH,OAAO,UAAU,MAAM,SAAS;AAAA,QAChC,KAAK,UAAU,IAAI,SAAS;AAAA,QAC5B,QAAQ,UAAU,UAAU;AAAA,MAAA,EAChC;AAAA,IAAA;AAAA,EACR,GAEH,CAAC,SAAS,CAAC;AAER,QAAA,eAAe,CAAC,UAAuB;AACvC,kBAAc,MAAM,EAAE;AACtB,oBAAgB,IAAI;AACpB,wBAAoB,MAAM,IAAI;AAAA,EAEpC;AACA,QAAM,gBAAgB,MAAM;AACtB,kBAAc,IAAI;AAClB,oBAAgB,KAAK;AAAA,EAC3B;AACA,QAAM,kBAAkB,WAAW;AACnCA,eAAAA,UAAU,MAAM;;AACV,QAAI,gBAAgB,MAAM;AAChB,WAAAC,MAAA,gBAAgB,SAAhB,gBAAAA,IAAsB,QAAQ;AAC5B,kBAAU,2BAA2B,SAAS;AACtC,gBAAA;AACI,oBAAA;AAAA,UACN,OAAO;AAAA,UACP,KAAK;AAAA,UACL,QAAQ;AAAA,QAAA,CACb;AAAA,MAGE,aAAAC,MAAA,gBAAgB,SAAhB,gBAAAA,IAAsB,YAAW,OAAO;AAC3C,kBAAU,6BAA6B,SAAS;AAAA,MAAA;AAAA,IACtD;AAAA,EAEN,GAEH,CAAC,mDAAiB,IAAI,CAAC;AAItB,MAAA,CAAC,OAAe,QAAA;AAEd,SAAAC,kCAAA,IAAC,UAAO,MAAM,QAAQ,cAAc,SAC9B,UAAAC,kCAAA,KAAC,eAAc,EAAA,WAAU,+DACnB,UAAA;AAAA,IAACD,kCAAA,IAAA,aAAA,EAAY,WAAU,yCAAyC,UAAO,QAAA;AAAA,IACvEC,kCAAAA,KAAC,OAAI,EAAA,WAAU,aACR,UAAA;AAAA,MAAiB,iBAAA,SAAS,eAAe,QACpCA,kCAAAA,KAAAC,kBAAAA,UAAA,EAAA,UAAA;AAAA,QAAAF,sCAAC,OACK,EAAA,UAAAA,kCAAA;AAAA,UAAC;AAAA,UAAA;AAAA,YACK,aAAY;AAAA,YACZ,MAAK;AAAA,YACL,WAAU;AAAA,YACV,WAAS;AAAA,YACT,OAAO;AAAA,YACP,UAAU,CAAC,MAAM,cAAc,EAAE,OAAO,KAAK;AAAA,UAAA;AAAA,QAAA,GAEzD;AAAA,QACAA,kCAAA,IAAC,OAAI,EAAA,WAAU,2GACT,UAAAA,kCAAAA,IAAC,QAAG,WAAU,aACP,UAAc,cAAA,WAAW,IACpBA,kCAAA,IAAC,OAAE,WAAU,iCAAgC,UAAgB,mBAAA,CAAA,IAE7D,cAAc,IAAI,CAAC,SACbC,kCAAA,KAAC,MAAiB,EAAA,WAAU,2BACtB,UAAA;AAAA,UAAAD,kCAAA;AAAA,YAAC;AAAA,YAAA;AAAA,cACK,MAAK;AAAA,cACL,IAAI,QAAQ,KAAK,EAAE;AAAA,cACnB,MAAK;AAAA,cACL,OAAO,KAAK;AAAA,cACZ,SAAS,eAAe,KAAK;AAAA,cAC7B,UAAU,MAAM,aAAa,IAAI;AAAA,cACjC,WAAU;AAAA,YAAA;AAAA,UAChB;AAAA,UACAC,kCAAA;AAAA,YAAC;AAAA,YAAA;AAAA,cACK,SAAS,QAAQ,KAAK,EAAE;AAAA,cACxB,WAAW,+CAA+C,eAAe,KAAK,KAAK,+BAA+B,iBAAiB;AAAA,cAElI,UAAA;AAAA,gBAAM,6BAAA;AAAA,gBAAK;AAAA,gBAACA,kCAAAA,KAAC,QAAK,EAAA,WAAU,iBAAgB,UAAA;AAAA,kBAAA;AAAA,kBAAE,6BAAM;AAAA,kBAAK;AAAA,gBAAA,EAAC,CAAA;AAAA,cAAA;AAAA,YAAA;AAAA,UAAA;AAAA,QACjE,EAAA,GAfG,KAAK,EAgBd,CACL,EAEb,CAAA,EACN,CAAA;AAAA,MAAA,GACN;AAAA,MAGC,gBAAgB,cAAeA,uCAAA,OAAA,EAAI,WAAU,aACvC,UAAA;AAAA,QACK,cAAAA,kCAAA,KAAC,OAAI,EAAA,WAAU,+DACT,UAAA;AAAA,UAACD,kCAAA,IAAA,KAAA,EAAE,WAAU,kDAAkD,UAAkB,mBAAA;AAAA,UACjFA,kCAAA;AAAA,YAAC;AAAA,YAAA;AAAA,cACK,OAAM;AAAA,cACN,WAAU;AAAA,cACV,SAAS,MAAM,cAAc;AAAA,YAAA;AAAA,UAAA;AAAA,QACnC,GACN;AAAA,QAGNC,kCAAAA,KAAC,OAAI,EAAA,WAAU,yCACT,UAAA;AAAA,UAAAA,uCAAC,OACK,EAAA,UAAA;AAAA,YAAAD,sCAAC,SAAM,EAAA,SAAQ,SAAQ,WAAU,gDAA+C,UAEhF,SAAA;AAAA,YACAA,kCAAA;AAAA,cAAC;AAAA,cAAA;AAAA,gBACK,MAAK;AAAA,gBACL,IAAG;AAAA,gBACH,MAAK;AAAA,gBACL,OAAO,SAAS;AAAA,gBAChB,UAAU,CAAC,MAAM,YAAY,EAAE,GAAG,UAAU,OAAO,EAAE,OAAO,OAAO;AAAA,gBACnE,KAAI;AAAA,gBACJ,MAAK;AAAA,gBACL,UAAQ;AAAA,gBACR,WAAU;AAAA,cAAA;AAAA,YAAA;AAAA,UAChB,GACN;AAAA,iDACC,OACK,EAAA,UAAA;AAAA,YAAAA,sCAAC,SAAM,EAAA,SAAQ,OAAM,WAAU,gDAA+C,UAE9E,YAAA;AAAA,YACAA,kCAAA;AAAA,cAAC;AAAA,cAAA;AAAA,gBACK,MAAK;AAAA,gBACL,IAAG;AAAA,gBACH,MAAK;AAAA,gBACL,OAAO,SAAS;AAAA,gBAChB,UAAU,CAAC,MAAM,YAAY,EAAE,GAAG,UAAU,KAAK,EAAE,OAAO,OAAO;AAAA,gBACjE,KAAI;AAAA,gBACJ,UAAQ;AAAA,gBACR,WAAU;AAAA,cAAA;AAAA,YAAA;AAAA,UAChB,GACN;AAAA,UACAC,kCAAAA,KAAC,OAAI,EAAA,WAAU,eACT,UAAA;AAAA,YAAAD,sCAAC,SAAM,EAAA,SAAQ,UAAS,WAAU,gDAA+C,UAEjF,YAAA;AAAA,YACAA,kCAAA;AAAA,cAAC;AAAA,cAAA;AAAA,gBACK,MAAK;AAAA,gBACL,IAAG;AAAA,gBACH,MAAK;AAAA,gBACL,SAAS,SAAS;AAAA,gBAClB,UAAU,CAAC,MAAM,YAAY,EAAE,GAAG,UAAU,QAAQ,EAAE,OAAO,SAAS;AAAA,gBACtE,WAAU;AAAA,cAAA;AAAA,YAAA;AAAA,UAChB,EACN,CAAA;AAAA,QAAA,EACN,CAAA;AAAA,MAAA,EACN,CAAA;AAAA,IAAA,GACN;AAAA,IAECC,kCAAA,KAAA,MAAA,EAAK,QAAO,QAAO,WAAU,oDACxB,UAAA;AAAA,MAACD,kCAAAA,IAAA,SAAA,EAAM,MAAK,UAAS,MAAK,WAAU,OAAO,yCAAY,YAAY;AAAA,MACnEA,kCAAAA,IAAC,WAAM,MAAK,UAAS,MAAK,gBAAe,OAAO,mCAAS,YAAY;AAAA,MACrEA,kCAAAA,IAAC,SAAM,EAAA,MAAK,UAAS,MAAK,SAAQ,QAAO,cAAS,UAAT,mBAAgB,WAAY,CAAA;AAAA,MACrEA,kCAAAA,IAAC,SAAM,EAAA,MAAK,UAAS,MAAK,YAAW,QAAO,cAAS,QAAT,mBAAc,WAAY,CAAA;AAAA,MACtEA,kCAAAA,IAAC,SAAM,EAAA,MAAK,UAAS,MAAK,UAAS,QAAO,cAAS,WAAT,mBAAiB,WAAY,CAAA;AAAA,MACvEA,kCAAAA,IAAC,WAAM,MAAK,UAAS,MAAK,YAAW,OAAO,qCAAU,YAAY;AAAA,MAClEA,kCAAAA,IAAC,WAAM,MAAK,UAAS,MAAK,aAAY,OAAO,uDAAmB,YAAY;AAAA,4CAC3E,SAAM,EAAA,MAAK,UAAS,MAAK,cAAa,OAAO,uBAAuB;AAAA,MACrEA,kCAAA;AAAA,QAAC;AAAA,QAAA;AAAA,UACK,SAAS;AAAA,UACT,WAAU;AAAA,UACf,UAAA;AAAA,QAAA;AAAA,MAED;AAAA,MACAA,kCAAA;AAAA,QAAC;AAAA,QAAA;AAAA,UACK,MAAK;AAAA,UACL,UAAU,eAAe;AAAA,UACzB,WAAW,4HAA4H,eAAe,OAAO,mCAAmC,+BAA+B;AAAA,UACpO,UAAA;AAAA,QAAA;AAAA,MAAA;AAAA,IAED,EACN,CAAA;AAAA,EAAA,EAAA,CACN,EACN,CAAA;AAEZ;AC3IMG,MAAAA,iBAA2BA,MAAM;AACrC,QAAM;AAAA,IAAEC;AAAAA,IAAoBC;AAAAA,IAAWC;AAAAA,IAAUC;AAAAA,MAAYC,cAA0B;AACvF,QAAMC,WAAWC,YAAY;AAC7B,QAAMC,kBAAkBC,WAAuB;AAE/C,QAAM,CAACC,8BAA8BC,+BAA+B,IAAIlB,aAAAA,SAAS,KAAK;AACtF,QAAM,CAACmB,eAAeC,gBAAgB,IAAIpB,sBAA4B;AACtE,QAAMqB,aAAaC,cAA0B;AAC7C,QAAM,CAACC,YAAYC,aAAa,IAAIxB,aAAAA,SAAS,KAAK;AAElD,QAAM,CAACyB,oBAAoBC,qBAAqB,IAAI1B,sBAAwB;AAC5E,QAAM2B,sBAAsB,CAC1B,MACA,aACA,eACA,SACA,OACA,UACA,IACA,EAAA;AAEF1B,eAAAA,UAAU,MAAM;AACV,QAAAc,gBAAgBa,UAAU,QAAQ;AAEpC,UAAIP,yCAAYI,oBAAoB;AAClCC,8BAAsBL,WAAWI,kBAAkB;AACnDP,wCAAgC,IAAI;AACpCM,sBAAc,KAAK;AAAA,MAErB,OACK;AACHE,8BAAsB,CAAA,CAAE;AAExBR,wCAAgC,KAAK;AACrCM,sBAAc,KAAK;AAAA,MAErB;AAAA,IACF;AAAA,EAEF,GAAG,CAACH,UAAU,CAAC;AAET,QAAAQ,0BAA2BC,SAA2B;AAC1DV,qBAAiBU,GAAG;AACpBN,kBAAc,IAAI;AAClBN,oCAAgC,IAAI;AAAA,EACtC;AACM,QAAAa,eAAgBC,mBAAqC;AACnD,UAAAC,WAAW,IAAIC,SAAS;AACrBD,aAAAE,OAAO,cAAc,iBAAiB;AAC/CF,aAASE,OAAO,cAAcH,cAAcI,GAAGC,UAAU;AACzDJ,aAASE,OAAO,YAAYzB,SAAS2B,SAAA,CAAU;AAC/CtB,oBAAgBuB,OAAOL,UAAU;AAAA,MAAEM,QAAQ;AAAA,IAAO,CAAC;AAAA,EACrD;AAEE,SAAAlC,kCAAAA,KAAC,OAAI;AAAA,IAAAmC,WAAU;AAAA,IACbC,UAAA,CAAApC,kCAAA,KAAC;MAAGmC,WAAU;AAAA,MAAiCE,SAASA,MAAM7B,SAAS,EAAE;AAAA,MAAG4B,UAAA,CAAA,KAAErC,kCAAA,IAAA,QAAA;AAAA,QAAKoC,WAAU;AAAA,QAAWC,UAAgB;AAAA,OAAA,GAAO,KAACpC,kCAAA,KAAC,QAAK;AAAA,QAAAmC,WAAU;AAAA,QAAWC,UAAA,CAAAhC,WAAU,GAAA;AAAA,MAAC,CAAA,GAAO,GAAA;AAAA,IAAC,CAAA,GAC9KL,kCAAA,IAAC,OAAI;AAAA,MAAAoC,WAAU;AAAA,IACf,CAAA,GAEApC,kCAAA,IAACuC,iBAAA;AAAA,MACCC,SAASjB;AAAAA,MACTkB,MAAMrC;AAAAA,MACNsC,WAAYhB,SACTzB,kCAAA,KAAA,MAAA;AAAA,QAAgBmC,WAAU;AAAA,QACzBC,UAAA,CAAArC,kCAAA,IAAC,MAAG;AAAA,UAAAoC,WAAU;AAAA,UAAwDC,UAAAX,IAAIM;AAAAA,QAAG,CAAA,GAC5EhC,kCAAA,IAAA,MAAA;AAAA,UAAGoC,WAAU;AAAA,UAAwDC,qCAAKM;AAAAA,QAAU,CAAA,GACpF3C,kCAAA,IAAA,MAAA;AAAA,UAAGoC,WAAU;AAAA,UAAuDC,qCAAKO;AAAAA,QAAY,CAAA,GACrF5C,kCAAA,IAAA,MAAA;AAAA,UAAGoC,WAAU;AAAA,UAAuDC,qCAAKQ;AAAAA,QAAM,CAAA,GAC/E7C,kCAAA,IAAA,MAAA;AAAA,UAAGoC,WAAU;AAAA,UAAuDC,qCAAKS;AAAAA,QAAI,CAAA,GAC9E9C,kCAAA,IAAC;UAAGoC,WAAU;AAAA,UACXC,sCAAKU,UACJ9C,kCAAAA,KAAC,QAAK;AAAA,YAAAmC,WAAU;AAAA,YACdC,UAAA,CAAArC,kCAAA,IAAC,OAAA;AAAA,cACCoC,WAAU;AAAA,cACVY,MAAK;AAAA,cACLC,SAAQ;AAAA,cACRC,OAAM;AAAA,cAENb,UAAArC,kCAAA,IAAC,QAAA;AAAA,gBACCmD,UAAS;AAAA,gBACTC,GAAE;AAAA,gBACFC,UAAS;AAAA,cACX,CAAA;AAAA,YACF,CAAA,GAAM,QAAA;AAAA,UAAA,CAER,IAEApD,kCAAA,KAAC,QAAK;AAAA,YAAAmC,WAAU;AAAA,YACdC,UAAA,CAAArC,kCAAA,IAAC,OAAA;AAAA,cACCoC,WAAU;AAAA,cACVY,MAAK;AAAA,cACLC,SAAQ;AAAA,cACRC,OAAM;AAAA,cAENb,UAAArC,kCAAA,IAAC,QAAA;AAAA,gBACCmD,UAAS;AAAA,gBACTC,GAAE;AAAA,gBACFC,UAAS;AAAA,cACX,CAAA;AAAA,YACF,CAAA,GAAM,UAAA;AAAA,UAER,CAAA;AAAA,QAEJ,CAAA,GACArD,kCAAA,IAAC,MAAG;AAAA,UAAAoC,WAAU;AAAA,UACZC,UAAArC,kCAAA,IAACsD,QAAA;AAAA,YACCC,SAAQ;AAAA,YACRC,MAAK;AAAA,YACLpB,WAAU;AAAA,YACVE,SAASA,MAAM;AACT,kBAAAmB,QAAQ,uDAAuD,GAAG;AACpE9B,6BAAaD,GAAG;AAAA,cAClB;AAAA,YACF;AAAA,YACAgC,OAAO;AAAA,cAAEC,WAAW;AAAA,YAAW;AAAA,YAE/BtB,UAAArC,kCAAA,IAAC4D,OAAM;AAAA,cAAAJ,MAAM;AAAA,YAAI,CAAA;AAAA,UACnB,CAAA;AAAA,QACF,CAAA,GACCxD,kCAAA,IAAA,MAAA;AAAA,UAAGoC,WAAU;AAAA,UACZC,gDAACwB,QAAO;AAAA,YAAAC,OAAM;AAAA,YAAON,MAAM;AAAA,YAAIlB,SAASA,MAAMb,wBAAwBC,GAAG;AAAA,UAAG,CAAA;AAAA,QAC9E,CAAA,CAAA;AAAA,MAAA,GA1DOA,IAAIM,EA2Db;AAAA,IAAA,CAEJ,GACA/B,kCAAA,KAAC8D,MAAK;AAAA,MAAA5B,QAAO;AAAA,MACXE,UAAA,CAAArC,kCAAA,IAAC;QAAMgE,MAAK;AAAA,QAAWC,OAAO3D;AAAAA,QAAU4D,QAAM;AAAA,MAAC,CAAA,yCAC9C,SAAM;AAAA,QAAAF,MAAK;AAAA,QAAUC,OAAO;AAAA,QAAIC,QAAM;AAAA,MAAC,CAAA,yCACvC,SAAM;AAAA,QAAAF,MAAK;AAAA,QAAaC,OAAO;AAAA,QAAaC,QAAM;AAAA,MAAC,CAAA,GAEpDlE,kCAAA,IAACsD,QAAA;AAAA,QACClB,WAAU;AAAA,QACV+B,MAAK;AAAA,QACN9B,UAAA;AAAA,MAAA,CAED,CAAA;AAAA,IACF,CAAA,GACArC,kCAAA,IAACoE,wBAAA;AAAA,MACCC,QAAQxD;AAAAA,MACRyD,OAAOjD,sBAAsB,CAAC;AAAA,MAC9BkD,SAASA,MAAMzD,gCAAgC,KAAK;AAAA,MACpD0D,QAAQrD,aAAa,iBAAiBd,uCAAWoE,MAAM,GAAG,GAAG,KAAK,oBAAoBpE,uCAAWoE,MAAM,GAAG,GAAG;AAAA,MAAKC,WAAW3D;AAAAA,MAC7HT;AAAAA,MACAC;AAAAA,IAAA,CACF,CAAA;AAAA,EACF,CAAA;AAEJ;"}