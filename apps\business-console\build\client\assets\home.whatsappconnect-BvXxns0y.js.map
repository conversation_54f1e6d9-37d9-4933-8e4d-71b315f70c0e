{"version": 3, "file": "home.whatsappconnect-BvXxns0y.js", "sources": ["../../../app/components/whatsapp/PhoneNumberSelector.tsx", "../../../app/components/whatsapp/WhatsAppConnectView.tsx", "../../../app/routes/home.whatsappconnect.tsx"], "sourcesContent": ["import { Form } from '@remix-run/react';\r\nimport { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle, CardContent } from '@components/ui/card';\r\nimport { RadioGroup, RadioGroupItem } from '@components/ui/radio-group';\r\nimport { PhoneNumber } from \"~/types/whatsapp\";\r\n\r\ninterface PhoneNumberSelectorProps {\r\n    numbers: PhoneNumber[];\r\n    selectedId?: string;\r\n    onSubmit: (phoneId: string, phoneNumber: string) => void;\r\n}\r\n\r\nexport function PhoneNumberSelector({ numbers, selectedId, onSubmit }: PhoneNumberSelectorProps) {\r\n    const handleSubmit = (event: React.FormEvent<HTMLFormElement>) => {\r\n        event.preventDefault();\r\n        const formData = new FormData(event.currentTarget);\r\n        const phoneId = formData.get('phoneId')?.toString();\r\n\r\n        if (phoneId) {\r\n            const selectedPhone = numbers.find(n => n.id === phoneId);\r\n            if (selectedPhone) {\r\n                onSubmit(phoneId, selectedPhone.display_phone_number);\r\n            }\r\n        }\r\n    };\r\n\r\n    return (\r\n        <Card className=\"w-full max-w-3xl\">\r\n            <CardHeader>\r\n                <CardTitle>Select WhatsApp Phone Number</CardTitle>\r\n            </CardHeader>\r\n            <CardContent>\r\n                <form onSubmit={handleSubmit}>\r\n                    <RadioGroup name=\"phoneId\" defaultValue={selectedId}>\r\n                        {numbers.map(number => (\r\n                            <div key={number.id} className=\"flex items-center space-x-2 p-4 hover:bg-gray-50\">\r\n                                <RadioGroupItem value={number.id} id={number.id} />\r\n                                <label htmlFor={number.id} className=\"flex-1\">\r\n                                    <div className=\"font-medium\">{number.display_phone_number}</div>\r\n                                    <div className=\"text-sm text-gray-500\">\r\n                                        <span className={`inline-block px-2 py-1 rounded ${number.code_verification_status === 'VERIFIED'\r\n                                                ? 'bg-green-100 text-green-800'\r\n                                                : 'bg-yellow-100 text-yellow-800'\r\n                                            }`}>\r\n                                            {number.code_verification_status}\r\n                                        </span>\r\n                                        <span className=\"ml-2\">Quality: {number.quality_rating}</span>\r\n                                    </div>\r\n                                    <div className=\"text-sm text-gray-500\">\r\n                                        Verified Name: {number.verified_name}\r\n                                    </div>\r\n                                </label>\r\n                            </div>\r\n                        ))}\r\n                    </RadioGroup>\r\n                    <button\r\n                        type=\"submit\"\r\n                        className=\"mt-4 w-full bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700\"\r\n                    >\r\n                        Select Number\r\n                    </button>\r\n                </form>\r\n            </CardContent>\r\n        </Card>\r\n    );\r\n}\r\n", "import {useState, useEffect} from 'react';\r\nimport {Link, useFetcher} from \"@remix-run/react\";\r\nimport {Card, CardHeader, CardTitle, CardDescription, CardContent} from '@components/ui/card';\r\nimport type {WhatsAppConnectionData, WhatsAppBusinessAccount, PhoneNumber} from \"~/types/whatsapp\";\r\nimport {PhoneNumberSelector} from \"@components/whatsapp/PhoneNumberSelector\";\r\n\r\ninterface WhatsAppConnectViewProps {\r\n    FACEBOOK_APP_ID: string;\r\n    connectionState: WhatsAppConnectionData | null;\r\n}\r\n\r\nexport function WhatsAppConnectView({FACEBOOK_APP_ID, connectionState}: WhatsAppConnectViewProps) {\r\n    const fetcher = useFetcher();\r\n    const [businessAccounts, setBusinessAccounts] = useState<WhatsAppBusinessAccount[]>([]);\r\n    const [phoneNumbers, setPhoneNumbers] = useState<PhoneNumber[]>([]);\r\n    const [selectedBusinessId, setSelectedBusinessId] = useState<string | null>(null);\r\n    const [hasFetchedBusinesses, setHasFetchedBusinesses] = useState(false);\r\n    const [isSubscribed, setIsSubscribed] = useState(false);\r\n    const [hasCheckedSubscription, setHasCheckedSubscription] = useState(false);\r\n    const [currentView, setCurrentView] = useState<'login' | 'businesses' | 'phones' | 'registered'>('login');\r\n    const CONFIG_ID = '***************';\r\n    const REQUIRED_APP_ID = '***************';\r\n\r\n    // Set initial view based on connection state\r\n    useEffect(() => {\r\n        if (!connectionState?.access) {\r\n            setCurrentView('login');\r\n        } else if (connectionState.mNetConnectedPhoneNumberId) {\r\n            setCurrentView('registered');\r\n        } else if (!hasFetchedBusinesses) {\r\n            setCurrentView('businesses');\r\n        }\r\n    }, [connectionState, hasFetchedBusinesses]);\r\n\r\n    useEffect(() => {\r\n        if (\r\n            currentView === 'registered' &&\r\n            connectionState?.wabaId &&\r\n            !hasCheckedSubscription &&\r\n            fetcher.state === 'idle'\r\n        ) {\r\n            const formData = new FormData();\r\n            formData.append('actionType', 'check-subscription');\r\n            formData.append('businessId', connectionState.wabaId);\r\n            fetcher.submit(formData, {method: 'post'});\r\n            setHasCheckedSubscription(true);\r\n        }\r\n    }, [currentView, connectionState?.wabaId, hasCheckedSubscription, fetcher.state]);\r\n\r\n\r\n    useEffect(() => {\r\n        if (currentView !== 'registered') {\r\n            setHasCheckedSubscription(false);\r\n        }\r\n    }, [currentView]);\r\n    // Fetch business accounts\r\n    useEffect(() => {\r\n        if (\r\n            connectionState?.access &&\r\n            !hasFetchedBusinesses &&\r\n            fetcher.state === 'idle' &&\r\n            currentView === 'businesses'\r\n        ) {\r\n            setHasFetchedBusinesses(true);\r\n            const formData = new FormData();\r\n            formData.append('actionType', 'fetch-businesses');\r\n            fetcher.submit(formData, {method: 'post'});\r\n        }\r\n    }, [connectionState?.access, hasFetchedBusinesses, fetcher.state, currentView]);\r\n\r\n    // Handle fetcher responses\r\n    useEffect(() => {\r\n        if (fetcher.data?.success) {\r\n            if ('businessAccounts' in fetcher.data) {\r\n                setBusinessAccounts(fetcher.data.businessAccounts);\r\n            } else if ('phoneNumbers' in fetcher.data) {\r\n                setPhoneNumbers(fetcher.data.phoneNumbers);\r\n            } else if ('subscribedApps' in fetcher.data) {\r\n                setIsSubscribed(fetcher.data.subscribedApps.some(app =>\r\n                    app.whatsapp_business_api_data?.id === REQUIRED_APP_ID\r\n                ));\r\n            } else if ('actionType' in fetcher.data) {\r\n                switch (fetcher.data.actionType) {\r\n                    case 'connect-phone':\r\n                        setCurrentView('registered');\r\n                        break;\r\n                    case 'delink':\r\n                        setCurrentView('login');\r\n                        break;\r\n                    case 'subscribe':\r\n                    case 'unsubscribe':\r\n                        // Refresh subscription status\r\n                        const formData = new FormData();\r\n                        formData.append('actionType', 'check-subscription');\r\n                        formData.append('businessId', connectionState!.wabaId!);\r\n                        fetcher.submit(formData, {method: 'post'});\r\n                        break;\r\n                }\r\n            }\r\n        }\r\n    }, [fetcher.data]);\r\n\r\n    const handleSubscriptionToggle = () => {\r\n        if (!connectionState?.wabaId) {\r\n            console.error('No WABA ID found');\r\n            return;\r\n        }\r\n\r\n        const formData = new FormData();\r\n        formData.append('actionType', isSubscribed ? 'unsubscribe' : 'subscribe');\r\n        formData.append('businessId', connectionState.wabaId);\r\n        setHasCheckedSubscription(false);\r\n        fetcher.submit(formData, {method: 'post'});\r\n    };\r\n\r\n    // Handle business selection\r\n    const handleBusinessSelect = async (businessId: string) => {\r\n        setSelectedBusinessId(businessId);\r\n        setCurrentView('phones');\r\n        const formData = new FormData();\r\n        formData.append('actionType', 'fetch-phone-numbers');\r\n        formData.append('businessId', businessId);\r\n        fetcher.submit(formData, {method: 'post'});\r\n    };\r\n\r\n    // Facebook login setup\r\n    useEffect(() => {\r\n        window.fbAsyncInit = function () {\r\n            window.FB.init({\r\n                appId: FACEBOOK_APP_ID,\r\n                cookie: true,\r\n                xfbml: true,\r\n                version: 'v21.0'\r\n            });\r\n        };\r\n\r\n        const script = document.createElement('script');\r\n        script.src = \"https://connect.facebook.net/en_US/sdk.js\";\r\n        script.async = true;\r\n        script.defer = true;\r\n        document.body.appendChild(script);\r\n\r\n        return () => {\r\n            document.body.removeChild(script);\r\n        };\r\n    }, [FACEBOOK_APP_ID]);\r\n\r\n    const handleFacebookLogin = () => {\r\n        window.FB.login((response) => {\r\n            if (response.status === 'connected' && response.authResponse?.code) {\r\n                const formData = new FormData();\r\n                formData.append('code', response.authResponse.code);\r\n                formData.append('actionType', 'exchange-token');\r\n                fetcher.submit(formData, {method: 'post'});\r\n            }\r\n        }, {\r\n            config_id: CONFIG_ID,\r\n            response_type: 'code',\r\n            override_default_response_type: true\r\n        });\r\n    };\r\n\r\n    return (\r\n        <div className=\"space-y-6\">\r\n            {fetcher.state !== 'idle' && (\r\n                <Card className=\"w-full max-w-3xl mx-auto\">\r\n                    <CardHeader>\r\n                        <CardTitle>Loading...</CardTitle>\r\n                    </CardHeader>\r\n                    <CardContent>\r\n                        <div className=\"flex items-center justify-center p-8\">\r\n                            <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"></div>\r\n                        </div>\r\n                    </CardContent>\r\n                </Card>\r\n            )}\r\n\r\n            {fetcher.state === 'idle' && currentView === 'login' && (\r\n                <Card className=\"w-full max-w-3xl mx-auto\">\r\n                    <CardHeader>\r\n                        <CardTitle>Connect WhatsApp Business</CardTitle>\r\n                        <CardDescription>Connect your WhatsApp Business account to continue</CardDescription>\r\n                    </CardHeader>\r\n                    <CardContent>\r\n                        <button\r\n                            onClick={handleFacebookLogin}\r\n                            className=\"w-full bg-blue-600 text-white px-6 py-3 rounded-md hover:bg-blue-700\"\r\n                        >\r\n                            Continue with Facebook\r\n                        </button>\r\n                    </CardContent>\r\n                </Card>\r\n            )}\r\n\r\n            {fetcher.state === 'idle' && currentView === 'businesses' && (\r\n                <Card className=\"w-full max-w-3xl mx-auto\">\r\n                    <CardHeader>\r\n                        <CardTitle>Select WhatsApp Business Account</CardTitle>\r\n                    </CardHeader>\r\n                    <CardContent>\r\n                        <div>\r\n                            {businessAccounts.map(account => (\r\n                                <div\r\n                                    key={account.id}\r\n                                    className=\"mb-4 p-4 border rounded cursor-pointer hover:bg-gray-50\"\r\n                                    onClick={() => handleBusinessSelect(account.id)}\r\n                                >\r\n                                    <div className=\"font-medium\">{account.name}</div>\r\n                                </div>\r\n                            ))}\r\n                        </div>\r\n                    </CardContent>\r\n                </Card>\r\n            )}\r\n\r\n            {fetcher.state === 'idle' && currentView === 'phones' && (\r\n                <div>\r\n                    {phoneNumbers.length > 0 && (\r\n                        <PhoneNumberSelector\r\n                            numbers={phoneNumbers}\r\n                            selectedId={connectionState?.mNetConnectedPhoneNumberId}\r\n                            onSubmit={(phoneId, phoneNumber) => {\r\n                                const formData = new FormData();\r\n                                formData.append('actionType', 'connect-phone');\r\n                                formData.append('phoneId', phoneId);\r\n                                formData.append('phoneNumber', phoneNumber);\r\n                                formData.append('wabaId', selectedBusinessId!);\r\n                                fetcher.submit(formData, { method: 'post' });\r\n                            }}\r\n                        />\r\n                    )}\r\n                </div>\r\n            )}\r\n\r\n            {fetcher.state === 'idle' && currentView === 'registered' && (\r\n                <Card className=\"w-full max-w-3xl mx-auto\">\r\n                    <CardHeader>\r\n                        <CardTitle>WhatsApp Business Registered</CardTitle>\r\n                        <CardDescription>Your WhatsApp Business account is ready to use</CardDescription>\r\n                    </CardHeader>\r\n                    <CardContent>\r\n                        <div className=\"space-y-4\">\r\n                            <div>\r\n                                <h3 className=\"font-medium\">Connected Phone Number</h3>\r\n                                <p>{connectionState?.mNetConnectedPhoneNumber}</p>\r\n                            </div>\r\n                            <div>\r\n                                <h3 className=\"font-medium\">Subscription Status</h3>\r\n                                <p className={isSubscribed ? \"text-green-600\" : \"text-yellow-600\"}>\r\n                                    {isSubscribed ? \"Subscribed\" : \"Not Subscribed\"}\r\n                                </p>\r\n                            </div>\r\n                            <div className=\"space-y-2\">\r\n                                <button\r\n                                    onClick={handleSubscriptionToggle}\r\n                                    className={`w-full px-4 py-2 rounded-md font-medium ${\r\n                                        isSubscribed\r\n                                            ? \"bg-red-500 hover:bg-red-600 text-black\"\r\n                                            : \"bg-emerald-500 hover:bg-emerald-600 text-black\"\r\n                                    }`}\r\n                                >\r\n                                    {isSubscribed ? \"Unsubscribe\" : \"Subscribe\"}\r\n                                </button>\r\n\r\n                                <Link\r\n                                    to=\"/home/<USER>\"\r\n                                    className=\"w-full bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 inline-block text-center\"\r\n                                >\r\n                                    Manage Templates\r\n                                </Link>\r\n\r\n                                <fetcher.Form method=\"post\">\r\n                                    <input type=\"hidden\" name=\"actionType\" value=\"delink\"/>\r\n                                    <button\r\n                                        type=\"submit\"\r\n                                        className=\"w-full bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700\"\r\n                                        onClick={(e) => {\r\n                                            if (!confirm('Are you sure you want to delink your WhatsApp Business account?')) {\r\n                                                e.preventDefault();\r\n                                            }\r\n                                        }}\r\n                                    >\r\n                                        Delink Account\r\n                                    </button>\r\n                                </fetcher.Form>\r\n                            </div>\r\n                        </div>\r\n                    </CardContent>\r\n                </Card>\r\n            )}\r\n        </div>\r\n    );\r\n\r\n\r\n}\r\n", "// File: app/routes/home.whatsappconnect.tsx\r\nimport {json, type LoaderFunction, type ActionFunction} from '@remix-run/node';\r\nimport {Outlet, useLoaderData, useLocation} from '@remix-run/react';\r\nimport {withAuth} from \"~/utils/auth-utils\";\r\nimport {getFirebaseAdmin} from \"~/services/firebase.server\";\r\nimport {WhatsAppConnectView} from '@components/whatsapp/WhatsAppConnectView';\r\nimport {\r\n    getSubscribedApps,\r\n    getWhatsAppBusinessAccounts,\r\n    getWhatsAppPhoneNumbers,\r\n    subscribeApp, unsubscribeApp\r\n} from '~/services/whatsappApi.server';\r\nimport type {WhatsAppConnectionData} from \"~/types/whatsapp\";\r\nimport {saveWABToken} from \"@services/businessConsoleService\";\r\n\r\ntype LoaderData = {\r\n    FACEBOOK_APP_ID: string;\r\n    connectionState: WhatsAppConnectionData | null;\r\n};\r\n\r\nexport const loader: LoaderFunction = withAuth(async ({user}) => {\r\n    console.log('WhatsAppConnect loader called', { \r\n        userId: user?.userId,\r\n        businessName: user?.businessName,\r\n        sellerId: user?.userDetails?.sellerId\r\n    });\r\n    \r\n    if (!user) {\r\n        console.log('WhatsAppConnect loader: No user found, returning default state', {\r\n            FACEBOOK_APP_ID: process.env.FACEBOOK_APP_ID\r\n        });\r\n        return json({FACEBOOK_APP_ID: process.env.FACEBOOK_APP_ID, connectionState: null});\r\n    }\r\n\r\n    const db = getFirebaseAdmin();\r\n    console.log('WhatsAppConnect loader: Fetching connection data from path', {\r\n        collection: 'facebook-connects',\r\n        docId: user.userDetails.sellerId.toString()\r\n    });\r\n    \r\n    const doc = await db.collection('facebook-connects')\r\n        .doc(user.userDetails.sellerId.toString()).get();\r\n\r\n    const connectionState = doc.exists ? doc.data() as WhatsAppConnectionData : null;\r\n    console.log('WhatsAppConnect loader: Connection state retrieved', { \r\n        sellerId: user.userDetails.sellerId, \r\n        connected: !!connectionState,\r\n        lastUpdated: connectionState?.updatedAt || 'N/A',\r\n        hasWabaId: !!connectionState?.wabaId,\r\n        hasPhoneNumber: !!connectionState?.mNetConnectedPhoneNumber\r\n    });\r\n\r\n    return json({\r\n        FACEBOOK_APP_ID: process.env.FACEBOOK_APP_ID,\r\n        connectionState\r\n    });\r\n});\r\n\r\nexport const action: ActionFunction = withAuth(async ({user, request}) => {\r\n    const formData = await request.formData();\r\n    const actionType = formData.get('actionType');\r\n    console.log('WhatsAppConnect action called', { \r\n        actionType, \r\n        userId: user.userId,\r\n        sellerId: user.userDetails.sellerId,\r\n        formData: Object.fromEntries(formData.entries()) \r\n    });\r\n    \r\n    const db = getFirebaseAdmin();\r\n    const docRef = db.collection('facebook-connects').doc(user.userDetails.sellerId.toString());\r\n\r\n    switch (actionType) {\r\n        case 'exchange-token': {\r\n            const code = formData.get('code')?.toString();\r\n            if (!code) return json({error: 'Code required'}, {status: 400});\r\n            console.log('WhatsAppConnect action: Exchanging token', { \r\n                sellerId: user.userDetails.sellerId,\r\n                code: code.substring(0, 10) + '...' // Only log part of the code for security\r\n            });\r\n\r\n            const tokenUrl = new URL('https://graph.facebook.com/v21.0/oauth/access_token');\r\n            tokenUrl.searchParams.set('client_id', process.env.FACEBOOK_APP_ID!);\r\n            tokenUrl.searchParams.set('client_secret', process.env.FACEBOOK_APP_SECRET!);\r\n            tokenUrl.searchParams.set('code', code);\r\n            console.log('WhatsAppConnect action: Token URL', { \r\n                url: tokenUrl.toString().replace(code, '[REDACTED]') \r\n            });\r\n\r\n            const response = await fetch(tokenUrl.toString());\r\n            const tokenData = await response.json();\r\n            console.log('WhatsAppConnect action: Token response', { \r\n                status: response.status,\r\n                ok: response.ok,\r\n                tokenData: {\r\n                    ...tokenData,\r\n                    access_token: tokenData.access_token ? `${tokenData.access_token.substring(0, 10)}...` : null\r\n                }\r\n            });\r\n\r\n            if (!response.ok) {\r\n                console.log('WhatsAppConnect action: Token exchange failed', { \r\n                    status: response.status, \r\n                    error: tokenData.error?.message,\r\n                    errorCode: tokenData.error?.code,\r\n                    errorType: tokenData.error?.type\r\n                });\r\n                return json({error: tokenData.error?.message || 'Token exchange failed'}, {status: 500});\r\n            }\r\n\r\n            const connectionData: WhatsAppConnectionData = {\r\n                access: tokenData,\r\n                user: {\r\n                    userId: user.userId,\r\n                    userName: user.userName,\r\n                },\r\n                businessName: user.businessName,\r\n                sellerId: user.userDetails.sellerId,\r\n                updatedAt: new Date().toISOString()\r\n            };\r\n\r\n            console.log('WhatsAppConnect action: Saving connection data', {\r\n                userId: connectionData.user.userId,\r\n                businessName: connectionData.businessName,\r\n                sellerId: connectionData.sellerId,\r\n                tokenExpires: connectionData.access.expires_in,\r\n                updatedAt: connectionData.updatedAt\r\n            });\r\n            \r\n            await docRef.set(connectionData);\r\n            console.log('WhatsAppConnect action: Token exchange successful and data saved');\r\n\r\n            return json({success: true});\r\n        }\r\n\r\n        case 'fetch-businesses': {\r\n            console.log('WhatsAppConnect action: Fetching business accounts', { \r\n                sellerId: user.userDetails.sellerId\r\n            });\r\n            \r\n            const doc = await docRef.get();\r\n            if (!doc.exists) {\r\n                console.log('WhatsAppConnect action: Connection data not found', {\r\n                    docPath: `facebook-connects/${user.userDetails.sellerId}`\r\n                });\r\n                return json({error: 'Connection not found'}, {status: 404});\r\n            }\r\n\r\n            const connectionData = doc.data() as WhatsAppConnectionData;\r\n            console.log('WhatsAppConnect action: Using access token', {\r\n                token: `${connectionData.access.access_token.substring(0, 10)}...`,\r\n                expiresIn: connectionData.access.expires_in,\r\n                tokenType: connectionData.access.token_type\r\n            });\r\n            \r\n            const businessResponse = await getWhatsAppBusinessAccounts(connectionData.access.access_token);\r\n            console.log('WhatsAppConnect action: Business accounts fetched', { \r\n                count: businessResponse.data?.length,\r\n                businesses: businessResponse.data?.map(b => ({\r\n                    id: b.id,\r\n                    name: b.name\r\n                }))\r\n            });\r\n\r\n            return json({\r\n                success: true,\r\n                businessAccounts: businessResponse.data\r\n            });\r\n        }\r\n\r\n        case 'fetch-phone-numbers': {\r\n            const businessId = formData.get('businessId')?.toString();\r\n            if (!businessId) return json({error: 'Business ID required'}, {status: 400});\r\n            console.log('WhatsAppConnect action: Fetching phone numbers', { \r\n                businessId,\r\n                sellerId: user.userDetails.sellerId \r\n            });\r\n\r\n            const doc = await docRef.get();\r\n            if (!doc.exists) {\r\n                console.log('WhatsAppConnect action: Connection data not found', {\r\n                    docPath: `facebook-connects/${user.userDetails.sellerId}`\r\n                });\r\n                return json({error: 'Connection not found'}, {status: 404});\r\n            }\r\n\r\n            const connectionData = doc.data() as WhatsAppConnectionData;\r\n            const phoneNumbers = await getWhatsAppPhoneNumbers(\r\n                connectionData.access.access_token,\r\n                businessId\r\n            );\r\n            console.log('WhatsAppConnect action: Phone numbers fetched', { \r\n                count: phoneNumbers.data?.length,\r\n                phoneNumbers: phoneNumbers.data?.map(p => ({\r\n                    id: p.id,\r\n                    displayPhoneNumber: p.display_phone_number,\r\n                    verifiedName: p.verified_name,\r\n                    qualityRating: p.quality_rating\r\n                })) \r\n            });\r\n\r\n            return json({\r\n                success: true,\r\n                phoneNumbers: phoneNumbers.data,\r\n            });\r\n        }\r\n\r\n        case 'connect-phone': {\r\n            const phoneId = formData.get('phoneId')?.toString();\r\n            const selectedPhoneNo = formData.get('phoneNumber')?.toString();\r\n            const phoneNumber = selectedPhoneNo?.replace(/^\\+91\\s*|\\s+/g, '');\r\n            const wabaId = formData.get('wabaId')?.toString();\r\n            if (!phoneId || !phoneNumber) return json({error: 'Phone details required'}, {status: 400});\r\n            console.log('WhatsAppConnect action: Connecting phone', { \r\n                phoneId, \r\n                phoneNumber, \r\n                wabaId,\r\n                selectedPhoneNo,\r\n                userMobile: user.userDetails.mobileNumber\r\n            });\r\n\r\n            const doc = await docRef.get();\r\n            if (!doc.exists) {\r\n                console.log('WhatsAppConnect action: Connection data not found', {\r\n                    docPath: `facebook-connects/${user.userDetails.sellerId}`\r\n                });\r\n                return json({error: 'Connection not found'}, {status: 404});\r\n            }\r\n\r\n            const connectionData = doc.data() as WhatsAppConnectionData;\r\n            console.log('WhatsAppConnect action: Using access token for phone connection', {\r\n                token: `${connectionData.access.access_token.substring(0, 10)}...`,\r\n                expiryInfo: {\r\n                    expiresIn: connectionData.access.expires_in,\r\n                    expiryTime: Date.now() + (connectionData.access.expires_in * 1000),\r\n                    expiryDate: new Date(Date.now() + (connectionData.access.expires_in * 1000)).toISOString()\r\n                }\r\n            });\r\n\r\n            // Register phone with WhatsApp API\r\n            const requestUrl = `https://graph.facebook.com/v21.0/${phoneId}/register`;\r\n            console.log('WhatsAppConnect action: Making registration request', {\r\n                url: requestUrl,\r\n                method: 'POST',\r\n                payload: {\r\n                    messaging_product: \"whatsapp\",\r\n                    pin: \"000000\"\r\n                }\r\n            });\r\n            \r\n            const response = await fetch(\r\n                requestUrl,\r\n                {\r\n                    method: 'POST',\r\n                    headers: {\r\n                        'Authorization': `Bearer ${connectionData.access.access_token}`,\r\n                        'Content-Type': 'application/json',\r\n                    },\r\n                    body: JSON.stringify({\r\n                        messaging_product: \"whatsapp\",\r\n                        pin: \"000000\"\r\n                    }),\r\n                }\r\n            );\r\n\r\n            const data = await response.json();\r\n            console.log('WhatsAppConnect action: Registration response', {\r\n                status: response.status,\r\n                ok: response.ok, \r\n                data\r\n            });\r\n            \r\n            if (!response.ok || !data.success) {\r\n                console.log('WhatsAppConnect action: Phone registration failed', { \r\n                    status: response.status, \r\n                    response: data,\r\n                    error: data.error\r\n                });\r\n                return json({error: 'Phone registration failed'}, {status: 500});\r\n            }\r\n\r\n            console.log(\"wawawawawa 2 wabaid: \", wabaId, \"mNetConsolePhoneNumber \", user.userDetails.mobileNumber)\r\n\r\n            // Only update Firebase if WhatsApp registration was successful\r\n            const updateData = {\r\n                mNetConnectedPhoneNumberId: phoneId,\r\n                mNetConnectedPhoneNumber: phoneNumber,\r\n                mNetConsolePhoneNumber: user.userDetails.mobileNumber,\r\n                wabaId: wabaId,\r\n                updatedAt: new Date().toISOString()\r\n            };\r\n            \r\n            console.log('WhatsAppConnect action: Updating Firebase document', {\r\n                docPath: `facebook-connects/${user.userDetails.sellerId}`,\r\n                updateData\r\n            });\r\n            \r\n            await docRef.update(updateData);\r\n            console.log('WhatsAppConnect action: Firebase updated with phone connection details');\r\n\r\n            const tokenData = {\r\n                token: connectionData.access.access_token,\r\n                wabMobile: phoneNumber,\r\n                wabPhoneNumberId: phoneId,\r\n                expiryTime: Date.now() + (connectionData.access.expires_in * 1000)\r\n            };\r\n            \r\n            console.log('WhatsAppConnect action: Saving WAB token', {\r\n                wabMobile: tokenData.wabMobile,\r\n                wabPhoneNumberId: tokenData.wabPhoneNumberId,\r\n                expiryTimeMs: tokenData.expiryTime,\r\n                expiryDate: new Date(tokenData.expiryTime).toISOString()\r\n            });\r\n            \r\n            await saveWABToken(tokenData, request);\r\n            console.log('WhatsAppConnect action: WAB token saved');\r\n\r\n            return json({success: true, actionType: 'connect-phone'});\r\n        }\r\n        //\r\n\r\n        case 'check-subscription': {\r\n            const businessId = formData.get('businessId')?.toString();\r\n            if (!businessId) return json({error: 'Business ID required'}, {status: 400});\r\n            console.log('WhatsAppConnect action: Checking subscription status', { \r\n                businessId,\r\n                sellerId: user.userDetails.sellerId \r\n            });\r\n\r\n            const doc = await docRef.get();\r\n            if (!doc.exists) {\r\n                console.log('WhatsAppConnect action: Connection data not found', {\r\n                    docPath: `facebook-connects/${user.userDetails.sellerId}`\r\n                });\r\n                return json({error: 'Connection not found'}, {status: 404});\r\n            }\r\n\r\n            const connectionData = doc.data() as WhatsAppConnectionData;\r\n            const response = await getSubscribedApps(\r\n                connectionData.access.access_token,\r\n                businessId\r\n            );\r\n            console.log('WhatsAppConnect action: Subscription check complete', { \r\n                appCount: response.data?.length,\r\n                apps: response.data?.map(app => ({\r\n                    id: app.id,\r\n                    name: app.name,\r\n                }))\r\n            });\r\n\r\n            return json({\r\n                success: true,\r\n                subscribedApps: response.data\r\n            });\r\n        }\r\n\r\n        case 'subscribe': {\r\n            const businessId = formData.get('businessId')?.toString();\r\n            if (!businessId) return json({error: 'Business ID required'}, {status: 400});\r\n            console.log('WhatsAppConnect action: Subscribing app', { \r\n                businessId,\r\n                sellerId: user.userDetails.sellerId\r\n            });\r\n\r\n            const doc = await docRef.get();\r\n            if (!doc.exists) {\r\n                console.log('WhatsAppConnect action: Connection data not found', {\r\n                    docPath: `facebook-connects/${user.userDetails.sellerId}`\r\n                });\r\n                return json({error: 'Connection not found'}, {status: 404});\r\n            }\r\n\r\n            const connectionData = doc.data() as WhatsAppConnectionData;\r\n            const response = await subscribeApp(\r\n                connectionData.access.access_token,\r\n                businessId\r\n            );\r\n            console.log('WhatsAppConnect action: App subscription complete', {\r\n                response,\r\n                businessId\r\n            });\r\n\r\n            return json({\r\n                success: true,\r\n                actionType: 'subscribe'\r\n            });\r\n        }\r\n\r\n        case 'unsubscribe': {\r\n            const businessId = formData.get('businessId')?.toString();\r\n            if (!businessId) return json({error: 'Business ID required'}, {status: 400});\r\n            console.log('WhatsAppConnect action: Unsubscribing app', { \r\n                businessId,\r\n                sellerId: user.userDetails.sellerId \r\n            });\r\n\r\n            const doc = await docRef.get();\r\n            if (!doc.exists) {\r\n                console.log('WhatsAppConnect action: Connection data not found', {\r\n                    docPath: `facebook-connects/${user.userDetails.sellerId}`\r\n                });\r\n                return json({error: 'Connection not found'}, {status: 404});\r\n            }\r\n\r\n            const connectionData = doc.data() as WhatsAppConnectionData;\r\n            const response = await unsubscribeApp(\r\n                connectionData.access.access_token,\r\n                businessId\r\n            );\r\n            console.log('WhatsAppConnect action: App unsubscription complete', {\r\n                response,\r\n                businessId\r\n            });\r\n\r\n            return json({\r\n                success: true,\r\n                actionType: 'unsubscribe'\r\n            });\r\n        }\r\n\r\n\r\n        //\r\n\r\n\r\n        case 'delink': {\r\n            console.log('WhatsAppConnect action: Delinking connection', { \r\n                sellerId: user.userDetails.sellerId,\r\n                docPath: `facebook-connects/${user.userDetails.sellerId}`\r\n            });\r\n            await docRef.delete();\r\n            console.log('WhatsAppConnect action: Connection successfully delinked');\r\n            return json({success: true});\r\n        }\r\n\r\n        default:\r\n            console.log('WhatsAppConnect action: Invalid action type', { \r\n                actionType,\r\n                validActions: [\r\n                    'exchange-token', \r\n                    'fetch-businesses', \r\n                    'fetch-phone-numbers', \r\n                    'connect-phone',\r\n                    'check-subscription',\r\n                    'subscribe',\r\n                    'unsubscribe',\r\n                    'delink'\r\n                ]\r\n            });\r\n            return json({error: 'Invalid action'}, {status: 400});\r\n    }\r\n});\r\n\r\n\r\nexport default function WhatsAppConnect() {\r\n    const {FACEBOOK_APP_ID, connectionState} = useLoaderData<LoaderData>();\r\n    const location = useLocation();\r\n    // const isTemplatesRoute = location.pathname.endsWith('/templates');\r\n\r\n\r\n    if (!FACEBOOK_APP_ID) {\r\n        return <div>Error: Facebook App ID not configured</div>;\r\n    }\r\n\r\n    return (\r\n        <div className=\"space-y-6\">\r\n            <WhatsAppConnectView\r\n                FACEBOOK_APP_ID={FACEBOOK_APP_ID}\r\n                connectionState={connectionState}\r\n            />\r\n\r\n        </div>\r\n    );\r\n}\r\n"], "names": ["jsxs", "jsx", "useState", "useEffect", "_a", "WhatsAppConnect", "FACEBOOK_APP_ID", "connectionState", "useLoaderData", "useLocation", "children", "className", "WhatsAppConnectView"], "mappings": ";;;;;;;;;;;;;;;;;;;AAWO,SAAS,oBAAoB,EAAE,SAAS,YAAY,YAAsC;AACvF,QAAA,eAAe,CAAC,UAA4C;;AAC9D,UAAM,eAAe;AACrB,UAAM,WAAW,IAAI,SAAS,MAAM,aAAa;AACjD,UAAM,WAAU,cAAS,IAAI,SAAS,MAAtB,mBAAyB;AAEzC,QAAI,SAAS;AACT,YAAM,gBAAgB,QAAQ,KAAK,CAAK,MAAA,EAAE,OAAO,OAAO;AACxD,UAAI,eAAe;AACN,iBAAA,SAAS,cAAc,oBAAoB;AAAA,MAAA;AAAA,IACxD;AAAA,EAER;AAGI,SAAAA,kCAAA,KAAC,MAAK,EAAA,WAAU,oBACZ,UAAA;AAAA,IAAAC,sCAAC,YACG,EAAA,UAAAA,kCAAA,IAAC,WAAU,EAAA,UAAA,+BAA4B,CAAA,GAC3C;AAAA,IACCA,sCAAA,aAAA,EACG,UAACD,kCAAAA,KAAA,QAAA,EAAK,UAAU,cACZ,UAAA;AAAA,MAACC,kCAAA,IAAA,YAAA,EAAW,MAAK,WAAU,cAAc,YACpC,UAAQ,QAAA,IAAI,CACT,WAAAD,uCAAC,OAAoB,EAAA,WAAU,oDAC3B,UAAA;AAAA,QAAAC,sCAAC,kBAAe,OAAO,OAAO,IAAI,IAAI,OAAO,IAAI;AAAA,+CAChD,SAAM,EAAA,SAAS,OAAO,IAAI,WAAU,UACjC,UAAA;AAAA,UAAAA,kCAAA,IAAC,OAAI,EAAA,WAAU,eAAe,UAAA,OAAO,sBAAqB;AAAA,UAC1DD,kCAAAA,KAAC,OAAI,EAAA,WAAU,yBACX,UAAA;AAAA,YAACC,kCAAA,IAAA,QAAA,EAAK,WAAW,kCAAkC,OAAO,6BAA6B,aAC7E,gCACA,+BACN,IACC,UAAA,OAAO,yBACZ,CAAA;AAAA,YACAD,kCAAAA,KAAC,QAAK,EAAA,WAAU,QAAO,UAAA;AAAA,cAAA;AAAA,cAAU,OAAO;AAAA,YAAA,EAAe,CAAA;AAAA,UAAA,GAC3D;AAAA,UACAA,kCAAAA,KAAC,OAAI,EAAA,WAAU,yBAAwB,UAAA;AAAA,YAAA;AAAA,YACnB,OAAO;AAAA,UAAA,EAC3B,CAAA;AAAA,QAAA,EACJ,CAAA;AAAA,MAhBM,EAAA,GAAA,OAAO,EAiBjB,CACH,GACL;AAAA,MACAC,kCAAA;AAAA,QAAC;AAAA,QAAA;AAAA,UACG,MAAK;AAAA,UACL,WAAU;AAAA,UACb,UAAA;AAAA,QAAA;AAAA,MAAA;AAAA,IAED,EAAA,CACJ,EACJ,CAAA;AAAA,EAAA,GACJ;AAER;ACrDO,SAAS,oBAAoB,EAAC,iBAAiB,mBAA4C;AAC9F,QAAM,UAAU,WAAW;AAC3B,QAAM,CAAC,kBAAkB,mBAAmB,IAAIC,aAAAA,SAAoC,CAAA,CAAE;AACtF,QAAM,CAAC,cAAc,eAAe,IAAIA,aAAAA,SAAwB,CAAA,CAAE;AAClE,QAAM,CAAC,oBAAoB,qBAAqB,IAAIA,aAAAA,SAAwB,IAAI;AAChF,QAAM,CAAC,sBAAsB,uBAAuB,IAAIA,aAAAA,SAAS,KAAK;AACtE,QAAM,CAAC,cAAc,eAAe,IAAIA,aAAAA,SAAS,KAAK;AACtD,QAAM,CAAC,wBAAwB,yBAAyB,IAAIA,aAAAA,SAAS,KAAK;AAC1E,QAAM,CAAC,aAAa,cAAc,IAAIA,aAAAA,SAA2D,OAAO;AACxG,QAAM,YAAY;AAClB,QAAM,kBAAkB;AAGxBC,eAAAA,UAAU,MAAM;AACR,QAAA,EAAC,mDAAiB,SAAQ;AAC1B,qBAAe,OAAO;AAAA,IAAA,WACf,gBAAgB,4BAA4B;AACnD,qBAAe,YAAY;AAAA,IAAA,WACpB,CAAC,sBAAsB;AAC9B,qBAAe,YAAY;AAAA,IAAA;AAAA,EAC/B,GACD,CAAC,iBAAiB,oBAAoB,CAAC;AAE1CA,eAAAA,UAAU,MAAM;AAER,QAAA,gBAAgB,iBAChB,mDAAiB,WACjB,CAAC,0BACD,QAAQ,UAAU,QACpB;AACQ,YAAA,WAAW,IAAI,SAAS;AACrB,eAAA,OAAO,cAAc,oBAAoB;AACzC,eAAA,OAAO,cAAc,gBAAgB,MAAM;AACpD,cAAQ,OAAO,UAAU,EAAC,QAAQ,QAAO;AACzC,gCAA0B,IAAI;AAAA,IAAA;AAAA,EAClC,GACD,CAAC,aAAa,mDAAiB,QAAQ,wBAAwB,QAAQ,KAAK,CAAC;AAGhFA,eAAAA,UAAU,MAAM;AACZ,QAAI,gBAAgB,cAAc;AAC9B,gCAA0B,KAAK;AAAA,IAAA;AAAA,EACnC,GACD,CAAC,WAAW,CAAC;AAEhBA,eAAAA,UAAU,MAAM;AAER,SAAA,mDAAiB,WACjB,CAAC,wBACD,QAAQ,UAAU,UAClB,gBAAgB,cAClB;AACE,8BAAwB,IAAI;AACtB,YAAA,WAAW,IAAI,SAAS;AACrB,eAAA,OAAO,cAAc,kBAAkB;AAChD,cAAQ,OAAO,UAAU,EAAC,QAAQ,QAAO;AAAA,IAAA;AAAA,EAC7C,GACD,CAAC,mDAAiB,QAAQ,sBAAsB,QAAQ,OAAO,WAAW,CAAC;AAG9EA,eAAAA,UAAU,MAAM;;AACR,SAAA,aAAQ,SAAR,mBAAc,SAAS;AACnB,UAAA,sBAAsB,QAAQ,MAAM;AAChB,4BAAA,QAAQ,KAAK,gBAAgB;AAAA,MAAA,WAC1C,kBAAkB,QAAQ,MAAM;AACvB,wBAAA,QAAQ,KAAK,YAAY;AAAA,MAAA,WAClC,oBAAoB,QAAQ,MAAM;AACzB,wBAAA,QAAQ,KAAK,eAAe;AAAA,UAAK,CAAA,QAC7C;;AAAA,qBAAAC,MAAA,IAAI,+BAAJ,gBAAAA,IAAgC,QAAO;AAAA;AAAA,QAAA,CAC1C;AAAA,MAAA,WACM,gBAAgB,QAAQ,MAAM;AAC7B,gBAAA,QAAQ,KAAK,YAAY;AAAA,UAC7B,KAAK;AACD,2BAAe,YAAY;AAC3B;AAAA,UACJ,KAAK;AACD,2BAAe,OAAO;AACtB;AAAA,UACJ,KAAK;AAAA,UACL,KAAK;AAEK,kBAAA,WAAW,IAAI,SAAS;AACrB,qBAAA,OAAO,cAAc,oBAAoB;AACzC,qBAAA,OAAO,cAAc,gBAAiB,MAAO;AACtD,oBAAQ,OAAO,UAAU,EAAC,QAAQ,QAAO;AACzC;AAAA,QAAA;AAAA,MACR;AAAA,IACJ;AAAA,EACJ,GACD,CAAC,QAAQ,IAAI,CAAC;AAEjB,QAAM,2BAA2B,MAAM;AAC/B,QAAA,EAAC,mDAAiB,SAAQ;AAC1B,cAAQ,MAAM,kBAAkB;AAChC;AAAA,IAAA;AAGE,UAAA,WAAW,IAAI,SAAS;AAC9B,aAAS,OAAO,cAAc,eAAe,gBAAgB,WAAW;AAC/D,aAAA,OAAO,cAAc,gBAAgB,MAAM;AACpD,8BAA0B,KAAK;AAC/B,YAAQ,OAAO,UAAU,EAAC,QAAQ,QAAO;AAAA,EAC7C;AAGM,QAAA,uBAAuB,OAAO,eAAuB;AACvD,0BAAsB,UAAU;AAChC,mBAAe,QAAQ;AACjB,UAAA,WAAW,IAAI,SAAS;AACrB,aAAA,OAAO,cAAc,qBAAqB;AAC1C,aAAA,OAAO,cAAc,UAAU;AACxC,YAAQ,OAAO,UAAU,EAAC,QAAQ,QAAO;AAAA,EAC7C;AAGAD,eAAAA,UAAU,MAAM;AACZ,WAAO,cAAc,WAAY;AAC7B,aAAO,GAAG,KAAK;AAAA,QACX,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,SAAS;AAAA,MAAA,CACZ;AAAA,IACL;AAEM,UAAA,SAAS,SAAS,cAAc,QAAQ;AAC9C,WAAO,MAAM;AACb,WAAO,QAAQ;AACf,WAAO,QAAQ;AACN,aAAA,KAAK,YAAY,MAAM;AAEhC,WAAO,MAAM;AACA,eAAA,KAAK,YAAY,MAAM;AAAA,IACpC;AAAA,EAAA,GACD,CAAC,eAAe,CAAC;AAEpB,QAAM,sBAAsB,MAAM;AACvB,WAAA,GAAG,MAAM,CAAC,aAAa;;AAC1B,UAAI,SAAS,WAAW,iBAAe,cAAS,iBAAT,mBAAuB,OAAM;AAC1D,cAAA,WAAW,IAAI,SAAS;AAC9B,iBAAS,OAAO,QAAQ,SAAS,aAAa,IAAI;AACzC,iBAAA,OAAO,cAAc,gBAAgB;AAC9C,gBAAQ,OAAO,UAAU,EAAC,QAAQ,QAAO;AAAA,MAAA;AAAA,IAC7C,GACD;AAAA,MACC,WAAW;AAAA,MACX,eAAe;AAAA,MACf,gCAAgC;AAAA,IAAA,CACnC;AAAA,EACL;AAGI,SAAAH,kCAAA,KAAC,OAAI,EAAA,WAAU,aACV,UAAA;AAAA,IAAA,QAAQ,UAAU,UACdA,kCAAA,KAAA,MAAA,EAAK,WAAU,4BACZ,UAAA;AAAA,MAAAC,sCAAC,YACG,EAAA,UAAAA,kCAAA,IAAC,WAAU,EAAA,UAAA,aAAU,CAAA,GACzB;AAAA,MACAA,kCAAA,IAAC,aACG,EAAA,UAAAA,kCAAAA,IAAC,OAAI,EAAA,WAAU,wCACX,UAAAA,kCAAAA,IAAC,OAAI,EAAA,WAAU,+DAA+D,CAAA,EAAA,CAClF,EACJ,CAAA;AAAA,IAAA,GACJ;AAAA,IAGH,QAAQ,UAAU,UAAU,gBAAgB,WACxCD,uCAAA,MAAA,EAAK,WAAU,4BACZ,UAAA;AAAA,MAAAA,uCAAC,YACG,EAAA,UAAA;AAAA,QAAAC,kCAAAA,IAAC,aAAU,UAAyB,4BAAA,CAAA;AAAA,QACpCA,kCAAAA,IAAC,mBAAgB,UAAkD,qDAAA,CAAA;AAAA,MAAA,GACvE;AAAA,4CACC,aACG,EAAA,UAAAA,kCAAA;AAAA,QAAC;AAAA,QAAA;AAAA,UACG,SAAS;AAAA,UACT,WAAU;AAAA,UACb,UAAA;AAAA,QAAA;AAAA,MAAA,EAGL,CAAA;AAAA,IAAA,GACJ;AAAA,IAGH,QAAQ,UAAU,UAAU,gBAAgB,gBACxCD,uCAAA,MAAA,EAAK,WAAU,4BACZ,UAAA;AAAA,MAAAC,sCAAC,YACG,EAAA,UAAAA,kCAAA,IAAC,WAAU,EAAA,UAAA,mCAAgC,CAAA,GAC/C;AAAA,4CACC,aACG,EAAA,UAAAA,sCAAC,OACI,EAAA,UAAA,iBAAiB,IAAI,CAClB,YAAAA,kCAAA;AAAA,QAAC;AAAA,QAAA;AAAA,UAEG,WAAU;AAAA,UACV,SAAS,MAAM,qBAAqB,QAAQ,EAAE;AAAA,UAE9C,UAACA,kCAAAA,IAAA,OAAA,EAAI,WAAU,eAAe,kBAAQ,KAAK,CAAA;AAAA,QAAA;AAAA,QAJtC,QAAQ;AAAA,MAMpB,CAAA,EACL,CAAA,EACJ,CAAA;AAAA,IAAA,GACJ;AAAA,IAGH,QAAQ,UAAU,UAAU,gBAAgB,YACxCA,kCAAAA,IAAA,OAAA,EACI,UAAa,aAAA,SAAS,KACnBA,kCAAA;AAAA,MAAC;AAAA,MAAA;AAAA,QACG,SAAS;AAAA,QACT,YAAY,mDAAiB;AAAA,QAC7B,UAAU,CAAC,SAAS,gBAAgB;AAC1B,gBAAA,WAAW,IAAI,SAAS;AACrB,mBAAA,OAAO,cAAc,eAAe;AACpC,mBAAA,OAAO,WAAW,OAAO;AACzB,mBAAA,OAAO,eAAe,WAAW;AACjC,mBAAA,OAAO,UAAU,kBAAmB;AAC7C,kBAAQ,OAAO,UAAU,EAAE,QAAQ,QAAQ;AAAA,QAAA;AAAA,MAC/C;AAAA,IAAA,GAGZ;AAAA,IAGH,QAAQ,UAAU,UAAU,gBAAgB,gBACxCD,uCAAA,MAAA,EAAK,WAAU,4BACZ,UAAA;AAAA,MAAAA,uCAAC,YACG,EAAA,UAAA;AAAA,QAAAC,kCAAAA,IAAC,aAAU,UAA4B,+BAAA,CAAA;AAAA,QACvCA,kCAAAA,IAAC,mBAAgB,UAA8C,iDAAA,CAAA;AAAA,MAAA,GACnE;AAAA,MACCA,sCAAA,aAAA,EACG,UAACD,kCAAAA,KAAA,OAAA,EAAI,WAAU,aACX,UAAA;AAAA,QAAAA,uCAAC,OACG,EAAA,UAAA;AAAA,UAACC,kCAAA,IAAA,MAAA,EAAG,WAAU,eAAc,UAAsB,0BAAA;AAAA,UAClDA,kCAAAA,IAAC,KAAG,EAAA,UAAA,mDAAiB,yBAAyB,CAAA;AAAA,QAAA,GAClD;AAAA,+CACC,OACG,EAAA,UAAA;AAAA,UAACA,kCAAA,IAAA,MAAA,EAAG,WAAU,eAAc,UAAmB,uBAAA;AAAA,UAC/CA,kCAAAA,IAAC,OAAE,WAAW,eAAe,mBAAmB,mBAC3C,UAAA,eAAe,eAAe,iBACnC,CAAA;AAAA,QAAA,GACJ;AAAA,QACAD,kCAAAA,KAAC,OAAI,EAAA,WAAU,aACX,UAAA;AAAA,UAAAC,kCAAA;AAAA,YAAC;AAAA,YAAA;AAAA,cACG,SAAS;AAAA,cACT,WAAW,2CACP,eACM,2CACA,gDACV;AAAA,cAEC,yBAAe,gBAAgB;AAAA,YAAA;AAAA,UACpC;AAAA,UAEAA,kCAAA;AAAA,YAAC;AAAA,YAAA;AAAA,cACG,IAAG;AAAA,cACH,WAAU;AAAA,cACb,UAAA;AAAA,YAAA;AAAA,UAED;AAAA,UAECD,kCAAA,KAAA,QAAQ,MAAR,EAAa,QAAO,QACjB,UAAA;AAAA,YAAAC,sCAAC,WAAM,MAAK,UAAS,MAAK,cAAa,OAAM,UAAQ;AAAA,YACrDA,kCAAA;AAAA,cAAC;AAAA,cAAA;AAAA,gBACG,MAAK;AAAA,gBACL,WAAU;AAAA,gBACV,SAAS,CAAC,MAAM;AACR,sBAAA,CAAC,QAAQ,iEAAiE,GAAG;AAC7E,sBAAE,eAAe;AAAA,kBAAA;AAAA,gBAEzB;AAAA,gBACH,UAAA;AAAA,cAAA;AAAA,YAAA;AAAA,UAED,EACJ,CAAA;AAAA,QAAA,EACJ,CAAA;AAAA,MAAA,EAAA,CACJ,EACJ,CAAA;AAAA,IAAA,EACJ,CAAA;AAAA,EAAA,GAER;AAIR;AC8JA,SAAwBI,kBAAkB;AACtC,QAAM;AAAA,IAACC;AAAAA,IAAiBC;AAAAA,EAAe,IAAIC,cAA0B;AACpDC,cAAY;AAI7B,MAAI,CAACH,iBAAiB;AACX,WAAAL,kCAAAA,IAAC;MAAIS,UAAqC;AAAA,IAAA,CAAA;AAAA,EACrD;AAGI,SAAAT,kCAAAA,IAAC,OAAI;AAAA,IAAAU,WAAU;AAAA,IACXD,UAAAT,kCAAA,IAACW,qBAAA;AAAA,MACGN;AAAAA,MACAC;AAAAA,IACJ,CAAA;AAAA,EAEJ,CAAA;AAER;"}