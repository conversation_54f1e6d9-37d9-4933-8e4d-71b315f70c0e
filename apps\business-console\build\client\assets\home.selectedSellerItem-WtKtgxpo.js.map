{"version": 3, "file": "home.selectedSellerItem-WtKtgxpo.js", "sources": ["../../../app/components/common/SelectedItemAddons.tsx", "../../../app/components/common/selectedItemVariation.tsx", "../../../app/routes/home.selectedSellerItem.tsx"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\nimport { <PERSON><PERSON>, DialogContent, DialogTitle } from \"../ui/dialog\";\r\nimport { MyAddonGroupData } from \"~/types/api/businessConsoleService/SellerManagement\";\r\nimport { Form, useActionData, useSubmit } from \"@remix-run/react\";\r\nimport { Search, SquareX } from \"lucide-react\";\r\nimport { AddOnGroup } from \"~/types/api/businessConsoleService/MyItemList\";\r\n\r\ninterface SelectedItemAddonsProps {\r\n      isOpen: boolean;\r\n      items: MyAddonGroupData[] | [];\r\n      onClose: () => void;\r\n      header: string;\r\n      groupData?: AddOnGroup;\r\n      sellerId?: number;\r\n      groupId?: number;\r\n      isEdit?: boolean;\r\n}\r\n\r\nconst SelectedItemAddons: React.FC<SelectedItemAddonsProps> = ({\r\n      isOpen,\r\n      items,\r\n      onClose,\r\n      header,\r\n      groupData,\r\n      sellerId,\r\n      groupId,\r\n      isEdit\r\n}) => {\r\n      // State for search and selection\r\n      const [selectedId, setSelectedId] = useState<string | number | null>(null);\r\n      const [searchTerm, setSearchTerm] = useState(\"\");\r\n      const [filteredAddon, setFilteredAddon] = useState<MyAddonGroupData[]>(items);\r\n      const [choosenAddon, setChoosenAddon] = useState<boolean>(false);\r\n      const [choosenAddonName, setChoosenAddonName] = useState<string>(\"\");\r\n      const submit = useSubmit();\r\n      const actionData = useActionData<{ success?: boolean }>();\r\n\r\n      // Form data state with proper initialization\r\n      const [formData, setFormData] = useState<AddOnGroup>({\r\n            sId: \"\",\r\n            name: \"\",\r\n            minSelect: 0,\r\n            maxSelect: 0,\r\n            description: \"\",\r\n            seq: 0,\r\n            varient: false,\r\n      });\r\n\r\n      // Initialize form data for edit mode\r\n      useEffect(() => {\r\n            if (groupData) {\r\n                  setFormData({\r\n                        id: groupData.id,\r\n                        sId: groupData.sId,\r\n                        name: groupData.name || \"\",\r\n                        minSelect: typeof groupData.minSelect === \"number\" ? groupData.minSelect : 0,\r\n                        maxSelect: typeof groupData.maxSelect === \"number\" ? groupData.maxSelect : 0,\r\n                        description: groupData.description || \"\",\r\n                        seq: typeof groupData.seq === \"number\" ? groupData.seq : 0,\r\n                        varient: !!groupData.varient,\r\n                  });\r\n\r\n                  // Set edit mode states\r\n                  if (groupData.sId) {\r\n                        setSelectedId(groupData.sId);\r\n                        setChoosenAddon(true);\r\n                        const selectedAddon = items.find((item) => item.id === Number(groupData.sId));\r\n                        if (selectedAddon) {\r\n                              setChoosenAddonName(selectedAddon.displayName);\r\n                        }\r\n                  }\r\n            } else {\r\n                  // Reset for non-edit mode\r\n                  setSelectedId(null);\r\n                  setChoosenAddon(false);\r\n                  setChoosenAddonName(\"\");\r\n                  setFormData({\r\n                        sId: \"\",\r\n                        name: \"\",\r\n                        minSelect: 0,\r\n                        maxSelect: 0,\r\n                        description: \"\",\r\n                        seq: 0,\r\n                        varient: false,\r\n                  });\r\n            }\r\n      }, [groupData, items, isOpen]);\r\n\r\n      // Filter addons based on search term\r\n      useEffect(() => {\r\n            if (searchTerm.length >= 3 && searchTerm !== \"\") {\r\n                  setFilteredAddon(\r\n                        items?.filter((addon) =>\r\n                              addon?.displayName.toLowerCase().includes(searchTerm.toLowerCase())\r\n                        )\r\n                  );\r\n            } else {\r\n                  setFilteredAddon(items);\r\n            }\r\n      }, [searchTerm, items]);\r\n\r\n      // Close modal on successful submission\r\n      useEffect(() => {\r\n            if (actionData?.success) {\r\n                  onClose();\r\n            }\r\n      }, [actionData, onClose]);\r\n\r\n      // Handle addon selection\r\n      const handleSelect = (addon: MyAddonGroupData) => {\r\n            setSelectedId(addon.id);\r\n            setChoosenAddon(true);\r\n            setChoosenAddonName(addon.displayName);\r\n      };\r\n\r\n      // Handle deselecting addon\r\n      const deselectAddonGroupMap = () => {\r\n            setSelectedId(null);\r\n            setChoosenAddon(false);\r\n            setChoosenAddonName(\"\");\r\n      };\r\n\r\n      // Handle cancel button\r\n      const handleCancel = () => {\r\n            if (choosenAddon || selectedId) {\r\n                  // Show addon list again\r\n                  setSelectedId(null);\r\n                  setChoosenAddon(false);\r\n                  setChoosenAddonName(\"\");\r\n                  setSearchTerm(\"\");\r\n                  setFilteredAddon(items);\r\n            } else {\r\n                  // Close modal if no addon is selected\r\n                  onClose();\r\n            }\r\n      };\r\n\r\n      if (!isOpen) return null;\r\n\r\n      return (\r\n            <Dialog open={isOpen} onOpenChange={onClose}>\r\n                  <DialogContent className=\"w-full max-w-md sm:max-w-lg bg-white rounded-2xl shadow-2xl\">\r\n                        <DialogTitle className=\"text-2xl font-bold text-gray-900 mb-4\">\r\n                              {header}\r\n                        </DialogTitle>\r\n\r\n                        <div className=\"space-y-6\">\r\n                              {/* Search and Selection Section */}\r\n                              {!choosenAddon && selectedId === null && (\r\n                                    <>\r\n                                          <div className=\"relative\">\r\n                                                <div className=\"absolute inset-y-0 left-3 flex items-center pointer-events-none\">\r\n                                                      <Search className=\"h-5 w-5 text-gray-400\" />\r\n                                                </div>\r\n                                                <input\r\n                                                      placeholder=\"Search by Addon Name\"\r\n                                                      type=\"search\"\r\n                                                      className=\"w-full pl-10 p-3 rounded-full border-2 border-gray-200 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-colors\"\r\n                                                      autoFocus\r\n                                                      value={searchTerm}\r\n                                                      onChange={(e) => setSearchTerm(e.target.value)}\r\n                                                />\r\n                                          </div>\r\n                                          <div className=\"mt-4 max-h-[40vh] overflow-y-auto pr-2 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100\">\r\n                                                <ul className=\"space-y-2\">\r\n                                                      {filteredAddon.length === 0 ? (\r\n                                                            <p className=\"p-4 text-gray-500 text-center\">\r\n                                                                  No add-ons-group found\r\n                                                            </p>\r\n                                                      ) : (\r\n                                                            filteredAddon?.map((item) => (\r\n                                                                  <li key={item.id} className=\"flex items-center gap-3\">\r\n                                                                        <input\r\n                                                                              type=\"checkbox\"\r\n                                                                              id={`item-${item.id}`}\r\n                                                                              name=\"selectedItem\"\r\n                                                                              value={item.id}\r\n                                                                              checked={selectedId === item.id}\r\n                                                                              onChange={() => handleSelect(item)}\r\n                                                                              className=\"h-5 w-5 text-blue-600 focus:ring-blue-500 rounded\"\r\n                                                                        />\r\n                                                                        <label\r\n                                                                              htmlFor={`item-${item.id}`}\r\n                                                                              className={`cursor-pointer p-3 w-full rounded-lg border ${selectedId === item.id\r\n                                                                                    ? \"bg-blue-50 border-blue-200\"\r\n                                                                                    : \"border-gray-200\"\r\n                                                                                    } text-gray-800 hover:bg-gray-50 transition-colors`}\r\n                                                                        >\r\n                                                                              {item?.displayName}{\" \"}\r\n                                                                              <span className=\"text-gray-500\">\r\n                                                                                    ({item?.internalName})\r\n                                                                              </span>\r\n                                                                        </label>\r\n                                                                  </li>\r\n                                                            ))\r\n                                                      )}\r\n                                                </ul>\r\n                                          </div>\r\n                                    </>\r\n                              )}\r\n\r\n                              {/* Form Section - Shown when an addon is selected */}\r\n                              {(choosenAddon || selectedId) && (\r\n                                    <div className=\"space-y-4\">\r\n                                          {/* Selected Addon Display */}\r\n                                          <div className=\"flex items-center justify-between bg-blue-50 p-3 rounded-lg\">\r\n                                                <p className=\"font-medium text-gray-800 truncate max-w-[80%]\">\r\n                                                      {choosenAddonName}\r\n                                                </p>\r\n                                                <SquareX\r\n                                                      color=\"red\"\r\n                                                      className=\"cursor-pointer hover:scale-110 transition-transform\"\r\n                                                      onClick={deselectAddonGroupMap}\r\n                                                />\r\n                                          </div>\r\n\r\n                                          {/* Form Fields */}\r\n                                          <div className=\"grid grid-cols-1 sm:grid-cols-2 gap-4\">\r\n                                                <div>\r\n                                                      <label\r\n                                                            htmlFor=\"name\"\r\n                                                            className=\"block text-sm font-medium text-gray-700\"\r\n                                                      >\r\n                                                            Name <span className=\"text-red-500\">*</span>\r\n                                                      </label>\r\n                                                      <input\r\n                                                            type=\"text\"\r\n                                                            name=\"name\"\r\n                                                            id=\"name\"\r\n                                                            value={formData.name}\r\n                                                            onChange={(e) =>\r\n                                                                  setFormData({ ...formData, name: e.target.value })\r\n                                                            }\r\n                                                            className=\"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm\"\r\n                                                            placeholder=\"Enter name\"\r\n                                                            required\r\n                                                      />\r\n                                                </div>\r\n                                                <div>\r\n                                                      <label\r\n                                                            htmlFor=\"seq\"\r\n                                                            className=\"block text-sm font-medium text-gray-700 mb-1\"\r\n                                                      >\r\n                                                            Sequence <span className=\"text-red-500\">*</span>\r\n                                                      </label>\r\n                                                      <input\r\n                                                            type=\"number\"\r\n                                                            id=\"seq\"\r\n                                                            name=\"seq\"\r\n                                                            value={formData.seq}\r\n                                                            onChange={(e) =>\r\n                                                                  setFormData({\r\n                                                                        ...formData,\r\n                                                                        seq: e.target.value === \"\" ? 0 : Number(e.target.value),\r\n                                                                  })\r\n                                                            }\r\n                                                            min=\"0\"\r\n                                                            required\r\n                                                            className=\"w-full p-2 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none\"\r\n                                                      />\r\n                                                </div>\r\n                                                <div>\r\n                                                      <label\r\n                                                            htmlFor=\"minSelect\"\r\n                                                            className=\"block text-sm font-medium text-gray-700\"\r\n                                                      >\r\n                                                            Minimum Select\r\n                                                      </label>\r\n                                                      <input\r\n                                                            type=\"number\"\r\n                                                            name=\"minSelect\"\r\n                                                            id=\"minSelect\"\r\n                                                            value={formData.minSelect}\r\n                                                            onChange={(e) =>\r\n                                                                  setFormData({\r\n                                                                        ...formData,\r\n                                                                        minSelect: e.target.value === \"\" ? 0 : Number(e.target.value),\r\n                                                                  })\r\n                                                            }\r\n                                                            className=\"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm\"\r\n                                                            min=\"0\"\r\n                                                      />\r\n                                                </div>\r\n                                                <div>\r\n                                                      <label\r\n                                                            htmlFor=\"maxSelect\"\r\n                                                            className=\"block text-sm font-medium text-gray-700\"\r\n                                                      >\r\n                                                            Maximum Select\r\n                                                      </label>\r\n                                                      <input\r\n                                                            type=\"number\"\r\n                                                            name=\"maxSelect\"\r\n                                                            id=\"maxSelect\"\r\n                                                            value={formData.maxSelect}\r\n                                                            onChange={(e) =>\r\n                                                                  setFormData({\r\n                                                                        ...formData,\r\n                                                                        maxSelect: e.target.value === \"\" ? 0 : Number(e.target.value),\r\n                                                                  })\r\n                                                            }\r\n                                                            className=\"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm\"\r\n                                                            min=\"0\"\r\n                                                      />\r\n                                                </div>\r\n                                                <div className=\"sm:col-span-2\">\r\n                                                      <label\r\n                                                            htmlFor=\"description\"\r\n                                                            className=\"block text-sm font-medium text-gray-700\"\r\n                                                      >\r\n                                                            Description\r\n                                                      </label>\r\n                                                      <textarea\r\n                                                            name=\"description\"\r\n                                                            id=\"description\"\r\n                                                            value={formData.description}\r\n                                                            onChange={(e) =>\r\n                                                                  setFormData({ ...formData, description: e.target.value })\r\n                                                            }\r\n                                                            className=\"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm\"\r\n                                                            placeholder=\"Enter description\"\r\n                                                            rows={3}\r\n                                                      />\r\n                                                </div>\r\n                                                {/* <div className=\"flex items-center gap-2\">\r\n                                                      <input\r\n                                                            type=\"checkbox\"\r\n                                                            id=\"varient\"\r\n                                                            name=\"varient\"\r\n                                                            checked={formData.varient}\r\n                                                            onChange={(e) =>\r\n                                                                  setFormData({ ...formData, varient: e.target.checked ? true : false })\r\n                                                            }\r\n                                                            className=\"h-5 w-5 text-blue-600 rounded focus:ring-2 focus:ring-blue-500 focus:ring-offset-2\"\r\n                                                      />\r\n                                                      <label\r\n                                                            htmlFor=\"varient\"\r\n                                                            className=\"text-sm font-medium text-gray-700\"\r\n                                                      >\r\n                                                            Variant\r\n                                                      </label>\r\n                                                </div> */}\r\n                                          </div>\r\n                                    </div>\r\n                              )}\r\n                        </div>\r\n\r\n                        {/* Form Submission */}\r\n                        <Form method=\"POST\" className=\"mt-6 flex flex-col sm:flex-row gap-3 justify-end\">\r\n                              {/* Hidden fields to pass data */}\r\n                              <input type=\"hidden\" name=\"sId\" value={selectedId?.toString()} />\r\n                              <input type=\"hidden\" name=\"itemId\" value={groupId?.toString()} />\r\n                              <input type=\"hidden\" name=\"minSelect\" value={formData.minSelect?.toString()} />\r\n                              <input type=\"hidden\" name=\"name\" value={formData.name?.toString()} />\r\n                              <input type=\"hidden\" name=\"maxSelect\" value={formData.maxSelect?.toString()} />\r\n                              <input type=\"hidden\" name=\"varient\" value={formData.varient?.toString()} />\r\n                              <input type=\"hidden\" name=\"description\" value={formData.description?.toString()} />\r\n                              <input type=\"hidden\" name=\"seq\" value={formData.seq?.toString()} />\r\n                              <input type=\"hidden\" name=\"sellerId\" value={sellerId?.toString()} />\r\n                              <input type=\"hidden\" name=\"addonName\" value={choosenAddonName?.toString()} />\r\n                              <input type=\"hidden\" name=\"actionType\" value={\"actionItemAddonGroup\"} />\r\n                              <input type=\"hidden\" name=\"mode\" value={isEdit ? \"EditMode\" : \"\"} />\r\n                              {isEdit && <input type=\"hidden\" name=\"selectedId\" value={groupData?.id?.toString()} />\r\n                              }\r\n\r\n                              {/* Action buttons */}\r\n                              <button\r\n                                    type=\"button\"\r\n                                    onClick={handleCancel}\r\n                                    className=\"w-full sm:w-auto px-4 py-2 rounded-lg bg-gray-100 text-gray-700 hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-300 text-sm font-medium transition-colors\"\r\n                              >\r\n                                    Cancel\r\n                              </button>\r\n                              <button\r\n                                    type=\"submit\"\r\n                                    disabled={!selectedId || !formData.name}\r\n                                    className={`w-full sm:w-auto px-4 py-2 rounded-lg text-sm font-medium text-white focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors ${!selectedId || !formData.name\r\n                                          ? \"bg-gray-400 cursor-not-allowed\"\r\n                                          : \"bg-blue-600 hover:bg-blue-700\"\r\n                                          }`}\r\n                              >\r\n                                    Confirm\r\n                              </button>\r\n                        </Form>\r\n                  </DialogContent>\r\n            </Dialog>\r\n      );\r\n};\r\n\r\nexport default SelectedItemAddons;", "import React, { useState, useEffect } from \"react\";\r\nimport { Dialog, DialogContent, DialogTitle } from \"../ui/dialog\";\r\nimport { MyVariationData } from \"~/types/api/businessConsoleService/SellerManagement\";\r\nimport { Form, useActionData, useFetcher } from \"@remix-run/react\";\r\nimport { Loader2, SquareX } from \"lucide-react\";\r\nimport { SelectedItem } from \"~/types/api/businessConsoleService/MyItemList\";\r\ninterface SelectedItemAddonsProps {\r\n      isOpen: boolean;\r\n      items: MyVariationData[] | [];\r\n      onClose: () => void;\r\n      header: string;\r\n      groupData?: SelectedItem,\r\n      sellerId?: number,\r\n      groupId?: number,\r\n      isEdit?: boolean\r\n\r\n}\r\n\r\nconst SelectedItemVariation: React.FC<SelectedItemAddonsProps> = ({\r\n      isOpen,\r\n      items,\r\n      onClose,\r\n      header,\r\n      groupData,\r\n      sellerId,\r\n      groupId,\r\n      isEdit\r\n}) => {\r\n      const [selectedId, setSelectedId] = useState<string | number | null>(null);\r\n      const [searchTerm, setSearchTerm] = useState('');\r\n      const [filteredVariation, setFilteredVariation] = useState<MyVariationData[]>(items)\r\n      const [choosenAddon, setChoosenAddon] = useState<boolean>(false);\r\n      const [choossenAddonName, setChoosenAddonName] = useState<string>('')\r\n      const [formData, setFormData] = useState({\r\n            id: groupData?.id,\r\n            sId: groupData?.sId || selectedId,\r\n            name: groupData?.name || \"\",\r\n            groupName: groupData?.groupName || \"\",\r\n            price: groupData?.price || 0,\r\n            strikeoffPrice: groupData?.strikeoffPrice || 0,\r\n            seq: groupData?.seq || 0,\r\n      });\r\n      useEffect(() => {\r\n            if (searchTerm.length >= 3) {\r\n                  setFilteredVariation(\r\n                        items.filter(item =>\r\n                              item.displayName.toLowerCase().includes(searchTerm.toLowerCase())\r\n                        )\r\n                  );\r\n            } else {\r\n                  setFilteredVariation(items);\r\n            }\r\n      }, [searchTerm, items]);\r\n      useEffect(() => {\r\n            if (!isOpen) {\r\n                  setSelectedId(null);\r\n                  setSearchTerm('');\r\n                  setChoosenAddon(false);\r\n                  setChoosenAddonName('');\r\n            }\r\n      }, [!isOpen]);\r\n\r\n      useEffect(() => {\r\n            if (groupData) {\r\n                  setFormData({\r\n                        id: groupData?.id || 0,\r\n                        sId: groupData.sId || '',\r\n                        name: groupData.name || '',\r\n                        groupName: groupData.groupName || '',\r\n                        price: groupData.price || 0,\r\n                        strikeoffPrice: groupData.strikeoffPrice || 0,\r\n                        seq: groupData.seq || 0,\r\n                  });\r\n                  setSelectedId(groupData.sId || null);\r\n                  setChoosenAddon(!!groupData.sId);\r\n                  setChoosenAddonName(groupData.name || '');\r\n            }\r\n      }, [groupData]);\r\n\r\n      const handleSelect = (variation: MyVariationData) => {\r\n            setSelectedId(variation?.id ?? 0);\r\n            setChoosenAddon(true)\r\n            setChoosenAddonName(variation.displayName)\r\n\r\n      }\r\n      const deselectAddongroupMap = () => {\r\n            setSelectedId(null)\r\n            setChoosenAddon(false)\r\n      }\r\n\r\n      const fetcher = useFetcher();\r\n      const loading = fetcher.state != \"idle\";\r\n      const actionData = useActionData<{ sucess?: boolean; error?: string }>();\r\n\r\n      useEffect(() => {\r\n            if (actionData?.sucess) {\r\n\r\n                  console.log(actionData.sucess, \"ppppppppp\")\r\n                  onClose();\r\n            }\r\n      }, [actionData, onClose]);\r\n\r\n      if (!isOpen) return null;\r\n      return (\r\n            <Dialog open={isOpen} onOpenChange={onClose}>\r\n\r\n                  <DialogContent className=\"w-full max-w-md sm:max-w-lg bg-white rounded-2xl shadow-2xl\">\r\n                        <DialogTitle className=\"text-2xl font-bold text-gray-900 mb-4\">{header}</DialogTitle>\r\n                        <div className=\"space-y-6\">\r\n                              {choosenAddon === false && selectedId === null && <>\r\n                                    <div>\r\n                                          <input\r\n                                                placeholder=\"Search by Addon Name\"\r\n                                                type=\"search\"\r\n                                                className=\"w-full p-3 rounded-full border-2 border-gray-200 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-colors\"\r\n                                                autoFocus\r\n                                                value={searchTerm}\r\n                                                onChange={(e) => setSearchTerm(e.target.value)}\r\n                                          />\r\n                                    </div>\r\n                                    <div className=\"mt-4 max-h-[40vh] overflow-y-auto pr-2 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100\">\r\n                                          <ul className=\"space-y-2\">\r\n                                                {filteredVariation.length === 0 ? (\r\n                                                      <p className=\"p-4 text-gray-500 text-center\">No add-ons-group found</p>\r\n                                                ) : (\r\n                                                      filteredVariation.map((item) => (\r\n                                                            <li key={item.id} className=\"flex items-center gap-3\">\r\n                                                                  <input\r\n                                                                        type=\"checkbox\"\r\n                                                                        id={`item-${item.id}`}\r\n                                                                        name=\"selectedItem\"\r\n                                                                        value={item.id}\r\n                                                                        checked={selectedId === item.id}\r\n                                                                        onChange={() => handleSelect(item)}\r\n                                                                        className=\"h-5 w-5 text-blue-600 focus:ring-blue-500 rounded\"\r\n                                                                  />\r\n                                                                  <label\r\n                                                                        htmlFor={`item-${item.id}`}\r\n                                                                        className={`cursor-pointer p-3 w-full rounded-lg border ${selectedId === item.id ? 'bg-blue-50 border-blue-200' : 'border-gray-200'} text-gray-800 hover:bg-gray-50 transition-colors`}\r\n                                                                  >\r\n                                                                        {item?.displayName} <span className=\"text-gray-500\">({item?.internalName})</span>\r\n                                                                  </label>\r\n                                                            </li>\r\n                                                      ))\r\n                                                )}\r\n                                          </ul>\r\n                                    </div>\r\n                              </>\r\n                              }\r\n\r\n                              {choosenAddon && selectedId && <div className=\"space-y-4\">\r\n                                    {selectedId && (\r\n                                          <div className=\"flex items-center justify-between bg-blue-50 p-3 rounded-lg\">\r\n                                                <p className=\"font-medium text-gray-800 truncate max-w-[80%]\">{choossenAddonName}</p>\r\n                                                <SquareX\r\n                                                      color=\"red\"\r\n                                                      className=\"cursor-pointer hover:scale-110 transition-transform\"\r\n                                                      onClick={() => deselectAddongroupMap()}\r\n                                                />\r\n                                          </div>\r\n                                    )}\r\n\r\n                                    <div className=\"grid grid-cols-1 sm:grid-cols-2 gap-4\">\r\n                                          <div>\r\n                                                <label htmlFor=\"name\" className=\"block text-sm font-medium text-gray-700\">\r\n                                                      Name\r\n                                                </label>\r\n                                                <input\r\n                                                      type=\"text\"\r\n                                                      name=\"name\"\r\n                                                      id=\"name\"\r\n                                                      value={formData.name}\r\n                                                      onChange={(e) => setFormData({ ...formData, name: e.target.value })}\r\n                                                      className=\"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm\"\r\n                                                      placeholder=\"Enter name\"\r\n                                                />\r\n                                          </div>\r\n                                          <div>\r\n                                                <label htmlFor=\"seq\" className=\"block text-sm font-medium text-gray-700 mb-1\">\r\n                                                      Sequence\r\n                                                </label>\r\n                                                <input\r\n                                                      type=\"number\"\r\n                                                      id=\"seq\"\r\n                                                      name=\"seq\"\r\n                                                      value={formData.seq}\r\n                                                      onChange={(e) => setFormData({ ...formData, seq: Number(e.target.value) })}\r\n                                                      min=\"0\"\r\n                                                      required\r\n                                                      className=\"w-full p-2 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none\"\r\n                                                />\r\n                                          </div>\r\n                                          <div>\r\n                                                <label htmlFor=\"groupName\" className=\"block text-sm font-medium text-gray-700\">\r\n                                                      groupName                                                </label>\r\n                                                <input\r\n                                                      type=\"text\"\r\n                                                      name=\"groupName\"\r\n                                                      id=\"groupName\"\r\n                                                      value={formData.groupName}\r\n                                                      onChange={(e) => setFormData({ ...formData, groupName: e.target.value })}\r\n                                                      className=\"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm\"\r\n                                                />\r\n                                          </div>\r\n                                          <div>\r\n                                                <label htmlFor=\"price\" className=\"block text-sm font-medium text-gray-700\">\r\n                                                      price\r\n                                                </label>\r\n                                                <input\r\n                                                      type=\"number\"\r\n                                                      name=\"price\"\r\n                                                      id=\"price\"\r\n                                                      value={formData.price}\r\n                                                      onChange={(e) => setFormData({ ...formData, price: Number(e.target.value) })}\r\n                                                      className=\"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm\"\r\n                                                      min=\"0\"\r\n                                                />\r\n                                          </div>\r\n                                          <div>\r\n                                                <label htmlFor=\"strikeoffPrice\" className=\"block text-sm font-medium text-gray-700\">\r\n                                                      strikeoffPrice\r\n                                                </label>\r\n                                                <input\r\n                                                      type=\"number\"\r\n                                                      name=\"strikeoffPrice\"\r\n                                                      id=\"strikeoffPrice\"\r\n                                                      value={formData.strikeoffPrice}\r\n                                                      onChange={(e) => setFormData({ ...formData, strikeoffPrice: Number(e.target.value) })}\r\n                                                      className=\"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm\"\r\n                                                      min=\"0\"\r\n                                                />\r\n                                          </div>\r\n\r\n\r\n\r\n                                    </div>\r\n                              </div>}\r\n                        </div>\r\n                        <Form method=\"POST\" className=\"mt-6 flex flex-col sm:flex-row gap-3 justify-end\">\r\n                              <input type=\"hidden\" name=\"sId\" value={selectedId?.toString()} readOnly />\r\n                              <input type=\"hidden\" name=\"itemId\" value={groupId?.toString()} readOnly />\r\n                              <input type=\"hidden\" name=\"groupName\" value={formData.groupName?.toString()} readOnly />\r\n                              <input type=\"hidden\" name=\"name\" value={formData.name?.toString()} readOnly />\r\n                              <input type=\"hidden\" name=\"seq\" value={formData.seq?.toString()} readOnly />\r\n                              <input type=\"hidden\" name=\"price\" value={formData.price?.toString()} readOnly />\r\n                              <input type=\"hidden\" name=\"strikeoffPrice\" value={formData.strikeoffPrice?.toString()} readOnly />\r\n                              <input type=\"hidden\" name=\"sellerId\" value={sellerId?.toString()} readOnly />\r\n                              <input type=\"hidden\" name=\"addonName\" value={choossenAddonName?.toString()} readOnly />\r\n                              <input type=\"hidden\" name=\"actionType\" value={\"actionItemVariation\"} readOnly />\r\n                              <input type=\"hidden\" name=\"mode\" value={isEdit ? \"EditMode\" : \"\"} />\r\n                              {isEdit && <input type=\"hidden\" name=\"selectedId\" value={groupData?.id?.toString()} readOnly />\r\n                              }\r\n                              <button\r\n                                    onClick={onClose}\r\n                                    className=\"w-full sm:w-auto px-4 py-2 rounded-lg bg-gray-100 text-gray-700 hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-300 text-sm font-medium\"\r\n                              >\r\n                                    Cancel\r\n                              </button>\r\n                              <button\r\n                                    type=\"submit\"\r\n                                    disabled={selectedId === null || loading}\r\n                                    className={`w-full sm:w-auto px-4 py-2 rounded-lg text-sm font-medium text-white focus:outline-none focus:ring-2 focus:ring-blue-500 ${selectedId === null ? 'bg-gray-400 cursor-not-allowed' : 'bg-blue-600 hover:bg-blue-700'}`}\r\n                              >\r\n\r\n                                    {loading ? (\r\n                                          <>\r\n                                                <Loader2 className=\"animate-spin h-5 w-5 mr-2\" />\r\n                                                Submitting...\r\n                                          </>\r\n                                    ) : (\r\n                                          'Confirm'\r\n                                    )}\r\n\r\n                              </button>\r\n                        </Form>\r\n                  </DialogContent>\r\n            </Dialog>\r\n      );\r\n};\r\n\r\nexport default SelectedItemVariation;\r\n", "import { useState, useEffect } from 'react';\r\nimport { Form, json, useActionData, useFetcher, useLoaderData, useNavigate, useSearchParams } from \"@remix-run/react\";\r\nimport { withAuth, withResponse } from \"~/utils/auth-utils\";\r\nimport { Pencil, Trash, Search, X } from \"lucide-react\";\r\nimport { Tabs, TabsList, TabsTrigger, TabsContent } from \"~/components/ui/tabs\";\r\nimport { ResponsiveTable } from '~/components/ui/responsiveTable';\r\nimport { Button } from '~/components/ui/button';\r\nimport SpinnerLoader from '~/components/loader/SpinnerLoader';\r\nimport { AddOnGroup, SelectedItem } from '~/types/api/businessConsoleService/MyItemList';\r\nimport { createItemAddonGroup, createItemVariation, deleteItemAddonGroup, deleteItemVarition, getSellerAddonGroups, getSellerItemVariation } from '~/services/sellerItem.service';\r\nimport { MyAddonGroupData, MyVariationData } from '~/types/api/businessConsoleService/SellerManagement';\r\nimport { getAddonsGroups, getVariation } from '~/services/businessConsoleService';\r\nimport SelectedItemAddons from '~/components/common/SelectedItemAddons';\r\nimport SelectedItemVariation from '~/components/common/selectedItemVariation';\r\nimport { useDebounce } from '~/hooks/useDebounce';\r\n\r\ninterface LoaderData {\r\n      seAddonGroupData?: AddOnGroup[];\r\n      variationList: SelectedItem[];\r\n      itemName: string;\r\n      itemId: number;\r\n      sellerId: number;\r\n}\r\n\r\ninterface ActionData {\r\n      selectedItemGruoupData: MyAddonGroupData[];\r\n      sucessMessage: string;\r\n      ErrorMessage: string;\r\n      sucess: boolean;\r\n}\r\n\r\ninterface VarActionData {\r\n      itemVariation: MyVariationData[];\r\n      sucess: boolean;\r\n}\r\n\r\nexport const loader = withAuth(async ({ request, user }) => {\r\n      const url = new URL(request.url);\r\n      const sellerId = Number(url.searchParams.get(\"sellerId\"));\r\n      const activeTab = url.searchParams.get(\"activeTab\") || 'Variations';\r\n      const page = Number(url.searchParams.get(\"page\") || \"0\");\r\n      const pageSize = Number(url.searchParams.get(\"pageSize\") || \"50\");\r\n      const matchBy = url.searchParams.get(\"matchBy\") || \"\";\r\n      const itemId = Number(url.searchParams.get(\"itemId\"));\r\n      const itemName = url.searchParams.get(\"itemName\");\r\n      let response;\r\n      let variationList: SelectedItem[] = [];\r\n      let seAddonGroupData: AddOnGroup[] = [];\r\n\r\n      try {\r\n            switch (activeTab) {\r\n                  case \"Variations\":\r\n                        response = await getSellerItemVariation(sellerId, page, pageSize, matchBy, itemId, request);\r\n                        variationList = response?.data || [];\r\n                        break;\r\n                  case \"AddonsGroups\":\r\n                        response = await getSellerAddonGroups(sellerId, page, pageSize, matchBy, itemId, request);\r\n                        seAddonGroupData = response?.data || [];\r\n                        break;\r\n            }\r\n            return withResponse({ variationList, itemName, seAddonGroupData, itemId, sellerId }, response?.headers);\r\n      } catch (error) {\r\n            console.error(\"Error in loader:\", error);\r\n            return json({ variationList: [] });\r\n      }\r\n});\r\n\r\nexport const action = withAuth(async ({ request }) => {\r\n      const formData = await request.formData();\r\n      const sellerId = Number(formData.get(\"sellerId\"));\r\n      const pageSize = Number(formData.get(\"pageSize\") || 50);\r\n      const page = Number(formData.get(\"page\") || \"0\");\r\n      const matchBy = formData.get(\"matchBy\") as string;\r\n      const actionType = formData.get(\"actionType\") as string;\r\n\r\n      if (actionType === \"getItemVariations\") {\r\n            try {\r\n                  const variationList = await getVariation(sellerId, page, pageSize, matchBy, request);\r\n                  const itemVariation = variationList.data;\r\n                  return withResponse({ itemVariation }, variationList.headers);\r\n            } catch (error) {\r\n                  console.error(\"Error loading variations:\", error);\r\n                  throw new Response('Failed to load variations', { status: 500 });\r\n            }\r\n      }\r\n\r\n      if (actionType === \"getAddonsGroupMap\") {\r\n            try {\r\n                  const addonsgList = await getAddonsGroups(sellerId, page, pageSize, matchBy, request);\r\n                  const selectedItemGruoupData = addonsgList.data;\r\n                  return withResponse({ selectedItemGruoupData }, addonsgList.headers);\r\n            } catch (error) {\r\n                  console.error(\"Error loading addons groups:\", error);\r\n                  throw new Response('Failed to load addons', { status: 500 });\r\n            }\r\n      } else if (actionType === \"actionItemVariation\") {\r\n            const itemId = Number(formData.get(\"itemId\"));\r\n            const sId = formData.get(\"sId\") as string;\r\n            const name = formData.get(\"name\") as string;\r\n            const groupName = formData.get(\"groupName\");\r\n            const price = Number(formData.get(\"price\"));\r\n            const strikeoffPrice = Number(formData.get(\"strikeoffPrice\"));\r\n            const seq = Number(formData.get(\"seq\"));\r\n            const id = Number(formData.get(\"selectedId\"));\r\n            const mode = formData.get(\"mode\")\r\n\r\n            const createPayload: any = {\r\n                  sId,\r\n                  name,\r\n                  groupName,\r\n                  seq,\r\n                  price,\r\n                  strikeoffPrice,\r\n            };\r\n            const editPayLoad: any = {\r\n                  id,\r\n                  sId,\r\n                  name,\r\n                  groupName,\r\n                  seq,\r\n                  price,\r\n                  strikeoffPrice,\r\n            };\r\n\r\n            const finalPayLoad = mode == \"EditMode\" ? editPayLoad : createPayload\r\n\r\n            try {\r\n                  const addonsList = await createItemVariation(sellerId, itemId, finalPayLoad, request);\r\n                  const selectedAddonsData = addonsList.data;\r\n                  return withResponse({ selectedAddonsData, sucess: addonsList.statusCode === 200 }, addonsList.headers);\r\n            } catch (error) {\r\n                  console.error(\"Error loading addons groups:\", error);\r\n                  throw new Response('Failed to load addons', { status: 500 });\r\n            }\r\n      } else if (actionType === \"actionItemAddonGroup\") {\r\n            const itemId = Number(formData.get(\"itemId\"));\r\n            const sequence = Number(formData.get(\"sequence\"));\r\n            const sId = formData.get(\"sId\") as string;\r\n            const name = formData.get(\"name\") as string;\r\n            const id = Number(formData.get(\"selectedId\"));\r\n            const minSelect = Number(formData.get(\"minSelect\"));\r\n            const maxSelect = Number(formData.get(\"maxSelect\"));\r\n            const description = formData.get(\"description\") as string;\r\n            const varient = formData.get(\"varient\") as unknown as boolean;\r\n            const mode = formData.get(\"mode\")\r\n\r\n            const createPayload: any = {\r\n                  sId,\r\n                  minSelect,\r\n                  maxSelect,\r\n                  name,\r\n                  seq: sequence,\r\n                  description,\r\n                  varient,\r\n            };\r\n            const editPayload: any = {\r\n                  id,\r\n                  sId,\r\n                  minSelect,\r\n                  maxSelect,\r\n                  name,\r\n                  seq: sequence,\r\n                  description,\r\n                  varient,\r\n            };\r\n\r\n\r\n            const finalPayload = mode === \"EditMode\" ? editPayload : createPayload;\r\n\r\n            try {\r\n                  const addonsList = await createItemAddonGroup(sellerId, itemId, finalPayload, request);\r\n                  const selectedAddonsData = addonsList.data;\r\n                  return withResponse({ selectedAddonsData, success: true }, addonsList.headers);\r\n            } catch (error) {\r\n                  console.error(\"Error loading addons groups:\", error);\r\n                  throw new Response('Failed to load addons', { status: 500 });\r\n            }\r\n      } else if (actionType === \"addonGroupDelete\") {\r\n            const ItemaddongId = Number(formData.get(\"ItemaddongId\"));\r\n            const itemId = Number(formData.get(\"itemId\"));\r\n\r\n            try {\r\n                  const addonsList = await deleteItemAddonGroup(sellerId, ItemaddongId, itemId, request);\r\n                  const selectedAddonsData = addonsList.data;\r\n                  return withResponse({ selectedAddonsData }, addonsList.headers);\r\n            } catch (error) {\r\n                  console.error(\"Error loading addons groups:\", error);\r\n                  throw new Response('Failed to load addons', { status: 500 });\r\n            }\r\n      } else if (actionType === \"itemVarDelete\") {\r\n            const varId = Number(formData.get(\"itemVarId\"));\r\n            const itemId = Number(formData.get(\"itemId\"));\r\n\r\n            try {\r\n                  const response = await deleteItemVarition(sellerId, varId, itemId, request);\r\n                  const itemVariation = response.data;\r\n                  return withResponse({ itemVariation }, response.headers);\r\n            } catch (error) {\r\n                  console.error(\"Error deleting variation:\", error);\r\n                  throw new Response('Failed to delete variation', { status: 500 });\r\n            }\r\n      }\r\n});\r\n\r\nexport default function SelectedSellerItem() {\r\n      const { variationList, itemName, seAddonGroupData, itemId, sellerId } = useLoaderData<LoaderData>();\r\n      const [searchParams] = useSearchParams();\r\n      const activeTab = searchParams.get(\"activeTab\") || \"Variations\";\r\n      const fetcher = useFetcher();\r\n      const navigate = useNavigate();\r\n      const loading = fetcher.state !== \"idle\";\r\n\r\n      const [variationSearchTerm, setVariationSearchTerm] = useState('');\r\n      const [addonGroupSearchTerm, setAddonGroupSearchTerm] = useState('');\r\n\r\n      const addonGroupDebounce = useDebounce(addonGroupSearchTerm, 300)\r\n      const variationDebounce = useDebounce(variationSearchTerm, 300)\r\n\r\n      const [filteredVariations, setFilteredVariations] = useState<SelectedItem[]>(variationList);\r\n      const [filteredAddonGroups, setFilteredAddonGroups] = useState<AddOnGroup[]>(seAddonGroupData || []);\r\n\r\n      const variationHeaders = [\"ID\", \"Name\", \"Group Name\", \"Price\", \"Strike Off Price\", \"Quantity\", \"\", \"\"];\r\n      const addonGroupHeaders = [\"ID\", \"Name\", \"Description\", \"Min Select\", \"Max Select\", \"\", \"\", \"\"];\r\n\r\n      useEffect(() => {\r\n            if (variationDebounce.length >= 3 && variationDebounce !== \"\") {\r\n                  const filtered = (variationList || []).filter((item) =>\r\n                        item.name?.toLowerCase().includes(variationDebounce.toLowerCase()) ||\r\n                        item.groupName?.toLowerCase().includes(variationDebounce.toLowerCase())\r\n                  );\r\n                  console.log('Filtered addon groups:', filtered);\r\n                  setFilteredVariations(filtered);\r\n            }\r\n            else {\r\n                  setFilteredVariations(variationList || []);\r\n            }\r\n\r\n      }, [variationList, variationDebounce]);\r\n\r\n      // Filter Addon Groups\r\n      useEffect(() => {\r\n            if (addonGroupDebounce.length >= 3 && addonGroupDebounce !== \"\") {\r\n                  const filtered = (seAddonGroupData || []).filter((item) =>\r\n                        item.name?.toLowerCase().includes(addonGroupDebounce.toLowerCase()) ||\r\n                        item.description?.toLowerCase().includes(addonGroupDebounce.toLowerCase())\r\n                  );\r\n                  console.log('Filtered addon groups:', filtered);\r\n                  setFilteredAddonGroups(filtered);\r\n            }\r\n            else {\r\n                  setFilteredAddonGroups(seAddonGroupData || []);\r\n            }\r\n\r\n      }, [seAddonGroupData, addonGroupDebounce]);\r\n\r\n      // Reset search term when switching tabs\r\n      useEffect(() => {\r\n            setVariationSearchTerm('');\r\n            setAddonGroupSearchTerm('');\r\n      }, [activeTab]);\r\n\r\n      const handleTabChange = (newTab: string) => {\r\n            navigate(`?itemId=${itemId}&itemName=${encodeURIComponent(itemName)}&activeTab=${newTab}&sellerId=${sellerId}`);\r\n      };\r\n\r\n      const [itemselectedGroupAddons, setItemselectedGroupAddons] = useState(false);\r\n      const [selectedAddonsgData, setSelectedAddonsgData] = useState<MyAddonGroupData[]>();\r\n      const [selectedItemGdata, setSelectedItemGdata] = useState<AddOnGroup>();\r\n      const [selectedItemVarData, setSelectedtemVarData] = useState<SelectedItem>();\r\n      const [selectedVariations, setSelectedVariations] = useState<MyVariationData[]>();\r\n      const [itemSelectedVariation, setItemSelectedVariation] = useState(false);\r\n\r\n      const addonitemgfetcher = useFetcher<ActionData>();\r\n      const itemVaritonfetcher = useFetcher<VarActionData>();\r\n\r\n      const actionData = useActionData<ActionData>();\r\n      const varActionData = useActionData<VarActionData>();\r\n\r\n      const [isEditopen, setIseditOpen] = useState(false);\r\n      const [isVarEditOpen, setIsVarEditOpen] = useState(false);\r\n\r\n      const handleDelete = (addonsData: AddOnGroup) => {\r\n            const formData = new FormData();\r\n            formData.append(\"actionType\", \"addonGroupDelete\");\r\n            formData.append(\"itemId\", itemId.toString());\r\n            formData.append(\"ItemaddongId\", addonsData?.id?.toString() ?? \"\");\r\n            formData.append(\"sellerId\", sellerId.toString());\r\n            addonitemgfetcher.submit(formData, { method: 'post' });\r\n      };\r\n\r\n      const handleItemVariationsDelete = (variationData: SelectedItem) => {\r\n            const formData = new FormData();\r\n            formData.append(\"actionType\", \"itemVarDelete\");\r\n            formData.append(\"itemId\", itemId.toString());\r\n            formData.append(\"itemVarId\", variationData?.id?.toString() ?? \"\");\r\n            formData.append(\"sellerId\", sellerId.toString());\r\n            itemVaritonfetcher.submit(formData, { method: 'post' });\r\n      };\r\n\r\n      useEffect(() => {\r\n            if (itemVaritonfetcher.state === \"idle\") {\r\n                  if (varActionData?.itemVariation) {\r\n                        setSelectedVariations(varActionData?.itemVariation);\r\n                        setItemSelectedVariation(true);\r\n                        setIsVarEditOpen(false)\r\n                  } else {\r\n                        setSelectedVariations([]);\r\n                        setItemSelectedVariation(false);\r\n                        setIsVarEditOpen(false)\r\n\r\n                  }\r\n            }\r\n      }, [varActionData]);\r\n\r\n      useEffect(() => {\r\n            if (addonitemgfetcher.state === \"idle\") {\r\n                  if (actionData?.selectedItemGruoupData) {\r\n                        setSelectedAddonsgData(actionData.selectedItemGruoupData);\r\n                        setItemselectedGroupAddons(true);\r\n                        setIseditOpen(false);\r\n                  } else {\r\n                        setSelectedAddonsgData([]);\r\n                        setIseditOpen(false);\r\n                        setItemselectedGroupAddons(false);\r\n                  }\r\n            }\r\n      }, [actionData]);\r\n\r\n      const handleSelectedItemGroupData = (row: AddOnGroup) => {\r\n            setItemselectedGroupAddons(true);\r\n            setSelectedItemGdata(row);\r\n            setIseditOpen(true);\r\n      };\r\n\r\n      const handleSelectedItemVarData = (row: SelectedItem) => {\r\n            setItemSelectedVariation(true);\r\n\r\n            setSelectedtemVarData(row);\r\n            setIsVarEditOpen(true);\r\n      };\r\n\r\n      return (\r\n            <div key={activeTab}>\r\n                  {loading && <SpinnerLoader loading={loading} />}\r\n                  <h1\r\n                        className=\"mb-6 text-2xl font-bold text-gray-900 cursor-pointer hover:text-blue-600 transition-colors\"\r\n                        onClick={() => navigate(-1)}\r\n                  >\r\n                        Seller Item / {itemName}\r\n                  </h1>\r\n\r\n                  <Tabs value={activeTab} onValueChange={handleTabChange} className=\"bg-white rounded-lg shadow-sm border border-gray-200\">\r\n                        <TabsList className=\"border-b border-gray-200\">\r\n                              <TabsTrigger\r\n                                    value=\"Variations\"\r\n                                    className=\"px-4 py-2 text-sm font-medium text-gray-700 data-[state=active]:text-blue-600 data-[state=active]:border-b-2 data-[state=active]:border-blue-600\"\r\n                              >\r\n                                    Variations\r\n                              </TabsTrigger>\r\n                              <TabsTrigger\r\n                                    value=\"AddonsGroups\"\r\n                                    className=\"px-4 py-2 text-sm font-medium text-gray-700 data-[state=active]:text-blue-600 data-[state=active]:border-b-2 data-[state=active]:border-blue-600\"\r\n                              >\r\n                                    Addons Groups\r\n                              </TabsTrigger>\r\n                        </TabsList>\r\n\r\n                        <TabsContent value=\"Variations\" className=\"p-4\">\r\n                              <div className=\"mb-4 relative\">\r\n                                    <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5\" />\r\n                                    <input\r\n                                          type=\"text\"\r\n                                          placeholder=\"Search variations by name or group name...\"\r\n                                          value={variationSearchTerm}\r\n                                          onChange={(e) => setVariationSearchTerm(e.target.value)}\r\n                                          className=\"w-full pl-10 pr-10 py-2 rounded-lg border border-gray-200 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all duration-200 text-sm\"\r\n                                    />\r\n                                    {variationSearchTerm && (\r\n                                          <X\r\n                                                className=\"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5 cursor-pointer hover:text-gray-600\"\r\n                                                onClick={() => setVariationSearchTerm('')}\r\n                                          />\r\n                                    )}\r\n                              </div>\r\n                              <ResponsiveTable\r\n                                    headers={variationHeaders}\r\n                                    data={filteredVariations}\r\n                                    renderRow={(row: SelectedItem) => (\r\n                                          <tr key={row.id} className=\"border-b hover:bg-gray-50 transition-colors\">\r\n                                                <td className=\"py-3 px-4 text-center text-sm text-gray-700\">{row?.id}</td>\r\n                                                <td className=\"py-3 px-4 text-center text-sm text-blue-600 cursor-pointer hover:underline\" onClick={() => navigate(`/home/<USER>/td>\r\n                                                <td className=\"py-3 px-4 text-center text-sm text-gray-700\">{row?.groupName || \"-\"}</td>\r\n                                                <td className=\"py-3 px-4 text-center text-sm text-gray-700\">\r\n                                                      {row?.price > 0 ? `₹ ${row?.price.toFixed(2)}` : '-'}\r\n                                                </td>\r\n                                                <td className=\"py-3 px-4 text-center text-sm text-gray-700\">\r\n                                                      {row?.strikeoffPrice > 0 ? `₹ ${row?.strikeoffPrice.toFixed(2)}` : '-'}\r\n                                                </td>\r\n                                                <td className=\"py-3 px-4 text-center text-sm text-gray-700\">\r\n                                                      {row?.qty > 0 ? `${row?.qty.toFixed(2)}` : '-'}\r\n                                                </td>\r\n                                                <td className=\"py-3 px-4 text-center\">\r\n                                                      <Button\r\n                                                            variant=\"ghost\"\r\n                                                            size=\"sm\"\r\n                                                            className=\"text-red-500 hover:text-red-700\"\r\n                                                            onClick={() => {\r\n                                                                  if (confirm(\"Are you sure you want to delete this Item Variation?\")) {\r\n                                                                        handleItemVariationsDelete(row);\r\n                                                                  }\r\n                                                            }}\r\n                                                      >\r\n                                                            <Trash size={18} />\r\n                                                      </Button>\r\n                                                </td>\r\n                                                <td className=\"py-3 px-4 text-center\">\r\n                                                      <Button\r\n                                                            variant=\"ghost\"\r\n                                                            size=\"sm\"\r\n                                                            className=\"text-blue-500 hover:text-blue-700\"\r\n                                                            onClick={() => handleSelectedItemVarData(row)}\r\n                                                      >\r\n                                                            <Pencil size={18} />\r\n                                                      </Button>\r\n                                                </td>\r\n                                          </tr>\r\n                                    )}\r\n                              />\r\n                              <Form method=\"post\">\r\n                                    <input name=\"sellerId\" value={sellerId} hidden />\r\n                                    <input name=\"matchBy\" value={\"\"} hidden />\r\n                                    <input name=\"actionType\" value={\"getItemVariations\"} hidden />\r\n                                    <input name=\"pageSize\" value={\"50\"} hidden />\r\n                                    <Button\r\n                                          className=\"fixed bottom-5 right-5 rounded-full bg-blue-600 text-white hover:bg-blue-700 shadow-lg transition-all duration-200 px-6 py-3\"\r\n                                          type=\"submit\"\r\n                                    >\r\n                                          + Add Variation\r\n                                    </Button>\r\n                              </Form>\r\n                              <SelectedItemVariation\r\n                                    isOpen={itemSelectedVariation}\r\n                                    items={selectedVariations || []}\r\n                                    onClose={() => setItemSelectedVariation(false)}\r\n                                    header={isVarEditOpen ? 'Edit Item Variation' : \"Add Item Variation\"}\r\n                                    sellerId={sellerId}\r\n                                    groupId={itemId}\r\n                                    groupData={isVarEditOpen ? selectedItemVarData : undefined}\r\n                                    isEdit={isVarEditOpen}\r\n                              />\r\n                        </TabsContent>\r\n\r\n                        <TabsContent value=\"AddonsGroups\" className=\"p-4\">\r\n                              <div className=\"mb-4 relative\">\r\n                                    <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5\" />\r\n                                    <input\r\n                                          type=\"text\"\r\n                                          placeholder=\"Search addon groups by name or description...\"\r\n                                          value={addonGroupSearchTerm}\r\n                                          onChange={(e) => setAddonGroupSearchTerm(e.target.value)}\r\n                                          className=\"w-full pl-10 pr-10 py-2 rounded-lg border border-gray-200 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all duration-200 text-sm\"\r\n                                    />\r\n                                    {addonGroupSearchTerm && (\r\n                                          <X\r\n                                                className=\"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5 cursor-pointer hover:text-gray-600\"\r\n                                                onClick={() => setAddonGroupSearchTerm('')}\r\n                                          />\r\n                                    )}\r\n                              </div>\r\n                              <ResponsiveTable\r\n                                    headers={addonGroupHeaders}\r\n                                    data={filteredAddonGroups}\r\n\r\n                                    renderRow={(row: AddOnGroup) => (\r\n                                          <tr key={row.id} className=\"border-b hover:bg-gray-50 transition-colors\">\r\n                                                <td className=\"py-3 px-4 text-center text-sm text-gray-700\">{row.id}</td>\r\n                                                <td className=\"py-3 px-4 text-center text-sm text-blue-600 cursor-pointer hover:underline\">{row.name}</td>\r\n                                                <td className=\"py-3 px-4 text-center text-sm text-gray-700\">{row.description || '-'}</td>\r\n                                                <td className=\"py-3 px-4 text-center text-sm text-gray-700\">{row.minSelect}</td>\r\n                                                <td className=\"py-3 px-4 text-center text-sm text-gray-700\">{row.maxSelect}</td>\r\n                                                {/* <td className=\"py-3 px-4 text-center text-sm\">\r\n                                                      {row?.varient ? (\r\n                                                            <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800\">\r\n                                                                  <svg\r\n                                                                        className=\"w-4 h-4 mr-1 text-green-500\"\r\n                                                                        fill=\"currentColor\"\r\n                                                                        viewBox=\"0 0 20 20\"\r\n                                                                        xmlns=\"http://www.w3.org/2000/svg\"\r\n                                                                  >\r\n                                                                        <path\r\n                                                                              fillRule=\"evenodd\"\r\n                                                                              d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414  Foram1.414l2 2a1 1 0 001.414 0l4-4z\"\r\n                                                                              clipRule=\"evenodd\" />\r\n                                                                  </svg>\r\n                                                                  Active\r\n                                                            </span>\r\n                                                      ) : (\r\n                                                            <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800\">\r\n                                                                  <svg\r\n                                                                        className=\"w-4 h-4 mr-1 text-red-500\"\r\n                                                                        fill=\"currentColor\"\r\n                                                                        viewBox=\"0 0 20 20\"\r\n                                                                        xmlns=\"http://www.w3.org/2000/svg\"\r\n                                                                  >\r\n                                                                        <path\r\n                                                                              fillRule=\"evenodd\"\r\n                                                                              d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 001.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\"\r\n                                                                              clipRule=\"evenodd\" />\r\n                                                                  </svg>\r\n                                                                  Inactive\r\n                                                            </span>\r\n                                                      )}\r\n                                                </td> */}\r\n                                                <td className=\"py-3 px-4 text-center\">\r\n                                                      <Button\r\n                                                            variant=\"ghost\"\r\n                                                            size=\"sm\"\r\n                                                            className=\"text-red-500 hover:text-red-700\"\r\n                                                            onClick={() => {\r\n                                                                  if (confirm(\"Are you sure you want to delete this Item AddonGroup?\")) {\r\n                                                                        handleDelete(row);\r\n                                                                  }\r\n                                                            }}\r\n                                                      >\r\n                                                            <Trash size={18} />\r\n                                                      </Button>\r\n                                                </td>\r\n                                                <td className=\"py-3 px-4 text-center\">\r\n                                                      <Button\r\n                                                            variant=\"ghost\"\r\n                                                            size=\"sm\"\r\n                                                            className=\"text-blue-500 hover:text-blue-700\"\r\n                                                            onClick={() => handleSelectedItemGroupData(row)}\r\n                                                      >\r\n                                                            <Pencil size={18} />\r\n                                                      </Button>\r\n                                                </td>\r\n                                          </tr>\r\n                                    )} />\r\n                              <Form method=\"post\">\r\n                                    <input name=\"sellerId\" value={sellerId} hidden />\r\n                                    <input name=\"matchBy\" value={\"\"} hidden />\r\n                                    <input name=\"actionType\" value={\"getAddonsGroupMap\"} hidden />\r\n                                    <input name=\"pageSize\" value={\"50\"} hidden />\r\n                                    <Button\r\n                                          className=\"fixed bottom-5 right-5 rounded-full bg-blue-600 text-white hover:bg-blue-700 shadow-lg transition-all duration-200 px-6 py-3\"\r\n                                          type=\"submit\"\r\n                                    >\r\n                                          + Add Addon Group\r\n                                    </Button>\r\n                              </Form>\r\n                              <SelectedItemAddons\r\n                                    isOpen={itemselectedGroupAddons}\r\n                                    items={selectedAddonsgData || []}\r\n                                    onClose={() => setItemselectedGroupAddons(false)}\r\n                                    header={isEditopen ? `Edit Addon for ${itemName?.slice(0, 15)}` : `Create Addon for ${itemName?.slice(0, 15)}`}\r\n                                    groupData={isEditopen ? selectedItemGdata : undefined}\r\n                                    sellerId={sellerId}\r\n                                    groupId={itemId}\r\n                                    isEdit={isEditopen}\r\n\r\n                              />\r\n                        </TabsContent>\r\n                  </Tabs>\r\n            </div>\r\n      );\r\n}"], "names": ["useState", "useEffect", "jsx", "jsxs", "Fragment", "Loader2", "SelectedSellerItem", "variationList", "itemName", "seAddonGroupData", "itemId", "sellerId", "useLoaderData", "searchParams", "useSearchParams", "activeTab", "get", "fetcher", "useFetcher", "navigate", "useNavigate", "loading", "state", "variationSearchTerm", "setVariationSearchTerm", "addonGroupSearchTerm", "setAddonGroupSearchTerm", "addonGroupDebounce", "useDebounce", "variationDebounce", "filteredVariations", "setFilteredVariations", "filteredAddonGroups", "setFilteredAddonGroups", "variationHeaders", "addonGroupHeaders", "length", "filtered", "filter", "item", "name", "toLowerCase", "includes", "groupName", "console", "log", "description", "handleTabChange", "newTab", "encodeURIComponent", "itemselectedGroupAddons", "setItemselectedGroupAddons", "selectedAddonsgData", "setSelectedAddonsgData", "selectedItemGdata", "setSelectedItemGdata", "selectedItemVarData", "setSelectedtemVarData", "selectedVariations", "setSelectedVariations", "itemSelectedVariation", "setItemSelectedVariation", "addoni<PERSON>g<PERSON><PERSON>er", "itemVaritonfetcher", "actionData", "useActionData", "varActionData", "isEditopen", "setIseditOpen", "isVarEditOpen", "setIsVarEditOpen", "handleDelete", "addonsData", "formData", "FormData", "append", "toString", "id", "submit", "method", "handleItemVariationsDelete", "variationData", "itemVariation", "selectedItemGruoupData", "handleSelectedItemGroupData", "row", "handleSelectedItemVarData", "children", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "className", "onClick", "Tabs", "value", "onValueChange", "TabsList", "TabsTrigger", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Search", "type", "placeholder", "onChange", "e", "target", "X", "ResponsiveTable", "headers", "data", "renderRow", "price", "toFixed", "strikeoffPrice", "qty", "<PERSON><PERSON>", "variant", "size", "confirm", "Trash", "Pencil", "Form", "hidden", "SelectedItemVariation", "isOpen", "items", "onClose", "header", "groupId", "groupData", "isEdit", "minSelect", "maxSelect", "SelectedItemAddons", "slice"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkBA,MAAM,qBAAwD,CAAC;AAAA,EACzD;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACN,MAAM;;AAEA,QAAM,CAAC,YAAY,aAAa,IAAIA,aAAAA,SAAiC,IAAI;AACzE,QAAM,CAAC,YAAY,aAAa,IAAIA,aAAAA,SAAS,EAAE;AAC/C,QAAM,CAAC,eAAe,gBAAgB,IAAIA,aAAAA,SAA6B,KAAK;AAC5E,QAAM,CAAC,cAAc,eAAe,IAAIA,aAAAA,SAAkB,KAAK;AAC/D,QAAM,CAAC,kBAAkB,mBAAmB,IAAIA,aAAAA,SAAiB,EAAE;AACpD,YAAU;AACzB,QAAM,aAAa,cAAqC;AAGxD,QAAM,CAAC,UAAU,WAAW,IAAIA,sBAAqB;AAAA,IAC/C,KAAK;AAAA,IACL,MAAM;AAAA,IACN,WAAW;AAAA,IACX,WAAW;AAAA,IACX,aAAa;AAAA,IACb,KAAK;AAAA,IACL,SAAS;AAAA,EAAA,CACd;AAGDC,eAAAA,UAAU,MAAM;AACV,QAAI,WAAW;AACG,kBAAA;AAAA,QACN,IAAI,UAAU;AAAA,QACd,KAAK,UAAU;AAAA,QACf,MAAM,UAAU,QAAQ;AAAA,QACxB,WAAW,OAAO,UAAU,cAAc,WAAW,UAAU,YAAY;AAAA,QAC3E,WAAW,OAAO,UAAU,cAAc,WAAW,UAAU,YAAY;AAAA,QAC3E,aAAa,UAAU,eAAe;AAAA,QACtC,KAAK,OAAO,UAAU,QAAQ,WAAW,UAAU,MAAM;AAAA,QACzD,SAAS,CAAC,CAAC,UAAU;AAAA,MAAA,CAC1B;AAGD,UAAI,UAAU,KAAK;AACb,sBAAc,UAAU,GAAG;AAC3B,wBAAgB,IAAI;AACd,cAAA,gBAAgB,MAAM,KAAK,CAAC,SAAS,KAAK,OAAO,OAAO,UAAU,GAAG,CAAC;AAC5E,YAAI,eAAe;AACb,8BAAoB,cAAc,WAAW;AAAA,QAAA;AAAA,MACnD;AAAA,IACN,OACC;AAED,oBAAc,IAAI;AAClB,sBAAgB,KAAK;AACrB,0BAAoB,EAAE;AACV,kBAAA;AAAA,QACN,KAAK;AAAA,QACL,MAAM;AAAA,QACN,WAAW;AAAA,QACX,WAAW;AAAA,QACX,aAAa;AAAA,QACb,KAAK;AAAA,QACL,SAAS;AAAA,MAAA,CACd;AAAA,IAAA;AAAA,EAEV,GAAA,CAAC,WAAW,OAAO,MAAM,CAAC;AAG7BA,eAAAA,UAAU,MAAM;AACV,QAAI,WAAW,UAAU,KAAK,eAAe,IAAI;AAC3C;AAAA,QACM,+BAAO;AAAA,UAAO,CAAC,UACT,+BAAO,YAAY,cAAc,SAAS,WAAW,YAAa;AAAA;AAAA,MAE9E;AAAA,IAAA,OACC;AACD,uBAAiB,KAAK;AAAA,IAAA;AAAA,EAC5B,GACH,CAAC,YAAY,KAAK,CAAC;AAGtBA,eAAAA,UAAU,MAAM;AACV,QAAI,yCAAY,SAAS;AACX,cAAA;AAAA,IAAA;AAAA,EACd,GACH,CAAC,YAAY,OAAO,CAAC;AAGlB,QAAA,eAAe,CAAC,UAA4B;AAC5C,kBAAc,MAAM,EAAE;AACtB,oBAAgB,IAAI;AACpB,wBAAoB,MAAM,WAAW;AAAA,EAC3C;AAGA,QAAM,wBAAwB,MAAM;AAC9B,kBAAc,IAAI;AAClB,oBAAgB,KAAK;AACrB,wBAAoB,EAAE;AAAA,EAC5B;AAGA,QAAM,eAAe,MAAM;AACrB,QAAI,gBAAgB,YAAY;AAE1B,oBAAc,IAAI;AAClB,sBAAgB,KAAK;AACrB,0BAAoB,EAAE;AACtB,oBAAc,EAAE;AAChB,uBAAiB,KAAK;AAAA,IAAA,OACrB;AAEO,cAAA;AAAA,IAAA;AAAA,EAEpB;AAEI,MAAA,CAAC,OAAe,QAAA;AAGd,SAAAC,kCAAA,IAAC,UAAO,MAAM,QAAQ,cAAc,SAC9B,UAAAC,kCAAA,KAAC,eAAc,EAAA,WAAU,+DACnB,UAAA;AAAA,IAACD,kCAAA,IAAA,aAAA,EAAY,WAAU,yCAChB,UACP,QAAA;AAAA,IAEAC,kCAAAA,KAAC,OAAI,EAAA,WAAU,aAER,UAAA;AAAA,MAAC,CAAA,gBAAgB,eAAe,QAErBA,kCAAAA,KAAAC,kBAAAA,UAAA,EAAA,UAAA;AAAA,QAACD,kCAAAA,KAAA,OAAA,EAAI,WAAU,YACT,UAAA;AAAA,UAAAD,kCAAAA,IAAC,SAAI,WAAU,mEACT,gDAAC,QAAO,EAAA,WAAU,yBAAwB,EAChD,CAAA;AAAA,UACAA,kCAAA;AAAA,YAAC;AAAA,YAAA;AAAA,cACK,aAAY;AAAA,cACZ,MAAK;AAAA,cACL,WAAU;AAAA,cACV,WAAS;AAAA,cACT,OAAO;AAAA,cACP,UAAU,CAAC,MAAM,cAAc,EAAE,OAAO,KAAK;AAAA,YAAA;AAAA,UAAA;AAAA,QACnD,GACN;AAAA,QACAA,kCAAA,IAAC,OAAI,EAAA,WAAU,2GACT,UAAAA,kCAAAA,IAAC,QAAG,WAAU,aACP,UAAc,cAAA,WAAW,IACpBA,kCAAA,IAAC,OAAE,WAAU,iCAAgC,UAE7C,yBAAA,CAAA,IAEA,+CAAe,IAAI,CAAC,SACdC,kCAAA,KAAC,MAAiB,EAAA,WAAU,2BACtB,UAAA;AAAA,UAAAD,kCAAA;AAAA,YAAC;AAAA,YAAA;AAAA,cACK,MAAK;AAAA,cACL,IAAI,QAAQ,KAAK,EAAE;AAAA,cACnB,MAAK;AAAA,cACL,OAAO,KAAK;AAAA,cACZ,SAAS,eAAe,KAAK;AAAA,cAC7B,UAAU,MAAM,aAAa,IAAI;AAAA,cACjC,WAAU;AAAA,YAAA;AAAA,UAChB;AAAA,UACAC,kCAAA;AAAA,YAAC;AAAA,YAAA;AAAA,cACK,SAAS,QAAQ,KAAK,EAAE;AAAA,cACxB,WAAW,+CAA+C,eAAe,KAAK,KACtE,+BACA,iBACF;AAAA,cAEL,UAAA;AAAA,gBAAM,6BAAA;AAAA,gBAAa;AAAA,gBACpBA,kCAAAA,KAAC,QAAK,EAAA,WAAU,iBAAgB,UAAA;AAAA,kBAAA;AAAA,kBACxB,6BAAM;AAAA,kBAAa;AAAA,gBAAA,EAC3B,CAAA;AAAA,cAAA;AAAA,YAAA;AAAA,UAAA;AAAA,QACN,EAAA,GArBG,KAAK,EAsBd,GAGlB,CAAA,EACN,CAAA;AAAA,MAAA,GACN;AAAA,OAIJ,gBAAgB,eACXA,kCAAA,KAAA,OAAA,EAAI,WAAU,aAET,UAAA;AAAA,QAACA,kCAAAA,KAAA,OAAA,EAAI,WAAU,+DACT,UAAA;AAAA,UAACD,kCAAA,IAAA,KAAA,EAAE,WAAU,kDACN,UACP,kBAAA;AAAA,UACAA,kCAAA;AAAA,YAAC;AAAA,YAAA;AAAA,cACK,OAAM;AAAA,cACN,WAAU;AAAA,cACV,SAAS;AAAA,YAAA;AAAA,UAAA;AAAA,QACf,GACN;AAAA,QAGAC,kCAAAA,KAAC,OAAI,EAAA,WAAU,yCACT,UAAA;AAAA,UAAAA,uCAAC,OACK,EAAA,UAAA;AAAA,YAAAA,kCAAA;AAAA,cAAC;AAAA,cAAA;AAAA,gBACK,SAAQ;AAAA,gBACR,WAAU;AAAA,gBACf,UAAA;AAAA,kBAAA;AAAA,kBACWD,kCAAA,IAAA,QAAA,EAAK,WAAU,gBAAe,UAAC,IAAA,CAAA;AAAA,gBAAA;AAAA,cAAA;AAAA,YAC3C;AAAA,YACAA,kCAAA;AAAA,cAAC;AAAA,cAAA;AAAA,gBACK,MAAK;AAAA,gBACL,MAAK;AAAA,gBACL,IAAG;AAAA,gBACH,OAAO,SAAS;AAAA,gBAChB,UAAU,CAAC,MACL,YAAY,EAAE,GAAG,UAAU,MAAM,EAAE,OAAO,OAAO;AAAA,gBAEvD,WAAU;AAAA,gBACV,aAAY;AAAA,gBACZ,UAAQ;AAAA,cAAA;AAAA,YAAA;AAAA,UACd,GACN;AAAA,iDACC,OACK,EAAA,UAAA;AAAA,YAAAC,kCAAA;AAAA,cAAC;AAAA,cAAA;AAAA,gBACK,SAAQ;AAAA,gBACR,WAAU;AAAA,gBACf,UAAA;AAAA,kBAAA;AAAA,kBACeD,kCAAA,IAAA,QAAA,EAAK,WAAU,gBAAe,UAAC,IAAA,CAAA;AAAA,gBAAA;AAAA,cAAA;AAAA,YAC/C;AAAA,YACAA,kCAAA;AAAA,cAAC;AAAA,cAAA;AAAA,gBACK,MAAK;AAAA,gBACL,IAAG;AAAA,gBACH,MAAK;AAAA,gBACL,OAAO,SAAS;AAAA,gBAChB,UAAU,CAAC,MACL,YAAY;AAAA,kBACN,GAAG;AAAA,kBACH,KAAK,EAAE,OAAO,UAAU,KAAK,IAAI,OAAO,EAAE,OAAO,KAAK;AAAA,gBAAA,CAC3D;AAAA,gBAEP,KAAI;AAAA,gBACJ,UAAQ;AAAA,gBACR,WAAU;AAAA,cAAA;AAAA,YAAA;AAAA,UAChB,GACN;AAAA,iDACC,OACK,EAAA,UAAA;AAAA,YAAAA,kCAAA;AAAA,cAAC;AAAA,cAAA;AAAA,gBACK,SAAQ;AAAA,gBACR,WAAU;AAAA,gBACf,UAAA;AAAA,cAAA;AAAA,YAED;AAAA,YACAA,kCAAA;AAAA,cAAC;AAAA,cAAA;AAAA,gBACK,MAAK;AAAA,gBACL,MAAK;AAAA,gBACL,IAAG;AAAA,gBACH,OAAO,SAAS;AAAA,gBAChB,UAAU,CAAC,MACL,YAAY;AAAA,kBACN,GAAG;AAAA,kBACH,WAAW,EAAE,OAAO,UAAU,KAAK,IAAI,OAAO,EAAE,OAAO,KAAK;AAAA,gBAAA,CACjE;AAAA,gBAEP,WAAU;AAAA,gBACV,KAAI;AAAA,cAAA;AAAA,YAAA;AAAA,UACV,GACN;AAAA,iDACC,OACK,EAAA,UAAA;AAAA,YAAAA,kCAAA;AAAA,cAAC;AAAA,cAAA;AAAA,gBACK,SAAQ;AAAA,gBACR,WAAU;AAAA,gBACf,UAAA;AAAA,cAAA;AAAA,YAED;AAAA,YACAA,kCAAA;AAAA,cAAC;AAAA,cAAA;AAAA,gBACK,MAAK;AAAA,gBACL,MAAK;AAAA,gBACL,IAAG;AAAA,gBACH,OAAO,SAAS;AAAA,gBAChB,UAAU,CAAC,MACL,YAAY;AAAA,kBACN,GAAG;AAAA,kBACH,WAAW,EAAE,OAAO,UAAU,KAAK,IAAI,OAAO,EAAE,OAAO,KAAK;AAAA,gBAAA,CACjE;AAAA,gBAEP,WAAU;AAAA,gBACV,KAAI;AAAA,cAAA;AAAA,YAAA;AAAA,UACV,GACN;AAAA,UACAC,kCAAAA,KAAC,OAAI,EAAA,WAAU,iBACT,UAAA;AAAA,YAAAD,kCAAA;AAAA,cAAC;AAAA,cAAA;AAAA,gBACK,SAAQ;AAAA,gBACR,WAAU;AAAA,gBACf,UAAA;AAAA,cAAA;AAAA,YAED;AAAA,YACAA,kCAAA;AAAA,cAAC;AAAA,cAAA;AAAA,gBACK,MAAK;AAAA,gBACL,IAAG;AAAA,gBACH,OAAO,SAAS;AAAA,gBAChB,UAAU,CAAC,MACL,YAAY,EAAE,GAAG,UAAU,aAAa,EAAE,OAAO,OAAO;AAAA,gBAE9D,WAAU;AAAA,gBACV,aAAY;AAAA,gBACZ,MAAM;AAAA,cAAA;AAAA,YAAA;AAAA,UACZ,EACN,CAAA;AAAA,QAAA,EAmBN,CAAA;AAAA,MAAA,EACN,CAAA;AAAA,IAAA,GAEZ;AAAA,IAGCC,kCAAA,KAAA,MAAA,EAAK,QAAO,QAAO,WAAU,oDAExB,UAAA;AAAA,MAACD,kCAAAA,IAAA,SAAA,EAAM,MAAK,UAAS,MAAK,OAAM,OAAO,yCAAY,YAAY;AAAA,MAC/DA,kCAAAA,IAAC,WAAM,MAAK,UAAS,MAAK,UAAS,OAAO,mCAAS,YAAY;AAAA,MAC/DA,kCAAAA,IAAC,SAAM,EAAA,MAAK,UAAS,MAAK,aAAY,QAAO,cAAS,cAAT,mBAAoB,WAAY,CAAA;AAAA,MAC7EA,kCAAAA,IAAC,SAAM,EAAA,MAAK,UAAS,MAAK,QAAO,QAAO,cAAS,SAAT,mBAAe,WAAY,CAAA;AAAA,MACnEA,kCAAAA,IAAC,SAAM,EAAA,MAAK,UAAS,MAAK,aAAY,QAAO,cAAS,cAAT,mBAAoB,WAAY,CAAA;AAAA,MAC7EA,kCAAAA,IAAC,SAAM,EAAA,MAAK,UAAS,MAAK,WAAU,QAAO,cAAS,YAAT,mBAAkB,WAAY,CAAA;AAAA,MACzEA,kCAAAA,IAAC,SAAM,EAAA,MAAK,UAAS,MAAK,eAAc,QAAO,cAAS,gBAAT,mBAAsB,WAAY,CAAA;AAAA,MACjFA,kCAAAA,IAAC,SAAM,EAAA,MAAK,UAAS,MAAK,OAAM,QAAO,cAAS,QAAT,mBAAc,WAAY,CAAA;AAAA,MACjEA,kCAAAA,IAAC,WAAM,MAAK,UAAS,MAAK,YAAW,OAAO,qCAAU,YAAY;AAAA,MAClEA,kCAAAA,IAAC,WAAM,MAAK,UAAS,MAAK,aAAY,OAAO,qDAAkB,YAAY;AAAA,4CAC1E,SAAM,EAAA,MAAK,UAAS,MAAK,cAAa,OAAO,wBAAwB;AAAA,MACtEA,kCAAAA,IAAC,WAAM,MAAK,UAAS,MAAK,QAAO,OAAO,SAAS,aAAa,GAAI,CAAA;AAAA,MACjE,UAAWA,kCAAAA,IAAA,SAAA,EAAM,MAAK,UAAS,MAAK,cAAa,QAAO,4CAAW,OAAX,mBAAe,WAAY,CAAA;AAAA,MAIpFA,kCAAA;AAAA,QAAC;AAAA,QAAA;AAAA,UACK,MAAK;AAAA,UACL,SAAS;AAAA,UACT,WAAU;AAAA,UACf,UAAA;AAAA,QAAA;AAAA,MAED;AAAA,MACAA,kCAAA;AAAA,QAAC;AAAA,QAAA;AAAA,UACK,MAAK;AAAA,UACL,UAAU,CAAC,cAAc,CAAC,SAAS;AAAA,UACnC,WAAW,8IAA8I,CAAC,cAAc,CAAC,SAAS,OAC1K,mCACA,+BACF;AAAA,UACX,UAAA;AAAA,QAAA;AAAA,MAAA;AAAA,IAED,EACN,CAAA;AAAA,EAAA,EAAA,CACN,EACN,CAAA;AAEZ;ACjXA,MAAM,wBAA2D,CAAC;AAAA,EAC5D;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACN,MAAM;;AACA,QAAM,CAAC,YAAY,aAAa,IAAIF,aAAAA,SAAiC,IAAI;AACzE,QAAM,CAAC,YAAY,aAAa,IAAIA,aAAAA,SAAS,EAAE;AAC/C,QAAM,CAAC,mBAAmB,oBAAoB,IAAIA,aAAAA,SAA4B,KAAK;AACnF,QAAM,CAAC,cAAc,eAAe,IAAIA,aAAAA,SAAkB,KAAK;AAC/D,QAAM,CAAC,mBAAmB,mBAAmB,IAAIA,aAAAA,SAAiB,EAAE;AACpE,QAAM,CAAC,UAAU,WAAW,IAAIA,sBAAS;AAAA,IACnC,IAAI,uCAAW;AAAA,IACf,MAAK,uCAAW,QAAO;AAAA,IACvB,OAAM,uCAAW,SAAQ;AAAA,IACzB,YAAW,uCAAW,cAAa;AAAA,IACnC,QAAO,uCAAW,UAAS;AAAA,IAC3B,iBAAgB,uCAAW,mBAAkB;AAAA,IAC7C,MAAK,uCAAW,QAAO;AAAA,EAAA,CAC5B;AACDC,eAAAA,UAAU,MAAM;AACN,QAAA,WAAW,UAAU,GAAG;AACtB;AAAA,QACM,MAAM;AAAA,UAAO,CAAA,SACP,KAAK,YAAY,YAAA,EAAc,SAAS,WAAW,YAAa,CAAA;AAAA,QAAA;AAAA,MAE5E;AAAA,IAAA,OACC;AACD,2BAAqB,KAAK;AAAA,IAAA;AAAA,EAChC,GACH,CAAC,YAAY,KAAK,CAAC;AACtBA,eAAAA,UAAU,MAAM;AACV,QAAI,CAAC,QAAQ;AACP,oBAAc,IAAI;AAClB,oBAAc,EAAE;AAChB,sBAAgB,KAAK;AACrB,0BAAoB,EAAE;AAAA,IAAA;AAAA,EAC5B,GACH,CAAC,CAAC,MAAM,CAAC;AAEZA,eAAAA,UAAU,MAAM;AACV,QAAI,WAAW;AACG,kBAAA;AAAA,QACN,KAAI,uCAAW,OAAM;AAAA,QACrB,KAAK,UAAU,OAAO;AAAA,QACtB,MAAM,UAAU,QAAQ;AAAA,QACxB,WAAW,UAAU,aAAa;AAAA,QAClC,OAAO,UAAU,SAAS;AAAA,QAC1B,gBAAgB,UAAU,kBAAkB;AAAA,QAC5C,KAAK,UAAU,OAAO;AAAA,MAAA,CAC3B;AACa,oBAAA,UAAU,OAAO,IAAI;AACnB,sBAAA,CAAC,CAAC,UAAU,GAAG;AACX,0BAAA,UAAU,QAAQ,EAAE;AAAA,IAAA;AAAA,EAC9C,GACH,CAAC,SAAS,CAAC;AAER,QAAA,eAAe,CAAC,cAA+B;AACjC,mBAAA,uCAAW,OAAM,CAAC;AAChC,oBAAgB,IAAI;AACpB,wBAAoB,UAAU,WAAW;AAAA,EAE/C;AACA,QAAM,wBAAwB,MAAM;AAC9B,kBAAc,IAAI;AAClB,oBAAgB,KAAK;AAAA,EAC3B;AAEA,QAAM,UAAU,WAAW;AACrB,QAAA,UAAU,QAAQ,SAAS;AACjC,QAAM,aAAa,cAAoD;AAEvEA,eAAAA,UAAU,MAAM;AACV,QAAI,yCAAY,QAAQ;AAEV,cAAA,IAAI,WAAW,QAAQ,WAAW;AAClC,cAAA;AAAA,IAAA;AAAA,EACd,GACH,CAAC,YAAY,OAAO,CAAC;AAEpB,MAAA,CAAC,OAAe,QAAA;AAEd,SAAAC,kCAAA,IAAC,UAAO,MAAM,QAAQ,cAAc,SAE9B,UAAAC,kCAAA,KAAC,eAAc,EAAA,WAAU,+DACnB,UAAA;AAAA,IAACD,kCAAA,IAAA,aAAA,EAAY,WAAU,yCAAyC,UAAO,QAAA;AAAA,IACvEC,kCAAAA,KAAC,OAAI,EAAA,WAAU,aACR,UAAA;AAAA,MAAiB,iBAAA,SAAS,eAAe,QACpCA,kCAAAA,KAAAC,kBAAAA,UAAA,EAAA,UAAA;AAAA,QAAAF,sCAAC,OACK,EAAA,UAAAA,kCAAA;AAAA,UAAC;AAAA,UAAA;AAAA,YACK,aAAY;AAAA,YACZ,MAAK;AAAA,YACL,WAAU;AAAA,YACV,WAAS;AAAA,YACT,OAAO;AAAA,YACP,UAAU,CAAC,MAAM,cAAc,EAAE,OAAO,KAAK;AAAA,UAAA;AAAA,QAAA,GAEzD;AAAA,QACAA,kCAAA,IAAC,OAAI,EAAA,WAAU,2GACT,UAAAA,kCAAAA,IAAC,QAAG,WAAU,aACP,UAAkB,kBAAA,WAAW,IACxBA,kCAAA,IAAC,OAAE,WAAU,iCAAgC,UAAsB,yBAAA,CAAA,IAEnE,kBAAkB,IAAI,CAAC,SACjBC,kCAAA,KAAC,MAAiB,EAAA,WAAU,2BACtB,UAAA;AAAA,UAAAD,kCAAA;AAAA,YAAC;AAAA,YAAA;AAAA,cACK,MAAK;AAAA,cACL,IAAI,QAAQ,KAAK,EAAE;AAAA,cACnB,MAAK;AAAA,cACL,OAAO,KAAK;AAAA,cACZ,SAAS,eAAe,KAAK;AAAA,cAC7B,UAAU,MAAM,aAAa,IAAI;AAAA,cACjC,WAAU;AAAA,YAAA;AAAA,UAChB;AAAA,UACAC,kCAAA;AAAA,YAAC;AAAA,YAAA;AAAA,cACK,SAAS,QAAQ,KAAK,EAAE;AAAA,cACxB,WAAW,+CAA+C,eAAe,KAAK,KAAK,+BAA+B,iBAAiB;AAAA,cAElI,UAAA;AAAA,gBAAM,6BAAA;AAAA,gBAAY;AAAA,gBAACA,kCAAAA,KAAC,QAAK,EAAA,WAAU,iBAAgB,UAAA;AAAA,kBAAA;AAAA,kBAAE,6BAAM;AAAA,kBAAa;AAAA,gBAAA,EAAC,CAAA;AAAA,cAAA;AAAA,YAAA;AAAA,UAAA;AAAA,QAChF,EAAA,GAfG,KAAK,EAgBd,CACL,EAEb,CAAA,EACN,CAAA;AAAA,MAAA,GACN;AAAA,MAGC,gBAAgB,cAAeA,uCAAA,OAAA,EAAI,WAAU,aACvC,UAAA;AAAA,QACK,cAAAA,kCAAA,KAAC,OAAI,EAAA,WAAU,+DACT,UAAA;AAAA,UAACD,kCAAA,IAAA,KAAA,EAAE,WAAU,kDAAkD,UAAkB,mBAAA;AAAA,UACjFA,kCAAA;AAAA,YAAC;AAAA,YAAA;AAAA,cACK,OAAM;AAAA,cACN,WAAU;AAAA,cACV,SAAS,MAAM,sBAAsB;AAAA,YAAA;AAAA,UAAA;AAAA,QAC3C,GACN;AAAA,QAGNC,kCAAAA,KAAC,OAAI,EAAA,WAAU,yCACT,UAAA;AAAA,UAAAA,uCAAC,OACK,EAAA,UAAA;AAAA,YAAAD,sCAAC,SAAM,EAAA,SAAQ,QAAO,WAAU,2CAA0C,UAE1E,QAAA;AAAA,YACAA,kCAAA;AAAA,cAAC;AAAA,cAAA;AAAA,gBACK,MAAK;AAAA,gBACL,MAAK;AAAA,gBACL,IAAG;AAAA,gBACH,OAAO,SAAS;AAAA,gBAChB,UAAU,CAAC,MAAM,YAAY,EAAE,GAAG,UAAU,MAAM,EAAE,OAAO,OAAO;AAAA,gBAClE,WAAU;AAAA,gBACV,aAAY;AAAA,cAAA;AAAA,YAAA;AAAA,UAClB,GACN;AAAA,iDACC,OACK,EAAA,UAAA;AAAA,YAAAA,sCAAC,SAAM,EAAA,SAAQ,OAAM,WAAU,gDAA+C,UAE9E,YAAA;AAAA,YACAA,kCAAA;AAAA,cAAC;AAAA,cAAA;AAAA,gBACK,MAAK;AAAA,gBACL,IAAG;AAAA,gBACH,MAAK;AAAA,gBACL,OAAO,SAAS;AAAA,gBAChB,UAAU,CAAC,MAAM,YAAY,EAAE,GAAG,UAAU,KAAK,OAAO,EAAE,OAAO,KAAK,GAAG;AAAA,gBACzE,KAAI;AAAA,gBACJ,UAAQ;AAAA,gBACR,WAAU;AAAA,cAAA;AAAA,YAAA;AAAA,UAChB,GACN;AAAA,iDACC,OACK,EAAA,UAAA;AAAA,YAAAA,sCAAC,SAAM,EAAA,SAAQ,aAAY,WAAU,2CAA0C,UAChB,6DAAA;AAAA,YAC/DA,kCAAA;AAAA,cAAC;AAAA,cAAA;AAAA,gBACK,MAAK;AAAA,gBACL,MAAK;AAAA,gBACL,IAAG;AAAA,gBACH,OAAO,SAAS;AAAA,gBAChB,UAAU,CAAC,MAAM,YAAY,EAAE,GAAG,UAAU,WAAW,EAAE,OAAO,OAAO;AAAA,gBACvE,WAAU;AAAA,cAAA;AAAA,YAAA;AAAA,UAChB,GACN;AAAA,iDACC,OACK,EAAA,UAAA;AAAA,YAAAA,sCAAC,SAAM,EAAA,SAAQ,SAAQ,WAAU,2CAA0C,UAE3E,SAAA;AAAA,YACAA,kCAAA;AAAA,cAAC;AAAA,cAAA;AAAA,gBACK,MAAK;AAAA,gBACL,MAAK;AAAA,gBACL,IAAG;AAAA,gBACH,OAAO,SAAS;AAAA,gBAChB,UAAU,CAAC,MAAM,YAAY,EAAE,GAAG,UAAU,OAAO,OAAO,EAAE,OAAO,KAAK,GAAG;AAAA,gBAC3E,WAAU;AAAA,gBACV,KAAI;AAAA,cAAA;AAAA,YAAA;AAAA,UACV,GACN;AAAA,iDACC,OACK,EAAA,UAAA;AAAA,YAAAA,sCAAC,SAAM,EAAA,SAAQ,kBAAiB,WAAU,2CAA0C,UAEpF,kBAAA;AAAA,YACAA,kCAAA;AAAA,cAAC;AAAA,cAAA;AAAA,gBACK,MAAK;AAAA,gBACL,MAAK;AAAA,gBACL,IAAG;AAAA,gBACH,OAAO,SAAS;AAAA,gBAChB,UAAU,CAAC,MAAM,YAAY,EAAE,GAAG,UAAU,gBAAgB,OAAO,EAAE,OAAO,KAAK,GAAG;AAAA,gBACpF,WAAU;AAAA,gBACV,KAAI;AAAA,cAAA;AAAA,YAAA;AAAA,UACV,EACN,CAAA;AAAA,QAAA,EAIN,CAAA;AAAA,MAAA,EACN,CAAA;AAAA,IAAA,GACN;AAAA,IACCC,kCAAA,KAAA,MAAA,EAAK,QAAO,QAAO,WAAU,oDACxB,UAAA;AAAA,MAACD,kCAAAA,IAAA,SAAA,EAAM,MAAK,UAAS,MAAK,OAAM,OAAO,yCAAY,YAAY,UAAQ,KAAC,CAAA;AAAA,MACxEA,kCAAAA,IAAC,SAAM,EAAA,MAAK,UAAS,MAAK,UAAS,OAAO,mCAAS,YAAY,UAAQ,KAAC,CAAA;AAAA,MACvEA,kCAAAA,IAAA,SAAA,EAAM,MAAK,UAAS,MAAK,aAAY,QAAO,cAAS,cAAT,mBAAoB,YAAY,UAAQ,KAAC,CAAA;AAAA,MACrFA,kCAAAA,IAAA,SAAA,EAAM,MAAK,UAAS,MAAK,QAAO,QAAO,cAAS,SAAT,mBAAe,YAAY,UAAQ,KAAC,CAAA;AAAA,MAC3EA,kCAAAA,IAAA,SAAA,EAAM,MAAK,UAAS,MAAK,OAAM,QAAO,cAAS,QAAT,mBAAc,YAAY,UAAQ,KAAC,CAAA;AAAA,MACzEA,kCAAAA,IAAA,SAAA,EAAM,MAAK,UAAS,MAAK,SAAQ,QAAO,cAAS,UAAT,mBAAgB,YAAY,UAAQ,KAAC,CAAA;AAAA,MAC7EA,kCAAAA,IAAA,SAAA,EAAM,MAAK,UAAS,MAAK,kBAAiB,QAAO,cAAS,mBAAT,mBAAyB,YAAY,UAAQ,KAAC,CAAA;AAAA,MAChGA,kCAAAA,IAAC,SAAM,EAAA,MAAK,UAAS,MAAK,YAAW,OAAO,qCAAU,YAAY,UAAQ,KAAC,CAAA;AAAA,MAC3EA,kCAAAA,IAAC,SAAM,EAAA,MAAK,UAAS,MAAK,aAAY,OAAO,uDAAmB,YAAY,UAAQ,KAAC,CAAA;AAAA,MACrFA,kCAAAA,IAAC,WAAM,MAAK,UAAS,MAAK,cAAa,OAAO,uBAAuB,UAAQ,KAAC,CAAA;AAAA,MAC9EA,kCAAAA,IAAC,WAAM,MAAK,UAAS,MAAK,QAAO,OAAO,SAAS,aAAa,GAAI,CAAA;AAAA,MACjE,UAAUA,kCAAA,IAAC,SAAM,EAAA,MAAK,UAAS,MAAK,cAAa,QAAO,4CAAW,OAAX,mBAAe,YAAY,UAAQ,KAAC,CAAA;AAAA,MAE7FA,kCAAA;AAAA,QAAC;AAAA,QAAA;AAAA,UACK,SAAS;AAAA,UACT,WAAU;AAAA,UACf,UAAA;AAAA,QAAA;AAAA,MAED;AAAA,MACAA,kCAAA;AAAA,QAAC;AAAA,QAAA;AAAA,UACK,MAAK;AAAA,UACL,UAAU,eAAe,QAAQ;AAAA,UACjC,WAAW,4HAA4H,eAAe,OAAO,mCAAmC,+BAA+B;AAAA,UAG9N,oBAEWC,kCAAAA,KAAAC,kBAAA,UAAA,EAAA,UAAA;AAAA,YAACF,kCAAAA,IAAAG,cAAA,EAAQ,WAAU,4BAA4B,CAAA;AAAA,YAAE;AAAA,UAAA,EAAA,CAEvD,IAEA;AAAA,QAAA;AAAA,MAAA;AAAA,IAGZ,EACN,CAAA;AAAA,EAAA,EAAA,CACN,EACN,CAAA;AAEZ;AC1EA,SAAwBC,qBAAqB;AACvC,QAAM;AAAA,IAAEC;AAAAA,IAAeC;AAAAA,IAAUC;AAAAA,IAAkBC;AAAAA,IAAQC;AAAAA,MAAaC,cAA0B;AAC5F,QAAA,CAACC,YAAY,IAAIC,gBAAgB;AACvC,QAAMC,YAAYF,aAAaG,IAAI,WAAW,KAAK;AACnD,QAAMC,UAAUC,WAAW;AAC3B,QAAMC,WAAWC,YAAY;AACvB,QAAAC,UAAUJ,QAAQK,UAAU;AAElC,QAAM,CAACC,qBAAqBC,sBAAsB,IAAIxB,aAAAA,SAAS,EAAE;AACjE,QAAM,CAACyB,sBAAsBC,uBAAuB,IAAI1B,aAAAA,SAAS,EAAE;AAE7D,QAAA2B,qBAAqBC,YAAYH,sBAAsB,GAAG;AAC1D,QAAAI,oBAAoBD,YAAYL,qBAAqB,GAAG;AAE9D,QAAM,CAACO,oBAAoBC,qBAAqB,IAAI/B,aAAAA,SAAyBO,aAAa;AAC1F,QAAM,CAACyB,qBAAqBC,sBAAsB,IAAIjC,aAAAA,SAAuBS,oBAAoB,CAAA,CAAE;AAE7F,QAAAyB,mBAAmB,CAAC,MAAM,QAAQ,cAAc,SAAS,oBAAoB,YAAY,IAAI,EAAE;AAC/F,QAAAC,oBAAoB,CAAC,MAAM,QAAQ,eAAe,cAAc,cAAc,IAAI,IAAI,EAAE;AAE9FlC,eAAAA,UAAU,MAAM;AACV,QAAI4B,kBAAkBO,UAAU,KAAKP,sBAAsB,IAAI;AACnD,YAAAQ,YAAY9B,iBAAiB,IAAI+B,OAAQC;;AACzCA,2BAAKC,SAALD,mBAAWE,cAAcC,SAASb,kBAAkBY,YAAA,SACpDF,UAAKI,cAALJ,mBAAgBE,cAAcC,SAASb,kBAAkBY;OAC/D;AACQG,cAAAC,IAAI,0BAA0BR,QAAQ;AAC9CN,4BAAsBM,QAAQ;AAAA,IACpC,OACK;AACuBN,4BAAAxB,iBAAiB,EAAE;AAAA,IAC/C;AAAA,EAEN,GAAG,CAACA,eAAesB,iBAAiB,CAAC;AAGrC5B,eAAAA,UAAU,MAAM;AACV,QAAI0B,mBAAmBS,UAAU,KAAKT,uBAAuB,IAAI;AACrD,YAAAU,YAAY5B,oBAAoB,IAAI6B,OAAQC;;AAC5CA,2BAAKC,SAALD,mBAAWE,cAAcC,SAASf,mBAAmBc,YAAA,SACrDF,UAAKO,gBAALP,mBAAkBE,cAAcC,SAASf,mBAAmBc;OAClE;AACQG,cAAAC,IAAI,0BAA0BR,QAAQ;AAC9CJ,6BAAuBI,QAAQ;AAAA,IACrC,OACK;AACwBJ,6BAAAxB,oBAAoB,EAAE;AAAA,IACnD;AAAA,EAEN,GAAG,CAACA,kBAAkBkB,kBAAkB,CAAC;AAGzC1B,eAAAA,UAAU,MAAM;AACVuB,2BAAuB,EAAE;AACzBE,4BAAwB,EAAE;AAAA,EAChC,GAAG,CAACX,SAAS,CAAC;AAER,QAAAgC,kBAAmBC,YAAmB;AAC7B7B,aAAA,WAAWT,MAAM,aAAauC,mBAAmBzC,QAAQ,CAAC,cAAcwC,MAAM,aAAarC,QAAQ,EAAE;AAAA,EACpH;AAEA,QAAM,CAACuC,yBAAyBC,0BAA0B,IAAInD,aAAAA,SAAS,KAAK;AAC5E,QAAM,CAACoD,qBAAqBC,sBAAsB,IAAIrD,sBAA6B;AACnF,QAAM,CAACsD,mBAAmBC,oBAAoB,IAAIvD,sBAAqB;AACvE,QAAM,CAACwD,qBAAqBC,qBAAqB,IAAIzD,sBAAuB;AAC5E,QAAM,CAAC0D,oBAAoBC,qBAAqB,IAAI3D,sBAA4B;AAChF,QAAM,CAAC4D,uBAAuBC,wBAAwB,IAAI7D,aAAAA,SAAS,KAAK;AAExE,QAAM8D,oBAAoB5C,WAAuB;AACjD,QAAM6C,qBAAqB7C,WAA0B;AAErD,QAAM8C,aAAaC,cAA0B;AAC7C,QAAMC,gBAAgBD,cAA6B;AAEnD,QAAM,CAACE,YAAYC,aAAa,IAAIpE,aAAAA,SAAS,KAAK;AAClD,QAAM,CAACqE,eAAeC,gBAAgB,IAAItE,aAAAA,SAAS,KAAK;AAElD,QAAAuE,eAAgBC,gBAA2B;;AACrC,UAAAC,WAAW,IAAIC,SAAS;AACrBD,aAAAE,OAAO,cAAc,kBAAkB;AAChDF,aAASE,OAAO,UAAUjE,OAAOkE,SAAA,CAAU;AAC3CH,aAASE,OAAO,kBAAgBH,8CAAYK,OAAZL,mBAAgBI,eAAc,EAAE;AAChEH,aAASE,OAAO,YAAYhE,SAASiE,SAAA,CAAU;AAC/Cd,sBAAkBgB,OAAOL,UAAU;AAAA,MAAEM,QAAQ;AAAA,IAAO,CAAC;AAAA,EAC3D;AAEM,QAAAC,6BAA8BC,mBAAgC;;AACxD,UAAAR,WAAW,IAAIC,SAAS;AACrBD,aAAAE,OAAO,cAAc,eAAe;AAC7CF,aAASE,OAAO,UAAUjE,OAAOkE,SAAA,CAAU;AAC3CH,aAASE,OAAO,eAAaM,oDAAeJ,OAAfI,mBAAmBL,eAAc,EAAE;AAChEH,aAASE,OAAO,YAAYhE,SAASiE,SAAA,CAAU;AAC/Cb,uBAAmBe,OAAOL,UAAU;AAAA,MAAEM,QAAQ;AAAA,IAAO,CAAC;AAAA,EAC5D;AAEA9E,eAAAA,UAAU,MAAM;AACN,QAAA8D,mBAAmBzC,UAAU,QAAQ;AACnC,UAAI4C,+CAAegB,eAAe;AAC5BvB,8BAAsBO,+CAAegB,aAAa;AAClDrB,iCAAyB,IAAI;AAC7BS,yBAAiB,KAAK;AAAA,MAC5B,OAAO;AACDX,8BAAsB,CAAA,CAAE;AACxBE,iCAAyB,KAAK;AAC9BS,yBAAiB,KAAK;AAAA,MAE5B;AAAA,IACN;AAAA,EACN,GAAG,CAACJ,aAAa,CAAC;AAElBjE,eAAAA,UAAU,MAAM;AACN,QAAA6D,kBAAkBxC,UAAU,QAAQ;AAClC,UAAI0C,yCAAYmB,wBAAwB;AAClC9B,+BAAuBW,WAAWmB,sBAAsB;AACxDhC,mCAA2B,IAAI;AAC/BiB,sBAAc,KAAK;AAAA,MACzB,OAAO;AACDf,+BAAuB,CAAA,CAAE;AACzBe,sBAAc,KAAK;AACnBjB,mCAA2B,KAAK;AAAA,MACtC;AAAA,IACN;AAAA,EACN,GAAG,CAACa,UAAU,CAAC;AAET,QAAAoB,8BAA+BC,SAAoB;AACnDlC,+BAA2B,IAAI;AAC/BI,yBAAqB8B,GAAG;AACxBjB,kBAAc,IAAI;AAAA,EACxB;AAEM,QAAAkB,4BAA6BD,SAAsB;AACnDxB,6BAAyB,IAAI;AAE7BJ,0BAAsB4B,GAAG;AACzBf,qBAAiB,IAAI;AAAA,EAC3B;AAEA,gDACO,OACM;AAAA,IAAAiB,UAAA,CAAWlE,WAAAnB,kCAAA,IAACsF;MAAcnE;AAAAA,IAAkB,CAAA,GAC7ClB,kCAAA,KAAC,MAAA;AAAA,MACKsF,WAAU;AAAA,MACVC,SAASA,MAAMvE,SAAS,EAAE;AAAA,MAC/BoE,UAAA,CAAA,kBACoB/E,QAAA;AAAA,IAAA,CACrB,0CAECmF,MAAK;AAAA,MAAAC,OAAO7E;AAAAA,MAAW8E,eAAe9C;AAAAA,MAAiB0C,WAAU;AAAA,MAC5DF,UAAA,CAACpF,kCAAA,KAAA2F,UAAA;AAAA,QAASL,WAAU;AAAA,QACdF,UAAA,CAAArF,kCAAA,IAAC6F,aAAA;AAAA,UACKH,OAAM;AAAA,UACNH,WAAU;AAAA,UACfF,UAAA;AAAA,QAAA,CAED,GACArF,kCAAA,IAAC6F,aAAA;AAAA,UACKH,OAAM;AAAA,UACNH,WAAU;AAAA,UACfF,UAAA;AAAA,QAAA,CAED,CAAA;AAAA,MACN,CAAA,GAECpF,kCAAA,KAAA6F,aAAA;AAAA,QAAYJ,OAAM;AAAA,QAAaH,WAAU;AAAA,QACpCF,UAAA,CAACpF,kCAAA,KAAA,OAAA;AAAA,UAAIsF,WAAU;AAAA,UACTF,UAAA,CAACrF,kCAAA,IAAA+F,QAAA;AAAA,YAAOR,WAAU;AAAA,UAA2E,CAAA,GAC7FvF,kCAAA,IAAC,SAAA;AAAA,YACKgG,MAAK;AAAA,YACLC,aAAY;AAAA,YACZP,OAAOrE;AAAAA,YACP6E,UAAWC,OAAM7E,uBAAuB6E,EAAEC,OAAOV,KAAK;AAAA,YACtDH,WAAU;AAAA,WAChB,GACClE,uBACKrB,kCAAA,IAACqG,GAAA;AAAA,YACKd,WAAU;AAAA,YACVC,SAASA,MAAMlE,uBAAuB,EAAE;AAAA,UAAA,CAC9C,CAAA;AAAA,QAEZ,CAAA,GACAtB,kCAAA,IAACsG,iBAAA;AAAA,UACKC,SAASvE;AAAAA,UACTwE,MAAM5E;AAAAA,UACN6E,WAAYtB,SACLlF,kCAAA,KAAA,MAAA;AAAA,YAAgBsF,WAAU;AAAA,YACrBF,UAAA,CAAArF,kCAAA,IAAC,MAAG;AAAA,cAAAuF,WAAU;AAAA,cAA+CF,UAAAF,2BAAKR;AAAAA,YAAG,CAAA,yCACpE,MAAG;AAAA,cAAAY,WAAU;AAAA,cAA6EC,SAASA,MAAMvE,SAAS,uCAAuCkE,2BAAKR,EAAE,kBAAkBQ,IAAI7C,IAAI,aAAa7B,QAAQ,EAAE;AAAA,cAAI4E,qCAAK/C;AAAAA,YAAK,CAAA,yCAC/N,MAAG;AAAA,cAAAiD,WAAU;AAAA,cAA+CF,WAAAF,2BAAK1C,cAAa;AAAA,YAAI,CAAA,GAClFzC,kCAAA,IAAA,MAAA;AAAA,cAAGuF,WAAU;AAAA,cACPF,sCAAKqB,SAAQ,IAAI,KAAKvB,2BAAKuB,MAAMC,QAAQ,EAAE,KAAK;AAAA,YACvD,CAAA,GACC3G,kCAAA,IAAA,MAAA;AAAA,cAAGuF,WAAU;AAAA,cACPF,sCAAKuB,kBAAiB,IAAI,KAAKzB,2BAAKyB,eAAeD,QAAQ,EAAE,KAAK;AAAA,YACzE,CAAA,GACC3G,kCAAA,IAAA,MAAA;AAAA,cAAGuF,WAAU;AAAA,cACPF,sCAAKwB,OAAM,IAAI,GAAG1B,2BAAK0B,IAAIF,QAAQ,EAAE,KAAK;AAAA,YACjD,CAAA,GACA3G,kCAAA,IAAC,MAAG;AAAA,cAAAuF,WAAU;AAAA,cACRF,UAAArF,kCAAA,IAAC8G,QAAA;AAAA,gBACKC,SAAQ;AAAA,gBACRC,MAAK;AAAA,gBACLzB,WAAU;AAAA,gBACVC,SAASA,MAAM;AACL,sBAAAyB,QAAQ,sDAAsD,GAAG;AAC/DnC,+CAA2BK,GAAG;AAAA,kBACpC;AAAA,gBACN;AAAA,gBAEAE,UAAArF,kCAAA,IAACkH,OAAM;AAAA,kBAAAF,MAAM;AAAA,gBAAI,CAAA;AAAA,cACvB,CAAA;AAAA,YACN,CAAA,GACAhH,kCAAA,IAAC,MAAG;AAAA,cAAAuF,WAAU;AAAA,cACRF,UAAArF,kCAAA,IAAC8G,QAAA;AAAA,gBACKC,SAAQ;AAAA,gBACRC,MAAK;AAAA,gBACLzB,WAAU;AAAA,gBACVC,SAASA,MAAMJ,0BAA0BD,GAAG;AAAA,gBAE5CE,UAAArF,kCAAA,IAACmH,QAAO;AAAA,kBAAAH,MAAM;AAAA,gBAAI,CAAA;AAAA,cACxB,CAAA;AAAA,YACN,CAAA,CAAA;AAAA,UAAA,GApCG7B,IAAIR,EAqCb;AAAA,QAAA,CAEZ,GACA1E,kCAAA,KAACmH,MAAK;AAAA,UAAAvC,QAAO;AAAA,UACPQ,UAAA,CAAArF,kCAAA,IAAC;YAAMsC,MAAK;AAAA,YAAWoD,OAAOjF;AAAAA,YAAU4G,QAAM;AAAA,UAAC,CAAA,yCAC9C,SAAM;AAAA,YAAA/E,MAAK;AAAA,YAAUoD,OAAO;AAAA,YAAI2B,QAAM;AAAA,UAAC,CAAA,yCACvC,SAAM;AAAA,YAAA/E,MAAK;AAAA,YAAaoD,OAAO;AAAA,YAAqB2B,QAAM;AAAA,UAAC,CAAA,yCAC3D,SAAM;AAAA,YAAA/E,MAAK;AAAA,YAAWoD,OAAO;AAAA,YAAM2B,QAAM;AAAA,UAAC,CAAA,GAC3CrH,kCAAA,IAAC8G,QAAA;AAAA,YACKvB,WAAU;AAAA,YACVS,MAAK;AAAA,YACVX,UAAA;AAAA,UAAA,CAED,CAAA;AAAA,QACN,CAAA,GACArF,kCAAA,IAACsH,uBAAA;AAAA,UACKC,QAAQ7D;AAAAA,UACR8D,OAAOhE,sBAAsB,CAAC;AAAA,UAC9BiE,SAASA,MAAM9D,yBAAyB,KAAK;AAAA,UAC7C+D,QAAQvD,gBAAgB,wBAAwB;AAAA,UAChD1D;AAAAA,UACAkH,SAASnH;AAAAA,UACToH,WAAWzD,gBAAgBb,sBAAsB;AAAA,UACjDuE,QAAQ1D;AAAAA,QAAA,CACd,CAAA;AAAA,MACN,CAAA,GAEClE,kCAAA,KAAA6F,aAAA;AAAA,QAAYJ,OAAM;AAAA,QAAeH,WAAU;AAAA,QACtCF,UAAA,CAACpF,kCAAA,KAAA,OAAA;AAAA,UAAIsF,WAAU;AAAA,UACTF,UAAA,CAACrF,kCAAA,IAAA+F,QAAA;AAAA,YAAOR,WAAU;AAAA,UAA2E,CAAA,GAC7FvF,kCAAA,IAAC,SAAA;AAAA,YACKgG,MAAK;AAAA,YACLC,aAAY;AAAA,YACZP,OAAOnE;AAAAA,YACP2E,UAAWC,OAAM3E,wBAAwB2E,EAAEC,OAAOV,KAAK;AAAA,YACvDH,WAAU;AAAA,WAChB,GACChE,wBACKvB,kCAAA,IAACqG,GAAA;AAAA,YACKd,WAAU;AAAA,YACVC,SAASA,MAAMhE,wBAAwB,EAAE;AAAA,UAAA,CAC/C,CAAA;AAAA,QAEZ,CAAA,GACAxB,kCAAA,IAACsG,iBAAA;AAAA,UACKC,SAAStE;AAAAA,UACTuE,MAAM1E;AAAAA,UAEN2E,WAAYtB,SACLlF,kCAAA,KAAA,MAAA;AAAA,YAAgBsF,WAAU;AAAA,YACrBF,UAAA,CAAArF,kCAAA,IAAC,MAAG;AAAA,cAAAuF,WAAU;AAAA,cAA+CF,UAAAF,IAAIR;AAAAA,YAAG,CAAA,GACnE3E,kCAAA,IAAA,MAAA;AAAA,cAAGuF,WAAU;AAAA,cAA8EF,cAAI/C;AAAAA,YAAK,CAAA,yCACpG,MAAG;AAAA,cAAAiD,WAAU;AAAA,cAA+CF,UAAAF,IAAIvC,eAAe;AAAA,YAAI,CAAA,GACnF5C,kCAAA,IAAA,MAAA;AAAA,cAAGuF,WAAU;AAAA,cAA+CF,cAAIyC;AAAAA,YAAU,CAAA,GAC1E9H,kCAAA,IAAA,MAAA;AAAA,cAAGuF,WAAU;AAAA,cAA+CF,cAAI0C;AAAAA,YAAU,CAAA,GAkC3E/H,kCAAA,IAAC,MAAG;AAAA,cAAAuF,WAAU;AAAA,cACRF,UAAArF,kCAAA,IAAC8G,QAAA;AAAA,gBACKC,SAAQ;AAAA,gBACRC,MAAK;AAAA,gBACLzB,WAAU;AAAA,gBACVC,SAASA,MAAM;AACL,sBAAAyB,QAAQ,uDAAuD,GAAG;AAChE5C,iCAAac,GAAG;AAAA,kBACtB;AAAA,gBACN;AAAA,gBAEAE,UAAArF,kCAAA,IAACkH,OAAM;AAAA,kBAAAF,MAAM;AAAA,gBAAI,CAAA;AAAA,cACvB,CAAA;AAAA,YACN,CAAA,GACAhH,kCAAA,IAAC,MAAG;AAAA,cAAAuF,WAAU;AAAA,cACRF,UAAArF,kCAAA,IAAC8G,QAAA;AAAA,gBACKC,SAAQ;AAAA,gBACRC,MAAK;AAAA,gBACLzB,WAAU;AAAA,gBACVC,SAASA,MAAMN,4BAA4BC,GAAG;AAAA,gBAE9CE,UAAArF,kCAAA,IAACmH,QAAO;AAAA,kBAAAH,MAAM;AAAA,gBAAI,CAAA;AAAA,cACxB,CAAA;AAAA,YACN,CAAA,CAAA;AAAA,UAAA,GA9DG7B,IAAIR,EA+Db;AAAA,QAAA,CACH,GACT1E,kCAAA,KAACmH,MAAK;AAAA,UAAAvC,QAAO;AAAA,UACPQ,UAAA,CAAArF,kCAAA,IAAC;YAAMsC,MAAK;AAAA,YAAWoD,OAAOjF;AAAAA,YAAU4G,QAAM;AAAA,UAAC,CAAA,yCAC9C,SAAM;AAAA,YAAA/E,MAAK;AAAA,YAAUoD,OAAO;AAAA,YAAI2B,QAAM;AAAA,UAAC,CAAA,yCACvC,SAAM;AAAA,YAAA/E,MAAK;AAAA,YAAaoD,OAAO;AAAA,YAAqB2B,QAAM;AAAA,UAAC,CAAA,yCAC3D,SAAM;AAAA,YAAA/E,MAAK;AAAA,YAAWoD,OAAO;AAAA,YAAM2B,QAAM;AAAA,UAAC,CAAA,GAC3CrH,kCAAA,IAAC8G,QAAA;AAAA,YACKvB,WAAU;AAAA,YACVS,MAAK;AAAA,YACVX,UAAA;AAAA,UAAA,CAED,CAAA;AAAA,QACN,CAAA,GACArF,kCAAA,IAACgI,oBAAA;AAAA,UACKT,QAAQvE;AAAAA,UACRwE,OAAOtE,uBAAuB,CAAC;AAAA,UAC/BuE,SAASA,MAAMxE,2BAA2B,KAAK;AAAA,UAC/CyE,QAAQzD,aAAa,kBAAkB3D,qCAAU2H,MAAM,GAAG,GAAG,KAAK,oBAAoB3H,qCAAU2H,MAAM,GAAG,GAAG;AAAA,UAC5GL,WAAW3D,aAAab,oBAAoB;AAAA,UAC5C3C;AAAAA,UACAkH,SAASnH;AAAAA,UACTqH,QAAQ5D;AAAAA,QAAA,CAEd,CAAA;AAAA,MACN,CAAA,CAAA;AAAA,IACN,CAAA,CAAA;AAAA,EAAA,GA7NIpD,SA8NV;AAEZ;"}