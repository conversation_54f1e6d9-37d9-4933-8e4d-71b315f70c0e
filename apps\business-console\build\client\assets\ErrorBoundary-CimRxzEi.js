import { r as reactExports, R as React, a as React$1, j as jsxRuntimeExports } from "./jsx-runtime-bB2y7OuD.js";
import { B as Button } from "./button-ByAXMyvk.js";
import { a as useNavigate, i as useRouteError, j as isRouteErrorResponse } from "./index-DhHTcibu.js";
import { n as n$1, s as s$1, K, y, A, o as o$1, m, t as t$1, L as L$1, O, a as s$2, u as u$1 } from "./use-sync-refs-DLXpJTw-.js";
function t(e) {
  typeof queueMicrotask == "function" ? queueMicrotask(e) : Promise.resolve().then(e).catch((o2) => setTimeout(() => {
    throw o2;
  }));
}
function o() {
  let n2 = [], r = { addEventListener(e, t2, s2, a) {
    return e.addEventListener(t2, s2, a), r.add(() => e.removeEventListener(t2, s2, a));
  }, requestAnimationFrame(...e) {
    let t2 = requestAnimationFrame(...e);
    return r.add(() => cancelAnimationFrame(t2));
  }, nextFrame(...e) {
    return r.requestAnimationFrame(() => r.requestAnimationFrame(...e));
  }, setTimeout(...e) {
    let t2 = setTimeout(...e);
    return r.add(() => clearTimeout(t2));
  }, microTask(...e) {
    let t$12 = { current: true };
    return t(() => {
      t$12.current && e[0]();
    }), r.add(() => {
      t$12.current = false;
    });
  }, style(e, t2, s2) {
    let a = e.style.getPropertyValue(t2);
    return Object.assign(e.style, { [t2]: s2 }), this.add(() => {
      Object.assign(e.style, { [t2]: a });
    });
  }, group(e) {
    let t2 = o();
    return e(t2), this.add(() => t2.dispose());
  }, add(e) {
    return n2.includes(e) || n2.push(e), () => {
      let t2 = n2.indexOf(e);
      if (t2 >= 0) for (let s2 of n2.splice(t2, 1)) s2();
    };
  }, dispose() {
    for (let e of n2.splice(0)) e();
  } };
  return r;
}
function p() {
  let [e] = reactExports.useState(o);
  return reactExports.useEffect(() => () => e.dispose(), [e]), e;
}
function c$1(u2 = 0) {
  let [t2, l2] = reactExports.useState(u2), g = reactExports.useCallback((e) => l2(e), [t2]), s2 = reactExports.useCallback((e) => l2((a) => a | e), [t2]), m2 = reactExports.useCallback((e) => (t2 & e) === e, [t2]), n2 = reactExports.useCallback((e) => l2((a) => a & ~e), [l2]), F = reactExports.useCallback((e) => l2((a) => a ^ e), [l2]);
  return { flags: t2, setFlag: g, addFlag: s2, hasFlag: m2, removeFlag: n2, toggleFlag: F };
}
var define_process_env_default = {};
var T, b;
typeof process != "undefined" && typeof globalThis != "undefined" && typeof Element != "undefined" && ((T = process == null ? void 0 : define_process_env_default) == null ? void 0 : T["NODE_ENV"]) === "test" && typeof ((b = Element == null ? void 0 : Element.prototype) == null ? void 0 : b.getAnimations) == "undefined" && (Element.prototype.getAnimations = function() {
  return console.warn(["Headless UI has polyfilled `Element.prototype.getAnimations` for your tests.", "Please install a proper polyfill e.g. `jsdom-testing-mocks`, to silence these warnings.", "", "Example usage:", "```js", "import { mockAnimationsApi } from 'jsdom-testing-mocks'", "mockAnimationsApi()", "```"].join(`
`)), [];
});
var L = ((r) => (r[r.None = 0] = "None", r[r.Closed = 1] = "Closed", r[r.Enter = 2] = "Enter", r[r.Leave = 4] = "Leave", r))(L || {});
function R(t2) {
  let n2 = {};
  for (let e in t2) t2[e] === true && (n2[`data-${e}`] = "");
  return n2;
}
function x(t2, n2, e, i2) {
  let [r, o2] = reactExports.useState(e), { hasFlag: s2, addFlag: a, removeFlag: l2 } = c$1(t2 && r ? 3 : 0), u2 = reactExports.useRef(false), f2 = reactExports.useRef(false), E = p();
  return n$1(() => {
    var d;
    if (t2) {
      if (e && o2(true), !n2) {
        e && a(3);
        return;
      }
      return (d = i2 == null ? void 0 : i2.start) == null || d.call(i2, e), C(n2, { inFlight: u2, prepare() {
        f2.current ? f2.current = false : f2.current = u2.current, u2.current = true, !f2.current && (e ? (a(3), l2(4)) : (a(4), l2(2)));
      }, run() {
        f2.current ? e ? (l2(3), a(4)) : (l2(4), a(3)) : e ? l2(1) : a(1);
      }, done() {
        var p2;
        f2.current && typeof n2.getAnimations == "function" && n2.getAnimations().length > 0 || (u2.current = false, l2(7), e || o2(false), (p2 = i2 == null ? void 0 : i2.end) == null || p2.call(i2, e));
      } });
    }
  }, [t2, e, n2, E]), t2 ? [r, { closed: s2(1), enter: s2(2), leave: s2(4), transition: s2(2) || s2(4) }] : [e, { closed: void 0, enter: void 0, leave: void 0, transition: void 0 }];
}
function C(t2, { prepare: n2, run: e, done: i2, inFlight: r }) {
  let o$12 = o();
  return j(t2, { prepare: n2, inFlight: r }), o$12.nextFrame(() => {
    e(), o$12.requestAnimationFrame(() => {
      o$12.add(M$1(t2, i2));
    });
  }), o$12.dispose;
}
function M$1(t2, n2) {
  var o$12, s2;
  let e = o();
  if (!t2) return e.dispose;
  let i2 = false;
  e.add(() => {
    i2 = true;
  });
  let r = (s2 = (o$12 = t2.getAnimations) == null ? void 0 : o$12.call(t2).filter((a) => a instanceof CSSTransition)) != null ? s2 : [];
  return r.length === 0 ? (n2(), e.dispose) : (Promise.allSettled(r.map((a) => a.finished)).then(() => {
    i2 || n2();
  }), e.dispose);
}
function j(t2, { inFlight: n2, prepare: e }) {
  if (n2 != null && n2.current) {
    e();
    return;
  }
  let i2 = t2.style.transition;
  t2.style.transition = "none", e(), t2.offsetHeight, t2.style.transition = i2;
}
let n = reactExports.createContext(null);
n.displayName = "OpenClosedContext";
var i = ((e) => (e[e.Open = 1] = "Open", e[e.Closed = 2] = "Closed", e[e.Closing = 4] = "Closing", e[e.Opening = 8] = "Opening", e))(i || {});
function u() {
  return reactExports.useContext(n);
}
function c({ value: o2, children: t2 }) {
  return React.createElement(n.Provider, { value: o2 }, t2);
}
function s() {
  let r = typeof document == "undefined";
  return "useSyncExternalStore" in React$1 ? ((o2) => o2.useSyncExternalStore)(React$1)(() => () => {
  }, () => false, () => !r) : false;
}
function l() {
  let r = s(), [e, n2] = reactExports.useState(s$1.isHandoffComplete);
  return e && s$1.isHandoffComplete === false && n2(false), reactExports.useEffect(() => {
    e !== true && n2(true);
  }, [e]), reactExports.useEffect(() => s$1.handoff(), []), r ? false : e;
}
function f() {
  let e = reactExports.useRef(false);
  return n$1(() => (e.current = true, () => {
    e.current = false;
  }), []), e;
}
function ue(e) {
  var t2;
  return !!(e.enter || e.enterFrom || e.enterTo || e.leave || e.leaveFrom || e.leaveTo) || ((t2 = e.as) != null ? t2 : de) !== reactExports.Fragment || React.Children.count(e.children) === 1;
}
let w = reactExports.createContext(null);
w.displayName = "TransitionContext";
var _e = ((n2) => (n2.Visible = "visible", n2.Hidden = "hidden", n2))(_e || {});
function De() {
  let e = reactExports.useContext(w);
  if (e === null) throw new Error("A <Transition.Child /> is used but it is missing a parent <Transition /> or <Transition.Root />.");
  return e;
}
function He() {
  let e = reactExports.useContext(M);
  if (e === null) throw new Error("A <Transition.Child /> is used but it is missing a parent <Transition /> or <Transition.Root />.");
  return e;
}
let M = reactExports.createContext(null);
M.displayName = "NestingContext";
function U(e) {
  return "children" in e ? U(e.children) : e.current.filter(({ el: t2 }) => t2.current !== null).filter(({ state: t2 }) => t2 === "visible").length > 0;
}
function Te(e, t2) {
  let n2 = s$2(e), l2 = reactExports.useRef([]), S = f(), R2 = p(), d = o$1((o2, i2 = A.Hidden) => {
    let a = l2.current.findIndex(({ el: s2 }) => s2 === o2);
    a !== -1 && (u$1(i2, { [A.Unmount]() {
      l2.current.splice(a, 1);
    }, [A.Hidden]() {
      l2.current[a].state = "hidden";
    } }), R2.microTask(() => {
      var s2;
      !U(l2) && S.current && ((s2 = n2.current) == null || s2.call(n2));
    }));
  }), y2 = o$1((o2) => {
    let i2 = l2.current.find(({ el: a }) => a === o2);
    return i2 ? i2.state !== "visible" && (i2.state = "visible") : l2.current.push({ el: o2, state: "visible" }), () => d(o2, A.Unmount);
  }), p$1 = reactExports.useRef([]), c2 = reactExports.useRef(Promise.resolve()), C2 = reactExports.useRef({ enter: [], leave: [] }), h = o$1((o2, i2, a) => {
    p$1.current.splice(0), t2 && (t2.chains.current[i2] = t2.chains.current[i2].filter(([s2]) => s2 !== o2)), t2 == null || t2.chains.current[i2].push([o2, new Promise((s2) => {
      p$1.current.push(s2);
    })]), t2 == null || t2.chains.current[i2].push([o2, new Promise((s2) => {
      Promise.all(C2.current[i2].map(([r, f2]) => f2)).then(() => s2());
    })]), i2 === "enter" ? c2.current = c2.current.then(() => t2 == null ? void 0 : t2.wait.current).then(() => a(i2)) : a(i2);
  }), g = o$1((o2, i2, a) => {
    Promise.all(C2.current[i2].splice(0).map(([s2, r]) => r)).then(() => {
      var s2;
      (s2 = p$1.current.shift()) == null || s2();
    }).then(() => a(i2));
  });
  return reactExports.useMemo(() => ({ children: l2, register: y2, unregister: d, onStart: h, onStop: g, wait: c2, chains: C2 }), [y2, d, l2, h, g, C2, c2]);
}
let de = reactExports.Fragment, fe = O.RenderStrategy;
function Ae(e, t2) {
  var ee, te;
  let { transition: n2 = true, beforeEnter: l$1, afterEnter: S, beforeLeave: R$1, afterLeave: d, enter: y$1, enterFrom: p2, enterTo: c$12, entered: C2, leave: h, leaveFrom: g, leaveTo: o2, ...i$1 } = e, [a, s2] = reactExports.useState(null), r = reactExports.useRef(null), f2 = ue(e), j2 = y(...f2 ? [r, t2, s2] : t2 === null ? [] : [t2]), H = (ee = i$1.unmount) == null || ee ? A.Unmount : A.Hidden, { show: u2, appear: z, initial: K2 } = De(), [v, G] = reactExports.useState(u2 ? "visible" : "hidden"), Q = He(), { register: A$1, unregister: I } = Q;
  n$1(() => A$1(r), [A$1, r]), n$1(() => {
    if (H === A.Hidden && r.current) {
      if (u2 && v !== "visible") {
        G("visible");
        return;
      }
      return u$1(v, { ["hidden"]: () => I(r), ["visible"]: () => A$1(r) });
    }
  }, [v, r, A$1, I, u2, H]);
  let B = l();
  n$1(() => {
    if (f2 && B && v === "visible" && r.current === null) throw new Error("Did you forget to passthrough the `ref` to the actual DOM node?");
  }, [r, v, B, f2]);
  let ce = K2 && !z, Y = z && u2 && K2, W = reactExports.useRef(false), L2 = Te(() => {
    W.current || (G("hidden"), I(r));
  }, Q), Z = o$1((k) => {
    W.current = true;
    let F = k ? "enter" : "leave";
    L2.onStart(r, F, (_) => {
      _ === "enter" ? l$1 == null || l$1() : _ === "leave" && (R$1 == null || R$1());
    });
  }), $ = o$1((k) => {
    let F = k ? "enter" : "leave";
    W.current = false, L2.onStop(r, F, (_) => {
      _ === "enter" ? S == null || S() : _ === "leave" && (d == null || d());
    }), F === "leave" && !U(L2) && (G("hidden"), I(r));
  });
  reactExports.useEffect(() => {
    f2 && n2 || (Z(u2), $(u2));
  }, [u2, f2, n2]);
  let pe = /* @__PURE__ */ (() => !(!n2 || !f2 || !B || ce))(), [, T2] = x(pe, a, u2, { start: Z, end: $ }), Ce = m({ ref: j2, className: ((te = t$1(i$1.className, Y && y$1, Y && p2, T2.enter && y$1, T2.enter && T2.closed && p2, T2.enter && !T2.closed && c$12, T2.leave && h, T2.leave && !T2.closed && g, T2.leave && T2.closed && o2, !T2.transition && u2 && C2)) == null ? void 0 : te.trim()) || void 0, ...R(T2) }), N = 0;
  v === "visible" && (N |= i.Open), v === "hidden" && (N |= i.Closed), T2.enter && (N |= i.Opening), T2.leave && (N |= i.Closing);
  let he = L$1();
  return React.createElement(M.Provider, { value: L2 }, React.createElement(c, { value: N }, he({ ourProps: Ce, theirProps: i$1, defaultTag: de, features: fe, visible: v === "visible", name: "Transition.Child" })));
}
function Ie(e, t2) {
  let { show: n2, appear: l$1 = false, unmount: S = true, ...R2 } = e, d = reactExports.useRef(null), y$1 = ue(e), p2 = y(...y$1 ? [d, t2] : t2 === null ? [] : [t2]);
  l();
  let c2 = u();
  if (n2 === void 0 && c2 !== null && (n2 = (c2 & i.Open) === i.Open), n2 === void 0) throw new Error("A <Transition /> is used but it is missing a `show={true | false}` prop.");
  let [C2, h] = reactExports.useState(n2 ? "visible" : "hidden"), g = Te(() => {
    n2 || h("hidden");
  }), [o2, i$1] = reactExports.useState(true), a = reactExports.useRef([n2]);
  n$1(() => {
    o2 !== false && a.current[a.current.length - 1] !== n2 && (a.current.push(n2), i$1(false));
  }, [a, n2]);
  let s2 = reactExports.useMemo(() => ({ show: n2, appear: l$1, initial: o2 }), [n2, l$1, o2]);
  n$1(() => {
    n2 ? h("visible") : !U(g) && d.current !== null && h("hidden");
  }, [n2, g]);
  let r = { unmount: S }, f2 = o$1(() => {
    var u2;
    o2 && i$1(false), (u2 = e.beforeEnter) == null || u2.call(e);
  }), j2 = o$1(() => {
    var u2;
    o2 && i$1(false), (u2 = e.beforeLeave) == null || u2.call(e);
  }), H = L$1();
  return React.createElement(M.Provider, { value: g }, React.createElement(w.Provider, { value: s2 }, H({ ourProps: { ...r, as: reactExports.Fragment, children: React.createElement(me, { ref: p2, ...r, ...R2, beforeEnter: f2, beforeLeave: j2 }) }, theirProps: {}, defaultTag: reactExports.Fragment, features: fe, visible: C2 === "visible", name: "Transition" })));
}
function Le(e, t2) {
  let n2 = reactExports.useContext(w) !== null, l2 = u() !== null;
  return React.createElement(React.Fragment, null, !n2 && l2 ? React.createElement(X, { ref: t2, ...e }) : React.createElement(me, { ref: t2, ...e }));
}
let X = K(Ie), me = K(Ae), Fe = K(Le), ze = Object.assign(X, { Child: Fe, Root: X });
const SuccessDialog = ({
  title,
  message,
  buttonText,
  buttonType = "primary",
  onClose,
  onRedirect,
  countdownStart = 5
}) => {
  const navigate = useNavigate();
  const [countdown, setCountdown] = reactExports.useState(countdownStart);
  const [isVisible, setIsVisible] = reactExports.useState(false);
  reactExports.useEffect(() => {
    setIsVisible(true);
  }, []);
  reactExports.useEffect(() => {
    let timer = null;
    if (onRedirect) {
      if (countdown <= 0) {
        console.log(countdown);
        onRedirect();
        return;
      }
      timer = setInterval(() => {
        setCountdown((prev) => prev - 1);
      }, 1e3);
    }
    if (timer) {
      return () => clearInterval(timer);
    }
  }, [countdown, onRedirect]);
  const handleClose = () => {
    if (onClose) {
      onClose();
    } else if (onRedirect) {
      onRedirect();
    } else {
      navigate(-1);
    }
  };
  return /* @__PURE__ */ jsxRuntimeExports.jsx(
    ze,
    {
      show: isVisible,
      enter: "transition-opacity duration-300",
      enterFrom: "opacity-0",
      enterTo: "opacity-100",
      leave: "transition-opacity duration-200",
      leaveFrom: "opacity-100",
      leaveTo: "opacity-0",
      children: /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "fixed inset-0 flex items-center justify-center bg-white z-50", children: /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "bg-white rounded-lg max-w-md w-full transform transition-all", children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex flex-col items-center", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "", children: /* @__PURE__ */ jsxRuntimeExports.jsx("img", { src: "/error_1.svg", alt: "Error", className: "mb-8 w-60 mr-8" }) }),
        title && /* @__PURE__ */ jsxRuntimeExports.jsx("h2", { className: "text-xl font-medium text-gray-700 mb-2", children: title }),
        message && /* @__PURE__ */ jsxRuntimeExports.jsx("p", { className: "text-md mb-4 text-center font-light text-gray-400 max-w-72", children: message }),
        buttonText && /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "w-full px-8 mt-4", children: buttonType === "primary" ? /* @__PURE__ */ jsxRuntimeExports.jsx(Button, { onClick: handleClose, className: `w-full`, children: buttonText }) : /* @__PURE__ */ jsxRuntimeExports.jsx(Button, { onClick: handleClose, className: `w-full`, children: buttonText }) }),
        onRedirect && /* @__PURE__ */ jsxRuntimeExports.jsxs("p", { className: "text-sm text-gray-500 mt-6", children: [
          "Redirecting in ",
          countdown,
          " second",
          countdown !== 1 ? "s" : "",
          "..."
        ] })
      ] }) }) })
    }
  );
};
function ErrorBoundary({
  title,
  message,
  onClose,
  onRetry
}) {
  const error = useRouteError();
  if (isRouteErrorResponse(error)) {
    return /* @__PURE__ */ jsxRuntimeExports.jsx("div", { children: /* @__PURE__ */ jsxRuntimeExports.jsx(
      SuccessDialog,
      {
        title: title || "Oops",
        message: message || error.statusText || "Something went wrong!",
        onClose: onRetry,
        buttonType: "primary",
        buttonText: "Retry"
      }
    ) });
  } else if (error instanceof Error) {
    return /* @__PURE__ */ jsxRuntimeExports.jsx(
      SuccessDialog,
      {
        title: title || "Sorry, something Went Wrong!",
        message: message || "We’re working on getting this fixed as soon as we can.",
        onClose,
        buttonType: "secondary",
        buttonText: "Close"
      }
    );
  } else {
    return /* @__PURE__ */ jsxRuntimeExports.jsx(SuccessDialog, { title: "Sorry", message: "Unknown Error" });
  }
}
export {
  ErrorBoundary as E,
  SuccessDialog as S
};
//# sourceMappingURL=ErrorBoundary-CimRxzEi.js.map
