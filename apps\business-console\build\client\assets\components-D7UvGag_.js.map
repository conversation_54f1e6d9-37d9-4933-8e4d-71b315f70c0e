{"version": 3, "file": "components-D7UvGag_.js", "sources": ["../../../node_modules/react-router-dom/dist/index.js", "../../../node_modules/turbo-stream/dist/turbo-stream.mjs", "../../../node_modules/@remix-run/server-runtime/dist/esm/single-fetch.js", "../../../node_modules/@remix-run/react/dist/esm/_virtual/_rollupPluginBabelHelpers.js", "../../../node_modules/@remix-run/react/dist/esm/invariant.js", "../../../node_modules/@remix-run/react/dist/esm/routeModules.js", "../../../node_modules/@remix-run/react/dist/esm/links.js", "../../../node_modules/@remix-run/react/dist/esm/markup.js", "../../../node_modules/@remix-run/react/dist/esm/data.js", "../../../node_modules/@remix-run/react/dist/esm/single-fetch.js", "../../../node_modules/@remix-run/react/dist/esm/errorBoundaries.js", "../../../node_modules/@remix-run/react/dist/esm/fallback.js", "../../../node_modules/@remix-run/react/dist/esm/routes.js", "../../../node_modules/@remix-run/react/dist/esm/fog-of-war.js", "../../../node_modules/@remix-run/react/dist/esm/components.js"], "sourcesContent": ["/**\n * React Router DOM v6.29.0\n *\n * Copyright (c) Remix Software Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE.md file in the root directory of this source tree.\n *\n * @license MIT\n */\nimport * as React from 'react';\nimport * as ReactDOM from 'react-dom';\nimport { UNSAFE_mapRouteProperties, UNSAFE_logV6DeprecationWarnings, UNSAFE_DataRouterContext, UNSAFE_DataRouterStateContext, Router, UNSAFE_useRoutesImpl, UNSAFE_NavigationContext, useHref, useResolvedPath, useLocation, useNavigate, createPath, UNSAFE_useRouteId, UNSAFE_RouteContext, useMatches, useNavigation, useBlocker } from 'react-router';\nexport { AbortedDeferredError, Await, MemoryRouter, Navigate, NavigationType, Outlet, Route, Router, Routes, UNSAFE_DataRouterContext, UNSAFE_DataRouterStateContext, UNSAF<PERSON>_LocationContext, UNSAFE_NavigationContext, UNSAFE_RouteContext, UNSAFE_useRouteId, createMemoryRouter, createPath, createRoutesFromChildren, createRoutesFromElements, defer, generatePath, isRouteErrorResponse, json, matchPath, matchRoutes, parsePath, redirect, redirectDocument, renderMatches, replace, resolvePath, useActionData, useAsyncError, useAsyncValue, useBlocker, useHref, useInRouterContext, useLoaderData, useLocation, useMatch, useMatches, useNavigate, useNavigation, useNavigationType, useOutlet, useOutletContext, useParams, useResolvedPath, useRevalidator, useRouteError, useRouteLoaderData, useRoutes } from 'react-router';\nimport { stripBasename, UNSAFE_warning, createRouter, createBrowserHistory, createHashHistory, UNSAFE_ErrorResponseImpl, UNSAFE_invariant, joinPaths, IDLE_FETCHER, matchPath } from '@remix-run/router';\nexport { UNSAFE_ErrorResponseImpl } from '@remix-run/router';\n\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  var sourceKeys = Object.keys(source);\n  var key, i;\n  for (i = 0; i < sourceKeys.length; i++) {\n    key = sourceKeys[i];\n    if (excluded.indexOf(key) >= 0) continue;\n    target[key] = source[key];\n  }\n  return target;\n}\n\nconst defaultMethod = \"get\";\nconst defaultEncType = \"application/x-www-form-urlencoded\";\nfunction isHtmlElement(object) {\n  return object != null && typeof object.tagName === \"string\";\n}\nfunction isButtonElement(object) {\n  return isHtmlElement(object) && object.tagName.toLowerCase() === \"button\";\n}\nfunction isFormElement(object) {\n  return isHtmlElement(object) && object.tagName.toLowerCase() === \"form\";\n}\nfunction isInputElement(object) {\n  return isHtmlElement(object) && object.tagName.toLowerCase() === \"input\";\n}\nfunction isModifiedEvent(event) {\n  return !!(event.metaKey || event.altKey || event.ctrlKey || event.shiftKey);\n}\nfunction shouldProcessLinkClick(event, target) {\n  return event.button === 0 && (\n  // Ignore everything but left clicks\n  !target || target === \"_self\") &&\n  // Let browser handle \"target=_blank\" etc.\n  !isModifiedEvent(event) // Ignore clicks with modifier keys\n  ;\n}\n/**\n * Creates a URLSearchParams object using the given initializer.\n *\n * This is identical to `new URLSearchParams(init)` except it also\n * supports arrays as values in the object form of the initializer\n * instead of just strings. This is convenient when you need multiple\n * values for a given key, but don't want to use an array initializer.\n *\n * For example, instead of:\n *\n *   let searchParams = new URLSearchParams([\n *     ['sort', 'name'],\n *     ['sort', 'price']\n *   ]);\n *\n * you can do:\n *\n *   let searchParams = createSearchParams({\n *     sort: ['name', 'price']\n *   });\n */\nfunction createSearchParams(init) {\n  if (init === void 0) {\n    init = \"\";\n  }\n  return new URLSearchParams(typeof init === \"string\" || Array.isArray(init) || init instanceof URLSearchParams ? init : Object.keys(init).reduce((memo, key) => {\n    let value = init[key];\n    return memo.concat(Array.isArray(value) ? value.map(v => [key, v]) : [[key, value]]);\n  }, []));\n}\nfunction getSearchParamsForLocation(locationSearch, defaultSearchParams) {\n  let searchParams = createSearchParams(locationSearch);\n  if (defaultSearchParams) {\n    // Use `defaultSearchParams.forEach(...)` here instead of iterating of\n    // `defaultSearchParams.keys()` to work-around a bug in Firefox related to\n    // web extensions. Relevant Bugzilla tickets:\n    // https://bugzilla.mozilla.org/show_bug.cgi?id=1414602\n    // https://bugzilla.mozilla.org/show_bug.cgi?id=1023984\n    defaultSearchParams.forEach((_, key) => {\n      if (!searchParams.has(key)) {\n        defaultSearchParams.getAll(key).forEach(value => {\n          searchParams.append(key, value);\n        });\n      }\n    });\n  }\n  return searchParams;\n}\n// One-time check for submitter support\nlet _formDataSupportsSubmitter = null;\nfunction isFormDataSubmitterSupported() {\n  if (_formDataSupportsSubmitter === null) {\n    try {\n      new FormData(document.createElement(\"form\"),\n      // @ts-expect-error if FormData supports the submitter parameter, this will throw\n      0);\n      _formDataSupportsSubmitter = false;\n    } catch (e) {\n      _formDataSupportsSubmitter = true;\n    }\n  }\n  return _formDataSupportsSubmitter;\n}\nconst supportedFormEncTypes = new Set([\"application/x-www-form-urlencoded\", \"multipart/form-data\", \"text/plain\"]);\nfunction getFormEncType(encType) {\n  if (encType != null && !supportedFormEncTypes.has(encType)) {\n    process.env.NODE_ENV !== \"production\" ? UNSAFE_warning(false, \"\\\"\" + encType + \"\\\" is not a valid `encType` for `<Form>`/`<fetcher.Form>` \" + (\"and will default to \\\"\" + defaultEncType + \"\\\"\")) : void 0;\n    return null;\n  }\n  return encType;\n}\nfunction getFormSubmissionInfo(target, basename) {\n  let method;\n  let action;\n  let encType;\n  let formData;\n  let body;\n  if (isFormElement(target)) {\n    // When grabbing the action from the element, it will have had the basename\n    // prefixed to ensure non-JS scenarios work, so strip it since we'll\n    // re-prefix in the router\n    let attr = target.getAttribute(\"action\");\n    action = attr ? stripBasename(attr, basename) : null;\n    method = target.getAttribute(\"method\") || defaultMethod;\n    encType = getFormEncType(target.getAttribute(\"enctype\")) || defaultEncType;\n    formData = new FormData(target);\n  } else if (isButtonElement(target) || isInputElement(target) && (target.type === \"submit\" || target.type === \"image\")) {\n    let form = target.form;\n    if (form == null) {\n      throw new Error(\"Cannot submit a <button> or <input type=\\\"submit\\\"> without a <form>\");\n    }\n    // <button>/<input type=\"submit\"> may override attributes of <form>\n    // When grabbing the action from the element, it will have had the basename\n    // prefixed to ensure non-JS scenarios work, so strip it since we'll\n    // re-prefix in the router\n    let attr = target.getAttribute(\"formaction\") || form.getAttribute(\"action\");\n    action = attr ? stripBasename(attr, basename) : null;\n    method = target.getAttribute(\"formmethod\") || form.getAttribute(\"method\") || defaultMethod;\n    encType = getFormEncType(target.getAttribute(\"formenctype\")) || getFormEncType(form.getAttribute(\"enctype\")) || defaultEncType;\n    // Build a FormData object populated from a form and submitter\n    formData = new FormData(form, target);\n    // If this browser doesn't support the `FormData(el, submitter)` format,\n    // then tack on the submitter value at the end.  This is a lightweight\n    // solution that is not 100% spec compliant.  For complete support in older\n    // browsers, consider using the `formdata-submitter-polyfill` package\n    if (!isFormDataSubmitterSupported()) {\n      let {\n        name,\n        type,\n        value\n      } = target;\n      if (type === \"image\") {\n        let prefix = name ? name + \".\" : \"\";\n        formData.append(prefix + \"x\", \"0\");\n        formData.append(prefix + \"y\", \"0\");\n      } else if (name) {\n        formData.append(name, value);\n      }\n    }\n  } else if (isHtmlElement(target)) {\n    throw new Error(\"Cannot submit element that is not <form>, <button>, or \" + \"<input type=\\\"submit|image\\\">\");\n  } else {\n    method = defaultMethod;\n    action = null;\n    encType = defaultEncType;\n    body = target;\n  }\n  // Send body for <Form encType=\"text/plain\" so we encode it into text\n  if (formData && encType === \"text/plain\") {\n    body = formData;\n    formData = undefined;\n  }\n  return {\n    action,\n    method: method.toLowerCase(),\n    encType,\n    formData,\n    body\n  };\n}\n\nconst _excluded = [\"onClick\", \"relative\", \"reloadDocument\", \"replace\", \"state\", \"target\", \"to\", \"preventScrollReset\", \"viewTransition\"],\n  _excluded2 = [\"aria-current\", \"caseSensitive\", \"className\", \"end\", \"style\", \"to\", \"viewTransition\", \"children\"],\n  _excluded3 = [\"fetcherKey\", \"navigate\", \"reloadDocument\", \"replace\", \"state\", \"method\", \"action\", \"onSubmit\", \"relative\", \"preventScrollReset\", \"viewTransition\"];\n// HEY YOU! DON'T TOUCH THIS VARIABLE!\n//\n// It is replaced with the proper version at build time via a babel plugin in\n// the rollup config.\n//\n// Export a global property onto the window for React Router detection by the\n// Core Web Vitals Technology Report.  This way they can configure the `wappalyzer`\n// to detect and properly classify live websites as being built with React Router:\n// https://github.com/HTTPArchive/wappalyzer/blob/main/src/technologies/r.json\nconst REACT_ROUTER_VERSION = \"6\";\ntry {\n  window.__reactRouterVersion = REACT_ROUTER_VERSION;\n} catch (e) {\n  // no-op\n}\nfunction createBrowserRouter(routes, opts) {\n  return createRouter({\n    basename: opts == null ? void 0 : opts.basename,\n    future: _extends({}, opts == null ? void 0 : opts.future, {\n      v7_prependBasename: true\n    }),\n    history: createBrowserHistory({\n      window: opts == null ? void 0 : opts.window\n    }),\n    hydrationData: (opts == null ? void 0 : opts.hydrationData) || parseHydrationData(),\n    routes,\n    mapRouteProperties: UNSAFE_mapRouteProperties,\n    dataStrategy: opts == null ? void 0 : opts.dataStrategy,\n    patchRoutesOnNavigation: opts == null ? void 0 : opts.patchRoutesOnNavigation,\n    window: opts == null ? void 0 : opts.window\n  }).initialize();\n}\nfunction createHashRouter(routes, opts) {\n  return createRouter({\n    basename: opts == null ? void 0 : opts.basename,\n    future: _extends({}, opts == null ? void 0 : opts.future, {\n      v7_prependBasename: true\n    }),\n    history: createHashHistory({\n      window: opts == null ? void 0 : opts.window\n    }),\n    hydrationData: (opts == null ? void 0 : opts.hydrationData) || parseHydrationData(),\n    routes,\n    mapRouteProperties: UNSAFE_mapRouteProperties,\n    dataStrategy: opts == null ? void 0 : opts.dataStrategy,\n    patchRoutesOnNavigation: opts == null ? void 0 : opts.patchRoutesOnNavigation,\n    window: opts == null ? void 0 : opts.window\n  }).initialize();\n}\nfunction parseHydrationData() {\n  var _window;\n  let state = (_window = window) == null ? void 0 : _window.__staticRouterHydrationData;\n  if (state && state.errors) {\n    state = _extends({}, state, {\n      errors: deserializeErrors(state.errors)\n    });\n  }\n  return state;\n}\nfunction deserializeErrors(errors) {\n  if (!errors) return null;\n  let entries = Object.entries(errors);\n  let serialized = {};\n  for (let [key, val] of entries) {\n    // Hey you!  If you change this, please change the corresponding logic in\n    // serializeErrors in react-router-dom/server.tsx :)\n    if (val && val.__type === \"RouteErrorResponse\") {\n      serialized[key] = new UNSAFE_ErrorResponseImpl(val.status, val.statusText, val.data, val.internal === true);\n    } else if (val && val.__type === \"Error\") {\n      // Attempt to reconstruct the right type of Error (i.e., ReferenceError)\n      if (val.__subType) {\n        let ErrorConstructor = window[val.__subType];\n        if (typeof ErrorConstructor === \"function\") {\n          try {\n            // @ts-expect-error\n            let error = new ErrorConstructor(val.message);\n            // Wipe away the client-side stack trace.  Nothing to fill it in with\n            // because we don't serialize SSR stack traces for security reasons\n            error.stack = \"\";\n            serialized[key] = error;\n          } catch (e) {\n            // no-op - fall through and create a normal Error\n          }\n        }\n      }\n      if (serialized[key] == null) {\n        let error = new Error(val.message);\n        // Wipe away the client-side stack trace.  Nothing to fill it in with\n        // because we don't serialize SSR stack traces for security reasons\n        error.stack = \"\";\n        serialized[key] = error;\n      }\n    } else {\n      serialized[key] = val;\n    }\n  }\n  return serialized;\n}\nconst ViewTransitionContext = /*#__PURE__*/React.createContext({\n  isTransitioning: false\n});\nif (process.env.NODE_ENV !== \"production\") {\n  ViewTransitionContext.displayName = \"ViewTransition\";\n}\nconst FetchersContext = /*#__PURE__*/React.createContext(new Map());\nif (process.env.NODE_ENV !== \"production\") {\n  FetchersContext.displayName = \"Fetchers\";\n}\n//#endregion\n////////////////////////////////////////////////////////////////////////////////\n//#region Components\n////////////////////////////////////////////////////////////////////////////////\n/**\n  Webpack + React 17 fails to compile on any of the following because webpack\n  complains that `startTransition` doesn't exist in `React`:\n  * import { startTransition } from \"react\"\n  * import * as React from from \"react\";\n    \"startTransition\" in React ? React.startTransition(() => setState()) : setState()\n  * import * as React from from \"react\";\n    \"startTransition\" in React ? React[\"startTransition\"](() => setState()) : setState()\n\n  Moving it to a constant such as the following solves the Webpack/React 17 issue:\n  * import * as React from from \"react\";\n    const START_TRANSITION = \"startTransition\";\n    START_TRANSITION in React ? React[START_TRANSITION](() => setState()) : setState()\n\n  However, that introduces webpack/terser minification issues in production builds\n  in React 18 where minification/obfuscation ends up removing the call of\n  React.startTransition entirely from the first half of the ternary.  Grabbing\n  this exported reference once up front resolves that issue.\n\n  See https://github.com/remix-run/react-router/issues/10579\n*/\nconst START_TRANSITION = \"startTransition\";\nconst startTransitionImpl = React[START_TRANSITION];\nconst FLUSH_SYNC = \"flushSync\";\nconst flushSyncImpl = ReactDOM[FLUSH_SYNC];\nconst USE_ID = \"useId\";\nconst useIdImpl = React[USE_ID];\nfunction startTransitionSafe(cb) {\n  if (startTransitionImpl) {\n    startTransitionImpl(cb);\n  } else {\n    cb();\n  }\n}\nfunction flushSyncSafe(cb) {\n  if (flushSyncImpl) {\n    flushSyncImpl(cb);\n  } else {\n    cb();\n  }\n}\nclass Deferred {\n  constructor() {\n    this.status = \"pending\";\n    this.promise = new Promise((resolve, reject) => {\n      this.resolve = value => {\n        if (this.status === \"pending\") {\n          this.status = \"resolved\";\n          resolve(value);\n        }\n      };\n      this.reject = reason => {\n        if (this.status === \"pending\") {\n          this.status = \"rejected\";\n          reject(reason);\n        }\n      };\n    });\n  }\n}\n/**\n * Given a Remix Router instance, render the appropriate UI\n */\nfunction RouterProvider(_ref) {\n  let {\n    fallbackElement,\n    router,\n    future\n  } = _ref;\n  let [state, setStateImpl] = React.useState(router.state);\n  let [pendingState, setPendingState] = React.useState();\n  let [vtContext, setVtContext] = React.useState({\n    isTransitioning: false\n  });\n  let [renderDfd, setRenderDfd] = React.useState();\n  let [transition, setTransition] = React.useState();\n  let [interruption, setInterruption] = React.useState();\n  let fetcherData = React.useRef(new Map());\n  let {\n    v7_startTransition\n  } = future || {};\n  let optInStartTransition = React.useCallback(cb => {\n    if (v7_startTransition) {\n      startTransitionSafe(cb);\n    } else {\n      cb();\n    }\n  }, [v7_startTransition]);\n  let setState = React.useCallback((newState, _ref2) => {\n    let {\n      deletedFetchers,\n      flushSync: flushSync,\n      viewTransitionOpts: viewTransitionOpts\n    } = _ref2;\n    newState.fetchers.forEach((fetcher, key) => {\n      if (fetcher.data !== undefined) {\n        fetcherData.current.set(key, fetcher.data);\n      }\n    });\n    deletedFetchers.forEach(key => fetcherData.current.delete(key));\n    let isViewTransitionUnavailable = router.window == null || router.window.document == null || typeof router.window.document.startViewTransition !== \"function\";\n    // If this isn't a view transition or it's not available in this browser,\n    // just update and be done with it\n    if (!viewTransitionOpts || isViewTransitionUnavailable) {\n      if (flushSync) {\n        flushSyncSafe(() => setStateImpl(newState));\n      } else {\n        optInStartTransition(() => setStateImpl(newState));\n      }\n      return;\n    }\n    // flushSync + startViewTransition\n    if (flushSync) {\n      // Flush through the context to mark DOM elements as transition=ing\n      flushSyncSafe(() => {\n        // Cancel any pending transitions\n        if (transition) {\n          renderDfd && renderDfd.resolve();\n          transition.skipTransition();\n        }\n        setVtContext({\n          isTransitioning: true,\n          flushSync: true,\n          currentLocation: viewTransitionOpts.currentLocation,\n          nextLocation: viewTransitionOpts.nextLocation\n        });\n      });\n      // Update the DOM\n      let t = router.window.document.startViewTransition(() => {\n        flushSyncSafe(() => setStateImpl(newState));\n      });\n      // Clean up after the animation completes\n      t.finished.finally(() => {\n        flushSyncSafe(() => {\n          setRenderDfd(undefined);\n          setTransition(undefined);\n          setPendingState(undefined);\n          setVtContext({\n            isTransitioning: false\n          });\n        });\n      });\n      flushSyncSafe(() => setTransition(t));\n      return;\n    }\n    // startTransition + startViewTransition\n    if (transition) {\n      // Interrupting an in-progress transition, cancel and let everything flush\n      // out, and then kick off a new transition from the interruption state\n      renderDfd && renderDfd.resolve();\n      transition.skipTransition();\n      setInterruption({\n        state: newState,\n        currentLocation: viewTransitionOpts.currentLocation,\n        nextLocation: viewTransitionOpts.nextLocation\n      });\n    } else {\n      // Completed navigation update with opted-in view transitions, let 'er rip\n      setPendingState(newState);\n      setVtContext({\n        isTransitioning: true,\n        flushSync: false,\n        currentLocation: viewTransitionOpts.currentLocation,\n        nextLocation: viewTransitionOpts.nextLocation\n      });\n    }\n  }, [router.window, transition, renderDfd, fetcherData, optInStartTransition]);\n  // Need to use a layout effect here so we are subscribed early enough to\n  // pick up on any render-driven redirects/navigations (useEffect/<Navigate>)\n  React.useLayoutEffect(() => router.subscribe(setState), [router, setState]);\n  // When we start a view transition, create a Deferred we can use for the\n  // eventual \"completed\" render\n  React.useEffect(() => {\n    if (vtContext.isTransitioning && !vtContext.flushSync) {\n      setRenderDfd(new Deferred());\n    }\n  }, [vtContext]);\n  // Once the deferred is created, kick off startViewTransition() to update the\n  // DOM and then wait on the Deferred to resolve (indicating the DOM update has\n  // happened)\n  React.useEffect(() => {\n    if (renderDfd && pendingState && router.window) {\n      let newState = pendingState;\n      let renderPromise = renderDfd.promise;\n      let transition = router.window.document.startViewTransition(async () => {\n        optInStartTransition(() => setStateImpl(newState));\n        await renderPromise;\n      });\n      transition.finished.finally(() => {\n        setRenderDfd(undefined);\n        setTransition(undefined);\n        setPendingState(undefined);\n        setVtContext({\n          isTransitioning: false\n        });\n      });\n      setTransition(transition);\n    }\n  }, [optInStartTransition, pendingState, renderDfd, router.window]);\n  // When the new location finally renders and is committed to the DOM, this\n  // effect will run to resolve the transition\n  React.useEffect(() => {\n    if (renderDfd && pendingState && state.location.key === pendingState.location.key) {\n      renderDfd.resolve();\n    }\n  }, [renderDfd, transition, state.location, pendingState]);\n  // If we get interrupted with a new navigation during a transition, we skip\n  // the active transition, let it cleanup, then kick it off again here\n  React.useEffect(() => {\n    if (!vtContext.isTransitioning && interruption) {\n      setPendingState(interruption.state);\n      setVtContext({\n        isTransitioning: true,\n        flushSync: false,\n        currentLocation: interruption.currentLocation,\n        nextLocation: interruption.nextLocation\n      });\n      setInterruption(undefined);\n    }\n  }, [vtContext.isTransitioning, interruption]);\n  React.useEffect(() => {\n    process.env.NODE_ENV !== \"production\" ? UNSAFE_warning(fallbackElement == null || !router.future.v7_partialHydration, \"`<RouterProvider fallbackElement>` is deprecated when using \" + \"`v7_partialHydration`, use a `HydrateFallback` component instead\") : void 0;\n    // Only log this once on initial mount\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n  let navigator = React.useMemo(() => {\n    return {\n      createHref: router.createHref,\n      encodeLocation: router.encodeLocation,\n      go: n => router.navigate(n),\n      push: (to, state, opts) => router.navigate(to, {\n        state,\n        preventScrollReset: opts == null ? void 0 : opts.preventScrollReset\n      }),\n      replace: (to, state, opts) => router.navigate(to, {\n        replace: true,\n        state,\n        preventScrollReset: opts == null ? void 0 : opts.preventScrollReset\n      })\n    };\n  }, [router]);\n  let basename = router.basename || \"/\";\n  let dataRouterContext = React.useMemo(() => ({\n    router,\n    navigator,\n    static: false,\n    basename\n  }), [router, navigator, basename]);\n  let routerFuture = React.useMemo(() => ({\n    v7_relativeSplatPath: router.future.v7_relativeSplatPath\n  }), [router.future.v7_relativeSplatPath]);\n  React.useEffect(() => UNSAFE_logV6DeprecationWarnings(future, router.future), [future, router.future]);\n  // The fragment and {null} here are important!  We need them to keep React 18's\n  // useId happy when we are server-rendering since we may have a <script> here\n  // containing the hydrated server-side staticContext (from StaticRouterProvider).\n  // useId relies on the component tree structure to generate deterministic id's\n  // so we need to ensure it remains the same on the client even though\n  // we don't need the <script> tag\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(UNSAFE_DataRouterContext.Provider, {\n    value: dataRouterContext\n  }, /*#__PURE__*/React.createElement(UNSAFE_DataRouterStateContext.Provider, {\n    value: state\n  }, /*#__PURE__*/React.createElement(FetchersContext.Provider, {\n    value: fetcherData.current\n  }, /*#__PURE__*/React.createElement(ViewTransitionContext.Provider, {\n    value: vtContext\n  }, /*#__PURE__*/React.createElement(Router, {\n    basename: basename,\n    location: state.location,\n    navigationType: state.historyAction,\n    navigator: navigator,\n    future: routerFuture\n  }, state.initialized || router.future.v7_partialHydration ? /*#__PURE__*/React.createElement(MemoizedDataRoutes, {\n    routes: router.routes,\n    future: router.future,\n    state: state\n  }) : fallbackElement))))), null);\n}\n// Memoize to avoid re-renders when updating `ViewTransitionContext`\nconst MemoizedDataRoutes = /*#__PURE__*/React.memo(DataRoutes);\nfunction DataRoutes(_ref3) {\n  let {\n    routes,\n    future,\n    state\n  } = _ref3;\n  return UNSAFE_useRoutesImpl(routes, undefined, state, future);\n}\n/**\n * A `<Router>` for use in web browsers. Provides the cleanest URLs.\n */\nfunction BrowserRouter(_ref4) {\n  let {\n    basename,\n    children,\n    future,\n    window\n  } = _ref4;\n  let historyRef = React.useRef();\n  if (historyRef.current == null) {\n    historyRef.current = createBrowserHistory({\n      window,\n      v5Compat: true\n    });\n  }\n  let history = historyRef.current;\n  let [state, setStateImpl] = React.useState({\n    action: history.action,\n    location: history.location\n  });\n  let {\n    v7_startTransition\n  } = future || {};\n  let setState = React.useCallback(newState => {\n    v7_startTransition && startTransitionImpl ? startTransitionImpl(() => setStateImpl(newState)) : setStateImpl(newState);\n  }, [setStateImpl, v7_startTransition]);\n  React.useLayoutEffect(() => history.listen(setState), [history, setState]);\n  React.useEffect(() => UNSAFE_logV6DeprecationWarnings(future), [future]);\n  return /*#__PURE__*/React.createElement(Router, {\n    basename: basename,\n    children: children,\n    location: state.location,\n    navigationType: state.action,\n    navigator: history,\n    future: future\n  });\n}\n/**\n * A `<Router>` for use in web browsers. Stores the location in the hash\n * portion of the URL so it is not sent to the server.\n */\nfunction HashRouter(_ref5) {\n  let {\n    basename,\n    children,\n    future,\n    window\n  } = _ref5;\n  let historyRef = React.useRef();\n  if (historyRef.current == null) {\n    historyRef.current = createHashHistory({\n      window,\n      v5Compat: true\n    });\n  }\n  let history = historyRef.current;\n  let [state, setStateImpl] = React.useState({\n    action: history.action,\n    location: history.location\n  });\n  let {\n    v7_startTransition\n  } = future || {};\n  let setState = React.useCallback(newState => {\n    v7_startTransition && startTransitionImpl ? startTransitionImpl(() => setStateImpl(newState)) : setStateImpl(newState);\n  }, [setStateImpl, v7_startTransition]);\n  React.useLayoutEffect(() => history.listen(setState), [history, setState]);\n  React.useEffect(() => UNSAFE_logV6DeprecationWarnings(future), [future]);\n  return /*#__PURE__*/React.createElement(Router, {\n    basename: basename,\n    children: children,\n    location: state.location,\n    navigationType: state.action,\n    navigator: history,\n    future: future\n  });\n}\n/**\n * A `<Router>` that accepts a pre-instantiated history object. It's important\n * to note that using your own history object is highly discouraged and may add\n * two versions of the history library to your bundles unless you use the same\n * version of the history library that React Router uses internally.\n */\nfunction HistoryRouter(_ref6) {\n  let {\n    basename,\n    children,\n    future,\n    history\n  } = _ref6;\n  let [state, setStateImpl] = React.useState({\n    action: history.action,\n    location: history.location\n  });\n  let {\n    v7_startTransition\n  } = future || {};\n  let setState = React.useCallback(newState => {\n    v7_startTransition && startTransitionImpl ? startTransitionImpl(() => setStateImpl(newState)) : setStateImpl(newState);\n  }, [setStateImpl, v7_startTransition]);\n  React.useLayoutEffect(() => history.listen(setState), [history, setState]);\n  React.useEffect(() => UNSAFE_logV6DeprecationWarnings(future), [future]);\n  return /*#__PURE__*/React.createElement(Router, {\n    basename: basename,\n    children: children,\n    location: state.location,\n    navigationType: state.action,\n    navigator: history,\n    future: future\n  });\n}\nif (process.env.NODE_ENV !== \"production\") {\n  HistoryRouter.displayName = \"unstable_HistoryRouter\";\n}\nconst isBrowser = typeof window !== \"undefined\" && typeof window.document !== \"undefined\" && typeof window.document.createElement !== \"undefined\";\nconst ABSOLUTE_URL_REGEX = /^(?:[a-z][a-z0-9+.-]*:|\\/\\/)/i;\n/**\n * The public API for rendering a history-aware `<a>`.\n */\nconst Link = /*#__PURE__*/React.forwardRef(function LinkWithRef(_ref7, ref) {\n  let {\n      onClick,\n      relative,\n      reloadDocument,\n      replace,\n      state,\n      target,\n      to,\n      preventScrollReset,\n      viewTransition\n    } = _ref7,\n    rest = _objectWithoutPropertiesLoose(_ref7, _excluded);\n  let {\n    basename\n  } = React.useContext(UNSAFE_NavigationContext);\n  // Rendered into <a href> for absolute URLs\n  let absoluteHref;\n  let isExternal = false;\n  if (typeof to === \"string\" && ABSOLUTE_URL_REGEX.test(to)) {\n    // Render the absolute href server- and client-side\n    absoluteHref = to;\n    // Only check for external origins client-side\n    if (isBrowser) {\n      try {\n        let currentUrl = new URL(window.location.href);\n        let targetUrl = to.startsWith(\"//\") ? new URL(currentUrl.protocol + to) : new URL(to);\n        let path = stripBasename(targetUrl.pathname, basename);\n        if (targetUrl.origin === currentUrl.origin && path != null) {\n          // Strip the protocol/origin/basename for same-origin absolute URLs\n          to = path + targetUrl.search + targetUrl.hash;\n        } else {\n          isExternal = true;\n        }\n      } catch (e) {\n        // We can't do external URL detection without a valid URL\n        process.env.NODE_ENV !== \"production\" ? UNSAFE_warning(false, \"<Link to=\\\"\" + to + \"\\\"> contains an invalid URL which will probably break \" + \"when clicked - please update to a valid URL path.\") : void 0;\n      }\n    }\n  }\n  // Rendered into <a href> for relative URLs\n  let href = useHref(to, {\n    relative\n  });\n  let internalOnClick = useLinkClickHandler(to, {\n    replace,\n    state,\n    target,\n    preventScrollReset,\n    relative,\n    viewTransition\n  });\n  function handleClick(event) {\n    if (onClick) onClick(event);\n    if (!event.defaultPrevented) {\n      internalOnClick(event);\n    }\n  }\n  return (\n    /*#__PURE__*/\n    // eslint-disable-next-line jsx-a11y/anchor-has-content\n    React.createElement(\"a\", _extends({}, rest, {\n      href: absoluteHref || href,\n      onClick: isExternal || reloadDocument ? onClick : handleClick,\n      ref: ref,\n      target: target\n    }))\n  );\n});\nif (process.env.NODE_ENV !== \"production\") {\n  Link.displayName = \"Link\";\n}\n/**\n * A `<Link>` wrapper that knows if it's \"active\" or not.\n */\nconst NavLink = /*#__PURE__*/React.forwardRef(function NavLinkWithRef(_ref8, ref) {\n  let {\n      \"aria-current\": ariaCurrentProp = \"page\",\n      caseSensitive = false,\n      className: classNameProp = \"\",\n      end = false,\n      style: styleProp,\n      to,\n      viewTransition,\n      children\n    } = _ref8,\n    rest = _objectWithoutPropertiesLoose(_ref8, _excluded2);\n  let path = useResolvedPath(to, {\n    relative: rest.relative\n  });\n  let location = useLocation();\n  let routerState = React.useContext(UNSAFE_DataRouterStateContext);\n  let {\n    navigator,\n    basename\n  } = React.useContext(UNSAFE_NavigationContext);\n  let isTransitioning = routerState != null &&\n  // Conditional usage is OK here because the usage of a data router is static\n  // eslint-disable-next-line react-hooks/rules-of-hooks\n  useViewTransitionState(path) && viewTransition === true;\n  let toPathname = navigator.encodeLocation ? navigator.encodeLocation(path).pathname : path.pathname;\n  let locationPathname = location.pathname;\n  let nextLocationPathname = routerState && routerState.navigation && routerState.navigation.location ? routerState.navigation.location.pathname : null;\n  if (!caseSensitive) {\n    locationPathname = locationPathname.toLowerCase();\n    nextLocationPathname = nextLocationPathname ? nextLocationPathname.toLowerCase() : null;\n    toPathname = toPathname.toLowerCase();\n  }\n  if (nextLocationPathname && basename) {\n    nextLocationPathname = stripBasename(nextLocationPathname, basename) || nextLocationPathname;\n  }\n  // If the `to` has a trailing slash, look at that exact spot.  Otherwise,\n  // we're looking for a slash _after_ what's in `to`.  For example:\n  //\n  // <NavLink to=\"/users\"> and <NavLink to=\"/users/\">\n  // both want to look for a / at index 6 to match URL `/users/matt`\n  const endSlashPosition = toPathname !== \"/\" && toPathname.endsWith(\"/\") ? toPathname.length - 1 : toPathname.length;\n  let isActive = locationPathname === toPathname || !end && locationPathname.startsWith(toPathname) && locationPathname.charAt(endSlashPosition) === \"/\";\n  let isPending = nextLocationPathname != null && (nextLocationPathname === toPathname || !end && nextLocationPathname.startsWith(toPathname) && nextLocationPathname.charAt(toPathname.length) === \"/\");\n  let renderProps = {\n    isActive,\n    isPending,\n    isTransitioning\n  };\n  let ariaCurrent = isActive ? ariaCurrentProp : undefined;\n  let className;\n  if (typeof classNameProp === \"function\") {\n    className = classNameProp(renderProps);\n  } else {\n    // If the className prop is not a function, we use a default `active`\n    // class for <NavLink />s that are active. In v5 `active` was the default\n    // value for `activeClassName`, but we are removing that API and can still\n    // use the old default behavior for a cleaner upgrade path and keep the\n    // simple styling rules working as they currently do.\n    className = [classNameProp, isActive ? \"active\" : null, isPending ? \"pending\" : null, isTransitioning ? \"transitioning\" : null].filter(Boolean).join(\" \");\n  }\n  let style = typeof styleProp === \"function\" ? styleProp(renderProps) : styleProp;\n  return /*#__PURE__*/React.createElement(Link, _extends({}, rest, {\n    \"aria-current\": ariaCurrent,\n    className: className,\n    ref: ref,\n    style: style,\n    to: to,\n    viewTransition: viewTransition\n  }), typeof children === \"function\" ? children(renderProps) : children);\n});\nif (process.env.NODE_ENV !== \"production\") {\n  NavLink.displayName = \"NavLink\";\n}\n/**\n * A `@remix-run/router`-aware `<form>`. It behaves like a normal form except\n * that the interaction with the server is with `fetch` instead of new document\n * requests, allowing components to add nicer UX to the page as the form is\n * submitted and returns with data.\n */\nconst Form = /*#__PURE__*/React.forwardRef((_ref9, forwardedRef) => {\n  let {\n      fetcherKey,\n      navigate,\n      reloadDocument,\n      replace,\n      state,\n      method = defaultMethod,\n      action,\n      onSubmit,\n      relative,\n      preventScrollReset,\n      viewTransition\n    } = _ref9,\n    props = _objectWithoutPropertiesLoose(_ref9, _excluded3);\n  let submit = useSubmit();\n  let formAction = useFormAction(action, {\n    relative\n  });\n  let formMethod = method.toLowerCase() === \"get\" ? \"get\" : \"post\";\n  let submitHandler = event => {\n    onSubmit && onSubmit(event);\n    if (event.defaultPrevented) return;\n    event.preventDefault();\n    let submitter = event.nativeEvent.submitter;\n    let submitMethod = (submitter == null ? void 0 : submitter.getAttribute(\"formmethod\")) || method;\n    submit(submitter || event.currentTarget, {\n      fetcherKey,\n      method: submitMethod,\n      navigate,\n      replace,\n      state,\n      relative,\n      preventScrollReset,\n      viewTransition\n    });\n  };\n  return /*#__PURE__*/React.createElement(\"form\", _extends({\n    ref: forwardedRef,\n    method: formMethod,\n    action: formAction,\n    onSubmit: reloadDocument ? onSubmit : submitHandler\n  }, props));\n});\nif (process.env.NODE_ENV !== \"production\") {\n  Form.displayName = \"Form\";\n}\n/**\n * This component will emulate the browser's scroll restoration on location\n * changes.\n */\nfunction ScrollRestoration(_ref10) {\n  let {\n    getKey,\n    storageKey\n  } = _ref10;\n  useScrollRestoration({\n    getKey,\n    storageKey\n  });\n  return null;\n}\nif (process.env.NODE_ENV !== \"production\") {\n  ScrollRestoration.displayName = \"ScrollRestoration\";\n}\n//#endregion\n////////////////////////////////////////////////////////////////////////////////\n//#region Hooks\n////////////////////////////////////////////////////////////////////////////////\nvar DataRouterHook;\n(function (DataRouterHook) {\n  DataRouterHook[\"UseScrollRestoration\"] = \"useScrollRestoration\";\n  DataRouterHook[\"UseSubmit\"] = \"useSubmit\";\n  DataRouterHook[\"UseSubmitFetcher\"] = \"useSubmitFetcher\";\n  DataRouterHook[\"UseFetcher\"] = \"useFetcher\";\n  DataRouterHook[\"useViewTransitionState\"] = \"useViewTransitionState\";\n})(DataRouterHook || (DataRouterHook = {}));\nvar DataRouterStateHook;\n(function (DataRouterStateHook) {\n  DataRouterStateHook[\"UseFetcher\"] = \"useFetcher\";\n  DataRouterStateHook[\"UseFetchers\"] = \"useFetchers\";\n  DataRouterStateHook[\"UseScrollRestoration\"] = \"useScrollRestoration\";\n})(DataRouterStateHook || (DataRouterStateHook = {}));\n// Internal hooks\nfunction getDataRouterConsoleError(hookName) {\n  return hookName + \" must be used within a data router.  See https://reactrouter.com/v6/routers/picking-a-router.\";\n}\nfunction useDataRouterContext(hookName) {\n  let ctx = React.useContext(UNSAFE_DataRouterContext);\n  !ctx ? process.env.NODE_ENV !== \"production\" ? UNSAFE_invariant(false, getDataRouterConsoleError(hookName)) : UNSAFE_invariant(false) : void 0;\n  return ctx;\n}\nfunction useDataRouterState(hookName) {\n  let state = React.useContext(UNSAFE_DataRouterStateContext);\n  !state ? process.env.NODE_ENV !== \"production\" ? UNSAFE_invariant(false, getDataRouterConsoleError(hookName)) : UNSAFE_invariant(false) : void 0;\n  return state;\n}\n// External hooks\n/**\n * Handles the click behavior for router `<Link>` components. This is useful if\n * you need to create custom `<Link>` components with the same click behavior we\n * use in our exported `<Link>`.\n */\nfunction useLinkClickHandler(to, _temp) {\n  let {\n    target,\n    replace: replaceProp,\n    state,\n    preventScrollReset,\n    relative,\n    viewTransition\n  } = _temp === void 0 ? {} : _temp;\n  let navigate = useNavigate();\n  let location = useLocation();\n  let path = useResolvedPath(to, {\n    relative\n  });\n  return React.useCallback(event => {\n    if (shouldProcessLinkClick(event, target)) {\n      event.preventDefault();\n      // If the URL hasn't changed, a regular <a> will do a replace instead of\n      // a push, so do the same here unless the replace prop is explicitly set\n      let replace = replaceProp !== undefined ? replaceProp : createPath(location) === createPath(path);\n      navigate(to, {\n        replace,\n        state,\n        preventScrollReset,\n        relative,\n        viewTransition\n      });\n    }\n  }, [location, navigate, path, replaceProp, state, target, to, preventScrollReset, relative, viewTransition]);\n}\n/**\n * A convenient wrapper for reading and writing search parameters via the\n * URLSearchParams interface.\n */\nfunction useSearchParams(defaultInit) {\n  process.env.NODE_ENV !== \"production\" ? UNSAFE_warning(typeof URLSearchParams !== \"undefined\", \"You cannot use the `useSearchParams` hook in a browser that does not \" + \"support the URLSearchParams API. If you need to support Internet \" + \"Explorer 11, we recommend you load a polyfill such as \" + \"https://github.com/ungap/url-search-params.\") : void 0;\n  let defaultSearchParamsRef = React.useRef(createSearchParams(defaultInit));\n  let hasSetSearchParamsRef = React.useRef(false);\n  let location = useLocation();\n  let searchParams = React.useMemo(() =>\n  // Only merge in the defaults if we haven't yet called setSearchParams.\n  // Once we call that we want those to take precedence, otherwise you can't\n  // remove a param with setSearchParams({}) if it has an initial value\n  getSearchParamsForLocation(location.search, hasSetSearchParamsRef.current ? null : defaultSearchParamsRef.current), [location.search]);\n  let navigate = useNavigate();\n  let setSearchParams = React.useCallback((nextInit, navigateOptions) => {\n    const newSearchParams = createSearchParams(typeof nextInit === \"function\" ? nextInit(searchParams) : nextInit);\n    hasSetSearchParamsRef.current = true;\n    navigate(\"?\" + newSearchParams, navigateOptions);\n  }, [navigate, searchParams]);\n  return [searchParams, setSearchParams];\n}\nfunction validateClientSideSubmission() {\n  if (typeof document === \"undefined\") {\n    throw new Error(\"You are calling submit during the server render. \" + \"Try calling submit within a `useEffect` or callback instead.\");\n  }\n}\nlet fetcherId = 0;\nlet getUniqueFetcherId = () => \"__\" + String(++fetcherId) + \"__\";\n/**\n * Returns a function that may be used to programmatically submit a form (or\n * some arbitrary data) to the server.\n */\nfunction useSubmit() {\n  let {\n    router\n  } = useDataRouterContext(DataRouterHook.UseSubmit);\n  let {\n    basename\n  } = React.useContext(UNSAFE_NavigationContext);\n  let currentRouteId = UNSAFE_useRouteId();\n  return React.useCallback(function (target, options) {\n    if (options === void 0) {\n      options = {};\n    }\n    validateClientSideSubmission();\n    let {\n      action,\n      method,\n      encType,\n      formData,\n      body\n    } = getFormSubmissionInfo(target, basename);\n    if (options.navigate === false) {\n      let key = options.fetcherKey || getUniqueFetcherId();\n      router.fetch(key, currentRouteId, options.action || action, {\n        preventScrollReset: options.preventScrollReset,\n        formData,\n        body,\n        formMethod: options.method || method,\n        formEncType: options.encType || encType,\n        flushSync: options.flushSync\n      });\n    } else {\n      router.navigate(options.action || action, {\n        preventScrollReset: options.preventScrollReset,\n        formData,\n        body,\n        formMethod: options.method || method,\n        formEncType: options.encType || encType,\n        replace: options.replace,\n        state: options.state,\n        fromRouteId: currentRouteId,\n        flushSync: options.flushSync,\n        viewTransition: options.viewTransition\n      });\n    }\n  }, [router, basename, currentRouteId]);\n}\n// v7: Eventually we should deprecate this entirely in favor of using the\n// router method directly?\nfunction useFormAction(action, _temp2) {\n  let {\n    relative\n  } = _temp2 === void 0 ? {} : _temp2;\n  let {\n    basename\n  } = React.useContext(UNSAFE_NavigationContext);\n  let routeContext = React.useContext(UNSAFE_RouteContext);\n  !routeContext ? process.env.NODE_ENV !== \"production\" ? UNSAFE_invariant(false, \"useFormAction must be used inside a RouteContext\") : UNSAFE_invariant(false) : void 0;\n  let [match] = routeContext.matches.slice(-1);\n  // Shallow clone path so we can modify it below, otherwise we modify the\n  // object referenced by useMemo inside useResolvedPath\n  let path = _extends({}, useResolvedPath(action ? action : \".\", {\n    relative\n  }));\n  // If no action was specified, browsers will persist current search params\n  // when determining the path, so match that behavior\n  // https://github.com/remix-run/remix/issues/927\n  let location = useLocation();\n  if (action == null) {\n    // Safe to write to this directly here since if action was undefined, we\n    // would have called useResolvedPath(\".\") which will never include a search\n    path.search = location.search;\n    // When grabbing search params from the URL, remove any included ?index param\n    // since it might not apply to our contextual route.  We add it back based\n    // on match.route.index below\n    let params = new URLSearchParams(path.search);\n    let indexValues = params.getAll(\"index\");\n    let hasNakedIndexParam = indexValues.some(v => v === \"\");\n    if (hasNakedIndexParam) {\n      params.delete(\"index\");\n      indexValues.filter(v => v).forEach(v => params.append(\"index\", v));\n      let qs = params.toString();\n      path.search = qs ? \"?\" + qs : \"\";\n    }\n  }\n  if ((!action || action === \".\") && match.route.index) {\n    path.search = path.search ? path.search.replace(/^\\?/, \"?index&\") : \"?index\";\n  }\n  // If we're operating within a basename, prepend it to the pathname prior\n  // to creating the form action.  If this is a root navigation, then just use\n  // the raw basename which allows the basename to have full control over the\n  // presence of a trailing slash on root actions\n  if (basename !== \"/\") {\n    path.pathname = path.pathname === \"/\" ? basename : joinPaths([basename, path.pathname]);\n  }\n  return createPath(path);\n}\n// TODO: (v7) Change the useFetcher generic default from `any` to `unknown`\n/**\n * Interacts with route loaders and actions without causing a navigation. Great\n * for any interaction that stays on the same page.\n */\nfunction useFetcher(_temp3) {\n  var _route$matches;\n  let {\n    key\n  } = _temp3 === void 0 ? {} : _temp3;\n  let {\n    router\n  } = useDataRouterContext(DataRouterHook.UseFetcher);\n  let state = useDataRouterState(DataRouterStateHook.UseFetcher);\n  let fetcherData = React.useContext(FetchersContext);\n  let route = React.useContext(UNSAFE_RouteContext);\n  let routeId = (_route$matches = route.matches[route.matches.length - 1]) == null ? void 0 : _route$matches.route.id;\n  !fetcherData ? process.env.NODE_ENV !== \"production\" ? UNSAFE_invariant(false, \"useFetcher must be used inside a FetchersContext\") : UNSAFE_invariant(false) : void 0;\n  !route ? process.env.NODE_ENV !== \"production\" ? UNSAFE_invariant(false, \"useFetcher must be used inside a RouteContext\") : UNSAFE_invariant(false) : void 0;\n  !(routeId != null) ? process.env.NODE_ENV !== \"production\" ? UNSAFE_invariant(false, \"useFetcher can only be used on routes that contain a unique \\\"id\\\"\") : UNSAFE_invariant(false) : void 0;\n  // Fetcher key handling\n  // OK to call conditionally to feature detect `useId`\n  // eslint-disable-next-line react-hooks/rules-of-hooks\n  let defaultKey = useIdImpl ? useIdImpl() : \"\";\n  let [fetcherKey, setFetcherKey] = React.useState(key || defaultKey);\n  if (key && key !== fetcherKey) {\n    setFetcherKey(key);\n  } else if (!fetcherKey) {\n    // We will only fall through here when `useId` is not available\n    setFetcherKey(getUniqueFetcherId());\n  }\n  // Registration/cleanup\n  React.useEffect(() => {\n    router.getFetcher(fetcherKey);\n    return () => {\n      // Tell the router we've unmounted - if v7_fetcherPersist is enabled this\n      // will not delete immediately but instead queue up a delete after the\n      // fetcher returns to an `idle` state\n      router.deleteFetcher(fetcherKey);\n    };\n  }, [router, fetcherKey]);\n  // Fetcher additions\n  let load = React.useCallback((href, opts) => {\n    !routeId ? process.env.NODE_ENV !== \"production\" ? UNSAFE_invariant(false, \"No routeId available for fetcher.load()\") : UNSAFE_invariant(false) : void 0;\n    router.fetch(fetcherKey, routeId, href, opts);\n  }, [fetcherKey, routeId, router]);\n  let submitImpl = useSubmit();\n  let submit = React.useCallback((target, opts) => {\n    submitImpl(target, _extends({}, opts, {\n      navigate: false,\n      fetcherKey\n    }));\n  }, [fetcherKey, submitImpl]);\n  let FetcherForm = React.useMemo(() => {\n    let FetcherForm = /*#__PURE__*/React.forwardRef((props, ref) => {\n      return /*#__PURE__*/React.createElement(Form, _extends({}, props, {\n        navigate: false,\n        fetcherKey: fetcherKey,\n        ref: ref\n      }));\n    });\n    if (process.env.NODE_ENV !== \"production\") {\n      FetcherForm.displayName = \"fetcher.Form\";\n    }\n    return FetcherForm;\n  }, [fetcherKey]);\n  // Exposed FetcherWithComponents\n  let fetcher = state.fetchers.get(fetcherKey) || IDLE_FETCHER;\n  let data = fetcherData.get(fetcherKey);\n  let fetcherWithComponents = React.useMemo(() => _extends({\n    Form: FetcherForm,\n    submit,\n    load\n  }, fetcher, {\n    data\n  }), [FetcherForm, submit, load, fetcher, data]);\n  return fetcherWithComponents;\n}\n/**\n * Provides all fetchers currently on the page. Useful for layouts and parent\n * routes that need to provide pending/optimistic UI regarding the fetch.\n */\nfunction useFetchers() {\n  let state = useDataRouterState(DataRouterStateHook.UseFetchers);\n  return Array.from(state.fetchers.entries()).map(_ref11 => {\n    let [key, fetcher] = _ref11;\n    return _extends({}, fetcher, {\n      key\n    });\n  });\n}\nconst SCROLL_RESTORATION_STORAGE_KEY = \"react-router-scroll-positions\";\nlet savedScrollPositions = {};\n/**\n * When rendered inside a RouterProvider, will restore scroll positions on navigations\n */\nfunction useScrollRestoration(_temp4) {\n  let {\n    getKey,\n    storageKey\n  } = _temp4 === void 0 ? {} : _temp4;\n  let {\n    router\n  } = useDataRouterContext(DataRouterHook.UseScrollRestoration);\n  let {\n    restoreScrollPosition,\n    preventScrollReset\n  } = useDataRouterState(DataRouterStateHook.UseScrollRestoration);\n  let {\n    basename\n  } = React.useContext(UNSAFE_NavigationContext);\n  let location = useLocation();\n  let matches = useMatches();\n  let navigation = useNavigation();\n  // Trigger manual scroll restoration while we're active\n  React.useEffect(() => {\n    window.history.scrollRestoration = \"manual\";\n    return () => {\n      window.history.scrollRestoration = \"auto\";\n    };\n  }, []);\n  // Save positions on pagehide\n  usePageHide(React.useCallback(() => {\n    if (navigation.state === \"idle\") {\n      let key = (getKey ? getKey(location, matches) : null) || location.key;\n      savedScrollPositions[key] = window.scrollY;\n    }\n    try {\n      sessionStorage.setItem(storageKey || SCROLL_RESTORATION_STORAGE_KEY, JSON.stringify(savedScrollPositions));\n    } catch (error) {\n      process.env.NODE_ENV !== \"production\" ? UNSAFE_warning(false, \"Failed to save scroll positions in sessionStorage, <ScrollRestoration /> will not work properly (\" + error + \").\") : void 0;\n    }\n    window.history.scrollRestoration = \"auto\";\n  }, [storageKey, getKey, navigation.state, location, matches]));\n  // Read in any saved scroll locations\n  if (typeof document !== \"undefined\") {\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    React.useLayoutEffect(() => {\n      try {\n        let sessionPositions = sessionStorage.getItem(storageKey || SCROLL_RESTORATION_STORAGE_KEY);\n        if (sessionPositions) {\n          savedScrollPositions = JSON.parse(sessionPositions);\n        }\n      } catch (e) {\n        // no-op, use default empty object\n      }\n    }, [storageKey]);\n    // Enable scroll restoration in the router\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    React.useLayoutEffect(() => {\n      let getKeyWithoutBasename = getKey && basename !== \"/\" ? (location, matches) => getKey( // Strip the basename to match useLocation()\n      _extends({}, location, {\n        pathname: stripBasename(location.pathname, basename) || location.pathname\n      }), matches) : getKey;\n      let disableScrollRestoration = router == null ? void 0 : router.enableScrollRestoration(savedScrollPositions, () => window.scrollY, getKeyWithoutBasename);\n      return () => disableScrollRestoration && disableScrollRestoration();\n    }, [router, basename, getKey]);\n    // Restore scrolling when state.restoreScrollPosition changes\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    React.useLayoutEffect(() => {\n      // Explicit false means don't do anything (used for submissions)\n      if (restoreScrollPosition === false) {\n        return;\n      }\n      // been here before, scroll to it\n      if (typeof restoreScrollPosition === \"number\") {\n        window.scrollTo(0, restoreScrollPosition);\n        return;\n      }\n      // try to scroll to the hash\n      if (location.hash) {\n        let el = document.getElementById(decodeURIComponent(location.hash.slice(1)));\n        if (el) {\n          el.scrollIntoView();\n          return;\n        }\n      }\n      // Don't reset if this navigation opted out\n      if (preventScrollReset === true) {\n        return;\n      }\n      // otherwise go to the top on new locations\n      window.scrollTo(0, 0);\n    }, [location, restoreScrollPosition, preventScrollReset]);\n  }\n}\n/**\n * Setup a callback to be fired on the window's `beforeunload` event. This is\n * useful for saving some data to `window.localStorage` just before the page\n * refreshes.\n *\n * Note: The `callback` argument should be a function created with\n * `React.useCallback()`.\n */\nfunction useBeforeUnload(callback, options) {\n  let {\n    capture\n  } = options || {};\n  React.useEffect(() => {\n    let opts = capture != null ? {\n      capture\n    } : undefined;\n    window.addEventListener(\"beforeunload\", callback, opts);\n    return () => {\n      window.removeEventListener(\"beforeunload\", callback, opts);\n    };\n  }, [callback, capture]);\n}\n/**\n * Setup a callback to be fired on the window's `pagehide` event. This is\n * useful for saving some data to `window.localStorage` just before the page\n * refreshes.  This event is better supported than beforeunload across browsers.\n *\n * Note: The `callback` argument should be a function created with\n * `React.useCallback()`.\n */\nfunction usePageHide(callback, options) {\n  let {\n    capture\n  } = options || {};\n  React.useEffect(() => {\n    let opts = capture != null ? {\n      capture\n    } : undefined;\n    window.addEventListener(\"pagehide\", callback, opts);\n    return () => {\n      window.removeEventListener(\"pagehide\", callback, opts);\n    };\n  }, [callback, capture]);\n}\n/**\n * Wrapper around useBlocker to show a window.confirm prompt to users instead\n * of building a custom UI with useBlocker.\n *\n * Warning: This has *a lot of rough edges* and behaves very differently (and\n * very incorrectly in some cases) across browsers if user click addition\n * back/forward navigations while the confirm is open.  Use at your own risk.\n */\nfunction usePrompt(_ref12) {\n  let {\n    when,\n    message\n  } = _ref12;\n  let blocker = useBlocker(when);\n  React.useEffect(() => {\n    if (blocker.state === \"blocked\") {\n      let proceed = window.confirm(message);\n      if (proceed) {\n        // This timeout is needed to avoid a weird \"race\" on POP navigations\n        // between the `window.history` revert navigation and the result of\n        // `window.confirm`\n        setTimeout(blocker.proceed, 0);\n      } else {\n        blocker.reset();\n      }\n    }\n  }, [blocker, message]);\n  React.useEffect(() => {\n    if (blocker.state === \"blocked\" && !when) {\n      blocker.reset();\n    }\n  }, [blocker, when]);\n}\n/**\n * Return a boolean indicating if there is an active view transition to the\n * given href.  You can use this value to render CSS classes or viewTransitionName\n * styles onto your elements\n *\n * @param href The destination href\n * @param [opts.relative] Relative routing type (\"route\" | \"path\")\n */\nfunction useViewTransitionState(to, opts) {\n  if (opts === void 0) {\n    opts = {};\n  }\n  let vtContext = React.useContext(ViewTransitionContext);\n  !(vtContext != null) ? process.env.NODE_ENV !== \"production\" ? UNSAFE_invariant(false, \"`useViewTransitionState` must be used within `react-router-dom`'s `RouterProvider`.  \" + \"Did you accidentally import `RouterProvider` from `react-router`?\") : UNSAFE_invariant(false) : void 0;\n  let {\n    basename\n  } = useDataRouterContext(DataRouterHook.useViewTransitionState);\n  let path = useResolvedPath(to, {\n    relative: opts.relative\n  });\n  if (!vtContext.isTransitioning) {\n    return false;\n  }\n  let currentPath = stripBasename(vtContext.currentLocation.pathname, basename) || vtContext.currentLocation.pathname;\n  let nextPath = stripBasename(vtContext.nextLocation.pathname, basename) || vtContext.nextLocation.pathname;\n  // Transition is active if we're going to or coming from the indicated\n  // destination.  This ensures that other PUSH navigations that reverse\n  // an indicated transition apply.  I.e., on the list view you have:\n  //\n  //   <NavLink to=\"/details/1\" viewTransition>\n  //\n  // If you click the breadcrumb back to the list view:\n  //\n  //   <NavLink to=\"/list\" viewTransition>\n  //\n  // We should apply the transition because it's indicated as active going\n  // from /list -> /details/1 and therefore should be active on the reverse\n  // (even though this isn't strictly a POP reverse)\n  return matchPath(path.pathname, nextPath) != null || matchPath(path.pathname, currentPath) != null;\n}\n//#endregion\n\nexport { BrowserRouter, Form, HashRouter, Link, NavLink, RouterProvider, ScrollRestoration, FetchersContext as UNSAFE_FetchersContext, ViewTransitionContext as UNSAFE_ViewTransitionContext, useScrollRestoration as UNSAFE_useScrollRestoration, createBrowserRouter, createHashRouter, createSearchParams, HistoryRouter as unstable_HistoryRouter, usePrompt as unstable_usePrompt, useBeforeUnload, useFetcher, useFetchers, useFormAction, useLinkClickHandler, useSearchParams, useSubmit, useViewTransitionState };\n//# sourceMappingURL=index.js.map\n", "// src/utils.ts\nvar HOLE = -1;\nvar NAN = -2;\nvar NEGATIVE_INFINITY = -3;\nvar NEGATIVE_ZERO = -4;\nvar NULL = -5;\nvar POSITIVE_INFINITY = -6;\nvar UNDEFINED = -7;\nvar TYPE_BIGINT = \"B\";\nvar TYPE_DATE = \"D\";\nvar TYPE_ERROR = \"E\";\nvar TYPE_MAP = \"M\";\nvar TYPE_NULL_OBJECT = \"N\";\nvar TYPE_PROMISE = \"P\";\nvar TYPE_REGEXP = \"R\";\nvar TYPE_SET = \"S\";\nvar TYPE_SYMBOL = \"Y\";\nvar TYPE_URL = \"U\";\nvar TYPE_PREVIOUS_RESOLVED = \"Z\";\nvar Deferred = class {\n  promise;\n  resolve;\n  reject;\n  constructor() {\n    this.promise = new Promise((resolve, reject) => {\n      this.resolve = resolve;\n      this.reject = reject;\n    });\n  }\n};\nfunction createLineSplittingTransform() {\n  const decoder = new TextDecoder();\n  let leftover = \"\";\n  return new TransformStream({\n    transform(chunk, controller) {\n      const str = decoder.decode(chunk, { stream: true });\n      const parts = (leftover + str).split(\"\\n\");\n      leftover = parts.pop() || \"\";\n      for (const part of parts) {\n        controller.enqueue(part);\n      }\n    },\n    flush(controller) {\n      if (leftover) {\n        controller.enqueue(leftover);\n      }\n    }\n  });\n}\n\n// src/flatten.ts\nfunction flatten(input) {\n  const { indices } = this;\n  const existing = indices.get(input);\n  if (existing)\n    return [existing];\n  if (input === void 0)\n    return UNDEFINED;\n  if (input === null)\n    return NULL;\n  if (Number.isNaN(input))\n    return NAN;\n  if (input === Number.POSITIVE_INFINITY)\n    return POSITIVE_INFINITY;\n  if (input === Number.NEGATIVE_INFINITY)\n    return NEGATIVE_INFINITY;\n  if (input === 0 && 1 / input < 0)\n    return NEGATIVE_ZERO;\n  const index = this.index++;\n  indices.set(input, index);\n  stringify.call(this, input, index);\n  return index;\n}\nfunction stringify(input, index) {\n  const { deferred, plugins, postPlugins } = this;\n  const str = this.stringified;\n  const stack = [[input, index]];\n  while (stack.length > 0) {\n    const [input2, index2] = stack.pop();\n    const partsForObj = (obj) => Object.keys(obj).map((k) => `\"_${flatten.call(this, k)}\":${flatten.call(this, obj[k])}`).join(\",\");\n    let error = null;\n    switch (typeof input2) {\n      case \"boolean\":\n      case \"number\":\n      case \"string\":\n        str[index2] = JSON.stringify(input2);\n        break;\n      case \"bigint\":\n        str[index2] = `[\"${TYPE_BIGINT}\",\"${input2}\"]`;\n        break;\n      case \"symbol\": {\n        const keyFor = Symbol.keyFor(input2);\n        if (!keyFor) {\n          error = new Error(\n            \"Cannot encode symbol unless created with Symbol.for()\"\n          );\n        } else {\n          str[index2] = `[\"${TYPE_SYMBOL}\",${JSON.stringify(keyFor)}]`;\n        }\n        break;\n      }\n      case \"object\": {\n        if (!input2) {\n          str[index2] = `${NULL}`;\n          break;\n        }\n        const isArray = Array.isArray(input2);\n        let pluginHandled = false;\n        if (!isArray && plugins) {\n          for (const plugin of plugins) {\n            const pluginResult = plugin(input2);\n            if (Array.isArray(pluginResult)) {\n              pluginHandled = true;\n              const [pluginIdentifier, ...rest] = pluginResult;\n              str[index2] = `[${JSON.stringify(pluginIdentifier)}`;\n              if (rest.length > 0) {\n                str[index2] += `,${rest.map((v) => flatten.call(this, v)).join(\",\")}`;\n              }\n              str[index2] += \"]\";\n              break;\n            }\n          }\n        }\n        if (!pluginHandled) {\n          let result = isArray ? \"[\" : \"{\";\n          if (isArray) {\n            for (let i = 0; i < input2.length; i++)\n              result += (i ? \",\" : \"\") + (i in input2 ? flatten.call(this, input2[i]) : HOLE);\n            str[index2] = `${result}]`;\n          } else if (input2 instanceof Date) {\n            str[index2] = `[\"${TYPE_DATE}\",${input2.getTime()}]`;\n          } else if (input2 instanceof URL) {\n            str[index2] = `[\"${TYPE_URL}\",${JSON.stringify(input2.href)}]`;\n          } else if (input2 instanceof RegExp) {\n            str[index2] = `[\"${TYPE_REGEXP}\",${JSON.stringify(\n              input2.source\n            )},${JSON.stringify(input2.flags)}]`;\n          } else if (input2 instanceof Set) {\n            if (input2.size > 0) {\n              str[index2] = `[\"${TYPE_SET}\",${[...input2].map((val) => flatten.call(this, val)).join(\",\")}]`;\n            } else {\n              str[index2] = `[\"${TYPE_SET}\"]`;\n            }\n          } else if (input2 instanceof Map) {\n            if (input2.size > 0) {\n              str[index2] = `[\"${TYPE_MAP}\",${[...input2].flatMap(([k, v]) => [\n                flatten.call(this, k),\n                flatten.call(this, v)\n              ]).join(\",\")}]`;\n            } else {\n              str[index2] = `[\"${TYPE_MAP}\"]`;\n            }\n          } else if (input2 instanceof Promise) {\n            str[index2] = `[\"${TYPE_PROMISE}\",${index2}]`;\n            deferred[index2] = input2;\n          } else if (input2 instanceof Error) {\n            str[index2] = `[\"${TYPE_ERROR}\",${JSON.stringify(input2.message)}`;\n            if (input2.name !== \"Error\") {\n              str[index2] += `,${JSON.stringify(input2.name)}`;\n            }\n            str[index2] += \"]\";\n          } else if (Object.getPrototypeOf(input2) === null) {\n            str[index2] = `[\"${TYPE_NULL_OBJECT}\",{${partsForObj(input2)}}]`;\n          } else if (isPlainObject(input2)) {\n            str[index2] = `{${partsForObj(input2)}}`;\n          } else {\n            error = new Error(\"Cannot encode object with prototype\");\n          }\n        }\n        break;\n      }\n      default: {\n        const isArray = Array.isArray(input2);\n        let pluginHandled = false;\n        if (!isArray && plugins) {\n          for (const plugin of plugins) {\n            const pluginResult = plugin(input2);\n            if (Array.isArray(pluginResult)) {\n              pluginHandled = true;\n              const [pluginIdentifier, ...rest] = pluginResult;\n              str[index2] = `[${JSON.stringify(pluginIdentifier)}`;\n              if (rest.length > 0) {\n                str[index2] += `,${rest.map((v) => flatten.call(this, v)).join(\",\")}`;\n              }\n              str[index2] += \"]\";\n              break;\n            }\n          }\n        }\n        if (!pluginHandled) {\n          error = new Error(\"Cannot encode function or unexpected type\");\n        }\n      }\n    }\n    if (error) {\n      let pluginHandled = false;\n      if (postPlugins) {\n        for (const plugin of postPlugins) {\n          const pluginResult = plugin(input2);\n          if (Array.isArray(pluginResult)) {\n            pluginHandled = true;\n            const [pluginIdentifier, ...rest] = pluginResult;\n            str[index2] = `[${JSON.stringify(pluginIdentifier)}`;\n            if (rest.length > 0) {\n              str[index2] += `,${rest.map((v) => flatten.call(this, v)).join(\",\")}`;\n            }\n            str[index2] += \"]\";\n            break;\n          }\n        }\n      }\n      if (!pluginHandled) {\n        throw error;\n      }\n    }\n  }\n}\nvar objectProtoNames = Object.getOwnPropertyNames(Object.prototype).sort().join(\"\\0\");\nfunction isPlainObject(thing) {\n  const proto = Object.getPrototypeOf(thing);\n  return proto === Object.prototype || proto === null || Object.getOwnPropertyNames(proto).sort().join(\"\\0\") === objectProtoNames;\n}\n\n// src/unflatten.ts\nvar globalObj = typeof window !== \"undefined\" ? window : typeof globalThis !== \"undefined\" ? globalThis : void 0;\nfunction unflatten(parsed) {\n  const { hydrated, values } = this;\n  if (typeof parsed === \"number\")\n    return hydrate.call(this, parsed);\n  if (!Array.isArray(parsed) || !parsed.length)\n    throw new SyntaxError();\n  const startIndex = values.length;\n  for (const value of parsed) {\n    values.push(value);\n  }\n  hydrated.length = values.length;\n  return hydrate.call(this, startIndex);\n}\nfunction hydrate(index) {\n  const { hydrated, values, deferred, plugins } = this;\n  let result;\n  const stack = [\n    [\n      index,\n      (v) => {\n        result = v;\n      }\n    ]\n  ];\n  let postRun = [];\n  while (stack.length > 0) {\n    const [index2, set] = stack.pop();\n    switch (index2) {\n      case UNDEFINED:\n        set(void 0);\n        continue;\n      case NULL:\n        set(null);\n        continue;\n      case NAN:\n        set(NaN);\n        continue;\n      case POSITIVE_INFINITY:\n        set(Infinity);\n        continue;\n      case NEGATIVE_INFINITY:\n        set(-Infinity);\n        continue;\n      case NEGATIVE_ZERO:\n        set(-0);\n        continue;\n    }\n    if (hydrated[index2]) {\n      set(hydrated[index2]);\n      continue;\n    }\n    const value = values[index2];\n    if (!value || typeof value !== \"object\") {\n      hydrated[index2] = value;\n      set(value);\n      continue;\n    }\n    if (Array.isArray(value)) {\n      if (typeof value[0] === \"string\") {\n        const [type, b, c] = value;\n        switch (type) {\n          case TYPE_DATE:\n            set(hydrated[index2] = new Date(b));\n            continue;\n          case TYPE_URL:\n            set(hydrated[index2] = new URL(b));\n            continue;\n          case TYPE_BIGINT:\n            set(hydrated[index2] = BigInt(b));\n            continue;\n          case TYPE_REGEXP:\n            set(hydrated[index2] = new RegExp(b, c));\n            continue;\n          case TYPE_SYMBOL:\n            set(hydrated[index2] = Symbol.for(b));\n            continue;\n          case TYPE_SET:\n            const newSet = /* @__PURE__ */ new Set();\n            hydrated[index2] = newSet;\n            for (let i = 1; i < value.length; i++)\n              stack.push([\n                value[i],\n                (v) => {\n                  newSet.add(v);\n                }\n              ]);\n            set(newSet);\n            continue;\n          case TYPE_MAP:\n            const map = /* @__PURE__ */ new Map();\n            hydrated[index2] = map;\n            for (let i = 1; i < value.length; i += 2) {\n              const r = [];\n              stack.push([\n                value[i + 1],\n                (v) => {\n                  r[1] = v;\n                }\n              ]);\n              stack.push([\n                value[i],\n                (k) => {\n                  r[0] = k;\n                }\n              ]);\n              postRun.push(() => {\n                map.set(r[0], r[1]);\n              });\n            }\n            set(map);\n            continue;\n          case TYPE_NULL_OBJECT:\n            const obj = /* @__PURE__ */ Object.create(null);\n            hydrated[index2] = obj;\n            for (const key of Object.keys(b).reverse()) {\n              const r = [];\n              stack.push([\n                b[key],\n                (v) => {\n                  r[1] = v;\n                }\n              ]);\n              stack.push([\n                Number(key.slice(1)),\n                (k) => {\n                  r[0] = k;\n                }\n              ]);\n              postRun.push(() => {\n                obj[r[0]] = r[1];\n              });\n            }\n            set(obj);\n            continue;\n          case TYPE_PROMISE:\n            if (hydrated[b]) {\n              set(hydrated[index2] = hydrated[b]);\n            } else {\n              const d = new Deferred();\n              deferred[b] = d;\n              set(hydrated[index2] = d.promise);\n            }\n            continue;\n          case TYPE_ERROR:\n            const [, message, errorType] = value;\n            let error = errorType && globalObj && globalObj[errorType] ? new globalObj[errorType](message) : new Error(message);\n            hydrated[index2] = error;\n            set(error);\n            continue;\n          case TYPE_PREVIOUS_RESOLVED:\n            set(hydrated[index2] = hydrated[b]);\n            continue;\n          default:\n            if (Array.isArray(plugins)) {\n              const r = [];\n              const vals = value.slice(1);\n              for (let i = 0; i < vals.length; i++) {\n                const v = vals[i];\n                stack.push([\n                  v,\n                  (v2) => {\n                    r[i] = v2;\n                  }\n                ]);\n              }\n              postRun.push(() => {\n                for (const plugin of plugins) {\n                  const result2 = plugin(value[0], ...r);\n                  if (result2) {\n                    set(hydrated[index2] = result2.value);\n                    return;\n                  }\n                }\n                throw new SyntaxError();\n              });\n              continue;\n            }\n            throw new SyntaxError();\n        }\n      } else {\n        const array = [];\n        hydrated[index2] = array;\n        for (let i = 0; i < value.length; i++) {\n          const n = value[i];\n          if (n !== HOLE) {\n            stack.push([\n              n,\n              (v) => {\n                array[i] = v;\n              }\n            ]);\n          }\n        }\n        set(array);\n        continue;\n      }\n    } else {\n      const object = {};\n      hydrated[index2] = object;\n      for (const key of Object.keys(value).reverse()) {\n        const r = [];\n        stack.push([\n          value[key],\n          (v) => {\n            r[1] = v;\n          }\n        ]);\n        stack.push([\n          Number(key.slice(1)),\n          (k) => {\n            r[0] = k;\n          }\n        ]);\n        postRun.push(() => {\n          object[r[0]] = r[1];\n        });\n      }\n      set(object);\n      continue;\n    }\n  }\n  while (postRun.length > 0) {\n    postRun.pop()();\n  }\n  return result;\n}\n\n// src/turbo-stream.ts\nasync function decode(readable, options) {\n  const { plugins } = options ?? {};\n  const done = new Deferred();\n  const reader = readable.pipeThrough(createLineSplittingTransform()).getReader();\n  const decoder = {\n    values: [],\n    hydrated: [],\n    deferred: {},\n    plugins\n  };\n  const decoded = await decodeInitial.call(decoder, reader);\n  let donePromise = done.promise;\n  if (decoded.done) {\n    done.resolve();\n  } else {\n    donePromise = decodeDeferred.call(decoder, reader).then(done.resolve).catch((reason) => {\n      for (const deferred of Object.values(decoder.deferred)) {\n        deferred.reject(reason);\n      }\n      done.reject(reason);\n    });\n  }\n  return {\n    done: donePromise.then(() => reader.closed),\n    value: decoded.value\n  };\n}\nasync function decodeInitial(reader) {\n  const read = await reader.read();\n  if (!read.value) {\n    throw new SyntaxError();\n  }\n  let line;\n  try {\n    line = JSON.parse(read.value);\n  } catch (reason) {\n    throw new SyntaxError();\n  }\n  return {\n    done: read.done,\n    value: unflatten.call(this, line)\n  };\n}\nasync function decodeDeferred(reader) {\n  let read = await reader.read();\n  while (!read.done) {\n    if (!read.value)\n      continue;\n    const line = read.value;\n    switch (line[0]) {\n      case TYPE_PROMISE: {\n        const colonIndex = line.indexOf(\":\");\n        const deferredId = Number(line.slice(1, colonIndex));\n        const deferred = this.deferred[deferredId];\n        if (!deferred) {\n          throw new Error(`Deferred ID ${deferredId} not found in stream`);\n        }\n        const lineData = line.slice(colonIndex + 1);\n        let jsonLine;\n        try {\n          jsonLine = JSON.parse(lineData);\n        } catch (reason) {\n          throw new SyntaxError();\n        }\n        const value = unflatten.call(this, jsonLine);\n        deferred.resolve(value);\n        break;\n      }\n      case TYPE_ERROR: {\n        const colonIndex = line.indexOf(\":\");\n        const deferredId = Number(line.slice(1, colonIndex));\n        const deferred = this.deferred[deferredId];\n        if (!deferred) {\n          throw new Error(`Deferred ID ${deferredId} not found in stream`);\n        }\n        const lineData = line.slice(colonIndex + 1);\n        let jsonLine;\n        try {\n          jsonLine = JSON.parse(lineData);\n        } catch (reason) {\n          throw new SyntaxError();\n        }\n        const value = unflatten.call(this, jsonLine);\n        deferred.reject(value);\n        break;\n      }\n      default:\n        throw new SyntaxError();\n    }\n    read = await reader.read();\n  }\n}\nfunction encode(input, options) {\n  const { plugins, postPlugins, signal } = options ?? {};\n  const encoder = {\n    deferred: {},\n    index: 0,\n    indices: /* @__PURE__ */ new Map(),\n    stringified: [],\n    plugins,\n    postPlugins,\n    signal\n  };\n  const textEncoder = new TextEncoder();\n  let lastSentIndex = 0;\n  const readable = new ReadableStream({\n    async start(controller) {\n      const id = flatten.call(encoder, input);\n      if (Array.isArray(id)) {\n        throw new Error(\"This should never happen\");\n      }\n      if (id < 0) {\n        controller.enqueue(textEncoder.encode(`${id}\n`));\n      } else {\n        controller.enqueue(\n          textEncoder.encode(`[${encoder.stringified.join(\",\")}]\n`)\n        );\n        lastSentIndex = encoder.stringified.length - 1;\n      }\n      const seenPromises = /* @__PURE__ */ new WeakSet();\n      while (Object.keys(encoder.deferred).length > 0) {\n        for (const [deferredId, deferred] of Object.entries(encoder.deferred)) {\n          if (seenPromises.has(deferred))\n            continue;\n          seenPromises.add(\n            encoder.deferred[Number(deferredId)] = raceSignal(\n              deferred,\n              encoder.signal\n            ).then(\n              (resolved) => {\n                const id2 = flatten.call(encoder, resolved);\n                if (Array.isArray(id2)) {\n                  controller.enqueue(\n                    textEncoder.encode(\n                      `${TYPE_PROMISE}${deferredId}:[[\"${TYPE_PREVIOUS_RESOLVED}\",${id2[0]}]]\n`\n                    )\n                  );\n                  encoder.index++;\n                  lastSentIndex++;\n                } else if (id2 < 0) {\n                  controller.enqueue(\n                    textEncoder.encode(`${TYPE_PROMISE}${deferredId}:${id2}\n`)\n                  );\n                } else {\n                  const values = encoder.stringified.slice(lastSentIndex + 1).join(\",\");\n                  controller.enqueue(\n                    textEncoder.encode(\n                      `${TYPE_PROMISE}${deferredId}:[${values}]\n`\n                    )\n                  );\n                  lastSentIndex = encoder.stringified.length - 1;\n                }\n              },\n              (reason) => {\n                if (!reason || typeof reason !== \"object\" || !(reason instanceof Error)) {\n                  reason = new Error(\"An unknown error occurred\");\n                }\n                const id2 = flatten.call(encoder, reason);\n                if (Array.isArray(id2)) {\n                  controller.enqueue(\n                    textEncoder.encode(\n                      `${TYPE_ERROR}${deferredId}:[[\"${TYPE_PREVIOUS_RESOLVED}\",${id2[0]}]]\n`\n                    )\n                  );\n                  encoder.index++;\n                  lastSentIndex++;\n                } else if (id2 < 0) {\n                  controller.enqueue(\n                    textEncoder.encode(`${TYPE_ERROR}${deferredId}:${id2}\n`)\n                  );\n                } else {\n                  const values = encoder.stringified.slice(lastSentIndex + 1).join(\",\");\n                  controller.enqueue(\n                    textEncoder.encode(\n                      `${TYPE_ERROR}${deferredId}:[${values}]\n`\n                    )\n                  );\n                  lastSentIndex = encoder.stringified.length - 1;\n                }\n              }\n            ).finally(() => {\n              delete encoder.deferred[Number(deferredId)];\n            })\n          );\n        }\n        await Promise.race(Object.values(encoder.deferred));\n      }\n      await Promise.all(Object.values(encoder.deferred));\n      controller.close();\n    }\n  });\n  return readable;\n}\nfunction raceSignal(promise, signal) {\n  if (!signal)\n    return promise;\n  if (signal.aborted)\n    return Promise.reject(signal.reason || new Error(\"Signal was aborted.\"));\n  const abort = new Promise((resolve, reject) => {\n    signal.addEventListener(\"abort\", (event) => {\n      reject(signal.reason || new Error(\"Signal was aborted.\"));\n    });\n    promise.then(resolve).catch(reject);\n  });\n  abort.catch(() => {\n  });\n  return Promise.race([abort, promise]);\n}\nexport {\n  decode,\n  encode\n};\n", "/**\n * @remix-run/server-runtime v2.15.3\n *\n * Copyright (c) Remix Software Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE.md file in the root directory of this source tree.\n *\n * @license MIT\n */\nimport { data as data$1, isRouteErrorResponse, stripBasename, UNSAFE_ErrorResponseImpl } from '@remix-run/router';\nimport { encode } from 'turbo-stream';\nimport { sanitizeErrors, sanitizeError } from './errors.js';\nimport { getDocumentHeaders } from './headers.js';\nimport { ServerMode } from './mode.js';\nimport { isResponse, isRedirectStatusCode } from './responses.js';\n\nconst SingleFetchRedirectSymbol = Symbol(\"SingleFetchRedirect\");\n// We can't use a 3xx status or else the `fetch()` would follow the redirect.\n// We need to communicate the redirect back as data so we can act on it in the\n// client side router.  We use a 202 to avoid any automatic caching we might\n// get from a 200 since a \"temporary\" redirect should not be cached.  This lets\n// the user control cache behavior via Cache-Control\nconst SINGLE_FETCH_REDIRECT_STATUS = 202;\nfunction getSingleFetchDataStrategy({\n  isActionDataRequest,\n  loadRouteIds\n} = {}) {\n  return async ({\n    request,\n    matches\n  }) => {\n    // Don't call loaders on action data requests\n    if (isActionDataRequest && request.method === \"GET\") {\n      return {};\n    }\n\n    // Only run opt-in loaders when fine-grained revalidation is enabled\n    let matchesToLoad = loadRouteIds ? matches.filter(m => loadRouteIds.includes(m.route.id)) : matches;\n    let results = await Promise.all(matchesToLoad.map(match => match.resolve()));\n    return results.reduce((acc, result, i) => Object.assign(acc, {\n      [matchesToLoad[i].route.id]: result\n    }), {});\n  };\n}\nasync function singleFetchAction(build, serverMode, staticHandler, request, handlerUrl, loadContext, handleError) {\n  try {\n    let handlerRequest = new Request(handlerUrl, {\n      method: request.method,\n      body: request.body,\n      headers: request.headers,\n      signal: request.signal,\n      ...(request.body ? {\n        duplex: \"half\"\n      } : undefined)\n    });\n    let result = await staticHandler.query(handlerRequest, {\n      requestContext: loadContext,\n      skipLoaderErrorBubbling: true,\n      dataStrategy: getSingleFetchDataStrategy({\n        isActionDataRequest: true\n      })\n    });\n\n    // Unlike `handleDataRequest`, when singleFetch is enabled, query does\n    // let non-Response return values through\n    if (isResponse(result)) {\n      return {\n        result: getSingleFetchRedirect(result.status, result.headers, build.basename),\n        headers: result.headers,\n        status: SINGLE_FETCH_REDIRECT_STATUS\n      };\n    }\n    let context = result;\n    let headers = getDocumentHeaders(build, context);\n    if (isRedirectStatusCode(context.statusCode) && headers.has(\"Location\")) {\n      return {\n        result: getSingleFetchRedirect(context.statusCode, headers, build.basename),\n        headers,\n        status: SINGLE_FETCH_REDIRECT_STATUS\n      };\n    }\n\n    // Sanitize errors outside of development environments\n    if (context.errors) {\n      Object.values(context.errors).forEach(err => {\n        // @ts-expect-error This is \"private\" from users but intended for internal use\n        if (!isRouteErrorResponse(err) || err.error) {\n          handleError(err);\n        }\n      });\n      context.errors = sanitizeErrors(context.errors, serverMode);\n    }\n    let singleFetchResult;\n    if (context.errors) {\n      singleFetchResult = {\n        error: Object.values(context.errors)[0]\n      };\n    } else {\n      singleFetchResult = {\n        data: Object.values(context.actionData || {})[0]\n      };\n    }\n    return {\n      result: singleFetchResult,\n      headers,\n      status: context.statusCode\n    };\n  } catch (error) {\n    handleError(error);\n    // These should only be internal remix errors, no need to deal with responseStubs\n    return {\n      result: {\n        error\n      },\n      headers: new Headers(),\n      status: 500\n    };\n  }\n}\nasync function singleFetchLoaders(build, serverMode, staticHandler, request, handlerUrl, loadContext, handleError) {\n  try {\n    var _URL$searchParams$get;\n    let handlerRequest = new Request(handlerUrl, {\n      headers: request.headers,\n      signal: request.signal\n    });\n    let loadRouteIds = ((_URL$searchParams$get = new URL(request.url).searchParams.get(\"_routes\")) === null || _URL$searchParams$get === void 0 ? void 0 : _URL$searchParams$get.split(\",\")) || undefined;\n    let result = await staticHandler.query(handlerRequest, {\n      requestContext: loadContext,\n      skipLoaderErrorBubbling: true,\n      dataStrategy: getSingleFetchDataStrategy({\n        loadRouteIds\n      })\n    });\n    if (isResponse(result)) {\n      return {\n        result: {\n          [SingleFetchRedirectSymbol]: getSingleFetchRedirect(result.status, result.headers, build.basename)\n        },\n        headers: result.headers,\n        status: SINGLE_FETCH_REDIRECT_STATUS\n      };\n    }\n    let context = result;\n    let headers = getDocumentHeaders(build, context);\n    if (isRedirectStatusCode(context.statusCode) && headers.has(\"Location\")) {\n      return {\n        result: {\n          [SingleFetchRedirectSymbol]: getSingleFetchRedirect(context.statusCode, headers, build.basename)\n        },\n        headers,\n        status: SINGLE_FETCH_REDIRECT_STATUS\n      };\n    }\n\n    // Sanitize errors outside of development environments\n    if (context.errors) {\n      Object.values(context.errors).forEach(err => {\n        // @ts-expect-error This is \"private\" from users but intended for internal use\n        if (!isRouteErrorResponse(err) || err.error) {\n          handleError(err);\n        }\n      });\n      context.errors = sanitizeErrors(context.errors, serverMode);\n    }\n\n    // Aggregate results based on the matches we intended to load since we get\n    // `null` values back in `context.loaderData` for routes we didn't load\n    let results = {};\n    let loadedMatches = loadRouteIds ? context.matches.filter(m => m.route.loader && loadRouteIds.includes(m.route.id)) : context.matches;\n    loadedMatches.forEach(m => {\n      var _context$loaderData, _context$errors;\n      let data = (_context$loaderData = context.loaderData) === null || _context$loaderData === void 0 ? void 0 : _context$loaderData[m.route.id];\n      let error = (_context$errors = context.errors) === null || _context$errors === void 0 ? void 0 : _context$errors[m.route.id];\n      if (error !== undefined) {\n        results[m.route.id] = {\n          error\n        };\n      } else if (data !== undefined) {\n        results[m.route.id] = {\n          data\n        };\n      }\n    });\n    return {\n      result: results,\n      headers,\n      status: context.statusCode\n    };\n  } catch (error) {\n    handleError(error);\n    // These should only be internal remix errors, no need to deal with responseStubs\n    return {\n      result: {\n        root: {\n          error\n        }\n      },\n      headers: new Headers(),\n      status: 500\n    };\n  }\n}\nfunction getSingleFetchRedirect(status, headers, basename) {\n  let redirect = headers.get(\"Location\");\n  if (basename) {\n    redirect = stripBasename(redirect, basename) || redirect;\n  }\n  return {\n    redirect,\n    status,\n    revalidate:\n    // Technically X-Remix-Revalidate isn't needed here - that was an implementation\n    // detail of ?_data requests as our way to tell the front end to revalidate when\n    // we didn't have a response body to include that information in.\n    // With single fetch, we tell the front end via this revalidate boolean field.\n    // However, we're respecting it for now because it may be something folks have\n    // used in their own responses\n    // TODO(v3): Consider removing or making this official public API\n    headers.has(\"X-Remix-Revalidate\") || headers.has(\"Set-Cookie\"),\n    reload: headers.has(\"X-Remix-Reload-Document\"),\n    replace: headers.has(\"X-Remix-Replace\")\n  };\n}\n\n// Note: If you change this function please change the corresponding\n// decodeViaTurboStream function in server-runtime\nfunction encodeViaTurboStream(data, requestSignal, streamTimeout, serverMode) {\n  let controller = new AbortController();\n  // How long are we willing to wait for all of the promises in `data` to resolve\n  // before timing out?  We default this to 50ms shorter than the default value for\n  // `ABORT_DELAY` in our built-in `entry.server.tsx` so that once we reject we\n  // have time to flush the rejections down through React's rendering stream before `\n  // we call abort() on that.  If the user provides their own it's up to them to\n  // decouple the aborting of the stream from the aborting of React's renderToPipeableStream\n  let timeoutId = setTimeout(() => controller.abort(new Error(\"Server Timeout\")), typeof streamTimeout === \"number\" ? streamTimeout : 4950);\n  requestSignal.addEventListener(\"abort\", () => clearTimeout(timeoutId));\n  return encode(data, {\n    signal: controller.signal,\n    plugins: [value => {\n      // Even though we sanitized errors on context.errors prior to responding,\n      // we still need to handle this for any deferred data that rejects with an\n      // Error - as those will not be sanitized yet\n      if (value instanceof Error) {\n        let {\n          name,\n          message,\n          stack\n        } = serverMode === ServerMode.Production ? sanitizeError(value, serverMode) : value;\n        return [\"SanitizedError\", name, message, stack];\n      }\n      if (value instanceof UNSAFE_ErrorResponseImpl) {\n        let {\n          data,\n          status,\n          statusText\n        } = value;\n        return [\"ErrorResponse\", data, status, statusText];\n      }\n      if (value && typeof value === \"object\" && SingleFetchRedirectSymbol in value) {\n        return [\"SingleFetchRedirect\", value[SingleFetchRedirectSymbol]];\n      }\n    }],\n    postPlugins: [value => {\n      if (!value) return;\n      if (typeof value !== \"object\") return;\n      return [\"SingleFetchClassInstance\", Object.fromEntries(Object.entries(value))];\n    }, () => [\"SingleFetchFallback\"]]\n  });\n}\nfunction data(value, init) {\n  return data$1(value, init);\n}\n\n// prettier-ignore\n// eslint-disable-next-line\n\nexport { SINGLE_FETCH_REDIRECT_STATUS, SingleFetchRedirectSymbol, data, encodeViaTurboStream, getSingleFetchDataStrategy, getSingleFetchRedirect, singleFetchAction, singleFetchLoaders };\n", "/**\n * @remix-run/react v2.15.3\n *\n * Copyright (c) Remix Software Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE.md file in the root directory of this source tree.\n *\n * @license MIT\n */\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\n\nexport { _extends as extends };\n", "/**\n * @remix-run/react v2.15.3\n *\n * Copyright (c) Remix Software Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE.md file in the root directory of this source tree.\n *\n * @license MIT\n */\nfunction invariant(value, message) {\n  if (value === false || value === null || typeof value === \"undefined\") {\n    throw new Error(message);\n  }\n}\n\nexport { invariant as default };\n", "/**\n * @remix-run/react v2.15.3\n *\n * Copyright (c) Remix Software Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE.md file in the root directory of this source tree.\n *\n * @license MIT\n */\n/**\n * A function that handles data mutations for a route on the client\n */\n\n/**\n * Arguments passed to a route `clientAction` function\n */\n\n/**\n * A function that loads data for a route on the client\n */\n\n/**\n * Arguments passed to a route `clientLoader` function\n */\n\n/**\n * ErrorBoundary to display for this route\n */\n\n/**\n * `<Route HydrateFallback>` component to render on initial loads\n * when client loaders are present\n */\n\n/**\n * Optional, root-only `<Route Layout>` component to wrap the root content in.\n * Useful for defining the <html>/<head>/<body> document shell shared by the\n * Component, HydrateFallback, and ErrorBoundary\n */\n\n/**\n * A function that defines `<link>` tags to be inserted into the `<head>` of\n * the document on route transitions.\n *\n * @see https://remix.run/route/meta\n */\n\n/**\n * A React component that is rendered for a route.\n */\n\n/**\n * An arbitrary object that is associated with a route.\n *\n * @see https://remix.run/route/handle\n */\n\nasync function loadRouteModule(route, routeModulesCache) {\n  if (route.id in routeModulesCache) {\n    return routeModulesCache[route.id];\n  }\n  try {\n    let routeModule = await import( /* webpackIgnore: true */route.module);\n    routeModulesCache[route.id] = routeModule;\n    return routeModule;\n  } catch (error) {\n    // If we can't load the route it's likely one of 2 things:\n    // - User got caught in the middle of a deploy and the CDN no longer has the\n    //   asset we're trying to import! Reload from the server and the user\n    //   (should) get the new manifest--unless the developer purged the static\n    //   assets, the manifest path, but not the documents 😬\n    // - Or, the asset trying to be imported has an error (usually in vite dev\n    //   mode), so the best we can do here is log the error for visibility\n    //   (via `Preserve log`) and reload\n\n    // Log the error so it can be accessed via the `Preserve Log` setting\n    console.error(`Error loading route module \\`${route.module}\\`, reloading page...`);\n    console.error(error);\n    if (window.__remixContext.isSpaMode &&\n    // @ts-expect-error\n    typeof import.meta.hot !== \"undefined\") {\n      // In SPA Mode (which implies vite) we don't want to perform a hard reload\n      // on dev-time errors since it's a vite compilation error and a reload is\n      // just going to fail with the same issue.  Let the UI bubble to the error\n      // boundary and let them see the error in the overlay or the dev server log\n      throw error;\n    }\n    window.location.reload();\n    return new Promise(() => {\n      // check out of this hook cause the DJs never gonna re[s]olve this\n    });\n  }\n}\n\nexport { loadRouteModule };\n", "/**\n * @remix-run/react v2.15.3\n *\n * Copyright (c) Remix Software Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE.md file in the root directory of this source tree.\n *\n * @license MIT\n */\nimport { parsePath } from 'react-router-dom';\nimport { loadRouteModule } from './routeModules.js';\n\n/**\n * Represents a `<link>` element.\n *\n * WHATWG Specification: https://html.spec.whatwg.org/multipage/semantics.html#the-link-element\n */\n\n////////////////////////////////////////////////////////////////////////////////\n\n/**\n * Gets all the links for a set of matches. The modules are assumed to have been\n * loaded already.\n */\nfunction getKeyedLinksForMatches(matches, routeModules, manifest) {\n  let descriptors = matches.map(match => {\n    var _module$links;\n    let module = routeModules[match.route.id];\n    let route = manifest.routes[match.route.id];\n    return [route.css ? route.css.map(href => ({\n      rel: \"stylesheet\",\n      href\n    })) : [], (module === null || module === void 0 ? void 0 : (_module$links = module.links) === null || _module$links === void 0 ? void 0 : _module$links.call(module)) || []];\n  }).flat(2);\n  let preloads = getCurrentPageModulePreloadHrefs(matches, manifest);\n  return dedupeLinkDescriptors(descriptors, preloads);\n}\nasync function prefetchStyleLinks(route, routeModule) {\n  var _route$css, _routeModule$links;\n  if (!route.css && !routeModule.links || !isPreloadSupported()) return;\n  let descriptors = [((_route$css = route.css) === null || _route$css === void 0 ? void 0 : _route$css.map(href => ({\n    rel: \"stylesheet\",\n    href\n  }))) ?? [], ((_routeModule$links = routeModule.links) === null || _routeModule$links === void 0 ? void 0 : _routeModule$links.call(routeModule)) ?? []].flat(1);\n  if (descriptors.length === 0) return;\n  let styleLinks = [];\n  for (let descriptor of descriptors) {\n    if (!isPageLinkDescriptor(descriptor) && descriptor.rel === \"stylesheet\") {\n      styleLinks.push({\n        ...descriptor,\n        rel: \"preload\",\n        as: \"style\"\n      });\n    }\n  }\n\n  // don't block for non-matching media queries, or for stylesheets that are\n  // already in the DOM (active route revalidations)\n  let matchingLinks = styleLinks.filter(link => (!link.media || window.matchMedia(link.media).matches) && !document.querySelector(`link[rel=\"stylesheet\"][href=\"${link.href}\"]`));\n  await Promise.all(matchingLinks.map(prefetchStyleLink));\n}\nasync function prefetchStyleLink(descriptor) {\n  return new Promise(resolve => {\n    let link = document.createElement(\"link\");\n    Object.assign(link, descriptor);\n    function removeLink() {\n      // if a navigation interrupts this prefetch React will update the <head>\n      // and remove the link we put in there manually, so we check if it's still\n      // there before trying to remove it\n      if (document.head.contains(link)) {\n        document.head.removeChild(link);\n      }\n    }\n    link.onload = () => {\n      removeLink();\n      resolve();\n    };\n    link.onerror = () => {\n      removeLink();\n      resolve();\n    };\n    document.head.appendChild(link);\n  });\n}\n\n////////////////////////////////////////////////////////////////////////////////\nfunction isPageLinkDescriptor(object) {\n  return object != null && typeof object.page === \"string\";\n}\nfunction isHtmlLinkDescriptor(object) {\n  if (object == null) {\n    return false;\n  }\n\n  // <link> may not have an href if <link rel=\"preload\"> is used with imageSrcSet + imageSizes\n  // https://github.com/remix-run/remix/issues/184\n  // https://html.spec.whatwg.org/commit-snapshots/cb4f5ff75de5f4cbd7013c4abad02f21c77d4d1c/#attr-link-imagesrcset\n  if (object.href == null) {\n    return object.rel === \"preload\" && typeof object.imageSrcSet === \"string\" && typeof object.imageSizes === \"string\";\n  }\n  return typeof object.rel === \"string\" && typeof object.href === \"string\";\n}\nasync function getKeyedPrefetchLinks(matches, manifest, routeModules) {\n  let links = await Promise.all(matches.map(async match => {\n    let mod = await loadRouteModule(manifest.routes[match.route.id], routeModules);\n    return mod.links ? mod.links() : [];\n  }));\n  return dedupeLinkDescriptors(links.flat(1).filter(isHtmlLinkDescriptor).filter(link => link.rel === \"stylesheet\" || link.rel === \"preload\").map(link => link.rel === \"stylesheet\" ? {\n    ...link,\n    rel: \"prefetch\",\n    as: \"style\"\n  } : {\n    ...link,\n    rel: \"prefetch\"\n  }));\n}\n\n// This is ridiculously identical to transition.ts `filterMatchesToLoad`\nfunction getNewMatchesForLinks(page, nextMatches, currentMatches, manifest, location, future, mode) {\n  let path = parsePathPatch(page);\n  let isNew = (match, index) => {\n    if (!currentMatches[index]) return true;\n    return match.route.id !== currentMatches[index].route.id;\n  };\n  let matchPathChanged = (match, index) => {\n    var _currentMatches$index;\n    return (\n      // param change, /users/123 -> /users/456\n      currentMatches[index].pathname !== match.pathname ||\n      // splat param changed, which is not present in match.path\n      // e.g. /files/images/avatar.jpg -> files/finances.xls\n      ((_currentMatches$index = currentMatches[index].route.path) === null || _currentMatches$index === void 0 ? void 0 : _currentMatches$index.endsWith(\"*\")) && currentMatches[index].params[\"*\"] !== match.params[\"*\"]\n    );\n  };\n\n  // NOTE: keep this mostly up-to-date w/ the transition data diff, but this\n  // version doesn't care about submissions\n  let newMatches = mode === \"data\" && (future.v3_singleFetch || location.search !== path.search) ?\n  // this is really similar to stuff in transition.ts, maybe somebody smarter\n  // than me (or in less of a hurry) can share some of it. You're the best.\n  nextMatches.filter((match, index) => {\n    let manifestRoute = manifest.routes[match.route.id];\n    if (!manifestRoute.hasLoader) {\n      return false;\n    }\n    if (isNew(match, index) || matchPathChanged(match, index)) {\n      return true;\n    }\n\n    // For reused routes on GET navigations, by default:\n    // - Single fetch always revalidates\n    // - Multi fetch revalidates if search params changed\n    let defaultShouldRevalidate = future.v3_singleFetch || location.search !== path.search;\n    if (match.route.shouldRevalidate) {\n      var _currentMatches$;\n      let routeChoice = match.route.shouldRevalidate({\n        currentUrl: new URL(location.pathname + location.search + location.hash, window.origin),\n        currentParams: ((_currentMatches$ = currentMatches[0]) === null || _currentMatches$ === void 0 ? void 0 : _currentMatches$.params) || {},\n        nextUrl: new URL(page, window.origin),\n        nextParams: match.params,\n        defaultShouldRevalidate\n      });\n      if (typeof routeChoice === \"boolean\") {\n        return routeChoice;\n      }\n    }\n    return defaultShouldRevalidate;\n  }) : nextMatches.filter((match, index) => {\n    let manifestRoute = manifest.routes[match.route.id];\n    return (mode === \"assets\" || manifestRoute.hasLoader) && (isNew(match, index) || matchPathChanged(match, index));\n  });\n  return newMatches;\n}\nfunction getDataLinkHrefs(page, matches, manifest) {\n  let path = parsePathPatch(page);\n  return dedupeHrefs(matches.filter(match => manifest.routes[match.route.id].hasLoader && !manifest.routes[match.route.id].hasClientLoader).map(match => {\n    let {\n      pathname,\n      search\n    } = path;\n    let searchParams = new URLSearchParams(search);\n    searchParams.set(\"_data\", match.route.id);\n    return `${pathname}?${searchParams}`;\n  }));\n}\nfunction getModuleLinkHrefs(matches, manifestPatch) {\n  return dedupeHrefs(matches.map(match => {\n    let route = manifestPatch.routes[match.route.id];\n    let hrefs = [route.module];\n    if (route.imports) {\n      hrefs = hrefs.concat(route.imports);\n    }\n    return hrefs;\n  }).flat(1));\n}\n\n// The `<Script>` will render rel=modulepreload for the current page, we don't\n// need to include them in a page prefetch, this gives us the list to remove\n// while deduping.\nfunction getCurrentPageModulePreloadHrefs(matches, manifest) {\n  return dedupeHrefs(matches.map(match => {\n    let route = manifest.routes[match.route.id];\n    let hrefs = [route.module];\n    if (route.imports) {\n      hrefs = hrefs.concat(route.imports);\n    }\n    return hrefs;\n  }).flat(1));\n}\nfunction dedupeHrefs(hrefs) {\n  return [...new Set(hrefs)];\n}\nfunction sortKeys(obj) {\n  let sorted = {};\n  let keys = Object.keys(obj).sort();\n  for (let key of keys) {\n    sorted[key] = obj[key];\n  }\n  return sorted;\n}\nfunction dedupeLinkDescriptors(descriptors, preloads) {\n  let set = new Set();\n  let preloadsSet = new Set(preloads);\n  return descriptors.reduce((deduped, descriptor) => {\n    let alreadyModulePreload = preloads && !isPageLinkDescriptor(descriptor) && descriptor.as === \"script\" && descriptor.href && preloadsSet.has(descriptor.href);\n    if (alreadyModulePreload) {\n      return deduped;\n    }\n    let key = JSON.stringify(sortKeys(descriptor));\n    if (!set.has(key)) {\n      set.add(key);\n      deduped.push({\n        key,\n        link: descriptor\n      });\n    }\n    return deduped;\n  }, []);\n}\n\n// https://github.com/remix-run/history/issues/897\nfunction parsePathPatch(href) {\n  let path = parsePath(href);\n  if (path.search === undefined) path.search = \"\";\n  return path;\n}\n\n// Detect if this browser supports <link rel=\"preload\"> (or has it enabled).\n// Originally added to handle the firefox `network.preload` config:\n//   https://bugzilla.mozilla.org/show_bug.cgi?id=1847811\nlet _isPreloadSupported;\nfunction isPreloadSupported() {\n  if (_isPreloadSupported !== undefined) {\n    return _isPreloadSupported;\n  }\n  let el = document.createElement(\"link\");\n  _isPreloadSupported = el.relList.supports(\"preload\");\n  el = null;\n  return _isPreloadSupported;\n}\n\nexport { getDataLinkHrefs, getKeyedLinksForMatches, getKeyedPrefetchLinks, getModuleLinkHrefs, getNewMatchesForLinks, isPageLinkDescriptor, prefetchStyleLinks };\n", "/**\n * @remix-run/react v2.15.3\n *\n * Copyright (c) Remix Software Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE.md file in the root directory of this source tree.\n *\n * @license MIT\n */\n// This escapeHtml utility is based on https://github.com/zertosh/htmlescape\n// License: https://github.com/zertosh/htmlescape/blob/0527ca7156a524d256101bb310a9f970f63078ad/LICENSE\n\n// We've chosen to inline the utility here to reduce the number of npm dependencies we have,\n// slightly decrease the code size compared the original package and make it esm compatible.\n\nconst ESCAPE_LOOKUP = {\n  \"&\": \"\\\\u0026\",\n  \">\": \"\\\\u003e\",\n  \"<\": \"\\\\u003c\",\n  \"\\u2028\": \"\\\\u2028\",\n  \"\\u2029\": \"\\\\u2029\"\n};\nconst ESCAPE_REGEX = /[&><\\u2028\\u2029]/g;\nfunction escapeHtml(html) {\n  return html.replace(ESCAPE_REGEX, match => ESCAPE_LOOKUP[match]);\n}\nfunction createHtml(html) {\n  return {\n    __html: html\n  };\n}\n\nexport { createHtml, escapeHtml };\n", "/**\n * @remix-run/react v2.15.3\n *\n * Copyright (c) Remix Software Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE.md file in the root directory of this source tree.\n *\n * @license MIT\n */\nimport { AbortedDeferredError, UNSAFE_DeferredData } from '@remix-run/router';\n\n/**\n * Data for a route that was returned from a `loader()`.\n */\n\nfunction isCatchResponse(response) {\n  return response.headers.get(\"X-Remix-Catch\") != null;\n}\nfunction isErrorResponse(response) {\n  return response.headers.get(\"X-Remix-Error\") != null;\n}\nfunction isNetworkErrorResponse(response) {\n  // If we reach the Remix server, we can safely identify response types via the\n  // X-Remix-Error/X-Remix-Catch headers.  However, if we never reach the Remix\n  // server, and instead receive a 4xx/5xx from somewhere in between (like\n  // Cloudflare), then we get a false negative in the isErrorResponse check and\n  // we incorrectly assume that the user returns the 4xx/5xx response and\n  // consider it successful.  To alleviate this, we add X-Remix-Response to any\n  // non-Error/non-Catch responses coming back from the server.  If we don't\n  // see this, we can conclude that a 4xx/5xx response never actually reached\n  // the Remix server and we can bubble it up as an error.\n  return isResponse(response) && response.status >= 400 && response.headers.get(\"X-Remix-Error\") == null && response.headers.get(\"X-Remix-Catch\") == null && response.headers.get(\"X-Remix-Response\") == null;\n}\nfunction isRedirectResponse(response) {\n  return response.headers.get(\"X-Remix-Redirect\") != null;\n}\nfunction isDeferredResponse(response) {\n  var _response$headers$get;\n  return !!((_response$headers$get = response.headers.get(\"Content-Type\")) !== null && _response$headers$get !== void 0 && _response$headers$get.match(/text\\/remix-deferred/));\n}\nfunction isResponse(value) {\n  return value != null && typeof value.status === \"number\" && typeof value.statusText === \"string\" && typeof value.headers === \"object\" && typeof value.body !== \"undefined\";\n}\nfunction isDeferredData(value) {\n  let deferred = value;\n  return deferred && typeof deferred === \"object\" && typeof deferred.data === \"object\" && typeof deferred.subscribe === \"function\" && typeof deferred.cancel === \"function\" && typeof deferred.resolveData === \"function\";\n}\nasync function fetchData(request, routeId, retry = 0) {\n  let url = new URL(request.url);\n  url.searchParams.set(\"_data\", routeId);\n  if (retry > 0) {\n    // Retry up to 3 times waiting 50, 250, 1250 ms\n    // between retries for a total of 1550 ms before giving up.\n    await new Promise(resolve => setTimeout(resolve, 5 ** retry * 10));\n  }\n  let init = await createRequestInit(request);\n  let revalidation = window.__remixRevalidation;\n  let response = await fetch(url.href, init).catch(error => {\n    if (typeof revalidation === \"number\" && revalidation === window.__remixRevalidation && (error === null || error === void 0 ? void 0 : error.name) === \"TypeError\" && retry < 3) {\n      return fetchData(request, routeId, retry + 1);\n    }\n    throw error;\n  });\n  if (isErrorResponse(response)) {\n    let data = await response.json();\n    let error = new Error(data.message);\n    error.stack = data.stack;\n    return error;\n  }\n  if (isNetworkErrorResponse(response)) {\n    let text = await response.text();\n    let error = new Error(text);\n    error.stack = undefined;\n    return error;\n  }\n  return response;\n}\nasync function createRequestInit(request) {\n  let init = {\n    signal: request.signal\n  };\n  if (request.method !== \"GET\") {\n    init.method = request.method;\n    let contentType = request.headers.get(\"Content-Type\");\n\n    // Check between word boundaries instead of startsWith() due to the last\n    // paragraph of https://httpwg.org/specs/rfc9110.html#field.content-type\n    if (contentType && /\\bapplication\\/json\\b/.test(contentType)) {\n      init.headers = {\n        \"Content-Type\": contentType\n      };\n      init.body = JSON.stringify(await request.json());\n    } else if (contentType && /\\btext\\/plain\\b/.test(contentType)) {\n      init.headers = {\n        \"Content-Type\": contentType\n      };\n      init.body = await request.text();\n    } else if (contentType && /\\bapplication\\/x-www-form-urlencoded\\b/.test(contentType)) {\n      init.body = new URLSearchParams(await request.text());\n    } else {\n      init.body = await request.formData();\n    }\n  }\n  return init;\n}\nconst DEFERRED_VALUE_PLACEHOLDER_PREFIX = \"__deferred_promise:\";\nasync function parseDeferredReadableStream(stream) {\n  if (!stream) {\n    throw new Error(\"parseDeferredReadableStream requires stream argument\");\n  }\n  let deferredData;\n  let deferredResolvers = {};\n  try {\n    let sectionReader = readStreamSections(stream);\n\n    // Read the first section to get the critical data\n    let initialSectionResult = await sectionReader.next();\n    let initialSection = initialSectionResult.value;\n    if (!initialSection) throw new Error(\"no critical data\");\n    let criticalData = JSON.parse(initialSection);\n\n    // Setup deferred data and resolvers for later based on the critical data\n    if (typeof criticalData === \"object\" && criticalData !== null) {\n      for (let [eventKey, value] of Object.entries(criticalData)) {\n        if (typeof value !== \"string\" || !value.startsWith(DEFERRED_VALUE_PLACEHOLDER_PREFIX)) {\n          continue;\n        }\n        deferredData = deferredData || {};\n        deferredData[eventKey] = new Promise((resolve, reject) => {\n          deferredResolvers[eventKey] = {\n            resolve: value => {\n              resolve(value);\n              delete deferredResolvers[eventKey];\n            },\n            reject: error => {\n              reject(error);\n              delete deferredResolvers[eventKey];\n            }\n          };\n        });\n      }\n    }\n\n    // Read the rest of the stream and resolve deferred promises\n    void (async () => {\n      try {\n        for await (let section of sectionReader) {\n          // Determine event type and data\n          let [event, ...sectionDataStrings] = section.split(\":\");\n          let sectionDataString = sectionDataStrings.join(\":\");\n          let data = JSON.parse(sectionDataString);\n          if (event === \"data\") {\n            for (let [key, value] of Object.entries(data)) {\n              if (deferredResolvers[key]) {\n                deferredResolvers[key].resolve(value);\n              }\n            }\n          } else if (event === \"error\") {\n            for (let [key, value] of Object.entries(data)) {\n              let err = new Error(value.message);\n              err.stack = value.stack;\n              if (deferredResolvers[key]) {\n                deferredResolvers[key].reject(err);\n              }\n            }\n          }\n        }\n        for (let [key, resolver] of Object.entries(deferredResolvers)) {\n          resolver.reject(new AbortedDeferredError(`Deferred ${key} will never be resolved`));\n        }\n      } catch (error) {\n        // Reject any existing deferred promises if something blows up\n        for (let resolver of Object.values(deferredResolvers)) {\n          resolver.reject(error);\n        }\n      }\n    })();\n    return new UNSAFE_DeferredData({\n      ...criticalData,\n      ...deferredData\n    });\n  } catch (error) {\n    for (let resolver of Object.values(deferredResolvers)) {\n      resolver.reject(error);\n    }\n    throw error;\n  }\n}\nasync function* readStreamSections(stream) {\n  let reader = stream.getReader();\n  let buffer = [];\n  let sections = [];\n  let closed = false;\n  let encoder = new TextEncoder();\n  let decoder = new TextDecoder();\n  let readStreamSection = async () => {\n    if (sections.length > 0) return sections.shift();\n\n    // Read from the stream until we have at least one complete section to process\n    while (!closed && sections.length === 0) {\n      let chunk = await reader.read();\n      if (chunk.done) {\n        closed = true;\n        break;\n      }\n      // Buffer the raw chunks\n      buffer.push(chunk.value);\n      try {\n        // Attempt to split off a section from the buffer\n        let bufferedString = decoder.decode(mergeArrays(...buffer));\n        let splitSections = bufferedString.split(\"\\n\\n\");\n        if (splitSections.length >= 2) {\n          // We have a complete section, so add it to the sections array\n          sections.push(...splitSections.slice(0, -1));\n          // Remove the section from the buffer and store the rest for future processing\n          buffer = [encoder.encode(splitSections.slice(-1).join(\"\\n\\n\"))];\n        }\n\n        // If we successfully parsed at least one section, break out of reading the stream\n        // to allow upstream processing of the processable sections\n        if (sections.length > 0) {\n          break;\n        }\n      } catch {\n        // If we failed to parse the buffer it was because we failed to decode the stream\n        // because we are missing bytes that we haven't yet received, so continue reading\n        // from the stream until we have a complete section\n        continue;\n      }\n    }\n\n    // If we have a complete section, return it\n    if (sections.length > 0) {\n      return sections.shift();\n    }\n\n    // If we have no complete section, but we have no more chunks to process,\n    // split those sections and clear out the buffer as there is no more data\n    // to process. If this errors, let it bubble up as the stream ended\n    // without valid data\n    if (buffer.length > 0) {\n      let bufferedString = decoder.decode(mergeArrays(...buffer));\n      sections = bufferedString.split(\"\\n\\n\").filter(s => s);\n      buffer = [];\n    }\n\n    // Return any remaining sections that have been processed\n    return sections.shift();\n  };\n  let section = await readStreamSection();\n  while (section) {\n    yield section;\n    section = await readStreamSection();\n  }\n}\nfunction mergeArrays(...arrays) {\n  let out = new Uint8Array(arrays.reduce((total, arr) => total + arr.length, 0));\n  let offset = 0;\n  for (let arr of arrays) {\n    out.set(arr, offset);\n    offset += arr.length;\n  }\n  return out;\n}\n\nexport { createRequestInit, fetchData, isCatchResponse, isDeferredData, isDeferredResponse, isErrorResponse, isNetworkErrorResponse, isRedirectResponse, isResponse, parseDeferredReadableStream };\n", "/**\n * @remix-run/react v2.15.3\n *\n * Copyright (c) Remix Software Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE.md file in the root directory of this source tree.\n *\n * @license MIT\n */\nimport * as React from 'react';\nimport { isRouteErrorResponse, data, UNSAFE_ErrorResponseImpl, redirect } from '@remix-run/router';\nimport { UNSAFE_SingleFetchRedirectSymbol } from '@remix-run/server-runtime';\nimport { decode } from 'turbo-stream';\nimport { createRequestInit, isResponse } from './data.js';\nimport { escapeHtml } from './markup.js';\nimport invariant from './invariant.js';\n\n// StreamTransfer recursively renders down chunks of the `serverHandoffStream`\n// into the client-side `streamController`\nfunction StreamTransfer({\n  context,\n  identifier,\n  reader,\n  textDecoder,\n  nonce\n}) {\n  // If the user didn't render the <Scripts> component then we don't have to\n  // bother streaming anything in\n  if (!context.renderMeta || !context.renderMeta.didRenderScripts) {\n    return null;\n  }\n  if (!context.renderMeta.streamCache) {\n    context.renderMeta.streamCache = {};\n  }\n  let {\n    streamCache\n  } = context.renderMeta;\n  let promise = streamCache[identifier];\n  if (!promise) {\n    promise = streamCache[identifier] = reader.read().then(result => {\n      streamCache[identifier].result = {\n        done: result.done,\n        value: textDecoder.decode(result.value, {\n          stream: true\n        })\n      };\n    }).catch(e => {\n      streamCache[identifier].error = e;\n    });\n  }\n  if (promise.error) {\n    throw promise.error;\n  }\n  if (promise.result === undefined) {\n    throw promise;\n  }\n  let {\n    done,\n    value\n  } = promise.result;\n  let scriptTag = value ? /*#__PURE__*/React.createElement(\"script\", {\n    nonce: nonce,\n    dangerouslySetInnerHTML: {\n      __html: `window.__remixContext.streamController.enqueue(${escapeHtml(JSON.stringify(value))});`\n    }\n  }) : null;\n  if (done) {\n    return /*#__PURE__*/React.createElement(React.Fragment, null, scriptTag, /*#__PURE__*/React.createElement(\"script\", {\n      nonce: nonce,\n      dangerouslySetInnerHTML: {\n        __html: `window.__remixContext.streamController.close();`\n      }\n    }));\n  } else {\n    return /*#__PURE__*/React.createElement(React.Fragment, null, scriptTag, /*#__PURE__*/React.createElement(React.Suspense, null, /*#__PURE__*/React.createElement(StreamTransfer, {\n      context: context,\n      identifier: identifier + 1,\n      reader: reader,\n      textDecoder: textDecoder,\n      nonce: nonce\n    })));\n  }\n}\nfunction getSingleFetchDataStrategy(manifest, routeModules, getRouter) {\n  return async ({\n    request,\n    matches,\n    fetcherKey\n  }) => {\n    // Actions are simple and behave the same for navigations and fetchers\n    if (request.method !== \"GET\") {\n      return singleFetchActionStrategy(request, matches);\n    }\n\n    // Fetcher loads are singular calls to one loader\n    if (fetcherKey) {\n      return singleFetchLoaderFetcherStrategy(request, matches);\n    }\n\n    // Navigational loads are more complex...\n    return singleFetchLoaderNavigationStrategy(manifest, routeModules, getRouter(), request, matches);\n  };\n}\n\n// Actions are simple since they're singular calls to the server for both\n// navigations and fetchers)\nasync function singleFetchActionStrategy(request, matches) {\n  let actionMatch = matches.find(m => m.shouldLoad);\n  invariant(actionMatch, \"No action match found\");\n  let actionStatus = undefined;\n  let result = await actionMatch.resolve(async handler => {\n    let result = await handler(async () => {\n      let url = singleFetchUrl(request.url);\n      let init = await createRequestInit(request);\n      let {\n        data,\n        status\n      } = await fetchAndDecode(url, init);\n      actionStatus = status;\n      return unwrapSingleFetchResult(data, actionMatch.route.id);\n    });\n    return result;\n  });\n  if (isResponse(result.result) || isRouteErrorResponse(result.result)) {\n    return {\n      [actionMatch.route.id]: result\n    };\n  }\n\n  // For non-responses, proxy along the statusCode via data()\n  // (most notably for skipping action error revalidation)\n  return {\n    [actionMatch.route.id]: {\n      type: result.type,\n      result: data(result.result, actionStatus)\n    }\n  };\n}\n\n// Loaders are trickier since we only want to hit the server once, so we\n// create a singular promise for all server-loader routes to latch onto.\nasync function singleFetchLoaderNavigationStrategy(manifest, routeModules, router, request, matches) {\n  // Track which routes need a server load - in case we need to tack on a\n  // `_routes` param\n  let routesParams = new Set();\n\n  // We only add `_routes` when one or more routes opts out of a load via\n  // `shouldRevalidate` or `clientLoader`\n  let foundOptOutRoute = false;\n\n  // Deferreds for each route so we can be sure they've all loaded via\n  // `match.resolve()`, and a singular promise that can tell us all routes\n  // have been resolved\n  let routeDfds = matches.map(() => createDeferred());\n  let routesLoadedPromise = Promise.all(routeDfds.map(d => d.promise));\n\n  // Deferred that we'll use for the call to the server that each match can\n  // await and parse out it's specific result\n  let singleFetchDfd = createDeferred();\n\n  // Base URL and RequestInit for calls to the server\n  let url = stripIndexParam(singleFetchUrl(request.url));\n  let init = await createRequestInit(request);\n\n  // We'll build up this results object as we loop through matches\n  let results = {};\n  let resolvePromise = Promise.all(matches.map(async (m, i) => m.resolve(async handler => {\n    routeDfds[i].resolve();\n    if (!m.shouldLoad) {\n      var _routeModules$m$route;\n      // If we're not yet initialized and this is the initial load, respect\n      // `shouldLoad` because we're only dealing with `clientLoader.hydrate`\n      // routes which will fall into the `clientLoader` section below.\n      if (!router.state.initialized) {\n        return;\n      }\n\n      // Otherwise, we opt out if we currently have data, a `loader`, and a\n      // `shouldRevalidate` function.  This implies that the user opted out\n      // via `shouldRevalidate`\n      if (m.route.id in router.state.loaderData && manifest.routes[m.route.id].hasLoader && (_routeModules$m$route = routeModules[m.route.id]) !== null && _routeModules$m$route !== void 0 && _routeModules$m$route.shouldRevalidate) {\n        foundOptOutRoute = true;\n        return;\n      }\n    }\n\n    // When a route has a client loader, it opts out of the singular call and\n    // calls it's server loader via `serverLoader()` using a `?_routes` param\n    if (manifest.routes[m.route.id].hasClientLoader) {\n      if (manifest.routes[m.route.id].hasLoader) {\n        foundOptOutRoute = true;\n      }\n      try {\n        let result = await fetchSingleLoader(handler, url, init, m.route.id);\n        results[m.route.id] = {\n          type: \"data\",\n          result\n        };\n      } catch (e) {\n        results[m.route.id] = {\n          type: \"error\",\n          result: e\n        };\n      }\n      return;\n    }\n\n    // Load this route on the server if it has a loader\n    if (manifest.routes[m.route.id].hasLoader) {\n      routesParams.add(m.route.id);\n    }\n\n    // Lump this match in with the others on a singular promise\n    try {\n      let result = await handler(async () => {\n        let data = await singleFetchDfd.promise;\n        return unwrapSingleFetchResults(data, m.route.id);\n      });\n      results[m.route.id] = {\n        type: \"data\",\n        result\n      };\n    } catch (e) {\n      results[m.route.id] = {\n        type: \"error\",\n        result: e\n      };\n    }\n  })));\n\n  // Wait for all routes to resolve above before we make the HTTP call\n  await routesLoadedPromise;\n\n  // We can skip the server call:\n  // - On initial hydration - only clientLoaders can pass through via `clientLoader.hydrate`\n  // - If there are no routes to fetch from the server\n  //\n  // One exception - if we are performing an HDR revalidation we have to call\n  // the server in case a new loader has shown up that the manifest doesn't yet\n  // know about\n  if ((!router.state.initialized || routesParams.size === 0) && !window.__remixHdrActive) {\n    singleFetchDfd.resolve({});\n  } else {\n    try {\n      // When one or more routes have opted out, we add a _routes param to\n      // limit the loaders to those that have a server loader and did not\n      // opt out\n      if (foundOptOutRoute && routesParams.size > 0) {\n        url.searchParams.set(\"_routes\", matches.filter(m => routesParams.has(m.route.id)).map(m => m.route.id).join(\",\"));\n      }\n      let data = await fetchAndDecode(url, init);\n      singleFetchDfd.resolve(data.data);\n    } catch (e) {\n      singleFetchDfd.reject(e);\n    }\n  }\n  await resolvePromise;\n  return results;\n}\n\n// Fetcher loader calls are much simpler than navigational loader calls\nasync function singleFetchLoaderFetcherStrategy(request, matches) {\n  let fetcherMatch = matches.find(m => m.shouldLoad);\n  invariant(fetcherMatch, \"No fetcher match found\");\n  let result = await fetcherMatch.resolve(async handler => {\n    let url = stripIndexParam(singleFetchUrl(request.url));\n    let init = await createRequestInit(request);\n    return fetchSingleLoader(handler, url, init, fetcherMatch.route.id);\n  });\n  return {\n    [fetcherMatch.route.id]: result\n  };\n}\nfunction fetchSingleLoader(handler, url, init, routeId) {\n  return handler(async () => {\n    let singleLoaderUrl = new URL(url);\n    singleLoaderUrl.searchParams.set(\"_routes\", routeId);\n    let {\n      data\n    } = await fetchAndDecode(singleLoaderUrl, init);\n    return unwrapSingleFetchResults(data, routeId);\n  });\n}\nfunction stripIndexParam(url) {\n  let indexValues = url.searchParams.getAll(\"index\");\n  url.searchParams.delete(\"index\");\n  let indexValuesToKeep = [];\n  for (let indexValue of indexValues) {\n    if (indexValue) {\n      indexValuesToKeep.push(indexValue);\n    }\n  }\n  for (let toKeep of indexValuesToKeep) {\n    url.searchParams.append(\"index\", toKeep);\n  }\n  return url;\n}\nfunction singleFetchUrl(reqUrl) {\n  let url = typeof reqUrl === \"string\" ? new URL(reqUrl, window.location.origin) : reqUrl;\n  if (url.pathname === \"/\") {\n    url.pathname = \"_root.data\";\n  } else {\n    url.pathname = `${url.pathname.replace(/\\/$/, \"\")}.data`;\n  }\n  return url;\n}\nasync function fetchAndDecode(url, init) {\n  let res = await fetch(url, init);\n  // Don't do a hard check against the header here.  We'll get `text/x-script`\n  // when we have a running server, but if folks want to prerender `.data` files\n  // and serve them from a CDN we should let them come back with whatever\n  // Content-Type their CDN provides and not force them to make sure `.data`\n  // files are served as `text/x-script`.  We'll throw if we can't decode anyway.\n\n  // some status codes are not permitted to have bodies, so we want to just\n  // treat those as \"no data\" instead of throwing an exception.\n  // 304 is not included here because the browser should fill those responses\n  // with the cached body content.\n  let NO_BODY_STATUS_CODES = new Set([100, 101, 204, 205]);\n  if (NO_BODY_STATUS_CODES.has(res.status)) {\n    if (!init.method || init.method === \"GET\") {\n      // SingleFetchResults can just have no routeId keys which will result\n      // in no data for all routes\n      return {\n        status: res.status,\n        data: {}\n      };\n    } else {\n      // SingleFetchResult is for a singular route and can specify no data\n      return {\n        status: res.status,\n        data: {\n          data: null\n        }\n      };\n    }\n  }\n  invariant(res.body, \"No response body to decode\");\n  try {\n    let decoded = await decodeViaTurboStream(res.body, window);\n    return {\n      status: res.status,\n      data: decoded.value\n    };\n  } catch (e) {\n    console.error(e);\n    throw new Error(`Unable to decode turbo-stream response from URL: ${url.toString()}`);\n  }\n}\n\n// Note: If you change this function please change the corresponding\n// encodeViaTurboStream function in server-runtime\nfunction decodeViaTurboStream(body, global) {\n  return decode(body, {\n    plugins: [(type, ...rest) => {\n      // Decode Errors back into Error instances using the right type and with\n      // the right (potentially undefined) stacktrace\n      if (type === \"SanitizedError\") {\n        let [name, message, stack] = rest;\n        let Constructor = Error;\n        // @ts-expect-error\n        if (name && name in global && typeof global[name] === \"function\") {\n          // @ts-expect-error\n          Constructor = global[name];\n        }\n        let error = new Constructor(message);\n        error.stack = stack;\n        return {\n          value: error\n        };\n      }\n      if (type === \"ErrorResponse\") {\n        let [data, status, statusText] = rest;\n        return {\n          value: new UNSAFE_ErrorResponseImpl(status, statusText, data)\n        };\n      }\n      if (type === \"SingleFetchRedirect\") {\n        return {\n          value: {\n            [UNSAFE_SingleFetchRedirectSymbol]: rest[0]\n          }\n        };\n      }\n    }, (type, value) => {\n      if (type === \"SingleFetchFallback\") {\n        return {\n          value: undefined\n        };\n      }\n      if (type === \"SingleFetchClassInstance\") {\n        return {\n          value\n        };\n      }\n    }]\n  });\n}\nfunction unwrapSingleFetchResults(results, routeId) {\n  let redirect = results[UNSAFE_SingleFetchRedirectSymbol];\n  if (redirect) {\n    return unwrapSingleFetchResult(redirect, routeId);\n  }\n  return results[routeId] !== undefined ? unwrapSingleFetchResult(results[routeId], routeId) : null;\n}\nfunction unwrapSingleFetchResult(result, routeId) {\n  if (\"error\" in result) {\n    throw result.error;\n  } else if (\"redirect\" in result) {\n    let headers = {};\n    if (result.revalidate) {\n      headers[\"X-Remix-Revalidate\"] = \"yes\";\n    }\n    if (result.reload) {\n      headers[\"X-Remix-Reload-Document\"] = \"yes\";\n    }\n    if (result.replace) {\n      headers[\"X-Remix-Replace\"] = \"yes\";\n    }\n    throw redirect(result.redirect, {\n      status: result.status,\n      headers\n    });\n  } else if (\"data\" in result) {\n    return result.data;\n  } else {\n    throw new Error(`No response found for routeId \"${routeId}\"`);\n  }\n}\nfunction createDeferred() {\n  let resolve;\n  let reject;\n  let promise = new Promise((res, rej) => {\n    resolve = async val => {\n      res(val);\n      try {\n        await promise;\n      } catch (e) {}\n    };\n    reject = async error => {\n      rej(error);\n      try {\n        await promise;\n      } catch (e) {}\n    };\n  });\n  return {\n    promise,\n    //@ts-ignore\n    resolve,\n    //@ts-ignore\n    reject\n  };\n}\n\nexport { StreamTransfer, decodeViaTurboStream, getSingleFetchDataStrategy, singleFetchUrl };\n", "/**\n * @remix-run/react v2.15.3\n *\n * Copyright (c) Remix Software Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE.md file in the root directory of this source tree.\n *\n * @license MIT\n */\nimport * as React from 'react';\nimport { isRouteErrorResponse } from 'react-router-dom';\nimport { useRemixContext, Scripts } from './components.js';\n\nclass RemixErrorBoundary extends React.Component {\n  constructor(props) {\n    super(props);\n    this.state = {\n      error: props.error || null,\n      location: props.location\n    };\n  }\n  static getDerivedStateFromError(error) {\n    return {\n      error\n    };\n  }\n  static getDerivedStateFromProps(props, state) {\n    // When we get into an error state, the user will likely click \"back\" to the\n    // previous page that didn't have an error. Because this wraps the entire\n    // application (even the HTML!) that will have no effect--the error page\n    // continues to display. This gives us a mechanism to recover from the error\n    // when the location changes.\n    //\n    // Whether we're in an error state or not, we update the location in state\n    // so that when we are in an error state, it gets reset when a new location\n    // comes in and the user recovers from the error.\n    if (state.location !== props.location) {\n      return {\n        error: props.error || null,\n        location: props.location\n      };\n    }\n\n    // If we're not changing locations, preserve the location but still surface\n    // any new errors that may come through. We retain the existing error, we do\n    // this because the error provided from the app state may be cleared without\n    // the location changing.\n    return {\n      error: props.error || state.error,\n      location: state.location\n    };\n  }\n  render() {\n    if (this.state.error) {\n      return /*#__PURE__*/React.createElement(RemixRootDefaultErrorBoundary, {\n        error: this.state.error,\n        isOutsideRemixApp: true\n      });\n    } else {\n      return this.props.children;\n    }\n  }\n}\n\n/**\n * When app's don't provide a root level ErrorBoundary, we default to this.\n */\nfunction RemixRootDefaultErrorBoundary({\n  error,\n  isOutsideRemixApp\n}) {\n  console.error(error);\n  let heyDeveloper = /*#__PURE__*/React.createElement(\"script\", {\n    dangerouslySetInnerHTML: {\n      __html: `\n        console.log(\n          \"💿 Hey developer 👋. You can provide a way better UX than this when your app throws errors. Check out https://remix.run/guides/errors for more information.\"\n        );\n      `\n    }\n  });\n  if (isRouteErrorResponse(error)) {\n    return /*#__PURE__*/React.createElement(BoundaryShell, {\n      title: \"Unhandled Thrown Response!\"\n    }, /*#__PURE__*/React.createElement(\"h1\", {\n      style: {\n        fontSize: \"24px\"\n      }\n    }, error.status, \" \", error.statusText), heyDeveloper);\n  }\n  let errorInstance;\n  if (error instanceof Error) {\n    errorInstance = error;\n  } else {\n    let errorString = error == null ? \"Unknown Error\" : typeof error === \"object\" && \"toString\" in error ? error.toString() : JSON.stringify(error);\n    errorInstance = new Error(errorString);\n  }\n  return /*#__PURE__*/React.createElement(BoundaryShell, {\n    title: \"Application Error!\",\n    isOutsideRemixApp: isOutsideRemixApp\n  }, /*#__PURE__*/React.createElement(\"h1\", {\n    style: {\n      fontSize: \"24px\"\n    }\n  }, \"Application Error\"), /*#__PURE__*/React.createElement(\"pre\", {\n    style: {\n      padding: \"2rem\",\n      background: \"hsla(10, 50%, 50%, 0.1)\",\n      color: \"red\",\n      overflow: \"auto\"\n    }\n  }, errorInstance.stack), heyDeveloper);\n}\nfunction BoundaryShell({\n  title,\n  renderScripts,\n  isOutsideRemixApp,\n  children\n}) {\n  var _routeModules$root;\n  let {\n    routeModules\n  } = useRemixContext();\n\n  // Generally speaking, when the root route has a Layout we want to use that\n  // as the app shell instead of the default `BoundaryShell` wrapper markup below.\n  // This is true for `loader`/`action` errors, most render errors, and\n  // `HydrateFallback` scenarios.\n\n  // However, render errors thrown from the `Layout` present a bit of an issue\n  // because if the `Layout` itself throws during the `ErrorBoundary` pass and\n  // we bubble outside the `RouterProvider` to the wrapping `RemixErrorBoundary`,\n  // by returning only `children` here we'll be trying to append a `<div>` to\n  // the `document` and the DOM will throw, putting React into an error/hydration\n  // loop.\n\n  // Instead, if we're ever rendering from the outermost `RemixErrorBoundary`\n  // during hydration that wraps `RouterProvider`, then we can't trust the\n  // `Layout` and should fallback to the default app shell so we're always\n  // returning an `<html>` document.\n  if ((_routeModules$root = routeModules.root) !== null && _routeModules$root !== void 0 && _routeModules$root.Layout && !isOutsideRemixApp) {\n    return children;\n  }\n  return /*#__PURE__*/React.createElement(\"html\", {\n    lang: \"en\"\n  }, /*#__PURE__*/React.createElement(\"head\", null, /*#__PURE__*/React.createElement(\"meta\", {\n    charSet: \"utf-8\"\n  }), /*#__PURE__*/React.createElement(\"meta\", {\n    name: \"viewport\",\n    content: \"width=device-width,initial-scale=1,viewport-fit=cover\"\n  }), /*#__PURE__*/React.createElement(\"title\", null, title)), /*#__PURE__*/React.createElement(\"body\", null, /*#__PURE__*/React.createElement(\"main\", {\n    style: {\n      fontFamily: \"system-ui, sans-serif\",\n      padding: \"2rem\"\n    }\n  }, children, renderScripts ? /*#__PURE__*/React.createElement(Scripts, null) : null)));\n}\n\nexport { BoundaryShell, RemixErrorBoundary, RemixRootDefaultErrorBoundary };\n", "/**\n * @remix-run/react v2.15.3\n *\n * Copyright (c) Remix Software Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE.md file in the root directory of this source tree.\n *\n * @license MIT\n */\nimport * as React from 'react';\nimport { BoundaryShell } from './errorBoundaries.js';\n\n// If the user sets `clientLoader.hydrate=true` somewhere but does not\n// provide a `HydrateFallback` at any level of the tree, then we need to at\n// least include `<Scripts>` in the SSR so we can hydrate the app and call the\n// `clientLoader` functions\nfunction RemixRootDefaultHydrateFallback() {\n  return /*#__PURE__*/React.createElement(BoundaryShell, {\n    title: \"Loading...\",\n    renderScripts: true\n  }, /*#__PURE__*/React.createElement(\"script\", {\n    dangerouslySetInnerHTML: {\n      __html: `\n              console.log(\n                \"💿 Hey developer 👋. You can provide a way better UX than this \" +\n                \"when your app is loading JS modules and/or running \\`clientLoader\\` \" +\n                \"functions. Check out https://remix.run/route/hydrate-fallback \" +\n                \"for more information.\"\n              );\n            `\n    }\n  }));\n}\n\nexport { RemixRootDefaultHydrateFallback };\n", "/**\n * @remix-run/react v2.15.3\n *\n * Copyright (c) Remix Software Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE.md file in the root directory of this source tree.\n *\n * @license MIT\n */\nimport * as React from 'react';\nimport { UNSAFE_ErrorResponseImpl } from '@remix-run/router';\nimport { useRouteError, redirect } from 'react-router-dom';\nimport { loadRouteModule } from './routeModules.js';\nimport { fetchData, isRedirectResponse, isCatchResponse, isDeferredResponse, parseDeferredReadableStream, isDeferredData, isResponse } from './data.js';\nimport { prefetchStyleLinks } from './links.js';\nimport { RemixRootDefaultErrorBoundary } from './errorBoundaries.js';\nimport { RemixRootDefaultHydrateFallback } from './fallback.js';\nimport invariant from './invariant.js';\n\n// NOTE: make sure to change the Route in server-runtime if you change this\n\n// NOTE: make sure to change the EntryRoute in server-runtime if you change this\n\n// Create a map of routes by parentId to use recursively instead of\n// repeatedly filtering the manifest.\nfunction groupRoutesByParentId(manifest) {\n  let routes = {};\n  Object.values(manifest).forEach(route => {\n    let parentId = route.parentId || \"\";\n    if (!routes[parentId]) {\n      routes[parentId] = [];\n    }\n    routes[parentId].push(route);\n  });\n  return routes;\n}\nfunction getRouteComponents(route, routeModule, isSpaMode) {\n  let Component = getRouteModuleComponent(routeModule);\n  // HydrateFallback can only exist on the root route in SPA Mode\n  let HydrateFallback = routeModule.HydrateFallback && (!isSpaMode || route.id === \"root\") ? routeModule.HydrateFallback : route.id === \"root\" ? RemixRootDefaultHydrateFallback : undefined;\n  let ErrorBoundary = routeModule.ErrorBoundary ? routeModule.ErrorBoundary : route.id === \"root\" ? () => /*#__PURE__*/React.createElement(RemixRootDefaultErrorBoundary, {\n    error: useRouteError()\n  }) : undefined;\n  if (route.id === \"root\" && routeModule.Layout) {\n    return {\n      ...(Component ? {\n        element: /*#__PURE__*/React.createElement(routeModule.Layout, null, /*#__PURE__*/React.createElement(Component, null))\n      } : {\n        Component\n      }),\n      ...(ErrorBoundary ? {\n        errorElement: /*#__PURE__*/React.createElement(routeModule.Layout, null, /*#__PURE__*/React.createElement(ErrorBoundary, null))\n      } : {\n        ErrorBoundary\n      }),\n      ...(HydrateFallback ? {\n        hydrateFallbackElement: /*#__PURE__*/React.createElement(routeModule.Layout, null, /*#__PURE__*/React.createElement(HydrateFallback, null))\n      } : {\n        HydrateFallback\n      })\n    };\n  }\n  return {\n    Component,\n    ErrorBoundary,\n    HydrateFallback\n  };\n}\nfunction createServerRoutes(manifest, routeModules, future, isSpaMode, parentId = \"\", routesByParentId = groupRoutesByParentId(manifest), spaModeLazyPromise = Promise.resolve({\n  Component: () => null\n})) {\n  return (routesByParentId[parentId] || []).map(route => {\n    let routeModule = routeModules[route.id];\n    invariant(routeModule, \"No `routeModule` available to create server routes\");\n    let dataRoute = {\n      ...getRouteComponents(route, routeModule, isSpaMode),\n      caseSensitive: route.caseSensitive,\n      id: route.id,\n      index: route.index,\n      path: route.path,\n      handle: routeModule.handle,\n      // For SPA Mode, all routes are lazy except root.  However we tell the\n      // router root is also lazy here too since we don't need a full\n      // implementation - we just need a `lazy` prop to tell the RR rendering\n      // where to stop which is always at the root route in SPA mode\n      lazy: isSpaMode ? () => spaModeLazyPromise : undefined,\n      // For partial hydration rendering, we need to indicate when the route\n      // has a loader/clientLoader, but it won't ever be called during the static\n      // render, so just give it a no-op function so we can render down to the\n      // proper fallback\n      loader: route.hasLoader || route.hasClientLoader ? () => null : undefined\n      // We don't need action/shouldRevalidate on these routes since they're\n      // for a static render\n    };\n    let children = createServerRoutes(manifest, routeModules, future, isSpaMode, route.id, routesByParentId, spaModeLazyPromise);\n    if (children.length > 0) dataRoute.children = children;\n    return dataRoute;\n  });\n}\nfunction createClientRoutesWithHMRRevalidationOptOut(needsRevalidation, manifest, routeModulesCache, initialState, future, isSpaMode) {\n  return createClientRoutes(manifest, routeModulesCache, initialState, future, isSpaMode, \"\", groupRoutesByParentId(manifest), needsRevalidation);\n}\nfunction preventInvalidServerHandlerCall(type, route, isSpaMode) {\n  if (isSpaMode) {\n    let fn = type === \"action\" ? \"serverAction()\" : \"serverLoader()\";\n    let msg = `You cannot call ${fn} in SPA Mode (routeId: \"${route.id}\")`;\n    console.error(msg);\n    throw new UNSAFE_ErrorResponseImpl(400, \"Bad Request\", new Error(msg), true);\n  }\n  let fn = type === \"action\" ? \"serverAction()\" : \"serverLoader()\";\n  let msg = `You are trying to call ${fn} on a route that does not have a server ` + `${type} (routeId: \"${route.id}\")`;\n  if (type === \"loader\" && !route.hasLoader || type === \"action\" && !route.hasAction) {\n    console.error(msg);\n    throw new UNSAFE_ErrorResponseImpl(400, \"Bad Request\", new Error(msg), true);\n  }\n}\nfunction noActionDefinedError(type, routeId) {\n  let article = type === \"clientAction\" ? \"a\" : \"an\";\n  let msg = `Route \"${routeId}\" does not have ${article} ${type}, but you are trying to ` + `submit to it. To fix this, please add ${article} \\`${type}\\` function to the route`;\n  console.error(msg);\n  throw new UNSAFE_ErrorResponseImpl(405, \"Method Not Allowed\", new Error(msg), true);\n}\nfunction createClientRoutes(manifest, routeModulesCache, initialState, future, isSpaMode, parentId = \"\", routesByParentId = groupRoutesByParentId(manifest), needsRevalidation) {\n  return (routesByParentId[parentId] || []).map(route => {\n    let routeModule = routeModulesCache[route.id];\n\n    // Fetch data from the server either via single fetch or the standard `?_data`\n    // request.  Unwrap it when called via `serverLoader`/`serverAction` in a\n    // client handler, otherwise return the raw response for the router to unwrap\n    async function fetchServerHandlerAndMaybeUnwrap(request, unwrap, singleFetch) {\n      if (typeof singleFetch === \"function\") {\n        let result = await singleFetch();\n        return result;\n      }\n      let result = await fetchServerHandler(request, route);\n      return unwrap ? unwrapServerResponse(result) : result;\n    }\n    function fetchServerLoader(request, unwrap, singleFetch) {\n      if (!route.hasLoader) return Promise.resolve(null);\n      return fetchServerHandlerAndMaybeUnwrap(request, unwrap, singleFetch);\n    }\n    function fetchServerAction(request, unwrap, singleFetch) {\n      if (!route.hasAction) {\n        throw noActionDefinedError(\"action\", route.id);\n      }\n      return fetchServerHandlerAndMaybeUnwrap(request, unwrap, singleFetch);\n    }\n    async function prefetchStylesAndCallHandler(handler) {\n      // Only prefetch links if we exist in the routeModulesCache (critical modules\n      // and navigating back to pages previously loaded via route.lazy).  Initial\n      // execution of route.lazy (when the module is not in the cache) will handle\n      // prefetching style links via loadRouteModuleWithBlockingLinks.\n      let cachedModule = routeModulesCache[route.id];\n      let linkPrefetchPromise = cachedModule ? prefetchStyleLinks(route, cachedModule) : Promise.resolve();\n      try {\n        return handler();\n      } finally {\n        await linkPrefetchPromise;\n      }\n    }\n    let dataRoute = {\n      id: route.id,\n      index: route.index,\n      path: route.path\n    };\n    if (routeModule) {\n      var _initialState$loaderD, _initialState$errors, _routeModule$clientLo;\n      // Use critical path modules directly\n      Object.assign(dataRoute, {\n        ...dataRoute,\n        ...getRouteComponents(route, routeModule, isSpaMode),\n        handle: routeModule.handle,\n        shouldRevalidate: getShouldRevalidateFunction(future, routeModule, route.id, needsRevalidation)\n      });\n      let initialData = initialState === null || initialState === void 0 ? void 0 : (_initialState$loaderD = initialState.loaderData) === null || _initialState$loaderD === void 0 ? void 0 : _initialState$loaderD[route.id];\n      let initialError = initialState === null || initialState === void 0 ? void 0 : (_initialState$errors = initialState.errors) === null || _initialState$errors === void 0 ? void 0 : _initialState$errors[route.id];\n      let isHydrationRequest = needsRevalidation == null && (((_routeModule$clientLo = routeModule.clientLoader) === null || _routeModule$clientLo === void 0 ? void 0 : _routeModule$clientLo.hydrate) === true || !route.hasLoader);\n      dataRoute.loader = async ({\n        request,\n        params\n      }, singleFetch) => {\n        try {\n          let result = await prefetchStylesAndCallHandler(async () => {\n            invariant(routeModule, \"No `routeModule` available for critical-route loader\");\n            if (!routeModule.clientLoader) {\n              if (isSpaMode) return null;\n              // Call the server when no client loader exists\n              return fetchServerLoader(request, false, singleFetch);\n            }\n            return routeModule.clientLoader({\n              request,\n              params,\n              async serverLoader() {\n                preventInvalidServerHandlerCall(\"loader\", route, isSpaMode);\n\n                // On the first call, resolve with the server result\n                if (isHydrationRequest) {\n                  if (initialData !== undefined) {\n                    return initialData;\n                  }\n                  if (initialError !== undefined) {\n                    throw initialError;\n                  }\n                  return null;\n                }\n\n                // Call the server loader for client-side navigations\n                return fetchServerLoader(request, true, singleFetch);\n              }\n            });\n          });\n          return result;\n        } finally {\n          // Whether or not the user calls `serverLoader`, we only let this\n          // stick around as true for one loader call\n          isHydrationRequest = false;\n        }\n      };\n\n      // Let React Router know whether to run this on hydration\n      dataRoute.loader.hydrate = shouldHydrateRouteLoader(route, routeModule, isSpaMode);\n      dataRoute.action = ({\n        request,\n        params\n      }, singleFetch) => {\n        return prefetchStylesAndCallHandler(async () => {\n          invariant(routeModule, \"No `routeModule` available for critical-route action\");\n          if (!routeModule.clientAction) {\n            if (isSpaMode) {\n              throw noActionDefinedError(\"clientAction\", route.id);\n            }\n            return fetchServerAction(request, false, singleFetch);\n          }\n          return routeModule.clientAction({\n            request,\n            params,\n            async serverAction() {\n              preventInvalidServerHandlerCall(\"action\", route, isSpaMode);\n              return fetchServerAction(request, true, singleFetch);\n            }\n          });\n        });\n      };\n    } else {\n      // If the lazy route does not have a client loader/action we want to call\n      // the server loader/action in parallel with the module load so we add\n      // loader/action as static props on the route\n      if (!route.hasClientLoader) {\n        dataRoute.loader = ({\n          request\n        }, singleFetch) => prefetchStylesAndCallHandler(() => {\n          if (isSpaMode) return Promise.resolve(null);\n          return fetchServerLoader(request, false, singleFetch);\n        });\n      }\n      if (!route.hasClientAction) {\n        dataRoute.action = ({\n          request\n        }, singleFetch) => prefetchStylesAndCallHandler(() => {\n          if (isSpaMode) {\n            throw noActionDefinedError(\"clientAction\", route.id);\n          }\n          return fetchServerAction(request, false, singleFetch);\n        });\n      }\n\n      // Load all other modules via route.lazy()\n      dataRoute.lazy = async () => {\n        let mod = await loadRouteModuleWithBlockingLinks(route, routeModulesCache);\n        let lazyRoute = {\n          ...mod\n        };\n        if (mod.clientLoader) {\n          let clientLoader = mod.clientLoader;\n          lazyRoute.loader = (args, singleFetch) => clientLoader({\n            ...args,\n            async serverLoader() {\n              preventInvalidServerHandlerCall(\"loader\", route, isSpaMode);\n              return fetchServerLoader(args.request, true, singleFetch);\n            }\n          });\n        }\n        if (mod.clientAction) {\n          let clientAction = mod.clientAction;\n          lazyRoute.action = (args, singleFetch) => clientAction({\n            ...args,\n            async serverAction() {\n              preventInvalidServerHandlerCall(\"action\", route, isSpaMode);\n              return fetchServerAction(args.request, true, singleFetch);\n            }\n          });\n        }\n        return {\n          ...(lazyRoute.loader ? {\n            loader: lazyRoute.loader\n          } : {}),\n          ...(lazyRoute.action ? {\n            action: lazyRoute.action\n          } : {}),\n          hasErrorBoundary: lazyRoute.hasErrorBoundary,\n          shouldRevalidate: getShouldRevalidateFunction(future, lazyRoute, route.id, needsRevalidation),\n          handle: lazyRoute.handle,\n          // No need to wrap these in layout since the root route is never\n          // loaded via route.lazy()\n          Component: lazyRoute.Component,\n          ErrorBoundary: lazyRoute.ErrorBoundary\n        };\n      };\n    }\n    let children = createClientRoutes(manifest, routeModulesCache, initialState, future, isSpaMode, route.id, routesByParentId, needsRevalidation);\n    if (children.length > 0) dataRoute.children = children;\n    return dataRoute;\n  });\n}\nfunction getShouldRevalidateFunction(future, route, routeId, needsRevalidation) {\n  // During HDR we force revalidation for updated routes\n  if (needsRevalidation) {\n    return wrapShouldRevalidateForHdr(routeId, route.shouldRevalidate, needsRevalidation);\n  }\n\n  // Single fetch revalidates by default, so override the RR default value which\n  // matches the multi-fetch behavior with `true`\n  if (future.v3_singleFetch && route.shouldRevalidate) {\n    let fn = route.shouldRevalidate;\n    return opts => fn({\n      ...opts,\n      defaultShouldRevalidate: true\n    });\n  }\n  return route.shouldRevalidate;\n}\n\n// When an HMR / HDR update happens we opt out of all user-defined\n// revalidation logic and force a revalidation on the first call\nfunction wrapShouldRevalidateForHdr(routeId, routeShouldRevalidate, needsRevalidation) {\n  let handledRevalidation = false;\n  return arg => {\n    if (!handledRevalidation) {\n      handledRevalidation = true;\n      return needsRevalidation.has(routeId);\n    }\n    return routeShouldRevalidate ? routeShouldRevalidate(arg) : arg.defaultShouldRevalidate;\n  };\n}\nasync function loadRouteModuleWithBlockingLinks(route, routeModules) {\n  let routeModule = await loadRouteModule(route, routeModules);\n  await prefetchStyleLinks(route, routeModule);\n\n  // Include all `browserSafeRouteExports` fields, except `HydrateFallback`\n  // since those aren't used on lazily loaded routes\n  return {\n    Component: getRouteModuleComponent(routeModule),\n    ErrorBoundary: routeModule.ErrorBoundary,\n    clientAction: routeModule.clientAction,\n    clientLoader: routeModule.clientLoader,\n    handle: routeModule.handle,\n    links: routeModule.links,\n    meta: routeModule.meta,\n    shouldRevalidate: routeModule.shouldRevalidate\n  };\n}\nasync function fetchServerHandler(request, route) {\n  let result = await fetchData(request, route.id);\n  if (result instanceof Error) {\n    throw result;\n  }\n  if (isRedirectResponse(result)) {\n    throw getRedirect(result);\n  }\n  if (isCatchResponse(result)) {\n    throw result;\n  }\n  if (isDeferredResponse(result) && result.body) {\n    return await parseDeferredReadableStream(result.body);\n  }\n  return result;\n}\nfunction unwrapServerResponse(result) {\n  if (isDeferredData(result)) {\n    return result.data;\n  }\n  if (isResponse(result)) {\n    let contentType = result.headers.get(\"Content-Type\");\n    // Check between word boundaries instead of startsWith() due to the last\n    // paragraph of https://httpwg.org/specs/rfc9110.html#field.content-type\n    if (contentType && /\\bapplication\\/json\\b/.test(contentType)) {\n      return result.json();\n    } else {\n      return result.text();\n    }\n  }\n  return result;\n}\nfunction getRedirect(response) {\n  let status = parseInt(response.headers.get(\"X-Remix-Status\"), 10) || 302;\n  let url = response.headers.get(\"X-Remix-Redirect\");\n  let headers = {};\n  let revalidate = response.headers.get(\"X-Remix-Revalidate\");\n  if (revalidate) {\n    headers[\"X-Remix-Revalidate\"] = revalidate;\n  }\n  let reloadDocument = response.headers.get(\"X-Remix-Reload-Document\");\n  if (reloadDocument) {\n    headers[\"X-Remix-Reload-Document\"] = reloadDocument;\n  }\n  let replace = response.headers.get(\"X-Remix-Replace\");\n  if (replace) {\n    headers[\"X-Remix-Replace\"] = replace;\n  }\n  return redirect(url, {\n    status,\n    headers\n  });\n}\n\n// Our compiler generates the default export as `{}` when no default is provided,\n// which can lead us to trying to use that as a Component in RR and calling\n// createElement on it.  Patching here as a quick fix and hoping it's no longer\n// an issue in Vite.\nfunction getRouteModuleComponent(routeModule) {\n  if (routeModule.default == null) return undefined;\n  let isEmptyObject = typeof routeModule.default === \"object\" && Object.keys(routeModule.default).length === 0;\n  if (!isEmptyObject) {\n    return routeModule.default;\n  }\n}\nfunction shouldHydrateRouteLoader(route, routeModule, isSpaMode) {\n  return isSpaMode && route.id !== \"root\" || routeModule.clientLoader != null && (routeModule.clientLoader.hydrate === true || route.hasLoader !== true);\n}\n\nexport { createClientRoutes, createClientRoutesWithHMRRevalidationOptOut, createServerRoutes, shouldHydrateRouteLoader };\n", "/**\n * @remix-run/react v2.15.3\n *\n * Copyright (c) Remix Software Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE.md file in the root directory of this source tree.\n *\n * @license MIT\n */\nimport { matchRoutes } from '@remix-run/router';\nimport * as React from 'react';\nimport { createClientRoutes } from './routes.js';\n\n// Currently rendered links that may need prefetching\nconst nextPaths = new Set();\n\n// FIFO queue of previously discovered routes to prevent re-calling on\n// subsequent navigations to the same path\nconst discoveredPathsMaxSize = 1000;\nconst discoveredPaths = new Set();\n\n// 7.5k to come in under the ~8k limit for most browsers\n// https://stackoverflow.com/a/417184\nconst URL_LIMIT = 7680;\nfunction isFogOfWarEnabled(future, isSpaMode) {\n  return future.v3_lazyRouteDiscovery === true && !isSpaMode;\n}\nfunction getPartialManifest(manifest, router) {\n  // Start with our matches for this pathname\n  let routeIds = new Set(router.state.matches.map(m => m.route.id));\n  let segments = router.state.location.pathname.split(\"/\").filter(Boolean);\n  let paths = [\"/\"];\n\n  // We've already matched to the last segment\n  segments.pop();\n\n  // Traverse each path for our parents and match in case they have pathless/index\n  // children we need to include in the initial manifest\n  while (segments.length > 0) {\n    paths.push(`/${segments.join(\"/\")}`);\n    segments.pop();\n  }\n  paths.forEach(path => {\n    let matches = matchRoutes(router.routes, path, router.basename);\n    if (matches) {\n      matches.forEach(m => routeIds.add(m.route.id));\n    }\n  });\n  let initialRoutes = [...routeIds].reduce((acc, id) => Object.assign(acc, {\n    [id]: manifest.routes[id]\n  }), {});\n  return {\n    ...manifest,\n    routes: initialRoutes\n  };\n}\nfunction getPatchRoutesOnNavigationFunction(manifest, routeModules, future, isSpaMode, basename) {\n  if (!isFogOfWarEnabled(future, isSpaMode)) {\n    return undefined;\n  }\n  return async ({\n    path,\n    patch,\n    signal\n  }) => {\n    if (discoveredPaths.has(path)) {\n      return;\n    }\n    await fetchAndApplyManifestPatches([path], manifest, routeModules, future, isSpaMode, basename, patch, signal);\n  };\n}\nfunction useFogOFWarDiscovery(router, manifest, routeModules, future, isSpaMode) {\n  React.useEffect(() => {\n    var _navigator$connection;\n    // Don't prefetch if not enabled or if the user has `saveData` enabled\n    if (!isFogOfWarEnabled(future, isSpaMode) || ((_navigator$connection = navigator.connection) === null || _navigator$connection === void 0 ? void 0 : _navigator$connection.saveData) === true) {\n      return;\n    }\n\n    // Register a link href for patching\n    function registerElement(el) {\n      let path = el.tagName === \"FORM\" ? el.getAttribute(\"action\") : el.getAttribute(\"href\");\n      if (!path) {\n        return;\n      }\n      let url = new URL(path, window.location.origin);\n      if (!discoveredPaths.has(url.pathname)) {\n        nextPaths.add(url.pathname);\n      }\n    }\n\n    // Fetch patches for all currently rendered links\n    async function fetchPatches() {\n      let lazyPaths = Array.from(nextPaths.keys()).filter(path => {\n        if (discoveredPaths.has(path)) {\n          nextPaths.delete(path);\n          return false;\n        }\n        return true;\n      });\n      if (lazyPaths.length === 0) {\n        return;\n      }\n      try {\n        await fetchAndApplyManifestPatches(lazyPaths, manifest, routeModules, future, isSpaMode, router.basename, router.patchRoutes);\n      } catch (e) {\n        console.error(\"Failed to fetch manifest patches\", e);\n      }\n    }\n\n    // Register and fetch patches for all initially-rendered links/forms\n    document.body.querySelectorAll(\"a[data-discover], form[data-discover]\").forEach(el => registerElement(el));\n    fetchPatches();\n\n    // Setup a MutationObserver to fetch all subsequently rendered links/forms\n    let debouncedFetchPatches = debounce(fetchPatches, 100);\n    function isElement(node) {\n      return node.nodeType === Node.ELEMENT_NODE;\n    }\n    let observer = new MutationObserver(records => {\n      let elements = new Set();\n      records.forEach(r => {\n        [r.target, ...r.addedNodes].forEach(node => {\n          if (!isElement(node)) return;\n          if (node.tagName === \"A\" && node.getAttribute(\"data-discover\")) {\n            elements.add(node);\n          } else if (node.tagName === \"FORM\" && node.getAttribute(\"data-discover\")) {\n            elements.add(node);\n          }\n          if (node.tagName !== \"A\") {\n            node.querySelectorAll(\"a[data-discover], form[data-discover]\").forEach(el => elements.add(el));\n          }\n        });\n      });\n      elements.forEach(el => registerElement(el));\n      debouncedFetchPatches();\n    });\n    observer.observe(document.documentElement, {\n      subtree: true,\n      childList: true,\n      attributes: true,\n      attributeFilter: [\"data-discover\", \"href\", \"action\"]\n    });\n    return () => observer.disconnect();\n  }, [future, isSpaMode, manifest, routeModules, router]);\n}\nasync function fetchAndApplyManifestPatches(paths, manifest, routeModules, future, isSpaMode, basename, patchRoutes, signal) {\n  let manifestPath = `${basename ?? \"/\"}/__manifest`.replace(/\\/+/g, \"/\");\n  let url = new URL(manifestPath, window.location.origin);\n  paths.sort().forEach(path => url.searchParams.append(\"p\", path));\n  url.searchParams.set(\"version\", manifest.version);\n\n  // If the URL is nearing the ~8k limit on GET requests, skip this optimization\n  // step and just let discovery happen on link click.  We also wipe out the\n  // nextPaths Set here so we can start filling it with fresh links\n  if (url.toString().length > URL_LIMIT) {\n    nextPaths.clear();\n    return;\n  }\n  let serverPatches;\n  try {\n    let res = await fetch(url, {\n      signal\n    });\n    if (!res.ok) {\n      throw new Error(`${res.status} ${res.statusText}`);\n    } else if (res.status >= 400) {\n      throw new Error(await res.text());\n    }\n    serverPatches = await res.json();\n  } catch (e) {\n    if (signal !== null && signal !== void 0 && signal.aborted) return;\n    throw e;\n  }\n\n  // Patch routes we don't know about yet into the manifest\n  let knownRoutes = new Set(Object.keys(manifest.routes));\n  let patches = Object.values(serverPatches).reduce((acc, route) => !knownRoutes.has(route.id) ? Object.assign(acc, {\n    [route.id]: route\n  }) : acc, {});\n  Object.assign(manifest.routes, patches);\n\n  // Track discovered paths so we don't have to fetch them again\n  paths.forEach(p => addToFifoQueue(p, discoveredPaths));\n\n  // Identify all parentIds for which we have new children to add and patch\n  // in their new children\n  let parentIds = new Set();\n  Object.values(patches).forEach(patch => {\n    if (!patch.parentId || !patches[patch.parentId]) {\n      parentIds.add(patch.parentId);\n    }\n  });\n  parentIds.forEach(parentId => patchRoutes(parentId || null, createClientRoutes(patches, routeModules, null, future, isSpaMode, parentId)));\n}\nfunction addToFifoQueue(path, queue) {\n  if (queue.size >= discoveredPathsMaxSize) {\n    let first = queue.values().next().value;\n    if (typeof first === \"string\") queue.delete(first);\n  }\n  queue.add(path);\n}\n\n// Thanks Josh!\n// https://www.joshwcomeau.com/snippets/javascript/debounce/\nfunction debounce(callback, wait) {\n  let timeoutId;\n  return (...args) => {\n    window.clearTimeout(timeoutId);\n    timeoutId = window.setTimeout(() => callback(...args), wait);\n  };\n}\n\nexport { fetchAndApplyManifestPatches, getPartialManifest, getPatchRoutesOnNavigationFunction, isFogOfWarEnabled, useFogOFWarDiscovery };\n", "/**\n * @remix-run/react v2.15.3\n *\n * Copyright (c) Remix Software Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE.md file in the root directory of this source tree.\n *\n * @license MIT\n */\nimport { extends as _extends } from './_virtual/_rollupPluginBabelHelpers.js';\nimport * as React from 'react';\nimport { useHref, NavLink as NavLink$1, Link as Link$1, Form as Form$1, matchRoutes, useLocation, Await as Await$1, useAsyncError, useMatches as useMatches$1, useLoaderData as useLoaderData$1, useRouteLoaderData as useRouteLoaderData$1, useActionData as useActionData$1, use<PERSON><PERSON><PERSON> as useFetcher$1, UNSAFE_DataRouterContext, UNSAFE_DataRouterStateContext } from 'react-router-dom';\nimport invariant from './invariant.js';\nimport { getKeyedLinksForMatches, isPageLinkDescriptor, getNewMatchesForLinks, getDataLinkHrefs, getModuleLinkHrefs, getKeyedPrefetchLinks } from './links.js';\nimport { escapeHtml, createHtml } from './markup.js';\nimport { singleFetchUrl } from './single-fetch.js';\nimport { isFogOfWarEnabled, getPartialManifest } from './fog-of-war.js';\n\nfunction useDataRouterContext() {\n  let context = React.useContext(UNSAFE_DataRouterContext);\n  invariant(context, \"You must render this element inside a <DataRouterContext.Provider> element\");\n  return context;\n}\nfunction useDataRouterStateContext() {\n  let context = React.useContext(UNSAFE_DataRouterStateContext);\n  invariant(context, \"You must render this element inside a <DataRouterStateContext.Provider> element\");\n  return context;\n}\n\n////////////////////////////////////////////////////////////////////////////////\n// RemixContext\n\nconst RemixContext = /*#__PURE__*/React.createContext(undefined);\nRemixContext.displayName = \"Remix\";\nfunction useRemixContext() {\n  let context = React.useContext(RemixContext);\n  invariant(context, \"You must render this element inside a <Remix> element\");\n  return context;\n}\n\n////////////////////////////////////////////////////////////////////////////////\n// Public API\n\n/**\n * Defines the discovery behavior of the link:\n *\n * - \"render\": Eagerly discover when the link is rendered (default)\n * - \"none\": No eager discovery - discover when the link is clicked\n */\n\n/**\n * Defines the prefetching behavior of the link:\n *\n * - \"none\": Never fetched\n * - \"intent\": Fetched when the user focuses or hovers the link\n * - \"render\": Fetched when the link is rendered\n * - \"viewport\": Fetched when the link is in the viewport\n */\n\nfunction usePrefetchBehavior(prefetch, theirElementProps) {\n  let [maybePrefetch, setMaybePrefetch] = React.useState(false);\n  let [shouldPrefetch, setShouldPrefetch] = React.useState(false);\n  let {\n    onFocus,\n    onBlur,\n    onMouseEnter,\n    onMouseLeave,\n    onTouchStart\n  } = theirElementProps;\n  let ref = React.useRef(null);\n  React.useEffect(() => {\n    if (prefetch === \"render\") {\n      setShouldPrefetch(true);\n    }\n    if (prefetch === \"viewport\") {\n      let callback = entries => {\n        entries.forEach(entry => {\n          setShouldPrefetch(entry.isIntersecting);\n        });\n      };\n      let observer = new IntersectionObserver(callback, {\n        threshold: 0.5\n      });\n      if (ref.current) observer.observe(ref.current);\n      return () => {\n        observer.disconnect();\n      };\n    }\n  }, [prefetch]);\n  let setIntent = () => {\n    if (prefetch === \"intent\") {\n      setMaybePrefetch(true);\n    }\n  };\n  let cancelIntent = () => {\n    if (prefetch === \"intent\") {\n      setMaybePrefetch(false);\n      setShouldPrefetch(false);\n    }\n  };\n  React.useEffect(() => {\n    if (maybePrefetch) {\n      let id = setTimeout(() => {\n        setShouldPrefetch(true);\n      }, 100);\n      return () => {\n        clearTimeout(id);\n      };\n    }\n  }, [maybePrefetch]);\n  return [shouldPrefetch, ref, {\n    onFocus: composeEventHandlers(onFocus, setIntent),\n    onBlur: composeEventHandlers(onBlur, cancelIntent),\n    onMouseEnter: composeEventHandlers(onMouseEnter, setIntent),\n    onMouseLeave: composeEventHandlers(onMouseLeave, cancelIntent),\n    onTouchStart: composeEventHandlers(onTouchStart, setIntent)\n  }];\n}\nconst ABSOLUTE_URL_REGEX = /^(?:[a-z][a-z0-9+.-]*:|\\/\\/)/i;\nfunction getDiscoverAttr(discover, isAbsolute, reloadDocument) {\n  return discover === \"render\" && !isAbsolute && !reloadDocument ? \"true\" : undefined;\n}\n\n/**\n * A special kind of `<Link>` that knows whether it is \"active\".\n *\n * @see https://remix.run/components/nav-link\n */\nlet NavLink = /*#__PURE__*/React.forwardRef(({\n  to,\n  prefetch = \"none\",\n  discover = \"render\",\n  ...props\n}, forwardedRef) => {\n  let isAbsolute = typeof to === \"string\" && ABSOLUTE_URL_REGEX.test(to);\n  let href = useHref(to);\n  let [shouldPrefetch, ref, prefetchHandlers] = usePrefetchBehavior(prefetch, props);\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(NavLink$1, _extends({}, props, prefetchHandlers, {\n    ref: mergeRefs(forwardedRef, ref),\n    to: to,\n    \"data-discover\": getDiscoverAttr(discover, isAbsolute, props.reloadDocument)\n  })), shouldPrefetch && !isAbsolute ? /*#__PURE__*/React.createElement(PrefetchPageLinks, {\n    page: href\n  }) : null);\n});\nNavLink.displayName = \"NavLink\";\n\n/**\n * This component renders an anchor tag and is the primary way the user will\n * navigate around your website.\n *\n * @see https://remix.run/components/link\n */\nlet Link = /*#__PURE__*/React.forwardRef(({\n  to,\n  prefetch = \"none\",\n  discover = \"render\",\n  ...props\n}, forwardedRef) => {\n  let isAbsolute = typeof to === \"string\" && ABSOLUTE_URL_REGEX.test(to);\n  let href = useHref(to);\n  let [shouldPrefetch, ref, prefetchHandlers] = usePrefetchBehavior(prefetch, props);\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(Link$1, _extends({}, props, prefetchHandlers, {\n    ref: mergeRefs(forwardedRef, ref),\n    to: to,\n    \"data-discover\": getDiscoverAttr(discover, isAbsolute, props.reloadDocument)\n  })), shouldPrefetch && !isAbsolute ? /*#__PURE__*/React.createElement(PrefetchPageLinks, {\n    page: href\n  }) : null);\n});\nLink.displayName = \"Link\";\n/**\n * This component renders a form tag and is the primary way the user will\n * submit information via your website.\n *\n * @see https://remix.run/components/form\n */\nlet Form = /*#__PURE__*/React.forwardRef(({\n  discover = \"render\",\n  ...props\n}, forwardedRef) => {\n  let isAbsolute = typeof props.action === \"string\" && ABSOLUTE_URL_REGEX.test(props.action);\n  return /*#__PURE__*/React.createElement(Form$1, _extends({}, props, {\n    ref: forwardedRef,\n    \"data-discover\": getDiscoverAttr(discover, isAbsolute, props.reloadDocument)\n  }));\n});\nForm.displayName = \"Form\";\nfunction composeEventHandlers(theirHandler, ourHandler) {\n  return event => {\n    theirHandler && theirHandler(event);\n    if (!event.defaultPrevented) {\n      ourHandler(event);\n    }\n  };\n}\n\n// Return the matches actively being displayed:\n// - In SPA Mode we only SSR/hydrate the root match, and include all matches\n//   after hydration. This lets the router handle initial match loads via lazy().\n// - When an error boundary is rendered, we slice off matches up to the\n//   boundary for <Links>/<Meta>\nfunction getActiveMatches(matches, errors, isSpaMode) {\n  if (isSpaMode && !isHydrated) {\n    return [matches[0]];\n  }\n  if (errors) {\n    let errorIdx = matches.findIndex(m => errors[m.route.id] !== undefined);\n    return matches.slice(0, errorIdx + 1);\n  }\n  return matches;\n}\n\n/**\n * Renders the `<link>` tags for the current routes.\n *\n * @see https://remix.run/components/links\n */\nfunction Links() {\n  let {\n    isSpaMode,\n    manifest,\n    routeModules,\n    criticalCss\n  } = useRemixContext();\n  let {\n    errors,\n    matches: routerMatches\n  } = useDataRouterStateContext();\n  let matches = getActiveMatches(routerMatches, errors, isSpaMode);\n  let keyedLinks = React.useMemo(() => getKeyedLinksForMatches(matches, routeModules, manifest), [matches, routeModules, manifest]);\n  return /*#__PURE__*/React.createElement(React.Fragment, null, criticalCss ? /*#__PURE__*/React.createElement(\"style\", {\n    dangerouslySetInnerHTML: {\n      __html: criticalCss\n    }\n  }) : null, keyedLinks.map(({\n    key,\n    link\n  }) => isPageLinkDescriptor(link) ? /*#__PURE__*/React.createElement(PrefetchPageLinks, _extends({\n    key: key\n  }, link)) : /*#__PURE__*/React.createElement(\"link\", _extends({\n    key: key\n  }, link))));\n}\n\n/**\n * This component renders all the `<link rel=\"prefetch\">` and\n * `<link rel=\"modulepreload\"/>` tags for all the assets (data, modules, css) of\n * a given page.\n *\n * @param props\n * @param props.page\n * @see https://remix.run/components/prefetch-page-links\n */\nfunction PrefetchPageLinks({\n  page,\n  ...dataLinkProps\n}) {\n  let {\n    router\n  } = useDataRouterContext();\n  let matches = React.useMemo(() => matchRoutes(router.routes, page, router.basename), [router.routes, page, router.basename]);\n  if (!matches) {\n    console.warn(`Tried to prefetch ${page} but no routes matched.`);\n    return null;\n  }\n  return /*#__PURE__*/React.createElement(PrefetchPageLinksImpl, _extends({\n    page: page,\n    matches: matches\n  }, dataLinkProps));\n}\nfunction useKeyedPrefetchLinks(matches) {\n  let {\n    manifest,\n    routeModules\n  } = useRemixContext();\n  let [keyedPrefetchLinks, setKeyedPrefetchLinks] = React.useState([]);\n  React.useEffect(() => {\n    let interrupted = false;\n    void getKeyedPrefetchLinks(matches, manifest, routeModules).then(links => {\n      if (!interrupted) {\n        setKeyedPrefetchLinks(links);\n      }\n    });\n    return () => {\n      interrupted = true;\n    };\n  }, [matches, manifest, routeModules]);\n  return keyedPrefetchLinks;\n}\nfunction PrefetchPageLinksImpl({\n  page,\n  matches: nextMatches,\n  ...linkProps\n}) {\n  let location = useLocation();\n  let {\n    future,\n    manifest,\n    routeModules\n  } = useRemixContext();\n  let {\n    loaderData,\n    matches\n  } = useDataRouterStateContext();\n  let newMatchesForData = React.useMemo(() => getNewMatchesForLinks(page, nextMatches, matches, manifest, location, future, \"data\"), [page, nextMatches, matches, manifest, location, future]);\n  let dataHrefs = React.useMemo(() => {\n    if (!future.v3_singleFetch) {\n      return getDataLinkHrefs(page, newMatchesForData, manifest);\n    }\n    if (page === location.pathname + location.search + location.hash) {\n      // Because we opt-into revalidation, don't compute this for the current page\n      // since it would always trigger a prefetch of the existing loaders\n      return [];\n    }\n\n    // Single-fetch is harder :)\n    // This parallels the logic in the single fetch data strategy\n    let routesParams = new Set();\n    let foundOptOutRoute = false;\n    nextMatches.forEach(m => {\n      var _routeModules$m$route;\n      if (!manifest.routes[m.route.id].hasLoader) {\n        return;\n      }\n      if (!newMatchesForData.some(m2 => m2.route.id === m.route.id) && m.route.id in loaderData && (_routeModules$m$route = routeModules[m.route.id]) !== null && _routeModules$m$route !== void 0 && _routeModules$m$route.shouldRevalidate) {\n        foundOptOutRoute = true;\n      } else if (manifest.routes[m.route.id].hasClientLoader) {\n        foundOptOutRoute = true;\n      } else {\n        routesParams.add(m.route.id);\n      }\n    });\n    if (routesParams.size === 0) {\n      return [];\n    }\n    let url = singleFetchUrl(page);\n    // When one or more routes have opted out, we add a _routes param to\n    // limit the loaders to those that have a server loader and did not\n    // opt out\n    if (foundOptOutRoute && routesParams.size > 0) {\n      url.searchParams.set(\"_routes\", nextMatches.filter(m => routesParams.has(m.route.id)).map(m => m.route.id).join(\",\"));\n    }\n    return [url.pathname + url.search];\n  }, [future.v3_singleFetch, loaderData, location, manifest, newMatchesForData, nextMatches, page, routeModules]);\n  let newMatchesForAssets = React.useMemo(() => getNewMatchesForLinks(page, nextMatches, matches, manifest, location, future, \"assets\"), [page, nextMatches, matches, manifest, location, future]);\n  let moduleHrefs = React.useMemo(() => getModuleLinkHrefs(newMatchesForAssets, manifest), [newMatchesForAssets, manifest]);\n\n  // needs to be a hook with async behavior because we need the modules, not\n  // just the manifest like the other links in here.\n  let keyedPrefetchLinks = useKeyedPrefetchLinks(newMatchesForAssets);\n  return /*#__PURE__*/React.createElement(React.Fragment, null, dataHrefs.map(href => /*#__PURE__*/React.createElement(\"link\", _extends({\n    key: href,\n    rel: \"prefetch\",\n    as: \"fetch\",\n    href: href\n  }, linkProps))), moduleHrefs.map(href => /*#__PURE__*/React.createElement(\"link\", _extends({\n    key: href,\n    rel: \"modulepreload\",\n    href: href\n  }, linkProps))), keyedPrefetchLinks.map(({\n    key,\n    link\n  }) =>\n  /*#__PURE__*/\n  // these don't spread `linkProps` because they are full link descriptors\n  // already with their own props\n  React.createElement(\"link\", _extends({\n    key: key\n  }, link))));\n}\n\n/**\n * Renders HTML tags related to metadata for the current route.\n *\n * @see https://remix.run/components/meta\n */\nfunction Meta() {\n  let {\n    isSpaMode,\n    routeModules\n  } = useRemixContext();\n  let {\n    errors,\n    matches: routerMatches,\n    loaderData\n  } = useDataRouterStateContext();\n  let location = useLocation();\n  let _matches = getActiveMatches(routerMatches, errors, isSpaMode);\n  let error = null;\n  if (errors) {\n    error = errors[_matches[_matches.length - 1].route.id];\n  }\n  let meta = [];\n  let leafMeta = null;\n  let matches = [];\n  for (let i = 0; i < _matches.length; i++) {\n    let _match = _matches[i];\n    let routeId = _match.route.id;\n    let data = loaderData[routeId];\n    let params = _match.params;\n    let routeModule = routeModules[routeId];\n    let routeMeta = [];\n    let match = {\n      id: routeId,\n      data,\n      meta: [],\n      params: _match.params,\n      pathname: _match.pathname,\n      handle: _match.route.handle,\n      error\n    };\n    matches[i] = match;\n    if (routeModule !== null && routeModule !== void 0 && routeModule.meta) {\n      routeMeta = typeof routeModule.meta === \"function\" ? routeModule.meta({\n        data,\n        params,\n        location,\n        matches,\n        error\n      }) : Array.isArray(routeModule.meta) ? [...routeModule.meta] : routeModule.meta;\n    } else if (leafMeta) {\n      // We only assign the route's meta to the nearest leaf if there is no meta\n      // export in the route. The meta function may return a falsy value which\n      // is effectively the same as an empty array.\n      routeMeta = [...leafMeta];\n    }\n    routeMeta = routeMeta || [];\n    if (!Array.isArray(routeMeta)) {\n      throw new Error(\"The route at \" + _match.route.path + \" returns an invalid value. All route meta functions must \" + \"return an array of meta objects.\" + \"\\n\\nTo reference the meta function API, see https://remix.run/route/meta\");\n    }\n    match.meta = routeMeta;\n    matches[i] = match;\n    meta = [...routeMeta];\n    leafMeta = meta;\n  }\n  return /*#__PURE__*/React.createElement(React.Fragment, null, meta.flat().map(metaProps => {\n    if (!metaProps) {\n      return null;\n    }\n    if (\"tagName\" in metaProps) {\n      let {\n        tagName,\n        ...rest\n      } = metaProps;\n      if (!isValidMetaTag(tagName)) {\n        console.warn(`A meta object uses an invalid tagName: ${tagName}. Expected either 'link' or 'meta'`);\n        return null;\n      }\n      let Comp = tagName;\n      return /*#__PURE__*/React.createElement(Comp, _extends({\n        key: JSON.stringify(rest)\n      }, rest));\n    }\n    if (\"title\" in metaProps) {\n      return /*#__PURE__*/React.createElement(\"title\", {\n        key: \"title\"\n      }, String(metaProps.title));\n    }\n    if (\"charset\" in metaProps) {\n      metaProps.charSet ??= metaProps.charset;\n      delete metaProps.charset;\n    }\n    if (\"charSet\" in metaProps && metaProps.charSet != null) {\n      return typeof metaProps.charSet === \"string\" ? /*#__PURE__*/React.createElement(\"meta\", {\n        key: \"charSet\",\n        charSet: metaProps.charSet\n      }) : null;\n    }\n    if (\"script:ld+json\" in metaProps) {\n      try {\n        let json = JSON.stringify(metaProps[\"script:ld+json\"]);\n        return /*#__PURE__*/React.createElement(\"script\", {\n          key: `script:ld+json:${json}`,\n          type: \"application/ld+json\",\n          dangerouslySetInnerHTML: {\n            __html: json\n          }\n        });\n      } catch (err) {\n        return null;\n      }\n    }\n    return /*#__PURE__*/React.createElement(\"meta\", _extends({\n      key: JSON.stringify(metaProps)\n    }, metaProps));\n  }));\n}\nfunction isValidMetaTag(tagName) {\n  return typeof tagName === \"string\" && /^(meta|link)$/.test(tagName);\n}\nfunction Await(props) {\n  return /*#__PURE__*/React.createElement(Await$1, props);\n}\n\n/**\n * Tracks whether Remix has finished hydrating or not, so scripts can be skipped\n * during client-side updates.\n */\nlet isHydrated = false;\n/**\n * Renders the `<script>` tags needed for the initial render. Bundles for\n * additional routes are loaded later as needed.\n *\n * @param props Additional properties to add to each script tag that is rendered.\n * In addition to scripts, \\<link rel=\"modulepreload\"> tags receive the crossOrigin\n * property if provided.\n *\n * @see https://remix.run/components/scripts\n */\nfunction Scripts(props) {\n  let {\n    manifest,\n    serverHandoffString,\n    abortDelay,\n    serializeError,\n    isSpaMode,\n    future,\n    renderMeta\n  } = useRemixContext();\n  let {\n    router,\n    static: isStatic,\n    staticContext\n  } = useDataRouterContext();\n  let {\n    matches: routerMatches\n  } = useDataRouterStateContext();\n  let enableFogOfWar = isFogOfWarEnabled(future, isSpaMode);\n\n  // Let <RemixServer> know that we hydrated and we should render the single\n  // fetch streaming scripts\n  if (renderMeta) {\n    renderMeta.didRenderScripts = true;\n  }\n  let matches = getActiveMatches(routerMatches, null, isSpaMode);\n  React.useEffect(() => {\n    isHydrated = true;\n  }, []);\n  let serializePreResolvedErrorImp = (key, error) => {\n    let toSerialize;\n    if (serializeError && error instanceof Error) {\n      toSerialize = serializeError(error);\n    } else {\n      toSerialize = error;\n    }\n    return `${JSON.stringify(key)}:__remixContext.p(!1, ${escapeHtml(JSON.stringify(toSerialize))})`;\n  };\n  let serializePreresolvedDataImp = (routeId, key, data) => {\n    let serializedData;\n    try {\n      serializedData = JSON.stringify(data);\n    } catch (error) {\n      return serializePreResolvedErrorImp(key, error);\n    }\n    return `${JSON.stringify(key)}:__remixContext.p(${escapeHtml(serializedData)})`;\n  };\n  let serializeErrorImp = (routeId, key, error) => {\n    let toSerialize;\n    if (serializeError && error instanceof Error) {\n      toSerialize = serializeError(error);\n    } else {\n      toSerialize = error;\n    }\n    return `__remixContext.r(${JSON.stringify(routeId)}, ${JSON.stringify(key)}, !1, ${escapeHtml(JSON.stringify(toSerialize))})`;\n  };\n  let serializeDataImp = (routeId, key, data) => {\n    let serializedData;\n    try {\n      serializedData = JSON.stringify(data);\n    } catch (error) {\n      return serializeErrorImp(routeId, key, error);\n    }\n    return `__remixContext.r(${JSON.stringify(routeId)}, ${JSON.stringify(key)}, ${escapeHtml(serializedData)})`;\n  };\n  let deferredScripts = [];\n  let initialScripts = React.useMemo(() => {\n    var _manifest$hmr;\n    let streamScript = future.v3_singleFetch ?\n    // prettier-ignore\n    \"window.__remixContext.stream = new ReadableStream({\" + \"start(controller){\" + \"window.__remixContext.streamController = controller;\" + \"}\" + \"}).pipeThrough(new TextEncoderStream());\" : \"\";\n    let contextScript = staticContext ? `window.__remixContext = ${serverHandoffString};${streamScript}` : \" \";\n\n    // When single fetch is enabled, deferred is handled by turbo-stream\n    let activeDeferreds = future.v3_singleFetch ? undefined : staticContext === null || staticContext === void 0 ? void 0 : staticContext.activeDeferreds;\n\n    // This sets up the __remixContext with utility functions used by the\n    // deferred scripts.\n    // - __remixContext.p is a function that takes a resolved value or error and returns a promise.\n    //   This is used for transmitting pre-resolved promises from the server to the client.\n    // - __remixContext.n is a function that takes a routeID and key to returns a promise for later\n    //   resolution by the subsequently streamed chunks.\n    // - __remixContext.r is a function that takes a routeID, key and value or error and resolves\n    //   the promise created by __remixContext.n.\n    // - __remixContext.t is a map or routeId to keys to an object containing `e` and `r` methods\n    //   to resolve or reject the promise created by __remixContext.n.\n    // - __remixContext.a is the active number of deferred scripts that should be rendered to match\n    //   the SSR tree for hydration on the client.\n    contextScript += !activeDeferreds ? \"\" : [\"__remixContext.p = function(v,e,p,x) {\", \"  if (typeof e !== 'undefined') {\", process.env.NODE_ENV === \"development\" ? \"    x=new Error(e.message);\\n    x.stack=e.stack;\" : '    x=new Error(\"Unexpected Server Error\");\\n    x.stack=undefined;', \"    p=Promise.reject(x);\", \"  } else {\", \"    p=Promise.resolve(v);\", \"  }\", \"  return p;\", \"};\", \"__remixContext.n = function(i,k) {\", \"  __remixContext.t = __remixContext.t || {};\", \"  __remixContext.t[i] = __remixContext.t[i] || {};\", \"  let p = new Promise((r, e) => {__remixContext.t[i][k] = {r:(v)=>{r(v);},e:(v)=>{e(v);}};});\", typeof abortDelay === \"number\" ? `setTimeout(() => {if(typeof p._error !== \"undefined\" || typeof p._data !== \"undefined\"){return;} __remixContext.t[i][k].e(new Error(\"Server timeout.\"))}, ${abortDelay});` : \"\", \"  return p;\", \"};\", \"__remixContext.r = function(i,k,v,e,p,x) {\", \"  p = __remixContext.t[i][k];\", \"  if (typeof e !== 'undefined') {\", process.env.NODE_ENV === \"development\" ? \"    x=new Error(e.message);\\n    x.stack=e.stack;\" : '    x=new Error(\"Unexpected Server Error\");\\n    x.stack=undefined;', \"    p.e(x);\", \"  } else {\", \"    p.r(v);\", \"  }\", \"};\"].join(\"\\n\") + Object.entries(activeDeferreds).map(([routeId, deferredData]) => {\n      let pendingKeys = new Set(deferredData.pendingKeys);\n      let promiseKeyValues = deferredData.deferredKeys.map(key => {\n        if (pendingKeys.has(key)) {\n          deferredScripts.push( /*#__PURE__*/React.createElement(DeferredHydrationScript, {\n            key: `${routeId} | ${key}`,\n            deferredData: deferredData,\n            routeId: routeId,\n            dataKey: key,\n            scriptProps: props,\n            serializeData: serializeDataImp,\n            serializeError: serializeErrorImp\n          }));\n          return `${JSON.stringify(key)}:__remixContext.n(${JSON.stringify(routeId)}, ${JSON.stringify(key)})`;\n        } else {\n          let trackedPromise = deferredData.data[key];\n          if (typeof trackedPromise._error !== \"undefined\") {\n            return serializePreResolvedErrorImp(key, trackedPromise._error);\n          } else {\n            return serializePreresolvedDataImp(routeId, key, trackedPromise._data);\n          }\n        }\n      }).join(\",\\n\");\n      return `Object.assign(__remixContext.state.loaderData[${JSON.stringify(routeId)}], {${promiseKeyValues}});`;\n    }).join(\"\\n\") + (deferredScripts.length > 0 ? `__remixContext.a=${deferredScripts.length};` : \"\");\n    let routeModulesScript = !isStatic ? \" \" : `${(_manifest$hmr = manifest.hmr) !== null && _manifest$hmr !== void 0 && _manifest$hmr.runtime ? `import ${JSON.stringify(manifest.hmr.runtime)};` : \"\"}${enableFogOfWar ? \"\" : `import ${JSON.stringify(manifest.url)}`};\n${matches.map((match, index) => `import * as route${index} from ${JSON.stringify(manifest.routes[match.route.id].module)};`).join(\"\\n\")}\n${enableFogOfWar ?\n    // Inline a minimal manifest with the SSR matches\n    `window.__remixManifest = ${JSON.stringify(getPartialManifest(manifest, router), null, 2)};` : \"\"}\nwindow.__remixRouteModules = {${matches.map((match, index) => `${JSON.stringify(match.route.id)}:route${index}`).join(\",\")}};\n\nimport(${JSON.stringify(manifest.entry.module)});`;\n    return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"script\", _extends({}, props, {\n      suppressHydrationWarning: true,\n      dangerouslySetInnerHTML: createHtml(contextScript),\n      type: undefined\n    })), /*#__PURE__*/React.createElement(\"script\", _extends({}, props, {\n      suppressHydrationWarning: true,\n      dangerouslySetInnerHTML: createHtml(routeModulesScript),\n      type: \"module\",\n      async: true\n    })));\n    // disabled deps array because we are purposefully only rendering this once\n    // for hydration, after that we want to just continue rendering the initial\n    // scripts as they were when the page first loaded\n    // eslint-disable-next-line\n  }, []);\n  if (!isStatic && typeof __remixContext === \"object\" && __remixContext.a) {\n    for (let i = 0; i < __remixContext.a; i++) {\n      deferredScripts.push( /*#__PURE__*/React.createElement(DeferredHydrationScript, {\n        key: i,\n        scriptProps: props,\n        serializeData: serializeDataImp,\n        serializeError: serializeErrorImp\n      }));\n    }\n  }\n  let routePreloads = matches.map(match => {\n    let route = manifest.routes[match.route.id];\n    return (route.imports || []).concat([route.module]);\n  }).flat(1);\n  let preloads = isHydrated ? [] : manifest.entry.imports.concat(routePreloads);\n  return isHydrated ? null : /*#__PURE__*/React.createElement(React.Fragment, null, !enableFogOfWar ? /*#__PURE__*/React.createElement(\"link\", {\n    rel: \"modulepreload\",\n    href: manifest.url,\n    crossOrigin: props.crossOrigin\n  }) : null, /*#__PURE__*/React.createElement(\"link\", {\n    rel: \"modulepreload\",\n    href: manifest.entry.module,\n    crossOrigin: props.crossOrigin\n  }), dedupe(preloads).map(path => /*#__PURE__*/React.createElement(\"link\", {\n    key: path,\n    rel: \"modulepreload\",\n    href: path,\n    crossOrigin: props.crossOrigin\n  })), initialScripts, deferredScripts);\n}\nfunction DeferredHydrationScript({\n  dataKey,\n  deferredData,\n  routeId,\n  scriptProps,\n  serializeData,\n  serializeError\n}) {\n  if (typeof document === \"undefined\" && deferredData && dataKey && routeId) {\n    invariant(deferredData.pendingKeys.includes(dataKey), `Deferred data for route ${routeId} with key ${dataKey} was not pending but tried to render a script for it.`);\n  }\n  return /*#__PURE__*/React.createElement(React.Suspense, {\n    fallback:\n    // This makes absolutely no sense. The server renders null as a fallback,\n    // but when hydrating, we need to render a script tag to avoid a hydration issue.\n    // To reproduce a hydration mismatch, just render null as a fallback.\n    typeof document === \"undefined\" && deferredData && dataKey && routeId ? null : /*#__PURE__*/React.createElement(\"script\", _extends({}, scriptProps, {\n      async: true,\n      suppressHydrationWarning: true,\n      dangerouslySetInnerHTML: {\n        __html: \" \"\n      }\n    }))\n  }, typeof document === \"undefined\" && deferredData && dataKey && routeId ? /*#__PURE__*/React.createElement(Await, {\n    resolve: deferredData.data[dataKey],\n    errorElement: /*#__PURE__*/React.createElement(ErrorDeferredHydrationScript, {\n      dataKey: dataKey,\n      routeId: routeId,\n      scriptProps: scriptProps,\n      serializeError: serializeError\n    }),\n    children: data => {\n      return /*#__PURE__*/React.createElement(\"script\", _extends({}, scriptProps, {\n        async: true,\n        suppressHydrationWarning: true,\n        dangerouslySetInnerHTML: {\n          __html: serializeData(routeId, dataKey, data)\n        }\n      }));\n    }\n  }) : /*#__PURE__*/React.createElement(\"script\", _extends({}, scriptProps, {\n    async: true,\n    suppressHydrationWarning: true,\n    dangerouslySetInnerHTML: {\n      __html: \" \"\n    }\n  })));\n}\nfunction ErrorDeferredHydrationScript({\n  dataKey,\n  routeId,\n  scriptProps,\n  serializeError\n}) {\n  let error = useAsyncError();\n  return /*#__PURE__*/React.createElement(\"script\", _extends({}, scriptProps, {\n    suppressHydrationWarning: true,\n    dangerouslySetInnerHTML: {\n      __html: serializeError(routeId, dataKey, error)\n    }\n  }));\n}\nfunction dedupe(array) {\n  return [...new Set(array)];\n}\n/**\n * Returns the active route matches, useful for accessing loaderData for\n * parent/child routes or the route \"handle\" property\n *\n * @see https://remix.run/hooks/use-matches\n */\nfunction useMatches() {\n  return useMatches$1();\n}\n\n/**\n * Returns the JSON parsed data from the current route's `loader`.\n *\n * @see https://remix.run/hooks/use-loader-data\n */\nfunction useLoaderData() {\n  return useLoaderData$1();\n}\n\n/**\n * Returns the loaderData for the given routeId.\n *\n * @see https://remix.run/hooks/use-route-loader-data\n */\nfunction useRouteLoaderData(routeId) {\n  return useRouteLoaderData$1(routeId);\n}\n\n/**\n * Returns the JSON parsed data from the current route's `action`.\n *\n * @see https://remix.run/hooks/use-action-data\n */\nfunction useActionData() {\n  return useActionData$1();\n}\n\n/**\n * Interacts with route loaders and actions without causing a navigation. Great\n * for any interaction that stays on the same page.\n *\n * @see https://remix.run/hooks/use-fetcher\n */\nfunction useFetcher(opts = {}) {\n  return useFetcher$1(opts);\n}\n\n/**\n * This component connects your app to the Remix asset server and\n * automatically reloads the page when files change in development.\n * In production, it renders null, so you can safely render it always in your root route.\n *\n * @see https://remix.run/docs/components/live-reload\n */\nconst LiveReload =\n// Dead Code Elimination magic for production builds.\n// This way devs don't have to worry about doing the NODE_ENV check themselves.\nprocess.env.NODE_ENV !== \"development\" ? () => null : function LiveReload({\n  origin,\n  port,\n  timeoutMs = 1000,\n  nonce = undefined\n}) {\n  // @ts-expect-error\n  let isViteClient = import.meta && import.meta.env !== undefined;\n  if (isViteClient) {\n    console.warn([\"`<LiveReload />` is obsolete when using Vite and can conflict with Vite's built-in HMR runtime.\", \"\", \"Remove `<LiveReload />` from your code and instead only use `<Scripts />`.\", \"Then refresh the page to remove lingering scripts from `<LiveReload />`.\"].join(\"\\n\"));\n    return null;\n  }\n  origin ??= process.env.REMIX_DEV_ORIGIN;\n  let js = String.raw;\n  return /*#__PURE__*/React.createElement(\"script\", {\n    nonce: nonce,\n    suppressHydrationWarning: true,\n    dangerouslySetInnerHTML: {\n      __html: js`\n                function remixLiveReloadConnect(config) {\n                  let LIVE_RELOAD_ORIGIN = ${JSON.stringify(origin)};\n                  let protocol =\n                    LIVE_RELOAD_ORIGIN ? new URL(LIVE_RELOAD_ORIGIN).protocol.replace(/^http/, \"ws\") :\n                    location.protocol === \"https:\" ? \"wss:\" : \"ws:\"; // remove in v2?\n                  let hostname = LIVE_RELOAD_ORIGIN ? new URL(LIVE_RELOAD_ORIGIN).hostname : location.hostname;\n                  let url = new URL(protocol + \"//\" + hostname + \"/socket\");\n\n                  url.port =\n                    ${port} ||\n                    (LIVE_RELOAD_ORIGIN ? new URL(LIVE_RELOAD_ORIGIN).port : 8002);\n\n                  let ws = new WebSocket(url.href);\n                  ws.onmessage = async (message) => {\n                    let event = JSON.parse(message.data);\n                    if (event.type === \"LOG\") {\n                      console.log(event.message);\n                    }\n                    if (event.type === \"RELOAD\") {\n                      console.log(\"💿 Reloading window ...\");\n                      window.location.reload();\n                    }\n                    if (event.type === \"HMR\") {\n                      if (!window.__hmr__ || !window.__hmr__.contexts) {\n                        console.log(\"💿 [HMR] No HMR context, reloading window ...\");\n                        window.location.reload();\n                        return;\n                      }\n                      if (!event.updates || !event.updates.length) return;\n                      let updateAccepted = false;\n                      let needsRevalidation = new Set();\n                      for (let update of event.updates) {\n                        console.log(\"[HMR] \" + update.reason + \" [\" + update.id +\"]\")\n                        if (update.revalidate) {\n                          needsRevalidation.add(update.routeId);\n                          console.log(\"[HMR] Revalidating [\" + update.routeId + \"]\");\n                        }\n                        let imported = await import(update.url +  '?t=' + event.assetsManifest.hmr.timestamp);\n                        if (window.__hmr__.contexts[update.id]) {\n                          let accepted = window.__hmr__.contexts[update.id].emit(\n                            imported\n                          );\n                          if (accepted) {\n                            console.log(\"[HMR] Update accepted by\", update.id);\n                            updateAccepted = true;\n                          }\n                        }\n                      }\n                      if (event.assetsManifest && window.__hmr__.contexts[\"remix:manifest\"]) {\n                        let accepted = window.__hmr__.contexts[\"remix:manifest\"].emit(\n                          { needsRevalidation, assetsManifest: event.assetsManifest }\n                        );\n                        if (accepted) {\n                          console.log(\"[HMR] Update accepted by\", \"remix:manifest\");\n                          updateAccepted = true;\n                        }\n                      }\n                      if (!updateAccepted) {\n                        console.log(\"[HMR] Update rejected, reloading...\");\n                        window.location.reload();\n                      }\n                    }\n                  };\n                  ws.onopen = () => {\n                    if (config && typeof config.onOpen === \"function\") {\n                      config.onOpen();\n                    }\n                  };\n                  ws.onclose = (event) => {\n                    if (event.code === 1006) {\n                      console.log(\"Remix dev asset server web socket closed. Reconnecting...\");\n                      setTimeout(\n                        () =>\n                          remixLiveReloadConnect({\n                            onOpen: () => window.location.reload(),\n                          }),\n                      ${String(timeoutMs)}\n                      );\n                    }\n                  };\n                  ws.onerror = (error) => {\n                    console.log(\"Remix dev asset server web socket error:\");\n                    console.error(error);\n                  };\n                }\n                remixLiveReloadConnect();\n              `\n    }\n  });\n};\nfunction mergeRefs(...refs) {\n  return value => {\n    refs.forEach(ref => {\n      if (typeof ref === \"function\") {\n        ref(value);\n      } else if (ref != null) {\n        ref.current = value;\n      }\n    });\n  };\n}\n\nexport { Await, Form, Link, Links, LiveReload, Meta, NavLink, PrefetchPageLinks, RemixContext, Scripts, composeEventHandlers, useActionData, useFetcher, useLoaderData, useMatches, useRemixContext, useRouteLoaderData };\n"], "names": ["_extends", "UNSAFE_warning", "React.createContext", "React.useState", "React.useRef", "React.useCallback", "React.useLayoutEffect", "React.useEffect", "Deferred", "transition", "navigator", "React.useMemo", "state", "UNSAFE_logV6DeprecationWarnings", "React.createElement", "React.Fragment", "UNSAFE_DataRouterContext", "UNSAFE_DataRouterStateContext", "React.memo", "UNSAFE_useRoutesImpl", "ABSOLUTE_URL_REGEX", "Link", "React.forwardRef", "replace", "React.useContext", "UNSAFE_NavigationContext", "NavLink", "Form", "DataRouterHook", "DataRouterStateHook", "useDataRouterContext", "UNSAFE_invariant", "UNSAFE_useRouteId", "UNSAFE_RouteContext", "useFetcher", "FetcherForm", "data", "location", "matches", "matchPath", "value", "UNSAFE_DeferredData", "result", "UNSAFE_ErrorResponseImpl", "UNSAFE_SingleFetchRedirectSymbol", "redirect", "React.Component", "fn", "msg", "React.Suspense"], "mappings": ";;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAiBA,SAASA,aAAW;AAClBA,eAAW,OAAO,SAAS,OAAO,OAAO,KAAK,IAAI,SAAU,QAAQ;AAClE,aAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACrC,UAAA,SAAS,UAAU,CAAC;AACxB,eAAS,OAAO,QAAQ;AACtB,YAAI,OAAO,UAAU,eAAe,KAAK,QAAQ,GAAG,GAAG;AAC9C,iBAAA,GAAG,IAAI,OAAO,GAAG;AAAA,QAAA;AAAA,MAC1B;AAAA,IACF;AAEK,WAAA;AAAA,EACT;AACO,SAAAA,WAAS,MAAM,MAAM,SAAS;AACvC;AACA,SAAS,8BAA8B,QAAQ,UAAU;AACnD,MAAA,UAAU,KAAM,QAAO,CAAC;AAC5B,MAAI,SAAS,CAAC;AACV,MAAA,aAAa,OAAO,KAAK,MAAM;AACnC,MAAI,KAAK;AACT,OAAK,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AACtC,UAAM,WAAW,CAAC;AAClB,QAAI,SAAS,QAAQ,GAAG,KAAK,EAAG;AACzB,WAAA,GAAG,IAAI,OAAO,GAAG;AAAA,EAAA;AAEnB,SAAA;AACT;AAEA,MAAM,gBAAgB;AACtB,MAAM,iBAAiB;AACvB,SAAS,cAAc,QAAQ;AAC7B,SAAO,UAAU,QAAQ,OAAO,OAAO,YAAY;AACrD;AACA,SAAS,gBAAgB,QAAQ;AAC/B,SAAO,cAAc,MAAM,KAAK,OAAO,QAAQ,kBAAkB;AACnE;AACA,SAAS,cAAc,QAAQ;AAC7B,SAAO,cAAc,MAAM,KAAK,OAAO,QAAQ,kBAAkB;AACnE;AACA,SAAS,eAAe,QAAQ;AAC9B,SAAO,cAAc,MAAM,KAAK,OAAO,QAAQ,kBAAkB;AACnE;AACA,SAAS,gBAAgB,OAAO;AACvB,SAAA,CAAC,EAAE,MAAM,WAAW,MAAM,UAAU,MAAM,WAAW,MAAM;AACpE;AACA,SAAS,uBAAuB,OAAO,QAAQ;AAC7C,SAAO,MAAM,WAAW;AAAA,GAExB,CAAC,UAAU,WAAW;AAAA,EAEtB,CAAC,gBAAgB,KAAK;AAExB;AAsBA,SAAS,mBAAmB,MAAM;AAChC,MAAI,SAAS,QAAQ;AACZ,WAAA;AAAA,EAAA;AAET,SAAO,IAAI,gBAAgB,OAAO,SAAS,YAAY,MAAM,QAAQ,IAAI,KAAK,gBAAgB,kBAAkB,OAAO,OAAO,KAAK,IAAI,EAAE,OAAO,CAAC,MAAM,QAAQ;AACzJ,QAAA,QAAQ,KAAK,GAAG;AACpB,WAAO,KAAK,OAAO,MAAM,QAAQ,KAAK,IAAI,MAAM,IAAI,CAAA,MAAK,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,KAAK,KAAK,CAAC,CAAC;AAAA,EACrF,GAAG,CAAE,CAAA,CAAC;AACR;AACA,SAAS,2BAA2B,gBAAgB,qBAAqB;AACnE,MAAA,eAAe,mBAAmB,cAAc;AACpD,MAAI,qBAAqB;AAMH,wBAAA,QAAQ,CAAC,GAAG,QAAQ;AACtC,UAAI,CAAC,aAAa,IAAI,GAAG,GAAG;AAC1B,4BAAoB,OAAO,GAAG,EAAE,QAAQ,CAAS,UAAA;AAClC,uBAAA,OAAO,KAAK,KAAK;AAAA,QAAA,CAC/B;AAAA,MAAA;AAAA,IACH,CACD;AAAA,EAAA;AAEI,SAAA;AACT;AAEA,IAAI,6BAA6B;AACjC,SAAS,+BAA+B;AACtC,MAAI,+BAA+B,MAAM;AACnC,QAAA;AACE,UAAA;AAAA,QAAS,SAAS,cAAc,MAAM;AAAA;AAAA,QAE1C;AAAA,MAAC;AAC4B,mCAAA;AAAA,aACtB,GAAG;AACmB,mCAAA;AAAA,IAAA;AAAA,EAC/B;AAEK,SAAA;AACT;AACA,MAAM,wBAA4B,oBAAA,IAAI,CAAC,qCAAqC,uBAAuB,YAAY,CAAC;AAChH,SAAS,eAAe,SAAS;AAC/B,MAAI,WAAW,QAAQ,CAAC,sBAAsB,IAAI,OAAO,GAAG;AAClBC,YAAe,OAAO,MAAO,UAAU,+DAAgE,0BAA2B,iBAAiB,IAAK;AACzL,WAAA;AAAA,EAAA;AAEF,SAAA;AACT;AACA,SAAS,sBAAsB,QAAQ,UAAU;AAC3C,MAAA;AACA,MAAA;AACA,MAAA;AACA,MAAA;AACA,MAAA;AACA,MAAA,cAAc,MAAM,GAAG;AAIrB,QAAA,OAAO,OAAO,aAAa,QAAQ;AACvC,aAAS,OAAO,cAAc,MAAM,QAAQ,IAAI;AACvC,aAAA,OAAO,aAAa,QAAQ,KAAK;AAC1C,cAAU,eAAe,OAAO,aAAa,SAAS,CAAC,KAAK;AACjD,eAAA,IAAI,SAAS,MAAM;AAAA,EACrB,WAAA,gBAAgB,MAAM,KAAK,eAAe,MAAM,MAAM,OAAO,SAAS,YAAY,OAAO,SAAS,UAAU;AACrH,QAAI,OAAO,OAAO;AAClB,QAAI,QAAQ,MAAM;AACV,YAAA,IAAI,MAAM,oEAAsE;AAAA,IAAA;AAMxF,QAAI,OAAO,OAAO,aAAa,YAAY,KAAK,KAAK,aAAa,QAAQ;AAC1E,aAAS,OAAO,cAAc,MAAM,QAAQ,IAAI;AAChD,aAAS,OAAO,aAAa,YAAY,KAAK,KAAK,aAAa,QAAQ,KAAK;AACnE,cAAA,eAAe,OAAO,aAAa,aAAa,CAAC,KAAK,eAAe,KAAK,aAAa,SAAS,CAAC,KAAK;AAErG,eAAA,IAAI,SAAS,MAAM,MAAM;AAKhC,QAAA,CAAC,gCAAgC;AAC/B,UAAA;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,MAAA,IACE;AACJ,UAAI,SAAS,SAAS;AAChB,YAAA,SAAS,OAAO,OAAO,MAAM;AACxB,iBAAA,OAAO,SAAS,KAAK,GAAG;AACxB,iBAAA,OAAO,SAAS,KAAK,GAAG;AAAA,iBACxB,MAAM;AACN,iBAAA,OAAO,MAAM,KAAK;AAAA,MAAA;AAAA,IAC7B;AAAA,EACF,WACS,cAAc,MAAM,GAAG;AAC1B,UAAA,IAAI,MAAM,oFAA2F;AAAA,EAAA,OACtG;AACI,aAAA;AACA,aAAA;AACC,cAAA;AACH,WAAA;AAAA,EAAA;AAGL,MAAA,YAAY,YAAY,cAAc;AACjC,WAAA;AACI,eAAA;AAAA,EAAA;AAEN,SAAA;AAAA,IACL;AAAA,IACA,QAAQ,OAAO,YAAY;AAAA,IAC3B;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,MAAM,YAAY,CAAC,WAAW,YAAY,kBAAkB,WAAW,SAAS,UAAU,MAAM,sBAAsB,gBAAgB,GACpI,aAAa,CAAC,gBAAgB,iBAAiB,aAAa,OAAO,SAAS,MAAM,kBAAkB,UAAU,GAC9G,aAAa,CAAC,cAAc,YAAY,kBAAkB,WAAW,SAAS,UAAU,UAAU,YAAY,YAAY,sBAAsB,gBAAgB;AAUlK,MAAM,uBAAuB;AAC7B,IAAI;AACF,SAAO,uBAAuB;AAChC,SAAS,GAAG;AAEZ;AAoFA,MAAM,mEAAyD;AAAA,EAC7D,iBAAiB;AACnB,CAAC;AAC0C;AACzC,wBAAsB,cAAc;AACtC;AACA,MAAM,kBAA+BC,6BAAAA,cAAoB,oBAAI,KAAK;AACvB;AACzC,kBAAgB,cAAc;AAChC;AA0BA,MAAM,mBAAmB;AACzB,MAAM,sBAAsB,MAAM,gBAAgB;AAClD,MAAM,aAAa;AACnB,MAAM,gBAAgB,SAAS,UAAU;AACzC,MAAM,SAAS;AACf,MAAM,YAAY,MAAM,MAAM;AAC9B,SAAS,oBAAoB,IAAI;AAC/B,MAAI,qBAAqB;AACvB,wBAAoB,EAAE;AAAA,EAAA,OACjB;AACF,OAAA;AAAA,EAAA;AAEP;AACA,SAAS,cAAc,IAAI;AACzB,MAAI,eAAe;AACjB,kBAAc,EAAE;AAAA,EAAA,OACX;AACF,OAAA;AAAA,EAAA;AAEP;AACA,IAAA,aAAA,MAAM,SAAS;AAAA,EACb,cAAc;AACZ,SAAK,SAAS;AACd,SAAK,UAAU,IAAI,QAAQ,CAAC,SAAS,WAAW;AAC9C,WAAK,UAAU,CAAS,UAAA;AAClB,YAAA,KAAK,WAAW,WAAW;AAC7B,eAAK,SAAS;AACd,kBAAQ,KAAK;AAAA,QAAA;AAAA,MAEjB;AACA,WAAK,SAAS,CAAU,WAAA;AAClB,YAAA,KAAK,WAAW,WAAW;AAC7B,eAAK,SAAS;AACd,iBAAO,MAAM;AAAA,QAAA;AAAA,MAEjB;AAAA,IAAA,CACD;AAAA,EAAA;AAEL;AAIA,SAAS,eAAe,MAAM;AACxB,MAAA;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,EAAA,IACE;AACJ,MAAI,CAAC,OAAO,YAAY,IAAIC,aAAAA,SAAe,OAAO,KAAK;AACvD,MAAI,CAAC,cAAc,eAAe,IAAIA,sBAAe;AACrD,MAAI,CAAC,WAAW,YAAY,IAAIA,sBAAe;AAAA,IAC7C,iBAAiB;AAAA,EAAA,CAClB;AACD,MAAI,CAAC,WAAW,YAAY,IAAIA,sBAAe;AAC/C,MAAI,CAAC,YAAY,aAAa,IAAIA,sBAAe;AACjD,MAAI,CAAC,cAAc,eAAe,IAAIA,sBAAe;AACrD,MAAI,cAAcC,aAAAA,OAAa,oBAAI,KAAK;AACpC,MAAA;AAAA,IACF;AAAA,EACF,IAAI,UAAU,CAAC;AACX,MAAA,uBAAuBC,yBAAkB,CAAM,OAAA;AACjD,QAAI,oBAAoB;AACtB,0BAAoB,EAAE;AAAA,IAAA,OACjB;AACF,SAAA;AAAA,IAAA;AAAA,EACL,GACC,CAAC,kBAAkB,CAAC;AACvB,MAAI,WAAWA,aAAAA,YAAkB,CAAC,UAAU,UAAU;AAChD,QAAA;AAAA,MACF;AAAA,MACA;AAAA,MACA;AAAA,IAAA,IACE;AACJ,aAAS,SAAS,QAAQ,CAAC,SAAS,QAAQ;AACtC,UAAA,QAAQ,SAAS,QAAW;AAC9B,oBAAY,QAAQ,IAAI,KAAK,QAAQ,IAAI;AAAA,MAAA;AAAA,IAC3C,CACD;AACD,oBAAgB,QAAQ,CAAO,QAAA,YAAY,QAAQ,OAAO,GAAG,CAAC;AAC9D,QAAI,8BAA8B,OAAO,UAAU,QAAQ,OAAO,OAAO,YAAY,QAAQ,OAAO,OAAO,OAAO,SAAS,wBAAwB;AAG/I,QAAA,CAAC,sBAAsB,6BAA6B;AACtD,UAAI,WAAW;AACC,sBAAA,MAAM,aAAa,QAAQ,CAAC;AAAA,MAAA,OACrC;AACgB,6BAAA,MAAM,aAAa,QAAQ,CAAC;AAAA,MAAA;AAEnD;AAAA,IAAA;AAGF,QAAI,WAAW;AAEb,oBAAc,MAAM;AAElB,YAAI,YAAY;AACd,uBAAa,UAAU,QAAQ;AAC/B,qBAAW,eAAe;AAAA,QAAA;AAEf,qBAAA;AAAA,UACX,iBAAiB;AAAA,UACjB,WAAW;AAAA,UACX,iBAAiB,mBAAmB;AAAA,UACpC,cAAc,mBAAmB;AAAA,QAAA,CAClC;AAAA,MAAA,CACF;AAED,UAAI,IAAI,OAAO,OAAO,SAAS,oBAAoB,MAAM;AACzC,sBAAA,MAAM,aAAa,QAAQ,CAAC;AAAA,MAAA,CAC3C;AAEC,QAAA,SAAS,QAAQ,MAAM;AACvB,sBAAc,MAAM;AAClB,uBAAa,MAAS;AACtB,wBAAc,MAAS;AACvB,0BAAgB,MAAS;AACZ,uBAAA;AAAA,YACX,iBAAiB;AAAA,UAAA,CAClB;AAAA,QAAA,CACF;AAAA,MAAA,CACF;AACa,oBAAA,MAAM,cAAc,CAAC,CAAC;AACpC;AAAA,IAAA;AAGF,QAAI,YAAY;AAGd,mBAAa,UAAU,QAAQ;AAC/B,iBAAW,eAAe;AACV,sBAAA;AAAA,QACd,OAAO;AAAA,QACP,iBAAiB,mBAAmB;AAAA,QACpC,cAAc,mBAAmB;AAAA,MAAA,CAClC;AAAA,IAAA,OACI;AAEL,sBAAgB,QAAQ;AACX,mBAAA;AAAA,QACX,iBAAiB;AAAA,QACjB,WAAW;AAAA,QACX,iBAAiB,mBAAmB;AAAA,QACpC,cAAc,mBAAmB;AAAA,MAAA,CAClC;AAAA,IAAA;AAAA,EACH,GACC,CAAC,OAAO,QAAQ,YAAY,WAAW,aAAa,oBAAoB,CAAC;AAGtEC,eAAA,gBAAgB,MAAM,OAAO,UAAU,QAAQ,GAAG,CAAC,QAAQ,QAAQ,CAAC;AAG1EC,eAAAA,UAAgB,MAAM;AACpB,QAAI,UAAU,mBAAmB,CAAC,UAAU,WAAW;AACxC,mBAAA,IAAIC,YAAU;AAAA,IAAA;AAAA,EAC7B,GACC,CAAC,SAAS,CAAC;AAIdD,eAAAA,UAAgB,MAAM;AAChB,QAAA,aAAa,gBAAgB,OAAO,QAAQ;AAC9C,UAAI,WAAW;AACf,UAAI,gBAAgB,UAAU;AAC9B,UAAIE,cAAa,OAAO,OAAO,SAAS,oBAAoB,YAAY;AACjD,6BAAA,MAAM,aAAa,QAAQ,CAAC;AAC3C,cAAA;AAAA,MAAA,CACP;AACDA,kBAAW,SAAS,QAAQ,MAAM;AAChC,qBAAa,MAAS;AACtB,sBAAc,MAAS;AACvB,wBAAgB,MAAS;AACZ,qBAAA;AAAA,UACX,iBAAiB;AAAA,QAAA,CAClB;AAAA,MAAA,CACF;AACD,oBAAcA,WAAU;AAAA,IAAA;AAAA,EAC1B,GACC,CAAC,sBAAsB,cAAc,WAAW,OAAO,MAAM,CAAC;AAGjEF,eAAAA,UAAgB,MAAM;AACpB,QAAI,aAAa,gBAAgB,MAAM,SAAS,QAAQ,aAAa,SAAS,KAAK;AACjF,gBAAU,QAAQ;AAAA,IAAA;AAAA,EACpB,GACC,CAAC,WAAW,YAAY,MAAM,UAAU,YAAY,CAAC;AAGxDA,eAAAA,UAAgB,MAAM;AAChB,QAAA,CAAC,UAAU,mBAAmB,cAAc;AAC9C,sBAAgB,aAAa,KAAK;AACrB,mBAAA;AAAA,QACX,iBAAiB;AAAA,QACjB,WAAW;AAAA,QACX,iBAAiB,aAAa;AAAA,QAC9B,cAAc,aAAa;AAAA,MAAA,CAC5B;AACD,sBAAgB,MAAS;AAAA,IAAA;AAAA,EAE1B,GAAA,CAAC,UAAU,iBAAiB,YAAY,CAAC;AAC5CA,eAAAA,UAAgB,MAAM;AACoBN,YAAe,mBAAmB,QAAQ,CAAC,OAAO,OAAO,qBAAqB,8HAAmI;AAAA,EAG3P,GAAG,EAAE;AACD,MAAAS,aAAYC,aAAAA,QAAc,MAAM;AAC3B,WAAA;AAAA,MACL,YAAY,OAAO;AAAA,MACnB,gBAAgB,OAAO;AAAA,MACvB,IAAI,CAAA,MAAK,OAAO,SAAS,CAAC;AAAA,MAC1B,MAAM,CAAC,IAAIC,QAAO,SAAS,OAAO,SAAS,IAAI;AAAA,QAC7C,OAAAA;AAAAA,QACA,oBAAoB,QAAQ,OAAO,SAAS,KAAK;AAAA,MAAA,CAClD;AAAA,MACD,SAAS,CAAC,IAAIA,QAAO,SAAS,OAAO,SAAS,IAAI;AAAA,QAChD,SAAS;AAAA,QACT,OAAAA;AAAAA,QACA,oBAAoB,QAAQ,OAAO,SAAS,KAAK;AAAA,MAClD,CAAA;AAAA,IACH;AAAA,EAAA,GACC,CAAC,MAAM,CAAC;AACP,MAAA,WAAW,OAAO,YAAY;AAC9B,MAAA,oBAAoBD,aAAAA,QAAc,OAAO;AAAA,IAC3C;AAAA,IACA,WAAAD;AAAA,IACA,QAAQ;AAAA,IACR;AAAA,EACE,IAAA,CAAC,QAAQA,YAAW,QAAQ,CAAC;AAC7B,MAAA,eAAeC,aAAAA,QAAc,OAAO;AAAA,IACtC,sBAAsB,OAAO,OAAO;AAAA,EAClC,IAAA,CAAC,OAAO,OAAO,oBAAoB,CAAC;AAClCJ,yBAAU,MAAMM,yBAAgC,QAAQ,OAAO,MAAM,GAAG,CAAC,QAAQ,OAAO,MAAM,CAAC;AAOjF,SAAAC,6BAAAA,cAAoBC,aAAAA,UAAgB,MAAyBD,6BAAAA,cAAcE,kBAAyB,UAAU;AAAA,IAChI,OAAO;AAAA,EAAA,GACOF,6BAAoB,cAAAG,uBAA8B,UAAU;AAAA,IAC1E,OAAO;AAAA,EAAA,GACOH,6BAAoB,cAAA,gBAAgB,UAAU;AAAA,IAC5D,OAAO,YAAY;AAAA,EAAA,GACLA,6BAAoB,cAAA,sBAAsB,UAAU;AAAA,IAClE,OAAO;AAAA,EAAA,GACaA,6BAAAA,cAAc,QAAQ;AAAA,IAC1C;AAAA,IACA,UAAU,MAAM;AAAA,IAChB,gBAAgB,MAAM;AAAA,IACtB,WAAAJ;AAAA,IACA,QAAQ;AAAA,EAAA,GACP,MAAM,eAAe,OAAO,OAAO,sBAAmCI,2CAAoB,oBAAoB;AAAA,IAC/G,QAAQ,OAAO;AAAA,IACf,QAAQ,OAAO;AAAA,IACf;AAAA,EACD,CAAA,IAAI,eAAe,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI;AACjC;AAEA,MAAM,qBAAwCI,6BAAA,KAAK,UAAU;AAC7D,SAAS,WAAW,OAAO;AACrB,MAAA;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,EAAA,IACE;AACJ,SAAOC,cAAqB,QAAQ,QAAW,OAAO,MAAM;AAC9D;AAqHA,MAAM,YAAY,OAAO,WAAW,eAAe,OAAO,OAAO,aAAa,eAAe,OAAO,OAAO,SAAS,kBAAkB;AACtI,MAAMC,uBAAqB;AAI3B,MAAMC,SAA0BC,6BAAAA,WAAW,SAAS,YAAY,OAAO,KAAK;AACtE,MAAA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,SAAAC;AAAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACE,IAAA,OACJ,OAAO,8BAA8B,OAAO,SAAS;AACnD,MAAA;AAAA,IACF;AAAA,EAAA,IACEC,aAAAA,WAAiBC,iBAAwB;AAEzC,MAAA;AACJ,MAAI,aAAa;AACjB,MAAI,OAAO,OAAO,YAAYL,qBAAmB,KAAK,EAAE,GAAG;AAE1C,mBAAA;AAEf,QAAI,WAAW;AACT,UAAA;AACF,YAAI,aAAa,IAAI,IAAI,OAAO,SAAS,IAAI;AAC7C,YAAI,YAAY,GAAG,WAAW,IAAI,IAAI,IAAI,IAAI,WAAW,WAAW,EAAE,IAAI,IAAI,IAAI,EAAE;AACpF,YAAI,OAAO,cAAc,UAAU,UAAU,QAAQ;AACrD,YAAI,UAAU,WAAW,WAAW,UAAU,QAAQ,MAAM;AAErD,eAAA,OAAO,UAAU,SAAS,UAAU;AAAA,QAAA,OACpC;AACQ,uBAAA;AAAA,QAAA;AAAA,eAER,GAAG;AAE8BnB,gBAAe,OAAO,eAAgB,KAAK,wGAA8G;AAAA,MAAI;AAAA,IACvM;AAAA,EACF;AAGE,MAAA,OAAO,QAAQ,IAAI;AAAA,IACrB;AAAA,EAAA,CACD;AACG,MAAA,kBAAkB,oBAAoB,IAAI;AAAA,IAC5C,SAAAsB;AAAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EAAA,CACD;AACD,WAAS,YAAY,OAAO;AACtB,QAAA,iBAAiB,KAAK;AACtB,QAAA,CAAC,MAAM,kBAAkB;AAC3B,sBAAgB,KAAK;AAAA,IAAA;AAAA,EACvB;AAEF;AAAA;AAAA,+CAGsB,KAAKvB,WAAS,CAAA,GAAI,MAAM;AAAA,MAC1C,MAAM,gBAAgB;AAAA,MACtB,SAAS,cAAc,iBAAiB,UAAU;AAAA,MAClD;AAAA,MACA;AAAA,IAAA,CACD,CAAC;AAAA;AAEN,CAAC;AAC0C;AACzCqB,SAAK,cAAc;AACrB;AAIA,MAAMK,YAA6BJ,6BAAAA,WAAW,SAAS,eAAe,OAAO,KAAK;AAC5E,MAAA;AAAA,IACA,gBAAgB,kBAAkB;AAAA,IAClC,gBAAgB;AAAA,IAChB,WAAW,gBAAgB;AAAA,IAC3B,MAAM;AAAA,IACN,OAAO;AAAA,IACP;AAAA,IACA;AAAA,IACA;AAAA,EACE,IAAA,OACJ,OAAO,8BAA8B,OAAO,UAAU;AACpD,MAAA,OAAO,gBAAgB,IAAI;AAAA,IAC7B,UAAU,KAAK;AAAA,EAAA,CAChB;AACD,MAAI,WAAW,YAAY;AACvB,MAAA,cAAcE,aAAM,WAAWP,sBAA6B;AAC5D,MAAA;AAAA,IACF,WAAAP;AAAA,IACA;AAAA,EAAA,IACEc,aAAAA,WAAiBC,iBAAwB;AAC7C,MAAI,kBAAkB,eAAe;AAAA;AAAA,EAGrC,uBAAuB,IAAI,KAAK,mBAAmB;AAC/C,MAAA,aAAaf,WAAU,iBAAiBA,WAAU,eAAe,IAAI,EAAE,WAAW,KAAK;AAC3F,MAAI,mBAAmB,SAAS;AAC5B,MAAA,uBAAuB,eAAe,YAAY,cAAc,YAAY,WAAW,WAAW,YAAY,WAAW,SAAS,WAAW;AACjJ,MAAI,CAAC,eAAe;AAClB,uBAAmB,iBAAiB,YAAY;AACzB,2BAAA,uBAAuB,qBAAqB,YAAgB,IAAA;AACnF,iBAAa,WAAW,YAAY;AAAA,EAAA;AAEtC,MAAI,wBAAwB,UAAU;AACb,2BAAA,cAAc,sBAAsB,QAAQ,KAAK;AAAA,EAAA;AAOpE,QAAA,mBAAmB,eAAe,OAAO,WAAW,SAAS,GAAG,IAAI,WAAW,SAAS,IAAI,WAAW;AAC7G,MAAI,WAAW,qBAAqB,cAAc,CAAC,OAAO,iBAAiB,WAAW,UAAU,KAAK,iBAAiB,OAAO,gBAAgB,MAAM;AACnJ,MAAI,YAAY,wBAAwB,SAAS,yBAAyB,cAAc,CAAC,OAAO,qBAAqB,WAAW,UAAU,KAAK,qBAAqB,OAAO,WAAW,MAAM,MAAM;AAClM,MAAI,cAAc;AAAA,IAChB;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACI,MAAA,cAAc,WAAW,kBAAkB;AAC3C,MAAA;AACA,MAAA,OAAO,kBAAkB,YAAY;AACvC,gBAAY,cAAc,WAAW;AAAA,EAAA,OAChC;AAML,gBAAY,CAAC,eAAe,WAAW,WAAW,MAAM,YAAY,YAAY,MAAM,kBAAkB,kBAAkB,IAAI,EAAE,OAAO,OAAO,EAAE,KAAK,GAAG;AAAA,EAAA;AAE1J,MAAI,QAAQ,OAAO,cAAc,aAAa,UAAU,WAAW,IAAI;AACvE,sCAAwC,cAAAW,QAAMrB,WAAS,CAAA,GAAI,MAAM;AAAA,IAC/D,gBAAgB;AAAA,IAChB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EAAA,CACD,GAAG,OAAO,aAAa,aAAa,SAAS,WAAW,IAAI,QAAQ;AACvE,CAAC;AAC0C;AACzC0B,YAAQ,cAAc;AACxB;AAOA,MAAMC,SAAoBL,6BAAiB,WAAA,CAAC,OAAO,iBAAiB;AAC9D,MAAA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,SAAAC;AAAAA,IACA;AAAA,IACA,SAAS;AAAA,IACT;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACE,IAAA,OACJ,QAAQ,8BAA8B,OAAO,UAAU;AACzD,MAAI,SAAS,UAAU;AACnB,MAAA,aAAa,cAAc,QAAQ;AAAA,IACrC;AAAA,EAAA,CACD;AACD,MAAI,aAAa,OAAO,YAAY,MAAM,QAAQ,QAAQ;AAC1D,MAAI,gBAAgB,CAAS,UAAA;AAC3B,gBAAY,SAAS,KAAK;AAC1B,QAAI,MAAM,iBAAkB;AAC5B,UAAM,eAAe;AACjB,QAAA,YAAY,MAAM,YAAY;AAClC,QAAI,gBAAgB,aAAa,OAAO,SAAS,UAAU,aAAa,YAAY,MAAM;AACnF,WAAA,aAAa,MAAM,eAAe;AAAA,MACvC;AAAA,MACA,QAAQ;AAAA,MACR;AAAA,MACA,SAAAA;AAAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IAAA,CACD;AAAA,EACH;AACoB,SAAAT,6BAAoB,cAAA,QAAQd,WAAS;AAAA,IACvD,KAAK;AAAA,IACL,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,UAAU,iBAAiB,WAAW;AAAA,EACxC,GAAG,KAAK,CAAC;AACX,CAAC;AAC0C;AACzC2B,SAAK,cAAc;AACrB;AAuBA,IAAI;AAAA,CACH,SAAUC,iBAAgB;AACzBA,kBAAe,sBAAsB,IAAI;AACzCA,kBAAe,WAAW,IAAI;AAC9BA,kBAAe,kBAAkB,IAAI;AACrCA,kBAAe,YAAY,IAAI;AAC/BA,kBAAe,wBAAwB,IAAI;AAC7C,GAAG,mBAAmB,iBAAiB,CAAA,EAAG;AAC1C,IAAI;AAAA,CACH,SAAUC,sBAAqB;AAC9BA,uBAAoB,YAAY,IAAI;AACpCA,uBAAoB,aAAa,IAAI;AACrCA,uBAAoB,sBAAsB,IAAI;AAChD,GAAG,wBAAwB,sBAAsB,CAAA,EAAG;AAEpD,SAAS,0BAA0B,UAAU;AAC3C,SAAO,WAAW;AACpB;AACA,SAASC,uBAAqB,UAAU;AAClC,MAAA,MAAMN,aAAM,WAAWR,iBAAwB;AAClD,GAAA,MAA8Ce,YAAiB,OAAO,0BAA0B,QAAQ,CAAC,IAA8B;AACjI,SAAA;AACT;AACA,SAAS,mBAAmB,UAAU;AAChC,MAAA,QAAQP,aAAM,WAAWP,sBAA6B;AACzD,GAAA,QAAgDc,YAAiB,OAAO,0BAA0B,QAAQ,CAAC,IAA8B;AACnI,SAAA;AACT;AAOA,SAAS,oBAAoB,IAAI,OAAO;AAClC,MAAA;AAAA,IACF;AAAA,IACA,SAAS;AAAA,IACT;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EAAA,IACE,UAAU,SAAS,CAAA,IAAK;AAC5B,MAAI,WAAW,YAAY;AAC3B,MAAI,WAAW,YAAY;AACvB,MAAA,OAAO,gBAAgB,IAAI;AAAA,IAC7B;AAAA,EAAA,CACD;AACM,SAAA1B,aAAAA,YAAkB,CAAS,UAAA;AAC5B,QAAA,uBAAuB,OAAO,MAAM,GAAG;AACzC,YAAM,eAAe;AAGjBkB,UAAAA,WAAU,gBAAgB,SAAY,cAAc,WAAW,QAAQ,MAAM,WAAW,IAAI;AAChG,eAAS,IAAI;AAAA,QACX,SAAAA;AAAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MAAA,CACD;AAAA,IAAA;AAAA,EAEF,GAAA,CAAC,UAAU,UAAU,MAAM,aAAa,OAAO,QAAQ,IAAI,oBAAoB,UAAU,cAAc,CAAC;AAC7G;AAKA,SAAS,gBAAgB,aAAa;AACItB,UAAe,OAAO,oBAAoB,aAAa,yOAAwP;AACvV,MAAI,yBAAyBG,aAAAA,OAAa,mBAAmB,WAAW,CAAC;AACrE,MAAA,wBAAwBA,aAAM,OAAO,KAAK;AAC9C,MAAI,WAAW,YAAY;AACvB,MAAA,eAAeO,aAAAA,QAAc;AAAA;AAAA;AAAA;AAAA,IAIjC,2BAA2B,SAAS,QAAQ,sBAAsB,UAAU,OAAO,uBAAuB,OAAO;AAAA,KAAG,CAAC,SAAS,MAAM,CAAC;AACrI,MAAI,WAAW,YAAY;AAC3B,MAAI,kBAAkBN,aAAAA,YAAkB,CAAC,UAAU,oBAAoB;AAC/D,UAAA,kBAAkB,mBAAmB,OAAO,aAAa,aAAa,SAAS,YAAY,IAAI,QAAQ;AAC7G,0BAAsB,UAAU;AACvB,aAAA,MAAM,iBAAiB,eAAe;AAAA,EAAA,GAC9C,CAAC,UAAU,YAAY,CAAC;AACpB,SAAA,CAAC,cAAc,eAAe;AACvC;AACA,SAAS,+BAA+B;AAClC,MAAA,OAAO,aAAa,aAAa;AAC7B,UAAA,IAAI,MAAM,+GAAoH;AAAA,EAAA;AAExI;AACA,IAAI,YAAY;AAChB,IAAI,qBAAqB,MAAM,OAAO,OAAO,EAAE,SAAS,IAAI;AAK5D,SAAS,YAAY;AACf,MAAA;AAAA,IACF;AAAA,EAAA,IACEyB,uBAAqB,eAAe,SAAS;AAC7C,MAAA;AAAA,IACF;AAAA,EAAA,IACEN,aAAAA,WAAiBC,iBAAwB;AAC7C,MAAI,iBAAiBO,WAAkB;AACvC,SAAO3B,aAAM,YAAY,SAAU,QAAQ,SAAS;AAClD,QAAI,YAAY,QAAQ;AACtB,gBAAU,CAAC;AAAA,IAAA;AAEgB,iCAAA;AACzB,QAAA;AAAA,MACF;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IAAA,IACE,sBAAsB,QAAQ,QAAQ;AACtC,QAAA,QAAQ,aAAa,OAAO;AAC1B,UAAA,MAAM,QAAQ,cAAc,mBAAmB;AACnD,aAAO,MAAM,KAAK,gBAAgB,QAAQ,UAAU,QAAQ;AAAA,QAC1D,oBAAoB,QAAQ;AAAA,QAC5B;AAAA,QACA;AAAA,QACA,YAAY,QAAQ,UAAU;AAAA,QAC9B,aAAa,QAAQ,WAAW;AAAA,QAChC,WAAW,QAAQ;AAAA,MAAA,CACpB;AAAA,IAAA,OACI;AACE,aAAA,SAAS,QAAQ,UAAU,QAAQ;AAAA,QACxC,oBAAoB,QAAQ;AAAA,QAC5B;AAAA,QACA;AAAA,QACA,YAAY,QAAQ,UAAU;AAAA,QAC9B,aAAa,QAAQ,WAAW;AAAA,QAChC,SAAS,QAAQ;AAAA,QACjB,OAAO,QAAQ;AAAA,QACf,aAAa;AAAA,QACb,WAAW,QAAQ;AAAA,QACnB,gBAAgB,QAAQ;AAAA,MAAA,CACzB;AAAA,IAAA;AAAA,EAEF,GAAA,CAAC,QAAQ,UAAU,cAAc,CAAC;AACvC;AAGA,SAAS,cAAc,QAAQ,QAAQ;AACjC,MAAA;AAAA,IACF;AAAA,EAAA,IACE,WAAW,SAAS,CAAA,IAAK;AACzB,MAAA;AAAA,IACF;AAAA,EAAA,IACEmB,aAAAA,WAAiBC,iBAAwB;AACzC,MAAA,eAAeD,aAAM,WAAWS,YAAmB;AACtD,GAAA,eAAuDF,YAAiB,OAAO,kDAAkD,IAA8B;AAChK,MAAI,CAAC,KAAK,IAAI,aAAa,QAAQ,MAAM,EAAE;AAG3C,MAAI,OAAO/B,WAAS,IAAI,gBAAgB,SAAS,SAAS,KAAK;AAAA,IAC7D;AAAA,EAAA,CACD,CAAC;AAIF,MAAI,WAAW,YAAY;AAC3B,MAAI,UAAU,MAAM;AAGlB,SAAK,SAAS,SAAS;AAIvB,QAAI,SAAS,IAAI,gBAAgB,KAAK,MAAM;AACxC,QAAA,cAAc,OAAO,OAAO,OAAO;AACvC,QAAI,qBAAqB,YAAY,KAAK,CAAA,MAAK,MAAM,EAAE;AACvD,QAAI,oBAAoB;AACtB,aAAO,OAAO,OAAO;AACT,kBAAA,OAAO,CAAK,MAAA,CAAC,EAAE,QAAQ,OAAK,OAAO,OAAO,SAAS,CAAC,CAAC;AAC7D,UAAA,KAAK,OAAO,SAAS;AACpB,WAAA,SAAS,KAAK,MAAM,KAAK;AAAA,IAAA;AAAA,EAChC;AAEF,OAAK,CAAC,UAAU,WAAW,QAAQ,MAAM,MAAM,OAAO;AAC/C,SAAA,SAAS,KAAK,SAAS,KAAK,OAAO,QAAQ,OAAO,SAAS,IAAI;AAAA,EAAA;AAMtE,MAAI,aAAa,KAAK;AACf,SAAA,WAAW,KAAK,aAAa,MAAM,WAAW,UAAU,CAAC,UAAU,KAAK,QAAQ,CAAC;AAAA,EAAA;AAExF,SAAO,WAAW,IAAI;AACxB;AAMA,SAASkC,aAAW,QAAQ;AACtB,MAAA;AACA,MAAA;AAAA,IACF;AAAA,EAAA,IACE,WAAW,SAAS,CAAA,IAAK;AACzB,MAAA;AAAA,IACF;AAAA,EAAA,IACEJ,uBAAqB,eAAe,UAAU;AAC9C,MAAA,QAAQ,mBAAmB,oBAAoB,UAAU;AACzD,MAAA,cAAcN,aAAM,WAAW,eAAe;AAC9C,MAAA,QAAQA,aAAM,WAAWS,YAAmB;AAChD,MAAI,WAAW,iBAAiB,MAAM,QAAQ,MAAM,QAAQ,SAAS,CAAC,MAAM,OAAO,SAAS,eAAe,MAAM;AAChH,GAAA,cAAsDF,YAAiB,OAAO,kDAAkD,IAA8B;AAC9J,GAAA,QAAgDA,YAAiB,OAAO,+CAA+C,IAA8B;AACpJ,IAAA,WAAW,QAAgDA,YAAiB,OAAO,kEAAoE,IAA8B;AAInL,MAAA,aAAa,YAAY,UAAA,IAAc;AAC3C,MAAI,CAAC,YAAY,aAAa,IAAI5B,aAAAA,SAAe,OAAO,UAAU;AAC9D,MAAA,OAAO,QAAQ,YAAY;AAC7B,kBAAc,GAAG;AAAA,EAAA,WACR,CAAC,YAAY;AAEtB,kBAAc,oBAAoB;AAAA,EAAA;AAGpCI,eAAAA,UAAgB,MAAM;AACpB,WAAO,WAAW,UAAU;AAC5B,WAAO,MAAM;AAIX,aAAO,cAAc,UAAU;AAAA,IACjC;AAAA,EAAA,GACC,CAAC,QAAQ,UAAU,CAAC;AAEvB,MAAI,OAAOF,aAAAA,YAAkB,CAAC,MAAM,SAAS;AAC1C,KAAA,UAAkD0B,YAAiB,OAAO,yCAAyC,IAA8B;AAClJ,WAAO,MAAM,YAAY,SAAS,MAAM,IAAI;AAAA,EAC3C,GAAA,CAAC,YAAY,SAAS,MAAM,CAAC;AAChC,MAAI,aAAa,UAAU;AAC3B,MAAI,SAAS1B,aAAAA,YAAkB,CAAC,QAAQ,SAAS;AAC/C,eAAW,QAAQL,WAAS,CAAA,GAAI,MAAM;AAAA,MACpC,UAAU;AAAA,MACV;AAAA,IAAA,CACD,CAAC;AAAA,EAAA,GACD,CAAC,YAAY,UAAU,CAAC;AACvB,MAAA,cAAcW,aAAAA,QAAc,MAAM;AACpC,QAAIwB,eAA2Bb,6BAAAA,WAAiB,CAAC,OAAO,QAAQ;AAC9D,0CAAwC,cAAAK,QAAM3B,WAAS,CAAA,GAAI,OAAO;AAAA,QAChE,UAAU;AAAA,QACV;AAAA,QACA;AAAA,MAAA,CACD,CAAC;AAAA,IAAA,CACH;AAC0C;AACzCmC,mBAAY,cAAc;AAAA,IAAA;AAErBA,WAAAA;AAAAA,EAAA,GACN,CAAC,UAAU,CAAC;AAEf,MAAI,UAAU,MAAM,SAAS,IAAI,UAAU,KAAK;AAC5C,MAAAC,QAAO,YAAY,IAAI,UAAU;AACrC,MAAI,wBAAwBzB,qBAAc,MAAMX,WAAS;AAAA,IACvD,MAAM;AAAA,IACN;AAAA,IACA;AAAA,KACC,SAAS;AAAA,IACV,MAAAoC;AAAA,EAAA,CACD,GAAG,CAAC,aAAa,QAAQ,MAAM,SAASA,KAAI,CAAC;AACvC,SAAA;AACT;AAcA,MAAM,iCAAiC;AACvC,IAAI,uBAAuB,CAAC;AAI5B,SAAS,qBAAqB,QAAQ;AAChC,MAAA;AAAA,IACF;AAAA,IACA;AAAA,EAAA,IACE,WAAW,SAAS,CAAA,IAAK;AACzB,MAAA;AAAA,IACF;AAAA,EAAA,IACEN,uBAAqB,eAAe,oBAAoB;AACxD,MAAA;AAAA,IACF;AAAA,IACA;AAAA,EAAA,IACE,mBAAmB,oBAAoB,oBAAoB;AAC3D,MAAA;AAAA,IACF;AAAA,EAAA,IACEN,aAAAA,WAAiBC,iBAAwB;AAC7C,MAAI,WAAW,YAAY;AAC3B,MAAI,UAAU,WAAW;AACzB,MAAI,aAAa,cAAc;AAE/BlB,eAAAA,UAAgB,MAAM;AACpB,WAAO,QAAQ,oBAAoB;AACnC,WAAO,MAAM;AACX,aAAO,QAAQ,oBAAoB;AAAA,IACrC;AAAA,EACF,GAAG,EAAE;AAEO,cAAAF,yBAAkB,MAAM;AAC9B,QAAA,WAAW,UAAU,QAAQ;AAC/B,UAAI,OAAO,SAAS,OAAO,UAAU,OAAO,IAAI,SAAS,SAAS;AAC7C,2BAAA,GAAG,IAAI,OAAO;AAAA,IAAA;AAEjC,QAAA;AACF,qBAAe,QAAQ,cAAc,gCAAgC,KAAK,UAAU,oBAAoB,CAAC;AAAA,aAClG,OAAO;AAC0BJ,cAAe,OAAO,sGAAsG,QAAQ,IAAI;AAAA,IAAI;AAEtL,WAAO,QAAQ,oBAAoB;AAAA,EAAA,GAClC,CAAC,YAAY,QAAQ,WAAW,OAAO,UAAU,OAAO,CAAC,CAAC;AAEzD,MAAA,OAAO,aAAa,aAAa;AAEnCK,iBAAAA,gBAAsB,MAAM;AACtB,UAAA;AACF,YAAI,mBAAmB,eAAe,QAAQ,cAAc,8BAA8B;AAC1F,YAAI,kBAAkB;AACG,iCAAA,KAAK,MAAM,gBAAgB;AAAA,QAAA;AAAA,eAE7C,GAAG;AAAA,MAAA;AAAA,IAEZ,GACC,CAAC,UAAU,CAAC;AAGfA,iBAAAA,gBAAsB,MAAM;AAC1B,UAAI,wBAAwB,UAAU,aAAa,MAAM,CAAC+B,WAAUC,aAAY;AAAA;AAAA,QAChFtC,WAAS,CAAC,GAAGqC,WAAU;AAAA,UACrB,UAAU,cAAcA,UAAS,UAAU,QAAQ,KAAKA,UAAS;AAAA,QAAA,CAClE;AAAA,QAAGC;AAAAA,MAAA,IAAW;AACX,UAAA,2BAA2B,UAAU,OAAO,SAAS,OAAO,wBAAwB,sBAAsB,MAAM,OAAO,SAAS,qBAAqB;AAClJ,aAAA,MAAM,4BAA4B,yBAAyB;AAAA,IACjE,GAAA,CAAC,QAAQ,UAAU,MAAM,CAAC;AAG7BhC,iBAAAA,gBAAsB,MAAM;AAE1B,UAAI,0BAA0B,OAAO;AACnC;AAAA,MAAA;AAGE,UAAA,OAAO,0BAA0B,UAAU;AACtC,eAAA,SAAS,GAAG,qBAAqB;AACxC;AAAA,MAAA;AAGF,UAAI,SAAS,MAAM;AACb,YAAA,KAAK,SAAS,eAAe,mBAAmB,SAAS,KAAK,MAAM,CAAC,CAAC,CAAC;AAC3E,YAAI,IAAI;AACN,aAAG,eAAe;AAClB;AAAA,QAAA;AAAA,MACF;AAGF,UAAI,uBAAuB,MAAM;AAC/B;AAAA,MAAA;AAGK,aAAA,SAAS,GAAG,CAAC;AAAA,IACnB,GAAA,CAAC,UAAU,uBAAuB,kBAAkB,CAAC;AAAA,EAAA;AAE5D;AA+BA,SAAS,YAAY,UAAU,SAAS;AAClC,MAAA;AAAA,IACF;AAAA,EAAA,IACa,CAAC;AAChBC,eAAAA,UAAgB,MAAM;AAChB,QAAA,OAAO,WAAW,OAAO;AAAA,MAC3B;AAAA,IAAA,IACE;AACG,WAAA,iBAAiB,YAAY,UAAU,IAAI;AAClD,WAAO,MAAM;AACJ,aAAA,oBAAoB,YAAY,UAAU,IAAI;AAAA,IACvD;AAAA,EAAA,GACC,CAAC,UAAU,OAAO,CAAC;AACxB;AA0CA,SAAS,uBAAuB,IAAI,MAAM;AACxC,MAAI,SAAS,QAAQ;AACnB,WAAO,CAAC;AAAA,EAAA;AAEN,MAAA,YAAYiB,aAAM,WAAW,qBAAqB;AACpD,IAAA,aAAa,QAAgDO,YAAiB,OAAO,wJAA6J,IAA8B;AAC9Q,MAAA;AAAA,IACF;AAAA,EAAA,IACED,uBAAqB,eAAe,sBAAsB;AAC1D,MAAA,OAAO,gBAAgB,IAAI;AAAA,IAC7B,UAAU,KAAK;AAAA,EAAA,CAChB;AACG,MAAA,CAAC,UAAU,iBAAiB;AACvB,WAAA;AAAA,EAAA;AAEL,MAAA,cAAc,cAAc,UAAU,gBAAgB,UAAU,QAAQ,KAAK,UAAU,gBAAgB;AACvG,MAAA,WAAW,cAAc,UAAU,aAAa,UAAU,QAAQ,KAAK,UAAU,aAAa;AAc3FS,SAAAA,UAAU,KAAK,UAAU,QAAQ,KAAK,QAAQA,UAAU,KAAK,UAAU,WAAW,KAAK;AAChG;AC56CA,IAAI,OAAO;AACX,IAAI,MAAM;AACV,IAAI,oBAAoB;AACxB,IAAI,gBAAgB;AACpB,IAAI,OAAO;AACX,IAAI,oBAAoB;AACxB,IAAI,YAAY;AAChB,IAAI,cAAc;AAClB,IAAI,YAAY;AAChB,IAAI,aAAa;AACjB,IAAI,WAAW;AACf,IAAI,mBAAmB;AACvB,IAAI,eAAe;AACnB,IAAI,cAAc;AAClB,IAAI,WAAW;AACf,IAAI,cAAc;AAClB,IAAI,WAAW;AACf,IAAI,yBAAyB;AAC7B,IAAI/B,YAAW,MAAM;AAAA,EAInB,cAAc;AAHd;AACA;AACA;AAEE,SAAK,UAAU,IAAI,QAAQ,CAAC,SAAS,WAAW;AAC9C,WAAK,UAAU;AACf,WAAK,SAAS;AAAA,IACpB,CAAK;AAAA,EACL;AACA;AACA,SAAS,+BAA+B;AACtC,QAAM,UAAU,IAAI,YAAa;AACjC,MAAI,WAAW;AACf,SAAO,IAAI,gBAAgB;AAAA,IACzB,UAAU,OAAO,YAAY;AAC3B,YAAM,MAAM,QAAQ,OAAO,OAAO,EAAE,QAAQ,MAAM;AAClD,YAAM,SAAS,WAAW,KAAK,MAAM,IAAI;AACzC,iBAAW,MAAM,IAAG,KAAM;AAC1B,iBAAW,QAAQ,OAAO;AACxB,mBAAW,QAAQ,IAAI;AAAA,MAC/B;AAAA,IACK;AAAA,IACD,MAAM,YAAY;AAChB,UAAI,UAAU;AACZ,mBAAW,QAAQ,QAAQ;AAAA,MACnC;AAAA,IACA;AAAA,EACA,CAAG;AACH;AAyKuB,OAAO,oBAAoB,OAAO,SAAS,EAAE,KAAI,EAAG,KAAK,IAAI;AAOpF,IAAI,YAAY,OAAO,WAAW,cAAc,SAAS,OAAO,eAAe,cAAc,aAAa;AAC1G,SAAS,UAAU,QAAQ;AACzB,QAAM,EAAE,UAAU,OAAM,IAAK;AAC7B,MAAI,OAAO,WAAW;AACpB,WAAO,QAAQ,KAAK,MAAM,MAAM;AAClC,MAAI,CAAC,MAAM,QAAQ,MAAM,KAAK,CAAC,OAAO;AACpC,UAAM,IAAI,YAAa;AACzB,QAAM,aAAa,OAAO;AAC1B,aAAW,SAAS,QAAQ;AAC1B,WAAO,KAAK,KAAK;AAAA,EACrB;AACE,WAAS,SAAS,OAAO;AACzB,SAAO,QAAQ,KAAK,MAAM,UAAU;AACtC;AACA,SAAS,QAAQ,OAAO;AACtB,QAAM,EAAE,UAAU,QAAQ,UAAU,QAAS,IAAG;AAChD,MAAI;AACJ,QAAM,QAAQ;AAAA,IACZ;AAAA,MACE;AAAA,MACA,CAAC,MAAM;AACL,iBAAS;AAAA,MACjB;AAAA,IACA;AAAA,EACG;AACD,MAAI,UAAU,CAAE;AAChB,SAAO,MAAM,SAAS,GAAG;AACvB,UAAM,CAAC,QAAQ,GAAG,IAAI,MAAM,IAAK;AACjC,YAAQ,QAAM;AAAA,MACZ,KAAK;AACH,YAAI,MAAM;AACV;AAAA,MACF,KAAK;AACH,YAAI,IAAI;AACR;AAAA,MACF,KAAK;AACH,YAAI,GAAG;AACP;AAAA,MACF,KAAK;AACH,YAAI,QAAQ;AACZ;AAAA,MACF,KAAK;AACH,YAAI,SAAS;AACb;AAAA,MACF,KAAK;AACH,YAAI,EAAE;AACN;AAAA,IACR;AACI,QAAI,SAAS,MAAM,GAAG;AACpB,UAAI,SAAS,MAAM,CAAC;AACpB;AAAA,IACN;AACI,UAAM,QAAQ,OAAO,MAAM;AAC3B,QAAI,CAAC,SAAS,OAAO,UAAU,UAAU;AACvC,eAAS,MAAM,IAAI;AACnB,UAAI,KAAK;AACT;AAAA,IACN;AACI,QAAI,MAAM,QAAQ,KAAK,GAAG;AACxB,UAAI,OAAO,MAAM,CAAC,MAAM,UAAU;AAChC,cAAM,CAAC,MAAM,GAAG,CAAC,IAAI;AACrB,gBAAQ,MAAI;AAAA,UACV,KAAK;AACH,gBAAI,SAAS,MAAM,IAAI,IAAI,KAAK,CAAC,CAAC;AAClC;AAAA,UACF,KAAK;AACH,gBAAI,SAAS,MAAM,IAAI,IAAI,IAAI,CAAC,CAAC;AACjC;AAAA,UACF,KAAK;AACH,gBAAI,SAAS,MAAM,IAAI,OAAO,CAAC,CAAC;AAChC;AAAA,UACF,KAAK;AACH,gBAAI,SAAS,MAAM,IAAI,IAAI,OAAO,GAAG,CAAC,CAAC;AACvC;AAAA,UACF,KAAK;AACH,gBAAI,SAAS,MAAM,IAAI,OAAO,IAAI,CAAC,CAAC;AACpC;AAAA,UACF,KAAK;AACH,kBAAM,SAAyB,oBAAI,IAAK;AACxC,qBAAS,MAAM,IAAI;AACnB,qBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ;AAChC,oBAAM,KAAK;AAAA,gBACT,MAAM,CAAC;AAAA,gBACP,CAAC,MAAM;AACL,yBAAO,IAAI,CAAC;AAAA,gBAC9B;AAAA,cACA,CAAe;AACH,gBAAI,MAAM;AACV;AAAA,UACF,KAAK;AACH,kBAAM,MAAsB,oBAAI,IAAK;AACrC,qBAAS,MAAM,IAAI;AACnB,qBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK,GAAG;AACxC,oBAAM,IAAI,CAAE;AACZ,oBAAM,KAAK;AAAA,gBACT,MAAM,IAAI,CAAC;AAAA,gBACX,CAAC,MAAM;AACL,oBAAE,CAAC,IAAI;AAAA,gBACzB;AAAA,cACA,CAAe;AACD,oBAAM,KAAK;AAAA,gBACT,MAAM,CAAC;AAAA,gBACP,CAAC,MAAM;AACL,oBAAE,CAAC,IAAI;AAAA,gBACzB;AAAA,cACA,CAAe;AACD,sBAAQ,KAAK,MAAM;AACjB,oBAAI,IAAI,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AAAA,cAClC,CAAe;AAAA,YACf;AACY,gBAAI,GAAG;AACP;AAAA,UACF,KAAK;AACH,kBAAM,MAAsB,uBAAO,OAAO,IAAI;AAC9C,qBAAS,MAAM,IAAI;AACnB,uBAAW,OAAO,OAAO,KAAK,CAAC,EAAE,WAAW;AAC1C,oBAAM,IAAI,CAAE;AACZ,oBAAM,KAAK;AAAA,gBACT,EAAE,GAAG;AAAA,gBACL,CAAC,MAAM;AACL,oBAAE,CAAC,IAAI;AAAA,gBACzB;AAAA,cACA,CAAe;AACD,oBAAM,KAAK;AAAA,gBACT,OAAO,IAAI,MAAM,CAAC,CAAC;AAAA,gBACnB,CAAC,MAAM;AACL,oBAAE,CAAC,IAAI;AAAA,gBACzB;AAAA,cACA,CAAe;AACD,sBAAQ,KAAK,MAAM;AACjB,oBAAI,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;AAAA,cAC/B,CAAe;AAAA,YACf;AACY,gBAAI,GAAG;AACP;AAAA,UACF,KAAK;AACH,gBAAI,SAAS,CAAC,GAAG;AACf,kBAAI,SAAS,MAAM,IAAI,SAAS,CAAC,CAAC;AAAA,YAChD,OAAmB;AACL,oBAAM,IAAI,IAAIA,UAAU;AACxB,uBAAS,CAAC,IAAI;AACd,kBAAI,SAAS,MAAM,IAAI,EAAE,OAAO;AAAA,YAC9C;AACY;AAAA,UACF,KAAK;AACH,kBAAM,CAAG,EAAA,SAAS,SAAS,IAAI;AAC/B,gBAAI,QAAQ,aAAa,aAAa,UAAU,SAAS,IAAI,IAAI,UAAU,SAAS,EAAE,OAAO,IAAI,IAAI,MAAM,OAAO;AAClH,qBAAS,MAAM,IAAI;AACnB,gBAAI,KAAK;AACT;AAAA,UACF,KAAK;AACH,gBAAI,SAAS,MAAM,IAAI,SAAS,CAAC,CAAC;AAClC;AAAA,UACF;AACE,gBAAI,MAAM,QAAQ,OAAO,GAAG;AAC1B,oBAAM,IAAI,CAAE;AACZ,oBAAM,OAAO,MAAM,MAAM,CAAC;AAC1B,uBAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,sBAAM,IAAI,KAAK,CAAC;AAChB,sBAAM,KAAK;AAAA,kBACT;AAAA,kBACA,CAAC,OAAO;AACN,sBAAE,CAAC,IAAI;AAAA,kBAC3B;AAAA,gBACA,CAAiB;AAAA,cACjB;AACc,sBAAQ,KAAK,MAAM;AACjB,2BAAW,UAAU,SAAS;AAC5B,wBAAM,UAAU,OAAO,MAAM,CAAC,GAAG,GAAG,CAAC;AACrC,sBAAI,SAAS;AACX,wBAAI,SAAS,MAAM,IAAI,QAAQ,KAAK;AACpC;AAAA,kBACpB;AAAA,gBACA;AACgB,sBAAM,IAAI,YAAa;AAAA,cACvC,CAAe;AACD;AAAA,YACd;AACY,kBAAM,IAAI,YAAa;AAAA,QACnC;AAAA,MACA,OAAa;AACL,cAAM,QAAQ,CAAE;AAChB,iBAAS,MAAM,IAAI;AACnB,iBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,gBAAM,IAAI,MAAM,CAAC;AACjB,cAAI,MAAM,MAAM;AACd,kBAAM,KAAK;AAAA,cACT;AAAA,cACA,CAAC,MAAM;AACL,sBAAM,CAAC,IAAI;AAAA,cAC3B;AAAA,YACA,CAAa;AAAA,UACb;AAAA,QACA;AACQ,YAAI,KAAK;AACT;AAAA,MACR;AAAA,IACA,OAAW;AACL,YAAM,SAAS,CAAE;AACjB,eAAS,MAAM,IAAI;AACnB,iBAAW,OAAO,OAAO,KAAK,KAAK,EAAE,WAAW;AAC9C,cAAM,IAAI,CAAE;AACZ,cAAM,KAAK;AAAA,UACT,MAAM,GAAG;AAAA,UACT,CAAC,MAAM;AACL,cAAE,CAAC,IAAI;AAAA,UACnB;AAAA,QACA,CAAS;AACD,cAAM,KAAK;AAAA,UACT,OAAO,IAAI,MAAM,CAAC,CAAC;AAAA,UACnB,CAAC,MAAM;AACL,cAAE,CAAC,IAAI;AAAA,UACnB;AAAA,QACA,CAAS;AACD,gBAAQ,KAAK,MAAM;AACjB,iBAAO,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;AAAA,QAC5B,CAAS;AAAA,MACT;AACM,UAAI,MAAM;AACV;AAAA,IACN;AAAA,EACA;AACE,SAAO,QAAQ,SAAS,GAAG;AACzB,YAAQ,IAAG,EAAI;AAAA,EACnB;AACE,SAAO;AACT;AAGA,eAAe,OAAO,UAAU,SAAS;AACvC,QAAM,EAAE,YAAY,WAAW,CAAE;AACjC,QAAM,OAAO,IAAIA,UAAU;AAC3B,QAAM,SAAS,SAAS,YAAY,6BAA8B,CAAA,EAAE,UAAW;AAC/E,QAAM,UAAU;AAAA,IACd,QAAQ,CAAE;AAAA,IACV,UAAU,CAAE;AAAA,IACZ,UAAU,CAAE;AAAA,IACZ;AAAA,EACD;AACD,QAAM,UAAU,MAAM,cAAc,KAAK,SAAS,MAAM;AACxD,MAAI,cAAc,KAAK;AACvB,MAAI,QAAQ,MAAM;AAChB,SAAK,QAAS;AAAA,EAClB,OAAS;AACL,kBAAc,eAAe,KAAK,SAAS,MAAM,EAAE,KAAK,KAAK,OAAO,EAAE,MAAM,CAAC,WAAW;AACtF,iBAAW,YAAY,OAAO,OAAO,QAAQ,QAAQ,GAAG;AACtD,iBAAS,OAAO,MAAM;AAAA,MAC9B;AACM,WAAK,OAAO,MAAM;AAAA,IACxB,CAAK;AAAA,EACL;AACE,SAAO;AAAA,IACL,MAAM,YAAY,KAAK,MAAM,OAAO,MAAM;AAAA,IAC1C,OAAO,QAAQ;AAAA,EAChB;AACH;AACA,eAAe,cAAc,QAAQ;AACnC,QAAM,OAAO,MAAM,OAAO,KAAM;AAChC,MAAI,CAAC,KAAK,OAAO;AACf,UAAM,IAAI,YAAa;AAAA,EAC3B;AACE,MAAI;AACJ,MAAI;AACF,WAAO,KAAK,MAAM,KAAK,KAAK;AAAA,EAC7B,SAAQ,QAAQ;AACf,UAAM,IAAI,YAAa;AAAA,EAC3B;AACE,SAAO;AAAA,IACL,MAAM,KAAK;AAAA,IACX,OAAO,UAAU,KAAK,MAAM,IAAI;AAAA,EACjC;AACH;AACA,eAAe,eAAe,QAAQ;AACpC,MAAI,OAAO,MAAM,OAAO,KAAM;AAC9B,SAAO,CAAC,KAAK,MAAM;AACjB,QAAI,CAAC,KAAK;AACR;AACF,UAAM,OAAO,KAAK;AAClB,YAAQ,KAAK,CAAC,GAAC;AAAA,MACb,KAAK,cAAc;AACjB,cAAM,aAAa,KAAK,QAAQ,GAAG;AACnC,cAAM,aAAa,OAAO,KAAK,MAAM,GAAG,UAAU,CAAC;AACnD,cAAM,WAAW,KAAK,SAAS,UAAU;AACzC,YAAI,CAAC,UAAU;AACb,gBAAM,IAAI,MAAM,eAAe,UAAU,sBAAsB;AAAA,QACzE;AACQ,cAAM,WAAW,KAAK,MAAM,aAAa,CAAC;AAC1C,YAAI;AACJ,YAAI;AACF,qBAAW,KAAK,MAAM,QAAQ;AAAA,QAC/B,SAAQ,QAAQ;AACf,gBAAM,IAAI,YAAa;AAAA,QACjC;AACQ,cAAM,QAAQ,UAAU,KAAK,MAAM,QAAQ;AAC3C,iBAAS,QAAQ,KAAK;AACtB;AAAA,MACR;AAAA,MACM,KAAK,YAAY;AACf,cAAM,aAAa,KAAK,QAAQ,GAAG;AACnC,cAAM,aAAa,OAAO,KAAK,MAAM,GAAG,UAAU,CAAC;AACnD,cAAM,WAAW,KAAK,SAAS,UAAU;AACzC,YAAI,CAAC,UAAU;AACb,gBAAM,IAAI,MAAM,eAAe,UAAU,sBAAsB;AAAA,QACzE;AACQ,cAAM,WAAW,KAAK,MAAM,aAAa,CAAC;AAC1C,YAAI;AACJ,YAAI;AACF,qBAAW,KAAK,MAAM,QAAQ;AAAA,QAC/B,SAAQ,QAAQ;AACf,gBAAM,IAAI,YAAa;AAAA,QACjC;AACQ,cAAM,QAAQ,UAAU,KAAK,MAAM,QAAQ;AAC3C,iBAAS,OAAO,KAAK;AACrB;AAAA,MACR;AAAA,MACM;AACE,cAAM,IAAI,YAAa;AAAA,IAC/B;AACI,WAAO,MAAM,OAAO,KAAM;AAAA,EAC9B;AACA;AChiBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAiBA,MAAM,4BAA4B,OAAO,qBAAqB;ACjB9D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA,SAAS,WAAW;AAClB,aAAW,OAAO,SAAS,OAAO,OAAO,KAAI,IAAK,SAAU,QAAQ;AAClE,aAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,UAAI,SAAS,UAAU,CAAC;AACxB,eAAS,OAAO,QAAQ;AACtB,YAAI,OAAO,UAAU,eAAe,KAAK,QAAQ,GAAG,GAAG;AACrD,iBAAO,GAAG,IAAI,OAAO,GAAG;AAAA,QAClC;AAAA,MACA;AAAA,IACA;AACI,WAAO;AAAA,EACR;AACD,SAAO,SAAS,MAAM,MAAM,SAAS;AACvC;ACvBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA,SAAS,UAAU,OAAO,SAAS;AACjC,MAAI,UAAU,SAAS,UAAU,QAAQ,OAAO,UAAU,aAAa;AACrE,UAAM,IAAI,MAAM,OAAO;AAAA,EAC3B;AACA;ACdA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA0DA,eAAe,gBAAgB,OAAO,mBAAmB;AACnD,MAAA,MAAM,MAAM,mBAAmB;AAC1B,WAAA,kBAAkB,MAAM,EAAE;AAAA,EAAA;AAE/B,MAAA;AACF,QAAI,cAAc,MAAM;AAAA;AAAA,MAAiC,MAAM;AAAA;AAC7C,sBAAA,MAAM,EAAE,IAAI;AACvB,WAAA;AAAA,WACA,OAAO;AAWd,YAAQ,MAAM,gCAAgC,MAAM,MAAM,uBAAuB;AACjF,YAAQ,MAAM,KAAK;AACnB,QAAI,OAAO,eAAe;AAAA,IAE1B,MAAwC;AAOxC,WAAO,SAAS,OAAO;AAChB,WAAA,IAAI,QAAQ,MAAM;AAAA,IAAA,CAExB;AAAA,EAAA;AAEL;AC7FA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAyBA,SAAS,wBAAwB,SAAS,cAAc,UAAU;AAChE,MAAI,cAAc,QAAQ,IAAI,WAAS;AACrC,QAAI;AACJ,QAAI,SAAS,aAAa,MAAM,MAAM,EAAE;AACxC,QAAI,QAAQ,SAAS,OAAO,MAAM,MAAM,EAAE;AAC1C,WAAO,CAAC,MAAM,MAAM,MAAM,IAAI,IAAI,WAAS;AAAA,MACzC,KAAK;AAAA,MACL;AAAA,IACN,EAAM,IAAI,CAAE,IAAG,WAAW,QAAQ,WAAW,SAAS,UAAU,gBAAgB,OAAO,WAAW,QAAQ,kBAAkB,SAAS,SAAS,cAAc,KAAK,MAAM,MAAM,EAAE;AAAA,EAC/K,CAAG,EAAE,KAAK,CAAC;AACT,MAAI,WAAW,iCAAiC,SAAS,QAAQ;AACjE,SAAO,sBAAsB,aAAa,QAAQ;AACpD;AACA,eAAe,mBAAmB,OAAO,aAAa;AACpD,MAAI,YAAY;AAChB,MAAI,CAAC,MAAM,OAAO,CAAC,YAAY,SAAS,CAAC,qBAAsB;AAC/D,MAAI,cAAc,GAAG,aAAa,MAAM,SAAS,QAAQ,eAAe,SAAS,SAAS,WAAW,IAAI,WAAS;AAAA,IAChH,KAAK;AAAA,IACL;AAAA,EACJ,EAAI,MAAM,CAAA,KAAM,qBAAqB,YAAY,WAAW,QAAQ,uBAAuB,SAAS,SAAS,mBAAmB,KAAK,WAAW,MAAM,EAAE,EAAE,KAAK,CAAC;AAC9J,MAAI,YAAY,WAAW,EAAG;AAC9B,MAAI,aAAa,CAAE;AACnB,WAAS,cAAc,aAAa;AAClC,QAAI,CAAC,qBAAqB,UAAU,KAAK,WAAW,QAAQ,cAAc;AACxE,iBAAW,KAAK;AAAA,QACd,GAAG;AAAA,QACH,KAAK;AAAA,QACL,IAAI;AAAA,MACZ,CAAO;AAAA,IACP;AAAA,EACA;AAIE,MAAI,gBAAgB,WAAW,OAAO,WAAS,CAAC,KAAK,SAAS,OAAO,WAAW,KAAK,KAAK,EAAE,YAAY,CAAC,SAAS,cAAc,gCAAgC,KAAK,IAAI,IAAI,CAAC;AAC9K,QAAM,QAAQ,IAAI,cAAc,IAAI,iBAAiB,CAAC;AACxD;AACA,eAAe,kBAAkB,YAAY;AAC3C,SAAO,IAAI,QAAQ,aAAW;AAC5B,QAAI,OAAO,SAAS,cAAc,MAAM;AACxC,WAAO,OAAO,MAAM,UAAU;AAC9B,aAAS,aAAa;AAIpB,UAAI,SAAS,KAAK,SAAS,IAAI,GAAG;AAChC,iBAAS,KAAK,YAAY,IAAI;AAAA,MACtC;AAAA,IACA;AACI,SAAK,SAAS,MAAM;AAClB,iBAAY;AACZ,cAAS;AAAA,IACV;AACD,SAAK,UAAU,MAAM;AACnB,iBAAY;AACZ,cAAS;AAAA,IACV;AACD,aAAS,KAAK,YAAY,IAAI;AAAA,EAClC,CAAG;AACH;AAGA,SAAS,qBAAqB,QAAQ;AACpC,SAAO,UAAU,QAAQ,OAAO,OAAO,SAAS;AAClD;AACA,SAAS,qBAAqB,QAAQ;AACpC,MAAI,UAAU,MAAM;AAClB,WAAO;AAAA,EACX;AAKE,MAAI,OAAO,QAAQ,MAAM;AACvB,WAAO,OAAO,QAAQ,aAAa,OAAO,OAAO,gBAAgB,YAAY,OAAO,OAAO,eAAe;AAAA,EAC9G;AACE,SAAO,OAAO,OAAO,QAAQ,YAAY,OAAO,OAAO,SAAS;AAClE;AACA,eAAe,sBAAsB,SAAS,UAAU,cAAc;AACpE,MAAI,QAAQ,MAAM,QAAQ,IAAI,QAAQ,IAAI,OAAM,UAAS;AACvD,QAAI,MAAM,MAAM,gBAAgB,SAAS,OAAO,MAAM,MAAM,EAAE,GAAG,YAAY;AAC7E,WAAO,IAAI,QAAQ,IAAI,MAAO,IAAG,CAAE;AAAA,EACvC,CAAG,CAAC;AACF,SAAO,sBAAsB,MAAM,KAAK,CAAC,EAAE,OAAO,oBAAoB,EAAE,OAAO,UAAQ,KAAK,QAAQ,gBAAgB,KAAK,QAAQ,SAAS,EAAE,IAAI,UAAQ,KAAK,QAAQ,eAAe;AAAA,IAClL,GAAG;AAAA,IACH,KAAK;AAAA,IACL,IAAI;AAAA,EACR,IAAM;AAAA,IACF,GAAG;AAAA,IACH,KAAK;AAAA,EACT,CAAG,CAAC;AACJ;AAGA,SAAS,sBAAsB,MAAM,aAAa,gBAAgB,UAAU,UAAU,QAAQ,MAAM;AAClG,MAAI,OAAO,eAAe,IAAI;AAC9B,MAAI,QAAQ,CAAC,OAAO,UAAU;AAC5B,QAAI,CAAC,eAAe,KAAK,EAAG,QAAO;AACnC,WAAO,MAAM,MAAM,OAAO,eAAe,KAAK,EAAE,MAAM;AAAA,EACvD;AACD,MAAI,mBAAmB,CAAC,OAAO,UAAU;AACvC,QAAI;AACJ;AAAA;AAAA,MAEE,eAAe,KAAK,EAAE,aAAa,MAAM;AAAA;AAAA,QAGvC,wBAAwB,eAAe,KAAK,EAAE,MAAM,UAAU,QAAQ,0BAA0B,SAAS,SAAS,sBAAsB,SAAS,GAAG,MAAM,eAAe,KAAK,EAAE,OAAO,GAAG,MAAM,MAAM,OAAO,GAAG;AAAA;AAAA,EAErN;AAID,MAAI,aAAa,SAAS,WAAW,OAAO,kBAAkB,SAAS,WAAW,KAAK;AAAA;AAAA;AAAA,IAGvF,YAAY,OAAO,CAAC,OAAO,UAAU;AACnC,UAAI,gBAAgB,SAAS,OAAO,MAAM,MAAM,EAAE;AAClD,UAAI,CAAC,cAAc,WAAW;AAC5B,eAAO;AAAA,MACb;AACI,UAAI,MAAM,OAAO,KAAK,KAAK,iBAAiB,OAAO,KAAK,GAAG;AACzD,eAAO;AAAA,MACb;AAKI,UAAI,0BAA0B,OAAO,kBAAkB,SAAS,WAAW,KAAK;AAChF,UAAI,MAAM,MAAM,kBAAkB;AAChC,YAAI;AACJ,YAAI,cAAc,MAAM,MAAM,iBAAiB;AAAA,UAC7C,YAAY,IAAI,IAAI,SAAS,WAAW,SAAS,SAAS,SAAS,MAAM,OAAO,MAAM;AAAA,UACtF,iBAAiB,mBAAmB,eAAe,CAAC,OAAO,QAAQ,qBAAqB,SAAS,SAAS,iBAAiB,WAAW,CAAE;AAAA,UACxI,SAAS,IAAI,IAAI,MAAM,OAAO,MAAM;AAAA,UACpC,YAAY,MAAM;AAAA,UAClB;AAAA,QACR,CAAO;AACD,YAAI,OAAO,gBAAgB,WAAW;AACpC,iBAAO;AAAA,QACf;AAAA,MACA;AACI,aAAO;AAAA,IACR,CAAA;AAAA,MAAI,YAAY,OAAO,CAAC,OAAO,UAAU;AACxC,QAAI,gBAAgB,SAAS,OAAO,MAAM,MAAM,EAAE;AAClD,YAAQ,SAAS,YAAY,cAAc,eAAe,MAAM,OAAO,KAAK,KAAK,iBAAiB,OAAO,KAAK;AAAA,EAClH,CAAG;AACD,SAAO;AACT;AACA,SAAS,iBAAiB,MAAM,SAAS,UAAU;AACjD,MAAI,OAAO,eAAe,IAAI;AAC9B,SAAO,YAAY,QAAQ,OAAO,WAAS,SAAS,OAAO,MAAM,MAAM,EAAE,EAAE,aAAa,CAAC,SAAS,OAAO,MAAM,MAAM,EAAE,EAAE,eAAe,EAAE,IAAI,WAAS;AACrJ,QAAI;AAAA,MACF;AAAA,MACA;AAAA,IACN,IAAQ;AACJ,QAAI,eAAe,IAAI,gBAAgB,MAAM;AAC7C,iBAAa,IAAI,SAAS,MAAM,MAAM,EAAE;AACxC,WAAO,GAAG,QAAQ,IAAI,YAAY;AAAA,EACtC,CAAG,CAAC;AACJ;AACA,SAAS,mBAAmB,SAAS,eAAe;AAClD,SAAO,YAAY,QAAQ,IAAI,WAAS;AACtC,QAAI,QAAQ,cAAc,OAAO,MAAM,MAAM,EAAE;AAC/C,QAAI,QAAQ,CAAC,MAAM,MAAM;AACzB,QAAI,MAAM,SAAS;AACjB,cAAQ,MAAM,OAAO,MAAM,OAAO;AAAA,IACxC;AACI,WAAO;AAAA,EACX,CAAG,EAAE,KAAK,CAAC,CAAC;AACZ;AAKA,SAAS,iCAAiC,SAAS,UAAU;AAC3D,SAAO,YAAY,QAAQ,IAAI,WAAS;AACtC,QAAI,QAAQ,SAAS,OAAO,MAAM,MAAM,EAAE;AAC1C,QAAI,QAAQ,CAAC,MAAM,MAAM;AACzB,QAAI,MAAM,SAAS;AACjB,cAAQ,MAAM,OAAO,MAAM,OAAO;AAAA,IACxC;AACI,WAAO;AAAA,EACX,CAAG,EAAE,KAAK,CAAC,CAAC;AACZ;AACA,SAAS,YAAY,OAAO;AAC1B,SAAO,CAAC,GAAG,IAAI,IAAI,KAAK,CAAC;AAC3B;AACA,SAAS,SAAS,KAAK;AACrB,MAAI,SAAS,CAAE;AACf,MAAI,OAAO,OAAO,KAAK,GAAG,EAAE,KAAM;AAClC,WAAS,OAAO,MAAM;AACpB,WAAO,GAAG,IAAI,IAAI,GAAG;AAAA,EACzB;AACE,SAAO;AACT;AACA,SAAS,sBAAsB,aAAa,UAAU;AACpD,MAAI,MAAM,oBAAI,IAAK;AACnB,MAAI,cAAc,IAAI,IAAI,QAAQ;AAClC,SAAO,YAAY,OAAO,CAAC,SAAS,eAAe;AACjD,QAAI,uBAAuB,YAAY,CAAC,qBAAqB,UAAU,KAAK,WAAW,OAAO,YAAY,WAAW,QAAQ,YAAY,IAAI,WAAW,IAAI;AAC5J,QAAI,sBAAsB;AACxB,aAAO;AAAA,IACb;AACI,QAAI,MAAM,KAAK,UAAU,SAAS,UAAU,CAAC;AAC7C,QAAI,CAAC,IAAI,IAAI,GAAG,GAAG;AACjB,UAAI,IAAI,GAAG;AACX,cAAQ,KAAK;AAAA,QACX;AAAA,QACA,MAAM;AAAA,MACd,CAAO;AAAA,IACP;AACI,WAAO;AAAA,EACR,GAAE,EAAE;AACP;AAGA,SAAS,eAAe,MAAM;AAC5B,MAAI,OAAO,UAAU,IAAI;AACzB,MAAI,KAAK,WAAW,OAAW,MAAK,SAAS;AAC7C,SAAO;AACT;AAKA,IAAI;AACJ,SAAS,qBAAqB;AAC5B,MAAI,wBAAwB,QAAW;AACrC,WAAO;AAAA,EACX;AACE,MAAI,KAAK,SAAS,cAAc,MAAM;AACtC,wBAAsB,GAAG,QAAQ,SAAS,SAAS;AACnD,OAAK;AACL,SAAO;AACT;ACpQA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAgBA,MAAM,gBAAgB;AAAA,EACpB,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,UAAU;AAAA,EACV,UAAU;AACZ;AACA,MAAM,eAAe;AACrB,SAAS,WAAW,MAAM;AACxB,SAAO,KAAK,QAAQ,cAAc,WAAS,cAAc,KAAK,CAAC;AACjE;AACA,SAAS,WAAW,MAAM;AACxB,SAAO;AAAA,IACL,QAAQ;AAAA,EACT;AACH;AC/BA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAgBA,SAAS,gBAAgB,UAAU;AACjC,SAAO,SAAS,QAAQ,IAAI,eAAe,KAAK;AAClD;AACA,SAAS,gBAAgB,UAAU;AACjC,SAAO,SAAS,QAAQ,IAAI,eAAe,KAAK;AAClD;AACA,SAAS,uBAAuB,UAAU;AAUxC,SAAO,WAAW,QAAQ,KAAK,SAAS,UAAU,OAAO,SAAS,QAAQ,IAAI,eAAe,KAAK,QAAQ,SAAS,QAAQ,IAAI,eAAe,KAAK,QAAQ,SAAS,QAAQ,IAAI,kBAAkB,KAAK;AACzM;AACA,SAAS,mBAAmB,UAAU;AACpC,SAAO,SAAS,QAAQ,IAAI,kBAAkB,KAAK;AACrD;AACA,SAAS,mBAAmB,UAAU;AACpC,MAAI;AACJ,SAAO,CAAC,GAAG,wBAAwB,SAAS,QAAQ,IAAI,cAAc,OAAO,QAAQ,0BAA0B,UAAU,sBAAsB,MAAM,sBAAsB;AAC7K;AACA,SAAS,WAAW,OAAO;AACzB,SAAO,SAAS,QAAQ,OAAO,MAAM,WAAW,YAAY,OAAO,MAAM,eAAe,YAAY,OAAO,MAAM,YAAY,YAAY,OAAO,MAAM,SAAS;AACjK;AACA,SAAS,eAAe,OAAO;AAC7B,MAAI,WAAW;AACf,SAAO,YAAY,OAAO,aAAa,YAAY,OAAO,SAAS,SAAS,YAAY,OAAO,SAAS,cAAc,cAAc,OAAO,SAAS,WAAW,cAAc,OAAO,SAAS,gBAAgB;AAC/M;AACA,eAAe,UAAU,SAAS,SAAS,QAAQ,GAAG;AACpD,MAAI,MAAM,IAAI,IAAI,QAAQ,GAAG;AAC7B,MAAI,aAAa,IAAI,SAAS,OAAO;AACrC,MAAI,QAAQ,GAAG;AAGb,UAAM,IAAI,QAAQ,aAAW,WAAW,SAAS,KAAK,QAAQ,EAAE,CAAC;AAAA,EACrE;AACE,MAAI,OAAO,MAAM,kBAAkB,OAAO;AAC1C,MAAI,eAAe,OAAO;AAC1B,MAAI,WAAW,MAAM,MAAM,IAAI,MAAM,IAAI,EAAE,MAAM,WAAS;AACxD,QAAI,OAAO,iBAAiB,YAAY,iBAAiB,OAAO,wBAAwB,UAAU,QAAQ,UAAU,SAAS,SAAS,MAAM,UAAU,eAAe,QAAQ,GAAG;AAC9K,aAAO,UAAU,SAAS,SAAS,QAAQ,CAAC;AAAA,IAClD;AACI,UAAM;AAAA,EACV,CAAG;AACD,MAAI,gBAAgB,QAAQ,GAAG;AAC7B,QAAI4B,QAAO,MAAM,SAAS,KAAM;AAChC,QAAI,QAAQ,IAAI,MAAMA,MAAK,OAAO;AAClC,UAAM,QAAQA,MAAK;AACnB,WAAO;AAAA,EACX;AACE,MAAI,uBAAuB,QAAQ,GAAG;AACpC,QAAI,OAAO,MAAM,SAAS,KAAM;AAChC,QAAI,QAAQ,IAAI,MAAM,IAAI;AAC1B,UAAM,QAAQ;AACd,WAAO;AAAA,EACX;AACE,SAAO;AACT;AACA,eAAe,kBAAkB,SAAS;AACxC,MAAI,OAAO;AAAA,IACT,QAAQ,QAAQ;AAAA,EACjB;AACD,MAAI,QAAQ,WAAW,OAAO;AAC5B,SAAK,SAAS,QAAQ;AACtB,QAAI,cAAc,QAAQ,QAAQ,IAAI,cAAc;AAIpD,QAAI,eAAe,wBAAwB,KAAK,WAAW,GAAG;AAC5D,WAAK,UAAU;AAAA,QACb,gBAAgB;AAAA,MACjB;AACD,WAAK,OAAO,KAAK,UAAU,MAAM,QAAQ,MAAM;AAAA,IAChD,WAAU,eAAe,kBAAkB,KAAK,WAAW,GAAG;AAC7D,WAAK,UAAU;AAAA,QACb,gBAAgB;AAAA,MACjB;AACD,WAAK,OAAO,MAAM,QAAQ,KAAM;AAAA,IACjC,WAAU,eAAe,yCAAyC,KAAK,WAAW,GAAG;AACpF,WAAK,OAAO,IAAI,gBAAgB,MAAM,QAAQ,KAAI,CAAE;AAAA,IAC1D,OAAW;AACL,WAAK,OAAO,MAAM,QAAQ,SAAU;AAAA,IAC1C;AAAA,EACA;AACE,SAAO;AACT;AACA,MAAM,oCAAoC;AAC1C,eAAe,4BAA4B,QAAQ;AACjD,MAAI,CAAC,QAAQ;AACX,UAAM,IAAI,MAAM,sDAAsD;AAAA,EAC1E;AACE,MAAI;AACJ,MAAI,oBAAoB,CAAE;AAC1B,MAAI;AACF,QAAI,gBAAgB,mBAAmB,MAAM;AAG7C,QAAI,uBAAuB,MAAM,cAAc,KAAM;AACrD,QAAI,iBAAiB,qBAAqB;AAC1C,QAAI,CAAC,eAAgB,OAAM,IAAI,MAAM,kBAAkB;AACvD,QAAI,eAAe,KAAK,MAAM,cAAc;AAG5C,QAAI,OAAO,iBAAiB,YAAY,iBAAiB,MAAM;AAC7D,eAAS,CAAC,UAAU,KAAK,KAAK,OAAO,QAAQ,YAAY,GAAG;AAC1D,YAAI,OAAO,UAAU,YAAY,CAAC,MAAM,WAAW,iCAAiC,GAAG;AACrF;AAAA,QACV;AACQ,uBAAe,gBAAgB,CAAE;AACjC,qBAAa,QAAQ,IAAI,IAAI,QAAQ,CAAC,SAAS,WAAW;AACxD,4BAAkB,QAAQ,IAAI;AAAA,YAC5B,SAAS,CAAAI,WAAS;AAChB,sBAAQA,MAAK;AACb,qBAAO,kBAAkB,QAAQ;AAAA,YAClC;AAAA,YACD,QAAQ,WAAS;AACf,qBAAO,KAAK;AACZ,qBAAO,kBAAkB,QAAQ;AAAA,YAC/C;AAAA,UACW;AAAA,QACX,CAAS;AAAA,MACT;AAAA,IACA;AAGI,UAAM,YAAY;AAChB,UAAI;AACF,uBAAe,WAAW,eAAe;AAEvC,cAAI,CAAC,OAAO,GAAG,kBAAkB,IAAI,QAAQ,MAAM,GAAG;AACtD,cAAI,oBAAoB,mBAAmB,KAAK,GAAG;AACnD,cAAIJ,QAAO,KAAK,MAAM,iBAAiB;AACvC,cAAI,UAAU,QAAQ;AACpB,qBAAS,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQA,KAAI,GAAG;AAC7C,kBAAI,kBAAkB,GAAG,GAAG;AAC1B,kCAAkB,GAAG,EAAE,QAAQ,KAAK;AAAA,cACpD;AAAA,YACA;AAAA,UACA,WAAqB,UAAU,SAAS;AAC5B,qBAAS,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQA,KAAI,GAAG;AAC7C,kBAAI,MAAM,IAAI,MAAM,MAAM,OAAO;AACjC,kBAAI,QAAQ,MAAM;AAClB,kBAAI,kBAAkB,GAAG,GAAG;AAC1B,kCAAkB,GAAG,EAAE,OAAO,GAAG;AAAA,cACjD;AAAA,YACA;AAAA,UACA;AAAA,QACA;AACQ,iBAAS,CAAC,KAAK,QAAQ,KAAK,OAAO,QAAQ,iBAAiB,GAAG;AAC7D,mBAAS,OAAO,IAAI,qBAAqB,YAAY,GAAG,yBAAyB,CAAC;AAAA,QAC5F;AAAA,MACO,SAAQ,OAAO;AAEd,iBAAS,YAAY,OAAO,OAAO,iBAAiB,GAAG;AACrD,mBAAS,OAAO,KAAK;AAAA,QAC/B;AAAA,MACA;AAAA,IACA,GAAQ;AACJ,WAAO,IAAIK,aAAoB;AAAA,MAC7B,GAAG;AAAA,MACH,GAAG;AAAA,IACT,CAAK;AAAA,EACF,SAAQ,OAAO;AACd,aAAS,YAAY,OAAO,OAAO,iBAAiB,GAAG;AACrD,eAAS,OAAO,KAAK;AAAA,IAC3B;AACI,UAAM;AAAA,EACV;AACA;AACA,gBAAgB,mBAAmB,QAAQ;AACzC,MAAI,SAAS,OAAO,UAAW;AAC/B,MAAI,SAAS,CAAE;AACf,MAAI,WAAW,CAAE;AACjB,MAAI,SAAS;AACb,MAAI,UAAU,IAAI,YAAa;AAC/B,MAAI,UAAU,IAAI,YAAa;AAC/B,MAAI,oBAAoB,YAAY;AAClC,QAAI,SAAS,SAAS,EAAG,QAAO,SAAS,MAAO;AAGhD,WAAO,CAAC,UAAU,SAAS,WAAW,GAAG;AACvC,UAAI,QAAQ,MAAM,OAAO,KAAM;AAC/B,UAAI,MAAM,MAAM;AACd,iBAAS;AACT;AAAA,MACR;AAEM,aAAO,KAAK,MAAM,KAAK;AACvB,UAAI;AAEF,YAAI,iBAAiB,QAAQ,OAAO,YAAY,GAAG,MAAM,CAAC;AAC1D,YAAI,gBAAgB,eAAe,MAAM,MAAM;AAC/C,YAAI,cAAc,UAAU,GAAG;AAE7B,mBAAS,KAAK,GAAG,cAAc,MAAM,GAAG,EAAE,CAAC;AAE3C,mBAAS,CAAC,QAAQ,OAAO,cAAc,MAAM,EAAE,EAAE,KAAK,MAAM,CAAC,CAAC;AAAA,QACxE;AAIQ,YAAI,SAAS,SAAS,GAAG;AACvB;AAAA,QACV;AAAA,MACA,QAAc;AAIN;AAAA,MACR;AAAA,IACA;AAGI,QAAI,SAAS,SAAS,GAAG;AACvB,aAAO,SAAS,MAAO;AAAA,IAC7B;AAMI,QAAI,OAAO,SAAS,GAAG;AACrB,UAAI,iBAAiB,QAAQ,OAAO,YAAY,GAAG,MAAM,CAAC;AAC1D,iBAAW,eAAe,MAAM,MAAM,EAAE,OAAO,OAAK,CAAC;AACrD,eAAS,CAAE;AAAA,IACjB;AAGI,WAAO,SAAS,MAAO;AAAA,EACxB;AACD,MAAI,UAAU,MAAM,kBAAmB;AACvC,SAAO,SAAS;AACd,UAAM;AACN,cAAU,MAAM,kBAAmB;AAAA,EACvC;AACA;AACA,SAAS,eAAe,QAAQ;AAC9B,MAAI,MAAM,IAAI,WAAW,OAAO,OAAO,CAAC,OAAO,QAAQ,QAAQ,IAAI,QAAQ,CAAC,CAAC;AAC7E,MAAI,SAAS;AACb,WAAS,OAAO,QAAQ;AACtB,QAAI,IAAI,KAAK,MAAM;AACnB,cAAU,IAAI;AAAA,EAClB;AACE,SAAO;AACT;ACxQA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAoFA,SAAS,2BAA2B,UAAU,cAAc,WAAW;AACrE,SAAO,OAAO;AAAA,IACZ;AAAA,IACA;AAAA,IACA;AAAA,EACJ,MAAQ;AAEJ,QAAI,QAAQ,WAAW,OAAO;AAC5B,aAAO,0BAA0B,SAAS,OAAO;AAAA,IACvD;AAGI,QAAI,YAAY;AACd,aAAO,iCAAiC,SAAS,OAAO;AAAA,IAC9D;AAGI,WAAO,oCAAoC,UAAU,cAAc,UAAW,GAAE,SAAS,OAAO;AAAA,EACjG;AACH;AAIA,eAAe,0BAA0B,SAAS,SAAS;AACzD,MAAI,cAAc,QAAQ,KAAK,OAAK,EAAE,UAAU;AAChD,YAAU,aAAa,uBAAuB;AAC9C,MAAI,eAAe;AACnB,MAAI,SAAS,MAAM,YAAY,QAAQ,OAAM,YAAW;AACtD,QAAIC,UAAS,MAAM,QAAQ,YAAY;AACrC,UAAI,MAAM,eAAe,QAAQ,GAAG;AACpC,UAAI,OAAO,MAAM,kBAAkB,OAAO;AAC1C,UAAI;AAAA,QACF,MAAAN;AAAA,QACA;AAAA,MACR,IAAU,MAAM,eAAe,KAAK,IAAI;AAClC,qBAAe;AACf,aAAO,wBAAwBA,OAAM,YAAY,MAAM,EAAE;AAAA,IAC/D,CAAK;AACD,WAAOM;AAAA,EACX,CAAG;AACD,MAAI,WAAW,OAAO,MAAM,KAAK,qBAAqB,OAAO,MAAM,GAAG;AACpE,WAAO;AAAA,MACL,CAAC,YAAY,MAAM,EAAE,GAAG;AAAA,IACzB;AAAA,EACL;AAIE,SAAO;AAAA,IACL,CAAC,YAAY,MAAM,EAAE,GAAG;AAAA,MACtB,MAAM,OAAO;AAAA,MACb,QAAQ,KAAK,OAAO,QAAQ,YAAY;AAAA,IAC9C;AAAA,EACG;AACH;AAIA,eAAe,oCAAoC,UAAU,cAAc,QAAQ,SAAS,SAAS;AAGnG,MAAI,eAAe,oBAAI,IAAK;AAI5B,MAAI,mBAAmB;AAKvB,MAAI,YAAY,QAAQ,IAAI,MAAM,eAAc,CAAE;AAClD,MAAI,sBAAsB,QAAQ,IAAI,UAAU,IAAI,OAAK,EAAE,OAAO,CAAC;AAInE,MAAI,iBAAiB,eAAgB;AAGrC,MAAI,MAAM,gBAAgB,eAAe,QAAQ,GAAG,CAAC;AACrD,MAAI,OAAO,MAAM,kBAAkB,OAAO;AAG1C,MAAI,UAAU,CAAE;AAChB,MAAI,iBAAiB,QAAQ,IAAI,QAAQ,IAAI,OAAO,GAAG,MAAM,EAAE,QAAQ,OAAM,YAAW;AACtF,cAAU,CAAC,EAAE,QAAS;AACtB,QAAI,CAAC,EAAE,YAAY;AACjB,UAAI;AAIJ,UAAI,CAAC,OAAO,MAAM,aAAa;AAC7B;AAAA,MACR;AAKM,UAAI,EAAE,MAAM,MAAM,OAAO,MAAM,cAAc,SAAS,OAAO,EAAE,MAAM,EAAE,EAAE,cAAc,wBAAwB,aAAa,EAAE,MAAM,EAAE,OAAO,QAAQ,0BAA0B,UAAU,sBAAsB,kBAAkB;AAC/N,2BAAmB;AACnB;AAAA,MACR;AAAA,IACA;AAII,QAAI,SAAS,OAAO,EAAE,MAAM,EAAE,EAAE,iBAAiB;AAC/C,UAAI,SAAS,OAAO,EAAE,MAAM,EAAE,EAAE,WAAW;AACzC,2BAAmB;AAAA,MAC3B;AACM,UAAI;AACF,YAAI,SAAS,MAAM,kBAAkB,SAAS,KAAK,MAAM,EAAE,MAAM,EAAE;AACnE,gBAAQ,EAAE,MAAM,EAAE,IAAI;AAAA,UACpB,MAAM;AAAA,UACN;AAAA,QACD;AAAA,MACF,SAAQ,GAAG;AACV,gBAAQ,EAAE,MAAM,EAAE,IAAI;AAAA,UACpB,MAAM;AAAA,UACN,QAAQ;AAAA,QACT;AAAA,MACT;AACM;AAAA,IACN;AAGI,QAAI,SAAS,OAAO,EAAE,MAAM,EAAE,EAAE,WAAW;AACzC,mBAAa,IAAI,EAAE,MAAM,EAAE;AAAA,IACjC;AAGI,QAAI;AACF,UAAI,SAAS,MAAM,QAAQ,YAAY;AACrC,YAAIN,QAAO,MAAM,eAAe;AAChC,eAAO,yBAAyBA,OAAM,EAAE,MAAM,EAAE;AAAA,MACxD,CAAO;AACD,cAAQ,EAAE,MAAM,EAAE,IAAI;AAAA,QACpB,MAAM;AAAA,QACN;AAAA,MACD;AAAA,IACF,SAAQ,GAAG;AACV,cAAQ,EAAE,MAAM,EAAE,IAAI;AAAA,QACpB,MAAM;AAAA,QACN,QAAQ;AAAA,MACT;AAAA,IACP;AAAA,EACG,CAAA,CAAC,CAAC;AAGH,QAAM;AASN,OAAK,CAAC,OAAO,MAAM,eAAe,aAAa,SAAS,MAAM,CAAC,OAAO,kBAAkB;AACtF,mBAAe,QAAQ,EAAE;AAAA,EAC7B,OAAS;AACL,QAAI;AAIF,UAAI,oBAAoB,aAAa,OAAO,GAAG;AAC7C,YAAI,aAAa,IAAI,WAAW,QAAQ,OAAO,OAAK,aAAa,IAAI,EAAE,MAAM,EAAE,CAAC,EAAE,IAAI,OAAK,EAAE,MAAM,EAAE,EAAE,KAAK,GAAG,CAAC;AAAA,MACxH;AACM,UAAIA,QAAO,MAAM,eAAe,KAAK,IAAI;AACzC,qBAAe,QAAQA,MAAK,IAAI;AAAA,IACjC,SAAQ,GAAG;AACV,qBAAe,OAAO,CAAC;AAAA,IAC7B;AAAA,EACA;AACE,QAAM;AACN,SAAO;AACT;AAGA,eAAe,iCAAiC,SAAS,SAAS;AAChE,MAAI,eAAe,QAAQ,KAAK,OAAK,EAAE,UAAU;AACjD,YAAU,cAAc,wBAAwB;AAChD,MAAI,SAAS,MAAM,aAAa,QAAQ,OAAM,YAAW;AACvD,QAAI,MAAM,gBAAgB,eAAe,QAAQ,GAAG,CAAC;AACrD,QAAI,OAAO,MAAM,kBAAkB,OAAO;AAC1C,WAAO,kBAAkB,SAAS,KAAK,MAAM,aAAa,MAAM,EAAE;AAAA,EACtE,CAAG;AACD,SAAO;AAAA,IACL,CAAC,aAAa,MAAM,EAAE,GAAG;AAAA,EAC1B;AACH;AACA,SAAS,kBAAkB,SAAS,KAAK,MAAM,SAAS;AACtD,SAAO,QAAQ,YAAY;AACzB,QAAI,kBAAkB,IAAI,IAAI,GAAG;AACjC,oBAAgB,aAAa,IAAI,WAAW,OAAO;AACnD,QAAI;AAAA,MACF,MAAAA;AAAA,IACN,IAAQ,MAAM,eAAe,iBAAiB,IAAI;AAC9C,WAAO,yBAAyBA,OAAM,OAAO;AAAA,EACjD,CAAG;AACH;AACA,SAAS,gBAAgB,KAAK;AAC5B,MAAI,cAAc,IAAI,aAAa,OAAO,OAAO;AACjD,MAAI,aAAa,OAAO,OAAO;AAC/B,MAAI,oBAAoB,CAAE;AAC1B,WAAS,cAAc,aAAa;AAClC,QAAI,YAAY;AACd,wBAAkB,KAAK,UAAU;AAAA,IACvC;AAAA,EACA;AACE,WAAS,UAAU,mBAAmB;AACpC,QAAI,aAAa,OAAO,SAAS,MAAM;AAAA,EAC3C;AACE,SAAO;AACT;AACA,SAAS,eAAe,QAAQ;AAC9B,MAAI,MAAM,OAAO,WAAW,WAAW,IAAI,IAAI,QAAQ,OAAO,SAAS,MAAM,IAAI;AACjF,MAAI,IAAI,aAAa,KAAK;AACxB,QAAI,WAAW;AAAA,EACnB,OAAS;AACL,QAAI,WAAW,GAAG,IAAI,SAAS,QAAQ,OAAO,EAAE,CAAC;AAAA,EACrD;AACE,SAAO;AACT;AACA,eAAe,eAAe,KAAK,MAAM;AACvC,MAAI,MAAM,MAAM,MAAM,KAAK,IAAI;AAW/B,MAAI,uBAAuB,oBAAI,IAAI,CAAC,KAAK,KAAK,KAAK,GAAG,CAAC;AACvD,MAAI,qBAAqB,IAAI,IAAI,MAAM,GAAG;AACxC,QAAI,CAAC,KAAK,UAAU,KAAK,WAAW,OAAO;AAGzC,aAAO;AAAA,QACL,QAAQ,IAAI;AAAA,QACZ,MAAM,CAAA;AAAA,MACP;AAAA,IACP,OAAW;AAEL,aAAO;AAAA,QACL,QAAQ,IAAI;AAAA,QACZ,MAAM;AAAA,UACJ,MAAM;AAAA,QAChB;AAAA,MACO;AAAA,IACP;AAAA,EACA;AACE,YAAU,IAAI,MAAM,4BAA4B;AAChD,MAAI;AACF,QAAI,UAAU,MAAM,qBAAqB,IAAI,MAAM,MAAM;AACzD,WAAO;AAAA,MACL,QAAQ,IAAI;AAAA,MACZ,MAAM,QAAQ;AAAA,IACf;AAAA,EACF,SAAQ,GAAG;AACV,YAAQ,MAAM,CAAC;AACf,UAAM,IAAI,MAAM,oDAAoD,IAAI,SAAU,CAAA,EAAE;AAAA,EACxF;AACA;AAIA,SAAS,qBAAqB,MAAM,QAAQ;AAC1C,SAAO,OAAO,MAAM;AAAA,IAClB,SAAS,CAAC,CAAC,SAAS,SAAS;AAG3B,UAAI,SAAS,kBAAkB;AAC7B,YAAI,CAAC,MAAM,SAAS,KAAK,IAAI;AAC7B,YAAI,cAAc;AAElB,YAAI,QAAQ,QAAQ,UAAU,OAAO,OAAO,IAAI,MAAM,YAAY;AAEhE,wBAAc,OAAO,IAAI;AAAA,QACnC;AACQ,YAAI,QAAQ,IAAI,YAAY,OAAO;AACnC,cAAM,QAAQ;AACd,eAAO;AAAA,UACL,OAAO;AAAA,QACR;AAAA,MACT;AACM,UAAI,SAAS,iBAAiB;AAC5B,YAAI,CAACA,OAAM,QAAQ,UAAU,IAAI;AACjC,eAAO;AAAA,UACL,OAAO,IAAIO,kBAAyB,QAAQ,YAAYP,KAAI;AAAA,QAC7D;AAAA,MACT;AACM,UAAI,SAAS,uBAAuB;AAClC,eAAO;AAAA,UACL,OAAO;AAAA,YACL,CAACQ,yBAAgC,GAAG,KAAK,CAAC;AAAA,UACtD;AAAA,QACS;AAAA,MACT;AAAA,IACA,GAAO,CAAC,MAAM,UAAU;AAClB,UAAI,SAAS,uBAAuB;AAClC,eAAO;AAAA,UACL,OAAO;AAAA,QACR;AAAA,MACT;AACM,UAAI,SAAS,4BAA4B;AACvC,eAAO;AAAA,UACL;AAAA,QACD;AAAA,MACT;AAAA,IACK,CAAA;AAAA,EACL,CAAG;AACH;AACA,SAAS,yBAAyB,SAAS,SAAS;AAClD,MAAIC,YAAW,QAAQD,yBAAgC;AACvD,MAAIC,WAAU;AACZ,WAAO,wBAAwBA,WAAU,OAAO;AAAA,EACpD;AACE,SAAO,QAAQ,OAAO,MAAM,SAAY,wBAAwB,QAAQ,OAAO,GAAG,OAAO,IAAI;AAC/F;AACA,SAAS,wBAAwB,QAAQ,SAAS;AAChD,MAAI,WAAW,QAAQ;AACrB,UAAM,OAAO;AAAA,EACjB,WAAa,cAAc,QAAQ;AAC/B,QAAI,UAAU,CAAE;AAChB,QAAI,OAAO,YAAY;AACrB,cAAQ,oBAAoB,IAAI;AAAA,IACtC;AACI,QAAI,OAAO,QAAQ;AACjB,cAAQ,yBAAyB,IAAI;AAAA,IAC3C;AACI,QAAI,OAAO,SAAS;AAClB,cAAQ,iBAAiB,IAAI;AAAA,IACnC;AACI,UAAM,SAAS,OAAO,UAAU;AAAA,MAC9B,QAAQ,OAAO;AAAA,MACf;AAAA,IACN,CAAK;AAAA,EACL,WAAa,UAAU,QAAQ;AAC3B,WAAO,OAAO;AAAA,EAClB,OAAS;AACL,UAAM,IAAI,MAAM,kCAAkC,OAAO,GAAG;AAAA,EAChE;AACA;AACA,SAAS,iBAAiB;AACxB,MAAI;AACJ,MAAI;AACJ,MAAI,UAAU,IAAI,QAAQ,CAAC,KAAK,QAAQ;AACtC,cAAU,OAAM,QAAO;AACrB,UAAI,GAAG;AACP,UAAI;AACF,cAAM;AAAA,MACP,SAAQ,GAAG;AAAA,MAAA;AAAA,IACb;AACD,aAAS,OAAM,UAAS;AACtB,UAAI,KAAK;AACT,UAAI;AACF,cAAM;AAAA,MACP,SAAQ,GAAG;AAAA,MAAA;AAAA,IACb;AAAA,EACL,CAAG;AACD,SAAO;AAAA,IACL;AAAA;AAAA,IAEA;AAAA;AAAA,IAEA;AAAA,EACD;AACH;ACtcA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAcA,MAAM,2BAA2BC,aAAAA,UAAgB;AAAA,EAC/C,YAAY,OAAO;AACjB,UAAM,KAAK;AACX,SAAK,QAAQ;AAAA,MACX,OAAO,MAAM,SAAS;AAAA,MACtB,UAAU,MAAM;AAAA,IACjB;AAAA,EACL;AAAA,EACE,OAAO,yBAAyB,OAAO;AACrC,WAAO;AAAA,MACL;AAAA,IACD;AAAA,EACL;AAAA,EACE,OAAO,yBAAyB,OAAO,OAAO;AAU5C,QAAI,MAAM,aAAa,MAAM,UAAU;AACrC,aAAO;AAAA,QACL,OAAO,MAAM,SAAS;AAAA,QACtB,UAAU,MAAM;AAAA,MACjB;AAAA,IACP;AAMI,WAAO;AAAA,MACL,OAAO,MAAM,SAAS,MAAM;AAAA,MAC5B,UAAU,MAAM;AAAA,IACjB;AAAA,EACL;AAAA,EACE,SAAS;AACP,QAAI,KAAK,MAAM,OAAO;AACpB,aAAoBhC,6BAAAA,cAAoB,+BAA+B;AAAA,QACrE,OAAO,KAAK,MAAM;AAAA,QAClB,mBAAmB;AAAA,MAC3B,CAAO;AAAA,IACP,OAAW;AACL,aAAO,KAAK,MAAM;AAAA,IACxB;AAAA,EACA;AACA;AAKA,SAAS,8BAA8B;AAAA,EACrC;AAAA,EACA;AACF,GAAG;AACD,UAAQ,MAAM,KAAK;AACnB,MAAI,eAA4BA,6BAAmB,cAAC,UAAU;AAAA,IAC5D,yBAAyB;AAAA,MACvB,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA,IAKd;AAAA,EACA,CAAG;AACD,MAAI,qBAAqB,KAAK,GAAG;AAC/B,WAAoBA,6BAAAA,cAAoB,eAAe;AAAA,MACrD,OAAO;AAAA,IACb,GAAoBA,6BAAAA,cAAoB,MAAM;AAAA,MACxC,OAAO;AAAA,QACL,UAAU;AAAA,MAClB;AAAA,IACA,GAAO,MAAM,QAAQ,KAAK,MAAM,UAAU,GAAG,YAAY;AAAA,EACzD;AACE,MAAI;AACJ,MAAI,iBAAiB,OAAO;AAC1B,oBAAgB;AAAA,EACpB,OAAS;AACL,QAAI,cAAc,SAAS,OAAO,kBAAkB,OAAO,UAAU,YAAY,cAAc,QAAQ,MAAM,SAAQ,IAAK,KAAK,UAAU,KAAK;AAC9I,oBAAgB,IAAI,MAAM,WAAW;AAAA,EACzC;AACE,SAAoBA,6BAAAA,cAAoB,eAAe;AAAA,IACrD,OAAO;AAAA,IACP;AAAA,EACJ,GAAkBA,6BAAAA,cAAoB,MAAM;AAAA,IACxC,OAAO;AAAA,MACL,UAAU;AAAA,IAChB;AAAA,EACA,GAAK,mBAAmB,GAAgBA,6BAAmB,cAAC,OAAO;AAAA,IAC/D,OAAO;AAAA,MACL,SAAS;AAAA,MACT,YAAY;AAAA,MACZ,OAAO;AAAA,MACP,UAAU;AAAA,IAChB;AAAA,EACA,GAAK,cAAc,KAAK,GAAG,YAAY;AACvC;AACA,SAAS,cAAc;AAAA,EACrB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,GAAG;AACD,MAAI;AACJ,MAAI;AAAA,IACF;AAAA,EACD,IAAG,gBAAiB;AAkBrB,OAAK,qBAAqB,aAAa,UAAU,QAAQ,uBAAuB,UAAU,mBAAmB,UAAU,CAAC,mBAAmB;AACzI,WAAO;AAAA,EACX;AACE,SAAoBA,6BAAAA,cAAoB,QAAQ;AAAA,IAC9C,MAAM;AAAA,EACP,GAAeA,6BAAmB,cAAC,QAAQ,MAAmBA,6BAAAA,cAAoB,QAAQ;AAAA,IACzF,SAAS;AAAA,EACb,CAAG,GAAgBA,6BAAmB,cAAC,QAAQ;AAAA,IAC3C,MAAM;AAAA,IACN,SAAS;AAAA,EACV,CAAA,GAAgBA,6BAAmB,cAAC,SAAS,MAAM,KAAK,CAAC,GAAgBA,6BAAAA,cAAoB,QAAQ,MAAmBA,6BAAAA,cAAoB,QAAQ;AAAA,IACnJ,OAAO;AAAA,MACL,YAAY;AAAA,MACZ,SAAS;AAAA,IACf;AAAA,EACA,GAAK,UAAU,gBAA6BA,6BAAmB,cAAC,SAAS,IAAI,IAAI,IAAI,CAAC,CAAC;AACvF;AC7JA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAiBA,SAAS,kCAAkC;AACzC,SAAoBA,6BAAAA,cAAoB,eAAe;AAAA,IACrD,OAAO;AAAA,IACP,eAAe;AAAA,EACnB,GAAkBA,6BAAAA,cAAoB,UAAU;AAAA,IAC5C,yBAAyB;AAAA,MACvB,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAQd;AAAA,EACA,CAAG,CAAC;AACJ;ACjCA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA0BA,SAAS,sBAAsB,UAAU;AACvC,MAAI,SAAS,CAAE;AACf,SAAO,OAAO,QAAQ,EAAE,QAAQ,WAAS;AACvC,QAAI,WAAW,MAAM,YAAY;AACjC,QAAI,CAAC,OAAO,QAAQ,GAAG;AACrB,aAAO,QAAQ,IAAI,CAAE;AAAA,IAC3B;AACI,WAAO,QAAQ,EAAE,KAAK,KAAK;AAAA,EAC/B,CAAG;AACD,SAAO;AACT;AACA,SAAS,mBAAmB,OAAO,aAAa,WAAW;AACzD,MAAI,YAAY,wBAAwB,WAAW;AAEnD,MAAI,kBAAkB,YAAY,oBAAoB,CAAC,aAAa,MAAM,OAAO,UAAU,YAAY,kBAAkB,MAAM,OAAO,SAAS,kCAAkC;AACjL,MAAI,gBAAgB,YAAY,gBAAgB,YAAY,gBAAgB,MAAM,OAAO,SAAS,MAAmBA,6BAAAA,cAAoB,+BAA+B;AAAA,IACtK,OAAO,cAAa;AAAA,EACrB,CAAA,IAAI;AACL,MAAI,MAAM,OAAO,UAAU,YAAY,QAAQ;AAC7C,WAAO;AAAA,MACL,GAAI,YAAY;AAAA,QACd,SAAsBA,6BAAmB,cAAC,YAAY,QAAQ,MAAmBA,6BAAmB,cAAC,WAAW,IAAI,CAAC;AAAA,MAC7H,IAAU;AAAA,QACF;AAAA,MACR;AAAA,MACM,GAAI,gBAAgB;AAAA,QAClB,cAA2BA,6BAAmB,cAAC,YAAY,QAAQ,MAAmBA,6BAAmB,cAAC,eAAe,IAAI,CAAC;AAAA,MACtI,IAAU;AAAA,QACF;AAAA,MACR;AAAA,MACM,GAAI,kBAAkB;AAAA,QACpB,wBAAqCA,6BAAmB,cAAC,YAAY,QAAQ,MAAmBA,6BAAmB,cAAC,iBAAiB,IAAI,CAAC;AAAA,MAClJ,IAAU;AAAA,QACF;AAAA,MACD;AAAA,IACF;AAAA,EACL;AACE,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,EACD;AACH;AAgCA,SAAS,4CAA4C,mBAAmB,UAAU,mBAAmB,cAAc,QAAQ,WAAW;AACpI,SAAO,mBAAmB,UAAU,mBAAmB,cAAc,QAAQ,WAAW,IAAI,sBAAsB,QAAQ,GAAG,iBAAiB;AAChJ;AACA,SAAS,gCAAgC,MAAM,OAAO,WAAW;AAC/D,MAAI,WAAW;AACb,QAAIiC,MAAK,SAAS,WAAW,mBAAmB;AAChD,QAAIC,OAAM,mBAAmBD,GAAE,2BAA2B,MAAM,EAAE;AAClE,YAAQ,MAAMC,IAAG;AACjB,UAAM,IAAIL,kBAAyB,KAAK,eAAe,IAAI,MAAMK,IAAG,GAAG,IAAI;AAAA,EAC/E;AACE,MAAI,KAAK,SAAS,WAAW,mBAAmB;AAChD,MAAI,MAAM,0BAA0B,EAAE,2CAAgD,IAAI,eAAe,MAAM,EAAE;AACjH,MAAI,SAAS,YAAY,CAAC,MAAM,aAAa,SAAS,YAAY,CAAC,MAAM,WAAW;AAClF,YAAQ,MAAM,GAAG;AACjB,UAAM,IAAIL,kBAAyB,KAAK,eAAe,IAAI,MAAM,GAAG,GAAG,IAAI;AAAA,EAC/E;AACA;AACA,SAAS,qBAAqB,MAAM,SAAS;AAC3C,MAAI,UAAU,SAAS,iBAAiB,MAAM;AAC9C,MAAI,MAAM,UAAU,OAAO,mBAAmB,OAAO,IAAI,IAAI,iEAAsE,OAAO,MAAM,IAAI;AACpJ,UAAQ,MAAM,GAAG;AACjB,QAAM,IAAIA,kBAAyB,KAAK,sBAAsB,IAAI,MAAM,GAAG,GAAG,IAAI;AACpF;AACA,SAAS,mBAAmB,UAAU,mBAAmB,cAAc,QAAQ,WAAW,WAAW,IAAI,mBAAmB,sBAAsB,QAAQ,GAAG,mBAAmB;AAC9K,UAAQ,iBAAiB,QAAQ,KAAK,CAAA,GAAI,IAAI,WAAS;AACrD,QAAI,cAAc,kBAAkB,MAAM,EAAE;AAK5C,mBAAe,iCAAiC,SAAS,QAAQ,aAAa;AAC5E,UAAI,OAAO,gBAAgB,YAAY;AACrC,YAAID,UAAS,MAAM,YAAa;AAChC,eAAOA;AAAA,MACf;AACM,UAAI,SAAS,MAAM,mBAAmB,SAAS,KAAK;AACpD,aAAO,SAAS,qBAAqB,MAAM,IAAI;AAAA,IACrD;AACI,aAAS,kBAAkB,SAAS,QAAQ,aAAa;AACvD,UAAI,CAAC,MAAM,UAAW,QAAO,QAAQ,QAAQ,IAAI;AACjD,aAAO,iCAAiC,SAAS,QAAQ,WAAW;AAAA,IAC1E;AACI,aAAS,kBAAkB,SAAS,QAAQ,aAAa;AACvD,UAAI,CAAC,MAAM,WAAW;AACpB,cAAM,qBAAqB,UAAU,MAAM,EAAE;AAAA,MACrD;AACM,aAAO,iCAAiC,SAAS,QAAQ,WAAW;AAAA,IAC1E;AACI,mBAAe,6BAA6B,SAAS;AAKnD,UAAI,eAAe,kBAAkB,MAAM,EAAE;AAC7C,UAAI,sBAAsB,eAAe,mBAAmB,OAAO,YAAY,IAAI,QAAQ,QAAS;AACpG,UAAI;AACF,eAAO,QAAS;AAAA,MACxB,UAAgB;AACR,cAAM;AAAA,MACd;AAAA,IACA;AACI,QAAI,YAAY;AAAA,MACd,IAAI,MAAM;AAAA,MACV,OAAO,MAAM;AAAA,MACb,MAAM,MAAM;AAAA,IACb;AACD,QAAI,aAAa;AACf,UAAI,uBAAuB,sBAAsB;AAEjD,aAAO,OAAO,WAAW;AAAA,QACvB,GAAG;AAAA,QACH,GAAG,mBAAmB,OAAO,aAAa,SAAS;AAAA,QACnD,QAAQ,YAAY;AAAA,QACpB,kBAAkB,4BAA4B,QAAQ,aAAa,MAAM,IAAI,iBAAiB;AAAA,MACtG,CAAO;AACD,UAAI,cAAc,iBAAiB,QAAQ,iBAAiB,SAAS,UAAU,wBAAwB,aAAa,gBAAgB,QAAQ,0BAA0B,SAAS,SAAS,sBAAsB,MAAM,EAAE;AACtN,UAAI,eAAe,iBAAiB,QAAQ,iBAAiB,SAAS,UAAU,uBAAuB,aAAa,YAAY,QAAQ,yBAAyB,SAAS,SAAS,qBAAqB,MAAM,EAAE;AAChN,UAAI,qBAAqB,qBAAqB,WAAW,wBAAwB,YAAY,kBAAkB,QAAQ,0BAA0B,SAAS,SAAS,sBAAsB,aAAa,QAAQ,CAAC,MAAM;AACrN,gBAAU,SAAS,OAAO;AAAA,QACxB;AAAA,QACA;AAAA,MACD,GAAE,gBAAgB;AACjB,YAAI;AACF,cAAI,SAAS,MAAM,6BAA6B,YAAY;AAC1D,sBAAU,aAAa,sDAAsD;AAC7E,gBAAI,CAAC,YAAY,cAAc;AAC7B,kBAAI,UAAW,QAAO;AAEtB,qBAAO,kBAAkB,SAAS,OAAO,WAAW;AAAA,YAClE;AACY,mBAAO,YAAY,aAAa;AAAA,cAC9B;AAAA,cACA;AAAA,cACA,MAAM,eAAe;AACnB,gDAAgC,UAAU,OAAO,SAAS;AAG1D,oBAAI,oBAAoB;AACtB,sBAAI,gBAAgB,QAAW;AAC7B,2BAAO;AAAA,kBAC3B;AACkB,sBAAI,iBAAiB,QAAW;AAC9B,0BAAM;AAAA,kBAC1B;AACkB,yBAAO;AAAA,gBACzB;AAGgB,uBAAO,kBAAkB,SAAS,MAAM,WAAW;AAAA,cACnE;AAAA,YACA,CAAa;AAAA,UACb,CAAW;AACD,iBAAO;AAAA,QACjB,UAAkB;AAGR,+BAAqB;AAAA,QAC/B;AAAA,MACO;AAGD,gBAAU,OAAO,UAAU,yBAAyB,OAAO,aAAa,SAAS;AACjF,gBAAU,SAAS,CAAC;AAAA,QAClB;AAAA,QACA;AAAA,MACD,GAAE,gBAAgB;AACjB,eAAO,6BAA6B,YAAY;AAC9C,oBAAU,aAAa,sDAAsD;AAC7E,cAAI,CAAC,YAAY,cAAc;AAC7B,gBAAI,WAAW;AACb,oBAAM,qBAAqB,gBAAgB,MAAM,EAAE;AAAA,YACjE;AACY,mBAAO,kBAAkB,SAAS,OAAO,WAAW;AAAA,UAChE;AACU,iBAAO,YAAY,aAAa;AAAA,YAC9B;AAAA,YACA;AAAA,YACA,MAAM,eAAe;AACnB,8CAAgC,UAAU,OAAO,SAAS;AAC1D,qBAAO,kBAAkB,SAAS,MAAM,WAAW;AAAA,YACjE;AAAA,UACA,CAAW;AAAA,QACX,CAAS;AAAA,MACF;AAAA,IACP,OAAW;AAIL,UAAI,CAAC,MAAM,iBAAiB;AAC1B,kBAAU,SAAS,CAAC;AAAA,UAClB;AAAA,QACV,GAAW,gBAAgB,6BAA6B,MAAM;AACpD,cAAI,UAAW,QAAO,QAAQ,QAAQ,IAAI;AAC1C,iBAAO,kBAAkB,SAAS,OAAO,WAAW;AAAA,QAC9D,CAAS;AAAA,MACT;AACM,UAAI,CAAC,MAAM,iBAAiB;AAC1B,kBAAU,SAAS,CAAC;AAAA,UAClB;AAAA,QACV,GAAW,gBAAgB,6BAA6B,MAAM;AACpD,cAAI,WAAW;AACb,kBAAM,qBAAqB,gBAAgB,MAAM,EAAE;AAAA,UAC/D;AACU,iBAAO,kBAAkB,SAAS,OAAO,WAAW;AAAA,QAC9D,CAAS;AAAA,MACT;AAGM,gBAAU,OAAO,YAAY;AAC3B,YAAI,MAAM,MAAM,iCAAiC,OAAO,iBAAiB;AACzE,YAAI,YAAY;AAAA,UACd,GAAG;AAAA,QACJ;AACD,YAAI,IAAI,cAAc;AACpB,cAAI,eAAe,IAAI;AACvB,oBAAU,SAAS,CAAC,MAAM,gBAAgB,aAAa;AAAA,YACrD,GAAG;AAAA,YACH,MAAM,eAAe;AACnB,8CAAgC,UAAU,OAAO,SAAS;AAC1D,qBAAO,kBAAkB,KAAK,SAAS,MAAM,WAAW;AAAA,YACtE;AAAA,UACA,CAAW;AAAA,QACX;AACQ,YAAI,IAAI,cAAc;AACpB,cAAI,eAAe,IAAI;AACvB,oBAAU,SAAS,CAAC,MAAM,gBAAgB,aAAa;AAAA,YACrD,GAAG;AAAA,YACH,MAAM,eAAe;AACnB,8CAAgC,UAAU,OAAO,SAAS;AAC1D,qBAAO,kBAAkB,KAAK,SAAS,MAAM,WAAW;AAAA,YACtE;AAAA,UACA,CAAW;AAAA,QACX;AACQ,eAAO;AAAA,UACL,GAAI,UAAU,SAAS;AAAA,YACrB,QAAQ,UAAU;AAAA,UACnB,IAAG;UACJ,GAAI,UAAU,SAAS;AAAA,YACrB,QAAQ,UAAU;AAAA,UACnB,IAAG;UACJ,kBAAkB,UAAU;AAAA,UAC5B,kBAAkB,4BAA4B,QAAQ,WAAW,MAAM,IAAI,iBAAiB;AAAA,UAC5F,QAAQ,UAAU;AAAA;AAAA;AAAA,UAGlB,WAAW,UAAU;AAAA,UACrB,eAAe,UAAU;AAAA,QAC1B;AAAA,MACF;AAAA,IACP;AACI,QAAI,WAAW,mBAAmB,UAAU,mBAAmB,cAAc,QAAQ,WAAW,MAAM,IAAI,kBAAkB,iBAAiB;AAC7I,QAAI,SAAS,SAAS,EAAG,WAAU,WAAW;AAC9C,WAAO;AAAA,EACX,CAAG;AACH;AACA,SAAS,4BAA4B,QAAQ,OAAO,SAAS,mBAAmB;AAE9E,MAAI,mBAAmB;AACrB,WAAO,2BAA2B,SAAS,MAAM,kBAAkB,iBAAiB;AAAA,EACxF;AAIE,MAAI,OAAO,kBAAkB,MAAM,kBAAkB;AACnD,QAAI,KAAK,MAAM;AACf,WAAO,UAAQ,GAAG;AAAA,MAChB,GAAG;AAAA,MACH,yBAAyB;AAAA,IAC/B,CAAK;AAAA,EACL;AACE,SAAO,MAAM;AACf;AAIA,SAAS,2BAA2B,SAAS,uBAAuB,mBAAmB;AACrF,MAAI,sBAAsB;AAC1B,SAAO,SAAO;AACZ,QAAI,CAAC,qBAAqB;AACxB,4BAAsB;AACtB,aAAO,kBAAkB,IAAI,OAAO;AAAA,IAC1C;AACI,WAAO,wBAAwB,sBAAsB,GAAG,IAAI,IAAI;AAAA,EACjE;AACH;AACA,eAAe,iCAAiC,OAAO,cAAc;AACnE,MAAI,cAAc,MAAM,gBAAgB,OAAO,YAAY;AAC3D,QAAM,mBAAmB,OAAO,WAAW;AAI3C,SAAO;AAAA,IACL,WAAW,wBAAwB,WAAW;AAAA,IAC9C,eAAe,YAAY;AAAA,IAC3B,cAAc,YAAY;AAAA,IAC1B,cAAc,YAAY;AAAA,IAC1B,QAAQ,YAAY;AAAA,IACpB,OAAO,YAAY;AAAA,IACnB,MAAM,YAAY;AAAA,IAClB,kBAAkB,YAAY;AAAA,EAC/B;AACH;AACA,eAAe,mBAAmB,SAAS,OAAO;AAChD,MAAI,SAAS,MAAM,UAAU,SAAS,MAAM,EAAE;AAC9C,MAAI,kBAAkB,OAAO;AAC3B,UAAM;AAAA,EACV;AACE,MAAI,mBAAmB,MAAM,GAAG;AAC9B,UAAM,YAAY,MAAM;AAAA,EAC5B;AACE,MAAI,gBAAgB,MAAM,GAAG;AAC3B,UAAM;AAAA,EACV;AACE,MAAI,mBAAmB,MAAM,KAAK,OAAO,MAAM;AAC7C,WAAO,MAAM,4BAA4B,OAAO,IAAI;AAAA,EACxD;AACE,SAAO;AACT;AACA,SAAS,qBAAqB,QAAQ;AACpC,MAAI,eAAe,MAAM,GAAG;AAC1B,WAAO,OAAO;AAAA,EAClB;AACE,MAAI,WAAW,MAAM,GAAG;AACtB,QAAI,cAAc,OAAO,QAAQ,IAAI,cAAc;AAGnD,QAAI,eAAe,wBAAwB,KAAK,WAAW,GAAG;AAC5D,aAAO,OAAO,KAAM;AAAA,IAC1B,OAAW;AACL,aAAO,OAAO,KAAM;AAAA,IAC1B;AAAA,EACA;AACE,SAAO;AACT;AACA,SAAS,YAAY,UAAU;AAC7B,MAAI,SAAS,SAAS,SAAS,QAAQ,IAAI,gBAAgB,GAAG,EAAE,KAAK;AACrE,MAAI,MAAM,SAAS,QAAQ,IAAI,kBAAkB;AACjD,MAAI,UAAU,CAAE;AAChB,MAAI,aAAa,SAAS,QAAQ,IAAI,oBAAoB;AAC1D,MAAI,YAAY;AACd,YAAQ,oBAAoB,IAAI;AAAA,EACpC;AACE,MAAI,iBAAiB,SAAS,QAAQ,IAAI,yBAAyB;AACnE,MAAI,gBAAgB;AAClB,YAAQ,yBAAyB,IAAI;AAAA,EACzC;AACE,MAAI,UAAU,SAAS,QAAQ,IAAI,iBAAiB;AACpD,MAAI,SAAS;AACX,YAAQ,iBAAiB,IAAI;AAAA,EACjC;AACE,SAAO,SAAS,KAAK;AAAA,IACnB;AAAA,IACA;AAAA,EACJ,CAAG;AACH;AAMA,SAAS,wBAAwB,aAAa;AAC5C,MAAI,YAAY,WAAW,KAAM,QAAO;AACxC,MAAI,gBAAgB,OAAO,YAAY,YAAY,YAAY,OAAO,KAAK,YAAY,OAAO,EAAE,WAAW;AAC3G,MAAI,CAAC,eAAe;AAClB,WAAO,YAAY;AAAA,EACvB;AACA;AACA,SAAS,yBAAyB,OAAO,aAAa,WAAW;AAC/D,SAAO,aAAa,MAAM,OAAO,UAAU,YAAY,gBAAgB,SAAS,YAAY,aAAa,YAAY,QAAQ,MAAM,cAAc;AACnJ;AC7aA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAeA,MAAM,YAAY,oBAAI,IAAK;AAI3B,MAAM,yBAAyB;AAC/B,MAAM,kBAAkB,oBAAI,IAAK;AAIjC,MAAM,YAAY;AAClB,SAAS,kBAAkB,QAAQ,WAAW;AAC5C,SAAO,OAAO,0BAA0B,QAAQ,CAAC;AACnD;AACA,SAAS,mBAAmB,UAAU,QAAQ;AAE5C,MAAI,WAAW,IAAI,IAAI,OAAO,MAAM,QAAQ,IAAI,OAAK,EAAE,MAAM,EAAE,CAAC;AAChE,MAAI,WAAW,OAAO,MAAM,SAAS,SAAS,MAAM,GAAG,EAAE,OAAO,OAAO;AACvE,MAAI,QAAQ,CAAC,GAAG;AAGhB,WAAS,IAAK;AAId,SAAO,SAAS,SAAS,GAAG;AAC1B,UAAM,KAAK,IAAI,SAAS,KAAK,GAAG,CAAC,EAAE;AACnC,aAAS,IAAK;AAAA,EAClB;AACE,QAAM,QAAQ,UAAQ;AACpB,QAAI,UAAU,YAAY,OAAO,QAAQ,MAAM,OAAO,QAAQ;AAC9D,QAAI,SAAS;AACX,cAAQ,QAAQ,OAAK,SAAS,IAAI,EAAE,MAAM,EAAE,CAAC;AAAA,IACnD;AAAA,EACA,CAAG;AACD,MAAI,gBAAgB,CAAC,GAAG,QAAQ,EAAE,OAAO,CAAC,KAAK,OAAO,OAAO,OAAO,KAAK;AAAA,IACvE,CAAC,EAAE,GAAG,SAAS,OAAO,EAAE;AAAA,EACzB,CAAA,GAAG,CAAA,CAAE;AACN,SAAO;AAAA,IACL,GAAG;AAAA,IACH,QAAQ;AAAA,EACT;AACH;AACA,SAAS,mCAAmC,UAAU,cAAc,QAAQ,WAAW,UAAU;AAC/F,MAAI,CAAC,kBAAkB,QAAQ,SAAS,GAAG;AACzC,WAAO;AAAA,EACX;AACE,SAAO,OAAO;AAAA,IACZ;AAAA,IACA;AAAA,IACA;AAAA,EACJ,MAAQ;AACJ,QAAI,gBAAgB,IAAI,IAAI,GAAG;AAC7B;AAAA,IACN;AACI,UAAM,6BAA6B,CAAC,IAAI,GAAG,UAAU,cAAc,QAAQ,WAAW,UAAU,OAAO,MAAM;AAAA,EAC9G;AACH;AACA,SAAS,qBAAqB,QAAQ,UAAU,cAAc,QAAQ,WAAW;AAC/EnC,eAAAA,UAAgB,MAAM;AACpB,QAAI;AAEJ,QAAI,CAAC,kBAAkB,QAAQ,SAAS,OAAO,wBAAwB,UAAU,gBAAgB,QAAQ,0BAA0B,SAAS,SAAS,sBAAsB,cAAc,MAAM;AAC7L;AAAA,IACN;AAGI,aAAS,gBAAgB,IAAI;AAC3B,UAAI,OAAO,GAAG,YAAY,SAAS,GAAG,aAAa,QAAQ,IAAI,GAAG,aAAa,MAAM;AACrF,UAAI,CAAC,MAAM;AACT;AAAA,MACR;AACM,UAAI,MAAM,IAAI,IAAI,MAAM,OAAO,SAAS,MAAM;AAC9C,UAAI,CAAC,gBAAgB,IAAI,IAAI,QAAQ,GAAG;AACtC,kBAAU,IAAI,IAAI,QAAQ;AAAA,MAClC;AAAA,IACA;AAGI,mBAAe,eAAe;AAC5B,UAAI,YAAY,MAAM,KAAK,UAAU,MAAM,EAAE,OAAO,UAAQ;AAC1D,YAAI,gBAAgB,IAAI,IAAI,GAAG;AAC7B,oBAAU,OAAO,IAAI;AACrB,iBAAO;AAAA,QACjB;AACQ,eAAO;AAAA,MACf,CAAO;AACD,UAAI,UAAU,WAAW,GAAG;AAC1B;AAAA,MACR;AACM,UAAI;AACF,cAAM,6BAA6B,WAAW,UAAU,cAAc,QAAQ,WAAW,OAAO,UAAU,OAAO,WAAW;AAAA,MAC7H,SAAQ,GAAG;AACV,gBAAQ,MAAM,oCAAoC,CAAC;AAAA,MAC3D;AAAA,IACA;AAGI,aAAS,KAAK,iBAAiB,uCAAuC,EAAE,QAAQ,QAAM,gBAAgB,EAAE,CAAC;AACzG,iBAAc;AAGd,QAAI,wBAAwB,SAAS,cAAc,GAAG;AACtD,aAAS,UAAU,MAAM;AACvB,aAAO,KAAK,aAAa,KAAK;AAAA,IACpC;AACI,QAAI,WAAW,IAAI,iBAAiB,aAAW;AAC7C,UAAI,WAAW,oBAAI,IAAK;AACxB,cAAQ,QAAQ,OAAK;AACnB,SAAC,EAAE,QAAQ,GAAG,EAAE,UAAU,EAAE,QAAQ,UAAQ;AAC1C,cAAI,CAAC,UAAU,IAAI,EAAG;AACtB,cAAI,KAAK,YAAY,OAAO,KAAK,aAAa,eAAe,GAAG;AAC9D,qBAAS,IAAI,IAAI;AAAA,UAC7B,WAAqB,KAAK,YAAY,UAAU,KAAK,aAAa,eAAe,GAAG;AACxE,qBAAS,IAAI,IAAI;AAAA,UAC7B;AACU,cAAI,KAAK,YAAY,KAAK;AACxB,iBAAK,iBAAiB,uCAAuC,EAAE,QAAQ,QAAM,SAAS,IAAI,EAAE,CAAC;AAAA,UACzG;AAAA,QACA,CAAS;AAAA,MACT,CAAO;AACD,eAAS,QAAQ,QAAM,gBAAgB,EAAE,CAAC;AAC1C,4BAAuB;AAAA,IAC7B,CAAK;AACD,aAAS,QAAQ,SAAS,iBAAiB;AAAA,MACzC,SAAS;AAAA,MACT,WAAW;AAAA,MACX,YAAY;AAAA,MACZ,iBAAiB,CAAC,iBAAiB,QAAQ,QAAQ;AAAA,IACzD,CAAK;AACD,WAAO,MAAM,SAAS,WAAY;AAAA,EACtC,GAAK,CAAC,QAAQ,WAAW,UAAU,cAAc,MAAM,CAAC;AACxD;AACA,eAAe,6BAA6B,OAAO,UAAU,cAAc,QAAQ,WAAW,UAAU,aAAa,QAAQ;AAC3H,MAAI,eAAe,GAAG,YAAY,GAAG,cAAc,QAAQ,QAAQ,GAAG;AACtE,MAAI,MAAM,IAAI,IAAI,cAAc,OAAO,SAAS,MAAM;AACtD,QAAM,OAAO,QAAQ,UAAQ,IAAI,aAAa,OAAO,KAAK,IAAI,CAAC;AAC/D,MAAI,aAAa,IAAI,WAAW,SAAS,OAAO;AAKhD,MAAI,IAAI,WAAW,SAAS,WAAW;AACrC,cAAU,MAAO;AACjB;AAAA,EACJ;AACE,MAAI;AACJ,MAAI;AACF,QAAI,MAAM,MAAM,MAAM,KAAK;AAAA,MACzB;AAAA,IACN,CAAK;AACD,QAAI,CAAC,IAAI,IAAI;AACX,YAAM,IAAI,MAAM,GAAG,IAAI,MAAM,IAAI,IAAI,UAAU,EAAE;AAAA,IACvD,WAAe,IAAI,UAAU,KAAK;AAC5B,YAAM,IAAI,MAAM,MAAM,IAAI,KAAI,CAAE;AAAA,IACtC;AACI,oBAAgB,MAAM,IAAI,KAAM;AAAA,EACjC,SAAQ,GAAG;AACV,QAAI,WAAW,QAAQ,WAAW,UAAU,OAAO,QAAS;AAC5D,UAAM;AAAA,EACV;AAGE,MAAI,cAAc,IAAI,IAAI,OAAO,KAAK,SAAS,MAAM,CAAC;AACtD,MAAI,UAAU,OAAO,OAAO,aAAa,EAAE,OAAO,CAAC,KAAK,UAAU,CAAC,YAAY,IAAI,MAAM,EAAE,IAAI,OAAO,OAAO,KAAK;AAAA,IAChH,CAAC,MAAM,EAAE,GAAG;AAAA,EAChB,CAAG,IAAI,KAAK,EAAE;AACZ,SAAO,OAAO,SAAS,QAAQ,OAAO;AAGtC,QAAM,QAAQ,OAAK,eAAe,GAAG,eAAe,CAAC;AAIrD,MAAI,YAAY,oBAAI,IAAK;AACzB,SAAO,OAAO,OAAO,EAAE,QAAQ,WAAS;AACtC,QAAI,CAAC,MAAM,YAAY,CAAC,QAAQ,MAAM,QAAQ,GAAG;AAC/C,gBAAU,IAAI,MAAM,QAAQ;AAAA,IAClC;AAAA,EACA,CAAG;AACD,YAAU,QAAQ,cAAY,YAAY,YAAY,MAAM,mBAAmB,SAAS,cAAc,MAAM,QAAQ,WAAW,QAAQ,CAAC,CAAC;AAC3I;AACA,SAAS,eAAe,MAAM,OAAO;AACnC,MAAI,MAAM,QAAQ,wBAAwB;AACxC,QAAI,QAAQ,MAAM,OAAM,EAAG,KAAM,EAAC;AAClC,QAAI,OAAO,UAAU,SAAU,OAAM,OAAO,KAAK;AAAA,EACrD;AACE,QAAM,IAAI,IAAI;AAChB;AAIA,SAAS,SAAS,UAAU,MAAM;AAChC,MAAI;AACJ,SAAO,IAAI,SAAS;AAClB,WAAO,aAAa,SAAS;AAC7B,gBAAY,OAAO,WAAW,MAAM,SAAS,GAAG,IAAI,GAAG,IAAI;AAAA,EAC5D;AACH;ACjMA,SAAS,uBAAuB;AAC1B,MAAA,UAAUiB,aAAM,WAAWR,iBAAwB;AACvD,YAAU,SAAS,4EAA4E;AACxF,SAAA;AACT;AACA,SAAS,4BAA4B;AAC/B,MAAA,UAAUQ,aAAM,WAAWP,sBAA6B;AAC5D,YAAU,SAAS,iFAAiF;AAC7F,SAAA;AACT;AAKM,MAAA,eAAkCf,6BAAA,cAAc,MAAS;AAC/D,aAAa,cAAc;AAC3B,SAAS,kBAAkB;AACrB,MAAA,UAAUsB,aAAM,WAAW,YAAY;AAC3C,YAAU,SAAS,uDAAuD;AACnE,SAAA;AACT;AAqBA,SAAS,oBAAoB,UAAU,mBAAmB;AACxD,MAAI,CAAC,eAAe,gBAAgB,IAAIrB,aAAAA,SAAe,KAAK;AAC5D,MAAI,CAAC,gBAAgB,iBAAiB,IAAIA,aAAAA,SAAe,KAAK;AAC1D,MAAA;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EAAA,IACE;AACA,MAAA,MAAMC,aAAM,OAAO,IAAI;AAC3BG,eAAAA,UAAgB,MAAM;AACpB,QAAI,aAAa,UAAU;AACzB,wBAAkB,IAAI;AAAA,IAAA;AAExB,QAAI,aAAa,YAAY;AAC3B,UAAI,WAAW,CAAW,YAAA;AACxB,gBAAQ,QAAQ,CAAS,UAAA;AACvB,4BAAkB,MAAM,cAAc;AAAA,QAAA,CACvC;AAAA,MACH;AACI,UAAA,WAAW,IAAI,qBAAqB,UAAU;AAAA,QAChD,WAAW;AAAA,MAAA,CACZ;AACD,UAAI,IAAI,QAAkB,UAAA,QAAQ,IAAI,OAAO;AAC7C,aAAO,MAAM;AACX,iBAAS,WAAW;AAAA,MACtB;AAAA,IAAA;AAAA,EACF,GACC,CAAC,QAAQ,CAAC;AACb,MAAI,YAAY,MAAM;AACpB,QAAI,aAAa,UAAU;AACzB,uBAAiB,IAAI;AAAA,IAAA;AAAA,EAEzB;AACA,MAAI,eAAe,MAAM;AACvB,QAAI,aAAa,UAAU;AACzB,uBAAiB,KAAK;AACtB,wBAAkB,KAAK;AAAA,IAAA;AAAA,EAE3B;AACAA,eAAAA,UAAgB,MAAM;AACpB,QAAI,eAAe;AACb,UAAA,KAAK,WAAW,MAAM;AACxB,0BAAkB,IAAI;AAAA,SACrB,GAAG;AACN,aAAO,MAAM;AACX,qBAAa,EAAE;AAAA,MACjB;AAAA,IAAA;AAAA,EACF,GACC,CAAC,aAAa,CAAC;AACX,SAAA,CAAC,gBAAgB,KAAK;AAAA,IAC3B,SAAS,qBAAqB,SAAS,SAAS;AAAA,IAChD,QAAQ,qBAAqB,QAAQ,YAAY;AAAA,IACjD,cAAc,qBAAqB,cAAc,SAAS;AAAA,IAC1D,cAAc,qBAAqB,cAAc,YAAY;AAAA,IAC7D,cAAc,qBAAqB,cAAc,SAAS;AAAA,EAAA,CAC3D;AACH;AACA,MAAM,qBAAqB;AAC3B,SAAS,gBAAgB,UAAU,YAAY,gBAAgB;AAC7D,SAAO,aAAa,YAAY,CAAC,cAAc,CAAC,iBAAiB,SAAS;AAC5E;AAOA,IAAI,UAA6Be,6BAAA,WAAW,CAAC;AAAA,EAC3C;AAAA,EACA,WAAW;AAAA,EACX,WAAW;AAAA,EACX,GAAG;AACL,GAAG,iBAAiB;AAClB,MAAI,aAAa,OAAO,OAAO,YAAY,mBAAmB,KAAK,EAAE;AACjE,MAAA,OAAO,QAAQ,EAAE;AACrB,MAAI,CAAC,gBAAgB,KAAK,gBAAgB,IAAI,oBAAoB,UAAU,KAAK;AACjF,SAA0BR,6BAAA,cAAcC,uBAAgB,MAAmBD,6BAAoB,cAAA,WAAW,SAAS,CAAA,GAAI,OAAO,kBAAkB;AAAA,IAC9I,KAAK,UAAU,cAAc,GAAG;AAAA,IAChC;AAAA,IACA,iBAAiB,gBAAgB,UAAU,YAAY,MAAM,cAAc;AAAA,EAC5E,CAAA,CAAC,GAAG,kBAAkB,CAAC,aAA0BA,6BAAAA,cAAoB,mBAAmB;AAAA,IACvF,MAAM;AAAA,EACP,CAAA,IAAI,IAAI;AACX,CAAC;AACD,QAAQ,cAAc;AAQlB,IAAA,OAA0BQ,6BAAA,WAAW,CAAC;AAAA,EACxC;AAAA,EACA,WAAW;AAAA,EACX,WAAW;AAAA,EACX,GAAG;AACL,GAAG,iBAAiB;AAClB,MAAI,aAAa,OAAO,OAAO,YAAY,mBAAmB,KAAK,EAAE;AACjE,MAAA,OAAO,QAAQ,EAAE;AACrB,MAAI,CAAC,gBAAgB,KAAK,gBAAgB,IAAI,oBAAoB,UAAU,KAAK;AACjF,SAA0BR,6BAAA,cAAcC,uBAAgB,MAAmBD,6BAAoB,cAAA,QAAQ,SAAS,CAAA,GAAI,OAAO,kBAAkB;AAAA,IAC3I,KAAK,UAAU,cAAc,GAAG;AAAA,IAChC;AAAA,IACA,iBAAiB,gBAAgB,UAAU,YAAY,MAAM,cAAc;AAAA,EAC5E,CAAA,CAAC,GAAG,kBAAkB,CAAC,aAA0BA,6BAAAA,cAAoB,mBAAmB;AAAA,IACvF,MAAM;AAAA,EACP,CAAA,IAAI,IAAI;AACX,CAAC;AACD,KAAK,cAAc;AAOf,IAAA,OAA0BQ,6BAAA,WAAW,CAAC;AAAA,EACxC,WAAW;AAAA,EACX,GAAG;AACL,GAAG,iBAAiB;AACd,MAAA,aAAa,OAAO,MAAM,WAAW,YAAY,mBAAmB,KAAK,MAAM,MAAM;AACzF,sCAAwC,cAAA,QAAQ,SAAS,CAAA,GAAI,OAAO;AAAA,IAClE,KAAK;AAAA,IACL,iBAAiB,gBAAgB,UAAU,YAAY,MAAM,cAAc;AAAA,EAAA,CAC5E,CAAC;AACJ,CAAC;AACD,KAAK,cAAc;AACnB,SAAS,qBAAqB,cAAc,YAAY;AACtD,SAAO,CAAS,UAAA;AACd,oBAAgB,aAAa,KAAK;AAC9B,QAAA,CAAC,MAAM,kBAAkB;AAC3B,iBAAW,KAAK;AAAA,IAAA;AAAA,EAEpB;AACF;AAOA,SAAS,iBAAiB,SAAS,QAAQ,WAAW;AAChD,MAAA,aAAa,CAAC,YAAY;AACrB,WAAA,CAAC,QAAQ,CAAC,CAAC;AAAA,EAAA;AAEpB,MAAI,QAAQ;AACN,QAAA,WAAW,QAAQ,UAAU,CAAA,MAAK,OAAO,EAAE,MAAM,EAAE,MAAM,MAAS;AACtE,WAAO,QAAQ,MAAM,GAAG,WAAW,CAAC;AAAA,EAAA;AAE/B,SAAA;AACT;AAOA,SAAS,QAAQ;AACX,MAAA;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,MACE,gBAAgB;AAChB,MAAA;AAAA,IACF;AAAA,IACA,SAAS;AAAA,MACP,0BAA0B;AAC9B,MAAI,UAAU,iBAAiB,eAAe,QAAQ,SAAS;AAC/D,MAAI,aAAaX,aAAAA,QAAc,MAAM,wBAAwB,SAAS,cAAc,QAAQ,GAAG,CAAC,SAAS,cAAc,QAAQ,CAAC;AAC5G,SAAAG,6BAAAA,cAAoBC,aAAAA,UAAgB,MAAM,cAA2BD,6BAAAA,cAAoB,SAAS;AAAA,IACpH,yBAAyB;AAAA,MACvB,QAAQ;AAAA,IAAA;AAAA,EAEX,CAAA,IAAI,MAAM,WAAW,IAAI,CAAC;AAAA,IACzB;AAAA,IACA;AAAA,QACI,qBAAqB,IAAI,IAAuBA,6BAAAA,cAAc,mBAAmB,SAAS;AAAA,IAC9F;AAAA,KACC,IAAI,CAAC,IAAuBA,2CAAc,QAAQ,SAAS;AAAA,IAC5D;AAAA,EAAA,GACC,IAAI,CAAC,CAAC,CAAC;AACZ;AAWA,SAAS,kBAAkB;AAAA,EACzB;AAAA,EACA,GAAG;AACL,GAAG;AACG,MAAA;AAAA,IACF;AAAA,MACE,qBAAqB;AACzB,MAAI,UAAUH,aAAAA,QAAc,MAAM,YAAY,OAAO,QAAQ,MAAM,OAAO,QAAQ,GAAG,CAAC,OAAO,QAAQ,MAAM,OAAO,QAAQ,CAAC;AAC3H,MAAI,CAAC,SAAS;AACJ,YAAA,KAAK,qBAAqB,IAAI,yBAAyB;AACxD,WAAA;AAAA,EAAA;AAEW,SAAAG,6BAAoB,cAAA,uBAAuB,SAAS;AAAA,IACtE;AAAA,IACA;AAAA,EACF,GAAG,aAAa,CAAC;AACnB;AACA,SAAS,sBAAsB,SAAS;AAClC,MAAA;AAAA,IACF;AAAA,IACA;AAAA,MACE,gBAAgB;AACpB,MAAI,CAAC,oBAAoB,qBAAqB,IAAIX,aAAAA,SAAe,CAAA,CAAE;AACnEI,eAAAA,UAAgB,MAAM;AACpB,QAAI,cAAc;AAClB,SAAK,sBAAsB,SAAS,UAAU,YAAY,EAAE,KAAK,CAAS,UAAA;AACxE,UAAI,CAAC,aAAa;AAChB,8BAAsB,KAAK;AAAA,MAAA;AAAA,IAC7B,CACD;AACD,WAAO,MAAM;AACG,oBAAA;AAAA,IAChB;AAAA,EACC,GAAA,CAAC,SAAS,UAAU,YAAY,CAAC;AAC7B,SAAA;AACT;AACA,SAAS,sBAAsB;AAAA,EAC7B;AAAA,EACA,SAAS;AAAA,EACT,GAAG;AACL,GAAG;AACD,MAAI,WAAW,YAAY;AACvB,MAAA;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,MACE,gBAAgB;AAChB,MAAA;AAAA,IACF;AAAA,IACA;AAAA,MACE,0BAA0B;AAC1B,MAAA,oBAAoBI,aAAc,QAAA,MAAM,sBAAsB,MAAM,aAAa,SAAS,UAAU,UAAU,QAAQ,MAAM,GAAG,CAAC,MAAM,aAAa,SAAS,UAAU,UAAU,MAAM,CAAC;AACvL,MAAA,YAAYA,aAAAA,QAAc,MAAM;AAC9B,QAAA,CAAC,OAAO,gBAAgB;AACnB,aAAA,iBAAiB,MAAM,mBAAmB,QAAQ;AAAA,IAAA;AAE3D,QAAI,SAAS,SAAS,WAAW,SAAS,SAAS,SAAS,MAAM;AAGhE,aAAO,CAAC;AAAA,IAAA;AAKN,QAAA,mCAAmB,IAAI;AAC3B,QAAI,mBAAmB;AACvB,gBAAY,QAAQ,CAAK,MAAA;AACnB,UAAA;AACJ,UAAI,CAAC,SAAS,OAAO,EAAE,MAAM,EAAE,EAAE,WAAW;AAC1C;AAAA,MAAA;AAEE,UAAA,CAAC,kBAAkB,KAAK,CAAM,OAAA,GAAG,MAAM,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,MAAM,eAAe,wBAAwB,aAAa,EAAE,MAAM,EAAE,OAAO,QAAQ,0BAA0B,UAAU,sBAAsB,kBAAkB;AACnN,2BAAA;AAAA,MAAA,WACV,SAAS,OAAO,EAAE,MAAM,EAAE,EAAE,iBAAiB;AACnC,2BAAA;AAAA,MAAA,OACd;AACQ,qBAAA,IAAI,EAAE,MAAM,EAAE;AAAA,MAAA;AAAA,IAC7B,CACD;AACG,QAAA,aAAa,SAAS,GAAG;AAC3B,aAAO,CAAC;AAAA,IAAA;AAEN,QAAA,MAAM,eAAe,IAAI;AAIzB,QAAA,oBAAoB,aAAa,OAAO,GAAG;AACzC,UAAA,aAAa,IAAI,WAAW,YAAY,OAAO,CAAK,MAAA,aAAa,IAAI,EAAE,MAAM,EAAE,CAAC,EAAE,IAAI,CAAK,MAAA,EAAE,MAAM,EAAE,EAAE,KAAK,GAAG,CAAC;AAAA,IAAA;AAEtH,WAAO,CAAC,IAAI,WAAW,IAAI,MAAM;AAAA,EACnC,GAAG,CAAC,OAAO,gBAAgB,YAAY,UAAU,UAAU,mBAAmB,aAAa,MAAM,YAAY,CAAC;AAC1G,MAAA,sBAAsBA,aAAc,QAAA,MAAM,sBAAsB,MAAM,aAAa,SAAS,UAAU,UAAU,QAAQ,QAAQ,GAAG,CAAC,MAAM,aAAa,SAAS,UAAU,UAAU,MAAM,CAAC;AAC3L,MAAA,cAAcA,qBAAc,MAAM,mBAAmB,qBAAqB,QAAQ,GAAG,CAAC,qBAAqB,QAAQ,CAAC;AAIpH,MAAA,qBAAqB,sBAAsB,mBAAmB;AAC9C,SAAAG,6BAAoB,cAAAC,uBAAgB,MAAM,UAAU,IAAI,CAAqB,SAAAD,6BAAAA,cAAoB,QAAQ,SAAS;AAAA,IACpI,KAAK;AAAA,IACL,KAAK;AAAA,IACL,IAAI;AAAA,IACJ;AAAA,EAAA,GACC,SAAS,CAAC,CAAC,GAAG,YAAY,IAAI,CAAqB,SAAAA,6BAAAA,cAAoB,QAAQ,SAAS;AAAA,IACzF,KAAK;AAAA,IACL,KAAK;AAAA,IACL;AAAA,KACC,SAAS,CAAC,CAAC,GAAG,mBAAmB,IAAI,CAAC;AAAA,IACvC;AAAA,IACA;AAAA,EACF;AAAA;AAAA;AAAA,IAIAA,6BAAoB,cAAA,QAAQ,SAAS;AAAA,MACnC;AAAA,IACF,GAAG,IAAI,CAAC;AAAA,GAAC,CAAC;AACZ;AAOA,SAAS,OAAO;AACV,MAAA;AAAA,IACF;AAAA,IACA;AAAA,MACE,gBAAgB;AAChB,MAAA;AAAA,IACF;AAAA,IACA,SAAS;AAAA,IACT;AAAA,MACE,0BAA0B;AAC9B,MAAI,WAAW,YAAY;AAC3B,MAAI,WAAW,iBAAiB,eAAe,QAAQ,SAAS;AAChE,MAAI,QAAQ;AACZ,MAAI,QAAQ;AACV,YAAQ,OAAO,SAAS,SAAS,SAAS,CAAC,EAAE,MAAM,EAAE;AAAA,EAAA;AAEvD,MAAI,OAAO,CAAC;AACZ,MAAI,WAAW;AACf,MAAI,UAAU,CAAC;AACf,WAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACpC,QAAA,SAAS,SAAS,CAAC;AACnB,QAAA,UAAU,OAAO,MAAM;AACvB,QAAAsB,QAAO,WAAW,OAAO;AAC7B,QAAI,SAAS,OAAO;AAChB,QAAA,cAAc,aAAa,OAAO;AACtC,QAAI,YAAY,CAAC;AACjB,QAAI,QAAQ;AAAA,MACV,IAAI;AAAA,MACJ,MAAAA;AAAA,MACA,MAAM,CAAC;AAAA,MACP,QAAQ,OAAO;AAAA,MACf,UAAU,OAAO;AAAA,MACjB,QAAQ,OAAO,MAAM;AAAA,MACrB;AAAA,IACF;AACA,YAAQ,CAAC,IAAI;AACb,QAAI,gBAAgB,QAAQ,gBAAgB,UAAU,YAAY,MAAM;AACtE,kBAAY,OAAO,YAAY,SAAS,aAAa,YAAY,KAAK;AAAA,QACpE,MAAAA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACD,CAAA,IAAI,MAAM,QAAQ,YAAY,IAAI,IAAI,CAAC,GAAG,YAAY,IAAI,IAAI,YAAY;AAAA,eAClE,UAAU;AAIP,kBAAA,CAAC,GAAG,QAAQ;AAAA,IAAA;AAE1B,gBAAY,aAAa,CAAC;AAC1B,QAAI,CAAC,MAAM,QAAQ,SAAS,GAAG;AAC7B,YAAM,IAAI,MAAM,kBAAkB,OAAO,MAAM,OAAO,mKAA6K;AAAA,IAAA;AAErO,UAAM,OAAO;AACb,YAAQ,CAAC,IAAI;AACN,WAAA,CAAC,GAAG,SAAS;AACT,eAAA;AAAA,EAAA;AAEO,SAAAtB,2CAAoBC,aAAM,UAAU,MAAM,KAAK,OAAO,IAAI,CAAa,cAAA;AACzF,QAAI,CAAC,WAAW;AACP,aAAA;AAAA,IAAA;AAET,QAAI,aAAa,WAAW;AACtB,UAAA;AAAA,QACF;AAAA,QACA,GAAG;AAAA,MAAA,IACD;AACA,UAAA,CAAC,eAAe,OAAO,GAAG;AACpB,gBAAA,KAAK,0CAA0C,OAAO,oCAAoC;AAC3F,eAAA;AAAA,MAAA;AAET,UAAI,OAAO;AACS,aAAAD,6BAAoB,cAAA,MAAM,SAAS;AAAA,QACrD,KAAK,KAAK,UAAU,IAAI;AAAA,MAC1B,GAAG,IAAI,CAAC;AAAA,IAAA;AAEV,QAAI,WAAW,WAAW;AACJ,aAAAA,6BAAAA,cAAoB,SAAS;AAAA,QAC/C,KAAK;AAAA,MAAA,GACJ,OAAO,UAAU,KAAK,CAAC;AAAA,IAAA;AAE5B,QAAI,aAAa,WAAW;AAC1B,gBAAU,YAAV,UAAU,UAAY,UAAU;AAChC,aAAO,UAAU;AAAA,IAAA;AAEnB,QAAI,aAAa,aAAa,UAAU,WAAW,MAAM;AACvD,aAAO,OAAO,UAAU,YAAY,WAAwBA,6BAAAA,cAAoB,QAAQ;AAAA,QACtF,KAAK;AAAA,QACL,SAAS,UAAU;AAAA,MACpB,CAAA,IAAI;AAAA,IAAA;AAEP,QAAI,oBAAoB,WAAW;AAC7B,UAAA;AACF,YAAI,OAAO,KAAK,UAAU,UAAU,gBAAgB,CAAC;AACjC,eAAAA,6BAAAA,cAAoB,UAAU;AAAA,UAChD,KAAK,kBAAkB,IAAI;AAAA,UAC3B,MAAM;AAAA,UACN,yBAAyB;AAAA,YACvB,QAAQ;AAAA,UAAA;AAAA,QACV,CACD;AAAA,eACM,KAAK;AACL,eAAA;AAAA,MAAA;AAAA,IACT;AAEkB,WAAAA,6BAAoB,cAAA,QAAQ,SAAS;AAAA,MACvD,KAAK,KAAK,UAAU,SAAS;AAAA,IAC/B,GAAG,SAAS,CAAC;AAAA,EAAA,CACd,CAAC;AACJ;AACA,SAAS,eAAe,SAAS;AAC/B,SAAO,OAAO,YAAY,YAAY,gBAAgB,KAAK,OAAO;AACpE;AACA,SAAS,MAAM,OAAO;AACA,SAAAA,6BAAoB,cAAA,SAAS,KAAK;AACxD;AAMA,IAAI,aAAa;AAWjB,SAAS,QAAQ,OAAO;AAClB,MAAA;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,MACE,gBAAgB;AAChB,MAAA;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,IACR;AAAA,MACE,qBAAqB;AACrB,MAAA;AAAA,IACF,SAAS;AAAA,MACP,0BAA0B;AAC1B,MAAA,iBAAiB,kBAAkB,QAAQ,SAAS;AAIxD,MAAI,YAAY;AACd,eAAW,mBAAmB;AAAA,EAAA;AAEhC,MAAI,UAAU,iBAAiB,eAAe,MAAM,SAAS;AAC7DP,eAAAA,UAAgB,MAAM;AACP,iBAAA;AAAA,EACf,GAAG,EAAE;AACD,MAAA,+BAA+B,CAAC,KAAK,UAAU;AAC7C,QAAA;AACA,QAAA,kBAAkB,iBAAiB,OAAO;AAC5C,oBAAc,eAAe,KAAK;AAAA,IAAA,OAC7B;AACS,oBAAA;AAAA,IAAA;AAET,WAAA,GAAG,KAAK,UAAU,GAAG,CAAC,yBAAyB,WAAW,KAAK,UAAU,WAAW,CAAC,CAAC;AAAA,EAC/F;AACA,MAAI,8BAA8B,CAAC,SAAS,KAAK6B,UAAS;AACpD,QAAA;AACA,QAAA;AACe,uBAAA,KAAK,UAAUA,KAAI;AAAA,aAC7B,OAAO;AACP,aAAA,6BAA6B,KAAK,KAAK;AAAA,IAAA;AAEzC,WAAA,GAAG,KAAK,UAAU,GAAG,CAAC,qBAAqB,WAAW,cAAc,CAAC;AAAA,EAC9E;AACA,MAAI,oBAAoB,CAAC,SAAS,KAAK,UAAU;AAC3C,QAAA;AACA,QAAA,kBAAkB,iBAAiB,OAAO;AAC5C,oBAAc,eAAe,KAAK;AAAA,IAAA,OAC7B;AACS,oBAAA;AAAA,IAAA;AAEhB,WAAO,oBAAoB,KAAK,UAAU,OAAO,CAAC,KAAK,KAAK,UAAU,GAAG,CAAC,SAAS,WAAW,KAAK,UAAU,WAAW,CAAC,CAAC;AAAA,EAC5H;AACA,MAAI,mBAAmB,CAAC,SAAS,KAAKA,UAAS;AACzC,QAAA;AACA,QAAA;AACe,uBAAA,KAAK,UAAUA,KAAI;AAAA,aAC7B,OAAO;AACP,aAAA,kBAAkB,SAAS,KAAK,KAAK;AAAA,IAAA;AAE9C,WAAO,oBAAoB,KAAK,UAAU,OAAO,CAAC,KAAK,KAAK,UAAU,GAAG,CAAC,KAAK,WAAW,cAAc,CAAC;AAAA,EAC3G;AACA,MAAI,kBAAkB,CAAC;AACnB,MAAA,iBAAiBzB,aAAAA,QAAc,MAAM;AACnC,QAAA;AACJ,QAAI,eAAe,OAAO;AAAA;AAAA,MAE1B;AAAA,QAA2L;AAC3L,QAAI,gBAAgB,gBAAgB,2BAA2B,mBAAmB,IAAI,YAAY,KAAK;AAGnG,QAAA,kBAAkB,OAAO,iBAAiB,SAAY,kBAAkB,QAAQ,kBAAkB,SAAS,SAAS,cAAc;AAcrH,qBAAA,CAAC,kBAAkB,KAAK,CAAC,0CAA0C,qCAA8E,qDAA6H,4BAA4B,cAAc,6BAA6B,OAAO,eAAe,MAAM,sCAAsC,gDAAgD,sDAAsD,iGAAiG,OAAO,eAAe,WAAW,6JAA6J,UAAU,OAAO,IAAI,eAAe,MAAM,8CAA8C,iCAAiC,qCAA8E,qDAA6H,eAAe,cAAc,eAAe,OAAO,IAAI,EAAE,KAAK,IAAI,IAAI,OAAO,QAAQ,eAAe,EAAE,IAAI,CAAC,CAAC,SAAS,YAAY,MAAM;AACtvC,UAAI,cAAc,IAAI,IAAI,aAAa,WAAW;AAClD,UAAI,mBAAmB,aAAa,aAAa,IAAI,CAAO,QAAA;AACtD,YAAA,YAAY,IAAI,GAAG,GAAG;AACR,0BAAA,KAAyBG,6BAAA,cAAc,yBAAyB;AAAA,YAC9E,KAAK,GAAG,OAAO,MAAM,GAAG;AAAA,YACxB;AAAA,YACA;AAAA,YACA,SAAS;AAAA,YACT,aAAa;AAAA,YACb,eAAe;AAAA,YACf,gBAAgB;AAAA,UAAA,CACjB,CAAC;AACF,iBAAO,GAAG,KAAK,UAAU,GAAG,CAAC,qBAAqB,KAAK,UAAU,OAAO,CAAC,KAAK,KAAK,UAAU,GAAG,CAAC;AAAA,QAAA,OAC5F;AACD,cAAA,iBAAiB,aAAa,KAAK,GAAG;AACtC,cAAA,OAAO,eAAe,WAAW,aAAa;AACzC,mBAAA,6BAA6B,KAAK,eAAe,MAAM;AAAA,UAAA,OACzD;AACL,mBAAO,4BAA4B,SAAS,KAAK,eAAe,KAAK;AAAA,UAAA;AAAA,QACvE;AAAA,MACF,CACD,EAAE,KAAK,KAAK;AACb,aAAO,iDAAiD,KAAK,UAAU,OAAO,CAAC,OAAO,gBAAgB;AAAA,IACvG,CAAA,EAAE,KAAK,IAAI,KAAK,gBAAgB,SAAS,IAAI,oBAAoB,gBAAgB,MAAM,MAAM;AAC9F,QAAI,qBAAqB,CAAC,WAAW,MAAM,IAAI,gBAAgB,SAAS,SAAS,QAAQ,kBAAkB,UAAU,cAAc,UAAU,UAAU,KAAK,UAAU,SAAS,IAAI,OAAO,CAAC,MAAM,EAAE,GAAG,iBAAiB,KAAK,UAAU,KAAK,UAAU,SAAS,GAAG,CAAC,EAAE;AAAA,EACtQ,QAAQ,IAAI,CAAC,OAAO,UAAU,oBAAoB,KAAK,SAAS,KAAK,UAAU,SAAS,OAAO,MAAM,MAAM,EAAE,EAAE,MAAM,CAAC,GAAG,EAAE,KAAK,IAAI,CAAC;AAAA,EACrI;AAAA;AAAA,MAEE,4BAA4B,KAAK,UAAU,mBAAmB,UAAU,MAAM,GAAG,MAAM,CAAC,CAAC;AAAA,QAAM,EAAE;AAAA,gCACrE,QAAQ,IAAI,CAAC,OAAO,UAAU,GAAG,KAAK,UAAU,MAAM,MAAM,EAAE,CAAC,SAAS,KAAK,EAAE,EAAE,KAAK,GAAG,CAAC;AAAA;AAAA,SAEjH,KAAK,UAAU,SAAS,MAAM,MAAM,CAAC;AACtB,WAAAA,6BAAoB,cAAAC,aAAgB,UAAA,MAAyBD,6BAAAA,cAAc,UAAU,SAAS,CAAC,GAAG,OAAO;AAAA,MAC3H,0BAA0B;AAAA,MAC1B,yBAAyB,WAAW,aAAa;AAAA,MACjD,MAAM;AAAA,IAAA,CACP,CAAC,GAAgBA,6BAAAA,cAAoB,UAAU,SAAS,CAAA,GAAI,OAAO;AAAA,MAClE,0BAA0B;AAAA,MAC1B,yBAAyB,WAAW,kBAAkB;AAAA,MACtD,MAAM;AAAA,MACN,OAAO;AAAA,IACR,CAAA,CAAC,CAAC;AAAA,EAKL,GAAG,EAAE;AACL,MAAI,CAAC,YAAY,OAAO,mBAAmB,YAAY,eAAe,GAAG;AACvE,aAAS,IAAI,GAAG,IAAI,eAAe,GAAG,KAAK;AACzB,sBAAA,KAAyBA,6BAAA,cAAc,yBAAyB;AAAA,QAC9E,KAAK;AAAA,QACL,aAAa;AAAA,QACb,eAAe;AAAA,QACf,gBAAgB;AAAA,MAAA,CACjB,CAAC;AAAA,IAAA;AAAA,EACJ;AAEE,MAAA,gBAAgB,QAAQ,IAAI,CAAS,UAAA;AACvC,QAAI,QAAQ,SAAS,OAAO,MAAM,MAAM,EAAE;AAClC,YAAA,MAAM,WAAW,CAAA,GAAI,OAAO,CAAC,MAAM,MAAM,CAAC;AAAA,EAAA,CACnD,EAAE,KAAK,CAAC;AACL,MAAA,WAAW,aAAa,KAAK,SAAS,MAAM,QAAQ,OAAO,aAAa;AACrE,SAAA,aAAa,OAAoBA,2CAAoBC,aAAAA,UAAgB,MAAM,CAAC,iBAAoCD,6BAAA,cAAc,QAAQ;AAAA,IAC3I,KAAK;AAAA,IACL,MAAM,SAAS;AAAA,IACf,aAAa,MAAM;AAAA,EAAA,CACpB,IAAI,MAAmBA,2CAAoB,QAAQ;AAAA,IAClD,KAAK;AAAA,IACL,MAAM,SAAS,MAAM;AAAA,IACrB,aAAa,MAAM;AAAA,EAAA,CACpB,GAAG,OAAO,QAAQ,EAAE,IAAI,CAAA,SAA2BA,6BAAA,cAAc,QAAQ;AAAA,IACxE,KAAK;AAAA,IACL,KAAK;AAAA,IACL,MAAM;AAAA,IACN,aAAa,MAAM;AAAA,EAAA,CACpB,CAAC,GAAG,gBAAgB,eAAe;AACtC;AACA,SAAS,wBAAwB;AAAA,EAC/B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,GAAG;AACD,MAAI,OAAO,aAAa,eAAe,gBAAgB,WAAW,SAAS;AAC/D,cAAA,aAAa,YAAY,SAAS,OAAO,GAAG,2BAA2B,OAAO,aAAa,OAAO,uDAAuD;AAAA,EAAA;AAEjJ,SAAAA,6BAAAA,cAAoBmC,aAAAA,UAAgB;AAAA,IACtD;AAAA;AAAA;AAAA;AAAA,MAIA,OAAO,aAAa,eAAe,gBAAgB,WAAW,UAAU,OAAoBnC,6BAAAA,cAAoB,UAAU,SAAS,CAAA,GAAI,aAAa;AAAA,QAClJ,OAAO;AAAA,QACP,0BAA0B;AAAA,QAC1B,yBAAyB;AAAA,UACvB,QAAQ;AAAA,QAAA;AAAA,MACV,CACD,CAAC;AAAA;AAAA,EAAA,GACD,OAAO,aAAa,eAAe,gBAAgB,WAAW,UAA6BA,6BAAA,cAAc,OAAO;AAAA,IACjH,SAAS,aAAa,KAAK,OAAO;AAAA,IAClC,cAAiCA,6BAAA,cAAc,8BAA8B;AAAA,MAC3E;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IAAA,CACD;AAAA,IACD,UAAU,CAAQsB,UAAA;AAChB,0CAAwC,cAAA,UAAU,SAAS,CAAA,GAAI,aAAa;AAAA,QAC1E,OAAO;AAAA,QACP,0BAA0B;AAAA,QAC1B,yBAAyB;AAAA,UACvB,QAAQ,cAAc,SAAS,SAASA,KAAI;AAAA,QAAA;AAAA,MAC9C,CACD,CAAC;AAAA,IAAA;AAAA,EAEL,CAAA,IAAuBtB,6BAAAA,cAAc,UAAU,SAAS,CAAA,GAAI,aAAa;AAAA,IACxE,OAAO;AAAA,IACP,0BAA0B;AAAA,IAC1B,yBAAyB;AAAA,MACvB,QAAQ;AAAA,IAAA;AAAA,EAEX,CAAA,CAAC,CAAC;AACL;AACA,SAAS,6BAA6B;AAAA,EACpC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,GAAG;AACD,MAAI,QAAQ,cAAc;AAC1B,sCAAwC,cAAA,UAAU,SAAS,CAAA,GAAI,aAAa;AAAA,IAC1E,0BAA0B;AAAA,IAC1B,yBAAyB;AAAA,MACvB,QAAQ,eAAe,SAAS,SAAS,KAAK;AAAA,IAAA;AAAA,EAChD,CACD,CAAC;AACJ;AACA,SAAS,OAAO,OAAO;AACrB,SAAO,CAAC,GAAG,IAAI,IAAI,KAAK,CAAC;AAC3B;AAgBA,SAAS,gBAAgB;AACvB,SAAO,gBAAgB;AACzB;AAOA,SAAS,mBAAmB,SAAS;AACnC,SAAO,qBAAqB,OAAO;AACrC;AAOA,SAAS,gBAAgB;AACvB,SAAO,gBAAgB;AACzB;AAQA,SAAS,WAAW,OAAO,IAAI;AAC7B,SAAO,aAAa,IAAI;AAC1B;AAyHA,SAAS,aAAa,MAAM;AAC1B,SAAO,CAAS,UAAA;AACd,SAAK,QAAQ,CAAO,QAAA;AACd,UAAA,OAAO,QAAQ,YAAY;AAC7B,YAAI,KAAK;AAAA,MAAA,WACA,OAAO,MAAM;AACtB,YAAI,UAAU;AAAA,MAAA;AAAA,IAChB,CACD;AAAA,EACH;AACF;", "x_google_ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14]}