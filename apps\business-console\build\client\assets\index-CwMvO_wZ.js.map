{"version": 3, "file": "index-CwMvO_wZ.js", "sources": ["../../../node_modules/html-to-image/es/util.js", "../../../node_modules/html-to-image/es/clone-pseudos.js", "../../../node_modules/html-to-image/es/mimes.js", "../../../node_modules/html-to-image/es/dataurl.js", "../../../node_modules/html-to-image/es/clone-node.js", "../../../node_modules/html-to-image/es/embed-resources.js", "../../../node_modules/html-to-image/es/embed-images.js", "../../../node_modules/html-to-image/es/apply-style.js", "../../../node_modules/html-to-image/es/embed-webfonts.js", "../../../node_modules/html-to-image/es/index.js"], "sourcesContent": ["export function resolveUrl(url, baseUrl) {\n    // url is absolute already\n    if (url.match(/^[a-z]+:\\/\\//i)) {\n        return url;\n    }\n    // url is absolute already, without protocol\n    if (url.match(/^\\/\\//)) {\n        return window.location.protocol + url;\n    }\n    // dataURI, mailto:, tel:, etc.\n    if (url.match(/^[a-z]+:/i)) {\n        return url;\n    }\n    const doc = document.implementation.createHTMLDocument();\n    const base = doc.createElement('base');\n    const a = doc.createElement('a');\n    doc.head.appendChild(base);\n    doc.body.appendChild(a);\n    if (baseUrl) {\n        base.href = baseUrl;\n    }\n    a.href = url;\n    return a.href;\n}\nexport const uuid = (() => {\n    // generate uuid for className of pseudo elements.\n    // We should not use GUIDs, otherwise pseudo elements sometimes cannot be captured.\n    let counter = 0;\n    // ref: http://stackoverflow.com/a/6248722/2519373\n    const random = () => \n    // eslint-disable-next-line no-bitwise\n    `0000${((Math.random() * 36 ** 4) << 0).toString(36)}`.slice(-4);\n    return () => {\n        counter += 1;\n        return `u${random()}${counter}`;\n    };\n})();\nexport function delay(ms) {\n    return (args) => new Promise((resolve) => {\n        setTimeout(() => resolve(args), ms);\n    });\n}\nexport function toArray(arrayLike) {\n    const arr = [];\n    for (let i = 0, l = arrayLike.length; i < l; i++) {\n        arr.push(arrayLike[i]);\n    }\n    return arr;\n}\nlet styleProps = null;\nexport function getStyleProperties(options = {}) {\n    if (styleProps) {\n        return styleProps;\n    }\n    if (options.includeStyleProperties) {\n        styleProps = options.includeStyleProperties;\n        return styleProps;\n    }\n    styleProps = toArray(window.getComputedStyle(document.documentElement));\n    return styleProps;\n}\nfunction px(node, styleProperty) {\n    const win = node.ownerDocument.defaultView || window;\n    const val = win.getComputedStyle(node).getPropertyValue(styleProperty);\n    return val ? parseFloat(val.replace('px', '')) : 0;\n}\nfunction getNodeWidth(node) {\n    const leftBorder = px(node, 'border-left-width');\n    const rightBorder = px(node, 'border-right-width');\n    return node.clientWidth + leftBorder + rightBorder;\n}\nfunction getNodeHeight(node) {\n    const topBorder = px(node, 'border-top-width');\n    const bottomBorder = px(node, 'border-bottom-width');\n    return node.clientHeight + topBorder + bottomBorder;\n}\nexport function getImageSize(targetNode, options = {}) {\n    const width = options.width || getNodeWidth(targetNode);\n    const height = options.height || getNodeHeight(targetNode);\n    return { width, height };\n}\nexport function getPixelRatio() {\n    let ratio;\n    let FINAL_PROCESS;\n    try {\n        FINAL_PROCESS = process;\n    }\n    catch (e) {\n        // pass\n    }\n    const val = FINAL_PROCESS && FINAL_PROCESS.env\n        ? FINAL_PROCESS.env.devicePixelRatio\n        : null;\n    if (val) {\n        ratio = parseInt(val, 10);\n        if (Number.isNaN(ratio)) {\n            ratio = 1;\n        }\n    }\n    return ratio || window.devicePixelRatio || 1;\n}\n// @see https://developer.mozilla.org/en-US/docs/Web/HTML/Element/canvas#maximum_canvas_size\nconst canvasDimensionLimit = 16384;\nexport function checkCanvasDimensions(canvas) {\n    if (canvas.width > canvasDimensionLimit ||\n        canvas.height > canvasDimensionLimit) {\n        if (canvas.width > canvasDimensionLimit &&\n            canvas.height > canvasDimensionLimit) {\n            if (canvas.width > canvas.height) {\n                canvas.height *= canvasDimensionLimit / canvas.width;\n                canvas.width = canvasDimensionLimit;\n            }\n            else {\n                canvas.width *= canvasDimensionLimit / canvas.height;\n                canvas.height = canvasDimensionLimit;\n            }\n        }\n        else if (canvas.width > canvasDimensionLimit) {\n            canvas.height *= canvasDimensionLimit / canvas.width;\n            canvas.width = canvasDimensionLimit;\n        }\n        else {\n            canvas.width *= canvasDimensionLimit / canvas.height;\n            canvas.height = canvasDimensionLimit;\n        }\n    }\n}\nexport function canvasToBlob(canvas, options = {}) {\n    if (canvas.toBlob) {\n        return new Promise((resolve) => {\n            canvas.toBlob(resolve, options.type ? options.type : 'image/png', options.quality ? options.quality : 1);\n        });\n    }\n    return new Promise((resolve) => {\n        const binaryString = window.atob(canvas\n            .toDataURL(options.type ? options.type : undefined, options.quality ? options.quality : undefined)\n            .split(',')[1]);\n        const len = binaryString.length;\n        const binaryArray = new Uint8Array(len);\n        for (let i = 0; i < len; i += 1) {\n            binaryArray[i] = binaryString.charCodeAt(i);\n        }\n        resolve(new Blob([binaryArray], {\n            type: options.type ? options.type : 'image/png',\n        }));\n    });\n}\nexport function createImage(url) {\n    return new Promise((resolve, reject) => {\n        const img = new Image();\n        img.onload = () => {\n            img.decode().then(() => {\n                requestAnimationFrame(() => resolve(img));\n            });\n        };\n        img.onerror = reject;\n        img.crossOrigin = 'anonymous';\n        img.decoding = 'async';\n        img.src = url;\n    });\n}\nexport async function svgToDataURL(svg) {\n    return Promise.resolve()\n        .then(() => new XMLSerializer().serializeToString(svg))\n        .then(encodeURIComponent)\n        .then((html) => `data:image/svg+xml;charset=utf-8,${html}`);\n}\nexport async function nodeToDataURL(node, width, height) {\n    const xmlns = 'http://www.w3.org/2000/svg';\n    const svg = document.createElementNS(xmlns, 'svg');\n    const foreignObject = document.createElementNS(xmlns, 'foreignObject');\n    svg.setAttribute('width', `${width}`);\n    svg.setAttribute('height', `${height}`);\n    svg.setAttribute('viewBox', `0 0 ${width} ${height}`);\n    foreignObject.setAttribute('width', '100%');\n    foreignObject.setAttribute('height', '100%');\n    foreignObject.setAttribute('x', '0');\n    foreignObject.setAttribute('y', '0');\n    foreignObject.setAttribute('externalResourcesRequired', 'true');\n    svg.appendChild(foreignObject);\n    foreignObject.appendChild(node);\n    return svgToDataURL(svg);\n}\nexport const isInstanceOfElement = (node, instance) => {\n    if (node instanceof instance)\n        return true;\n    const nodePrototype = Object.getPrototypeOf(node);\n    if (nodePrototype === null)\n        return false;\n    return (nodePrototype.constructor.name === instance.name ||\n        isInstanceOfElement(nodePrototype, instance));\n};\n//# sourceMappingURL=util.js.map", "import { uuid, getStyleProperties } from './util';\nfunction formatCSSText(style) {\n    const content = style.getPropertyValue('content');\n    return `${style.cssText} content: '${content.replace(/'|\"/g, '')}';`;\n}\nfunction formatCSSProperties(style, options) {\n    return getStyleProperties(options)\n        .map((name) => {\n        const value = style.getPropertyValue(name);\n        const priority = style.getPropertyPriority(name);\n        return `${name}: ${value}${priority ? ' !important' : ''};`;\n    })\n        .join(' ');\n}\nfunction getPseudoElementStyle(className, pseudo, style, options) {\n    const selector = `.${className}:${pseudo}`;\n    const cssText = style.cssText\n        ? formatCSSText(style)\n        : formatCSSProperties(style, options);\n    return document.createTextNode(`${selector}{${cssText}}`);\n}\nfunction clonePseudoElement(nativeNode, clonedNode, pseudo, options) {\n    const style = window.getComputedStyle(nativeNode, pseudo);\n    const content = style.getPropertyValue('content');\n    if (content === '' || content === 'none') {\n        return;\n    }\n    const className = uuid();\n    try {\n        clonedNode.className = `${clonedNode.className} ${className}`;\n    }\n    catch (err) {\n        return;\n    }\n    const styleElement = document.createElement('style');\n    styleElement.appendChild(getPseudoElementStyle(className, pseudo, style, options));\n    clonedNode.appendChild(styleElement);\n}\nexport function clonePseudoElements(nativeNode, clonedNode, options) {\n    clonePseudoElement(nativeNode, clonedNode, ':before', options);\n    clonePseudoElement(nativeNode, clonedNode, ':after', options);\n}\n//# sourceMappingURL=clone-pseudos.js.map", "const WOFF = 'application/font-woff';\nconst JPEG = 'image/jpeg';\nconst mimes = {\n    woff: WOFF,\n    woff2: WOFF,\n    ttf: 'application/font-truetype',\n    eot: 'application/vnd.ms-fontobject',\n    png: 'image/png',\n    jpg: JPEG,\n    jpeg: JPEG,\n    gif: 'image/gif',\n    tiff: 'image/tiff',\n    svg: 'image/svg+xml',\n    webp: 'image/webp',\n};\nfunction getExtension(url) {\n    const match = /\\.([^./]*?)$/g.exec(url);\n    return match ? match[1] : '';\n}\nexport function getMimeType(url) {\n    const extension = getExtension(url).toLowerCase();\n    return mimes[extension] || '';\n}\n//# sourceMappingURL=mimes.js.map", "function getContentFromDataUrl(dataURL) {\n    return dataURL.split(/,/)[1];\n}\nexport function isDataUrl(url) {\n    return url.search(/^(data:)/) !== -1;\n}\nexport function makeDataUrl(content, mimeType) {\n    return `data:${mimeType};base64,${content}`;\n}\nexport async function fetchAsDataURL(url, init, process) {\n    const res = await fetch(url, init);\n    if (res.status === 404) {\n        throw new Error(`Resource \"${res.url}\" not found`);\n    }\n    const blob = await res.blob();\n    return new Promise((resolve, reject) => {\n        const reader = new FileReader();\n        reader.onerror = reject;\n        reader.onloadend = () => {\n            try {\n                resolve(process({ res, result: reader.result }));\n            }\n            catch (error) {\n                reject(error);\n            }\n        };\n        reader.readAsDataURL(blob);\n    });\n}\nconst cache = {};\nfunction getCacheKey(url, contentType, includeQueryParams) {\n    let key = url.replace(/\\?.*/, '');\n    if (includeQueryParams) {\n        key = url;\n    }\n    // font resource\n    if (/ttf|otf|eot|woff2?/i.test(key)) {\n        key = key.replace(/.*\\//, '');\n    }\n    return contentType ? `[${contentType}]${key}` : key;\n}\nexport async function resourceToDataURL(resourceUrl, contentType, options) {\n    const cacheKey = getCacheKey(resourceUrl, contentType, options.includeQueryParams);\n    if (cache[cacheKey] != null) {\n        return cache[cacheKey];\n    }\n    // ref: https://developer.mozilla.org/en/docs/Web/API/XMLHttpRequest/Using_XMLHttpRequest#Bypassing_the_cache\n    if (options.cacheBust) {\n        // eslint-disable-next-line no-param-reassign\n        resourceUrl += (/\\?/.test(resourceUrl) ? '&' : '?') + new Date().getTime();\n    }\n    let dataURL;\n    try {\n        const content = await fetchAsDataURL(resourceUrl, options.fetchRequestInit, ({ res, result }) => {\n            if (!contentType) {\n                // eslint-disable-next-line no-param-reassign\n                contentType = res.headers.get('Content-Type') || '';\n            }\n            return getContentFromDataUrl(result);\n        });\n        dataURL = makeDataUrl(content, contentType);\n    }\n    catch (error) {\n        dataURL = options.imagePlaceholder || '';\n        let msg = `Failed to fetch resource: ${resourceUrl}`;\n        if (error) {\n            msg = typeof error === 'string' ? error : error.message;\n        }\n        if (msg) {\n            console.warn(msg);\n        }\n    }\n    cache[cacheKey] = dataURL;\n    return dataURL;\n}\n//# sourceMappingURL=dataurl.js.map", "import { clonePseudoElements } from './clone-pseudos';\nimport { createImage, toArray, isInstanceOfElement, getStyleProperties, } from './util';\nimport { getMimeType } from './mimes';\nimport { resourceToDataURL } from './dataurl';\nasync function cloneCanvasElement(canvas) {\n    const dataURL = canvas.toDataURL();\n    if (dataURL === 'data:,') {\n        return canvas.cloneNode(false);\n    }\n    return createImage(dataURL);\n}\nasync function cloneVideoElement(video, options) {\n    if (video.currentSrc) {\n        const canvas = document.createElement('canvas');\n        const ctx = canvas.getContext('2d');\n        canvas.width = video.clientWidth;\n        canvas.height = video.clientHeight;\n        ctx === null || ctx === void 0 ? void 0 : ctx.drawImage(video, 0, 0, canvas.width, canvas.height);\n        const dataURL = canvas.toDataURL();\n        return createImage(dataURL);\n    }\n    const poster = video.poster;\n    const contentType = getMimeType(poster);\n    const dataURL = await resourceToDataURL(poster, contentType, options);\n    return createImage(dataURL);\n}\nasync function cloneIFrameElement(iframe, options) {\n    var _a;\n    try {\n        if ((_a = iframe === null || iframe === void 0 ? void 0 : iframe.contentDocument) === null || _a === void 0 ? void 0 : _a.body) {\n            return (await cloneNode(iframe.contentDocument.body, options, true));\n        }\n    }\n    catch (_b) {\n        // Failed to clone iframe\n    }\n    return iframe.cloneNode(false);\n}\nasync function cloneSingleNode(node, options) {\n    if (isInstanceOfElement(node, HTMLCanvasElement)) {\n        return cloneCanvasElement(node);\n    }\n    if (isInstanceOfElement(node, HTMLVideoElement)) {\n        return cloneVideoElement(node, options);\n    }\n    if (isInstanceOfElement(node, HTMLIFrameElement)) {\n        return cloneIFrameElement(node, options);\n    }\n    return node.cloneNode(isSVGElement(node));\n}\nconst isSlotElement = (node) => node.tagName != null && node.tagName.toUpperCase() === 'SLOT';\nconst isSVGElement = (node) => node.tagName != null && node.tagName.toUpperCase() === 'SVG';\nasync function cloneChildren(nativeNode, clonedNode, options) {\n    var _a, _b;\n    if (isSVGElement(clonedNode)) {\n        return clonedNode;\n    }\n    let children = [];\n    if (isSlotElement(nativeNode) && nativeNode.assignedNodes) {\n        children = toArray(nativeNode.assignedNodes());\n    }\n    else if (isInstanceOfElement(nativeNode, HTMLIFrameElement) &&\n        ((_a = nativeNode.contentDocument) === null || _a === void 0 ? void 0 : _a.body)) {\n        children = toArray(nativeNode.contentDocument.body.childNodes);\n    }\n    else {\n        children = toArray(((_b = nativeNode.shadowRoot) !== null && _b !== void 0 ? _b : nativeNode).childNodes);\n    }\n    if (children.length === 0 ||\n        isInstanceOfElement(nativeNode, HTMLVideoElement)) {\n        return clonedNode;\n    }\n    await children.reduce((deferred, child) => deferred\n        .then(() => cloneNode(child, options))\n        .then((clonedChild) => {\n        if (clonedChild) {\n            clonedNode.appendChild(clonedChild);\n        }\n    }), Promise.resolve());\n    return clonedNode;\n}\nfunction cloneCSSStyle(nativeNode, clonedNode, options) {\n    const targetStyle = clonedNode.style;\n    if (!targetStyle) {\n        return;\n    }\n    const sourceStyle = window.getComputedStyle(nativeNode);\n    if (sourceStyle.cssText) {\n        targetStyle.cssText = sourceStyle.cssText;\n        targetStyle.transformOrigin = sourceStyle.transformOrigin;\n    }\n    else {\n        getStyleProperties(options).forEach((name) => {\n            let value = sourceStyle.getPropertyValue(name);\n            if (name === 'font-size' && value.endsWith('px')) {\n                const reducedFont = Math.floor(parseFloat(value.substring(0, value.length - 2))) - 0.1;\n                value = `${reducedFont}px`;\n            }\n            if (isInstanceOfElement(nativeNode, HTMLIFrameElement) &&\n                name === 'display' &&\n                value === 'inline') {\n                value = 'block';\n            }\n            if (name === 'd' && clonedNode.getAttribute('d')) {\n                value = `path(${clonedNode.getAttribute('d')})`;\n            }\n            targetStyle.setProperty(name, value, sourceStyle.getPropertyPriority(name));\n        });\n    }\n}\nfunction cloneInputValue(nativeNode, clonedNode) {\n    if (isInstanceOfElement(nativeNode, HTMLTextAreaElement)) {\n        clonedNode.innerHTML = nativeNode.value;\n    }\n    if (isInstanceOfElement(nativeNode, HTMLInputElement)) {\n        clonedNode.setAttribute('value', nativeNode.value);\n    }\n}\nfunction cloneSelectValue(nativeNode, clonedNode) {\n    if (isInstanceOfElement(nativeNode, HTMLSelectElement)) {\n        const clonedSelect = clonedNode;\n        const selectedOption = Array.from(clonedSelect.children).find((child) => nativeNode.value === child.getAttribute('value'));\n        if (selectedOption) {\n            selectedOption.setAttribute('selected', '');\n        }\n    }\n}\nfunction decorate(nativeNode, clonedNode, options) {\n    if (isInstanceOfElement(clonedNode, Element)) {\n        cloneCSSStyle(nativeNode, clonedNode, options);\n        clonePseudoElements(nativeNode, clonedNode, options);\n        cloneInputValue(nativeNode, clonedNode);\n        cloneSelectValue(nativeNode, clonedNode);\n    }\n    return clonedNode;\n}\nasync function ensureSVGSymbols(clone, options) {\n    const uses = clone.querySelectorAll ? clone.querySelectorAll('use') : [];\n    if (uses.length === 0) {\n        return clone;\n    }\n    const processedDefs = {};\n    for (let i = 0; i < uses.length; i++) {\n        const use = uses[i];\n        const id = use.getAttribute('xlink:href');\n        if (id) {\n            const exist = clone.querySelector(id);\n            const definition = document.querySelector(id);\n            if (!exist && definition && !processedDefs[id]) {\n                // eslint-disable-next-line no-await-in-loop\n                processedDefs[id] = (await cloneNode(definition, options, true));\n            }\n        }\n    }\n    const nodes = Object.values(processedDefs);\n    if (nodes.length) {\n        const ns = 'http://www.w3.org/1999/xhtml';\n        const svg = document.createElementNS(ns, 'svg');\n        svg.setAttribute('xmlns', ns);\n        svg.style.position = 'absolute';\n        svg.style.width = '0';\n        svg.style.height = '0';\n        svg.style.overflow = 'hidden';\n        svg.style.display = 'none';\n        const defs = document.createElementNS(ns, 'defs');\n        svg.appendChild(defs);\n        for (let i = 0; i < nodes.length; i++) {\n            defs.appendChild(nodes[i]);\n        }\n        clone.appendChild(svg);\n    }\n    return clone;\n}\nexport async function cloneNode(node, options, isRoot) {\n    if (!isRoot && options.filter && !options.filter(node)) {\n        return null;\n    }\n    return Promise.resolve(node)\n        .then((clonedNode) => cloneSingleNode(clonedNode, options))\n        .then((clonedNode) => cloneChildren(node, clonedNode, options))\n        .then((clonedNode) => decorate(node, clonedNode, options))\n        .then((clonedNode) => ensureSVGSymbols(clonedNode, options));\n}\n//# sourceMappingURL=clone-node.js.map", "import { resolveUrl } from './util';\nimport { getMimeType } from './mimes';\nimport { isDataUrl, makeDataUrl, resourceToDataURL } from './dataurl';\nconst URL_REGEX = /url\\((['\"]?)([^'\"]+?)\\1\\)/g;\nconst URL_WITH_FORMAT_REGEX = /url\\([^)]+\\)\\s*format\\(([\"']?)([^\"']+)\\1\\)/g;\nconst FONT_SRC_REGEX = /src:\\s*(?:url\\([^)]+\\)\\s*format\\([^)]+\\)[,;]\\s*)+/g;\nfunction toRegex(url) {\n    // eslint-disable-next-line no-useless-escape\n    const escaped = url.replace(/([.*+?^${}()|\\[\\]\\/\\\\])/g, '\\\\$1');\n    return new RegExp(`(url\\\\(['\"]?)(${escaped})(['\"]?\\\\))`, 'g');\n}\nexport function parseURLs(cssText) {\n    const urls = [];\n    cssText.replace(URL_REGEX, (raw, quotation, url) => {\n        urls.push(url);\n        return raw;\n    });\n    return urls.filter((url) => !isDataUrl(url));\n}\nexport async function embed(cssText, resourceURL, baseURL, options, getContentFromUrl) {\n    try {\n        const resolvedURL = baseURL ? resolveUrl(resourceURL, baseURL) : resourceURL;\n        const contentType = getMimeType(resourceURL);\n        let dataURL;\n        if (getContentFromUrl) {\n            const content = await getContentFromUrl(resolvedURL);\n            dataURL = makeDataUrl(content, contentType);\n        }\n        else {\n            dataURL = await resourceToDataURL(resolvedURL, contentType, options);\n        }\n        return cssText.replace(toRegex(resourceURL), `$1${dataURL}$3`);\n    }\n    catch (error) {\n        // pass\n    }\n    return cssText;\n}\nfunction filterPreferredFontFormat(str, { preferredFontFormat }) {\n    return !preferredFontFormat\n        ? str\n        : str.replace(FONT_SRC_REGEX, (match) => {\n            // eslint-disable-next-line no-constant-condition\n            while (true) {\n                const [src, , format] = URL_WITH_FORMAT_REGEX.exec(match) || [];\n                if (!format) {\n                    return '';\n                }\n                if (format === preferredFontFormat) {\n                    return `src: ${src};`;\n                }\n            }\n        });\n}\nexport function shouldEmbed(url) {\n    return url.search(URL_REGEX) !== -1;\n}\nexport async function embedResources(cssText, baseUrl, options) {\n    if (!shouldEmbed(cssText)) {\n        return cssText;\n    }\n    const filteredCSSText = filterPreferredFontFormat(cssText, options);\n    const urls = parseURLs(filteredCSSText);\n    return urls.reduce((deferred, url) => deferred.then((css) => embed(css, url, baseUrl, options)), Promise.resolve(filteredCSSText));\n}\n//# sourceMappingURL=embed-resources.js.map", "import { embedResources } from './embed-resources';\nimport { toArray, isInstanceOfElement } from './util';\nimport { isDataUrl, resourceToDataURL } from './dataurl';\nimport { getMimeType } from './mimes';\nasync function embedProp(propName, node, options) {\n    var _a;\n    const propValue = (_a = node.style) === null || _a === void 0 ? void 0 : _a.getPropertyValue(propName);\n    if (propValue) {\n        const cssString = await embedResources(propValue, null, options);\n        node.style.setProperty(propName, cssString, node.style.getPropertyPriority(propName));\n        return true;\n    }\n    return false;\n}\nasync function embedBackground(clonedNode, options) {\n    ;\n    (await embedProp('background', clonedNode, options)) ||\n        (await embedProp('background-image', clonedNode, options));\n    (await embed<PERSON>rop('mask', clonedNode, options)) ||\n        (await embedProp('-webkit-mask', clonedNode, options)) ||\n        (await embedProp('mask-image', clonedNode, options)) ||\n        (await embedProp('-webkit-mask-image', clonedNode, options));\n}\nasync function embedImageNode(clonedNode, options) {\n    const isImageElement = isInstanceOfElement(clonedNode, HTMLImageElement);\n    if (!(isImageElement && !isDataUrl(clonedNode.src)) &&\n        !(isInstanceOfElement(clonedNode, SVGImageElement) &&\n            !isDataUrl(clonedNode.href.baseVal))) {\n        return;\n    }\n    const url = isImageElement ? clonedNode.src : clonedNode.href.baseVal;\n    const dataURL = await resourceToDataURL(url, getMimeType(url), options);\n    await new Promise((resolve, reject) => {\n        clonedNode.onload = resolve;\n        clonedNode.onerror = options.onImageErrorHandler\n            ? (...attributes) => {\n                try {\n                    resolve(options.onImageErrorHandler(...attributes));\n                }\n                catch (error) {\n                    reject(error);\n                }\n            }\n            : reject;\n        const image = clonedNode;\n        if (image.decode) {\n            image.decode = resolve;\n        }\n        if (image.loading === 'lazy') {\n            image.loading = 'eager';\n        }\n        if (isImageElement) {\n            clonedNode.srcset = '';\n            clonedNode.src = dataURL;\n        }\n        else {\n            clonedNode.href.baseVal = dataURL;\n        }\n    });\n}\nasync function embedChildren(clonedNode, options) {\n    const children = toArray(clonedNode.childNodes);\n    const deferreds = children.map((child) => embedImages(child, options));\n    await Promise.all(deferreds).then(() => clonedNode);\n}\nexport async function embedImages(clonedNode, options) {\n    if (isInstanceOfElement(clonedNode, Element)) {\n        await embedBackground(clonedNode, options);\n        await embedImageNode(clonedNode, options);\n        await embedChildren(clonedNode, options);\n    }\n}\n//# sourceMappingURL=embed-images.js.map", "export function applyStyle(node, options) {\n    const { style } = node;\n    if (options.backgroundColor) {\n        style.backgroundColor = options.backgroundColor;\n    }\n    if (options.width) {\n        style.width = `${options.width}px`;\n    }\n    if (options.height) {\n        style.height = `${options.height}px`;\n    }\n    const manual = options.style;\n    if (manual != null) {\n        Object.keys(manual).forEach((key) => {\n            style[key] = manual[key];\n        });\n    }\n    return node;\n}\n//# sourceMappingURL=apply-style.js.map", "import { toArray } from './util';\nimport { fetchAsDataURL } from './dataurl';\nimport { shouldEmbed, embedResources } from './embed-resources';\nconst cssFetchCache = {};\nasync function fetchCSS(url) {\n    let cache = cssFetchCache[url];\n    if (cache != null) {\n        return cache;\n    }\n    const res = await fetch(url);\n    const cssText = await res.text();\n    cache = { url, cssText };\n    cssFetchCache[url] = cache;\n    return cache;\n}\nasync function embedFonts(data, options) {\n    let cssText = data.cssText;\n    const regexUrl = /url\\([\"']?([^\"')]+)[\"']?\\)/g;\n    const fontLocs = cssText.match(/url\\([^)]+\\)/g) || [];\n    const loadFonts = fontLocs.map(async (loc) => {\n        let url = loc.replace(regexUrl, '$1');\n        if (!url.startsWith('https://')) {\n            url = new URL(url, data.url).href;\n        }\n        return fetchAsDataURL(url, options.fetchRequestInit, ({ result }) => {\n            cssText = cssText.replace(loc, `url(${result})`);\n            return [loc, result];\n        });\n    });\n    return Promise.all(loadFonts).then(() => cssText);\n}\nfunction parseCSS(source) {\n    if (source == null) {\n        return [];\n    }\n    const result = [];\n    const commentsRegex = /(\\/\\*[\\s\\S]*?\\*\\/)/gi;\n    // strip out comments\n    let cssText = source.replace(commentsRegex, '');\n    // eslint-disable-next-line prefer-regex-literals\n    const keyframesRegex = new RegExp('((@.*?keyframes [\\\\s\\\\S]*?){([\\\\s\\\\S]*?}\\\\s*?)})', 'gi');\n    // eslint-disable-next-line no-constant-condition\n    while (true) {\n        const matches = keyframesRegex.exec(cssText);\n        if (matches === null) {\n            break;\n        }\n        result.push(matches[0]);\n    }\n    cssText = cssText.replace(keyframesRegex, '');\n    const importRegex = /@import[\\s\\S]*?url\\([^)]*\\)[\\s\\S]*?;/gi;\n    // to match css & media queries together\n    const combinedCSSRegex = '((\\\\s*?(?:\\\\/\\\\*[\\\\s\\\\S]*?\\\\*\\\\/)?\\\\s*?@media[\\\\s\\\\S]' +\n        '*?){([\\\\s\\\\S]*?)}\\\\s*?})|(([\\\\s\\\\S]*?){([\\\\s\\\\S]*?)})';\n    // unified regex\n    const unifiedRegex = new RegExp(combinedCSSRegex, 'gi');\n    // eslint-disable-next-line no-constant-condition\n    while (true) {\n        let matches = importRegex.exec(cssText);\n        if (matches === null) {\n            matches = unifiedRegex.exec(cssText);\n            if (matches === null) {\n                break;\n            }\n            else {\n                importRegex.lastIndex = unifiedRegex.lastIndex;\n            }\n        }\n        else {\n            unifiedRegex.lastIndex = importRegex.lastIndex;\n        }\n        result.push(matches[0]);\n    }\n    return result;\n}\nasync function getCSSRules(styleSheets, options) {\n    const ret = [];\n    const deferreds = [];\n    // First loop inlines imports\n    styleSheets.forEach((sheet) => {\n        if ('cssRules' in sheet) {\n            try {\n                toArray(sheet.cssRules || []).forEach((item, index) => {\n                    if (item.type === CSSRule.IMPORT_RULE) {\n                        let importIndex = index + 1;\n                        const url = item.href;\n                        const deferred = fetchCSS(url)\n                            .then((metadata) => embedFonts(metadata, options))\n                            .then((cssText) => parseCSS(cssText).forEach((rule) => {\n                            try {\n                                sheet.insertRule(rule, rule.startsWith('@import')\n                                    ? (importIndex += 1)\n                                    : sheet.cssRules.length);\n                            }\n                            catch (error) {\n                                console.error('Error inserting rule from remote css', {\n                                    rule,\n                                    error,\n                                });\n                            }\n                        }))\n                            .catch((e) => {\n                            console.error('Error loading remote css', e.toString());\n                        });\n                        deferreds.push(deferred);\n                    }\n                });\n            }\n            catch (e) {\n                const inline = styleSheets.find((a) => a.href == null) || document.styleSheets[0];\n                if (sheet.href != null) {\n                    deferreds.push(fetchCSS(sheet.href)\n                        .then((metadata) => embedFonts(metadata, options))\n                        .then((cssText) => parseCSS(cssText).forEach((rule) => {\n                        inline.insertRule(rule, inline.cssRules.length);\n                    }))\n                        .catch((err) => {\n                        console.error('Error loading remote stylesheet', err);\n                    }));\n                }\n                console.error('Error inlining remote css file', e);\n            }\n        }\n    });\n    return Promise.all(deferreds).then(() => {\n        // Second loop parses rules\n        styleSheets.forEach((sheet) => {\n            if ('cssRules' in sheet) {\n                try {\n                    toArray(sheet.cssRules || []).forEach((item) => {\n                        ret.push(item);\n                    });\n                }\n                catch (e) {\n                    console.error(`Error while reading CSS rules from ${sheet.href}`, e);\n                }\n            }\n        });\n        return ret;\n    });\n}\nfunction getWebFontRules(cssRules) {\n    return cssRules\n        .filter((rule) => rule.type === CSSRule.FONT_FACE_RULE)\n        .filter((rule) => shouldEmbed(rule.style.getPropertyValue('src')));\n}\nasync function parseWebFontRules(node, options) {\n    if (node.ownerDocument == null) {\n        throw new Error('Provided element is not within a Document');\n    }\n    const styleSheets = toArray(node.ownerDocument.styleSheets);\n    const cssRules = await getCSSRules(styleSheets, options);\n    return getWebFontRules(cssRules);\n}\nfunction normalizeFontFamily(font) {\n    return font.trim().replace(/[\"']/g, '');\n}\nfunction getUsedFonts(node) {\n    const fonts = new Set();\n    function traverse(node) {\n        const fontFamily = node.style.fontFamily || getComputedStyle(node).fontFamily;\n        fontFamily.split(',').forEach((font) => {\n            fonts.add(normalizeFontFamily(font));\n        });\n        Array.from(node.children).forEach((child) => {\n            if (child instanceof HTMLElement) {\n                traverse(child);\n            }\n        });\n    }\n    traverse(node);\n    return fonts;\n}\nexport async function getWebFontCSS(node, options) {\n    const rules = await parseWebFontRules(node, options);\n    const usedFonts = getUsedFonts(node);\n    const cssTexts = await Promise.all(rules\n        .filter((rule) => usedFonts.has(normalizeFontFamily(rule.style.fontFamily)))\n        .map((rule) => {\n        const baseUrl = rule.parentStyleSheet\n            ? rule.parentStyleSheet.href\n            : null;\n        return embedResources(rule.cssText, baseUrl, options);\n    }));\n    return cssTexts.join('\\n');\n}\nexport async function embedWebFonts(clonedNode, options) {\n    const cssText = options.fontEmbedCSS != null\n        ? options.fontEmbedCSS\n        : options.skipFonts\n            ? null\n            : await getWebFontCSS(clonedNode, options);\n    if (cssText) {\n        const styleNode = document.createElement('style');\n        const sytleContent = document.createTextNode(cssText);\n        styleNode.appendChild(sytleContent);\n        if (clonedNode.firstChild) {\n            clonedNode.insertBefore(styleNode, clonedNode.firstChild);\n        }\n        else {\n            clonedNode.appendChild(styleNode);\n        }\n    }\n}\n//# sourceMappingURL=embed-webfonts.js.map", "import { cloneNode } from './clone-node';\nimport { embedImages } from './embed-images';\nimport { applyStyle } from './apply-style';\nimport { embedWebFonts, getWebFontCSS } from './embed-webfonts';\nimport { getImageSize, getPixelRatio, createImage, canvasToBlob, nodeToDataURL, checkCanvasDimensions, } from './util';\nexport async function toSvg(node, options = {}) {\n    const { width, height } = getImageSize(node, options);\n    const clonedNode = (await cloneNode(node, options, true));\n    await embedWebFonts(clonedNode, options);\n    await embedImages(clonedNode, options);\n    applyStyle(clonedNode, options);\n    const datauri = await nodeToDataURL(clonedNode, width, height);\n    return datauri;\n}\nexport async function toCanvas(node, options = {}) {\n    const { width, height } = getImageSize(node, options);\n    const svg = await toSvg(node, options);\n    const img = await createImage(svg);\n    const canvas = document.createElement('canvas');\n    const context = canvas.getContext('2d');\n    const ratio = options.pixelRatio || getPixelRatio();\n    const canvasWidth = options.canvasWidth || width;\n    const canvasHeight = options.canvasHeight || height;\n    canvas.width = canvasWidth * ratio;\n    canvas.height = canvasHeight * ratio;\n    if (!options.skipAutoScale) {\n        checkCanvasDimensions(canvas);\n    }\n    canvas.style.width = `${canvasWidth}`;\n    canvas.style.height = `${canvasHeight}`;\n    if (options.backgroundColor) {\n        context.fillStyle = options.backgroundColor;\n        context.fillRect(0, 0, canvas.width, canvas.height);\n    }\n    context.drawImage(img, 0, 0, canvas.width, canvas.height);\n    return canvas;\n}\nexport async function toPixelData(node, options = {}) {\n    const { width, height } = getImageSize(node, options);\n    const canvas = await toCanvas(node, options);\n    const ctx = canvas.getContext('2d');\n    return ctx.getImageData(0, 0, width, height).data;\n}\nexport async function toPng(node, options = {}) {\n    const canvas = await toCanvas(node, options);\n    return canvas.toDataURL();\n}\nexport async function toJpeg(node, options = {}) {\n    const canvas = await toCanvas(node, options);\n    return canvas.toDataURL('image/jpeg', options.quality || 1);\n}\nexport async function toBlob(node, options = {}) {\n    const canvas = await toCanvas(node, options);\n    const blob = await canvasToBlob(canvas);\n    return blob;\n}\nexport async function getFontEmbedCSS(node, options = {}) {\n    return getWebFontCSS(node, options);\n}\n//# sourceMappingURL=index.js.map"], "names": ["process", "dataURL", "cache", "node"], "mappings": "AAAO,SAAS,WAAW,KAAK,SAAS;AAErC,MAAI,IAAI,MAAM,eAAe,GAAG;AAC5B,WAAO;AAAA,EACf;AAEI,MAAI,IAAI,MAAM,OAAO,GAAG;AACpB,WAAO,OAAO,SAAS,WAAW;AAAA,EAC1C;AAEI,MAAI,IAAI,MAAM,WAAW,GAAG;AACxB,WAAO;AAAA,EACf;AACI,QAAM,MAAM,SAAS,eAAe,mBAAoB;AACxD,QAAM,OAAO,IAAI,cAAc,MAAM;AACrC,QAAM,IAAI,IAAI,cAAc,GAAG;AAC/B,MAAI,KAAK,YAAY,IAAI;AACzB,MAAI,KAAK,YAAY,CAAC;AACtB,MAAI,SAAS;AACT,SAAK,OAAO;AAAA,EACpB;AACI,IAAE,OAAO;AACT,SAAO,EAAE;AACb;AACO,MAAM,OAAQ,uBAAM;AAGvB,MAAI,UAAU;AAEd,QAAM,SAAS;AAAA;AAAA,IAEf,QAAS,KAAK,OAAM,IAAK,MAAM,KAAM,GAAG,SAAS,EAAE,CAAC,GAAG,MAAM,EAAE;AAAA;AAC/D,SAAO,MAAM;AACT,eAAW;AACX,WAAO,IAAI,QAAQ,GAAG,OAAO;AAAA,EAChC;AACL,GAAI;AAMG,SAAS,QAAQ,WAAW;AAC/B,QAAM,MAAM,CAAE;AACd,WAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG,KAAK;AAC9C,QAAI,KAAK,UAAU,CAAC,CAAC;AAAA,EAC7B;AACI,SAAO;AACX;AACA,IAAI,aAAa;AACV,SAAS,mBAAmB,UAAU,IAAI;AAC7C,MAAI,YAAY;AACZ,WAAO;AAAA,EACf;AACI,MAAI,QAAQ,wBAAwB;AAChC,iBAAa,QAAQ;AACrB,WAAO;AAAA,EACf;AACI,eAAa,QAAQ,OAAO,iBAAiB,SAAS,eAAe,CAAC;AACtE,SAAO;AACX;AACA,SAAS,GAAG,MAAM,eAAe;AAC7B,QAAM,MAAM,KAAK,cAAc,eAAe;AAC9C,QAAM,MAAM,IAAI,iBAAiB,IAAI,EAAE,iBAAiB,aAAa;AACrE,SAAO,MAAM,WAAW,IAAI,QAAQ,MAAM,EAAE,CAAC,IAAI;AACrD;AACA,SAAS,aAAa,MAAM;AACxB,QAAM,aAAa,GAAG,MAAM,mBAAmB;AAC/C,QAAM,cAAc,GAAG,MAAM,oBAAoB;AACjD,SAAO,KAAK,cAAc,aAAa;AAC3C;AACA,SAAS,cAAc,MAAM;AACzB,QAAM,YAAY,GAAG,MAAM,kBAAkB;AAC7C,QAAM,eAAe,GAAG,MAAM,qBAAqB;AACnD,SAAO,KAAK,eAAe,YAAY;AAC3C;AACO,SAAS,aAAa,YAAY,UAAU,IAAI;AACnD,QAAM,QAAQ,QAAQ,SAAS,aAAa,UAAU;AACtD,QAAM,SAAS,QAAQ,UAAU,cAAc,UAAU;AACzD,SAAO,EAAE,OAAO,OAAQ;AAC5B;AACO,SAAS,gBAAgB;AAC5B,MAAI;AACJ,MAAI;AACJ,MAAI;AACA,oBAAgB;AAAA,EACxB,SACW,GAAG;AAAA,EAEd;AACI,QAAM,MAAM,iBAAiB,cAAc,MACrC,cAAc,IAAI,mBAClB;AACN,MAAI,KAAK;AACL,YAAQ,SAAS,KAAK,EAAE;AACxB,QAAI,OAAO,MAAM,KAAK,GAAG;AACrB,cAAQ;AAAA,IACpB;AAAA,EACA;AACI,SAAO,SAAS,OAAO,oBAAoB;AAC/C;AAEA,MAAM,uBAAuB;AACtB,SAAS,sBAAsB,QAAQ;AAC1C,MAAI,OAAO,QAAQ,wBACf,OAAO,SAAS,sBAAsB;AACtC,QAAI,OAAO,QAAQ,wBACf,OAAO,SAAS,sBAAsB;AACtC,UAAI,OAAO,QAAQ,OAAO,QAAQ;AAC9B,eAAO,UAAU,uBAAuB,OAAO;AAC/C,eAAO,QAAQ;AAAA,MAC/B,OACiB;AACD,eAAO,SAAS,uBAAuB,OAAO;AAC9C,eAAO,SAAS;AAAA,MAChC;AAAA,IACA,WACiB,OAAO,QAAQ,sBAAsB;AAC1C,aAAO,UAAU,uBAAuB,OAAO;AAC/C,aAAO,QAAQ;AAAA,IAC3B,OACa;AACD,aAAO,SAAS,uBAAuB,OAAO;AAC9C,aAAO,SAAS;AAAA,IAC5B;AAAA,EACA;AACA;AAqBO,SAAS,YAAY,KAAK;AAC7B,SAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACpC,UAAM,MAAM,IAAI,MAAO;AACvB,QAAI,SAAS,MAAM;AACf,UAAI,SAAS,KAAK,MAAM;AACpB,8BAAsB,MAAM,QAAQ,GAAG,CAAC;AAAA,MACxD,CAAa;AAAA,IACJ;AACD,QAAI,UAAU;AACd,QAAI,cAAc;AAClB,QAAI,WAAW;AACf,QAAI,MAAM;AAAA,EAClB,CAAK;AACL;AACO,eAAe,aAAa,KAAK;AACpC,SAAO,QAAQ,QAAO,EACjB,KAAK,MAAM,IAAI,gBAAgB,kBAAkB,GAAG,CAAC,EACrD,KAAK,kBAAkB,EACvB,KAAK,CAAC,SAAS,oCAAoC,IAAI,EAAE;AAClE;AACO,eAAe,cAAc,MAAM,OAAO,QAAQ;AACrD,QAAM,QAAQ;AACd,QAAM,MAAM,SAAS,gBAAgB,OAAO,KAAK;AACjD,QAAM,gBAAgB,SAAS,gBAAgB,OAAO,eAAe;AACrE,MAAI,aAAa,SAAS,GAAG,KAAK,EAAE;AACpC,MAAI,aAAa,UAAU,GAAG,MAAM,EAAE;AACtC,MAAI,aAAa,WAAW,OAAO,KAAK,IAAI,MAAM,EAAE;AACpD,gBAAc,aAAa,SAAS,MAAM;AAC1C,gBAAc,aAAa,UAAU,MAAM;AAC3C,gBAAc,aAAa,KAAK,GAAG;AACnC,gBAAc,aAAa,KAAK,GAAG;AACnC,gBAAc,aAAa,6BAA6B,MAAM;AAC9D,MAAI,YAAY,aAAa;AAC7B,gBAAc,YAAY,IAAI;AAC9B,SAAO,aAAa,GAAG;AAC3B;AACO,MAAM,sBAAsB,CAAC,MAAM,aAAa;AACnD,MAAI,gBAAgB;AAChB,WAAO;AACX,QAAM,gBAAgB,OAAO,eAAe,IAAI;AAChD,MAAI,kBAAkB;AAClB,WAAO;AACX,SAAQ,cAAc,YAAY,SAAS,SAAS,QAChD,oBAAoB,eAAe,QAAQ;AACnD;AC9LA,SAAS,cAAc,OAAO;AAC1B,QAAM,UAAU,MAAM,iBAAiB,SAAS;AAChD,SAAO,GAAG,MAAM,OAAO,cAAc,QAAQ,QAAQ,QAAQ,EAAE,CAAC;AACpE;AACA,SAAS,oBAAoB,OAAO,SAAS;AACzC,SAAO,mBAAmB,OAAO,EAC5B,IAAI,CAAC,SAAS;AACf,UAAM,QAAQ,MAAM,iBAAiB,IAAI;AACzC,UAAM,WAAW,MAAM,oBAAoB,IAAI;AAC/C,WAAO,GAAG,IAAI,KAAK,KAAK,GAAG,WAAW,gBAAgB,EAAE;AAAA,EAC3D,CAAA,EACI,KAAK,GAAG;AACjB;AACA,SAAS,sBAAsB,WAAW,QAAQ,OAAO,SAAS;AAC9D,QAAM,WAAW,IAAI,SAAS,IAAI,MAAM;AACxC,QAAM,UAAU,MAAM,UAChB,cAAc,KAAK,IACnB,oBAAoB,OAAO,OAAO;AACxC,SAAO,SAAS,eAAe,GAAG,QAAQ,IAAI,OAAO,GAAG;AAC5D;AACA,SAAS,mBAAmB,YAAY,YAAY,QAAQ,SAAS;AACjE,QAAM,QAAQ,OAAO,iBAAiB,YAAY,MAAM;AACxD,QAAM,UAAU,MAAM,iBAAiB,SAAS;AAChD,MAAI,YAAY,MAAM,YAAY,QAAQ;AACtC;AAAA,EACR;AACI,QAAM,YAAY,KAAM;AACxB,MAAI;AACA,eAAW,YAAY,GAAG,WAAW,SAAS,IAAI,SAAS;AAAA,EACnE,SACW,KAAK;AACR;AAAA,EACR;AACI,QAAM,eAAe,SAAS,cAAc,OAAO;AACnD,eAAa,YAAY,sBAAsB,WAAW,QAAQ,OAAO,OAAO,CAAC;AACjF,aAAW,YAAY,YAAY;AACvC;AACO,SAAS,oBAAoB,YAAY,YAAY,SAAS;AACjE,qBAAmB,YAAY,YAAY,WAAW,OAAO;AAC7D,qBAAmB,YAAY,YAAY,UAAU,OAAO;AAChE;ACzCA,MAAM,OAAO;AACb,MAAM,OAAO;AACb,MAAM,QAAQ;AAAA,EACV,MAAM;AAAA,EACN,OAAO;AAAA,EACP,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,MAAM;AAAA,EACN,KAAK;AAAA,EACL,MAAM;AAAA,EACN,KAAK;AAAA,EACL,MAAM;AACV;AACA,SAAS,aAAa,KAAK;AACvB,QAAM,QAAQ,gBAAgB,KAAK,GAAG;AACtC,SAAO,QAAQ,MAAM,CAAC,IAAI;AAC9B;AACO,SAAS,YAAY,KAAK;AAC7B,QAAM,YAAY,aAAa,GAAG,EAAE,YAAa;AACjD,SAAO,MAAM,SAAS,KAAK;AAC/B;ACtBA,SAAS,sBAAsB,SAAS;AACpC,SAAO,QAAQ,MAAM,GAAG,EAAE,CAAC;AAC/B;AACO,SAAS,UAAU,KAAK;AAC3B,SAAO,IAAI,OAAO,UAAU,MAAM;AACtC;AACO,SAAS,YAAY,SAAS,UAAU;AAC3C,SAAO,QAAQ,QAAQ,WAAW,OAAO;AAC7C;AACO,eAAe,eAAe,KAAK,MAAMA,UAAS;AACrD,QAAM,MAAM,MAAM,MAAM,KAAK,IAAI;AACjC,MAAI,IAAI,WAAW,KAAK;AACpB,UAAM,IAAI,MAAM,aAAa,IAAI,GAAG,aAAa;AAAA,EACzD;AACI,QAAM,OAAO,MAAM,IAAI,KAAM;AAC7B,SAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACpC,UAAM,SAAS,IAAI,WAAY;AAC/B,WAAO,UAAU;AACjB,WAAO,YAAY,MAAM;AACrB,UAAI;AACA,gBAAQA,SAAQ,EAAE,KAAK,QAAQ,OAAO,OAAM,CAAE,CAAC;AAAA,MAC/D,SACmB,OAAO;AACV,eAAO,KAAK;AAAA,MAC5B;AAAA,IACS;AACD,WAAO,cAAc,IAAI;AAAA,EACjC,CAAK;AACL;AACA,MAAM,QAAQ,CAAE;AAChB,SAAS,YAAY,KAAK,aAAa,oBAAoB;AACvD,MAAI,MAAM,IAAI,QAAQ,QAAQ,EAAE;AAChC,MAAI,oBAAoB;AACpB,UAAM;AAAA,EACd;AAEI,MAAI,sBAAsB,KAAK,GAAG,GAAG;AACjC,UAAM,IAAI,QAAQ,QAAQ,EAAE;AAAA,EACpC;AACI,SAAO,cAAc,IAAI,WAAW,IAAI,GAAG,KAAK;AACpD;AACO,eAAe,kBAAkB,aAAa,aAAa,SAAS;AACvE,QAAM,WAAW,YAAY,aAAa,aAAa,QAAQ,kBAAkB;AACjF,MAAI,MAAM,QAAQ,KAAK,MAAM;AACzB,WAAO,MAAM,QAAQ;AAAA,EAC7B;AAEI,MAAI,QAAQ,WAAW;AAEnB,oBAAgB,KAAK,KAAK,WAAW,IAAI,MAAM,QAAO,oBAAI,KAAM,GAAC,QAAS;AAAA,EAClF;AACI,MAAI;AACJ,MAAI;AACA,UAAM,UAAU,MAAM,eAAe,aAAa,QAAQ,kBAAkB,CAAC,EAAE,KAAK,aAAa;AAC7F,UAAI,CAAC,aAAa;AAEd,sBAAc,IAAI,QAAQ,IAAI,cAAc,KAAK;AAAA,MACjE;AACY,aAAO,sBAAsB,MAAM;AAAA,IAC/C,CAAS;AACD,cAAU,YAAY,SAAS,WAAW;AAAA,EAClD,SACW,OAAO;AACV,cAAU,QAAQ,oBAAoB;AACtC,QAAI,MAAM,6BAA6B,WAAW;AAClD,QAAI,OAAO;AACP,YAAM,OAAO,UAAU,WAAW,QAAQ,MAAM;AAAA,IAC5D;AACQ,QAAI,KAAK;AACL,cAAQ,KAAK,GAAG;AAAA,IAC5B;AAAA,EACA;AACI,QAAM,QAAQ,IAAI;AAClB,SAAO;AACX;ACtEA,eAAe,mBAAmB,QAAQ;AACtC,QAAM,UAAU,OAAO,UAAW;AAClC,MAAI,YAAY,UAAU;AACtB,WAAO,OAAO,UAAU,KAAK;AAAA,EACrC;AACI,SAAO,YAAY,OAAO;AAC9B;AACA,eAAe,kBAAkB,OAAO,SAAS;AAC7C,MAAI,MAAM,YAAY;AAClB,UAAM,SAAS,SAAS,cAAc,QAAQ;AAC9C,UAAM,MAAM,OAAO,WAAW,IAAI;AAClC,WAAO,QAAQ,MAAM;AACrB,WAAO,SAAS,MAAM;AACtB,YAAQ,QAAQ,QAAQ,SAAS,SAAS,IAAI,UAAU,OAAO,GAAG,GAAG,OAAO,OAAO,OAAO,MAAM;AAChG,UAAMC,WAAU,OAAO,UAAW;AAClC,WAAO,YAAYA,QAAO;AAAA,EAClC;AACI,QAAM,SAAS,MAAM;AACrB,QAAM,cAAc,YAAY,MAAM;AACtC,QAAM,UAAU,MAAM,kBAAkB,QAAQ,aAAa,OAAO;AACpE,SAAO,YAAY,OAAO;AAC9B;AACA,eAAe,mBAAmB,QAAQ,SAAS;AAC/C,MAAI;AACJ,MAAI;AACA,SAAK,KAAK,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO,qBAAqB,QAAQ,OAAO,SAAS,SAAS,GAAG,MAAM;AAC5H,aAAQ,MAAM,UAAU,OAAO,gBAAgB,MAAM,SAAS,IAAI;AAAA,IAC9E;AAAA,EACA,SACW,IAAI;AAAA,EAEf;AACI,SAAO,OAAO,UAAU,KAAK;AACjC;AACA,eAAe,gBAAgB,MAAM,SAAS;AAC1C,MAAI,oBAAoB,MAAM,iBAAiB,GAAG;AAC9C,WAAO,mBAAmB,IAAI;AAAA,EACtC;AACI,MAAI,oBAAoB,MAAM,gBAAgB,GAAG;AAC7C,WAAO,kBAAkB,MAAM,OAAO;AAAA,EAC9C;AACI,MAAI,oBAAoB,MAAM,iBAAiB,GAAG;AAC9C,WAAO,mBAAmB,MAAM,OAAO;AAAA,EAC/C;AACI,SAAO,KAAK,UAAU,aAAa,IAAI,CAAC;AAC5C;AACA,MAAM,gBAAgB,CAAC,SAAS,KAAK,WAAW,QAAQ,KAAK,QAAQ,YAAW,MAAO;AACvF,MAAM,eAAe,CAAC,SAAS,KAAK,WAAW,QAAQ,KAAK,QAAQ,YAAW,MAAO;AACtF,eAAe,cAAc,YAAY,YAAY,SAAS;AAC1D,MAAI,IAAI;AACR,MAAI,aAAa,UAAU,GAAG;AAC1B,WAAO;AAAA,EACf;AACI,MAAI,WAAW,CAAE;AACjB,MAAI,cAAc,UAAU,KAAK,WAAW,eAAe;AACvD,eAAW,QAAQ,WAAW,eAAe;AAAA,EACrD,WACa,oBAAoB,YAAY,iBAAiB,OACpD,KAAK,WAAW,qBAAqB,QAAQ,OAAO,SAAS,SAAS,GAAG,OAAO;AAClF,eAAW,QAAQ,WAAW,gBAAgB,KAAK,UAAU;AAAA,EACrE,OACS;AACD,eAAW,UAAU,KAAK,WAAW,gBAAgB,QAAQ,OAAO,SAAS,KAAK,YAAY,UAAU;AAAA,EAChH;AACI,MAAI,SAAS,WAAW,KACpB,oBAAoB,YAAY,gBAAgB,GAAG;AACnD,WAAO;AAAA,EACf;AACI,QAAM,SAAS,OAAO,CAAC,UAAU,UAAU,SACtC,KAAK,MAAM,UAAU,OAAO,OAAO,CAAC,EACpC,KAAK,CAAC,gBAAgB;AACvB,QAAI,aAAa;AACb,iBAAW,YAAY,WAAW;AAAA,IAC9C;AAAA,EACA,CAAK,GAAG,QAAQ,SAAS;AACrB,SAAO;AACX;AACA,SAAS,cAAc,YAAY,YAAY,SAAS;AACpD,QAAM,cAAc,WAAW;AAC/B,MAAI,CAAC,aAAa;AACd;AAAA,EACR;AACI,QAAM,cAAc,OAAO,iBAAiB,UAAU;AACtD,MAAI,YAAY,SAAS;AACrB,gBAAY,UAAU,YAAY;AAClC,gBAAY,kBAAkB,YAAY;AAAA,EAClD,OACS;AACD,uBAAmB,OAAO,EAAE,QAAQ,CAAC,SAAS;AAC1C,UAAI,QAAQ,YAAY,iBAAiB,IAAI;AAC7C,UAAI,SAAS,eAAe,MAAM,SAAS,IAAI,GAAG;AAC9C,cAAM,cAAc,KAAK,MAAM,WAAW,MAAM,UAAU,GAAG,MAAM,SAAS,CAAC,CAAC,CAAC,IAAI;AACnF,gBAAQ,GAAG,WAAW;AAAA,MACtC;AACY,UAAI,oBAAoB,YAAY,iBAAiB,KACjD,SAAS,aACT,UAAU,UAAU;AACpB,gBAAQ;AAAA,MACxB;AACY,UAAI,SAAS,OAAO,WAAW,aAAa,GAAG,GAAG;AAC9C,gBAAQ,QAAQ,WAAW,aAAa,GAAG,CAAC;AAAA,MAC5D;AACY,kBAAY,YAAY,MAAM,OAAO,YAAY,oBAAoB,IAAI,CAAC;AAAA,IACtF,CAAS;AAAA,EACT;AACA;AACA,SAAS,gBAAgB,YAAY,YAAY;AAC7C,MAAI,oBAAoB,YAAY,mBAAmB,GAAG;AACtD,eAAW,YAAY,WAAW;AAAA,EAC1C;AACI,MAAI,oBAAoB,YAAY,gBAAgB,GAAG;AACnD,eAAW,aAAa,SAAS,WAAW,KAAK;AAAA,EACzD;AACA;AACA,SAAS,iBAAiB,YAAY,YAAY;AAC9C,MAAI,oBAAoB,YAAY,iBAAiB,GAAG;AACpD,UAAM,eAAe;AACrB,UAAM,iBAAiB,MAAM,KAAK,aAAa,QAAQ,EAAE,KAAK,CAAC,UAAU,WAAW,UAAU,MAAM,aAAa,OAAO,CAAC;AACzH,QAAI,gBAAgB;AAChB,qBAAe,aAAa,YAAY,EAAE;AAAA,IACtD;AAAA,EACA;AACA;AACA,SAAS,SAAS,YAAY,YAAY,SAAS;AAC/C,MAAI,oBAAoB,YAAY,OAAO,GAAG;AAC1C,kBAAc,YAAY,YAAY,OAAO;AAC7C,wBAAoB,YAAY,YAAY,OAAO;AACnD,oBAAgB,YAAY,UAAU;AACtC,qBAAiB,YAAY,UAAU;AAAA,EAC/C;AACI,SAAO;AACX;AACA,eAAe,iBAAiB,OAAO,SAAS;AAC5C,QAAM,OAAO,MAAM,mBAAmB,MAAM,iBAAiB,KAAK,IAAI,CAAE;AACxE,MAAI,KAAK,WAAW,GAAG;AACnB,WAAO;AAAA,EACf;AACI,QAAM,gBAAgB,CAAE;AACxB,WAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AAClC,UAAM,MAAM,KAAK,CAAC;AAClB,UAAM,KAAK,IAAI,aAAa,YAAY;AACxC,QAAI,IAAI;AACJ,YAAM,QAAQ,MAAM,cAAc,EAAE;AACpC,YAAM,aAAa,SAAS,cAAc,EAAE;AAC5C,UAAI,CAAC,SAAS,cAAc,CAAC,cAAc,EAAE,GAAG;AAE5C,sBAAc,EAAE,IAAK,MAAM,UAAU,YAAY,SAAS,IAAI;AAAA,MAC9E;AAAA,IACA;AAAA,EACA;AACI,QAAM,QAAQ,OAAO,OAAO,aAAa;AACzC,MAAI,MAAM,QAAQ;AACd,UAAM,KAAK;AACX,UAAM,MAAM,SAAS,gBAAgB,IAAI,KAAK;AAC9C,QAAI,aAAa,SAAS,EAAE;AAC5B,QAAI,MAAM,WAAW;AACrB,QAAI,MAAM,QAAQ;AAClB,QAAI,MAAM,SAAS;AACnB,QAAI,MAAM,WAAW;AACrB,QAAI,MAAM,UAAU;AACpB,UAAM,OAAO,SAAS,gBAAgB,IAAI,MAAM;AAChD,QAAI,YAAY,IAAI;AACpB,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACnC,WAAK,YAAY,MAAM,CAAC,CAAC;AAAA,IACrC;AACQ,UAAM,YAAY,GAAG;AAAA,EAC7B;AACI,SAAO;AACX;AACO,eAAe,UAAU,MAAM,SAAS,QAAQ;AACnD,MAAI,CAAC,UAAU,QAAQ,UAAU,CAAC,QAAQ,OAAO,IAAI,GAAG;AACpD,WAAO;AAAA,EACf;AACI,SAAO,QAAQ,QAAQ,IAAI,EACtB,KAAK,CAAC,eAAe,gBAAgB,YAAY,OAAO,CAAC,EACzD,KAAK,CAAC,eAAe,cAAc,MAAM,YAAY,OAAO,CAAC,EAC7D,KAAK,CAAC,eAAe,SAAS,MAAM,YAAY,OAAO,CAAC,EACxD,KAAK,CAAC,eAAe,iBAAiB,YAAY,OAAO,CAAC;AACnE;ACnLA,MAAM,YAAY;AAClB,MAAM,wBAAwB;AAC9B,MAAM,iBAAiB;AACvB,SAAS,QAAQ,KAAK;AAElB,QAAM,UAAU,IAAI,QAAQ,4BAA4B,MAAM;AAC9D,SAAO,IAAI,OAAO,iBAAiB,OAAO,eAAe,GAAG;AAChE;AACO,SAAS,UAAU,SAAS;AAC/B,QAAM,OAAO,CAAE;AACf,UAAQ,QAAQ,WAAW,CAAC,KAAK,WAAW,QAAQ;AAChD,SAAK,KAAK,GAAG;AACb,WAAO;AAAA,EACf,CAAK;AACD,SAAO,KAAK,OAAO,CAAC,QAAQ,CAAC,UAAU,GAAG,CAAC;AAC/C;AACO,eAAe,MAAM,SAAS,aAAa,SAAS,SAAS,mBAAmB;AACnF,MAAI;AACA,UAAM,cAAc,UAAU,WAAW,aAAa,OAAO,IAAI;AACjE,UAAM,cAAc,YAAY,WAAW;AAC3C,QAAI;AACJ,QAAI,kBAAmB;AAAA,SAIlB;AACD,gBAAU,MAAM,kBAAkB,aAAa,aAAa,OAAO;AAAA,IAC/E;AACQ,WAAO,QAAQ,QAAQ,QAAQ,WAAW,GAAG,KAAK,OAAO,IAAI;AAAA,EACrE,SACW,OAAO;AAAA,EAElB;AACI,SAAO;AACX;AACA,SAAS,0BAA0B,KAAK,EAAE,uBAAuB;AAC7D,SAAO,CAAC,sBACF,MACA,IAAI,QAAQ,gBAAgB,CAAC,UAAU;AAErC,WAAO,MAAM;AACT,YAAM,CAAC,KAAG,EAAI,MAAM,IAAI,sBAAsB,KAAK,KAAK,KAAK,CAAE;AAC/D,UAAI,CAAC,QAAQ;AACT,eAAO;AAAA,MAC3B;AACgB,UAAI,WAAW,qBAAqB;AAChC,eAAO,QAAQ,GAAG;AAAA,MACtC;AAAA,IACA;AAAA,EACA,CAAS;AACT;AACO,SAAS,YAAY,KAAK;AAC7B,SAAO,IAAI,OAAO,SAAS,MAAM;AACrC;AACO,eAAe,eAAe,SAAS,SAAS,SAAS;AAC5D,MAAI,CAAC,YAAY,OAAO,GAAG;AACvB,WAAO;AAAA,EACf;AACI,QAAM,kBAAkB,0BAA0B,SAAS,OAAO;AAClE,QAAM,OAAO,UAAU,eAAe;AACtC,SAAO,KAAK,OAAO,CAAC,UAAU,QAAQ,SAAS,KAAK,CAAC,QAAQ,MAAM,KAAK,KAAK,SAAS,OAAO,CAAC,GAAG,QAAQ,QAAQ,eAAe,CAAC;AACrI;AC5DA,eAAe,UAAU,UAAU,MAAM,SAAS;AAC9C,MAAI;AACJ,QAAM,aAAa,KAAK,KAAK,WAAW,QAAQ,OAAO,SAAS,SAAS,GAAG,iBAAiB,QAAQ;AACrG,MAAI,WAAW;AACX,UAAM,YAAY,MAAM,eAAe,WAAW,MAAM,OAAO;AAC/D,SAAK,MAAM,YAAY,UAAU,WAAW,KAAK,MAAM,oBAAoB,QAAQ,CAAC;AACpF,WAAO;AAAA,EACf;AACI,SAAO;AACX;AACA,eAAe,gBAAgB,YAAY,SAAS;AAEhD,EAAC,MAAM,UAAU,cAAc,YAAY,OAAO,KAC7C,MAAM,UAAU,oBAAoB,YAAY,OAAO;AAC5D,EAAC,MAAM,UAAU,QAAQ,YAAY,OAAO,KACvC,MAAM,UAAU,gBAAgB,YAAY,OAAO,KACnD,MAAM,UAAU,cAAc,YAAY,OAAO,KACjD,MAAM,UAAU,sBAAsB,YAAY,OAAO;AAClE;AACA,eAAe,eAAe,YAAY,SAAS;AAC/C,QAAM,iBAAiB,oBAAoB,YAAY,gBAAgB;AACvE,MAAI,EAAE,kBAAkB,CAAC,UAAU,WAAW,GAAG,MAC7C,EAAE,oBAAoB,YAAY,eAAe,KAC7C,CAAC,UAAU,WAAW,KAAK,OAAO,IAAI;AAC1C;AAAA,EACR;AACI,QAAM,MAAM,iBAAiB,WAAW,MAAM,WAAW,KAAK;AAC9D,QAAM,UAAU,MAAM,kBAAkB,KAAK,YAAY,GAAG,GAAG,OAAO;AACtE,QAAM,IAAI,QAAQ,CAAC,SAAS,WAAW;AACnC,eAAW,SAAS;AACpB,eAAW,UAAU,QAAQ,sBACvB,IAAI,eAAe;AACjB,UAAI;AACA,gBAAQ,QAAQ,oBAAoB,GAAG,UAAU,CAAC;AAAA,MACtE,SACuB,OAAO;AACV,eAAO,KAAK;AAAA,MAChC;AAAA,IACA,IACc;AACN,UAAM,QAAQ;AACd,QAAI,MAAM,QAAQ;AACd,YAAM,SAAS;AAAA,IAC3B;AACQ,QAAI,MAAM,YAAY,QAAQ;AAC1B,YAAM,UAAU;AAAA,IAC5B;AACQ,QAAI,gBAAgB;AAChB,iBAAW,SAAS;AACpB,iBAAW,MAAM;AAAA,IAC7B,OACa;AACD,iBAAW,KAAK,UAAU;AAAA,IACtC;AAAA,EACA,CAAK;AACL;AACA,eAAe,cAAc,YAAY,SAAS;AAC9C,QAAM,WAAW,QAAQ,WAAW,UAAU;AAC9C,QAAM,YAAY,SAAS,IAAI,CAAC,UAAU,YAAY,OAAO,OAAO,CAAC;AACrE,QAAM,QAAQ,IAAI,SAAS,EAAE,KAAK,MAAM,UAAU;AACtD;AACO,eAAe,YAAY,YAAY,SAAS;AACnD,MAAI,oBAAoB,YAAY,OAAO,GAAG;AAC1C,UAAM,gBAAgB,YAAY,OAAO;AACzC,UAAM,eAAe,YAAY,OAAO;AACxC,UAAM,cAAc,YAAY,OAAO;AAAA,EAC/C;AACA;ACvEO,SAAS,WAAW,MAAM,SAAS;AACtC,QAAM,EAAE,MAAK,IAAK;AAClB,MAAI,QAAQ,iBAAiB;AACzB,UAAM,kBAAkB,QAAQ;AAAA,EACxC;AACI,MAAI,QAAQ,OAAO;AACf,UAAM,QAAQ,GAAG,QAAQ,KAAK;AAAA,EACtC;AACI,MAAI,QAAQ,QAAQ;AAChB,UAAM,SAAS,GAAG,QAAQ,MAAM;AAAA,EACxC;AACI,QAAM,SAAS,QAAQ;AACvB,MAAI,UAAU,MAAM;AAChB,WAAO,KAAK,MAAM,EAAE,QAAQ,CAAC,QAAQ;AACjC,YAAM,GAAG,IAAI,OAAO,GAAG;AAAA,IACnC,CAAS;AAAA,EACT;AACI,SAAO;AACX;ACfA,MAAM,gBAAgB,CAAE;AACxB,eAAe,SAAS,KAAK;AACzB,MAAIC,SAAQ,cAAc,GAAG;AAC7B,MAAIA,UAAS,MAAM;AACf,WAAOA;AAAA,EACf;AACI,QAAM,MAAM,MAAM,MAAM,GAAG;AAC3B,QAAM,UAAU,MAAM,IAAI,KAAM;AAChC,EAAAA,SAAQ,EAAE,KAAK,QAAS;AACxB,gBAAc,GAAG,IAAIA;AACrB,SAAOA;AACX;AACA,eAAe,WAAW,MAAM,SAAS;AACrC,MAAI,UAAU,KAAK;AACnB,QAAM,WAAW;AACjB,QAAM,WAAW,QAAQ,MAAM,eAAe,KAAK,CAAE;AACrD,QAAM,YAAY,SAAS,IAAI,OAAO,QAAQ;AAC1C,QAAI,MAAM,IAAI,QAAQ,UAAU,IAAI;AACpC,QAAI,CAAC,IAAI,WAAW,UAAU,GAAG;AAC7B,YAAM,IAAI,IAAI,KAAK,KAAK,GAAG,EAAE;AAAA,IACzC;AACQ,WAAO,eAAe,KAAK,QAAQ,kBAAkB,CAAC,EAAE,aAAa;AACjE,gBAAU,QAAQ,QAAQ,KAAK,OAAO,MAAM,GAAG;AAC/C,aAAO,CAAC,KAAK,MAAM;AAAA,IAC/B,CAAS;AAAA,EACT,CAAK;AACD,SAAO,QAAQ,IAAI,SAAS,EAAE,KAAK,MAAM,OAAO;AACpD;AACA,SAAS,SAAS,QAAQ;AACtB,MAAI,UAAU,MAAM;AAChB,WAAO,CAAE;AAAA,EACjB;AACI,QAAM,SAAS,CAAE;AACjB,QAAM,gBAAgB;AAEtB,MAAI,UAAU,OAAO,QAAQ,eAAe,EAAE;AAE9C,QAAM,iBAAiB,IAAI,OAAO,oDAAoD,IAAI;AAE1F,SAAO,MAAM;AACT,UAAM,UAAU,eAAe,KAAK,OAAO;AAC3C,QAAI,YAAY,MAAM;AAClB;AAAA,IACZ;AACQ,WAAO,KAAK,QAAQ,CAAC,CAAC;AAAA,EAC9B;AACI,YAAU,QAAQ,QAAQ,gBAAgB,EAAE;AAC5C,QAAM,cAAc;AAEpB,QAAM,mBAAmB;AAGzB,QAAM,eAAe,IAAI,OAAO,kBAAkB,IAAI;AAEtD,SAAO,MAAM;AACT,QAAI,UAAU,YAAY,KAAK,OAAO;AACtC,QAAI,YAAY,MAAM;AAClB,gBAAU,aAAa,KAAK,OAAO;AACnC,UAAI,YAAY,MAAM;AAClB;AAAA,MAChB,OACiB;AACD,oBAAY,YAAY,aAAa;AAAA,MACrD;AAAA,IACA,OACa;AACD,mBAAa,YAAY,YAAY;AAAA,IACjD;AACQ,WAAO,KAAK,QAAQ,CAAC,CAAC;AAAA,EAC9B;AACI,SAAO;AACX;AACA,eAAe,YAAY,aAAa,SAAS;AAC7C,QAAM,MAAM,CAAE;AACd,QAAM,YAAY,CAAE;AAEpB,cAAY,QAAQ,CAAC,UAAU;AAC3B,QAAI,cAAc,OAAO;AACrB,UAAI;AACA,gBAAQ,MAAM,YAAY,CAAA,CAAE,EAAE,QAAQ,CAAC,MAAM,UAAU;AACnD,cAAI,KAAK,SAAS,QAAQ,aAAa;AACnC,gBAAI,cAAc,QAAQ;AAC1B,kBAAM,MAAM,KAAK;AACjB,kBAAM,WAAW,SAAS,GAAG,EACxB,KAAK,CAAC,aAAa,WAAW,UAAU,OAAO,CAAC,EAChD,KAAK,CAAC,YAAY,SAAS,OAAO,EAAE,QAAQ,CAAC,SAAS;AACvD,kBAAI;AACA,sBAAM,WAAW,MAAM,KAAK,WAAW,SAAS,IACzC,eAAe,IAChB,MAAM,SAAS,MAAM;AAAA,cAC3D,SACmC,OAAO;AACV,wBAAQ,MAAM,wCAAwC;AAAA,kBAClD;AAAA,kBACA;AAAA,gBACpC,CAAiC;AAAA,cACjC;AAAA,YACA,CAAyB,CAAC,EACG,MAAM,CAAC,MAAM;AACd,sBAAQ,MAAM,4BAA4B,EAAE,SAAQ,CAAE;AAAA,YAClF,CAAyB;AACD,sBAAU,KAAK,QAAQ;AAAA,UAC/C;AAAA,QACA,CAAiB;AAAA,MACjB,SACmB,GAAG;AACN,cAAM,SAAS,YAAY,KAAK,CAAC,MAAM,EAAE,QAAQ,IAAI,KAAK,SAAS,YAAY,CAAC;AAChF,YAAI,MAAM,QAAQ,MAAM;AACpB,oBAAU,KAAK,SAAS,MAAM,IAAI,EAC7B,KAAK,CAAC,aAAa,WAAW,UAAU,OAAO,CAAC,EAChD,KAAK,CAAC,YAAY,SAAS,OAAO,EAAE,QAAQ,CAAC,SAAS;AACvD,mBAAO,WAAW,MAAM,OAAO,SAAS,MAAM;AAAA,UACtE,CAAqB,CAAC,EACG,MAAM,CAAC,QAAQ;AAChB,oBAAQ,MAAM,mCAAmC,GAAG;AAAA,UAC5E,CAAqB,CAAC;AAAA,QACtB;AACgB,gBAAQ,MAAM,kCAAkC,CAAC;AAAA,MACjE;AAAA,IACA;AAAA,EACA,CAAK;AACD,SAAO,QAAQ,IAAI,SAAS,EAAE,KAAK,MAAM;AAErC,gBAAY,QAAQ,CAAC,UAAU;AAC3B,UAAI,cAAc,OAAO;AACrB,YAAI;AACA,kBAAQ,MAAM,YAAY,CAAE,CAAA,EAAE,QAAQ,CAAC,SAAS;AAC5C,gBAAI,KAAK,IAAI;AAAA,UACrC,CAAqB;AAAA,QACrB,SACuB,GAAG;AACN,kBAAQ,MAAM,sCAAsC,MAAM,IAAI,IAAI,CAAC;AAAA,QACvF;AAAA,MACA;AAAA,IACA,CAAS;AACD,WAAO;AAAA,EACf,CAAK;AACL;AACA,SAAS,gBAAgB,UAAU;AAC/B,SAAO,SACF,OAAO,CAAC,SAAS,KAAK,SAAS,QAAQ,cAAc,EACrD,OAAO,CAAC,SAAS,YAAY,KAAK,MAAM,iBAAiB,KAAK,CAAC,CAAC;AACzE;AACA,eAAe,kBAAkB,MAAM,SAAS;AAC5C,MAAI,KAAK,iBAAiB,MAAM;AAC5B,UAAM,IAAI,MAAM,2CAA2C;AAAA,EACnE;AACI,QAAM,cAAc,QAAQ,KAAK,cAAc,WAAW;AAC1D,QAAM,WAAW,MAAM,YAAY,aAAa,OAAO;AACvD,SAAO,gBAAgB,QAAQ;AACnC;AACA,SAAS,oBAAoB,MAAM;AAC/B,SAAO,KAAK,KAAI,EAAG,QAAQ,SAAS,EAAE;AAC1C;AACA,SAAS,aAAa,MAAM;AACxB,QAAM,QAAQ,oBAAI,IAAK;AACvB,WAAS,SAASC,OAAM;AACpB,UAAM,aAAaA,MAAK,MAAM,cAAc,iBAAiBA,KAAI,EAAE;AACnE,eAAW,MAAM,GAAG,EAAE,QAAQ,CAAC,SAAS;AACpC,YAAM,IAAI,oBAAoB,IAAI,CAAC;AAAA,IAC/C,CAAS;AACD,UAAM,KAAKA,MAAK,QAAQ,EAAE,QAAQ,CAAC,UAAU;AACzC,UAAI,iBAAiB,aAAa;AAC9B,iBAAS,KAAK;AAAA,MAC9B;AAAA,IACA,CAAS;AAAA,EACT;AACI,WAAS,IAAI;AACb,SAAO;AACX;AACO,eAAe,cAAc,MAAM,SAAS;AAC/C,QAAM,QAAQ,MAAM,kBAAkB,MAAM,OAAO;AACnD,QAAM,YAAY,aAAa,IAAI;AACnC,QAAM,WAAW,MAAM,QAAQ,IAAI,MAC9B,OAAO,CAAC,SAAS,UAAU,IAAI,oBAAoB,KAAK,MAAM,UAAU,CAAC,CAAC,EAC1E,IAAI,CAAC,SAAS;AACf,UAAM,UAAU,KAAK,mBACf,KAAK,iBAAiB,OACtB;AACN,WAAO,eAAe,KAAK,SAAS,SAAS,OAAO;AAAA,EAC5D,CAAK,CAAC;AACF,SAAO,SAAS,KAAK,IAAI;AAC7B;AACO,eAAe,cAAc,YAAY,SAAS;AACrD,QAAM,UAAU,QAAQ,gBAAgB,OAClC,QAAQ,eACR,QAAQ,YACJ,OACA,MAAM,cAAc,YAAY,OAAO;AACjD,MAAI,SAAS;AACT,UAAM,YAAY,SAAS,cAAc,OAAO;AAChD,UAAM,eAAe,SAAS,eAAe,OAAO;AACpD,cAAU,YAAY,YAAY;AAClC,QAAI,WAAW,YAAY;AACvB,iBAAW,aAAa,WAAW,WAAW,UAAU;AAAA,IACpE,OACa;AACD,iBAAW,YAAY,SAAS;AAAA,IAC5C;AAAA,EACA;AACA;ACtMO,eAAe,MAAM,MAAM,UAAU,IAAI;AAC5C,QAAM,EAAE,OAAO,OAAM,IAAK,aAAa,MAAM,OAAO;AACpD,QAAM,aAAc,MAAM,UAAU,MAAM,SAAS,IAAI;AACvD,QAAM,cAAc,YAAY,OAAO;AACvC,QAAM,YAAY,YAAY,OAAO;AACrC,aAAW,YAAY,OAAO;AAC9B,QAAM,UAAU,MAAM,cAAc,YAAY,OAAO,MAAM;AAC7D,SAAO;AACX;AACO,eAAe,SAAS,MAAM,UAAU,IAAI;AAC/C,QAAM,EAAE,OAAO,OAAM,IAAK,aAAa,MAAM,OAAO;AACpD,QAAM,MAAM,MAAM,MAAM,MAAM,OAAO;AACrC,QAAM,MAAM,MAAM,YAAY,GAAG;AACjC,QAAM,SAAS,SAAS,cAAc,QAAQ;AAC9C,QAAM,UAAU,OAAO,WAAW,IAAI;AACtC,QAAM,QAAQ,QAAQ,cAAc,cAAe;AACnD,QAAM,cAAc,QAAQ,eAAe;AAC3C,QAAM,eAAe,QAAQ,gBAAgB;AAC7C,SAAO,QAAQ,cAAc;AAC7B,SAAO,SAAS,eAAe;AAC/B,MAAI,CAAC,QAAQ,eAAe;AACxB,0BAAsB,MAAM;AAAA,EACpC;AACI,SAAO,MAAM,QAAQ,GAAG,WAAW;AACnC,SAAO,MAAM,SAAS,GAAG,YAAY;AACrC,MAAI,QAAQ,iBAAiB;AACzB,YAAQ,YAAY,QAAQ;AAC5B,YAAQ,SAAS,GAAG,GAAG,OAAO,OAAO,OAAO,MAAM;AAAA,EAC1D;AACI,UAAQ,UAAU,KAAK,GAAG,GAAG,OAAO,OAAO,OAAO,MAAM;AACxD,SAAO;AACX;AAOO,eAAe,MAAM,MAAM,UAAU,IAAI;AAC5C,QAAM,SAAS,MAAM,SAAS,MAAM,OAAO;AAC3C,SAAO,OAAO,UAAW;AAC7B;", "x_google_ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9]}