{"version": 3, "file": "sellerSetting.dashboard-DJMAwsYp.js", "sources": ["../../../app/routes/sellerSetting.dashboard.tsx"], "sourcesContent": ["import { useLoaderData } from \"@remix-run/react\";\r\nimport { metabaseService } from \"../utils/metabase\";\r\nimport { withAuth, withResponse } from \"../utils/auth-utils\";\r\n\r\nexport const loader = withAuth(async ({ user }) => {\r\n  const embedUrl = metabaseService.generateDashboardUrl(10, {\r\n    id: user.sellerId,\r\n  });\r\n  \r\n  return withResponse({ embedUrl });\r\n});\r\n\r\nexport default function Dashboard() {\r\n  const { embedUrl } = useLoaderData<typeof loader>();\r\n\r\n  return (\r\n    <div className=\"p-6\">\r\n      <div className=\"mb-6\">\r\n        <h1 className=\"text-3xl font-bold text-gray-900\">Dashboard</h1>\r\n        <p className=\"text-gray-600 mt-2\">Overview of your restaurant performance</p>\r\n      </div>\r\n      \r\n      <div className=\"w-full h-screen\">\r\n        {embedUrl ? (\r\n          <iframe\r\n            id=\"metabase-iframe\"\r\n            src={embedUrl}\r\n            title=\"Restaurant Dashboard\"\r\n            className=\"w-full h-full border-0\"\r\n          />\r\n        ) : (\r\n          <div className=\"flex items-center justify-center min-h-[400px]\">\r\n            <div className=\"text-center\">\r\n              <div className=\"mx-auto mb-4 w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center\">\r\n                <svg className=\"w-8 h-8 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\" />\r\n                </svg>\r\n              </div>\r\n              <h2 className=\"text-2xl font-bold mb-2\">Dashboard Loading</h2>\r\n              <p className=\"text-gray-500\">\r\n                Loading your restaurant analytics...\r\n              </p>\r\n            </div>\r\n          </div>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n} "], "names": ["Dashboard", "embedUrl", "useLoaderData", "jsxs", "className", "children", "jsx", "id", "src", "title", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d"], "mappings": ";;;;AAYA,SAAwBA,YAAY;AAC5B,QAAA;AAAA,IAAEC;AAAAA,EAAS,IAAIC,cAA6B;AAGhD,SAAAC,kCAAAA,KAAC,OAAI;AAAA,IAAAC,WAAU;AAAA,IACbC,UAAA,CAACF,kCAAA,KAAA,OAAA;AAAA,MAAIC,WAAU;AAAA,MACbC,UAAA,CAACC,kCAAA,IAAA,MAAA;AAAA,QAAGF,WAAU;AAAA,QAAmCC,UAAS;AAAA,MAAA,CAAA,GACzDC,kCAAA,IAAA,KAAA;AAAA,QAAEF,WAAU;AAAA,QAAqBC,UAAuC;AAAA,MAAA,CAAA,CAAA;AAAA,IAC3E,CAAA,GAECC,kCAAA,IAAA,OAAA;AAAA,MAAIF,WAAU;AAAA,MACZC,UACCJ,WAAAK,kCAAA,IAAC,UAAA;AAAA,QACCC,IAAG;AAAA,QACHC,KAAKP;AAAAA,QACLQ,OAAM;AAAA,QACNL,WAAU;AAAA,MAAA,CACZ,0CAEC,OAAI;AAAA,QAAAA,WAAU;AAAA,QACbC,UAACF,kCAAA,KAAA,OAAA;AAAA,UAAIC,WAAU;AAAA,UACbC,UAAA,CAACC,kCAAA,IAAA,OAAA;AAAA,YAAIF,WAAU;AAAA,YACbC,UAACC,kCAAA,IAAA,OAAA;AAAA,cAAIF,WAAU;AAAA,cAAqBM,MAAK;AAAA,cAAOC,QAAO;AAAA,cAAeC,SAAQ;AAAA,cAC5EP,UAACC,kCAAA,IAAA,QAAA;AAAA,gBAAKO,eAAc;AAAA,gBAAQC,gBAAe;AAAA,gBAAQC,aAAa;AAAA,gBAAGC,GAAE;AAAA,cAAuM,CAAA;AAAA,YAC9Q,CAAA;AAAA,UACF,CAAA,GACCV,kCAAA,IAAA,MAAA;AAAA,YAAGF,WAAU;AAAA,YAA0BC,UAAiB;AAAA,UAAA,CAAA,GACxDC,kCAAA,IAAA,KAAA;AAAA,YAAEF,WAAU;AAAA,YAAgBC,UAE7B;AAAA,UAAA,CAAA,CAAA;AAAA,QACF,CAAA;AAAA,MACF,CAAA;AAAA,IAEJ,CAAA,CAAA;AAAA,EACF,CAAA;AAEJ;"}