import { j as jsxRuntimeExports } from "./jsx-runtime-bB2y7OuD.js";
const ResponsivePagination = ({
  totalPages,
  currentPage,
  onPageChange,
  maxVisiblePages = 5
}) => {
  const getPageNumbers = () => {
    const pages = [];
    const halfVisible = Math.floor(maxVisiblePages / 2);
    let startPage = Math.max(0, currentPage - halfVisible);
    let endPage = Math.min(totalPages - 1, startPage + maxVisiblePages - 1);
    if (endPage - startPage + 1 < maxVisiblePages) {
      startPage = Math.max(0, endPage - maxVisiblePages + 1);
    }
    if (startPage > 0) {
      pages.push(0);
      if (startPage > 1) pages.push("ellipsis");
    }
    for (let i = startPage; i <= endPage; i++) {
      pages.push(i);
    }
    if (endPage < totalPages - 1) {
      if (endPage < totalPages - 2) pages.push("ellipsis");
      pages.push(totalPages - 1);
    }
    return pages;
  };
  return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center justify-end space-x-2 py-4 pr-5", children: [
    /* @__PURE__ */ jsxRuntimeExports.jsx(
      "button",
      {
        className: "px-3 py-1 border rounded disabled:opacity-50",
        onClick: () => onPageChange(currentPage - 1),
        disabled: currentPage === 0,
        children: "Prev"
      }
    ),
    getPageNumbers().map(
      (page, index) => page === "ellipsis" ? /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "px-3", children: "..." }, `ellipsis-${index}`) : /* @__PURE__ */ jsxRuntimeExports.jsx(
        "button",
        {
          className: `px-3 py-1 border rounded ${currentPage === page ? "bg-blue-500 text-white" : ""}`,
          onClick: () => onPageChange(page),
          children: page + 1
        },
        page
      )
    ),
    /* @__PURE__ */ jsxRuntimeExports.jsx(
      "button",
      {
        className: "px-3 py-1 border rounded disabled:opacity-50",
        onClick: () => onPageChange(currentPage + 1),
        disabled: currentPage === totalPages - 1,
        children: "Next"
      }
    )
  ] });
};
export {
  ResponsivePagination as R
};
//# sourceMappingURL=responsivePagination-D-iSBEkA.js.map
