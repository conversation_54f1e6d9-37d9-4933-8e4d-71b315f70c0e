import { j as jsxRuntimeExports, r as reactExports } from "./jsx-runtime-bB2y7OuD.js";
import { I as Input } from "./input-3v87qohQ.js";
import { S as ScrollArea } from "./scroll-area-Cyn6sZm1.js";
import { B as Button } from "./button-ByAXMyvk.js";
import { C as Card, a as CardContent } from "./card-BJQMSLe_.js";
import { B as Badge } from "./badge-BsHDHlRV.js";
import { G as GenIcon, F as FaWhatsapp } from "./index-Bx88Z3Oa.js";
import { c as createLucideIcon } from "./createLucideIcon-uwkRm45G.js";
import { S as Share2 } from "./share-2-BRs1HrxR.js";
import { M as MessageCircle } from "./message-circle-VDmkNDXi.js";
import { D as Dialog, a as DialogContent, c as <PERSON><PERSON>Header, b as <PERSON>alogTitle, e as DialogFooter } from "./dialog-BqKosxNq.js";
import { S as Select, a as SelectTrigger, b as SelectValue, c as SelectContent, d as SelectItem } from "./select-BFGSXKcr.js";
import { L as Label } from "./label-cSASrwzW.js";
import { R as Root, P as Portal, O as Overlay, C as Content } from "./index-DdafHWkt.js";
import { P as Primitive } from "./index-CzZ6EYkN.js";
import { u as useId } from "./index-DVTNuYOr.js";
import { c as cn } from "./utils-GkgzjW3c.js";
import { S as Search } from "./search-DzDJ71yc.js";
import { P as Popover, a as PopoverTrigger, b as PopoverContent } from "./popover-CD2vRFIm.js";
import { C as Check } from "./check-_dbWxIzT.js";
import { X as X$1 } from "./x-CCG_WJDF.js";
import { a as useFetcher, u as useLoaderData, b as useSearchParams, c as useSubmit } from "./components-D7UvGag_.js";
import "./index-QLGF6kQx.js";
import "./index-z_byfFrQ.js";
import "./index-D7VH9Fc8.js";
import "./index-BTdCMChR.js";
import "./index-IXOTxK3N.js";
import "./index-ImHKLo0a.js";
import "./index-Vp2vNLNM.js";
import "./index-qCtcpOcW.js";
import "./index-BXzPK7u0.js";
import "./index-CEHS9zzk.js";
import "./index-DscYByPT.js";
import "./index-CkL5tk39.js";
import "./index-dIGjFc0I.js";
import "./index-CpKiYcZd.js";
import "./index-C88PRvfd.js";
import "./chevron-down-pCP5jmjX.js";
import "./index-DhHTcibu.js";
/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */
const ChevronsUpDown = createLucideIcon("ChevronsUpDown", [
  ["path", { d: "m7 15 5 5 5-5", key: "1hf1tw" }],
  ["path", { d: "m7 9 5-5 5 5", key: "sgt6xg" }]
]);
/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */
const Variable = createLucideIcon("Variable", [
  ["path", { d: "M8 21s-4-3-4-9 4-9 4-9", key: "uto9ud" }],
  ["path", { d: "M16 3s4 3 4 9-4 9-4 9", key: "4w2vsq" }],
  ["line", { x1: "15", x2: "9", y1: "9", y2: "15", key: "f7djnv" }],
  ["line", { x1: "9", x2: "15", y1: "9", y2: "15", key: "1shsy8" }]
]);
function BsFillReplyFill(props) {
  return GenIcon({ "attr": { "fill": "currentColor", "viewBox": "0 0 16 16" }, "child": [{ "tag": "path", "attr": { "d": "M5.921 11.9 1.353 8.62a.72.72 0 0 1 0-1.238L5.921 4.1A.716.716 0 0 1 7 4.719V6c1.5 0 6 0 7 8-2.5-4.5-7-4-7-4v1.281c0 .56-.606.898-1.079.62z" }, "child": [] }] })(props);
}
function TemplateCard({ template, onSendMessage }) {
  var _a;
  const getTypeIcon = (type) => {
    switch (type) {
      case "SMS":
        return /* @__PURE__ */ jsxRuntimeExports.jsx(MessageCircle, { className: "h-4 w-4" });
      case "WhatsApp":
      case "hybrid":
        return /* @__PURE__ */ jsxRuntimeExports.jsx(Share2, { className: "h-4 w-4" });
      default:
        return null;
    }
  };
  return /* @__PURE__ */ jsxRuntimeExports.jsx(Card, { className: "hover:shadow-lg transition-shadow", children: /* @__PURE__ */ jsxRuntimeExports.jsxs(CardContent, { className: "p-4 flex flex-col", children: [
    /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center justify-between mb-4", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center gap-2", children: [
        getTypeIcon(template.type),
        /* @__PURE__ */ jsxRuntimeExports.jsx("h3", { className: "font-medium", children: template.name })
      ] }),
      /* @__PURE__ */ jsxRuntimeExports.jsx(Badge, { variant: "outline", className: "font-normal", children: template.type })
    ] }),
    /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "border rounded-lg p-4 mb-4 space-y-4 bg-gray-50", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "text-sm text-muted-foreground mb-2", children: "WhatsApp Preview:" }),
      /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "bg-[#e7ffdb] rounded-lg p-3 max-w-[85%] ml-auto relative shadow-sm", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "text-sm text-[#111b21] font-bold mb-2", children: template.header }),
        /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "whitespace-pre-wrap text-[#111b21] text-sm", children: template.preview || template.content }),
        /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "text-right text-xs text-[#667781] mt-1", children: "09:30 AM" }),
        /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "absolute right-0 top-0 w-2 h-4 bg-[#e7ffdb] transform translate-x-[7px]", style: {
          clipPath: "polygon(0 0, 100% 0, 0 100%)"
        } }),
        /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "flex flex-col justify-center gap-1", children: (_a = template.ctas) == null ? void 0 : _a.map((cta) => /* @__PURE__ */ jsxRuntimeExports.jsxs(jsxRuntimeExports.Fragment, { children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "border-t mt-1 -mx-3" }),
          /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "text-blue-500 px-2 py-1 text-sm flex gap-2 items-center justify-center", children: [
            cta.type === "quick_reply" ? /* @__PURE__ */ jsxRuntimeExports.jsx(BsFillReplyFill, {}) : null,
            " ",
            /* @__PURE__ */ jsxRuntimeExports.jsx("span", { children: cta.label })
          ] }, cta.id)
        ] })) })
      ] })
    ] }),
    template.variables.length > 0 && /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "space-y-2 mb-4", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center text-sm text-muted-foreground", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx(Variable, { className: "h-4 w-4 mr-2" }),
        /* @__PURE__ */ jsxRuntimeExports.jsx("span", { children: "Variables" })
      ] }),
      /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "flex flex-wrap gap-2", children: template.variables.map((variable) => /* @__PURE__ */ jsxRuntimeExports.jsx(Badge, { variant: "secondary", children: variable }, variable)) })
    ] }),
    /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center justify-between mt-4 self-end", children: [
      template.lastUsed && /* @__PURE__ */ jsxRuntimeExports.jsxs("span", { className: "text-sm text-muted-foreground", children: [
        "Last used: ",
        template.lastUsed
      ] }),
      /* @__PURE__ */ jsxRuntimeExports.jsxs(
        Button,
        {
          className: "rounded-md border border-primary-200 text-primary-600 shadow-none flex items-center hover:bg-primary-600 hover:text-white",
          variant: "outline",
          size: "sm",
          onClick: () => onSendMessage(template),
          children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx(FaWhatsapp, { className: "mr-2" }),
            /* @__PURE__ */ jsxRuntimeExports.jsx("span", { children: "Send Message" })
          ]
        }
      )
    ] })
  ] }) });
}
var U = 1, Y$1 = 0.9, H = 0.8, J = 0.17, p = 0.1, u = 0.999, $ = 0.9999;
var k$1 = 0.99, m = /[\\\/_+.#"@\[\(\{&]/, B = /[\\\/_+.#"@\[\(\{&]/g, K$1 = /[\s-]/, X = /[\s-]/g;
function G$1(_, C, h, P, A, f, O) {
  if (f === C.length) return A === _.length ? U : k$1;
  var T2 = `${A},${f}`;
  if (O[T2] !== void 0) return O[T2];
  for (var L = P.charAt(f), c = h.indexOf(L, A), S = 0, E, N2, R, M2; c >= 0; ) E = G$1(_, C, h, P, c + 1, f + 1, O), E > S && (c === A ? E *= U : m.test(_.charAt(c - 1)) ? (E *= H, R = _.slice(A, c - 1).match(B), R && A > 0 && (E *= Math.pow(u, R.length))) : K$1.test(_.charAt(c - 1)) ? (E *= Y$1, M2 = _.slice(A, c - 1).match(X), M2 && A > 0 && (E *= Math.pow(u, M2.length))) : (E *= J, A > 0 && (E *= Math.pow(u, c - A))), _.charAt(c) !== C.charAt(f) && (E *= $)), (E < p && h.charAt(c - 1) === P.charAt(f + 1) || P.charAt(f + 1) === P.charAt(f) && h.charAt(c - 1) !== P.charAt(f)) && (N2 = G$1(_, C, h, P, c + 1, f + 2, O), N2 * p > E && (E = N2 * p)), E > S && (S = E), c = h.indexOf(L, c + 1);
  return O[T2] = S, S;
}
function D(_) {
  return _.toLowerCase().replace(X, " ");
}
function W(_, C, h) {
  return _ = h && h.length > 0 ? `${_ + " " + h.join(" ")}` : _, G$1(_, C, D(_), D(C), 0, 0, {});
}
var shim = { exports: {} };
var useSyncExternalStoreShim_development = {};
/**
 * @license React
 * use-sync-external-store-shim.development.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
(function() {
  function is(x, y) {
    return x === y && (0 !== x || 1 / x === 1 / y) || x !== x && y !== y;
  }
  function useSyncExternalStore$2(subscribe, getSnapshot) {
    didWarnOld18Alpha || void 0 === React.startTransition || (didWarnOld18Alpha = true, console.error(
      "You are using an outdated, pre-release alpha of React 18 that does not support useSyncExternalStore. The use-sync-external-store shim will not work correctly. Upgrade to a newer pre-release."
    ));
    var value = getSnapshot();
    if (!didWarnUncachedGetSnapshot) {
      var cachedValue = getSnapshot();
      objectIs(value, cachedValue) || (console.error(
        "The result of getSnapshot should be cached to avoid an infinite loop"
      ), didWarnUncachedGetSnapshot = true);
    }
    cachedValue = useState({
      inst: { value, getSnapshot }
    });
    var inst = cachedValue[0].inst, forceUpdate = cachedValue[1];
    useLayoutEffect(
      function() {
        inst.value = value;
        inst.getSnapshot = getSnapshot;
        checkIfSnapshotChanged(inst) && forceUpdate({ inst });
      },
      [subscribe, value, getSnapshot]
    );
    useEffect(
      function() {
        checkIfSnapshotChanged(inst) && forceUpdate({ inst });
        return subscribe(function() {
          checkIfSnapshotChanged(inst) && forceUpdate({ inst });
        });
      },
      [subscribe]
    );
    useDebugValue(value);
    return value;
  }
  function checkIfSnapshotChanged(inst) {
    var latestGetSnapshot = inst.getSnapshot;
    inst = inst.value;
    try {
      var nextValue = latestGetSnapshot();
      return !objectIs(inst, nextValue);
    } catch (error) {
      return true;
    }
  }
  function useSyncExternalStore$1(subscribe, getSnapshot) {
    return getSnapshot();
  }
  "undefined" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ && "function" === typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart && __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(Error());
  var React = reactExports, objectIs = "function" === typeof Object.is ? Object.is : is, useState = React.useState, useEffect = React.useEffect, useLayoutEffect = React.useLayoutEffect, useDebugValue = React.useDebugValue, didWarnOld18Alpha = false, didWarnUncachedGetSnapshot = false, shim2 = "undefined" === typeof window || "undefined" === typeof window.document || "undefined" === typeof window.document.createElement ? useSyncExternalStore$1 : useSyncExternalStore$2;
  useSyncExternalStoreShim_development.useSyncExternalStore = void 0 !== React.useSyncExternalStore ? React.useSyncExternalStore : shim2;
  "undefined" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ && "function" === typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop && __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(Error());
})();
{
  shim.exports = useSyncExternalStoreShim_development;
}
var shimExports = shim.exports;
var N = '[cmdk-group=""]', Q = '[cmdk-group-items=""]', be = '[cmdk-group-heading=""]', Z = '[cmdk-item=""]', le = `${Z}:not([aria-disabled="true"])`, Y = "cmdk-item-select", I = "data-value", he = (r, o, t) => W(r, o, t), ue = reactExports.createContext(void 0), K = () => reactExports.useContext(ue), de = reactExports.createContext(void 0), ee = () => reactExports.useContext(de), fe = reactExports.createContext(void 0);
var me = reactExports.forwardRef((r, o) => {
  let t = k(() => {
    var e, s;
    return { search: "", value: (s = (e = r.value) != null ? e : r.defaultValue) != null ? s : "", filtered: { count: 0, items: /* @__PURE__ */ new Map(), groups: /* @__PURE__ */ new Set() } };
  }), u2 = k(() => /* @__PURE__ */ new Set()), c = k(() => /* @__PURE__ */ new Map()), d = k(() => /* @__PURE__ */ new Map()), f = k(() => /* @__PURE__ */ new Set()), p2 = pe(r), { label: v, children: b, value: l, onValueChange: y, filter: E, shouldFilter: C, loop: H2, disablePointerSelection: ge = false, vimBindings: $2 = true, ...O } = r, te = useId(), B2 = useId(), F = useId(), x = reactExports.useRef(null), R = Te();
  M(() => {
    if (l !== void 0) {
      let e = l.trim();
      t.current.value = e, h.emit();
    }
  }, [l]), M(() => {
    R(6, re);
  }, []);
  let h = reactExports.useMemo(() => ({ subscribe: (e) => (f.current.add(e), () => f.current.delete(e)), snapshot: () => t.current, setState: (e, s, i) => {
    var a, m2, g;
    if (!Object.is(t.current[e], s)) {
      if (t.current[e] = s, e === "search") W2(), U2(), R(1, z);
      else if (e === "value" && (i || R(5, re), ((a = p2.current) == null ? void 0 : a.value) !== void 0)) {
        let S = s != null ? s : "";
        (g = (m2 = p2.current).onValueChange) == null || g.call(m2, S);
        return;
      }
      h.emit();
    }
  }, emit: () => {
    f.current.forEach((e) => e());
  } }), []), q = reactExports.useMemo(() => ({ value: (e, s, i) => {
    var a;
    s !== ((a = d.current.get(e)) == null ? void 0 : a.value) && (d.current.set(e, { value: s, keywords: i }), t.current.filtered.items.set(e, ne(s, i)), R(2, () => {
      U2(), h.emit();
    }));
  }, item: (e, s) => (u2.current.add(e), s && (c.current.has(s) ? c.current.get(s).add(e) : c.current.set(s, /* @__PURE__ */ new Set([e]))), R(3, () => {
    W2(), U2(), t.current.value || z(), h.emit();
  }), () => {
    d.current.delete(e), u2.current.delete(e), t.current.filtered.items.delete(e);
    let i = A();
    R(4, () => {
      W2(), (i == null ? void 0 : i.getAttribute("id")) === e && z(), h.emit();
    });
  }), group: (e) => (c.current.has(e) || c.current.set(e, /* @__PURE__ */ new Set()), () => {
    d.current.delete(e), c.current.delete(e);
  }), filter: () => p2.current.shouldFilter, label: v || r["aria-label"], getDisablePointerSelection: () => p2.current.disablePointerSelection, listId: te, inputId: F, labelId: B2, listInnerRef: x }), []);
  function ne(e, s) {
    var a, m2;
    let i = (m2 = (a = p2.current) == null ? void 0 : a.filter) != null ? m2 : he;
    return e ? i(e, t.current.search, s) : 0;
  }
  function U2() {
    if (!t.current.search || p2.current.shouldFilter === false) return;
    let e = t.current.filtered.items, s = [];
    t.current.filtered.groups.forEach((a) => {
      let m2 = c.current.get(a), g = 0;
      m2.forEach((S) => {
        let P = e.get(S);
        g = Math.max(P, g);
      }), s.push([a, g]);
    });
    let i = x.current;
    _().sort((a, m2) => {
      var P, V;
      let g = a.getAttribute("id"), S = m2.getAttribute("id");
      return ((P = e.get(S)) != null ? P : 0) - ((V = e.get(g)) != null ? V : 0);
    }).forEach((a) => {
      let m2 = a.closest(Q);
      m2 ? m2.appendChild(a.parentElement === m2 ? a : a.closest(`${Q} > *`)) : i.appendChild(a.parentElement === i ? a : a.closest(`${Q} > *`));
    }), s.sort((a, m2) => m2[1] - a[1]).forEach((a) => {
      var g;
      let m2 = (g = x.current) == null ? void 0 : g.querySelector(`${N}[${I}="${encodeURIComponent(a[0])}"]`);
      m2 == null || m2.parentElement.appendChild(m2);
    });
  }
  function z() {
    let e = _().find((i) => i.getAttribute("aria-disabled") !== "true"), s = e == null ? void 0 : e.getAttribute(I);
    h.setState("value", s || void 0);
  }
  function W2() {
    var s, i, a, m2;
    if (!t.current.search || p2.current.shouldFilter === false) {
      t.current.filtered.count = u2.current.size;
      return;
    }
    t.current.filtered.groups = /* @__PURE__ */ new Set();
    let e = 0;
    for (let g of u2.current) {
      let S = (i = (s = d.current.get(g)) == null ? void 0 : s.value) != null ? i : "", P = (m2 = (a = d.current.get(g)) == null ? void 0 : a.keywords) != null ? m2 : [], V = ne(S, P);
      t.current.filtered.items.set(g, V), V > 0 && e++;
    }
    for (let [g, S] of c.current) for (let P of S) if (t.current.filtered.items.get(P) > 0) {
      t.current.filtered.groups.add(g);
      break;
    }
    t.current.filtered.count = e;
  }
  function re() {
    var s, i, a;
    let e = A();
    e && (((s = e.parentElement) == null ? void 0 : s.firstChild) === e && ((a = (i = e.closest(N)) == null ? void 0 : i.querySelector(be)) == null || a.scrollIntoView({ block: "nearest" })), e.scrollIntoView({ block: "nearest" }));
  }
  function A() {
    var e;
    return (e = x.current) == null ? void 0 : e.querySelector(`${Z}[aria-selected="true"]`);
  }
  function _() {
    var e;
    return Array.from(((e = x.current) == null ? void 0 : e.querySelectorAll(le)) || []);
  }
  function J2(e) {
    let i = _()[e];
    i && h.setState("value", i.getAttribute(I));
  }
  function X2(e) {
    var g;
    let s = A(), i = _(), a = i.findIndex((S) => S === s), m2 = i[a + e];
    (g = p2.current) != null && g.loop && (m2 = a + e < 0 ? i[i.length - 1] : a + e === i.length ? i[0] : i[a + e]), m2 && h.setState("value", m2.getAttribute(I));
  }
  function oe(e) {
    let s = A(), i = s == null ? void 0 : s.closest(N), a;
    for (; i && !a; ) i = e > 0 ? Ie(i, N) : Me(i, N), a = i == null ? void 0 : i.querySelector(le);
    a ? h.setState("value", a.getAttribute(I)) : X2(e);
  }
  let ie = () => J2(_().length - 1), ae = (e) => {
    e.preventDefault(), e.metaKey ? ie() : e.altKey ? oe(1) : X2(1);
  }, se = (e) => {
    e.preventDefault(), e.metaKey ? J2(0) : e.altKey ? oe(-1) : X2(-1);
  };
  return reactExports.createElement(Primitive.div, { ref: o, tabIndex: -1, ...O, "cmdk-root": "", onKeyDown: (e) => {
    var s;
    if ((s = O.onKeyDown) == null || s.call(O, e), !e.defaultPrevented) switch (e.key) {
      case "n":
      case "j": {
        $2 && e.ctrlKey && ae(e);
        break;
      }
      case "ArrowDown": {
        ae(e);
        break;
      }
      case "p":
      case "k": {
        $2 && e.ctrlKey && se(e);
        break;
      }
      case "ArrowUp": {
        se(e);
        break;
      }
      case "Home": {
        e.preventDefault(), J2(0);
        break;
      }
      case "End": {
        e.preventDefault(), ie();
        break;
      }
      case "Enter":
        if (!e.nativeEvent.isComposing && e.keyCode !== 229) {
          e.preventDefault();
          let i = A();
          if (i) {
            let a = new Event(Y);
            i.dispatchEvent(a);
          }
        }
    }
  } }, reactExports.createElement("label", { "cmdk-label": "", htmlFor: q.inputId, id: q.labelId, style: Le }, v), j(r, (e) => reactExports.createElement(de.Provider, { value: h }, reactExports.createElement(ue.Provider, { value: q }, e))));
}), ye = reactExports.forwardRef((r, o) => {
  var F, x;
  let t = useId(), u2 = reactExports.useRef(null), c = reactExports.useContext(fe), d = K(), f = pe(r), p2 = (x = (F = f.current) == null ? void 0 : F.forceMount) != null ? x : c == null ? void 0 : c.forceMount;
  M(() => {
    if (!p2) return d.item(t, c == null ? void 0 : c.id);
  }, [p2]);
  let v = ve(t, u2, [r.value, r.children, u2], r.keywords), b = ee(), l = T((R) => R.value && R.value === v.current), y = T((R) => p2 || d.filter() === false ? true : R.search ? R.filtered.items.get(t) > 0 : true);
  reactExports.useEffect(() => {
    let R = u2.current;
    if (!(!R || r.disabled)) return R.addEventListener(Y, E), () => R.removeEventListener(Y, E);
  }, [y, r.onSelect, r.disabled]);
  function E() {
    var R, h;
    C(), (h = (R = f.current).onSelect) == null || h.call(R, v.current);
  }
  function C() {
    b.setState("value", v.current, true);
  }
  if (!y) return null;
  let { disabled: H2, value: ge, onSelect: $2, forceMount: O, keywords: te, ...B2 } = r;
  return reactExports.createElement(Primitive.div, { ref: G([u2, o]), ...B2, id: t, "cmdk-item": "", role: "option", "aria-disabled": !!H2, "aria-selected": !!l, "data-disabled": !!H2, "data-selected": !!l, onPointerMove: H2 || d.getDisablePointerSelection() ? void 0 : C, onClick: H2 ? void 0 : E }, r.children);
}), Se = reactExports.forwardRef((r, o) => {
  let { heading: t, children: u2, forceMount: c, ...d } = r, f = useId(), p2 = reactExports.useRef(null), v = reactExports.useRef(null), b = useId(), l = K(), y = T((C) => c || l.filter() === false ? true : C.search ? C.filtered.groups.has(f) : true);
  M(() => l.group(f), []), ve(f, p2, [r.value, r.heading, v]);
  let E = reactExports.useMemo(() => ({ id: f, forceMount: c }), [c]);
  return reactExports.createElement(Primitive.div, { ref: G([p2, o]), ...d, "cmdk-group": "", role: "presentation", hidden: y ? void 0 : true }, t && reactExports.createElement("div", { ref: v, "cmdk-group-heading": "", "aria-hidden": true, id: b }, t), j(r, (C) => reactExports.createElement("div", { "cmdk-group-items": "", role: "group", "aria-labelledby": t ? b : void 0 }, reactExports.createElement(fe.Provider, { value: E }, C))));
}), Ee = reactExports.forwardRef((r, o) => {
  let { alwaysRender: t, ...u2 } = r, c = reactExports.useRef(null), d = T((f) => !f.search);
  return !t && !d ? null : reactExports.createElement(Primitive.div, { ref: G([c, o]), ...u2, "cmdk-separator": "", role: "separator" });
}), Ce = reactExports.forwardRef((r, o) => {
  let { onValueChange: t, ...u2 } = r, c = r.value != null, d = ee(), f = T((l) => l.search), p2 = T((l) => l.value), v = K(), b = reactExports.useMemo(() => {
    var y;
    let l = (y = v.listInnerRef.current) == null ? void 0 : y.querySelector(`${Z}[${I}="${encodeURIComponent(p2)}"]`);
    return l == null ? void 0 : l.getAttribute("id");
  }, []);
  return reactExports.useEffect(() => {
    r.value != null && d.setState("search", r.value);
  }, [r.value]), reactExports.createElement(Primitive.input, { ref: o, ...u2, "cmdk-input": "", autoComplete: "off", autoCorrect: "off", spellCheck: false, "aria-autocomplete": "list", role: "combobox", "aria-expanded": true, "aria-controls": v.listId, "aria-labelledby": v.labelId, "aria-activedescendant": b, id: v.inputId, type: "text", value: c ? r.value : f, onChange: (l) => {
    c || d.setState("search", l.target.value), t == null || t(l.target.value);
  } });
}), xe = reactExports.forwardRef((r, o) => {
  let { children: t, label: u2 = "Suggestions", ...c } = r, d = reactExports.useRef(null), f = reactExports.useRef(null), p2 = K();
  return reactExports.useEffect(() => {
    if (f.current && d.current) {
      let v = f.current, b = d.current, l, y = new ResizeObserver(() => {
        l = requestAnimationFrame(() => {
          let E = v.offsetHeight;
          b.style.setProperty("--cmdk-list-height", E.toFixed(1) + "px");
        });
      });
      return y.observe(v), () => {
        cancelAnimationFrame(l), y.unobserve(v);
      };
    }
  }, []), reactExports.createElement(Primitive.div, { ref: G([d, o]), ...c, "cmdk-list": "", role: "listbox", "aria-label": u2, id: p2.listId }, j(r, (v) => reactExports.createElement("div", { ref: G([f, p2.listInnerRef]), "cmdk-list-sizer": "" }, v)));
}), Pe = reactExports.forwardRef((r, o) => {
  let { open: t, onOpenChange: u2, overlayClassName: c, contentClassName: d, container: f, ...p2 } = r;
  return reactExports.createElement(Root, { open: t, onOpenChange: u2 }, reactExports.createElement(Portal, { container: f }, reactExports.createElement(Overlay, { "cmdk-overlay": "", className: c }), reactExports.createElement(Content, { "aria-label": r.label, "cmdk-dialog": "", className: d }, reactExports.createElement(me, { ref: o, ...p2 }))));
}), we = reactExports.forwardRef((r, o) => T((u2) => u2.filtered.count === 0) ? reactExports.createElement(Primitive.div, { ref: o, ...r, "cmdk-empty": "", role: "presentation" }) : null), De = reactExports.forwardRef((r, o) => {
  let { progress: t, children: u2, label: c = "Loading...", ...d } = r;
  return reactExports.createElement(Primitive.div, { ref: o, ...d, "cmdk-loading": "", role: "progressbar", "aria-valuenow": t, "aria-valuemin": 0, "aria-valuemax": 100, "aria-label": c }, j(r, (f) => reactExports.createElement("div", { "aria-hidden": true }, f)));
}), Ve = Object.assign(me, { List: xe, Item: ye, Input: Ce, Group: Se, Separator: Ee, Dialog: Pe, Empty: we, Loading: De });
function Ie(r, o) {
  let t = r.nextElementSibling;
  for (; t; ) {
    if (t.matches(o)) return t;
    t = t.nextElementSibling;
  }
}
function Me(r, o) {
  let t = r.previousElementSibling;
  for (; t; ) {
    if (t.matches(o)) return t;
    t = t.previousElementSibling;
  }
}
function pe(r) {
  let o = reactExports.useRef(r);
  return M(() => {
    o.current = r;
  }), o;
}
var M = typeof window == "undefined" ? reactExports.useEffect : reactExports.useLayoutEffect;
function k(r) {
  let o = reactExports.useRef();
  return o.current === void 0 && (o.current = r()), o;
}
function G(r) {
  return (o) => {
    r.forEach((t) => {
      typeof t == "function" ? t(o) : t != null && (t.current = o);
    });
  };
}
function T(r) {
  let o = ee(), t = () => r(o.snapshot());
  return shimExports.useSyncExternalStore(o.subscribe, t, t);
}
function ve(r, o, t, u2 = []) {
  let c = reactExports.useRef(), d = K();
  return M(() => {
    var v;
    let f = (() => {
      var b;
      for (let l of t) {
        if (typeof l == "string") return l.trim();
        if (typeof l == "object" && "current" in l) return l.current ? (b = l.current.textContent) == null ? void 0 : b.trim() : c.current;
      }
    })(), p2 = u2.map((b) => b.trim());
    d.value(r, f, p2), (v = o.current) == null || v.setAttribute(I, f), c.current = f;
  }), c;
}
var Te = () => {
  let [r, o] = reactExports.useState(), t = k(() => /* @__PURE__ */ new Map());
  return M(() => {
    t.current.forEach((u2) => u2()), t.current = /* @__PURE__ */ new Map();
  }, [r]), (u2, c) => {
    t.current.set(u2, c), o({});
  };
};
function ke(r) {
  let o = r.type;
  return typeof o == "function" ? o(r.props) : "render" in o ? o.render(r.props) : r;
}
function j({ asChild: r, children: o }, t) {
  return r && reactExports.isValidElement(o) ? reactExports.cloneElement(ke(o), { ref: o.ref }, t(o.props.children)) : t(o);
}
var Le = { position: "absolute", width: "1px", height: "1px", padding: "0", margin: "-1px", overflow: "hidden", clip: "rect(0, 0, 0, 0)", whiteSpace: "nowrap", borderWidth: "0" };
const Command = reactExports.forwardRef(({ className, ...props }, ref) => /* @__PURE__ */ jsxRuntimeExports.jsx(
  Ve,
  {
    ref,
    className: cn(
      "flex h-full w-full flex-col overflow-hidden rounded-md bg-popover text-popover-foreground",
      className
    ),
    ...props
  }
));
Command.displayName = Ve.displayName;
const CommandInput = reactExports.forwardRef(({ className, ...props }, ref) => /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center border-b px-3", "cmdk-input-wrapper": "", children: [
  /* @__PURE__ */ jsxRuntimeExports.jsx(Search, { className: "mr-2 h-4 w-4 shrink-0 opacity-50" }),
  /* @__PURE__ */ jsxRuntimeExports.jsx(
    Ve.Input,
    {
      ref,
      className: cn(
        "flex h-11 w-full rounded-md bg-transparent py-3 text-sm outline-none placeholder:text-muted-foreground disabled:cursor-not-allowed disabled:opacity-50",
        className
      ),
      ...props
    }
  )
] }));
CommandInput.displayName = Ve.Input.displayName;
const CommandList = reactExports.forwardRef(({ className, ...props }, ref) => /* @__PURE__ */ jsxRuntimeExports.jsx(
  Ve.List,
  {
    ref,
    className: cn("max-h-[300px] overflow-y-auto overflow-x-hidden", className),
    ...props
  }
));
CommandList.displayName = Ve.List.displayName;
const CommandEmpty = reactExports.forwardRef((props, ref) => /* @__PURE__ */ jsxRuntimeExports.jsx(
  Ve.Empty,
  {
    ref,
    className: "py-6 text-center text-sm",
    ...props
  }
));
CommandEmpty.displayName = Ve.Empty.displayName;
const CommandGroup = reactExports.forwardRef(({ className, ...props }, ref) => /* @__PURE__ */ jsxRuntimeExports.jsx(
  Ve.Group,
  {
    ref,
    className: cn(
      "overflow-hidden p-1 text-foreground [&_[cmdk-group-heading]]:px-2 [&_[cmdk-group-heading]]:py-1.5 [&_[cmdk-group-heading]]:text-xs [&_[cmdk-group-heading]]:font-medium [&_[cmdk-group-heading]]:text-muted-foreground",
      className
    ),
    ...props
  }
));
CommandGroup.displayName = Ve.Group.displayName;
const CommandSeparator = reactExports.forwardRef(({ className, ...props }, ref) => /* @__PURE__ */ jsxRuntimeExports.jsx(
  Ve.Separator,
  {
    ref,
    className: cn("-mx-1 h-px bg-border", className),
    ...props
  }
));
CommandSeparator.displayName = Ve.Separator.displayName;
const CommandItem = reactExports.forwardRef(({ className, ...props }, ref) => /* @__PURE__ */ jsxRuntimeExports.jsx(
  Ve.Item,
  {
    ref,
    className: cn(
      "relative flex cursor-default gap-2 select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none data-[disabled=true]:pointer-events-none data-[selected='true']:bg-accent data-[selected=true]:text-accent-foreground data-[disabled=true]:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",
      className
    ),
    ...props
  }
));
CommandItem.displayName = Ve.Item.displayName;
function SearchableSelect({
  items,
  selectedItems,
  onSearch,
  onToggleItem,
  onLoadMore,
  hasMore,
  isLoading,
  getItemId,
  getItemLabel,
  placeholder = "Select items...",
  searchPlaceholder = "Search items...",
  emptyMessage = "No items found.",
  loadMoreText = "Load More",
  loadingText = "Loading...",
  className
}) {
  const [open, setOpen] = reactExports.useState(false);
  const [searchTerm, setSearchTerm] = reactExports.useState("");
  const searchTimeoutRef = reactExports.useRef();
  const initialLoadRef = reactExports.useRef(false);
  const handleOpenChange = reactExports.useCallback((newOpen) => {
    setOpen(newOpen);
    if (newOpen && !initialLoadRef.current) {
      initialLoadRef.current = true;
      onSearch("");
    }
    if (!newOpen) {
      setSearchTerm("");
      initialLoadRef.current = false;
    }
  }, [onSearch]);
  const handleSearch = reactExports.useCallback((value) => {
    setSearchTerm(value);
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }
    searchTimeoutRef.current = setTimeout(() => {
      onSearch(value);
    }, 300);
  }, [onSearch]);
  reactExports.useEffect(() => {
    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
    };
  }, []);
  return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className, children: [
    /* @__PURE__ */ jsxRuntimeExports.jsxs(Popover, { open, onOpenChange: handleOpenChange, children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx(PopoverTrigger, { asChild: true, children: /* @__PURE__ */ jsxRuntimeExports.jsxs(
        Button,
        {
          variant: "outline",
          role: "combobox",
          className: "w-full justify-between",
          "aria-expanded": open,
          children: [
            selectedItems.length > 0 ? `${selectedItems.length} items selected` : placeholder,
            /* @__PURE__ */ jsxRuntimeExports.jsx(ChevronsUpDown, { className: "ml-2 h-4 w-4 shrink-0 opacity-50" })
          ]
        }
      ) }),
      /* @__PURE__ */ jsxRuntimeExports.jsx(
        PopoverContent,
        {
          className: "w-full p-0",
          align: "start",
          onWheel: (e) => e.stopPropagation(),
          onTouchMove: (e) => e.stopPropagation(),
          children: /* @__PURE__ */ jsxRuntimeExports.jsxs(Command, { shouldFilter: false, children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx(
              CommandInput,
              {
                placeholder: searchPlaceholder,
                value: searchTerm,
                onValueChange: handleSearch
              }
            ),
            /* @__PURE__ */ jsxRuntimeExports.jsx(CommandGroup, { className: "max-h-[300px] overflow-y-scroll", children: isLoading ? /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "p-4 text-center text-sm text-muted-foreground", children: loadingText }) : items.length > 0 ? items.map((item) => /* @__PURE__ */ jsxRuntimeExports.jsx(
              CommandItem,
              {
                value: String(getItemId(item)),
                onSelect: () => onToggleItem(item),
                children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center gap-2", children: [
                  /* @__PURE__ */ jsxRuntimeExports.jsx(
                    Check,
                    {
                      className: cn(
                        "h-4 w-4",
                        selectedItems.some(
                          (selected) => getItemId(selected) === getItemId(item)
                        ) ? "opacity-100" : "opacity-0"
                      )
                    }
                  ),
                  /* @__PURE__ */ jsxRuntimeExports.jsx("span", { children: getItemLabel(item) })
                ] })
              },
              getItemId(item)
            )) : /* @__PURE__ */ jsxRuntimeExports.jsx(CommandEmpty, { children: emptyMessage }) }),
            hasMore && items.length > 0 && !isLoading && /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "p-2 text-center", children: /* @__PURE__ */ jsxRuntimeExports.jsx(Button, { variant: "ghost", size: "sm", onClick: onLoadMore, children: loadMoreText }) })
          ] })
        }
      )
    ] }),
    selectedItems.length > 0 && /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "flex flex-wrap gap-2 mt-2", children: selectedItems.map((item) => /* @__PURE__ */ jsxRuntimeExports.jsxs(
      Badge,
      {
        variant: "secondary",
        className: "flex items-center gap-1",
        children: [
          getItemLabel(item),
          /* @__PURE__ */ jsxRuntimeExports.jsx(
            X$1,
            {
              className: "h-3 w-3 cursor-pointer",
              onClick: (e) => {
                e.stopPropagation();
                onToggleItem(item);
              }
            }
          )
        ]
      },
      getItemId(item)
    )) })
  ] });
}
function SendMessageDialog({
  template,
  customerGroups,
  showItems = false,
  showCustomers = true,
  showVariables = false,
  onClose,
  onSend
}) {
  var _a;
  const itemsFetcher = useFetcher();
  const customersFetcher = useFetcher();
  const [step, setStep] = reactExports.useState("targeting");
  const [variables, setVariables] = reactExports.useState(
    () => Object.fromEntries(template.variables.map((v) => [v, ""]))
  );
  const [selectedGroup, setSelectedGroup] = reactExports.useState(((_a = customerGroups[0]) == null ? void 0 : _a.type) || "All");
  const [selectedItems, setSelectedItems] = reactExports.useState([]);
  const [selectedCustomers, setSelectedCustomers] = reactExports.useState([]);
  const [items, setItems] = reactExports.useState([]);
  const [customers, setCustomers] = reactExports.useState([]);
  const [hasMoreItems, setHasMoreItems] = reactExports.useState(true);
  const [hasMoreCustomers, setHasMoreCustomers] = reactExports.useState(true);
  const [itemPagination, setItemPagination] = reactExports.useState({
    search: "",
    page: 1,
    pageSize: 10
  });
  const [customerPagination, setCustomerPagination] = reactExports.useState({
    search: "",
    page: 1,
    pageSize: 10
  });
  reactExports.useEffect(() => {
    var _a2;
    if ((_a2 = itemsFetcher.data) == null ? void 0 : _a2.data) {
      const newItems = itemsFetcher.data.data;
      if (itemPagination.page > 1) {
        setItems((prev) => {
          const existingIds = new Set(prev.map((item) => item.Id));
          const filteredNewItems = newItems.filter((item) => !existingIds.has(item.Id));
          return [...prev, ...filteredNewItems];
        });
      } else {
        setItems(newItems);
      }
      setHasMoreItems(newItems.length === itemPagination.pageSize);
    }
  }, [itemsFetcher.data, itemPagination.page]);
  reactExports.useEffect(() => {
    var _a2;
    if ((_a2 = customersFetcher.data) == null ? void 0 : _a2.data) {
      const newCustomers = customersFetcher.data.data;
      if (customerPagination.page > 1) {
        setCustomers((prev) => {
          const existingIds = new Set(prev.map((customer) => customer.buyerId));
          const filteredNewCustomers = newCustomers.filter((customer) => !existingIds.has(customer.buyerId));
          return [...prev, ...filteredNewCustomers];
        });
      } else {
        setCustomers(newCustomers);
      }
      setHasMoreCustomers(newCustomers.length === customerPagination.pageSize);
    }
  }, [customersFetcher.data, customerPagination.page]);
  const onSearchItems = reactExports.useCallback((params) => {
    const searchParams = new URLSearchParams();
    searchParams.set("searchType", "items");
    if (params.search) searchParams.set("search", params.search);
    if (params.page) searchParams.set("page", String(params.page));
    if (params.pageSize) searchParams.set("pageSize", String(params.pageSize));
    itemsFetcher.load(`?${searchParams.toString()}`);
  }, [itemsFetcher]);
  const onSearchCustomers = reactExports.useCallback((params) => {
    const searchParams = new URLSearchParams();
    searchParams.set("searchType", "customers");
    if (params.search) searchParams.set("search", params.search);
    if (params.page) searchParams.set("page", String(params.page));
    if (params.pageSize) searchParams.set("pageSize", String(params.pageSize));
    customersFetcher.load(`?${searchParams.toString()}`);
  }, [customersFetcher]);
  const handleLoadMoreItems = reactExports.useCallback(() => {
    const nextPage = itemPagination.page + 1;
    setItemPagination((prev) => ({ ...prev, page: nextPage }));
    onSearchItems({
      ...itemPagination,
      page: nextPage
    });
  }, [itemPagination, onSearchItems]);
  const handleLoadMoreCustomers = reactExports.useCallback(() => {
    const nextPage = customerPagination.page + 1;
    setCustomerPagination((prev) => ({ ...prev, page: nextPage }));
    onSearchCustomers({
      ...customerPagination,
      page: nextPage
    });
  }, [customerPagination, onSearchCustomers]);
  const handleItemSearch = reactExports.useCallback(async (search) => {
    const newPagination = {
      search,
      page: 1,
      pageSize: 10
    };
    setItemPagination(newPagination);
    onSearchItems(newPagination);
  }, [onSearchItems]);
  const handleCustomerSearch = reactExports.useCallback(async (search) => {
    const newPagination = {
      search,
      page: 1,
      pageSize: 10
    };
    setCustomerPagination(newPagination);
    onSearchCustomers(newPagination);
  }, [onSearchCustomers]);
  const handleVariableChange = (name, value) => {
    setVariables((prev) => ({ ...prev, [name]: value }));
  };
  const handleNext = () => {
    if (step === "variables") {
      setStep("targeting");
    } else if (step === "targeting") {
      setStep("confirm");
    }
  };
  const handleBack = () => {
    if (step === "targeting") {
      setStep("variables");
    } else if (step === "confirm") {
      setStep("targeting");
    }
  };
  const handleSend = () => {
    onSend({
      id: template.id,
      templateId: template.templateId,
      variables,
      selectedGroup,
      selectedItems: selectedItems.map((item) => item.Id),
      selectedCustomers: selectedCustomers.map((customer) => customer.buyerId)
    });
    onClose();
  };
  const toggleItem = reactExports.useCallback((item) => {
    setSelectedItems((prev) => {
      const exists = prev.some((i) => i.Id === item.Id);
      return exists ? prev.filter((i) => i.Id !== item.Id) : [...prev, item];
    });
  }, []);
  const toggleCustomer = reactExports.useCallback((customer) => {
    setSelectedCustomers((prev) => {
      const exists = prev.some((c) => c.buyerId === customer.buyerId);
      return exists ? prev.filter((c) => c.buyerId !== customer.buyerId) : [...prev, customer];
    });
  }, []);
  const isNextDisabled = () => {
    if (step === "variables") {
      return template.variables.some((v) => !variables[v]);
    }
    if (step === "targeting") {
      if (selectedGroup === "Custom") {
        return selectedCustomers.length === 0;
      }
      return !selectedGroup;
    }
    return false;
  };
  const renderTargetingStep = () => /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "space-y-6", children: [
    showItems && /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "space-y-2", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx(Label, { children: "Select Items" }),
      /* @__PURE__ */ jsxRuntimeExports.jsx(
        SearchableSelect,
        {
          items,
          selectedItems,
          onSearch: handleItemSearch,
          onToggleItem: toggleItem,
          onLoadMore: handleLoadMoreItems,
          hasMore: hasMoreItems,
          isLoading: itemsFetcher.state === "loading",
          getItemId: (item) => item.Id,
          getItemLabel: (item) => item.name,
          placeholder: "Select items...",
          searchPlaceholder: "Search items...",
          emptyMessage: "No items found.",
          loadMoreText: "Load More Items"
        }
      )
    ] }),
    showCustomers && /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "space-y-2", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx(Label, { children: "Select Customer Group" }),
      /* @__PURE__ */ jsxRuntimeExports.jsxs(Select, { value: selectedGroup, onValueChange: setSelectedGroup, children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx(SelectTrigger, { children: /* @__PURE__ */ jsxRuntimeExports.jsx(SelectValue, { placeholder: "Select a group" }) }),
        /* @__PURE__ */ jsxRuntimeExports.jsx(SelectContent, { children: customerGroups.map((group) => /* @__PURE__ */ jsxRuntimeExports.jsxs(SelectItem, { value: group.type, children: [
          group.name,
          " (",
          group.customerCount,
          " customers)"
        ] }, group.type)) })
      ] })
    ] }),
    selectedGroup === "Custom" && /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "space-y-2", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx(Label, { children: "Select Customers" }),
      /* @__PURE__ */ jsxRuntimeExports.jsx(
        SearchableSelect,
        {
          items: customers,
          selectedItems: selectedCustomers,
          onSearch: handleCustomerSearch,
          onToggleItem: toggleCustomer,
          onLoadMore: handleLoadMoreCustomers,
          hasMore: hasMoreCustomers,
          isLoading: customersFetcher.state === "loading",
          getItemId: (customer) => customer.buyerId,
          getItemLabel: (customer) => customer.buyerName,
          placeholder: "Search customers...",
          searchPlaceholder: "Search customers...",
          emptyMessage: "No customers found.",
          loadMoreText: "Load More Customers"
        }
      )
    ] })
  ] });
  const renderStep = () => {
    var _a2;
    switch (step) {
      case "variables":
        return /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "space-y-4", children: template.variables.map((variable) => /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "space-y-2", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx(Label, { children: variable }),
          /* @__PURE__ */ jsxRuntimeExports.jsx(
            Input,
            {
              value: variables[variable] || "",
              onChange: (e) => handleVariableChange(variable, e.target.value),
              placeholder: `Enter ${variable}`
            }
          )
        ] }, variable)) });
      case "targeting":
        return renderTargetingStep();
      case "confirm":
      default:
        return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "space-y-4", children: [
          showVariables && Object.keys(variables).length > 0 && /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "space-y-2", children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx("h3", { className: "font-medium", children: "Variables" }),
            Object.entries(variables).map(([key, value]) => /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex justify-between", children: [
              /* @__PURE__ */ jsxRuntimeExports.jsxs("span", { className: "text-muted-foreground", children: [
                key,
                ":"
              ] }),
              /* @__PURE__ */ jsxRuntimeExports.jsx("span", { children: value })
            ] }, key))
          ] }),
          /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "space-y-2", children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx("h3", { className: "font-medium", children: "Target Audience" }),
            /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex justify-between", children: [
              /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "text-muted-foreground", children: "Group:" }),
              /* @__PURE__ */ jsxRuntimeExports.jsx("span", { children: selectedGroup === "Custom" ? `Custom (${selectedCustomers.length} customers)` : ((_a2 = customerGroups.find((g) => g.type === selectedGroup)) == null ? void 0 : _a2.name) || "" })
            ] }),
            selectedItems.length > 0 && /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex justify-between", children: [
              /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "text-muted-foreground", children: "Items:" }),
              /* @__PURE__ */ jsxRuntimeExports.jsxs("span", { children: [
                selectedItems.length,
                " selected"
              ] })
            ] })
          ] })
        ] });
    }
  };
  return /* @__PURE__ */ jsxRuntimeExports.jsx(Dialog, { open: true, onOpenChange: onClose, children: /* @__PURE__ */ jsxRuntimeExports.jsxs(DialogContent, { className: "max-w-2xl rounded-md", children: [
    /* @__PURE__ */ jsxRuntimeExports.jsx(DialogHeader, { children: /* @__PURE__ */ jsxRuntimeExports.jsx(DialogTitle, { children: step === "variables" ? "Fill in Variables" : step === "targeting" ? "Select Target Audience" : "Confirm Message" }) }),
    renderStep(),
    /* @__PURE__ */ jsxRuntimeExports.jsxs(DialogFooter, { children: [
      step !== "targeting" && /* @__PURE__ */ jsxRuntimeExports.jsx(Button, { variant: "outline", onClick: handleBack, children: "Back" }),
      step === "confirm" ? /* @__PURE__ */ jsxRuntimeExports.jsx(Button, { onClick: handleSend, children: "Send Message" }) : /* @__PURE__ */ jsxRuntimeExports.jsx(Button, { onClick: handleNext, disabled: isNextDisabled(), children: "Next" })
    ] })
  ] }) });
}
function Templates() {
  const {
    templates,
    customerGroups
  } = useLoaderData();
  const [searchParams, setSearchParams] = useSearchParams();
  const [selectedTemplate, setSelectedTemplate] = reactExports.useState(null);
  const submit = useSubmit();
  const handleSearch = (value) => {
    const params = new URLSearchParams(searchParams);
    if (value) {
      params.set("search", value);
    } else {
      params.delete("search");
    }
    params.set("page", "1");
    setSearchParams(params);
  };
  const handleSendMessage = (template) => {
    setSelectedTemplate(template);
  };
  const handleSend = async (data) => {
    var _a, _b;
    const formData = new FormData();
    formData.set("id", ((_a = data.id) == null ? void 0 : _a.toString()) || "");
    formData.set("templateId", ((_b = data.templateId) == null ? void 0 : _b.toString()) || "");
    formData.set("variables", JSON.stringify(data.variables));
    formData.set("selectedGroup", data.selectedGroup);
    if (data.selectedItems) {
      formData.set("selectedItems", JSON.stringify(data.selectedItems));
    }
    if (data.selectedCustomers) {
      formData.set("selectedCustomers", JSON.stringify(data.selectedCustomers));
    }
    submit(formData, {
      method: "post"
    });
    setSelectedTemplate(null);
  };
  return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
    children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
      className: "relative mb-4",
      children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Search, {
        className: "absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground"
      }), /* @__PURE__ */ jsxRuntimeExports.jsx(Input, {
        placeholder: "Search templates...",
        className: "pl-10",
        value: searchParams.get("search") || "",
        onChange: (e) => handleSearch(e.target.value)
      })]
    }), /* @__PURE__ */ jsxRuntimeExports.jsx(ScrollArea, {
      className: "h-[calc(100vh-100px)]",
      children: /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
        className: "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",
        children: templates.map((template) => /* @__PURE__ */ jsxRuntimeExports.jsx(TemplateCard, {
          template,
          onSendMessage: handleSendMessage
        }, template.name))
      })
    }), selectedTemplate && /* @__PURE__ */ jsxRuntimeExports.jsx(SendMessageDialog, {
      template: selectedTemplate,
      showItems: selectedTemplate.variables.length > 0 && selectedTemplate.variables.includes("Item_Name"),
      showCustomers: true,
      showVariables: false,
      customerGroups,
      onClose: () => setSelectedTemplate(null),
      onSend: handleSend
    })]
  });
}
export {
  Templates as default
};
//# sourceMappingURL=seller.marketing.templates-F4DKlODm.js.map
