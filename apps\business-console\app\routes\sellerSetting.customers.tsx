import type React from "react"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "../components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "../components/ui/card"
import { Input } from "../components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../components/ui/select"
import { Phone, MapPin, User, ArrowUp, ArrowDown, Info, Pencil, Save, X, Search, Filter, CalendarIcon } from "lucide-react"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "../components/ui/dialog"
import { json, ActionFunction, LoaderFunction } from "@remix-run/node"
import { useFetcher, useLoaderData, useSearchParams } from "@remix-run/react"
import { withAuth, withResponse } from "../utils/auth-utils"
import { useToast } from "../hooks/use-toast"
import { <PERSON>Rang<PERSON> } from "react-day-picker"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "../components/ui/tabs"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "../components/ui/table"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "../components/ui/tooltip"
import SpinnerLoader from "../components/loader/SpinnerLoader"
import { Popover, PopoverContent, PopoverTrigger, PopoverClose } from "../components/ui/popover"
import { cn } from "../lib/utils"
import { format } from "date-fns"
import { Calendar } from "../components/ui/calendar"
// Customer Analysis Components
import ConversionRate from "../components/common/ConversionRate";
import CustomersAcquisition from "../components/common/CustomerAcquisation";
import CustomerSales from "../components/common/CustomerSales";
import { getAcquiSitionRates, getConversionRate, getSellerSales } from "../services/buyerSetting";
import { getBuyerSummary, updateFbDiscountPrice } from "../services/businessConsoleService";
import { CustomerAcquisition, CustomerConversionRate, SellerSales } from "../types/api/businessConsoleService/BuyerAccountingResponse";
import { BuyerSummaryDetailsResponseItem } from "../types/api/businessConsoleService/BuyerSummaryDetailsResponseItem";

type ActionIntent = "Customer Overview" | "Customer List" | "Customer Loyalty" | "UpdateFbDiscount";

export interface BuyerDetails {
  buyerId: number;
  buyerName: string;
  ownerName: string;
  address: string;
  mobileNumber: string;
  totalOrders: number;
  totalAmount: number;
  pendingAmount: number;
  lastOrderedDate: string;
}

interface LoaderData {
  sales: SellerSales,
  customerConversionRates: CustomerConversionRate,
  customerAcquisitionRate: CustomerAcquisition,
  week: number,
  // Customer List Data
  customers: BuyerSummaryDetailsResponseItem[];
  currentPage: number;
  hasNextPage: boolean;
  hasMoreData: boolean;
  tabValue: string;
  sortByvalue: string;
  searchBy: string;
  sortByOrder: string;
}

interface ActionData {
  intent: ActionIntent;
  errorMessage: string;
  success: boolean;
  data: { customers: BuyerDetails[], totalElements: number, pageSize: number, currentPage: number };
}

export const loader: LoaderFunction = withAuth(async ({ request, user }) => {
  try {
    const week = 4;
    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get("page") || "0");
    const tabValue = url.searchParams.get("tabValue") || "all";
    const sortBy = url.searchParams.get("sortBy") || "buyerName";
    const searchBy = url.searchParams.get("searchBy") || "";
    const sortByOrder = url.searchParams.get("sortByOrder") || "asc";
    const validSearchBy = searchBy && searchBy.length >= 3 ? searchBy : "";
    const pageSize = 50;

    // Make parallel API calls
    const [salesResponse, conversionResponse, acquisitionResponse, customersResponse] = await Promise.all([
      getSellerSales(week, request),
      getConversionRate(3, request),
      getAcquiSitionRates(week, request),
      getBuyerSummary(user.userId, page, pageSize, tabValue, sortBy, validSearchBy, sortByOrder, request)
    ]);

    const hasNextPage = (customersResponse.data?.length ?? 0) >= pageSize;
    const nextPageResponse = await getBuyerSummary(user.userId, page + 1, pageSize, tabValue, sortBy, validSearchBy, sortByOrder, request);
    const hasMoreData = (nextPageResponse.data ?? []).length > 0;

    const responseHeaders = new Headers();
    [salesResponse, conversionResponse, acquisitionResponse, customersResponse].forEach(response => {
      if (response.headers?.has('Set-Cookie')) {
        responseHeaders.set('Set-Cookie', response.headers.get('Set-Cookie')!);
      }
    });

    return withResponse({
      sales: salesResponse.data,
      customerConversionRates: conversionResponse.data,
      customerAcquisitionRate: acquisitionResponse.data,
      week: week,
      // Customer List Data
      customers: customersResponse.data,
      currentPage: page,
      hasNextPage,
      hasMoreData,
      tabValue,
      sortByvalue: sortBy,
      searchBy,
      sortByOrder
    }, responseHeaders);
  }
  catch (error) {
    throw new Response("Failed to fetch customer analysis data", {
      status: 500,
      statusText: error instanceof Error ? error.message : "Unknown error"
    });
  }
});

export const action: ActionFunction = withAuth(async ({ request, user }) => {
  const formData = await request.formData();
  const intent = formData.get("intent") as ActionIntent;

  if (!intent) {
    return json({ success: false, errorMessage: "Invalid request", intent: intent }, { status: 400 });
  }

  if (!user.sellerId) {
    return json({ success: false, errorMessage: "Seller ID not found", intent: intent }, { status: 400 });
  }

  if (intent === "UpdateFbDiscount") {
    const nBuyerId = Number(formData.get("nBuyerId"))
    const fBDiscount = Number(formData.get("fbDiscount"))

    try {
      const response = await updateFbDiscountPrice(nBuyerId, fBDiscount, request);
      return withResponse({ success: true, intent: intent, data: response?.data }, response?.headers);
    }
    catch (error) {
      return json({ success: false, intent: intent, errorMessage: "Failed to update discount" }, { status: 400 })
    }
  }

  return json({ success: false, intent: intent, errorMessage: "Invalid intent" }, { status: 400 });
});


export default function SellerCustomers() {
  const { sales, customerConversionRates, customerAcquisitionRate, week, customers, currentPage, tabValue, sortByvalue, searchBy, sortByOrder } = useLoaderData<LoaderData>();
  const [selectedCustomer, setSelectedCustomer] = useState<BuyerDetails | null>(null)
  const [activeTab, setActiveTab] = useState<"Customer Overview" | "Customer List" | "Customer Loyalty">("Customer Overview")
  const fetcher = useFetcher<ActionData>()
  const [searchParams, setSearchParams] = useSearchParams()

  // action
  const [actionType, setActionType] = useState<string>("")
  const [actionSelectedCustomer, setActionSelectedCustomer] = useState<BuyerDetails | null>(null)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const { toast } = useToast()

  // Search and Filters
  const [searchType, setSearchType] = useState<"ID" | "Mobile" | "Name">("ID")
  const [searchTerm, setSearchTerm] = useState<string>("")
  const [filterDate, setFilterDate] = useState<DateRange | undefined>(undefined);
  const [pageSize, setPageSize] = useState(20)

  // local state
  const [dateRange, setDateRange] = useState<DateRange | undefined>(undefined);

  // Customer List specific state
  const [customerListSearchTerm, setCustomerListSearchTerm] = useState(searchBy)
  const [customerListSortBy, setCustomerListSortBy] = useState(sortByvalue)
  const [customerListSortOrder, setCustomerListSortOrder] = useState(sortByOrder)
  const [customerListActiveTab, setCustomerListActiveTab] = useState(tabValue)
  const [fbDiscounts, setFbDiscounts] = useState<{ [key: number]: string }>({});
  const [isFbDis, setIsFbDis] = useState<{ [key: number]: boolean }>({});

  // debounce on search term
  useEffect(() => {
    const timer = setTimeout(() => {
      // This effect is now used for the search functionality
    }, 500);

    return () => {
      clearTimeout(timer);
    };
  }, [searchTerm]);

  // Navigation function to update URL params and trigger reload
  const updateCustomerListParams = (params: Record<string, string>) => {
    const newSearchParams = new URLSearchParams(searchParams);
    Object.entries(params).forEach(([key, value]) => {
      if (value) {
        newSearchParams.set(key, value);
      } else {
        newSearchParams.delete(key);
      }
    });
    setSearchParams(newSearchParams);
  };

  const handleTabChange = (newTab: string) => {
    setSearchType("ID")
    setSearchTerm("")
    setFilterDate(undefined)
    setDateRange(undefined)
    setPageSize(20)
    setActiveTab(newTab as "Customer Overview" | "Customer List" | "Customer Loyalty")
  }

  const searchTypeFilters = [
    { label: "Customer ID", value: "ID" },
    { label: "Customer Mobile", value: "Mobile" },
    { label: "Customer Name", value: "Name" },
  ]

  const handleAction = (customer: BuyerDetails, action: string) => {
    setActionSelectedCustomer(customer)
    setActionType(action)
  }

  const handleSubmitAction = (formData: Record<string, unknown>) => {
    const actionData = new FormData();
    actionData.append("intent", actionType);
    actionData.append("data", JSON.stringify({ customer: actionSelectedCustomer, formData }))
    fetcher.submit(actionData, { method: "post" })
    setIsSubmitting(true)
  }

  // Customer List specific handlers
  const handleCustomerListTabChange = (newTab: string) => {
    const tabMap: { [key: string]: string } = {
      all: "all",
      oneOrder: "one_order",
      frequent: "frequent_orders",
      zero_orders: "zero_orders",
    };
    const validTabValue = tabMap[newTab] || "all";
    setCustomerListActiveTab(newTab);

    // Update URL params to trigger API call
    updateCustomerListParams({
      tabValue: validTabValue,
      page: "0", // Reset to first page when changing tabs
      sortBy: customerListSortBy,
      sortByOrder: customerListSortOrder,
      searchBy: customerListSearchTerm
    });
  };

  const handleCustomerListSort = (newSort: string) => {
    setCustomerListSortBy((prevSortBy) => {
      const isSameSort = prevSortBy === newSort;
      setCustomerListSortOrder((prevSortOrder) => {
        const newOrder = isSameSort && prevSortOrder === "asc" ? "desc" : "asc";

        // Update URL params to trigger API call
        updateCustomerListParams({
          tabValue: customerListActiveTab,
          page: "0", // Reset to first page when sorting
          sortBy: newSort,
          sortByOrder: newOrder,
          searchBy: customerListSearchTerm
        });

        return newOrder;
      });
      return newSort;
    });
  };

  const getSortIcon = (column: string) => {
    if (customerListSortBy !== column) return null;
    return customerListSortOrder === "asc" ? <ArrowUp className="w-4 h-4 ml-1" /> : <ArrowDown className="w-4 h-4 ml-1" />;
  };

  const handleCustomerListSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setCustomerListSearchTerm(value);

    // Update URL params to trigger API call (with debounce)
    setTimeout(() => {
      updateCustomerListParams({
        tabValue: customerListActiveTab,
        page: "0", // Reset to first page when searching
        sortBy: customerListSortBy,
        sortByOrder: customerListSortOrder,
        searchBy: value.length >= 3 ? value : ""
      });
    }, 300);
  };

  const handleChangeFbDiscount = (nBuyerId: number, val: string) => {
    if (/^\d*\.?\d*$/.test(val) || val === "") {
      setFbDiscounts((prev) => ({ ...prev, [nBuyerId]: val }));
    }
  };

  const fbFetcher = useFetcher<BuyerSummaryDetailsResponseItem>()

  const handleSave = (nBuyerId: number) => {
    const formData = new FormData()
    formData.append("intent", "UpdateFbDiscount");
    formData.append("nBuyerId", nBuyerId.toString());
    formData.append("fbDiscount", fbDiscounts[nBuyerId]);
    fbFetcher.submit(formData, { method: "POST" })
    setIsFbDis((prev) => ({ ...prev, [nBuyerId]: false }));
  };

  // Handle pagination changes
  const handlePageSizeChange = (newPageSize: number) => {
    setPageSize(newPageSize);
    updateCustomerListParams({
      page: "0", // Reset to first page when changing page size
      pageSize: newPageSize.toString()
    });
  };

  const handlePageChange = (newPage: number) => {
    updateCustomerListParams({
      page: newPage.toString()
    });
  };

  useEffect(() => {
    if (fetcher.data?.intent === "UpdateFbDiscount") {
      if (fetcher.data?.success) {
        toast({
          title: "Success",
          description: "Discount updated successfully",
        })
      } else if (fetcher.data?.success === false) {
        toast({
          title: "Error",
          description: fetcher.data?.errorMessage,
          variant: "destructive"
        })
      }
    }
  }, [fetcher.data, toast])

  return (
    <div className="min-h-screen p-6">
      <div className="mx-auto mb-6">

        {/* Header */}
        <div className="mb-4">
          <div className="flex flex-col gap-2 sm:flex-row sm:items-center sm:justify-between">
            <div>
              <h1 className="text-xl md:text-3xl font-bold text-gray-900">Customers</h1>
              <p className="mt-2 text-gray-600">Manage your customer relationships and loyalty programs</p>
            </div>
          </div>
        </div>

        <Tabs value={activeTab} onValueChange={handleTabChange}>
          <TabsList className="w-full h-10 mb-1">
            <TabsTrigger value="Customer Overview" className="w-1/2 h-8 py-1">Customer Overview</TabsTrigger>
            <TabsTrigger value="Customer List" className="w-1/2 h-8 py-1">Customer List</TabsTrigger>
            <TabsTrigger value="Customer Loyalty" className="w-1/2 h-8 py-1">Customer Loyalty</TabsTrigger>
          </TabsList>

          <TabsContent value="Customer Overview">
            <div className="w-full min-h-screen bg-slate-100">
              <main className="p-2">
                <section>
                  <CustomerSales sales={sales} />
                </section>
                <section>
                  <ConversionRate conversionRates={customerConversionRates} week={week} />
                </section>
                <section>
                  <CustomersAcquisition customerAcquisitionRate={customerAcquisitionRate} />
                </section>
              </main>
            </div>
          </TabsContent>

          <TabsContent value="Customer List">
            {/* Search and Filters */}
            {/* <Card className="mb-1 bg-gray-100">
              <CardContent className="p-2">
                <div className="space-y-1">
                  <div className="grid sm:grid-cols-2 lg:grid-cols-4 gap-2">
                    <div className="relative lg:col-span-1">
                      <Select value={searchType} onValueChange={(value: typeof searchType) => setSearchType(value)}>
                        <SelectTrigger>
                          <SelectValue placeholder="Search by" />
                        </SelectTrigger>
                        <SelectContent>
                          {searchTypeFilters.map((filter) => (
                            <SelectItem key={filter.value} value={filter.value}>
                              {filter.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="relative lg:col-span-2">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                      <Input
                        placeholder={searchType === "Name" ? "Search name" : (searchType === "Mobile") ? "Search by Mobile" : "Search by ID"}
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="pl-10"
                      />
                    </div>

                    <div className="relative sm:col-span-2 lg:col-span-1 ">
                      <Popover>
                        <PopoverTrigger asChild>
                          <Button
                            id="filterDate"
                            variant="outline"
                            className={cn(
                              "w-full justify-start text-left font-normal",
                              !filterDate && "text-muted-foreground"
                            )}
                          >
                            <CalendarIcon />
                            {filterDate?.from ? (
                              filterDate.to ? (
                                <>
                                  {format(filterDate.from, "LLL dd, y")} - {format(filterDate.to, "LLL dd, y")}
                                </>
                              ) : (
                                format(filterDate.from, "LLL dd, y")
                              )
                            ) : (
                              <span>Pick a date</span>
                            )}
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0" align="start">
                          <Calendar
                            initialFocus
                            selected={dateRange}
                            mode="range"
                            onSelect={(range: DateRange | undefined) => {
                              if (!range?.from) return;
                              setDateRange({
                                from: range.from,
                                to: range.to || undefined,
                              });
                            }}
                          />
                          <PopoverClose className="w-full">
                            <Button
                              variant="ghost"
                              className="w-full text-blue-500 hover:text-blue-500 justify-center"
                              onClick={() => setFilterDate(dateRange)}
                            >
                              Set
                            </Button>
                          </PopoverClose>
                        </PopoverContent>
                      </Popover>
                    </div>
                  </div>

                 
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-2">
                    <div className="space-y-1 lg:col-start-4 flex items-end">
                      <Button
                        onClick={() => {
                          setSearchType("ID")
                          setSearchTerm("")
                          setFilterDate(undefined)
                          setDateRange(undefined)
                          setPageSize(20)
                        }}
                        className="w-full"
                      >
                        <Filter className="w-4 h-4 mr-2" />
                        Clear Filters
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card> */}

            {/* Customer List Tabs */}
            <Card className="mb-1">
              <CardContent className="p-2">
                <Tabs value={customerListActiveTab} onValueChange={handleCustomerListTabChange} className="mb-4">
                  <TabsList className="w-full h-10 mb-1">
                    <TabsTrigger value="all" className="w-1/2 h-8 py-1"> All Customers</TabsTrigger>
                    <TabsTrigger value="frequent" className="w-1/2 h-8 py-1"> Frequent Customers</TabsTrigger>
                    <TabsTrigger value="oneOrder" className="w-1/2 h-8 py-1"> One Order Customers</TabsTrigger>
                    <TabsTrigger value="zero_orders" className="w-1/2 h-8 py-1"> New Customers</TabsTrigger>
                  </TabsList>
                </Tabs>

                {/* Customer Search */}
                <div className="flex justify-between mb-4">
                  <Input
                    placeholder="Search by name or owner"
                    value={customerListSearchTerm}
                    onChange={handleCustomerListSearch}
                    className="max-w-sm"
                  />
                  <Select value={customerListSortBy} onValueChange={handleCustomerListSort}>
                    <SelectTrigger className="w-[180px]">
                      <SelectValue placeholder="Sort by" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="buyerName">Name</SelectItem>
                      <SelectItem value="totalOrders">Number of Orders</SelectItem>
                      <SelectItem value="pendingAmount">Pending Balance</SelectItem>
                      <SelectItem value="lastOrderedDate">Order Duration Days</SelectItem>
                      <SelectItem value="fbDiscount">F.B.Discount</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Customer Table */}
                <Table>
                  {fbFetcher.state !== "idle" && <SpinnerLoader loading={true} />}
                  <TableHeader>
                    <TableRow>
                      <TableHead className="cursor-pointer" onClick={() => handleCustomerListSort("buyerName")}>
                        <span className='flex flex-row items-center gap-1'>
                          <span>Name</span>
                          <span>{getSortIcon("buyerName")}</span>
                        </span>
                      </TableHead>
                      <TableHead className="cursor-pointer" onClick={() => handleCustomerListSort("totalOrders")}>
                        <span className='flex flex-row items-center gap-1'>
                          <span>Num of Orders</span>
                          {getSortIcon("totalOrders")}
                        </span>
                      </TableHead>
                      <TableHead className="cursor-pointer" onClick={() => handleCustomerListSort("pendingAmount")}>
                        <span className='flex flex-row items-center gap-1'>
                          <span>Pending Balance</span>
                          {getSortIcon("pendingAmount")}
                        </span>
                      </TableHead>
                      <TableHead className="cursor-pointer" onClick={() => handleCustomerListSort("lastOrderedDate")}>
                        <span className='flex flex-row items-center gap-1'>
                          <span>Order Duration Days</span>
                          {getSortIcon("lastOrderedDate")}
                        </span>
                      </TableHead>
                      <TableHead className="cursor-pointer" onClick={() => handleCustomerListSort("fbDiscount")}>
                        <span className='flex flex-row items-center gap-1'><span>Freq Buyer Discount</span> {getSortIcon("fbDiscount")} </span>
                      </TableHead>
                    </TableRow>
                  </TableHeader>

                  <TableBody>
                    {customers.map((customer) => (
                      <TableRow key={customer.buyerId}>
                        <TableCell>
                          <div className='flex flex-row gap-2 items-center'>
                            <div className="text-blue-600 hover:underline cursor-pointer">
                              <div>{customer.buyerName !== "" ? customer.buyerName : "( Name not given )"}</div>
                            </div>
                            <Dialog>
                              <DialogTrigger asChild>
                                <Info size={18} className="cursor-pointer text-gray-600 hover:text-purple-600 transition-all" />
                              </DialogTrigger>
                              <DialogContent className="sm:max-w-fit p-5 rounded-lg">
                                <DialogHeader>
                                  <DialogTitle className="text-lg font-semibold text-gray-800">About Customer</DialogTitle>
                                </DialogHeader>
                                <div className="flex flex-col gap-3 mt-2">
                                  <div className="flex flex-row gap-2">
                                    <span className="text-sm text-gray-600">Buyer Name:</span>
                                    <span className="text-base text-purple-800 font-semibold">{customer.buyerName}</span>
                                  </div>
                                  <div className="flex flex-row gap-2">
                                    <span className="text-sm text-gray-600">Mobile Number:</span>
                                    <span className="text-base text-purple-800 font-semibold">{customer.mobileNumber}</span>
                                  </div>
                                </div>
                              </DialogContent>
                            </Dialog>
                          </div>
                        </TableCell>
                        <TableCell>{customer.totalOrders}</TableCell>
                        <TableCell>₹ {customer.pendingAmount.toLocaleString('en-IN')}</TableCell>
                        <TableCell>
                          {customer.lastOrderedDate
                            ? (isNaN(new Date(customer.lastOrderedDate).getTime())
                              ? '-'
                              : Math.floor((new Date().getTime() - new Date(customer.lastOrderedDate).getTime()) / (1000 * 60 * 60 * 24)) + ' days')
                            : '-'}
                        </TableCell>
                        <TableCell className='flex flex-row items-center gap-2'>
                          {isFbDis[customer.nBuyerId] ? (
                            <div className="flex flex-row justify-center items-center gap-3">
                              <Input
                                value={String(fbDiscounts[customer.nBuyerId] ?? customer.fbDiscount ?? "")}
                                onChange={(e) => {
                                  const val = e.target.value;
                                  if (!/^\d*\.?\d*$/.test(val)) return;
                                  let numVal = parseFloat(val);
                                  if (numVal > 100) numVal = 100;
                                  if (numVal < 0 || isNaN(numVal)) numVal = 0;
                                  handleChangeFbDiscount(customer.nBuyerId, String(numVal));
                                }}
                                disabled={!isFbDis[customer.nBuyerId]}
                                type="number"
                                min="0"
                                max="100"
                                step="0.01"
                              />
                              <Save
                                size={24}
                                onClick={() => handleSave(customer.nBuyerId)}
                                className="cursor-pointer text-green-500"
                              />
                              <X
                                color="red"
                                size={24}
                                className="cursor-pointer text-red-500"
                                onClick={() => setIsFbDis({})}
                              />
                            </div>
                          ) : (
                            <div className='flex flex-row gap-3 items-center justify-center'>
                              <span>{customer.fbDiscount > 0 ? `${customer.fbDiscount} %` : "-"}</span>
                              <Pencil
                                size={15}
                                onClick={() => setIsFbDis({ [customer.nBuyerId]: true })}
                                className="cursor-pointer text-blue-500"
                              />
                            </div>
                          )}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>

                {/* Pagination */}
                <Card className="p-0 mb-1">
                  <CardContent className="px-1.5 py-1">
                    <div className="flex flex-row items-center justify-end gap-1.5">
                      <Select value={pageSize.toString()} onValueChange={(value) => handlePageSizeChange(Number(value))}>
                        <SelectTrigger className="w-[140px] h-[36px]">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="20">20 per Page</SelectItem>
                          <SelectItem value="50">50 per Page</SelectItem>
                          <SelectItem value="100">100 per Page</SelectItem>
                        </SelectContent>
                      </Select>
                      <Select value={currentPage.toString()} onValueChange={(value) => handlePageChange(Number(value))}>
                        <SelectTrigger className="w-[140px] h-[36px]">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {Array.from({ length: Math.ceil((customers.length || 1) / pageSize) }, (_, i) => (
                            <SelectItem key={i} value={i.toString()}>
                              Page {i + 1}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </CardContent>
                </Card>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="Customer Loyalty">
            <div className="flex items-center justify-center min-h-[400px]">
              <Card className="w-full max-w-md">
                <CardHeader className="text-center">
                  <div className="mx-auto mb-4 w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                    <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                    </svg>
                  </div>
                  <CardTitle className="text-2xl">Loyalty Program Coming Soon</CardTitle>
                  <CardDescription className="text-lg">
                    We&apos;re working hard to bring you comprehensive restaurant analytics and insights.
                  </CardDescription>
                </CardHeader>
                <CardContent className="text-center">
                  <p className="text-gray-500 mb-4">
                    Get ready for powerful metrics, real-time data, and actionable insights to grow your business.
                  </p>
                  <div className="inline-flex items-center px-4 py-2 bg-gradient-to-r from-blue-500 to-purple-600 text-black rounded-lg">
                    <span className="mr-2">🚀</span>
                    Launching Soon
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>

        {/* Customer Details Modal */}
        <CustomerDetailsModal customer={selectedCustomer} onClose={() => setSelectedCustomer(null)} onAction={handleAction} />

        {/* Action Modal */}
        <ActionModal
          customer={actionSelectedCustomer}
          actionType={actionType}
          onClose={() => {
            setActionSelectedCustomer(null)
            setActionType("")
          }}
          isSubmitting={isSubmitting}
          onSubmit={handleSubmitAction}
        />
      </div>
    </div >
  )
}


// Customer Details Modal
interface CustomerDetailsModalProps {
  customer: BuyerDetails | null
  onClose: () => void
  onAction: (customer: BuyerDetails, action: string) => void
}

export function CustomerDetailsModal({ customer, onClose }: CustomerDetailsModalProps) {
  if (!customer) return null

  const handlePhoneClick = (phoneNumber: string) => {
    window.open(`tel:${phoneNumber}`, "_self")
  }

  return (
    <Dialog open={!!customer} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto w-[95vw] sm:w-full rounded-md">
        <DialogHeader>
          <DialogTitle className="text-lg sm:text-xl flex items-center gap-2">
            Customer Details - #{customer.buyerId}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <User className="w-4 h-4" />
              <span className="font-medium">{customer.buyerName}</span>
            </div>
            <div className="flex items-center gap-2">
              <Phone className="w-4 h-4" />
              <span>{customer.mobileNumber}</span>
              <Button
                size="sm"
                variant="outline"
                onClick={() => handlePhoneClick(customer.mobileNumber)}
                className="h-6 w-6 p-0"
              >
                <Phone className="w-3 h-3" />
              </Button>
            </div>
            <div className="flex items-center gap-2">
              <MapPin className="w-4 h-4" />
              <span>{customer.address}</span>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-2 pt-4 border-t">
            <Button variant="outline" onClick={onClose}>
              Close
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}


// Action Modal
interface ActionModalProps {
  customer: BuyerDetails | null
  actionType: string
  onClose: () => void
  isSubmitting: boolean
  onSubmit: (formData: Record<string, unknown>) => void
}

export function ActionModal({ customer, actionType, onClose, isSubmitting, onSubmit }: ActionModalProps) {
  const [formData] = useState({
    reason: ""
  })

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    onSubmit(formData)
  }

  if (!customer || !actionType) return null

  return (
    <Dialog open={true} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl rounded-md">
        <DialogHeader>
          <DialogTitle>
            Action Modal
          </DialogTitle>
        </DialogHeader>
        <div className="space-y-4">
          <div className="flex gap-2 pt-4">
            <Button variant="outline" onClick={onClose} disabled={isSubmitting}>
              Cancel
            </Button>
            <Button onClick={handleSubmit} disabled={isSubmitting}>
              {isSubmitting ? "Processing..." : "Confirm"}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}