import { r as reactExports, j as jsxRuntimeExports } from "./jsx-runtime-bB2y7OuD.js";
import { T as Table, a as TableHeader, b as TableRow, c as TableHead, d as TableBody, e as TableCell } from "./table-CAxdJZsY.js";
import { B as Badge } from "./badge-BsHDHlRV.js";
import { B as Button } from "./button-ByAXMyvk.js";
import { C as Card, a as CardContent } from "./card-BJQMSLe_.js";
import { P as Printer, A as AddEntryModal, S as StockTransactionType } from "./AddEntryModal-BDnVYESI.js";
import { S as SpinnerLoader } from "./SpinnerLoader-CAZRmIJX.js";
import { u as useLoaderData, a as useFetcher, F as Form } from "./components-D7UvGag_.js";
import { u as useNavigation } from "./index-DhHTcibu.js";
import "./utils-GkgzjW3c.js";
import "./index-ImHKLo0a.js";
import "./index-z_byfFrQ.js";
import "./createLucideIcon-uwkRm45G.js";
import "./dialog-BqKosxNq.js";
import "./index-DdafHWkt.js";
import "./index-D7VH9Fc8.js";
import "./index-DVTNuYOr.js";
import "./index-dIGjFc0I.js";
import "./index-BXzPK7u0.js";
import "./index-CzZ6EYkN.js";
import "./index-Vp2vNLNM.js";
import "./index-CEHS9zzk.js";
import "./index-QLGF6kQx.js";
import "./x-CCG_WJDF.js";
function Transactions() {
  var _a, _b;
  const {
    transactions,
    unit,
    selectedDate,
    stockId,
    sellerId
  } = useLoaderData();
  const navigation = useNavigation();
  const [isModalOpen, setIsModalOpen] = reactExports.useState(false);
  const [date, setDate] = reactExports.useState(selectedDate);
  const fetcher = useFetcher();
  const getTypeColor = (type) => {
    switch (type) {
      case StockTransactionType.RECEIVED:
        return "bg-green-100 text-green-800";
      case StockTransactionType.DELIVERED:
        return "bg-blue-100 text-blue-800";
      case StockTransactionType.SPOILED:
        return "bg-red-100 text-red-800";
      case StockTransactionType.RETURNED:
        return "bg-amber-100 text-amber-800";
      case StockTransactionType.CORRECTION:
        return "bg-purple-100 text-purple-800";
      case StockTransactionType.CONVERTED:
        return "bg-indigo-100 text-indigo-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD"
    }).format(amount);
  };
  const handlePrint = () => {
    window.print();
  };
  const handleAddEntry = async (data) => {
    const formData = new FormData();
    formData.append("intent", "create");
    formData.append("transactionType", data.stockTransactionType);
    formData.append("narration", data.narration);
    formData.append("quantity", data.quantity.toString());
    formData.append("deliveryDate", data.deliveryDate);
    if (sellerId) {
      formData.append("sellerId", sellerId.toString());
    }
    fetcher.submit(formData, {
      method: "POST"
    });
  };
  const loading = fetcher.state !== "idle";
  return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
    className: "space-y-6",
    children: [loading && /* @__PURE__ */ jsxRuntimeExports.jsx(SpinnerLoader, {
      loading
    }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
      className: "flex items-center justify-between no-print",
      children: [/* @__PURE__ */ jsxRuntimeExports.jsx("h2", {
        className: "text-2xl font-bold text-gray-900",
        children: "Transaction History"
      }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
        className: "flex gap-3",
        children: [/* @__PURE__ */ jsxRuntimeExports.jsxs(Form, {
          method: "get",
          className: "flex items-center gap-2",
          children: [/* @__PURE__ */ jsxRuntimeExports.jsx("input", {
            type: "hidden",
            name: "intent",
            value: "filter"
          }), sellerId ? /* @__PURE__ */ jsxRuntimeExports.jsx("input", {
            type: "hidden",
            name: "sellerId",
            value: sellerId.toString()
          }) : null, /* @__PURE__ */ jsxRuntimeExports.jsx("input", {
            type: "date",
            name: "deliveryDate",
            value: date,
            onChange: (e) => setDate(e.target.value),
            className: "border rounded-md p-2"
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
            type: "submit",
            variant: "outline",
            size: "sm",
            disabled: navigation.state === "submitting" && ((_a = navigation.formData) == null ? void 0 : _a.get("intent")) === "filter",
            children: navigation.state === "submitting" && ((_b = navigation.formData) == null ? void 0 : _b.get("intent")) === "filter" ? "Filtering..." : "Filter"
          })]
        }), /* @__PURE__ */ jsxRuntimeExports.jsxs(Button, {
          variant: "outline",
          size: "sm",
          className: "flex items-center gap-2 hover:bg-gray-100 transition-colors",
          onClick: handlePrint,
          children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Printer, {
            className: "h-4 w-4"
          }), "Print"]
        })]
      })]
    }), /* @__PURE__ */ jsxRuntimeExports.jsx(Card, {
      className: "printable-area shadow-sm",
      children: /* @__PURE__ */ jsxRuntimeExports.jsx(CardContent, {
        className: "p-0",
        children: /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
          className: "overflow-x-auto max-h-[600px] print:overflow-visible print:max-h-none",
          children: /* @__PURE__ */ jsxRuntimeExports.jsxs(Table, {
            className: "min-w-full ",
            children: [/* @__PURE__ */ jsxRuntimeExports.jsx(TableHeader, {
              className: "sticky top-0 bg-white z-10 print:static",
              children: /* @__PURE__ */ jsxRuntimeExports.jsxs(TableRow, {
                className: " hover:bg-gray-50  bg-white ",
                children: [/* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
                  className: "font-semibold text-gray-700 whitespace-nowrap",
                  children: "Type"
                }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
                  className: "font-semibold text-gray-700 whitespace-nowrap",
                  children: "Narration"
                }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
                  className: "text-right font-semibold text-gray-700 whitespace-nowrap",
                  children: "Balance"
                }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
                  className: "text-right font-semibold text-gray-700 whitespace-nowrap",
                  children: "T(Rcvd-Del)"
                }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
                  className: "text-right font-semibold text-gray-700 whitespace-nowrap",
                  children: "T(Ret-Spoil)"
                }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
                  className: "text-right font-semibold text-gray-700 whitespace-nowrap",
                  children: "T(Conv-Corr)"
                }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
                  className: "text-right font-semibold text-gray-700 whitespace-nowrap",
                  children: "ItemTotal"
                }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
                  className: "text-right font-semibold text-gray-700 whitespace-nowrap",
                  children: "T.Dist.Charges"
                }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
                  className: "text-right font-semibold text-gray-700 whitespace-nowrap",
                  children: "SalesComm"
                }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
                  className: "text-right font-semibold text-gray-700 whitespace-nowrap",
                  children: "SupNetAmt"
                }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
                  className: "font-semibold text-gray-700 whitespace-nowrap",
                  children: "User"
                })]
              })
            }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableBody, {
              className: "overflow-y-auto",
              children: transactions.length > 0 ? transactions.map((transaction, index) => /* @__PURE__ */ jsxRuntimeExports.jsxs(TableRow, {
                className: `hover:bg-gray-50 transition-colors ${index % 2 === 0 ? "bg-white" : "bg-gray-25"}`,
                children: [/* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
                  children: /* @__PURE__ */ jsxRuntimeExports.jsx(Badge, {
                    className: `${getTypeColor(transaction.transactionType)} border-0 font-medium transition-colors cursor-default`,
                    children: transaction.transactionType
                  })
                }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
                  className: "max-w-[300px] break-words text-gray-700",
                  children: transaction.narration
                }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
                  className: "text-right",
                  children: /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                    className: "font-medium text-blue-600",
                    children: transaction.balanceAfter || "-"
                  })
                }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
                  className: "text-sm text-gray-600 text-right",
                  children: (transaction.totalReceived || transaction.totalDelivered) > 0 ? `${transaction.totalReceived || 0} - ${transaction.totalDelivered || 0}` : "-"
                }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
                  className: "text-sm text-gray-600 text-right",
                  children: (transaction.totalReturned || transaction.totalSpoiled) > 0 ? `${transaction.totalReturned || 0} - ${transaction.totalSpoiled || 0}` : "-"
                }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
                  className: "text-sm text-gray-600 text-right",
                  children: (transaction.totalConverted || transaction.totalCorrection) > 0 ? `${transaction.totalConverted || 0} - ${transaction.totalCorrection || 0}` : "-"
                }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
                  className: "text-right font-bold",
                  children: transaction.itemTotalAmount > 0 ? /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                    className: "text-gray-900",
                    children: formatCurrency(transaction.itemTotalAmount)
                  }) : /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                    className: "text-gray-400",
                    children: "-"
                  })
                }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
                  className: "text-right font-bold",
                  children: transaction.totalDistributionCharges > 0 ? /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                    className: "text-gray-900",
                    children: formatCurrency(transaction.totalDistributionCharges)
                  }) : /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                    className: "text-gray-400",
                    children: "-"
                  })
                }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
                  className: "text-right font-bold",
                  children: transaction.totalSalesComm > 0 ? /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                    className: "text-gray-900",
                    children: `% ${transaction.totalSalesComm}`
                  }) : /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                    className: "text-gray-400",
                    children: "-"
                  })
                }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
                  className: "text-right font-bold",
                  children: transaction.supplierNetAmount > 0 ? /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                    className: "text-gray-900",
                    children: formatCurrency(transaction.supplierNetAmount)
                  }) : /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                    className: "text-gray-400",
                    children: "-"
                  })
                }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
                  className: "text-sm text-gray-600",
                  children: transaction.username
                })]
              }, transaction.invStockTransactionId)) : /* @__PURE__ */ jsxRuntimeExports.jsx(TableRow, {
                children: /* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
                  colSpan: 10,
                  className: "text-center py-12",
                  children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                    className: "flex flex-col items-center gap-2",
                    children: [/* @__PURE__ */ jsxRuntimeExports.jsx("div", {
                      className: "text-gray-400 text-lg",
                      children: "📋"
                    }), /* @__PURE__ */ jsxRuntimeExports.jsx("p", {
                      className: "text-gray-500 font-medium",
                      children: "No transactions found"
                    }), /* @__PURE__ */ jsxRuntimeExports.jsx("p", {
                      className: "text-gray-400 text-sm",
                      children: "Add your first transaction to get started"
                    })]
                  })
                })
              })
            })]
          })
        })
      })
    }), /* @__PURE__ */ jsxRuntimeExports.jsx(AddEntryModal, {
      isOpen: isModalOpen,
      onClose: () => setIsModalOpen(false),
      itemId: stockId,
      onAddEntry: handleAddEntry
    })]
  });
}
export {
  Transactions as default
};
//# sourceMappingURL=home.selectedMyStock._stockId.transactions-DnuXDzKV.js.map
