import { r as reactExports, j as jsxRuntimeExports } from "./jsx-runtime-bB2y7OuD.js";
import { B as Badge } from "./badge-BsHDHlRV.js";
import { B as Button } from "./button-ByAXMyvk.js";
import { C as Card, a as CardContent } from "./card-BJQMSLe_.js";
import { I as Input } from "./input-3v87qohQ.js";
import { L as Label } from "./label-cSASrwzW.js";
import { S as Select, a as SelectTrigger, b as SelectValue, c as SelectContent, d as SelectItem } from "./select-BFGSXKcr.js";
import { u as usePolling, R as RefreshCw, C as Clock, a as CircleAlert, d as dayjs, M as MessageCircleQuestion, T as Timer, P as Package, A as ArrowRight } from "./dayjs.min-DJknNzX7.js";
import { D as Dialog, a as DialogContent, c as <PERSON>alogHeader, b as DialogTitle } from "./dialog-BqKosxNq.js";
import { S as Separator } from "./separator-35EEKp-L.js";
import { u as useToast } from "./use-toast-EUd7m8UG.js";
import { P as Popover, a as PopoverTrigger, b as PopoverContent, c as PopoverClose } from "./popover-CD2vRFIm.js";
import { c as cn } from "./utils-CCoQZ9cZ.js";
import { C as Calendar, a as Calendar$1 } from "./calendar-_8-DqkPN.js";
import { T as Tabs, a as TabsList, b as TabsTrigger, c as TabsContent } from "./tabs-CfSdyzWr.js";
import { T as Table, a as TableHeader, b as TableRow, c as TableHead, d as TableBody, e as TableCell } from "./table-CAxdJZsY.js";
import { a as useFetcher } from "./components-D7UvGag_.js";
import { S as Search } from "./search-DzDJ71yc.js";
import { F as Filter } from "./filter-QRp_VNWc.js";
import { U as User } from "./user-C2IWJPnC.js";
import { P as Phone } from "./phone-HnpbSr97.js";
import { M as MapPin } from "./map-pin-BWBTUiG2.js";
import { S as Store } from "./store-CHb1Nw9Z.js";
import { T as Truck } from "./truck-ypDO_-A_.js";
import { E as Eye } from "./eye-BtRudnNq.js";
import { T as TriangleAlert } from "./triangle-alert-BYC5ld_8.js";
import { f as format } from "./format-82yT_5--.js";
import { J } from "./textarea-DWEAjqHe.js";
import "./index-ImHKLo0a.js";
import "./utils-GkgzjW3c.js";
import "./index-z_byfFrQ.js";
import "./index-CzZ6EYkN.js";
import "./index-Vp2vNLNM.js";
import "./index-IXOTxK3N.js";
import "./index-D7VH9Fc8.js";
import "./index-qCtcpOcW.js";
import "./index-BTdCMChR.js";
import "./index-BXzPK7u0.js";
import "./index-CEHS9zzk.js";
import "./index-DVTNuYOr.js";
import "./index-DscYByPT.js";
import "./index-CkL5tk39.js";
import "./index-dIGjFc0I.js";
import "./index-CpKiYcZd.js";
import "./index-C88PRvfd.js";
import "./chevron-down-pCP5jmjX.js";
import "./createLucideIcon-uwkRm45G.js";
import "./check-_dbWxIzT.js";
import "./index-DdafHWkt.js";
import "./index-QLGF6kQx.js";
import "./x-CCG_WJDF.js";
import "./addMonths-Dj4hq91A.js";
import "./isSameDay-BQMn9z7h.js";
import "./addDays-CyH8qBoF.js";
import "./chevron-right-B-tR7Kir.js";
import "./chevron-left-CLqBlTg1.js";
import "./index-CG37gmC0.js";
import "./index-DhHTcibu.js";
import "./use-sync-refs-DLXpJTw-.js";
function LiveOrderDashboard() {
  var _a, _b, _c, _d, _e, _f, _g, _h, _i, _j;
  const [allOrders, setAllOrders] = reactExports.useState();
  const [selectedOrder, setSelectedOrder] = reactExports.useState(null);
  const [orderStatusCounts, setOrderStatusCounts] = reactExports.useState([]);
  const [activeTab, setActiveTab] = reactExports.useState("live");
  const fetcher = useFetcher();
  const [actionType, setActionType] = reactExports.useState("");
  const [actionSelectedOrder, setActionSelectedOrder] = reactExports.useState(null);
  const [isSubmitting, setIsSubmitting] = reactExports.useState(false);
  const {
    toast
  } = useToast();
  const [searchType, setSearchType] = reactExports.useState("OrderID");
  const [searchTerm, setSearchTerm] = reactExports.useState("");
  const [filterDate, setFilterDate] = reactExports.useState({
    from: /* @__PURE__ */ new Date(),
    to: /* @__PURE__ */ new Date()
  });
  const [filterStatus, setFilterStatus] = reactExports.useState("");
  const [pageSize, setPageSize] = reactExports.useState(20);
  const [currentPage, setCurrentPage] = reactExports.useState(0);
  const [totalElements, setTotalElements] = reactExports.useState(0);
  const [dateRange, setDateRange] = reactExports.useState({
    from: /* @__PURE__ */ new Date(),
    to: /* @__PURE__ */ new Date()
  });
  const [debounceSearchTerm, setDebounceSearchTerm] = reactExports.useState("");
  reactExports.useEffect(() => {
    const timer = setTimeout(() => {
      setDebounceSearchTerm(searchTerm);
    }, 500);
    return () => {
      clearTimeout(timer);
    };
  }, [searchTerm]);
  const [animationKey, setAnimationKey] = reactExports.useState(0);
  const {
    isPolling,
    startPolling,
    stopPolling
  } = usePolling(() => refreshOrders(true), 59e3);
  const refreshOrders = reactExports.useCallback((isAutoRefresh) => {
    console.log("Refreshing orders...");
    const formData = new FormData();
    formData.append("intent", "Fetch Orders");
    const data = {
      activeTab,
      searchType,
      searchTerm: debounceSearchTerm,
      filterDate,
      filterStatus,
      pageSize,
      currentPage
    };
    formData.append("data", JSON.stringify(data));
    fetcher.submit(formData, {
      method: "post"
    });
  }, [activeTab, debounceSearchTerm, filterDate, filterStatus, pageSize, currentPage]);
  reactExports.useEffect(() => {
    refreshOrders();
  }, [activeTab, debounceSearchTerm, filterDate, filterStatus, pageSize, currentPage]);
  reactExports.useEffect(() => {
    if (activeTab === "live") {
      startPolling();
    } else {
      stopPolling();
    }
    return () => stopPolling();
  }, [activeTab, startPolling, stopPolling]);
  reactExports.useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.visibilityState === "visible" && activeTab === "live") {
        startPolling();
      } else {
        stopPolling();
      }
    };
    document.addEventListener("visibilitychange", handleVisibilityChange);
    return () => {
      document.removeEventListener("visibilitychange", handleVisibilityChange);
    };
  }, [activeTab, startPolling, stopPolling]);
  const handleTabChange = (newTab) => {
    setSearchType("OrderID");
    setSearchTerm("");
    setDebounceSearchTerm("");
    if (newTab === "live") {
      setFilterDate({
        from: /* @__PURE__ */ new Date(),
        to: /* @__PURE__ */ new Date()
      });
      setDateRange({
        from: /* @__PURE__ */ new Date(),
        to: /* @__PURE__ */ new Date()
      });
    } else {
      setFilterDate(void 0);
      setDateRange(void 0);
    }
    setFilterStatus("");
    setPageSize(20);
    setCurrentPage(0);
    setAllOrders([]);
    setOrderStatusCounts([]);
    setActiveTab(newTab);
  };
  const searchTypeFilters = [{
    label: "Order ID",
    value: "OrderID"
  }, {
    label: "Buyer ID",
    value: "BuyerID"
  }, {
    label: "Buyer Mobile",
    value: "BuyerMobile"
  }, {
    label: "Name",
    value: "Name"
  }];
  const statusFilters = [{
    label: "Created",
    value: "Created"
  }, {
    label: "Accepted",
    value: "Accepted"
  }, {
    label: "Packed",
    value: "Packed"
  }, {
    label: "Assigned",
    value: "Assigned"
  }, {
    label: "Picked Up",
    value: "PickedUp"
  }, {
    label: "Dispatched",
    value: "Dispatched"
  }, {
    label: "Delivered",
    value: "Delivered"
  }, {
    label: "Cancelled",
    value: "Cancelled"
  }];
  const {
    liveOrders,
    completedOrders
  } = reactExports.useMemo(() => {
    const live = allOrders == null ? void 0 : allOrders.filter((order) => !["Delivered", "Cancelled"].includes(order.orderStatus)).sort((a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime());
    const completed = allOrders == null ? void 0 : allOrders.filter((order) => ["Delivered", "Cancelled"].includes(order.orderStatus));
    return {
      liveOrders: live,
      completedOrders: completed
    };
  }, [allOrders]);
  const stats = {
    total: totalElements,
    created: ((_a = orderStatusCounts.find((s) => s.status === "Created")) == null ? void 0 : _a.count) || 0,
    accepted: ((_b = orderStatusCounts.find((s) => s.status === "Accepted")) == null ? void 0 : _b.count) || 0,
    packed: ((_c = orderStatusCounts.find((s) => s.status === "Packed")) == null ? void 0 : _c.count) || 0,
    assigned: ((_d = orderStatusCounts.find((s) => s.status === "Assigned")) == null ? void 0 : _d.count) || 0,
    dispatched: ((_e = orderStatusCounts.find((s) => s.status === "Dispatched")) == null ? void 0 : _e.count) || 0,
    delivered: ((_f = orderStatusCounts.find((s) => s.status === "Delivered")) == null ? void 0 : _f.count) || 0,
    cancelled: ((_g = orderStatusCounts.find((s) => s.status === "Cancelled")) == null ? void 0 : _g.count) || 0
  };
  const handleAction = (order, action2) => {
    setActionSelectedOrder(order);
    setActionType(action2);
  };
  const handleSubmitAction = (formData) => {
    const actionData = new FormData();
    actionData.append("intent", actionType);
    actionData.append("data", JSON.stringify({
      order: actionSelectedOrder,
      formData
    }));
    fetcher.submit(actionData, {
      method: "post"
    });
    setIsSubmitting(true);
  };
  reactExports.useEffect(() => {
    var _a2, _b2, _c2, _d2, _e2, _f2, _g2, _h2, _i2, _j2, _k, _l, _m, _n, _o, _p, _q, _r, _s, _t, _u, _v, _w, _x, _y, _z;
    if (((_a2 = fetcher.data) == null ? void 0 : _a2.intent) === "Fetch Orders") {
      if ((_b2 = fetcher.data) == null ? void 0 : _b2.success) {
        ((_d2 = (_c2 = fetcher.data) == null ? void 0 : _c2.data) == null ? void 0 : _d2.orders) ? setAllOrders(fetcher.data.data.orders) : setAllOrders([]);
        ((_f2 = (_e2 = fetcher.data) == null ? void 0 : _e2.data) == null ? void 0 : _f2.totalElements) ? setTotalElements(fetcher.data.data.totalElements) : setTotalElements(0);
        ((_h2 = (_g2 = fetcher.data) == null ? void 0 : _g2.data) == null ? void 0 : _h2.orderStatusCounts) ? setOrderStatusCounts(fetcher.data.data.orderStatusCounts) : setOrderStatusCounts([]);
      } else if (((_i2 = fetcher.data) == null ? void 0 : _i2.success) === false) {
        console.log((_j2 = fetcher.data) == null ? void 0 : _j2.errorMessage);
        toast({
          title: "Error",
          description: (_k = fetcher.data) == null ? void 0 : _k.errorMessage,
          variant: "destructive"
        });
      }
    }
    if (((_l = fetcher.data) == null ? void 0 : _l.intent) === "Cancel Order") {
      if ((_m = fetcher.data) == null ? void 0 : _m.success) {
        toast({
          title: "Order Cancelled",
          description: "Order cancelled successfully",
          style: {
            backgroundColor: "#00A33F",
            color: "#ffffff"
          }
        });
        setActionSelectedOrder(null);
        setActionType("");
        setIsSubmitting(false);
        refreshOrders(false);
      } else if (((_n = fetcher.data) == null ? void 0 : _n.success) === false) {
        console.log((_o = fetcher.data) == null ? void 0 : _o.errorMessage);
        setIsSubmitting(false);
        toast({
          title: "Error",
          description: (_p = fetcher.data) == null ? void 0 : _p.errorMessage,
          variant: "destructive"
        });
      }
    }
    if (((_q = fetcher.data) == null ? void 0 : _q.intent) === "Update LiveOrder Status") {
      if ((_r = fetcher.data) == null ? void 0 : _r.success) {
        toast({
          title: "Order Status Updated",
          description: "Order status updated successfully",
          style: {
            backgroundColor: "#00A33F",
            color: "#ffffff"
          }
        });
        setActionSelectedOrder(null);
        setActionType("");
        setIsSubmitting(false);
        refreshOrders(false);
      } else if (((_s = fetcher.data) == null ? void 0 : _s.success) === false) {
        console.log((_t = fetcher.data) == null ? void 0 : _t.errorMessage);
        setIsSubmitting(false);
        toast({
          title: "Error",
          description: (_u = fetcher.data) == null ? void 0 : _u.errorMessage,
          variant: "destructive"
        });
      }
    }
    if (((_v = fetcher.data) == null ? void 0 : _v.intent) === "Mark Delivered") {
      if ((_w = fetcher.data) == null ? void 0 : _w.success) {
        toast({
          title: "Order Marked as Delivered",
          description: "Order marked as delivered successfully",
          style: {
            backgroundColor: "#00A33F",
            color: "#ffffff"
          }
        });
        setActionSelectedOrder(null);
        setActionType("");
        setIsSubmitting(false);
        refreshOrders(false);
      } else if (((_x = fetcher.data) == null ? void 0 : _x.success) === false) {
        console.log((_y = fetcher.data) == null ? void 0 : _y.errorMessage);
        setIsSubmitting(false);
        toast({
          title: "Error",
          description: (_z = fetcher.data) == null ? void 0 : _z.errorMessage,
          variant: "destructive"
        });
      }
    }
  }, [fetcher.data]);
  return /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
    className: "min-h-screen p-6",
    children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
      className: "mx-auto mb-6",
      children: [/* @__PURE__ */ jsxRuntimeExports.jsx("div", {
        className: "mb-4",
        children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
          className: "flex flex-col gap-2 sm:flex-row sm:items-center sm:justify-between",
          children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
            children: [/* @__PURE__ */ jsxRuntimeExports.jsx("h1", {
              className: "text-xl md:text-3xl font-bold text-gray-900",
              children: "Orders"
            }), /* @__PURE__ */ jsxRuntimeExports.jsx("p", {
              className: "mt-2 text-gray-600",
              children: "Manage and track all your restaurant orders"
            })]
          }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
            className: "flex flex-row items-center gap-2",
            children: [/* @__PURE__ */ jsxRuntimeExports.jsxs(Button, {
              variant: "outline",
              size: "sm",
              onClick: () => refreshOrders(false),
              disabled: (fetcher.state === "loading" || fetcher.state === "submitting") && ((_h = fetcher.data) == null ? void 0 : _h.intent) === "Fetch Orders",
              className: "w-fit sm:ml-auto flex items-center gap-2",
              children: [/* @__PURE__ */ jsxRuntimeExports.jsx(RefreshCw, {
                className: `w-4 h-4 ${(fetcher.state === "loading" || fetcher.state === "submitting") && ((_i = fetcher.data) == null ? void 0 : _i.intent) === "Fetch Orders" ? "animate-spin" : ""}`
              }), (fetcher.state === "loading" || fetcher.state === "submitting") && ((_j = fetcher.data) == null ? void 0 : _j.intent) === "Fetch Orders" ? "Refreshing..." : "Refresh"]
            }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
              className: "flex items-center gap-2 text-sm text-gray-600",
              children: [/* @__PURE__ */ jsxRuntimeExports.jsx("div", {
                className: `w-2 h-2 rounded-full ${isPolling ? "bg-green-500 animate-pulse" : "bg-gray-400"}`
              }), "Auto-refresh ", isPolling ? "ON" : "OFF"]
            })]
          })]
        })
      }), /* @__PURE__ */ jsxRuntimeExports.jsxs(Tabs, {
        value: activeTab,
        onValueChange: handleTabChange,
        children: [/* @__PURE__ */ jsxRuntimeExports.jsxs(TabsList, {
          className: "w-full h-10 mb-2",
          children: [/* @__PURE__ */ jsxRuntimeExports.jsx(TabsTrigger, {
            value: "live",
            className: "w-1/2 h-8 py-1",
            children: "⏱️ Live Orders"
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(TabsTrigger, {
            value: "all",
            className: "w-1/2 h-8 py-1",
            children: "📦 All Orders"
          })]
        }), /* @__PURE__ */ jsxRuntimeExports.jsx(Card, {
          className: "mb-1 bg-gray-100",
          children: /* @__PURE__ */ jsxRuntimeExports.jsx(CardContent, {
            className: "p-2",
            children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
              className: "space-y-1",
              children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                className: "grid sm:grid-cols-2 lg:grid-cols-4 gap-2",
                children: [/* @__PURE__ */ jsxRuntimeExports.jsx("div", {
                  className: "relative lg:col-span-1",
                  children: /* @__PURE__ */ jsxRuntimeExports.jsxs(Select, {
                    value: searchType,
                    onValueChange: (value) => setSearchType(value),
                    children: [/* @__PURE__ */ jsxRuntimeExports.jsx(SelectTrigger, {
                      children: /* @__PURE__ */ jsxRuntimeExports.jsx(SelectValue, {
                        placeholder: "Search by"
                      })
                    }), /* @__PURE__ */ jsxRuntimeExports.jsx(SelectContent, {
                      children: searchTypeFilters.map((filter) => /* @__PURE__ */ jsxRuntimeExports.jsx(SelectItem, {
                        value: filter.value,
                        children: filter.label
                      }, filter.value))
                    })]
                  })
                }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                  className: "relative lg:col-span-2",
                  children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Search, {
                    className: "absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"
                  }), /* @__PURE__ */ jsxRuntimeExports.jsx(Input, {
                    placeholder: searchType === "Name" ? "Search name" : searchType === "BuyerMobile" ? "Search by Mobile" : "Search by ID",
                    value: searchTerm,
                    onChange: (e) => setSearchTerm(e.target.value),
                    className: "pl-10"
                  })]
                }), /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
                  className: "relative sm:col-span-2 lg:col-span-1 ",
                  children: /* @__PURE__ */ jsxRuntimeExports.jsxs(Popover, {
                    children: [/* @__PURE__ */ jsxRuntimeExports.jsx(PopoverTrigger, {
                      asChild: true,
                      children: /* @__PURE__ */ jsxRuntimeExports.jsxs(Button, {
                        id: "filterDate",
                        variant: "outline",
                        className: cn("w-full justify-start text-left font-normal", !filterDate && "text-muted-foreground"),
                        children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Calendar, {}), (filterDate == null ? void 0 : filterDate.from) ? filterDate.to ? /* @__PURE__ */ jsxRuntimeExports.jsxs(jsxRuntimeExports.Fragment, {
                          children: [format(filterDate.from, "LLL dd, y"), " - ", format(filterDate.to, "LLL dd, y")]
                        }) : format(filterDate.from, "LLL dd, y") : /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                          children: "Pick a date"
                        })]
                      })
                    }), /* @__PURE__ */ jsxRuntimeExports.jsxs(PopoverContent, {
                      className: "w-auto p-0",
                      align: "start",
                      children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Calendar$1, {
                        initialFocus: true,
                        selected: dateRange,
                        mode: "range",
                        onSelect: (range) => {
                          if (!(range == null ? void 0 : range.from)) return;
                          setDateRange({
                            from: range.from,
                            to: range.to || void 0
                          });
                        }
                      }), /* @__PURE__ */ jsxRuntimeExports.jsx(PopoverClose, {
                        className: "w-full",
                        children: /* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
                          variant: "ghost",
                          className: "w-full text-blue-500 hover:text-blue-500 justify-center",
                          onClick: () => setFilterDate(dateRange),
                          children: "Set"
                        })
                      })]
                    })]
                  })
                })]
              }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                className: "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-2",
                children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                  className: "space-y-1",
                  children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Label, {
                    className: "text-sm font-medium",
                    children: "Status"
                  }), /* @__PURE__ */ jsxRuntimeExports.jsxs(Select, {
                    value: filterStatus,
                    onValueChange: (value) => setFilterStatus(value),
                    children: [/* @__PURE__ */ jsxRuntimeExports.jsx(SelectTrigger, {
                      children: /* @__PURE__ */ jsxRuntimeExports.jsx(SelectValue, {
                        placeholder: "Select status"
                      })
                    }), /* @__PURE__ */ jsxRuntimeExports.jsx(SelectContent, {
                      children: statusFilters.map((filter) => /* @__PURE__ */ jsxRuntimeExports.jsx(SelectItem, {
                        value: filter.value,
                        children: filter.label
                      }, filter.value))
                    })]
                  })]
                }), /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
                  className: "space-y-1 lg:col-start-4 flex items-end",
                  children: /* @__PURE__ */ jsxRuntimeExports.jsxs(Button, {
                    onClick: () => {
                      setSearchType("OrderID");
                      setSearchTerm("");
                      setDebounceSearchTerm("");
                      if (activeTab === "live") {
                        setFilterDate({
                          from: /* @__PURE__ */ new Date(),
                          to: /* @__PURE__ */ new Date()
                        });
                        setDateRange({
                          from: /* @__PURE__ */ new Date(),
                          to: /* @__PURE__ */ new Date()
                        });
                      } else {
                        setFilterDate(void 0);
                        setDateRange(void 0);
                      }
                      setFilterStatus("");
                      setPageSize(20);
                      setCurrentPage(0);
                    },
                    className: "w-full",
                    children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Filter, {
                      className: "w-4 h-4 mr-2"
                    }), "Clear Filters"]
                  })
                })]
              })]
            })
          })
        }), /* @__PURE__ */ jsxRuntimeExports.jsx(Card, {
          className: "p-0 mb-1",
          children: /* @__PURE__ */ jsxRuntimeExports.jsx(CardContent, {
            className: "px-1.5 py-1",
            children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
              className: "flex flex-row items-center justify-end gap-1.5",
              children: [/* @__PURE__ */ jsxRuntimeExports.jsxs(Select, {
                value: pageSize.toString(),
                onValueChange: (value) => setPageSize(Number(value)),
                children: [/* @__PURE__ */ jsxRuntimeExports.jsx(SelectTrigger, {
                  className: "w-[140px] h-[36px]",
                  children: /* @__PURE__ */ jsxRuntimeExports.jsx(SelectValue, {})
                }), /* @__PURE__ */ jsxRuntimeExports.jsxs(SelectContent, {
                  children: [/* @__PURE__ */ jsxRuntimeExports.jsx(SelectItem, {
                    value: "20",
                    children: "20 per Page"
                  }), /* @__PURE__ */ jsxRuntimeExports.jsx(SelectItem, {
                    value: "50",
                    children: "50 per Page"
                  }), /* @__PURE__ */ jsxRuntimeExports.jsx(SelectItem, {
                    value: "100",
                    children: "100 per Page"
                  })]
                })]
              }), /* @__PURE__ */ jsxRuntimeExports.jsxs(Select, {
                value: currentPage.toString(),
                onValueChange: (value) => setCurrentPage(Number(value)),
                children: [/* @__PURE__ */ jsxRuntimeExports.jsx(SelectTrigger, {
                  className: "w-[140px] h-[36px]",
                  children: /* @__PURE__ */ jsxRuntimeExports.jsx(SelectValue, {})
                }), /* @__PURE__ */ jsxRuntimeExports.jsx(SelectContent, {
                  children: Array.from({
                    length: Math.ceil(totalElements / pageSize)
                  }, (_, i) => /* @__PURE__ */ jsxRuntimeExports.jsxs(SelectItem, {
                    value: i.toString(),
                    children: ["Page ", i + 1]
                  }, i))
                })]
              })]
            })
          })
        }), activeTab === "live" && /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
          className: "grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-8 gap-1 sm:gap-2 mb-4",
          children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Card, {
            className: "shadow-sm",
            children: /* @__PURE__ */ jsxRuntimeExports.jsxs(CardContent, {
              className: "p-1 sm:p-2 text-center",
              children: [/* @__PURE__ */ jsxRuntimeExports.jsx("div", {
                className: "text-xs sm:text-sm text-gray-800",
                children: "Total"
              }), /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
                className: "text-lg leading-6 font-semibold text-gray-900",
                children: stats.total
              })]
            })
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(Card, {
            className: "shadow-sm",
            children: /* @__PURE__ */ jsxRuntimeExports.jsxs(CardContent, {
              className: "p-1 sm:p-2 text-center",
              children: [/* @__PURE__ */ jsxRuntimeExports.jsx("div", {
                className: "text-xs sm:text-sm text-gray-800",
                children: "Created"
              }), /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
                className: "text-lg leading-6 font-semibold text-purple-600",
                children: stats.created
              })]
            })
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(Card, {
            className: "shadow-sm",
            children: /* @__PURE__ */ jsxRuntimeExports.jsxs(CardContent, {
              className: "p-1 sm:p-2 text-center",
              children: [/* @__PURE__ */ jsxRuntimeExports.jsx("div", {
                className: "text-xs sm:text-sm text-gray-800",
                children: "Accepted"
              }), /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
                className: "text-lg leading-6 font-semibold text-purple-600",
                children: stats.accepted
              })]
            })
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(Card, {
            className: "shadow-sm",
            children: /* @__PURE__ */ jsxRuntimeExports.jsxs(CardContent, {
              className: "p-1 sm:p-2 text-center",
              children: [/* @__PURE__ */ jsxRuntimeExports.jsx("div", {
                className: "text-xs sm:text-sm text-gray-800",
                children: "Packed"
              }), /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
                className: "text-lg leading-6 font-semibold text-orange-600",
                children: stats.packed
              })]
            })
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(Card, {
            className: "shadow-sm",
            children: /* @__PURE__ */ jsxRuntimeExports.jsxs(CardContent, {
              className: "p-1 sm:p-2 text-center",
              children: [/* @__PURE__ */ jsxRuntimeExports.jsx("div", {
                className: "text-xs sm:text-sm text-gray-800",
                children: "Assigned"
              }), /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
                className: "text-lg leading-6 font-semibold text-orange-600",
                children: stats.assigned
              })]
            })
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(Card, {
            className: "shadow-sm",
            children: /* @__PURE__ */ jsxRuntimeExports.jsxs(CardContent, {
              className: "p-1 sm:p-2 text-center",
              children: [/* @__PURE__ */ jsxRuntimeExports.jsx("div", {
                className: "text-xs sm:text-sm text-gray-800",
                children: "Dispatched"
              }), /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
                className: "text-lg leading-6 font-semibold text-yellow-600",
                children: stats.dispatched
              })]
            })
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(Card, {
            className: "shadow-sm",
            children: /* @__PURE__ */ jsxRuntimeExports.jsxs(CardContent, {
              className: "p-1 sm:p-2 text-center",
              children: [/* @__PURE__ */ jsxRuntimeExports.jsx("div", {
                className: "text-xs sm:text-sm text-gray-800",
                children: "Delivered"
              }), /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
                className: "text-lg leading-6 font-semibold text-green-600",
                children: stats.delivered
              })]
            })
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(Card, {
            className: "shadow-sm",
            children: /* @__PURE__ */ jsxRuntimeExports.jsxs(CardContent, {
              className: "p-1 sm:p-2 text-center",
              children: [/* @__PURE__ */ jsxRuntimeExports.jsx("div", {
                className: "text-xs sm:text-sm text-gray-800",
                children: "Cancelled"
              }), /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
                className: "text-lg leading-6 font-semibold text-red-600",
                children: stats.cancelled
              })]
            })
          })]
        }), /* @__PURE__ */ jsxRuntimeExports.jsxs(TabsContent, {
          value: "live",
          children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
            className: "mb-8",
            children: [/* @__PURE__ */ jsxRuntimeExports.jsx("div", {
              className: "flex items-center justify-between mb-4",
              children: /* @__PURE__ */ jsxRuntimeExports.jsxs("h2", {
                className: "text-xl sm:text-2xl font-bold flex items-center gap-2",
                children: [/* @__PURE__ */ jsxRuntimeExports.jsx("div", {
                  className: "w-3 h-3 bg-green-500 rounded-full animate-pulse"
                }), "Live Orders", /* @__PURE__ */ jsxRuntimeExports.jsx(Badge, {
                  variant: "secondary",
                  className: "ml-2",
                  children: totalElements - (stats.delivered + stats.cancelled) || 0
                })]
              })
            }), (liveOrders == null ? void 0 : liveOrders.length) === 0 ? /* @__PURE__ */ jsxRuntimeExports.jsx(Card, {
              children: /* @__PURE__ */ jsxRuntimeExports.jsx(CardContent, {
                className: "p-8 text-center",
                children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                  className: "text-gray-500",
                  children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Clock, {
                    className: "w-12 h-12 mx-auto mb-4 opacity-50"
                  }), /* @__PURE__ */ jsxRuntimeExports.jsx("p", {
                    className: "text-lg font-medium",
                    children: "No live orders"
                  }), /* @__PURE__ */ jsxRuntimeExports.jsx("p", {
                    className: "text-sm",
                    children: "No active orders for the selected date and filters."
                  })]
                })
              })
            }) : /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
              className: "grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-4 transition-all duration-300 ease-out",
              style: {
                transform: "translateY(0)",
                transition: "transform 0.3s ease-out"
              },
              children: liveOrders == null ? void 0 : liveOrders.map((order, index) => /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
                className: "animate-in fade-in slide-in-from-bottom-4 duration-500 ease-out transform transition-all hover:scale-[1.01]",
                style: {
                  animationDelay: `${index * 50}ms`,
                  animationFillMode: "both",
                  willChange: "transform"
                },
                children: /* @__PURE__ */ jsxRuntimeExports.jsx(OrderCard, {
                  order,
                  onViewDetails: setSelectedOrder,
                  onAction: handleAction
                })
              }, `${order.orderGroupId}-${animationKey}`))
            }, `live-orders-${animationKey}`)]
          }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
            className: "mb-8",
            children: [/* @__PURE__ */ jsxRuntimeExports.jsx("div", {
              className: "flex items-center justify-between mb-4",
              children: /* @__PURE__ */ jsxRuntimeExports.jsxs("h2", {
                className: "text-xl sm:text-2xl font-bold flex items-center gap-2",
                children: [/* @__PURE__ */ jsxRuntimeExports.jsx("div", {
                  className: "w-3 h-3 bg-gray-500 rounded-full"
                }), "Completed Orders", /* @__PURE__ */ jsxRuntimeExports.jsx(Badge, {
                  variant: "secondary",
                  className: "ml-2",
                  children: stats.delivered + stats.cancelled || 0
                })]
              })
            }), (completedOrders == null ? void 0 : completedOrders.length) === 0 ? /* @__PURE__ */ jsxRuntimeExports.jsx(Card, {
              children: /* @__PURE__ */ jsxRuntimeExports.jsx(CardContent, {
                className: "p-8 text-center",
                children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                  className: "text-gray-500",
                  children: [/* @__PURE__ */ jsxRuntimeExports.jsx(CircleAlert, {
                    className: "w-12 h-12 mx-auto mb-4 opacity-50"
                  }), /* @__PURE__ */ jsxRuntimeExports.jsx("p", {
                    className: "text-lg font-medium",
                    children: "No completed orders"
                  }), /* @__PURE__ */ jsxRuntimeExports.jsx("p", {
                    className: "text-sm",
                    children: "No completed orders for the selected date and filters."
                  })]
                })
              })
            }) : /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
              className: "overflow-x-auto",
              children: /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
                className: "flex gap-4 pb-4 min-w-max transition-all duration-300 ease-out",
                style: {
                  transform: "translateX(0)",
                  transition: "transform 0.3s ease-out"
                },
                children: completedOrders == null ? void 0 : completedOrders.map((order, index) => /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
                  className: "flex-shrink-0 w-72 sm:w-80 animate-in fade-in slide-in-from-right-4 duration-500 ease-out transform transition-all hover:scale-[1.01]",
                  style: {
                    animationDelay: `${index * 50}ms`,
                    animationFillMode: "both",
                    willChange: "transform"
                  },
                  children: /* @__PURE__ */ jsxRuntimeExports.jsx(OrderCard, {
                    order,
                    onViewDetails: setSelectedOrder,
                    onAction: handleAction
                  })
                }, `${order.orderGroupId}-${animationKey}`))
              }, `completed-orders-${animationKey}`)
            })]
          })]
        }), /* @__PURE__ */ jsxRuntimeExports.jsx(TabsContent, {
          value: "all",
          children: /* @__PURE__ */ jsxRuntimeExports.jsxs(Table, {
            children: [/* @__PURE__ */ jsxRuntimeExports.jsx(TableHeader, {
              children: /* @__PURE__ */ jsxRuntimeExports.jsxs(TableRow, {
                children: [/* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
                  className: "h-10 px-2 py-1",
                  children: "Order ID"
                }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
                  className: "h-10 px-2 py-1",
                  children: "Customer"
                }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
                  className: "h-10 px-2 py-1",
                  children: "Amount"
                }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
                  className: "h-10 px-2 py-1",
                  children: "Payment Status"
                }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
                  className: "h-10 px-2 py-1",
                  children: "Order Status"
                }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
                  className: "h-10 px-2 py-1",
                  children: "Actions"
                })]
              })
            }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableBody, {
              children: (allOrders == null ? void 0 : allOrders.length) === 0 ? /* @__PURE__ */ jsxRuntimeExports.jsx(TableRow, {
                children: /* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
                  colSpan: 6,
                  className: "h-24 text-center",
                  children: "No results."
                })
              }) : allOrders == null ? void 0 : allOrders.map((order) => /* @__PURE__ */ jsxRuntimeExports.jsxs(TableRow, {
                children: [/* @__PURE__ */ jsxRuntimeExports.jsxs(TableCell, {
                  className: "px-2 py-2",
                  children: [/* @__PURE__ */ jsxRuntimeExports.jsx("p", {
                    children: order.orderGroupId
                  }), /* @__PURE__ */ jsxRuntimeExports.jsx("p", {
                    className: "text-xs text-gray-600",
                    children: dayjs(order.createdAt).format("DD/MM/YYYY, h:mm A")
                  })]
                }), /* @__PURE__ */ jsxRuntimeExports.jsxs(TableCell, {
                  className: "px-2 py-2 break-all",
                  children: [/* @__PURE__ */ jsxRuntimeExports.jsx("p", {
                    children: order.buyerName
                  }), /* @__PURE__ */ jsxRuntimeExports.jsx("p", {
                    className: "text-xs text-gray-600",
                    children: order.bAreaName
                  })]
                }), /* @__PURE__ */ jsxRuntimeExports.jsxs(TableCell, {
                  className: "whitespace-nowrap px-2 py-2",
                  children: ["₹ ", order.totalOrderGroupAmount.toLocaleString()]
                }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
                  className: "px-2 py-2",
                  children: order.paymentCompleted ? "Paid" : "Pending"
                }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
                  className: "px-2 py-2",
                  children: /* @__PURE__ */ jsxRuntimeExports.jsx(Badge, {
                    variant: order.orderStatus === "Delivered" ? "default" : order.orderStatus === "Cancelled" ? "destructive" : "secondary",
                    children: order.orderStatus
                  })
                }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
                  className: "px-2 py-2",
                  children: /* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
                    variant: "outline",
                    size: "sm",
                    onClick: () => setSelectedOrder(order),
                    children: "View"
                  })
                })]
              }, order.orderGroupId))
            })]
          })
        })]
      }), /* @__PURE__ */ jsxRuntimeExports.jsx(OrderDetailsModal, {
        order: selectedOrder,
        onClose: () => setSelectedOrder(null),
        onAction: handleAction
      }), /* @__PURE__ */ jsxRuntimeExports.jsx(ActionModal, {
        order: actionSelectedOrder,
        actionType,
        onClose: () => {
          setActionSelectedOrder(null);
          setActionType("");
        },
        isSubmitting,
        onSubmit: handleSubmitAction
      })]
    })
  });
}
function OrderCard({
  order,
  onViewDetails,
  onAction
}) {
  const [currentTime, setCurrentTime] = reactExports.useState(Date.now());
  reactExports.useEffect(() => {
    if (!["Delivered", "Cancelled"].includes(order.orderStatus)) {
      const interval = setInterval(() => {
        setCurrentTime(Date.now());
      }, 1e3);
      return () => clearInterval(interval);
    }
  }, [order.orderStatus]);
  const styling = getOrderCardStyling(order);
  const timeDiff = getTimeDifferenceInSeconds(order.createdAt);
  const isNewOrder = timeDiff <= 60;
  reactExports.useEffect(() => {
    if (isNewOrder) {
      const audio = new Audio("/new-order.mp3");
      audio.play().catch(console.warn);
      return () => {
        audio.pause();
        audio.src = "";
      };
    }
  }, [isNewOrder]);
  const handlePhoneClick = (phoneNumber, e) => {
    e.stopPropagation();
    window.open(`tel:${phoneNumber}`, "_self");
  };
  const getActionButtons = () => {
    const buttons = [];
    if (order.orderStatus === "Created") {
      buttons.push(/* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
        variant: "destructive",
        size: "sm",
        onClick: (e) => {
          e.stopPropagation();
          onAction(order, "Cancel Order");
        },
        className: "text-xs",
        children: "Cancel Order"
      }, "cancel"));
    }
    if (showUpdateStatusButton(order)) {
      buttons.push(/* @__PURE__ */ jsxRuntimeExports.jsxs(Button, {
        variant: "default",
        size: "sm",
        onClick: (e) => {
          e.stopPropagation();
          onAction(order, "Update LiveOrder Status");
        },
        className: "text-xs",
        children: ["Update to ", order.orderStatus === "Created" ? "Accepted" : order.orderStatus === "Accepted" ? "Packed" : order.orderStatus === "Packed" ? "Assigned" : order.orderStatus === "Assigned" ? "PickedUp" : "Dispatched", " ", /* @__PURE__ */ jsxRuntimeExports.jsx(ArrowRight, {
          className: "w-3 h-3"
        })]
      }, "updateStatus"));
    }
    if (order.orderStatus === "Dispatched") {
      buttons.push(/* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
        variant: "default",
        size: "sm",
        onClick: (e) => {
          e.stopPropagation();
          onAction(order, "Mark Delivered");
        },
        className: "text-xs bg-blue-500 hover:bg-blue-500",
        children: "Mark Delivered"
      }, "markDelivered"));
    }
    return buttons;
  };
  return /* @__PURE__ */ jsxRuntimeExports.jsx(Card, {
    className: `${styling.className} will-change-transform cursor-pointer transition-all duration-500 h-full hover:shadow-lg`,
    onClick: () => onViewDetails(order),
    onMouseEnter: (e) => {
      e.currentTarget.style.animationPlayState = "paused";
    },
    onMouseLeave: (e) => {
      e.currentTarget.style.animationPlayState = "running";
    },
    children: /* @__PURE__ */ jsxRuntimeExports.jsxs(CardContent, {
      className: "p-3 sm:p-4",
      children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
        className: "flex flex-col sm:flex-row sm:justify-between sm:items-start gap-2 mb-3",
        children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
          className: "flex flex-wrap items-center gap-2",
          children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
            className: "font-semibold text-base sm:text-lg",
            children: ["#", order.orderGroupId]
          }), isNewOrder && /* @__PURE__ */ jsxRuntimeExports.jsx(Badge, {
            variant: "secondary",
            className: "bg-gradient-to-r from-emerald-100 to-green-100 text-emerald-800 text-xs border border-emerald-200 shadow-sm font-semibold animate-bounce",
            children: "✨ NEW"
          }), order.supportTickets && order.supportTickets.length > 0 && /* @__PURE__ */ jsxRuntimeExports.jsxs(Badge, {
            variant: "destructive",
            className: "flex items-center gap-1 text-xs",
            children: [/* @__PURE__ */ jsxRuntimeExports.jsx(MessageCircleQuestion, {
              className: "w-3 h-3"
            }), order.supportTickets.length]
          })]
        }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
          className: "text-xs sm:text-sm text-gray-500 flex items-center gap-1",
          children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Clock, {
            className: "w-3 h-3 sm:w-4 sm:h-4"
          }), ["Cancelled"].includes(order.orderStatus) ? formatTimeElapsed(getTimeDifferenceInSeconds(order.createdAt, order.deliveredTime || order.updatedAt)) : ["Delivered"].includes(order.orderStatus) ? formatTimeElapsed(getTimeDifferenceInSeconds(order.createdAt, order.deliveredTime || order.updatedAt)) : formatTimeElapsed(timeDiff)]
        })]
      }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
        className: "mb-3",
        children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
          className: "flex items-center gap-2 mb-1",
          children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Timer, {
            className: "w-3 h-3 sm:w-4 sm:h-4 text-gray-500 flex-shrink-0"
          }), /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
            className: "text-xs sm:text-sm text-gray-500",
            children: dayjs(order.createdAt).format("DD MMM YY - h:mm A")
          })]
        }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
          className: "flex items-center gap-2 mb-1",
          children: [/* @__PURE__ */ jsxRuntimeExports.jsx(User, {
            className: "w-3 h-3 sm:w-4 sm:h-4 text-gray-500 flex-shrink-0"
          }), /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
            className: "font-medium text-sm sm:text-base truncate",
            children: order.buyerName
          }), /* @__PURE__ */ jsxRuntimeExports.jsx("button", {
            onClick: (e) => handlePhoneClick(order.buyerMobile, e),
            className: "text-blue-600 hover:text-blue-800 flex-shrink-0 p-1 rounded hover:bg-blue-50",
            children: /* @__PURE__ */ jsxRuntimeExports.jsx(Phone, {
              className: "w-3 h-3 sm:w-4 sm:h-4"
            })
          })]
        }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
          className: "text-xs sm:text-sm text-gray-600 flex items-start gap-2",
          children: [/* @__PURE__ */ jsxRuntimeExports.jsx(MapPin, {
            className: "w-3 h-3 sm:w-4 sm:h-4 text-gray-500 mt-0.5 flex-shrink-0"
          }), /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
            className: "line-clamp-2",
            children: order.bAddress
          })]
        })]
      }), /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
        className: "mb-3",
        children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
          className: "flex items-center gap-2 flex-wrap",
          children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Store, {
            className: "w-3 h-3 sm:w-4 sm:h-4 text-gray-500 flex-shrink-0"
          }), /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
            className: "font-medium text-sm sm:text-base truncate",
            children: order.sellerName
          }), /* @__PURE__ */ jsxRuntimeExports.jsx("button", {
            onClick: (e) => handlePhoneClick(order.sellerMobile, e),
            className: "text-blue-600 hover:text-blue-800 flex-shrink-0 p-1 rounded hover:bg-blue-50",
            children: /* @__PURE__ */ jsxRuntimeExports.jsx(Phone, {
              className: "w-3 h-3 sm:w-4 sm:h-4"
            })
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(Badge, {
            variant: "outline",
            className: "text-xs",
            children: order.logisticProvider
          }), order.pos !== "none" && order.pos !== void 0 && /* @__PURE__ */ jsxRuntimeExports.jsx(Badge, {
            variant: "outline",
            className: "text-xs",
            children: order.pos
          })]
        })
      }), order.logisticProvider === "MP2" && order.logisticDetails && /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
        className: "mb-3",
        children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
          className: "flex items-center gap-2 flex-wrap",
          children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Truck, {
            className: "w-3 h-3 sm:w-4 sm:h-4 text-gray-500 flex-shrink-0"
          }), /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
            className: "text-xs sm:text-sm truncate",
            children: order.logisticDetails.riderName
          }), order.logisticDetails.riderPhone && /* @__PURE__ */ jsxRuntimeExports.jsx("button", {
            onClick: (e) => {
              var _a;
              return handlePhoneClick(((_a = order.logisticDetails) == null ? void 0 : _a.riderPhone.toString()) || "", e);
            },
            className: "text-blue-600 hover:text-blue-800 flex-shrink-0 p-1 rounded hover:bg-blue-50",
            children: /* @__PURE__ */ jsxRuntimeExports.jsx(Phone, {
              className: "w-3 h-3 sm:w-4 sm:h-4"
            })
          }), order.logStatus && /* @__PURE__ */ jsxRuntimeExports.jsx(Badge, {
            variant: "outline",
            className: "text-xs",
            children: getLogisticStatusDisplayName(order.logStatus)
          })]
        })
      }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
        className: "mb-3",
        children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
          className: "text-sm sm:text-base font-medium text-gray-900 mb-1",
          children: [order.totalItems, " item(s) • ₹", order.totalOrderGroupAmount]
        }), order.orderDetails && /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
          className: "text-xs text-gray-500 line-clamp-2",
          children: [order.orderDetails.slice(0, 3).map((item, idx) => /* @__PURE__ */ jsxRuntimeExports.jsxs("span", {
            children: [item.itemName, " x", item.qty, idx < Math.min(order.orderDetails.length, 3) - 1 ? ", " : ""]
          }, item.orderId)), order.orderDetails.length > 3 && "..."]
        })]
      }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
        className: "flex flex-col gap-2",
        children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
          className: "flex justify-between items-center",
          children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Badge, {
            variant: order.orderStatus === "Delivered" ? "default" : order.orderStatus === "Cancelled" ? "destructive" : "secondary",
            className: "capitalize text-xs",
            children: getStatusDisplayName(order.orderStatus)
          }), /* @__PURE__ */ jsxRuntimeExports.jsxs(Button, {
            variant: "outline",
            size: "sm",
            onClick: (e) => {
              e.stopPropagation();
              onViewDetails(order);
            },
            className: "flex items-center gap-1 text-xs sm:text-sm",
            children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Eye, {
              className: "w-3 h-3 sm:w-4 sm:h-4"
            }), "Details"]
          })]
        }), getActionButtons().length > 0 && /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
          className: "flex gap-2 flex-wrap",
          children: getActionButtons()
        })]
      }), styling.helperText && /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
        className: "mt-3 p-3 bg-gradient-to-r from-amber-50 via-amber-25 to-amber-50 border border-amber-200 rounded-lg text-xs text-amber-900 shadow-sm backdrop-blur-sm",
        children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
          className: "flex items-start gap-2",
          children: [/* @__PURE__ */ jsxRuntimeExports.jsx(CircleAlert, {
            className: "w-4 h-4 text-amber-600 mt-0.5 flex-shrink-0"
          }), /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
            className: "leading-relaxed font-medium",
            children: styling.helperText
          })]
        })
      })]
    })
  });
}
function OrderDetailsModal({
  order,
  onClose,
  onAction
}) {
  var _a;
  if (!order) return null;
  const handlePhoneClick = (phoneNumber) => {
    window.open(`tel:${phoneNumber}`, "_self");
  };
  const timeDiff = getTimeDifferenceInSeconds(order.createdAt);
  return /* @__PURE__ */ jsxRuntimeExports.jsx(Dialog, {
    open: !!order,
    onOpenChange: onClose,
    children: /* @__PURE__ */ jsxRuntimeExports.jsxs(DialogContent, {
      className: "max-w-4xl max-h-[90vh] overflow-y-auto w-[95vw] sm:w-full rounded-md",
      children: [/* @__PURE__ */ jsxRuntimeExports.jsx(DialogHeader, {
        children: /* @__PURE__ */ jsxRuntimeExports.jsxs(DialogTitle, {
          className: "text-lg sm:text-xl flex items-center gap-2",
          children: ["Order Details - #", order.orderGroupId, /* @__PURE__ */ jsxRuntimeExports.jsx(Badge, {
            variant: order.orderStatus === "Delivered" ? "default" : order.orderStatus === "Cancelled" ? "destructive" : "secondary",
            className: "capitalize",
            children: getStatusDisplayName(order.orderStatus)
          })]
        })
      }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
        className: "space-y-6",
        children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
          children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("h3", {
            className: "font-semibold mb-3 flex items-center gap-2",
            children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Clock, {
              className: "w-4 h-4"
            }), "Order Timeline"]
          }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
            className: "space-y-2 text-sm",
            children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
              className: "flex justify-between",
              children: [/* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                children: "Order Placed:"
              }), /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                children: dayjs(order.createdAt).format("ddd, DD MMM YYYY - h:mm:ss A")
              })]
            }), order.acceptedTime && /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
              className: "flex justify-between",
              children: [/* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                children: "Accepted:"
              }), /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                children: dayjs(order.acceptedTime).format("ddd, DD MMM YYYY - h:mm:ss A")
              })]
            }), order.packedTime && /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
              className: "flex justify-between",
              children: [/* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                children: "Packed:"
              }), /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                children: dayjs(order.packedTime).format("ddd, DD MMM YYYY - h:mm:ss A")
              })]
            }), order.assignedTime && /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
              className: "flex justify-between",
              children: [/* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                children: "Assigned:"
              }), /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                children: dayjs(order.assignedTime).format("ddd, DD MMM YYYY - h:mm:ss A")
              })]
            }), order.pickedUpTime && /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
              className: "flex justify-between",
              children: [/* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                children: "Picked Up:"
              }), /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                children: dayjs(order.pickedUpTime).format("ddd, DD MMM YYYY - h:mm:ss A")
              })]
            }), order.deliveryStartTime && /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
              className: "flex justify-between",
              children: [/* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                children: "Dispatched:"
              }), /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                children: dayjs(order.deliveryStartTime).format("ddd, DD MMM YYYY - h:mm:ss A")
              })]
            }), order.deliveredTime && /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
              className: "flex justify-between",
              children: [/* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                children: "Delivered:"
              }), /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                children: dayjs(order.deliveredTime).format("ddd, DD MMM YYYY - h:mm:ss A")
              })]
            }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
              className: "flex justify-between font-medium",
              children: [/* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                children: "Total Time:"
              }), /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                children: ["Delivered", "Cancelled"].includes(order.orderStatus) ? formatTimeElapsed(getTimeDifferenceInSeconds(order.createdAt, order.deliveredTime || order.updatedAt)) : formatTimeElapsed(timeDiff)
              })]
            })]
          })]
        }), /* @__PURE__ */ jsxRuntimeExports.jsx(Separator, {}), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
          className: "grid grid-cols-1 sm:grid-cols-2 gap-6",
          children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
            children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("h3", {
              className: "font-semibold mb-3 flex items-center gap-2",
              children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Package, {
                className: "w-4 h-4"
              }), "Order Information"]
            }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
              className: "space-y-2 text-sm",
              children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                children: [/* @__PURE__ */ jsxRuntimeExports.jsx("strong", {
                  children: "Order ID:"
                }), " #", order.orderGroupId]
              }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                children: [/* @__PURE__ */ jsxRuntimeExports.jsx("strong", {
                  children: "Network:"
                }), " ", order.networkName]
              }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                children: [/* @__PURE__ */ jsxRuntimeExports.jsx("strong", {
                  children: "Delivery Type:"
                }), " ", order.deliveryType]
              }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                children: [/* @__PURE__ */ jsxRuntimeExports.jsx("strong", {
                  children: "Logistics Provider:"
                }), " ", order.logisticProvider]
              }), order.pos !== "none" && order.pos !== void 0 && /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                children: [/* @__PURE__ */ jsxRuntimeExports.jsx("strong", {
                  children: "POS:"
                }), " ", order.pos]
              }), order.deliveryOtp && /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                children: [/* @__PURE__ */ jsxRuntimeExports.jsx("strong", {
                  children: "Delivery OTP:"
                }), " ", order.deliveryOtp]
              })]
            })]
          }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
            children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("h3", {
              className: "font-semibold mb-3 flex items-center gap-2",
              children: [/* @__PURE__ */ jsxRuntimeExports.jsx(User, {
                className: "w-4 h-4"
              }), "Customer Information"]
            }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
              className: "space-y-2 text-sm",
              children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                children: [/* @__PURE__ */ jsxRuntimeExports.jsx("strong", {
                  children: "Name:"
                }), " ", order.buyerName]
              }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                className: "flex items-center gap-2",
                children: [/* @__PURE__ */ jsxRuntimeExports.jsx("strong", {
                  children: "Phone:"
                }), /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                  children: order.buyerMobile
                }), /* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
                  size: "sm",
                  variant: "outline",
                  onClick: () => handlePhoneClick(order.buyerMobile),
                  className: "h-6 w-6 p-0",
                  children: /* @__PURE__ */ jsxRuntimeExports.jsx(Phone, {
                    className: "w-3 h-3"
                  })
                })]
              }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                children: [/* @__PURE__ */ jsxRuntimeExports.jsx("strong", {
                  children: "Address:"
                }), " ", order.bAddress]
              }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                children: [/* @__PURE__ */ jsxRuntimeExports.jsx("strong", {
                  children: "Area:"
                }), " ", order.bAreaName]
              })]
            })]
          })]
        }), /* @__PURE__ */ jsxRuntimeExports.jsx(Separator, {}), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
          children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("h3", {
            className: "font-semibold mb-3 flex items-center gap-2",
            children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Store, {
              className: "w-4 h-4"
            }), "Restaurant Information"]
          }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
            className: "grid grid-cols-1 sm:grid-cols-2 gap-4 text-sm",
            children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
              children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                children: [/* @__PURE__ */ jsxRuntimeExports.jsx("strong", {
                  children: "Name:"
                }), " ", order.sellerName]
              }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                className: "flex items-center gap-2",
                children: [/* @__PURE__ */ jsxRuntimeExports.jsx("strong", {
                  children: "Phone:"
                }), /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                  children: order.sellerMobile
                }), /* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
                  size: "sm",
                  variant: "outline",
                  onClick: () => handlePhoneClick(order.sellerMobile),
                  className: "h-6 w-6 p-0",
                  children: /* @__PURE__ */ jsxRuntimeExports.jsx(Phone, {
                    className: "w-3 h-3"
                  })
                })]
              })]
            }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
              children: [order.agentName && /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                children: [/* @__PURE__ */ jsxRuntimeExports.jsx("strong", {
                  children: "Agent:"
                }), " ", order.agentName]
              }), order.sellerMessage && /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                children: [/* @__PURE__ */ jsxRuntimeExports.jsx("strong", {
                  children: "Message:"
                }), " ", order.sellerMessage]
              })]
            })]
          })]
        }), order.logisticProvider === "MP2" && ((_a = order.logisticDetails) == null ? void 0 : _a.riderName) && /* @__PURE__ */ jsxRuntimeExports.jsxs(jsxRuntimeExports.Fragment, {
          children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Separator, {}), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
            children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("h3", {
              className: "font-semibold mb-3 flex items-center gap-2",
              children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Truck, {
                className: "w-4 h-4"
              }), "Delivery Partner"]
            }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
              className: "text-sm space-y-2",
              children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                children: [/* @__PURE__ */ jsxRuntimeExports.jsx("strong", {
                  children: "Name:"
                }), " ", order.logisticDetails.riderName]
              }), order.logisticDetails.riderPhone && /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                className: "flex items-center gap-2",
                children: [/* @__PURE__ */ jsxRuntimeExports.jsx("strong", {
                  children: "Phone:"
                }), /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                  children: order.logisticDetails.riderPhone
                }), /* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
                  size: "sm",
                  variant: "outline",
                  onClick: () => handlePhoneClick(order.logisticDetails.riderPhone.toString()),
                  className: "h-6 w-6 p-0",
                  children: /* @__PURE__ */ jsxRuntimeExports.jsx(Phone, {
                    className: "w-3 h-3"
                  })
                })]
              }), order.logStatus && /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                children: [/* @__PURE__ */ jsxRuntimeExports.jsx("strong", {
                  children: "Logistics Status:"
                }), /* @__PURE__ */ jsxRuntimeExports.jsx(Badge, {
                  className: "ml-2",
                  children: getLogisticStatusDisplayName(order.logStatus)
                })]
              }), order.logisticDetails.trackingUrl && /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                children: [/* @__PURE__ */ jsxRuntimeExports.jsx("strong", {
                  children: "Tracking:"
                }), /* @__PURE__ */ jsxRuntimeExports.jsx("a", {
                  href: order.logisticDetails.trackingUrl,
                  target: "_blank",
                  rel: "noopener noreferrer",
                  className: "ml-2 text-blue-600 hover:underline",
                  children: "Track Order"
                })]
              })]
            })]
          })]
        }), order.orderDetails && order.orderDetails.length > 0 && /* @__PURE__ */ jsxRuntimeExports.jsxs(jsxRuntimeExports.Fragment, {
          children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Separator, {}), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
            children: [/* @__PURE__ */ jsxRuntimeExports.jsx("h3", {
              className: "font-semibold mb-3",
              children: "Order Items"
            }), /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
              className: "space-y-3",
              children: order.orderDetails.map((item) => /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
                className: "border rounded p-3 bg-gray-50",
                children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                  className: "flex justify-between items-start",
                  children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                    className: "flex-1",
                    children: [/* @__PURE__ */ jsxRuntimeExports.jsx("div", {
                      className: "font-medium",
                      children: item.itemName
                    }), item.itemRegionalLanguageName && /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
                      className: "text-sm text-gray-600",
                      children: item.itemRegionalLanguageName
                    }), item.variationName && /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                      className: "text-sm text-gray-600",
                      children: ["Variation: ", item.variationName]
                    }), item.addOns && /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                      className: "text-sm text-gray-600",
                      children: ["Add-ons:", " ", item.addOns.flatMap((aog) => aog.addOnItemList.map((addon) => `${addon.name} (+₹${addon.price})`)).join(", ")]
                    }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                      className: "text-xs text-gray-500 mt-1",
                      children: [item.diet && /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                        className: `inline-block w-2 h-2 rounded-full mr-1 ${item.diet === "veg" ? "bg-green-500" : item.diet === "nonveg" ? "bg-red-500" : "bg-yellow-500"}`
                      }), item.diet, " • ", item.unit]
                    })]
                  }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                    className: "text-right ml-4",
                    children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                      className: "text-sm",
                      children: ["Qty: ", item.qty]
                    }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                      className: "text-sm",
                      children: ["₹", item.pricePerUnit, " per ", item.unit]
                    }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                      className: "font-semibold",
                      children: ["₹", item.amount, item.strikeOffAmount && item.strikeOffAmount !== item.amount && /* @__PURE__ */ jsxRuntimeExports.jsxs("span", {
                        className: "ml-0.5 text-sm text-gray-600 line-through",
                        children: ["₹", item.strikeOffAmount]
                      })]
                    })]
                  })]
                })
              }, item.orderId))
            })]
          })]
        }), /* @__PURE__ */ jsxRuntimeExports.jsx(Separator, {}), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
          children: [/* @__PURE__ */ jsxRuntimeExports.jsx("h3", {
            className: "font-semibold mb-3",
            children: "Payment Breakdown"
          }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
            className: "bg-gray-50 rounded p-4 space-y-2 text-sm",
            children: [order.totalItemsStrikeoffAmount && order.totalItemsStrikeoffAmount !== order.itemsTotalAmount && /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
              className: "flex justify-between",
              children: [/* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                children: "Item Total (strikeoff):"
              }), /* @__PURE__ */ jsxRuntimeExports.jsxs("span", {
                children: ["₹", order.totalItemsStrikeoffAmount]
              })]
            }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
              className: "flex justify-between",
              children: [/* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                children: "Item Total:"
              }), /* @__PURE__ */ jsxRuntimeExports.jsxs("span", {
                children: ["₹", order.itemsTotalAmount]
              })]
            }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
              className: "flex justify-between",
              children: [/* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                children: "Delivery Charge:"
              }), /* @__PURE__ */ jsxRuntimeExports.jsxs("span", {
                children: ["₹", order.totalDeliveryCharge]
              })]
            }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
              className: "flex justify-between",
              children: [/* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                children: "Packaging Charges:"
              }), /* @__PURE__ */ jsxRuntimeExports.jsxs("span", {
                children: ["₹", order.packagingCharges]
              })]
            }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
              className: "flex justify-between",
              children: [/* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                children: "Platform Fee:"
              }), /* @__PURE__ */ jsxRuntimeExports.jsxs("span", {
                children: ["₹", order.platformFee]
              })]
            }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
              className: "flex justify-between",
              children: [/* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                children: "Tax Amount:"
              }), /* @__PURE__ */ jsxRuntimeExports.jsxs("span", {
                children: ["₹", order.totalTaxAmount]
              })]
            }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
              className: "flex justify-between text-red-600",
              children: [/* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                children: "Discount:"
              }), /* @__PURE__ */ jsxRuntimeExports.jsxs("span", {
                children: ["-₹", order.totalDiscountAmount]
              })]
            }), /* @__PURE__ */ jsxRuntimeExports.jsx(Separator, {}), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
              className: "flex justify-between font-semibold text-lg",
              children: [/* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                children: "Total Amount:"
              }), /* @__PURE__ */ jsxRuntimeExports.jsxs("span", {
                children: ["₹", order.totalOrderGroupAmount]
              })]
            }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
              className: "flex justify-between text-sm text-gray-600",
              children: [/* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                children: "COD Amount:"
              }), /* @__PURE__ */ jsxRuntimeExports.jsxs("span", {
                children: ["₹", order.codAmount]
              })]
            }), order.walletAmount > 0 && /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
              className: "flex justify-between text-sm text-gray-600",
              children: [/* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                children: "Wallet Amount:"
              }), /* @__PURE__ */ jsxRuntimeExports.jsxs("span", {
                children: ["₹", order.walletAmount]
              })]
            })]
          })]
        }), order.supportTickets && order.supportTickets.length > 0 && /* @__PURE__ */ jsxRuntimeExports.jsxs(jsxRuntimeExports.Fragment, {
          children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Separator, {}), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
            children: [/* @__PURE__ */ jsxRuntimeExports.jsx("h3", {
              className: "font-semibold mb-3",
              children: "Support Tickets"
            }), /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
              className: "space-y-3",
              children: order.supportTickets.map((ticket) => /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                className: "border rounded p-3",
                children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                  className: "flex justify-between items-start mb-2",
                  children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                    className: "font-medium",
                    children: ["Ticket #", ticket.ticketId]
                  }), /* @__PURE__ */ jsxRuntimeExports.jsx(Badge, {
                    variant: ticket.status === "OPEN" ? "destructive" : ticket.status === "WIP" ? "default" : "secondary",
                    children: ticket.status
                  })]
                }), /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
                  className: "text-sm text-gray-600 mb-2",
                  children: ticket.description
                }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                  className: "text-xs text-gray-500",
                  children: ["Created: ", new Date(ticket.createdDate).toLocaleString()]
                })]
              }, ticket.ticketId))
            })]
          })]
        }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
          className: "flex gap-2 pt-4 border-t",
          children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
            variant: "outline",
            onClick: onClose,
            children: "Close"
          }), order.orderStatus === "Created" && /* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
            variant: "destructive",
            onClick: () => onAction(order, "Cancel Order"),
            children: "Cancel Order"
          }), showUpdateStatusButton(order) && /* @__PURE__ */ jsxRuntimeExports.jsxs(Button, {
            variant: "default",
            onClick: () => onAction(order, "Update LiveOrder Status"),
            children: ["Update to ", order.orderStatus === "Created" ? "Accepted" : order.orderStatus === "Accepted" ? "Packed" : order.orderStatus === "Packed" ? "Assigned" : order.orderStatus === "Assigned" ? "PickedUp" : "Dispatched", " ", /* @__PURE__ */ jsxRuntimeExports.jsx(ArrowRight, {
              className: "w-3 h-3"
            })]
          }), order.orderStatus === "Dispatched" && /* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
            variant: "default",
            className: "bg-blue-500 hover:bg-blue-500",
            onClick: () => onAction(order, "Mark Delivered"),
            children: "Mark Delivered"
          })]
        })]
      })]
    })
  });
}
function ActionModal({
  order,
  actionType,
  onClose,
  isSubmitting,
  onSubmit
}) {
  const [formData, setFormData] = reactExports.useState({
    // order cancel
    reason: "",
    // update status to packed
    boxes: "",
    bags: "",
    // mark delivered
    deliveryCode: "",
    creditAmount: "",
    boxesGiven: "",
    boxesTaken: ""
  });
  const handleSubmit = (e) => {
    e.preventDefault();
    onSubmit(formData);
  };
  if (!order || !actionType) return null;
  const renderCancelModal = () => /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
    className: "space-y-4",
    children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
      className: "flex items-center gap-2 p-4 bg-red-50 border border-red-200 rounded",
      children: [/* @__PURE__ */ jsxRuntimeExports.jsx(TriangleAlert, {
        className: "w-5 h-5 text-red-600"
      }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
        children: [/* @__PURE__ */ jsxRuntimeExports.jsx("p", {
          className: "font-medium text-red-800",
          children: "Cancel Order"
        }), /* @__PURE__ */ jsxRuntimeExports.jsx("p", {
          className: "text-sm text-red-600",
          children: "This action is irreversible. The order will be cancelled from all systems."
        })]
      })]
    }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
      children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Label, {
        htmlFor: "reason",
        children: "Cancellation Reason"
      }), /* @__PURE__ */ jsxRuntimeExports.jsx(J, {
        id: "reason",
        value: formData.reason,
        onChange: (e) => setFormData((prev) => ({
          ...prev,
          reason: e.target.value
        })),
        placeholder: "Enter reason for cancellation...",
        className: "mt-1 w-72 block border-2 rounded-md"
      })]
    }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
      className: "flex gap-2 pt-4",
      children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
        variant: "outline",
        onClick: onClose,
        disabled: isSubmitting,
        children: "Cancel"
      }), /* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
        variant: "destructive",
        onClick: handleSubmit,
        disabled: isSubmitting,
        children: isSubmitting ? "Cancelling..." : "Confirm Cancellation"
      })]
    })]
  });
  const renderUpdateStatusModal = () => /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
    className: "space-y-4",
    children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
      className: "flex items-center gap-2 p-4 bg-yellow-50 border border-yellow-200 rounded",
      children: [/* @__PURE__ */ jsxRuntimeExports.jsx(TriangleAlert, {
        className: "w-5 h-5 text-yellow-600"
      }), /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
        children: /* @__PURE__ */ jsxRuntimeExports.jsxs("p", {
          className: "text-yellow-700",
          children: ["This will update the status of the order to ", order.orderStatus === "Created" ? "Accepted" : order.orderStatus === "Accepted" ? "Packed" : order.orderStatus === "Packed" ? "Assigned" : order.orderStatus === "Assigned" ? "PickedUp" : "Dispatched", "."]
        })
      })]
    }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
      className: "flex gap-2 pt-4",
      children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
        variant: "outline",
        onClick: onClose,
        disabled: isSubmitting,
        children: "Cancel"
      }), /* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
        onClick: handleSubmit,
        disabled: isSubmitting,
        children: isSubmitting ? "Updating..." : "Confirm Update"
      })]
    })]
  });
  const renderMarkDeliveredModal = () => /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
    className: "space-y-4",
    children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
      className: "flex items-center gap-2 p-4 bg-yellow-50 border border-yellow-200 rounded",
      children: [/* @__PURE__ */ jsxRuntimeExports.jsx(TriangleAlert, {
        className: "w-5 h-5 text-yellow-600"
      }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
        children: [/* @__PURE__ */ jsxRuntimeExports.jsx("p", {
          className: "font-medium text-yellow-800",
          children: "Mark Order as Delivered"
        }), /* @__PURE__ */ jsxRuntimeExports.jsx("p", {
          className: "text-sm text-yellow-600",
          children: "This will mark the order as delivered."
        })]
      })]
    }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
      className: "flex gap-2 pt-4",
      children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
        variant: "outline",
        onClick: onClose,
        disabled: isSubmitting,
        children: "Cancel"
      }), /* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
        onClick: handleSubmit,
        disabled: isSubmitting,
        className: "bg-blue-500 hover:bg-blue-500",
        children: isSubmitting ? "Marking..." : "Confirm Delivery"
      })]
    })]
  });
  return /* @__PURE__ */ jsxRuntimeExports.jsx(Dialog, {
    open: true,
    onOpenChange: onClose,
    children: /* @__PURE__ */ jsxRuntimeExports.jsxs(DialogContent, {
      className: "max-w-2xl rounded-md",
      children: [/* @__PURE__ */ jsxRuntimeExports.jsx(DialogHeader, {
        children: /* @__PURE__ */ jsxRuntimeExports.jsxs(DialogTitle, {
          children: [actionType === "Cancel Order" && "Cancel Order", actionType === "Update LiveOrder Status" && "Update LiveOrder Status", actionType === "Mark Delivered" && "Mark Delivered"]
        })
      }), actionType === "Cancel Order" && renderCancelModal(), actionType === "Update LiveOrder Status" && renderUpdateStatusModal(), actionType === "Mark Delivered" && renderMarkDeliveredModal()]
    })
  });
}
const getTimeDifferenceInSeconds = (orderTime, endTime) => {
  const orderDate = new Date(orderTime);
  const compareDate = endTime ? new Date(endTime) : /* @__PURE__ */ new Date();
  return Math.floor((compareDate.getTime() - orderDate.getTime()) / 1e3);
};
const formatTimeElapsed = (seconds) => {
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);
  const days = Math.floor(hours / 24);
  if (days > 0) {
    return `${days}d ${hours % 24}h`;
  }
  if (hours > 0) {
    return `${hours}h ${minutes % 60}m`;
  }
  return `${minutes}m`;
};
const getOrderCardStyling = (order) => {
  const timeDiff = getTimeDifferenceInSeconds(order.createdAt);
  const baseClasses = "border rounded-lg transition-all duration-500 ";
  let pulseClasses = "";
  let bgClasses = "bg-white hover:shadow-md ";
  let helperText = "";
  switch (order.orderStatus) {
    case "Created":
      if (timeDiff > 180) {
        bgClasses = "bg-gradient-to-br from-red-200 to-red-50 ";
        pulseClasses = "card-alert-breathing ";
        helperText = "🚨 URGENT: Order not yet accepted. Please inform the seller to either accept or cancel the order.";
      } else if (timeDiff > 120) {
        bgClasses = "bg-gradient-to-br from-amber-200 to-amber-50 ";
        pulseClasses = "card-alert-warning ";
        helperText = "⚠️ Order yet to be accepted. Please inform the seller again.";
      }
      break;
    case "Accepted":
      if (order.logisticProvider === "MP2") {
        if (timeDiff > 900 && order.logStatus && ["LOG_CREATED", "LOG_PENDING", "LOG_SEARCHING_AGENT"].includes(order.logStatus)) {
          bgClasses = "bg-gradient-to-br from-red-200 to-red-50 ";
          pulseClasses = "card-alert-animated ";
          helperText = "🚨 CRITICAL: Please highlight the order with mp2 team or start manual fulfilment of the order.";
        } else if (timeDiff > 600 && order.logStatus && ["LOG_CREATED", "LOG_PENDING", "LOG_SEARCHING_AGENT"].includes(order.logStatus)) {
          bgClasses = "bg-gradient-to-br from-orange-200 to-orange-50 ";
          pulseClasses = "card-alert-warning ";
          helperText = "⚡ Please highlight the order to mp2 team and start preparing for manual deliveries.";
        } else if (timeDiff >= 600 && order.logStatus === "LOG_AGENT_ASSIGNED") {
          pulseClasses = "card-alert-warning ";
          helperText = "📞 Please follow up with the rider and check if he is moving toward the restaurant.";
        }
      } else if (order.logisticProvider === "SELF" && timeDiff > 900) {
        pulseClasses = "card-alert-warning ";
        helperText = "🏪 Please follow up with the seller for self delivery.";
      }
      break;
    case "Packed":
      if (order.logisticProvider === "MP2") {
        if (timeDiff > 1200 && ["LOG_CREATED", "LOG_PENDING", "LOG_SEARCHING_AGENT"].includes(order.logStatus)) {
          bgClasses = "bg-gradient-to-br from-red-200 to-red-50 ";
          pulseClasses = "card-alert-breathing ";
          helperText = "🚨 URGENT: Please check with the delivery partner and raise the issue with the mp2 team.";
        } else if (timeDiff > 1200 && order.logStatus === "LOG_AGENT_ASSIGNED") {
          bgClasses = "bg-gradient-to-br from-red-200 to-red-50 ";
          pulseClasses = "card-alert-animated ";
          helperText = "🏃‍♂️ Please follow up with the rider and check if he is moving toward the restaurant.";
        }
      } else if (order.logisticProvider === "SELF" && timeDiff > 1200) {
        pulseClasses = "card-alert-warning ";
        helperText = "🏃‍♂️ Please follow up with the seller for self delivery.";
      }
      break;
  }
  return {
    className: baseClasses + bgClasses + pulseClasses,
    helperText
  };
};
const getStatusDisplayName = (status) => {
  switch (status) {
    case "Created":
      return "Acceptance Pending";
    case "Packed":
      return "Ready for Pickup";
    default:
      return status;
  }
};
const getLogisticStatusDisplayName = (logStatus) => {
  return logStatus.replace("LOG_", "").replace("_", " ").toLowerCase().split(" ").map((word) => word.charAt(0).toUpperCase() + word.slice(1)).join(" ");
};
const showUpdateStatusButton = (order) => {
  const isPos = order.pos !== "none" && order.pos !== void 0;
  const isLogisticProvider = order.logisticProvider !== "SELF" && order.logisticProvider !== void 0;
  if (isPos && isLogisticProvider) {
    return order.orderStatus === "Created" || order.orderStatus === "Accepted" || order.orderStatus === "Packed" || order.orderStatus === "Assigned" || order.orderStatus === "PickedUp";
  }
  if (!isPos && !isLogisticProvider) {
    return order.orderStatus === "Created" || order.orderStatus === "Accepted" || order.orderStatus === "Packed" || order.orderStatus === "Assigned" || order.orderStatus === "PickedUp";
  }
  if (isPos && !isLogisticProvider) {
    return order.orderStatus === "Created" || order.orderStatus === "Accepted" || order.orderStatus === "Packed" || order.orderStatus === "Assigned" || order.orderStatus === "PickedUp";
  }
  if (!isPos && isLogisticProvider) {
    return order.orderStatus === "Created" || order.orderStatus === "Accepted" || order.orderStatus === "Packed" || order.orderStatus === "Assigned" || order.orderStatus === "PickedUp";
  }
  return order.orderStatus === "Created" || order.orderStatus === "Accepted" || order.orderStatus === "Packed" || order.orderStatus === "Assigned" || order.orderStatus === "PickedUp";
};
export {
  LiveOrderDashboard as default
};
//# sourceMappingURL=sellerSetting.orders-D9a4aVvU.js.map
