import { r as reactExports, j as jsxRuntimeExports } from "./jsx-runtime-bB2y7OuD.js";
import { I as Input } from "./input-3v87qohQ.js";
import { B as Button } from "./button-ByAXMyvk.js";
import { C as Card, b as <PERSON><PERSON><PERSON><PERSON>, c as Card<PERSON><PERSON><PERSON>, d as CardDescription, a as CardContent } from "./card-BJQMSLe_.js";
import { S as ScrollArea } from "./scroll-area-Cyn6sZm1.js";
import { P as Phone } from "./phone-HnpbSr97.js";
import { c as createLucideIcon } from "./createLucideIcon-uwkRm45G.js";
import { M as MapPin } from "./map-pin-BWBTUiG2.js";
import "./utils-GkgzjW3c.js";
import "./index-z_byfFrQ.js";
import "./index-ImHKLo0a.js";
import "./index-CzZ6EYkN.js";
import "./index-Vp2vNLNM.js";
import "./index-QLGF6kQx.js";
import "./index-D7VH9Fc8.js";
import "./index-BTdCMChR.js";
import "./index-IXOTxK3N.js";
/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */
const Mail = createLucideIcon("Mail", [
  ["rect", { width: "20", height: "16", x: "2", y: "4", rx: "2", key: "18n3k1" }],
  ["path", { d: "m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7", key: "1ocrg3" }]
]);
const mockCustomers = [
  {
    id: 1,
    name: "John Doe",
    phone: "+91 **********",
    email: "<EMAIL>",
    address: "123 Main St, City"
  },
  {
    id: 2,
    name: "Jane Smith",
    phone: "+91 **********",
    email: "<EMAIL>",
    address: "456 Oak St, City"
  }
  // Add more mock data as needed
];
function Customers() {
  const [searchTerm, setSearchTerm] = reactExports.useState("");
  const filteredCustomers = mockCustomers.filter((customer) => customer.name.toLowerCase().includes(searchTerm.toLowerCase()));
  return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
    className: "container mx-auto p-4 space-y-4",
    children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
      className: "flex flex-col space-y-4 md:flex-row md:justify-between md:items-center",
      children: [/* @__PURE__ */ jsxRuntimeExports.jsx("h1", {
        className: "text-2xl font-bold",
        children: "My Customers"
      }), /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
        className: "w-full md:w-1/3",
        children: /* @__PURE__ */ jsxRuntimeExports.jsx(Input, {
          type: "search",
          placeholder: "Search customers...",
          value: searchTerm,
          onChange: (e) => setSearchTerm(e.target.value),
          className: "w-full"
        })
      })]
    }), /* @__PURE__ */ jsxRuntimeExports.jsx(ScrollArea, {
      className: "h-[calc(100vh-200px)]",
      children: /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
        className: "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",
        children: filteredCustomers.map((customer) => /* @__PURE__ */ jsxRuntimeExports.jsxs(Card, {
          className: "hover:shadow-lg transition-shadow",
          children: [/* @__PURE__ */ jsxRuntimeExports.jsxs(CardHeader, {
            children: [/* @__PURE__ */ jsxRuntimeExports.jsx(CardTitle, {
              children: customer.name
            }), /* @__PURE__ */ jsxRuntimeExports.jsxs(CardDescription, {
              children: ["Customer ID: #", customer.id]
            })]
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(CardContent, {
            children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
              className: "space-y-2",
              children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                className: "flex items-center space-x-2",
                children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Phone, {
                  className: "h-4 w-4 text-gray-500"
                }), /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                  children: customer.phone
                })]
              }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                className: "flex items-center space-x-2",
                children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Mail, {
                  className: "h-4 w-4 text-gray-500"
                }), /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                  children: customer.email
                })]
              }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                className: "flex items-center space-x-2",
                children: [/* @__PURE__ */ jsxRuntimeExports.jsx(MapPin, {
                  className: "h-4 w-4 text-gray-500"
                }), /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                  children: customer.address
                })]
              }), /* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
                className: "w-full mt-4",
                children: "View Details"
              })]
            })
          })]
        }, customer.id))
      })
    })]
  });
}
export {
  Customers as default
};
//# sourceMappingURL=seller.customers-C7LMgTTF.js.map
