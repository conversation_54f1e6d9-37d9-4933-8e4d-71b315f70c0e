import { r as reactExports, j as jsxRuntimeExports } from "./jsx-runtime-bB2y7OuD.js";
import { S as SuccessDialog, E as ErrorBoundary$1 } from "./ErrorBoundary-CimRxzEi.js";
import { S as SpinnerLoader } from "./SpinnerLoader-CAZRmIJX.js";
import { B as Button } from "./button-ByAXMyvk.js";
import { a as useNavigate } from "./index-DhHTcibu.js";
import { u as useLoaderData } from "./components-D7UvGag_.js";
import "./use-sync-refs-DLXpJTw-.js";
import "./index-z_byfFrQ.js";
import "./index-ImHKLo0a.js";
import "./utils-GkgzjW3c.js";
import "./index-Vp2vNLNM.js";
function PlaceOrder() {
  const navigate = useNavigate();
  const [iframeLoading, setIframeLoading] = reactExports.useState(true);
  const [error, setError] = reactExports.useState("");
  const loader2 = useLoaderData();
  const token = loader2.token;
  const iframeRef = reactExports.useRef(null);
  reactExports.useEffect(() => {
    if (typeof window !== "undefined") {
      const handleMessage = (event) => {
        console.log("Message received from Child:", event.data);
        const message = event.data;
        if (message.type === "PLACE_ORDER") {
          if (message.payload.success) {
            navigate(`/home/<USER>/${loader2.buyerId}`);
          } else {
            setError("Unable to place order");
          }
        }
        if (message.type === "IFRAME_LOADED") {
          if (message.payload.success) {
            setIframeLoading(false);
          } else {
            setIframeLoading(false);
            setError("Something went wrong");
          }
        }
        if (message.type === "IFRAME_ERROR") {
          console.error("IFRAME Error: ", message.payload.message);
          setIframeLoading(false);
          setError(message.payload.message || "Something went wrong");
        }
      };
      window.addEventListener("message", handleMessage);
      return () => {
        window.removeEventListener("message", handleMessage);
      };
    }
  }, []);
  const handleCloseError = () => {
    setError("");
    navigate(-1);
  };
  if (!iframeLoading && error.length) {
    setIframeLoading(false);
    return /* @__PURE__ */ jsxRuntimeExports.jsx(SuccessDialog, {
      title: "Oops",
      message: error || "Something went wrong!",
      onClose: handleCloseError,
      buttonType: "primary",
      buttonText: "Retry"
    });
  }
  console.log(loader2.serverEnv);
  return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
    className: "fixed inset-0 flex items-center justify-center bg-black bg-opacity-40 p-4 overflow-y-scroll z-50 h-full w-full",
    children: [iframeLoading && /* @__PURE__ */ jsxRuntimeExports.jsx(SpinnerLoader, {
      loading: iframeLoading
    }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
      className: "flex flex-col min-h-[80%] min-w-[412px]",
      children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
        className: "flex flex-row justify-between items-center bg-white w-full p-4 h-14 mb-2  rounded-md",
        children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
          className: "text-lg font-semibold",
          children: ["Place order for", /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
            className: "text-teal-500 ml-4",
            children: loader2.iframeData.buyerData.mobileNumber
          })]
        }), /* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
          onClick: () => navigate(-1),
          className: "bg-red-500 px-4 py-3 rounded-md text-lg hover:bg-red-600",
          children: "Cancel"
        })]
      }), /* @__PURE__ */ jsxRuntimeExports.jsx("iframe", {
        ref: iframeRef,
        className: "flex-grow overflow-y-scroll rounded-md w-full",
        title: "chooseitems",
        src: `https://po${loader2.serverEnv === "development" ? "-uat" : ""}.mnetlive.com/seller/chooseitems?token=${token}`
      })]
    })]
  });
}
function ErrorBoundary() {
  const navigate = useNavigate();
  return /* @__PURE__ */ jsxRuntimeExports.jsx(ErrorBoundary$1, {
    onRetry: () => navigate(-1)
  });
}
export {
  ErrorBoundary,
  PlaceOrder as default
};
//# sourceMappingURL=home.place-order-pfe3YbU4.js.map
