function formatIndianNumber(value) {
  return value.toLocaleString("en-IN");
}
function formatWeight(weight) {
  if (weight >= 1e3) {
    return `${(weight / 1e3).toFixed(1)} Ton`;
  } else if (weight >= 1) {
    return `${formatIndianNumber(weight)} Kg`;
  } else {
    return `${(weight * 1e3).toFixed(2)} g`;
  }
}
function formatCurrency(currency) {
  if (currency >= 1e7) {
    return `₹ ${(currency / 1e7).toFixed(1)} Cr`;
  } else if (currency >= 1e5) {
    return `₹ ${(currency / 1e5).toFixed(1)} L`;
  } else {
    return `₹ ${formatIndianNumber(currency)}`;
  }
}
function camelCaseToWords(text) {
  return text.replace(/([A-Z])/g, " $1").replace(/^./, (str) => str.toUpperCase());
}
export {
  formatWeight as a,
  camelCaseToWords as c,
  formatCurrency as f
};
//# sourceMappingURL=format-Da3JpRMs.js.map
