{"version": 3, "file": "index-BXzPK7u0.js", "sources": ["../../../node_modules/@radix-ui/react-use-escape-keydown/dist/index.mjs", "../../../node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs", "../../../node_modules/@radix-ui/react-portal/dist/index.mjs"], "sourcesContent": ["// packages/react/use-escape-keydown/src/useEscapeKeydown.tsx\nimport * as React from \"react\";\nimport { useCallbackRef } from \"@radix-ui/react-use-callback-ref\";\nfunction useEscapeKeydown(onEscapeKeyDownProp, ownerDocument = globalThis?.document) {\n  const onEscapeKeyDown = useCallbackRef(onEscapeKeyDownProp);\n  React.useEffect(() => {\n    const handleKeyDown = (event) => {\n      if (event.key === \"Escape\") {\n        onEscapeKeyDown(event);\n      }\n    };\n    ownerDocument.addEventListener(\"keydown\", handleKeyDown, { capture: true });\n    return () => ownerDocument.removeEventListener(\"keydown\", handleKeyDown, { capture: true });\n  }, [onEscapeKeyDown, ownerDocument]);\n}\nexport {\n  useEscapeKeydown\n};\n//# sourceMappingURL=index.mjs.map\n", "\"use client\";\n\n// packages/react/dismissable-layer/src/dismissable-layer.tsx\nimport * as React from \"react\";\nimport { composeEventHandlers } from \"@radix-ui/primitive\";\nimport { Primitive, dispatchDiscreteCustomEvent } from \"@radix-ui/react-primitive\";\nimport { useComposedRefs } from \"@radix-ui/react-compose-refs\";\nimport { useCallbackRef } from \"@radix-ui/react-use-callback-ref\";\nimport { useEscapeKeydown } from \"@radix-ui/react-use-escape-keydown\";\nimport { jsx } from \"react/jsx-runtime\";\nvar DISMISSABLE_LAYER_NAME = \"DismissableLayer\";\nvar CONTEXT_UPDATE = \"dismissableLayer.update\";\nvar POINTER_DOWN_OUTSIDE = \"dismissableLayer.pointerDownOutside\";\nvar FOCUS_OUTSIDE = \"dismissableLayer.focusOutside\";\nvar originalBodyPointerEvents;\nvar DismissableLayerContext = React.createContext({\n  layers: /* @__PURE__ */ new Set(),\n  layersWithOutsidePointerEventsDisabled: /* @__PURE__ */ new Set(),\n  branches: /* @__PURE__ */ new Set()\n});\nvar DismissableLayer = React.forwardRef(\n  (props, forwardedRef) => {\n    const {\n      disableOutsidePointerEvents = false,\n      onEscapeKeyDown,\n      onPointerDownOutside,\n      onFocusOutside,\n      onInteractOutside,\n      onDismiss,\n      ...layerProps\n    } = props;\n    const context = React.useContext(DismissableLayerContext);\n    const [node, setNode] = React.useState(null);\n    const ownerDocument = node?.ownerDocument ?? globalThis?.document;\n    const [, force] = React.useState({});\n    const composedRefs = useComposedRefs(forwardedRef, (node2) => setNode(node2));\n    const layers = Array.from(context.layers);\n    const [highestLayerWithOutsidePointerEventsDisabled] = [...context.layersWithOutsidePointerEventsDisabled].slice(-1);\n    const highestLayerWithOutsidePointerEventsDisabledIndex = layers.indexOf(highestLayerWithOutsidePointerEventsDisabled);\n    const index = node ? layers.indexOf(node) : -1;\n    const isBodyPointerEventsDisabled = context.layersWithOutsidePointerEventsDisabled.size > 0;\n    const isPointerEventsEnabled = index >= highestLayerWithOutsidePointerEventsDisabledIndex;\n    const pointerDownOutside = usePointerDownOutside((event) => {\n      const target = event.target;\n      const isPointerDownOnBranch = [...context.branches].some((branch) => branch.contains(target));\n      if (!isPointerEventsEnabled || isPointerDownOnBranch) return;\n      onPointerDownOutside?.(event);\n      onInteractOutside?.(event);\n      if (!event.defaultPrevented) onDismiss?.();\n    }, ownerDocument);\n    const focusOutside = useFocusOutside((event) => {\n      const target = event.target;\n      const isFocusInBranch = [...context.branches].some((branch) => branch.contains(target));\n      if (isFocusInBranch) return;\n      onFocusOutside?.(event);\n      onInteractOutside?.(event);\n      if (!event.defaultPrevented) onDismiss?.();\n    }, ownerDocument);\n    useEscapeKeydown((event) => {\n      const isHighestLayer = index === context.layers.size - 1;\n      if (!isHighestLayer) return;\n      onEscapeKeyDown?.(event);\n      if (!event.defaultPrevented && onDismiss) {\n        event.preventDefault();\n        onDismiss();\n      }\n    }, ownerDocument);\n    React.useEffect(() => {\n      if (!node) return;\n      if (disableOutsidePointerEvents) {\n        if (context.layersWithOutsidePointerEventsDisabled.size === 0) {\n          originalBodyPointerEvents = ownerDocument.body.style.pointerEvents;\n          ownerDocument.body.style.pointerEvents = \"none\";\n        }\n        context.layersWithOutsidePointerEventsDisabled.add(node);\n      }\n      context.layers.add(node);\n      dispatchUpdate();\n      return () => {\n        if (disableOutsidePointerEvents && context.layersWithOutsidePointerEventsDisabled.size === 1) {\n          ownerDocument.body.style.pointerEvents = originalBodyPointerEvents;\n        }\n      };\n    }, [node, ownerDocument, disableOutsidePointerEvents, context]);\n    React.useEffect(() => {\n      return () => {\n        if (!node) return;\n        context.layers.delete(node);\n        context.layersWithOutsidePointerEventsDisabled.delete(node);\n        dispatchUpdate();\n      };\n    }, [node, context]);\n    React.useEffect(() => {\n      const handleUpdate = () => force({});\n      document.addEventListener(CONTEXT_UPDATE, handleUpdate);\n      return () => document.removeEventListener(CONTEXT_UPDATE, handleUpdate);\n    }, []);\n    return /* @__PURE__ */ jsx(\n      Primitive.div,\n      {\n        ...layerProps,\n        ref: composedRefs,\n        style: {\n          pointerEvents: isBodyPointerEventsDisabled ? isPointerEventsEnabled ? \"auto\" : \"none\" : void 0,\n          ...props.style\n        },\n        onFocusCapture: composeEventHandlers(props.onFocusCapture, focusOutside.onFocusCapture),\n        onBlurCapture: composeEventHandlers(props.onBlurCapture, focusOutside.onBlurCapture),\n        onPointerDownCapture: composeEventHandlers(\n          props.onPointerDownCapture,\n          pointerDownOutside.onPointerDownCapture\n        )\n      }\n    );\n  }\n);\nDismissableLayer.displayName = DISMISSABLE_LAYER_NAME;\nvar BRANCH_NAME = \"DismissableLayerBranch\";\nvar DismissableLayerBranch = React.forwardRef((props, forwardedRef) => {\n  const context = React.useContext(DismissableLayerContext);\n  const ref = React.useRef(null);\n  const composedRefs = useComposedRefs(forwardedRef, ref);\n  React.useEffect(() => {\n    const node = ref.current;\n    if (node) {\n      context.branches.add(node);\n      return () => {\n        context.branches.delete(node);\n      };\n    }\n  }, [context.branches]);\n  return /* @__PURE__ */ jsx(Primitive.div, { ...props, ref: composedRefs });\n});\nDismissableLayerBranch.displayName = BRANCH_NAME;\nfunction usePointerDownOutside(onPointerDownOutside, ownerDocument = globalThis?.document) {\n  const handlePointerDownOutside = useCallbackRef(onPointerDownOutside);\n  const isPointerInsideReactTreeRef = React.useRef(false);\n  const handleClickRef = React.useRef(() => {\n  });\n  React.useEffect(() => {\n    const handlePointerDown = (event) => {\n      if (event.target && !isPointerInsideReactTreeRef.current) {\n        let handleAndDispatchPointerDownOutsideEvent2 = function() {\n          handleAndDispatchCustomEvent(\n            POINTER_DOWN_OUTSIDE,\n            handlePointerDownOutside,\n            eventDetail,\n            { discrete: true }\n          );\n        };\n        var handleAndDispatchPointerDownOutsideEvent = handleAndDispatchPointerDownOutsideEvent2;\n        const eventDetail = { originalEvent: event };\n        if (event.pointerType === \"touch\") {\n          ownerDocument.removeEventListener(\"click\", handleClickRef.current);\n          handleClickRef.current = handleAndDispatchPointerDownOutsideEvent2;\n          ownerDocument.addEventListener(\"click\", handleClickRef.current, { once: true });\n        } else {\n          handleAndDispatchPointerDownOutsideEvent2();\n        }\n      } else {\n        ownerDocument.removeEventListener(\"click\", handleClickRef.current);\n      }\n      isPointerInsideReactTreeRef.current = false;\n    };\n    const timerId = window.setTimeout(() => {\n      ownerDocument.addEventListener(\"pointerdown\", handlePointerDown);\n    }, 0);\n    return () => {\n      window.clearTimeout(timerId);\n      ownerDocument.removeEventListener(\"pointerdown\", handlePointerDown);\n      ownerDocument.removeEventListener(\"click\", handleClickRef.current);\n    };\n  }, [ownerDocument, handlePointerDownOutside]);\n  return {\n    // ensures we check React component tree (not just DOM tree)\n    onPointerDownCapture: () => isPointerInsideReactTreeRef.current = true\n  };\n}\nfunction useFocusOutside(onFocusOutside, ownerDocument = globalThis?.document) {\n  const handleFocusOutside = useCallbackRef(onFocusOutside);\n  const isFocusInsideReactTreeRef = React.useRef(false);\n  React.useEffect(() => {\n    const handleFocus = (event) => {\n      if (event.target && !isFocusInsideReactTreeRef.current) {\n        const eventDetail = { originalEvent: event };\n        handleAndDispatchCustomEvent(FOCUS_OUTSIDE, handleFocusOutside, eventDetail, {\n          discrete: false\n        });\n      }\n    };\n    ownerDocument.addEventListener(\"focusin\", handleFocus);\n    return () => ownerDocument.removeEventListener(\"focusin\", handleFocus);\n  }, [ownerDocument, handleFocusOutside]);\n  return {\n    onFocusCapture: () => isFocusInsideReactTreeRef.current = true,\n    onBlurCapture: () => isFocusInsideReactTreeRef.current = false\n  };\n}\nfunction dispatchUpdate() {\n  const event = new CustomEvent(CONTEXT_UPDATE);\n  document.dispatchEvent(event);\n}\nfunction handleAndDispatchCustomEvent(name, handler, detail, { discrete }) {\n  const target = detail.originalEvent.target;\n  const event = new CustomEvent(name, { bubbles: false, cancelable: true, detail });\n  if (handler) target.addEventListener(name, handler, { once: true });\n  if (discrete) {\n    dispatchDiscreteCustomEvent(target, event);\n  } else {\n    target.dispatchEvent(event);\n  }\n}\nvar Root = DismissableLayer;\nvar Branch = DismissableLayerBranch;\nexport {\n  Branch,\n  DismissableLayer,\n  DismissableLayerBranch,\n  Root\n};\n//# sourceMappingURL=index.mjs.map\n", "\"use client\";\n\n// packages/react/portal/src/portal.tsx\nimport * as React from \"react\";\nimport ReactDOM from \"react-dom\";\nimport { Primitive } from \"@radix-ui/react-primitive\";\nimport { useLayoutEffect } from \"@radix-ui/react-use-layout-effect\";\nimport { jsx } from \"react/jsx-runtime\";\nvar PORTAL_NAME = \"Portal\";\nvar Portal = React.forwardRef((props, forwardedRef) => {\n  const { container: containerProp, ...portalProps } = props;\n  const [mounted, setMounted] = React.useState(false);\n  useLayoutEffect(() => setMounted(true), []);\n  const container = containerProp || mounted && globalThis?.document?.body;\n  return container ? ReactDOM.createPortal(/* @__PURE__ */ jsx(Primitive.div, { ...portalProps, ref: forwardedRef }), container) : null;\n});\nPortal.displayName = PORTAL_NAME;\nvar Root = Portal;\nexport {\n  Portal,\n  Root\n};\n//# sourceMappingURL=index.mjs.map\n"], "names": ["React.useEffect", "React.createContext", "React.forwardRef", "React.useContext", "React.useState", "jsx", "React.useRef", "useLayoutEffect"], "mappings": ";;;;;AAGA,SAAS,iBAAiB,qBAAqB,gBAAgB,yCAAY,UAAU;AACnF,QAAM,kBAAkB,eAAe,mBAAmB;AAC1DA,eAAAA,UAAgB,MAAM;AACpB,UAAM,gBAAgB,CAAC,UAAU;AAC/B,UAAI,MAAM,QAAQ,UAAU;AAC1B,wBAAgB,KAAK;AAAA,MAC7B;AAAA,IACK;AACD,kBAAc,iBAAiB,WAAW,eAAe,EAAE,SAAS,MAAM;AAC1E,WAAO,MAAM,cAAc,oBAAoB,WAAW,eAAe,EAAE,SAAS,MAAM;AAAA,EAC9F,GAAK,CAAC,iBAAiB,aAAa,CAAC;AACrC;ACJA,IAAI,yBAAyB;AAC7B,IAAI,iBAAiB;AACrB,IAAI,uBAAuB;AAC3B,IAAI,gBAAgB;AACpB,IAAI;AACJ,IAAI,0BAA0BC,aAAAA,cAAoB;AAAA,EAChD,QAAwB,oBAAI,IAAK;AAAA,EACjC,wCAAwD,oBAAI,IAAK;AAAA,EACjE,UAA0B,oBAAI,IAAG;AACnC,CAAC;AACE,IAAC,mBAAmBC,aAAgB;AAAA,EACrC,CAAC,OAAO,iBAAiB;AACvB,UAAM;AAAA,MACJ,8BAA8B;AAAA,MAC9B;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,GAAG;AAAA,IACT,IAAQ;AACJ,UAAM,UAAUC,aAAgB,WAAC,uBAAuB;AACxD,UAAM,CAAC,MAAM,OAAO,IAAIC,aAAAA,SAAe,IAAI;AAC3C,UAAM,iBAAgB,6BAAM,mBAAiB,yCAAY;AACzD,UAAM,GAAG,KAAK,IAAIA,aAAc,SAAC,EAAE;AACnC,UAAM,eAAe,gBAAgB,cAAc,CAAC,UAAU,QAAQ,KAAK,CAAC;AAC5E,UAAM,SAAS,MAAM,KAAK,QAAQ,MAAM;AACxC,UAAM,CAAC,4CAA4C,IAAI,CAAC,GAAG,QAAQ,sCAAsC,EAAE,MAAM,EAAE;AACnH,UAAM,oDAAoD,OAAO,QAAQ,4CAA4C;AACrH,UAAM,QAAQ,OAAO,OAAO,QAAQ,IAAI,IAAI;AAC5C,UAAM,8BAA8B,QAAQ,uCAAuC,OAAO;AAC1F,UAAM,yBAAyB,SAAS;AACxC,UAAM,qBAAqB,sBAAsB,CAAC,UAAU;AAC1D,YAAM,SAAS,MAAM;AACrB,YAAM,wBAAwB,CAAC,GAAG,QAAQ,QAAQ,EAAE,KAAK,CAAC,WAAW,OAAO,SAAS,MAAM,CAAC;AAC5F,UAAI,CAAC,0BAA0B,sBAAuB;AACtD,mEAAuB;AACvB,6DAAoB;AACpB,UAAI,CAAC,MAAM,iBAAkB;AAAA,IAC9B,GAAE,aAAa;AAChB,UAAM,eAAe,gBAAgB,CAAC,UAAU;AAC9C,YAAM,SAAS,MAAM;AACrB,YAAM,kBAAkB,CAAC,GAAG,QAAQ,QAAQ,EAAE,KAAK,CAAC,WAAW,OAAO,SAAS,MAAM,CAAC;AACtF,UAAI,gBAAiB;AACrB,uDAAiB;AACjB,6DAAoB;AACpB,UAAI,CAAC,MAAM,iBAAkB;AAAA,IAC9B,GAAE,aAAa;AAChB,qBAAiB,CAAC,UAAU;AAC1B,YAAM,iBAAiB,UAAU,QAAQ,OAAO,OAAO;AACvD,UAAI,CAAC,eAAgB;AACrB,yDAAkB;AAClB,UAAI,CAAC,MAAM,oBAAoB,WAAW;AACxC,cAAM,eAAgB;AACtB,kBAAW;AAAA,MACnB;AAAA,IACK,GAAE,aAAa;AAChBJ,iBAAAA,UAAgB,MAAM;AACpB,UAAI,CAAC,KAAM;AACX,UAAI,6BAA6B;AAC/B,YAAI,QAAQ,uCAAuC,SAAS,GAAG;AAC7D,sCAA4B,cAAc,KAAK,MAAM;AACrD,wBAAc,KAAK,MAAM,gBAAgB;AAAA,QACnD;AACQ,gBAAQ,uCAAuC,IAAI,IAAI;AAAA,MAC/D;AACM,cAAQ,OAAO,IAAI,IAAI;AACvB,qBAAgB;AAChB,aAAO,MAAM;AACX,YAAI,+BAA+B,QAAQ,uCAAuC,SAAS,GAAG;AAC5F,wBAAc,KAAK,MAAM,gBAAgB;AAAA,QACnD;AAAA,MACO;AAAA,IACF,GAAE,CAAC,MAAM,eAAe,6BAA6B,OAAO,CAAC;AAC9DA,iBAAAA,UAAgB,MAAM;AACpB,aAAO,MAAM;AACX,YAAI,CAAC,KAAM;AACX,gBAAQ,OAAO,OAAO,IAAI;AAC1B,gBAAQ,uCAAuC,OAAO,IAAI;AAC1D,uBAAgB;AAAA,MACjB;AAAA,IACP,GAAO,CAAC,MAAM,OAAO,CAAC;AAClBA,iBAAAA,UAAgB,MAAM;AACpB,YAAM,eAAe,MAAM,MAAM,EAAE;AACnC,eAAS,iBAAiB,gBAAgB,YAAY;AACtD,aAAO,MAAM,SAAS,oBAAoB,gBAAgB,YAAY;AAAA,IACvE,GAAE,EAAE;AACL,WAAuBK,kCAAG;AAAA,MACxB,UAAU;AAAA,MACV;AAAA,QACE,GAAG;AAAA,QACH,KAAK;AAAA,QACL,OAAO;AAAA,UACL,eAAe,8BAA8B,yBAAyB,SAAS,SAAS;AAAA,UACxF,GAAG,MAAM;AAAA,QACV;AAAA,QACD,gBAAgB,qBAAqB,MAAM,gBAAgB,aAAa,cAAc;AAAA,QACtF,eAAe,qBAAqB,MAAM,eAAe,aAAa,aAAa;AAAA,QACnF,sBAAsB;AAAA,UACpB,MAAM;AAAA,UACN,mBAAmB;AAAA,QAC7B;AAAA,MACA;AAAA,IACK;AAAA,EACL;AACA;AACA,iBAAiB,cAAc;AAC/B,IAAI,cAAc;AAClB,IAAI,yBAAyBH,aAAgB,WAAC,CAAC,OAAO,iBAAiB;AACrE,QAAM,UAAUC,aAAgB,WAAC,uBAAuB;AACxD,QAAM,MAAMG,aAAY,OAAC,IAAI;AAC7B,QAAM,eAAe,gBAAgB,cAAc,GAAG;AACtDN,eAAAA,UAAgB,MAAM;AACpB,UAAM,OAAO,IAAI;AACjB,QAAI,MAAM;AACR,cAAQ,SAAS,IAAI,IAAI;AACzB,aAAO,MAAM;AACX,gBAAQ,SAAS,OAAO,IAAI;AAAA,MAC7B;AAAA,IACP;AAAA,EACA,GAAK,CAAC,QAAQ,QAAQ,CAAC;AACrB,SAAuBK,kCAAAA,IAAI,UAAU,KAAK,EAAE,GAAG,OAAO,KAAK,cAAc;AAC3E,CAAC;AACD,uBAAuB,cAAc;AACrC,SAAS,sBAAsB,sBAAsB,gBAAgB,yCAAY,UAAU;AACzF,QAAM,2BAA2B,eAAe,oBAAoB;AACpE,QAAM,8BAA8BC,aAAY,OAAC,KAAK;AACtD,QAAM,iBAAiBA,aAAAA,OAAa,MAAM;AAAA,EAC5C,CAAG;AACDN,eAAAA,UAAgB,MAAM;AACpB,UAAM,oBAAoB,CAAC,UAAU;AACnC,UAAI,MAAM,UAAU,CAAC,4BAA4B,SAAS;AACxD,YAAI,4CAA4C,WAAW;AACzD;AAAA,YACE;AAAA,YACA;AAAA,YACA;AAAA,YACA,EAAE,UAAU,KAAI;AAAA,UACjB;AAAA,QACF;AAED,cAAM,cAAc,EAAE,eAAe,MAAO;AAC5C,YAAI,MAAM,gBAAgB,SAAS;AACjC,wBAAc,oBAAoB,SAAS,eAAe,OAAO;AACjE,yBAAe,UAAU;AACzB,wBAAc,iBAAiB,SAAS,eAAe,SAAS,EAAE,MAAM,MAAM;AAAA,QACxF,OAAe;AACL,oDAA2C;AAAA,QACrD;AAAA,MACA,OAAa;AACL,sBAAc,oBAAoB,SAAS,eAAe,OAAO;AAAA,MACzE;AACM,kCAA4B,UAAU;AAAA,IACvC;AACD,UAAM,UAAU,OAAO,WAAW,MAAM;AACtC,oBAAc,iBAAiB,eAAe,iBAAiB;AAAA,IAChE,GAAE,CAAC;AACJ,WAAO,MAAM;AACX,aAAO,aAAa,OAAO;AAC3B,oBAAc,oBAAoB,eAAe,iBAAiB;AAClE,oBAAc,oBAAoB,SAAS,eAAe,OAAO;AAAA,IAClE;AAAA,EACL,GAAK,CAAC,eAAe,wBAAwB,CAAC;AAC5C,SAAO;AAAA;AAAA,IAEL,sBAAsB,MAAM,4BAA4B,UAAU;AAAA,EACnE;AACH;AACA,SAAS,gBAAgB,gBAAgB,gBAAgB,yCAAY,UAAU;AAC7E,QAAM,qBAAqB,eAAe,cAAc;AACxD,QAAM,4BAA4BM,aAAY,OAAC,KAAK;AACpDN,eAAAA,UAAgB,MAAM;AACpB,UAAM,cAAc,CAAC,UAAU;AAC7B,UAAI,MAAM,UAAU,CAAC,0BAA0B,SAAS;AACtD,cAAM,cAAc,EAAE,eAAe,MAAO;AAC5C,qCAA6B,eAAe,oBAAoB,aAAa;AAAA,UAC3E,UAAU;AAAA,QACpB,CAAS;AAAA,MACT;AAAA,IACK;AACD,kBAAc,iBAAiB,WAAW,WAAW;AACrD,WAAO,MAAM,cAAc,oBAAoB,WAAW,WAAW;AAAA,EACzE,GAAK,CAAC,eAAe,kBAAkB,CAAC;AACtC,SAAO;AAAA,IACL,gBAAgB,MAAM,0BAA0B,UAAU;AAAA,IAC1D,eAAe,MAAM,0BAA0B,UAAU;AAAA,EAC1D;AACH;AACA,SAAS,iBAAiB;AACxB,QAAM,QAAQ,IAAI,YAAY,cAAc;AAC5C,WAAS,cAAc,KAAK;AAC9B;AACA,SAAS,6BAA6B,MAAM,SAAS,QAAQ,EAAE,SAAQ,GAAI;AACzE,QAAM,SAAS,OAAO,cAAc;AACpC,QAAM,QAAQ,IAAI,YAAY,MAAM,EAAE,SAAS,OAAO,YAAY,MAAM,QAAQ;AAChF,MAAI,QAAS,QAAO,iBAAiB,MAAM,SAAS,EAAE,MAAM,MAAM;AAClE,MAAI,UAAU;AACZ,gCAA4B,QAAQ,KAAK;AAAA,EAC7C,OAAS;AACL,WAAO,cAAc,KAAK;AAAA,EAC9B;AACA;AACG,IAAC,OAAO;AACR,IAAC,SAAS;AC7Mb,IAAI,cAAc;AACf,IAAC,SAASE,aAAgB,WAAC,CAAC,OAAO,iBAAiB;;AACrD,QAAM,EAAE,WAAW,eAAe,GAAG,YAAa,IAAG;AACrD,QAAM,CAAC,SAAS,UAAU,IAAIE,aAAAA,SAAe,KAAK;AAClDG,mBAAgB,MAAM,WAAW,IAAI,GAAG,CAAA,CAAE;AAC1C,QAAM,YAAY,iBAAiB,aAAW,8CAAY,aAAZ,mBAAsB;AACpE,SAAO,YAAY,SAAS,aAA6BF,kCAAG,IAAC,UAAU,KAAK,EAAE,GAAG,aAAa,KAAK,aAAc,CAAA,GAAG,SAAS,IAAI;AACnI,CAAC;AACD,OAAO,cAAc;", "x_google_ignoreList": [0, 1, 2]}