{"version": 3, "file": "chevron-right-B-tR7Kir.js", "sources": ["../../../node_modules/lucide-react/dist/esm/icons/chevron-right.js"], "sourcesContent": ["/**\n * @license lucide-react v0.453.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst ChevronRight = createLucideIcon(\"ChevronRight\", [\n  [\"path\", { d: \"m9 18 6-6-6-6\", key: \"mthhwq\" }]\n]);\n\nexport { ChevronRight as default };\n//# sourceMappingURL=chevron-right.js.map\n"], "names": [], "mappings": ";AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASK,MAAC,eAAe,iBAAiB,gBAAgB;AAAA,EACpD,CAAC,QAAQ,EAAE,GAAG,iBAAiB,KAAK,SAAU,CAAA;AAChD,CAAC;", "x_google_ignoreList": [0]}