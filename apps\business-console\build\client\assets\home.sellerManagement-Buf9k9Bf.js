import { r as reactExports, j as jsxRuntimeExports } from "./jsx-runtime-bB2y7OuD.js";
import { T as Table, a as TableHeader, b as TableRow, c as TableHead, d as TableBody, e as TableCell } from "./table-CAxdJZsY.js";
import { B as Button } from "./button-ByAXMyvk.js";
import { I as Input } from "./input-3v87qohQ.js";
import { D as Dialog, a as DialogContent, c as DialogHeader, b as DialogTitle } from "./dialog-BqKosxNq.js";
import { L as Label } from "./label-cSASrwzW.js";
import { u as useToast } from "./ToastProvider-rciVe3-g.js";
import { S as SpinnerLoader } from "./SpinnerLoader-CAZRmIJX.js";
import { R as RadioGroup, a as RadioGroupItem } from "./radio-group-ChzooXbR.js";
import { a as useFetcher, F as Form, u as useLoaderData, L as Link } from "./components-D7UvGag_.js";
import { S as Switch } from "./switch-DQ_qW6ch.js";
import { a as useNavigate } from "./index-DhHTcibu.js";
import { S as Search } from "./search-DzDJ71yc.js";
import "./utils-GkgzjW3c.js";
import "./index-z_byfFrQ.js";
import "./index-ImHKLo0a.js";
import "./index-DdafHWkt.js";
import "./index-D7VH9Fc8.js";
import "./index-DVTNuYOr.js";
import "./index-dIGjFc0I.js";
import "./index-BXzPK7u0.js";
import "./index-CzZ6EYkN.js";
import "./index-Vp2vNLNM.js";
import "./index-CEHS9zzk.js";
import "./index-QLGF6kQx.js";
import "./x-CCG_WJDF.js";
import "./createLucideIcon-uwkRm45G.js";
import "./index-CG37gmC0.js";
import "./index-qCtcpOcW.js";
import "./index-BTdCMChR.js";
import "./index-CkL5tk39.js";
import "./index-CpKiYcZd.js";
const initialFormState = {
  name: "",
  address: "",
  email: "",
  customerSupportNumber: "",
  owner: {
    firstName: "",
    lastName: "",
    email: "",
    mobileNumber: "",
    address: ""
  },
  latitude: "",
  longitude: "",
  ondcDomain: "RET10",
  pincode: ""
};
function CreateSeller({ isOpen, onClose, seller }) {
  const [formData, setFormData] = reactExports.useState({
    id: (seller == null ? void 0 : seller.id) || void 0,
    name: (seller == null ? void 0 : seller.name) || "",
    address: (seller == null ? void 0 : seller.address) || "",
    email: (seller == null ? void 0 : seller.email) || "",
    customerSupportNumber: (seller == null ? void 0 : seller.customerSupportNumber) || "",
    owner: {
      firstName: (seller == null ? void 0 : seller.owner.firstName) || "",
      lastName: (seller == null ? void 0 : seller.owner.lastName) || "",
      email: (seller == null ? void 0 : seller.owner.email) || "",
      mobileNumber: (seller == null ? void 0 : seller.owner.mobileNumber) || "",
      address: (seller == null ? void 0 : seller.owner.address) || ""
    },
    latitude: (seller == null ? void 0 : seller.latitude) || "",
    longitude: (seller == null ? void 0 : seller.longitude) || "",
    ondcDomain: (seller == null ? void 0 : seller.ondcDomain) || "RET10",
    pincode: (seller == null ? void 0 : seller.pincode) || ""
  });
  const fetcher = useFetcher();
  const [errors, setErrors] = reactExports.useState({});
  const isEditMode = !!(seller == null ? void 0 : seller.id);
  const isLoading = fetcher.state !== "idle";
  const { showToast } = useToast();
  reactExports.useEffect(() => {
    var _a, _b, _c, _d;
    if (fetcher.state === "idle" && fetcher.data) {
      if (((_a = fetcher == null ? void 0 : fetcher.data) == null ? void 0 : _a.success) === true) {
        showToast("Seller created successfully", "success");
        setFormData(initialFormState);
        setErrors({});
        onClose();
      } else if (((_b = fetcher == null ? void 0 : fetcher.data) == null ? void 0 : _b.success) === false && ((_c = fetcher == null ? void 0 : fetcher.data) == null ? void 0 : _c.error)) {
        showToast((_d = fetcher.data) == null ? void 0 : _d.error, "error");
      } else {
        showToast("Unexpected response from server", "error");
      }
    }
  }, [fetcher.state, fetcher.data, onClose]);
  reactExports.useEffect(() => {
    if (!isOpen) {
      setFormData(initialFormState);
    }
  }, [isOpen]);
  const handleChange = (e) => {
    const { name, value } = e.target;
    if (name.startsWith("owner.")) {
      const field = name.split(".")[1];
      setFormData((prev) => ({
        ...prev,
        owner: { ...prev.owner, [field]: value }
      }));
    } else {
      setFormData((prev) => ({ ...prev, [name]: value }));
    }
  };
  const validateForm = () => {
    let newErrors = {};
    if (!formData.name) newErrors.name = "Name is required";
    if (!formData.address) {
      newErrors.address = "Address is required";
    } else if (!/^[A-Za-z0-9\s]{25,}$/.test(formData.address)) {
      newErrors.address = "Address should be atleast 25 characters and contains only 0-9 and A-Z";
    }
    if (!formData.email) newErrors.email = "Email is required";
    if (!formData.customerSupportNumber) newErrors.customerSupportNumber = "Customer Support Number is required";
    if (!formData.owner.firstName) newErrors["owner.firstName"] = "First Name is required";
    if (!formData.owner.lastName) newErrors["owner.lastName"] = "Last Name is required";
    if (!formData.owner.email) newErrors["owner.email"] = "Owner Email is required";
    if (!formData.owner.mobileNumber) {
      newErrors["owner.mobileNumber"] = "Mobile Number is required";
    } else if (!/^\d{10}$/.test(formData.owner.mobileNumber)) {
      newErrors["owner.mobileNumber"] = "Mobile Number must be 10 digits";
    }
    if (formData.ondcDomain === "RET11") {
      if (!formData.pincode) {
        newErrors.pincode = "Pincode is required";
      } else if (formData.pincode.length !== 6) {
        newErrors.pincode = "Pincode must be 6 digits";
      }
    }
    if (!formData.latitude) newErrors.latitude = "Latitude is required";
    if (!formData.longitude) newErrors.longitude = "Longitude is required";
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };
  const handleSubmit = (e) => {
    e.preventDefault();
    if (!validateForm()) return;
    const data = new FormData();
    Object.entries(formData).forEach(([key, value]) => {
      if (typeof value === "object") {
        Object.entries(value).forEach(([subKey, subValue]) => {
          data.append(`owner.${subKey}`, String(subValue));
        });
      } else {
        data.append(key, String(value));
      }
    });
    data.append("roles", "SellerOwner");
    data.append("actionType", "createNewSeller");
    fetcher.submit(data, { method: isEditMode ? "put" : "post" });
  };
  return /* @__PURE__ */ jsxRuntimeExports.jsx(Dialog, { open: isOpen, onOpenChange: onClose, children: /* @__PURE__ */ jsxRuntimeExports.jsxs(DialogContent, { className: "max-h-[90vh] max-w-screen-md flex flex-col p-0", children: [
    isLoading && /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "absolute inset-0 flex items-center justify-center bg-white/10 backdrop-blur-sm z-50", children: /* @__PURE__ */ jsxRuntimeExports.jsx(SpinnerLoader, { loading: isLoading }) }),
    /* @__PURE__ */ jsxRuntimeExports.jsx(DialogHeader, { className: "sticky top-0 bg-white z-10 px-6 py-4 border-b", children: /* @__PURE__ */ jsxRuntimeExports.jsx(DialogTitle, { className: "font-bold text-lg", children: "Create Seller" }) }),
    /* @__PURE__ */ jsxRuntimeExports.jsxs(Form, { onSubmit: handleSubmit, className: "flex-1 overflow-y-auto space-y-4 p-6", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx("h3", { className: "font-semibold", children: "Business Type" }),
      /* @__PURE__ */ jsxRuntimeExports.jsxs(
        RadioGroup,
        {
          value: formData.ondcDomain,
          onValueChange: (val) => setFormData((prev) => ({ ...prev, ondcDomain: val })),
          className: "flex flex-row gap-5",
          children: [
            /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center space-x-2", children: [
              /* @__PURE__ */ jsxRuntimeExports.jsx(RadioGroupItem, { id: "type-RET10", value: "RET10" }),
              /* @__PURE__ */ jsxRuntimeExports.jsx(Label, { htmlFor: "type-RET10", children: "Non-Restaurant" })
            ] }),
            /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center space-x-2", children: [
              /* @__PURE__ */ jsxRuntimeExports.jsx(RadioGroupItem, { id: "type-RET11", value: "RET11" }),
              /* @__PURE__ */ jsxRuntimeExports.jsx(Label, { htmlFor: "type-RET11", children: "Restaurant" })
            ] })
          ]
        }
      ),
      /* @__PURE__ */ jsxRuntimeExports.jsx("h3", { className: "font-semibold", children: "Business Details" }),
      /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "space-y-3 ", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex  flex-col md:flex-row  gap-3", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsxs(Label, { className: " flex-1 flex flex-col gap-y-2", children: [
            "Name",
            /* @__PURE__ */ jsxRuntimeExports.jsx(Input, { type: "text", name: "name", placeholder: "Name", value: formData.name, onChange: handleChange, className: errors.name ? "border-red-500" : "" }),
            errors.name && /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "text-red-500 text-sm", children: errors.name })
          ] }),
          /* @__PURE__ */ jsxRuntimeExports.jsxs(Label, { className: " flex-1 flex flex-col gap-y-2", children: [
            "Address",
            /* @__PURE__ */ jsxRuntimeExports.jsx(Input, { type: "text", name: "address", placeholder: "Address", value: formData.address, onChange: handleChange, required: true, className: errors.address ? "border-red-500" : "" }),
            errors.address && /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "text-red-500 text-sm", children: errors.address })
          ] })
        ] }),
        /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex flex-col md:flex-row gap-3", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsxs(Label, { className: "flex-1 flex flex-col gap-y-2", children: [
            "Email",
            /* @__PURE__ */ jsxRuntimeExports.jsx(Input, { type: "email", name: "email", placeholder: "Email", onChange: handleChange, required: true, className: errors.email ? "border-red-500" : "" }),
            errors.email && /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "text-red-500 text-sm", children: errors.email })
          ] }),
          /* @__PURE__ */ jsxRuntimeExports.jsxs(Label, { className: " flex-1  flex flex-col gap-y-2", children: [
            "Customer Support Number",
            /* @__PURE__ */ jsxRuntimeExports.jsx(
              Input,
              {
                type: "number",
                name: "customerSupportNumber",
                value: formData.customerSupportNumber,
                onChange: (e) => {
                  const value = e.target.value.replace(/\D/g, "");
                  if (value.length <= 10) {
                    setFormData((prev) => ({ ...prev, customerSupportNumber: value }));
                  }
                },
                className: errors.customerSupportNumber ? "border-red-500" : "",
                placeholder: "Enter 10-digit number"
              }
            ),
            errors.customerSupportNumber && /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "text-red-500 text-sm", children: errors.customerSupportNumber })
          ] })
        ] })
      ] }),
      /* @__PURE__ */ jsxRuntimeExports.jsx("h3", { className: "font-semibold", children: "Owner Details" }),
      /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "space-y-3", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex flex-col md:flex-row gap-3", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsxs(Label, { className: " flex-1 flex flex-col gap-y-2", children: [
            "First Name",
            /* @__PURE__ */ jsxRuntimeExports.jsx(Input, { type: "text", name: "owner.firstName", placeholder: "First Name", onChange: handleChange, required: true })
          ] }),
          /* @__PURE__ */ jsxRuntimeExports.jsxs(Label, { className: "flex-1  flex flex-col gap-y-2", children: [
            "Last Name",
            /* @__PURE__ */ jsxRuntimeExports.jsx(Input, { type: "text", name: "owner.lastName", placeholder: "Last Name", onChange: handleChange, required: true })
          ] })
        ] }),
        /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: " flex flex-col md:flex-row gap-3", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsxs(Label, { className: " flex-1 flex flex-col gap-y-2", children: [
            "Owner Email",
            /* @__PURE__ */ jsxRuntimeExports.jsx(Input, { type: "email", name: "owner.email", placeholder: "Owner Email", onChange: handleChange, required: true })
          ] }),
          /* @__PURE__ */ jsxRuntimeExports.jsxs(Label, { className: " flex-1  flex flex-col gap-y-2", children: [
            "Owner Mobile Number",
            /* @__PURE__ */ jsxRuntimeExports.jsx(
              Input,
              {
                type: "number",
                name: "owner.mobileNumber",
                value: formData.owner.mobileNumber,
                onChange: (e) => {
                  const value = e.target.value.replace(/\D/g, "");
                  if (value.length <= 10) {
                    setFormData((prev) => ({
                      ...prev,
                      owner: {
                        ...prev.owner,
                        mobileNumber: value
                      }
                    }));
                  }
                },
                placeholder: "Owner MobileNumber",
                className: errors["owner.mobileNumber"] ? "border-red-500" : ""
              }
            ),
            errors["owner.mobileNumber"] && /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "text-red-500 text-sm", children: errors["owner.mobileNumber"] })
          ] })
        ] }),
        /* @__PURE__ */ jsxRuntimeExports.jsxs(Label, { children: [
          "Owner Address",
          /* @__PURE__ */ jsxRuntimeExports.jsx(Input, { type: "text", name: "owner.address", placeholder: "Owner Address", onChange: handleChange, required: true })
        ] })
      ] }),
      /* @__PURE__ */ jsxRuntimeExports.jsx("h3", { className: "font-semibold", children: "Location" }),
      formData.ondcDomain === "RET11" && /* @__PURE__ */ jsxRuntimeExports.jsxs(Label, { className: "flex-1 flex flex-col  gap-y-3 w-fit", children: [
        "Pincode",
        /* @__PURE__ */ jsxRuntimeExports.jsx(Input, { type: "text", name: "pincode", placeholder: "Pincode", onChange: handleChange, required: true }),
        errors.pincode && /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "text-red-500 text-sm", children: errors.pincode })
      ] }),
      /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: " flex flex-col md:flex-row gap-3", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsxs(Label, { className: "flex-1 flex flex-col  gap-y-3 tex-", children: [
          "Latitude",
          /* @__PURE__ */ jsxRuntimeExports.jsx(Input, { type: "text", name: "latitude", placeholder: "Latitude", onChange: handleChange, required: true })
        ] }),
        /* @__PURE__ */ jsxRuntimeExports.jsxs(Label, { className: "flex-1 flex  flex-col gap-y-3", children: [
          "Longitude",
          /* @__PURE__ */ jsxRuntimeExports.jsx(Input, { type: "text", name: "longitude", placeholder: "Longitude", onChange: handleChange, required: true })
        ] })
      ] }),
      /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "flex justify-end", children: /* @__PURE__ */ jsxRuntimeExports.jsx(Button, { type: "submit", className: "rounded-full hover:bg-blue-400  bg-blue-300", loading: isLoading, disabled: isLoading, children: isEditMode ? "Update Seller" : "Create Seller" }) })
    ] })
  ] }) });
}
function SellerManagementPage() {
  const {
    showToast
  } = useToast();
  const {
    sellerData
  } = useLoaderData();
  sellerData.length;
  const [isModalOpen, setIsModalOpen] = reactExports.useState(false);
  const goTo = useNavigate();
  const [isSuccess, setIsSuccess] = reactExports.useState(false);
  const [sellerStates, setSellerStates] = reactExports.useState(() => {
    return Array.isArray(sellerData) ? sellerData.reduce((acc, seller) => {
      acc[seller.id] = seller.enabled;
      return acc;
    }, {}) : {};
  });
  const fetcher = useFetcher();
  const handleSwitch = async (sellerId) => {
    setSellerStates((prev) => ({
      ...prev,
      [sellerId]: !prev[sellerId]
      // Toggle status before request completes
    }));
    const formData = new FormData();
    formData.append("sellerId", sellerId.toString());
    formData.append("actionType", "updateSellerStatus");
    try {
      await fetcher.submit(formData, {
        method: "put"
      });
      if (fetcher.state === "idle") {
        showToast("sellerStatus Updated SuccessFully", "success");
      }
    } catch (error) {
      console.error("Failed to update status:", error);
      setSellerStates((prev) => ({
        ...prev,
        [sellerId]: !prev[sellerId]
      }));
    }
  };
  const handleSubmitPushMenu = (row) => {
    console.log(",,,,,,,,,,,,,,,,,,");
    const formData = new FormData();
    setUniQId(row);
    formData.append("actionType", "pushMenu");
    formData.append("sellerId", row.toString());
    fetcher.submit(formData, {
      method: "POST"
    });
  };
  reactExports.useEffect(() => {
    if (fetcher.state === "idle" && fetcher.data && "sellerData" in fetcher.data) {
      const data = fetcher.data;
      setSellerStates((prev) => (data.sellerData || []).reduce((acc, seller) => {
        return acc;
      }, {}));
    }
  }, [fetcher.state, fetcher.data]);
  const [searchTerm, setSearchTerm] = reactExports.useState("");
  const [sellerFilter, setSellerFilter] = reactExports.useState([]);
  const handleSearch = (val) => {
    setSearchTerm(val);
  };
  const [uniQId, setUniQId] = reactExports.useState(0);
  reactExports.useEffect(() => {
    if (sellerData) {
      setSellerFilter(sellerData);
    }
  }, [sellerData]);
  reactExports.useEffect(() => {
    if (typeof searchTerm === "string" && searchTerm.length >= 3) {
      const filteredSellers = sellerData == null ? void 0 : sellerData.filter((item) => (item == null ? void 0 : item.name) && typeof item.name === "string" && item.name.toLowerCase().includes(searchTerm.toLowerCase()));
      setSellerFilter(filteredSellers || []);
      console.log(filteredSellers, "Filtered Sellers");
    } else {
      setSellerFilter(sellerData || []);
      console.log(sellerData, "All Sellers");
    }
  }, [searchTerm, sellerData]);
  reactExports.useEffect(() => {
    var _a, _b;
    if ((_a = fetcher == null ? void 0 : fetcher.data) == null ? void 0 : _a.success) {
      setIsSuccess(true);
      console.log(fetcher.data, "fetcherData.......");
      showToast("sucessFully Pushed Menu", "success");
    } else if (((_b = fetcher.data) == null ? void 0 : _b.success) === false) {
      setIsSuccess(false);
      showToast("Failed to Push Menu", "error");
    }
  }, [fetcher.data]);
  const loading = fetcher.state !== "idle";
  return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
    className: "container mx-auto p-6",
    children: [loading && /* @__PURE__ */ jsxRuntimeExports.jsx(SpinnerLoader, {
      loading
    }), /* @__PURE__ */ jsxRuntimeExports.jsx("h1", {
      className: "text-2xl font-bold mb-4",
      children: "Seller Management"
    }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
      className: "flex flex-col my-3 relative ",
      children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Search, {
        className: "absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
      }), /* @__PURE__ */ jsxRuntimeExports.jsx(Input, {
        type: "search",
        placeholder: "Search by Seller Name",
        value: searchTerm,
        onChange: (e) => handleSearch(e.target.value),
        className: "max-w-sm rounded-full pl-10  ",
        autoFocus: true
      })]
    }), /* @__PURE__ */ jsxRuntimeExports.jsxs(Table, {
      children: [/* @__PURE__ */ jsxRuntimeExports.jsx(TableHeader, {
        className: "bg-gray-100",
        children: /* @__PURE__ */ jsxRuntimeExports.jsxs(TableRow, {
          children: [/* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
            children: "ID"
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
            children: "Name"
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
            children: "Wallet Balance"
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
            children: "Stock Management"
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
            children: "Status"
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {})]
        })
      }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableBody, {
        children: sellerFilter.length > 0 ? sellerFilter.map((seller) => /* @__PURE__ */ jsxRuntimeExports.jsxs(TableRow, {
          children: [/* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
            children: seller.id
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
            className: `cursor-pointer text-blue-500 font-bold  items-center`,
            onClick: () => goTo(`/home/<USER>
            children: seller.name
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
            children: seller.walletBalance > 0 ? seller.walletBalance : "-"
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
            children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
              className: "flex gap-2",
              children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Link, {
                to: `/home/<USER>
                children: /* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
                  variant: "outline",
                  size: "sm",
                  className: "hover:bg-primary hover:text-white transition-colors",
                  children: "MyStock"
                })
              }), /* @__PURE__ */ jsxRuntimeExports.jsx(Link, {
                to: `/home/<USER>
                children: /* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
                  variant: "outline",
                  size: "sm",
                  className: "hover:bg-primary hover:text-white transition-colors",
                  children: "StockWithMe"
                })
              })]
            })
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
            children: /* @__PURE__ */ jsxRuntimeExports.jsx(Switch, {
              checked: sellerStates[seller.id],
              onCheckedChange: () => handleSwitch(seller.id)
            })
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
            children: /* @__PURE__ */ jsxRuntimeExports.jsx("button", {
              className: `
          px-6 py-2 rounded-full font-bold text-white
          ${isSuccess && seller.id === uniQId ? "bg-green-600" : "bg-blue-600"}
          hover:bg-blue-700
          disabled:bg-gray-400 disabled:cursor-not-allowed
        `,
              onClick: () => handleSubmitPushMenu(seller == null ? void 0 : seller.id),
              disabled: loading,
              children: /* @__PURE__ */ jsxRuntimeExports.jsxs("span", {
                className: "flex items-center justify-center",
                children: [loading && seller.id === uniQId ? /* @__PURE__ */ jsxRuntimeExports.jsxs("svg", {
                  className: "h-5 w-5 mr-2 text-white",
                  xmlns: "http://www.w3.org/2000/svg",
                  fill: "none",
                  viewBox: "0 0 24 24",
                  children: [/* @__PURE__ */ jsxRuntimeExports.jsx("circle", {
                    className: "opacity-25",
                    cx: "12",
                    cy: "12",
                    r: "10",
                    stroke: "currentColor",
                    strokeWidth: "4"
                  }), /* @__PURE__ */ jsxRuntimeExports.jsx("path", {
                    className: "opacity-75",
                    fill: "currentColor",
                    d: "M4 12a8 8 0 018-8v8h8a8 8 0 01-16 0z"
                  })]
                }) : isSuccess && seller.id === uniQId ? /* @__PURE__ */ jsxRuntimeExports.jsx("svg", {
                  className: "h-5 w-5 mr-2",
                  xmlns: "http://www.w3.org/2000/svg",
                  fill: "none",
                  viewBox: "0 0 24 24",
                  stroke: "currentColor",
                  children: /* @__PURE__ */ jsxRuntimeExports.jsx("path", {
                    strokeLinecap: "round",
                    strokeLinejoin: "round",
                    strokeWidth: "2",
                    d: "M5 13l4 4L19 7"
                  })
                }) : null, loading && seller.id === uniQId ? "Pushing..." : isSuccess && seller.id === uniQId ? "Pushed!" : "Push Menu"]
              })
            })
          })]
        }, seller.id)) : /* @__PURE__ */ jsxRuntimeExports.jsx(TableRow, {
          children: /* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
            colSpan: 6,
            className: "h-24 text-center",
            children: "No results."
          })
        })
      })]
    }), /* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
      className: "fixed bottom-5 right-5 rounded-full cursor-pointer",
      onClick: () => setIsModalOpen(true),
      children: "+ Add Seller"
    }), /* @__PURE__ */ jsxRuntimeExports.jsx(CreateSeller, {
      isOpen: isModalOpen,
      onClose: () => setIsModalOpen(false)
    })]
  });
}
export {
  SellerManagementPage as default
};
//# sourceMappingURL=home.sellerManagement-Buf9k9Bf.js.map
