import { r as reactExports, j as jsxRuntimeExports } from "./jsx-runtime-bB2y7OuD.js";
import { C as Card, b as <PERSON><PERSON><PERSON><PERSON>, c as <PERSON><PERSON><PERSON><PERSON>, d as CardDes<PERSON>, a as CardContent } from "./card-BJQMSLe_.js";
import { S as Select, a as SelectTrigger, b as SelectValue, c as SelectContent, d as SelectItem } from "./select-BFGSXKcr.js";
import { B as Button } from "./button-ByAXMyvk.js";
import { C as Checkbox } from "./checkbox-DoRUSdrQ.js";
import { d as decodePolygon, L as LoadScript, G as GoogleMap, P as Polygon } from "./polyline-utils-DkQLXyiU.js";
import { u as useLoaderData, b as useSearchParams, F as Form } from "./components-D7UvGag_.js";
import { a as useNavigate } from "./index-DhHTcibu.js";
import { A as ArrowLeft } from "./arrow-left-D_Ztdrp5.js";
import { A as ArrowUpDown } from "./arrow-up-down-DLQ-Vrdr.js";
import "./utils-GkgzjW3c.js";
import "./index-Vp2vNLNM.js";
import "./index-IXOTxK3N.js";
import "./index-D7VH9Fc8.js";
import "./index-qCtcpOcW.js";
import "./index-z_byfFrQ.js";
import "./index-BTdCMChR.js";
import "./index-BXzPK7u0.js";
import "./index-CzZ6EYkN.js";
import "./index-CEHS9zzk.js";
import "./index-DVTNuYOr.js";
import "./index-DscYByPT.js";
import "./index-CkL5tk39.js";
import "./index-dIGjFc0I.js";
import "./index-CpKiYcZd.js";
import "./index-C88PRvfd.js";
import "./chevron-down-pCP5jmjX.js";
import "./createLucideIcon-uwkRm45G.js";
import "./check-_dbWxIzT.js";
import "./index-ImHKLo0a.js";
import "./index-QLGF6kQx.js";
const BANGALORE_CENTER = {
  lat: 12.9716,
  lng: 77.5946
};
function AttachLocality() {
  var _a;
  const {
    states,
    districts,
    googleMapsApiKey,
    globalAreas = []
  } = useLoaderData();
  const [searchParams, setSearchParams] = useSearchParams();
  const [selectedState, setSelectedState] = reactExports.useState(searchParams.get("state") || "");
  const [selectedDistrict, setSelectedDistrict] = reactExports.useState(searchParams.get("district") || "");
  const [selectedAreaIds, setSelectedAreaIds] = reactExports.useState(/* @__PURE__ */ new Set());
  const [map, setMap] = reactExports.useState(null);
  const [mapLoaded, setMapLoaded] = reactExports.useState(false);
  const [sortBy, setSortBy] = reactExports.useState("name");
  const [sortOrder, setSortOrder] = reactExports.useState("asc");
  const navigate = useNavigate();
  const areasWithPolygons = reactExports.useMemo(() => globalAreas.filter((area) => area.polygon), [globalAreas]);
  const handleStateChange = (state) => {
    setSelectedState(state);
    setSelectedDistrict("");
    setSelectedAreaIds(/* @__PURE__ */ new Set());
    setSearchParams({
      state
    });
  };
  const handleDistrictChange = (district) => {
    setSelectedDistrict(district);
    setSearchParams({
      state: selectedState,
      district
    });
    setSelectedAreaIds(/* @__PURE__ */ new Set());
  };
  const handleSubmit = (e) => {
    e.preventDefault();
    const formData = new FormData();
    selectedAreaIds.forEach((id) => formData.append("areaIds", id.toString()));
    fetch("/home/<USER>/attach", {
      method: "POST",
      body: formData
    }).then(() => {
      navigate("/home/<USER>");
    });
  };
  const toggleAreaSelection = reactExports.useCallback((areaId) => {
    setSelectedAreaIds((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(areaId)) {
        newSet.delete(areaId);
      } else {
        newSet.add(areaId);
      }
      return newSet;
    });
  }, []);
  const fitBounds = reactExports.useCallback(() => {
    if (map && areasWithPolygons.length > 0) {
      const bounds = new google.maps.LatLngBounds();
      const areasToFit = selectedAreaIds.size > 0 ? areasWithPolygons.filter((area) => selectedAreaIds.has(area.id)) : areasWithPolygons;
      areasToFit.forEach((area) => {
        if (area.polygon) {
          decodePolygon(area.polygon).forEach((coord) => bounds.extend(coord));
        }
      });
      map.fitBounds(bounds);
    }
  }, [map, areasWithPolygons, selectedAreaIds]);
  reactExports.useEffect(() => {
    if (mapLoaded) {
      fitBounds();
    }
  }, [mapLoaded, fitBounds, selectedDistrict, selectedAreaIds]);
  const handleSort = (column) => {
    if (sortBy === column) {
      setSortOrder(sortOrder === "asc" ? "desc" : "asc");
    } else {
      setSortBy(column);
      setSortOrder("asc");
    }
  };
  const sortedAreas = reactExports.useMemo(() => {
    return [...areasWithPolygons].sort((a, b) => {
      if (sortBy === "name") {
        return sortOrder === "asc" ? a.name.localeCompare(b.name) : b.name.localeCompare(a.name);
      } else if (sortBy === "state") {
        return sortOrder === "asc" ? a.state.localeCompare(b.state) : b.state.localeCompare(a.state);
      } else {
        return sortOrder === "asc" ? a.district.localeCompare(b.district) : b.district.localeCompare(a.district);
      }
    });
  }, [areasWithPolygons, sortBy, sortOrder]);
  return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
    className: "h-[calc(100vh-64px)] bg-white p-6 overflow-y-auto",
    children: [/* @__PURE__ */ jsxRuntimeExports.jsx("div", {
      className: "flex justify-between items-center mb-6",
      children: /* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
        onClick: () => navigate("/home/<USER>"),
        variant: "ghost",
        size: "icon",
        children: /* @__PURE__ */ jsxRuntimeExports.jsx(ArrowLeft, {
          size: 24
        })
      })
    }), /* @__PURE__ */ jsxRuntimeExports.jsxs(Card, {
      className: "w-full mx-auto",
      children: [/* @__PURE__ */ jsxRuntimeExports.jsxs(CardHeader, {
        children: [/* @__PURE__ */ jsxRuntimeExports.jsx(CardTitle, {
          children: "Select Areas to Attach"
        }), /* @__PURE__ */ jsxRuntimeExports.jsx(CardDescription, {
          children: "Choose areas from available localities"
        })]
      }), /* @__PURE__ */ jsxRuntimeExports.jsxs(CardContent, {
        className: "space-y-4",
        children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
          className: "flex gap-4 mb-4",
          children: [/* @__PURE__ */ jsxRuntimeExports.jsxs(Select, {
            value: selectedState,
            onValueChange: handleStateChange,
            children: [/* @__PURE__ */ jsxRuntimeExports.jsx(SelectTrigger, {
              className: "w-[180px]",
              children: /* @__PURE__ */ jsxRuntimeExports.jsx(SelectValue, {
                placeholder: "Select state"
              })
            }), /* @__PURE__ */ jsxRuntimeExports.jsx(SelectContent, {
              children: states.map((state) => /* @__PURE__ */ jsxRuntimeExports.jsx(SelectItem, {
                value: state,
                children: state
              }, state))
            })]
          }), /* @__PURE__ */ jsxRuntimeExports.jsxs(Select, {
            value: selectedDistrict,
            onValueChange: handleDistrictChange,
            disabled: !selectedState,
            children: [/* @__PURE__ */ jsxRuntimeExports.jsx(SelectTrigger, {
              className: "w-[180px]",
              children: /* @__PURE__ */ jsxRuntimeExports.jsx(SelectValue, {
                placeholder: "Select district"
              })
            }), /* @__PURE__ */ jsxRuntimeExports.jsx(SelectContent, {
              children: selectedState && ((_a = districts[selectedState]) == null ? void 0 : _a.map((district) => /* @__PURE__ */ jsxRuntimeExports.jsx(SelectItem, {
                value: district,
                children: district
              }, district)))
            })]
          })]
        }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
          className: "flex gap-4 h-[calc(100vh-300px)]",
          children: [/* @__PURE__ */ jsxRuntimeExports.jsx("div", {
            className: "w-1/2 h-full",
            children: /* @__PURE__ */ jsxRuntimeExports.jsx(LoadScript, {
              googleMapsApiKey,
              children: /* @__PURE__ */ jsxRuntimeExports.jsx(GoogleMap, {
                mapContainerClassName: "w-full h-full",
                center: BANGALORE_CENTER,
                zoom: 11,
                onLoad: (map2) => {
                  setMap(map2);
                  setMapLoaded(true);
                },
                options: {
                  streetViewControl: false,
                  mapTypeControl: false,
                  fullscreenControl: false,
                  zoomControl: true,
                  clickableIcons: false
                },
                children: mapLoaded && areasWithPolygons.map((area) => /* @__PURE__ */ jsxRuntimeExports.jsx(Polygon, {
                  paths: decodePolygon(area.polygon),
                  options: {
                    fillColor: selectedAreaIds.has(area.id) ? "#4F46E5" : "#9CA3AF",
                    fillOpacity: selectedAreaIds.has(area.id) ? 0.4 : 0.2,
                    strokeWeight: selectedAreaIds.has(area.id) ? 2 : 1,
                    strokeColor: selectedAreaIds.has(area.id) ? "#4F46E5" : "#9CA3AF",
                    clickable: true
                  },
                  onClick: () => toggleAreaSelection(area.id)
                }, area.id))
              })
            })
          }), /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
            className: "w-1/2 h-full overflow-auto",
            children: /* @__PURE__ */ jsxRuntimeExports.jsxs("table", {
              className: "min-w-full divide-y divide-gray-200",
              children: [/* @__PURE__ */ jsxRuntimeExports.jsx("thead", {
                className: "bg-gray-50 sticky top-0",
                children: /* @__PURE__ */ jsxRuntimeExports.jsxs("tr", {
                  children: [/* @__PURE__ */ jsxRuntimeExports.jsx("th", {
                    className: "px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",
                    children: "Select"
                  }), /* @__PURE__ */ jsxRuntimeExports.jsx("th", {
                    className: "px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",
                    children: /* @__PURE__ */ jsxRuntimeExports.jsxs(Button, {
                      variant: "ghost",
                      onClick: () => handleSort("name"),
                      className: "flex items-center",
                      children: ["Name", /* @__PURE__ */ jsxRuntimeExports.jsx(ArrowUpDown, {
                        size: 14,
                        className: "ml-1"
                      })]
                    })
                  }), /* @__PURE__ */ jsxRuntimeExports.jsx("th", {
                    className: "px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",
                    children: /* @__PURE__ */ jsxRuntimeExports.jsxs(Button, {
                      variant: "ghost",
                      onClick: () => handleSort("state"),
                      className: "flex items-center",
                      children: ["State", /* @__PURE__ */ jsxRuntimeExports.jsx(ArrowUpDown, {
                        size: 14,
                        className: "ml-1"
                      })]
                    })
                  }), /* @__PURE__ */ jsxRuntimeExports.jsx("th", {
                    className: "px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",
                    children: /* @__PURE__ */ jsxRuntimeExports.jsxs(Button, {
                      variant: "ghost",
                      onClick: () => handleSort("district"),
                      className: "flex items-center",
                      children: ["District", /* @__PURE__ */ jsxRuntimeExports.jsx(ArrowUpDown, {
                        size: 14,
                        className: "ml-1"
                      })]
                    })
                  })]
                })
              }), /* @__PURE__ */ jsxRuntimeExports.jsx("tbody", {
                className: "bg-white divide-y divide-gray-200",
                children: sortedAreas.map((area) => /* @__PURE__ */ jsxRuntimeExports.jsxs("tr", {
                  className: "hover:bg-gray-50",
                  children: [/* @__PURE__ */ jsxRuntimeExports.jsx("td", {
                    className: "px-6 py-4 whitespace-nowrap",
                    children: /* @__PURE__ */ jsxRuntimeExports.jsx(Checkbox, {
                      checked: selectedAreaIds.has(area.id),
                      onCheckedChange: () => toggleAreaSelection(area.id),
                      id: `area-${area.id}`
                    })
                  }), /* @__PURE__ */ jsxRuntimeExports.jsx("td", {
                    className: "px-6 py-4 whitespace-nowrap",
                    children: /* @__PURE__ */ jsxRuntimeExports.jsx("label", {
                      htmlFor: `area-${area.id}`,
                      className: "text-sm text-gray-900",
                      children: area.name
                    })
                  }), /* @__PURE__ */ jsxRuntimeExports.jsx("td", {
                    className: "px-6 py-4 whitespace-nowrap text-sm text-gray-500",
                    children: area.state
                  }), /* @__PURE__ */ jsxRuntimeExports.jsx("td", {
                    className: "px-6 py-4 whitespace-nowrap text-sm text-gray-500",
                    children: area.district
                  })]
                }, area.id))
              })]
            })
          })]
        }), /* @__PURE__ */ jsxRuntimeExports.jsx(Form, {
          onSubmit: handleSubmit,
          children: /* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
            type: "submit",
            disabled: selectedAreaIds.size === 0,
            className: "w-full",
            children: "Attach Selected Areas"
          })
        })]
      })]
    })]
  });
}
export {
  AttachLocality as default
};
//# sourceMappingURL=home.localities.attach-B74gUwIS.js.map
