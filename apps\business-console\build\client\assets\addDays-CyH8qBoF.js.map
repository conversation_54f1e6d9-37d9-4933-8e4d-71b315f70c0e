{"version": 3, "file": "addDays-CyH8qBoF.js", "sources": ["../../../node_modules/date-fns/addDays.mjs"], "sourcesContent": ["import { toDate } from \"./toDate.mjs\";\nimport { constructFrom } from \"./constructFrom.mjs\";\n\n/**\n * @name addDays\n * @category Day Helpers\n * @summary Add the specified number of days to the given date.\n *\n * @description\n * Add the specified number of days to the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param date - The date to be changed\n * @param amount - The amount of days to be added.\n *\n * @returns The new date with the days added\n *\n * @example\n * // Add 10 days to 1 September 2014:\n * const result = addDays(new Date(2014, 8, 1), 10)\n * //=> Thu Sep 11 2014 00:00:00\n */\nexport function addDays(date, amount) {\n  const _date = toDate(date);\n  if (isNaN(amount)) return constructFrom(date, NaN);\n  if (!amount) {\n    // If 0 days, no-op to avoid changing times in the hour before end of DST\n    return _date;\n  }\n  _date.setDate(_date.getDate() + amount);\n  return _date;\n}\n\n// Fallback for modularized imports:\nexport default addDays;\n"], "names": [], "mappings": ";AAuBO,SAAS,QAAQ,MAAM,QAAQ;AACpC,QAAM,QAAQ,OAAO,IAAI;AACzB,MAAI,MAAM,MAAM,EAAG,QAAO,cAAc,MAAM,GAAG;AACjD,MAAI,CAAC,QAAQ;AAEX,WAAO;AAAA,EACX;AACE,QAAM,QAAQ,MAAM,QAAO,IAAK,MAAM;AACtC,SAAO;AACT;", "x_google_ignoreList": [0]}