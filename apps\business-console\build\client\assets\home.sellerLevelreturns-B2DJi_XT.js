import { r as reactExports, j as jsxRuntimeExports } from "./jsx-runtime-bB2y7OuD.js";
import { u as useLoaderData } from "./components-D7UvGag_.js";
import "./index-Vp2vNLNM.js";
import "./index-DhHTcibu.js";
function SellerLevelReturns() {
  const {
    embedUrl
  } = useLoaderData();
  const [isLoading, setIsLoading] = reactExports.useState(true);
  reactExports.useEffect(() => {
    if (embedUrl) {
      setIsLoading(false);
    }
  }, [embedUrl]);
  reactExports.useEffect(() => {
    const handleScroll = () => {
      var _a;
      const iframe = document.getElementById("metabase-iframe");
      if (iframe) {
        (_a = iframe.contentWindow) == null ? void 0 : _a.scrollTo(0, window.scrollY);
      }
    };
    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);
  return /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
    className: "flex min-h-screen",
    children: /* @__PURE__ */ jsxRuntimeExports.jsx("main", {
      className: "flex-1 overflow-y-auto",
      children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
        className: "p-4 sm:p-6",
        children: [/* @__PURE__ */ jsxRuntimeExports.jsx("div", {
          className: "flex justify-between items-center mb-4",
          children: /* @__PURE__ */ jsxRuntimeExports.jsx("h1", {
            className: "text-2xl font-bold text-gray-800",
            children: "Seller Level Returns Report"
          })
        }), /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
          className: "bg-white shadow-md rounded-md overflow-hidden",
          children: isLoading ? /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
            className: "flex justify-center items-center h-96",
            children: /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
              className: "animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"
            })
          }) : embedUrl ? /* @__PURE__ */ jsxRuntimeExports.jsx("iframe", {
            id: "metabase-iframe",
            src: embedUrl,
            title: "Metabase Dashboard",
            className: "w-full h-[600px] border-0",
            allowTransparency: true
          }) : /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
            className: "p-6 text-center text-red-500",
            children: "Failed to load the dashboard."
          })
        })]
      })
    })
  });
}
export {
  SellerLevelReturns as default
};
//# sourceMappingURL=home.sellerLevelreturns-B2DJi_XT.js.map
