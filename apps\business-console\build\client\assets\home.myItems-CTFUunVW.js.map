{"version": 3, "file": "home.myItems-CTFUunVW.js", "sources": ["../../../app/routes/home.myItems.tsx"], "sourcesContent": ["\r\nimport { useState } from 'react'\r\nimport { <PERSON><PERSON> } from \"@components/ui/button\"\r\nimport { Input } from \"@components/ui/input\"\r\nimport { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from \"@components/ui/table\"\r\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from \"@components/ui/select\"\r\nimport { Form, Link, useLoaderData, useNavigate } from \"@remix-run/react\"\r\nimport { SellerItem } from '~/types/api/businessConsoleService/MyItemList'\r\nimport { getSellerItems } from '~/services/myItems'\r\nimport { withAuth, withResponse } from \"@utils/auth-utils\";\r\n\r\ninterface LoaderData {\r\n      data: SellerItem[];\r\n      currentPage: number;\r\n      hasNextPage: boolean;\r\n      totalPages: number;\r\n}\r\n\r\nexport const loader = withAuth(async ({ request }) => {\r\n      try {\r\n            const url = new URL(request.url);\r\n            const page = parseInt(url.searchParams.get(\"page\") || \"0\");\r\n            const pageSize = parseInt(url.searchParams.get(\"pageSize\") || \"50\");\r\n            const search = (url.searchParams.get(\"matchBy\") || \"\");\r\n\r\n            const response = await getSellerItems(page, pageSize, search, request);\r\n            const hasNextPage = response.data.length >= pageSize;\r\n\r\n            return withResponse({\r\n                  data: response.data,\r\n                  currentPage: page,\r\n                  hasNextPage,\r\n                  totalPages: Math.ceil(response.data.length / pageSize)\r\n            }, response.headers);\r\n      } catch (error) {\r\n            console.error(\"Seller items error:\", error);\r\n            throw new Response(\"Failed to fetch seller items data\", {\r\n                  status: 500\r\n            });\r\n      }\r\n});\r\n\r\nexport default function MyItems() {\r\n      const navigate = useNavigate();\r\n      const { data: sellerItems, currentPage, hasNextPage, totalPages } = useLoaderData<LoaderData>()\r\n      const [searchTerm, setSearchTerm] = useState('')\r\n      const [pageSize, setPageSize] = useState(\"50\")\r\n\r\n      const handlePageSizeChange = (newPageSize: string) => {\r\n            setPageSize(newPageSize)\r\n            navigate(`/home/<USER>\n      }\r\n\r\n      const filterSellerItems = sellerItems\r\n            .filter(item =>\r\n                  item.name.toLowerCase().includes(searchTerm.toLowerCase())\r\n            )\r\n\r\n\r\n      const handlePageSearch = (value: string) => {\r\n            if (value.length >= 3) {\r\n                  setSearchTerm(value)\r\n                  navigate(`/home/<USER>\n\r\n            }\r\n            else {\r\n                  setSearchTerm(value)\r\n            }\r\n      }\r\n\r\n      return (\r\n            <div className=\"container mx-auto p-6\">\r\n                  <div className=\"flex justify-between items-center mb-6\">\r\n                        <h1 className=\"text-2xl font-bold\">My Items</h1>\r\n                  </div>\r\n                  <div className=\"flex justify-between mb-4\">\r\n                        <Input\r\n                              placeholder=\"Search by Item Name\"\r\n                              value={searchTerm}\r\n                              onChange={(e) => handlePageSearch(e.target.value)}\r\n                              className=\"max-w-sm\"\r\n                        />\r\n                        <Select value={pageSize} onValueChange={handlePageSizeChange}>\r\n                              <SelectTrigger className=\"w-[180px]\">\r\n                                    <SelectValue placeholder=\"Items per page\" />\r\n                              </SelectTrigger>\r\n                              <SelectContent>\r\n                                    <SelectItem value=\"5\">5 per page</SelectItem>\r\n                                    <SelectItem value=\"10\">10 per page</SelectItem>\r\n                                    <SelectItem value=\"20\">20 per page</SelectItem>\r\n                                    <SelectItem value=\"50\">50 per page</SelectItem>\r\n                              </SelectContent>\r\n                        </Select>\r\n                  </div>\r\n                  <Table>\r\n                        <TableHeader>\r\n                              <TableRow>\r\n                                    <TableHead>Item Image</TableHead>\r\n                                    <TableHead>Item Name</TableHead>\r\n                                    <TableHead>Item Unit</TableHead>\r\n                              </TableRow>\r\n                        </TableHeader>\r\n                        <TableBody>\r\n                              {filterSellerItems.sort((a, b) => a.name.localeCompare(b.name)).map((item) => (\r\n                                    <TableRow key={item.Id}>\r\n                                          <TableCell>\r\n                                                <img src={item.picture}\r\n                                                      alt=\"ItemImage\"\r\n                                                      className=\"h-10 w-10\" />\r\n                                          </TableCell>\r\n                                          <TableCell>\r\n                                                <Link to={`/home/<USER>/${item.Id}`}\r\n                                                      className=\"text-blue-600 hover:underline\">\r\n                                                      <div>\r\n                                                            <div>{item.name}</div>\r\n                                                      </div>\r\n                                                </Link>\r\n                                          </TableCell>\r\n                                          <TableCell>{item.unit}</TableCell>\r\n                                    </TableRow>\r\n                              ))}\r\n                        </TableBody>\r\n                  </Table>\r\n                  <div className=\"flex items-center justify-between space-x-2 py-4\">\r\n                        <div className=\"flex-1 text-sm text-muted-foreground\">\r\n                              {/* Page {currentPage + 1} of {totalPages} */}\r\n                        </div>\r\n                        <div className=\"flex items-center space-x-2\">\r\n                              <Form method=\"get\">\r\n                                    <input type=\"hidden\" name=\"page\" value={currentPage - 1} />\r\n                                    <input type=\"hidden\" name=\"pageSize\" value={pageSize} />\r\n                                    <Button\r\n                                          variant=\"outline\"\r\n                                          size=\"sm\"\r\n                                          type=\"submit\"\r\n                                          disabled={currentPage <= 0}\r\n                                    >\r\n                                          Previous\r\n                                    </Button>\r\n                              </Form>\r\n                              <Form method=\"get\">\r\n                                    <input type=\"hidden\" name=\"page\" value={currentPage + 1} />\r\n                                    <input type=\"hidden\" name=\"pageSize\" value={pageSize} />\r\n                                    <Button\r\n                                          variant=\"outline\"\r\n                                          size=\"sm\"\r\n                                          type=\"submit\"\r\n                                          disabled={!hasNextPage}\r\n                                    >\r\n                                          Next\r\n                                    </Button>\r\n                              </Form>\r\n                        </div>\r\n                  </div>\r\n            </div>\r\n      )\r\n}\r\n"], "names": ["MyItems", "navigate", "useNavigate", "data", "sellerItems", "currentPage", "hasNextPage", "totalPages", "useLoaderData", "searchTerm", "setSearchTerm", "useState", "pageSize", "setPageSize", "handlePageSizeChange", "newPageSize", "filterSellerItems", "filter", "item", "name", "toLowerCase", "includes", "handlePageSearch", "value", "length", "jsxs", "className", "children", "jsx", "Input", "placeholder", "onChange", "e", "target", "Select", "onValueChange", "SelectTrigger", "SelectValue", "SelectContent", "SelectItem", "Table", "TableHeader", "TableRow", "TableHead", "TableBody", "sort", "a", "b", "localeCompare", "map", "TableCell", "src", "picture", "alt", "Link", "to", "Id", "unit", "Form", "method", "type", "<PERSON><PERSON>", "variant", "size", "disabled"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AA0CA,SAAwBA,UAAU;AAC5B,QAAMC,WAAWC,YAAY;AAC7B,QAAM;AAAA,IAAEC,MAAMC;AAAAA,IAAaC;AAAAA,IAAaC;AAAAA,IAAaC;AAAAA,MAAeC,cAA0B;AAC9F,QAAM,CAACC,YAAYC,aAAa,IAAIC,aAAAA,SAAS,EAAE;AAC/C,QAAM,CAACC,UAAUC,WAAW,IAAIF,aAAAA,SAAS,IAAI;AAEvC,QAAAG,uBAAwBC,iBAAwB;AAChDF,gBAAYE,WAAW;AACvBd,aAAS,sBAAsBI,WAAW,aAAaU,WAAW,YAAYN,UAAU,EAAE;AAAA,EAChG;AAEA,QAAMO,oBAAoBZ,YACnBa,OAAOC,UACFA,KAAKC,KAAKC,cAAcC,SAASZ,WAAWW,YAAa,CAAA,CAC/D;AAGA,QAAAE,mBAAoBC,WAAkB;AAClC,QAAAA,MAAMC,UAAU,GAAG;AACjBd,oBAAca,KAAK;AACnBtB,eAAS,sBAAsBI,WAAW,aAAaO,QAAQ,YAAYW,KAAK,EAAE;AAAA,IAExF,OACK;AACCb,oBAAca,KAAK;AAAA,IACzB;AAAA,EACN;AAGM,SAAAE,kCAAAA,KAAC,OAAI;AAAA,IAAAC,WAAU;AAAA,IACTC,UAAA,CAACC,kCAAA,IAAA,OAAA;AAAA,MAAIF,WAAU;AAAA,MACTC,UAAAC,kCAAA,IAAC;QAAGF,WAAU;AAAA,QAAqBC;MAAQ,CAAA;AAAA,IACjD,CAAA,GACAF,kCAAA,KAAC,OAAI;AAAA,MAAAC,WAAU;AAAA,MACTC,UAAA,CAAAC,kCAAA,IAACC,OAAA;AAAA,QACKC,aAAY;AAAA,QACZP,OAAOd;AAAAA,QACPsB,UAAWC,OAAMV,iBAAiBU,EAAEC,OAAOV,KAAK;AAAA,QAChDG,WAAU;AAAA,MAAA,CAChB,GACCD,kCAAA,KAAAS,QAAA;AAAA,QAAOX,OAAOX;AAAAA,QAAUuB,eAAerB;AAAAA,QAClCa,UAAA,CAAAC,kCAAA,IAACQ;UAAcV,WAAU;AAAA,UACnBC,gDAACU,aAAY;AAAA,YAAAP,aAAY;AAAA,UAAiB,CAAA;AAAA,QAChD,CAAA,0CACCQ,eACK;AAAA,UAAAX,UAAA,CAACC,kCAAA,IAAAW,YAAA;AAAA,YAAWhB,OAAM;AAAA,YAAII,UAAU;AAAA,UAAA,CAAA,GAC/BC,kCAAA,IAAAW,YAAA;AAAA,YAAWhB,OAAM;AAAA,YAAKI,UAAW;AAAA,UAAA,CAAA,GACjCC,kCAAA,IAAAW,YAAA;AAAA,YAAWhB,OAAM;AAAA,YAAKI,UAAW;AAAA,UAAA,CAAA,GACjCC,kCAAA,IAAAW,YAAA;AAAA,YAAWhB,OAAM;AAAA,YAAKI,UAAW;AAAA,UAAA,CAAA,CAAA;AAAA,QACxC,CAAA,CAAA;AAAA,MACN,CAAA,CAAA;AAAA,IACN,CAAA,0CACCa,OACK;AAAA,MAAAb,UAAA,CAACC,kCAAA,IAAAa,aAAA;AAAA,QACKd,iDAACe,UACK;AAAA,UAAAf,UAAA,CAAAC,kCAAA,IAACe;YAAUhB,UAAU;AAAA,UAAA,CAAA,GACrBC,kCAAA,IAACe;YAAUhB,UAAS;AAAA,UAAA,CAAA,GACpBC,kCAAA,IAACe;YAAUhB,UAAS;AAAA,UAAA,CAAA,CAAA;AAAA,QAC1B,CAAA;AAAA,MACN,CAAA,yCACCiB,WACM;AAAA,QAAAjB,UAAAX,kBAAkB6B,KAAK,CAACC,GAAGC,MAAMD,EAAE3B,KAAK6B,cAAcD,EAAE5B,IAAI,CAAC,EAAE8B,IAAK/B,iDAC9DwB,UACK;AAAA,UAAAf,UAAA,CAAAC,kCAAA,IAACsB,WACK;AAAA,YAAAvB,UAAAC,kCAAA,IAAC,OAAA;AAAA,cAAIuB,KAAKjC,KAAKkC;AAAAA,cACTC,KAAI;AAAA,cACJ3B,WAAU;AAAA,YAAY,CAAA;AAAA,UAClC,CAAA,yCACCwB,WACK;AAAA,YAAAvB,UAAAC,kCAAA,IAAC0B,MAAA;AAAA,cAAKC,IAAI,qBAAqBrC,KAAKsC,EAAE;AAAA,cAChC9B,WAAU;AAAA,cACVC,gDAAC,OACK;AAAA,gBAAAA,UAAAC,kCAAA,IAAC,OAAK;AAAA,kBAAAD,UAAAT,KAAKC;AAAAA,gBAAK,CAAA;AAAA,cACtB,CAAA;AAAA,YACN,CAAA;AAAA,UACN,CAAA,GACAS,kCAAA,IAACsB,WAAW;AAAA,YAAAvB,UAAAT,KAAKuC;AAAAA,UAAK,CAAA,CAAA;AAAA,QAdb,GAAAvC,KAAKsC,EAepB,CACL;AAAA,MACP,CAAA,CAAA;AAAA,IACN,CAAA,GACA/B,kCAAA,KAAC,OAAI;AAAA,MAAAC,WAAU;AAAA,MACTC,UAAA,CAACC,kCAAA,IAAA,OAAA;AAAA,QAAIF,WAAU;AAAA,MAEf,CAAA,GACAD,kCAAA,KAAC,OAAI;AAAA,QAAAC,WAAU;AAAA,QACTC,UAAA,CAACF,kCAAA,KAAAiC,MAAA;AAAA,UAAKC,QAAO;AAAA,UACPhC,UAAA,CAAAC,kCAAA,IAAC;YAAMgC,MAAK;AAAA,YAASzC,MAAK;AAAA,YAAOI,OAAOlB,cAAc;AAAA,UAAG,CAAA,yCACxD,SAAM;AAAA,YAAAuD,MAAK;AAAA,YAASzC,MAAK;AAAA,YAAWI,OAAOX;AAAAA,UAAU,CAAA,GACtDgB,kCAAA,IAACiC,QAAA;AAAA,YACKC,SAAQ;AAAA,YACRC,MAAK;AAAA,YACLH,MAAK;AAAA,YACLI,UAAU3D,eAAe;AAAA,YAC9BsB,UAAA;AAAA,UAAA,CAED,CAAA;AAAA,QACN,CAAA,GACAF,kCAAA,KAACiC,MAAK;AAAA,UAAAC,QAAO;AAAA,UACPhC,UAAA,CAAAC,kCAAA,IAAC;YAAMgC,MAAK;AAAA,YAASzC,MAAK;AAAA,YAAOI,OAAOlB,cAAc;AAAA,UAAG,CAAA,yCACxD,SAAM;AAAA,YAAAuD,MAAK;AAAA,YAASzC,MAAK;AAAA,YAAWI,OAAOX;AAAAA,UAAU,CAAA,GACtDgB,kCAAA,IAACiC,QAAA;AAAA,YACKC,SAAQ;AAAA,YACRC,MAAK;AAAA,YACLH,MAAK;AAAA,YACLI,UAAU,CAAC1D;AAAAA,YAChBqB,UAAA;AAAA,UAAA,CAED,CAAA;AAAA,QACN,CAAA,CAAA;AAAA,MACN,CAAA,CAAA;AAAA,IACN,CAAA,CAAA;AAAA,EACN,CAAA;AAEZ;"}