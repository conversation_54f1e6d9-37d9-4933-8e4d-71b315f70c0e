{"version": 3, "file": "tooltip-CmSNYR5K.js", "sources": ["../../../node_modules/@radix-ui/react-tooltip/dist/index.mjs", "../../../app/components/ui/tooltip.tsx"], "sourcesContent": ["\"use client\";\n\n// packages/react/tooltip/src/tooltip.tsx\nimport * as React from \"react\";\nimport { composeEventHandlers } from \"@radix-ui/primitive\";\nimport { useComposedRefs } from \"@radix-ui/react-compose-refs\";\nimport { createContextScope } from \"@radix-ui/react-context\";\nimport { DismissableLayer } from \"@radix-ui/react-dismissable-layer\";\nimport { useId } from \"@radix-ui/react-id\";\nimport * as PopperPrimitive from \"@radix-ui/react-popper\";\nimport { createPopperScope } from \"@radix-ui/react-popper\";\nimport { Portal as PortalPrimitive } from \"@radix-ui/react-portal\";\nimport { Presence } from \"@radix-ui/react-presence\";\nimport { Primitive } from \"@radix-ui/react-primitive\";\nimport { Slottable } from \"@radix-ui/react-slot\";\nimport { useControllableState } from \"@radix-ui/react-use-controllable-state\";\nimport * as VisuallyHiddenPrimitive from \"@radix-ui/react-visually-hidden\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar [createTooltipContext, createTooltipScope] = createContextScope(\"Tooltip\", [\n  createPopperScope\n]);\nvar usePopperScope = createPopperScope();\nvar PROVIDER_NAME = \"TooltipProvider\";\nvar DEFAULT_DELAY_DURATION = 700;\nvar TOOLTIP_OPEN = \"tooltip.open\";\nvar [TooltipProviderContextProvider, useTooltipProviderContext] = createTooltipContext(PROVIDER_NAME);\nvar TooltipProvider = (props) => {\n  const {\n    __scopeTooltip,\n    delayDuration = DEFAULT_DELAY_DURATION,\n    skipDelayDuration = 300,\n    disableHoverableContent = false,\n    children\n  } = props;\n  const [isOpenDelayed, setIsOpenDelayed] = React.useState(true);\n  const isPointerInTransitRef = React.useRef(false);\n  const skipDelayTimerRef = React.useRef(0);\n  React.useEffect(() => {\n    const skipDelayTimer = skipDelayTimerRef.current;\n    return () => window.clearTimeout(skipDelayTimer);\n  }, []);\n  return /* @__PURE__ */ jsx(\n    TooltipProviderContextProvider,\n    {\n      scope: __scopeTooltip,\n      isOpenDelayed,\n      delayDuration,\n      onOpen: React.useCallback(() => {\n        window.clearTimeout(skipDelayTimerRef.current);\n        setIsOpenDelayed(false);\n      }, []),\n      onClose: React.useCallback(() => {\n        window.clearTimeout(skipDelayTimerRef.current);\n        skipDelayTimerRef.current = window.setTimeout(\n          () => setIsOpenDelayed(true),\n          skipDelayDuration\n        );\n      }, [skipDelayDuration]),\n      isPointerInTransitRef,\n      onPointerInTransitChange: React.useCallback((inTransit) => {\n        isPointerInTransitRef.current = inTransit;\n      }, []),\n      disableHoverableContent,\n      children\n    }\n  );\n};\nTooltipProvider.displayName = PROVIDER_NAME;\nvar TOOLTIP_NAME = \"Tooltip\";\nvar [TooltipContextProvider, useTooltipContext] = createTooltipContext(TOOLTIP_NAME);\nvar Tooltip = (props) => {\n  const {\n    __scopeTooltip,\n    children,\n    open: openProp,\n    defaultOpen = false,\n    onOpenChange,\n    disableHoverableContent: disableHoverableContentProp,\n    delayDuration: delayDurationProp\n  } = props;\n  const providerContext = useTooltipProviderContext(TOOLTIP_NAME, props.__scopeTooltip);\n  const popperScope = usePopperScope(__scopeTooltip);\n  const [trigger, setTrigger] = React.useState(null);\n  const contentId = useId();\n  const openTimerRef = React.useRef(0);\n  const disableHoverableContent = disableHoverableContentProp ?? providerContext.disableHoverableContent;\n  const delayDuration = delayDurationProp ?? providerContext.delayDuration;\n  const wasOpenDelayedRef = React.useRef(false);\n  const [open = false, setOpen] = useControllableState({\n    prop: openProp,\n    defaultProp: defaultOpen,\n    onChange: (open2) => {\n      if (open2) {\n        providerContext.onOpen();\n        document.dispatchEvent(new CustomEvent(TOOLTIP_OPEN));\n      } else {\n        providerContext.onClose();\n      }\n      onOpenChange?.(open2);\n    }\n  });\n  const stateAttribute = React.useMemo(() => {\n    return open ? wasOpenDelayedRef.current ? \"delayed-open\" : \"instant-open\" : \"closed\";\n  }, [open]);\n  const handleOpen = React.useCallback(() => {\n    window.clearTimeout(openTimerRef.current);\n    openTimerRef.current = 0;\n    wasOpenDelayedRef.current = false;\n    setOpen(true);\n  }, [setOpen]);\n  const handleClose = React.useCallback(() => {\n    window.clearTimeout(openTimerRef.current);\n    openTimerRef.current = 0;\n    setOpen(false);\n  }, [setOpen]);\n  const handleDelayedOpen = React.useCallback(() => {\n    window.clearTimeout(openTimerRef.current);\n    openTimerRef.current = window.setTimeout(() => {\n      wasOpenDelayedRef.current = true;\n      setOpen(true);\n      openTimerRef.current = 0;\n    }, delayDuration);\n  }, [delayDuration, setOpen]);\n  React.useEffect(() => {\n    return () => {\n      if (openTimerRef.current) {\n        window.clearTimeout(openTimerRef.current);\n        openTimerRef.current = 0;\n      }\n    };\n  }, []);\n  return /* @__PURE__ */ jsx(PopperPrimitive.Root, { ...popperScope, children: /* @__PURE__ */ jsx(\n    TooltipContextProvider,\n    {\n      scope: __scopeTooltip,\n      contentId,\n      open,\n      stateAttribute,\n      trigger,\n      onTriggerChange: setTrigger,\n      onTriggerEnter: React.useCallback(() => {\n        if (providerContext.isOpenDelayed) handleDelayedOpen();\n        else handleOpen();\n      }, [providerContext.isOpenDelayed, handleDelayedOpen, handleOpen]),\n      onTriggerLeave: React.useCallback(() => {\n        if (disableHoverableContent) {\n          handleClose();\n        } else {\n          window.clearTimeout(openTimerRef.current);\n          openTimerRef.current = 0;\n        }\n      }, [handleClose, disableHoverableContent]),\n      onOpen: handleOpen,\n      onClose: handleClose,\n      disableHoverableContent,\n      children\n    }\n  ) });\n};\nTooltip.displayName = TOOLTIP_NAME;\nvar TRIGGER_NAME = \"TooltipTrigger\";\nvar TooltipTrigger = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeTooltip, ...triggerProps } = props;\n    const context = useTooltipContext(TRIGGER_NAME, __scopeTooltip);\n    const providerContext = useTooltipProviderContext(TRIGGER_NAME, __scopeTooltip);\n    const popperScope = usePopperScope(__scopeTooltip);\n    const ref = React.useRef(null);\n    const composedRefs = useComposedRefs(forwardedRef, ref, context.onTriggerChange);\n    const isPointerDownRef = React.useRef(false);\n    const hasPointerMoveOpenedRef = React.useRef(false);\n    const handlePointerUp = React.useCallback(() => isPointerDownRef.current = false, []);\n    React.useEffect(() => {\n      return () => document.removeEventListener(\"pointerup\", handlePointerUp);\n    }, [handlePointerUp]);\n    return /* @__PURE__ */ jsx(PopperPrimitive.Anchor, { asChild: true, ...popperScope, children: /* @__PURE__ */ jsx(\n      Primitive.button,\n      {\n        \"aria-describedby\": context.open ? context.contentId : void 0,\n        \"data-state\": context.stateAttribute,\n        ...triggerProps,\n        ref: composedRefs,\n        onPointerMove: composeEventHandlers(props.onPointerMove, (event) => {\n          if (event.pointerType === \"touch\") return;\n          if (!hasPointerMoveOpenedRef.current && !providerContext.isPointerInTransitRef.current) {\n            context.onTriggerEnter();\n            hasPointerMoveOpenedRef.current = true;\n          }\n        }),\n        onPointerLeave: composeEventHandlers(props.onPointerLeave, () => {\n          context.onTriggerLeave();\n          hasPointerMoveOpenedRef.current = false;\n        }),\n        onPointerDown: composeEventHandlers(props.onPointerDown, () => {\n          isPointerDownRef.current = true;\n          document.addEventListener(\"pointerup\", handlePointerUp, { once: true });\n        }),\n        onFocus: composeEventHandlers(props.onFocus, () => {\n          if (!isPointerDownRef.current) context.onOpen();\n        }),\n        onBlur: composeEventHandlers(props.onBlur, context.onClose),\n        onClick: composeEventHandlers(props.onClick, context.onClose)\n      }\n    ) });\n  }\n);\nTooltipTrigger.displayName = TRIGGER_NAME;\nvar PORTAL_NAME = \"TooltipPortal\";\nvar [PortalProvider, usePortalContext] = createTooltipContext(PORTAL_NAME, {\n  forceMount: void 0\n});\nvar TooltipPortal = (props) => {\n  const { __scopeTooltip, forceMount, children, container } = props;\n  const context = useTooltipContext(PORTAL_NAME, __scopeTooltip);\n  return /* @__PURE__ */ jsx(PortalProvider, { scope: __scopeTooltip, forceMount, children: /* @__PURE__ */ jsx(Presence, { present: forceMount || context.open, children: /* @__PURE__ */ jsx(PortalPrimitive, { asChild: true, container, children }) }) });\n};\nTooltipPortal.displayName = PORTAL_NAME;\nvar CONTENT_NAME = \"TooltipContent\";\nvar TooltipContent = React.forwardRef(\n  (props, forwardedRef) => {\n    const portalContext = usePortalContext(CONTENT_NAME, props.__scopeTooltip);\n    const { forceMount = portalContext.forceMount, side = \"top\", ...contentProps } = props;\n    const context = useTooltipContext(CONTENT_NAME, props.__scopeTooltip);\n    return /* @__PURE__ */ jsx(Presence, { present: forceMount || context.open, children: context.disableHoverableContent ? /* @__PURE__ */ jsx(TooltipContentImpl, { side, ...contentProps, ref: forwardedRef }) : /* @__PURE__ */ jsx(TooltipContentHoverable, { side, ...contentProps, ref: forwardedRef }) });\n  }\n);\nvar TooltipContentHoverable = React.forwardRef((props, forwardedRef) => {\n  const context = useTooltipContext(CONTENT_NAME, props.__scopeTooltip);\n  const providerContext = useTooltipProviderContext(CONTENT_NAME, props.__scopeTooltip);\n  const ref = React.useRef(null);\n  const composedRefs = useComposedRefs(forwardedRef, ref);\n  const [pointerGraceArea, setPointerGraceArea] = React.useState(null);\n  const { trigger, onClose } = context;\n  const content = ref.current;\n  const { onPointerInTransitChange } = providerContext;\n  const handleRemoveGraceArea = React.useCallback(() => {\n    setPointerGraceArea(null);\n    onPointerInTransitChange(false);\n  }, [onPointerInTransitChange]);\n  const handleCreateGraceArea = React.useCallback(\n    (event, hoverTarget) => {\n      const currentTarget = event.currentTarget;\n      const exitPoint = { x: event.clientX, y: event.clientY };\n      const exitSide = getExitSideFromRect(exitPoint, currentTarget.getBoundingClientRect());\n      const paddedExitPoints = getPaddedExitPoints(exitPoint, exitSide);\n      const hoverTargetPoints = getPointsFromRect(hoverTarget.getBoundingClientRect());\n      const graceArea = getHull([...paddedExitPoints, ...hoverTargetPoints]);\n      setPointerGraceArea(graceArea);\n      onPointerInTransitChange(true);\n    },\n    [onPointerInTransitChange]\n  );\n  React.useEffect(() => {\n    return () => handleRemoveGraceArea();\n  }, [handleRemoveGraceArea]);\n  React.useEffect(() => {\n    if (trigger && content) {\n      const handleTriggerLeave = (event) => handleCreateGraceArea(event, content);\n      const handleContentLeave = (event) => handleCreateGraceArea(event, trigger);\n      trigger.addEventListener(\"pointerleave\", handleTriggerLeave);\n      content.addEventListener(\"pointerleave\", handleContentLeave);\n      return () => {\n        trigger.removeEventListener(\"pointerleave\", handleTriggerLeave);\n        content.removeEventListener(\"pointerleave\", handleContentLeave);\n      };\n    }\n  }, [trigger, content, handleCreateGraceArea, handleRemoveGraceArea]);\n  React.useEffect(() => {\n    if (pointerGraceArea) {\n      const handleTrackPointerGrace = (event) => {\n        const target = event.target;\n        const pointerPosition = { x: event.clientX, y: event.clientY };\n        const hasEnteredTarget = trigger?.contains(target) || content?.contains(target);\n        const isPointerOutsideGraceArea = !isPointInPolygon(pointerPosition, pointerGraceArea);\n        if (hasEnteredTarget) {\n          handleRemoveGraceArea();\n        } else if (isPointerOutsideGraceArea) {\n          handleRemoveGraceArea();\n          onClose();\n        }\n      };\n      document.addEventListener(\"pointermove\", handleTrackPointerGrace);\n      return () => document.removeEventListener(\"pointermove\", handleTrackPointerGrace);\n    }\n  }, [trigger, content, pointerGraceArea, onClose, handleRemoveGraceArea]);\n  return /* @__PURE__ */ jsx(TooltipContentImpl, { ...props, ref: composedRefs });\n});\nvar [VisuallyHiddenContentContextProvider, useVisuallyHiddenContentContext] = createTooltipContext(TOOLTIP_NAME, { isInside: false });\nvar TooltipContentImpl = React.forwardRef(\n  (props, forwardedRef) => {\n    const {\n      __scopeTooltip,\n      children,\n      \"aria-label\": ariaLabel,\n      onEscapeKeyDown,\n      onPointerDownOutside,\n      ...contentProps\n    } = props;\n    const context = useTooltipContext(CONTENT_NAME, __scopeTooltip);\n    const popperScope = usePopperScope(__scopeTooltip);\n    const { onClose } = context;\n    React.useEffect(() => {\n      document.addEventListener(TOOLTIP_OPEN, onClose);\n      return () => document.removeEventListener(TOOLTIP_OPEN, onClose);\n    }, [onClose]);\n    React.useEffect(() => {\n      if (context.trigger) {\n        const handleScroll = (event) => {\n          const target = event.target;\n          if (target?.contains(context.trigger)) onClose();\n        };\n        window.addEventListener(\"scroll\", handleScroll, { capture: true });\n        return () => window.removeEventListener(\"scroll\", handleScroll, { capture: true });\n      }\n    }, [context.trigger, onClose]);\n    return /* @__PURE__ */ jsx(\n      DismissableLayer,\n      {\n        asChild: true,\n        disableOutsidePointerEvents: false,\n        onEscapeKeyDown,\n        onPointerDownOutside,\n        onFocusOutside: (event) => event.preventDefault(),\n        onDismiss: onClose,\n        children: /* @__PURE__ */ jsxs(\n          PopperPrimitive.Content,\n          {\n            \"data-state\": context.stateAttribute,\n            ...popperScope,\n            ...contentProps,\n            ref: forwardedRef,\n            style: {\n              ...contentProps.style,\n              // re-namespace exposed content custom properties\n              ...{\n                \"--radix-tooltip-content-transform-origin\": \"var(--radix-popper-transform-origin)\",\n                \"--radix-tooltip-content-available-width\": \"var(--radix-popper-available-width)\",\n                \"--radix-tooltip-content-available-height\": \"var(--radix-popper-available-height)\",\n                \"--radix-tooltip-trigger-width\": \"var(--radix-popper-anchor-width)\",\n                \"--radix-tooltip-trigger-height\": \"var(--radix-popper-anchor-height)\"\n              }\n            },\n            children: [\n              /* @__PURE__ */ jsx(Slottable, { children }),\n              /* @__PURE__ */ jsx(VisuallyHiddenContentContextProvider, { scope: __scopeTooltip, isInside: true, children: /* @__PURE__ */ jsx(VisuallyHiddenPrimitive.Root, { id: context.contentId, role: \"tooltip\", children: ariaLabel || children }) })\n            ]\n          }\n        )\n      }\n    );\n  }\n);\nTooltipContent.displayName = CONTENT_NAME;\nvar ARROW_NAME = \"TooltipArrow\";\nvar TooltipArrow = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeTooltip, ...arrowProps } = props;\n    const popperScope = usePopperScope(__scopeTooltip);\n    const visuallyHiddenContentContext = useVisuallyHiddenContentContext(\n      ARROW_NAME,\n      __scopeTooltip\n    );\n    return visuallyHiddenContentContext.isInside ? null : /* @__PURE__ */ jsx(PopperPrimitive.Arrow, { ...popperScope, ...arrowProps, ref: forwardedRef });\n  }\n);\nTooltipArrow.displayName = ARROW_NAME;\nfunction getExitSideFromRect(point, rect) {\n  const top = Math.abs(rect.top - point.y);\n  const bottom = Math.abs(rect.bottom - point.y);\n  const right = Math.abs(rect.right - point.x);\n  const left = Math.abs(rect.left - point.x);\n  switch (Math.min(top, bottom, right, left)) {\n    case left:\n      return \"left\";\n    case right:\n      return \"right\";\n    case top:\n      return \"top\";\n    case bottom:\n      return \"bottom\";\n    default:\n      throw new Error(\"unreachable\");\n  }\n}\nfunction getPaddedExitPoints(exitPoint, exitSide, padding = 5) {\n  const paddedExitPoints = [];\n  switch (exitSide) {\n    case \"top\":\n      paddedExitPoints.push(\n        { x: exitPoint.x - padding, y: exitPoint.y + padding },\n        { x: exitPoint.x + padding, y: exitPoint.y + padding }\n      );\n      break;\n    case \"bottom\":\n      paddedExitPoints.push(\n        { x: exitPoint.x - padding, y: exitPoint.y - padding },\n        { x: exitPoint.x + padding, y: exitPoint.y - padding }\n      );\n      break;\n    case \"left\":\n      paddedExitPoints.push(\n        { x: exitPoint.x + padding, y: exitPoint.y - padding },\n        { x: exitPoint.x + padding, y: exitPoint.y + padding }\n      );\n      break;\n    case \"right\":\n      paddedExitPoints.push(\n        { x: exitPoint.x - padding, y: exitPoint.y - padding },\n        { x: exitPoint.x - padding, y: exitPoint.y + padding }\n      );\n      break;\n  }\n  return paddedExitPoints;\n}\nfunction getPointsFromRect(rect) {\n  const { top, right, bottom, left } = rect;\n  return [\n    { x: left, y: top },\n    { x: right, y: top },\n    { x: right, y: bottom },\n    { x: left, y: bottom }\n  ];\n}\nfunction isPointInPolygon(point, polygon) {\n  const { x, y } = point;\n  let inside = false;\n  for (let i = 0, j = polygon.length - 1; i < polygon.length; j = i++) {\n    const xi = polygon[i].x;\n    const yi = polygon[i].y;\n    const xj = polygon[j].x;\n    const yj = polygon[j].y;\n    const intersect = yi > y !== yj > y && x < (xj - xi) * (y - yi) / (yj - yi) + xi;\n    if (intersect) inside = !inside;\n  }\n  return inside;\n}\nfunction getHull(points) {\n  const newPoints = points.slice();\n  newPoints.sort((a, b) => {\n    if (a.x < b.x) return -1;\n    else if (a.x > b.x) return 1;\n    else if (a.y < b.y) return -1;\n    else if (a.y > b.y) return 1;\n    else return 0;\n  });\n  return getHullPresorted(newPoints);\n}\nfunction getHullPresorted(points) {\n  if (points.length <= 1) return points.slice();\n  const upperHull = [];\n  for (let i = 0; i < points.length; i++) {\n    const p = points[i];\n    while (upperHull.length >= 2) {\n      const q = upperHull[upperHull.length - 1];\n      const r = upperHull[upperHull.length - 2];\n      if ((q.x - r.x) * (p.y - r.y) >= (q.y - r.y) * (p.x - r.x)) upperHull.pop();\n      else break;\n    }\n    upperHull.push(p);\n  }\n  upperHull.pop();\n  const lowerHull = [];\n  for (let i = points.length - 1; i >= 0; i--) {\n    const p = points[i];\n    while (lowerHull.length >= 2) {\n      const q = lowerHull[lowerHull.length - 1];\n      const r = lowerHull[lowerHull.length - 2];\n      if ((q.x - r.x) * (p.y - r.y) >= (q.y - r.y) * (p.x - r.x)) lowerHull.pop();\n      else break;\n    }\n    lowerHull.push(p);\n  }\n  lowerHull.pop();\n  if (upperHull.length === 1 && lowerHull.length === 1 && upperHull[0].x === lowerHull[0].x && upperHull[0].y === lowerHull[0].y) {\n    return upperHull;\n  } else {\n    return upperHull.concat(lowerHull);\n  }\n}\nvar Provider = TooltipProvider;\nvar Root3 = Tooltip;\nvar Trigger = TooltipTrigger;\nvar Portal = TooltipPortal;\nvar Content2 = TooltipContent;\nvar Arrow2 = TooltipArrow;\nexport {\n  Arrow2 as Arrow,\n  Content2 as Content,\n  Portal,\n  Provider,\n  Root3 as Root,\n  Tooltip,\n  TooltipArrow,\n  TooltipContent,\n  TooltipPortal,\n  TooltipProvider,\n  TooltipTrigger,\n  Trigger,\n  createTooltipScope\n};\n//# sourceMappingURL=index.mjs.map\n", "import * as React from \"react\"\r\nimport * as TooltipPrimitive from \"@radix-ui/react-tooltip\"\r\n\r\nimport { cn } from \"~/lib/utils\"\r\n\r\nconst TooltipProvider = TooltipPrimitive.Provider\r\n\r\nconst Tooltip = TooltipPrimitive.Root\r\n\r\nconst TooltipTrigger = TooltipPrimitive.Trigger\r\n\r\nconst TooltipContent = React.forwardRef<\r\n  React.ElementRef<typeof TooltipPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof TooltipPrimitive.Content>\r\n>(({ className, sideOffset = 4, ...props }, ref) => (\r\n  <TooltipPrimitive.Content\r\n    ref={ref}\r\n    sideOffset={sideOffset}\r\n    className={cn(\r\n      \"z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nTooltipContent.displayName = TooltipPrimitive.Content.displayName\r\n\r\nexport { Tooltip, TooltipTrigger, TooltipContent, TooltipProvider }\r\n"], "names": ["TooltipProvider", "React.useState", "React.useRef", "React.useEffect", "jsx", "React.useCallback", "<PERSON><PERSON><PERSON>", "React.useMemo", "PopperPrimitive.Root", "TooltipTrigger", "React.forwardRef", "PopperPrimitive.Anchor", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jsxs", "PopperPrimitive.Content", "VisuallyHiddenPrimitive.Root", "PopperPrimitive.Arrow", "TooltipPrimitive.Provider", "TooltipPrimitive.Root", "TooltipPrimitive.Trigger", "TooltipPrimitive.Content"], "mappings": ";;;;;;;;;;;AAkBA,IAAI,CAAC,sBAAsB,kBAAkB,IAAI,mBAAmB,WAAW;AAAA,EAC7E;AACF,CAAC;AACD,IAAI,iBAAiB,kBAAmB;AACxC,IAAI,gBAAgB;AACpB,IAAI,yBAAyB;AAC7B,IAAI,eAAe;AACnB,IAAI,CAAC,gCAAgC,yBAAyB,IAAI,qBAAqB,aAAa;AACpG,IAAIA,oBAAkB,CAAC,UAAU;AAC/B,QAAM;AAAA,IACJ;AAAA,IACA,gBAAgB;AAAA,IAChB,oBAAoB;AAAA,IACpB,0BAA0B;AAAA,IAC1B;AAAA,EACJ,IAAM;AACJ,QAAM,CAAC,eAAe,gBAAgB,IAAIC,aAAAA,SAAe,IAAI;AAC7D,QAAM,wBAAwBC,aAAY,OAAC,KAAK;AAChD,QAAM,oBAAoBA,aAAY,OAAC,CAAC;AACxCC,eAAAA,UAAgB,MAAM;AACpB,UAAM,iBAAiB,kBAAkB;AACzC,WAAO,MAAM,OAAO,aAAa,cAAc;AAAA,EAChD,GAAE,EAAE;AACL,SAAuBC,kCAAG;AAAA,IACxB;AAAA,IACA;AAAA,MACE,OAAO;AAAA,MACP;AAAA,MACA;AAAA,MACA,QAAQC,aAAiB,YAAC,MAAM;AAC9B,eAAO,aAAa,kBAAkB,OAAO;AAC7C,yBAAiB,KAAK;AAAA,MACvB,GAAE,EAAE;AAAA,MACL,SAASA,aAAiB,YAAC,MAAM;AAC/B,eAAO,aAAa,kBAAkB,OAAO;AAC7C,0BAAkB,UAAU,OAAO;AAAA,UACjC,MAAM,iBAAiB,IAAI;AAAA,UAC3B;AAAA,QACD;AAAA,MACT,GAAS,CAAC,iBAAiB,CAAC;AAAA,MACtB;AAAA,MACA,0BAA0BA,aAAAA,YAAkB,CAAC,cAAc;AACzD,8BAAsB,UAAU;AAAA,MACjC,GAAE,EAAE;AAAA,MACL;AAAA,MACA;AAAA,IACN;AAAA,EACG;AACH;AACAL,kBAAgB,cAAc;AAC9B,IAAI,eAAe;AACnB,IAAI,CAAC,wBAAwB,iBAAiB,IAAI,qBAAqB,YAAY;AACnF,IAAIM,YAAU,CAAC,UAAU;AACvB,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA,MAAM;AAAA,IACN,cAAc;AAAA,IACd;AAAA,IACA,yBAAyB;AAAA,IACzB,eAAe;AAAA,EACnB,IAAM;AACJ,QAAM,kBAAkB,0BAA0B,cAAc,MAAM,cAAc;AACpF,QAAM,cAAc,eAAe,cAAc;AACjD,QAAM,CAAC,SAAS,UAAU,IAAIL,aAAAA,SAAe,IAAI;AACjD,QAAM,YAAY,MAAO;AACzB,QAAM,eAAeC,aAAY,OAAC,CAAC;AACnC,QAAM,0BAA0B,+BAA+B,gBAAgB;AAC/E,QAAM,gBAAgB,qBAAqB,gBAAgB;AAC3D,QAAM,oBAAoBA,aAAY,OAAC,KAAK;AAC5C,QAAM,CAAC,OAAO,OAAO,OAAO,IAAI,qBAAqB;AAAA,IACnD,MAAM;AAAA,IACN,aAAa;AAAA,IACb,UAAU,CAAC,UAAU;AACnB,UAAI,OAAO;AACT,wBAAgB,OAAQ;AACxB,iBAAS,cAAc,IAAI,YAAY,YAAY,CAAC;AAAA,MAC5D,OAAa;AACL,wBAAgB,QAAS;AAAA,MACjC;AACM,mDAAe;AAAA,IACrB;AAAA,EACA,CAAG;AACD,QAAM,iBAAiBK,aAAAA,QAAc,MAAM;AACzC,WAAO,OAAO,kBAAkB,UAAU,iBAAiB,iBAAiB;AAAA,EAChF,GAAK,CAAC,IAAI,CAAC;AACT,QAAM,aAAaF,aAAAA,YAAkB,MAAM;AACzC,WAAO,aAAa,aAAa,OAAO;AACxC,iBAAa,UAAU;AACvB,sBAAkB,UAAU;AAC5B,YAAQ,IAAI;AAAA,EAChB,GAAK,CAAC,OAAO,CAAC;AACZ,QAAM,cAAcA,aAAAA,YAAkB,MAAM;AAC1C,WAAO,aAAa,aAAa,OAAO;AACxC,iBAAa,UAAU;AACvB,YAAQ,KAAK;AAAA,EACjB,GAAK,CAAC,OAAO,CAAC;AACZ,QAAM,oBAAoBA,aAAAA,YAAkB,MAAM;AAChD,WAAO,aAAa,aAAa,OAAO;AACxC,iBAAa,UAAU,OAAO,WAAW,MAAM;AAC7C,wBAAkB,UAAU;AAC5B,cAAQ,IAAI;AACZ,mBAAa,UAAU;AAAA,IACxB,GAAE,aAAa;AAAA,EACpB,GAAK,CAAC,eAAe,OAAO,CAAC;AAC3BF,eAAAA,UAAgB,MAAM;AACpB,WAAO,MAAM;AACX,UAAI,aAAa,SAAS;AACxB,eAAO,aAAa,aAAa,OAAO;AACxC,qBAAa,UAAU;AAAA,MAC/B;AAAA,IACK;AAAA,EACF,GAAE,EAAE;AACL,SAAuBC,kCAAG,IAACI,OAAsB,EAAE,GAAG,aAAa,UAA0BJ,kCAAG;AAAA,IAC9F;AAAA,IACA;AAAA,MACE,OAAO;AAAA,MACP;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,iBAAiB;AAAA,MACjB,gBAAgBC,aAAiB,YAAC,MAAM;AACtC,YAAI,gBAAgB,cAAe,mBAAmB;AAAA,YACjD,YAAY;AAAA,MAClB,GAAE,CAAC,gBAAgB,eAAe,mBAAmB,UAAU,CAAC;AAAA,MACjE,gBAAgBA,aAAiB,YAAC,MAAM;AACtC,YAAI,yBAAyB;AAC3B,sBAAa;AAAA,QACvB,OAAe;AACL,iBAAO,aAAa,aAAa,OAAO;AACxC,uBAAa,UAAU;AAAA,QACjC;AAAA,MACA,GAAS,CAAC,aAAa,uBAAuB,CAAC;AAAA,MACzC,QAAQ;AAAA,MACR,SAAS;AAAA,MACT;AAAA,MACA;AAAA,IACN;AAAA,EACA,GAAK;AACL;AACAC,UAAQ,cAAc;AACtB,IAAI,eAAe;AACnB,IAAIG,mBAAiBC,aAAgB;AAAA,EACnC,CAAC,OAAO,iBAAiB;AACvB,UAAM,EAAE,gBAAgB,GAAG,aAAY,IAAK;AAC5C,UAAM,UAAU,kBAAkB,cAAc,cAAc;AAC9D,UAAM,kBAAkB,0BAA0B,cAAc,cAAc;AAC9E,UAAM,cAAc,eAAe,cAAc;AACjD,UAAM,MAAMR,aAAY,OAAC,IAAI;AAC7B,UAAM,eAAe,gBAAgB,cAAc,KAAK,QAAQ,eAAe;AAC/E,UAAM,mBAAmBA,aAAY,OAAC,KAAK;AAC3C,UAAM,0BAA0BA,aAAY,OAAC,KAAK;AAClD,UAAM,kBAAkBG,aAAAA,YAAkB,MAAM,iBAAiB,UAAU,OAAO,EAAE;AACpFF,iBAAAA,UAAgB,MAAM;AACpB,aAAO,MAAM,SAAS,oBAAoB,aAAa,eAAe;AAAA,IAC5E,GAAO,CAAC,eAAe,CAAC;AACpB,WAAuBC,kCAAAA,IAAIO,QAAwB,EAAE,SAAS,MAAM,GAAG,aAAa,UAA0BP,kCAAG;AAAA,MAC/G,UAAU;AAAA,MACV;AAAA,QACE,oBAAoB,QAAQ,OAAO,QAAQ,YAAY;AAAA,QACvD,cAAc,QAAQ;AAAA,QACtB,GAAG;AAAA,QACH,KAAK;AAAA,QACL,eAAe,qBAAqB,MAAM,eAAe,CAAC,UAAU;AAClE,cAAI,MAAM,gBAAgB,QAAS;AACnC,cAAI,CAAC,wBAAwB,WAAW,CAAC,gBAAgB,sBAAsB,SAAS;AACtF,oBAAQ,eAAgB;AACxB,oCAAwB,UAAU;AAAA,UAC9C;AAAA,QACA,CAAS;AAAA,QACD,gBAAgB,qBAAqB,MAAM,gBAAgB,MAAM;AAC/D,kBAAQ,eAAgB;AACxB,kCAAwB,UAAU;AAAA,QAC5C,CAAS;AAAA,QACD,eAAe,qBAAqB,MAAM,eAAe,MAAM;AAC7D,2BAAiB,UAAU;AAC3B,mBAAS,iBAAiB,aAAa,iBAAiB,EAAE,MAAM,MAAM;AAAA,QAChF,CAAS;AAAA,QACD,SAAS,qBAAqB,MAAM,SAAS,MAAM;AACjD,cAAI,CAAC,iBAAiB,QAAS,SAAQ,OAAQ;AAAA,QACzD,CAAS;AAAA,QACD,QAAQ,qBAAqB,MAAM,QAAQ,QAAQ,OAAO;AAAA,QAC1D,SAAS,qBAAqB,MAAM,SAAS,QAAQ,OAAO;AAAA,MACpE;AAAA,IACA,GAAO;AAAA,EACP;AACA;AACAK,iBAAe,cAAc;AAC7B,IAAI,cAAc;AAClB,IAAI,CAAC,gBAAgB,gBAAgB,IAAI,qBAAqB,aAAa;AAAA,EACzE,YAAY;AACd,CAAC;AAOD,IAAI,eAAe;AACnB,IAAIG,mBAAiBF,aAAgB;AAAA,EACnC,CAAC,OAAO,iBAAiB;AACvB,UAAM,gBAAgB,iBAAiB,cAAc,MAAM,cAAc;AACzE,UAAM,EAAE,aAAa,cAAc,YAAY,OAAO,OAAO,GAAG,aAAY,IAAK;AACjF,UAAM,UAAU,kBAAkB,cAAc,MAAM,cAAc;AACpE,WAAuBN,kCAAG,IAAC,UAAU,EAAE,SAAS,cAAc,QAAQ,MAAM,UAAU,QAAQ,0BAA0CA,kCAAG,IAAC,oBAAoB,EAAE,MAAM,GAAG,cAAc,KAAK,cAAc,IAAoBA,kCAAG,IAAC,yBAAyB,EAAE,MAAM,GAAG,cAAc,KAAK,aAAY,CAAE,EAAC,CAAE;AAAA,EAChT;AACA;AACA,IAAI,0BAA0BM,aAAgB,WAAC,CAAC,OAAO,iBAAiB;AACtE,QAAM,UAAU,kBAAkB,cAAc,MAAM,cAAc;AACpE,QAAM,kBAAkB,0BAA0B,cAAc,MAAM,cAAc;AACpF,QAAM,MAAMR,aAAY,OAAC,IAAI;AAC7B,QAAM,eAAe,gBAAgB,cAAc,GAAG;AACtD,QAAM,CAAC,kBAAkB,mBAAmB,IAAID,aAAAA,SAAe,IAAI;AACnE,QAAM,EAAE,SAAS,QAAO,IAAK;AAC7B,QAAM,UAAU,IAAI;AACpB,QAAM,EAAE,yBAAwB,IAAK;AACrC,QAAM,wBAAwBI,aAAAA,YAAkB,MAAM;AACpD,wBAAoB,IAAI;AACxB,6BAAyB,KAAK;AAAA,EAClC,GAAK,CAAC,wBAAwB,CAAC;AAC7B,QAAM,wBAAwBA,aAAiB;AAAA,IAC7C,CAAC,OAAO,gBAAgB;AACtB,YAAM,gBAAgB,MAAM;AAC5B,YAAM,YAAY,EAAE,GAAG,MAAM,SAAS,GAAG,MAAM,QAAS;AACxD,YAAM,WAAW,oBAAoB,WAAW,cAAc,sBAAqB,CAAE;AACrF,YAAM,mBAAmB,oBAAoB,WAAW,QAAQ;AAChE,YAAM,oBAAoB,kBAAkB,YAAY,sBAAqB,CAAE;AAC/E,YAAM,YAAY,QAAQ,CAAC,GAAG,kBAAkB,GAAG,iBAAiB,CAAC;AACrE,0BAAoB,SAAS;AAC7B,+BAAyB,IAAI;AAAA,IAC9B;AAAA,IACD,CAAC,wBAAwB;AAAA,EAC1B;AACDF,eAAAA,UAAgB,MAAM;AACpB,WAAO,MAAM,sBAAuB;AAAA,EACxC,GAAK,CAAC,qBAAqB,CAAC;AAC1BA,eAAAA,UAAgB,MAAM;AACpB,QAAI,WAAW,SAAS;AACtB,YAAM,qBAAqB,CAAC,UAAU,sBAAsB,OAAO,OAAO;AAC1E,YAAM,qBAAqB,CAAC,UAAU,sBAAsB,OAAO,OAAO;AAC1E,cAAQ,iBAAiB,gBAAgB,kBAAkB;AAC3D,cAAQ,iBAAiB,gBAAgB,kBAAkB;AAC3D,aAAO,MAAM;AACX,gBAAQ,oBAAoB,gBAAgB,kBAAkB;AAC9D,gBAAQ,oBAAoB,gBAAgB,kBAAkB;AAAA,MAC/D;AAAA,IACP;AAAA,EACG,GAAE,CAAC,SAAS,SAAS,uBAAuB,qBAAqB,CAAC;AACnEA,eAAAA,UAAgB,MAAM;AACpB,QAAI,kBAAkB;AACpB,YAAM,0BAA0B,CAAC,UAAU;AACzC,cAAM,SAAS,MAAM;AACrB,cAAM,kBAAkB,EAAE,GAAG,MAAM,SAAS,GAAG,MAAM,QAAS;AAC9D,cAAM,oBAAmB,mCAAS,SAAS,aAAW,mCAAS,SAAS;AACxE,cAAM,4BAA4B,CAAC,iBAAiB,iBAAiB,gBAAgB;AACrF,YAAI,kBAAkB;AACpB,gCAAuB;AAAA,QACxB,WAAU,2BAA2B;AACpC,gCAAuB;AACvB,kBAAS;AAAA,QACnB;AAAA,MACO;AACD,eAAS,iBAAiB,eAAe,uBAAuB;AAChE,aAAO,MAAM,SAAS,oBAAoB,eAAe,uBAAuB;AAAA,IACtF;AAAA,EACA,GAAK,CAAC,SAAS,SAAS,kBAAkB,SAAS,qBAAqB,CAAC;AACvE,SAAuBC,kCAAAA,IAAI,oBAAoB,EAAE,GAAG,OAAO,KAAK,cAAc;AAChF,CAAC;AACD,IAAI,CAAC,sCAAsC,+BAA+B,IAAI,qBAAqB,cAAc,EAAE,UAAU,OAAO;AACpI,IAAI,qBAAqBM,aAAgB;AAAA,EACvC,CAAC,OAAO,iBAAiB;AACvB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA,cAAc;AAAA,MACd;AAAA,MACA;AAAA,MACA,GAAG;AAAA,IACT,IAAQ;AACJ,UAAM,UAAU,kBAAkB,cAAc,cAAc;AAC9D,UAAM,cAAc,eAAe,cAAc;AACjD,UAAM,EAAE,QAAO,IAAK;AACpBP,iBAAAA,UAAgB,MAAM;AACpB,eAAS,iBAAiB,cAAc,OAAO;AAC/C,aAAO,MAAM,SAAS,oBAAoB,cAAc,OAAO;AAAA,IACrE,GAAO,CAAC,OAAO,CAAC;AACZA,iBAAAA,UAAgB,MAAM;AACpB,UAAI,QAAQ,SAAS;AACnB,cAAM,eAAe,CAAC,UAAU;AAC9B,gBAAM,SAAS,MAAM;AACrB,cAAI,iCAAQ,SAAS,QAAQ,SAAU,SAAS;AAAA,QACjD;AACD,eAAO,iBAAiB,UAAU,cAAc,EAAE,SAAS,MAAM;AACjE,eAAO,MAAM,OAAO,oBAAoB,UAAU,cAAc,EAAE,SAAS,MAAM;AAAA,MACzF;AAAA,IACK,GAAE,CAAC,QAAQ,SAAS,OAAO,CAAC;AAC7B,WAAuBC,kCAAG;AAAA,MACxB;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,6BAA6B;AAAA,QAC7B;AAAA,QACA;AAAA,QACA,gBAAgB,CAAC,UAAU,MAAM,eAAgB;AAAA,QACjD,WAAW;AAAA,QACX,UAA0BS,kCAAI;AAAA,UAC5BC;AAAAA,UACA;AAAA,YACE,cAAc,QAAQ;AAAA,YACtB,GAAG;AAAA,YACH,GAAG;AAAA,YACH,KAAK;AAAA,YACL,OAAO;AAAA,cACL,GAAG,aAAa;AAAA;AAAA,cAEhB,GAAG;AAAA,gBACD,4CAA4C;AAAA,gBAC5C,2CAA2C;AAAA,gBAC3C,4CAA4C;AAAA,gBAC5C,iCAAiC;AAAA,gBACjC,kCAAkC;AAAA,cAClD;AAAA,YACa;AAAA,YACD,UAAU;AAAA,cACQV,sCAAI,WAAW,EAAE,UAAU;AAAA,cAC3BA,sCAAI,sCAAsC,EAAE,OAAO,gBAAgB,UAAU,MAAM,UAA0BA,sCAAIW,MAA8B,EAAE,IAAI,QAAQ,WAAW,MAAM,WAAW,UAAU,aAAa,SAAQ,CAAE,EAAG,CAAA;AAAA,YAC3P;AAAA,UACA;AAAA,QACA;AAAA,MACA;AAAA,IACK;AAAA,EACL;AACA;AACAH,iBAAe,cAAc;AAC7B,IAAI,aAAa;AACjB,IAAI,eAAeF,aAAgB;AAAA,EACjC,CAAC,OAAO,iBAAiB;AACvB,UAAM,EAAE,gBAAgB,GAAG,WAAU,IAAK;AAC1C,UAAM,cAAc,eAAe,cAAc;AACjD,UAAM,+BAA+B;AAAA,MACnC;AAAA,MACA;AAAA,IACD;AACD,WAAO,6BAA6B,WAAW,OAAuBN,kCAAG,IAACY,OAAuB,EAAE,GAAG,aAAa,GAAG,YAAY,KAAK,aAAY,CAAE;AAAA,EACzJ;AACA;AACA,aAAa,cAAc;AAC3B,SAAS,oBAAoB,OAAO,MAAM;AACxC,QAAM,MAAM,KAAK,IAAI,KAAK,MAAM,MAAM,CAAC;AACvC,QAAM,SAAS,KAAK,IAAI,KAAK,SAAS,MAAM,CAAC;AAC7C,QAAM,QAAQ,KAAK,IAAI,KAAK,QAAQ,MAAM,CAAC;AAC3C,QAAM,OAAO,KAAK,IAAI,KAAK,OAAO,MAAM,CAAC;AACzC,UAAQ,KAAK,IAAI,KAAK,QAAQ,OAAO,IAAI,GAAC;AAAA,IACxC,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT;AACE,YAAM,IAAI,MAAM,aAAa;AAAA,EACnC;AACA;AACA,SAAS,oBAAoB,WAAW,UAAU,UAAU,GAAG;AAC7D,QAAM,mBAAmB,CAAE;AAC3B,UAAQ,UAAQ;AAAA,IACd,KAAK;AACH,uBAAiB;AAAA,QACf,EAAE,GAAG,UAAU,IAAI,SAAS,GAAG,UAAU,IAAI,QAAS;AAAA,QACtD,EAAE,GAAG,UAAU,IAAI,SAAS,GAAG,UAAU,IAAI,QAAO;AAAA,MACrD;AACD;AAAA,IACF,KAAK;AACH,uBAAiB;AAAA,QACf,EAAE,GAAG,UAAU,IAAI,SAAS,GAAG,UAAU,IAAI,QAAS;AAAA,QACtD,EAAE,GAAG,UAAU,IAAI,SAAS,GAAG,UAAU,IAAI,QAAO;AAAA,MACrD;AACD;AAAA,IACF,KAAK;AACH,uBAAiB;AAAA,QACf,EAAE,GAAG,UAAU,IAAI,SAAS,GAAG,UAAU,IAAI,QAAS;AAAA,QACtD,EAAE,GAAG,UAAU,IAAI,SAAS,GAAG,UAAU,IAAI,QAAO;AAAA,MACrD;AACD;AAAA,IACF,KAAK;AACH,uBAAiB;AAAA,QACf,EAAE,GAAG,UAAU,IAAI,SAAS,GAAG,UAAU,IAAI,QAAS;AAAA,QACtD,EAAE,GAAG,UAAU,IAAI,SAAS,GAAG,UAAU,IAAI,QAAO;AAAA,MACrD;AACD;AAAA,EACN;AACE,SAAO;AACT;AACA,SAAS,kBAAkB,MAAM;AAC/B,QAAM,EAAE,KAAK,OAAO,QAAQ,KAAM,IAAG;AACrC,SAAO;AAAA,IACL,EAAE,GAAG,MAAM,GAAG,IAAK;AAAA,IACnB,EAAE,GAAG,OAAO,GAAG,IAAK;AAAA,IACpB,EAAE,GAAG,OAAO,GAAG,OAAQ;AAAA,IACvB,EAAE,GAAG,MAAM,GAAG,OAAM;AAAA,EACrB;AACH;AACA,SAAS,iBAAiB,OAAO,SAAS;AACxC,QAAM,EAAE,GAAG,EAAC,IAAK;AACjB,MAAI,SAAS;AACb,WAAS,IAAI,GAAG,IAAI,QAAQ,SAAS,GAAG,IAAI,QAAQ,QAAQ,IAAI,KAAK;AACnE,UAAM,KAAK,QAAQ,CAAC,EAAE;AACtB,UAAM,KAAK,QAAQ,CAAC,EAAE;AACtB,UAAM,KAAK,QAAQ,CAAC,EAAE;AACtB,UAAM,KAAK,QAAQ,CAAC,EAAE;AACtB,UAAM,YAAY,KAAK,MAAM,KAAK,KAAK,KAAK,KAAK,OAAO,IAAI,OAAO,KAAK,MAAM;AAC9E,QAAI,UAAW,UAAS,CAAC;AAAA,EAC7B;AACE,SAAO;AACT;AACA,SAAS,QAAQ,QAAQ;AACvB,QAAM,YAAY,OAAO,MAAO;AAChC,YAAU,KAAK,CAAC,GAAG,MAAM;AACvB,QAAI,EAAE,IAAI,EAAE,EAAG,QAAO;AAAA,aACb,EAAE,IAAI,EAAE,EAAG,QAAO;AAAA,aAClB,EAAE,IAAI,EAAE,EAAG,QAAO;AAAA,aAClB,EAAE,IAAI,EAAE,EAAG,QAAO;AAAA,QACtB,QAAO;AAAA,EAChB,CAAG;AACD,SAAO,iBAAiB,SAAS;AACnC;AACA,SAAS,iBAAiB,QAAQ;AAChC,MAAI,OAAO,UAAU,EAAG,QAAO,OAAO,MAAO;AAC7C,QAAM,YAAY,CAAE;AACpB,WAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,UAAM,IAAI,OAAO,CAAC;AAClB,WAAO,UAAU,UAAU,GAAG;AAC5B,YAAM,IAAI,UAAU,UAAU,SAAS,CAAC;AACxC,YAAM,IAAI,UAAU,UAAU,SAAS,CAAC;AACxC,WAAK,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,GAAI,WAAU,IAAK;AAAA,UACtE;AAAA,IACX;AACI,cAAU,KAAK,CAAC;AAAA,EACpB;AACE,YAAU,IAAK;AACf,QAAM,YAAY,CAAE;AACpB,WAAS,IAAI,OAAO,SAAS,GAAG,KAAK,GAAG,KAAK;AAC3C,UAAM,IAAI,OAAO,CAAC;AAClB,WAAO,UAAU,UAAU,GAAG;AAC5B,YAAM,IAAI,UAAU,UAAU,SAAS,CAAC;AACxC,YAAM,IAAI,UAAU,UAAU,SAAS,CAAC;AACxC,WAAK,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,GAAI,WAAU,IAAK;AAAA,UACtE;AAAA,IACX;AACI,cAAU,KAAK,CAAC;AAAA,EACpB;AACE,YAAU,IAAK;AACf,MAAI,UAAU,WAAW,KAAK,UAAU,WAAW,KAAK,UAAU,CAAC,EAAE,MAAM,UAAU,CAAC,EAAE,KAAK,UAAU,CAAC,EAAE,MAAM,UAAU,CAAC,EAAE,GAAG;AAC9H,WAAO;AAAA,EACX,OAAS;AACL,WAAO,UAAU,OAAO,SAAS;AAAA,EACrC;AACA;AACA,IAAI,WAAWhB;AACf,IAAI,QAAQM;AACZ,IAAI,UAAUG;AAEd,IAAI,WAAWG;AC9df,MAAM,kBAAkBK;AAExB,MAAM,UAAUC;AAEhB,MAAM,iBAAiBC;AAEjB,MAAA,iBAAiBT,aAAAA,WAGrB,CAAC,EAAE,WAAW,aAAa,GAAG,GAAG,SAAS,QAC1CN,kCAAA;AAAA,EAACgB;AAAAA,EAAA;AAAA,IACC;AAAA,IACA;AAAA,IACA,WAAW;AAAA,MACT;AAAA,MACA;AAAA,IACF;AAAA,IACC,GAAG;AAAA,EAAA;AACN,CACD;AACD,eAAe,cAAcA,SAAyB;", "x_google_ignoreList": [0]}