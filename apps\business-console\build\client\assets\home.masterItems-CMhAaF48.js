import { j as jsxRuntimeExports, r as reactExports } from "./jsx-runtime-bB2y7OuD.js";
import { u as useDebounce } from "./useDebounce-BXbH_IFZ.js";
import { B as Button } from "./button-ByAXMyvk.js";
import { I as Input } from "./input-3v87qohQ.js";
import { T as Tabs, a as TabsList, b as TabsTrigger, c as TabsContent } from "./tabs-CfSdyzWr.js";
import { D as Dialog, d as DialogTrigger, a as DialogContent, c as DialogHeader, b as DialogTitle, e as DialogFooter } from "./dialog-BqKosxNq.js";
import { P as Pagination, a as PaginationContent, b as PaginationItem, c as PaginationPrevious, f as PaginationEllipsis, d as PaginationLink, e as PaginationNext } from "./pagination-DzgbTb6G.js";
import { L as Label } from "./label-cSASrwzW.js";
import { R as RadioGroup, a as RadioGroupItem } from "./radio-group-ChzooXbR.js";
import { a as useFetcher, u as useLoaderData } from "./components-D7UvGag_.js";
import { J } from "./textarea-DWEAjqHe.js";
import { S as Select, a as SelectTrigger, b as SelectValue, c as SelectContent, d as SelectItem } from "./select-BFGSXKcr.js";
import { a as useNavigate } from "./index-DhHTcibu.js";
import "./index-z_byfFrQ.js";
import "./index-ImHKLo0a.js";
import "./utils-GkgzjW3c.js";
import "./index-D7VH9Fc8.js";
import "./index-CG37gmC0.js";
import "./index-qCtcpOcW.js";
import "./index-DVTNuYOr.js";
import "./index-CzZ6EYkN.js";
import "./index-Vp2vNLNM.js";
import "./index-dIGjFc0I.js";
import "./index-BTdCMChR.js";
import "./index-QLGF6kQx.js";
import "./index-DdafHWkt.js";
import "./index-BXzPK7u0.js";
import "./index-CEHS9zzk.js";
import "./x-CCG_WJDF.js";
import "./createLucideIcon-uwkRm45G.js";
import "./chevron-left-CLqBlTg1.js";
import "./chevron-right-B-tR7Kir.js";
import "./index-CkL5tk39.js";
import "./index-CpKiYcZd.js";
import "./use-sync-refs-DLXpJTw-.js";
import "./index-IXOTxK3N.js";
import "./index-DscYByPT.js";
import "./index-C88PRvfd.js";
import "./chevron-down-pCP5jmjX.js";
import "./check-_dbWxIzT.js";
var util;
(function(util2) {
  util2.assertEqual = (val) => val;
  function assertIs(_arg) {
  }
  util2.assertIs = assertIs;
  function assertNever(_x) {
    throw new Error();
  }
  util2.assertNever = assertNever;
  util2.arrayToEnum = (items) => {
    const obj = {};
    for (const item of items) {
      obj[item] = item;
    }
    return obj;
  };
  util2.getValidEnumValues = (obj) => {
    const validKeys = util2.objectKeys(obj).filter((k) => typeof obj[obj[k]] !== "number");
    const filtered = {};
    for (const k of validKeys) {
      filtered[k] = obj[k];
    }
    return util2.objectValues(filtered);
  };
  util2.objectValues = (obj) => {
    return util2.objectKeys(obj).map(function(e) {
      return obj[e];
    });
  };
  util2.objectKeys = typeof Object.keys === "function" ? (obj) => Object.keys(obj) : (object) => {
    const keys = [];
    for (const key in object) {
      if (Object.prototype.hasOwnProperty.call(object, key)) {
        keys.push(key);
      }
    }
    return keys;
  };
  util2.find = (arr, checker) => {
    for (const item of arr) {
      if (checker(item))
        return item;
    }
    return void 0;
  };
  util2.isInteger = typeof Number.isInteger === "function" ? (val) => Number.isInteger(val) : (val) => typeof val === "number" && isFinite(val) && Math.floor(val) === val;
  function joinValues(array, separator = " | ") {
    return array.map((val) => typeof val === "string" ? `'${val}'` : val).join(separator);
  }
  util2.joinValues = joinValues;
  util2.jsonStringifyReplacer = (_, value) => {
    if (typeof value === "bigint") {
      return value.toString();
    }
    return value;
  };
})(util || (util = {}));
var objectUtil;
(function(objectUtil2) {
  objectUtil2.mergeShapes = (first, second) => {
    return {
      ...first,
      ...second
      // second overwrites first
    };
  };
})(objectUtil || (objectUtil = {}));
const ZodParsedType = util.arrayToEnum([
  "string",
  "nan",
  "number",
  "integer",
  "float",
  "boolean",
  "date",
  "bigint",
  "symbol",
  "function",
  "undefined",
  "null",
  "array",
  "object",
  "unknown",
  "promise",
  "void",
  "never",
  "map",
  "set"
]);
const getParsedType = (data) => {
  const t = typeof data;
  switch (t) {
    case "undefined":
      return ZodParsedType.undefined;
    case "string":
      return ZodParsedType.string;
    case "number":
      return isNaN(data) ? ZodParsedType.nan : ZodParsedType.number;
    case "boolean":
      return ZodParsedType.boolean;
    case "function":
      return ZodParsedType.function;
    case "bigint":
      return ZodParsedType.bigint;
    case "symbol":
      return ZodParsedType.symbol;
    case "object":
      if (Array.isArray(data)) {
        return ZodParsedType.array;
      }
      if (data === null) {
        return ZodParsedType.null;
      }
      if (data.then && typeof data.then === "function" && data.catch && typeof data.catch === "function") {
        return ZodParsedType.promise;
      }
      if (typeof Map !== "undefined" && data instanceof Map) {
        return ZodParsedType.map;
      }
      if (typeof Set !== "undefined" && data instanceof Set) {
        return ZodParsedType.set;
      }
      if (typeof Date !== "undefined" && data instanceof Date) {
        return ZodParsedType.date;
      }
      return ZodParsedType.object;
    default:
      return ZodParsedType.unknown;
  }
};
const ZodIssueCode = util.arrayToEnum([
  "invalid_type",
  "invalid_literal",
  "custom",
  "invalid_union",
  "invalid_union_discriminator",
  "invalid_enum_value",
  "unrecognized_keys",
  "invalid_arguments",
  "invalid_return_type",
  "invalid_date",
  "invalid_string",
  "too_small",
  "too_big",
  "invalid_intersection_types",
  "not_multiple_of",
  "not_finite"
]);
const quotelessJson = (obj) => {
  const json = JSON.stringify(obj, null, 2);
  return json.replace(/"([^"]+)":/g, "$1:");
};
class ZodError extends Error {
  get errors() {
    return this.issues;
  }
  constructor(issues) {
    super();
    this.issues = [];
    this.addIssue = (sub) => {
      this.issues = [...this.issues, sub];
    };
    this.addIssues = (subs = []) => {
      this.issues = [...this.issues, ...subs];
    };
    const actualProto = new.target.prototype;
    if (Object.setPrototypeOf) {
      Object.setPrototypeOf(this, actualProto);
    } else {
      this.__proto__ = actualProto;
    }
    this.name = "ZodError";
    this.issues = issues;
  }
  format(_mapper) {
    const mapper = _mapper || function(issue) {
      return issue.message;
    };
    const fieldErrors = { _errors: [] };
    const processError = (error) => {
      for (const issue of error.issues) {
        if (issue.code === "invalid_union") {
          issue.unionErrors.map(processError);
        } else if (issue.code === "invalid_return_type") {
          processError(issue.returnTypeError);
        } else if (issue.code === "invalid_arguments") {
          processError(issue.argumentsError);
        } else if (issue.path.length === 0) {
          fieldErrors._errors.push(mapper(issue));
        } else {
          let curr = fieldErrors;
          let i = 0;
          while (i < issue.path.length) {
            const el = issue.path[i];
            const terminal = i === issue.path.length - 1;
            if (!terminal) {
              curr[el] = curr[el] || { _errors: [] };
            } else {
              curr[el] = curr[el] || { _errors: [] };
              curr[el]._errors.push(mapper(issue));
            }
            curr = curr[el];
            i++;
          }
        }
      }
    };
    processError(this);
    return fieldErrors;
  }
  static assert(value) {
    if (!(value instanceof ZodError)) {
      throw new Error(`Not a ZodError: ${value}`);
    }
  }
  toString() {
    return this.message;
  }
  get message() {
    return JSON.stringify(this.issues, util.jsonStringifyReplacer, 2);
  }
  get isEmpty() {
    return this.issues.length === 0;
  }
  flatten(mapper = (issue) => issue.message) {
    const fieldErrors = {};
    const formErrors = [];
    for (const sub of this.issues) {
      if (sub.path.length > 0) {
        fieldErrors[sub.path[0]] = fieldErrors[sub.path[0]] || [];
        fieldErrors[sub.path[0]].push(mapper(sub));
      } else {
        formErrors.push(mapper(sub));
      }
    }
    return { formErrors, fieldErrors };
  }
  get formErrors() {
    return this.flatten();
  }
}
ZodError.create = (issues) => {
  const error = new ZodError(issues);
  return error;
};
const errorMap = (issue, _ctx) => {
  let message;
  switch (issue.code) {
    case ZodIssueCode.invalid_type:
      if (issue.received === ZodParsedType.undefined) {
        message = "Required";
      } else {
        message = `Expected ${issue.expected}, received ${issue.received}`;
      }
      break;
    case ZodIssueCode.invalid_literal:
      message = `Invalid literal value, expected ${JSON.stringify(issue.expected, util.jsonStringifyReplacer)}`;
      break;
    case ZodIssueCode.unrecognized_keys:
      message = `Unrecognized key(s) in object: ${util.joinValues(issue.keys, ", ")}`;
      break;
    case ZodIssueCode.invalid_union:
      message = `Invalid input`;
      break;
    case ZodIssueCode.invalid_union_discriminator:
      message = `Invalid discriminator value. Expected ${util.joinValues(issue.options)}`;
      break;
    case ZodIssueCode.invalid_enum_value:
      message = `Invalid enum value. Expected ${util.joinValues(issue.options)}, received '${issue.received}'`;
      break;
    case ZodIssueCode.invalid_arguments:
      message = `Invalid function arguments`;
      break;
    case ZodIssueCode.invalid_return_type:
      message = `Invalid function return type`;
      break;
    case ZodIssueCode.invalid_date:
      message = `Invalid date`;
      break;
    case ZodIssueCode.invalid_string:
      if (typeof issue.validation === "object") {
        if ("includes" in issue.validation) {
          message = `Invalid input: must include "${issue.validation.includes}"`;
          if (typeof issue.validation.position === "number") {
            message = `${message} at one or more positions greater than or equal to ${issue.validation.position}`;
          }
        } else if ("startsWith" in issue.validation) {
          message = `Invalid input: must start with "${issue.validation.startsWith}"`;
        } else if ("endsWith" in issue.validation) {
          message = `Invalid input: must end with "${issue.validation.endsWith}"`;
        } else {
          util.assertNever(issue.validation);
        }
      } else if (issue.validation !== "regex") {
        message = `Invalid ${issue.validation}`;
      } else {
        message = "Invalid";
      }
      break;
    case ZodIssueCode.too_small:
      if (issue.type === "array")
        message = `Array must contain ${issue.exact ? "exactly" : issue.inclusive ? `at least` : `more than`} ${issue.minimum} element(s)`;
      else if (issue.type === "string")
        message = `String must contain ${issue.exact ? "exactly" : issue.inclusive ? `at least` : `over`} ${issue.minimum} character(s)`;
      else if (issue.type === "number")
        message = `Number must be ${issue.exact ? `exactly equal to ` : issue.inclusive ? `greater than or equal to ` : `greater than `}${issue.minimum}`;
      else if (issue.type === "date")
        message = `Date must be ${issue.exact ? `exactly equal to ` : issue.inclusive ? `greater than or equal to ` : `greater than `}${new Date(Number(issue.minimum))}`;
      else
        message = "Invalid input";
      break;
    case ZodIssueCode.too_big:
      if (issue.type === "array")
        message = `Array must contain ${issue.exact ? `exactly` : issue.inclusive ? `at most` : `less than`} ${issue.maximum} element(s)`;
      else if (issue.type === "string")
        message = `String must contain ${issue.exact ? `exactly` : issue.inclusive ? `at most` : `under`} ${issue.maximum} character(s)`;
      else if (issue.type === "number")
        message = `Number must be ${issue.exact ? `exactly` : issue.inclusive ? `less than or equal to` : `less than`} ${issue.maximum}`;
      else if (issue.type === "bigint")
        message = `BigInt must be ${issue.exact ? `exactly` : issue.inclusive ? `less than or equal to` : `less than`} ${issue.maximum}`;
      else if (issue.type === "date")
        message = `Date must be ${issue.exact ? `exactly` : issue.inclusive ? `smaller than or equal to` : `smaller than`} ${new Date(Number(issue.maximum))}`;
      else
        message = "Invalid input";
      break;
    case ZodIssueCode.custom:
      message = `Invalid input`;
      break;
    case ZodIssueCode.invalid_intersection_types:
      message = `Intersection results could not be merged`;
      break;
    case ZodIssueCode.not_multiple_of:
      message = `Number must be a multiple of ${issue.multipleOf}`;
      break;
    case ZodIssueCode.not_finite:
      message = "Number must be finite";
      break;
    default:
      message = _ctx.defaultError;
      util.assertNever(issue);
  }
  return { message };
};
let overrideErrorMap = errorMap;
function setErrorMap(map) {
  overrideErrorMap = map;
}
function getErrorMap() {
  return overrideErrorMap;
}
const makeIssue = (params) => {
  const { data, path, errorMaps, issueData } = params;
  const fullPath = [...path, ...issueData.path || []];
  const fullIssue = {
    ...issueData,
    path: fullPath
  };
  if (issueData.message !== void 0) {
    return {
      ...issueData,
      path: fullPath,
      message: issueData.message
    };
  }
  let errorMessage = "";
  const maps = errorMaps.filter((m) => !!m).slice().reverse();
  for (const map of maps) {
    errorMessage = map(fullIssue, { data, defaultError: errorMessage }).message;
  }
  return {
    ...issueData,
    path: fullPath,
    message: errorMessage
  };
};
const EMPTY_PATH = [];
function addIssueToContext(ctx, issueData) {
  const overrideMap = getErrorMap();
  const issue = makeIssue({
    issueData,
    data: ctx.data,
    path: ctx.path,
    errorMaps: [
      ctx.common.contextualErrorMap,
      // contextual error map is first priority
      ctx.schemaErrorMap,
      // then schema-bound map if available
      overrideMap,
      // then global override map
      overrideMap === errorMap ? void 0 : errorMap
      // then global default map
    ].filter((x) => !!x)
  });
  ctx.common.issues.push(issue);
}
class ParseStatus {
  constructor() {
    this.value = "valid";
  }
  dirty() {
    if (this.value === "valid")
      this.value = "dirty";
  }
  abort() {
    if (this.value !== "aborted")
      this.value = "aborted";
  }
  static mergeArray(status, results) {
    const arrayValue = [];
    for (const s of results) {
      if (s.status === "aborted")
        return INVALID;
      if (s.status === "dirty")
        status.dirty();
      arrayValue.push(s.value);
    }
    return { status: status.value, value: arrayValue };
  }
  static async mergeObjectAsync(status, pairs) {
    const syncPairs = [];
    for (const pair of pairs) {
      const key = await pair.key;
      const value = await pair.value;
      syncPairs.push({
        key,
        value
      });
    }
    return ParseStatus.mergeObjectSync(status, syncPairs);
  }
  static mergeObjectSync(status, pairs) {
    const finalObject = {};
    for (const pair of pairs) {
      const { key, value } = pair;
      if (key.status === "aborted")
        return INVALID;
      if (value.status === "aborted")
        return INVALID;
      if (key.status === "dirty")
        status.dirty();
      if (value.status === "dirty")
        status.dirty();
      if (key.value !== "__proto__" && (typeof value.value !== "undefined" || pair.alwaysSet)) {
        finalObject[key.value] = value.value;
      }
    }
    return { status: status.value, value: finalObject };
  }
}
const INVALID = Object.freeze({
  status: "aborted"
});
const DIRTY = (value) => ({ status: "dirty", value });
const OK = (value) => ({ status: "valid", value });
const isAborted = (x) => x.status === "aborted";
const isDirty = (x) => x.status === "dirty";
const isValid = (x) => x.status === "valid";
const isAsync = (x) => typeof Promise !== "undefined" && x instanceof Promise;
function __classPrivateFieldGet(receiver, state, kind, f) {
  if (typeof state === "function" ? receiver !== state || true : !state.has(receiver)) throw new TypeError("Cannot read private member from an object whose class did not declare it");
  return state.get(receiver);
}
function __classPrivateFieldSet(receiver, state, value, kind, f) {
  if (typeof state === "function" ? receiver !== state || true : !state.has(receiver)) throw new TypeError("Cannot write private member to an object whose class did not declare it");
  return state.set(receiver, value), value;
}
typeof SuppressedError === "function" ? SuppressedError : function(error, suppressed, message) {
  var e = new Error(message);
  return e.name = "SuppressedError", e.error = error, e.suppressed = suppressed, e;
};
var errorUtil;
(function(errorUtil2) {
  errorUtil2.errToObj = (message) => typeof message === "string" ? { message } : message || {};
  errorUtil2.toString = (message) => typeof message === "string" ? message : message === null || message === void 0 ? void 0 : message.message;
})(errorUtil || (errorUtil = {}));
var _ZodEnum_cache, _ZodNativeEnum_cache;
class ParseInputLazyPath {
  constructor(parent, value, path, key) {
    this._cachedPath = [];
    this.parent = parent;
    this.data = value;
    this._path = path;
    this._key = key;
  }
  get path() {
    if (!this._cachedPath.length) {
      if (this._key instanceof Array) {
        this._cachedPath.push(...this._path, ...this._key);
      } else {
        this._cachedPath.push(...this._path, this._key);
      }
    }
    return this._cachedPath;
  }
}
const handleResult = (ctx, result) => {
  if (isValid(result)) {
    return { success: true, data: result.value };
  } else {
    if (!ctx.common.issues.length) {
      throw new Error("Validation failed but no issues detected.");
    }
    return {
      success: false,
      get error() {
        if (this._error)
          return this._error;
        const error = new ZodError(ctx.common.issues);
        this._error = error;
        return this._error;
      }
    };
  }
};
function processCreateParams(params) {
  if (!params)
    return {};
  const { errorMap: errorMap2, invalid_type_error, required_error, description } = params;
  if (errorMap2 && (invalid_type_error || required_error)) {
    throw new Error(`Can't use "invalid_type_error" or "required_error" in conjunction with custom error map.`);
  }
  if (errorMap2)
    return { errorMap: errorMap2, description };
  const customMap = (iss, ctx) => {
    var _a, _b;
    const { message } = params;
    if (iss.code === "invalid_enum_value") {
      return { message: message !== null && message !== void 0 ? message : ctx.defaultError };
    }
    if (typeof ctx.data === "undefined") {
      return { message: (_a = message !== null && message !== void 0 ? message : required_error) !== null && _a !== void 0 ? _a : ctx.defaultError };
    }
    if (iss.code !== "invalid_type")
      return { message: ctx.defaultError };
    return { message: (_b = message !== null && message !== void 0 ? message : invalid_type_error) !== null && _b !== void 0 ? _b : ctx.defaultError };
  };
  return { errorMap: customMap, description };
}
class ZodType {
  get description() {
    return this._def.description;
  }
  _getType(input) {
    return getParsedType(input.data);
  }
  _getOrReturnCtx(input, ctx) {
    return ctx || {
      common: input.parent.common,
      data: input.data,
      parsedType: getParsedType(input.data),
      schemaErrorMap: this._def.errorMap,
      path: input.path,
      parent: input.parent
    };
  }
  _processInputParams(input) {
    return {
      status: new ParseStatus(),
      ctx: {
        common: input.parent.common,
        data: input.data,
        parsedType: getParsedType(input.data),
        schemaErrorMap: this._def.errorMap,
        path: input.path,
        parent: input.parent
      }
    };
  }
  _parseSync(input) {
    const result = this._parse(input);
    if (isAsync(result)) {
      throw new Error("Synchronous parse encountered promise.");
    }
    return result;
  }
  _parseAsync(input) {
    const result = this._parse(input);
    return Promise.resolve(result);
  }
  parse(data, params) {
    const result = this.safeParse(data, params);
    if (result.success)
      return result.data;
    throw result.error;
  }
  safeParse(data, params) {
    var _a;
    const ctx = {
      common: {
        issues: [],
        async: (_a = params === null || params === void 0 ? void 0 : params.async) !== null && _a !== void 0 ? _a : false,
        contextualErrorMap: params === null || params === void 0 ? void 0 : params.errorMap
      },
      path: (params === null || params === void 0 ? void 0 : params.path) || [],
      schemaErrorMap: this._def.errorMap,
      parent: null,
      data,
      parsedType: getParsedType(data)
    };
    const result = this._parseSync({ data, path: ctx.path, parent: ctx });
    return handleResult(ctx, result);
  }
  "~validate"(data) {
    var _a, _b;
    const ctx = {
      common: {
        issues: [],
        async: !!this["~standard"].async
      },
      path: [],
      schemaErrorMap: this._def.errorMap,
      parent: null,
      data,
      parsedType: getParsedType(data)
    };
    if (!this["~standard"].async) {
      try {
        const result = this._parseSync({ data, path: [], parent: ctx });
        return isValid(result) ? {
          value: result.value
        } : {
          issues: ctx.common.issues
        };
      } catch (err) {
        if ((_b = (_a = err === null || err === void 0 ? void 0 : err.message) === null || _a === void 0 ? void 0 : _a.toLowerCase()) === null || _b === void 0 ? void 0 : _b.includes("encountered")) {
          this["~standard"].async = true;
        }
        ctx.common = {
          issues: [],
          async: true
        };
      }
    }
    return this._parseAsync({ data, path: [], parent: ctx }).then((result) => isValid(result) ? {
      value: result.value
    } : {
      issues: ctx.common.issues
    });
  }
  async parseAsync(data, params) {
    const result = await this.safeParseAsync(data, params);
    if (result.success)
      return result.data;
    throw result.error;
  }
  async safeParseAsync(data, params) {
    const ctx = {
      common: {
        issues: [],
        contextualErrorMap: params === null || params === void 0 ? void 0 : params.errorMap,
        async: true
      },
      path: (params === null || params === void 0 ? void 0 : params.path) || [],
      schemaErrorMap: this._def.errorMap,
      parent: null,
      data,
      parsedType: getParsedType(data)
    };
    const maybeAsyncResult = this._parse({ data, path: ctx.path, parent: ctx });
    const result = await (isAsync(maybeAsyncResult) ? maybeAsyncResult : Promise.resolve(maybeAsyncResult));
    return handleResult(ctx, result);
  }
  refine(check, message) {
    const getIssueProperties = (val) => {
      if (typeof message === "string" || typeof message === "undefined") {
        return { message };
      } else if (typeof message === "function") {
        return message(val);
      } else {
        return message;
      }
    };
    return this._refinement((val, ctx) => {
      const result = check(val);
      const setError = () => ctx.addIssue({
        code: ZodIssueCode.custom,
        ...getIssueProperties(val)
      });
      if (typeof Promise !== "undefined" && result instanceof Promise) {
        return result.then((data) => {
          if (!data) {
            setError();
            return false;
          } else {
            return true;
          }
        });
      }
      if (!result) {
        setError();
        return false;
      } else {
        return true;
      }
    });
  }
  refinement(check, refinementData) {
    return this._refinement((val, ctx) => {
      if (!check(val)) {
        ctx.addIssue(typeof refinementData === "function" ? refinementData(val, ctx) : refinementData);
        return false;
      } else {
        return true;
      }
    });
  }
  _refinement(refinement) {
    return new ZodEffects({
      schema: this,
      typeName: ZodFirstPartyTypeKind.ZodEffects,
      effect: { type: "refinement", refinement }
    });
  }
  superRefine(refinement) {
    return this._refinement(refinement);
  }
  constructor(def) {
    this.spa = this.safeParseAsync;
    this._def = def;
    this.parse = this.parse.bind(this);
    this.safeParse = this.safeParse.bind(this);
    this.parseAsync = this.parseAsync.bind(this);
    this.safeParseAsync = this.safeParseAsync.bind(this);
    this.spa = this.spa.bind(this);
    this.refine = this.refine.bind(this);
    this.refinement = this.refinement.bind(this);
    this.superRefine = this.superRefine.bind(this);
    this.optional = this.optional.bind(this);
    this.nullable = this.nullable.bind(this);
    this.nullish = this.nullish.bind(this);
    this.array = this.array.bind(this);
    this.promise = this.promise.bind(this);
    this.or = this.or.bind(this);
    this.and = this.and.bind(this);
    this.transform = this.transform.bind(this);
    this.brand = this.brand.bind(this);
    this.default = this.default.bind(this);
    this.catch = this.catch.bind(this);
    this.describe = this.describe.bind(this);
    this.pipe = this.pipe.bind(this);
    this.readonly = this.readonly.bind(this);
    this.isNullable = this.isNullable.bind(this);
    this.isOptional = this.isOptional.bind(this);
    this["~standard"] = {
      version: 1,
      vendor: "zod",
      validate: (data) => this["~validate"](data)
    };
  }
  optional() {
    return ZodOptional.create(this, this._def);
  }
  nullable() {
    return ZodNullable.create(this, this._def);
  }
  nullish() {
    return this.nullable().optional();
  }
  array() {
    return ZodArray.create(this);
  }
  promise() {
    return ZodPromise.create(this, this._def);
  }
  or(option) {
    return ZodUnion.create([this, option], this._def);
  }
  and(incoming) {
    return ZodIntersection.create(this, incoming, this._def);
  }
  transform(transform) {
    return new ZodEffects({
      ...processCreateParams(this._def),
      schema: this,
      typeName: ZodFirstPartyTypeKind.ZodEffects,
      effect: { type: "transform", transform }
    });
  }
  default(def) {
    const defaultValueFunc = typeof def === "function" ? def : () => def;
    return new ZodDefault({
      ...processCreateParams(this._def),
      innerType: this,
      defaultValue: defaultValueFunc,
      typeName: ZodFirstPartyTypeKind.ZodDefault
    });
  }
  brand() {
    return new ZodBranded({
      typeName: ZodFirstPartyTypeKind.ZodBranded,
      type: this,
      ...processCreateParams(this._def)
    });
  }
  catch(def) {
    const catchValueFunc = typeof def === "function" ? def : () => def;
    return new ZodCatch({
      ...processCreateParams(this._def),
      innerType: this,
      catchValue: catchValueFunc,
      typeName: ZodFirstPartyTypeKind.ZodCatch
    });
  }
  describe(description) {
    const This = this.constructor;
    return new This({
      ...this._def,
      description
    });
  }
  pipe(target) {
    return ZodPipeline.create(this, target);
  }
  readonly() {
    return ZodReadonly.create(this);
  }
  isOptional() {
    return this.safeParse(void 0).success;
  }
  isNullable() {
    return this.safeParse(null).success;
  }
}
const cuidRegex = /^c[^\s-]{8,}$/i;
const cuid2Regex = /^[0-9a-z]+$/;
const ulidRegex = /^[0-9A-HJKMNP-TV-Z]{26}$/i;
const uuidRegex = /^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$/i;
const nanoidRegex = /^[a-z0-9_-]{21}$/i;
const jwtRegex = /^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/;
const durationRegex = /^[-+]?P(?!$)(?:(?:[-+]?\d+Y)|(?:[-+]?\d+[.,]\d+Y$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:(?:[-+]?\d+W)|(?:[-+]?\d+[.,]\d+W$))?(?:(?:[-+]?\d+D)|(?:[-+]?\d+[.,]\d+D$))?(?:T(?=[\d+-])(?:(?:[-+]?\d+H)|(?:[-+]?\d+[.,]\d+H$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:[-+]?\d+(?:[.,]\d+)?S)?)??$/;
const emailRegex = /^(?!\.)(?!.*\.\.)([A-Z0-9_'+\-\.]*)[A-Z0-9_+-]@([A-Z0-9][A-Z0-9\-]*\.)+[A-Z]{2,}$/i;
const _emojiRegex = `^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$`;
let emojiRegex;
const ipv4Regex = /^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/;
const ipv4CidrRegex = /^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/(3[0-2]|[12]?[0-9])$/;
const ipv6Regex = /^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))$/;
const ipv6CidrRegex = /^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/;
const base64Regex = /^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/;
const base64urlRegex = /^([0-9a-zA-Z-_]{4})*(([0-9a-zA-Z-_]{2}(==)?)|([0-9a-zA-Z-_]{3}(=)?))?$/;
const dateRegexSource = `((\\d\\d[2468][048]|\\d\\d[13579][26]|\\d\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\d{4}-((0[13578]|1[02])-(0[1-9]|[12]\\d|3[01])|(0[469]|11)-(0[1-9]|[12]\\d|30)|(02)-(0[1-9]|1\\d|2[0-8])))`;
const dateRegex = new RegExp(`^${dateRegexSource}$`);
function timeRegexSource(args) {
  let regex = `([01]\\d|2[0-3]):[0-5]\\d:[0-5]\\d`;
  if (args.precision) {
    regex = `${regex}\\.\\d{${args.precision}}`;
  } else if (args.precision == null) {
    regex = `${regex}(\\.\\d+)?`;
  }
  return regex;
}
function timeRegex(args) {
  return new RegExp(`^${timeRegexSource(args)}$`);
}
function datetimeRegex(args) {
  let regex = `${dateRegexSource}T${timeRegexSource(args)}`;
  const opts = [];
  opts.push(args.local ? `Z?` : `Z`);
  if (args.offset)
    opts.push(`([+-]\\d{2}:?\\d{2})`);
  regex = `${regex}(${opts.join("|")})`;
  return new RegExp(`^${regex}$`);
}
function isValidIP(ip, version) {
  if ((version === "v4" || !version) && ipv4Regex.test(ip)) {
    return true;
  }
  if ((version === "v6" || !version) && ipv6Regex.test(ip)) {
    return true;
  }
  return false;
}
function isValidJWT(jwt, alg) {
  if (!jwtRegex.test(jwt))
    return false;
  try {
    const [header] = jwt.split(".");
    const base64 = header.replace(/-/g, "+").replace(/_/g, "/").padEnd(header.length + (4 - header.length % 4) % 4, "=");
    const decoded = JSON.parse(atob(base64));
    if (typeof decoded !== "object" || decoded === null)
      return false;
    if (!decoded.typ || !decoded.alg)
      return false;
    if (alg && decoded.alg !== alg)
      return false;
    return true;
  } catch (_a) {
    return false;
  }
}
function isValidCidr(ip, version) {
  if ((version === "v4" || !version) && ipv4CidrRegex.test(ip)) {
    return true;
  }
  if ((version === "v6" || !version) && ipv6CidrRegex.test(ip)) {
    return true;
  }
  return false;
}
class ZodString extends ZodType {
  _parse(input) {
    if (this._def.coerce) {
      input.data = String(input.data);
    }
    const parsedType = this._getType(input);
    if (parsedType !== ZodParsedType.string) {
      const ctx2 = this._getOrReturnCtx(input);
      addIssueToContext(ctx2, {
        code: ZodIssueCode.invalid_type,
        expected: ZodParsedType.string,
        received: ctx2.parsedType
      });
      return INVALID;
    }
    const status = new ParseStatus();
    let ctx = void 0;
    for (const check of this._def.checks) {
      if (check.kind === "min") {
        if (input.data.length < check.value) {
          ctx = this._getOrReturnCtx(input, ctx);
          addIssueToContext(ctx, {
            code: ZodIssueCode.too_small,
            minimum: check.value,
            type: "string",
            inclusive: true,
            exact: false,
            message: check.message
          });
          status.dirty();
        }
      } else if (check.kind === "max") {
        if (input.data.length > check.value) {
          ctx = this._getOrReturnCtx(input, ctx);
          addIssueToContext(ctx, {
            code: ZodIssueCode.too_big,
            maximum: check.value,
            type: "string",
            inclusive: true,
            exact: false,
            message: check.message
          });
          status.dirty();
        }
      } else if (check.kind === "length") {
        const tooBig = input.data.length > check.value;
        const tooSmall = input.data.length < check.value;
        if (tooBig || tooSmall) {
          ctx = this._getOrReturnCtx(input, ctx);
          if (tooBig) {
            addIssueToContext(ctx, {
              code: ZodIssueCode.too_big,
              maximum: check.value,
              type: "string",
              inclusive: true,
              exact: true,
              message: check.message
            });
          } else if (tooSmall) {
            addIssueToContext(ctx, {
              code: ZodIssueCode.too_small,
              minimum: check.value,
              type: "string",
              inclusive: true,
              exact: true,
              message: check.message
            });
          }
          status.dirty();
        }
      } else if (check.kind === "email") {
        if (!emailRegex.test(input.data)) {
          ctx = this._getOrReturnCtx(input, ctx);
          addIssueToContext(ctx, {
            validation: "email",
            code: ZodIssueCode.invalid_string,
            message: check.message
          });
          status.dirty();
        }
      } else if (check.kind === "emoji") {
        if (!emojiRegex) {
          emojiRegex = new RegExp(_emojiRegex, "u");
        }
        if (!emojiRegex.test(input.data)) {
          ctx = this._getOrReturnCtx(input, ctx);
          addIssueToContext(ctx, {
            validation: "emoji",
            code: ZodIssueCode.invalid_string,
            message: check.message
          });
          status.dirty();
        }
      } else if (check.kind === "uuid") {
        if (!uuidRegex.test(input.data)) {
          ctx = this._getOrReturnCtx(input, ctx);
          addIssueToContext(ctx, {
            validation: "uuid",
            code: ZodIssueCode.invalid_string,
            message: check.message
          });
          status.dirty();
        }
      } else if (check.kind === "nanoid") {
        if (!nanoidRegex.test(input.data)) {
          ctx = this._getOrReturnCtx(input, ctx);
          addIssueToContext(ctx, {
            validation: "nanoid",
            code: ZodIssueCode.invalid_string,
            message: check.message
          });
          status.dirty();
        }
      } else if (check.kind === "cuid") {
        if (!cuidRegex.test(input.data)) {
          ctx = this._getOrReturnCtx(input, ctx);
          addIssueToContext(ctx, {
            validation: "cuid",
            code: ZodIssueCode.invalid_string,
            message: check.message
          });
          status.dirty();
        }
      } else if (check.kind === "cuid2") {
        if (!cuid2Regex.test(input.data)) {
          ctx = this._getOrReturnCtx(input, ctx);
          addIssueToContext(ctx, {
            validation: "cuid2",
            code: ZodIssueCode.invalid_string,
            message: check.message
          });
          status.dirty();
        }
      } else if (check.kind === "ulid") {
        if (!ulidRegex.test(input.data)) {
          ctx = this._getOrReturnCtx(input, ctx);
          addIssueToContext(ctx, {
            validation: "ulid",
            code: ZodIssueCode.invalid_string,
            message: check.message
          });
          status.dirty();
        }
      } else if (check.kind === "url") {
        try {
          new URL(input.data);
        } catch (_a) {
          ctx = this._getOrReturnCtx(input, ctx);
          addIssueToContext(ctx, {
            validation: "url",
            code: ZodIssueCode.invalid_string,
            message: check.message
          });
          status.dirty();
        }
      } else if (check.kind === "regex") {
        check.regex.lastIndex = 0;
        const testResult = check.regex.test(input.data);
        if (!testResult) {
          ctx = this._getOrReturnCtx(input, ctx);
          addIssueToContext(ctx, {
            validation: "regex",
            code: ZodIssueCode.invalid_string,
            message: check.message
          });
          status.dirty();
        }
      } else if (check.kind === "trim") {
        input.data = input.data.trim();
      } else if (check.kind === "includes") {
        if (!input.data.includes(check.value, check.position)) {
          ctx = this._getOrReturnCtx(input, ctx);
          addIssueToContext(ctx, {
            code: ZodIssueCode.invalid_string,
            validation: { includes: check.value, position: check.position },
            message: check.message
          });
          status.dirty();
        }
      } else if (check.kind === "toLowerCase") {
        input.data = input.data.toLowerCase();
      } else if (check.kind === "toUpperCase") {
        input.data = input.data.toUpperCase();
      } else if (check.kind === "startsWith") {
        if (!input.data.startsWith(check.value)) {
          ctx = this._getOrReturnCtx(input, ctx);
          addIssueToContext(ctx, {
            code: ZodIssueCode.invalid_string,
            validation: { startsWith: check.value },
            message: check.message
          });
          status.dirty();
        }
      } else if (check.kind === "endsWith") {
        if (!input.data.endsWith(check.value)) {
          ctx = this._getOrReturnCtx(input, ctx);
          addIssueToContext(ctx, {
            code: ZodIssueCode.invalid_string,
            validation: { endsWith: check.value },
            message: check.message
          });
          status.dirty();
        }
      } else if (check.kind === "datetime") {
        const regex = datetimeRegex(check);
        if (!regex.test(input.data)) {
          ctx = this._getOrReturnCtx(input, ctx);
          addIssueToContext(ctx, {
            code: ZodIssueCode.invalid_string,
            validation: "datetime",
            message: check.message
          });
          status.dirty();
        }
      } else if (check.kind === "date") {
        const regex = dateRegex;
        if (!regex.test(input.data)) {
          ctx = this._getOrReturnCtx(input, ctx);
          addIssueToContext(ctx, {
            code: ZodIssueCode.invalid_string,
            validation: "date",
            message: check.message
          });
          status.dirty();
        }
      } else if (check.kind === "time") {
        const regex = timeRegex(check);
        if (!regex.test(input.data)) {
          ctx = this._getOrReturnCtx(input, ctx);
          addIssueToContext(ctx, {
            code: ZodIssueCode.invalid_string,
            validation: "time",
            message: check.message
          });
          status.dirty();
        }
      } else if (check.kind === "duration") {
        if (!durationRegex.test(input.data)) {
          ctx = this._getOrReturnCtx(input, ctx);
          addIssueToContext(ctx, {
            validation: "duration",
            code: ZodIssueCode.invalid_string,
            message: check.message
          });
          status.dirty();
        }
      } else if (check.kind === "ip") {
        if (!isValidIP(input.data, check.version)) {
          ctx = this._getOrReturnCtx(input, ctx);
          addIssueToContext(ctx, {
            validation: "ip",
            code: ZodIssueCode.invalid_string,
            message: check.message
          });
          status.dirty();
        }
      } else if (check.kind === "jwt") {
        if (!isValidJWT(input.data, check.alg)) {
          ctx = this._getOrReturnCtx(input, ctx);
          addIssueToContext(ctx, {
            validation: "jwt",
            code: ZodIssueCode.invalid_string,
            message: check.message
          });
          status.dirty();
        }
      } else if (check.kind === "cidr") {
        if (!isValidCidr(input.data, check.version)) {
          ctx = this._getOrReturnCtx(input, ctx);
          addIssueToContext(ctx, {
            validation: "cidr",
            code: ZodIssueCode.invalid_string,
            message: check.message
          });
          status.dirty();
        }
      } else if (check.kind === "base64") {
        if (!base64Regex.test(input.data)) {
          ctx = this._getOrReturnCtx(input, ctx);
          addIssueToContext(ctx, {
            validation: "base64",
            code: ZodIssueCode.invalid_string,
            message: check.message
          });
          status.dirty();
        }
      } else if (check.kind === "base64url") {
        if (!base64urlRegex.test(input.data)) {
          ctx = this._getOrReturnCtx(input, ctx);
          addIssueToContext(ctx, {
            validation: "base64url",
            code: ZodIssueCode.invalid_string,
            message: check.message
          });
          status.dirty();
        }
      } else {
        util.assertNever(check);
      }
    }
    return { status: status.value, value: input.data };
  }
  _regex(regex, validation, message) {
    return this.refinement((data) => regex.test(data), {
      validation,
      code: ZodIssueCode.invalid_string,
      ...errorUtil.errToObj(message)
    });
  }
  _addCheck(check) {
    return new ZodString({
      ...this._def,
      checks: [...this._def.checks, check]
    });
  }
  email(message) {
    return this._addCheck({ kind: "email", ...errorUtil.errToObj(message) });
  }
  url(message) {
    return this._addCheck({ kind: "url", ...errorUtil.errToObj(message) });
  }
  emoji(message) {
    return this._addCheck({ kind: "emoji", ...errorUtil.errToObj(message) });
  }
  uuid(message) {
    return this._addCheck({ kind: "uuid", ...errorUtil.errToObj(message) });
  }
  nanoid(message) {
    return this._addCheck({ kind: "nanoid", ...errorUtil.errToObj(message) });
  }
  cuid(message) {
    return this._addCheck({ kind: "cuid", ...errorUtil.errToObj(message) });
  }
  cuid2(message) {
    return this._addCheck({ kind: "cuid2", ...errorUtil.errToObj(message) });
  }
  ulid(message) {
    return this._addCheck({ kind: "ulid", ...errorUtil.errToObj(message) });
  }
  base64(message) {
    return this._addCheck({ kind: "base64", ...errorUtil.errToObj(message) });
  }
  base64url(message) {
    return this._addCheck({
      kind: "base64url",
      ...errorUtil.errToObj(message)
    });
  }
  jwt(options) {
    return this._addCheck({ kind: "jwt", ...errorUtil.errToObj(options) });
  }
  ip(options) {
    return this._addCheck({ kind: "ip", ...errorUtil.errToObj(options) });
  }
  cidr(options) {
    return this._addCheck({ kind: "cidr", ...errorUtil.errToObj(options) });
  }
  datetime(options) {
    var _a, _b;
    if (typeof options === "string") {
      return this._addCheck({
        kind: "datetime",
        precision: null,
        offset: false,
        local: false,
        message: options
      });
    }
    return this._addCheck({
      kind: "datetime",
      precision: typeof (options === null || options === void 0 ? void 0 : options.precision) === "undefined" ? null : options === null || options === void 0 ? void 0 : options.precision,
      offset: (_a = options === null || options === void 0 ? void 0 : options.offset) !== null && _a !== void 0 ? _a : false,
      local: (_b = options === null || options === void 0 ? void 0 : options.local) !== null && _b !== void 0 ? _b : false,
      ...errorUtil.errToObj(options === null || options === void 0 ? void 0 : options.message)
    });
  }
  date(message) {
    return this._addCheck({ kind: "date", message });
  }
  time(options) {
    if (typeof options === "string") {
      return this._addCheck({
        kind: "time",
        precision: null,
        message: options
      });
    }
    return this._addCheck({
      kind: "time",
      precision: typeof (options === null || options === void 0 ? void 0 : options.precision) === "undefined" ? null : options === null || options === void 0 ? void 0 : options.precision,
      ...errorUtil.errToObj(options === null || options === void 0 ? void 0 : options.message)
    });
  }
  duration(message) {
    return this._addCheck({ kind: "duration", ...errorUtil.errToObj(message) });
  }
  regex(regex, message) {
    return this._addCheck({
      kind: "regex",
      regex,
      ...errorUtil.errToObj(message)
    });
  }
  includes(value, options) {
    return this._addCheck({
      kind: "includes",
      value,
      position: options === null || options === void 0 ? void 0 : options.position,
      ...errorUtil.errToObj(options === null || options === void 0 ? void 0 : options.message)
    });
  }
  startsWith(value, message) {
    return this._addCheck({
      kind: "startsWith",
      value,
      ...errorUtil.errToObj(message)
    });
  }
  endsWith(value, message) {
    return this._addCheck({
      kind: "endsWith",
      value,
      ...errorUtil.errToObj(message)
    });
  }
  min(minLength, message) {
    return this._addCheck({
      kind: "min",
      value: minLength,
      ...errorUtil.errToObj(message)
    });
  }
  max(maxLength, message) {
    return this._addCheck({
      kind: "max",
      value: maxLength,
      ...errorUtil.errToObj(message)
    });
  }
  length(len, message) {
    return this._addCheck({
      kind: "length",
      value: len,
      ...errorUtil.errToObj(message)
    });
  }
  /**
   * Equivalent to `.min(1)`
   */
  nonempty(message) {
    return this.min(1, errorUtil.errToObj(message));
  }
  trim() {
    return new ZodString({
      ...this._def,
      checks: [...this._def.checks, { kind: "trim" }]
    });
  }
  toLowerCase() {
    return new ZodString({
      ...this._def,
      checks: [...this._def.checks, { kind: "toLowerCase" }]
    });
  }
  toUpperCase() {
    return new ZodString({
      ...this._def,
      checks: [...this._def.checks, { kind: "toUpperCase" }]
    });
  }
  get isDatetime() {
    return !!this._def.checks.find((ch) => ch.kind === "datetime");
  }
  get isDate() {
    return !!this._def.checks.find((ch) => ch.kind === "date");
  }
  get isTime() {
    return !!this._def.checks.find((ch) => ch.kind === "time");
  }
  get isDuration() {
    return !!this._def.checks.find((ch) => ch.kind === "duration");
  }
  get isEmail() {
    return !!this._def.checks.find((ch) => ch.kind === "email");
  }
  get isURL() {
    return !!this._def.checks.find((ch) => ch.kind === "url");
  }
  get isEmoji() {
    return !!this._def.checks.find((ch) => ch.kind === "emoji");
  }
  get isUUID() {
    return !!this._def.checks.find((ch) => ch.kind === "uuid");
  }
  get isNANOID() {
    return !!this._def.checks.find((ch) => ch.kind === "nanoid");
  }
  get isCUID() {
    return !!this._def.checks.find((ch) => ch.kind === "cuid");
  }
  get isCUID2() {
    return !!this._def.checks.find((ch) => ch.kind === "cuid2");
  }
  get isULID() {
    return !!this._def.checks.find((ch) => ch.kind === "ulid");
  }
  get isIP() {
    return !!this._def.checks.find((ch) => ch.kind === "ip");
  }
  get isCIDR() {
    return !!this._def.checks.find((ch) => ch.kind === "cidr");
  }
  get isBase64() {
    return !!this._def.checks.find((ch) => ch.kind === "base64");
  }
  get isBase64url() {
    return !!this._def.checks.find((ch) => ch.kind === "base64url");
  }
  get minLength() {
    let min = null;
    for (const ch of this._def.checks) {
      if (ch.kind === "min") {
        if (min === null || ch.value > min)
          min = ch.value;
      }
    }
    return min;
  }
  get maxLength() {
    let max = null;
    for (const ch of this._def.checks) {
      if (ch.kind === "max") {
        if (max === null || ch.value < max)
          max = ch.value;
      }
    }
    return max;
  }
}
ZodString.create = (params) => {
  var _a;
  return new ZodString({
    checks: [],
    typeName: ZodFirstPartyTypeKind.ZodString,
    coerce: (_a = params === null || params === void 0 ? void 0 : params.coerce) !== null && _a !== void 0 ? _a : false,
    ...processCreateParams(params)
  });
};
function floatSafeRemainder(val, step) {
  const valDecCount = (val.toString().split(".")[1] || "").length;
  const stepDecCount = (step.toString().split(".")[1] || "").length;
  const decCount = valDecCount > stepDecCount ? valDecCount : stepDecCount;
  const valInt = parseInt(val.toFixed(decCount).replace(".", ""));
  const stepInt = parseInt(step.toFixed(decCount).replace(".", ""));
  return valInt % stepInt / Math.pow(10, decCount);
}
class ZodNumber extends ZodType {
  constructor() {
    super(...arguments);
    this.min = this.gte;
    this.max = this.lte;
    this.step = this.multipleOf;
  }
  _parse(input) {
    if (this._def.coerce) {
      input.data = Number(input.data);
    }
    const parsedType = this._getType(input);
    if (parsedType !== ZodParsedType.number) {
      const ctx2 = this._getOrReturnCtx(input);
      addIssueToContext(ctx2, {
        code: ZodIssueCode.invalid_type,
        expected: ZodParsedType.number,
        received: ctx2.parsedType
      });
      return INVALID;
    }
    let ctx = void 0;
    const status = new ParseStatus();
    for (const check of this._def.checks) {
      if (check.kind === "int") {
        if (!util.isInteger(input.data)) {
          ctx = this._getOrReturnCtx(input, ctx);
          addIssueToContext(ctx, {
            code: ZodIssueCode.invalid_type,
            expected: "integer",
            received: "float",
            message: check.message
          });
          status.dirty();
        }
      } else if (check.kind === "min") {
        const tooSmall = check.inclusive ? input.data < check.value : input.data <= check.value;
        if (tooSmall) {
          ctx = this._getOrReturnCtx(input, ctx);
          addIssueToContext(ctx, {
            code: ZodIssueCode.too_small,
            minimum: check.value,
            type: "number",
            inclusive: check.inclusive,
            exact: false,
            message: check.message
          });
          status.dirty();
        }
      } else if (check.kind === "max") {
        const tooBig = check.inclusive ? input.data > check.value : input.data >= check.value;
        if (tooBig) {
          ctx = this._getOrReturnCtx(input, ctx);
          addIssueToContext(ctx, {
            code: ZodIssueCode.too_big,
            maximum: check.value,
            type: "number",
            inclusive: check.inclusive,
            exact: false,
            message: check.message
          });
          status.dirty();
        }
      } else if (check.kind === "multipleOf") {
        if (floatSafeRemainder(input.data, check.value) !== 0) {
          ctx = this._getOrReturnCtx(input, ctx);
          addIssueToContext(ctx, {
            code: ZodIssueCode.not_multiple_of,
            multipleOf: check.value,
            message: check.message
          });
          status.dirty();
        }
      } else if (check.kind === "finite") {
        if (!Number.isFinite(input.data)) {
          ctx = this._getOrReturnCtx(input, ctx);
          addIssueToContext(ctx, {
            code: ZodIssueCode.not_finite,
            message: check.message
          });
          status.dirty();
        }
      } else {
        util.assertNever(check);
      }
    }
    return { status: status.value, value: input.data };
  }
  gte(value, message) {
    return this.setLimit("min", value, true, errorUtil.toString(message));
  }
  gt(value, message) {
    return this.setLimit("min", value, false, errorUtil.toString(message));
  }
  lte(value, message) {
    return this.setLimit("max", value, true, errorUtil.toString(message));
  }
  lt(value, message) {
    return this.setLimit("max", value, false, errorUtil.toString(message));
  }
  setLimit(kind, value, inclusive, message) {
    return new ZodNumber({
      ...this._def,
      checks: [
        ...this._def.checks,
        {
          kind,
          value,
          inclusive,
          message: errorUtil.toString(message)
        }
      ]
    });
  }
  _addCheck(check) {
    return new ZodNumber({
      ...this._def,
      checks: [...this._def.checks, check]
    });
  }
  int(message) {
    return this._addCheck({
      kind: "int",
      message: errorUtil.toString(message)
    });
  }
  positive(message) {
    return this._addCheck({
      kind: "min",
      value: 0,
      inclusive: false,
      message: errorUtil.toString(message)
    });
  }
  negative(message) {
    return this._addCheck({
      kind: "max",
      value: 0,
      inclusive: false,
      message: errorUtil.toString(message)
    });
  }
  nonpositive(message) {
    return this._addCheck({
      kind: "max",
      value: 0,
      inclusive: true,
      message: errorUtil.toString(message)
    });
  }
  nonnegative(message) {
    return this._addCheck({
      kind: "min",
      value: 0,
      inclusive: true,
      message: errorUtil.toString(message)
    });
  }
  multipleOf(value, message) {
    return this._addCheck({
      kind: "multipleOf",
      value,
      message: errorUtil.toString(message)
    });
  }
  finite(message) {
    return this._addCheck({
      kind: "finite",
      message: errorUtil.toString(message)
    });
  }
  safe(message) {
    return this._addCheck({
      kind: "min",
      inclusive: true,
      value: Number.MIN_SAFE_INTEGER,
      message: errorUtil.toString(message)
    })._addCheck({
      kind: "max",
      inclusive: true,
      value: Number.MAX_SAFE_INTEGER,
      message: errorUtil.toString(message)
    });
  }
  get minValue() {
    let min = null;
    for (const ch of this._def.checks) {
      if (ch.kind === "min") {
        if (min === null || ch.value > min)
          min = ch.value;
      }
    }
    return min;
  }
  get maxValue() {
    let max = null;
    for (const ch of this._def.checks) {
      if (ch.kind === "max") {
        if (max === null || ch.value < max)
          max = ch.value;
      }
    }
    return max;
  }
  get isInt() {
    return !!this._def.checks.find((ch) => ch.kind === "int" || ch.kind === "multipleOf" && util.isInteger(ch.value));
  }
  get isFinite() {
    let max = null, min = null;
    for (const ch of this._def.checks) {
      if (ch.kind === "finite" || ch.kind === "int" || ch.kind === "multipleOf") {
        return true;
      } else if (ch.kind === "min") {
        if (min === null || ch.value > min)
          min = ch.value;
      } else if (ch.kind === "max") {
        if (max === null || ch.value < max)
          max = ch.value;
      }
    }
    return Number.isFinite(min) && Number.isFinite(max);
  }
}
ZodNumber.create = (params) => {
  return new ZodNumber({
    checks: [],
    typeName: ZodFirstPartyTypeKind.ZodNumber,
    coerce: (params === null || params === void 0 ? void 0 : params.coerce) || false,
    ...processCreateParams(params)
  });
};
class ZodBigInt extends ZodType {
  constructor() {
    super(...arguments);
    this.min = this.gte;
    this.max = this.lte;
  }
  _parse(input) {
    if (this._def.coerce) {
      try {
        input.data = BigInt(input.data);
      } catch (_a) {
        return this._getInvalidInput(input);
      }
    }
    const parsedType = this._getType(input);
    if (parsedType !== ZodParsedType.bigint) {
      return this._getInvalidInput(input);
    }
    let ctx = void 0;
    const status = new ParseStatus();
    for (const check of this._def.checks) {
      if (check.kind === "min") {
        const tooSmall = check.inclusive ? input.data < check.value : input.data <= check.value;
        if (tooSmall) {
          ctx = this._getOrReturnCtx(input, ctx);
          addIssueToContext(ctx, {
            code: ZodIssueCode.too_small,
            type: "bigint",
            minimum: check.value,
            inclusive: check.inclusive,
            message: check.message
          });
          status.dirty();
        }
      } else if (check.kind === "max") {
        const tooBig = check.inclusive ? input.data > check.value : input.data >= check.value;
        if (tooBig) {
          ctx = this._getOrReturnCtx(input, ctx);
          addIssueToContext(ctx, {
            code: ZodIssueCode.too_big,
            type: "bigint",
            maximum: check.value,
            inclusive: check.inclusive,
            message: check.message
          });
          status.dirty();
        }
      } else if (check.kind === "multipleOf") {
        if (input.data % check.value !== BigInt(0)) {
          ctx = this._getOrReturnCtx(input, ctx);
          addIssueToContext(ctx, {
            code: ZodIssueCode.not_multiple_of,
            multipleOf: check.value,
            message: check.message
          });
          status.dirty();
        }
      } else {
        util.assertNever(check);
      }
    }
    return { status: status.value, value: input.data };
  }
  _getInvalidInput(input) {
    const ctx = this._getOrReturnCtx(input);
    addIssueToContext(ctx, {
      code: ZodIssueCode.invalid_type,
      expected: ZodParsedType.bigint,
      received: ctx.parsedType
    });
    return INVALID;
  }
  gte(value, message) {
    return this.setLimit("min", value, true, errorUtil.toString(message));
  }
  gt(value, message) {
    return this.setLimit("min", value, false, errorUtil.toString(message));
  }
  lte(value, message) {
    return this.setLimit("max", value, true, errorUtil.toString(message));
  }
  lt(value, message) {
    return this.setLimit("max", value, false, errorUtil.toString(message));
  }
  setLimit(kind, value, inclusive, message) {
    return new ZodBigInt({
      ...this._def,
      checks: [
        ...this._def.checks,
        {
          kind,
          value,
          inclusive,
          message: errorUtil.toString(message)
        }
      ]
    });
  }
  _addCheck(check) {
    return new ZodBigInt({
      ...this._def,
      checks: [...this._def.checks, check]
    });
  }
  positive(message) {
    return this._addCheck({
      kind: "min",
      value: BigInt(0),
      inclusive: false,
      message: errorUtil.toString(message)
    });
  }
  negative(message) {
    return this._addCheck({
      kind: "max",
      value: BigInt(0),
      inclusive: false,
      message: errorUtil.toString(message)
    });
  }
  nonpositive(message) {
    return this._addCheck({
      kind: "max",
      value: BigInt(0),
      inclusive: true,
      message: errorUtil.toString(message)
    });
  }
  nonnegative(message) {
    return this._addCheck({
      kind: "min",
      value: BigInt(0),
      inclusive: true,
      message: errorUtil.toString(message)
    });
  }
  multipleOf(value, message) {
    return this._addCheck({
      kind: "multipleOf",
      value,
      message: errorUtil.toString(message)
    });
  }
  get minValue() {
    let min = null;
    for (const ch of this._def.checks) {
      if (ch.kind === "min") {
        if (min === null || ch.value > min)
          min = ch.value;
      }
    }
    return min;
  }
  get maxValue() {
    let max = null;
    for (const ch of this._def.checks) {
      if (ch.kind === "max") {
        if (max === null || ch.value < max)
          max = ch.value;
      }
    }
    return max;
  }
}
ZodBigInt.create = (params) => {
  var _a;
  return new ZodBigInt({
    checks: [],
    typeName: ZodFirstPartyTypeKind.ZodBigInt,
    coerce: (_a = params === null || params === void 0 ? void 0 : params.coerce) !== null && _a !== void 0 ? _a : false,
    ...processCreateParams(params)
  });
};
class ZodBoolean extends ZodType {
  _parse(input) {
    if (this._def.coerce) {
      input.data = Boolean(input.data);
    }
    const parsedType = this._getType(input);
    if (parsedType !== ZodParsedType.boolean) {
      const ctx = this._getOrReturnCtx(input);
      addIssueToContext(ctx, {
        code: ZodIssueCode.invalid_type,
        expected: ZodParsedType.boolean,
        received: ctx.parsedType
      });
      return INVALID;
    }
    return OK(input.data);
  }
}
ZodBoolean.create = (params) => {
  return new ZodBoolean({
    typeName: ZodFirstPartyTypeKind.ZodBoolean,
    coerce: (params === null || params === void 0 ? void 0 : params.coerce) || false,
    ...processCreateParams(params)
  });
};
class ZodDate extends ZodType {
  _parse(input) {
    if (this._def.coerce) {
      input.data = new Date(input.data);
    }
    const parsedType = this._getType(input);
    if (parsedType !== ZodParsedType.date) {
      const ctx2 = this._getOrReturnCtx(input);
      addIssueToContext(ctx2, {
        code: ZodIssueCode.invalid_type,
        expected: ZodParsedType.date,
        received: ctx2.parsedType
      });
      return INVALID;
    }
    if (isNaN(input.data.getTime())) {
      const ctx2 = this._getOrReturnCtx(input);
      addIssueToContext(ctx2, {
        code: ZodIssueCode.invalid_date
      });
      return INVALID;
    }
    const status = new ParseStatus();
    let ctx = void 0;
    for (const check of this._def.checks) {
      if (check.kind === "min") {
        if (input.data.getTime() < check.value) {
          ctx = this._getOrReturnCtx(input, ctx);
          addIssueToContext(ctx, {
            code: ZodIssueCode.too_small,
            message: check.message,
            inclusive: true,
            exact: false,
            minimum: check.value,
            type: "date"
          });
          status.dirty();
        }
      } else if (check.kind === "max") {
        if (input.data.getTime() > check.value) {
          ctx = this._getOrReturnCtx(input, ctx);
          addIssueToContext(ctx, {
            code: ZodIssueCode.too_big,
            message: check.message,
            inclusive: true,
            exact: false,
            maximum: check.value,
            type: "date"
          });
          status.dirty();
        }
      } else {
        util.assertNever(check);
      }
    }
    return {
      status: status.value,
      value: new Date(input.data.getTime())
    };
  }
  _addCheck(check) {
    return new ZodDate({
      ...this._def,
      checks: [...this._def.checks, check]
    });
  }
  min(minDate, message) {
    return this._addCheck({
      kind: "min",
      value: minDate.getTime(),
      message: errorUtil.toString(message)
    });
  }
  max(maxDate, message) {
    return this._addCheck({
      kind: "max",
      value: maxDate.getTime(),
      message: errorUtil.toString(message)
    });
  }
  get minDate() {
    let min = null;
    for (const ch of this._def.checks) {
      if (ch.kind === "min") {
        if (min === null || ch.value > min)
          min = ch.value;
      }
    }
    return min != null ? new Date(min) : null;
  }
  get maxDate() {
    let max = null;
    for (const ch of this._def.checks) {
      if (ch.kind === "max") {
        if (max === null || ch.value < max)
          max = ch.value;
      }
    }
    return max != null ? new Date(max) : null;
  }
}
ZodDate.create = (params) => {
  return new ZodDate({
    checks: [],
    coerce: (params === null || params === void 0 ? void 0 : params.coerce) || false,
    typeName: ZodFirstPartyTypeKind.ZodDate,
    ...processCreateParams(params)
  });
};
class ZodSymbol extends ZodType {
  _parse(input) {
    const parsedType = this._getType(input);
    if (parsedType !== ZodParsedType.symbol) {
      const ctx = this._getOrReturnCtx(input);
      addIssueToContext(ctx, {
        code: ZodIssueCode.invalid_type,
        expected: ZodParsedType.symbol,
        received: ctx.parsedType
      });
      return INVALID;
    }
    return OK(input.data);
  }
}
ZodSymbol.create = (params) => {
  return new ZodSymbol({
    typeName: ZodFirstPartyTypeKind.ZodSymbol,
    ...processCreateParams(params)
  });
};
class ZodUndefined extends ZodType {
  _parse(input) {
    const parsedType = this._getType(input);
    if (parsedType !== ZodParsedType.undefined) {
      const ctx = this._getOrReturnCtx(input);
      addIssueToContext(ctx, {
        code: ZodIssueCode.invalid_type,
        expected: ZodParsedType.undefined,
        received: ctx.parsedType
      });
      return INVALID;
    }
    return OK(input.data);
  }
}
ZodUndefined.create = (params) => {
  return new ZodUndefined({
    typeName: ZodFirstPartyTypeKind.ZodUndefined,
    ...processCreateParams(params)
  });
};
class ZodNull extends ZodType {
  _parse(input) {
    const parsedType = this._getType(input);
    if (parsedType !== ZodParsedType.null) {
      const ctx = this._getOrReturnCtx(input);
      addIssueToContext(ctx, {
        code: ZodIssueCode.invalid_type,
        expected: ZodParsedType.null,
        received: ctx.parsedType
      });
      return INVALID;
    }
    return OK(input.data);
  }
}
ZodNull.create = (params) => {
  return new ZodNull({
    typeName: ZodFirstPartyTypeKind.ZodNull,
    ...processCreateParams(params)
  });
};
class ZodAny extends ZodType {
  constructor() {
    super(...arguments);
    this._any = true;
  }
  _parse(input) {
    return OK(input.data);
  }
}
ZodAny.create = (params) => {
  return new ZodAny({
    typeName: ZodFirstPartyTypeKind.ZodAny,
    ...processCreateParams(params)
  });
};
class ZodUnknown extends ZodType {
  constructor() {
    super(...arguments);
    this._unknown = true;
  }
  _parse(input) {
    return OK(input.data);
  }
}
ZodUnknown.create = (params) => {
  return new ZodUnknown({
    typeName: ZodFirstPartyTypeKind.ZodUnknown,
    ...processCreateParams(params)
  });
};
class ZodNever extends ZodType {
  _parse(input) {
    const ctx = this._getOrReturnCtx(input);
    addIssueToContext(ctx, {
      code: ZodIssueCode.invalid_type,
      expected: ZodParsedType.never,
      received: ctx.parsedType
    });
    return INVALID;
  }
}
ZodNever.create = (params) => {
  return new ZodNever({
    typeName: ZodFirstPartyTypeKind.ZodNever,
    ...processCreateParams(params)
  });
};
class ZodVoid extends ZodType {
  _parse(input) {
    const parsedType = this._getType(input);
    if (parsedType !== ZodParsedType.undefined) {
      const ctx = this._getOrReturnCtx(input);
      addIssueToContext(ctx, {
        code: ZodIssueCode.invalid_type,
        expected: ZodParsedType.void,
        received: ctx.parsedType
      });
      return INVALID;
    }
    return OK(input.data);
  }
}
ZodVoid.create = (params) => {
  return new ZodVoid({
    typeName: ZodFirstPartyTypeKind.ZodVoid,
    ...processCreateParams(params)
  });
};
class ZodArray extends ZodType {
  _parse(input) {
    const { ctx, status } = this._processInputParams(input);
    const def = this._def;
    if (ctx.parsedType !== ZodParsedType.array) {
      addIssueToContext(ctx, {
        code: ZodIssueCode.invalid_type,
        expected: ZodParsedType.array,
        received: ctx.parsedType
      });
      return INVALID;
    }
    if (def.exactLength !== null) {
      const tooBig = ctx.data.length > def.exactLength.value;
      const tooSmall = ctx.data.length < def.exactLength.value;
      if (tooBig || tooSmall) {
        addIssueToContext(ctx, {
          code: tooBig ? ZodIssueCode.too_big : ZodIssueCode.too_small,
          minimum: tooSmall ? def.exactLength.value : void 0,
          maximum: tooBig ? def.exactLength.value : void 0,
          type: "array",
          inclusive: true,
          exact: true,
          message: def.exactLength.message
        });
        status.dirty();
      }
    }
    if (def.minLength !== null) {
      if (ctx.data.length < def.minLength.value) {
        addIssueToContext(ctx, {
          code: ZodIssueCode.too_small,
          minimum: def.minLength.value,
          type: "array",
          inclusive: true,
          exact: false,
          message: def.minLength.message
        });
        status.dirty();
      }
    }
    if (def.maxLength !== null) {
      if (ctx.data.length > def.maxLength.value) {
        addIssueToContext(ctx, {
          code: ZodIssueCode.too_big,
          maximum: def.maxLength.value,
          type: "array",
          inclusive: true,
          exact: false,
          message: def.maxLength.message
        });
        status.dirty();
      }
    }
    if (ctx.common.async) {
      return Promise.all([...ctx.data].map((item, i) => {
        return def.type._parseAsync(new ParseInputLazyPath(ctx, item, ctx.path, i));
      })).then((result2) => {
        return ParseStatus.mergeArray(status, result2);
      });
    }
    const result = [...ctx.data].map((item, i) => {
      return def.type._parseSync(new ParseInputLazyPath(ctx, item, ctx.path, i));
    });
    return ParseStatus.mergeArray(status, result);
  }
  get element() {
    return this._def.type;
  }
  min(minLength, message) {
    return new ZodArray({
      ...this._def,
      minLength: { value: minLength, message: errorUtil.toString(message) }
    });
  }
  max(maxLength, message) {
    return new ZodArray({
      ...this._def,
      maxLength: { value: maxLength, message: errorUtil.toString(message) }
    });
  }
  length(len, message) {
    return new ZodArray({
      ...this._def,
      exactLength: { value: len, message: errorUtil.toString(message) }
    });
  }
  nonempty(message) {
    return this.min(1, message);
  }
}
ZodArray.create = (schema, params) => {
  return new ZodArray({
    type: schema,
    minLength: null,
    maxLength: null,
    exactLength: null,
    typeName: ZodFirstPartyTypeKind.ZodArray,
    ...processCreateParams(params)
  });
};
function deepPartialify(schema) {
  if (schema instanceof ZodObject) {
    const newShape = {};
    for (const key in schema.shape) {
      const fieldSchema = schema.shape[key];
      newShape[key] = ZodOptional.create(deepPartialify(fieldSchema));
    }
    return new ZodObject({
      ...schema._def,
      shape: () => newShape
    });
  } else if (schema instanceof ZodArray) {
    return new ZodArray({
      ...schema._def,
      type: deepPartialify(schema.element)
    });
  } else if (schema instanceof ZodOptional) {
    return ZodOptional.create(deepPartialify(schema.unwrap()));
  } else if (schema instanceof ZodNullable) {
    return ZodNullable.create(deepPartialify(schema.unwrap()));
  } else if (schema instanceof ZodTuple) {
    return ZodTuple.create(schema.items.map((item) => deepPartialify(item)));
  } else {
    return schema;
  }
}
class ZodObject extends ZodType {
  constructor() {
    super(...arguments);
    this._cached = null;
    this.nonstrict = this.passthrough;
    this.augment = this.extend;
  }
  _getCached() {
    if (this._cached !== null)
      return this._cached;
    const shape = this._def.shape();
    const keys = util.objectKeys(shape);
    return this._cached = { shape, keys };
  }
  _parse(input) {
    const parsedType = this._getType(input);
    if (parsedType !== ZodParsedType.object) {
      const ctx2 = this._getOrReturnCtx(input);
      addIssueToContext(ctx2, {
        code: ZodIssueCode.invalid_type,
        expected: ZodParsedType.object,
        received: ctx2.parsedType
      });
      return INVALID;
    }
    const { status, ctx } = this._processInputParams(input);
    const { shape, keys: shapeKeys } = this._getCached();
    const extraKeys = [];
    if (!(this._def.catchall instanceof ZodNever && this._def.unknownKeys === "strip")) {
      for (const key in ctx.data) {
        if (!shapeKeys.includes(key)) {
          extraKeys.push(key);
        }
      }
    }
    const pairs = [];
    for (const key of shapeKeys) {
      const keyValidator = shape[key];
      const value = ctx.data[key];
      pairs.push({
        key: { status: "valid", value: key },
        value: keyValidator._parse(new ParseInputLazyPath(ctx, value, ctx.path, key)),
        alwaysSet: key in ctx.data
      });
    }
    if (this._def.catchall instanceof ZodNever) {
      const unknownKeys = this._def.unknownKeys;
      if (unknownKeys === "passthrough") {
        for (const key of extraKeys) {
          pairs.push({
            key: { status: "valid", value: key },
            value: { status: "valid", value: ctx.data[key] }
          });
        }
      } else if (unknownKeys === "strict") {
        if (extraKeys.length > 0) {
          addIssueToContext(ctx, {
            code: ZodIssueCode.unrecognized_keys,
            keys: extraKeys
          });
          status.dirty();
        }
      } else if (unknownKeys === "strip") ;
      else {
        throw new Error(`Internal ZodObject error: invalid unknownKeys value.`);
      }
    } else {
      const catchall = this._def.catchall;
      for (const key of extraKeys) {
        const value = ctx.data[key];
        pairs.push({
          key: { status: "valid", value: key },
          value: catchall._parse(
            new ParseInputLazyPath(ctx, value, ctx.path, key)
            //, ctx.child(key), value, getParsedType(value)
          ),
          alwaysSet: key in ctx.data
        });
      }
    }
    if (ctx.common.async) {
      return Promise.resolve().then(async () => {
        const syncPairs = [];
        for (const pair of pairs) {
          const key = await pair.key;
          const value = await pair.value;
          syncPairs.push({
            key,
            value,
            alwaysSet: pair.alwaysSet
          });
        }
        return syncPairs;
      }).then((syncPairs) => {
        return ParseStatus.mergeObjectSync(status, syncPairs);
      });
    } else {
      return ParseStatus.mergeObjectSync(status, pairs);
    }
  }
  get shape() {
    return this._def.shape();
  }
  strict(message) {
    errorUtil.errToObj;
    return new ZodObject({
      ...this._def,
      unknownKeys: "strict",
      ...message !== void 0 ? {
        errorMap: (issue, ctx) => {
          var _a, _b, _c, _d;
          const defaultError = (_c = (_b = (_a = this._def).errorMap) === null || _b === void 0 ? void 0 : _b.call(_a, issue, ctx).message) !== null && _c !== void 0 ? _c : ctx.defaultError;
          if (issue.code === "unrecognized_keys")
            return {
              message: (_d = errorUtil.errToObj(message).message) !== null && _d !== void 0 ? _d : defaultError
            };
          return {
            message: defaultError
          };
        }
      } : {}
    });
  }
  strip() {
    return new ZodObject({
      ...this._def,
      unknownKeys: "strip"
    });
  }
  passthrough() {
    return new ZodObject({
      ...this._def,
      unknownKeys: "passthrough"
    });
  }
  // const AugmentFactory =
  //   <Def extends ZodObjectDef>(def: Def) =>
  //   <Augmentation extends ZodRawShape>(
  //     augmentation: Augmentation
  //   ): ZodObject<
  //     extendShape<ReturnType<Def["shape"]>, Augmentation>,
  //     Def["unknownKeys"],
  //     Def["catchall"]
  //   > => {
  //     return new ZodObject({
  //       ...def,
  //       shape: () => ({
  //         ...def.shape(),
  //         ...augmentation,
  //       }),
  //     }) as any;
  //   };
  extend(augmentation) {
    return new ZodObject({
      ...this._def,
      shape: () => ({
        ...this._def.shape(),
        ...augmentation
      })
    });
  }
  /**
   * Prior to zod@1.0.12 there was a bug in the
   * inferred type of merged objects. Please
   * upgrade if you are experiencing issues.
   */
  merge(merging) {
    const merged = new ZodObject({
      unknownKeys: merging._def.unknownKeys,
      catchall: merging._def.catchall,
      shape: () => ({
        ...this._def.shape(),
        ...merging._def.shape()
      }),
      typeName: ZodFirstPartyTypeKind.ZodObject
    });
    return merged;
  }
  // merge<
  //   Incoming extends AnyZodObject,
  //   Augmentation extends Incoming["shape"],
  //   NewOutput extends {
  //     [k in keyof Augmentation | keyof Output]: k extends keyof Augmentation
  //       ? Augmentation[k]["_output"]
  //       : k extends keyof Output
  //       ? Output[k]
  //       : never;
  //   },
  //   NewInput extends {
  //     [k in keyof Augmentation | keyof Input]: k extends keyof Augmentation
  //       ? Augmentation[k]["_input"]
  //       : k extends keyof Input
  //       ? Input[k]
  //       : never;
  //   }
  // >(
  //   merging: Incoming
  // ): ZodObject<
  //   extendShape<T, ReturnType<Incoming["_def"]["shape"]>>,
  //   Incoming["_def"]["unknownKeys"],
  //   Incoming["_def"]["catchall"],
  //   NewOutput,
  //   NewInput
  // > {
  //   const merged: any = new ZodObject({
  //     unknownKeys: merging._def.unknownKeys,
  //     catchall: merging._def.catchall,
  //     shape: () =>
  //       objectUtil.mergeShapes(this._def.shape(), merging._def.shape()),
  //     typeName: ZodFirstPartyTypeKind.ZodObject,
  //   }) as any;
  //   return merged;
  // }
  setKey(key, schema) {
    return this.augment({ [key]: schema });
  }
  // merge<Incoming extends AnyZodObject>(
  //   merging: Incoming
  // ): //ZodObject<T & Incoming["_shape"], UnknownKeys, Catchall> = (merging) => {
  // ZodObject<
  //   extendShape<T, ReturnType<Incoming["_def"]["shape"]>>,
  //   Incoming["_def"]["unknownKeys"],
  //   Incoming["_def"]["catchall"]
  // > {
  //   // const mergedShape = objectUtil.mergeShapes(
  //   //   this._def.shape(),
  //   //   merging._def.shape()
  //   // );
  //   const merged: any = new ZodObject({
  //     unknownKeys: merging._def.unknownKeys,
  //     catchall: merging._def.catchall,
  //     shape: () =>
  //       objectUtil.mergeShapes(this._def.shape(), merging._def.shape()),
  //     typeName: ZodFirstPartyTypeKind.ZodObject,
  //   }) as any;
  //   return merged;
  // }
  catchall(index) {
    return new ZodObject({
      ...this._def,
      catchall: index
    });
  }
  pick(mask) {
    const shape = {};
    util.objectKeys(mask).forEach((key) => {
      if (mask[key] && this.shape[key]) {
        shape[key] = this.shape[key];
      }
    });
    return new ZodObject({
      ...this._def,
      shape: () => shape
    });
  }
  omit(mask) {
    const shape = {};
    util.objectKeys(this.shape).forEach((key) => {
      if (!mask[key]) {
        shape[key] = this.shape[key];
      }
    });
    return new ZodObject({
      ...this._def,
      shape: () => shape
    });
  }
  /**
   * @deprecated
   */
  deepPartial() {
    return deepPartialify(this);
  }
  partial(mask) {
    const newShape = {};
    util.objectKeys(this.shape).forEach((key) => {
      const fieldSchema = this.shape[key];
      if (mask && !mask[key]) {
        newShape[key] = fieldSchema;
      } else {
        newShape[key] = fieldSchema.optional();
      }
    });
    return new ZodObject({
      ...this._def,
      shape: () => newShape
    });
  }
  required(mask) {
    const newShape = {};
    util.objectKeys(this.shape).forEach((key) => {
      if (mask && !mask[key]) {
        newShape[key] = this.shape[key];
      } else {
        const fieldSchema = this.shape[key];
        let newField = fieldSchema;
        while (newField instanceof ZodOptional) {
          newField = newField._def.innerType;
        }
        newShape[key] = newField;
      }
    });
    return new ZodObject({
      ...this._def,
      shape: () => newShape
    });
  }
  keyof() {
    return createZodEnum(util.objectKeys(this.shape));
  }
}
ZodObject.create = (shape, params) => {
  return new ZodObject({
    shape: () => shape,
    unknownKeys: "strip",
    catchall: ZodNever.create(),
    typeName: ZodFirstPartyTypeKind.ZodObject,
    ...processCreateParams(params)
  });
};
ZodObject.strictCreate = (shape, params) => {
  return new ZodObject({
    shape: () => shape,
    unknownKeys: "strict",
    catchall: ZodNever.create(),
    typeName: ZodFirstPartyTypeKind.ZodObject,
    ...processCreateParams(params)
  });
};
ZodObject.lazycreate = (shape, params) => {
  return new ZodObject({
    shape,
    unknownKeys: "strip",
    catchall: ZodNever.create(),
    typeName: ZodFirstPartyTypeKind.ZodObject,
    ...processCreateParams(params)
  });
};
class ZodUnion extends ZodType {
  _parse(input) {
    const { ctx } = this._processInputParams(input);
    const options = this._def.options;
    function handleResults(results) {
      for (const result of results) {
        if (result.result.status === "valid") {
          return result.result;
        }
      }
      for (const result of results) {
        if (result.result.status === "dirty") {
          ctx.common.issues.push(...result.ctx.common.issues);
          return result.result;
        }
      }
      const unionErrors = results.map((result) => new ZodError(result.ctx.common.issues));
      addIssueToContext(ctx, {
        code: ZodIssueCode.invalid_union,
        unionErrors
      });
      return INVALID;
    }
    if (ctx.common.async) {
      return Promise.all(options.map(async (option) => {
        const childCtx = {
          ...ctx,
          common: {
            ...ctx.common,
            issues: []
          },
          parent: null
        };
        return {
          result: await option._parseAsync({
            data: ctx.data,
            path: ctx.path,
            parent: childCtx
          }),
          ctx: childCtx
        };
      })).then(handleResults);
    } else {
      let dirty = void 0;
      const issues = [];
      for (const option of options) {
        const childCtx = {
          ...ctx,
          common: {
            ...ctx.common,
            issues: []
          },
          parent: null
        };
        const result = option._parseSync({
          data: ctx.data,
          path: ctx.path,
          parent: childCtx
        });
        if (result.status === "valid") {
          return result;
        } else if (result.status === "dirty" && !dirty) {
          dirty = { result, ctx: childCtx };
        }
        if (childCtx.common.issues.length) {
          issues.push(childCtx.common.issues);
        }
      }
      if (dirty) {
        ctx.common.issues.push(...dirty.ctx.common.issues);
        return dirty.result;
      }
      const unionErrors = issues.map((issues2) => new ZodError(issues2));
      addIssueToContext(ctx, {
        code: ZodIssueCode.invalid_union,
        unionErrors
      });
      return INVALID;
    }
  }
  get options() {
    return this._def.options;
  }
}
ZodUnion.create = (types, params) => {
  return new ZodUnion({
    options: types,
    typeName: ZodFirstPartyTypeKind.ZodUnion,
    ...processCreateParams(params)
  });
};
const getDiscriminator = (type) => {
  if (type instanceof ZodLazy) {
    return getDiscriminator(type.schema);
  } else if (type instanceof ZodEffects) {
    return getDiscriminator(type.innerType());
  } else if (type instanceof ZodLiteral) {
    return [type.value];
  } else if (type instanceof ZodEnum) {
    return type.options;
  } else if (type instanceof ZodNativeEnum) {
    return util.objectValues(type.enum);
  } else if (type instanceof ZodDefault) {
    return getDiscriminator(type._def.innerType);
  } else if (type instanceof ZodUndefined) {
    return [void 0];
  } else if (type instanceof ZodNull) {
    return [null];
  } else if (type instanceof ZodOptional) {
    return [void 0, ...getDiscriminator(type.unwrap())];
  } else if (type instanceof ZodNullable) {
    return [null, ...getDiscriminator(type.unwrap())];
  } else if (type instanceof ZodBranded) {
    return getDiscriminator(type.unwrap());
  } else if (type instanceof ZodReadonly) {
    return getDiscriminator(type.unwrap());
  } else if (type instanceof ZodCatch) {
    return getDiscriminator(type._def.innerType);
  } else {
    return [];
  }
};
class ZodDiscriminatedUnion extends ZodType {
  _parse(input) {
    const { ctx } = this._processInputParams(input);
    if (ctx.parsedType !== ZodParsedType.object) {
      addIssueToContext(ctx, {
        code: ZodIssueCode.invalid_type,
        expected: ZodParsedType.object,
        received: ctx.parsedType
      });
      return INVALID;
    }
    const discriminator = this.discriminator;
    const discriminatorValue = ctx.data[discriminator];
    const option = this.optionsMap.get(discriminatorValue);
    if (!option) {
      addIssueToContext(ctx, {
        code: ZodIssueCode.invalid_union_discriminator,
        options: Array.from(this.optionsMap.keys()),
        path: [discriminator]
      });
      return INVALID;
    }
    if (ctx.common.async) {
      return option._parseAsync({
        data: ctx.data,
        path: ctx.path,
        parent: ctx
      });
    } else {
      return option._parseSync({
        data: ctx.data,
        path: ctx.path,
        parent: ctx
      });
    }
  }
  get discriminator() {
    return this._def.discriminator;
  }
  get options() {
    return this._def.options;
  }
  get optionsMap() {
    return this._def.optionsMap;
  }
  /**
   * The constructor of the discriminated union schema. Its behaviour is very similar to that of the normal z.union() constructor.
   * However, it only allows a union of objects, all of which need to share a discriminator property. This property must
   * have a different value for each object in the union.
   * @param discriminator the name of the discriminator property
   * @param types an array of object schemas
   * @param params
   */
  static create(discriminator, options, params) {
    const optionsMap = /* @__PURE__ */ new Map();
    for (const type of options) {
      const discriminatorValues = getDiscriminator(type.shape[discriminator]);
      if (!discriminatorValues.length) {
        throw new Error(`A discriminator value for key \`${discriminator}\` could not be extracted from all schema options`);
      }
      for (const value of discriminatorValues) {
        if (optionsMap.has(value)) {
          throw new Error(`Discriminator property ${String(discriminator)} has duplicate value ${String(value)}`);
        }
        optionsMap.set(value, type);
      }
    }
    return new ZodDiscriminatedUnion({
      typeName: ZodFirstPartyTypeKind.ZodDiscriminatedUnion,
      discriminator,
      options,
      optionsMap,
      ...processCreateParams(params)
    });
  }
}
function mergeValues(a, b) {
  const aType = getParsedType(a);
  const bType = getParsedType(b);
  if (a === b) {
    return { valid: true, data: a };
  } else if (aType === ZodParsedType.object && bType === ZodParsedType.object) {
    const bKeys = util.objectKeys(b);
    const sharedKeys = util.objectKeys(a).filter((key) => bKeys.indexOf(key) !== -1);
    const newObj = { ...a, ...b };
    for (const key of sharedKeys) {
      const sharedValue = mergeValues(a[key], b[key]);
      if (!sharedValue.valid) {
        return { valid: false };
      }
      newObj[key] = sharedValue.data;
    }
    return { valid: true, data: newObj };
  } else if (aType === ZodParsedType.array && bType === ZodParsedType.array) {
    if (a.length !== b.length) {
      return { valid: false };
    }
    const newArray = [];
    for (let index = 0; index < a.length; index++) {
      const itemA = a[index];
      const itemB = b[index];
      const sharedValue = mergeValues(itemA, itemB);
      if (!sharedValue.valid) {
        return { valid: false };
      }
      newArray.push(sharedValue.data);
    }
    return { valid: true, data: newArray };
  } else if (aType === ZodParsedType.date && bType === ZodParsedType.date && +a === +b) {
    return { valid: true, data: a };
  } else {
    return { valid: false };
  }
}
class ZodIntersection extends ZodType {
  _parse(input) {
    const { status, ctx } = this._processInputParams(input);
    const handleParsed = (parsedLeft, parsedRight) => {
      if (isAborted(parsedLeft) || isAborted(parsedRight)) {
        return INVALID;
      }
      const merged = mergeValues(parsedLeft.value, parsedRight.value);
      if (!merged.valid) {
        addIssueToContext(ctx, {
          code: ZodIssueCode.invalid_intersection_types
        });
        return INVALID;
      }
      if (isDirty(parsedLeft) || isDirty(parsedRight)) {
        status.dirty();
      }
      return { status: status.value, value: merged.data };
    };
    if (ctx.common.async) {
      return Promise.all([
        this._def.left._parseAsync({
          data: ctx.data,
          path: ctx.path,
          parent: ctx
        }),
        this._def.right._parseAsync({
          data: ctx.data,
          path: ctx.path,
          parent: ctx
        })
      ]).then(([left, right]) => handleParsed(left, right));
    } else {
      return handleParsed(this._def.left._parseSync({
        data: ctx.data,
        path: ctx.path,
        parent: ctx
      }), this._def.right._parseSync({
        data: ctx.data,
        path: ctx.path,
        parent: ctx
      }));
    }
  }
}
ZodIntersection.create = (left, right, params) => {
  return new ZodIntersection({
    left,
    right,
    typeName: ZodFirstPartyTypeKind.ZodIntersection,
    ...processCreateParams(params)
  });
};
class ZodTuple extends ZodType {
  _parse(input) {
    const { status, ctx } = this._processInputParams(input);
    if (ctx.parsedType !== ZodParsedType.array) {
      addIssueToContext(ctx, {
        code: ZodIssueCode.invalid_type,
        expected: ZodParsedType.array,
        received: ctx.parsedType
      });
      return INVALID;
    }
    if (ctx.data.length < this._def.items.length) {
      addIssueToContext(ctx, {
        code: ZodIssueCode.too_small,
        minimum: this._def.items.length,
        inclusive: true,
        exact: false,
        type: "array"
      });
      return INVALID;
    }
    const rest = this._def.rest;
    if (!rest && ctx.data.length > this._def.items.length) {
      addIssueToContext(ctx, {
        code: ZodIssueCode.too_big,
        maximum: this._def.items.length,
        inclusive: true,
        exact: false,
        type: "array"
      });
      status.dirty();
    }
    const items = [...ctx.data].map((item, itemIndex) => {
      const schema = this._def.items[itemIndex] || this._def.rest;
      if (!schema)
        return null;
      return schema._parse(new ParseInputLazyPath(ctx, item, ctx.path, itemIndex));
    }).filter((x) => !!x);
    if (ctx.common.async) {
      return Promise.all(items).then((results) => {
        return ParseStatus.mergeArray(status, results);
      });
    } else {
      return ParseStatus.mergeArray(status, items);
    }
  }
  get items() {
    return this._def.items;
  }
  rest(rest) {
    return new ZodTuple({
      ...this._def,
      rest
    });
  }
}
ZodTuple.create = (schemas, params) => {
  if (!Array.isArray(schemas)) {
    throw new Error("You must pass an array of schemas to z.tuple([ ... ])");
  }
  return new ZodTuple({
    items: schemas,
    typeName: ZodFirstPartyTypeKind.ZodTuple,
    rest: null,
    ...processCreateParams(params)
  });
};
class ZodRecord extends ZodType {
  get keySchema() {
    return this._def.keyType;
  }
  get valueSchema() {
    return this._def.valueType;
  }
  _parse(input) {
    const { status, ctx } = this._processInputParams(input);
    if (ctx.parsedType !== ZodParsedType.object) {
      addIssueToContext(ctx, {
        code: ZodIssueCode.invalid_type,
        expected: ZodParsedType.object,
        received: ctx.parsedType
      });
      return INVALID;
    }
    const pairs = [];
    const keyType = this._def.keyType;
    const valueType = this._def.valueType;
    for (const key in ctx.data) {
      pairs.push({
        key: keyType._parse(new ParseInputLazyPath(ctx, key, ctx.path, key)),
        value: valueType._parse(new ParseInputLazyPath(ctx, ctx.data[key], ctx.path, key)),
        alwaysSet: key in ctx.data
      });
    }
    if (ctx.common.async) {
      return ParseStatus.mergeObjectAsync(status, pairs);
    } else {
      return ParseStatus.mergeObjectSync(status, pairs);
    }
  }
  get element() {
    return this._def.valueType;
  }
  static create(first, second, third) {
    if (second instanceof ZodType) {
      return new ZodRecord({
        keyType: first,
        valueType: second,
        typeName: ZodFirstPartyTypeKind.ZodRecord,
        ...processCreateParams(third)
      });
    }
    return new ZodRecord({
      keyType: ZodString.create(),
      valueType: first,
      typeName: ZodFirstPartyTypeKind.ZodRecord,
      ...processCreateParams(second)
    });
  }
}
class ZodMap extends ZodType {
  get keySchema() {
    return this._def.keyType;
  }
  get valueSchema() {
    return this._def.valueType;
  }
  _parse(input) {
    const { status, ctx } = this._processInputParams(input);
    if (ctx.parsedType !== ZodParsedType.map) {
      addIssueToContext(ctx, {
        code: ZodIssueCode.invalid_type,
        expected: ZodParsedType.map,
        received: ctx.parsedType
      });
      return INVALID;
    }
    const keyType = this._def.keyType;
    const valueType = this._def.valueType;
    const pairs = [...ctx.data.entries()].map(([key, value], index) => {
      return {
        key: keyType._parse(new ParseInputLazyPath(ctx, key, ctx.path, [index, "key"])),
        value: valueType._parse(new ParseInputLazyPath(ctx, value, ctx.path, [index, "value"]))
      };
    });
    if (ctx.common.async) {
      const finalMap = /* @__PURE__ */ new Map();
      return Promise.resolve().then(async () => {
        for (const pair of pairs) {
          const key = await pair.key;
          const value = await pair.value;
          if (key.status === "aborted" || value.status === "aborted") {
            return INVALID;
          }
          if (key.status === "dirty" || value.status === "dirty") {
            status.dirty();
          }
          finalMap.set(key.value, value.value);
        }
        return { status: status.value, value: finalMap };
      });
    } else {
      const finalMap = /* @__PURE__ */ new Map();
      for (const pair of pairs) {
        const key = pair.key;
        const value = pair.value;
        if (key.status === "aborted" || value.status === "aborted") {
          return INVALID;
        }
        if (key.status === "dirty" || value.status === "dirty") {
          status.dirty();
        }
        finalMap.set(key.value, value.value);
      }
      return { status: status.value, value: finalMap };
    }
  }
}
ZodMap.create = (keyType, valueType, params) => {
  return new ZodMap({
    valueType,
    keyType,
    typeName: ZodFirstPartyTypeKind.ZodMap,
    ...processCreateParams(params)
  });
};
class ZodSet extends ZodType {
  _parse(input) {
    const { status, ctx } = this._processInputParams(input);
    if (ctx.parsedType !== ZodParsedType.set) {
      addIssueToContext(ctx, {
        code: ZodIssueCode.invalid_type,
        expected: ZodParsedType.set,
        received: ctx.parsedType
      });
      return INVALID;
    }
    const def = this._def;
    if (def.minSize !== null) {
      if (ctx.data.size < def.minSize.value) {
        addIssueToContext(ctx, {
          code: ZodIssueCode.too_small,
          minimum: def.minSize.value,
          type: "set",
          inclusive: true,
          exact: false,
          message: def.minSize.message
        });
        status.dirty();
      }
    }
    if (def.maxSize !== null) {
      if (ctx.data.size > def.maxSize.value) {
        addIssueToContext(ctx, {
          code: ZodIssueCode.too_big,
          maximum: def.maxSize.value,
          type: "set",
          inclusive: true,
          exact: false,
          message: def.maxSize.message
        });
        status.dirty();
      }
    }
    const valueType = this._def.valueType;
    function finalizeSet(elements2) {
      const parsedSet = /* @__PURE__ */ new Set();
      for (const element of elements2) {
        if (element.status === "aborted")
          return INVALID;
        if (element.status === "dirty")
          status.dirty();
        parsedSet.add(element.value);
      }
      return { status: status.value, value: parsedSet };
    }
    const elements = [...ctx.data.values()].map((item, i) => valueType._parse(new ParseInputLazyPath(ctx, item, ctx.path, i)));
    if (ctx.common.async) {
      return Promise.all(elements).then((elements2) => finalizeSet(elements2));
    } else {
      return finalizeSet(elements);
    }
  }
  min(minSize, message) {
    return new ZodSet({
      ...this._def,
      minSize: { value: minSize, message: errorUtil.toString(message) }
    });
  }
  max(maxSize, message) {
    return new ZodSet({
      ...this._def,
      maxSize: { value: maxSize, message: errorUtil.toString(message) }
    });
  }
  size(size, message) {
    return this.min(size, message).max(size, message);
  }
  nonempty(message) {
    return this.min(1, message);
  }
}
ZodSet.create = (valueType, params) => {
  return new ZodSet({
    valueType,
    minSize: null,
    maxSize: null,
    typeName: ZodFirstPartyTypeKind.ZodSet,
    ...processCreateParams(params)
  });
};
class ZodFunction extends ZodType {
  constructor() {
    super(...arguments);
    this.validate = this.implement;
  }
  _parse(input) {
    const { ctx } = this._processInputParams(input);
    if (ctx.parsedType !== ZodParsedType.function) {
      addIssueToContext(ctx, {
        code: ZodIssueCode.invalid_type,
        expected: ZodParsedType.function,
        received: ctx.parsedType
      });
      return INVALID;
    }
    function makeArgsIssue(args, error) {
      return makeIssue({
        data: args,
        path: ctx.path,
        errorMaps: [
          ctx.common.contextualErrorMap,
          ctx.schemaErrorMap,
          getErrorMap(),
          errorMap
        ].filter((x) => !!x),
        issueData: {
          code: ZodIssueCode.invalid_arguments,
          argumentsError: error
        }
      });
    }
    function makeReturnsIssue(returns, error) {
      return makeIssue({
        data: returns,
        path: ctx.path,
        errorMaps: [
          ctx.common.contextualErrorMap,
          ctx.schemaErrorMap,
          getErrorMap(),
          errorMap
        ].filter((x) => !!x),
        issueData: {
          code: ZodIssueCode.invalid_return_type,
          returnTypeError: error
        }
      });
    }
    const params = { errorMap: ctx.common.contextualErrorMap };
    const fn = ctx.data;
    if (this._def.returns instanceof ZodPromise) {
      const me = this;
      return OK(async function(...args) {
        const error = new ZodError([]);
        const parsedArgs = await me._def.args.parseAsync(args, params).catch((e) => {
          error.addIssue(makeArgsIssue(args, e));
          throw error;
        });
        const result = await Reflect.apply(fn, this, parsedArgs);
        const parsedReturns = await me._def.returns._def.type.parseAsync(result, params).catch((e) => {
          error.addIssue(makeReturnsIssue(result, e));
          throw error;
        });
        return parsedReturns;
      });
    } else {
      const me = this;
      return OK(function(...args) {
        const parsedArgs = me._def.args.safeParse(args, params);
        if (!parsedArgs.success) {
          throw new ZodError([makeArgsIssue(args, parsedArgs.error)]);
        }
        const result = Reflect.apply(fn, this, parsedArgs.data);
        const parsedReturns = me._def.returns.safeParse(result, params);
        if (!parsedReturns.success) {
          throw new ZodError([makeReturnsIssue(result, parsedReturns.error)]);
        }
        return parsedReturns.data;
      });
    }
  }
  parameters() {
    return this._def.args;
  }
  returnType() {
    return this._def.returns;
  }
  args(...items) {
    return new ZodFunction({
      ...this._def,
      args: ZodTuple.create(items).rest(ZodUnknown.create())
    });
  }
  returns(returnType) {
    return new ZodFunction({
      ...this._def,
      returns: returnType
    });
  }
  implement(func) {
    const validatedFunc = this.parse(func);
    return validatedFunc;
  }
  strictImplement(func) {
    const validatedFunc = this.parse(func);
    return validatedFunc;
  }
  static create(args, returns, params) {
    return new ZodFunction({
      args: args ? args : ZodTuple.create([]).rest(ZodUnknown.create()),
      returns: returns || ZodUnknown.create(),
      typeName: ZodFirstPartyTypeKind.ZodFunction,
      ...processCreateParams(params)
    });
  }
}
class ZodLazy extends ZodType {
  get schema() {
    return this._def.getter();
  }
  _parse(input) {
    const { ctx } = this._processInputParams(input);
    const lazySchema = this._def.getter();
    return lazySchema._parse({ data: ctx.data, path: ctx.path, parent: ctx });
  }
}
ZodLazy.create = (getter, params) => {
  return new ZodLazy({
    getter,
    typeName: ZodFirstPartyTypeKind.ZodLazy,
    ...processCreateParams(params)
  });
};
class ZodLiteral extends ZodType {
  _parse(input) {
    if (input.data !== this._def.value) {
      const ctx = this._getOrReturnCtx(input);
      addIssueToContext(ctx, {
        received: ctx.data,
        code: ZodIssueCode.invalid_literal,
        expected: this._def.value
      });
      return INVALID;
    }
    return { status: "valid", value: input.data };
  }
  get value() {
    return this._def.value;
  }
}
ZodLiteral.create = (value, params) => {
  return new ZodLiteral({
    value,
    typeName: ZodFirstPartyTypeKind.ZodLiteral,
    ...processCreateParams(params)
  });
};
function createZodEnum(values, params) {
  return new ZodEnum({
    values,
    typeName: ZodFirstPartyTypeKind.ZodEnum,
    ...processCreateParams(params)
  });
}
class ZodEnum extends ZodType {
  constructor() {
    super(...arguments);
    _ZodEnum_cache.set(this, void 0);
  }
  _parse(input) {
    if (typeof input.data !== "string") {
      const ctx = this._getOrReturnCtx(input);
      const expectedValues = this._def.values;
      addIssueToContext(ctx, {
        expected: util.joinValues(expectedValues),
        received: ctx.parsedType,
        code: ZodIssueCode.invalid_type
      });
      return INVALID;
    }
    if (!__classPrivateFieldGet(this, _ZodEnum_cache)) {
      __classPrivateFieldSet(this, _ZodEnum_cache, new Set(this._def.values));
    }
    if (!__classPrivateFieldGet(this, _ZodEnum_cache).has(input.data)) {
      const ctx = this._getOrReturnCtx(input);
      const expectedValues = this._def.values;
      addIssueToContext(ctx, {
        received: ctx.data,
        code: ZodIssueCode.invalid_enum_value,
        options: expectedValues
      });
      return INVALID;
    }
    return OK(input.data);
  }
  get options() {
    return this._def.values;
  }
  get enum() {
    const enumValues = {};
    for (const val of this._def.values) {
      enumValues[val] = val;
    }
    return enumValues;
  }
  get Values() {
    const enumValues = {};
    for (const val of this._def.values) {
      enumValues[val] = val;
    }
    return enumValues;
  }
  get Enum() {
    const enumValues = {};
    for (const val of this._def.values) {
      enumValues[val] = val;
    }
    return enumValues;
  }
  extract(values, newDef = this._def) {
    return ZodEnum.create(values, {
      ...this._def,
      ...newDef
    });
  }
  exclude(values, newDef = this._def) {
    return ZodEnum.create(this.options.filter((opt) => !values.includes(opt)), {
      ...this._def,
      ...newDef
    });
  }
}
_ZodEnum_cache = /* @__PURE__ */ new WeakMap();
ZodEnum.create = createZodEnum;
class ZodNativeEnum extends ZodType {
  constructor() {
    super(...arguments);
    _ZodNativeEnum_cache.set(this, void 0);
  }
  _parse(input) {
    const nativeEnumValues = util.getValidEnumValues(this._def.values);
    const ctx = this._getOrReturnCtx(input);
    if (ctx.parsedType !== ZodParsedType.string && ctx.parsedType !== ZodParsedType.number) {
      const expectedValues = util.objectValues(nativeEnumValues);
      addIssueToContext(ctx, {
        expected: util.joinValues(expectedValues),
        received: ctx.parsedType,
        code: ZodIssueCode.invalid_type
      });
      return INVALID;
    }
    if (!__classPrivateFieldGet(this, _ZodNativeEnum_cache)) {
      __classPrivateFieldSet(this, _ZodNativeEnum_cache, new Set(util.getValidEnumValues(this._def.values)));
    }
    if (!__classPrivateFieldGet(this, _ZodNativeEnum_cache).has(input.data)) {
      const expectedValues = util.objectValues(nativeEnumValues);
      addIssueToContext(ctx, {
        received: ctx.data,
        code: ZodIssueCode.invalid_enum_value,
        options: expectedValues
      });
      return INVALID;
    }
    return OK(input.data);
  }
  get enum() {
    return this._def.values;
  }
}
_ZodNativeEnum_cache = /* @__PURE__ */ new WeakMap();
ZodNativeEnum.create = (values, params) => {
  return new ZodNativeEnum({
    values,
    typeName: ZodFirstPartyTypeKind.ZodNativeEnum,
    ...processCreateParams(params)
  });
};
class ZodPromise extends ZodType {
  unwrap() {
    return this._def.type;
  }
  _parse(input) {
    const { ctx } = this._processInputParams(input);
    if (ctx.parsedType !== ZodParsedType.promise && ctx.common.async === false) {
      addIssueToContext(ctx, {
        code: ZodIssueCode.invalid_type,
        expected: ZodParsedType.promise,
        received: ctx.parsedType
      });
      return INVALID;
    }
    const promisified = ctx.parsedType === ZodParsedType.promise ? ctx.data : Promise.resolve(ctx.data);
    return OK(promisified.then((data) => {
      return this._def.type.parseAsync(data, {
        path: ctx.path,
        errorMap: ctx.common.contextualErrorMap
      });
    }));
  }
}
ZodPromise.create = (schema, params) => {
  return new ZodPromise({
    type: schema,
    typeName: ZodFirstPartyTypeKind.ZodPromise,
    ...processCreateParams(params)
  });
};
class ZodEffects extends ZodType {
  innerType() {
    return this._def.schema;
  }
  sourceType() {
    return this._def.schema._def.typeName === ZodFirstPartyTypeKind.ZodEffects ? this._def.schema.sourceType() : this._def.schema;
  }
  _parse(input) {
    const { status, ctx } = this._processInputParams(input);
    const effect = this._def.effect || null;
    const checkCtx = {
      addIssue: (arg) => {
        addIssueToContext(ctx, arg);
        if (arg.fatal) {
          status.abort();
        } else {
          status.dirty();
        }
      },
      get path() {
        return ctx.path;
      }
    };
    checkCtx.addIssue = checkCtx.addIssue.bind(checkCtx);
    if (effect.type === "preprocess") {
      const processed = effect.transform(ctx.data, checkCtx);
      if (ctx.common.async) {
        return Promise.resolve(processed).then(async (processed2) => {
          if (status.value === "aborted")
            return INVALID;
          const result = await this._def.schema._parseAsync({
            data: processed2,
            path: ctx.path,
            parent: ctx
          });
          if (result.status === "aborted")
            return INVALID;
          if (result.status === "dirty")
            return DIRTY(result.value);
          if (status.value === "dirty")
            return DIRTY(result.value);
          return result;
        });
      } else {
        if (status.value === "aborted")
          return INVALID;
        const result = this._def.schema._parseSync({
          data: processed,
          path: ctx.path,
          parent: ctx
        });
        if (result.status === "aborted")
          return INVALID;
        if (result.status === "dirty")
          return DIRTY(result.value);
        if (status.value === "dirty")
          return DIRTY(result.value);
        return result;
      }
    }
    if (effect.type === "refinement") {
      const executeRefinement = (acc) => {
        const result = effect.refinement(acc, checkCtx);
        if (ctx.common.async) {
          return Promise.resolve(result);
        }
        if (result instanceof Promise) {
          throw new Error("Async refinement encountered during synchronous parse operation. Use .parseAsync instead.");
        }
        return acc;
      };
      if (ctx.common.async === false) {
        const inner = this._def.schema._parseSync({
          data: ctx.data,
          path: ctx.path,
          parent: ctx
        });
        if (inner.status === "aborted")
          return INVALID;
        if (inner.status === "dirty")
          status.dirty();
        executeRefinement(inner.value);
        return { status: status.value, value: inner.value };
      } else {
        return this._def.schema._parseAsync({ data: ctx.data, path: ctx.path, parent: ctx }).then((inner) => {
          if (inner.status === "aborted")
            return INVALID;
          if (inner.status === "dirty")
            status.dirty();
          return executeRefinement(inner.value).then(() => {
            return { status: status.value, value: inner.value };
          });
        });
      }
    }
    if (effect.type === "transform") {
      if (ctx.common.async === false) {
        const base = this._def.schema._parseSync({
          data: ctx.data,
          path: ctx.path,
          parent: ctx
        });
        if (!isValid(base))
          return base;
        const result = effect.transform(base.value, checkCtx);
        if (result instanceof Promise) {
          throw new Error(`Asynchronous transform encountered during synchronous parse operation. Use .parseAsync instead.`);
        }
        return { status: status.value, value: result };
      } else {
        return this._def.schema._parseAsync({ data: ctx.data, path: ctx.path, parent: ctx }).then((base) => {
          if (!isValid(base))
            return base;
          return Promise.resolve(effect.transform(base.value, checkCtx)).then((result) => ({ status: status.value, value: result }));
        });
      }
    }
    util.assertNever(effect);
  }
}
ZodEffects.create = (schema, effect, params) => {
  return new ZodEffects({
    schema,
    typeName: ZodFirstPartyTypeKind.ZodEffects,
    effect,
    ...processCreateParams(params)
  });
};
ZodEffects.createWithPreprocess = (preprocess, schema, params) => {
  return new ZodEffects({
    schema,
    effect: { type: "preprocess", transform: preprocess },
    typeName: ZodFirstPartyTypeKind.ZodEffects,
    ...processCreateParams(params)
  });
};
class ZodOptional extends ZodType {
  _parse(input) {
    const parsedType = this._getType(input);
    if (parsedType === ZodParsedType.undefined) {
      return OK(void 0);
    }
    return this._def.innerType._parse(input);
  }
  unwrap() {
    return this._def.innerType;
  }
}
ZodOptional.create = (type, params) => {
  return new ZodOptional({
    innerType: type,
    typeName: ZodFirstPartyTypeKind.ZodOptional,
    ...processCreateParams(params)
  });
};
class ZodNullable extends ZodType {
  _parse(input) {
    const parsedType = this._getType(input);
    if (parsedType === ZodParsedType.null) {
      return OK(null);
    }
    return this._def.innerType._parse(input);
  }
  unwrap() {
    return this._def.innerType;
  }
}
ZodNullable.create = (type, params) => {
  return new ZodNullable({
    innerType: type,
    typeName: ZodFirstPartyTypeKind.ZodNullable,
    ...processCreateParams(params)
  });
};
class ZodDefault extends ZodType {
  _parse(input) {
    const { ctx } = this._processInputParams(input);
    let data = ctx.data;
    if (ctx.parsedType === ZodParsedType.undefined) {
      data = this._def.defaultValue();
    }
    return this._def.innerType._parse({
      data,
      path: ctx.path,
      parent: ctx
    });
  }
  removeDefault() {
    return this._def.innerType;
  }
}
ZodDefault.create = (type, params) => {
  return new ZodDefault({
    innerType: type,
    typeName: ZodFirstPartyTypeKind.ZodDefault,
    defaultValue: typeof params.default === "function" ? params.default : () => params.default,
    ...processCreateParams(params)
  });
};
class ZodCatch extends ZodType {
  _parse(input) {
    const { ctx } = this._processInputParams(input);
    const newCtx = {
      ...ctx,
      common: {
        ...ctx.common,
        issues: []
      }
    };
    const result = this._def.innerType._parse({
      data: newCtx.data,
      path: newCtx.path,
      parent: {
        ...newCtx
      }
    });
    if (isAsync(result)) {
      return result.then((result2) => {
        return {
          status: "valid",
          value: result2.status === "valid" ? result2.value : this._def.catchValue({
            get error() {
              return new ZodError(newCtx.common.issues);
            },
            input: newCtx.data
          })
        };
      });
    } else {
      return {
        status: "valid",
        value: result.status === "valid" ? result.value : this._def.catchValue({
          get error() {
            return new ZodError(newCtx.common.issues);
          },
          input: newCtx.data
        })
      };
    }
  }
  removeCatch() {
    return this._def.innerType;
  }
}
ZodCatch.create = (type, params) => {
  return new ZodCatch({
    innerType: type,
    typeName: ZodFirstPartyTypeKind.ZodCatch,
    catchValue: typeof params.catch === "function" ? params.catch : () => params.catch,
    ...processCreateParams(params)
  });
};
class ZodNaN extends ZodType {
  _parse(input) {
    const parsedType = this._getType(input);
    if (parsedType !== ZodParsedType.nan) {
      const ctx = this._getOrReturnCtx(input);
      addIssueToContext(ctx, {
        code: ZodIssueCode.invalid_type,
        expected: ZodParsedType.nan,
        received: ctx.parsedType
      });
      return INVALID;
    }
    return { status: "valid", value: input.data };
  }
}
ZodNaN.create = (params) => {
  return new ZodNaN({
    typeName: ZodFirstPartyTypeKind.ZodNaN,
    ...processCreateParams(params)
  });
};
const BRAND = Symbol("zod_brand");
class ZodBranded extends ZodType {
  _parse(input) {
    const { ctx } = this._processInputParams(input);
    const data = ctx.data;
    return this._def.type._parse({
      data,
      path: ctx.path,
      parent: ctx
    });
  }
  unwrap() {
    return this._def.type;
  }
}
class ZodPipeline extends ZodType {
  _parse(input) {
    const { status, ctx } = this._processInputParams(input);
    if (ctx.common.async) {
      const handleAsync = async () => {
        const inResult = await this._def.in._parseAsync({
          data: ctx.data,
          path: ctx.path,
          parent: ctx
        });
        if (inResult.status === "aborted")
          return INVALID;
        if (inResult.status === "dirty") {
          status.dirty();
          return DIRTY(inResult.value);
        } else {
          return this._def.out._parseAsync({
            data: inResult.value,
            path: ctx.path,
            parent: ctx
          });
        }
      };
      return handleAsync();
    } else {
      const inResult = this._def.in._parseSync({
        data: ctx.data,
        path: ctx.path,
        parent: ctx
      });
      if (inResult.status === "aborted")
        return INVALID;
      if (inResult.status === "dirty") {
        status.dirty();
        return {
          status: "dirty",
          value: inResult.value
        };
      } else {
        return this._def.out._parseSync({
          data: inResult.value,
          path: ctx.path,
          parent: ctx
        });
      }
    }
  }
  static create(a, b) {
    return new ZodPipeline({
      in: a,
      out: b,
      typeName: ZodFirstPartyTypeKind.ZodPipeline
    });
  }
}
class ZodReadonly extends ZodType {
  _parse(input) {
    const result = this._def.innerType._parse(input);
    const freeze = (data) => {
      if (isValid(data)) {
        data.value = Object.freeze(data.value);
      }
      return data;
    };
    return isAsync(result) ? result.then((data) => freeze(data)) : freeze(result);
  }
  unwrap() {
    return this._def.innerType;
  }
}
ZodReadonly.create = (type, params) => {
  return new ZodReadonly({
    innerType: type,
    typeName: ZodFirstPartyTypeKind.ZodReadonly,
    ...processCreateParams(params)
  });
};
function cleanParams(params, data) {
  const p = typeof params === "function" ? params(data) : typeof params === "string" ? { message: params } : params;
  const p2 = typeof p === "string" ? { message: p } : p;
  return p2;
}
function custom(check, _params = {}, fatal) {
  if (check)
    return ZodAny.create().superRefine((data, ctx) => {
      var _a, _b;
      const r = check(data);
      if (r instanceof Promise) {
        return r.then((r2) => {
          var _a2, _b2;
          if (!r2) {
            const params = cleanParams(_params, data);
            const _fatal = (_b2 = (_a2 = params.fatal) !== null && _a2 !== void 0 ? _a2 : fatal) !== null && _b2 !== void 0 ? _b2 : true;
            ctx.addIssue({ code: "custom", ...params, fatal: _fatal });
          }
        });
      }
      if (!r) {
        const params = cleanParams(_params, data);
        const _fatal = (_b = (_a = params.fatal) !== null && _a !== void 0 ? _a : fatal) !== null && _b !== void 0 ? _b : true;
        ctx.addIssue({ code: "custom", ...params, fatal: _fatal });
      }
      return;
    });
  return ZodAny.create();
}
const late = {
  object: ZodObject.lazycreate
};
var ZodFirstPartyTypeKind;
(function(ZodFirstPartyTypeKind2) {
  ZodFirstPartyTypeKind2["ZodString"] = "ZodString";
  ZodFirstPartyTypeKind2["ZodNumber"] = "ZodNumber";
  ZodFirstPartyTypeKind2["ZodNaN"] = "ZodNaN";
  ZodFirstPartyTypeKind2["ZodBigInt"] = "ZodBigInt";
  ZodFirstPartyTypeKind2["ZodBoolean"] = "ZodBoolean";
  ZodFirstPartyTypeKind2["ZodDate"] = "ZodDate";
  ZodFirstPartyTypeKind2["ZodSymbol"] = "ZodSymbol";
  ZodFirstPartyTypeKind2["ZodUndefined"] = "ZodUndefined";
  ZodFirstPartyTypeKind2["ZodNull"] = "ZodNull";
  ZodFirstPartyTypeKind2["ZodAny"] = "ZodAny";
  ZodFirstPartyTypeKind2["ZodUnknown"] = "ZodUnknown";
  ZodFirstPartyTypeKind2["ZodNever"] = "ZodNever";
  ZodFirstPartyTypeKind2["ZodVoid"] = "ZodVoid";
  ZodFirstPartyTypeKind2["ZodArray"] = "ZodArray";
  ZodFirstPartyTypeKind2["ZodObject"] = "ZodObject";
  ZodFirstPartyTypeKind2["ZodUnion"] = "ZodUnion";
  ZodFirstPartyTypeKind2["ZodDiscriminatedUnion"] = "ZodDiscriminatedUnion";
  ZodFirstPartyTypeKind2["ZodIntersection"] = "ZodIntersection";
  ZodFirstPartyTypeKind2["ZodTuple"] = "ZodTuple";
  ZodFirstPartyTypeKind2["ZodRecord"] = "ZodRecord";
  ZodFirstPartyTypeKind2["ZodMap"] = "ZodMap";
  ZodFirstPartyTypeKind2["ZodSet"] = "ZodSet";
  ZodFirstPartyTypeKind2["ZodFunction"] = "ZodFunction";
  ZodFirstPartyTypeKind2["ZodLazy"] = "ZodLazy";
  ZodFirstPartyTypeKind2["ZodLiteral"] = "ZodLiteral";
  ZodFirstPartyTypeKind2["ZodEnum"] = "ZodEnum";
  ZodFirstPartyTypeKind2["ZodEffects"] = "ZodEffects";
  ZodFirstPartyTypeKind2["ZodNativeEnum"] = "ZodNativeEnum";
  ZodFirstPartyTypeKind2["ZodOptional"] = "ZodOptional";
  ZodFirstPartyTypeKind2["ZodNullable"] = "ZodNullable";
  ZodFirstPartyTypeKind2["ZodDefault"] = "ZodDefault";
  ZodFirstPartyTypeKind2["ZodCatch"] = "ZodCatch";
  ZodFirstPartyTypeKind2["ZodPromise"] = "ZodPromise";
  ZodFirstPartyTypeKind2["ZodBranded"] = "ZodBranded";
  ZodFirstPartyTypeKind2["ZodPipeline"] = "ZodPipeline";
  ZodFirstPartyTypeKind2["ZodReadonly"] = "ZodReadonly";
})(ZodFirstPartyTypeKind || (ZodFirstPartyTypeKind = {}));
const instanceOfType = (cls, params = {
  message: `Input not instance of ${cls.name}`
}) => custom((data) => data instanceof cls, params);
const stringType = ZodString.create;
const numberType = ZodNumber.create;
const nanType = ZodNaN.create;
const bigIntType = ZodBigInt.create;
const booleanType = ZodBoolean.create;
const dateType = ZodDate.create;
const symbolType = ZodSymbol.create;
const undefinedType = ZodUndefined.create;
const nullType = ZodNull.create;
const anyType = ZodAny.create;
const unknownType = ZodUnknown.create;
const neverType = ZodNever.create;
const voidType = ZodVoid.create;
const arrayType = ZodArray.create;
const objectType = ZodObject.create;
const strictObjectType = ZodObject.strictCreate;
const unionType = ZodUnion.create;
const discriminatedUnionType = ZodDiscriminatedUnion.create;
const intersectionType = ZodIntersection.create;
const tupleType = ZodTuple.create;
const recordType = ZodRecord.create;
const mapType = ZodMap.create;
const setType = ZodSet.create;
const functionType = ZodFunction.create;
const lazyType = ZodLazy.create;
const literalType = ZodLiteral.create;
const enumType = ZodEnum.create;
const nativeEnumType = ZodNativeEnum.create;
const promiseType = ZodPromise.create;
const effectsType = ZodEffects.create;
const optionalType = ZodOptional.create;
const nullableType = ZodNullable.create;
const preprocessType = ZodEffects.createWithPreprocess;
const pipelineType = ZodPipeline.create;
const ostring = () => stringType().optional();
const onumber = () => numberType().optional();
const oboolean = () => booleanType().optional();
const coerce = {
  string: (arg) => ZodString.create({ ...arg, coerce: true }),
  number: (arg) => ZodNumber.create({ ...arg, coerce: true }),
  boolean: (arg) => ZodBoolean.create({
    ...arg,
    coerce: true
  }),
  bigint: (arg) => ZodBigInt.create({ ...arg, coerce: true }),
  date: (arg) => ZodDate.create({ ...arg, coerce: true })
};
const NEVER = INVALID;
var z = /* @__PURE__ */ Object.freeze({
  __proto__: null,
  defaultErrorMap: errorMap,
  setErrorMap,
  getErrorMap,
  makeIssue,
  EMPTY_PATH,
  addIssueToContext,
  ParseStatus,
  INVALID,
  DIRTY,
  OK,
  isAborted,
  isDirty,
  isValid,
  isAsync,
  get util() {
    return util;
  },
  get objectUtil() {
    return objectUtil;
  },
  ZodParsedType,
  getParsedType,
  ZodType,
  datetimeRegex,
  ZodString,
  ZodNumber,
  ZodBigInt,
  ZodBoolean,
  ZodDate,
  ZodSymbol,
  ZodUndefined,
  ZodNull,
  ZodAny,
  ZodUnknown,
  ZodNever,
  ZodVoid,
  ZodArray,
  ZodObject,
  ZodUnion,
  ZodDiscriminatedUnion,
  ZodIntersection,
  ZodTuple,
  ZodRecord,
  ZodMap,
  ZodSet,
  ZodFunction,
  ZodLazy,
  ZodLiteral,
  ZodEnum,
  ZodNativeEnum,
  ZodPromise,
  ZodEffects,
  ZodTransformer: ZodEffects,
  ZodOptional,
  ZodNullable,
  ZodDefault,
  ZodCatch,
  ZodNaN,
  BRAND,
  ZodBranded,
  ZodPipeline,
  ZodReadonly,
  custom,
  Schema: ZodType,
  ZodSchema: ZodType,
  late,
  get ZodFirstPartyTypeKind() {
    return ZodFirstPartyTypeKind;
  },
  coerce,
  any: anyType,
  array: arrayType,
  bigint: bigIntType,
  boolean: booleanType,
  date: dateType,
  discriminatedUnion: discriminatedUnionType,
  effect: effectsType,
  "enum": enumType,
  "function": functionType,
  "instanceof": instanceOfType,
  intersection: intersectionType,
  lazy: lazyType,
  literal: literalType,
  map: mapType,
  nan: nanType,
  nativeEnum: nativeEnumType,
  never: neverType,
  "null": nullType,
  nullable: nullableType,
  number: numberType,
  object: objectType,
  oboolean,
  onumber,
  optional: optionalType,
  ostring,
  pipeline: pipelineType,
  preprocess: preprocessType,
  promise: promiseType,
  record: recordType,
  set: setType,
  strictObject: strictObjectType,
  string: stringType,
  symbol: symbolType,
  transformer: effectsType,
  tuple: tupleType,
  "undefined": undefinedType,
  union: unionType,
  unknown: unknownType,
  "void": voidType,
  NEVER,
  ZodIssueCode,
  quotelessJson,
  ZodError
});
function ItemTable({
  items,
  currentPage,
  totalPages,
  itemsPerPage,
  onPageChange,
  onEditItem,
  onDuplicateItem
}) {
  const safeItems = Array.isArray(items) ? items : [];
  safeItems.length;
  const getPageNumbers = () => {
    const pages = [];
    const maxVisiblePages = 5;
    const halfVisible = Math.floor(maxVisiblePages / 2);
    let startPage = Math.max(0, currentPage - halfVisible);
    const endPage = Math.min(totalPages - 1, startPage + maxVisiblePages - 1);
    if (endPage - startPage + 1 < maxVisiblePages) {
      startPage = Math.max(0, endPage - maxVisiblePages + 1);
    }
    if (startPage > 0) {
      pages.push(0);
      if (startPage > 1) pages.push("ellipsis");
    }
    for (let i = startPage; i <= endPage; i++) {
      pages.push(i);
    }
    if (endPage < totalPages - 1) {
      if (endPage < totalPages - 2) pages.push("ellipsis");
      pages.push(totalPages - 1);
    }
    return pages;
  };
  return /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "rounded-md border", children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "relative w-full overflow-auto", children: [
    /* @__PURE__ */ jsxRuntimeExports.jsxs("table", { className: "w-full caption-bottom text-sm", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx("thead", { children: /* @__PURE__ */ jsxRuntimeExports.jsxs("tr", { className: "border-b bg-gray-100", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx("th", { className: "px-4 py-2 text-left", children: "ID" }),
        /* @__PURE__ */ jsxRuntimeExports.jsx("th", { className: "px-4 py-2 text-left", children: "Item Name" }),
        /* @__PURE__ */ jsxRuntimeExports.jsx("th", { className: "px-4 py-2 text-left", children: "Brand" }),
        /* @__PURE__ */ jsxRuntimeExports.jsx("th", { className: "px-4 py-2 text-left", children: "Unit" }),
        /* @__PURE__ */ jsxRuntimeExports.jsx("th", { className: "px-4 py-2 text-left", children: "Categories" }),
        /* @__PURE__ */ jsxRuntimeExports.jsx("th", { className: "px-4 py-2 text-left", children: "Item Details" }),
        /* @__PURE__ */ jsxRuntimeExports.jsx("th", { className: "px-4 py-2 text-left", children: "Status" }),
        /* @__PURE__ */ jsxRuntimeExports.jsx("th", { className: "px-4 py-2 text-left", children: "Actions" })
      ] }) }),
      /* @__PURE__ */ jsxRuntimeExports.jsx("tbody", { children: safeItems.length === 0 ? /* @__PURE__ */ jsxRuntimeExports.jsx("tr", { children: /* @__PURE__ */ jsxRuntimeExports.jsx("td", { colSpan: 8, className: "px-4 py-8 text-center text-gray-500", children: "No items found" }) }) : safeItems.map((item) => {
        var _a;
        return /* @__PURE__ */ jsxRuntimeExports.jsxs("tr", { className: "border-b", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx("td", { className: "px-4 py-2", children: item.id }),
          /* @__PURE__ */ jsxRuntimeExports.jsx("td", { className: "px-4 py-2", children: item.name }),
          /* @__PURE__ */ jsxRuntimeExports.jsx("td", { className: "px-4 py-2", children: item.brandName }),
          /* @__PURE__ */ jsxRuntimeExports.jsx("td", { className: "px-4 py-2", children: item.defaultUnit }),
          /* @__PURE__ */ jsxRuntimeExports.jsx("td", { className: "px-4 py-2", children: ((_a = item.categories) == null ? void 0 : _a.map((cat) => /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex gap-2 items-center last:mb-0", children: [
            /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "text-xs text-gray-500", children: [
              "ID: ",
              cat.id
            ] }),
            /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "text-sm", children: cat.name })
          ] }, cat.id))) || "-" }),
          /* @__PURE__ */ jsxRuntimeExports.jsx("td", { className: "px-4 py-2", children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "space-y-1", children: [
            /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "text-xs", children: [
              /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "font-medium", children: "Min Qty:" }),
              " ",
              item.minimumOrderQty || "-"
            ] }),
            /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "text-sm", children: [
              /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "font-medium", children: "Increment Qty:" }),
              " ",
              item.incrementOrderQty || "-"
            ] }),
            /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "text-sm", children: [
              /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "font-medium", children: "MRP:" }),
              " ",
              item.mrp || "-"
            ] }),
            /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "text-sm", children: [
              /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "font-medium", children: "WtFactor:" }),
              " ",
              "-"
            ] })
          ] }) }),
          /* @__PURE__ */ jsxRuntimeExports.jsx("td", { className: "px-4 py-2", children: /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: `px-2 py-1 rounded-full text-xs ${item.disabled ? "bg-red-100 text-red-800" : "bg-green-100 text-green-800"}`, children: item.disabled ? "Disabled" : "Active" }) }),
          /* @__PURE__ */ jsxRuntimeExports.jsx("td", { className: "px-4 py-2", children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex flex-col gap-2", children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx(
              Button,
              {
                size: "sm",
                variant: "secondary",
                onClick: () => onEditItem(item),
                children: "Edit"
              }
            ),
            /* @__PURE__ */ jsxRuntimeExports.jsx(
              Button,
              {
                size: "sm",
                variant: "outline",
                onClick: () => onDuplicateItem(item),
                children: "Duplicate"
              }
            )
          ] }) })
        ] }, item.id);
      }) })
    ] }),
    /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "flex items-center justify-end space-x-2 py-4", children: /* @__PURE__ */ jsxRuntimeExports.jsx(Pagination, { children: /* @__PURE__ */ jsxRuntimeExports.jsxs(PaginationContent, { children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx(PaginationItem, { children: /* @__PURE__ */ jsxRuntimeExports.jsx(
        PaginationPrevious,
        {
          href: "#",
          onClick: (e) => {
            e.preventDefault();
            if (currentPage > 0) {
              onPageChange(currentPage - 1);
            }
          },
          "aria-disabled": currentPage <= 0
        }
      ) }),
      getPageNumbers().map((page, index) => page === "ellipsis" ? /* @__PURE__ */ jsxRuntimeExports.jsx(PaginationItem, { children: /* @__PURE__ */ jsxRuntimeExports.jsx(PaginationEllipsis, {}) }, `ellipsis-${index}`) : /* @__PURE__ */ jsxRuntimeExports.jsx(PaginationItem, { children: /* @__PURE__ */ jsxRuntimeExports.jsx(
        PaginationLink,
        {
          href: "#",
          onClick: (e) => {
            e.preventDefault();
            onPageChange(Number(page));
          },
          isActive: currentPage === page,
          children: page
        }
      ) }, page)),
      /* @__PURE__ */ jsxRuntimeExports.jsx(PaginationItem, { children: /* @__PURE__ */ jsxRuntimeExports.jsx(
        PaginationNext,
        {
          href: "#",
          onClick: (e) => {
            e.preventDefault();
            if (currentPage < totalPages - 1) {
              onPageChange(currentPage + 1);
            }
          },
          "aria-disabled": currentPage >= totalPages - 1
        }
      ) })
    ] }) }) })
  ] }) });
}
function StepOne({ formData, onChange, errors, mode, renderRETInput }) {
  var _a, _b, _c;
  const [brandItems, setBrandItems] = reactExports.useState([]);
  const [loading, setLoading] = reactExports.useState(false);
  const [page, setPage] = reactExports.useState(1);
  const [hasMore, setHasMore] = reactExports.useState(true);
  const fetcher = useFetcher();
  reactExports.useEffect(() => {
    var _a2;
    if (fetcher.state === "idle" && ((_a2 = fetcher.data) == null ? void 0 : _a2.brands)) {
      const items = fetcher.data.brands.map((brand) => ({
        value: brand.id,
        label: brand.name
      }));
      if (fetcher.data.brandPage === 1) {
        setBrandItems(items);
      } else {
        setBrandItems((prev) => [...prev, ...items]);
      }
      setPage(fetcher.data.brandPage);
      setHasMore(items.length === 10);
      setLoading(false);
    }
  }, [fetcher.state, fetcher.data]);
  return /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "grid gap-4 p-4 md:p-6", children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "grid gap-4 md:grid-cols-2", children: [
    /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "col-span-full md:col-span-2", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx(Label, { children: "Business Type" }),
      /* @__PURE__ */ jsxRuntimeExports.jsxs(
        RadioGroup,
        {
          value: formData.itemConfig.ondcDomain,
          onValueChange: (val) => onChange({ itemConfig: { ...formData.itemConfig, ondcDomain: val } }),
          className: "grid grid-cols-3 gap-4 mt-1",
          disabled: mode === "edit",
          children: [
            /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center space-x-2", children: [
              /* @__PURE__ */ jsxRuntimeExports.jsx(RadioGroupItem, { id: "type-RET11", value: "RET11" }),
              /* @__PURE__ */ jsxRuntimeExports.jsx(Label, { htmlFor: "type-RET11", children: "Restaurant" })
            ] }),
            /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center space-x-2", children: [
              /* @__PURE__ */ jsxRuntimeExports.jsx(RadioGroupItem, { id: "type-RET10", value: "RET10" }),
              /* @__PURE__ */ jsxRuntimeExports.jsx(Label, { htmlFor: "type-RET10", children: "Non-Restaurant" })
            ] })
          ]
        }
      )
    ] }),
    /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "col-span-full md:col-span-2", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx(Label, { htmlFor: "itemName", children: "Item Name (Required)" }),
      /* @__PURE__ */ jsxRuntimeExports.jsx(
        Input,
        {
          name: "itemName",
          value: String(formData.itemName || ""),
          onChange: (e) => onChange({ itemName: e.target.value }),
          required: true,
          className: "w-full"
        }
      ),
      ((_a = errors == null ? void 0 : errors.itemName) == null ? void 0 : _a[0]) && /* @__PURE__ */ jsxRuntimeExports.jsx("p", { className: "text-red-500", children: errors.itemName[0] })
    ] }),
    renderRETInput("brandName") && /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "col-span-full md:col-span-2", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx(Label, { htmlFor: "brandName", children: "Brand Name" }),
      /* @__PURE__ */ jsxRuntimeExports.jsx(
        Input,
        {
          name: "brandName",
          value: String(formData.brandName || ""),
          onChange: (e) => onChange({ brandName: e.target.value }),
          required: true,
          className: "w-full"
        }
      ),
      ((_b = errors == null ? void 0 : errors.brandName) == null ? void 0 : _b[0]) && /* @__PURE__ */ jsxRuntimeExports.jsx("p", { className: "text-red-500", children: errors.brandName[0] })
    ] }),
    mode === "create" && /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "col-span-full md:col-span-2", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx(Label, { htmlFor: "groupId", children: "Group ID" }),
      /* @__PURE__ */ jsxRuntimeExports.jsx(
        Input,
        {
          name: "groupId",
          value: String(formData.groupId || ""),
          onChange: (e) => onChange({ groupId: e.target.value }),
          className: "w-full"
        }
      ),
      ((_c = errors == null ? void 0 : errors.groupId) == null ? void 0 : _c[0]) && /* @__PURE__ */ jsxRuntimeExports.jsx("p", { className: "text-red-500", children: errors.groupId[0] })
    ] })
  ] }) });
}
const StepOne$1 = reactExports.memo(StepOne);
function StepTwo({ formData, onChange, errors, renderRETInput }) {
  var _a, _b, _c, _d, _e, _f, _g, _h, _i, _j, _k, _l, _m;
  const [tagInput, setTagInput] = reactExports.useState("");
  const [categoryInput, setCategoryInput] = reactExports.useState("");
  const [categoryItems, setCategoryItems] = reactExports.useState([]);
  const [loadingCategories, setLoadingCategories] = reactExports.useState(false);
  const [categoryPage, setCategoryPage] = reactExports.useState(1);
  const [hasMoreCategories, setHasMoreCategories] = reactExports.useState(true);
  const fetcher = useFetcher();
  const [uploadingImage, setUploadingImage] = reactExports.useState(false);
  const [uploadError, setUploadError] = reactExports.useState(null);
  const [selectedFile, setSelectedFile] = reactExports.useState(null);
  const fileInputRef = reactExports.useRef(null);
  const uploadFetcher = useFetcher();
  const searchCategories = async (query) => {
    setLoadingCategories(true);
    fetcher.load(`/home/<USER>
  };
  const loadMoreCategories = async () => {
    if (!hasMoreCategories || loadingCategories) return;
    const nextPage = categoryPage + 1;
    fetcher.load(`/home/<USER>
  };
  reactExports.useEffect(() => {
    var _a2;
    if ((_a2 = fetcher.data) == null ? void 0 : _a2.categories) {
      const items = fetcher.data.categories.map((category) => ({
        value: String(category.id),
        label: category.name,
        numericId: category.id
      }));
      if (fetcher.data.categoryPage === 0) {
        setCategoryItems(items);
      } else {
        setCategoryItems((prev) => [...prev, ...items]);
      }
      setCategoryPage(fetcher.data.categoryPage);
      setHasMoreCategories(items.length === 20);
      setLoadingCategories(false);
    }
  }, [fetcher.data]);
  const handleFileSelect = async (event) => {
    var _a2;
    const file = (_a2 = event.target.files) == null ? void 0 : _a2[0];
    if (!file) return;
    setUploadError(null);
    const MAX_FILE_SIZE = 500 * 1024 * 1024;
    if (file.size > MAX_FILE_SIZE) {
      setUploadError("File size exceeds 5MB limit");
      return;
    }
    const allowedTypes = ["image/jpeg", "image/png", "image/gif", "image/webp"];
    if (!allowedTypes.includes(file.type)) {
      setUploadError("Only JPEG, PNG, GIF, and WEBP images are allowed");
      return;
    }
    setSelectedFile(file);
  };
  const handleUpload = () => {
    if (!selectedFile) return;
    setUploadingImage(true);
    const formData2 = new FormData();
    formData2.append("_action", "uploadImage");
    formData2.append("file", selectedFile, selectedFile.name);
    uploadFetcher.submit(formData2, {
      method: "post",
      encType: "multipart/form-data"
    });
  };
  const openFilePicker = () => {
    var _a2;
    (_a2 = fileInputRef.current) == null ? void 0 : _a2.click();
  };
  reactExports.useEffect(() => {
    if (uploadFetcher.data) {
      if (uploadFetcher.data.error) {
        setUploadError(uploadFetcher.data.error);
        setUploadingImage(false);
      } else if (uploadFetcher.data.fileUrl) {
        const newImage = {
          id: Date.now(),
          url: uploadFetcher.data.fileUrl,
          sequence: (formData.images || []).length + 1,
          isDefault: false
        };
        onChange({
          images: [...formData.images || [], newImage]
        });
        setUploadingImage(false);
        setUploadError(null);
        setSelectedFile(null);
        if (fileInputRef.current) {
          fileInputRef.current.value = "";
        }
      }
    }
  }, [uploadFetcher.data]);
  const handleRemoveImage = (id, e) => {
    e == null ? void 0 : e.preventDefault();
    e == null ? void 0 : e.stopPropagation();
    onChange({
      images: (formData.images || []).filter((img) => img.id !== id)
    });
  };
  const handleSequenceChange = (id, newSequence) => {
    const currentImages = [...formData.images || []];
    const targetImage = currentImages.find((img) => img.id === id);
    currentImages.find((img) => img.isDefault);
    if (!targetImage || targetImage.isDefault) {
      return;
    }
    const validSequence = Math.max(2, newSequence);
    const updatedImages = currentImages.map((img) => {
      if (img.isDefault) {
        return { ...img, sequence: 1 };
      }
      if (img.id === id) {
        return { ...img, sequence: validSequence };
      }
      if (img.sequence >= validSequence && img.id !== id) {
        return { ...img, sequence: img.sequence + 1 };
      }
      return img;
    });
    const sortedImages = updatedImages.sort((a, b) => {
      if (a.isDefault) return -1;
      if (b.isDefault) return 1;
      return a.sequence - b.sequence;
    });
    onChange({
      images: sortedImages
    });
  };
  const handleSetDefault = (id, e) => {
    e == null ? void 0 : e.preventDefault();
    e == null ? void 0 : e.stopPropagation();
    const currentImages = [...formData.images || []];
    const updatedImages = currentImages.map((img) => {
      if (img.id === id) {
        return { ...img, isDefault: true, sequence: 1 };
      }
      if (img.isDefault) {
        return { ...img, isDefault: false, sequence: 2 };
      }
      return {
        ...img,
        isDefault: false,
        sequence: img.sequence >= 2 ? img.sequence + 1 : img.sequence
      };
    });
    const sortedImages = updatedImages.sort((a, b) => {
      if (a.isDefault) return -1;
      if (b.isDefault) return 1;
      return a.sequence - b.sequence;
    });
    onChange({
      images: sortedImages
    });
  };
  const handleTranslationChange = (key, value) => {
    onChange({
      translations: {
        ...formData.translations || {},
        [key]: value
      }
    });
  };
  const handleAddTag = () => {
    if (!tagInput.trim()) return;
    const normalizedTag = tagInput.trim().toLowerCase();
    const existingTags = formData.searchTags || [];
    const isDuplicate = existingTags.some((tag) => tag.toLowerCase() === normalizedTag);
    if (!isDuplicate) {
      onChange({
        searchTags: [...existingTags, tagInput.trim()]
      });
    }
    setTagInput("");
  };
  const handleRemoveTag = (tag) => {
    onChange({
      searchTags: (formData.searchTags || []).filter((t) => t !== tag)
    });
  };
  const handleAddCategory = (categoryValue) => {
    const existingCategories = formData.assignedCategories || [];
    const categoryItem = categoryItems.find((item) => item.value === categoryValue);
    if (!categoryItem) return;
    const categoryId = categoryItem.numericId;
    if (!existingCategories.some((id) => id === categoryId)) {
      onChange({
        assignedCategories: [...existingCategories, categoryId],
        // Also update the categories array to maintain name mapping
        categories: [
          ...formData.categories || [],
          { id: categoryId, name: categoryItem.label }
        ]
      });
    }
    setCategoryInput("");
  };
  const handleRemoveCategory = (categoryId) => {
    onChange({
      assignedCategories: (formData.assignedCategories || []).filter((id) => id !== categoryId),
      categories: (formData.categories || []).filter((cat) => cat.id !== categoryId)
    });
  };
  const getCategoryName = (categoryId) => {
    var _a2;
    const newCategory = categoryItems.find((item) => item.numericId === categoryId);
    if (newCategory) return newCategory.label;
    const existingCategory = (_a2 = formData.categories) == null ? void 0 : _a2.find((cat) => cat.id === categoryId);
    if (existingCategory) return existingCategory.name;
    return categoryId;
  };
  return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "grid gap-4 p-4 md:p-6", children: [
    /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "col-span-full", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsxs(Label, { children: [
        "Item Images ",
        ((_a = formData.itemConfig) == null ? void 0 : _a.ondcDomain) === "RET10" ? `(Required: at least 1)` : ""
      ] }),
      /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex gap-2 items-center", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx(
          Input,
          {
            type: "text",
            readOnly: true,
            value: (selectedFile == null ? void 0 : selectedFile.name) || "No file selected",
            className: "flex-grow",
            onClick: openFilePicker,
            style: { cursor: "pointer" }
          }
        ),
        /* @__PURE__ */ jsxRuntimeExports.jsx(
          "input",
          {
            ref: fileInputRef,
            type: "file",
            accept: "image/*",
            onChange: handleFileSelect,
            className: "hidden"
          }
        ),
        /* @__PURE__ */ jsxRuntimeExports.jsx(
          Button,
          {
            type: "button",
            onClick: selectedFile ? handleUpload : openFilePicker,
            disabled: uploadingImage,
            children: uploadingImage ? /* @__PURE__ */ jsxRuntimeExports.jsxs("span", { className: "flex items-center gap-2", children: [
              /* @__PURE__ */ jsxRuntimeExports.jsxs("svg", { className: "animate-spin h-4 w-4", viewBox: "0 0 24 24", children: [
                /* @__PURE__ */ jsxRuntimeExports.jsx("circle", { className: "opacity-25", cx: "12", cy: "12", r: "10", stroke: "currentColor", strokeWidth: "4", fill: "none" }),
                /* @__PURE__ */ jsxRuntimeExports.jsx("path", { className: "opacity-75", fill: "currentColor", d: "M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" })
              ] }),
              "Uploading..."
            ] }) : selectedFile ? "Upload" : "Add Image"
          }
        )
      ] }),
      uploadError && /* @__PURE__ */ jsxRuntimeExports.jsx("p", { className: "text-red-500 mt-1 text-sm", children: uploadError }),
      ((_b = errors == null ? void 0 : errors.images) == null ? void 0 : _b[0]) && /* @__PURE__ */ jsxRuntimeExports.jsx("p", { className: "text-red-500 mt-1 text-sm", children: errors.images[0] }),
      /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "mt-2 space-y-2", children: (formData.images || []).sort((a, b) => {
        if (a.isDefault) return -1;
        if (b.isDefault) return 1;
        return a.sequence - b.sequence;
      }).map((img) => /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center justify-between border p-2", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center gap-2", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx("span", { children: "Seq:" }),
          img.isDefault ? /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "w-20 px-3 py-2", children: "1" }) : /* @__PURE__ */ jsxRuntimeExports.jsx(
            Input,
            {
              type: "number",
              min: "2",
              value: img.sequence,
              onChange: (e) => handleSequenceChange(img.id, parseInt(e.target.value)),
              className: "w-20",
              onBlur: (e) => {
                if (!e.target.value || parseInt(e.target.value) < 2) {
                  handleSequenceChange(img.id, 2);
                }
              },
              style: {
                WebkitAppearance: "none",
                MozAppearance: "textfield"
              }
            }
          )
        ] }),
        /* @__PURE__ */ jsxRuntimeExports.jsx("img", { src: img.url, alt: "", className: "w-12 h-12 object-cover" }),
        /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center gap-2", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx(Button, { variant: "destructive", size: "sm", onClick: (e) => handleRemoveImage(img.id, e), children: "Remove" }),
          /* @__PURE__ */ jsxRuntimeExports.jsx(
            Button,
            {
              variant: img.isDefault ? "default" : "outline",
              size: "sm",
              onClick: (e) => handleSetDefault(img.id, e),
              children: img.isDefault ? "Default" : "Set Default"
            }
          )
        ] })
      ] }, img.id)) })
    ] }),
    renderRETInput("translations") && /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "grid md:grid-cols-2 gap-2", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx(Label, { children: "Kannada Name" }),
        /* @__PURE__ */ jsxRuntimeExports.jsx(
          Input,
          {
            value: ((_c = formData.translations) == null ? void 0 : _c.kanadaName) || "",
            onChange: (e) => handleTranslationChange("kanadaName", e.target.value)
          }
        )
      ] }),
      /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx(Label, { children: "Hindi Name" }),
        /* @__PURE__ */ jsxRuntimeExports.jsx(
          Input,
          {
            value: ((_d = formData.translations) == null ? void 0 : _d.hindiName) || "",
            onChange: (e) => handleTranslationChange("hindiName", e.target.value)
          }
        )
      ] }),
      /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx(Label, { children: "Tamil Name" }),
        /* @__PURE__ */ jsxRuntimeExports.jsx(
          Input,
          {
            value: ((_e = formData.translations) == null ? void 0 : _e.tamilName) || "",
            onChange: (e) => handleTranslationChange("tamilName", e.target.value)
          }
        )
      ] }),
      /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx(Label, { children: "Telugu Name" }),
        /* @__PURE__ */ jsxRuntimeExports.jsx(
          Input,
          {
            value: ((_f = formData.translations) == null ? void 0 : _f.teluguName) || "",
            onChange: (e) => handleTranslationChange("teluguName", e.target.value)
          }
        )
      ] }),
      /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx(Label, { children: "Bengali Name" }),
        /* @__PURE__ */ jsxRuntimeExports.jsx(
          Input,
          {
            value: ((_g = formData.translations) == null ? void 0 : _g.bengaliName) || "",
            onChange: (e) => handleTranslationChange("bengaliName", e.target.value)
          }
        )
      ] }),
      /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx(Label, { children: "Malayalam Name" }),
        /* @__PURE__ */ jsxRuntimeExports.jsx(
          Input,
          {
            value: ((_h = formData.translations) == null ? void 0 : _h.malyalumName) || "",
            onChange: (e) => handleTranslationChange("malyalumName", e.target.value)
          }
        )
      ] }),
      /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx(Label, { children: "Marathi Name" }),
        /* @__PURE__ */ jsxRuntimeExports.jsx(
          Input,
          {
            value: ((_i = formData.translations) == null ? void 0 : _i.marathiName) || "",
            onChange: (e) => handleTranslationChange("marathiName", e.target.value)
          }
        )
      ] }),
      /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx(Label, { children: "Gujarati Name" }),
        /* @__PURE__ */ jsxRuntimeExports.jsx(
          Input,
          {
            value: ((_j = formData.translations) == null ? void 0 : _j.gujaratiName) || "",
            onChange: (e) => handleTranslationChange("gujaratiName", e.target.value)
          }
        )
      ] }),
      /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx(Label, { children: "Assami Name" }),
        /* @__PURE__ */ jsxRuntimeExports.jsx(
          Input,
          {
            value: ((_k = formData.translations) == null ? void 0 : _k.assamiName) || "",
            onChange: (e) => handleTranslationChange("assamiName", e.target.value)
          }
        )
      ] })
    ] }),
    /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "col-span-full", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx(Label, { children: "Assigned Categories (Required)" }),
      /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex gap-2 items-center", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx(
          Input,
          {
            placeholder: "Search categories",
            value: categoryInput,
            onChange: (e) => {
              setCategoryInput(e.target.value);
              searchCategories(e.target.value);
            }
          }
        ),
        /* @__PURE__ */ jsxRuntimeExports.jsx(
          Button,
          {
            type: "button",
            onClick: () => {
              if (categoryItems.length > 0) {
                handleAddCategory(categoryItems[0].value);
              }
            },
            children: "Add Category"
          }
        )
      ] }),
      /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "flex gap-2 mt-2 flex-wrap", children: (formData.assignedCategories || []).map((categoryId) => {
        const categoryName = getCategoryName(categoryId);
        return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "inline-flex items-center space-x-1 bg-gray-200 p-1 rounded", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsxs("span", { children: [
            categoryName,
            " - ",
            categoryId
          ] }),
          /* @__PURE__ */ jsxRuntimeExports.jsx("button", { onClick: () => handleRemoveCategory(categoryId), children: "×" })
        ] }, categoryId);
      }) }),
      ((_l = errors == null ? void 0 : errors.assignedCategories) == null ? void 0 : _l[0]) && /* @__PURE__ */ jsxRuntimeExports.jsx("p", { className: "text-red-500", children: errors.assignedCategories[0] }),
      loadingCategories && /* @__PURE__ */ jsxRuntimeExports.jsx("p", { children: "Loading categories..." }),
      categoryItems.length > 0 && categoryInput && /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "mt-2 border rounded-md p-2", children: [
        categoryItems.map((category) => {
          var _a2;
          if ((_a2 = formData.assignedCategories) == null ? void 0 : _a2.some((id) => id === category.numericId)) {
            return null;
          }
          return /* @__PURE__ */ jsxRuntimeExports.jsx(
            "button",
            {
              className: "w-full text-left cursor-pointer hover:bg-gray-100 p-1",
              onClick: () => handleAddCategory(category.value),
              onKeyDown: (e) => {
                if (e.key === "Enter" || e.key === " ") {
                  handleAddCategory(category.value);
                }
              },
              children: category.label
            },
            category.value
          );
        }),
        hasMoreCategories && /* @__PURE__ */ jsxRuntimeExports.jsx(
          Button,
          {
            type: "button",
            variant: "ghost",
            className: "w-full mt-2",
            onClick: loadMoreCategories,
            children: "Load More"
          }
        )
      ] })
    ] }),
    /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "col-span-full", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx(Label, { children: "Search Tags (Required)" }),
      /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex gap-2 items-center", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx(
          Input,
          {
            placeholder: "Enter a tag",
            value: tagInput,
            onChange: (e) => setTagInput(e.target.value),
            onKeyDown: (e) => {
              if (e.key === "Enter") {
                e.preventDefault();
                handleAddTag();
              }
            }
          }
        ),
        /* @__PURE__ */ jsxRuntimeExports.jsx(Button, { type: "button", onClick: handleAddTag, children: "Add Tag" })
      ] }),
      ((_m = errors == null ? void 0 : errors.searchTags) == null ? void 0 : _m[0]) && /* @__PURE__ */ jsxRuntimeExports.jsx("p", { className: "text-red-500", children: errors.searchTags[0] }),
      /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "flex gap-2 mt-2 flex-wrap", children: (formData.searchTags || []).map((tag) => /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "inline-flex items-center space-x-1 bg-gray-200 p-1 rounded", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx("span", { children: tag }),
        /* @__PURE__ */ jsxRuntimeExports.jsx("button", { onClick: () => handleRemoveTag(tag), children: "×" })
      ] }, tag)) })
    ] })
  ] });
}
const StepTwo$1 = reactExports.memo(StepTwo);
const DEFAULT_UNITS = ["Pcs", "kg", "g", "l", "ml", "Box", "unit"];
const DEFAULT_DIETARY = ["veg", "nonveg", "egg"];
function StepThree({ formData, onChange, errors, mode, renderRETInput }) {
  var _a, _b, _c, _d, _e, _f, _g, _h, _i, _j, _k, _l, _m, _n;
  const defaultItemConfig = reactExports.useMemo(() => {
    var _a2, _b2, _c2, _d2, _e2, _f2, _g2, _h2, _i2, _j2, _k2, _l2, _m2, _n2, _o, _p, _q, _r, _s;
    return {
      type: ((_a2 = formData.itemConfig) == null ? void 0 : _a2.type) || "B2B",
      unit: formData.defaultUnit || "kg",
      minimumOrderQty: mode === "create" ? 1 : (_b2 = formData.itemConfig) == null ? void 0 : _b2.minimumOrderQty,
      incrementOrderQty: mode === "create" ? 1 : (_c2 = formData.itemConfig) == null ? void 0 : _c2.incrementOrderQty,
      weightFactor: mode === "create" ? 1 : (_d2 = formData.itemConfig) == null ? void 0 : _d2.weightFactor,
      packaging: (_e2 = formData.itemConfig) == null ? void 0 : _e2.packaging,
      mrpPerUnit: (_f2 = formData.itemConfig) == null ? void 0 : _f2.mrpPerUnit,
      maximumOrderQty: (_g2 = formData.itemConfig) == null ? void 0 : _g2.maximumOrderQty,
      maxAvailableQty: (_h2 = formData.itemConfig) == null ? void 0 : _h2.maxAvailableQty,
      productId: (_i2 = formData.itemConfig) == null ? void 0 : _i2.productId,
      originalProductId: (_j2 = formData.itemConfig) == null ? void 0 : _j2.originalProductId,
      isDefaultVariant: ((_k2 = formData.itemConfig) == null ? void 0 : _k2.isDefaultVariant) || false,
      sequencePriority: (_l2 = formData.itemConfig) == null ? void 0 : _l2.sequencePriority,
      gstEligible: ((_m2 = formData.itemConfig) == null ? void 0 : _m2.gstEligible) || "no",
      gstHsnCode: (_n2 = formData.itemConfig) == null ? void 0 : _n2.gstHsnCode,
      gstRate: (_o = formData.itemConfig) == null ? void 0 : _o.gstRate,
      ondcDomain: formData.itemConfig.ondcDomain || "RET10",
      taxExempt: (_p = formData.itemConfig) == null ? void 0 : _p.taxExempt,
      disabled: mode === "create" || mode === "duplicate" ? false : ((_q = formData.itemConfig) == null ? void 0 : _q.disabled) || false,
      description: (_r = formData.itemConfig) == null ? void 0 : _r.description,
      diet: (_s = formData.itemConfig) == null ? void 0 : _s.diet
    };
  }, [formData, mode]);
  const [itemConfig, setItemConfig] = reactExports.useState(() => {
    const config = { ...defaultItemConfig, ...formData.itemConfig };
    return {
      ...config,
      packaging: config.packaging,
      // Ensure minimum values for quantities
      minimumOrderQty: config.minimumOrderQty,
      incrementOrderQty: config.incrementOrderQty,
      maximumOrderQty: config.maximumOrderQty,
      maxAvailableQty: config.maxAvailableQty,
      weightFactor: config.weightFactor
    };
  });
  const [b2b, setB2b] = reactExports.useState(() => Boolean(formData.b2b));
  const [b2c, setB2c] = reactExports.useState(() => Boolean(formData.b2c));
  const [groupId, setGroupId] = reactExports.useState(() => formData.groupId);
  const [groupSeq, setGroupSeq] = reactExports.useState(() => formData.groupSeq);
  reactExports.useEffect(() => {
    if (!formData.itemConfig) return;
    const hasSignificantChanges = JSON.stringify(formData.itemConfig) !== JSON.stringify(itemConfig);
    if (hasSignificantChanges) {
      setItemConfig(() => ({
        ...formData.itemConfig,
        // Ensure minimum values are maintained
        minimumOrderQty: formData.itemConfig.minimumOrderQty,
        incrementOrderQty: formData.itemConfig.incrementOrderQty,
        maximumOrderQty: formData.itemConfig.maximumOrderQty,
        maxAvailableQty: formData.itemConfig.maxAvailableQty,
        weightFactor: formData.itemConfig.weightFactor
      }));
      setB2b(Boolean(formData.b2b));
      setB2c(Boolean(formData.b2c));
      setGroupId(formData.groupId);
      setGroupSeq(formData.groupSeq);
    }
  }, [formData]);
  const debouncedValues = useDebounce({
    itemConfig,
    b2b,
    b2c,
    groupId,
    groupSeq
  }, 300);
  reactExports.useEffect(() => {
    const { itemConfig: itemConfig2, b2b: b2b2, b2c: b2c2, groupId: groupId2, groupSeq: groupSeq2 } = debouncedValues;
    const newData = {
      itemConfig: itemConfig2,
      b2b: b2b2,
      b2c: b2c2,
      groupId: groupId2,
      groupSeq: groupSeq2
    };
    if (JSON.stringify(newData) !== JSON.stringify({
      itemConfig: formData.itemConfig,
      b2b: formData.b2b,
      b2c: formData.b2c,
      groupId: formData.groupId,
      groupSeq: formData.groupSeq
    })) {
      onChange(newData);
    }
  }, [debouncedValues, onChange, formData]);
  const allUnits = reactExports.useMemo(() => {
    const units = [...DEFAULT_UNITS];
    if (formData.defaultUnit && !units.includes(formData.defaultUnit)) {
      units.push(formData.defaultUnit);
    }
    return units;
  }, [formData.defaultUnit]);
  const handleUnitChange = (value) => {
    var _a2, _b2;
    const oldUnit = formData.defaultUnit || ((_a2 = formData.itemConfig) == null ? void 0 : _a2.unit);
    const oldPackaging = (_b2 = formData.itemConfig) == null ? void 0 : _b2.packaging;
    const shouldUpdatePackaging = oldPackaging === oldUnit;
    onChange({
      defaultUnit: value,
      itemConfig: {
        ...formData.itemConfig || {},
        unit: value,
        packaging: shouldUpdatePackaging ? value : oldPackaging
      }
    });
  };
  const updateConfigField = reactExports.useCallback((field, value, isBlur) => {
    setItemConfig((prev) => {
      const newValue = { ...prev };
      if (field === "incrementOrderQty" || field === "minimumOrderQty" || field === "maximumOrderQty" || field === "maxAvailableQty" || field === "weightFactor" || field === "mrpPerUnit" || field === "gstRate") {
        newValue[field] = Number(value) || 0;
      } else {
        newValue[field] = value;
      }
      return newValue;
    });
  }, []);
  return /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "grid gap-4 p-4 md:p-6", children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "grid gap-4 md:grid-cols-2", children: [
    /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "col-span-full", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx(Label, { children: "Item Name" }),
      /* @__PURE__ */ jsxRuntimeExports.jsx(Input, { value: String(formData.itemName || ""), readOnly: true }),
      ((_a = errors == null ? void 0 : errors.itemName) == null ? void 0 : _a[0]) && /* @__PURE__ */ jsxRuntimeExports.jsx("p", { className: "text-red-500", children: errors.itemName[0] })
    ] }),
    renderRETInput("description") && /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "col-span-full", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx(Label, { className: "block", children: "Item Description" }),
      /* @__PURE__ */ jsxRuntimeExports.jsx(
        J,
        {
          value: itemConfig.description,
          onChange: (e) => updateConfigField("description", e.target.value),
          rows: 4,
          className: "w-full mt-1 border rounded-md border-neutral-300"
        }
      ),
      ((_b = errors == null ? void 0 : errors.description) == null ? void 0 : _b[0]) && /* @__PURE__ */ jsxRuntimeExports.jsx("p", { className: "text-red-500", children: errors.description[0] })
    ] }),
    renderRETInput("type") && /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx(Label, { children: "Type" }),
      /* @__PURE__ */ jsxRuntimeExports.jsxs(
        RadioGroup,
        {
          value: itemConfig.type,
          onValueChange: (val) => updateConfigField("type", val),
          className: "grid grid-cols-3 gap-4 mt-1",
          children: [
            /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center space-x-2", children: [
              /* @__PURE__ */ jsxRuntimeExports.jsx(RadioGroupItem, { id: "type-b2b", value: "B2B" }),
              /* @__PURE__ */ jsxRuntimeExports.jsx(Label, { htmlFor: "type-b2b", children: "B2B" })
            ] }),
            /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center space-x-2", children: [
              /* @__PURE__ */ jsxRuntimeExports.jsx(RadioGroupItem, { id: "type-b2c", value: "B2C" }),
              /* @__PURE__ */ jsxRuntimeExports.jsx(Label, { htmlFor: "type-b2c", children: "B2C" })
            ] })
          ]
        }
      )
    ] }),
    renderRETInput("unit") && /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx(Label, { children: "Unit" }),
      /* @__PURE__ */ jsxRuntimeExports.jsx(
        RadioGroup,
        {
          value: formData.defaultUnit || ((_c = formData.itemConfig) == null ? void 0 : _c.unit) || "Pcs",
          onValueChange: handleUnitChange,
          className: "grid grid-cols-3 gap-4 mt-1",
          children: allUnits.map((unit) => /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center space-x-2", children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx(RadioGroupItem, { value: unit, id: `unit-${unit}` }),
            /* @__PURE__ */ jsxRuntimeExports.jsx(Label, { htmlFor: `unit-${unit}`, className: "cursor-pointer", children: unit })
          ] }, unit))
        }
      ),
      ((_d = errors == null ? void 0 : errors.defaultUnit) == null ? void 0 : _d[0]) && /* @__PURE__ */ jsxRuntimeExports.jsx("p", { className: "text-red-500 text-sm mt-1", children: errors.defaultUnit[0] })
    ] }),
    renderRETInput("unit") && (formData.defaultUnit === "unit" || ((_e = formData.itemConfig) == null ? void 0 : _e.unit) === "unit") && /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx(Label, { children: "Packaging" }),
      /* @__PURE__ */ jsxRuntimeExports.jsx(
        Input,
        {
          value: itemConfig.packaging,
          onChange: (e) => updateConfigField("packaging", e.target.value)
        }
      )
    ] }),
    renderRETInput("diet") && /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx(Label, { children: "Dietary" }),
      /* @__PURE__ */ jsxRuntimeExports.jsx(
        RadioGroup,
        {
          value: itemConfig.diet,
          onValueChange: (val) => updateConfigField("diet", val),
          className: "grid grid-cols-3 gap-4 mt-1",
          children: DEFAULT_DIETARY.map((diet) => /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center space-x-2", children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx(RadioGroupItem, { value: diet, id: `diet-${diet}` }),
            /* @__PURE__ */ jsxRuntimeExports.jsx(Label, { htmlFor: `diet-${diet}`, className: "cursor-pointer", children: diet })
          ] }, diet))
        }
      ),
      ((_f = errors == null ? void 0 : errors.diet) == null ? void 0 : _f[0]) && /* @__PURE__ */ jsxRuntimeExports.jsx("p", { className: "text-red-500 text-sm mt-1", children: errors.diet[0] })
    ] }),
    renderRETInput("minimumOrderQty") && /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx(Label, { children: "Minimum Order Qty" }),
      /* @__PURE__ */ jsxRuntimeExports.jsx(
        Input,
        {
          type: "number",
          value: itemConfig.minimumOrderQty,
          onChange: (e) => updateConfigField("minimumOrderQty", e.target.value),
          onBlur: (e) => updateConfigField("minimumOrderQty", e.target.value, true)
        }
      ),
      ((_g = errors == null ? void 0 : errors.minimumOrderQty) == null ? void 0 : _g[0]) && /* @__PURE__ */ jsxRuntimeExports.jsx("p", { className: "text-red-500 text-sm mt-1", children: errors.minimumOrderQty[0] })
    ] }),
    renderRETInput("maximumOrderQty") && /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx(Label, { children: "Maximum Order Qty" }),
      /* @__PURE__ */ jsxRuntimeExports.jsx(
        Input,
        {
          type: "number",
          value: itemConfig.maximumOrderQty,
          onChange: (e) => updateConfigField("maximumOrderQty", e.target.value),
          onBlur: (e) => updateConfigField("maximumOrderQty", e.target.value, true)
        }
      ),
      ((_h = errors == null ? void 0 : errors.maximumOrderQty) == null ? void 0 : _h[0]) && /* @__PURE__ */ jsxRuntimeExports.jsx("p", { className: "text-red-500 text-sm mt-1", children: errors.maximumOrderQty[0] })
    ] }),
    renderRETInput("maxAvailableQty") && /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx(Label, { children: "Maximum Available Daily Qty" }),
      /* @__PURE__ */ jsxRuntimeExports.jsx(
        Input,
        {
          type: "number",
          value: itemConfig.maxAvailableQty,
          onChange: (e) => updateConfigField("maxAvailableQty", e.target.value),
          onBlur: (e) => updateConfigField("maxAvailableQty", e.target.value, true)
        }
      ),
      ((_i = errors == null ? void 0 : errors.maxAvailableQty) == null ? void 0 : _i[0]) && /* @__PURE__ */ jsxRuntimeExports.jsx("p", { className: "text-red-500 text-sm mt-1", children: errors.maxAvailableQty[0] })
    ] }),
    renderRETInput("incrementOrderQty") && /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx(Label, { children: "Increment Qty" }),
      /* @__PURE__ */ jsxRuntimeExports.jsx(
        Input,
        {
          type: "number",
          value: itemConfig.incrementOrderQty,
          onChange: (e) => updateConfigField("incrementOrderQty", e.target.value),
          onBlur: (e) => updateConfigField("incrementOrderQty", e.target.value, true)
        }
      ),
      ((_j = errors == null ? void 0 : errors.incrementOrderQty) == null ? void 0 : _j[0]) && /* @__PURE__ */ jsxRuntimeExports.jsx("p", { className: "text-red-500 text-sm mt-1", children: errors.incrementOrderQty[0] })
    ] }),
    renderRETInput("weightFactor") && /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx(Label, { children: "Weight Factor" }),
      /* @__PURE__ */ jsxRuntimeExports.jsx(
        Input,
        {
          type: "number",
          step: "0.001",
          value: itemConfig.weightFactor,
          onChange: (e) => updateConfigField("weightFactor", e.target.value),
          onBlur: (e) => updateConfigField("weightFactor", e.target.value, true)
        }
      ),
      ((_k = errors == null ? void 0 : errors.weightFactor) == null ? void 0 : _k[0]) && /* @__PURE__ */ jsxRuntimeExports.jsx("p", { className: "text-red-500 text-sm mt-1", children: errors.weightFactor[0] })
    ] }),
    /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx(Label, { children: "MRP/Price per unit" }),
      /* @__PURE__ */ jsxRuntimeExports.jsx(
        Input,
        {
          type: "number",
          value: itemConfig.mrpPerUnit,
          onChange: (e) => updateConfigField("mrpPerUnit", Number(e.target.value))
        }
      )
    ] }),
    renderRETInput("productId") && /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx(Label, { children: "Product ID" }),
      /* @__PURE__ */ jsxRuntimeExports.jsx(
        Input,
        {
          value: itemConfig.productId,
          onChange: (e) => updateConfigField("productId", e.target.value)
        }
      )
    ] }),
    mode !== "create" && renderRETInput("groupId") && //   <>
    //   {/* j) Select original product/variant => dropdown */}
    //   <div>
    //     <Label>Original Product/Variant</Label>
    //     <Select
    //       onValueChange={(val) => updateConfigField("originalProductId", val)}
    //       value={itemConfig.originalProductId || undefined}
    //     >
    //       <SelectTrigger>
    //         <SelectValue placeholder="Select something" />
    //       </SelectTrigger>
    //       <SelectContent>
    //         <SelectItem value="none">None</SelectItem>
    //         <SelectItem value="100">Product #100</SelectItem>
    //         <SelectItem value="200">Product #200</SelectItem>
    //         <SelectItem value="300">Product #300</SelectItem>
    //       </SelectContent>
    //     </Select>
    //   </div>
    //   {/* k) Is default variant => yes/no switch or checkbox */}
    //   <div className="flex items-center space-x-2">
    //     <Switch
    //       checked={itemConfig.isDefaultVariant}
    //       onCheckedChange={(checked) => updateConfigField("isDefaultVariant", checked)}
    //     />
    //     <Label>Is Default Variant?</Label>
    //   </div>
    //   {/* l) Sequence / Priority => Drop down */}
    //   <div>
    //     <Label>Sequence/Priority</Label>
    //     <Select
    //       onValueChange={(val) => updateConfigField("sequencePriority", val)}
    //       value={itemConfig.sequencePriority || undefined}
    //     >
    //       <SelectTrigger>
    //         <SelectValue placeholder="Select priority" />
    //       </SelectTrigger>
    //       <SelectContent>
    //         <SelectItem value="none">None</SelectItem>
    //         <SelectItem value="1">1</SelectItem>
    //         <SelectItem value="2">2</SelectItem>
    //         <SelectItem value="3">3</SelectItem>
    //       </SelectContent>
    //     </Select>
    //   </div>
    // </>
    /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "col-span-full md:col-span-2", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx(Label, { htmlFor: "groupId", children: "Group ID" }),
      /* @__PURE__ */ jsxRuntimeExports.jsx(
        Input,
        {
          name: "groupId",
          value: String(groupId),
          onChange: (e) => setGroupId(e.target.value),
          className: "w-full"
        }
      ),
      ((_l = errors == null ? void 0 : errors.groupId) == null ? void 0 : _l[0]) && /* @__PURE__ */ jsxRuntimeExports.jsx("p", { className: "text-red-500", children: errors.groupId[0] })
    ] }),
    renderRETInput("gstEligible") && /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "col-span-full", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx(Label, { children: "Is GST Eligible?" }),
      /* @__PURE__ */ jsxRuntimeExports.jsxs(
        RadioGroup,
        {
          value: itemConfig.gstEligible,
          onValueChange: (val) => updateConfigField("gstEligible", val),
          children: [
            /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center space-x-2", children: [
              /* @__PURE__ */ jsxRuntimeExports.jsx(RadioGroupItem, { id: "gst-yes", value: "yes" }),
              /* @__PURE__ */ jsxRuntimeExports.jsx(Label, { htmlFor: "gst-yes", children: "Yes" })
            ] }),
            /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center space-x-2", children: [
              /* @__PURE__ */ jsxRuntimeExports.jsx(RadioGroupItem, { id: "gst-no", value: "no" }),
              /* @__PURE__ */ jsxRuntimeExports.jsx(Label, { htmlFor: "gst-no", children: "No" })
            ] })
          ]
        }
      )
    ] }),
    itemConfig.gstEligible === "yes" && /* @__PURE__ */ jsxRuntimeExports.jsxs(jsxRuntimeExports.Fragment, { children: [
      /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx(Label, { children: "GST HSN Code" }),
        /* @__PURE__ */ jsxRuntimeExports.jsx(
          Input,
          {
            value: itemConfig.gstHsnCode,
            onChange: (e) => updateConfigField("gstHsnCode", e.target.value),
            placeholder: "Enter HSN Code"
          }
        )
      ] }),
      /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx(Label, { children: "GST Rate (%)" }),
        /* @__PURE__ */ jsxRuntimeExports.jsx(
          Input,
          {
            type: "number",
            value: itemConfig.gstRate,
            onChange: (e) => updateConfigField("gstRate", Number(e.target.value)),
            placeholder: "Enter GST Rate"
          }
        )
      ] })
    ] }),
    /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx(Label, { children: "Item Status" }),
      /* @__PURE__ */ jsxRuntimeExports.jsxs(
        RadioGroup,
        {
          value: itemConfig.disabled ? "disabled" : "enabled",
          onValueChange: (val) => updateConfigField("disabled", val === "disabled"),
          className: "grid grid-cols-2 gap-4 mt-1",
          children: [
            /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center space-x-2", children: [
              /* @__PURE__ */ jsxRuntimeExports.jsx(RadioGroupItem, { id: "status-enabled", value: "enabled" }),
              /* @__PURE__ */ jsxRuntimeExports.jsx(Label, { htmlFor: "status-enabled", children: "Enabled" })
            ] }),
            /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center space-x-2", children: [
              /* @__PURE__ */ jsxRuntimeExports.jsx(RadioGroupItem, { id: "status-disabled", value: "disabled" }),
              /* @__PURE__ */ jsxRuntimeExports.jsx(Label, { htmlFor: "status-disabled", children: "Disabled" })
            ] })
          ]
        }
      ),
      ((_m = errors == null ? void 0 : errors.disabled) == null ? void 0 : _m[0]) && /* @__PURE__ */ jsxRuntimeExports.jsx("p", { className: "text-red-500 text-sm mt-1", children: errors.disabled[0] })
    ] }),
    /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx(Label, { children: itemConfig.ondcDomain === "RET10" ? "Tax Excluded" : "Is GST Eligible?" }),
      /* @__PURE__ */ jsxRuntimeExports.jsxs(
        RadioGroup,
        {
          value: itemConfig.taxExempt ? "enabled" : "disabled",
          onValueChange: (val) => updateConfigField("taxExempt", val === "enabled"),
          className: "grid grid-cols-2 gap-4 mt-1",
          children: [
            /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center space-x-2", children: [
              /* @__PURE__ */ jsxRuntimeExports.jsx(RadioGroupItem, { id: "taxExempt-no", value: itemConfig.ondcDomain === "RET11" ? "enabled" : "disabled" }),
              /* @__PURE__ */ jsxRuntimeExports.jsx(Label, { htmlFor: "taxExempt-no", children: "No" })
            ] }),
            /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center space-x-2", children: [
              /* @__PURE__ */ jsxRuntimeExports.jsx(RadioGroupItem, { id: "taxExempt-yes", value: itemConfig.ondcDomain === "RET11" ? "disabled" : "enabled" }),
              /* @__PURE__ */ jsxRuntimeExports.jsx(Label, { htmlFor: "taxExempt-yes", children: "Yes" })
            ] })
          ]
        }
      ),
      itemConfig.ondcDomain === "RET11" && /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "mt-1 text-sm text-gray-500", children: "ⓘ Select no incase of MRP based products" }),
      ((_n = errors == null ? void 0 : errors.taxExempt) == null ? void 0 : _n[0]) && /* @__PURE__ */ jsxRuntimeExports.jsx("p", { className: "text-red-500 text-sm mt-1", children: errors.taxExempt[0] })
    ] })
  ] }) });
}
const StepThree$1 = reactExports.memo(StepThree);
const stepOneSchema = z.object({
  ondcDomain: z.enum(["RET11", "RET10"]),
  itemName: z.string().nonempty("Item name is required"),
  brandName: z.string().optional(),
  groupId: z.string().optional()
});
const stepTwoSchema = z.object({
  ondcDomain: z.enum(["RET11", "RET10"]),
  images: z.array(z.object({
    id: z.number(),
    url: z.string().nonempty("Image URL is required"),
    sequence: z.number(),
    isDefault: z.boolean()
  })).optional(),
  translations: z.object({
    kanadaName: z.string().optional(),
    hindiName: z.string().optional(),
    tamilName: z.string().optional(),
    teluguName: z.string().optional(),
    bengaliName: z.string().optional(),
    malyalumName: z.string().optional(),
    marathiName: z.string().optional(),
    gujaratiName: z.string().optional(),
    assamiName: z.string().optional()
  }),
  searchTags: z.array(z.string()).min(1, "At least one search tag is required"),
  assignedCategories: z.array(z.number()).min(1, "At least one category must be assigned")
}).superRefine((data, ctx) => {
  if (data.ondcDomain === "RET10" && (!data.images || data.images.length === 0)) {
    ctx.addIssue({
      path: ["images"],
      code: z.ZodIssueCode.custom,
      message: "At least one image is required for RET10"
    });
  }
});
const stepThreeSchema = z.object({
  itemConfig: z.object({
    type: z.enum(["B2B", "B2C"]),
    unit: z.string().nonempty("Unit is required"),
    minimumOrderQty: z.number().nonnegative("Minimum order quantity must be at least 0"),
    incrementOrderQty: z.number().nonnegative("Increment order quantity must be at least 0"),
    weightFactor: z.number().min(1e-3, "Weight factor must be at least 0.001"),
    packaging: z.string().optional(),
    mrpPerUnit: z.number().min(1e-4, "MRP per unit must be at least 0.0001"),
    maximumOrderQty: z.number().nonnegative("Maximum order quantity must be at least 0"),
    maxAvailableQty: z.number().nonnegative("Maximum available quantity must be at least 0"),
    productId: z.string().optional(),
    originalProductId: z.string(),
    isDefaultVariant: z.boolean(),
    sequencePriority: z.string(),
    gstEligible: z.enum(["yes", "no"]),
    gstHsnCode: z.string().optional(),
    gstRate: z.number().min(0, "GST rate must be at least 0").optional(),
    disabled: z.boolean(),
    ondcDomain: z.enum(["RET11", "RET10"]),
    taxExempt: z.boolean(),
    description: z.string().optional(),
    diet: z.string().optional()
  })
}).superRefine((data, ctx) => {
  const domain = data.itemConfig.ondcDomain;
  if (domain === "RET11") {
    const { description, diet } = data.itemConfig;
    if (description && description.length < 25) {
      ctx.addIssue({
        path: ["itemConfig", "description"],
        code: z.ZodIssueCode.custom,
        message: "Description must be at least 25 characters"
      });
    } else if (description && description.length > 1024) {
      ctx.addIssue({
        path: ["itemConfig", "description"],
        code: z.ZodIssueCode.custom,
        message: "Description must be at most 1024 characters"
      });
    }
    if (!diet || diet.trim() === "") {
      ctx.addIssue({
        path: ["itemConfig", "diet"],
        code: z.ZodIssueCode.custom,
        message: "Dietary is required"
      });
    }
  }
});
const itemSchema = z.object({
  id: z.number().optional(),
  ondcDomain: z.enum(["RET11", "RET10"]),
  defaultUnit: z.string().nonempty("Default unit is required"),
  name: z.string().nonempty("Name is required"),
  picture: z.string().optional(),
  nameInKannada: z.string().optional(),
  nameInTelugu: z.string().optional(),
  nameInTamil: z.string().optional(),
  nameInMalayalam: z.string().optional(),
  nameInHindi: z.string().optional(),
  nameInAssame: z.string().optional(),
  nameInGujarati: z.string().optional(),
  nameInMarathi: z.string().optional(),
  nameInBangla: z.string().optional(),
  defaultWeightFactor: z.number().optional(),
  gstHsnCode: z.string().optional(),
  gstRate: z.number().optional(),
  source: z.string().nonempty("Source is required"),
  sourceKey: z.string().nonempty("Source key is required"),
  productId: z.string().optional(),
  brandName: z.string().optional(),
  packaging: z.string().optional(),
  mrp: z.number().min(1, "MRP must be at least 1"),
  b2b: z.boolean(),
  b2c: z.boolean(),
  groupId: z.string().optional(),
  groupSeq: z.number().optional(),
  searchTag: z.string().optional(),
  categories: z.array(
    z.object({
      id: z.number(),
      name: z.string().optional(),
      picture: z.string().optional(),
      picturex: z.string().optional(),
      picturexx: z.string().optional(),
      level: z.number().optional(),
      totalItems: z.number().optional(),
      parentCategories: z.array(z.string()).optional()
    })
  ).optional()
});
itemSchema.extend({
  defaultUnit: z.string().optional(),
  name: z.string().optional(),
  picture: z.string().optional(),
  mrp: z.number().optional(),
  b2b: z.boolean().optional(),
  b2c: z.boolean().optional(),
  source: z.string().optional(),
  sourceKey: z.string().optional()
});
const getInitialFormState = () => ({
  itemName: "",
  images: [],
  translations: {
    kanadaName: "",
    hindiName: "",
    tamilName: "",
    teluguName: "",
    bengaliName: "",
    malyalumName: "",
    marathiName: "",
    gujaratiName: "",
    assamiName: ""
  },
  searchTags: [],
  assignedCategories: [],
  itemConfig: {
    type: "B2B",
    unit: "kg",
    minimumOrderQty: 0,
    incrementOrderQty: 1,
    weightFactor: 1,
    packaging: void 0,
    mrpPerUnit: 0,
    maximumOrderQty: 10,
    maxAvailableQty: 100,
    productId: void 0,
    originalProductId: "",
    isDefaultVariant: false,
    sequencePriority: "",
    gstEligible: "no",
    gstHsnCode: void 0,
    gstRate: void 0,
    disabled: false,
    ondcDomain: "RET10",
    taxExempt: false
  },
  // Optional fields with empty defaults
  brandName: void 0,
  defaultUnit: "kg",
  name: "",
  picture: "",
  mrp: 0,
  b2b: false,
  b2c: false,
  groupId: void 0,
  groupSeq: void 0,
  searchTag: ""
});
const getInitialRestaurantFormState = () => ({
  itemName: "",
  images: [],
  translations: {
    kanadaName: "",
    hindiName: "",
    tamilName: "",
    teluguName: "",
    bengaliName: "",
    malyalumName: "",
    marathiName: "",
    gujaratiName: "",
    assamiName: ""
  },
  searchTags: [],
  assignedCategories: [],
  itemConfig: {
    type: "B2C",
    unit: "unit",
    minimumOrderQty: 1,
    incrementOrderQty: 1,
    weightFactor: 1,
    packaging: void 0,
    mrpPerUnit: 0,
    maximumOrderQty: 100,
    maxAvailableQty: 1e3,
    productId: void 0,
    originalProductId: "",
    isDefaultVariant: false,
    sequencePriority: "",
    gstEligible: "no",
    gstHsnCode: void 0,
    gstRate: void 0,
    disabled: false,
    ondcDomain: "RET11",
    taxExempt: false,
    description: "",
    diet: ""
  },
  // Optional fields with empty defaults
  brandName: void 0,
  defaultUnit: "unit",
  name: "",
  picture: "",
  mrp: 0,
  b2b: false,
  b2c: false,
  groupId: void 0,
  groupSeq: void 0,
  searchTag: ""
});
function masterItemDtoToFormState(dto) {
  var _a, _b;
  const images = ((_a = dto.picture) == null ? void 0 : _a.split(",").map((url, index) => ({
    id: Date.now() + index,
    url: url.trim(),
    sequence: index + 1,
    isDefault: index === 0
  }))) || [];
  const searchTags = ((_b = dto.searchTag) == null ? void 0 : _b.split(",").map((tag) => tag.trim()).filter(Boolean)) || [];
  const assignedCategories = (dto.categories || []).map((cat) => cat.id);
  const validOndcDomain = (domain) => {
    if (domain === "RET11") return "RET11";
    return "RET10";
  };
  const itemConfig = {
    type: dto.b2b ? "B2B" : "B2C",
    unit: dto.defaultUnit,
    minimumOrderQty: dto.minimumOrderQty || 0,
    incrementOrderQty: dto.incrementOrderQty || 0,
    weightFactor: dto.defaultWeightFactor || 1e-3,
    packaging: dto.packaging || "",
    mrpPerUnit: dto.mrp || 0,
    maximumOrderQty: dto.maximumOrderQty || 0,
    maxAvailableQty: dto.maxAvailableQty || 0,
    productId: dto.productId || "",
    originalProductId: dto.productId || "",
    isDefaultVariant: false,
    sequencePriority: "",
    gstEligible: dto.gstHsnCode ? "yes" : "no",
    gstHsnCode: dto.gstHsnCode || "",
    gstRate: dto.gstRate || 0,
    disabled: dto.disabled ?? false,
    ondcDomain: validOndcDomain(dto.ondcDomain),
    taxExempt: dto.taxExempt ?? false,
    // Optional in target type
    // Use validated value    taxExempt:dto.taxExempt||false
    description: dto.description || "",
    diet: dto.diet || ""
  };
  const formState = {
    // Start with all original fields from DTO
    ...dto,
    // UI-specific transformations
    itemName: dto.name,
    images,
    searchTags,
    assignedCategories,
    groupId: dto.groupId || "",
    groupSeq: dto.groupSeq,
    translations: {
      kanadaName: dto.nameInKannada || "",
      hindiName: dto.nameInHindi || "",
      tamilName: dto.nameInTamil || "",
      teluguName: dto.nameInTelugu || "",
      bengaliName: dto.nameInBangla || "",
      malyalumName: dto.nameInMalayalam || "",
      marathiName: dto.nameInMarathi || "",
      gujaratiName: dto.nameInGujarati || "",
      assamiName: dto.nameInAssame || ""
    },
    itemConfig
  };
  return formState;
}
function formStateToMasterItemRequest(formState, originalDto) {
  var _a, _b;
  const request = {
    // name: formState.itemName as string || originalDto?.name || "",
    // picture: formState.images.map(img => img.url).join(",") || originalDto?.picture || "",
    // defaultUnit: formState.defaultUnit || formState.itemConfig.unit || originalDto?.defaultUnit || "",
    // b2b: formState.itemConfig.type === "B2B",
    // b2c: formState.itemConfig.type === "B2C",
    ondcDomain: formState.itemConfig.ondcDomain
    // taxExempt: formState.itemConfig.taxExempt || originalDto?.taxExempt || false,
    // mrp: formState.itemConfig.mrpPerUnit || originalDto?.mrp || 0,
    // Add itemConfig fields
    // minimumOrderQty: formState.itemConfig.minimumOrderQty,
    // incrementOrderQty: formState.itemConfig.incrementOrderQty,
    // maximumOrderQty: formState.itemConfig.maximumOrderQty,
    // maxAvailableQty: formState.itemConfig.maxAvailableQty,
    // disabled: formState.itemConfig.disabled,
    // productId: formState.itemConfig.productId || originalDto?.productId,
  };
  if (request.ondcDomain === "RET11") {
    request.b2b = false;
    request.b2c = true;
  } else {
    request.b2b = formState.itemConfig.type === "B2B";
    request.b2c = formState.itemConfig.type === "B2C";
  }
  if (!(originalDto == null ? void 0 : originalDto.name) || originalDto.name !== formState.itemName) {
    request.name = formState.itemName;
  }
  if (request.ondcDomain === "RET10" && (!(originalDto == null ? void 0 : originalDto.brandName) || originalDto.brandName !== formState.brandName)) {
    request.brandName = formState.brandName;
  }
  if (!(originalDto == null ? void 0 : originalDto.groupId) || originalDto.groupId !== formState.groupId) {
    request.groupId = formState.groupId;
  }
  if (!(originalDto == null ? void 0 : originalDto.groupSeq) || originalDto.groupSeq !== formState.groupSeq) {
    request.groupSeq = formState.groupSeq;
  }
  if (!(originalDto == null ? void 0 : originalDto.source) || originalDto.source !== formState.source) {
    request.source = formState.source ? formState.source : request.ondcDomain === "RET11" ? "rnet" : "mnet";
  }
  if (!(originalDto == null ? void 0 : originalDto.sourceKey) || originalDto.sourceKey !== formState.sourceKey) {
    request.sourceKey = formState.sourceKey || crypto.randomUUID();
  }
  if (!(originalDto == null ? void 0 : originalDto.picture) || originalDto.picture !== formState.images.map((img) => img.url).join(",")) {
    request.picture = formState.images.map((img) => img.url).join(",");
  }
  if (request.ondcDomain === "RET10") {
    if (!(originalDto == null ? void 0 : originalDto.nameInKannada) || originalDto.nameInKannada !== formState.translations.kanadaName) {
      request.nameInKannada = formState.translations.kanadaName;
    }
    if (!(originalDto == null ? void 0 : originalDto.nameInHindi) || originalDto.nameInHindi !== formState.translations.hindiName) {
      request.nameInHindi = formState.translations.hindiName;
    }
    if (!(originalDto == null ? void 0 : originalDto.nameInTamil) || originalDto.nameInTamil !== formState.translations.tamilName) {
      request.nameInTamil = formState.translations.tamilName;
    }
    if (!(originalDto == null ? void 0 : originalDto.nameInTelugu) || originalDto.nameInTelugu !== formState.translations.teluguName) {
      request.nameInTelugu = formState.translations.teluguName;
    }
    if (!(originalDto == null ? void 0 : originalDto.nameInBangla) || originalDto.nameInBangla !== formState.translations.bengaliName) {
      request.nameInBangla = formState.translations.bengaliName;
    }
    if (!(originalDto == null ? void 0 : originalDto.nameInMalayalam) || originalDto.nameInMalayalam !== formState.translations.malyalumName) {
      request.nameInMalayalam = formState.translations.malyalumName;
    }
    if (!(originalDto == null ? void 0 : originalDto.nameInMarathi) || originalDto.nameInMarathi !== formState.translations.marathiName) {
      request.nameInMarathi = formState.translations.marathiName;
    }
    if (!(originalDto == null ? void 0 : originalDto.nameInGujarati) || originalDto.nameInGujarati !== formState.translations.gujaratiName) {
      request.nameInGujarati = formState.translations.gujaratiName;
    }
    if (!(originalDto == null ? void 0 : originalDto.nameInAssame) || originalDto.nameInAssame !== formState.translations.assamiName) {
      request.nameInAssame = formState.translations.assamiName;
    }
  }
  if (formState.searchTags) {
    const existingTags = ((_a = originalDto == null ? void 0 : originalDto.searchTag) == null ? void 0 : _a.split(",")) || [];
    const addedTags = formState.searchTags.filter((tag) => !existingTags.includes(tag));
    const removedTags = existingTags.filter((tag) => !formState.searchTags.includes(tag));
    if (addedTags.length > 0) {
      request.searchTag = existingTags.concat(addedTags).join(",");
    }
    if (removedTags.length > 0) {
      request.searchTag = existingTags.filter((tag) => !removedTags.includes(tag)).join(",");
    }
  }
  if (formState.assignedCategories) {
    const existingCategories = ((_b = originalDto == null ? void 0 : originalDto.categories) == null ? void 0 : _b.map((category) => category.id)) || [];
    const addedCategories = formState.assignedCategories.filter((id) => !existingCategories.includes(id));
    const removedCategories = existingCategories.filter((id) => !formState.assignedCategories.includes(id));
    if (addedCategories.length > 0) {
      request.categories = ((originalDto == null ? void 0 : originalDto.categories) || []).concat(addedCategories.map((id) => ({ id })));
    }
    if (removedCategories.length > 0) {
      request.categories = ((originalDto == null ? void 0 : originalDto.categories) || []).filter((category) => !removedCategories.includes(category.id));
    }
  }
  if (request.ondcDomain === "RET11" && (!(originalDto == null ? void 0 : originalDto.description) || originalDto.description !== formState.itemConfig.description)) {
    request.description = formState.itemConfig.description;
  }
  if (request.ondcDomain === "RET11" && (!(originalDto == null ? void 0 : originalDto.diet) || originalDto.diet !== formState.itemConfig.diet)) {
    request.diet = formState.itemConfig.diet;
  }
  if (!(originalDto == null ? void 0 : originalDto.defaultUnit) || originalDto.defaultUnit !== formState.defaultUnit) {
    request.defaultUnit = formState.defaultUnit;
  }
  if (!(originalDto == null ? void 0 : originalDto.packaging) || originalDto.packaging !== formState.itemConfig.packaging) {
    request.packaging = formState.itemConfig.packaging;
  }
  if (!(originalDto == null ? void 0 : originalDto.mrp) || originalDto.mrp !== formState.itemConfig.mrpPerUnit) {
    request.mrp = formState.itemConfig.mrpPerUnit;
  }
  if (request.ondcDomain === "RET10" && (!(originalDto == null ? void 0 : originalDto.minimumOrderQty) || originalDto.minimumOrderQty !== formState.itemConfig.minimumOrderQty)) {
    request.minimumOrderQty = formState.itemConfig.minimumOrderQty;
  }
  if (request.ondcDomain === "RET10" && (!(originalDto == null ? void 0 : originalDto.incrementOrderQty) || originalDto.incrementOrderQty !== formState.itemConfig.incrementOrderQty)) {
    request.incrementOrderQty = formState.itemConfig.incrementOrderQty;
  }
  if (request.ondcDomain === "RET10" && (!(originalDto == null ? void 0 : originalDto.maximumOrderQty) || originalDto.maximumOrderQty !== formState.itemConfig.maximumOrderQty)) {
    request.maximumOrderQty = formState.itemConfig.maximumOrderQty;
  }
  if (request.ondcDomain === "RET10" && (!(originalDto == null ? void 0 : originalDto.maxAvailableQty) || originalDto.maxAvailableQty !== formState.itemConfig.maxAvailableQty)) {
    request.maxAvailableQty = formState.itemConfig.maxAvailableQty;
  }
  if ((originalDto == null ? void 0 : originalDto.disabled) === void 0 || originalDto.disabled !== formState.itemConfig.disabled) {
    request.disabled = formState.itemConfig.disabled;
  }
  if (request.ondcDomain === "RET10" && (!(originalDto == null ? void 0 : originalDto.productId) || originalDto.productId !== formState.itemConfig.productId)) {
    request.productId = formState.itemConfig.productId;
  }
  if (request.ondcDomain === "RET10" && (!(originalDto == null ? void 0 : originalDto.defaultWeightFactor) || originalDto.defaultWeightFactor !== formState.itemConfig.weightFactor)) {
    request.defaultWeightFactor = formState.itemConfig.weightFactor;
  }
  if (formState.itemConfig.gstEligible === "yes") {
    request.gstHsnCode = formState.itemConfig.gstHsnCode;
    request.gstRate = formState.itemConfig.gstRate;
  } else {
    request.gstHsnCode = void 0;
    request.gstRate = void 0;
  }
  if ((originalDto == null ? void 0 : originalDto.taxExempt) === void 0 || originalDto.taxExempt !== formState.itemConfig.taxExempt) {
    request.taxExempt = formState.itemConfig.taxExempt;
  }
  Object.keys(request).forEach((key) => {
    if (request[key] === void 0 || request[key] === "") {
      delete request[key];
    }
  });
  return request;
}
function PageSizeSelector({
  value,
  onValueChange,
  options = [5, 10, 20, 50],
  className = "w-[180px]"
}) {
  return /* @__PURE__ */ jsxRuntimeExports.jsxs(Select, { value, onValueChange, children: [
    /* @__PURE__ */ jsxRuntimeExports.jsx(SelectTrigger, { className, children: /* @__PURE__ */ jsxRuntimeExports.jsx(SelectValue, { placeholder: "Items per page" }) }),
    /* @__PURE__ */ jsxRuntimeExports.jsx(SelectContent, { children: options.map((size) => /* @__PURE__ */ jsxRuntimeExports.jsxs(SelectItem, { value: size.toString(), children: [
      size,
      " per page"
    ] }, size)) })
  ] });
}
function MasterItemsRoute() {
  var _a, _b, _c;
  const loaderData = useLoaderData();
  const navigate = useNavigate();
  const fetcher = useFetcher();
  const actionData = fetcher.data;
  const [openDialog, setOpenDialog] = reactExports.useState(false);
  const [currentStep, setCurrentStep] = reactExports.useState(1);
  const [selectedItem, setSelectedItem] = reactExports.useState(null);
  const [mode, setMode] = reactExports.useState("create");
  const [formDataState, setFormDataState] = reactExports.useState(getInitialFormState());
  const [searchInput, setSearchInput] = reactExports.useState(loaderData.search || "");
  const debouncedSearch = useDebounce(searchInput, 500);
  const [pageSize, setPageSize] = reactExports.useState(((_a = loaderData.itemsPerPage) == null ? void 0 : _a.toString()) || "20");
  reactExports.useEffect(() => {
    if (debouncedSearch.length === 0 || debouncedSearch.length >= 3) {
      const searchParams = new URLSearchParams(window.location.search);
      searchParams.set("tab", loaderData.tab || "b2b");
      if (debouncedSearch) {
        searchParams.set("search", debouncedSearch);
      } else {
        searchParams.delete("search");
      }
      searchParams.set("page", "0");
      navigate(`?${searchParams.toString()}`);
    }
  }, [debouncedSearch, loaderData.tab, navigate]);
  const handleSearchChange = (e) => {
    setSearchInput(e.target.value);
  };
  reactExports.useEffect(() => {
    var _a2, _b2;
    if (fetcher.state === "idle" && ((_a2 = fetcher.data) == null ? void 0 : _a2.success) && ((_b2 = fetcher.data) == null ? void 0 : _b2.intent) !== "uploadImage") {
      setOpenDialog(false);
      setFormDataState(getInitialFormState());
      setCurrentStep(1);
      navigate(`?tab=${loaderData.tab}&search=${loaderData.search || ""}&page=${loaderData.currentPage || 0}`, {
        replace: true
      });
    }
  }, [fetcher.state, fetcher.data, loaderData.tab, loaderData.search, loaderData.currentPage, navigate]);
  const handleFormDataChange = (data) => {
    setFormDataState((prev) => ({
      ...prev,
      ...data
    }));
  };
  const handleEditItem = reactExports.useCallback((item) => {
    console.log("item", item);
    setFormDataState(masterItemDtoToFormState(item));
    setMode("edit");
    setSelectedItem(item);
    setCurrentStep(1);
    setOpenDialog(true);
  }, []);
  const handleDuplicateItem = reactExports.useCallback((item) => {
    const formState = masterItemDtoToFormState(item);
    formState.id = void 0;
    formState.itemName = `${item.name} (Copy)`;
    formState.name = `${item.name} (Copy)`;
    formState.source = "mnet";
    formState.sourceKey = crypto.randomUUID();
    setFormDataState(formState);
    setMode("duplicate");
    setSelectedItem(item);
    setCurrentStep(1);
    setOpenDialog(true);
  }, []);
  const handleDialogChange = (open) => {
    if (!open) {
      setSelectedItem(null);
      setMode("create");
      setCurrentStep(1);
      setFormDataState(getInitialFormState());
    }
    setOpenDialog(open);
  };
  function handleTabChange(newTab) {
    navigate(`?tab=${newTab}&search=${loaderData.search}&page=0&pageSize=${pageSize}`);
  }
  function handlePageChange(newPage) {
    navigate(`?tab=${loaderData.tab}&search=${loaderData.search}&page=${newPage}&pageSize=${pageSize}`);
  }
  const handlePageSizeChange = (newSize) => {
    setPageSize(newSize);
    navigate(`?tab=${loaderData.tab}&search=${loaderData.search || ""}&page=0&pageSize=${newSize}`);
  };
  const renderStepContent = (step) => {
    switch (step) {
      case 1:
        return /* @__PURE__ */ jsxRuntimeExports.jsx(StepOne$1, {
          formData: formDataState,
          onChange: handleFormDataChange,
          errors: actionData == null ? void 0 : actionData.errors,
          mode,
          renderRETInput
        });
      case 2:
        return /* @__PURE__ */ jsxRuntimeExports.jsx(StepTwo$1, {
          formData: formDataState,
          onChange: handleFormDataChange,
          errors: actionData == null ? void 0 : actionData.errors,
          mode,
          renderRETInput
        });
      case 3:
        return /* @__PURE__ */ jsxRuntimeExports.jsx(StepThree$1, {
          formData: formDataState,
          onChange: handleFormDataChange,
          errors: actionData == null ? void 0 : actionData.errors,
          mode,
          renderRETInput
        });
      default:
        return null;
    }
  };
  const renderRETInput = reactExports.useCallback((field) => {
    var _a2, _b2;
    const RET10INPUTNAMES = ["brandName", "translations", "type", "unit", "minimumOrderQty", "maximumOrderQty", "maxAvailableQty", "incrementOrderQty", "weightFactor", "productId", "gstEligible", "groupId"];
    const RET11INPUTNAMES = ["description", "diet", "groupId"];
    if (((_a2 = formDataState == null ? void 0 : formDataState.itemConfig) == null ? void 0 : _a2.ondcDomain) === "RET10") {
      return RET10INPUTNAMES.includes(field);
    }
    if (((_b2 = formDataState == null ? void 0 : formDataState.itemConfig) == null ? void 0 : _b2.ondcDomain) === "RET11") {
      return RET11INPUTNAMES.includes(field);
    }
    return false;
  }, [(_b = formDataState == null ? void 0 : formDataState.itemConfig) == null ? void 0 : _b.ondcDomain]);
  reactExports.useEffect(() => {
    var _a2;
    if (((_a2 = formDataState == null ? void 0 : formDataState.itemConfig) == null ? void 0 : _a2.ondcDomain) === "RET11" && mode === "create") {
      setFormDataState(getInitialRestaurantFormState());
    }
  }, [(_c = formDataState == null ? void 0 : formDataState.itemConfig) == null ? void 0 : _c.ondcDomain]);
  const [validationErrors, setValidationErrors] = reactExports.useState({});
  const [isCurrentStepValid, setIsCurrentStepValid] = reactExports.useState(false);
  const validateCurrentStep = reactExports.useCallback(() => {
    try {
      switch (currentStep) {
        case 1:
          stepOneSchema.parse({
            ondcDomain: formDataState.itemConfig.ondcDomain,
            itemName: formDataState.itemName,
            brandName: formDataState.brandName,
            groupId: formDataState.groupId
          });
          setValidationErrors({});
          return true;
        case 2:
          stepTwoSchema.parse({
            ondcDomain: formDataState.itemConfig.ondcDomain,
            images: formDataState.images,
            translations: formDataState.translations,
            searchTags: formDataState.searchTags,
            assignedCategories: formDataState.assignedCategories
          });
          setValidationErrors({});
          return true;
        case 3:
          stepThreeSchema.parse({
            itemConfig: formDataState.itemConfig
          });
          setValidationErrors({});
          return true;
        default:
          return false;
      }
    } catch (error) {
      if (error instanceof z.ZodError) {
        const fieldErrors = {};
        Object.entries(error.formErrors.fieldErrors).forEach(([key, value]) => {
          fieldErrors[key] = value || [];
        });
        setValidationErrors(fieldErrors);
        return false;
      }
      return false;
    }
  }, [currentStep, formDataState]);
  reactExports.useEffect(() => {
    const isValid2 = validateCurrentStep();
    setIsCurrentStepValid(isValid2);
  }, [validateCurrentStep]);
  const goNext = () => {
    if (isCurrentStepValid) {
      setCurrentStep((step) => step < 3 ? step + 1 : step);
    }
  };
  const goBack = () => setCurrentStep((step) => step > 1 ? step - 1 : step);
  const handleSubmit = (e) => {
    e.preventDefault();
    if (!isCurrentStepValid) {
      return;
    }
    const formData = new FormData();
    const apiRequest = formStateToMasterItemRequest(formDataState, mode === "edit" && selectedItem ? selectedItem : void 0);
    console.log(apiRequest, "pppppppppppppp");
    formData.set("requestData", JSON.stringify(apiRequest));
    formData.set("_intent", mode);
    if (mode === "edit" && selectedItem) {
      formData.set("itemId", selectedItem.id.toString());
    }
    fetcher.submit(formData, {
      method: "post"
    });
  };
  reactExports.useEffect(() => {
    var _a2;
    if ((_a2 = fetcher.data) == null ? void 0 : _a2.errors) {
      const fieldErrors = {};
      Object.entries(fetcher.data.errors).forEach(([key, value]) => {
        fieldErrors[key] = value || [];
      });
      setValidationErrors(fieldErrors);
    }
  }, [fetcher.data]);
  console.log("actionData", actionData);
  return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
    className: "p-6",
    children: [/* @__PURE__ */ jsxRuntimeExports.jsx("h1", {
      className: "text-xl font-bold mb-4",
      children: "Master Items"
    }), /* @__PURE__ */ jsxRuntimeExports.jsxs(Tabs, {
      value: loaderData.tab,
      onValueChange: handleTabChange,
      children: [/* @__PURE__ */ jsxRuntimeExports.jsxs(TabsList, {
        className: "mb-4",
        children: [/* @__PURE__ */ jsxRuntimeExports.jsx(TabsTrigger, {
          value: "b2b",
          children: "B2B"
        }), /* @__PURE__ */ jsxRuntimeExports.jsx(TabsTrigger, {
          value: "b2c",
          children: "B2C"
        })]
      }), /* @__PURE__ */ jsxRuntimeExports.jsx(TabsContent, {
        value: "b2b",
        children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
          className: "",
          children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
            className: "flex justify-between mb-4",
            children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Input, {
              placeholder: "Search Items (min. 3 characters)",
              value: searchInput,
              onChange: handleSearchChange,
              className: "w-72"
            }), /* @__PURE__ */ jsxRuntimeExports.jsx(PageSizeSelector, {
              value: pageSize,
              onValueChange: handlePageSizeChange
            })]
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(ItemTable, {
            items: loaderData.items || [],
            currentPage: loaderData.currentPage || 0,
            totalPages: loaderData.totalPages || 1,
            itemsPerPage: loaderData.itemsPerPage || 10,
            onPageChange: handlePageChange,
            onEditItem: handleEditItem,
            onDuplicateItem: handleDuplicateItem
          })]
        })
      }), /* @__PURE__ */ jsxRuntimeExports.jsx(TabsContent, {
        value: "b2c",
        children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
          className: "",
          children: [/* @__PURE__ */ jsxRuntimeExports.jsx("div", {
            className: "mb-4",
            children: /* @__PURE__ */ jsxRuntimeExports.jsx(Input, {
              placeholder: "Search Items (min. 3 characters)",
              value: searchInput,
              onChange: handleSearchChange,
              className: "w-72"
            })
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(ItemTable, {
            items: loaderData.items || [],
            currentPage: loaderData.currentPage || 0,
            totalPages: loaderData.totalPages || 1,
            itemsPerPage: loaderData.itemsPerPage || 10,
            onPageChange: handlePageChange,
            onEditItem: handleEditItem,
            onDuplicateItem: handleDuplicateItem
          })]
        })
      })]
    }), /* @__PURE__ */ jsxRuntimeExports.jsxs(Dialog, {
      open: openDialog,
      onOpenChange: handleDialogChange,
      children: [/* @__PURE__ */ jsxRuntimeExports.jsx(DialogTrigger, {
        asChild: true,
        children: /* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
          className: "fixed bottom-5 right-5 rounded-full",
          children: "+ Add Item"
        })
      }), /* @__PURE__ */ jsxRuntimeExports.jsxs(DialogContent, {
        className: "max-w-[95vw] w-full md:max-w-2xl h-[90vh] flex flex-col",
        "aria-describedby": "dialog-description",
        onInteractOutside: (e) => {
          const target = e.target;
          if (target.closest('[role="listbox"], [role="combobox"]')) {
            e.preventDefault();
          }
        },
        children: [/* @__PURE__ */ jsxRuntimeExports.jsx(DialogHeader, {
          children: /* @__PURE__ */ jsxRuntimeExports.jsxs("h2", {
            className: "text-lg font-semibold",
            children: [mode === "edit" ? "Edit" : mode === "duplicate" ? "Duplicate" : "Add", " Master Item"]
          })
        }), /* @__PURE__ */ jsxRuntimeExports.jsxs(DialogTitle, {
          children: ["Step ", currentStep, ": ", getStepName(currentStep)]
        }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
          id: "dialog-description",
          className: "sr-only",
          children: [mode === "edit" ? "Edit existing" : mode === "duplicate" ? "Create duplicate of" : "Add new", " master item using a ", currentStep, "-step form process"]
        }), /* @__PURE__ */ jsxRuntimeExports.jsxs("form", {
          onSubmit: handleSubmit,
          className: "flex flex-col flex-grow overflow-y-auto",
          children: [/* @__PURE__ */ jsxRuntimeExports.jsx("input", {
            type: "hidden",
            name: "_intent",
            value: mode
          }), mode === "edit" && /* @__PURE__ */ jsxRuntimeExports.jsx("input", {
            type: "hidden",
            name: "itemId",
            value: selectedItem == null ? void 0 : selectedItem.id
          }), validationErrors && Object.values(validationErrors).flat().length > 0 && /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
            className: "bg-red-50 border border-red-200 text-sm text-red-700 px-4 py-2 rounded mb-2",
            children: Object.values(validationErrors).flat().map((error, index) => /* @__PURE__ */ jsxRuntimeExports.jsx("p", {
              children: error
            }, index))
          }), /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
            className: "flex-grow overflow-y-auto",
            children: renderStepContent(currentStep)
          }), /* @__PURE__ */ jsxRuntimeExports.jsxs(DialogFooter, {
            className: "mt-4 border-t pt-4",
            children: [currentStep > 1 && /* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
              type: "button",
              variant: "outline",
              onClick: goBack,
              children: "Back"
            }), currentStep < 3 && /* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
              type: "button",
              onClick: goNext,
              disabled: !isCurrentStepValid,
              children: "Next"
            }), currentStep === 3 && /* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
              type: "submit",
              disabled: fetcher.state === "submitting" || !isCurrentStepValid,
              children: fetcher.state === "submitting" ? "Submitting..." : "Submit"
            })]
          })]
        })]
      })]
    })]
  });
}
function getStepName(step) {
  switch (step) {
    case 1:
      return "Item Name";
    case 2:
      return "Item Details";
    case 3:
      return "Item Configuration";
    default:
      return "";
  }
}
export {
  MasterItemsRoute as default
};
//# sourceMappingURL=home.masterItems-CMhAaF48.js.map
