{"version": 3, "file": "login-Bz9xXZlj.js", "sources": ["../../../app/routes/login.tsx"], "sourcesContent": ["import { useState, useEffect } from 'react'\r\nimport { Form, useActionData, useLoaderData, useLocation, useNavigation, useSubmit } from '@remix-run/react'\r\nimport { User } from \"~/types\"\r\nimport { ArrowLeft } from \"lucide-react\"\r\nimport { <PERSON>, CardContent, CardHeader, CardTitle } from \"@components/ui/card\"\r\nimport { Button } from \"@components/ui/button\"\r\nimport { Input } from \"@components/ui/input\"\r\nimport { Label } from \"@components/ui/label\"\r\nimport { ActionFunction, json, LoaderFunction, redirect, TypedResponse } from \"@remix-run/node\";\r\nimport { getDeviceInfo, requestOtp, verifyOtp } from \"@services/auth.server\";\r\nimport { commitSession, getSession } from \"@utils/session.server\";\r\n\r\ninterface ActionData {\r\n    success: boolean;\r\n    message?: string;\r\n    user?: User;\r\n    intent?: string;\r\n}\r\n\r\ninterface LoaderData {\r\n    success: boolean;\r\n    message?: string;\r\n    domain?: string;\r\n}\r\n\r\nexport const loader: LoaderFunction = async ({ request }): Promise<LoaderData> => {\r\n\r\n    const url = new URL(request.url);\r\n    const domain = url.hostname;\r\n\r\n    return {\r\n        success: true,\r\n        domain,\r\n    }\r\n}\r\n\r\nexport const action: ActionFunction = async ({\r\n    request\r\n}): Promise<TypedResponse<ActionData>> => {\r\n    try {\r\n        const formData = await request.formData();\r\n        const intent = formData.get('intent') as string;\r\n        const phoneNumber = formData.get('phoneNumber') as string;\r\n        const otp = formData.get('otp') as string;\r\n        let accessToken = formData.get('access_token') as string;\r\n        let refreshToken = formData.get('refresh_token') as string;\r\n\r\n        // Get the returnTo URL from the form data\r\n        const returnTo = formData.get('returnTo') as string;\r\n\r\n        if (!intent) {\r\n            return json<ActionData>(\r\n                {\r\n                    success: false,\r\n                    message: 'Intent is required'\r\n                },\r\n                { status: 400 }\r\n            );\r\n        }\r\n\r\n        const deviceInfo = getDeviceInfo();\r\n\r\n        switch (intent) {\r\n            case 'getOtp': {\r\n                const payload = {\r\n                    app: 'seller_app',\r\n                    mobileNumber: phoneNumber,\r\n                    password: '',\r\n                    admin: false,\r\n                    deviceInfo,\r\n                };\r\n\r\n                await requestOtp(payload);\r\n\r\n                return json<ActionData>({\r\n                    success: true,\r\n                    message: 'OTP sent successfully',\r\n                    intent: 'getOtp'\r\n                });\r\n            }\r\n\r\n            case 'verifyOtp': {\r\n\r\n\r\n                if (!accessToken || !refreshToken) {\r\n                    if (!phoneNumber || !otp) {\r\n                        return json<ActionData>(\r\n                            {\r\n                                success: false,\r\n                                message: 'Phone number and OTP are required',\r\n                                intent: 'verifyOtp',\r\n                            },\r\n                            { status: 400 }\r\n                        );\r\n                    }\r\n\r\n                    const verifyResponse = await verifyOtp(phoneNumber, otp);\r\n                    // Use tokens from local storage if available, otherwise use from verify response\r\n                    accessToken = verifyResponse.data?.access_token ?? '';\r\n                    refreshToken = verifyResponse.data?.refresh_token ?? '';\r\n                }\r\n\r\n\r\n                const session = await getSession(request.headers.get('Cookie'));\r\n\r\n\r\n\r\n                if (!accessToken || !refreshToken) {\r\n                    throw new Error('Invalid authentication response');\r\n                }\r\n\r\n                session.set('access_token', accessToken);\r\n                session.set('refresh_token', refreshToken);\r\n\r\n                const headers = new Headers();\r\n                const cookieString = await commitSession(session);\r\n                headers.set('Set-Cookie', cookieString);\r\n\r\n                // Redirect to the return URL if it exists, otherwise to dashboard\r\n                const redirectTo = returnTo ? decodeURIComponent(returnTo) : '/home/<USER>';\r\n                return redirect(redirectTo, {\r\n                    headers\r\n                });\r\n            }\r\n\r\n            case 'clearActionData': {\r\n                return json<ActionData>({\r\n                    success: true,\r\n                    message: '',\r\n                    intent: 'clearActionData'\r\n                });\r\n            }\r\n\r\n            default: {\r\n                return json<ActionData>(\r\n                    {\r\n                        success: false,\r\n                        message: 'Invalid intent'\r\n                    },\r\n                    { status: 400 }\r\n                );\r\n            }\r\n        }\r\n    } catch (error) {\r\n        console.error('Authentication error:', error);\r\n        return json<ActionData>(\r\n            {\r\n                success: false,\r\n                message: error instanceof Error ? error.message : 'Authentication failed'\r\n            },\r\n            { status: 500 }\r\n        );\r\n    }\r\n};\r\n\r\nexport default function Login() {\r\n    const loader = useLoaderData<LoaderData>();\r\n\r\n    const [step, setStep] = useState('phone')\r\n    const [phoneNumber, setPhoneNumber] = useState('')\r\n    const [otp, setOtp] = useState('')\r\n    const [delay, setDelay] = useState(59)\r\n    const [isMnet, setMnet] = useState(false);\r\n\r\n\r\n    const location = useLocation();\r\n    const searchParams = new URLSearchParams(location.search);\r\n    const returnTo = searchParams.get('returnTo');\r\n\r\n    const actionData = useActionData()\r\n    const navigation = useNavigation()\r\n    const submit = useSubmit()\r\n\r\n\r\n    useEffect(() => {\r\n        // Get tokens from localStorage when component mounts\r\n        const accessToken = localStorage.getItem('access_token');\r\n        const refreshToken = localStorage.getItem('refresh_token');\r\n\r\n        if (accessToken && refreshToken) {\r\n            // Submit the tokens to the action\r\n            submit(\r\n                {\r\n                    intent: 'verifyOtp',\r\n                    phoneNumber,\r\n                    otp,\r\n                    access_token: accessToken,\r\n                    refresh_token: refreshToken,\r\n                    returnTo: returnTo\r\n                },\r\n                { method: 'post' }\r\n            );\r\n\r\n            // Clear tokens from localStorage\r\n            localStorage.removeItem('access_token');\r\n            localStorage.removeItem('refresh_token');\r\n        }\r\n    }, []);\r\n\r\n    useEffect(() => {\r\n        console.log(\"domain...\", loader?.domain);\r\n        if (loader?.domain && loader?.domain === \"farmersmandi.in\") {\r\n            setMnet(true);\r\n        }\r\n    }, [loader?.domain])\r\n\r\n\r\n\r\n\r\n\r\n    useEffect(() => {\r\n        let timer\r\n        if (step === 'otp' && delay > 0) {\r\n            timer = setTimeout(() => setDelay(delay - 1), 1000)\r\n        }\r\n        console.log(\"zazazaza Login useEffect 2: \", step, delay)\r\n        return () => clearTimeout(timer)\r\n    }, [step, delay])\r\n\r\n    const handleResendOtp = () => {\r\n        setOtp('')\r\n        setDelay(59)\r\n        setStep('otp')\r\n    }\r\n\r\n    useEffect(() => {\r\n        if (actionData?.success && actionData.intent === 'getOtp') {\r\n            setStep('otp')\r\n            setDelay(59)\r\n            console.log(\"zazazaza Login useEffect 1: \", actionData)\r\n        }\r\n    }, [actionData])\r\n\r\n    const handleBack = () => {\r\n        setStep('phone')\r\n        setOtp('')\r\n        submit({ intent: 'clearActionData' }, { method: 'post', replace: true })\r\n    }\r\n\r\n    return (\r\n        <div className=\"min-h-screen flex items-center justify-center bg-background p-4 relative overflow-hidden\">\r\n            {/* Grid Background with Fade */}\r\n            <div className=\"absolute inset-0 opacity-10\">\r\n                <div className=\"absolute inset-0 bg-gradient-to-b from-transparent via-background to-background z-10\" />\r\n                <div className=\"grid grid-cols-12 gap-4 h-full w-full\">\r\n                    {[...Array(144)].map((_, i) => (\r\n                        <div key={i} className=\"border-[0.5px] border-fm-green/20 h-full\" />\r\n                    ))}\r\n                </div>\r\n            </div>\r\n\r\n            <div className=\"w-full max-w-6xl relative z-20\">\r\n                {/* Main Login Section */}\r\n                <div className={`grid ${isMnet ? \"md:grid-cols-2 gap-8\" : \"\"} min-h-[80vh]`}>\r\n                    {/* Left Side - Features */}\r\n                    {isMnet && <Card\r\n                        className=\"bg-gradient-to-br from-fm-green/5 to-transparent backdrop-blur-sm border-fm-green/20 flex flex-col\">\r\n                        <CardHeader className=\"flex-none\">\r\n                            <div className=\"flex items-center gap-4 mb-6\">\r\n                                <div>\r\n                                    <div className=\"text-xl font-semibold text-fm-green\">farmersMandi</div>\r\n                                    <div className=\"text-xs text-gray-500\">by Brih Solutions</div>\r\n                                </div>\r\n                                <div className=\"h-8 w-[1px] bg-gray-300\" />\r\n                                <img src=\"/mnet-logo.svg\" alt=\"mNet Logo\" className=\"h-8\" />\r\n                            </div>\r\n                            <CardTitle className=\"text-2xl font-medium mb-8\">Helping businesses launch their own\r\n                                e-commerce app</CardTitle>\r\n                        </CardHeader>\r\n                        <CardContent className=\"flex-grow flex flex-col justify-between\">\r\n                            <ul className=\"space-y-6\">\r\n                                <li className=\"flex items-center gap-4\">\r\n                                    <div className=\"bg-fm-green/10 p-3 rounded-full\">\r\n                                        <svg className=\"w-6 h-6 text-fm-green\" fill=\"none\" strokeLinecap=\"round\"\r\n                                            strokeLinejoin=\"round\" strokeWidth=\"2\" viewBox=\"0 0 24 24\"\r\n                                            stroke=\"currentColor\">\r\n                                            <path d=\"M5 13l4 4L19 7\" />\r\n                                        </svg>\r\n                                    </div>\r\n                                    <span className=\"text-base\">Launch your own app within a week</span>\r\n                                </li>\r\n                                <li className=\"flex items-center gap-4\">\r\n                                    <div className=\"bg-fm-green/10 p-3 rounded-full\">\r\n                                        <svg className=\"w-6 h-6 text-fm-green\" fill=\"none\" strokeLinecap=\"round\"\r\n                                            strokeLinejoin=\"round\" strokeWidth=\"2\" viewBox=\"0 0 24 24\"\r\n                                            stroke=\"currentColor\">\r\n                                            <path d=\"M5 13l4 4L19 7\" />\r\n                                        </svg>\r\n                                    </div>\r\n                                    <span className=\"text-base\">Go online and increase your sales</span>\r\n                                </li>\r\n                                <li className=\"flex items-center gap-4\">\r\n                                    <div className=\"bg-fm-green/10 p-3 rounded-full\">\r\n                                        <svg className=\"w-6 h-6 text-fm-green\" fill=\"none\" strokeLinecap=\"round\"\r\n                                            strokeLinejoin=\"round\" strokeWidth=\"2\" viewBox=\"0 0 24 24\"\r\n                                            stroke=\"currentColor\">\r\n                                            <path d=\"M5 13l4 4L19 7\" />\r\n                                        </svg>\r\n                                    </div>\r\n                                    <span className=\"text-base\">Build your brand and gain trust</span>\r\n                                </li>\r\n                                <li className=\"flex items-center gap-4\">\r\n                                    <div className=\"bg-fm-green/10 p-3 rounded-full\">\r\n                                        <svg className=\"w-6 h-6 text-fm-green\" fill=\"none\" strokeLinecap=\"round\"\r\n                                            strokeLinejoin=\"round\" strokeWidth=\"2\" viewBox=\"0 0 24 24\"\r\n                                            stroke=\"currentColor\">\r\n                                            <path d=\"M5 13l4 4L19 7\" />\r\n                                        </svg>\r\n                                    </div>\r\n                                    <span className=\"text-base\">Streamline customer communications</span>\r\n                                </li>\r\n                            </ul>\r\n\r\n\r\n                            <div className=\"pt-8 border-t border-gray-200 mt-12\">\r\n                                <h3 className=\"text-base font-medium mb-6\">Trusted by businesses across India</h3>\r\n                                <div className=\"grid grid-cols-4 gap-6\">\r\n                                    {[...Array(8)].map((_, index) => (\r\n                                        <div key={index}\r\n                                            className=\"bg-white p-4 rounded-full w-24 h-24 flex items-center justify-center shadow-sm hover:shadow-md transition-shadow\">\r\n                                            <img\r\n                                                src={`/customer${index + 1}.png?height=60&width=60`}\r\n                                                alt={`Customer logo ${index + 1}`}\r\n                                                className=\"w-16 h-16 object-contain\"\r\n                                            />\r\n                                        </div>\r\n                                    ))}\r\n                                </div>\r\n                            </div>\r\n                        </CardContent>\r\n                    </Card>\r\n                    }\r\n                    {/* Right Side - Login Form */}\r\n                    <div className=\"flex items-center justify-center\">\r\n                        <Card className=\"backdrop-blur-sm border-fm-green/20 w-full max-w-md shadow-lg\">\r\n                            <CardHeader className=\"text-center pb-4\">\r\n                                <CardTitle className=\"text-xl font-bold\">Login to mNet Business Console</CardTitle>\r\n                                {isMnet && <p className=\"text-sm text-gray-500 mt-1\">Your fruit & vegetable Mandi online</p>}\r\n                            </CardHeader>\r\n                            <CardContent>\r\n                                <Form method=\"post\" className=\"space-y-6\">\r\n                                    {step === 'phone' ? (\r\n                                        <>\r\n                                            <div className=\"space-y-2\">\r\n                                                <Label htmlFor=\"phoneNumber\">Phone Number</Label>\r\n                                                <div className=\"flex rounded-md shadow-sm\">\r\n                                                    <span\r\n                                                        className=\"inline-flex items-center rounded-l-md border border-r-0 border-input bg-muted px-3 text-sm text-muted-foreground\">\r\n                                                        +91\r\n                                                    </span>\r\n                                                    <Input\r\n                                                        type=\"tel\"\r\n                                                        id=\"phoneNumber\"\r\n                                                        name=\"phoneNumber\"\r\n                                                        value={phoneNumber}\r\n                                                        onChange={(e) => setPhoneNumber(e.target.value)}\r\n                                                        maxLength={10}\r\n                                                        required\r\n                                                        className=\"rounded-l-none\"\r\n                                                    />\r\n                                                </div>\r\n                                                <p className=\"text-sm text-muted-foreground\">We'll send you an OTP on\r\n                                                    this number</p>\r\n                                            </div>\r\n                                            <Button\r\n                                                type=\"submit\"\r\n                                                name=\"intent\"\r\n                                                value=\"getOtp\"\r\n                                                disabled={phoneNumber.length < 10 || navigation.state === 'submitting'}\r\n                                                className=\"w-full bg-fm-green hover:bg-fm-hover\"\r\n                                            >\r\n                                                {navigation.state === 'submitting' ? 'Sending...' : 'GET OTP'}\r\n                                            </Button>\r\n                                        </>\r\n                                    ) : (\r\n                                        <>\r\n                                            <input type=\"hidden\" name=\"phoneNumber\" value={phoneNumber} />\r\n                                            <div className=\"space-y-2\">\r\n                                                <div className=\"flex items-center\">\r\n                                                    <Button\r\n                                                        type=\"button\"\r\n                                                        onClick={handleBack}\r\n                                                        variant=\"ghost\"\r\n                                                        size=\"sm\"\r\n                                                        className=\"mr-2\"\r\n                                                    >\r\n                                                        <ArrowLeft size={16} />\r\n                                                    </Button>\r\n                                                    <Label htmlFor=\"otp\">Enter OTP</Label>\r\n                                                </div>\r\n                                                <Input\r\n                                                    type=\"number\"\r\n                                                    id=\"otp\"\r\n                                                    name=\"otp\"\r\n                                                    value={otp}\r\n                                                    onChange={(e) => {\r\n                                                        const value = e.target.value.replace(/\\D/g, \"\"); // Remove non-numeric characters\r\n\r\n                                                        if (value.length <= 6) {\r\n\r\n                                                            setOtp(e.target.value)\r\n                                                        }\r\n                                                    }\r\n                                                    }\r\n                                                    maxLength={6}\r\n                                                    required\r\n                                                />\r\n                                                <p className=\"text-sm text-muted-foreground\">\r\n                                                    Didn't receive the OTP?{' '}\r\n                                                    <Button\r\n                                                        type=\"button\"\r\n                                                        onClick={handleResendOtp}\r\n                                                        disabled={delay > 0 || navigation.state === 'submitting'}\r\n                                                        variant=\"link\"\r\n                                                        className=\"p-0 h-auto font-normal text-fm-green\"\r\n                                                    >\r\n                                                        Resend OTP {delay > 0 && `in ${delay}s`}\r\n                                                    </Button>\r\n                                                </p>\r\n                                            </div>\r\n                                            <Button\r\n                                                type=\"submit\"\r\n                                                name=\"intent\"\r\n                                                value=\"verifyOtp\"\r\n                                                disabled={otp.length < 6 || navigation.state === 'submitting'}\r\n                                                className=\"w-full bg-fm-green hover:bg-fm-hover\"\r\n                                            >\r\n                                                {navigation.state === 'submitting' ? 'Verifying...' : 'CONTINUE'}\r\n                                            </Button>\r\n                                        </>\r\n                                    )}\r\n\r\n                                    {returnTo && <input type=\"hidden\" name=\"returnTo\" value={returnTo} />}\r\n                                </Form>\r\n\r\n                                {actionData?.message && actionData.intent !== 'clearActionData' && (\r\n                                    <p className={`mt-4 text-sm ${actionData.success ? 'text-green-600' : 'text-destructive'}`}>\r\n                                        {actionData.message}\r\n                                    </p>\r\n                                )}\r\n                            </CardContent>\r\n                        </Card>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    )\r\n}\r\n"], "names": ["<PERSON><PERSON>", "loader", "useLoaderData", "step", "setStep", "useState", "phoneNumber", "setPhoneNumber", "otp", "setOtp", "delay", "<PERSON><PERSON><PERSON><PERSON>", "isMnet", "setMnet", "location", "useLocation", "searchParams", "URLSearchParams", "search", "returnTo", "get", "actionData", "useActionData", "navigation", "useNavigation", "submit", "useSubmit", "useEffect", "accessToken", "localStorage", "getItem", "refreshToken", "intent", "access_token", "refresh_token", "method", "removeItem", "console", "log", "domain", "timer", "setTimeout", "clearTimeout", "handleResendOtp", "success", "handleBack", "replace", "jsxs", "className", "children", "jsx", "Array", "map", "_", "i", "Card", "<PERSON><PERSON><PERSON><PERSON>", "src", "alt", "CardTitle", "<PERSON><PERSON><PERSON><PERSON>", "fill", "strokeLinecap", "strokeLinejoin", "strokeWidth", "viewBox", "stroke", "d", "index", "Form", "Fragment", "Label", "htmlFor", "Input", "type", "id", "name", "value", "onChange", "e", "target", "max<PERSON><PERSON><PERSON>", "required", "<PERSON><PERSON>", "disabled", "length", "state", "onClick", "variant", "size", "ArrowLeft", "message"], "mappings": ";;;;;;;;;;;;;;AA2JA,SAAwBA,QAAQ;AAC5B,QAAMC,UAASC,cAA0B;AAEzC,QAAM,CAACC,MAAMC,OAAO,IAAIC,aAAAA,SAAS,OAAO;AACxC,QAAM,CAACC,aAAaC,cAAc,IAAIF,aAAAA,SAAS,EAAE;AACjD,QAAM,CAACG,KAAKC,MAAM,IAAIJ,aAAAA,SAAS,EAAE;AACjC,QAAM,CAACK,OAAOC,QAAQ,IAAIN,aAAAA,SAAS,EAAE;AACrC,QAAM,CAACO,QAAQC,OAAO,IAAIR,aAAAA,SAAS,KAAK;AAGxC,QAAMS,WAAWC,YAAY;AAC7B,QAAMC,eAAe,IAAIC,gBAAgBH,SAASI,MAAM;AAClD,QAAAC,WAAWH,aAAaI,IAAI,UAAU;AAE5C,QAAMC,aAAaC,cAAc;AACjC,QAAMC,aAAaC,cAAc;AACjC,QAAMC,SAASC,UAAU;AAGzBC,eAAAA,UAAU,MAAM;AAEN,UAAAC,cAAcC,aAAaC,QAAQ,cAAc;AACjD,UAAAC,eAAeF,aAAaC,QAAQ,eAAe;AAEzD,QAAIF,eAAeG,cAAc;AAE7BN,aACI;AAAA,QACIO,QAAQ;AAAA,QACR1B;AAAAA,QACAE;AAAAA,QACAyB,cAAcL;AAAAA,QACdM,eAAeH;AAAAA,QACfZ;AAAAA,MACJ,GACA;AAAA,QAAEgB,QAAQ;AAAA,MAAO,CACrB;AAGAN,mBAAaO,WAAW,cAAc;AACtCP,mBAAaO,WAAW,eAAe;AAAA,IAC3C;AAAA,EACJ,GAAG,EAAE;AAELT,eAAAA,UAAU,MAAM;AACJU,YAAAC,IAAI,aAAarC,mCAAQsC,MAAM;AACvC,SAAItC,mCAAQsC,YAAUtC,mCAAQsC,YAAW,mBAAmB;AACxD1B,cAAQ,IAAI;AAAA,IAChB;AAAA,EACJ,GAAG,CAACZ,mCAAQsC,MAAM,CAAC;AAMnBZ,eAAAA,UAAU,MAAM;AACR,QAAAa;AACA,QAAArC,SAAS,SAASO,QAAQ,GAAG;AAC7B8B,cAAQC,WAAW,MAAM9B,SAASD,QAAQ,CAAC,GAAG,GAAI;AAAA,IACtD;AACQ2B,YAAAC,IAAI,gCAAgCnC,MAAMO,KAAK;AAChD,WAAA,MAAMgC,aAAaF,KAAK;AAAA,EACnC,GAAG,CAACrC,MAAMO,KAAK,CAAC;AAEhB,QAAMiC,kBAAkBA,MAAM;AAC1BlC,WAAO,EAAE;AACTE,aAAS,EAAE;AACXP,YAAQ,KAAK;AAAA,EACjB;AAEAuB,eAAAA,UAAU,MAAM;AACZ,SAAIN,yCAAYuB,YAAWvB,WAAWW,WAAW,UAAU;AACvD5B,cAAQ,KAAK;AACbO,eAAS,EAAE;AACH0B,cAAAC,IAAI,gCAAgCjB,UAAU;AAAA,IAC1D;AAAA,EACJ,GAAG,CAACA,UAAU,CAAC;AAEf,QAAMwB,aAAaA,MAAM;AACrBzC,YAAQ,OAAO;AACfK,WAAO,EAAE;AACFgB,WAAA;AAAA,MAAEO,QAAQ;AAAA,IAAkB,GAAG;AAAA,MAAEG,QAAQ;AAAA,MAAQW,SAAS;AAAA,IAAK,CAAC;AAAA,EAC3E;AAGI,SAAAC,kCAAAA,KAAC,OAAI;AAAA,IAAAC,WAAU;AAAA,IAEXC,UAAA,CAACF,kCAAA,KAAA,OAAA;AAAA,MAAIC,WAAU;AAAA,MACXC,UAAA,CAACC,kCAAA,IAAA,OAAA;AAAA,QAAIF,WAAU;AAAA,MAAuF,CAAA,GACtGE,kCAAA,IAAC;QAAIF,WAAU;AAAA,QACVC,WAAC,GAAGE,MAAM,GAAG,CAAC,EAAEC,IAAI,CAACC,GAAGC,MACpBJ,kCAAA,IAAA,OAAA;AAAA,UAAYF,WAAU;AAAA,QAAb,GAAAM,CAAwD,CACrE;AAAA,MACL,CAAA,CAAA;AAAA,IACJ,CAAA,GAEAJ,kCAAA,IAAC,OAAI;AAAA,MAAAF,WAAU;AAAA,MAEXC,UAAAF,kCAAA,KAAC,OAAI;AAAA,QAAAC,WAAW,QAAQpC,SAAS,yBAAyB,EAAE;AAAA,QAEvDqC,UAAA,CAAUrC,UAAAmC,kCAAA,KAACQ,MAAA;AAAA,UACRP,WAAU;AAAA,UACVC,UAAA,CAACF,kCAAA,KAAAS,YAAA;AAAA,YAAWR,WAAU;AAAA,YAClBC,UAAA,CAACF,kCAAA,KAAA,OAAA;AAAA,cAAIC,WAAU;AAAA,cACXC,UAAA,CAAAF,kCAAA,KAAC,OACG;AAAA,gBAAAE,UAAA,CAACC,kCAAA,IAAA,OAAA;AAAA,kBAAIF,WAAU;AAAA,kBAAsCC,UAAY;AAAA,gBAAA,CAAA,GAChEC,kCAAA,IAAA,OAAA;AAAA,kBAAIF,WAAU;AAAA,kBAAwBC,UAAiB;AAAA,gBAAA,CAAA,CAAA;AAAA,cAC5D,CAAA,GACAC,kCAAA,IAAC,OAAI;AAAA,gBAAAF,WAAU;AAAA,cAA0B,CAAA,yCACxC,OAAI;AAAA,gBAAAS,KAAI;AAAA,gBAAiBC,KAAI;AAAA,gBAAYV,WAAU;AAAA,cAAM,CAAA,CAAA;AAAA,YAC9D,CAAA,GACCE,kCAAA,IAAAS,WAAA;AAAA,cAAUX,WAAU;AAAA,cAA4BC,UAC/B;AAAA,YAAA,CAAA,CAAA;AAAA,UACtB,CAAA,GACAF,kCAAA,KAACa,aAAY;AAAA,YAAAZ,WAAU;AAAA,YACnBC,UAAA,CAACF,kCAAA,KAAA,MAAA;AAAA,cAAGC,WAAU;AAAA,cACVC,UAAA,CAACF,kCAAA,KAAA,MAAA;AAAA,gBAAGC,WAAU;AAAA,gBACVC,UAAA,CAACC,kCAAA,IAAA,OAAA;AAAA,kBAAIF,WAAU;AAAA,kBACXC,UAAAC,kCAAA,IAAC,OAAA;AAAA,oBAAIF,WAAU;AAAA,oBAAwBa,MAAK;AAAA,oBAAOC,eAAc;AAAA,oBAC7DC,gBAAe;AAAA,oBAAQC,aAAY;AAAA,oBAAIC,SAAQ;AAAA,oBAC/CC,QAAO;AAAA,oBACPjB,UAAAC,kCAAA,IAAC,QAAK;AAAA,sBAAAiB,GAAE;AAAA,oBAAiB,CAAA;AAAA,kBAC7B,CAAA;AAAA,gBACJ,CAAA,GACCjB,kCAAA,IAAA,QAAA;AAAA,kBAAKF,WAAU;AAAA,kBAAYC,UAAiC;AAAA,gBAAA,CAAA,CAAA;AAAA,cACjE,CAAA,GACAF,kCAAA,KAAC,MAAG;AAAA,gBAAAC,WAAU;AAAA,gBACVC,UAAA,CAACC,kCAAA,IAAA,OAAA;AAAA,kBAAIF,WAAU;AAAA,kBACXC,UAAAC,kCAAA,IAAC,OAAA;AAAA,oBAAIF,WAAU;AAAA,oBAAwBa,MAAK;AAAA,oBAAOC,eAAc;AAAA,oBAC7DC,gBAAe;AAAA,oBAAQC,aAAY;AAAA,oBAAIC,SAAQ;AAAA,oBAC/CC,QAAO;AAAA,oBACPjB,UAAAC,kCAAA,IAAC,QAAK;AAAA,sBAAAiB,GAAE;AAAA,oBAAiB,CAAA;AAAA,kBAC7B,CAAA;AAAA,gBACJ,CAAA,GACCjB,kCAAA,IAAA,QAAA;AAAA,kBAAKF,WAAU;AAAA,kBAAYC,UAAiC;AAAA,gBAAA,CAAA,CAAA;AAAA,cACjE,CAAA,GACAF,kCAAA,KAAC,MAAG;AAAA,gBAAAC,WAAU;AAAA,gBACVC,UAAA,CAACC,kCAAA,IAAA,OAAA;AAAA,kBAAIF,WAAU;AAAA,kBACXC,UAAAC,kCAAA,IAAC,OAAA;AAAA,oBAAIF,WAAU;AAAA,oBAAwBa,MAAK;AAAA,oBAAOC,eAAc;AAAA,oBAC7DC,gBAAe;AAAA,oBAAQC,aAAY;AAAA,oBAAIC,SAAQ;AAAA,oBAC/CC,QAAO;AAAA,oBACPjB,UAAAC,kCAAA,IAAC,QAAK;AAAA,sBAAAiB,GAAE;AAAA,oBAAiB,CAAA;AAAA,kBAC7B,CAAA;AAAA,gBACJ,CAAA,GACCjB,kCAAA,IAAA,QAAA;AAAA,kBAAKF,WAAU;AAAA,kBAAYC,UAA+B;AAAA,gBAAA,CAAA,CAAA;AAAA,cAC/D,CAAA,GACAF,kCAAA,KAAC,MAAG;AAAA,gBAAAC,WAAU;AAAA,gBACVC,UAAA,CAACC,kCAAA,IAAA,OAAA;AAAA,kBAAIF,WAAU;AAAA,kBACXC,UAAAC,kCAAA,IAAC,OAAA;AAAA,oBAAIF,WAAU;AAAA,oBAAwBa,MAAK;AAAA,oBAAOC,eAAc;AAAA,oBAC7DC,gBAAe;AAAA,oBAAQC,aAAY;AAAA,oBAAIC,SAAQ;AAAA,oBAC/CC,QAAO;AAAA,oBACPjB,UAAAC,kCAAA,IAAC,QAAK;AAAA,sBAAAiB,GAAE;AAAA,oBAAiB,CAAA;AAAA,kBAC7B,CAAA;AAAA,gBACJ,CAAA,GACCjB,kCAAA,IAAA,QAAA;AAAA,kBAAKF,WAAU;AAAA,kBAAYC,UAAkC;AAAA,gBAAA,CAAA,CAAA;AAAA,cAClE,CAAA,CAAA;AAAA,YACJ,CAAA,GAGAF,kCAAA,KAAC,OAAI;AAAA,cAAAC,WAAU;AAAA,cACXC,UAAA,CAACC,kCAAA,IAAA,MAAA;AAAA,gBAAGF,WAAU;AAAA,gBAA6BC,UAAkC;AAAA,cAAA,CAAA,GAC5EC,kCAAA,IAAA,OAAA;AAAA,gBAAIF,WAAU;AAAA,gBACVC,UAAC,CAAA,GAAGE,MAAM,CAAC,CAAC,EAAEC,IAAI,CAACC,GAAGe,UACnBlB,kCAAA,IAAC,OAAA;AAAA,kBACGF,WAAU;AAAA,kBACVC,UAAAC,kCAAA,IAAC,OAAA;AAAA,oBACGO,KAAK,YAAYW,QAAQ,CAAC;AAAA,oBAC1BV,KAAK,iBAAiBU,QAAQ,CAAC;AAAA,oBAC/BpB,WAAU;AAAA,kBACd,CAAA;AAAA,gBAAA,GANMoB,KAOV,CACH;AAAA,cACL,CAAA,CAAA;AAAA,YACJ,CAAA,CAAA;AAAA,UACJ,CAAA,CAAA;AAAA,QAAA,CACJ,yCAGC,OAAI;AAAA,UAAApB,WAAU;AAAA,UACXC,UAACF,kCAAA,KAAAQ,MAAA;AAAA,YAAKP,WAAU;AAAA,YACZC,UAAA,CAACF,kCAAA,KAAAS,YAAA;AAAA,cAAWR,WAAU;AAAA,cAClBC,UAAA,CAACC,kCAAA,IAAAS,WAAA;AAAA,gBAAUX,WAAU;AAAA,gBAAoBC,UAA8B;AAAA,eAAA,GACtErC,UAAUsC,kCAAA,IAAC,KAAE;AAAA,gBAAAF,WAAU;AAAA,gBAA6BC,UAAmC;AAAA,cAAA,CAAA,CAAA;AAAA,YAC5F,CAAA,0CACCW,aACG;AAAA,cAAAX,UAAA,CAAAF,kCAAA,KAACsB,MAAK;AAAA,gBAAAlC,QAAO;AAAA,gBAAOa,WAAU;AAAA,gBACzBC,UAAA,CAAA9C,SAAS,UAEF4C,kCAAAA,KAAAuB,kBAAAA,UAAA;AAAA,kBAAArB,UAAA,CAACF,kCAAA,KAAA,OAAA;AAAA,oBAAIC,WAAU;AAAA,oBACXC,UAAA,CAACC,kCAAA,IAAAqB,OAAA;AAAA,sBAAMC,SAAQ;AAAA,sBAAcvB,UAAY;AAAA,oBAAA,CAAA,GACzCF,kCAAA,KAAC,OAAI;AAAA,sBAAAC,WAAU;AAAA,sBACXC,UAAA,CAAAC,kCAAA,IAAC,QAAA;AAAA,wBACGF,WAAU;AAAA,wBAAmHC,UAAA;AAAA,sBAAA,CAEjI,GACAC,kCAAA,IAACuB,OAAA;AAAA,wBACGC,MAAK;AAAA,wBACLC,IAAG;AAAA,wBACHC,MAAK;AAAA,wBACLC,OAAOvE;AAAAA,wBACPwE,UAAWC,OAAMxE,eAAewE,EAAEC,OAAOH,KAAK;AAAA,wBAC9CI,WAAW;AAAA,wBACXC,UAAQ;AAAA,wBACRlC,WAAU;AAAA,sBAAA,CACd,CAAA;AAAA,oBACJ,CAAA,GACCE,kCAAA,IAAA,KAAA;AAAA,sBAAEF,WAAU;AAAA,sBAAgCC,UAC9B;AAAA,oBAAA,CAAA,CAAA;AAAA,kBACnB,CAAA,GACAC,kCAAA,IAACiC,QAAA;AAAA,oBACGT,MAAK;AAAA,oBACLE,MAAK;AAAA,oBACLC,OAAM;AAAA,oBACNO,UAAU9E,YAAY+E,SAAS,MAAM9D,WAAW+D,UAAU;AAAA,oBAC1DtC,WAAU;AAAA,oBAETC,UAAA1B,WAAW+D,UAAU,eAAe,eAAe;AAAA,kBAAA,CACxD,CAAA;AAAA,gBAAA,CACJ,IAGIvC,kCAAA,KAAAuB,4BAAA;AAAA,kBAAArB,UAAA,CAAAC,kCAAA,IAAC;oBAAMwB,MAAK;AAAA,oBAASE,MAAK;AAAA,oBAAcC,OAAOvE;AAAAA,kBAAa,CAAA,GAC5DyC,kCAAA,KAAC,OAAI;AAAA,oBAAAC,WAAU;AAAA,oBACXC,UAAA,CAACF,kCAAA,KAAA,OAAA;AAAA,sBAAIC,WAAU;AAAA,sBACXC,UAAA,CAAAC,kCAAA,IAACiC,QAAA;AAAA,wBACGT,MAAK;AAAA,wBACLa,SAAS1C;AAAAA,wBACT2C,SAAQ;AAAA,wBACRC,MAAK;AAAA,wBACLzC,WAAU;AAAA,wBAEVC,UAAAC,kCAAA,IAACwC,WAAU;AAAA,0BAAAD,MAAM;AAAA,wBAAI,CAAA;AAAA,sBAAA,CACzB,GACCvC,kCAAA,IAAAqB,OAAA;AAAA,wBAAMC,SAAQ;AAAA,wBAAMvB,UAAS;AAAA,sBAAA,CAAA,CAAA;AAAA,oBAClC,CAAA,GACAC,kCAAA,IAACuB,OAAA;AAAA,sBACGC,MAAK;AAAA,sBACLC,IAAG;AAAA,sBACHC,MAAK;AAAA,sBACLC,OAAOrE;AAAAA,sBACPsE,UAAWC,OAAM;AACb,8BAAMF,QAAQE,EAAEC,OAAOH,MAAM/B,QAAQ,OAAO,EAAE;AAE1C,4BAAA+B,MAAMQ,UAAU,GAAG;AAEZ5E,iCAAAsE,EAAEC,OAAOH,KAAK;AAAA,wBACzB;AAAA,sBACJ;AAAA,sBAEAI,WAAW;AAAA,sBACXC,UAAQ;AAAA,oBAAA,CACZ,GACAnC,kCAAA,KAAC,KAAE;AAAA,sBAAAC,WAAU;AAAA,sBAAgCC,UAAA,CAAA,2BACjB,KACxBF,kCAAAA,KAACoC,QAAA;AAAA,wBACGT,MAAK;AAAA,wBACLa,SAAS5C;AAAAA,wBACTyC,UAAU1E,QAAQ,KAAKa,WAAW+D,UAAU;AAAA,wBAC5CE,SAAQ;AAAA,wBACRxC,WAAU;AAAA,wBACbC,UAAA,CAAA,eACevC,QAAQ,KAAK,MAAMA,KAAK,GAAA;AAAA,sBAAA,CACxC,CAAA;AAAA,oBACJ,CAAA,CAAA;AAAA,kBACJ,CAAA,GACAwC,kCAAA,IAACiC,QAAA;AAAA,oBACGT,MAAK;AAAA,oBACLE,MAAK;AAAA,oBACLC,OAAM;AAAA,oBACNO,UAAU5E,IAAI6E,SAAS,KAAK9D,WAAW+D,UAAU;AAAA,oBACjDtC,WAAU;AAAA,oBAETC,UAAA1B,WAAW+D,UAAU,eAAe,iBAAiB;AAAA,kBAAA,CAC1D,CAAA;AAAA,iBACJ,GAGHnE,kDAAa,SAAM;AAAA,kBAAAuD,MAAK;AAAA,kBAASE,MAAK;AAAA,kBAAWC,OAAO1D;AAAAA,gBAAU,CAAA,CAAA;AAAA,cACvE,CAAA,IAECE,yCAAYsE,YAAWtE,WAAWW,WAAW,qBACzCkB,kCAAA,IAAA,KAAA;AAAA,gBAAEF,WAAW,gBAAgB3B,WAAWuB,UAAU,mBAAmB,kBAAkB;AAAA,gBACnFK,qBAAW0C;AAAAA,cAChB,CAAA,CAAA;AAAA,YAER,CAAA,CAAA;AAAA,UACJ,CAAA;AAAA,QACJ,CAAA,CAAA;AAAA,MACJ,CAAA;AAAA,IACJ,CAAA,CAAA;AAAA,EACJ,CAAA;AAER;"}