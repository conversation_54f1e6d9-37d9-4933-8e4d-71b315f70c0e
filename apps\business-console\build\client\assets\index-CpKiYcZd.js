import { r as reactExports } from "./jsx-runtime-bB2y7OuD.js";
function usePrevious(value) {
  const ref = reactExports.useRef({ value, previous: value });
  return reactExports.useMemo(() => {
    if (ref.current.value !== value) {
      ref.current.previous = ref.current.value;
      ref.current.value = value;
    }
    return ref.current.previous;
  }, [value]);
}
export {
  usePrevious as u
};
//# sourceMappingURL=index-CpKiYcZd.js.map
