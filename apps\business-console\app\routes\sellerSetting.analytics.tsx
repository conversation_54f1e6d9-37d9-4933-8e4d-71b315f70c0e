import { useLoaderData } from "@remix-run/react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "../components/ui/tabs";
import { metabaseService } from "../utils/metabase";
import { withAuth, withResponse } from "../utils/auth-utils";

export const loader = withAuth(async ({ user }) => {
  const overviewUrl = metabaseService.generateDashboardUrl(11, {
    id: user.sellerId,
  });
  const itemsAnalyticsUrl = metabaseService.generateDashboardUrl(12, {
    id: user.sellerId,
  });
  
  return withResponse({ overviewUrl, itemsAnalyticsUrl });
});

export default function Analytics() {
  const { overviewUrl, itemsAnalyticsUrl } = useLoaderData<typeof loader>();

  return (
    <div className="p-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-gray-900">Analytics</h1>
        <p className="text-gray-600 mt-2">Detailed performance insights</p>
      </div>
      
      <Tabs defaultValue="overview" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="items-analytics">Items Analytics</TabsTrigger>
        </TabsList>
        
        <TabsContent value="overview" className="mt-6">
          <div className="w-full h-screen">
            {overviewUrl ? (
              <iframe
                id="metabase-overview-iframe"
                src={overviewUrl}
                title="Overview Dashboard"
                className="w-full h-full border-0"
              />
            ) : (
              <div className="flex items-center justify-center min-h-[400px]">
                <div className="text-center">
                  <div className="mx-auto mb-4 w-16 h-16 bg-gradient-to-r from-teal-500 to-green-600 rounded-full flex items-center justify-center">
                    <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                    </svg>
                  </div>
                  <h2 className="text-2xl font-bold mb-2">Overview Dashboard Loading</h2>
                  <p className="text-gray-500">
                    Loading your overview analytics...
                  </p>
                </div>
              </div>
            )}
          </div>
        </TabsContent>
        
        <TabsContent value="items-analytics" className="mt-6">
          <div className="w-full h-screen">
            {itemsAnalyticsUrl ? (
              <iframe
                id="metabase-items-iframe"
                src={itemsAnalyticsUrl}
                title="Items Analytics Dashboard"
                className="w-full h-full border-0"
              />
            ) : (
              <div className="flex items-center justify-center min-h-[400px]">
                <div className="text-center">
                  <div className="mx-auto mb-4 w-16 h-16 bg-gradient-to-r from-teal-500 to-green-600 rounded-full flex items-center justify-center">
                    <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                    </svg>
                  </div>
                  <h2 className="text-2xl font-bold mb-2">Items Analytics Loading</h2>
                  <p className="text-gray-500">
                    Loading your items analytics...
                  </p>
                </div>
              </div>
            )}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
} 