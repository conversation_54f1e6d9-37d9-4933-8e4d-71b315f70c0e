import { r as reactExports, j as jsxRuntimeExports } from "./jsx-runtime-bB2y7OuD.js";
import { B as Button } from "./button-ByAXMyvk.js";
import { C as Card } from "./card-BJQMSLe_.js";
import { I as Input } from "./input-3v87qohQ.js";
import { T as Table, a as TableHeader, b as TableRow, c as TableHead, d as TableBody, e as TableCell } from "./table-CAxdJZsY.js";
import { S as Switch } from "./switch-DQ_qW6ch.js";
import { T as Tabs, a as TabsList, b as TabsTrigger, c as TabsContent } from "./tabs-CfSdyzWr.js";
import { a as useNavigate } from "./index-DhHTcibu.js";
import { u as useLoaderData, d as useActionData, a as useFetcher } from "./components-D7UvGag_.js";
import { A as ArrowLeft } from "./arrow-left-D_Ztdrp5.js";
import "./index-z_byfFrQ.js";
import "./index-ImHKLo0a.js";
import "./utils-GkgzjW3c.js";
import "./index-D7VH9Fc8.js";
import "./index-dIGjFc0I.js";
import "./index-CpKiYcZd.js";
import "./index-CkL5tk39.js";
import "./index-CzZ6EYkN.js";
import "./index-Vp2vNLNM.js";
import "./index-CG37gmC0.js";
import "./index-qCtcpOcW.js";
import "./index-DVTNuYOr.js";
import "./index-BTdCMChR.js";
import "./index-QLGF6kQx.js";
import "./createLucideIcon-uwkRm45G.js";
function CommonItemCategoryList({ data, mItemCategoryList, addItem }) {
  const [searchTerm, setSearchTerm] = reactExports.useState("");
  const [openAddbtn, setOpenAddbtn] = reactExports.useState(false);
  const [selectedCategory, setSelectedCategory] = reactExports.useState("");
  return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "container mx-auto p-6", children: [
    /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex justify-between mb-4", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx(
        Input,
        {
          placeholder: "Search by Item Name",
          value: searchTerm,
          onChange: (e) => setSearchTerm(e.target.value),
          className: "max-w-sm"
        }
      ),
      /* @__PURE__ */ jsxRuntimeExports.jsx(Button, { onClick: () => setOpenAddbtn(true), size: "sm", children: "Add" })
    ] }),
    openAddbtn && /* @__PURE__ */ jsxRuntimeExports.jsx(Card, { className: "w-full md:w-1/2 max-w-xl p-6 border rounded-lg shadow-md space-y-6 bg-white ", children: /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "space-y-6", children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "my-4", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "flex items-center space-x-4", children: /* @__PURE__ */ jsxRuntimeExports.jsx("p", { className: "text-lg font-semibold text-gray-700", children: "Adding Network Category" }) }),
      /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center space-x-4 mt-6", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx("p", { className: "text-lg font-semibold text-gray-700", children: "Select Category :" }),
        /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "w-full sm:w-auto", children: /* @__PURE__ */ jsxRuntimeExports.jsxs(
          "select",
          {
            className: "border border-gray-300 p-2 rounded-md w-full sm:w-56 text-sm",
            value: selectedCategory,
            onChange: (e) => setSelectedCategory(e.target.value),
            children: [
              /* @__PURE__ */ jsxRuntimeExports.jsx("option", { value: "", children: "All Categories" }),
              mItemCategoryList == null ? void 0 : mItemCategoryList.map((category) => /* @__PURE__ */ jsxRuntimeExports.jsx("option", { value: category.id, children: category.name }, category.id))
            ]
          }
        ) })
      ] }),
      /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "  justify-center items-center my-4", children: /* @__PURE__ */ jsxRuntimeExports.jsx(Button, { className: "align-center", onClick: () => addItem == null ? void 0 : addItem(Number(selectedCategory)), children: "ADD" }) })
    ] }) }) }),
    openAddbtn == false && /* @__PURE__ */ jsxRuntimeExports.jsxs(Table, { children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx(TableHeader, { children: /* @__PURE__ */ jsxRuntimeExports.jsxs(TableRow, { children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, { className: "cursor-pointer", children: "Item Image" }),
        /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, { className: "cursor-pointer", children: "Item Name " }),
        /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, { className: "cursor-pointer", children: "picturex " }),
        /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, { className: "cursor-pointer", children: "picturexx " }),
        /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, { className: "cursor-pointer", children: "level " })
      ] }) }),
      /* @__PURE__ */ jsxRuntimeExports.jsx(TableBody, { children: data == null ? void 0 : data.filter((x) => x.name.toLowerCase().includes(searchTerm.toLowerCase())).sort((a, b) => a.name.localeCompare(b.name)).map((item) => {
        return /* @__PURE__ */ jsxRuntimeExports.jsxs(TableRow, { children: [
          /* @__PURE__ */ jsxRuntimeExports.jsxs(TableCell, { children: [
            " ",
            /* @__PURE__ */ jsxRuntimeExports.jsx(
              "img",
              {
                src: item == null ? void 0 : item.picture,
                alt: "ItemImage",
                className: "h-10 w-10"
              }
            )
          ] }),
          /* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, { children: item.name }),
          /* @__PURE__ */ jsxRuntimeExports.jsxs(TableCell, { children: [
            " ",
            /* @__PURE__ */ jsxRuntimeExports.jsx(
              "img",
              {
                src: item == null ? void 0 : item.picturex,
                alt: "ItemImage",
                className: "h-10 w-10"
              }
            )
          ] }),
          /* @__PURE__ */ jsxRuntimeExports.jsxs(TableCell, { children: [
            " ",
            /* @__PURE__ */ jsxRuntimeExports.jsx(
              "img",
              {
                src: item == null ? void 0 : item.picturexx,
                alt: "ItemImage",
                className: "h-10 w-10"
              }
            )
          ] }),
          /* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, { children: item.level })
        ] }, item.level);
      }) })
    ] })
  ] });
}
function SelectedNetworkCategory() {
  const navigate = useNavigate();
  const {
    data,
    netWorkId,
    name
  } = useLoaderData();
  const networkItemCategory = useActionData();
  const [selectedNetworkItemCategory, setSelectedNetworkItemCategory] = reactExports.useState(networkItemCategory == null ? void 0 : networkItemCategory.data);
  const [activeTab, setActiveTab] = reactExports.useState("config");
  const [networkName, setNetworkName] = reactExports.useState(data == null ? void 0 : data.name);
  const [managerName, setMangerName] = reactExports.useState(data == null ? void 0 : data.managerName);
  const [description, setDescription] = reactExports.useState(data == null ? void 0 : data.description);
  const fetcher = useFetcher();
  const [mItemCategoryList, setMItemCategoryList] = reactExports.useState();
  const [update, setUpdate] = reactExports.useState(false);
  const handleTabChange = (newTab) => {
    const formData = new FormData();
    setActiveTab(newTab);
    if (newTab === "netWorkItemDetails") {
      console.log("networkItemCateGoryCalled");
      formData.append("intent", "NetworkItemCategory");
      formData.append("netWorkId", netWorkId.toString());
      fetcher.submit(formData, {
        method: "POST"
      });
    }
  };
  const addNcItem = (categoryId) => {
    const formData = new FormData();
    formData.append("intent", "addingCategory");
    formData.append("categoryId", categoryId);
    formData.append("netWorkId", netWorkId);
    fetcher.submit(formData, {
      method: "POST"
    });
  };
  const handleUpdate = (attributetype, val, id, type) => {
    const formData = new FormData();
    formData.append("updateType", type);
    formData.append("netWorkId", netWorkId);
    formData.append("attribute", attributetype);
    formData.append("value", val);
    formData.append("name", name);
    fetcher.submit(formData, {
      method: "POST"
    });
  };
  reactExports.useEffect(() => {
    var _a;
    if (((_a = fetcher.data) == null ? void 0 : _a.data) && activeTab === "netWorkItemDetails") {
      setSelectedNetworkItemCategory(fetcher.data.data);
      setMItemCategoryList(fetcher.data.mCategories);
      console.log(networkItemCategory);
    }
  }, [fetcher.data, activeTab]);
  reactExports.useEffect(() => {
    var _a;
    if (((_a = fetcher.data) == null ? void 0 : _a.data) && fetcher.state !== "idle") {
      setUpdate(false);
    }
  }, [fetcher.data]);
  return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
    className: "container mx-auto p-6",
    children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
      className: "flex items-center gap-2 mb-6",
      children: [/* @__PURE__ */ jsxRuntimeExports.jsxs(Button, {
        variant: "ghost",
        size: "sm",
        onClick: () => navigate(-1),
        children: [/* @__PURE__ */ jsxRuntimeExports.jsx(ArrowLeft, {
          className: "h-4 w-4 mr-2"
        }), "Back to NetWorks"]
      }), /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
        className: "text-muted-foreground",
        children: "/"
      }), /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
        className: "font-semibold",
        children: name
      })]
    }), /* @__PURE__ */ jsxRuntimeExports.jsxs(Tabs, {
      value: activeTab,
      onValueChange: handleTabChange,
      className: "mb-6",
      children: [/* @__PURE__ */ jsxRuntimeExports.jsxs(TabsList, {
        children: [/* @__PURE__ */ jsxRuntimeExports.jsx(TabsTrigger, {
          value: "config",
          children: "Config"
        }), /* @__PURE__ */ jsxRuntimeExports.jsx(TabsTrigger, {
          value: "netWorkItemDetails",
          children: "NetWork Item Category"
        })]
      }), /* @__PURE__ */ jsxRuntimeExports.jsx(TabsContent, {
        value: "config",
        children: /* @__PURE__ */ jsxRuntimeExports.jsx(Card, {
          className: "w-full max-w-xl p-6 border rounded-lg shadow-md space-y-6 bg-white",
          children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
            className: "space-y-6",
            children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
              className: "flex justify-between items-center",
              children: [/* @__PURE__ */ jsxRuntimeExports.jsx("p", {
                className: "text-xl font-bold",
                children: "Details"
              }), /* @__PURE__ */ jsxRuntimeExports.jsx(Switch, {
                onClick: () => setUpdate(!update)
              })]
            }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
              className: "space-y-4",
              children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                className: "flex flex-wrap items-center space-y-2 md:space-y-0 md:space-x-4",
                children: [/* @__PURE__ */ jsxRuntimeExports.jsx("p", {
                  className: "text-lg font-semibold text-gray-700",
                  children: "ID:"
                }), /* @__PURE__ */ jsxRuntimeExports.jsx("p", {
                  className: "text-lg text-gray-900",
                  children: data.id
                })]
              }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                className: "flex flex-wrap items-center md:space-x-4",
                children: [/* @__PURE__ */ jsxRuntimeExports.jsx("p", {
                  className: "w-full md:w-auto text-lg font-semibold text-gray-700",
                  children: "Name:"
                }), /* @__PURE__ */ jsxRuntimeExports.jsx(Input, {
                  className: "flex-1 w-full md:w-auto",
                  placeholder: "Enter Name",
                  value: networkName,
                  onChange: (e) => setNetworkName(e.target.value)
                }), update && /* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
                  className: "mt-2 md:mt-0 w-full md:w-auto",
                  onClick: () => handleUpdate("name", networkName, data == null ? void 0 : data.id, "network"),
                  children: "Update"
                })]
              }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                className: "flex flex-wrap items-center md:space-x-4",
                children: [/* @__PURE__ */ jsxRuntimeExports.jsx("p", {
                  className: "w-full md:w-auto text-lg font-semibold text-gray-700",
                  children: "Manager Name:"
                }), /* @__PURE__ */ jsxRuntimeExports.jsx(Input, {
                  className: "flex-1 w-full md:w-auto",
                  placeholder: "Enter Manager Name",
                  value: managerName,
                  onChange: (e) => setMangerName(e.target.value)
                }), update && /* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
                  className: "mt-2 md:mt-0 w-full md:w-auto",
                  onClick: () => handleUpdate("managerName", managerName, data == null ? void 0 : data.id, "network"),
                  children: "Update"
                })]
              })]
            })]
          }, data.id)
        })
      }), /* @__PURE__ */ jsxRuntimeExports.jsx(TabsContent, {
        value: "netWorkItemDetails",
        children: /* @__PURE__ */ jsxRuntimeExports.jsx(CommonItemCategoryList, {
          data: selectedNetworkItemCategory,
          mItemCategoryList,
          addItem: addNcItem
        })
      })]
    })]
  });
}
export {
  SelectedNetworkCategory as default
};
//# sourceMappingURL=home.selectedNetworkCategory-7-j05VLA.js.map
