import { r as reactExports, j as jsxRuntimeExports } from "./jsx-runtime-bB2y7OuD.js";
import { C as Card, b as <PERSON><PERSON><PERSON><PERSON>, c as Card<PERSON><PERSON><PERSON>, d as CardDescription, a as CardContent } from "./card-BJQMSLe_.js";
import { I as Input } from "./input-3v87qohQ.js";
import { B as Button } from "./button-ByAXMyvk.js";
import { d as decodePolygon, L as LoadScript, G as GoogleMap, S as StandaloneSearchBox, P as Polygon, D as DrawingManager } from "./polyline-utils-DkQLXyiU.js";
import { u as useLoaderData, F as Form } from "./components-D7UvGag_.js";
import { a as useNavigate } from "./index-DhHTcibu.js";
import { A as ArrowLeft } from "./arrow-left-D_Ztdrp5.js";
import "./utils-GkgzjW3c.js";
import "./index-z_byfFrQ.js";
import "./index-ImHKLo0a.js";
import "./index-Vp2vNLNM.js";
import "./createLucideIcon-uwkRm45G.js";
const BANGALORE_CENTER = {
  lat: 12.9716,
  lng: 77.5946
};
function EditLocality() {
  const {
    area,
    googleMapsApiKey
  } = useLoaderData();
  const [isClient, setIsClient] = reactExports.useState(false);
  const [scriptLoaded, setScriptLoaded] = reactExports.useState(false);
  const [name, setName] = reactExports.useState(area.name);
  const [polygon, setPolygon] = reactExports.useState(area.polygon ? decodePolygon(area.polygon) : []);
  const [map, setMap] = reactExports.useState(null);
  const [searchBox, setSearchBox] = reactExports.useState(null);
  const polygonRef = reactExports.useRef(null);
  const polygonListenersRef = reactExports.useRef([]);
  const navigate = useNavigate();
  reactExports.useEffect(() => {
    setIsClient(true);
  }, []);
  const onLoad = reactExports.useCallback((mapInstance) => {
    setMap(mapInstance);
  }, []);
  const onPolygonComplete = reactExports.useCallback((poly) => {
    if (polygonRef.current) {
      polygonRef.current.setMap(null);
      polygonListenersRef.current.forEach((listener) => google.maps.event.removeListener(listener));
      polygonListenersRef.current = [];
    }
    polygonRef.current = poly;
    const polyArray = poly.getPath().getArray().map((latlng) => ({
      lat: latlng.lat(),
      lng: latlng.lng()
    }));
    setPolygon(polyArray);
    const path = poly.getPath();
    const updatePolygon = () => {
      setPolygon(path.getArray().map((latlng) => ({
        lat: latlng.lat(),
        lng: latlng.lng()
      })));
    };
    const setAtListener = google.maps.event.addListener(path, "set_at", updatePolygon);
    const insertAtListener = google.maps.event.addListener(path, "insert_at", updatePolygon);
    const removeAtListener = google.maps.event.addListener(path, "remove_at", updatePolygon);
    polygonListenersRef.current.push(setAtListener, insertAtListener, removeAtListener);
  }, []);
  const onSearchBoxLoad = (ref) => {
    setSearchBox(ref);
  };
  const onPlacesChanged = () => {
    if (searchBox && map) {
      const places = searchBox.getPlaces();
      if (places && places.length > 0) {
        const place = places[0];
        if (place.geometry && place.geometry.location) {
          map.panTo(place.geometry.location);
          map.setZoom(15);
        }
      }
    }
  };
  reactExports.useEffect(() => {
    if (map && polygon.length > 0) {
      const bounds = new google.maps.LatLngBounds();
      polygon.forEach((coord) => bounds.extend(coord));
      map.fitBounds(bounds);
    }
  }, [map, polygon]);
  reactExports.useEffect(() => {
    return () => {
      polygonListenersRef.current.forEach((listener) => google.maps.event.removeListener(listener));
      polygonListenersRef.current = [];
    };
  }, []);
  if (!isClient) {
    return /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
      children: "Loading..."
    });
  }
  return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
    className: "h-[calc(100vh-64px)] bg-white p-6 overflow-y-auto",
    children: [/* @__PURE__ */ jsxRuntimeExports.jsx("div", {
      className: "flex justify-between items-center mb-6",
      children: /* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
        onClick: () => navigate("/home/<USER>"),
        variant: "ghost",
        size: "icon",
        children: /* @__PURE__ */ jsxRuntimeExports.jsx(ArrowLeft, {
          size: 24
        })
      })
    }), /* @__PURE__ */ jsxRuntimeExports.jsxs(Card, {
      className: "w-full mx-auto",
      children: [/* @__PURE__ */ jsxRuntimeExports.jsxs(CardHeader, {
        children: [/* @__PURE__ */ jsxRuntimeExports.jsxs(CardTitle, {
          children: ["Edit Area: ", area.name]
        }), /* @__PURE__ */ jsxRuntimeExports.jsx(CardDescription, {
          children: "Update the area name and boundary"
        })]
      }), /* @__PURE__ */ jsxRuntimeExports.jsx(CardContent, {
        className: "space-y-4",
        children: /* @__PURE__ */ jsxRuntimeExports.jsx(Form, {
          method: "PUT",
          children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
            className: "space-y-4",
            children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
              children: [/* @__PURE__ */ jsxRuntimeExports.jsx("label", {
                htmlFor: "name",
                className: "block text-sm font-medium text-gray-700",
                children: "Area Name"
              }), /* @__PURE__ */ jsxRuntimeExports.jsx(Input, {
                id: "name",
                name: "name",
                value: name,
                onChange: (e) => setName(e.target.value),
                required: true,
                className: "mt-1"
              })]
            }), /* @__PURE__ */ jsxRuntimeExports.jsx("input", {
              type: "hidden",
              name: "polygon",
              value: encode(polygon)
            }), /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
              className: "h-[400px] relative",
              children: /* @__PURE__ */ jsxRuntimeExports.jsx(LoadScript, {
                googleMapsApiKey,
                libraries: ["drawing", "places"],
                onLoad: () => setScriptLoaded(true),
                children: /* @__PURE__ */ jsxRuntimeExports.jsxs(GoogleMap, {
                  mapContainerClassName: "w-full h-full",
                  center: BANGALORE_CENTER,
                  zoom: 11,
                  onLoad,
                  children: [/* @__PURE__ */ jsxRuntimeExports.jsx(StandaloneSearchBox, {
                    onLoad: onSearchBoxLoad,
                    onPlacesChanged,
                    children: /* @__PURE__ */ jsxRuntimeExports.jsx(Input, {
                      type: "text",
                      placeholder: "Search for a location",
                      className: "absolute top-2 left-1/2 transform -translate-x-1/2 w-1/3 z-10"
                    })
                  }), polygon.length > 0 && /* @__PURE__ */ jsxRuntimeExports.jsx(Polygon, {
                    path: polygon,
                    options: {
                      fillColor: "#4F46E5",
                      fillOpacity: 0.4,
                      strokeColor: "#4F46E5",
                      strokeWeight: 2
                    }
                  }), scriptLoaded && typeof window !== "undefined" && window.google && /* @__PURE__ */ jsxRuntimeExports.jsx(DrawingManager, {
                    onPolygonComplete,
                    options: {
                      polygonOptions: {
                        fillColor: "#4F46E5",
                        fillOpacity: 0.4,
                        strokeColor: "#4F46E5",
                        strokeWeight: 2,
                        clickable: true,
                        editable: true,
                        draggable: true
                      },
                      drawingControl: true,
                      drawingControlOptions: {
                        position: window.google.maps.ControlPosition.TOP_CENTER,
                        drawingModes: [window.google.maps.drawing.OverlayType.POLYGON]
                      }
                    }
                  })]
                })
              })
            }), /* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
              type: "submit",
              className: "w-full",
              children: "Update Area"
            })]
          })
        })
      })]
    })]
  });
}
export {
  EditLocality as default
};
//# sourceMappingURL=home.localities._areaId.edit-BPUHB0ho.js.map
