{"version": 3, "file": "seller.marketing.campaigns-B7gK4M9z.js", "sources": ["../../../app/components/marketing/CampaignCard.tsx", "../../../app/routes/seller.marketing.campaigns.tsx"], "sourcesContent": ["import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from \"@components/ui/card\";\r\nimport { Button } from \"@components/ui/button\";\r\nimport { Badge } from \"@components/ui/badge\";\r\nimport { MessageCircle, Share2, Users } from \"lucide-react\";\r\n\r\nexport interface Campaign {\r\n  id: number;\r\n  title: string;\r\n  type: \"SMS\" | \"WhatsApp\";\r\n  status: \"Active\" | \"Scheduled\" | \"Completed\";\r\n  reach: string;\r\n  date: string;\r\n}\r\n\r\ninterface CampaignCardProps {\r\n  campaign: Campaign;\r\n  onViewDetails: (id: number) => void;\r\n}\r\n\r\nexport function CampaignCard({ campaign, onViewDetails }: CampaignCardProps) {\r\n  const getStatusColor = (status: Campaign[\"status\"]) => {\r\n    switch (status) {\r\n      case \"Active\":\r\n        return \"bg-green-500\";\r\n      case \"Scheduled\":\r\n        return \"bg-blue-500\";\r\n      case \"Completed\":\r\n        return \"bg-gray-500\";\r\n      default:\r\n        return \"bg-gray-500\";\r\n    }\r\n  };\r\n\r\n  const getTypeIcon = (type: Campaign[\"type\"]) => {\r\n    switch (type) {\r\n      case \"SMS\":\r\n        return <MessageCircle className=\"h-4 w-4\" />;\r\n      case \"WhatsApp\":\r\n        return <Share2 className=\"h-4 w-4\" />;\r\n      default:\r\n        return null;\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Card>\r\n      <CardHeader>\r\n        <div className=\"flex items-center justify-between\">\r\n          <CardTitle className=\"text-lg\">{campaign.title}</CardTitle>\r\n          <Badge className={getStatusColor(campaign.status)}>\r\n            {campaign.status}\r\n          </Badge>\r\n        </div>\r\n      </CardHeader>\r\n      <CardContent>\r\n        <div className=\"space-y-2\">\r\n          <div className=\"flex items-center text-sm text-muted-foreground\">\r\n            {getTypeIcon(campaign.type)}\r\n            <span className=\"ml-2\">{campaign.type}</span>\r\n          </div>\r\n          <div className=\"flex items-center text-sm text-muted-foreground\">\r\n            <Users className=\"h-4 w-4\" />\r\n            <span className=\"ml-2\">{campaign.reach}</span>\r\n          </div>\r\n          <div className=\"text-sm text-muted-foreground\">\r\n            {new Date(campaign.date).toLocaleDateString()}\r\n          </div>\r\n        </div>\r\n      </CardContent>\r\n      <CardFooter>\r\n        <Button\r\n          variant=\"outline\"\r\n          className=\"w-full\"\r\n          onClick={() => onViewDetails(campaign.id)}\r\n        >\r\n          View Details\r\n        </Button>\r\n      </CardFooter>\r\n    </Card>\r\n  );\r\n} ", "import { But<PERSON> } from \"@components/ui/button\";\r\nimport { ScrollArea } from \"@components/ui/scroll-area\";\r\nimport { MessageCircle, Share2 } from \"lucide-react\";\r\nimport { CampaignCard } from \"@components/marketing/CampaignCard\";\r\nimport type { Campaign } from \"@components/marketing/CampaignCard\";\r\n\r\n// Mock data for campaigns - Move to service later\r\nconst mockCampaigns: Campaign[] = [\r\n  {\r\n    id: 1,\r\n    title: \"Summer Sale\",\r\n    type: \"SMS\",\r\n    status: \"Active\",\r\n    reach: \"1,200 customers\",\r\n    date: \"2024-03-20\",\r\n  },\r\n  {\r\n    id: 2,\r\n    title: \"New Product Launch\",\r\n    type: \"WhatsApp\",\r\n    status: \"Scheduled\",\r\n    reach: \"800 customers\",\r\n    date: \"2024-03-25\",\r\n  },\r\n];\r\n\r\nexport default function Campaigns() {\r\n  const handleViewCampaignDetails = (id: number) => {\r\n    // Implement view campaign details logic\r\n    console.log(\"View campaign details:\", id);\r\n  };\r\n\r\n  return (\r\n    <div>\r\n      <div className=\"flex justify-end mb-4 space-x-2\">\r\n        <Button variant=\"outline\">\r\n          <MessageCircle className=\"h-4 w-4 mr-2\" />\r\n          SMS Campaign\r\n        </Button>\r\n        <Button>\r\n          <Share2 className=\"h-4 w-4 mr-2\" />\r\n          WhatsApp Campaign\r\n        </Button>\r\n      </div>\r\n\r\n      <ScrollArea className=\"h-[calc(100vh-300px)]\">\r\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\r\n          {mockCampaigns.map((campaign) => (\r\n            <CampaignCard\r\n              key={campaign.id}\r\n              campaign={campaign}\r\n              onViewDetails={handleViewCampaignDetails}\r\n            />\r\n          ))}\r\n        </div>\r\n      </ScrollArea>\r\n    </div>\r\n  );\r\n} "], "names": ["jsx", "jsxs", "mockCampaigns", "id", "title", "type", "status", "reach", "date", "Campaigns", "handleViewCampaignDetails", "console", "log", "children", "className", "<PERSON><PERSON>", "variant", "MessageCircle", "Share2", "ScrollArea", "map", "campaign", "CampaignCard", "onViewDetails"], "mappings": ";;;;;;;;;;;;;;;;;;AAmBO,SAAS,aAAa,EAAE,UAAU,iBAAoC;AACrE,QAAA,iBAAiB,CAAC,WAA+B;AACrD,YAAQ,QAAQ;AAAA,MACd,KAAK;AACI,eAAA;AAAA,MACT,KAAK;AACI,eAAA;AAAA,MACT,KAAK;AACI,eAAA;AAAA,MACT;AACS,eAAA;AAAA,IAAA;AAAA,EAEb;AAEM,QAAA,cAAc,CAAC,SAA2B;AAC9C,YAAQ,MAAM;AAAA,MACZ,KAAK;AACI,eAAAA,kCAAA,IAAC,eAAc,EAAA,WAAU,UAAU,CAAA;AAAA,MAC5C,KAAK;AACI,eAAAA,kCAAA,IAAC,QAAO,EAAA,WAAU,UAAU,CAAA;AAAA,MACrC;AACS,eAAA;AAAA,IAAA;AAAA,EAEb;AAEA,gDACG,MACC,EAAA,UAAA;AAAA,IAAAA,sCAAC,YACC,EAAA,UAAAC,kCAAAA,KAAC,OAAI,EAAA,WAAU,qCACb,UAAA;AAAA,MAAAD,kCAAA,IAAC,WAAU,EAAA,WAAU,WAAW,UAAA,SAAS,OAAM;AAAA,MAC/CA,sCAAC,SAAM,WAAW,eAAe,SAAS,MAAM,GAC7C,mBAAS,OACZ,CAAA;AAAA,IAAA,EAAA,CACF,EACF,CAAA;AAAA,IACCA,sCAAA,aAAA,EACC,UAACC,kCAAAA,KAAA,OAAA,EAAI,WAAU,aACb,UAAA;AAAA,MAACA,kCAAAA,KAAA,OAAA,EAAI,WAAU,mDACZ,UAAA;AAAA,QAAA,YAAY,SAAS,IAAI;AAAA,QACzBD,kCAAA,IAAA,QAAA,EAAK,WAAU,QAAQ,mBAAS,KAAK,CAAA;AAAA,MAAA,GACxC;AAAA,MACAC,kCAAAA,KAAC,OAAI,EAAA,WAAU,mDACb,UAAA;AAAA,QAACD,kCAAAA,IAAA,OAAA,EAAM,WAAU,UAAU,CAAA;AAAA,QAC1BA,kCAAA,IAAA,QAAA,EAAK,WAAU,QAAQ,mBAAS,MAAM,CAAA;AAAA,MAAA,GACzC;AAAA,MACAA,kCAAAA,IAAC,OAAI,EAAA,WAAU,iCACZ,UAAA,IAAI,KAAK,SAAS,IAAI,EAAE,mBAAA,EAC3B,CAAA;AAAA,IAAA,EAAA,CACF,EACF,CAAA;AAAA,0CACC,YACC,EAAA,UAAAA,kCAAA;AAAA,MAAC;AAAA,MAAA;AAAA,QACC,SAAQ;AAAA,QACR,WAAU;AAAA,QACV,SAAS,MAAM,cAAc,SAAS,EAAE;AAAA,QACzC,UAAA;AAAA,MAAA;AAAA,IAAA,EAGH,CAAA;AAAA,EAAA,GACF;AAEJ;ACzEA,MAAME,gBAA4B,CAChC;AAAA,EACEC,IAAI;AAAA,EACJC,OAAO;AAAA,EACPC,MAAM;AAAA,EACNC,QAAQ;AAAA,EACRC,OAAO;AAAA,EACPC,MAAM;AACR,GACA;AAAA,EACEL,IAAI;AAAA,EACJC,OAAO;AAAA,EACPC,MAAM;AAAA,EACNC,QAAQ;AAAA,EACRC,OAAO;AAAA,EACPC,MAAM;AACR,CAAA;AAGF,SAAwBC,YAAY;AAC5B,QAAAC,4BAA6BP,QAAe;AAExCQ,YAAAC,IAAI,0BAA0BT,EAAE;AAAA,EAC1C;AAEA,gDACG,OACC;AAAA,IAAAU,UAAA,CAACZ,kCAAA,KAAA,OAAA;AAAA,MAAIa,WAAU;AAAA,MACbD,UAAA,CAACZ,kCAAA,KAAAc,QAAA;AAAA,QAAOC,SAAQ;AAAA,QACdH,UAAA,CAACb,kCAAA,IAAAiB,eAAA;AAAA,UAAcH,WAAU;AAAA,QAAe,CAAA,GAAE,cAAA;AAAA,MAE5C,CAAA,0CACCC,QACC;AAAA,QAAAF,UAAA,CAACb,kCAAA,IAAAkB,QAAA;AAAA,UAAOJ,WAAU;AAAA,QAAe,CAAA,GAAE,mBAAA;AAAA,MAErC,CAAA,CAAA;AAAA,IACF,CAAA,GAEAd,kCAAA,IAACmB,YAAW;AAAA,MAAAL,WAAU;AAAA,MACpBD,UAAAb,kCAAA,IAAC,OAAI;AAAA,QAAAc,WAAU;AAAA,QACZD,UAAAX,cAAckB,IAAKC,cAClBrB,kCAAAA,IAACsB,cAAA;AAAA,UAECD;AAAAA,UACAE,eAAeb;AAAAA,QAAA,GAFVW,SAASlB,EAGhB,CACD;AAAA,MACH,CAAA;AAAA,IACF,CAAA,CAAA;AAAA,EACF,CAAA;AAEJ;"}