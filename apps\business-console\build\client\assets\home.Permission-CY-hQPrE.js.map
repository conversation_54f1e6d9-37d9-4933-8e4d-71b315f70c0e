{"version": 3, "file": "home.Permission-CY-hQPrE.js", "sources": ["../../../app/routes/home.Permission.tsx"], "sourcesContent": ["import { useLoaderData } from \"@remix-run/react\";\r\nimport { useState } from \"react\"\r\nimport { Input } from \"~/components/ui/input\"\r\nimport {\r\n      Table,\r\n      TableBody,\r\n      TableCell,\r\n      TableHead,\r\n      TableHeader,\r\n      TableRow,\r\n} from \"~/components/ui/table\";\r\nimport { getPermissions } from \"~/services/permission\";\r\nimport { Permissions } from \"~/types/api/businessConsoleService/Permissions\";\r\nimport { withAuth, withResponse } from \"~/utils/auth-utils\";\r\n\r\n\r\nexport const loader = withAuth(async ({ request }) => {\r\n      try {\r\n\r\n            const response = await getPermissions(request);\r\n            return withResponse({ data: response.data }, response.headers);\r\n      } catch (error) {\r\n            console.error('Trip summary error:', error);\r\n            throw new Error(`Error fetching trip details: ${error}`);\r\n      }\r\n});\r\n\r\n\r\n\r\nexport default function Permision() {\r\n      const loaderData = useLoaderData<{ data: Permissions[] }>()\r\n      const [searchTerm, setSearchTerm] = useState('')\r\n\r\n      function handleSearch(x: Permissions) {\r\n            return x.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\r\n                  x.resource.toLowerCase().includes(searchTerm.toLowerCase()) ||\r\n                  x.id.toString().toLowerCase().includes(searchTerm.toLowerCase())\r\n      }\r\n      return (\r\n            <div className=\"container mx-auto p-6\">\r\n                  <div className=\"flex justify-between items-center mb-6\">\r\n                        <h1 className=\"text-2xl font-bold\">Permission </h1>\r\n                  </div>\r\n                  <div className=\"flex justify-between mb-4\">\r\n                        <Input\r\n                              placeholder=\"Search By Id, Permission ,Type\"\r\n                              value={searchTerm}\r\n                              onChange={(e) => setSearchTerm(e.target.value)}\r\n                              className=\"max-w-sm\"\r\n                        />\r\n                  </div>\r\n                  <div className=\"rounded-md border\">\r\n                        <Table>\r\n                              <TableHeader>\r\n                                    <TableRow>\r\n                                          <TableHead>ID</TableHead>\r\n                                          <TableHead>PerMission</TableHead>\r\n                                          <TableHead>Resource</TableHead>\r\n                                    </TableRow>\r\n                              </TableHeader>\r\n                              <TableBody>\r\n                                    {loaderData?.data?.length > 0 ?\r\n\r\n                                          loaderData?.data.filter((x) => handleSearch(x))?.map((x) => {\r\n                                                return (\r\n                                                      <TableRow key={x.id}>\r\n                                                            <TableCell>{x?.id}</TableCell>\r\n                                                            <TableCell>{x?.name}</TableCell>\r\n                                                            <TableCell>{x?.resource}</TableCell>\r\n                                                      </TableRow>\r\n                                                )\r\n                                          })\r\n\r\n\r\n                                          :\r\n                                          <TableRow>\r\n                                                <TableCell\r\n                                                      colSpan={9}\r\n                                                      className=\"h-24 text-center\"\r\n                                                >\r\n                                                      No results.\r\n                                                </TableCell>\r\n                                          </TableRow>}\r\n\r\n\r\n                              </TableBody>\r\n\r\n                        </Table>\r\n                  </div>\r\n\r\n            </div>\r\n      )\r\n}"], "names": ["Permision", "loaderData", "useLoaderData", "searchTerm", "setSearchTerm", "useState", "handleSearch", "x", "name", "toLowerCase", "includes", "resource", "id", "toString", "jsxs", "className", "children", "jsx", "Input", "placeholder", "value", "onChange", "e", "target", "Table", "TableHeader", "TableRow", "TableHead", "TableBody", "data", "length", "filter", "map", "TableCell", "colSpan"], "mappings": ";;;;;;;AA6BA,SAAwBA,YAAY;;AAC9B,QAAMC,aAAaC,cAAuC;AAC1D,QAAM,CAACC,YAAYC,aAAa,IAAIC,aAAAA,SAAS,EAAE;AAE/C,WAASC,aAAaC,GAAgB;AACzB,WAAAA,EAAEC,KAAKC,YAAA,EAAcC,SAASP,WAAWM,aAAa,KACvDF,EAAEI,SAASF,YAAY,EAAEC,SAASP,WAAWM,YAAY,CAAC,KAC1DF,EAAEK,GAAGC,WAAWJ,cAAcC,SAASP,WAAWM,YAAA,CAAa;AAAA,EAC3E;AAEM,SAAAK,kCAAAA,KAAC,OAAI;AAAA,IAAAC,WAAU;AAAA,IACTC,UAAA,CAACC,kCAAA,IAAA,OAAA;AAAA,MAAIF,WAAU;AAAA,MACTC,UAAAC,kCAAA,IAAC;QAAGF,WAAU;AAAA,QAAqBC;MAAW,CAAA;AAAA,IACpD,CAAA,GACAC,kCAAA,IAAC,OAAI;AAAA,MAAAF,WAAU;AAAA,MACTC,UAAAC,kCAAA,IAACC,OAAA;AAAA,QACKC,aAAY;AAAA,QACZC,OAAOjB;AAAAA,QACPkB,UAAWC,OAAMlB,cAAckB,EAAEC,OAAOH,KAAK;AAAA,QAC7CL,WAAU;AAAA,MAChB,CAAA;AAAA,IACN,CAAA,GACCE,kCAAA,IAAA,OAAA;AAAA,MAAIF,WAAU;AAAA,MACTC,iDAACQ,OACK;AAAA,QAAAR,UAAA,CAACC,kCAAA,IAAAQ,aAAA;AAAA,UACKT,iDAACU,UACK;AAAA,YAAAV,UAAA,CAAAC,kCAAA,IAACU;cAAUX,UAAE;AAAA,YAAA,CAAA,GACbC,kCAAA,IAACU;cAAUX,UAAU;AAAA,YAAA,CAAA,GACrBC,kCAAA,IAACU;cAAUX,UAAQ;AAAA,YAAA,CAAA,CAAA;AAAA,UACzB,CAAA;AAAA,QACN,CAAA,yCACCY,WACM;AAAA,UAAAZ,YAAAf,8CAAY4B,SAAZ5B,mBAAkB6B,UAAS,KAEtB7B,8CAAY4B,KAAKE,OAAQxB,OAAMD,aAAaC,CAAC,OAA7CN,mBAAiD+B,IAAKzB,OAAM;AACtD,0DACOmB,UACK;AAAA,cAAAV,UAAA,CAACC,kCAAA,IAAAgB,WAAA;AAAA,gBAAWjB,iCAAGJ;AAAAA,cAAG,CAAA,GAClBK,kCAAA,IAACgB,WAAW;AAAA,gBAAAjB,UAAAT,uBAAGC;AAAAA,cAAK,CAAA,GACpBS,kCAAA,IAACgB,WAAW;AAAA,gBAAAjB,UAAAT,uBAAGI;AAAAA,cAAS,CAAA,CAAA;AAAA,YAAA,GAHfJ,EAAEK,EAIjB;AAAA,UAEZ,KAIAK,kCAAA,IAACS,UACK;AAAA,YAAAV,UAAAC,kCAAA,IAACgB,WAAA;AAAA,cACKC,SAAS;AAAA,cACTnB,WAAU;AAAA,cACfC,UAAA;AAAA,YAED,CAAA;AAAA,UACN,CAAA;AAAA,QAGZ,CAAA,CAAA;AAAA,MAEN,CAAA;AAAA,IACN,CAAA,CAAA;AAAA,EAEN,CAAA;AAEZ;"}