{"version": 3, "file": "useDebounce-BXbH_IFZ.js", "sources": ["../../../app/hooks/useDebounce.ts"], "sourcesContent": ["import { useEffect, useState } from 'react';\r\n\r\nexport function useDebounce<T>(value: T, delay: number): T {\r\n  const [debouncedValue, setDebouncedValue] = useState<T>(value);\r\n\r\n  useEffect(() => {\r\n    const timer = setTimeout(() => {\r\n      setDebouncedValue(value);\r\n    }, delay);\r\n\r\n    return () => {\r\n      clearTimeout(timer);\r\n    };\r\n  }, [value, delay]);\r\n\r\n  return debouncedValue;\r\n} "], "names": ["useState", "useEffect"], "mappings": ";AAEgB,SAAA,YAAe,OAAU,OAAkB;AACzD,QAAM,CAAC,gBAAgB,iBAAiB,IAAIA,aAAAA,SAAY,KAAK;AAE7DC,eAAAA,UAAU,MAAM;AACR,UAAA,QAAQ,WAAW,MAAM;AAC7B,wBAAkB,KAAK;AAAA,OACtB,KAAK;AAER,WAAO,MAAM;AACX,mBAAa,KAAK;AAAA,IACpB;AAAA,EAAA,GACC,CAAC,OAAO,KAAK,CAAC;AAEV,SAAA;AACT;"}