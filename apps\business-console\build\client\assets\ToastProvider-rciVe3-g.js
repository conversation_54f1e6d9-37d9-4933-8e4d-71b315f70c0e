import { r as reactExports, j as jsxRuntimeExports } from "./jsx-runtime-bB2y7OuD.js";
const ToastContext = reactExports.createContext(void 0);
const ToastProvider = ({ children }) => {
  const [toast, setToast] = reactExports.useState(null);
  const showToast = (message, type) => {
    setToast({ message, type });
    setTimeout(() => setToast(null), 3e3);
  };
  const clearToast = () => setToast(null);
  return /* @__PURE__ */ jsxRuntimeExports.jsxs(ToastContext.Provider, { value: { toast, showToast, clearToast }, children: [
    children,
    toast && /* @__PURE__ */ jsxRuntimeExports.jsx(
      "div",
      {
        style: {
          position: "fixed",
          top: 20,
          right: 20,
          padding: "10px 20px",
          backgroundColor: toast.type === "success" ? "green" : "red",
          color: "white",
          borderRadius: 5
        },
        children: toast.message
      }
    )
  ] });
};
const useToast = () => {
  const context = reactExports.useContext(ToastContext);
  if (!context) {
    throw new Error("useToast must be used within a ToastProvider");
  }
  return context;
};
export {
  ToastProvider as T,
  useToast as u
};
//# sourceMappingURL=ToastProvider-rciVe3-g.js.map
