import { r as reactExports, j as jsxRuntimeExports } from "./jsx-runtime-bB2y7OuD.js";
import { T as Table, a as TableHeader, b as TableRow, c as TableHead, d as TableBody, e as TableCell } from "./table-CAxdJZsY.js";
import { B as Button } from "./button-ByAXMyvk.js";
import { D as Dialog, a as DialogContent, b as DialogTitle } from "./dialog-BqKosxNq.js";
import { S as SpinnerLoader } from "./SpinnerLoader-CAZRmIJX.js";
import { u as useToast } from "./ToastProvider-rciVe3-g.js";
import { a as useFetcher, u as useLoaderData } from "./components-D7UvGag_.js";
import { I as Input } from "./input-3v87qohQ.js";
import { u as useDebounce } from "./useDebounce-BXbH_IFZ.js";
import { a as useNavigate } from "./index-DhHTcibu.js";
import { S as Search } from "./search-DzDJ71yc.js";
import "./utils-GkgzjW3c.js";
import "./index-z_byfFrQ.js";
import "./index-ImHKLo0a.js";
import "./index-DdafHWkt.js";
import "./index-D7VH9Fc8.js";
import "./index-DVTNuYOr.js";
import "./index-dIGjFc0I.js";
import "./index-BXzPK7u0.js";
import "./index-CzZ6EYkN.js";
import "./index-Vp2vNLNM.js";
import "./index-CEHS9zzk.js";
import "./index-QLGF6kQx.js";
import "./x-CCG_WJDF.js";
import "./createLucideIcon-uwkRm45G.js";
const NetworkManagementModal = ({
  isOpen,
  onClose,
  sellerList
}) => {
  const fetcher = useFetcher();
  const { showToast } = useToast();
  const [managerId, setManagerId] = reactExports.useState("");
  const [sellerId, setSellerId] = reactExports.useState("");
  const [formData, setFormData] = reactExports.useState({
    name: "",
    description: "",
    isPrivate: false,
    managerId: "",
    domain: "",
    networkType: [],
    businessType: "",
    defaultSellerId: "",
    multiSeller: ""
  });
  const [networkError, setNetworkError] = reactExports.useState(false);
  const isLoading = fetcher.state !== "idle";
  reactExports.useEffect(() => {
    var _a, _b;
    if (fetcher.data) {
      if ((_a = fetcher.data) == null ? void 0 : _a.success) {
        showToast("Network Created Successfully", "success");
        onClose();
      } else {
        if (((_b = fetcher.data) == null ? void 0 : _b.success) === false) {
          showToast("Network Creation Failed", "error");
        }
      }
    }
  }, [fetcher.state, fetcher.data, onClose]);
  const handleChange = (e) => {
    const { name, value, type } = e.target;
    const updatedValue = type === "checkbox" && e.target instanceof HTMLInputElement ? e.target.checked : value;
    setFormData((prev) => ({
      ...prev,
      [name]: updatedValue
    }));
  };
  const handleManagerChange = (e) => {
    const selectedValue = e.target.value ? parseInt(e.target.value, 10) : "";
    setManagerId(selectedValue);
    setFormData((prev) => ({
      ...prev,
      managerId: selectedValue
    }));
  };
  const handleSellerChange = (e) => {
    const selectedValue = e.target.value ? parseInt(e.target.value, 10) : "";
    setSellerId(selectedValue);
    setFormData((prev) => ({
      ...prev,
      defaultSellerId: selectedValue
    }));
  };
  const handleNetworkTypeChange = (type) => {
    setFormData((prev) => {
      const newNetworkType = [type];
      setNetworkError(newNetworkType.length === 0);
      return { ...prev, networkType: newNetworkType };
    });
  };
  const handleBusinessTypeChange = (type) => {
    setFormData((prev) => ({
      ...prev,
      businessType: prev.businessType === type ? "" : type
    }));
  };
  const handleSubmit = (e) => {
    e.preventDefault();
    if (formData.networkType.length === 0) {
      setNetworkError(true);
      return;
    }
    if (formData.multiSeller === "" || formData.multiSeller === void 0) {
      showToast("Please select an option for MultiSeller", "error");
      return;
    }
    fetcher.submit(formData, { method: "post" });
  };
  return /* @__PURE__ */ jsxRuntimeExports.jsx(Dialog, { open: isOpen, onOpenChange: onClose, children: /* @__PURE__ */ jsxRuntimeExports.jsxs(DialogContent, { className: "max-h-[90vh] w-full max-w-2xl rounded-lg bg-white p-6 shadow-xl", children: [
    isLoading && /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "absolute inset-0 flex items-center justify-center bg-white/50 backdrop-blur-sm", children: /* @__PURE__ */ jsxRuntimeExports.jsx(SpinnerLoader, { loading: isLoading }) }),
    /* @__PURE__ */ jsxRuntimeExports.jsx(DialogTitle, { className: "text-2xl font-semibold text-gray-800", children: "Create Network" }),
    /* @__PURE__ */ jsxRuntimeExports.jsxs("form", { onSubmit: handleSubmit, className: "space-y-6", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "max-h-[60vh] overflow-y-auto pr-4", children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "space-y-4", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx("label", { className: "block text-sm font-medium text-gray-700", children: "Name" }),
          /* @__PURE__ */ jsxRuntimeExports.jsx(
            "input",
            {
              type: "text",
              name: "name",
              value: formData.name,
              onChange: handleChange,
              required: true,
              className: "mt-1 w-full rounded-md border border-gray-300 p-2 focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50",
              placeholder: "Enter network name"
            }
          )
        ] }),
        /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx("label", { className: "block text-sm font-medium text-gray-700", children: "Description" }),
          /* @__PURE__ */ jsxRuntimeExports.jsx(
            "textarea",
            {
              name: "description",
              value: formData.description,
              onChange: handleChange,
              required: true,
              className: "mt-1 w-full rounded-md border border-gray-300 p-2 focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50",
              rows: 4,
              placeholder: "Describe the network"
            }
          )
        ] }),
        /* @__PURE__ */ jsxRuntimeExports.jsx("div", { children: /* @__PURE__ */ jsxRuntimeExports.jsxs("label", { className: "flex items-center space-x-2", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx(
            "input",
            {
              type: "checkbox",
              name: "isPrivate",
              checked: formData.isPrivate,
              onChange: handleChange,
              className: "h-4 w-4 rounded text-blue-600 focus:ring-blue-500"
            }
          ),
          /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "text-sm text-gray-700", children: "Private Network" })
        ] }) }),
        /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx("label", { className: "block text-sm font-medium text-gray-700", children: "Manager" }),
          /* @__PURE__ */ jsxRuntimeExports.jsxs(
            "select",
            {
              name: "managerId",
              value: managerId,
              onChange: handleManagerChange,
              required: true,
              className: "mt-1 w-full rounded-md border border-gray-300 p-2 focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50",
              children: [
                /* @__PURE__ */ jsxRuntimeExports.jsx("option", { value: "", children: "Select Manager" }),
                sellerList == null ? void 0 : sellerList.map((seller) => {
                  var _a;
                  return /* @__PURE__ */ jsxRuntimeExports.jsx(
                    "option",
                    {
                      value: (_a = seller == null ? void 0 : seller.businessId) == null ? void 0 : _a.toString(),
                      children: seller == null ? void 0 : seller.name
                    },
                    seller == null ? void 0 : seller.id
                  );
                })
              ]
            }
          )
        ] }),
        /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx("label", { className: "block text-sm font-medium text-gray-700", children: "Domain URL" }),
          /* @__PURE__ */ jsxRuntimeExports.jsx(
            "input",
            {
              type: "text",
              name: "domain",
              value: formData.domain,
              onChange: handleChange,
              required: true,
              className: "mt-1 w-full rounded-md border border-gray-300 p-2 focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50",
              placeholder: "https://example.com"
            }
          )
        ] }),
        /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx("label", { className: "block text-sm font-medium text-gray-700", children: "Seller" }),
          /* @__PURE__ */ jsxRuntimeExports.jsxs(
            "select",
            {
              name: "sellerId",
              value: sellerId,
              onChange: handleSellerChange,
              required: true,
              className: "mt-1 w-full rounded-md border border-gray-300 p-2 focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50",
              children: [
                /* @__PURE__ */ jsxRuntimeExports.jsx("option", { value: "", children: "Select Seller" }),
                sellerList == null ? void 0 : sellerList.map((seller) => {
                  var _a;
                  return /* @__PURE__ */ jsxRuntimeExports.jsx("option", { value: (_a = seller == null ? void 0 : seller.id) == null ? void 0 : _a.toString(), children: seller == null ? void 0 : seller.name }, seller == null ? void 0 : seller.id);
                })
              ]
            }
          )
        ] }),
        /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx("label", { className: "block text-sm font-medium text-gray-700", children: "Network Type" }),
          /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "mt-2 flex space-x-6", children: ["B2B", "B2C"].map((type) => /* @__PURE__ */ jsxRuntimeExports.jsxs("label", { className: "flex items-center space-x-2", children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx(
              "input",
              {
                type: "radio",
                name: "networkType",
                value: type,
                checked: formData.networkType.includes(type),
                onChange: () => handleNetworkTypeChange(type),
                className: "h-4 w-4 text-blue-600 focus:ring-blue-500"
              }
            ),
            /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "text-sm text-gray-700", children: type })
          ] }, type)) }),
          networkError && /* @__PURE__ */ jsxRuntimeExports.jsx("p", { className: "mt-1 text-sm text-red-500", children: "Please select a network type." })
        ] }),
        /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx("label", { className: "block text-sm font-medium text-gray-700", children: "Business Type" }),
          /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "mt-2 flex space-x-6", children: ["Restaurant", "Non-Restaurant"].map((type) => /* @__PURE__ */ jsxRuntimeExports.jsxs("label", { className: "flex items-center space-x-2", children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx(
              "input",
              {
                type: "checkbox",
                disabled: type == "Restaurant" && formData.networkType.includes("B2B"),
                checked: formData.businessType === type,
                onChange: () => handleBusinessTypeChange(type),
                className: "h-4 w-4 rounded text-blue-600 focus:ring-blue-500"
              }
            ),
            /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "text-sm text-gray-700", children: type })
          ] }, type)) })
        ] }),
        /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx("label", { className: "block text-sm font-medium text-gray-700", children: "MultiSeller" }),
          /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "mt-2 flex space-x-6", children: [
            { label: "Yes", value: true },
            { label: "No", value: false }
          ].map((option) => /* @__PURE__ */ jsxRuntimeExports.jsxs(
            "label",
            {
              className: "flex items-center space-x-2",
              children: [
                /* @__PURE__ */ jsxRuntimeExports.jsx(
                  "input",
                  {
                    type: "radio",
                    name: "multiSeller",
                    value: option.value.toString(),
                    checked: formData.multiSeller === option.value,
                    onChange: () => setFormData((prev) => ({
                      ...prev,
                      multiSeller: option.value
                    })),
                    className: "h-4 w-4 text-blue-600 focus:ring-blue-500"
                  }
                ),
                /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "text-sm text-gray-700", children: option.label })
              ]
            },
            option.label
          )) }),
          (formData.multiSeller === "" || formData.multiSeller === void 0) && /* @__PURE__ */ jsxRuntimeExports.jsx("p", { className: "mt-1 text-sm text-red-500", children: "Please select an option for MultiSeller." })
        ] })
      ] }) }),
      /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex justify-end space-x-3 pt-4", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx(
          "button",
          {
            type: "button",
            onClick: onClose,
            className: "rounded-md bg-gray-200 px-4 py-2 text-sm font-medium text-gray-700 transition-colors hover:bg-gray-300 focus:outline-none focus:ring focus:ring-gray-200 focus:ring-opacity-50",
            children: "Cancel"
          }
        ),
        /* @__PURE__ */ jsxRuntimeExports.jsx(
          "button",
          {
            type: "submit",
            disabled: isLoading,
            className: "rounded-md bg-blue-600 px-4 py-2 text-sm font-medium text-white transition-colors hover:bg-blue-700 focus:outline-none focus:ring focus:ring-blue-200 focus:ring-opacity-50 disabled:bg-blue-400",
            children: isLoading ? "Creating..." : "Create"
          }
        )
      ] })
    ] })
  ] }) });
};
function NetworksPage() {
  const goTo = useNavigate();
  const {
    netWorkData,
    sellerList,
    permissions
  } = useLoaderData();
  const salesPermissions = (permissions == null ? void 0 : permissions.includes("FmSalesManager")) ?? false;
  netWorkData.length;
  const [isModalOpen, setIsModalOpen] = reactExports.useState(false);
  const [searchTerm, setSearchTerm] = reactExports.useState("");
  const [networkFilter, setNetworkFilter] = reactExports.useState([]);
  const handleSearch = (val) => {
    setSearchTerm(val);
  };
  const fetcher = useFetcher();
  const loading = fetcher.state !== "idle";
  const debouncedSearchTerm = useDebounce(searchTerm, 500);
  reactExports.useEffect(() => {
    if (netWorkData) {
      setNetworkFilter(netWorkData);
    }
  }, [netWorkData, fetcher == null ? void 0 : fetcher.data]);
  reactExports.useEffect(() => {
    if (debouncedSearchTerm.length >= 3 && debouncedSearchTerm !== "") {
      const filterdata = netWorkData == null ? void 0 : netWorkData.filter((item) => item.name.toLowerCase().includes(debouncedSearchTerm.toLowerCase()));
      setNetworkFilter(filterdata);
    } else {
      setNetworkFilter(netWorkData);
    }
  }, [debouncedSearchTerm, netWorkData]);
  return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
    className: "container mx-auto p-6",
    children: [/* @__PURE__ */ jsxRuntimeExports.jsx("h1", {
      className: "text-2xl font-bold mb-4",
      children: "Network Management"
    }), " ", /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
      className: "flex flex-col my-3",
      children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
        className: "relative mx-10 flex flex-col ",
        children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Search, {
          className: "absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
        }), /* @__PURE__ */ jsxRuntimeExports.jsx(Input, {
          type: "search",
          placeholder: "Search by Network Name",
          value: searchTerm,
          onChange: (e) => handleSearch(e.target.value),
          className: "max-w-sm rounded-full pl-10"
        })]
      })
    }), /* @__PURE__ */ jsxRuntimeExports.jsxs(Table, {
      children: [/* @__PURE__ */ jsxRuntimeExports.jsx(TableHeader, {
        className: "bg-gray-100",
        children: /* @__PURE__ */ jsxRuntimeExports.jsxs(TableRow, {
          children: [/* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
            children: "ID"
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
            children: "Name"
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
            children: "Description"
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
            children: "Manager Business ID"
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
            children: "Manager Name"
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {})]
        })
      }), /* @__PURE__ */ jsxRuntimeExports.jsxs(TableBody, {
        children: [loading && /* @__PURE__ */ jsxRuntimeExports.jsx(SpinnerLoader, {
          loading,
          size: 20
        }), (networkFilter == null ? void 0 : networkFilter.length) > 0 ? networkFilter.map((network) => /* @__PURE__ */ jsxRuntimeExports.jsxs(TableRow, {
          children: [/* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
            children: network.id
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
            className: `cursor-pointer text-blue-500 font-bold  items-center`,
            onClick: () => goTo(`/home/<USER>
            children: network.name
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
            children: network.description
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
            children: network.managerBusinessId
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
            children: network.managerName
          })]
        }, network.id)) : /* @__PURE__ */ jsxRuntimeExports.jsx(TableRow, {
          children: /* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
            colSpan: 6,
            className: "h-24 text-center",
            children: "No results."
          })
        })]
      })]
    }), !salesPermissions && /* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
      className: "fixed bottom-5 right-5 rounded-full cursor-pointer",
      onClick: () => setIsModalOpen(true),
      children: "+ Add Network"
    }), "      ", /* @__PURE__ */ jsxRuntimeExports.jsx(NetworkManagementModal, {
      isOpen: isModalOpen,
      onClose: () => setIsModalOpen(false),
      sellerList
    })]
  });
}
export {
  NetworksPage as default
};
//# sourceMappingURL=home.networkManagement-CcSi5aJh.js.map
