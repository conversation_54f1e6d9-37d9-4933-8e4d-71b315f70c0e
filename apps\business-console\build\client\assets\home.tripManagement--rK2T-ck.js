import { r as reactExports, R as React, j as jsxRuntimeExports } from "./jsx-runtime-bB2y7OuD.js";
import { B as Button } from "./button-ByAXMyvk.js";
import { C as Calendar, a as Calendar$1 } from "./calendar-_8-DqkPN.js";
import { I as Input } from "./input-3v87qohQ.js";
import { P as Popover, a as PopoverTrigger, b as PopoverContent } from "./popover-CD2vRFIm.js";
import { T as Table, a as TableHeader, c as TableHead, d as TableBody, b as TableRow, e as TableCell } from "./table-CAxdJZsY.js";
import { c as cn } from "./utils-CCoQZ9cZ.js";
import { u as useLoaderData, a as useFetcher, b as useSearchParams } from "./components-D7UvGag_.js";
import { f as format } from "./format-82yT_5--.js";
import "./index-z_byfFrQ.js";
import "./index-ImHKLo0a.js";
import "./utils-GkgzjW3c.js";
import "./createLucideIcon-uwkRm45G.js";
import "./addMonths-Dj4hq91A.js";
import "./isSameDay-BQMn9z7h.js";
import "./addDays-CyH8qBoF.js";
import "./chevron-right-B-tR7Kir.js";
import "./chevron-left-CLqBlTg1.js";
import "./index-D7VH9Fc8.js";
import "./index-BXzPK7u0.js";
import "./index-CzZ6EYkN.js";
import "./index-Vp2vNLNM.js";
import "./index-CEHS9zzk.js";
import "./index-DVTNuYOr.js";
import "./index-DscYByPT.js";
import "./index-CkL5tk39.js";
import "./index-QLGF6kQx.js";
import "./index-dIGjFc0I.js";
import "./index-DhHTcibu.js";
function TripManagement() {
  var _a;
  const data = useLoaderData();
  const [date, setDate] = reactExports.useState(/* @__PURE__ */ new Date());
  const [searchTerm, setSearchTerm] = reactExports.useState("");
  const fetcher = useFetcher();
  const [tripData, setTriData] = reactExports.useState(data.data || []);
  const handleSubmit = (date2) => {
    if (!dateRange.from) return;
    const formattedFrom = format(dateRange.from, "yyyy-MM-dd");
    const formattedTo = dateRange.to ? format(dateRange.to, "yyyy-MM-dd") : formattedFrom;
    const formData = new FormData();
    formData.append("from", formattedFrom);
    formData.append("to", formattedTo);
    fetcher.submit(formData, {
      method: "GET"
    });
  };
  const itemsPerPage = 200;
  const [currentPage, setCurrentPage] = reactExports.useState(1);
  const totalPages = Math.ceil(tripData.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const currentTrips = tripData.slice(startIndex, startIndex + itemsPerPage);
  reactExports.useEffect(() => {
    if (fetcher.data) {
      setTriData(fetcher.data.data);
    }
  }, [(_a = fetcher.data) == null ? void 0 : _a.data]);
  const filterTrips = (trip) => {
    var _a2, _b, _c;
    return (((_a2 = trip.sellerName) == null ? void 0 : _a2.toLowerCase()) || "").includes(searchTerm.toLowerCase()) || (((_b = trip.tripId) == null ? void 0 : _b.toString().toLowerCase()) || "").includes(searchTerm.toLowerCase()) || (((_c = trip.driverName) == null ? void 0 : _c.toLowerCase()) || "").includes(searchTerm.toLowerCase());
  };
  const handleSetPage = (page) => {
    if (page >= 1 && page <= totalPages) {
      setCurrentPage(page);
    }
  };
  const [searchParams] = useSearchParams();
  const startDate = searchParams.get("from");
  const endDate = searchParams.get("to");
  const [dateRange, setDateRange] = React.useState({
    from: startDate ? new Date(startDate) : /* @__PURE__ */ new Date(),
    to: endDate ? new Date(endDate) : /* @__PURE__ */ new Date()
  });
  const handleDateChange = (range) => {
    if (!(range == null ? void 0 : range.from)) return;
    setDateRange({
      from: range.from,
      to: range.to || void 0
      // If 'to' is not selected, keep it undefined
    });
  };
  return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
    className: "container mx-auto w-full",
    children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
      className: "flex my-7 space-x-10",
      children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
        className: " flex space-x-2",
        children: [/* @__PURE__ */ jsxRuntimeExports.jsx("div", {
          className: "w-full md:w-auto",
          children: /* @__PURE__ */ jsxRuntimeExports.jsxs(Popover, {
            children: [/* @__PURE__ */ jsxRuntimeExports.jsx(PopoverTrigger, {
              asChild: true,
              children: /* @__PURE__ */ jsxRuntimeExports.jsxs(Button, {
                id: "date",
                variant: "outline",
                className: cn("w-[300px] justify-start text-left font-normal", !dateRange.from && "text-muted-foreground"),
                children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Calendar, {}), (dateRange == null ? void 0 : dateRange.from) ? dateRange.to ? /* @__PURE__ */ jsxRuntimeExports.jsxs(jsxRuntimeExports.Fragment, {
                  children: [format(dateRange.from, "LLL dd, y"), " - ", format(dateRange.to, "LLL dd, y")]
                }) : format(dateRange.from, "LLL dd, y") : /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                  children: "Pick a date"
                })]
              })
            }), /* @__PURE__ */ jsxRuntimeExports.jsx(PopoverContent, {
              className: "w-auto p-0",
              align: "start",
              children: /* @__PURE__ */ jsxRuntimeExports.jsx(Calendar$1, {
                initialFocus: true,
                selected: dateRange,
                mode: "range",
                onSelect: handleDateChange
              })
            })]
          })
        }), /* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
          onClick: () => handleSubmit(),
          children: "Get Trips"
        })]
      }), /* @__PURE__ */ jsxRuntimeExports.jsx(Input, {
        placeholder: "Search By tripId.Seller,DriverName",
        value: searchTerm,
        onChange: (e) => setSearchTerm(e.target.value),
        className: "max-w-sm"
      })]
    }), /* @__PURE__ */ jsxRuntimeExports.jsxs(Table, {
      children: [/* @__PURE__ */ jsxRuntimeExports.jsxs(TableHeader, {
        className: "bg-gray-100",
        children: [/* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
          children: "TripId"
        }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
          children: "Seller"
        }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
          children: "Driver"
        }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
          children: "Orders"
        }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
          children: "Total Delivered Order"
        }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
          children: "D.Date"
        }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
          children: "Status"
        })]
      }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableBody, {
        children: currentTrips.length > 0 ? currentTrips.sort((a, b) => {
          const Priority = {
            Dispatched: 1,
            Open: 2
          };
          return (Priority[a.tripStatus] || 3) - (Priority[b.tripStatus] || 3);
        }).filter((trip) => filterTrips(trip)).map((x) => /* @__PURE__ */ jsxRuntimeExports.jsxs(TableRow, {
          children: [/* @__PURE__ */ jsxRuntimeExports.jsxs(TableCell, {
            children: [" ", x.tripId, "  "]
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
            children: `${x.sellerId}-${x.sellerName}`
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
            children: x.driverName
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
            children: x.totalOrders
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
            children: x.totalDeliveredOrders
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
            children: (x == null ? void 0 : x.deliveryDate) ? format(x.deliveryDate, "MM-dd-yyyy") : ""
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
            className: x.tripStatus === "Dispatched" ? "text-red-500" : x.tripStatus === "Open" ? "text-orange-500" : "text-green-600",
            children: x.tripStatus
          })]
        }, x.tripId)) : /* @__PURE__ */ jsxRuntimeExports.jsx(TableRow, {
          children: /* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
            colSpan: 9,
            className: "h-24 text-center",
            children: "No results."
          })
        })
      })]
    }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
      className: "flex items-center space-x-2 my-2",
      children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
        variant: "outline",
        size: "sm",
        onClick: () => handleSetPage(currentPage - 1),
        disabled: currentPage === 1,
        children: "Previous"
      }), /* @__PURE__ */ jsxRuntimeExports.jsxs("span", {
        className: "text-gray-700",
        children: ["Page ", currentPage, " of ", totalPages]
      }), /* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
        variant: "outline",
        size: "sm",
        onClick: () => handleSetPage(currentPage + 1),
        disabled: currentPage === totalPages,
        children: "Next"
      })]
    })]
  });
}
export {
  TripManagement as default
};
//# sourceMappingURL=home.tripManagement--rK2T-ck.js.map
