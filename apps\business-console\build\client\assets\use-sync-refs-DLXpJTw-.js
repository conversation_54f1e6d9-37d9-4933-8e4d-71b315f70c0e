import { r as reactExports, R as React } from "./jsx-runtime-bB2y7OuD.js";
var i = Object.defineProperty;
var d = (t2, e, n2) => e in t2 ? i(t2, e, { enumerable: true, configurable: true, writable: true, value: n2 }) : t2[e] = n2;
var r = (t2, e, n2) => (d(t2, typeof e != "symbol" ? e + "" : e, n2), n2);
let o$1 = class o {
  constructor() {
    r(this, "current", this.detect());
    r(this, "handoffState", "pending");
    r(this, "currentId", 0);
  }
  set(e) {
    this.current !== e && (this.handoffState = "pending", this.currentId = 0, this.current = e);
  }
  reset() {
    this.set(this.detect());
  }
  nextId() {
    return ++this.currentId;
  }
  get isServer() {
    return this.current === "server";
  }
  get isClient() {
    return this.current === "client";
  }
  detect() {
    return typeof window == "undefined" || typeof document == "undefined" ? "server" : "client";
  }
  handoff() {
    this.handoffState === "pending" && (this.handoffState = "complete");
  }
  get isHandoffComplete() {
    return this.handoffState === "complete";
  }
};
let s$1 = new o$1();
let n = (e, t2) => {
  s$1.isServer ? reactExports.useEffect(e, t2) : reactExports.useLayoutEffect(e, t2);
};
function s(e) {
  let r2 = reactExports.useRef(e);
  return n(() => {
    r2.current = e;
  }, [e]), r2;
}
let o2 = function(t2) {
  let e = s(t2);
  return React.useCallback((...r2) => e.current(...r2), [e]);
};
function t(...r2) {
  return Array.from(new Set(r2.flatMap((n2) => typeof n2 == "string" ? n2.split(" ") : []))).filter(Boolean).join(" ");
}
function u$1(r2, n2, ...a) {
  if (r2 in n2) {
    let e = n2[r2];
    return typeof e == "function" ? e(...a) : e;
  }
  let t2 = new Error(`Tried to handle "${r2}" but there is no handler defined. Only defined handlers are: ${Object.keys(n2).map((e) => `"${e}"`).join(", ")}.`);
  throw Error.captureStackTrace && Error.captureStackTrace(t2, u$1), t2;
}
var O = ((a) => (a[a.None = 0] = "None", a[a.RenderStrategy = 1] = "RenderStrategy", a[a.Static = 2] = "Static", a))(O || {}), A = ((e) => (e[e.Unmount = 0] = "Unmount", e[e.Hidden = 1] = "Hidden", e))(A || {});
function L() {
  let n2 = U();
  return reactExports.useCallback((r2) => C({ mergeRefs: n2, ...r2 }), [n2]);
}
function C({ ourProps: n2, theirProps: r2, slot: e, defaultTag: a, features: s2, visible: t2 = true, name: l, mergeRefs: i2 }) {
  i2 = i2 != null ? i2 : $;
  let o3 = P(r2, n2);
  if (t2) return F(o3, e, a, l, i2);
  let y2 = s2 != null ? s2 : 0;
  if (y2 & 2) {
    let { static: f = false, ...u2 } = o3;
    if (f) return F(u2, e, a, l, i2);
  }
  if (y2 & 1) {
    let { unmount: f = true, ...u2 } = o3;
    return u$1(f ? 0 : 1, { [0]() {
      return null;
    }, [1]() {
      return F({ ...u2, hidden: true, style: { display: "none" } }, e, a, l, i2);
    } });
  }
  return F(o3, e, a, l, i2);
}
function F(n2, r2 = {}, e, a, s2) {
  let { as: t$1 = e, children: l, refName: i2 = "ref", ...o3 } = h(n2, ["unmount", "static"]), y2 = n2.ref !== void 0 ? { [i2]: n2.ref } : {}, f = typeof l == "function" ? l(r2) : l;
  "className" in o3 && o3.className && typeof o3.className == "function" && (o3.className = o3.className(r2)), o3["aria-labelledby"] && o3["aria-labelledby"] === o3.id && (o3["aria-labelledby"] = void 0);
  let u2 = {};
  if (r2) {
    let d2 = false, p = [];
    for (let [c, T] of Object.entries(r2)) typeof T == "boolean" && (d2 = true), T === true && p.push(c.replace(/([A-Z])/g, (g) => `-${g.toLowerCase()}`));
    if (d2) {
      u2["data-headlessui-state"] = p.join(" ");
      for (let c of p) u2[`data-${c}`] = "";
    }
  }
  if (t$1 === reactExports.Fragment && (Object.keys(m(o3)).length > 0 || Object.keys(m(u2)).length > 0)) if (!reactExports.isValidElement(f) || Array.isArray(f) && f.length > 1) {
    if (Object.keys(m(o3)).length > 0) throw new Error(['Passing props on "Fragment"!', "", `The current component <${a} /> is rendering a "Fragment".`, "However we need to passthrough the following props:", Object.keys(m(o3)).concat(Object.keys(m(u2))).map((d2) => `  - ${d2}`).join(`
`), "", "You can apply a few solutions:", ['Add an `as="..."` prop, to ensure that we render an actual element instead of a "Fragment".', "Render a single element as the child so that we can forward the props onto that element."].map((d2) => `  - ${d2}`).join(`
`)].join(`
`));
  } else {
    let d2 = f.props, p = d2 == null ? void 0 : d2.className, c = typeof p == "function" ? (...R) => t(p(...R), o3.className) : t(p, o3.className), T = c ? { className: c } : {}, g = P(f.props, m(h(o3, ["ref"])));
    for (let R in u2) R in g && delete u2[R];
    return reactExports.cloneElement(f, Object.assign({}, g, u2, y2, { ref: s2(H(f), y2.ref) }, T));
  }
  return reactExports.createElement(t$1, Object.assign({}, h(o3, ["ref"]), t$1 !== reactExports.Fragment && y2, t$1 !== reactExports.Fragment && u2), f);
}
function U() {
  let n2 = reactExports.useRef([]), r2 = reactExports.useCallback((e) => {
    for (let a of n2.current) a != null && (typeof a == "function" ? a(e) : a.current = e);
  }, []);
  return (...e) => {
    if (!e.every((a) => a == null)) return n2.current = e, r2;
  };
}
function $(...n2) {
  return n2.every((r2) => r2 == null) ? void 0 : (r2) => {
    for (let e of n2) e != null && (typeof e == "function" ? e(r2) : e.current = r2);
  };
}
function P(...n2) {
  if (n2.length === 0) return {};
  if (n2.length === 1) return n2[0];
  let r2 = {}, e = {};
  for (let s2 of n2) for (let t2 in s2) t2.startsWith("on") && typeof s2[t2] == "function" ? (e[t2] != null || (e[t2] = []), e[t2].push(s2[t2])) : r2[t2] = s2[t2];
  if (r2.disabled || r2["aria-disabled"]) for (let s2 in e) /^(on(?:Click|Pointer|Mouse|Key)(?:Down|Up|Press)?)$/.test(s2) && (e[s2] = [(t2) => {
    var l;
    return (l = t2 == null ? void 0 : t2.preventDefault) == null ? void 0 : l.call(t2);
  }]);
  for (let s2 in e) Object.assign(r2, { [s2](t2, ...l) {
    let i2 = e[s2];
    for (let o3 of i2) {
      if ((t2 instanceof Event || (t2 == null ? void 0 : t2.nativeEvent) instanceof Event) && t2.defaultPrevented) return;
      o3(t2, ...l);
    }
  } });
  return r2;
}
function _(...n2) {
  if (n2.length === 0) return {};
  if (n2.length === 1) return n2[0];
  let r2 = {}, e = {};
  for (let s2 of n2) for (let t2 in s2) t2.startsWith("on") && typeof s2[t2] == "function" ? (e[t2] != null || (e[t2] = []), e[t2].push(s2[t2])) : r2[t2] = s2[t2];
  for (let s2 in e) Object.assign(r2, { [s2](...t2) {
    let l = e[s2];
    for (let i2 of l) i2 == null || i2(...t2);
  } });
  return r2;
}
function K(n2) {
  var r2;
  return Object.assign(reactExports.forwardRef(n2), { displayName: (r2 = n2.displayName) != null ? r2 : n2.name });
}
function m(n2) {
  let r2 = Object.assign({}, n2);
  for (let e in r2) r2[e] === void 0 && delete r2[e];
  return r2;
}
function h(n2, r2 = []) {
  let e = Object.assign({}, n2);
  for (let a of r2) a in e && delete e[a];
  return e;
}
function H(n2) {
  return React.version.split(".")[0] >= "19" ? n2.props.ref : n2.ref;
}
let u = Symbol();
function y(...t2) {
  let n2 = reactExports.useRef(t2);
  reactExports.useEffect(() => {
    n2.current = t2;
  }, [t2]);
  let c = o2((e) => {
    for (let o3 of n2.current) o3 != null && (typeof o3 == "function" ? o3(e) : o3.current = e);
  });
  return t2.every((e) => e == null || (e == null ? void 0 : e[u])) ? void 0 : c;
}
export {
  A,
  K,
  L,
  O,
  _,
  s as a,
  m,
  n,
  o2 as o,
  s$1 as s,
  t,
  u$1 as u,
  y
};
//# sourceMappingURL=use-sync-refs-DLXpJTw-.js.map
