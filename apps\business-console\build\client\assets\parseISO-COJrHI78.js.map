{"version": 3, "file": "parseISO-COJrHI78.js", "sources": ["../../../node_modules/date-fns/parseISO.mjs"], "sourcesContent": ["import { millisecondsInHour, millisecondsInMinute } from \"./constants.mjs\";\n\n/**\n * The {@link parseISO} function options.\n */\n\n/**\n * @name parseISO\n * @category Common Helpers\n * @summary Parse ISO string\n *\n * @description\n * Parse the given string in ISO 8601 format and return an instance of Date.\n *\n * Function accepts complete ISO 8601 formats as well as partial implementations.\n * ISO 8601: http://en.wikipedia.org/wiki/ISO_8601\n *\n * If the argument isn't a string, the function cannot parse the string or\n * the values are invalid, it returns Invalid Date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param argument - The value to convert\n * @param options - An object with options\n *\n * @returns The parsed date in the local time zone\n *\n * @example\n * // Convert string '2014-02-11T11:30:30' to date:\n * const result = parseISO('2014-02-11T11:30:30')\n * //=> Tue Feb 11 2014 11:30:30\n *\n * @example\n * // Convert string '+02014101' to date,\n * // if the additional number of digits in the extended year format is 1:\n * const result = parseISO('+02014101', { additionalDigits: 1 })\n * //=> Fri Apr 11 2014 00:00:00\n */\nexport function parseISO(argument, options) {\n  const additionalDigits = options?.additionalDigits ?? 2;\n  const dateStrings = splitDateString(argument);\n\n  let date;\n  if (dateStrings.date) {\n    const parseYearResult = parseYear(dateStrings.date, additionalDigits);\n    date = parseDate(parseYearResult.restDateString, parseYearResult.year);\n  }\n\n  if (!date || isNaN(date.getTime())) {\n    return new Date(NaN);\n  }\n\n  const timestamp = date.getTime();\n  let time = 0;\n  let offset;\n\n  if (dateStrings.time) {\n    time = parseTime(dateStrings.time);\n    if (isNaN(time)) {\n      return new Date(NaN);\n    }\n  }\n\n  if (dateStrings.timezone) {\n    offset = parseTimezone(dateStrings.timezone);\n    if (isNaN(offset)) {\n      return new Date(NaN);\n    }\n  } else {\n    const dirtyDate = new Date(timestamp + time);\n    // JS parsed string assuming it's in UTC timezone\n    // but we need it to be parsed in our timezone\n    // so we use utc values to build date in our timezone.\n    // Year values from 0 to 99 map to the years 1900 to 1999\n    // so set year explicitly with setFullYear.\n    const result = new Date(0);\n    result.setFullYear(\n      dirtyDate.getUTCFullYear(),\n      dirtyDate.getUTCMonth(),\n      dirtyDate.getUTCDate(),\n    );\n    result.setHours(\n      dirtyDate.getUTCHours(),\n      dirtyDate.getUTCMinutes(),\n      dirtyDate.getUTCSeconds(),\n      dirtyDate.getUTCMilliseconds(),\n    );\n    return result;\n  }\n\n  return new Date(timestamp + time + offset);\n}\n\nconst patterns = {\n  dateTimeDelimiter: /[T ]/,\n  timeZoneDelimiter: /[Z ]/i,\n  timezone: /([Z+-].*)$/,\n};\n\nconst dateRegex =\n  /^-?(?:(\\d{3})|(\\d{2})(?:-?(\\d{2}))?|W(\\d{2})(?:-?(\\d{1}))?|)$/;\nconst timeRegex =\n  /^(\\d{2}(?:[.,]\\d*)?)(?::?(\\d{2}(?:[.,]\\d*)?))?(?::?(\\d{2}(?:[.,]\\d*)?))?$/;\nconst timezoneRegex = /^([+-])(\\d{2})(?::?(\\d{2}))?$/;\n\nfunction splitDateString(dateString) {\n  const dateStrings = {};\n  const array = dateString.split(patterns.dateTimeDelimiter);\n  let timeString;\n\n  // The regex match should only return at maximum two array elements.\n  // [date], [time], or [date, time].\n  if (array.length > 2) {\n    return dateStrings;\n  }\n\n  if (/:/.test(array[0])) {\n    timeString = array[0];\n  } else {\n    dateStrings.date = array[0];\n    timeString = array[1];\n    if (patterns.timeZoneDelimiter.test(dateStrings.date)) {\n      dateStrings.date = dateString.split(patterns.timeZoneDelimiter)[0];\n      timeString = dateString.substr(\n        dateStrings.date.length,\n        dateString.length,\n      );\n    }\n  }\n\n  if (timeString) {\n    const token = patterns.timezone.exec(timeString);\n    if (token) {\n      dateStrings.time = timeString.replace(token[1], \"\");\n      dateStrings.timezone = token[1];\n    } else {\n      dateStrings.time = timeString;\n    }\n  }\n\n  return dateStrings;\n}\n\nfunction parseYear(dateString, additionalDigits) {\n  const regex = new RegExp(\n    \"^(?:(\\\\d{4}|[+-]\\\\d{\" +\n      (4 + additionalDigits) +\n      \"})|(\\\\d{2}|[+-]\\\\d{\" +\n      (2 + additionalDigits) +\n      \"})$)\",\n  );\n\n  const captures = dateString.match(regex);\n  // Invalid ISO-formatted year\n  if (!captures) return { year: NaN, restDateString: \"\" };\n\n  const year = captures[1] ? parseInt(captures[1]) : null;\n  const century = captures[2] ? parseInt(captures[2]) : null;\n\n  // either year or century is null, not both\n  return {\n    year: century === null ? year : century * 100,\n    restDateString: dateString.slice((captures[1] || captures[2]).length),\n  };\n}\n\nfunction parseDate(dateString, year) {\n  // Invalid ISO-formatted year\n  if (year === null) return new Date(NaN);\n\n  const captures = dateString.match(dateRegex);\n  // Invalid ISO-formatted string\n  if (!captures) return new Date(NaN);\n\n  const isWeekDate = !!captures[4];\n  const dayOfYear = parseDateUnit(captures[1]);\n  const month = parseDateUnit(captures[2]) - 1;\n  const day = parseDateUnit(captures[3]);\n  const week = parseDateUnit(captures[4]);\n  const dayOfWeek = parseDateUnit(captures[5]) - 1;\n\n  if (isWeekDate) {\n    if (!validateWeekDate(year, week, dayOfWeek)) {\n      return new Date(NaN);\n    }\n    return dayOfISOWeekYear(year, week, dayOfWeek);\n  } else {\n    const date = new Date(0);\n    if (\n      !validateDate(year, month, day) ||\n      !validateDayOfYearDate(year, dayOfYear)\n    ) {\n      return new Date(NaN);\n    }\n    date.setUTCFullYear(year, month, Math.max(dayOfYear, day));\n    return date;\n  }\n}\n\nfunction parseDateUnit(value) {\n  return value ? parseInt(value) : 1;\n}\n\nfunction parseTime(timeString) {\n  const captures = timeString.match(timeRegex);\n  if (!captures) return NaN; // Invalid ISO-formatted time\n\n  const hours = parseTimeUnit(captures[1]);\n  const minutes = parseTimeUnit(captures[2]);\n  const seconds = parseTimeUnit(captures[3]);\n\n  if (!validateTime(hours, minutes, seconds)) {\n    return NaN;\n  }\n\n  return (\n    hours * millisecondsInHour + minutes * millisecondsInMinute + seconds * 1000\n  );\n}\n\nfunction parseTimeUnit(value) {\n  return (value && parseFloat(value.replace(\",\", \".\"))) || 0;\n}\n\nfunction parseTimezone(timezoneString) {\n  if (timezoneString === \"Z\") return 0;\n\n  const captures = timezoneString.match(timezoneRegex);\n  if (!captures) return 0;\n\n  const sign = captures[1] === \"+\" ? -1 : 1;\n  const hours = parseInt(captures[2]);\n  const minutes = (captures[3] && parseInt(captures[3])) || 0;\n\n  if (!validateTimezone(hours, minutes)) {\n    return NaN;\n  }\n\n  return sign * (hours * millisecondsInHour + minutes * millisecondsInMinute);\n}\n\nfunction dayOfISOWeekYear(isoWeekYear, week, day) {\n  const date = new Date(0);\n  date.setUTCFullYear(isoWeekYear, 0, 4);\n  const fourthOfJanuaryDay = date.getUTCDay() || 7;\n  const diff = (week - 1) * 7 + day + 1 - fourthOfJanuaryDay;\n  date.setUTCDate(date.getUTCDate() + diff);\n  return date;\n}\n\n// Validation functions\n\n// February is null to handle the leap year (using ||)\nconst daysInMonths = [31, null, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];\n\nfunction isLeapYearIndex(year) {\n  return year % 400 === 0 || (year % 4 === 0 && year % 100 !== 0);\n}\n\nfunction validateDate(year, month, date) {\n  return (\n    month >= 0 &&\n    month <= 11 &&\n    date >= 1 &&\n    date <= (daysInMonths[month] || (isLeapYearIndex(year) ? 29 : 28))\n  );\n}\n\nfunction validateDayOfYearDate(year, dayOfYear) {\n  return dayOfYear >= 1 && dayOfYear <= (isLeapYearIndex(year) ? 366 : 365);\n}\n\nfunction validateWeekDate(_year, week, day) {\n  return week >= 1 && week <= 53 && day >= 0 && day <= 6;\n}\n\nfunction validateTime(hours, minutes, seconds) {\n  if (hours === 24) {\n    return minutes === 0 && seconds === 0;\n  }\n\n  return (\n    seconds >= 0 &&\n    seconds < 60 &&\n    minutes >= 0 &&\n    minutes < 60 &&\n    hours >= 0 &&\n    hours < 25\n  );\n}\n\nfunction validateTimezone(_hours, minutes) {\n  return minutes >= 0 && minutes <= 59;\n}\n\n// Fallback for modularized imports:\nexport default parseISO;\n"], "names": [], "mappings": ";AAsCO,SAAS,SAAS,UAAU,SAAS;AAC1C,QAAM,mBAAgD;AACtD,QAAM,cAAc,gBAAgB,QAAQ;AAE5C,MAAI;AACJ,MAAI,YAAY,MAAM;AACpB,UAAM,kBAAkB,UAAU,YAAY,MAAM,gBAAgB;AACpE,WAAO,UAAU,gBAAgB,gBAAgB,gBAAgB,IAAI;AAAA,EACzE;AAEE,MAAI,CAAC,QAAQ,MAAM,KAAK,QAAS,CAAA,GAAG;AAClC,WAAO,oBAAI,KAAK,GAAG;AAAA,EACvB;AAEE,QAAM,YAAY,KAAK,QAAS;AAChC,MAAI,OAAO;AACX,MAAI;AAEJ,MAAI,YAAY,MAAM;AACpB,WAAO,UAAU,YAAY,IAAI;AACjC,QAAI,MAAM,IAAI,GAAG;AACf,aAAO,oBAAI,KAAK,GAAG;AAAA,IACzB;AAAA,EACA;AAEE,MAAI,YAAY,UAAU;AACxB,aAAS,cAAc,YAAY,QAAQ;AAC3C,QAAI,MAAM,MAAM,GAAG;AACjB,aAAO,oBAAI,KAAK,GAAG;AAAA,IACzB;AAAA,EACA,OAAS;AACL,UAAM,YAAY,IAAI,KAAK,YAAY,IAAI;AAM3C,UAAM,SAAS,oBAAI,KAAK,CAAC;AACzB,WAAO;AAAA,MACL,UAAU,eAAgB;AAAA,MAC1B,UAAU,YAAa;AAAA,MACvB,UAAU,WAAY;AAAA,IACvB;AACD,WAAO;AAAA,MACL,UAAU,YAAa;AAAA,MACvB,UAAU,cAAe;AAAA,MACzB,UAAU,cAAe;AAAA,MACzB,UAAU,mBAAoB;AAAA,IAC/B;AACD,WAAO;AAAA,EACX;AAEE,SAAO,IAAI,KAAK,YAAY,OAAO,MAAM;AAC3C;AAEA,MAAM,WAAW;AAAA,EACf,mBAAmB;AAAA,EACnB,mBAAmB;AAAA,EACnB,UAAU;AACZ;AAEA,MAAM,YACJ;AACF,MAAM,YACJ;AACF,MAAM,gBAAgB;AAEtB,SAAS,gBAAgB,YAAY;AACnC,QAAM,cAAc,CAAE;AACtB,QAAM,QAAQ,WAAW,MAAM,SAAS,iBAAiB;AACzD,MAAI;AAIJ,MAAI,MAAM,SAAS,GAAG;AACpB,WAAO;AAAA,EACX;AAEE,MAAI,IAAI,KAAK,MAAM,CAAC,CAAC,GAAG;AACtB,iBAAa,MAAM,CAAC;AAAA,EACxB,OAAS;AACL,gBAAY,OAAO,MAAM,CAAC;AAC1B,iBAAa,MAAM,CAAC;AACpB,QAAI,SAAS,kBAAkB,KAAK,YAAY,IAAI,GAAG;AACrD,kBAAY,OAAO,WAAW,MAAM,SAAS,iBAAiB,EAAE,CAAC;AACjE,mBAAa,WAAW;AAAA,QACtB,YAAY,KAAK;AAAA,QACjB,WAAW;AAAA,MACZ;AAAA,IACP;AAAA,EACA;AAEE,MAAI,YAAY;AACd,UAAM,QAAQ,SAAS,SAAS,KAAK,UAAU;AAC/C,QAAI,OAAO;AACT,kBAAY,OAAO,WAAW,QAAQ,MAAM,CAAC,GAAG,EAAE;AAClD,kBAAY,WAAW,MAAM,CAAC;AAAA,IACpC,OAAW;AACL,kBAAY,OAAO;AAAA,IACzB;AAAA,EACA;AAEE,SAAO;AACT;AAEA,SAAS,UAAU,YAAY,kBAAkB;AAC/C,QAAM,QAAQ,IAAI;AAAA,IAChB,0BACG,IAAI,oBACL,yBACC,IAAI,oBACL;AAAA,EACH;AAED,QAAM,WAAW,WAAW,MAAM,KAAK;AAEvC,MAAI,CAAC,SAAU,QAAO,EAAE,MAAM,KAAK,gBAAgB,GAAI;AAEvD,QAAM,OAAO,SAAS,CAAC,IAAI,SAAS,SAAS,CAAC,CAAC,IAAI;AACnD,QAAM,UAAU,SAAS,CAAC,IAAI,SAAS,SAAS,CAAC,CAAC,IAAI;AAGtD,SAAO;AAAA,IACL,MAAM,YAAY,OAAO,OAAO,UAAU;AAAA,IAC1C,gBAAgB,WAAW,OAAO,SAAS,CAAC,KAAK,SAAS,CAAC,GAAG,MAAM;AAAA,EACrE;AACH;AAEA,SAAS,UAAU,YAAY,MAAM;AAEnC,MAAI,SAAS,KAAM,QAAO,oBAAI,KAAK,GAAG;AAEtC,QAAM,WAAW,WAAW,MAAM,SAAS;AAE3C,MAAI,CAAC,SAAU,QAAO,oBAAI,KAAK,GAAG;AAElC,QAAM,aAAa,CAAC,CAAC,SAAS,CAAC;AAC/B,QAAM,YAAY,cAAc,SAAS,CAAC,CAAC;AAC3C,QAAM,QAAQ,cAAc,SAAS,CAAC,CAAC,IAAI;AAC3C,QAAM,MAAM,cAAc,SAAS,CAAC,CAAC;AACrC,QAAM,OAAO,cAAc,SAAS,CAAC,CAAC;AACtC,QAAM,YAAY,cAAc,SAAS,CAAC,CAAC,IAAI;AAE/C,MAAI,YAAY;AACd,QAAI,CAAC,iBAAiB,MAAM,MAAM,SAAS,GAAG;AAC5C,aAAO,oBAAI,KAAK,GAAG;AAAA,IACzB;AACI,WAAO,iBAAiB,MAAM,MAAM,SAAS;AAAA,EACjD,OAAS;AACL,UAAM,OAAO,oBAAI,KAAK,CAAC;AACvB,QACE,CAAC,aAAa,MAAM,OAAO,GAAG,KAC9B,CAAC,sBAAsB,MAAM,SAAS,GACtC;AACA,aAAO,oBAAI,KAAK,GAAG;AAAA,IACzB;AACI,SAAK,eAAe,MAAM,OAAO,KAAK,IAAI,WAAW,GAAG,CAAC;AACzD,WAAO;AAAA,EACX;AACA;AAEA,SAAS,cAAc,OAAO;AAC5B,SAAO,QAAQ,SAAS,KAAK,IAAI;AACnC;AAEA,SAAS,UAAU,YAAY;AAC7B,QAAM,WAAW,WAAW,MAAM,SAAS;AAC3C,MAAI,CAAC,SAAU,QAAO;AAEtB,QAAM,QAAQ,cAAc,SAAS,CAAC,CAAC;AACvC,QAAM,UAAU,cAAc,SAAS,CAAC,CAAC;AACzC,QAAM,UAAU,cAAc,SAAS,CAAC,CAAC;AAEzC,MAAI,CAAC,aAAa,OAAO,SAAS,OAAO,GAAG;AAC1C,WAAO;AAAA,EACX;AAEE,SACE,QAAQ,qBAAqB,UAAU,uBAAuB,UAAU;AAE5E;AAEA,SAAS,cAAc,OAAO;AAC5B,SAAQ,SAAS,WAAW,MAAM,QAAQ,KAAK,GAAG,CAAC,KAAM;AAC3D;AAEA,SAAS,cAAc,gBAAgB;AACrC,MAAI,mBAAmB,IAAK,QAAO;AAEnC,QAAM,WAAW,eAAe,MAAM,aAAa;AACnD,MAAI,CAAC,SAAU,QAAO;AAEtB,QAAM,OAAO,SAAS,CAAC,MAAM,MAAM,KAAK;AACxC,QAAM,QAAQ,SAAS,SAAS,CAAC,CAAC;AAClC,QAAM,UAAW,SAAS,CAAC,KAAK,SAAS,SAAS,CAAC,CAAC,KAAM;AAE1D,MAAI,CAAC,iBAAiB,OAAO,OAAO,GAAG;AACrC,WAAO;AAAA,EACX;AAEE,SAAO,QAAQ,QAAQ,qBAAqB,UAAU;AACxD;AAEA,SAAS,iBAAiB,aAAa,MAAM,KAAK;AAChD,QAAM,OAAO,oBAAI,KAAK,CAAC;AACvB,OAAK,eAAe,aAAa,GAAG,CAAC;AACrC,QAAM,qBAAqB,KAAK,UAAS,KAAM;AAC/C,QAAM,QAAQ,OAAO,KAAK,IAAI,MAAM,IAAI;AACxC,OAAK,WAAW,KAAK,WAAU,IAAK,IAAI;AACxC,SAAO;AACT;AAKA,MAAM,eAAe,CAAC,IAAI,MAAM,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAEtE,SAAS,gBAAgB,MAAM;AAC7B,SAAO,OAAO,QAAQ,KAAM,OAAO,MAAM,KAAK,OAAO,QAAQ;AAC/D;AAEA,SAAS,aAAa,MAAM,OAAO,MAAM;AACvC,SACE,SAAS,KACT,SAAS,MACT,QAAQ,KACR,SAAS,aAAa,KAAK,MAAM,gBAAgB,IAAI,IAAI,KAAK;AAElE;AAEA,SAAS,sBAAsB,MAAM,WAAW;AAC9C,SAAO,aAAa,KAAK,cAAc,gBAAgB,IAAI,IAAI,MAAM;AACvE;AAEA,SAAS,iBAAiB,OAAO,MAAM,KAAK;AAC1C,SAAO,QAAQ,KAAK,QAAQ,MAAM,OAAO,KAAK,OAAO;AACvD;AAEA,SAAS,aAAa,OAAO,SAAS,SAAS;AAC7C,MAAI,UAAU,IAAI;AAChB,WAAO,YAAY,KAAK,YAAY;AAAA,EACxC;AAEE,SACE,WAAW,KACX,UAAU,MACV,WAAW,KACX,UAAU,MACV,SAAS,KACT,QAAQ;AAEZ;AAEA,SAAS,iBAAiB,QAAQ,SAAS;AACzC,SAAO,WAAW,KAAK,WAAW;AACpC;", "x_google_ignoreList": [0]}