{"version": 3, "file": "coupons-sX9zH31d.js", "sources": ["../../../app/types/api/businessConsoleService/coupons.ts"], "sourcesContent": ["export interface CouponDetailsType {\r\n  id: number;\r\n  sellerId: number;\r\n  couponCode: string;\r\n  description: string;\r\n  discountType: string;\r\n  couponName: string;\r\n  discountValue: number;\r\n  usageCount: number;\r\n  totalUsers: number;\r\n  totalOrders: number;\r\n  totalDiscountAmount: string;\r\n  minOrderValue: number;\r\n  maxDiscountAmount: number;\r\n  maxUsageCount: number;\r\n  active: boolean;\r\n  validFrom: string;\r\n  validTo: string;\r\n}\r\n\r\nexport type ConfigKey = \"discount\" | \"filter\" | \"validity\";\r\nexport type ConfigAdditionalKey =\r\n  | \"discountAdditional\"\r\n  | \"filterAdditional\"\r\n  | \"validityAdditional\";\r\n\r\nexport enum configKeysEnum {\r\n  discount = \"discountAdditional\",\r\n  filter = \"filterAdditional\",\r\n  validity = \"validityAdditional\",\r\n}\r\n\r\nexport interface CouponPreloadType {\r\n  couponTypes: {\r\n    label: string;\r\n    value: string;\r\n  }[];\r\n  couponTypesConfigurations: {\r\n    label: string;\r\n    value: string;\r\n    configuration: {\r\n      [K in ConfigKey]: {\r\n        label: string;\r\n        value: string;\r\n        description: string;\r\n        properties: {\r\n          label: string;\r\n          value: string;\r\n          operatorConfig?: {\r\n            input: string;\r\n            options?: {\r\n              label: string;\r\n              value: string;\r\n            }[];\r\n          };\r\n          valueConfig?: {\r\n            input: string;\r\n            options?: {\r\n              label: string;\r\n              value: string;\r\n            }[];\r\n          };\r\n        }[];\r\n        propertyConfig?: {\r\n          input: string;\r\n          type: string;\r\n          label: string;\r\n          value: string;\r\n          options?: {\r\n            label: string;\r\n            value: string;\r\n          }[];\r\n        }[];\r\n      };\r\n    };\r\n  }[];\r\n}\r\n\r\nexport const DISCOUNT_UNIT_MAP: Record<string, string> = {\r\n  item: \"unit\",\r\n  amount: \"/-\",\r\n  discountFixed: \"/-\",\r\n  maxDiscountAmount: \"/-\",\r\n  minDiscountAmount: \"/-\",\r\n  percentage: \"%\",\r\n  discountPercent: \"%\",\r\n  weight: \"gm\",\r\n  volume: \"ltr\",\r\n};\r\n\r\nexport type IdConfigItem = {\r\n  id: string;\r\n  property: string;\r\n  operator?: string;\r\n  value: string;\r\n};\r\n\r\nexport interface CouponPayloadType {\r\n  couponType: string;\r\n  couponCode?: string;\r\n  description?: string;\r\n  customConfiguration?: {\r\n    discount: IdConfigItem[];\r\n    filter: IdConfigItem[];\r\n    validity: IdConfigItem[];\r\n    discountAdditional?: IdConfigItem[];\r\n    filterAdditional?: IdConfigItem[];\r\n    validityAdditional?: IdConfigItem[];\r\n  };\r\n}\r\n\r\nexport type ConfigItem = {\r\n  property: string;\r\n  operator?: string;\r\n  value: string;\r\n};\r\n\r\nexport interface AddCouponType {\r\n  code: string;\r\n  name: string;\r\n  description: string;\r\n  discountConfiguration?: ConfigItem[];\r\n  filterConfiguration?: ConfigItem[];\r\n  validityConfiguration?: ConfigItem[];\r\n}\r\n\r\nexport interface CouponType {\r\n  id: number;\r\n  code: string;\r\n  name: string;\r\n  description: string;\r\n  discountType?: string;\r\n  discountConfiguration?: ConfigItem[];\r\n  filterConfiguration?: ConfigItem[];\r\n  validityConfiguration?: ConfigItem[];\r\n}\r\n"], "names": ["configKeysEnum"], "mappings": "AA0BY,IAAA,mCAAAA,oBAAL;AACLA,kBAAA,UAAW,IAAA;AACXA,kBAAA,QAAS,IAAA;AACTA,kBAAA,UAAW,IAAA;AAHDA,SAAAA;AAAA,GAAA,kBAAA,CAAA,CAAA;AAoDL,MAAM,oBAA4C;AAAA,EACvD,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,eAAe;AAAA,EACf,mBAAmB;AAAA,EACnB,mBAAmB;AAAA,EACnB,YAAY;AAAA,EACZ,iBAAiB;AAAA,EACjB,QAAQ;AAAA,EACR,QAAQ;AACV;"}