import { j as jsxRuntimeExports } from "./jsx-runtime-bB2y7OuD.js";
import { u as useLoaderData } from "./components-D7UvGag_.js";
import "./index-Vp2vNLNM.js";
import "./index-DhHTcibu.js";
function PostLogin() {
  const {
    access_token,
    user
  } = useLoaderData();
  return /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
    className: "flex flex-col items-center justify-center min-h-screen bg-gray-100",
    children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
      className: "bg-white p-8 rounded shadow-md text-center",
      children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("h1", {
        className: "text-2xl font-bold mb-4",
        children: ["Welcome, ", user.userName, "!"]
      }), /* @__PURE__ */ jsxRuntimeExports.jsxs("p", {
        className: "text-gray-700 mb-2",
        children: [/* @__PURE__ */ jsxRuntimeExports.jsx("strong", {
          children: "Business Name:"
        }), " ", user.businessName]
      }), /* @__PURE__ */ jsxRuntimeExports.jsxs("p", {
        className: "text-gray-700",
        children: [/* @__PURE__ */ jsxRuntimeExports.jsx("strong", {
          children: "Authorized with token:"
        }), " ", access_token]
      })]
    })
  });
}
export {
  PostLogin as default
};
//# sourceMappingURL=postlogin-DVW84aVc.js.map
