{"version": 3, "file": "house.room._roomno-c9ic5ib5.js", "sources": ["../../../app/routes/house.room.$roomno.tsx"], "sourcesContent": ["import {useLoaderData} from \"@remix-run/react\";\r\nimport {LoaderFunctionArgs} from \"@remix-run/node\";\r\n\r\n\r\nexport const loader = async ({params}:LoaderFunctionArgs) => {\r\n\r\n    return {roomNo: params.roomno}\r\n\r\n}\r\ninterface LoaderData {\r\n    roomNo: string\r\n}\r\nexport default function Room(){\r\n    const {roomNo} = useLoaderData<LoaderData>()\r\n    return <div>Room: {roomNo}</div>\r\n}\r\n"], "names": ["Room", "roomNo", "useLoaderData", "children"], "mappings": ";;;;AAYA,SAAwBA,OAAM;AACpB,QAAA;AAAA,IAACC;AAAAA,EAAM,IAAIC,cAA0B;AAC3C,gDAAQ,OAAI;AAAA,IAAAC,UAAA,CAAA,UAAOF,MAAA;AAAA,EAAO,CAAA;AAC9B;"}