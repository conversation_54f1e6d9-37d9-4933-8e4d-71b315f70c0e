{"version": 3, "file": "home.place-order-pfe3YbU4.js", "sources": ["../../../app/routes/home.place-order.tsx"], "sourcesContent": ["import { LoaderFunction } from \"@remix-run/node\";\r\nimport { generateSessionToken } from \"~/utils/jwt\";\r\nimport { getSession } from \"~/utils/session.server\";\r\nimport { useLoaderData, useNavigate } from \"@remix-run/react\";\r\nimport { useEffect, useRef, useState } from \"react\";\r\nimport { IframeData, ServerEnv } from \"~/types\";\r\nimport ErrorBoundaryComponent from \"~/components/error/ErrorBoundary\";\r\nimport SpinnerLoader from \"~/components/loader/SpinnerLoader\";\r\nimport { Button } from \"~/components/ui/button\";\r\nimport ErrorModel from \"~/components/error/Error\";\r\n\r\ntype EventName = \"PLACE_ORDER\" | \"IFRAME_LOADED\" | \"IFRAME_ERROR\";\r\n\r\ninterface IframeMessage {\r\n  type: EventName;\r\n  payload: {\r\n    success: boolean;\r\n    message?: string;\r\n  };\r\n}\r\n\r\ninterface LoaderData {\r\n  buyerId: number;\r\n  token: string;\r\n  iframeData: IframeData;\r\n  serverEnv: ServerEnv;\r\n}\r\n\r\nexport const loader: LoaderFunction = async ({ request }) => {\r\n  const session = await getSession(request.headers.get(\"Cookie\"));\r\n  const access_token = session.get(\"access_token\");\r\n  const refresh_token = session.get(\"refresh_token\");\r\n  const url = new URL(request.url);\r\n  const mobileNumber = url.searchParams.get(\"mobileNumber\");\r\n  const buyerIdStr = url.searchParams.get(\"buyerId\");\r\n\r\n  if (!access_token || !refresh_token || !mobileNumber || !buyerIdStr) {\r\n    throw Response.json(\r\n      {},\r\n      { status: 404, statusText: \"Invalid Mobile or Buyer ID\" }\r\n    );\r\n  }\r\n\r\n  const buyerId = parseInt(buyerIdStr, 10);\r\n\r\n  const data: IframeData = {\r\n    buyerData: {\r\n      buyerId,\r\n      mobileNumber,\r\n    },\r\n    sellerData: {\r\n      sellerId: 1,\r\n      mobileNumber: \"\",\r\n    },\r\n  };\r\n\r\n  const tokenForIframe = generateSessionToken({\r\n    access_token,\r\n    refresh_token,\r\n    data,\r\n  });\r\n\r\n  return {\r\n    buyerId,\r\n    token: tokenForIframe,\r\n    iframeData: data,\r\n    serverEnv: process.env.SERVER_ENV as ServerEnv,\r\n  };\r\n};\r\n\r\nexport default function PlaceOrder() {\r\n  const navigate = useNavigate();\r\n  const [iframeLoading, setIframeLoading] = useState(true);\r\n  const [error, setError] = useState(\"\");\r\n  const loader = useLoaderData<LoaderData>();\r\n  const token = loader.token;\r\n  const iframeRef = useRef<HTMLIFrameElement>(null);\r\n\r\n  useEffect(() => {\r\n    if (typeof window !== \"undefined\") {\r\n      // Add listener for messages from the child\r\n      const handleMessage = (event: MessageEvent) => {\r\n        console.log(\"Message received from Child:\", event.data);\r\n        const message = event.data as IframeMessage;\r\n        if (message.type === \"PLACE_ORDER\") {\r\n          if (message.payload.success) {\r\n            navigate(`/home/<USER>/${loader.buyerId}`);\r\n          } else {\r\n            setError(\"Unable to place order\");\r\n          }\r\n        }\r\n        if (message.type === \"IFRAME_LOADED\") {\r\n          if (message.payload.success) {\r\n            setIframeLoading(false);\r\n          } else {\r\n            setIframeLoading(false);\r\n            setError(\"Something went wrong\");\r\n          }\r\n        }\r\n\r\n        if (message.type === \"IFRAME_ERROR\") {\r\n          console.error(\"IFRAME Error: \", message.payload.message);\r\n          setIframeLoading(false);\r\n          setError(message.payload.message || \"Something went wrong\");\r\n        }\r\n      };\r\n\r\n      window.addEventListener(\"message\", handleMessage);\r\n\r\n      // Cleanup\r\n      return () => {\r\n        window.removeEventListener(\"message\", handleMessage);\r\n      };\r\n    }\r\n  }, []);\r\n\r\n  // const sendMessageToChild = () => {\r\n  //   if (iframeRef.current?.contentWindow) {\r\n  //     iframeRef.current.contentWindow.postMessage({ type: \"GREETING\", data: \"Hello from Parent!\" }, \"*\");\r\n  //   }\r\n  // };\r\n\r\n  const handleCloseError = () => {\r\n    setError(\"\");\r\n    navigate(-1);\r\n  };\r\n\r\n  if (!iframeLoading && error.length) {\r\n    setIframeLoading(false);\r\n    return (\r\n      <ErrorModel\r\n        title={\"Oops\"}\r\n        message={error || \"Something went wrong!\"}\r\n        onClose={handleCloseError}\r\n        buttonType=\"primary\"\r\n        buttonText=\"Retry\"\r\n      />\r\n    );\r\n  }\r\n  console.log(loader.serverEnv);\r\n  return (\r\n    <div className=\"fixed inset-0 flex items-center justify-center bg-black bg-opacity-40 p-4 overflow-y-scroll z-50 h-full w-full\">\r\n      {iframeLoading && <SpinnerLoader loading={iframeLoading} />}\r\n      <div className=\"flex flex-col min-h-[80%] min-w-[412px]\">\r\n        <div className=\"flex flex-row justify-between items-center bg-white w-full p-4 h-14 mb-2  rounded-md\">\r\n          <div className=\"text-lg font-semibold\">\r\n            {\"Place order for\"}\r\n            <span className=\"text-teal-500 ml-4\">\r\n              {loader.iframeData.buyerData.mobileNumber}\r\n            </span>\r\n          </div>\r\n          <Button\r\n            onClick={() => navigate(-1)}\r\n            className=\"bg-red-500 px-4 py-3 rounded-md text-lg hover:bg-red-600\"\r\n          >\r\n            Cancel\r\n          </Button>\r\n        </div>\r\n        <iframe\r\n          ref={iframeRef}\r\n          className=\"flex-grow overflow-y-scroll rounded-md w-full\"\r\n          title=\"chooseitems\"\r\n          src={`https://po${\r\n            loader.serverEnv === \"development\" ? \"-uat\" : \"\"\r\n          }.mnetlive.com/seller/chooseitems?token=${token}`}\r\n          // style={{ width: '480px', height: '700px', border: 'none' }}\r\n        />\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport function ErrorBoundary() {\r\n  const navigate = useNavigate();\r\n  return <ErrorBoundaryComponent onRetry={() => navigate(-1)} />;\r\n}\r\n"], "names": ["PlaceOrder", "navigate", "useNavigate", "iframeLoading", "setIframeLoading", "useState", "error", "setError", "loader", "useLoaderData", "token", "iframeRef", "useRef", "useEffect", "window", "handleMessage", "event", "console", "log", "data", "message", "type", "payload", "success", "buyerId", "addEventListener", "removeEventListener", "handleCloseError", "length", "jsx", "ErrorModel", "title", "onClose", "buttonType", "buttonText", "serverEnv", "jsxs", "className", "children", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "loading", "iframeData", "buyerData", "mobileNumber", "<PERSON><PERSON>", "onClick", "ref", "src", "Error<PERSON>ou<PERSON><PERSON>", "ErrorBoundaryComponent", "onRetry"], "mappings": ";;;;;;;;;;;AAsEA,SAAwBA,aAAa;AACnC,QAAMC,WAAWC,YAAY;AAC7B,QAAM,CAACC,eAAeC,gBAAgB,IAAIC,aAAAA,SAAS,IAAI;AACvD,QAAM,CAACC,OAAOC,QAAQ,IAAIF,aAAAA,SAAS,EAAE;AACrC,QAAMG,UAASC,cAA0B;AACzC,QAAMC,QAAQF,QAAOE;AACf,QAAAC,YAAYC,oBAA0B,IAAI;AAEhDC,eAAAA,UAAU,MAAM;AACV,QAAA,OAAOC,WAAW,aAAa;AAE3B,YAAAC,gBAAiBC,WAAwB;AACrCC,gBAAAC,IAAI,gCAAgCF,MAAMG,IAAI;AACtD,cAAMC,UAAUJ,MAAMG;AAClB,YAAAC,QAAQC,SAAS,eAAe;AAC9B,cAAAD,QAAQE,QAAQC,SAAS;AAClBtB,qBAAA,0BAA0BO,QAAOgB,OAAO,EAAE;AAAA,UACrD,OAAO;AACLjB,qBAAS,uBAAuB;AAAA,UAClC;AAAA,QACF;AACI,YAAAa,QAAQC,SAAS,iBAAiB;AAChC,cAAAD,QAAQE,QAAQC,SAAS;AAC3BnB,6BAAiB,KAAK;AAAA,UACxB,OAAO;AACLA,6BAAiB,KAAK;AACtBG,qBAAS,sBAAsB;AAAA,UACjC;AAAA,QACF;AAEI,YAAAa,QAAQC,SAAS,gBAAgB;AACnCJ,kBAAQX,MAAM,kBAAkBc,QAAQE,QAAQF,OAAO;AACvDhB,2BAAiB,KAAK;AACbG,mBAAAa,QAAQE,QAAQF,WAAW,sBAAsB;AAAA,QAC5D;AAAA,MACF;AAEON,aAAAW,iBAAiB,WAAWV,aAAa;AAGhD,aAAO,MAAM;AACJD,eAAAY,oBAAoB,WAAWX,aAAa;AAAA,MACrD;AAAA,IACF;AAAA,EACF,GAAG,EAAE;AAQL,QAAMY,mBAAmBA,MAAM;AAC7BpB,aAAS,EAAE;AACXN,aAAS,EAAE;AAAA,EACb;AAEI,MAAA,CAACE,iBAAiBG,MAAMsB,QAAQ;AAClCxB,qBAAiB,KAAK;AAEpB,WAAAyB,kCAAAA,IAACC,eAAA;AAAA,MACCC,OAAO;AAAA,MACPX,SAASd,SAAS;AAAA,MAClB0B,SAASL;AAAAA,MACTM,YAAW;AAAA,MACXC,YAAW;AAAA,IAAA,CACb;AAAA,EAEJ;AACQjB,UAAAC,IAAIV,QAAO2B,SAAS;AAE1B,SAAAC,kCAAAA,KAAC,OAAI;AAAA,IAAAC,WAAU;AAAA,IACZC,UAAA,CAAiBnC,iBAAA0B,kCAAA,IAACU,eAAc;AAAA,MAAAC,SAASrC;AAAAA,IAAe,CAAA,GACzDiC,kCAAA,KAAC,OAAI;AAAA,MAAAC,WAAU;AAAA,MACbC,UAAA,CAACF,kCAAA,KAAA,OAAA;AAAA,QAAIC,WAAU;AAAA,QACbC,UAAA,CAACF,kCAAA,KAAA,OAAA;AAAA,UAAIC,WAAU;AAAA,UACZC,UAAA,CAAA,mBACDT,kCAAA,IAAC;YAAKQ,WAAU;AAAA,YACbC,UAAA9B,QAAOiC,WAAWC,UAAUC;AAAAA,UAC/B,CAAA,CAAA;AAAA,QACF,CAAA,GACAd,kCAAA,IAACe,QAAA;AAAA,UACCC,SAASA,MAAM5C,SAAS,EAAE;AAAA,UAC1BoC,WAAU;AAAA,UACXC,UAAA;AAAA,QAAA,CAED,CAAA;AAAA,MACF,CAAA,GACAT,kCAAA,IAAC,UAAA;AAAA,QACCiB,KAAKnC;AAAAA,QACL0B,WAAU;AAAA,QACVN,OAAM;AAAA,QACNgB,KAAK,aACHvC,QAAO2B,cAAc,gBAAgB,SAAS,EAChD,0CAA0CzB,KAAK;AAAA,MAAA,CAEjD,CAAA;AAAA,IACF,CAAA,CAAA;AAAA,EACF,CAAA;AAEJ;AAEO,SAASsC,gBAAgB;AAC9B,QAAM/C,WAAWC,YAAY;AAC7B,+CAAQ+C,iBAAuB;AAAA,IAAAC,SAASA,MAAMjD,SAAS,EAAE;AAAA,EAAG,CAAA;AAC9D;"}