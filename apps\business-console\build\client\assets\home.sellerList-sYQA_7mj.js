import { r as reactExports, j as jsxRuntimeExports } from "./jsx-runtime-bB2y7OuD.js";
import { B as Button } from "./button-ByAXMyvk.js";
import { R as ResponsiveTable } from "./responsiveTable-CMOWL3Wl.js";
import { u as useLoaderData } from "./components-D7UvGag_.js";
import { a as useNavigate } from "./index-DhHTcibu.js";
import "./index-z_byfFrQ.js";
import "./index-ImHKLo0a.js";
import "./utils-GkgzjW3c.js";
import "./index-Vp2vNLNM.js";
function SellerList() {
  const sellerList = useLoaderData();
  const [searchTerm, setSearchTerm] = reactExports.useState("");
  const navigate = useNavigate();
  const SellerHeaders = ["Id", "Seller Name", "Status", "", ""];
  return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
    className: "container mx-auto p-6",
    children: [/* @__PURE__ */ jsxRuntimeExports.jsx("div", {
      className: "flex justify-between items-center mb-6",
      children: /* @__PURE__ */ jsxRuntimeExports.jsx("h1", {
        className: "text-2xl font-bold",
        children: "Sellers"
      })
    }), /* @__PURE__ */ jsxRuntimeExports.jsx(ResponsiveTable, {
      headers: SellerHeaders,
      data: sellerList == null ? void 0 : sellerList.data,
      renderRow: (row) => /* @__PURE__ */ jsxRuntimeExports.jsxs("tr", {
        className: "border-b",
        children: [/* @__PURE__ */ jsxRuntimeExports.jsx("td", {
          className: "py-2 px-3 font-medium text-left",
          children: row.id
        }), /* @__PURE__ */ jsxRuntimeExports.jsxs("td", {
          className: "py-2  text-right text-blue-500 cursor-pointer flex-wrap",
          children: [/* @__PURE__ */ jsxRuntimeExports.jsx("span", {
            onClick: () => navigate(`/home/<USER>
            children: row.name
          }), "  ", (row == null ? void 0 : row.name) || "-"]
        }), /* @__PURE__ */ jsxRuntimeExports.jsx("td", {
          className: "py-2 px-3 text-right",
          children: /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
            className: `px-2 py-1 rounded-full text-xs ${row.enabled === true ? "bg-red-100 text-red-800" : "bg-green-200 text-green-800"}`,
            children: (row == null ? void 0 : row.enabled) === true ? "Disabled" : "Active"
          })
        }), /* @__PURE__ */ jsxRuntimeExports.jsx("td", {
          className: "py-2 px-20 text-right"
        })]
      }, row.id)
    }), /* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
      className: "fixed bottom-5 right-5 rounded-full",
      onClick: () => navigate("/home/<USER>"),
      children: "+ Add Seller"
    })]
  });
}
export {
  SellerList as default
};
//# sourceMappingURL=home.sellerList-sYQA_7mj.js.map
