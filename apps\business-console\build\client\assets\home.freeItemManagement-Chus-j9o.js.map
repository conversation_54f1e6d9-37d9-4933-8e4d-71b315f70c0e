{"version": 3, "file": "home.freeItemManagement-Chus-j9o.js", "sources": ["../../../app/types/api/mockData/mockdata.ts", "../../../app/components/FreeItems/FreeItemCard.tsx", "../../../app/components/FreeItems/freeItemForm.tsx", "../../../app/components/FreeItems/FreeItemModal.tsx", "../../../app/routes/home.freeItemManagement.tsx"], "sourcesContent": ["import { DiscountType, FreeItem } from \"../businessConsoleService/FreeItem\";\r\n\r\n\r\nexport const DISCOUNT_TYPES: { value: DiscountType; label: string }[] = [\r\n  { value: \"percentage\", label: \"Percentage Discount\" },\r\n  { value: \"flat\", label: \"Flat Discount\" },\r\n  { value: \"freeitem\", label: \"Free Item\" },\r\n  { value: \"buyonegetone\", label: \"Buy One Get One\" },\r\n];\r\n\r\nexport const FREE_ITEM_OPTIONS = [\r\n  { value: \"item1\", label: \"Item 1\" },\r\n  { value: \"item2\", label: \"Item 2\" },\r\n  { value: \"item3\", label: \"Item 3\" },\r\n  { value: \"item4\", label: \"Item 4\" },\r\n  { value: \"item5\", label: \"Item 5\" },\r\n];\r\n\r\nexport const MOCK_FREE_ITEMS: FreeItem[] = [\r\n  {\r\n    id: \"1\",\r\n    discountPercentage: 15,\r\n    discountUpto: 500,\r\n    discountMinOrderQty: 1000,\r\n    validFrom: new Date(2023, 4, 1),\r\n    validTo: new Date(2023, 6, 30),\r\n    discountDisabled: false,\r\n    discountType: \"percentage\",\r\n    createdAt: new Date(2023, 3, 15),\r\n    updatedAt: new Date(2023, 3, 15),\r\n  },\r\n  {\r\n    id: \"2\",\r\n    discountFlat: 200,\r\n    discountMinOrderQty: 500,\r\n    validFrom: new Date(2023, 5, 1),\r\n    validTo: new Date(2023, 7, 31),\r\n    discountDisabled: false,\r\n    discountType: \"flat\",\r\n    createdAt: new Date(2023, 4, 10),\r\n    updatedAt: new Date(2023, 4, 10),\r\n  },\r\n  {\r\n    id: \"3\",\r\n    freeItemId: \"item1\",\r\n    freeItemQty: 1,\r\n    discountMinOrderQty: 1500,\r\n    validFrom: new Date(2023, 5, 15),\r\n    validTo: new Date(2023, 8, 15),\r\n    discountDisabled: false,\r\n    discountType: \"freeitem\",\r\n    createdAt: new Date(2023, 5, 1),\r\n    updatedAt: new Date(2023, 5, 1),\r\n  },\r\n  {\r\n    id: \"4\",\r\n    validFrom: new Date(2023, 6, 1),\r\n    validTo: new Date(2023, 9, 30),\r\n    discountDisabled: true,\r\n    discountType: \"buyonegetone\",\r\n    createdAt: new Date(2023, 5, 20),\r\n    updatedAt: new Date(2023, 5, 20),\r\n  },\r\n  {\r\n    id: \"5\",\r\n    discountPercentage: 20,\r\n    discountUpto: 1000,\r\n    discountMinOrderQty: 2000,\r\n    validFrom: new Date(2023, 7, 1),\r\n    validTo: new Date(2023, 10, 31),\r\n    discountDisabled: false,\r\n    discountType: \"percentage\",\r\n    createdAt: new Date(2023, 6, 15),\r\n    updatedAt: new Date(2023, 6, 15),\r\n  },\r\n];", "import React from \"react\";\r\nimport { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from \"../ui/card\";\r\nimport { Badge } from \"../ui/badge\";\r\nimport { FreeItem } from \"~/types/api/businessConsoleService/FreeItem\";\r\nimport { DISCOUNT_TYPES } from \"~/types/api/mockData/mockdata\";\r\nimport { Button } from \"../ui/button\";\r\n\r\n\r\ninterface FreeItemCardProps {\r\n  item: FreeItem;\r\n  onEdit: (item: FreeItem) => void;\r\n}\r\n\r\n// Utility function to format date as 'DD MMM YYYY'\r\nconst formatDate = (date: Date | string) => {\r\n  const d = typeof date === \"string\" ? new Date(date) : date;\r\n  return d.toLocaleDateString(undefined, {\r\n    day: \"2-digit\",\r\n    month: \"short\",\r\n    year: \"numeric\",\r\n  });\r\n};\r\n\r\nconst FreeItemCard: React.FC<FreeItemCardProps> = ({ item, onEdit }) => {\r\n  const getDiscountTypeLabel = (type: string) => {\r\n    return DISCOUNT_TYPES.find((t) => t.value === type)?.label || type;\r\n  };\r\n\r\n  const getDiscountValue = () => {\r\n    switch (item.discountType) {\r\n      case \"percentage\":\r\n        return `${item.discountPercentage}% Off`;\r\n      case \"flat\":\r\n        return `₹${item.discountFlat} Off`;\r\n      case \"freeitem\":\r\n        return `Free Item x${item.freeItemQty}`;\r\n      case \"buyonegetone\":\r\n        return \"Buy 1 Get 1 Free\";\r\n      default:\r\n        return \"Special Offer\";\r\n    }\r\n  };\r\n\r\n  const now = new Date();\r\n  const isActive = now >= item.validFrom && now <= item.validTo && !item.discountDisabled;\r\n\r\n  return (\r\n    <Card className=\"h-full overflow-hidden shadow-md hover:shadow-lg transition-shadow duration-300\">\r\n      <CardHeader className=\"p-4\">\r\n        <div className=\"flex justify-between items-start\">\r\n          <div className=\"flex flex-col\">\r\n            <p className=\"text-lg font-semibold\">\r\n              {getDiscountValue()}\r\n            </p>\r\n            <p className=\"text-sm text-muted-foreground\">\r\n              {getDiscountTypeLabel(item.discountType)}\r\n            </p>\r\n          </div>\r\n          <Badge variant={isActive ? \"default\" : \"outline\"} className={isActive ? \"bg-green-500\" : \"\"}>\r\n            {isActive ? \"Active\" : \"Inactive\"}\r\n          </Badge>\r\n        </div>\r\n      </CardHeader>\r\n      <CardContent className=\"p-4 pt-0\">\r\n        <div className=\"space-y-2\">\r\n          {item.discountMinOrderQty && (\r\n            <div className=\"flex items-center justify-between\">\r\n              <span className=\"text-sm text-muted-foreground\">Min. Order</span>\r\n              <span className=\"font-medium\">₹{item.discountMinOrderQty}</span>\r\n            </div>\r\n          )}\r\n          {item.discountUpto && (\r\n            <div className=\"flex items-center justify-between\">\r\n              <span className=\"text-sm text-muted-foreground\">Max Discount</span>\r\n              <span className=\"font-medium\">₹{item.discountUpto}</span>\r\n            </div>\r\n          )}\r\n          <div className=\"flex items-center justify-between\">\r\n            <span className=\"text-sm text-muted-foreground\">Valid From</span>\r\n            <span className=\"font-medium\">{formatDate(item.validFrom)}</span>\r\n          </div>\r\n          <div className=\"flex items-center justify-between\">\r\n            <span className=\"text-sm text-muted-foreground\">Valid Until</span>\r\n            <span className=\"font-medium\">{formatDate(item.validTo)}</span>\r\n          </div>\r\n        </div>\r\n      </CardContent>\r\n      <CardFooter className=\"p-4 pt-0\">\r\n        <Button \r\n          variant=\"outline\" \r\n          className=\"w-full\"\r\n          onClick={() => onEdit(item)}\r\n        >\r\n          Edit Offer\r\n        </Button>\r\n      </CardFooter>\r\n    </Card>\r\n  );\r\n};\r\n\r\nexport default FreeItemCard;", "import React, { useEffect } from \"react\";\r\nimport { FreeItemFormData } from \"~/types/api/businessConsoleService/FreeItem\";\r\n\r\ninterface FreeItemFormProps {\r\n  initialData?: FreeItemFormData;\r\n  onSubmit: (data: FreeItemFormData) => void;\r\n}\r\n\r\nconst defaultValues: FreeItemFormData = {\r\n  discountType: \"percentage\",\r\n  discountPercentage: null,\r\n  discountFlat: null,\r\n  discountUpto: null,\r\n  discountMinOrderQty: null,\r\n  validFrom: new Date(),\r\n  validTo: new Date(new Date().setMonth(new Date().getMonth() + 1)),\r\n  discountDisabled: false,\r\n  freeItemId: null,\r\n  freeItemQty: null,\r\n};\r\n\r\nconst FreeItemForm: React.FC<FreeItemFormProps> = ({ initialData, onSubmit }) => {\r\n  const [form, setForm] = React.useState<FreeItemFormData>(initialData || defaultValues);\r\n\r\n  useEffect(() => {\r\n    if (initialData) setForm(initialData);\r\n  }, [initialData]);\r\n\r\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {\r\n    const target = e.target as HTMLInputElement;\r\n    const { name, value, type } = target;\r\n    setForm((prev) => ({\r\n      ...prev,\r\n      [name]: type === \"checkbox\" ? target.checked : value,\r\n    }));\r\n  };\r\n\r\n  const handleDateChange = (name: keyof FreeItemFormData, value: string) => {\r\n    setForm((prev) => ({ ...prev, [name]: new Date(value) }));\r\n  };\r\n\r\n  const handleSubmit = (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n    onSubmit(form);\r\n  };\r\n\r\n  return (\r\n    <form onSubmit={handleSubmit} className=\"space-y-6\">\r\n      <div>\r\n        <label className=\"block font-medium mb-1\">Discount Type</label>\r\n        <select\r\n          name=\"discountType\"\r\n          value={form.discountType}\r\n          onChange={handleChange}\r\n          className=\"w-full border rounded px-3 py-2\"\r\n        >\r\n          <option value=\"percentage\">Percentage</option>\r\n          <option value=\"flat\">Flat</option>\r\n          <option value=\"freeitem\">Free Item</option>\r\n        </select>\r\n      </div>\r\n\r\n      {form.discountType === \"percentage\" && (\r\n        <>\r\n          <div>\r\n            <label className=\"block font-medium mb-1\">Discount Percentage (%)</label>\r\n            <input\r\n              type=\"number\"\r\n              name=\"discountPercentage\"\r\n              min={0}\r\n              max={100}\r\n              value={form.discountPercentage ?? \"\"}\r\n              onChange={handleChange}\r\n              className=\"w-full border rounded px-3 py-2\"\r\n              placeholder=\"Enter percentage\"\r\n            />\r\n          </div>\r\n          <div>\r\n            <label className=\"block font-medium mb-1\">Discount Up To (₹)</label>\r\n            <input\r\n              type=\"number\"\r\n              name=\"discountUpto\"\r\n              min={0}\r\n              value={form.discountUpto ?? \"\"}\r\n              onChange={handleChange}\r\n              className=\"w-full border rounded px-3 py-2\"\r\n              placeholder=\"Enter maximum discount amount\"\r\n            />\r\n          </div>\r\n        </>\r\n      )}\r\n\r\n      {form.discountType === \"flat\" && (\r\n        <div>\r\n          <label className=\"block font-medium mb-1\">Flat Discount Amount (₹)</label>\r\n          <input\r\n            type=\"number\"\r\n            name=\"discountFlat\"\r\n            min={0}\r\n            value={form.discountFlat ?? \"\"}\r\n            onChange={handleChange}\r\n            className=\"w-full border rounded px-3 py-2\"\r\n            placeholder=\"Enter flat discount amount\"\r\n          />\r\n        </div>\r\n      )}\r\n\r\n      {form.discountType === \"freeitem\" && (\r\n        <>\r\n          <div>\r\n            <label className=\"block font-medium mb-1\">Free Item</label>\r\n            <input\r\n              type=\"text\"\r\n              name=\"freeItemId\"\r\n              value={form.freeItemId ?? \"\"}\r\n              onChange={handleChange}\r\n              className=\"w-full border rounded px-3 py-2\"\r\n              placeholder=\"Enter free item ID\"\r\n            />\r\n          </div>\r\n          <div>\r\n            <label className=\"block font-medium mb-1\">Free Item Quantity</label>\r\n            <input\r\n              type=\"number\"\r\n              name=\"freeItemQty\"\r\n              min={1}\r\n              value={form.freeItemQty ?? \"\"}\r\n              onChange={handleChange}\r\n              className=\"w-full border rounded px-3 py-2\"\r\n              placeholder=\"Enter quantity\"\r\n            />\r\n          </div>\r\n        </>\r\n      )}\r\n\r\n      <div>\r\n        <label className=\"block font-medium mb-1\">Minimum Order Amount (₹)</label>\r\n        <input\r\n          type=\"number\"\r\n          name=\"discountMinOrderQty\"\r\n          min={0}\r\n          value={form.discountMinOrderQty ?? \"\"}\r\n          onChange={handleChange}\r\n          className=\"w-full border rounded px-3 py-2\"\r\n          placeholder=\"Enter minimum order amount\"\r\n        />\r\n      </div>\r\n\r\n      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\r\n        <div>\r\n          <label className=\"block font-medium mb-1\">Valid From</label>\r\n          <input\r\n            type=\"date\"\r\n            name=\"validFrom\"\r\n            value={form.validFrom ? new Date(form.validFrom).toISOString().split(\"T\")[0] : \"\"}\r\n            onChange={(e) => handleDateChange(\"validFrom\", e.target.value)}\r\n            className=\"w-full border rounded px-3 py-2\"\r\n          />\r\n        </div>\r\n        <div>\r\n          <label className=\"block font-medium mb-1\">Valid To</label>\r\n          <input\r\n            type=\"date\"\r\n            name=\"validTo\"\r\n            value={form.validTo ? new Date(form.validTo).toISOString().split(\"T\")[0] : \"\"}\r\n            onChange={(e) => handleDateChange(\"validTo\", e.target.value)}\r\n            className=\"w-full border rounded px-3 py-2\"\r\n          />\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"flex items-center gap-2\">\r\n        <input\r\n          type=\"checkbox\"\r\n          name=\"discountDisabled\"\r\n          checked={form.discountDisabled}\r\n          onChange={handleChange}\r\n          className=\"h-4 w-4\"\r\n        />\r\n        <label className=\"font-medium\">Disable Discount</label>\r\n      </div>\r\n\r\n      <div className=\"flex justify-end space-x-4 pt-4\">\r\n        <button type=\"submit\" className=\"bg-blue-600 text-white px-4 py-2 rounded\">\r\n          {initialData?.id ? \"Update\" : \"Create\"} Free Item Offer\r\n        </button>\r\n      </div>\r\n    </form>\r\n  );\r\n};\r\n\r\nexport default FreeItemForm;", "\r\nimport React from \"react\";\r\nimport { FreeItemFormData } from \"~/types/api/businessConsoleService/FreeItem\";\r\nimport { Dialog, DialogContent, DialogHeader, DialogTitle } from \"../ui/dialog\";\r\nimport FreeItemForm from \"./freeItemForm\";\r\nimport { toast } from \"~/hooks/use-toast\";\r\n\r\ninterface FreeItemModalProps {\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n  onSubmit: (data: FreeItemFormData) => void;\r\n  initialData?: FreeItemFormData;\r\n}\r\n\r\nconst FreeItemModal: React.FC<FreeItemModalProps> = ({\r\n  isOpen,\r\n  onClose,\r\n  onSubmit,\r\n  initialData,\r\n}) => {\r\n  const handleSubmit = (data: FreeItemFormData) => {\r\n    onSubmit(data);\r\n    toast({\r\n      description: initialData?.id ? \"Free item offer updated!\" : \"Free item offer created!\"\r\n    });\r\n    onClose();\r\n  };\r\n\r\n  return (\r\n    <Dialog open={isOpen} onOpenChange={onClose}>\r\n      <DialogContent className=\"sm:max-w-[600px]\">\r\n        <DialogHeader>\r\n          <DialogTitle>\r\n            {initialData?.id ? \"Edit\" : \"Create\"} Free Item Offer\r\n          </DialogTitle>\r\n        </DialogHeader>\r\n        <FreeItemForm initialData={initialData} onSubmit={handleSubmit} />\r\n      </DialogContent>\r\n    </Dialog>\r\n  );\r\n};\r\n\r\nexport default FreeItemModal;", "import type { LoaderFunction, ActionFunction } from \"@remix-run/node\";\r\nimport { json } from \"@remix-run/node\";\r\nimport { useLoaderData, useFetcher } from \"@remix-run/react\";\r\nimport React, { useMemo, useState, useEffect } from \"react\";\r\nimport { Search } from \"lucide-react\";\r\nimport { FreeItem, FreeItemFormData } from \"~/types/api/businessConsoleService/FreeItem\";\r\nimport { MOCK_FREE_ITEMS } from \"~/types/api/mockData/mockdata\";\r\nimport { Button } from \"~/components/ui/button\";\r\nimport { Input } from \"~/components/ui/input\";\r\nimport FreeItemCard from \"~/components/FreeItems/FreeItemCard\";\r\nimport FreeItemModal from \"~/components/FreeItems/FreeItemModal\";\r\n\r\n// --- Loader: returns mock data ---\r\nexport const loader: LoaderFunction = async () => {\r\n  return json({ items: MOCK_FREE_ITEMS });\r\n};\r\n\r\n// --- Action: handles create/update in-memory (mock) ---\r\nlet freeItems = [...MOCK_FREE_ITEMS]; // In-memory mock, resets on server restart\r\n\r\nexport const action: ActionFunction = async ({ request }) => {\r\n  const formData = await request.formData();\r\n  const intent = formData.get(\"_intent\");\r\n  const data: any = Object.fromEntries(formData);\r\n\r\n  if (intent === \"create\" || intent === \"update\") {\r\n    const item: FreeItem = {\r\n      id: data.id || Math.random().toString(36).substring(2, 11),\r\n      discountType: data.discountType,\r\n      discountPercentage: data.discountPercentage ? Number(data.discountPercentage) : undefined,\r\n      discountFlat: data.discountFlat ? Number(data.discountFlat) : undefined,\r\n      discountUpto: data.discountUpto ? Number(data.discountUpto) : undefined,\r\n      discountMinOrderQty: data.discountMinOrderQty ? Number(data.discountMinOrderQty) : undefined,\r\n      freeItemQty: data.freeItemQty ? Number(data.freeItemQty) : undefined,\r\n      freeItemId: data.freeItemId ? data.freeItemId : undefined,\r\n      validFrom: new Date(data.validFrom),\r\n      validTo: new Date(data.validTo),\r\n      discountDisabled: data.discountDisabled === \"true\",\r\n      createdAt: data.createdAt ? new Date(data.createdAt) : new Date(),\r\n      updatedAt: new Date(),\r\n    };\r\n    if (intent === \"update\") {\r\n      freeItems = freeItems.map((i) => (i.id === item.id ? item : i));\r\n    } else {\r\n      freeItems.push(item);\r\n    }\r\n    return json({ success: true, item });\r\n  }\r\n  return json({ success: false });\r\n};\r\n\r\n\r\n\r\n\r\nconst Index: React.FC = () => {\r\n  const { items: initialItems } = useLoaderData<{ items: FreeItem[] }>();\r\n  const fetcher = useFetcher<any>();\r\n  const [items, setItems] = useState<FreeItem[]>(initialItems);\r\n  const [searchQuery, setSearchQuery] = useState<string>(\"\");\r\n  const [modalOpen, setModalOpen] = useState<boolean>(false);\r\n  const [currentItem, setCurrentItem] = useState<FreeItemFormData | undefined>(undefined);\r\n\r\n  // Update local state when fetcher returns a new item\r\n  useEffect(() => {\r\n    if (fetcher.data?.success && fetcher.data.item) {\r\n      setItems((prev) => {\r\n        const exists = prev.some((i) => i.id === fetcher.data.item.id);\r\n        if (exists) {\r\n          return prev.map((i) => (i.id === fetcher.data.item.id ? fetcher.data.item : i));\r\n        } else {\r\n          return [...prev, fetcher.data.item];\r\n        }\r\n      });\r\n      setModalOpen(false);\r\n    }\r\n  }, [fetcher.data]);\r\n\r\n  const filteredItems = useMemo(() => {\r\n    return items.filter((item) => {\r\n      const searchLower = searchQuery.toLowerCase();\r\n      if (item.discountType && item.discountType.toLowerCase().includes(searchLower)) return true;\r\n      if (item.discountPercentage && item.discountPercentage.toString().includes(searchQuery)) return true;\r\n      if (item.discountFlat && item.discountFlat.toString().includes(searchQuery)) return true;\r\n      if (item.discountMinOrderQty && item.discountMinOrderQty.toString().includes(searchQuery)) return true;\r\n      return false;\r\n    });\r\n  }, [items, searchQuery]);\r\n\r\n  const handleCreateClick = () => {\r\n    setCurrentItem(undefined);\r\n    setModalOpen(true);\r\n  };\r\n\r\n  const handleEditItem = (item: FreeItem) => {\r\n    setCurrentItem(item);\r\n    setModalOpen(true);\r\n  };\r\n\r\n  const handleSubmitItem = (data: FreeItemFormData) => {\r\n    const formData = new FormData();\r\n    Object.entries(data).forEach(([key, value]) => {\r\n      if (value !== undefined && value !== null) formData.append(key, String(value));\r\n    });\r\n    formData.append(\"_intent\", data.id ? \"update\" : \"create\");\r\n    fetcher.submit(formData, { method: \"post\" });\r\n  };\r\n\r\n  return (\r\n    <div className=\"container mx-auto py-8 px-4\">\r\n      <div className=\"flex flex-col md:flex-row justify-between items-center mb-8\">\r\n        <h1 className=\"text-3xl font-bold mb-4 md:mb-0\">Free Item Offers</h1>\r\n        <div className=\"w-full md:w-auto flex flex-col md:flex-row space-y-4 md:space-y-0 md:space-x-4\">\r\n          <div className=\"relative w-full md:w-64\">\r\n            <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\" />\r\n            <Input\r\n              placeholder=\"Search offers...\"\r\n              className=\"pl-10\"\r\n              value={searchQuery}\r\n              onChange={(e) => setSearchQuery(e.target.value)}\r\n            />\r\n          </div>\r\n          <Button\r\n            onClick={handleCreateClick}\r\n            className=\"whitespace-nowrap\"\r\n          >\r\n            Create Free Item Offer\r\n          </Button>\r\n        </div>\r\n      </div>\r\n\r\n      {filteredItems.length > 0 ? (\r\n        <div className=\"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6\">\r\n          {filteredItems.map((item) => (\r\n            <FreeItemCard\r\n              key={item.id}\r\n              item={item}\r\n              onEdit={handleEditItem}\r\n            />\r\n          ))}\r\n        </div>\r\n      ) : (\r\n        <div className=\"text-center py-20 bg-muted bg-opacity-50 rounded-lg\">\r\n          <h3 className=\"text-xl font-medium text-gray-600\">No offers found</h3>\r\n          <p className=\"text-gray-500 mt-2\">\r\n            {searchQuery ? \"Try adjusting your search\" : \"Create your first free item offer\"}\r\n          </p>\r\n          {!searchQuery && (\r\n            <Button\r\n              onClick={handleCreateClick}\r\n              className=\"mt-4\"\r\n              variant=\"default\"\r\n            >\r\n              Create Offer\r\n            </Button>\r\n          )}\r\n        </div>\r\n      )}\r\n\r\n      <FreeItemModal\r\n        isOpen={modalOpen}\r\n        onClose={() => setModalOpen(false)}\r\n        onSubmit={handleSubmitItem}\r\n        initialData={currentItem}\r\n      />\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Index;"], "names": ["jsxs", "jsx", "useEffect", "Fragment", "Index", "items", "initialItems", "useLoaderData", "fetcher", "useFetcher", "setItems", "useState", "searchQuery", "setSearch<PERSON>uery", "modalOpen", "setModalOpen", "currentItem", "setCurrentItem", "data", "success", "item", "prev", "exists", "some", "i", "id", "map", "filteredItems", "useMemo", "filter", "searchLower", "toLowerCase", "discountType", "includes", "discountPercentage", "toString", "discountFlat", "discountMinOrderQty", "handleCreateClick", "handleEditItem", "handleSubmitItem", "formData", "FormData", "Object", "entries", "for<PERSON>ach", "key", "value", "append", "String", "submit", "method", "className", "children", "Search", "Input", "placeholder", "onChange", "e", "target", "<PERSON><PERSON>", "onClick", "length", "FreeItemCard", "onEdit", "variant", "FreeItemModal", "isOpen", "onClose", "onSubmit", "initialData"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAGO,MAAM,iBAA2D;AAAA,EACtE,EAAE,OAAO,cAAc,OAAO,sBAAsB;AAAA,EACpD,EAAE,OAAO,QAAQ,OAAO,gBAAgB;AAAA,EACxC,EAAE,OAAO,YAAY,OAAO,YAAY;AAAA,EACxC,EAAE,OAAO,gBAAgB,OAAO,kBAAkB;AACpD;ACMA,MAAM,aAAa,CAAC,SAAwB;AAC1C,QAAM,IAAI,OAAO,SAAS,WAAW,IAAI,KAAK,IAAI,IAAI;AAC/C,SAAA,EAAE,mBAAmB,QAAW;AAAA,IACrC,KAAK;AAAA,IACL,OAAO;AAAA,IACP,MAAM;AAAA,EAAA,CACP;AACH;AAEA,MAAM,eAA4C,CAAC,EAAE,MAAM,aAAa;AAChE,QAAA,uBAAuB,CAAC,SAAiB;;AACtC,aAAA,oBAAe,KAAK,CAAC,MAAM,EAAE,UAAU,IAAI,MAA3C,mBAA8C,UAAS;AAAA,EAChE;AAEA,QAAM,mBAAmB,MAAM;AAC7B,YAAQ,KAAK,cAAc;AAAA,MACzB,KAAK;AACI,eAAA,GAAG,KAAK,kBAAkB;AAAA,MACnC,KAAK;AACI,eAAA,IAAI,KAAK,YAAY;AAAA,MAC9B,KAAK;AACI,eAAA,cAAc,KAAK,WAAW;AAAA,MACvC,KAAK;AACI,eAAA;AAAA,MACT;AACS,eAAA;AAAA,IAAA;AAAA,EAEb;AAEM,QAAA,0BAAU,KAAK;AACf,QAAA,WAAW,OAAO,KAAK,aAAa,OAAO,KAAK,WAAW,CAAC,KAAK;AAGrE,SAAAA,kCAAA,KAAC,MAAK,EAAA,WAAU,mFACd,UAAA;AAAA,IAAAC,kCAAAA,IAAC,cAAW,WAAU,OACpB,UAACD,kCAAA,KAAA,OAAA,EAAI,WAAU,oCACb,UAAA;AAAA,MAACA,kCAAAA,KAAA,OAAA,EAAI,WAAU,iBACb,UAAA;AAAA,QAAAC,kCAAA,IAAC,KAAE,EAAA,WAAU,yBACV,UAAA,iBAAA,GACH;AAAA,8CACC,KAAE,EAAA,WAAU,iCACV,UAAqB,qBAAA,KAAK,YAAY,EACzC,CAAA;AAAA,MAAA,GACF;AAAA,MACCA,kCAAA,IAAA,OAAA,EAAM,SAAS,WAAW,YAAY,WAAW,WAAW,WAAW,iBAAiB,IACtF,UAAW,WAAA,WAAW,WACzB,CAAA;AAAA,IAAA,EAAA,CACF,EACF,CAAA;AAAA,0CACC,aAAY,EAAA,WAAU,YACrB,UAACD,kCAAA,KAAA,OAAA,EAAI,WAAU,aACZ,UAAA;AAAA,MAAA,KAAK,uBACJA,uCAAC,OAAI,EAAA,WAAU,qCACb,UAAA;AAAA,QAACC,kCAAA,IAAA,QAAA,EAAK,WAAU,iCAAgC,UAAU,cAAA;AAAA,QAC1DD,kCAAAA,KAAC,QAAK,EAAA,WAAU,eAAc,UAAA;AAAA,UAAA;AAAA,UAAE,KAAK;AAAA,QAAA,EAAoB,CAAA;AAAA,MAAA,GAC3D;AAAA,MAED,KAAK,gBACHA,uCAAA,OAAA,EAAI,WAAU,qCACb,UAAA;AAAA,QAACC,kCAAA,IAAA,QAAA,EAAK,WAAU,iCAAgC,UAAY,gBAAA;AAAA,QAC5DD,kCAAAA,KAAC,QAAK,EAAA,WAAU,eAAc,UAAA;AAAA,UAAA;AAAA,UAAE,KAAK;AAAA,QAAA,EAAa,CAAA;AAAA,MAAA,GACpD;AAAA,MAEFA,kCAAAA,KAAC,OAAI,EAAA,WAAU,qCACb,UAAA;AAAA,QAACC,kCAAA,IAAA,QAAA,EAAK,WAAU,iCAAgC,UAAU,cAAA;AAAA,8CACzD,QAAK,EAAA,WAAU,eAAe,UAAW,WAAA,KAAK,SAAS,EAAE,CAAA;AAAA,MAAA,GAC5D;AAAA,MACAD,kCAAAA,KAAC,OAAI,EAAA,WAAU,qCACb,UAAA;AAAA,QAACC,kCAAA,IAAA,QAAA,EAAK,WAAU,iCAAgC,UAAW,eAAA;AAAA,8CAC1D,QAAK,EAAA,WAAU,eAAe,UAAW,WAAA,KAAK,OAAO,EAAE,CAAA;AAAA,MAAA,EAC1D,CAAA;AAAA,IAAA,EAAA,CACF,EACF,CAAA;AAAA,IACAA,kCAAAA,IAAC,YAAW,EAAA,WAAU,YACpB,UAAAA,kCAAA;AAAA,MAAC;AAAA,MAAA;AAAA,QACC,SAAQ;AAAA,QACR,WAAU;AAAA,QACV,SAAS,MAAM,OAAO,IAAI;AAAA,QAC3B,UAAA;AAAA,MAAA;AAAA,IAAA,EAGH,CAAA;AAAA,EAAA,GACF;AAEJ;AC1FA,MAAM,gBAAkC;AAAA,EACtC,cAAc;AAAA,EACd,oBAAoB;AAAA,EACpB,cAAc;AAAA,EACd,cAAc;AAAA,EACd,qBAAqB;AAAA,EACrB,+BAAe,KAAK;AAAA,EACpB,SAAS,IAAI,MAAK,oBAAI,KAAK,GAAE,UAAS,oBAAI,KAAK,GAAE,SAAS,IAAI,CAAC,CAAC;AAAA,EAChE,kBAAkB;AAAA,EAClB,YAAY;AAAA,EACZ,aAAa;AACf;AAEA,MAAM,eAA4C,CAAC,EAAE,aAAa,eAAe;AAC/E,QAAM,CAAC,MAAM,OAAO,IAAI,MAAM,SAA2B,eAAe,aAAa;AAErFC,eAAAA,UAAU,MAAM;AACV,QAAA,qBAAqB,WAAW;AAAA,EAAA,GACnC,CAAC,WAAW,CAAC;AAEV,QAAA,eAAe,CAAC,MAA+D;AACnF,UAAM,SAAS,EAAE;AACjB,UAAM,EAAE,MAAM,OAAO,KAAS,IAAA;AAC9B,YAAQ,CAAC,UAAU;AAAA,MACjB,GAAG;AAAA,MACH,CAAC,IAAI,GAAG,SAAS,aAAa,OAAO,UAAU;AAAA,IAAA,EAC/C;AAAA,EACJ;AAEM,QAAA,mBAAmB,CAAC,MAA8B,UAAkB;AACxE,YAAQ,CAAC,UAAU,EAAE,GAAG,MAAM,CAAC,IAAI,GAAG,IAAI,KAAK,KAAK,EAAA,EAAI;AAAA,EAC1D;AAEM,QAAA,eAAe,CAAC,MAAuB;AAC3C,MAAE,eAAe;AACjB,aAAS,IAAI;AAAA,EACf;AAEA,SACGF,kCAAAA,KAAA,QAAA,EAAK,UAAU,cAAc,WAAU,aACtC,UAAA;AAAA,IAAAA,uCAAC,OACC,EAAA,UAAA;AAAA,MAACC,kCAAA,IAAA,SAAA,EAAM,WAAU,0BAAyB,UAAa,iBAAA;AAAA,MACvDD,kCAAA;AAAA,QAAC;AAAA,QAAA;AAAA,UACC,MAAK;AAAA,UACL,OAAO,KAAK;AAAA,UACZ,UAAU;AAAA,UACV,WAAU;AAAA,UAEV,UAAA;AAAA,YAACC,kCAAA,IAAA,UAAA,EAAO,OAAM,cAAa,UAAU,cAAA;AAAA,YACpCA,kCAAA,IAAA,UAAA,EAAO,OAAM,QAAO,UAAI,QAAA;AAAA,YACxBA,kCAAA,IAAA,UAAA,EAAO,OAAM,YAAW,UAAS,YAAA,CAAA;AAAA,UAAA;AAAA,QAAA;AAAA,MAAA;AAAA,IACpC,GACF;AAAA,IAEC,KAAK,iBAAiB,gBAEnBD,kCAAA,KAAAG,kBAAA,UAAA,EAAA,UAAA;AAAA,MAAAH,uCAAC,OACC,EAAA,UAAA;AAAA,QAACC,kCAAA,IAAA,SAAA,EAAM,WAAU,0BAAyB,UAAuB,2BAAA;AAAA,QACjEA,kCAAA;AAAA,UAAC;AAAA,UAAA;AAAA,YACC,MAAK;AAAA,YACL,MAAK;AAAA,YACL,KAAK;AAAA,YACL,KAAK;AAAA,YACL,OAAO,KAAK,sBAAsB;AAAA,YAClC,UAAU;AAAA,YACV,WAAU;AAAA,YACV,aAAY;AAAA,UAAA;AAAA,QAAA;AAAA,MACd,GACF;AAAA,6CACC,OACC,EAAA,UAAA;AAAA,QAACA,kCAAA,IAAA,SAAA,EAAM,WAAU,0BAAyB,UAAkB,sBAAA;AAAA,QAC5DA,kCAAA;AAAA,UAAC;AAAA,UAAA;AAAA,YACC,MAAK;AAAA,YACL,MAAK;AAAA,YACL,KAAK;AAAA,YACL,OAAO,KAAK,gBAAgB;AAAA,YAC5B,UAAU;AAAA,YACV,WAAU;AAAA,YACV,aAAY;AAAA,UAAA;AAAA,QAAA;AAAA,MACd,EACF,CAAA;AAAA,IAAA,GACF;AAAA,IAGD,KAAK,iBAAiB,UACrBD,kCAAA,KAAC,OACC,EAAA,UAAA;AAAA,MAACC,kCAAA,IAAA,SAAA,EAAM,WAAU,0BAAyB,UAAwB,4BAAA;AAAA,MAClEA,kCAAA;AAAA,QAAC;AAAA,QAAA;AAAA,UACC,MAAK;AAAA,UACL,MAAK;AAAA,UACL,KAAK;AAAA,UACL,OAAO,KAAK,gBAAgB;AAAA,UAC5B,UAAU;AAAA,UACV,WAAU;AAAA,UACV,aAAY;AAAA,QAAA;AAAA,MAAA;AAAA,IACd,GACF;AAAA,IAGD,KAAK,iBAAiB,cAEnBD,kCAAA,KAAAG,kBAAA,UAAA,EAAA,UAAA;AAAA,MAAAH,uCAAC,OACC,EAAA,UAAA;AAAA,QAACC,kCAAA,IAAA,SAAA,EAAM,WAAU,0BAAyB,UAAS,aAAA;AAAA,QACnDA,kCAAA;AAAA,UAAC;AAAA,UAAA;AAAA,YACC,MAAK;AAAA,YACL,MAAK;AAAA,YACL,OAAO,KAAK,cAAc;AAAA,YAC1B,UAAU;AAAA,YACV,WAAU;AAAA,YACV,aAAY;AAAA,UAAA;AAAA,QAAA;AAAA,MACd,GACF;AAAA,6CACC,OACC,EAAA,UAAA;AAAA,QAACA,kCAAA,IAAA,SAAA,EAAM,WAAU,0BAAyB,UAAkB,sBAAA;AAAA,QAC5DA,kCAAA;AAAA,UAAC;AAAA,UAAA;AAAA,YACC,MAAK;AAAA,YACL,MAAK;AAAA,YACL,KAAK;AAAA,YACL,OAAO,KAAK,eAAe;AAAA,YAC3B,UAAU;AAAA,YACV,WAAU;AAAA,YACV,aAAY;AAAA,UAAA;AAAA,QAAA;AAAA,MACd,EACF,CAAA;AAAA,IAAA,GACF;AAAA,2CAGD,OACC,EAAA,UAAA;AAAA,MAACA,kCAAA,IAAA,SAAA,EAAM,WAAU,0BAAyB,UAAwB,4BAAA;AAAA,MAClEA,kCAAA;AAAA,QAAC;AAAA,QAAA;AAAA,UACC,MAAK;AAAA,UACL,MAAK;AAAA,UACL,KAAK;AAAA,UACL,OAAO,KAAK,uBAAuB;AAAA,UACnC,UAAU;AAAA,UACV,WAAU;AAAA,UACV,aAAY;AAAA,QAAA;AAAA,MAAA;AAAA,IACd,GACF;AAAA,IAEAD,kCAAAA,KAAC,OAAI,EAAA,WAAU,yCACb,UAAA;AAAA,MAAAA,uCAAC,OACC,EAAA,UAAA;AAAA,QAACC,kCAAA,IAAA,SAAA,EAAM,WAAU,0BAAyB,UAAU,cAAA;AAAA,QACpDA,kCAAA;AAAA,UAAC;AAAA,UAAA;AAAA,YACC,MAAK;AAAA,YACL,MAAK;AAAA,YACL,OAAO,KAAK,YAAY,IAAI,KAAK,KAAK,SAAS,EAAE,YAAc,EAAA,MAAM,GAAG,EAAE,CAAC,IAAI;AAAA,YAC/E,UAAU,CAAC,MAAM,iBAAiB,aAAa,EAAE,OAAO,KAAK;AAAA,YAC7D,WAAU;AAAA,UAAA;AAAA,QAAA;AAAA,MACZ,GACF;AAAA,6CACC,OACC,EAAA,UAAA;AAAA,QAACA,kCAAA,IAAA,SAAA,EAAM,WAAU,0BAAyB,UAAQ,YAAA;AAAA,QAClDA,kCAAA;AAAA,UAAC;AAAA,UAAA;AAAA,YACC,MAAK;AAAA,YACL,MAAK;AAAA,YACL,OAAO,KAAK,UAAU,IAAI,KAAK,KAAK,OAAO,EAAE,YAAc,EAAA,MAAM,GAAG,EAAE,CAAC,IAAI;AAAA,YAC3E,UAAU,CAAC,MAAM,iBAAiB,WAAW,EAAE,OAAO,KAAK;AAAA,YAC3D,WAAU;AAAA,UAAA;AAAA,QAAA;AAAA,MACZ,EACF,CAAA;AAAA,IAAA,GACF;AAAA,IAEAD,kCAAAA,KAAC,OAAI,EAAA,WAAU,2BACb,UAAA;AAAA,MAAAC,kCAAA;AAAA,QAAC;AAAA,QAAA;AAAA,UACC,MAAK;AAAA,UACL,MAAK;AAAA,UACL,SAAS,KAAK;AAAA,UACd,UAAU;AAAA,UACV,WAAU;AAAA,QAAA;AAAA,MACZ;AAAA,MACCA,kCAAA,IAAA,SAAA,EAAM,WAAU,eAAc,UAAgB,mBAAA,CAAA;AAAA,IAAA,GACjD;AAAA,IAEAA,kCAAAA,IAAC,SAAI,WAAU,mCACb,iDAAC,UAAO,EAAA,MAAK,UAAS,WAAU,4CAC7B,UAAA;AAAA,OAAA,2CAAa,MAAK,WAAW;AAAA,MAAS;AAAA,IAAA,EAAA,CACzC,EACF,CAAA;AAAA,EAAA,GACF;AAEJ;AC/KA,MAAM,gBAA8C,CAAC;AAAA,EACnD;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,MAAM;AACE,QAAA,eAAe,CAAC,SAA2B;AAC/C,aAAS,IAAI;AACP,UAAA;AAAA,MACJ,cAAa,2CAAa,MAAK,6BAA6B;AAAA,IAAA,CAC7D;AACO,YAAA;AAAA,EACV;AAGE,SAAAA,kCAAA,IAAC,UAAO,MAAM,QAAQ,cAAc,SAClC,UAAAD,kCAAA,KAAC,eAAc,EAAA,WAAU,oBACvB,UAAA;AAAA,IAACC,kCAAA,IAAA,cAAA,EACC,iDAAC,aACE,EAAA,UAAA;AAAA,OAAA,2CAAa,MAAK,SAAS;AAAA,MAAS;AAAA,IAAA,EAAA,CACvC,EACF,CAAA;AAAA,IACCA,kCAAAA,IAAA,cAAA,EAAa,aAA0B,UAAU,aAAc,CAAA;AAAA,EAAA,EAAA,CAClE,EACF,CAAA;AAEJ;ACcMG,MAAAA,QAAkBA,MAAM;AAC5B,QAAM;AAAA,IAAEC,OAAOC;AAAAA,EAAa,IAAIC,cAAqC;AACrE,QAAMC,UAAUC,WAAgB;AAChC,QAAM,CAACJ,OAAOK,QAAQ,IAAIC,aAAAA,SAAqBL,YAAY;AAC3D,QAAM,CAACM,aAAaC,cAAc,IAAIF,aAAAA,SAAiB,EAAE;AACzD,QAAM,CAACG,WAAWC,YAAY,IAAIJ,aAAAA,SAAkB,KAAK;AACzD,QAAM,CAACK,aAAaC,cAAc,IAAIN,aAAAA,SAAuC,MAAS;AAGtFT,eAAAA,UAAU,MAAM;;AACd,UAAIM,aAAQU,SAARV,mBAAcW,YAAWX,QAAQU,KAAKE,MAAM;AAC9CV,eAAUW,UAAS;AACX,cAAAC,SAASD,KAAKE,KAAMC,OAAMA,EAAEC,OAAOjB,QAAQU,KAAKE,KAAKK,EAAE;AAC7D,YAAIH,QAAQ;AACV,iBAAOD,KAAKK,IAAKF,OAAOA,EAAEC,OAAOjB,QAAQU,KAAKE,KAAKK,KAAKjB,QAAQU,KAAKE,OAAOI,CAAE;AAAA,QAChF,OAAO;AACL,iBAAO,CAAC,GAAGH,MAAMb,QAAQU,KAAKE,IAAI;AAAA,QACpC;AAAA,MACF,CAAC;AACDL,mBAAa,KAAK;AAAA,IACpB;AAAA,EACF,GAAG,CAACP,QAAQU,IAAI,CAAC;AAEX,QAAAS,gBAAgBC,aAAAA,QAAQ,MAAM;AAC3B,WAAAvB,MAAMwB,OAAQT,UAAS;AACtB,YAAAU,cAAclB,YAAYmB,YAAY;AACxC,UAAAX,KAAKY,gBAAgBZ,KAAKY,aAAaD,cAAcE,SAASH,WAAW,EAAU,QAAA;AACnF,UAAAV,KAAKc,sBAAsBd,KAAKc,mBAAmBC,WAAWF,SAASrB,WAAW,EAAU,QAAA;AAC5F,UAAAQ,KAAKgB,gBAAgBhB,KAAKgB,aAAaD,WAAWF,SAASrB,WAAW,EAAU,QAAA;AAChF,UAAAQ,KAAKiB,uBAAuBjB,KAAKiB,oBAAoBF,WAAWF,SAASrB,WAAW,EAAU,QAAA;AAC3F,aAAA;AAAA,IACT,CAAC;AAAA,EACH,GAAG,CAACP,OAAOO,WAAW,CAAC;AAEvB,QAAM0B,oBAAoBA,MAAM;AAC9BrB,mBAAe,MAAS;AACxBF,iBAAa,IAAI;AAAA,EACnB;AAEM,QAAAwB,iBAAkBnB,UAAmB;AACzCH,mBAAeG,IAAI;AACnBL,iBAAa,IAAI;AAAA,EACnB;AAEM,QAAAyB,mBAAoBtB,UAA2B;AAC7C,UAAAuB,WAAW,IAAIC,SAAS;AACvBC,WAAAC,QAAQ1B,IAAI,EAAE2B,QAAQ,CAAC,CAACC,KAAKC,KAAK,MAAM;AACzC,UAAAA,UAAU,UAAaA,UAAU,eAAeC,OAAOF,KAAKG,OAAOF,KAAK,CAAC;AAAA,IAC/E,CAAC;AACDN,aAASO,OAAO,WAAW9B,KAAKO,KAAK,WAAW,QAAQ;AACxDjB,YAAQ0C,OAAOT,UAAU;AAAA,MAAEU,QAAQ;AAAA,IAAO,CAAC;AAAA,EAC7C;AAGE,SAAAnD,kCAAAA,KAAC,OAAI;AAAA,IAAAoD,WAAU;AAAA,IACbC,UAAA,CAACrD,kCAAA,KAAA,OAAA;AAAA,MAAIoD,WAAU;AAAA,MACbC,UAAA,CAACpD,kCAAA,IAAA,MAAA;AAAA,QAAGmD,WAAU;AAAA,QAAkCC,UAAgB;AAAA,MAAA,CAAA,GAChErD,kCAAA,KAAC,OAAI;AAAA,QAAAoD,WAAU;AAAA,QACbC,UAAA,CAACrD,kCAAA,KAAA,OAAA;AAAA,UAAIoD,WAAU;AAAA,UACbC,UAAA,CAACpD,kCAAA,IAAAqD,QAAA;AAAA,YAAOF,WAAU;AAAA,UAA2E,CAAA,GAC7FnD,kCAAA,IAACsD,OAAA;AAAA,YACCC,aAAY;AAAA,YACZJ,WAAU;AAAA,YACVL,OAAOnC;AAAAA,YACP6C,UAAWC,OAAM7C,eAAe6C,EAAEC,OAAOZ,KAAK;AAAA,UAAA,CAChD,CAAA;AAAA,QACF,CAAA,GACA9C,kCAAA,IAAC2D,QAAA;AAAA,UACCC,SAASvB;AAAAA,UACTc,WAAU;AAAA,UACXC,UAAA;AAAA,QAAA,CAED,CAAA;AAAA,MACF,CAAA,CAAA;AAAA,IACF,CAAA,GAEC1B,cAAcmC,SAAS,IACrB7D,kCAAAA,IAAA,OAAA;AAAA,MAAImD,WAAU;AAAA,MACZC,UAAA1B,cAAcD,IAAKN,UAClBnB,kCAAAA,IAAC8D,cAAA;AAAA,QAEC3C;AAAAA,QACA4C,QAAQzB;AAAAA,MAAA,GAFHnB,KAAKK,EAGZ,CACD;AAAA,IACH,CAAA,IAECzB,kCAAA,KAAA,OAAA;AAAA,MAAIoD,WAAU;AAAA,MACbC,UAAA,CAACpD,kCAAA,IAAA,MAAA;AAAA,QAAGmD,WAAU;AAAA,QAAoCC,UAAe;AAAA,MAAA,CAAA,yCAChE,KAAE;AAAA,QAAAD,WAAU;AAAA,QACVC,UAAAzC,cAAc,8BAA8B;AAAA,MAC/C,CAAA,GACC,CAACA,eACAX,kCAAA,IAAC2D,QAAA;AAAA,QACCC,SAASvB;AAAAA,QACTc,WAAU;AAAA,QACVa,SAAQ;AAAA,QACTZ,UAAA;AAAA,MAAA,CAED,CAAA;AAAA,IAEJ,CAAA,GAGFpD,kCAAA,IAACiE,eAAA;AAAA,MACCC,QAAQrD;AAAAA,MACRsD,SAASA,MAAMrD,aAAa,KAAK;AAAA,MACjCsD,UAAU7B;AAAAA,MACV8B,aAAatD;AAAAA,IAAA,CACf,CAAA;AAAA,EACF,CAAA;AAEJ;"}