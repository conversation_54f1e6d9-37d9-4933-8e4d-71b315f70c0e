{"version": 3, "file": "seller.marketing.analytics-zUneR_oL.js", "sources": ["../../../app/routes/seller.marketing.analytics.tsx"], "sourcesContent": ["export default function Analytics() {\r\n  return (\r\n    <div className=\"flex items-center justify-center h-[calc(100vh-300px)]\">\r\n      <p className=\"text-muted-foreground\">Analytics feature coming soon...</p>\r\n    </div>\r\n  );\r\n} "], "names": ["Analytics", "jsx", "className", "children"], "mappings": ";AAAA,SAAwBA,YAAY;AAEhC,SAAAC,kCAAAA,IAAC;IAAIC,WAAU;AAAA,IACbC,gDAAC,KAAE;AAAA,MAAAD,WAAU;AAAA,MAAwBC,UAAA;AAAA,IAAgC,CAAA;AAAA,EACvE,CAAA;AAEJ;"}