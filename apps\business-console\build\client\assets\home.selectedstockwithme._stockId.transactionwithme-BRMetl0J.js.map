{"version": 3, "file": "home.selectedstockwithme._stockId.transactionwithme-BRMetl0J.js", "sources": ["../../../app/routes/home.selectedstockwithme.$stockId.transactionwithme.tsx"], "sourcesContent": ["import { json, LoaderFunctionArgs, ActionFunctionArgs, redirect } from \"@remix-run/node\";\r\n  import { useLoaderData, Form, useNavigation, useActionData, useFetcher } from \"@remix-run/react\";\r\n  import { useEffect, useRef, useState } from \"react\";\r\n  import {\r\n    Table,\r\n    TableBody,\r\n    TableCell,\r\n    TableHead,\r\n    TableHeader,\r\n    TableRow,\r\n  } from \"~/components/ui/table\";\r\n  import { Badge } from \"~/components/ui/badge\";\r\n  import { Button } from \"~/components/ui/button\";\r\n  import { Card, CardHeader, CardContent, CardTitle } from \"~/components/ui/card\";\r\n  import { Plus, Printer, User } from \"lucide-react\";\r\n  import { createStockTransactions, getStockTransactions } from \"~/services/stockservices/stockTransactions\";\r\n  import { CreateStockTransactionInput, InvStockTransaction, StockTransactionType } from \"~/types/api/businessConsoleService/ItemStock\";\r\n  import { withResponse } from \"~/utils/auth-utils\";\r\n  import AddEntryModal from \"~/components/StockComponent/AddEntryModal\";\r\nimport { toast } from \"~/hooks/use-toast\";\r\nimport SpinnerLoader from \"~/components/loader/SpinnerLoader\";\r\n\r\n\r\n\r\n\r\n  interface ApiResponse<T> {\r\n    data: T;\r\n    status: number;\r\n    message?: string;\r\n  }\r\n\r\n  interface LoaderData {\r\n    transactions: InvStockTransaction[];\r\n    unit: string;\r\n    selectedDate: string;\r\n    stockId: number;\r\n    sellerId?: number;\r\n\r\n\r\n  }\r\n\r\n  interface ActionData {\r\n    error?: string;\r\n    success?: boolean;\r\n    message?: string;\r\n  }\r\n\r\n  // Sample AddEntryModal component\r\n\r\n  // Loader function to fetch transaction data\r\n  export async function loader({ request}: LoaderFunctionArgs) {\r\n    const url = new URL(request.url);\r\n    const deliveryDate = url.searchParams.get(\"deliveryDate\") || new Date().toISOString().split(\"T\")[0];\r\n    const unit = url.searchParams.get(\"unit\") || \"units\";\r\n\r\n    const SellerId= parseInt(url.searchParams.get(\"sellerId\") as string||\"0\")\r\n    const isSeller = SellerId==null || isNaN(SellerId) || SellerId === 0;\r\n    const stockId = (typeof (arguments[0]?.params?.stockId) !== \"undefined\") ? parseInt(arguments[0].params.stockId) : 0; // Get stockId from params\r\n\r\n\r\n    try {\r\n      const response = await getStockTransactions(isSeller, stockId,SellerId, deliveryDate, request);\r\n      return withResponse<LoaderData>({\r\n        transactions: response.data || [],\r\n        unit,\r\n        selectedDate: deliveryDate,\r\n        stockId,\r\n        sellerId:SellerId\r\n      });\r\n    } catch (error: any) {\r\n      console.error(\"Error fetching transactions:\", error);\r\n      throw new Error(\"Failed to fetch transactions\");\r\n    }\r\n  }\r\n\r\n  // Action function to handle form submission\r\n  export async function action({ request }: ActionFunctionArgs) {\r\n    const formData = await request.formData();\r\n    const intent = formData.get(\"intent\");\r\n    const sellerId = parseInt(formData.get(\"sellerId\")?.toString() || \"0\");\r\n    const isSeller = sellerId === 0 || isNaN(sellerId); // Determine if the user is a seller\r\n\r\n    const selectedStockId = (typeof (arguments[0]?.params?.stockId) !== \"undefined\") ? parseInt(arguments[0].params.stockId) : 0; // Get stockId from params\r\n\r\n\r\n    if (intent === \"filter\") {\r\n      const deliveryDate = formData.get(\"deliveryDate\")?.toString();\r\n      if (!deliveryDate) {\r\n        return json<ActionData>({ error: \"Delivery date is required\" }, { status: 400 });\r\n      }\r\n      return json<ActionData>({ success: true });\r\n    }\r\n\r\n    if (intent === \"create\") {\r\n      const stockId = selectedStockId; // Replace with dynamic stockId\r\n      const isSellectedSeller = isSeller; \r\n      const stockCreationbody: CreateStockTransactionInput = {\r\n        stockTransactionType: formData.get(\"transactionType\") as StockTransactionType,\r\n        narration: formData.get(\"narration\")?.toString() || \"\",\r\n        quantity: Number(formData.get(\"quantity\")) || 0,\r\n        deliveryDate: formData.get(\"deliveryDate\")?.toString() || \"\",\r\n      };\r\n\r\n      if (!stockCreationbody.stockTransactionType || !stockCreationbody.narration || stockCreationbody.quantity <= 0 || !stockCreationbody.deliveryDate) {\r\n        return json<ActionData>({ error: \"All fields are required and must be valid\" }, { status: 400 });\r\n      }\r\n      \r\n      try {\r\n        const response = await createStockTransactions(isSellectedSeller, stockId, sellerId,stockCreationbody, request);\r\n        return  withResponse<ActionData>({\r\n          success: true,\r\n          message: \"Transaction created successfully\",\r\n\r\n        },response.headers);\r\n      }\r\n      catch (error: any) {\r\n        throw new Error(\"Failed to fetch transactions\");\r\n      }\r\n    }\r\n\r\n    return json<ActionData>({ error: \"Invalid intent\" }, { status: 400 });\r\n  }\r\n\r\n  export default function TransactionsWithMe() {\r\n    const { transactions, unit, selectedDate, stockId,sellerId } = useLoaderData<LoaderData>();\r\n    const navigation = useNavigation();\r\n    const [isModalOpen, setIsModalOpen] = useState(false);\r\n    const [date, setDate] = useState(selectedDate); \r\n    const fetcher = useFetcher();\r\n    console.log(sellerId, \"sellerId in transaction with me\");\r\n\r\n\r\n\r\n    // const reactToPrintFn = useReactToPrint({ contentRef });\r\n\r\n    useEffect(() => {\r\n      const data = fetcher.data as ActionData | undefined;\r\n      if (data?.success) {\r\n        setIsModalOpen(false);\r\n        toast({ description: data.message || \"Transaction created successfully\" });\r\n      }\r\n      if (data?.error) {\r\n        toast({ description: data.error });\r\n      }\r\n    }, [(fetcher.data as ActionData | undefined)?.success, (fetcher.data as ActionData | undefined)?.error]); \r\n\r\n    const getTypeColor = (type: string) => {\r\n      switch (type) {\r\n        case StockTransactionType.RECEIVED:\r\n          return \"bg-green-100 text-green-800\";\r\n        case StockTransactionType.DELIVERED:\r\n          return \"bg-blue-100 text-blue-800\";\r\n        case StockTransactionType.SPOILED:\r\n          return \"bg-red-100 text-red-800\";\r\n        case StockTransactionType.RETURNED:\r\n          return \"bg-amber-100 text-amber-800\";\r\n        case StockTransactionType.CORRECTION:\r\n          return \"bg-purple-100 text-purple-800\";\r\n        case StockTransactionType.CONVERTED:\r\n          return \"bg-indigo-100 text-indigo-800\";\r\n        default:\r\n          return \"bg-gray-100 text-gray-800\";\r\n      }\r\n    };\r\n    // const formatDate = (dateString: string) => {\r\n    //   return new Date(dateString).toLocaleDateString(\"en-US\", {\r\n    //     year: \"numeric\",\r\n    //     month: \"short\",\r\n    //     day: \"numeric\",\r\n    //   });\r\n    // };\r\n    const formatCurrency = (amount: number) => {\r\n      return new Intl.NumberFormat(\"en-US\", {\r\n        style: \"currency\",\r\n        currency: \"USD\",\r\n      }).format(amount);\r\n    };\r\n    const handlePrint = () => {\r\n      window.print();\r\n    };\r\n    const handleAddEntry = async (data: CreateStockTransactionInput) => {\r\n      const formData = new FormData();\r\n      formData.append(\"intent\", \"create\");\r\n      formData.append(\"transactionType\", data.stockTransactionType);\r\n      formData.append(\"narration\", data.narration);\r\n      formData.append(\"quantity\", data.quantity.toString());\r\n      formData.append(\"deliveryDate\", data.deliveryDate);\r\n      if (sellerId) {\r\n        formData.append(\"sellerId\", sellerId.toString());\r\n      }\r\n      fetcher.submit(formData, {method: \"POST\",\r\n      });\r\n    };\r\n\r\n          const loading= fetcher.state !==\"idle\"\r\n    return (\r\n      <div className=\"space-y-6\">\r\n        {loading && ( <SpinnerLoader loading={loading} />)}\r\n        {/* Header with Actions */}\r\n        <div className=\"flex items-center justify-between no-print\">\r\n          <h2 className=\"text-2xl font-bold text-gray-900\">Transaction History</h2>\r\n          <div className=\"flex gap-3\">\r\n            <Form method=\"get\" className=\"flex items-center gap-2\">\r\n            {sellerId ? (\r\n  <input type=\"hidden\" name=\"sellerId\" value={sellerId.toString()} />\r\n) : null}               \r\n              <input type=\"hidden\" name=\"intent\" value=\"filter\" />\r\n              <input\r\n                type=\"date\"\r\n                name=\"deliveryDate\"\r\n                value={date}\r\n                onChange={(e) => setDate(e.target.value)}\r\n                className=\"border rounded-md p-2\"\r\n              />\r\n              <Button\r\n                type=\"submit\"\r\n                variant=\"outline\"\r\n                size=\"sm\"\r\n                disabled={navigation.state === \"submitting\" && navigation.formData?.get(\"intent\") === \"filter\"}\r\n              >\r\n                {navigation.state === \"submitting\" && navigation.formData?.get(\"intent\") === \"filter\" ? \"Filtering...\" : \"Filter\"}\r\n              </Button>\r\n            </Form>\r\n            <Button\r\n              variant=\"outline\"\r\n              size=\"sm\"\r\n              onClick={() => setIsModalOpen(true)}\r\n              className=\"flex items-center gap-2 hover:bg-primary hover:text-white transition-colors\"\r\n            >\r\n              <Plus className=\"h-4 w-4\" />\r\n              Add Entry\r\n            </Button>\r\n          \r\n              <Button\r\n                variant=\"outline\"\r\n                size=\"sm\"\r\n                className=\"flex items-center gap-2 hover:bg-gray-100 transition-colors\"\r\n                onClick={() =>handlePrint()}\r\n              >\r\n                <Printer className=\"h-4 w-4\" />\r\n                Print\r\n              </Button>\r\n          \r\n        \r\n          </div>\r\n        </div>\r\n\r\n        {/* Main Transaction Table */}\r\n        <Card className=\"printable-area shadow-sm\" >\r\n          <CardContent className=\"p-0\">\r\n          <div className=\"overflow-x-auto max-h-[600px] print:overflow-visible print:max-h-none\"\r\n          >\r\n          <Table className=\"min-w-full \">\r\n          <TableHeader className=\"sticky top-0 bg-white z-10 print:static\">\r\n                  <TableRow className=\" hover:bg-gray-50  bg-white \">\r\n                    <TableHead className=\"font-semibold text-gray-700 whitespace-nowrap\">Type</TableHead>\r\n                    <TableHead className=\"font-semibold text-gray-700 whitespace-nowrap\">Narration</TableHead>\r\n                    {/* <TableHead className=\"text-right font-semibold text-gray-700 whitespace-nowrap\">Qty</TableHead> */}\r\n                    {/* <TableHead className=\"text-right font-semibold text-gray-700 whitespace-nowrap\">U.Price</TableHead> */}\r\n                    {/* <TableHead className=\"text-right font-semibold text-gray-700 whitespace-nowrap\">T.Value</TableHead> */}\r\n                    <TableHead className=\"text-right font-semibold text-gray-700 whitespace-nowrap\">Balance</TableHead>\r\n                    <TableHead className=\"text-right font-semibold text-gray-700 whitespace-nowrap\">T(Rcvd-Del)</TableHead>\r\n                    <TableHead className=\"text-right font-semibold text-gray-700 whitespace-nowrap\">T(Ret-Spoil)</TableHead>\r\n                    <TableHead className=\"text-right font-semibold text-gray-700 whitespace-nowrap\">T(Conv-Corr)</TableHead>\r\n                    <TableHead className=\"text-right font-semibold text-gray-700 whitespace-nowrap\">ItemTotal</TableHead>\r\n                    <TableHead className=\"text-right font-semibold text-gray-700 whitespace-nowrap\">T.Dist.Charges</TableHead>\r\n                    <TableHead className=\"text-right font-semibold text-gray-700 whitespace-nowrap\">SalesComm</TableHead>\r\n                    <TableHead className=\"text-right font-semibold text-gray-700 whitespace-nowrap\">SupNetAmt</TableHead>\r\n                    <TableHead className=\"font-semibold text-gray-700 whitespace-nowrap\">User</TableHead>\r\n                  </TableRow>\r\n                </TableHeader>\r\n                <TableBody className=\"overflow-y-auto\">\r\n                  {transactions.length > 0 ? (\r\n                    transactions.map((transaction, index) => (\r\n                      <TableRow\r\n                        key={transaction.invStockTransactionId}\r\n                        className={`hover:bg-gray-50 transition-colors ${\r\n                          index % 2 === 0 ? \"bg-white\" : \"bg-gray-25\"\r\n                        }`}\r\n                      >\r\n                        <TableCell>\r\n                          <Badge\r\n                            className={`${getTypeColor(transaction.transactionType)} border-0 font-medium transition-colors cursor-default`}\r\n                          >\r\n                            {transaction.transactionType}\r\n                          </Badge>\r\n                        </TableCell>\r\n                        <TableCell className=\"max-w-[300px] break-words text-gray-700\">\r\n                          {transaction.narration}\r\n                        </TableCell>\r\n                        {/* <TableCell className=\"text-right font-medium\">\r\n                          {transaction.quantity > 0 ? (\r\n                            <span className=\"text-green-600\">\r\n                              +{transaction.quantity} \r\n                            </span>\r\n                          ) : transaction.quantity < 0 ? (\r\n                            <span className=\"text-red-600\">\r\n                              {transaction.quantity} \r\n                            </span>\r\n                          ) : (\r\n                            <span className=\"text-gray-400\">-</span>\r\n                          )}\r\n                        </TableCell>\r\n                        <TableCell className=\"text-right\">\r\n                          {transaction.unitPrice > 0 ? (\r\n                            <span className=\"font-medium text-gray-900\">\r\n                              {formatCurrency(transaction.unitPrice)}\r\n                            </span>\r\n                          ) : (\r\n                            <span className=\"text-gray-400\">-</span>\r\n                          )}\r\n                        </TableCell>\r\n                        <TableCell className=\"text-right font-bold\">\r\n                          {transaction.totalValue > 0 ? (\r\n                            <span className=\"text-gray-900\">\r\n                              {formatCurrency(transaction.totalValue)}\r\n                            </span>\r\n                          ) : (\r\n                            <span className=\"text-gray-400\">-</span>\r\n                          )}\r\n                        </TableCell> */}\r\n                        <TableCell className=\"text-right\">\r\n                          <span className=\"font-medium text-blue-600\">\r\n                            {transaction.balanceAfter||\"-\"}\r\n                          </span>\r\n                        </TableCell>\r\n                        <TableCell className=\"text-sm text-gray-600 text-right\">\r\n                          {(transaction.totalReceived || transaction.totalDelivered) > 0\r\n                            ? `${transaction.totalReceived || 0} - ${transaction.totalDelivered || 0}`\r\n                            : \"-\"}\r\n                        </TableCell>\r\n                        <TableCell className=\"text-sm text-gray-600 text-right\">\r\n                          {(transaction.totalReturned || transaction.totalSpoiled) >0\r\n                          ? `${transaction.totalReturned || 0} - ${transaction.totalSpoiled || 0}`\r\n                            : \"-\"}\r\n                          \r\n                        </TableCell>\r\n                        <TableCell className=\"text-sm text-gray-600 text-right\">\r\n                           {(transaction.totalConverted || transaction.totalCorrection) > 0\r\n                            ? `${transaction.totalConverted || 0} - ${transaction.totalCorrection || 0}`\r\n                            : \"-\"}\r\n                        </TableCell>\r\n                        <TableCell className=\"text-right font-bold\">\r\n                          {transaction.itemTotalAmount > 0 ? (\r\n                            <span className=\"text-gray-900\">\r\n                              {formatCurrency(transaction.itemTotalAmount)}\r\n                            </span>\r\n                          ) : (\r\n                            <span className=\"text-gray-400\">-</span>\r\n                          )}\r\n                        </TableCell>\r\n                       \r\n                        <TableCell className=\"text-right font-bold\">\r\n                          {transaction.totalDistributionCharges > 0 ? (\r\n                            <span className=\"text-gray-900\">\r\n                              {formatCurrency(transaction.totalDistributionCharges)}\r\n                            </span>\r\n                          ) : (\r\n                            <span className=\"text-gray-400\">-</span>\r\n                          )}\r\n                        </TableCell>\r\n                        <TableCell className=\"text-right font-bold\">\r\n                          {transaction.totalSalesComm > 0 ? (\r\n                            <span className=\"text-gray-900\">\r\n                              {`% ${transaction.totalSalesComm}`}\r\n                            </span>\r\n                          ) : (\r\n                            <span className=\"text-gray-400\">-</span>\r\n                          )}\r\n                        </TableCell>\r\n                        <TableCell className=\"text-right font-bold\">\r\n                          {transaction.supplierNetAmount > 0 ? (\r\n                            <span className=\"text-gray-900\">\r\n                              {formatCurrency(transaction.supplierNetAmount)}\r\n                            </span>\r\n                          ) : (\r\n                            <span className=\"text-gray-400\">-</span>\r\n                          )}\r\n                        </TableCell>\r\n                        <TableCell className=\"text-sm text-gray-600\">\r\n                          {transaction.username}\r\n                        </TableCell>\r\n                      </TableRow>\r\n                    ))\r\n                  ) : (\r\n                    <TableRow>\r\n                      <TableCell colSpan={15} className=\"text-center py-12\">\r\n                        <div className=\"flex flex-col items-center gap-2\">\r\n                          <div className=\"text-gray-400 text-lg\">📋</div>\r\n                          <p className=\"text-gray-500 font-medium\">No transactions found</p>\r\n                          <p className=\"text-gray-400 text-sm\">Add your first transaction to get started</p>\r\n                        </div>\r\n                      </TableCell>\r\n                    </TableRow>\r\n                  )}\r\n                </TableBody>\r\n              </Table>\r\n            </div>\r\n          </CardContent>\r\n        </Card>\r\n\r\n        {/* Add Entry Modal */}\r\n        <AddEntryModal\r\n          isOpen={isModalOpen}\r\n          onClose={() => setIsModalOpen(false)}\r\n          itemId={stockId}\r\n          onAddEntry={handleAddEntry}\r\n        />\r\n      </div>\r\n    );\r\n  }"], "names": ["TransactionsWithMe", "transactions", "unit", "selectedDate", "stockId", "sellerId", "useLoaderData", "navigation", "useNavigation", "isModalOpen", "setIsModalOpen", "useState", "date", "setDate", "fetcher", "useFetcher", "console", "log", "useEffect", "data", "success", "toast", "description", "message", "error", "getTypeColor", "type", "StockTransactionType", "RECEIVED", "DELIVERED", "SPOILED", "RETURNED", "CORRECTION", "CONVERTED", "formatCurrency", "amount", "Intl", "NumberFormat", "style", "currency", "format", "handlePrint", "window", "print", "handleAddEntry", "formData", "FormData", "append", "stockTransactionType", "narration", "quantity", "toString", "deliveryDate", "submit", "method", "loading", "state", "jsxs", "className", "children", "jsx", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Form", "name", "value", "onChange", "e", "target", "<PERSON><PERSON>", "variant", "size", "disabled", "get", "onClick", "Plus", "Printer", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Table", "TableHeader", "TableRow", "TableHead", "TableBody", "length", "map", "transaction", "index", "TableCell", "Badge", "transactionType", "balanceAfter", "totalReceived", "totalDelivered", "totalReturned", "totalSpoiled", "totalConverted", "totalCorrection", "itemTotalAmount", "totalDistributionCharges", "totalSalesComm", "supplierNetAmount", "username", "invStockTransactionId", "colSpan", "AddEntryModal", "isOpen", "onClose", "itemId", "onAddEntry"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AA2HE,SAAwBA,qBAAqB;;AAC3C,QAAM;AAAA,IAAEC;AAAAA,IAAcC;AAAAA,IAAMC;AAAAA,IAAcC;AAAAA,IAAQC;AAAAA,MAAaC,cAA0B;AACzF,QAAMC,aAAaC,cAAc;AACjC,QAAM,CAACC,aAAaC,cAAc,IAAIC,aAAAA,SAAS,KAAK;AACpD,QAAM,CAACC,MAAMC,OAAO,IAAIF,aAAAA,SAASR,YAAY;AAC7C,QAAMW,UAAUC,WAAW;AACnBC,UAAAC,IAAIZ,UAAU,iCAAiC;AAMvDa,eAAAA,UAAU,MAAM;AACd,UAAMC,OAAOL,QAAQK;AACrB,QAAIA,6BAAMC,SAAS;AACjBV,qBAAe,KAAK;AACpBW,YAAM;AAAA,QAAEC,aAAaH,KAAKI,WAAW;AAAA,MAAmC,CAAC;AAAA,IAC3E;AACA,QAAIJ,6BAAMK,OAAO;AACfH,YAAM;AAAA,QAAEC,aAAaH,KAAKK;AAAAA,MAAM,CAAC;AAAA,IACnC;AAAA,EACF,GAAG,EAAEV,aAAQK,SAARL,mBAAyCM,UAAUN,aAAQK,SAARL,mBAAyCU,KAAK,CAAC;AAEjG,QAAAC,eAAgBC,UAAiB;AACrC,YAAQA,MAAM;AAAA,MACZ,KAAKC,qBAAqBC;AACjB,eAAA;AAAA,MACT,KAAKD,qBAAqBE;AACjB,eAAA;AAAA,MACT,KAAKF,qBAAqBG;AACjB,eAAA;AAAA,MACT,KAAKH,qBAAqBI;AACjB,eAAA;AAAA,MACT,KAAKJ,qBAAqBK;AACjB,eAAA;AAAA,MACT,KAAKL,qBAAqBM;AACjB,eAAA;AAAA,MACT;AACS,eAAA;AAAA,IACX;AAAA,EACF;AAQM,QAAAC,iBAAkBC,YAAmB;AAClC,WAAA,IAAIC,KAAKC,aAAa,SAAS;AAAA,MACpCC,OAAO;AAAA,MACPC,UAAU;AAAA,IACZ,CAAC,EAAEC,OAAOL,MAAM;AAAA,EAClB;AACA,QAAMM,cAAcA,MAAM;AACxBC,WAAOC,MAAM;AAAA,EACf;AACM,QAAAC,iBAAiB,OAAOzB,SAAsC;AAC5D,UAAA0B,WAAW,IAAIC,SAAS;AACrBD,aAAAE,OAAO,UAAU,QAAQ;AACzBF,aAAAE,OAAO,mBAAmB5B,KAAK6B,oBAAoB;AACnDH,aAAAE,OAAO,aAAa5B,KAAK8B,SAAS;AAC3CJ,aAASE,OAAO,YAAY5B,KAAK+B,SAASC,UAAU;AAC3CN,aAAAE,OAAO,gBAAgB5B,KAAKiC,YAAY;AACjD,QAAI/C,UAAU;AACZwC,eAASE,OAAO,YAAY1C,SAAS8C,SAAA,CAAU;AAAA,IACjD;AACArC,YAAQuC,OAAOR,UAAU;AAAA,MAACS,QAAQ;AAAA,IAClC,CAAC;AAAA,EACH;AAEY,QAAAC,UAASzC,QAAQ0C,UAAS;AAEpC,SAAAC,kCAAAA,KAAC,OAAI;AAAA,IAAAC,WAAU;AAAA,IACZC,UAAA,CAAaJ,WAAAK,kCAAA,IAACC;MAAcN;AAAAA,IAAkB,CAAA,GAE/CE,kCAAA,KAAC,OAAI;AAAA,MAAAC,WAAU;AAAA,MACbC,UAAA,CAACC,kCAAA,IAAA,MAAA;AAAA,QAAGF,WAAU;AAAA,QAAmCC,UAAmB;AAAA,MAAA,CAAA,GACpEF,kCAAA,KAAC,OAAI;AAAA,QAAAC,WAAU;AAAA,QACbC,UAAA,CAAAF,kCAAA,KAACK,MAAK;AAAA,UAAAR,QAAO;AAAA,UAAMI,WAAU;AAAA,UAC5BC,UAAA,CACXtD,WAAAuD,kCAAA,IAAC,SAAM;AAAA,YAAAlC,MAAK;AAAA,YAASqC,MAAK;AAAA,YAAWC,OAAO3D,SAAS8C,SAAS;AAAA,WAAG,IAC/D,4CACW,SAAM;AAAA,YAAAzB,MAAK;AAAA,YAASqC,MAAK;AAAA,YAASC,OAAM;AAAA,UAAS,CAAA,GAClDJ,kCAAA,IAAC,SAAA;AAAA,YACClC,MAAK;AAAA,YACLqC,MAAK;AAAA,YACLC,OAAOpD;AAAAA,YACPqD,UAAWC,OAAMrD,QAAQqD,EAAEC,OAAOH,KAAK;AAAA,YACvCN,WAAU;AAAA,UAAA,CACZ,GACAE,kCAAA,IAACQ,QAAA;AAAA,YACC1C,MAAK;AAAA,YACL2C,SAAQ;AAAA,YACRC,MAAK;AAAA,YACLC,UAAUhE,WAAWiD,UAAU,kBAAgBjD,gBAAWsC,aAAXtC,mBAAqBiE,IAAI,eAAc;AAAA,YAErFb,UAAApD,WAAWiD,UAAU,kBAAgBjD,gBAAWsC,aAAXtC,mBAAqBiE,IAAI,eAAc,WAAW,iBAAiB;AAAA,UAAA,CAC3G,CAAA;AAAA,QACF,CAAA,GACAf,kCAAA,KAACW,QAAA;AAAA,UACCC,SAAQ;AAAA,UACRC,MAAK;AAAA,UACLG,SAASA,MAAM/D,eAAe,IAAI;AAAA,UAClCgD,WAAU;AAAA,UAEVC,UAAA,CAACC,kCAAA,IAAAc,MAAA;AAAA,YAAKhB,WAAU;AAAA,UAAU,CAAA,GAAE,WAAA;AAAA,QAAA,CAE9B,GAEED,kCAAA,KAACW,QAAA;AAAA,UACCC,SAAQ;AAAA,UACRC,MAAK;AAAA,UACLZ,WAAU;AAAA,UACVe,SAASA,MAAKhC,YAAY;AAAA,UAE1BkB,UAAA,CAACC,kCAAA,IAAAe,SAAA;AAAA,YAAQjB,WAAU;AAAA,UAAU,CAAA,GAAE,OAAA;AAAA,QAAA,CAEjC,CAAA;AAAA,MAGJ,CAAA,CAAA;AAAA,IACF,CAAA,yCAGCkB,MAAK;AAAA,MAAAlB,WAAU;AAAA,MACdC,UAACC,kCAAA,IAAAiB,aAAA;AAAA,QAAYnB,WAAU;AAAA,QACvBC,UAAAC,kCAAA,IAAC,OAAA;AAAA,UAAIF,WAAU;AAAA,UAEfC,UAAAF,kCAAA,KAACqB,OAAM;AAAA,YAAApB,WAAU;AAAA,YACjBC,UAAA,CAAAC,kCAAA,IAACmB;cAAYrB,WAAU;AAAA,cACfC,UAACF,kCAAA,KAAAuB,UAAA;AAAA,gBAAStB,WAAU;AAAA,gBAClBC,UAAA,CAACC,kCAAA,IAAAqB,WAAA;AAAA,kBAAUvB,WAAU;AAAA,kBAAgDC,UAAI;AAAA,gBAAA,CAAA,GACxEC,kCAAA,IAAAqB,WAAA;AAAA,kBAAUvB,WAAU;AAAA,kBAAgDC,UAAS;AAAA,gBAAA,CAAA,GAI7EC,kCAAA,IAAAqB,WAAA;AAAA,kBAAUvB,WAAU;AAAA,kBAA2DC,UAAO;AAAA,gBAAA,CAAA,GACtFC,kCAAA,IAAAqB,WAAA;AAAA,kBAAUvB,WAAU;AAAA,kBAA2DC,UAAW;AAAA,gBAAA,CAAA,GAC1FC,kCAAA,IAAAqB,WAAA;AAAA,kBAAUvB,WAAU;AAAA,kBAA2DC,UAAY;AAAA,gBAAA,CAAA,GAC3FC,kCAAA,IAAAqB,WAAA;AAAA,kBAAUvB,WAAU;AAAA,kBAA2DC,UAAY;AAAA,gBAAA,CAAA,GAC3FC,kCAAA,IAAAqB,WAAA;AAAA,kBAAUvB,WAAU;AAAA,kBAA2DC,UAAS;AAAA,gBAAA,CAAA,GACxFC,kCAAA,IAAAqB,WAAA;AAAA,kBAAUvB,WAAU;AAAA,kBAA2DC,UAAc;AAAA,gBAAA,CAAA,GAC7FC,kCAAA,IAAAqB,WAAA;AAAA,kBAAUvB,WAAU;AAAA,kBAA2DC,UAAS;AAAA,gBAAA,CAAA,GACxFC,kCAAA,IAAAqB,WAAA;AAAA,kBAAUvB,WAAU;AAAA,kBAA2DC,UAAS;AAAA,gBAAA,CAAA,GACxFC,kCAAA,IAAAqB,WAAA;AAAA,kBAAUvB,WAAU;AAAA,kBAAgDC,UAAI;AAAA,gBAAA,CAAA,CAAA;AAAA,cAC3E,CAAA;AAAA,YACF,CAAA,GACAC,kCAAA,IAACsB,WAAU;AAAA,cAAAxB,WAAU;AAAA,cAClBC,UAAA1D,aAAakF,SAAS,IACrBlF,aAAamF,IAAI,CAACC,aAAaC,UAC7B7B,kCAAA,KAACuB,UAAA;AAAA,gBAECtB,WAAW,sCACT4B,QAAQ,MAAM,IAAI,aAAa,YACjC;AAAA,gBAEA3B,UAAA,CAAAC,kCAAA,IAAC2B,WACC;AAAA,kBAAA5B,UAAAC,kCAAA,IAAC4B,OAAA;AAAA,oBACC9B,WAAW,GAAGjC,aAAa4D,YAAYI,eAAe,CAAC;AAAA,oBAEtD9B,UAAY0B,YAAAI;AAAAA,kBACf,CAAA;AAAA,gBACF,CAAA,GACC7B,kCAAA,IAAA2B,WAAA;AAAA,kBAAU7B,WAAU;AAAA,kBAClBC,sBAAYV;AAAAA,gBACf,CAAA,GAgCAW,kCAAA,IAAC2B,WAAU;AAAA,kBAAA7B,WAAU;AAAA,kBACnBC,UAAAC,kCAAA,IAAC,QAAK;AAAA,oBAAAF,WAAU;AAAA,oBACbC,UAAA0B,YAAYK,gBAAc;AAAA,kBAC7B,CAAA;AAAA,gBACF,CAAA,yCACCH,WAAU;AAAA,kBAAA7B,WAAU;AAAA,kBACjBC,WAAY0B,YAAAM,iBAAiBN,YAAYO,kBAAkB,IACzD,GAAGP,YAAYM,iBAAiB,CAAC,MAAMN,YAAYO,kBAAkB,CAAC,KACtE;AAAA,gBACN,CAAA,yCACCL,WAAU;AAAA,kBAAA7B,WAAU;AAAA,kBACjBC,WAAY0B,YAAAQ,iBAAiBR,YAAYS,gBAAe,IACxD,GAAGT,YAAYQ,iBAAiB,CAAC,MAAMR,YAAYS,gBAAgB,CAAC,KAClE;AAAA,gBAEN,CAAA,yCACCP,WAAU;AAAA,kBAAA7B,WAAU;AAAA,kBAChBC,WAAY0B,YAAAU,kBAAkBV,YAAYW,mBAAmB,IAC5D,GAAGX,YAAYU,kBAAkB,CAAC,MAAMV,YAAYW,mBAAmB,CAAC,KACxE;AAAA,gBACN,CAAA,GACApC,kCAAA,IAAC2B;kBAAU7B,WAAU;AAAA,kBAClBC,sBAAYsC,kBAAkB,0CAC5B,QAAK;AAAA,oBAAAvC,WAAU;AAAA,oBACbC,UAAezB,eAAAmD,YAAYY,eAAe;AAAA,kBAC7C,CAAA,0CAEC,QAAK;AAAA,oBAAAvC,WAAU;AAAA,oBAAgBC,UAAA;AAAA,kBAAC,CAAA;AAAA,gBAErC,CAAA,GAEAC,kCAAA,IAAC2B;kBAAU7B,WAAU;AAAA,kBAClBC,sBAAYuC,2BAA2B,0CACrC,QAAK;AAAA,oBAAAxC,WAAU;AAAA,oBACbC,UAAezB,eAAAmD,YAAYa,wBAAwB;AAAA,kBACtD,CAAA,0CAEC,QAAK;AAAA,oBAAAxC,WAAU;AAAA,oBAAgBC,UAAA;AAAA,kBAAC,CAAA;AAAA,gBAErC,CAAA,GACAC,kCAAA,IAAC2B;kBAAU7B,WAAU;AAAA,kBAClBC,sBAAYwC,iBAAiB,0CAC3B,QAAK;AAAA,oBAAAzC,WAAU;AAAA,oBACbC,UAAK,KAAA0B,YAAYc,cAAc;AAAA,kBAClC,CAAA,0CAEC,QAAK;AAAA,oBAAAzC,WAAU;AAAA,oBAAgBC,UAAA;AAAA,kBAAC,CAAA;AAAA,gBAErC,CAAA,GACAC,kCAAA,IAAC2B;kBAAU7B,WAAU;AAAA,kBAClBC,sBAAYyC,oBAAoB,0CAC9B,QAAK;AAAA,oBAAA1C,WAAU;AAAA,oBACbC,UAAezB,eAAAmD,YAAYe,iBAAiB;AAAA,kBAC/C,CAAA,0CAEC,QAAK;AAAA,oBAAA1C,WAAU;AAAA,oBAAgBC,UAAA;AAAA,kBAAC,CAAA;AAAA,gBAErC,CAAA,GACCC,kCAAA,IAAA2B,WAAA;AAAA,kBAAU7B,WAAU;AAAA,kBAClBC,sBAAY0C;AAAAA,gBACf,CAAA,CAAA;AAAA,cAAA,GA1GKhB,YAAYiB,qBA2GnB,CACD,IAEA1C,kCAAAA,IAAAoB,UAAA;AAAA,gBACCrB,UAACC,kCAAA,IAAA2B,WAAA;AAAA,kBAAUgB,SAAS;AAAA,kBAAI7C,WAAU;AAAA,kBAChCC,UAACF,kCAAA,KAAA,OAAA;AAAA,oBAAIC,WAAU;AAAA,oBACbC,UAAA,CAACC,kCAAA,IAAA,OAAA;AAAA,sBAAIF,WAAU;AAAA,sBAAwBC,UAAE;AAAA,oBAAA,CAAA,GACxCC,kCAAA,IAAA,KAAA;AAAA,sBAAEF,WAAU;AAAA,sBAA4BC,UAAqB;AAAA,oBAAA,CAAA,GAC7DC,kCAAA,IAAA,KAAA;AAAA,sBAAEF,WAAU;AAAA,sBAAwBC,UAAyC;AAAA,oBAAA,CAAA,CAAA;AAAA,kBAChF,CAAA;AAAA,gBACF,CAAA;AAAA,cACF,CAAA;AAAA,YAEJ,CAAA,CAAA;AAAA,UACF,CAAA;AAAA,QACF,CAAA;AAAA,MACF,CAAA;AAAA,IACF,CAAA,GAGAC,kCAAA,IAAC4C,eAAA;AAAA,MACCC,QAAQhG;AAAAA,MACRiG,SAASA,MAAMhG,eAAe,KAAK;AAAA,MACnCiG,QAAQvG;AAAAA,MACRwG,YAAYhE;AAAAA,IAAA,CACd,CAAA;AAAA,EACF,CAAA;AAEJ;"}