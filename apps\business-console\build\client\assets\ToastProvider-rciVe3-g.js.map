{"version": 3, "file": "ToastProvider-rciVe3-g.js", "sources": ["../../../app/components/ui/ToastProvider.tsx"], "sourcesContent": ["// app/components/ToastProvider.tsx\r\nimport React, { createContext, useContext, useState } from \"react\";\r\n\r\ntype Toast = { message: string; type: \"success\" | \"error\" };\r\ntype ToastContextType = {\r\n      toast: Toast | null;\r\n      showToast: (message: string, type: \"success\" | \"error\") => void;\r\n      clearToast: () => void;\r\n};\r\n\r\nconst ToastContext = createContext<ToastContextType | undefined>(undefined);\r\n\r\nexport const ToastProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {\r\n      const [toast, setToast] = useState<Toast | null>(null);\r\n\r\n      const showToast = (message: string, type: \"success\" | \"error\") => {\r\n            setToast({ message, type });\r\n            setTimeout(() => setToast(null), 3000); // Auto-hide after 3 seconds\r\n      };\r\n\r\n      const clearToast = () => setToast(null);\r\n\r\n      return (\r\n            <ToastContext.Provider value={{ toast, showToast, clearToast }}>\r\n                  {children}\r\n                  {toast && (\r\n                        <div\r\n                              style={{\r\n                                    position: \"fixed\",\r\n                                    top: 20,\r\n                                    right: 20,\r\n                                    padding: \"10px 20px\",\r\n                                    backgroundColor: toast.type === \"success\" ? \"green\" : \"red\",\r\n                                    color: \"white\",\r\n                                    borderRadius: 5,\r\n                              }}\r\n                        >\r\n                              {toast.message}\r\n                        </div>\r\n                  )}\r\n            </ToastContext.Provider>\r\n      );\r\n};\r\n\r\nexport const useToast = () => {\r\n      const context = useContext(ToastContext);\r\n      if (!context) {\r\n            throw new Error(\"useToast must be used within a ToastProvider\");\r\n      }\r\n      return context;\r\n};\r\n"], "names": ["createContext", "useState", "jsxs", "jsx", "useContext"], "mappings": ";AAUA,MAAM,eAAeA,2BAA4C,MAAS;AAEnE,MAAM,gBAAyD,CAAC,EAAE,eAAe;AAClF,QAAM,CAAC,OAAO,QAAQ,IAAIC,aAAAA,SAAuB,IAAI;AAE/C,QAAA,YAAY,CAAC,SAAiB,SAA8B;AACnD,aAAA,EAAE,SAAS,MAAM;AAC1B,eAAW,MAAM,SAAS,IAAI,GAAG,GAAI;AAAA,EAC3C;AAEM,QAAA,aAAa,MAAM,SAAS,IAAI;AAGhC,SAAAC,kCAAA,KAAC,aAAa,UAAb,EAAsB,OAAO,EAAE,OAAO,WAAW,WAC3C,GAAA,UAAA;AAAA,IAAA;AAAA,IACA,SACKC,kCAAA;AAAA,MAAC;AAAA,MAAA;AAAA,QACK,OAAO;AAAA,UACD,UAAU;AAAA,UACV,KAAK;AAAA,UACL,OAAO;AAAA,UACP,SAAS;AAAA,UACT,iBAAiB,MAAM,SAAS,YAAY,UAAU;AAAA,UACtD,OAAO;AAAA,UACP,cAAc;AAAA,QACpB;AAAA,QAEC,UAAM,MAAA;AAAA,MAAA;AAAA,IAAA;AAAA,EACb,GAEZ;AAEZ;AAEO,MAAM,WAAW,MAAM;AAClB,QAAA,UAAUC,wBAAW,YAAY;AACvC,MAAI,CAAC,SAAS;AACF,UAAA,IAAI,MAAM,8CAA8C;AAAA,EAAA;AAE7D,SAAA;AACb;"}