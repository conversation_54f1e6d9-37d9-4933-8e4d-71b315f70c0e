{"version": 3, "file": "home.delivery-XchKanLj.js", "sources": ["../../../node_modules/date-fns/constructNow.mjs", "../../../node_modules/date-fns/isToday.mjs", "../../../node_modules/date-fns/isTomorrow.mjs", "../../../app/routes/home.delivery.tsx"], "sourcesContent": ["import { constructFrom } from \"./constructFrom.mjs\";\n\n/**\n * @name constructNow\n * @category Generic Helpers\n * @summary Constructs a new current date using the passed value constructor.\n * @pure false\n *\n * @description\n * The function constructs a new current date using the constructor from\n * the reference date. It helps to build generic functions that accept date\n * extensions and use the current date.\n *\n * It defaults to `Date` if the passed reference date is a number or a string.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param date - The reference date to take constructor from\n *\n * @returns Current date initialized using the given date constructor\n *\n * @example\n * import { constructNow, isSameDay } from 'date-fns'\n *\n * function isToday<DateType extends Date>(\n *   date: DateType | number | string,\n * ): boolean {\n *   // If we were to use `new Date()` directly, the function would  behave\n *   // differently in different timezones and return false for the same date.\n *   return isSameDay(date, constructNow(date));\n * }\n */\nexport function constructNow(date) {\n  return constructFrom(date, Date.now());\n}\n\n// Fallback for modularized imports:\nexport default constructNow;\n", "import { constructNow } from \"./constructNow.mjs\";\nimport { isSameDay } from \"./isSameDay.mjs\";\n\n/**\n * @name isToday\n * @category Day Helpers\n * @summary Is the given date today?\n * @pure false\n *\n * @description\n * Is the given date today?\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param date - The date to check\n *\n * @returns The date is today\n *\n * @example\n * // If today is 6 October 2014, is 6 October 14:00:00 today?\n * const result = isToday(new Date(2014, 9, 6, 14, 0))\n * //=> true\n */\nexport function isToday(date) {\n  return isSameDay(date, constructNow(date));\n}\n\n// Fallback for modularized imports:\nexport default isToday;\n", "import { addDays } from \"./addDays.mjs\";\nimport { constructNow } from \"./constructNow.mjs\";\nimport { isSameDay } from \"./isSameDay.mjs\";\n\n/**\n * @name isTomorrow\n * @category Day Helpers\n * @summary Is the given date tomorrow?\n * @pure false\n *\n * @description\n * Is the given date tomorrow?\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param date - The date to check\n *\n * @returns The date is tomorrow\n *\n * @example\n * // If today is 6 October 2014, is 7 October 14:00:00 tomorrow?\n * const result = isTomorrow(new Date(2014, 9, 7, 14, 0))\n * //=> true\n */\nexport function isTomorrow(date) {\n  return isSameDay(date, addDays(constructNow(date), 1));\n}\n\n// Fallback for modularized imports:\nexport default isTomorrow;\n", "import { useState } from \"react\";\r\nimport { format, isToday, isTomorrow, parseISO } from \"date-fns\";\r\nimport { AlertTriangle, Truck } from \"lucide-react\";\r\n\r\nimport { Button } from \"@components/ui/button\";\r\nimport { Card, CardContent } from \"@components/ui/card\";\r\nimport { Switch } from \"@components/ui/switch\";\r\nimport { Label } from \"@components/ui/label\";\r\n\r\ntype Slot = {\r\n    date: string;\r\n    timeSlot: string;\r\n    orders: number;\r\n    tonnage: number;\r\n    trucks: number;\r\n    deliveryPercentage: number;\r\n    credit: number;\r\n    cashCollected: number;\r\n    routePlanningPending?: boolean;\r\n    isOrdering: boolean;\r\n};\r\n\r\nconst mockData: Slot[] = [\r\n    { date: \"2024-10-23\", timeSlot: \"07:00 AM - 10:00 AM\", orders: 50, tonnage: 5000, trucks: 5, deliveryPercentage: 55, credit: 7600, cashCollected: 76000, isOrdering: false },\r\n    { date: \"2024-10-24\", timeSlot: \"10:00 AM - 01:00 PM\", orders: 30, tonnage: 3000, trucks: 3, deliveryPercentage: 60, credit: 5000, cashCollected: 50000, isOrdering: false },\r\n    { date: \"2024-10-25\", timeSlot: \"01:00 PM - 04:00 PM\", orders: 40, tonnage: 4000, trucks: 4, deliveryPercentage: 70, credit: 6000, cashCollected: 60000, routePlanningPending: true, isOrdering: false },\r\n];\r\n\r\nexport default function HomeDelivery() {\r\n    const [slots, setSlots] = useState<Slot[]>(mockData);\r\n\r\n    const toggleOrdering = (index: number) => {\r\n        setSlots(prevSlots =>\r\n            prevSlots.map((slot, i) =>\r\n                i === index ? { ...slot, isOrdering: !slot.isOrdering } : slot\r\n            )\r\n        );\r\n    };\r\n\r\n    if (slots.length === 0) {\r\n        return <div>No delivery slots available.</div>;\r\n    }\r\n\r\n    return (\r\n        <div className=\"container mx-auto p-4\">\r\n            <h1 className=\"text-2xl font-bold mb-6\">HomeDelivery Slots</h1>\r\n            <div className=\"space-y-6\">\r\n                {slots.map((slot, index) => (\r\n                    <DeliverySlot key={index} slot={slot} onToggleOrdering={() => toggleOrdering(index)} />\r\n                ))}\r\n            </div>\r\n        </div>\r\n    );\r\n}\r\n\r\ntype DeliverySlotProps = {\r\n    slot: Slot;\r\n    onToggleOrdering: () => void;\r\n};\r\n\r\nfunction DeliverySlot({ slot, onToggleOrdering }: DeliverySlotProps) {\r\n    const slotDate = parseISO(slot.date);\r\n    let dateDisplay;\r\n    if (isToday(slotDate)) {\r\n        dateDisplay = \"Today\";\r\n    } else if (isTomorrow(slotDate)) {\r\n        dateDisplay = \"Tomorrow\";\r\n    } else {\r\n        dateDisplay = format(slotDate, \"MMM d, yyyy\");\r\n    }\r\n\r\n    return (\r\n        <Card>\r\n            <CardContent className=\"p-4\">\r\n                <div className=\"flex flex-wrap justify-between items-center mb-4\">\r\n                    <h2 className=\"text-lg font-semibold\">\r\n                        Slot: {dateDisplay} {slot.timeSlot}\r\n                    </h2>\r\n                    <div className=\"flex items-center space-x-2 mt-2 sm:mt-0\">\r\n                        <Switch\r\n                            id={`ordering-mode-${slot.date}`}\r\n                            checked={slot.isOrdering}\r\n                            onCheckedChange={onToggleOrdering}\r\n                        />\r\n                        <Label htmlFor={`ordering-mode-${slot.date}`}>Ordering</Label>\r\n                    </div>\r\n                </div>\r\n                <div className=\"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-4\">\r\n                    <StatSquare label=\"Orders\" value={slot.orders} />\r\n                    <StatSquare label=\"Tonnage\" value={`${slot.tonnage} KG`} />\r\n                    <StatSquare label=\"Trucks\" value={slot.trucks} icon={<Truck className=\"w-4 h-4\" />} />\r\n                    <StatSquare label=\"HomeDelivery%\" value={`${slot.deliveryPercentage}%`} />\r\n                    <StatSquare label=\"Credit\" value={`₹${slot.credit.toLocaleString()}`} />\r\n                    <StatSquare label=\"Cash collected\" value={`₹${slot.cashCollected.toLocaleString()}`} />\r\n                </div>\r\n                {slot.routePlanningPending && (\r\n                    <WarningSquare label=\"Route planning Pending\" className=\"mt-4\" />\r\n                )}\r\n                <div className=\"flex justify-end mt-4\">\r\n                    <Button variant=\"outline\">View/Edit Items</Button>\r\n                </div>\r\n            </CardContent>\r\n        </Card>\r\n    );\r\n}\r\n\r\ntype StatSquareProps = {\r\n    label: string;\r\n    value: string | number;\r\n    icon?: React.ReactNode;\r\n};\r\n\r\nfunction StatSquare({ label, value, icon = null }: StatSquareProps) {\r\n    return (\r\n        <div className=\"bg-muted p-3 rounded\">\r\n            <div className=\"text-sm text-muted-foreground mb-1 flex items-center gap-1\">\r\n                {icon}\r\n                {label}\r\n            </div>\r\n            <div className=\"font-semibold\">{value}</div>\r\n        </div>\r\n    );\r\n}\r\n\r\ntype WarningSquareProps = {\r\n    label: string;\r\n    className?: string;\r\n};\r\n\r\nfunction WarningSquare({ label, className = \"\" }: WarningSquareProps) {\r\n    return (\r\n        <div className={`bg-red-100 p-3 rounded flex items-center space-x-2 ${className}`}>\r\n            <AlertTriangle className=\"text-red-500 w-5 h-5\" />\r\n            <span className=\"text-sm font-medium text-red-700\">{label}</span>\r\n        </div>\r\n    );\r\n}\r\n"], "names": ["mockData", "date", "timeSlot", "orders", "tonnage", "trucks", "deliveryPercentage", "credit", "cashCollected", "isOrdering", "routePlanningPending", "HomeDelivery", "slots", "setSlots", "useState", "toggleOrdering", "index", "prevSlots", "map", "slot", "i", "length", "jsx", "children", "jsxs", "className", "DeliverySlot", "onToggleOrdering", "slotDate", "parseISO", "dateDisplay", "isToday", "isTomorrow", "format", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Switch", "id", "checked", "onCheckedChange", "Label", "htmlFor", "StatSquare", "label", "value", "icon", "Truck", "toLocaleString", "WarningSquare", "<PERSON><PERSON>", "variant", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAgCO,SAAS,aAAa,MAAM;AACjC,SAAO,cAAc,MAAM,KAAK,IAAG,CAAE;AACvC;ACXO,SAAS,QAAQ,MAAM;AAC5B,SAAO,UAAU,MAAM,aAAa,IAAI,CAAC;AAC3C;ACDO,SAAS,WAAW,MAAM;AAC/B,SAAO,UAAU,MAAM,QAAQ,aAAa,IAAI,GAAG,CAAC,CAAC;AACvD;ACJA,MAAMA,WAAmB,CACrB;AAAA,EAAEC,MAAM;AAAA,EAAcC,UAAU;AAAA,EAAuBC,QAAQ;AAAA,EAAIC,SAAS;AAAA,EAAMC,QAAQ;AAAA,EAAGC,oBAAoB;AAAA,EAAIC,QAAQ;AAAA,EAAMC,eAAe;AAAA,EAAOC,YAAY;AAAM,GAC3K;AAAA,EAAER,MAAM;AAAA,EAAcC,UAAU;AAAA,EAAuBC,QAAQ;AAAA,EAAIC,SAAS;AAAA,EAAMC,QAAQ;AAAA,EAAGC,oBAAoB;AAAA,EAAIC,QAAQ;AAAA,EAAMC,eAAe;AAAA,EAAOC,YAAY;AAAM,GAC3K;AAAA,EAAER,MAAM;AAAA,EAAcC,UAAU;AAAA,EAAuBC,QAAQ;AAAA,EAAIC,SAAS;AAAA,EAAMC,QAAQ;AAAA,EAAGC,oBAAoB;AAAA,EAAIC,QAAQ;AAAA,EAAMC,eAAe;AAAA,EAAOE,sBAAsB;AAAA,EAAMD,YAAY;AAAM,CAAA;AAG3M,SAAwBE,eAAe;AACnC,QAAM,CAACC,OAAOC,QAAQ,IAAIC,aAAAA,SAAiBd,QAAQ;AAE7C,QAAAe,iBAAkBC,WAAkB;AACtCH,4BACII,UAAUC,IAAI,CAACC,MAAMC,MACjBA,MAAMJ,QAAQ;AAAA,MAAE,GAAGG;AAAAA,MAAMV,YAAY,CAACU,KAAKV;AAAAA,IAAe,IAAAU,IAC9D,CACJ;AAAA,EACJ;AAEI,MAAAP,MAAMS,WAAW,GAAG;AACb,WAAAC,kCAAAA,IAAC;MAAIC,UAA4B;AAAA,IAAA,CAAA;AAAA,EAC5C;AAGI,SAAAC,kCAAAA,KAAC,OAAI;AAAA,IAAAC,WAAU;AAAA,IACXF,UAAA,CAACD,kCAAA,IAAA,MAAA;AAAA,MAAGG,WAAU;AAAA,MAA0BF,UAAkB;AAAA,IAAA,CAAA,yCACzD,OAAI;AAAA,MAAAE,WAAU;AAAA,MACVF,UAAMX,MAAAM,IAAI,CAACC,MAAMH,gDACbU,cAAyB;AAAA,QAAAP;AAAAA,QAAYQ,kBAAkBA,MAAMZ,eAAeC,KAAK;AAAA,MAA/D,GAAAA,KAAkE,CACxF;AAAA,IACL,CAAA,CAAA;AAAA,EACJ,CAAA;AAER;AAOA,SAASU,aAAa;AAAA,EAAEP;AAAAA,EAAMQ;AAAiB,GAAsB;AAC3D,QAAAC,WAAWC,SAASV,KAAKlB,IAAI;AAC/B,MAAA6B;AACA,MAAAC,QAAQH,QAAQ,GAAG;AACLE,kBAAA;AAAA,EAClB,WAAWE,WAAWJ,QAAQ,GAAG;AACfE,kBAAA;AAAA,EAClB,OAAO;AACWA,kBAAAG,OAAOL,UAAU,aAAa;AAAA,EAChD;AAEA,SACKN,kCAAAA,IAAAY,MAAA;AAAA,IACGX,UAACC,kCAAA,KAAAW,aAAA;AAAA,MAAYV,WAAU;AAAA,MACnBF,UAAA,CAACC,kCAAA,KAAA,OAAA;AAAA,QAAIC,WAAU;AAAA,QACXF,UAAA,CAACC,kCAAA,KAAA,MAAA;AAAA,UAAGC,WAAU;AAAA,UAAwBF,UAAA,CAAA,UAC3BO,aAAY,KAAEX,KAAKjB,QAAA;AAAA,QAC9B,CAAA,GACAsB,kCAAA,KAAC,OAAI;AAAA,UAAAC,WAAU;AAAA,UACXF,UAAA,CAAAD,kCAAA,IAACc,QAAA;AAAA,YACGC,IAAI,iBAAiBlB,KAAKlB,IAAI;AAAA,YAC9BqC,SAASnB,KAAKV;AAAAA,YACd8B,iBAAiBZ;AAAAA,UAAA,CACrB,yCACCa,OAAM;AAAA,YAAAC,SAAS,iBAAiBtB,KAAKlB,IAAI;AAAA,YAAIsB,UAAQ;AAAA,UAAA,CAAA,CAAA;AAAA,QAC1D,CAAA,CAAA;AAAA,MACJ,CAAA,GACAC,kCAAA,KAAC,OAAI;AAAA,QAAAC,WAAU;AAAA,QACXF,UAAA,CAAAD,kCAAA,IAACoB,YAAW;AAAA,UAAAC,OAAM;AAAA,UAASC,OAAOzB,KAAKhB;AAAAA,QAAQ,CAAA,GAC/CmB,kCAAA,IAACoB;UAAWC,OAAM;AAAA,UAAUC,OAAO,GAAGzB,KAAKf,OAAO;AAAA,QAAO,CAAA,GACxDkB,kCAAA,IAAAoB,YAAA;AAAA,UAAWC,OAAM;AAAA,UAASC,OAAOzB,KAAKd;AAAAA,UAAQwC,MAAOvB,kCAAA,IAAAwB,OAAA;AAAA,YAAMrB,WAAU;AAAA,UAAU,CAAA;AAAA,QAAI,CAAA,GACpFH,kCAAA,IAACoB;UAAWC,OAAM;AAAA,UAAgBC,OAAO,GAAGzB,KAAKb,kBAAkB;AAAA,QAAK,CAAA,GACxEgB,kCAAA,IAACoB,YAAW;AAAA,UAAAC,OAAM;AAAA,UAASC,OAAO,IAAIzB,KAAKZ,OAAOwC,eAAe,CAAC;AAAA,QAAI,CAAA,GACtEzB,kCAAA,IAACoB,YAAW;AAAA,UAAAC,OAAM;AAAA,UAAiBC,OAAO,IAAIzB,KAAKX,cAAcuC,eAAe,CAAC;AAAA,QAAI,CAAA,CAAA;AAAA,MACzF,CAAA,GACC5B,KAAKT,wBACFY,kCAAAA,IAAC0B;QAAcL,OAAM;AAAA,QAAyBlB,WAAU;AAAA,MAAO,CAAA,GAEnEH,kCAAA,IAAC;QAAIG,WAAU;AAAA,QACXF,gDAAC0B,QAAO;AAAA,UAAAC,SAAQ;AAAA,UAAU3B,UAAA;AAAA,QAAe,CAAA;AAAA,MAC7C,CAAA,CAAA;AAAA,IACJ,CAAA;AAAA,EACJ,CAAA;AAER;AAQA,SAASmB,WAAW;AAAA,EAAEC;AAAAA,EAAOC;AAAAA,EAAOC,OAAO;AAAK,GAAoB;AAE5D,SAAArB,kCAAAA,KAAC,OAAI;AAAA,IAAAC,WAAU;AAAA,IACXF,UAAA,CAACC,kCAAA,KAAA,OAAA;AAAA,MAAIC,WAAU;AAAA,MACVF,UAAA,CAAAsB,MACAF,KAAA;AAAA,IACL,CAAA,GACCrB,kCAAA,IAAA,OAAA;AAAA,MAAIG,WAAU;AAAA,MAAiBF,UAAMqB;AAAAA,IAAA,CAAA,CAAA;AAAA,EAC1C,CAAA;AAER;AAOA,SAASI,cAAc;AAAA,EAAEL;AAAAA,EAAOlB,YAAY;AAAG,GAAuB;AAClE,SACKD,kCAAAA,KAAA,OAAA;AAAA,IAAIC,WAAW,sDAAsDA,SAAS;AAAA,IAC3EF,UAAA,CAACD,kCAAA,IAAA6B,eAAA;AAAA,MAAc1B,WAAU;AAAA,IAAuB,CAAA,GAC/CH,kCAAA,IAAA,QAAA;AAAA,MAAKG,WAAU;AAAA,MAAoCF,UAAMoB;AAAAA,IAAA,CAAA,CAAA;AAAA,EAC9D,CAAA;AAER;", "x_google_ignoreList": [0, 1, 2]}