import { r as reactExports, j as jsxRuntimeExports } from "./jsx-runtime-bB2y7OuD.js";
import { B as Button } from "./button-ByAXMyvk.js";
import { C as Card, b as <PERSON><PERSON>eader, c as <PERSON><PERSON><PERSON><PERSON>, a as CardContent } from "./card-BJQMSLe_.js";
import { I as Input } from "./input-3v87qohQ.js";
import { L as Label } from "./label-cSASrwzW.js";
import { S as Switch } from "./switch-DQ_qW6ch.js";
import { u as useToast } from "./ToastProvider-rciVe3-g.js";
import { C as ConfigType } from "./DeliveryConfig-Dk5cNfWo.js";
import { u as useLoaderData, a as useFetcher } from "./components-D7UvGag_.js";
import { b as useRevalidator } from "./index-DhHTcibu.js";
import { T as Truck } from "./truck-ypDO_-A_.js";
import { P as Plus } from "./plus-D1sB_2yX.js";
import { T as Trash2 } from "./trash-2-DjkfFIB-.js";
import "./index-z_byfFrQ.js";
import "./index-ImHKLo0a.js";
import "./utils-GkgzjW3c.js";
import "./index-CzZ6EYkN.js";
import "./index-Vp2vNLNM.js";
import "./index-D7VH9Fc8.js";
import "./index-dIGjFc0I.js";
import "./index-CpKiYcZd.js";
import "./index-CkL5tk39.js";
import "./createLucideIcon-uwkRm45G.js";
function DeliveryConfigCard({
  config,
  onDelete,
  onStatusToggle
}) {
  return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
    className: "p-4 rounded-xl shadow-[0px_2px_8px_0px_rgba(0,0,0,0.1)] hover:shadow-[0px_4px_12px_0px_rgba(0,0,0,0.15)] transition-shadow duration-200 bg-white border border-neutral-100",
    children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
      className: "mb-3",
      children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
        className: "flex flex-row justify-between items-center",
        children: [/* @__PURE__ */ jsxRuntimeExports.jsx("div", {
          className: "p-1 border border-neutral-600 rounded-md bg-[#FCFCFD]",
          children: /* @__PURE__ */ jsxRuntimeExports.jsxs("h3", {
            className: "text-base font-semibold text-typography-400",
            children: ["Config #", config.id]
          })
        }), /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
          className: `px-2 py-1 rounded-md text-xs font-medium ${config.active ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"}`,
          children: config.active ? "Active" : "Inactive"
        })]
      }), /* @__PURE__ */ jsxRuntimeExports.jsx("p", {
        className: "mt-2 text-sm text-typography-500",
        children: config.configType === ConfigType.ORDER_VALUE_BASED ? "Order value based delivery configuration" : "Percentage based delivery configuration"
      })]
    }), /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
      className: "border-b border-neutral-200"
    }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
      className: "my-3",
      children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
        className: "grid grid-cols-2 gap-4",
        children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
          children: [/* @__PURE__ */ jsxRuntimeExports.jsx("p", {
            className: "text-xs text-typography-400 mb-1",
            children: "Order Range"
          }), /* @__PURE__ */ jsxRuntimeExports.jsxs("p", {
            className: "text-sm font-medium text-typography-700",
            children: ["₹", config.minOrderValue, " - ₹", config.maxOrderValue]
          })]
        }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
          className: "text-right",
          children: [/* @__PURE__ */ jsxRuntimeExports.jsx("p", {
            className: "text-xs text-typography-400 mb-1",
            children: "Max Buyer Charge"
          }), /* @__PURE__ */ jsxRuntimeExports.jsxs("p", {
            className: "text-sm font-medium text-typography-700",
            children: ["₹", config.maxBuyerDeliveryCharge]
          })]
        })]
      }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
        className: "mt-4 grid grid-cols-2 gap-4",
        children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
          children: [/* @__PURE__ */ jsxRuntimeExports.jsx("p", {
            className: "text-xs text-typography-400 mb-1",
            children: "Buyer Share"
          }), /* @__PURE__ */ jsxRuntimeExports.jsxs("p", {
            className: "text-sm font-medium text-typography-700",
            children: [config.buyerPercentage, "%"]
          })]
        }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
          className: "text-right",
          children: [/* @__PURE__ */ jsxRuntimeExports.jsx("p", {
            className: "text-xs text-typography-400 mb-1",
            children: "Seller Share"
          }), /* @__PURE__ */ jsxRuntimeExports.jsxs("p", {
            className: "text-sm font-medium text-typography-700",
            children: [config.sellerPercentage, "%"]
          })]
        })]
      })]
    }), /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
      className: "border-b border-neutral-200"
    }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
      className: "relative mt-3 flex flex-row gap-3 justify-between items-center",
      children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
        className: "flex items-center gap-2",
        children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Switch, {
          checked: config.active,
          onCheckedChange: () => onStatusToggle(config.id, config.active)
        }), /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
          className: "text-sm text-typography-600",
          children: config.active ? "Active" : "Inactive"
        })]
      }), /* @__PURE__ */ jsxRuntimeExports.jsx("button", {
        className: "border border-neutral-200 rounded-full p-2 cursor-pointer hover:bg-red-50 hover:border-red-200 transition-colors duration-200",
        onClick: () => onDelete(config.id),
        "aria-label": "Delete delivery configuration",
        children: /* @__PURE__ */ jsxRuntimeExports.jsx(Trash2, {
          className: "w-4 h-4 text-red-500"
        })
      })]
    })]
  });
}
function DeliveryConfig() {
  const {
    data: deliveryConfigs
  } = useLoaderData();
  const {
    revalidate
  } = useRevalidator();
  const [showForm, setShowForm] = reactExports.useState(false);
  const [slabChangeAt, setSlabChangeAt] = reactExports.useState(400);
  const [lowerSlabCapAt, setLowerSlabCapAt] = reactExports.useState(60);
  const [higherSlabCapAt, setHigherSlabCapAt] = reactExports.useState(30);
  const fetcher = useFetcher();
  const {
    showToast
  } = useToast();
  reactExports.useEffect(() => {
    if (fetcher.state === "idle" && fetcher.data) {
      if (fetcher.data.success === true && fetcher.data.actionType === "deleteDeliveryConfig") {
        showToast("Delivery Configuration deleted successfully", "success");
        revalidate();
      } else if (fetcher.data.success === false && fetcher.data.actionType === "deleteDeliveryConfig") {
        showToast("Failed to delete Delivery Configuration", "error");
      } else if (fetcher.data.success === true && fetcher.data.actionType === "addDeliveryConfig") {
        showToast("Delivery Configuration created successfully", "success");
        setShowForm(false);
        revalidate();
      } else if (fetcher.data.success === false && fetcher.data.actionType === "addDeliveryConfig") {
        showToast("Failed to create Delivery Configuration", "error");
      } else if (fetcher.data.success === true && fetcher.data.actionType === "updateStatus") {
        showToast("Delivery Configuration status updated successfully", "success");
        revalidate();
      } else if (fetcher.data.success === false && fetcher.data.actionType === "updateStatus") {
        showToast("Failed to update Delivery Configuration status", "error");
      }
    }
  }, [fetcher.state, fetcher.data]);
  const handleDelete = (configId) => {
    if (confirm("Are you sure you want to delete this delivery configuration?")) {
      const formData = new FormData();
      formData.append("actionType", "deleteDeliveryConfig");
      formData.append("configId", configId.toString());
      fetcher.submit(formData, {
        method: "POST"
      });
    }
  };
  const handleStatusToggle = (configId, currentStatus) => {
    const formData = new FormData();
    formData.append("actionType", "updateStatus");
    formData.append("configId", configId.toString());
    formData.append("active", (!currentStatus).toString());
    fetcher.submit(formData, {
      method: "POST"
    });
  };
  const handleAddConfig = () => {
    const formData = new FormData();
    formData.append("actionType", "addDeliveryConfig");
    formData.append("slabChangeAt", slabChangeAt.toString());
    formData.append("lowerSlabCapAt", lowerSlabCapAt.toString());
    formData.append("higherSlabCapAt", higherSlabCapAt.toString());
    fetcher.submit(formData, {
      method: "POST"
    });
  };
  return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
    className: "p-6",
    children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
      className: "mb-6",
      children: [/* @__PURE__ */ jsxRuntimeExports.jsx("h1", {
        className: "text-3xl font-bold text-gray-900",
        children: "Delivery Charge Configurations"
      }), /* @__PURE__ */ jsxRuntimeExports.jsx("p", {
        className: "text-gray-600 mt-2",
        children: "Manage your delivery charge configurations"
      })]
    }), /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
      "aria-labelledby": "delivery-configs-list",
      className: "pb-20 md:pb-5",
      children: deliveryConfigs && deliveryConfigs.length > 0 ? /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
        className: "grid grid-cols-1 lg:grid-cols-2 gap-4",
        children: deliveryConfigs.map((config) => /* @__PURE__ */ jsxRuntimeExports.jsx(DeliveryConfigCard, {
          config,
          onDelete: handleDelete,
          onStatusToggle: handleStatusToggle
        }, config.id))
      }) : /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
        className: "flex flex-col items-center justify-center py-10 text-center",
        children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
          className: "w-full max-w-4xl",
          children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
            className: "text-center mb-8",
            children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Truck, {
              className: "h-16 w-16 text-gray-400 mx-auto mb-4"
            }), /* @__PURE__ */ jsxRuntimeExports.jsx("h3", {
              className: "text-2xl font-semibold text-foreground mb-2",
              children: "Create Your First Delivery Configuration"
            }), /* @__PURE__ */ jsxRuntimeExports.jsx("p", {
              className: "text-muted-foreground",
              children: "Set up your delivery charge configuration to manage how delivery costs are split between you and your customers."
            })]
          }), !showForm ? /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
            className: "text-center",
            children: /* @__PURE__ */ jsxRuntimeExports.jsxs(Button, {
              onClick: () => setShowForm(true),
              className: "bg-blue-600 hover:bg-blue-700 text-white px-6 py-3",
              children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Plus, {
                className: "h-4 w-4 mr-2"
              }), "Add Delivery Configuration"]
            })
          }) : /* @__PURE__ */ jsxRuntimeExports.jsxs(Card, {
            className: "text-left",
            children: [/* @__PURE__ */ jsxRuntimeExports.jsx(CardHeader, {
              children: /* @__PURE__ */ jsxRuntimeExports.jsx(CardTitle, {
                className: "text-lg",
                children: "Configure Delivery Charges"
              })
            }), /* @__PURE__ */ jsxRuntimeExports.jsxs(CardContent, {
              className: "space-y-4",
              children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Label, {
                  htmlFor: "slabChangeAt",
                  className: "text-sm font-medium",
                  children: "Order Value Threshold (₹)"
                }), /* @__PURE__ */ jsxRuntimeExports.jsx(Input, {
                  id: "slabChangeAt",
                  type: "number",
                  value: slabChangeAt,
                  onChange: (e) => setSlabChangeAt(Number(e.target.value)),
                  className: "mt-1",
                  placeholder: "400"
                }), /* @__PURE__ */ jsxRuntimeExports.jsx("p", {
                  className: "text-xs text-gray-500 mt-1",
                  children: "Orders above this amount will have lower delivery charges"
                })]
              }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Label, {
                  htmlFor: "lowerSlabCapAt",
                  className: "text-sm font-medium",
                  children: "Max Delivery Charge for Orders Below Threshold (₹)"
                }), /* @__PURE__ */ jsxRuntimeExports.jsx(Input, {
                  id: "lowerSlabCapAt",
                  type: "number",
                  value: lowerSlabCapAt,
                  onChange: (e) => setLowerSlabCapAt(Number(e.target.value)),
                  className: "mt-1",
                  placeholder: "60"
                }), /* @__PURE__ */ jsxRuntimeExports.jsxs("p", {
                  className: "text-xs text-gray-500 mt-1",
                  children: ["Maximum amount customers pay for orders under ₹", slabChangeAt]
                })]
              }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Label, {
                  htmlFor: "higherSlabCapAt",
                  className: "text-sm font-medium",
                  children: "Max Delivery Charge for Orders Above Threshold (₹)"
                }), /* @__PURE__ */ jsxRuntimeExports.jsx(Input, {
                  id: "higherSlabCapAt",
                  type: "number",
                  value: higherSlabCapAt,
                  onChange: (e) => setHigherSlabCapAt(Number(e.target.value)),
                  className: "mt-1",
                  placeholder: "30"
                }), /* @__PURE__ */ jsxRuntimeExports.jsxs("p", {
                  className: "text-xs text-gray-500 mt-1",
                  children: ["Maximum amount customers pay for orders above ₹", slabChangeAt]
                })]
              }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                className: "bg-blue-50 p-3 rounded-lg",
                children: [/* @__PURE__ */ jsxRuntimeExports.jsx("h4", {
                  className: "text-sm font-medium text-blue-900 mb-2",
                  children: "How it works:"
                }), /* @__PURE__ */ jsxRuntimeExports.jsxs("ul", {
                  className: "text-xs text-blue-800 space-y-1",
                  children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("li", {
                    children: ["• Orders under ₹", slabChangeAt, ": Customer pays max ₹", lowerSlabCapAt, ", you cover the rest"]
                  }), /* @__PURE__ */ jsxRuntimeExports.jsxs("li", {
                    children: ["• Orders above ₹", slabChangeAt, ": Customer pays max ₹", higherSlabCapAt, ", you cover the rest"]
                  }), /* @__PURE__ */ jsxRuntimeExports.jsx("li", {
                    children: "• This encourages larger orders while keeping delivery affordable"
                  })]
                })]
              }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                className: "flex gap-3 pt-4",
                children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
                  onClick: handleAddConfig,
                  disabled: fetcher.state === "submitting" || fetcher.state === "loading",
                  className: "bg-blue-600 hover:bg-blue-700 text-white flex-1",
                  children: fetcher.state === "submitting" ? "Saving..." : "Save Configuration"
                }), /* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
                  variant: "outline",
                  onClick: () => setShowForm(false),
                  className: "flex-1",
                  children: "Cancel"
                })]
              })]
            })]
          })]
        })
      })
    }), /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
      className: "fixed bottom-0 left-0 right-0 z-30 p-4 flex justify-center md:static md:justify-end md:border-0",
      children: /* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
        onClick: () => setShowForm(true),
        className: "w-full md:w-auto",
        children: "Add New Delivery Configuration"
      })
    })]
  });
}
export {
  DeliveryConfig as default
};
//# sourceMappingURL=sellerSetting.deliveryConfig-DxsIVDgK.js.map
