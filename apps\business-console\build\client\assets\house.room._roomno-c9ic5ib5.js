import { j as jsxRuntimeExports } from "./jsx-runtime-bB2y7OuD.js";
import { u as useLoaderData } from "./components-D7UvGag_.js";
import "./index-Vp2vNLNM.js";
import "./index-DhHTcibu.js";
function Room() {
  const {
    roomNo
  } = useLoaderData();
  return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
    children: ["Room: ", roomNo]
  });
}
export {
  Room as default
};
//# sourceMappingURL=house.room._roomno-c9ic5ib5.js.map
