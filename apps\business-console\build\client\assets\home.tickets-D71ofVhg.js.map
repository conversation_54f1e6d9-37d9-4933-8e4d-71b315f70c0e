{"version": 3, "file": "home.tickets-D71ofVhg.js", "sources": ["../../../app/components/ui/ticket-status-badge.tsx", "../../../app/components/ui/ticket-notes-dialog.tsx", "../../../app/components/ui/ticket-status-dialog.tsx", "../../../app/utils/ticketUtils.ts", "../../../app/utils/cn.ts", "../../../app/components/ui/date-picker.tsx", "../../../app/routes/home.tickets.tsx"], "sourcesContent": ["import { cn } from \"~/lib/utils\";\r\nimport { Badge } from \"./badge\";\r\nimport { SupportTicketStatus } from \"~/types/api/businessConsoleService/Tickets\";\r\n\r\ninterface TicketStatusBadgeProps {\r\n  status: SupportTicketStatus;\r\n  className?: string;\r\n}\r\n\r\nexport function TicketStatusBadge({ status, className }: TicketStatusBadgeProps) {\r\n  const statusStyles: Record<SupportTicketStatus, string> = {\r\n    OPEN: \"bg-yellow-100 text-yellow-800 hover:bg-yellow-100\",\r\n    WIP: \"bg-blue-100 text-blue-800 hover:bg-blue-100\",\r\n    CLOSED: \"bg-green-100 text-green-800 hover:bg-green-100\"\r\n  };\r\n\r\n  // Get the style for the status, defaulting to OPEN style if not found\r\n  const style = statusStyles[status] || statusStyles.OPEN;\r\n\r\n  // Format the display text\r\n  const displayText = status === \"WIP\" ? \"Work In Progress\" : status.charAt(0) + status.slice(1).toLowerCase();\r\n\r\n  return (\r\n    <Badge \r\n      className={cn(style, className)}\r\n      variant=\"outline\"\r\n    >\r\n      {displayText}\r\n    </Badge>\r\n  );\r\n} ", "import { useState, useEffect } from \"react\";\r\nimport { useFetcher } from \"@remix-run/react\";\r\nimport { <PERSON><PERSON>, <PERSON>alogContent, DialogHeader, DialogTitle } from \"./dialog\";\r\nimport { But<PERSON> } from \"./button\";\r\nimport { Input } from \"./input\";\r\nimport { TicketNote } from \"~/types/api/businessConsoleService/Tickets\";\r\n\r\ninterface TicketNotesDialogProps {\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n  ticketId: number;\r\n  ticketNotes: TicketNote[];\r\n}\r\n\r\n// Response type for note submission\r\ninterface NoteSubmitResponse {\r\n  success: boolean;\r\n  message?: string;\r\n  note?: TicketNote;\r\n  error?: string;\r\n}\r\n\r\nexport function TicketNotesDialog({ \r\n  isOpen, \r\n  onClose, \r\n  ticketId, \r\n  ticketNotes\r\n}: TicketNotesDialogProps) {\r\n  const [note, setNote] = useState(\"\");\r\n  const addNoteFetcher = useFetcher<NoteSubmitResponse>();\r\n  const [hasSubmitted, setHasSubmitted] = useState(false);\r\n  \r\n  const isSubmitting = addNoteFetcher.state === \"submitting\";\r\n  \r\n  // Reset note input when submission is successful\r\n  useEffect(() => {\r\n    if (\r\n      hasSubmitted && \r\n      addNoteFetcher.state === \"idle\" && \r\n      addNoteFetcher.data?.success === true\r\n    ) {\r\n      setNote(\"\");\r\n      setHasSubmitted(false);\r\n      \r\n      // Simply close the dialog - Remix will automatically revalidate the data\r\n      onClose();\r\n    }\r\n  }, [addNoteFetcher.state, addNoteFetcher.data, hasSubmitted, onClose]);\r\n  \r\n  const handleSubmit = () => {\r\n    if (!note.trim()) return;\r\n    \r\n    setHasSubmitted(true);\r\n    addNoteFetcher.submit(\r\n      { \r\n        note,\r\n        ticketId: ticketId.toString(),\r\n        _action: \"addNote\"\r\n      },\r\n      { \r\n        method: \"post\", \r\n        action: `/home/<USER>/notes` \r\n      }\r\n    );\r\n  };\r\n\r\n  return (\r\n    <Dialog open={isOpen} onOpenChange={onClose}>\r\n      <DialogContent className=\"sm:max-w-[500px]\">\r\n        <DialogHeader>\r\n          <DialogTitle>Ticket Notes</DialogTitle>\r\n        </DialogHeader>\r\n        \r\n        <div className=\"max-h-[400px] overflow-y-auto py-4\">\r\n          {ticketNotes && ticketNotes.length > 0 ? (\r\n            <div className=\"space-y-4\">\r\n              {ticketNotes.map((noteItem) => (\r\n                <div key={noteItem.ticketNoteId} className=\"border rounded-md p-3 bg-gray-50\">\r\n                  <p className=\"text-sm\">{noteItem.note}</p>\r\n                </div>\r\n              ))}\r\n            </div>\r\n          ) : (\r\n            <p className=\"text-center text-gray-500 py-4\">No notes available</p>\r\n          )}\r\n        </div>\r\n        \r\n        <div className=\"pt-4 border-t\">\r\n          <div className=\"flex flex-col space-y-4\">\r\n            <Input\r\n              value={note}\r\n              onChange={(e) => setNote(e.target.value)}\r\n              placeholder=\"Add a note...\"\r\n              className=\"w-full\"\r\n            />\r\n            <Button \r\n              onClick={handleSubmit} \r\n              disabled={!note.trim() || isSubmitting}\r\n              loading={isSubmitting}\r\n              className=\"self-end\"\r\n            >\r\n              Add Note\r\n            </Button>\r\n          </div>\r\n        </div>\r\n      </DialogContent>\r\n    </Dialog>\r\n  );\r\n} ", "import { useState, useEffect } from \"react\";\r\nimport { useFetcher } from \"@remix-run/react\";\r\nimport { <PERSON><PERSON>, <PERSON>alogContent, <PERSON><PERSON>Header, <PERSON><PERSON>Title, DialogFooter } from \"./dialog\";\r\nimport { <PERSON><PERSON> } from \"./button\";\r\nimport { Label } from \"./label\";\r\nimport { RadioGroup, RadioGroupItem } from \"./radio-group\";\r\nimport { SupportTicketStatus } from \"~/types/api/businessConsoleService/Tickets\";\r\n\r\ninterface TicketStatusDialogProps {\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n  ticket: {\r\n    ticketId: number;\r\n    status: SupportTicketStatus;\r\n  };\r\n}\r\n\r\nexport function TicketStatusDialog({ \r\n  isOpen, \r\n  onClose, \r\n  ticket\r\n}: TicketStatusDialogProps) {\r\n  const [status, setStatus] = useState<SupportTicketStatus>(ticket.status);\r\n  const updateStatusFetcher = useFetcher();\r\n  const [hasSubmitted, setHasSubmitted] = useState(false);\r\n  \r\n  const isSubmitting = updateStatusFetcher.state === \"submitting\";\r\n  \r\n  // Close dialog when status update is successful\r\n  useEffect(() => {\r\n    if (\r\n      hasSubmitted &&\r\n      updateStatusFetcher.state === \"idle\" && \r\n      updateStatusFetcher.data?.success === true\r\n    ) {\r\n      setHasSubmitted(false);\r\n      onClose();\r\n    }\r\n  }, [updateStatusFetcher.state, updateStatusFetcher.data, hasSubmitted, onClose]);\r\n  \r\n  const handleSubmit = () => {\r\n    if (status === ticket.status) {\r\n      onClose();\r\n      return;\r\n    }\r\n    \r\n    setHasSubmitted(true);\r\n    updateStatusFetcher.submit(\r\n      { \r\n        status,\r\n        ticketId: ticket.ticketId.toString(),\r\n        _action: \"updateStatus\"\r\n      },\r\n      { \r\n        method: \"post\", \r\n        action: `/home/<USER>/status` \r\n      }\r\n    );\r\n  };\r\n\r\n  return (\r\n    <Dialog open={isOpen} onOpenChange={onClose}>\r\n      <DialogContent className=\"sm:max-w-[425px]\">\r\n        <DialogHeader>\r\n          <DialogTitle>Update Ticket Status</DialogTitle>\r\n        </DialogHeader>\r\n        \r\n        <div className=\"py-4\">\r\n          <RadioGroup value={status} onValueChange={(value) => setStatus(value as SupportTicketStatus)}>\r\n            <div className=\"flex items-center space-x-2\">\r\n              <RadioGroupItem value=\"OPEN\" id=\"open\" />\r\n              <Label htmlFor=\"open\" className=\"cursor-pointer\">Open</Label>\r\n            </div>\r\n            <div className=\"flex items-center space-x-2\">\r\n              <RadioGroupItem value=\"WIP\" id=\"wip\" />\r\n              <Label htmlFor=\"wip\" className=\"cursor-pointer\">Work In Progress</Label>\r\n            </div>\r\n            <div className=\"flex items-center space-x-2\">\r\n              <RadioGroupItem value=\"CLOSED\" id=\"closed\" />\r\n              <Label htmlFor=\"closed\" className=\"cursor-pointer\">Closed</Label>\r\n            </div>\r\n          </RadioGroup>\r\n        </div>\r\n        \r\n        <DialogFooter>\r\n          <Button variant=\"outline\" onClick={onClose}>\r\n            Cancel\r\n          </Button>\r\n          <Button \r\n            type=\"submit\" \r\n            onClick={handleSubmit} \r\n            loading={isSubmitting}\r\n            disabled={isSubmitting || status === ticket.status}\r\n          >\r\n            Update\r\n          </Button>\r\n        </DialogFooter>\r\n      </DialogContent>\r\n    </Dialog>\r\n  );\r\n} ", "/**\r\n * Get time difference in a readable format\r\n */\r\nexport function getTimeSinceCreation(date: string): string {\r\n  const now = new Date();\r\n  const createdAt = new Date(date);\r\n  const diffMs = now.getTime() - createdAt.getTime();\r\n  \r\n  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));\r\n  const diffHours = Math.floor((diffMs % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));\r\n  const diffMinutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));\r\n  \r\n  if (diffDays > 0) {\r\n    return `${diffDays} day${diffDays > 1 ? 's' : ''}`;\r\n  } else if (diffHours > 0) {\r\n    return `${diffHours} hour${diffHours > 1 ? 's' : ''}`;\r\n  } else {\r\n    return `${diffMinutes} minute${diffMinutes > 1 ? 's' : ''}`;\r\n  }\r\n}\r\n\r\n/**\r\n * Format date to locale string\r\n */\r\nexport function formatDate(date: string): string {\r\n  return new Date(date).toLocaleString();\r\n}\r\n\r\n/**\r\n * Sort tickets by updated date in ascending order\r\n */\r\nexport function sortTicketsByUpdatedAt<T extends { updatedAt: string }>(tickets: T[]): T[] {\r\n  return [...tickets].sort((a, b) => \r\n    new Date(a.updatedAt).getTime() - new Date(b.updatedAt).getTime()\r\n  );\r\n} ", "import { ClassValue, clsx } from 'clsx';\r\nimport { twMerge } from 'tailwind-merge';\r\n\r\n/**\r\n * Combines multiple class values into a single string, optimizing Tailwind CSS classes.\r\n * @param inputs - Class values to be combined\r\n * @returns Optimized class string\r\n */\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs));\r\n} ", "import { useState, useEffect } from 'react';\r\nimport { format } from 'date-fns';\r\nimport { Calendar } from './calendar';\r\nimport { Popover, PopoverContent, PopoverTrigger } from './popover';\r\nimport { Button } from './button';\r\nimport { cn } from '~/utils/cn';\r\nimport { CalendarIcon } from 'lucide-react';\r\n\r\ninterface DatePickerProps {\r\n  date: Date | null;\r\n  onSelect: (date: Date | null) => void;\r\n  className?: string;\r\n  id?: string;\r\n}\r\n\r\nexport function DatePicker({ date, onSelect, className, id }: DatePickerProps) {\r\n  const [open, setOpen] = useState(false);\r\n\r\n  // Make sure the internal state is synchronized with the prop\r\n  useEffect(() => {\r\n    if (!open) {\r\n      // No changes needed, component is closed\r\n      return;\r\n    }\r\n  }, [open, date]);\r\n\r\n  return (\r\n    <Popover open={open} onOpenChange={setOpen}>\r\n      <PopoverTrigger asChild>\r\n        <Button\r\n          variant=\"outline\"\r\n          className={cn(\r\n            'w-[240px] justify-start text-left font-normal',\r\n            !date && 'text-muted-foreground',\r\n            className\r\n          )}\r\n          id={id}\r\n        >\r\n          <CalendarIcon className=\"mr-2 h-4 w-4\" />\r\n          {date ? format(date, 'PPP') : <span>Select date</span>}\r\n        </Button>\r\n      </PopoverTrigger>\r\n      <PopoverContent className=\"w-auto p-0\" align=\"start\">\r\n        <Calendar\r\n          mode=\"single\"\r\n          selected={date || undefined}\r\n          onSelect={(newDate) => {\r\n            onSelect(newDate || null);\r\n            setOpen(false);\r\n          }}\r\n          initialFocus\r\n        />\r\n        {date && (\r\n          <div className=\"p-3 border-t border-border\">\r\n            <Button\r\n              variant=\"ghost\"\r\n              className=\"w-full justify-center\"\r\n              onClick={() => {\r\n                onSelect(null);\r\n                setOpen(false);\r\n              }}\r\n            >\r\n              Clear\r\n            </Button>\r\n          </div>\r\n        )}\r\n      </PopoverContent>\r\n    </Popover>\r\n  );\r\n} ", "import { use<PERSON><PERSON><PERSON><PERSON><PERSON>, useSearch<PERSON>ara<PERSON>, useNavigation } from \"@remix-run/react\";\r\nimport { json } from \"@remix-run/node\";\r\nimport { useState, useEffect } from \"react\";\r\nimport type { LoaderFunctionArgs, ActionFunctionArgs } from \"@remix-run/node\";\r\nimport {\r\n  Table,\r\n  TableBody,\r\n  TableCell,\r\n  TableHead,\r\n  TableHeader,\r\n  TableRow,\r\n} from \"~/components/ui/table\";\r\nimport { Button } from \"~/components/ui/button\";\r\nimport { Input } from \"~/components/ui/input\";\r\nimport { TicketStatusBadge } from \"~/components/ui/ticket-status-badge\";\r\nimport { TicketNotesDialog } from \"~/components/ui/ticket-notes-dialog\";\r\nimport { TicketStatusDialog } from \"~/components/ui/ticket-status-dialog\";\r\nimport { Ticket, TicketNote, SupportTicketStatus } from \"~/types/api/businessConsoleService/Tickets\";\r\nimport { getTickets, getTicketNotes } from \"~/services/ticketService\";\r\nimport { getTimeSinceCreation } from \"~/utils/ticketUtils\";\r\nimport { useToast } from \"~/hooks/use-toast\";\r\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from \"~/components/ui/select\";\r\nimport { DatePicker } from \"~/components/ui/date-picker\";\r\n\r\nexport const loader = async ({ request }: LoaderFunctionArgs) => {\r\n  try {\r\n    const url = new URL(request.url);\r\n    \r\n    // Extract all filter parameters from URL search params\r\n    const ticketId = url.searchParams.get('ticketId') ? Number(url.searchParams.get('ticketId')) : null;\r\n    const userId = url.searchParams.get('userId') ? Number(url.searchParams.get('userId')) : null;\r\n    const status = url.searchParams.get('status') ? \r\n      url.searchParams.getAll('status') as SupportTicketStatus[] : \r\n      null;\r\n    const fromDate = url.searchParams.get('fromDate') || null;\r\n    const toDate = url.searchParams.get('toDate') || null;\r\n    const pageNo = Number(url.searchParams.get('page') || '0');\r\n    const pageSize = Number(url.searchParams.get('pageSize') || '10');\r\n    \r\n    // Pass all parameters to the API\r\n    const ticketsResponse = await getTickets(\r\n      request, \r\n      {\r\n        ticketId,\r\n        userId,\r\n        status,\r\n        fromDate,\r\n        toDate,\r\n        pageNo,\r\n        pageSize,\r\n      }\r\n    );\r\n    \r\n    return json({ \r\n      tickets: ticketsResponse.data || [], \r\n      totalCount: ticketsResponse?.data?.length || 0,\r\n      pageNo,\r\n      pageSize\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Error loading tickets:\", error);\r\n    return json({ \r\n      tickets: [], \r\n      totalCount: 0, \r\n      pageNo: 0, \r\n      pageSize: 10, \r\n      error: \"Failed to load tickets\" \r\n    });\r\n  }\r\n};\r\n\r\nexport const action = async ({ request }: ActionFunctionArgs) => {\r\n  const formData = await request.formData();\r\n  const { _action } = Object.fromEntries(formData);\r\n  \r\n  try {\r\n    // Get notes for a ticket\r\n    if (_action === \"getNotes\") {\r\n      const ticketId = Number(formData.get(\"ticketId\"));\r\n      \r\n      if (!ticketId) {\r\n        return json({ success: false, error: \"Missing ticket ID\" });\r\n      }\r\n      \r\n      const notesResponse = await getTicketNotes(ticketId, request);\r\n      return json({ \r\n        success: true, \r\n        action: \"getNotes\", \r\n        notes: notesResponse.data\r\n      });\r\n    }\r\n    \r\n    return json({ success: false, error: \"Invalid action\" });\r\n  } catch (error) {\r\n    console.error(\"Error processing action:\", error);\r\n    return json({ success: false, error: \"Failed to process action\" });\r\n  }\r\n};\r\n\r\nexport default function TicketsPage() {\r\n  const { tickets, totalCount, pageNo, pageSize } = useLoaderData<typeof loader>();\r\n  const [searchParams, setSearchParams] = useSearchParams();\r\n  const { toast } = useToast();\r\n  \r\n  const [searchQuery, setSearchQuery] = useState(\"\");\r\n  const [selectedTicket, setSelectedTicket] = useState<Ticket | null>(null);\r\n  const [statusDialogOpen, setStatusDialogOpen] = useState(false);\r\n  const [notesDialogOpen, setNotesDialogOpen] = useState(false);\r\n  const [ticketNotes, setTicketNotes] = useState<TicketNote[]>([]);\r\n  const [isLoadingNotes, setIsLoadingNotes] = useState(false);\r\n  \r\n  // Listen for URL change or other state changes that should trigger refresh\r\n  useEffect(() => {\r\n    if (!notesDialogOpen) {\r\n      setTicketNotes([]);\r\n    }\r\n  }, [notesDialogOpen]);\r\n\r\n  // Apply client-side search filter to tickets (for quick filtering without API call)\r\n  const filteredTickets = tickets.filter((ticket) => {\r\n    const query = searchQuery.toLowerCase();\r\n    return (\r\n      ticket.userName.toLowerCase().includes(query) ||\r\n      ticket.userMobileNo.toLowerCase().includes(query) ||\r\n      ticket.sellerName?.toLowerCase().includes(query) ||\r\n      ticket.description.toLowerCase().includes(query) ||\r\n      ticket.userType.toLowerCase().includes(query) ||\r\n      ticket.ticketType?.toLowerCase().includes(query)\r\n    );\r\n  });\r\n  \r\n  const handleOpenNotesDialog = async (ticket: Ticket) => {\r\n    setSelectedTicket(ticket);\r\n    setNotesDialogOpen(true);\r\n    await fetchTicketNotes(ticket.ticketId);\r\n  };\r\n  \r\n  const fetchTicketNotes = async (ticketId: number) => {\r\n    setIsLoadingNotes(true);\r\n    \r\n    try {\r\n      // Get notes for the selected ticket\r\n      const form = new FormData();\r\n      form.append(\"_action\", \"getNotes\");\r\n      form.append(\"ticketId\", ticketId.toString());\r\n      \r\n      const response = await fetch(\"/home/<USER>\", {\r\n        method: \"POST\",\r\n        body: form\r\n      });\r\n      \r\n      const data = await response.json();\r\n      \r\n      if (data.success && data.notes) {\r\n        setTicketNotes(data.notes);\r\n        if (notesDialogOpen) {\r\n          toast({\r\n            title: \"Notes Loaded\",\r\n            description: \"Ticket notes loaded successfully\",\r\n            variant: \"default\"\r\n          });\r\n        }\r\n      } else {\r\n        setTicketNotes([]);\r\n        toast({\r\n          title: \"Error\",\r\n          description: data.error || \"Failed to load notes\",\r\n          variant: \"destructive\"\r\n        });\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error fetching notes:\", error);\r\n      toast({\r\n        title: \"Error\",\r\n        description: \"Failed to load ticket notes\",\r\n        variant: \"destructive\"\r\n      });\r\n      setTicketNotes([]);\r\n    } finally {\r\n      setIsLoadingNotes(false);\r\n    }\r\n  };\r\n  \r\n  const handleOpenStatusDialog = (ticket: Ticket) => {\r\n    setSelectedTicket(ticket);\r\n    setStatusDialogOpen(true);\r\n  };\r\n  \r\n  // Handle status filter change\r\n  const handleStatusFilterChange = (value: string) => {\r\n    if (value === \"ALL\") {\r\n      searchParams.delete(\"status\");\r\n    } else {\r\n      searchParams.set(\"status\", value);\r\n    }\r\n    searchParams.set(\"page\", \"0\"); // Reset to first page\r\n    setSearchParams(searchParams);\r\n  };\r\n  \r\n  // Handle date filter changes\r\n  const handleFromDateChange = (date: Date | null) => {\r\n    if (date) {\r\n      searchParams.set(\"fromDate\", date.toISOString().split('T')[0]);\r\n    } else {\r\n      searchParams.delete(\"fromDate\");\r\n    }\r\n    searchParams.set(\"page\", \"0\"); // Reset to first page\r\n    setSearchParams(searchParams);\r\n  };\r\n  \r\n  const handleToDateChange = (date: Date | null) => {\r\n    if (date) {\r\n      searchParams.set(\"toDate\", date.toISOString().split('T')[0]);\r\n    } else {\r\n      searchParams.delete(\"toDate\");\r\n    }\r\n    searchParams.set(\"page\", \"0\"); // Reset to first page\r\n    setSearchParams(searchParams);\r\n  };\r\n  \r\n  // Handle pagination\r\n  const handlePageChange = (newPage: number) => {\r\n    searchParams.set(\"page\", newPage.toString());\r\n    setSearchParams(searchParams);\r\n  };\r\n  \r\n  // Handle page size change\r\n  const handlePageSizeChange = (newSize: string) => {\r\n    searchParams.set(\"pageSize\", newSize);\r\n    searchParams.set(\"page\", \"0\"); // Reset to first page\r\n    setSearchParams(searchParams);\r\n  };\r\n  \r\n  // Calculate total pages\r\n  const totalPages = Math.ceil(totalCount / pageSize);\r\n  \r\n  return (\r\n    <div className=\"container mx-auto p-6\">\r\n      <div className=\"flex justify-between items-center mb-6\">\r\n        <h1 className=\"text-2xl font-bold\">Support Tickets</h1>\r\n      </div>\r\n      \r\n      <div className=\"flex flex-col space-y-4 mb-6\">\r\n        {/* Filters row */}\r\n        <div className=\"flex flex-wrap gap-4 items-end\">\r\n          {/* <div className=\"w-full md:w-auto\">\r\n            <Input\r\n              placeholder=\"Search tickets...\"\r\n              value={searchQuery}\r\n              onChange={(e) => setSearchQuery(e.target.value)}\r\n              className=\"w-full\"\r\n            />\r\n          </div> */}\r\n          \r\n          <div className=\"w-full md:w-auto\">\r\n            <label htmlFor=\"status-filter\" className=\"block text-sm font-medium mb-1\">Status</label>\r\n            <Select\r\n              defaultValue={searchParams.get(\"status\") || \"ALL\"}\r\n              onValueChange={handleStatusFilterChange}\r\n            >\r\n              <SelectTrigger className=\"w-[180px]\" id=\"status-filter\">\r\n                <SelectValue placeholder=\"Select status\" />\r\n              </SelectTrigger>\r\n              <SelectContent>\r\n                <SelectItem value=\"ALL\">All Statuses</SelectItem>\r\n                <SelectItem value=\"OPEN\">Open</SelectItem>\r\n                <SelectItem value=\"WIP\">In Progress</SelectItem>\r\n                <SelectItem value=\"CLOSED\">Closed</SelectItem>\r\n              </SelectContent>\r\n            </Select>\r\n          </div>\r\n          \r\n          <div className=\"w-full md:w-auto\">\r\n            <label htmlFor=\"from-date\" className=\"block text-sm font-medium mb-1\">From Date</label>\r\n            <DatePicker\r\n              date={searchParams.get(\"fromDate\") ? new Date(searchParams.get(\"fromDate\")!) : null}\r\n              onSelect={handleFromDateChange}\r\n              id=\"from-date\"\r\n            />\r\n          </div>\r\n          \r\n          <div className=\"w-full md:w-auto\">\r\n            <label htmlFor=\"to-date\" className=\"block text-sm font-medium mb-1\">To Date</label>\r\n            <DatePicker\r\n              date={searchParams.get(\"toDate\") ? new Date(searchParams.get(\"toDate\")!) : null}\r\n              onSelect={handleToDateChange}\r\n              id=\"to-date\"\r\n            />\r\n          </div>\r\n        </div>\r\n      </div>\r\n      \r\n      <div className=\"rounded-md border\">\r\n        <Table>\r\n          <TableHeader>\r\n            <TableRow>\r\n              <TableHead>Ticket ID</TableHead>\r\n              <TableHead>User Type</TableHead>\r\n              <TableHead>User Name</TableHead>\r\n              <TableHead>Phone</TableHead>\r\n              <TableHead>Seller</TableHead>\r\n              <TableHead>Ticket Type</TableHead>\r\n              <TableHead>Description</TableHead>\r\n              <TableHead>Last Update</TableHead>\r\n              <TableHead>Status</TableHead>\r\n              <TableHead>Time Unresolved</TableHead>\r\n              <TableHead>Actions</TableHead>\r\n            </TableRow>\r\n          </TableHeader>\r\n          <TableBody>\r\n            {filteredTickets.length > 0 ? (\r\n              filteredTickets.map((ticket) => (\r\n                <TableRow key={ticket.ticketId}>\r\n                  <TableCell>#{ticket.ticketId}</TableCell>\r\n                  <TableCell>{ticket.userType}</TableCell>\r\n                  <TableCell>{ticket.userName}</TableCell>\r\n                  <TableCell>{ticket.userMobileNo}</TableCell>\r\n                  <TableCell>{ticket.sellerName}</TableCell>\r\n                  <TableCell>{ticket.ticketType}</TableCell>\r\n                  <TableCell className=\"max-w-[200px] truncate\">\r\n                    {ticket.description}\r\n                  </TableCell>\r\n                  <TableCell>\r\n                    <div className=\"flex flex-col\">\r\n                      <span>{new Date(ticket.lastModifiedDate).toLocaleDateString()}</span>\r\n                    </div>\r\n                  </TableCell>\r\n                  <TableCell>\r\n                    <TicketStatusBadge status={ticket.status} />\r\n                  </TableCell>\r\n                  <TableCell>\r\n                    {getTimeSinceCreation(ticket.createdDate)}\r\n                  </TableCell>\r\n                  <TableCell>\r\n                    <div className=\"flex gap-2\">\r\n                      <Button\r\n                        size=\"sm\"\r\n                        variant=\"outline\"\r\n                        onClick={() => handleOpenNotesDialog(ticket)}\r\n                      >\r\n                        Notes\r\n                      </Button>\r\n                      <Button\r\n                        size=\"sm\"\r\n                        onClick={() => handleOpenStatusDialog(ticket)}\r\n                      >\r\n                        Update\r\n                      </Button>\r\n                    </div>\r\n                  </TableCell>\r\n                </TableRow>\r\n              ))\r\n            ) : (\r\n              <TableRow>\r\n                <TableCell\r\n                  colSpan={11}\r\n                  className=\"h-24 text-center\"\r\n                >\r\n                  No tickets found.\r\n                </TableCell>\r\n              </TableRow>\r\n            )}\r\n          </TableBody>\r\n        </Table>\r\n      </div>\r\n      \r\n      {/* Pagination controls */}\r\n      {totalPages > 0 && (\r\n        <div className=\"flex justify-between items-center mt-4\">\r\n          <div className=\"flex items-center gap-2\">\r\n            <span className=\"text-sm text-gray-600\">\r\n              Showing {(pageNo) * pageSize} to {Math.min((pageNo + 1) * pageSize, totalCount)} of {totalCount} items\r\n            </span>\r\n            <Select\r\n              value={pageSize.toString()}\r\n              onValueChange={handlePageSizeChange}\r\n            >\r\n              <SelectTrigger className=\"w-[80px]\">\r\n                <SelectValue />\r\n              </SelectTrigger>\r\n              <SelectContent>\r\n                <SelectItem value=\"5\">5</SelectItem>\r\n                <SelectItem value=\"10\">10</SelectItem>\r\n                <SelectItem value=\"25\">25</SelectItem>\r\n                <SelectItem value=\"50\">50</SelectItem>\r\n              </SelectContent>\r\n            </Select>\r\n            <span className=\"text-sm text-gray-600\">per page</span>\r\n          </div>\r\n          \r\n          <div className=\"flex gap-1\">\r\n            <Button \r\n              variant=\"outline\" \r\n              size=\"sm\"\r\n              onClick={() => handlePageChange(0)}\r\n              disabled={pageNo === 0}\r\n            >\r\n              First\r\n            </Button>\r\n            <Button \r\n              variant=\"outline\" \r\n              size=\"sm\"\r\n              onClick={() => handlePageChange(pageNo - 1)}\r\n              disabled={pageNo === 0}\r\n            >\r\n              Previous\r\n            </Button>\r\n            <span className=\"flex items-center px-3 text-sm\">\r\n              Page {pageNo + 1} of {totalPages + 1}\r\n            </span>\r\n            <Button \r\n              variant=\"outline\" \r\n              size=\"sm\"\r\n              onClick={() => handlePageChange(pageNo + 1)}\r\n              disabled={pageNo >= totalPages}\r\n            >\r\n              Next\r\n            </Button>\r\n            <Button \r\n              variant=\"outline\" \r\n              size=\"sm\"\r\n              onClick={() => handlePageChange(totalPages - 1)}\r\n              disabled={pageNo >= totalPages}\r\n            >\r\n              Last\r\n            </Button>\r\n          </div>\r\n        </div>\r\n      )}\r\n      \r\n      {/* Ticket Notes Dialog */}\r\n      {selectedTicket && (\r\n        <TicketNotesDialog\r\n          isOpen={notesDialogOpen}\r\n          onClose={() => setNotesDialogOpen(false)}\r\n          ticketId={selectedTicket.ticketId}\r\n          ticketNotes={ticketNotes}\r\n        />\r\n      )}\r\n      \r\n      {/* Ticket Status Update Dialog */}\r\n      {selectedTicket && (\r\n        <TicketStatusDialog\r\n          isOpen={statusDialogOpen}\r\n          onClose={() => {\r\n            setStatusDialogOpen(false);\r\n          }}\r\n          ticket={selectedTicket}\r\n        />\r\n      )}\r\n    </div>\r\n  );\r\n} "], "names": ["jsx", "cn", "useState", "useEffect", "jsxs", "CalendarIcon", "Calendar", "TicketsPage", "tickets", "totalCount", "pageNo", "pageSize", "useLoaderData", "searchParams", "setSearchParams", "useSearchParams", "toast", "useToast", "searchQuery", "setSearch<PERSON>uery", "selected<PERSON><PERSON>et", "setSelectedTicket", "statusDialogOpen", "setStatusDialogOpen", "notesDialogOpen", "setNotesDialogOpen", "ticketNotes", "setTicketNotes", "isLoadingNotes", "setIsLoadingNotes", "filteredTickets", "filter", "ticket", "query", "toLowerCase", "userName", "includes", "userMobileNo", "sellerName", "description", "userType", "ticketType", "handleOpenNotesDialog", "fetchTicketNotes", "ticketId", "form", "FormData", "append", "toString", "response", "fetch", "method", "body", "data", "json", "success", "notes", "title", "variant", "error", "console", "handleOpenStatusDialog", "handleStatusFilterChange", "value", "delete", "set", "handleFromDateChange", "date", "toISOString", "split", "handleToDateChange", "handlePageChange", "newPage", "handlePageSizeChange", "newSize", "totalPages", "Math", "ceil", "className", "children", "htmlFor", "Select", "defaultValue", "get", "onValueChange", "SelectTrigger", "id", "SelectValue", "placeholder", "SelectContent", "SelectItem", "DatePicker", "Date", "onSelect", "Table", "TableHeader", "TableRow", "TableHead", "TableBody", "length", "map", "TableCell", "lastModifiedDate", "toLocaleDateString", "TicketStatusBadge", "status", "getTimeSinceCreation", "createdDate", "<PERSON><PERSON>", "size", "onClick", "colSpan", "min", "disabled", "TicketNotesDialog", "isOpen", "onClose", "TicketStatusDialog"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASO,SAAS,kBAAkB,EAAE,QAAQ,aAAqC;AAC/E,QAAM,eAAoD;AAAA,IACxD,MAAM;AAAA,IACN,KAAK;AAAA,IACL,QAAQ;AAAA,EACV;AAGA,QAAM,QAAQ,aAAa,MAAM,KAAK,aAAa;AAGnD,QAAM,cAAc,WAAW,QAAQ,qBAAqB,OAAO,OAAO,CAAC,IAAI,OAAO,MAAM,CAAC,EAAE,YAAY;AAGzG,SAAAA,kCAAA;AAAA,IAAC;AAAA,IAAA;AAAA,MACC,WAAWC,KAAG,OAAO,SAAS;AAAA,MAC9B,SAAQ;AAAA,MAEP,UAAA;AAAA,IAAA;AAAA,EACH;AAEJ;ACRO,SAAS,kBAAkB;AAAA,EAChC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,GAA2B;AACzB,QAAM,CAAC,MAAM,OAAO,IAAIC,aAAAA,SAAS,EAAE;AACnC,QAAM,iBAAiB,WAA+B;AACtD,QAAM,CAAC,cAAc,eAAe,IAAIA,aAAAA,SAAS,KAAK;AAEhD,QAAA,eAAe,eAAe,UAAU;AAG9CC,eAAAA,UAAU,MAAM;;AACd,QACE,gBACA,eAAe,UAAU,YACzB,oBAAe,SAAf,mBAAqB,aAAY,MACjC;AACA,cAAQ,EAAE;AACV,sBAAgB,KAAK;AAGb,cAAA;AAAA,IAAA;AAAA,EACV,GACC,CAAC,eAAe,OAAO,eAAe,MAAM,cAAc,OAAO,CAAC;AAErE,QAAM,eAAe,MAAM;AACrB,QAAA,CAAC,KAAK,OAAQ;AAElB,oBAAgB,IAAI;AACL,mBAAA;AAAA,MACb;AAAA,QACE;AAAA,QACA,UAAU,SAAS,SAAS;AAAA,QAC5B,SAAS;AAAA,MACX;AAAA,MACA;AAAA,QACE,QAAQ;AAAA,QACR,QAAQ;AAAA,MAAA;AAAA,IAEZ;AAAA,EACF;AAGE,SAAAH,kCAAA,IAAC,UAAO,MAAM,QAAQ,cAAc,SAClC,UAAAI,kCAAA,KAAC,eAAc,EAAA,WAAU,oBACvB,UAAA;AAAA,IAAAJ,sCAAC,cACC,EAAA,UAAAA,kCAAA,IAAC,aAAY,EAAA,UAAA,eAAY,CAAA,GAC3B;AAAA,0CAEC,OAAI,EAAA,WAAU,sCACZ,UAAA,eAAe,YAAY,SAAS,IAClCA,kCAAA,IAAA,OAAA,EAAI,WAAU,aACZ,UAAA,YAAY,IAAI,CAAC,aACfA,kCAAAA,IAAA,OAAA,EAAgC,WAAU,oCACzC,gDAAC,KAAE,EAAA,WAAU,WAAW,UAAA,SAAS,KAAK,CAAA,KAD9B,SAAS,YAEnB,CACD,EACH,CAAA,IAEAA,kCAAA,IAAC,OAAE,WAAU,kCAAiC,+BAAkB,CAAA,GAEpE;AAAA,0CAEC,OAAI,EAAA,WAAU,iBACb,UAACI,kCAAA,KAAA,OAAA,EAAI,WAAU,2BACb,UAAA;AAAA,MAAAJ,kCAAA;AAAA,QAAC;AAAA,QAAA;AAAA,UACC,OAAO;AAAA,UACP,UAAU,CAAC,MAAM,QAAQ,EAAE,OAAO,KAAK;AAAA,UACvC,aAAY;AAAA,UACZ,WAAU;AAAA,QAAA;AAAA,MACZ;AAAA,MACAA,kCAAA;AAAA,QAAC;AAAA,QAAA;AAAA,UACC,SAAS;AAAA,UACT,UAAU,CAAC,KAAK,KAAA,KAAU;AAAA,UAC1B,SAAS;AAAA,UACT,WAAU;AAAA,UACX,UAAA;AAAA,QAAA;AAAA,MAAA;AAAA,IAED,EAAA,CACF,EACF,CAAA;AAAA,EAAA,EAAA,CACF,EACF,CAAA;AAEJ;AC3FO,SAAS,mBAAmB;AAAA,EACjC;AAAA,EACA;AAAA,EACA;AACF,GAA4B;AAC1B,QAAM,CAAC,QAAQ,SAAS,IAAIE,aAAAA,SAA8B,OAAO,MAAM;AACvE,QAAM,sBAAsB,WAAW;AACvC,QAAM,CAAC,cAAc,eAAe,IAAIA,aAAAA,SAAS,KAAK;AAEhD,QAAA,eAAe,oBAAoB,UAAU;AAGnDC,eAAAA,UAAU,MAAM;;AACd,QACE,gBACA,oBAAoB,UAAU,YAC9B,yBAAoB,SAApB,mBAA0B,aAAY,MACtC;AACA,sBAAgB,KAAK;AACb,cAAA;AAAA,IAAA;AAAA,EACV,GACC,CAAC,oBAAoB,OAAO,oBAAoB,MAAM,cAAc,OAAO,CAAC;AAE/E,QAAM,eAAe,MAAM;AACrB,QAAA,WAAW,OAAO,QAAQ;AACpB,cAAA;AACR;AAAA,IAAA;AAGF,oBAAgB,IAAI;AACA,wBAAA;AAAA,MAClB;AAAA,QACE;AAAA,QACA,UAAU,OAAO,SAAS,SAAS;AAAA,QACnC,SAAS;AAAA,MACX;AAAA,MACA;AAAA,QACE,QAAQ;AAAA,QACR,QAAQ;AAAA,MAAA;AAAA,IAEZ;AAAA,EACF;AAGE,SAAAH,kCAAA,IAAC,UAAO,MAAM,QAAQ,cAAc,SAClC,UAAAI,kCAAA,KAAC,eAAc,EAAA,WAAU,oBACvB,UAAA;AAAA,IAAAJ,sCAAC,cACC,EAAA,UAAAA,kCAAA,IAAC,aAAY,EAAA,UAAA,uBAAoB,CAAA,GACnC;AAAA,IAECA,kCAAA,IAAA,OAAA,EAAI,WAAU,QACb,UAACI,kCAAAA,KAAA,YAAA,EAAW,OAAO,QAAQ,eAAe,CAAC,UAAU,UAAU,KAA4B,GACzF,UAAA;AAAA,MAACA,kCAAAA,KAAA,OAAA,EAAI,WAAU,+BACb,UAAA;AAAA,QAAAJ,kCAAA,IAAC,gBAAe,EAAA,OAAM,QAAO,IAAG,QAAO;AAAA,8CACtC,OAAM,EAAA,SAAQ,QAAO,WAAU,kBAAiB,UAAI,OAAA,CAAA;AAAA,MAAA,GACvD;AAAA,MACAI,kCAAAA,KAAC,OAAI,EAAA,WAAU,+BACb,UAAA;AAAA,QAAAJ,kCAAA,IAAC,gBAAe,EAAA,OAAM,OAAM,IAAG,OAAM;AAAA,8CACpC,OAAM,EAAA,SAAQ,OAAM,WAAU,kBAAiB,UAAgB,mBAAA,CAAA;AAAA,MAAA,GAClE;AAAA,MACAI,kCAAAA,KAAC,OAAI,EAAA,WAAU,+BACb,UAAA;AAAA,QAAAJ,kCAAA,IAAC,gBAAe,EAAA,OAAM,UAAS,IAAG,UAAS;AAAA,8CAC1C,OAAM,EAAA,SAAQ,UAAS,WAAU,kBAAiB,UAAM,SAAA,CAAA;AAAA,MAAA,EAC3D,CAAA;AAAA,IAAA,EAAA,CACF,EACF,CAAA;AAAA,2CAEC,cACC,EAAA,UAAA;AAAA,MAAAA,sCAAC,QAAO,EAAA,SAAQ,WAAU,SAAS,SAAS,UAE5C,UAAA;AAAA,MACAA,kCAAA;AAAA,QAAC;AAAA,QAAA;AAAA,UACC,MAAK;AAAA,UACL,SAAS;AAAA,UACT,SAAS;AAAA,UACT,UAAU,gBAAgB,WAAW,OAAO;AAAA,UAC7C,UAAA;AAAA,QAAA;AAAA,MAAA;AAAA,IAED,EACF,CAAA;AAAA,EAAA,EAAA,CACF,EACF,CAAA;AAEJ;ACjGO,SAAS,qBAAqB,MAAsB;AACnD,QAAA,0BAAU,KAAK;AACf,QAAA,YAAY,IAAI,KAAK,IAAI;AAC/B,QAAM,SAAS,IAAI,QAAQ,IAAI,UAAU,QAAQ;AAEjD,QAAM,WAAW,KAAK,MAAM,UAAU,MAAO,KAAK,KAAK,GAAG;AACpD,QAAA,YAAY,KAAK,MAAO,UAAU,MAAO,KAAK,KAAK,OAAQ,MAAO,KAAK,GAAG;AAC1E,QAAA,cAAc,KAAK,MAAO,UAAU,MAAO,KAAK,OAAQ,MAAO,GAAG;AAExE,MAAI,WAAW,GAAG;AAChB,WAAO,GAAG,QAAQ,OAAO,WAAW,IAAI,MAAM,EAAE;AAAA,EAAA,WACvC,YAAY,GAAG;AACxB,WAAO,GAAG,SAAS,QAAQ,YAAY,IAAI,MAAM,EAAE;AAAA,EAAA,OAC9C;AACL,WAAO,GAAG,WAAW,UAAU,cAAc,IAAI,MAAM,EAAE;AAAA,EAAA;AAE7D;ACXO,SAAS,MAAM,QAAsB;AACnC,SAAA,QAAQ,KAAK,MAAM,CAAC;AAC7B;ACKO,SAAS,WAAW,EAAE,MAAM,UAAU,WAAW,MAAuB;AAC7E,QAAM,CAAC,MAAM,OAAO,IAAIE,aAAAA,SAAS,KAAK;AAGtCC,eAAAA,UAAU,MAAM;AACd,QAAI,CAAC,MAAM;AAET;AAAA,IAAA;AAAA,EACF,GACC,CAAC,MAAM,IAAI,CAAC;AAEf,SACGC,kCAAAA,KAAA,SAAA,EAAQ,MAAY,cAAc,SACjC,UAAA;AAAA,IAACJ,kCAAAA,IAAA,gBAAA,EAAe,SAAO,MACrB,UAAAI,kCAAA;AAAA,MAAC;AAAA,MAAA;AAAA,QACC,SAAQ;AAAA,QACR,WAAW;AAAA,UACT;AAAA,UACA,CAAC,QAAQ;AAAA,UACT;AAAA,QACF;AAAA,QACA;AAAA,QAEA,UAAA;AAAA,UAACJ,kCAAAA,IAAAK,UAAA,EAAa,WAAU,eAAe,CAAA;AAAA,UACtC,OAAO,OAAO,MAAM,KAAK,IAAIL,kCAAAA,IAAC,UAAK,UAAW,cAAA,CAAA;AAAA,QAAA;AAAA,MAAA;AAAA,IAAA,GAEnD;AAAA,IACCI,kCAAA,KAAA,gBAAA,EAAe,WAAU,cAAa,OAAM,SAC3C,UAAA;AAAA,MAAAJ,kCAAA;AAAA,QAACM;AAAAA,QAAA;AAAA,UACC,MAAK;AAAA,UACL,UAAU,QAAQ;AAAA,UAClB,UAAU,CAAC,YAAY;AACrB,qBAAS,WAAW,IAAI;AACxB,oBAAQ,KAAK;AAAA,UACf;AAAA,UACA,cAAY;AAAA,QAAA;AAAA,MACd;AAAA,MACC,QACCN,kCAAA,IAAC,OAAI,EAAA,WAAU,8BACb,UAAAA,kCAAA;AAAA,QAAC;AAAA,QAAA;AAAA,UACC,SAAQ;AAAA,UACR,WAAU;AAAA,UACV,SAAS,MAAM;AACb,qBAAS,IAAI;AACb,oBAAQ,KAAK;AAAA,UACf;AAAA,UACD,UAAA;AAAA,QAAA;AAAA,MAAA,EAGH,CAAA;AAAA,IAAA,EAEJ,CAAA;AAAA,EAAA,GACF;AAEJ;AC8BA,SAAwBO,cAAc;AACpC,QAAM;AAAA,IAAEC;AAAAA,IAASC;AAAAA,IAAYC;AAAAA,IAAQC;AAAAA,MAAaC,cAA6B;AAC/E,QAAM,CAACC,cAAcC,eAAe,IAAIC,gBAAgB;AAClD,QAAA;AAAA,IAAEC;AAAAA,EAAM,IAAIC,SAAS;AAE3B,QAAM,CAACC,aAAaC,cAAc,IAAIjB,aAAAA,SAAS,EAAE;AACjD,QAAM,CAACkB,gBAAgBC,iBAAiB,IAAInB,aAAAA,SAAwB,IAAI;AACxE,QAAM,CAACoB,kBAAkBC,mBAAmB,IAAIrB,aAAAA,SAAS,KAAK;AAC9D,QAAM,CAACsB,iBAAiBC,kBAAkB,IAAIvB,aAAAA,SAAS,KAAK;AAC5D,QAAM,CAACwB,aAAaC,cAAc,IAAIzB,aAAAA,SAAuB,CAAA,CAAE;AAC/D,QAAM,CAAC0B,gBAAgBC,iBAAiB,IAAI3B,aAAAA,SAAS,KAAK;AAG1DC,eAAAA,UAAU,MAAM;AACd,QAAI,CAACqB,iBAAiB;AACpBG,qBAAe,CAAA,CAAE;AAAA,IACnB;AAAA,EACF,GAAG,CAACH,eAAe,CAAC;AAGpB,QAAMM,kBAAkBtB,QAAQuB,OAAQC,YAAW;;AAC3C,UAAAC,QAAQf,YAAYgB,YAAY;AACtC,WACEF,OAAOG,SAASD,YAAA,EAAcE,SAASH,KAAK,KAC5CD,OAAOK,aAAaH,YAAY,EAAEE,SAASH,KAAK,OAChDD,YAAOM,eAAPN,mBAAmBE,cAAcE,SAASH,WAC1CD,OAAOO,YAAYL,YAAY,EAAEE,SAASH,KAAK,KAC/CD,OAAOQ,SAASN,YAAc,EAAAE,SAASH,KAAK,OAC5CD,YAAOS,eAAPT,mBAAmBE,cAAcE,SAASH;AAAAA,EAE9C,CAAC;AAEK,QAAAS,wBAAwB,OAAOV,WAAmB;AACtDX,sBAAkBW,MAAM;AACxBP,uBAAmB,IAAI;AACjB,UAAAkB,iBAAiBX,OAAOY,QAAQ;AAAA,EACxC;AAEM,QAAAD,mBAAmB,OAAOC,aAAqB;AACnDf,sBAAkB,IAAI;AAElB,QAAA;AAEI,YAAAgB,OAAO,IAAIC,SAAS;AACrBD,WAAAE,OAAO,WAAW,UAAU;AACjCF,WAAKE,OAAO,YAAYH,SAASI,SAAA,CAAU;AAErC,YAAAC,WAAW,MAAMC,MAAM,iBAAiB;AAAA,QAC5CC,QAAQ;AAAA,QACRC,MAAMP;AAAAA,MACR,CAAC;AAEK,YAAAQ,OAAO,MAAMJ,SAASK,KAAK;AAE7B,UAAAD,KAAKE,WAAWF,KAAKG,OAAO;AAC9B7B,uBAAe0B,KAAKG,KAAK;AACzB,YAAIhC,iBAAiB;AACbR,gBAAA;AAAA,YACJyC,OAAO;AAAA,YACPlB,aAAa;AAAA,YACbmB,SAAS;AAAA,UACX,CAAC;AAAA,QACH;AAAA,MACF,OAAO;AACL/B,uBAAe,CAAA,CAAE;AACXX,cAAA;AAAA,UACJyC,OAAO;AAAA,UACPlB,aAAac,KAAKM,SAAS;AAAA,UAC3BD,SAAS;AAAA,QACX,CAAC;AAAA,MACH;AAAA,aACOC,OAAO;AACNC,cAAAD,MAAM,yBAAyBA,KAAK;AACtC3C,YAAA;AAAA,QACJyC,OAAO;AAAA,QACPlB,aAAa;AAAA,QACbmB,SAAS;AAAA,MACX,CAAC;AACD/B,qBAAe,CAAA,CAAE;AAAA,IACnB,UAAE;AACAE,wBAAkB,KAAK;AAAA,IACzB;AAAA,EACF;AAEM,QAAAgC,yBAA0B7B,YAAmB;AACjDX,sBAAkBW,MAAM;AACxBT,wBAAoB,IAAI;AAAA,EAC1B;AAGM,QAAAuC,2BAA4BC,WAAkB;AAClD,QAAIA,UAAU,OAAO;AACnBlD,mBAAamD,OAAO,QAAQ;AAAA,IAC9B,OAAO;AACQnD,mBAAAoD,IAAI,UAAUF,KAAK;AAAA,IAClC;AACalD,iBAAAoD,IAAI,QAAQ,GAAG;AAC5BnD,oBAAgBD,YAAY;AAAA,EAC9B;AAGM,QAAAqD,uBAAwBC,UAAsB;AAClD,QAAIA,MAAM;AACKtD,mBAAAoD,IAAI,YAAYE,KAAKC,cAAcC,MAAM,GAAG,EAAE,CAAC,CAAC;AAAA,IAC/D,OAAO;AACLxD,mBAAamD,OAAO,UAAU;AAAA,IAChC;AACanD,iBAAAoD,IAAI,QAAQ,GAAG;AAC5BnD,oBAAgBD,YAAY;AAAA,EAC9B;AAEM,QAAAyD,qBAAsBH,UAAsB;AAChD,QAAIA,MAAM;AACKtD,mBAAAoD,IAAI,UAAUE,KAAKC,cAAcC,MAAM,GAAG,EAAE,CAAC,CAAC;AAAA,IAC7D,OAAO;AACLxD,mBAAamD,OAAO,QAAQ;AAAA,IAC9B;AACanD,iBAAAoD,IAAI,QAAQ,GAAG;AAC5BnD,oBAAgBD,YAAY;AAAA,EAC9B;AAGM,QAAA0D,mBAAoBC,aAAoB;AAC5C3D,iBAAaoD,IAAI,QAAQO,QAAQxB,SAAA,CAAU;AAC3ClC,oBAAgBD,YAAY;AAAA,EAC9B;AAGM,QAAA4D,uBAAwBC,aAAoB;AACnC7D,iBAAAoD,IAAI,YAAYS,OAAO;AACvB7D,iBAAAoD,IAAI,QAAQ,GAAG;AAC5BnD,oBAAgBD,YAAY;AAAA,EAC9B;AAGA,QAAM8D,aAAaC,KAAKC,KAAKpE,aAAaE,QAAQ;AAGhD,SAAAP,kCAAAA,KAAC,OAAI;AAAA,IAAA0E,WAAU;AAAA,IACbC,UAAA,CAAC/E,kCAAA,IAAA,OAAA;AAAA,MAAI8E,WAAU;AAAA,MACbC,UAAA/E,kCAAA,IAAC;QAAG8E,WAAU;AAAA,QAAqBC;MAAe,CAAA;AAAA,IACpD,CAAA,yCAEC,OAAI;AAAA,MAAAD,WAAU;AAAA,MAEbC,UAAC3E,kCAAA,KAAA,OAAA;AAAA,QAAI0E,WAAU;AAAA,QAUbC,UAAA,CAAC3E,kCAAA,KAAA,OAAA;AAAA,UAAI0E,WAAU;AAAA,UACbC,UAAA,CAAA/E,kCAAA,IAAC,SAAM;AAAA,YAAAgF,SAAQ;AAAA,YAAgBF,WAAU;AAAA,YAAiCC,UAAM;AAAA,UAAA,CAAA,GAChF3E,kCAAA,KAAC6E,QAAA;AAAA,YACCC,cAAcrE,aAAasE,IAAI,QAAQ,KAAK;AAAA,YAC5CC,eAAetB;AAAAA,YAEfiB,UAAA,CAAC/E,kCAAA,IAAAqF,eAAA;AAAA,cAAcP,WAAU;AAAA,cAAYQ,IAAG;AAAA,cACtCP,UAAC/E,kCAAA,IAAAuF,aAAA;AAAA,gBAAYC,aAAY;AAAA,cAAgB,CAAA;AAAA,YAC3C,CAAA,0CACCC,eACC;AAAA,cAAAV,UAAA,CAAC/E,kCAAA,IAAA0F,YAAA;AAAA,gBAAW3B,OAAM;AAAA,gBAAMgB,UAAY;AAAA,cAAA,CAAA,GACnC/E,kCAAA,IAAA0F,YAAA;AAAA,gBAAW3B,OAAM;AAAA,gBAAOgB,UAAI;AAAA,cAAA,CAAA,GAC5B/E,kCAAA,IAAA0F,YAAA;AAAA,gBAAW3B,OAAM;AAAA,gBAAMgB,UAAW;AAAA,cAAA,CAAA,GAClC/E,kCAAA,IAAA0F,YAAA;AAAA,gBAAW3B,OAAM;AAAA,gBAASgB,UAAM;AAAA,cAAA,CAAA,CAAA;AAAA,YACnC,CAAA,CAAA;AAAA,UAAA,CACF,CAAA;AAAA,QACF,CAAA,GAEA3E,kCAAA,KAAC,OAAI;AAAA,UAAA0E,WAAU;AAAA,UACbC,UAAA,CAAA/E,kCAAA,IAAC,SAAM;AAAA,YAAAgF,SAAQ;AAAA,YAAYF,WAAU;AAAA,YAAiCC,UAAS;AAAA,UAAA,CAAA,GAC/E/E,kCAAA,IAAC2F,YAAA;AAAA,YACCxB,MAAMtD,aAAasE,IAAI,UAAU,IAAI,IAAIS,KAAK/E,aAAasE,IAAI,UAAU,CAAE,IAAI;AAAA,YAC/EU,UAAU3B;AAAAA,YACVoB,IAAG;AAAA,UAAA,CACL,CAAA;AAAA,QACF,CAAA,GAEAlF,kCAAA,KAAC,OAAI;AAAA,UAAA0E,WAAU;AAAA,UACbC,UAAA,CAAA/E,kCAAA,IAAC,SAAM;AAAA,YAAAgF,SAAQ;AAAA,YAAUF,WAAU;AAAA,YAAiCC,UAAO;AAAA,UAAA,CAAA,GAC3E/E,kCAAA,IAAC2F,YAAA;AAAA,YACCxB,MAAMtD,aAAasE,IAAI,QAAQ,IAAI,IAAIS,KAAK/E,aAAasE,IAAI,QAAQ,CAAE,IAAI;AAAA,YAC3EU,UAAUvB;AAAAA,YACVgB,IAAG;AAAA,UAAA,CACL,CAAA;AAAA,QACF,CAAA,CAAA;AAAA,MACF,CAAA;AAAA,IACF,CAAA,GAECtF,kCAAA,IAAA,OAAA;AAAA,MAAI8E,WAAU;AAAA,MACbC,iDAACe,OACC;AAAA,QAAAf,UAAA,CAAC/E,kCAAA,IAAA+F,aAAA;AAAA,UACChB,iDAACiB,UACC;AAAA,YAAAjB,UAAA,CAAA/E,kCAAA,IAACiG;cAAUlB,UAAS;AAAA,YAAA,CAAA,GACpB/E,kCAAA,IAACiG;cAAUlB,UAAS;AAAA,YAAA,CAAA,GACpB/E,kCAAA,IAACiG;cAAUlB,UAAS;AAAA,YAAA,CAAA,GACpB/E,kCAAA,IAACiG;cAAUlB,UAAK;AAAA,YAAA,CAAA,GAChB/E,kCAAA,IAACiG;cAAUlB,UAAM;AAAA,YAAA,CAAA,GACjB/E,kCAAA,IAACiG;cAAUlB,UAAW;AAAA,YAAA,CAAA,GACtB/E,kCAAA,IAACiG;cAAUlB,UAAW;AAAA,YAAA,CAAA,GACtB/E,kCAAA,IAACiG;cAAUlB,UAAW;AAAA,YAAA,CAAA,GACtB/E,kCAAA,IAACiG;cAAUlB,UAAM;AAAA,YAAA,CAAA,GACjB/E,kCAAA,IAACiG;cAAUlB,UAAe;AAAA,YAAA,CAAA,GAC1B/E,kCAAA,IAACiG;cAAUlB,UAAO;AAAA,YAAA,CAAA,CAAA;AAAA,UACpB,CAAA;AAAA,QACF,CAAA,GACA/E,kCAAA,IAACkG,WACE;AAAA,UAAAnB,UAAAjD,gBAAgBqE,SAAS,IACxBrE,gBAAgBsE,IAAKpE,YACnB5B,kCAAA,KAAC4F,UACC;AAAA,YAAAjB,UAAA,CAAA3E,kCAAA,KAACiG,WAAU;AAAA,cAAAtB,UAAA,CAAA,KAAE/C,OAAOY,QAAA;AAAA,YAAS,CAAA,GAC7B5C,kCAAA,IAACqG,WAAW;AAAA,cAAAtB,UAAA/C,OAAOQ;AAAAA,YAAS,CAAA,GAC5BxC,kCAAA,IAACqG,WAAW;AAAA,cAAAtB,UAAA/C,OAAOG;AAAAA,YAAS,CAAA,GAC5BnC,kCAAA,IAACqG,WAAW;AAAA,cAAAtB,UAAA/C,OAAOK;AAAAA,YAAa,CAAA,GAChCrC,kCAAA,IAACqG,WAAW;AAAA,cAAAtB,UAAA/C,OAAOM;AAAAA,YAAW,CAAA,GAC9BtC,kCAAA,IAACqG,WAAW;AAAA,cAAAtB,UAAA/C,OAAOS;AAAAA,YAAW,CAAA,GAC7BzC,kCAAA,IAAAqG,WAAA;AAAA,cAAUvB,WAAU;AAAA,cAClBC,iBAAOxC;AAAAA,YACV,CAAA,yCACC8D,WACC;AAAA,cAAAtB,UAAA/E,kCAAA,IAAC,OAAI;AAAA,gBAAA8E,WAAU;AAAA,gBACbC,UAAC/E,kCAAA,IAAA,QAAA;AAAA,kBAAM+E,UAAI,IAAAa,KAAK5D,OAAOsE,gBAAgB,EAAEC,mBAAmB;AAAA,gBAAE,CAAA;AAAA,cAChE,CAAA;AAAA,YACF,CAAA,yCACCF,WACC;AAAA,cAAAtB,UAAA/E,kCAAA,IAACwG;gBAAkBC,QAAQzE,OAAOyE;AAAAA,cAAQ,CAAA;AAAA,YAC5C,CAAA,GACCzG,kCAAA,IAAAqG,WAAA;AAAA,cACEtB,UAAqB2B,qBAAA1E,OAAO2E,WAAW;AAAA,YAC1C,CAAA,GACC3G,kCAAA,IAAAqG,WAAA;AAAA,cACCtB,UAAC3E,kCAAA,KAAA,OAAA;AAAA,gBAAI0E,WAAU;AAAA,gBACbC,UAAA,CAAA/E,kCAAA,IAAC4G,QAAA;AAAA,kBACCC,MAAK;AAAA,kBACLnD,SAAQ;AAAA,kBACRoD,SAASA,MAAMpE,sBAAsBV,MAAM;AAAA,kBAC5C+C,UAAA;AAAA,gBAAA,CAED,GACA/E,kCAAA,IAAC4G,QAAA;AAAA,kBACCC,MAAK;AAAA,kBACLC,SAASA,MAAMjD,uBAAuB7B,MAAM;AAAA,kBAC7C+C,UAAA;AAAA,gBAAA,CAED,CAAA;AAAA,cACF,CAAA;AAAA,YACF,CAAA,CAAA;AAAA,UAAA,GArCa/C,OAAOY,QAsCtB,CACD,0CAEAoD,UACC;AAAA,YAAAjB,UAAA/E,kCAAA,IAACqG,WAAA;AAAA,cACCU,SAAS;AAAA,cACTjC,WAAU;AAAA,cACXC,UAAA;AAAA,YAED,CAAA;AAAA,UACF,CAAA;AAAA,QAEJ,CAAA,CAAA;AAAA,MACF,CAAA;AAAA,IACF,CAAA,GAGCJ,aAAa,KACXvE,kCAAAA,KAAA,OAAA;AAAA,MAAI0E,WAAU;AAAA,MACbC,UAAA,CAAC3E,kCAAA,KAAA,OAAA;AAAA,QAAI0E,WAAU;AAAA,QACbC,UAAA,CAAC3E,kCAAA,KAAA,QAAA;AAAA,UAAK0E,WAAU;AAAA,UAAwBC,UAAA,CAAA,YAC5BrE,SAAUC,UAAS,QAAKiE,KAAKoC,KAAKtG,SAAS,KAAKC,UAAUF,UAAU,GAAE,QAAKA,YAAW,QAAA;AAAA,QAClG,CAAA,GACAL,kCAAA,KAAC6E,QAAA;AAAA,UACClB,OAAOpD,SAASqC,SAAS;AAAA,UACzBoC,eAAeX;AAAAA,UAEfM,UAAA,CAAA/E,kCAAA,IAACqF,eAAc;AAAA,YAAAP,WAAU;AAAA,YACvBC,UAAA/E,kCAAAA,IAACuF,cAAY,CAAA;AAAA,UACf,CAAA,0CACCE,eACC;AAAA,YAAAV,UAAA,CAAC/E,kCAAA,IAAA0F,YAAA;AAAA,cAAW3B,OAAM;AAAA,cAAIgB,UAAC;AAAA,YAAA,CAAA,GACtB/E,kCAAA,IAAA0F,YAAA;AAAA,cAAW3B,OAAM;AAAA,cAAKgB,UAAE;AAAA,YAAA,CAAA,GACxB/E,kCAAA,IAAA0F,YAAA;AAAA,cAAW3B,OAAM;AAAA,cAAKgB,UAAE;AAAA,YAAA,CAAA,GACxB/E,kCAAA,IAAA0F,YAAA;AAAA,cAAW3B,OAAM;AAAA,cAAKgB,UAAE;AAAA,YAAA,CAAA,CAAA;AAAA,UAC3B,CAAA,CAAA;AAAA,QAAA,CACF,GACC/E,kCAAA,IAAA,QAAA;AAAA,UAAK8E,WAAU;AAAA,UAAwBC,UAAQ;AAAA,QAAA,CAAA,CAAA;AAAA,MAClD,CAAA,GAEA3E,kCAAA,KAAC,OAAI;AAAA,QAAA0E,WAAU;AAAA,QACbC,UAAA,CAAA/E,kCAAA,IAAC4G,QAAA;AAAA,UACClD,SAAQ;AAAA,UACRmD,MAAK;AAAA,UACLC,SAASA,MAAMvC,iBAAiB,CAAC;AAAA,UACjC0C,UAAUvG,WAAW;AAAA,UACtBqE,UAAA;AAAA,QAAA,CAED,GACA/E,kCAAA,IAAC4G,QAAA;AAAA,UACClD,SAAQ;AAAA,UACRmD,MAAK;AAAA,UACLC,SAASA,MAAMvC,iBAAiB7D,SAAS,CAAC;AAAA,UAC1CuG,UAAUvG,WAAW;AAAA,UACtBqE,UAAA;AAAA,QAAA,CAED,GACA3E,kCAAA,KAAC,QAAK;AAAA,UAAA0E,WAAU;AAAA,UAAiCC,UAAA,CAAA,SACzCrE,SAAS,GAAE,QAAKiE,aAAa,CAAA;AAAA,QACrC,CAAA,GACA3E,kCAAA,IAAC4G,QAAA;AAAA,UACClD,SAAQ;AAAA,UACRmD,MAAK;AAAA,UACLC,SAASA,MAAMvC,iBAAiB7D,SAAS,CAAC;AAAA,UAC1CuG,UAAUvG,UAAUiE;AAAAA,UACrBI,UAAA;AAAA,QAAA,CAED,GACA/E,kCAAA,IAAC4G,QAAA;AAAA,UACClD,SAAQ;AAAA,UACRmD,MAAK;AAAA,UACLC,SAASA,MAAMvC,iBAAiBI,aAAa,CAAC;AAAA,UAC9CsC,UAAUvG,UAAUiE;AAAAA,UACrBI,UAAA;AAAA,QAAA,CAED,CAAA;AAAA,MACF,CAAA,CAAA;AAAA,KACF,GAID3D,kBACCpB,kCAAA,IAACkH,mBAAA;AAAA,MACCC,QAAQ3F;AAAAA,MACR4F,SAASA,MAAM3F,mBAAmB,KAAK;AAAA,MACvCmB,UAAUxB,eAAewB;AAAAA,MACzBlB;AAAAA,KACF,GAIDN,kBACCpB,kCAAA,IAACqH,oBAAA;AAAA,MACCF,QAAQ7F;AAAAA,MACR8F,SAASA,MAAM;AACb7F,4BAAoB,KAAK;AAAA,MAC3B;AAAA,MACAS,QAAQZ;AAAAA,IAAA,CACV,CAAA;AAAA,EAEJ,CAAA;AAEJ;"}