import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";

export default function Integrations() {
  return (
    <div className="p-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-gray-900">Integrations</h1>
        <p className="text-gray-600 mt-2">Connect with third-party services</p>
      </div>
      
      <div className="flex items-center justify-center min-h-[400px]">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <div className="mx-auto mb-4 w-16 h-16 bg-gradient-to-r from-yellow-500 to-orange-600 rounded-full flex items-center justify-center">
              <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
            </div>
            <CardTitle className="text-2xl">Integrations Coming Soon</CardTitle>
            <CardDescription className="text-lg">
              We&apos;re working on seamless integrations with your favorite services.
            </CardDescription>
          </CardHeader>
          <CardContent className="text-center">
            <p className="text-gray-500 mb-4">
              Connect payment gateways, delivery partners, and analytics tools in one place.
            </p>
            <div className="inline-flex items-center px-4 py-2 bg-gradient-to-r from-yellow-500 to-orange-600 text-black rounded-lg">
              <span className="mr-2">🔌</span>
              Launching Soon
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
} 