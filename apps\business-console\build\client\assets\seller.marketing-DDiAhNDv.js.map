{"version": 3, "file": "seller.marketing-DDiAhNDv.js", "sources": ["../../../app/routes/seller.marketing.tsx"], "sourcesContent": ["// import { Ta<PERSON>, Ta<PERSON>List, TabsTrigger } from \"@components/ui/tabs\";\r\nimport { Outlet, useLocation } from \"@remix-run/react\";\r\n\r\nexport default function MarketingLayout() {\r\n  const location = useLocation();\r\n  const activeTab = location.pathname.split(\"/\").pop() || \"templates\";\r\n\r\n  return (\r\n    <div className=\"container mx-auto p-4 space-y-4\">\r\n      <div className=\"flex flex-col space-y-4\">\r\n        <div className=\"flex items-center gap-4\">\r\n          {/* {activeTab === \"templates\" && (\r\n            <Button variant=\"ghost\" size=\"icon\" className=\"md:hidden\">\r\n              <ArrowLeft className=\"h-5 w-5\" />\r\n            </Button>\r\n          )} */}\r\n          <h1 className=\"text-2xl font-bold\">\r\n            {activeTab === \"templates\" ? \"Template Management\" : \"Marketing\"}\r\n          </h1>\r\n        </div>\r\n\r\n        {/* <Tabs value={activeTab} className=\"w-full\">\r\n          <TabsList className=\"grid w-full grid-cols-3 mb-4\">\r\n            <Link to=\"campaigns\">\r\n              <TabsTrigger value=\"campaigns\" className=\"w-full\">\r\n                Campaigns\r\n              </TabsTrigger>\r\n            </Link>\r\n            <Link to=\"templates\">\r\n              <TabsTrigger value=\"templates\" className=\"w-full\">\r\n                Templates\r\n              </TabsTrigger>\r\n            </Link>\r\n            <Link to=\"analytics\">\r\n              <TabsTrigger value=\"analytics\" className=\"w-full\">\r\n                Analytics\r\n              </TabsTrigger>\r\n            </Link>\r\n          </TabsList>\r\n        </Tabs> */}\r\n\r\n        <Outlet />\r\n      </div>\r\n    </div>\r\n  );\r\n} "], "names": ["MarketingLayout", "location", "useLocation", "activeTab", "pathname", "split", "pop", "className", "children", "jsxs", "jsx", "Outlet"], "mappings": ";;AAGA,SAAwBA,kBAAkB;AACxC,QAAMC,WAAWC,YAAY;AAC7B,QAAMC,YAAYF,SAASG,SAASC,MAAM,GAAG,EAAEC,SAAS;AAExD,+CACG,OAAI;AAAA,IAAAC,WAAU;AAAA,IACbC,UAACC,kCAAA,KAAA,OAAA;AAAA,MAAIF,WAAU;AAAA,MACbC,UAAA,CAACE,kCAAA,IAAA,OAAA;AAAA,QAAIH,WAAU;AAAA,QAMbC,UAACE,kCAAA,IAAA,MAAA;AAAA,UAAGH,WAAU;AAAA,UACXC,UAAcL,cAAA,cAAc,wBAAwB;AAAA,QACvD,CAAA;AAAA,OACF,yCAsBCQ,QAAO,EAAA,CAAA;AAAA,IACV,CAAA;AAAA,EACF,CAAA;AAEJ;"}