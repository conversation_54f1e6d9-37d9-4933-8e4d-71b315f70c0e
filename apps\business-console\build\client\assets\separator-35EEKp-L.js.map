{"version": 3, "file": "separator-35EEKp-L.js", "sources": ["../../../node_modules/@radix-ui/react-separator/dist/index.mjs", "../../../app/components/ui/separator.tsx"], "sourcesContent": ["// packages/react/separator/src/separator.tsx\nimport * as React from \"react\";\nimport { Primitive } from \"@radix-ui/react-primitive\";\nimport { jsx } from \"react/jsx-runtime\";\nvar NAME = \"Separator\";\nvar DEFAULT_ORIENTATION = \"horizontal\";\nvar ORIENTATIONS = [\"horizontal\", \"vertical\"];\nvar Separator = React.forwardRef((props, forwardedRef) => {\n  const { decorative, orientation: orientationProp = DEFAULT_ORIENTATION, ...domProps } = props;\n  const orientation = isValidOrientation(orientationProp) ? orientationProp : DEFAULT_ORIENTATION;\n  const ariaOrientation = orientation === \"vertical\" ? orientation : void 0;\n  const semanticProps = decorative ? { role: \"none\" } : { \"aria-orientation\": ariaOrientation, role: \"separator\" };\n  return /* @__PURE__ */ jsx(\n    Primitive.div,\n    {\n      \"data-orientation\": orientation,\n      ...semanticProps,\n      ...domProps,\n      ref: forwardedRef\n    }\n  );\n});\nSeparator.displayName = NAME;\nfunction isValidOrientation(orientation) {\n  return ORIENTATIONS.includes(orientation);\n}\nvar Root = Separator;\nexport {\n  Root,\n  Separator\n};\n//# sourceMappingURL=index.mjs.map\n", "import * as React from \"react\"\r\nimport * as SeparatorPrimitive from \"@radix-ui/react-separator\"\r\n\r\nimport { cn } from \"~/lib/utils\"\r\n\r\nconst Separator = React.forwardRef<\r\n  React.ElementRef<typeof SeparatorPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof SeparatorPrimitive.Root>\r\n>(\r\n  (\r\n    { className, orientation = \"horizontal\", decorative = true, ...props },\r\n    ref\r\n  ) => (\r\n    <SeparatorPrimitive.Root\r\n      ref={ref}\r\n      decorative={decorative}\r\n      orientation={orientation}\r\n      className={cn(\r\n        \"shrink-0 bg-border\",\r\n        orientation === \"horizontal\" ? \"h-[1px] w-full\" : \"h-full w-[1px]\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n)\r\nSeparator.displayName = SeparatorPrimitive.Root.displayName\r\n\r\nexport { Separator }\r\n"], "names": ["Separator", "React.forwardRef", "jsx", "SeparatorPrimitive.Root"], "mappings": ";;;AAIA,IAAI,OAAO;AACX,IAAI,sBAAsB;AAC1B,IAAI,eAAe,CAAC,cAAc,UAAU;AAC5C,IAAIA,cAAYC,aAAgB,WAAC,CAAC,OAAO,iBAAiB;AACxD,QAAM,EAAE,YAAY,aAAa,kBAAkB,qBAAqB,GAAG,SAAQ,IAAK;AACxF,QAAM,cAAc,mBAAmB,eAAe,IAAI,kBAAkB;AAC5E,QAAM,kBAAkB,gBAAgB,aAAa,cAAc;AACnE,QAAM,gBAAgB,aAAa,EAAE,MAAM,WAAW,EAAE,oBAAoB,iBAAiB,MAAM,YAAa;AAChH,SAAuBC,kCAAG;AAAA,IACxB,UAAU;AAAA,IACV;AAAA,MACE,oBAAoB;AAAA,MACpB,GAAG;AAAA,MACH,GAAG;AAAA,MACH,KAAK;AAAA,IACX;AAAA,EACG;AACH,CAAC;AACDF,YAAU,cAAc;AACxB,SAAS,mBAAmB,aAAa;AACvC,SAAO,aAAa,SAAS,WAAW;AAC1C;AACA,IAAI,OAAOA;ACrBX,MAAM,YAAYC,aAAM;AAAA,EAItB,CACE,EAAE,WAAW,cAAc,cAAc,aAAa,MAAM,GAAG,SAC/D,QAEAC,kCAAA;AAAA,IAACC;AAAAA,IAAA;AAAA,MACC;AAAA,MACA;AAAA,MACA;AAAA,MACA,WAAW;AAAA,QACT;AAAA,QACA,gBAAgB,eAAe,mBAAmB;AAAA,QAClD;AAAA,MACF;AAAA,MACC,GAAG;AAAA,IAAA;AAAA,EAAA;AAGV;AACA,UAAU,cAAcA,KAAwB;", "x_google_ignoreList": [0]}