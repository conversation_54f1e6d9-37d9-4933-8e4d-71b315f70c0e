{"version": 3, "file": "checkbox-DoRUSdrQ.js", "sources": ["../../../node_modules/@radix-ui/react-checkbox/dist/index.mjs", "../../../app/components/ui/checkbox.tsx"], "sourcesContent": ["\"use client\";\n\n// packages/react/checkbox/src/checkbox.tsx\nimport * as React from \"react\";\nimport { useComposedRefs } from \"@radix-ui/react-compose-refs\";\nimport { createContextScope } from \"@radix-ui/react-context\";\nimport { composeEventHandlers } from \"@radix-ui/primitive\";\nimport { useControllableState } from \"@radix-ui/react-use-controllable-state\";\nimport { usePrevious } from \"@radix-ui/react-use-previous\";\nimport { useSize } from \"@radix-ui/react-use-size\";\nimport { Presence } from \"@radix-ui/react-presence\";\nimport { Primitive } from \"@radix-ui/react-primitive\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar CHECKBOX_NAME = \"Checkbox\";\nvar [createCheckboxContext, createCheckboxScope] = createContextScope(CHECKBOX_NAME);\nvar [CheckboxProvider, useCheckboxContext] = createCheckboxContext(CHECKBOX_NAME);\nvar Checkbox = React.forwardRef(\n  (props, forwardedRef) => {\n    const {\n      __scopeCheckbox,\n      name,\n      checked: checkedProp,\n      defaultChecked,\n      required,\n      disabled,\n      value = \"on\",\n      onCheckedChange,\n      form,\n      ...checkboxProps\n    } = props;\n    const [button, setButton] = React.useState(null);\n    const composedRefs = useComposedRefs(forwardedRef, (node) => setButton(node));\n    const hasConsumerStoppedPropagationRef = React.useRef(false);\n    const isFormControl = button ? form || !!button.closest(\"form\") : true;\n    const [checked = false, setChecked] = useControllableState({\n      prop: checkedProp,\n      defaultProp: defaultChecked,\n      onChange: onCheckedChange\n    });\n    const initialCheckedStateRef = React.useRef(checked);\n    React.useEffect(() => {\n      const form2 = button?.form;\n      if (form2) {\n        const reset = () => setChecked(initialCheckedStateRef.current);\n        form2.addEventListener(\"reset\", reset);\n        return () => form2.removeEventListener(\"reset\", reset);\n      }\n    }, [button, setChecked]);\n    return /* @__PURE__ */ jsxs(CheckboxProvider, { scope: __scopeCheckbox, state: checked, disabled, children: [\n      /* @__PURE__ */ jsx(\n        Primitive.button,\n        {\n          type: \"button\",\n          role: \"checkbox\",\n          \"aria-checked\": isIndeterminate(checked) ? \"mixed\" : checked,\n          \"aria-required\": required,\n          \"data-state\": getState(checked),\n          \"data-disabled\": disabled ? \"\" : void 0,\n          disabled,\n          value,\n          ...checkboxProps,\n          ref: composedRefs,\n          onKeyDown: composeEventHandlers(props.onKeyDown, (event) => {\n            if (event.key === \"Enter\") event.preventDefault();\n          }),\n          onClick: composeEventHandlers(props.onClick, (event) => {\n            setChecked((prevChecked) => isIndeterminate(prevChecked) ? true : !prevChecked);\n            if (isFormControl) {\n              hasConsumerStoppedPropagationRef.current = event.isPropagationStopped();\n              if (!hasConsumerStoppedPropagationRef.current) event.stopPropagation();\n            }\n          })\n        }\n      ),\n      isFormControl && /* @__PURE__ */ jsx(\n        BubbleInput,\n        {\n          control: button,\n          bubbles: !hasConsumerStoppedPropagationRef.current,\n          name,\n          value,\n          checked,\n          required,\n          disabled,\n          form,\n          style: { transform: \"translateX(-100%)\" },\n          defaultChecked: isIndeterminate(defaultChecked) ? false : defaultChecked\n        }\n      )\n    ] });\n  }\n);\nCheckbox.displayName = CHECKBOX_NAME;\nvar INDICATOR_NAME = \"CheckboxIndicator\";\nvar CheckboxIndicator = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeCheckbox, forceMount, ...indicatorProps } = props;\n    const context = useCheckboxContext(INDICATOR_NAME, __scopeCheckbox);\n    return /* @__PURE__ */ jsx(Presence, { present: forceMount || isIndeterminate(context.state) || context.state === true, children: /* @__PURE__ */ jsx(\n      Primitive.span,\n      {\n        \"data-state\": getState(context.state),\n        \"data-disabled\": context.disabled ? \"\" : void 0,\n        ...indicatorProps,\n        ref: forwardedRef,\n        style: { pointerEvents: \"none\", ...props.style }\n      }\n    ) });\n  }\n);\nCheckboxIndicator.displayName = INDICATOR_NAME;\nvar BubbleInput = (props) => {\n  const { control, checked, bubbles = true, defaultChecked, ...inputProps } = props;\n  const ref = React.useRef(null);\n  const prevChecked = usePrevious(checked);\n  const controlSize = useSize(control);\n  React.useEffect(() => {\n    const input = ref.current;\n    const inputProto = window.HTMLInputElement.prototype;\n    const descriptor = Object.getOwnPropertyDescriptor(inputProto, \"checked\");\n    const setChecked = descriptor.set;\n    if (prevChecked !== checked && setChecked) {\n      const event = new Event(\"click\", { bubbles });\n      input.indeterminate = isIndeterminate(checked);\n      setChecked.call(input, isIndeterminate(checked) ? false : checked);\n      input.dispatchEvent(event);\n    }\n  }, [prevChecked, checked, bubbles]);\n  const defaultCheckedRef = React.useRef(isIndeterminate(checked) ? false : checked);\n  return /* @__PURE__ */ jsx(\n    \"input\",\n    {\n      type: \"checkbox\",\n      \"aria-hidden\": true,\n      defaultChecked: defaultChecked ?? defaultCheckedRef.current,\n      ...inputProps,\n      tabIndex: -1,\n      ref,\n      style: {\n        ...props.style,\n        ...controlSize,\n        position: \"absolute\",\n        pointerEvents: \"none\",\n        opacity: 0,\n        margin: 0\n      }\n    }\n  );\n};\nfunction isIndeterminate(checked) {\n  return checked === \"indeterminate\";\n}\nfunction getState(checked) {\n  return isIndeterminate(checked) ? \"indeterminate\" : checked ? \"checked\" : \"unchecked\";\n}\nvar Root = Checkbox;\nvar Indicator = CheckboxIndicator;\nexport {\n  Checkbox,\n  CheckboxIndicator,\n  Indicator,\n  Root,\n  createCheckboxScope\n};\n//# sourceMappingURL=index.mjs.map\n", "import * as React from \"react\"\r\nimport * as CheckboxPrimitive from \"@radix-ui/react-checkbox\"\r\nimport { Check } from \"lucide-react\"\r\n\r\nimport { cn } from \"~/lib/utils\"\r\n\r\nconst Checkbox = React.forwardRef<\r\n  React.ElementRef<typeof CheckboxPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof CheckboxPrimitive.Root>\r\n>(({ className, ...props }, ref) => (\r\n  <CheckboxPrimitive.Root\r\n    ref={ref}\r\n    className={cn(\r\n      \"peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    <CheckboxPrimitive.Indicator\r\n      className={cn(\"flex items-center justify-center text-current\")}\r\n    >\r\n      <Check className=\"h-4 w-4\" />\r\n    </CheckboxPrimitive.Indicator>\r\n  </CheckboxPrimitive.Root>\r\n))\r\nCheckbox.displayName = CheckboxPrimitive.Root.displayName\r\n\r\nexport { Checkbox }\r\n"], "names": ["Checkbox", "React.forwardRef", "React.useState", "React.useRef", "React.useEffect", "jsxs", "jsx", "CheckboxPrimitive.Root", "CheckboxPrimitive.Indicator"], "mappings": ";;;;;;;;;;AAaA,IAAI,gBAAgB;AACpB,IAAI,CAAC,uBAAuB,mBAAmB,IAAI,mBAAmB,aAAa;AACnF,IAAI,CAAC,kBAAkB,kBAAkB,IAAI,sBAAsB,aAAa;AAChF,IAAIA,aAAWC,aAAgB;AAAA,EAC7B,CAAC,OAAO,iBAAiB;AACvB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA,SAAS;AAAA,MACT;AAAA,MACA;AAAA,MACA;AAAA,MACA,QAAQ;AAAA,MACR;AAAA,MACA;AAAA,MACA,GAAG;AAAA,IACT,IAAQ;AACJ,UAAM,CAAC,QAAQ,SAAS,IAAIC,aAAAA,SAAe,IAAI;AAC/C,UAAM,eAAe,gBAAgB,cAAc,CAAC,SAAS,UAAU,IAAI,CAAC;AAC5E,UAAM,mCAAmCC,aAAY,OAAC,KAAK;AAC3D,UAAM,gBAAgB,SAAS,QAAQ,CAAC,CAAC,OAAO,QAAQ,MAAM,IAAI;AAClE,UAAM,CAAC,UAAU,OAAO,UAAU,IAAI,qBAAqB;AAAA,MACzD,MAAM;AAAA,MACN,aAAa;AAAA,MACb,UAAU;AAAA,IAChB,CAAK;AACD,UAAM,yBAAyBA,aAAY,OAAC,OAAO;AACnDC,iBAAAA,UAAgB,MAAM;AACpB,YAAM,QAAQ,iCAAQ;AACtB,UAAI,OAAO;AACT,cAAM,QAAQ,MAAM,WAAW,uBAAuB,OAAO;AAC7D,cAAM,iBAAiB,SAAS,KAAK;AACrC,eAAO,MAAM,MAAM,oBAAoB,SAAS,KAAK;AAAA,MAC7D;AAAA,IACA,GAAO,CAAC,QAAQ,UAAU,CAAC;AACvB,WAAuBC,kCAAI,KAAC,kBAAkB,EAAE,OAAO,iBAAiB,OAAO,SAAS,UAAU,UAAU;AAAA,MAC1FC,kCAAG;AAAA,QACjB,UAAU;AAAA,QACV;AAAA,UACE,MAAM;AAAA,UACN,MAAM;AAAA,UACN,gBAAgB,gBAAgB,OAAO,IAAI,UAAU;AAAA,UACrD,iBAAiB;AAAA,UACjB,cAAc,SAAS,OAAO;AAAA,UAC9B,iBAAiB,WAAW,KAAK;AAAA,UACjC;AAAA,UACA;AAAA,UACA,GAAG;AAAA,UACH,KAAK;AAAA,UACL,WAAW,qBAAqB,MAAM,WAAW,CAAC,UAAU;AAC1D,gBAAI,MAAM,QAAQ,QAAS,OAAM,eAAgB;AAAA,UAC7D,CAAW;AAAA,UACD,SAAS,qBAAqB,MAAM,SAAS,CAAC,UAAU;AACtD,uBAAW,CAAC,gBAAgB,gBAAgB,WAAW,IAAI,OAAO,CAAC,WAAW;AAC9E,gBAAI,eAAe;AACjB,+CAAiC,UAAU,MAAM,qBAAsB;AACvE,kBAAI,CAAC,iCAAiC,QAAS,OAAM,gBAAiB;AAAA,YACpF;AAAA,UACW,CAAA;AAAA,QACX;AAAA,MACO;AAAA,MACD,iBAAiCA,kCAAG;AAAA,QAClC;AAAA,QACA;AAAA,UACE,SAAS;AAAA,UACT,SAAS,CAAC,iCAAiC;AAAA,UAC3C;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA,OAAO,EAAE,WAAW,oBAAqB;AAAA,UACzC,gBAAgB,gBAAgB,cAAc,IAAI,QAAQ;AAAA,QACpE;AAAA,MACA;AAAA,IACA,GAAO;AAAA,EACP;AACA;AACAN,WAAS,cAAc;AACvB,IAAI,iBAAiB;AACrB,IAAI,oBAAoBC,aAAgB;AAAA,EACtC,CAAC,OAAO,iBAAiB;AACvB,UAAM,EAAE,iBAAiB,YAAY,GAAG,eAAgB,IAAG;AAC3D,UAAM,UAAU,mBAAmB,gBAAgB,eAAe;AAClE,WAAuBK,kCAAG,IAAC,UAAU,EAAE,SAAS,cAAc,gBAAgB,QAAQ,KAAK,KAAK,QAAQ,UAAU,MAAM,UAA0BA,kCAAG;AAAA,MACnJ,UAAU;AAAA,MACV;AAAA,QACE,cAAc,SAAS,QAAQ,KAAK;AAAA,QACpC,iBAAiB,QAAQ,WAAW,KAAK;AAAA,QACzC,GAAG;AAAA,QACH,KAAK;AAAA,QACL,OAAO,EAAE,eAAe,QAAQ,GAAG,MAAM,MAAK;AAAA,MACtD;AAAA,IACA,GAAO;AAAA,EACP;AACA;AACA,kBAAkB,cAAc;AAChC,IAAI,cAAc,CAAC,UAAU;AAC3B,QAAM,EAAE,SAAS,SAAS,UAAU,MAAM,gBAAgB,GAAG,WAAU,IAAK;AAC5E,QAAM,MAAMH,aAAY,OAAC,IAAI;AAC7B,QAAM,cAAc,YAAY,OAAO;AACvC,QAAM,cAAc,QAAQ,OAAO;AACnCC,eAAAA,UAAgB,MAAM;AACpB,UAAM,QAAQ,IAAI;AAClB,UAAM,aAAa,OAAO,iBAAiB;AAC3C,UAAM,aAAa,OAAO,yBAAyB,YAAY,SAAS;AACxE,UAAM,aAAa,WAAW;AAC9B,QAAI,gBAAgB,WAAW,YAAY;AACzC,YAAM,QAAQ,IAAI,MAAM,SAAS,EAAE,QAAO,CAAE;AAC5C,YAAM,gBAAgB,gBAAgB,OAAO;AAC7C,iBAAW,KAAK,OAAO,gBAAgB,OAAO,IAAI,QAAQ,OAAO;AACjE,YAAM,cAAc,KAAK;AAAA,IAC/B;AAAA,EACG,GAAE,CAAC,aAAa,SAAS,OAAO,CAAC;AAClC,QAAM,oBAAoBD,aAAAA,OAAa,gBAAgB,OAAO,IAAI,QAAQ,OAAO;AACjF,SAAuBG,kCAAG;AAAA,IACxB;AAAA,IACA;AAAA,MACE,MAAM;AAAA,MACN,eAAe;AAAA,MACf,gBAAgB,kBAAkB,kBAAkB;AAAA,MACpD,GAAG;AAAA,MACH,UAAU;AAAA,MACV;AAAA,MACA,OAAO;AAAA,QACL,GAAG,MAAM;AAAA,QACT,GAAG;AAAA,QACH,UAAU;AAAA,QACV,eAAe;AAAA,QACf,SAAS;AAAA,QACT,QAAQ;AAAA,MAChB;AAAA,IACA;AAAA,EACG;AACH;AACA,SAAS,gBAAgB,SAAS;AAChC,SAAO,YAAY;AACrB;AACA,SAAS,SAAS,SAAS;AACzB,SAAO,gBAAgB,OAAO,IAAI,kBAAkB,UAAU,YAAY;AAC5E;AACA,IAAI,OAAON;AACX,IAAI,YAAY;ACtJV,MAAA,WAAWC,aAGf,WAAA,CAAC,EAAE,WAAW,GAAG,MAAM,GAAG,QAC1BK,kCAAA;AAAA,EAACC;AAAAA,EAAA;AAAA,IACC;AAAA,IACA,WAAW;AAAA,MACT;AAAA,MACA;AAAA,IACF;AAAA,IACC,GAAG;AAAA,IAEJ,UAAAD,kCAAA;AAAA,MAACE;AAAAA,MAAA;AAAA,QACC,WAAW,GAAG,+CAA+C;AAAA,QAE7D,UAAAF,kCAAAA,IAAC,OAAM,EAAA,WAAU,UAAU,CAAA;AAAA,MAAA;AAAA,IAAA;AAAA,EAC7B;AACF,CACD;AACD,SAAS,cAAcC,KAAuB;", "x_google_ignoreList": [0]}