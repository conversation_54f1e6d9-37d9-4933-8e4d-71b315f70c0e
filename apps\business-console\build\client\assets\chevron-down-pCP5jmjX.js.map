{"version": 3, "file": "chevron-down-pCP5jmjX.js", "sources": ["../../../node_modules/lucide-react/dist/esm/icons/chevron-down.js"], "sourcesContent": ["/**\n * @license lucide-react v0.453.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst ChevronDown = createLucideIcon(\"ChevronDown\", [\n  [\"path\", { d: \"m6 9 6 6 6-6\", key: \"qrunsl\" }]\n]);\n\nexport { ChevronDown as default };\n//# sourceMappingURL=chevron-down.js.map\n"], "names": [], "mappings": ";AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASK,MAAC,cAAc,iBAAiB,eAAe;AAAA,EAClD,CAAC,QAAQ,EAAE,GAAG,gBAAgB,KAAK,SAAU,CAAA;AAC/C,CAAC;", "x_google_ignoreList": [0]}