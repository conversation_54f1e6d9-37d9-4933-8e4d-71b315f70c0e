{"version": 3, "file": "index-z_byfFrQ.js", "sources": ["../../../node_modules/@radix-ui/react-compose-refs/dist/index.mjs", "../../../node_modules/@radix-ui/react-slot/dist/index.mjs"], "sourcesContent": ["// packages/react/compose-refs/src/composeRefs.tsx\nimport * as React from \"react\";\nfunction setRef(ref, value) {\n  if (typeof ref === \"function\") {\n    return ref(value);\n  } else if (ref !== null && ref !== void 0) {\n    ref.current = value;\n  }\n}\nfunction composeRefs(...refs) {\n  return (node) => {\n    let hasCleanup = false;\n    const cleanups = refs.map((ref) => {\n      const cleanup = setRef(ref, node);\n      if (!hasCleanup && typeof cleanup == \"function\") {\n        hasCleanup = true;\n      }\n      return cleanup;\n    });\n    if (hasCleanup) {\n      return () => {\n        for (let i = 0; i < cleanups.length; i++) {\n          const cleanup = cleanups[i];\n          if (typeof cleanup == \"function\") {\n            cleanup();\n          } else {\n            setRef(refs[i], null);\n          }\n        }\n      };\n    }\n  };\n}\nfunction useComposedRefs(...refs) {\n  return React.useCallback(composeRefs(...refs), refs);\n}\nexport {\n  composeRefs,\n  useComposedRefs\n};\n//# sourceMappingURL=index.mjs.map\n", "// packages/react/slot/src/slot.tsx\nimport * as React from \"react\";\nimport { composeRefs } from \"@radix-ui/react-compose-refs\";\nimport { Fragment as Fragment2, jsx } from \"react/jsx-runtime\";\nvar Slot = React.forwardRef((props, forwardedRef) => {\n  const { children, ...slotProps } = props;\n  const childrenArray = React.Children.toArray(children);\n  const slottable = childrenArray.find(isSlottable);\n  if (slottable) {\n    const newElement = slottable.props.children;\n    const newChildren = childrenArray.map((child) => {\n      if (child === slottable) {\n        if (React.Children.count(newElement) > 1) return React.Children.only(null);\n        return React.isValidElement(newElement) ? newElement.props.children : null;\n      } else {\n        return child;\n      }\n    });\n    return /* @__PURE__ */ jsx(SlotClone, { ...slotProps, ref: forwardedRef, children: React.isValidElement(newElement) ? React.cloneElement(newElement, void 0, newChildren) : null });\n  }\n  return /* @__PURE__ */ jsx(SlotClone, { ...slotProps, ref: forwardedRef, children });\n});\nSlot.displayName = \"Slot\";\nvar SlotClone = React.forwardRef((props, forwardedRef) => {\n  const { children, ...slotProps } = props;\n  if (React.isValidElement(children)) {\n    const childrenRef = getElementRef(children);\n    const props2 = mergeProps(slotProps, children.props);\n    if (children.type !== React.Fragment) {\n      props2.ref = forwardedRef ? composeRefs(forwardedRef, childrenRef) : childrenRef;\n    }\n    return React.cloneElement(children, props2);\n  }\n  return React.Children.count(children) > 1 ? React.Children.only(null) : null;\n});\nSlotClone.displayName = \"SlotClone\";\nvar Slottable = ({ children }) => {\n  return /* @__PURE__ */ jsx(Fragment2, { children });\n};\nfunction isSlottable(child) {\n  return React.isValidElement(child) && child.type === Slottable;\n}\nfunction mergeProps(slotProps, childProps) {\n  const overrideProps = { ...childProps };\n  for (const propName in childProps) {\n    const slotPropValue = slotProps[propName];\n    const childPropValue = childProps[propName];\n    const isHandler = /^on[A-Z]/.test(propName);\n    if (isHandler) {\n      if (slotPropValue && childPropValue) {\n        overrideProps[propName] = (...args) => {\n          childPropValue(...args);\n          slotPropValue(...args);\n        };\n      } else if (slotPropValue) {\n        overrideProps[propName] = slotPropValue;\n      }\n    } else if (propName === \"style\") {\n      overrideProps[propName] = { ...slotPropValue, ...childPropValue };\n    } else if (propName === \"className\") {\n      overrideProps[propName] = [slotPropValue, childPropValue].filter(Boolean).join(\" \");\n    }\n  }\n  return { ...slotProps, ...overrideProps };\n}\nfunction getElementRef(element) {\n  let getter = Object.getOwnPropertyDescriptor(element.props, \"ref\")?.get;\n  let mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return element.ref;\n  }\n  getter = Object.getOwnPropertyDescriptor(element, \"ref\")?.get;\n  mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return element.props.ref;\n  }\n  return element.props.ref || element.ref;\n}\nvar Root = Slot;\nexport {\n  Root,\n  Slot,\n  Slottable\n};\n//# sourceMappingURL=index.mjs.map\n"], "names": ["React.useCallback", "React.forwardRef", "React.Children", "React.isValidElement", "jsx", "React.cloneElement", "React.Fragment", "Fragment2"], "mappings": ";AAEA,SAAS,OAAO,KAAK,OAAO;AAC1B,MAAI,OAAO,QAAQ,YAAY;AAC7B,WAAO,IAAI,KAAK;AAAA,EACjB,WAAU,QAAQ,QAAQ,QAAQ,QAAQ;AACzC,QAAI,UAAU;AAAA,EAClB;AACA;AACA,SAAS,eAAe,MAAM;AAC5B,SAAO,CAAC,SAAS;AACf,QAAI,aAAa;AACjB,UAAM,WAAW,KAAK,IAAI,CAAC,QAAQ;AACjC,YAAM,UAAU,OAAO,KAAK,IAAI;AAChC,UAAI,CAAC,cAAc,OAAO,WAAW,YAAY;AAC/C,qBAAa;AAAA,MACrB;AACM,aAAO;AAAA,IACb,CAAK;AACD,QAAI,YAAY;AACd,aAAO,MAAM;AACX,iBAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,gBAAM,UAAU,SAAS,CAAC;AAC1B,cAAI,OAAO,WAAW,YAAY;AAChC,oBAAS;AAAA,UACrB,OAAiB;AACL,mBAAO,KAAK,CAAC,GAAG,IAAI;AAAA,UAChC;AAAA,QACA;AAAA,MACO;AAAA,IACP;AAAA,EACG;AACH;AACA,SAAS,mBAAmB,MAAM;AAChC,SAAOA,aAAiB,YAAC,YAAY,GAAG,IAAI,GAAG,IAAI;AACrD;AC/BG,IAAC,OAAOC,aAAgB,WAAC,CAAC,OAAO,iBAAiB;AACnD,QAAM,EAAE,UAAU,GAAG,UAAS,IAAK;AACnC,QAAM,gBAAgBC,aAAAA,SAAe,QAAQ,QAAQ;AACrD,QAAM,YAAY,cAAc,KAAK,WAAW;AAChD,MAAI,WAAW;AACb,UAAM,aAAa,UAAU,MAAM;AACnC,UAAM,cAAc,cAAc,IAAI,CAAC,UAAU;AAC/C,UAAI,UAAU,WAAW;AACvB,YAAIA,aAAc,SAAC,MAAM,UAAU,IAAI,EAAG,QAAOA,aAAc,SAAC,KAAK,IAAI;AACzE,eAAOC,aAAAA,eAAqB,UAAU,IAAI,WAAW,MAAM,WAAW;AAAA,MAC9E,OAAa;AACL,eAAO;AAAA,MACf;AAAA,IACA,CAAK;AACD,WAAuBC,kCAAAA,IAAI,WAAW,EAAE,GAAG,WAAW,KAAK,cAAc,UAAUD,aAAoB,eAAC,UAAU,IAAIE,aAAAA,aAAmB,YAAY,QAAQ,WAAW,IAAI,MAAM;AAAA,EACtL;AACE,SAAuBD,kCAAAA,IAAI,WAAW,EAAE,GAAG,WAAW,KAAK,cAAc,UAAU;AACrF,CAAC;AACD,KAAK,cAAc;AACnB,IAAI,YAAYH,aAAgB,WAAC,CAAC,OAAO,iBAAiB;AACxD,QAAM,EAAE,UAAU,GAAG,UAAS,IAAK;AACnC,MAAIE,aAAAA,eAAqB,QAAQ,GAAG;AAClC,UAAM,cAAc,cAAc,QAAQ;AAC1C,UAAM,SAAS,WAAW,WAAW,SAAS,KAAK;AACnD,QAAI,SAAS,SAASG,uBAAgB;AACpC,aAAO,MAAM,eAAe,YAAY,cAAc,WAAW,IAAI;AAAA,IAC3E;AACI,WAAOD,aAAkB,aAAC,UAAU,MAAM;AAAA,EAC9C;AACE,SAAOH,aAAc,SAAC,MAAM,QAAQ,IAAI,IAAIA,sBAAe,KAAK,IAAI,IAAI;AAC1E,CAAC;AACD,UAAU,cAAc;AACrB,IAAC,YAAY,CAAC,EAAE,eAAe;AAChC,SAAuBE,sCAAIG,kBAAAA,UAAW,EAAE,UAAU;AACpD;AACA,SAAS,YAAY,OAAO;AAC1B,SAAOJ,aAAoB,eAAC,KAAK,KAAK,MAAM,SAAS;AACvD;AACA,SAAS,WAAW,WAAW,YAAY;AACzC,QAAM,gBAAgB,EAAE,GAAG,WAAY;AACvC,aAAW,YAAY,YAAY;AACjC,UAAM,gBAAgB,UAAU,QAAQ;AACxC,UAAM,iBAAiB,WAAW,QAAQ;AAC1C,UAAM,YAAY,WAAW,KAAK,QAAQ;AAC1C,QAAI,WAAW;AACb,UAAI,iBAAiB,gBAAgB;AACnC,sBAAc,QAAQ,IAAI,IAAI,SAAS;AACrC,yBAAe,GAAG,IAAI;AACtB,wBAAc,GAAG,IAAI;AAAA,QACtB;AAAA,MACF,WAAU,eAAe;AACxB,sBAAc,QAAQ,IAAI;AAAA,MAClC;AAAA,IACA,WAAe,aAAa,SAAS;AAC/B,oBAAc,QAAQ,IAAI,EAAE,GAAG,eAAe,GAAG,eAAgB;AAAA,IACvE,WAAe,aAAa,aAAa;AACnC,oBAAc,QAAQ,IAAI,CAAC,eAAe,cAAc,EAAE,OAAO,OAAO,EAAE,KAAK,GAAG;AAAA,IACxF;AAAA,EACA;AACE,SAAO,EAAE,GAAG,WAAW,GAAG,cAAe;AAC3C;AACA,SAAS,cAAc,SAAS;;AAC9B,MAAI,UAAS,YAAO,yBAAyB,QAAQ,OAAO,KAAK,MAApD,mBAAuD;AACpE,MAAI,UAAU,UAAU,oBAAoB,UAAU,OAAO;AAC7D,MAAI,SAAS;AACX,WAAO,QAAQ;AAAA,EACnB;AACE,YAAS,YAAO,yBAAyB,SAAS,KAAK,MAA9C,mBAAiD;AAC1D,YAAU,UAAU,oBAAoB,UAAU,OAAO;AACzD,MAAI,SAAS;AACX,WAAO,QAAQ,MAAM;AAAA,EACzB;AACE,SAAO,QAAQ,MAAM,OAAO,QAAQ;AACtC;", "x_google_ignoreList": [0, 1]}