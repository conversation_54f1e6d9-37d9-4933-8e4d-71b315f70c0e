import { r as reactExports, j as jsxRuntimeExports } from "./jsx-runtime-bB2y7OuD.js";
import { I as Input } from "./input-3v87qohQ.js";
import "./utils-GkgzjW3c.js";
function FinanceConfig() {
  const [searchTerm, setSearchTerm] = reactExports.useState("");
  return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
    className: "container mx-auto p-6",
    children: [/* @__PURE__ */ jsxRuntimeExports.jsx("div", {
      className: "flex justify-between items-center mb-6",
      children: /* @__PURE__ */ jsxRuntimeExports.jsx("h1", {
        className: "text-2xl font-bold",
        children: "mNet Users"
      })
    }), /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
      className: "flex justify-between mb-4",
      children: /* @__PURE__ */ jsxRuntimeExports.jsx(Input, {
        placeholder: "Search by name or owner",
        value: searchTerm,
        onChange: (e) => setSearchTerm(e.target.value),
        className: "max-w-sm"
      })
    })]
  });
}
export {
  FinanceConfig as default
};
//# sourceMappingURL=home.financeReports-BxPcgJtA.js.map
