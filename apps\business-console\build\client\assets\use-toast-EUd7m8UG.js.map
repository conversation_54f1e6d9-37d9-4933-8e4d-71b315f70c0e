{"version": 3, "file": "use-toast-EUd7m8UG.js", "sources": ["../../../app/hooks/use-toast.ts"], "sourcesContent": ["\"use client\"\r\n\r\n// Inspired by react-hot-toast library\r\nimport * as React from \"react\"\r\n\r\nimport type {\r\n  ToastActionElement,\r\n  ToastProps,\r\n} from \"~/components/ui/toast\"\r\n\r\nconst TOAST_LIMIT = 1\r\nconst TOAST_REMOVE_DELAY = 1000000\r\n\r\ntype ToasterToast = ToastProps & {\r\n  id: string\r\n  title?: React.ReactNode\r\n  description?: React.ReactNode\r\n  action?: ToastActionElement\r\n}\r\n\r\nconst actionTypes = {\r\n  ADD_TOAST: \"ADD_TOAST\",\r\n  UPDATE_TOAST: \"UPDATE_TOAST\",\r\n  DISMISS_TOAST: \"DISMISS_TOAST\",\r\n  REMOVE_TOAST: \"REMOVE_TOAST\",\r\n} as const\r\n\r\nlet count = 0\r\n\r\nfunction genId() {\r\n  count = (count + 1) % Number.MAX_SAFE_INTEGER\r\n  return count.toString()\r\n}\r\n\r\ntype ActionType = typeof actionTypes\r\n\r\ntype Action =\r\n  | {\r\n      type: ActionType[\"ADD_TOAST\"]\r\n      toast: ToasterToast\r\n    }\r\n  | {\r\n      type: ActionType[\"UPDATE_TOAST\"]\r\n      toast: Partial<ToasterToast>\r\n    }\r\n  | {\r\n      type: ActionType[\"DISMISS_TOAST\"]\r\n      toastId?: ToasterToast[\"id\"]\r\n    }\r\n  | {\r\n      type: ActionType[\"REMOVE_TOAST\"]\r\n      toastId?: ToasterToast[\"id\"]\r\n    }\r\n\r\ninterface State {\r\n  toasts: ToasterToast[]\r\n}\r\n\r\nconst toastTimeouts = new Map<string, ReturnType<typeof setTimeout>>()\r\n\r\nconst addToRemoveQueue = (toastId: string) => {\r\n  if (toastTimeouts.has(toastId)) {\r\n    return\r\n  }\r\n\r\n  const timeout = setTimeout(() => {\r\n    toastTimeouts.delete(toastId)\r\n    dispatch({\r\n      type: \"REMOVE_TOAST\",\r\n      toastId: toastId,\r\n    })\r\n  }, TOAST_REMOVE_DELAY)\r\n\r\n  toastTimeouts.set(toastId, timeout)\r\n}\r\n\r\nexport const reducer = (state: State, action: Action): State => {\r\n  switch (action.type) {\r\n    case \"ADD_TOAST\":\r\n      return {\r\n        ...state,\r\n        toasts: [action.toast, ...state.toasts].slice(0, TOAST_LIMIT),\r\n      }\r\n\r\n    case \"UPDATE_TOAST\":\r\n      return {\r\n        ...state,\r\n        toasts: state.toasts.map((t) =>\r\n          t.id === action.toast.id ? { ...t, ...action.toast } : t\r\n        ),\r\n      }\r\n\r\n    case \"DISMISS_TOAST\": {\r\n      const { toastId } = action\r\n\r\n      // ! Side effects ! - This could be extracted into a dismissToast() action,\r\n      // but I'll keep it here for simplicity\r\n      if (toastId) {\r\n        addToRemoveQueue(toastId)\r\n      } else {\r\n        state.toasts.forEach((toast) => {\r\n          addToRemoveQueue(toast.id)\r\n        })\r\n      }\r\n\r\n      return {\r\n        ...state,\r\n        toasts: state.toasts.map((t) =>\r\n          t.id === toastId || toastId === undefined\r\n            ? {\r\n                ...t,\r\n                open: false,\r\n              }\r\n            : t\r\n        ),\r\n      }\r\n    }\r\n    case \"REMOVE_TOAST\":\r\n      if (action.toastId === undefined) {\r\n        return {\r\n          ...state,\r\n          toasts: [],\r\n        }\r\n      }\r\n      return {\r\n        ...state,\r\n        toasts: state.toasts.filter((t) => t.id !== action.toastId),\r\n      }\r\n  }\r\n}\r\n\r\nconst listeners: Array<(state: State) => void> = []\r\n\r\nlet memoryState: State = { toasts: [] }\r\n\r\nfunction dispatch(action: Action) {\r\n  memoryState = reducer(memoryState, action)\r\n  listeners.forEach((listener) => {\r\n    listener(memoryState)\r\n  })\r\n}\r\n\r\ntype Toast = Omit<ToasterToast, \"id\">\r\n\r\nfunction toast({ ...props }: Toast) {\r\n  const id = genId()\r\n\r\n  const update = (props: ToasterToast) =>\r\n    dispatch({\r\n      type: \"UPDATE_TOAST\",\r\n      toast: { ...props, id },\r\n    })\r\n  const dismiss = () => dispatch({ type: \"DISMISS_TOAST\", toastId: id })\r\n\r\n  dispatch({\r\n    type: \"ADD_TOAST\",\r\n    toast: {\r\n      ...props,\r\n      id,\r\n      open: true,\r\n      onOpenChange: (open) => {\r\n        if (!open) dismiss()\r\n      },\r\n    },\r\n  })\r\n\r\n  return {\r\n    id: id,\r\n    dismiss,\r\n    update,\r\n  }\r\n}\r\n\r\nfunction useToast() {\r\n  const [state, setState] = React.useState<State>(memoryState)\r\n\r\n  React.useEffect(() => {\r\n    listeners.push(setState)\r\n    return () => {\r\n      const index = listeners.indexOf(setState)\r\n      if (index > -1) {\r\n        listeners.splice(index, 1)\r\n      }\r\n    }\r\n  }, [state])\r\n\r\n  return {\r\n    ...state,\r\n    toast,\r\n    dismiss: (toastId?: string) => dispatch({ type: \"DISMISS_TOAST\", toastId }),\r\n  }\r\n}\r\n\r\nexport { useToast, toast }\r\n"], "names": ["toast", "props", "React.useState", "React.useEffect"], "mappings": ";AAUA,MAAM,cAAc;AACpB,MAAM,qBAAqB;AAgB3B,IAAI,QAAQ;AAEZ,SAAS,QAAQ;AACN,WAAA,QAAQ,KAAK,OAAO;AAC7B,SAAO,MAAM,SAAS;AACxB;AA0BA,MAAM,oCAAoB,IAA2C;AAErE,MAAM,mBAAmB,CAAC,YAAoB;AACxC,MAAA,cAAc,IAAI,OAAO,GAAG;AAC9B;AAAA,EAAA;AAGI,QAAA,UAAU,WAAW,MAAM;AAC/B,kBAAc,OAAO,OAAO;AACnB,aAAA;AAAA,MACP,MAAM;AAAA,MACN;AAAA,IAAA,CACD;AAAA,KACA,kBAAkB;AAEP,gBAAA,IAAI,SAAS,OAAO;AACpC;AAEa,MAAA,UAAU,CAAC,OAAc,WAA0B;AAC9D,UAAQ,OAAO,MAAM;AAAA,IACnB,KAAK;AACI,aAAA;AAAA,QACL,GAAG;AAAA,QACH,QAAQ,CAAC,OAAO,OAAO,GAAG,MAAM,MAAM,EAAE,MAAM,GAAG,WAAW;AAAA,MAC9D;AAAA,IAEF,KAAK;AACI,aAAA;AAAA,QACL,GAAG;AAAA,QACH,QAAQ,MAAM,OAAO;AAAA,UAAI,CAAC,MACxB,EAAE,OAAO,OAAO,MAAM,KAAK,EAAE,GAAG,GAAG,GAAG,OAAO,UAAU;AAAA,QAAA;AAAA,MAE3D;AAAA,IAEF,KAAK,iBAAiB;AACd,YAAA,EAAE,YAAY;AAIpB,UAAI,SAAS;AACX,yBAAiB,OAAO;AAAA,MAAA,OACnB;AACC,cAAA,OAAO,QAAQ,CAACA,WAAU;AAC9B,2BAAiBA,OAAM,EAAE;AAAA,QAAA,CAC1B;AAAA,MAAA;AAGI,aAAA;AAAA,QACL,GAAG;AAAA,QACH,QAAQ,MAAM,OAAO;AAAA,UAAI,CAAC,MACxB,EAAE,OAAO,WAAW,YAAY,SAC5B;AAAA,YACE,GAAG;AAAA,YACH,MAAM;AAAA,UAAA,IAER;AAAA,QAAA;AAAA,MAER;AAAA,IAAA;AAAA,IAEF,KAAK;AACC,UAAA,OAAO,YAAY,QAAW;AACzB,eAAA;AAAA,UACL,GAAG;AAAA,UACH,QAAQ,CAAA;AAAA,QACV;AAAA,MAAA;AAEK,aAAA;AAAA,QACL,GAAG;AAAA,QACH,QAAQ,MAAM,OAAO,OAAO,CAAC,MAAM,EAAE,OAAO,OAAO,OAAO;AAAA,MAC5D;AAAA,EAAA;AAEN;AAEA,MAAM,YAA2C,CAAC;AAElD,IAAI,cAAqB,EAAE,QAAQ,GAAG;AAEtC,SAAS,SAAS,QAAgB;AAClB,gBAAA,QAAQ,aAAa,MAAM;AAC/B,YAAA,QAAQ,CAAC,aAAa;AAC9B,aAAS,WAAW;AAAA,EAAA,CACrB;AACH;AAIA,SAAS,MAAM,EAAE,GAAG,SAAgB;AAClC,QAAM,KAAK,MAAM;AAEX,QAAA,SAAS,CAACC,WACd,SAAS;AAAA,IACP,MAAM;AAAA,IACN,OAAO,EAAE,GAAGA,QAAO,GAAG;AAAA,EAAA,CACvB;AACG,QAAA,UAAU,MAAM,SAAS,EAAE,MAAM,iBAAiB,SAAS,IAAI;AAE5D,WAAA;AAAA,IACP,MAAM;AAAA,IACN,OAAO;AAAA,MACL,GAAG;AAAA,MACH;AAAA,MACA,MAAM;AAAA,MACN,cAAc,CAAC,SAAS;AAClB,YAAA,CAAC,KAAc,SAAA;AAAA,MAAA;AAAA,IACrB;AAAA,EACF,CACD;AAEM,SAAA;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,WAAW;AAClB,QAAM,CAAC,OAAO,QAAQ,IAAIC,aAAAA,SAAsB,WAAW;AAE3DC,eAAAA,UAAgB,MAAM;AACpB,cAAU,KAAK,QAAQ;AACvB,WAAO,MAAM;AACL,YAAA,QAAQ,UAAU,QAAQ,QAAQ;AACxC,UAAI,QAAQ,IAAI;AACJ,kBAAA,OAAO,OAAO,CAAC;AAAA,MAAA;AAAA,IAE7B;AAAA,EAAA,GACC,CAAC,KAAK,CAAC;AAEH,SAAA;AAAA,IACL,GAAG;AAAA,IACH;AAAA,IACA,SAAS,CAAC,YAAqB,SAAS,EAAE,MAAM,iBAAiB,QAAS,CAAA;AAAA,EAC5E;AACF;"}