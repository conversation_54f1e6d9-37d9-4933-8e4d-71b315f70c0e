{"version": 3, "file": "home.mystock-C02ZrtKb.js", "sources": ["../../../app/routes/home.mystock.tsx"], "sourcesContent": ["import { useEffect, useState } from \"react\";\r\nimport { useToast } from \"~/hooks/use-toast\";\r\nimport { ItemStock } from \"~/types/api/businessConsoleService/ItemStock\";\r\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from \"~/components/ui/select\";\r\nimport { Input } from \"~/components/ui/input\";\r\nimport { Layout } from \"~/root\";\r\nimport { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from \"~/components/ui/table\";\r\nimport { json, Link, useFetcher, useLoaderData } from \"@remix-run/react\";\r\nimport { Button } from \"~/components/ui/button\";\r\nimport { Switch } from \"~/components/ui/switch\";\r\nimport { withAuth, withResponse } from \"~/utils/auth-utils\";\r\nimport { getMyStocks, updateMyStock } from \"~/services/stockservices/mystockservice\";\r\nimport { User } from \"~/types\";\r\n\r\n interface LoaderData{\r\n  stockItems: ItemStock[];\r\n  pageNo:number,\r\n  pageSize:number,\r\n  sellerId:number,\r\n  \r\n }\r\ninterface ActionData{\r\n  error: string | null;\r\n  success: boolean,\r\n  stockData: ItemStock\r\n }\r\nexport const loader = withAuth(async ({ request,user }) => {\r\n  const url = new URL(request.url);\r\n  const SellerId= parseInt(url.searchParams.get(\"sellerId\") as string||\"0\")\r\n   const isSeller = SellerId==null || isNaN(SellerId) || SellerId === 0;\r\n  const pageNo =parseInt(url.searchParams.get(\"page\")||\"0\");\r\n  const pageSize = parseInt(url.searchParams.get(\"pageSize\")||\"100\")\r\n  const matchBy = url.searchParams.get(\"matchBy\") || \"\";\r\n  try {\r\n    const response = await getMyStocks(isSeller,SellerId,pageNo,pageSize,matchBy,request);\r\n    return withResponse({\r\n      stockItems: response.data,\r\n      pageNo,\r\n      pageSize,\r\n      sellerId: SellerId,\r\n    },response?.headers);\r\n  } catch (error) {\r\n    console.error(\"Error in loader:\", error);\r\n    // Return a JSON-based error shape\r\n    throw new Response(\"failed to get sellers\", { status: 500 })\r\n  }\r\n});\r\nexport const action = withAuth(async ({ request }: { request: Request,  }) => {\r\n  const formData = await request.formData();\r\n  const stockId = parseInt(formData.get('stockId') as string);\r\n  const active = formData.has('active') ? formData.get('active') === 'true' : undefined;\r\n  const pricePerUnit = formData.has('pricePerUnit') ? parseFloat(formData.get('pricePerUnit') as string) : undefined;\r\n  const maxAvailableQty = formData.has('maxAvailableQty') ? parseFloat(formData.get('maxAvailableQty') as string) : undefined;\r\n        const sellerId = parseInt(formData.get('sellerId') as string);\r\n        const isSeller = sellerId==null || isNaN(sellerId) || sellerId === 0;\r\n \r\n  // Only include fields that are present\r\n  const requestBody: any = { stockId };\r\n  if (typeof active === 'boolean') requestBody.active = active;\r\n  if (typeof pricePerUnit === 'number' && !isNaN(pricePerUnit)) requestBody.pricePerUnit = pricePerUnit;\r\n  if (typeof maxAvailableQty === 'number' && !isNaN(maxAvailableQty)) requestBody.maxAvailableQty = maxAvailableQty;\r\n\r\n  try {\r\n    const response = await updateMyStock( isSeller, stockId, sellerId, requestBody, request);\r\n    return withResponse({\r\n      stockData: response.data,\r\n      success: true,\r\n      error: null,\r\n      \r\n    }, response?.headers);\r\n  } catch (error) {\r\n    withResponse({\r\n      stockData: null,\r\n      success: false,\r\n      error: \"failed to update stock\",\r\n    }, undefined)\r\n  \r\n       \r\n    console.error(\"Error in action:\", error);\r\n    throw new Response(\"failed to update stock\", { status: 500 });\r\n  }\r\n});\r\n\r\nexport default function MyStock() {\r\n  const { stockItems, pageNo, pageSize,sellerId } = useLoaderData<LoaderData>();\r\n  const [items, setItems] = useState<ItemStock[]>(stockItems)\r\n  const [searchTerm, setSearchTerm] = useState(\"\");\r\n  const [sortBy, setSortBy] = useState(\"itemName\");\r\n  const { toast } = useToast();\r\n//   const actionFetcher= useFetcher<ActionData>()s\r\n useEffect(()=>{\r\n   if (stockItems ){\r\n   setItems(stockItems)\r\n }\r\n },[stockItems])\r\n\r\n  const fetcher = useFetcher<ActionData>();\r\n\r\n  // Re-run the loader after a successful action\r\n  useEffect(() => {\r\n    if (fetcher.state === \"idle\" && fetcher.data?.success && fetcher.data.stockData) {\r\n      setItems((prevItems) =>\r\n        prevItems.map((item) =>\r\n          item.stockId === fetcher.data?.stockData?.stockId ? fetcher.data?.stockData : item\r\n        )\r\n      );\r\n      toast({\r\n        title: \"Stock updated successfully\",\r\n        variant: \"default\",\r\n        description: \"Stock updated successfully\",\r\n      });\r\n      // Use pageNo and pageSize from loader data\r\n    } else if (fetcher.state === \"idle\" && fetcher.data?.error) {\r\n      toast({\r\n        title: \"Something went wrong\",\r\n        variant: \"destructive\",\r\n        description: fetcher.data?.error,\r\n      });\r\n    }\r\n  }, [fetcher.state, fetcher.data,  ]);\r\n \r\n\r\n  const filteredItems = items.filter(item => \r\n    item.itemName.toLowerCase().includes(searchTerm.toLowerCase()) ||\r\n    item.distributor.toLowerCase().includes(searchTerm.toLowerCase())\r\n  );\r\n  \r\n  const sortedItems = [...filteredItems].sort((a, b) => {\r\n    switch (sortBy) {\r\n      case \"itemName\":\r\n        return a.itemName.localeCompare(b.itemName);\r\n      case \"distributerName\":\r\n        return a.distributor.localeCompare(b.distributor);\r\n      case \"myPrice\":\r\n        return b.pricePerUnit - a.pricePerUnit;\r\n      case \"newOrders\":\r\n        return b.maxAvailableQty - a.maxAvailableQty;\r\n      case \"active\":\r\n        return a.active === b.active ? 0 : a.active ? -1 : 1;\r\n      default:\r\n        return 0;\r\n    }\r\n  });\r\n  \r\n  // const handlePriceEdit = (id: string, newPrice: number) => {\r\n  //   if (isNaN(newPrice) || newPrice <= 0) {\r\n  //     toast({\r\n  //       title: \"Invalid Price\",\r\n  //       description: \"Please enter a valid price amount\",\r\n  //       variant: \"destructive\"\r\n  //     });\r\n  //     return;\r\n  //   }\r\n    \r\n  //   updateItem(id, { pricePerUnit: newPrice });\r\n  // };\r\n  return (\r\n    <Layout>\r\n      <div className=\"mb-8\">\r\n        <h1 className=\"text-3xl font-bold tracking-tight\">My Stock</h1>\r\n        <p className=\"text-muted-foreground mt-2\">\r\n          Manage your inventory items and track stock levels\r\n        </p>\r\n      </div>\r\n      \r\n      <div className=\"flex flex-col md:flex-row gap-4 mb-6\">\r\n        <div className=\"flex-1\">\r\n          <Input\r\n            placeholder=\"Search by item name or distributer...\"\r\n            value={searchTerm}\r\n            onChange={(e) => setSearchTerm(e.target.value)}\r\n            className=\"max-w-md\"\r\n          />\r\n        </div>\r\n        <div className=\"w-full md:w-48\">\r\n          <Select value={sortBy} onValueChange={setSortBy}>\r\n            <SelectTrigger>\r\n              <SelectValue placeholder=\"Sort by\" />\r\n            </SelectTrigger>\r\n            <SelectContent>\r\n              <SelectItem value=\"itemName\">Item Name</SelectItem>\r\n              <SelectItem value=\"distributerName\">Distributer</SelectItem>\r\n              <SelectItem value=\"inStock\">Stock Level</SelectItem>\r\n              <SelectItem value=\"myPrice\">Price</SelectItem>\r\n              <SelectItem value=\"newOrders\">New Orders</SelectItem>\r\n              <SelectItem value=\"active\">Active Status</SelectItem>\r\n            </SelectContent>\r\n          </Select>\r\n        </div>\r\n      </div>\r\n      \r\n      <div className=\"rounded-md border\">\r\n        <Table>\r\n          <TableHeader>\r\n            <TableRow>\r\n              <TableHead>Item Name</TableHead>\r\n              <TableHead>Distributer</TableHead>\r\n              <TableHead>Avail QTY</TableHead>\r\n              <TableHead>My Price</TableHead>\r\n              <TableHead>Active</TableHead>\r\n            </TableRow>\r\n          </TableHeader>\r\n          <TableBody>\r\n            {sortedItems.length > 0 ? (\r\n              sortedItems.map((item) => (\r\n                <TableRow key={item.stockId}>\r\n                  <TableCell>\r\n                    <Link \r\n                     to={`/home/<USER>/${item.stockId}/transactions?sellerId=${sellerId}`} \r\n                      className=\"font-medium text-primary hover:underline\"\r\n                    >\r\n                      {item.itemName}\r\n                    </Link>\r\n                  </TableCell>\r\n                  <TableCell>{item.distributor}</TableCell>\r\n                  <TableCell>\r\n                    {item.maxAvailableQty > 0 ? (\r\n                     <div className=\"flex items-center gap-2\">\r\n                     <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800\">\r\n                       {item.maxAvailableQty}\r\n                     </span>\r\n                     <Button \r\n                       variant=\"ghost\" \r\n                       size=\"sm\" \r\n                       onClick={() => {\r\n                         const input = prompt(\"Enter new Qty:\", item.maxAvailableQty.toString());\r\n                   \r\n                         // ✅ Don't continue if user pressed Cancel\r\n                         if (input === null) return;\r\n                   \r\n                         const newQty = parseFloat(input);\r\n                   \r\n                         // ✅ Don't submit if input is not a valid number\r\n                         if (isNaN(newQty)) return;\r\n                   \r\n                         const formData = new FormData();\r\n                         formData.append(\"stockId\", item.stockId.toString());\r\n                         formData.append(\"maxAvailableQty\", newQty.toString());\r\n                         if (sellerId !== undefined) {\r\n                           formData.append(\"sellerId\", sellerId.toString());\r\n                         }\r\n                         fetcher.submit(\r\n                           formData,\r\n                           { method: 'POST' }\r\n                         );\r\n                       }}\r\n                       className=\"h-7 px-2 text-xs\"\r\n                     >\r\n                       Edit\r\n                     </Button>\r\n                   </div>\r\n                    ) : (\r\n                      \"-\"\r\n                    )}\r\n                  </TableCell>\r\n                  <TableCell>\r\n                    <div className=\"flex items-center gap-2\">\r\n                      <span>₹ {item.pricePerUnit.toFixed(2)}</span>\r\n                      <Button \r\n                        variant=\"ghost\" \r\n                        size=\"sm\" \r\n                        onClick={() => {\r\n                          const input = prompt(\"Enter new price:\", item.pricePerUnit.toString());\r\n                          const formData = new FormData();\r\n\r\n                         \r\n\r\n                          // Cancelled prompt\r\n                          if (input === null) return;\r\n                        \r\n                          const newPrice = parseFloat(input);\r\n                          if (sellerId !== undefined) {\r\n                            formData.append(\"sellerId\", sellerId.toString());\r\n                          }\r\n                          formData.append(\"stockId\", item.stockId.toString());\r\n                          formData.append(\"pricePerUnit\", newPrice.toString());\r\n\r\n                          // Invalid input (e.g. empty or not a number)\r\n                          if (isNaN(newPrice)) return;   \r\n\r\n                          fetcher.submit(\r\n                            formData,\r\n                            { method: 'POST' }\r\n                          );\r\n                    \r\n                        }}\r\n                        className=\"h-7 px-2 text-xs\"\r\n                      >\r\n                        Edit\r\n                      </Button>\r\n                    </div>\r\n                  </TableCell>\r\n                  <TableCell>\r\n                    <Switch\r\n                      checked={item.active}\r\n                      onCheckedChange={(checked) => {\r\n                        const formData = new FormData();\r\n                         if (sellerId !== undefined) {\r\n                            formData.append(\"sellerId\", sellerId.toString());\r\n                          }\r\n                          formData.append(\"stockId\", item.stockId.toString());\r\n                          formData.append(\"active\", checked.toString());\r\n                        \r\n                          fetcher.submit(\r\n                            formData,\r\n                            { method: 'POST' }\r\n                          );                      \r\n                      \r\n                      }\r\n                      }\r\n                    />\r\n                  </TableCell>\r\n                </TableRow>\r\n              ))\r\n            ) : (\r\n              <TableRow>\r\n                <TableCell colSpan={6} className=\"text-center py-6 text-muted-foreground\">\r\n                  No items found. Try adjusting your search criteria.\r\n                </TableCell>\r\n              </TableRow>\r\n            )}\r\n          </TableBody>\r\n        </Table>\r\n      </div>\r\n    </Layout>\r\n  );\r\n}"], "names": ["MyStock", "stockItems", "pageNo", "pageSize", "sellerId", "useLoaderData", "items", "setItems", "useState", "searchTerm", "setSearchTerm", "sortBy", "setSortBy", "toast", "useToast", "useEffect", "fetcher", "useFetcher", "state", "data", "success", "stockData", "prevItems", "map", "item", "stockId", "title", "variant", "description", "error", "filteredItems", "filter", "itemName", "toLowerCase", "includes", "distributor", "sortedItems", "sort", "a", "b", "localeCompare", "pricePerUnit", "maxAvailableQty", "active", "Layout", "children", "jsxs", "className", "jsx", "Input", "placeholder", "value", "onChange", "e", "target", "Select", "onValueChange", "SelectTrigger", "SelectValue", "SelectContent", "SelectItem", "Table", "TableHeader", "TableRow", "TableHead", "TableBody", "length", "TableCell", "Link", "to", "<PERSON><PERSON>", "size", "onClick", "input", "prompt", "toString", "newQty", "parseFloat", "isNaN", "formData", "FormData", "append", "submit", "method", "toFixed", "newPrice", "Switch", "checked", "onCheckedChange", "colSpan"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmFA,SAAwBA,UAAU;AAChC,QAAM;AAAA,IAAEC;AAAAA,IAAYC;AAAAA,IAAQC;AAAAA,IAASC;AAAAA,MAAaC,cAA0B;AAC5E,QAAM,CAACC,OAAOC,QAAQ,IAAIC,aAAAA,SAAsBP,UAAU;AAC1D,QAAM,CAACQ,YAAYC,aAAa,IAAIF,aAAAA,SAAS,EAAE;AAC/C,QAAM,CAACG,QAAQC,SAAS,IAAIJ,aAAAA,SAAS,UAAU;AACzC,QAAA;AAAA,IAAEK;AAAAA,EAAM,IAAIC,SAAS;AAE5BC,eAAAA,UAAU,MAAI;AACZ,QAAId,YAAY;AAChBM,eAASN,UAAU;AAAA,IACrB;AAAA,EACA,GAAE,CAACA,UAAU,CAAC;AAEb,QAAMe,UAAUC,WAAuB;AAGvCF,eAAAA,UAAU,MAAM;;AACV,QAAAC,QAAQE,UAAU,YAAUF,aAAQG,SAARH,mBAAcI,YAAWJ,QAAQG,KAAKE,WAAW;AAC/Ed,eAAUe,eACRA,UAAUC,IAAKC,UACb;;AAAAA,oBAAKC,cAAYT,OAAAA,MAAAA,QAAQG,SAARH,gBAAAA,IAAcK,cAAdL,gBAAAA,IAAyBS,YAAUT,MAAAA,QAAQG,SAARH,gBAAAA,IAAcK,YAAYG;AAAAA,OAChF,CACF;AACMX,YAAA;AAAA,QACJa,OAAO;AAAA,QACPC,SAAS;AAAA,QACTC,aAAa;AAAA,MACf,CAAC;AAAA,IAEH,WAAWZ,QAAQE,UAAU,YAAUF,aAAQG,SAARH,mBAAca,QAAO;AACpDhB,YAAA;AAAA,QACJa,OAAO;AAAA,QACPC,SAAS;AAAA,QACTC,cAAaZ,aAAQG,SAARH,mBAAca;AAAAA,MAC7B,CAAC;AAAA,IACH;AAAA,KACC,CAACb,QAAQE,OAAOF,QAAQG,IAAO,CAAC;AAGnC,QAAMW,gBAAgBxB,MAAMyB,iBAC1BP,KAAKQ,SAASC,YAAY,EAAEC,SAASzB,WAAWwB,aAAa,KAC7DT,KAAKW,YAAYF,cAAcC,SAASzB,WAAWwB,aAAa,CAClE;AAEM,QAAAG,cAAc,CAAC,GAAGN,aAAa,EAAEO,KAAK,CAACC,GAAGC,MAAM;AACpD,YAAQ5B,QAAQ;AAAA,MACd,KAAK;AACH,eAAO2B,EAAEN,SAASQ,cAAcD,EAAEP,QAAQ;AAAA,MAC5C,KAAK;AACH,eAAOM,EAAEH,YAAYK,cAAcD,EAAEJ,WAAW;AAAA,MAClD,KAAK;AACI,eAAAI,EAAEE,eAAeH,EAAEG;AAAAA,MAC5B,KAAK;AACI,eAAAF,EAAEG,kBAAkBJ,EAAEI;AAAAA,MAC/B,KAAK;AACH,eAAOJ,EAAEK,WAAWJ,EAAEI,SAAS,IAAIL,EAAEK,SAAS,KAAK;AAAA,MACrD;AACS,eAAA;AAAA,IACX;AAAA,EACF,CAAC;AAcD,gDACGC,QACC;AAAA,IAAAC,UAAA,CAACC,kCAAA,KAAA,OAAA;AAAA,MAAIC,WAAU;AAAA,MACbF,UAAA,CAACG,kCAAA,IAAA,MAAA;AAAA,QAAGD,WAAU;AAAA,QAAoCF,UAAQ;AAAA,MAAA,CAAA,GACzDG,kCAAA,IAAA,KAAA;AAAA,QAAED,WAAU;AAAA,QAA6BF,UAE1C;AAAA,MAAA,CAAA,CAAA;AAAA,IACF,CAAA,GAEAC,kCAAA,KAAC,OAAI;AAAA,MAAAC,WAAU;AAAA,MACbF,UAAA,CAACG,kCAAA,IAAA,OAAA;AAAA,QAAID,WAAU;AAAA,QACbF,UAAAG,kCAAA,IAACC,OAAA;AAAA,UACCC,aAAY;AAAA,UACZC,OAAO1C;AAAAA,UACP2C,UAAWC,OAAM3C,cAAc2C,EAAEC,OAAOH,KAAK;AAAA,UAC7CJ,WAAU;AAAA,QACZ,CAAA;AAAA,MACF,CAAA,GACAC,kCAAA,IAAC;QAAID,WAAU;AAAA,QACbF,iDAACU,QAAO;AAAA,UAAAJ,OAAOxC;AAAAA,UAAQ6C,eAAe5C;AAAAA,UACpCiC,UAAA,CAAAG,kCAAA,IAACS,eACC;AAAA,YAAAZ,UAAAG,kCAAA,IAACU,aAAY;AAAA,cAAAR,aAAY;AAAA,YAAU,CAAA;AAAA,UACrC,CAAA,0CACCS,eACC;AAAA,YAAAd,UAAA,CAACG,kCAAA,IAAAY,YAAA;AAAA,cAAWT,OAAM;AAAA,cAAWN,UAAS;AAAA,YAAA,CAAA,GACrCG,kCAAA,IAAAY,YAAA;AAAA,cAAWT,OAAM;AAAA,cAAkBN,UAAW;AAAA,YAAA,CAAA,GAC9CG,kCAAA,IAAAY,YAAA;AAAA,cAAWT,OAAM;AAAA,cAAUN,UAAW;AAAA,YAAA,CAAA,GACtCG,kCAAA,IAAAY,YAAA;AAAA,cAAWT,OAAM;AAAA,cAAUN,UAAK;AAAA,YAAA,CAAA,GAChCG,kCAAA,IAAAY,YAAA;AAAA,cAAWT,OAAM;AAAA,cAAYN,UAAU;AAAA,YAAA,CAAA,GACvCG,kCAAA,IAAAY,YAAA;AAAA,cAAWT,OAAM;AAAA,cAASN,UAAa;AAAA,YAAA,CAAA,CAAA;AAAA,UAC1C,CAAA,CAAA;AAAA,QACF,CAAA;AAAA,MACF,CAAA,CAAA;AAAA,IACF,CAAA,GAECG,kCAAA,IAAA,OAAA;AAAA,MAAID,WAAU;AAAA,MACbF,iDAACgB,OACC;AAAA,QAAAhB,UAAA,CAACG,kCAAA,IAAAc,aAAA;AAAA,UACCjB,iDAACkB,UACC;AAAA,YAAAlB,UAAA,CAAAG,kCAAA,IAACgB;cAAUnB,UAAS;AAAA,YAAA,CAAA,GACpBG,kCAAA,IAACgB;cAAUnB,UAAW;AAAA,YAAA,CAAA,GACtBG,kCAAA,IAACgB;cAAUnB,UAAS;AAAA,YAAA,CAAA,GACpBG,kCAAA,IAACgB;cAAUnB,UAAQ;AAAA,YAAA,CAAA,GACnBG,kCAAA,IAACgB;cAAUnB,UAAM;AAAA,YAAA,CAAA,CAAA;AAAA,UACnB,CAAA;AAAA,QACF,CAAA,GACAG,kCAAA,IAACiB,WACE;AAAA,UAAApB,UAAAT,YAAY8B,SAAS,IACpB9B,YAAYb,IAAKC,UACfsB,kCAAA,KAACiB,UACC;AAAA,YAAAlB,UAAA,CAAAG,kCAAA,IAACmB,WACC;AAAA,cAAAtB,UAAAG,kCAAA,IAACoB,MAAA;AAAA,gBACAC,IAAI,yBAAyB7C,KAAKC,OAAO,0BAA0BrB,QAAQ;AAAA,gBAC1E2C,WAAU;AAAA,gBAETF,UAAKrB,KAAAQ;AAAAA,cACR,CAAA;AAAA,YACF,CAAA,GACAgB,kCAAA,IAACmB,WAAW;AAAA,cAAAtB,UAAArB,KAAKW;AAAAA,YAAY,CAAA,GAC7Ba,kCAAA,IAACmB;cACEtB,UAAKrB,KAAAkB,kBAAkB,IACtBI,kCAAAA,KAAA,OAAA;AAAA,gBAAIC,WAAU;AAAA,gBACfF,UAAA,CAAAG,kCAAA,IAAC,QAAK;AAAA,kBAAAD,WAAU;AAAA,kBACbF,UAAArB,KAAKkB;AAAAA,gBACR,CAAA,GACAM,kCAAA,IAACsB,QAAA;AAAA,kBACC3C,SAAQ;AAAA,kBACR4C,MAAK;AAAA,kBACLC,SAASA,MAAM;AACb,0BAAMC,QAAQC,OAAO,kBAAkBlD,KAAKkB,gBAAgBiC,UAAU;AAGtE,wBAAIF,UAAU,KAAM;AAEd,0BAAAG,SAASC,WAAWJ,KAAK;AAG3B,wBAAAK,MAAMF,MAAM,EAAG;AAEb,0BAAAG,WAAW,IAAIC,SAAS;AAC9BD,6BAASE,OAAO,WAAWzD,KAAKC,QAAQkD,UAAU;AAClDI,6BAASE,OAAO,mBAAmBL,OAAOD,SAAA,CAAU;AACpD,wBAAIvE,aAAa,QAAW;AAC1B2E,+BAASE,OAAO,YAAY7E,SAASuE,SAAA,CAAU;AAAA,oBACjD;AACQ3D,4BAAAkE,OACNH,UACA;AAAA,sBAAEI,QAAQ;AAAA,oBAAO,CACnB;AAAA,kBACF;AAAA,kBACApC,WAAU;AAAA,kBACXF,UAAA;AAAA,gBAAA,CAED,CAAA;AAAA,cAAA,CACF,IAEG;AAAA,YAEJ,CAAA,GACCG,kCAAA,IAAAmB,WAAA;AAAA,cACCtB,UAACC,kCAAA,KAAA,OAAA;AAAA,gBAAIC,WAAU;AAAA,gBACbF,UAAA,CAAAC,kCAAA,KAAC,QAAK;AAAA,kBAAAD,UAAA,CAAA,MAAGrB,KAAKiB,aAAa2C,QAAQ,CAAC,CAAA;AAAA,gBAAE,CAAA,GACtCpC,kCAAA,IAACsB,QAAA;AAAA,kBACC3C,SAAQ;AAAA,kBACR4C,MAAK;AAAA,kBACLC,SAASA,MAAM;AACb,0BAAMC,QAAQC,OAAO,oBAAoBlD,KAAKiB,aAAakC,UAAU;AAC/D,0BAAAI,WAAW,IAAIC,SAAS;AAK9B,wBAAIP,UAAU,KAAM;AAEd,0BAAAY,WAAWR,WAAWJ,KAAK;AACjC,wBAAIrE,aAAa,QAAW;AAC1B2E,+BAASE,OAAO,YAAY7E,SAASuE,SAAA,CAAU;AAAA,oBACjD;AACAI,6BAASE,OAAO,WAAWzD,KAAKC,QAAQkD,UAAU;AAClDI,6BAASE,OAAO,gBAAgBI,SAASV,SAAA,CAAU;AAG/C,wBAAAG,MAAMO,QAAQ,EAAG;AAEbrE,4BAAAkE,OACNH,UACA;AAAA,sBAAEI,QAAQ;AAAA,oBAAO,CACnB;AAAA,kBAEF;AAAA,kBACApC,WAAU;AAAA,kBACXF,UAAA;AAAA,gBAAA,CAED,CAAA;AAAA,cACF,CAAA;AAAA,YACF,CAAA,yCACCsB,WACC;AAAA,cAAAtB,UAAAG,kCAAA,IAACsC,QAAA;AAAA,gBACCC,SAAS/D,KAAKmB;AAAAA,gBACd6C,iBAAkBD,aAAY;AACtB,wBAAAR,WAAW,IAAIC,SAAS;AAC7B,sBAAI5E,aAAa,QAAW;AACzB2E,6BAASE,OAAO,YAAY7E,SAASuE,SAAA,CAAU;AAAA,kBACjD;AACAI,2BAASE,OAAO,WAAWzD,KAAKC,QAAQkD,UAAU;AAClDI,2BAASE,OAAO,UAAUM,QAAQZ,SAAA,CAAU;AAEpC3D,0BAAAkE,OACNH,UACA;AAAA,oBAAEI,QAAQ;AAAA,kBAAO,CACnB;AAAA,gBAEJ;AAAA,cAEF,CAAA;AAAA,YACF,CAAA,CAAA;AAAA,UAAA,GA1Ga3D,KAAKC,OA2GpB,CACD,0CAEAsC,UACC;AAAA,YAAAlB,UAAAG,kCAAA,IAACmB,WAAU;AAAA,cAAAsB,SAAS;AAAA,cAAG1C,WAAU;AAAA,cAAyCF,UAAA;AAAA,YAE1E,CAAA;AAAA,UACF,CAAA;AAAA,QAEJ,CAAA,CAAA;AAAA,MACF,CAAA;AAAA,IACF,CAAA,CAAA;AAAA,EACF,CAAA;AAEJ;"}