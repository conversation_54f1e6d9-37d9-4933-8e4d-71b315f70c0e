{"version": 3, "file": "home.whatsapp-MRy1eJ8v.js", "sources": ["../../../node_modules/lucide-react/dist/esm/icons/external-link.js", "../../../app/routes/home.whatsapp.tsx"], "sourcesContent": ["/**\n * @license lucide-react v0.453.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst ExternalLink = createLucideIcon(\"ExternalLink\", [\n  [\"path\", { d: \"M15 3h6v6\", key: \"1q9fwt\" }],\n  [\"path\", { d: \"M10 14 21 3\", key: \"gplh6r\" }],\n  [\"path\", { d: \"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6\", key: \"a6xqqp\" }]\n]);\n\nexport { ExternalLink as default };\n//# sourceMappingURL=external-link.js.map\n", "import { Button } from \"@components/ui/button\"\r\nimport {\r\n    Table,\r\n    TableBody,\r\n    TableCell,\r\n    TableHead,\r\n    TableHeader,\r\n    TableRow,\r\n} from \"@components/ui/table\"\r\nimport { ExternalLink } from \"lucide-react\"\r\nimport {Link} from \"@remix-run/react\";\r\n\r\ntype Template = {\r\n    name: string\r\n    category: string\r\n    language: string\r\n    content: string\r\n    status: string\r\n    messagesDelivered: number\r\n    messageReadRate: string\r\n    topBlockReason: string\r\n    lastEdited: string\r\n}\r\n\r\nconst templates: Template[] = [\r\n    {\r\n        name: \"complete_order_cancellation\",\r\n        category: \"Utility\",\r\n        language: \"English\",\r\n        content: \"Dear {{1}}, We're sorry, but your order #{{2}} couldn't be fulfilled due to stock unavailability.❌ We apologize for the inconvenience and hope to serve you again. 🙏 Best, farmersMandi\",\r\n        status: \"Active – Quality pending\",\r\n        messagesDelivered: 0,\r\n        messageReadRate: \"1\",\r\n        topBlockReason: \"––\",\r\n        lastEdited: \"4 Sep 2024\",\r\n    },\r\n    {\r\n        name: \"out_for_delivery\",\r\n        category: \"Utility\",\r\n        language: \"English\",\r\n        content: \"Good morning {{1}}! 🌅 Your order #{{2}} is on the way and will be delivered soon. 🚚 Total payable on delivery: *₹{{3}}* Thanks for choosing us! 🙏 Best, farmersMandi\",\r\n        status: \"Active – Quality pending\",\r\n        messagesDelivered: 0,\r\n        messageReadRate: \"2\",\r\n        topBlockReason: \"––\",\r\n        lastEdited: \"4 Sep 2024\",\r\n    },\r\n    {\r\n        name: \"delivery_confirmation_with_credit\",\r\n        category: \"Utility\",\r\n        language: \"English\",\r\n        content: \"Hi {{1}} 👋, Your order #{{2}} has been delivered. ✅ Order Amount : ₹{{3}} Total Amount due: *₹{{4}}*. 💰 Please pay soon. 💳 Best, farmersMandi\",\r\n        status: \"Active – Quality pending\",\r\n        messagesDelivered: 0,\r\n        messageReadRate: \"0\",\r\n        topBlockReason: \"––\",\r\n        lastEdited: \"4 Sep 2024\",\r\n    },\r\n    {\r\n        name: \"delivery_completed\",\r\n        category: \"Utility\",\r\n        language: \"English\",\r\n        content: \"Hi {{1}} 👋, Your order *#{{2}}* has been delivered. ✅ We hope you're happy with the fresh produce! 🥗 Total amount paid: *₹{{3}}*. 💰 Thanks for choosing us! 🙏 Best, farmersMandi\",\r\n        status: \"Active – Quality pending\",\r\n        messagesDelivered: 0,\r\n        messageReadRate: \"2\",\r\n        topBlockReason: \"––\",\r\n        lastEdited: \"4 Sep 2024\",\r\n    },\r\n    {\r\n        name: \"f_business_order_confirm\",\r\n        category: \"Utility\",\r\n        language: \"English\",\r\n        content: \"Hello {{1}} 👋, Thank you for your order! 🛒 We've received your request for the following items: {{2}} Your order *#{{3}}* will be delivered tomorrow morning. 🚚 Total payable: ₹{{4}}. 💰 Thanks for choosing farmersMandi ! 🙏 Best, farmersMandi\",\r\n        status: \"Active – Quality pending\",\r\n        messagesDelivered: 0,\r\n        messageReadRate: \"1\",\r\n        topBlockReason: \"––\",\r\n        lastEdited: \"4 Sep 2024\",\r\n    },\r\n    {\r\n        name: \"hello_world\",\r\n        category: \"Utility\",\r\n        language: \"English (US)\",\r\n        content: \"Welcome and congratulations!! This message demonstrates your ability to send a WhatsApp message notification from the Cloud API, hosted by Meta. Thank you for taking the time to test with us.\",\r\n        status: \"Active – Quality pending\",\r\n        messagesDelivered: 1,\r\n        messageReadRate: \"0% (0)\",\r\n        topBlockReason: \"––\",\r\n        lastEdited: \"31 Aug 2024\",\r\n    },\r\n]\r\n\r\nexport default function WhatsAppTemplates() {\r\n    return (\r\n        <div className=\"container mx-auto py-10\">\r\n            <div className=\"flex justify-between items-center mb-6\">\r\n                <h1 className=\"text-2xl font-bold\">WhatsApp Templates</h1>\r\n                <Button asChild>\r\n                    <Link to=\"https://business.facebook.com/latest/whatsapp_manager/message_templates\" target=\"_blank\" rel=\"noopener noreferrer\">\r\n                        <ExternalLink className=\"mr-2 h-4 w-4\" /> Templates Manager\r\n                    </Link>\r\n                </Button>\r\n            </div>\r\n            <div className=\"rounded-md border overflow-x-auto\">\r\n                <Table>\r\n                    <TableHeader>\r\n                        <TableRow>\r\n                            <TableHead>Template Name</TableHead>\r\n                            <TableHead>Category</TableHead>\r\n                            <TableHead>Language</TableHead>\r\n                            <TableHead>Status</TableHead>\r\n                            <TableHead>Messages Delivered</TableHead>\r\n                            <TableHead>Message Read Rate</TableHead>\r\n                            <TableHead>Top Block Reason</TableHead>\r\n                            <TableHead>Last Edited</TableHead>\r\n                        </TableRow>\r\n                    </TableHeader>\r\n                    <TableBody>\r\n                        {templates.map((template) => (\r\n                            <TableRow key={template.name}>\r\n                                <TableCell className=\"font-medium\">{template.name}</TableCell>\r\n                                <TableCell>{template.category}</TableCell>\r\n                                <TableCell>{template.language}</TableCell>\r\n                                <TableCell>\r\n                  <span\r\n                      className=\"px-2 py-1 rounded-full text-xs font-semibold bg-yellow-100 text-yellow-800\"\r\n                  >\r\n                    {template.status}\r\n                  </span>\r\n                                </TableCell>\r\n                                <TableCell>{template.messagesDelivered}</TableCell>\r\n                                <TableCell>{template.messageReadRate}</TableCell>\r\n                                <TableCell>{template.topBlockReason}</TableCell>\r\n                                <TableCell>{template.lastEdited}</TableCell>\r\n                            </TableRow>\r\n                        ))}\r\n                    </TableBody>\r\n                </Table>\r\n            </div>\r\n        </div>\r\n    )\r\n}\r\n"], "names": ["templates", "name", "category", "language", "content", "status", "messagesDelivered", "messageReadRate", "topBlockReason", "lastEdited", "WhatsAppTemplates", "jsxs", "className", "children", "jsx", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Link", "to", "target", "rel", "ExternalLink", "Table", "TableHeader", "TableRow", "TableHead", "TableBody", "map", "template", "TableCell"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA,MAAM,eAAe,iBAAiB,gBAAgB;AAAA,EACpD,CAAC,QAAQ,EAAE,GAAG,aAAa,KAAK,SAAQ,CAAE;AAAA,EAC1C,CAAC,QAAQ,EAAE,GAAG,eAAe,KAAK,SAAQ,CAAE;AAAA,EAC5C,CAAC,QAAQ,EAAE,GAAG,4DAA4D,KAAK,SAAU,CAAA;AAC3F,CAAC;ACWD,MAAMA,YAAwB,CAC1B;AAAA,EACIC,MAAM;AAAA,EACNC,UAAU;AAAA,EACVC,UAAU;AAAA,EACVC,SAAS;AAAA,EACTC,QAAQ;AAAA,EACRC,mBAAmB;AAAA,EACnBC,iBAAiB;AAAA,EACjBC,gBAAgB;AAAA,EAChBC,YAAY;AAChB,GACA;AAAA,EACIR,MAAM;AAAA,EACNC,UAAU;AAAA,EACVC,UAAU;AAAA,EACVC,SAAS;AAAA,EACTC,QAAQ;AAAA,EACRC,mBAAmB;AAAA,EACnBC,iBAAiB;AAAA,EACjBC,gBAAgB;AAAA,EAChBC,YAAY;AAChB,GACA;AAAA,EACIR,MAAM;AAAA,EACNC,UAAU;AAAA,EACVC,UAAU;AAAA,EACVC,SAAS;AAAA,EACTC,QAAQ;AAAA,EACRC,mBAAmB;AAAA,EACnBC,iBAAiB;AAAA,EACjBC,gBAAgB;AAAA,EAChBC,YAAY;AAChB,GACA;AAAA,EACIR,MAAM;AAAA,EACNC,UAAU;AAAA,EACVC,UAAU;AAAA,EACVC,SAAS;AAAA,EACTC,QAAQ;AAAA,EACRC,mBAAmB;AAAA,EACnBC,iBAAiB;AAAA,EACjBC,gBAAgB;AAAA,EAChBC,YAAY;AAChB,GACA;AAAA,EACIR,MAAM;AAAA,EACNC,UAAU;AAAA,EACVC,UAAU;AAAA,EACVC,SAAS;AAAA,EACTC,QAAQ;AAAA,EACRC,mBAAmB;AAAA,EACnBC,iBAAiB;AAAA,EACjBC,gBAAgB;AAAA,EAChBC,YAAY;AAChB,GACA;AAAA,EACIR,MAAM;AAAA,EACNC,UAAU;AAAA,EACVC,UAAU;AAAA,EACVC,SAAS;AAAA,EACTC,QAAQ;AAAA,EACRC,mBAAmB;AAAA,EACnBC,iBAAiB;AAAA,EACjBC,gBAAgB;AAAA,EAChBC,YAAY;AAChB,CAAA;AAGJ,SAAwBC,oBAAoB;AAEpC,SAAAC,kCAAAA,KAAC,OAAI;AAAA,IAAAC,WAAU;AAAA,IACXC,UAAA,CAACF,kCAAA,KAAA,OAAA;AAAA,MAAIC,WAAU;AAAA,MACXC,UAAA,CAACC,kCAAA,IAAA,MAAA;AAAA,QAAGF,WAAU;AAAA,QAAqBC,UAAkB;AAAA,MAAA,CAAA,GACrDC,kCAAA,IAACC,QAAO;AAAA,QAAAC,SAAO;AAAA,QACXH,UAAAF,kCAAA,KAACM,MAAK;AAAA,UAAAC,IAAG;AAAA,UAA0EC,QAAO;AAAA,UAASC,KAAI;AAAA,UACnGP,UAAA,CAACC,kCAAA,IAAAO,cAAA;AAAA,YAAaT,WAAU;AAAA,UAAe,CAAA,GAAE,oBAAA;AAAA,QAC7C,CAAA;AAAA,MACJ,CAAA,CAAA;AAAA,IACJ,CAAA,GACCE,kCAAA,IAAA,OAAA;AAAA,MAAIF,WAAU;AAAA,MACXC,iDAACS,OACG;AAAA,QAAAT,UAAA,CAACC,kCAAA,IAAAS,aAAA;AAAA,UACGV,iDAACW,UACG;AAAA,YAAAX,UAAA,CAAAC,kCAAA,IAACW;cAAUZ,UAAa;AAAA,YAAA,CAAA,GACxBC,kCAAA,IAACW;cAAUZ,UAAQ;AAAA,YAAA,CAAA,GACnBC,kCAAA,IAACW;cAAUZ,UAAQ;AAAA,YAAA,CAAA,GACnBC,kCAAA,IAACW;cAAUZ,UAAM;AAAA,YAAA,CAAA,GACjBC,kCAAA,IAACW;cAAUZ,UAAkB;AAAA,YAAA,CAAA,GAC7BC,kCAAA,IAACW;cAAUZ,UAAiB;AAAA,YAAA,CAAA,GAC5BC,kCAAA,IAACW;cAAUZ,UAAgB;AAAA,YAAA,CAAA,GAC3BC,kCAAA,IAACW;cAAUZ,UAAW;AAAA,YAAA,CAAA,CAAA;AAAA,UAC1B,CAAA;AAAA,QACJ,CAAA,yCACCa,WACI;AAAA,UAAAb,UAAAb,UAAU2B,IAAKC,qDACXJ,UACG;AAAA,YAAAX,UAAA,CAAAC,kCAAA,IAACe,WAAU;AAAA,cAAAjB,WAAU;AAAA,cAAeC,UAAAe,SAAS3B;AAAAA,YAAK,CAAA,GAClDa,kCAAA,IAACe,WAAW;AAAA,cAAAhB,UAAAe,SAAS1B;AAAAA,YAAS,CAAA,GAC9BY,kCAAA,IAACe,WAAW;AAAA,cAAAhB,UAAAe,SAASzB;AAAAA,YAAS,CAAA,yCAC7B0B,WACf;AAAA,cAAAhB,UAAAC,kCAAA,IAAC,QAAA;AAAA,gBACGF,WAAU;AAAA,gBAEXC,UAASe,SAAAvB;AAAAA,cACZ,CAAA;AAAA,YACc,CAAA,GACAS,kCAAA,IAACe,WAAW;AAAA,cAAAhB,UAAAe,SAAStB;AAAAA,YAAkB,CAAA,GACvCQ,kCAAA,IAACe,WAAW;AAAA,cAAAhB,UAAAe,SAASrB;AAAAA,YAAgB,CAAA,GACrCO,kCAAA,IAACe,WAAW;AAAA,cAAAhB,UAAAe,SAASpB;AAAAA,YAAe,CAAA,GACpCM,kCAAA,IAACe,WAAW;AAAA,cAAAhB,UAAAe,SAASnB;AAAAA,YAAW,CAAA,CAAA;AAAA,UAdrB,GAAAmB,SAAS3B,IAexB,CACH;AAAA,QACL,CAAA,CAAA;AAAA,MACJ,CAAA;AAAA,IACJ,CAAA,CAAA;AAAA,EACJ,CAAA;AAER;", "x_google_ignoreList": [0]}