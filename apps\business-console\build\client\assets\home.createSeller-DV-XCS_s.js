import { r as reactExports, j as jsxRuntimeExports } from "./jsx-runtime-bB2y7OuD.js";
import { B as Button } from "./button-ByAXMyvk.js";
import { I as Input } from "./input-3v87qohQ.js";
import { d as useActionData, F as Form } from "./components-D7UvGag_.js";
import { a as useNavigate } from "./index-DhHTcibu.js";
import { A as ArrowLeft } from "./arrow-left-D_Ztdrp5.js";
import "./index-z_byfFrQ.js";
import "./index-ImHKLo0a.js";
import "./utils-GkgzjW3c.js";
import "./index-Vp2vNLNM.js";
import "./createLucideIcon-uwkRm45G.js";
function CreateSeller() {
  useActionData();
  const [formData, setFormData] = reactExports.useState({
    name: "",
    address: "",
    email: "",
    customerSupportNumber: "",
    owner: {
      firstName: "",
      lastName: "",
      email: "",
      mobileNumber: "",
      address: "",
      password: "",
      businessId: 0,
      roles: ""
    },
    areaId: 0,
    latitude: "",
    longitude: ""
  });
  const handleChange = (e) => {
    const {
      name,
      value
    } = e.target;
    if (name.startsWith("owner.")) {
      const field = name.split(".")[1];
      setFormData((prev) => ({
        ...prev,
        owner: {
          ...prev.owner,
          [field]: value
        }
      }));
    } else {
      setFormData((prev) => ({
        ...prev,
        [name]: value
      }));
    }
  };
  const navigate = useNavigate();
  return /* @__PURE__ */ jsxRuntimeExports.jsxs(Form, {
    method: "post",
    className: "space-y-4 p-4 border rounded max-w-2xl bg-slate-100",
    children: [/* @__PURE__ */ jsxRuntimeExports.jsx("div", {
      className: "flex items-center gap-2 mb-1",
      children: /* @__PURE__ */ jsxRuntimeExports.jsxs(Button, {
        variant: "ghost",
        size: "sm",
        onClick: () => navigate("/home/<USER>"),
        children: [/* @__PURE__ */ jsxRuntimeExports.jsx(ArrowLeft, {
          className: "h-4 w-4 mr-2"
        }), "Back to Sellers"]
      })
    }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
      children: [/* @__PURE__ */ jsxRuntimeExports.jsx("h3", {
        children: "Seller Details"
      }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
        className: "space-y-3 mt-2",
        children: [" ", /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
          className: "flex gap-3 ",
          children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Input, {
            type: "text",
            name: "name",
            placeholder: "Name",
            onChange: handleChange,
            required: true,
            className: "max-w-sm"
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(Input, {
            type: "text",
            name: "address",
            placeholder: "Address",
            onChange: handleChange,
            required: true,
            className: "max-w-sm"
          })]
        }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
          className: "flex gap-3",
          children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Input, {
            type: "email",
            name: "email",
            placeholder: "Email",
            onChange: handleChange,
            required: true,
            className: "max-w-sm"
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(Input, {
            type: "text",
            name: "customerSupportNumber",
            placeholder: "Customer Support Number",
            onChange: handleChange,
            required: true,
            className: "max-w-sm"
          })]
        })]
      })]
    }), /* @__PURE__ */ jsxRuntimeExports.jsx("h3", {
      children: "Owner Details"
    }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
      className: "flex gap-3",
      children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Input, {
        type: "text",
        name: "owner.firstName",
        placeholder: "First Name",
        onChange: handleChange,
        required: true,
        className: "max-w-sm"
      }), /* @__PURE__ */ jsxRuntimeExports.jsx(Input, {
        type: "text",
        name: "owner.lastName",
        placeholder: "Last Name",
        onChange: handleChange,
        required: true,
        className: "max-w-sm"
      })]
    }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
      className: "flex gap-3",
      children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Input, {
        type: "email",
        name: "owner.email",
        placeholder: "Owner Email",
        onChange: handleChange,
        required: true,
        className: "max-w-sm"
      }), /* @__PURE__ */ jsxRuntimeExports.jsx(Input, {
        type: "text",
        name: "owner.mobileNumber",
        placeholder: "Mobile Number",
        onChange: handleChange,
        required: true,
        className: "max-w-sm"
      })]
    }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
      className: "flex gap-3",
      children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Input, {
        type: "text",
        name: "owner.address",
        placeholder: "Owner Address",
        onChange: handleChange,
        required: true,
        className: "max-w-sm"
      }), /* @__PURE__ */ jsxRuntimeExports.jsx(Input, {
        type: "password",
        name: "owner.password",
        placeholder: "Password",
        onChange: handleChange,
        required: true,
        className: "max-w-sm"
      })]
    }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
      className: "flex gap-3",
      children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Input, {
        type: "number",
        name: "owner.businessId",
        placeholder: "Business ID",
        onChange: handleChange,
        required: true,
        className: "max-w-sm"
      }), /* @__PURE__ */ jsxRuntimeExports.jsx(Input, {
        type: "text",
        name: "owner.roles",
        placeholder: "Roles (comma separated)",
        onChange: handleChange,
        required: true,
        className: "max-w-sm"
      })]
    }), /* @__PURE__ */ jsxRuntimeExports.jsx("h3", {
      children: "Location"
    }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
      className: "flex gap-3",
      children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Input, {
        type: "number",
        name: "areaId",
        placeholder: "Area ID",
        onChange: handleChange,
        required: true,
        className: "max-w-sm"
      }), /* @__PURE__ */ jsxRuntimeExports.jsx(Input, {
        type: "text",
        name: "latitude",
        placeholder: "Latitude",
        onChange: handleChange,
        required: true,
        className: "max-w-sm"
      }), /* @__PURE__ */ jsxRuntimeExports.jsx(Input, {
        type: "text",
        name: "longitude",
        placeholder: "Longitude",
        onChange: handleChange,
        required: true,
        className: "max-w-sm"
      })]
    }), /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
      className: "flex justify-end",
      children: /* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
        type: "submit",
        className: "rounded-full",
        children: "Create Seller"
      })
    })]
  });
}
export {
  CreateSeller as default
};
//# sourceMappingURL=home.createSeller-DV-XCS_s.js.map
