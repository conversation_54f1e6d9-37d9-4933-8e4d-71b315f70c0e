import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";

export default function Menu() {
  return (
    <div className="p-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-gray-900">Menu Management</h1>
        <p className="text-gray-600 mt-2">Manage your restaurant menu items</p>
      </div>
      
      <div className="flex items-center justify-center min-h-[400px]">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <div className="mx-auto mb-4 w-16 h-16 bg-gradient-to-r from-orange-500 to-red-600 rounded-full flex items-center justify-center">
              <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 10h16M4 14h16M4 18h16" />
              </svg>
            </div>
            <CardTitle className="text-2xl">Menu Management Coming Soon</CardTitle>
            <CardDescription className="text-lg">
              We&apos;re creating an intuitive menu management system for your restaurant.
            </CardDescription>
          </CardHeader>
          <CardContent className="text-center">
            <p className="text-gray-500 mb-4">
              Add, edit, and organize your menu items with ease. Manage categories, pricing, and availability.
            </p>
            <div className="inline-flex items-center px-4 py-2 bg-gradient-to-r from-orange-500 to-red-600 text-black rounded-lg">
              <span className="mr-2">🍽️</span>
              Launching Soon
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
} 