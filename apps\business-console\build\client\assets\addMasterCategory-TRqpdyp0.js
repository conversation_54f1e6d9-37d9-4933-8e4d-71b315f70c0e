import { r as reactExports, j as jsxRuntimeExports } from "./jsx-runtime-bB2y7OuD.js";
import { I as Input } from "./input-3v87qohQ.js";
import { L as Label } from "./label-cSASrwzW.js";
import { D as Dialog, d as DialogTrigger, a as DialogContent } from "./dialog-BqKosxNq.js";
import { B as Button } from "./button-ByAXMyvk.js";
import { S as Select, a as SelectTrigger, b as SelectValue, c as SelectContent, d as SelectItem } from "./select-BFGSXKcr.js";
import { a as useFetcher } from "./components-D7UvGag_.js";
import { R as RadioGroup, a as RadioGroupItem } from "./radio-group-ChzooXbR.js";
function SearchableCategories({
  label = "Categories",
  apiUrl,
  selectedCategories,
  onCategoryAdd,
  onCategoryRemove,
  required = false,
  error,
  level
}) {
  const [categoryInput, setCategoryInput] = reactExports.useState("");
  const [categoryItems, setCategoryItems] = reactExports.useState([]);
  const [loadingCategories, setLoadingCategories] = reactExports.useState(false);
  const [categoryPage, setCategoryPage] = reactExports.useState(1);
  const [hasMoreCategories, setHasMoreCategories] = reactExports.useState(true);
  const fetcher = useFetcher();
  const searchCategories = async (query) => {
    setLoadingCategories(true);
    fetcher.load(`${apiUrl}?searchCategories=${query}&categoryPage=0&level=${level}`);
  };
  const loadMoreCategories = async () => {
    if (!hasMoreCategories || loadingCategories) return;
    const nextPage = categoryPage + 1;
    fetcher.load(`${apiUrl}?searchCategories=${categoryInput}&categoryPage=${nextPage}&level=${level}`);
  };
  reactExports.useEffect(() => {
    var _a;
    if ((_a = fetcher.data) == null ? void 0 : _a.categories) {
      const items = fetcher.data.categories.map((category) => ({
        value: String(category.id),
        label: category.name,
        numericId: category.id
      }));
      if (fetcher.data.categoryPage === 0) {
        setCategoryItems(items);
      } else {
        setCategoryItems((prev) => [...prev, ...items]);
      }
      setCategoryPage(fetcher.data.categoryPage);
      setHasMoreCategories(items.length === 20);
      setLoadingCategories(false);
    }
  }, [fetcher.data]);
  const handleAddCategory = (categoryValue) => {
    const categoryItem = categoryItems.find((item) => item.value === categoryValue);
    if (!categoryItem) return;
    const categoryId = categoryItem.numericId;
    if (!selectedCategories.some((cat) => cat.numericId === categoryId)) {
      onCategoryAdd(categoryId, categoryItem.label);
    }
    setCategoryInput("");
  };
  const getCategoryName = (categoryId) => {
    const newCategory = categoryItems.find((item) => item.numericId === categoryId);
    if (newCategory) return newCategory.label;
    const selectedCategory = selectedCategories.find((item) => item.numericId === categoryId);
    if (selectedCategory) return selectedCategory.label;
    return String(categoryId);
  };
  return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "w-full", children: [
    /* @__PURE__ */ jsxRuntimeExports.jsxs(Label, { children: [
      label,
      " ",
      required && "(Required)"
    ] }),
    /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "flex gap-2 items-center", children: /* @__PURE__ */ jsxRuntimeExports.jsx(
      Input,
      {
        placeholder: "Search categories",
        value: categoryInput,
        onChange: (e) => {
          setCategoryInput(e.target.value);
          searchCategories(e.target.value);
        }
      }
    ) }),
    required && selectedCategories.length < 1 && /* @__PURE__ */ jsxRuntimeExports.jsx("p", { className: "text-red-500", children: "At least one category is required." }),
    /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "flex gap-2 mt-2 flex-wrap", children: selectedCategories.map((category) => {
      const categoryName = getCategoryName(category.numericId);
      return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "inline-flex items-center space-x-1 bg-gray-200 p-1 rounded", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx("span", { children: categoryName }),
        /* @__PURE__ */ jsxRuntimeExports.jsx(
          "button",
          {
            onClick: () => onCategoryRemove(category.numericId),
            className: "hover:text-red-500",
            children: "×"
          }
        )
      ] }, category.numericId);
    }) }),
    error && /* @__PURE__ */ jsxRuntimeExports.jsx("p", { className: "text-red-500", children: error }),
    loadingCategories && /* @__PURE__ */ jsxRuntimeExports.jsx("p", { children: "Loading categories..." }),
    categoryItems.length > 0 && categoryInput && /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "mt-2 border rounded-md p-2", children: [
      categoryItems.map((category) => {
        if (selectedCategories.some((cat) => cat.numericId === category.numericId)) {
          return null;
        }
        return /* @__PURE__ */ jsxRuntimeExports.jsx(
          "button",
          {
            className: "w-full text-left cursor-pointer hover:bg-gray-100 p-1",
            onClick: () => handleAddCategory(category.value),
            onKeyDown: (e) => {
              if (e.key === "Enter" || e.key === " ") {
                handleAddCategory(category.value);
              }
            },
            children: category.label
          },
          category.value
        );
      }),
      hasMoreCategories && /* @__PURE__ */ jsxRuntimeExports.jsx(
        Button,
        {
          type: "button",
          variant: "ghost",
          className: "w-full mt-2",
          onClick: loadMoreCategories,
          children: "Load More"
        }
      )
    ] })
  ] });
}
function AddMasterCategory({
  categoryDetails,
  buttonName,
  handleSubmit,
  mode
}) {
  const [ctnBtnClicked, setCtnBtnClicked] = reactExports.useState(false);
  const [categoryName, setCategoryName] = reactExports.useState(mode === "Edit" ? categoryDetails == null ? void 0 : categoryDetails.name : "");
  const [uploadingImage, setUploadingImage] = reactExports.useState(false);
  const [uploadError, setUploadError] = reactExports.useState(null);
  const [selectedFile, setSelectedFile] = reactExports.useState(null);
  const [filePreviewUrl, setFilePreviewUrl] = reactExports.useState(null);
  const [uploadedImageUrl, setUploadedImageUrl] = reactExports.useState(
    mode === "Edit" && (categoryDetails == null ? void 0 : categoryDetails.picture) ? categoryDetails.picture.toString() : ""
  );
  const fileInputRef = reactExports.useRef(null);
  const uploadFetcher = useFetcher();
  const [selectedLevel, setSelectedLevel] = reactExports.useState(mode === "Edit" ? categoryDetails == null ? void 0 : categoryDetails.level.toString() : "");
  const [sequence, setSequence] = reactExports.useState(
    mode === "Edit" && (categoryDetails == null ? void 0 : categoryDetails.sequence) !== void 0 ? categoryDetails.sequence.toString() : ""
  );
  const [ondcDomain, setOndcDomain] = reactExports.useState(
    mode === "Edit" && (categoryDetails == null ? void 0 : categoryDetails.ondcDomain) ? categoryDetails.ondcDomain : "RET10"
  );
  const [images, setImages] = reactExports.useState([]);
  const [dialogOpen, setDialogOpen] = reactExports.useState(false);
  const transformToCategoryItem = (parentCategories) => {
    return parentCategories.map((category) => ({
      value: category.id.toString(),
      label: category.name,
      numericId: category.id
    }));
  };
  const [updatedCategory, setUpdatedCategory] = reactExports.useState(
    () => mode === "Edit" && (categoryDetails == null ? void 0 : categoryDetails.parentCategories) ? transformToCategoryItem(categoryDetails == null ? void 0 : categoryDetails.parentCategories) : []
  );
  const resetForm = () => {
    if (mode !== "Edit") {
      setCategoryName("");
      setSelectedLevel("");
      setUpdatedCategory([]);
      setSelectedFile(null);
      setFilePreviewUrl(null);
      setUploadedImageUrl("");
      setUploadingImage(false);
      setUploadError(null);
      setCtnBtnClicked(false);
    }
  };
  const levels = [
    { id: "1", name: "1" },
    { id: "2", name: "2" },
    { id: "3", name: "3" }
  ];
  const handleSelectedLevel = (value) => {
    setSelectedLevel(value);
  };
  const isContinueDisabled = !categoryName || !selectedLevel;
  const isSubmitDisabled = () => {
    if (ondcDomain === "RET10") {
      return !uploadedImageUrl || !categoryName || !selectedLevel;
    } else {
      return !categoryName || !selectedLevel;
    }
  };
  const handleFileSelect = async (event) => {
    var _a;
    const file = (_a = event.target.files) == null ? void 0 : _a[0];
    if (!file) return;
    setUploadError(null);
    const MAX_FILE_SIZE = 500 * 1024 * 1024;
    if (file.size > MAX_FILE_SIZE) {
      setUploadError("File size exceeds 5MB limit");
      return;
    }
    const allowedTypes = ["image/jpeg", "image/png", "image/gif", "image/webp"];
    if (!allowedTypes.includes(file.type)) {
      setUploadError("Only JPEG, PNG, GIF, and WEBP images are allowed");
      return;
    }
    setSelectedFile(file);
    const reader = new FileReader();
    reader.onloadend = () => {
      setFilePreviewUrl(reader.result);
    };
    reader.readAsDataURL(file);
  };
  const handleUpload = () => {
    if (!selectedFile) return;
    setUploadingImage(true);
    const formData = new FormData();
    formData.append("_action", "uploadImage");
    formData.append("file", selectedFile, selectedFile.name);
    uploadFetcher.submit(formData, {
      method: "post",
      encType: "multipart/form-data"
    });
  };
  const openFilePicker = () => {
    var _a;
    (_a = fileInputRef.current) == null ? void 0 : _a.click();
  };
  reactExports.useEffect(() => {
    if (uploadFetcher.data) {
      if (uploadFetcher.data.error) {
        setUploadError(uploadFetcher.data.error);
        setUploadingImage(false);
      } else if (uploadFetcher.data.fileUrl) {
        setUploadedImageUrl(uploadFetcher.data.fileUrl);
        const newImage = {
          id: Date.now(),
          url: uploadFetcher.data.fileUrl,
          sequence: images.length + 1,
          isDefault: false
        };
        setImages((prevImages) => [...prevImages, newImage]);
        setUploadingImage(false);
        setUploadError(null);
        setSelectedFile(null);
        setFilePreviewUrl(null);
        if (fileInputRef.current) {
          fileInputRef.current.value = "";
        }
      }
    }
  }, [uploadFetcher.data]);
  const closeDialog = () => {
    if (mode !== "Edit") {
      setCategoryName("");
      setSelectedLevel("");
      setSelectedFile(null);
      setUpdatedCategory([]);
      setFilePreviewUrl(null);
      setUploadedImageUrl("");
      setUploadingImage(false);
      setUploadError(null);
      setCtnBtnClicked(false);
      setDialogOpen(false);
    }
    setDialogOpen(false);
  };
  const onHandleSubmit = () => {
    if (!selectedLevel || !categoryName) {
      console.error("Required fields are missing.");
      return;
    }
    handleSubmit(
      ondcDomain,
      parseInt(selectedLevel),
      categoryName,
      parseInt(sequence ?? "0"),
      uploadedImageUrl,
      updatedCategory,
      closeDialog,
      mode,
      categoryDetails == null ? void 0 : categoryDetails.id
    );
  };
  const handleOndcDomainChange = (val) => {
    setOndcDomain(val);
    if (val === "RET11") {
      setSelectedLevel("1");
      setUpdatedCategory([]);
    } else {
      setSelectedLevel(mode === "Edit" ? categoryDetails == null ? void 0 : categoryDetails.level.toString() : "");
      setUpdatedCategory(
        () => mode === "Edit" && (categoryDetails == null ? void 0 : categoryDetails.parentCategories) ? transformToCategoryItem(categoryDetails == null ? void 0 : categoryDetails.parentCategories) : []
      );
    }
  };
  return /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "container mx-auto   ", children: /* @__PURE__ */ jsxRuntimeExports.jsxs(Dialog, { open: dialogOpen, onOpenChange: (isOpen) => {
    setDialogOpen(isOpen);
    if (!isOpen) {
      resetForm();
    }
  }, children: [
    mode === "Edit" ? /* @__PURE__ */ jsxRuntimeExports.jsx(DialogTrigger, { asChild: true, className: "flex", children: /* @__PURE__ */ jsxRuntimeExports.jsx(
      Button,
      {
        size: "sm",
        variant: "secondary",
        children: "Edit"
      }
    ) }) : /* @__PURE__ */ jsxRuntimeExports.jsx(DialogTrigger, { asChild: true, className: "flex", children: /* @__PURE__ */ jsxRuntimeExports.jsxs(Button, { className: "fixed bottom-5 right-5 rounded-full", children: [
      "+ ",
      buttonName
    ] }) }),
    /* @__PURE__ */ jsxRuntimeExports.jsxs(
      DialogContent,
      {
        className: "w-full sm:w-[400px] md:w-[600px] max-h-[90vh] overflow-y-auto absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2",
        children: [
          /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "grid gap-4", children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "space-y-2", children: mode === "Edit" ? /* @__PURE__ */ jsxRuntimeExports.jsx("h4", { className: "font-medium leading-none text-center md:text-left", children: "Edit Category" }) : /* @__PURE__ */ jsxRuntimeExports.jsx("h4", { className: "font-medium leading-none text-center md:text-left", children: "Add Category" }) }),
            /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "grid gap-4", children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "grid grid-cols-1 md:grid-cols-2 gap-4", children: [
              /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "space-y-2 md:col-span-2", children: [
                /* @__PURE__ */ jsxRuntimeExports.jsx(Label, { htmlFor: "ondcDomain", children: "Business Type" }),
                /* @__PURE__ */ jsxRuntimeExports.jsxs(
                  RadioGroup,
                  {
                    value: ondcDomain,
                    onValueChange: (val) => handleOndcDomainChange(val),
                    className: "grid grid-cols-3 gap-4 mt-1",
                    children: [
                      /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center space-x-2", children: [
                        /* @__PURE__ */ jsxRuntimeExports.jsx(RadioGroupItem, { id: "type-RET11", value: "RET11" }),
                        /* @__PURE__ */ jsxRuntimeExports.jsx(Label, { htmlFor: "type-RET11", children: "Restaurant" })
                      ] }),
                      /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center space-x-2", children: [
                        /* @__PURE__ */ jsxRuntimeExports.jsx(RadioGroupItem, { id: "type-RET10", value: "RET10" }),
                        /* @__PURE__ */ jsxRuntimeExports.jsx(Label, { htmlFor: "type-RET10", children: "Non-Restaurant" })
                      ] })
                    ]
                  }
                )
              ] }),
              /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "space-y-2", children: [
                /* @__PURE__ */ jsxRuntimeExports.jsx(Label, { htmlFor: "categoryName", children: "Category Name (Required)" }),
                /* @__PURE__ */ jsxRuntimeExports.jsx(
                  Input,
                  {
                    id: "categoryName",
                    placeholder: "Enter category name",
                    className: "w-full h-8",
                    required: true,
                    value: categoryName,
                    onChange: (e) => setCategoryName(e.target.value),
                    disabled: mode === "Edit"
                  }
                ),
                /* @__PURE__ */ jsxRuntimeExports.jsx("p", { className: "text-sm text-slate-400 ", children: "Enter the English Name" })
              ] }),
              ondcDomain === "RET10" && /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "space-y-2", children: [
                /* @__PURE__ */ jsxRuntimeExports.jsx(Label, { htmlFor: "categoryDetails", children: "Category Details (Required)" }),
                /* @__PURE__ */ jsxRuntimeExports.jsxs(
                  Select,
                  {
                    value: selectedLevel,
                    onValueChange: handleSelectedLevel,
                    disabled: mode === "Edit",
                    children: [
                      /* @__PURE__ */ jsxRuntimeExports.jsx(SelectTrigger, { children: /* @__PURE__ */ jsxRuntimeExports.jsx(SelectValue, { placeholder: "Select a Level" }) }),
                      /* @__PURE__ */ jsxRuntimeExports.jsx(SelectContent, { children: levels.map((x) => /* @__PURE__ */ jsxRuntimeExports.jsx(SelectItem, { value: x.id, children: x.name }, x.id)) })
                    ]
                  }
                )
              ] }),
              ctnBtnClicked && /* @__PURE__ */ jsxRuntimeExports.jsxs(jsxRuntimeExports.Fragment, { children: [
                /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "col-span-full", children: selectedLevel !== "3" && ondcDomain === "RET10" && /* @__PURE__ */ jsxRuntimeExports.jsx(
                  SearchableCategories,
                  {
                    label: "Parent Categories",
                    apiUrl: "/home/<USER>",
                    selectedCategories: updatedCategory || [],
                    onCategoryAdd: (categoryId, categoryName2) => {
                      setUpdatedCategory((prevUpdatedCategory) => [
                        ...prevUpdatedCategory || [],
                        { numericId: categoryId, value: categoryName2, label: "" }
                      ]);
                    },
                    onCategoryRemove: (categoryId) => {
                      setUpdatedCategory(
                        (prevUpdatedCategory) => (prevUpdatedCategory || []).filter(
                          (cat) => cat.numericId !== categoryId
                        )
                      );
                    },
                    required: true,
                    level: (Number(selectedLevel) || 0) + 1
                  }
                ) }),
                /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "col-span-full", children: [
                  /* @__PURE__ */ jsxRuntimeExports.jsxs(Label, { children: [
                    "Category Image ",
                    ondcDomain === "RET10" ? "(Required)" : "(Optional)"
                  ] }),
                  /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex gap-2 items-center", children: [
                    /* @__PURE__ */ jsxRuntimeExports.jsx(
                      Input,
                      {
                        type: "text",
                        readOnly: true,
                        value: (selectedFile == null ? void 0 : selectedFile.name) || "No file selected",
                        className: "flex-grow",
                        onClick: openFilePicker,
                        style: { cursor: "pointer" }
                      }
                    ),
                    /* @__PURE__ */ jsxRuntimeExports.jsx(
                      "input",
                      {
                        ref: fileInputRef,
                        type: "file",
                        accept: "image/*",
                        onChange: handleFileSelect,
                        className: "hidden"
                      }
                    ),
                    /* @__PURE__ */ jsxRuntimeExports.jsx(
                      Button,
                      {
                        type: "button",
                        onClick: selectedFile ? handleUpload : openFilePicker,
                        disabled: uploadingImage,
                        children: uploadingImage ? /* @__PURE__ */ jsxRuntimeExports.jsxs("span", { className: "flex items-center gap-2", children: [
                          /* @__PURE__ */ jsxRuntimeExports.jsxs("svg", { className: "animate-spin h-4 w-4", viewBox: "0 0 24 24", children: [
                            /* @__PURE__ */ jsxRuntimeExports.jsx("circle", { className: "opacity-25", cx: "12", cy: "12", r: "10", stroke: "currentColor", strokeWidth: "4", fill: "none" }),
                            /* @__PURE__ */ jsxRuntimeExports.jsx("path", { className: "opacity-75", fill: "currentColor", d: "M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" })
                          ] }),
                          "Uploading..."
                        ] }) : selectedFile ? "Upload" : "Add Image"
                      }
                    )
                  ] }),
                  uploadError && /* @__PURE__ */ jsxRuntimeExports.jsx("p", { className: "text-red-500 mt-1 text-sm", children: uploadError }),
                  /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "mt-2 space-y-2", children: [
                    filePreviewUrl && /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "flex justify-center", children: /* @__PURE__ */ jsxRuntimeExports.jsx("img", { src: filePreviewUrl, alt: "Image Preview", className: "max-w-full h-20 object-cover rounded" }) }),
                    uploadedImageUrl && !filePreviewUrl && /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "flex justify-center", children: /* @__PURE__ */ jsxRuntimeExports.jsx("img", { src: uploadedImageUrl, alt: "Uploaded Image", className: "max-w-full h-20 object-cover rounded" }) })
                  ] })
                ] }),
                selectedLevel === "1" && /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "space-y-2", children: [
                  /* @__PURE__ */ jsxRuntimeExports.jsx(Label, { htmlFor: "Sequence", children: "Sequence (Required)" }),
                  /* @__PURE__ */ jsxRuntimeExports.jsx(
                    Input,
                    {
                      id: "sequence",
                      placeholder: "Enter Sequence number",
                      className: "w-full h-8",
                      required: true,
                      value: sequence,
                      onChange: (e) => setSequence(e.target.value),
                      type: "number"
                    }
                  )
                ] })
              ] })
            ] }) })
          ] }),
          /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex flex-col md:flex-row justify-end gap-2 mt-5", children: [
            !ctnBtnClicked && /* @__PURE__ */ jsxRuntimeExports.jsx(
              Button,
              {
                size: "sm",
                className: "w-full md:w-auto",
                disabled: isContinueDisabled,
                onClick: () => setCtnBtnClicked(true),
                children: "Continue"
              }
            ),
            ctnBtnClicked && /* @__PURE__ */ jsxRuntimeExports.jsx(
              Button,
              {
                size: "sm",
                className: "w-full md:w-auto",
                onClick: () => onHandleSubmit(),
                disabled: isSubmitDisabled(),
                children: "Submit"
              }
            )
          ] })
        ]
      }
    )
  ] }) });
}
export {
  AddMasterCategory as A,
  SearchableCategories as S
};
//# sourceMappingURL=addMasterCategory-TRqpdyp0.js.map
