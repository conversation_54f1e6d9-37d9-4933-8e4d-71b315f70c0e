import { Card, CardContent, CardDescription, Card<PERSON>eader, CardTitle } from "~/components/ui/card";

export default function Campaigns() {
  return (
    <div className="p-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-gray-900">Campaigns</h1>
        <p className="text-gray-600 mt-2">Manage your marketing campaigns</p>
      </div>
      
      <div className="flex items-center justify-center min-h-[400px]">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <div className="mx-auto mb-4 w-16 h-16 bg-gradient-to-r from-pink-500 to-purple-600 rounded-full flex items-center justify-center">
              <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5.882V19.24a1.76 1.76 0 01-3.417.592l-2.147-6.15M18 13a3 3 0 100-6M5.436 13.683A4.001 4.001 0 017 6h1.832c4.1 0 7.625-1.234 9.168-3v14c-1.543-1.766-5.067-3-9.168-3H7a3.988 3.988 0 01-1.564-.317z" />
              </svg>
            </div>
            <CardTitle className="text-2xl">Marketing Campaigns Coming Soon</CardTitle>
            <CardDescription className="text-lg">
              We&apos;re building powerful marketing tools to grow your customer base.
            </CardDescription>
          </CardHeader>
          <CardContent className="text-center">
            <p className="text-gray-500 mb-4">
              Create targeted campaigns, track performance, and boost your restaurant&apos;s visibility.
            </p>
            <div className="inline-flex items-center px-4 py-2 bg-gradient-to-r from-pink-500 to-purple-600 text-black rounded-lg">
              <span className="mr-2">📢</span>
              Launching Soon
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
} 