import { r as reactExports, j as jsxRuntimeExports } from "./jsx-runtime-bB2y7OuD.js";
import { C as Card, b as <PERSON><PERSON><PERSON><PERSON>, c as Card<PERSON><PERSON><PERSON>, d as CardDescription, a as CardContent } from "./card-BJQMSLe_.js";
import { B as Button } from "./button-ByAXMyvk.js";
import { S as Select, a as SelectTrigger, b as SelectValue, c as SelectContent, d as SelectItem } from "./select-BFGSXKcr.js";
import { u as useToast } from "./use-toast-EUd7m8UG.js";
import { I as Input } from "./input-3v87qohQ.js";
import { a as useNavigate } from "./index-DhHTcibu.js";
import { c as useSubmit, u as useLoaderData, d as useActionData } from "./components-D7UvGag_.js";
import { A as ArrowLeft } from "./arrow-left-D_Ztdrp5.js";
import "./utils-GkgzjW3c.js";
import "./index-z_byfFrQ.js";
import "./index-ImHKLo0a.js";
import "./index-Vp2vNLNM.js";
import "./index-IXOTxK3N.js";
import "./index-D7VH9Fc8.js";
import "./index-qCtcpOcW.js";
import "./index-BTdCMChR.js";
import "./index-BXzPK7u0.js";
import "./index-CzZ6EYkN.js";
import "./index-CEHS9zzk.js";
import "./index-DVTNuYOr.js";
import "./index-DscYByPT.js";
import "./index-CkL5tk39.js";
import "./index-dIGjFc0I.js";
import "./index-CpKiYcZd.js";
import "./index-C88PRvfd.js";
import "./chevron-down-pCP5jmjX.js";
import "./createLucideIcon-uwkRm45G.js";
import "./check-_dbWxIzT.js";
const googleMapsPromise = {
  promise: null,
  resolve: null,
  reject: null
};
const loadGoogleMapsScript = (apiKey) => {
  if (googleMapsPromise.promise) {
    return googleMapsPromise.promise;
  }
  googleMapsPromise.promise = new Promise((resolve, reject) => {
    var _a, _b;
    googleMapsPromise.resolve = resolve;
    googleMapsPromise.reject = reject;
    if ((_b = (_a = window.google) == null ? void 0 : _a.maps) == null ? void 0 : _b.drawing) {
      resolve();
      return;
    }
    window.initMap = () => {
      if (googleMapsPromise.resolve) {
        googleMapsPromise.resolve();
      }
    };
    const script = document.createElement("script");
    script.src = `https://maps.googleapis.com/maps/api/js?key=${apiKey}&libraries=drawing,geometry&v=weekly&callback=initMap`;
    script.async = true;
    script.onerror = () => {
      if (googleMapsPromise.reject) {
        googleMapsPromise.reject(new Error("Failed to load Google Maps script"));
      }
    };
    document.head.appendChild(script);
  });
  return googleMapsPromise.promise;
};
function Map({
  center,
  zoom,
  onPolygonComplete,
  apiKey,
  existingCoordinates
}) {
  const ref = reactExports.useRef(null);
  const [isLibraryLoaded, setIsLibraryLoaded] = reactExports.useState(false);
  const mapRef = reactExports.useRef(null);
  const polygonRef = reactExports.useRef(null);
  const drawingManagerRef = reactExports.useRef(null);
  reactExports.useEffect(() => {
    const loadMap = async () => {
      try {
        await loadGoogleMapsScript(apiKey);
        setIsLibraryLoaded(true);
      } catch (error) {
        console.error("Error loading Google Maps:", error);
      }
    };
    loadMap();
  }, [apiKey]);
  const updatePolygon = reactExports.useCallback((coordinates) => {
    if (!mapRef.current || !(google == null ? void 0 : google.maps)) return;
    if (polygonRef.current) {
      polygonRef.current.setMap(null);
    }
    polygonRef.current = new google.maps.Polygon({
      paths: coordinates,
      fillColor: "#4F46E5",
      fillOpacity: 0.3,
      strokeWeight: 2,
      strokeColor: "#4F46E5",
      editable: true,
      draggable: true,
      map: mapRef.current
    });
    google.maps.event.addListener(polygonRef.current.getPath(), "set_at", () => {
      if (!polygonRef.current) return;
      const newCoords = polygonRef.current.getPath().getArray().map((latLng) => ({
        lat: latLng.lat(),
        lng: latLng.lng()
      }));
      onPolygonComplete(newCoords);
    });
    google.maps.event.addListener(polygonRef.current.getPath(), "insert_at", () => {
      if (!polygonRef.current) return;
      const newCoords = polygonRef.current.getPath().getArray().map((latLng) => ({
        lat: latLng.lat(),
        lng: latLng.lng()
      }));
      onPolygonComplete(newCoords);
    });
  }, [onPolygonComplete]);
  reactExports.useEffect(() => {
    if (!isLibraryLoaded || !ref.current || !(google == null ? void 0 : google.maps)) return;
    const map = new google.maps.Map(ref.current, {
      center,
      zoom,
      streetViewControl: false,
      mapTypeControl: false
    });
    mapRef.current = map;
    const bangaloreBounds = new google.maps.LatLngBounds(new google.maps.LatLng(12.864162, 77.43861), new google.maps.LatLng(13.139784, 77.711895));
    map.fitBounds(bangaloreBounds);
    const drawingManager = new google.maps.drawing.DrawingManager({
      drawingMode: google.maps.drawing.OverlayType.POLYGON,
      drawingControl: true,
      drawingControlOptions: {
        position: google.maps.ControlPosition.TOP_CENTER,
        drawingModes: [google.maps.drawing.OverlayType.POLYGON]
      },
      polygonOptions: {
        fillColor: "#4F46E5",
        fillOpacity: 0.3,
        strokeWeight: 2,
        strokeColor: "#4F46E5",
        editable: true,
        draggable: true
      }
    });
    drawingManagerRef.current = drawingManager;
    drawingManager.setMap(map);
    google.maps.event.addListener(drawingManager, "overlaycomplete", (event) => {
      if (event.type === google.maps.drawing.OverlayType.POLYGON) {
        const polygon = event.overlay;
        polygon.setMap(null);
        drawingManager.setDrawingMode(null);
        const coordinates = polygon.getPath().getArray().map((latLng) => ({
          lat: latLng.lat(),
          lng: latLng.lng()
        }));
        updatePolygon(coordinates);
        onPolygonComplete(coordinates);
      }
    });
    if (existingCoordinates && existingCoordinates.length > 0) {
      updatePolygon(existingCoordinates);
    }
    return () => {
      if (drawingManagerRef.current) {
        drawingManagerRef.current.setMap(null);
      }
      if (polygonRef.current) {
        polygonRef.current.setMap(null);
      }
    };
  }, [isLibraryLoaded, center, zoom, onPolygonComplete, existingCoordinates, updatePolygon]);
  return /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
    ref,
    className: "w-full h-96 rounded-lg"
  });
}
function NewLocality() {
  var _a;
  const navigate = useNavigate();
  const submit = useSubmit();
  const {
    states,
    districts,
    googleMapsApiKey
  } = useLoaderData();
  const actionData = useActionData();
  const {
    toast
  } = useToast();
  const [selectedState, setSelectedState] = reactExports.useState("");
  const [selectedDistrict, setSelectedDistrict] = reactExports.useState("");
  const [localityName, setLocalityName] = reactExports.useState("");
  const [showMap, setShowMap] = reactExports.useState(false);
  const [polygonCoordinates, setPolygonCoordinates] = reactExports.useState([]);
  const [isSubmitting, setIsSubmitting] = reactExports.useState(false);
  const handleStateChange = (value) => {
    setSelectedState(value);
    setSelectedDistrict("");
    setShowMap(false);
    setPolygonCoordinates([]);
  };
  const handleDistrictChange = (value) => {
    setSelectedDistrict(value);
    setShowMap(true);
  };
  const handlePolygonComplete = reactExports.useCallback((coordinates) => {
    setPolygonCoordinates(coordinates);
  }, []);
  const handleSave = () => {
    if (!localityName.trim()) {
      toast({
        title: "Error",
        description: "Please enter a locality name",
        variant: "destructive"
      });
      return;
    }
    if (!selectedState || !selectedDistrict) {
      toast({
        title: "Error",
        description: "Please select both state and district",
        variant: "destructive"
      });
      return;
    }
    if (polygonCoordinates.length < 3) {
      toast({
        title: "Error",
        description: "Please draw a valid polygon on the map",
        variant: "destructive"
      });
      return;
    }
    if (polygonCoordinates[0].lat !== polygonCoordinates[polygonCoordinates.length - 1].lat || polygonCoordinates[0].lng !== polygonCoordinates[polygonCoordinates.length - 1].lng) {
      polygonCoordinates.push(polygonCoordinates[0]);
    }
    const path = new google.maps.MVCArray(polygonCoordinates.map((coord) => new google.maps.LatLng(coord.lat, coord.lng)));
    const encodedPolyline = encodeURIComponent(google.maps.geometry.encoding.encodePath(path));
    const formData = new FormData();
    formData.set("name", localityName);
    formData.set("state", selectedState);
    formData.set("district", selectedDistrict);
    formData.set("polygon", encodedPolyline);
    setIsSubmitting(true);
    submit(formData, {
      method: "post"
    });
  };
  reactExports.useEffect(() => {
    if (actionData == null ? void 0 : actionData.error) {
      toast({
        title: "Error",
        description: actionData.error,
        variant: "destructive"
      });
    }
  }, [actionData, toast]);
  return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
    className: "h-[calc(100vh-64px)] bg-white p-6 overflow-y-auto",
    children: [/* @__PURE__ */ jsxRuntimeExports.jsx("div", {
      className: "flex justify-between items-center mb-6",
      children: /* @__PURE__ */ jsxRuntimeExports.jsx("button", {
        onClick: () => navigate("/home/<USER>"),
        className: "p-2 hover:bg-gray-100 rounded-full",
        children: /* @__PURE__ */ jsxRuntimeExports.jsx(ArrowLeft, {
          size: 24
        })
      })
    }), /* @__PURE__ */ jsxRuntimeExports.jsxs(Card, {
      className: "w-full max-w-2xl mx-auto",
      children: [/* @__PURE__ */ jsxRuntimeExports.jsxs(CardHeader, {
        children: [/* @__PURE__ */ jsxRuntimeExports.jsx(CardTitle, {
          children: "Add New Locality"
        }), /* @__PURE__ */ jsxRuntimeExports.jsx(CardDescription, {
          children: "Enter locality details and draw the boundary on the map"
        })]
      }), /* @__PURE__ */ jsxRuntimeExports.jsx(CardContent, {
        children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
          className: "space-y-4",
          children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
            className: "space-y-2",
            children: [/* @__PURE__ */ jsxRuntimeExports.jsx("label", {
              className: "text-sm font-medium",
              children: "Locality Name"
            }), /* @__PURE__ */ jsxRuntimeExports.jsx(Input, {
              placeholder: "Enter locality name",
              value: localityName,
              onChange: (e) => setLocalityName(e.target.value)
            })]
          }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
            className: "space-y-2",
            children: [/* @__PURE__ */ jsxRuntimeExports.jsx("label", {
              className: "text-sm font-medium",
              children: "State"
            }), /* @__PURE__ */ jsxRuntimeExports.jsxs(Select, {
              value: selectedState,
              onValueChange: handleStateChange,
              children: [/* @__PURE__ */ jsxRuntimeExports.jsx(SelectTrigger, {
                children: /* @__PURE__ */ jsxRuntimeExports.jsx(SelectValue, {
                  placeholder: "Select a state"
                })
              }), /* @__PURE__ */ jsxRuntimeExports.jsx(SelectContent, {
                children: states.map((state) => /* @__PURE__ */ jsxRuntimeExports.jsx(SelectItem, {
                  value: state,
                  children: state
                }, state))
              })]
            })]
          }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
            className: "space-y-2",
            children: [/* @__PURE__ */ jsxRuntimeExports.jsx("label", {
              className: "text-sm font-medium",
              children: "District"
            }), /* @__PURE__ */ jsxRuntimeExports.jsxs(Select, {
              value: selectedDistrict,
              onValueChange: handleDistrictChange,
              disabled: !selectedState,
              children: [/* @__PURE__ */ jsxRuntimeExports.jsx(SelectTrigger, {
                children: /* @__PURE__ */ jsxRuntimeExports.jsx(SelectValue, {
                  placeholder: "Select a district"
                })
              }), /* @__PURE__ */ jsxRuntimeExports.jsx(SelectContent, {
                children: selectedState && ((_a = districts[selectedState]) == null ? void 0 : _a.map((district) => /* @__PURE__ */ jsxRuntimeExports.jsx(SelectItem, {
                  value: district,
                  children: district
                }, district)))
              })]
            })]
          }), showMap && /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
            className: "space-y-4",
            children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Map, {
              center: {
                lat: 12.9716,
                lng: 77.5946
              },
              zoom: 11,
              onPolygonComplete: handlePolygonComplete,
              apiKey: googleMapsApiKey,
              existingCoordinates: polygonCoordinates
            }), /* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
              onClick: handleSave,
              disabled: isSubmitting || polygonCoordinates.length < 3 || !localityName.trim(),
              className: "w-full",
              children: isSubmitting ? "Creating..." : "Save Locality"
            })]
          })]
        })
      })]
    })]
  });
}
export {
  NewLocality as default
};
//# sourceMappingURL=home.localities.new-UAyIxODY.js.map
