{"version": 3, "file": "home.buyerManagement-DoYaCCkj.js", "sources": ["../../../app/components/ui/searchBar.tsx", "../../../app/routes/home.buyerManagement.tsx"], "sourcesContent": ["import { useState, useEffect } from \"react\";\r\n\r\ninterface SearchBarProps {\r\n      onSearch: (query: string) => void;\r\n      onCancel: () => void;\r\n      isLoading: boolean;\r\n}\r\n\r\nexport default function SearchBar({ onSearch, onCancel, isLoading }: SearchBarProps) {\r\n      const [query, setQuery] = useState(\"\");\r\n\r\n      useEffect(() => {\r\n            const handler = setTimeout(() => {\r\n                  if (query.trim() !== \"\" && query.length >= 3) {\r\n                        onSearch(query);\r\n                  }\r\n            }, 500);\r\n\r\n            return () => clearTimeout(handler);\r\n      }, [query]);\r\n\r\n      return (\r\n            <div className=\"relative w-full max-w-md mx-auto\">\r\n                  <input\r\n                        type=\"text\"\r\n                        placeholder=\"Search...\"\r\n                        value={query}\r\n                        onChange={(e) => setQuery(e.target.value)}\r\n                        className=\"w-full p-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\r\n                  />\r\n                  {query && (\r\n                        <button\r\n                              onClick={() => {\r\n                                    setQuery(\"\");\r\n                                    onCancel();\r\n                              }}\r\n                              className=\"absolute right-3 top-2 text-gray-600 hover:text-red-500\"\r\n                        >\r\n                              ✕\r\n                        </button>\r\n                  )}\r\n                  {isLoading && <span className=\"absolute right-10 top-2 animate-spin\">⏳</span>}\r\n            </div>\r\n      );\r\n}\r\n", "\r\n\r\nimport { LoaderFunction } from \"@remix-run/node\";\r\nimport { use<PERSON><PERSON><PERSON>, useLoaderData, useNavigate } from \"@remix-run/react\";\r\nimport { BcBuyerDto, } from \"~/types/api/businessConsoleService/salesinfo\";\r\nimport { getBuyerData, updateBuyerAttribute } from \"~/services/salesinfoDetails\";\r\nimport { withAuth, withResponse } from \"@utils/auth-utils\";\r\nimport { useState } from \"react\";\r\nimport { Pagination, PaginationContent, PaginationItem, PaginationLink, PaginationNext, PaginationPrevious } from \"~/components/ui/pagination\";\r\nimport { ResponsiveTable } from \"~/components/ui/responsiveTable\";\r\nimport SearchBar from \"~/components/ui/searchBar\";\r\nimport { Pencil, Save, X } from \"lucide-react\";\r\nimport { Input } from \"~/components/ui/input\";\r\nimport { Switch } from \"~/components/ui/switch\";\r\nimport SpinnerLoader from \"~/components/loader/SpinnerLoader\";\r\n\r\nexport interface LoaderData {\r\n      buyerData: BcBuyerDto[],\r\n}\r\nexport const loader: LoaderFunction = withAuth(async ({ request }) => {\r\n      const url = new URL(request.url);\r\n      const searchBy = url.searchParams.get(\"search\") || \"\"\r\n      const page = Number(url.searchParams.get(\"pageNo\")) || 0;\r\n      const pageSize = Number(url.searchParams.get(\"pageSize\")) || 10;\r\n      try {\r\n            let response = null;\r\n\r\n            response = await getBuyerData(page, pageSize, searchBy, request)\r\n\r\n            return withResponse({ buyerData: response?.data }, response?.headers)\r\n      } catch (error) {\r\n            console.error(\"Seller sales error:\", error);\r\n            throw new Response(\"Failed to fetch seller sales\", { status: 500 });\r\n      }\r\n});\r\nexport const action = withAuth(async ({ request }) => {\r\n      const formData = await request.formData();\r\n      const intent = formData.get(\"_intent\") as string;\r\n\r\n      const buyerId = Number(formData.get(\"buyerId\"));\r\n      if (isNaN(buyerId)) {\r\n            return new Response(\"Invalid buyer ID\", { status: 400 });\r\n      }\r\n\r\n      const AttributeType = formData.get(\"attribute\") as string;\r\n      if (!AttributeType) {\r\n            return new Response(\"Missing attribute type\", { status: 400 });\r\n      }\r\n\r\n      try {\r\n            let response;\r\n            if (intent === \"updateBuyerName\") {\r\n                  const buyerName = formData.get(\"buyerName\") as string;\r\n                  if (!buyerName) return new Response(\"Missing buyer name\", { status: 400 });\r\n\r\n                  response = await updateBuyerAttribute(buyerId, AttributeType, buyerName, request);\r\n            }\r\n            else if (intent === \"updateLatLon\") {\r\n                  const latitude = formData.get(\"latitude\");\r\n                  const longitude = formData.get(\"longitude\");\r\n                  if (!latitude || !longitude) return new Response(\"Missing lat/lon\", { status: 400 });\r\n\r\n                  response = await updateBuyerAttribute(buyerId, AttributeType, `${latitude}, ${longitude}`, request);\r\n            }\r\n            else if (intent === \"updateStatus\") {\r\n                  const status = formData.get(\"status\") as string;\r\n                  if (status === null) return new Response(\"Missing status\", { status: 400 });\r\n\r\n                  response = await updateBuyerAttribute(buyerId, AttributeType, status, request);\r\n            }\r\n            else {\r\n                  return new Response(\"Invalid intent\", { status: 400 });\r\n            }\r\n\r\n            return withResponse({ buyerData: response?.data || {} }, response.headers);\r\n      } catch (error) {\r\n            console.error(\"Update failed:\", error);\r\n            return new Response(\"Failed to update\", { status: 500 });\r\n      }\r\n});\r\n\r\n\r\nexport default function BuyerManagement() {\r\n      const navigate = useNavigate();\r\n      const { buyerData } = useLoaderData<LoaderData>()\r\n      const [currentPage, setCurrentPage] = useState(0)\r\n      const itemsPerPage = 50;\r\n      const [searchTerm, setSearchTerm] = useState('')\r\n      const handlePageChange = (newPage: number) => {\r\n            setCurrentPage(newPage);\r\n            navigate(`?search=${searchTerm}&pageNo=${newPage}&pageSize=${itemsPerPage}`);\r\n      };\r\n      const BuyerManagementHeader = [\r\n            \"BuyerID\",\r\n            \"BuyerName\",\r\n            \"MobileNumber\",\r\n            \"Address\",\r\n            \"Lat/Lan\",\r\n            \"W.Balance\",\r\n            \"Status\",\r\n      ];\r\n\r\n      const fetcher = useFetcher<BcBuyerDto[]>()\r\n      const isLoading = fetcher.state !== \"idle\";\r\n\r\n      const handleSearch = async (query: string) => {\r\n            setSearchTerm(query);\r\n            if (query.length >= 3) {\r\n                  navigate(`?search=${query}&pageNo=${0}&pageSize=${itemsPerPage}`)\r\n\r\n            }\r\n            else if (query.length === 0) {\r\n                  // Reset when query is empty\r\n                  navigate(`?pageNo=${0}&pageSize=${itemsPerPage}`);\r\n                  setSearchTerm(\"\")\r\n\r\n            }\r\n      };\r\n      const handleCancel = () => {\r\n            navigate(`?&pageNo=${0}&pageSize=${itemsPerPage}`)\r\n            setSearchTerm(\"\")\r\n\r\n      };\r\n\r\n      const [updateBuyerName, setUpdateBuyerName] = useState<{ [key: number]: string }>({});\r\n      const [isBuyerUpdate, SetIsBuyerUpdate] = useState<{ [key: number]: boolean }>({});\r\n\r\n      const handleUpdateName = (buyerId: number, val: string) => {\r\n            setUpdateBuyerName((prev) => ({ ...prev, [buyerId]: val }));\r\n      };\r\n      const [updateLatLon, setUpdateLatLon] = useState<{ [key: number]: { lat: string, lon: string } }>({});\r\n      const [isBuyerLatLonUpdate, setIsBuyerLatLonUpdate] = useState<{ [key: number]: boolean }>({});\r\n      const handleSave = (buyerId: number,) => {\r\n            const formData = new FormData()\r\n            formData.append(\"_intent\", \"updateBuyerName\");\r\n            formData.append(\"buyerId\", buyerId.toString());\r\n            formData.append(\"buyerName\", updateBuyerName[buyerId]);\r\n            formData.append(\"attribute\", \"name\")\r\n            fetcher.submit(formData, { method: \"put\" })\r\n\r\n\r\n            SetIsBuyerUpdate((prev) => ({ ...prev, [buyerId]: false }));\r\n      };\r\n      const [buyerStatus, setBuyerStatus] = useState<{ [key: number]: boolean }>({});\r\n      const handleUpdateLatLon = (buyerId: number, field: \"lat\" | \"lon\", val: string) => {\r\n            setUpdateLatLon((prev) => ({\r\n                  ...prev,\r\n                  [buyerId]: { ...prev[buyerId], [field]: val }\r\n            }));\r\n      };\r\n      const handleSaveLatLon = (buyerId: number) => {\r\n            const latLon = updateLatLon[buyerId];\r\n            if (!latLon?.lat || !latLon?.lon) return;\r\n            const formData = new FormData();\r\n            formData.append(\"_intent\", \"updateLatLon\");\r\n            formData.append(\"buyerId\", buyerId.toString());\r\n            formData.append(\"latitude\", latLon.lat);\r\n            formData.append(\"longitude\", latLon.lon);\r\n            formData.append(\"attribute\", \"latlong\");\r\n\r\n            fetcher.submit(formData, { method: \"put\" });\r\n            setIsBuyerLatLonUpdate((prev) => ({ ...prev, [buyerId]: false }));\r\n      };\r\n      const handleToggleStatus = (buyerId: number, currentStatus: boolean) => {\r\n            const newStatus = !currentStatus;\r\n            setBuyerStatus((prev) => ({ ...prev, [buyerId]: newStatus }));\r\n            const formData = new FormData();\r\n            formData.append(\"_intent\", \"updateStatus\");\r\n            formData.append(\"buyerId\", buyerId.toString());\r\n            formData.append(\"status\", newStatus.toString()); // Convert boolean to string\r\n            formData.append(\"attribute\", \"status\");\r\n\r\n            fetcher.submit(formData, { method: \"put\" });\r\n      };\r\n      return (\r\n            <div className=\"container mx-auto w-full p-6\">\r\n                  <div className=\"flex flex-row justify-between items-center\">\r\n                        <div className=\" items-center my-3\">\r\n                              <h1 className=\"text-2xl font-bold\">BuyerManagement</h1>\r\n                        </div>\r\n                        <div className=\"flex my-3 w-full justify-start\">\r\n                              <SearchBar onSearch={handleSearch} onCancel={handleCancel} isLoading={isLoading} />\r\n                        </div>\r\n\r\n                  </div>\r\n                  <div>\r\n                        <SpinnerLoader loading={isLoading} />\r\n                        <ResponsiveTable\r\n                              headers={BuyerManagementHeader}\r\n                              data={buyerData\r\n                              }\r\n                              renderRow={(row) => (\r\n                                    <tr key={row.buyerId} className=\"border-b\">\r\n                                          <td className=\"py-2 px-3 font-medium text-center\">{row.buyerId}</td>\r\n                                          <td className=\"py-2 px-3 cursor-pointer text-center w-full\">\r\n\r\n                                                {isBuyerUpdate[row.buyerId] ? <>\r\n                                                      <div className=\"flex flex-row gap-2 justify-center \">\r\n                                                            <Input\r\n                                                                  type=\"text\"\r\n                                                                  value={updateBuyerName[row.buyerId] ?? row.name}\r\n                                                                  onChange={(e) => handleUpdateName(row.buyerId, e.target.value)}\r\n                                                                  className=\" px-2 py-1 border border-gray-300 rounded-md\"\r\n\r\n                                                            />\r\n\r\n                                                            <Save size={24} onClick={() => handleSave(row.buyerId)} />\r\n                                                            <X\r\n                                                                  color=\"red\"\r\n                                                                  size={24}\r\n                                                                  className=\"cursor-pointer text-red-500\"\r\n                                                                  onClick={() => SetIsBuyerUpdate({})} // Close all inputs\r\n                                                            />\r\n                                                      </div>\r\n\r\n\r\n                                                </> : <div className=\"flex flex-row gap-2 items-center justify-center\">\r\n                                                      {row.name || \"-\"}\r\n                                                      <Pencil size={15} onClick={() => SetIsBuyerUpdate({ [row.buyerId]: true })} />\r\n                                                </div>}\r\n\r\n\r\n                                          </td>\r\n                                          <td className=\"py-2 px-3 text-center\">\r\n                                                {row?.mobileNumber || \"-\"}\r\n                                          </td>\r\n                                          <td className=\"py-2 px-3 text-center\">\r\n                                                {row?.address || \"-\"}\r\n                                          </td>\r\n                                          <td className=\"py-2 px-3 text-center w-40\">\r\n                                                {isBuyerLatLonUpdate[row.buyerId] ? (\r\n                                                      <div className=\"flex flex-row gap-2 justify-center items-center\">\r\n                                                            <Input\r\n                                                                  type=\"text\"\r\n                                                                  value={updateLatLon[row.buyerId]?.lat ?? row.latitude}\r\n                                                                  onChange={(e) => handleUpdateLatLon(row.buyerId, \"lat\", e.target.value)}\r\n                                                                  className=\"w-20 px-2 py-1 border border-gray-300 rounded-md\"\r\n                                                                  placeholder=\"Lat\"\r\n                                                            />\r\n                                                            <Input\r\n                                                                  type=\"text\"\r\n                                                                  value={updateLatLon[row.buyerId]?.lon ?? row.longitude}\r\n                                                                  onChange={(e) => handleUpdateLatLon(row.buyerId, \"lon\", e.target.value)}\r\n                                                                  className=\"w-20 px-2 py-1 border border-gray-300 rounded-md\"\r\n                                                                  placeholder=\"Lon\"\r\n                                                            />\r\n                                                            <Save size={24} onClick={() => handleSaveLatLon(row.buyerId)} />\r\n                                                            <X color=\"red\" size={24} className=\"cursor-pointer text-red-500\"\r\n                                                                  onClick={() => setIsBuyerLatLonUpdate({})} />\r\n                                                      </div>\r\n                                                ) : (\r\n                                                      <div className=\"flex flex-row gap-2 justify-center items-center\">\r\n                                                            {row.latitude}, {row.longitude}\r\n                                                            <Pencil size={24} onClick={() => setIsBuyerLatLonUpdate({ [row.buyerId]: true })} className=\"cursor-pointer\" />\r\n                                                      </div>\r\n                                                )}\r\n                                          </td>\r\n\r\n\r\n                                          <td className=\"py-2 px-3 text-center\">\r\n                                                {row.walletBalance > 0 ? row?.walletBalance.toFixed(1) : \"-\"}\r\n                                          </td>\r\n\r\n                                          <td className=\"py-2 px-3 text-center\">\r\n                                                <div className=\"flex items-center justify-center space-x-2\">\r\n                                                      <Switch\r\n                                                            checked={!(buyerStatus[row.buyerId] || row.status)}\r\n                                                            onCheckedChange={() => handleToggleStatus(row.buyerId, buyerStatus[row.buyerId])}\r\n                                                      />\r\n\r\n\r\n                                                </div>\r\n                                          </td>\r\n\r\n\r\n                                    </tr>\r\n                              )\r\n                              }\r\n                              emptyMessage=\"No data available for the selected filters.\"\r\n                        />\r\n                  </div>\r\n                  <div className=\"flex justify-center items-center mt-6\">\r\n                        <Pagination className=\"flex items-center space-x-2 bg-white shadow-md rounded-lg px-4 py-2\">\r\n                              <PaginationContent className=\"flex items-center space-x-2\">\r\n                                    {currentPage > 0 && (\r\n                                          <PaginationItem>\r\n                                                <PaginationPrevious\r\n                                                      onClick={() => handlePageChange(currentPage - 1)}\r\n                                                      className=\"px-3 py-1 bg-gray-200 hover:bg-gray-300 text-gray-700 rounded-md transition cursor-pointer\"\r\n                                                />\r\n                                          </PaginationItem>\r\n                                    )}\r\n                                    <PaginationItem>\r\n                                          <PaginationLink className=\"px-4 py-1 bg-blue-500 text-white rounded-md shadow-md\">\r\n                                                {currentPage + 1}\r\n                                          </PaginationLink>\r\n                                    </PaginationItem>\r\n                                    <PaginationItem>\r\n                                          <PaginationNext\r\n                                                onClick={() => handlePageChange(currentPage + 1)}\r\n                                                className=\"px-3 py-1 bg-gray-200 hover:bg-gray-300 text-gray-700 rounded-md transition cursor-pointer\"\r\n                                          />\r\n                                    </PaginationItem>\r\n                              </PaginationContent>\r\n                        </Pagination>\r\n                  </div>\r\n\r\n            </div >\r\n\r\n      );\r\n}\r\n"], "names": ["useState", "useEffect", "jsxs", "jsx", "BuyerManagement", "navigate", "useNavigate", "buyerData", "useLoaderData", "currentPage", "setCurrentPage", "itemsPerPage", "searchTerm", "setSearchTerm", "handlePageChange", "newPage", "BuyerManagementHeader", "fetcher", "useFetcher", "isLoading", "state", "handleSearch", "query", "length", "handleCancel", "updateBuyerName", "setUpdateBuyerName", "isBuyerUpdate", "SetIsBuyerUpdate", "handleUpdateName", "buyerId", "val", "prev", "updateLatLon", "setUpdateLatLon", "isBuyerLatLonUpdate", "setIsBuyerLatLonUpdate", "handleSave", "formData", "FormData", "append", "toString", "submit", "method", "buyerStatus", "setBuyerStatus", "handleUpdateLatLon", "field", "handleSaveLatLon", "latLon", "lat", "lon", "handleToggleStatus", "currentStatus", "newStatus", "className", "children", "SearchBar", "onSearch", "onCancel", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "loading", "ResponsiveTable", "headers", "data", "renderRow", "row", "Fragment", "Input", "type", "value", "name", "onChange", "e", "target", "Save", "size", "onClick", "X", "color", "Pencil", "mobileNumber", "address", "latitude", "placeholder", "longitude", "walletBalance", "toFixed", "Switch", "checked", "status", "onCheckedChange", "emptyMessage", "Pagination", "Pa<PERSON><PERSON><PERSON><PERSON><PERSON>", "PaginationItem", "PaginationPrevious", "PaginationLink", "PaginationNext"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAQA,SAAwB,UAAU,EAAE,UAAU,UAAU,aAA6B;AAC/E,QAAM,CAAC,OAAO,QAAQ,IAAIA,aAAAA,SAAS,EAAE;AAErCC,eAAAA,UAAU,MAAM;AACJ,UAAA,UAAU,WAAW,MAAM;AAC3B,UAAI,MAAM,KAAK,MAAM,MAAM,MAAM,UAAU,GAAG;AACxC,iBAAS,KAAK;AAAA,MAAA;AAAA,OAEvB,GAAG;AAEC,WAAA,MAAM,aAAa,OAAO;AAAA,EAAA,GACpC,CAAC,KAAK,CAAC;AAGJ,SAAAC,kCAAA,KAAC,OAAI,EAAA,WAAU,oCACT,UAAA;AAAA,IAAAC,kCAAA;AAAA,MAAC;AAAA,MAAA;AAAA,QACK,MAAK;AAAA,QACL,aAAY;AAAA,QACZ,OAAO;AAAA,QACP,UAAU,CAAC,MAAM,SAAS,EAAE,OAAO,KAAK;AAAA,QACxC,WAAU;AAAA,MAAA;AAAA,IAChB;AAAA,IACC,SACKA,kCAAA;AAAA,MAAC;AAAA,MAAA;AAAA,QACK,SAAS,MAAM;AACT,mBAAS,EAAE;AACF,mBAAA;AAAA,QACf;AAAA,QACA,WAAU;AAAA,QACf,UAAA;AAAA,MAAA;AAAA,IAED;AAAA,IAEL,aAAaA,kCAAA,IAAC,QAAK,EAAA,WAAU,wCAAuC,UAAC,IAAA,CAAA;AAAA,EAAA,GAC5E;AAEZ;ACsCA,SAAwBC,kBAAkB;AACpC,QAAMC,WAAWC,YAAY;AACvB,QAAA;AAAA,IAAEC;AAAAA,EAAU,IAAIC,cAA0B;AAChD,QAAM,CAACC,aAAaC,cAAc,IAAIV,aAAAA,SAAS,CAAC;AAChD,QAAMW,eAAe;AACrB,QAAM,CAACC,YAAYC,aAAa,IAAIb,aAAAA,SAAS,EAAE;AACzC,QAAAc,mBAAoBC,aAAoB;AACxCL,mBAAeK,OAAO;AACtBV,aAAS,WAAWO,UAAU,WAAWG,OAAO,aAAaJ,YAAY,EAAE;AAAA,EACjF;AACA,QAAMK,wBAAwB,CACxB,WACA,aACA,gBACA,WACA,WACA,aACA,QAAA;AAGN,QAAMC,UAAUC,WAAyB;AACnC,QAAAC,YAAYF,QAAQG,UAAU;AAE9B,QAAAC,eAAe,OAAOC,UAAkB;AACxCT,kBAAcS,KAAK;AACf,QAAAA,MAAMC,UAAU,GAAG;AACjBlB,eAAS,WAAWiB,KAAK,WAAW,CAAC,aAAaX,YAAY,EAAE;AAAA,IAEtE,WACSW,MAAMC,WAAW,GAAG;AAEvBlB,eAAS,WAAW,CAAC,aAAaM,YAAY,EAAE;AAChDE,oBAAc,EAAE;AAAA,IAEtB;AAAA,EACN;AACA,QAAMW,eAAeA,MAAM;AACrBnB,aAAS,YAAY,CAAC,aAAaM,YAAY,EAAE;AACjDE,kBAAc,EAAE;AAAA,EAEtB;AAEA,QAAM,CAACY,iBAAiBC,kBAAkB,IAAI1B,aAAAA,SAAoC,CAAA,CAAE;AACpF,QAAM,CAAC2B,eAAeC,gBAAgB,IAAI5B,aAAAA,SAAqC,CAAA,CAAE;AAE3E,QAAA6B,mBAAmBA,CAACC,SAAiBC,QAAgB;AAClCL,uBAACM,WAAU;AAAA,MAAE,GAAGA;AAAAA,MAAM,CAACF,OAAO,GAAGC;AAAAA,IAAI,EAAE;AAAA,EAChE;AACA,QAAM,CAACE,cAAcC,eAAe,IAAIlC,aAAAA,SAA0D,CAAA,CAAE;AACpG,QAAM,CAACmC,qBAAqBC,sBAAsB,IAAIpC,aAAAA,SAAqC,CAAA,CAAE;AACvF,QAAAqC,aAAcP,aAAqB;AAC7B,UAAAQ,WAAW,IAAIC,SAAS;AACrBD,aAAAE,OAAO,WAAW,iBAAiB;AAC5CF,aAASE,OAAO,WAAWV,QAAQW,SAAA,CAAU;AAC7CH,aAASE,OAAO,aAAaf,gBAAgBK,OAAO,CAAC;AAC5CQ,aAAAE,OAAO,aAAa,MAAM;AACnCvB,YAAQyB,OAAOJ,UAAU;AAAA,MAAEK,QAAQ;AAAA,IAAM,CAAC;AAGzBf,qBAACI,WAAU;AAAA,MAAE,GAAGA;AAAAA,MAAM,CAACF,OAAO,GAAG;AAAA,IAAM,EAAE;AAAA,EAChE;AACA,QAAM,CAACc,aAAaC,cAAc,IAAI7C,aAAAA,SAAqC,CAAA,CAAE;AAC7E,QAAM8C,qBAAqBA,CAAChB,SAAiBiB,OAAsBhB,QAAgB;AAC7EG,oBAAiBF,WAAU;AAAA,MACrB,GAAGA;AAAAA,MACH,CAACF,OAAO,GAAG;AAAA,QAAE,GAAGE,KAAKF,OAAO;AAAA,QAAG,CAACiB,KAAK,GAAGhB;AAAAA,MAAI;AAAA,IAClD,EAAE;AAAA,EACR;AACM,QAAAiB,mBAAoBlB,aAAoB;AAClC,UAAAmB,SAAShB,aAAaH,OAAO;AACnC,QAAI,EAACmB,iCAAQC,QAAO,EAACD,iCAAQE,KAAK;AAC5B,UAAAb,WAAW,IAAIC,SAAS;AACrBD,aAAAE,OAAO,WAAW,cAAc;AACzCF,aAASE,OAAO,WAAWV,QAAQW,SAAA,CAAU;AACpCH,aAAAE,OAAO,YAAYS,OAAOC,GAAG;AAC7BZ,aAAAE,OAAO,aAAaS,OAAOE,GAAG;AAC9Bb,aAAAE,OAAO,aAAa,SAAS;AAEtCvB,YAAQyB,OAAOJ,UAAU;AAAA,MAAEK,QAAQ;AAAA,IAAM,CAAC;AACnBP,2BAACJ,WAAU;AAAA,MAAE,GAAGA;AAAAA,MAAM,CAACF,OAAO,GAAG;AAAA,IAAM,EAAE;AAAA,EACtE;AACM,QAAAsB,qBAAqBA,CAACtB,SAAiBuB,kBAA2B;AAClE,UAAMC,YAAY,CAACD;AACJR,mBAACb,WAAU;AAAA,MAAE,GAAGA;AAAAA,MAAM,CAACF,OAAO,GAAGwB;AAAAA,IAAU,EAAE;AACtD,UAAAhB,WAAW,IAAIC,SAAS;AACrBD,aAAAE,OAAO,WAAW,cAAc;AACzCF,aAASE,OAAO,WAAWV,QAAQW,SAAA,CAAU;AAC7CH,aAASE,OAAO,UAAUc,UAAUb,SAAA,CAAU;AACrCH,aAAAE,OAAO,aAAa,QAAQ;AAErCvB,YAAQyB,OAAOJ,UAAU;AAAA,MAAEK,QAAQ;AAAA,IAAM,CAAC;AAAA,EAChD;AAEM,SAAAzC,kCAAAA,KAAC,OAAI;AAAA,IAAAqD,WAAU;AAAA,IACTC,UAAA,CAACtD,kCAAA,KAAA,OAAA;AAAA,MAAIqD,WAAU;AAAA,MACTC,UAAA,CAACrD,kCAAA,IAAA,OAAA;AAAA,QAAIoD,WAAU;AAAA,QACTC,UAAArD,kCAAA,IAAC;UAAGoD,WAAU;AAAA,UAAqBC;QAAe,CAAA;AAAA,MACxD,CAAA,GACArD,kCAAA,IAAC,OAAI;AAAA,QAAAoD,WAAU;AAAA,QACTC,UAAArD,kCAAA,IAACsD,WAAU;AAAA,UAAAC,UAAUrC;AAAAA,UAAcsC,UAAUnC;AAAAA,UAAcL;AAAAA,QAAsB,CAAA;AAAA,MACvF,CAAA,CAAA;AAAA,IAEN,CAAA,0CACC,OACK;AAAA,MAAAqC,UAAA,CAACrD,kCAAA,IAAAyD,eAAA;AAAA,QAAcC,SAAS1C;AAAAA,MAAW,CAAA,GACnChB,kCAAA,IAAC2D,iBAAA;AAAA,QACKC,SAAS/C;AAAAA,QACTgD,MAAMzD;AAAAA,QAEN0D,WAAYC;;AACLhE,mDAAA,KAAA,MAAA;AAAA,YAAqBqD,WAAU;AAAA,YAC1BC,UAAA,CAAArD,kCAAA,IAAC,MAAG;AAAA,cAAAoD,WAAU;AAAA,cAAqCC,UAAAU,IAAIpC;AAAAA,YAAQ,CAAA,GAC9D3B,kCAAA,IAAA,MAAA;AAAA,cAAGoD,WAAU;AAAA,cAEPC,UAAc7B,cAAAuC,IAAIpC,OAAO,IACpB3B,kCAAAA,IAAAgE,kBAAAA,UAAA;AAAA,gBAAAX,UAAAtD,kCAAA,KAAC,OAAI;AAAA,kBAAAqD,WAAU;AAAA,kBACTC,UAAA,CAAArD,kCAAA,IAACiE,OAAA;AAAA,oBACKC,MAAK;AAAA,oBACLC,OAAO7C,gBAAgByC,IAAIpC,OAAO,KAAKoC,IAAIK;AAAAA,oBAC3CC,UAAWC,OAAM5C,iBAAiBqC,IAAIpC,SAAS2C,EAAEC,OAAOJ,KAAK;AAAA,oBAC7Df,WAAU;AAAA,kBAAA,CAEhB,GAEApD,kCAAA,IAACwE;oBAAKC,MAAM;AAAA,oBAAIC,SAASA,MAAMxC,WAAW6B,IAAIpC,OAAO;AAAA,kBAAG,CAAA,GACxD3B,kCAAA,IAAC2E,GAAA;AAAA,oBACKC,OAAM;AAAA,oBACNH,MAAM;AAAA,oBACNrB,WAAU;AAAA,oBACVsB,SAASA,MAAMjD,iBAAiB,CAAE,CAAA;AAAA,kBAAA,CACxC,CAAA;AAAA,gBACN,CAAA;AAAA,cAGN,CAAA,IAAO1B,kCAAA,KAAA,OAAA;AAAA,gBAAIqD,WAAU;AAAA,gBACdC,UAAA,CAAAU,IAAIK,QAAQ,KACZpE,kCAAAA,IAAA6E,QAAA;AAAA,kBAAOJ,MAAM;AAAA,kBAAIC,SAASA,MAAMjD,iBAAiB;AAAA,oBAAE,CAACsC,IAAIpC,OAAO,GAAG;AAAA,kBAAM,CAAA;AAAA,gBAAG,CAAA,CAAA;AAAA,cAClF,CAAA;AAAA,YAGN,CAAA,yCACC,MAAG;AAAA,cAAAyB,WAAU;AAAA,cACPC,WAAAU,2BAAKe,iBAAgB;AAAA,YAC5B,CAAA,yCACC,MAAG;AAAA,cAAA1B,WAAU;AAAA,cACPC,WAAAU,2BAAKgB,YAAW;AAAA,YACvB,CAAA,GACA/E,kCAAA,IAAC,MAAG;AAAA,cAAAoD,WAAU;AAAA,cACPC,UAAArB,oBAAoB+B,IAAIpC,OAAO,IAC1B5B,kCAAAA,KAAC,OAAI;AAAA,gBAAAqD,WAAU;AAAA,gBACTC,UAAA,CAAArD,kCAAA,IAACiE,OAAA;AAAA,kBACKC,MAAK;AAAA,kBACLC,SAAOrC,kBAAaiC,IAAIpC,OAAO,MAAxBG,mBAA2BiB,QAAOgB,IAAIiB;AAAAA,kBAC7CX,UAAWC,OAAM3B,mBAAmBoB,IAAIpC,SAAS,OAAO2C,EAAEC,OAAOJ,KAAK;AAAA,kBACtEf,WAAU;AAAA,kBACV6B,aAAY;AAAA,gBAAA,CAClB,GACAjF,kCAAA,IAACiE,OAAA;AAAA,kBACKC,MAAK;AAAA,kBACLC,SAAOrC,kBAAaiC,IAAIpC,OAAO,MAAxBG,mBAA2BkB,QAAOe,IAAImB;AAAAA,kBAC7Cb,UAAWC,OAAM3B,mBAAmBoB,IAAIpC,SAAS,OAAO2C,EAAEC,OAAOJ,KAAK;AAAA,kBACtEf,WAAU;AAAA,kBACV6B,aAAY;AAAA,gBAAA,CAClB,GACAjF,kCAAA,IAACwE;kBAAKC,MAAM;AAAA,kBAAIC,SAASA,MAAM7B,iBAAiBkB,IAAIpC,OAAO;AAAA,gBAAG,CAAA,GAC9D3B,kCAAA,IAAC2E,GAAA;AAAA,kBAAEC,OAAM;AAAA,kBAAMH,MAAM;AAAA,kBAAIrB,WAAU;AAAA,kBAC7BsB,SAASA,MAAMzC,uBAAuB,CAAE,CAAA;AAAA,gBAAA,CAAG,CAAA;AAAA,cAAA,CACvD,IAEAlC,kCAAA,KAAC,OAAI;AAAA,gBAAAqD,WAAU;AAAA,gBACRC,UAAA,CAAIU,IAAAiB,UAAS,MAAGjB,IAAImB,iDACpBL,QAAO;AAAA,kBAAAJ,MAAM;AAAA,kBAAIC,SAASA,MAAMzC,uBAAuB;AAAA,oBAAE,CAAC8B,IAAIpC,OAAO,GAAG;AAAA,kBAAK,CAAC;AAAA,kBAAGyB,WAAU;AAAA,gBAAiB,CAAA,CAAA;AAAA,cACnH,CAAA;AAAA,YAEZ,CAAA,GAGCpD,kCAAA,IAAA,MAAA;AAAA,cAAGoD,WAAU;AAAA,cACPC,UAAIU,IAAAoB,gBAAgB,IAAIpB,2BAAKoB,cAAcC,QAAQ,KAAK;AAAA,YAC/D,CAAA,yCAEC,MAAG;AAAA,cAAAhC,WAAU;AAAA,cACRC,UAACrD,kCAAA,IAAA,OAAA;AAAA,gBAAIoD,WAAU;AAAA,gBACTC,UAAArD,kCAAA,IAACqF,QAAA;AAAA,kBACKC,SAAS,EAAE7C,YAAYsB,IAAIpC,OAAO,KAAKoC,IAAIwB;AAAAA,kBAC3CC,iBAAiBA,MAAMvC,mBAAmBc,IAAIpC,SAASc,YAAYsB,IAAIpC,OAAO,CAAC;AAAA,gBACrF,CAAA;AAAA,cAGN,CAAA;AAAA,YACN,CAAA,CAAA;AAAA,UAAA,GAhFGoC,IAAIpC,OAmFb;AAAA;AAAA,QAGN8D,cAAa;AAAA,MAAA,CACnB,CAAA;AAAA,IACN,CAAA,GACAzF,kCAAA,IAAC,OAAI;AAAA,MAAAoD,WAAU;AAAA,MACTC,UAAArD,kCAAA,IAAC0F,YAAW;AAAA,QAAAtC,WAAU;AAAA,QAChBC,UAAAtD,kCAAA,KAAC4F,mBAAkB;AAAA,UAAAvC,WAAU;AAAA,UACtBC,UAAA,CAAc/C,cAAA,2CACRsF,gBACK;AAAA,YAAAvC,UAAArD,kCAAA,IAAC6F,oBAAA;AAAA,cACKnB,SAASA,MAAM/D,iBAAiBL,cAAc,CAAC;AAAA,cAC/C8C,WAAU;AAAA,YAChB,CAAA;AAAA,UACN,CAAA,GAENpD,kCAAA,IAAC4F;YACKvC,UAACrD,kCAAA,IAAA8F,gBAAA;AAAA,cAAe1C,WAAU;AAAA,cACnBC,UAAA/C,cAAc;AAAA,YACrB,CAAA;AAAA,UACN,CAAA,yCACCsF,gBACK;AAAA,YAAAvC,UAAArD,kCAAA,IAAC+F,gBAAA;AAAA,cACKrB,SAASA,MAAM/D,iBAAiBL,cAAc,CAAC;AAAA,cAC/C8C,WAAU;AAAA,YAChB,CAAA;AAAA,UACN,CAAA,CAAA;AAAA,QACN,CAAA;AAAA,MACN,CAAA;AAAA,IACN,CAAA,CAAA;AAAA,EAEN,CAAA;AAGZ;"}