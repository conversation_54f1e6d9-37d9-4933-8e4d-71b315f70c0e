var configKeysEnum = /* @__PURE__ */ ((configKeysEnum2) => {
  configKeysEnum2["discount"] = "discountAdditional";
  configKeysEnum2["filter"] = "filterAdditional";
  configKeysEnum2["validity"] = "validityAdditional";
  return configKeysEnum2;
})(configKeysEnum || {});
const DISCOUNT_UNIT_MAP = {
  item: "unit",
  amount: "/-",
  discountFixed: "/-",
  maxDiscountAmount: "/-",
  minDiscountAmount: "/-",
  percentage: "%",
  discountPercent: "%",
  weight: "gm",
  volume: "ltr"
};
export {
  DISCOUNT_UNIT_MAP as D,
  configKeysEnum as c
};
//# sourceMappingURL=coupons-sX9zH31d.js.map
