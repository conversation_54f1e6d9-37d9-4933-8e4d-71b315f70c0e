import { R as React, r as reactExports, j as jsxRuntimeExports } from "./jsx-runtime-bB2y7OuD.js";
import { B as Button } from "./button-ByAXMyvk.js";
import { C as Card, a as CardContent, b as CardHeader, c as CardTitle, d as CardDescription } from "./card-BJQMSLe_.js";
import { I as Input } from "./input-3v87qohQ.js";
import { S as Select, a as SelectTrigger, b as SelectValue, c as SelectContent, d as SelectItem } from "./select-BFGSXKcr.js";
import { D as Dialog, d as DialogTrigger, a as DialogContent, c as DialogHeader, b as DialogTitle } from "./dialog-BqKosxNq.js";
import { u as useToast } from "./use-toast-EUd7m8UG.js";
import { T as Tabs, a as TabsList, b as TabsTrigger, c as TabsContent } from "./tabs-CfSdyzWr.js";
import { T as Table, a as TableHeader, b as TableRow, c as TableHead, d as TableBody, e as TableCell } from "./table-CAxdJZsY.js";
import { S as SpinnerLoader } from "./SpinnerLoader-CAZRmIJX.js";
import { i as isFunction, D as Dot, f as findAllByType, E as ErrorBar, b as Layer, c as filterProps, d as Curve, A as Animate, e as interpolateNumber, g as isEqual, h as isNil, j as hasClipDot, k as LabelList, l as getValueByDataKey, u as uniqueId, G as Global, m as getCateCoordinateOfLine, n as generateCategoricalChart, X as XAxis, Y as YAxis, o as formatAxisMap, R as ResponsiveContainer, C as CartesianGrid, T as Tooltip, B as BarChart, a as Bar } from "./BarChart-DYWu-Gnb.js";
import { a as clsx } from "./utils-GkgzjW3c.js";
import { f as formatCurrency } from "./format-Da3JpRMs.js";
import { u as useLoaderData, a as useFetcher, b as useSearchParams } from "./components-D7UvGag_.js";
import { I as Info, A as ArrowUp, a as ArrowDown } from "./info-WIlFwAxF.js";
import { S as Save } from "./save-xzNIILKr.js";
import { X } from "./x-CCG_WJDF.js";
import { P as Pencil } from "./pencil-C1goHmP8.js";
import { U as User } from "./user-C2IWJPnC.js";
import { P as Phone } from "./phone-HnpbSr97.js";
import { M as MapPin } from "./map-pin-BWBTUiG2.js";
import "./index-z_byfFrQ.js";
import "./index-ImHKLo0a.js";
import "./index-Vp2vNLNM.js";
import "./index-IXOTxK3N.js";
import "./index-D7VH9Fc8.js";
import "./index-qCtcpOcW.js";
import "./index-BTdCMChR.js";
import "./index-BXzPK7u0.js";
import "./index-CzZ6EYkN.js";
import "./index-CEHS9zzk.js";
import "./index-DVTNuYOr.js";
import "./index-DscYByPT.js";
import "./index-CkL5tk39.js";
import "./index-dIGjFc0I.js";
import "./index-CpKiYcZd.js";
import "./index-C88PRvfd.js";
import "./chevron-down-pCP5jmjX.js";
import "./createLucideIcon-uwkRm45G.js";
import "./check-_dbWxIzT.js";
import "./index-DdafHWkt.js";
import "./index-QLGF6kQx.js";
import "./index-CG37gmC0.js";
import "./index-DhHTcibu.js";
var _excluded = ["type", "layout", "connectNulls", "ref"], _excluded2 = ["key"];
function _typeof(o) {
  "@babel/helpers - typeof";
  return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(o2) {
    return typeof o2;
  } : function(o2) {
    return o2 && "function" == typeof Symbol && o2.constructor === Symbol && o2 !== Symbol.prototype ? "symbol" : typeof o2;
  }, _typeof(o);
}
function _objectWithoutProperties(source, excluded) {
  if (source == null) return {};
  var target = _objectWithoutPropertiesLoose(source, excluded);
  var key, i;
  if (Object.getOwnPropertySymbols) {
    var sourceSymbolKeys = Object.getOwnPropertySymbols(source);
    for (i = 0; i < sourceSymbolKeys.length; i++) {
      key = sourceSymbolKeys[i];
      if (excluded.indexOf(key) >= 0) continue;
      if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;
      target[key] = source[key];
    }
  }
  return target;
}
function _objectWithoutPropertiesLoose(source, excluded) {
  if (source == null) return {};
  var target = {};
  for (var key in source) {
    if (Object.prototype.hasOwnProperty.call(source, key)) {
      if (excluded.indexOf(key) >= 0) continue;
      target[key] = source[key];
    }
  }
  return target;
}
function _extends() {
  _extends = Object.assign ? Object.assign.bind() : function(target) {
    for (var i = 1; i < arguments.length; i++) {
      var source = arguments[i];
      for (var key in source) {
        if (Object.prototype.hasOwnProperty.call(source, key)) {
          target[key] = source[key];
        }
      }
    }
    return target;
  };
  return _extends.apply(this, arguments);
}
function ownKeys(e, r) {
  var t = Object.keys(e);
  if (Object.getOwnPropertySymbols) {
    var o = Object.getOwnPropertySymbols(e);
    r && (o = o.filter(function(r2) {
      return Object.getOwnPropertyDescriptor(e, r2).enumerable;
    })), t.push.apply(t, o);
  }
  return t;
}
function _objectSpread(e) {
  for (var r = 1; r < arguments.length; r++) {
    var t = null != arguments[r] ? arguments[r] : {};
    r % 2 ? ownKeys(Object(t), true).forEach(function(r2) {
      _defineProperty(e, r2, t[r2]);
    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function(r2) {
      Object.defineProperty(e, r2, Object.getOwnPropertyDescriptor(t, r2));
    });
  }
  return e;
}
function _toConsumableArray(arr) {
  return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread();
}
function _nonIterableSpread() {
  throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
}
function _unsupportedIterableToArray(o, minLen) {
  if (!o) return;
  if (typeof o === "string") return _arrayLikeToArray(o, minLen);
  var n = Object.prototype.toString.call(o).slice(8, -1);
  if (n === "Object" && o.constructor) n = o.constructor.name;
  if (n === "Map" || n === "Set") return Array.from(o);
  if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);
}
function _iterableToArray(iter) {
  if (typeof Symbol !== "undefined" && iter[Symbol.iterator] != null || iter["@@iterator"] != null) return Array.from(iter);
}
function _arrayWithoutHoles(arr) {
  if (Array.isArray(arr)) return _arrayLikeToArray(arr);
}
function _arrayLikeToArray(arr, len) {
  if (len == null || len > arr.length) len = arr.length;
  for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i];
  return arr2;
}
function _classCallCheck(instance, Constructor) {
  if (!(instance instanceof Constructor)) {
    throw new TypeError("Cannot call a class as a function");
  }
}
function _defineProperties(target, props) {
  for (var i = 0; i < props.length; i++) {
    var descriptor = props[i];
    descriptor.enumerable = descriptor.enumerable || false;
    descriptor.configurable = true;
    if ("value" in descriptor) descriptor.writable = true;
    Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor);
  }
}
function _createClass(Constructor, protoProps, staticProps) {
  if (protoProps) _defineProperties(Constructor.prototype, protoProps);
  if (staticProps) _defineProperties(Constructor, staticProps);
  Object.defineProperty(Constructor, "prototype", { writable: false });
  return Constructor;
}
function _callSuper(t, o, e) {
  return o = _getPrototypeOf(o), _possibleConstructorReturn(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], _getPrototypeOf(t).constructor) : o.apply(t, e));
}
function _possibleConstructorReturn(self, call) {
  if (call && (_typeof(call) === "object" || typeof call === "function")) {
    return call;
  } else if (call !== void 0) {
    throw new TypeError("Derived constructors may only return object or undefined");
  }
  return _assertThisInitialized(self);
}
function _assertThisInitialized(self) {
  if (self === void 0) {
    throw new ReferenceError("this hasn't been initialised - super() hasn't been called");
  }
  return self;
}
function _isNativeReflectConstruct() {
  try {
    var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function() {
    }));
  } catch (t2) {
  }
  return (_isNativeReflectConstruct = function _isNativeReflectConstruct2() {
    return !!t;
  })();
}
function _getPrototypeOf(o) {
  _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function _getPrototypeOf2(o2) {
    return o2.__proto__ || Object.getPrototypeOf(o2);
  };
  return _getPrototypeOf(o);
}
function _inherits(subClass, superClass) {
  if (typeof superClass !== "function" && superClass !== null) {
    throw new TypeError("Super expression must either be null or a function");
  }
  subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, writable: true, configurable: true } });
  Object.defineProperty(subClass, "prototype", { writable: false });
  if (superClass) _setPrototypeOf(subClass, superClass);
}
function _setPrototypeOf(o, p) {
  _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf2(o2, p2) {
    o2.__proto__ = p2;
    return o2;
  };
  return _setPrototypeOf(o, p);
}
function _defineProperty(obj, key, value) {
  key = _toPropertyKey(key);
  if (key in obj) {
    Object.defineProperty(obj, key, { value, enumerable: true, configurable: true, writable: true });
  } else {
    obj[key] = value;
  }
  return obj;
}
function _toPropertyKey(t) {
  var i = _toPrimitive(t, "string");
  return "symbol" == _typeof(i) ? i : i + "";
}
function _toPrimitive(t, r) {
  if ("object" != _typeof(t) || !t) return t;
  var e = t[Symbol.toPrimitive];
  if (void 0 !== e) {
    var i = e.call(t, r);
    if ("object" != _typeof(i)) return i;
    throw new TypeError("@@toPrimitive must return a primitive value.");
  }
  return String(t);
}
var Line = /* @__PURE__ */ function(_PureComponent) {
  function Line2() {
    var _this;
    _classCallCheck(this, Line2);
    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
      args[_key] = arguments[_key];
    }
    _this = _callSuper(this, Line2, [].concat(args));
    _defineProperty(_this, "state", {
      isAnimationFinished: true,
      totalLength: 0
    });
    _defineProperty(_this, "generateSimpleStrokeDasharray", function(totalLength, length) {
      return "".concat(length, "px ").concat(totalLength - length, "px");
    });
    _defineProperty(_this, "getStrokeDasharray", function(length, totalLength, lines) {
      var lineLength = lines.reduce(function(pre, next) {
        return pre + next;
      });
      if (!lineLength) {
        return _this.generateSimpleStrokeDasharray(totalLength, length);
      }
      var count = Math.floor(length / lineLength);
      var remainLength = length % lineLength;
      var restLength = totalLength - length;
      var remainLines = [];
      for (var i = 0, sum = 0; i < lines.length; sum += lines[i], ++i) {
        if (sum + lines[i] > remainLength) {
          remainLines = [].concat(_toConsumableArray(lines.slice(0, i)), [remainLength - sum]);
          break;
        }
      }
      var emptyLines = remainLines.length % 2 === 0 ? [0, restLength] : [restLength];
      return [].concat(_toConsumableArray(Line2.repeat(lines, count)), _toConsumableArray(remainLines), emptyLines).map(function(line) {
        return "".concat(line, "px");
      }).join(", ");
    });
    _defineProperty(_this, "id", uniqueId("recharts-line-"));
    _defineProperty(_this, "pathRef", function(node) {
      _this.mainCurve = node;
    });
    _defineProperty(_this, "handleAnimationEnd", function() {
      _this.setState({
        isAnimationFinished: true
      });
      if (_this.props.onAnimationEnd) {
        _this.props.onAnimationEnd();
      }
    });
    _defineProperty(_this, "handleAnimationStart", function() {
      _this.setState({
        isAnimationFinished: false
      });
      if (_this.props.onAnimationStart) {
        _this.props.onAnimationStart();
      }
    });
    return _this;
  }
  _inherits(Line2, _PureComponent);
  return _createClass(Line2, [{
    key: "componentDidMount",
    value: function componentDidMount() {
      if (!this.props.isAnimationActive) {
        return;
      }
      var totalLength = this.getTotalLength();
      this.setState({
        totalLength
      });
    }
  }, {
    key: "componentDidUpdate",
    value: function componentDidUpdate() {
      if (!this.props.isAnimationActive) {
        return;
      }
      var totalLength = this.getTotalLength();
      if (totalLength !== this.state.totalLength) {
        this.setState({
          totalLength
        });
      }
    }
  }, {
    key: "getTotalLength",
    value: function getTotalLength() {
      var curveDom = this.mainCurve;
      try {
        return curveDom && curveDom.getTotalLength && curveDom.getTotalLength() || 0;
      } catch (err) {
        return 0;
      }
    }
  }, {
    key: "renderErrorBar",
    value: function renderErrorBar(needClip, clipPathId) {
      if (this.props.isAnimationActive && !this.state.isAnimationFinished) {
        return null;
      }
      var _this$props = this.props, points = _this$props.points, xAxis = _this$props.xAxis, yAxis = _this$props.yAxis, layout = _this$props.layout, children = _this$props.children;
      var errorBarItems = findAllByType(children, ErrorBar);
      if (!errorBarItems) {
        return null;
      }
      var dataPointFormatter = function dataPointFormatter2(dataPoint, dataKey) {
        return {
          x: dataPoint.x,
          y: dataPoint.y,
          value: dataPoint.value,
          errorVal: getValueByDataKey(dataPoint.payload, dataKey)
        };
      };
      var errorBarProps = {
        clipPath: needClip ? "url(#clipPath-".concat(clipPathId, ")") : null
      };
      return /* @__PURE__ */ React.createElement(Layer, errorBarProps, errorBarItems.map(function(item) {
        return /* @__PURE__ */ React.cloneElement(item, {
          key: "bar-".concat(item.props.dataKey),
          data: points,
          xAxis,
          yAxis,
          layout,
          dataPointFormatter
        });
      }));
    }
  }, {
    key: "renderDots",
    value: function renderDots(needClip, clipDot, clipPathId) {
      var isAnimationActive = this.props.isAnimationActive;
      if (isAnimationActive && !this.state.isAnimationFinished) {
        return null;
      }
      var _this$props2 = this.props, dot = _this$props2.dot, points = _this$props2.points, dataKey = _this$props2.dataKey;
      var lineProps = filterProps(this.props, false);
      var customDotProps = filterProps(dot, true);
      var dots = points.map(function(entry, i) {
        var dotProps = _objectSpread(_objectSpread(_objectSpread({
          key: "dot-".concat(i),
          r: 3
        }, lineProps), customDotProps), {}, {
          value: entry.value,
          dataKey,
          cx: entry.x,
          cy: entry.y,
          index: i,
          payload: entry.payload
        });
        return Line2.renderDotItem(dot, dotProps);
      });
      var dotsProps = {
        clipPath: needClip ? "url(#clipPath-".concat(clipDot ? "" : "dots-").concat(clipPathId, ")") : null
      };
      return /* @__PURE__ */ React.createElement(Layer, _extends({
        className: "recharts-line-dots",
        key: "dots"
      }, dotsProps), dots);
    }
  }, {
    key: "renderCurveStatically",
    value: function renderCurveStatically(points, needClip, clipPathId, props) {
      var _this$props3 = this.props, type = _this$props3.type, layout = _this$props3.layout, connectNulls = _this$props3.connectNulls;
      _this$props3.ref;
      var others = _objectWithoutProperties(_this$props3, _excluded);
      var curveProps = _objectSpread(_objectSpread(_objectSpread({}, filterProps(others, true)), {}, {
        fill: "none",
        className: "recharts-line-curve",
        clipPath: needClip ? "url(#clipPath-".concat(clipPathId, ")") : null,
        points
      }, props), {}, {
        type,
        layout,
        connectNulls
      });
      return /* @__PURE__ */ React.createElement(Curve, _extends({}, curveProps, {
        pathRef: this.pathRef
      }));
    }
  }, {
    key: "renderCurveWithAnimation",
    value: function renderCurveWithAnimation(needClip, clipPathId) {
      var _this2 = this;
      var _this$props4 = this.props, points = _this$props4.points, strokeDasharray = _this$props4.strokeDasharray, isAnimationActive = _this$props4.isAnimationActive, animationBegin = _this$props4.animationBegin, animationDuration = _this$props4.animationDuration, animationEasing = _this$props4.animationEasing, animationId = _this$props4.animationId, animateNewValues = _this$props4.animateNewValues, width = _this$props4.width, height = _this$props4.height;
      var _this$state = this.state, prevPoints = _this$state.prevPoints, totalLength = _this$state.totalLength;
      return /* @__PURE__ */ React.createElement(Animate, {
        begin: animationBegin,
        duration: animationDuration,
        isActive: isAnimationActive,
        easing: animationEasing,
        from: {
          t: 0
        },
        to: {
          t: 1
        },
        key: "line-".concat(animationId),
        onAnimationEnd: this.handleAnimationEnd,
        onAnimationStart: this.handleAnimationStart
      }, function(_ref) {
        var t = _ref.t;
        if (prevPoints) {
          var prevPointsDiffFactor = prevPoints.length / points.length;
          var stepData = points.map(function(entry, index) {
            var prevPointIndex = Math.floor(index * prevPointsDiffFactor);
            if (prevPoints[prevPointIndex]) {
              var prev = prevPoints[prevPointIndex];
              var interpolatorX = interpolateNumber(prev.x, entry.x);
              var interpolatorY = interpolateNumber(prev.y, entry.y);
              return _objectSpread(_objectSpread({}, entry), {}, {
                x: interpolatorX(t),
                y: interpolatorY(t)
              });
            }
            if (animateNewValues) {
              var _interpolatorX = interpolateNumber(width * 2, entry.x);
              var _interpolatorY = interpolateNumber(height / 2, entry.y);
              return _objectSpread(_objectSpread({}, entry), {}, {
                x: _interpolatorX(t),
                y: _interpolatorY(t)
              });
            }
            return _objectSpread(_objectSpread({}, entry), {}, {
              x: entry.x,
              y: entry.y
            });
          });
          return _this2.renderCurveStatically(stepData, needClip, clipPathId);
        }
        var interpolator = interpolateNumber(0, totalLength);
        var curLength = interpolator(t);
        var currentStrokeDasharray;
        if (strokeDasharray) {
          var lines = "".concat(strokeDasharray).split(/[,\s]+/gim).map(function(num) {
            return parseFloat(num);
          });
          currentStrokeDasharray = _this2.getStrokeDasharray(curLength, totalLength, lines);
        } else {
          currentStrokeDasharray = _this2.generateSimpleStrokeDasharray(totalLength, curLength);
        }
        return _this2.renderCurveStatically(points, needClip, clipPathId, {
          strokeDasharray: currentStrokeDasharray
        });
      });
    }
  }, {
    key: "renderCurve",
    value: function renderCurve(needClip, clipPathId) {
      var _this$props5 = this.props, points = _this$props5.points, isAnimationActive = _this$props5.isAnimationActive;
      var _this$state2 = this.state, prevPoints = _this$state2.prevPoints, totalLength = _this$state2.totalLength;
      if (isAnimationActive && points && points.length && (!prevPoints && totalLength > 0 || !isEqual(prevPoints, points))) {
        return this.renderCurveWithAnimation(needClip, clipPathId);
      }
      return this.renderCurveStatically(points, needClip, clipPathId);
    }
  }, {
    key: "render",
    value: function render() {
      var _filterProps;
      var _this$props6 = this.props, hide = _this$props6.hide, dot = _this$props6.dot, points = _this$props6.points, className = _this$props6.className, xAxis = _this$props6.xAxis, yAxis = _this$props6.yAxis, top = _this$props6.top, left = _this$props6.left, width = _this$props6.width, height = _this$props6.height, isAnimationActive = _this$props6.isAnimationActive, id = _this$props6.id;
      if (hide || !points || !points.length) {
        return null;
      }
      var isAnimationFinished = this.state.isAnimationFinished;
      var hasSinglePoint = points.length === 1;
      var layerClass = clsx("recharts-line", className);
      var needClipX = xAxis && xAxis.allowDataOverflow;
      var needClipY = yAxis && yAxis.allowDataOverflow;
      var needClip = needClipX || needClipY;
      var clipPathId = isNil(id) ? this.id : id;
      var _ref2 = (_filterProps = filterProps(dot, false)) !== null && _filterProps !== void 0 ? _filterProps : {
        r: 3,
        strokeWidth: 2
      }, _ref2$r = _ref2.r, r = _ref2$r === void 0 ? 3 : _ref2$r, _ref2$strokeWidth = _ref2.strokeWidth, strokeWidth = _ref2$strokeWidth === void 0 ? 2 : _ref2$strokeWidth;
      var _ref3 = hasClipDot(dot) ? dot : {}, _ref3$clipDot = _ref3.clipDot, clipDot = _ref3$clipDot === void 0 ? true : _ref3$clipDot;
      var dotSize = r * 2 + strokeWidth;
      return /* @__PURE__ */ React.createElement(Layer, {
        className: layerClass
      }, needClipX || needClipY ? /* @__PURE__ */ React.createElement("defs", null, /* @__PURE__ */ React.createElement("clipPath", {
        id: "clipPath-".concat(clipPathId)
      }, /* @__PURE__ */ React.createElement("rect", {
        x: needClipX ? left : left - width / 2,
        y: needClipY ? top : top - height / 2,
        width: needClipX ? width : width * 2,
        height: needClipY ? height : height * 2
      })), !clipDot && /* @__PURE__ */ React.createElement("clipPath", {
        id: "clipPath-dots-".concat(clipPathId)
      }, /* @__PURE__ */ React.createElement("rect", {
        x: left - dotSize / 2,
        y: top - dotSize / 2,
        width: width + dotSize,
        height: height + dotSize
      }))) : null, !hasSinglePoint && this.renderCurve(needClip, clipPathId), this.renderErrorBar(needClip, clipPathId), (hasSinglePoint || dot) && this.renderDots(needClip, clipDot, clipPathId), (!isAnimationActive || isAnimationFinished) && LabelList.renderCallByParent(this.props, points));
    }
  }], [{
    key: "getDerivedStateFromProps",
    value: function getDerivedStateFromProps(nextProps, prevState) {
      if (nextProps.animationId !== prevState.prevAnimationId) {
        return {
          prevAnimationId: nextProps.animationId,
          curPoints: nextProps.points,
          prevPoints: prevState.curPoints
        };
      }
      if (nextProps.points !== prevState.curPoints) {
        return {
          curPoints: nextProps.points
        };
      }
      return null;
    }
  }, {
    key: "repeat",
    value: function repeat(lines, count) {
      var linesUnit = lines.length % 2 !== 0 ? [].concat(_toConsumableArray(lines), [0]) : lines;
      var result = [];
      for (var i = 0; i < count; ++i) {
        result = [].concat(_toConsumableArray(result), _toConsumableArray(linesUnit));
      }
      return result;
    }
  }, {
    key: "renderDotItem",
    value: function renderDotItem(option, props) {
      var dotItem;
      if (/* @__PURE__ */ React.isValidElement(option)) {
        dotItem = /* @__PURE__ */ React.cloneElement(option, props);
      } else if (isFunction(option)) {
        dotItem = option(props);
      } else {
        var key = props.key, dotProps = _objectWithoutProperties(props, _excluded2);
        var className = clsx("recharts-line-dot", typeof option !== "boolean" ? option.className : "");
        dotItem = /* @__PURE__ */ React.createElement(Dot, _extends({
          key
        }, dotProps, {
          className
        }));
      }
      return dotItem;
    }
  }]);
}(reactExports.PureComponent);
_defineProperty(Line, "displayName", "Line");
_defineProperty(Line, "defaultProps", {
  xAxisId: 0,
  yAxisId: 0,
  connectNulls: false,
  activeDot: true,
  dot: true,
  legendType: "line",
  stroke: "#3182bd",
  strokeWidth: 1,
  fill: "#fff",
  points: [],
  isAnimationActive: !Global.isSsr,
  animateNewValues: true,
  animationBegin: 0,
  animationDuration: 1500,
  animationEasing: "ease",
  hide: false,
  label: false
});
_defineProperty(Line, "getComposedData", function(_ref4) {
  var props = _ref4.props, xAxis = _ref4.xAxis, yAxis = _ref4.yAxis, xAxisTicks = _ref4.xAxisTicks, yAxisTicks = _ref4.yAxisTicks, dataKey = _ref4.dataKey, bandSize = _ref4.bandSize, displayedData = _ref4.displayedData, offset = _ref4.offset;
  var layout = props.layout;
  var points = displayedData.map(function(entry, index) {
    var value = getValueByDataKey(entry, dataKey);
    if (layout === "horizontal") {
      return {
        x: getCateCoordinateOfLine({
          axis: xAxis,
          ticks: xAxisTicks,
          bandSize,
          entry,
          index
        }),
        y: isNil(value) ? null : yAxis.scale(value),
        value,
        payload: entry
      };
    }
    return {
      x: isNil(value) ? null : xAxis.scale(value),
      y: getCateCoordinateOfLine({
        axis: yAxis,
        ticks: yAxisTicks,
        bandSize,
        entry,
        index
      }),
      value,
      payload: entry
    };
  });
  return _objectSpread({
    points,
    layout
  }, offset);
});
var LineChart = generateCategoricalChart({
  chartName: "LineChart",
  GraphicalChild: Line,
  axisComponents: [{
    axisType: "xAxis",
    AxisComp: XAxis
  }, {
    axisType: "yAxis",
    AxisComp: YAxis
  }],
  formatAxisMap
});
const legendData$1 = [
  { label: "Ordered", color: "#6366F1" },
  { label: "Searched", color: "#A855F7" },
  { label: "Delivered", color: "#F472B6" }
];
const CustomTick$2 = ({
  x = 0,
  y = 0,
  payload,
  index = 0,
  chartData = []
}) => {
  var _a;
  const week = (payload == null ? void 0 : payload.value) || "";
  const range = ((_a = chartData[index]) == null ? void 0 : _a.range) || "";
  return /* @__PURE__ */ jsxRuntimeExports.jsxs("g", { transform: `translate(${x},${y})`, children: [
    /* @__PURE__ */ jsxRuntimeExports.jsx(
      "text",
      {
        x: 0,
        y: 0,
        dy: 8,
        textAnchor: "middle",
        fill: "#6b7280",
        fontSize: 12,
        className: "font-medium",
        children: week
      }
    ),
    /* @__PURE__ */ jsxRuntimeExports.jsx(
      "text",
      {
        x: 0,
        y: 20,
        dy: 8,
        textAnchor: "middle",
        fill: "#9ca3af",
        fontSize: 8,
        className: "font-light",
        children: range
      }
    )
  ] });
};
function ConversionRate({ conversionRates, week }) {
  const chartData = ((conversionRates == null ? void 0 : conversionRates.weeklySales) || []).map((convRate) => ({
    week: (convRate == null ? void 0 : convRate.week) || "N/A",
    range: (convRate == null ? void 0 : convRate.dateRange) || "",
    ordered: (convRate == null ? void 0 : convRate.ordered) || 0,
    searched: (convRate == null ? void 0 : convRate.searched) || 0,
    delivered: (convRate == null ? void 0 : convRate.delivered) || 0
  }));
  return /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "w-full my-2 bg-white rounded-xl shadow-lg p-6 ", children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "space-y-4", children: [
    /* @__PURE__ */ jsxRuntimeExports.jsx("h3", { className: "text-lg font-semibold text-gray-800", children: "Conversion Rate" }),
    /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-baseline gap-4", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx("p", { className: "text-2xl font-bold text-indigo-600", children: (conversionRates == null ? void 0 : conversionRates.conversionRate) > 0 ? `${conversionRates.conversionRate.toFixed(1)}%` : "0.0%" }),
      /* @__PURE__ */ jsxRuntimeExports.jsxs("span", { className: "text-sm text-gray-500", children: [
        "Last ",
        week,
        " weeks"
      ] })
    ] }),
    /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex flex-col sm:flex-row gap-6 items-start ", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "w-full sm:w-2/3 -mx-6", children: /* @__PURE__ */ jsxRuntimeExports.jsx(ResponsiveContainer, { width: "100%", height: 250, children: /* @__PURE__ */ jsxRuntimeExports.jsxs(
        LineChart,
        {
          data: chartData,
          margin: { top: 20, right: 20, bottom: 20, left: 0 },
          children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx(
              CartesianGrid,
              {
                vertical: false,
                stroke: "#e5e7eb",
                strokeDasharray: "3 3"
              }
            ),
            /* @__PURE__ */ jsxRuntimeExports.jsx(
              XAxis,
              {
                dataKey: "week",
                tickLine: false,
                tickMargin: 15,
                axisLine: { stroke: "#e5e7eb" },
                height: 60,
                tick: /* @__PURE__ */ jsxRuntimeExports.jsx(CustomTick$2, { chartData })
              }
            ),
            /* @__PURE__ */ jsxRuntimeExports.jsx(
              YAxis,
              {
                dataKey: "searched",
                allowDecimals: false,
                tickLine: false,
                axisLine: { stroke: "#e5e7eb" },
                tickFormatter: (value) => `${value}`,
                className: "text-gray-600"
              }
            ),
            /* @__PURE__ */ jsxRuntimeExports.jsx(
              Tooltip,
              {
                formatter: (value, name) => [
                  value,
                  name.charAt(0).toUpperCase() + name.slice(1)
                ],
                contentStyle: {
                  backgroundColor: "#fff",
                  borderRadius: "8px",
                  border: "1px solid #e5e7eb",
                  boxShadow: "0 2px 4px rgba(0,0,0,0.1)"
                }
              }
            ),
            legendData$1.map((item) => /* @__PURE__ */ jsxRuntimeExports.jsx(
              Line,
              {
                dataKey: item.label.toLowerCase(),
                type: "monotone",
                stroke: item.color,
                strokeWidth: 2,
                dot: { r: 4 },
                activeDot: { r: 6 }
              },
              item.label
            ))
          ]
        }
      ) }) }),
      /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "flex  sm: flex-row md:flex-col gap-3 self-center", children: legendData$1.map((item) => /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center gap-2", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx(
          "span",
          {
            className: "w-3 h-3 rounded-full",
            style: { backgroundColor: item.color }
          }
        ),
        /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "text-sm font-medium text-gray-700", children: item.label })
      ] }, item.label)) })
    ] })
  ] }) });
}
const legendData = [
  { label: "Ordered", color: "#7987FF" },
  { label: "New", color: "#E697FF" },
  { label: "Repeat", color: "#FFA5CB" }
];
const CustomTick$1 = ({
  x = 0,
  y = 0,
  payload,
  index = 0,
  chartData = []
}) => {
  var _a;
  const week = (payload == null ? void 0 : payload.value) || "";
  const range = ((_a = chartData[index]) == null ? void 0 : _a.range) || "";
  return /* @__PURE__ */ jsxRuntimeExports.jsxs("g", { transform: `translate(${x},${y})`, children: [
    /* @__PURE__ */ jsxRuntimeExports.jsx(
      "text",
      {
        x: 0,
        y: 0,
        dy: 8,
        textAnchor: "middle",
        fill: "#6b7280",
        fontSize: 12,
        className: "font-medium",
        children: week
      }
    ),
    /* @__PURE__ */ jsxRuntimeExports.jsx(
      "text",
      {
        x: 0,
        y: 20,
        dy: 8,
        textAnchor: "middle",
        fill: "#9ca3af",
        fontSize: 8,
        className: "font-light",
        children: range
      }
    )
  ] });
};
function CustomersAcquisition({ customerAcquisitionRate }) {
  const chartData = ((customerAcquisitionRate == null ? void 0 : customerAcquisitionRate.weeklyAcquisition) || []).map((acquisition) => ({
    week: (acquisition == null ? void 0 : acquisition.week) || "N/A",
    range: (acquisition == null ? void 0 : acquisition.dateRange) || "",
    ordered: (acquisition == null ? void 0 : acquisition.ordered) || 0,
    new: (acquisition == null ? void 0 : acquisition.newCustomers) || 0,
    repeat: (acquisition == null ? void 0 : acquisition.repeat) || 0
  }));
  const totalCustomers = customerAcquisitionRate == null ? void 0 : customerAcquisitionRate.totalCustomers;
  const avgNewCustomers = customerAcquisitionRate.newCustomers;
  return /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "w-full my-3 bg-white rounded-xl shadow-lg p-6", children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "space-y-4", children: [
    /* @__PURE__ */ jsxRuntimeExports.jsx("h3", { className: "text-lg font-semibold text-gray-800", children: "Customer Acquisition" }),
    /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-baseline gap-4", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx("p", { className: "text-2xl font-bold text-purple-600", children: totalCustomers || "" }),
      /* @__PURE__ */ jsxRuntimeExports.jsxs("span", { className: "text-sm text-gray-500", children: [
        avgNewCustomers,
        " new customers per week"
      ] })
    ] }),
    /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex flex-col sm:flex-row gap-6 items-start", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "w-full sm:w-2/3", children: /* @__PURE__ */ jsxRuntimeExports.jsx(ResponsiveContainer, { width: "100%", height: 250, children: /* @__PURE__ */ jsxRuntimeExports.jsxs(
        LineChart,
        {
          data: chartData,
          margin: { top: 20, right: 20, bottom: 20, left: 0 },
          children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx(
              CartesianGrid,
              {
                vertical: false,
                stroke: "#e5e7eb",
                strokeDasharray: "3 3"
              }
            ),
            /* @__PURE__ */ jsxRuntimeExports.jsx(
              XAxis,
              {
                dataKey: "week",
                tickLine: false,
                tickMargin: 15,
                axisLine: { stroke: "#e5e7eb" },
                height: 60,
                tick: /* @__PURE__ */ jsxRuntimeExports.jsx(CustomTick$1, { chartData })
              }
            ),
            /* @__PURE__ */ jsxRuntimeExports.jsx(
              YAxis,
              {
                dataKey: "new",
                allowDecimals: false,
                tickLine: false,
                axisLine: { stroke: "#e5e7eb" },
                tickFormatter: (value) => `${value}`,
                className: "text-gray-600"
              }
            ),
            /* @__PURE__ */ jsxRuntimeExports.jsx(
              Tooltip,
              {
                formatter: (value, name) => [
                  value,
                  name.charAt(0).toUpperCase() + name.slice(1)
                ],
                contentStyle: {
                  backgroundColor: "#fff",
                  borderRadius: "8px",
                  border: "1px solid #e5e7eb",
                  boxShadow: "0 2px 4px rgba(0,0,0,0.1)"
                }
              }
            ),
            legendData.map((item) => /* @__PURE__ */ jsxRuntimeExports.jsx(
              Line,
              {
                dataKey: item.label.toLowerCase(),
                type: "monotone",
                stroke: item.color,
                strokeWidth: 2,
                dot: { r: 4 },
                activeDot: { r: 6 }
              },
              item.label
            ))
          ]
        }
      ) }) }),
      /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "flex  sm:flex-row md:flex-col gap-3 self-center", children: legendData.map((item) => /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center gap-2", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx(
          "span",
          {
            className: "w-3 h-3 rounded-full",
            style: { backgroundColor: item.color }
          }
        ),
        /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "text-sm font-medium text-gray-700", children: item.label })
      ] }, item.label)) })
    ] })
  ] }) });
}
const CustomTick = ({
  x = 0,
  y = 0,
  payload,
  index = 0,
  chartData = []
}) => {
  var _a;
  const week = (payload == null ? void 0 : payload.value) || "";
  const range = ((_a = chartData[index]) == null ? void 0 : _a.range) || "";
  return /* @__PURE__ */ jsxRuntimeExports.jsxs("g", { transform: `translate(${x},${y})`, children: [
    /* @__PURE__ */ jsxRuntimeExports.jsx(
      "text",
      {
        x: 0,
        y: 0,
        dy: 8,
        textAnchor: "middle",
        fill: "#666",
        fontSize: 12,
        className: "font-medium",
        children: week
      }
    ),
    /* @__PURE__ */ jsxRuntimeExports.jsx(
      "text",
      {
        x: 0,
        y: 20,
        dy: 8,
        textAnchor: "middle",
        fill: "#999",
        fontSize: 8,
        className: "font-light",
        children: range
      }
    )
  ] });
};
function CustomerSales({ sales }) {
  var _a;
  const chartData = ((sales == null ? void 0 : sales.weeklySales) || []).map((weekData) => ({
    week: (weekData == null ? void 0 : weekData.week) || "N/A",
    range: (weekData == null ? void 0 : weekData.dateRange) || "",
    sales: (weekData == null ? void 0 : weekData.totalDeliveredOrders) || 0
  }));
  return /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "w-full my-3 bg-white rounded-xl shadow-lg p-6  ", children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "space-y-3", children: [
    /* @__PURE__ */ jsxRuntimeExports.jsx("h3", { className: "text-lg font-semibold text-gray-800", children: "Sales Overview" }),
    /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "flex items-center gap-6", children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx("p", { className: "text-2xl font-bold text-blue-600", children: formatCurrency((sales == null ? void 0 : sales.totalRevenue) || 0) }),
      /* @__PURE__ */ jsxRuntimeExports.jsxs("p", { className: "text-sm text-gray-600", children: [
        ((_a = sales == null ? void 0 : sales.totalOrders) == null ? void 0 : _a.toLocaleString()) || 0,
        " Orders"
      ] })
    ] }) }),
    /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "mt-6 -mx-6", children: [
      " ",
      "          ",
      /* @__PURE__ */ jsxRuntimeExports.jsx(ResponsiveContainer, { width: "100%", height: 250, children: /* @__PURE__ */ jsxRuntimeExports.jsxs(
        BarChart,
        {
          data: chartData,
          margin: { top: 20, right: 20, bottom: 20, left: 0 },
          children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx(
              CartesianGrid,
              {
                vertical: false,
                stroke: "#e5e7eb",
                strokeDasharray: "3 3"
              }
            ),
            /* @__PURE__ */ jsxRuntimeExports.jsx(
              XAxis,
              {
                dataKey: "week",
                tickLine: false,
                tickMargin: 15,
                axisLine: { stroke: "#e5e7eb" },
                height: 60,
                tick: /* @__PURE__ */ jsxRuntimeExports.jsx(CustomTick, { chartData })
              }
            ),
            /* @__PURE__ */ jsxRuntimeExports.jsx(
              YAxis,
              {
                dataKey: "sales",
                allowDecimals: false,
                tickLine: false,
                axisLine: { stroke: "#e5e7eb" },
                tickFormatter: (value) => `${value}`,
                className: "text-gray-600"
              }
            ),
            /* @__PURE__ */ jsxRuntimeExports.jsx(
              Tooltip,
              {
                formatter: (value) => value,
                contentStyle: {
                  backgroundColor: "#fff",
                  borderRadius: "8px",
                  border: "1px solid #e5e7eb",
                  boxShadow: "0 2px 4px rgba(0,0,0,0.1)"
                }
              }
            ),
            /* @__PURE__ */ jsxRuntimeExports.jsx(
              Bar,
              {
                dataKey: "sales",
                fill: "#6B8EF1",
                barSize: 20,
                radius: [4, 4, 0, 0]
              }
            )
          ]
        }
      ) })
    ] })
  ] }) });
}
function SellerCustomers() {
  const {
    sales,
    customerConversionRates,
    customerAcquisitionRate,
    week,
    customers,
    currentPage,
    tabValue,
    sortByvalue,
    searchBy,
    sortByOrder
  } = useLoaderData();
  const [selectedCustomer, setSelectedCustomer] = reactExports.useState(null);
  const [activeTab, setActiveTab] = reactExports.useState("Customer Overview");
  const fetcher = useFetcher();
  const [searchParams, setSearchParams] = useSearchParams();
  const [actionType, setActionType] = reactExports.useState("");
  const [actionSelectedCustomer, setActionSelectedCustomer] = reactExports.useState(null);
  const [isSubmitting, setIsSubmitting] = reactExports.useState(false);
  const {
    toast
  } = useToast();
  const [searchType, setSearchType] = reactExports.useState("ID");
  const [searchTerm, setSearchTerm] = reactExports.useState("");
  const [filterDate, setFilterDate] = reactExports.useState(void 0);
  const [pageSize, setPageSize] = reactExports.useState(20);
  const [dateRange, setDateRange] = reactExports.useState(void 0);
  const [customerListSearchTerm, setCustomerListSearchTerm] = reactExports.useState(searchBy);
  const [customerListSortBy, setCustomerListSortBy] = reactExports.useState(sortByvalue);
  const [customerListSortOrder, setCustomerListSortOrder] = reactExports.useState(sortByOrder);
  const [customerListActiveTab, setCustomerListActiveTab] = reactExports.useState(tabValue);
  const [fbDiscounts, setFbDiscounts] = reactExports.useState({});
  const [isFbDis, setIsFbDis] = reactExports.useState({});
  reactExports.useEffect(() => {
    const timer = setTimeout(() => {
    }, 500);
    return () => {
      clearTimeout(timer);
    };
  }, [searchTerm]);
  const updateCustomerListParams = (params) => {
    const newSearchParams = new URLSearchParams(searchParams);
    Object.entries(params).forEach(([key, value]) => {
      if (value) {
        newSearchParams.set(key, value);
      } else {
        newSearchParams.delete(key);
      }
    });
    setSearchParams(newSearchParams);
  };
  const handleTabChange = (newTab) => {
    setSearchType("ID");
    setSearchTerm("");
    setFilterDate(void 0);
    setDateRange(void 0);
    setPageSize(20);
    setActiveTab(newTab);
  };
  const handleAction = (customer, action2) => {
    setActionSelectedCustomer(customer);
    setActionType(action2);
  };
  const handleSubmitAction = (formData) => {
    const actionData = new FormData();
    actionData.append("intent", actionType);
    actionData.append("data", JSON.stringify({
      customer: actionSelectedCustomer,
      formData
    }));
    fetcher.submit(actionData, {
      method: "post"
    });
    setIsSubmitting(true);
  };
  const handleCustomerListTabChange = (newTab) => {
    const tabMap = {
      all: "all",
      oneOrder: "one_order",
      frequent: "frequent_orders",
      zero_orders: "zero_orders"
    };
    const validTabValue = tabMap[newTab] || "all";
    setCustomerListActiveTab(newTab);
    updateCustomerListParams({
      tabValue: validTabValue,
      page: "0",
      // Reset to first page when changing tabs
      sortBy: customerListSortBy,
      sortByOrder: customerListSortOrder,
      searchBy: customerListSearchTerm
    });
  };
  const handleCustomerListSort = (newSort) => {
    setCustomerListSortBy((prevSortBy) => {
      const isSameSort = prevSortBy === newSort;
      setCustomerListSortOrder((prevSortOrder) => {
        const newOrder = isSameSort && prevSortOrder === "asc" ? "desc" : "asc";
        updateCustomerListParams({
          tabValue: customerListActiveTab,
          page: "0",
          // Reset to first page when sorting
          sortBy: newSort,
          sortByOrder: newOrder,
          searchBy: customerListSearchTerm
        });
        return newOrder;
      });
      return newSort;
    });
  };
  const getSortIcon = (column) => {
    if (customerListSortBy !== column) return null;
    return customerListSortOrder === "asc" ? /* @__PURE__ */ jsxRuntimeExports.jsx(ArrowUp, {
      className: "w-4 h-4 ml-1"
    }) : /* @__PURE__ */ jsxRuntimeExports.jsx(ArrowDown, {
      className: "w-4 h-4 ml-1"
    });
  };
  const handleCustomerListSearch = (e) => {
    const value = e.target.value;
    setCustomerListSearchTerm(value);
    setTimeout(() => {
      updateCustomerListParams({
        tabValue: customerListActiveTab,
        page: "0",
        // Reset to first page when searching
        sortBy: customerListSortBy,
        sortByOrder: customerListSortOrder,
        searchBy: value.length >= 3 ? value : ""
      });
    }, 300);
  };
  const handleChangeFbDiscount = (nBuyerId, val) => {
    if (/^\d*\.?\d*$/.test(val) || val === "") {
      setFbDiscounts((prev) => ({
        ...prev,
        [nBuyerId]: val
      }));
    }
  };
  const fbFetcher = useFetcher();
  const handleSave = (nBuyerId) => {
    const formData = new FormData();
    formData.append("intent", "UpdateFbDiscount");
    formData.append("nBuyerId", nBuyerId.toString());
    formData.append("fbDiscount", fbDiscounts[nBuyerId]);
    fbFetcher.submit(formData, {
      method: "POST"
    });
    setIsFbDis((prev) => ({
      ...prev,
      [nBuyerId]: false
    }));
  };
  const handlePageSizeChange = (newPageSize) => {
    setPageSize(newPageSize);
    updateCustomerListParams({
      page: "0",
      // Reset to first page when changing page size
      pageSize: newPageSize.toString()
    });
  };
  const handlePageChange = (newPage) => {
    updateCustomerListParams({
      page: newPage.toString()
    });
  };
  reactExports.useEffect(() => {
    var _a, _b, _c, _d;
    if (((_a = fetcher.data) == null ? void 0 : _a.intent) === "UpdateFbDiscount") {
      if ((_b = fetcher.data) == null ? void 0 : _b.success) {
        toast({
          title: "Success",
          description: "Discount updated successfully"
        });
      } else if (((_c = fetcher.data) == null ? void 0 : _c.success) === false) {
        toast({
          title: "Error",
          description: (_d = fetcher.data) == null ? void 0 : _d.errorMessage,
          variant: "destructive"
        });
      }
    }
  }, [fetcher.data, toast]);
  return /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
    className: "min-h-screen p-6",
    children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
      className: "mx-auto mb-6",
      children: [/* @__PURE__ */ jsxRuntimeExports.jsx("div", {
        className: "mb-4",
        children: /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
          className: "flex flex-col gap-2 sm:flex-row sm:items-center sm:justify-between",
          children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
            children: [/* @__PURE__ */ jsxRuntimeExports.jsx("h1", {
              className: "text-xl md:text-3xl font-bold text-gray-900",
              children: "Customers"
            }), /* @__PURE__ */ jsxRuntimeExports.jsx("p", {
              className: "mt-2 text-gray-600",
              children: "Manage your customer relationships and loyalty programs"
            })]
          })
        })
      }), /* @__PURE__ */ jsxRuntimeExports.jsxs(Tabs, {
        value: activeTab,
        onValueChange: handleTabChange,
        children: [/* @__PURE__ */ jsxRuntimeExports.jsxs(TabsList, {
          className: "w-full h-10 mb-1",
          children: [/* @__PURE__ */ jsxRuntimeExports.jsx(TabsTrigger, {
            value: "Customer Overview",
            className: "w-1/2 h-8 py-1",
            children: "Customer Overview"
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(TabsTrigger, {
            value: "Customer List",
            className: "w-1/2 h-8 py-1",
            children: "Customer List"
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(TabsTrigger, {
            value: "Customer Loyalty",
            className: "w-1/2 h-8 py-1",
            children: "Customer Loyalty"
          })]
        }), /* @__PURE__ */ jsxRuntimeExports.jsx(TabsContent, {
          value: "Customer Overview",
          children: /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
            className: "w-full min-h-screen bg-slate-100",
            children: /* @__PURE__ */ jsxRuntimeExports.jsxs("main", {
              className: "p-2",
              children: [/* @__PURE__ */ jsxRuntimeExports.jsx("section", {
                children: /* @__PURE__ */ jsxRuntimeExports.jsx(CustomerSales, {
                  sales
                })
              }), /* @__PURE__ */ jsxRuntimeExports.jsx("section", {
                children: /* @__PURE__ */ jsxRuntimeExports.jsx(ConversionRate, {
                  conversionRates: customerConversionRates,
                  week
                })
              }), /* @__PURE__ */ jsxRuntimeExports.jsx("section", {
                children: /* @__PURE__ */ jsxRuntimeExports.jsx(CustomersAcquisition, {
                  customerAcquisitionRate
                })
              })]
            })
          })
        }), /* @__PURE__ */ jsxRuntimeExports.jsx(TabsContent, {
          value: "Customer List",
          children: /* @__PURE__ */ jsxRuntimeExports.jsx(Card, {
            className: "mb-1",
            children: /* @__PURE__ */ jsxRuntimeExports.jsxs(CardContent, {
              className: "p-2",
              children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Tabs, {
                value: customerListActiveTab,
                onValueChange: handleCustomerListTabChange,
                className: "mb-4",
                children: /* @__PURE__ */ jsxRuntimeExports.jsxs(TabsList, {
                  className: "w-full h-10 mb-1",
                  children: [/* @__PURE__ */ jsxRuntimeExports.jsx(TabsTrigger, {
                    value: "all",
                    className: "w-1/2 h-8 py-1",
                    children: " All Customers"
                  }), /* @__PURE__ */ jsxRuntimeExports.jsx(TabsTrigger, {
                    value: "frequent",
                    className: "w-1/2 h-8 py-1",
                    children: " Frequent Customers"
                  }), /* @__PURE__ */ jsxRuntimeExports.jsx(TabsTrigger, {
                    value: "oneOrder",
                    className: "w-1/2 h-8 py-1",
                    children: " One Order Customers"
                  }), /* @__PURE__ */ jsxRuntimeExports.jsx(TabsTrigger, {
                    value: "zero_orders",
                    className: "w-1/2 h-8 py-1",
                    children: " New Customers"
                  })]
                })
              }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                className: "flex justify-between mb-4",
                children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Input, {
                  placeholder: "Search by name or owner",
                  value: customerListSearchTerm,
                  onChange: handleCustomerListSearch,
                  className: "max-w-sm"
                }), /* @__PURE__ */ jsxRuntimeExports.jsxs(Select, {
                  value: customerListSortBy,
                  onValueChange: handleCustomerListSort,
                  children: [/* @__PURE__ */ jsxRuntimeExports.jsx(SelectTrigger, {
                    className: "w-[180px]",
                    children: /* @__PURE__ */ jsxRuntimeExports.jsx(SelectValue, {
                      placeholder: "Sort by"
                    })
                  }), /* @__PURE__ */ jsxRuntimeExports.jsxs(SelectContent, {
                    children: [/* @__PURE__ */ jsxRuntimeExports.jsx(SelectItem, {
                      value: "buyerName",
                      children: "Name"
                    }), /* @__PURE__ */ jsxRuntimeExports.jsx(SelectItem, {
                      value: "totalOrders",
                      children: "Number of Orders"
                    }), /* @__PURE__ */ jsxRuntimeExports.jsx(SelectItem, {
                      value: "pendingAmount",
                      children: "Pending Balance"
                    }), /* @__PURE__ */ jsxRuntimeExports.jsx(SelectItem, {
                      value: "lastOrderedDate",
                      children: "Order Duration Days"
                    }), /* @__PURE__ */ jsxRuntimeExports.jsx(SelectItem, {
                      value: "fbDiscount",
                      children: "F.B.Discount"
                    })]
                  })]
                })]
              }), /* @__PURE__ */ jsxRuntimeExports.jsxs(Table, {
                children: [fbFetcher.state !== "idle" && /* @__PURE__ */ jsxRuntimeExports.jsx(SpinnerLoader, {
                  loading: true
                }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableHeader, {
                  children: /* @__PURE__ */ jsxRuntimeExports.jsxs(TableRow, {
                    children: [/* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
                      className: "cursor-pointer",
                      onClick: () => handleCustomerListSort("buyerName"),
                      children: /* @__PURE__ */ jsxRuntimeExports.jsxs("span", {
                        className: "flex flex-row items-center gap-1",
                        children: [/* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                          children: "Name"
                        }), /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                          children: getSortIcon("buyerName")
                        })]
                      })
                    }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
                      className: "cursor-pointer",
                      onClick: () => handleCustomerListSort("totalOrders"),
                      children: /* @__PURE__ */ jsxRuntimeExports.jsxs("span", {
                        className: "flex flex-row items-center gap-1",
                        children: [/* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                          children: "Num of Orders"
                        }), getSortIcon("totalOrders")]
                      })
                    }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
                      className: "cursor-pointer",
                      onClick: () => handleCustomerListSort("pendingAmount"),
                      children: /* @__PURE__ */ jsxRuntimeExports.jsxs("span", {
                        className: "flex flex-row items-center gap-1",
                        children: [/* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                          children: "Pending Balance"
                        }), getSortIcon("pendingAmount")]
                      })
                    }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
                      className: "cursor-pointer",
                      onClick: () => handleCustomerListSort("lastOrderedDate"),
                      children: /* @__PURE__ */ jsxRuntimeExports.jsxs("span", {
                        className: "flex flex-row items-center gap-1",
                        children: [/* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                          children: "Order Duration Days"
                        }), getSortIcon("lastOrderedDate")]
                      })
                    }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
                      className: "cursor-pointer",
                      onClick: () => handleCustomerListSort("fbDiscount"),
                      children: /* @__PURE__ */ jsxRuntimeExports.jsxs("span", {
                        className: "flex flex-row items-center gap-1",
                        children: [/* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                          children: "Freq Buyer Discount"
                        }), " ", getSortIcon("fbDiscount"), " "]
                      })
                    })]
                  })
                }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableBody, {
                  children: customers.map((customer) => /* @__PURE__ */ jsxRuntimeExports.jsxs(TableRow, {
                    children: [/* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
                      children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                        className: "flex flex-row gap-2 items-center",
                        children: [/* @__PURE__ */ jsxRuntimeExports.jsx("div", {
                          className: "text-blue-600 hover:underline cursor-pointer",
                          children: /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
                            children: customer.buyerName !== "" ? customer.buyerName : "( Name not given )"
                          })
                        }), /* @__PURE__ */ jsxRuntimeExports.jsxs(Dialog, {
                          children: [/* @__PURE__ */ jsxRuntimeExports.jsx(DialogTrigger, {
                            asChild: true,
                            children: /* @__PURE__ */ jsxRuntimeExports.jsx(Info, {
                              size: 18,
                              className: "cursor-pointer text-gray-600 hover:text-purple-600 transition-all"
                            })
                          }), /* @__PURE__ */ jsxRuntimeExports.jsxs(DialogContent, {
                            className: "sm:max-w-fit p-5 rounded-lg",
                            children: [/* @__PURE__ */ jsxRuntimeExports.jsx(DialogHeader, {
                              children: /* @__PURE__ */ jsxRuntimeExports.jsx(DialogTitle, {
                                className: "text-lg font-semibold text-gray-800",
                                children: "About Customer"
                              })
                            }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                              className: "flex flex-col gap-3 mt-2",
                              children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                                className: "flex flex-row gap-2",
                                children: [/* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                                  className: "text-sm text-gray-600",
                                  children: "Buyer Name:"
                                }), /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                                  className: "text-base text-purple-800 font-semibold",
                                  children: customer.buyerName
                                })]
                              }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                                className: "flex flex-row gap-2",
                                children: [/* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                                  className: "text-sm text-gray-600",
                                  children: "Mobile Number:"
                                }), /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                                  className: "text-base text-purple-800 font-semibold",
                                  children: customer.mobileNumber
                                })]
                              })]
                            })]
                          })]
                        })]
                      })
                    }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
                      children: customer.totalOrders
                    }), /* @__PURE__ */ jsxRuntimeExports.jsxs(TableCell, {
                      children: ["₹ ", customer.pendingAmount.toLocaleString("en-IN")]
                    }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
                      children: customer.lastOrderedDate ? isNaN(new Date(customer.lastOrderedDate).getTime()) ? "-" : Math.floor(((/* @__PURE__ */ new Date()).getTime() - new Date(customer.lastOrderedDate).getTime()) / (1e3 * 60 * 60 * 24)) + " days" : "-"
                    }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
                      className: "flex flex-row items-center gap-2",
                      children: isFbDis[customer.nBuyerId] ? /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                        className: "flex flex-row justify-center items-center gap-3",
                        children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Input, {
                          value: String(fbDiscounts[customer.nBuyerId] ?? customer.fbDiscount ?? ""),
                          onChange: (e) => {
                            const val = e.target.value;
                            if (!/^\d*\.?\d*$/.test(val)) return;
                            let numVal = parseFloat(val);
                            if (numVal > 100) numVal = 100;
                            if (numVal < 0 || isNaN(numVal)) numVal = 0;
                            handleChangeFbDiscount(customer.nBuyerId, String(numVal));
                          },
                          disabled: !isFbDis[customer.nBuyerId],
                          type: "number",
                          min: "0",
                          max: "100",
                          step: "0.01"
                        }), /* @__PURE__ */ jsxRuntimeExports.jsx(Save, {
                          size: 24,
                          onClick: () => handleSave(customer.nBuyerId),
                          className: "cursor-pointer text-green-500"
                        }), /* @__PURE__ */ jsxRuntimeExports.jsx(X, {
                          color: "red",
                          size: 24,
                          className: "cursor-pointer text-red-500",
                          onClick: () => setIsFbDis({})
                        })]
                      }) : /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                        className: "flex flex-row gap-3 items-center justify-center",
                        children: [/* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                          children: customer.fbDiscount > 0 ? `${customer.fbDiscount} %` : "-"
                        }), /* @__PURE__ */ jsxRuntimeExports.jsx(Pencil, {
                          size: 15,
                          onClick: () => setIsFbDis({
                            [customer.nBuyerId]: true
                          }),
                          className: "cursor-pointer text-blue-500"
                        })]
                      })
                    })]
                  }, customer.buyerId))
                })]
              }), /* @__PURE__ */ jsxRuntimeExports.jsx(Card, {
                className: "p-0 mb-1",
                children: /* @__PURE__ */ jsxRuntimeExports.jsx(CardContent, {
                  className: "px-1.5 py-1",
                  children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                    className: "flex flex-row items-center justify-end gap-1.5",
                    children: [/* @__PURE__ */ jsxRuntimeExports.jsxs(Select, {
                      value: pageSize.toString(),
                      onValueChange: (value) => handlePageSizeChange(Number(value)),
                      children: [/* @__PURE__ */ jsxRuntimeExports.jsx(SelectTrigger, {
                        className: "w-[140px] h-[36px]",
                        children: /* @__PURE__ */ jsxRuntimeExports.jsx(SelectValue, {})
                      }), /* @__PURE__ */ jsxRuntimeExports.jsxs(SelectContent, {
                        children: [/* @__PURE__ */ jsxRuntimeExports.jsx(SelectItem, {
                          value: "20",
                          children: "20 per Page"
                        }), /* @__PURE__ */ jsxRuntimeExports.jsx(SelectItem, {
                          value: "50",
                          children: "50 per Page"
                        }), /* @__PURE__ */ jsxRuntimeExports.jsx(SelectItem, {
                          value: "100",
                          children: "100 per Page"
                        })]
                      })]
                    }), /* @__PURE__ */ jsxRuntimeExports.jsxs(Select, {
                      value: currentPage.toString(),
                      onValueChange: (value) => handlePageChange(Number(value)),
                      children: [/* @__PURE__ */ jsxRuntimeExports.jsx(SelectTrigger, {
                        className: "w-[140px] h-[36px]",
                        children: /* @__PURE__ */ jsxRuntimeExports.jsx(SelectValue, {})
                      }), /* @__PURE__ */ jsxRuntimeExports.jsx(SelectContent, {
                        children: Array.from({
                          length: Math.ceil((customers.length || 1) / pageSize)
                        }, (_, i) => /* @__PURE__ */ jsxRuntimeExports.jsxs(SelectItem, {
                          value: i.toString(),
                          children: ["Page ", i + 1]
                        }, i))
                      })]
                    })]
                  })
                })
              })]
            })
          })
        }), /* @__PURE__ */ jsxRuntimeExports.jsx(TabsContent, {
          value: "Customer Loyalty",
          children: /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
            className: "flex items-center justify-center min-h-[400px]",
            children: /* @__PURE__ */ jsxRuntimeExports.jsxs(Card, {
              className: "w-full max-w-md",
              children: [/* @__PURE__ */ jsxRuntimeExports.jsxs(CardHeader, {
                className: "text-center",
                children: [/* @__PURE__ */ jsxRuntimeExports.jsx("div", {
                  className: "mx-auto mb-4 w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center",
                  children: /* @__PURE__ */ jsxRuntimeExports.jsx("svg", {
                    className: "w-8 h-8 text-white",
                    fill: "none",
                    stroke: "currentColor",
                    viewBox: "0 0 24 24",
                    children: /* @__PURE__ */ jsxRuntimeExports.jsx("path", {
                      strokeLinecap: "round",
                      strokeLinejoin: "round",
                      strokeWidth: 2,
                      d: "M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                    })
                  })
                }), /* @__PURE__ */ jsxRuntimeExports.jsx(CardTitle, {
                  className: "text-2xl",
                  children: "Loyalty Program Coming Soon"
                }), /* @__PURE__ */ jsxRuntimeExports.jsx(CardDescription, {
                  className: "text-lg",
                  children: "We're working hard to bring you comprehensive restaurant analytics and insights."
                })]
              }), /* @__PURE__ */ jsxRuntimeExports.jsxs(CardContent, {
                className: "text-center",
                children: [/* @__PURE__ */ jsxRuntimeExports.jsx("p", {
                  className: "text-gray-500 mb-4",
                  children: "Get ready for powerful metrics, real-time data, and actionable insights to grow your business."
                }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                  className: "inline-flex items-center px-4 py-2 bg-gradient-to-r from-blue-500 to-purple-600 text-black rounded-lg",
                  children: [/* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                    className: "mr-2",
                    children: "🚀"
                  }), "Launching Soon"]
                })]
              })]
            })
          })
        })]
      }), /* @__PURE__ */ jsxRuntimeExports.jsx(CustomerDetailsModal, {
        customer: selectedCustomer,
        onClose: () => setSelectedCustomer(null),
        onAction: handleAction
      }), /* @__PURE__ */ jsxRuntimeExports.jsx(ActionModal, {
        customer: actionSelectedCustomer,
        actionType,
        onClose: () => {
          setActionSelectedCustomer(null);
          setActionType("");
        },
        isSubmitting,
        onSubmit: handleSubmitAction
      })]
    })
  });
}
function CustomerDetailsModal({
  customer,
  onClose
}) {
  if (!customer) return null;
  const handlePhoneClick = (phoneNumber) => {
    window.open(`tel:${phoneNumber}`, "_self");
  };
  return /* @__PURE__ */ jsxRuntimeExports.jsx(Dialog, {
    open: !!customer,
    onOpenChange: onClose,
    children: /* @__PURE__ */ jsxRuntimeExports.jsxs(DialogContent, {
      className: "max-w-4xl max-h-[90vh] overflow-y-auto w-[95vw] sm:w-full rounded-md",
      children: [/* @__PURE__ */ jsxRuntimeExports.jsx(DialogHeader, {
        children: /* @__PURE__ */ jsxRuntimeExports.jsxs(DialogTitle, {
          className: "text-lg sm:text-xl flex items-center gap-2",
          children: ["Customer Details - #", customer.buyerId]
        })
      }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
        className: "space-y-4",
        children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
          className: "space-y-2",
          children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
            className: "flex items-center gap-2",
            children: [/* @__PURE__ */ jsxRuntimeExports.jsx(User, {
              className: "w-4 h-4"
            }), /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
              className: "font-medium",
              children: customer.buyerName
            })]
          }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
            className: "flex items-center gap-2",
            children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Phone, {
              className: "w-4 h-4"
            }), /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
              children: customer.mobileNumber
            }), /* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
              size: "sm",
              variant: "outline",
              onClick: () => handlePhoneClick(customer.mobileNumber),
              className: "h-6 w-6 p-0",
              children: /* @__PURE__ */ jsxRuntimeExports.jsx(Phone, {
                className: "w-3 h-3"
              })
            })]
          }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
            className: "flex items-center gap-2",
            children: [/* @__PURE__ */ jsxRuntimeExports.jsx(MapPin, {
              className: "w-4 h-4"
            }), /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
              children: customer.address
            })]
          })]
        }), /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
          className: "flex gap-2 pt-4 border-t",
          children: /* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
            variant: "outline",
            onClick: onClose,
            children: "Close"
          })
        })]
      })]
    })
  });
}
function ActionModal({
  customer,
  actionType,
  onClose,
  isSubmitting,
  onSubmit
}) {
  const [formData] = reactExports.useState({
    reason: ""
  });
  const handleSubmit = (e) => {
    e.preventDefault();
    onSubmit(formData);
  };
  if (!customer || !actionType) return null;
  return /* @__PURE__ */ jsxRuntimeExports.jsx(Dialog, {
    open: true,
    onOpenChange: onClose,
    children: /* @__PURE__ */ jsxRuntimeExports.jsxs(DialogContent, {
      className: "max-w-2xl rounded-md",
      children: [/* @__PURE__ */ jsxRuntimeExports.jsx(DialogHeader, {
        children: /* @__PURE__ */ jsxRuntimeExports.jsx(DialogTitle, {
          children: "Action Modal"
        })
      }), /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
        className: "space-y-4",
        children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
          className: "flex gap-2 pt-4",
          children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
            variant: "outline",
            onClick: onClose,
            disabled: isSubmitting,
            children: "Cancel"
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
            onClick: handleSubmit,
            disabled: isSubmitting,
            children: isSubmitting ? "Processing..." : "Confirm"
          })]
        })
      })]
    })
  });
}
export {
  SellerCustomers as default
};
//# sourceMappingURL=sellerSetting.customers-B9uiDZMr.js.map
