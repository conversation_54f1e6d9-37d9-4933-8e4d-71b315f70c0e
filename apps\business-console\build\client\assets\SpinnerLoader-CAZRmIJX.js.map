{"version": 3, "file": "SpinnerLoader-CAZRmIJX.js", "sources": ["../../../app/components/loader/SpinnerLoader.tsx"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\r\n\r\ninterface SpinnerLoaderProps {\r\n  loading: boolean;\r\n  size?: number\r\n}\r\n\r\nconst SpinnerLoader: React.FC<SpinnerLoaderProps> = ({ loading, size }) => {\r\n  const [active, setActive] = useState<boolean>(loading);\r\n\r\n  useEffect(() => {\r\n    setActive(loading);\r\n  }, [loading]);\r\n\r\n  return (\r\n    active && (\r\n      <div\r\n        role=\"progressbar\"\r\n        aria-valuetext={active ? \"Loading\" : undefined}\r\n        aria-hidden={!active}\r\n        className={`fixed inset-0 flex items-center justify-center bg-black bg-opacity-40 pointer-events-none z-50 p-4`}\r\n\r\n      >\r\n        <div className=\"p-6 text-center\">\r\n          <div className={`${size ? `w-${size} h-${size}` : \"w-16 h-16\"}  border-4 border-gray-200 border-t-4 border-t-primary rounded-full animate-spin mx-auto mb-4`}></div>\r\n        </div>\r\n      </div>\r\n    )\r\n  );\r\n};\r\n\r\nexport default SpinnerLoader;\r\n"], "names": ["useState", "useEffect", "jsx"], "mappings": ";AAOA,MAAM,gBAA8C,CAAC,EAAE,SAAS,WAAW;AACzE,QAAM,CAAC,QAAQ,SAAS,IAAIA,aAAAA,SAAkB,OAAO;AAErDC,eAAAA,UAAU,MAAM;AACd,cAAU,OAAO;AAAA,EAAA,GAChB,CAAC,OAAO,CAAC;AAEZ,SACE,UACEC,kCAAA;AAAA,IAAC;AAAA,IAAA;AAAA,MACC,MAAK;AAAA,MACL,kBAAgB,SAAS,YAAY;AAAA,MACrC,eAAa,CAAC;AAAA,MACd,WAAW;AAAA,MAGX,gDAAC,OAAI,EAAA,WAAU,mBACb,UAAAA,kCAAAA,IAAC,SAAI,WAAW,GAAG,OAAO,KAAK,IAAI,MAAM,IAAI,KAAK,WAAW,iGAAiG,EAChK,CAAA;AAAA,IAAA;AAAA,EACF;AAGN;"}