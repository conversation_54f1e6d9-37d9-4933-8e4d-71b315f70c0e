{"version": 3, "file": "home.networkManagement-CcSi5aJh.js", "sources": ["../../../app/components/ui/createnetWorkManagement.tsx", "../../../app/routes/home.networkManagement.tsx"], "sourcesContent": ["import { useEffect, useState } from \"react\";\r\nimport { useFetcher } from \"@remix-run/react\";\r\nimport { Dialog, DialogContent, DialogTitle } from \"./dialog\";\r\nimport { Seller } from \"~/types/api/businessConsoleService/MasterItemCategory\";\r\nimport SpinnerLoader from \"../loader/SpinnerLoader\";\r\nimport { useToast } from \"./ToastProvider\";\r\n\r\ninterface NetworkManagementProps {\r\n      isOpen: boolean;\r\n      onClose: () => void;\r\n      sellerList: Seller[];\r\n}\r\n\r\nconst NetworkManagementModal: React.FC<NetworkManagementProps> = ({\r\n      isOpen,\r\n      onClose,\r\n      sellerList,\r\n}) => {\r\n      const fetcher = useFetcher();\r\n      const { showToast } = useToast();\r\n      const [managerId, setManagerId] = useState<number | \"\">(\"\");\r\n      const [sellerId, setSellerId] = useState<number | \"\">(\"\");\r\n      const [formData, setFormData] = useState({\r\n            name: \"\",\r\n            description: \"\",\r\n            isPrivate: false,\r\n            managerId: \"\" as number | \"\",\r\n            domain: \"\",\r\n            networkType: [] as string[],\r\n            businessType: \"\",\r\n            defaultSellerId: \"\" as number | \"\",\r\n            multiSeller: \"\" as boolean | \"\",\r\n      });\r\n      const [networkError, setNetworkError] = useState(false);\r\n      const isLoading = fetcher.state !== \"idle\";\r\n      useEffect(() => {\r\n            if (fetcher.data) {\r\n                  if ((fetcher.data as any)?.success) {\r\n                        showToast(\"Network Created Successfully\", \"success\");\r\n                        onClose();\r\n                  } else {\r\n                        if ((fetcher.data as any)?.success === false) {\r\n                              showToast(\"Network Creation Failed\", \"error\");\r\n                        }\r\n                  }\r\n            }\r\n      }, [fetcher.state, fetcher.data, onClose]);\r\n\r\n      const handleChange = (\r\n            e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>\r\n      ) => {\r\n            const { name, value, type } = e.target;\r\n            const updatedValue =\r\n                  type === \"checkbox\" && e.target instanceof HTMLInputElement\r\n                        ? e.target.checked\r\n                        : value;\r\n\r\n            setFormData((prev) => ({\r\n                  ...prev,\r\n                  [name]: updatedValue,\r\n            }));\r\n      };\r\n\r\n      const handleManagerChange = (e: React.ChangeEvent<HTMLSelectElement>) => {\r\n            const selectedValue = e.target.value ? parseInt(e.target.value, 10) : \"\";\r\n            setManagerId(selectedValue);\r\n            setFormData((prev) => ({\r\n                  ...prev,\r\n                  managerId: selectedValue,\r\n            }));\r\n      };\r\n\r\n      const handleSellerChange = (e: React.ChangeEvent<HTMLSelectElement>) => {\r\n            const selectedValue = e.target.value ? parseInt(e.target.value, 10) : \"\";\r\n            setSellerId(selectedValue);\r\n            setFormData((prev) => ({\r\n                  ...prev,\r\n                  defaultSellerId: selectedValue,\r\n            }));\r\n      };\r\n\r\n      const handleNetworkTypeChange = (type: string) => {\r\n            setFormData((prev) => {\r\n                  const newNetworkType = [type]; // Set only the selected type\r\n                  setNetworkError(newNetworkType.length === 0);\r\n                  return { ...prev, networkType: newNetworkType };\r\n            });\r\n      };\r\n\r\n      const handleBusinessTypeChange = (type: string) => {\r\n            setFormData((prev) => ({\r\n                  ...prev,\r\n                  businessType: prev.businessType === type ? \"\" : type,\r\n            }));\r\n      };\r\n\r\n      const handleSubmit = (e: React.FormEvent) => {\r\n            e.preventDefault();\r\n\r\n            if (formData.networkType.length === 0) {\r\n                  setNetworkError(true);\r\n                  return;\r\n            }\r\n\r\n            if (formData.multiSeller === \"\" || formData.multiSeller === undefined) {\r\n                  showToast(\"Please select an option for MultiSeller\", \"error\");\r\n                  return;\r\n            }\r\n\r\n            fetcher.submit(formData, { method: \"post\" });\r\n      };\r\n\r\n      return (\r\n            <Dialog open={isOpen} onOpenChange={onClose}>\r\n                  <DialogContent className=\"max-h-[90vh] w-full max-w-2xl rounded-lg bg-white p-6 shadow-xl\">\r\n                        {isLoading && (\r\n                              <div className=\"absolute inset-0 flex items-center justify-center bg-white/50 backdrop-blur-sm\">\r\n                                    <SpinnerLoader loading={isLoading} />\r\n                              </div>\r\n                        )}\r\n                        <DialogTitle className=\"text-2xl font-semibold text-gray-800\">\r\n                              Create Network\r\n                        </DialogTitle>\r\n                        <form onSubmit={handleSubmit} className=\"space-y-6\">\r\n                              <div className=\"max-h-[60vh] overflow-y-auto pr-4\">\r\n                                    <div className=\"space-y-4\">\r\n                                          <div>\r\n                                                <label className=\"block text-sm font-medium text-gray-700\">\r\n                                                      Name\r\n                                                </label>\r\n                                                <input\r\n                                                      type=\"text\"\r\n                                                      name=\"name\"\r\n                                                      value={formData.name}\r\n                                                      onChange={handleChange}\r\n                                                      required\r\n                                                      className=\"mt-1 w-full rounded-md border border-gray-300 p-2 focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50\"\r\n                                                      placeholder=\"Enter network name\"\r\n                                                />\r\n                                          </div>\r\n\r\n                                          <div>\r\n                                                <label className=\"block text-sm font-medium text-gray-700\">\r\n                                                      Description\r\n                                                </label>\r\n                                                <textarea\r\n                                                      name=\"description\"\r\n                                                      value={formData.description}\r\n                                                      onChange={handleChange}\r\n                                                      required\r\n                                                      className=\"mt-1 w-full rounded-md border border-gray-300 p-2 focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50\"\r\n                                                      rows={4}\r\n                                                      placeholder=\"Describe the network\"\r\n                                                />\r\n                                          </div>\r\n\r\n                                          <div>\r\n                                                <label className=\"flex items-center space-x-2\">\r\n                                                      <input\r\n                                                            type=\"checkbox\"\r\n                                                            name=\"isPrivate\"\r\n                                                            checked={formData.isPrivate}\r\n                                                            onChange={handleChange}\r\n                                                            className=\"h-4 w-4 rounded text-blue-600 focus:ring-blue-500\"\r\n                                                      />\r\n                                                      <span className=\"text-sm text-gray-700\">Private Network</span>\r\n                                                </label>\r\n                                          </div>\r\n\r\n                                          <div>\r\n                                                <label className=\"block text-sm font-medium text-gray-700\">\r\n                                                      Manager\r\n                                                </label>\r\n                                                <select\r\n                                                      name=\"managerId\"\r\n                                                      value={managerId}\r\n                                                      onChange={handleManagerChange}\r\n                                                      required\r\n                                                      className=\"mt-1 w-full rounded-md border border-gray-300 p-2 focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50\"\r\n                                                >\r\n                                                      <option value=\"\">Select Manager</option>\r\n                                                      {sellerList?.map((seller) => (\r\n                                                            <option\r\n                                                                  key={seller?.id}\r\n                                                                  value={seller?.businessId?.toString()}\r\n                                                            >\r\n                                                                  {seller?.name}\r\n                                                            </option>\r\n                                                      ))}\r\n                                                </select>\r\n                                          </div>\r\n\r\n                                          <div>\r\n                                                <label className=\"block text-sm font-medium text-gray-700\">\r\n                                                      Domain URL\r\n                                                </label>\r\n                                                <input\r\n                                                      type=\"text\"\r\n                                                      name=\"domain\"\r\n                                                      value={formData.domain}\r\n                                                      onChange={handleChange}\r\n                                                      required\r\n                                                      className=\"mt-1 w-full rounded-md border border-gray-300 p-2 focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50\"\r\n                                                      placeholder=\"https://example.com\"\r\n                                                />\r\n                                          </div>\r\n\r\n                                          <div>\r\n                                                <label className=\"block text-sm font-medium text-gray-700\">\r\n                                                      Seller\r\n                                                </label>\r\n                                                <select\r\n                                                      name=\"sellerId\"\r\n                                                      value={sellerId}\r\n                                                      onChange={handleSellerChange}\r\n                                                      required\r\n                                                      className=\"mt-1 w-full rounded-md border border-gray-300 p-2 focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50\"\r\n                                                >\r\n                                                      <option value=\"\">Select Seller</option>\r\n                                                      {sellerList?.map((seller) => (\r\n                                                            <option key={seller?.id} value={seller?.id?.toString()}>\r\n                                                                  {seller?.name}\r\n                                                            </option>\r\n                                                      ))}\r\n                                                </select>\r\n                                          </div>\r\n\r\n                                          <div>\r\n                                                <label className=\"block text-sm font-medium text-gray-700\">\r\n                                                      Network Type\r\n                                                </label>\r\n                                                <div className=\"mt-2 flex space-x-6\">\r\n                                                      {[\"B2B\", \"B2C\"].map((type) => (\r\n                                                            <label key={type} className=\"flex items-center space-x-2\">\r\n                                                                  <input\r\n                                                                        type=\"radio\"\r\n                                                                        name=\"networkType\"\r\n                                                                        value={type}\r\n                                                                        checked={formData.networkType.includes(type)}\r\n                                                                        onChange={() => handleNetworkTypeChange(type)}\r\n                                                                        className=\"h-4 w-4 text-blue-600 focus:ring-blue-500\"\r\n                                                                  />\r\n                                                                  <span className=\"text-sm text-gray-700\">{type}</span>\r\n                                                            </label>\r\n                                                      ))}\r\n                                                </div>\r\n                                                {networkError && (\r\n                                                      <p className=\"mt-1 text-sm text-red-500\">\r\n                                                            Please select a network type.\r\n                                                      </p>\r\n                                                )}\r\n                                          </div>\r\n\r\n                                          <div>\r\n                                                <label className=\"block text-sm font-medium text-gray-700\">\r\n                                                      Business Type\r\n                                                </label>\r\n                                                <div className=\"mt-2 flex space-x-6\">\r\n                                                      {[\"Restaurant\", \"Non-Restaurant\"].map((type) => (\r\n                                                            <label key={type} className=\"flex items-center space-x-2\">\r\n                                                                  <input\r\n                                                                        type=\"checkbox\"\r\n                                                                        disabled={type == \"Restaurant\" && formData.networkType.includes(\"B2B\")}\r\n                                                                        checked={formData.businessType === type}\r\n                                                                        onChange={() => handleBusinessTypeChange(type)}\r\n                                                                        className=\"h-4 w-4 rounded text-blue-600 focus:ring-blue-500\"\r\n                                                                  />\r\n                                                                  <span className=\"text-sm text-gray-700\">{type}</span>\r\n                                                            </label>\r\n                                                      ))}\r\n                                                </div>\r\n                                          </div>\r\n\r\n                                          <div>\r\n                                                <label className=\"block text-sm font-medium text-gray-700\">\r\n                                                      MultiSeller\r\n                                                </label>\r\n                                                <div className=\"mt-2 flex space-x-6\">\r\n                                                      {[\r\n                                                            { label: \"Yes\", value: true },\r\n                                                            { label: \"No\", value: false },\r\n                                                      ].map((option) => (\r\n                                                            <label\r\n                                                                  key={option.label}\r\n                                                                  className=\"flex items-center space-x-2\"\r\n                                                            >\r\n                                                                  <input\r\n                                                                        type=\"radio\"\r\n                                                                        name=\"multiSeller\"\r\n                                                                        value={option.value.toString()}\r\n                                                                        checked={formData.multiSeller === option.value}\r\n                                                                        onChange={() =>\r\n                                                                              setFormData((prev) => ({\r\n                                                                                    ...prev,\r\n                                                                                    multiSeller: option.value,\r\n                                                                              }))\r\n                                                                        }\r\n                                                                        className=\"h-4 w-4 text-blue-600 focus:ring-blue-500\"\r\n                                                                  />\r\n                                                                  <span className=\"text-sm text-gray-700\">\r\n                                                                        {option.label}\r\n                                                                  </span>\r\n                                                            </label>\r\n                                                      ))}\r\n                                                </div>\r\n                                                {(formData.multiSeller === \"\" ||\r\n                                                      formData.multiSeller === undefined) && (\r\n                                                            <p className=\"mt-1 text-sm text-red-500\">\r\n                                                                  Please select an option for MultiSeller.\r\n                                                            </p>\r\n                                                      )}\r\n                                          </div>\r\n                                    </div>\r\n                              </div>\r\n\r\n                              <div className=\"flex justify-end space-x-3 pt-4\">\r\n                                    <button\r\n                                          type=\"button\"\r\n                                          onClick={onClose}\r\n                                          className=\"rounded-md bg-gray-200 px-4 py-2 text-sm font-medium text-gray-700 transition-colors hover:bg-gray-300 focus:outline-none focus:ring focus:ring-gray-200 focus:ring-opacity-50\"\r\n                                    >\r\n                                          Cancel\r\n                                    </button>\r\n                                    <button\r\n                                          type=\"submit\"\r\n                                          disabled={isLoading}\r\n                                          className=\"rounded-md bg-blue-600 px-4 py-2 text-sm font-medium text-white transition-colors hover:bg-blue-700 focus:outline-none focus:ring focus:ring-blue-200 focus:ring-opacity-50 disabled:bg-blue-400\"\r\n                                    >\r\n                                          {isLoading ? \"Creating...\" : \"Create\"}\r\n                                    </button>\r\n                              </div>\r\n                        </form>\r\n                  </DialogContent>\r\n            </Dialog>\r\n      );\r\n};\r\n\r\nexport default NetworkManagementModal;", "'use client'\r\n\r\nimport { json } from \"@remix-run/node\";\r\nimport { use<PERSON><PERSON><PERSON>, useLoaderData, useNavigate } from \"@remix-run/react\";\r\nimport * as React from \"react\";\r\nimport { getNetworks } from \"~/services/businessConsoleService\";\r\nimport { Networks } from \"~/types/api/businessConsoleService/Network\";\r\nimport { withAuth, withResponse } from \"~/utils/auth-utils\";\r\nimport { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from \"~/components/ui/table\";\r\nimport { Edit, Search } from \"lucide-react\";\r\nimport { Button } from \"~/components/ui/button\";\r\nimport { useEffect, useState } from \"react\";\r\nimport NetworkManagementModal from \"~/components/ui/createnetWorkManagement\";\r\nimport { createNetwork } from \"~/services/netWorks\";\r\nimport { Seller } from \"~/types/api/businessConsoleService/SellerManagement\";\r\nimport { getSellerList } from \"~/services/masterItemCategories\";\r\nimport { Input } from \"~/components/ui/input\";\r\nimport { useDebounce } from \"~/hooks/useDebounce\";\r\nimport SpinnerLoader from \"~/components/loader/SpinnerLoader\";\r\n\r\n\r\nexport type CreateNetwork = Omit<Networks, \"id\">;\r\n\r\ninterface LoaderData {\r\n  netWorkData: Networks[],\r\n  sellerList: Seller[],\r\n  permissions: string[]\r\n\r\n}\r\n\r\nexport interface ActionData {\r\n  success?: boolean,\r\n  error?: string\r\n}\r\n\r\nexport const loader = withAuth(async ({ request, user }) => {\r\n  try {\r\n    console.log(\"loader entered\");\r\n    const response = await getNetworks(request);\r\n    const responseSeller = await getSellerList(request)\r\n    const permission = user?.userDetails?.roles\r\n\r\n    return withResponse({\r\n      netWorkData: response.data,\r\n      sellerList: responseSeller.data,\r\n      permissions: permission\r\n    }, response.headers);\r\n\r\n  } catch (error) {\r\n    console.log(\"loader failed\");\r\n    console.error(\"Error in loader:\", error);\r\n    // Return a JSON-based error shape\r\n    throw new Response('failed to getSeller', { status: 500 })\r\n\r\n  }\r\n\r\n});\r\n\r\nexport async function action({ request }: { request: Request }) {\r\n  const formData = await request.formData();\r\n  const businessType = formData.get(\"businessType\") as string;\r\n\r\n\r\n  const ondcDomain = businessType === \"Restaurant\" ? \"RET11\" : \"RET10\"\r\n\r\n  const data: any = {\r\n    name: formData.get(\"name\") as string,\r\n    description: formData.get(\"description\") as string,\r\n    isPrivate: formData.get(\"isPrivate\") === \"true\",\r\n    managerId: Number(formData.get(\"managerId\")),\r\n    networkType: formData.get(\"networkType\") as string,\r\n    domain: formData.get(\"domain\") as string,\r\n    ondcDomain: ondcDomain,\r\n    defaultSellerId: Number(formData.get(\"defaultSellerId\")),\r\n    multiSeller: formData.get(\"multiSeller\") as unknown as boolean\r\n  };\r\n  try {\r\n    const response = await createNetwork(data, request);\r\n    return withResponse({ success: response.statusCode == 200 }, response.headers)\r\n  }\r\n  catch (error) {\r\n    console.log(\"loader failed\");\r\n    console.error(\"Error in action:\", error);\r\n\r\n    throw new Response('failed to createSeller', { status: 500 })\r\n  }\r\n}\r\nexport default function NetworksPage() {\r\n  const goTo = useNavigate();\r\n  const { netWorkData, sellerList, permissions } = useLoaderData<LoaderData>();\r\n  const salesPermissions = permissions?.includes(\"FmSalesManager\") ?? false\r\n  const dataleng = netWorkData.length;\r\n  const [isModalOpen, setIsModalOpen] = useState(false);\r\n  const [searchTerm, setSearchTerm] = useState('');\r\n  const [networkFilter, setNetworkFilter] = useState<Networks[]>([]);\r\n  const handleSearch = (val: string) => {\r\n    setSearchTerm(val)\r\n\r\n  }\r\n  const fetcher = useFetcher<{ networkData: Networks[] }>()\r\n  const loading = fetcher.state !== \"idle\";\r\n  const debouncedSearchTerm = useDebounce(searchTerm, 500);\r\n\r\n  useEffect(() => {\r\n    if (netWorkData) {\r\n      setNetworkFilter(netWorkData);\r\n    }\r\n  }, [netWorkData, fetcher?.data]);\r\n\r\n  useEffect(() => {\r\n    if (debouncedSearchTerm.length >= 3 && debouncedSearchTerm !== \"\") {\r\n\r\n      const filterdata = netWorkData?.filter(item => item.name.toLowerCase().includes(debouncedSearchTerm.toLowerCase()))\r\n\r\n      setNetworkFilter(filterdata)\r\n    }\r\n    else {\r\n      setNetworkFilter(netWorkData)\r\n    }\r\n\r\n  }, [debouncedSearchTerm, netWorkData])\r\n\r\n\r\n\r\n\r\n  return (\r\n    <div className=\"container mx-auto p-6\">\r\n      <h1 className=\"text-2xl font-bold mb-4\">Network Management</h1> <div className=\"flex flex-col my-3\">\r\n        <div className=\"relative mx-10 flex flex-col \">\r\n          <Search className='absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400' />\r\n          <Input\r\n            type='search'\r\n            placeholder=\"Search by Network Name\"\r\n            value={searchTerm}\r\n            onChange={(e: { target: { value: string; }; }) => handleSearch(e.target.value)}\r\n            className=\"max-w-sm rounded-full pl-10\"\r\n          />\r\n        </div>\r\n      </div>\r\n\r\n      <Table>\r\n        {/* Table header */}\r\n        <TableHeader className=\"bg-gray-100\">\r\n          <TableRow>\r\n            <TableHead>ID</TableHead>\r\n            <TableHead>Name</TableHead>\r\n            <TableHead>Description</TableHead>\r\n            <TableHead>Manager Business ID</TableHead>\r\n            <TableHead>Manager Name</TableHead>\r\n            {/* An extra column for any actions, e.g., Edit */}\r\n            <TableHead></TableHead>\r\n          </TableRow>\r\n        </TableHeader>\r\n\r\n        {/* Table body */}\r\n        <TableBody>\r\n          {loading && <SpinnerLoader loading={loading} size={20} />}\r\n\r\n          {networkFilter?.length > 0 ? (\r\n            networkFilter.map((network) => (\r\n              <TableRow key={network.id}>\r\n                <TableCell>{network.id}</TableCell>\r\n                <TableCell className={`cursor-pointer text-blue-500 font-bold  items-center`}\r\n                  onClick={() =>\r\n                    goTo(`/home/<USER>\n                  }                                    >{network.name}</TableCell>\r\n                <TableCell>{network.description}</TableCell>\r\n                <TableCell>{network.managerBusinessId}</TableCell>\r\n                <TableCell>{network.managerName}</TableCell>\r\n              </TableRow>\r\n            ))\r\n          ) : (\r\n            <TableRow>\r\n              <TableCell colSpan={6} className=\"h-24 text-center\">\r\n                No results.\r\n              </TableCell>\r\n            </TableRow>\r\n          )}\r\n        </TableBody>\r\n      </Table>\r\n\r\n      {!salesPermissions && <Button className=\"fixed bottom-5 right-5 rounded-full cursor-pointer\" onClick={() => setIsModalOpen(true)}>+ Add Network</Button>\r\n      }      <NetworkManagementModal isOpen={isModalOpen} onClose={() => setIsModalOpen(false)} sellerList={sellerList} />\r\n\r\n    </div>\r\n  );\r\n}"], "names": ["useState", "useEffect", "jsx", "jsxs", "NetworksPage", "goTo", "useNavigate", "netWorkData", "sellerList", "permissions", "useLoaderData", "salesPermissions", "includes", "length", "isModalOpen", "setIsModalOpen", "searchTerm", "setSearchTerm", "networkFilter", "setNetworkFilter", "handleSearch", "val", "fetcher", "useFetcher", "loading", "state", "debouncedSearchTerm", "useDebounce", "data", "filterdata", "filter", "item", "name", "toLowerCase", "className", "children", "Search", "Input", "type", "placeholder", "value", "onChange", "e", "target", "Table", "TableHeader", "TableRow", "TableHead", "TableBody", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "size", "map", "network", "TableCell", "id", "onClick", "description", "managerBusinessId", "<PERSON><PERSON><PERSON>", "colSpan", "<PERSON><PERSON>", "NetworkManagementModal", "isOpen", "onClose"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAaA,MAAM,yBAA2D,CAAC;AAAA,EAC5D;AAAA,EACA;AAAA,EACA;AACN,MAAM;AACA,QAAM,UAAU,WAAW;AACrB,QAAA,EAAE,UAAU,IAAI,SAAS;AAC/B,QAAM,CAAC,WAAW,YAAY,IAAIA,aAAAA,SAAsB,EAAE;AAC1D,QAAM,CAAC,UAAU,WAAW,IAAIA,aAAAA,SAAsB,EAAE;AACxD,QAAM,CAAC,UAAU,WAAW,IAAIA,sBAAS;AAAA,IACnC,MAAM;AAAA,IACN,aAAa;AAAA,IACb,WAAW;AAAA,IACX,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,aAAa,CAAC;AAAA,IACd,cAAc;AAAA,IACd,iBAAiB;AAAA,IACjB,aAAa;AAAA,EAAA,CAClB;AACD,QAAM,CAAC,cAAc,eAAe,IAAIA,aAAAA,SAAS,KAAK;AAChD,QAAA,YAAY,QAAQ,UAAU;AACpCC,eAAAA,UAAU,MAAM;;AACV,QAAI,QAAQ,MAAM;AACP,WAAA,aAAQ,SAAR,mBAAsB,SAAS;AAC9B,kBAAU,gCAAgC,SAAS;AAC3C,gBAAA;AAAA,MAAA,OACP;AACI,cAAA,aAAQ,SAAR,mBAAsB,aAAY,OAAO;AACxC,oBAAU,2BAA2B,OAAO;AAAA,QAAA;AAAA,MAClD;AAAA,IACN;AAAA,EACN,GACH,CAAC,QAAQ,OAAO,QAAQ,MAAM,OAAO,CAAC;AAEnC,QAAA,eAAe,CACf,MACD;AACC,UAAM,EAAE,MAAM,OAAO,SAAS,EAAE;AAC1B,UAAA,eACA,SAAS,cAAc,EAAE,kBAAkB,mBACnC,EAAE,OAAO,UACT;AAEd,gBAAY,CAAC,UAAU;AAAA,MACjB,GAAG;AAAA,MACH,CAAC,IAAI,GAAG;AAAA,IAAA,EACZ;AAAA,EACR;AAEM,QAAA,sBAAsB,CAAC,MAA4C;AAC7D,UAAA,gBAAgB,EAAE,OAAO,QAAQ,SAAS,EAAE,OAAO,OAAO,EAAE,IAAI;AACtE,iBAAa,aAAa;AAC1B,gBAAY,CAAC,UAAU;AAAA,MACjB,GAAG;AAAA,MACH,WAAW;AAAA,IAAA,EACf;AAAA,EACR;AAEM,QAAA,qBAAqB,CAAC,MAA4C;AAC5D,UAAA,gBAAgB,EAAE,OAAO,QAAQ,SAAS,EAAE,OAAO,OAAO,EAAE,IAAI;AACtE,gBAAY,aAAa;AACzB,gBAAY,CAAC,UAAU;AAAA,MACjB,GAAG;AAAA,MACH,iBAAiB;AAAA,IAAA,EACrB;AAAA,EACR;AAEM,QAAA,0BAA0B,CAAC,SAAiB;AAC5C,gBAAY,CAAC,SAAS;AACV,YAAA,iBAAiB,CAAC,IAAI;AACZ,sBAAA,eAAe,WAAW,CAAC;AAC3C,aAAO,EAAE,GAAG,MAAM,aAAa,eAAe;AAAA,IAAA,CACnD;AAAA,EACP;AAEM,QAAA,2BAA2B,CAAC,SAAiB;AAC7C,gBAAY,CAAC,UAAU;AAAA,MACjB,GAAG;AAAA,MACH,cAAc,KAAK,iBAAiB,OAAO,KAAK;AAAA,IAAA,EACpD;AAAA,EACR;AAEM,QAAA,eAAe,CAAC,MAAuB;AACvC,MAAE,eAAe;AAEb,QAAA,SAAS,YAAY,WAAW,GAAG;AACjC,sBAAgB,IAAI;AACpB;AAAA,IAAA;AAGN,QAAI,SAAS,gBAAgB,MAAM,SAAS,gBAAgB,QAAW;AACjE,gBAAU,2CAA2C,OAAO;AAC5D;AAAA,IAAA;AAGN,YAAQ,OAAO,UAAU,EAAE,QAAQ,QAAQ;AAAA,EACjD;AAGM,SAAAC,kCAAA,IAAC,UAAO,MAAM,QAAQ,cAAc,SAC9B,UAAAC,kCAAA,KAAC,eAAc,EAAA,WAAU,mEAClB,UAAA;AAAA,IACK,aAAAD,kCAAAA,IAAC,SAAI,WAAU,kFACT,gDAAC,eAAc,EAAA,SAAS,WAAW,EACzC,CAAA;AAAA,IAELA,kCAAA,IAAA,aAAA,EAAY,WAAU,wCAAuC,UAE9D,kBAAA;AAAA,IACCC,kCAAA,KAAA,QAAA,EAAK,UAAU,cAAc,WAAU,aAClC,UAAA;AAAA,MAAAD,kCAAAA,IAAC,SAAI,WAAU,qCACT,UAACC,kCAAA,KAAA,OAAA,EAAI,WAAU,aACT,UAAA;AAAA,QAAAA,uCAAC,OACK,EAAA,UAAA;AAAA,UAACD,kCAAA,IAAA,SAAA,EAAM,WAAU,2CAA0C,UAE3D,QAAA;AAAA,UACAA,kCAAA;AAAA,YAAC;AAAA,YAAA;AAAA,cACK,MAAK;AAAA,cACL,MAAK;AAAA,cACL,OAAO,SAAS;AAAA,cAChB,UAAU;AAAA,cACV,UAAQ;AAAA,cACR,WAAU;AAAA,cACV,aAAY;AAAA,YAAA;AAAA,UAAA;AAAA,QAClB,GACN;AAAA,+CAEC,OACK,EAAA,UAAA;AAAA,UAACA,kCAAA,IAAA,SAAA,EAAM,WAAU,2CAA0C,UAE3D,eAAA;AAAA,UACAA,kCAAA;AAAA,YAAC;AAAA,YAAA;AAAA,cACK,MAAK;AAAA,cACL,OAAO,SAAS;AAAA,cAChB,UAAU;AAAA,cACV,UAAQ;AAAA,cACR,WAAU;AAAA,cACV,MAAM;AAAA,cACN,aAAY;AAAA,YAAA;AAAA,UAAA;AAAA,QAClB,GACN;AAAA,QAECA,sCAAA,OAAA,EACK,UAACC,kCAAAA,KAAA,SAAA,EAAM,WAAU,+BACX,UAAA;AAAA,UAAAD,kCAAA;AAAA,YAAC;AAAA,YAAA;AAAA,cACK,MAAK;AAAA,cACL,MAAK;AAAA,cACL,SAAS,SAAS;AAAA,cAClB,UAAU;AAAA,cACV,WAAU;AAAA,YAAA;AAAA,UAChB;AAAA,UACCA,kCAAA,IAAA,QAAA,EAAK,WAAU,yBAAwB,UAAe,kBAAA,CAAA;AAAA,QAAA,EAAA,CAC7D,EACN,CAAA;AAAA,+CAEC,OACK,EAAA,UAAA;AAAA,UAACA,kCAAA,IAAA,SAAA,EAAM,WAAU,2CAA0C,UAE3D,WAAA;AAAA,UACAC,kCAAA;AAAA,YAAC;AAAA,YAAA;AAAA,cACK,MAAK;AAAA,cACL,OAAO;AAAA,cACP,UAAU;AAAA,cACV,UAAQ;AAAA,cACR,WAAU;AAAA,cAEV,UAAA;AAAA,gBAACD,kCAAA,IAAA,UAAA,EAAO,OAAM,IAAG,UAAc,kBAAA;AAAA,gBAC9B,yCAAY,IAAI,CAAC;;AACZA,2DAAA;AAAA,oBAAC;AAAA,oBAAA;AAAA,sBAEK,QAAO,sCAAQ,eAAR,mBAAoB;AAAA,sBAE1B,UAAQ,iCAAA;AAAA,oBAAA;AAAA,oBAHJ,iCAAQ;AAAA,kBAKxB;AAAA;AAAA,cAAA;AAAA,YAAA;AAAA,UAAA;AAAA,QACP,GACN;AAAA,+CAEC,OACK,EAAA,UAAA;AAAA,UAACA,kCAAA,IAAA,SAAA,EAAM,WAAU,2CAA0C,UAE3D,cAAA;AAAA,UACAA,kCAAA;AAAA,YAAC;AAAA,YAAA;AAAA,cACK,MAAK;AAAA,cACL,MAAK;AAAA,cACL,OAAO,SAAS;AAAA,cAChB,UAAU;AAAA,cACV,UAAQ;AAAA,cACR,WAAU;AAAA,cACV,aAAY;AAAA,YAAA;AAAA,UAAA;AAAA,QAClB,GACN;AAAA,+CAEC,OACK,EAAA,UAAA;AAAA,UAACA,kCAAA,IAAA,SAAA,EAAM,WAAU,2CAA0C,UAE3D,UAAA;AAAA,UACAC,kCAAA;AAAA,YAAC;AAAA,YAAA;AAAA,cACK,MAAK;AAAA,cACL,OAAO;AAAA,cACP,UAAU;AAAA,cACV,UAAQ;AAAA,cACR,WAAU;AAAA,cAEV,UAAA;AAAA,gBAACD,kCAAA,IAAA,UAAA,EAAO,OAAM,IAAG,UAAa,iBAAA;AAAA,gBAC7B,yCAAY,IAAI,CAAC;;+DACX,UAAwB,EAAA,QAAO,sCAAQ,OAAR,mBAAY,YACrC,UAAA,iCAAQ,KADF,GAAA,iCAAQ,EAErB;AAAA;AAAA,cACL;AAAA,YAAA;AAAA,UAAA;AAAA,QACP,GACN;AAAA,+CAEC,OACK,EAAA,UAAA;AAAA,UAACA,kCAAA,IAAA,SAAA,EAAM,WAAU,2CAA0C,UAE3D,gBAAA;AAAA,UACCA,kCAAA,IAAA,OAAA,EAAI,WAAU,uBACR,WAAC,OAAO,KAAK,EAAE,IAAI,CAAC,SACdC,uCAAA,SAAA,EAAiB,WAAU,+BACtB,UAAA;AAAA,YAAAD,kCAAA;AAAA,cAAC;AAAA,cAAA;AAAA,gBACK,MAAK;AAAA,gBACL,MAAK;AAAA,gBACL,OAAO;AAAA,gBACP,SAAS,SAAS,YAAY,SAAS,IAAI;AAAA,gBAC3C,UAAU,MAAM,wBAAwB,IAAI;AAAA,gBAC5C,WAAU;AAAA,cAAA;AAAA,YAChB;AAAA,YACCA,kCAAA,IAAA,QAAA,EAAK,WAAU,yBAAyB,UAAK,KAAA,CAAA;AAAA,UAAA,KATxC,IAUZ,CACL,GACP;AAAA,UACC,gBACKA,kCAAA,IAAC,KAAE,EAAA,WAAU,6BAA4B,UAEzC,gCAAA,CAAA;AAAA,QAAA,GAEZ;AAAA,+CAEC,OACK,EAAA,UAAA;AAAA,UAACA,kCAAA,IAAA,SAAA,EAAM,WAAU,2CAA0C,UAE3D,iBAAA;AAAA,UACCA,kCAAA,IAAA,OAAA,EAAI,WAAU,uBACR,WAAC,cAAc,gBAAgB,EAAE,IAAI,CAAC,SAChCC,uCAAA,SAAA,EAAiB,WAAU,+BACtB,UAAA;AAAA,YAAAD,kCAAA;AAAA,cAAC;AAAA,cAAA;AAAA,gBACK,MAAK;AAAA,gBACL,UAAU,QAAQ,gBAAgB,SAAS,YAAY,SAAS,KAAK;AAAA,gBACrE,SAAS,SAAS,iBAAiB;AAAA,gBACnC,UAAU,MAAM,yBAAyB,IAAI;AAAA,gBAC7C,WAAU;AAAA,cAAA;AAAA,YAChB;AAAA,YACCA,kCAAA,IAAA,QAAA,EAAK,WAAU,yBAAyB,UAAK,KAAA,CAAA;AAAA,UAAA,EARxC,GAAA,IASZ,CACL,EACP,CAAA;AAAA,QAAA,GACN;AAAA,+CAEC,OACK,EAAA,UAAA;AAAA,UAACA,kCAAA,IAAA,SAAA,EAAM,WAAU,2CAA0C,UAE3D,eAAA;AAAA,UACAA,kCAAAA,IAAC,OAAI,EAAA,WAAU,uBACR,UAAA;AAAA,YACK,EAAE,OAAO,OAAO,OAAO,KAAK;AAAA,YAC5B,EAAE,OAAO,MAAM,OAAO,MAAM;AAAA,UAAA,EAChC,IAAI,CAAC,WACDC,kCAAA;AAAA,YAAC;AAAA,YAAA;AAAA,cAEK,WAAU;AAAA,cAEV,UAAA;AAAA,gBAAAD,kCAAA;AAAA,kBAAC;AAAA,kBAAA;AAAA,oBACK,MAAK;AAAA,oBACL,MAAK;AAAA,oBACL,OAAO,OAAO,MAAM,SAAS;AAAA,oBAC7B,SAAS,SAAS,gBAAgB,OAAO;AAAA,oBACzC,UAAU,MACJ,YAAY,CAAC,UAAU;AAAA,sBACjB,GAAG;AAAA,sBACH,aAAa,OAAO;AAAA,oBAAA,EACxB;AAAA,oBAER,WAAU;AAAA,kBAAA;AAAA,gBAChB;AAAA,gBACCA,kCAAA,IAAA,QAAA,EAAK,WAAU,yBACT,iBAAO,MACd,CAAA;AAAA,cAAA;AAAA,YAAA;AAAA,YAlBK,OAAO;AAAA,UAoBvB,CAAA,GACP;AAAA,WACE,SAAS,gBAAgB,MACrB,SAAS,gBAAgB,WAClBA,kCAAA,IAAA,KAAA,EAAE,WAAU,6BAA4B,UAEzC,2CAAA,CAAA;AAAA,QAAA,EAElB,CAAA;AAAA,MAAA,EAAA,CACN,EACN,CAAA;AAAA,MAEAC,kCAAAA,KAAC,OAAI,EAAA,WAAU,mCACT,UAAA;AAAA,QAAAD,kCAAA;AAAA,UAAC;AAAA,UAAA;AAAA,YACK,MAAK;AAAA,YACL,SAAS;AAAA,YACT,WAAU;AAAA,YACf,UAAA;AAAA,UAAA;AAAA,QAED;AAAA,QACAA,kCAAA;AAAA,UAAC;AAAA,UAAA;AAAA,YACK,MAAK;AAAA,YACL,UAAU;AAAA,YACV,WAAU;AAAA,YAET,sBAAY,gBAAgB;AAAA,UAAA;AAAA,QAAA;AAAA,MACnC,EACN,CAAA;AAAA,IAAA,EACN,CAAA;AAAA,EAAA,EAAA,CACN,EACN,CAAA;AAEZ;ACxPA,SAAwBE,eAAe;AACrC,QAAMC,OAAOC,YAAY;AACzB,QAAM;AAAA,IAAEC;AAAAA,IAAaC;AAAAA,IAAYC;AAAAA,MAAgBC,cAA0B;AAC3E,QAAMC,oBAAmBF,2CAAaG,SAAS,sBAAqB;AACnDL,cAAYM;AAC7B,QAAM,CAACC,aAAaC,cAAc,IAAIf,aAAAA,SAAS,KAAK;AACpD,QAAM,CAACgB,YAAYC,aAAa,IAAIjB,aAAAA,SAAS,EAAE;AAC/C,QAAM,CAACkB,eAAeC,gBAAgB,IAAInB,aAAAA,SAAqB,CAAA,CAAE;AAC3D,QAAAoB,eAAgBC,SAAgB;AACpCJ,kBAAcI,GAAG;AAAA,EAEnB;AACA,QAAMC,UAAUC,WAAwC;AAClD,QAAAC,UAAUF,QAAQG,UAAU;AAC5B,QAAAC,sBAAsBC,YAAYX,YAAY,GAAG;AAEvDf,eAAAA,UAAU,MAAM;AACd,QAAIM,aAAa;AACfY,uBAAiBZ,WAAW;AAAA,IAC9B;AAAA,EACC,GAAA,CAACA,aAAae,mCAASM,IAAI,CAAC;AAE/B3B,eAAAA,UAAU,MAAM;AACd,QAAIyB,oBAAoBb,UAAU,KAAKa,wBAAwB,IAAI;AAEjE,YAAMG,aAAatB,2CAAauB,OAAOC,UAAQA,KAAKC,KAAKC,YAAY,EAAErB,SAASc,oBAAoBO,YAAa,CAAA;AAEjHd,uBAAiBU,UAAU;AAAA,IAC7B,OACK;AACHV,uBAAiBZ,WAAW;AAAA,IAC9B;AAAA,EAEF,GAAG,CAACmB,qBAAqBnB,WAAW,CAAC;AAMnC,SAAAJ,kCAAAA,KAAC,OAAI;AAAA,IAAA+B,WAAU;AAAA,IACbC,UAAA,CAACjC,kCAAA,IAAA,MAAA;AAAA,MAAGgC,WAAU;AAAA,MAA0BC,UAAkB;AAAA,KAAA,GAAK,2CAAE,OAAI;AAAA,MAAAD,WAAU;AAAA,MAC7EC,UAAChC,kCAAA,KAAA,OAAA;AAAA,QAAI+B,WAAU;AAAA,QACbC,UAAA,CAACjC,kCAAA,IAAAkC,QAAA;AAAA,UAAOF,WAAU;AAAA,QAAmE,CAAA,GACrFhC,kCAAA,IAACmC,OAAA;AAAA,UACCC,MAAK;AAAA,UACLC,aAAY;AAAA,UACZC,OAAOxB;AAAAA,UACPyB,UAAWC,OAAuCtB,aAAasB,EAAEC,OAAOH,KAAK;AAAA,UAC7EN,WAAU;AAAA,QAAA,CACZ,CAAA;AAAA,MACF,CAAA;AAAA,IACF,CAAA,0CAECU,OAEC;AAAA,MAAAT,UAAA,CAAAjC,kCAAA,IAAC2C,aAAY;AAAA,QAAAX,WAAU;AAAA,QACrBC,UAAAhC,kCAAA,KAAC2C,UACC;AAAA,UAAAX,UAAA,CAAAjC,kCAAA,IAAC6C;YAAUZ,UAAE;AAAA,UAAA,CAAA,GACbjC,kCAAA,IAAC6C;YAAUZ,UAAI;AAAA,UAAA,CAAA,GACfjC,kCAAA,IAAC6C;YAAUZ,UAAW;AAAA,UAAA,CAAA,GACtBjC,kCAAA,IAAC6C;YAAUZ,UAAmB;AAAA,UAAA,CAAA,GAC9BjC,kCAAA,IAAC6C;YAAUZ,UAAY;AAAA,WAAA,yCAEtBY,WAAU,EAAA,CAAA;AAAA,QACb,CAAA;AAAA,MACF,CAAA,0CAGCC,WACE;AAAA,QAAAb,UAAA,CAAAX,WAAYtB,kCAAA,IAAA+C,eAAA;AAAA,UAAczB;AAAAA,UAAkB0B,MAAM;AAAA,QAAI,CAAA,IAEtDhC,+CAAeL,UAAS,IACvBK,cAAciC,IAAKC,oDAChBN,UACC;AAAA,UAAAX,UAAA,CAACjC,kCAAA,IAAAmD,WAAA;AAAA,YAAWlB,kBAAQmB;AAAAA,UAAG,CAAA,GACvBpD,kCAAA,IAACmD,WAAA;AAAA,YAAUnB,WAAW;AAAA,YACpBqB,SAASA,MACPlD,KAAK,kCAAkC+C,QAAQE,EAAE,gBAAgBF,QAAQpB,IAAI,EAAE;AAAA,YAC1CG,UAAQiB,QAAApB;AAAAA,UAAA,CAAK,GACtD9B,kCAAA,IAACmD,WAAW;AAAA,YAAAlB,UAAAiB,QAAQI;AAAAA,UAAY,CAAA,GAChCtD,kCAAA,IAACmD,WAAW;AAAA,YAAAlB,UAAAiB,QAAQK;AAAAA,UAAkB,CAAA,GACtCvD,kCAAA,IAACmD,WAAW;AAAA,YAAAlB,UAAAiB,QAAQM;AAAAA,UAAY,CAAA,CAAA;AAAA,QAAA,GARnBN,QAAQE,EASvB,CACD,IAEApD,kCAAAA,IAAA4C,UAAA;AAAA,UACCX,UAACjC,kCAAA,IAAAmD,WAAA;AAAA,YAAUM,SAAS;AAAA,YAAGzB,WAAU;AAAA,YAAmBC;UAEpD,CAAA;AAAA,QACF,CAAA,CAAA;AAAA,MAEJ,CAAA,CAAA;AAAA,IACF,CAAA,GAEC,CAACxB,oBAAoBT,kCAAA,IAAC0D,QAAO;AAAA,MAAA1B,WAAU;AAAA,MAAqDqB,SAASA,MAAMxC,eAAe,IAAI;AAAA,MAAGoB,UAAa;AAAA,KAAA,GAC9I,UAAMjC,kCAAA,IAAC2D;MAAuBC,QAAQhD;AAAAA,MAAaiD,SAASA,MAAMhD,eAAe,KAAK;AAAA,MAAGP;AAAAA,IAAwB,CAAA,CAAA;AAAA,EAEpH,CAAA;AAEJ;"}