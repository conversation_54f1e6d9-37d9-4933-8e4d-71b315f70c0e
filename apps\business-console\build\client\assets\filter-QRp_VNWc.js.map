{"version": 3, "file": "filter-QRp_VNWc.js", "sources": ["../../../node_modules/lucide-react/dist/esm/icons/filter.js"], "sourcesContent": ["/**\n * @license lucide-react v0.453.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst Filter = createLucideIcon(\"Filter\", [\n  [\"polygon\", { points: \"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3\", key: \"1yg77f\" }]\n]);\n\nexport { Filter as default };\n//# sourceMappingURL=filter.js.map\n"], "names": [], "mappings": ";AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASK,MAAC,SAAS,iBAAiB,UAAU;AAAA,EACxC,CAAC,WAAW,EAAE,QAAQ,+CAA+C,KAAK,SAAU,CAAA;AACtF,CAAC;", "x_google_ignoreList": [0]}