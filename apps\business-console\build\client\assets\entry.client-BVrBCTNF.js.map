{"version": 3, "file": "entry.client-BVrBCTNF.js", "sources": ["../../../node_modules/@remix-run/react/dist/esm/errors.js", "../../../node_modules/@remix-run/react/dist/esm/browser.js", "../../../node_modules/react-dom/client.js", "../../../app/entry.client.tsx"], "sourcesContent": ["/**\n * @remix-run/react v2.15.3\n *\n * Copyright (c) Remix Software Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE.md file in the root directory of this source tree.\n *\n * @license MIT\n */\nimport { UNSAFE_ErrorResponseImpl } from '@remix-run/router';\n\nfunction deserializeErrors(errors) {\n  if (!errors) return null;\n  let entries = Object.entries(errors);\n  let serialized = {};\n  for (let [key, val] of entries) {\n    // Hey you!  If you change this, please change the corresponding logic in\n    // serializeErrors in remix-server-runtime/errors.ts :)\n    if (val && val.__type === \"RouteErrorResponse\") {\n      serialized[key] = new UNSAFE_ErrorResponseImpl(val.status, val.statusText, val.data, val.internal === true);\n    } else if (val && val.__type === \"Error\") {\n      // Attempt to reconstruct the right type of Error (i.e., ReferenceError)\n      if (val.__subType) {\n        let ErrorConstructor = window[val.__subType];\n        if (typeof ErrorConstructor === \"function\") {\n          try {\n            // @ts-expect-error\n            let error = new ErrorConstructor(val.message);\n            error.stack = val.stack;\n            serialized[key] = error;\n          } catch (e) {\n            // no-op - fall through and create a normal Error\n          }\n        }\n      }\n      if (serialized[key] == null) {\n        let error = new Error(val.message);\n        error.stack = val.stack;\n        serialized[key] = error;\n      }\n    } else {\n      serialized[key] = val;\n    }\n  }\n  return serialized;\n}\n\nexport { deserializeErrors };\n", "/**\n * @remix-run/react v2.15.3\n *\n * Copyright (c) Remix Software Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE.md file in the root directory of this source tree.\n *\n * @license MIT\n */\nimport { createRouter, createBrowserHistory } from '@remix-run/router';\nimport * as React from 'react';\nimport { UNSAFE_mapRouteProperties } from 'react-router';\nimport { matchRoutes, RouterProvider } from 'react-router-dom';\nimport { RemixContext } from './components.js';\nimport { RemixErrorBoundary } from './errorBoundaries.js';\nimport { deserializeErrors } from './errors.js';\nimport { createClientRoutesWithHMRRevalidationOptOut, createClientRoutes, shouldHydrateRouteLoader } from './routes.js';\nimport { decodeViaTurboStream, getSingleFetchDataStrategy } from './single-fetch.js';\nimport invariant from './invariant.js';\nimport { getPatchRoutesOnNavigationFunction, useFogOFWarDiscovery } from './fog-of-war.js';\n\n/* eslint-disable prefer-let/prefer-let */\n\n/* eslint-enable prefer-let/prefer-let */\n\nlet stateDecodingPromise;\nlet router;\nlet routerInitialized = false;\nlet hmrAbortController;\nlet hmrRouterReadyResolve;\n// There's a race condition with HMR where the remix:manifest is signaled before\n// the router is assigned in the RemixBrowser component. This promise gates the\n// HMR handler until the router is ready\nlet hmrRouterReadyPromise = new Promise(resolve => {\n  // body of a promise is executed immediately, so this can be resolved outside\n  // of the promise body\n  hmrRouterReadyResolve = resolve;\n}).catch(() => {\n  // This is a noop catch handler to avoid unhandled promise rejection warnings\n  // in the console. The promise is never rejected.\n  return undefined;\n});\n\n// @ts-expect-error\nif (import.meta && import.meta.hot) {\n  // @ts-expect-error\n  import.meta.hot.accept(\"remix:manifest\", async ({\n    assetsManifest,\n    needsRevalidation\n  }) => {\n    let router = await hmrRouterReadyPromise;\n    // This should never happen, but just in case...\n    if (!router) {\n      console.error(\"Failed to accept HMR update because the router was not ready.\");\n      return;\n    }\n    let routeIds = [...new Set(router.state.matches.map(m => m.route.id).concat(Object.keys(window.__remixRouteModules)))];\n    if (hmrAbortController) {\n      hmrAbortController.abort();\n    }\n    hmrAbortController = new AbortController();\n    let signal = hmrAbortController.signal;\n\n    // Load new route modules that we've seen.\n    let newRouteModules = Object.assign({}, window.__remixRouteModules, Object.fromEntries((await Promise.all(routeIds.map(async id => {\n      var _assetsManifest$hmr, _window$__remixRouteM, _window$__remixRouteM2, _window$__remixRouteM3;\n      if (!assetsManifest.routes[id]) {\n        return null;\n      }\n      let imported = await import(assetsManifest.routes[id].module + `?t=${(_assetsManifest$hmr = assetsManifest.hmr) === null || _assetsManifest$hmr === void 0 ? void 0 : _assetsManifest$hmr.timestamp}`);\n      return [id, {\n        ...imported,\n        // react-refresh takes care of updating these in-place,\n        // if we don't preserve existing values we'll loose state.\n        default: imported.default ? ((_window$__remixRouteM = window.__remixRouteModules[id]) === null || _window$__remixRouteM === void 0 ? void 0 : _window$__remixRouteM.default) ?? imported.default : imported.default,\n        ErrorBoundary: imported.ErrorBoundary ? ((_window$__remixRouteM2 = window.__remixRouteModules[id]) === null || _window$__remixRouteM2 === void 0 ? void 0 : _window$__remixRouteM2.ErrorBoundary) ?? imported.ErrorBoundary : imported.ErrorBoundary,\n        HydrateFallback: imported.HydrateFallback ? ((_window$__remixRouteM3 = window.__remixRouteModules[id]) === null || _window$__remixRouteM3 === void 0 ? void 0 : _window$__remixRouteM3.HydrateFallback) ?? imported.HydrateFallback : imported.HydrateFallback\n      }];\n    }))).filter(Boolean)));\n    Object.assign(window.__remixRouteModules, newRouteModules);\n    // Create new routes\n    let routes = createClientRoutesWithHMRRevalidationOptOut(needsRevalidation, assetsManifest.routes, window.__remixRouteModules, window.__remixContext.state, window.__remixContext.future, window.__remixContext.isSpaMode);\n\n    // This is temporary API and will be more granular before release\n    router._internalSetRoutes(routes);\n\n    // Wait for router to be idle before updating the manifest and route modules\n    // and triggering a react-refresh\n    let unsub = router.subscribe(state => {\n      if (state.revalidation === \"idle\") {\n        unsub();\n        // Abort if a new update comes in while we're waiting for the\n        // router to be idle.\n        if (signal.aborted) return;\n        // Ensure RouterProvider setState has flushed before re-rendering\n        setTimeout(() => {\n          Object.assign(window.__remixManifest, assetsManifest);\n          window.$RefreshRuntime$.performReactRefresh();\n        }, 1);\n      }\n    });\n    window.__remixRevalidation = (window.__remixRevalidation || 0) + 1;\n    router.revalidate();\n  });\n}\n\n/**\n * The entry point for a Remix app when it is rendered in the browser (in\n * `app/entry.client.js`). This component is used by React to hydrate the HTML\n * that was received from the server.\n */\nfunction RemixBrowser(_props) {\n  if (!router) {\n    // When single fetch is enabled, we need to suspend until the initial state\n    // snapshot is decoded into window.__remixContext.state\n    if (window.__remixContext.future.v3_singleFetch) {\n      // Note: `stateDecodingPromise` is not coupled to `router` - we'll reach this\n      // code potentially many times waiting for our state to arrive, but we'll\n      // then only get past here and create the `router` one time\n      if (!stateDecodingPromise) {\n        let stream = window.__remixContext.stream;\n        invariant(stream, \"No stream found for single fetch decoding\");\n        window.__remixContext.stream = undefined;\n        stateDecodingPromise = decodeViaTurboStream(stream, window).then(value => {\n          window.__remixContext.state = value.value;\n          stateDecodingPromise.value = true;\n        }).catch(e => {\n          stateDecodingPromise.error = e;\n        });\n      }\n      if (stateDecodingPromise.error) {\n        throw stateDecodingPromise.error;\n      }\n      if (!stateDecodingPromise.value) {\n        throw stateDecodingPromise;\n      }\n    }\n    let routes = createClientRoutes(window.__remixManifest.routes, window.__remixRouteModules, window.__remixContext.state, window.__remixContext.future, window.__remixContext.isSpaMode);\n    let hydrationData = undefined;\n    if (!window.__remixContext.isSpaMode) {\n      // Create a shallow clone of `loaderData` we can mutate for partial hydration.\n      // When a route exports a `clientLoader` and a `HydrateFallback`, the SSR will\n      // render the fallback so we need the client to do the same for hydration.\n      // The server loader data has already been exposed to these route `clientLoader`'s\n      // in `createClientRoutes` above, so we need to clear out the version we pass to\n      // `createBrowserRouter` so it initializes and runs the client loaders.\n      hydrationData = {\n        ...window.__remixContext.state,\n        loaderData: {\n          ...window.__remixContext.state.loaderData\n        }\n      };\n      let initialMatches = matchRoutes(routes, window.location, window.__remixContext.basename);\n      if (initialMatches) {\n        for (let match of initialMatches) {\n          let routeId = match.route.id;\n          let route = window.__remixRouteModules[routeId];\n          let manifestRoute = window.__remixManifest.routes[routeId];\n          // Clear out the loaderData to avoid rendering the route component when the\n          // route opted into clientLoader hydration and either:\n          // * gave us a HydrateFallback\n          // * or doesn't have a server loader and we have no data to render\n          if (route && shouldHydrateRouteLoader(manifestRoute, route, window.__remixContext.isSpaMode) && (route.HydrateFallback || !manifestRoute.hasLoader)) {\n            hydrationData.loaderData[routeId] = undefined;\n          } else if (manifestRoute && !manifestRoute.hasLoader) {\n            // Since every Remix route gets a `loader` on the client side to load\n            // the route JS module, we need to add a `null` value to `loaderData`\n            // for any routes that don't have server loaders so our partial\n            // hydration logic doesn't kick off the route module loaders during\n            // hydration\n            hydrationData.loaderData[routeId] = null;\n          }\n        }\n      }\n      if (hydrationData && hydrationData.errors) {\n        hydrationData.errors = deserializeErrors(hydrationData.errors);\n      }\n    }\n\n    // We don't use createBrowserRouter here because we need fine-grained control\n    // over initialization to support synchronous `clientLoader` flows.\n    router = createRouter({\n      routes,\n      history: createBrowserHistory(),\n      basename: window.__remixContext.basename,\n      future: {\n        v7_normalizeFormMethod: true,\n        v7_fetcherPersist: window.__remixContext.future.v3_fetcherPersist,\n        v7_partialHydration: true,\n        v7_prependBasename: true,\n        v7_relativeSplatPath: window.__remixContext.future.v3_relativeSplatPath,\n        // Single fetch enables this underlying behavior\n        v7_skipActionErrorRevalidation: window.__remixContext.future.v3_singleFetch === true\n      },\n      hydrationData,\n      mapRouteProperties: UNSAFE_mapRouteProperties,\n      dataStrategy: window.__remixContext.future.v3_singleFetch ? getSingleFetchDataStrategy(window.__remixManifest, window.__remixRouteModules, () => router) : undefined,\n      patchRoutesOnNavigation: getPatchRoutesOnNavigationFunction(window.__remixManifest, window.__remixRouteModules, window.__remixContext.future, window.__remixContext.isSpaMode, window.__remixContext.basename)\n    });\n\n    // We can call initialize() immediately if the router doesn't have any\n    // loaders to run on hydration\n    if (router.state.initialized) {\n      routerInitialized = true;\n      router.initialize();\n    }\n\n    // @ts-ignore\n    router.createRoutesForHMR = createClientRoutesWithHMRRevalidationOptOut;\n    window.__remixRouter = router;\n\n    // Notify that the router is ready for HMR\n    if (hmrRouterReadyResolve) {\n      hmrRouterReadyResolve(router);\n    }\n  }\n\n  // Critical CSS can become stale after code changes, e.g. styles might be\n  // removed from a component, but the styles will still be present in the\n  // server HTML. This allows our HMR logic to clear the critical CSS state.\n\n  let [criticalCss, setCriticalCss] = React.useState(process.env.NODE_ENV === \"development\" ? window.__remixContext.criticalCss : undefined);\n  if (process.env.NODE_ENV === \"development\") {\n    window.__remixClearCriticalCss = () => setCriticalCss(undefined);\n  }\n\n  // This is due to the short circuit return above when the pathname doesn't\n  // match and we force a hard reload.  This is an exceptional scenario in which\n  // we can't hydrate anyway.\n\n  let [location, setLocation] = React.useState(router.state.location);\n  React.useLayoutEffect(() => {\n    // If we had to run clientLoaders on hydration, we delay initialization until\n    // after we've hydrated to avoid hydration issues from synchronous client loaders\n    if (!routerInitialized) {\n      routerInitialized = true;\n      router.initialize();\n    }\n  }, []);\n  React.useLayoutEffect(() => {\n    return router.subscribe(newState => {\n      if (newState.location !== location) {\n        setLocation(newState.location);\n      }\n    });\n  }, [location]);\n  useFogOFWarDiscovery(router, window.__remixManifest, window.__remixRouteModules, window.__remixContext.future, window.__remixContext.isSpaMode);\n\n  // We need to include a wrapper RemixErrorBoundary here in case the root error\n  // boundary also throws and we need to bubble up outside of the router entirely.\n  // Then we need a stateful location here so the user can back-button navigate\n  // out of there\n  return (\n    /*#__PURE__*/\n    // This fragment is important to ensure we match the <RemixServer> JSX\n    // structure so that useId values hydrate correctly\n    React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(RemixContext.Provider, {\n      value: {\n        manifest: window.__remixManifest,\n        routeModules: window.__remixRouteModules,\n        future: window.__remixContext.future,\n        criticalCss,\n        isSpaMode: window.__remixContext.isSpaMode\n      }\n    }, /*#__PURE__*/React.createElement(RemixErrorBoundary, {\n      location: location\n    }, /*#__PURE__*/React.createElement(RouterProvider, {\n      router: router,\n      fallbackElement: null,\n      future: {\n        v7_startTransition: true\n      }\n    }))), window.__remixContext.future.v3_singleFetch ? /*#__PURE__*/React.createElement(React.Fragment, null) : null)\n  );\n}\n\nexport { RemixBrowser };\n", "'use strict';\n\nvar m = require('react-dom');\nif (process.env.NODE_ENV === 'production') {\n  exports.createRoot = m.createRoot;\n  exports.hydrateRoot = m.hydrateRoot;\n} else {\n  var i = m.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;\n  exports.createRoot = function(c, o) {\n    i.usingClientEntryPoint = true;\n    try {\n      return m.createRoot(c, o);\n    } finally {\n      i.usingClientEntryPoint = false;\n    }\n  };\n  exports.hydrateRoot = function(c, h, o) {\n    i.usingClientEntryPoint = true;\n    try {\n      return m.hydrateRoot(c, h, o);\n    } finally {\n      i.usingClientEntryPoint = false;\n    }\n  };\n}\n", "/**\r\n * By default, <PERSON> will handle hydrating your app on the client for you.\r\n * You are free to delete this file if you'd like to, but if you ever want it revealed again, you can run `npx remix reveal` ✨\r\n * For more information, see https://remix.run/file-conventions/entry.client\r\n */\r\n\r\nimport { RemixBrowser } from \"@remix-run/react\";\r\nimport { startTransition, StrictMode } from \"react\";\r\nimport { hydrateRoot } from \"react-dom/client\";\r\n\r\nstartTransition(() => {\r\n  hydrateRoot(\r\n    document,\r\n    <StrictMode>\r\n      <RemixBrowser />\r\n    </StrictMode>\r\n  );\r\n});\r\n"], "names": ["UNSAFE_ErrorResponseImpl", "React.useState", "React.useLayoutEffect", "React.createElement", "React.Fragment", "require$$0", "startTransition", "jsx", "StrictMode"], "mappings": ";;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA,SAAS,kBAAkB,QAAQ;AACjC,MAAI,CAAC,OAAQ,QAAO;AACpB,MAAI,UAAU,OAAO,QAAQ,MAAM;AACnC,MAAI,aAAa,CAAE;AACnB,WAAS,CAAC,KAAK,GAAG,KAAK,SAAS;AAG9B,QAAI,OAAO,IAAI,WAAW,sBAAsB;AAC9C,iBAAW,GAAG,IAAI,IAAIA,kBAAyB,IAAI,QAAQ,IAAI,YAAY,IAAI,MAAM,IAAI,aAAa,IAAI;AAAA,IAC3G,WAAU,OAAO,IAAI,WAAW,SAAS;AAExC,UAAI,IAAI,WAAW;AACjB,YAAI,mBAAmB,OAAO,IAAI,SAAS;AAC3C,YAAI,OAAO,qBAAqB,YAAY;AAC1C,cAAI;AAEF,gBAAI,QAAQ,IAAI,iBAAiB,IAAI,OAAO;AAC5C,kBAAM,QAAQ,IAAI;AAClB,uBAAW,GAAG,IAAI;AAAA,UACnB,SAAQ,GAAG;AAAA,UAEtB;AAAA,QACA;AAAA,MACA;AACM,UAAI,WAAW,GAAG,KAAK,MAAM;AAC3B,YAAI,QAAQ,IAAI,MAAM,IAAI,OAAO;AACjC,cAAM,QAAQ,IAAI;AAClB,mBAAW,GAAG,IAAI;AAAA,MAC1B;AAAA,IACA,OAAW;AACL,iBAAW,GAAG,IAAI;AAAA,IACxB;AAAA,EACA;AACE,SAAO;AACT;AC9CA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA0BA,IAAI;AACJ,IAAI;AACJ,IAAI,oBAAoB;AAExB,IAAI;AAIwB,IAAI,QAAQ,CAAW,YAAA;AAGzB,0BAAA;AAC1B,CAAC,EAAE,MAAM,MAAM;AAGN,SAAA;AACT,CAAC;AAsED,SAAS,aAAa,QAAQ;AAC5B,MAAI,CAAC,QAAQ;AAGP,QAAA,OAAO,eAAe,OAAO,gBAAgB;AAI/C,UAAI,CAAC,sBAAsB;AACrB,YAAA,SAAS,OAAO,eAAe;AACnC,kBAAU,QAAQ,2CAA2C;AAC7D,eAAO,eAAe,SAAS;AAC/B,+BAAuB,qBAAqB,QAAQ,MAAM,EAAE,KAAK,CAAS,UAAA;AACjE,iBAAA,eAAe,QAAQ,MAAM;AACpC,+BAAqB,QAAQ;AAAA,QAAA,CAC9B,EAAE,MAAM,CAAK,MAAA;AACZ,+BAAqB,QAAQ;AAAA,QAAA,CAC9B;AAAA,MAAA;AAEH,UAAI,qBAAqB,OAAO;AAC9B,cAAM,qBAAqB;AAAA,MAAA;AAEzB,UAAA,CAAC,qBAAqB,OAAO;AACzB,cAAA;AAAA,MAAA;AAAA,IACR;AAEF,QAAI,SAAS,mBAAmB,OAAO,gBAAgB,QAAQ,OAAO,qBAAqB,OAAO,eAAe,OAAO,OAAO,eAAe,QAAQ,OAAO,eAAe,SAAS;AACrL,QAAI,gBAAgB;AAChB,QAAA,CAAC,OAAO,eAAe,WAAW;AAOpB,sBAAA;AAAA,QACd,GAAG,OAAO,eAAe;AAAA,QACzB,YAAY;AAAA,UACV,GAAG,OAAO,eAAe,MAAM;AAAA,QAAA;AAAA,MAEnC;AACA,UAAI,iBAAiB,YAAY,QAAQ,OAAO,UAAU,OAAO,eAAe,QAAQ;AACxF,UAAI,gBAAgB;AAClB,iBAAS,SAAS,gBAAgB;AAC5B,cAAA,UAAU,MAAM,MAAM;AACtB,cAAA,QAAQ,OAAO,oBAAoB,OAAO;AAC9C,cAAI,gBAAgB,OAAO,gBAAgB,OAAO,OAAO;AAKzD,cAAI,SAAS,yBAAyB,eAAe,OAAO,OAAO,eAAe,SAAS,MAAM,MAAM,mBAAmB,CAAC,cAAc,YAAY;AACrI,0BAAA,WAAW,OAAO,IAAI;AAAA,UAC3B,WAAA,iBAAiB,CAAC,cAAc,WAAW;AAMtC,0BAAA,WAAW,OAAO,IAAI;AAAA,UAAA;AAAA,QACtC;AAAA,MACF;AAEE,UAAA,iBAAiB,cAAc,QAAQ;AAC3B,sBAAA,SAAS,kBAAkB,cAAc,MAAM;AAAA,MAAA;AAAA,IAC/D;AAKF,aAAS,aAAa;AAAA,MACpB;AAAA,MACA,SAAS,qBAAqB;AAAA,MAC9B,UAAU,OAAO,eAAe;AAAA,MAChC,QAAQ;AAAA,QACN,wBAAwB;AAAA,QACxB,mBAAmB,OAAO,eAAe,OAAO;AAAA,QAChD,qBAAqB;AAAA,QACrB,oBAAoB;AAAA,QACpB,sBAAsB,OAAO,eAAe,OAAO;AAAA;AAAA,QAEnD,gCAAgC,OAAO,eAAe,OAAO,mBAAmB;AAAA,MAClF;AAAA,MACA;AAAA,MACA;AAAA,MACA,cAAc,OAAO,eAAe,OAAO,iBAAiB,2BAA2B,OAAO,iBAAiB,OAAO,qBAAqB,MAAM,MAAM,IAAI;AAAA,MAC3J,yBAAyB,mCAAmC,OAAO,iBAAiB,OAAO,qBAAqB,OAAO,eAAe,QAAQ,OAAO,eAAe,WAAW,OAAO,eAAe,QAAQ;AAAA,IAAA,CAC9M;AAIG,QAAA,OAAO,MAAM,aAAa;AACR,0BAAA;AACpB,aAAO,WAAW;AAAA,IAAA;AAIpB,WAAO,qBAAqB;AAC5B,WAAO,gBAAgB;AAGvB,QAAI,uBAAuB;AACzB,4BAAsB,MAAM;AAAA,IAAA;AAAA,EAC9B;AAOE,MAAA,CAAC,aAAa,cAAc,IAAIC,sBAAwD,OAAO,eAAe,WAAuB;AAC7F;AACnC,WAAA,0BAA0B,MAAM,eAAe,MAAS;AAAA,EAAA;AAO7D,MAAA,CAAC,UAAU,WAAW,IAAIA,aAAe,SAAA,OAAO,MAAM,QAAQ;AAClEC,eAAAA,gBAAsB,MAAM;AAG1B,QAAI,CAAC,mBAAmB;AACF,0BAAA;AACpB,aAAO,WAAW;AAAA,IAAA;AAAA,EAEtB,GAAG,EAAE;AACLA,eAAAA,gBAAsB,MAAM;AACnB,WAAA,OAAO,UAAU,CAAY,aAAA;AAC9B,UAAA,SAAS,aAAa,UAAU;AAClC,oBAAY,SAAS,QAAQ;AAAA,MAAA;AAAA,IAC/B,CACD;AAAA,EAAA,GACA,CAAC,QAAQ,CAAC;AACQ,uBAAA,QAAQ,OAAO,iBAAiB,OAAO,qBAAqB,OAAO,eAAe,QAAQ,OAAO,eAAe,SAAS;AAM9I;AAAA;AAAA;AAAA,IAIEC,6BAAAA,cAAoBC,aAAAA,UAAgB,MAAyBD,6BAAAA,cAAc,aAAa,UAAU;AAAA,MAChG,OAAO;AAAA,QACL,UAAU,OAAO;AAAA,QACjB,cAAc,OAAO;AAAA,QACrB,QAAQ,OAAO,eAAe;AAAA,QAC9B;AAAA,QACA,WAAW,OAAO,eAAe;AAAA,MAAA;AAAA,IACnC,GACoBA,6BAAAA,cAAc,oBAAoB;AAAA,MACtD;AAAA,IAAA,GACoBA,6BAAAA,cAAc,gBAAgB;AAAA,MAClD;AAAA,MACA,iBAAiB;AAAA,MACjB,QAAQ;AAAA,QACN,oBAAoB;AAAA,MAAA;AAAA,IACtB,CACD,CAAC,CAAC,GAAG,OAAO,eAAe,OAAO,iBAA8BA,2CAAoBC,aAAAA,UAAgB,IAAI,IAAI,IAAI;AAAA;AAErH;;ACjRA,IAAI,IAAIC;AAID;AACL,MAAI,IAAI,EAAE;AASV,gBAAsB,SAAS,GAAG,GAAG,GAAG;AACtC,MAAE,wBAAwB;AACtB,QAAA;AACF,aAAO,EAAE,YAAY,GAAG,GAAG,CAAC;AAAA,IAAA,UAC5B;AACA,QAAE,wBAAwB;AAAA,IAAA;AAAA,EAE9B;AACF;ACdAC,aAAAA,gBAAgB,MAAM;AACpB;AAAA,IACE;AAAA,IACCC,kCAAA,IAAAC,aAAA,YAAA,EACC,UAACD,kCAAAA,IAAA,cAAA,CAAA,CAAa,EAChB,CAAA;AAAA,EACF;AACF,CAAC;", "x_google_ignoreList": [0, 1, 2]}