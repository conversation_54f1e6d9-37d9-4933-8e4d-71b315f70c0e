import { r as reactExports, j as jsxRuntimeExports } from "./jsx-runtime-bB2y7OuD.js";
import { B as Button } from "./button-ByAXMyvk.js";
import { d as useActionData, F as Form } from "./components-D7UvGag_.js";
import "./index-z_byfFrQ.js";
import "./index-ImHKLo0a.js";
import "./utils-GkgzjW3c.js";
import "./index-Vp2vNLNM.js";
import "./index-DhHTcibu.js";
function WhatsAppPage() {
  const actionData = useActionData();
  const [isSubmitting, setIsSubmitting] = reactExports.useState(false);
  return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
    className: "p-6",
    children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Form, {
      method: "post",
      onSubmit: () => setIsSubmitting(true),
      onChange: () => {
        if (actionData) setIsSubmitting(false);
      },
      children: /* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
        type: "submit",
        disabled: isSubmitting,
        children: isSubmitting ? "Sending..." : "Send Hello World Template"
      })
    }), (actionData == null ? void 0 : actionData.success) && /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
      className: "mt-4 text-green-600",
      children: "Message sent successfully!"
    }), (actionData == null ? void 0 : actionData.error) && /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
      className: "mt-4 text-red-600",
      children: actionData.error
    })]
  });
}
export {
  WhatsAppPage as default
};
//# sourceMappingURL=whatsapp2-BbnqJz6m.js.map
