{"version": 3, "file": "home.sellerConfig-DKmCtRS7.js", "sources": ["../../../app/routes/home.sellerConfig.tsx"], "sourcesContent": ["import { useState } from \"react\"\r\nimport { Input } from \"~/components/ui/input\"\r\nimport { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from \"~/components/ui/table\"\r\n\r\n\r\nexport default function SellerConfig() {\r\n      const [searchTerm, setSearchTerm] = useState('')\r\n\r\n      return (\r\n            <div className=\"container mx-auto p-6\">\r\n                  <div className=\"flex justify-between items-center mb-6\">\r\n                        <h1 className=\"text-2xl font-bold\">Seller Config</h1>\r\n                  </div>\r\n\r\n                  <div className=\"flex justify-between mb-4\">\r\n                        <Input\r\n                              placeholder=\"Search by name or owner\"\r\n                              value={searchTerm}\r\n                              onChange={(e) => setSearchTerm(e.target.value)}\r\n                              className=\"max-w-sm\"\r\n                        />\r\n                  </div>\r\n                  <div className=\"rounded-md border\">\r\n                        <Table>\r\n                              <TableHeader>\r\n                                    <TableRow>\r\n                                          <TableHead>ID</TableHead>\r\n                                          <TableHead>Name</TableHead>\r\n                                          <TableHead> Owner Mobile</TableHead>\r\n                                          <TableHead>Enable</TableHead>\r\n                                    </TableRow>\r\n                              </TableHeader>\r\n                              <TableBody>\r\n\r\n                              </TableBody>\r\n                              <TableRow>\r\n                                    <TableCell\r\n                                          colSpan={9}\r\n                                          className=\"h-24 text-center\"\r\n                                    >\r\n                                          No results.\r\n                                    </TableCell>\r\n                              </TableRow>\r\n                        </Table>\r\n                  </div>\r\n            </div>\r\n      )\r\n}"], "names": ["SellerConfig", "searchTerm", "setSearchTerm", "useState", "jsxs", "className", "children", "jsx", "Input", "placeholder", "value", "onChange", "e", "target", "Table", "TableHeader", "TableRow", "TableHead", "TableBody", "TableCell", "colSpan"], "mappings": ";;;;AAKA,SAAwBA,eAAe;AACjC,QAAM,CAACC,YAAYC,aAAa,IAAIC,aAAAA,SAAS,EAAE;AAGzC,SAAAC,kCAAAA,KAAC,OAAI;AAAA,IAAAC,WAAU;AAAA,IACTC,UAAA,CAACC,kCAAA,IAAA,OAAA;AAAA,MAAIF,WAAU;AAAA,MACTC,UAAAC,kCAAA,IAAC;QAAGF,WAAU;AAAA,QAAqBC;MAAa,CAAA;AAAA,IACtD,CAAA,GAEAC,kCAAA,IAAC,OAAI;AAAA,MAAAF,WAAU;AAAA,MACTC,UAAAC,kCAAA,IAACC,OAAA;AAAA,QACKC,aAAY;AAAA,QACZC,OAAOT;AAAAA,QACPU,UAAWC,OAAMV,cAAcU,EAAEC,OAAOH,KAAK;AAAA,QAC7CL,WAAU;AAAA,MAChB,CAAA;AAAA,IACN,CAAA,GACCE,kCAAA,IAAA,OAAA;AAAA,MAAIF,WAAU;AAAA,MACTC,iDAACQ,OACK;AAAA,QAAAR,UAAA,CAACC,kCAAA,IAAAQ,aAAA;AAAA,UACKT,iDAACU,UACK;AAAA,YAAAV,UAAA,CAAAC,kCAAA,IAACU;cAAUX,UAAE;AAAA,YAAA,CAAA,GACbC,kCAAA,IAACU;cAAUX,UAAI;AAAA,YAAA,CAAA,GACfC,kCAAA,IAACU;cAAUX,UAAa;AAAA,YAAA,CAAA,GACxBC,kCAAA,IAACU;cAAUX,UAAM;AAAA,YAAA,CAAA,CAAA;AAAA,UACvB,CAAA;AAAA,QACN,CAAA,yCACCY,WAED,CAAA,CAAA,yCACCF,UACK;AAAA,UAAAV,UAAAC,kCAAA,IAACY,WAAA;AAAA,YACKC,SAAS;AAAA,YACTf,WAAU;AAAA,YACfC,UAAA;AAAA,UAED,CAAA;AAAA,QACN,CAAA,CAAA;AAAA,MACN,CAAA;AAAA,IACN,CAAA,CAAA;AAAA,EACN,CAAA;AAEZ;"}