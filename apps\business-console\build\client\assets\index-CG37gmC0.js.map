{"version": 3, "file": "index-CG37gmC0.js", "sources": ["../../../node_modules/@radix-ui/react-roving-focus/dist/index.mjs"], "sourcesContent": ["\"use client\";\n\n// packages/react/roving-focus/src/roving-focus-group.tsx\nimport * as React from \"react\";\nimport { composeEventHandlers } from \"@radix-ui/primitive\";\nimport { createCollection } from \"@radix-ui/react-collection\";\nimport { useComposedRefs } from \"@radix-ui/react-compose-refs\";\nimport { createContextScope } from \"@radix-ui/react-context\";\nimport { useId } from \"@radix-ui/react-id\";\nimport { Primitive } from \"@radix-ui/react-primitive\";\nimport { useCallbackRef } from \"@radix-ui/react-use-callback-ref\";\nimport { useControllableState } from \"@radix-ui/react-use-controllable-state\";\nimport { useDirection } from \"@radix-ui/react-direction\";\nimport { jsx } from \"react/jsx-runtime\";\nvar ENTRY_FOCUS = \"rovingFocusGroup.onEntryFocus\";\nvar EVENT_OPTIONS = { bubbles: false, cancelable: true };\nvar GROUP_NAME = \"RovingFocusGroup\";\nvar [Collection, useCollection, createCollectionScope] = createCollection(GROUP_NAME);\nvar [createRovingFocusGroupContext, createRovingFocusGroupScope] = createContextScope(\n  GROUP_NAME,\n  [createCollectionScope]\n);\nvar [RovingFocusProvider, useRovingFocusContext] = createRovingFocusGroupContext(GROUP_NAME);\nvar RovingFocusGroup = React.forwardRef(\n  (props, forwardedRef) => {\n    return /* @__PURE__ */ jsx(Collection.Provider, { scope: props.__scopeRovingFocusGroup, children: /* @__PURE__ */ jsx(Collection.Slot, { scope: props.__scopeRovingFocusGroup, children: /* @__PURE__ */ jsx(RovingFocusGroupImpl, { ...props, ref: forwardedRef }) }) });\n  }\n);\nRovingFocusGroup.displayName = GROUP_NAME;\nvar RovingFocusGroupImpl = React.forwardRef((props, forwardedRef) => {\n  const {\n    __scopeRovingFocusGroup,\n    orientation,\n    loop = false,\n    dir,\n    currentTabStopId: currentTabStopIdProp,\n    defaultCurrentTabStopId,\n    onCurrentTabStopIdChange,\n    onEntryFocus,\n    preventScrollOnEntryFocus = false,\n    ...groupProps\n  } = props;\n  const ref = React.useRef(null);\n  const composedRefs = useComposedRefs(forwardedRef, ref);\n  const direction = useDirection(dir);\n  const [currentTabStopId = null, setCurrentTabStopId] = useControllableState({\n    prop: currentTabStopIdProp,\n    defaultProp: defaultCurrentTabStopId,\n    onChange: onCurrentTabStopIdChange\n  });\n  const [isTabbingBackOut, setIsTabbingBackOut] = React.useState(false);\n  const handleEntryFocus = useCallbackRef(onEntryFocus);\n  const getItems = useCollection(__scopeRovingFocusGroup);\n  const isClickFocusRef = React.useRef(false);\n  const [focusableItemsCount, setFocusableItemsCount] = React.useState(0);\n  React.useEffect(() => {\n    const node = ref.current;\n    if (node) {\n      node.addEventListener(ENTRY_FOCUS, handleEntryFocus);\n      return () => node.removeEventListener(ENTRY_FOCUS, handleEntryFocus);\n    }\n  }, [handleEntryFocus]);\n  return /* @__PURE__ */ jsx(\n    RovingFocusProvider,\n    {\n      scope: __scopeRovingFocusGroup,\n      orientation,\n      dir: direction,\n      loop,\n      currentTabStopId,\n      onItemFocus: React.useCallback(\n        (tabStopId) => setCurrentTabStopId(tabStopId),\n        [setCurrentTabStopId]\n      ),\n      onItemShiftTab: React.useCallback(() => setIsTabbingBackOut(true), []),\n      onFocusableItemAdd: React.useCallback(\n        () => setFocusableItemsCount((prevCount) => prevCount + 1),\n        []\n      ),\n      onFocusableItemRemove: React.useCallback(\n        () => setFocusableItemsCount((prevCount) => prevCount - 1),\n        []\n      ),\n      children: /* @__PURE__ */ jsx(\n        Primitive.div,\n        {\n          tabIndex: isTabbingBackOut || focusableItemsCount === 0 ? -1 : 0,\n          \"data-orientation\": orientation,\n          ...groupProps,\n          ref: composedRefs,\n          style: { outline: \"none\", ...props.style },\n          onMouseDown: composeEventHandlers(props.onMouseDown, () => {\n            isClickFocusRef.current = true;\n          }),\n          onFocus: composeEventHandlers(props.onFocus, (event) => {\n            const isKeyboardFocus = !isClickFocusRef.current;\n            if (event.target === event.currentTarget && isKeyboardFocus && !isTabbingBackOut) {\n              const entryFocusEvent = new CustomEvent(ENTRY_FOCUS, EVENT_OPTIONS);\n              event.currentTarget.dispatchEvent(entryFocusEvent);\n              if (!entryFocusEvent.defaultPrevented) {\n                const items = getItems().filter((item) => item.focusable);\n                const activeItem = items.find((item) => item.active);\n                const currentItem = items.find((item) => item.id === currentTabStopId);\n                const candidateItems = [activeItem, currentItem, ...items].filter(\n                  Boolean\n                );\n                const candidateNodes = candidateItems.map((item) => item.ref.current);\n                focusFirst(candidateNodes, preventScrollOnEntryFocus);\n              }\n            }\n            isClickFocusRef.current = false;\n          }),\n          onBlur: composeEventHandlers(props.onBlur, () => setIsTabbingBackOut(false))\n        }\n      )\n    }\n  );\n});\nvar ITEM_NAME = \"RovingFocusGroupItem\";\nvar RovingFocusGroupItem = React.forwardRef(\n  (props, forwardedRef) => {\n    const {\n      __scopeRovingFocusGroup,\n      focusable = true,\n      active = false,\n      tabStopId,\n      ...itemProps\n    } = props;\n    const autoId = useId();\n    const id = tabStopId || autoId;\n    const context = useRovingFocusContext(ITEM_NAME, __scopeRovingFocusGroup);\n    const isCurrentTabStop = context.currentTabStopId === id;\n    const getItems = useCollection(__scopeRovingFocusGroup);\n    const { onFocusableItemAdd, onFocusableItemRemove } = context;\n    React.useEffect(() => {\n      if (focusable) {\n        onFocusableItemAdd();\n        return () => onFocusableItemRemove();\n      }\n    }, [focusable, onFocusableItemAdd, onFocusableItemRemove]);\n    return /* @__PURE__ */ jsx(\n      Collection.ItemSlot,\n      {\n        scope: __scopeRovingFocusGroup,\n        id,\n        focusable,\n        active,\n        children: /* @__PURE__ */ jsx(\n          Primitive.span,\n          {\n            tabIndex: isCurrentTabStop ? 0 : -1,\n            \"data-orientation\": context.orientation,\n            ...itemProps,\n            ref: forwardedRef,\n            onMouseDown: composeEventHandlers(props.onMouseDown, (event) => {\n              if (!focusable) event.preventDefault();\n              else context.onItemFocus(id);\n            }),\n            onFocus: composeEventHandlers(props.onFocus, () => context.onItemFocus(id)),\n            onKeyDown: composeEventHandlers(props.onKeyDown, (event) => {\n              if (event.key === \"Tab\" && event.shiftKey) {\n                context.onItemShiftTab();\n                return;\n              }\n              if (event.target !== event.currentTarget) return;\n              const focusIntent = getFocusIntent(event, context.orientation, context.dir);\n              if (focusIntent !== void 0) {\n                if (event.metaKey || event.ctrlKey || event.altKey || event.shiftKey) return;\n                event.preventDefault();\n                const items = getItems().filter((item) => item.focusable);\n                let candidateNodes = items.map((item) => item.ref.current);\n                if (focusIntent === \"last\") candidateNodes.reverse();\n                else if (focusIntent === \"prev\" || focusIntent === \"next\") {\n                  if (focusIntent === \"prev\") candidateNodes.reverse();\n                  const currentIndex = candidateNodes.indexOf(event.currentTarget);\n                  candidateNodes = context.loop ? wrapArray(candidateNodes, currentIndex + 1) : candidateNodes.slice(currentIndex + 1);\n                }\n                setTimeout(() => focusFirst(candidateNodes));\n              }\n            })\n          }\n        )\n      }\n    );\n  }\n);\nRovingFocusGroupItem.displayName = ITEM_NAME;\nvar MAP_KEY_TO_FOCUS_INTENT = {\n  ArrowLeft: \"prev\",\n  ArrowUp: \"prev\",\n  ArrowRight: \"next\",\n  ArrowDown: \"next\",\n  PageUp: \"first\",\n  Home: \"first\",\n  PageDown: \"last\",\n  End: \"last\"\n};\nfunction getDirectionAwareKey(key, dir) {\n  if (dir !== \"rtl\") return key;\n  return key === \"ArrowLeft\" ? \"ArrowRight\" : key === \"ArrowRight\" ? \"ArrowLeft\" : key;\n}\nfunction getFocusIntent(event, orientation, dir) {\n  const key = getDirectionAwareKey(event.key, dir);\n  if (orientation === \"vertical\" && [\"ArrowLeft\", \"ArrowRight\"].includes(key)) return void 0;\n  if (orientation === \"horizontal\" && [\"ArrowUp\", \"ArrowDown\"].includes(key)) return void 0;\n  return MAP_KEY_TO_FOCUS_INTENT[key];\n}\nfunction focusFirst(candidates, preventScroll = false) {\n  const PREVIOUSLY_FOCUSED_ELEMENT = document.activeElement;\n  for (const candidate of candidates) {\n    if (candidate === PREVIOUSLY_FOCUSED_ELEMENT) return;\n    candidate.focus({ preventScroll });\n    if (document.activeElement !== PREVIOUSLY_FOCUSED_ELEMENT) return;\n  }\n}\nfunction wrapArray(array, startIndex) {\n  return array.map((_, index) => array[(startIndex + index) % array.length]);\n}\nvar Root = RovingFocusGroup;\nvar Item = RovingFocusGroupItem;\nexport {\n  Item,\n  Root,\n  RovingFocusGroup,\n  RovingFocusGroupItem,\n  createRovingFocusGroupScope\n};\n//# sourceMappingURL=index.mjs.map\n"], "names": ["React.forwardRef", "jsx", "React.useRef", "React.useState", "React.useEffect", "React.useCallback"], "mappings": ";;;;;;;;AAcA,IAAI,cAAc;AAClB,IAAI,gBAAgB,EAAE,SAAS,OAAO,YAAY,KAAM;AACxD,IAAI,aAAa;AACjB,IAAI,CAAC,YAAY,eAAe,qBAAqB,IAAI,iBAAiB,UAAU;AACjF,IAAC,CAAC,+BAA+B,2BAA2B,IAAI;AAAA,EACjE;AAAA,EACA,CAAC,qBAAqB;AACxB;AACA,IAAI,CAAC,qBAAqB,qBAAqB,IAAI,8BAA8B,UAAU;AAC3F,IAAI,mBAAmBA,aAAgB;AAAA,EACrC,CAAC,OAAO,iBAAiB;AACvB,WAAuBC,sCAAI,WAAW,UAAU,EAAE,OAAO,MAAM,yBAAyB,UAA0BA,sCAAI,WAAW,MAAM,EAAE,OAAO,MAAM,yBAAyB,UAA0BA,kCAAAA,IAAI,sBAAsB,EAAE,GAAG,OAAO,KAAK,cAAc,EAAC,CAAE,EAAC,CAAE;AAAA,EAC5Q;AACA;AACA,iBAAiB,cAAc;AAC/B,IAAI,uBAAuBD,aAAgB,WAAC,CAAC,OAAO,iBAAiB;AACnE,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA,OAAO;AAAA,IACP;AAAA,IACA,kBAAkB;AAAA,IAClB;AAAA,IACA;AAAA,IACA;AAAA,IACA,4BAA4B;AAAA,IAC5B,GAAG;AAAA,EACP,IAAM;AACJ,QAAM,MAAME,aAAY,OAAC,IAAI;AAC7B,QAAM,eAAe,gBAAgB,cAAc,GAAG;AACtD,QAAM,YAAY,aAAa,GAAG;AAClC,QAAM,CAAC,mBAAmB,MAAM,mBAAmB,IAAI,qBAAqB;AAAA,IAC1E,MAAM;AAAA,IACN,aAAa;AAAA,IACb,UAAU;AAAA,EACd,CAAG;AACD,QAAM,CAAC,kBAAkB,mBAAmB,IAAIC,aAAAA,SAAe,KAAK;AACpE,QAAM,mBAAmB,eAAe,YAAY;AACpD,QAAM,WAAW,cAAc,uBAAuB;AACtD,QAAM,kBAAkBD,aAAY,OAAC,KAAK;AAC1C,QAAM,CAAC,qBAAqB,sBAAsB,IAAIC,aAAAA,SAAe,CAAC;AACtEC,eAAAA,UAAgB,MAAM;AACpB,UAAM,OAAO,IAAI;AACjB,QAAI,MAAM;AACR,WAAK,iBAAiB,aAAa,gBAAgB;AACnD,aAAO,MAAM,KAAK,oBAAoB,aAAa,gBAAgB;AAAA,IACzE;AAAA,EACA,GAAK,CAAC,gBAAgB,CAAC;AACrB,SAAuBH,kCAAG;AAAA,IACxB;AAAA,IACA;AAAA,MACE,OAAO;AAAA,MACP;AAAA,MACA,KAAK;AAAA,MACL;AAAA,MACA;AAAA,MACA,aAAaI,aAAiB;AAAA,QAC5B,CAAC,cAAc,oBAAoB,SAAS;AAAA,QAC5C,CAAC,mBAAmB;AAAA,MACrB;AAAA,MACD,gBAAgBA,aAAAA,YAAkB,MAAM,oBAAoB,IAAI,GAAG,CAAA,CAAE;AAAA,MACrE,oBAAoBA,aAAiB;AAAA,QACnC,MAAM,uBAAuB,CAAC,cAAc,YAAY,CAAC;AAAA,QACzD,CAAA;AAAA,MACD;AAAA,MACD,uBAAuBA,aAAiB;AAAA,QACtC,MAAM,uBAAuB,CAAC,cAAc,YAAY,CAAC;AAAA,QACzD,CAAA;AAAA,MACD;AAAA,MACD,UAA0BJ,kCAAG;AAAA,QAC3B,UAAU;AAAA,QACV;AAAA,UACE,UAAU,oBAAoB,wBAAwB,IAAI,KAAK;AAAA,UAC/D,oBAAoB;AAAA,UACpB,GAAG;AAAA,UACH,KAAK;AAAA,UACL,OAAO,EAAE,SAAS,QAAQ,GAAG,MAAM,MAAO;AAAA,UAC1C,aAAa,qBAAqB,MAAM,aAAa,MAAM;AACzD,4BAAgB,UAAU;AAAA,UACtC,CAAW;AAAA,UACD,SAAS,qBAAqB,MAAM,SAAS,CAAC,UAAU;AACtD,kBAAM,kBAAkB,CAAC,gBAAgB;AACzC,gBAAI,MAAM,WAAW,MAAM,iBAAiB,mBAAmB,CAAC,kBAAkB;AAChF,oBAAM,kBAAkB,IAAI,YAAY,aAAa,aAAa;AAClE,oBAAM,cAAc,cAAc,eAAe;AACjD,kBAAI,CAAC,gBAAgB,kBAAkB;AACrC,sBAAM,QAAQ,WAAW,OAAO,CAAC,SAAS,KAAK,SAAS;AACxD,sBAAM,aAAa,MAAM,KAAK,CAAC,SAAS,KAAK,MAAM;AACnD,sBAAM,cAAc,MAAM,KAAK,CAAC,SAAS,KAAK,OAAO,gBAAgB;AACrE,sBAAM,iBAAiB,CAAC,YAAY,aAAa,GAAG,KAAK,EAAE;AAAA,kBACzD;AAAA,gBACD;AACD,sBAAM,iBAAiB,eAAe,IAAI,CAAC,SAAS,KAAK,IAAI,OAAO;AACpE,2BAAW,gBAAgB,yBAAyB;AAAA,cACpE;AAAA,YACA;AACY,4BAAgB,UAAU;AAAA,UACtC,CAAW;AAAA,UACD,QAAQ,qBAAqB,MAAM,QAAQ,MAAM,oBAAoB,KAAK,CAAC;AAAA,QACrF;AAAA,MACA;AAAA,IACA;AAAA,EACG;AACH,CAAC;AACD,IAAI,YAAY;AAChB,IAAI,uBAAuBD,aAAgB;AAAA,EACzC,CAAC,OAAO,iBAAiB;AACvB,UAAM;AAAA,MACJ;AAAA,MACA,YAAY;AAAA,MACZ,SAAS;AAAA,MACT;AAAA,MACA,GAAG;AAAA,IACT,IAAQ;AACJ,UAAM,SAAS,MAAO;AACtB,UAAM,KAAK,aAAa;AACxB,UAAM,UAAU,sBAAsB,WAAW,uBAAuB;AACxE,UAAM,mBAAmB,QAAQ,qBAAqB;AACtD,UAAM,WAAW,cAAc,uBAAuB;AACtD,UAAM,EAAE,oBAAoB,sBAAqB,IAAK;AACtDI,iBAAAA,UAAgB,MAAM;AACpB,UAAI,WAAW;AACb,2BAAoB;AACpB,eAAO,MAAM,sBAAuB;AAAA,MAC5C;AAAA,IACK,GAAE,CAAC,WAAW,oBAAoB,qBAAqB,CAAC;AACzD,WAAuBH,kCAAG;AAAA,MACxB,WAAW;AAAA,MACX;AAAA,QACE,OAAO;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,QACA,UAA0BA,kCAAG;AAAA,UAC3B,UAAU;AAAA,UACV;AAAA,YACE,UAAU,mBAAmB,IAAI;AAAA,YACjC,oBAAoB,QAAQ;AAAA,YAC5B,GAAG;AAAA,YACH,KAAK;AAAA,YACL,aAAa,qBAAqB,MAAM,aAAa,CAAC,UAAU;AAC9D,kBAAI,CAAC,UAAW,OAAM,eAAgB;AAAA,kBACjC,SAAQ,YAAY,EAAE;AAAA,YACzC,CAAa;AAAA,YACD,SAAS,qBAAqB,MAAM,SAAS,MAAM,QAAQ,YAAY,EAAE,CAAC;AAAA,YAC1E,WAAW,qBAAqB,MAAM,WAAW,CAAC,UAAU;AAC1D,kBAAI,MAAM,QAAQ,SAAS,MAAM,UAAU;AACzC,wBAAQ,eAAgB;AACxB;AAAA,cAChB;AACc,kBAAI,MAAM,WAAW,MAAM,cAAe;AAC1C,oBAAM,cAAc,eAAe,OAAO,QAAQ,aAAa,QAAQ,GAAG;AAC1E,kBAAI,gBAAgB,QAAQ;AAC1B,oBAAI,MAAM,WAAW,MAAM,WAAW,MAAM,UAAU,MAAM,SAAU;AACtE,sBAAM,eAAgB;AACtB,sBAAM,QAAQ,WAAW,OAAO,CAAC,SAAS,KAAK,SAAS;AACxD,oBAAI,iBAAiB,MAAM,IAAI,CAAC,SAAS,KAAK,IAAI,OAAO;AACzD,oBAAI,gBAAgB,OAAQ,gBAAe,QAAS;AAAA,yBAC3C,gBAAgB,UAAU,gBAAgB,QAAQ;AACzD,sBAAI,gBAAgB,OAAQ,gBAAe,QAAS;AACpD,wBAAM,eAAe,eAAe,QAAQ,MAAM,aAAa;AAC/D,mCAAiB,QAAQ,OAAO,UAAU,gBAAgB,eAAe,CAAC,IAAI,eAAe,MAAM,eAAe,CAAC;AAAA,gBACrI;AACgB,2BAAW,MAAM,WAAW,cAAc,CAAC;AAAA,cAC3D;AAAA,YACa,CAAA;AAAA,UACb;AAAA,QACA;AAAA,MACA;AAAA,IACK;AAAA,EACL;AACA;AACA,qBAAqB,cAAc;AACnC,IAAI,0BAA0B;AAAA,EAC5B,WAAW;AAAA,EACX,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,UAAU;AAAA,EACV,KAAK;AACP;AACA,SAAS,qBAAqB,KAAK,KAAK;AACtC,MAAI,QAAQ,MAAO,QAAO;AAC1B,SAAO,QAAQ,cAAc,eAAe,QAAQ,eAAe,cAAc;AACnF;AACA,SAAS,eAAe,OAAO,aAAa,KAAK;AAC/C,QAAM,MAAM,qBAAqB,MAAM,KAAK,GAAG;AAC/C,MAAI,gBAAgB,cAAc,CAAC,aAAa,YAAY,EAAE,SAAS,GAAG,EAAG,QAAO;AACpF,MAAI,gBAAgB,gBAAgB,CAAC,WAAW,WAAW,EAAE,SAAS,GAAG,EAAG,QAAO;AACnF,SAAO,wBAAwB,GAAG;AACpC;AACA,SAAS,WAAW,YAAY,gBAAgB,OAAO;AACrD,QAAM,6BAA6B,SAAS;AAC5C,aAAW,aAAa,YAAY;AAClC,QAAI,cAAc,2BAA4B;AAC9C,cAAU,MAAM,EAAE,eAAe;AACjC,QAAI,SAAS,kBAAkB,2BAA4B;AAAA,EAC/D;AACA;AACA,SAAS,UAAU,OAAO,YAAY;AACpC,SAAO,MAAM,IAAI,CAAC,GAAG,UAAU,OAAO,aAAa,SAAS,MAAM,MAAM,CAAC;AAC3E;AACG,IAAC,OAAO;AACR,IAAC,OAAO;", "x_google_ignoreList": [0]}