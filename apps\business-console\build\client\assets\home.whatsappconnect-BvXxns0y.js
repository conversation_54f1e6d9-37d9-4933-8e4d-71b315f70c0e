import { j as jsxRuntimeExports, r as reactExports } from "./jsx-runtime-bB2y7OuD.js";
import { C as Card, b as <PERSON><PERSON><PERSON>er, c as <PERSON><PERSON><PERSON><PERSON>, a as CardContent, d as CardDescription } from "./card-BJQMSLe_.js";
import { R as RadioGroup, a as RadioGroupItem } from "./radio-group-ChzooXbR.js";
import { a as useFetcher, L as Link, u as useLoaderData } from "./components-D7UvGag_.js";
import { c as useLocation } from "./index-DhHTcibu.js";
import "./utils-GkgzjW3c.js";
import "./index-D7VH9Fc8.js";
import "./index-z_byfFrQ.js";
import "./index-CzZ6EYkN.js";
import "./index-Vp2vNLNM.js";
import "./index-CG37gmC0.js";
import "./index-qCtcpOcW.js";
import "./index-DVTNuYOr.js";
import "./index-dIGjFc0I.js";
import "./index-BTdCMChR.js";
import "./index-CkL5tk39.js";
import "./index-CpKiYcZd.js";
import "./index-QLGF6kQx.js";
import "./createLucideIcon-uwkRm45G.js";
function PhoneNumberSelector({ numbers, selectedId, onSubmit }) {
  const handleSubmit = (event) => {
    var _a;
    event.preventDefault();
    const formData = new FormData(event.currentTarget);
    const phoneId = (_a = formData.get("phoneId")) == null ? void 0 : _a.toString();
    if (phoneId) {
      const selectedPhone = numbers.find((n) => n.id === phoneId);
      if (selectedPhone) {
        onSubmit(phoneId, selectedPhone.display_phone_number);
      }
    }
  };
  return /* @__PURE__ */ jsxRuntimeExports.jsxs(Card, { className: "w-full max-w-3xl", children: [
    /* @__PURE__ */ jsxRuntimeExports.jsx(CardHeader, { children: /* @__PURE__ */ jsxRuntimeExports.jsx(CardTitle, { children: "Select WhatsApp Phone Number" }) }),
    /* @__PURE__ */ jsxRuntimeExports.jsx(CardContent, { children: /* @__PURE__ */ jsxRuntimeExports.jsxs("form", { onSubmit: handleSubmit, children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx(RadioGroup, { name: "phoneId", defaultValue: selectedId, children: numbers.map((number) => /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center space-x-2 p-4 hover:bg-gray-50", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx(RadioGroupItem, { value: number.id, id: number.id }),
        /* @__PURE__ */ jsxRuntimeExports.jsxs("label", { htmlFor: number.id, className: "flex-1", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "font-medium", children: number.display_phone_number }),
          /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "text-sm text-gray-500", children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: `inline-block px-2 py-1 rounded ${number.code_verification_status === "VERIFIED" ? "bg-green-100 text-green-800" : "bg-yellow-100 text-yellow-800"}`, children: number.code_verification_status }),
            /* @__PURE__ */ jsxRuntimeExports.jsxs("span", { className: "ml-2", children: [
              "Quality: ",
              number.quality_rating
            ] })
          ] }),
          /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "text-sm text-gray-500", children: [
            "Verified Name: ",
            number.verified_name
          ] })
        ] })
      ] }, number.id)) }),
      /* @__PURE__ */ jsxRuntimeExports.jsx(
        "button",
        {
          type: "submit",
          className: "mt-4 w-full bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700",
          children: "Select Number"
        }
      )
    ] }) })
  ] });
}
function WhatsAppConnectView({ FACEBOOK_APP_ID, connectionState }) {
  const fetcher = useFetcher();
  const [businessAccounts, setBusinessAccounts] = reactExports.useState([]);
  const [phoneNumbers, setPhoneNumbers] = reactExports.useState([]);
  const [selectedBusinessId, setSelectedBusinessId] = reactExports.useState(null);
  const [hasFetchedBusinesses, setHasFetchedBusinesses] = reactExports.useState(false);
  const [isSubscribed, setIsSubscribed] = reactExports.useState(false);
  const [hasCheckedSubscription, setHasCheckedSubscription] = reactExports.useState(false);
  const [currentView, setCurrentView] = reactExports.useState("login");
  const CONFIG_ID = "***************";
  const REQUIRED_APP_ID = "***************";
  reactExports.useEffect(() => {
    if (!(connectionState == null ? void 0 : connectionState.access)) {
      setCurrentView("login");
    } else if (connectionState.mNetConnectedPhoneNumberId) {
      setCurrentView("registered");
    } else if (!hasFetchedBusinesses) {
      setCurrentView("businesses");
    }
  }, [connectionState, hasFetchedBusinesses]);
  reactExports.useEffect(() => {
    if (currentView === "registered" && (connectionState == null ? void 0 : connectionState.wabaId) && !hasCheckedSubscription && fetcher.state === "idle") {
      const formData = new FormData();
      formData.append("actionType", "check-subscription");
      formData.append("businessId", connectionState.wabaId);
      fetcher.submit(formData, { method: "post" });
      setHasCheckedSubscription(true);
    }
  }, [currentView, connectionState == null ? void 0 : connectionState.wabaId, hasCheckedSubscription, fetcher.state]);
  reactExports.useEffect(() => {
    if (currentView !== "registered") {
      setHasCheckedSubscription(false);
    }
  }, [currentView]);
  reactExports.useEffect(() => {
    if ((connectionState == null ? void 0 : connectionState.access) && !hasFetchedBusinesses && fetcher.state === "idle" && currentView === "businesses") {
      setHasFetchedBusinesses(true);
      const formData = new FormData();
      formData.append("actionType", "fetch-businesses");
      fetcher.submit(formData, { method: "post" });
    }
  }, [connectionState == null ? void 0 : connectionState.access, hasFetchedBusinesses, fetcher.state, currentView]);
  reactExports.useEffect(() => {
    var _a;
    if ((_a = fetcher.data) == null ? void 0 : _a.success) {
      if ("businessAccounts" in fetcher.data) {
        setBusinessAccounts(fetcher.data.businessAccounts);
      } else if ("phoneNumbers" in fetcher.data) {
        setPhoneNumbers(fetcher.data.phoneNumbers);
      } else if ("subscribedApps" in fetcher.data) {
        setIsSubscribed(fetcher.data.subscribedApps.some(
          (app) => {
            var _a2;
            return ((_a2 = app.whatsapp_business_api_data) == null ? void 0 : _a2.id) === REQUIRED_APP_ID;
          }
        ));
      } else if ("actionType" in fetcher.data) {
        switch (fetcher.data.actionType) {
          case "connect-phone":
            setCurrentView("registered");
            break;
          case "delink":
            setCurrentView("login");
            break;
          case "subscribe":
          case "unsubscribe":
            const formData = new FormData();
            formData.append("actionType", "check-subscription");
            formData.append("businessId", connectionState.wabaId);
            fetcher.submit(formData, { method: "post" });
            break;
        }
      }
    }
  }, [fetcher.data]);
  const handleSubscriptionToggle = () => {
    if (!(connectionState == null ? void 0 : connectionState.wabaId)) {
      console.error("No WABA ID found");
      return;
    }
    const formData = new FormData();
    formData.append("actionType", isSubscribed ? "unsubscribe" : "subscribe");
    formData.append("businessId", connectionState.wabaId);
    setHasCheckedSubscription(false);
    fetcher.submit(formData, { method: "post" });
  };
  const handleBusinessSelect = async (businessId) => {
    setSelectedBusinessId(businessId);
    setCurrentView("phones");
    const formData = new FormData();
    formData.append("actionType", "fetch-phone-numbers");
    formData.append("businessId", businessId);
    fetcher.submit(formData, { method: "post" });
  };
  reactExports.useEffect(() => {
    window.fbAsyncInit = function() {
      window.FB.init({
        appId: FACEBOOK_APP_ID,
        cookie: true,
        xfbml: true,
        version: "v21.0"
      });
    };
    const script = document.createElement("script");
    script.src = "https://connect.facebook.net/en_US/sdk.js";
    script.async = true;
    script.defer = true;
    document.body.appendChild(script);
    return () => {
      document.body.removeChild(script);
    };
  }, [FACEBOOK_APP_ID]);
  const handleFacebookLogin = () => {
    window.FB.login((response) => {
      var _a;
      if (response.status === "connected" && ((_a = response.authResponse) == null ? void 0 : _a.code)) {
        const formData = new FormData();
        formData.append("code", response.authResponse.code);
        formData.append("actionType", "exchange-token");
        fetcher.submit(formData, { method: "post" });
      }
    }, {
      config_id: CONFIG_ID,
      response_type: "code",
      override_default_response_type: true
    });
  };
  return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "space-y-6", children: [
    fetcher.state !== "idle" && /* @__PURE__ */ jsxRuntimeExports.jsxs(Card, { className: "w-full max-w-3xl mx-auto", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx(CardHeader, { children: /* @__PURE__ */ jsxRuntimeExports.jsx(CardTitle, { children: "Loading..." }) }),
      /* @__PURE__ */ jsxRuntimeExports.jsx(CardContent, { children: /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "flex items-center justify-center p-8", children: /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600" }) }) })
    ] }),
    fetcher.state === "idle" && currentView === "login" && /* @__PURE__ */ jsxRuntimeExports.jsxs(Card, { className: "w-full max-w-3xl mx-auto", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsxs(CardHeader, { children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx(CardTitle, { children: "Connect WhatsApp Business" }),
        /* @__PURE__ */ jsxRuntimeExports.jsx(CardDescription, { children: "Connect your WhatsApp Business account to continue" })
      ] }),
      /* @__PURE__ */ jsxRuntimeExports.jsx(CardContent, { children: /* @__PURE__ */ jsxRuntimeExports.jsx(
        "button",
        {
          onClick: handleFacebookLogin,
          className: "w-full bg-blue-600 text-white px-6 py-3 rounded-md hover:bg-blue-700",
          children: "Continue with Facebook"
        }
      ) })
    ] }),
    fetcher.state === "idle" && currentView === "businesses" && /* @__PURE__ */ jsxRuntimeExports.jsxs(Card, { className: "w-full max-w-3xl mx-auto", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx(CardHeader, { children: /* @__PURE__ */ jsxRuntimeExports.jsx(CardTitle, { children: "Select WhatsApp Business Account" }) }),
      /* @__PURE__ */ jsxRuntimeExports.jsx(CardContent, { children: /* @__PURE__ */ jsxRuntimeExports.jsx("div", { children: businessAccounts.map((account) => /* @__PURE__ */ jsxRuntimeExports.jsx(
        "div",
        {
          className: "mb-4 p-4 border rounded cursor-pointer hover:bg-gray-50",
          onClick: () => handleBusinessSelect(account.id),
          children: /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "font-medium", children: account.name })
        },
        account.id
      )) }) })
    ] }),
    fetcher.state === "idle" && currentView === "phones" && /* @__PURE__ */ jsxRuntimeExports.jsx("div", { children: phoneNumbers.length > 0 && /* @__PURE__ */ jsxRuntimeExports.jsx(
      PhoneNumberSelector,
      {
        numbers: phoneNumbers,
        selectedId: connectionState == null ? void 0 : connectionState.mNetConnectedPhoneNumberId,
        onSubmit: (phoneId, phoneNumber) => {
          const formData = new FormData();
          formData.append("actionType", "connect-phone");
          formData.append("phoneId", phoneId);
          formData.append("phoneNumber", phoneNumber);
          formData.append("wabaId", selectedBusinessId);
          fetcher.submit(formData, { method: "post" });
        }
      }
    ) }),
    fetcher.state === "idle" && currentView === "registered" && /* @__PURE__ */ jsxRuntimeExports.jsxs(Card, { className: "w-full max-w-3xl mx-auto", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsxs(CardHeader, { children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx(CardTitle, { children: "WhatsApp Business Registered" }),
        /* @__PURE__ */ jsxRuntimeExports.jsx(CardDescription, { children: "Your WhatsApp Business account is ready to use" })
      ] }),
      /* @__PURE__ */ jsxRuntimeExports.jsx(CardContent, { children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "space-y-4", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx("h3", { className: "font-medium", children: "Connected Phone Number" }),
          /* @__PURE__ */ jsxRuntimeExports.jsx("p", { children: connectionState == null ? void 0 : connectionState.mNetConnectedPhoneNumber })
        ] }),
        /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx("h3", { className: "font-medium", children: "Subscription Status" }),
          /* @__PURE__ */ jsxRuntimeExports.jsx("p", { className: isSubscribed ? "text-green-600" : "text-yellow-600", children: isSubscribed ? "Subscribed" : "Not Subscribed" })
        ] }),
        /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "space-y-2", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx(
            "button",
            {
              onClick: handleSubscriptionToggle,
              className: `w-full px-4 py-2 rounded-md font-medium ${isSubscribed ? "bg-red-500 hover:bg-red-600 text-black" : "bg-emerald-500 hover:bg-emerald-600 text-black"}`,
              children: isSubscribed ? "Unsubscribe" : "Subscribe"
            }
          ),
          /* @__PURE__ */ jsxRuntimeExports.jsx(
            Link,
            {
              to: "/home/<USER>",
              className: "w-full bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 inline-block text-center",
              children: "Manage Templates"
            }
          ),
          /* @__PURE__ */ jsxRuntimeExports.jsxs(fetcher.Form, { method: "post", children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx("input", { type: "hidden", name: "actionType", value: "delink" }),
            /* @__PURE__ */ jsxRuntimeExports.jsx(
              "button",
              {
                type: "submit",
                className: "w-full bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700",
                onClick: (e) => {
                  if (!confirm("Are you sure you want to delink your WhatsApp Business account?")) {
                    e.preventDefault();
                  }
                },
                children: "Delink Account"
              }
            )
          ] })
        ] })
      ] }) })
    ] })
  ] });
}
function WhatsAppConnect() {
  const {
    FACEBOOK_APP_ID,
    connectionState
  } = useLoaderData();
  useLocation();
  if (!FACEBOOK_APP_ID) {
    return /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
      children: "Error: Facebook App ID not configured"
    });
  }
  return /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
    className: "space-y-6",
    children: /* @__PURE__ */ jsxRuntimeExports.jsx(WhatsAppConnectView, {
      FACEBOOK_APP_ID,
      connectionState
    })
  });
}
export {
  WhatsAppConnect as default
};
//# sourceMappingURL=home.whatsappconnect-BvXxns0y.js.map
