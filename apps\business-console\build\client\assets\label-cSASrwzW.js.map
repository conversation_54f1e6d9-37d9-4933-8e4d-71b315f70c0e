{"version": 3, "file": "label-cSASrwzW.js", "sources": ["../../../node_modules/@radix-ui/react-label/dist/index.mjs", "../../../app/components/ui/label.tsx"], "sourcesContent": ["\"use client\";\n\n// packages/react/label/src/label.tsx\nimport * as React from \"react\";\nimport { Primitive } from \"@radix-ui/react-primitive\";\nimport { jsx } from \"react/jsx-runtime\";\nvar NAME = \"Label\";\nvar Label = React.forwardRef((props, forwardedRef) => {\n  return /* @__PURE__ */ jsx(\n    Primitive.label,\n    {\n      ...props,\n      ref: forwardedRef,\n      onMouseDown: (event) => {\n        const target = event.target;\n        if (target.closest(\"button, input, select, textarea\")) return;\n        props.onMouseDown?.(event);\n        if (!event.defaultPrevented && event.detail > 1) event.preventDefault();\n      }\n    }\n  );\n});\nLabel.displayName = NAME;\nvar Root = Label;\nexport {\n  Label,\n  Root\n};\n//# sourceMappingURL=index.mjs.map\n", "import * as React from \"react\"\r\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"~/lib/utils\"\r\n\r\nconst labelVariants = cva(\r\n  \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\"\r\n)\r\n\r\nconst Label = React.forwardRef<\r\n  React.ElementRef<typeof LabelPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root> &\r\n    VariantProps<typeof labelVariants>\r\n>(({ className, ...props }, ref) => (\r\n  <LabelPrimitive.Root\r\n    ref={ref}\r\n    className={cn(labelVariants(), className)}\r\n    {...props}\r\n  />\r\n))\r\nLabel.displayName = LabelPrimitive.Root.displayName\r\n\r\nexport { Label }\r\n"], "names": ["Label", "React.forwardRef", "jsx", "LabelPrimitive.Root"], "mappings": ";;;;AAMA,IAAI,OAAO;AACX,IAAIA,UAAQC,aAAgB,WAAC,CAAC,OAAO,iBAAiB;AACpD,SAAuBC,kCAAG;AAAA,IACxB,UAAU;AAAA,IACV;AAAA,MACE,GAAG;AAAA,MACH,KAAK;AAAA,MACL,aAAa,CAAC,UAAU;;AACtB,cAAM,SAAS,MAAM;AACrB,YAAI,OAAO,QAAQ,iCAAiC,EAAG;AACvD,oBAAM,gBAAN,+BAAoB;AACpB,YAAI,CAAC,MAAM,oBAAoB,MAAM,SAAS,EAAG,OAAM,eAAgB;AAAA,MAC/E;AAAA,IACA;AAAA,EACG;AACH,CAAC;AACDF,QAAM,cAAc;AACpB,IAAI,OAAOA;ACjBX,MAAM,gBAAgB;AAAA,EACpB;AACF;AAEM,MAAA,QAAQC,aAIZ,WAAA,CAAC,EAAE,WAAW,GAAG,MAAM,GAAG,QAC1BC,kCAAA;AAAA,EAACC;AAAAA,EAAA;AAAA,IACC;AAAA,IACA,WAAW,GAAG,cAAc,GAAG,SAAS;AAAA,IACvC,GAAG;AAAA,EAAA;AACN,CACD;AACD,MAAM,cAAcA,KAAoB;", "x_google_ignoreList": [0]}