import { j as jsxRuntimeExports } from "./jsx-runtime-bB2y7OuD.js";
import { B as But<PERSON> } from "./button-ByAXMyvk.js";
import "./index-z_byfFrQ.js";
import "./index-ImHKLo0a.js";
import "./utils-GkgzjW3c.js";
function Storybook() {
  return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
    children: [/* @__PURE__ */ jsxRuntimeExports.jsx("h1", {
      children: "Storybook"
    }), /* @__PURE__ */ jsxRuntimeExports.jsx("p", {
      children: /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
        children: /* @__PURE__ */ jsxRuntimeExports.jsx(Button, {})
      })
    })]
  });
}
export {
  Storybook as default
};
//# sourceMappingURL=storybook-ci_-JJjw.js.map
