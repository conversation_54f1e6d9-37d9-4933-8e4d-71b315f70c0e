{"version": 3, "file": "index-qCtcpOcW.js", "sources": ["../../../node_modules/@radix-ui/react-collection/dist/index.mjs"], "sourcesContent": ["\"use client\";\n\n// packages/react/collection/src/collection.tsx\nimport React from \"react\";\nimport { createContextScope } from \"@radix-ui/react-context\";\nimport { useComposedRefs } from \"@radix-ui/react-compose-refs\";\nimport { Slot } from \"@radix-ui/react-slot\";\nimport { jsx } from \"react/jsx-runtime\";\nfunction createCollection(name) {\n  const PROVIDER_NAME = name + \"CollectionProvider\";\n  const [createCollectionContext, createCollectionScope] = createContextScope(PROVIDER_NAME);\n  const [CollectionProviderImpl, useCollectionContext] = createCollectionContext(\n    PROVIDER_NAME,\n    { collectionRef: { current: null }, itemMap: /* @__PURE__ */ new Map() }\n  );\n  const CollectionProvider = (props) => {\n    const { scope, children } = props;\n    const ref = React.useRef(null);\n    const itemMap = React.useRef(/* @__PURE__ */ new Map()).current;\n    return /* @__PURE__ */ jsx(CollectionProviderImpl, { scope, itemMap, collectionRef: ref, children });\n  };\n  CollectionProvider.displayName = PROVIDER_NAME;\n  const COLLECTION_SLOT_NAME = name + \"CollectionSlot\";\n  const CollectionSlot = React.forwardRef(\n    (props, forwardedRef) => {\n      const { scope, children } = props;\n      const context = useCollectionContext(COLLECTION_SLOT_NAME, scope);\n      const composedRefs = useComposedRefs(forwardedRef, context.collectionRef);\n      return /* @__PURE__ */ jsx(Slot, { ref: composedRefs, children });\n    }\n  );\n  CollectionSlot.displayName = COLLECTION_SLOT_NAME;\n  const ITEM_SLOT_NAME = name + \"CollectionItemSlot\";\n  const ITEM_DATA_ATTR = \"data-radix-collection-item\";\n  const CollectionItemSlot = React.forwardRef(\n    (props, forwardedRef) => {\n      const { scope, children, ...itemData } = props;\n      const ref = React.useRef(null);\n      const composedRefs = useComposedRefs(forwardedRef, ref);\n      const context = useCollectionContext(ITEM_SLOT_NAME, scope);\n      React.useEffect(() => {\n        context.itemMap.set(ref, { ref, ...itemData });\n        return () => void context.itemMap.delete(ref);\n      });\n      return /* @__PURE__ */ jsx(Slot, { ...{ [ITEM_DATA_ATTR]: \"\" }, ref: composedRefs, children });\n    }\n  );\n  CollectionItemSlot.displayName = ITEM_SLOT_NAME;\n  function useCollection(scope) {\n    const context = useCollectionContext(name + \"CollectionConsumer\", scope);\n    const getItems = React.useCallback(() => {\n      const collectionNode = context.collectionRef.current;\n      if (!collectionNode) return [];\n      const orderedNodes = Array.from(collectionNode.querySelectorAll(`[${ITEM_DATA_ATTR}]`));\n      const items = Array.from(context.itemMap.values());\n      const orderedItems = items.sort(\n        (a, b) => orderedNodes.indexOf(a.ref.current) - orderedNodes.indexOf(b.ref.current)\n      );\n      return orderedItems;\n    }, [context.collectionRef, context.itemMap]);\n    return getItems;\n  }\n  return [\n    { Provider: CollectionProvider, Slot: CollectionSlot, ItemSlot: CollectionItemSlot },\n    useCollection,\n    createCollectionScope\n  ];\n}\nexport {\n  createCollection\n};\n//# sourceMappingURL=index.mjs.map\n"], "names": ["jsx"], "mappings": ";;;AAQA,SAAS,iBAAiB,MAAM;AAC9B,QAAM,gBAAgB,OAAO;AAC7B,QAAM,CAAC,yBAAyB,qBAAqB,IAAI,mBAAmB,aAAa;AACzF,QAAM,CAAC,wBAAwB,oBAAoB,IAAI;AAAA,IACrD;AAAA,IACA,EAAE,eAAe,EAAE,SAAS,KAAI,GAAI,SAAyB,oBAAI,IAAK,EAAA;AAAA,EACvE;AACD,QAAM,qBAAqB,CAAC,UAAU;AACpC,UAAM,EAAE,OAAO,SAAQ,IAAK;AAC5B,UAAM,MAAM,MAAM,OAAO,IAAI;AAC7B,UAAM,UAAU,MAAM,OAAuB,oBAAI,IAAG,CAAE,EAAE;AACxD,WAAuBA,kCAAG,IAAC,wBAAwB,EAAE,OAAO,SAAS,eAAe,KAAK,UAAU;AAAA,EACpG;AACD,qBAAmB,cAAc;AACjC,QAAM,uBAAuB,OAAO;AACpC,QAAM,iBAAiB,MAAM;AAAA,IAC3B,CAAC,OAAO,iBAAiB;AACvB,YAAM,EAAE,OAAO,SAAQ,IAAK;AAC5B,YAAM,UAAU,qBAAqB,sBAAsB,KAAK;AAChE,YAAM,eAAe,gBAAgB,cAAc,QAAQ,aAAa;AACxE,aAAuBA,kCAAAA,IAAI,MAAM,EAAE,KAAK,cAAc,SAAQ,CAAE;AAAA,IACtE;AAAA,EACG;AACD,iBAAe,cAAc;AAC7B,QAAM,iBAAiB,OAAO;AAC9B,QAAM,iBAAiB;AACvB,QAAM,qBAAqB,MAAM;AAAA,IAC/B,CAAC,OAAO,iBAAiB;AACvB,YAAM,EAAE,OAAO,UAAU,GAAG,SAAU,IAAG;AACzC,YAAM,MAAM,MAAM,OAAO,IAAI;AAC7B,YAAM,eAAe,gBAAgB,cAAc,GAAG;AACtD,YAAM,UAAU,qBAAqB,gBAAgB,KAAK;AAC1D,YAAM,UAAU,MAAM;AACpB,gBAAQ,QAAQ,IAAI,KAAK,EAAE,KAAK,GAAG,UAAU;AAC7C,eAAO,MAAM,KAAK,QAAQ,QAAQ,OAAO,GAAG;AAAA,MACpD,CAAO;AACD,aAAuBA,sCAAI,MAAM,EAAE,GAAG,EAAE,CAAC,cAAc,GAAG,GAAE,GAAI,KAAK,cAAc,UAAU;AAAA,IACnG;AAAA,EACG;AACD,qBAAmB,cAAc;AACjC,WAAS,cAAc,OAAO;AAC5B,UAAM,UAAU,qBAAqB,OAAO,sBAAsB,KAAK;AACvE,UAAM,WAAW,MAAM,YAAY,MAAM;AACvC,YAAM,iBAAiB,QAAQ,cAAc;AAC7C,UAAI,CAAC,eAAgB,QAAO,CAAE;AAC9B,YAAM,eAAe,MAAM,KAAK,eAAe,iBAAiB,IAAI,cAAc,GAAG,CAAC;AACtF,YAAM,QAAQ,MAAM,KAAK,QAAQ,QAAQ,QAAQ;AACjD,YAAM,eAAe,MAAM;AAAA,QACzB,CAAC,GAAG,MAAM,aAAa,QAAQ,EAAE,IAAI,OAAO,IAAI,aAAa,QAAQ,EAAE,IAAI,OAAO;AAAA,MACnF;AACD,aAAO;AAAA,IACR,GAAE,CAAC,QAAQ,eAAe,QAAQ,OAAO,CAAC;AAC3C,WAAO;AAAA,EACX;AACE,SAAO;AAAA,IACL,EAAE,UAAU,oBAAoB,MAAM,gBAAgB,UAAU,mBAAoB;AAAA,IACpF;AAAA,IACA;AAAA,EACD;AACH;", "x_google_ignoreList": [0]}