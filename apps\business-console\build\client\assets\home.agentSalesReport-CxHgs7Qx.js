import { r as reactExports, j as jsxRuntimeExports } from "./jsx-runtime-bB2y7OuD.js";
import { B as Button } from "./button-ByAXMyvk.js";
import { I as Input } from "./input-3v87qohQ.js";
import { T as Table, a as TableHeader, b as TableRow, c as TableHead, d as TableBody, e as TableCell } from "./table-CAxdJZsY.js";
import { P as Popover, a as PopoverTrigger, b as PopoverContent } from "./popover-CD2vRFIm.js";
import { C as Calendar, a as Calendar$1 } from "./calendar-_8-DqkPN.js";
import { S as Select, a as SelectTrigger, b as SelectValue, c as SelectContent, d as SelectItem } from "./select-BFGSXKcr.js";
import { a as useNavigate } from "./index-DhHTcibu.js";
import { u as useLoaderData, a as useFetcher } from "./components-D7UvGag_.js";
import { p as parseISO } from "./parseISO-COJrHI78.js";
import { A as ArrowLeft } from "./arrow-left-D_Ztdrp5.js";
import { f as format } from "./format-82yT_5--.js";
import "./index-z_byfFrQ.js";
import "./index-ImHKLo0a.js";
import "./utils-GkgzjW3c.js";
import "./index-D7VH9Fc8.js";
import "./index-BXzPK7u0.js";
import "./index-CzZ6EYkN.js";
import "./index-Vp2vNLNM.js";
import "./index-CEHS9zzk.js";
import "./index-DVTNuYOr.js";
import "./index-DscYByPT.js";
import "./index-CkL5tk39.js";
import "./index-QLGF6kQx.js";
import "./index-dIGjFc0I.js";
import "./createLucideIcon-uwkRm45G.js";
import "./addMonths-Dj4hq91A.js";
import "./isSameDay-BQMn9z7h.js";
import "./addDays-CyH8qBoF.js";
import "./chevron-right-B-tR7Kir.js";
import "./chevron-left-CLqBlTg1.js";
import "./index-IXOTxK3N.js";
import "./index-qCtcpOcW.js";
import "./index-BTdCMChR.js";
import "./index-CpKiYcZd.js";
import "./index-C88PRvfd.js";
import "./chevron-down-pCP5jmjX.js";
import "./check-_dbWxIzT.js";
function AgentWiseSales() {
  var _a;
  const navigate = useNavigate();
  const loaderData = useLoaderData();
  const initialDate = parseISO(loaderData.date);
  const [date, setDate] = reactExports.useState(initialDate);
  const [data, setData] = reactExports.useState(loaderData.agentSales);
  const [loading, setLoading] = reactExports.useState(false);
  const [searchTerm, setSearchTerm] = reactExports.useState("");
  const [pageSize, setPageSize] = reactExports.useState("10");
  const [currentPage, setCurrentPage] = reactExports.useState(1);
  const fetcher = useFetcher();
  reactExports.useEffect(() => {
    var _a2, _b;
    if ((_a2 = fetcher.data) == null ? void 0 : _a2.agentSales) {
      setData((_b = fetcher.data) == null ? void 0 : _b.agentSales);
    }
  }, [(_a = fetcher.data) == null ? void 0 : _a.agentSales]);
  const filteredData = reactExports.useMemo(() => {
    return data.filter((item) => {
      const searchFields = [item.agentId.toString().toLowerCase(), item.agentName.toLowerCase()];
      return searchTerm === "" || searchFields.some((field) => field.includes(searchTerm.toLowerCase()));
    });
  }, [data, searchTerm]);
  const paginatedData = reactExports.useMemo(() => {
    const start = (currentPage - 1) * Number(pageSize);
    const end = start + Number(pageSize);
    return [...filteredData].sort((a, b) => a.agentName.localeCompare(b.agentName)).slice(start, end);
  }, [filteredData, currentPage, pageSize]);
  const totalPages = Math.ceil(filteredData.length / Number(pageSize));
  const handleSubmit = (sellerId, deliveryDate) => {
    const formData = new FormData();
    formData.append("sellerId", sellerId.toString());
    formData.append("date", deliveryDate);
    fetcher.submit(formData, {
      method: "GET"
    });
  };
  return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
    className: "container mx-auto",
    children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
      className: "flex items-center gap-2 mb-6 my-6",
      children: [/* @__PURE__ */ jsxRuntimeExports.jsxs(Button, {
        variant: "ghost",
        size: "sm",
        onClick: () => navigate(-1),
        children: [/* @__PURE__ */ jsxRuntimeExports.jsx(ArrowLeft, {
          className: "h-4 w-4 mr-2"
        }), "Back to Sales Reports"]
      }), /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
        className: "text-muted-foreground",
        children: "/"
      }), /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
        className: "font-semibold",
        children: loaderData.sellerName
      })]
    }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
      className: "flex space-x-5  my-5",
      children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
        className: " flex space-x-2",
        children: [/* @__PURE__ */ jsxRuntimeExports.jsxs(Popover, {
          children: [/* @__PURE__ */ jsxRuntimeExports.jsx(PopoverTrigger, {
            asChild: true,
            children: /* @__PURE__ */ jsxRuntimeExports.jsxs(Button, {
              variant: "outline",
              className: "w-[280px]",
              children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Calendar, {
                className: "mr-2 h-4 w-4"
              }), date ? format(date, "PPP") : "Pick a date"]
            })
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(PopoverContent, {
            className: "w-auto p-0",
            children: /* @__PURE__ */ jsxRuntimeExports.jsx(Calendar$1, {
              mode: "single",
              selected: date,
              onSelect: setDate,
              initialFocus: true
            })
          })]
        }), /* @__PURE__ */ jsxRuntimeExports.jsx(Input, {
          type: "hidden",
          name: "date",
          value: date ? format(date, "yyyy-MM-dd") : ""
        }), /* @__PURE__ */ jsxRuntimeExports.jsx(Input, {
          type: "hidden",
          name: "sellerId",
          value: loaderData.sellerId
        }), /* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
          type: "submit",
          onClick: () => handleSubmit(loaderData.sellerId, date),
          children: loading ? "Submitting" : "View Report"
        })]
      }), /* @__PURE__ */ jsxRuntimeExports.jsx(Input, {
        placeholder: "Search by Agent Name or ID...",
        value: searchTerm,
        onChange: (e) => setSearchTerm(e.target.value),
        className: "max-w-sm"
      }), /* @__PURE__ */ jsxRuntimeExports.jsxs(Select, {
        value: pageSize,
        onValueChange: setPageSize,
        children: [/* @__PURE__ */ jsxRuntimeExports.jsx(SelectTrigger, {
          className: "w-[180px]",
          children: /* @__PURE__ */ jsxRuntimeExports.jsx(SelectValue, {
            placeholder: "Rows per page"
          })
        }), /* @__PURE__ */ jsxRuntimeExports.jsxs(SelectContent, {
          children: [/* @__PURE__ */ jsxRuntimeExports.jsx(SelectItem, {
            value: "5",
            children: "5 per page"
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(SelectItem, {
            value: "10",
            children: "10 per page"
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(SelectItem, {
            value: "20",
            children: "20 per page"
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(SelectItem, {
            value: "50",
            children: "50 per page"
          })]
        })]
      })]
    }), /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
      className: "rounded-md border",
      children: /* @__PURE__ */ jsxRuntimeExports.jsxs(Table, {
        children: [/* @__PURE__ */ jsxRuntimeExports.jsx(TableHeader, {
          children: /* @__PURE__ */ jsxRuntimeExports.jsxs(TableRow, {
            children: [/* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
              className: "text-right",
              children: "Booked Qty"
            }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
              className: "text-right",
              children: "Delivered Qty"
            }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
              className: "text-right",
              children: "Return Qty"
            }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
              className: "text-right",
              children: "Cancel Qty"
            }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
              className: "text-right",
              children: "Shop Count"
            })]
          })
        }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableBody, {
          children: paginatedData.length > 0 ? paginatedData.map((row) => /* @__PURE__ */ jsxRuntimeExports.jsxs(TableRow, {
            children: [/* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
              className: "font-medium",
              children: row.agentId
            }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
              children: row.agentName
            }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
              className: "text-right",
              children: row.totalBookedWeight.toFixed(2)
            }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
              className: "text-right",
              children: row.totalDeliveredWeight.toFixed(2)
            }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
              className: "text-right",
              children: row.totalReturnedWeight.toFixed(2)
            }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
              className: "text-right",
              children: row.totalCancelledWeight.toFixed(2)
            }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
              className: "text-right",
              children: row.shopCount.toFixed(2)
            })]
          }, row.agentId)) : /* @__PURE__ */ jsxRuntimeExports.jsx(TableRow, {
            children: /* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
              colSpan: 7,
              className: "h-24 text-center",
              children: "No results."
            })
          })
        })]
      })
    }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
      className: "flex items-center justify-between px-2 py-4",
      children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
        className: "text-sm text-gray-500",
        children: ["Showing ", (currentPage - 1) * Number(pageSize) + 1, " to", " ", Math.min(currentPage * Number(pageSize), filteredData.length), " of", " ", filteredData.length, " results"]
      }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
        className: "flex items-center space-x-2",
        children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
          variant: "outline",
          size: "sm",
          onClick: () => setCurrentPage((prev) => Math.max(prev - 1, 1)),
          disabled: currentPage === 1,
          children: "Previous"
        }), /* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
          variant: "outline",
          size: "sm",
          onClick: () => setCurrentPage((prev) => Math.min(prev + 1, totalPages)),
          disabled: currentPage === totalPages,
          children: "Next"
        })]
      })]
    })]
  });
}
export {
  AgentWiseSales as default
};
//# sourceMappingURL=home.agentSalesReport-CxHgs7Qx.js.map
