import { j as jsxRuntimeExports } from "./jsx-runtime-bB2y7OuD.js";
import { B as But<PERSON> } from "./button-ByAXMyvk.js";
import { S as ScrollArea } from "./scroll-area-Cyn6sZm1.js";
import { C as Card, b as <PERSON><PERSON><PERSON><PERSON>, c as <PERSON><PERSON><PERSON><PERSON>, a as Card<PERSON>ontent, e as <PERSON><PERSON><PERSON><PERSON> } from "./card-BJQMSLe_.js";
import { B as Badge } from "./badge-BsHDHlRV.js";
import { U as Users } from "./users-Bl2kD8ju.js";
import { S as Share2 } from "./share-2-BRs1HrxR.js";
import { M as MessageCircle } from "./message-circle-VDmkNDXi.js";
import "./index-z_byfFrQ.js";
import "./index-ImHKLo0a.js";
import "./utils-GkgzjW3c.js";
import "./index-CzZ6EYkN.js";
import "./index-Vp2vNLNM.js";
import "./index-QLGF6kQx.js";
import "./index-D7VH9Fc8.js";
import "./index-BTdCMChR.js";
import "./index-IXOTxK3N.js";
import "./createLucideIcon-uwkRm45G.js";
function CampaignCard({ campaign, onViewDetails }) {
  const getStatusColor = (status) => {
    switch (status) {
      case "Active":
        return "bg-green-500";
      case "Scheduled":
        return "bg-blue-500";
      case "Completed":
        return "bg-gray-500";
      default:
        return "bg-gray-500";
    }
  };
  const getTypeIcon = (type) => {
    switch (type) {
      case "SMS":
        return /* @__PURE__ */ jsxRuntimeExports.jsx(MessageCircle, { className: "h-4 w-4" });
      case "WhatsApp":
        return /* @__PURE__ */ jsxRuntimeExports.jsx(Share2, { className: "h-4 w-4" });
      default:
        return null;
    }
  };
  return /* @__PURE__ */ jsxRuntimeExports.jsxs(Card, { children: [
    /* @__PURE__ */ jsxRuntimeExports.jsx(CardHeader, { children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center justify-between", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx(CardTitle, { className: "text-lg", children: campaign.title }),
      /* @__PURE__ */ jsxRuntimeExports.jsx(Badge, { className: getStatusColor(campaign.status), children: campaign.status })
    ] }) }),
    /* @__PURE__ */ jsxRuntimeExports.jsx(CardContent, { children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "space-y-2", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center text-sm text-muted-foreground", children: [
        getTypeIcon(campaign.type),
        /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "ml-2", children: campaign.type })
      ] }),
      /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center text-sm text-muted-foreground", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx(Users, { className: "h-4 w-4" }),
        /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "ml-2", children: campaign.reach })
      ] }),
      /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "text-sm text-muted-foreground", children: new Date(campaign.date).toLocaleDateString() })
    ] }) }),
    /* @__PURE__ */ jsxRuntimeExports.jsx(CardFooter, { children: /* @__PURE__ */ jsxRuntimeExports.jsx(
      Button,
      {
        variant: "outline",
        className: "w-full",
        onClick: () => onViewDetails(campaign.id),
        children: "View Details"
      }
    ) })
  ] });
}
const mockCampaigns = [{
  id: 1,
  title: "Summer Sale",
  type: "SMS",
  status: "Active",
  reach: "1,200 customers",
  date: "2024-03-20"
}, {
  id: 2,
  title: "New Product Launch",
  type: "WhatsApp",
  status: "Scheduled",
  reach: "800 customers",
  date: "2024-03-25"
}];
function Campaigns() {
  const handleViewCampaignDetails = (id) => {
    console.log("View campaign details:", id);
  };
  return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
    children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
      className: "flex justify-end mb-4 space-x-2",
      children: [/* @__PURE__ */ jsxRuntimeExports.jsxs(Button, {
        variant: "outline",
        children: [/* @__PURE__ */ jsxRuntimeExports.jsx(MessageCircle, {
          className: "h-4 w-4 mr-2"
        }), "SMS Campaign"]
      }), /* @__PURE__ */ jsxRuntimeExports.jsxs(Button, {
        children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Share2, {
          className: "h-4 w-4 mr-2"
        }), "WhatsApp Campaign"]
      })]
    }), /* @__PURE__ */ jsxRuntimeExports.jsx(ScrollArea, {
      className: "h-[calc(100vh-300px)]",
      children: /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
        className: "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",
        children: mockCampaigns.map((campaign) => /* @__PURE__ */ jsxRuntimeExports.jsx(CampaignCard, {
          campaign,
          onViewDetails: handleViewCampaignDetails
        }, campaign.id))
      })
    })]
  });
}
export {
  Campaigns as default
};
//# sourceMappingURL=seller.marketing.campaigns-B7gK4M9z.js.map
