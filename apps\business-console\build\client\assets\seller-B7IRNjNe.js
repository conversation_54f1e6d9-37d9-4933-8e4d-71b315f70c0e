import { r as reactExports, j as jsxRuntimeExports } from "./jsx-runtime-bB2y7OuD.js";
import { S as Sheet, a as SheetTrigger, b as SheetContent } from "./sheet-BP4xlyNq.js";
import { B as Button } from "./button-ByAXMyvk.js";
import { u as useLoaderData, L as Link } from "./components-D7UvGag_.js";
import { c as useLocation, N as Navigate, O as Outlet } from "./index-DhHTcibu.js";
import { M as Menu, a as Megaphone } from "./menu-jBs_wTVq.js";
import "./index-DdafHWkt.js";
import "./index-D7VH9Fc8.js";
import "./index-z_byfFrQ.js";
import "./index-DVTNuYOr.js";
import "./index-dIGjFc0I.js";
import "./index-BXzPK7u0.js";
import "./index-CzZ6EYkN.js";
import "./index-Vp2vNLNM.js";
import "./index-CEHS9zzk.js";
import "./index-QLGF6kQx.js";
import "./index-ImHKLo0a.js";
import "./utils-GkgzjW3c.js";
import "./x-CCG_WJDF.js";
import "./createLucideIcon-uwkRm45G.js";
function Seller() {
  const loaderData = useLoaderData();
  const location = useLocation();
  const [isOpen, setIsOpen] = reactExports.useState(false);
  const [shouldRedirect, setShouldRedirect] = reactExports.useState(false);
  const isWhatsAppEnabled = true;
  const activeSection = location.pathname.split("/")[2];
  reactExports.useEffect(() => {
    if (location.pathname === "/seller") {
      setShouldRedirect(true);
    } else {
      setShouldRedirect(false);
    }
  }, [location.pathname]);
  if (shouldRedirect && isWhatsAppEnabled) {
    return /* @__PURE__ */ jsxRuntimeExports.jsx(Navigate, {
      to: "/seller/marketing",
      replace: true
    });
  }
  const NavContent = () => {
    var _a, _b, _c, _d, _e, _f;
    return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
      className: "flex flex-col h-full",
      children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
        className: "flex flex-col items-start space-y-1 gap-2 p-4",
        children: [/* @__PURE__ */ jsxRuntimeExports.jsx("img", {
          src: "/mnet-logo.svg",
          alt: "mNet Logo",
          className: "h-12 w-auto"
        }), /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
          className: "flex items-center mt-4 px-4 py-2 border rounded-md w-full",
          children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
            className: "flex items-center space-x-3",
            children: [/* @__PURE__ */ jsxRuntimeExports.jsx("div", {
              className: "h-6 w-6 rounded-full bg-primary flex items-center justify-center text-m font-bold text-white",
              children: ((_d = (_c = (_b = (_a = loaderData == null ? void 0 : loaderData.userDetails) == null ? void 0 : _a.userDetails) == null ? void 0 : _b.businessName) == null ? void 0 : _c[0]) == null ? void 0 : _d.toUpperCase()) || "B"
            }), /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
              className: "text-gray-900 text-m",
              children: ((_f = (_e = loaderData == null ? void 0 : loaderData.userDetails) == null ? void 0 : _e.userDetails) == null ? void 0 : _f.businessName) || "Business Name"
            })]
          })
        })]
      }), /* @__PURE__ */ jsxRuntimeExports.jsx("nav", {
        className: "flex-1 p-4",
        children: /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
          className: "space-y-2",
          children: /* @__PURE__ */ jsxRuntimeExports.jsx(Link, {
            to: "/seller/marketing",
            onClick: () => setIsOpen(false),
            children: /* @__PURE__ */ jsxRuntimeExports.jsxs(Button, {
              variant: activeSection === "marketing" ? "secondary" : "ghost",
              className: "w-full justify-start",
              children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Megaphone, {
                className: "h-5 w-5 mr-3"
              }), "Marketing"]
            })
          })
        })
      })]
    });
  };
  return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
    className: "min-h-screen bg-white",
    children: [/* @__PURE__ */ jsxRuntimeExports.jsx("div", {
      className: "hidden md:flex md:w-64 md:flex-col md:fixed md:inset-y-0 border-r",
      children: /* @__PURE__ */ jsxRuntimeExports.jsx(NavContent, {})
    }), /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
      className: "md:hidden border-b",
      children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
        className: "flex items-center justify-between p-4",
        children: [/* @__PURE__ */ jsxRuntimeExports.jsx("img", {
          src: "/mnet-logo.svg",
          alt: "mNet Logo",
          className: "h-8 w-auto"
        }), /* @__PURE__ */ jsxRuntimeExports.jsxs(Sheet, {
          open: isOpen,
          onOpenChange: setIsOpen,
          children: [/* @__PURE__ */ jsxRuntimeExports.jsx(SheetTrigger, {
            asChild: true,
            children: /* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
              variant: "ghost",
              size: "icon",
              children: /* @__PURE__ */ jsxRuntimeExports.jsx(Menu, {
                className: "h-6 w-6"
              })
            })
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(SheetContent, {
            side: "left",
            className: "w-64 p-0",
            children: /* @__PURE__ */ jsxRuntimeExports.jsx(NavContent, {})
          })]
        })]
      })
    }), /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
      className: "md:pl-64",
      children: /* @__PURE__ */ jsxRuntimeExports.jsx("main", {
        className: "flex-1",
        children: /* @__PURE__ */ jsxRuntimeExports.jsx(Outlet, {})
      })
    })]
  });
}
export {
  Seller as default
};
//# sourceMappingURL=seller-B7IRNjNe.js.map
