import { r as reactExports, j as jsxRuntimeExports } from "./jsx-runtime-bB2y7OuD.js";
import { B as Button } from "./button-ByAXMyvk.js";
import { I as Input } from "./input-3v87qohQ.js";
import { T as Table, a as TableHeader, b as TableRow, c as TableHead, d as TableBody, e as TableCell } from "./table-CAxdJZsY.js";
import { T as Tabs, a as TabsList, b as TabsTrigger, c as TabsContent } from "./tabs-CfSdyzWr.js";
import { C as Card, b as CardHeader, c as CardTitle, a as CardContent } from "./card-BJQMSLe_.js";
import { S as Switch } from "./switch-DQ_qW6ch.js";
import { S as SpinnerLoader } from "./SpinnerLoader-CAZRmIJX.js";
import { f as formatCurrency, a as formatWeight } from "./format-Da3JpRMs.js";
import { C as CustomerDashboard } from "./bar-dashboard-TOc0xTaf.js";
import { u as useLoaderData, a as useFetcher } from "./components-D7UvGag_.js";
import { a as useNavigate } from "./index-DhHTcibu.js";
import { A as ArrowLeft } from "./arrow-left-D_Ztdrp5.js";
import { S as Save } from "./save-xzNIILKr.js";
import { S as SquarePen } from "./square-pen-BXxSi9JH.js";
import { f as format } from "./format-82yT_5--.js";
import "./index-z_byfFrQ.js";
import "./index-ImHKLo0a.js";
import "./utils-GkgzjW3c.js";
import "./index-D7VH9Fc8.js";
import "./index-CG37gmC0.js";
import "./index-qCtcpOcW.js";
import "./index-DVTNuYOr.js";
import "./index-CzZ6EYkN.js";
import "./index-Vp2vNLNM.js";
import "./index-dIGjFc0I.js";
import "./index-BTdCMChR.js";
import "./index-QLGF6kQx.js";
import "./index-CpKiYcZd.js";
import "./index-CkL5tk39.js";
import "./select-BFGSXKcr.js";
import "./index-IXOTxK3N.js";
import "./index-BXzPK7u0.js";
import "./index-CEHS9zzk.js";
import "./index-DscYByPT.js";
import "./index-C88PRvfd.js";
import "./chevron-down-pCP5jmjX.js";
import "./createLucideIcon-uwkRm45G.js";
import "./check-_dbWxIzT.js";
import "./BarChart-DYWu-Gnb.js";
import "./chevron-left-CLqBlTg1.js";
import "./chevron-right-B-tR7Kir.js";
import "./addDays-CyH8qBoF.js";
import "./addMonths-Dj4hq91A.js";
function ItemDetails() {
  const {
    sellectedSellerItemSummay,
    SelectedSellerItem: SelectedSellerItem2,
    currentPage,
    hasNextPage,
    userPermissions,
    itemId,
    itemDashBoardData,
    dashboardGroupBy
  } = useLoaderData();
  const [searchTerm, setSearchTerm] = reactExports.useState("");
  const [activeTab, setActiveTab] = reactExports.useState("dashboard");
  const fetcher = useFetcher();
  const navigate = useNavigate();
  const [dynamicContractPrices, setDynamicContractPrices] = reactExports.useState([]);
  const [uniqueId, setUnqueId] = reactExports.useState(0);
  const [cPriceEnable, setCPriceEnable] = reactExports.useState(false);
  const [cPriceEditable, setCPriceEditable] = reactExports.useState(false);
  const [customergraphData, setCustomerGraphData] = reactExports.useState(itemDashBoardData);
  const [dashboardGroup, setIsDashBoardGroup] = reactExports.useState(dashboardGroupBy);
  reactExports.useEffect(() => {
    if (userPermissions) {
      const isContractPrice = userPermissions.includes("seller_app.contracPriceEnabled");
      const isContractPriceEditable = userPermissions.includes("seller_app.contracPriceEditable");
      setCPriceEditable(isContractPriceEditable);
      setCPriceEnable(isContractPrice);
    }
  }, [userPermissions]);
  reactExports.useEffect(() => {
    var _a, _b;
    if (((_a = fetcher == null ? void 0 : fetcher.data) == null ? void 0 : _a.ContractPrice) && ((_b = fetcher.data.ContractPrice) == null ? void 0 : _b.length) > 0) {
      setDynamicContractPrices(fetcher.data.ContractPrice);
    }
  }, [fetcher == null ? void 0 : fetcher.data, activeTab === "myBuyers"]);
  reactExports.useEffect(() => {
    var _a;
    if (((_a = fetcher.data) == null ? void 0 : _a.data) && activeTab === "dashboard") {
      setCustomerGraphData(fetcher.data.data);
      setIsDashBoardGroup(fetcher.data.dashboardGroupBy);
    }
  }, [fetcher.state, activeTab === "dashboard"]);
  const searchHandle = (x) => {
    return x.buyerName.toLocaleLowerCase().includes(searchTerm.toLocaleLowerCase()) || x.buyerMobile.toString().toLocaleLowerCase().includes(searchTerm.toLocaleLowerCase());
  };
  const handleTabChange = (tab) => {
    setActiveTab(tab);
    if (tab === "myBuyers") {
      const formData = new FormData();
      formData.append("intent", "fetchContractPrices");
      formData.append("itemId", itemId.toString());
      fetcher.submit(formData, {
        method: "post"
      });
    }
  };
  const handleUniqId = (buyerId) => {
    setUnqueId(buyerId);
  };
  const handleGraph = (value, summaryDate) => {
    fetcher.submit({
      dashboardGroupBy: value,
      summaryDate: summaryDate.toISOString(),
      intent: "itemdashboard",
      ItemId: itemId.toString()
    }, {
      method: "post"
    });
  };
  return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
    className: "container mx-auto p-6",
    children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
      className: "flex items-center gap-2 mb-6",
      children: [/* @__PURE__ */ jsxRuntimeExports.jsxs(Button, {
        variant: "ghost",
        size: "sm",
        onClick: () => navigate(-1),
        children: [/* @__PURE__ */ jsxRuntimeExports.jsx(ArrowLeft, {
          className: "h-4 w-4 mr-2"
        }), "Back to My Items"]
      }), /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
        className: "text-muted-foreground",
        children: "/"
      }), /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
        className: "font-semibold",
        children: sellectedSellerItemSummay == null ? void 0 : sellectedSellerItemSummay.itemName
      })]
    }), /* @__PURE__ */ jsxRuntimeExports.jsxs(Card, {
      className: "mb-4 ",
      children: [/* @__PURE__ */ jsxRuntimeExports.jsx(CardHeader, {
        children: /* @__PURE__ */ jsxRuntimeExports.jsx(CardTitle, {
          className: "flex items-center justify-between",
          children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
            className: "flex gap-4 ",
            children: [/* @__PURE__ */ jsxRuntimeExports.jsx("img", {
              src: sellectedSellerItemSummay == null ? void 0 : sellectedSellerItemSummay.itemImage,
              height: 80,
              width: 80
            }), /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
              className: "self-center",
              children: sellectedSellerItemSummay == null ? void 0 : sellectedSellerItemSummay.itemName
            })]
          })
        })
      }), /* @__PURE__ */ jsxRuntimeExports.jsx(CardContent, {
        children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
          className: "grid grid-cols-1 md:grid-cols-2 gap-4 flex ",
          children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("p", {
            className: "flex items-center mt-1 text-sm gap-1",
            children: ["Minimum Order Qty : ", /* @__PURE__ */ jsxRuntimeExports.jsxs("p", {
              className: "text-sm text font-bold",
              children: [sellectedSellerItemSummay == null ? void 0 : sellectedSellerItemSummay.itemMinOrderQty, " "]
            }), " ", sellectedSellerItemSummay.unit]
          }), /* @__PURE__ */ jsxRuntimeExports.jsxs("p", {
            className: "flex items-start mt-1 text-sm gap-1",
            children: ["Increment Order Qty:", /* @__PURE__ */ jsxRuntimeExports.jsxs("p", {
              className: "text-sm text font-bold",
              children: [" ", sellectedSellerItemSummay == null ? void 0 : sellectedSellerItemSummay.itemIncrementQty]
            }), " ", sellectedSellerItemSummay.unit]
          })]
        })
      })]
    }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
      className: "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6",
      children: [/* @__PURE__ */ jsxRuntimeExports.jsxs(Card, {
        children: [/* @__PURE__ */ jsxRuntimeExports.jsx(CardHeader, {
          className: "flex flex-row items-center justify-between space-y-0 pb-1",
          children: /* @__PURE__ */ jsxRuntimeExports.jsx(CardTitle, {
            className: "text-sm font-medium",
            children: "Revenue"
          })
        }), /* @__PURE__ */ jsxRuntimeExports.jsxs(CardContent, {
          children: [/* @__PURE__ */ jsxRuntimeExports.jsx("div", {
            className: "text-2xl font-bold",
            children: formatCurrency((sellectedSellerItemSummay == null ? void 0 : sellectedSellerItemSummay.revenue) || 0)
          }), /* @__PURE__ */ jsxRuntimeExports.jsxs("p", {
            className: "text-xs text-muted-foreground",
            children: ["from ", (sellectedSellerItemSummay == null ? void 0 : sellectedSellerItemSummay.totalOrders.toLocaleString("en-IN")) || 0, " Customers & ", (sellectedSellerItemSummay == null ? void 0 : sellectedSellerItemSummay.totalDaysOfDelivery.toLocaleString("en-IN")) || 0, " Deliveries"]
          })]
        })]
      }), /* @__PURE__ */ jsxRuntimeExports.jsxs(Card, {
        children: [/* @__PURE__ */ jsxRuntimeExports.jsx(CardHeader, {
          className: "flex flex-row items-center justify-between space-y-0 pb-1",
          children: /* @__PURE__ */ jsxRuntimeExports.jsx(CardTitle, {
            className: "text-sm font-medium",
            children: "Returns"
          })
        }), /* @__PURE__ */ jsxRuntimeExports.jsxs(CardContent, {
          children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
            className: "text-xl font-bold",
            children: [formatWeight((sellectedSellerItemSummay == null ? void 0 : sellectedSellerItemSummay.returnedQty) || 0), " "]
          }), /* @__PURE__ */ jsxRuntimeExports.jsxs("p", {
            className: "text-xs text-destructive",
            children: ["Revenue loss : ", formatCurrency((sellectedSellerItemSummay == null ? void 0 : sellectedSellerItemSummay.returnedAmount) || 0)]
          })]
        })]
      })]
    }), /* @__PURE__ */ jsxRuntimeExports.jsxs(Tabs, {
      value: activeTab,
      onValueChange: handleTabChange,
      children: [/* @__PURE__ */ jsxRuntimeExports.jsxs(TabsList, {
        children: [/* @__PURE__ */ jsxRuntimeExports.jsx(TabsTrigger, {
          value: "dashboard",
          children: "Dashboard"
        }), /* @__PURE__ */ jsxRuntimeExports.jsx(TabsTrigger, {
          value: "sales",
          children: "Sales Report"
        }), cPriceEnable && /* @__PURE__ */ jsxRuntimeExports.jsx(TabsTrigger, {
          value: "myBuyers",
          children: "Contract Prices"
        })]
      }), activeTab === "myBuyers" && /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
        className: "flex justify-between items-center my-4",
        children: /* @__PURE__ */ jsxRuntimeExports.jsx(Input, {
          placeholder: "Search by BuyerName",
          value: searchTerm,
          onChange: (e) => setSearchTerm(e.target.value),
          className: "max-w-sm"
        })
      }), /* @__PURE__ */ jsxRuntimeExports.jsx(TabsContent, {
        value: "dashboard",
        children: /* @__PURE__ */ jsxRuntimeExports.jsx(CustomerDashboard, {
          data: customergraphData || [],
          handleGraph,
          dashboardGroupBy: dashboardGroup
        })
      }), /* @__PURE__ */ jsxRuntimeExports.jsx(TabsContent, {
        value: "sales",
        children: /* @__PURE__ */ jsxRuntimeExports.jsxs(Table, {
          children: [/* @__PURE__ */ jsxRuntimeExports.jsx(TableHeader, {
            children: /* @__PURE__ */ jsxRuntimeExports.jsxs(TableRow, {
              children: [/* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
                children: "Delivery Date"
              }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
                children: "Ordered Quantity"
              }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
                children: "Supply Shortage"
              }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
                children: "Return Quantity"
              }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
                children: "Delivered Quantity"
              }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
                children: "Revenue"
              })]
            })
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableBody, {
            children: SelectedSellerItem2 == null ? void 0 : SelectedSellerItem2.map((item) => /* @__PURE__ */ jsxRuntimeExports.jsxs(TableRow, {
              children: [/* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
                children: format(item == null ? void 0 : item.deliveryDate, "yyyy-MM-dd")
              }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
                children: item.bookedQty > 0 ? `${item == null ? void 0 : item.bookedQty} ${sellectedSellerItemSummay == null ? void 0 : sellectedSellerItemSummay.unit}` : "-"
              }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
                children: (item == null ? void 0 : item.cancelledQty) > 0 ? `${item == null ? void 0 : item.cancelledQty} ${sellectedSellerItemSummay == null ? void 0 : sellectedSellerItemSummay.unit}` : "-"
              }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
                children: item.returnedQty > 0 ? `${item == null ? void 0 : item.returnedQty}${sellectedSellerItemSummay == null ? void 0 : sellectedSellerItemSummay.unit}` : "-"
              }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
                children: item.deliveredQty > 0 ? `${item == null ? void 0 : item.deliveredQty} ${sellectedSellerItemSummay == null ? void 0 : sellectedSellerItemSummay.unit}` : "-"
              }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
                children: item.revenue > 0 ? `₹ ${item == null ? void 0 : item.revenue}` : "-"
              })]
            }, item == null ? void 0 : item.deliveryDate))
          })]
        })
      }), cPriceEnable && /* @__PURE__ */ jsxRuntimeExports.jsxs(TabsContent, {
        value: "myBuyers",
        children: [fetcher.state !== "idle" && /* @__PURE__ */ jsxRuntimeExports.jsx(SpinnerLoader, {
          size: 8,
          loading: true
        }), /* @__PURE__ */ jsxRuntimeExports.jsxs(Table, {
          children: [/* @__PURE__ */ jsxRuntimeExports.jsx(TableHeader, {
            children: /* @__PURE__ */ jsxRuntimeExports.jsxs(TableRow, {
              children: [/* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
                children: "BuyerId "
              }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
                children: "Buyer Name"
              }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
                children: "Buyer Mobile"
              }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
                children: "Price"
              }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
                children: "Enabled"
              })]
            })
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableBody, {
            children: dynamicContractPrices == null ? void 0 : dynamicContractPrices.filter((x) => searchHandle(x)).map((buyer) => /* @__PURE__ */ jsxRuntimeExports.jsx(BuyerContactPrice, {
              buyerData: buyer,
              uniqueId,
              cPriceEditable,
              handleUniqId: (buyerId) => handleUniqId(buyerId)
            }))
          })]
        })]
      })]
    })]
  });
}
function BuyerContactPrice({
  buyerData,
  handleUniqId,
  uniqueId,
  cPriceEditable
}) {
  const fetcher = useFetcher();
  const [cPrice, setCPrice] = reactExports.useState(0);
  const [buyer, setBuyer] = reactExports.useState(buyerData);
  const [isEdit, setIsEdit] = reactExports.useState(false);
  const onAddHanle = () => {
    handleUniqId(buyer.buyerId);
    const formData = new FormData();
    formData.append("intent", "ContractPrice");
    formData.append("siItemId", buyer.sellerItemId.toString());
    formData.append("buyerId", buyer.buyerId.toString());
    fetcher.submit(formData, {
      method: "POST"
    });
  };
  const onHandleStatus = (enable) => {
    handleUniqId(buyer.buyerId);
    const formData = new FormData();
    formData.append("intent", "ContractUpdateStatus");
    formData.append("cPriceId", buyer.contractPriceId.toString());
    formData.append("cPriceEnable", enable.toString());
    fetcher.submit(formData, {
      method: "POST"
    });
    setIsEdit(false);
  };
  const onHandlPrice = () => {
    handleUniqId(buyer.buyerId);
    const formData = new FormData();
    formData.append("intent", "ContractUpdatePrice");
    formData.append("cPriceId", buyer.contractPriceId.toString());
    formData.append("cPrice", cPrice.toString());
    fetcher.submit(formData, {
      method: "POST"
    });
    setIsEdit(false);
  };
  const handleEdit = () => {
    handleUniqId(buyer.buyerId);
    setIsEdit(true);
  };
  reactExports.useEffect(() => {
    if (fetcher.data) {
      setBuyer(fetcher.data);
    }
  }, [fetcher.data]);
  return /* @__PURE__ */ jsxRuntimeExports.jsxs(TableRow, {
    children: [/* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
      children: buyer == null ? void 0 : buyer.buyerId
    }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
      children: buyer == null ? void 0 : buyer.buyerName
    }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
      children: buyer == null ? void 0 : buyer.buyerMobile
    }), buyer.newItem === false && /* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
      className: "flex gap-2",
      children: isEdit && uniqueId === buyer.buyerId ? /* @__PURE__ */ jsxRuntimeExports.jsxs(jsxRuntimeExports.Fragment, {
        children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Input, {
          placeholder: buyer == null ? void 0 : buyer.cbItemPrice.toString(),
          onChange: (e) => setCPrice(Number(e.target.value)),
          type: "number",
          className: "w-20 h-10 "
        }), /* @__PURE__ */ jsxRuntimeExports.jsx(Save, {
          height: 20,
          width: 20,
          onClick: () => onHandlPrice(),
          className: "my-3 cursor-pointer "
        })]
      }) : /* @__PURE__ */ jsxRuntimeExports.jsxs(jsxRuntimeExports.Fragment, {
        children: [" ", `₹ ${buyer == null ? void 0 : buyer.cbItemPrice}`, cPriceEditable && /* @__PURE__ */ jsxRuntimeExports.jsx(SquarePen, {
          height: 20,
          width: 20,
          onClick: () => handleEdit()
        })]
      })
    }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
      children: buyer.newItem ? /* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
        type: "button",
        onClick: () => onAddHanle(),
        children: uniqueId === buyer.buyerId ? "ADDING..." : "ADD"
      }) : /* @__PURE__ */ jsxRuntimeExports.jsx(Switch, {
        checked: buyer.enabled,
        onCheckedChange: () => onHandleStatus(!buyer.enabled)
      })
    })]
  }, buyer.buyerId);
}
export {
  ItemDetails as default
};
//# sourceMappingURL=home.itemDetails._itemId-B00VxXt6.js.map
