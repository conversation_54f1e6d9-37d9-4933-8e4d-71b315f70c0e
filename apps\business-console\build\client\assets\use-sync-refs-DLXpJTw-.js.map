{"version": 3, "file": "use-sync-refs-DLXpJTw-.js", "sources": ["../../../node_modules/@headlessui/react/dist/utils/env.js", "../../../node_modules/@headlessui/react/dist/hooks/use-iso-morphic-effect.js", "../../../node_modules/@headlessui/react/dist/hooks/use-latest-value.js", "../../../node_modules/@headlessui/react/dist/hooks/use-event.js", "../../../node_modules/@headlessui/react/dist/utils/class-names.js", "../../../node_modules/@headlessui/react/dist/utils/match.js", "../../../node_modules/@headlessui/react/dist/utils/render.js", "../../../node_modules/@headlessui/react/dist/hooks/use-sync-refs.js"], "sourcesContent": ["var i=Object.defineProperty;var d=(t,e,n)=>e in t?i(t,e,{enumerable:!0,configurable:!0,writable:!0,value:n}):t[e]=n;var r=(t,e,n)=>(d(t,typeof e!=\"symbol\"?e+\"\":e,n),n);class o{constructor(){r(this,\"current\",this.detect());r(this,\"handoffState\",\"pending\");r(this,\"currentId\",0)}set(e){this.current!==e&&(this.handoffState=\"pending\",this.currentId=0,this.current=e)}reset(){this.set(this.detect())}nextId(){return++this.currentId}get isServer(){return this.current===\"server\"}get isClient(){return this.current===\"client\"}detect(){return typeof window==\"undefined\"||typeof document==\"undefined\"?\"server\":\"client\"}handoff(){this.handoffState===\"pending\"&&(this.handoffState=\"complete\")}get isHandoffComplete(){return this.handoffState===\"complete\"}}let s=new o;export{s as env};\n", "import{useEffect as f,useLayoutEffect as c}from\"react\";import{env as i}from'../utils/env.js';let n=(e,t)=>{i.isServer?f(e,t):c(e,t)};export{n as useIsoMorphicEffect};\n", "import{useRef as t}from\"react\";import{useIsoMorphicEffect as o}from'./use-iso-morphic-effect.js';function s(e){let r=t(e);return o(()=>{r.current=e},[e]),r}export{s as useLatestValue};\n", "import a from\"react\";import{useLatestValue as n}from'./use-latest-value.js';let o=function(t){let e=n(t);return a.useCallback((...r)=>e.current(...r),[e])};export{o as useEvent};\n", "function t(...r){return Array.from(new Set(r.flatMap(n=>typeof n==\"string\"?n.split(\" \"):[]))).filter(Boolean).join(\" \")}export{t as classNames};\n", "function u(r,n,...a){if(r in n){let e=n[r];return typeof e==\"function\"?e(...a):e}let t=new Error(`Tried to handle \"${r}\" but there is no handler defined. Only defined handlers are: ${Object.keys(n).map(e=>`\"${e}\"`).join(\", \")}.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,u),t}export{u as match};\n", "import E,{Fragment as b,cloneElement as j,createElement as v,forwardRef as S,isValidElement as w,use<PERSON><PERSON>back as x,useRef as k}from\"react\";import{classNames as N}from'./class-names.js';import{match as M}from'./match.js';var O=(a=>(a[a.None=0]=\"None\",a[a.RenderStrategy=1]=\"RenderStrategy\",a[a.Static=2]=\"Static\",a))(O||{}),A=(e=>(e[e.Unmount=0]=\"Unmount\",e[e.Hidden=1]=\"Hidden\",e))(A||{});function L(){let n=U();return x(r=>C({mergeRefs:n,...r}),[n])}function C({ourProps:n,theirProps:r,slot:e,defaultTag:a,features:s,visible:t=!0,name:l,mergeRefs:i}){i=i!=null?i:$;let o=P(r,n);if(t)return F(o,e,a,l,i);let y=s!=null?s:0;if(y&2){let{static:f=!1,...u}=o;if(f)return F(u,e,a,l,i)}if(y&1){let{unmount:f=!0,...u}=o;return M(f?0:1,{[0](){return null},[1](){return F({...u,hidden:!0,style:{display:\"none\"}},e,a,l,i)}})}return F(o,e,a,l,i)}function F(n,r={},e,a,s){let{as:t=e,children:l,refName:i=\"ref\",...o}=h(n,[\"unmount\",\"static\"]),y=n.ref!==void 0?{[i]:n.ref}:{},f=typeof l==\"function\"?l(r):l;\"className\"in o&&o.className&&typeof o.className==\"function\"&&(o.className=o.className(r)),o[\"aria-labelledby\"]&&o[\"aria-labelledby\"]===o.id&&(o[\"aria-labelledby\"]=void 0);let u={};if(r){let d=!1,p=[];for(let[c,T]of Object.entries(r))typeof T==\"boolean\"&&(d=!0),T===!0&&p.push(c.replace(/([A-Z])/g,g=>`-${g.toLowerCase()}`));if(d){u[\"data-headlessui-state\"]=p.join(\" \");for(let c of p)u[`data-${c}`]=\"\"}}if(t===b&&(Object.keys(m(o)).length>0||Object.keys(m(u)).length>0))if(!w(f)||Array.isArray(f)&&f.length>1){if(Object.keys(m(o)).length>0)throw new Error(['Passing props on \"Fragment\"!',\"\",`The current component <${a} /> is rendering a \"Fragment\".`,\"However we need to passthrough the following props:\",Object.keys(m(o)).concat(Object.keys(m(u))).map(d=>`  - ${d}`).join(`\n`),\"\",\"You can apply a few solutions:\",['Add an `as=\"...\"` prop, to ensure that we render an actual element instead of a \"Fragment\".',\"Render a single element as the child so that we can forward the props onto that element.\"].map(d=>`  - ${d}`).join(`\n`)].join(`\n`))}else{let d=f.props,p=d==null?void 0:d.className,c=typeof p==\"function\"?(...R)=>N(p(...R),o.className):N(p,o.className),T=c?{className:c}:{},g=P(f.props,m(h(o,[\"ref\"])));for(let R in u)R in g&&delete u[R];return j(f,Object.assign({},g,u,y,{ref:s(H(f),y.ref)},T))}return v(t,Object.assign({},h(o,[\"ref\"]),t!==b&&y,t!==b&&u),f)}function U(){let n=k([]),r=x(e=>{for(let a of n.current)a!=null&&(typeof a==\"function\"?a(e):a.current=e)},[]);return(...e)=>{if(!e.every(a=>a==null))return n.current=e,r}}function $(...n){return n.every(r=>r==null)?void 0:r=>{for(let e of n)e!=null&&(typeof e==\"function\"?e(r):e.current=r)}}function P(...n){var a;if(n.length===0)return{};if(n.length===1)return n[0];let r={},e={};for(let s of n)for(let t in s)t.startsWith(\"on\")&&typeof s[t]==\"function\"?((a=e[t])!=null||(e[t]=[]),e[t].push(s[t])):r[t]=s[t];if(r.disabled||r[\"aria-disabled\"])for(let s in e)/^(on(?:Click|Pointer|Mouse|Key)(?:Down|Up|Press)?)$/.test(s)&&(e[s]=[t=>{var l;return(l=t==null?void 0:t.preventDefault)==null?void 0:l.call(t)}]);for(let s in e)Object.assign(r,{[s](t,...l){let i=e[s];for(let o of i){if((t instanceof Event||(t==null?void 0:t.nativeEvent)instanceof Event)&&t.defaultPrevented)return;o(t,...l)}}});return r}function _(...n){var a;if(n.length===0)return{};if(n.length===1)return n[0];let r={},e={};for(let s of n)for(let t in s)t.startsWith(\"on\")&&typeof s[t]==\"function\"?((a=e[t])!=null||(e[t]=[]),e[t].push(s[t])):r[t]=s[t];for(let s in e)Object.assign(r,{[s](...t){let l=e[s];for(let i of l)i==null||i(...t)}});return r}function K(n){var r;return Object.assign(S(n),{displayName:(r=n.displayName)!=null?r:n.name})}function m(n){let r=Object.assign({},n);for(let e in r)r[e]===void 0&&delete r[e];return r}function h(n,r=[]){let e=Object.assign({},n);for(let a of r)a in e&&delete e[a];return e}function H(n){return E.version.split(\".\")[0]>=\"19\"?n.props.ref:n.ref}export{O as RenderFeatures,A as RenderStrategy,m as compact,K as forwardRefWithAs,_ as mergeProps,L as useRender};\n", "import{useEffect as l,useRef as i}from\"react\";import{useEvent as r}from'./use-event.js';let u=Symbol();function T(t,n=!0){return Object.assign(t,{[u]:n})}function y(...t){let n=i(t);l(()=>{n.current=t},[t]);let c=r(e=>{for(let o of n.current)o!=null&&(typeof o==\"function\"?o(e):o.current=e)});return t.every(e=>e==null||(e==null?void 0:e[u]))?void 0:c}export{T as optionalRef,y as useSyncRefs};\n"], "names": ["t", "n", "s", "o", "i", "f", "c", "r", "a", "u", "x", "y", "M", "d", "b", "w", "N", "j", "v", "k", "S", "E", "l"], "mappings": ";AAAA,IAAI,IAAE,OAAO;AAAe,IAAI,IAAE,CAACA,IAAE,GAAEC,OAAI,KAAKD,KAAE,EAAEA,IAAE,GAAE,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAMC,GAAC,CAAC,IAAED,GAAE,CAAC,IAAEC;AAAE,IAAI,IAAE,CAACD,IAAE,GAAEC,QAAK,EAAED,IAAE,OAAO,KAAG,WAAS,IAAE,KAAG,GAAEC,EAAC,GAAEA;AAAG,IAAA,MAAA,MAAM,EAAC;AAAA,EAAC,cAAa;AAAC,MAAE,MAAK,WAAU,KAAK,OAAM,CAAE;AAAE,MAAE,MAAK,gBAAe,SAAS;AAAE,MAAE,MAAK,aAAY,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,GAAE;AAAC,SAAK,YAAU,MAAI,KAAK,eAAa,WAAU,KAAK,YAAU,GAAE,KAAK,UAAQ;AAAA,EAAE;AAAA,EAAC,QAAO;AAAC,SAAK,IAAI,KAAK,OAAM,CAAE;AAAA,EAAC;AAAA,EAAC,SAAQ;AAAC,WAAM,EAAE,KAAK;AAAA,EAAS;AAAA,EAAC,IAAI,WAAU;AAAC,WAAO,KAAK,YAAU;AAAA,EAAQ;AAAA,EAAC,IAAI,WAAU;AAAC,WAAO,KAAK,YAAU;AAAA,EAAQ;AAAA,EAAC,SAAQ;AAAC,WAAO,OAAO,UAAQ,eAAa,OAAO,YAAU,cAAY,WAAS;AAAA,EAAQ;AAAA,EAAC,UAAS;AAAC,SAAK,iBAAe,cAAY,KAAK,eAAa;AAAA,EAAW;AAAA,EAAC,IAAI,oBAAmB;AAAC,WAAO,KAAK,iBAAe;AAAA,EAAU;AAAC;AAAI,IAACC,MAAE,IAAIC;ACAppB,IAAC,IAAE,CAAC,GAAEH,OAAI;AAACI,MAAE,WAASC,uBAAE,GAAEL,EAAC,IAAEM,6BAAE,GAAEN,EAAC;AAAC;ACAlC,SAAS,EAAE,GAAE;AAAC,MAAIO,KAAEP,aAAC,OAAC,CAAC;AAAE,SAAOG,EAAE,MAAI;AAAC,IAAAI,GAAE,UAAQ;AAAA,EAAC,GAAE,CAAC,CAAC,CAAC,GAAEA;AAAC;ACA5E,IAACJ,KAAE,SAASH,IAAE;AAAC,MAAI,IAAEC,EAAED,EAAC;AAAE,SAAOQ,MAAE,YAAY,IAAID,OAAI,EAAE,QAAQ,GAAGA,EAAC,GAAE,CAAC,CAAC,CAAC;AAAC;ACA1J,SAAS,KAAKA,IAAE;AAAC,SAAO,MAAM,KAAK,IAAI,IAAIA,GAAE,QAAQ,CAAAN,OAAG,OAAOA,MAAG,WAASA,GAAE,MAAM,GAAG,IAAE,CAAE,CAAA,CAAC,CAAC,EAAE,OAAO,OAAO,EAAE,KAAK,GAAG;AAAC;ACAvH,SAASQ,IAAEF,IAAEN,OAAK,GAAE;AAAC,MAAGM,MAAKN,IAAE;AAAC,QAAI,IAAEA,GAAEM,EAAC;AAAE,WAAO,OAAO,KAAG,aAAW,EAAE,GAAG,CAAC,IAAE;AAAA,EAAC;AAAC,MAAIP,KAAE,IAAI,MAAM,oBAAoBO,EAAC,iEAAiE,OAAO,KAAKN,EAAC,EAAE,IAAI,OAAG,IAAI,CAAC,GAAG,EAAE,KAAK,IAAI,CAAC,GAAG;AAAE,QAAM,MAAM,qBAAmB,MAAM,kBAAkBD,IAAES,GAAC,GAAET;AAAC;ACArE,IAAC,KAAG,QAAI,EAAE,EAAE,OAAK,CAAC,IAAE,QAAO,EAAE,EAAE,iBAAe,CAAC,IAAE,kBAAiB,EAAE,EAAE,SAAO,CAAC,IAAE,UAAS,IAAI,KAAG,EAAE,GAAE,KAAG,QAAI,EAAE,EAAE,UAAQ,CAAC,IAAE,WAAU,EAAE,EAAE,SAAO,CAAC,IAAE,UAAS,IAAI,KAAG,EAAE;AAAE,SAAS,IAAG;AAAC,MAAIC,KAAE,EAAC;AAAG,SAAOS,yBAAE,CAAAH,OAAG,EAAE,EAAC,WAAUN,IAAE,GAAGM,GAAC,CAAC,GAAE,CAACN,EAAC,CAAC;AAAC;AAAC,SAAS,EAAE,EAAC,UAASA,IAAE,YAAWM,IAAE,MAAK,GAAE,YAAW,GAAE,UAASL,IAAE,SAAQF,KAAE,MAAG,MAAK,GAAE,WAAUI,GAAC,GAAE;AAAC,EAAAA,KAAEA,MAAG,OAAKA,KAAE;AAAE,MAAID,KAAE,EAAEI,IAAEN,EAAC;AAAE,MAAGD,GAAE,QAAO,EAAEG,IAAE,GAAE,GAAE,GAAEC,EAAC;AAAE,MAAIO,KAAET,MAAG,OAAKA,KAAE;AAAE,MAAGS,KAAE,GAAE;AAAC,QAAG,EAAC,QAAO,IAAE,OAAG,GAAGF,GAAC,IAAEN;AAAE,QAAG,EAAE,QAAO,EAAEM,IAAE,GAAE,GAAE,GAAEL,EAAC;AAAA,EAAC;AAAC,MAAGO,KAAE,GAAE;AAAC,QAAG,EAAC,SAAQ,IAAE,MAAG,GAAGF,GAAC,IAAEN;AAAE,WAAOS,IAAE,IAAE,IAAE,GAAE,EAAC,CAAC,CAAC,IAAG;AAAC,aAAO;AAAA,IAAI,GAAE,CAAC,CAAC,IAAG;AAAC,aAAO,EAAE,EAAC,GAAGH,IAAE,QAAO,MAAG,OAAM,EAAC,SAAQ,OAAM,EAAC,GAAE,GAAE,GAAE,GAAEL,EAAC;AAAA,IAAC,EAAC,CAAC;AAAA,EAAC;AAAC,SAAO,EAAED,IAAE,GAAE,GAAE,GAAEC,EAAC;AAAC;AAAC,SAAS,EAAEH,IAAEM,KAAE,CAAA,GAAG,GAAE,GAAEL,IAAE;AAAC,MAAG,EAAC,IAAGF,MAAE,GAAE,UAAS,GAAE,SAAQI,KAAE,OAAM,GAAGD,GAAC,IAAE,EAAEF,IAAE,CAAC,WAAU,QAAQ,CAAC,GAAEU,KAAEV,GAAE,QAAM,SAAO,EAAC,CAACG,EAAC,GAAEH,GAAE,IAAG,IAAE,CAAA,GAAG,IAAE,OAAO,KAAG,aAAW,EAAEM,EAAC,IAAE;AAAE,iBAAcJ,MAAGA,GAAE,aAAW,OAAOA,GAAE,aAAW,eAAaA,GAAE,YAAUA,GAAE,UAAUI,EAAC,IAAGJ,GAAE,iBAAiB,KAAGA,GAAE,iBAAiB,MAAIA,GAAE,OAAKA,GAAE,iBAAiB,IAAE;AAAQ,MAAIM,KAAE,CAAA;AAAG,MAAGF,IAAE;AAAC,QAAIM,KAAE,OAAG,IAAE,CAAA;AAAG,aAAO,CAAC,GAAE,CAAC,KAAI,OAAO,QAAQN,EAAC,EAAE,QAAO,KAAG,cAAYM,KAAE,OAAI,MAAI,QAAI,EAAE,KAAK,EAAE,QAAQ,YAAW,OAAG,IAAI,EAAE,YAAW,CAAE,EAAE,CAAC;AAAE,QAAGA,IAAE;AAAC,MAAAJ,GAAE,uBAAuB,IAAE,EAAE,KAAK,GAAG;AAAE,eAAQ,KAAK,EAAE,CAAAA,GAAE,QAAQ,CAAC,EAAE,IAAE;AAAA,IAAE;AAAA,EAAC;AAAC,MAAGT,QAAIc,aAAAA,aAAI,OAAO,KAAK,EAAEX,EAAC,CAAC,EAAE,SAAO,KAAG,OAAO,KAAK,EAAEM,EAAC,CAAC,EAAE,SAAO,GAAG,KAAG,CAACM,aAAAA,eAAE,CAAC,KAAG,MAAM,QAAQ,CAAC,KAAG,EAAE,SAAO,GAAE;AAAC,QAAG,OAAO,KAAK,EAAEZ,EAAC,CAAC,EAAE,SAAO,EAAE,OAAM,IAAI,MAAM,CAAC,gCAA+B,IAAG,0BAA0B,CAAC,kCAAiC,uDAAsD,OAAO,KAAK,EAAEA,EAAC,CAAC,EAAE,OAAO,OAAO,KAAK,EAAEM,EAAC,CAAC,CAAC,EAAE,IAAI,CAAAI,OAAG,OAAOA,EAAC,EAAE,EAAE,KAAK;AAAA,CACnuD,GAAE,IAAG,kCAAiC,CAAC,+FAA8F,0FAA0F,EAAE,IAAI,CAAAA,OAAG,OAAOA,EAAC,EAAE,EAAE,KAAK;AAAA,CACzP,CAAC,EAAE,KAAK;AAAA,CACR,CAAC;AAAA,EAAC,OAAK;AAAC,QAAIA,KAAE,EAAE,OAAM,IAAEA,MAAG,OAAK,SAAOA,GAAE,WAAU,IAAE,OAAO,KAAG,aAAW,IAAI,MAAIG,EAAE,EAAE,GAAG,CAAC,GAAEb,GAAE,SAAS,IAAEa,EAAE,GAAEb,GAAE,SAAS,GAAE,IAAE,IAAE,EAAC,WAAU,EAAC,IAAE,CAAA,GAAG,IAAE,EAAE,EAAE,OAAM,EAAE,EAAEA,IAAE,CAAC,KAAK,CAAC,CAAC,CAAC;AAAE,aAAQ,KAAKM,GAAE,MAAK,KAAG,OAAOA,GAAE,CAAC;AAAE,WAAOQ,aAAC,aAAC,GAAE,OAAO,OAAO,CAAE,GAAC,GAAER,IAAEE,IAAE,EAAC,KAAIT,GAAE,EAAE,CAAC,GAAES,GAAE,GAAG,EAAC,GAAE,CAAC,CAAC;AAAA,EAAC;AAAC,SAAOO,aAAC,cAAClB,KAAE,OAAO,OAAO,CAAA,GAAG,EAAEG,IAAE,CAAC,KAAK,CAAC,GAAEH,QAAIc,aAAAA,YAAGH,IAAEX,QAAIc,aAAC,YAAEL,EAAC,GAAE,CAAC;AAAC;AAAC,SAAS,IAAG;AAAC,MAAIR,KAAEkB,aAAAA,OAAE,CAAE,CAAA,GAAEZ,KAAEG,yBAAE,OAAG;AAAC,aAAQ,KAAKT,GAAE,QAAQ,MAAG,SAAO,OAAO,KAAG,aAAW,EAAE,CAAC,IAAE,EAAE,UAAQ;AAAA,EAAE,GAAE,CAAE,CAAA;AAAE,SAAM,IAAI,MAAI;AAAC,QAAG,CAAC,EAAE,MAAM,OAAG,KAAG,IAAI,EAAE,QAAOA,GAAE,UAAQ,GAAEM;AAAA,EAAC;AAAC;AAAC,SAAS,KAAKN,IAAE;AAAC,SAAOA,GAAE,MAAM,CAAAM,OAAGA,MAAG,IAAI,IAAE,SAAO,CAAAA,OAAG;AAAC,aAAQ,KAAKN,GAAE,MAAG,SAAO,OAAO,KAAG,aAAW,EAAEM,EAAC,IAAE,EAAE,UAAQA;AAAA,EAAE;AAAC;AAAC,SAAS,KAAKN,IAAE;AAAO,MAAGA,GAAE,WAAS,EAAE,QAAM,CAAA;AAAG,MAAGA,GAAE,WAAS,EAAE,QAAOA,GAAE,CAAC;AAAE,MAAIM,KAAE,CAAE,GAAC,IAAE;AAAG,WAAQL,MAAKD,GAAE,UAAQD,MAAKE,GAAE,CAAAF,GAAE,WAAW,IAAI,KAAG,OAAOE,GAAEF,EAAC,KAAG,cAAe,EAAEA,EAAC,KAAI,SAAO,EAAEA,EAAC,IAAE,CAAA,IAAI,EAAEA,EAAC,EAAE,KAAKE,GAAEF,EAAC,CAAC,KAAGO,GAAEP,EAAC,IAAEE,GAAEF,EAAC;AAAE,MAAGO,GAAE,YAAUA,GAAE,eAAe,EAAE,UAAQL,MAAK,EAAE,uDAAsD,KAAKA,EAAC,MAAI,EAAEA,EAAC,IAAE,CAAC,CAAAF,OAAG;AAAC,QAAI;AAAE,YAAO,IAAEA,MAAG,OAAK,SAAOA,GAAE,mBAAiB,OAAK,SAAO,EAAE,KAAKA,EAAC;AAAA,EAAC,CAAC;AAAG,WAAQE,MAAK,EAAE,QAAO,OAAOK,IAAE,EAAC,CAACL,EAAC,EAAEF,OAAK,GAAE;AAAC,QAAII,KAAE,EAAEF,EAAC;AAAE,aAAQC,MAAKC,IAAE;AAAC,WAAIJ,cAAa,UAAQA,MAAG,OAAK,SAAOA,GAAE,wBAAuB,UAAQA,GAAE,iBAAiB;AAAO,MAAAG,GAAEH,IAAE,GAAG,CAAC;AAAA,IAAC;AAAA,EAAC,EAAC,CAAC;AAAE,SAAOO;AAAC;AAAC,SAAS,KAAKN,IAAE;AAAO,MAAGA,GAAE,WAAS,EAAE,QAAM;AAAG,MAAGA,GAAE,WAAS,EAAE,QAAOA,GAAE,CAAC;AAAE,MAAIM,KAAE,CAAA,GAAG,IAAE,CAAE;AAAC,WAAQL,MAAKD,GAAE,UAAQD,MAAKE,GAAE,CAAAF,GAAE,WAAW,IAAI,KAAG,OAAOE,GAAEF,EAAC,KAAG,cAAe,EAAEA,EAAC,KAAI,SAAO,EAAEA,EAAC,IAAE,CAAE,IAAE,EAAEA,EAAC,EAAE,KAAKE,GAAEF,EAAC,CAAC,KAAGO,GAAEP,EAAC,IAAEE,GAAEF,EAAC;AAAE,WAAQE,MAAK,EAAE,QAAO,OAAOK,IAAE,EAAC,CAACL,EAAC,KAAKF,IAAE;AAAC,QAAI,IAAE,EAAEE,EAAC;AAAE,aAAQE,MAAK,EAAE,CAAAA,MAAG,QAAMA,GAAE,GAAGJ,EAAC;AAAA,EAAC,EAAC,CAAC;AAAE,SAAOO;AAAC;AAAC,SAAS,EAAEN,IAAE;AAAC,MAAIM;AAAE,SAAO,OAAO,OAAOa,aAAAA,WAAEnB,EAAC,GAAE,EAAC,cAAaM,KAAEN,GAAE,gBAAc,OAAKM,KAAEN,GAAE,KAAI,CAAC;AAAC;AAAC,SAAS,EAAEA,IAAE;AAAC,MAAIM,KAAE,OAAO,OAAO,CAAA,GAAGN,EAAC;AAAE,WAAQ,KAAKM,GAAE,CAAAA,GAAE,CAAC,MAAI,UAAQ,OAAOA,GAAE,CAAC;AAAE,SAAOA;AAAC;AAAC,SAAS,EAAEN,IAAEM,KAAE,CAAA,GAAG;AAAC,MAAI,IAAE,OAAO,OAAO,IAAGN,EAAC;AAAE,WAAQ,KAAKM,GAAE,MAAK,KAAG,OAAO,EAAE,CAAC;AAAE,SAAO;AAAC;AAAC,SAAS,EAAEN,IAAE;AAAC,SAAOoB,MAAE,QAAQ,MAAM,GAAG,EAAE,CAAC,KAAG,OAAKpB,GAAE,MAAM,MAAIA,GAAE;AAAG;ACHrwD,IAAI,IAAE,OAAM;AAAsD,SAAS,KAAKD,IAAE;AAAC,MAAIC,KAAEG,aAAC,OAACJ,EAAC;AAAEsB,eAAC,UAAC,MAAI;AAAC,IAAArB,GAAE,UAAQD;AAAA,EAAC,GAAE,CAACA,EAAC,CAAC;AAAE,MAAI,IAAEO,GAAE,OAAG;AAAC,aAAQJ,MAAKF,GAAE,QAAQ,CAAAE,MAAG,SAAO,OAAOA,MAAG,aAAWA,GAAE,CAAC,IAAEA,GAAE,UAAQ;AAAA,EAAE,CAAC;AAAE,SAAOH,GAAE,MAAM,OAAG,KAAG,SAAO,KAAG,OAAK,SAAO,EAAE,CAAC,EAAE,IAAE,SAAO;AAAC;", "x_google_ignoreList": [0, 1, 2, 3, 4, 5, 6, 7]}