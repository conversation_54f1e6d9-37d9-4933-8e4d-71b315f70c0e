{"version": 3, "file": "format-82yT_5--.js", "sources": ["../../../node_modules/date-fns/toDate.mjs", "../../../node_modules/date-fns/constructFrom.mjs", "../../../node_modules/date-fns/constants.mjs", "../../../node_modules/date-fns/_lib/defaultOptions.mjs", "../../../node_modules/date-fns/startOfWeek.mjs", "../../../node_modules/date-fns/startOfISOWeek.mjs", "../../../node_modules/date-fns/getISOWeekYear.mjs", "../../../node_modules/date-fns/startOfDay.mjs", "../../../node_modules/date-fns/_lib/getTimezoneOffsetInMilliseconds.mjs", "../../../node_modules/date-fns/differenceInCalendarDays.mjs", "../../../node_modules/date-fns/startOfISOWeekYear.mjs", "../../../node_modules/date-fns/isDate.mjs", "../../../node_modules/date-fns/isValid.mjs", "../../../node_modules/date-fns/startOfYear.mjs", "../../../node_modules/date-fns/locale/en-US/_lib/formatDistance.mjs", "../../../node_modules/date-fns/locale/_lib/buildFormatLongFn.mjs", "../../../node_modules/date-fns/locale/en-US/_lib/formatLong.mjs", "../../../node_modules/date-fns/locale/en-US/_lib/formatRelative.mjs", "../../../node_modules/date-fns/locale/_lib/buildLocalizeFn.mjs", "../../../node_modules/date-fns/locale/en-US/_lib/localize.mjs", "../../../node_modules/date-fns/locale/_lib/buildMatchFn.mjs", "../../../node_modules/date-fns/locale/_lib/buildMatchPatternFn.mjs", "../../../node_modules/date-fns/locale/en-US/_lib/match.mjs", "../../../node_modules/date-fns/locale/en-US.mjs", "../../../node_modules/date-fns/getDayOfYear.mjs", "../../../node_modules/date-fns/getISOWeek.mjs", "../../../node_modules/date-fns/getWeekYear.mjs", "../../../node_modules/date-fns/startOfWeekYear.mjs", "../../../node_modules/date-fns/getWeek.mjs", "../../../node_modules/date-fns/_lib/addLeadingZeros.mjs", "../../../node_modules/date-fns/_lib/format/lightFormatters.mjs", "../../../node_modules/date-fns/_lib/format/formatters.mjs", "../../../node_modules/date-fns/_lib/format/longFormatters.mjs", "../../../node_modules/date-fns/_lib/protectedTokens.mjs", "../../../node_modules/date-fns/format.mjs"], "sourcesContent": ["/**\n * @name toDate\n * @category Common Helpers\n * @summary Convert the given argument to an instance of Date.\n *\n * @description\n * Convert the given argument to an instance of Date.\n *\n * If the argument is an instance of Date, the function returns its clone.\n *\n * If the argument is a number, it is treated as a timestamp.\n *\n * If the argument is none of the above, the function returns Invalid Date.\n *\n * **Note**: *all* Date arguments passed to any *date-fns* function is processed by `toDate`.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param argument - The value to convert\n *\n * @returns The parsed date in the local time zone\n *\n * @example\n * // Clone the date:\n * const result = toDate(new Date(2014, 1, 11, 11, 30, 30))\n * //=> Tue Feb 11 2014 11:30:30\n *\n * @example\n * // Convert the timestamp to date:\n * const result = toDate(1392098430000)\n * //=> Tue Feb 11 2014 11:30:30\n */\nexport function toDate(argument) {\n  const argStr = Object.prototype.toString.call(argument);\n\n  // Clone the date\n  if (\n    argument instanceof Date ||\n    (typeof argument === \"object\" && argStr === \"[object Date]\")\n  ) {\n    // Prevent the date to lose the milliseconds when passed to new Date() in IE10\n    return new argument.constructor(+argument);\n  } else if (\n    typeof argument === \"number\" ||\n    argStr === \"[object Number]\" ||\n    typeof argument === \"string\" ||\n    argStr === \"[object String]\"\n  ) {\n    // TODO: Can we get rid of as?\n    return new Date(argument);\n  } else {\n    // TODO: Can we get rid of as?\n    return new Date(NaN);\n  }\n}\n\n// Fallback for modularized imports:\nexport default toDate;\n", "/**\n * @name constructFrom\n * @category Generic Helpers\n * @summary Constructs a date using the reference date and the value\n *\n * @description\n * The function constructs a new date using the constructor from the reference\n * date and the given value. It helps to build generic functions that accept\n * date extensions.\n *\n * It defaults to `Date` if the passed reference date is a number or a string.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param date - The reference date to take constructor from\n * @param value - The value to create the date\n *\n * @returns Date initialized using the given date and value\n *\n * @example\n * import { constructFrom } from 'date-fns'\n *\n * // A function that clones a date preserving the original type\n * function cloneDate<DateType extends Date(date: DateType): DateType {\n *   return constructFrom(\n *     date, // Use contrustor from the given date\n *     date.getTime() // Use the date value to create a new date\n *   )\n * }\n */\nexport function constructFrom(date, value) {\n  if (date instanceof Date) {\n    return new date.constructor(value);\n  } else {\n    return new Date(value);\n  }\n}\n\n// Fallback for modularized imports:\nexport default constructFrom;\n", "/**\n * @module constants\n * @summary Useful constants\n * @description\n * Collection of useful date constants.\n *\n * The constants could be imported from `date-fns/constants`:\n *\n * ```ts\n * import { maxTime, minTime } from \"./constants/date-fns/constants\";\n *\n * function isAllowedTime(time) {\n *   return time <= maxTime && time >= minTime;\n * }\n * ```\n */\n\n/**\n * @constant\n * @name daysInWeek\n * @summary Days in 1 week.\n */\nexport const daysInWeek = 7;\n\n/**\n * @constant\n * @name daysInYear\n * @summary Days in 1 year.\n *\n * @description\n * How many days in a year.\n *\n * One years equals 365.2425 days according to the formula:\n *\n * > Leap year occures every 4 years, except for years that are divisable by 100 and not divisable by 400.\n * > 1 mean year = (365+1/4-1/100+1/400) days = 365.2425 days\n */\nexport const daysInYear = 365.2425;\n\n/**\n * @constant\n * @name maxTime\n * @summary Maximum allowed time.\n *\n * @example\n * import { maxTime } from \"./constants/date-fns/constants\";\n *\n * const isValid = 8640000000000001 <= maxTime;\n * //=> false\n *\n * new Date(8640000000000001);\n * //=> Invalid Date\n */\nexport const maxTime = Math.pow(10, 8) * 24 * 60 * 60 * 1000;\n\n/**\n * @constant\n * @name minTime\n * @summary Minimum allowed time.\n *\n * @example\n * import { minTime } from \"./constants/date-fns/constants\";\n *\n * const isValid = -8640000000000001 >= minTime;\n * //=> false\n *\n * new Date(-8640000000000001)\n * //=> Invalid Date\n */\nexport const minTime = -maxTime;\n\n/**\n * @constant\n * @name millisecondsInWeek\n * @summary Milliseconds in 1 week.\n */\nexport const millisecondsInWeek = 604800000;\n\n/**\n * @constant\n * @name millisecondsInDay\n * @summary Milliseconds in 1 day.\n */\nexport const millisecondsInDay = 86400000;\n\n/**\n * @constant\n * @name millisecondsInMinute\n * @summary Milliseconds in 1 minute\n */\nexport const millisecondsInMinute = 60000;\n\n/**\n * @constant\n * @name millisecondsInHour\n * @summary Milliseconds in 1 hour\n */\nexport const millisecondsInHour = 3600000;\n\n/**\n * @constant\n * @name millisecondsInSecond\n * @summary Milliseconds in 1 second\n */\nexport const millisecondsInSecond = 1000;\n\n/**\n * @constant\n * @name minutesInYear\n * @summary Minutes in 1 year.\n */\nexport const minutesInYear = 525600;\n\n/**\n * @constant\n * @name minutesInMonth\n * @summary Minutes in 1 month.\n */\nexport const minutesInMonth = 43200;\n\n/**\n * @constant\n * @name minutesInDay\n * @summary Minutes in 1 day.\n */\nexport const minutesInDay = 1440;\n\n/**\n * @constant\n * @name minutesInHour\n * @summary Minutes in 1 hour.\n */\nexport const minutesInHour = 60;\n\n/**\n * @constant\n * @name monthsInQuarter\n * @summary Months in 1 quarter.\n */\nexport const monthsInQuarter = 3;\n\n/**\n * @constant\n * @name monthsInYear\n * @summary Months in 1 year.\n */\nexport const monthsInYear = 12;\n\n/**\n * @constant\n * @name quartersInYear\n * @summary Quarters in 1 year\n */\nexport const quartersInYear = 4;\n\n/**\n * @constant\n * @name secondsInHour\n * @summary Seconds in 1 hour.\n */\nexport const secondsInHour = 3600;\n\n/**\n * @constant\n * @name secondsInMinute\n * @summary Seconds in 1 minute.\n */\nexport const secondsInMinute = 60;\n\n/**\n * @constant\n * @name secondsInDay\n * @summary Seconds in 1 day.\n */\nexport const secondsInDay = secondsInHour * 24;\n\n/**\n * @constant\n * @name secondsInWeek\n * @summary Seconds in 1 week.\n */\nexport const secondsInWeek = secondsInDay * 7;\n\n/**\n * @constant\n * @name secondsInYear\n * @summary Seconds in 1 year.\n */\nexport const secondsInYear = secondsInDay * daysInYear;\n\n/**\n * @constant\n * @name secondsInMonth\n * @summary Seconds in 1 month\n */\nexport const secondsInMonth = secondsInYear / 12;\n\n/**\n * @constant\n * @name secondsInQuarter\n * @summary Seconds in 1 quarter.\n */\nexport const secondsInQuarter = secondsInMonth * 3;\n", "let defaultOptions = {};\n\nexport function getDefaultOptions() {\n  return defaultOptions;\n}\n\nexport function setDefaultOptions(newOptions) {\n  defaultOptions = newOptions;\n}\n", "import { toDate } from \"./toDate.mjs\";\nimport { getDefaultOptions } from \"./_lib/defaultOptions.mjs\";\n\n/**\n * The {@link startOfWeek} function options.\n */\n\n/**\n * @name startOfWeek\n * @category Week Helpers\n * @summary Return the start of a week for the given date.\n *\n * @description\n * Return the start of a week for the given date.\n * The result will be in the local timezone.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param date - The original date\n * @param options - An object with options\n *\n * @returns The start of a week\n *\n * @example\n * // The start of a week for 2 September 2014 11:55:00:\n * const result = startOfWeek(new Date(2014, 8, 2, 11, 55, 0))\n * //=> Sun Aug 31 2014 00:00:00\n *\n * @example\n * // If the week starts on Monday, the start of the week for 2 September 2014 11:55:00:\n * const result = startOfWeek(new Date(2014, 8, 2, 11, 55, 0), { weekStartsOn: 1 })\n * //=> Mon Sep 01 2014 00:00:00\n */\nexport function startOfWeek(date, options) {\n  const defaultOptions = getDefaultOptions();\n  const weekStartsOn =\n    options?.weekStartsOn ??\n    options?.locale?.options?.weekStartsOn ??\n    defaultOptions.weekStartsOn ??\n    defaultOptions.locale?.options?.weekStartsOn ??\n    0;\n\n  const _date = toDate(date);\n  const day = _date.getDay();\n  const diff = (day < weekStartsOn ? 7 : 0) + day - weekStartsOn;\n\n  _date.setDate(_date.getDate() - diff);\n  _date.setHours(0, 0, 0, 0);\n  return _date;\n}\n\n// Fallback for modularized imports:\nexport default startOfWeek;\n", "import { startOfWeek } from \"./startOfWeek.mjs\";\n\n/**\n * @name startOfISOWeek\n * @category ISO Week Helpers\n * @summary Return the start of an ISO week for the given date.\n *\n * @description\n * Return the start of an ISO week for the given date.\n * The result will be in the local timezone.\n *\n * ISO week-numbering year: http://en.wikipedia.org/wiki/ISO_week_date\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param date - The original date\n *\n * @returns The start of an ISO week\n *\n * @example\n * // The start of an ISO week for 2 September 2014 11:55:00:\n * const result = startOfISOWeek(new Date(2014, 8, 2, 11, 55, 0))\n * //=> Mon Sep 01 2014 00:00:00\n */\nexport function startOfISOWeek(date) {\n  return startOfWeek(date, { weekStartsOn: 1 });\n}\n\n// Fallback for modularized imports:\nexport default startOfISOWeek;\n", "import { constructFrom } from \"./constructFrom.mjs\";\nimport { startOfISOWeek } from \"./startOfISOWeek.mjs\";\nimport { toDate } from \"./toDate.mjs\";\n\n/**\n * @name getISOWeekYear\n * @category ISO Week-Numbering Year Helpers\n * @summary Get the ISO week-numbering year of the given date.\n *\n * @description\n * Get the ISO week-numbering year of the given date,\n * which always starts 3 days before the year's first Thursday.\n *\n * ISO week-numbering year: http://en.wikipedia.org/wiki/ISO_week_date\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param date - The given date\n *\n * @returns The ISO week-numbering year\n *\n * @example\n * // Which ISO-week numbering year is 2 January 2005?\n * const result = getISOWeekYear(new Date(2005, 0, 2))\n * //=> 2004\n */\nexport function getISOWeekYear(date) {\n  const _date = toDate(date);\n  const year = _date.getFullYear();\n\n  const fourthOfJanuaryOfNextYear = constructFrom(date, 0);\n  fourthOfJanuaryOfNextYear.setFullYear(year + 1, 0, 4);\n  fourthOfJanuaryOfNextYear.setHours(0, 0, 0, 0);\n  const startOfNextYear = startOfISOWeek(fourthOfJanuaryOfNextYear);\n\n  const fourthOfJanuaryOfThisYear = constructFrom(date, 0);\n  fourthOfJanuaryOfThisYear.setFullYear(year, 0, 4);\n  fourthOfJanuaryOfThisYear.setHours(0, 0, 0, 0);\n  const startOfThisYear = startOfISOWeek(fourthOfJanuaryOfThisYear);\n\n  if (_date.getTime() >= startOfNextYear.getTime()) {\n    return year + 1;\n  } else if (_date.getTime() >= startOfThisYear.getTime()) {\n    return year;\n  } else {\n    return year - 1;\n  }\n}\n\n// Fallback for modularized imports:\nexport default getISOWeekYear;\n", "import { toDate } from \"./toDate.mjs\";\n\n/**\n * @name startOfDay\n * @category Day Helpers\n * @summary Return the start of a day for the given date.\n *\n * @description\n * Return the start of a day for the given date.\n * The result will be in the local timezone.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param date - The original date\n *\n * @returns The start of a day\n *\n * @example\n * // The start of a day for 2 September 2014 11:55:00:\n * const result = startOfDay(new Date(2014, 8, 2, 11, 55, 0))\n * //=> Tue Sep 02 2014 00:00:00\n */\nexport function startOfDay(date) {\n  const _date = toDate(date);\n  _date.setHours(0, 0, 0, 0);\n  return _date;\n}\n\n// Fallback for modularized imports:\nexport default startOfDay;\n", "import { toDate } from \"../toDate.mjs\";\n\n/**\n * Google Chrome as of 67.0.3396.87 introduced timezones with offset that includes seconds.\n * They usually appear for dates that denote time before the timezones were introduced\n * (e.g. for 'Europe/Prague' timezone the offset is GMT+00:57:44 before 1 October 1891\n * and GMT+01:00:00 after that date)\n *\n * Date#getTimezoneOffset returns the offset in minutes and would return 57 for the example above,\n * which would lead to incorrect calculations.\n *\n * This function returns the timezone offset in milliseconds that takes seconds in account.\n */\nexport function getTimezoneOffsetInMilliseconds(date) {\n  const _date = toDate(date);\n  const utcDate = new Date(\n    Date.UTC(\n      _date.getFullYear(),\n      _date.getMonth(),\n      _date.getDate(),\n      _date.getHours(),\n      _date.getMinutes(),\n      _date.getSeconds(),\n      _date.getMilliseconds(),\n    ),\n  );\n  utcDate.setUTCFullYear(_date.getFullYear());\n  return +date - +utcDate;\n}\n", "import { millisecondsInDay } from \"./constants.mjs\";\nimport { startOfDay } from \"./startOfDay.mjs\";\nimport { getTimezoneOffsetInMilliseconds } from \"./_lib/getTimezoneOffsetInMilliseconds.mjs\";\n\n/**\n * @name differenceInCalendarDays\n * @category Day Helpers\n * @summary Get the number of calendar days between the given dates.\n *\n * @description\n * Get the number of calendar days between the given dates. This means that the times are removed\n * from the dates and then the difference in days is calculated.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param dateLeft - The later date\n * @param dateRight - The earlier date\n *\n * @returns The number of calendar days\n *\n * @example\n * // How many calendar days are between\n * // 2 July 2011 23:00:00 and 2 July 2012 00:00:00?\n * const result = differenceInCalendarDays(\n *   new Date(2012, 6, 2, 0, 0),\n *   new Date(2011, 6, 2, 23, 0)\n * )\n * //=> 366\n * // How many calendar days are between\n * // 2 July 2011 23:59:00 and 3 July 2011 00:01:00?\n * const result = differenceInCalendarDays(\n *   new Date(2011, 6, 3, 0, 1),\n *   new Date(2011, 6, 2, 23, 59)\n * )\n * //=> 1\n */\nexport function differenceInCalendarDays(dateLeft, dateRight) {\n  const startOfDayLeft = startOfDay(dateLeft);\n  const startOfDayRight = startOfDay(dateRight);\n\n  const timestampLeft =\n    +startOfDayLeft - getTimezoneOffsetInMilliseconds(startOfDayLeft);\n  const timestampRight =\n    +startOfDayRight - getTimezoneOffsetInMilliseconds(startOfDayRight);\n\n  // Round the number of days to the nearest integer because the number of\n  // milliseconds in a day is not constant (e.g. it's different in the week of\n  // the daylight saving time clock shift).\n  return Math.round((timestampLeft - timestampRight) / millisecondsInDay);\n}\n\n// Fallback for modularized imports:\nexport default differenceInCalendarDays;\n", "import { getISOWeekYear } from \"./getISOWeekYear.mjs\";\nimport { startOfISOWeek } from \"./startOfISOWeek.mjs\";\nimport { constructFrom } from \"./constructFrom.mjs\";\n\n/**\n * @name startOfISOWeekYear\n * @category ISO Week-Numbering Year Helpers\n * @summary Return the start of an ISO week-numbering year for the given date.\n *\n * @description\n * Return the start of an ISO week-numbering year,\n * which always starts 3 days before the year's first Thursday.\n * The result will be in the local timezone.\n *\n * ISO week-numbering year: http://en.wikipedia.org/wiki/ISO_week_date\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param date - The original date\n *\n * @returns The start of an ISO week-numbering year\n *\n * @example\n * // The start of an ISO week-numbering year for 2 July 2005:\n * const result = startOfISOWeekYear(new Date(2005, 6, 2))\n * //=> Mon Jan 03 2005 00:00:00\n */\nexport function startOfISOWeekYear(date) {\n  const year = getISOWeekYear(date);\n  const fourthOfJanuary = constructFrom(date, 0);\n  fourthOfJanuary.setFullYear(year, 0, 4);\n  fourthOfJanuary.setHours(0, 0, 0, 0);\n  return startOfISOWeek(fourthOfJanuary);\n}\n\n// Fallback for modularized imports:\nexport default startOfISOWeekYear;\n", "/**\n * @name isDate\n * @category Common Helpers\n * @summary Is the given value a date?\n *\n * @description\n * Returns true if the given value is an instance of Date. The function works for dates transferred across iframes.\n *\n * @param value - The value to check\n *\n * @returns True if the given value is a date\n *\n * @example\n * // For a valid date:\n * const result = isDate(new Date())\n * //=> true\n *\n * @example\n * // For an invalid date:\n * const result = isDate(new Date(NaN))\n * //=> true\n *\n * @example\n * // For some value:\n * const result = isDate('2014-02-31')\n * //=> false\n *\n * @example\n * // For an object:\n * const result = isDate({})\n * //=> false\n */\nexport function isDate(value) {\n  return (\n    value instanceof Date ||\n    (typeof value === \"object\" &&\n      Object.prototype.toString.call(value) === \"[object Date]\")\n  );\n}\n\n// Fallback for modularized imports:\nexport default isDate;\n", "import { isDate } from \"./isDate.mjs\";\nimport { toDate } from \"./toDate.mjs\";\n\n/**\n * @name isValid\n * @category Common Helpers\n * @summary Is the given date valid?\n *\n * @description\n * Returns false if argument is Invalid Date and true otherwise.\n * Argument is converted to Date using `toDate`. See [toDate](https://date-fns.org/docs/toDate)\n * Invalid Date is a Date, whose time value is NaN.\n *\n * Time value of Date: http://es5.github.io/#x15.9.1.1\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param date - The date to check\n *\n * @returns The date is valid\n *\n * @example\n * // For the valid date:\n * const result = isValid(new Date(2014, 1, 31))\n * //=> true\n *\n * @example\n * // For the value, convertable into a date:\n * const result = isValid(1393804800000)\n * //=> true\n *\n * @example\n * // For the invalid date:\n * const result = isValid(new Date(''))\n * //=> false\n */\nexport function isValid(date) {\n  if (!isDate(date) && typeof date !== \"number\") {\n    return false;\n  }\n  const _date = toDate(date);\n  return !isNaN(Number(_date));\n}\n\n// Fallback for modularized imports:\nexport default isValid;\n", "import { toDate } from \"./toDate.mjs\";\nimport { constructFrom } from \"./constructFrom.mjs\";\n\n/**\n * @name startOfYear\n * @category Year Helpers\n * @summary Return the start of a year for the given date.\n *\n * @description\n * Return the start of a year for the given date.\n * The result will be in the local timezone.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param date - The original date\n *\n * @returns The start of a year\n *\n * @example\n * // The start of a year for 2 September 2014 11:55:00:\n * const result = startOfYear(new Date(2014, 8, 2, 11, 55, 00))\n * //=> Wed Jan 01 2014 00:00:00\n */\nexport function startOfYear(date) {\n  const cleanDate = toDate(date);\n  const _date = constructFrom(date, 0);\n  _date.setFullYear(cleanDate.getFullYear(), 0, 1);\n  _date.setHours(0, 0, 0, 0);\n  return _date;\n}\n\n// Fallback for modularized imports:\nexport default startOfYear;\n", "const formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: \"less than a second\",\n    other: \"less than {{count}} seconds\",\n  },\n\n  xSeconds: {\n    one: \"1 second\",\n    other: \"{{count}} seconds\",\n  },\n\n  halfAMinute: \"half a minute\",\n\n  lessThanXMinutes: {\n    one: \"less than a minute\",\n    other: \"less than {{count}} minutes\",\n  },\n\n  xMinutes: {\n    one: \"1 minute\",\n    other: \"{{count}} minutes\",\n  },\n\n  aboutXHours: {\n    one: \"about 1 hour\",\n    other: \"about {{count}} hours\",\n  },\n\n  xHours: {\n    one: \"1 hour\",\n    other: \"{{count}} hours\",\n  },\n\n  xDays: {\n    one: \"1 day\",\n    other: \"{{count}} days\",\n  },\n\n  aboutXWeeks: {\n    one: \"about 1 week\",\n    other: \"about {{count}} weeks\",\n  },\n\n  xWeeks: {\n    one: \"1 week\",\n    other: \"{{count}} weeks\",\n  },\n\n  aboutXMonths: {\n    one: \"about 1 month\",\n    other: \"about {{count}} months\",\n  },\n\n  xMonths: {\n    one: \"1 month\",\n    other: \"{{count}} months\",\n  },\n\n  aboutXYears: {\n    one: \"about 1 year\",\n    other: \"about {{count}} years\",\n  },\n\n  xYears: {\n    one: \"1 year\",\n    other: \"{{count}} years\",\n  },\n\n  overXYears: {\n    one: \"over 1 year\",\n    other: \"over {{count}} years\",\n  },\n\n  almostXYears: {\n    one: \"almost 1 year\",\n    other: \"almost {{count}} years\",\n  },\n};\n\nexport const formatDistance = (token, count, options) => {\n  let result;\n\n  const tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === \"string\") {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace(\"{{count}}\", count.toString());\n  }\n\n  if (options?.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return \"in \" + result;\n    } else {\n      return result + \" ago\";\n    }\n  }\n\n  return result;\n};\n", "export function buildFormatLongFn(args) {\n  return (options = {}) => {\n    // TODO: Remove String()\n    const width = options.width ? String(options.width) : args.defaultWidth;\n    const format = args.formats[width] || args.formats[args.defaultWidth];\n    return format;\n  };\n}\n", "import { buildFormatLongFn } from \"../../_lib/buildFormatLongFn.mjs\";\n\nconst dateFormats = {\n  full: \"EEEE, MMMM do, y\",\n  long: \"MMMM do, y\",\n  medium: \"MMM d, y\",\n  short: \"MM/dd/yyyy\",\n};\n\nconst timeFormats = {\n  full: \"h:mm:ss a zzzz\",\n  long: \"h:mm:ss a z\",\n  medium: \"h:mm:ss a\",\n  short: \"h:mm a\",\n};\n\nconst dateTimeFormats = {\n  full: \"{{date}} 'at' {{time}}\",\n  long: \"{{date}} 'at' {{time}}\",\n  medium: \"{{date}}, {{time}}\",\n  short: \"{{date}}, {{time}}\",\n};\n\nexport const formatLong = {\n  date: buildFormatLongFn({\n    formats: dateFormats,\n    defaultWidth: \"full\",\n  }),\n\n  time: buildFormatLongFn({\n    formats: timeFormats,\n    defaultWidth: \"full\",\n  }),\n\n  dateTime: buildFormatLongFn({\n    formats: dateTimeFormats,\n    defaultWidth: \"full\",\n  }),\n};\n", "const formatRelativeLocale = {\n  lastWeek: \"'last' eeee 'at' p\",\n  yesterday: \"'yesterday at' p\",\n  today: \"'today at' p\",\n  tomorrow: \"'tomorrow at' p\",\n  nextWeek: \"eeee 'at' p\",\n  other: \"P\",\n};\n\nexport const formatRelative = (token, _date, _baseDate, _options) =>\n  formatRelativeLocale[token];\n", "/* eslint-disable no-unused-vars */\n\n/**\n * The localize function argument callback which allows to convert raw value to\n * the actual type.\n *\n * @param value - The value to convert\n *\n * @returns The converted value\n */\n\n/**\n * The map of localized values for each width.\n */\n\n/**\n * The index type of the locale unit value. It types conversion of units of\n * values that don't start at 0 (i.e. quarters).\n */\n\n/**\n * Converts the unit value to the tuple of values.\n */\n\n/**\n * The tuple of localized era values. The first element represents BC,\n * the second element represents AD.\n */\n\n/**\n * The tuple of localized quarter values. The first element represents Q1.\n */\n\n/**\n * The tuple of localized day values. The first element represents Sunday.\n */\n\n/**\n * The tuple of localized month values. The first element represents January.\n */\n\nexport function buildLocalizeFn(args) {\n  return (value, options) => {\n    const context = options?.context ? String(options.context) : \"standalone\";\n\n    let valuesArray;\n    if (context === \"formatting\" && args.formattingValues) {\n      const defaultWidth = args.defaultFormattingWidth || args.defaultWidth;\n      const width = options?.width ? String(options.width) : defaultWidth;\n\n      valuesArray =\n        args.formattingValues[width] || args.formattingValues[defaultWidth];\n    } else {\n      const defaultWidth = args.defaultWidth;\n      const width = options?.width ? String(options.width) : args.defaultWidth;\n\n      valuesArray = args.values[width] || args.values[defaultWidth];\n    }\n    const index = args.argumentCallback ? args.argumentCallback(value) : value;\n\n    // @ts-expect-error - For some reason TypeScript just don't want to match it, no matter how hard we try. I challenge you to try to remove it!\n    return valuesArray[index];\n  };\n}\n", "import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.mjs\";\n\nconst eraValues = {\n  narrow: [\"B\", \"A\"],\n  abbreviated: [\"BC\", \"AD\"],\n  wide: [\"Before Christ\", \"Anno Domini\"],\n};\n\nconst quarterValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  abbreviated: [\"Q1\", \"Q2\", \"Q3\", \"Q4\"],\n  wide: [\"1st quarter\", \"2nd quarter\", \"3rd quarter\", \"4th quarter\"],\n};\n\n// Note: in English, the names of days of the week and months are capitalized.\n// If you are making a new locale based on this one, check if the same is true for the language you're working on.\n// Generally, formatted dates should look like they are in the middle of a sentence,\n// e.g. in Spanish language the weekdays and months should be in the lowercase.\nconst monthValues = {\n  narrow: [\"J\", \"F\", \"M\", \"A\", \"M\", \"J\", \"J\", \"A\", \"S\", \"O\", \"N\", \"D\"],\n  abbreviated: [\n    \"Jan\",\n    \"Feb\",\n    \"Mar\",\n    \"Apr\",\n    \"May\",\n    \"Jun\",\n    \"Jul\",\n    \"Aug\",\n    \"Sep\",\n    \"Oct\",\n    \"Nov\",\n    \"Dec\",\n  ],\n\n  wide: [\n    \"January\",\n    \"February\",\n    \"March\",\n    \"April\",\n    \"May\",\n    \"June\",\n    \"July\",\n    \"August\",\n    \"September\",\n    \"October\",\n    \"November\",\n    \"December\",\n  ],\n};\n\nconst dayValues = {\n  narrow: [\"S\", \"M\", \"T\", \"W\", \"T\", \"F\", \"S\"],\n  short: [\"Su\", \"Mo\", \"Tu\", \"We\", \"Th\", \"Fr\", \"Sa\"],\n  abbreviated: [\"Sun\", \"Mon\", \"Tue\", \"Wed\", \"Thu\", \"Fri\", \"Sat\"],\n  wide: [\n    \"Sunday\",\n    \"Monday\",\n    \"Tuesday\",\n    \"Wednesday\",\n    \"Thursday\",\n    \"Friday\",\n    \"Saturday\",\n  ],\n};\n\nconst dayPeriodValues = {\n  narrow: {\n    am: \"a\",\n    pm: \"p\",\n    midnight: \"mi\",\n    noon: \"n\",\n    morning: \"morning\",\n    afternoon: \"afternoon\",\n    evening: \"evening\",\n    night: \"night\",\n  },\n  abbreviated: {\n    am: \"AM\",\n    pm: \"PM\",\n    midnight: \"midnight\",\n    noon: \"noon\",\n    morning: \"morning\",\n    afternoon: \"afternoon\",\n    evening: \"evening\",\n    night: \"night\",\n  },\n  wide: {\n    am: \"a.m.\",\n    pm: \"p.m.\",\n    midnight: \"midnight\",\n    noon: \"noon\",\n    morning: \"morning\",\n    afternoon: \"afternoon\",\n    evening: \"evening\",\n    night: \"night\",\n  },\n};\n\nconst formattingDayPeriodValues = {\n  narrow: {\n    am: \"a\",\n    pm: \"p\",\n    midnight: \"mi\",\n    noon: \"n\",\n    morning: \"in the morning\",\n    afternoon: \"in the afternoon\",\n    evening: \"in the evening\",\n    night: \"at night\",\n  },\n  abbreviated: {\n    am: \"AM\",\n    pm: \"PM\",\n    midnight: \"midnight\",\n    noon: \"noon\",\n    morning: \"in the morning\",\n    afternoon: \"in the afternoon\",\n    evening: \"in the evening\",\n    night: \"at night\",\n  },\n  wide: {\n    am: \"a.m.\",\n    pm: \"p.m.\",\n    midnight: \"midnight\",\n    noon: \"noon\",\n    morning: \"in the morning\",\n    afternoon: \"in the afternoon\",\n    evening: \"in the evening\",\n    night: \"at night\",\n  },\n};\n\nconst ordinalNumber = (dirtyNumber, _options) => {\n  const number = Number(dirtyNumber);\n\n  // If ordinal numbers depend on context, for example,\n  // if they are different for different grammatical genders,\n  // use `options.unit`.\n  //\n  // `unit` can be 'year', 'quarter', 'month', 'week', 'date', 'dayOfYear',\n  // 'day', 'hour', 'minute', 'second'.\n\n  const rem100 = number % 100;\n  if (rem100 > 20 || rem100 < 10) {\n    switch (rem100 % 10) {\n      case 1:\n        return number + \"st\";\n      case 2:\n        return number + \"nd\";\n      case 3:\n        return number + \"rd\";\n    }\n  }\n  return number + \"th\";\n};\n\nexport const localize = {\n  ordinalNumber,\n\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\",\n  }),\n\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: (quarter) => quarter - 1,\n  }),\n\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\",\n  }),\n\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\",\n  }),\n\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: \"wide\",\n  }),\n};\n", "export function buildMatchFn(args) {\n  return (string, options = {}) => {\n    const width = options.width;\n\n    const matchPattern =\n      (width && args.matchPatterns[width]) ||\n      args.matchPatterns[args.defaultMatchWidth];\n    const matchResult = string.match(matchPattern);\n\n    if (!matchResult) {\n      return null;\n    }\n    const matchedString = matchResult[0];\n\n    const parsePatterns =\n      (width && args.parsePatterns[width]) ||\n      args.parsePatterns[args.defaultParseWidth];\n\n    const key = Array.isArray(parsePatterns)\n      ? findIndex(parsePatterns, (pattern) => pattern.test(matchedString))\n      : // eslint-disable-next-line @typescript-eslint/no-explicit-any -- I challange you to fix the type\n        findKey(parsePatterns, (pattern) => pattern.test(matchedString));\n\n    let value;\n\n    value = args.valueCallback ? args.valueCallback(key) : key;\n    value = options.valueCallback\n      ? // eslint-disable-next-line @typescript-eslint/no-explicit-any -- I challange you to fix the type\n        options.valueCallback(value)\n      : value;\n\n    const rest = string.slice(matchedString.length);\n\n    return { value, rest };\n  };\n}\n\nfunction findKey(object, predicate) {\n  for (const key in object) {\n    if (\n      Object.prototype.hasOwnProperty.call(object, key) &&\n      predicate(object[key])\n    ) {\n      return key;\n    }\n  }\n  return undefined;\n}\n\nfunction findIndex(array, predicate) {\n  for (let key = 0; key < array.length; key++) {\n    if (predicate(array[key])) {\n      return key;\n    }\n  }\n  return undefined;\n}\n", "export function buildMatchPatternFn(args) {\n  return (string, options = {}) => {\n    const matchResult = string.match(args.matchPattern);\n    if (!matchResult) return null;\n    const matchedString = matchResult[0];\n\n    const parseResult = string.match(args.parsePattern);\n    if (!parseResult) return null;\n    let value = args.valueCallback\n      ? args.valueCallback(parseResult[0])\n      : parseResult[0];\n\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any -- I challange you to fix the type\n    value = options.valueCallback ? options.valueCallback(value) : value;\n\n    const rest = string.slice(matchedString.length);\n\n    return { value, rest };\n  };\n}\n", "import { buildMatchFn } from \"../../_lib/buildMatchFn.mjs\";\nimport { buildMatchPatternFn } from \"../../_lib/buildMatchPatternFn.mjs\";\n\nconst matchOrdinalNumberPattern = /^(\\d+)(th|st|nd|rd)?/i;\nconst parseOrdinalNumberPattern = /\\d+/i;\n\nconst matchEraPatterns = {\n  narrow: /^(b|a)/i,\n  abbreviated: /^(b\\.?\\s?c\\.?|b\\.?\\s?c\\.?\\s?e\\.?|a\\.?\\s?d\\.?|c\\.?\\s?e\\.?)/i,\n  wide: /^(before christ|before common era|anno domini|common era)/i,\n};\nconst parseEraPatterns = {\n  any: [/^b/i, /^(a|c)/i],\n};\n\nconst matchQuarterPatterns = {\n  narrow: /^[1234]/i,\n  abbreviated: /^q[1234]/i,\n  wide: /^[1234](th|st|nd|rd)? quarter/i,\n};\nconst parseQuarterPatterns = {\n  any: [/1/i, /2/i, /3/i, /4/i],\n};\n\nconst matchMonthPatterns = {\n  narrow: /^[jfmasond]/i,\n  abbreviated: /^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i,\n  wide: /^(january|february|march|april|may|june|july|august|september|october|november|december)/i,\n};\nconst parseMonthPatterns = {\n  narrow: [\n    /^j/i,\n    /^f/i,\n    /^m/i,\n    /^a/i,\n    /^m/i,\n    /^j/i,\n    /^j/i,\n    /^a/i,\n    /^s/i,\n    /^o/i,\n    /^n/i,\n    /^d/i,\n  ],\n\n  any: [\n    /^ja/i,\n    /^f/i,\n    /^mar/i,\n    /^ap/i,\n    /^may/i,\n    /^jun/i,\n    /^jul/i,\n    /^au/i,\n    /^s/i,\n    /^o/i,\n    /^n/i,\n    /^d/i,\n  ],\n};\n\nconst matchDayPatterns = {\n  narrow: /^[smtwf]/i,\n  short: /^(su|mo|tu|we|th|fr|sa)/i,\n  abbreviated: /^(sun|mon|tue|wed|thu|fri|sat)/i,\n  wide: /^(sunday|monday|tuesday|wednesday|thursday|friday|saturday)/i,\n};\nconst parseDayPatterns = {\n  narrow: [/^s/i, /^m/i, /^t/i, /^w/i, /^t/i, /^f/i, /^s/i],\n  any: [/^su/i, /^m/i, /^tu/i, /^w/i, /^th/i, /^f/i, /^sa/i],\n};\n\nconst matchDayPeriodPatterns = {\n  narrow: /^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,\n  any: /^([ap]\\.?\\s?m\\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i,\n};\nconst parseDayPeriodPatterns = {\n  any: {\n    am: /^a/i,\n    pm: /^p/i,\n    midnight: /^mi/i,\n    noon: /^no/i,\n    morning: /morning/i,\n    afternoon: /afternoon/i,\n    evening: /evening/i,\n    night: /night/i,\n  },\n};\n\nexport const match = {\n  ordinalNumber: buildMatchPatternFn({\n    matchPattern: matchOrdinalNumberPattern,\n    parsePattern: parseOrdinalNumberPattern,\n    valueCallback: (value) => parseInt(value, 10),\n  }),\n\n  era: buildMatchFn({\n    matchPatterns: matchEraPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseEraPatterns,\n    defaultParseWidth: \"any\",\n  }),\n\n  quarter: buildMatchFn({\n    matchPatterns: matchQuarterPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseQuarterPatterns,\n    defaultParseWidth: \"any\",\n    valueCallback: (index) => index + 1,\n  }),\n\n  month: buildMatchFn({\n    matchPatterns: matchMonthPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseMonthPatterns,\n    defaultParseWidth: \"any\",\n  }),\n\n  day: buildMatchFn({\n    matchPatterns: matchDayPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseDayPatterns,\n    defaultParseWidth: \"any\",\n  }),\n\n  dayPeriod: buildMatchFn({\n    matchPatterns: matchDayPeriodPatterns,\n    defaultMatchWidth: \"any\",\n    parsePatterns: parseDayPeriodPatterns,\n    defaultParseWidth: \"any\",\n  }),\n};\n", "import { formatDistance } from \"./en-US/_lib/formatDistance.mjs\";\nimport { formatLong } from \"./en-US/_lib/formatLong.mjs\";\nimport { formatRelative } from \"./en-US/_lib/formatRelative.mjs\";\nimport { localize } from \"./en-US/_lib/localize.mjs\";\nimport { match } from \"./en-US/_lib/match.mjs\";\n\n/**\n * @category Locales\n * @summary English locale (United States).\n * @language English\n * @iso-639-2 eng\n * <AUTHOR> [@kossnocorp](https://github.com/kossnocorp)\n * <AUTHOR> [@leshakoss](https://github.com/leshakoss)\n */\nexport const enUS = {\n  code: \"en-US\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 0 /* Sunday */,\n    firstWeekContainsDate: 1,\n  },\n};\n\n// Fallback for modularized imports:\nexport default enUS;\n", "import { differenceInCalendarDays } from \"./differenceInCalendarDays.mjs\";\nimport { startOfYear } from \"./startOfYear.mjs\";\nimport { toDate } from \"./toDate.mjs\";\n\n/**\n * @name getDayOfYear\n * @category Day Helpers\n * @summary Get the day of the year of the given date.\n *\n * @description\n * Get the day of the year of the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param date - The given date\n *\n * @returns The day of year\n *\n * @example\n * // Which day of the year is 2 July 2014?\n * const result = getDayOfYear(new Date(2014, 6, 2))\n * //=> 183\n */\nexport function getDayOfYear(date) {\n  const _date = toDate(date);\n  const diff = differenceInCalendarDays(_date, startOfYear(_date));\n  const dayOfYear = diff + 1;\n  return dayOfYear;\n}\n\n// Fallback for modularized imports:\nexport default getDayOfYear;\n", "import { millisecondsInWeek } from \"./constants.mjs\";\nimport { startOfISOWeek } from \"./startOfISOWeek.mjs\";\nimport { startOfISOWeekYear } from \"./startOfISOWeekYear.mjs\";\nimport { toDate } from \"./toDate.mjs\";\n\n/**\n * @name getISOWeek\n * @category ISO Week Helpers\n * @summary Get the ISO week of the given date.\n *\n * @description\n * Get the ISO week of the given date.\n *\n * ISO week-numbering year: http://en.wikipedia.org/wiki/ISO_week_date\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param date - The given date\n *\n * @returns The ISO week\n *\n * @example\n * // Which week of the ISO-week numbering year is 2 January 2005?\n * const result = getISOWeek(new Date(2005, 0, 2))\n * //=> 53\n */\nexport function getISOWeek(date) {\n  const _date = toDate(date);\n  const diff = +startOfISOWeek(_date) - +startOfISOWeekYear(_date);\n\n  // Round the number of weeks to the nearest integer because the number of\n  // milliseconds in a week is not constant (e.g. it's different in the week of\n  // the daylight saving time clock shift).\n  return Math.round(diff / millisecondsInWeek) + 1;\n}\n\n// Fallback for modularized imports:\nexport default getISOWeek;\n", "import { constructFrom } from \"./constructFrom.mjs\";\nimport { startOfWeek } from \"./startOfWeek.mjs\";\nimport { toDate } from \"./toDate.mjs\";\nimport { getDefaultOptions } from \"./_lib/defaultOptions.mjs\";\n\n/**\n * The {@link getWeekYear} function options.\n */\n\n/**\n * @name getWeekYear\n * @category Week-Numbering Year Helpers\n * @summary Get the local week-numbering year of the given date.\n *\n * @description\n * Get the local week-numbering year of the given date.\n * The exact calculation depends on the values of\n * `options.weekStartsOn` (which is the index of the first day of the week)\n * and `options.firstWeekContainsDate` (which is the day of January, which is always in\n * the first week of the week-numbering year)\n *\n * Week numbering: https://en.wikipedia.org/wiki/Week#The_ISO_week_date_system\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param date - The given date\n * @param options - An object with options.\n *\n * @returns The local week-numbering year\n *\n * @example\n * // Which week numbering year is 26 December 2004 with the default settings?\n * const result = getWeekYear(new Date(2004, 11, 26))\n * //=> 2005\n *\n * @example\n * // Which week numbering year is 26 December 2004 if week starts on Saturday?\n * const result = getWeekYear(new Date(2004, 11, 26), { weekStartsOn: 6 })\n * //=> 2004\n *\n * @example\n * // Which week numbering year is 26 December 2004 if the first week contains 4 January?\n * const result = getWeekYear(new Date(2004, 11, 26), { firstWeekContainsDate: 4 })\n * //=> 2004\n */\nexport function getWeekYear(date, options) {\n  const _date = toDate(date);\n  const year = _date.getFullYear();\n\n  const defaultOptions = getDefaultOptions();\n  const firstWeekContainsDate =\n    options?.firstWeekContainsDate ??\n    options?.locale?.options?.firstWeekContainsDate ??\n    defaultOptions.firstWeekContainsDate ??\n    defaultOptions.locale?.options?.firstWeekContainsDate ??\n    1;\n\n  const firstWeekOfNextYear = constructFrom(date, 0);\n  firstWeekOfNextYear.setFullYear(year + 1, 0, firstWeekContainsDate);\n  firstWeekOfNextYear.setHours(0, 0, 0, 0);\n  const startOfNextYear = startOfWeek(firstWeekOfNextYear, options);\n\n  const firstWeekOfThisYear = constructFrom(date, 0);\n  firstWeekOfThisYear.setFullYear(year, 0, firstWeekContainsDate);\n  firstWeekOfThisYear.setHours(0, 0, 0, 0);\n  const startOfThisYear = startOfWeek(firstWeekOfThisYear, options);\n\n  if (_date.getTime() >= startOfNextYear.getTime()) {\n    return year + 1;\n  } else if (_date.getTime() >= startOfThisYear.getTime()) {\n    return year;\n  } else {\n    return year - 1;\n  }\n}\n\n// Fallback for modularized imports:\nexport default getWeekYear;\n", "import { constructFrom } from \"./constructFrom.mjs\";\nimport { getWeekYear } from \"./getWeekYear.mjs\";\nimport { startOfWeek } from \"./startOfWeek.mjs\";\nimport { getDefaultOptions } from \"./_lib/defaultOptions.mjs\";\n\n/**\n * The {@link startOfWeekYear} function options.\n */\n\n/**\n * @name startOfWeekYear\n * @category Week-Numbering Year Helpers\n * @summary Return the start of a local week-numbering year for the given date.\n *\n * @description\n * Return the start of a local week-numbering year.\n * The exact calculation depends on the values of\n * `options.weekStartsOn` (which is the index of the first day of the week)\n * and `options.firstWeekContainsDate` (which is the day of January, which is always in\n * the first week of the week-numbering year)\n *\n * Week numbering: https://en.wikipedia.org/wiki/Week#The_ISO_week_date_system\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param date - The original date\n * @param options - An object with options\n *\n * @returns The start of a week-numbering year\n *\n * @example\n * // The start of an a week-numbering year for 2 July 2005 with default settings:\n * const result = startOfWeekYear(new Date(2005, 6, 2))\n * //=> Sun Dec 26 2004 00:00:00\n *\n * @example\n * // The start of a week-numbering year for 2 July 2005\n * // if Monday is the first day of week\n * // and 4 January is always in the first week of the year:\n * const result = startOfWeekYear(new Date(2005, 6, 2), {\n *   weekStartsOn: 1,\n *   firstWeekContainsDate: 4\n * })\n * //=> Mon Jan 03 2005 00:00:00\n */\nexport function startOfWeekYear(date, options) {\n  const defaultOptions = getDefaultOptions();\n  const firstWeekContainsDate =\n    options?.firstWeekContainsDate ??\n    options?.locale?.options?.firstWeekContainsDate ??\n    defaultOptions.firstWeekContainsDate ??\n    defaultOptions.locale?.options?.firstWeekContainsDate ??\n    1;\n\n  const year = getWeekYear(date, options);\n  const firstWeek = constructFrom(date, 0);\n  firstWeek.setFullYear(year, 0, firstWeekContainsDate);\n  firstWeek.setHours(0, 0, 0, 0);\n  const _date = startOfWeek(firstWeek, options);\n  return _date;\n}\n\n// Fallback for modularized imports:\nexport default startOfWeekYear;\n", "import { millisecondsInWeek } from \"./constants.mjs\";\nimport { startOfWeek } from \"./startOfWeek.mjs\";\nimport { startOfWeekYear } from \"./startOfWeekYear.mjs\";\nimport { toDate } from \"./toDate.mjs\";\n\n/**\n * The {@link getWeek} function options.\n */\n\n/**\n * @name getWeek\n * @category Week Helpers\n * @summary Get the local week index of the given date.\n *\n * @description\n * Get the local week index of the given date.\n * The exact calculation depends on the values of\n * `options.weekStartsOn` (which is the index of the first day of the week)\n * and `options.firstWeekContainsDate` (which is the day of January, which is always in\n * the first week of the week-numbering year)\n *\n * Week numbering: https://en.wikipedia.org/wiki/Week#The_ISO_week_date_system\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param date - The given date\n * @param options - An object with options\n *\n * @returns The week\n *\n * @example\n * // Which week of the local week numbering year is 2 January 2005 with default options?\n * const result = getWeek(new Date(2005, 0, 2))\n * //=> 2\n *\n * @example\n * // Which week of the local week numbering year is 2 January 2005,\n * // if Monday is the first day of the week,\n * // and the first week of the year always contains 4 January?\n * const result = getWeek(new Date(2005, 0, 2), {\n *   weekStartsOn: 1,\n *   firstWeekContainsDate: 4\n * })\n * //=> 53\n */\n\nexport function getWeek(date, options) {\n  const _date = toDate(date);\n  const diff = +startOfWeek(_date, options) - +startOfWeekYear(_date, options);\n\n  // Round the number of weeks to the nearest integer because the number of\n  // milliseconds in a week is not constant (e.g. it's different in the week of\n  // the daylight saving time clock shift).\n  return Math.round(diff / millisecondsInWeek) + 1;\n}\n\n// Fallback for modularized imports:\nexport default getWeek;\n", "export function addLeadingZeros(number, targetLength) {\n  const sign = number < 0 ? \"-\" : \"\";\n  const output = Math.abs(number).toString().padStart(targetLength, \"0\");\n  return sign + output;\n}\n", "import { addLeadingZeros } from \"../addLeadingZeros.mjs\";\n\n/*\n * |     | Unit                           |     | Unit                           |\n * |-----|--------------------------------|-----|--------------------------------|\n * |  a  | AM, PM                         |  A* |                                |\n * |  d  | Day of month                   |  D  |                                |\n * |  h  | Hour [1-12]                    |  H  | Hour [0-23]                    |\n * |  m  | Minute                         |  M  | Month                          |\n * |  s  | Second                         |  S  | Fraction of second             |\n * |  y  | Year (abs)                     |  Y  |                                |\n *\n * Letters marked by * are not implemented but reserved by Unicode standard.\n */\n\nexport const lightFormatters = {\n  // Year\n  y(date, token) {\n    // From http://www.unicode.org/reports/tr35/tr35-31/tr35-dates.html#Date_Format_tokens\n    // | Year     |     y | yy |   yyy |  yyyy | yyyyy |\n    // |----------|-------|----|-------|-------|-------|\n    // | AD 1     |     1 | 01 |   001 |  0001 | 00001 |\n    // | AD 12    |    12 | 12 |   012 |  0012 | 00012 |\n    // | AD 123   |   123 | 23 |   123 |  0123 | 00123 |\n    // | AD 1234  |  1234 | 34 |  1234 |  1234 | 01234 |\n    // | AD 12345 | 12345 | 45 | 12345 | 12345 | 12345 |\n\n    const signedYear = date.getFullYear();\n    // Returns 1 for 1 BC (which is year 0 in JavaScript)\n    const year = signedYear > 0 ? signedYear : 1 - signedYear;\n    return addLeadingZeros(token === \"yy\" ? year % 100 : year, token.length);\n  },\n\n  // Month\n  M(date, token) {\n    const month = date.getMonth();\n    return token === \"M\" ? String(month + 1) : addLeadingZeros(month + 1, 2);\n  },\n\n  // Day of the month\n  d(date, token) {\n    return addLeadingZeros(date.getDate(), token.length);\n  },\n\n  // AM or PM\n  a(date, token) {\n    const dayPeriodEnumValue = date.getHours() / 12 >= 1 ? \"pm\" : \"am\";\n\n    switch (token) {\n      case \"a\":\n      case \"aa\":\n        return dayPeriodEnumValue.toUpperCase();\n      case \"aaa\":\n        return dayPeriodEnumValue;\n      case \"aaaaa\":\n        return dayPeriodEnumValue[0];\n      case \"aaaa\":\n      default:\n        return dayPeriodEnumValue === \"am\" ? \"a.m.\" : \"p.m.\";\n    }\n  },\n\n  // Hour [1-12]\n  h(date, token) {\n    return addLeadingZeros(date.getHours() % 12 || 12, token.length);\n  },\n\n  // Hour [0-23]\n  H(date, token) {\n    return addLeadingZeros(date.getHours(), token.length);\n  },\n\n  // Minute\n  m(date, token) {\n    return addLeadingZeros(date.getMinutes(), token.length);\n  },\n\n  // Second\n  s(date, token) {\n    return addLeadingZeros(date.getSeconds(), token.length);\n  },\n\n  // Fraction of second\n  S(date, token) {\n    const numberOfDigits = token.length;\n    const milliseconds = date.getMilliseconds();\n    const fractionalSeconds = Math.trunc(\n      milliseconds * Math.pow(10, numberOfDigits - 3),\n    );\n    return addLeadingZeros(fractionalSeconds, token.length);\n  },\n};\n", "import { getDayOfYear } from \"../../getDayOfYear.mjs\";\nimport { getISOWeek } from \"../../getISOWeek.mjs\";\nimport { getISOWeekYear } from \"../../getISOWeekYear.mjs\";\nimport { getWeek } from \"../../getWeek.mjs\";\nimport { getWeekYear } from \"../../getWeekYear.mjs\";\nimport { addLeadingZeros } from \"../addLeadingZeros.mjs\";\nimport { lightFormatters } from \"./lightFormatters.mjs\";\n\nconst dayPeriodEnum = {\n  am: \"am\",\n  pm: \"pm\",\n  midnight: \"midnight\",\n  noon: \"noon\",\n  morning: \"morning\",\n  afternoon: \"afternoon\",\n  evening: \"evening\",\n  night: \"night\",\n};\n\n/*\n * |     | Unit                           |     | Unit                           |\n * |-----|--------------------------------|-----|--------------------------------|\n * |  a  | AM, PM                         |  A* | Milliseconds in day            |\n * |  b  | AM, PM, noon, midnight         |  B  | Flexible day period            |\n * |  c  | Stand-alone local day of week  |  C* | Localized hour w/ day period   |\n * |  d  | Day of month                   |  D  | Day of year                    |\n * |  e  | Local day of week              |  E  | Day of week                    |\n * |  f  |                                |  F* | Day of week in month           |\n * |  g* | Modified Julian day            |  G  | Era                            |\n * |  h  | Hour [1-12]                    |  H  | Hour [0-23]                    |\n * |  i! | ISO day of week                |  I! | ISO week of year               |\n * |  j* | Localized hour w/ day period   |  J* | Localized hour w/o day period  |\n * |  k  | Hour [1-24]                    |  K  | Hour [0-11]                    |\n * |  l* | (deprecated)                   |  L  | Stand-alone month              |\n * |  m  | Minute                         |  M  | Month                          |\n * |  n  |                                |  N  |                                |\n * |  o! | Ordinal number modifier        |  O  | Timezone (GMT)                 |\n * |  p! | Long localized time            |  P! | Long localized date            |\n * |  q  | Stand-alone quarter            |  Q  | Quarter                        |\n * |  r* | Related Gregorian year         |  R! | ISO week-numbering year        |\n * |  s  | Second                         |  S  | Fraction of second             |\n * |  t! | Seconds timestamp              |  T! | Milliseconds timestamp         |\n * |  u  | Extended year                  |  U* | Cyclic year                    |\n * |  v* | Timezone (generic non-locat.)  |  V* | Timezone (location)            |\n * |  w  | Local week of year             |  W* | Week of month                  |\n * |  x  | Timezone (ISO-8601 w/o Z)      |  X  | Timezone (ISO-8601)            |\n * |  y  | Year (abs)                     |  Y  | Local week-numbering year      |\n * |  z  | Timezone (specific non-locat.) |  Z* | Timezone (aliases)             |\n *\n * Letters marked by * are not implemented but reserved by Unicode standard.\n *\n * Letters marked by ! are non-standard, but implemented by date-fns:\n * - `o` modifies the previous token to turn it into an ordinal (see `format` docs)\n * - `i` is ISO day of week. For `i` and `ii` is returns numeric ISO week days,\n *   i.e. 7 for Sunday, 1 for Monday, etc.\n * - `I` is ISO week of year, as opposed to `w` which is local week of year.\n * - `R` is ISO week-numbering year, as opposed to `Y` which is local week-numbering year.\n *   `R` is supposed to be used in conjunction with `I` and `i`\n *   for universal ISO week-numbering date, whereas\n *   `Y` is supposed to be used in conjunction with `w` and `e`\n *   for week-numbering date specific to the locale.\n * - `P` is long localized date format\n * - `p` is long localized time format\n */\n\nexport const formatters = {\n  // Era\n  G: function (date, token, localize) {\n    const era = date.getFullYear() > 0 ? 1 : 0;\n    switch (token) {\n      // AD, BC\n      case \"G\":\n      case \"GG\":\n      case \"GGG\":\n        return localize.era(era, { width: \"abbreviated\" });\n      // A, B\n      case \"GGGGG\":\n        return localize.era(era, { width: \"narrow\" });\n      // Anno Domini, Before Christ\n      case \"GGGG\":\n      default:\n        return localize.era(era, { width: \"wide\" });\n    }\n  },\n\n  // Year\n  y: function (date, token, localize) {\n    // Ordinal number\n    if (token === \"yo\") {\n      const signedYear = date.getFullYear();\n      // Returns 1 for 1 BC (which is year 0 in JavaScript)\n      const year = signedYear > 0 ? signedYear : 1 - signedYear;\n      return localize.ordinalNumber(year, { unit: \"year\" });\n    }\n\n    return lightFormatters.y(date, token);\n  },\n\n  // Local week-numbering year\n  Y: function (date, token, localize, options) {\n    const signedWeekYear = getWeekYear(date, options);\n    // Returns 1 for 1 BC (which is year 0 in JavaScript)\n    const weekYear = signedWeekYear > 0 ? signedWeekYear : 1 - signedWeekYear;\n\n    // Two digit year\n    if (token === \"YY\") {\n      const twoDigitYear = weekYear % 100;\n      return addLeadingZeros(twoDigitYear, 2);\n    }\n\n    // Ordinal number\n    if (token === \"Yo\") {\n      return localize.ordinalNumber(weekYear, { unit: \"year\" });\n    }\n\n    // Padding\n    return addLeadingZeros(weekYear, token.length);\n  },\n\n  // ISO week-numbering year\n  R: function (date, token) {\n    const isoWeekYear = getISOWeekYear(date);\n\n    // Padding\n    return addLeadingZeros(isoWeekYear, token.length);\n  },\n\n  // Extended year. This is a single number designating the year of this calendar system.\n  // The main difference between `y` and `u` localizers are B.C. years:\n  // | Year | `y` | `u` |\n  // |------|-----|-----|\n  // | AC 1 |   1 |   1 |\n  // | BC 1 |   1 |   0 |\n  // | BC 2 |   2 |  -1 |\n  // Also `yy` always returns the last two digits of a year,\n  // while `uu` pads single digit years to 2 characters and returns other years unchanged.\n  u: function (date, token) {\n    const year = date.getFullYear();\n    return addLeadingZeros(year, token.length);\n  },\n\n  // Quarter\n  Q: function (date, token, localize) {\n    const quarter = Math.ceil((date.getMonth() + 1) / 3);\n    switch (token) {\n      // 1, 2, 3, 4\n      case \"Q\":\n        return String(quarter);\n      // 01, 02, 03, 04\n      case \"QQ\":\n        return addLeadingZeros(quarter, 2);\n      // 1st, 2nd, 3rd, 4th\n      case \"Qo\":\n        return localize.ordinalNumber(quarter, { unit: \"quarter\" });\n      // Q1, Q2, Q3, Q4\n      case \"QQQ\":\n        return localize.quarter(quarter, {\n          width: \"abbreviated\",\n          context: \"formatting\",\n        });\n      // 1, 2, 3, 4 (narrow quarter; could be not numerical)\n      case \"QQQQQ\":\n        return localize.quarter(quarter, {\n          width: \"narrow\",\n          context: \"formatting\",\n        });\n      // 1st quarter, 2nd quarter, ...\n      case \"QQQQ\":\n      default:\n        return localize.quarter(quarter, {\n          width: \"wide\",\n          context: \"formatting\",\n        });\n    }\n  },\n\n  // Stand-alone quarter\n  q: function (date, token, localize) {\n    const quarter = Math.ceil((date.getMonth() + 1) / 3);\n    switch (token) {\n      // 1, 2, 3, 4\n      case \"q\":\n        return String(quarter);\n      // 01, 02, 03, 04\n      case \"qq\":\n        return addLeadingZeros(quarter, 2);\n      // 1st, 2nd, 3rd, 4th\n      case \"qo\":\n        return localize.ordinalNumber(quarter, { unit: \"quarter\" });\n      // Q1, Q2, Q3, Q4\n      case \"qqq\":\n        return localize.quarter(quarter, {\n          width: \"abbreviated\",\n          context: \"standalone\",\n        });\n      // 1, 2, 3, 4 (narrow quarter; could be not numerical)\n      case \"qqqqq\":\n        return localize.quarter(quarter, {\n          width: \"narrow\",\n          context: \"standalone\",\n        });\n      // 1st quarter, 2nd quarter, ...\n      case \"qqqq\":\n      default:\n        return localize.quarter(quarter, {\n          width: \"wide\",\n          context: \"standalone\",\n        });\n    }\n  },\n\n  // Month\n  M: function (date, token, localize) {\n    const month = date.getMonth();\n    switch (token) {\n      case \"M\":\n      case \"MM\":\n        return lightFormatters.M(date, token);\n      // 1st, 2nd, ..., 12th\n      case \"Mo\":\n        return localize.ordinalNumber(month + 1, { unit: \"month\" });\n      // Jan, Feb, ..., Dec\n      case \"MMM\":\n        return localize.month(month, {\n          width: \"abbreviated\",\n          context: \"formatting\",\n        });\n      // J, F, ..., D\n      case \"MMMMM\":\n        return localize.month(month, {\n          width: \"narrow\",\n          context: \"formatting\",\n        });\n      // January, February, ..., December\n      case \"MMMM\":\n      default:\n        return localize.month(month, { width: \"wide\", context: \"formatting\" });\n    }\n  },\n\n  // Stand-alone month\n  L: function (date, token, localize) {\n    const month = date.getMonth();\n    switch (token) {\n      // 1, 2, ..., 12\n      case \"L\":\n        return String(month + 1);\n      // 01, 02, ..., 12\n      case \"LL\":\n        return addLeadingZeros(month + 1, 2);\n      // 1st, 2nd, ..., 12th\n      case \"Lo\":\n        return localize.ordinalNumber(month + 1, { unit: \"month\" });\n      // Jan, Feb, ..., Dec\n      case \"LLL\":\n        return localize.month(month, {\n          width: \"abbreviated\",\n          context: \"standalone\",\n        });\n      // J, F, ..., D\n      case \"LLLLL\":\n        return localize.month(month, {\n          width: \"narrow\",\n          context: \"standalone\",\n        });\n      // January, February, ..., December\n      case \"LLLL\":\n      default:\n        return localize.month(month, { width: \"wide\", context: \"standalone\" });\n    }\n  },\n\n  // Local week of year\n  w: function (date, token, localize, options) {\n    const week = getWeek(date, options);\n\n    if (token === \"wo\") {\n      return localize.ordinalNumber(week, { unit: \"week\" });\n    }\n\n    return addLeadingZeros(week, token.length);\n  },\n\n  // ISO week of year\n  I: function (date, token, localize) {\n    const isoWeek = getISOWeek(date);\n\n    if (token === \"Io\") {\n      return localize.ordinalNumber(isoWeek, { unit: \"week\" });\n    }\n\n    return addLeadingZeros(isoWeek, token.length);\n  },\n\n  // Day of the month\n  d: function (date, token, localize) {\n    if (token === \"do\") {\n      return localize.ordinalNumber(date.getDate(), { unit: \"date\" });\n    }\n\n    return lightFormatters.d(date, token);\n  },\n\n  // Day of year\n  D: function (date, token, localize) {\n    const dayOfYear = getDayOfYear(date);\n\n    if (token === \"Do\") {\n      return localize.ordinalNumber(dayOfYear, { unit: \"dayOfYear\" });\n    }\n\n    return addLeadingZeros(dayOfYear, token.length);\n  },\n\n  // Day of week\n  E: function (date, token, localize) {\n    const dayOfWeek = date.getDay();\n    switch (token) {\n      // Tue\n      case \"E\":\n      case \"EE\":\n      case \"EEE\":\n        return localize.day(dayOfWeek, {\n          width: \"abbreviated\",\n          context: \"formatting\",\n        });\n      // T\n      case \"EEEEE\":\n        return localize.day(dayOfWeek, {\n          width: \"narrow\",\n          context: \"formatting\",\n        });\n      // Tu\n      case \"EEEEEE\":\n        return localize.day(dayOfWeek, {\n          width: \"short\",\n          context: \"formatting\",\n        });\n      // Tuesday\n      case \"EEEE\":\n      default:\n        return localize.day(dayOfWeek, {\n          width: \"wide\",\n          context: \"formatting\",\n        });\n    }\n  },\n\n  // Local day of week\n  e: function (date, token, localize, options) {\n    const dayOfWeek = date.getDay();\n    const localDayOfWeek = (dayOfWeek - options.weekStartsOn + 8) % 7 || 7;\n    switch (token) {\n      // Numerical value (Nth day of week with current locale or weekStartsOn)\n      case \"e\":\n        return String(localDayOfWeek);\n      // Padded numerical value\n      case \"ee\":\n        return addLeadingZeros(localDayOfWeek, 2);\n      // 1st, 2nd, ..., 7th\n      case \"eo\":\n        return localize.ordinalNumber(localDayOfWeek, { unit: \"day\" });\n      case \"eee\":\n        return localize.day(dayOfWeek, {\n          width: \"abbreviated\",\n          context: \"formatting\",\n        });\n      // T\n      case \"eeeee\":\n        return localize.day(dayOfWeek, {\n          width: \"narrow\",\n          context: \"formatting\",\n        });\n      // Tu\n      case \"eeeeee\":\n        return localize.day(dayOfWeek, {\n          width: \"short\",\n          context: \"formatting\",\n        });\n      // Tuesday\n      case \"eeee\":\n      default:\n        return localize.day(dayOfWeek, {\n          width: \"wide\",\n          context: \"formatting\",\n        });\n    }\n  },\n\n  // Stand-alone local day of week\n  c: function (date, token, localize, options) {\n    const dayOfWeek = date.getDay();\n    const localDayOfWeek = (dayOfWeek - options.weekStartsOn + 8) % 7 || 7;\n    switch (token) {\n      // Numerical value (same as in `e`)\n      case \"c\":\n        return String(localDayOfWeek);\n      // Padded numerical value\n      case \"cc\":\n        return addLeadingZeros(localDayOfWeek, token.length);\n      // 1st, 2nd, ..., 7th\n      case \"co\":\n        return localize.ordinalNumber(localDayOfWeek, { unit: \"day\" });\n      case \"ccc\":\n        return localize.day(dayOfWeek, {\n          width: \"abbreviated\",\n          context: \"standalone\",\n        });\n      // T\n      case \"ccccc\":\n        return localize.day(dayOfWeek, {\n          width: \"narrow\",\n          context: \"standalone\",\n        });\n      // Tu\n      case \"cccccc\":\n        return localize.day(dayOfWeek, {\n          width: \"short\",\n          context: \"standalone\",\n        });\n      // Tuesday\n      case \"cccc\":\n      default:\n        return localize.day(dayOfWeek, {\n          width: \"wide\",\n          context: \"standalone\",\n        });\n    }\n  },\n\n  // ISO day of week\n  i: function (date, token, localize) {\n    const dayOfWeek = date.getDay();\n    const isoDayOfWeek = dayOfWeek === 0 ? 7 : dayOfWeek;\n    switch (token) {\n      // 2\n      case \"i\":\n        return String(isoDayOfWeek);\n      // 02\n      case \"ii\":\n        return addLeadingZeros(isoDayOfWeek, token.length);\n      // 2nd\n      case \"io\":\n        return localize.ordinalNumber(isoDayOfWeek, { unit: \"day\" });\n      // Tue\n      case \"iii\":\n        return localize.day(dayOfWeek, {\n          width: \"abbreviated\",\n          context: \"formatting\",\n        });\n      // T\n      case \"iiiii\":\n        return localize.day(dayOfWeek, {\n          width: \"narrow\",\n          context: \"formatting\",\n        });\n      // Tu\n      case \"iiiiii\":\n        return localize.day(dayOfWeek, {\n          width: \"short\",\n          context: \"formatting\",\n        });\n      // Tuesday\n      case \"iiii\":\n      default:\n        return localize.day(dayOfWeek, {\n          width: \"wide\",\n          context: \"formatting\",\n        });\n    }\n  },\n\n  // AM or PM\n  a: function (date, token, localize) {\n    const hours = date.getHours();\n    const dayPeriodEnumValue = hours / 12 >= 1 ? \"pm\" : \"am\";\n\n    switch (token) {\n      case \"a\":\n      case \"aa\":\n        return localize.dayPeriod(dayPeriodEnumValue, {\n          width: \"abbreviated\",\n          context: \"formatting\",\n        });\n      case \"aaa\":\n        return localize\n          .dayPeriod(dayPeriodEnumValue, {\n            width: \"abbreviated\",\n            context: \"formatting\",\n          })\n          .toLowerCase();\n      case \"aaaaa\":\n        return localize.dayPeriod(dayPeriodEnumValue, {\n          width: \"narrow\",\n          context: \"formatting\",\n        });\n      case \"aaaa\":\n      default:\n        return localize.dayPeriod(dayPeriodEnumValue, {\n          width: \"wide\",\n          context: \"formatting\",\n        });\n    }\n  },\n\n  // AM, PM, midnight, noon\n  b: function (date, token, localize) {\n    const hours = date.getHours();\n    let dayPeriodEnumValue;\n    if (hours === 12) {\n      dayPeriodEnumValue = dayPeriodEnum.noon;\n    } else if (hours === 0) {\n      dayPeriodEnumValue = dayPeriodEnum.midnight;\n    } else {\n      dayPeriodEnumValue = hours / 12 >= 1 ? \"pm\" : \"am\";\n    }\n\n    switch (token) {\n      case \"b\":\n      case \"bb\":\n        return localize.dayPeriod(dayPeriodEnumValue, {\n          width: \"abbreviated\",\n          context: \"formatting\",\n        });\n      case \"bbb\":\n        return localize\n          .dayPeriod(dayPeriodEnumValue, {\n            width: \"abbreviated\",\n            context: \"formatting\",\n          })\n          .toLowerCase();\n      case \"bbbbb\":\n        return localize.dayPeriod(dayPeriodEnumValue, {\n          width: \"narrow\",\n          context: \"formatting\",\n        });\n      case \"bbbb\":\n      default:\n        return localize.dayPeriod(dayPeriodEnumValue, {\n          width: \"wide\",\n          context: \"formatting\",\n        });\n    }\n  },\n\n  // in the morning, in the afternoon, in the evening, at night\n  B: function (date, token, localize) {\n    const hours = date.getHours();\n    let dayPeriodEnumValue;\n    if (hours >= 17) {\n      dayPeriodEnumValue = dayPeriodEnum.evening;\n    } else if (hours >= 12) {\n      dayPeriodEnumValue = dayPeriodEnum.afternoon;\n    } else if (hours >= 4) {\n      dayPeriodEnumValue = dayPeriodEnum.morning;\n    } else {\n      dayPeriodEnumValue = dayPeriodEnum.night;\n    }\n\n    switch (token) {\n      case \"B\":\n      case \"BB\":\n      case \"BBB\":\n        return localize.dayPeriod(dayPeriodEnumValue, {\n          width: \"abbreviated\",\n          context: \"formatting\",\n        });\n      case \"BBBBB\":\n        return localize.dayPeriod(dayPeriodEnumValue, {\n          width: \"narrow\",\n          context: \"formatting\",\n        });\n      case \"BBBB\":\n      default:\n        return localize.dayPeriod(dayPeriodEnumValue, {\n          width: \"wide\",\n          context: \"formatting\",\n        });\n    }\n  },\n\n  // Hour [1-12]\n  h: function (date, token, localize) {\n    if (token === \"ho\") {\n      let hours = date.getHours() % 12;\n      if (hours === 0) hours = 12;\n      return localize.ordinalNumber(hours, { unit: \"hour\" });\n    }\n\n    return lightFormatters.h(date, token);\n  },\n\n  // Hour [0-23]\n  H: function (date, token, localize) {\n    if (token === \"Ho\") {\n      return localize.ordinalNumber(date.getHours(), { unit: \"hour\" });\n    }\n\n    return lightFormatters.H(date, token);\n  },\n\n  // Hour [0-11]\n  K: function (date, token, localize) {\n    const hours = date.getHours() % 12;\n\n    if (token === \"Ko\") {\n      return localize.ordinalNumber(hours, { unit: \"hour\" });\n    }\n\n    return addLeadingZeros(hours, token.length);\n  },\n\n  // Hour [1-24]\n  k: function (date, token, localize) {\n    let hours = date.getHours();\n    if (hours === 0) hours = 24;\n\n    if (token === \"ko\") {\n      return localize.ordinalNumber(hours, { unit: \"hour\" });\n    }\n\n    return addLeadingZeros(hours, token.length);\n  },\n\n  // Minute\n  m: function (date, token, localize) {\n    if (token === \"mo\") {\n      return localize.ordinalNumber(date.getMinutes(), { unit: \"minute\" });\n    }\n\n    return lightFormatters.m(date, token);\n  },\n\n  // Second\n  s: function (date, token, localize) {\n    if (token === \"so\") {\n      return localize.ordinalNumber(date.getSeconds(), { unit: \"second\" });\n    }\n\n    return lightFormatters.s(date, token);\n  },\n\n  // Fraction of second\n  S: function (date, token) {\n    return lightFormatters.S(date, token);\n  },\n\n  // Timezone (ISO-8601. If offset is 0, output is always `'Z'`)\n  X: function (date, token, _localize) {\n    const timezoneOffset = date.getTimezoneOffset();\n\n    if (timezoneOffset === 0) {\n      return \"Z\";\n    }\n\n    switch (token) {\n      // Hours and optional minutes\n      case \"X\":\n        return formatTimezoneWithOptionalMinutes(timezoneOffset);\n\n      // Hours, minutes and optional seconds without `:` delimiter\n      // Note: neither ISO-8601 nor JavaScript supports seconds in timezone offsets\n      // so this token always has the same output as `XX`\n      case \"XXXX\":\n      case \"XX\": // Hours and minutes without `:` delimiter\n        return formatTimezone(timezoneOffset);\n\n      // Hours, minutes and optional seconds with `:` delimiter\n      // Note: neither ISO-8601 nor JavaScript supports seconds in timezone offsets\n      // so this token always has the same output as `XXX`\n      case \"XXXXX\":\n      case \"XXX\": // Hours and minutes with `:` delimiter\n      default:\n        return formatTimezone(timezoneOffset, \":\");\n    }\n  },\n\n  // Timezone (ISO-8601. If offset is 0, output is `'+00:00'` or equivalent)\n  x: function (date, token, _localize) {\n    const timezoneOffset = date.getTimezoneOffset();\n\n    switch (token) {\n      // Hours and optional minutes\n      case \"x\":\n        return formatTimezoneWithOptionalMinutes(timezoneOffset);\n\n      // Hours, minutes and optional seconds without `:` delimiter\n      // Note: neither ISO-8601 nor JavaScript supports seconds in timezone offsets\n      // so this token always has the same output as `xx`\n      case \"xxxx\":\n      case \"xx\": // Hours and minutes without `:` delimiter\n        return formatTimezone(timezoneOffset);\n\n      // Hours, minutes and optional seconds with `:` delimiter\n      // Note: neither ISO-8601 nor JavaScript supports seconds in timezone offsets\n      // so this token always has the same output as `xxx`\n      case \"xxxxx\":\n      case \"xxx\": // Hours and minutes with `:` delimiter\n      default:\n        return formatTimezone(timezoneOffset, \":\");\n    }\n  },\n\n  // Timezone (GMT)\n  O: function (date, token, _localize) {\n    const timezoneOffset = date.getTimezoneOffset();\n\n    switch (token) {\n      // Short\n      case \"O\":\n      case \"OO\":\n      case \"OOO\":\n        return \"GMT\" + formatTimezoneShort(timezoneOffset, \":\");\n      // Long\n      case \"OOOO\":\n      default:\n        return \"GMT\" + formatTimezone(timezoneOffset, \":\");\n    }\n  },\n\n  // Timezone (specific non-location)\n  z: function (date, token, _localize) {\n    const timezoneOffset = date.getTimezoneOffset();\n\n    switch (token) {\n      // Short\n      case \"z\":\n      case \"zz\":\n      case \"zzz\":\n        return \"GMT\" + formatTimezoneShort(timezoneOffset, \":\");\n      // Long\n      case \"zzzz\":\n      default:\n        return \"GMT\" + formatTimezone(timezoneOffset, \":\");\n    }\n  },\n\n  // Seconds timestamp\n  t: function (date, token, _localize) {\n    const timestamp = Math.trunc(date.getTime() / 1000);\n    return addLeadingZeros(timestamp, token.length);\n  },\n\n  // Milliseconds timestamp\n  T: function (date, token, _localize) {\n    const timestamp = date.getTime();\n    return addLeadingZeros(timestamp, token.length);\n  },\n};\n\nfunction formatTimezoneShort(offset, delimiter = \"\") {\n  const sign = offset > 0 ? \"-\" : \"+\";\n  const absOffset = Math.abs(offset);\n  const hours = Math.trunc(absOffset / 60);\n  const minutes = absOffset % 60;\n  if (minutes === 0) {\n    return sign + String(hours);\n  }\n  return sign + String(hours) + delimiter + addLeadingZeros(minutes, 2);\n}\n\nfunction formatTimezoneWithOptionalMinutes(offset, delimiter) {\n  if (offset % 60 === 0) {\n    const sign = offset > 0 ? \"-\" : \"+\";\n    return sign + addLeadingZeros(Math.abs(offset) / 60, 2);\n  }\n  return formatTimezone(offset, delimiter);\n}\n\nfunction formatTimezone(offset, delimiter = \"\") {\n  const sign = offset > 0 ? \"-\" : \"+\";\n  const absOffset = Math.abs(offset);\n  const hours = addLeadingZeros(Math.trunc(absOffset / 60), 2);\n  const minutes = addLeadingZeros(absOffset % 60, 2);\n  return sign + hours + delimiter + minutes;\n}\n", "const dateLongFormatter = (pattern, formatLong) => {\n  switch (pattern) {\n    case \"P\":\n      return formatLong.date({ width: \"short\" });\n    case \"PP\":\n      return formatLong.date({ width: \"medium\" });\n    case \"PPP\":\n      return formatLong.date({ width: \"long\" });\n    case \"PPPP\":\n    default:\n      return formatLong.date({ width: \"full\" });\n  }\n};\n\nconst timeLongFormatter = (pattern, formatLong) => {\n  switch (pattern) {\n    case \"p\":\n      return formatLong.time({ width: \"short\" });\n    case \"pp\":\n      return formatLong.time({ width: \"medium\" });\n    case \"ppp\":\n      return formatLong.time({ width: \"long\" });\n    case \"pppp\":\n    default:\n      return formatLong.time({ width: \"full\" });\n  }\n};\n\nconst dateTimeLongFormatter = (pattern, formatLong) => {\n  const matchResult = pattern.match(/(P+)(p+)?/) || [];\n  const datePattern = matchResult[1];\n  const timePattern = matchResult[2];\n\n  if (!timePattern) {\n    return dateLongFormatter(pattern, formatLong);\n  }\n\n  let dateTimeFormat;\n\n  switch (datePattern) {\n    case \"P\":\n      dateTimeFormat = formatLong.dateTime({ width: \"short\" });\n      break;\n    case \"PP\":\n      dateTimeFormat = formatLong.dateTime({ width: \"medium\" });\n      break;\n    case \"PPP\":\n      dateTimeFormat = formatLong.dateTime({ width: \"long\" });\n      break;\n    case \"PPPP\":\n    default:\n      dateTimeFormat = formatLong.dateTime({ width: \"full\" });\n      break;\n  }\n\n  return dateTimeFormat\n    .replace(\"{{date}}\", dateLongFormatter(datePattern, formatLong))\n    .replace(\"{{time}}\", timeLongFormatter(timePattern, formatLong));\n};\n\nexport const longFormatters = {\n  p: timeLongFormatter,\n  P: dateTimeLongFormatter,\n};\n", "const dayOfYearTokenRE = /^D+$/;\nconst weekYearTokenRE = /^Y+$/;\n\nconst throwTokens = [\"D\", \"DD\", \"YY\", \"YYYY\"];\n\nexport function isProtectedDayOfYearToken(token) {\n  return dayOfYearTokenRE.test(token);\n}\n\nexport function isProtectedWeekYearToken(token) {\n  return weekYearTokenRE.test(token);\n}\n\nexport function warnOrThrowProtectedError(token, format, input) {\n  const _message = message(token, format, input);\n  console.warn(_message);\n  if (throwTokens.includes(token)) throw new RangeError(_message);\n}\n\nfunction message(token, format, input) {\n  const subject = token[0] === \"Y\" ? \"years\" : \"days of the month\";\n  return `Use \\`${token.toLowerCase()}\\` instead of \\`${token}\\` (in \\`${format}\\`) for formatting ${subject} to the input \\`${input}\\`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md`;\n}\n", "import { defaultLocale } from \"./_lib/defaultLocale.mjs\";\nimport { getDefaultOptions } from \"./_lib/defaultOptions.mjs\";\nimport { formatters } from \"./_lib/format/formatters.mjs\";\nimport { longFormatters } from \"./_lib/format/longFormatters.mjs\";\nimport {\n  isProtectedDayOfYearToken,\n  isProtectedWeekYearToken,\n  warnOrThrowProtectedError,\n} from \"./_lib/protectedTokens.mjs\";\nimport { isValid } from \"./isValid.mjs\";\nimport { toDate } from \"./toDate.mjs\";\n\n// Rexports of internal for libraries to use.\n// See: https://github.com/date-fns/date-fns/issues/3638#issuecomment-1877082874\nexport { formatters, longFormatters };\n\n// This RegExp consists of three parts separated by `|`:\n// - [yYQqMLwIdDecihHKkms]o matches any available ordinal number token\n//   (one of the certain letters followed by `o`)\n// - (\\w)\\1* matches any sequences of the same letter\n// - '' matches two quote characters in a row\n// - '(''|[^'])+('|$) matches anything surrounded by two quote characters ('),\n//   except a single quote symbol, which ends the sequence.\n//   Two quote characters do not end the sequence.\n//   If there is no matching single quote\n//   then the sequence will continue until the end of the string.\n// - . matches any single character unmatched by previous parts of the RegExps\nconst formattingTokensRegExp =\n  /[yYQqMLwIdDecihHKkms]o|(\\w)\\1*|''|'(''|[^'])+('|$)|./g;\n\n// This RegExp catches symbols escaped by quotes, and also\n// sequences of symbols P, p, and the combinations like `PPPPPPPppppp`\nconst longFormattingTokensRegExp = /P+p+|P+|p+|''|'(''|[^'])+('|$)|./g;\n\nconst escapedStringRegExp = /^'([^]*?)'?$/;\nconst doubleQuoteRegExp = /''/g;\nconst unescapedLatinCharacterRegExp = /[a-zA-Z]/;\n\nexport { format as formatDate };\n\n/**\n * The {@link format} function options.\n */\n\n/**\n * @name format\n * @alias formatDate\n * @category Common Helpers\n * @summary Format the date.\n *\n * @description\n * Return the formatted date string in the given format. The result may vary by locale.\n *\n * > ⚠️ Please note that the `format` tokens differ from Moment.js and other libraries.\n * > See: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md\n *\n * The characters wrapped between two single quotes characters (') are escaped.\n * Two single quotes in a row, whether inside or outside a quoted sequence, represent a 'real' single quote.\n * (see the last example)\n *\n * Format of the string is based on Unicode Technical Standard #35:\n * https://www.unicode.org/reports/tr35/tr35-dates.html#Date_Field_Symbol_Table\n * with a few additions (see note 7 below the table).\n *\n * Accepted patterns:\n * | Unit                            | Pattern | Result examples                   | Notes |\n * |---------------------------------|---------|-----------------------------------|-------|\n * | Era                             | G..GGG  | AD, BC                            |       |\n * |                                 | GGGG    | Anno Domini, Before Christ        | 2     |\n * |                                 | GGGGG   | A, B                              |       |\n * | Calendar year                   | y       | 44, 1, 1900, 2017                 | 5     |\n * |                                 | yo      | 44th, 1st, 0th, 17th              | 5,7   |\n * |                                 | yy      | 44, 01, 00, 17                    | 5     |\n * |                                 | yyy     | 044, 001, 1900, 2017              | 5     |\n * |                                 | yyyy    | 0044, 0001, 1900, 2017            | 5     |\n * |                                 | yyyyy   | ...                               | 3,5   |\n * | Local week-numbering year       | Y       | 44, 1, 1900, 2017                 | 5     |\n * |                                 | Yo      | 44th, 1st, 1900th, 2017th         | 5,7   |\n * |                                 | YY      | 44, 01, 00, 17                    | 5,8   |\n * |                                 | YYY     | 044, 001, 1900, 2017              | 5     |\n * |                                 | YYYY    | 0044, 0001, 1900, 2017            | 5,8   |\n * |                                 | YYYYY   | ...                               | 3,5   |\n * | ISO week-numbering year         | R       | -43, 0, 1, 1900, 2017             | 5,7   |\n * |                                 | RR      | -43, 00, 01, 1900, 2017           | 5,7   |\n * |                                 | RRR     | -043, 000, 001, 1900, 2017        | 5,7   |\n * |                                 | RRRR    | -0043, 0000, 0001, 1900, 2017     | 5,7   |\n * |                                 | RRRRR   | ...                               | 3,5,7 |\n * | Extended year                   | u       | -43, 0, 1, 1900, 2017             | 5     |\n * |                                 | uu      | -43, 01, 1900, 2017               | 5     |\n * |                                 | uuu     | -043, 001, 1900, 2017             | 5     |\n * |                                 | uuuu    | -0043, 0001, 1900, 2017           | 5     |\n * |                                 | uuuuu   | ...                               | 3,5   |\n * | Quarter (formatting)            | Q       | 1, 2, 3, 4                        |       |\n * |                                 | Qo      | 1st, 2nd, 3rd, 4th                | 7     |\n * |                                 | QQ      | 01, 02, 03, 04                    |       |\n * |                                 | QQQ     | Q1, Q2, Q3, Q4                    |       |\n * |                                 | QQQQ    | 1st quarter, 2nd quarter, ...     | 2     |\n * |                                 | QQQQQ   | 1, 2, 3, 4                        | 4     |\n * | Quarter (stand-alone)           | q       | 1, 2, 3, 4                        |       |\n * |                                 | qo      | 1st, 2nd, 3rd, 4th                | 7     |\n * |                                 | qq      | 01, 02, 03, 04                    |       |\n * |                                 | qqq     | Q1, Q2, Q3, Q4                    |       |\n * |                                 | qqqq    | 1st quarter, 2nd quarter, ...     | 2     |\n * |                                 | qqqqq   | 1, 2, 3, 4                        | 4     |\n * | Month (formatting)              | M       | 1, 2, ..., 12                     |       |\n * |                                 | Mo      | 1st, 2nd, ..., 12th               | 7     |\n * |                                 | MM      | 01, 02, ..., 12                   |       |\n * |                                 | MMM     | Jan, Feb, ..., Dec                |       |\n * |                                 | MMMM    | January, February, ..., December  | 2     |\n * |                                 | MMMMM   | J, F, ..., D                      |       |\n * | Month (stand-alone)             | L       | 1, 2, ..., 12                     |       |\n * |                                 | Lo      | 1st, 2nd, ..., 12th               | 7     |\n * |                                 | LL      | 01, 02, ..., 12                   |       |\n * |                                 | LLL     | Jan, Feb, ..., Dec                |       |\n * |                                 | LLLL    | January, February, ..., December  | 2     |\n * |                                 | LLLLL   | J, F, ..., D                      |       |\n * | Local week of year              | w       | 1, 2, ..., 53                     |       |\n * |                                 | wo      | 1st, 2nd, ..., 53th               | 7     |\n * |                                 | ww      | 01, 02, ..., 53                   |       |\n * | ISO week of year                | I       | 1, 2, ..., 53                     | 7     |\n * |                                 | Io      | 1st, 2nd, ..., 53th               | 7     |\n * |                                 | II      | 01, 02, ..., 53                   | 7     |\n * | Day of month                    | d       | 1, 2, ..., 31                     |       |\n * |                                 | do      | 1st, 2nd, ..., 31st               | 7     |\n * |                                 | dd      | 01, 02, ..., 31                   |       |\n * | Day of year                     | D       | 1, 2, ..., 365, 366               | 9     |\n * |                                 | Do      | 1st, 2nd, ..., 365th, 366th       | 7     |\n * |                                 | DD      | 01, 02, ..., 365, 366             | 9     |\n * |                                 | DDD     | 001, 002, ..., 365, 366           |       |\n * |                                 | DDDD    | ...                               | 3     |\n * | Day of week (formatting)        | E..EEE  | Mon, Tue, Wed, ..., Sun           |       |\n * |                                 | EEEE    | Monday, Tuesday, ..., Sunday      | 2     |\n * |                                 | EEEEE   | M, T, W, T, F, S, S               |       |\n * |                                 | EEEEEE  | Mo, Tu, We, Th, Fr, Sa, Su        |       |\n * | ISO day of week (formatting)    | i       | 1, 2, 3, ..., 7                   | 7     |\n * |                                 | io      | 1st, 2nd, ..., 7th                | 7     |\n * |                                 | ii      | 01, 02, ..., 07                   | 7     |\n * |                                 | iii     | Mon, Tue, Wed, ..., Sun           | 7     |\n * |                                 | iiii    | Monday, Tuesday, ..., Sunday      | 2,7   |\n * |                                 | iiiii   | M, T, W, T, F, S, S               | 7     |\n * |                                 | iiiiii  | Mo, Tu, We, Th, Fr, Sa, Su        | 7     |\n * | Local day of week (formatting)  | e       | 2, 3, 4, ..., 1                   |       |\n * |                                 | eo      | 2nd, 3rd, ..., 1st                | 7     |\n * |                                 | ee      | 02, 03, ..., 01                   |       |\n * |                                 | eee     | Mon, Tue, Wed, ..., Sun           |       |\n * |                                 | eeee    | Monday, Tuesday, ..., Sunday      | 2     |\n * |                                 | eeeee   | M, T, W, T, F, S, S               |       |\n * |                                 | eeeeee  | Mo, Tu, We, Th, Fr, Sa, Su        |       |\n * | Local day of week (stand-alone) | c       | 2, 3, 4, ..., 1                   |       |\n * |                                 | co      | 2nd, 3rd, ..., 1st                | 7     |\n * |                                 | cc      | 02, 03, ..., 01                   |       |\n * |                                 | ccc     | Mon, Tue, Wed, ..., Sun           |       |\n * |                                 | cccc    | Monday, Tuesday, ..., Sunday      | 2     |\n * |                                 | ccccc   | M, T, W, T, F, S, S               |       |\n * |                                 | cccccc  | Mo, Tu, We, Th, Fr, Sa, Su        |       |\n * | AM, PM                          | a..aa   | AM, PM                            |       |\n * |                                 | aaa     | am, pm                            |       |\n * |                                 | aaaa    | a.m., p.m.                        | 2     |\n * |                                 | aaaaa   | a, p                              |       |\n * | AM, PM, noon, midnight          | b..bb   | AM, PM, noon, midnight            |       |\n * |                                 | bbb     | am, pm, noon, midnight            |       |\n * |                                 | bbbb    | a.m., p.m., noon, midnight        | 2     |\n * |                                 | bbbbb   | a, p, n, mi                       |       |\n * | Flexible day period             | B..BBB  | at night, in the morning, ...     |       |\n * |                                 | BBBB    | at night, in the morning, ...     | 2     |\n * |                                 | BBBBB   | at night, in the morning, ...     |       |\n * | Hour [1-12]                     | h       | 1, 2, ..., 11, 12                 |       |\n * |                                 | ho      | 1st, 2nd, ..., 11th, 12th         | 7     |\n * |                                 | hh      | 01, 02, ..., 11, 12               |       |\n * | Hour [0-23]                     | H       | 0, 1, 2, ..., 23                  |       |\n * |                                 | Ho      | 0th, 1st, 2nd, ..., 23rd          | 7     |\n * |                                 | HH      | 00, 01, 02, ..., 23               |       |\n * | Hour [0-11]                     | K       | 1, 2, ..., 11, 0                  |       |\n * |                                 | Ko      | 1st, 2nd, ..., 11th, 0th          | 7     |\n * |                                 | KK      | 01, 02, ..., 11, 00               |       |\n * | Hour [1-24]                     | k       | 24, 1, 2, ..., 23                 |       |\n * |                                 | ko      | 24th, 1st, 2nd, ..., 23rd         | 7     |\n * |                                 | kk      | 24, 01, 02, ..., 23               |       |\n * | Minute                          | m       | 0, 1, ..., 59                     |       |\n * |                                 | mo      | 0th, 1st, ..., 59th               | 7     |\n * |                                 | mm      | 00, 01, ..., 59                   |       |\n * | Second                          | s       | 0, 1, ..., 59                     |       |\n * |                                 | so      | 0th, 1st, ..., 59th               | 7     |\n * |                                 | ss      | 00, 01, ..., 59                   |       |\n * | Fraction of second              | S       | 0, 1, ..., 9                      |       |\n * |                                 | SS      | 00, 01, ..., 99                   |       |\n * |                                 | SSS     | 000, 001, ..., 999                |       |\n * |                                 | SSSS    | ...                               | 3     |\n * | Timezone (ISO-8601 w/ Z)        | X       | -08, +0530, Z                     |       |\n * |                                 | XX      | -0800, +0530, Z                   |       |\n * |                                 | XXX     | -08:00, +05:30, Z                 |       |\n * |                                 | XXXX    | -0800, +0530, Z, +123456          | 2     |\n * |                                 | XXXXX   | -08:00, +05:30, Z, +12:34:56      |       |\n * | Timezone (ISO-8601 w/o Z)       | x       | -08, +0530, +00                   |       |\n * |                                 | xx      | -0800, +0530, +0000               |       |\n * |                                 | xxx     | -08:00, +05:30, +00:00            | 2     |\n * |                                 | xxxx    | -0800, +0530, +0000, +123456      |       |\n * |                                 | xxxxx   | -08:00, +05:30, +00:00, +12:34:56 |       |\n * | Timezone (GMT)                  | O...OOO | GMT-8, GMT+5:30, GMT+0            |       |\n * |                                 | OOOO    | GMT-08:00, GMT+05:30, GMT+00:00   | 2     |\n * | Timezone (specific non-locat.)  | z...zzz | GMT-8, GMT+5:30, GMT+0            | 6     |\n * |                                 | zzzz    | GMT-08:00, GMT+05:30, GMT+00:00   | 2,6   |\n * | Seconds timestamp               | t       | 512969520                         | 7     |\n * |                                 | tt      | ...                               | 3,7   |\n * | Milliseconds timestamp          | T       | 512969520900                      | 7     |\n * |                                 | TT      | ...                               | 3,7   |\n * | Long localized date             | P       | 04/29/1453                        | 7     |\n * |                                 | PP      | Apr 29, 1453                      | 7     |\n * |                                 | PPP     | April 29th, 1453                  | 7     |\n * |                                 | PPPP    | Friday, April 29th, 1453          | 2,7   |\n * | Long localized time             | p       | 12:00 AM                          | 7     |\n * |                                 | pp      | 12:00:00 AM                       | 7     |\n * |                                 | ppp     | 12:00:00 AM GMT+2                 | 7     |\n * |                                 | pppp    | 12:00:00 AM GMT+02:00             | 2,7   |\n * | Combination of date and time    | Pp      | 04/29/1453, 12:00 AM              | 7     |\n * |                                 | PPpp    | Apr 29, 1453, 12:00:00 AM         | 7     |\n * |                                 | PPPppp  | April 29th, 1453 at ...           | 7     |\n * |                                 | PPPPpppp| Friday, April 29th, 1453 at ...   | 2,7   |\n * Notes:\n * 1. \"Formatting\" units (e.g. formatting quarter) in the default en-US locale\n *    are the same as \"stand-alone\" units, but are different in some languages.\n *    \"Formatting\" units are declined according to the rules of the language\n *    in the context of a date. \"Stand-alone\" units are always nominative singular:\n *\n *    `format(new Date(2017, 10, 6), 'do LLLL', {locale: cs}) //=> '6. listopad'`\n *\n *    `format(new Date(2017, 10, 6), 'do MMMM', {locale: cs}) //=> '6. listopadu'`\n *\n * 2. Any sequence of the identical letters is a pattern, unless it is escaped by\n *    the single quote characters (see below).\n *    If the sequence is longer than listed in table (e.g. `EEEEEEEEEEE`)\n *    the output will be the same as default pattern for this unit, usually\n *    the longest one (in case of ISO weekdays, `EEEE`). Default patterns for units\n *    are marked with \"2\" in the last column of the table.\n *\n *    `format(new Date(2017, 10, 6), 'MMM') //=> 'Nov'`\n *\n *    `format(new Date(2017, 10, 6), 'MMMM') //=> 'November'`\n *\n *    `format(new Date(2017, 10, 6), 'MMMMM') //=> 'N'`\n *\n *    `format(new Date(2017, 10, 6), 'MMMMMM') //=> 'November'`\n *\n *    `format(new Date(2017, 10, 6), 'MMMMMMM') //=> 'November'`\n *\n * 3. Some patterns could be unlimited length (such as `yyyyyyyy`).\n *    The output will be padded with zeros to match the length of the pattern.\n *\n *    `format(new Date(2017, 10, 6), 'yyyyyyyy') //=> '00002017'`\n *\n * 4. `QQQQQ` and `qqqqq` could be not strictly numerical in some locales.\n *    These tokens represent the shortest form of the quarter.\n *\n * 5. The main difference between `y` and `u` patterns are B.C. years:\n *\n *    | Year | `y` | `u` |\n *    |------|-----|-----|\n *    | AC 1 |   1 |   1 |\n *    | BC 1 |   1 |   0 |\n *    | BC 2 |   2 |  -1 |\n *\n *    Also `yy` always returns the last two digits of a year,\n *    while `uu` pads single digit years to 2 characters and returns other years unchanged:\n *\n *    | Year | `yy` | `uu` |\n *    |------|------|------|\n *    | 1    |   01 |   01 |\n *    | 14   |   14 |   14 |\n *    | 376  |   76 |  376 |\n *    | 1453 |   53 | 1453 |\n *\n *    The same difference is true for local and ISO week-numbering years (`Y` and `R`),\n *    except local week-numbering years are dependent on `options.weekStartsOn`\n *    and `options.firstWeekContainsDate` (compare [getISOWeekYear](https://date-fns.org/docs/getISOWeekYear)\n *    and [getWeekYear](https://date-fns.org/docs/getWeekYear)).\n *\n * 6. Specific non-location timezones are currently unavailable in `date-fns`,\n *    so right now these tokens fall back to GMT timezones.\n *\n * 7. These patterns are not in the Unicode Technical Standard #35:\n *    - `i`: ISO day of week\n *    - `I`: ISO week of year\n *    - `R`: ISO week-numbering year\n *    - `t`: seconds timestamp\n *    - `T`: milliseconds timestamp\n *    - `o`: ordinal number modifier\n *    - `P`: long localized date\n *    - `p`: long localized time\n *\n * 8. `YY` and `YYYY` tokens represent week-numbering years but they are often confused with years.\n *    You should enable `options.useAdditionalWeekYearTokens` to use them. See: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md\n *\n * 9. `D` and `DD` tokens represent days of the year but they are often confused with days of the month.\n *    You should enable `options.useAdditionalDayOfYearTokens` to use them. See: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param date - The original date\n * @param format - The string of tokens\n * @param options - An object with options\n *\n * @returns The formatted date string\n *\n * @throws `date` must not be Invalid Date\n * @throws `options.locale` must contain `localize` property\n * @throws `options.locale` must contain `formatLong` property\n * @throws use `yyyy` instead of `YYYY` for formatting years using [format provided] to the input [input provided]; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md\n * @throws use `yy` instead of `YY` for formatting years using [format provided] to the input [input provided]; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md\n * @throws use `d` instead of `D` for formatting days of the month using [format provided] to the input [input provided]; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md\n * @throws use `dd` instead of `DD` for formatting days of the month using [format provided] to the input [input provided]; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md\n * @throws format string contains an unescaped latin alphabet character\n *\n * @example\n * // Represent 11 February 2014 in middle-endian format:\n * const result = format(new Date(2014, 1, 11), 'MM/dd/yyyy')\n * //=> '02/11/2014'\n *\n * @example\n * // Represent 2 July 2014 in Esperanto:\n * import { eoLocale } from 'date-fns/locale/eo'\n * const result = format(new Date(2014, 6, 2), \"do 'de' MMMM yyyy\", {\n *   locale: eoLocale\n * })\n * //=> '2-a de julio 2014'\n *\n * @example\n * // Escape string by single quote characters:\n * const result = format(new Date(2014, 6, 2, 15), \"h 'o''clock'\")\n * //=> \"3 o'clock\"\n */\nexport function format(date, formatStr, options) {\n  const defaultOptions = getDefaultOptions();\n  const locale = options?.locale ?? defaultOptions.locale ?? defaultLocale;\n\n  const firstWeekContainsDate =\n    options?.firstWeekContainsDate ??\n    options?.locale?.options?.firstWeekContainsDate ??\n    defaultOptions.firstWeekContainsDate ??\n    defaultOptions.locale?.options?.firstWeekContainsDate ??\n    1;\n\n  const weekStartsOn =\n    options?.weekStartsOn ??\n    options?.locale?.options?.weekStartsOn ??\n    defaultOptions.weekStartsOn ??\n    defaultOptions.locale?.options?.weekStartsOn ??\n    0;\n\n  const originalDate = toDate(date);\n\n  if (!isValid(originalDate)) {\n    throw new RangeError(\"Invalid time value\");\n  }\n\n  let parts = formatStr\n    .match(longFormattingTokensRegExp)\n    .map((substring) => {\n      const firstCharacter = substring[0];\n      if (firstCharacter === \"p\" || firstCharacter === \"P\") {\n        const longFormatter = longFormatters[firstCharacter];\n        return longFormatter(substring, locale.formatLong);\n      }\n      return substring;\n    })\n    .join(\"\")\n    .match(formattingTokensRegExp)\n    .map((substring) => {\n      // Replace two single quote characters with one single quote character\n      if (substring === \"''\") {\n        return { isToken: false, value: \"'\" };\n      }\n\n      const firstCharacter = substring[0];\n      if (firstCharacter === \"'\") {\n        return { isToken: false, value: cleanEscapedString(substring) };\n      }\n\n      if (formatters[firstCharacter]) {\n        return { isToken: true, value: substring };\n      }\n\n      if (firstCharacter.match(unescapedLatinCharacterRegExp)) {\n        throw new RangeError(\n          \"Format string contains an unescaped latin alphabet character `\" +\n            firstCharacter +\n            \"`\",\n        );\n      }\n\n      return { isToken: false, value: substring };\n    });\n\n  // invoke localize preprocessor (only for french locales at the moment)\n  if (locale.localize.preprocessor) {\n    parts = locale.localize.preprocessor(originalDate, parts);\n  }\n\n  const formatterOptions = {\n    firstWeekContainsDate,\n    weekStartsOn,\n    locale,\n  };\n\n  return parts\n    .map((part) => {\n      if (!part.isToken) return part.value;\n\n      const token = part.value;\n\n      if (\n        (!options?.useAdditionalWeekYearTokens &&\n          isProtectedWeekYearToken(token)) ||\n        (!options?.useAdditionalDayOfYearTokens &&\n          isProtectedDayOfYearToken(token))\n      ) {\n        warnOrThrowProtectedError(token, formatStr, String(date));\n      }\n\n      const formatter = formatters[token[0]];\n      return formatter(originalDate, token, locale.localize, formatterOptions);\n    })\n    .join(\"\");\n}\n\nfunction cleanEscapedString(input) {\n  const matched = input.match(escapedStringRegExp);\n\n  if (!matched) {\n    return input;\n  }\n\n  return matched[1].replace(doubleQuoteRegExp, \"'\");\n}\n\n// Fallback for modularized imports:\nexport default format;\n"], "names": ["defaultOptions", "format", "localize", "formatLong", "defaultLocale"], "mappings": "AAgCO,SAAS,OAAO,UAAU;AAC/B,QAAM,SAAS,OAAO,UAAU,SAAS,KAAK,QAAQ;AAGtD,MACE,oBAAoB,QACnB,OAAO,aAAa,YAAY,WAAW,iBAC5C;AAEA,WAAO,IAAI,SAAS,YAAY,CAAC,QAAQ;AAAA,EAC7C,WACI,OAAO,aAAa,YACpB,WAAW,qBACX,OAAO,aAAa,YACpB,WAAW,mBACX;AAEA,WAAO,IAAI,KAAK,QAAQ;AAAA,EAC5B,OAAS;AAEL,WAAO,oBAAI,KAAK,GAAG;AAAA,EACvB;AACA;ACxBO,SAAS,cAAc,MAAM,OAAO;AACzC,MAAI,gBAAgB,MAAM;AACxB,WAAO,IAAI,KAAK,YAAY,KAAK;AAAA,EACrC,OAAS;AACL,WAAO,IAAI,KAAK,KAAK;AAAA,EACzB;AACA;ACwCY,MAAC,qBAAqB;AAO3B,MAAM,oBAAoB;AAOrB,MAAC,uBAAuB;AAOxB,MAAC,qBAAqB;ACjGlC,IAAI,iBAAiB,CAAE;AAEhB,SAAS,oBAAoB;AAClC,SAAO;AACT;AC6BO,SAAS,YAAY,MAAM,SAAS;AJjC3C;AIkCE,QAAMA,kBAAiB,kBAAmB;AAC1C,QAAM,gBACJ,mCAAS,mBACT,8CAAS,WAAT,mBAAiB,YAAjB,mBAA0B,iBAC1BA,gBAAe,kBACf,WAAAA,gBAAe,WAAf,mBAAuB,YAAvB,mBAAgC,iBAChC;AAEF,QAAM,QAAQ,OAAO,IAAI;AACzB,QAAM,MAAM,MAAM,OAAQ;AAC1B,QAAM,QAAQ,MAAM,eAAe,IAAI,KAAK,MAAM;AAElD,QAAM,QAAQ,MAAM,QAAO,IAAK,IAAI;AACpC,QAAM,SAAS,GAAG,GAAG,GAAG,CAAC;AACzB,SAAO;AACT;ACzBO,SAAS,eAAe,MAAM;AACnC,SAAO,YAAY,MAAM,EAAE,cAAc,EAAC,CAAE;AAC9C;ACAO,SAAS,eAAe,MAAM;AACnC,QAAM,QAAQ,OAAO,IAAI;AACzB,QAAM,OAAO,MAAM,YAAa;AAEhC,QAAM,4BAA4B,cAAc,MAAM,CAAC;AACvD,4BAA0B,YAAY,OAAO,GAAG,GAAG,CAAC;AACpD,4BAA0B,SAAS,GAAG,GAAG,GAAG,CAAC;AAC7C,QAAM,kBAAkB,eAAe,yBAAyB;AAEhE,QAAM,4BAA4B,cAAc,MAAM,CAAC;AACvD,4BAA0B,YAAY,MAAM,GAAG,CAAC;AAChD,4BAA0B,SAAS,GAAG,GAAG,GAAG,CAAC;AAC7C,QAAM,kBAAkB,eAAe,yBAAyB;AAEhE,MAAI,MAAM,QAAO,KAAM,gBAAgB,QAAO,GAAI;AAChD,WAAO,OAAO;AAAA,EACf,WAAU,MAAM,QAAS,KAAI,gBAAgB,QAAO,GAAI;AACvD,WAAO;AAAA,EACX,OAAS;AACL,WAAO,OAAO;AAAA,EAClB;AACA;ACzBO,SAAS,WAAW,MAAM;AAC/B,QAAM,QAAQ,OAAO,IAAI;AACzB,QAAM,SAAS,GAAG,GAAG,GAAG,CAAC;AACzB,SAAO;AACT;ACbO,SAAS,gCAAgC,MAAM;AACpD,QAAM,QAAQ,OAAO,IAAI;AACzB,QAAM,UAAU,IAAI;AAAA,IAClB,KAAK;AAAA,MACH,MAAM,YAAa;AAAA,MACnB,MAAM,SAAU;AAAA,MAChB,MAAM,QAAS;AAAA,MACf,MAAM,SAAU;AAAA,MAChB,MAAM,WAAY;AAAA,MAClB,MAAM,WAAY;AAAA,MAClB,MAAM,gBAAiB;AAAA,IACxB;AAAA,EACF;AACD,UAAQ,eAAe,MAAM,aAAa;AAC1C,SAAO,CAAC,OAAO,CAAC;AAClB;ACQO,SAAS,yBAAyB,UAAU,WAAW;AAC5D,QAAM,iBAAiB,WAAW,QAAQ;AAC1C,QAAM,kBAAkB,WAAW,SAAS;AAE5C,QAAM,gBACJ,CAAC,iBAAiB,gCAAgC,cAAc;AAClE,QAAM,iBACJ,CAAC,kBAAkB,gCAAgC,eAAe;AAKpE,SAAO,KAAK,OAAO,gBAAgB,kBAAkB,iBAAiB;AACxE;ACtBO,SAAS,mBAAmB,MAAM;AACvC,QAAM,OAAO,eAAe,IAAI;AAChC,QAAM,kBAAkB,cAAc,MAAM,CAAC;AAC7C,kBAAgB,YAAY,MAAM,GAAG,CAAC;AACtC,kBAAgB,SAAS,GAAG,GAAG,GAAG,CAAC;AACnC,SAAO,eAAe,eAAe;AACvC;ACDO,SAAS,OAAO,OAAO;AAC5B,SACE,iBAAiB,QAChB,OAAO,UAAU,YAChB,OAAO,UAAU,SAAS,KAAK,KAAK,MAAM;AAEhD;ACFO,SAAS,QAAQ,MAAM;AAC5B,MAAI,CAAC,OAAO,IAAI,KAAK,OAAO,SAAS,UAAU;AAC7C,WAAO;AAAA,EACX;AACE,QAAM,QAAQ,OAAO,IAAI;AACzB,SAAO,CAAC,MAAM,OAAO,KAAK,CAAC;AAC7B;ACnBO,SAAS,YAAY,MAAM;AAChC,QAAM,YAAY,OAAO,IAAI;AAC7B,QAAM,QAAQ,cAAc,MAAM,CAAC;AACnC,QAAM,YAAY,UAAU,YAAW,GAAI,GAAG,CAAC;AAC/C,QAAM,SAAS,GAAG,GAAG,GAAG,CAAC;AACzB,SAAO;AACT;AC7BA,MAAM,uBAAuB;AAAA,EAC3B,kBAAkB;AAAA,IAChB,KAAK;AAAA,IACL,OAAO;AAAA,EACR;AAAA,EAED,UAAU;AAAA,IACR,KAAK;AAAA,IACL,OAAO;AAAA,EACR;AAAA,EAED,aAAa;AAAA,EAEb,kBAAkB;AAAA,IAChB,KAAK;AAAA,IACL,OAAO;AAAA,EACR;AAAA,EAED,UAAU;AAAA,IACR,KAAK;AAAA,IACL,OAAO;AAAA,EACR;AAAA,EAED,aAAa;AAAA,IACX,KAAK;AAAA,IACL,OAAO;AAAA,EACR;AAAA,EAED,QAAQ;AAAA,IACN,KAAK;AAAA,IACL,OAAO;AAAA,EACR;AAAA,EAED,OAAO;AAAA,IACL,KAAK;AAAA,IACL,OAAO;AAAA,EACR;AAAA,EAED,aAAa;AAAA,IACX,KAAK;AAAA,IACL,OAAO;AAAA,EACR;AAAA,EAED,QAAQ;AAAA,IACN,KAAK;AAAA,IACL,OAAO;AAAA,EACR;AAAA,EAED,cAAc;AAAA,IACZ,KAAK;AAAA,IACL,OAAO;AAAA,EACR;AAAA,EAED,SAAS;AAAA,IACP,KAAK;AAAA,IACL,OAAO;AAAA,EACR;AAAA,EAED,aAAa;AAAA,IACX,KAAK;AAAA,IACL,OAAO;AAAA,EACR;AAAA,EAED,QAAQ;AAAA,IACN,KAAK;AAAA,IACL,OAAO;AAAA,EACR;AAAA,EAED,YAAY;AAAA,IACV,KAAK;AAAA,IACL,OAAO;AAAA,EACR;AAAA,EAED,cAAc;AAAA,IACZ,KAAK;AAAA,IACL,OAAO;AAAA,EACR;AACH;AAEO,MAAM,iBAAiB,CAAC,OAAO,OAAO,YAAY;AACvD,MAAI;AAEJ,QAAM,aAAa,qBAAqB,KAAK;AAC7C,MAAI,OAAO,eAAe,UAAU;AAClC,aAAS;AAAA,EACb,WAAa,UAAU,GAAG;AACtB,aAAS,WAAW;AAAA,EACxB,OAAS;AACL,aAAS,WAAW,MAAM,QAAQ,aAAa,MAAM,UAAU;AAAA,EACnE;AAEE,MAAI,mCAAS,WAAW;AACtB,QAAI,QAAQ,cAAc,QAAQ,aAAa,GAAG;AAChD,aAAO,QAAQ;AAAA,IACrB,OAAW;AACL,aAAO,SAAS;AAAA,IACtB;AAAA,EACA;AAEE,SAAO;AACT;ACpGO,SAAS,kBAAkB,MAAM;AACtC,SAAO,CAAC,UAAU,OAAO;AAEvB,UAAM,QAAQ,QAAQ,QAAQ,OAAO,QAAQ,KAAK,IAAI,KAAK;AAC3D,UAAMC,UAAS,KAAK,QAAQ,KAAK,KAAK,KAAK,QAAQ,KAAK,YAAY;AACpE,WAAOA;AAAA,EACR;AACH;ACLA,MAAM,cAAc;AAAA,EAClB,MAAM;AAAA,EACN,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,OAAO;AACT;AAEA,MAAM,cAAc;AAAA,EAClB,MAAM;AAAA,EACN,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,OAAO;AACT;AAEA,MAAM,kBAAkB;AAAA,EACtB,MAAM;AAAA,EACN,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,OAAO;AACT;AAEO,MAAM,aAAa;AAAA,EACxB,MAAM,kBAAkB;AAAA,IACtB,SAAS;AAAA,IACT,cAAc;AAAA,EAClB,CAAG;AAAA,EAED,MAAM,kBAAkB;AAAA,IACtB,SAAS;AAAA,IACT,cAAc;AAAA,EAClB,CAAG;AAAA,EAED,UAAU,kBAAkB;AAAA,IAC1B,SAAS;AAAA,IACT,cAAc;AAAA,EAClB,CAAG;AACH;ACtCA,MAAM,uBAAuB;AAAA,EAC3B,UAAU;AAAA,EACV,WAAW;AAAA,EACX,OAAO;AAAA,EACP,UAAU;AAAA,EACV,UAAU;AAAA,EACV,OAAO;AACT;AAEO,MAAM,iBAAiB,CAAC,OAAO,OAAO,WAAW,aACtD,qBAAqB,KAAK;AC+BrB,SAAS,gBAAgB,MAAM;AACpC,SAAO,CAAC,OAAO,YAAY;AACzB,UAAM,WAAU,mCAAS,WAAU,OAAO,QAAQ,OAAO,IAAI;AAE7D,QAAI;AACJ,QAAI,YAAY,gBAAgB,KAAK,kBAAkB;AACrD,YAAM,eAAe,KAAK,0BAA0B,KAAK;AACzD,YAAM,SAAQ,mCAAS,SAAQ,OAAO,QAAQ,KAAK,IAAI;AAEvD,oBACE,KAAK,iBAAiB,KAAK,KAAK,KAAK,iBAAiB,YAAY;AAAA,IAC1E,OAAW;AACL,YAAM,eAAe,KAAK;AAC1B,YAAM,SAAQ,mCAAS,SAAQ,OAAO,QAAQ,KAAK,IAAI,KAAK;AAE5D,oBAAc,KAAK,OAAO,KAAK,KAAK,KAAK,OAAO,YAAY;AAAA,IAClE;AACI,UAAM,QAAQ,KAAK,mBAAmB,KAAK,iBAAiB,KAAK,IAAI;AAGrE,WAAO,YAAY,KAAK;AAAA,EACzB;AACH;AC7DA,MAAM,YAAY;AAAA,EAChB,QAAQ,CAAC,KAAK,GAAG;AAAA,EACjB,aAAa,CAAC,MAAM,IAAI;AAAA,EACxB,MAAM,CAAC,iBAAiB,aAAa;AACvC;AAEA,MAAM,gBAAgB;AAAA,EACpB,QAAQ,CAAC,KAAK,KAAK,KAAK,GAAG;AAAA,EAC3B,aAAa,CAAC,MAAM,MAAM,MAAM,IAAI;AAAA,EACpC,MAAM,CAAC,eAAe,eAAe,eAAe,aAAa;AACnE;AAMA,MAAM,cAAc;AAAA,EAClB,QAAQ,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG;AAAA,EACnE,aAAa;AAAA,IACX;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACD;AAAA,EAED,MAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACD;AACH;AAEA,MAAM,YAAY;AAAA,EAChB,QAAQ,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG;AAAA,EAC1C,OAAO,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI;AAAA,EAChD,aAAa,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,KAAK;AAAA,EAC7D,MAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACD;AACH;AAEA,MAAM,kBAAkB;AAAA,EACtB,QAAQ;AAAA,IACN,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACR;AAAA,EACD,aAAa;AAAA,IACX,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACR;AAAA,EACD,MAAM;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACR;AACH;AAEA,MAAM,4BAA4B;AAAA,EAChC,QAAQ;AAAA,IACN,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACR;AAAA,EACD,aAAa;AAAA,IACX,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACR;AAAA,EACD,MAAM;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACR;AACH;AAEA,MAAM,gBAAgB,CAAC,aAAa,aAAa;AAC/C,QAAM,SAAS,OAAO,WAAW;AASjC,QAAM,SAAS,SAAS;AACxB,MAAI,SAAS,MAAM,SAAS,IAAI;AAC9B,YAAQ,SAAS,IAAE;AAAA,MACjB,KAAK;AACH,eAAO,SAAS;AAAA,MAClB,KAAK;AACH,eAAO,SAAS;AAAA,MAClB,KAAK;AACH,eAAO,SAAS;AAAA,IACxB;AAAA,EACA;AACE,SAAO,SAAS;AAClB;AAEO,MAAM,WAAW;AAAA,EACtB;AAAA,EAEA,KAAK,gBAAgB;AAAA,IACnB,QAAQ;AAAA,IACR,cAAc;AAAA,EAClB,CAAG;AAAA,EAED,SAAS,gBAAgB;AAAA,IACvB,QAAQ;AAAA,IACR,cAAc;AAAA,IACd,kBAAkB,CAAC,YAAY,UAAU;AAAA,EAC7C,CAAG;AAAA,EAED,OAAO,gBAAgB;AAAA,IACrB,QAAQ;AAAA,IACR,cAAc;AAAA,EAClB,CAAG;AAAA,EAED,KAAK,gBAAgB;AAAA,IACnB,QAAQ;AAAA,IACR,cAAc;AAAA,EAClB,CAAG;AAAA,EAED,WAAW,gBAAgB;AAAA,IACzB,QAAQ;AAAA,IACR,cAAc;AAAA,IACd,kBAAkB;AAAA,IAClB,wBAAwB;AAAA,EAC5B,CAAG;AACH;AC1LO,SAAS,aAAa,MAAM;AACjC,SAAO,CAAC,QAAQ,UAAU,OAAO;AAC/B,UAAM,QAAQ,QAAQ;AAEtB,UAAM,eACH,SAAS,KAAK,cAAc,KAAK,KAClC,KAAK,cAAc,KAAK,iBAAiB;AAC3C,UAAM,cAAc,OAAO,MAAM,YAAY;AAE7C,QAAI,CAAC,aAAa;AAChB,aAAO;AAAA,IACb;AACI,UAAM,gBAAgB,YAAY,CAAC;AAEnC,UAAM,gBACH,SAAS,KAAK,cAAc,KAAK,KAClC,KAAK,cAAc,KAAK,iBAAiB;AAE3C,UAAM,MAAM,MAAM,QAAQ,aAAa,IACnC,UAAU,eAAe,CAAC,YAAY,QAAQ,KAAK,aAAa,CAAC;AAAA;AAAA,MAEjE,QAAQ,eAAe,CAAC,YAAY,QAAQ,KAAK,aAAa,CAAC;AAAA;AAEnE,QAAI;AAEJ,YAAQ,KAAK,gBAAgB,KAAK,cAAc,GAAG,IAAI;AACvD,YAAQ,QAAQ;AAAA;AAAA,MAEZ,QAAQ,cAAc,KAAK;AAAA,QAC3B;AAEJ,UAAM,OAAO,OAAO,MAAM,cAAc,MAAM;AAE9C,WAAO,EAAE,OAAO,KAAM;AAAA,EACvB;AACH;AAEA,SAAS,QAAQ,QAAQ,WAAW;AAClC,aAAW,OAAO,QAAQ;AACxB,QACE,OAAO,UAAU,eAAe,KAAK,QAAQ,GAAG,KAChD,UAAU,OAAO,GAAG,CAAC,GACrB;AACA,aAAO;AAAA,IACb;AAAA,EACA;AACE,SAAO;AACT;AAEA,SAAS,UAAU,OAAO,WAAW;AACnC,WAAS,MAAM,GAAG,MAAM,MAAM,QAAQ,OAAO;AAC3C,QAAI,UAAU,MAAM,GAAG,CAAC,GAAG;AACzB,aAAO;AAAA,IACb;AAAA,EACA;AACE,SAAO;AACT;ACxDO,SAAS,oBAAoB,MAAM;AACxC,SAAO,CAAC,QAAQ,UAAU,OAAO;AAC/B,UAAM,cAAc,OAAO,MAAM,KAAK,YAAY;AAClD,QAAI,CAAC,YAAa,QAAO;AACzB,UAAM,gBAAgB,YAAY,CAAC;AAEnC,UAAM,cAAc,OAAO,MAAM,KAAK,YAAY;AAClD,QAAI,CAAC,YAAa,QAAO;AACzB,QAAI,QAAQ,KAAK,gBACb,KAAK,cAAc,YAAY,CAAC,CAAC,IACjC,YAAY,CAAC;AAGjB,YAAQ,QAAQ,gBAAgB,QAAQ,cAAc,KAAK,IAAI;AAE/D,UAAM,OAAO,OAAO,MAAM,cAAc,MAAM;AAE9C,WAAO,EAAE,OAAO,KAAM;AAAA,EACvB;AACH;AChBA,MAAM,4BAA4B;AAClC,MAAM,4BAA4B;AAElC,MAAM,mBAAmB;AAAA,EACvB,QAAQ;AAAA,EACR,aAAa;AAAA,EACb,MAAM;AACR;AACA,MAAM,mBAAmB;AAAA,EACvB,KAAK,CAAC,OAAO,SAAS;AACxB;AAEA,MAAM,uBAAuB;AAAA,EAC3B,QAAQ;AAAA,EACR,aAAa;AAAA,EACb,MAAM;AACR;AACA,MAAM,uBAAuB;AAAA,EAC3B,KAAK,CAAC,MAAM,MAAM,MAAM,IAAI;AAC9B;AAEA,MAAM,qBAAqB;AAAA,EACzB,QAAQ;AAAA,EACR,aAAa;AAAA,EACb,MAAM;AACR;AACA,MAAM,qBAAqB;AAAA,EACzB,QAAQ;AAAA,IACN;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACD;AAAA,EAED,KAAK;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACD;AACH;AAEA,MAAM,mBAAmB;AAAA,EACvB,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,aAAa;AAAA,EACb,MAAM;AACR;AACA,MAAM,mBAAmB;AAAA,EACvB,QAAQ,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,KAAK;AAAA,EACxD,KAAK,CAAC,QAAQ,OAAO,QAAQ,OAAO,QAAQ,OAAO,MAAM;AAC3D;AAEA,MAAM,yBAAyB;AAAA,EAC7B,QAAQ;AAAA,EACR,KAAK;AACP;AACA,MAAM,yBAAyB;AAAA,EAC7B,KAAK;AAAA,IACH,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACR;AACH;AAEO,MAAM,QAAQ;AAAA,EACnB,eAAe,oBAAoB;AAAA,IACjC,cAAc;AAAA,IACd,cAAc;AAAA,IACd,eAAe,CAAC,UAAU,SAAS,OAAO,EAAE;AAAA,EAChD,CAAG;AAAA,EAED,KAAK,aAAa;AAAA,IAChB,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,eAAe;AAAA,IACf,mBAAmB;AAAA,EACvB,CAAG;AAAA,EAED,SAAS,aAAa;AAAA,IACpB,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,eAAe,CAAC,UAAU,QAAQ;AAAA,EACtC,CAAG;AAAA,EAED,OAAO,aAAa;AAAA,IAClB,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,eAAe;AAAA,IACf,mBAAmB;AAAA,EACvB,CAAG;AAAA,EAED,KAAK,aAAa;AAAA,IAChB,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,eAAe;AAAA,IACf,mBAAmB;AAAA,EACvB,CAAG;AAAA,EAED,WAAW,aAAa;AAAA,IACtB,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,eAAe;AAAA,IACf,mBAAmB;AAAA,EACvB,CAAG;AACH;ACrHY,MAAC,OAAO;AAAA,EAClB,MAAM;AAAA,EACN;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,SAAS;AAAA,IACP,cAAc;AAAA,IACd,uBAAuB;AAAA,EACxB;AACH;ACFO,SAAS,aAAa,MAAM;AACjC,QAAM,QAAQ,OAAO,IAAI;AACzB,QAAM,OAAO,yBAAyB,OAAO,YAAY,KAAK,CAAC;AAC/D,QAAM,YAAY,OAAO;AACzB,SAAO;AACT;ACFO,SAAS,WAAW,MAAM;AAC/B,QAAM,QAAQ,OAAO,IAAI;AACzB,QAAM,OAAO,CAAC,eAAe,KAAK,IAAI,CAAC,mBAAmB,KAAK;AAK/D,SAAO,KAAK,MAAM,OAAO,kBAAkB,IAAI;AACjD;ACWO,SAAS,YAAY,MAAM,SAAS;A1B7C3C;A0B8CE,QAAM,QAAQ,OAAO,IAAI;AACzB,QAAM,OAAO,MAAM,YAAa;AAEhC,QAAMD,kBAAiB,kBAAmB;AAC1C,QAAM,yBACJ,mCAAS,4BACT,8CAAS,WAAT,mBAAiB,YAAjB,mBAA0B,0BAC1BA,gBAAe,2BACf,WAAAA,gBAAe,WAAf,mBAAuB,YAAvB,mBAAgC,0BAChC;AAEF,QAAM,sBAAsB,cAAc,MAAM,CAAC;AACjD,sBAAoB,YAAY,OAAO,GAAG,GAAG,qBAAqB;AAClE,sBAAoB,SAAS,GAAG,GAAG,GAAG,CAAC;AACvC,QAAM,kBAAkB,YAAY,qBAAqB,OAAO;AAEhE,QAAM,sBAAsB,cAAc,MAAM,CAAC;AACjD,sBAAoB,YAAY,MAAM,GAAG,qBAAqB;AAC9D,sBAAoB,SAAS,GAAG,GAAG,GAAG,CAAC;AACvC,QAAM,kBAAkB,YAAY,qBAAqB,OAAO;AAEhE,MAAI,MAAM,QAAO,KAAM,gBAAgB,QAAO,GAAI;AAChD,WAAO,OAAO;AAAA,EACf,WAAU,MAAM,QAAS,KAAI,gBAAgB,QAAO,GAAI;AACvD,WAAO;AAAA,EACX,OAAS;AACL,WAAO,OAAO;AAAA,EAClB;AACA;AC7BO,SAAS,gBAAgB,MAAM,SAAS;A3B7C/C;A2B8CE,QAAMA,kBAAiB,kBAAmB;AAC1C,QAAM,yBACJ,mCAAS,4BACT,8CAAS,WAAT,mBAAiB,YAAjB,mBAA0B,0BAC1BA,gBAAe,2BACf,WAAAA,gBAAe,WAAf,mBAAuB,YAAvB,mBAAgC,0BAChC;AAEF,QAAM,OAAO,YAAY,MAAM,OAAO;AACtC,QAAM,YAAY,cAAc,MAAM,CAAC;AACvC,YAAU,YAAY,MAAM,GAAG,qBAAqB;AACpD,YAAU,SAAS,GAAG,GAAG,GAAG,CAAC;AAC7B,QAAM,QAAQ,YAAY,WAAW,OAAO;AAC5C,SAAO;AACT;ACdO,SAAS,QAAQ,MAAM,SAAS;AACrC,QAAM,QAAQ,OAAO,IAAI;AACzB,QAAM,OAAO,CAAC,YAAY,OAAO,OAAO,IAAI,CAAC,gBAAgB,OAAO,OAAO;AAK3E,SAAO,KAAK,MAAM,OAAO,kBAAkB,IAAI;AACjD;ACtDO,SAAS,gBAAgB,QAAQ,cAAc;AACpD,QAAM,OAAO,SAAS,IAAI,MAAM;AAChC,QAAM,SAAS,KAAK,IAAI,MAAM,EAAE,WAAW,SAAS,cAAc,GAAG;AACrE,SAAO,OAAO;AAChB;ACWO,MAAM,kBAAkB;AAAA;AAAA,EAE7B,EAAE,MAAM,OAAO;AAUb,UAAM,aAAa,KAAK,YAAa;AAErC,UAAM,OAAO,aAAa,IAAI,aAAa,IAAI;AAC/C,WAAO,gBAAgB,UAAU,OAAO,OAAO,MAAM,MAAM,MAAM,MAAM;AAAA,EACxE;AAAA;AAAA,EAGD,EAAE,MAAM,OAAO;AACb,UAAM,QAAQ,KAAK,SAAU;AAC7B,WAAO,UAAU,MAAM,OAAO,QAAQ,CAAC,IAAI,gBAAgB,QAAQ,GAAG,CAAC;AAAA,EACxE;AAAA;AAAA,EAGD,EAAE,MAAM,OAAO;AACb,WAAO,gBAAgB,KAAK,QAAO,GAAI,MAAM,MAAM;AAAA,EACpD;AAAA;AAAA,EAGD,EAAE,MAAM,OAAO;AACb,UAAM,qBAAqB,KAAK,SAAQ,IAAK,MAAM,IAAI,OAAO;AAE9D,YAAQ,OAAK;AAAA,MACX,KAAK;AAAA,MACL,KAAK;AACH,eAAO,mBAAmB,YAAa;AAAA,MACzC,KAAK;AACH,eAAO;AAAA,MACT,KAAK;AACH,eAAO,mBAAmB,CAAC;AAAA,MAC7B,KAAK;AAAA,MACL;AACE,eAAO,uBAAuB,OAAO,SAAS;AAAA,IACtD;AAAA,EACG;AAAA;AAAA,EAGD,EAAE,MAAM,OAAO;AACb,WAAO,gBAAgB,KAAK,SAAQ,IAAK,MAAM,IAAI,MAAM,MAAM;AAAA,EAChE;AAAA;AAAA,EAGD,EAAE,MAAM,OAAO;AACb,WAAO,gBAAgB,KAAK,SAAQ,GAAI,MAAM,MAAM;AAAA,EACrD;AAAA;AAAA,EAGD,EAAE,MAAM,OAAO;AACb,WAAO,gBAAgB,KAAK,WAAU,GAAI,MAAM,MAAM;AAAA,EACvD;AAAA;AAAA,EAGD,EAAE,MAAM,OAAO;AACb,WAAO,gBAAgB,KAAK,WAAU,GAAI,MAAM,MAAM;AAAA,EACvD;AAAA;AAAA,EAGD,EAAE,MAAM,OAAO;AACb,UAAM,iBAAiB,MAAM;AAC7B,UAAM,eAAe,KAAK,gBAAiB;AAC3C,UAAM,oBAAoB,KAAK;AAAA,MAC7B,eAAe,KAAK,IAAI,IAAI,iBAAiB,CAAC;AAAA,IAC/C;AACD,WAAO,gBAAgB,mBAAmB,MAAM,MAAM;AAAA,EACvD;AACH;ACnFA,MAAM,gBAAgB;AAAA,EAGpB,UAAU;AAAA,EACV,MAAM;AAAA,EACN,SAAS;AAAA,EACT,WAAW;AAAA,EACX,SAAS;AAAA,EACT,OAAO;AACT;AAgDO,MAAM,aAAa;AAAA;AAAA,EAExB,GAAG,SAAU,MAAM,OAAOE,WAAU;AAClC,UAAM,MAAM,KAAK,YAAa,IAAG,IAAI,IAAI;AACzC,YAAQ,OAAK;AAAA,MAEX,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AACH,eAAOA,UAAS,IAAI,KAAK,EAAE,OAAO,cAAa,CAAE;AAAA,MAEnD,KAAK;AACH,eAAOA,UAAS,IAAI,KAAK,EAAE,OAAO,SAAQ,CAAE;AAAA,MAE9C,KAAK;AAAA,MACL;AACE,eAAOA,UAAS,IAAI,KAAK,EAAE,OAAO,OAAM,CAAE;AAAA,IAClD;AAAA,EACG;AAAA;AAAA,EAGD,GAAG,SAAU,MAAM,OAAOA,WAAU;AAElC,QAAI,UAAU,MAAM;AAClB,YAAM,aAAa,KAAK,YAAa;AAErC,YAAM,OAAO,aAAa,IAAI,aAAa,IAAI;AAC/C,aAAOA,UAAS,cAAc,MAAM,EAAE,MAAM,OAAM,CAAE;AAAA,IAC1D;AAEI,WAAO,gBAAgB,EAAE,MAAM,KAAK;AAAA,EACrC;AAAA;AAAA,EAGD,GAAG,SAAU,MAAM,OAAOA,WAAU,SAAS;AAC3C,UAAM,iBAAiB,YAAY,MAAM,OAAO;AAEhD,UAAM,WAAW,iBAAiB,IAAI,iBAAiB,IAAI;AAG3D,QAAI,UAAU,MAAM;AAClB,YAAM,eAAe,WAAW;AAChC,aAAO,gBAAgB,cAAc,CAAC;AAAA,IAC5C;AAGI,QAAI,UAAU,MAAM;AAClB,aAAOA,UAAS,cAAc,UAAU,EAAE,MAAM,OAAM,CAAE;AAAA,IAC9D;AAGI,WAAO,gBAAgB,UAAU,MAAM,MAAM;AAAA,EAC9C;AAAA;AAAA,EAGD,GAAG,SAAU,MAAM,OAAO;AACxB,UAAM,cAAc,eAAe,IAAI;AAGvC,WAAO,gBAAgB,aAAa,MAAM,MAAM;AAAA,EACjD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWD,GAAG,SAAU,MAAM,OAAO;AACxB,UAAM,OAAO,KAAK,YAAa;AAC/B,WAAO,gBAAgB,MAAM,MAAM,MAAM;AAAA,EAC1C;AAAA;AAAA,EAGD,GAAG,SAAU,MAAM,OAAOA,WAAU;AAClC,UAAM,UAAU,KAAK,MAAM,KAAK,SAAU,IAAG,KAAK,CAAC;AACnD,YAAQ,OAAK;AAAA,MAEX,KAAK;AACH,eAAO,OAAO,OAAO;AAAA,MAEvB,KAAK;AACH,eAAO,gBAAgB,SAAS,CAAC;AAAA,MAEnC,KAAK;AACH,eAAOA,UAAS,cAAc,SAAS,EAAE,MAAM,UAAS,CAAE;AAAA,MAE5D,KAAK;AACH,eAAOA,UAAS,QAAQ,SAAS;AAAA,UAC/B,OAAO;AAAA,UACP,SAAS;AAAA,QACnB,CAAS;AAAA,MAEH,KAAK;AACH,eAAOA,UAAS,QAAQ,SAAS;AAAA,UAC/B,OAAO;AAAA,UACP,SAAS;AAAA,QACnB,CAAS;AAAA,MAEH,KAAK;AAAA,MACL;AACE,eAAOA,UAAS,QAAQ,SAAS;AAAA,UAC/B,OAAO;AAAA,UACP,SAAS;AAAA,QACnB,CAAS;AAAA,IACT;AAAA,EACG;AAAA;AAAA,EAGD,GAAG,SAAU,MAAM,OAAOA,WAAU;AAClC,UAAM,UAAU,KAAK,MAAM,KAAK,SAAU,IAAG,KAAK,CAAC;AACnD,YAAQ,OAAK;AAAA,MAEX,KAAK;AACH,eAAO,OAAO,OAAO;AAAA,MAEvB,KAAK;AACH,eAAO,gBAAgB,SAAS,CAAC;AAAA,MAEnC,KAAK;AACH,eAAOA,UAAS,cAAc,SAAS,EAAE,MAAM,UAAS,CAAE;AAAA,MAE5D,KAAK;AACH,eAAOA,UAAS,QAAQ,SAAS;AAAA,UAC/B,OAAO;AAAA,UACP,SAAS;AAAA,QACnB,CAAS;AAAA,MAEH,KAAK;AACH,eAAOA,UAAS,QAAQ,SAAS;AAAA,UAC/B,OAAO;AAAA,UACP,SAAS;AAAA,QACnB,CAAS;AAAA,MAEH,KAAK;AAAA,MACL;AACE,eAAOA,UAAS,QAAQ,SAAS;AAAA,UAC/B,OAAO;AAAA,UACP,SAAS;AAAA,QACnB,CAAS;AAAA,IACT;AAAA,EACG;AAAA;AAAA,EAGD,GAAG,SAAU,MAAM,OAAOA,WAAU;AAClC,UAAM,QAAQ,KAAK,SAAU;AAC7B,YAAQ,OAAK;AAAA,MACX,KAAK;AAAA,MACL,KAAK;AACH,eAAO,gBAAgB,EAAE,MAAM,KAAK;AAAA,MAEtC,KAAK;AACH,eAAOA,UAAS,cAAc,QAAQ,GAAG,EAAE,MAAM,SAAS;AAAA,MAE5D,KAAK;AACH,eAAOA,UAAS,MAAM,OAAO;AAAA,UAC3B,OAAO;AAAA,UACP,SAAS;AAAA,QACnB,CAAS;AAAA,MAEH,KAAK;AACH,eAAOA,UAAS,MAAM,OAAO;AAAA,UAC3B,OAAO;AAAA,UACP,SAAS;AAAA,QACnB,CAAS;AAAA,MAEH,KAAK;AAAA,MACL;AACE,eAAOA,UAAS,MAAM,OAAO,EAAE,OAAO,QAAQ,SAAS,cAAc;AAAA,IAC7E;AAAA,EACG;AAAA;AAAA,EAGD,GAAG,SAAU,MAAM,OAAOA,WAAU;AAClC,UAAM,QAAQ,KAAK,SAAU;AAC7B,YAAQ,OAAK;AAAA,MAEX,KAAK;AACH,eAAO,OAAO,QAAQ,CAAC;AAAA,MAEzB,KAAK;AACH,eAAO,gBAAgB,QAAQ,GAAG,CAAC;AAAA,MAErC,KAAK;AACH,eAAOA,UAAS,cAAc,QAAQ,GAAG,EAAE,MAAM,SAAS;AAAA,MAE5D,KAAK;AACH,eAAOA,UAAS,MAAM,OAAO;AAAA,UAC3B,OAAO;AAAA,UACP,SAAS;AAAA,QACnB,CAAS;AAAA,MAEH,KAAK;AACH,eAAOA,UAAS,MAAM,OAAO;AAAA,UAC3B,OAAO;AAAA,UACP,SAAS;AAAA,QACnB,CAAS;AAAA,MAEH,KAAK;AAAA,MACL;AACE,eAAOA,UAAS,MAAM,OAAO,EAAE,OAAO,QAAQ,SAAS,cAAc;AAAA,IAC7E;AAAA,EACG;AAAA;AAAA,EAGD,GAAG,SAAU,MAAM,OAAOA,WAAU,SAAS;AAC3C,UAAM,OAAO,QAAQ,MAAM,OAAO;AAElC,QAAI,UAAU,MAAM;AAClB,aAAOA,UAAS,cAAc,MAAM,EAAE,MAAM,OAAM,CAAE;AAAA,IAC1D;AAEI,WAAO,gBAAgB,MAAM,MAAM,MAAM;AAAA,EAC1C;AAAA;AAAA,EAGD,GAAG,SAAU,MAAM,OAAOA,WAAU;AAClC,UAAM,UAAU,WAAW,IAAI;AAE/B,QAAI,UAAU,MAAM;AAClB,aAAOA,UAAS,cAAc,SAAS,EAAE,MAAM,OAAM,CAAE;AAAA,IAC7D;AAEI,WAAO,gBAAgB,SAAS,MAAM,MAAM;AAAA,EAC7C;AAAA;AAAA,EAGD,GAAG,SAAU,MAAM,OAAOA,WAAU;AAClC,QAAI,UAAU,MAAM;AAClB,aAAOA,UAAS,cAAc,KAAK,QAAO,GAAI,EAAE,MAAM,QAAQ;AAAA,IACpE;AAEI,WAAO,gBAAgB,EAAE,MAAM,KAAK;AAAA,EACrC;AAAA;AAAA,EAGD,GAAG,SAAU,MAAM,OAAOA,WAAU;AAClC,UAAM,YAAY,aAAa,IAAI;AAEnC,QAAI,UAAU,MAAM;AAClB,aAAOA,UAAS,cAAc,WAAW,EAAE,MAAM,YAAW,CAAE;AAAA,IACpE;AAEI,WAAO,gBAAgB,WAAW,MAAM,MAAM;AAAA,EAC/C;AAAA;AAAA,EAGD,GAAG,SAAU,MAAM,OAAOA,WAAU;AAClC,UAAM,YAAY,KAAK,OAAQ;AAC/B,YAAQ,OAAK;AAAA,MAEX,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AACH,eAAOA,UAAS,IAAI,WAAW;AAAA,UAC7B,OAAO;AAAA,UACP,SAAS;AAAA,QACnB,CAAS;AAAA,MAEH,KAAK;AACH,eAAOA,UAAS,IAAI,WAAW;AAAA,UAC7B,OAAO;AAAA,UACP,SAAS;AAAA,QACnB,CAAS;AAAA,MAEH,KAAK;AACH,eAAOA,UAAS,IAAI,WAAW;AAAA,UAC7B,OAAO;AAAA,UACP,SAAS;AAAA,QACnB,CAAS;AAAA,MAEH,KAAK;AAAA,MACL;AACE,eAAOA,UAAS,IAAI,WAAW;AAAA,UAC7B,OAAO;AAAA,UACP,SAAS;AAAA,QACnB,CAAS;AAAA,IACT;AAAA,EACG;AAAA;AAAA,EAGD,GAAG,SAAU,MAAM,OAAOA,WAAU,SAAS;AAC3C,UAAM,YAAY,KAAK,OAAQ;AAC/B,UAAM,kBAAkB,YAAY,QAAQ,eAAe,KAAK,KAAK;AACrE,YAAQ,OAAK;AAAA,MAEX,KAAK;AACH,eAAO,OAAO,cAAc;AAAA,MAE9B,KAAK;AACH,eAAO,gBAAgB,gBAAgB,CAAC;AAAA,MAE1C,KAAK;AACH,eAAOA,UAAS,cAAc,gBAAgB,EAAE,MAAM,MAAK,CAAE;AAAA,MAC/D,KAAK;AACH,eAAOA,UAAS,IAAI,WAAW;AAAA,UAC7B,OAAO;AAAA,UACP,SAAS;AAAA,QACnB,CAAS;AAAA,MAEH,KAAK;AACH,eAAOA,UAAS,IAAI,WAAW;AAAA,UAC7B,OAAO;AAAA,UACP,SAAS;AAAA,QACnB,CAAS;AAAA,MAEH,KAAK;AACH,eAAOA,UAAS,IAAI,WAAW;AAAA,UAC7B,OAAO;AAAA,UACP,SAAS;AAAA,QACnB,CAAS;AAAA,MAEH,KAAK;AAAA,MACL;AACE,eAAOA,UAAS,IAAI,WAAW;AAAA,UAC7B,OAAO;AAAA,UACP,SAAS;AAAA,QACnB,CAAS;AAAA,IACT;AAAA,EACG;AAAA;AAAA,EAGD,GAAG,SAAU,MAAM,OAAOA,WAAU,SAAS;AAC3C,UAAM,YAAY,KAAK,OAAQ;AAC/B,UAAM,kBAAkB,YAAY,QAAQ,eAAe,KAAK,KAAK;AACrE,YAAQ,OAAK;AAAA,MAEX,KAAK;AACH,eAAO,OAAO,cAAc;AAAA,MAE9B,KAAK;AACH,eAAO,gBAAgB,gBAAgB,MAAM,MAAM;AAAA,MAErD,KAAK;AACH,eAAOA,UAAS,cAAc,gBAAgB,EAAE,MAAM,MAAK,CAAE;AAAA,MAC/D,KAAK;AACH,eAAOA,UAAS,IAAI,WAAW;AAAA,UAC7B,OAAO;AAAA,UACP,SAAS;AAAA,QACnB,CAAS;AAAA,MAEH,KAAK;AACH,eAAOA,UAAS,IAAI,WAAW;AAAA,UAC7B,OAAO;AAAA,UACP,SAAS;AAAA,QACnB,CAAS;AAAA,MAEH,KAAK;AACH,eAAOA,UAAS,IAAI,WAAW;AAAA,UAC7B,OAAO;AAAA,UACP,SAAS;AAAA,QACnB,CAAS;AAAA,MAEH,KAAK;AAAA,MACL;AACE,eAAOA,UAAS,IAAI,WAAW;AAAA,UAC7B,OAAO;AAAA,UACP,SAAS;AAAA,QACnB,CAAS;AAAA,IACT;AAAA,EACG;AAAA;AAAA,EAGD,GAAG,SAAU,MAAM,OAAOA,WAAU;AAClC,UAAM,YAAY,KAAK,OAAQ;AAC/B,UAAM,eAAe,cAAc,IAAI,IAAI;AAC3C,YAAQ,OAAK;AAAA,MAEX,KAAK;AACH,eAAO,OAAO,YAAY;AAAA,MAE5B,KAAK;AACH,eAAO,gBAAgB,cAAc,MAAM,MAAM;AAAA,MAEnD,KAAK;AACH,eAAOA,UAAS,cAAc,cAAc,EAAE,MAAM,MAAK,CAAE;AAAA,MAE7D,KAAK;AACH,eAAOA,UAAS,IAAI,WAAW;AAAA,UAC7B,OAAO;AAAA,UACP,SAAS;AAAA,QACnB,CAAS;AAAA,MAEH,KAAK;AACH,eAAOA,UAAS,IAAI,WAAW;AAAA,UAC7B,OAAO;AAAA,UACP,SAAS;AAAA,QACnB,CAAS;AAAA,MAEH,KAAK;AACH,eAAOA,UAAS,IAAI,WAAW;AAAA,UAC7B,OAAO;AAAA,UACP,SAAS;AAAA,QACnB,CAAS;AAAA,MAEH,KAAK;AAAA,MACL;AACE,eAAOA,UAAS,IAAI,WAAW;AAAA,UAC7B,OAAO;AAAA,UACP,SAAS;AAAA,QACnB,CAAS;AAAA,IACT;AAAA,EACG;AAAA;AAAA,EAGD,GAAG,SAAU,MAAM,OAAOA,WAAU;AAClC,UAAM,QAAQ,KAAK,SAAU;AAC7B,UAAM,qBAAqB,QAAQ,MAAM,IAAI,OAAO;AAEpD,YAAQ,OAAK;AAAA,MACX,KAAK;AAAA,MACL,KAAK;AACH,eAAOA,UAAS,UAAU,oBAAoB;AAAA,UAC5C,OAAO;AAAA,UACP,SAAS;AAAA,QACnB,CAAS;AAAA,MACH,KAAK;AACH,eAAOA,UACJ,UAAU,oBAAoB;AAAA,UAC7B,OAAO;AAAA,UACP,SAAS;AAAA,QACV,CAAA,EACA,YAAa;AAAA,MAClB,KAAK;AACH,eAAOA,UAAS,UAAU,oBAAoB;AAAA,UAC5C,OAAO;AAAA,UACP,SAAS;AAAA,QACnB,CAAS;AAAA,MACH,KAAK;AAAA,MACL;AACE,eAAOA,UAAS,UAAU,oBAAoB;AAAA,UAC5C,OAAO;AAAA,UACP,SAAS;AAAA,QACnB,CAAS;AAAA,IACT;AAAA,EACG;AAAA;AAAA,EAGD,GAAG,SAAU,MAAM,OAAOA,WAAU;AAClC,UAAM,QAAQ,KAAK,SAAU;AAC7B,QAAI;AACJ,QAAI,UAAU,IAAI;AAChB,2BAAqB,cAAc;AAAA,IACzC,WAAe,UAAU,GAAG;AACtB,2BAAqB,cAAc;AAAA,IACzC,OAAW;AACL,2BAAqB,QAAQ,MAAM,IAAI,OAAO;AAAA,IACpD;AAEI,YAAQ,OAAK;AAAA,MACX,KAAK;AAAA,MACL,KAAK;AACH,eAAOA,UAAS,UAAU,oBAAoB;AAAA,UAC5C,OAAO;AAAA,UACP,SAAS;AAAA,QACnB,CAAS;AAAA,MACH,KAAK;AACH,eAAOA,UACJ,UAAU,oBAAoB;AAAA,UAC7B,OAAO;AAAA,UACP,SAAS;AAAA,QACV,CAAA,EACA,YAAa;AAAA,MAClB,KAAK;AACH,eAAOA,UAAS,UAAU,oBAAoB;AAAA,UAC5C,OAAO;AAAA,UACP,SAAS;AAAA,QACnB,CAAS;AAAA,MACH,KAAK;AAAA,MACL;AACE,eAAOA,UAAS,UAAU,oBAAoB;AAAA,UAC5C,OAAO;AAAA,UACP,SAAS;AAAA,QACnB,CAAS;AAAA,IACT;AAAA,EACG;AAAA;AAAA,EAGD,GAAG,SAAU,MAAM,OAAOA,WAAU;AAClC,UAAM,QAAQ,KAAK,SAAU;AAC7B,QAAI;AACJ,QAAI,SAAS,IAAI;AACf,2BAAqB,cAAc;AAAA,IACzC,WAAe,SAAS,IAAI;AACtB,2BAAqB,cAAc;AAAA,IACzC,WAAe,SAAS,GAAG;AACrB,2BAAqB,cAAc;AAAA,IACzC,OAAW;AACL,2BAAqB,cAAc;AAAA,IACzC;AAEI,YAAQ,OAAK;AAAA,MACX,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AACH,eAAOA,UAAS,UAAU,oBAAoB;AAAA,UAC5C,OAAO;AAAA,UACP,SAAS;AAAA,QACnB,CAAS;AAAA,MACH,KAAK;AACH,eAAOA,UAAS,UAAU,oBAAoB;AAAA,UAC5C,OAAO;AAAA,UACP,SAAS;AAAA,QACnB,CAAS;AAAA,MACH,KAAK;AAAA,MACL;AACE,eAAOA,UAAS,UAAU,oBAAoB;AAAA,UAC5C,OAAO;AAAA,UACP,SAAS;AAAA,QACnB,CAAS;AAAA,IACT;AAAA,EACG;AAAA;AAAA,EAGD,GAAG,SAAU,MAAM,OAAOA,WAAU;AAClC,QAAI,UAAU,MAAM;AAClB,UAAI,QAAQ,KAAK,SAAQ,IAAK;AAC9B,UAAI,UAAU,EAAG,SAAQ;AACzB,aAAOA,UAAS,cAAc,OAAO,EAAE,MAAM,OAAM,CAAE;AAAA,IAC3D;AAEI,WAAO,gBAAgB,EAAE,MAAM,KAAK;AAAA,EACrC;AAAA;AAAA,EAGD,GAAG,SAAU,MAAM,OAAOA,WAAU;AAClC,QAAI,UAAU,MAAM;AAClB,aAAOA,UAAS,cAAc,KAAK,SAAQ,GAAI,EAAE,MAAM,QAAQ;AAAA,IACrE;AAEI,WAAO,gBAAgB,EAAE,MAAM,KAAK;AAAA,EACrC;AAAA;AAAA,EAGD,GAAG,SAAU,MAAM,OAAOA,WAAU;AAClC,UAAM,QAAQ,KAAK,SAAQ,IAAK;AAEhC,QAAI,UAAU,MAAM;AAClB,aAAOA,UAAS,cAAc,OAAO,EAAE,MAAM,OAAM,CAAE;AAAA,IAC3D;AAEI,WAAO,gBAAgB,OAAO,MAAM,MAAM;AAAA,EAC3C;AAAA;AAAA,EAGD,GAAG,SAAU,MAAM,OAAOA,WAAU;AAClC,QAAI,QAAQ,KAAK,SAAU;AAC3B,QAAI,UAAU,EAAG,SAAQ;AAEzB,QAAI,UAAU,MAAM;AAClB,aAAOA,UAAS,cAAc,OAAO,EAAE,MAAM,OAAM,CAAE;AAAA,IAC3D;AAEI,WAAO,gBAAgB,OAAO,MAAM,MAAM;AAAA,EAC3C;AAAA;AAAA,EAGD,GAAG,SAAU,MAAM,OAAOA,WAAU;AAClC,QAAI,UAAU,MAAM;AAClB,aAAOA,UAAS,cAAc,KAAK,WAAU,GAAI,EAAE,MAAM,UAAU;AAAA,IACzE;AAEI,WAAO,gBAAgB,EAAE,MAAM,KAAK;AAAA,EACrC;AAAA;AAAA,EAGD,GAAG,SAAU,MAAM,OAAOA,WAAU;AAClC,QAAI,UAAU,MAAM;AAClB,aAAOA,UAAS,cAAc,KAAK,WAAU,GAAI,EAAE,MAAM,UAAU;AAAA,IACzE;AAEI,WAAO,gBAAgB,EAAE,MAAM,KAAK;AAAA,EACrC;AAAA;AAAA,EAGD,GAAG,SAAU,MAAM,OAAO;AACxB,WAAO,gBAAgB,EAAE,MAAM,KAAK;AAAA,EACrC;AAAA;AAAA,EAGD,GAAG,SAAU,MAAM,OAAO,WAAW;AACnC,UAAM,iBAAiB,KAAK,kBAAmB;AAE/C,QAAI,mBAAmB,GAAG;AACxB,aAAO;AAAA,IACb;AAEI,YAAQ,OAAK;AAAA,MAEX,KAAK;AACH,eAAO,kCAAkC,cAAc;AAAA,MAKzD,KAAK;AAAA,MACL,KAAK;AACH,eAAO,eAAe,cAAc;AAAA,MAKtC,KAAK;AAAA,MACL,KAAK;AAAA,MACL;AACE,eAAO,eAAe,gBAAgB,GAAG;AAAA,IACjD;AAAA,EACG;AAAA;AAAA,EAGD,GAAG,SAAU,MAAM,OAAO,WAAW;AACnC,UAAM,iBAAiB,KAAK,kBAAmB;AAE/C,YAAQ,OAAK;AAAA,MAEX,KAAK;AACH,eAAO,kCAAkC,cAAc;AAAA,MAKzD,KAAK;AAAA,MACL,KAAK;AACH,eAAO,eAAe,cAAc;AAAA,MAKtC,KAAK;AAAA,MACL,KAAK;AAAA,MACL;AACE,eAAO,eAAe,gBAAgB,GAAG;AAAA,IACjD;AAAA,EACG;AAAA;AAAA,EAGD,GAAG,SAAU,MAAM,OAAO,WAAW;AACnC,UAAM,iBAAiB,KAAK,kBAAmB;AAE/C,YAAQ,OAAK;AAAA,MAEX,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AACH,eAAO,QAAQ,oBAAoB,gBAAgB,GAAG;AAAA,MAExD,KAAK;AAAA,MACL;AACE,eAAO,QAAQ,eAAe,gBAAgB,GAAG;AAAA,IACzD;AAAA,EACG;AAAA;AAAA,EAGD,GAAG,SAAU,MAAM,OAAO,WAAW;AACnC,UAAM,iBAAiB,KAAK,kBAAmB;AAE/C,YAAQ,OAAK;AAAA,MAEX,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AACH,eAAO,QAAQ,oBAAoB,gBAAgB,GAAG;AAAA,MAExD,KAAK;AAAA,MACL;AACE,eAAO,QAAQ,eAAe,gBAAgB,GAAG;AAAA,IACzD;AAAA,EACG;AAAA;AAAA,EAGD,GAAG,SAAU,MAAM,OAAO,WAAW;AACnC,UAAM,YAAY,KAAK,MAAM,KAAK,QAAS,IAAG,GAAI;AAClD,WAAO,gBAAgB,WAAW,MAAM,MAAM;AAAA,EAC/C;AAAA;AAAA,EAGD,GAAG,SAAU,MAAM,OAAO,WAAW;AACnC,UAAM,YAAY,KAAK,QAAS;AAChC,WAAO,gBAAgB,WAAW,MAAM,MAAM;AAAA,EAC/C;AACH;AAEA,SAAS,oBAAoB,QAAQ,YAAY,IAAI;AACnD,QAAM,OAAO,SAAS,IAAI,MAAM;AAChC,QAAM,YAAY,KAAK,IAAI,MAAM;AACjC,QAAM,QAAQ,KAAK,MAAM,YAAY,EAAE;AACvC,QAAM,UAAU,YAAY;AAC5B,MAAI,YAAY,GAAG;AACjB,WAAO,OAAO,OAAO,KAAK;AAAA,EAC9B;AACE,SAAO,OAAO,OAAO,KAAK,IAAI,YAAY,gBAAgB,SAAS,CAAC;AACtE;AAEA,SAAS,kCAAkC,QAAQ,WAAW;AAC5D,MAAI,SAAS,OAAO,GAAG;AACrB,UAAM,OAAO,SAAS,IAAI,MAAM;AAChC,WAAO,OAAO,gBAAgB,KAAK,IAAI,MAAM,IAAI,IAAI,CAAC;AAAA,EAC1D;AACE,SAAO,eAAe,QAAQ,SAAS;AACzC;AAEA,SAAS,eAAe,QAAQ,YAAY,IAAI;AAC9C,QAAM,OAAO,SAAS,IAAI,MAAM;AAChC,QAAM,YAAY,KAAK,IAAI,MAAM;AACjC,QAAM,QAAQ,gBAAgB,KAAK,MAAM,YAAY,EAAE,GAAG,CAAC;AAC3D,QAAM,UAAU,gBAAgB,YAAY,IAAI,CAAC;AACjD,SAAO,OAAO,QAAQ,YAAY;AACpC;ACvwBA,MAAM,oBAAoB,CAAC,SAASC,gBAAe;AACjD,UAAQ,SAAO;AAAA,IACb,KAAK;AACH,aAAOA,YAAW,KAAK,EAAE,OAAO,QAAO,CAAE;AAAA,IAC3C,KAAK;AACH,aAAOA,YAAW,KAAK,EAAE,OAAO,SAAQ,CAAE;AAAA,IAC5C,KAAK;AACH,aAAOA,YAAW,KAAK,EAAE,OAAO,OAAM,CAAE;AAAA,IAC1C,KAAK;AAAA,IACL;AACE,aAAOA,YAAW,KAAK,EAAE,OAAO,OAAM,CAAE;AAAA,EAC9C;AACA;AAEA,MAAM,oBAAoB,CAAC,SAASA,gBAAe;AACjD,UAAQ,SAAO;AAAA,IACb,KAAK;AACH,aAAOA,YAAW,KAAK,EAAE,OAAO,QAAO,CAAE;AAAA,IAC3C,KAAK;AACH,aAAOA,YAAW,KAAK,EAAE,OAAO,SAAQ,CAAE;AAAA,IAC5C,KAAK;AACH,aAAOA,YAAW,KAAK,EAAE,OAAO,OAAM,CAAE;AAAA,IAC1C,KAAK;AAAA,IACL;AACE,aAAOA,YAAW,KAAK,EAAE,OAAO,OAAM,CAAE;AAAA,EAC9C;AACA;AAEA,MAAM,wBAAwB,CAAC,SAASA,gBAAe;AACrD,QAAM,cAAc,QAAQ,MAAM,WAAW,KAAK,CAAE;AACpD,QAAM,cAAc,YAAY,CAAC;AACjC,QAAM,cAAc,YAAY,CAAC;AAEjC,MAAI,CAAC,aAAa;AAChB,WAAO,kBAAkB,SAASA,WAAU;AAAA,EAChD;AAEE,MAAI;AAEJ,UAAQ,aAAW;AAAA,IACjB,KAAK;AACH,uBAAiBA,YAAW,SAAS,EAAE,OAAO,QAAO,CAAE;AACvD;AAAA,IACF,KAAK;AACH,uBAAiBA,YAAW,SAAS,EAAE,OAAO,SAAQ,CAAE;AACxD;AAAA,IACF,KAAK;AACH,uBAAiBA,YAAW,SAAS,EAAE,OAAO,OAAM,CAAE;AACtD;AAAA,IACF,KAAK;AAAA,IACL;AACE,uBAAiBA,YAAW,SAAS,EAAE,OAAO,OAAM,CAAE;AACtD;AAAA,EACN;AAEE,SAAO,eACJ,QAAQ,YAAY,kBAAkB,aAAaA,WAAU,CAAC,EAC9D,QAAQ,YAAY,kBAAkB,aAAaA,WAAU,CAAC;AACnE;AAEO,MAAM,iBAAiB;AAAA,EAC5B,GAAG;AAAA,EACH,GAAG;AACL;AC/DA,MAAM,mBAAmB;AACzB,MAAM,kBAAkB;AAExB,MAAM,cAAc,CAAC,KAAK,MAAM,MAAM,MAAM;AAErC,SAAS,0BAA0B,OAAO;AAC/C,SAAO,iBAAiB,KAAK,KAAK;AACpC;AAEO,SAAS,yBAAyB,OAAO;AAC9C,SAAO,gBAAgB,KAAK,KAAK;AACnC;AAEO,SAAS,0BAA0B,OAAOF,SAAQ,OAAO;AAC9D,QAAM,WAAW,QAAQ,OAAOA,SAAQ,KAAK;AAC7C,UAAQ,KAAK,QAAQ;AACrB,MAAI,YAAY,SAAS,KAAK,EAAG,OAAM,IAAI,WAAW,QAAQ;AAChE;AAEA,SAAS,QAAQ,OAAOA,SAAQ,OAAO;AACrC,QAAM,UAAU,MAAM,CAAC,MAAM,MAAM,UAAU;AAC7C,SAAO,SAAS,MAAM,YAAa,CAAA,mBAAmB,KAAK,YAAYA,OAAM,sBAAsB,OAAO,mBAAmB,KAAK;AACpI;ACKA,MAAM,yBACJ;AAIF,MAAM,6BAA6B;AAEnC,MAAM,sBAAsB;AAC5B,MAAM,oBAAoB;AAC1B,MAAM,gCAAgC;AAsS/B,SAAS,OAAO,MAAM,WAAW,SAAS;AlC1UjD;AkC2UE,QAAMD,kBAAiB,kBAAmB;AAC1C,QAAM,UAAS,mCAAS,WAAUA,gBAAe,UAAUI;AAE3D,QAAM,yBACJ,mCAAS,4BACT,8CAAS,WAAT,mBAAiB,YAAjB,mBAA0B,0BAC1BJ,gBAAe,2BACf,WAAAA,gBAAe,WAAf,mBAAuB,YAAvB,mBAAgC,0BAChC;AAEF,QAAM,gBACJ,mCAAS,mBACT,8CAAS,WAAT,mBAAiB,YAAjB,mBAA0B,iBAC1BA,gBAAe,kBACf,WAAAA,gBAAe,WAAf,mBAAuB,YAAvB,mBAAgC,iBAChC;AAEF,QAAM,eAAe,OAAO,IAAI;AAEhC,MAAI,CAAC,QAAQ,YAAY,GAAG;AAC1B,UAAM,IAAI,WAAW,oBAAoB;AAAA,EAC7C;AAEE,MAAI,QAAQ,UACT,MAAM,0BAA0B,EAChC,IAAI,CAAC,cAAc;AAClB,UAAM,iBAAiB,UAAU,CAAC;AAClC,QAAI,mBAAmB,OAAO,mBAAmB,KAAK;AACpD,YAAM,gBAAgB,eAAe,cAAc;AACnD,aAAO,cAAc,WAAW,OAAO,UAAU;AAAA,IACzD;AACM,WAAO;AAAA,EACR,CAAA,EACA,KAAK,EAAE,EACP,MAAM,sBAAsB,EAC5B,IAAI,CAAC,cAAc;AAElB,QAAI,cAAc,MAAM;AACtB,aAAO,EAAE,SAAS,OAAO,OAAO,IAAK;AAAA,IAC7C;AAEM,UAAM,iBAAiB,UAAU,CAAC;AAClC,QAAI,mBAAmB,KAAK;AAC1B,aAAO,EAAE,SAAS,OAAO,OAAO,mBAAmB,SAAS,EAAG;AAAA,IACvE;AAEM,QAAI,WAAW,cAAc,GAAG;AAC9B,aAAO,EAAE,SAAS,MAAM,OAAO,UAAW;AAAA,IAClD;AAEM,QAAI,eAAe,MAAM,6BAA6B,GAAG;AACvD,YAAM,IAAI;AAAA,QACR,mEACE,iBACA;AAAA,MACH;AAAA,IACT;AAEM,WAAO,EAAE,SAAS,OAAO,OAAO,UAAW;AAAA,EACjD,CAAK;AAGH,MAAI,OAAO,SAAS,cAAc;AAChC,YAAQ,OAAO,SAAS,aAAa,cAAc,KAAK;AAAA,EAC5D;AAEE,QAAM,mBAAmB;AAAA,IACvB;AAAA,IACA;AAAA,IACA;AAAA,EACD;AAED,SAAO,MACJ,IAAI,CAAC,SAAS;AACb,QAAI,CAAC,KAAK,QAAS,QAAO,KAAK;AAE/B,UAAM,QAAQ,KAAK;AAEnB,QACG,EAAC,mCAAS,gCACT,yBAAyB,KAAK,KAC/B,EAAC,mCAAS,iCACT,0BAA0B,KAAK,GACjC;AACA,gCAA0B,OAAO,WAAW,OAAO,IAAI,CAAC;AAAA,IAChE;AAEM,UAAM,YAAY,WAAW,MAAM,CAAC,CAAC;AACrC,WAAO,UAAU,cAAc,OAAO,OAAO,UAAU,gBAAgB;AAAA,EACxE,CAAA,EACA,KAAK,EAAE;AACZ;AAEA,SAAS,mBAAmB,OAAO;AACjC,QAAM,UAAU,MAAM,MAAM,mBAAmB;AAE/C,MAAI,CAAC,SAAS;AACZ,WAAO;AAAA,EACX;AAEE,SAAO,QAAQ,CAAC,EAAE,QAAQ,mBAAmB,GAAG;AAClD;", "x_google_ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34]}