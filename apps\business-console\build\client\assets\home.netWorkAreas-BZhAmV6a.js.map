{"version": 3, "file": "home.netWorkAreas-BZhAmV6a.js", "sources": ["../../../app/routes/home.netWorkAreas.tsx"], "sourcesContent": ["\r\nimport { ActionFunctionArgs } from \"@remix-run/node\";\r\nimport { Form, json, useActionD<PERSON>, useF<PERSON>cher, useLoaderData } from \"@remix-run/react\";\r\nimport { Edit } from \"lucide-react\";\r\nimport { useEffect, useMemo, useState } from \"react\";\r\nimport { But<PERSON> } from \"~/components/ui/button\";\r\nimport { Input } from \"~/components/ui/input\";\r\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from \"~/components/ui/select\";\r\nimport { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from \"~/components/ui/table\";\r\nimport { getNetWorkAreas } from \"~/services/netWorks\"\r\nimport { User } from \"~/types\";\r\nimport { NetWorkAreaDetails, NetWorkDetails } from \"~/types/api/businessConsoleService/netWorkinfo\";\r\nimport { getSession } from \"~/utils/session.server\";\r\nimport { withAuth, withResponse } from \"@utils/auth-utils\";\r\n\r\n\r\n\r\nexport const loader = withAuth(async ({ request }) => {\r\n      try {\r\n\r\n            // const response = await getNetWorks(request);\r\n            return withResponse({ data: [] }, new Headers());\r\n      } catch (error) {\r\n            console.error('Network details error:', error);\r\n            throw new Error(`Error fetching network details: ${error}`);\r\n      }\r\n});\r\n\r\nexport const action = withAuth(async ({ request }) => {\r\n      try {\r\n            const formData = await request.formData();\r\n            const networkId = formData.get(\"netWorkId\");\r\n\r\n            if (!networkId) {\r\n                  throw json(\r\n                        { error: \"Network ID is required\" },\r\n                        { status: 400 }\r\n                  );\r\n            }\r\n\r\n            const session = await getSession(request.headers.get(\"Cookie\"));\r\n            const access_token = session.get(\"access_token\") as string | null;\r\n\r\n            const response = await getNetWorkAreas(Number(networkId), access_token);\r\n            return withResponse({ data: response.data }, response.headers);\r\n      } catch (error) {\r\n            console.error('Network areas error:', error);\r\n            throw new Response(\"Failed to get network areas\", { status: 500 });\r\n      }\r\n});\r\n\r\nexport default function NetWorkAreas() {\r\n      const initialData = useLoaderData<{ data: NetWorkDetails[] }>();\r\n      const areaData = useActionData<{ data: NetWorkAreaDetails[] }>();\r\n      const [netWorkAreas, setNetWorkAreas] = useState<NetWorkAreaDetails[] | []>(areaData?.data || [])\r\n      const [searchTerm, setSearchTerm] = useState(\"\")\r\n      const [netWorkId, setNetWorkId] = useState(\"\")\r\n      const [pageSize, setPageSize] = useState(\"50\");\r\n      const [currentPage, setCurrentPage] = useState(1);\r\n\r\n      useEffect(() => {\r\n            if (areaData?.data) {\r\n                  setNetWorkAreas(areaData.data)\r\n            }\r\n\r\n\r\n      }, [areaData?.data])\r\n\r\n      const filterNetWorks = netWorkAreas\r\n            ?.filter((x) => x.agentName.toLowerCase().includes(searchTerm.toLowerCase()) ||\r\n                  x.networkAreaName.toLowerCase().includes(searchTerm.toLowerCase()))\r\n\r\n      const paginatedData = useMemo(() => {\r\n            const start = (currentPage - 1) * Number(pageSize);\r\n            const end = start + Number(pageSize);\r\n            return [...filterNetWorks]\r\n                  .sort((a, b) => (a.networkAreaId > b.networkAreaId ? -1 : 1))\r\n                  .slice(start, end);\r\n      }, [filterNetWorks, currentPage, pageSize]);\r\n\r\n      const totalPages = Math.ceil(filterNetWorks.length / Number(pageSize));\r\n\r\n      return (\r\n            <div className=\"container mx-auto w-full\" >\r\n                  <div className=\"flex my-7 space-x-10\">\r\n                        <Form method=\"post\" className=\"flex space-x-8\">\r\n                              <Select value={netWorkId} onValueChange={setNetWorkId} name=\"netWorkId\">\r\n                                    <SelectTrigger className=\"w-[180px]\">\r\n                                          <SelectValue placeholder=\"Select NetWork \" />\r\n                                    </SelectTrigger>\r\n                                    <SelectContent>\r\n                                          {initialData.data.sort((a, b) => a.managerName.localeCompare(b.managerName)).map((x) => {\r\n                                                return (\r\n                                                      <SelectItem value={x.id.toString()}>{x.name}</SelectItem>\r\n\r\n                                                )\r\n                                          })\r\n                                          }\r\n\r\n                                    </SelectContent>\r\n                              </Select>\r\n                              <Button type=\"submit\" >\r\n                                    Get NetWork Areas\r\n                              </Button>\r\n                        </Form>\r\n\r\n                        <Input placeholder=\"Search Area/Agent\"\r\n                              value={searchTerm}\r\n                              onChange={(e) => setSearchTerm(e.target.value)}\r\n                              className=\"max-w-sm\"\r\n\r\n                        />\r\n                        <Select value={pageSize} onValueChange={setPageSize}>\r\n                              <SelectTrigger className=\"w-[180px]\">\r\n                                    <SelectValue placeholder=\"Rows per page\" />\r\n                              </SelectTrigger>\r\n                              <SelectContent>\r\n                                    <SelectItem value=\"5\">5 per page</SelectItem>\r\n                                    <SelectItem value=\"10\">10 per page</SelectItem>\r\n                                    <SelectItem value=\"20\">20 per page</SelectItem>\r\n                                    <SelectItem value=\"50\">50 per page</SelectItem>\r\n                              </SelectContent>\r\n                        </Select>\r\n                  </div>\r\n\r\n                  <Table>\r\n                        <TableHeader className=\"bg-gray-100\">\r\n                              <TableHead>Area Name</TableHead>\r\n                              <TableHead>Agent Name </TableHead>\r\n                              <TableHead>      </TableHead>\r\n\r\n\r\n                        </TableHeader>\r\n                        <TableBody>\r\n\r\n                              {paginatedData.length > 0 ? (\r\n                                    paginatedData?.sort((a, b) => a.networkAreaName.localeCompare(b.networkAreaName)).map((area) =>\r\n                                    (<TableRow key={area.networkAreaId}>\r\n                                          <TableCell>{area.networkAreaName}</TableCell>\r\n                                          <TableCell>{area.agentName}</TableCell>\r\n                                          <TableCell>\r\n\r\n                                                <Edit className=\"h-4 w-4\" />\r\n                                          </TableCell>\r\n\r\n\r\n\r\n                                    </TableRow>))\r\n                              )\r\n                                    : (<TableRow>\r\n                                          <TableCell\r\n                                                colSpan={9}\r\n                                                className=\"h-24 text-center\"\r\n                                          >\r\n                                                No results.\r\n                                          </TableCell>\r\n                                    </TableRow>\r\n                                    )}\r\n\r\n                        </TableBody>\r\n                  </Table>\r\n                  <div className=\"flex items-center space-x-2\">\r\n                        <Button\r\n                              variant=\"outline\"\r\n                              size=\"sm\"\r\n                              onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}\r\n                              disabled={currentPage === 1}\r\n                        >\r\n                              Previous\r\n                        </Button>\r\n                        <Button\r\n                              variant=\"outline\"\r\n                              size=\"sm\"\r\n                              onClick={() =>\r\n                                    setCurrentPage((prev) => Math.min(prev + 1, totalPages))\r\n                              }\r\n                              disabled={currentPage === totalPages}\r\n                        >\r\n                              Next\r\n                        </Button>\r\n                  </div>\r\n            </div>\r\n      )\r\n}\r\n"], "names": ["NetWorkAreas", "initialData", "useLoaderData", "areaData", "useActionData", "netWorkAreas", "setNetWorkAreas", "useState", "data", "searchTerm", "setSearchTerm", "netWorkId", "setNetWorkId", "pageSize", "setPageSize", "currentPage", "setCurrentPage", "useEffect", "filterNetWorks", "filter", "x", "<PERSON><PERSON><PERSON>", "toLowerCase", "includes", "networkAreaName", "paginatedData", "useMemo", "start", "Number", "end", "sort", "a", "b", "networkAreaId", "slice", "totalPages", "Math", "ceil", "length", "jsxs", "className", "children", "Form", "method", "Select", "value", "onValueChange", "name", "jsx", "SelectTrigger", "SelectValue", "placeholder", "SelectContent", "<PERSON><PERSON><PERSON>", "localeCompare", "map", "SelectItem", "id", "toString", "<PERSON><PERSON>", "type", "Input", "onChange", "e", "target", "Table", "TableHeader", "TableHead", "TableBody", "area", "TableRow", "TableCell", "Edit", "colSpan", "variant", "size", "onClick", "prev", "max", "disabled", "min"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmDA,SAAwBA,eAAe;AACjC,QAAMC,cAAcC,cAA0C;AAC9D,QAAMC,WAAWC,cAA8C;AACzD,QAAA,CAACC,cAAcC,eAAe,IAAIC,uBAAoCJ,qCAAUK,SAAQ,EAAE;AAChG,QAAM,CAACC,YAAYC,aAAa,IAAIH,aAAAA,SAAS,EAAE;AAC/C,QAAM,CAACI,WAAWC,YAAY,IAAIL,aAAAA,SAAS,EAAE;AAC7C,QAAM,CAACM,UAAUC,WAAW,IAAIP,aAAAA,SAAS,IAAI;AAC7C,QAAM,CAACQ,aAAaC,cAAc,IAAIT,aAAAA,SAAS,CAAC;AAEhDU,eAAAA,UAAU,MAAM;AACV,QAAId,qCAAUK,MAAM;AACdF,sBAAgBH,SAASK,IAAI;AAAA,IACnC;AAAA,EAGN,GAAG,CAACL,qCAAUK,IAAI,CAAC;AAEb,QAAAU,iBAAiBb,6CACfc,OAAQC,OAAMA,EAAEC,UAAUC,YAAY,EAAEC,SAASd,WAAWa,aAAa,KACrEF,EAAEI,gBAAgBF,cAAcC,SAASd,WAAWa,YAAY,CAAC;AAEvE,QAAAG,gBAAgBC,aAAAA,QAAQ,MAAM;AAC9B,UAAMC,SAASZ,cAAc,KAAKa,OAAOf,QAAQ;AAC3C,UAAAgB,MAAMF,QAAQC,OAAOf,QAAQ;AACnC,WAAO,CAAC,GAAGK,cAAc,EAClBY,KAAK,CAACC,GAAGC,MAAOD,EAAEE,gBAAgBD,EAAEC,gBAAgB,KAAK,CAAE,EAC3DC,MAAMP,OAAOE,GAAG;AAAA,EAC1B,GAAA,CAACX,gBAAgBH,aAAaF,QAAQ,CAAC;AAE1C,QAAMsB,aAAaC,KAAKC,KAAKnB,eAAeoB,SAASV,OAAOf,QAAQ,CAAC;AAG/D,SAAA0B,kCAAAA,KAAC,OAAI;AAAA,IAAAC,WAAU;AAAA,IACTC,UAAA,CAACF,kCAAA,KAAA,OAAA;AAAA,MAAIC,WAAU;AAAA,MACTC,UAAA,CAAAF,kCAAA,KAACG,MAAK;AAAA,QAAAC,QAAO;AAAA,QAAOH,WAAU;AAAA,QACxBC,UAAA,CAAAF,kCAAA,KAACK;UAAOC,OAAOlC;AAAAA,UAAWmC,eAAelC;AAAAA,UAAcmC,MAAK;AAAA,UACtDN,UAAA,CAAAO,kCAAA,IAACC;YAAcT,WAAU;AAAA,YACnBC,gDAACS,aAAY;AAAA,cAAAC,aAAY;AAAA,YAAkB,CAAA;AAAA,UACjD,CAAA,yCACCC,eACM;AAAA,YAAAX,UAAAxC,YAAYO,KAAKsB,KAAK,CAACC,GAAGC,MAAMD,EAAEsB,YAAYC,cAActB,EAAEqB,WAAW,CAAC,EAAEE,IAAKnC,OAAM;AAE5E,qBAAA4B,kCAAAA,IAACQ;gBAAWX,OAAOzB,EAAEqC,GAAGC,SAAS;AAAA,gBAAIjB,YAAEM;AAAAA,cAAK,CAAA;AAAA,YAGvD,CAAA;AAAA,UAGP,CAAA,CAAA;AAAA,QACN,CAAA,GACCC,kCAAA,IAAAW,QAAA;AAAA,UAAOC,MAAK;AAAA,UAAUnB,UAEvB;AAAA,QAAA,CAAA,CAAA;AAAA,MACN,CAAA,GAEAO,kCAAA,IAACa,OAAA;AAAA,QAAMV,aAAY;AAAA,QACbN,OAAOpC;AAAAA,QACPqD,UAAWC,OAAMrD,cAAcqD,EAAEC,OAAOnB,KAAK;AAAA,QAC7CL,WAAU;AAAA,MAAA,CAEhB,GACCD,kCAAA,KAAAK,QAAA;AAAA,QAAOC,OAAOhC;AAAAA,QAAUiC,eAAehC;AAAAA,QAClC2B,UAAA,CAAAO,kCAAA,IAACC;UAAcT,WAAU;AAAA,UACnBC,gDAACS,aAAY;AAAA,YAAAC,aAAY;AAAA,UAAgB,CAAA;AAAA,QAC/C,CAAA,0CACCC,eACK;AAAA,UAAAX,UAAA,CAACO,kCAAA,IAAAQ,YAAA;AAAA,YAAWX,OAAM;AAAA,YAAIJ,UAAU;AAAA,UAAA,CAAA,GAC/BO,kCAAA,IAAAQ,YAAA;AAAA,YAAWX,OAAM;AAAA,YAAKJ,UAAW;AAAA,UAAA,CAAA,GACjCO,kCAAA,IAAAQ,YAAA;AAAA,YAAWX,OAAM;AAAA,YAAKJ,UAAW;AAAA,UAAA,CAAA,GACjCO,kCAAA,IAAAQ,YAAA;AAAA,YAAWX,OAAM;AAAA,YAAKJ,UAAW;AAAA,UAAA,CAAA,CAAA;AAAA,QACxC,CAAA,CAAA;AAAA,MACN,CAAA,CAAA;AAAA,IACN,CAAA,0CAECwB,OACK;AAAA,MAAAxB,UAAA,CAACF,kCAAA,KAAA2B,aAAA;AAAA,QAAY1B,WAAU;AAAA,QACjBC,UAAA,CAAAO,kCAAA,IAACmB;UAAU1B,UAAS;AAAA,QAAA,CAAA,GACpBO,kCAAA,IAACmB;UAAU1B,UAAW;AAAA,QAAA,CAAA,GACtBO,kCAAA,IAACmB;UAAU1B,UAAM;AAAA,QAAA,CAAA,CAAA;AAAA,MAGvB,CAAA,GACAO,kCAAA,IAACoB;QAEM3B,UAAchB,cAAAa,SAAS,IAClBb,+CAAeK,KAAK,CAACC,GAAGC,MAAMD,EAAEP,gBAAgB8B,cAActB,EAAER,eAAe,GAAG+B,IAAKc,UACtF9B,kCAAA,KAAC+B,UACI;AAAA,UAAA7B,UAAA,CAACO,kCAAA,IAAAuB,WAAA;AAAA,YAAW9B,eAAKjB;AAAAA,UAAgB,CAAA,GACjCwB,kCAAA,IAACuB,WAAW;AAAA,YAAA9B,UAAA4B,KAAKhD;AAAAA,UAAU,CAAA,yCAC1BkD,WAEK;AAAA,YAAA9B,UAAAO,kCAAA,IAACwB,WAAK;AAAA,cAAAhC,WAAU;AAAA,YAAU,CAAA;AAAA,UAChC,CAAA,CAAA;AAAA,QAAA,GANU6B,KAAKpC,aAUrB,2CAEIqC,UACE;AAAA,UAAA7B,UAAAO,kCAAA,IAACuB,WAAA;AAAA,YACKE,SAAS;AAAA,YACTjC,WAAU;AAAA,YACfC,UAAA;AAAA,UAED,CAAA;AAAA,QACN,CAAA;AAAA,MAGZ,CAAA,CAAA;AAAA,IACN,CAAA,GACAF,kCAAA,KAAC,OAAI;AAAA,MAAAC,WAAU;AAAA,MACTC,UAAA,CAAAO,kCAAA,IAACW,QAAA;AAAA,QACKe,SAAQ;AAAA,QACRC,MAAK;AAAA,QACLC,SAASA,MAAM5D,eAAgB6D,UAASzC,KAAK0C,IAAID,OAAO,GAAG,CAAC,CAAC;AAAA,QAC7DE,UAAUhE,gBAAgB;AAAA,QAC/B0B,UAAA;AAAA,MAAA,CAED,GACAO,kCAAA,IAACW,QAAA;AAAA,QACKe,SAAQ;AAAA,QACRC,MAAK;AAAA,QACLC,SAASA,MACH5D,eAAgB6D,UAASzC,KAAK4C,IAAIH,OAAO,GAAG1C,UAAU,CAAC;AAAA,QAE7D4C,UAAUhE,gBAAgBoB;AAAAA,QAC/BM,UAAA;AAAA,MAAA,CAED,CAAA;AAAA,IACN,CAAA,CAAA;AAAA,EACN,CAAA;AAEZ;"}