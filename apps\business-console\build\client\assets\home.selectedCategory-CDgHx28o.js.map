{"version": 3, "file": "home.selectedCategory-CDgHx28o.js", "sources": ["../../../app/components/ui/addCategoryItem.tsx", "../../../app/routes/home.selectedCategory.tsx"], "sourcesContent": ["import { Check, CirclePlus, Pencil } from \"lucide-react\";\r\nimport { <PERSON><PERSON>, DialogContent, DialogTrigger } from \"./dialog\";\r\nimport { But<PERSON> } from \"./button\";\r\nimport { useState } from \"react\";\r\nimport { ItemsList, MasterItemCategories } from \"~/types/api/businessConsoleService/MasterItemCategory\";\r\nimport { CategoryItem, SearchableCategories } from \"../masterItems/searchableCategories\";\r\n\r\n\r\n\r\n\r\ninterface AddCategoryItemProps {\r\n      categoryDetails?: MasterItemCategories[];\r\n      buttonName: string;\r\n      handleSubmit: (itemId: number, ParentCategoryIds: CategoryItem[], itemDetails: ItemsList, closeDialog: () => void) => void;\r\n      mode?: string\r\n      level?: number,\r\n      itemDetails?: ItemsList\r\n\r\n}\r\nexport default function AddCategoryItem({\r\n      categoryDetails,\r\n      buttonName,\r\n      handleSubmit,\r\n      mode,\r\n      level,\r\n      itemDetails\r\n\r\n}: AddCategoryItemProps) {\r\n\r\n      const [dialogOpen, setDialogOpen] = useState(false);\r\n\r\n      const transformToCategoryItem = (parentCategories: MasterItemCategories[]): CategoryItem[] => {\r\n            return parentCategories.map((category) => ({\r\n                  value: category.id.toString(),\r\n                  label: category.name,\r\n                  numericId: category.id,\r\n            }));\r\n      };\r\n\r\n      const [updatedCategory, setUpdatedCategory] = useState<CategoryItem[]>(() =>\r\n            mode === \"Edit\" && categoryDetails ? transformToCategoryItem(categoryDetails) : []\r\n      );\r\n\r\n      function handleClose() {\r\n            setDialogOpen(false);\r\n      }\r\n\r\n      const handleSubmitBtn = () => {\r\n\r\n            const categoriesWithDetails = updatedCategory.map((category) => ({\r\n                  id: category.numericId,\r\n                  disabled: false,\r\n                  name: category.label,\r\n                  picture: itemDetails?.picture || \"\",\r\n                  picturex: \"\",\r\n                  picturexx: \"\",\r\n                  level: level || 0,\r\n                  totalItems: 1,\r\n                  parentCategories: [],\r\n            }));\r\n            const requestBody = {\r\n                  id: itemDetails?.id || 0,\r\n                  disabled: false,\r\n                  defaultUnit: itemDetails?.defaultUnit || \"\",\r\n                  name: itemDetails?.name || \"Default Item Name\",\r\n                  picture: itemDetails?.picture || \"\",\r\n                  nameInKannada: itemDetails?.nameInKannada || \"\",\r\n                  nameInTelugu: itemDetails?.nameInTelugu || \"\",\r\n                  nameInTamil: itemDetails?.nameInTamil || \"\",\r\n                  nameInMalayalam: itemDetails?.nameInMalayalam || \"\",\r\n                  nameInHindi: itemDetails?.nameInHindi || \"\",\r\n                  nameInAssame: itemDetails?.nameInAssame || \"\",\r\n                  nameInGujarati: itemDetails?.nameInGujarati || \"\",\r\n                  nameInMarathi: itemDetails?.nameInMarathi || \"\",\r\n                  nameInBangla: itemDetails?.nameInBangla || \"\",\r\n                  defaultWeightFactor: itemDetails?.defaultWeightFactor || 0,\r\n                  gstHsnCode: itemDetails?.gstHsnCode || \"\",\r\n                  gstRate: itemDetails?.gstRate || 0,\r\n                  source: itemDetails?.source || \"\",\r\n                  sourceKey: itemDetails?.sourceKey || \"\",\r\n                  productId: itemDetails?.productId || \"\",\r\n                  brandName: itemDetails?.brandName || \"\",\r\n                  packaging: itemDetails?.packaging || \"\",\r\n                  mrp: itemDetails?.mrp || 0,\r\n                  ownerBId: itemDetails?.ownerBId || 0,\r\n                  b2b: itemDetails?.b2b || false,\r\n                  b2c: itemDetails?.b2c || false,\r\n                  groupId: itemDetails?.groupId || \"\",\r\n                  groupSeq: itemDetails?.groupSeq || 0,\r\n                  searchTag: itemDetails?.searchTag || \"\",\r\n\r\n                  categories: categoriesWithDetails,\r\n            };\r\n\r\n\r\n\r\n\r\n            handleSubmit(itemDetails?.id ?? 0, updatedCategory, requestBody, handleClose);\r\n\r\n      }\r\n\r\n\r\n\r\n      return (\r\n            <div className=\"container mx-auto   \">\r\n                  <Dialog open={dialogOpen} onOpenChange={(isOpen) => {\r\n                        setDialogOpen(isOpen);\r\n                        if (!isOpen) {\r\n                              setDialogOpen(false);\r\n                        }\r\n                  }}>\r\n                        <DialogTrigger asChild className=\"flex \">\r\n                              <Pencil\r\n                                    size={16}\r\n                                    className=\"cursor-pointer\"  >\r\n                              </Pencil>\r\n                        </DialogTrigger>\r\n                        <DialogContent\r\n                              className=\"w-full sm:w-[400px] md:w-[600px] max-h-[90vh] overflow-y-auto absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2\"\r\n                        >\r\n                              <div className=\"grid gap-4\">\r\n                                    <div className=\"space-y-2\">\r\n                                          <h4 className=\"font-medium leading-none text-center md:text-left\">\r\n                                                Edit Categories For Item\r\n                                          </h4>\r\n                                    </div>\r\n\r\n                                    <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n                                          <div className=\"space-y-2\">\r\n                                                <img\r\n                                                      src={\r\n                                                            itemDetails?.picture && itemDetails.picture.includes(\",\")\r\n                                                                  ? itemDetails.picture.split(\",\")[0] // Use the first image in the split array\r\n                                                                  : itemDetails?.picture\r\n                                                      }\r\n                                                      alt=\"\"\r\n                                                      className=\"w-12 h-12 object-cover\"\r\n                                                />\r\n                                                <p className=\"text-md font-bold\">Item Name : {itemDetails?.name}  </p>\r\n                                          </div>\r\n                                    </div>\r\n                              </div>\r\n                              <div className=\"col-span-full\">\r\n                                    <SearchableCategories\r\n                                          label=\"Assigned Categories\"\r\n                                          apiUrl=\"/home/<USER>\"\r\n\r\n                                          selectedCategories={updatedCategory || []}\r\n                                          onCategoryAdd={(categoryId, categoryName) => {\r\n\r\n                                                setUpdatedCategory((prevUpdatedCategory) => [\r\n                                                      ...(prevUpdatedCategory || []),\r\n                                                      { numericId: categoryId, value: categoryName, label: \"\" }\r\n                                                ]);\r\n                                          }}\r\n                                          onCategoryRemove={(categoryId) => {\r\n\r\n                                                setUpdatedCategory((prevUpdatedCategory) =>\r\n                                                      (prevUpdatedCategory || []).filter(\r\n                                                            (cat) => cat.numericId !== categoryId\r\n                                                      )\r\n                                                );\r\n                                          }}\r\n                                          required={true}\r\n                                          level={1}\r\n\r\n                                    />\r\n                              </div>\r\n                              <div className=\"flex flex-col md:flex-row justify-end gap-2 mt-5\">\r\n                                    <Button\r\n                                          size=\"sm\"\r\n                                          className=\"w-full md:w-auto\"\r\n\r\n                                          onClick={() => handleSubmitBtn()}  >\r\n                                          Submit\r\n                                    </Button>\r\n\r\n                              </div>\r\n                        </DialogContent>\r\n                  </Dialog>\r\n\r\n            </div>\r\n      );\r\n}\r\n", "import { <PERSON>, json, useFetcher, use<PERSON>oaderD<PERSON>, useNavigate } from \"@remix-run/react\";\r\nimport { ChevronRight, Edit, ListFilter, Pencil } from \"lucide-react\";\r\nimport { useEffect, useState } from \"react\";\r\nimport { CategoryItem } from \"~/components/masterItems/searchableCategories\";\r\nimport AddCategoryItem from \"~/components/ui/addCategoryItem\";\r\nimport AddMasterCategory from \"~/components/ui/addMasterCategory\";\r\nimport { Card, CardContent } from \"~/components/ui/card\";\r\nimport { Input } from \"~/components/ui/input\";\r\nimport { Pagination, PaginationContent, PaginationItem, PaginationLink, PaginationNext, PaginationPrevious } from \"~/components/ui/pagination\";\r\nimport { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from \"~/components/ui/table\";\r\nimport { Tabs, TabsContent, TabsList, TabsTrigger } from \"~/components/ui/tabs\";\r\nimport { useDebounce } from \"~/hooks/useDebounce\";\r\nimport { getSelectedChildCategory, getSelectedMasterCatItems, getSelectedMasterItemCategories, getUploadedUrl, updateAttributes, updateMasterItem, UpdateMasterItemCategory } from \"~/services/masterItemCategories\";\r\nimport s3Service from \"~/services/s3.service\";\r\nimport { ItemsList, MasterItemCategories } from \"~/types/api/businessConsoleService/MasterItemCategory\";\r\nimport { withAuth, withResponse } from \"~/utils/auth-utils\";\r\ninterface LoaderData {\r\n      selectedId: number,\r\n      name: string,\r\n      items: ItemsList[],\r\n      categoryDetail: MasterItemCategories,\r\n      parentCategory: MasterItemCategories[],\r\n      categoryList: MasterItemCategories[],\r\n\r\n\r\n\r\n}\r\n\r\nexport const loader = withAuth(async ({ user, request }) => {\r\n      const url = new URL(request.url);\r\n      const selectediCId = Number(url.searchParams.get(\"selectedICId\"));\r\n      const level = Number(url.searchParams.get(\"Level\"))\r\n\r\n      const name = (url.searchParams.get(\"name\"));\r\n      const activeTab = url.searchParams.get(\"activeTab\") || level === 1 && \"Items\"\r\n      const searchTerm = url.searchParams.get(\"searchTerm\") || \"\";\r\n      const page = Number(url.searchParams.get(\"page\")) || 0;\r\n      const pageSize = Number(url.searchParams.get(\"pageSize\")) || 10;\r\n\r\n\r\n      try {\r\n            let response: any;\r\n            let categoryList: MasterItemCategories[] | [] = [];\r\n            let items: ItemsList[] | [] = []\r\n            const categoryDetail = await getSelectedMasterItemCategories(selectediCId, request);\r\n            switch (activeTab) {\r\n\r\n                  case \"Items\":\r\n                        response = await getSelectedMasterCatItems(\r\n                              selectediCId,\r\n                              request,\r\n                              page,\r\n                              pageSize,\r\n                              searchTerm\r\n                        );\r\n                        if (response.data?.length) {\r\n                              items = response.data;\r\n                        }\r\n                        break;\r\n\r\n\r\n                  case \"childCategories\":\r\n                        response = await getSelectedChildCategory(selectediCId,\r\n                              request,\r\n                              page,\r\n                              pageSize,\r\n                              searchTerm);\r\n                        if (response.data?.length) {\r\n                              categoryList = response.data;\r\n                        }\r\n                        break;\r\n            }\r\n            return withResponse({\r\n                  categoryDetail: categoryDetail.data,\r\n                  categoryList,\r\n                  parentCategory: categoryDetail.data.parentCategories,\r\n                  items,\r\n                  selectedId: selectediCId,\r\n                  name: name,\r\n            }, categoryDetail?.headers);\r\n      }\r\n      catch (error) {\r\n            if (error instanceof Response && error.status === 404) {\r\n                  throw json({ error: \"MasterItemCategory pg Not found\" }, { status: 404 });\r\n            }\r\n            throw new Response(\"Failed to fetch MasterItemCategory \", { status: 500 });\r\n      }\r\n})\r\nexport const action = withAuth(async ({ user, request }) => {\r\n      const formData = await request.formData();\r\n      const intent = formData.get(\"_intent\");\r\n      const categoryName = formData.get(\"categoryName\") as string;\r\n      const categoryLevel = formData.get(\"categoryLevel\") as unknown as number;\r\n      const picture = formData.get(\"imageUrl\") as string;\r\n      const parentId = formData.get(\"parentId\");\r\n      const icId = formData.get(\"icId\") as unknown as number;\r\n      const mode = formData.get(\"mode\") as string;\r\n      const requestBody = formData.get(\"requestBody\");\r\n      const sequence = formData.get(\"sequence\") as unknown as number;\r\n\r\n\r\n\r\n\r\n\r\n      let parsedParentCat = [];\r\n      if (typeof parentId === \"string\" && parentId.trim() !== \"\") {\r\n            try {\r\n                  parsedParentCat = JSON.parse(parentId);\r\n                  console.log(parsedParentCat, \"9090999000\")\r\n            } catch (error) {\r\n                  console.error(\"Error parsing parentId:\", error);\r\n                  parsedParentCat = []; // Fallback to an empty array\r\n            }\r\n      } else {\r\n            console.warn(\"parentId is not a valid JSON string or is empty.\");\r\n      }\r\n\r\n\r\n      if (intent === \"edit\") {\r\n            let parsedRequestBody: any;\r\n\r\n            if (requestBody && typeof requestBody === \"string\") {\r\n                  try {\r\n                        parsedRequestBody = JSON.parse(requestBody) as ItemsList;\r\n\r\n                  } catch (error) {\r\n                        console.error(\"Error parsing requestBody:\", error);\r\n                        throw json({ error: \"Invalid requestBody format\" }, { status: 400 });\r\n                  }\r\n            }\r\n            const itemId = formData.get(\"itemId\");\r\n            if (!itemId) {\r\n                  throw json({ error: \"Item ID is required for editing\" }, { status: 400 });\r\n            }\r\n            const response = await updateMasterItem(Number(itemId), parsedRequestBody, request);\r\n\r\n            return withResponse({ success: true, intent }, response.headers);\r\n      }\r\n\r\n\r\n\r\n      if (formData.get(\"_action\") === \"uploadImage\") {\r\n            try {\r\n                  const file = formData.get(\"file\");\r\n                  console.log(\"Received file:\", {\r\n                        type: file?.constructor.name,\r\n                        isBlob: file instanceof Blob,\r\n                        size: file instanceof Blob ? file.size : 'N/A',\r\n                        contentType: file instanceof Blob ? file.type : 'N/A'\r\n                  });\r\n\r\n                  if (!file || !(file instanceof Blob)) {\r\n                        return json({ success: false, error: \"No file provided\" }, { status: 400 });\r\n                  }\r\n\r\n                  // Validate file size\r\n                  const MAX_FILE_SIZE = 500 * 1024 * 1024; // 5MB\r\n                  if (file.size > MAX_FILE_SIZE) {\r\n                        return json({\r\n                              success: false,\r\n                              error: \"File size exceeds 5MB limit\"\r\n                        }, { status: 400 });\r\n                  }\r\n\r\n                  // Read file as buffer\r\n                  const arrayBuffer = await file.arrayBuffer();\r\n                  const buffer = Buffer.from(arrayBuffer);\r\n\r\n                  const fileUrl = await s3Service.uploadFile({\r\n                        file: buffer,\r\n                        fileName: (file as File).name || 'image.jpg',\r\n                        contentType: file.type || 'image/jpeg',\r\n                  });\r\n\r\n                  return json({ success: true, fileUrl });\r\n            } catch (error) {\r\n                  console.error(\"File upload error:\", error);\r\n                  if (error instanceof Error) {\r\n                        return json({\r\n                              success: false,\r\n                              error: error.message || \"Failed to upload file\"\r\n                        }, { status: 500 });\r\n                  }\r\n                  return json({\r\n                        success: false,\r\n                        error: \"An unexpected error occurred while uploading the file\"\r\n                  }, { status: 500 });\r\n            }\r\n      }\r\n      else if (intent === \"_createCategory\") {\r\n            try {\r\n                  const response = await UpdateMasterItemCategory(categoryName, categoryLevel, sequence, picture, parsedParentCat, request, mode, icId);\r\n                  return withResponse({ data: response.data }, response.headers);\r\n\r\n            } catch (error) {\r\n                  return json({ error: \"Something went wrong\" }, { status: 500 });\r\n            }\r\n      }\r\n\r\n      return json({ error: \"No valid action found\" }, { status: 400 });\r\n});\r\n\r\nexport default function SelectedCategory() {\r\n      const navigate = useNavigate()\r\n      const { categoryList, items, selectedId, name, categoryDetail, parentCategory } = useLoaderData<LoaderData>()\r\n      const [activeTab, setActiveTab] = useState(categoryDetail.level === 1 ? 'Items' : \"parentCategories\")\r\n      const [searchTerm, setSearchTerm] = useState('')\r\n      const [currentPage, setCurrentPage] = useState(0)\r\n      const fetcher = useFetcher()\r\n      const debouncedSearchTerm = useDebounce(searchTerm, 500);\r\n\r\n      useEffect(() => {\r\n            if (debouncedSearchTerm.length >= 3) {\r\n                  navigate(\r\n                        `/home/<USER>\n                  );\r\n            } else if (debouncedSearchTerm === \"\") {\r\n                  // Clear searchTerm and reset to base URL to show all data\r\n                  navigate(\r\n                        `/home/<USER>\n                  );\r\n            }\r\n      }, [debouncedSearchTerm, navigate, selectedId, name, activeTab, currentPage]);\r\n\r\n      const handleTabChange = (newTab: string) => {\r\n            setActiveTab(newTab);\r\n            navigate(`/home/<USER>\n      };\r\n      const handlePageChange = (newPage: number) => {\r\n            setCurrentPage(newPage)\r\n            navigate(`/home/<USER>\n      };\r\n\r\n\r\n\r\n      const handleSubmit = (selectedLevel: number, categoryName: string, sequence: number, uploadedImageUrl: string, parentId: CategoryItem[], closeDialog: () => void, mode?: string, icId?: number) => {\r\n            const formData = new FormData();\r\n            formData.append(\"categoryName\", categoryName)\r\n            formData.append(\"categoryLevel\", selectedLevel.toString())\r\n            formData.append(\"sequence\", sequence.toString())\r\n\r\n            formData.append(\"imageUrl\", uploadedImageUrl)\r\n            formData.append(\"parentId\", JSON.stringify(parentId))\r\n            formData.append(\"icId\", icId as unknown as string);\r\n            formData.append(\"mode\", mode as string);\r\n            formData.append(\"_intent\", \"_createCategory\")\r\n            fetcher.submit(formData, { method: \"POST\" })\r\n            closeDialog()\r\n      }\r\n\r\n\r\n      const handleEdit = (itemId: number, parentId: CategoryItem[], requestBody: ItemsList, closeDialog: () => void) => {\r\n            const formData = new FormData();\r\n\r\n            formData.append(\"parentId\", JSON.stringify(parentId))\r\n            formData.append(\"requestBody\", JSON.stringify(requestBody))\r\n\r\n            formData.append(\"itemId\", itemId as unknown as string);\r\n            formData.append(\"_intent\", \"edit\")\r\n            fetcher.submit(formData, { method: \"PUT\" })\r\n            closeDialog()\r\n      }\r\n      return (\r\n            <div className=\"container mx-auto p-6\">\r\n                  <div className=\"flex items-center gap-2 mb-6\">\r\n                        <span className=\"font-semibold cursor-pointer\" onClick={() => navigate(\"/home/<USER>\")}> Categories\r\n                        </span>\r\n                        <ChevronRight />\r\n                        <span className=\"font-semibold\">{name}</span>\r\n                  </div>\r\n                  <Card>\r\n                        <CardContent className=\"flex flex-col md:flex-row justify-between my-3 gap-4\">\r\n\r\n                              <div className=\"flex flex-col md:flex-row gap-5\">\r\n                                    <img\r\n                                          src={categoryDetail?.picture}\r\n                                          alt=\"ItemImage\"\r\n                                          className=\"h-10 w-10 self-center md:self-auto mt-4\"\r\n                                    />\r\n                                    <div>\r\n                                          {categoryDetail?.name && <p className=\" text-md flex gap-1\">\r\n                                                Cat.Name: <p className=\"font-bold text-md\">{categoryDetail?.name}</p>\r\n                                          </p>}\r\n                                          {categoryDetail?.level && <p className=\"text-md flex gap-1\">\r\n                                                Cat.Level: <p className=\"font-bold text-md\">{categoryDetail?.level === 1 ? \"L1\" : categoryDetail.level === 2 ? \"L2\" : categoryDetail.level === 3 ? \"L3\" : \"-\"}</p>\r\n\r\n                                          </p>}\r\n                                          {categoryDetail?.parentCategories?.length >= 0 && <p className=\"text-md flex gap-1\">\r\n                                                Parent Categories:\r\n                                                <div className=\"flex flex-col gap-1\">\r\n                                                      {categoryDetail?.parentCategories?.length > 0 ? (\r\n                                                            categoryDetail.parentCategories.map((parent, index) => (\r\n                                                                  <span key={index} className=\"font-bold text-md\"> {index + 1}.{parent.name}</span>\r\n                                                            ))\r\n                                                      ) : (\r\n                                                            \"-\"\r\n                                                      )}\r\n                                                </div>\r\n                                          </p>}\r\n                                    </div>\r\n                              </div>\r\n                              <div className=\"flex flex-col gap-3 items-start md:items-end mr-0 md:mr-20\">\r\n                                    <p className=\"font-semibold text-md flex gap-2 items-center\">\r\n\r\n                                          <AddMasterCategory buttonName=\"Edit\" categoryDetails={categoryDetail}\r\n                                                handleSubmit={handleSubmit} mode=\"Edit\" />\r\n                                    </p>\r\n                                    {categoryDetail?.totalItems > 0 && <p className=\"text-md flex gap-1\">\r\n                                          Total Items:<p className=\"font-semibold text-md\">{categoryDetail?.totalItems} </p>\r\n\r\n                                    </p>}\r\n                                    {/* <p className=\"text-md flex gap-1\">\r\n                                          Total Sellers:<p className=\"font-semibold text-md\">{data} </p>\r\n                                    </p> */}\r\n                              </div>\r\n                        </CardContent>\r\n                  </Card>\r\n\r\n                  <Tabs value={activeTab} onValueChange={handleTabChange} className=\"my-5\">\r\n                        <div className=\"flex justify-between\">\r\n                              <TabsList>\r\n                                    {categoryDetail.level === 1 && <TabsTrigger value=\"Items\">Items</TabsTrigger>}\r\n                                    <TabsTrigger value=\"parentCategories\">Parent Categories</TabsTrigger>\r\n                                    <TabsTrigger value=\"childCategories\">Child Category</TabsTrigger>\r\n\r\n\r\n                              </TabsList>\r\n                              {activeTab !== \"parentCategories\" && <div className=\"flex justify-center gap-10\">\r\n                                    <Input\r\n                                          placeholder=\"Search \"\r\n                                          value={searchTerm}\r\n                                          onChange={(e) => setSearchTerm(e.target.value)}\r\n\r\n                                    />\r\n\r\n                              </div>}\r\n                        </div>\r\n                        <TabsContent value=\"parentCategories\">\r\n                              <Table>\r\n                                    <TableHeader>\r\n                                          <TableRow>\r\n                                                <TableHead className=\"font-bold\" >Category Id</TableHead>\r\n                                                <TableHead className=\"cursor-pointer font-bold \" >Category Name</TableHead>\r\n                                                <TableHead className=\"font-bold \" >Category Level</TableHead>\r\n                                                <TableHead className=\"font-bold \" >Parent Category</TableHead>\r\n                                                <TableHead className=\"font-bold \" >Actions</TableHead>\r\n                                          </TableRow>\r\n                                    </TableHeader>\r\n                                    <TableBody>\r\n                                          {parentCategory?.length === 0 ? (\r\n                                                <TableRow>\r\n                                                      <TableCell className=\"py-4 text-center align-middle\" colSpan={100}>\r\n                                                            No Categories Found\r\n                                                      </TableCell>\r\n                                                </TableRow>\r\n                                          ) : (\r\n                                                parentCategory?.map((y: MasterItemCategories) =>\r\n\r\n                                                (\r\n\r\n\r\n                                                      <TableRow key={y.id}>\r\n                                                            <TableCell>{y?.id}</TableCell>\r\n                                                            <TableCell className=\"flex gap-2\">\r\n                                                                  <img src={y?.picture} alt=\"\" className=\"w-12 h-12 object-cover\" />\r\n                                                                  {y?.name}\r\n                                                            </TableCell>\r\n                                                            <TableCell>\r\n                                                                  {y?.level === 1 ? \"L1\" : y?.level === 2 ? \"L2\" : y?.level === 3 ? \"L3\" : \"-\"}\r\n                                                            </TableCell>\r\n                                                            <TableCell>\r\n                                                                  <div className=\"flex flex-col gap-1\">\r\n                                                                        {y?.parentCategories?.length > 0\r\n                                                                              ? y?.parentCategories.map((parent) => (\r\n                                                                                    <span key={parent.id}>{parent.name}</span>\r\n                                                                              ))\r\n                                                                              : \"-\"}\r\n                                                                  </div>\r\n                                                            </TableCell>\r\n                                                            <TableCell>\r\n                                                                  <AddMasterCategory\r\n                                                                        buttonName=\"Edit\"\r\n                                                                        categoryDetails={y}\r\n                                                                        handleSubmit={handleSubmit}\r\n                                                                        mode=\"Edit\"\r\n                                                                  />\r\n                                                            </TableCell>\r\n                                                      </TableRow>\r\n                                                ))\r\n\r\n                                          )}\r\n                                    </TableBody>\r\n                              </Table>\r\n                        </TabsContent>\r\n                        <TabsContent value=\"childCategories\">\r\n                              <Table>\r\n                                    <TableHeader>\r\n                                          <TableRow>\r\n                                                <TableHead className=\"font-bold\" >Category Id</TableHead>\r\n                                                <TableHead className=\"cursor-pointer font-bold \" >Category Name</TableHead>\r\n                                                <TableHead className=\"font-bold \" >Category Level</TableHead>\r\n                                                <TableHead className=\"font-bold \" >Parent Category</TableHead>\r\n                                                <TableHead className=\"font-bold \" >Actions</TableHead>\r\n                                          </TableRow>\r\n                                    </TableHeader>\r\n                                    <TableBody>\r\n                                          {categoryList?.length === 0 ?\r\n                                                <TableRow>\r\n                                                      <TableCell className=\"py-4 text-center align-middle\" colSpan={100}>\r\n                                                            No Categories Found\r\n                                                      </TableCell>\r\n                                                </TableRow>\r\n\r\n                                                : categoryList?.map((x: MasterItemCategories) => {\r\n                                                      return (\r\n                                                            <TableRow key={x.id}>\r\n                                                                  <TableCell>{x?.id}</TableCell>\r\n                                                                  <TableCell className=\"flex gap-2\"> <img src={x?.picture} alt=\"\" className=\"w-12 h-12 object-cover\" /> {x?.name}</TableCell>\r\n                                                                  <TableCell>   {x?.level === 1 ? \"L1\" : x?.level === 2 ? \"L2\" : x?.level === 3 ? \"L3\" : \"-\"}\r\n                                                                  </TableCell>\r\n                                                                  <TableCell>\r\n                                                                        <div className=\" flex flex-col gap-1\">{x?.parentCategories?.length > 0\r\n                                                                              ? x?.parentCategories.map((parent) => <span >{parent.name}</span>)\r\n                                                                              : \"-\"}\r\n                                                                        </div>\r\n\r\n                                                                  </TableCell>\r\n                                                                  <TableCell><AddMasterCategory buttonName=\"Edit\" categoryDetails={x}\r\n                                                                        handleSubmit={handleSubmit} mode=\"Edit\" /></TableCell>\r\n\r\n                                                            </TableRow>\r\n                                                      )\r\n                                                })\r\n\r\n\r\n                                          }\r\n\r\n                                    </TableBody>\r\n                              </Table>\r\n                        </TabsContent>\r\n                        <TabsContent value=\"Items\">\r\n                              <Table>\r\n                                    <TableHeader>\r\n                                          <TableRow>\r\n                                                <TableHead className=\"font-bold\">Item Id</TableHead>\r\n                                                <TableHead className=\"font-bold\">Item Name</TableHead>\r\n                                                <TableHead className=\"font-bold\">Item Details</TableHead>\r\n                                                {/* <TableHead className=\"font-bold \"><p>Number of Orders</p>\r\n                                                      <p>(Number of Sellers)</p></TableHead> */}\r\n                                                <TableHead className=\"font-bold\">Status</TableHead>\r\n                                                <TableHead className=\"font-bold\">Categories</TableHead>\r\n\r\n                                          </TableRow>\r\n                                    </TableHeader>\r\n                                    <TableBody>\r\n                                          {items.length === 0 ?\r\n                                                <TableRow>\r\n                                                      <TableCell className=\"py-4 text-center align-middle\" colSpan={100}>\r\n                                                            No Items Found\r\n                                                      </TableCell>\r\n                                                </TableRow>\r\n                                                :\r\n                                                items?.map((x: ItemsList) => {\r\n                                                      return (\r\n                                                            <TableRow key={x.id}>\r\n                                                                  <TableCell>{x?.id}</TableCell>\r\n                                                                  <TableCell className=\"items-center\">\r\n                                                                        <div className=\"flex items-center gap-1\">\r\n\r\n                                                                              <img\r\n                                                                                    src={\r\n                                                                                          x?.picture && x.picture.includes(\",\")\r\n                                                                                                ? x.picture.split(\",\")[0] // Use the first image in the split array\r\n                                                                                                : x.picture\r\n                                                                                    }\r\n                                                                                    alt=\"\"\r\n                                                                                    className=\"w-12 h-12 object-cover\"\r\n                                                                              />  {x?.name}\r\n                                                                        </div>\r\n                                                                        <div className=\"text-xs mt-2\">\r\n                                                                              <span className=\"font-medium\">Packaging:</span> {x.packaging || '-'}\r\n                                                                        </div>\r\n                                                                  </TableCell>\r\n\r\n                                                                  <TableCell>\r\n                                                                        <div className=\"space-y-1\">\r\n                                                                              <div className=\"text-xs\">\r\n                                                                                    <span className=\"font-medium\">Min Qty:</span> {x.minimumOrderQty || '-'}\r\n                                                                              </div>\r\n                                                                              <div className=\"text-xs\">\r\n                                                                                    <span className=\"font-medium\">Increment Qty:</span> {x.incrementOrderQty || '-'}\r\n                                                                              </div>\r\n                                                                              <div className=\"text-xs\">\r\n                                                                                    <span className=\"font-medium\">MRP:</span> {x.mrp || '-'}\r\n                                                                              </div>\r\n                                                                              <div className=\"text-xs\">\r\n                                                                                    <span className=\"font-medium\">Source:</span> {x.source || '-'}\r\n                                                                              </div>\r\n                                                                        </div>\r\n\r\n                                                                  </TableCell>\r\n                                                                  <TableCell ><span className={`px-2 py-1 rounded-full text-xs ${x.disabled ? 'bg-red-100 text-red-800' : 'bg-green-200 text-green-800'}`}>{x?.disabled === true ? \"Disabled\" : \"Active\"}</span>\r\n                                                                  </TableCell>\r\n\r\n                                                                  <TableCell className=\"flex gap-2 align-middle\" >  <div className=\"flex flex-col \">{x?.categories?.length > 0\r\n                                                                        ? x?.categories.map((parent) => <span >{parent.name}</span>)\r\n                                                                        : \"-\"}\r\n                                                                  </div>\r\n                                                                        <AddCategoryItem buttonName={\"Edit\"} handleSubmit={handleEdit\r\n                                                                        } itemDetails={x} categoryDetails={x.categories} level={categoryDetail.level} mode={\"Edit\"} />\r\n\r\n                                                                  </TableCell>\r\n\r\n                                                            </TableRow>\r\n                                                      )\r\n                                                })}\r\n\r\n                                    </TableBody>\r\n\r\n                              </Table>\r\n                        </TabsContent>\r\n                        {activeTab !== \"parentCategories\" && <div className=\"flex justify-between items-center mt-6 overflow-hidden \">\r\n                              <Pagination>\r\n                                    <PaginationContent>\r\n                                          {currentPage > 0 && (\r\n                                                <PaginationItem>\r\n                                                      <PaginationPrevious onClick={() => handlePageChange(currentPage - 1)} className=\"cursor-pointer\" />\r\n                                                </PaginationItem>\r\n                                          )}\r\n                                          <PaginationItem>\r\n                                                <PaginationLink className=\"cursor-pointer\">{currentPage + 1}</PaginationLink>\r\n                                          </PaginationItem>\r\n                                          <PaginationItem>\r\n                                                <PaginationNext onClick={() => handlePageChange(currentPage + 1)} className=\"cursor-pointer\" />\r\n                                          </PaginationItem>\r\n                                    </PaginationContent>\r\n                              </Pagination>\r\n                        </div>}\r\n                  </Tabs>\r\n            </div>\r\n      )\r\n}\r\n"], "names": ["useState", "jsx", "jsxs", "SelectedCategory", "navigate", "useNavigate", "categoryList", "items", "selectedId", "name", "categoryDetail", "parentCategory", "useLoaderData", "activeTab", "setActiveTab", "level", "searchTerm", "setSearchTerm", "currentPage", "setCurrentPage", "fetcher", "useFetcher", "debouncedSearchTerm", "useDebounce", "useEffect", "length", "toLocaleLowerCase", "handleTabChange", "newTab", "handlePageChange", "newPage", "handleSubmit", "selectedLevel", "categoryName", "sequence", "uploadedImageUrl", "parentId", "closeDialog", "mode", "icId", "formData", "FormData", "append", "toString", "JSON", "stringify", "submit", "method", "handleEdit", "itemId", "requestBody", "className", "children", "onClick", "ChevronRight", "Card", "<PERSON><PERSON><PERSON><PERSON>", "src", "picture", "alt", "parentCategories", "map", "parent", "index", "AddMasterCategory", "buttonName", "categoryDetails", "totalItems", "Tabs", "value", "onValueChange", "TabsList", "TabsTrigger", "Input", "placeholder", "onChange", "e", "target", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Table", "TableHeader", "TableRow", "TableHead", "TableBody", "TableCell", "colSpan", "y", "id", "x", "includes", "split", "packaging", "minimumOrderQty", "incrementOrderQty", "mrp", "source", "disabled", "categories", "AddCategoryItem", "itemDetails", "Pagination", "Pa<PERSON><PERSON><PERSON><PERSON><PERSON>", "PaginationItem", "PaginationPrevious", "PaginationLink", "PaginationNext"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmBA,SAAwB,gBAAgB;AAAA,EAClC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAEN,GAAyB;AAEnB,QAAM,CAAC,YAAY,aAAa,IAAIA,aAAAA,SAAS,KAAK;AAE5C,QAAA,0BAA0B,CAAC,qBAA6D;AACjF,WAAA,iBAAiB,IAAI,CAAC,cAAc;AAAA,MACrC,OAAO,SAAS,GAAG,SAAS;AAAA,MAC5B,OAAO,SAAS;AAAA,MAChB,WAAW,SAAS;AAAA,IAAA,EACxB;AAAA,EACR;AAEM,QAAA,CAAC,iBAAiB,kBAAkB,IAAIA,aAAA;AAAA,IAAyB,MACjE,SAAS,UAAU,kBAAkB,wBAAwB,eAAe,IAAI,CAAA;AAAA,EACtF;AAEA,WAAS,cAAc;AACjB,kBAAc,KAAK;AAAA,EAAA;AAGzB,QAAM,kBAAkB,MAAM;AAExB,UAAM,wBAAwB,gBAAgB,IAAI,CAAC,cAAc;AAAA,MAC3D,IAAI,SAAS;AAAA,MACb,UAAU;AAAA,MACV,MAAM,SAAS;AAAA,MACf,UAAS,2CAAa,YAAW;AAAA,MACjC,UAAU;AAAA,MACV,WAAW;AAAA,MACX,OAAO,SAAS;AAAA,MAChB,YAAY;AAAA,MACZ,kBAAkB,CAAA;AAAA,IAAC,EACvB;AACF,UAAM,cAAc;AAAA,MACd,KAAI,2CAAa,OAAM;AAAA,MACvB,UAAU;AAAA,MACV,cAAa,2CAAa,gBAAe;AAAA,MACzC,OAAM,2CAAa,SAAQ;AAAA,MAC3B,UAAS,2CAAa,YAAW;AAAA,MACjC,gBAAe,2CAAa,kBAAiB;AAAA,MAC7C,eAAc,2CAAa,iBAAgB;AAAA,MAC3C,cAAa,2CAAa,gBAAe;AAAA,MACzC,kBAAiB,2CAAa,oBAAmB;AAAA,MACjD,cAAa,2CAAa,gBAAe;AAAA,MACzC,eAAc,2CAAa,iBAAgB;AAAA,MAC3C,iBAAgB,2CAAa,mBAAkB;AAAA,MAC/C,gBAAe,2CAAa,kBAAiB;AAAA,MAC7C,eAAc,2CAAa,iBAAgB;AAAA,MAC3C,sBAAqB,2CAAa,wBAAuB;AAAA,MACzD,aAAY,2CAAa,eAAc;AAAA,MACvC,UAAS,2CAAa,YAAW;AAAA,MACjC,SAAQ,2CAAa,WAAU;AAAA,MAC/B,YAAW,2CAAa,cAAa;AAAA,MACrC,YAAW,2CAAa,cAAa;AAAA,MACrC,YAAW,2CAAa,cAAa;AAAA,MACrC,YAAW,2CAAa,cAAa;AAAA,MACrC,MAAK,2CAAa,QAAO;AAAA,MACzB,WAAU,2CAAa,aAAY;AAAA,MACnC,MAAK,2CAAa,QAAO;AAAA,MACzB,MAAK,2CAAa,QAAO;AAAA,MACzB,UAAS,2CAAa,YAAW;AAAA,MACjC,WAAU,2CAAa,aAAY;AAAA,MACnC,YAAW,2CAAa,cAAa;AAAA,MAErC,YAAY;AAAA,IAClB;AAKA,kBAAa,2CAAa,OAAM,GAAG,iBAAiB,aAAa,WAAW;AAAA,EAElF;AAKM,SAAAC,kCAAA,IAAC,OAAI,EAAA,WAAU,wBACT,UAAAC,uCAAC,UAAO,MAAM,YAAY,cAAc,CAAC,WAAW;AAC9C,kBAAc,MAAM;AACpB,QAAI,CAAC,QAAQ;AACP,oBAAc,KAAK;AAAA,IAAA;AAAA,EAGzB,GAAA,UAAA;AAAA,IAAAD,kCAAA,IAAC,eAAc,EAAA,SAAO,MAAC,WAAU,SAC3B,UAAAA,kCAAA;AAAA,MAAC;AAAA,MAAA;AAAA,QACK,MAAM;AAAA,QACN,WAAU;AAAA,MAAA;AAAA,IAAA,GAEtB;AAAA,IACAC,kCAAA;AAAA,MAAC;AAAA,MAAA;AAAA,QACK,WAAU;AAAA,QAEV,UAAA;AAAA,UAACA,kCAAAA,KAAA,OAAA,EAAI,WAAU,cACT,UAAA;AAAA,YAACD,kCAAAA,IAAA,OAAA,EAAI,WAAU,aACT,UAAAA,kCAAA,IAAC,QAAG,WAAU,qDAAoD,sCAElE,EACN,CAAA;AAAA,kDAEC,OAAI,EAAA,WAAU,yCACT,UAACC,kCAAA,KAAA,OAAA,EAAI,WAAU,aACT,UAAA;AAAA,cAAAD,kCAAA;AAAA,gBAAC;AAAA,gBAAA;AAAA,kBACK,MACM,2CAAa,YAAW,YAAY,QAAQ,SAAS,GAAG,IAChD,YAAY,QAAQ,MAAM,GAAG,EAAE,CAAC,IAChC,2CAAa;AAAA,kBAE3B,KAAI;AAAA,kBACJ,WAAU;AAAA,gBAAA;AAAA,cAChB;AAAA,cACAC,kCAAAA,KAAC,KAAE,EAAA,WAAU,qBAAoB,UAAA;AAAA,gBAAA;AAAA,gBAAa,2CAAa;AAAA,gBAAK;AAAA,cAAA,EAAE,CAAA;AAAA,YAAA,EAAA,CACxE,EACN,CAAA;AAAA,UAAA,GACN;AAAA,UACAD,kCAAAA,IAAC,OAAI,EAAA,WAAU,iBACT,UAAAA,kCAAA;AAAA,YAAC;AAAA,YAAA;AAAA,cACK,OAAM;AAAA,cACN,QAAO;AAAA,cAEP,oBAAoB,mBAAmB,CAAC;AAAA,cACxC,eAAe,CAAC,YAAY,iBAAiB;AAEvC,mCAAmB,CAAC,wBAAwB;AAAA,kBACtC,GAAI,uBAAuB,CAAC;AAAA,kBAC5B,EAAE,WAAW,YAAY,OAAO,cAAc,OAAO,GAAG;AAAA,gBAAA,CAC7D;AAAA,cACP;AAAA,cACA,kBAAkB,CAAC,eAAe;AAE5B;AAAA,kBAAmB,CAAC,yBACb,uBAAuB,CAAA,GAAI;AAAA,oBACtB,CAAC,QAAQ,IAAI,cAAc;AAAA,kBAAA;AAAA,gBAEvC;AAAA,cACN;AAAA,cACA,UAAU;AAAA,cACV,OAAO;AAAA,YAAA;AAAA,UAAA,GAGnB;AAAA,UACAA,kCAAAA,IAAC,OAAI,EAAA,WAAU,oDACT,UAAAA,kCAAA;AAAA,YAAC;AAAA,YAAA;AAAA,cACK,MAAK;AAAA,cACL,WAAU;AAAA,cAEV,SAAS,MAAM,gBAAgB;AAAA,cAAK,UAAA;AAAA,YAAA;AAAA,UAAA,EAIhD,CAAA;AAAA,QAAA;AAAA,MAAA;AAAA,IAAA;AAAA,EACN,EAAA,CACN,EAEN,CAAA;AAEZ;ACmBA,SAAwBE,mBAAmB;;AACrC,QAAMC,WAAWC,YAAY;AACvB,QAAA;AAAA,IAAEC;AAAAA,IAAcC;AAAAA,IAAOC;AAAAA,IAAYC;AAAAA,IAAMC;AAAAA,IAAgBC;AAAAA,MAAmBC,cAA0B;AACtG,QAAA,CAACC,WAAWC,YAAY,IAAId,sBAASU,eAAeK,UAAU,IAAI,UAAU,kBAAkB;AACpG,QAAM,CAACC,YAAYC,aAAa,IAAIjB,aAAAA,SAAS,EAAE;AAC/C,QAAM,CAACkB,aAAaC,cAAc,IAAInB,aAAAA,SAAS,CAAC;AAChD,QAAMoB,UAAUC,WAAW;AACrB,QAAAC,sBAAsBC,YAAYP,YAAY,GAAG;AAEvDQ,eAAAA,UAAU,MAAM;AACN,QAAAF,oBAAoBG,UAAU,GAAG;AAC/BrB,eACM,uCAAuCI,UAAU,SAASC,IAAI,cAAcI,SAAS,SAASK,WAAW,eAAeI,oBAAoBI,kBAAA,CAAmB,EACrK;AAAA,IACN,WAAWJ,wBAAwB,IAAI;AAEjClB,eACM,uCAAuCI,UAAU,SAASC,IAAI,cAAcI,SAAS,SAASK,WAAW,EAC/G;AAAA,IACN;AAAA,EACN,GAAG,CAACI,qBAAqBlB,UAAUI,YAAYC,MAAMI,WAAWK,WAAW,CAAC;AAEtE,QAAAS,kBAAmBC,YAAmB;AACtCd,iBAAac,MAAM;AACnBxB,aAAS,uCAAuCI,UAAU,SAASC,IAAI,cAAcmB,MAAM,SAAS,CAAC,eAAeZ,WAAWU,kBAAkB,CAAC,EAAE;AAAA,EAC1J;AACM,QAAAG,mBAAoBC,aAAoB;AACxCX,mBAAeW,OAAO;AACtB1B,aAAS,uCAAuCI,UAAU,SAASC,IAAI,cAAcI,SAAS,SAASiB,OAAO,eAAed,WAAWU,kBAAkB,CAAC,EAAE;AAAA,EACnK;AAIM,QAAAK,eAAeA,CAACC,eAAuBC,cAAsBC,UAAkBC,kBAA0BC,UAA0BC,aAAyBC,MAAeC,SAAkB;AACvL,UAAAC,WAAW,IAAIC,SAAS;AACrBD,aAAAE,OAAO,gBAAgBT,YAAY;AAC5CO,aAASE,OAAO,iBAAiBV,cAAcW,SAAA,CAAU;AACzDH,aAASE,OAAO,YAAYR,SAASS,SAAA,CAAU;AAEtCH,aAAAE,OAAO,YAAYP,gBAAgB;AAC5CK,aAASE,OAAO,YAAYE,KAAKC,UAAUT,QAAQ,CAAC;AAC3CI,aAAAE,OAAO,QAAQH,IAAyB;AACxCC,aAAAE,OAAO,QAAQJ,IAAc;AAC7BE,aAAAE,OAAO,WAAW,iBAAiB;AAC5CtB,YAAQ0B,OAAON,UAAU;AAAA,MAAEO,QAAQ;AAAA,IAAO,CAAC;AAC/BV,gBAAA;AAAA,EAClB;AAGA,QAAMW,aAAaA,CAACC,QAAgBb,UAA0Bc,aAAwBb,gBAA4B;AACtG,UAAAG,WAAW,IAAIC,SAAS;AAE9BD,aAASE,OAAO,YAAYE,KAAKC,UAAUT,QAAQ,CAAC;AACpDI,aAASE,OAAO,eAAeE,KAAKC,UAAUK,WAAW,CAAC;AAEjDV,aAAAE,OAAO,UAAUO,MAA2B;AAC5CT,aAAAE,OAAO,WAAW,MAAM;AACjCtB,YAAQ0B,OAAON,UAAU;AAAA,MAAEO,QAAQ;AAAA,IAAM,CAAC;AAC9BV,gBAAA;AAAA,EAClB;AAEM,SAAAnC,kCAAAA,KAAC,OAAI;AAAA,IAAAiD,WAAU;AAAA,IACTC,UAAA,CAAClD,kCAAA,KAAA,OAAA;AAAA,MAAIiD,WAAU;AAAA,MACTC,UAAA,CAACnD,kCAAA,IAAA,QAAA;AAAA,QAAKkD,WAAU;AAAA,QAA+BE,SAASA,MAAMjD,SAAS,0BAA0B;AAAA,QAAGgD,UACpG;AAAA,MAAA,CAAA,yCACCE,cAAa,CAAA,CAAA,GACbrD,kCAAAA,IAAA,QAAA;AAAA,QAAKkD,WAAU;AAAA,QAAiBC,UAAK3C;AAAAA,MAAA,CAAA,CAAA;AAAA,IAC5C,CAAA,GACCR,kCAAA,IAAAsD,MAAA;AAAA,MACKH,UAAClD,kCAAA,KAAAsD,aAAA;AAAA,QAAYL,WAAU;AAAA,QAEjBC,UAAA,CAAClD,kCAAA,KAAA,OAAA;AAAA,UAAIiD,WAAU;AAAA,UACTC,UAAA,CAAAnD,kCAAA,IAAC,OAAA;AAAA,YACKwD,KAAK/C,iDAAgBgD;AAAAA,YACrBC,KAAI;AAAA,YACJR,WAAU;AAAA,UAAA,CAChB,0CACC,OACM;AAAA,YAAAC,UAAA,EAAA1C,iDAAgBD,SAAQP,kCAAAA,KAAC,KAAE;AAAA,cAAAiD,WAAU;AAAA,cAAsBC,UAAA,CAAA,cAC3CnD,kCAAA,IAAA,KAAA;AAAA,gBAAEkD,WAAU;AAAA,gBAAqBC,2DAAgB3C;AAAAA,cAAK,CAAA,CAAA;AAAA,YACvE,CAAA,IACCC,iDAAgBK,UAAUb,kCAAAA,KAAA,KAAA;AAAA,cAAEiD,WAAU;AAAA,cAAqBC,UAAA,CAAA,qDAC1C,KAAE;AAAA,gBAAAD,WAAU;AAAA,gBAAqBC,WAAA1C,iDAAgBK,WAAU,IAAI,OAAOL,eAAeK,UAAU,IAAI,OAAOL,eAAeK,UAAU,IAAI,OAAO;AAAA,cAAI,CAAA,CAAA;AAAA,YAEpK,CAAA,KACCL,sDAAgBkD,qBAAhBlD,mBAAkCe,WAAU,KAAMvB,kCAAA,KAAA,KAAA;AAAA,cAAEiD,WAAU;AAAA,cAAqBC,UAAA,CAAA,4DAE7E,OAAI;AAAA,gBAAAD,WAAU;AAAA,gBACRC,YAAA1C,sDAAgBkD,qBAAhBlD,mBAAkCe,UAAS,IACtCf,eAAekD,iBAAiBC,IAAI,CAACC,QAAQC,UACtC7D,kCAAAA,KAAA,QAAA;AAAA,kBAAiBiD,WAAU;AAAA,kBAAoBC,UAAA,CAAA,KAAEW,QAAQ,GAAE,KAAED,OAAOrD,IAAA;AAAA,gBAA1D,GAAAsD,KAA+D,CAC/E,IAED;AAAA,cAEZ,CAAA,CAAA;AAAA,YACN,CAAA,CAAA;AAAA,UACN,CAAA,CAAA;AAAA,QACN,CAAA,GACA7D,kCAAA,KAAC,OAAI;AAAA,UAAAiD,WAAU;AAAA,UACTC,UAAA,CAACnD,kCAAA,IAAA,KAAA;AAAA,YAAEkD,WAAU;AAAA,YAEPC,UAAAnD,kCAAA,IAAC+D,mBAAA;AAAA,cAAkBC,YAAW;AAAA,cAAOC,iBAAiBxD;AAAAA,cAChDqB;AAAAA,cAA4BO,MAAK;AAAA,YAAO,CAAA;AAAA,UACpD,CAAA,IACC5B,iDAAgByD,cAAa,KAAMjE,kCAAAA,KAAA,KAAA;AAAA,YAAEiD,WAAU;AAAA,YAAqBC,UAAA,CAAA,gBACnDlD,kCAAA,KAAC,KAAE;AAAA,cAAAiD,WAAU;AAAA,cAAyBC,UAAA,CAAgB1C,iDAAAyD,YAAW,GAAA;AAAA,YAAC,CAAA,CAAA;AAAA,UAEpF,CAAA,CAAA;AAAA,QAIN,CAAA,CAAA;AAAA,MACN,CAAA;AAAA,IACN,CAAA,0CAECC,MAAK;AAAA,MAAAC,OAAOxD;AAAAA,MAAWyD,eAAe3C;AAAAA,MAAiBwB,WAAU;AAAA,MAC5DC,UAAA,CAAClD,kCAAA,KAAA,OAAA;AAAA,QAAIiD,WAAU;AAAA,QACTC,UAAA,CAAAlD,kCAAA,KAACqE,UACM;AAAA,UAAAnB,UAAA,CAAA1C,eAAeK,UAAU,KAAKd,kCAAAA,IAACuE,aAAY;AAAA,YAAAH,OAAM;AAAA,YAAQjB,UAAK;AAAA,UAAA,CAAA,GAC9DnD,kCAAA,IAAAuE,aAAA;AAAA,YAAYH,OAAM;AAAA,YAAmBjB,UAAiB;AAAA,UAAA,CAAA,GACtDnD,kCAAA,IAAAuE,aAAA;AAAA,YAAYH,OAAM;AAAA,YAAkBjB,UAAc;AAAA,UAAA,CAAA,CAAA;AAAA,QAGzD,CAAA,GACCvC,cAAc,sBAAuBZ,kCAAAA,IAAA,OAAA;AAAA,UAAIkD,WAAU;AAAA,UAC9CC,UAAAnD,kCAAA,IAACwE,OAAA;AAAA,YACKC,aAAY;AAAA,YACZL,OAAOrD;AAAAA,YACP2D,UAAWC,OAAM3D,cAAc2D,EAAEC,OAAOR,KAAK;AAAA,UAEnD,CAAA;AAAA,QAEN,CAAA,CAAA;AAAA,MACN,CAAA,GACCpE,kCAAA,IAAA6E,aAAA;AAAA,QAAYT,OAAM;AAAA,QACbjB,iDAAC2B,OACK;AAAA,UAAA3B,UAAA,CAACnD,kCAAA,IAAA+E,aAAA;AAAA,YACK5B,iDAAC6B,UACK;AAAA,cAAA7B,UAAA,CAACnD,kCAAA,IAAAiF,WAAA;AAAA,gBAAU/B,WAAU;AAAA,gBAAaC,UAAW;AAAA,cAAA,CAAA,GAC5CnD,kCAAA,IAAAiF,WAAA;AAAA,gBAAU/B,WAAU;AAAA,gBAA6BC,UAAa;AAAA,cAAA,CAAA,GAC9DnD,kCAAA,IAAAiF,WAAA;AAAA,gBAAU/B,WAAU;AAAA,gBAAcC,UAAc;AAAA,cAAA,CAAA,GAChDnD,kCAAA,IAAAiF,WAAA;AAAA,gBAAU/B,WAAU;AAAA,gBAAcC,UAAe;AAAA,cAAA,CAAA,GACjDnD,kCAAA,IAAAiF,WAAA;AAAA,gBAAU/B,WAAU;AAAA,gBAAcC,UAAO;AAAA,cAAA,CAAA,CAAA;AAAA,YAChD,CAAA;AAAA,UACN,CAAA,GACAnD,kCAAA,IAACkF;YACM/B,WAAgBzC,iDAAAc,YAAW,IACrBxB,kCAAAA,IAAAgF,UAAA;AAAA,cACK7B,gDAACgC,WAAU;AAAA,gBAAAjC,WAAU;AAAA,gBAAgCkC,SAAS;AAAA,gBAAKjC;cAEnE,CAAA;AAAA,YACN,CAAA,IAEAzC,iDAAgBkD,IAAKyB,OAKfpF;;AAAAA,uDAAAA,KAAC+E,UACK;AAAA,gBAAA7B,UAAA,CAACnD,kCAAA,IAAAmF,WAAA;AAAA,kBAAWhC,iCAAGmC;AAAAA,gBAAG,CAAA,GAClBrF,kCAAA,KAACkF,WAAU;AAAA,kBAAAjC,WAAU;AAAA,kBACfC,UAAA,CAAAnD,kCAAA,IAAC;oBAAIwD,KAAK6B,uBAAG5B;AAAAA,oBAASC,KAAI;AAAA,oBAAGR,WAAU;AAAA,kBAAyB,CAAA,GAC/DmC,uBAAG7E,IAAA;AAAA,gBACV,CAAA,GACCR,kCAAA,IAAAmF,WAAA;AAAA,kBACMhC,WAAGkC,uBAAAvE,WAAU,IAAI,QAAOuE,uBAAGvE,WAAU,IAAI,QAAOuE,uBAAGvE,WAAU,IAAI,OAAO;AAAA,gBAC/E,CAAA,GACAd,kCAAA,IAACmF,WACK;AAAA,kBAAAhC,UAAAnD,kCAAA,IAAC,OAAI;AAAA,oBAAAkD,WAAU;AAAA,oBACRC,YAAGkC,MAAAA,uBAAA1B,qBAAA0B,gBAAAA,IAAkB7D,UAAS,IACvB6D,uBAAG1B,iBAAiBC,IAAKC,YACpB7D,kCAAAA,IAAA,QAAA;AAAA,sBAAsBmD,UAAOU,OAAArD;AAAAA,oBAAA,GAAnBqD,OAAOyB,EAAiB,KAEvC;AAAA,kBACd,CAAA;AAAA,gBACN,CAAA,yCACCH,WACK;AAAA,kBAAAhC,UAAAnD,kCAAA,IAAC+D,mBAAA;AAAA,oBACKC,YAAW;AAAA,oBACXC,iBAAiBoB;AAAAA,oBACjBvD;AAAAA,oBACAO,MAAK;AAAA,kBACX,CAAA;AAAA,gBACN,CAAA,CAAA;AAAA,cAzBS,GAAAgD,EAAEC,EA0BjB;AAAA;AAAA,UAIlB,CAAA,CAAA;AAAA,QACN,CAAA;AAAA,MACN,CAAA,GACCtF,kCAAA,IAAA6E,aAAA;AAAA,QAAYT,OAAM;AAAA,QACbjB,iDAAC2B,OACK;AAAA,UAAA3B,UAAA,CAACnD,kCAAA,IAAA+E,aAAA;AAAA,YACK5B,iDAAC6B,UACK;AAAA,cAAA7B,UAAA,CAACnD,kCAAA,IAAAiF,WAAA;AAAA,gBAAU/B,WAAU;AAAA,gBAAaC,UAAW;AAAA,cAAA,CAAA,GAC5CnD,kCAAA,IAAAiF,WAAA;AAAA,gBAAU/B,WAAU;AAAA,gBAA6BC,UAAa;AAAA,cAAA,CAAA,GAC9DnD,kCAAA,IAAAiF,WAAA;AAAA,gBAAU/B,WAAU;AAAA,gBAAcC,UAAc;AAAA,cAAA,CAAA,GAChDnD,kCAAA,IAAAiF,WAAA;AAAA,gBAAU/B,WAAU;AAAA,gBAAcC,UAAe;AAAA,cAAA,CAAA,GACjDnD,kCAAA,IAAAiF,WAAA;AAAA,gBAAU/B,WAAU;AAAA,gBAAcC,UAAO;AAAA,cAAA,CAAA,CAAA;AAAA,YAChD,CAAA;AAAA,UACN,CAAA,yCACC+B,WACM;AAAA,YAAA/B,WAAA9C,6CAAcmB,YAAW,IACpBxB,kCAAAA,IAACgF;cACK7B,UAACnD,kCAAA,IAAAmF,WAAA;AAAA,gBAAUjC,WAAU;AAAA,gBAAgCkC,SAAS;AAAA,gBAAKjC,UAEnE;AAAA,cAAA,CAAA;AAAA,YAAA,CACN,IAEE9C,6CAAcuD,IAAK2B,OAA4B;;AAC3C,4DACOP,UACK;AAAA,gBAAA7B,UAAA,CAACnD,kCAAA,IAAAmF,WAAA;AAAA,kBAAWhC,iCAAGmC;AAAAA,gBAAG,CAAA,GAClBrF,kCAAA,KAACkF,WAAU;AAAA,kBAAAjC,WAAU;AAAA,kBAAaC,UAAA,CAAA,KAACnD,kCAAA,IAAC;oBAAIwD,KAAK+B,uBAAG9B;AAAAA,oBAASC,KAAI;AAAA,oBAAGR,WAAU;AAAA,kBAAyB,CAAA,GAAE,KAAEqC,uBAAG/E,IAAA;AAAA,gBAAK,CAAA,0CAC9G2E,WAAU;AAAA,kBAAAhC,UAAA,CAAA,QAAIoC,uBAAGzE,WAAU,IAAI,QAAOyE,uBAAGzE,WAAU,IAAI,QAAOyE,uBAAGzE,WAAU,IAAI,OAAO,GAAA;AAAA,gBACvF,CAAA,GACAd,kCAAA,IAACmF;kBACKhC,UAACnD,kCAAA,IAAA,OAAA;AAAA,oBAAIkD,WAAU;AAAA,oBAAwBC,YAAAoC,MAAAA,uBAAG5B,qBAAH4B,gBAAAA,IAAqB/D,UAAS,IAC7D+D,uBAAG5B,iBAAiBC,IAAKC,YAAY7D,kCAAAA,IAAA,QAAA;AAAA,sBAAOmD,iBAAO3C;AAAAA,oBAAK,CAAA,KACxD;AAAA,kBACR,CAAA;AAAA,gBAEN,CAAA,yCACC2E,WAAU;AAAA,kBAAAhC,UAAAnD,kCAAA,IAAC+D,mBAAA;AAAA,oBAAkBC,YAAW;AAAA,oBAAOC,iBAAiBsB;AAAAA,oBAC3DzD;AAAAA,oBAA4BO,MAAK;AAAA,kBAAO,CAAA;AAAA,gBAAE,CAAA,CAAA;AAAA,cAAA,GAbvCkD,EAAED,EAejB;AAAA,YAEX;AAAA,UAKb,CAAA,CAAA;AAAA,QACN,CAAA;AAAA,MACN,CAAA,GACCtF,kCAAA,IAAA6E,aAAA;AAAA,QAAYT,OAAM;AAAA,QACbjB,iDAAC2B,OACK;AAAA,UAAA3B,UAAA,CAACnD,kCAAA,IAAA+E,aAAA;AAAA,YACK5B,iDAAC6B,UACK;AAAA,cAAA7B,UAAA,CAACnD,kCAAA,IAAAiF,WAAA;AAAA,gBAAU/B,WAAU;AAAA,gBAAYC,UAAO;AAAA,cAAA,CAAA,GACvCnD,kCAAA,IAAAiF,WAAA;AAAA,gBAAU/B,WAAU;AAAA,gBAAYC,UAAS;AAAA,cAAA,CAAA,GACzCnD,kCAAA,IAAAiF,WAAA;AAAA,gBAAU/B,WAAU;AAAA,gBAAYC,UAAY;AAAA,cAAA,CAAA,GAG5CnD,kCAAA,IAAAiF,WAAA;AAAA,gBAAU/B,WAAU;AAAA,gBAAYC,UAAM;AAAA,cAAA,CAAA,GACtCnD,kCAAA,IAAAiF,WAAA;AAAA,gBAAU/B,WAAU;AAAA,gBAAYC,UAAU;AAAA,cAAA,CAAA,CAAA;AAAA,YAEjD,CAAA;AAAA,UACN,CAAA,yCACC+B,WACM;AAAA,YAAA/B,UAAA7C,MAAMkB,WAAW,IACZxB,kCAAAA,IAACgF;cACK7B,UAACnD,kCAAA,IAAAmF,WAAA;AAAA,gBAAUjC,WAAU;AAAA,gBAAgCkC,SAAS;AAAA,gBAAKjC,UAEnE;AAAA,cAAA,CAAA;AAAA,YAAA,CACN,IAEA7C,+BAAOsD,IAAK2B,OAAiB;;AACvB,4DACOP,UACK;AAAA,gBAAA7B,UAAA,CAACnD,kCAAA,IAAAmF,WAAA;AAAA,kBAAWhC,iCAAGmC;AAAAA,gBAAG,CAAA,GAClBrF,kCAAA,KAACkF,WAAU;AAAA,kBAAAjC,WAAU;AAAA,kBACfC,UAAA,CAAClD,kCAAA,KAAA,OAAA;AAAA,oBAAIiD,WAAU;AAAA,oBAETC,UAAA,CAAAnD,kCAAA,IAAC,OAAA;AAAA,sBACKwD,MACM+B,uBAAG9B,YAAW8B,EAAE9B,QAAQ+B,SAAS,GAAG,IAC5BD,EAAE9B,QAAQgC,MAAM,GAAG,EAAE,CAAC,IACtBF,EAAE9B;AAAAA,sBAEhBC,KAAI;AAAA,sBACJR,WAAU;AAAA,oBAAA,CAChB,GAAE,MAAGqC,uBAAG/E,IAAA;AAAA,kBACd,CAAA,GACAP,kCAAA,KAAC,OAAI;AAAA,oBAAAiD,WAAU;AAAA,oBACTC,UAAA,CAACnD,kCAAA,IAAA,QAAA;AAAA,sBAAKkD,WAAU;AAAA,sBAAcC,UAAU;AAAA,oBAAA,CAAA,GAAO,KAAEoC,EAAEG,aAAa,GAAA;AAAA,kBACtE,CAAA,CAAA;AAAA,gBACN,CAAA,GAEC1F,kCAAA,IAAAmF,WAAA;AAAA,kBACKhC,UAAClD,kCAAA,KAAA,OAAA;AAAA,oBAAIiD,WAAU;AAAA,oBACTC,UAAA,CAAClD,kCAAA,KAAA,OAAA;AAAA,sBAAIiD,WAAU;AAAA,sBACTC,UAAA,CAACnD,kCAAA,IAAA,QAAA;AAAA,wBAAKkD,WAAU;AAAA,wBAAcC,UAAQ;AAAA,sBAAA,CAAA,GAAO,KAAEoC,EAAEI,mBAAmB,GAAA;AAAA,oBAC1E,CAAA,GACA1F,kCAAA,KAAC,OAAI;AAAA,sBAAAiD,WAAU;AAAA,sBACTC,UAAA,CAACnD,kCAAA,IAAA,QAAA;AAAA,wBAAKkD,WAAU;AAAA,wBAAcC,UAAc;AAAA,sBAAA,CAAA,GAAO,KAAEoC,EAAEK,qBAAqB,GAAA;AAAA,oBAClF,CAAA,GACA3F,kCAAA,KAAC,OAAI;AAAA,sBAAAiD,WAAU;AAAA,sBACTC,UAAA,CAACnD,kCAAA,IAAA,QAAA;AAAA,wBAAKkD,WAAU;AAAA,wBAAcC,UAAI;AAAA,sBAAA,CAAA,GAAO,KAAEoC,EAAEM,OAAO,GAAA;AAAA,oBAC1D,CAAA,GACA5F,kCAAA,KAAC,OAAI;AAAA,sBAAAiD,WAAU;AAAA,sBACTC,UAAA,CAACnD,kCAAA,IAAA,QAAA;AAAA,wBAAKkD,WAAU;AAAA,wBAAcC,UAAO;AAAA,sBAAA,CAAA,GAAO,KAAEoC,EAAEO,UAAU,GAAA;AAAA,oBAChE,CAAA,CAAA;AAAA,kBACN,CAAA;AAAA,gBAEN,CAAA,yCACCX,WAAW;AAAA,kBAAAhC,UAAAnD,kCAAA,IAAC,QAAK;AAAA,oBAAAkD,WAAW,kCAAkCqC,EAAEQ,WAAW,4BAA4B,6BAA6B;AAAA,oBAAK5C,WAAGoC,uBAAAQ,cAAa,OAAO,aAAa;AAAA,kBAAS,CAAA;AAAA,gBACvL,CAAA,GAEA9F,kCAAA,KAACkF,WAAU;AAAA,kBAAAjC,WAAU;AAAA,kBAA2BC,UAAA,CAAA,4CAAG,OAAI;AAAA,oBAAAD,WAAU;AAAA,oBAAkBC,YAAGoC,MAAAA,uBAAAS,eAAAT,gBAAAA,IAAY/D,UAAS,IACnG+D,uBAAGS,WAAWpC,IAAKC,YAAY7D,kCAAAA,IAAA,QAAA;AAAA,sBAAOmD,iBAAO3C;AAAAA,oBAAK,CAAA,KAClD;AAAA,kBACR,CAAA,GACOR,kCAAA,IAAAiG,iBAAA;AAAA,oBAAgBjC,YAAY;AAAA,oBAAQlC,cAAciB;AAAAA,oBACjDmD,aAAaX;AAAAA,oBAAGtB,iBAAiBsB,EAAES;AAAAA,oBAAYlF,OAAOL,eAAeK;AAAAA,oBAAOuB,MAAM;AAAA,kBAAQ,CAAA,CAAA;AAAA,gBAElG,CAAA,CAAA;AAAA,cAAA,GA/CSkD,EAAED,EAiDjB;AAAA,YAEX;AAAA,UAEb,CAAA,CAAA;AAAA,QAEN,CAAA;AAAA,MACN,CAAA,GACC1E,cAAc,sBAAuBZ,kCAAAA,IAAA,OAAA;AAAA,QAAIkD,WAAU;AAAA,QAC9CC,UAAAnD,kCAAA,IAACmG,YACK;AAAA,UAAAhD,UAAAlD,kCAAA,KAACmG,mBACM;AAAA,YAAAjD,UAAA,CAAAlC,cAAc,KACTjB,kCAAAA,IAACqG,gBACK;AAAA,cAAAlD,UAAAnD,kCAAA,IAACsG,oBAAmB;AAAA,gBAAAlD,SAASA,MAAMxB,iBAAiBX,cAAc,CAAC;AAAA,gBAAGiC,WAAU;AAAA,cAAiB,CAAA;AAAA,YACvG,CAAA,GAENlD,kCAAA,IAACqG;cACKlD,UAACnD,kCAAA,IAAAuG,gBAAA;AAAA,gBAAerD,WAAU;AAAA,gBAAkBC,UAAAlC,cAAc;AAAA,cAAE,CAAA;AAAA,YAClE,CAAA,GACCjB,kCAAA,IAAAqG,gBAAA;AAAA,cACKlD,UAACnD,kCAAA,IAAAwG,gBAAA;AAAA,gBAAepD,SAASA,MAAMxB,iBAAiBX,cAAc,CAAC;AAAA,gBAAGiC,WAAU;AAAA,cAAiB,CAAA;AAAA,YACnG,CAAA,CAAA;AAAA,UACN,CAAA;AAAA,QACN,CAAA;AAAA,MACN,CAAA,CAAA;AAAA,IACN,CAAA,CAAA;AAAA,EACN,CAAA;AAEZ;"}