import { c as createLucideIcon } from "./createLucideIcon-uwkRm45G.js";
/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */
const LoaderCircle = createLucideIcon("LoaderCircle", [
  ["path", { d: "M21 12a9 9 0 1 1-6.219-8.56", key: "13zald" }]
]);
export {
  LoaderCircle as L
};
//# sourceMappingURL=loader-circle-BLZgch8Y.js.map
