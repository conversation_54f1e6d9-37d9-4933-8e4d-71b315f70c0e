{"version": 3, "file": "home.selectedVariation-CogSx-gO.js", "sources": ["../../../app/components/common/selectedVariationAddons.tsx", "../../../app/routes/home.selectedVariation.tsx"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\nimport { Dialog, DialogContent, DialogTitle } from \"../ui/dialog\";\r\nimport { MyAddonData } from \"~/types/api/businessConsoleService/SellerManagement\";\r\nimport { Form, useFetcher } from \"@remix-run/react\";\r\nimport { SquareX } from \"lucide-react\";\r\nimport { useToast } from \"../ui/ToastProvider\";\r\nimport { AddOnGroup } from \"~/types/api/businessConsoleService/MyItemList\";\r\n\r\n\r\n\r\ninterface SelectedVariationAddonsProps {\r\n      isOpen: boolean;\r\n      items: MyAddonData[];\r\n      onClose: () => void;\r\n      header: string;\r\n      groupData?: AddOnGroup,\r\n      sellerId?: number,\r\n      groupId?: number,\r\n      isEdit?: boolean\r\n}\r\nconst SelectedVariationAddons: React.FC<SelectedVariationAddonsProps> = ({\r\n      isOpen,\r\n      items,\r\n      onClose,\r\n      header,\r\n      groupData,\r\n      sellerId,\r\n      groupId,\r\n      isEdit\r\n}) => {\r\n      const [selectedId, setSelectedId] = useState<string | number | null>(null);\r\n      const [searchTerm, setSearchTerm] = useState('');\r\n      const [filteredAddon, setFilteredAddon] = useState<MyAddonData[]>(items)\r\n      const [choosenAddon, setChoosenAddon] = useState<boolean>(false);\r\n      const [choossenAddonName, setChoosenAddonName] = useState<string>('');\r\n      const [formData, setFormData] = useState({\r\n            minSelect: groupData?.minSelect.toString() || \"0\",\r\n            maxSelect: groupData?.seq.toString() || \"0\",\r\n            name: groupData?.name.toString() || \"\",\r\n            description: groupData?.description.toString() || \"\",\r\n            varient: groupData?.varient ?? false,\r\n            seq: groupData?.seq ?? \"0\"\r\n      });\r\n      const { showToast } = useToast()\r\n      useEffect(() => {\r\n            if (searchTerm.length >= 3 && searchTerm !== \"\") {\r\n                  setFilteredAddon(items?.filter(addon => addon?.name.toLowerCase().includes(searchTerm.toLowerCase())))\r\n            }\r\n            else {\r\n                  setFilteredAddon(items)\r\n            } ``\r\n      }, [searchTerm, items]);\r\n\r\n      useEffect(() => {\r\n            if (!isOpen) {\r\n                  setSelectedId(null);\r\n                  setSearchTerm(\"\");\r\n                  setChoosenAddon(false);\r\n                  setFormData({\r\n                        minSelect: \"0\",\r\n                        maxSelect: \"0\",\r\n                        name: \"\",\r\n                        description: \"\",\r\n                        varient: false,\r\n                        seq: \"0\"\r\n                  })\r\n            }\r\n\r\n      }, [isOpen]);\r\n      useEffect(() => {\r\n            if (groupData) {\r\n                  setChoosenAddon(true);\r\n                  setSelectedId(groupData?.id)\r\n                  setChoosenAddonName(groupData.name)\r\n                  setFormData(prev => ({\r\n                        ...prev,\r\n                        minSelect: groupData?.minSelect.toString(),\r\n                        maxSelect: groupData?.seq.toString(),\r\n                        name: groupData?.name.toString(),\r\n                        description: groupData?.description.toString(),\r\n                        varient: groupData?.varient,\r\n                  }))\r\n            }\r\n\r\n      }, [groupData]);\r\n\r\n      const handleSelect = (addon: MyAddonData) => {\r\n            setSelectedId(addon.id);\r\n            setChoosenAddon(true)\r\n            setChoosenAddonName(addon.name)\r\n\r\n      }\r\n      const deselectAddon = () => {\r\n            setSelectedId(null)\r\n            setChoosenAddon(false)\r\n      }\r\n      const groupMapfetcher = useFetcher();\r\n      useEffect(() => {\r\n            if (groupMapfetcher.data) {\r\n                  if (groupMapfetcher.data?.sucess) {\r\n                        showToast(\"sucess to Map GroupData\", 'success')\r\n                        onClose()\r\n                        setFormData({\r\n                              minSelect: \"0\",\r\n                              maxSelect: \"0\",\r\n                              name: \"\",\r\n                              description: \"\",\r\n                              varient: false,\r\n                              seq: \"0\"\r\n                        })\r\n\r\n                  }\r\n                  else if (groupMapfetcher.data?.sucess === false) {\r\n                        showToast(\"failed to  Map  GroupData\", 'success')\r\n                  }\r\n\r\n            }\r\n\r\n      }, [groupMapfetcher?.data])\r\n\r\n\r\n\r\n      if (!isOpen) return null;\r\n      return (\r\n            <Dialog open={isOpen} onOpenChange={onClose}>\r\n                  <DialogContent className=\"w-full max-w-md sm:max-w-lg bg-white rounded-2xl shadow-2xl\">\r\n                        <DialogTitle className=\"text-2xl font-bold text-gray-900 mb-4\">{header}</DialogTitle>\r\n                        <div className=\"space-y-6\">\r\n                              {choosenAddon === false && selectedId === null && <>\r\n                                    <div>\r\n                                          <input\r\n                                                placeholder=\"Search by Addon Name\"\r\n                                                type=\"search\"\r\n                                                className=\"w-full p-3 rounded-full border-2 border-gray-200 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-colors\"\r\n                                                autoFocus\r\n                                                value={searchTerm}\r\n                                                onChange={(e) => setSearchTerm(e.target.value)}\r\n                                          />\r\n                                    </div>\r\n                                    <div className=\"mt-4 max-h-[40vh] overflow-y-auto pr-2 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100\">\r\n                                          <ul className=\"space-y-2\">\r\n                                                {filteredAddon.length === 0 ? (\r\n                                                      <p className=\"p-4 text-gray-500 text-center\">No add-ons found</p>\r\n                                                ) : (\r\n                                                      filteredAddon.map((item) => (\r\n                                                            <li key={item.id} className=\"flex items-center gap-3\">\r\n                                                                  <input\r\n                                                                        type=\"checkbox\"\r\n                                                                        id={`item-${item.id}`}\r\n                                                                        name=\"selectedItem\"\r\n                                                                        value={item.id}\r\n                                                                        checked={selectedId === item.id}\r\n                                                                        onChange={() => handleSelect(item)}\r\n                                                                        className=\"h-5 w-5 text-blue-600 focus:ring-blue-500 rounded\"\r\n                                                                  />\r\n                                                                  <label\r\n                                                                        htmlFor={`item-${item.id}`}\r\n                                                                        className={`cursor-pointer p-3 w-full rounded-lg border ${selectedId === item.id ? 'bg-blue-50 border-blue-200' : 'border-gray-200'} text-gray-800 hover:bg-gray-50 transition-colors`}\r\n                                                                  >\r\n                                                                        {item?.name} <span className=\"text-gray-500\">({item?.diet})</span>\r\n                                                                  </label>\r\n                                                            </li>\r\n                                                      ))\r\n                                                )}\r\n                                          </ul>\r\n                                    </div>\r\n                              </>\r\n                              }\r\n\r\n                              {choosenAddon && selectedId && <div className=\"space-y-4\">\r\n                                    {selectedId && (\r\n                                          <div className=\"flex items-center justify-between bg-blue-50 p-3 rounded-lg\">\r\n                                                <p className=\"font-medium text-gray-800 truncate max-w-[80%]\">{choossenAddonName}</p>\r\n                                                <SquareX\r\n                                                      color=\"red\"\r\n                                                      className=\"cursor-pointer hover:scale-110 transition-transform\"\r\n                                                      onClick={() => deselectAddon()}\r\n                                                />\r\n                                          </div>\r\n                                    )}\r\n\r\n                                    <div className=\"grid grid-cols-1 sm:grid-cols-2 gap-4\">\r\n                                          <div>\r\n                                                <label htmlFor=\"price\" className=\"block text-sm font-medium text-gray-700 mb-1\">\r\n                                                      name\r\n                                                </label>\r\n                                                <input\r\n                                                      type=\"text\"\r\n                                                      id=\"name\"\r\n                                                      name=\"name\"\r\n                                                      placeholder=\"Enter the Name\"\r\n                                                      value={formData.name}\r\n                                                      onChange={(e) => setFormData({ ...formData, name: e.target.value })}\r\n                                                      required\r\n                                                      className=\"w-full p-2 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none\"\r\n                                                />\r\n                                          </div>\r\n                                          <div>\r\n                                                <label htmlFor=\"price\" className=\"block text-sm font-medium text-gray-700 mb-1\">\r\n                                                      description\r\n                                                </label>\r\n                                                <input\r\n                                                      type=\"text\"\r\n                                                      id=\"description\"\r\n                                                      name=\"description\"\r\n                                                      placeholder=\"Enter the description\"\r\n\r\n                                                      value={formData.description}\r\n                                                      onChange={(e) => setFormData({ ...formData, description: e.target.value })}\r\n                                                      required\r\n                                                      className=\"w-full p-2 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none\"\r\n                                                />\r\n                                          </div>\r\n                                          <div>\r\n                                                <label htmlFor=\"minSelect\" className=\"block text-sm font-medium text-gray-700 mb-1\">\r\n                                                      minSelect\r\n                                                </label>\r\n                                                <input\r\n                                                      type=\"number\"\r\n                                                      id=\"minSelect\"\r\n                                                      name=\"minSelect\"\r\n                                                      placeholder=\"Enter the minimum selection count\"\r\n                                                      value={formData.minSelect}\r\n                                                      onChange={(e) => setFormData({ ...formData, minSelect: e.target.value })}\r\n                                                      min=\"0\"\r\n                                                      required\r\n                                                      className=\"w-full p-2 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none\"\r\n                                                />\r\n                                          </div>\r\n                                          <div>\r\n                                                <label htmlFor=\"maxSelect\" className=\"block text-sm font-medium text-gray-700 mb-1\">\r\n                                                      maxSelect\r\n                                                </label>\r\n                                                <input\r\n                                                      type=\"number\"\r\n                                                      id=\"maxSelect\"\r\n                                                      name=\"maxSelect\"\r\n                                                      placeholder=\"Enter the maximum selection count\"\r\n                                                      value={formData.maxSelect}\r\n                                                      onChange={(e) => setFormData({ ...formData, maxSelect: e.target.value })}\r\n                                                      min=\"0\"\r\n                                                      required\r\n                                                      className=\"w-full p-2 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none\"\r\n                                                />\r\n                                          </div>\r\n                                          <div>\r\n                                                <label htmlFor=\"seq\" className=\"block text-sm font-medium text-gray-700 mb-1\">\r\n                                                      Sequence\r\n                                                </label>\r\n                                                <input\r\n                                                      type=\"number\"\r\n                                                      id=\"seq\"\r\n                                                      name=\"seq\"\r\n                                                      placeholder=\"Enter the sequence number\"\r\n                                                      value={formData.seq}\r\n                                                      onChange={(e) => setFormData({ ...formData, seq: e.target.value })}\r\n                                                      min=\"0\"\r\n                                                      required\r\n                                                      className=\"w-full p-2 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none\"\r\n                                                />\r\n                                          </div>\r\n\r\n                                    </div>\r\n                              </div>}\r\n                        </div>\r\n                        <Form method=\"POST\" className=\"mt-6 flex flex-col sm:flex-row gap-3 justify-end\">\r\n                              <input type=\"hidden\" name=\"addonId\" value={selectedId?.toString()} />\r\n                              <input type=\"hidden\" name=\"variationId\" value={groupId?.toString()} />\r\n                              <input type=\"hidden\" name=\"minSelect\" value={formData.minSelect?.toString()} />\r\n                              <input type=\"hidden\" name=\"maxSelect\" value={formData.maxSelect?.toString()} />\r\n                              <input type=\"hidden\" name=\"varient\" value={formData.varient?.toString()} />\r\n                              <input type=\"hidden\" name=\"sellerId\" value={sellerId?.toString()} />\r\n                              <input type=\"hidden\" name=\"name\" value={formData.name?.toString()} />\r\n                              <input type=\"hidden\" name=\"description\" value={formData.description?.toString()} />\r\n                              <input type=\"hidden\" name=\"seq\" value={formData.seq?.toString()} />\r\n                              {isEdit && <input type=\"hidden\" name=\"itemVariationId\" value={groupData.id?.toString()} />\r\n                              }\r\n                              <input type=\"hidden\" name=\"addonName\" value={choossenAddonName?.toString()} />\r\n                              <input type=\"hidden\" name=\"actionType\" value={\"actionAddonforVariation\"} />\r\n                              <input type=\"hidden\" name=\"mode\" value={isEdit ? \"EditMode\" : \"\"} />\r\n\r\n                              <button\r\n                                    onClick={onClose}\r\n                                    className=\"w-full sm:w-auto px-4 py-2 rounded-lg bg-gray-100 text-gray-700 hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-300 text-sm font-medium\"\r\n                              >\r\n                                    Cancel\r\n                              </button>\r\n                              <button\r\n                                    type=\"submit\"\r\n                                    disabled={selectedId === null}\r\n                                    className={`w-full sm:w-auto px-4 py-2 rounded-lg text-sm font-medium text-white focus:outline-none focus:ring-2 focus:ring-blue-500 ${selectedId === null ? 'bg-gray-400 cursor-not-allowed' : 'bg-blue-600 hover:bg-blue-700'}`}\r\n                              >\r\n                                    Confirm\r\n                              </button>\r\n                        </Form>\r\n                  </DialogContent>\r\n            </Dialog>\r\n      );\r\n};\r\n\r\nexport default SelectedVariationAddons;\r\n", "import type { LoaderFunction } from \"@remix-run/node\";\r\nimport { Form, useActionData, useFetcher, useLoaderData, useNavigate } from \"@remix-run/react\";\r\nimport type { MyAddonData } from \"~/types/api/businessConsoleService/SellerManagement\";\r\nimport { createVariationAddonsGroup, deleteAddonVariation, getAddons, getSelectedVarAddonGroup } from \"~/services/businessConsoleService\";\r\nimport { withAuth, withResponse } from \"~/utils/auth-utils\";\r\nimport { ResponsiveTable } from \"~/components/ui/responsiveTable\";\r\nimport { Button } from \"~/components/ui/button\";\r\nimport { Pencil, Trash } from \"lucide-react\";\r\nimport React, { useEffect, useState } from \"react\";\r\nimport { AddOnGroup } from \"~/types/api/businessConsoleService/MyItemList\";\r\nimport SelectedVariationAddons from \"~/components/common/selectedVariationAddons\";\r\nimport SpinnerLoader from \"~/components/loader/SpinnerLoader\";\r\nimport { Input } from \"~/components/ui/input\";\r\nimport { useDebounce } from \"~/hooks/useDebounce\";\r\ninterface LoaderData {\r\n      selectedVarGruoupData: AddOnGroup[],\r\n      variationName: string,\r\n      sellerId: number,\r\n      variationId: number\r\n}\r\ninterface ActionData {\r\n      selectedAddonsData: MyAddonData[],\r\n      sucessMessage: string,\r\n      ErrorMessage: string,\r\n      sucess?: boolean,\r\n      error?: string\r\n\r\n}\r\nexport const loader: LoaderFunction = withAuth(async ({ request }) => {\r\n      const url = new URL(request.url);\r\n      const page = parseInt(url.searchParams.get(\"page\") || \"0\");\r\n      const pageSize = parseInt(url.searchParams.get(\"pageSize\") || \"50\");\r\n      const matchBy = url.searchParams.get(\"matchBy\") || \"\";\r\n      const sellerId = Number(url.searchParams.get(\"sellerId\"));\r\n      const variationId = Number(url.searchParams.get(\"variationId\"));\r\n      const variationName = url.searchParams.get(\"variationName\");\r\n      try {\r\n            const addonsGroupsResponse = await getSelectedVarAddonGroup(sellerId, variationId, page, pageSize, matchBy, request);\r\n            const selectedVarGruoupData = addonsGroupsResponse.data;\r\n            return withResponse({ selectedVarGruoupData, variationName, sellerId, variationId }, addonsGroupsResponse.headers);\r\n      } catch (error) {\r\n            console.error(\"Error loading addons groups:\", error);\r\n            throw new Response('Failed to load addons', { status: 500 });\r\n      }\r\n});\r\n\r\nexport const action = withAuth(async ({ request }) => {\r\n      const formData = await request.formData();\r\n      const sellerId = Number(formData.get(\"sellerId\"))\r\n      const pageSize = Number(formData.get(\"pageSize\") || \"50\");\r\n      const page = Number(formData.get(\"page\") || \"0\");\r\n      const matchBy = formData.get(\"matchBy\") as string\r\n      const actionType = formData.get(\"actionType\") as string\r\n\r\n      if (actionType === \"getAddons\") {\r\n            try {\r\n                  const addonsList = await getAddons(sellerId, page, pageSize, matchBy, request);\r\n                  const selectedAddonsData = addonsList.data;\r\n                  return withResponse({ selectedAddonsData }, addonsList.headers);\r\n            } catch (error) {\r\n                  console.error(\"Error loading addons groups:\", error);\r\n                  throw new Response('Failed to load addons', { status: 500 });\r\n            }\r\n      }\r\n      else if (\r\n            actionType === \"actionAddonforVariation\"\r\n      ) {\r\n            // const addonGroupId = Number(formData.get(\"addonGroupId\"));\r\n            const minSelect = Number(formData.get(\"minSelect\"));\r\n            const varient = (formData.get(\"varient\")) as unknown as boolean;\r\n            const maxSelect = Number(formData.get(\"maxSelect\"));\r\n            const addonId = Number(formData.get(\"addonId\"));\r\n            const name = formData.get(\"name\") as string\r\n            const description = formData.get(\"description\") as string\r\n            const variationId = Number(formData.get(\"variationId\"))\r\n            const seq = Number(formData.get(\"seq\"));\r\n            const mode = formData.get(\"mode\") as string;\r\n            const itemVariationId = Number(formData.get(\"itemVariationId\"));\r\n\r\n            const payload: AddOnGroup = {\r\n                  sId: addonId.toString(),\r\n                  minSelect: minSelect,\r\n                  maxSelect: maxSelect,\r\n                  name: name,\r\n                  description: description,\r\n                  seq: seq,\r\n                  varient: varient\r\n\r\n            }\r\n            const editpayload: AddOnGroup = {\r\n                  id: itemVariationId,\r\n                  sId: addonId.toString(),\r\n                  minSelect: minSelect,\r\n                  maxSelect: maxSelect,\r\n                  name: name,\r\n                  description: description,\r\n                  seq: seq,\r\n                  varient: varient\r\n\r\n            }\r\n\r\n\r\n            const finalPayload = mode == \"EditMode\" ? editpayload : payload;\r\n            try {\r\n                  const addonsList = await createVariationAddonsGroup(sellerId, variationId, finalPayload, request);\r\n                  return withResponse({ sucess: addonsList.statusCode === 200 }, addonsList.headers);\r\n            } catch (error) {\r\n                  console.error(\"Error loading addons groups:\", error);\r\n                  throw new Response('Failed to load addons', { status: 500 });\r\n            }\r\n      }\r\n      else if (\r\n            actionType == \"addonsVarDelete\"\r\n      ) {\r\n            const addonVarId = Number(formData.get(\"addonVarId\"));\r\n            const addonGId = Number(formData.get(\"addonGId\"));\r\n\r\n\r\n            try {\r\n                  const addonsList = await deleteAddonVariation(sellerId, addonVarId, addonGId, request);\r\n                  const selectedAddonsData = addonsList.data;\r\n                  return withResponse({ selectedAddonsData }, addonsList.headers);\r\n            } catch (error) {\r\n                  console.error(\"Error loading addons groups:\", error);\r\n                  throw new Response('Failed to load addons', { status: 500 });\r\n            }\r\n      }\r\n})\r\nconst SelectedVariation: React.FC = () => {\r\n      const { selectedVarGruoupData, variationName, sellerId, variationId } = useLoaderData<LoaderData>();\r\n      const navigate = useNavigate();\r\n      const addonMapfetcher = useFetcher<ActionData>()\r\n\r\n      console.log(selectedVarGruoupData, \"LLLLLLLLLLLLLL\")\r\n\r\n      const [isAddselectedGroupAddonsOpen, setIsAddselectedGroupAddonsOpen] = useState(false);\r\n      const [selectedGdata, setSelectedGdata] = useState<AddOnGroup>();\r\n      const actionData = useActionData<ActionData>();\r\n      const [isEditopen, setIsEditOpen] = useState(false)\r\n\r\n      const [selectedAddonsData, setSelectedAddonsData] = useState<MyAddonData[]>()\r\n      const selectedGroupHeader = [\r\n            \"Id\",\r\n            \"name\",\r\n            \"description\",\r\n            \"seq\",\r\n            \"minSelect\",\r\n            \"maxSelect\",\r\n            \"\",\r\n            \"\"\r\n      ];\r\n      useEffect(() => {\r\n            if (addonMapfetcher.state === \"idle\") {\r\n\r\n                  if (actionData?.selectedAddonsData) {\r\n                        setSelectedAddonsData(actionData.selectedAddonsData)\r\n                        setIsAddselectedGroupAddonsOpen(true)\r\n                        setIsEditOpen(false)\r\n\r\n                  }\r\n                  else {\r\n                        setSelectedAddonsData([])\r\n\r\n                        setIsAddselectedGroupAddonsOpen(false)\r\n                        setIsEditOpen(false)\r\n\r\n                  }\r\n            }\r\n\r\n      }, [actionData])\r\n\r\n      const handleSelectedGroupData = (row: AddOnGroup) => {\r\n            setSelectedGdata(row);\r\n            setIsEditOpen(true);\r\n            setIsAddselectedGroupAddonsOpen(true)\r\n      }\r\n      const handleDelete = (addonsmapData: AddOnGroup) => {\r\n            const formData = new FormData();\r\n            formData.append(\"actionType\", \"addonsVarDelete\");\r\n            formData.append(\"addonGId\", addonsmapData?.id.toString())\r\n            formData.append(\"addonVarId\", variationId.toString())\r\n            formData.append(\"sellerId\", sellerId.toString())\r\n\r\n            addonMapfetcher.submit(formData, { method: 'post' })\r\n      }\r\n\r\n      const [searchTerm, setSearchTerm] = useState('');\r\n      const [filteredVariations, setFilteredVariations] = useState<AddOnGroup[]>([])\r\n      const loading = addonMapfetcher.state !== \"idle\"\r\n      const debouncedSearchTerm = useDebounce(searchTerm, 500);\r\n\r\n      useEffect(() => {\r\n            if (debouncedSearchTerm.length >= 2 && debouncedSearchTerm !== \"\") {\r\n                  const filtered = selectedVarGruoupData.filter((item) =>\r\n                        [item.name, item.description].some(\r\n                              (field) => field?.toLowerCase().includes(debouncedSearchTerm.toLowerCase())\r\n                        )\r\n                  )\r\n                  setFilteredVariations(filtered)\r\n            }\r\n            else {\r\n                  setFilteredVariations(selectedVarGruoupData)\r\n            }\r\n\r\n      }, [debouncedSearchTerm, selectedVarGruoupData]);\r\n      return (\r\n            <div className=\"h-full\">\r\n                  {loading && <SpinnerLoader loading={loading} size={20} />}\r\n\r\n                  <h1 className=\" mb-4 font-bold cursor-pointer  hover:text-blue-400  text-black   \" onClick={() => navigate(-1)}> <span className=\"text-2xl\">MyItemVariation / </span> <span className=\"text-xl\">{variationName} </span> </h1>\r\n                  <div className=\"flex flex-wrap mb-2 \">\r\n\r\n                        <Input\r\n                              placeholder=\"Search by  Name or Description\"\r\n                              value={searchTerm}\r\n                              type='search'\r\n                              onChange={(e) => setSearchTerm(e.target.value)}\r\n                              className=\"max-w-sm mt-2 rounded-full \"\r\n                        />\r\n                  </div>\r\n\r\n\r\n                  <ResponsiveTable\r\n                        headers={selectedGroupHeader}\r\n                        data={filteredVariations}\r\n                        renderRow={(row) => (\r\n                              <tr key={row.id} className=\"border-b\">\r\n                                    <td className=\"py-2 px-3 text-center whitespace-normal break-words \">{row.id}</td>\r\n                                    <td className=\"py-2 px-3 text-center whitespace-normal break-words \">{row?.name}</td>\r\n                                    <td className=\"py-2 px-3 text-center whitespace-normal break-words\">{row?.description}</td>\r\n                                    <td className=\"py-2 px-3 text-center whitespace-normal break-words\">{row?.seq}</td>\r\n                                    <td className=\"py-2 px-3 text-center whitespace-normal break-words\">{row?.minSelect}</td>\r\n                                    <td className=\"py-2 px-3 text-center whitespace-normal break-words\">{row?.minSelect}</td>\r\n\r\n                                    <td className=\"py-2 px-3 text-center cursor-pointer\">\r\n                                          <Button\r\n                                                variant=\"ghost\"\r\n                                                size=\"sm\"\r\n                                                className=\"text-red-500 hover:text-red-900\"\r\n                                                onClick={() => {\r\n                                                      if (confirm(\"Are you sure you want to delete this ?\")) {\r\n                                                            handleDelete(row)\r\n                                                      }\r\n                                                }}\r\n                                                style={{ alignSelf: \"flex-end\" }}\r\n                                          >\r\n                                                <Trash size={20} />\r\n                                          </Button>\r\n                                    </td>\r\n                                    <td className=\"py-2 px-3 text-center cursor-pointer\">\r\n                                          <Pencil color='blue' size={20} onClick={() => handleSelectedGroupData(row)} />\r\n                                    </td>\r\n                              </tr>\r\n                        )}\r\n                  />\r\n                  <Form method=\"post\" >\r\n                        <input name=\"sellerId\" value={sellerId} hidden />\r\n                        <input name=\"matchBy\" value={\"\"} hidden />\r\n                        <input name=\"actionType\" value={\"getAddons\"} hidden />\r\n\r\n                        <Button\r\n                              className=\"fixed bottom-5 right-5 rounded-full cursor-pointer\"\r\n                              type=\"submit\"\r\n                        >\r\n                              + Create Addon for Item Variation\r\n                        </Button>\r\n                  </Form>\r\n                  <SelectedVariationAddons\r\n                        isOpen={isAddselectedGroupAddonsOpen}\r\n                        items={selectedAddonsData || []}\r\n                        onClose={() => setIsAddselectedGroupAddonsOpen(false)}\r\n                        header={isEditopen ? `Edit Addon for${variationName?.slice(0, 15)}` : `Create Addon for ${variationName?.slice(0, 15)} `} groupData={selectedGdata}\r\n                        sellerId={sellerId}\r\n                        groupId={variationId}\r\n                        isEdit={isEditopen}\r\n                  />\r\n            </div>\r\n      );\r\n};\r\nexport default SelectedVariation;\r\n"], "names": ["useState", "useEffect", "_a", "_b", "jsx", "jsxs", "Fragment", "SelectedVariation", "selectedVarGruoupData", "variationName", "sellerId", "variationId", "useLoaderData", "navigate", "useNavigate", "addon<PERSON><PERSON><PERSON><PERSON>er", "useFetcher", "console", "log", "isAddselectedGroupAddonsOpen", "setIsAddselectedGroupAddonsOpen", "selectedGdata", "setSelectedGdata", "actionData", "useActionData", "isEditopen", "setIsEditOpen", "selectedAddonsData", "setSelectedAddonsData", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "state", "handleSelectedGroupData", "row", "handleDelete", "addonsmapData", "formData", "FormData", "append", "id", "toString", "submit", "method", "searchTerm", "setSearchTerm", "filteredVariations", "setFilteredVariations", "loading", "debouncedSearchTerm", "useDebounce", "length", "filtered", "filter", "item", "name", "description", "some", "field", "toLowerCase", "includes", "className", "children", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "size", "onClick", "Input", "placeholder", "value", "type", "onChange", "e", "target", "ResponsiveTable", "headers", "data", "renderRow", "seq", "minSelect", "<PERSON><PERSON>", "variant", "confirm", "style", "alignSelf", "Trash", "Pencil", "color", "Form", "hidden", "SelectedVariationAddons", "isOpen", "items", "onClose", "header", "slice", "groupData", "groupId", "isEdit"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAoBA,MAAM,0BAAkE,CAAC;AAAA,EACnE;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACN,MAAM;;AACA,QAAM,CAAC,YAAY,aAAa,IAAIA,aAAAA,SAAiC,IAAI;AACzE,QAAM,CAAC,YAAY,aAAa,IAAIA,aAAAA,SAAS,EAAE;AAC/C,QAAM,CAAC,eAAe,gBAAgB,IAAIA,aAAAA,SAAwB,KAAK;AACvE,QAAM,CAAC,cAAc,eAAe,IAAIA,aAAAA,SAAkB,KAAK;AAC/D,QAAM,CAAC,mBAAmB,mBAAmB,IAAIA,aAAAA,SAAiB,EAAE;AACpE,QAAM,CAAC,UAAU,WAAW,IAAIA,sBAAS;AAAA,IACnC,YAAW,uCAAW,UAAU,eAAc;AAAA,IAC9C,YAAW,uCAAW,IAAI,eAAc;AAAA,IACxC,OAAM,uCAAW,KAAK,eAAc;AAAA,IACpC,cAAa,uCAAW,YAAY,eAAc;AAAA,IAClD,UAAS,uCAAW,YAAW;AAAA,IAC/B,MAAK,uCAAW,QAAO;AAAA,EAAA,CAC5B;AACK,QAAA,EAAE,UAAU,IAAI,SAAS;AAC/BC,eAAAA,UAAU,MAAM;AACV,QAAI,WAAW,UAAU,KAAK,eAAe,IAAI;AAC3C,uBAAiB,+BAAO,OAAO,CAAS,UAAA,+BAAO,KAAK,cAAc,SAAS,WAAW,YAAA,GAAe;AAAA,IAAA,OAEtG;AACC,uBAAiB,KAAK;AAAA,IAAA;AAAA,EAC1B,GACL,CAAC,YAAY,KAAK,CAAC;AAEtBA,eAAAA,UAAU,MAAM;AACV,QAAI,CAAC,QAAQ;AACP,oBAAc,IAAI;AAClB,oBAAc,EAAE;AAChB,sBAAgB,KAAK;AACT,kBAAA;AAAA,QACN,WAAW;AAAA,QACX,WAAW;AAAA,QACX,MAAM;AAAA,QACN,aAAa;AAAA,QACb,SAAS;AAAA,QACT,KAAK;AAAA,MAAA,CACV;AAAA,IAAA;AAAA,EACP,GAEH,CAAC,MAAM,CAAC;AACXA,eAAAA,UAAU,MAAM;AACV,QAAI,WAAW;AACT,sBAAgB,IAAI;AACpB,oBAAc,uCAAW,EAAE;AAC3B,0BAAoB,UAAU,IAAI;AAClC,kBAAY,CAAS,UAAA;AAAA,QACf,GAAG;AAAA,QACH,WAAW,uCAAW,UAAU;AAAA,QAChC,WAAW,uCAAW,IAAI;AAAA,QAC1B,MAAM,uCAAW,KAAK;AAAA,QACtB,aAAa,uCAAW,YAAY;AAAA,QACpC,SAAS,uCAAW;AAAA,MAAA,EACxB;AAAA,IAAA;AAAA,EACR,GAEH,CAAC,SAAS,CAAC;AAER,QAAA,eAAe,CAAC,UAAuB;AACvC,kBAAc,MAAM,EAAE;AACtB,oBAAgB,IAAI;AACpB,wBAAoB,MAAM,IAAI;AAAA,EAEpC;AACA,QAAM,gBAAgB,MAAM;AACtB,kBAAc,IAAI;AAClB,oBAAgB,KAAK;AAAA,EAC3B;AACA,QAAM,kBAAkB,WAAW;AACnCA,eAAAA,UAAU,MAAM;;AACV,QAAI,gBAAgB,MAAM;AAChB,WAAAC,MAAA,gBAAgB,SAAhB,gBAAAA,IAAsB,QAAQ;AAC5B,kBAAU,2BAA2B,SAAS;AACtC,gBAAA;AACI,oBAAA;AAAA,UACN,WAAW;AAAA,UACX,WAAW;AAAA,UACX,MAAM;AAAA,UACN,aAAa;AAAA,UACb,SAAS;AAAA,UACT,KAAK;AAAA,QAAA,CACV;AAAA,MAGE,aAAAC,MAAA,gBAAgB,SAAhB,gBAAAA,IAAsB,YAAW,OAAO;AAC3C,kBAAU,6BAA6B,SAAS;AAAA,MAAA;AAAA,IACtD;AAAA,EAEN,GAEH,CAAC,mDAAiB,IAAI,CAAC;AAItB,MAAA,CAAC,OAAe,QAAA;AAEd,SAAAC,kCAAA,IAAC,UAAO,MAAM,QAAQ,cAAc,SAC9B,UAAAC,kCAAA,KAAC,eAAc,EAAA,WAAU,+DACnB,UAAA;AAAA,IAACD,kCAAA,IAAA,aAAA,EAAY,WAAU,yCAAyC,UAAO,QAAA;AAAA,IACvEC,kCAAAA,KAAC,OAAI,EAAA,WAAU,aACR,UAAA;AAAA,MAAiB,iBAAA,SAAS,eAAe,QACpCA,kCAAAA,KAAAC,kBAAAA,UAAA,EAAA,UAAA;AAAA,QAAAF,sCAAC,OACK,EAAA,UAAAA,kCAAA;AAAA,UAAC;AAAA,UAAA;AAAA,YACK,aAAY;AAAA,YACZ,MAAK;AAAA,YACL,WAAU;AAAA,YACV,WAAS;AAAA,YACT,OAAO;AAAA,YACP,UAAU,CAAC,MAAM,cAAc,EAAE,OAAO,KAAK;AAAA,UAAA;AAAA,QAAA,GAEzD;AAAA,QACAA,kCAAA,IAAC,OAAI,EAAA,WAAU,2GACT,UAAAA,kCAAAA,IAAC,QAAG,WAAU,aACP,UAAc,cAAA,WAAW,IACpBA,kCAAA,IAAC,OAAE,WAAU,iCAAgC,UAAgB,mBAAA,CAAA,IAE7D,cAAc,IAAI,CAAC,SACbC,kCAAA,KAAC,MAAiB,EAAA,WAAU,2BACtB,UAAA;AAAA,UAAAD,kCAAA;AAAA,YAAC;AAAA,YAAA;AAAA,cACK,MAAK;AAAA,cACL,IAAI,QAAQ,KAAK,EAAE;AAAA,cACnB,MAAK;AAAA,cACL,OAAO,KAAK;AAAA,cACZ,SAAS,eAAe,KAAK;AAAA,cAC7B,UAAU,MAAM,aAAa,IAAI;AAAA,cACjC,WAAU;AAAA,YAAA;AAAA,UAChB;AAAA,UACAC,kCAAA;AAAA,YAAC;AAAA,YAAA;AAAA,cACK,SAAS,QAAQ,KAAK,EAAE;AAAA,cACxB,WAAW,+CAA+C,eAAe,KAAK,KAAK,+BAA+B,iBAAiB;AAAA,cAElI,UAAA;AAAA,gBAAM,6BAAA;AAAA,gBAAK;AAAA,gBAACA,kCAAAA,KAAC,QAAK,EAAA,WAAU,iBAAgB,UAAA;AAAA,kBAAA;AAAA,kBAAE,6BAAM;AAAA,kBAAK;AAAA,gBAAA,EAAC,CAAA;AAAA,cAAA;AAAA,YAAA;AAAA,UAAA;AAAA,QACjE,EAAA,GAfG,KAAK,EAgBd,CACL,EAEb,CAAA,EACN,CAAA;AAAA,MAAA,GACN;AAAA,MAGC,gBAAgB,cAAeA,uCAAA,OAAA,EAAI,WAAU,aACvC,UAAA;AAAA,QACK,cAAAA,kCAAA,KAAC,OAAI,EAAA,WAAU,+DACT,UAAA;AAAA,UAACD,kCAAA,IAAA,KAAA,EAAE,WAAU,kDAAkD,UAAkB,mBAAA;AAAA,UACjFA,kCAAA;AAAA,YAAC;AAAA,YAAA;AAAA,cACK,OAAM;AAAA,cACN,WAAU;AAAA,cACV,SAAS,MAAM,cAAc;AAAA,YAAA;AAAA,UAAA;AAAA,QACnC,GACN;AAAA,QAGNC,kCAAAA,KAAC,OAAI,EAAA,WAAU,yCACT,UAAA;AAAA,UAAAA,uCAAC,OACK,EAAA,UAAA;AAAA,YAAAD,sCAAC,SAAM,EAAA,SAAQ,SAAQ,WAAU,gDAA+C,UAEhF,QAAA;AAAA,YACAA,kCAAA;AAAA,cAAC;AAAA,cAAA;AAAA,gBACK,MAAK;AAAA,gBACL,IAAG;AAAA,gBACH,MAAK;AAAA,gBACL,aAAY;AAAA,gBACZ,OAAO,SAAS;AAAA,gBAChB,UAAU,CAAC,MAAM,YAAY,EAAE,GAAG,UAAU,MAAM,EAAE,OAAO,OAAO;AAAA,gBAClE,UAAQ;AAAA,gBACR,WAAU;AAAA,cAAA;AAAA,YAAA;AAAA,UAChB,GACN;AAAA,iDACC,OACK,EAAA,UAAA;AAAA,YAAAA,sCAAC,SAAM,EAAA,SAAQ,SAAQ,WAAU,gDAA+C,UAEhF,eAAA;AAAA,YACAA,kCAAA;AAAA,cAAC;AAAA,cAAA;AAAA,gBACK,MAAK;AAAA,gBACL,IAAG;AAAA,gBACH,MAAK;AAAA,gBACL,aAAY;AAAA,gBAEZ,OAAO,SAAS;AAAA,gBAChB,UAAU,CAAC,MAAM,YAAY,EAAE,GAAG,UAAU,aAAa,EAAE,OAAO,OAAO;AAAA,gBACzE,UAAQ;AAAA,gBACR,WAAU;AAAA,cAAA;AAAA,YAAA;AAAA,UAChB,GACN;AAAA,iDACC,OACK,EAAA,UAAA;AAAA,YAAAA,sCAAC,SAAM,EAAA,SAAQ,aAAY,WAAU,gDAA+C,UAEpF,aAAA;AAAA,YACAA,kCAAA;AAAA,cAAC;AAAA,cAAA;AAAA,gBACK,MAAK;AAAA,gBACL,IAAG;AAAA,gBACH,MAAK;AAAA,gBACL,aAAY;AAAA,gBACZ,OAAO,SAAS;AAAA,gBAChB,UAAU,CAAC,MAAM,YAAY,EAAE,GAAG,UAAU,WAAW,EAAE,OAAO,OAAO;AAAA,gBACvE,KAAI;AAAA,gBACJ,UAAQ;AAAA,gBACR,WAAU;AAAA,cAAA;AAAA,YAAA;AAAA,UAChB,GACN;AAAA,iDACC,OACK,EAAA,UAAA;AAAA,YAAAA,sCAAC,SAAM,EAAA,SAAQ,aAAY,WAAU,gDAA+C,UAEpF,aAAA;AAAA,YACAA,kCAAA;AAAA,cAAC;AAAA,cAAA;AAAA,gBACK,MAAK;AAAA,gBACL,IAAG;AAAA,gBACH,MAAK;AAAA,gBACL,aAAY;AAAA,gBACZ,OAAO,SAAS;AAAA,gBAChB,UAAU,CAAC,MAAM,YAAY,EAAE,GAAG,UAAU,WAAW,EAAE,OAAO,OAAO;AAAA,gBACvE,KAAI;AAAA,gBACJ,UAAQ;AAAA,gBACR,WAAU;AAAA,cAAA;AAAA,YAAA;AAAA,UAChB,GACN;AAAA,iDACC,OACK,EAAA,UAAA;AAAA,YAAAA,sCAAC,SAAM,EAAA,SAAQ,OAAM,WAAU,gDAA+C,UAE9E,YAAA;AAAA,YACAA,kCAAA;AAAA,cAAC;AAAA,cAAA;AAAA,gBACK,MAAK;AAAA,gBACL,IAAG;AAAA,gBACH,MAAK;AAAA,gBACL,aAAY;AAAA,gBACZ,OAAO,SAAS;AAAA,gBAChB,UAAU,CAAC,MAAM,YAAY,EAAE,GAAG,UAAU,KAAK,EAAE,OAAO,OAAO;AAAA,gBACjE,KAAI;AAAA,gBACJ,UAAQ;AAAA,gBACR,WAAU;AAAA,cAAA;AAAA,YAAA;AAAA,UAChB,EACN,CAAA;AAAA,QAAA,EAEN,CAAA;AAAA,MAAA,EACN,CAAA;AAAA,IAAA,GACN;AAAA,IACCC,kCAAA,KAAA,MAAA,EAAK,QAAO,QAAO,WAAU,oDACxB,UAAA;AAAA,MAACD,kCAAAA,IAAA,SAAA,EAAM,MAAK,UAAS,MAAK,WAAU,OAAO,yCAAY,YAAY;AAAA,MACnEA,kCAAAA,IAAC,WAAM,MAAK,UAAS,MAAK,eAAc,OAAO,mCAAS,YAAY;AAAA,MACpEA,kCAAAA,IAAC,SAAM,EAAA,MAAK,UAAS,MAAK,aAAY,QAAO,cAAS,cAAT,mBAAoB,WAAY,CAAA;AAAA,MAC7EA,kCAAAA,IAAC,SAAM,EAAA,MAAK,UAAS,MAAK,aAAY,QAAO,cAAS,cAAT,mBAAoB,WAAY,CAAA;AAAA,MAC7EA,kCAAAA,IAAC,SAAM,EAAA,MAAK,UAAS,MAAK,WAAU,QAAO,cAAS,YAAT,mBAAkB,WAAY,CAAA;AAAA,MACzEA,kCAAAA,IAAC,WAAM,MAAK,UAAS,MAAK,YAAW,OAAO,qCAAU,YAAY;AAAA,MAClEA,kCAAAA,IAAC,SAAM,EAAA,MAAK,UAAS,MAAK,QAAO,QAAO,cAAS,SAAT,mBAAe,WAAY,CAAA;AAAA,MACnEA,kCAAAA,IAAC,SAAM,EAAA,MAAK,UAAS,MAAK,eAAc,QAAO,cAAS,gBAAT,mBAAsB,WAAY,CAAA;AAAA,MACjFA,kCAAAA,IAAC,SAAM,EAAA,MAAK,UAAS,MAAK,OAAM,QAAO,cAAS,QAAT,mBAAc,WAAY,CAAA;AAAA,MAChE,UAAWA,kCAAAA,IAAA,SAAA,EAAM,MAAK,UAAS,MAAK,mBAAkB,QAAO,eAAU,OAAV,mBAAc,WAAY,CAAA;AAAA,MAExFA,kCAAAA,IAAC,WAAM,MAAK,UAAS,MAAK,aAAY,OAAO,uDAAmB,YAAY;AAAA,4CAC3E,SAAM,EAAA,MAAK,UAAS,MAAK,cAAa,OAAO,2BAA2B;AAAA,MACzEA,kCAAAA,IAAC,WAAM,MAAK,UAAS,MAAK,QAAO,OAAO,SAAS,aAAa,GAAI,CAAA;AAAA,MAElEA,kCAAA;AAAA,QAAC;AAAA,QAAA;AAAA,UACK,SAAS;AAAA,UACT,WAAU;AAAA,UACf,UAAA;AAAA,QAAA;AAAA,MAED;AAAA,MACAA,kCAAA;AAAA,QAAC;AAAA,QAAA;AAAA,UACK,MAAK;AAAA,UACL,UAAU,eAAe;AAAA,UACzB,WAAW,4HAA4H,eAAe,OAAO,mCAAmC,+BAA+B;AAAA,UACpO,UAAA;AAAA,QAAA;AAAA,MAAA;AAAA,IAED,EACN,CAAA;AAAA,EAAA,EAAA,CACN,EACN,CAAA;AAEZ;AC1KMG,MAAAA,oBAA8BA,MAAM;AACpC,QAAM;AAAA,IAAEC;AAAAA,IAAuBC;AAAAA,IAAeC;AAAAA,IAAUC;AAAAA,MAAgBC,cAA0B;AAClG,QAAMC,WAAWC,YAAY;AAC7B,QAAMC,kBAAkBC,WAAuB;AAEvCC,UAAAC,IAAIV,uBAAuB,gBAAgB;AAEnD,QAAM,CAACW,8BAA8BC,+BAA+B,IAAIpB,aAAAA,SAAS,KAAK;AACtF,QAAM,CAACqB,eAAeC,gBAAgB,IAAItB,sBAAqB;AAC/D,QAAMuB,aAAaC,cAA0B;AAC7C,QAAM,CAACC,YAAYC,aAAa,IAAI1B,aAAAA,SAAS,KAAK;AAElD,QAAM,CAAC2B,oBAAoBC,qBAAqB,IAAI5B,sBAAwB;AAC5E,QAAM6B,sBAAsB,CACtB,MACA,QACA,eACA,OACA,aACA,aACA,IACA,EAAA;AAEN5B,eAAAA,UAAU,MAAM;AACN,QAAAc,gBAAgBe,UAAU,QAAQ;AAEhC,UAAIP,yCAAYI,oBAAoB;AAC9BC,8BAAsBL,WAAWI,kBAAkB;AACnDP,wCAAgC,IAAI;AACpCM,sBAAc,KAAK;AAAA,MAEzB,OACK;AACCE,8BAAsB,CAAA,CAAE;AAExBR,wCAAgC,KAAK;AACrCM,sBAAc,KAAK;AAAA,MAEzB;AAAA,IACN;AAAA,EAEN,GAAG,CAACH,UAAU,CAAC;AAET,QAAAQ,0BAA2BC,SAAoB;AAC/CV,qBAAiBU,GAAG;AACpBN,kBAAc,IAAI;AAClBN,oCAAgC,IAAI;AAAA,EAC1C;AACM,QAAAa,eAAgBC,mBAA8B;AACxC,UAAAC,WAAW,IAAIC,SAAS;AACrBD,aAAAE,OAAO,cAAc,iBAAiB;AAC/CF,aAASE,OAAO,YAAYH,+CAAeI,GAAGC,UAAU;AACxDJ,aAASE,OAAO,cAAc1B,YAAY4B,SAAA,CAAU;AACpDJ,aAASE,OAAO,YAAY3B,SAAS6B,SAAA,CAAU;AAE/CxB,oBAAgByB,OAAOL,UAAU;AAAA,MAAEM,QAAQ;AAAA,IAAO,CAAC;AAAA,EACzD;AAEA,QAAM,CAACC,YAAYC,aAAa,IAAI3C,aAAAA,SAAS,EAAE;AAC/C,QAAM,CAAC4C,oBAAoBC,qBAAqB,IAAI7C,aAAAA,SAAuB,CAAA,CAAE;AACvE,QAAA8C,UAAU/B,gBAAgBe,UAAU;AACpC,QAAAiB,sBAAsBC,YAAYN,YAAY,GAAG;AAEvDzC,eAAAA,UAAU,MAAM;AACV,QAAI8C,oBAAoBE,UAAU,KAAKF,wBAAwB,IAAI;AAC7D,YAAMG,WAAW1C,sBAAsB2C,OAAQC,UACzC,CAACA,KAAKC,MAAMD,KAAKE,WAAW,EAAEC,KACvBC,WAAUA,+BAAOC,cAAcC,SAASX,oBAAoBU,YAAa,EAChF,CACN;AACAZ,4BAAsBK,QAAQ;AAAA,IACpC,OACK;AACCL,4BAAsBrC,qBAAqB;AAAA,IACjD;AAAA,EAEN,GAAG,CAACuC,qBAAqBvC,qBAAqB,CAAC;AAEzC,SAAAH,kCAAAA,KAAC,OAAI;AAAA,IAAAsD,WAAU;AAAA,IACRC,UAAA,CAAAd,WAAY1C,kCAAA,IAAAyD,eAAA;AAAA,MAAcf;AAAAA,MAAkBgB,MAAM;AAAA,IAAI,CAAA,GAEvDzD,kCAAA,KAAC;MAAGsD,WAAU;AAAA,MAAqEI,SAASA,MAAMlD,SAAS,EAAE;AAAA,MAAG+C,UAAA,CAAA,KAAExD,kCAAA,IAAA,QAAA;AAAA,QAAKuD,WAAU;AAAA,QAAWC,UAAkB;AAAA,OAAA,GAAO,KAACvD,kCAAA,KAAC,QAAK;AAAA,QAAAsD,WAAU;AAAA,QAAWC,UAAA,CAAAnD,eAAc,GAAA;AAAA,MAAC,CAAA,GAAO,GAAA;AAAA,IAAC,CAAA,GACxNL,kCAAA,IAAC,OAAI;AAAA,MAAAuD,WAAU;AAAA,MAETC,UAAAxD,kCAAA,IAAC4D,OAAA;AAAA,QACKC,aAAY;AAAA,QACZC,OAAOxB;AAAAA,QACPyB,MAAK;AAAA,QACLC,UAAWC,OAAM1B,cAAc0B,EAAEC,OAAOJ,KAAK;AAAA,QAC7CP,WAAU;AAAA,MAChB,CAAA;AAAA,IACN,CAAA,GAGAvD,kCAAA,IAACmE,iBAAA;AAAA,MACKC,SAAS3C;AAAAA,MACT4C,MAAM7B;AAAAA,MACN8B,WAAY1C,SACL3B,kCAAA,KAAA,MAAA;AAAA,QAAgBsD,WAAU;AAAA,QACrBC,UAAA,CAAAxD,kCAAA,IAAC,MAAG;AAAA,UAAAuD,WAAU;AAAA,UAAwDC,UAAA5B,IAAIM;AAAAA,QAAG,CAAA,GAC5ElC,kCAAA,IAAA,MAAA;AAAA,UAAGuD,WAAU;AAAA,UAAwDC,qCAAKP;AAAAA,QAAK,CAAA,GAC/EjD,kCAAA,IAAA,MAAA;AAAA,UAAGuD,WAAU;AAAA,UAAuDC,qCAAKN;AAAAA,QAAY,CAAA,GACrFlD,kCAAA,IAAA,MAAA;AAAA,UAAGuD,WAAU;AAAA,UAAuDC,qCAAKe;AAAAA,QAAI,CAAA,GAC7EvE,kCAAA,IAAA,MAAA;AAAA,UAAGuD,WAAU;AAAA,UAAuDC,qCAAKgB;AAAAA,QAAU,CAAA,GACnFxE,kCAAA,IAAA,MAAA;AAAA,UAAGuD,WAAU;AAAA,UAAuDC,qCAAKgB;AAAAA,QAAU,CAAA,GAEpFxE,kCAAA,IAAC,MAAG;AAAA,UAAAuD,WAAU;AAAA,UACRC,UAAAxD,kCAAA,IAACyE,QAAA;AAAA,YACKC,SAAQ;AAAA,YACRhB,MAAK;AAAA,YACLH,WAAU;AAAA,YACVI,SAASA,MAAM;AACL,kBAAAgB,QAAQ,wCAAwC,GAAG;AACjD9C,6BAAaD,GAAG;AAAA,cACtB;AAAA,YACN;AAAA,YACAgD,OAAO;AAAA,cAAEC,WAAW;AAAA,YAAW;AAAA,YAE/BrB,UAAAxD,kCAAA,IAAC8E,OAAM;AAAA,cAAApB,MAAM;AAAA,YAAI,CAAA;AAAA,UACvB,CAAA;AAAA,QACN,CAAA,GACC1D,kCAAA,IAAA,MAAA;AAAA,UAAGuD,WAAU;AAAA,UACRC,gDAACuB,QAAO;AAAA,YAAAC,OAAM;AAAA,YAAOtB,MAAM;AAAA,YAAIC,SAASA,MAAMhC,wBAAwBC,GAAG;AAAA,UAAG,CAAA;AAAA,QAClF,CAAA,CAAA;AAAA,MAAA,GAzBGA,IAAIM,EA0Bb;AAAA,IAAA,CAEZ,GACAjC,kCAAA,KAACgF,MAAK;AAAA,MAAA5C,QAAO;AAAA,MACPmB,UAAA,CAAAxD,kCAAA,IAAC;QAAMiD,MAAK;AAAA,QAAWa,OAAOxD;AAAAA,QAAU4E,QAAM;AAAA,MAAC,CAAA,yCAC9C,SAAM;AAAA,QAAAjC,MAAK;AAAA,QAAUa,OAAO;AAAA,QAAIoB,QAAM;AAAA,MAAC,CAAA,yCACvC,SAAM;AAAA,QAAAjC,MAAK;AAAA,QAAaa,OAAO;AAAA,QAAaoB,QAAM;AAAA,MAAC,CAAA,GAEpDlF,kCAAA,IAACyE,QAAA;AAAA,QACKlB,WAAU;AAAA,QACVQ,MAAK;AAAA,QACVP,UAAA;AAAA,MAAA,CAED,CAAA;AAAA,IACN,CAAA,GACAxD,kCAAA,IAACmF,yBAAA;AAAA,MACKC,QAAQrE;AAAAA,MACRsE,OAAO9D,sBAAsB,CAAC;AAAA,MAC9B+D,SAASA,MAAMtE,gCAAgC,KAAK;AAAA,MACpDuE,QAAQlE,aAAa,iBAAiBhB,+CAAemF,MAAM,GAAG,GAAG,KAAK,oBAAoBnF,+CAAemF,MAAM,GAAG,GAAG;AAAA,MAAKC,WAAWxE;AAAAA,MACrIX;AAAAA,MACAoF,SAASnF;AAAAA,MACToF,QAAQtE;AAAAA,IAAA,CACd,CAAA;AAAA,EACN,CAAA;AAEZ;"}