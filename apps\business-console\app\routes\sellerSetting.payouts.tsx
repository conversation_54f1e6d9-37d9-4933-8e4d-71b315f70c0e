import { useLoaderData } from "@remix-run/react";
import { metabaseService } from "../utils/metabase";
import { withAuth, withResponse } from "../utils/auth-utils";

export const loader = withAuth(async ({ user }) => {
  const embedUrl = metabaseService.generateDashboardUrl(9, {
    seller_id_: user.sellerId,
  });
  
  return withResponse({ embedUrl });
});

export default function Payouts() {
  const { embedUrl } = useLoaderData<typeof loader>();

  return (
    <div className="p-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-gray-900">Payouts</h1>
        <p className="text-gray-600 mt-2">Manage your earnings and payments</p>
      </div>
      
      <div className="w-full h-screen">
        {embedUrl ? (
          <iframe
            id="metabase-payouts-iframe"
            src={embedUrl}
            title="Payouts Dashboard"
            className="w-full h-full border-0"
          />
        ) : (
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="text-center">
              <div className="mx-auto mb-4 w-16 h-16 bg-gradient-to-r from-green-500 to-yellow-500 rounded-full flex items-center justify-center">
                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
                </svg>
              </div>
              <h2 className="text-2xl font-bold mb-2">Payouts Dashboard Loading</h2>
              <p className="text-gray-500">
                Loading your payouts and earnings data...
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
} 