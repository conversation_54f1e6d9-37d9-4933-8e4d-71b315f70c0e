import { r as reactExports, j as jsxRuntimeExports } from "./jsx-runtime-bB2y7OuD.js";
import { T as Tabs, a as TabsList, b as TabsTrigger, c as TabsContent } from "./tabs-CfSdyzWr.js";
import { R as ResponsiveTable } from "./responsiveTable-CMOWL3Wl.js";
import { B as Button } from "./button-ByAXMyvk.js";
import { S as SpinnerLoader } from "./SpinnerLoader-CAZRmIJX.js";
import { D as Dialog, a as DialogContent, b as DialogTitle } from "./dialog-BqKosxNq.js";
import { c as useSubmit, d as useActionData, F as Form, a as useFetcher, u as useLoaderData, b as useSearchParams } from "./components-D7UvGag_.js";
import { S as Search } from "./search-DzDJ71yc.js";
import { S as SquareX } from "./square-x-CN_sBqMN.js";
import { L as LoaderCircle } from "./loader-circle-BLZgch8Y.js";
import { u as useDebounce } from "./useDebounce-BXbH_IFZ.js";
import { a as useNavigate } from "./index-DhHTcibu.js";
import { X } from "./x-CCG_WJDF.js";
import { T as Trash } from "./trash-cZnr6Uhr.js";
import { P as Pencil } from "./pencil-C1goHmP8.js";
import "./index-D7VH9Fc8.js";
import "./index-CG37gmC0.js";
import "./index-qCtcpOcW.js";
import "./index-z_byfFrQ.js";
import "./index-DVTNuYOr.js";
import "./index-CzZ6EYkN.js";
import "./index-Vp2vNLNM.js";
import "./index-dIGjFc0I.js";
import "./index-BTdCMChR.js";
import "./index-QLGF6kQx.js";
import "./utils-GkgzjW3c.js";
import "./index-ImHKLo0a.js";
import "./index-DdafHWkt.js";
import "./index-BXzPK7u0.js";
import "./index-CEHS9zzk.js";
import "./createLucideIcon-uwkRm45G.js";
const SelectedItemAddons = ({
  isOpen,
  items,
  onClose,
  header,
  groupData,
  sellerId,
  groupId,
  isEdit
}) => {
  var _a, _b, _c, _d, _e, _f, _g;
  const [selectedId, setSelectedId] = reactExports.useState(null);
  const [searchTerm, setSearchTerm] = reactExports.useState("");
  const [filteredAddon, setFilteredAddon] = reactExports.useState(items);
  const [choosenAddon, setChoosenAddon] = reactExports.useState(false);
  const [choosenAddonName, setChoosenAddonName] = reactExports.useState("");
  useSubmit();
  const actionData = useActionData();
  const [formData, setFormData] = reactExports.useState({
    sId: "",
    name: "",
    minSelect: 0,
    maxSelect: 0,
    description: "",
    seq: 0,
    varient: false
  });
  reactExports.useEffect(() => {
    if (groupData) {
      setFormData({
        id: groupData.id,
        sId: groupData.sId,
        name: groupData.name || "",
        minSelect: typeof groupData.minSelect === "number" ? groupData.minSelect : 0,
        maxSelect: typeof groupData.maxSelect === "number" ? groupData.maxSelect : 0,
        description: groupData.description || "",
        seq: typeof groupData.seq === "number" ? groupData.seq : 0,
        varient: !!groupData.varient
      });
      if (groupData.sId) {
        setSelectedId(groupData.sId);
        setChoosenAddon(true);
        const selectedAddon = items.find((item) => item.id === Number(groupData.sId));
        if (selectedAddon) {
          setChoosenAddonName(selectedAddon.displayName);
        }
      }
    } else {
      setSelectedId(null);
      setChoosenAddon(false);
      setChoosenAddonName("");
      setFormData({
        sId: "",
        name: "",
        minSelect: 0,
        maxSelect: 0,
        description: "",
        seq: 0,
        varient: false
      });
    }
  }, [groupData, items, isOpen]);
  reactExports.useEffect(() => {
    if (searchTerm.length >= 3 && searchTerm !== "") {
      setFilteredAddon(
        items == null ? void 0 : items.filter(
          (addon) => addon == null ? void 0 : addon.displayName.toLowerCase().includes(searchTerm.toLowerCase())
        )
      );
    } else {
      setFilteredAddon(items);
    }
  }, [searchTerm, items]);
  reactExports.useEffect(() => {
    if (actionData == null ? void 0 : actionData.success) {
      onClose();
    }
  }, [actionData, onClose]);
  const handleSelect = (addon) => {
    setSelectedId(addon.id);
    setChoosenAddon(true);
    setChoosenAddonName(addon.displayName);
  };
  const deselectAddonGroupMap = () => {
    setSelectedId(null);
    setChoosenAddon(false);
    setChoosenAddonName("");
  };
  const handleCancel = () => {
    if (choosenAddon || selectedId) {
      setSelectedId(null);
      setChoosenAddon(false);
      setChoosenAddonName("");
      setSearchTerm("");
      setFilteredAddon(items);
    } else {
      onClose();
    }
  };
  if (!isOpen) return null;
  return /* @__PURE__ */ jsxRuntimeExports.jsx(Dialog, { open: isOpen, onOpenChange: onClose, children: /* @__PURE__ */ jsxRuntimeExports.jsxs(DialogContent, { className: "w-full max-w-md sm:max-w-lg bg-white rounded-2xl shadow-2xl", children: [
    /* @__PURE__ */ jsxRuntimeExports.jsx(DialogTitle, { className: "text-2xl font-bold text-gray-900 mb-4", children: header }),
    /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "space-y-6", children: [
      !choosenAddon && selectedId === null && /* @__PURE__ */ jsxRuntimeExports.jsxs(jsxRuntimeExports.Fragment, { children: [
        /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "relative", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "absolute inset-y-0 left-3 flex items-center pointer-events-none", children: /* @__PURE__ */ jsxRuntimeExports.jsx(Search, { className: "h-5 w-5 text-gray-400" }) }),
          /* @__PURE__ */ jsxRuntimeExports.jsx(
            "input",
            {
              placeholder: "Search by Addon Name",
              type: "search",
              className: "w-full pl-10 p-3 rounded-full border-2 border-gray-200 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-colors",
              autoFocus: true,
              value: searchTerm,
              onChange: (e) => setSearchTerm(e.target.value)
            }
          )
        ] }),
        /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "mt-4 max-h-[40vh] overflow-y-auto pr-2 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100", children: /* @__PURE__ */ jsxRuntimeExports.jsx("ul", { className: "space-y-2", children: filteredAddon.length === 0 ? /* @__PURE__ */ jsxRuntimeExports.jsx("p", { className: "p-4 text-gray-500 text-center", children: "No add-ons-group found" }) : filteredAddon == null ? void 0 : filteredAddon.map((item) => /* @__PURE__ */ jsxRuntimeExports.jsxs("li", { className: "flex items-center gap-3", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx(
            "input",
            {
              type: "checkbox",
              id: `item-${item.id}`,
              name: "selectedItem",
              value: item.id,
              checked: selectedId === item.id,
              onChange: () => handleSelect(item),
              className: "h-5 w-5 text-blue-600 focus:ring-blue-500 rounded"
            }
          ),
          /* @__PURE__ */ jsxRuntimeExports.jsxs(
            "label",
            {
              htmlFor: `item-${item.id}`,
              className: `cursor-pointer p-3 w-full rounded-lg border ${selectedId === item.id ? "bg-blue-50 border-blue-200" : "border-gray-200"} text-gray-800 hover:bg-gray-50 transition-colors`,
              children: [
                item == null ? void 0 : item.displayName,
                " ",
                /* @__PURE__ */ jsxRuntimeExports.jsxs("span", { className: "text-gray-500", children: [
                  "(",
                  item == null ? void 0 : item.internalName,
                  ")"
                ] })
              ]
            }
          )
        ] }, item.id)) }) })
      ] }),
      (choosenAddon || selectedId) && /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "space-y-4", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center justify-between bg-blue-50 p-3 rounded-lg", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx("p", { className: "font-medium text-gray-800 truncate max-w-[80%]", children: choosenAddonName }),
          /* @__PURE__ */ jsxRuntimeExports.jsx(
            SquareX,
            {
              color: "red",
              className: "cursor-pointer hover:scale-110 transition-transform",
              onClick: deselectAddonGroupMap
            }
          )
        ] }),
        /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "grid grid-cols-1 sm:grid-cols-2 gap-4", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { children: [
            /* @__PURE__ */ jsxRuntimeExports.jsxs(
              "label",
              {
                htmlFor: "name",
                className: "block text-sm font-medium text-gray-700",
                children: [
                  "Name ",
                  /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "text-red-500", children: "*" })
                ]
              }
            ),
            /* @__PURE__ */ jsxRuntimeExports.jsx(
              "input",
              {
                type: "text",
                name: "name",
                id: "name",
                value: formData.name,
                onChange: (e) => setFormData({ ...formData, name: e.target.value }),
                className: "mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm",
                placeholder: "Enter name",
                required: true
              }
            )
          ] }),
          /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { children: [
            /* @__PURE__ */ jsxRuntimeExports.jsxs(
              "label",
              {
                htmlFor: "seq",
                className: "block text-sm font-medium text-gray-700 mb-1",
                children: [
                  "Sequence ",
                  /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "text-red-500", children: "*" })
                ]
              }
            ),
            /* @__PURE__ */ jsxRuntimeExports.jsx(
              "input",
              {
                type: "number",
                id: "seq",
                name: "seq",
                value: formData.seq,
                onChange: (e) => setFormData({
                  ...formData,
                  seq: e.target.value === "" ? 0 : Number(e.target.value)
                }),
                min: "0",
                required: true,
                className: "w-full p-2 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none"
              }
            )
          ] }),
          /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx(
              "label",
              {
                htmlFor: "minSelect",
                className: "block text-sm font-medium text-gray-700",
                children: "Minimum Select"
              }
            ),
            /* @__PURE__ */ jsxRuntimeExports.jsx(
              "input",
              {
                type: "number",
                name: "minSelect",
                id: "minSelect",
                value: formData.minSelect,
                onChange: (e) => setFormData({
                  ...formData,
                  minSelect: e.target.value === "" ? 0 : Number(e.target.value)
                }),
                className: "mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm",
                min: "0"
              }
            )
          ] }),
          /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx(
              "label",
              {
                htmlFor: "maxSelect",
                className: "block text-sm font-medium text-gray-700",
                children: "Maximum Select"
              }
            ),
            /* @__PURE__ */ jsxRuntimeExports.jsx(
              "input",
              {
                type: "number",
                name: "maxSelect",
                id: "maxSelect",
                value: formData.maxSelect,
                onChange: (e) => setFormData({
                  ...formData,
                  maxSelect: e.target.value === "" ? 0 : Number(e.target.value)
                }),
                className: "mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm",
                min: "0"
              }
            )
          ] }),
          /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "sm:col-span-2", children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx(
              "label",
              {
                htmlFor: "description",
                className: "block text-sm font-medium text-gray-700",
                children: "Description"
              }
            ),
            /* @__PURE__ */ jsxRuntimeExports.jsx(
              "textarea",
              {
                name: "description",
                id: "description",
                value: formData.description,
                onChange: (e) => setFormData({ ...formData, description: e.target.value }),
                className: "mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm",
                placeholder: "Enter description",
                rows: 3
              }
            )
          ] })
        ] })
      ] })
    ] }),
    /* @__PURE__ */ jsxRuntimeExports.jsxs(Form, { method: "POST", className: "mt-6 flex flex-col sm:flex-row gap-3 justify-end", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx("input", { type: "hidden", name: "sId", value: selectedId == null ? void 0 : selectedId.toString() }),
      /* @__PURE__ */ jsxRuntimeExports.jsx("input", { type: "hidden", name: "itemId", value: groupId == null ? void 0 : groupId.toString() }),
      /* @__PURE__ */ jsxRuntimeExports.jsx("input", { type: "hidden", name: "minSelect", value: (_a = formData.minSelect) == null ? void 0 : _a.toString() }),
      /* @__PURE__ */ jsxRuntimeExports.jsx("input", { type: "hidden", name: "name", value: (_b = formData.name) == null ? void 0 : _b.toString() }),
      /* @__PURE__ */ jsxRuntimeExports.jsx("input", { type: "hidden", name: "maxSelect", value: (_c = formData.maxSelect) == null ? void 0 : _c.toString() }),
      /* @__PURE__ */ jsxRuntimeExports.jsx("input", { type: "hidden", name: "varient", value: (_d = formData.varient) == null ? void 0 : _d.toString() }),
      /* @__PURE__ */ jsxRuntimeExports.jsx("input", { type: "hidden", name: "description", value: (_e = formData.description) == null ? void 0 : _e.toString() }),
      /* @__PURE__ */ jsxRuntimeExports.jsx("input", { type: "hidden", name: "seq", value: (_f = formData.seq) == null ? void 0 : _f.toString() }),
      /* @__PURE__ */ jsxRuntimeExports.jsx("input", { type: "hidden", name: "sellerId", value: sellerId == null ? void 0 : sellerId.toString() }),
      /* @__PURE__ */ jsxRuntimeExports.jsx("input", { type: "hidden", name: "addonName", value: choosenAddonName == null ? void 0 : choosenAddonName.toString() }),
      /* @__PURE__ */ jsxRuntimeExports.jsx("input", { type: "hidden", name: "actionType", value: "actionItemAddonGroup" }),
      /* @__PURE__ */ jsxRuntimeExports.jsx("input", { type: "hidden", name: "mode", value: isEdit ? "EditMode" : "" }),
      isEdit && /* @__PURE__ */ jsxRuntimeExports.jsx("input", { type: "hidden", name: "selectedId", value: (_g = groupData == null ? void 0 : groupData.id) == null ? void 0 : _g.toString() }),
      /* @__PURE__ */ jsxRuntimeExports.jsx(
        "button",
        {
          type: "button",
          onClick: handleCancel,
          className: "w-full sm:w-auto px-4 py-2 rounded-lg bg-gray-100 text-gray-700 hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-300 text-sm font-medium transition-colors",
          children: "Cancel"
        }
      ),
      /* @__PURE__ */ jsxRuntimeExports.jsx(
        "button",
        {
          type: "submit",
          disabled: !selectedId || !formData.name,
          className: `w-full sm:w-auto px-4 py-2 rounded-lg text-sm font-medium text-white focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors ${!selectedId || !formData.name ? "bg-gray-400 cursor-not-allowed" : "bg-blue-600 hover:bg-blue-700"}`,
          children: "Confirm"
        }
      )
    ] })
  ] }) });
};
const SelectedItemVariation = ({
  isOpen,
  items,
  onClose,
  header,
  groupData,
  sellerId,
  groupId,
  isEdit
}) => {
  var _a, _b, _c, _d, _e, _f;
  const [selectedId, setSelectedId] = reactExports.useState(null);
  const [searchTerm, setSearchTerm] = reactExports.useState("");
  const [filteredVariation, setFilteredVariation] = reactExports.useState(items);
  const [choosenAddon, setChoosenAddon] = reactExports.useState(false);
  const [choossenAddonName, setChoosenAddonName] = reactExports.useState("");
  const [formData, setFormData] = reactExports.useState({
    id: groupData == null ? void 0 : groupData.id,
    sId: (groupData == null ? void 0 : groupData.sId) || selectedId,
    name: (groupData == null ? void 0 : groupData.name) || "",
    groupName: (groupData == null ? void 0 : groupData.groupName) || "",
    price: (groupData == null ? void 0 : groupData.price) || 0,
    strikeoffPrice: (groupData == null ? void 0 : groupData.strikeoffPrice) || 0,
    seq: (groupData == null ? void 0 : groupData.seq) || 0
  });
  reactExports.useEffect(() => {
    if (searchTerm.length >= 3) {
      setFilteredVariation(
        items.filter(
          (item) => item.displayName.toLowerCase().includes(searchTerm.toLowerCase())
        )
      );
    } else {
      setFilteredVariation(items);
    }
  }, [searchTerm, items]);
  reactExports.useEffect(() => {
    if (!isOpen) {
      setSelectedId(null);
      setSearchTerm("");
      setChoosenAddon(false);
      setChoosenAddonName("");
    }
  }, [!isOpen]);
  reactExports.useEffect(() => {
    if (groupData) {
      setFormData({
        id: (groupData == null ? void 0 : groupData.id) || 0,
        sId: groupData.sId || "",
        name: groupData.name || "",
        groupName: groupData.groupName || "",
        price: groupData.price || 0,
        strikeoffPrice: groupData.strikeoffPrice || 0,
        seq: groupData.seq || 0
      });
      setSelectedId(groupData.sId || null);
      setChoosenAddon(!!groupData.sId);
      setChoosenAddonName(groupData.name || "");
    }
  }, [groupData]);
  const handleSelect = (variation) => {
    setSelectedId((variation == null ? void 0 : variation.id) ?? 0);
    setChoosenAddon(true);
    setChoosenAddonName(variation.displayName);
  };
  const deselectAddongroupMap = () => {
    setSelectedId(null);
    setChoosenAddon(false);
  };
  const fetcher = useFetcher();
  const loading = fetcher.state != "idle";
  const actionData = useActionData();
  reactExports.useEffect(() => {
    if (actionData == null ? void 0 : actionData.sucess) {
      console.log(actionData.sucess, "ppppppppp");
      onClose();
    }
  }, [actionData, onClose]);
  if (!isOpen) return null;
  return /* @__PURE__ */ jsxRuntimeExports.jsx(Dialog, { open: isOpen, onOpenChange: onClose, children: /* @__PURE__ */ jsxRuntimeExports.jsxs(DialogContent, { className: "w-full max-w-md sm:max-w-lg bg-white rounded-2xl shadow-2xl", children: [
    /* @__PURE__ */ jsxRuntimeExports.jsx(DialogTitle, { className: "text-2xl font-bold text-gray-900 mb-4", children: header }),
    /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "space-y-6", children: [
      choosenAddon === false && selectedId === null && /* @__PURE__ */ jsxRuntimeExports.jsxs(jsxRuntimeExports.Fragment, { children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx("div", { children: /* @__PURE__ */ jsxRuntimeExports.jsx(
          "input",
          {
            placeholder: "Search by Addon Name",
            type: "search",
            className: "w-full p-3 rounded-full border-2 border-gray-200 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-colors",
            autoFocus: true,
            value: searchTerm,
            onChange: (e) => setSearchTerm(e.target.value)
          }
        ) }),
        /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "mt-4 max-h-[40vh] overflow-y-auto pr-2 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100", children: /* @__PURE__ */ jsxRuntimeExports.jsx("ul", { className: "space-y-2", children: filteredVariation.length === 0 ? /* @__PURE__ */ jsxRuntimeExports.jsx("p", { className: "p-4 text-gray-500 text-center", children: "No add-ons-group found" }) : filteredVariation.map((item) => /* @__PURE__ */ jsxRuntimeExports.jsxs("li", { className: "flex items-center gap-3", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx(
            "input",
            {
              type: "checkbox",
              id: `item-${item.id}`,
              name: "selectedItem",
              value: item.id,
              checked: selectedId === item.id,
              onChange: () => handleSelect(item),
              className: "h-5 w-5 text-blue-600 focus:ring-blue-500 rounded"
            }
          ),
          /* @__PURE__ */ jsxRuntimeExports.jsxs(
            "label",
            {
              htmlFor: `item-${item.id}`,
              className: `cursor-pointer p-3 w-full rounded-lg border ${selectedId === item.id ? "bg-blue-50 border-blue-200" : "border-gray-200"} text-gray-800 hover:bg-gray-50 transition-colors`,
              children: [
                item == null ? void 0 : item.displayName,
                " ",
                /* @__PURE__ */ jsxRuntimeExports.jsxs("span", { className: "text-gray-500", children: [
                  "(",
                  item == null ? void 0 : item.internalName,
                  ")"
                ] })
              ]
            }
          )
        ] }, item.id)) }) })
      ] }),
      choosenAddon && selectedId && /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "space-y-4", children: [
        selectedId && /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center justify-between bg-blue-50 p-3 rounded-lg", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx("p", { className: "font-medium text-gray-800 truncate max-w-[80%]", children: choossenAddonName }),
          /* @__PURE__ */ jsxRuntimeExports.jsx(
            SquareX,
            {
              color: "red",
              className: "cursor-pointer hover:scale-110 transition-transform",
              onClick: () => deselectAddongroupMap()
            }
          )
        ] }),
        /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "grid grid-cols-1 sm:grid-cols-2 gap-4", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx("label", { htmlFor: "name", className: "block text-sm font-medium text-gray-700", children: "Name" }),
            /* @__PURE__ */ jsxRuntimeExports.jsx(
              "input",
              {
                type: "text",
                name: "name",
                id: "name",
                value: formData.name,
                onChange: (e) => setFormData({ ...formData, name: e.target.value }),
                className: "mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm",
                placeholder: "Enter name"
              }
            )
          ] }),
          /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx("label", { htmlFor: "seq", className: "block text-sm font-medium text-gray-700 mb-1", children: "Sequence" }),
            /* @__PURE__ */ jsxRuntimeExports.jsx(
              "input",
              {
                type: "number",
                id: "seq",
                name: "seq",
                value: formData.seq,
                onChange: (e) => setFormData({ ...formData, seq: Number(e.target.value) }),
                min: "0",
                required: true,
                className: "w-full p-2 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none"
              }
            )
          ] }),
          /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx("label", { htmlFor: "groupName", className: "block text-sm font-medium text-gray-700", children: "groupName                                                " }),
            /* @__PURE__ */ jsxRuntimeExports.jsx(
              "input",
              {
                type: "text",
                name: "groupName",
                id: "groupName",
                value: formData.groupName,
                onChange: (e) => setFormData({ ...formData, groupName: e.target.value }),
                className: "mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm"
              }
            )
          ] }),
          /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx("label", { htmlFor: "price", className: "block text-sm font-medium text-gray-700", children: "price" }),
            /* @__PURE__ */ jsxRuntimeExports.jsx(
              "input",
              {
                type: "number",
                name: "price",
                id: "price",
                value: formData.price,
                onChange: (e) => setFormData({ ...formData, price: Number(e.target.value) }),
                className: "mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm",
                min: "0"
              }
            )
          ] }),
          /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx("label", { htmlFor: "strikeoffPrice", className: "block text-sm font-medium text-gray-700", children: "strikeoffPrice" }),
            /* @__PURE__ */ jsxRuntimeExports.jsx(
              "input",
              {
                type: "number",
                name: "strikeoffPrice",
                id: "strikeoffPrice",
                value: formData.strikeoffPrice,
                onChange: (e) => setFormData({ ...formData, strikeoffPrice: Number(e.target.value) }),
                className: "mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm",
                min: "0"
              }
            )
          ] })
        ] })
      ] })
    ] }),
    /* @__PURE__ */ jsxRuntimeExports.jsxs(Form, { method: "POST", className: "mt-6 flex flex-col sm:flex-row gap-3 justify-end", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx("input", { type: "hidden", name: "sId", value: selectedId == null ? void 0 : selectedId.toString(), readOnly: true }),
      /* @__PURE__ */ jsxRuntimeExports.jsx("input", { type: "hidden", name: "itemId", value: groupId == null ? void 0 : groupId.toString(), readOnly: true }),
      /* @__PURE__ */ jsxRuntimeExports.jsx("input", { type: "hidden", name: "groupName", value: (_a = formData.groupName) == null ? void 0 : _a.toString(), readOnly: true }),
      /* @__PURE__ */ jsxRuntimeExports.jsx("input", { type: "hidden", name: "name", value: (_b = formData.name) == null ? void 0 : _b.toString(), readOnly: true }),
      /* @__PURE__ */ jsxRuntimeExports.jsx("input", { type: "hidden", name: "seq", value: (_c = formData.seq) == null ? void 0 : _c.toString(), readOnly: true }),
      /* @__PURE__ */ jsxRuntimeExports.jsx("input", { type: "hidden", name: "price", value: (_d = formData.price) == null ? void 0 : _d.toString(), readOnly: true }),
      /* @__PURE__ */ jsxRuntimeExports.jsx("input", { type: "hidden", name: "strikeoffPrice", value: (_e = formData.strikeoffPrice) == null ? void 0 : _e.toString(), readOnly: true }),
      /* @__PURE__ */ jsxRuntimeExports.jsx("input", { type: "hidden", name: "sellerId", value: sellerId == null ? void 0 : sellerId.toString(), readOnly: true }),
      /* @__PURE__ */ jsxRuntimeExports.jsx("input", { type: "hidden", name: "addonName", value: choossenAddonName == null ? void 0 : choossenAddonName.toString(), readOnly: true }),
      /* @__PURE__ */ jsxRuntimeExports.jsx("input", { type: "hidden", name: "actionType", value: "actionItemVariation", readOnly: true }),
      /* @__PURE__ */ jsxRuntimeExports.jsx("input", { type: "hidden", name: "mode", value: isEdit ? "EditMode" : "" }),
      isEdit && /* @__PURE__ */ jsxRuntimeExports.jsx("input", { type: "hidden", name: "selectedId", value: (_f = groupData == null ? void 0 : groupData.id) == null ? void 0 : _f.toString(), readOnly: true }),
      /* @__PURE__ */ jsxRuntimeExports.jsx(
        "button",
        {
          onClick: onClose,
          className: "w-full sm:w-auto px-4 py-2 rounded-lg bg-gray-100 text-gray-700 hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-300 text-sm font-medium",
          children: "Cancel"
        }
      ),
      /* @__PURE__ */ jsxRuntimeExports.jsx(
        "button",
        {
          type: "submit",
          disabled: selectedId === null || loading,
          className: `w-full sm:w-auto px-4 py-2 rounded-lg text-sm font-medium text-white focus:outline-none focus:ring-2 focus:ring-blue-500 ${selectedId === null ? "bg-gray-400 cursor-not-allowed" : "bg-blue-600 hover:bg-blue-700"}`,
          children: loading ? /* @__PURE__ */ jsxRuntimeExports.jsxs(jsxRuntimeExports.Fragment, { children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx(LoaderCircle, { className: "animate-spin h-5 w-5 mr-2" }),
            "Submitting..."
          ] }) : "Confirm"
        }
      )
    ] })
  ] }) });
};
function SelectedSellerItem() {
  const {
    variationList,
    itemName,
    seAddonGroupData,
    itemId,
    sellerId
  } = useLoaderData();
  const [searchParams] = useSearchParams();
  const activeTab = searchParams.get("activeTab") || "Variations";
  const fetcher = useFetcher();
  const navigate = useNavigate();
  const loading = fetcher.state !== "idle";
  const [variationSearchTerm, setVariationSearchTerm] = reactExports.useState("");
  const [addonGroupSearchTerm, setAddonGroupSearchTerm] = reactExports.useState("");
  const addonGroupDebounce = useDebounce(addonGroupSearchTerm, 300);
  const variationDebounce = useDebounce(variationSearchTerm, 300);
  const [filteredVariations, setFilteredVariations] = reactExports.useState(variationList);
  const [filteredAddonGroups, setFilteredAddonGroups] = reactExports.useState(seAddonGroupData || []);
  const variationHeaders = ["ID", "Name", "Group Name", "Price", "Strike Off Price", "Quantity", "", ""];
  const addonGroupHeaders = ["ID", "Name", "Description", "Min Select", "Max Select", "", "", ""];
  reactExports.useEffect(() => {
    if (variationDebounce.length >= 3 && variationDebounce !== "") {
      const filtered = (variationList || []).filter((item) => {
        var _a, _b;
        return ((_a = item.name) == null ? void 0 : _a.toLowerCase().includes(variationDebounce.toLowerCase())) || ((_b = item.groupName) == null ? void 0 : _b.toLowerCase().includes(variationDebounce.toLowerCase()));
      });
      console.log("Filtered addon groups:", filtered);
      setFilteredVariations(filtered);
    } else {
      setFilteredVariations(variationList || []);
    }
  }, [variationList, variationDebounce]);
  reactExports.useEffect(() => {
    if (addonGroupDebounce.length >= 3 && addonGroupDebounce !== "") {
      const filtered = (seAddonGroupData || []).filter((item) => {
        var _a, _b;
        return ((_a = item.name) == null ? void 0 : _a.toLowerCase().includes(addonGroupDebounce.toLowerCase())) || ((_b = item.description) == null ? void 0 : _b.toLowerCase().includes(addonGroupDebounce.toLowerCase()));
      });
      console.log("Filtered addon groups:", filtered);
      setFilteredAddonGroups(filtered);
    } else {
      setFilteredAddonGroups(seAddonGroupData || []);
    }
  }, [seAddonGroupData, addonGroupDebounce]);
  reactExports.useEffect(() => {
    setVariationSearchTerm("");
    setAddonGroupSearchTerm("");
  }, [activeTab]);
  const handleTabChange = (newTab) => {
    navigate(`?itemId=${itemId}&itemName=${encodeURIComponent(itemName)}&activeTab=${newTab}&sellerId=${sellerId}`);
  };
  const [itemselectedGroupAddons, setItemselectedGroupAddons] = reactExports.useState(false);
  const [selectedAddonsgData, setSelectedAddonsgData] = reactExports.useState();
  const [selectedItemGdata, setSelectedItemGdata] = reactExports.useState();
  const [selectedItemVarData, setSelectedtemVarData] = reactExports.useState();
  const [selectedVariations, setSelectedVariations] = reactExports.useState();
  const [itemSelectedVariation, setItemSelectedVariation] = reactExports.useState(false);
  const addonitemgfetcher = useFetcher();
  const itemVaritonfetcher = useFetcher();
  const actionData = useActionData();
  const varActionData = useActionData();
  const [isEditopen, setIseditOpen] = reactExports.useState(false);
  const [isVarEditOpen, setIsVarEditOpen] = reactExports.useState(false);
  const handleDelete = (addonsData) => {
    var _a;
    const formData = new FormData();
    formData.append("actionType", "addonGroupDelete");
    formData.append("itemId", itemId.toString());
    formData.append("ItemaddongId", ((_a = addonsData == null ? void 0 : addonsData.id) == null ? void 0 : _a.toString()) ?? "");
    formData.append("sellerId", sellerId.toString());
    addonitemgfetcher.submit(formData, {
      method: "post"
    });
  };
  const handleItemVariationsDelete = (variationData) => {
    var _a;
    const formData = new FormData();
    formData.append("actionType", "itemVarDelete");
    formData.append("itemId", itemId.toString());
    formData.append("itemVarId", ((_a = variationData == null ? void 0 : variationData.id) == null ? void 0 : _a.toString()) ?? "");
    formData.append("sellerId", sellerId.toString());
    itemVaritonfetcher.submit(formData, {
      method: "post"
    });
  };
  reactExports.useEffect(() => {
    if (itemVaritonfetcher.state === "idle") {
      if (varActionData == null ? void 0 : varActionData.itemVariation) {
        setSelectedVariations(varActionData == null ? void 0 : varActionData.itemVariation);
        setItemSelectedVariation(true);
        setIsVarEditOpen(false);
      } else {
        setSelectedVariations([]);
        setItemSelectedVariation(false);
        setIsVarEditOpen(false);
      }
    }
  }, [varActionData]);
  reactExports.useEffect(() => {
    if (addonitemgfetcher.state === "idle") {
      if (actionData == null ? void 0 : actionData.selectedItemGruoupData) {
        setSelectedAddonsgData(actionData.selectedItemGruoupData);
        setItemselectedGroupAddons(true);
        setIseditOpen(false);
      } else {
        setSelectedAddonsgData([]);
        setIseditOpen(false);
        setItemselectedGroupAddons(false);
      }
    }
  }, [actionData]);
  const handleSelectedItemGroupData = (row) => {
    setItemselectedGroupAddons(true);
    setSelectedItemGdata(row);
    setIseditOpen(true);
  };
  const handleSelectedItemVarData = (row) => {
    setItemSelectedVariation(true);
    setSelectedtemVarData(row);
    setIsVarEditOpen(true);
  };
  return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
    children: [loading && /* @__PURE__ */ jsxRuntimeExports.jsx(SpinnerLoader, {
      loading
    }), /* @__PURE__ */ jsxRuntimeExports.jsxs("h1", {
      className: "mb-6 text-2xl font-bold text-gray-900 cursor-pointer hover:text-blue-600 transition-colors",
      onClick: () => navigate(-1),
      children: ["Seller Item / ", itemName]
    }), /* @__PURE__ */ jsxRuntimeExports.jsxs(Tabs, {
      value: activeTab,
      onValueChange: handleTabChange,
      className: "bg-white rounded-lg shadow-sm border border-gray-200",
      children: [/* @__PURE__ */ jsxRuntimeExports.jsxs(TabsList, {
        className: "border-b border-gray-200",
        children: [/* @__PURE__ */ jsxRuntimeExports.jsx(TabsTrigger, {
          value: "Variations",
          className: "px-4 py-2 text-sm font-medium text-gray-700 data-[state=active]:text-blue-600 data-[state=active]:border-b-2 data-[state=active]:border-blue-600",
          children: "Variations"
        }), /* @__PURE__ */ jsxRuntimeExports.jsx(TabsTrigger, {
          value: "AddonsGroups",
          className: "px-4 py-2 text-sm font-medium text-gray-700 data-[state=active]:text-blue-600 data-[state=active]:border-b-2 data-[state=active]:border-blue-600",
          children: "Addons Groups"
        })]
      }), /* @__PURE__ */ jsxRuntimeExports.jsxs(TabsContent, {
        value: "Variations",
        className: "p-4",
        children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
          className: "mb-4 relative",
          children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Search, {
            className: "absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5"
          }), /* @__PURE__ */ jsxRuntimeExports.jsx("input", {
            type: "text",
            placeholder: "Search variations by name or group name...",
            value: variationSearchTerm,
            onChange: (e) => setVariationSearchTerm(e.target.value),
            className: "w-full pl-10 pr-10 py-2 rounded-lg border border-gray-200 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all duration-200 text-sm"
          }), variationSearchTerm && /* @__PURE__ */ jsxRuntimeExports.jsx(X, {
            className: "absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5 cursor-pointer hover:text-gray-600",
            onClick: () => setVariationSearchTerm("")
          })]
        }), /* @__PURE__ */ jsxRuntimeExports.jsx(ResponsiveTable, {
          headers: variationHeaders,
          data: filteredVariations,
          renderRow: (row) => /* @__PURE__ */ jsxRuntimeExports.jsxs("tr", {
            className: "border-b hover:bg-gray-50 transition-colors",
            children: [/* @__PURE__ */ jsxRuntimeExports.jsx("td", {
              className: "py-3 px-4 text-center text-sm text-gray-700",
              children: row == null ? void 0 : row.id
            }), /* @__PURE__ */ jsxRuntimeExports.jsx("td", {
              className: "py-3 px-4 text-center text-sm text-blue-600 cursor-pointer hover:underline",
              onClick: () => navigate(`/home/<USER>
              children: row == null ? void 0 : row.name
            }), /* @__PURE__ */ jsxRuntimeExports.jsx("td", {
              className: "py-3 px-4 text-center text-sm text-gray-700",
              children: (row == null ? void 0 : row.groupName) || "-"
            }), /* @__PURE__ */ jsxRuntimeExports.jsx("td", {
              className: "py-3 px-4 text-center text-sm text-gray-700",
              children: (row == null ? void 0 : row.price) > 0 ? `₹ ${row == null ? void 0 : row.price.toFixed(2)}` : "-"
            }), /* @__PURE__ */ jsxRuntimeExports.jsx("td", {
              className: "py-3 px-4 text-center text-sm text-gray-700",
              children: (row == null ? void 0 : row.strikeoffPrice) > 0 ? `₹ ${row == null ? void 0 : row.strikeoffPrice.toFixed(2)}` : "-"
            }), /* @__PURE__ */ jsxRuntimeExports.jsx("td", {
              className: "py-3 px-4 text-center text-sm text-gray-700",
              children: (row == null ? void 0 : row.qty) > 0 ? `${row == null ? void 0 : row.qty.toFixed(2)}` : "-"
            }), /* @__PURE__ */ jsxRuntimeExports.jsx("td", {
              className: "py-3 px-4 text-center",
              children: /* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
                variant: "ghost",
                size: "sm",
                className: "text-red-500 hover:text-red-700",
                onClick: () => {
                  if (confirm("Are you sure you want to delete this Item Variation?")) {
                    handleItemVariationsDelete(row);
                  }
                },
                children: /* @__PURE__ */ jsxRuntimeExports.jsx(Trash, {
                  size: 18
                })
              })
            }), /* @__PURE__ */ jsxRuntimeExports.jsx("td", {
              className: "py-3 px-4 text-center",
              children: /* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
                variant: "ghost",
                size: "sm",
                className: "text-blue-500 hover:text-blue-700",
                onClick: () => handleSelectedItemVarData(row),
                children: /* @__PURE__ */ jsxRuntimeExports.jsx(Pencil, {
                  size: 18
                })
              })
            })]
          }, row.id)
        }), /* @__PURE__ */ jsxRuntimeExports.jsxs(Form, {
          method: "post",
          children: [/* @__PURE__ */ jsxRuntimeExports.jsx("input", {
            name: "sellerId",
            value: sellerId,
            hidden: true
          }), /* @__PURE__ */ jsxRuntimeExports.jsx("input", {
            name: "matchBy",
            value: "",
            hidden: true
          }), /* @__PURE__ */ jsxRuntimeExports.jsx("input", {
            name: "actionType",
            value: "getItemVariations",
            hidden: true
          }), /* @__PURE__ */ jsxRuntimeExports.jsx("input", {
            name: "pageSize",
            value: "50",
            hidden: true
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
            className: "fixed bottom-5 right-5 rounded-full bg-blue-600 text-white hover:bg-blue-700 shadow-lg transition-all duration-200 px-6 py-3",
            type: "submit",
            children: "+ Add Variation"
          })]
        }), /* @__PURE__ */ jsxRuntimeExports.jsx(SelectedItemVariation, {
          isOpen: itemSelectedVariation,
          items: selectedVariations || [],
          onClose: () => setItemSelectedVariation(false),
          header: isVarEditOpen ? "Edit Item Variation" : "Add Item Variation",
          sellerId,
          groupId: itemId,
          groupData: isVarEditOpen ? selectedItemVarData : void 0,
          isEdit: isVarEditOpen
        })]
      }), /* @__PURE__ */ jsxRuntimeExports.jsxs(TabsContent, {
        value: "AddonsGroups",
        className: "p-4",
        children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
          className: "mb-4 relative",
          children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Search, {
            className: "absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5"
          }), /* @__PURE__ */ jsxRuntimeExports.jsx("input", {
            type: "text",
            placeholder: "Search addon groups by name or description...",
            value: addonGroupSearchTerm,
            onChange: (e) => setAddonGroupSearchTerm(e.target.value),
            className: "w-full pl-10 pr-10 py-2 rounded-lg border border-gray-200 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all duration-200 text-sm"
          }), addonGroupSearchTerm && /* @__PURE__ */ jsxRuntimeExports.jsx(X, {
            className: "absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5 cursor-pointer hover:text-gray-600",
            onClick: () => setAddonGroupSearchTerm("")
          })]
        }), /* @__PURE__ */ jsxRuntimeExports.jsx(ResponsiveTable, {
          headers: addonGroupHeaders,
          data: filteredAddonGroups,
          renderRow: (row) => /* @__PURE__ */ jsxRuntimeExports.jsxs("tr", {
            className: "border-b hover:bg-gray-50 transition-colors",
            children: [/* @__PURE__ */ jsxRuntimeExports.jsx("td", {
              className: "py-3 px-4 text-center text-sm text-gray-700",
              children: row.id
            }), /* @__PURE__ */ jsxRuntimeExports.jsx("td", {
              className: "py-3 px-4 text-center text-sm text-blue-600 cursor-pointer hover:underline",
              children: row.name
            }), /* @__PURE__ */ jsxRuntimeExports.jsx("td", {
              className: "py-3 px-4 text-center text-sm text-gray-700",
              children: row.description || "-"
            }), /* @__PURE__ */ jsxRuntimeExports.jsx("td", {
              className: "py-3 px-4 text-center text-sm text-gray-700",
              children: row.minSelect
            }), /* @__PURE__ */ jsxRuntimeExports.jsx("td", {
              className: "py-3 px-4 text-center text-sm text-gray-700",
              children: row.maxSelect
            }), /* @__PURE__ */ jsxRuntimeExports.jsx("td", {
              className: "py-3 px-4 text-center",
              children: /* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
                variant: "ghost",
                size: "sm",
                className: "text-red-500 hover:text-red-700",
                onClick: () => {
                  if (confirm("Are you sure you want to delete this Item AddonGroup?")) {
                    handleDelete(row);
                  }
                },
                children: /* @__PURE__ */ jsxRuntimeExports.jsx(Trash, {
                  size: 18
                })
              })
            }), /* @__PURE__ */ jsxRuntimeExports.jsx("td", {
              className: "py-3 px-4 text-center",
              children: /* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
                variant: "ghost",
                size: "sm",
                className: "text-blue-500 hover:text-blue-700",
                onClick: () => handleSelectedItemGroupData(row),
                children: /* @__PURE__ */ jsxRuntimeExports.jsx(Pencil, {
                  size: 18
                })
              })
            })]
          }, row.id)
        }), /* @__PURE__ */ jsxRuntimeExports.jsxs(Form, {
          method: "post",
          children: [/* @__PURE__ */ jsxRuntimeExports.jsx("input", {
            name: "sellerId",
            value: sellerId,
            hidden: true
          }), /* @__PURE__ */ jsxRuntimeExports.jsx("input", {
            name: "matchBy",
            value: "",
            hidden: true
          }), /* @__PURE__ */ jsxRuntimeExports.jsx("input", {
            name: "actionType",
            value: "getAddonsGroupMap",
            hidden: true
          }), /* @__PURE__ */ jsxRuntimeExports.jsx("input", {
            name: "pageSize",
            value: "50",
            hidden: true
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
            className: "fixed bottom-5 right-5 rounded-full bg-blue-600 text-white hover:bg-blue-700 shadow-lg transition-all duration-200 px-6 py-3",
            type: "submit",
            children: "+ Add Addon Group"
          })]
        }), /* @__PURE__ */ jsxRuntimeExports.jsx(SelectedItemAddons, {
          isOpen: itemselectedGroupAddons,
          items: selectedAddonsgData || [],
          onClose: () => setItemselectedGroupAddons(false),
          header: isEditopen ? `Edit Addon for ${itemName == null ? void 0 : itemName.slice(0, 15)}` : `Create Addon for ${itemName == null ? void 0 : itemName.slice(0, 15)}`,
          groupData: isEditopen ? selectedItemGdata : void 0,
          sellerId,
          groupId: itemId,
          isEdit: isEditopen
        })]
      })]
    })]
  }, activeTab);
}
export {
  SelectedSellerItem as default
};
//# sourceMappingURL=home.selectedSellerItem-WtKtgxpo.js.map
