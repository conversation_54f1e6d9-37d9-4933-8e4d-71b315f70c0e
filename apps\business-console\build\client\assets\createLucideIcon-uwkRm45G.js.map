{"version": 3, "file": "createLucideIcon-uwkRm45G.js", "sources": ["../../../node_modules/lucide-react/dist/esm/shared/src/utils.js", "../../../node_modules/lucide-react/dist/esm/defaultAttributes.js", "../../../node_modules/lucide-react/dist/esm/Icon.js", "../../../node_modules/lucide-react/dist/esm/createLucideIcon.js"], "sourcesContent": ["/**\n * @license lucide-react v0.453.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nconst toKebabCase = (string) => string.replace(/([a-z0-9])([A-Z])/g, \"$1-$2\").toLowerCase();\nconst mergeClasses = (...classes) => classes.filter((className, index, array) => {\n  return Boolean(className) && array.indexOf(className) === index;\n}).join(\" \");\n\nexport { mergeClasses, toKebabCase };\n//# sourceMappingURL=utils.js.map\n", "/**\n * @license lucide-react v0.453.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nvar defaultAttributes = {\n  xmlns: \"http://www.w3.org/2000/svg\",\n  width: 24,\n  height: 24,\n  viewBox: \"0 0 24 24\",\n  fill: \"none\",\n  stroke: \"currentColor\",\n  strokeWidth: 2,\n  strokeLinecap: \"round\",\n  strokeLinejoin: \"round\"\n};\n\nexport { defaultAttributes as default };\n//# sourceMappingURL=defaultAttributes.js.map\n", "/**\n * @license lucide-react v0.453.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport { forwardRef, createElement } from 'react';\nimport defaultAttributes from './defaultAttributes.js';\nimport { mergeClasses } from './shared/src/utils.js';\n\nconst Icon = forwardRef(\n  ({\n    color = \"currentColor\",\n    size = 24,\n    strokeWidth = 2,\n    absoluteStrokeWidth,\n    className = \"\",\n    children,\n    iconNode,\n    ...rest\n  }, ref) => {\n    return createElement(\n      \"svg\",\n      {\n        ref,\n        ...defaultAttributes,\n        width: size,\n        height: size,\n        stroke: color,\n        strokeWidth: absoluteStrokeWidth ? Number(strokeWidth) * 24 / Number(size) : strokeWidth,\n        className: mergeClasses(\"lucide\", className),\n        ...rest\n      },\n      [\n        ...iconNode.map(([tag, attrs]) => createElement(tag, attrs)),\n        ...Array.isArray(children) ? children : [children]\n      ]\n    );\n  }\n);\n\nexport { Icon as default };\n//# sourceMappingURL=Icon.js.map\n", "/**\n * @license lucide-react v0.453.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport { forwardRef, createElement } from 'react';\nimport { mergeClasses, toKebabCase } from './shared/src/utils.js';\nimport Icon from './Icon.js';\n\nconst createLucideIcon = (iconName, iconNode) => {\n  const Component = forwardRef(\n    ({ className, ...props }, ref) => createElement(Icon, {\n      ref,\n      iconNode,\n      className: mergeClasses(`lucide-${toKebabCase(iconName)}`, className),\n      ...props\n    })\n  );\n  Component.displayName = `${iconName}`;\n  return Component;\n};\n\nexport { createLucideIcon as default };\n//# sourceMappingURL=createLucideIcon.js.map\n"], "names": ["forwardRef", "createElement"], "mappings": ";AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAOA,MAAM,cAAc,CAAC,WAAW,OAAO,QAAQ,sBAAsB,OAAO,EAAE,YAAa;AAC3F,MAAM,eAAe,IAAI,YAAY,QAAQ,OAAO,CAAC,WAAW,OAAO,UAAU;AAC/E,SAAO,QAAQ,SAAS,KAAK,MAAM,QAAQ,SAAS,MAAM;AAC5D,CAAC,EAAE,KAAK,GAAG;ACVX;AAAA;AAAA;AAAA;AAAA;AAAA;AAOA,IAAI,oBAAoB;AAAA,EACtB,OAAO;AAAA,EACP,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,aAAa;AAAA,EACb,eAAe;AAAA,EACf,gBAAgB;AAClB;ACjBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA,MAAM,OAAOA,aAAU;AAAA,EACrB,CAAC;AAAA,IACC,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,cAAc;AAAA,IACd;AAAA,IACA,YAAY;AAAA,IACZ;AAAA,IACA;AAAA,IACA,GAAG;AAAA,EACJ,GAAE,QAAQ;AACT,WAAOC,aAAa;AAAA,MAClB;AAAA,MACA;AAAA,QACE;AAAA,QACA,GAAG;AAAA,QACH,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,aAAa,sBAAsB,OAAO,WAAW,IAAI,KAAK,OAAO,IAAI,IAAI;AAAA,QAC7E,WAAW,aAAa,UAAU,SAAS;AAAA,QAC3C,GAAG;AAAA,MACJ;AAAA,MACD;AAAA,QACE,GAAG,SAAS,IAAI,CAAC,CAAC,KAAK,KAAK,MAAMA,aAAa,cAAC,KAAK,KAAK,CAAC;AAAA,QAC3D,GAAG,MAAM,QAAQ,QAAQ,IAAI,WAAW,CAAC,QAAQ;AAAA,MACzD;AAAA,IACK;AAAA,EACL;AACA;ACxCA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWK,MAAC,mBAAmB,CAAC,UAAU,aAAa;AAC/C,QAAM,YAAYD,aAAU;AAAA,IAC1B,CAAC,EAAE,WAAW,GAAG,MAAO,GAAE,QAAQC,aAAa,cAAC,MAAM;AAAA,MACpD;AAAA,MACA;AAAA,MACA,WAAW,aAAa,UAAU,YAAY,QAAQ,CAAC,IAAI,SAAS;AAAA,MACpE,GAAG;AAAA,IACJ,CAAA;AAAA,EACF;AACD,YAAU,cAAc,GAAG,QAAQ;AACnC,SAAO;AACT;", "x_google_ignoreList": [0, 1, 2, 3]}