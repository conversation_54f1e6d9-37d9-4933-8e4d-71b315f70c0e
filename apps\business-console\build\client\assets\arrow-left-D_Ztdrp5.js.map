{"version": 3, "file": "arrow-left-D_Ztdrp5.js", "sources": ["../../../node_modules/lucide-react/dist/esm/icons/arrow-left.js"], "sourcesContent": ["/**\n * @license lucide-react v0.453.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst ArrowLeft = createLucideIcon(\"ArrowLeft\", [\n  [\"path\", { d: \"m12 19-7-7 7-7\", key: \"1l729n\" }],\n  [\"path\", { d: \"M19 12H5\", key: \"x3x0zl\" }]\n]);\n\nexport { ArrowLeft as default };\n//# sourceMappingURL=arrow-left.js.map\n"], "names": [], "mappings": ";AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASK,MAAC,YAAY,iBAAiB,aAAa;AAAA,EAC9C,CAAC,QAAQ,EAAE,GAAG,kBAAkB,KAAK,SAAQ,CAAE;AAAA,EAC/C,CAAC,QAAQ,EAAE,GAAG,YAAY,KAAK,SAAU,CAAA;AAC3C,CAAC;", "x_google_ignoreList": [0]}