import { r as reactExports, j as jsxRuntimeExports } from "./jsx-runtime-bB2y7OuD.js";
import { B as Button } from "./button-ByAXMyvk.js";
import { I as Input } from "./input-3v87qohQ.js";
import { S as Separator } from "./separator-35EEKp-L.js";
import { u as useToast } from "./ToastProvider-rciVe3-g.js";
import { c as camelCaseToWords } from "./format-Da3JpRMs.js";
import { u as useLoaderData, a as useFetcher } from "./components-D7UvGag_.js";
import { a as useNavigate, b as useRevalidator } from "./index-DhHTcibu.js";
import { S as Search } from "./search-DzDJ71yc.js";
import { C as CircleX } from "./circle-x-CT1OEq17.js";
import { P as Pen } from "./pen-NKWZhTNQ.js";
import { T as Trash2 } from "./trash-2-DjkfFIB-.js";
import { f as format } from "./format-82yT_5--.js";
import { p as parseISO } from "./parseISO-COJrHI78.js";
import "./index-z_byfFrQ.js";
import "./index-ImHKLo0a.js";
import "./utils-GkgzjW3c.js";
import "./index-CzZ6EYkN.js";
import "./index-Vp2vNLNM.js";
import "./createLucideIcon-uwkRm45G.js";
function CouponCard({
  coupon,
  fetcher,
  navigate
}) {
  return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
    className: "p-4 rounded-xl shadow-[0px_2px_8px_0px_rgba(0,0,0,0.1)] hover:shadow-[0px_4px_12px_0px_rgba(0,0,0,0.15)] transition-shadow duration-200 bg-white border border-neutral-100",
    children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
      className: "mb-3",
      children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
        className: "flex flex-row justify-between items-center",
        children: [/* @__PURE__ */ jsxRuntimeExports.jsx("div", {
          className: "p-1 border border-neutral-600 rounded-md bg-[#FCFCFD]",
          children: /* @__PURE__ */ jsxRuntimeExports.jsx("h3", {
            className: "text-base font-semibold text-typography-400",
            children: coupon.couponCode
          })
        }), /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
          className: `px-2 py-1 rounded-md text-xs font-medium ${coupon.active ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"}`,
          children: coupon.active === true ? "Active" : coupon.active === false ? "Expired" : ""
        })]
      }), /* @__PURE__ */ jsxRuntimeExports.jsx("p", {
        className: "mt-2 text-sm text-typography-500 line-clamp-2",
        children: coupon.description
      })]
    }), /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
      className: "border-b border-neutral-200"
    }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
      className: "my-3",
      children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
        className: "grid grid-cols-2 gap-4",
        children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
          children: [/* @__PURE__ */ jsxRuntimeExports.jsx("p", {
            className: "text-xs text-typography-400 mb-1",
            children: "Coupon Type"
          }), /* @__PURE__ */ jsxRuntimeExports.jsx("p", {
            className: "text-sm font-medium text-typography-700",
            children: coupon.couponName ? camelCaseToWords(coupon.couponName) : "Not specified"
          })]
        }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
          className: "text-right",
          children: [/* @__PURE__ */ jsxRuntimeExports.jsx("p", {
            className: "text-xs text-typography-400 mb-1",
            children: "Valid Till"
          }), /* @__PURE__ */ jsxRuntimeExports.jsx("p", {
            className: "text-sm font-medium text-typography-700",
            children: coupon.validTo ? format(parseISO(coupon.validTo), "dd MMMM yyyy") : "Not specified"
          })]
        })]
      }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
        className: "mt-4 grid grid-cols-2 gap-4",
        children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
          children: [/* @__PURE__ */ jsxRuntimeExports.jsx("p", {
            className: "text-xs text-typography-400 mb-1",
            children: "Coupon Usage"
          }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
            className: "flex flex-row items-center text-sm font-medium text-typography-700",
            children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("span", {
              className: "mr-1",
              children: [coupon.totalUsers || "0", " Users"]
            }), /* @__PURE__ */ jsxRuntimeExports.jsx(Separator, {
              orientation: "vertical",
              className: "h-4 mx-1 bg-neutral-300"
            }), /* @__PURE__ */ jsxRuntimeExports.jsxs("span", {
              className: "ml-1",
              children: [coupon.totalOrders, " Orders"]
            })]
          })]
        }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
          className: "text-right",
          children: [/* @__PURE__ */ jsxRuntimeExports.jsx("p", {
            className: "text-xs text-typography-400 mb-1",
            children: "Discount Amount"
          }), /* @__PURE__ */ jsxRuntimeExports.jsxs("p", {
            className: "text-sm font-medium text-typography-700",
            children: ["₹ ", coupon.discountValue]
          })]
        })]
      })]
    }), /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
      className: "border-b border-neutral-200"
    }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
      className: "relative mt-3 flex flex-row gap-3 justify-end",
      children: [/* @__PURE__ */ jsxRuntimeExports.jsx("button", {
        className: "border border-neutral-200 rounded-full p-2 cursor-pointer hover:bg-primary-50 hover:border-primary-200 transition-colors duration-200",
        onClick: () => navigate(`/sellerSetting/coupon/${coupon.id}`),
        "aria-label": "Edit coupon",
        children: /* @__PURE__ */ jsxRuntimeExports.jsx(Pen, {
          className: "w-4 h-4 text-primary-600"
        })
      }), /* @__PURE__ */ jsxRuntimeExports.jsx("button", {
        className: "border border-neutral-200 rounded-full p-2 cursor-pointer hover:bg-red-50 hover:border-red-200 transition-colors duration-200",
        onClick: () => {
          if (confirm("Are you sure you want to delete this coupon?")) {
            fetcher.submit({
              actionType: "deleteCoupon",
              couponId: String(coupon.id)
            }, {
              method: "DELETE"
            });
          }
        },
        "aria-label": "Delete coupon",
        children: /* @__PURE__ */ jsxRuntimeExports.jsx(Trash2, {
          className: "w-4 h-4 text-red-500"
        })
      })]
    })]
  });
}
function QuickCouponCard({
  title,
  description,
  icon,
  defaultValue,
  onSubmit,
  isLoading
}) {
  const [discountValue, setDiscountValue] = reactExports.useState(defaultValue);
  return /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
    className: "p-6 rounded-xl border-2 border-dashed border-primary-200 bg-gradient-to-br from-primary-50 to-white hover:border-primary-300 transition-all duration-200",
    children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
      className: "flex flex-col items-center text-center space-y-4",
      children: [/* @__PURE__ */ jsxRuntimeExports.jsx("div", {
        className: "p-3 rounded-full bg-primary-100",
        children: icon
      }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
        children: [/* @__PURE__ */ jsxRuntimeExports.jsx("h3", {
          className: "text-lg font-semibold text-typography-700 mb-1",
          children: title
        }), /* @__PURE__ */ jsxRuntimeExports.jsx("p", {
          className: "text-sm text-typography-500",
          children: description
        })]
      }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
        className: "flex items-center space-x-2 w-full max-w-xs",
        children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Input, {
          type: "number",
          value: discountValue,
          onChange: (e) => setDiscountValue(e.target.value),
          className: "text-center font-medium",
          min: "1",
          max: "100"
        }), /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
          className: "text-sm font-medium text-typography-600 whitespace-nowrap",
          children: "% off"
        })]
      }), /* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
        onClick: () => onSubmit(discountValue),
        disabled: isLoading || !discountValue || Number(discountValue) <= 0,
        className: "w-full bg-primary hover:bg-primary-600 text-white font-medium",
        children: isLoading ? "Creating..." : "Save Coupon"
      })]
    })
  });
}
function Coupons() {
  const {
    data: coupons
  } = useLoaderData();
  const navigate = useNavigate();
  const {
    revalidate
  } = useRevalidator();
  const [searchTerm, setSearchTerm] = reactExports.useState("");
  const [filteredCoupons, setFilteredCoupons] = reactExports.useState(coupons || []);
  const fetcher = useFetcher();
  const fetcher1 = useFetcher();
  const {
    showToast
  } = useToast();
  const handleSearchChange = (e) => {
    const value = e.target.value;
    setSearchTerm(value);
  };
  const clearSearch = () => {
    setSearchTerm("");
  };
  reactExports.useEffect(() => {
    if (!coupons) return;
    if (searchTerm.trim() === "") {
      setFilteredCoupons(coupons);
    } else {
      const term = searchTerm.toLowerCase().trim();
      const filtered = coupons.filter((coupon) => coupon.couponCode.toLowerCase().includes(term));
      setFilteredCoupons(filtered);
    }
  }, [searchTerm, coupons]);
  reactExports.useEffect(() => {
    if (fetcher.state === "idle" && fetcher.data) {
      if (fetcher.data.success === true && fetcher.data.actionType === "deleteCoupon") {
        showToast("Coupon deleted successfully", "success");
        revalidate();
      } else if (fetcher.data.success === false && fetcher.data.actionType === "deleteCoupon") {
        showToast("Failed to delete coupon", "error");
      } else if (fetcher.data.success === true && fetcher.data.actionType === "addCoupon") {
        showToast("Coupon created successfully", "success");
        revalidate();
      } else if (fetcher.data.success === false && fetcher.data.actionType === "addCoupon") {
        showToast("Failed to create coupon", "error");
      }
    }
  }, [fetcher.state, fetcher.data]);
  reactExports.useEffect(() => {
    if (fetcher1.state === "idle" && fetcher1.data) {
      if (fetcher1.data.success === true && fetcher1.data.actionType === "addCoupon") {
        showToast("Coupon created successfully", "success");
        revalidate();
      } else if (fetcher1.data.success === false && fetcher1.data.actionType === "addCoupon") {
        showToast("Failed to create coupon", "error");
      }
    }
  }, [fetcher1.state, fetcher1.data]);
  const handleQuickCoupon = async (type, discountValue) => {
    const configurations = {
      newCustomer: {
        code: "WELCOME_" + discountValue,
        name: "newCustomer",
        description: `Flat ${discountValue}% off for new customers`,
        discountConfiguration: [{
          property: "discountPercent",
          operator: "EQ",
          value: discountValue + ""
        }],
        filterConfiguration: [],
        validityConfiguration: [{
          property: "firstNOrders",
          operator: "EQ",
          value: "1"
        }]
      },
      repeating: {
        code: "REPEAT_" + discountValue,
        name: "highOrderValue",
        description: `Flat ${discountValue}% off for loyal customers`,
        discountConfiguration: [{
          property: "discountPercent",
          operator: "EQ",
          value: discountValue + ""
        }],
        filterConfiguration: [{
          property: "orderCount",
          operator: "GTE",
          value: "2"
        }],
        validityConfiguration: []
      }
    };
    if (type === "newCustomer") {
      fetcher.submit({
        actionType: "addCoupon",
        payload: JSON.stringify(configurations[type])
      }, {
        method: "POST"
      });
    } else if (type === "repeating") {
      fetcher1.submit({
        actionType: "addCoupon",
        payload: JSON.stringify(configurations[type])
      }, {
        method: "POST"
      });
    }
  };
  return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
    className: "p-6",
    children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
      className: "mb-6",
      children: [/* @__PURE__ */ jsxRuntimeExports.jsx("h1", {
        className: "text-3xl font-bold text-gray-900",
        children: "Coupons"
      }), /* @__PURE__ */ jsxRuntimeExports.jsx("p", {
        className: "text-gray-600 mt-2",
        children: "Manage your promotional offers and discounts"
      })]
    }), /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
      className: "mb-6",
      children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
        className: "relative max-w-md",
        children: [/* @__PURE__ */ jsxRuntimeExports.jsx("div", {
          className: "absolute inset-y-0 left-3 flex items-center pointer-events-none",
          children: /* @__PURE__ */ jsxRuntimeExports.jsx(Search, {
            className: "h-4 w-4 text-muted-foreground"
          })
        }), /* @__PURE__ */ jsxRuntimeExports.jsx(Input, {
          type: "text",
          placeholder: "Search coupons by code...",
          className: "pl-10 pr-10",
          value: searchTerm,
          onChange: handleSearchChange
        }), searchTerm && /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
          className: "absolute inset-y-0 right-3 flex items-center",
          children: /* @__PURE__ */ jsxRuntimeExports.jsx("button", {
            onClick: clearSearch,
            className: "text-muted-foreground hover:text-foreground transition-colors",
            "aria-label": "Clear search",
            children: /* @__PURE__ */ jsxRuntimeExports.jsx(CircleX, {
              className: "h-4 w-4"
            })
          })
        })]
      })
    }), /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
      "aria-labelledby": "coupons-list",
      className: "pb-20 md:pb-5",
      children: filteredCoupons.length > 0 ? /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
        className: "grid grid-cols-1 lg:grid-cols-2 gap-4",
        children: filteredCoupons.map((coupon) => /* @__PURE__ */ jsxRuntimeExports.jsx(CouponCard, {
          coupon,
          fetcher,
          navigate
        }, coupon.id))
      }) : /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
        className: "flex flex-col items-center justify-center py-10 text-center",
        children: searchTerm ? /* @__PURE__ */ jsxRuntimeExports.jsxs(jsxRuntimeExports.Fragment, {
          children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("p", {
            className: "text-muted-foreground mb-2",
            children: ['No coupons found matching "', searchTerm, '"']
          }), /* @__PURE__ */ jsxRuntimeExports.jsx("p", {
            className: "text-muted-foreground text-sm",
            children: "Try a different search term or clear the search"
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
            variant: "outline",
            className: "mt-4",
            onClick: clearSearch,
            children: "Clear Search"
          })]
        }) : /* @__PURE__ */ jsxRuntimeExports.jsx(jsxRuntimeExports.Fragment, {
          children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
            className: "w-full max-w-4xl",
            children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
              className: "text-center mb-8",
              children: [/* @__PURE__ */ jsxRuntimeExports.jsx("h3", {
                className: "text-2xl font-semibold text-foreground mb-2",
                children: "Create Your First Coupon"
              }), /* @__PURE__ */ jsxRuntimeExports.jsx("p", {
                className: "text-muted-foreground",
                children: "Get started with these popular coupon templates or create a custom one"
              })]
            }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
              className: "grid grid-cols-1 md:grid-cols-2 gap-6 mb-8",
              children: [/* @__PURE__ */ jsxRuntimeExports.jsx(QuickCouponCard, {
                title: "New Customer Discount",
                description: "Welcome first-time buyers with a special discount",
                icon: /* @__PURE__ */ jsxRuntimeExports.jsx("svg", {
                  className: "w-6 h-6 text-primary-600",
                  fill: "none",
                  stroke: "currentColor",
                  viewBox: "0 0 24 24",
                  children: /* @__PURE__ */ jsxRuntimeExports.jsx("path", {
                    strokeLinecap: "round",
                    strokeLinejoin: "round",
                    strokeWidth: 2,
                    d: "M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                  })
                }),
                defaultValue: "30",
                onSubmit: (value) => handleQuickCoupon("newCustomer", value),
                isLoading: fetcher.state === "submitting" || fetcher.state === "loading"
              }), /* @__PURE__ */ jsxRuntimeExports.jsx(QuickCouponCard, {
                title: "Repeating Customer Discount",
                description: "Reward your returning customers for their loyalty",
                icon: /* @__PURE__ */ jsxRuntimeExports.jsx("svg", {
                  className: "w-6 h-6 text-primary-600",
                  fill: "none",
                  stroke: "currentColor",
                  viewBox: "0 0 24 24",
                  children: /* @__PURE__ */ jsxRuntimeExports.jsx("path", {
                    strokeLinecap: "round",
                    strokeLinejoin: "round",
                    strokeWidth: 2,
                    d: "M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"
                  })
                }),
                defaultValue: "20",
                onSubmit: (value) => handleQuickCoupon("repeating", value),
                isLoading: fetcher1.state === "submitting" || fetcher1.state === "loading"
              })]
            }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
              className: "text-center",
              children: [/* @__PURE__ */ jsxRuntimeExports.jsx("p", {
                className: "text-muted-foreground text-sm mb-4",
                children: "Need more customization?"
              }), /* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
                variant: "outline",
                onClick: () => navigate("/sellerSetting/coupon/add"),
                className: "font-medium",
                children: "Create Custom Coupon"
              })]
            })]
          })
        })
      })
    }), /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
      className: "fixed bottom-0 left-0 right-0 z-30 p-4 flex justify-center md:static md:justify-end md:border-0",
      children: /* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
        onClick: () => navigate("/sellerSetting/coupon/add"),
        className: "w-full md:w-auto",
        children: "Add New Coupon"
      })
    })]
  });
}
export {
  Coupons as default
};
//# sourceMappingURL=sellerSetting.coupons-BpL1CFRF.js.map
