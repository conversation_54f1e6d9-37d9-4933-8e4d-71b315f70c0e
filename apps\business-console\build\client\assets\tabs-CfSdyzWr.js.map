{"version": 3, "file": "tabs-CfSdyzWr.js", "sources": ["../../../node_modules/@radix-ui/react-tabs/dist/index.mjs", "../../../app/components/ui/tabs.tsx"], "sourcesContent": ["\"use client\";\n\n// packages/react/tabs/src/tabs.tsx\nimport * as React from \"react\";\nimport { composeEventHandlers } from \"@radix-ui/primitive\";\nimport { createContextScope } from \"@radix-ui/react-context\";\nimport { createRovingFocusGroupScope } from \"@radix-ui/react-roving-focus\";\nimport { Presence } from \"@radix-ui/react-presence\";\nimport { Primitive } from \"@radix-ui/react-primitive\";\nimport * as RovingFocusGroup from \"@radix-ui/react-roving-focus\";\nimport { useDirection } from \"@radix-ui/react-direction\";\nimport { useControllableState } from \"@radix-ui/react-use-controllable-state\";\nimport { useId } from \"@radix-ui/react-id\";\nimport { jsx } from \"react/jsx-runtime\";\nvar TABS_NAME = \"Tabs\";\nvar [createTabsContext, createTabsScope] = createContextScope(TABS_NAME, [\n  createRovingFocusGroupScope\n]);\nvar useRovingFocusGroupScope = createRovingFocusGroupScope();\nvar [TabsProvider, useTabsContext] = createTabsContext(TABS_NAME);\nvar Tabs = React.forwardRef(\n  (props, forwardedRef) => {\n    const {\n      __scopeTabs,\n      value: valueProp,\n      onValueChange,\n      defaultValue,\n      orientation = \"horizontal\",\n      dir,\n      activationMode = \"automatic\",\n      ...tabsProps\n    } = props;\n    const direction = useDirection(dir);\n    const [value, setValue] = useControllableState({\n      prop: valueProp,\n      onChange: onValueChange,\n      defaultProp: defaultValue\n    });\n    return /* @__PURE__ */ jsx(\n      TabsProvider,\n      {\n        scope: __scopeTabs,\n        baseId: useId(),\n        value,\n        onValueChange: setValue,\n        orientation,\n        dir: direction,\n        activationMode,\n        children: /* @__PURE__ */ jsx(\n          Primitive.div,\n          {\n            dir: direction,\n            \"data-orientation\": orientation,\n            ...tabsProps,\n            ref: forwardedRef\n          }\n        )\n      }\n    );\n  }\n);\nTabs.displayName = TABS_NAME;\nvar TAB_LIST_NAME = \"TabsList\";\nvar TabsList = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeTabs, loop = true, ...listProps } = props;\n    const context = useTabsContext(TAB_LIST_NAME, __scopeTabs);\n    const rovingFocusGroupScope = useRovingFocusGroupScope(__scopeTabs);\n    return /* @__PURE__ */ jsx(\n      RovingFocusGroup.Root,\n      {\n        asChild: true,\n        ...rovingFocusGroupScope,\n        orientation: context.orientation,\n        dir: context.dir,\n        loop,\n        children: /* @__PURE__ */ jsx(\n          Primitive.div,\n          {\n            role: \"tablist\",\n            \"aria-orientation\": context.orientation,\n            ...listProps,\n            ref: forwardedRef\n          }\n        )\n      }\n    );\n  }\n);\nTabsList.displayName = TAB_LIST_NAME;\nvar TRIGGER_NAME = \"TabsTrigger\";\nvar TabsTrigger = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeTabs, value, disabled = false, ...triggerProps } = props;\n    const context = useTabsContext(TRIGGER_NAME, __scopeTabs);\n    const rovingFocusGroupScope = useRovingFocusGroupScope(__scopeTabs);\n    const triggerId = makeTriggerId(context.baseId, value);\n    const contentId = makeContentId(context.baseId, value);\n    const isSelected = value === context.value;\n    return /* @__PURE__ */ jsx(\n      RovingFocusGroup.Item,\n      {\n        asChild: true,\n        ...rovingFocusGroupScope,\n        focusable: !disabled,\n        active: isSelected,\n        children: /* @__PURE__ */ jsx(\n          Primitive.button,\n          {\n            type: \"button\",\n            role: \"tab\",\n            \"aria-selected\": isSelected,\n            \"aria-controls\": contentId,\n            \"data-state\": isSelected ? \"active\" : \"inactive\",\n            \"data-disabled\": disabled ? \"\" : void 0,\n            disabled,\n            id: triggerId,\n            ...triggerProps,\n            ref: forwardedRef,\n            onMouseDown: composeEventHandlers(props.onMouseDown, (event) => {\n              if (!disabled && event.button === 0 && event.ctrlKey === false) {\n                context.onValueChange(value);\n              } else {\n                event.preventDefault();\n              }\n            }),\n            onKeyDown: composeEventHandlers(props.onKeyDown, (event) => {\n              if ([\" \", \"Enter\"].includes(event.key)) context.onValueChange(value);\n            }),\n            onFocus: composeEventHandlers(props.onFocus, () => {\n              const isAutomaticActivation = context.activationMode !== \"manual\";\n              if (!isSelected && !disabled && isAutomaticActivation) {\n                context.onValueChange(value);\n              }\n            })\n          }\n        )\n      }\n    );\n  }\n);\nTabsTrigger.displayName = TRIGGER_NAME;\nvar CONTENT_NAME = \"TabsContent\";\nvar TabsContent = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeTabs, value, forceMount, children, ...contentProps } = props;\n    const context = useTabsContext(CONTENT_NAME, __scopeTabs);\n    const triggerId = makeTriggerId(context.baseId, value);\n    const contentId = makeContentId(context.baseId, value);\n    const isSelected = value === context.value;\n    const isMountAnimationPreventedRef = React.useRef(isSelected);\n    React.useEffect(() => {\n      const rAF = requestAnimationFrame(() => isMountAnimationPreventedRef.current = false);\n      return () => cancelAnimationFrame(rAF);\n    }, []);\n    return /* @__PURE__ */ jsx(Presence, { present: forceMount || isSelected, children: ({ present }) => /* @__PURE__ */ jsx(\n      Primitive.div,\n      {\n        \"data-state\": isSelected ? \"active\" : \"inactive\",\n        \"data-orientation\": context.orientation,\n        role: \"tabpanel\",\n        \"aria-labelledby\": triggerId,\n        hidden: !present,\n        id: contentId,\n        tabIndex: 0,\n        ...contentProps,\n        ref: forwardedRef,\n        style: {\n          ...props.style,\n          animationDuration: isMountAnimationPreventedRef.current ? \"0s\" : void 0\n        },\n        children: present && children\n      }\n    ) });\n  }\n);\nTabsContent.displayName = CONTENT_NAME;\nfunction makeTriggerId(baseId, value) {\n  return `${baseId}-trigger-${value}`;\n}\nfunction makeContentId(baseId, value) {\n  return `${baseId}-content-${value}`;\n}\nvar Root2 = Tabs;\nvar List = TabsList;\nvar Trigger = TabsTrigger;\nvar Content = TabsContent;\nexport {\n  Content,\n  List,\n  Root2 as Root,\n  Tabs,\n  TabsContent,\n  TabsList,\n  TabsTrigger,\n  Trigger,\n  createTabsScope\n};\n//# sourceMappingURL=index.mjs.map\n", "import * as React from \"react\"\r\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\r\n\r\nimport { cn } from \"~/lib/utils\"\r\n\r\nconst Tabs = TabsPrimitive.Root\r\n\r\nconst TabsList = React.forwardRef<\r\n  React.ElementRef<typeof TabsPrimitive.List>,\r\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.List>\r\n>(({ className, ...props }, ref) => (\r\n  <TabsPrimitive.List\r\n    ref={ref}\r\n    className={cn(\r\n      \"inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nTabsList.displayName = TabsPrimitive.List.displayName\r\n\r\nconst TabsTrigger = React.forwardRef<\r\n  React.ElementRef<typeof TabsPrimitive.Trigger>,\r\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger>\r\n>(({ className, ...props }, ref) => (\r\n  <TabsPrimitive.Trigger\r\n    ref={ref}\r\n    className={cn(\r\n      \"inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nTabsTrigger.displayName = TabsPrimitive.Trigger.displayName\r\n\r\nconst TabsContent = React.forwardRef<\r\n  React.ElementRef<typeof TabsPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Content>\r\n>(({ className, ...props }, ref) => (\r\n  <TabsPrimitive.Content\r\n    ref={ref}\r\n    className={cn(\r\n      \"mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nTabsContent.displayName = TabsPrimitive.Content.displayName\r\n\r\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\r\n"], "names": ["Tabs", "React.forwardRef", "jsx", "TabsList", "RovingFocusGroup.Root", "TabsTrigger", "RovingFocusGroup.Item", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "React.useRef", "React.useEffect", "TabsPrimitive.Root", "TabsPrimitive.List", "TabsPrimitive.Trigger", "TabsPrimitive.Content"], "mappings": ";;;;;;;;;AAcA,IAAI,YAAY;AAChB,IAAI,CAAC,mBAAmB,eAAe,IAAI,mBAAmB,WAAW;AAAA,EACvE;AACF,CAAC;AACD,IAAI,2BAA2B,4BAA6B;AAC5D,IAAI,CAAC,cAAc,cAAc,IAAI,kBAAkB,SAAS;AAChE,IAAIA,SAAOC,aAAgB;AAAA,EACzB,CAAC,OAAO,iBAAiB;AACvB,UAAM;AAAA,MACJ;AAAA,MACA,OAAO;AAAA,MACP;AAAA,MACA;AAAA,MACA,cAAc;AAAA,MACd;AAAA,MACA,iBAAiB;AAAA,MACjB,GAAG;AAAA,IACT,IAAQ;AACJ,UAAM,YAAY,aAAa,GAAG;AAClC,UAAM,CAAC,OAAO,QAAQ,IAAI,qBAAqB;AAAA,MAC7C,MAAM;AAAA,MACN,UAAU;AAAA,MACV,aAAa;AAAA,IACnB,CAAK;AACD,WAAuBC,kCAAG;AAAA,MACxB;AAAA,MACA;AAAA,QACE,OAAO;AAAA,QACP,QAAQ,MAAO;AAAA,QACf;AAAA,QACA,eAAe;AAAA,QACf;AAAA,QACA,KAAK;AAAA,QACL;AAAA,QACA,UAA0BA,kCAAG;AAAA,UAC3B,UAAU;AAAA,UACV;AAAA,YACE,KAAK;AAAA,YACL,oBAAoB;AAAA,YACpB,GAAG;AAAA,YACH,KAAK;AAAA,UACjB;AAAA,QACA;AAAA,MACA;AAAA,IACK;AAAA,EACL;AACA;AACAF,OAAK,cAAc;AACnB,IAAI,gBAAgB;AACpB,IAAIG,aAAWF,aAAgB;AAAA,EAC7B,CAAC,OAAO,iBAAiB;AACvB,UAAM,EAAE,aAAa,OAAO,MAAM,GAAG,UAAW,IAAG;AACnD,UAAM,UAAU,eAAe,eAAe,WAAW;AACzD,UAAM,wBAAwB,yBAAyB,WAAW;AAClE,WAAuBC,kCAAG;AAAA,MACxBE;AAAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,GAAG;AAAA,QACH,aAAa,QAAQ;AAAA,QACrB,KAAK,QAAQ;AAAA,QACb;AAAA,QACA,UAA0BF,kCAAG;AAAA,UAC3B,UAAU;AAAA,UACV;AAAA,YACE,MAAM;AAAA,YACN,oBAAoB,QAAQ;AAAA,YAC5B,GAAG;AAAA,YACH,KAAK;AAAA,UACjB;AAAA,QACA;AAAA,MACA;AAAA,IACK;AAAA,EACL;AACA;AACAC,WAAS,cAAc;AACvB,IAAI,eAAe;AACnB,IAAIE,gBAAcJ,aAAgB;AAAA,EAChC,CAAC,OAAO,iBAAiB;AACvB,UAAM,EAAE,aAAa,OAAO,WAAW,OAAO,GAAG,aAAY,IAAK;AAClE,UAAM,UAAU,eAAe,cAAc,WAAW;AACxD,UAAM,wBAAwB,yBAAyB,WAAW;AAClE,UAAM,YAAY,cAAc,QAAQ,QAAQ,KAAK;AACrD,UAAM,YAAY,cAAc,QAAQ,QAAQ,KAAK;AACrD,UAAM,aAAa,UAAU,QAAQ;AACrC,WAAuBC,kCAAG;AAAA,MACxBI;AAAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,GAAG;AAAA,QACH,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,UAA0BJ,kCAAG;AAAA,UAC3B,UAAU;AAAA,UACV;AAAA,YACE,MAAM;AAAA,YACN,MAAM;AAAA,YACN,iBAAiB;AAAA,YACjB,iBAAiB;AAAA,YACjB,cAAc,aAAa,WAAW;AAAA,YACtC,iBAAiB,WAAW,KAAK;AAAA,YACjC;AAAA,YACA,IAAI;AAAA,YACJ,GAAG;AAAA,YACH,KAAK;AAAA,YACL,aAAa,qBAAqB,MAAM,aAAa,CAAC,UAAU;AAC9D,kBAAI,CAAC,YAAY,MAAM,WAAW,KAAK,MAAM,YAAY,OAAO;AAC9D,wBAAQ,cAAc,KAAK;AAAA,cAC3C,OAAqB;AACL,sBAAM,eAAgB;AAAA,cACtC;AAAA,YACA,CAAa;AAAA,YACD,WAAW,qBAAqB,MAAM,WAAW,CAAC,UAAU;AAC1D,kBAAI,CAAC,KAAK,OAAO,EAAE,SAAS,MAAM,GAAG,EAAG,SAAQ,cAAc,KAAK;AAAA,YACjF,CAAa;AAAA,YACD,SAAS,qBAAqB,MAAM,SAAS,MAAM;AACjD,oBAAM,wBAAwB,QAAQ,mBAAmB;AACzD,kBAAI,CAAC,cAAc,CAAC,YAAY,uBAAuB;AACrD,wBAAQ,cAAc,KAAK;AAAA,cAC3C;AAAA,YACa,CAAA;AAAA,UACb;AAAA,QACA;AAAA,MACA;AAAA,IACK;AAAA,EACL;AACA;AACAG,cAAY,cAAc;AAC1B,IAAI,eAAe;AACnB,IAAIE,gBAAcN,aAAgB;AAAA,EAChC,CAAC,OAAO,iBAAiB;AACvB,UAAM,EAAE,aAAa,OAAO,YAAY,UAAU,GAAG,aAAY,IAAK;AACtE,UAAM,UAAU,eAAe,cAAc,WAAW;AACxD,UAAM,YAAY,cAAc,QAAQ,QAAQ,KAAK;AACrD,UAAM,YAAY,cAAc,QAAQ,QAAQ,KAAK;AACrD,UAAM,aAAa,UAAU,QAAQ;AACrC,UAAM,+BAA+BO,aAAY,OAAC,UAAU;AAC5DC,iBAAAA,UAAgB,MAAM;AACpB,YAAM,MAAM,sBAAsB,MAAM,6BAA6B,UAAU,KAAK;AACpF,aAAO,MAAM,qBAAqB,GAAG;AAAA,IACtC,GAAE,EAAE;AACL,WAAuBP,kCAAG,IAAC,UAAU,EAAE,SAAS,cAAc,YAAY,UAAU,CAAC,EAAE,QAAO,MAAuBA,kCAAG;AAAA,MACtH,UAAU;AAAA,MACV;AAAA,QACE,cAAc,aAAa,WAAW;AAAA,QACtC,oBAAoB,QAAQ;AAAA,QAC5B,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,QAAQ,CAAC;AAAA,QACT,IAAI;AAAA,QACJ,UAAU;AAAA,QACV,GAAG;AAAA,QACH,KAAK;AAAA,QACL,OAAO;AAAA,UACL,GAAG,MAAM;AAAA,UACT,mBAAmB,6BAA6B,UAAU,OAAO;AAAA,QAClE;AAAA,QACD,UAAU,WAAW;AAAA,MAC7B;AAAA,IACA,GAAO;AAAA,EACP;AACA;AACAK,cAAY,cAAc;AAC1B,SAAS,cAAc,QAAQ,OAAO;AACpC,SAAO,GAAG,MAAM,YAAY,KAAK;AACnC;AACA,SAAS,cAAc,QAAQ,OAAO;AACpC,SAAO,GAAG,MAAM,YAAY,KAAK;AACnC;AACA,IAAI,QAAQP;AACZ,IAAI,OAAOG;AACX,IAAI,UAAUE;AACd,IAAI,UAAUE;ACrLd,MAAM,OAAOG;AAEP,MAAA,WAAWT,aAGf,WAAA,CAAC,EAAE,WAAW,GAAG,MAAM,GAAG,QAC1BC,kCAAA;AAAA,EAACS;AAAAA,EAAA;AAAA,IACC;AAAA,IACA,WAAW;AAAA,MACT;AAAA,MACA;AAAA,IACF;AAAA,IACC,GAAG;AAAA,EAAA;AACN,CACD;AACD,SAAS,cAAcA,KAAmB;AAEpC,MAAA,cAAcV,aAGlB,WAAA,CAAC,EAAE,WAAW,GAAG,MAAM,GAAG,QAC1BC,kCAAA;AAAA,EAACU;AAAAA,EAAA;AAAA,IACC;AAAA,IACA,WAAW;AAAA,MACT;AAAA,MACA;AAAA,IACF;AAAA,IACC,GAAG;AAAA,EAAA;AACN,CACD;AACD,YAAY,cAAcA,QAAsB;AAE1C,MAAA,cAAcX,aAGlB,WAAA,CAAC,EAAE,WAAW,GAAG,MAAM,GAAG,QAC1BC,kCAAA;AAAA,EAACW;AAAAA,EAAA;AAAA,IACC;AAAA,IACA,WAAW;AAAA,MACT;AAAA,MACA;AAAA,IACF;AAAA,IACC,GAAG;AAAA,EAAA;AACN,CACD;AACD,YAAY,cAAcA,QAAsB;", "x_google_ignoreList": [0]}