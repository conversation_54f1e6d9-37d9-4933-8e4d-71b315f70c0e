import { r as reactExports, j as jsxRuntimeExports } from "./jsx-runtime-bB2y7OuD.js";
import { B as Button } from "./button-ByAXMyvk.js";
import { a as useFetcher, F as Form, u as useLoaderData } from "./components-D7UvGag_.js";
import { P as Pencil } from "./pencil-C1goHmP8.js";
import { c as createLucideIcon } from "./createLucideIcon-uwkRm45G.js";
import "./index-z_byfFrQ.js";
import "./index-ImHKLo0a.js";
import "./utils-GkgzjW3c.js";
import "./index-Vp2vNLNM.js";
import "./index-DhHTcibu.js";
/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */
const CircleMinus = createLucideIcon("CircleMinus", [
  ["circle", { cx: "12", cy: "12", r: "10", key: "1mglay" }],
  ["path", { d: "M8 12h8", key: "1wcyev" }]
]);
/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */
const CirclePlus = createLucideIcon("CirclePlus", [
  ["circle", { cx: "12", cy: "12", r: "10", key: "1mglay" }],
  ["path", { d: "M8 12h8", key: "1wcyev" }],
  ["path", { d: "M12 8v8", key: "napkw2" }]
]);
function ActiveBanners({ bannerDetails }) {
  const [sequence, setSequence] = reactExports.useState(bannerDetails.sequenceId);
  const [isEditing, setIsEditing] = reactExports.useState(false);
  const [error, setError] = reactExports.useState(null);
  const fetcher = useFetcher();
  const loading = fetcher.state === "submitting";
  reactExports.useEffect(() => {
    setSequence(bannerDetails.sequenceId);
  }, [bannerDetails.sequenceId]);
  const handleChange = (value) => {
    const numValue = Number(value);
    setSequence(numValue);
    if (isNaN(numValue) || numValue < 1) {
      setError("Sequence must be a number greater than 0");
    } else {
      setError(null);
    }
  };
  const handleSave = () => {
    if (error || sequence === bannerDetails.sequenceId) {
      setIsEditing(false);
      return;
    }
    const formData = new FormData();
    formData.append("netWorkId", bannerDetails.networkId.toString());
    formData.append("sequenceId", sequence.toString());
    formData.append("actionType", "updateSequence");
    formData.append("bannerId", bannerDetails.id.toString());
    fetcher.submit(formData, { method: "put" });
    setIsEditing(false);
  };
  return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex flex-col shadow-lg bg-white px-6 py-6 w-full rounded-xl border border-gray-200 mb-2 transition-all hover:shadow-xl", children: [
    /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "flex gap-8 border-b border-dashed border-gray-300 pb-4 w-full", children: /* @__PURE__ */ jsxRuntimeExports.jsx(
      "img",
      {
        src: bannerDetails.bannerUrl,
        alt: "Banner",
        className: "rounded-lg sm: w-full md:max-w-md h-[160px] object-fill shadow-sm"
      }
    ) }),
    /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "mt-4 w-full", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx("p", { className: "text-sm text-gray-500 font-medium mb-1", children: "Sequence" }),
      /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center justify-between w-full gap-2", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "relative flex items-center", children: isEditing ? /* @__PURE__ */ jsxRuntimeExports.jsx(
          "input",
          {
            type: "number",
            value: sequence,
            onChange: (e) => handleChange(e.target.value),
            onBlur: () => handleSave(),
            onKeyPress: (e) => e.key === "Enter" && handleSave(),
            className: `w-20 px-3 py-2 text-sm text-gray-800 bg-white border ${error ? "border-red-500" : "border-gray-300 focus:ring-2 focus:ring-blue-500"} rounded-md shadow-sm focus:outline-none transition-all`,
            min: "1",
            autoFocus: true
          }
        ) : /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center gap-2 px-3 py-2 bg-gray-100 border border-gray-300 rounded-md", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx("p", { className: "text-sm text-gray-800 font-medium", children: sequence }),
          /* @__PURE__ */ jsxRuntimeExports.jsx(
            "button",
            {
              onClick: () => setIsEditing(true),
              className: "text-gray-500 hover:text-blue-600 transition-colors",
              children: /* @__PURE__ */ jsxRuntimeExports.jsx(Pencil, { size: 16 })
            }
          )
        ] }) }),
        /* @__PURE__ */ jsxRuntimeExports.jsxs(Form, { method: "post", className: "flex-shrink-0", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx("input", { type: "hidden", name: "actionType", value: "updateBannerStatus" }),
          /* @__PURE__ */ jsxRuntimeExports.jsx("input", { type: "hidden", name: "bannerId", value: bannerDetails == null ? void 0 : bannerDetails.id }),
          /* @__PURE__ */ jsxRuntimeExports.jsx(
            "input",
            {
              type: "hidden",
              name: "status",
              value: (bannerDetails == null ? void 0 : bannerDetails.active) != null ? String(bannerDetails.active) : "false"
            }
          ),
          /* @__PURE__ */ jsxRuntimeExports.jsx(
            Button,
            {
              className: "flex items-center gap-2 py-2 px-4 text-white bg-secondary-600 hover:bg-secondary-700 rounded-md shadow-md transition-all disabled:opacity-50",
              type: "submit",
              disabled: loading,
              children: loading ? "Updating..." : /* @__PURE__ */ jsxRuntimeExports.jsxs(jsxRuntimeExports.Fragment, { children: [
                /* @__PURE__ */ jsxRuntimeExports.jsx(CircleMinus, { size: 20 }),
                /* @__PURE__ */ jsxRuntimeExports.jsx("span", { children: "Remove Banner" })
              ] })
            }
          )
        ] })
      ] })
    ] })
  ] });
}
function InActiveBanners({ bannerDetails }) {
  const fetcher = useFetcher();
  const loading = fetcher.state != "idle";
  return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex flex-col shadow-md bg-white p-4 w-full  border-r-8 border-transparent rounded-lg gap-2  h-full", children: [
    /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "flex gap-8 border-b border-dashed border-gray-300 pb-4 w-full", children: /* @__PURE__ */ jsxRuntimeExports.jsx(
      "img",
      {
        src: bannerDetails == null ? void 0 : bannerDetails.bannerUrl,
        alt: "Banner",
        className: "rounded-lg h-[160px] object-cover shadow-sm  sm: w-full md:max-w-md"
      }
    ) }),
    /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "flex flex-col w-full", children: /* @__PURE__ */ jsxRuntimeExports.jsxs(Form, { method: "post", className: "flex justify-end w-full", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx("input", { type: "hidden", name: "actionType", value: "updateBannerStatus" }),
      /* @__PURE__ */ jsxRuntimeExports.jsx("input", { type: "hidden", name: "bannerId", value: bannerDetails == null ? void 0 : bannerDetails.id }),
      /* @__PURE__ */ jsxRuntimeExports.jsx(
        "input",
        {
          type: "hidden",
          name: "status",
          value: (bannerDetails == null ? void 0 : bannerDetails.active) != null ? String(bannerDetails == null ? void 0 : bannerDetails.active) : "false"
        }
      ),
      /* @__PURE__ */ jsxRuntimeExports.jsx("input", { type: "hidden", name: "sequence", value: bannerDetails == null ? void 0 : bannerDetails.sequenceId }),
      /* @__PURE__ */ jsxRuntimeExports.jsx(
        Button,
        {
          className: "flex items-center gap-2 py-2 px-4 text-white bg-primary-600 hover:bg-red-700 rounded-md shadow-md transition-all disabled:opacity-50",
          type: "submit",
          disabled: loading,
          children: loading ? "Updating..." : /* @__PURE__ */ jsxRuntimeExports.jsxs(jsxRuntimeExports.Fragment, { children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx(CirclePlus, { size: 20 }),
            /* @__PURE__ */ jsxRuntimeExports.jsx("span", { children: "          Enable Banner" })
          ] })
        }
      )
    ] }) })
  ] });
}
function Nbanners() {
  const {
    banners
  } = useLoaderData();
  const inactiveBanners = banners == null ? void 0 : banners.filter((banner) => !(banner == null ? void 0 : banner.active));
  const activeBanners = banners == null ? void 0 : banners.filter((banner) => banner == null ? void 0 : banner.active);
  return /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
    className: "min-h-screen p-6",
    children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
      className: "mx-auto mb-6",
      children: [/* @__PURE__ */ jsxRuntimeExports.jsx("div", {
        className: "mb-4",
        children: /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
          className: "flex flex-col gap-2 sm:flex-row sm:items-center sm:justify-between",
          children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
            children: [/* @__PURE__ */ jsxRuntimeExports.jsx("h1", {
              className: "text-xl md:text-3xl font-bold text-gray-900",
              children: "My Banners"
            }), /* @__PURE__ */ jsxRuntimeExports.jsx("p", {
              className: "mt-2 text-gray-600",
              children: "Manage your promotional banners and sequences"
            })]
          })
        })
      }), /* @__PURE__ */ jsxRuntimeExports.jsxs("main", {
        className: "max-w-7xl mx-auto",
        children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("section", {
          children: [/* @__PURE__ */ jsxRuntimeExports.jsx("h2", {
            className: "text-xl font-semibold text-foreground mb-4",
            children: "Active Banners"
          }), Array.isArray(activeBanners) && activeBanners.length > 0 ? /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
            className: "grid gap-2 sm:grid-cols-1  ",
            children: activeBanners.map((banner) => /* @__PURE__ */ jsxRuntimeExports.jsx(ActiveBanners, {
              bannerDetails: banner
            }, banner.id))
          }) : /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
            className: "flex flex-col items-center justify-center py-12 bg-white rounded-lg border border-border",
            children: [/* @__PURE__ */ jsxRuntimeExports.jsx("svg", {
              className: "w-16 h-16 text-muted-foreground mb-4",
              fill: "none",
              stroke: "currentColor",
              viewBox: "0 0 24 24",
              xmlns: "http://www.w3.org/2000/svg",
              children: /* @__PURE__ */ jsxRuntimeExports.jsx("path", {
                strokeLinecap: "round",
                strokeLinejoin: "round",
                strokeWidth: "2",
                d: "M3 12h18M3 6h18M3 18h18"
              })
            }), /* @__PURE__ */ jsxRuntimeExports.jsx("p", {
              className: "text-lg font-medium text-foreground",
              children: "No Active Banners"
            }), /* @__PURE__ */ jsxRuntimeExports.jsx("p", {
              className: "text-sm text-muted-foreground mt-1",
              children: "Add some banners to get started!"
            })]
          })]
        }), /* @__PURE__ */ jsxRuntimeExports.jsxs("section", {
          className: "mt-8",
          children: [/* @__PURE__ */ jsxRuntimeExports.jsx("h2", {
            className: "text-xl font-semibold text-foreground mb-4",
            children: "Inactive Banners"
          }), Array.isArray(inactiveBanners) && inactiveBanners.length > 0 ? /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
            className: "grid gap-4 sm:grid-cols-1 ",
            children: inactiveBanners.map((banner) => /* @__PURE__ */ jsxRuntimeExports.jsx(InActiveBanners, {
              bannerDetails: banner
            }, banner.id))
          }) : /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
            className: "flex flex-col items-center justify-center py-12 bg-white rounded-lg border border-border",
            children: [/* @__PURE__ */ jsxRuntimeExports.jsx("svg", {
              className: "w-16 h-16 text-muted-foreground mb-4",
              fill: "none",
              stroke: "currentColor",
              viewBox: "0 0 24 24",
              xmlns: "http://www.w3.org/2000/svg",
              children: /* @__PURE__ */ jsxRuntimeExports.jsx("path", {
                strokeLinecap: "round",
                strokeLinejoin: "round",
                strokeWidth: "2",
                d: "M3 12h18M3 6h18M3 18h18"
              })
            }), /* @__PURE__ */ jsxRuntimeExports.jsx("p", {
              className: "text-lg font-medium text-foreground",
              children: "No Inactive Banners"
            }), /* @__PURE__ */ jsxRuntimeExports.jsx("p", {
              className: "text-sm text-muted-foreground mt-1",
              children: "Inactive banners will appear here."
            })]
          })]
        })]
      })]
    })
  });
}
export {
  Nbanners as default
};
//# sourceMappingURL=sellerSetting.nbanners-D-jUrYAG.js.map
