{"version": 3, "file": "message-circle-VDmkNDXi.js", "sources": ["../../../node_modules/lucide-react/dist/esm/icons/message-circle.js"], "sourcesContent": ["/**\n * @license lucide-react v0.453.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst MessageCircle = createLucideIcon(\"MessageCircle\", [\n  [\"path\", { d: \"M7.9 20A9 9 0 1 0 4 16.1L2 22Z\", key: \"vv11sd\" }]\n]);\n\nexport { MessageCircle as default };\n//# sourceMappingURL=message-circle.js.map\n"], "names": [], "mappings": ";AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASK,MAAC,gBAAgB,iBAAiB,iBAAiB;AAAA,EACtD,CAAC,QAAQ,EAAE,GAAG,kCAAkC,KAAK,SAAU,CAAA;AACjE,CAAC;", "x_google_ignoreList": [0]}