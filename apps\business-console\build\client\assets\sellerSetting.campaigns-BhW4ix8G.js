import { j as jsxRuntimeExports } from "./jsx-runtime-bB2y7OuD.js";
import { C as Card, b as <PERSON><PERSON>eader, c as <PERSON><PERSON><PERSON><PERSON>, d as CardDescription, a as CardContent } from "./card-BJQMSLe_.js";
import "./utils-GkgzjW3c.js";
function Campaigns() {
  return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
    className: "p-6",
    children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
      className: "mb-6",
      children: [/* @__PURE__ */ jsxRuntimeExports.jsx("h1", {
        className: "text-3xl font-bold text-gray-900",
        children: "Campaigns"
      }), /* @__PURE__ */ jsxRuntimeExports.jsx("p", {
        className: "text-gray-600 mt-2",
        children: "Manage your marketing campaigns"
      })]
    }), /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
      className: "flex items-center justify-center min-h-[400px]",
      children: /* @__PURE__ */ jsxRuntimeExports.jsxs(Card, {
        className: "w-full max-w-md",
        children: [/* @__PURE__ */ jsxRuntimeExports.jsxs(CardHeader, {
          className: "text-center",
          children: [/* @__PURE__ */ jsxRuntimeExports.jsx("div", {
            className: "mx-auto mb-4 w-16 h-16 bg-gradient-to-r from-pink-500 to-purple-600 rounded-full flex items-center justify-center",
            children: /* @__PURE__ */ jsxRuntimeExports.jsx("svg", {
              className: "w-8 h-8 text-white",
              fill: "none",
              stroke: "currentColor",
              viewBox: "0 0 24 24",
              children: /* @__PURE__ */ jsxRuntimeExports.jsx("path", {
                strokeLinecap: "round",
                strokeLinejoin: "round",
                strokeWidth: 2,
                d: "M11 5.882V19.24a1.76 1.76 0 01-3.417.592l-2.147-6.15M18 13a3 3 0 100-6M5.436 13.683A4.001 4.001 0 017 6h1.832c4.1 0 7.625-1.234 9.168-3v14c-1.543-1.766-5.067-3-9.168-3H7a3.988 3.988 0 01-1.564-.317z"
              })
            })
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(CardTitle, {
            className: "text-2xl",
            children: "Marketing Campaigns Coming Soon"
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(CardDescription, {
            className: "text-lg",
            children: "We're building powerful marketing tools to grow your customer base."
          })]
        }), /* @__PURE__ */ jsxRuntimeExports.jsxs(CardContent, {
          className: "text-center",
          children: [/* @__PURE__ */ jsxRuntimeExports.jsx("p", {
            className: "text-gray-500 mb-4",
            children: "Create targeted campaigns, track performance, and boost your restaurant's visibility."
          }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
            className: "inline-flex items-center px-4 py-2 bg-gradient-to-r from-pink-500 to-purple-600 text-black rounded-lg",
            children: [/* @__PURE__ */ jsxRuntimeExports.jsx("span", {
              className: "mr-2",
              children: "📢"
            }), "Launching Soon"]
          })]
        })]
      })
    })]
  });
}
export {
  Campaigns as default
};
//# sourceMappingURL=sellerSetting.campaigns-BhW4ix8G.js.map
