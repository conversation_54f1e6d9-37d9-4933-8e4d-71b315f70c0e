import { r as reactExports, j as jsxRuntimeExports } from "./jsx-runtime-bB2y7OuD.js";
import { R as ResponsiveTable } from "./responsiveTable-CMOWL3Wl.js";
import { B as Button } from "./button-ByAXMyvk.js";
import { D as Dialog, a as DialogContent, b as DialogTitle } from "./dialog-BqKosxNq.js";
import { u as useToast } from "./ToastProvider-rciVe3-g.js";
import { a as useFetcher, F as Form, u as useLoaderData, d as useActionData } from "./components-D7UvGag_.js";
import { S as SquareX } from "./square-x-CN_sBqMN.js";
import { S as SpinnerLoader } from "./SpinnerLoader-CAZRmIJX.js";
import { I as Input } from "./input-3v87qohQ.js";
import { u as useDebounce } from "./useDebounce-BXbH_IFZ.js";
import { a as useNavigate } from "./index-DhHTcibu.js";
import { T as Trash } from "./trash-cZnr6Uhr.js";
import { P as Pencil } from "./pencil-C1goHmP8.js";
import "./index-z_byfFrQ.js";
import "./index-ImHKLo0a.js";
import "./utils-GkgzjW3c.js";
import "./index-DdafHWkt.js";
import "./index-D7VH9Fc8.js";
import "./index-DVTNuYOr.js";
import "./index-dIGjFc0I.js";
import "./index-BXzPK7u0.js";
import "./index-CzZ6EYkN.js";
import "./index-Vp2vNLNM.js";
import "./index-CEHS9zzk.js";
import "./index-QLGF6kQx.js";
import "./x-CCG_WJDF.js";
import "./createLucideIcon-uwkRm45G.js";
const SelectedVariationAddons = ({
  isOpen,
  items,
  onClose,
  header,
  groupData,
  sellerId,
  groupId,
  isEdit
}) => {
  var _a, _b, _c, _d, _e, _f, _g;
  const [selectedId, setSelectedId] = reactExports.useState(null);
  const [searchTerm, setSearchTerm] = reactExports.useState("");
  const [filteredAddon, setFilteredAddon] = reactExports.useState(items);
  const [choosenAddon, setChoosenAddon] = reactExports.useState(false);
  const [choossenAddonName, setChoosenAddonName] = reactExports.useState("");
  const [formData, setFormData] = reactExports.useState({
    minSelect: (groupData == null ? void 0 : groupData.minSelect.toString()) || "0",
    maxSelect: (groupData == null ? void 0 : groupData.seq.toString()) || "0",
    name: (groupData == null ? void 0 : groupData.name.toString()) || "",
    description: (groupData == null ? void 0 : groupData.description.toString()) || "",
    varient: (groupData == null ? void 0 : groupData.varient) ?? false,
    seq: (groupData == null ? void 0 : groupData.seq) ?? "0"
  });
  const { showToast } = useToast();
  reactExports.useEffect(() => {
    if (searchTerm.length >= 3 && searchTerm !== "") {
      setFilteredAddon(items == null ? void 0 : items.filter((addon) => addon == null ? void 0 : addon.name.toLowerCase().includes(searchTerm.toLowerCase())));
    } else {
      setFilteredAddon(items);
    }
  }, [searchTerm, items]);
  reactExports.useEffect(() => {
    if (!isOpen) {
      setSelectedId(null);
      setSearchTerm("");
      setChoosenAddon(false);
      setFormData({
        minSelect: "0",
        maxSelect: "0",
        name: "",
        description: "",
        varient: false,
        seq: "0"
      });
    }
  }, [isOpen]);
  reactExports.useEffect(() => {
    if (groupData) {
      setChoosenAddon(true);
      setSelectedId(groupData == null ? void 0 : groupData.id);
      setChoosenAddonName(groupData.name);
      setFormData((prev) => ({
        ...prev,
        minSelect: groupData == null ? void 0 : groupData.minSelect.toString(),
        maxSelect: groupData == null ? void 0 : groupData.seq.toString(),
        name: groupData == null ? void 0 : groupData.name.toString(),
        description: groupData == null ? void 0 : groupData.description.toString(),
        varient: groupData == null ? void 0 : groupData.varient
      }));
    }
  }, [groupData]);
  const handleSelect = (addon) => {
    setSelectedId(addon.id);
    setChoosenAddon(true);
    setChoosenAddonName(addon.name);
  };
  const deselectAddon = () => {
    setSelectedId(null);
    setChoosenAddon(false);
  };
  const groupMapfetcher = useFetcher();
  reactExports.useEffect(() => {
    var _a2, _b2;
    if (groupMapfetcher.data) {
      if ((_a2 = groupMapfetcher.data) == null ? void 0 : _a2.sucess) {
        showToast("sucess to Map GroupData", "success");
        onClose();
        setFormData({
          minSelect: "0",
          maxSelect: "0",
          name: "",
          description: "",
          varient: false,
          seq: "0"
        });
      } else if (((_b2 = groupMapfetcher.data) == null ? void 0 : _b2.sucess) === false) {
        showToast("failed to  Map  GroupData", "success");
      }
    }
  }, [groupMapfetcher == null ? void 0 : groupMapfetcher.data]);
  if (!isOpen) return null;
  return /* @__PURE__ */ jsxRuntimeExports.jsx(Dialog, { open: isOpen, onOpenChange: onClose, children: /* @__PURE__ */ jsxRuntimeExports.jsxs(DialogContent, { className: "w-full max-w-md sm:max-w-lg bg-white rounded-2xl shadow-2xl", children: [
    /* @__PURE__ */ jsxRuntimeExports.jsx(DialogTitle, { className: "text-2xl font-bold text-gray-900 mb-4", children: header }),
    /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "space-y-6", children: [
      choosenAddon === false && selectedId === null && /* @__PURE__ */ jsxRuntimeExports.jsxs(jsxRuntimeExports.Fragment, { children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx("div", { children: /* @__PURE__ */ jsxRuntimeExports.jsx(
          "input",
          {
            placeholder: "Search by Addon Name",
            type: "search",
            className: "w-full p-3 rounded-full border-2 border-gray-200 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-colors",
            autoFocus: true,
            value: searchTerm,
            onChange: (e) => setSearchTerm(e.target.value)
          }
        ) }),
        /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "mt-4 max-h-[40vh] overflow-y-auto pr-2 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100", children: /* @__PURE__ */ jsxRuntimeExports.jsx("ul", { className: "space-y-2", children: filteredAddon.length === 0 ? /* @__PURE__ */ jsxRuntimeExports.jsx("p", { className: "p-4 text-gray-500 text-center", children: "No add-ons found" }) : filteredAddon.map((item) => /* @__PURE__ */ jsxRuntimeExports.jsxs("li", { className: "flex items-center gap-3", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx(
            "input",
            {
              type: "checkbox",
              id: `item-${item.id}`,
              name: "selectedItem",
              value: item.id,
              checked: selectedId === item.id,
              onChange: () => handleSelect(item),
              className: "h-5 w-5 text-blue-600 focus:ring-blue-500 rounded"
            }
          ),
          /* @__PURE__ */ jsxRuntimeExports.jsxs(
            "label",
            {
              htmlFor: `item-${item.id}`,
              className: `cursor-pointer p-3 w-full rounded-lg border ${selectedId === item.id ? "bg-blue-50 border-blue-200" : "border-gray-200"} text-gray-800 hover:bg-gray-50 transition-colors`,
              children: [
                item == null ? void 0 : item.name,
                " ",
                /* @__PURE__ */ jsxRuntimeExports.jsxs("span", { className: "text-gray-500", children: [
                  "(",
                  item == null ? void 0 : item.diet,
                  ")"
                ] })
              ]
            }
          )
        ] }, item.id)) }) })
      ] }),
      choosenAddon && selectedId && /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "space-y-4", children: [
        selectedId && /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center justify-between bg-blue-50 p-3 rounded-lg", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx("p", { className: "font-medium text-gray-800 truncate max-w-[80%]", children: choossenAddonName }),
          /* @__PURE__ */ jsxRuntimeExports.jsx(
            SquareX,
            {
              color: "red",
              className: "cursor-pointer hover:scale-110 transition-transform",
              onClick: () => deselectAddon()
            }
          )
        ] }),
        /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "grid grid-cols-1 sm:grid-cols-2 gap-4", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx("label", { htmlFor: "price", className: "block text-sm font-medium text-gray-700 mb-1", children: "name" }),
            /* @__PURE__ */ jsxRuntimeExports.jsx(
              "input",
              {
                type: "text",
                id: "name",
                name: "name",
                placeholder: "Enter the Name",
                value: formData.name,
                onChange: (e) => setFormData({ ...formData, name: e.target.value }),
                required: true,
                className: "w-full p-2 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none"
              }
            )
          ] }),
          /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx("label", { htmlFor: "price", className: "block text-sm font-medium text-gray-700 mb-1", children: "description" }),
            /* @__PURE__ */ jsxRuntimeExports.jsx(
              "input",
              {
                type: "text",
                id: "description",
                name: "description",
                placeholder: "Enter the description",
                value: formData.description,
                onChange: (e) => setFormData({ ...formData, description: e.target.value }),
                required: true,
                className: "w-full p-2 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none"
              }
            )
          ] }),
          /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx("label", { htmlFor: "minSelect", className: "block text-sm font-medium text-gray-700 mb-1", children: "minSelect" }),
            /* @__PURE__ */ jsxRuntimeExports.jsx(
              "input",
              {
                type: "number",
                id: "minSelect",
                name: "minSelect",
                placeholder: "Enter the minimum selection count",
                value: formData.minSelect,
                onChange: (e) => setFormData({ ...formData, minSelect: e.target.value }),
                min: "0",
                required: true,
                className: "w-full p-2 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none"
              }
            )
          ] }),
          /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx("label", { htmlFor: "maxSelect", className: "block text-sm font-medium text-gray-700 mb-1", children: "maxSelect" }),
            /* @__PURE__ */ jsxRuntimeExports.jsx(
              "input",
              {
                type: "number",
                id: "maxSelect",
                name: "maxSelect",
                placeholder: "Enter the maximum selection count",
                value: formData.maxSelect,
                onChange: (e) => setFormData({ ...formData, maxSelect: e.target.value }),
                min: "0",
                required: true,
                className: "w-full p-2 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none"
              }
            )
          ] }),
          /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx("label", { htmlFor: "seq", className: "block text-sm font-medium text-gray-700 mb-1", children: "Sequence" }),
            /* @__PURE__ */ jsxRuntimeExports.jsx(
              "input",
              {
                type: "number",
                id: "seq",
                name: "seq",
                placeholder: "Enter the sequence number",
                value: formData.seq,
                onChange: (e) => setFormData({ ...formData, seq: e.target.value }),
                min: "0",
                required: true,
                className: "w-full p-2 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none"
              }
            )
          ] })
        ] })
      ] })
    ] }),
    /* @__PURE__ */ jsxRuntimeExports.jsxs(Form, { method: "POST", className: "mt-6 flex flex-col sm:flex-row gap-3 justify-end", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx("input", { type: "hidden", name: "addonId", value: selectedId == null ? void 0 : selectedId.toString() }),
      /* @__PURE__ */ jsxRuntimeExports.jsx("input", { type: "hidden", name: "variationId", value: groupId == null ? void 0 : groupId.toString() }),
      /* @__PURE__ */ jsxRuntimeExports.jsx("input", { type: "hidden", name: "minSelect", value: (_a = formData.minSelect) == null ? void 0 : _a.toString() }),
      /* @__PURE__ */ jsxRuntimeExports.jsx("input", { type: "hidden", name: "maxSelect", value: (_b = formData.maxSelect) == null ? void 0 : _b.toString() }),
      /* @__PURE__ */ jsxRuntimeExports.jsx("input", { type: "hidden", name: "varient", value: (_c = formData.varient) == null ? void 0 : _c.toString() }),
      /* @__PURE__ */ jsxRuntimeExports.jsx("input", { type: "hidden", name: "sellerId", value: sellerId == null ? void 0 : sellerId.toString() }),
      /* @__PURE__ */ jsxRuntimeExports.jsx("input", { type: "hidden", name: "name", value: (_d = formData.name) == null ? void 0 : _d.toString() }),
      /* @__PURE__ */ jsxRuntimeExports.jsx("input", { type: "hidden", name: "description", value: (_e = formData.description) == null ? void 0 : _e.toString() }),
      /* @__PURE__ */ jsxRuntimeExports.jsx("input", { type: "hidden", name: "seq", value: (_f = formData.seq) == null ? void 0 : _f.toString() }),
      isEdit && /* @__PURE__ */ jsxRuntimeExports.jsx("input", { type: "hidden", name: "itemVariationId", value: (_g = groupData.id) == null ? void 0 : _g.toString() }),
      /* @__PURE__ */ jsxRuntimeExports.jsx("input", { type: "hidden", name: "addonName", value: choossenAddonName == null ? void 0 : choossenAddonName.toString() }),
      /* @__PURE__ */ jsxRuntimeExports.jsx("input", { type: "hidden", name: "actionType", value: "actionAddonforVariation" }),
      /* @__PURE__ */ jsxRuntimeExports.jsx("input", { type: "hidden", name: "mode", value: isEdit ? "EditMode" : "" }),
      /* @__PURE__ */ jsxRuntimeExports.jsx(
        "button",
        {
          onClick: onClose,
          className: "w-full sm:w-auto px-4 py-2 rounded-lg bg-gray-100 text-gray-700 hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-300 text-sm font-medium",
          children: "Cancel"
        }
      ),
      /* @__PURE__ */ jsxRuntimeExports.jsx(
        "button",
        {
          type: "submit",
          disabled: selectedId === null,
          className: `w-full sm:w-auto px-4 py-2 rounded-lg text-sm font-medium text-white focus:outline-none focus:ring-2 focus:ring-blue-500 ${selectedId === null ? "bg-gray-400 cursor-not-allowed" : "bg-blue-600 hover:bg-blue-700"}`,
          children: "Confirm"
        }
      )
    ] })
  ] }) });
};
const SelectedVariation = () => {
  const {
    selectedVarGruoupData,
    variationName,
    sellerId,
    variationId
  } = useLoaderData();
  const navigate = useNavigate();
  const addonMapfetcher = useFetcher();
  console.log(selectedVarGruoupData, "LLLLLLLLLLLLLL");
  const [isAddselectedGroupAddonsOpen, setIsAddselectedGroupAddonsOpen] = reactExports.useState(false);
  const [selectedGdata, setSelectedGdata] = reactExports.useState();
  const actionData = useActionData();
  const [isEditopen, setIsEditOpen] = reactExports.useState(false);
  const [selectedAddonsData, setSelectedAddonsData] = reactExports.useState();
  const selectedGroupHeader = ["Id", "name", "description", "seq", "minSelect", "maxSelect", "", ""];
  reactExports.useEffect(() => {
    if (addonMapfetcher.state === "idle") {
      if (actionData == null ? void 0 : actionData.selectedAddonsData) {
        setSelectedAddonsData(actionData.selectedAddonsData);
        setIsAddselectedGroupAddonsOpen(true);
        setIsEditOpen(false);
      } else {
        setSelectedAddonsData([]);
        setIsAddselectedGroupAddonsOpen(false);
        setIsEditOpen(false);
      }
    }
  }, [actionData]);
  const handleSelectedGroupData = (row) => {
    setSelectedGdata(row);
    setIsEditOpen(true);
    setIsAddselectedGroupAddonsOpen(true);
  };
  const handleDelete = (addonsmapData) => {
    const formData = new FormData();
    formData.append("actionType", "addonsVarDelete");
    formData.append("addonGId", addonsmapData == null ? void 0 : addonsmapData.id.toString());
    formData.append("addonVarId", variationId.toString());
    formData.append("sellerId", sellerId.toString());
    addonMapfetcher.submit(formData, {
      method: "post"
    });
  };
  const [searchTerm, setSearchTerm] = reactExports.useState("");
  const [filteredVariations, setFilteredVariations] = reactExports.useState([]);
  const loading = addonMapfetcher.state !== "idle";
  const debouncedSearchTerm = useDebounce(searchTerm, 500);
  reactExports.useEffect(() => {
    if (debouncedSearchTerm.length >= 2 && debouncedSearchTerm !== "") {
      const filtered = selectedVarGruoupData.filter((item) => [item.name, item.description].some((field) => field == null ? void 0 : field.toLowerCase().includes(debouncedSearchTerm.toLowerCase())));
      setFilteredVariations(filtered);
    } else {
      setFilteredVariations(selectedVarGruoupData);
    }
  }, [debouncedSearchTerm, selectedVarGruoupData]);
  return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
    className: "h-full",
    children: [loading && /* @__PURE__ */ jsxRuntimeExports.jsx(SpinnerLoader, {
      loading,
      size: 20
    }), /* @__PURE__ */ jsxRuntimeExports.jsxs("h1", {
      className: " mb-4 font-bold cursor-pointer  hover:text-blue-400  text-black   ",
      onClick: () => navigate(-1),
      children: [" ", /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
        className: "text-2xl",
        children: "MyItemVariation / "
      }), " ", /* @__PURE__ */ jsxRuntimeExports.jsxs("span", {
        className: "text-xl",
        children: [variationName, " "]
      }), " "]
    }), /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
      className: "flex flex-wrap mb-2 ",
      children: /* @__PURE__ */ jsxRuntimeExports.jsx(Input, {
        placeholder: "Search by  Name or Description",
        value: searchTerm,
        type: "search",
        onChange: (e) => setSearchTerm(e.target.value),
        className: "max-w-sm mt-2 rounded-full "
      })
    }), /* @__PURE__ */ jsxRuntimeExports.jsx(ResponsiveTable, {
      headers: selectedGroupHeader,
      data: filteredVariations,
      renderRow: (row) => /* @__PURE__ */ jsxRuntimeExports.jsxs("tr", {
        className: "border-b",
        children: [/* @__PURE__ */ jsxRuntimeExports.jsx("td", {
          className: "py-2 px-3 text-center whitespace-normal break-words ",
          children: row.id
        }), /* @__PURE__ */ jsxRuntimeExports.jsx("td", {
          className: "py-2 px-3 text-center whitespace-normal break-words ",
          children: row == null ? void 0 : row.name
        }), /* @__PURE__ */ jsxRuntimeExports.jsx("td", {
          className: "py-2 px-3 text-center whitespace-normal break-words",
          children: row == null ? void 0 : row.description
        }), /* @__PURE__ */ jsxRuntimeExports.jsx("td", {
          className: "py-2 px-3 text-center whitespace-normal break-words",
          children: row == null ? void 0 : row.seq
        }), /* @__PURE__ */ jsxRuntimeExports.jsx("td", {
          className: "py-2 px-3 text-center whitespace-normal break-words",
          children: row == null ? void 0 : row.minSelect
        }), /* @__PURE__ */ jsxRuntimeExports.jsx("td", {
          className: "py-2 px-3 text-center whitespace-normal break-words",
          children: row == null ? void 0 : row.minSelect
        }), /* @__PURE__ */ jsxRuntimeExports.jsx("td", {
          className: "py-2 px-3 text-center cursor-pointer",
          children: /* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
            variant: "ghost",
            size: "sm",
            className: "text-red-500 hover:text-red-900",
            onClick: () => {
              if (confirm("Are you sure you want to delete this ?")) {
                handleDelete(row);
              }
            },
            style: {
              alignSelf: "flex-end"
            },
            children: /* @__PURE__ */ jsxRuntimeExports.jsx(Trash, {
              size: 20
            })
          })
        }), /* @__PURE__ */ jsxRuntimeExports.jsx("td", {
          className: "py-2 px-3 text-center cursor-pointer",
          children: /* @__PURE__ */ jsxRuntimeExports.jsx(Pencil, {
            color: "blue",
            size: 20,
            onClick: () => handleSelectedGroupData(row)
          })
        })]
      }, row.id)
    }), /* @__PURE__ */ jsxRuntimeExports.jsxs(Form, {
      method: "post",
      children: [/* @__PURE__ */ jsxRuntimeExports.jsx("input", {
        name: "sellerId",
        value: sellerId,
        hidden: true
      }), /* @__PURE__ */ jsxRuntimeExports.jsx("input", {
        name: "matchBy",
        value: "",
        hidden: true
      }), /* @__PURE__ */ jsxRuntimeExports.jsx("input", {
        name: "actionType",
        value: "getAddons",
        hidden: true
      }), /* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
        className: "fixed bottom-5 right-5 rounded-full cursor-pointer",
        type: "submit",
        children: "+ Create Addon for Item Variation"
      })]
    }), /* @__PURE__ */ jsxRuntimeExports.jsx(SelectedVariationAddons, {
      isOpen: isAddselectedGroupAddonsOpen,
      items: selectedAddonsData || [],
      onClose: () => setIsAddselectedGroupAddonsOpen(false),
      header: isEditopen ? `Edit Addon for${variationName == null ? void 0 : variationName.slice(0, 15)}` : `Create Addon for ${variationName == null ? void 0 : variationName.slice(0, 15)} `,
      groupData: selectedGdata,
      sellerId,
      groupId: variationId,
      isEdit: isEditopen
    })]
  });
};
export {
  SelectedVariation as default
};
//# sourceMappingURL=home.selectedVariation-CogSx-gO.js.map
