{"version": 3, "file": "home.agentWiseSales-BcmHcWmz.js", "sources": ["../../../app/routes/home.agentWiseSales.tsx"], "sourcesContent": ["import { j<PERSON>, LoaderFunction } from \"@remix-run/node\";\r\nimport { Form, useFetcher, useLoaderData, useNavigate, useSearchParams } from \"@remix-run/react\";\r\nimport * as React from \"react\";\r\nimport { ArrowLeft, CalendarIcon } from \"lucide-react\";\r\nimport { But<PERSON> } from \"~/components/ui/button\";\r\nimport { Input } from \"~/components/ui/input\";\r\nimport {\r\n      Popover,\r\n      PopoverContent,\r\n      PopoverTrigger,\r\n} from \"~/components/ui/popover\";\r\nimport { Calendar } from \"~/components/ui/calendar\";\r\nimport { format } from \"date-fns\";\r\nimport {\r\n      Select,\r\n      SelectContent,\r\n      SelectItem,\r\n      SelectTrigger,\r\n      SelectValue,\r\n} from \"~/components/ui/select\";\r\nimport { SalesData, SalesDetails, SellerSalesInfo } from \"~/types/api/businessConsoleService/salesinfo\";\r\nimport { getPrivateSellerSales, getSalesData } from \"~/services/salesinfoDetails\";\r\nimport { withAuth, withResponse } from \"@utils/auth-utils\";\r\nimport { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from \"~/components/ui/tabs\";\r\nimport { useState } from \"react\";\r\nimport { Pagination, PaginationContent, PaginationItem, PaginationLink, PaginationNext, PaginationPrevious } from \"~/components/ui/pagination\";\r\nimport { ResponsiveTable } from \"~/components/ui/responsiveTable\";\r\nimport { DateRange } from \"react-day-picker\";\r\nimport { cn } from \"~/lib/utils\";\r\n\r\nexport interface LoaderData {\r\n      salesData: SalesDetails,\r\n      sellerId: number,\r\n      selectedTab: string,\r\n      AgentId: number,\r\n      name: string,\r\n      areaId: number,\r\n      sellerRole: string\r\n\r\n\r\n}\r\n\r\nexport const loader: LoaderFunction = withAuth(async ({ request, user }) => {\r\n      const url = new URL(request.url);\r\n      const sellerPermission = user.userPermissions?.includes('seller_app.scBasic')\r\n      const fromDate = url.searchParams.get(\"from\") || new Date().toISOString().split(\"T\")[0];\r\n      const toDate = url.searchParams.get(\"to\") || fromDate;\r\n      const selectedTab = url.searchParams.get(\"selectedTab\")\r\n      const activeTab = url.searchParams.get(\"activeTab\") || selectedTab;\r\n      const sellerId = Number(url.searchParams.get(\"sellerId\"));\r\n      const name = (url.searchParams.get(\"name\"));\r\n      const agentId = Number(url.searchParams.get(\"agentId\"));\r\n      const areaId = Number(url.searchParams.get(\"areaId\"));\r\n      const page = Number(url.searchParams.get(\"page\")) || 0;\r\n      const pageSize = Number(url.searchParams.get(\"pageSize\")) || 10;\r\n      if (sellerPermission) {\r\n            try {\r\n                  let response = null;\r\n                  switch (activeTab) {\r\n\r\n                        case 'agentWise':\r\n                              response = await getPrivateSellerSales(page, pageSize, \"agentwise\", fromDate, toDate, request, sellerId, agentId, areaId)\r\n                              break;\r\n                        case 'localityWise':\r\n                              response = await getPrivateSellerSales(page, pageSize, \"localitywise\", fromDate, toDate, request, sellerId, agentId, areaId)\r\n                              break;\r\n                        default:\r\n                              // If no valid activeTab is provided, handle the default case (optional)\r\n                              console.log(\"No valid activeTab selected\");\r\n                              break;\r\n\r\n                  }\r\n                  return withResponse({\r\n                        salesData: response?.data,\r\n                        sellerId: sellerId,\r\n                        selectedTab: selectedTab,\r\n                        AgentId: agentId,\r\n                        name: name,\r\n                        areaId: areaId,\r\n                        sellerPermission: sellerPermission\r\n\r\n                  }, response?.headers)\r\n            } catch (error) {\r\n                  console.error(\"Seller sales error:\", error);\r\n                  throw new Response(\"Failed to fetch seller sales\", { status: 500 });\r\n            }\r\n      }\r\n      else {\r\n\r\n            try {\r\n                  let response = null;\r\n\r\n                  switch (activeTab) {\r\n                        case 'sellerWise':\r\n                              response = await getSalesData(0, pageSize, \"Sellerwise\", fromDate, toDate, request, sellerId, agentId, areaId)\r\n\r\n                              break;\r\n                        case 'agentWise':\r\n                              response = await getSalesData(page, pageSize, \"Agentwise\", fromDate, toDate, request, sellerId, agentId, areaId)\r\n                              break;\r\n                        case 'localityWise':\r\n                              response = await getSalesData(page, pageSize, \"Localitywise\", fromDate, toDate, request, sellerId, agentId, areaId)\r\n                              break;\r\n                        default:\r\n                              // If no valid activeTab is provided, handle the default case (optional)\r\n                              console.log(\"No valid activeTab selected\");\r\n                              break;\r\n\r\n                  }\r\n                  return withResponse({\r\n                        salesData: response?.data,\r\n                        sellerId: sellerId,\r\n                        selectedTab: selectedTab,\r\n                        AgentId: agentId,\r\n                        name: name,\r\n                        areaId: areaId,\r\n                        sellerRole: sellerPermission\r\n\r\n                  }, response?.headers)\r\n            } catch (error) {\r\n                  console.error(\"Seller sales error:\", error);\r\n                  throw new Response(\"Failed to fetch seller sales\", { status: 500 });\r\n            }\r\n      }\r\n});\r\nexport default function AgentWiseSales() {\r\n\r\n      const navigate = useNavigate();\r\n      const { salesData, sellerId, selectedTab, AgentId, name, areaId, sellerRole} = useLoaderData<LoaderData>()\r\n\r\n      const [activeTab, setActiveTab] = React.useState(selectedTab === \"localityWise\" ? \"sellerWise\" : selectedTab);\r\n      const [currentPage, setCurrentPage] = useState(0)\r\n      const [searchParams] = useSearchParams();\r\n      const selectedDate = searchParams.get(\"date\")\r\n      const [date, setDate] = React.useState<Date>(\r\n            selectedDate ? new Date(selectedDate) : new Date() // Parse the date if present; use the current date otherwise\r\n      );\r\n      const startDate = searchParams.get(\"from\")\r\n      const endDate = searchParams.get(\"to\")\r\n\r\n\r\n      const [dateRange, setDateRange] = React.useState<DateRange>({\r\n            from: startDate ? new Date(startDate) : new Date(),\r\n            to: endDate ? new Date(endDate) : new Date(),\r\n      });\r\n\r\n\r\n      const itemsPerPage = 20;\r\n      const handleTabChange = (newTab: string) => {\r\n            if (!dateRange.from) return; // Ensure 'from' date exists\r\n\r\n            const formattedFrom = format(dateRange.from, \"yyyy-MM-dd\");\r\n            const formattedTo = dateRange.to ? format(dateRange.to, \"yyyy-MM-dd\") : formattedFrom;\r\n            setActiveTab(newTab);\r\n            navigate(`?activeTab=${newTab}&page=${currentPage}&pageSize=${itemsPerPage}&from=${formattedFrom}&to=${formattedTo}&agentId=${AgentId}&sellerId=${sellerId}&selectedTab=${selectedTab}&name=${name}&areaId=${areaId}`)\r\n      };\r\n      const handlePageChange = (newPage: number) => {\r\n            if (!dateRange.from) return; // Ensure 'from' date exists\r\n            const formattedFrom = format(dateRange.from, \"yyyy-MM-dd\");\r\n            const formattedTo = dateRange.to ? format(dateRange.to, \"yyyy-MM-dd\") : formattedFrom;\r\n            setCurrentPage(newPage)\r\n            navigate(`?activeTab=${activeTab}&page=${newPage}&pageSize=${itemsPerPage}&from=${formattedFrom}&to=${formattedTo}&agentId=${AgentId}&sellerId=${sellerId}&selectedTab=${selectedTab}&name=${name}&areaId=${areaId}`);\r\n      };\r\n      const totalDeliveredWt = salesData?.salesData && Array.isArray(salesData.salesData)\r\n            ? salesData.salesData.map((x) => x?.deliveredWeight || 0).reduce((acc, wet) => acc + wet, 0)\r\n            : 0;\r\n\r\n      const totalReturnsWt = salesData?.salesData && Array.isArray(salesData.salesData)\r\n            ? salesData.salesData.map((x) => x?.returnedWeight || 0).reduce((a, b) => a + b, 0)\r\n            : 0;\r\n\r\n      const totalBookingWt = salesData?.salesData && Array.isArray(salesData.salesData)\r\n            ? salesData.salesData.map((x) => x?.bookedWeight || 0).reduce((a, b) => a + b, 0)\r\n            : 0;\r\n\r\n      const totalCancelWt = salesData?.salesData && Array.isArray(salesData.salesData)\r\n            ? salesData.salesData.map((x) => x?.cancelledWeight || 0).reduce((a, b) => a + b, 0) : 0;\r\n      const sellerWiseHeaders = [\r\n            \"Seller ID\",\r\n            \"Seller Name\",\r\n            \"Booked Qty\",\r\n            \"Delivered Qty\",\r\n            \"Return Qty\",\r\n            \"Cancel Qty\",\r\n            \"Total Shops\",\r\n            \"Ordered Shops\",\r\n            \"Active Shops\"\r\n\r\n\r\n\r\n      ];\r\n      const AgentWiseHeaders = [\r\n            \"Agent ID\",\r\n            \"Agent Name\",\r\n            \"Booked Qty\",\r\n            \"Delivered Qty\",\r\n            \"Return Qty\",\r\n            \"Cancel Qty\",\r\n            \"Total Shops\",\r\n            \"Ordered Shops\",\r\n            \"Active Shops\"\r\n\r\n      ];\r\n      const sellerAgentWiseHeaders = [\r\n\r\n            \"Agent ID\",\r\n            \"Agent Name\",\r\n            \"Booked Qty\",\r\n            \"Delivered Qty\",\r\n            \"Return Qty\",\r\n            \"Cancel Qty\",\r\n            \"Ordered Shops\",\r\n            \"BookedAmt\",\r\n            \"DeliveredAmt\",\r\n           \r\n\r\n      ]\r\n\r\n\r\n      const LocalityWise = [\r\n            \"Locality ID\",\r\n            \"Locality Name\",\r\n            \"Booked Qty\",\r\n            \"Delivered Qty\",\r\n            \"Return Qty\",\r\n            \"Cancel Qty\",\r\n            \"Total Shops\",\r\n            \"Ordered Shops\",\r\n            \"Active Shops\"\r\n\r\n      ];\r\n      const sellerLocalityWise = [\r\n            \"Locality ID\",\r\n            \"Locality Name\",\r\n            \"Booked Qty\",\r\n            \"Delivered Qty\",\r\n            \"Return Qty\",\r\n            \"Cancel Qty\",\r\n            \"Active Shops\",\r\n             \"BookedAmt\",\r\n            \"DeliveredAmt\"\r\n\r\n      ];\r\n      const footerTotals = [\r\n            \"\",\r\n            \"\",\r\n            totalBookingWt.toFixed(2),\r\n            totalDeliveredWt.toFixed(2),\r\n            totalReturnsWt.toFixed(2),\r\n            totalCancelWt.toFixed(2)\r\n\r\n      ];\r\n      const handleViewSales = () => {\r\n\r\n            if (!dateRange.from) return; // Ensure 'from' date exists\r\n\r\n            const formattedFrom = format(dateRange.from, \"yyyy-MM-dd\");\r\n            const formattedTo = dateRange.to ? format(dateRange.to, \"yyyy-MM-dd\") : formattedFrom;\r\n            navigate(`?activeTab=${activeTab}&page=${currentPage}&pageSize=${itemsPerPage}&from=${formattedFrom}&to=${formattedTo}&agentId=${AgentId}&sellerId=${sellerId}&selectedTab=${selectedTab}&name=${name}&areaId=${areaId}`);\r\n\r\n      }\r\n\r\n      const handleDateChange = (range: DateRange | undefined) => {\r\n            if (!range?.from) return; // Ensure 'from' is never undefined\r\n\r\n            setDateRange({\r\n                  from: range.from,\r\n                  to: range.to || undefined, // If 'to' is not selected, keep it undefined\r\n            });\r\n      };\r\n\r\n      return (\r\n            <div className=\"container mx-auto w-full p-6\">\r\n                  <div className=\" items-center mb-6\">\r\n                        <Button variant=\"ghost\" size=\"sm\" onClick={() => navigate(`/home/<USER>\"yyyy-MM-dd\")}`)}>\r\n                              <ArrowLeft className=\"h-4 w-4 mr-2\" />\r\n                              Back to Sales Report\r\n                        </Button>\r\n                        <span className=\"text-muted-foreground\">/</span>\r\n                        <span className=\"font-semibold\">{name}</span>\r\n                  </div>\r\n                  <div className=\"flex flex-col space-y-5 md:flex-row md:space-x-5 md:space-y-0 my-5\">\r\n                        <div className=\"grid grid-cols-1 gap-3 md:flex md:items-center md:space-x-2\">\r\n                              <div className=\"w-full md:w-auto\">\r\n                                    <Popover>\r\n                                          <PopoverTrigger asChild>\r\n                                                <Button\r\n                                                      id=\"date\"\r\n                                                      variant={\"outline\"}\r\n                                                      className={cn(\r\n                                                            \"w-[300px] justify-start text-left font-normal\",\r\n                                                            !dateRange.from && \"text-muted-foreground\"\r\n                                                      )}\r\n                                                >\r\n                                                      <CalendarIcon />\r\n                                                      {dateRange?.from ? (\r\n                                                            dateRange.to ? (\r\n                                                                  <>\r\n                                                                        {format(dateRange.from, \"LLL dd, y\")} - {format(dateRange.to, \"LLL dd, y\")}\r\n                                                                  </>\r\n                                                            ) : (\r\n                                                                  format(dateRange.from, \"LLL dd, y\")\r\n                                                            )\r\n                                                      ) : (\r\n                                                            <span>Pick a date</span>\r\n                                                      )}\r\n                                                </Button>\r\n\r\n                                          </PopoverTrigger>\r\n                                          <PopoverContent className=\"w-auto p-0\" align=\"start\">\r\n                                                <Calendar\r\n                                                      initialFocus\r\n                                                      selected={dateRange}\r\n                                                      mode=\"range\" // Enable range selection\r\n                                                      onSelect={handleDateChange}\r\n                                                />\r\n                                          </PopoverContent>\r\n                                    </Popover>\r\n                              </div>\r\n\r\n                              <Button\r\n                                    type=\"submit\"\r\n                                    className=\"w-full md:w-auto md:rounded-full\"\r\n                                    onClick={() => handleViewSales()}\r\n\r\n                              >\r\n                                    View Report\r\n                              </Button>\r\n                        </div>\r\n\r\n                  </div>\r\n                  <Tabs value={activeTab} onValueChange={handleTabChange} className=\"my-5\">\r\n                        <TabsList>\r\n                              {!sellerRole && (selectedTab === \"sellerWise\" || selectedTab === \"localityWise\") && <TabsTrigger value=\"sellerWise\">Seller Wise</TabsTrigger>\r\n                              }\r\n                              {!sellerRole && (selectedTab === \"agentWise\" || selectedTab === \"localityWise\") && <TabsTrigger value=\"agentWise\">Agent Wise</TabsTrigger>\r\n                              }\r\n                              {(selectedTab !== \"localityWise\") && <TabsTrigger value=\"localityWise\">Locality Wise</TabsTrigger>\r\n                              }                        </TabsList>\r\n                        <TabsContent value=\"sellerWise\">\r\n                              <ResponsiveTable\r\n                                    headers={sellerWiseHeaders}\r\n                                    data={\r\n                                          salesData.salesData\r\n                                    }\r\n                                    renderRow={(row) => (\r\n                                          <tr key={row.id} className=\"border-b\">\r\n                                                <td className=\"py-2 px-3 font-medium text-left\">{row.id}</td>\r\n                                                <td className=\"py-2 px-3  text-left\">\r\n                                                      {row.name}\r\n                                                </td>\r\n                                                <td className=\"py-2 px-3 text-right\">\r\n                                                      {row?.bookedWeight.toFixed(1) || \"-\"}\r\n                                                </td>\r\n                                                <td className=\"py-2 px-3 text-right\">\r\n                                                      {row?.deliveredWeight.toFixed(1) || \"-\"}\r\n                                                </td>\r\n                                                <td className=\"py-2 px-3 text-right\">\r\n                                                      {row?.returnedWeight.toFixed(1) || \"-\"}\r\n                                                </td>\r\n                                                <td className=\"py-2 px-3 text-right\">\r\n                                                      {row?.cancelledWeight.toFixed(1) || \"-\"}\r\n                                                </td>\r\n                                                <td className=\"py-2 px-3 text-right\">\r\n                                                      {row?.customerCount || \"-\"}\r\n                                                </td>\r\n                                                <td className=\"py-2 px-3 text-right\">\r\n                                                      {row?.orderCount || \"-\"}\r\n                                                </td>\r\n                                                <td className=\"py-2 px-3 text-right\">\r\n                                                      {row?.activeShopCount || \"-\"}\r\n                                                </td>\r\n                                          </tr>\r\n                                    )}\r\n                                    footerTotals={footerTotals}\r\n                                    emptyMessage=\"No data available for the selected filters.\"\r\n                              />\r\n                        </TabsContent>\r\n                        <TabsContent value=\"agentWise\">\r\n                              <ResponsiveTable\r\n                                    headers={sellerRole?sellerAgentWiseHeaders:AgentWiseHeaders}\r\n                                    data={\r\n                                          salesData.salesData\r\n                                    }\r\n                                    renderRow={(row) => (\r\n                                          <tr key={row.id} className=\"border-b\">\r\n                                                <td className=\"py-2 px-3 font-medium text-left\">{row.id}</td>\r\n                                                <td className=\"py-2 px-3  text-left\">\r\n\r\n                                                      {row.name}\r\n\r\n                                                </td>\r\n                                                <td className=\"py-2 px-3 text-right\">\r\n                                                      {row?.bookedWeight.toFixed(1) || \"-\"}\r\n                                                </td>\r\n                                                <td className=\"py-2 px-3 text-right\">\r\n                                                      {row?.deliveredWeight.toFixed(1) || \"-\"}\r\n                                                </td>\r\n                                                <td className=\"py-2 px-3 text-right\">\r\n                                                      {row?.returnedWeight.toFixed(1) || \"-\"}\r\n                                                </td>\r\n                                                <td className=\"py-2 px-3 text-right\">\r\n                                                      {row?.cancelledWeight.toFixed(1) || \"-\"}\r\n                                                </td>\r\n                                                {!sellerRole && (\r\n <td className=\"py-2 px-3 text-right\">\r\n                                                      {row?.customerCount || \"-\"}\r\n                                                </td>)}\r\n                                                <td className=\"py-2 px-3 text-right\">\r\n                                                      {row?.orderCount || \"-\"}\r\n                                                </td>\r\n                                                <td className=\"py-2 px-3 text-right\">\r\n                                                      {row?.activeShopCount || \"-\"}\r\n                                                </td>\r\n                                                {sellerRole && (\r\n                                                      <td className=\"py-2 px-3 text-right\">\r\n                                                            {row?.bookedAmount.toFixed(2) || \"-\"}\r\n                                                      </td>\r\n                                                )}\r\n                                                {sellerRole && (\r\n                                                      <td className=\"py-2 px-3 text-right\">\r\n                                                            {row?.deliveredAmount.toFixed(2) || \"-\"}\r\n                                                      </td>\r\n                                                )}\r\n                                          </tr>\r\n                                    )}\r\n                                    footerTotals={footerTotals}\r\n                                    emptyMessage=\"No data available for the selected filters.\"\r\n                              />\r\n                        </TabsContent>\r\n                        <TabsContent value=\"localityWise\">\r\n                              <ResponsiveTable\r\n                                    headers={ sellerRole?sellerLocalityWise:LocalityWise}\r\n                                    data={\r\n                                          salesData.salesData\r\n                                    }\r\n                                    renderRow={(row) => (\r\n                                          <tr key={row.id} className=\"border-b\">\r\n                                                <td className=\"py-2 px-3 font-medium text-left\">{row.id}</td>\r\n                                                <td className=\"py-2 px-3  text-left\">\r\n\r\n                                                      {row.name}\r\n\r\n                                                </td>\r\n                                                <td className=\"py-2 px-3 text-right\">\r\n                                                      {row?.bookedWeight.toFixed(1) || \"-\"}\r\n                                                </td>\r\n                                                <td className=\"py-2 px-3 text-right\">\r\n                                                      {row?.deliveredWeight.toFixed(1) || \"-\"}\r\n                                                </td>\r\n                                                <td className=\"py-2 px-3 text-right\">\r\n                                                      {row?.returnedWeight.toFixed(1) || \"-\"}\r\n                                                </td>\r\n                                                <td className=\"py-2 px-3 text-right\">\r\n                                                      {row?.cancelledWeight.toFixed(1) || \"-\"}\r\n                                                </td>\r\n                                                {sellerRole && (\r\n<td className=\"py-2 px-3 text-right\">\r\n                                                      {row?.customerCount || \"-\"}\r\n                                                </td>)}\r\n                                                <td className=\"py-2 px-3 text-right\">\r\n                                                      {row?.orderCount || \"-\"}\r\n                                                </td>\r\n                                                {!sellerRole && (\r\n  <td className=\"py-2 px-3 text-right\">\r\n                                                      {row?.activeShopCount || \"-\"}\r\n                                                </td>)}\r\n                                                {sellerRole && (\r\n                                                      <td className=\"py-2 px-3 text-right\">\r\n                                                            {row?.bookedAmount.toFixed(2) || \"-\"}\r\n                                                      </td>\r\n                                                )}\r\n                                                {sellerRole && (\r\n                                                      <td className=\"py-2 px-3 text-right\">\r\n                                                            {row?.deliveredAmount.toFixed(2) || \"-\"}\r\n                                                      </td>\r\n                                                )}\r\n                                                 \r\n                                          </tr>\r\n                                    )}\r\n                                    footerTotals={footerTotals}\r\n                                    emptyMessage=\"No data available for the selected filters.\"\r\n                              />\r\n                        </TabsContent>\r\n                  </Tabs>\r\n                  {/* <div className=\"rounded-md border\">\r\n                        <TabsContent value=\"sellerWise\">\r\n                              <ResponsiveTable\r\n                                    headers={sellerWiseHeaders}\r\n                                    data={salesData}\r\n                                    renderRow={(row) => (\r\n                                          <tr key={row.id} className=\"border-b\">\r\n                                                <td className=\"py-2 px-3 font-medium text-left\">{row.id}</td>\r\n                                                <td className=\"py-2 px-3 text-blue-600 underline cursor-pointer text-left\">\r\n                                                      <span\r\n                                                            onClick={() =>\r\n                                                                  navigate(\r\n                                                                        `/home/<USER>\n                                                                              \"en-CA\"\r\n                                                                        )}&sellerName=${row.name.toString()}`\r\n                                                                  )\r\n                                                            }\r\n                                                      >\r\n                                                            {row.name}\r\n                                                      </span>\r\n                                                </td>\r\n                                                <td className=\"py-2 px-3 text-right\">\r\n                                                      {row.bookedWeight.toFixed(2)}\r\n                                                </td>\r\n                                                <td className=\"py-2 px-3 text-right\">\r\n                                                      {row.deliveredWeight.toFixed(2)}\r\n                                                </td>\r\n                                                <td className=\"py-2 px-3 text-right\">\r\n                                                      {row.returnedWeight.toFixed(2)}\r\n                                                </td>\r\n                                                <td className=\"py-2 px-3 text-right\">\r\n                                                      {row.cancelledWeight.toFixed(2)}\r\n                                                </td>\r\n                                          </tr>\r\n                                    )}\r\n                                    footerTotals={footerTotals}\r\n                                    emptyMessage=\"No data available for the selected filters.\"\r\n                              />\r\n                        </TabsContent>\r\n                        <TabsContent value=\"agentWise\">\r\n                              <ResponsiveTable\r\n                                    headers={AgentWiseHeaders}\r\n                                    data={data}\r\n                                    renderRow={(row) => (\r\n                                          <tr key={row.sellerId} className=\"border-b\">\r\n                                                <td className=\"py-2 px-3 font-medium text-left\">{row.sellerId}</td>\r\n                                                <td className=\"py-2 px-3 text-blue-600 underline cursor-pointer text-left\">\r\n                                                      <span\r\n                                                            onClick={() =>\r\n                                                                  navigate(\r\n                                                                        `/home/<USER>\n                                                                              \"en-CA\"\r\n                                                                        )}&sellerName=${row.sellerName.toString()}`\r\n                                                                  )\r\n                                                            }\r\n                                                      >\r\n                                                            {row.sellerName}\r\n                                                      </span>\r\n                                                </td>\r\n                                                <td className=\"py-2 px-3 text-right\">\r\n                                                      {row.totalBookedWeight.toFixed(2)}\r\n                                                </td>\r\n                                                <td className=\"py-2 px-3 text-right\">\r\n                                                      {row.totalDeliveredWeight.toFixed(2)}\r\n                                                </td>\r\n                                                <td className=\"py-2 px-3 text-right\">\r\n                                                      {row.totalReturnedWeight.toFixed(2)}\r\n                                                </td>\r\n                                                <td className=\"py-2 px-3 text-right\">\r\n                                                      {row.totalCancelledWeight.toFixed(2)}\r\n                                                </td>\r\n                                          </tr>\r\n                                    )}\r\n                                    footerTotals={footerTotals}\r\n                                    emptyMessage=\"No data available for the selected filters.\"\r\n                              />\r\n                        </TabsContent>\r\n                        <TabsContent value=\"localityWise\">\r\n                              <ResponsiveTable\r\n                                    headers={LocalityWise}\r\n                                    data={data}\r\n                                    renderRow={(row) => (\r\n                                          <tr key={row.sellerId} className=\"border-b\">\r\n                                                <td className=\"py-2 px-3 font-medium text-left\">{row.sellerId}</td>\r\n                                                <td className=\"py-2 px-3 text-blue-600 underline cursor-pointer text-left\">\r\n                                                      <span\r\n                                                            onClick={() =>\r\n                                                                  navigate(\r\n                                                                        `/home/<USER>\n                                                                              \"en-CA\"\r\n                                                                        )}&sellerName=${row.sellerName.toString()}`\r\n                                                                  )\r\n                                                            }\r\n                                                      >\r\n                                                            {row.sellerName}\r\n                                                      </span>\r\n                                                </td>\r\n                                                <td className=\"py-2 px-3 text-right\">\r\n                                                      {row.totalBookedWeight.toFixed(2)}\r\n                                                </td>\r\n                                                <td className=\"py-2 px-3 text-right\">\r\n                                                      {row.totalDeliveredWeight.toFixed(2)}\r\n                                                </td>\r\n                                                <td className=\"py-2 px-3 text-right\">\r\n                                                      {row.totalReturnedWeight.toFixed(2)}\r\n                                                </td>\r\n                                                <td className=\"py-2 px-3 text-right\">\r\n                                                      {row.totalCancelledWeight.toFixed(2)}\r\n                                                </td>\r\n                                          </tr>\r\n                                    )}\r\n                                    footerTotals={footerTotals}\r\n                                    emptyMessage=\"No data available for the selected filters.\"\r\n                              />\r\n                        </TabsContent>\r\n                  </div> */}\r\n                  <div className=\"flex justify-between items-center mt-6 overflow-hidden \">\r\n                        <Pagination>\r\n                              <PaginationContent>\r\n                                    {currentPage > 0 && (\r\n                                          <PaginationItem>\r\n                                                <PaginationPrevious onClick={() => handlePageChange(currentPage - 1)} />\r\n                                          </PaginationItem>\r\n                                    )}\r\n                                    <PaginationItem>\r\n                                          <PaginationLink>{currentPage + 1}</PaginationLink>\r\n                                    </PaginationItem>\r\n                                    <PaginationItem>\r\n                                          <PaginationNext onClick={() => handlePageChange(currentPage + 1)} />\r\n                                    </PaginationItem>\r\n                              </PaginationContent>\r\n                        </Pagination>\r\n                  </div>\r\n            </div>\r\n\r\n      );\r\n}\r\n"], "names": ["AgentWiseSales", "navigate", "useNavigate", "salesData", "sellerId", "selectedTab", "AgentId", "name", "areaId", "sellerRole", "useLoaderData", "activeTab", "setActiveTab", "React", "currentPage", "setCurrentPage", "useState", "searchParams", "useSearchParams", "selectedDate", "get", "date", "setDate", "Date", "startDate", "endDate", "date<PERSON><PERSON><PERSON>", "setDateRange", "from", "to", "itemsPerPage", "handleTabChange", "newTab", "formattedFrom", "format", "formattedTo", "handlePageChange", "newPage", "totalDeliveredWt", "Array", "isArray", "map", "x", "deliveredWeight", "reduce", "acc", "wet", "totalReturnsWt", "returnedWeight", "a", "b", "totalBookingWt", "bookedWeight", "totalCancelWt", "cancelledWeight", "sellerWiseHeaders", "AgentWiseHeaders", "sellerAgentWiseHeaders", "LocalityWise", "sellerLocalityWise", "footerTotals", "toFixed", "handleViewSales", "handleDateChange", "range", "jsxs", "className", "children", "<PERSON><PERSON>", "variant", "size", "onClick", "jsx", "ArrowLeft", "Popover", "PopoverTrigger", "<PERSON><PERSON><PERSON><PERSON>", "id", "cn", "CalendarIcon", "Fragment", "PopoverC<PERSON>nt", "align", "Calendar", "initialFocus", "selected", "mode", "onSelect", "type", "Tabs", "value", "onValueChange", "TabsList", "TabsTrigger", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ResponsiveTable", "headers", "data", "renderRow", "row", "customerCount", "orderCount", "activeShopCount", "emptyMessage", "bookedAmount", "deliveredAmount", "Pagination", "Pa<PERSON><PERSON><PERSON><PERSON><PERSON>", "PaginationItem", "PaginationPrevious", "PaginationLink", "PaginationNext"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6HA,SAAwBA,iBAAiB;AAEnC,QAAMC,WAAWC,YAAY;AACvB,QAAA;AAAA,IAAEC;AAAAA,IAAWC;AAAAA,IAAUC;AAAAA,IAAaC;AAAAA,IAASC;AAAAA,IAAMC;AAAAA,IAAQC;AAAAA,EAAU,IAAIC,cAA0B;AAEnG,QAAA,CAACC,WAAWC,YAAY,IAAIC,aAAAA,SAAeR,gBAAgB,iBAAiB,eAAeA,WAAW;AAC5G,QAAM,CAACS,aAAaC,cAAc,IAAIC,aAAAA,SAAS,CAAC;AAC1C,QAAA,CAACC,YAAY,IAAIC,gBAAgB;AACjC,QAAAC,eAAeF,aAAaG,IAAI,MAAM;AAC5C,QAAM,CAACC,MAAMC,OAAO,IAAIT,aAAM;AAAA,IACxBM,eAAe,IAAII,KAAKJ,YAAY,wBAAQI,KAAK;AAAA;AAAA,EACvD;AACM,QAAAC,YAAYP,aAAaG,IAAI,MAAM;AACnC,QAAAK,UAAUR,aAAaG,IAAI,IAAI;AAGrC,QAAM,CAACM,WAAWC,YAAY,IAAId,sBAA0B;AAAA,IACtDe,MAAMJ,YAAY,IAAID,KAAKC,SAAS,wBAAQD,KAAK;AAAA,IACjDM,IAAIJ,UAAU,IAAIF,KAAKE,OAAO,wBAAQF,KAAK;AAAA,EACjD,CAAC;AAGD,QAAMO,eAAe;AACf,QAAAC,kBAAmBC,YAAmB;AAClC,QAAA,CAACN,UAAUE,KAAM;AAErB,UAAMK,gBAAgBC,OAAOR,UAAUE,MAAM,YAAY;AACzD,UAAMO,cAAcT,UAAUG,KAAKK,OAAOR,UAAUG,IAAI,YAAY,IAAII;AACxErB,iBAAaoB,MAAM;AACV/B,aAAA,cAAc+B,MAAM,SAASlB,WAAW,aAAagB,YAAY,SAASG,aAAa,OAAOE,WAAW,YAAY7B,OAAO,aAAaF,QAAQ,gBAAgBC,WAAW,SAASE,IAAI,WAAWC,MAAM,EAAE;AAAA,EAC3N;AACM,QAAA4B,mBAAoBC,aAAoB;AACpC,QAAA,CAACX,UAAUE,KAAM;AACrB,UAAMK,gBAAgBC,OAAOR,UAAUE,MAAM,YAAY;AACzD,UAAMO,cAAcT,UAAUG,KAAKK,OAAOR,UAAUG,IAAI,YAAY,IAAII;AACxElB,mBAAesB,OAAO;AACbpC,aAAA,cAAcU,SAAS,SAAS0B,OAAO,aAAaP,YAAY,SAASG,aAAa,OAAOE,WAAW,YAAY7B,OAAO,aAAaF,QAAQ,gBAAgBC,WAAW,SAASE,IAAI,WAAWC,MAAM,EAAE;AAAA,EAC1N;AACM,QAAA8B,oBAAmBnC,uCAAWA,cAAaoC,MAAMC,QAAQrC,UAAUA,SAAS,IAC1EA,UAAUA,UAAUsC,IAAKC,QAAMA,uBAAGC,oBAAmB,CAAC,EAAEC,OAAO,CAACC,KAAKC,QAAQD,MAAMC,KAAK,CAAC,IACzF;AAEF,QAAAC,kBAAiB5C,uCAAWA,cAAaoC,MAAMC,QAAQrC,UAAUA,SAAS,IACxEA,UAAUA,UAAUsC,IAAKC,QAAMA,uBAAGM,mBAAkB,CAAC,EAAEJ,OAAO,CAACK,GAAGC,MAAMD,IAAIC,GAAG,CAAC,IAChF;AAEF,QAAAC,kBAAiBhD,uCAAWA,cAAaoC,MAAMC,QAAQrC,UAAUA,SAAS,IACxEA,UAAUA,UAAUsC,IAAKC,QAAMA,uBAAGU,iBAAgB,CAAC,EAAER,OAAO,CAACK,GAAGC,MAAMD,IAAIC,GAAG,CAAC,IAC9E;AAEF,QAAAG,iBAAgBlD,uCAAWA,cAAaoC,MAAMC,QAAQrC,UAAUA,SAAS,IACvEA,UAAUA,UAAUsC,IAAKC,QAAMA,uBAAGY,oBAAmB,CAAC,EAAEV,OAAO,CAACK,GAAGC,MAAMD,IAAIC,GAAG,CAAC,IAAI;AAC7F,QAAMK,oBAAoB,CACpB,aACA,eACA,cACA,iBACA,cACA,cACA,eACA,iBACA,cAAA;AAKN,QAAMC,mBAAmB,CACnB,YACA,cACA,cACA,iBACA,cACA,cACA,eACA,iBACA,cAAA;AAGN,QAAMC,yBAAyB,CAEzB,YACA,cACA,cACA,iBACA,cACA,cACA,iBACA,aACA,cAAA;AAMN,QAAMC,eAAe,CACf,eACA,iBACA,cACA,iBACA,cACA,cACA,eACA,iBACA,cAAA;AAGN,QAAMC,qBAAqB,CACrB,eACA,iBACA,cACA,iBACA,cACA,cACA,gBACC,aACD,cAAA;AAGN,QAAMC,eAAe,CACf,IACA,IACAT,eAAeU,QAAQ,CAAC,GACxBvB,iBAAiBuB,QAAQ,CAAC,GAC1Bd,eAAec,QAAQ,CAAC,GACxBR,cAAcQ,QAAQ,CAAC,CAAA;AAG7B,QAAMC,kBAAkBA,MAAM;AAEpB,QAAA,CAACpC,UAAUE,KAAM;AAErB,UAAMK,gBAAgBC,OAAOR,UAAUE,MAAM,YAAY;AACzD,UAAMO,cAAcT,UAAUG,KAAKK,OAAOR,UAAUG,IAAI,YAAY,IAAII;AAC/DhC,aAAA,cAAcU,SAAS,SAASG,WAAW,aAAagB,YAAY,SAASG,aAAa,OAAOE,WAAW,YAAY7B,OAAO,aAAaF,QAAQ,gBAAgBC,WAAW,SAASE,IAAI,WAAWC,MAAM,EAAE;AAAA,EAE9N;AAEM,QAAAuD,mBAAoBC,WAAiC;AACjD,QAAA,EAACA,+BAAOpC,MAAM;AAELD,iBAAA;AAAA,MACPC,MAAMoC,MAAMpC;AAAAA,MACZC,IAAImC,MAAMnC,MAAM;AAAA;AAAA,IACtB,CAAC;AAAA,EACP;AAGM,SAAAoC,kCAAAA,KAAC,OAAI;AAAA,IAAAC,WAAU;AAAA,IACTC,UAAA,CAACF,kCAAA,KAAA,OAAA;AAAA,MAAIC,WAAU;AAAA,MACTC,UAAA,CAAAF,kCAAA,KAACG;QAAOC,SAAQ;AAAA,QAAQC,MAAK;AAAA,QAAKC,SAASA,MAAMtE,SAAS,8BAA8BiC,OAAO,IAAIX,KAAKF,IAAI,GAAG,YAAY,CAAC,EAAE;AAAA,QACxH8C,UAAA,CAACK,kCAAA,IAAAC,WAAA;AAAA,UAAUP,WAAU;AAAA,QAAe,CAAA,GAAE,sBAAA;AAAA,MAE5C,CAAA,GACCM,kCAAA,IAAA,QAAA;AAAA,QAAKN,WAAU;AAAA,QAAwBC,UAAC;AAAA,MAAA,CAAA,GACxCK,kCAAA,IAAA,QAAA;AAAA,QAAKN,WAAU;AAAA,QAAiBC,UAAK5D;AAAAA,MAAA,CAAA,CAAA;AAAA,IAC5C,CAAA,yCACC,OAAI;AAAA,MAAA2D,WAAU;AAAA,MACTC,UAACF,kCAAA,KAAA,OAAA;AAAA,QAAIC,WAAU;AAAA,QACTC,UAAA,CAAAK,kCAAA,IAAC,OAAI;AAAA,UAAAN,WAAU;AAAA,UACTC,UAAAF,kCAAA,KAACS,SACK;AAAA,YAAAP,UAAA,CAACK,kCAAA,IAAAG,gBAAA;AAAA,cAAeC,SAAO;AAAA,cACjBT,UAAAF,kCAAA,KAACG,QAAA;AAAA,gBACKS,IAAG;AAAA,gBACHR,SAAS;AAAA,gBACTH,WAAWY,GACL,iDACA,CAACpD,UAAUE,QAAQ,uBACzB;AAAA,gBAEAuC,UAAA,CAAAK,kCAAAA,IAACO,UAAa,EAAA,IACbrD,uCAAWE,QACNF,UAAUG,KAEGoC,kCAAAA,KAAAe,kBAAAA,UAAA;AAAA,kBAAAb,UAAA,CAAOjC,OAAAR,UAAUE,MAAM,WAAW,GAAE,OAAIM,OAAOR,UAAUG,IAAI,WAAW,CAAA;AAAA,gBAC/E,CAAA,IAEAK,OAAOR,UAAUE,MAAM,WAAW,IAGxC4C,kCAAA,IAAC;kBAAKL,UAAW;AAAA,gBAAA,CAAA,CAAA;AAAA,cAE7B,CAAA;AAAA,YAEN,CAAA,GACCK,kCAAA,IAAAS,gBAAA;AAAA,cAAef,WAAU;AAAA,cAAagB,OAAM;AAAA,cACvCf,UAAAK,kCAAA,IAACW,YAAA;AAAA,gBACKC,cAAY;AAAA,gBACZC,UAAU3D;AAAAA,gBACV4D,MAAK;AAAA,gBACLC,UAAUxB;AAAAA,cAChB,CAAA;AAAA,YACN,CAAA,CAAA;AAAA,UACN,CAAA;AAAA,QACN,CAAA,GAEAS,kCAAA,IAACJ,QAAA;AAAA,UACKoB,MAAK;AAAA,UACLtB,WAAU;AAAA,UACVK,SAASA,MAAMT,gBAAgB;AAAA,UAEpCK,UAAA;AAAA,QAAA,CAED,CAAA;AAAA,MACN,CAAA;AAAA,IAEN,CAAA,0CACCsB,MAAK;AAAA,MAAAC,OAAO/E;AAAAA,MAAWgF,eAAe5D;AAAAA,MAAiBmC,WAAU;AAAA,MAC5DC,UAAA,CAAAF,kCAAA,KAAC2B,UACM;AAAA,QAAAzB,UAAA,CAAC,CAAA1D,eAAeJ,gBAAgB,gBAAgBA,gBAAgB,mBAAoBmE,kCAAA,IAAAqB,aAAA;AAAA,UAAYH,OAAM;AAAA,UAAavB,UAAW;AAAA,QAAA,CAAA,GAE9H,CAAC1D,eAAeJ,gBAAgB,eAAeA,gBAAgB,mBAAoBmE,kCAAA,IAAAqB,aAAA;AAAA,UAAYH,OAAM;AAAA,UAAYvB,UAAU;AAAA,QAAA,CAAA,GAE1H9D,gBAAgB,kBAAmBmE,kCAAAA,IAACqB,aAAY;AAAA,UAAAH,OAAM;AAAA,UAAevB,UAAa;AAAA,QAAA,CAAA,GACnF,0BAAA;AAAA,MAAwB,CAAA,GAC/BK,kCAAA,IAACsB,aAAY;AAAA,QAAAJ,OAAM;AAAA,QACbvB,UAAAK,kCAAA,IAACuB,iBAAA;AAAA,UACKC,SAASzC;AAAAA,UACT0C,MACM9F,UAAUA;AAAAA,UAEhB+F,WAAYC,SACLlC,kCAAA,KAAA,MAAA;AAAA,YAAgBC,WAAU;AAAA,YACrBC,UAAA,CAAAK,kCAAA,IAAC,MAAG;AAAA,cAAAN,WAAU;AAAA,cAAmCC,UAAAgC,IAAItB;AAAAA,YAAG,CAAA,GACvDL,kCAAA,IAAA,MAAA;AAAA,cAAGN,WAAU;AAAA,cACPC,cAAI5D;AAAAA,YACX,CAAA,GACAiE,kCAAA,IAAC;cAAGN,WAAU;AAAA,cACPC,sCAAKf,aAAaS,QAAQ,OAAM;AAAA,YACvC,CAAA,GACAW,kCAAA,IAAC;cAAGN,WAAU;AAAA,cACPC,sCAAKxB,gBAAgBkB,QAAQ,OAAM;AAAA,YAC1C,CAAA,GACAW,kCAAA,IAAC;cAAGN,WAAU;AAAA,cACPC,sCAAKnB,eAAea,QAAQ,OAAM;AAAA,YACzC,CAAA,GACAW,kCAAA,IAAC;cAAGN,WAAU;AAAA,cACPC,sCAAKb,gBAAgBO,QAAQ,OAAM;AAAA,YAC1C,CAAA,yCACC,MAAG;AAAA,cAAAK,WAAU;AAAA,cACPC,WAAAgC,2BAAKC,kBAAiB;AAAA,YAC7B,CAAA,yCACC,MAAG;AAAA,cAAAlC,WAAU;AAAA,cACPC,WAAAgC,2BAAKE,eAAc;AAAA,YAC1B,CAAA,yCACC,MAAG;AAAA,cAAAnC,WAAU;AAAA,cACPC,WAAAgC,2BAAKG,oBAAmB;AAAA,YAC/B,CAAA,CAAA;AAAA,UAAA,GAzBGH,IAAItB,EA0Bb;AAAA,UAENjB;AAAAA,UACA2C,cAAa;AAAA,QACnB,CAAA;AAAA,MACN,CAAA,GACA/B,kCAAA,IAACsB,aAAY;AAAA,QAAAJ,OAAM;AAAA,QACbvB,UAAAK,kCAAA,IAACuB,iBAAA;AAAA,UACKC,SAASvF,aAAWgD,yBAAuBD;AAAAA,UAC3CyC,MACM9F,UAAUA;AAAAA,UAEhB+F,WAAYC,SACLlC,kCAAA,KAAA,MAAA;AAAA,YAAgBC,WAAU;AAAA,YACrBC,UAAA,CAAAK,kCAAA,IAAC,MAAG;AAAA,cAAAN,WAAU;AAAA,cAAmCC,UAAAgC,IAAItB;AAAAA,YAAG,CAAA,GACvDL,kCAAA,IAAA,MAAA;AAAA,cAAGN,WAAU;AAAA,cAEPC,cAAI5D;AAAAA,YAEX,CAAA,GACAiE,kCAAA,IAAC;cAAGN,WAAU;AAAA,cACPC,sCAAKf,aAAaS,QAAQ,OAAM;AAAA,YACvC,CAAA,GACAW,kCAAA,IAAC;cAAGN,WAAU;AAAA,cACPC,sCAAKxB,gBAAgBkB,QAAQ,OAAM;AAAA,YAC1C,CAAA,GACAW,kCAAA,IAAC;cAAGN,WAAU;AAAA,cACPC,sCAAKnB,eAAea,QAAQ,OAAM;AAAA,YACzC,CAAA,GACAW,kCAAA,IAAC;cAAGN,WAAU;AAAA,cACPC,sCAAKb,gBAAgBO,QAAQ,OAAM;AAAA,YAC1C,CAAA,GACC,CAACpD,cAChD+D,kCAAA,IAAA,MAAA;AAAA,cAAGN,WAAU;AAAA,cACwCC,WAAAgC,2BAAKC,kBAAiB;AAAA,YAC7B,CAAA,yCACC,MAAG;AAAA,cAAAlC,WAAU;AAAA,cACPC,WAAAgC,2BAAKE,eAAc;AAAA,YAC1B,CAAA,yCACC,MAAG;AAAA,cAAAnC,WAAU;AAAA,cACPC,WAAAgC,2BAAKG,oBAAmB;AAAA,aAC/B,GACC7F,cACM+D,kCAAA,IAAA,MAAA;AAAA,cAAGN,WAAU;AAAA,cACPC,sCAAKqC,aAAa3C,QAAQ,OAAM;AAAA,aACvC,GAELpD,cACM+D,kCAAA,IAAA,MAAA;AAAA,cAAGN,WAAU;AAAA,cACPC,sCAAKsC,gBAAgB5C,QAAQ,OAAM;AAAA,YAC1C,CAAA,CAAA;AAAA,UAAA,GArCHsC,IAAItB,EAuCb;AAAA,UAENjB;AAAAA,UACA2C,cAAa;AAAA,QACnB,CAAA;AAAA,MACN,CAAA,GACA/B,kCAAA,IAACsB,aAAY;AAAA,QAAAJ,OAAM;AAAA,QACbvB,UAAAK,kCAAA,IAACuB,iBAAA;AAAA,UACKC,SAAUvF,aAAWkD,qBAAmBD;AAAAA,UACxCuC,MACM9F,UAAUA;AAAAA,UAEhB+F,WAAYC,SACLlC,kCAAA,KAAA,MAAA;AAAA,YAAgBC,WAAU;AAAA,YACrBC,UAAA,CAAAK,kCAAA,IAAC,MAAG;AAAA,cAAAN,WAAU;AAAA,cAAmCC,UAAAgC,IAAItB;AAAAA,YAAG,CAAA,GACvDL,kCAAA,IAAA,MAAA;AAAA,cAAGN,WAAU;AAAA,cAEPC,cAAI5D;AAAAA,YAEX,CAAA,GACAiE,kCAAA,IAAC;cAAGN,WAAU;AAAA,cACPC,sCAAKf,aAAaS,QAAQ,OAAM;AAAA,YACvC,CAAA,GACAW,kCAAA,IAAC;cAAGN,WAAU;AAAA,cACPC,sCAAKxB,gBAAgBkB,QAAQ,OAAM;AAAA,YAC1C,CAAA,GACAW,kCAAA,IAAC;cAAGN,WAAU;AAAA,cACPC,sCAAKnB,eAAea,QAAQ,OAAM;AAAA,YACzC,CAAA,GACAW,kCAAA,IAAC;cAAGN,WAAU;AAAA,cACPC,sCAAKb,gBAAgBO,QAAQ,OAAM;AAAA,aAC1C,GACCpD,cAChD+D,kCAAA,IAAA,MAAA;AAAA,cAAGN,WAAU;AAAA,cACyCC,WAAAgC,2BAAKC,kBAAiB;AAAA,YAC7B,CAAA,yCACC,MAAG;AAAA,cAAAlC,WAAU;AAAA,cACPC,WAAAgC,2BAAKE,eAAc;AAAA,YAC1B,CAAA,GACC,CAAC5F,cAC/C+D,kCAAA,IAAA,MAAA;AAAA,cAAGN,WAAU;AAAA,cACuCC,WAAAgC,2BAAKG,oBAAmB;AAAA,aAC/B,GACC7F,cACM+D,kCAAA,IAAA,MAAA;AAAA,cAAGN,WAAU;AAAA,cACPC,sCAAKqC,aAAa3C,QAAQ,OAAM;AAAA,aACvC,GAELpD,cACM+D,kCAAA,IAAA,MAAA;AAAA,cAAGN,WAAU;AAAA,cACPC,sCAAKsC,gBAAgB5C,QAAQ,OAAM;AAAA,YAC1C,CAAA,CAAA;AAAA,UAAA,GAtCHsC,IAAItB,EAyCb;AAAA,UAENjB;AAAAA,UACA2C,cAAa;AAAA,QACnB,CAAA;AAAA,MACN,CAAA,CAAA;AAAA,IACN,CAAA,yCAqHC,OAAI;AAAA,MAAArC,WAAU;AAAA,MACTC,UAACK,kCAAA,IAAAkC,YAAA;AAAA,QACKvC,iDAACwC,mBACM;AAAA,UAAAxC,UAAA,CAAcrD,cAAA,KACR0D,kCAAAA,IAAAoC,gBAAA;AAAA,YACKzC,UAACK,kCAAA,IAAAqC,oBAAA;AAAA,cAAmBtC,SAASA,MAAMnC,iBAAiBtB,cAAc,CAAC;AAAA,YAAG,CAAA;AAAA,UAC5E,CAAA,yCAEL8F,gBACK;AAAA,YAAAzC,UAAAK,kCAAA,IAACsC,gBAAgB;AAAA,cAAA3C,UAAArD,cAAc;AAAA,YAAE,CAAA;AAAA,UACvC,CAAA,GACA0D,kCAAA,IAACoC,gBACK;AAAA,YAAAzC,UAAAK,kCAAA,IAACuC,gBAAe;AAAA,cAAAxC,SAASA,MAAMnC,iBAAiBtB,cAAc,CAAC;AAAA,YAAG,CAAA;AAAA,UACxE,CAAA,CAAA;AAAA,QACN,CAAA;AAAA,MACN,CAAA;AAAA,IACN,CAAA,CAAA;AAAA,EACN,CAAA;AAGZ;"}