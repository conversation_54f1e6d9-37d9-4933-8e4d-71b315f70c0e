{"version": 3, "file": "home.sellerWiseSales-5h2LupVK.js", "sources": ["../../../app/routes/home.sellerWiseSales.tsx"], "sourcesContent": ["import { ActionFunction, json, LoaderFunction } from \"@remix-run/node\";\r\nimport { Form, useActionData, useFetcher, useLoaderData, useNavigate, useSearchParams } from \"@remix-run/react\";\r\nimport * as React from \"react\";\r\nimport { CalendarIcon } from \"lucide-react\";\r\nimport { Button } from \"~/components/ui/button\";\r\nimport { Input } from \"~/components/ui/input\";\r\nimport {\r\n      Popover,\r\n      PopoverContent,\r\n      PopoverTrigger,\r\n} from \"~/components/ui/popover\";\r\nimport { Calendar } from \"~/components/ui/calendar\";\r\nimport { format, formatDate } from \"date-fns\";\r\nimport {\r\n      Select,\r\n      SelectContent,\r\n      SelectItem,\r\n      SelectTrigger,\r\n      SelectValue,\r\n} from \"~/components/ui/select\";\r\nimport { SalesData, SalesDetails, SellerSalesInfo } from \"~/types/api/businessConsoleService/salesinfo\";\r\nimport { getPrivateSellerSales, getSalesData } from \"~/services/salesinfoDetails\";\r\nimport { withAuth, withResponse } from \"@utils/auth-utils\";\r\nimport { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from \"~/components/ui/tabs\";\r\nimport { useState } from \"react\";\r\nimport { Pagination, PaginationContent, PaginationItem, PaginationLink, PaginationNext, PaginationPrevious } from \"~/components/ui/pagination\";\r\nimport { ResponsiveTable } from \"~/components/ui/responsiveTable\";\r\nimport { cn } from \"~/lib/utils\";\r\nimport { DateRange } from \"react-day-picker\";\r\nexport interface LoaderData {\r\n      salesData: SalesDetails,\r\n      sellerRole: string\r\n}\r\nfunction convertToCSV(data: { salesData: any[] } | undefined | null, headers: string[]): string {\r\n      // Return just headers if data is missing or has no salesData\r\n      if (!data?.salesData?.length) {\r\n            return headers.join(\",\");\r\n      }\r\n\r\n      const headerRow = headers.join(\",\");\r\n      const dataRows = data.salesData.map(row => {\r\n            return [\r\n                  row.id ?? \"\",\r\n                  `\"${row.name ?? \"\"}\"`,\r\n                  row.bookedWeight?.toFixed(1) ?? \"-\",\r\n                  row.deliveredWeight?.toFixed(1) ?? \"-\",\r\n                  row.returnedWeight?.toFixed(1) ?? \"-\",\r\n                  row.cancelledWeight?.toFixed(1) ?? \"-\",\r\n                  row.customerCount ?? \"-\",\r\n                  row.orderCount ?? \"-\",\r\n                  row.activeShopCount ?? \"-\"\r\n            ].join(\",\");\r\n      });\r\n\r\n      return [headerRow, ...dataRows].join(\"\\n\");\r\n}\r\n\r\nexport const loader: LoaderFunction = withAuth(async ({ request, user }) => {\r\n      const url = new URL(request.url);\r\n      const sellerPermission = user.userPermissions?.includes('seller_app.scBasic')\r\n      console.log(user.userPermissions, \"4444444444444444\")\r\n\r\n      console.log(sellerPermission, \"5555555555555555\")\r\n      const fromDate = url.searchParams.get(\"from\") || new Date().toISOString().split(\"T\")[0];\r\n      const toDate = url.searchParams.get(\"to\") || fromDate;\r\n      // const date = url.searchParams.get(\"date\") || new Date().toISOString().split(\"T\")[0];\r\n      const page = Number(url.searchParams.get(\"page\")) || 0;\r\n      const pageSize = Number(url.searchParams.get(\"pageSize\")) || 10;\r\n\r\n      if (sellerPermission) {\r\n            try {\r\n                  const activeTab = url.searchParams.get(\"activeTab\") || \"agentWise\";\r\n                  let response = null;\r\n                  switch (activeTab) {\r\n                        case 'agentWise':\r\n                              response = await getPrivateSellerSales(page, pageSize, \"Agentwise\", fromDate, toDate, request)\r\n                              break;\r\n                        case 'localityWise':\r\n                              response = await getPrivateSellerSales(page, pageSize, \"Localitywise\", fromDate, toDate, request)\r\n                              break;\r\n                        default:\r\n                              console.log(\"No valid activeTab selected\");\r\n                              break;\r\n                  }\r\n                  return withResponse({\r\n                        salesData: response?.data,\r\n                        sellerRole: sellerPermission\r\n                  }, response?.headers)\r\n            } catch (error) {\r\n                  console.error(\"Seller sales error:\", error);\r\n                  throw new Response(\"Failed to fetch seller sales\", { status: 500 });\r\n            }\r\n      }\r\n      else {\r\n\r\n            try {\r\n                  const activeTab = url.searchParams.get(\"activeTab\") || \"sellerWise\";\r\n                  let response = null;\r\n                  switch (activeTab) {\r\n                        case 'sellerWise':\r\n                              response = await getSalesData(0, pageSize, \"Sellerwise\", fromDate, toDate, request)\r\n\r\n                              break;\r\n                        case 'agentWise':\r\n                              response = await getSalesData(page, pageSize, \"Agentwise\", fromDate, toDate, request)\r\n                              break;\r\n                        case 'localityWise':\r\n                              response = await getSalesData(page, pageSize, \"Localitywise\", fromDate, toDate, request)\r\n                              break;\r\n                        default:\r\n                              console.log(\"No valid activeTab selected\");\r\n                              break;\r\n\r\n                  }\r\n                  return withResponse({\r\n                        salesData: response?.data,\r\n                        sellerRole: sellerPermission\r\n                  }, response?.headers)\r\n            } catch (error) {\r\n                  console.error(\"Seller sales error:\", error);\r\n                  throw new Response(\"Failed to fetch seller sales\", { status: 500 });\r\n            }\r\n\r\n      }\r\n});\r\n\r\nexport const action: ActionFunction = withAuth(async ({ request, user }) => {\r\n      const formData = await request.formData();\r\n      const activeTab = formData.get(\"activeTab\") as string;\r\n      const fromDate = formData.get(\"from\") as string;\r\n      const toDate = formData.get(\"to\") as string;\r\n      const sellerPermission = user.userPermissions?.includes('seller_app.scBasic')\r\n\r\n      if (sellerPermission) {\r\n            try {\r\n                  let response = null;\r\n                  switch (activeTab) {\r\n                        case 'agentWise':\r\n                              response = await getPrivateSellerSales(0, 1000, \"Agentwise\", fromDate, toDate, request)\r\n                              break;\r\n                        case 'localityWise':\r\n                              response = await getPrivateSellerSales(0, 1000, \"Localitywise\", fromDate, toDate, request)\r\n                              break;\r\n                        default:\r\n                              console.log(\"No valid activeTab selected\");\r\n                              break;\r\n                  }\r\n                  return withResponse({\r\n                        salesData: response?.data,\r\n                        sellerRole: sellerPermission\r\n                  }, response?.headers)\r\n            } catch (error) {\r\n                  console.error(\"CSV export error:\", error);\r\n                  throw new Response(\"Failed to generate CSV\", { status: 500 });\r\n            }\r\n      }\r\n      else {\r\n            try {\r\n                  let response;\r\n                  switch (activeTab) {\r\n                        case 'sellerWise':\r\n                              response = await getSalesData(0, 1000, \"Sellerwise\", fromDate, toDate, request);\r\n                              break;\r\n                        case 'agentWise':\r\n                              response = await getSalesData(0, 1000, \"Agentwise\", fromDate, toDate, request);\r\n                              break;\r\n                        case 'localityWise':\r\n                              response = await getSalesData(0, 1000, \"Localitywise\", fromDate, toDate, request);\r\n                              break;\r\n                        default:\r\n                              throw new Error(\"Invalid tab selected\");\r\n                  }\r\n\r\n                  return json({\r\n                        salesData: response?.data,\r\n                        sellerRole: sellerPermission\r\n\r\n                  });\r\n            } catch (error) {\r\n                  console.error(\"CSV export error:\", error);\r\n                  throw new Response(\"Failed to generate CSV\", { status: 500 });\r\n            }\r\n      }\r\n});\r\nexport default function SellerWiseSales() {\r\n\r\n      const navigate = useNavigate();\r\n      const { salesData, sellerRole } = useLoaderData<LoaderData>()\r\n      const [searchParams] = useSearchParams();\r\n      const startDate = searchParams.get(\"from\")\r\n      const endDate = searchParams.get(\"to\")\r\n\r\n\r\n      const [activeTab, setActiveTab] = React.useState(searchParams.get(\"activeTab\") || (sellerRole ? \"agentWise\" : \"sellerWise\")); const [currentPage, setCurrentPage] = useState(0)\r\n      const [searchTerm, setSearchTerm] = useState('')\r\n\r\n      const [dateRange, setDateRange] = React.useState<DateRange>({\r\n            from: startDate ? new Date(startDate) : new Date(),\r\n            to: endDate ? new Date(endDate) : new Date(),\r\n      });\r\n\r\n      const itemsPerPage = 20;\r\n      const handleTabChange = (newTab: string) => {\r\n            setActiveTab(newTab);\r\n\r\n            if (!dateRange.from) return; // Ensure 'from' date exists\r\n\r\n            const formattedFrom = format(dateRange.from, \"yyyy-MM-dd\");\r\n            const formattedTo = dateRange.to ? format(dateRange.to, \"yyyy-MM-dd\") : formattedFrom;\r\n\r\n            navigate(`?activeTab=${newTab}&page=${0}&pageSize=${itemsPerPage}&from=${formattedFrom}&to=${formattedTo}`);\r\n      };\r\n\r\n      const handlePageChange = (newPage: number) => {\r\n            if (!dateRange.from) return; // Ensure 'from' date exists\r\n\r\n            const formattedFrom = format(dateRange.from, \"yyyy-MM-dd\");\r\n            const formattedTo = dateRange.to ? format(dateRange.to, \"yyyy-MM-dd\") : formattedFrom;\r\n            setCurrentPage(newPage)\r\n            navigate(`?activeTab=${activeTab}&page=${newPage}&pageSize=${itemsPerPage}&from=${formattedFrom}&to=${formattedTo}`);\r\n      };\r\n\r\n      const handleViewSales = () => {\r\n            if (!dateRange.from) return; // Ensure 'from' date exists\r\n\r\n            const formattedFrom = format(dateRange.from, \"yyyy-MM-dd\");\r\n            const formattedTo = dateRange.to ? format(dateRange.to, \"yyyy-MM-dd\") : formattedFrom; // Default 'to' as 'from'\r\n\r\n            navigate(`?activeTab=${activeTab}&page=${currentPage}&pageSize=${itemsPerPage}&from=${formattedFrom}&to=${formattedTo}`);\r\n      };\r\n\r\n      // const handleSearch = (Val: string) => {\r\n      //       setSearchTerm(Val)\r\n      //       navigate(`?activeTab=${activeTab}&page=${currentPage}&searchTerm=${searchTerm}&pageSize=${itemsPerPage}`)\r\n      // }\r\n\r\n      const totalDeliveredWt = salesData?.salesData && Array.isArray(salesData.salesData)\r\n            ? salesData.salesData.map((x) => x?.deliveredWeight || 0).reduce((acc, wet) => acc + wet, 0)\r\n            : 0;\r\n\r\n      const totalReturnsWt = salesData?.salesData && Array.isArray(salesData.salesData)\r\n            ? salesData.salesData.map((x) => x?.returnedWeight || 0).reduce((a, b) => a + b, 0)\r\n            : 0;\r\n\r\n      const totalBookingWt = salesData?.salesData && Array.isArray(salesData.salesData)\r\n            ? salesData.salesData.map((x) => x?.bookedWeight || 0).reduce((a, b) => a + b, 0)\r\n            : 0;\r\n\r\n      const totalCancelWt = salesData?.salesData && Array.isArray(salesData.salesData)\r\n            ? salesData.salesData.map((x) => x?.cancelledWeight || 0).reduce((a, b) => a + b, 0)\r\n            : 0;\r\n\r\n      const sellerWiseHeaders = [\r\n            \"Seller ID\",\r\n            \"Seller Name\",\r\n            \"Booked Qty\",\r\n            \"Delivered Qty\",\r\n            \"Return Qty\",\r\n            \"Cancel Qty\",\r\n            \"Total Shops\",\r\n            \"Ordered Shops\",\r\n            \"Active Shops\"\r\n\r\n\r\n\r\n      ];\r\n      const sellerAgentWiseHeaders = [\r\n\r\n            \"Agent ID\",\r\n            \"Agent Name\",\r\n            \"Booked Qty\",\r\n            \"Delivered Qty\",\r\n            \"Return Qty\",\r\n            \"Cancel Qty\",\r\n            \"Ordered Shops\",\r\n            \"BookedAmt\",\r\n            \"DeliveredAmt\",\r\n           \r\n\r\n      ]\r\n      const sellerLocalityWise = [\r\n            \"Locality ID\",\r\n            \"Locality Name\",\r\n            \"Booked Qty\",\r\n            \"Delivered Qty\",\r\n            \"Return Qty\",\r\n            \"Cancel Qty\",\r\n            \"Active Shops\",\r\n             \"BookedAmt\",\r\n            \"DeliveredAmt\"\r\n\r\n      ];\r\n      const AgentWiseHeaders = [\r\n            \"Agent ID\",\r\n            \"Agent Name\",\r\n            \"Booked Qty\",\r\n            \"Delivered Qty\",\r\n            \"Return Qty\",\r\n            \"Cancel Qty\",\r\n            \"Total Shops\",\r\n            \"Ordered Shops\",\r\n            \"Active Shops\"\r\n\r\n      ];\r\n      const LocalityWise = [\r\n            \"Locality ID\",\r\n            \"Locality Name\",\r\n            \"Booked Qty\",\r\n            \"Delivered Qty\",\r\n            \"Return Qty\",\r\n            \"Cancel Qty\",\r\n            \"Total Shops\",\r\n            \"Ordered Shops\",\r\n            \"Active Shops\"\r\n\r\n      ];\r\n\r\n      const footerTotals = [\r\n            \"\",\r\n            \"\",\r\n            totalBookingWt.toFixed(2),\r\n            totalDeliveredWt.toFixed(2),\r\n            totalReturnsWt.toFixed(2),\r\n            totalCancelWt.toFixed(2)\r\n\r\n      ];\r\n      const handleDateChange = (range: DateRange | undefined) => {\r\n            if (!range?.from) return; // Ensure 'from' is never undefined\r\n\r\n            setDateRange({\r\n                  from: range.from,\r\n                  to: range.to || undefined, // If 'to' is not selected, keep it undefined\r\n            });\r\n      };\r\n\r\n      const actionData = useActionData<typeof action>();\r\n\r\n\r\n      const fetcher = useFetcher<{ salesData?: any[] }>();\r\n      const [isExporting, setIsExporting] = React.useState(false); // Track when export is triggered\r\n\r\n      const handleCSVDownload = () => {\r\n            if (!dateRange.from) return;\r\n\r\n            setIsExporting(true);  // Mark as exporting\r\n\r\n            const formattedFrom = format(dateRange.from, \"yyyy-MM-dd\");\r\n            const formattedTo = dateRange.to ? format(dateRange.to, \"yyyy-MM-dd\") : formattedFrom;\r\n\r\n            fetcher.submit(\r\n                  {\r\n                        activeTab,\r\n                        from: formattedFrom,\r\n                        to: formattedTo,\r\n                  },\r\n                  { method: \"post\" }\r\n            );\r\n      };\r\n\r\n      React.useEffect(() => {\r\n            if (isExporting && fetcher.data?.salesData) {\r\n                  const headers = activeTab === \"sellerWise\" ? sellerWiseHeaders :\r\n                        activeTab === \"agentWise\" ? AgentWiseHeaders :\r\n                              LocalityWise;\r\n\r\n                  const csvContent = convertToCSV(fetcher?.data?.salesData, headers);\r\n                  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });\r\n                  const link = document.createElement('a');\r\n                  const url = URL.createObjectURL(blob);\r\n\r\n                  link.setAttribute('href', url);\r\n                  link.setAttribute('download', `${activeTab}_sales_${startDate}_to_${endDate}.csv`);\r\n                  document.body.appendChild(link);\r\n                  link.click();\r\n                  document.body.removeChild(link);\r\n\r\n                  setIsExporting(false);  // Reset export state after download\r\n            }\r\n      }, [fetcher.data, isExporting, activeTab, startDate, endDate]);\r\n\r\n      const [selectedAgentId, setSelectedAgentId] = useState(0)\r\n      return (\r\n            <div className=\"container mx-auto w-full p-6\">\r\n                  <div className=\"items-center mb-6 flex justify-between\">\r\n                        <h1 className=\"text-2xl font-bold\">Sales Report</h1>\r\n                        <Button\r\n                              variant=\"outline\"\r\n                              onClick={handleCSVDownload}\r\n                              disabled={fetcher.state !== \"idle\"}\r\n                        >\r\n                              {fetcher.state === \"submitting\" ? \"Exporting...\" : \"Export to CSV\"}\r\n                        </Button>\r\n                  </div>\r\n                  <div className=\"flex flex-col space-y-5 md:flex-row md:space-x-5 md:space-y-0 my-5\">\r\n                        <div className=\"grid grid-cols-1 gap-3 md:flex md:items-center md:space-x-2\">\r\n                              <div className=\"w-full md:w-auto\">\r\n                                    <Popover>\r\n                                          <PopoverTrigger asChild>\r\n                                                <Button\r\n                                                      id=\"date\"\r\n                                                      variant={\"outline\"}\r\n                                                      className={cn(\r\n                                                            \"w-[300px] justify-start text-left font-normal\",\r\n                                                            !dateRange.from && \"text-muted-foreground\"\r\n                                                      )}\r\n                                                >\r\n                                                      <CalendarIcon />\r\n                                                      {dateRange?.from ? (\r\n                                                            dateRange.to ? (\r\n                                                                  <>\r\n                                                                        {format(dateRange.from, \"LLL dd, y\")} - {format(dateRange.to, \"LLL dd, y\")}\r\n                                                                  </>\r\n                                                            ) : (\r\n                                                                  format(dateRange.from, \"LLL dd, y\")\r\n                                                            )\r\n                                                      ) : (\r\n                                                            <span>Pick a date</span>\r\n                                                      )}\r\n                                                </Button>\r\n\r\n                                          </PopoverTrigger>\r\n                                          <PopoverContent className=\"w-auto p-0\" align=\"start\">\r\n                                                <Calendar\r\n                                                      initialFocus\r\n                                                      selected={dateRange}\r\n                                                      mode=\"range\" // Enable range selection\r\n                                                      onSelect={handleDateChange}\r\n                                                />\r\n                                          </PopoverContent>\r\n                                    </Popover>\r\n                              </div>\r\n\r\n                              <Button\r\n                                    type=\"submit\"\r\n                                    className=\"w-full md:w-auto md:rounded-full\"\r\n                                    onClick={() => handleViewSales()}\r\n\r\n                              >\r\n                                    View Report\r\n                              </Button>\r\n                        </div>\r\n\r\n                  </div>\r\n\r\n                  <Tabs value={activeTab} onValueChange={handleTabChange} className=\"my-5\">\r\n                        <TabsList>\r\n                              {!sellerRole && <TabsTrigger value=\"sellerWise\">Seller Wise</TabsTrigger>\r\n                              }                              <TabsTrigger value=\"agentWise\">Agent Wise</TabsTrigger>\r\n                              <TabsTrigger value=\"localityWise\">Locality Wise</TabsTrigger>\r\n                        </TabsList>\r\n                        <TabsContent value=\"sellerWise\">\r\n                              <ResponsiveTable\r\n                                    headers={sellerWiseHeaders}\r\n                                    data={salesData.salesData\r\n                                    }\r\n                                    renderRow={(row) => (\r\n                                          <tr key={row.id} className=\"border-b\">\r\n                                                <td className=\"py-2 px-3 font-medium text-left\">{row.id}</td>\r\n                                                <td className=\"py-2 px-3 text-blue-600 underline cursor-pointer text-left\">\r\n                                                      <span\r\n                                                            onClick={() => {\r\n                                                                  if (!dateRange.from) return; // Ensure 'from' date exists\r\n\r\n                                                                  const formattedFrom = format(dateRange.from, \"yyyy-MM-dd\");\r\n                                                                  const formattedTo = dateRange.to ? format(dateRange.to, \"yyyy-MM-dd\") : formattedFrom;\r\n                                                                  navigate(\r\n                                                                        `/home/<USER>\"agentWise\"}&agentId=undefined&areaId=undefined`\r\n                                                                  )\r\n                                                            }}\r\n                                                      >\r\n                                                            {row.name}\r\n                                                      </span>\r\n                                                </td>\r\n                                                <td className=\"py-2 px-3 text-right\">\r\n                                                      {row?.bookedWeight.toFixed(1) || \"-\"}\r\n                                                </td>\r\n                                                <td className=\"py-2 px-3 text-right\">\r\n                                                      {row?.deliveredWeight.toFixed(1) || \"-\"}\r\n                                                </td>\r\n                                                <td className=\"py-2 px-3 text-right\">\r\n                                                      {row?.returnedWeight.toFixed(1) || \"-\"}\r\n                                                </td>\r\n                                                <td className=\"py-2 px-3 text-right\">\r\n                                                      {row?.cancelledWeight.toFixed(1) || \"-\"}\r\n                                                </td>\r\n                                                <td className=\"py-2 px-3 text-right\">\r\n                                                      {row?.customerCount || \"-\"}\r\n                                                </td>\r\n                                                <td className=\"py-2 px-3 text-right\">\r\n                                                      {row?.orderCount || \"-\"}\r\n                                                </td>\r\n                                                <td className=\"py-2 px-3 text-right\">\r\n                                                      {row?.activeShopCount || \"-\"}\r\n                                                </td>\r\n                                          </tr>\r\n                                    )}\r\n                                    footerTotals={footerTotals}\r\n                                    emptyMessage=\"No data available for the selected filters.\"\r\n                              />\r\n                        </TabsContent>\r\n                        <TabsContent value=\"agentWise\">\r\n                              <ResponsiveTable\r\n                                headers={sellerRole?sellerAgentWiseHeaders:AgentWiseHeaders}\r\n\r\n                                    data={\r\n                                          salesData.salesData\r\n                                    }\r\n                                    renderRow={(row) => (\r\n                                          <tr key={row.id} className=\"border-b\">\r\n                                                <td className=\"py-2 px-3 font-medium text-left\">{row.id}</td>\r\n                                                <td className=\"py-2 px-3 text-blue-600 underline cursor-pointer text-left\">\r\n                                                    { !sellerRole? <span\r\n                                                            onClick={() => {\r\n                                                                  if (!dateRange.from) return; // Ensure 'from' date exists\r\n\r\n                                                                  const formattedFrom = format(dateRange.from, \"yyyy-MM-dd\");\r\n                                                                  const formattedTo = dateRange.to ? format(dateRange.to, \"yyyy-MM-dd\") : formattedFrom;\r\n\r\n                                                                  setSelectedAgentId(row.id)\r\n                                                                  navigate(\r\n                                                                        `/home/<USER>\"agentWise\" : \"sellerWise\"}&sellerId=undefined&areaId=undefined`\r\n                                                                  )\r\n                                                            }}\r\n                                                      >\r\n                                                            {row.name}\r\n                                                      </span>:\r\n                                                      \r\n                                                      <span>\r\n                                                            {row.name}\r\n                                                      </span>\r\n                                    }\r\n                                                </td>\r\n                                                <td className=\"py-2 px-3 text-right\">\r\n                                                      {row?.bookedWeight.toFixed(1) || \"-\"}\r\n                                                </td>\r\n                                                <td className=\"py-2 px-3 text-right\">\r\n                                                      {row?.deliveredWeight.toFixed(1) || \"-\"}\r\n                                                </td>\r\n                                                <td className=\"py-2 px-3 text-right\">\r\n                                                      {row?.returnedWeight.toFixed(1) || \"-\"}\r\n                                                </td>\r\n                                                <td className=\"py-2 px-3 text-right\">\r\n                                                      {row?.cancelledWeight.toFixed(1) || \"-\"}\r\n                                                </td>\r\n                                                {!sellerRole && (\r\n <td className=\"py-2 px-3 text-right\">\r\n                                                      {row?.customerCount || \"-\"}\r\n                                                </td>)}\r\n                                                <td className=\"py-2 px-3 text-right\">\r\n                                                      {row?.orderCount || \"-\"}\r\n                                                </td>\r\n                                                {!sellerRole && (\r\n                                                <td className=\"py-2 px-3 text-right\">\r\n                                                      {row?.activeShopCount || \"-\"}\r\n                                                </td>)}\r\n                                                {sellerRole && (\r\n                                                      <td className=\"py-2 px-3 text-right\">\r\n                                                            {row?.bookedAmount.toFixed(2) || \"-\"}\r\n                                                      </td>\r\n                                                )}\r\n                                                {sellerRole && (\r\n                                                      <td className=\"py-2 px-3 text-right\">\r\n                                                            {row?.deliveredAmount.toFixed(2) || \"-\"}\r\n                                                      </td>\r\n                                                )}\r\n                                          </tr>\r\n                                    )}\r\n                                    footerTotals={footerTotals}\r\n                                    emptyMessage=\"No data available for the selected filters.\"\r\n                              />\r\n                        </TabsContent>\r\n                        <TabsContent value=\"localityWise\">\r\n                              <ResponsiveTable\r\n                                    headers={ sellerRole?sellerLocalityWise:LocalityWise}\r\n                                    data={\r\n                                          salesData.salesData\r\n                                    }\r\n                                    renderRow={(row) => (\r\n                                          <tr key={row.id} className=\"border-b\">\r\n                                                <td className=\"py-2 px-3 font-medium text-left\">{row.id}</td>\r\n                                                <td className=\"py-2 px-3 text-blue-600 underline cursor-pointer text-left\">\r\n                                                { !sellerRole? <span\r\n                                                            onClick={() => {\r\n                                                                  if (!dateRange.from) return; // Ensure 'from' date exists\r\n\r\n                                                                  const formattedFrom = format(dateRange.from, \"yyyy-MM-dd\");\r\n                                                                  const formattedTo = dateRange.to ? format(dateRange.to, \"yyyy-MM-dd\") : formattedFrom;\r\n                                                                  navigate(\r\n                                                                        `/home/<USER>\"localityWise\"}&sellerId=undefined&agentId=${sellerRole ? selectedAgentId : 'undefined'}`\r\n                                                                  )\r\n                                                            }}\r\n                                                      >\r\n                                                            {row.name}\r\n                                                      </span>:\r\n                                                      <span>\r\n                                                            {row.name}\r\n                                                      </span>\r\n                                    }\r\n\r\n                                                </td>\r\n                                                <td className=\"py-2 px-3 text-right\">\r\n                                                      {row?.bookedWeight.toFixed(1) || \"-\"}\r\n                                                </td>\r\n                                                <td className=\"py-2 px-3 text-right\">\r\n                                                      {row?.deliveredWeight.toFixed(1) || \"-\"}\r\n                                                </td>\r\n                                                <td className=\"py-2 px-3 text-right\">\r\n                                                      {row?.returnedWeight.toFixed(1) || \"-\"}\r\n                                                </td>\r\n                                                <td className=\"py-2 px-3 text-right\">\r\n                                                      {row?.cancelledWeight.toFixed(1) || \"-\"}\r\n                                                </td>\r\n                                         {!sellerRole && (\r\n<td className=\"py-2 px-3 text-right\">\r\n                                                      {row?.customerCount || \"-\"}\r\n                                                </td>)}\r\n                                                <td className=\"py-2 px-3 text-right\">\r\n                                                      {row?.orderCount || \"-\"}\r\n                                                </td>\r\n                                                {!sellerRole && (\r\n  <td className=\"py-2 px-3 text-right\">\r\n                                                      {row?.activeShopCount || \"-\"}\r\n                                                </td>)}\r\n                                                {sellerRole && (\r\n                                                      <td className=\"py-2 px-3 text-right\">\r\n                                                            {row?.bookedAmount.toFixed(2) || \"-\"}\r\n                                                      </td>\r\n                                                )}\r\n                                                {sellerRole && (\r\n                                                      <td className=\"py-2 px-3 text-right\">\r\n                                                            {row?.deliveredAmount.toFixed(2) || \"-\"}\r\n                                                      </td>\r\n                                                )}\r\n                                                 \r\n                                          </tr>\r\n                                    )}\r\n                                    footerTotals={footerTotals}\r\n                                    emptyMessage=\"No data available for the selected filters.\"\r\n                              />\r\n                        </TabsContent>\r\n                  </Tabs>\r\n\r\n                  <div className=\"flex justify-between items-center mt-6 overflow-hidden \">\r\n                        <Pagination>\r\n                              <PaginationContent>\r\n                                    {currentPage > 0 && (\r\n                                          <PaginationItem>\r\n                                                <PaginationPrevious onClick={() => handlePageChange(currentPage - 1)} />\r\n                                          </PaginationItem>\r\n                                    )}\r\n                                    <PaginationItem>\r\n                                          <PaginationLink>{currentPage + 1}</PaginationLink>\r\n                                    </PaginationItem>\r\n                                    <PaginationItem>\r\n                                          <PaginationNext onClick={() => handlePageChange(currentPage + 1)} />\r\n                                    </PaginationItem>\r\n                              </PaginationContent>\r\n                        </Pagination>\r\n                  </div>\r\n            </div>\r\n\r\n      );\r\n}\r\n"], "names": ["convertToCSV", "data", "headers", "salesData", "length", "join", "headerRow", "dataRows", "map", "row", "id", "name", "bookedWeight", "toFixed", "deliveredWeight", "returnedWeight", "cancelledWeight", "customerCount", "orderCount", "activeShopCount", "SellerWiseSales", "navigate", "useNavigate", "sellerRole", "useLoaderData", "searchParams", "useSearchParams", "startDate", "get", "endDate", "activeTab", "setActiveTab", "React", "currentPage", "setCurrentPage", "useState", "searchTerm", "setSearchTerm", "date<PERSON><PERSON><PERSON>", "setDateRange", "from", "Date", "to", "itemsPerPage", "handleTabChange", "newTab", "formattedFrom", "format", "formattedTo", "handlePageChange", "newPage", "handleViewSales", "totalDeliveredWt", "Array", "isArray", "x", "reduce", "acc", "wet", "totalReturnsWt", "a", "b", "totalBookingWt", "totalCancelWt", "sellerWiseHeaders", "sellerAgentWiseHeaders", "sellerLocalityWise", "AgentWiseHeaders", "LocalityWise", "footerTotals", "handleDateChange", "range", "useActionData", "fetcher", "useFetcher", "isExporting", "setIsExporting", "handleCSVDownload", "submit", "method", "csv<PERSON><PERSON>nt", "blob", "Blob", "type", "link", "document", "createElement", "url", "URL", "createObjectURL", "setAttribute", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "selectedAgentId", "setSelectedAgentId", "jsxs", "className", "children", "jsx", "<PERSON><PERSON>", "variant", "onClick", "disabled", "state", "Popover", "PopoverTrigger", "<PERSON><PERSON><PERSON><PERSON>", "cn", "CalendarIcon", "Fragment", "PopoverC<PERSON>nt", "align", "Calendar", "initialFocus", "selected", "mode", "onSelect", "Tabs", "value", "onValueChange", "TabsList", "TabsTrigger", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ResponsiveTable", "renderRow", "toString", "emptyMessage", "bookedAmount", "deliveredAmount", "Pagination", "Pa<PERSON><PERSON><PERSON><PERSON><PERSON>", "PaginationItem", "PaginationPrevious", "PaginationLink", "PaginationNext"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiCA,SAASA,aAAaC,MAA+CC,SAA2B;;AAEtF,MAAA,GAACD,kCAAME,cAANF,mBAAiBG,SAAQ;AACjB,WAAAF,QAAQG,KAAK,GAAG;AAAA,EAC7B;AAEM,QAAAC,YAAYJ,QAAQG,KAAK,GAAG;AAClC,QAAME,WAAWN,KAAKE,UAAUK,IAAWC,SAAA;;AAC9B,WAAA,CACDA,IAAIC,MAAM,IACV,IAAID,IAAIE,QAAQ,EAAE,OAClBF,MAAAA,IAAIG,iBAAJH,gBAAAA,IAAkBI,QAAQ,OAAM,OAChCJ,SAAIK,oBAAJL,mBAAqBI,QAAQ,OAAM,OACnCJ,SAAIM,mBAAJN,mBAAoBI,QAAQ,OAAM,OAClCJ,SAAIO,oBAAJP,mBAAqBI,QAAQ,OAAM,KACnCJ,IAAIQ,iBAAiB,KACrBR,IAAIS,cAAc,KAClBT,IAAIU,mBAAmB,GAAA,EAC3Bd,KAAK,GAAG;AAAA,EAChB,CAAC;AAED,SAAO,CAACC,WAAW,GAAGC,QAAQ,EAAEF,KAAK,IAAI;AAC/C;AAiIA,SAAwBe,kBAAkB;AAEpC,QAAMC,WAAWC,YAAY;AAC7B,QAAM;AAAA,IAAEnB;AAAAA,IAAWoB;AAAAA,EAAW,IAAIC,cAA0B;AACtD,QAAA,CAACC,YAAY,IAAIC,gBAAgB;AACjC,QAAAC,YAAYF,aAAaG,IAAI,MAAM;AACnC,QAAAC,UAAUJ,aAAaG,IAAI,IAAI;AAGrC,QAAM,CAACE,WAAWC,YAAY,IAAIC,aAAM,SAASP,aAAaG,IAAI,WAAW,MAAML,aAAa,cAAc,aAAa;AAAG,QAAM,CAACU,aAAaC,cAAc,IAAIC,aAAAA,SAAS,CAAC;AAC9K,QAAM,CAACC,YAAYC,aAAa,IAAIF,aAAAA,SAAS,EAAE;AAE/C,QAAM,CAACG,WAAWC,YAAY,IAAIP,sBAA0B;AAAA,IACtDQ,MAAMb,YAAY,IAAIc,KAAKd,SAAS,wBAAQc,KAAK;AAAA,IACjDC,IAAIb,UAAU,IAAIY,KAAKZ,OAAO,wBAAQY,KAAK;AAAA,EACjD,CAAC;AAED,QAAME,eAAe;AACf,QAAAC,kBAAmBC,YAAmB;AACtCd,iBAAac,MAAM;AAEf,QAAA,CAACP,UAAUE,KAAM;AAErB,UAAMM,gBAAgBC,OAAOT,UAAUE,MAAM,YAAY;AACzD,UAAMQ,cAAcV,UAAUI,KAAKK,OAAOT,UAAUI,IAAI,YAAY,IAAII;AAE/DzB,aAAA,cAAcwB,MAAM,SAAS,CAAC,aAAaF,YAAY,SAASG,aAAa,OAAOE,WAAW,EAAE;AAAA,EAChH;AAEM,QAAAC,mBAAoBC,aAAoB;AACpC,QAAA,CAACZ,UAAUE,KAAM;AAErB,UAAMM,gBAAgBC,OAAOT,UAAUE,MAAM,YAAY;AACzD,UAAMQ,cAAcV,UAAUI,KAAKK,OAAOT,UAAUI,IAAI,YAAY,IAAII;AACxEZ,mBAAegB,OAAO;AACb7B,aAAA,cAAcS,SAAS,SAASoB,OAAO,aAAaP,YAAY,SAASG,aAAa,OAAOE,WAAW,EAAE;AAAA,EACzH;AAEA,QAAMG,kBAAkBA,MAAM;AACpB,QAAA,CAACb,UAAUE,KAAM;AAErB,UAAMM,gBAAgBC,OAAOT,UAAUE,MAAM,YAAY;AACzD,UAAMQ,cAAcV,UAAUI,KAAKK,OAAOT,UAAUI,IAAI,YAAY,IAAII;AAE/DzB,aAAA,cAAcS,SAAS,SAASG,WAAW,aAAaU,YAAY,SAASG,aAAa,OAAOE,WAAW,EAAE;AAAA,EAC7H;AAOM,QAAAI,oBAAmBjD,uCAAWA,cAAakD,MAAMC,QAAQnD,UAAUA,SAAS,IAC1EA,UAAUA,UAAUK,IAAK+C,QAAMA,uBAAGzC,oBAAmB,CAAC,EAAE0C,OAAO,CAACC,KAAKC,QAAQD,MAAMC,KAAK,CAAC,IACzF;AAEF,QAAAC,kBAAiBxD,uCAAWA,cAAakD,MAAMC,QAAQnD,UAAUA,SAAS,IACxEA,UAAUA,UAAUK,IAAK+C,QAAMA,uBAAGxC,mBAAkB,CAAC,EAAEyC,OAAO,CAACI,GAAGC,MAAMD,IAAIC,GAAG,CAAC,IAChF;AAEF,QAAAC,kBAAiB3D,uCAAWA,cAAakD,MAAMC,QAAQnD,UAAUA,SAAS,IACxEA,UAAUA,UAAUK,IAAK+C,QAAMA,uBAAG3C,iBAAgB,CAAC,EAAE4C,OAAO,CAACI,GAAGC,MAAMD,IAAIC,GAAG,CAAC,IAC9E;AAEF,QAAAE,iBAAgB5D,uCAAWA,cAAakD,MAAMC,QAAQnD,UAAUA,SAAS,IACvEA,UAAUA,UAAUK,IAAK+C,QAAMA,uBAAGvC,oBAAmB,CAAC,EAAEwC,OAAO,CAACI,GAAGC,MAAMD,IAAIC,GAAG,CAAC,IACjF;AAER,QAAMG,oBAAoB,CACpB,aACA,eACA,cACA,iBACA,cACA,cACA,eACA,iBACA,cAAA;AAKN,QAAMC,yBAAyB,CAEzB,YACA,cACA,cACA,iBACA,cACA,cACA,iBACA,aACA,cAAA;AAIN,QAAMC,qBAAqB,CACrB,eACA,iBACA,cACA,iBACA,cACA,cACA,gBACC,aACD,cAAA;AAGN,QAAMC,mBAAmB,CACnB,YACA,cACA,cACA,iBACA,cACA,cACA,eACA,iBACA,cAAA;AAGN,QAAMC,eAAe,CACf,eACA,iBACA,cACA,iBACA,cACA,cACA,eACA,iBACA,cAAA;AAIN,QAAMC,eAAe,CACf,IACA,IACAP,eAAejD,QAAQ,CAAC,GACxBuC,iBAAiBvC,QAAQ,CAAC,GAC1B8C,eAAe9C,QAAQ,CAAC,GACxBkD,cAAclD,QAAQ,CAAC,CAAA;AAGvB,QAAAyD,mBAAoBC,WAAiC;AACjD,QAAA,EAACA,+BAAO/B,MAAM;AAELD,iBAAA;AAAA,MACPC,MAAM+B,MAAM/B;AAAAA,MACZE,IAAI6B,MAAM7B,MAAM;AAAA;AAAA,IACtB,CAAC;AAAA,EACP;AAEmB8B,gBAA6B;AAGhD,QAAMC,UAAUC,WAAkC;AAClD,QAAM,CAACC,aAAaC,cAAc,IAAI5C,aAAAA,SAAe,KAAK;AAE1D,QAAM6C,oBAAoBA,MAAM;AACtB,QAAA,CAACvC,UAAUE,KAAM;AAErBoC,mBAAe,IAAI;AAEnB,UAAM9B,gBAAgBC,OAAOT,UAAUE,MAAM,YAAY;AACzD,UAAMQ,cAAcV,UAAUI,KAAKK,OAAOT,UAAUI,IAAI,YAAY,IAAII;AAEhE2B,YAAAK,OACF;AAAA,MACMhD;AAAAA,MACAU,MAAMM;AAAAA,MACNJ,IAAIM;AAAAA,IACV,GACA;AAAA,MAAE+B,QAAQ;AAAA,IAAO,CACvB;AAAA,EACN;AAEA/C,eAAAA,UAAgB,MAAM;;AACZ,QAAA2C,iBAAeF,aAAQxE,SAARwE,mBAActE,YAAW;AACtC,YAAMD,UAAU4B,cAAc,eAAekC,oBACvClC,cAAc,cAAcqC,mBACtBC;AAEZ,YAAMY,aAAahF,cAAayE,wCAASxE,SAATwE,mBAAetE,WAAWD,OAAO;AAC3D,YAAA+E,OAAO,IAAIC,KAAK,CAACF,UAAU,GAAG;AAAA,QAAEG,MAAM;AAAA,MAA0B,CAAC;AACjE,YAAAC,OAAOC,SAASC,cAAc,GAAG;AACjC,YAAAC,MAAMC,IAAIC,gBAAgBR,IAAI;AAE/BG,WAAAM,aAAa,QAAQH,GAAG;AACxBH,WAAAM,aAAa,YAAY,GAAG5D,SAAS,UAAUH,SAAS,OAAOE,OAAO,MAAM;AACxEwD,eAAAM,KAAKC,YAAYR,IAAI;AAC9BA,WAAKS,MAAM;AACFR,eAAAM,KAAKG,YAAYV,IAAI;AAE9BR,qBAAe,KAAK;AAAA,IAC1B;AAAA,EACN,GAAG,CAACH,QAAQxE,MAAM0E,aAAa7C,WAAWH,WAAWE,OAAO,CAAC;AAE7D,QAAM,CAACkE,iBAAiBC,kBAAkB,IAAI7D,aAAAA,SAAS,CAAC;AAElD,SAAA8D,kCAAAA,KAAC,OAAI;AAAA,IAAAC,WAAU;AAAA,IACTC,UAAA,CAACF,kCAAA,KAAA,OAAA;AAAA,MAAIC,WAAU;AAAA,MACTC,UAAA,CAACC,kCAAA,IAAA,MAAA;AAAA,QAAGF,WAAU;AAAA,QAAqBC,UAAY;AAAA,MAAA,CAAA,GAC/CC,kCAAA,IAACC,QAAA;AAAA,QACKC,SAAQ;AAAA,QACRC,SAAS1B;AAAAA,QACT2B,UAAU/B,QAAQgC,UAAU;AAAA,QAE3BN,UAAA1B,QAAQgC,UAAU,eAAe,iBAAiB;AAAA,MAAA,CACzD,CAAA;AAAA,IACN,CAAA,yCACC,OAAI;AAAA,MAAAP,WAAU;AAAA,MACTC,UAACF,kCAAA,KAAA,OAAA;AAAA,QAAIC,WAAU;AAAA,QACTC,UAAA,CAAAC,kCAAA,IAAC,OAAI;AAAA,UAAAF,WAAU;AAAA,UACTC,UAAAF,kCAAA,KAACS,SACK;AAAA,YAAAP,UAAA,CAACC,kCAAA,IAAAO,gBAAA;AAAA,cAAeC,SAAO;AAAA,cACjBT,UAAAF,kCAAA,KAACI,QAAA;AAAA,gBACK3F,IAAG;AAAA,gBACH4F,SAAS;AAAA,gBACTJ,WAAWW,GACL,iDACA,CAACvE,UAAUE,QAAQ,uBACzB;AAAA,gBAEA2D,UAAA,CAAAC,kCAAAA,IAACU,UAAa,EAAA,IACbxE,uCAAWE,QACNF,UAAUI,KAEGuD,kCAAAA,KAAAc,kBAAAA,UAAA;AAAA,kBAAAZ,UAAA,CAAOpD,OAAAT,UAAUE,MAAM,WAAW,GAAE,OAAIO,OAAOT,UAAUI,IAAI,WAAW,CAAA;AAAA,gBAC/E,CAAA,IAEAK,OAAOT,UAAUE,MAAM,WAAW,IAGxC4D,kCAAA,IAAC;kBAAKD,UAAW;AAAA,gBAAA,CAAA,CAAA;AAAA,cAE7B,CAAA;AAAA,YAEN,CAAA,GACCC,kCAAA,IAAAY,gBAAA;AAAA,cAAed,WAAU;AAAA,cAAae,OAAM;AAAA,cACvCd,UAAAC,kCAAA,IAACc,YAAA;AAAA,gBACKC,cAAY;AAAA,gBACZC,UAAU9E;AAAAA,gBACV+E,MAAK;AAAA,gBACLC,UAAUhD;AAAAA,cAChB,CAAA;AAAA,YACN,CAAA,CAAA;AAAA,UACN,CAAA;AAAA,QACN,CAAA,GAEA8B,kCAAA,IAACC,QAAA;AAAA,UACKlB,MAAK;AAAA,UACLe,WAAU;AAAA,UACVK,SAASA,MAAMpD,gBAAgB;AAAA,UAEpCgD,UAAA;AAAA,QAAA,CAED,CAAA;AAAA,MACN,CAAA;AAAA,IAEN,CAAA,0CAECoB,MAAK;AAAA,MAAAC,OAAO1F;AAAAA,MAAW2F,eAAe7E;AAAAA,MAAiBsD,WAAU;AAAA,MAC5DC,UAAA,CAAAF,kCAAA,KAACyB,UACM;AAAA,QAAAvB,UAAA,CAAA,CAAC5E,cAAc6E,kCAAAA,IAACuB,aAAY;AAAA,UAAAH,OAAM;AAAA,UAAarB,UAAW;AAAA,SAAA,GAC1D,kCAA+BC,kCAAA,IAAAuB,aAAA;AAAA,UAAYH,OAAM;AAAA,UAAYrB,UAAU;AAAA,QAAA,CAAA,GACvEC,kCAAA,IAAAuB,aAAA;AAAA,UAAYH,OAAM;AAAA,UAAerB,UAAa;AAAA,QAAA,CAAA,CAAA;AAAA,MACrD,CAAA,GACAC,kCAAA,IAACwB,aAAY;AAAA,QAAAJ,OAAM;AAAA,QACbrB,UAAAC,kCAAA,IAACyB,iBAAA;AAAA,UACK3H,SAAS8D;AAAAA,UACT/D,MAAME,UAAUA;AAAAA,UAEhB2H,WAAYrH,SACLwF,kCAAA,KAAA,MAAA;AAAA,YAAgBC,WAAU;AAAA,YACrBC,UAAA,CAAAC,kCAAA,IAAC,MAAG;AAAA,cAAAF,WAAU;AAAA,cAAmCC,UAAA1F,IAAIC;AAAAA,YAAG,CAAA,GACxD0F,kCAAA,IAAC,MAAG;AAAA,cAAAF,WAAU;AAAA,cACRC,UAAAC,kCAAA,IAAC,QAAA;AAAA,gBACKG,SAASA,MAAM;AACL,sBAAA,CAACjE,UAAUE,KAAM;AAErB,wBAAMM,gBAAgBC,OAAOT,UAAUE,MAAM,YAAY;AACzD,wBAAMQ,cAAcV,UAAUI,KAAKK,OAAOT,UAAUI,IAAI,YAAY,IAAII;AACxEzB,2BACM,iCAAiCZ,IAAIC,EAAE,SAASoC,aAAa,OAAOE,WAAW,SAASvC,IAAIE,KAAKoH,SAAS,CAAC,gBAAgB,WAAW,qCAC5I;AAAA,gBACN;AAAA,gBAEC5B,UAAI1F,IAAAE;AAAAA,cACX,CAAA;AAAA,YACN,CAAA,GACAyF,kCAAA,IAAC;cAAGF,WAAU;AAAA,cACPC,sCAAKvF,aAAaC,QAAQ,OAAM;AAAA,YACvC,CAAA,GACAuF,kCAAA,IAAC;cAAGF,WAAU;AAAA,cACPC,sCAAKrF,gBAAgBD,QAAQ,OAAM;AAAA,YAC1C,CAAA,GACAuF,kCAAA,IAAC;cAAGF,WAAU;AAAA,cACPC,sCAAKpF,eAAeF,QAAQ,OAAM;AAAA,YACzC,CAAA,GACAuF,kCAAA,IAAC;cAAGF,WAAU;AAAA,cACPC,sCAAKnF,gBAAgBH,QAAQ,OAAM;AAAA,YAC1C,CAAA,yCACC,MAAG;AAAA,cAAAqF,WAAU;AAAA,cACPC,WAAA1F,2BAAKQ,kBAAiB;AAAA,YAC7B,CAAA,yCACC,MAAG;AAAA,cAAAiF,WAAU;AAAA,cACPC,WAAA1F,2BAAKS,eAAc;AAAA,YAC1B,CAAA,yCACC,MAAG;AAAA,cAAAgF,WAAU;AAAA,cACPC,WAAA1F,2BAAKU,oBAAmB;AAAA,YAC/B,CAAA,CAAA;AAAA,UAAA,GArCGV,IAAIC,EAsCb;AAAA,UAEN2D;AAAAA,UACA2D,cAAa;AAAA,QACnB,CAAA;AAAA,MACN,CAAA,GACA5B,kCAAA,IAACwB,aAAY;AAAA,QAAAJ,OAAM;AAAA,QACbrB,UAAAC,kCAAA,IAACyB,iBAAA;AAAA,UACC3H,SAASqB,aAAW0C,yBAAuBE;AAAAA,UAEvClE,MACME,UAAUA;AAAAA,UAEhB2H,WAAYrH,SACLwF,kCAAA,KAAA,MAAA;AAAA,YAAgBC,WAAU;AAAA,YACrBC,UAAA,CAAAC,kCAAA,IAAC,MAAG;AAAA,cAAAF,WAAU;AAAA,cAAmCC,UAAA1F,IAAIC;AAAAA,YAAG,CAAA,GACvD0F,kCAAA,IAAA,MAAA;AAAA,cAAGF,WAAU;AAAA,cACRC,WAAC5E,aAAY6E,kCAAA,IAAC,QAAA;AAAA,gBACRG,SAASA,MAAM;AACL,sBAAA,CAACjE,UAAUE,KAAM;AAErB,wBAAMM,gBAAgBC,OAAOT,UAAUE,MAAM,YAAY;AACzD,wBAAMQ,cAAcV,UAAUI,KAAKK,OAAOT,UAAUI,IAAI,YAAY,IAAII;AAExEkD,qCAAmBvF,IAAIC,EAAE;AACzBW,2BACM,gCAAgCZ,IAAIC,EAAE,SAASoC,aAAa,OAAOE,WAAW,SAASvC,IAAIE,KAAKoH,SAAS,CAAC,gBAAgBxG,aAAa,cAAc,YAAY,sCACvK;AAAA,gBACN;AAAA,gBAEC4E,UAAI1F,IAAAE;AAAAA,cAAA,CACX,IAEAyF,kCAAA,IAAC,QACM;AAAA,gBAAAD,UAAA1F,IAAIE;AAAAA,cACX,CAAA;AAAA,YAEN,CAAA,GACAyF,kCAAA,IAAC;cAAGF,WAAU;AAAA,cACPC,sCAAKvF,aAAaC,QAAQ,OAAM;AAAA,YACvC,CAAA,GACAuF,kCAAA,IAAC;cAAGF,WAAU;AAAA,cACPC,sCAAKrF,gBAAgBD,QAAQ,OAAM;AAAA,YAC1C,CAAA,GACAuF,kCAAA,IAAC;cAAGF,WAAU;AAAA,cACPC,sCAAKpF,eAAeF,QAAQ,OAAM;AAAA,YACzC,CAAA,GACAuF,kCAAA,IAAC;cAAGF,WAAU;AAAA,cACPC,sCAAKnF,gBAAgBH,QAAQ,OAAM;AAAA,YAC1C,CAAA,GACC,CAACU,cAChD6E,kCAAA,IAAA,MAAA;AAAA,cAAGF,WAAU;AAAA,cACwCC,WAAA1F,2BAAKQ,kBAAiB;AAAA,YAC7B,CAAA,yCACC,MAAG;AAAA,cAAAiF,WAAU;AAAA,cACPC,WAAA1F,2BAAKS,eAAc;AAAA,YAC1B,CAAA,GACC,CAACK,cACD6E,kCAAA,IAAA,MAAA;AAAA,cAAGF,WAAU;AAAA,cACPC,WAAA1F,2BAAKU,oBAAmB;AAAA,aAC/B,GACCI,cACM6E,kCAAA,IAAA,MAAA;AAAA,cAAGF,WAAU;AAAA,cACPC,sCAAK8B,aAAapH,QAAQ,OAAM;AAAA,aACvC,GAELU,cACM6E,kCAAA,IAAA,MAAA;AAAA,cAAGF,WAAU;AAAA,cACPC,sCAAK+B,gBAAgBrH,QAAQ,OAAM;AAAA,YAC1C,CAAA,CAAA;AAAA,UAAA,GAvDHJ,IAAIC,EAyDb;AAAA,UAEN2D;AAAAA,UACA2D,cAAa;AAAA,QACnB,CAAA;AAAA,MACN,CAAA,GACA5B,kCAAA,IAACwB,aAAY;AAAA,QAAAJ,OAAM;AAAA,QACbrB,UAAAC,kCAAA,IAACyB,iBAAA;AAAA,UACK3H,SAAUqB,aAAW2C,qBAAmBE;AAAAA,UACxCnE,MACME,UAAUA;AAAAA,UAEhB2H,WAAYrH,SACLwF,kCAAA,KAAA,MAAA;AAAA,YAAgBC,WAAU;AAAA,YACrBC,UAAA,CAAAC,kCAAA,IAAC,MAAG;AAAA,cAAAF,WAAU;AAAA,cAAmCC,UAAA1F,IAAIC;AAAAA,YAAG,CAAA,GACvD0F,kCAAA,IAAA,MAAA;AAAA,cAAGF,WAAU;AAAA,cACZC,WAAC5E,aAAY6E,kCAAA,IAAC,QAAA;AAAA,gBACJG,SAASA,MAAM;AACL,sBAAA,CAACjE,UAAUE,KAAM;AAErB,wBAAMM,gBAAgBC,OAAOT,UAAUE,MAAM,YAAY;AACzD,wBAAMQ,cAAcV,UAAUI,KAAKK,OAAOT,UAAUI,IAAI,YAAY,IAAII;AACxEzB,2BACM,+BAA+BZ,IAAIC,EAAE,SAASoC,aAAa,OAAOE,WAAW,SAASvC,IAAIE,KAAKoH,UAAU,SAAStH,IAAIE,KAAKoH,SAAU,CAAA,gBAAgB,cAAc,+BAA+BxG,aAAawE,kBAAkB,WAAW,EAClP;AAAA,gBACN;AAAA,gBAECI,UAAI1F,IAAAE;AAAAA,cAAA,CACX,IACAyF,kCAAA,IAAC,QACM;AAAA,gBAAAD,UAAA1F,IAAIE;AAAAA,cACX,CAAA;AAAA,YAGN,CAAA,GACAyF,kCAAA,IAAC;cAAGF,WAAU;AAAA,cACPC,sCAAKvF,aAAaC,QAAQ,OAAM;AAAA,YACvC,CAAA,GACAuF,kCAAA,IAAC;cAAGF,WAAU;AAAA,cACPC,sCAAKrF,gBAAgBD,QAAQ,OAAM;AAAA,YAC1C,CAAA,GACAuF,kCAAA,IAAC;cAAGF,WAAU;AAAA,cACPC,sCAAKpF,eAAeF,QAAQ,OAAM;AAAA,YACzC,CAAA,GACAuF,kCAAA,IAAC;cAAGF,WAAU;AAAA,cACPC,sCAAKnF,gBAAgBH,QAAQ,OAAM;AAAA,YAC1C,CAAA,GACN,CAACU,cAC1C6E,kCAAA,IAAA,MAAA;AAAA,cAAGF,WAAU;AAAA,cACyCC,WAAA1F,2BAAKQ,kBAAiB;AAAA,YAC7B,CAAA,yCACC,MAAG;AAAA,cAAAiF,WAAU;AAAA,cACPC,WAAA1F,2BAAKS,eAAc;AAAA,YAC1B,CAAA,GACC,CAACK,cAC/C6E,kCAAA,IAAA,MAAA;AAAA,cAAGF,WAAU;AAAA,cACuCC,WAAA1F,2BAAKU,oBAAmB;AAAA,aAC/B,GACCI,cACM6E,kCAAA,IAAA,MAAA;AAAA,cAAGF,WAAU;AAAA,cACPC,sCAAK8B,aAAapH,QAAQ,OAAM;AAAA,aACvC,GAELU,cACM6E,kCAAA,IAAA,MAAA;AAAA,cAAGF,WAAU;AAAA,cACPC,sCAAK+B,gBAAgBrH,QAAQ,OAAM;AAAA,YAC1C,CAAA,CAAA;AAAA,UAAA,GArDHJ,IAAIC,EAwDb;AAAA,UAEN2D;AAAAA,UACA2D,cAAa;AAAA,QACnB,CAAA;AAAA,MACN,CAAA,CAAA;AAAA,IACN,CAAA,yCAEC,OAAI;AAAA,MAAA9B,WAAU;AAAA,MACTC,UAACC,kCAAA,IAAA+B,YAAA;AAAA,QACKhC,iDAACiC,mBACM;AAAA,UAAAjC,UAAA,CAAclE,cAAA,KACRmE,kCAAAA,IAAAiC,gBAAA;AAAA,YACKlC,UAACC,kCAAA,IAAAkC,oBAAA;AAAA,cAAmB/B,SAASA,MAAMtD,iBAAiBhB,cAAc,CAAC;AAAA,YAAG,CAAA;AAAA,UAC5E,CAAA,yCAELoG,gBACK;AAAA,YAAAlC,UAAAC,kCAAA,IAACmC,gBAAgB;AAAA,cAAApC,UAAAlE,cAAc;AAAA,YAAE,CAAA;AAAA,UACvC,CAAA,GACAmE,kCAAA,IAACiC,gBACK;AAAA,YAAAlC,UAAAC,kCAAA,IAACoC,gBAAe;AAAA,cAAAjC,SAASA,MAAMtD,iBAAiBhB,cAAc,CAAC;AAAA,YAAG,CAAA;AAAA,UACxE,CAAA,CAAA;AAAA,QACN,CAAA;AAAA,MACN,CAAA;AAAA,IACN,CAAA,CAAA;AAAA,EACN,CAAA;AAGZ;"}