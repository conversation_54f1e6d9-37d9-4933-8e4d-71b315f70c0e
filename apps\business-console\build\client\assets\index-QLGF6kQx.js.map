{"version": 3, "file": "index-QLGF6kQx.js", "sources": ["../../../node_modules/@radix-ui/react-presence/dist/index.mjs"], "sourcesContent": ["\"use client\";\n\n// packages/react/presence/src/Presence.tsx\nimport * as React2 from \"react\";\nimport { useComposedRefs } from \"@radix-ui/react-compose-refs\";\nimport { useLayoutEffect } from \"@radix-ui/react-use-layout-effect\";\n\n// packages/react/presence/src/useStateMachine.tsx\nimport * as React from \"react\";\nfunction useStateMachine(initialState, machine) {\n  return React.useReducer((state, event) => {\n    const nextState = machine[state][event];\n    return nextState ?? state;\n  }, initialState);\n}\n\n// packages/react/presence/src/Presence.tsx\nvar Presence = (props) => {\n  const { present, children } = props;\n  const presence = usePresence(present);\n  const child = typeof children === \"function\" ? children({ present: presence.isPresent }) : React2.Children.only(children);\n  const ref = useComposedRefs(presence.ref, getElementRef(child));\n  const forceMount = typeof children === \"function\";\n  return forceMount || presence.isPresent ? React2.cloneElement(child, { ref }) : null;\n};\nPresence.displayName = \"Presence\";\nfunction usePresence(present) {\n  const [node, setNode] = React2.useState();\n  const stylesRef = React2.useRef({});\n  const prevPresentRef = React2.useRef(present);\n  const prevAnimationNameRef = React2.useRef(\"none\");\n  const initialState = present ? \"mounted\" : \"unmounted\";\n  const [state, send] = useStateMachine(initialState, {\n    mounted: {\n      UNMOUNT: \"unmounted\",\n      ANIMATION_OUT: \"unmountSuspended\"\n    },\n    unmountSuspended: {\n      MOUNT: \"mounted\",\n      ANIMATION_END: \"unmounted\"\n    },\n    unmounted: {\n      MOUNT: \"mounted\"\n    }\n  });\n  React2.useEffect(() => {\n    const currentAnimationName = getAnimationName(stylesRef.current);\n    prevAnimationNameRef.current = state === \"mounted\" ? currentAnimationName : \"none\";\n  }, [state]);\n  useLayoutEffect(() => {\n    const styles = stylesRef.current;\n    const wasPresent = prevPresentRef.current;\n    const hasPresentChanged = wasPresent !== present;\n    if (hasPresentChanged) {\n      const prevAnimationName = prevAnimationNameRef.current;\n      const currentAnimationName = getAnimationName(styles);\n      if (present) {\n        send(\"MOUNT\");\n      } else if (currentAnimationName === \"none\" || styles?.display === \"none\") {\n        send(\"UNMOUNT\");\n      } else {\n        const isAnimating = prevAnimationName !== currentAnimationName;\n        if (wasPresent && isAnimating) {\n          send(\"ANIMATION_OUT\");\n        } else {\n          send(\"UNMOUNT\");\n        }\n      }\n      prevPresentRef.current = present;\n    }\n  }, [present, send]);\n  useLayoutEffect(() => {\n    if (node) {\n      let timeoutId;\n      const ownerWindow = node.ownerDocument.defaultView ?? window;\n      const handleAnimationEnd = (event) => {\n        const currentAnimationName = getAnimationName(stylesRef.current);\n        const isCurrentAnimation = currentAnimationName.includes(event.animationName);\n        if (event.target === node && isCurrentAnimation) {\n          send(\"ANIMATION_END\");\n          if (!prevPresentRef.current) {\n            const currentFillMode = node.style.animationFillMode;\n            node.style.animationFillMode = \"forwards\";\n            timeoutId = ownerWindow.setTimeout(() => {\n              if (node.style.animationFillMode === \"forwards\") {\n                node.style.animationFillMode = currentFillMode;\n              }\n            });\n          }\n        }\n      };\n      const handleAnimationStart = (event) => {\n        if (event.target === node) {\n          prevAnimationNameRef.current = getAnimationName(stylesRef.current);\n        }\n      };\n      node.addEventListener(\"animationstart\", handleAnimationStart);\n      node.addEventListener(\"animationcancel\", handleAnimationEnd);\n      node.addEventListener(\"animationend\", handleAnimationEnd);\n      return () => {\n        ownerWindow.clearTimeout(timeoutId);\n        node.removeEventListener(\"animationstart\", handleAnimationStart);\n        node.removeEventListener(\"animationcancel\", handleAnimationEnd);\n        node.removeEventListener(\"animationend\", handleAnimationEnd);\n      };\n    } else {\n      send(\"ANIMATION_END\");\n    }\n  }, [node, send]);\n  return {\n    isPresent: [\"mounted\", \"unmountSuspended\"].includes(state),\n    ref: React2.useCallback((node2) => {\n      if (node2) stylesRef.current = getComputedStyle(node2);\n      setNode(node2);\n    }, [])\n  };\n}\nfunction getAnimationName(styles) {\n  return styles?.animationName || \"none\";\n}\nfunction getElementRef(element) {\n  let getter = Object.getOwnPropertyDescriptor(element.props, \"ref\")?.get;\n  let mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return element.ref;\n  }\n  getter = Object.getOwnPropertyDescriptor(element, \"ref\")?.get;\n  mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return element.props.ref;\n  }\n  return element.props.ref || element.ref;\n}\nexport {\n  Presence\n};\n//# sourceMappingURL=index.mjs.map\n"], "names": ["React.useReducer", "React2.Children", "React2.cloneElement", "React2.useState", "React2.useRef", "React2.useEffect", "useLayoutEffect", "React2.useCallback"], "mappings": ";;;AASA,SAAS,gBAAgB,cAAc,SAAS;AAC9C,SAAOA,aAAgB,WAAC,CAAC,OAAO,UAAU;AACxC,UAAM,YAAY,QAAQ,KAAK,EAAE,KAAK;AACtC,WAAO,aAAa;AAAA,EACrB,GAAE,YAAY;AACjB;AAGG,IAAC,WAAW,CAAC,UAAU;AACxB,QAAM,EAAE,SAAS,SAAQ,IAAK;AAC9B,QAAM,WAAW,YAAY,OAAO;AACpC,QAAM,QAAQ,OAAO,aAAa,aAAa,SAAS,EAAE,SAAS,SAAS,UAAS,CAAE,IAAIC,sBAAgB,KAAK,QAAQ;AACxH,QAAM,MAAM,gBAAgB,SAAS,KAAK,cAAc,KAAK,CAAC;AAC9D,QAAM,aAAa,OAAO,aAAa;AACvC,SAAO,cAAc,SAAS,YAAYC,aAAAA,aAAoB,OAAO,EAAE,IAAK,CAAA,IAAI;AAClF;AACA,SAAS,cAAc;AACvB,SAAS,YAAY,SAAS;AAC5B,QAAM,CAAC,MAAM,OAAO,IAAIC,sBAAiB;AACzC,QAAM,YAAYC,aAAa,OAAC,EAAE;AAClC,QAAM,iBAAiBA,aAAa,OAAC,OAAO;AAC5C,QAAM,uBAAuBA,aAAa,OAAC,MAAM;AACjD,QAAM,eAAe,UAAU,YAAY;AAC3C,QAAM,CAAC,OAAO,IAAI,IAAI,gBAAgB,cAAc;AAAA,IAClD,SAAS;AAAA,MACP,SAAS;AAAA,MACT,eAAe;AAAA,IAChB;AAAA,IACD,kBAAkB;AAAA,MAChB,OAAO;AAAA,MACP,eAAe;AAAA,IAChB;AAAA,IACD,WAAW;AAAA,MACT,OAAO;AAAA,IACb;AAAA,EACA,CAAG;AACDC,eAAAA,UAAiB,MAAM;AACrB,UAAM,uBAAuB,iBAAiB,UAAU,OAAO;AAC/D,yBAAqB,UAAU,UAAU,YAAY,uBAAuB;AAAA,EAChF,GAAK,CAAC,KAAK,CAAC;AACVC,mBAAgB,MAAM;AACpB,UAAM,SAAS,UAAU;AACzB,UAAM,aAAa,eAAe;AAClC,UAAM,oBAAoB,eAAe;AACzC,QAAI,mBAAmB;AACrB,YAAM,oBAAoB,qBAAqB;AAC/C,YAAM,uBAAuB,iBAAiB,MAAM;AACpD,UAAI,SAAS;AACX,aAAK,OAAO;AAAA,MACb,WAAU,yBAAyB,WAAU,iCAAQ,aAAY,QAAQ;AACxE,aAAK,SAAS;AAAA,MACtB,OAAa;AACL,cAAM,cAAc,sBAAsB;AAC1C,YAAI,cAAc,aAAa;AAC7B,eAAK,eAAe;AAAA,QAC9B,OAAe;AACL,eAAK,SAAS;AAAA,QACxB;AAAA,MACA;AACM,qBAAe,UAAU;AAAA,IAC/B;AAAA,EACA,GAAK,CAAC,SAAS,IAAI,CAAC;AAClBA,mBAAgB,MAAM;AACpB,QAAI,MAAM;AACR,UAAI;AACJ,YAAM,cAAc,KAAK,cAAc,eAAe;AACtD,YAAM,qBAAqB,CAAC,UAAU;AACpC,cAAM,uBAAuB,iBAAiB,UAAU,OAAO;AAC/D,cAAM,qBAAqB,qBAAqB,SAAS,MAAM,aAAa;AAC5E,YAAI,MAAM,WAAW,QAAQ,oBAAoB;AAC/C,eAAK,eAAe;AACpB,cAAI,CAAC,eAAe,SAAS;AAC3B,kBAAM,kBAAkB,KAAK,MAAM;AACnC,iBAAK,MAAM,oBAAoB;AAC/B,wBAAY,YAAY,WAAW,MAAM;AACvC,kBAAI,KAAK,MAAM,sBAAsB,YAAY;AAC/C,qBAAK,MAAM,oBAAoB;AAAA,cAC/C;AAAA,YACA,CAAa;AAAA,UACb;AAAA,QACA;AAAA,MACO;AACD,YAAM,uBAAuB,CAAC,UAAU;AACtC,YAAI,MAAM,WAAW,MAAM;AACzB,+BAAqB,UAAU,iBAAiB,UAAU,OAAO;AAAA,QAC3E;AAAA,MACO;AACD,WAAK,iBAAiB,kBAAkB,oBAAoB;AAC5D,WAAK,iBAAiB,mBAAmB,kBAAkB;AAC3D,WAAK,iBAAiB,gBAAgB,kBAAkB;AACxD,aAAO,MAAM;AACX,oBAAY,aAAa,SAAS;AAClC,aAAK,oBAAoB,kBAAkB,oBAAoB;AAC/D,aAAK,oBAAoB,mBAAmB,kBAAkB;AAC9D,aAAK,oBAAoB,gBAAgB,kBAAkB;AAAA,MAC5D;AAAA,IACP,OAAW;AACL,WAAK,eAAe;AAAA,IAC1B;AAAA,EACA,GAAK,CAAC,MAAM,IAAI,CAAC;AACf,SAAO;AAAA,IACL,WAAW,CAAC,WAAW,kBAAkB,EAAE,SAAS,KAAK;AAAA,IACzD,KAAKC,aAAAA,YAAmB,CAAC,UAAU;AACjC,UAAI,MAAO,WAAU,UAAU,iBAAiB,KAAK;AACrD,cAAQ,KAAK;AAAA,IACnB,GAAO,CAAE,CAAA;AAAA,EACN;AACH;AACA,SAAS,iBAAiB,QAAQ;AAChC,UAAO,iCAAQ,kBAAiB;AAClC;AACA,SAAS,cAAc,SAAS;;AAC9B,MAAI,UAAS,YAAO,yBAAyB,QAAQ,OAAO,KAAK,MAApD,mBAAuD;AACpE,MAAI,UAAU,UAAU,oBAAoB,UAAU,OAAO;AAC7D,MAAI,SAAS;AACX,WAAO,QAAQ;AAAA,EACnB;AACE,YAAS,YAAO,yBAAyB,SAAS,KAAK,MAA9C,mBAAiD;AAC1D,YAAU,UAAU,oBAAoB,UAAU,OAAO;AACzD,MAAI,SAAS;AACX,WAAO,QAAQ,MAAM;AAAA,EACzB;AACE,SAAO,QAAQ,MAAM,OAAO,QAAQ;AACtC;", "x_google_ignoreList": [0]}