{"version": 3, "file": "home.agentSalesReport-CxHgs7Qx.js", "sources": ["../../../app/routes/home.agentSalesReport.tsx"], "sourcesContent": ["import { j<PERSON>, LoaderFunction } from \"@remix-run/node\";\r\nimport { Form, useFetcher, use<PERSON>oaderD<PERSON>, useNavigate, useSearchParams } from \"@remix-run/react\";\r\nimport * as React from \"react\";\r\nimport { ArrowLeft, CalendarIcon } from \"lucide-react\";\r\nimport { <PERSON><PERSON> } from \"~/components/ui/button\";\r\nimport { Input } from \"~/components/ui/input\";\r\nimport {\r\n      Table,\r\n      TableBody,\r\n      TableCell,\r\n      TableHead,\r\n      TableHeader,\r\n      TableRow,\r\n} from \"~/components/ui/table\";\r\nimport {\r\n      Popover,\r\n      PopoverContent,\r\n      PopoverTrigger,\r\n} from \"~/components/ui/popover\";\r\nimport { Calendar } from \"~/components/ui/calendar\";\r\nimport { format, parseISO } from \"date-fns\";\r\nimport { getSession } from \"~/utils/session.server\";\r\n// import { getAgentSales } from \"~/services/salesinfoDetails\";\r\nimport { AgentSalesInfo } from \"~/types/api/businessConsoleService/salesinfo\";\r\nimport {\r\n      Select,\r\n      SelectContent,\r\n      SelectItem,\r\n      SelectTrigger,\r\n      SelectValue,\r\n} from \"~/components/ui/select\";\r\nimport { withAuth, withResponse } from \"@utils/auth-utils\";\r\n\r\n\r\ninterface LoaderData {\r\n      agentSales: AgentSalesInfo[]\r\n      sellerName: string | null,\r\n      date: string\r\n}\r\n\r\nexport const loader: LoaderFunction = withAuth(async ({ request }) => {\r\n      const url = new URL(request.url);\r\n      const date = url.searchParams.get(\"date\") as string;\r\n      const formatDate = format(date, \"yyyy-MM-dd\")\r\n      const sellerName = url.searchParams.get(\"sellerName\")\r\n            ? decodeURIComponent(url.searchParams.get(\"sellerName\") as string)\r\n            : null;\r\n      if (!formatDate) {\r\n            throw new Error(\"Date is required\");\r\n      }\r\n\r\n      const session = await getSession(request.headers.get(\"Cookie\"));\r\n      const access_token = session.get(\"access_token\") as string | null;\r\n      try {\r\n            // const response = await getAgentSales(sellerId, formatDate, access_token);\r\n            return withResponse({\r\n                  // agentSales: response.data,\r\n                  agentSales: [],\r\n\r\n\r\n            })\r\n      }\r\n      catch (error) {\r\n            throw new Error(`Error fetching transaction details: ${error}`);\r\n      }\r\n});\r\n\r\nexport default function AgentWiseSales() {\r\n      const navigate = useNavigate();\r\n      const loaderData = useLoaderData<LoaderData>();\r\n      const initialDate = parseISO(loaderData.date);\r\n      const [date, setDate] = React.useState<Date | undefined>(initialDate);\r\n      const [data, setData] = React.useState<AgentSalesInfo[]>(loaderData.agentSales);\r\n      const [loading, setLoading] = React.useState(false);\r\n      const [searchTerm, setSearchTerm] = React.useState(\"\");\r\n      const [pageSize, setPageSize] = React.useState(\"10\");\r\n      const [currentPage, setCurrentPage] = React.useState(1);\r\n      const fetcher = useFetcher<LoaderData>();\r\n      React.useEffect(() => {\r\n\r\n            if (fetcher.data?.agentSales) {\r\n\r\n\r\n                  setData(fetcher.data?.agentSales);\r\n            }\r\n      }, [fetcher.data?.agentSales]);\r\n\r\n      const filteredData = React.useMemo(() => {\r\n            return data.filter((item) => {\r\n                  const searchFields = [\r\n                        item.agentId.toString().toLowerCase(),\r\n                        item.agentName.toLowerCase(),\r\n                  ];\r\n                  return searchTerm === \"\" || searchFields.some((field) =>\r\n                        field.includes(searchTerm.toLowerCase())\r\n                  );\r\n            });\r\n      }, [data, searchTerm]);\r\n\r\n      const paginatedData = React.useMemo(() => {\r\n            const start = (currentPage - 1) * Number(pageSize);\r\n            const end = start + Number(pageSize);\r\n            return [...filteredData]\r\n                  .sort((a, b) => a.agentName.localeCompare(b.agentName))\r\n                  .slice(start, end);\r\n      }, [filteredData, currentPage, pageSize]);\r\n\r\n      const totalPages = Math.ceil(filteredData.length / Number(pageSize));\r\n\r\n\r\n      const handleSubmit = (sellerId: number, deliveryDate: Date | undefined) => {\r\n            const formData = new FormData();\r\n            formData.append(\"sellerId\", sellerId.toString())\r\n            formData.append(\"date\", deliveryDate as unknown as string)\r\n            fetcher.submit(formData, { method: \"GET\" })\r\n\r\n      }\r\n\r\n      return (\r\n            <div className=\"container mx-auto\">\r\n                  <div className=\"flex items-center gap-2 mb-6 my-6\">\r\n                        <Button variant=\"ghost\" size=\"sm\" onClick={() => navigate(-1)}>\r\n                              <ArrowLeft className=\"h-4 w-4 mr-2\" />\r\n                              Back to Sales Reports\r\n                        </Button>\r\n                        <span className=\"text-muted-foreground\">/</span>\r\n                        <span className=\"font-semibold\">{loaderData.sellerName}</span>\r\n                  </div>\r\n                  <div className=\"flex space-x-5  my-5\">\r\n\r\n\r\n                        <div className=\" flex space-x-2\">\r\n\r\n\r\n\r\n                              <Popover>\r\n                                    <PopoverTrigger asChild>\r\n                                          <Button variant=\"outline\" className=\"w-[280px]\">\r\n                                                <CalendarIcon className=\"mr-2 h-4 w-4\" />\r\n                                                {date ? format(date, \"PPP\") : \"Pick a date\"}\r\n                                          </Button>\r\n                                    </PopoverTrigger>\r\n                                    <PopoverContent className=\"w-auto p-0\" >\r\n                                          <Calendar\r\n                                                mode=\"single\"\r\n                                                selected={date}\r\n                                                onSelect={setDate}\r\n                                                initialFocus\r\n                                          />\r\n                                    </PopoverContent>\r\n                              </Popover>\r\n                              <Input type=\"hidden\" name=\"date\" value={date ? format(date, \"yyyy-MM-dd\") : \"\"} />\r\n                              <Input type=\"hidden\" name=\"sellerId\" value={loaderData.sellerId} />\r\n\r\n                              <Button type=\"submit\" onClick={() => handleSubmit(loaderData.sellerId, date)}>\r\n                                    {loading ? \"Submitting\" : \"View Report\"}\r\n                              </Button>\r\n                        </div>\r\n                        <Input\r\n                              placeholder=\"Search by Agent Name or ID...\"\r\n                              value={searchTerm}\r\n                              onChange={(e) => setSearchTerm(e.target.value)}\r\n                              className=\"max-w-sm\"\r\n                        />\r\n                        <Select value={pageSize} onValueChange={setPageSize}>\r\n                              <SelectTrigger className=\"w-[180px]\">\r\n                                    <SelectValue placeholder=\"Rows per page\" />\r\n                              </SelectTrigger>\r\n                              <SelectContent>\r\n                                    <SelectItem value=\"5\">5 per page</SelectItem>\r\n                                    <SelectItem value=\"10\">10 per page</SelectItem>\r\n                                    <SelectItem value=\"20\">20 per page</SelectItem>\r\n                                    <SelectItem value=\"50\">50 per page</SelectItem>\r\n                              </SelectContent>\r\n                        </Select>\r\n                  </div>\r\n\r\n                  <div className=\"rounded-md border\">\r\n                        <Table>\r\n                              <TableHeader>\r\n                                    <TableRow>\r\n                                          <TableHead className=\"text-right\">Booked Qty</TableHead>\r\n                                          <TableHead className=\"text-right\">Delivered Qty</TableHead>\r\n                                          <TableHead className=\"text-right\">Return Qty</TableHead>\r\n                                          <TableHead className=\"text-right\">Cancel Qty</TableHead>\r\n                                          <TableHead className=\"text-right\">Shop Count</TableHead>\r\n                                    </TableRow>\r\n                              </TableHeader>\r\n                              <TableBody>\r\n                                    {paginatedData.length > 0 ? (\r\n                                          paginatedData.map((row) => (\r\n                                                <TableRow key={row.agentId}>\r\n                                                      <TableCell className=\"font-medium\">{row.agentId}</TableCell>\r\n                                                      <TableCell>{row.agentName}</TableCell>\r\n                                                      <TableCell className=\"text-right\">\r\n                                                            {row.totalBookedWeight.toFixed(2)}\r\n                                                      </TableCell>\r\n                                                      <TableCell className=\"text-right\">\r\n                                                            {row.totalDeliveredWeight.toFixed(2)}\r\n                                                      </TableCell>\r\n                                                      <TableCell className=\"text-right\">\r\n                                                            {row.totalReturnedWeight.toFixed(2)}\r\n                                                      </TableCell>\r\n                                                      <TableCell className=\"text-right\">\r\n                                                            {row.totalCancelledWeight.toFixed(2)}\r\n                                                      </TableCell>\r\n                                                      <TableCell className=\"text-right\">\r\n                                                            {row.shopCount.toFixed(2)}\r\n                                                      </TableCell>\r\n                                                </TableRow>\r\n                                          ))\r\n                                    ) : (\r\n                                          <TableRow>\r\n                                                <TableCell\r\n                                                      colSpan={7}\r\n                                                      className=\"h-24 text-center\"\r\n                                                >\r\n                                                      No results.\r\n                                                </TableCell>\r\n                                          </TableRow>\r\n                                    )}\r\n                              </TableBody>\r\n                        </Table>\r\n                  </div>\r\n\r\n                  <div className=\"flex items-center justify-between px-2 py-4\">\r\n                        <div className=\"text-sm text-gray-500\">\r\n                              Showing {(currentPage - 1) * Number(pageSize) + 1} to{\" \"}\r\n                              {Math.min(currentPage * Number(pageSize), filteredData.length)} of{\" \"}\r\n                              {filteredData.length} results\r\n                        </div>\r\n                        <div className=\"flex items-center space-x-2\">\r\n                              <Button\r\n                                    variant=\"outline\"\r\n                                    size=\"sm\"\r\n                                    onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}\r\n                                    disabled={currentPage === 1}\r\n                              >\r\n                                    Previous\r\n                              </Button>\r\n                              <Button\r\n                                    variant=\"outline\"\r\n                                    size=\"sm\"\r\n                                    onClick={() =>\r\n                                          setCurrentPage((prev) => Math.min(prev + 1, totalPages))\r\n                                    }\r\n                                    disabled={currentPage === totalPages}\r\n                              >\r\n                                    Next\r\n                              </Button>\r\n                        </div>\r\n                  </div>\r\n            </div>\r\n      );\r\n}\r\n"], "names": ["AgentWiseSales", "navigate", "useNavigate", "loaderData", "useLoaderData", "initialDate", "parseISO", "date", "setDate", "React", "data", "setData", "agentSales", "loading", "setLoading", "searchTerm", "setSearchTerm", "pageSize", "setPageSize", "currentPage", "setCurrentPage", "fetcher", "useFetcher", "filteredData", "filter", "item", "searchFields", "agentId", "toString", "toLowerCase", "<PERSON><PERSON><PERSON>", "some", "field", "includes", "paginatedData", "start", "Number", "end", "sort", "a", "b", "localeCompare", "slice", "totalPages", "Math", "ceil", "length", "handleSubmit", "sellerId", "deliveryDate", "formData", "FormData", "append", "submit", "method", "jsxs", "className", "children", "<PERSON><PERSON>", "variant", "size", "onClick", "jsx", "ArrowLeft", "sellerName", "Popover", "PopoverTrigger", "<PERSON><PERSON><PERSON><PERSON>", "CalendarIcon", "format", "PopoverC<PERSON>nt", "Calendar", "mode", "selected", "onSelect", "initialFocus", "Input", "type", "name", "value", "placeholder", "onChange", "e", "target", "Select", "onValueChange", "SelectTrigger", "SelectValue", "SelectContent", "SelectItem", "Table", "TableHeader", "TableRow", "TableHead", "TableBody", "map", "row", "TableCell", "totalBookedWeight", "toFixed", "totalDeliveredWeight", "totalReturnedWeight", "totalCancelledWeight", "shopCount", "colSpan", "min", "prev", "max", "disabled"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmEA,SAAwBA,iBAAiB;;AACnC,QAAMC,WAAWC,YAAY;AAC7B,QAAMC,aAAaC,cAA0B;AACvC,QAAAC,cAAcC,SAASH,WAAWI,IAAI;AAC5C,QAAM,CAACA,MAAMC,OAAO,IAAIC,aAAAA,SAAiCJ,WAAW;AACpE,QAAM,CAACK,MAAMC,OAAO,IAAIF,aAAAA,SAAiCN,WAAWS,UAAU;AAC9E,QAAM,CAACC,SAASC,UAAU,IAAIL,aAAAA,SAAe,KAAK;AAClD,QAAM,CAACM,YAAYC,aAAa,IAAIP,aAAAA,SAAe,EAAE;AACrD,QAAM,CAACQ,UAAUC,WAAW,IAAIT,aAAAA,SAAe,IAAI;AACnD,QAAM,CAACU,aAAaC,cAAc,IAAIX,aAAAA,SAAe,CAAC;AACtD,QAAMY,UAAUC,WAAuB;AACvCb,eAAAA,UAAgB,MAAM;;AAEZ,SAAAY,MAAAA,QAAQX,SAARW,gBAAAA,IAAcT,YAAY;AAGhBD,eAAAU,aAAQX,SAARW,mBAAcT,UAAU;AAAA,IACtC;AAAA,EACH,GAAA,EAACS,aAAQX,SAARW,mBAAcT,UAAU,CAAC;AAEvB,QAAAW,eAAed,aAAAA,QAAc,MAAM;AAC5B,WAAAC,KAAKc,OAAQC,UAAS;AACvB,YAAMC,eAAe,CACfD,KAAKE,QAAQC,SAAS,EAAEC,YAAY,GACpCJ,KAAKK,UAAUD,aAAY;AAE1B,aAAAd,eAAe,MAAMW,aAAaK,KAAMC,WACzCA,MAAMC,SAASlB,WAAWc,aAAa,CAC7C;AAAA,IACN,CAAC;AAAA,EACP,GAAG,CAACnB,MAAMK,UAAU,CAAC;AAEf,QAAAmB,gBAAgBzB,aAAAA,QAAc,MAAM;AACpC,UAAM0B,SAAShB,cAAc,KAAKiB,OAAOnB,QAAQ;AAC3C,UAAAoB,MAAMF,QAAQC,OAAOnB,QAAQ;AACnC,WAAO,CAAC,GAAGM,YAAY,EAChBe,KAAK,CAACC,GAAGC,MAAMD,EAAET,UAAUW,cAAcD,EAAEV,SAAS,CAAC,EACrDY,MAAMP,OAAOE,GAAG;AAAA,EAC1B,GAAA,CAACd,cAAcJ,aAAaF,QAAQ,CAAC;AAExC,QAAM0B,aAAaC,KAAKC,KAAKtB,aAAauB,SAASV,OAAOnB,QAAQ,CAAC;AAG7D,QAAA8B,eAAeA,CAACC,UAAkBC,iBAAmC;AAC/D,UAAAC,WAAW,IAAIC,SAAS;AAC9BD,aAASE,OAAO,YAAYJ,SAASpB,SAAA,CAAU;AACtCsB,aAAAE,OAAO,QAAQH,YAAiC;AACzD5B,YAAQgC,OAAOH,UAAU;AAAA,MAAEI,QAAQ;AAAA,IAAM,CAAC;AAAA,EAEhD;AAGM,SAAAC,kCAAAA,KAAC,OAAI;AAAA,IAAAC,WAAU;AAAA,IACTC,UAAA,CAACF,kCAAA,KAAA,OAAA;AAAA,MAAIC,WAAU;AAAA,MACTC,UAAA,CAACF,kCAAA,KAAAG,QAAA;AAAA,QAAOC,SAAQ;AAAA,QAAQC,MAAK;AAAA,QAAKC,SAASA,MAAM5D,SAAS,EAAE;AAAA,QACtDwD,UAAA,CAACK,kCAAA,IAAAC,WAAA;AAAA,UAAUP,WAAU;AAAA,QAAe,CAAA,GAAE,uBAAA;AAAA,MAE5C,CAAA,GACCM,kCAAA,IAAA,QAAA;AAAA,QAAKN,WAAU;AAAA,QAAwBC,UAAC;AAAA,MAAA,CAAA,GACxCK,kCAAA,IAAA,QAAA;AAAA,QAAKN,WAAU;AAAA,QAAiBC,qBAAWO;AAAAA,MAAW,CAAA,CAAA;AAAA,IAC7D,CAAA,GACAT,kCAAA,KAAC,OAAI;AAAA,MAAAC,WAAU;AAAA,MAGTC,UAAA,CAACF,kCAAA,KAAA,OAAA;AAAA,QAAIC,WAAU;AAAA,QAITC,UAAA,CAAAF,kCAAA,KAACU,SACK;AAAA,UAAAR,UAAA,CAACK,kCAAA,IAAAI,gBAAA;AAAA,YAAeC,SAAO;AAAA,YACjBV,UAAAF,kCAAA,KAACG;cAAOC,SAAQ;AAAA,cAAUH,WAAU;AAAA,cAC9BC,UAAA,CAACK,kCAAA,IAAAM,UAAA;AAAA,gBAAaZ,WAAU;AAAA,cAAe,CAAA,GACtCjD,OAAO8D,OAAO9D,MAAM,KAAK,IAAI,aAAA;AAAA,YACpC,CAAA;AAAA,UACN,CAAA,GACAuD,kCAAA,IAACQ,gBAAe;AAAA,YAAAd,WAAU;AAAA,YACpBC,UAAAK,kCAAA,IAACS,YAAA;AAAA,cACKC,MAAK;AAAA,cACLC,UAAUlE;AAAAA,cACVmE,UAAUlE;AAAAA,cACVmE,cAAY;AAAA,YAClB,CAAA;AAAA,UACN,CAAA,CAAA;AAAA,QACN,CAAA,GACCb,kCAAA,IAAAc,OAAA;AAAA,UAAMC,MAAK;AAAA,UAASC,MAAK;AAAA,UAAOC,OAAOxE,OAAO8D,OAAO9D,MAAM,YAAY,IAAI;AAAA,QAAI,CAAA,GAChFuD,kCAAA,IAACc;UAAMC,MAAK;AAAA,UAASC,MAAK;AAAA,UAAWC,OAAO5E,WAAW6C;AAAAA,QAAU,CAAA,GAEhEc,kCAAA,IAAAJ,QAAA;AAAA,UAAOmB,MAAK;AAAA,UAAShB,SAASA,MAAMd,aAAa5C,WAAW6C,UAAUzC,IAAI;AAAA,UACpEkD,UAAA5C,UAAU,eAAe;AAAA,QAChC,CAAA,CAAA;AAAA,MACN,CAAA,GACAiD,kCAAA,IAACc,OAAA;AAAA,QACKI,aAAY;AAAA,QACZD,OAAOhE;AAAAA,QACPkE,UAAWC,OAAMlE,cAAckE,EAAEC,OAAOJ,KAAK;AAAA,QAC7CvB,WAAU;AAAA,MAAA,CAChB,GACCD,kCAAA,KAAA6B,QAAA;AAAA,QAAOL,OAAO9D;AAAAA,QAAUoE,eAAenE;AAAAA,QAClCuC,UAAA,CAAAK,kCAAA,IAACwB;UAAc9B,WAAU;AAAA,UACnBC,gDAAC8B,aAAY;AAAA,YAAAP,aAAY;AAAA,UAAgB,CAAA;AAAA,QAC/C,CAAA,0CACCQ,eACK;AAAA,UAAA/B,UAAA,CAACK,kCAAA,IAAA2B,YAAA;AAAA,YAAWV,OAAM;AAAA,YAAItB,UAAU;AAAA,UAAA,CAAA,GAC/BK,kCAAA,IAAA2B,YAAA;AAAA,YAAWV,OAAM;AAAA,YAAKtB,UAAW;AAAA,UAAA,CAAA,GACjCK,kCAAA,IAAA2B,YAAA;AAAA,YAAWV,OAAM;AAAA,YAAKtB,UAAW;AAAA,UAAA,CAAA,GACjCK,kCAAA,IAAA2B,YAAA;AAAA,YAAWV,OAAM;AAAA,YAAKtB,UAAW;AAAA,UAAA,CAAA,CAAA;AAAA,QACxC,CAAA,CAAA;AAAA,MACN,CAAA,CAAA;AAAA,IACN,CAAA,GAECK,kCAAA,IAAA,OAAA;AAAA,MAAIN,WAAU;AAAA,MACTC,iDAACiC,OACK;AAAA,QAAAjC,UAAA,CAACK,kCAAA,IAAA6B,aAAA;AAAA,UACKlC,iDAACmC,UACK;AAAA,YAAAnC,UAAA,CAACK,kCAAA,IAAA+B,WAAA;AAAA,cAAUrC,WAAU;AAAA,cAAaC,UAAU;AAAA,YAAA,CAAA,GAC3CK,kCAAA,IAAA+B,WAAA;AAAA,cAAUrC,WAAU;AAAA,cAAaC,UAAa;AAAA,YAAA,CAAA,GAC9CK,kCAAA,IAAA+B,WAAA;AAAA,cAAUrC,WAAU;AAAA,cAAaC,UAAU;AAAA,YAAA,CAAA,GAC3CK,kCAAA,IAAA+B,WAAA;AAAA,cAAUrC,WAAU;AAAA,cAAaC,UAAU;AAAA,YAAA,CAAA,GAC3CK,kCAAA,IAAA+B,WAAA;AAAA,cAAUrC,WAAU;AAAA,cAAaC,UAAU;AAAA,YAAA,CAAA,CAAA;AAAA,UAClD,CAAA;AAAA,QACN,CAAA,GACAK,kCAAA,IAACgC,WACM;AAAA,UAAArC,UAAAvB,cAAcY,SAAS,IAClBZ,cAAc6D,IAAKC,SACbzC,kCAAA,KAACqC,UACK;AAAA,YAAAnC,UAAA,CAAAK,kCAAA,IAACmC,WAAU;AAAA,cAAAzC,WAAU;AAAA,cAAeC,UAAAuC,IAAIrE;AAAAA,YAAQ,CAAA,GAChDmC,kCAAA,IAACmC,WAAW;AAAA,cAAAxC,UAAAuC,IAAIlE;AAAAA,YAAU,CAAA,GAC1BgC,kCAAA,IAACmC;cAAUzC,WAAU;AAAA,cACdC,cAAIyC,kBAAkBC,QAAQ,CAAC;AAAA,YACtC,CAAA,GACArC,kCAAA,IAACmC;cAAUzC,WAAU;AAAA,cACdC,cAAI2C,qBAAqBD,QAAQ,CAAC;AAAA,YACzC,CAAA,GACArC,kCAAA,IAACmC;cAAUzC,WAAU;AAAA,cACdC,cAAI4C,oBAAoBF,QAAQ,CAAC;AAAA,YACxC,CAAA,GACArC,kCAAA,IAACmC;cAAUzC,WAAU;AAAA,cACdC,cAAI6C,qBAAqBH,QAAQ,CAAC;AAAA,YACzC,CAAA,GACArC,kCAAA,IAACmC;cAAUzC,WAAU;AAAA,cACdC,cAAI8C,UAAUJ,QAAQ,CAAC;AAAA,YAC9B,CAAA,CAAA;AAAA,UAAA,GAjBSH,IAAIrE,OAkBnB,CACL,0CAEAiE,UACK;AAAA,YAAAnC,UAAAK,kCAAA,IAACmC,WAAA;AAAA,cACKO,SAAS;AAAA,cACThD,WAAU;AAAA,cACfC,UAAA;AAAA,YAED,CAAA;AAAA,UACN,CAAA;AAAA,QAEZ,CAAA,CAAA;AAAA,MACN,CAAA;AAAA,IACN,CAAA,GAEAF,kCAAA,KAAC,OAAI;AAAA,MAAAC,WAAU;AAAA,MACTC,UAAA,CAACF,kCAAA,KAAA,OAAA;AAAA,QAAIC,WAAU;AAAA,QAAwBC,UAAA,CAAA,aACvBtC,cAAc,KAAKiB,OAAOnB,QAAQ,IAAI,GAAE,OAAI,KACrD2B,KAAK6D,IAAItF,cAAciB,OAAOnB,QAAQ,GAAGM,aAAauB,MAAM,GAAE,OAAI,KAClEvB,aAAauB,QAAO,UAAA;AAAA,MAC3B,CAAA,GACAS,kCAAA,KAAC,OAAI;AAAA,QAAAC,WAAU;AAAA,QACTC,UAAA,CAAAK,kCAAA,IAACJ,QAAA;AAAA,UACKC,SAAQ;AAAA,UACRC,MAAK;AAAA,UACLC,SAASA,MAAMzC,eAAgBsF,UAAS9D,KAAK+D,IAAID,OAAO,GAAG,CAAC,CAAC;AAAA,UAC7DE,UAAUzF,gBAAgB;AAAA,UAC/BsC,UAAA;AAAA,QAAA,CAED,GACAK,kCAAA,IAACJ,QAAA;AAAA,UACKC,SAAQ;AAAA,UACRC,MAAK;AAAA,UACLC,SAASA,MACHzC,eAAgBsF,UAAS9D,KAAK6D,IAAIC,OAAO,GAAG/D,UAAU,CAAC;AAAA,UAE7DiE,UAAUzF,gBAAgBwB;AAAAA,UAC/Bc,UAAA;AAAA,QAAA,CAED,CAAA;AAAA,MACN,CAAA,CAAA;AAAA,IACN,CAAA,CAAA;AAAA,EACN,CAAA;AAEZ;"}