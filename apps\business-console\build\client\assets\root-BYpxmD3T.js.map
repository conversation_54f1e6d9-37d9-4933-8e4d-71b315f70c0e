{"version": 3, "file": "root-BYpxmD3T.js", "sources": ["../../../node_modules/@remix-run/react/dist/esm/scroll-restoration.js", "../../../app/tailwind.css?url", "../../../app/components/loader/GlobalSpinnerLoader.tsx", "../../../node_modules/@radix-ui/react-toast/dist/index.mjs", "../../../app/components/ui/toast.tsx", "../../../app/components/ui/toaster.tsx", "../../../app/root.tsx"], "sourcesContent": ["/**\n * @remix-run/react v2.15.3\n *\n * Copyright (c) Remix Software Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE.md file in the root directory of this source tree.\n *\n * @license MIT\n */\nimport { extends as _extends } from './_virtual/_rollupPluginBabelHelpers.js';\nimport * as React from 'react';\nimport { useLocation, useMatches, UNSAFE_useScrollRestoration } from 'react-router-dom';\nimport { useRemixContext } from './components.js';\n\nlet STORAGE_KEY = \"positions\";\n\n/**\n * This component will emulate the browser's scroll restoration on location\n * changes.\n *\n * @see https://remix.run/components/scroll-restoration\n */\nfunction ScrollRestoration({\n  getKey,\n  ...props\n}) {\n  let {\n    isSpaMode\n  } = useRemixContext();\n  let location = useLocation();\n  let matches = useMatches();\n  UNSAFE_useScrollRestoration({\n    getKey,\n    storageKey: STORAGE_KEY\n  });\n\n  // In order to support `get<PERSON><PERSON>`, we need to compute a \"key\" here so we can\n  // hydrate that up so that SSR scroll restoration isn't waiting on <PERSON>act to\n  // hydrate. *However*, our key on the server is not the same as our key on\n  // the client!  So if the user's getKey implementation returns the SSR\n  // location key, then let's ignore it and let our inline <script> below pick\n  // up the client side history state key\n  let key = React.useMemo(() => {\n    if (!getKey) return null;\n    let userKey = getKey(location, matches);\n    return userKey !== location.key ? userKey : null;\n  },\n  // Nah, we only need this the first time for the SSR render\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  []);\n\n  // In SPA Mode, there's nothing to restore on initial render since we didn't\n  // render anything on the server\n  if (isSpaMode) {\n    return null;\n  }\n  let restoreScroll = ((STORAGE_KEY, restoreKey) => {\n    if (!window.history.state || !window.history.state.key) {\n      let key = Math.random().toString(32).slice(2);\n      window.history.replaceState({\n        key\n      }, \"\");\n    }\n    try {\n      let positions = JSON.parse(sessionStorage.getItem(STORAGE_KEY) || \"{}\");\n      let storedY = positions[restoreKey || window.history.state.key];\n      if (typeof storedY === \"number\") {\n        window.scrollTo(0, storedY);\n      }\n    } catch (error) {\n      console.error(error);\n      sessionStorage.removeItem(STORAGE_KEY);\n    }\n  }).toString();\n  return /*#__PURE__*/React.createElement(\"script\", _extends({}, props, {\n    suppressHydrationWarning: true,\n    dangerouslySetInnerHTML: {\n      __html: `(${restoreScroll})(${JSON.stringify(STORAGE_KEY)}, ${JSON.stringify(key)})`\n    }\n  }));\n}\n\nexport { ScrollRestoration };\n", "import \"C:/Users/<USER>/Documents/V Folder/Projects/FM/mnet-comm/apps/business-console/app/tailwind.css?transform-only\";export default \"__VITE_CSS_URL__433a2f55736572732f76617375642f446f63756d656e74732f5620466f6c6465722f50726f6a656374732f464d2f6d6e65742d636f6d6d2f617070732f627573696e6573732d636f6e736f6c652f6170702f7461696c77696e642e6373733f7472616e73666f726d2d6f6e6c79__\"", "import { useNavigation } from \"@remix-run/react\";\r\nimport React from \"react\";\r\n\r\nconst GlobalSpinnerLoader: React.FC = () => {\r\n  const navigation = useNavigation();\r\n\r\n  const active = navigation.state !== \"idle\";\r\n  return (\r\n    active && (\r\n\r\n      <div\r\n        role=\"progressbar\"\r\n        aria-valuetext={active ? \"Loading\" : undefined}\r\n        aria-hidden={!active}\r\n        className=\"fixed inset-0 flex items-center justify-center bg-black bg-opacity-40 pointer-events-none z-50 p-4\"\r\n      >\r\n        <div className=\"p-6 text-center\">\r\n          <div className=\"w-20 h-20 border-4 border-transparent border-t-blue-400 rounded-full animate-spin flex items-center justify-center\">\r\n            <div className=\"w-16 h-16 border-4 border-transparent border-t-red-400 rounded-full animate-spin\"></div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n\r\n      // <div\r\n      //   role=\"progressbar\"\r\n      //   aria-valuetext={active ? \"Loading\" : undefined}\r\n      //   aria-hidden={!active}\r\n      //   className={`fixed inset-0 flex items-center justify-center bg-black bg-opacity-40  pointer-events-none z-50 p-4`}\r\n      // >\r\n      //   <div className=\"p-6 text-center\">\r\n      //     <div className=\"w-16 h-16 border-4 border-gray-200 border-t-4 border-t-primary rounded-full animate-spin mx-auto mb-4\"></div>\r\n      //   </div>\r\n      // </div>\r\n    )\r\n  );\r\n};\r\n\r\nexport default GlobalSpinnerLoader;\r\n", "\"use client\";\n\n// packages/react/toast/src/toast.tsx\nimport * as React from \"react\";\nimport * as ReactDOM from \"react-dom\";\nimport { composeEventHandlers } from \"@radix-ui/primitive\";\nimport { useComposedRefs } from \"@radix-ui/react-compose-refs\";\nimport { createCollection } from \"@radix-ui/react-collection\";\nimport { createContextScope } from \"@radix-ui/react-context\";\nimport * as DismissableLayer from \"@radix-ui/react-dismissable-layer\";\nimport { Portal } from \"@radix-ui/react-portal\";\nimport { Presence } from \"@radix-ui/react-presence\";\nimport { Primitive, dispatchDiscreteCustomEvent } from \"@radix-ui/react-primitive\";\nimport { useCallbackRef } from \"@radix-ui/react-use-callback-ref\";\nimport { useControllableState } from \"@radix-ui/react-use-controllable-state\";\nimport { useLayoutEffect } from \"@radix-ui/react-use-layout-effect\";\nimport { VisuallyHidden } from \"@radix-ui/react-visually-hidden\";\nimport { Fragment, jsx, jsxs } from \"react/jsx-runtime\";\nvar PROVIDER_NAME = \"ToastProvider\";\nvar [Collection, useCollection, createCollectionScope] = createCollection(\"Toast\");\nvar [createToastContext, createToastScope] = createContextScope(\"Toast\", [createCollectionScope]);\nvar [ToastProviderProvider, useToastProviderContext] = createToastContext(PROVIDER_NAME);\nvar ToastProvider = (props) => {\n  const {\n    __scopeToast,\n    label = \"Notification\",\n    duration = 5e3,\n    swipeDirection = \"right\",\n    swipeThreshold = 50,\n    children\n  } = props;\n  const [viewport, setViewport] = React.useState(null);\n  const [toastCount, setToastCount] = React.useState(0);\n  const isFocusedToastEscapeKeyDownRef = React.useRef(false);\n  const isClosePausedRef = React.useRef(false);\n  if (!label.trim()) {\n    console.error(\n      `Invalid prop \\`label\\` supplied to \\`${PROVIDER_NAME}\\`. Expected non-empty \\`string\\`.`\n    );\n  }\n  return /* @__PURE__ */ jsx(Collection.Provider, { scope: __scopeToast, children: /* @__PURE__ */ jsx(\n    ToastProviderProvider,\n    {\n      scope: __scopeToast,\n      label,\n      duration,\n      swipeDirection,\n      swipeThreshold,\n      toastCount,\n      viewport,\n      onViewportChange: setViewport,\n      onToastAdd: React.useCallback(() => setToastCount((prevCount) => prevCount + 1), []),\n      onToastRemove: React.useCallback(() => setToastCount((prevCount) => prevCount - 1), []),\n      isFocusedToastEscapeKeyDownRef,\n      isClosePausedRef,\n      children\n    }\n  ) });\n};\nToastProvider.displayName = PROVIDER_NAME;\nvar VIEWPORT_NAME = \"ToastViewport\";\nvar VIEWPORT_DEFAULT_HOTKEY = [\"F8\"];\nvar VIEWPORT_PAUSE = \"toast.viewportPause\";\nvar VIEWPORT_RESUME = \"toast.viewportResume\";\nvar ToastViewport = React.forwardRef(\n  (props, forwardedRef) => {\n    const {\n      __scopeToast,\n      hotkey = VIEWPORT_DEFAULT_HOTKEY,\n      label = \"Notifications ({hotkey})\",\n      ...viewportProps\n    } = props;\n    const context = useToastProviderContext(VIEWPORT_NAME, __scopeToast);\n    const getItems = useCollection(__scopeToast);\n    const wrapperRef = React.useRef(null);\n    const headFocusProxyRef = React.useRef(null);\n    const tailFocusProxyRef = React.useRef(null);\n    const ref = React.useRef(null);\n    const composedRefs = useComposedRefs(forwardedRef, ref, context.onViewportChange);\n    const hotkeyLabel = hotkey.join(\"+\").replace(/Key/g, \"\").replace(/Digit/g, \"\");\n    const hasToasts = context.toastCount > 0;\n    React.useEffect(() => {\n      const handleKeyDown = (event) => {\n        const isHotkeyPressed = hotkey.length !== 0 && hotkey.every((key) => event[key] || event.code === key);\n        if (isHotkeyPressed) ref.current?.focus();\n      };\n      document.addEventListener(\"keydown\", handleKeyDown);\n      return () => document.removeEventListener(\"keydown\", handleKeyDown);\n    }, [hotkey]);\n    React.useEffect(() => {\n      const wrapper = wrapperRef.current;\n      const viewport = ref.current;\n      if (hasToasts && wrapper && viewport) {\n        const handlePause = () => {\n          if (!context.isClosePausedRef.current) {\n            const pauseEvent = new CustomEvent(VIEWPORT_PAUSE);\n            viewport.dispatchEvent(pauseEvent);\n            context.isClosePausedRef.current = true;\n          }\n        };\n        const handleResume = () => {\n          if (context.isClosePausedRef.current) {\n            const resumeEvent = new CustomEvent(VIEWPORT_RESUME);\n            viewport.dispatchEvent(resumeEvent);\n            context.isClosePausedRef.current = false;\n          }\n        };\n        const handleFocusOutResume = (event) => {\n          const isFocusMovingOutside = !wrapper.contains(event.relatedTarget);\n          if (isFocusMovingOutside) handleResume();\n        };\n        const handlePointerLeaveResume = () => {\n          const isFocusInside = wrapper.contains(document.activeElement);\n          if (!isFocusInside) handleResume();\n        };\n        wrapper.addEventListener(\"focusin\", handlePause);\n        wrapper.addEventListener(\"focusout\", handleFocusOutResume);\n        wrapper.addEventListener(\"pointermove\", handlePause);\n        wrapper.addEventListener(\"pointerleave\", handlePointerLeaveResume);\n        window.addEventListener(\"blur\", handlePause);\n        window.addEventListener(\"focus\", handleResume);\n        return () => {\n          wrapper.removeEventListener(\"focusin\", handlePause);\n          wrapper.removeEventListener(\"focusout\", handleFocusOutResume);\n          wrapper.removeEventListener(\"pointermove\", handlePause);\n          wrapper.removeEventListener(\"pointerleave\", handlePointerLeaveResume);\n          window.removeEventListener(\"blur\", handlePause);\n          window.removeEventListener(\"focus\", handleResume);\n        };\n      }\n    }, [hasToasts, context.isClosePausedRef]);\n    const getSortedTabbableCandidates = React.useCallback(\n      ({ tabbingDirection }) => {\n        const toastItems = getItems();\n        const tabbableCandidates = toastItems.map((toastItem) => {\n          const toastNode = toastItem.ref.current;\n          const toastTabbableCandidates = [toastNode, ...getTabbableCandidates(toastNode)];\n          return tabbingDirection === \"forwards\" ? toastTabbableCandidates : toastTabbableCandidates.reverse();\n        });\n        return (tabbingDirection === \"forwards\" ? tabbableCandidates.reverse() : tabbableCandidates).flat();\n      },\n      [getItems]\n    );\n    React.useEffect(() => {\n      const viewport = ref.current;\n      if (viewport) {\n        const handleKeyDown = (event) => {\n          const isMetaKey = event.altKey || event.ctrlKey || event.metaKey;\n          const isTabKey = event.key === \"Tab\" && !isMetaKey;\n          if (isTabKey) {\n            const focusedElement = document.activeElement;\n            const isTabbingBackwards = event.shiftKey;\n            const targetIsViewport = event.target === viewport;\n            if (targetIsViewport && isTabbingBackwards) {\n              headFocusProxyRef.current?.focus();\n              return;\n            }\n            const tabbingDirection = isTabbingBackwards ? \"backwards\" : \"forwards\";\n            const sortedCandidates = getSortedTabbableCandidates({ tabbingDirection });\n            const index = sortedCandidates.findIndex((candidate) => candidate === focusedElement);\n            if (focusFirst(sortedCandidates.slice(index + 1))) {\n              event.preventDefault();\n            } else {\n              isTabbingBackwards ? headFocusProxyRef.current?.focus() : tailFocusProxyRef.current?.focus();\n            }\n          }\n        };\n        viewport.addEventListener(\"keydown\", handleKeyDown);\n        return () => viewport.removeEventListener(\"keydown\", handleKeyDown);\n      }\n    }, [getItems, getSortedTabbableCandidates]);\n    return /* @__PURE__ */ jsxs(\n      DismissableLayer.Branch,\n      {\n        ref: wrapperRef,\n        role: \"region\",\n        \"aria-label\": label.replace(\"{hotkey}\", hotkeyLabel),\n        tabIndex: -1,\n        style: { pointerEvents: hasToasts ? void 0 : \"none\" },\n        children: [\n          hasToasts && /* @__PURE__ */ jsx(\n            FocusProxy,\n            {\n              ref: headFocusProxyRef,\n              onFocusFromOutsideViewport: () => {\n                const tabbableCandidates = getSortedTabbableCandidates({\n                  tabbingDirection: \"forwards\"\n                });\n                focusFirst(tabbableCandidates);\n              }\n            }\n          ),\n          /* @__PURE__ */ jsx(Collection.Slot, { scope: __scopeToast, children: /* @__PURE__ */ jsx(Primitive.ol, { tabIndex: -1, ...viewportProps, ref: composedRefs }) }),\n          hasToasts && /* @__PURE__ */ jsx(\n            FocusProxy,\n            {\n              ref: tailFocusProxyRef,\n              onFocusFromOutsideViewport: () => {\n                const tabbableCandidates = getSortedTabbableCandidates({\n                  tabbingDirection: \"backwards\"\n                });\n                focusFirst(tabbableCandidates);\n              }\n            }\n          )\n        ]\n      }\n    );\n  }\n);\nToastViewport.displayName = VIEWPORT_NAME;\nvar FOCUS_PROXY_NAME = \"ToastFocusProxy\";\nvar FocusProxy = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeToast, onFocusFromOutsideViewport, ...proxyProps } = props;\n    const context = useToastProviderContext(FOCUS_PROXY_NAME, __scopeToast);\n    return /* @__PURE__ */ jsx(\n      VisuallyHidden,\n      {\n        \"aria-hidden\": true,\n        tabIndex: 0,\n        ...proxyProps,\n        ref: forwardedRef,\n        style: { position: \"fixed\" },\n        onFocus: (event) => {\n          const prevFocusedElement = event.relatedTarget;\n          const isFocusFromOutsideViewport = !context.viewport?.contains(prevFocusedElement);\n          if (isFocusFromOutsideViewport) onFocusFromOutsideViewport();\n        }\n      }\n    );\n  }\n);\nFocusProxy.displayName = FOCUS_PROXY_NAME;\nvar TOAST_NAME = \"Toast\";\nvar TOAST_SWIPE_START = \"toast.swipeStart\";\nvar TOAST_SWIPE_MOVE = \"toast.swipeMove\";\nvar TOAST_SWIPE_CANCEL = \"toast.swipeCancel\";\nvar TOAST_SWIPE_END = \"toast.swipeEnd\";\nvar Toast = React.forwardRef(\n  (props, forwardedRef) => {\n    const { forceMount, open: openProp, defaultOpen, onOpenChange, ...toastProps } = props;\n    const [open = true, setOpen] = useControllableState({\n      prop: openProp,\n      defaultProp: defaultOpen,\n      onChange: onOpenChange\n    });\n    return /* @__PURE__ */ jsx(Presence, { present: forceMount || open, children: /* @__PURE__ */ jsx(\n      ToastImpl,\n      {\n        open,\n        ...toastProps,\n        ref: forwardedRef,\n        onClose: () => setOpen(false),\n        onPause: useCallbackRef(props.onPause),\n        onResume: useCallbackRef(props.onResume),\n        onSwipeStart: composeEventHandlers(props.onSwipeStart, (event) => {\n          event.currentTarget.setAttribute(\"data-swipe\", \"start\");\n        }),\n        onSwipeMove: composeEventHandlers(props.onSwipeMove, (event) => {\n          const { x, y } = event.detail.delta;\n          event.currentTarget.setAttribute(\"data-swipe\", \"move\");\n          event.currentTarget.style.setProperty(\"--radix-toast-swipe-move-x\", `${x}px`);\n          event.currentTarget.style.setProperty(\"--radix-toast-swipe-move-y\", `${y}px`);\n        }),\n        onSwipeCancel: composeEventHandlers(props.onSwipeCancel, (event) => {\n          event.currentTarget.setAttribute(\"data-swipe\", \"cancel\");\n          event.currentTarget.style.removeProperty(\"--radix-toast-swipe-move-x\");\n          event.currentTarget.style.removeProperty(\"--radix-toast-swipe-move-y\");\n          event.currentTarget.style.removeProperty(\"--radix-toast-swipe-end-x\");\n          event.currentTarget.style.removeProperty(\"--radix-toast-swipe-end-y\");\n        }),\n        onSwipeEnd: composeEventHandlers(props.onSwipeEnd, (event) => {\n          const { x, y } = event.detail.delta;\n          event.currentTarget.setAttribute(\"data-swipe\", \"end\");\n          event.currentTarget.style.removeProperty(\"--radix-toast-swipe-move-x\");\n          event.currentTarget.style.removeProperty(\"--radix-toast-swipe-move-y\");\n          event.currentTarget.style.setProperty(\"--radix-toast-swipe-end-x\", `${x}px`);\n          event.currentTarget.style.setProperty(\"--radix-toast-swipe-end-y\", `${y}px`);\n          setOpen(false);\n        })\n      }\n    ) });\n  }\n);\nToast.displayName = TOAST_NAME;\nvar [ToastInteractiveProvider, useToastInteractiveContext] = createToastContext(TOAST_NAME, {\n  onClose() {\n  }\n});\nvar ToastImpl = React.forwardRef(\n  (props, forwardedRef) => {\n    const {\n      __scopeToast,\n      type = \"foreground\",\n      duration: durationProp,\n      open,\n      onClose,\n      onEscapeKeyDown,\n      onPause,\n      onResume,\n      onSwipeStart,\n      onSwipeMove,\n      onSwipeCancel,\n      onSwipeEnd,\n      ...toastProps\n    } = props;\n    const context = useToastProviderContext(TOAST_NAME, __scopeToast);\n    const [node, setNode] = React.useState(null);\n    const composedRefs = useComposedRefs(forwardedRef, (node2) => setNode(node2));\n    const pointerStartRef = React.useRef(null);\n    const swipeDeltaRef = React.useRef(null);\n    const duration = durationProp || context.duration;\n    const closeTimerStartTimeRef = React.useRef(0);\n    const closeTimerRemainingTimeRef = React.useRef(duration);\n    const closeTimerRef = React.useRef(0);\n    const { onToastAdd, onToastRemove } = context;\n    const handleClose = useCallbackRef(() => {\n      const isFocusInToast = node?.contains(document.activeElement);\n      if (isFocusInToast) context.viewport?.focus();\n      onClose();\n    });\n    const startTimer = React.useCallback(\n      (duration2) => {\n        if (!duration2 || duration2 === Infinity) return;\n        window.clearTimeout(closeTimerRef.current);\n        closeTimerStartTimeRef.current = (/* @__PURE__ */ new Date()).getTime();\n        closeTimerRef.current = window.setTimeout(handleClose, duration2);\n      },\n      [handleClose]\n    );\n    React.useEffect(() => {\n      const viewport = context.viewport;\n      if (viewport) {\n        const handleResume = () => {\n          startTimer(closeTimerRemainingTimeRef.current);\n          onResume?.();\n        };\n        const handlePause = () => {\n          const elapsedTime = (/* @__PURE__ */ new Date()).getTime() - closeTimerStartTimeRef.current;\n          closeTimerRemainingTimeRef.current = closeTimerRemainingTimeRef.current - elapsedTime;\n          window.clearTimeout(closeTimerRef.current);\n          onPause?.();\n        };\n        viewport.addEventListener(VIEWPORT_PAUSE, handlePause);\n        viewport.addEventListener(VIEWPORT_RESUME, handleResume);\n        return () => {\n          viewport.removeEventListener(VIEWPORT_PAUSE, handlePause);\n          viewport.removeEventListener(VIEWPORT_RESUME, handleResume);\n        };\n      }\n    }, [context.viewport, duration, onPause, onResume, startTimer]);\n    React.useEffect(() => {\n      if (open && !context.isClosePausedRef.current) startTimer(duration);\n    }, [open, duration, context.isClosePausedRef, startTimer]);\n    React.useEffect(() => {\n      onToastAdd();\n      return () => onToastRemove();\n    }, [onToastAdd, onToastRemove]);\n    const announceTextContent = React.useMemo(() => {\n      return node ? getAnnounceTextContent(node) : null;\n    }, [node]);\n    if (!context.viewport) return null;\n    return /* @__PURE__ */ jsxs(Fragment, { children: [\n      announceTextContent && /* @__PURE__ */ jsx(\n        ToastAnnounce,\n        {\n          __scopeToast,\n          role: \"status\",\n          \"aria-live\": type === \"foreground\" ? \"assertive\" : \"polite\",\n          \"aria-atomic\": true,\n          children: announceTextContent\n        }\n      ),\n      /* @__PURE__ */ jsx(ToastInteractiveProvider, { scope: __scopeToast, onClose: handleClose, children: ReactDOM.createPortal(\n        /* @__PURE__ */ jsx(Collection.ItemSlot, { scope: __scopeToast, children: /* @__PURE__ */ jsx(\n          DismissableLayer.Root,\n          {\n            asChild: true,\n            onEscapeKeyDown: composeEventHandlers(onEscapeKeyDown, () => {\n              if (!context.isFocusedToastEscapeKeyDownRef.current) handleClose();\n              context.isFocusedToastEscapeKeyDownRef.current = false;\n            }),\n            children: /* @__PURE__ */ jsx(\n              Primitive.li,\n              {\n                role: \"status\",\n                \"aria-live\": \"off\",\n                \"aria-atomic\": true,\n                tabIndex: 0,\n                \"data-state\": open ? \"open\" : \"closed\",\n                \"data-swipe-direction\": context.swipeDirection,\n                ...toastProps,\n                ref: composedRefs,\n                style: { userSelect: \"none\", touchAction: \"none\", ...props.style },\n                onKeyDown: composeEventHandlers(props.onKeyDown, (event) => {\n                  if (event.key !== \"Escape\") return;\n                  onEscapeKeyDown?.(event.nativeEvent);\n                  if (!event.nativeEvent.defaultPrevented) {\n                    context.isFocusedToastEscapeKeyDownRef.current = true;\n                    handleClose();\n                  }\n                }),\n                onPointerDown: composeEventHandlers(props.onPointerDown, (event) => {\n                  if (event.button !== 0) return;\n                  pointerStartRef.current = { x: event.clientX, y: event.clientY };\n                }),\n                onPointerMove: composeEventHandlers(props.onPointerMove, (event) => {\n                  if (!pointerStartRef.current) return;\n                  const x = event.clientX - pointerStartRef.current.x;\n                  const y = event.clientY - pointerStartRef.current.y;\n                  const hasSwipeMoveStarted = Boolean(swipeDeltaRef.current);\n                  const isHorizontalSwipe = [\"left\", \"right\"].includes(context.swipeDirection);\n                  const clamp = [\"left\", \"up\"].includes(context.swipeDirection) ? Math.min : Math.max;\n                  const clampedX = isHorizontalSwipe ? clamp(0, x) : 0;\n                  const clampedY = !isHorizontalSwipe ? clamp(0, y) : 0;\n                  const moveStartBuffer = event.pointerType === \"touch\" ? 10 : 2;\n                  const delta = { x: clampedX, y: clampedY };\n                  const eventDetail = { originalEvent: event, delta };\n                  if (hasSwipeMoveStarted) {\n                    swipeDeltaRef.current = delta;\n                    handleAndDispatchCustomEvent(TOAST_SWIPE_MOVE, onSwipeMove, eventDetail, {\n                      discrete: false\n                    });\n                  } else if (isDeltaInDirection(delta, context.swipeDirection, moveStartBuffer)) {\n                    swipeDeltaRef.current = delta;\n                    handleAndDispatchCustomEvent(TOAST_SWIPE_START, onSwipeStart, eventDetail, {\n                      discrete: false\n                    });\n                    event.target.setPointerCapture(event.pointerId);\n                  } else if (Math.abs(x) > moveStartBuffer || Math.abs(y) > moveStartBuffer) {\n                    pointerStartRef.current = null;\n                  }\n                }),\n                onPointerUp: composeEventHandlers(props.onPointerUp, (event) => {\n                  const delta = swipeDeltaRef.current;\n                  const target = event.target;\n                  if (target.hasPointerCapture(event.pointerId)) {\n                    target.releasePointerCapture(event.pointerId);\n                  }\n                  swipeDeltaRef.current = null;\n                  pointerStartRef.current = null;\n                  if (delta) {\n                    const toast = event.currentTarget;\n                    const eventDetail = { originalEvent: event, delta };\n                    if (isDeltaInDirection(delta, context.swipeDirection, context.swipeThreshold)) {\n                      handleAndDispatchCustomEvent(TOAST_SWIPE_END, onSwipeEnd, eventDetail, {\n                        discrete: true\n                      });\n                    } else {\n                      handleAndDispatchCustomEvent(\n                        TOAST_SWIPE_CANCEL,\n                        onSwipeCancel,\n                        eventDetail,\n                        {\n                          discrete: true\n                        }\n                      );\n                    }\n                    toast.addEventListener(\"click\", (event2) => event2.preventDefault(), {\n                      once: true\n                    });\n                  }\n                })\n              }\n            )\n          }\n        ) }),\n        context.viewport\n      ) })\n    ] });\n  }\n);\nvar ToastAnnounce = (props) => {\n  const { __scopeToast, children, ...announceProps } = props;\n  const context = useToastProviderContext(TOAST_NAME, __scopeToast);\n  const [renderAnnounceText, setRenderAnnounceText] = React.useState(false);\n  const [isAnnounced, setIsAnnounced] = React.useState(false);\n  useNextFrame(() => setRenderAnnounceText(true));\n  React.useEffect(() => {\n    const timer = window.setTimeout(() => setIsAnnounced(true), 1e3);\n    return () => window.clearTimeout(timer);\n  }, []);\n  return isAnnounced ? null : /* @__PURE__ */ jsx(Portal, { asChild: true, children: /* @__PURE__ */ jsx(VisuallyHidden, { ...announceProps, children: renderAnnounceText && /* @__PURE__ */ jsxs(Fragment, { children: [\n    context.label,\n    \" \",\n    children\n  ] }) }) });\n};\nvar TITLE_NAME = \"ToastTitle\";\nvar ToastTitle = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeToast, ...titleProps } = props;\n    return /* @__PURE__ */ jsx(Primitive.div, { ...titleProps, ref: forwardedRef });\n  }\n);\nToastTitle.displayName = TITLE_NAME;\nvar DESCRIPTION_NAME = \"ToastDescription\";\nvar ToastDescription = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeToast, ...descriptionProps } = props;\n    return /* @__PURE__ */ jsx(Primitive.div, { ...descriptionProps, ref: forwardedRef });\n  }\n);\nToastDescription.displayName = DESCRIPTION_NAME;\nvar ACTION_NAME = \"ToastAction\";\nvar ToastAction = React.forwardRef(\n  (props, forwardedRef) => {\n    const { altText, ...actionProps } = props;\n    if (!altText.trim()) {\n      console.error(\n        `Invalid prop \\`altText\\` supplied to \\`${ACTION_NAME}\\`. Expected non-empty \\`string\\`.`\n      );\n      return null;\n    }\n    return /* @__PURE__ */ jsx(ToastAnnounceExclude, { altText, asChild: true, children: /* @__PURE__ */ jsx(ToastClose, { ...actionProps, ref: forwardedRef }) });\n  }\n);\nToastAction.displayName = ACTION_NAME;\nvar CLOSE_NAME = \"ToastClose\";\nvar ToastClose = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeToast, ...closeProps } = props;\n    const interactiveContext = useToastInteractiveContext(CLOSE_NAME, __scopeToast);\n    return /* @__PURE__ */ jsx(ToastAnnounceExclude, { asChild: true, children: /* @__PURE__ */ jsx(\n      Primitive.button,\n      {\n        type: \"button\",\n        ...closeProps,\n        ref: forwardedRef,\n        onClick: composeEventHandlers(props.onClick, interactiveContext.onClose)\n      }\n    ) });\n  }\n);\nToastClose.displayName = CLOSE_NAME;\nvar ToastAnnounceExclude = React.forwardRef((props, forwardedRef) => {\n  const { __scopeToast, altText, ...announceExcludeProps } = props;\n  return /* @__PURE__ */ jsx(\n    Primitive.div,\n    {\n      \"data-radix-toast-announce-exclude\": \"\",\n      \"data-radix-toast-announce-alt\": altText || void 0,\n      ...announceExcludeProps,\n      ref: forwardedRef\n    }\n  );\n});\nfunction getAnnounceTextContent(container) {\n  const textContent = [];\n  const childNodes = Array.from(container.childNodes);\n  childNodes.forEach((node) => {\n    if (node.nodeType === node.TEXT_NODE && node.textContent) textContent.push(node.textContent);\n    if (isHTMLElement(node)) {\n      const isHidden = node.ariaHidden || node.hidden || node.style.display === \"none\";\n      const isExcluded = node.dataset.radixToastAnnounceExclude === \"\";\n      if (!isHidden) {\n        if (isExcluded) {\n          const altText = node.dataset.radixToastAnnounceAlt;\n          if (altText) textContent.push(altText);\n        } else {\n          textContent.push(...getAnnounceTextContent(node));\n        }\n      }\n    }\n  });\n  return textContent;\n}\nfunction handleAndDispatchCustomEvent(name, handler, detail, { discrete }) {\n  const currentTarget = detail.originalEvent.currentTarget;\n  const event = new CustomEvent(name, { bubbles: true, cancelable: true, detail });\n  if (handler) currentTarget.addEventListener(name, handler, { once: true });\n  if (discrete) {\n    dispatchDiscreteCustomEvent(currentTarget, event);\n  } else {\n    currentTarget.dispatchEvent(event);\n  }\n}\nvar isDeltaInDirection = (delta, direction, threshold = 0) => {\n  const deltaX = Math.abs(delta.x);\n  const deltaY = Math.abs(delta.y);\n  const isDeltaX = deltaX > deltaY;\n  if (direction === \"left\" || direction === \"right\") {\n    return isDeltaX && deltaX > threshold;\n  } else {\n    return !isDeltaX && deltaY > threshold;\n  }\n};\nfunction useNextFrame(callback = () => {\n}) {\n  const fn = useCallbackRef(callback);\n  useLayoutEffect(() => {\n    let raf1 = 0;\n    let raf2 = 0;\n    raf1 = window.requestAnimationFrame(() => raf2 = window.requestAnimationFrame(fn));\n    return () => {\n      window.cancelAnimationFrame(raf1);\n      window.cancelAnimationFrame(raf2);\n    };\n  }, [fn]);\n}\nfunction isHTMLElement(node) {\n  return node.nodeType === node.ELEMENT_NODE;\n}\nfunction getTabbableCandidates(container) {\n  const nodes = [];\n  const walker = document.createTreeWalker(container, NodeFilter.SHOW_ELEMENT, {\n    acceptNode: (node) => {\n      const isHiddenInput = node.tagName === \"INPUT\" && node.type === \"hidden\";\n      if (node.disabled || node.hidden || isHiddenInput) return NodeFilter.FILTER_SKIP;\n      return node.tabIndex >= 0 ? NodeFilter.FILTER_ACCEPT : NodeFilter.FILTER_SKIP;\n    }\n  });\n  while (walker.nextNode()) nodes.push(walker.currentNode);\n  return nodes;\n}\nfunction focusFirst(candidates) {\n  const previouslyFocusedElement = document.activeElement;\n  return candidates.some((candidate) => {\n    if (candidate === previouslyFocusedElement) return true;\n    candidate.focus();\n    return document.activeElement !== previouslyFocusedElement;\n  });\n}\nvar Provider = ToastProvider;\nvar Viewport = ToastViewport;\nvar Root2 = Toast;\nvar Title = ToastTitle;\nvar Description = ToastDescription;\nvar Action = ToastAction;\nvar Close = ToastClose;\nexport {\n  Action,\n  Close,\n  Description,\n  Provider,\n  Root2 as Root,\n  Title,\n  Toast,\n  ToastAction,\n  ToastClose,\n  ToastDescription,\n  ToastProvider,\n  ToastTitle,\n  ToastViewport,\n  Viewport,\n  createToastScope\n};\n//# sourceMappingURL=index.mjs.map\n", "import * as React from \"react\"\r\nimport * as ToastPrimitives from \"@radix-ui/react-toast\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\nimport { X } from \"lucide-react\"\r\n\r\nimport { cn } from \"~/lib/utils\"\r\n\r\nconst ToastProvider = ToastPrimitives.Provider\r\n\r\nconst ToastViewport = React.forwardRef<\r\n  React.ElementRef<typeof ToastPrimitives.Viewport>,\r\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Viewport>\r\n>(({ className, ...props }, ref) => (\r\n  <ToastPrimitives.Viewport\r\n    ref={ref}\r\n    className={cn(\r\n      \"fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nToastViewport.displayName = ToastPrimitives.Viewport.displayName\r\n\r\nconst toastVariants = cva(\r\n  \"group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: \"border bg-background text-foreground\",\r\n        destructive:\r\n          \"destructive group border-destructive bg-destructive text-destructive-foreground\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nconst Toast = React.forwardRef<\r\n  React.ElementRef<typeof ToastPrimitives.Root>,\r\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Root> &\r\n    VariantProps<typeof toastVariants>\r\n>(({ className, variant, ...props }, ref) => {\r\n  return (\r\n    <ToastPrimitives.Root\r\n      ref={ref}\r\n      className={cn(toastVariants({ variant }), className)}\r\n      {...props}\r\n    />\r\n  )\r\n})\r\nToast.displayName = ToastPrimitives.Root.displayName\r\n\r\nconst ToastAction = React.forwardRef<\r\n  React.ElementRef<typeof ToastPrimitives.Action>,\r\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Action>\r\n>(({ className, ...props }, ref) => (\r\n  <ToastPrimitives.Action\r\n    ref={ref}\r\n    className={cn(\r\n      \"inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nToastAction.displayName = ToastPrimitives.Action.displayName\r\n\r\nconst ToastClose = React.forwardRef<\r\n  React.ElementRef<typeof ToastPrimitives.Close>,\r\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Close>\r\n>(({ className, ...props }, ref) => (\r\n  <ToastPrimitives.Close\r\n    ref={ref}\r\n    className={cn(\r\n      \"absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600\",\r\n      className\r\n    )}\r\n    toast-close=\"\"\r\n    {...props}\r\n  >\r\n    <X className=\"h-4 w-4\" />\r\n  </ToastPrimitives.Close>\r\n))\r\nToastClose.displayName = ToastPrimitives.Close.displayName\r\n\r\nconst ToastTitle = React.forwardRef<\r\n  React.ElementRef<typeof ToastPrimitives.Title>,\r\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Title>\r\n>(({ className, ...props }, ref) => (\r\n  <ToastPrimitives.Title\r\n    ref={ref}\r\n    className={cn(\"text-sm font-semibold\", className)}\r\n    {...props}\r\n  />\r\n))\r\nToastTitle.displayName = ToastPrimitives.Title.displayName\r\n\r\nconst ToastDescription = React.forwardRef<\r\n  React.ElementRef<typeof ToastPrimitives.Description>,\r\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Description>\r\n>(({ className, ...props }, ref) => (\r\n  <ToastPrimitives.Description\r\n    ref={ref}\r\n    className={cn(\"text-sm opacity-90\", className)}\r\n    {...props}\r\n  />\r\n))\r\nToastDescription.displayName = ToastPrimitives.Description.displayName\r\n\r\ntype ToastProps = React.ComponentPropsWithoutRef<typeof Toast>\r\n\r\ntype ToastActionElement = React.ReactElement<typeof ToastAction>\r\n\r\nexport {\r\n  type ToastProps,\r\n  type ToastActionElement,\r\n  ToastProvider,\r\n  ToastViewport,\r\n  Toast,\r\n  ToastTitle,\r\n  ToastDescription,\r\n  ToastClose,\r\n  ToastAction,\r\n}\r\n", "import { useToast } from \"~/hooks/use-toast\"\r\nimport {\r\n  Toast,\r\n  ToastClose,\r\n  ToastDescription,\r\n  ToastProvider,\r\n  ToastTitle,\r\n  ToastViewport,\r\n} from \"~/components/ui/toast\"\r\n\r\nexport function Toaster() {\r\n  const { toasts } = useToast()\r\n\r\n  return (\r\n    <ToastProvider>\r\n      {toasts.map(function ({ id, title, description, action, ...props }) {\r\n        return (\r\n          <Toast key={id} {...props}>\r\n            <div className=\"grid gap-1\">\r\n              {title && <ToastTitle>{title}</ToastTitle>}\r\n              {description && (\r\n                <ToastDescription>{description}</ToastDescription>\r\n              )}\r\n            </div>\r\n            {action}\r\n            <ToastClose />\r\n          </Toast>\r\n        )\r\n      })}\r\n      <ToastViewport />\r\n    </ToastProvider>\r\n  )\r\n}\r\n", "import {\r\n  j<PERSON>,\r\n  <PERSON><PERSON>,\r\n  <PERSON><PERSON>,\r\n  <PERSON><PERSON>,\r\n  <PERSON><PERSON><PERSON>,\r\n  ScrollRestoration,\r\n  useLocation,\r\n  useRouteLoaderData,\r\n} from \"@remix-run/react\";\r\nimport type { LinksFunction } from \"@remix-run/node\";\r\nimport styles from \"./tailwind.css?url\"\r\nimport GlobalSpinnerLoader from \"./components/loader/GlobalSpinnerLoader\";\r\nimport ErrorBoundary from \"./components/error/ErrorBoundary\";\r\nimport { ToastProvider } from \"./components/ui/ToastProvider\";\r\nimport { Toaster } from \"./components/ui/toaster\";\r\nimport { handleBuyerAppAutoLogin } from \"./utils/auth.server\";\r\nimport { getNetworkTheme } from \"./services/netWorks\";\r\nimport { NetworkTheme } from \"./types/api/common\";\r\nimport { useNavigate } from \"@remix-run/react\";\r\nimport { useEffect } from \"react\";\r\nimport { themeCache, extractUserIdFromToken, generateCacheKey } from \"./utils/cache\";\r\nimport { getSession } from \"./utils/session.server\";\r\n\r\nexport const links: LinksFunction = () => [\r\n  { rel: \"stylesheet\", href: styles },\r\n  { rel: \"preconnect\", href: \"https://fonts.googleapis.com\" },\r\n  {\r\n    rel: \"preconnect\",\r\n    href: \"https://fonts.gstatic.com\",\r\n    crossOrigin: \"anonymous\",\r\n  },\r\n  {\r\n    rel: \"stylesheet\",\r\n    href: \"https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap\",\r\n  },\r\n  { \r\n    rel: \"stylesheet\",\r\n    href: \"https://fonts.googleapis.com/css2?family=Nunito:ital,wght@0,200..1000;1,200..1000&display=swap\",\r\n  },\r\n  {\r\n    rel: \"icon\",\r\n    type: \"image/png\",\r\n    sizes: \"192x192\",\r\n    href: \"https://ik.imagekit.io/u7uktwxu0/Business%20Icons/fevicon-business%20console.svg\",\r\n  },\r\n  {\r\n    rel: \"icon\",\r\n    type: \"image/png\",\r\n    sizes: \"512x512\",\r\n    href: \"https://ik.imagekit.io/u7uktwxu0/Business%20Icons/fevicon-business%20console.svg\",\r\n  },\r\n];\r\n\r\ninterface LoaderData {\r\n  theme:  \"light\";\r\n  sellerDomainConfig: NetworkTheme;\r\n}\r\n\r\nexport const loader = async ({ request }: { request: Request }) => {\r\n  const theme = request?.headers?.get(\"sec-ch-prefers-color-scheme\") || \"light\";\r\n  const buyerAppLoginResult = await handleBuyerAppAutoLogin(request);\r\n  if (buyerAppLoginResult) {\r\n    return buyerAppLoginResult;\r\n  }\r\n  \r\n  // Extract userId from session for secure cache key\r\n  const session = await getSession(request.headers.get(\"Cookie\"));\r\n  const token = session.get(\"access_token\");\r\n  const userId = extractUserIdFromToken(token);\r\n  \r\n  // Generate secure cache key using domain and userId\r\n  const url = new URL(request.url);\r\n  const cacheKey = generateCacheKey(url.hostname, userId);\r\n  \r\n  try {\r\n    const sellerDomainConfig = await themeCache.getOrSet(\r\n      cacheKey,\r\n      async () => {\r\n        console.log(\"Fetching fresh theme data from API\");\r\n        const response = await getNetworkTheme(request);\r\n        if (!response.data) {\r\n          throw new Error(\"No theme data received from API\");\r\n        }\r\n        return response.data;\r\n      },\r\n      15 * 60 * 1000 // 15 minutes TTL\r\n    );\r\n\r\n    console.log(sellerDomainConfig, \"sellerDomainConfig\");\r\n    \r\n    return json({ \r\n      theme, \r\n      sellerDomainConfig \r\n    });\r\n  } catch (error) {\r\n    console.log(\"Error fetching theme data:\", error);\r\n    \r\n    // Return default theme data\r\n    return json({ \r\n      theme,\r\n      sellerDomainConfig: {} as NetworkTheme\r\n    });\r\n  }\r\n};\r\n\r\nexport function Layout({ children }: { children: React.ReactNode }) {\r\n  const navigate = useNavigate();\r\n  const loader = useRouteLoaderData<LoaderData>(\"root\");  \r\n  const location = useLocation();\r\n\r\n  const theme = loader?.theme || \"light\";\r\n  const sellerDomainConfig = loader?.sellerDomainConfig;\r\n\r\n  useEffect(() => {\r\n    if(sellerDomainConfig?.networkType === \"B2C\" \r\n      && sellerDomainConfig?.ondcDomain === \"RET11\" \r\n      && (location.pathname === \"/home\" \r\n        || location.pathname === \"/\" \r\n        || location.pathname === \"/home/<USER>\" \r\n        || location.pathname === \"/home/<USER>/dashboard\")){\r\n      navigate(\"/sellerSetting\");\r\n      return;\r\n    }\r\n  }, []);\r\n  \r\n  return (\r\n    <html lang=\"en\" className={theme}>\r\n      <head>\r\n        <meta charSet=\"utf-8\" />\r\n        <meta name=\"viewport\" content=\"width=device-width, initial-scale=1\" />\r\n        <Meta />\r\n        <Links />\r\n      </head>\r\n      <body className={theme === \"light\" ? \"bg-white text-black\" : \"bg-white text-black\"}>\r\n        <GlobalSpinnerLoader />\r\n        <ToastProvider>\r\n          {children}\r\n        </ToastProvider>\r\n        <Toaster />\r\n        <ScrollRestoration />\r\n        <Scripts />\r\n      </body>\r\n    </html>\r\n  );\r\n}\r\n\r\nexport default function App() {\r\n  return <>\r\n    <Outlet />\r\n\r\n  </>\r\n}\r\n\r\n\r\nexport { ErrorBoundary };\r\n\r\n"], "names": ["UNSAFE_useScrollRestoration", "React.useMemo", "STORAGE_KEY", "key", "React.createElement", "jsx", "ToastProvider", "React.useState", "React.useRef", "React.useCallback", "ToastViewport", "React.forwardRef", "React.useEffect", "jsxs", "DismissableLayer.Branch", "Toast", "Fragment", "ReactDOM.createPortal", "DismissableLayer.Root", "ToastTitle", "ToastDescription", "ToastAction", "ToastClose", "useLayoutEffect", "ToastPrimitives.Provider", "ToastPrimitives.Viewport", "ToastPrimitives.Root", "ToastPrimitives.Action", "ToastPrimitives.Close", "ToastPrimitives.Title", "ToastPrimitives.Description", "links", "rel", "href", "styles", "crossOrigin", "type", "sizes", "Layout", "children", "navigate", "useNavigate", "loader", "useRouteLoaderData", "location", "useLocation", "theme", "sellerDomainConfig", "useEffect", "networkType", "ondcDomain", "pathname", "lang", "className", "charSet", "name", "content", "Meta", "Links", "GlobalSpinnerLoader", "Toaster", "ScrollRestoration", "<PERSON><PERSON><PERSON>", "App", "Outlet"], "mappings": ";;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAeA,IAAI,cAAc;AAQlB,SAAS,kBAAkB;AAAA,EACzB;AAAA,EACA,GAAG;AACL,GAAG;AACD,MAAI;AAAA,IACF;AAAA,EACD,IAAG,gBAAiB;AACrB,MAAI,WAAW,YAAa;AAC5B,MAAI,UAAU,WAAY;AAC1BA,uBAA4B;AAAA,IAC1B;AAAA,IACA,YAAY;AAAA,EAChB,CAAG;AAQD,MAAI,MAAMC,aAAAA;AAAAA,IAAc,MAAM;AAC5B,UAAI,CAAC,OAAQ,QAAO;AACpB,UAAI,UAAU,OAAO,UAAU,OAAO;AACtC,aAAO,YAAY,SAAS,MAAM,UAAU;AAAA,IAC7C;AAAA;AAAA;AAAA,IAGD;EAAE;AAIF,MAAI,WAAW;AACb,WAAO;AAAA,EACX;AACE,MAAI,iBAAiB,CAACC,cAAa,eAAe;AAChD,QAAI,CAAC,OAAO,QAAQ,SAAS,CAAC,OAAO,QAAQ,MAAM,KAAK;AACtD,UAAIC,OAAM,KAAK,OAAQ,EAAC,SAAS,EAAE,EAAE,MAAM,CAAC;AAC5C,aAAO,QAAQ,aAAa;AAAA,QAC1B,KAAAA;AAAA,MACD,GAAE,EAAE;AAAA,IACX;AACI,QAAI;AACF,UAAI,YAAY,KAAK,MAAM,eAAe,QAAQD,YAAW,KAAK,IAAI;AACtE,UAAI,UAAU,UAAU,cAAc,OAAO,QAAQ,MAAM,GAAG;AAC9D,UAAI,OAAO,YAAY,UAAU;AAC/B,eAAO,SAAS,GAAG,OAAO;AAAA,MAClC;AAAA,IACK,SAAQ,OAAO;AACd,cAAQ,MAAM,KAAK;AACnB,qBAAe,WAAWA,YAAW;AAAA,IAC3C;AAAA,EACG,GAAE,SAAU;AACb,SAAoBE,6BAAmB,cAAC,UAAU,SAAS,CAAA,GAAI,OAAO;AAAA,IACpE,0BAA0B;AAAA,IAC1B,yBAAyB;AAAA,MACvB,QAAQ,IAAI,aAAa,KAAK,KAAK,UAAU,WAAW,CAAC,KAAK,KAAK,UAAU,GAAG,CAAC;AAAA,IACvF;AAAA,EACA,CAAG,CAAC;AACJ;ACjFuH,MAAe,SAAA;ACGtI,MAAM,sBAAgC,MAAM;AAC1C,QAAM,aAAa,cAAc;AAE3B,QAAA,SAAS,WAAW,UAAU;AACpC,SACE,UAEEC,kCAAA;AAAA,IAAC;AAAA,IAAA;AAAA,MACC,MAAK;AAAA,MACL,kBAAgB,SAAS,YAAY;AAAA,MACrC,eAAa,CAAC;AAAA,MACd,WAAU;AAAA,MAEV,UAACA,kCAAAA,IAAA,OAAA,EAAI,WAAU,mBACb,UAACA,kCAAA,IAAA,OAAA,EAAI,WAAU,sHACb,UAACA,kCAAAA,IAAA,OAAA,EAAI,WAAU,mFAAA,CAAmF,GACpG,EACF,CAAA;AAAA,IAAA;AAAA,EACF;AAeN;AClBA,IAAI,gBAAgB;AACpB,IAAI,CAAC,YAAY,eAAe,qBAAqB,IAAI,iBAAiB,OAAO;AACjF,IAAI,CAAC,oBAAoB,gBAAgB,IAAI,mBAAmB,SAAS,CAAC,qBAAqB,CAAC;AAChG,IAAI,CAAC,uBAAuB,uBAAuB,IAAI,mBAAmB,aAAa;AACvF,IAAIC,kBAAgB,CAAC,UAAU;AAC7B,QAAM;AAAA,IACJ;AAAA,IACA,QAAQ;AAAA,IACR,WAAW;AAAA,IACX,iBAAiB;AAAA,IACjB,iBAAiB;AAAA,IACjB;AAAA,EACJ,IAAM;AACJ,QAAM,CAAC,UAAU,WAAW,IAAIC,aAAAA,SAAe,IAAI;AACnD,QAAM,CAAC,YAAY,aAAa,IAAIA,aAAAA,SAAe,CAAC;AACpD,QAAM,iCAAiCC,aAAY,OAAC,KAAK;AACzD,QAAM,mBAAmBA,aAAY,OAAC,KAAK;AAC3C,MAAI,CAAC,MAAM,QAAQ;AACjB,YAAQ;AAAA,MACN,wCAAwC,aAAa;AAAA,IACtD;AAAA,EACL;AACE,SAAuBH,kCAAAA,IAAI,WAAW,UAAU,EAAE,OAAO,cAAc,UAA0BA,kCAAG;AAAA,IAClG;AAAA,IACA;AAAA,MACE,OAAO;AAAA,MACP;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,kBAAkB;AAAA,MAClB,YAAYI,aAAAA,YAAkB,MAAM,cAAc,CAAC,cAAc,YAAY,CAAC,GAAG,EAAE;AAAA,MACnF,eAAeA,aAAAA,YAAkB,MAAM,cAAc,CAAC,cAAc,YAAY,CAAC,GAAG,EAAE;AAAA,MACtF;AAAA,MACA;AAAA,MACA;AAAA,IACN;AAAA,EACA,GAAK;AACL;AACAH,gBAAc,cAAc;AAC5B,IAAI,gBAAgB;AACpB,IAAI,0BAA0B,CAAC,IAAI;AACnC,IAAI,iBAAiB;AACrB,IAAI,kBAAkB;AACtB,IAAII,kBAAgBC,aAAgB;AAAA,EAClC,CAAC,OAAO,iBAAiB;AACvB,UAAM;AAAA,MACJ;AAAA,MACA,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,GAAG;AAAA,IACT,IAAQ;AACJ,UAAM,UAAU,wBAAwB,eAAe,YAAY;AACnE,UAAM,WAAW,cAAc,YAAY;AAC3C,UAAM,aAAaH,aAAY,OAAC,IAAI;AACpC,UAAM,oBAAoBA,aAAY,OAAC,IAAI;AAC3C,UAAM,oBAAoBA,aAAY,OAAC,IAAI;AAC3C,UAAM,MAAMA,aAAY,OAAC,IAAI;AAC7B,UAAM,eAAe,gBAAgB,cAAc,KAAK,QAAQ,gBAAgB;AAChF,UAAM,cAAc,OAAO,KAAK,GAAG,EAAE,QAAQ,QAAQ,EAAE,EAAE,QAAQ,UAAU,EAAE;AAC7E,UAAM,YAAY,QAAQ,aAAa;AACvCI,iBAAAA,UAAgB,MAAM;AACpB,YAAM,gBAAgB,CAAC,UAAU;;AAC/B,cAAM,kBAAkB,OAAO,WAAW,KAAK,OAAO,MAAM,CAAC,QAAQ,MAAM,GAAG,KAAK,MAAM,SAAS,GAAG;AACrG,YAAI,gBAAiB,WAAI,YAAJ,mBAAa;AAAA,MACnC;AACD,eAAS,iBAAiB,WAAW,aAAa;AAClD,aAAO,MAAM,SAAS,oBAAoB,WAAW,aAAa;AAAA,IACxE,GAAO,CAAC,MAAM,CAAC;AACXA,iBAAAA,UAAgB,MAAM;AACpB,YAAM,UAAU,WAAW;AAC3B,YAAM,WAAW,IAAI;AACrB,UAAI,aAAa,WAAW,UAAU;AACpC,cAAM,cAAc,MAAM;AACxB,cAAI,CAAC,QAAQ,iBAAiB,SAAS;AACrC,kBAAM,aAAa,IAAI,YAAY,cAAc;AACjD,qBAAS,cAAc,UAAU;AACjC,oBAAQ,iBAAiB,UAAU;AAAA,UAC/C;AAAA,QACS;AACD,cAAM,eAAe,MAAM;AACzB,cAAI,QAAQ,iBAAiB,SAAS;AACpC,kBAAM,cAAc,IAAI,YAAY,eAAe;AACnD,qBAAS,cAAc,WAAW;AAClC,oBAAQ,iBAAiB,UAAU;AAAA,UAC/C;AAAA,QACS;AACD,cAAM,uBAAuB,CAAC,UAAU;AACtC,gBAAM,uBAAuB,CAAC,QAAQ,SAAS,MAAM,aAAa;AAClE,cAAI,qBAAsB,cAAc;AAAA,QACzC;AACD,cAAM,2BAA2B,MAAM;AACrC,gBAAM,gBAAgB,QAAQ,SAAS,SAAS,aAAa;AAC7D,cAAI,CAAC,cAAe,cAAc;AAAA,QACnC;AACD,gBAAQ,iBAAiB,WAAW,WAAW;AAC/C,gBAAQ,iBAAiB,YAAY,oBAAoB;AACzD,gBAAQ,iBAAiB,eAAe,WAAW;AACnD,gBAAQ,iBAAiB,gBAAgB,wBAAwB;AACjE,eAAO,iBAAiB,QAAQ,WAAW;AAC3C,eAAO,iBAAiB,SAAS,YAAY;AAC7C,eAAO,MAAM;AACX,kBAAQ,oBAAoB,WAAW,WAAW;AAClD,kBAAQ,oBAAoB,YAAY,oBAAoB;AAC5D,kBAAQ,oBAAoB,eAAe,WAAW;AACtD,kBAAQ,oBAAoB,gBAAgB,wBAAwB;AACpE,iBAAO,oBAAoB,QAAQ,WAAW;AAC9C,iBAAO,oBAAoB,SAAS,YAAY;AAAA,QACjD;AAAA,MACT;AAAA,IACK,GAAE,CAAC,WAAW,QAAQ,gBAAgB,CAAC;AACxC,UAAM,8BAA8BH,aAAiB;AAAA,MACnD,CAAC,EAAE,iBAAgB,MAAO;AACxB,cAAM,aAAa,SAAU;AAC7B,cAAM,qBAAqB,WAAW,IAAI,CAAC,cAAc;AACvD,gBAAM,YAAY,UAAU,IAAI;AAChC,gBAAM,0BAA0B,CAAC,WAAW,GAAG,sBAAsB,SAAS,CAAC;AAC/E,iBAAO,qBAAqB,aAAa,0BAA0B,wBAAwB,QAAS;AAAA,QAC9G,CAAS;AACD,gBAAQ,qBAAqB,aAAa,mBAAmB,QAAS,IAAG,oBAAoB,KAAM;AAAA,MACpG;AAAA,MACD,CAAC,QAAQ;AAAA,IACV;AACDG,iBAAAA,UAAgB,MAAM;AACpB,YAAM,WAAW,IAAI;AACrB,UAAI,UAAU;AACZ,cAAM,gBAAgB,CAAC,UAAU;;AAC/B,gBAAM,YAAY,MAAM,UAAU,MAAM,WAAW,MAAM;AACzD,gBAAM,WAAW,MAAM,QAAQ,SAAS,CAAC;AACzC,cAAI,UAAU;AACZ,kBAAM,iBAAiB,SAAS;AAChC,kBAAM,qBAAqB,MAAM;AACjC,kBAAM,mBAAmB,MAAM,WAAW;AAC1C,gBAAI,oBAAoB,oBAAoB;AAC1C,sCAAkB,YAAlB,mBAA2B;AAC3B;AAAA,YACd;AACY,kBAAM,mBAAmB,qBAAqB,cAAc;AAC5D,kBAAM,mBAAmB,4BAA4B,EAAE,kBAAkB;AACzE,kBAAM,QAAQ,iBAAiB,UAAU,CAAC,cAAc,cAAc,cAAc;AACpF,gBAAI,WAAW,iBAAiB,MAAM,QAAQ,CAAC,CAAC,GAAG;AACjD,oBAAM,eAAgB;AAAA,YACpC,OAAmB;AACL,oCAAqB,uBAAkB,YAAlB,mBAA2B,WAAU,uBAAkB,YAAlB,mBAA2B;AAAA,YACnG;AAAA,UACA;AAAA,QACS;AACD,iBAAS,iBAAiB,WAAW,aAAa;AAClD,eAAO,MAAM,SAAS,oBAAoB,WAAW,aAAa;AAAA,MAC1E;AAAA,IACA,GAAO,CAAC,UAAU,2BAA2B,CAAC;AAC1C,WAAuBC,kCAAI;AAAA,MACzBC;AAAAA,MACA;AAAA,QACE,KAAK;AAAA,QACL,MAAM;AAAA,QACN,cAAc,MAAM,QAAQ,YAAY,WAAW;AAAA,QACnD,UAAU;AAAA,QACV,OAAO,EAAE,eAAe,YAAY,SAAS,OAAQ;AAAA,QACrD,UAAU;AAAA,UACR,aAA6BT,kCAAG;AAAA,YAC9B;AAAA,YACA;AAAA,cACE,KAAK;AAAA,cACL,4BAA4B,MAAM;AAChC,sBAAM,qBAAqB,4BAA4B;AAAA,kBACrD,kBAAkB;AAAA,gBACpC,CAAiB;AACD,2BAAW,kBAAkB;AAAA,cAC7C;AAAA,YACA;AAAA,UACW;AAAA,UACeA,kCAAG,IAAC,WAAW,MAAM,EAAE,OAAO,cAAc,UAA0BA,sCAAI,UAAU,IAAI,EAAE,UAAU,IAAI,GAAG,eAAe,KAAK,aAAc,CAAA,GAAG;AAAA,UAChK,aAA6BA,kCAAG;AAAA,YAC9B;AAAA,YACA;AAAA,cACE,KAAK;AAAA,cACL,4BAA4B,MAAM;AAChC,sBAAM,qBAAqB,4BAA4B;AAAA,kBACrD,kBAAkB;AAAA,gBACpC,CAAiB;AACD,2BAAW,kBAAkB;AAAA,cAC7C;AAAA,YACA;AAAA,UACA;AAAA,QACA;AAAA,MACA;AAAA,IACK;AAAA,EACL;AACA;AACAK,gBAAc,cAAc;AAC5B,IAAI,mBAAmB;AACvB,IAAI,aAAaC,aAAgB;AAAA,EAC/B,CAAC,OAAO,iBAAiB;AACvB,UAAM,EAAE,cAAc,4BAA4B,GAAG,WAAY,IAAG;AACpE,UAAM,UAAU,wBAAwB,kBAAkB,YAAY;AACtE,WAAuBN,kCAAG;AAAA,MACxB;AAAA,MACA;AAAA,QACE,eAAe;AAAA,QACf,UAAU;AAAA,QACV,GAAG;AAAA,QACH,KAAK;AAAA,QACL,OAAO,EAAE,UAAU,QAAS;AAAA,QAC5B,SAAS,CAAC,UAAU;;AAClB,gBAAM,qBAAqB,MAAM;AACjC,gBAAM,6BAA6B,GAAC,aAAQ,aAAR,mBAAkB,SAAS;AAC/D,cAAI,2BAA4B,4BAA4B;AAAA,QACtE;AAAA,MACA;AAAA,IACK;AAAA,EACL;AACA;AACA,WAAW,cAAc;AACzB,IAAI,aAAa;AACjB,IAAI,oBAAoB;AACxB,IAAI,mBAAmB;AACvB,IAAI,qBAAqB;AACzB,IAAI,kBAAkB;AACtB,IAAIU,UAAQJ,aAAgB;AAAA,EAC1B,CAAC,OAAO,iBAAiB;AACvB,UAAM,EAAE,YAAY,MAAM,UAAU,aAAa,cAAc,GAAG,WAAU,IAAK;AACjF,UAAM,CAAC,OAAO,MAAM,OAAO,IAAI,qBAAqB;AAAA,MAClD,MAAM;AAAA,MACN,aAAa;AAAA,MACb,UAAU;AAAA,IAChB,CAAK;AACD,WAAuBN,kCAAAA,IAAI,UAAU,EAAE,SAAS,cAAc,MAAM,UAA0BA,kCAAG;AAAA,MAC/F;AAAA,MACA;AAAA,QACE;AAAA,QACA,GAAG;AAAA,QACH,KAAK;AAAA,QACL,SAAS,MAAM,QAAQ,KAAK;AAAA,QAC5B,SAAS,eAAe,MAAM,OAAO;AAAA,QACrC,UAAU,eAAe,MAAM,QAAQ;AAAA,QACvC,cAAc,qBAAqB,MAAM,cAAc,CAAC,UAAU;AAChE,gBAAM,cAAc,aAAa,cAAc,OAAO;AAAA,QAChE,CAAS;AAAA,QACD,aAAa,qBAAqB,MAAM,aAAa,CAAC,UAAU;AAC9D,gBAAM,EAAE,GAAG,EAAG,IAAG,MAAM,OAAO;AAC9B,gBAAM,cAAc,aAAa,cAAc,MAAM;AACrD,gBAAM,cAAc,MAAM,YAAY,8BAA8B,GAAG,CAAC,IAAI;AAC5E,gBAAM,cAAc,MAAM,YAAY,8BAA8B,GAAG,CAAC,IAAI;AAAA,QACtF,CAAS;AAAA,QACD,eAAe,qBAAqB,MAAM,eAAe,CAAC,UAAU;AAClE,gBAAM,cAAc,aAAa,cAAc,QAAQ;AACvD,gBAAM,cAAc,MAAM,eAAe,4BAA4B;AACrE,gBAAM,cAAc,MAAM,eAAe,4BAA4B;AACrE,gBAAM,cAAc,MAAM,eAAe,2BAA2B;AACpE,gBAAM,cAAc,MAAM,eAAe,2BAA2B;AAAA,QAC9E,CAAS;AAAA,QACD,YAAY,qBAAqB,MAAM,YAAY,CAAC,UAAU;AAC5D,gBAAM,EAAE,GAAG,EAAG,IAAG,MAAM,OAAO;AAC9B,gBAAM,cAAc,aAAa,cAAc,KAAK;AACpD,gBAAM,cAAc,MAAM,eAAe,4BAA4B;AACrE,gBAAM,cAAc,MAAM,eAAe,4BAA4B;AACrE,gBAAM,cAAc,MAAM,YAAY,6BAA6B,GAAG,CAAC,IAAI;AAC3E,gBAAM,cAAc,MAAM,YAAY,6BAA6B,GAAG,CAAC,IAAI;AAC3E,kBAAQ,KAAK;AAAA,QACd,CAAA;AAAA,MACT;AAAA,IACA,GAAO;AAAA,EACP;AACA;AACAU,QAAM,cAAc;AACpB,IAAI,CAAC,0BAA0B,0BAA0B,IAAI,mBAAmB,YAAY;AAAA,EAC1F,UAAU;AAAA,EACZ;AACA,CAAC;AACD,IAAI,YAAYJ,aAAgB;AAAA,EAC9B,CAAC,OAAO,iBAAiB;AACvB,UAAM;AAAA,MACJ;AAAA,MACA,OAAO;AAAA,MACP,UAAU;AAAA,MACV;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,GAAG;AAAA,IACT,IAAQ;AACJ,UAAM,UAAU,wBAAwB,YAAY,YAAY;AAChE,UAAM,CAAC,MAAM,OAAO,IAAIJ,aAAAA,SAAe,IAAI;AAC3C,UAAM,eAAe,gBAAgB,cAAc,CAAC,UAAU,QAAQ,KAAK,CAAC;AAC5E,UAAM,kBAAkBC,aAAY,OAAC,IAAI;AACzC,UAAM,gBAAgBA,aAAY,OAAC,IAAI;AACvC,UAAM,WAAW,gBAAgB,QAAQ;AACzC,UAAM,yBAAyBA,aAAY,OAAC,CAAC;AAC7C,UAAM,6BAA6BA,aAAY,OAAC,QAAQ;AACxD,UAAM,gBAAgBA,aAAY,OAAC,CAAC;AACpC,UAAM,EAAE,YAAY,cAAa,IAAK;AACtC,UAAM,cAAc,eAAe,MAAM;;AACvC,YAAM,iBAAiB,6BAAM,SAAS,SAAS;AAC/C,UAAI,eAAgB,eAAQ,aAAR,mBAAkB;AACtC,cAAS;AAAA,IACf,CAAK;AACD,UAAM,aAAaC,aAAiB;AAAA,MAClC,CAAC,cAAc;AACb,YAAI,CAAC,aAAa,cAAc,SAAU;AAC1C,eAAO,aAAa,cAAc,OAAO;AACzC,+BAAuB,WAA2B,oBAAI,KAAI,GAAI,QAAS;AACvE,sBAAc,UAAU,OAAO,WAAW,aAAa,SAAS;AAAA,MACjE;AAAA,MACD,CAAC,WAAW;AAAA,IACb;AACDG,iBAAAA,UAAgB,MAAM;AACpB,YAAM,WAAW,QAAQ;AACzB,UAAI,UAAU;AACZ,cAAM,eAAe,MAAM;AACzB,qBAAW,2BAA2B,OAAO;AAC7C;AAAA,QACD;AACD,cAAM,cAAc,MAAM;AACxB,gBAAM,eAA+B,oBAAI,KAAM,GAAE,QAAO,IAAK,uBAAuB;AACpF,qCAA2B,UAAU,2BAA2B,UAAU;AAC1E,iBAAO,aAAa,cAAc,OAAO;AACzC;AAAA,QACD;AACD,iBAAS,iBAAiB,gBAAgB,WAAW;AACrD,iBAAS,iBAAiB,iBAAiB,YAAY;AACvD,eAAO,MAAM;AACX,mBAAS,oBAAoB,gBAAgB,WAAW;AACxD,mBAAS,oBAAoB,iBAAiB,YAAY;AAAA,QAC3D;AAAA,MACT;AAAA,IACA,GAAO,CAAC,QAAQ,UAAU,UAAU,SAAS,UAAU,UAAU,CAAC;AAC9DA,iBAAAA,UAAgB,MAAM;AACpB,UAAI,QAAQ,CAAC,QAAQ,iBAAiB,QAAS,YAAW,QAAQ;AAAA,IACxE,GAAO,CAAC,MAAM,UAAU,QAAQ,kBAAkB,UAAU,CAAC;AACzDA,iBAAAA,UAAgB,MAAM;AACpB,iBAAY;AACZ,aAAO,MAAM,cAAe;AAAA,IAClC,GAAO,CAAC,YAAY,aAAa,CAAC;AAC9B,UAAM,sBAAsBX,aAAAA,QAAc,MAAM;AAC9C,aAAO,OAAO,uBAAuB,IAAI,IAAI;AAAA,IACnD,GAAO,CAAC,IAAI,CAAC;AACT,QAAI,CAAC,QAAQ,SAAU,QAAO;AAC9B,WAAuBY,kCAAI,KAACG,4BAAU,EAAE,UAAU;AAAA,MAChD,uBAAuCX,kCAAG;AAAA,QACxC;AAAA,QACA;AAAA,UACE;AAAA,UACA,MAAM;AAAA,UACN,aAAa,SAAS,eAAe,cAAc;AAAA,UACnD,eAAe;AAAA,UACf,UAAU;AAAA,QACpB;AAAA,MACO;AAAA,MACeA,kCAAG,IAAC,0BAA0B,EAAE,OAAO,cAAc,SAAS,aAAa,UAAUY,gBAAqB;AAAA,QACxGZ,kCAAG,IAAC,WAAW,UAAU,EAAE,OAAO,cAAc,UAA0BA,kCAAG;AAAA,UAC3Fa;AAAAA,UACA;AAAA,YACE,SAAS;AAAA,YACT,iBAAiB,qBAAqB,iBAAiB,MAAM;AAC3D,kBAAI,CAAC,QAAQ,+BAA+B,QAAS,aAAa;AAClE,sBAAQ,+BAA+B,UAAU;AAAA,YAC/D,CAAa;AAAA,YACD,UAA0Bb,kCAAG;AAAA,cAC3B,UAAU;AAAA,cACV;AAAA,gBACE,MAAM;AAAA,gBACN,aAAa;AAAA,gBACb,eAAe;AAAA,gBACf,UAAU;AAAA,gBACV,cAAc,OAAO,SAAS;AAAA,gBAC9B,wBAAwB,QAAQ;AAAA,gBAChC,GAAG;AAAA,gBACH,KAAK;AAAA,gBACL,OAAO,EAAE,YAAY,QAAQ,aAAa,QAAQ,GAAG,MAAM,MAAO;AAAA,gBAClE,WAAW,qBAAqB,MAAM,WAAW,CAAC,UAAU;AAC1D,sBAAI,MAAM,QAAQ,SAAU;AAC5B,qEAAkB,MAAM;AACxB,sBAAI,CAAC,MAAM,YAAY,kBAAkB;AACvC,4BAAQ,+BAA+B,UAAU;AACjD,gCAAa;AAAA,kBACjC;AAAA,gBACA,CAAiB;AAAA,gBACD,eAAe,qBAAqB,MAAM,eAAe,CAAC,UAAU;AAClE,sBAAI,MAAM,WAAW,EAAG;AACxB,kCAAgB,UAAU,EAAE,GAAG,MAAM,SAAS,GAAG,MAAM,QAAS;AAAA,gBAClF,CAAiB;AAAA,gBACD,eAAe,qBAAqB,MAAM,eAAe,CAAC,UAAU;AAClE,sBAAI,CAAC,gBAAgB,QAAS;AAC9B,wBAAM,IAAI,MAAM,UAAU,gBAAgB,QAAQ;AAClD,wBAAM,IAAI,MAAM,UAAU,gBAAgB,QAAQ;AAClD,wBAAM,sBAAsB,QAAQ,cAAc,OAAO;AACzD,wBAAM,oBAAoB,CAAC,QAAQ,OAAO,EAAE,SAAS,QAAQ,cAAc;AAC3E,wBAAM,QAAQ,CAAC,QAAQ,IAAI,EAAE,SAAS,QAAQ,cAAc,IAAI,KAAK,MAAM,KAAK;AAChF,wBAAM,WAAW,oBAAoB,MAAM,GAAG,CAAC,IAAI;AACnD,wBAAM,WAAW,CAAC,oBAAoB,MAAM,GAAG,CAAC,IAAI;AACpD,wBAAM,kBAAkB,MAAM,gBAAgB,UAAU,KAAK;AAC7D,wBAAM,QAAQ,EAAE,GAAG,UAAU,GAAG,SAAU;AAC1C,wBAAM,cAAc,EAAE,eAAe,OAAO,MAAO;AACnD,sBAAI,qBAAqB;AACvB,kCAAc,UAAU;AACxB,iDAA6B,kBAAkB,aAAa,aAAa;AAAA,sBACvE,UAAU;AAAA,oBAChC,CAAqB;AAAA,kBACrB,WAA6B,mBAAmB,OAAO,QAAQ,gBAAgB,eAAe,GAAG;AAC7E,kCAAc,UAAU;AACxB,iDAA6B,mBAAmB,cAAc,aAAa;AAAA,sBACzE,UAAU;AAAA,oBAChC,CAAqB;AACD,0BAAM,OAAO,kBAAkB,MAAM,SAAS;AAAA,kBAClE,WAA6B,KAAK,IAAI,CAAC,IAAI,mBAAmB,KAAK,IAAI,CAAC,IAAI,iBAAiB;AACzE,oCAAgB,UAAU;AAAA,kBAC9C;AAAA,gBACA,CAAiB;AAAA,gBACD,aAAa,qBAAqB,MAAM,aAAa,CAAC,UAAU;AAC9D,wBAAM,QAAQ,cAAc;AAC5B,wBAAM,SAAS,MAAM;AACrB,sBAAI,OAAO,kBAAkB,MAAM,SAAS,GAAG;AAC7C,2BAAO,sBAAsB,MAAM,SAAS;AAAA,kBAChE;AACkB,gCAAc,UAAU;AACxB,kCAAgB,UAAU;AAC1B,sBAAI,OAAO;AACT,0BAAM,QAAQ,MAAM;AACpB,0BAAM,cAAc,EAAE,eAAe,OAAO,MAAO;AACnD,wBAAI,mBAAmB,OAAO,QAAQ,gBAAgB,QAAQ,cAAc,GAAG;AAC7E,mDAA6B,iBAAiB,YAAY,aAAa;AAAA,wBACrE,UAAU;AAAA,sBAClC,CAAuB;AAAA,oBACvB,OAA2B;AACL;AAAA,wBACE;AAAA,wBACA;AAAA,wBACA;AAAA,wBACA;AAAA,0BACE,UAAU;AAAA,wBACpC;AAAA,sBACuB;AAAA,oBACvB;AACoB,0BAAM,iBAAiB,SAAS,CAAC,WAAW,OAAO,kBAAkB;AAAA,sBACnE,MAAM;AAAA,oBAC5B,CAAqB;AAAA,kBACrB;AAAA,gBACiB,CAAA;AAAA,cACjB;AAAA,YACA;AAAA,UACA;AAAA,QACA,GAAW;AAAA,QACH,QAAQ;AAAA,MAChB,EAAS,CAAA;AAAA,IACT,GAAO;AAAA,EACP;AACA;AACA,IAAI,gBAAgB,CAAC,UAAU;AAC7B,QAAM,EAAE,cAAc,UAAU,GAAG,cAAe,IAAG;AACrD,QAAM,UAAU,wBAAwB,YAAY,YAAY;AAChE,QAAM,CAAC,oBAAoB,qBAAqB,IAAIE,aAAAA,SAAe,KAAK;AACxE,QAAM,CAAC,aAAa,cAAc,IAAIA,aAAAA,SAAe,KAAK;AAC1D,eAAa,MAAM,sBAAsB,IAAI,CAAC;AAC9CK,eAAAA,UAAgB,MAAM;AACpB,UAAM,QAAQ,OAAO,WAAW,MAAM,eAAe,IAAI,GAAG,GAAG;AAC/D,WAAO,MAAM,OAAO,aAAa,KAAK;AAAA,EACvC,GAAE,EAAE;AACL,SAAO,cAAc,OAAuBP,kCAAG,IAAC,QAAQ,EAAE,SAAS,MAAM,UAA0BA,kCAAAA,IAAI,gBAAgB,EAAE,GAAG,eAAe,UAAU,sBAAsCQ,uCAAKG,kBAAAA,UAAU,EAAE,UAAU;AAAA,IACpN,QAAQ;AAAA,IACR;AAAA,IACA;AAAA,EACJ,EAAK,CAAA,EAAG,CAAA,GAAG;AACX;AACA,IAAI,aAAa;AACjB,IAAIG,eAAaR,aAAgB;AAAA,EAC/B,CAAC,OAAO,iBAAiB;AACvB,UAAM,EAAE,cAAc,GAAG,WAAU,IAAK;AACxC,WAAuBN,kCAAAA,IAAI,UAAU,KAAK,EAAE,GAAG,YAAY,KAAK,cAAc;AAAA,EAClF;AACA;AACAc,aAAW,cAAc;AACzB,IAAI,mBAAmB;AACvB,IAAIC,qBAAmBT,aAAgB;AAAA,EACrC,CAAC,OAAO,iBAAiB;AACvB,UAAM,EAAE,cAAc,GAAG,iBAAgB,IAAK;AAC9C,WAAuBN,kCAAAA,IAAI,UAAU,KAAK,EAAE,GAAG,kBAAkB,KAAK,cAAc;AAAA,EACxF;AACA;AACAe,mBAAiB,cAAc;AAC/B,IAAI,cAAc;AAClB,IAAIC,gBAAcV,aAAgB;AAAA,EAChC,CAAC,OAAO,iBAAiB;AACvB,UAAM,EAAE,SAAS,GAAG,YAAW,IAAK;AACpC,QAAI,CAAC,QAAQ,QAAQ;AACnB,cAAQ;AAAA,QACN,0CAA0C,WAAW;AAAA,MACtD;AACD,aAAO;AAAA,IACb;AACI,WAAuBN,kCAAAA,IAAI,sBAAsB,EAAE,SAAS,SAAS,MAAM,UAA0BA,kCAAG,IAACiB,cAAY,EAAE,GAAG,aAAa,KAAK,aAAY,CAAE,EAAC,CAAE;AAAA,EACjK;AACA;AACAD,cAAY,cAAc;AAC1B,IAAI,aAAa;AACjB,IAAIC,eAAaX,aAAgB;AAAA,EAC/B,CAAC,OAAO,iBAAiB;AACvB,UAAM,EAAE,cAAc,GAAG,WAAU,IAAK;AACxC,UAAM,qBAAqB,2BAA2B,YAAY,YAAY;AAC9E,WAAuBN,kCAAAA,IAAI,sBAAsB,EAAE,SAAS,MAAM,UAA0BA,kCAAG;AAAA,MAC7F,UAAU;AAAA,MACV;AAAA,QACE,MAAM;AAAA,QACN,GAAG;AAAA,QACH,KAAK;AAAA,QACL,SAAS,qBAAqB,MAAM,SAAS,mBAAmB,OAAO;AAAA,MAC/E;AAAA,IACA,GAAO;AAAA,EACP;AACA;AACAiB,aAAW,cAAc;AACzB,IAAI,uBAAuBX,aAAgB,WAAC,CAAC,OAAO,iBAAiB;AACnE,QAAM,EAAE,cAAc,SAAS,GAAG,qBAAsB,IAAG;AAC3D,SAAuBN,kCAAG;AAAA,IACxB,UAAU;AAAA,IACV;AAAA,MACE,qCAAqC;AAAA,MACrC,iCAAiC,WAAW;AAAA,MAC5C,GAAG;AAAA,MACH,KAAK;AAAA,IACX;AAAA,EACG;AACH,CAAC;AACD,SAAS,uBAAuB,WAAW;AACzC,QAAM,cAAc,CAAE;AACtB,QAAM,aAAa,MAAM,KAAK,UAAU,UAAU;AAClD,aAAW,QAAQ,CAAC,SAAS;AAC3B,QAAI,KAAK,aAAa,KAAK,aAAa,KAAK,YAAa,aAAY,KAAK,KAAK,WAAW;AAC3F,QAAI,cAAc,IAAI,GAAG;AACvB,YAAM,WAAW,KAAK,cAAc,KAAK,UAAU,KAAK,MAAM,YAAY;AAC1E,YAAM,aAAa,KAAK,QAAQ,8BAA8B;AAC9D,UAAI,CAAC,UAAU;AACb,YAAI,YAAY;AACd,gBAAM,UAAU,KAAK,QAAQ;AAC7B,cAAI,QAAS,aAAY,KAAK,OAAO;AAAA,QAC/C,OAAe;AACL,sBAAY,KAAK,GAAG,uBAAuB,IAAI,CAAC;AAAA,QAC1D;AAAA,MACA;AAAA,IACA;AAAA,EACA,CAAG;AACD,SAAO;AACT;AACA,SAAS,6BAA6B,MAAM,SAAS,QAAQ,EAAE,SAAQ,GAAI;AACzE,QAAM,gBAAgB,OAAO,cAAc;AAC3C,QAAM,QAAQ,IAAI,YAAY,MAAM,EAAE,SAAS,MAAM,YAAY,MAAM,QAAQ;AAC/E,MAAI,QAAS,eAAc,iBAAiB,MAAM,SAAS,EAAE,MAAM,MAAM;AACzE,MAAI,UAAU;AACZ,gCAA4B,eAAe,KAAK;AAAA,EACpD,OAAS;AACL,kBAAc,cAAc,KAAK;AAAA,EACrC;AACA;AACA,IAAI,qBAAqB,CAAC,OAAO,WAAW,YAAY,MAAM;AAC5D,QAAM,SAAS,KAAK,IAAI,MAAM,CAAC;AAC/B,QAAM,SAAS,KAAK,IAAI,MAAM,CAAC;AAC/B,QAAM,WAAW,SAAS;AAC1B,MAAI,cAAc,UAAU,cAAc,SAAS;AACjD,WAAO,YAAY,SAAS;AAAA,EAChC,OAAS;AACL,WAAO,CAAC,YAAY,SAAS;AAAA,EACjC;AACA;AACA,SAAS,aAAa,WAAW,MAAM;AACvC,GAAG;AACD,QAAM,KAAK,eAAe,QAAQ;AAClCkB,mBAAgB,MAAM;AACpB,QAAI,OAAO;AACX,QAAI,OAAO;AACX,WAAO,OAAO,sBAAsB,MAAM,OAAO,OAAO,sBAAsB,EAAE,CAAC;AACjF,WAAO,MAAM;AACX,aAAO,qBAAqB,IAAI;AAChC,aAAO,qBAAqB,IAAI;AAAA,IACjC;AAAA,EACL,GAAK,CAAC,EAAE,CAAC;AACT;AACA,SAAS,cAAc,MAAM;AAC3B,SAAO,KAAK,aAAa,KAAK;AAChC;AACA,SAAS,sBAAsB,WAAW;AACxC,QAAM,QAAQ,CAAE;AAChB,QAAM,SAAS,SAAS,iBAAiB,WAAW,WAAW,cAAc;AAAA,IAC3E,YAAY,CAAC,SAAS;AACpB,YAAM,gBAAgB,KAAK,YAAY,WAAW,KAAK,SAAS;AAChE,UAAI,KAAK,YAAY,KAAK,UAAU,cAAe,QAAO,WAAW;AACrE,aAAO,KAAK,YAAY,IAAI,WAAW,gBAAgB,WAAW;AAAA,IACxE;AAAA,EACA,CAAG;AACD,SAAO,OAAO,SAAU,EAAE,OAAM,KAAK,OAAO,WAAW;AACvD,SAAO;AACT;AACA,SAAS,WAAW,YAAY;AAC9B,QAAM,2BAA2B,SAAS;AAC1C,SAAO,WAAW,KAAK,CAAC,cAAc;AACpC,QAAI,cAAc,yBAA0B,QAAO;AACnD,cAAU,MAAO;AACjB,WAAO,SAAS,kBAAkB;AAAA,EACtC,CAAG;AACH;AACA,IAAI,WAAWjB;AACf,IAAI,WAAWI;AACf,IAAI,QAAQK;AACZ,IAAI,QAAQI;AACZ,IAAI,cAAcC;AAClB,IAAI,SAASC;AACb,IAAI,QAAQC;AC/mBZ,MAAM,gBAAgBE;AAEtB,MAAM,gBAAgBb,aAGpB,WAAA,CAAC,EAAE,WAAW,GAAG,MAAM,GAAG,QAC1BN,kCAAA;AAAA,EAACoB;AAAAA,EAAA;AAAA,IACC;AAAA,IACA,WAAW;AAAA,MACT;AAAA,MACA;AAAA,IACF;AAAA,IACC,GAAG;AAAA,EAAA;AACN,CACD;AACD,cAAc,cAAcA,SAAyB;AAErD,MAAM,gBAAgB;AAAA,EACpB;AAAA,EACA;AAAA,IACE,UAAU;AAAA,MACR,SAAS;AAAA,QACP,SAAS;AAAA,QACT,aACE;AAAA,MAAA;AAAA,IAEN;AAAA,IACA,iBAAiB;AAAA,MACf,SAAS;AAAA,IAAA;AAAA,EACX;AAEJ;AAEA,MAAM,QAAQd,aAAM,WAIlB,CAAC,EAAE,WAAW,SAAS,GAAG,MAAM,GAAG,QAAQ;AAEzC,SAAAN,kCAAA;AAAA,IAACqB;AAAAA,IAAA;AAAA,MACC;AAAA,MACA,WAAW,GAAG,cAAc,EAAE,QAAS,CAAA,GAAG,SAAS;AAAA,MAClD,GAAG;AAAA,IAAA;AAAA,EACN;AAEJ,CAAC;AACD,MAAM,cAAcA,MAAqB;AAEzC,MAAM,cAAcf,aAGlB,WAAA,CAAC,EAAE,WAAW,GAAG,MAAM,GAAG,QAC1BN,kCAAA;AAAA,EAACsB;AAAAA,EAAA;AAAA,IACC;AAAA,IACA,WAAW;AAAA,MACT;AAAA,MACA;AAAA,IACF;AAAA,IACC,GAAG;AAAA,EAAA;AACN,CACD;AACD,YAAY,cAAcA,OAAuB;AAEjD,MAAM,aAAahB,aAGjB,WAAA,CAAC,EAAE,WAAW,GAAG,MAAM,GAAG,QAC1BN,kCAAA;AAAA,EAACuB;AAAAA,EAAA;AAAA,IACC;AAAA,IACA,WAAW;AAAA,MACT;AAAA,MACA;AAAA,IACF;AAAA,IACA,eAAY;AAAA,IACX,GAAG;AAAA,IAEJ,UAAAvB,kCAAAA,IAAC,GAAE,EAAA,WAAU,UAAU,CAAA;AAAA,EAAA;AACzB,CACD;AACD,WAAW,cAAcuB,MAAsB;AAE/C,MAAM,aAAajB,aAGjB,WAAA,CAAC,EAAE,WAAW,GAAG,MAAM,GAAG,QAC1BN,kCAAA;AAAA,EAACwB;AAAAA,EAAA;AAAA,IACC;AAAA,IACA,WAAW,GAAG,yBAAyB,SAAS;AAAA,IAC/C,GAAG;AAAA,EAAA;AACN,CACD;AACD,WAAW,cAAcA,MAAsB;AAE/C,MAAM,mBAAmBlB,aAGvB,WAAA,CAAC,EAAE,WAAW,GAAG,MAAM,GAAG,QAC1BN,kCAAA;AAAA,EAACyB;AAAAA,EAAA;AAAA,IACC;AAAA,IACA,WAAW,GAAG,sBAAsB,SAAS;AAAA,IAC5C,GAAG;AAAA,EAAA;AACN,CACD;AACD,iBAAiB,cAAcA,YAA4B;ACpGpD,SAAS,UAAU;AAClB,QAAA,EAAE,OAAO,IAAI,SAAS;AAE5B,gDACG,eACE,EAAA,UAAA;AAAA,IAAO,OAAA,IAAI,SAAU,EAAE,IAAI,OAAO,aAAa,QAAQ,GAAG,SAAS;AAEhE,aAAAjB,kCAAA,KAAC,OAAgB,EAAA,GAAG,OAClB,UAAA;AAAA,QAACA,kCAAAA,KAAA,OAAA,EAAI,WAAU,cACZ,UAAA;AAAA,UAAS,SAAAR,kCAAAA,IAAC,cAAY,UAAM,MAAA,CAAA;AAAA,UAC5B,eACEA,kCAAAA,IAAA,kBAAA,EAAkB,UAAY,YAAA,CAAA;AAAA,QAAA,GAEnC;AAAA,QACC;AAAA,8CACA,YAAW,CAAA,CAAA;AAAA,MAAA,EAAA,GARF,EASZ;AAAA,IAAA,CAEH;AAAA,0CACA,eAAc,CAAA,CAAA;AAAA,EAAA,GACjB;AAEJ;ACRa0B,MAAAA,QAAuBA,MAAM,CACxC;AAAA,EAAEC,KAAK;AAAA,EAAcC,MAAMC;AAAO,GAClC;AAAA,EAAEF,KAAK;AAAA,EAAcC,MAAM;AAA+B,GAC1D;AAAA,EACED,KAAK;AAAA,EACLC,MAAM;AAAA,EACNE,aAAa;AACf,GACA;AAAA,EACEH,KAAK;AAAA,EACLC,MAAM;AACR,GACA;AAAA,EACED,KAAK;AAAA,EACLC,MAAM;AACR,GACA;AAAA,EACED,KAAK;AAAA,EACLI,MAAM;AAAA,EACNC,OAAO;AAAA,EACPJ,MAAM;AACR,GACA;AAAA,EACED,KAAK;AAAA,EACLI,MAAM;AAAA,EACNC,OAAO;AAAA,EACPJ,MAAM;AACR,CAAA;AAuDc,SAAAK,OAAO;AAAA,EAAEC;AAAS,GAAkC;AAClE,QAAMC,WAAWC,YAAY;AACvBC,QAAAA,UAASC,mBAA+B,MAAM;AACpD,QAAMC,WAAWC,YAAY;AAEvB,QAAAC,SAAQJ,mCAAQI,UAAS;AAC/B,QAAMC,qBAAqBL,mCAAQK;AAEnCC,eAAAA,UAAU,MAAM;AACd,SAAGD,yDAAoBE,iBAAgB,UAClCF,yDAAoBG,gBAAe,YAClCN,SAASO,aAAa,WACrBP,SAASO,aAAa,OACtBP,SAASO,aAAa,qBACtBP,SAASO,aAAa,8BAA6B;AACxDX,eAAS,gBAAgB;AACzB;AAAA,IACF;AAAA,EACF,GAAG,EAAE;AAEL,SACG3B,kCAAAA,KAAA,QAAA;AAAA,IAAKuC,MAAK;AAAA,IAAKC,WAAWP;AAAAA,IACzBP,UAAA,CAAA1B,kCAAA,KAAC,QACC;AAAA,MAAA0B,UAAA,CAAClC,kCAAA,IAAA,QAAA;AAAA,QAAKiD,SAAQ;AAAA,MAAQ,CAAA,GACrBjD,kCAAA,IAAA,QAAA;AAAA,QAAKkD,MAAK;AAAA,QAAWC,SAAQ;AAAA,MAAsC,CAAA,yCACnEC,MAAK,CAAA,CAAA,yCACLC,OAAM,EAAA,CAAA;AAAA,IACT,CAAA,0CACC,QAAK;AAAA,MAAAL,WAAWP,UAAU,UAAU,wBAAwB;AAAA,MAC3DP,UAAA,CAAAlC,kCAAAA,IAACsD,qBAAoB,CAAA,CAAA,GACrBtD,kCAAA,IAACC;QACEiC;AAAAA,MACH,CAAA,yCACCqB,SAAQ,CAAA,CAAA,yCACRC,mBAAkB,EAAA,yCAClBC,SAAQ,CAAA,CAAA,CAAA;AAAA,IACX,CAAA,CAAA;AAAA,EACF,CAAA;AAEJ;AAEA,SAAwBC,MAAM;AACrB,SAAA1D,kCAAAA,IAAAW,kBAAAA,UAAA;AAAA,IACLuB,UAAClC,kCAAAA,IAAA2D,QAAA,CAAO,CAAA;AAAA,EAEV,CAAA;AACF;", "x_google_ignoreList": [0, 3]}