import { r as reactExports, j as jsxRuntimeExports } from "./jsx-runtime-bB2y7OuD.js";
import { T as Tabs, a as TabsList, b as TabsTrigger } from "./tabs-CfSdyzWr.js";
import { C as Card, b as <PERSON><PERSON><PERSON><PERSON>, c as <PERSON><PERSON><PERSON><PERSON>, a as CardContent } from "./card-BJQMSLe_.js";
import { B as Button } from "./button-ByAXMyvk.js";
import { S as Select, a as SelectTrigger, b as SelectValue, c as SelectContent, d as SelectItem } from "./select-BFGSXKcr.js";
import { L as Label, R as ResponsiveContainer, B as Bar<PERSON>hart, C as CartesianGrid, X as XAxis, Y as YAxis, T as Tooltip, a as Bar } from "./BarChart-DYWu-Gnb.js";
import { C as ChevronLeft } from "./chevron-left-CLqBlTg1.js";
import { C as ChevronRight } from "./chevron-right-B-tR7Kir.js";
import { f as format } from "./format-82yT_5--.js";
import { a as addDays } from "./addDays-CyH8qBoF.js";
import { a as addMonths } from "./addMonths-Dj4hq91A.js";
var DashboardGroupBy = /* @__PURE__ */ ((DashboardGroupBy2) => {
  DashboardGroupBy2["Daily"] = "Daily";
  DashboardGroupBy2["Weekly"] = "Weekly";
  DashboardGroupBy2["Monthly"] = "Monthly";
  return DashboardGroupBy2;
})(DashboardGroupBy || {});
const calculatePeriodDates = (dataPoint, activeTab) => {
  try {
    let startDate = dataPoint.startDate;
    let endDate = activeTab === DashboardGroupBy.Daily ? dataPoint.startDate : activeTab === DashboardGroupBy.Weekly ? addDays(startDate, 6) : addDays(addMonths(startDate, 1), -1);
    return { startDate, endDate };
  } catch (error) {
    console.error("Error calculating date range:", error);
    return null;
  }
};
const CustomerDashboard = ({
  data,
  handleGraph,
  dashboardGroupBy,
  handleBarSelected
}) => {
  const [chartData, setChartData] = reactExports.useState([]);
  const getDefaultSelectedData = () => {
    if (chartData === null || chartData.length === 0)
      return null;
    return chartData[chartData.length - 1];
  };
  const [activeTab, setActiveTab] = reactExports.useState(dashboardGroupBy);
  const [selectedData, setSelectedData] = reactExports.useState(getDefaultSelectedData());
  reactExports.useEffect(() => {
    if (data) {
      const preparedData = [];
      if (Array.isArray(data)) {
        console.log(data, "&&&&&&&&&&&&&&&7");
        data.forEach((item) => {
          var label = dashboardGroupBy === DashboardGroupBy.Daily ? format(item.startDate, "dd/MM") : dashboardGroupBy === DashboardGroupBy.Weekly ? format(item.startDate, "dd/MM") : format(item.startDate, "MMMM");
          preparedData.push({
            periodLabel: label,
            startDate: new Date(item.startDate),
            Revenue: Math.round(item.totalAmount),
            returnAmount: item.returnAmount,
            returnweight: item.returnWeight,
            Quantity: Math.round(item.totalWeight),
            Orders: item.totalOrders,
            AovValue: Math.round(item.avgOrderValue),
            NewCustomers: item.newCustomerCount
          });
        });
      }
      setChartData(preparedData);
    }
  }, [data]);
  reactExports.useEffect(() => {
    if (data) {
      const preparedData = [];
      if (Array.isArray(data)) {
        data.forEach((item) => {
          var label = dashboardGroupBy === DashboardGroupBy.Daily ? format(item.startDate, "dd/MM") : dashboardGroupBy === DashboardGroupBy.Weekly ? format(item.startDate, "dd/MM") : format(item.startDate, "MMMM");
          preparedData.push({ periodLabel: label, startDate: new Date(item.startDate), Revenue: Math.round(item.totalAmount), returnAmount: item.returnAmount, returnweight: item.returnWeight, Quantity: Math.round(item.totalWeight), Orders: item.totalOrders, AovValue: Math.round(item.avgOrderValue), NewCustomers: item.newCustomerCount });
        });
      }
      setChartData(preparedData);
      setActiveTab(dashboardGroupBy);
    }
  }, [data, selectedData]);
  const handleBarClick = (dp) => {
    setSelectedData(dp);
    if (handleBarSelected) {
      const periodDates = calculatePeriodDates(dp, activeTab);
      if (periodDates) {
        handleBarSelected(periodDates.startDate, periodDates.endDate, activeTab, chartData[chartData.length - 1].startDate, dp);
      }
    }
  };
  const isBarSelected = (periodLabel) => {
    return periodLabel === (selectedData == null ? void 0 : selectedData.periodLabel);
  };
  const getTimeRangeTitle = () => {
    if (chartData === null || chartData.length === 0)
      return "";
    try {
      const startDate = chartData[0].startDate;
      const endDate = chartData[chartData.length - 1].startDate;
      if (activeTab === DashboardGroupBy.Daily) {
        return `${format(startDate, "d MMM")} - ${format(endDate, "d MMM, yyyy")}`;
      }
      if (activeTab === DashboardGroupBy.Weekly) {
        const lastDay = addDays(endDate, 6);
        return `${format(startDate, "d MMM")} - ${format(lastDay, "d MMM, yyyy")}`;
      }
      if (activeTab === DashboardGroupBy.Monthly) {
        return `${format(startDate, new Date(startDate).getFullYear() === new Date(endDate).getFullYear() ? "MMM" : "MMM yyyy")} - ${format(endDate, "MMM yyyy")}`;
      }
    } catch (error) {
      console.error("Date calculation error:", error);
      return "Date range unavailable";
    }
    return "";
  };
  const getSelectedPeriodTitle = () => {
    if (!selectedData) return "";
    try {
      const startDate = selectedData.startDate;
      if (activeTab === DashboardGroupBy.Daily) {
        return `${format(startDate, "EEEE, dd MMM")}`;
      }
      if (activeTab === DashboardGroupBy.Weekly) {
        const lastDay = addDays(selectedData.startDate, 6);
        return `${format(startDate, "d MMM")} - ${format(lastDay, "d MMM, yyyy")}`;
      }
      if (activeTab === DashboardGroupBy.Monthly) {
        return `${format(startDate, "MMMM yyyy")}`;
      }
    } catch (error) {
      console.error("Date calculation error:", error);
      return "Date range unavailable";
    }
    return "";
  };
  const handlePreviousPeriod = () => {
    var currentSummaryDate = chartData[chartData.length - 1].startDate;
    var prevSD = activeTab === DashboardGroupBy.Daily ? addDays(currentSummaryDate, -15) : activeTab === DashboardGroupBy.Weekly ? addDays(currentSummaryDate, -4 * 7) : addMonths(currentSummaryDate, -3);
    handleGraph(activeTab, prevSD);
  };
  const handleNextPeriod = () => {
    var currentSummaryDate = chartData[chartData.length - 1].startDate;
    var nextSD = activeTab === DashboardGroupBy.Daily ? addDays(currentSummaryDate, 15) : activeTab === DashboardGroupBy.Weekly ? addDays(currentSummaryDate, 4 * 7) : addMonths(currentSummaryDate, 3);
    if (addDays(nextSD, -1) > /* @__PURE__ */ new Date())
      nextSD = /* @__PURE__ */ new Date();
    handleGraph(activeTab, nextSD);
  };
  const getPrevDisabled = () => {
    if (activeTab === DashboardGroupBy.Daily && (chartData === null || chartData.length < 30)) {
      return true;
    }
    if (activeTab === DashboardGroupBy.Weekly && (chartData === null || chartData.length < 12)) {
      return true;
    }
    if (activeTab === DashboardGroupBy.Monthly && (chartData === null || chartData.length < 6)) {
      return true;
    }
    return false;
  };
  const getNextDisabled = () => {
    if (activeTab === DashboardGroupBy.Daily && (chartData === null || chartData.length === 0 || addDays(chartData[chartData.length - 1].startDate, 1) >= /* @__PURE__ */ new Date())) {
      return true;
    }
    if (activeTab === DashboardGroupBy.Weekly && (chartData === null || chartData.length === 0 || addDays(chartData[chartData.length - 1].startDate, 7) >= /* @__PURE__ */ new Date())) {
      return true;
    }
    if (activeTab === DashboardGroupBy.Monthly && (chartData === null || chartData.length === 0 || addMonths(chartData[chartData.length - 1].startDate, 1) >= /* @__PURE__ */ new Date())) {
      return true;
    }
    return false;
  };
  const handleTabChange = (value) => {
    setActiveTab(value);
    handleGraph(value, /* @__PURE__ */ new Date());
  };
  const [selectedMetric, setSelectedMetric] = reactExports.useState("Revenue");
  const updateMetrics = (val) => [
    setSelectedMetric(val)
  ];
  return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { children: [
    /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex justify-between items-center p-4 ", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx("h1", { className: "text-2xl font-bold text-primary", children: "Dashboard" }),
      /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: " flex flex-row gap-2", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx(Label, { children: "Metrics" }),
        /* @__PURE__ */ jsxRuntimeExports.jsxs(
          Select,
          {
            onValueChange: (val) => updateMetrics(val),
            value: selectedMetric,
            children: [
              /* @__PURE__ */ jsxRuntimeExports.jsx(SelectTrigger, { children: /* @__PURE__ */ jsxRuntimeExports.jsx(SelectValue, { placeholder: "Select Metrics" }) }),
              /* @__PURE__ */ jsxRuntimeExports.jsxs(SelectContent, { children: [
                /* @__PURE__ */ jsxRuntimeExports.jsx(SelectItem, { value: "Revenue", children: "Revenue ( ₹ )" }),
                /* @__PURE__ */ jsxRuntimeExports.jsx(SelectItem, { value: "Quantity", children: "Quantity ( kg )" }),
                /* @__PURE__ */ jsxRuntimeExports.jsx(SelectItem, { value: "AovValue", children: "Avg Order Value ( ₹ )" }),
                /* @__PURE__ */ jsxRuntimeExports.jsx(SelectItem, { value: "Orders", children: "Orders" }),
                /* @__PURE__ */ jsxRuntimeExports.jsx(SelectItem, { value: "NewCustomers", children: "New Customers" })
              ] })
            ]
          }
        ),
        /* @__PURE__ */ jsxRuntimeExports.jsx(
          Tabs,
          {
            value: activeTab,
            onValueChange: (value) => handleTabChange(value),
            children: /* @__PURE__ */ jsxRuntimeExports.jsxs(TabsList, { children: [
              /* @__PURE__ */ jsxRuntimeExports.jsx(TabsTrigger, { value: DashboardGroupBy.Daily, children: "Daily" }),
              /* @__PURE__ */ jsxRuntimeExports.jsx(TabsTrigger, { value: DashboardGroupBy.Weekly, children: "Weekly" }),
              /* @__PURE__ */ jsxRuntimeExports.jsx(TabsTrigger, { value: DashboardGroupBy.Monthly, children: "Monthly" })
            ] })
          }
        )
      ] })
    ] }),
    /* @__PURE__ */ jsxRuntimeExports.jsxs(Card, { children: [
      /* @__PURE__ */ jsxRuntimeExports.jsxs(CardHeader, { className: "flex flex-row items-center justify-between", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "space-y-1", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx(CardTitle, { children: "Revenue Overview" }),
          /* @__PURE__ */ jsxRuntimeExports.jsx("p", { className: "text-sm text-muted-foreground", children: getTimeRangeTitle() })
        ] }),
        /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center space-x-2", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx(
            Button,
            {
              variant: "outline",
              size: "icon",
              onClick: () => {
                handlePreviousPeriod();
              },
              disabled: getPrevDisabled(),
              children: /* @__PURE__ */ jsxRuntimeExports.jsx(ChevronLeft, { className: "h-4 w-4" })
            }
          ),
          /* @__PURE__ */ jsxRuntimeExports.jsx(
            Button,
            {
              variant: "outline",
              size: "icon",
              onClick: () => {
                handleNextPeriod();
              },
              disabled: getNextDisabled(),
              children: /* @__PURE__ */ jsxRuntimeExports.jsx(ChevronRight, { className: "h-4 w-4" })
            }
          )
        ] })
      ] }),
      /* @__PURE__ */ jsxRuntimeExports.jsx(CardContent, { children: /* @__PURE__ */ jsxRuntimeExports.jsx(ResponsiveContainer, { width: "100%", height: 250, children: /* @__PURE__ */ jsxRuntimeExports.jsxs(
        BarChart,
        {
          data: chartData,
          margin: { top: 20, right: 20, left: 20, bottom: 20 },
          onClick: (data2) => {
            if (data2 && data2.activePayload && data2.activePayload.length > 0) {
              handleBarClick(data2.activePayload[0].payload);
            }
          },
          children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx(CartesianGrid, { strokeDasharray: "8 8", vertical: false }),
            /* @__PURE__ */ jsxRuntimeExports.jsx(XAxis, { dataKey: "periodLabel" }),
            /* @__PURE__ */ jsxRuntimeExports.jsx(
              YAxis,
              {
                width: 80,
                orientation: "right",
                axisLine: false,
                tickLine: false
              }
            ),
            /* @__PURE__ */ jsxRuntimeExports.jsx(Tooltip, { formatter: (value) => value }),
            /* @__PURE__ */ jsxRuntimeExports.jsx(
              Bar,
              {
                dataKey: selectedMetric,
                fill: "hsl(var(--primary))",
                shape: (props) => {
                  const { x, y, width, height, periodLabel } = props;
                  const fill = isBarSelected(periodLabel) ? "hsl(var(--primary))" : "hsl(var(--primary) / 0.7)";
                  return /* @__PURE__ */ jsxRuntimeExports.jsx(
                    "rect",
                    {
                      x,
                      y,
                      width,
                      height,
                      fill,
                      rx: 4,
                      ry: 4
                    }
                  );
                }
              }
            )
          ]
        }
      ) }) })
    ] }),
    selectedData && /* @__PURE__ */ jsxRuntimeExports.jsx("h2", { className: "text-2xl font-semibold mt-2 text-center mb-0 pb-0", children: getSelectedPeriodTitle() })
  ] });
};
export {
  CustomerDashboard as C
};
//# sourceMappingURL=bar-dashboard-TOc0xTaf.js.map
