{"version": 3, "file": "house-DGVoXb1T.js", "sources": ["../../../app/routes/house.tsx"], "sourcesContent": ["import {Outlet} from \"@remix-run/react\";\r\n\r\nexport default function House() {\r\n    return <div>\r\n        <div>House Parent</div>\r\n        <div>\r\n            <Outlet/>\r\n        </div>\r\n        <div>End</div>\r\n\r\n    </div>\r\n}\r\n"], "names": ["House", "children", "jsx", "Outlet"], "mappings": ";;AAEA,SAAwBA,QAAQ;AAC5B,gDAAQ,OACJ;AAAA,IAAAC,UAAA,CAAAC,kCAAA,IAAC;MAAID,UAAY;AAAA,IAAA,CAAA,GAChBC,kCAAA,IAAA,OAAA;AAAA,MACGD,UAACC,kCAAAA,IAAAC,QAAA,CAAM,CAAA;AAAA,IACX,CAAA,GACAD,kCAAA,IAAC;MAAID,UAAG;AAAA,IAAA,CAAA,CAAA;AAAA,EAEZ,CAAA;AACJ;"}