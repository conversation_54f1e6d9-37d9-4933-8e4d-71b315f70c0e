{"version": 3, "file": "index-DdafHWkt.js", "sources": ["../../../node_modules/@radix-ui/react-dialog/dist/index.mjs"], "sourcesContent": ["\"use client\";\n\n// packages/react/dialog/src/dialog.tsx\nimport * as React from \"react\";\nimport { composeEventHandlers } from \"@radix-ui/primitive\";\nimport { useComposedRefs } from \"@radix-ui/react-compose-refs\";\nimport { createContext, createContextScope } from \"@radix-ui/react-context\";\nimport { useId } from \"@radix-ui/react-id\";\nimport { useControllableState } from \"@radix-ui/react-use-controllable-state\";\nimport { DismissableLayer } from \"@radix-ui/react-dismissable-layer\";\nimport { FocusScope } from \"@radix-ui/react-focus-scope\";\nimport { Portal as PortalPrimitive } from \"@radix-ui/react-portal\";\nimport { Presence } from \"@radix-ui/react-presence\";\nimport { Primitive } from \"@radix-ui/react-primitive\";\nimport { useFocusGuards } from \"@radix-ui/react-focus-guards\";\nimport { RemoveScroll } from \"react-remove-scroll\";\nimport { hideOthers } from \"aria-hidden\";\nimport { Slot } from \"@radix-ui/react-slot\";\nimport { Fragment, jsx, jsxs } from \"react/jsx-runtime\";\nvar DIALOG_NAME = \"Dialog\";\nvar [createDialogContext, createDialogScope] = createContextScope(DIALOG_NAME);\nvar [DialogProvider, useDialogContext] = createDialogContext(DIALOG_NAME);\nvar Dialog = (props) => {\n  const {\n    __scopeDialog,\n    children,\n    open: openProp,\n    defaultOpen,\n    onOpenChange,\n    modal = true\n  } = props;\n  const triggerRef = React.useRef(null);\n  const contentRef = React.useRef(null);\n  const [open = false, setOpen] = useControllableState({\n    prop: openProp,\n    defaultProp: defaultOpen,\n    onChange: onOpenChange\n  });\n  return /* @__PURE__ */ jsx(\n    DialogProvider,\n    {\n      scope: __scopeDialog,\n      triggerRef,\n      contentRef,\n      contentId: useId(),\n      titleId: useId(),\n      descriptionId: useId(),\n      open,\n      onOpenChange: setOpen,\n      onOpenToggle: React.useCallback(() => setOpen((prevOpen) => !prevOpen), [setOpen]),\n      modal,\n      children\n    }\n  );\n};\nDialog.displayName = DIALOG_NAME;\nvar TRIGGER_NAME = \"DialogTrigger\";\nvar DialogTrigger = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeDialog, ...triggerProps } = props;\n    const context = useDialogContext(TRIGGER_NAME, __scopeDialog);\n    const composedTriggerRef = useComposedRefs(forwardedRef, context.triggerRef);\n    return /* @__PURE__ */ jsx(\n      Primitive.button,\n      {\n        type: \"button\",\n        \"aria-haspopup\": \"dialog\",\n        \"aria-expanded\": context.open,\n        \"aria-controls\": context.contentId,\n        \"data-state\": getState(context.open),\n        ...triggerProps,\n        ref: composedTriggerRef,\n        onClick: composeEventHandlers(props.onClick, context.onOpenToggle)\n      }\n    );\n  }\n);\nDialogTrigger.displayName = TRIGGER_NAME;\nvar PORTAL_NAME = \"DialogPortal\";\nvar [PortalProvider, usePortalContext] = createDialogContext(PORTAL_NAME, {\n  forceMount: void 0\n});\nvar DialogPortal = (props) => {\n  const { __scopeDialog, forceMount, children, container } = props;\n  const context = useDialogContext(PORTAL_NAME, __scopeDialog);\n  return /* @__PURE__ */ jsx(PortalProvider, { scope: __scopeDialog, forceMount, children: React.Children.map(children, (child) => /* @__PURE__ */ jsx(Presence, { present: forceMount || context.open, children: /* @__PURE__ */ jsx(PortalPrimitive, { asChild: true, container, children: child }) })) });\n};\nDialogPortal.displayName = PORTAL_NAME;\nvar OVERLAY_NAME = \"DialogOverlay\";\nvar DialogOverlay = React.forwardRef(\n  (props, forwardedRef) => {\n    const portalContext = usePortalContext(OVERLAY_NAME, props.__scopeDialog);\n    const { forceMount = portalContext.forceMount, ...overlayProps } = props;\n    const context = useDialogContext(OVERLAY_NAME, props.__scopeDialog);\n    return context.modal ? /* @__PURE__ */ jsx(Presence, { present: forceMount || context.open, children: /* @__PURE__ */ jsx(DialogOverlayImpl, { ...overlayProps, ref: forwardedRef }) }) : null;\n  }\n);\nDialogOverlay.displayName = OVERLAY_NAME;\nvar DialogOverlayImpl = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeDialog, ...overlayProps } = props;\n    const context = useDialogContext(OVERLAY_NAME, __scopeDialog);\n    return (\n      // Make sure `Content` is scrollable even when it doesn't live inside `RemoveScroll`\n      // ie. when `Overlay` and `Content` are siblings\n      /* @__PURE__ */ jsx(RemoveScroll, { as: Slot, allowPinchZoom: true, shards: [context.contentRef], children: /* @__PURE__ */ jsx(\n        Primitive.div,\n        {\n          \"data-state\": getState(context.open),\n          ...overlayProps,\n          ref: forwardedRef,\n          style: { pointerEvents: \"auto\", ...overlayProps.style }\n        }\n      ) })\n    );\n  }\n);\nvar CONTENT_NAME = \"DialogContent\";\nvar DialogContent = React.forwardRef(\n  (props, forwardedRef) => {\n    const portalContext = usePortalContext(CONTENT_NAME, props.__scopeDialog);\n    const { forceMount = portalContext.forceMount, ...contentProps } = props;\n    const context = useDialogContext(CONTENT_NAME, props.__scopeDialog);\n    return /* @__PURE__ */ jsx(Presence, { present: forceMount || context.open, children: context.modal ? /* @__PURE__ */ jsx(DialogContentModal, { ...contentProps, ref: forwardedRef }) : /* @__PURE__ */ jsx(DialogContentNonModal, { ...contentProps, ref: forwardedRef }) });\n  }\n);\nDialogContent.displayName = CONTENT_NAME;\nvar DialogContentModal = React.forwardRef(\n  (props, forwardedRef) => {\n    const context = useDialogContext(CONTENT_NAME, props.__scopeDialog);\n    const contentRef = React.useRef(null);\n    const composedRefs = useComposedRefs(forwardedRef, context.contentRef, contentRef);\n    React.useEffect(() => {\n      const content = contentRef.current;\n      if (content) return hideOthers(content);\n    }, []);\n    return /* @__PURE__ */ jsx(\n      DialogContentImpl,\n      {\n        ...props,\n        ref: composedRefs,\n        trapFocus: context.open,\n        disableOutsidePointerEvents: true,\n        onCloseAutoFocus: composeEventHandlers(props.onCloseAutoFocus, (event) => {\n          event.preventDefault();\n          context.triggerRef.current?.focus();\n        }),\n        onPointerDownOutside: composeEventHandlers(props.onPointerDownOutside, (event) => {\n          const originalEvent = event.detail.originalEvent;\n          const ctrlLeftClick = originalEvent.button === 0 && originalEvent.ctrlKey === true;\n          const isRightClick = originalEvent.button === 2 || ctrlLeftClick;\n          if (isRightClick) event.preventDefault();\n        }),\n        onFocusOutside: composeEventHandlers(\n          props.onFocusOutside,\n          (event) => event.preventDefault()\n        )\n      }\n    );\n  }\n);\nvar DialogContentNonModal = React.forwardRef(\n  (props, forwardedRef) => {\n    const context = useDialogContext(CONTENT_NAME, props.__scopeDialog);\n    const hasInteractedOutsideRef = React.useRef(false);\n    const hasPointerDownOutsideRef = React.useRef(false);\n    return /* @__PURE__ */ jsx(\n      DialogContentImpl,\n      {\n        ...props,\n        ref: forwardedRef,\n        trapFocus: false,\n        disableOutsidePointerEvents: false,\n        onCloseAutoFocus: (event) => {\n          props.onCloseAutoFocus?.(event);\n          if (!event.defaultPrevented) {\n            if (!hasInteractedOutsideRef.current) context.triggerRef.current?.focus();\n            event.preventDefault();\n          }\n          hasInteractedOutsideRef.current = false;\n          hasPointerDownOutsideRef.current = false;\n        },\n        onInteractOutside: (event) => {\n          props.onInteractOutside?.(event);\n          if (!event.defaultPrevented) {\n            hasInteractedOutsideRef.current = true;\n            if (event.detail.originalEvent.type === \"pointerdown\") {\n              hasPointerDownOutsideRef.current = true;\n            }\n          }\n          const target = event.target;\n          const targetIsTrigger = context.triggerRef.current?.contains(target);\n          if (targetIsTrigger) event.preventDefault();\n          if (event.detail.originalEvent.type === \"focusin\" && hasPointerDownOutsideRef.current) {\n            event.preventDefault();\n          }\n        }\n      }\n    );\n  }\n);\nvar DialogContentImpl = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeDialog, trapFocus, onOpenAutoFocus, onCloseAutoFocus, ...contentProps } = props;\n    const context = useDialogContext(CONTENT_NAME, __scopeDialog);\n    const contentRef = React.useRef(null);\n    const composedRefs = useComposedRefs(forwardedRef, contentRef);\n    useFocusGuards();\n    return /* @__PURE__ */ jsxs(Fragment, { children: [\n      /* @__PURE__ */ jsx(\n        FocusScope,\n        {\n          asChild: true,\n          loop: true,\n          trapped: trapFocus,\n          onMountAutoFocus: onOpenAutoFocus,\n          onUnmountAutoFocus: onCloseAutoFocus,\n          children: /* @__PURE__ */ jsx(\n            DismissableLayer,\n            {\n              role: \"dialog\",\n              id: context.contentId,\n              \"aria-describedby\": context.descriptionId,\n              \"aria-labelledby\": context.titleId,\n              \"data-state\": getState(context.open),\n              ...contentProps,\n              ref: composedRefs,\n              onDismiss: () => context.onOpenChange(false)\n            }\n          )\n        }\n      ),\n      /* @__PURE__ */ jsxs(Fragment, { children: [\n        /* @__PURE__ */ jsx(TitleWarning, { titleId: context.titleId }),\n        /* @__PURE__ */ jsx(DescriptionWarning, { contentRef, descriptionId: context.descriptionId })\n      ] })\n    ] });\n  }\n);\nvar TITLE_NAME = \"DialogTitle\";\nvar DialogTitle = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeDialog, ...titleProps } = props;\n    const context = useDialogContext(TITLE_NAME, __scopeDialog);\n    return /* @__PURE__ */ jsx(Primitive.h2, { id: context.titleId, ...titleProps, ref: forwardedRef });\n  }\n);\nDialogTitle.displayName = TITLE_NAME;\nvar DESCRIPTION_NAME = \"DialogDescription\";\nvar DialogDescription = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeDialog, ...descriptionProps } = props;\n    const context = useDialogContext(DESCRIPTION_NAME, __scopeDialog);\n    return /* @__PURE__ */ jsx(Primitive.p, { id: context.descriptionId, ...descriptionProps, ref: forwardedRef });\n  }\n);\nDialogDescription.displayName = DESCRIPTION_NAME;\nvar CLOSE_NAME = \"DialogClose\";\nvar DialogClose = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeDialog, ...closeProps } = props;\n    const context = useDialogContext(CLOSE_NAME, __scopeDialog);\n    return /* @__PURE__ */ jsx(\n      Primitive.button,\n      {\n        type: \"button\",\n        ...closeProps,\n        ref: forwardedRef,\n        onClick: composeEventHandlers(props.onClick, () => context.onOpenChange(false))\n      }\n    );\n  }\n);\nDialogClose.displayName = CLOSE_NAME;\nfunction getState(open) {\n  return open ? \"open\" : \"closed\";\n}\nvar TITLE_WARNING_NAME = \"DialogTitleWarning\";\nvar [WarningProvider, useWarningContext] = createContext(TITLE_WARNING_NAME, {\n  contentName: CONTENT_NAME,\n  titleName: TITLE_NAME,\n  docsSlug: \"dialog\"\n});\nvar TitleWarning = ({ titleId }) => {\n  const titleWarningContext = useWarningContext(TITLE_WARNING_NAME);\n  const MESSAGE = `\\`${titleWarningContext.contentName}\\` requires a \\`${titleWarningContext.titleName}\\` for the component to be accessible for screen reader users.\n\nIf you want to hide the \\`${titleWarningContext.titleName}\\`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/${titleWarningContext.docsSlug}`;\n  React.useEffect(() => {\n    if (titleId) {\n      const hasTitle = document.getElementById(titleId);\n      if (!hasTitle) console.error(MESSAGE);\n    }\n  }, [MESSAGE, titleId]);\n  return null;\n};\nvar DESCRIPTION_WARNING_NAME = \"DialogDescriptionWarning\";\nvar DescriptionWarning = ({ contentRef, descriptionId }) => {\n  const descriptionWarningContext = useWarningContext(DESCRIPTION_WARNING_NAME);\n  const MESSAGE = `Warning: Missing \\`Description\\` or \\`aria-describedby={undefined}\\` for {${descriptionWarningContext.contentName}}.`;\n  React.useEffect(() => {\n    const describedById = contentRef.current?.getAttribute(\"aria-describedby\");\n    if (descriptionId && describedById) {\n      const hasDescription = document.getElementById(descriptionId);\n      if (!hasDescription) console.warn(MESSAGE);\n    }\n  }, [MESSAGE, contentRef, descriptionId]);\n  return null;\n};\nvar Root = Dialog;\nvar Trigger = DialogTrigger;\nvar Portal = DialogPortal;\nvar Overlay = DialogOverlay;\nvar Content = DialogContent;\nvar Title = DialogTitle;\nvar Description = DialogDescription;\nvar Close = DialogClose;\nexport {\n  Close,\n  Content,\n  Description,\n  Dialog,\n  DialogClose,\n  DialogContent,\n  DialogDescription,\n  DialogOverlay,\n  DialogPortal,\n  DialogTitle,\n  DialogTrigger,\n  Overlay,\n  Portal,\n  Root,\n  Title,\n  Trigger,\n  WarningProvider,\n  createDialogScope\n};\n//# sourceMappingURL=index.mjs.map\n"], "names": ["React.useRef", "jsx", "React.useCallback", "React.forwardRef", "React.Children", "PortalPrimitive", "RemoveScroll", "React.useEffect", "jsxs", "Fragment", "createContext"], "mappings": ";;;;;;;;;AAmBA,IAAI,cAAc;AACf,IAAC,CAAC,qBAAqB,iBAAiB,IAAI,mBAAmB,WAAW;AAC7E,IAAI,CAAC,gBAAgB,gBAAgB,IAAI,oBAAoB,WAAW;AACrE,IAAC,SAAS,CAAC,UAAU;AACtB,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA,MAAM;AAAA,IACN;AAAA,IACA;AAAA,IACA,QAAQ;AAAA,EACZ,IAAM;AACJ,QAAM,aAAaA,aAAY,OAAC,IAAI;AACpC,QAAM,aAAaA,aAAY,OAAC,IAAI;AACpC,QAAM,CAAC,OAAO,OAAO,OAAO,IAAI,qBAAqB;AAAA,IACnD,MAAM;AAAA,IACN,aAAa;AAAA,IACb,UAAU;AAAA,EACd,CAAG;AACD,SAAuBC,kCAAG;AAAA,IACxB;AAAA,IACA;AAAA,MACE,OAAO;AAAA,MACP;AAAA,MACA;AAAA,MACA,WAAW,MAAO;AAAA,MAClB,SAAS,MAAO;AAAA,MAChB,eAAe,MAAO;AAAA,MACtB;AAAA,MACA,cAAc;AAAA,MACd,cAAcC,aAAAA,YAAkB,MAAM,QAAQ,CAAC,aAAa,CAAC,QAAQ,GAAG,CAAC,OAAO,CAAC;AAAA,MACjF;AAAA,MACA;AAAA,IACN;AAAA,EACG;AACH;AACA,OAAO,cAAc;AACrB,IAAI,eAAe;AACnB,IAAI,gBAAgBC,aAAgB;AAAA,EAClC,CAAC,OAAO,iBAAiB;AACvB,UAAM,EAAE,eAAe,GAAG,aAAY,IAAK;AAC3C,UAAM,UAAU,iBAAiB,cAAc,aAAa;AAC5D,UAAM,qBAAqB,gBAAgB,cAAc,QAAQ,UAAU;AAC3E,WAAuBF,kCAAG;AAAA,MACxB,UAAU;AAAA,MACV;AAAA,QACE,MAAM;AAAA,QACN,iBAAiB;AAAA,QACjB,iBAAiB,QAAQ;AAAA,QACzB,iBAAiB,QAAQ;AAAA,QACzB,cAAc,SAAS,QAAQ,IAAI;AAAA,QACnC,GAAG;AAAA,QACH,KAAK;AAAA,QACL,SAAS,qBAAqB,MAAM,SAAS,QAAQ,YAAY;AAAA,MACzE;AAAA,IACK;AAAA,EACL;AACA;AACA,cAAc,cAAc;AAC5B,IAAI,cAAc;AAClB,IAAI,CAAC,gBAAgB,gBAAgB,IAAI,oBAAoB,aAAa;AAAA,EACxE,YAAY;AACd,CAAC;AACD,IAAI,eAAe,CAAC,UAAU;AAC5B,QAAM,EAAE,eAAe,YAAY,UAAU,UAAW,IAAG;AAC3D,QAAM,UAAU,iBAAiB,aAAa,aAAa;AAC3D,SAAuBA,kCAAG,IAAC,gBAAgB,EAAE,OAAO,eAAe,YAAY,UAAUG,aAAAA,SAAe,IAAI,UAAU,CAAC,UAA0BH,kCAAG,IAAC,UAAU,EAAE,SAAS,cAAc,QAAQ,MAAM,UAA0BA,kCAAG,IAACI,UAAiB,EAAE,SAAS,MAAM,WAAW,UAAU,MAAO,CAAA,EAAG,CAAA,CAAC,EAAC,CAAE;AAC3S;AACA,aAAa,cAAc;AAC3B,IAAI,eAAe;AACnB,IAAI,gBAAgBF,aAAgB;AAAA,EAClC,CAAC,OAAO,iBAAiB;AACvB,UAAM,gBAAgB,iBAAiB,cAAc,MAAM,aAAa;AACxE,UAAM,EAAE,aAAa,cAAc,YAAY,GAAG,aAAc,IAAG;AACnE,UAAM,UAAU,iBAAiB,cAAc,MAAM,aAAa;AAClE,WAAO,QAAQ,QAAwBF,sCAAI,UAAU,EAAE,SAAS,cAAc,QAAQ,MAAM,UAA0BA,sCAAI,mBAAmB,EAAE,GAAG,cAAc,KAAK,aAAc,CAAA,EAAG,CAAA,IAAI;AAAA,EAC9L;AACA;AACA,cAAc,cAAc;AAC5B,IAAI,oBAAoBE,aAAgB;AAAA,EACtC,CAAC,OAAO,iBAAiB;AACvB,UAAM,EAAE,eAAe,GAAG,aAAY,IAAK;AAC3C,UAAM,UAAU,iBAAiB,cAAc,aAAa;AAC5D;AAAA;AAAA;AAAA,MAGkBF,kCAAAA,IAAIK,mBAAc,EAAE,IAAI,MAAM,gBAAgB,MAAM,QAAQ,CAAC,QAAQ,UAAU,GAAG,UAA0BL,kCAAG;AAAA,QAC7H,UAAU;AAAA,QACV;AAAA,UACE,cAAc,SAAS,QAAQ,IAAI;AAAA,UACnC,GAAG;AAAA,UACH,KAAK;AAAA,UACL,OAAO,EAAE,eAAe,QAAQ,GAAG,aAAa,MAAK;AAAA,QAC/D;AAAA,MACA,EAAS,CAAA;AAAA;AAAA,EAET;AACA;AACA,IAAI,eAAe;AAChB,IAAC,gBAAgBE,aAAgB;AAAA,EAClC,CAAC,OAAO,iBAAiB;AACvB,UAAM,gBAAgB,iBAAiB,cAAc,MAAM,aAAa;AACxE,UAAM,EAAE,aAAa,cAAc,YAAY,GAAG,aAAc,IAAG;AACnE,UAAM,UAAU,iBAAiB,cAAc,MAAM,aAAa;AAClE,WAAuBF,sCAAI,UAAU,EAAE,SAAS,cAAc,QAAQ,MAAM,UAAU,QAAQ,QAAwBA,kCAAG,IAAC,oBAAoB,EAAE,GAAG,cAAc,KAAK,aAAc,CAAA,IAAoBA,kCAAAA,IAAI,uBAAuB,EAAE,GAAG,cAAc,KAAK,aAAY,CAAE,EAAC,CAAE;AAAA,EAChR;AACA;AACA,cAAc,cAAc;AAC5B,IAAI,qBAAqBE,aAAgB;AAAA,EACvC,CAAC,OAAO,iBAAiB;AACvB,UAAM,UAAU,iBAAiB,cAAc,MAAM,aAAa;AAClE,UAAM,aAAaH,aAAY,OAAC,IAAI;AACpC,UAAM,eAAe,gBAAgB,cAAc,QAAQ,YAAY,UAAU;AACjFO,iBAAAA,UAAgB,MAAM;AACpB,YAAM,UAAU,WAAW;AAC3B,UAAI,QAAS,QAAO,WAAW,OAAO;AAAA,IACvC,GAAE,EAAE;AACL,WAAuBN,kCAAG;AAAA,MACxB;AAAA,MACA;AAAA,QACE,GAAG;AAAA,QACH,KAAK;AAAA,QACL,WAAW,QAAQ;AAAA,QACnB,6BAA6B;AAAA,QAC7B,kBAAkB,qBAAqB,MAAM,kBAAkB,CAAC,UAAU;;AACxE,gBAAM,eAAgB;AACtB,wBAAQ,WAAW,YAAnB,mBAA4B;AAAA,QACtC,CAAS;AAAA,QACD,sBAAsB,qBAAqB,MAAM,sBAAsB,CAAC,UAAU;AAChF,gBAAM,gBAAgB,MAAM,OAAO;AACnC,gBAAM,gBAAgB,cAAc,WAAW,KAAK,cAAc,YAAY;AAC9E,gBAAM,eAAe,cAAc,WAAW,KAAK;AACnD,cAAI,aAAc,OAAM,eAAgB;AAAA,QAClD,CAAS;AAAA,QACD,gBAAgB;AAAA,UACd,MAAM;AAAA,UACN,CAAC,UAAU,MAAM,eAAc;AAAA,QACzC;AAAA,MACA;AAAA,IACK;AAAA,EACL;AACA;AACA,IAAI,wBAAwBE,aAAgB;AAAA,EAC1C,CAAC,OAAO,iBAAiB;AACvB,UAAM,UAAU,iBAAiB,cAAc,MAAM,aAAa;AAClE,UAAM,0BAA0BH,aAAY,OAAC,KAAK;AAClD,UAAM,2BAA2BA,aAAY,OAAC,KAAK;AACnD,WAAuBC,kCAAG;AAAA,MACxB;AAAA,MACA;AAAA,QACE,GAAG;AAAA,QACH,KAAK;AAAA,QACL,WAAW;AAAA,QACX,6BAA6B;AAAA,QAC7B,kBAAkB,CAAC,UAAU;;AAC3B,sBAAM,qBAAN,+BAAyB;AACzB,cAAI,CAAC,MAAM,kBAAkB;AAC3B,gBAAI,CAAC,wBAAwB,QAAS,eAAQ,WAAW,YAAnB,mBAA4B;AAClE,kBAAM,eAAgB;AAAA,UAClC;AACU,kCAAwB,UAAU;AAClC,mCAAyB,UAAU;AAAA,QACpC;AAAA,QACD,mBAAmB,CAAC,UAAU;;AAC5B,sBAAM,sBAAN,+BAA0B;AAC1B,cAAI,CAAC,MAAM,kBAAkB;AAC3B,oCAAwB,UAAU;AAClC,gBAAI,MAAM,OAAO,cAAc,SAAS,eAAe;AACrD,uCAAyB,UAAU;AAAA,YACjD;AAAA,UACA;AACU,gBAAM,SAAS,MAAM;AACrB,gBAAM,mBAAkB,aAAQ,WAAW,YAAnB,mBAA4B,SAAS;AAC7D,cAAI,gBAAiB,OAAM,eAAgB;AAC3C,cAAI,MAAM,OAAO,cAAc,SAAS,aAAa,yBAAyB,SAAS;AACrF,kBAAM,eAAgB;AAAA,UAClC;AAAA,QACA;AAAA,MACA;AAAA,IACK;AAAA,EACL;AACA;AACA,IAAI,oBAAoBE,aAAgB;AAAA,EACtC,CAAC,OAAO,iBAAiB;AACvB,UAAM,EAAE,eAAe,WAAW,iBAAiB,kBAAkB,GAAG,aAAY,IAAK;AACzF,UAAM,UAAU,iBAAiB,cAAc,aAAa;AAC5D,UAAM,aAAaH,aAAY,OAAC,IAAI;AACpC,UAAM,eAAe,gBAAgB,cAAc,UAAU;AAC7D,mBAAgB;AAChB,WAAuBQ,kCAAI,KAACC,4BAAU,EAAE,UAAU;AAAA,MAChCR,kCAAG;AAAA,QACjB;AAAA,QACA;AAAA,UACE,SAAS;AAAA,UACT,MAAM;AAAA,UACN,SAAS;AAAA,UACT,kBAAkB;AAAA,UAClB,oBAAoB;AAAA,UACpB,UAA0BA,kCAAG;AAAA,YAC3B;AAAA,YACA;AAAA,cACE,MAAM;AAAA,cACN,IAAI,QAAQ;AAAA,cACZ,oBAAoB,QAAQ;AAAA,cAC5B,mBAAmB,QAAQ;AAAA,cAC3B,cAAc,SAAS,QAAQ,IAAI;AAAA,cACnC,GAAG;AAAA,cACH,KAAK;AAAA,cACL,WAAW,MAAM,QAAQ,aAAa,KAAK;AAAA,YACzD;AAAA,UACA;AAAA,QACA;AAAA,MACO;AAAA,MACeO,uCAAKC,kBAAAA,UAAU,EAAE,UAAU;AAAA,QACzBR,kCAAAA,IAAI,cAAc,EAAE,SAAS,QAAQ,QAAO,CAAE;AAAA,QAC9CA,kCAAG,IAAC,oBAAoB,EAAE,YAAY,eAAe,QAAQ,cAAe,CAAA;AAAA,MACpG,EAAS,CAAA;AAAA,IACT,GAAO;AAAA,EACP;AACA;AACA,IAAI,aAAa;AACd,IAAC,cAAcE,aAAgB;AAAA,EAChC,CAAC,OAAO,iBAAiB;AACvB,UAAM,EAAE,eAAe,GAAG,WAAU,IAAK;AACzC,UAAM,UAAU,iBAAiB,YAAY,aAAa;AAC1D,WAAuBF,sCAAI,UAAU,IAAI,EAAE,IAAI,QAAQ,SAAS,GAAG,YAAY,KAAK,aAAY,CAAE;AAAA,EACtG;AACA;AACA,YAAY,cAAc;AAC1B,IAAI,mBAAmB;AACvB,IAAI,oBAAoBE,aAAgB;AAAA,EACtC,CAAC,OAAO,iBAAiB;AACvB,UAAM,EAAE,eAAe,GAAG,iBAAgB,IAAK;AAC/C,UAAM,UAAU,iBAAiB,kBAAkB,aAAa;AAChE,WAAuBF,sCAAI,UAAU,GAAG,EAAE,IAAI,QAAQ,eAAe,GAAG,kBAAkB,KAAK,aAAY,CAAE;AAAA,EACjH;AACA;AACA,kBAAkB,cAAc;AAChC,IAAI,aAAa;AACjB,IAAI,cAAcE,aAAgB;AAAA,EAChC,CAAC,OAAO,iBAAiB;AACvB,UAAM,EAAE,eAAe,GAAG,WAAU,IAAK;AACzC,UAAM,UAAU,iBAAiB,YAAY,aAAa;AAC1D,WAAuBF,kCAAG;AAAA,MACxB,UAAU;AAAA,MACV;AAAA,QACE,MAAM;AAAA,QACN,GAAG;AAAA,QACH,KAAK;AAAA,QACL,SAAS,qBAAqB,MAAM,SAAS,MAAM,QAAQ,aAAa,KAAK,CAAC;AAAA,MACtF;AAAA,IACK;AAAA,EACL;AACA;AACA,YAAY,cAAc;AAC1B,SAAS,SAAS,MAAM;AACtB,SAAO,OAAO,SAAS;AACzB;AACA,IAAI,qBAAqB;AACtB,IAAC,CAAC,iBAAiB,iBAAiB,IAAIS,eAAc,oBAAoB;AAAA,EAC3E,aAAa;AAAA,EACb,WAAW;AAAA,EACX,UAAU;AACZ,CAAC;AACD,IAAI,eAAe,CAAC,EAAE,cAAc;AAClC,QAAM,sBAAsB,kBAAkB,kBAAkB;AAChE,QAAM,UAAU,KAAK,oBAAoB,WAAW,mBAAmB,oBAAoB,SAAS;AAAA;AAAA,4BAE1E,oBAAoB,SAAS;AAAA;AAAA,4EAEmB,oBAAoB,QAAQ;AACtGH,eAAAA,UAAgB,MAAM;AACpB,QAAI,SAAS;AACX,YAAM,WAAW,SAAS,eAAe,OAAO;AAChD,UAAI,CAAC,SAAU,SAAQ,MAAM,OAAO;AAAA,IAC1C;AAAA,EACA,GAAK,CAAC,SAAS,OAAO,CAAC;AACrB,SAAO;AACT;AACA,IAAI,2BAA2B;AAC/B,IAAI,qBAAqB,CAAC,EAAE,YAAY,oBAAoB;AAC1D,QAAM,4BAA4B,kBAAkB,wBAAwB;AAC5E,QAAM,UAAU,6EAA6E,0BAA0B,WAAW;AAClIA,eAAAA,UAAgB,MAAM;;AACpB,UAAM,iBAAgB,gBAAW,YAAX,mBAAoB,aAAa;AACvD,QAAI,iBAAiB,eAAe;AAClC,YAAM,iBAAiB,SAAS,eAAe,aAAa;AAC5D,UAAI,CAAC,eAAgB,SAAQ,KAAK,OAAO;AAAA,IAC/C;AAAA,EACG,GAAE,CAAC,SAAS,YAAY,aAAa,CAAC;AACvC,SAAO;AACT;AACG,IAAC,OAAO;AACR,IAAC,UAAU;AACX,IAAC,SAAS;AACV,IAAC,UAAU;AACX,IAAC,UAAU;AACX,IAAC,QAAQ;AACT,IAAC,cAAc;AACf,IAAC,QAAQ;", "x_google_ignoreList": [0]}