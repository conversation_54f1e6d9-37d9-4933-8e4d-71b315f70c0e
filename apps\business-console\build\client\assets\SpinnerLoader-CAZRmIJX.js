import { r as reactExports, j as jsxRuntimeExports } from "./jsx-runtime-bB2y7OuD.js";
const SpinnerLoader = ({ loading, size }) => {
  const [active, setActive] = reactExports.useState(loading);
  reactExports.useEffect(() => {
    setActive(loading);
  }, [loading]);
  return active && /* @__PURE__ */ jsxRuntimeExports.jsx(
    "div",
    {
      role: "progressbar",
      "aria-valuetext": active ? "Loading" : void 0,
      "aria-hidden": !active,
      className: `fixed inset-0 flex items-center justify-center bg-black bg-opacity-40 pointer-events-none z-50 p-4`,
      children: /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "p-6 text-center", children: /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: `${size ? `w-${size} h-${size}` : "w-16 h-16"}  border-4 border-gray-200 border-t-4 border-t-primary rounded-full animate-spin mx-auto mb-4` }) })
    }
  );
};
export {
  SpinnerLoader as S
};
//# sourceMappingURL=SpinnerLoader-CAZRmIJX.js.map
