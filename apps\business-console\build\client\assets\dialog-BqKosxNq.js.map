{"version": 3, "file": "dialog-BqKosxNq.js", "sources": ["../../../app/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\r\nimport { X } from \"lucide-react\"\r\n\r\nimport { cn } from \"~/lib/utils\"\r\n\r\nconst Dialog = DialogPrimitive.Root\r\n\r\nconst DialogTrigger = DialogPrimitive.Trigger\r\n\r\nconst DialogPortal = DialogPrimitive.Portal\r\n\r\nconst DialogClose = DialogPrimitive.Close\r\n\r\nconst DialogOverlay = React.forwardRef<\r\n  React.ElementRef<typeof DialogPrimitive.Overlay>,\r\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Overlay>\r\n>(({ className, ...props }, ref) => (\r\n  <DialogPrimitive.Overlay\r\n    ref={ref}\r\n    className={cn(\r\n      \"fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nDialogOverlay.displayName = DialogPrimitive.Overlay.displayName\r\n\r\nconst DialogContent = React.forwardRef<\r\n  React.ElementRef<typeof DialogPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Content>\r\n>(({ className, children, ...props }, ref) => (\r\n  <DialogPortal>\r\n    <DialogOverlay />\r\n    <DialogPrimitive.Content\r\n      ref={ref}\r\n      className={cn(\r\n        \"fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      {children}\r\n      <DialogPrimitive.Close className=\"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground\">\r\n        <X className=\"h-4 w-4\" />\r\n        <span className=\"sr-only\">Close</span>\r\n      </DialogPrimitive.Close>\r\n    </DialogPrimitive.Content>\r\n  </DialogPortal>\r\n))\r\nDialogContent.displayName = DialogPrimitive.Content.displayName\r\n\r\nconst DialogHeader = ({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLDivElement>) => (\r\n  <div\r\n    className={cn(\r\n      \"flex flex-col space-y-1.5 text-center sm:text-left\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n)\r\nDialogHeader.displayName = \"DialogHeader\"\r\n\r\nconst DialogFooter = ({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLDivElement>) => (\r\n  <div\r\n    className={cn(\r\n      \"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n)\r\nDialogFooter.displayName = \"DialogFooter\"\r\n\r\nconst DialogTitle = React.forwardRef<\r\n  React.ElementRef<typeof DialogPrimitive.Title>,\r\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Title>\r\n>(({ className, ...props }, ref) => (\r\n  <DialogPrimitive.Title\r\n    ref={ref}\r\n    className={cn(\r\n      \"text-lg font-semibold leading-none tracking-tight\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nDialogTitle.displayName = DialogPrimitive.Title.displayName\r\n\r\nconst DialogDescription = React.forwardRef<\r\n  React.ElementRef<typeof DialogPrimitive.Description>,\r\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Description>\r\n>(({ className, ...props }, ref) => (\r\n  <DialogPrimitive.Description\r\n    ref={ref}\r\n    className={cn(\"text-sm text-muted-foreground\", className)}\r\n    {...props}\r\n  />\r\n))\r\nDialogDescription.displayName = DialogPrimitive.Description.displayName\r\n\r\nexport {\r\n  Dialog,\r\n  DialogPortal,\r\n  DialogOverlay,\r\n  DialogClose,\r\n  DialogTrigger,\r\n  DialogContent,\r\n  DialogHeader,\r\n  DialogFooter,\r\n  DialogTitle,\r\n  DialogDescription,\r\n}\r\n"], "names": ["DialogPrimitive.Root", "DialogPrimitive.Trigger", "DialogPrimitive.Portal", "DialogPrimitive.Close", "React.forwardRef", "jsx", "DialogPrimitive.Overlay", "jsxs", "DialogPrimitive.Content", "DialogPrimitive.Title", "DialogPrimitive.Description"], "mappings": ";;;;AAQA,MAAM,SAASA;AAEf,MAAM,gBAAgBC;AAEtB,MAAM,eAAeC;AAErB,MAAM,cAAcC;AAEpB,MAAM,gBAAgBC,aAGpB,WAAA,CAAC,EAAE,WAAW,GAAG,MAAM,GAAG,QAC1BC,kCAAA;AAAA,EAACC;AAAAA,EAAA;AAAA,IACC;AAAA,IACA,WAAW;AAAA,MACT;AAAA,MACA;AAAA,IACF;AAAA,IACC,GAAG;AAAA,EAAA;AACN,CACD;AACD,cAAc,cAAcA,QAAwB;AAEpD,MAAM,gBAAgBF,aAGpB,WAAA,CAAC,EAAE,WAAW,UAAU,GAAG,MAAM,GAAG,QACpCG,kCAAAA,KAAC,cACC,EAAA,UAAA;AAAA,EAAAF,kCAAA,IAAC,eAAc,EAAA;AAAA,EACfE,kCAAA;AAAA,IAACC;AAAAA,IAAA;AAAA,MACC;AAAA,MACA,WAAW;AAAA,QACT;AAAA,QACA;AAAA,MACF;AAAA,MACC,GAAG;AAAA,MAEH,UAAA;AAAA,QAAA;AAAA,QACAD,kCAAAA,KAAAJ,OAAA,EAAsB,WAAU,iRAC/B,UAAA;AAAA,UAACE,kCAAAA,IAAA,GAAA,EAAE,WAAU,UAAU,CAAA;AAAA,UACtBA,kCAAA,IAAA,QAAA,EAAK,WAAU,WAAU,UAAK,QAAA,CAAA;AAAA,QAAA,EACjC,CAAA;AAAA,MAAA;AAAA,IAAA;AAAA,EAAA;AACF,GACF,CACD;AACD,cAAc,cAAcG,QAAwB;AAEpD,MAAM,eAAe,CAAC;AAAA,EACpB;AAAA,EACA,GAAG;AACL,MACEH,kCAAA;AAAA,EAAC;AAAA,EAAA;AAAA,IACC,WAAW;AAAA,MACT;AAAA,MACA;AAAA,IACF;AAAA,IACC,GAAG;AAAA,EAAA;AACN;AAEF,aAAa,cAAc;AAE3B,MAAM,eAAe,CAAC;AAAA,EACpB;AAAA,EACA,GAAG;AACL,MACEA,kCAAA;AAAA,EAAC;AAAA,EAAA;AAAA,IACC,WAAW;AAAA,MACT;AAAA,MACA;AAAA,IACF;AAAA,IACC,GAAG;AAAA,EAAA;AACN;AAEF,aAAa,cAAc;AAErB,MAAA,cAAcD,aAGlB,WAAA,CAAC,EAAE,WAAW,GAAG,MAAM,GAAG,QAC1BC,kCAAA;AAAA,EAACI;AAAAA,EAAA;AAAA,IACC;AAAA,IACA,WAAW;AAAA,MACT;AAAA,MACA;AAAA,IACF;AAAA,IACC,GAAG;AAAA,EAAA;AACN,CACD;AACD,YAAY,cAAcA,MAAsB;AAE1C,MAAA,oBAAoBL,aAGxB,WAAA,CAAC,EAAE,WAAW,GAAG,MAAM,GAAG,QAC1BC,kCAAA;AAAA,EAACK;AAAAA,EAAA;AAAA,IACC;AAAA,IACA,WAAW,GAAG,iCAAiC,SAAS;AAAA,IACvD,GAAG;AAAA,EAAA;AACN,CACD;AACD,kBAAkB,cAAcA,YAA4B;"}