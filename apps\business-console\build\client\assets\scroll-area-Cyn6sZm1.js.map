{"version": 3, "file": "scroll-area-Cyn6sZm1.js", "sources": ["../../../node_modules/@radix-ui/react-scroll-area/dist/index.mjs", "../../../app/components/ui/scroll-area.tsx"], "sourcesContent": ["\"use client\";\n\n// packages/react/scroll-area/src/scroll-area.tsx\nimport * as React2 from \"react\";\nimport { Primitive } from \"@radix-ui/react-primitive\";\nimport { Presence } from \"@radix-ui/react-presence\";\nimport { createContextScope } from \"@radix-ui/react-context\";\nimport { useComposedRefs } from \"@radix-ui/react-compose-refs\";\nimport { useCallbackRef } from \"@radix-ui/react-use-callback-ref\";\nimport { useDirection } from \"@radix-ui/react-direction\";\nimport { useLayoutEffect } from \"@radix-ui/react-use-layout-effect\";\nimport { clamp } from \"@radix-ui/number\";\nimport { composeEventHandlers } from \"@radix-ui/primitive\";\n\n// packages/react/scroll-area/src/use-state-machine.ts\nimport * as React from \"react\";\nfunction useStateMachine(initialState, machine) {\n  return React.useReducer((state, event) => {\n    const nextState = machine[state][event];\n    return nextState ?? state;\n  }, initialState);\n}\n\n// packages/react/scroll-area/src/scroll-area.tsx\nimport { Fragment, jsx, jsxs } from \"react/jsx-runtime\";\nvar SCROLL_AREA_NAME = \"ScrollArea\";\nvar [createScrollAreaContext, createScrollAreaScope] = createContextScope(SCROLL_AREA_NAME);\nvar [ScrollAreaProvider, useScrollAreaContext] = createScrollAreaContext(SCROLL_AREA_NAME);\nvar ScrollArea = React2.forwardRef(\n  (props, forwardedRef) => {\n    const {\n      __scopeScrollArea,\n      type = \"hover\",\n      dir,\n      scrollHideDelay = 600,\n      ...scrollAreaProps\n    } = props;\n    const [scrollArea, setScrollArea] = React2.useState(null);\n    const [viewport, setViewport] = React2.useState(null);\n    const [content, setContent] = React2.useState(null);\n    const [scrollbarX, setScrollbarX] = React2.useState(null);\n    const [scrollbarY, setScrollbarY] = React2.useState(null);\n    const [cornerWidth, setCornerWidth] = React2.useState(0);\n    const [cornerHeight, setCornerHeight] = React2.useState(0);\n    const [scrollbarXEnabled, setScrollbarXEnabled] = React2.useState(false);\n    const [scrollbarYEnabled, setScrollbarYEnabled] = React2.useState(false);\n    const composedRefs = useComposedRefs(forwardedRef, (node) => setScrollArea(node));\n    const direction = useDirection(dir);\n    return /* @__PURE__ */ jsx(\n      ScrollAreaProvider,\n      {\n        scope: __scopeScrollArea,\n        type,\n        dir: direction,\n        scrollHideDelay,\n        scrollArea,\n        viewport,\n        onViewportChange: setViewport,\n        content,\n        onContentChange: setContent,\n        scrollbarX,\n        onScrollbarXChange: setScrollbarX,\n        scrollbarXEnabled,\n        onScrollbarXEnabledChange: setScrollbarXEnabled,\n        scrollbarY,\n        onScrollbarYChange: setScrollbarY,\n        scrollbarYEnabled,\n        onScrollbarYEnabledChange: setScrollbarYEnabled,\n        onCornerWidthChange: setCornerWidth,\n        onCornerHeightChange: setCornerHeight,\n        children: /* @__PURE__ */ jsx(\n          Primitive.div,\n          {\n            dir: direction,\n            ...scrollAreaProps,\n            ref: composedRefs,\n            style: {\n              position: \"relative\",\n              // Pass corner sizes as CSS vars to reduce re-renders of context consumers\n              [\"--radix-scroll-area-corner-width\"]: cornerWidth + \"px\",\n              [\"--radix-scroll-area-corner-height\"]: cornerHeight + \"px\",\n              ...props.style\n            }\n          }\n        )\n      }\n    );\n  }\n);\nScrollArea.displayName = SCROLL_AREA_NAME;\nvar VIEWPORT_NAME = \"ScrollAreaViewport\";\nvar ScrollAreaViewport = React2.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeScrollArea, children, nonce, ...viewportProps } = props;\n    const context = useScrollAreaContext(VIEWPORT_NAME, __scopeScrollArea);\n    const ref = React2.useRef(null);\n    const composedRefs = useComposedRefs(forwardedRef, ref, context.onViewportChange);\n    return /* @__PURE__ */ jsxs(Fragment, { children: [\n      /* @__PURE__ */ jsx(\n        \"style\",\n        {\n          dangerouslySetInnerHTML: {\n            __html: `[data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}`\n          },\n          nonce\n        }\n      ),\n      /* @__PURE__ */ jsx(\n        Primitive.div,\n        {\n          \"data-radix-scroll-area-viewport\": \"\",\n          ...viewportProps,\n          ref: composedRefs,\n          style: {\n            /**\n             * We don't support `visible` because the intention is to have at least one scrollbar\n             * if this component is used and `visible` will behave like `auto` in that case\n             * https://developer.mozilla.org/en-US/docs/Web/CSS/overflow#description\n             *\n             * We don't handle `auto` because the intention is for the native implementation\n             * to be hidden if using this component. We just want to ensure the node is scrollable\n             * so could have used either `scroll` or `auto` here. We picked `scroll` to prevent\n             * the browser from having to work out whether to render native scrollbars or not,\n             * we tell it to with the intention of hiding them in CSS.\n             */\n            overflowX: context.scrollbarXEnabled ? \"scroll\" : \"hidden\",\n            overflowY: context.scrollbarYEnabled ? \"scroll\" : \"hidden\",\n            ...props.style\n          },\n          children: /* @__PURE__ */ jsx(\"div\", { ref: context.onContentChange, style: { minWidth: \"100%\", display: \"table\" }, children })\n        }\n      )\n    ] });\n  }\n);\nScrollAreaViewport.displayName = VIEWPORT_NAME;\nvar SCROLLBAR_NAME = \"ScrollAreaScrollbar\";\nvar ScrollAreaScrollbar = React2.forwardRef(\n  (props, forwardedRef) => {\n    const { forceMount, ...scrollbarProps } = props;\n    const context = useScrollAreaContext(SCROLLBAR_NAME, props.__scopeScrollArea);\n    const { onScrollbarXEnabledChange, onScrollbarYEnabledChange } = context;\n    const isHorizontal = props.orientation === \"horizontal\";\n    React2.useEffect(() => {\n      isHorizontal ? onScrollbarXEnabledChange(true) : onScrollbarYEnabledChange(true);\n      return () => {\n        isHorizontal ? onScrollbarXEnabledChange(false) : onScrollbarYEnabledChange(false);\n      };\n    }, [isHorizontal, onScrollbarXEnabledChange, onScrollbarYEnabledChange]);\n    return context.type === \"hover\" ? /* @__PURE__ */ jsx(ScrollAreaScrollbarHover, { ...scrollbarProps, ref: forwardedRef, forceMount }) : context.type === \"scroll\" ? /* @__PURE__ */ jsx(ScrollAreaScrollbarScroll, { ...scrollbarProps, ref: forwardedRef, forceMount }) : context.type === \"auto\" ? /* @__PURE__ */ jsx(ScrollAreaScrollbarAuto, { ...scrollbarProps, ref: forwardedRef, forceMount }) : context.type === \"always\" ? /* @__PURE__ */ jsx(ScrollAreaScrollbarVisible, { ...scrollbarProps, ref: forwardedRef }) : null;\n  }\n);\nScrollAreaScrollbar.displayName = SCROLLBAR_NAME;\nvar ScrollAreaScrollbarHover = React2.forwardRef((props, forwardedRef) => {\n  const { forceMount, ...scrollbarProps } = props;\n  const context = useScrollAreaContext(SCROLLBAR_NAME, props.__scopeScrollArea);\n  const [visible, setVisible] = React2.useState(false);\n  React2.useEffect(() => {\n    const scrollArea = context.scrollArea;\n    let hideTimer = 0;\n    if (scrollArea) {\n      const handlePointerEnter = () => {\n        window.clearTimeout(hideTimer);\n        setVisible(true);\n      };\n      const handlePointerLeave = () => {\n        hideTimer = window.setTimeout(() => setVisible(false), context.scrollHideDelay);\n      };\n      scrollArea.addEventListener(\"pointerenter\", handlePointerEnter);\n      scrollArea.addEventListener(\"pointerleave\", handlePointerLeave);\n      return () => {\n        window.clearTimeout(hideTimer);\n        scrollArea.removeEventListener(\"pointerenter\", handlePointerEnter);\n        scrollArea.removeEventListener(\"pointerleave\", handlePointerLeave);\n      };\n    }\n  }, [context.scrollArea, context.scrollHideDelay]);\n  return /* @__PURE__ */ jsx(Presence, { present: forceMount || visible, children: /* @__PURE__ */ jsx(\n    ScrollAreaScrollbarAuto,\n    {\n      \"data-state\": visible ? \"visible\" : \"hidden\",\n      ...scrollbarProps,\n      ref: forwardedRef\n    }\n  ) });\n});\nvar ScrollAreaScrollbarScroll = React2.forwardRef((props, forwardedRef) => {\n  const { forceMount, ...scrollbarProps } = props;\n  const context = useScrollAreaContext(SCROLLBAR_NAME, props.__scopeScrollArea);\n  const isHorizontal = props.orientation === \"horizontal\";\n  const debounceScrollEnd = useDebounceCallback(() => send(\"SCROLL_END\"), 100);\n  const [state, send] = useStateMachine(\"hidden\", {\n    hidden: {\n      SCROLL: \"scrolling\"\n    },\n    scrolling: {\n      SCROLL_END: \"idle\",\n      POINTER_ENTER: \"interacting\"\n    },\n    interacting: {\n      SCROLL: \"interacting\",\n      POINTER_LEAVE: \"idle\"\n    },\n    idle: {\n      HIDE: \"hidden\",\n      SCROLL: \"scrolling\",\n      POINTER_ENTER: \"interacting\"\n    }\n  });\n  React2.useEffect(() => {\n    if (state === \"idle\") {\n      const hideTimer = window.setTimeout(() => send(\"HIDE\"), context.scrollHideDelay);\n      return () => window.clearTimeout(hideTimer);\n    }\n  }, [state, context.scrollHideDelay, send]);\n  React2.useEffect(() => {\n    const viewport = context.viewport;\n    const scrollDirection = isHorizontal ? \"scrollLeft\" : \"scrollTop\";\n    if (viewport) {\n      let prevScrollPos = viewport[scrollDirection];\n      const handleScroll = () => {\n        const scrollPos = viewport[scrollDirection];\n        const hasScrollInDirectionChanged = prevScrollPos !== scrollPos;\n        if (hasScrollInDirectionChanged) {\n          send(\"SCROLL\");\n          debounceScrollEnd();\n        }\n        prevScrollPos = scrollPos;\n      };\n      viewport.addEventListener(\"scroll\", handleScroll);\n      return () => viewport.removeEventListener(\"scroll\", handleScroll);\n    }\n  }, [context.viewport, isHorizontal, send, debounceScrollEnd]);\n  return /* @__PURE__ */ jsx(Presence, { present: forceMount || state !== \"hidden\", children: /* @__PURE__ */ jsx(\n    ScrollAreaScrollbarVisible,\n    {\n      \"data-state\": state === \"hidden\" ? \"hidden\" : \"visible\",\n      ...scrollbarProps,\n      ref: forwardedRef,\n      onPointerEnter: composeEventHandlers(props.onPointerEnter, () => send(\"POINTER_ENTER\")),\n      onPointerLeave: composeEventHandlers(props.onPointerLeave, () => send(\"POINTER_LEAVE\"))\n    }\n  ) });\n});\nvar ScrollAreaScrollbarAuto = React2.forwardRef((props, forwardedRef) => {\n  const context = useScrollAreaContext(SCROLLBAR_NAME, props.__scopeScrollArea);\n  const { forceMount, ...scrollbarProps } = props;\n  const [visible, setVisible] = React2.useState(false);\n  const isHorizontal = props.orientation === \"horizontal\";\n  const handleResize = useDebounceCallback(() => {\n    if (context.viewport) {\n      const isOverflowX = context.viewport.offsetWidth < context.viewport.scrollWidth;\n      const isOverflowY = context.viewport.offsetHeight < context.viewport.scrollHeight;\n      setVisible(isHorizontal ? isOverflowX : isOverflowY);\n    }\n  }, 10);\n  useResizeObserver(context.viewport, handleResize);\n  useResizeObserver(context.content, handleResize);\n  return /* @__PURE__ */ jsx(Presence, { present: forceMount || visible, children: /* @__PURE__ */ jsx(\n    ScrollAreaScrollbarVisible,\n    {\n      \"data-state\": visible ? \"visible\" : \"hidden\",\n      ...scrollbarProps,\n      ref: forwardedRef\n    }\n  ) });\n});\nvar ScrollAreaScrollbarVisible = React2.forwardRef((props, forwardedRef) => {\n  const { orientation = \"vertical\", ...scrollbarProps } = props;\n  const context = useScrollAreaContext(SCROLLBAR_NAME, props.__scopeScrollArea);\n  const thumbRef = React2.useRef(null);\n  const pointerOffsetRef = React2.useRef(0);\n  const [sizes, setSizes] = React2.useState({\n    content: 0,\n    viewport: 0,\n    scrollbar: { size: 0, paddingStart: 0, paddingEnd: 0 }\n  });\n  const thumbRatio = getThumbRatio(sizes.viewport, sizes.content);\n  const commonProps = {\n    ...scrollbarProps,\n    sizes,\n    onSizesChange: setSizes,\n    hasThumb: Boolean(thumbRatio > 0 && thumbRatio < 1),\n    onThumbChange: (thumb) => thumbRef.current = thumb,\n    onThumbPointerUp: () => pointerOffsetRef.current = 0,\n    onThumbPointerDown: (pointerPos) => pointerOffsetRef.current = pointerPos\n  };\n  function getScrollPosition(pointerPos, dir) {\n    return getScrollPositionFromPointer(pointerPos, pointerOffsetRef.current, sizes, dir);\n  }\n  if (orientation === \"horizontal\") {\n    return /* @__PURE__ */ jsx(\n      ScrollAreaScrollbarX,\n      {\n        ...commonProps,\n        ref: forwardedRef,\n        onThumbPositionChange: () => {\n          if (context.viewport && thumbRef.current) {\n            const scrollPos = context.viewport.scrollLeft;\n            const offset = getThumbOffsetFromScroll(scrollPos, sizes, context.dir);\n            thumbRef.current.style.transform = `translate3d(${offset}px, 0, 0)`;\n          }\n        },\n        onWheelScroll: (scrollPos) => {\n          if (context.viewport) context.viewport.scrollLeft = scrollPos;\n        },\n        onDragScroll: (pointerPos) => {\n          if (context.viewport) {\n            context.viewport.scrollLeft = getScrollPosition(pointerPos, context.dir);\n          }\n        }\n      }\n    );\n  }\n  if (orientation === \"vertical\") {\n    return /* @__PURE__ */ jsx(\n      ScrollAreaScrollbarY,\n      {\n        ...commonProps,\n        ref: forwardedRef,\n        onThumbPositionChange: () => {\n          if (context.viewport && thumbRef.current) {\n            const scrollPos = context.viewport.scrollTop;\n            const offset = getThumbOffsetFromScroll(scrollPos, sizes);\n            thumbRef.current.style.transform = `translate3d(0, ${offset}px, 0)`;\n          }\n        },\n        onWheelScroll: (scrollPos) => {\n          if (context.viewport) context.viewport.scrollTop = scrollPos;\n        },\n        onDragScroll: (pointerPos) => {\n          if (context.viewport) context.viewport.scrollTop = getScrollPosition(pointerPos);\n        }\n      }\n    );\n  }\n  return null;\n});\nvar ScrollAreaScrollbarX = React2.forwardRef((props, forwardedRef) => {\n  const { sizes, onSizesChange, ...scrollbarProps } = props;\n  const context = useScrollAreaContext(SCROLLBAR_NAME, props.__scopeScrollArea);\n  const [computedStyle, setComputedStyle] = React2.useState();\n  const ref = React2.useRef(null);\n  const composeRefs = useComposedRefs(forwardedRef, ref, context.onScrollbarXChange);\n  React2.useEffect(() => {\n    if (ref.current) setComputedStyle(getComputedStyle(ref.current));\n  }, [ref]);\n  return /* @__PURE__ */ jsx(\n    ScrollAreaScrollbarImpl,\n    {\n      \"data-orientation\": \"horizontal\",\n      ...scrollbarProps,\n      ref: composeRefs,\n      sizes,\n      style: {\n        bottom: 0,\n        left: context.dir === \"rtl\" ? \"var(--radix-scroll-area-corner-width)\" : 0,\n        right: context.dir === \"ltr\" ? \"var(--radix-scroll-area-corner-width)\" : 0,\n        [\"--radix-scroll-area-thumb-width\"]: getThumbSize(sizes) + \"px\",\n        ...props.style\n      },\n      onThumbPointerDown: (pointerPos) => props.onThumbPointerDown(pointerPos.x),\n      onDragScroll: (pointerPos) => props.onDragScroll(pointerPos.x),\n      onWheelScroll: (event, maxScrollPos) => {\n        if (context.viewport) {\n          const scrollPos = context.viewport.scrollLeft + event.deltaX;\n          props.onWheelScroll(scrollPos);\n          if (isScrollingWithinScrollbarBounds(scrollPos, maxScrollPos)) {\n            event.preventDefault();\n          }\n        }\n      },\n      onResize: () => {\n        if (ref.current && context.viewport && computedStyle) {\n          onSizesChange({\n            content: context.viewport.scrollWidth,\n            viewport: context.viewport.offsetWidth,\n            scrollbar: {\n              size: ref.current.clientWidth,\n              paddingStart: toInt(computedStyle.paddingLeft),\n              paddingEnd: toInt(computedStyle.paddingRight)\n            }\n          });\n        }\n      }\n    }\n  );\n});\nvar ScrollAreaScrollbarY = React2.forwardRef((props, forwardedRef) => {\n  const { sizes, onSizesChange, ...scrollbarProps } = props;\n  const context = useScrollAreaContext(SCROLLBAR_NAME, props.__scopeScrollArea);\n  const [computedStyle, setComputedStyle] = React2.useState();\n  const ref = React2.useRef(null);\n  const composeRefs = useComposedRefs(forwardedRef, ref, context.onScrollbarYChange);\n  React2.useEffect(() => {\n    if (ref.current) setComputedStyle(getComputedStyle(ref.current));\n  }, [ref]);\n  return /* @__PURE__ */ jsx(\n    ScrollAreaScrollbarImpl,\n    {\n      \"data-orientation\": \"vertical\",\n      ...scrollbarProps,\n      ref: composeRefs,\n      sizes,\n      style: {\n        top: 0,\n        right: context.dir === \"ltr\" ? 0 : void 0,\n        left: context.dir === \"rtl\" ? 0 : void 0,\n        bottom: \"var(--radix-scroll-area-corner-height)\",\n        [\"--radix-scroll-area-thumb-height\"]: getThumbSize(sizes) + \"px\",\n        ...props.style\n      },\n      onThumbPointerDown: (pointerPos) => props.onThumbPointerDown(pointerPos.y),\n      onDragScroll: (pointerPos) => props.onDragScroll(pointerPos.y),\n      onWheelScroll: (event, maxScrollPos) => {\n        if (context.viewport) {\n          const scrollPos = context.viewport.scrollTop + event.deltaY;\n          props.onWheelScroll(scrollPos);\n          if (isScrollingWithinScrollbarBounds(scrollPos, maxScrollPos)) {\n            event.preventDefault();\n          }\n        }\n      },\n      onResize: () => {\n        if (ref.current && context.viewport && computedStyle) {\n          onSizesChange({\n            content: context.viewport.scrollHeight,\n            viewport: context.viewport.offsetHeight,\n            scrollbar: {\n              size: ref.current.clientHeight,\n              paddingStart: toInt(computedStyle.paddingTop),\n              paddingEnd: toInt(computedStyle.paddingBottom)\n            }\n          });\n        }\n      }\n    }\n  );\n});\nvar [ScrollbarProvider, useScrollbarContext] = createScrollAreaContext(SCROLLBAR_NAME);\nvar ScrollAreaScrollbarImpl = React2.forwardRef((props, forwardedRef) => {\n  const {\n    __scopeScrollArea,\n    sizes,\n    hasThumb,\n    onThumbChange,\n    onThumbPointerUp,\n    onThumbPointerDown,\n    onThumbPositionChange,\n    onDragScroll,\n    onWheelScroll,\n    onResize,\n    ...scrollbarProps\n  } = props;\n  const context = useScrollAreaContext(SCROLLBAR_NAME, __scopeScrollArea);\n  const [scrollbar, setScrollbar] = React2.useState(null);\n  const composeRefs = useComposedRefs(forwardedRef, (node) => setScrollbar(node));\n  const rectRef = React2.useRef(null);\n  const prevWebkitUserSelectRef = React2.useRef(\"\");\n  const viewport = context.viewport;\n  const maxScrollPos = sizes.content - sizes.viewport;\n  const handleWheelScroll = useCallbackRef(onWheelScroll);\n  const handleThumbPositionChange = useCallbackRef(onThumbPositionChange);\n  const handleResize = useDebounceCallback(onResize, 10);\n  function handleDragScroll(event) {\n    if (rectRef.current) {\n      const x = event.clientX - rectRef.current.left;\n      const y = event.clientY - rectRef.current.top;\n      onDragScroll({ x, y });\n    }\n  }\n  React2.useEffect(() => {\n    const handleWheel = (event) => {\n      const element = event.target;\n      const isScrollbarWheel = scrollbar?.contains(element);\n      if (isScrollbarWheel) handleWheelScroll(event, maxScrollPos);\n    };\n    document.addEventListener(\"wheel\", handleWheel, { passive: false });\n    return () => document.removeEventListener(\"wheel\", handleWheel, { passive: false });\n  }, [viewport, scrollbar, maxScrollPos, handleWheelScroll]);\n  React2.useEffect(handleThumbPositionChange, [sizes, handleThumbPositionChange]);\n  useResizeObserver(scrollbar, handleResize);\n  useResizeObserver(context.content, handleResize);\n  return /* @__PURE__ */ jsx(\n    ScrollbarProvider,\n    {\n      scope: __scopeScrollArea,\n      scrollbar,\n      hasThumb,\n      onThumbChange: useCallbackRef(onThumbChange),\n      onThumbPointerUp: useCallbackRef(onThumbPointerUp),\n      onThumbPositionChange: handleThumbPositionChange,\n      onThumbPointerDown: useCallbackRef(onThumbPointerDown),\n      children: /* @__PURE__ */ jsx(\n        Primitive.div,\n        {\n          ...scrollbarProps,\n          ref: composeRefs,\n          style: { position: \"absolute\", ...scrollbarProps.style },\n          onPointerDown: composeEventHandlers(props.onPointerDown, (event) => {\n            const mainPointer = 0;\n            if (event.button === mainPointer) {\n              const element = event.target;\n              element.setPointerCapture(event.pointerId);\n              rectRef.current = scrollbar.getBoundingClientRect();\n              prevWebkitUserSelectRef.current = document.body.style.webkitUserSelect;\n              document.body.style.webkitUserSelect = \"none\";\n              if (context.viewport) context.viewport.style.scrollBehavior = \"auto\";\n              handleDragScroll(event);\n            }\n          }),\n          onPointerMove: composeEventHandlers(props.onPointerMove, handleDragScroll),\n          onPointerUp: composeEventHandlers(props.onPointerUp, (event) => {\n            const element = event.target;\n            if (element.hasPointerCapture(event.pointerId)) {\n              element.releasePointerCapture(event.pointerId);\n            }\n            document.body.style.webkitUserSelect = prevWebkitUserSelectRef.current;\n            if (context.viewport) context.viewport.style.scrollBehavior = \"\";\n            rectRef.current = null;\n          })\n        }\n      )\n    }\n  );\n});\nvar THUMB_NAME = \"ScrollAreaThumb\";\nvar ScrollAreaThumb = React2.forwardRef(\n  (props, forwardedRef) => {\n    const { forceMount, ...thumbProps } = props;\n    const scrollbarContext = useScrollbarContext(THUMB_NAME, props.__scopeScrollArea);\n    return /* @__PURE__ */ jsx(Presence, { present: forceMount || scrollbarContext.hasThumb, children: /* @__PURE__ */ jsx(ScrollAreaThumbImpl, { ref: forwardedRef, ...thumbProps }) });\n  }\n);\nvar ScrollAreaThumbImpl = React2.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeScrollArea, style, ...thumbProps } = props;\n    const scrollAreaContext = useScrollAreaContext(THUMB_NAME, __scopeScrollArea);\n    const scrollbarContext = useScrollbarContext(THUMB_NAME, __scopeScrollArea);\n    const { onThumbPositionChange } = scrollbarContext;\n    const composedRef = useComposedRefs(\n      forwardedRef,\n      (node) => scrollbarContext.onThumbChange(node)\n    );\n    const removeUnlinkedScrollListenerRef = React2.useRef(void 0);\n    const debounceScrollEnd = useDebounceCallback(() => {\n      if (removeUnlinkedScrollListenerRef.current) {\n        removeUnlinkedScrollListenerRef.current();\n        removeUnlinkedScrollListenerRef.current = void 0;\n      }\n    }, 100);\n    React2.useEffect(() => {\n      const viewport = scrollAreaContext.viewport;\n      if (viewport) {\n        const handleScroll = () => {\n          debounceScrollEnd();\n          if (!removeUnlinkedScrollListenerRef.current) {\n            const listener = addUnlinkedScrollListener(viewport, onThumbPositionChange);\n            removeUnlinkedScrollListenerRef.current = listener;\n            onThumbPositionChange();\n          }\n        };\n        onThumbPositionChange();\n        viewport.addEventListener(\"scroll\", handleScroll);\n        return () => viewport.removeEventListener(\"scroll\", handleScroll);\n      }\n    }, [scrollAreaContext.viewport, debounceScrollEnd, onThumbPositionChange]);\n    return /* @__PURE__ */ jsx(\n      Primitive.div,\n      {\n        \"data-state\": scrollbarContext.hasThumb ? \"visible\" : \"hidden\",\n        ...thumbProps,\n        ref: composedRef,\n        style: {\n          width: \"var(--radix-scroll-area-thumb-width)\",\n          height: \"var(--radix-scroll-area-thumb-height)\",\n          ...style\n        },\n        onPointerDownCapture: composeEventHandlers(props.onPointerDownCapture, (event) => {\n          const thumb = event.target;\n          const thumbRect = thumb.getBoundingClientRect();\n          const x = event.clientX - thumbRect.left;\n          const y = event.clientY - thumbRect.top;\n          scrollbarContext.onThumbPointerDown({ x, y });\n        }),\n        onPointerUp: composeEventHandlers(props.onPointerUp, scrollbarContext.onThumbPointerUp)\n      }\n    );\n  }\n);\nScrollAreaThumb.displayName = THUMB_NAME;\nvar CORNER_NAME = \"ScrollAreaCorner\";\nvar ScrollAreaCorner = React2.forwardRef(\n  (props, forwardedRef) => {\n    const context = useScrollAreaContext(CORNER_NAME, props.__scopeScrollArea);\n    const hasBothScrollbarsVisible = Boolean(context.scrollbarX && context.scrollbarY);\n    const hasCorner = context.type !== \"scroll\" && hasBothScrollbarsVisible;\n    return hasCorner ? /* @__PURE__ */ jsx(ScrollAreaCornerImpl, { ...props, ref: forwardedRef }) : null;\n  }\n);\nScrollAreaCorner.displayName = CORNER_NAME;\nvar ScrollAreaCornerImpl = React2.forwardRef((props, forwardedRef) => {\n  const { __scopeScrollArea, ...cornerProps } = props;\n  const context = useScrollAreaContext(CORNER_NAME, __scopeScrollArea);\n  const [width, setWidth] = React2.useState(0);\n  const [height, setHeight] = React2.useState(0);\n  const hasSize = Boolean(width && height);\n  useResizeObserver(context.scrollbarX, () => {\n    const height2 = context.scrollbarX?.offsetHeight || 0;\n    context.onCornerHeightChange(height2);\n    setHeight(height2);\n  });\n  useResizeObserver(context.scrollbarY, () => {\n    const width2 = context.scrollbarY?.offsetWidth || 0;\n    context.onCornerWidthChange(width2);\n    setWidth(width2);\n  });\n  return hasSize ? /* @__PURE__ */ jsx(\n    Primitive.div,\n    {\n      ...cornerProps,\n      ref: forwardedRef,\n      style: {\n        width,\n        height,\n        position: \"absolute\",\n        right: context.dir === \"ltr\" ? 0 : void 0,\n        left: context.dir === \"rtl\" ? 0 : void 0,\n        bottom: 0,\n        ...props.style\n      }\n    }\n  ) : null;\n});\nfunction toInt(value) {\n  return value ? parseInt(value, 10) : 0;\n}\nfunction getThumbRatio(viewportSize, contentSize) {\n  const ratio = viewportSize / contentSize;\n  return isNaN(ratio) ? 0 : ratio;\n}\nfunction getThumbSize(sizes) {\n  const ratio = getThumbRatio(sizes.viewport, sizes.content);\n  const scrollbarPadding = sizes.scrollbar.paddingStart + sizes.scrollbar.paddingEnd;\n  const thumbSize = (sizes.scrollbar.size - scrollbarPadding) * ratio;\n  return Math.max(thumbSize, 18);\n}\nfunction getScrollPositionFromPointer(pointerPos, pointerOffset, sizes, dir = \"ltr\") {\n  const thumbSizePx = getThumbSize(sizes);\n  const thumbCenter = thumbSizePx / 2;\n  const offset = pointerOffset || thumbCenter;\n  const thumbOffsetFromEnd = thumbSizePx - offset;\n  const minPointerPos = sizes.scrollbar.paddingStart + offset;\n  const maxPointerPos = sizes.scrollbar.size - sizes.scrollbar.paddingEnd - thumbOffsetFromEnd;\n  const maxScrollPos = sizes.content - sizes.viewport;\n  const scrollRange = dir === \"ltr\" ? [0, maxScrollPos] : [maxScrollPos * -1, 0];\n  const interpolate = linearScale([minPointerPos, maxPointerPos], scrollRange);\n  return interpolate(pointerPos);\n}\nfunction getThumbOffsetFromScroll(scrollPos, sizes, dir = \"ltr\") {\n  const thumbSizePx = getThumbSize(sizes);\n  const scrollbarPadding = sizes.scrollbar.paddingStart + sizes.scrollbar.paddingEnd;\n  const scrollbar = sizes.scrollbar.size - scrollbarPadding;\n  const maxScrollPos = sizes.content - sizes.viewport;\n  const maxThumbPos = scrollbar - thumbSizePx;\n  const scrollClampRange = dir === \"ltr\" ? [0, maxScrollPos] : [maxScrollPos * -1, 0];\n  const scrollWithoutMomentum = clamp(scrollPos, scrollClampRange);\n  const interpolate = linearScale([0, maxScrollPos], [0, maxThumbPos]);\n  return interpolate(scrollWithoutMomentum);\n}\nfunction linearScale(input, output) {\n  return (value) => {\n    if (input[0] === input[1] || output[0] === output[1]) return output[0];\n    const ratio = (output[1] - output[0]) / (input[1] - input[0]);\n    return output[0] + ratio * (value - input[0]);\n  };\n}\nfunction isScrollingWithinScrollbarBounds(scrollPos, maxScrollPos) {\n  return scrollPos > 0 && scrollPos < maxScrollPos;\n}\nvar addUnlinkedScrollListener = (node, handler = () => {\n}) => {\n  let prevPosition = { left: node.scrollLeft, top: node.scrollTop };\n  let rAF = 0;\n  (function loop() {\n    const position = { left: node.scrollLeft, top: node.scrollTop };\n    const isHorizontalScroll = prevPosition.left !== position.left;\n    const isVerticalScroll = prevPosition.top !== position.top;\n    if (isHorizontalScroll || isVerticalScroll) handler();\n    prevPosition = position;\n    rAF = window.requestAnimationFrame(loop);\n  })();\n  return () => window.cancelAnimationFrame(rAF);\n};\nfunction useDebounceCallback(callback, delay) {\n  const handleCallback = useCallbackRef(callback);\n  const debounceTimerRef = React2.useRef(0);\n  React2.useEffect(() => () => window.clearTimeout(debounceTimerRef.current), []);\n  return React2.useCallback(() => {\n    window.clearTimeout(debounceTimerRef.current);\n    debounceTimerRef.current = window.setTimeout(handleCallback, delay);\n  }, [handleCallback, delay]);\n}\nfunction useResizeObserver(element, onResize) {\n  const handleResize = useCallbackRef(onResize);\n  useLayoutEffect(() => {\n    let rAF = 0;\n    if (element) {\n      const resizeObserver = new ResizeObserver(() => {\n        cancelAnimationFrame(rAF);\n        rAF = window.requestAnimationFrame(handleResize);\n      });\n      resizeObserver.observe(element);\n      return () => {\n        window.cancelAnimationFrame(rAF);\n        resizeObserver.unobserve(element);\n      };\n    }\n  }, [element, handleResize]);\n}\nvar Root = ScrollArea;\nvar Viewport = ScrollAreaViewport;\nvar Scrollbar = ScrollAreaScrollbar;\nvar Thumb = ScrollAreaThumb;\nvar Corner = ScrollAreaCorner;\nexport {\n  Corner,\n  Root,\n  ScrollArea,\n  ScrollAreaCorner,\n  ScrollAreaScrollbar,\n  ScrollAreaThumb,\n  ScrollAreaViewport,\n  Scrollbar,\n  Thumb,\n  Viewport,\n  createScrollAreaScope\n};\n//# sourceMappingURL=index.mjs.map\n", "import * as React from \"react\"\r\nimport * as ScrollAreaPrimitive from \"@radix-ui/react-scroll-area\"\r\n\r\nimport { cn } from \"~/lib/utils\"\r\n\r\nconst ScrollArea = React.forwardRef<\r\n  React.ElementRef<typeof ScrollAreaPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof ScrollAreaPrimitive.Root>\r\n>(({ className, children, ...props }, ref) => (\r\n  <ScrollAreaPrimitive.Root\r\n    ref={ref}\r\n    className={cn(\"relative overflow-hidden\", className)}\r\n    {...props}\r\n  >\r\n    <ScrollAreaPrimitive.Viewport className=\"h-full w-full rounded-[inherit]\">\r\n      {children}\r\n    </ScrollAreaPrimitive.Viewport>\r\n    <ScrollBar />\r\n    <ScrollAreaPrimitive.Corner />\r\n  </ScrollAreaPrimitive.Root>\r\n))\r\nScrollArea.displayName = ScrollAreaPrimitive.Root.displayName\r\n\r\nconst ScrollBar = React.forwardRef<\r\n  React.ElementRef<typeof ScrollAreaPrimitive.ScrollAreaScrollbar>,\r\n  React.ComponentPropsWithoutRef<typeof ScrollAreaPrimitive.ScrollAreaScrollbar>\r\n>(({ className, orientation = \"vertical\", ...props }, ref) => (\r\n  <ScrollAreaPrimitive.ScrollAreaScrollbar\r\n    ref={ref}\r\n    orientation={orientation}\r\n    className={cn(\r\n      \"flex touch-none select-none transition-colors\",\r\n      orientation === \"vertical\" &&\r\n        \"h-full w-2.5 border-l border-l-transparent p-[1px]\",\r\n      orientation === \"horizontal\" &&\r\n        \"h-2.5 flex-col border-t border-t-transparent p-[1px]\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    <ScrollAreaPrimitive.ScrollAreaThumb className=\"relative flex-1 rounded-full bg-border\" />\r\n  </ScrollAreaPrimitive.ScrollAreaScrollbar>\r\n))\r\nScrollBar.displayName = ScrollAreaPrimitive.ScrollAreaScrollbar.displayName\r\n\r\nexport { ScrollArea, ScrollBar }\r\n"], "names": ["React.useReducer", "ScrollArea", "React2.forwardRef", "React2.useState", "jsx", "React2.useRef", "jsxs", "Fragment", "React2.useEffect", "React2.useCallback", "useLayoutEffect", "React.forwardRef", "ScrollAreaPrimitive.Root", "ScrollAreaPrimitive.Viewport", "ScrollAreaPrimitive.Corner", "ScrollAreaPrimitive.ScrollAreaScrollbar", "ScrollAreaPrimitive.ScrollAreaThumb"], "mappings": ";;;;;;;;AAgBA,SAAS,gBAAgB,cAAc,SAAS;AAC9C,SAAOA,aAAgB,WAAC,CAAC,OAAO,UAAU;AACxC,UAAM,YAAY,QAAQ,KAAK,EAAE,KAAK;AACtC,WAAO,aAAa;AAAA,EACrB,GAAE,YAAY;AACjB;AAIA,IAAI,mBAAmB;AACvB,IAAI,CAAC,yBAAyB,qBAAqB,IAAI,mBAAmB,gBAAgB;AAC1F,IAAI,CAAC,oBAAoB,oBAAoB,IAAI,wBAAwB,gBAAgB;AACzF,IAAIC,eAAaC,aAAiB;AAAA,EAChC,CAAC,OAAO,iBAAiB;AACvB,UAAM;AAAA,MACJ;AAAA,MACA,OAAO;AAAA,MACP;AAAA,MACA,kBAAkB;AAAA,MAClB,GAAG;AAAA,IACT,IAAQ;AACJ,UAAM,CAAC,YAAY,aAAa,IAAIC,aAAAA,SAAgB,IAAI;AACxD,UAAM,CAAC,UAAU,WAAW,IAAIA,aAAAA,SAAgB,IAAI;AACpD,UAAM,CAAC,SAAS,UAAU,IAAIA,aAAAA,SAAgB,IAAI;AAClD,UAAM,CAAC,YAAY,aAAa,IAAIA,aAAAA,SAAgB,IAAI;AACxD,UAAM,CAAC,YAAY,aAAa,IAAIA,aAAAA,SAAgB,IAAI;AACxD,UAAM,CAAC,aAAa,cAAc,IAAIA,aAAAA,SAAgB,CAAC;AACvD,UAAM,CAAC,cAAc,eAAe,IAAIA,aAAAA,SAAgB,CAAC;AACzD,UAAM,CAAC,mBAAmB,oBAAoB,IAAIA,aAAAA,SAAgB,KAAK;AACvE,UAAM,CAAC,mBAAmB,oBAAoB,IAAIA,aAAAA,SAAgB,KAAK;AACvE,UAAM,eAAe,gBAAgB,cAAc,CAAC,SAAS,cAAc,IAAI,CAAC;AAChF,UAAM,YAAY,aAAa,GAAG;AAClC,WAAuBC,kCAAG;AAAA,MACxB;AAAA,MACA;AAAA,QACE,OAAO;AAAA,QACP;AAAA,QACA,KAAK;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,QACA,kBAAkB;AAAA,QAClB;AAAA,QACA,iBAAiB;AAAA,QACjB;AAAA,QACA,oBAAoB;AAAA,QACpB;AAAA,QACA,2BAA2B;AAAA,QAC3B;AAAA,QACA,oBAAoB;AAAA,QACpB;AAAA,QACA,2BAA2B;AAAA,QAC3B,qBAAqB;AAAA,QACrB,sBAAsB;AAAA,QACtB,UAA0BA,kCAAG;AAAA,UAC3B,UAAU;AAAA,UACV;AAAA,YACE,KAAK;AAAA,YACL,GAAG;AAAA,YACH,KAAK;AAAA,YACL,OAAO;AAAA,cACL,UAAU;AAAA;AAAA,cAEV,CAAC,kCAAkC,GAAG,cAAc;AAAA,cACpD,CAAC,mCAAmC,GAAG,eAAe;AAAA,cACtD,GAAG,MAAM;AAAA,YACvB;AAAA,UACA;AAAA,QACA;AAAA,MACA;AAAA,IACK;AAAA,EACL;AACA;AACAH,aAAW,cAAc;AACzB,IAAI,gBAAgB;AACpB,IAAI,qBAAqBC,aAAiB;AAAA,EACxC,CAAC,OAAO,iBAAiB;AACvB,UAAM,EAAE,mBAAmB,UAAU,OAAO,GAAG,cAAe,IAAG;AACjE,UAAM,UAAU,qBAAqB,eAAe,iBAAiB;AACrE,UAAM,MAAMG,aAAa,OAAC,IAAI;AAC9B,UAAM,eAAe,gBAAgB,cAAc,KAAK,QAAQ,gBAAgB;AAChF,WAAuBC,kCAAI,KAACC,4BAAU,EAAE,UAAU;AAAA,MAChCH,kCAAG;AAAA,QACjB;AAAA,QACA;AAAA,UACE,yBAAyB;AAAA,YACvB,QAAQ;AAAA,UACT;AAAA,UACD;AAAA,QACV;AAAA,MACO;AAAA,MACeA,kCAAG;AAAA,QACjB,UAAU;AAAA,QACV;AAAA,UACE,mCAAmC;AAAA,UACnC,GAAG;AAAA,UACH,KAAK;AAAA,UACL,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,YAYL,WAAW,QAAQ,oBAAoB,WAAW;AAAA,YAClD,WAAW,QAAQ,oBAAoB,WAAW;AAAA,YAClD,GAAG,MAAM;AAAA,UACV;AAAA,UACD,UAA0BA,kCAAAA,IAAI,OAAO,EAAE,KAAK,QAAQ,iBAAiB,OAAO,EAAE,UAAU,QAAQ,SAAS,QAAO,GAAI,SAAU,CAAA;AAAA,QACxI;AAAA,MACA;AAAA,IACA,GAAO;AAAA,EACP;AACA;AACA,mBAAmB,cAAc;AACjC,IAAI,iBAAiB;AACrB,IAAI,sBAAsBF,aAAiB;AAAA,EACzC,CAAC,OAAO,iBAAiB;AACvB,UAAM,EAAE,YAAY,GAAG,eAAc,IAAK;AAC1C,UAAM,UAAU,qBAAqB,gBAAgB,MAAM,iBAAiB;AAC5E,UAAM,EAAE,2BAA2B,0BAAyB,IAAK;AACjE,UAAM,eAAe,MAAM,gBAAgB;AAC3CM,iBAAAA,UAAiB,MAAM;AACrB,qBAAe,0BAA0B,IAAI,IAAI,0BAA0B,IAAI;AAC/E,aAAO,MAAM;AACX,uBAAe,0BAA0B,KAAK,IAAI,0BAA0B,KAAK;AAAA,MAClF;AAAA,IACF,GAAE,CAAC,cAAc,2BAA2B,yBAAyB,CAAC;AACvE,WAAO,QAAQ,SAAS,UAA0BJ,kCAAG,IAAC,0BAA0B,EAAE,GAAG,gBAAgB,KAAK,cAAc,WAAY,CAAA,IAAI,QAAQ,SAAS,WAA2BA,kCAAG,IAAC,2BAA2B,EAAE,GAAG,gBAAgB,KAAK,cAAc,WAAU,CAAE,IAAI,QAAQ,SAAS,SAAyBA,sCAAI,yBAAyB,EAAE,GAAG,gBAAgB,KAAK,cAAc,WAAY,CAAA,IAAI,QAAQ,SAAS,WAA2BA,kCAAAA,IAAI,4BAA4B,EAAE,GAAG,gBAAgB,KAAK,aAAc,CAAA,IAAI;AAAA,EACtgB;AACA;AACA,oBAAoB,cAAc;AAClC,IAAI,2BAA2BF,aAAiB,WAAC,CAAC,OAAO,iBAAiB;AACxE,QAAM,EAAE,YAAY,GAAG,eAAc,IAAK;AAC1C,QAAM,UAAU,qBAAqB,gBAAgB,MAAM,iBAAiB;AAC5E,QAAM,CAAC,SAAS,UAAU,IAAIC,aAAAA,SAAgB,KAAK;AACnDK,eAAAA,UAAiB,MAAM;AACrB,UAAM,aAAa,QAAQ;AAC3B,QAAI,YAAY;AAChB,QAAI,YAAY;AACd,YAAM,qBAAqB,MAAM;AAC/B,eAAO,aAAa,SAAS;AAC7B,mBAAW,IAAI;AAAA,MAChB;AACD,YAAM,qBAAqB,MAAM;AAC/B,oBAAY,OAAO,WAAW,MAAM,WAAW,KAAK,GAAG,QAAQ,eAAe;AAAA,MAC/E;AACD,iBAAW,iBAAiB,gBAAgB,kBAAkB;AAC9D,iBAAW,iBAAiB,gBAAgB,kBAAkB;AAC9D,aAAO,MAAM;AACX,eAAO,aAAa,SAAS;AAC7B,mBAAW,oBAAoB,gBAAgB,kBAAkB;AACjE,mBAAW,oBAAoB,gBAAgB,kBAAkB;AAAA,MAClE;AAAA,IACP;AAAA,EACG,GAAE,CAAC,QAAQ,YAAY,QAAQ,eAAe,CAAC;AAChD,SAAuBJ,kCAAAA,IAAI,UAAU,EAAE,SAAS,cAAc,SAAS,UAA0BA,kCAAG;AAAA,IAClG;AAAA,IACA;AAAA,MACE,cAAc,UAAU,YAAY;AAAA,MACpC,GAAG;AAAA,MACH,KAAK;AAAA,IACX;AAAA,EACA,GAAK;AACL,CAAC;AACD,IAAI,4BAA4BF,aAAiB,WAAC,CAAC,OAAO,iBAAiB;AACzE,QAAM,EAAE,YAAY,GAAG,eAAc,IAAK;AAC1C,QAAM,UAAU,qBAAqB,gBAAgB,MAAM,iBAAiB;AAC5E,QAAM,eAAe,MAAM,gBAAgB;AAC3C,QAAM,oBAAoB,oBAAoB,MAAM,KAAK,YAAY,GAAG,GAAG;AAC3E,QAAM,CAAC,OAAO,IAAI,IAAI,gBAAgB,UAAU;AAAA,IAC9C,QAAQ;AAAA,MACN,QAAQ;AAAA,IACT;AAAA,IACD,WAAW;AAAA,MACT,YAAY;AAAA,MACZ,eAAe;AAAA,IAChB;AAAA,IACD,aAAa;AAAA,MACX,QAAQ;AAAA,MACR,eAAe;AAAA,IAChB;AAAA,IACD,MAAM;AAAA,MACJ,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,eAAe;AAAA,IACrB;AAAA,EACA,CAAG;AACDM,eAAAA,UAAiB,MAAM;AACrB,QAAI,UAAU,QAAQ;AACpB,YAAM,YAAY,OAAO,WAAW,MAAM,KAAK,MAAM,GAAG,QAAQ,eAAe;AAC/E,aAAO,MAAM,OAAO,aAAa,SAAS;AAAA,IAChD;AAAA,EACG,GAAE,CAAC,OAAO,QAAQ,iBAAiB,IAAI,CAAC;AACzCA,eAAAA,UAAiB,MAAM;AACrB,UAAM,WAAW,QAAQ;AACzB,UAAM,kBAAkB,eAAe,eAAe;AACtD,QAAI,UAAU;AACZ,UAAI,gBAAgB,SAAS,eAAe;AAC5C,YAAM,eAAe,MAAM;AACzB,cAAM,YAAY,SAAS,eAAe;AAC1C,cAAM,8BAA8B,kBAAkB;AACtD,YAAI,6BAA6B;AAC/B,eAAK,QAAQ;AACb,4BAAmB;AAAA,QAC7B;AACQ,wBAAgB;AAAA,MACjB;AACD,eAAS,iBAAiB,UAAU,YAAY;AAChD,aAAO,MAAM,SAAS,oBAAoB,UAAU,YAAY;AAAA,IACtE;AAAA,EACA,GAAK,CAAC,QAAQ,UAAU,cAAc,MAAM,iBAAiB,CAAC;AAC5D,SAAuBJ,kCAAG,IAAC,UAAU,EAAE,SAAS,cAAc,UAAU,UAAU,UAA0BA,kCAAG;AAAA,IAC7G;AAAA,IACA;AAAA,MACE,cAAc,UAAU,WAAW,WAAW;AAAA,MAC9C,GAAG;AAAA,MACH,KAAK;AAAA,MACL,gBAAgB,qBAAqB,MAAM,gBAAgB,MAAM,KAAK,eAAe,CAAC;AAAA,MACtF,gBAAgB,qBAAqB,MAAM,gBAAgB,MAAM,KAAK,eAAe,CAAC;AAAA,IAC5F;AAAA,EACA,GAAK;AACL,CAAC;AACD,IAAI,0BAA0BF,aAAiB,WAAC,CAAC,OAAO,iBAAiB;AACvE,QAAM,UAAU,qBAAqB,gBAAgB,MAAM,iBAAiB;AAC5E,QAAM,EAAE,YAAY,GAAG,eAAc,IAAK;AAC1C,QAAM,CAAC,SAAS,UAAU,IAAIC,aAAAA,SAAgB,KAAK;AACnD,QAAM,eAAe,MAAM,gBAAgB;AAC3C,QAAM,eAAe,oBAAoB,MAAM;AAC7C,QAAI,QAAQ,UAAU;AACpB,YAAM,cAAc,QAAQ,SAAS,cAAc,QAAQ,SAAS;AACpE,YAAM,cAAc,QAAQ,SAAS,eAAe,QAAQ,SAAS;AACrE,iBAAW,eAAe,cAAc,WAAW;AAAA,IACzD;AAAA,EACG,GAAE,EAAE;AACL,oBAAkB,QAAQ,UAAU,YAAY;AAChD,oBAAkB,QAAQ,SAAS,YAAY;AAC/C,SAAuBC,kCAAAA,IAAI,UAAU,EAAE,SAAS,cAAc,SAAS,UAA0BA,kCAAG;AAAA,IAClG;AAAA,IACA;AAAA,MACE,cAAc,UAAU,YAAY;AAAA,MACpC,GAAG;AAAA,MACH,KAAK;AAAA,IACX;AAAA,EACA,GAAK;AACL,CAAC;AACD,IAAI,6BAA6BF,aAAiB,WAAC,CAAC,OAAO,iBAAiB;AAC1E,QAAM,EAAE,cAAc,YAAY,GAAG,eAAgB,IAAG;AACxD,QAAM,UAAU,qBAAqB,gBAAgB,MAAM,iBAAiB;AAC5E,QAAM,WAAWG,aAAa,OAAC,IAAI;AACnC,QAAM,mBAAmBA,aAAa,OAAC,CAAC;AACxC,QAAM,CAAC,OAAO,QAAQ,IAAIF,sBAAgB;AAAA,IACxC,SAAS;AAAA,IACT,UAAU;AAAA,IACV,WAAW,EAAE,MAAM,GAAG,cAAc,GAAG,YAAY,EAAC;AAAA,EACxD,CAAG;AACD,QAAM,aAAa,cAAc,MAAM,UAAU,MAAM,OAAO;AAC9D,QAAM,cAAc;AAAA,IAClB,GAAG;AAAA,IACH;AAAA,IACA,eAAe;AAAA,IACf,UAAU,QAAQ,aAAa,KAAK,aAAa,CAAC;AAAA,IAClD,eAAe,CAAC,UAAU,SAAS,UAAU;AAAA,IAC7C,kBAAkB,MAAM,iBAAiB,UAAU;AAAA,IACnD,oBAAoB,CAAC,eAAe,iBAAiB,UAAU;AAAA,EAChE;AACD,WAAS,kBAAkB,YAAY,KAAK;AAC1C,WAAO,6BAA6B,YAAY,iBAAiB,SAAS,OAAO,GAAG;AAAA,EACxF;AACE,MAAI,gBAAgB,cAAc;AAChC,WAAuBC,kCAAG;AAAA,MACxB;AAAA,MACA;AAAA,QACE,GAAG;AAAA,QACH,KAAK;AAAA,QACL,uBAAuB,MAAM;AAC3B,cAAI,QAAQ,YAAY,SAAS,SAAS;AACxC,kBAAM,YAAY,QAAQ,SAAS;AACnC,kBAAM,SAAS,yBAAyB,WAAW,OAAO,QAAQ,GAAG;AACrE,qBAAS,QAAQ,MAAM,YAAY,eAAe,MAAM;AAAA,UACpE;AAAA,QACS;AAAA,QACD,eAAe,CAAC,cAAc;AAC5B,cAAI,QAAQ,SAAU,SAAQ,SAAS,aAAa;AAAA,QACrD;AAAA,QACD,cAAc,CAAC,eAAe;AAC5B,cAAI,QAAQ,UAAU;AACpB,oBAAQ,SAAS,aAAa,kBAAkB,YAAY,QAAQ,GAAG;AAAA,UACnF;AAAA,QACA;AAAA,MACA;AAAA,IACK;AAAA,EACL;AACE,MAAI,gBAAgB,YAAY;AAC9B,WAAuBA,kCAAG;AAAA,MACxB;AAAA,MACA;AAAA,QACE,GAAG;AAAA,QACH,KAAK;AAAA,QACL,uBAAuB,MAAM;AAC3B,cAAI,QAAQ,YAAY,SAAS,SAAS;AACxC,kBAAM,YAAY,QAAQ,SAAS;AACnC,kBAAM,SAAS,yBAAyB,WAAW,KAAK;AACxD,qBAAS,QAAQ,MAAM,YAAY,kBAAkB,MAAM;AAAA,UACvE;AAAA,QACS;AAAA,QACD,eAAe,CAAC,cAAc;AAC5B,cAAI,QAAQ,SAAU,SAAQ,SAAS,YAAY;AAAA,QACpD;AAAA,QACD,cAAc,CAAC,eAAe;AAC5B,cAAI,QAAQ,SAAU,SAAQ,SAAS,YAAY,kBAAkB,UAAU;AAAA,QACzF;AAAA,MACA;AAAA,IACK;AAAA,EACL;AACE,SAAO;AACT,CAAC;AACD,IAAI,uBAAuBF,aAAiB,WAAC,CAAC,OAAO,iBAAiB;AACpE,QAAM,EAAE,OAAO,eAAe,GAAG,eAAgB,IAAG;AACpD,QAAM,UAAU,qBAAqB,gBAAgB,MAAM,iBAAiB;AAC5E,QAAM,CAAC,eAAe,gBAAgB,IAAIC,sBAAiB;AAC3D,QAAM,MAAME,aAAa,OAAC,IAAI;AAC9B,QAAM,cAAc,gBAAgB,cAAc,KAAK,QAAQ,kBAAkB;AACjFG,eAAAA,UAAiB,MAAM;AACrB,QAAI,IAAI,QAAS,kBAAiB,iBAAiB,IAAI,OAAO,CAAC;AAAA,EACnE,GAAK,CAAC,GAAG,CAAC;AACR,SAAuBJ,kCAAG;AAAA,IACxB;AAAA,IACA;AAAA,MACE,oBAAoB;AAAA,MACpB,GAAG;AAAA,MACH,KAAK;AAAA,MACL;AAAA,MACA,OAAO;AAAA,QACL,QAAQ;AAAA,QACR,MAAM,QAAQ,QAAQ,QAAQ,0CAA0C;AAAA,QACxE,OAAO,QAAQ,QAAQ,QAAQ,0CAA0C;AAAA,QACzE,CAAC,iCAAiC,GAAG,aAAa,KAAK,IAAI;AAAA,QAC3D,GAAG,MAAM;AAAA,MACV;AAAA,MACD,oBAAoB,CAAC,eAAe,MAAM,mBAAmB,WAAW,CAAC;AAAA,MACzE,cAAc,CAAC,eAAe,MAAM,aAAa,WAAW,CAAC;AAAA,MAC7D,eAAe,CAAC,OAAO,iBAAiB;AACtC,YAAI,QAAQ,UAAU;AACpB,gBAAM,YAAY,QAAQ,SAAS,aAAa,MAAM;AACtD,gBAAM,cAAc,SAAS;AAC7B,cAAI,iCAAiC,WAAW,YAAY,GAAG;AAC7D,kBAAM,eAAgB;AAAA,UAClC;AAAA,QACA;AAAA,MACO;AAAA,MACD,UAAU,MAAM;AACd,YAAI,IAAI,WAAW,QAAQ,YAAY,eAAe;AACpD,wBAAc;AAAA,YACZ,SAAS,QAAQ,SAAS;AAAA,YAC1B,UAAU,QAAQ,SAAS;AAAA,YAC3B,WAAW;AAAA,cACT,MAAM,IAAI,QAAQ;AAAA,cAClB,cAAc,MAAM,cAAc,WAAW;AAAA,cAC7C,YAAY,MAAM,cAAc,YAAY;AAAA,YAC1D;AAAA,UACA,CAAW;AAAA,QACX;AAAA,MACA;AAAA,IACA;AAAA,EACG;AACH,CAAC;AACD,IAAI,uBAAuBF,aAAiB,WAAC,CAAC,OAAO,iBAAiB;AACpE,QAAM,EAAE,OAAO,eAAe,GAAG,eAAgB,IAAG;AACpD,QAAM,UAAU,qBAAqB,gBAAgB,MAAM,iBAAiB;AAC5E,QAAM,CAAC,eAAe,gBAAgB,IAAIC,sBAAiB;AAC3D,QAAM,MAAME,aAAa,OAAC,IAAI;AAC9B,QAAM,cAAc,gBAAgB,cAAc,KAAK,QAAQ,kBAAkB;AACjFG,eAAAA,UAAiB,MAAM;AACrB,QAAI,IAAI,QAAS,kBAAiB,iBAAiB,IAAI,OAAO,CAAC;AAAA,EACnE,GAAK,CAAC,GAAG,CAAC;AACR,SAAuBJ,kCAAG;AAAA,IACxB;AAAA,IACA;AAAA,MACE,oBAAoB;AAAA,MACpB,GAAG;AAAA,MACH,KAAK;AAAA,MACL;AAAA,MACA,OAAO;AAAA,QACL,KAAK;AAAA,QACL,OAAO,QAAQ,QAAQ,QAAQ,IAAI;AAAA,QACnC,MAAM,QAAQ,QAAQ,QAAQ,IAAI;AAAA,QAClC,QAAQ;AAAA,QACR,CAAC,kCAAkC,GAAG,aAAa,KAAK,IAAI;AAAA,QAC5D,GAAG,MAAM;AAAA,MACV;AAAA,MACD,oBAAoB,CAAC,eAAe,MAAM,mBAAmB,WAAW,CAAC;AAAA,MACzE,cAAc,CAAC,eAAe,MAAM,aAAa,WAAW,CAAC;AAAA,MAC7D,eAAe,CAAC,OAAO,iBAAiB;AACtC,YAAI,QAAQ,UAAU;AACpB,gBAAM,YAAY,QAAQ,SAAS,YAAY,MAAM;AACrD,gBAAM,cAAc,SAAS;AAC7B,cAAI,iCAAiC,WAAW,YAAY,GAAG;AAC7D,kBAAM,eAAgB;AAAA,UAClC;AAAA,QACA;AAAA,MACO;AAAA,MACD,UAAU,MAAM;AACd,YAAI,IAAI,WAAW,QAAQ,YAAY,eAAe;AACpD,wBAAc;AAAA,YACZ,SAAS,QAAQ,SAAS;AAAA,YAC1B,UAAU,QAAQ,SAAS;AAAA,YAC3B,WAAW;AAAA,cACT,MAAM,IAAI,QAAQ;AAAA,cAClB,cAAc,MAAM,cAAc,UAAU;AAAA,cAC5C,YAAY,MAAM,cAAc,aAAa;AAAA,YAC3D;AAAA,UACA,CAAW;AAAA,QACX;AAAA,MACA;AAAA,IACA;AAAA,EACG;AACH,CAAC;AACD,IAAI,CAAC,mBAAmB,mBAAmB,IAAI,wBAAwB,cAAc;AACrF,IAAI,0BAA0BF,aAAiB,WAAC,CAAC,OAAO,iBAAiB;AACvE,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,GAAG;AAAA,EACP,IAAM;AACJ,QAAM,UAAU,qBAAqB,gBAAgB,iBAAiB;AACtE,QAAM,CAAC,WAAW,YAAY,IAAIC,aAAAA,SAAgB,IAAI;AACtD,QAAM,cAAc,gBAAgB,cAAc,CAAC,SAAS,aAAa,IAAI,CAAC;AAC9E,QAAM,UAAUE,aAAa,OAAC,IAAI;AAClC,QAAM,0BAA0BA,aAAa,OAAC,EAAE;AAChD,QAAM,WAAW,QAAQ;AACzB,QAAM,eAAe,MAAM,UAAU,MAAM;AAC3C,QAAM,oBAAoB,eAAe,aAAa;AACtD,QAAM,4BAA4B,eAAe,qBAAqB;AACtE,QAAM,eAAe,oBAAoB,UAAU,EAAE;AACrD,WAAS,iBAAiB,OAAO;AAC/B,QAAI,QAAQ,SAAS;AACnB,YAAM,IAAI,MAAM,UAAU,QAAQ,QAAQ;AAC1C,YAAM,IAAI,MAAM,UAAU,QAAQ,QAAQ;AAC1C,mBAAa,EAAE,GAAG,GAAG;AAAA,IAC3B;AAAA,EACA;AACEG,eAAAA,UAAiB,MAAM;AACrB,UAAM,cAAc,CAAC,UAAU;AAC7B,YAAM,UAAU,MAAM;AACtB,YAAM,mBAAmB,uCAAW,SAAS;AAC7C,UAAI,iBAAkB,mBAAkB,OAAO,YAAY;AAAA,IAC5D;AACD,aAAS,iBAAiB,SAAS,aAAa,EAAE,SAAS,OAAO;AAClE,WAAO,MAAM,SAAS,oBAAoB,SAAS,aAAa,EAAE,SAAS,OAAO;AAAA,EACnF,GAAE,CAAC,UAAU,WAAW,cAAc,iBAAiB,CAAC;AACzDA,eAAAA,UAAiB,2BAA2B,CAAC,OAAO,yBAAyB,CAAC;AAC9E,oBAAkB,WAAW,YAAY;AACzC,oBAAkB,QAAQ,SAAS,YAAY;AAC/C,SAAuBJ,kCAAG;AAAA,IACxB;AAAA,IACA;AAAA,MACE,OAAO;AAAA,MACP;AAAA,MACA;AAAA,MACA,eAAe,eAAe,aAAa;AAAA,MAC3C,kBAAkB,eAAe,gBAAgB;AAAA,MACjD,uBAAuB;AAAA,MACvB,oBAAoB,eAAe,kBAAkB;AAAA,MACrD,UAA0BA,kCAAG;AAAA,QAC3B,UAAU;AAAA,QACV;AAAA,UACE,GAAG;AAAA,UACH,KAAK;AAAA,UACL,OAAO,EAAE,UAAU,YAAY,GAAG,eAAe,MAAO;AAAA,UACxD,eAAe,qBAAqB,MAAM,eAAe,CAAC,UAAU;AAClE,kBAAM,cAAc;AACpB,gBAAI,MAAM,WAAW,aAAa;AAChC,oBAAM,UAAU,MAAM;AACtB,sBAAQ,kBAAkB,MAAM,SAAS;AACzC,sBAAQ,UAAU,UAAU,sBAAuB;AACnD,sCAAwB,UAAU,SAAS,KAAK,MAAM;AACtD,uBAAS,KAAK,MAAM,mBAAmB;AACvC,kBAAI,QAAQ,SAAU,SAAQ,SAAS,MAAM,iBAAiB;AAC9D,+BAAiB,KAAK;AAAA,YACpC;AAAA,UACA,CAAW;AAAA,UACD,eAAe,qBAAqB,MAAM,eAAe,gBAAgB;AAAA,UACzE,aAAa,qBAAqB,MAAM,aAAa,CAAC,UAAU;AAC9D,kBAAM,UAAU,MAAM;AACtB,gBAAI,QAAQ,kBAAkB,MAAM,SAAS,GAAG;AAC9C,sBAAQ,sBAAsB,MAAM,SAAS;AAAA,YAC3D;AACY,qBAAS,KAAK,MAAM,mBAAmB,wBAAwB;AAC/D,gBAAI,QAAQ,SAAU,SAAQ,SAAS,MAAM,iBAAiB;AAC9D,oBAAQ,UAAU;AAAA,UACnB,CAAA;AAAA,QACX;AAAA,MACA;AAAA,IACA;AAAA,EACG;AACH,CAAC;AACD,IAAI,aAAa;AACjB,IAAI,kBAAkBF,aAAiB;AAAA,EACrC,CAAC,OAAO,iBAAiB;AACvB,UAAM,EAAE,YAAY,GAAG,WAAU,IAAK;AACtC,UAAM,mBAAmB,oBAAoB,YAAY,MAAM,iBAAiB;AAChF,WAAuBE,kCAAAA,IAAI,UAAU,EAAE,SAAS,cAAc,iBAAiB,UAAU,UAA0BA,kCAAAA,IAAI,qBAAqB,EAAE,KAAK,cAAc,GAAG,WAAY,CAAA,GAAG;AAAA,EACvL;AACA;AACA,IAAI,sBAAsBF,aAAiB;AAAA,EACzC,CAAC,OAAO,iBAAiB;AACvB,UAAM,EAAE,mBAAmB,OAAO,GAAG,WAAY,IAAG;AACpD,UAAM,oBAAoB,qBAAqB,YAAY,iBAAiB;AAC5E,UAAM,mBAAmB,oBAAoB,YAAY,iBAAiB;AAC1E,UAAM,EAAE,sBAAqB,IAAK;AAClC,UAAM,cAAc;AAAA,MAClB;AAAA,MACA,CAAC,SAAS,iBAAiB,cAAc,IAAI;AAAA,IAC9C;AACD,UAAM,kCAAkCG,aAAa,OAAC,MAAM;AAC5D,UAAM,oBAAoB,oBAAoB,MAAM;AAClD,UAAI,gCAAgC,SAAS;AAC3C,wCAAgC,QAAS;AACzC,wCAAgC,UAAU;AAAA,MAClD;AAAA,IACK,GAAE,GAAG;AACNG,iBAAAA,UAAiB,MAAM;AACrB,YAAM,WAAW,kBAAkB;AACnC,UAAI,UAAU;AACZ,cAAM,eAAe,MAAM;AACzB,4BAAmB;AACnB,cAAI,CAAC,gCAAgC,SAAS;AAC5C,kBAAM,WAAW,0BAA0B,UAAU,qBAAqB;AAC1E,4CAAgC,UAAU;AAC1C,kCAAuB;AAAA,UACnC;AAAA,QACS;AACD,8BAAuB;AACvB,iBAAS,iBAAiB,UAAU,YAAY;AAChD,eAAO,MAAM,SAAS,oBAAoB,UAAU,YAAY;AAAA,MACxE;AAAA,IACK,GAAE,CAAC,kBAAkB,UAAU,mBAAmB,qBAAqB,CAAC;AACzE,WAAuBJ,kCAAG;AAAA,MACxB,UAAU;AAAA,MACV;AAAA,QACE,cAAc,iBAAiB,WAAW,YAAY;AAAA,QACtD,GAAG;AAAA,QACH,KAAK;AAAA,QACL,OAAO;AAAA,UACL,OAAO;AAAA,UACP,QAAQ;AAAA,UACR,GAAG;AAAA,QACJ;AAAA,QACD,sBAAsB,qBAAqB,MAAM,sBAAsB,CAAC,UAAU;AAChF,gBAAM,QAAQ,MAAM;AACpB,gBAAM,YAAY,MAAM,sBAAuB;AAC/C,gBAAM,IAAI,MAAM,UAAU,UAAU;AACpC,gBAAM,IAAI,MAAM,UAAU,UAAU;AACpC,2BAAiB,mBAAmB,EAAE,GAAG,EAAC,CAAE;AAAA,QACtD,CAAS;AAAA,QACD,aAAa,qBAAqB,MAAM,aAAa,iBAAiB,gBAAgB;AAAA,MAC9F;AAAA,IACK;AAAA,EACL;AACA;AACA,gBAAgB,cAAc;AAC9B,IAAI,cAAc;AAClB,IAAI,mBAAmBF,aAAiB;AAAA,EACtC,CAAC,OAAO,iBAAiB;AACvB,UAAM,UAAU,qBAAqB,aAAa,MAAM,iBAAiB;AACzE,UAAM,2BAA2B,QAAQ,QAAQ,cAAc,QAAQ,UAAU;AACjF,UAAM,YAAY,QAAQ,SAAS,YAAY;AAC/C,WAAO,YAA4BE,kCAAG,IAAC,sBAAsB,EAAE,GAAG,OAAO,KAAK,aAAc,CAAA,IAAI;AAAA,EACpG;AACA;AACA,iBAAiB,cAAc;AAC/B,IAAI,uBAAuBF,aAAiB,WAAC,CAAC,OAAO,iBAAiB;AACpE,QAAM,EAAE,mBAAmB,GAAG,YAAW,IAAK;AAC9C,QAAM,UAAU,qBAAqB,aAAa,iBAAiB;AACnE,QAAM,CAAC,OAAO,QAAQ,IAAIC,aAAAA,SAAgB,CAAC;AAC3C,QAAM,CAAC,QAAQ,SAAS,IAAIA,aAAAA,SAAgB,CAAC;AAC7C,QAAM,UAAU,QAAQ,SAAS,MAAM;AACvC,oBAAkB,QAAQ,YAAY,MAAM;;AAC1C,UAAM,YAAU,aAAQ,eAAR,mBAAoB,iBAAgB;AACpD,YAAQ,qBAAqB,OAAO;AACpC,cAAU,OAAO;AAAA,EACrB,CAAG;AACD,oBAAkB,QAAQ,YAAY,MAAM;;AAC1C,UAAM,WAAS,aAAQ,eAAR,mBAAoB,gBAAe;AAClD,YAAQ,oBAAoB,MAAM;AAClC,aAAS,MAAM;AAAA,EACnB,CAAG;AACD,SAAO,UAA0BC,kCAAG;AAAA,IAClC,UAAU;AAAA,IACV;AAAA,MACE,GAAG;AAAA,MACH,KAAK;AAAA,MACL,OAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA,UAAU;AAAA,QACV,OAAO,QAAQ,QAAQ,QAAQ,IAAI;AAAA,QACnC,MAAM,QAAQ,QAAQ,QAAQ,IAAI;AAAA,QAClC,QAAQ;AAAA,QACR,GAAG,MAAM;AAAA,MACjB;AAAA,IACA;AAAA,EACA,IAAM;AACN,CAAC;AACD,SAAS,MAAM,OAAO;AACpB,SAAO,QAAQ,SAAS,OAAO,EAAE,IAAI;AACvC;AACA,SAAS,cAAc,cAAc,aAAa;AAChD,QAAM,QAAQ,eAAe;AAC7B,SAAO,MAAM,KAAK,IAAI,IAAI;AAC5B;AACA,SAAS,aAAa,OAAO;AAC3B,QAAM,QAAQ,cAAc,MAAM,UAAU,MAAM,OAAO;AACzD,QAAM,mBAAmB,MAAM,UAAU,eAAe,MAAM,UAAU;AACxE,QAAM,aAAa,MAAM,UAAU,OAAO,oBAAoB;AAC9D,SAAO,KAAK,IAAI,WAAW,EAAE;AAC/B;AACA,SAAS,6BAA6B,YAAY,eAAe,OAAO,MAAM,OAAO;AACnF,QAAM,cAAc,aAAa,KAAK;AACtC,QAAM,cAAc,cAAc;AAClC,QAAM,SAAS,iBAAiB;AAChC,QAAM,qBAAqB,cAAc;AACzC,QAAM,gBAAgB,MAAM,UAAU,eAAe;AACrD,QAAM,gBAAgB,MAAM,UAAU,OAAO,MAAM,UAAU,aAAa;AAC1E,QAAM,eAAe,MAAM,UAAU,MAAM;AAC3C,QAAM,cAAc,QAAQ,QAAQ,CAAC,GAAG,YAAY,IAAI,CAAC,eAAe,IAAI,CAAC;AAC7E,QAAM,cAAc,YAAY,CAAC,eAAe,aAAa,GAAG,WAAW;AAC3E,SAAO,YAAY,UAAU;AAC/B;AACA,SAAS,yBAAyB,WAAW,OAAO,MAAM,OAAO;AAC/D,QAAM,cAAc,aAAa,KAAK;AACtC,QAAM,mBAAmB,MAAM,UAAU,eAAe,MAAM,UAAU;AACxE,QAAM,YAAY,MAAM,UAAU,OAAO;AACzC,QAAM,eAAe,MAAM,UAAU,MAAM;AAC3C,QAAM,cAAc,YAAY;AAChC,QAAM,mBAAmB,QAAQ,QAAQ,CAAC,GAAG,YAAY,IAAI,CAAC,eAAe,IAAI,CAAC;AAClF,QAAM,wBAAwB,MAAM,WAAW,gBAAgB;AAC/D,QAAM,cAAc,YAAY,CAAC,GAAG,YAAY,GAAG,CAAC,GAAG,WAAW,CAAC;AACnE,SAAO,YAAY,qBAAqB;AAC1C;AACA,SAAS,YAAY,OAAO,QAAQ;AAClC,SAAO,CAAC,UAAU;AAChB,QAAI,MAAM,CAAC,MAAM,MAAM,CAAC,KAAK,OAAO,CAAC,MAAM,OAAO,CAAC,EAAG,QAAO,OAAO,CAAC;AACrE,UAAM,SAAS,OAAO,CAAC,IAAI,OAAO,CAAC,MAAM,MAAM,CAAC,IAAI,MAAM,CAAC;AAC3D,WAAO,OAAO,CAAC,IAAI,SAAS,QAAQ,MAAM,CAAC;AAAA,EAC5C;AACH;AACA,SAAS,iCAAiC,WAAW,cAAc;AACjE,SAAO,YAAY,KAAK,YAAY;AACtC;AACA,IAAI,4BAA4B,CAAC,MAAM,UAAU,MAAM;AACvD,MAAM;AACJ,MAAI,eAAe,EAAE,MAAM,KAAK,YAAY,KAAK,KAAK,UAAW;AACjE,MAAI,MAAM;AACV,GAAC,SAAS,OAAO;AACf,UAAM,WAAW,EAAE,MAAM,KAAK,YAAY,KAAK,KAAK,UAAW;AAC/D,UAAM,qBAAqB,aAAa,SAAS,SAAS;AAC1D,UAAM,mBAAmB,aAAa,QAAQ,SAAS;AACvD,QAAI,sBAAsB,iBAAkB,SAAS;AACrD,mBAAe;AACf,UAAM,OAAO,sBAAsB,IAAI;AAAA,EAC3C,GAAM;AACJ,SAAO,MAAM,OAAO,qBAAqB,GAAG;AAC9C;AACA,SAAS,oBAAoB,UAAU,OAAO;AAC5C,QAAM,iBAAiB,eAAe,QAAQ;AAC9C,QAAM,mBAAmBC,aAAa,OAAC,CAAC;AACxCG,eAAgB,UAAC,MAAM,MAAM,OAAO,aAAa,iBAAiB,OAAO,GAAG,EAAE;AAC9E,SAAOC,aAAkB,YAAC,MAAM;AAC9B,WAAO,aAAa,iBAAiB,OAAO;AAC5C,qBAAiB,UAAU,OAAO,WAAW,gBAAgB,KAAK;AAAA,EACtE,GAAK,CAAC,gBAAgB,KAAK,CAAC;AAC5B;AACA,SAAS,kBAAkB,SAAS,UAAU;AAC5C,QAAM,eAAe,eAAe,QAAQ;AAC5CC,mBAAgB,MAAM;AACpB,QAAI,MAAM;AACV,QAAI,SAAS;AACX,YAAM,iBAAiB,IAAI,eAAe,MAAM;AAC9C,6BAAqB,GAAG;AACxB,cAAM,OAAO,sBAAsB,YAAY;AAAA,MACvD,CAAO;AACD,qBAAe,QAAQ,OAAO;AAC9B,aAAO,MAAM;AACX,eAAO,qBAAqB,GAAG;AAC/B,uBAAe,UAAU,OAAO;AAAA,MACjC;AAAA,IACP;AAAA,EACA,GAAK,CAAC,SAAS,YAAY,CAAC;AAC5B;AACA,IAAI,OAAOT;AACX,IAAI,WAAW;AAGf,IAAI,SAAS;AC/sBP,MAAA,aAAaU,aAAAA,WAGjB,CAAC,EAAE,WAAW,UAAU,GAAG,MAAM,GAAG,QACpCL,kCAAA;AAAA,EAACM;AAAAA,EAAA;AAAA,IACC;AAAA,IACA,WAAW,GAAG,4BAA4B,SAAS;AAAA,IAClD,GAAG;AAAA,IAEJ,UAAA;AAAA,MAAAR,kCAAAA,IAACS,UAAA,EAA6B,WAAU,mCACrC,SACH,CAAA;AAAA,4CACC,WAAU,EAAA;AAAA,MACXT,kCAAA,IAACU,QAAA,CAA2B,CAAA;AAAA,IAAA;AAAA,EAAA;AAC9B,CACD;AACD,WAAW,cAAcF,KAAyB;AAElD,MAAM,YAAYD,aAAAA,WAGhB,CAAC,EAAE,WAAW,cAAc,YAAY,GAAG,SAAS,QACpDP,kCAAA;AAAA,EAACW;AAAAA,EAAA;AAAA,IACC;AAAA,IACA;AAAA,IACA,WAAW;AAAA,MACT;AAAA,MACA,gBAAgB,cACd;AAAA,MACF,gBAAgB,gBACd;AAAA,MACF;AAAA,IACF;AAAA,IACC,GAAG;AAAA,IAEJ,UAACX,kCAAAA,IAAAY,iBAAA,EAAoC,WAAU,yCAAyC,CAAA;AAAA,EAAA;AAC1F,CACD;AACD,UAAU,cAAcD,oBAAwC;", "x_google_ignoreList": [0]}