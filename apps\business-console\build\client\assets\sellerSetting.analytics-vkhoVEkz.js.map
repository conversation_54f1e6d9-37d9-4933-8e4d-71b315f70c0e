{"version": 3, "file": "sellerSetting.analytics-vkhoVEkz.js", "sources": ["../../../app/routes/sellerSetting.analytics.tsx"], "sourcesContent": ["import { useLoaderData } from \"@remix-run/react\";\r\nimport { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from \"../components/ui/tabs\";\r\nimport { metabaseService } from \"../utils/metabase\";\r\nimport { withAuth, withResponse } from \"../utils/auth-utils\";\r\n\r\nexport const loader = withAuth(async ({ user }) => {\r\n  const overviewUrl = metabaseService.generateDashboardUrl(11, {\r\n    id: user.sellerId,\r\n  });\r\n  const itemsAnalyticsUrl = metabaseService.generateDashboardUrl(12, {\r\n    id: user.sellerId,\r\n  });\r\n  \r\n  return withResponse({ overviewUrl, itemsAnalyticsUrl });\r\n});\r\n\r\nexport default function Analytics() {\r\n  const { overviewUrl, itemsAnalyticsUrl } = useLoaderData<typeof loader>();\r\n\r\n  return (\r\n    <div className=\"p-6\">\r\n      <div className=\"mb-6\">\r\n        <h1 className=\"text-3xl font-bold text-gray-900\">Analytics</h1>\r\n        <p className=\"text-gray-600 mt-2\">Detailed performance insights</p>\r\n      </div>\r\n      \r\n      <Tabs defaultValue=\"overview\" className=\"w-full\">\r\n        <TabsList className=\"grid w-full grid-cols-2\">\r\n          <TabsTrigger value=\"overview\">Overview</TabsTrigger>\r\n          <TabsTrigger value=\"items-analytics\">Items Analytics</TabsTrigger>\r\n        </TabsList>\r\n        \r\n        <TabsContent value=\"overview\" className=\"mt-6\">\r\n          <div className=\"w-full h-screen\">\r\n            {overviewUrl ? (\r\n              <iframe\r\n                id=\"metabase-overview-iframe\"\r\n                src={overviewUrl}\r\n                title=\"Overview Dashboard\"\r\n                className=\"w-full h-full border-0\"\r\n              />\r\n            ) : (\r\n              <div className=\"flex items-center justify-center min-h-[400px]\">\r\n                <div className=\"text-center\">\r\n                  <div className=\"mx-auto mb-4 w-16 h-16 bg-gradient-to-r from-teal-500 to-green-600 rounded-full flex items-center justify-center\">\r\n                    <svg className=\"w-8 h-8 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\" />\r\n                    </svg>\r\n                  </div>\r\n                  <h2 className=\"text-2xl font-bold mb-2\">Overview Dashboard Loading</h2>\r\n                  <p className=\"text-gray-500\">\r\n                    Loading your overview analytics...\r\n                  </p>\r\n                </div>\r\n              </div>\r\n            )}\r\n          </div>\r\n        </TabsContent>\r\n        \r\n        <TabsContent value=\"items-analytics\" className=\"mt-6\">\r\n          <div className=\"w-full h-screen\">\r\n            {itemsAnalyticsUrl ? (\r\n              <iframe\r\n                id=\"metabase-items-iframe\"\r\n                src={itemsAnalyticsUrl}\r\n                title=\"Items Analytics Dashboard\"\r\n                className=\"w-full h-full border-0\"\r\n              />\r\n            ) : (\r\n              <div className=\"flex items-center justify-center min-h-[400px]\">\r\n                <div className=\"text-center\">\r\n                  <div className=\"mx-auto mb-4 w-16 h-16 bg-gradient-to-r from-teal-500 to-green-600 rounded-full flex items-center justify-center\">\r\n                    <svg className=\"w-8 h-8 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\" />\r\n                    </svg>\r\n                  </div>\r\n                  <h2 className=\"text-2xl font-bold mb-2\">Items Analytics Loading</h2>\r\n                  <p className=\"text-gray-500\">\r\n                    Loading your items analytics...\r\n                  </p>\r\n                </div>\r\n              </div>\r\n            )}\r\n          </div>\r\n        </TabsContent>\r\n      </Tabs>\r\n    </div>\r\n  );\r\n} "], "names": ["Analytics", "overviewUrl", "itemsAnalyticsUrl", "useLoaderData", "jsxs", "className", "children", "jsx", "Tabs", "defaultValue", "TabsList", "TabsTrigger", "value", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "id", "src", "title", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d"], "mappings": ";;;;;;;;;;;;;;;AAgBA,SAAwBA,YAAY;AAClC,QAAM;AAAA,IAAEC;AAAAA,IAAaC;AAAAA,EAAkB,IAAIC,cAA6B;AAGtE,SAAAC,kCAAAA,KAAC,OAAI;AAAA,IAAAC,WAAU;AAAA,IACbC,UAAA,CAACF,kCAAA,KAAA,OAAA;AAAA,MAAIC,WAAU;AAAA,MACbC,UAAA,CAACC,kCAAA,IAAA,MAAA;AAAA,QAAGF,WAAU;AAAA,QAAmCC,UAAS;AAAA,MAAA,CAAA,GACzDC,kCAAA,IAAA,KAAA;AAAA,QAAEF,WAAU;AAAA,QAAqBC,UAA6B;AAAA,MAAA,CAAA,CAAA;AAAA,IACjE,CAAA,GAECF,kCAAA,KAAAI,MAAA;AAAA,MAAKC,cAAa;AAAA,MAAWJ,WAAU;AAAA,MACtCC,UAAA,CAACF,kCAAA,KAAAM,UAAA;AAAA,QAASL,WAAU;AAAA,QAClBC,UAAA,CAACC,kCAAA,IAAAI,aAAA;AAAA,UAAYC,OAAM;AAAA,UAAWN,UAAQ;AAAA,QAAA,CAAA,GACrCC,kCAAA,IAAAI,aAAA;AAAA,UAAYC,OAAM;AAAA,UAAkBN,UAAe;AAAA,QAAA,CAAA,CAAA;AAAA,MACtD,CAAA,GAEAC,kCAAA,IAACM,aAAY;AAAA,QAAAD,OAAM;AAAA,QAAWP,WAAU;AAAA,QACtCC,UAACC,kCAAA,IAAA,OAAA;AAAA,UAAIF,WAAU;AAAA,UACZC,UACCL,cAAAM,kCAAA,IAAC,UAAA;AAAA,YACCO,IAAG;AAAA,YACHC,KAAKd;AAAAA,YACLe,OAAM;AAAA,YACNX,WAAU;AAAA,UAAA,CACZ,0CAEC,OAAI;AAAA,YAAAA,WAAU;AAAA,YACbC,UAACF,kCAAA,KAAA,OAAA;AAAA,cAAIC,WAAU;AAAA,cACbC,UAAA,CAACC,kCAAA,IAAA,OAAA;AAAA,gBAAIF,WAAU;AAAA,gBACbC,UAACC,kCAAA,IAAA,OAAA;AAAA,kBAAIF,WAAU;AAAA,kBAAqBY,MAAK;AAAA,kBAAOC,QAAO;AAAA,kBAAeC,SAAQ;AAAA,kBAC5Eb,UAACC,kCAAA,IAAA,QAAA;AAAA,oBAAKa,eAAc;AAAA,oBAAQC,gBAAe;AAAA,oBAAQC,aAAa;AAAA,oBAAGC,GAAE;AAAA,kBAAuM,CAAA;AAAA,gBAC9Q,CAAA;AAAA,cACF,CAAA,GACChB,kCAAA,IAAA,MAAA;AAAA,gBAAGF,WAAU;AAAA,gBAA0BC,UAA0B;AAAA,cAAA,CAAA,GACjEC,kCAAA,IAAA,KAAA;AAAA,gBAAEF,WAAU;AAAA,gBAAgBC,UAE7B;AAAA,cAAA,CAAA,CAAA;AAAA,YACF,CAAA;AAAA,UACF,CAAA;AAAA,QAEJ,CAAA;AAAA,MACF,CAAA,GAEAC,kCAAA,IAACM,aAAY;AAAA,QAAAD,OAAM;AAAA,QAAkBP,WAAU;AAAA,QAC7CC,UAACC,kCAAA,IAAA,OAAA;AAAA,UAAIF,WAAU;AAAA,UACZC,UACCJ,oBAAAK,kCAAA,IAAC,UAAA;AAAA,YACCO,IAAG;AAAA,YACHC,KAAKb;AAAAA,YACLc,OAAM;AAAA,YACNX,WAAU;AAAA,UAAA,CACZ,0CAEC,OAAI;AAAA,YAAAA,WAAU;AAAA,YACbC,UAACF,kCAAA,KAAA,OAAA;AAAA,cAAIC,WAAU;AAAA,cACbC,UAAA,CAACC,kCAAA,IAAA,OAAA;AAAA,gBAAIF,WAAU;AAAA,gBACbC,UAACC,kCAAA,IAAA,OAAA;AAAA,kBAAIF,WAAU;AAAA,kBAAqBY,MAAK;AAAA,kBAAOC,QAAO;AAAA,kBAAeC,SAAQ;AAAA,kBAC5Eb,UAACC,kCAAA,IAAA,QAAA;AAAA,oBAAKa,eAAc;AAAA,oBAAQC,gBAAe;AAAA,oBAAQC,aAAa;AAAA,oBAAGC,GAAE;AAAA,kBAAuM,CAAA;AAAA,gBAC9Q,CAAA;AAAA,cACF,CAAA,GACChB,kCAAA,IAAA,MAAA;AAAA,gBAAGF,WAAU;AAAA,gBAA0BC,UAAuB;AAAA,cAAA,CAAA,GAC9DC,kCAAA,IAAA,KAAA;AAAA,gBAAEF,WAAU;AAAA,gBAAgBC,UAE7B;AAAA,cAAA,CAAA,CAAA;AAAA,YACF,CAAA;AAAA,UACF,CAAA;AAAA,QAEJ,CAAA;AAAA,MACF,CAAA,CAAA;AAAA,IACF,CAAA,CAAA;AAAA,EACF,CAAA;AAEJ;"}