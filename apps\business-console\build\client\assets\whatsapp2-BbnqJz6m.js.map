{"version": 3, "file": "whatsapp2-BbnqJz6m.js", "sources": ["../../../app/routes/whatsapp2.tsx"], "sourcesContent": ["import { json, LoaderFunction, ActionFunction } from '@remix-run/node';\r\nimport { useLoaderData, Form, useActionData } from '@remix-run/react';\r\nimport { useState } from 'react';\r\nimport { Button } from '~/components/ui/button';\r\n\r\nexport const loader: LoaderFunction = async () => {\r\n    return json({});\r\n};\r\n\r\nexport const action: ActionFunction = async ({ request }) => {\r\n    const authToken = process.env.AUTH_TOKEN;\r\n    if (!authToken) {\r\n        throw new Error('AUTH_TOKEN environment variable is not set');\r\n    }\r\n\r\n    try {\r\n        // Get the origin from the incoming request\r\n        const url = new URL(request.url);\r\n        const baseUrl = url.origin;\r\n\r\n        const response = await fetch(`${baseUrl}/api/whatsapp/send-template-message`, {\r\n            method: 'POST',\r\n            headers: {\r\n                'Content-Type': 'application/json',\r\n                'Authorization': `Bearer ${authToken}`\r\n            },\r\n            body: JSON.stringify({\r\n                phoneNo: '8137878484',\r\n                templateName: 'hello_mnet'\r\n            })\r\n        });\r\n\r\n        if (!response.ok) {\r\n            throw new Error('Failed to send message');\r\n        }\r\n\r\n        const data = await response.json();\r\n        return json({ success: true, data });\r\n    } catch (error) {\r\n        console.error('Error:', error);\r\n        return json({ error: \"Failed to send template message\" }, { status: 500 });\r\n    }\r\n};\r\n\r\nexport default function WhatsAppPage() {\r\n    const actionData = useActionData();\r\n    const [isSubmitting, setIsSubmitting] = useState(false);\r\n\r\n    return (\r\n        <div className=\"p-6\">\r\n            <Form\r\n                method=\"post\"\r\n                onSubmit={() => setIsSubmitting(true)}\r\n                onChange={() => {\r\n                    if (actionData) setIsSubmitting(false);\r\n                }}\r\n            >\r\n                <Button\r\n                    type=\"submit\"\r\n                    disabled={isSubmitting}\r\n                >\r\n                    {isSubmitting ? 'Sending...' : 'Send Hello World Template'}\r\n                </Button>\r\n            </Form>\r\n\r\n            {actionData?.success && (\r\n                <div className=\"mt-4 text-green-600\">Message sent successfully!</div>\r\n            )}\r\n\r\n            {actionData?.error && (\r\n                <div className=\"mt-4 text-red-600\">{actionData.error}</div>\r\n            )}\r\n        </div>\r\n    );\r\n}\r\n"], "names": ["WhatsAppPage", "actionData", "useActionData", "isSubmitting", "setIsSubmitting", "useState", "jsxs", "className", "children", "jsx", "Form", "method", "onSubmit", "onChange", "<PERSON><PERSON>", "type", "disabled", "success", "error"], "mappings": ";;;;;;;;AA4CA,SAAwBA,eAAe;AACnC,QAAMC,aAAaC,cAAc;AACjC,QAAM,CAACC,cAAcC,eAAe,IAAIC,aAAAA,SAAS,KAAK;AAGlD,SAAAC,kCAAAA,KAAC,OAAI;AAAA,IAAAC,WAAU;AAAA,IACXC,UAAA,CAAAC,kCAAA,IAACC,MAAA;AAAA,MACGC,QAAO;AAAA,MACPC,UAAUA,MAAMR,gBAAgB,IAAI;AAAA,MACpCS,UAAUA,MAAM;AACR,YAAAZ,4BAA4B,KAAK;AAAA,MACzC;AAAA,MAEAO,UAAAC,kCAAA,IAACK,QAAA;AAAA,QACGC,MAAK;AAAA,QACLC,UAAUb;AAAAA,QAETK,yBAAe,eAAe;AAAA,MACnC,CAAA;AAAA,IACJ,CAAA,IAECP,yCAAYgB,YACTR,kCAAAA,IAAC,OAAI;AAAA,MAAAF,WAAU;AAAA,MAAsBC,UAA0B;AAAA,IAAA,CAAA,IAGlEP,yCAAYiB,UACTT,kCAAAA,IAAC;MAAIF,WAAU;AAAA,MAAqBC,qBAAWU;AAAAA,IAAM,CAAA,CAAA;AAAA,EAE7D,CAAA;AAER;"}