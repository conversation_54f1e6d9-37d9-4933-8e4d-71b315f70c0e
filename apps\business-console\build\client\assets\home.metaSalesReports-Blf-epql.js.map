{"version": 3, "file": "home.metaSalesReports-Blf-epql.js", "sources": ["../../../app/routes/home.metaSalesReports.tsx"], "sourcesContent": ["import { useLoaderD<PERSON>, useSubmit, useSearchParams } from \"@remix-run/react\";\r\nimport { LoaderFunction, LoaderFunctionArgs, json } from \"@remix-run/node\";\r\nimport jwt from \"jsonwebtoken\";\r\nimport { useEffect, useState } from \"react\";\r\nimport { withAuth } from \"~/utils/auth-utils\";\r\n\r\nconst METABASE_SECRET_KEY = process.env.METABASE_SECRET_KEY || \"\";\r\nconst METABASE_SITE_URL = process.env.METABASE_SITE_URL || \"http://43.205.118.52:4001\";\r\n\r\nexport const loader: LoaderFunction = withAuth(async ({ request, user }) => {\r\n      const sellerId = user.sellerId;\r\n\r\n      // Get the date range from query parameters\r\n      const url = new URL(request.url);\r\n      const dateRange = url.searchParams.get(\"dateRange\") || \"7\"; // Default to weekly (7)\r\n\r\n      if (!METABASE_SECRET_KEY) {\r\n            throw new Error(\"Metabase secret key is not configured.\");\r\n      }\r\n\r\n      const payload = {\r\n            resource: { dashboard: 7 },\r\n            params: {\r\n                  seller_id: sellerId,\r\n                  date: dateRange // last 7 days\r\n            },\r\n            exp: Math.round(Date.now() / 1000) + (10 * 60) // expires in 10 minutes\r\n      };\r\n\r\n      const token = jwt.sign(payload, METABASE_SECRET_KEY);\r\n      const embedUrl = `${METABASE_SITE_URL}/embed/dashboard/${token}#bordered=false&titled=false`;\r\n\r\n      return json({ embedUrl, dateRange });\r\n});\r\n\r\nexport default function MetaDashboard() {\r\n      const { embedUrl, dateRange } = useLoaderData<typeof loader>();\r\n      const [isLoading, setIsLoading] = useState(true);\r\n      const submit = useSubmit();\r\n      const [searchParams] = useSearchParams();\r\n\r\n\r\n      // Handle iframe loading state\r\n      useEffect(() => {\r\n            if (embedUrl) {\r\n                  setIsLoading(false);\r\n            }\r\n      }, [embedUrl]);\r\n\r\n      // Scroll synchronization\r\n      useEffect(() => {\r\n            const handleScroll = () => {\r\n                  const iframe = document.getElementById(\"metabase-iframe\") as HTMLIFrameElement;\r\n                  if (iframe) {\r\n                        iframe.contentWindow?.scrollTo(0, window.scrollY);\r\n                  }\r\n            };\r\n\r\n            window.addEventListener(\"scroll\", handleScroll);\r\n            return () => window.removeEventListener(\"scroll\", handleScroll);\r\n      }, []);\r\n\r\n      // Handle dropdown change\r\n      const handleDateRangeChange = (event: React.ChangeEvent<HTMLSelectElement>) => {\r\n            const newDateRange = event.target.value;\r\n            submit({ dateRange: newDateRange }, { method: \"get\", action: \".\" });\r\n      };\r\n\r\n      return (\r\n            <div className=\"flex min-h-screen\">\r\n                  <main className=\"flex-1 overflow-y-auto\">\r\n                        <div className=\"p-4 sm:p-6\">\r\n                              <div className=\"flex justify-between items-center mb-4\">\r\n                                    <h1 className=\"text-2xl font-bold text-gray-800\">Sales Dashboard</h1>\r\n                                    <div>\r\n                                          <label htmlFor=\"dateRange\" className=\"text-gray-700 font-bold mr-2\">\r\n                                                Range:\r\n                                          </label>\r\n                                          <select\r\n                                                value={dateRange}\r\n                                                onChange={handleDateRangeChange}\r\n                                                className=\"p-2 border rounded-md bg-white text-gray-800\"\r\n                                          >\r\n                                                <option value=\"1\">Daily</option>\r\n                                                <option value=\"7\">Weekly</option>\r\n                                                <option value=\"30\">Monthly</option>\r\n                                          </select>\r\n                                    </div>\r\n                              </div>\r\n\r\n                              <div className=\"bg-white shadow-md rounded-md overflow-hidden\">\r\n                                    {isLoading ? (\r\n                                          <div className=\"flex justify-center items-center h-96\">\r\n                                                <div className=\"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500\"></div>\r\n                                          </div>\r\n                                    ) : embedUrl ? (\r\n                                          <iframe\r\n                                                id=\"metabase-iframe\"\r\n                                                src={embedUrl}\r\n                                                title=\"Metabase Dashboard\"\r\n                                                className=\"w-full h-[2000px] border-0\"\r\n                                                allowTransparency\r\n                                          />\r\n                                    ) : (\r\n                                          <div className=\"p-6 text-center text-red-500\">\r\n                                                Failed to load the dashboard.\r\n                                          </div>\r\n                                    )}\r\n                              </div>\r\n                        </div>\r\n                  </main>\r\n            </div>\r\n      );\r\n}"], "names": ["MetaDashboard", "embedUrl", "date<PERSON><PERSON><PERSON>", "useLoaderData", "isLoading", "setIsLoading", "useState", "submit", "useSubmit", "useSearchParams", "useEffect", "handleScroll", "iframe", "document", "getElementById", "contentWindow", "scrollTo", "window", "scrollY", "addEventListener", "removeEventListener", "handleDateRangeChange", "event", "newDateRange", "target", "value", "method", "action", "jsx", "className", "children", "jsxs", "htmlFor", "onChange", "id", "src", "title", "allowTransparency"], "mappings": ";;;;AAmCA,SAAwBA,gBAAgB;AAClC,QAAM;AAAA,IAAEC;AAAAA,IAAUC;AAAAA,EAAU,IAAIC,cAA6B;AAC7D,QAAM,CAACC,WAAWC,YAAY,IAAIC,aAAAA,SAAS,IAAI;AAC/C,QAAMC,SAASC,UAAU;AACFC,kBAAgB;AAIvCC,eAAAA,UAAU,MAAM;AACV,QAAIT,UAAU;AACRI,mBAAa,KAAK;AAAA,IACxB;AAAA,EACN,GAAG,CAACJ,QAAQ,CAAC;AAGbS,eAAAA,UAAU,MAAM;AACV,UAAMC,eAAeA,MAAM;;AACf,YAAAC,SAASC,SAASC,eAAe,iBAAiB;AACxD,UAAIF,QAAQ;AACNA,qBAAOG,kBAAPH,mBAAsBI,SAAS,GAAGC,OAAOC;AAAAA,MAC/C;AAAA,IACN;AAEOD,WAAAE,iBAAiB,UAAUR,YAAY;AAC9C,WAAO,MAAMM,OAAOG,oBAAoB,UAAUT,YAAY;AAAA,EACpE,GAAG,EAAE;AAGC,QAAAU,wBAAyBC,WAAgD;AACnE,UAAAC,eAAeD,MAAME,OAAOC;AAC3BlB,WAAA;AAAA,MAAEL,WAAWqB;AAAAA,IAAa,GAAG;AAAA,MAAEG,QAAQ;AAAA,MAAOC,QAAQ;AAAA,IAAI,CAAC;AAAA,EACxE;AAGM,SAAAC,kCAAAA,IAAC,OAAI;AAAA,IAAAC,WAAU;AAAA,IACTC,UAAAF,kCAAA,IAAC,QAAK;AAAA,MAAAC,WAAU;AAAA,MACVC,UAAAC,kCAAA,KAAC,OAAI;AAAA,QAAAF,WAAU;AAAA,QACTC,UAAA,CAACC,kCAAA,KAAA,OAAA;AAAA,UAAIF,WAAU;AAAA,UACTC,UAAA,CAACF,kCAAA,IAAA,MAAA;AAAA,YAAGC,WAAU;AAAA,YAAmCC,UAAe;AAAA,UAAA,CAAA,0CAC/D,OACK;AAAA,YAAAA,UAAA,CAAAF,kCAAA,IAAC,SAAM;AAAA,cAAAI,SAAQ;AAAA,cAAYH,WAAU;AAAA,cAA+BC,UAEpE;AAAA,YAAA,CAAA,GACAC,kCAAA,KAAC,UAAA;AAAA,cACKN,OAAOvB;AAAAA,cACP+B,UAAUZ;AAAAA,cACVQ,WAAU;AAAA,cAEVC,UAAA,CAACF,kCAAA,IAAA,UAAA;AAAA,gBAAOH,OAAM;AAAA,gBAAIK,UAAK;AAAA,cAAA,CAAA,GACtBF,kCAAA,IAAA,UAAA;AAAA,gBAAOH,OAAM;AAAA,gBAAIK,UAAM;AAAA,cAAA,CAAA,GACvBF,kCAAA,IAAA,UAAA;AAAA,gBAAOH,OAAM;AAAA,gBAAKK,UAAO;AAAA,cAAA,CAAA,CAAA;AAAA,YAAA,CAChC,CAAA;AAAA,UACN,CAAA,CAAA;AAAA,QACN,CAAA,GAECF,kCAAA,IAAA,OAAA;AAAA,UAAIC,WAAU;AAAA,UACRC,sBACMF,kCAAA,IAAA,OAAA;AAAA,YAAIC,WAAU;AAAA,YACTC,gDAAC,OAAI;AAAA,cAAAD,WAAU;AAAA,YAA4E,CAAA;AAAA,WACjG,IACF5B,WACE2B,kCAAA,IAAC,UAAA;AAAA,YACKM,IAAG;AAAA,YACHC,KAAKlC;AAAAA,YACLmC,OAAM;AAAA,YACNP,WAAU;AAAA,YACVQ,mBAAiB;AAAA,UAAA,CACvB,IAECT,kCAAA,IAAA,OAAA;AAAA,YAAIC,WAAU;AAAA,YAA+BC;UAE9C,CAAA;AAAA,QAEZ,CAAA,CAAA;AAAA,MACN,CAAA;AAAA,IACN,CAAA;AAAA,EACN,CAAA;AAEZ;"}