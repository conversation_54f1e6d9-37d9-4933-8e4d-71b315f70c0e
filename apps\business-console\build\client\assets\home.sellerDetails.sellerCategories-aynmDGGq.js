import { r as reactExports, j as jsxRuntimeExports } from "./jsx-runtime-bB2y7OuD.js";
import { D as Dialog, a as DialogContent, b as DialogTitle } from "./dialog-BqKosxNq.js";
import { I as Input } from "./input-3v87qohQ.js";
import { R as ResponsivePagination } from "./responsivePagination-D-iSBEkA.js";
import { R as ResponsiveTable } from "./responsiveTable-CMOWL3Wl.js";
import { S as Select, a as SelectTrigger, b as SelectValue, c as SelectContent, d as SelectItem } from "./select-BFGSXKcr.js";
import { u as useToast } from "./ToastProvider-rciVe3-g.js";
import { u as useDebounce } from "./useDebounce-BXbH_IFZ.js";
import { u as useLoaderData, a as useFetcher } from "./components-D7UvGag_.js";
import { a as useNavigate } from "./index-DhHTcibu.js";
import { P as Pencil } from "./pencil-C1goHmP8.js";
import "./index-DdafHWkt.js";
import "./index-D7VH9Fc8.js";
import "./index-z_byfFrQ.js";
import "./index-DVTNuYOr.js";
import "./index-dIGjFc0I.js";
import "./index-BXzPK7u0.js";
import "./index-CzZ6EYkN.js";
import "./index-Vp2vNLNM.js";
import "./index-CEHS9zzk.js";
import "./index-QLGF6kQx.js";
import "./utils-GkgzjW3c.js";
import "./x-CCG_WJDF.js";
import "./createLucideIcon-uwkRm45G.js";
import "./index-IXOTxK3N.js";
import "./index-qCtcpOcW.js";
import "./index-BTdCMChR.js";
import "./index-DscYByPT.js";
import "./index-CkL5tk39.js";
import "./index-CpKiYcZd.js";
import "./index-C88PRvfd.js";
import "./chevron-down-pCP5jmjX.js";
import "./check-_dbWxIzT.js";
const sellerCategoryHeader = ["Category Id", "Category Name", "No. of items", "Sequence", ""];
function SellerCategories() {
  const {
    sellerId,
    sellerName,
    activeTab,
    sellerBId,
    currentPage,
    sellerCategories
  } = useLoaderData();
  const navigate = useNavigate();
  const fetcher = useFetcher();
  const [searchTerm, setSearchTerm] = reactExports.useState("");
  const [pageSize, setPageSize] = reactExports.useState("10");
  const [pageNum, setPageNum] = reactExports.useState(0);
  const [selectedCategory, setSelectedCategory] = reactExports.useState(null);
  const [isEditModalOpen, setIsEditModalOpen] = reactExports.useState(false);
  const handlePageSizeChange = (newPageSize) => {
    setPageSize(newPageSize);
    navigate(`?sellerId=${sellerId}&sellerName=${sellerName}&activeTab=${activeTab}&sellerBId=${sellerBId}&page=${currentPage}&pageSize=${newPageSize}&matchBy=${searchTerm}`);
  };
  const handlePageChange = (newPageSize) => {
    setPageNum(Number(newPageSize));
    navigate(`?sellerId=${sellerId}&sellerName=${sellerName}&activeTab=${activeTab}&sellerBId=${sellerBId}&page=${Number(newPageSize)}&pageSize=${pageSize}&matchBy=${searchTerm}`);
  };
  const handleEditModal = (row) => {
    setSelectedCategory(row);
    setIsEditModalOpen(true);
  };
  const handleSave = (updatedData) => {
    console.log("Updated Data:", updatedData);
    const formData = new FormData();
    formData.append("intent", "editSellerCategory");
    formData.append("sellerCategory", JSON.stringify(updatedData));
    formData.append("categoryId", updatedData.id);
    formData.append("sellerId", sellerId.toString());
    fetcher.submit(formData, {
      method: "put"
    });
    setIsEditModalOpen(false);
  };
  const debounceSearchTerm = useDebounce(searchTerm, 300);
  const handlePageSearch = (value) => {
    setSearchTerm(value);
  };
  reactExports.useEffect(() => {
    if (debounceSearchTerm.length >= 3) {
      navigate(`?sellerId=${sellerId}&sellerName=${sellerName}&activeTab=${activeTab}&sellerBId=${sellerBId}&page=${currentPage}&pageSize=${pageSize}&matchBy=${encodeURIComponent(debounceSearchTerm)}`);
    } else {
      navigate(`?sellerId=${sellerId}&sellerName=${sellerName}&activeTab=${activeTab}&sellerBId=${sellerBId}&page=${currentPage}&pageSize=${pageSize}`);
    }
  }, [debounceSearchTerm]);
  reactExports.useEffect(() => {
    if (isEditModalOpen === false) {
      setSelectedCategory(null);
    }
  }, [isEditModalOpen]);
  const {
    showToast
  } = useToast();
  const [isSuccess, setIsSuccess] = reactExports.useState(false);
  reactExports.useEffect(() => {
    var _a, _b;
    if (((_a = fetcher == null ? void 0 : fetcher.data) == null ? void 0 : _a.success) === true) {
      setIsSuccess(true);
      showToast("Sucessfully updated category sequence", "success");
      navigate(`?sellerId=${sellerId}&sellerName=${sellerName}&activeTab=${activeTab}&sellerBId=${sellerBId}&page=${currentPage}&pageSize=${pageSize}&matchBy=${searchTerm}`);
    } else if (((_b = fetcher.data) == null ? void 0 : _b.success) === false) {
      setIsSuccess(false);
      showToast("Failed to Update category sequence", "error");
    }
  }, [fetcher.data]);
  return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
    children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
      className: "flex justify-between my-4",
      children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Input, {
        placeholder: "Search by Category Name",
        value: searchTerm,
        type: "search",
        onChange: (e) => handlePageSearch(e.target.value),
        className: "max-w-sm  rounded-full"
      }), /* @__PURE__ */ jsxRuntimeExports.jsxs(Select, {
        value: pageSize,
        onValueChange: handlePageSizeChange,
        children: [/* @__PURE__ */ jsxRuntimeExports.jsx(SelectTrigger, {
          className: "w-[180px]",
          children: /* @__PURE__ */ jsxRuntimeExports.jsx(SelectValue, {
            placeholder: "Categories per page"
          })
        }), /* @__PURE__ */ jsxRuntimeExports.jsxs(SelectContent, {
          children: [/* @__PURE__ */ jsxRuntimeExports.jsx(SelectItem, {
            value: "5",
            children: "5 per page"
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(SelectItem, {
            value: "10",
            children: "10 per page"
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(SelectItem, {
            value: "20",
            children: "20 per page"
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(SelectItem, {
            value: "50",
            children: "50 per page"
          })]
        })]
      })]
    }), /* @__PURE__ */ jsxRuntimeExports.jsx(ResponsiveTable, {
      headers: sellerCategoryHeader,
      data: sellerCategories,
      renderRow: (row) => {
        return /* @__PURE__ */ jsxRuntimeExports.jsxs("tr", {
          className: "border-b",
          children: [/* @__PURE__ */ jsxRuntimeExports.jsx("td", {
            className: "py-2 px-3 text-center whitespace-normal break-words sticky left-0 bg-white z-10",
            children: row.id
          }), /* @__PURE__ */ jsxRuntimeExports.jsx("td", {
            className: "py-2 px-3 text-center sticky left-36 bg-white z-10",
            children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
              className: "flex items-center gap-2 justify-center p-1 rounded-md",
              children: [/* @__PURE__ */ jsxRuntimeExports.jsx("img", {
                src: row == null ? void 0 : row.picture,
                alt: "image",
                className: "h-10 w-10 rounded-full object-cover flex-shrink-0"
              }), /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                className: "w-40 flex-wrap break-words text-left cursor-pointer",
                children: row == null ? void 0 : row.name
              })]
            })
          }), /* @__PURE__ */ jsxRuntimeExports.jsx("td", {
            className: "py-2 px-3 text-center  whitespace-normal break-words",
            children: row == null ? void 0 : row.totalItems
          }), /* @__PURE__ */ jsxRuntimeExports.jsxs("td", {
            className: "py-2 px-3 text-center  whitespace-normal break-words flex flex-row gap-2 justify-center",
            children: [row == null ? void 0 : row.sequence, /* @__PURE__ */ jsxRuntimeExports.jsx(Pencil, {
              className: "w-4 h-4 my-auto cursor-pointer",
              onClick: () => handleEditModal(row)
            })]
          })]
        }, row.id);
      }
    }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
      className: "flex items-center justify-center space-x-2 py-4 overflow-x-auto whitespace-nowrap",
      children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("h2", {
        className: "shrink-0",
        children: ["Current Page: ", pageNum + 1]
      }), /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
        className: "overflow-x-auto",
        children: /* @__PURE__ */ jsxRuntimeExports.jsx(ResponsivePagination, {
          totalPages: Number(pageSize),
          currentPage: pageNum,
          onPageChange: (pageNum2) => handlePageChange(pageNum2.toString())
        })
      })]
    }), /* @__PURE__ */ jsxRuntimeExports.jsx(Dialog, {
      open: isEditModalOpen,
      onOpenChange: setIsEditModalOpen,
      children: /* @__PURE__ */ jsxRuntimeExports.jsxs(DialogContent, {
        className: "max-w-md p-6 bg-white rounded-xl shadow-2xl sm:max-w-lg",
        children: [/* @__PURE__ */ jsxRuntimeExports.jsx(DialogTitle, {
          className: "text-xl font-bold text-gray-900 sm:text-2xl",
          children: "Edit Category"
        }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
          className: "flex flex-col gap-4",
          children: [/* @__PURE__ */ jsxRuntimeExports.jsx("label", {
            children: "Category Sequence"
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(Input, {
            id: "sequence",
            placeholder: "Enter sequence",
            type: "number",
            className: "w-full h-8",
            required: true,
            value: selectedCategory == null ? void 0 : selectedCategory.sequence,
            onChange: (e) => selectedCategory && setSelectedCategory({
              ...selectedCategory,
              sequence: Number(e.target.value)
            })
          })]
        }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
          className: "mt-6 flex flex-col gap-3 sm:flex-row sm:justify-end",
          children: [/* @__PURE__ */ jsxRuntimeExports.jsx("button", {
            onClick: () => setIsEditModalOpen(false),
            className: "w-full rounded-lg bg-gray-100 px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-300 sm:w-auto",
            children: "Cancel"
          }), /* @__PURE__ */ jsxRuntimeExports.jsx("button", {
            onClick: () => handleSave(selectedCategory),
            className: "w-full rounded-lg bg-blue-600 px-4 py-2 text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 sm:w-auto",
            children: "Save"
          })]
        })]
      })
    })]
  });
}
export {
  SellerCategories as default
};
//# sourceMappingURL=home.sellerDetails.sellerCategories-aynmDGGq.js.map
