import { j as jsxRuntimeExports } from "./jsx-runtime-bB2y7OuD.js";
import { C as Card, b as <PERSON><PERSON><PERSON><PERSON>, c as <PERSON><PERSON><PERSON><PERSON>, d as CardDescription, a as CardContent } from "./card-BJQMSLe_.js";
import { F as FaWhatsapp } from "./index-Bx88Z3Oa.js";
import { L as Link } from "./components-D7UvGag_.js";
import { c as createLucideIcon } from "./createLucideIcon-uwkRm45G.js";
import "./utils-GkgzjW3c.js";
import "./index-Vp2vNLNM.js";
import "./index-DhHTcibu.js";
/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */
const GalleryHorizontal = createLucideIcon("GalleryHorizontal", [
  ["path", { d: "M2 3v18", key: "pzttux" }],
  ["rect", { width: "12", height: "18", x: "6", y: "3", rx: "2", key: "btr8bg" }],
  ["path", { d: "M22 3v18", key: "6jf3v" }]
]);
function Settings() {
  return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
    className: "p-6",
    children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
      className: "mb-6",
      children: [/* @__PURE__ */ jsxRuntimeExports.jsx("h1", {
        className: "text-3xl font-bold text-gray-900",
        children: "Settings"
      }), /* @__PURE__ */ jsxRuntimeExports.jsx("p", {
        className: "text-gray-600 mt-2",
        children: "Manage your restaurant configuration"
      })]
    }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
      className: "mt-8",
      children: [/* @__PURE__ */ jsxRuntimeExports.jsx("h2", {
        className: "text-xl font-semibold text-gray-900 mb-4",
        children: "Coming Soon Features"
      }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
        className: "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",
        children: [/* @__PURE__ */ jsxRuntimeExports.jsxs(Card, {
          className: "border-dashed border-2 border-gray-300",
          children: [/* @__PURE__ */ jsxRuntimeExports.jsxs(CardHeader, {
            children: [/* @__PURE__ */ jsxRuntimeExports.jsx(CardTitle, {
              className: "text-gray-600",
              children: "Advanced Analytics"
            }), /* @__PURE__ */ jsxRuntimeExports.jsx(CardDescription, {
              children: "Detailed business insights"
            })]
          }), /* @__PURE__ */ jsxRuntimeExports.jsxs(CardContent, {
            children: [/* @__PURE__ */ jsxRuntimeExports.jsx("div", {
              className: "text-3xl font-bold text-gray-400",
              children: "📊"
            }), /* @__PURE__ */ jsxRuntimeExports.jsx("p", {
              className: "text-sm text-gray-500 mt-2",
              children: "Launching soon"
            })]
          })]
        }), /* @__PURE__ */ jsxRuntimeExports.jsxs(Card, {
          className: "border-dashed border-2 border-gray-300",
          children: [/* @__PURE__ */ jsxRuntimeExports.jsxs(CardHeader, {
            children: [/* @__PURE__ */ jsxRuntimeExports.jsx(CardTitle, {
              className: "text-gray-600",
              children: "Inventory Management"
            }), /* @__PURE__ */ jsxRuntimeExports.jsx(CardDescription, {
              children: "Stock tracking system"
            })]
          }), /* @__PURE__ */ jsxRuntimeExports.jsxs(CardContent, {
            children: [/* @__PURE__ */ jsxRuntimeExports.jsx("div", {
              className: "text-3xl font-bold text-gray-400",
              children: "📦"
            }), /* @__PURE__ */ jsxRuntimeExports.jsx("p", {
              className: "text-sm text-gray-500 mt-2",
              children: "Launching soon"
            })]
          })]
        }), /* @__PURE__ */ jsxRuntimeExports.jsxs(Card, {
          className: "border-dashed border-2 border-gray-300",
          children: [/* @__PURE__ */ jsxRuntimeExports.jsxs(CardHeader, {
            children: [/* @__PURE__ */ jsxRuntimeExports.jsx(CardTitle, {
              className: "text-gray-600",
              children: "Customer Loyalty"
            }), /* @__PURE__ */ jsxRuntimeExports.jsx(CardDescription, {
              children: "Rewards program"
            })]
          }), /* @__PURE__ */ jsxRuntimeExports.jsxs(CardContent, {
            children: [/* @__PURE__ */ jsxRuntimeExports.jsx("div", {
              className: "text-3xl font-bold text-gray-400",
              children: "🎁"
            }), /* @__PURE__ */ jsxRuntimeExports.jsx("p", {
              className: "text-sm text-gray-500 mt-2",
              children: "Launching soon"
            })]
          })]
        }), /* @__PURE__ */ jsxRuntimeExports.jsxs(Card, {
          className: "border-dashed border-2 border-gray-300",
          children: [/* @__PURE__ */ jsxRuntimeExports.jsxs(CardHeader, {
            children: [/* @__PURE__ */ jsxRuntimeExports.jsx(CardTitle, {
              className: "text-gray-600",
              children: "Multi-location"
            }), /* @__PURE__ */ jsxRuntimeExports.jsx(CardDescription, {
              children: "Branch management"
            })]
          }), /* @__PURE__ */ jsxRuntimeExports.jsxs(CardContent, {
            children: [/* @__PURE__ */ jsxRuntimeExports.jsx("div", {
              className: "text-3xl font-bold text-gray-400",
              children: "🏢"
            }), /* @__PURE__ */ jsxRuntimeExports.jsx("p", {
              className: "text-sm text-gray-500 mt-2",
              children: "Launching soon"
            })]
          })]
        }), /* @__PURE__ */ jsxRuntimeExports.jsxs(Card, {
          className: "border-dashed border-2 border-gray-300",
          children: [/* @__PURE__ */ jsxRuntimeExports.jsxs(CardHeader, {
            children: [/* @__PURE__ */ jsxRuntimeExports.jsx(CardTitle, {
              className: "text-gray-600",
              children: "API Integration"
            }), /* @__PURE__ */ jsxRuntimeExports.jsx(CardDescription, {
              children: "Third-party connections"
            })]
          }), /* @__PURE__ */ jsxRuntimeExports.jsxs(CardContent, {
            children: [/* @__PURE__ */ jsxRuntimeExports.jsx("div", {
              className: "text-3xl font-bold text-gray-400",
              children: "🔌"
            }), /* @__PURE__ */ jsxRuntimeExports.jsx("p", {
              className: "text-sm text-gray-500 mt-2",
              children: "Launching soon"
            })]
          })]
        }), /* @__PURE__ */ jsxRuntimeExports.jsxs(Card, {
          className: "border-dashed border-2 border-gray-300",
          children: [/* @__PURE__ */ jsxRuntimeExports.jsxs(CardHeader, {
            children: [/* @__PURE__ */ jsxRuntimeExports.jsx(CardTitle, {
              className: "text-gray-600",
              children: "Advanced Reports"
            }), /* @__PURE__ */ jsxRuntimeExports.jsx(CardDescription, {
              children: "Custom reporting"
            })]
          }), /* @__PURE__ */ jsxRuntimeExports.jsxs(CardContent, {
            children: [/* @__PURE__ */ jsxRuntimeExports.jsx("div", {
              className: "text-3xl font-bold text-gray-400",
              children: "📈"
            }), /* @__PURE__ */ jsxRuntimeExports.jsx("p", {
              className: "text-sm text-gray-500 mt-2",
              children: "Launching soon"
            })]
          })]
        })]
      })]
    }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
      className: "mt-8",
      children: [/* @__PURE__ */ jsxRuntimeExports.jsx("h2", {
        className: "text-xl font-semibold text-gray-900 mb-4",
        children: "Additional Settings"
      }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
        className: "grid grid-cols-1 md:grid-cols-3 gap-4",
        children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Link, {
          to: "/sellerSetting/nbanners",
          children: /* @__PURE__ */ jsxRuntimeExports.jsx(Card, {
            className: "hover:shadow-lg transition-shadow cursor-pointer",
            children: /* @__PURE__ */ jsxRuntimeExports.jsxs(CardHeader, {
              children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                className: "flex items-center space-x-3",
                children: [/* @__PURE__ */ jsxRuntimeExports.jsx(GalleryHorizontal, {
                  className: "text-primary",
                  size: 20
                }), /* @__PURE__ */ jsxRuntimeExports.jsx(CardTitle, {
                  className: "text-lg",
                  children: "Banners & Sequence"
                })]
              }), /* @__PURE__ */ jsxRuntimeExports.jsx(CardDescription, {
                children: "Manage promotional banners"
              })]
            })
          })
        }), /* @__PURE__ */ jsxRuntimeExports.jsx(Link, {
          to: "/sellerSetting/whatsappprofile",
          children: /* @__PURE__ */ jsxRuntimeExports.jsx(Card, {
            className: "hover:shadow-lg transition-shadow cursor-pointer",
            children: /* @__PURE__ */ jsxRuntimeExports.jsxs(CardHeader, {
              children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                className: "flex items-center space-x-3",
                children: [/* @__PURE__ */ jsxRuntimeExports.jsx(FaWhatsapp, {
                  className: "text-primary",
                  size: 20
                }), /* @__PURE__ */ jsxRuntimeExports.jsx(CardTitle, {
                  className: "text-lg",
                  children: "WhatsApp Settings"
                })]
              }), /* @__PURE__ */ jsxRuntimeExports.jsx(CardDescription, {
                children: "Configure WhatsApp integration"
              })]
            })
          })
        }), /* @__PURE__ */ jsxRuntimeExports.jsx(Link, {
          to: "/sellerSetting/coupons",
          children: /* @__PURE__ */ jsxRuntimeExports.jsx(Card, {
            className: "hover:shadow-lg transition-shadow cursor-pointer",
            children: /* @__PURE__ */ jsxRuntimeExports.jsxs(CardHeader, {
              children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                className: "flex items-center space-x-3",
                children: [/* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                  className: "text-2xl",
                  children: "🎉"
                }), /* @__PURE__ */ jsxRuntimeExports.jsx(CardTitle, {
                  className: "text-lg",
                  children: "Coupons"
                })]
              }), /* @__PURE__ */ jsxRuntimeExports.jsx(CardDescription, {
                children: "Manage discount offers"
              })]
            })
          })
        })]
      })]
    })]
  });
}
export {
  Settings as default
};
//# sourceMappingURL=sellerSetting.settings-CAz6o2Cx.js.map
