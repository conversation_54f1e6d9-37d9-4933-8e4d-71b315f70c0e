{"version": 3, "file": "DeliveryConfig-Dk5cNfWo.js", "sources": ["../../../app/types/api/businessConsoleService/DeliveryConfig.ts"], "sourcesContent": ["\r\nexport enum ConfigType{\r\n  PERCENTAGE_BASED=\"PERCENTAGE_BASED\",\r\n  ORDER_VALUE_BASED=\"ORDER_VALUE_BASED\"\r\n}\r\nexport interface DcBody{\r\n  sellerId:number,\r\n  configType:ConfigType,\r\n  buyerPercentage:number,\r\n  sellerPercentage:number,\r\n  minOrderValue:number,\r\n  maxOrderValue:number,\r\n  maxBuyerDeliveryCharge:number,\r\n  maxSellerDeliveryCharge:number,\r\n  active:boolean\r\n}\r\nexport interface dclistingResponse{\r\n  \r\n    id:number,\r\n    sellerId:number,\r\n    configType:ConfigType,\r\n    buyerPercentage:number,\r\n    sellerPercentage:number,\r\n    minOrderValue:number,\r\n    maxOrderValue:number,\r\n    maxBuyerDeliveryCharge:number,\r\n    maxSellerDeliveryCharge:number,\r\n    active:boolean,\r\n    createdAt: string,\r\n    updatedAt:string,\r\n    version: number\r\n  \r\n}\r\nexport interface DcCong{\r\n  success:boolean,\r\n  data:dclistingResponse[],\r\n  error: {\r\n    code: string,\r\n    message: string\r\n  }\r\n}\r\nexport interface DcCreateRes{\r\n  success:boolean,\r\n  data:dclistingResponse,\r\n  error: {\r\n    code: string,\r\n    message: string\r\n  }\r\n}\r\n\r\n\r\n\r\n\r\n\r\n"], "names": ["ConfigType"], "mappings": "AACY,IAAA,+BAAAA,gBAAL;AACLA,cAAA,kBAAiB,IAAA;AACjBA,cAAA,mBAAkB,IAAA;AAFRA,SAAAA;AAAA,GAAA,cAAA,CAAA,CAAA;"}