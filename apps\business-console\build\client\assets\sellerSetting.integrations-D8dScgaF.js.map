{"version": 3, "file": "sellerSetting.integrations-D8dScgaF.js", "sources": ["../../../app/routes/sellerSetting.integrations.tsx"], "sourcesContent": ["import { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"~/components/ui/card\";\r\n\r\nexport default function Integrations() {\r\n  return (\r\n    <div className=\"p-6\">\r\n      <div className=\"mb-6\">\r\n        <h1 className=\"text-3xl font-bold text-gray-900\">Integrations</h1>\r\n        <p className=\"text-gray-600 mt-2\">Connect with third-party services</p>\r\n      </div>\r\n      \r\n      <div className=\"flex items-center justify-center min-h-[400px]\">\r\n        <Card className=\"w-full max-w-md\">\r\n          <CardHeader className=\"text-center\">\r\n            <div className=\"mx-auto mb-4 w-16 h-16 bg-gradient-to-r from-yellow-500 to-orange-600 rounded-full flex items-center justify-center\">\r\n              <svg className=\"w-8 h-8 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 10V3L4 14h7v7l9-11h-7z\" />\r\n              </svg>\r\n            </div>\r\n            <CardTitle className=\"text-2xl\">Integrations Coming Soon</CardTitle>\r\n            <CardDescription className=\"text-lg\">\r\n              We&apos;re working on seamless integrations with your favorite services.\r\n            </CardDescription>\r\n          </CardHeader>\r\n          <CardContent className=\"text-center\">\r\n            <p className=\"text-gray-500 mb-4\">\r\n              Connect payment gateways, delivery partners, and analytics tools in one place.\r\n            </p>\r\n            <div className=\"inline-flex items-center px-4 py-2 bg-gradient-to-r from-yellow-500 to-orange-600 text-black rounded-lg\">\r\n              <span className=\"mr-2\">🔌</span>\r\n              Launching Soon\r\n            </div>\r\n          </CardContent>\r\n        </Card>\r\n      </div>\r\n    </div>\r\n  );\r\n} "], "names": ["Integrations", "jsxs", "className", "children", "jsx", "Card", "<PERSON><PERSON><PERSON><PERSON>", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "CardTitle", "CardDescription", "<PERSON><PERSON><PERSON><PERSON>"], "mappings": ";;;AAEA,SAAwBA,eAAe;AAEnC,SAAAC,kCAAAA,KAAC,OAAI;AAAA,IAAAC,WAAU;AAAA,IACbC,UAAA,CAACF,kCAAA,KAAA,OAAA;AAAA,MAAIC,WAAU;AAAA,MACbC,UAAA,CAACC,kCAAA,IAAA,MAAA;AAAA,QAAGF,WAAU;AAAA,QAAmCC,UAAY;AAAA,MAAA,CAAA,GAC5DC,kCAAA,IAAA,KAAA;AAAA,QAAEF,WAAU;AAAA,QAAqBC,UAAiC;AAAA,MAAA,CAAA,CAAA;AAAA,IACrE,CAAA,yCAEC,OAAI;AAAA,MAAAD,WAAU;AAAA,MACbC,UAACF,kCAAA,KAAAI,MAAA;AAAA,QAAKH,WAAU;AAAA,QACdC,UAAA,CAACF,kCAAA,KAAAK,YAAA;AAAA,UAAWJ,WAAU;AAAA,UACpBC,UAAA,CAACC,kCAAA,IAAA,OAAA;AAAA,YAAIF,WAAU;AAAA,YACbC,UAACC,kCAAA,IAAA,OAAA;AAAA,cAAIF,WAAU;AAAA,cAAqBK,MAAK;AAAA,cAAOC,QAAO;AAAA,cAAeC,SAAQ;AAAA,cAC5EN,UAACC,kCAAA,IAAA,QAAA;AAAA,gBAAKM,eAAc;AAAA,gBAAQC,gBAAe;AAAA,gBAAQC,aAAa;AAAA,gBAAGC,GAAE;AAAA,cAA6B,CAAA;AAAA,YACpG,CAAA;AAAA,UACF,CAAA,GACCT,kCAAA,IAAAU,WAAA;AAAA,YAAUZ,WAAU;AAAA,YAAWC,UAAwB;AAAA,UAAA,CAAA,GACvDC,kCAAA,IAAAW,iBAAA;AAAA,YAAgBb,WAAU;AAAA,YAAUC,UAErC;AAAA,UAAA,CAAA,CAAA;AAAA,QACF,CAAA,GACAF,kCAAA,KAACe,aAAY;AAAA,UAAAd,WAAU;AAAA,UACrBC,UAAA,CAACC,kCAAA,IAAA,KAAA;AAAA,YAAEF,WAAU;AAAA,YAAqBC,UAElC;AAAA,UAAA,CAAA,GACAF,kCAAA,KAAC,OAAI;AAAA,YAAAC,WAAU;AAAA,YACbC,UAAA,CAACC,kCAAA,IAAA,QAAA;AAAA,cAAKF,WAAU;AAAA,cAAOC,UAAE;AAAA,YAAA,CAAA,GAAO,gBAAA;AAAA,UAElC,CAAA,CAAA;AAAA,QACF,CAAA,CAAA;AAAA,MACF,CAAA;AAAA,IACF,CAAA,CAAA;AAAA,EACF,CAAA;AAEJ;"}