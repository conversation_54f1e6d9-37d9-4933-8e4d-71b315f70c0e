{"version": 3, "file": "AddEntryModal-BDnVYESI.js", "sources": ["../../../node_modules/lucide-react/dist/esm/icons/printer.js", "../../../app/types/api/businessConsoleService/ItemStock.ts", "../../../app/components/StockComponent/AddEntryModal.tsx"], "sourcesContent": ["/**\n * @license lucide-react v0.453.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst Printer = createLucideIcon(\"Printer\", [\n  [\n    \"path\",\n    {\n      d: \"M6 18H4a2 2 0 0 1-2-2v-5a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v5a2 2 0 0 1-2 2h-2\",\n      key: \"143wyd\"\n    }\n  ],\n  [\"path\", { d: \"M6 9V3a1 1 0 0 1 1-1h10a1 1 0 0 1 1 1v6\", key: \"1itne7\" }],\n  [\"rect\", { x: \"6\", y: \"14\", width: \"12\", height: \"8\", rx: \"1\", key: \"1ue0tg\" }]\n]);\n\nexport { Printer as default };\n//# sourceMappingURL=printer.js.map\n", "export interface ItemStock {\r\n  stockId:number\r\n      itemName: string;\r\n      distributor: string;\r\n      supplier: string;\r\n      maxAvailableQty: number;\r\n      pricePerUnit: number;\r\n      active: boolean;\r\n    }\r\n    \r\n    export enum StockTransactionType {\r\n      RECEIVED = 'RECEIVED',\r\n      DELIVERED = 'DELIVERED',\r\n      SPOILED = 'SPOILED',\r\n      RETURNED = 'RETURNED',\r\n      CONVERTED = 'CONVERTED',\r\n      CORRECTION = 'CORRECTION'\r\n    }\r\n    export interface InvStockTransaction {\r\n      deliveryDate: string;\r\n      invStockTransactionId: number;\r\n      transactionType: StockTransactionType;\r\n      quantity: number;\r\n      unitPrice: number;\r\n      totalValue: number;\r\n      narration: string;\r\n      username: string;\r\n      balanceAfter: number;\r\n      totalReceived: number;\r\n      totalDelivered: number;\r\n      totalReturned: number;\r\n      totalSpoiled: number;\r\n      totalConverted: number;\r\n      totalCorrection: number;\r\n      itemTotalAmount:number,\r\n      totalDistributionCharges:number,\r\n      totalSalesComm:number;\r\n      supplierNetAmount:number;\r\n    }\r\n    export interface CreateStockTransactionInput {\r\n          stockTransactionType: StockTransactionType;\r\n          quantity: number;\r\n          narration: string;\r\n          deliveryDate: string; // or use `Date` if you prefer\r\n        }\r\n\r\n       \r\n    \r\n", "import { CreateStockTransactionInput, StockTransactionType } from \"~/types/api/businessConsoleService/ItemStock\";\r\nimport { Button } from \"../ui/button\";\r\nimport { Dialog, DialogContent, DialogFooter, DialogHeader, DialogTitle } from \"../ui/dialog\";\r\nimport { useState } from \"react\";\r\nimport { useFetcher, useNavigation } from \"@remix-run/react\";\r\n\r\ninterface AddEntryModalProps {\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n  itemId: number;\r\n  onAddEntry: (data: CreateStockTransactionInput) => Promise<void>;\r\n}\r\nfunction AddEntryModal({ isOpen, onClose, itemId, onAddEntry }: AddEntryModalProps) {\r\n  const [formData, setFormData] = useState<CreateStockTransactionInput>({\r\n    stockTransactionType: StockTransactionType.CORRECTION, // or another valid StockTransactionType value\r\n    narration: \"\",\r\n    quantity: 0,\r\n    deliveryDate: new Date().toISOString().split(\"T\")[0],\r\n  });\r\n  const [error, setError] = useState<string | null>(null);\r\n  const [loading, setLoading] = useState(false);   \r\n\r\n  const handleSubmit = async (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n    if (!formData.stockTransactionType || !formData.narration || formData.quantity <= 0) {\r\n      setError(\"All fields are required and must be valid\");\r\n      return;\r\n    }\r\n\r\n    setLoading(true);\r\n    try {\r\n      await onAddEntry(formData);\r\n      setFormData({\r\n        stockTransactionType: StockTransactionType.CORRECTION,\r\n        narration: \"\",\r\n        quantity: 0,\r\n        deliveryDate: new Date().toISOString().split(\"T\")[0],\r\n      });\r\n      setError(null);\r\n      onClose(); // Close modal on success\r\n    } catch (err: any) {\r\n      setError(err.message || \"Failed to create transaction\");\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n\r\n\r\n  return (\r\n    <Dialog open={isOpen} onOpenChange={onClose}>\r\n      <DialogContent className=\"sm:max-w-[425px]\">\r\n        <DialogHeader>\r\n          <DialogTitle>Add New Transaction</DialogTitle>\r\n        </DialogHeader>\r\n        <form onSubmit={handleSubmit}>\r\n          {error && <div className=\"mb-4 text-red-600\">{error}</div>}\r\n          <div className=\"grid gap-4 py-4\">\r\n            <div className=\"grid grid-cols-4 items-center gap-4\">\r\n              <label htmlFor=\"transactionType\" className=\"text-right text-sm font-medium\">\r\n                Type:\r\n              </label>\r\n              <select\r\n                id=\"transactionType\"\r\n                value={formData.stockTransactionType}\r\n                onChange={(e) => setFormData({ ...formData, stockTransactionType: e.target.value as StockTransactionType })}\r\n                className=\"col-span-3 border rounded-md p-2\"\r\n                required\r\n              >\r\n                <option value=\"\">Select Type</option>\r\n                <option value={StockTransactionType.RECEIVED}>Received</option>\r\n                <option value={StockTransactionType.DELIVERED}>Delivered</option>\r\n                <option value={StockTransactionType.SPOILED}>Spoiled</option>\r\n                <option value={StockTransactionType.RETURNED}>Returned</option>\r\n                <option value={StockTransactionType.CORRECTION}>Correction</option>\r\n                <option value={StockTransactionType.CONVERTED}>Converted</option>\r\n              </select>\r\n            </div>\r\n            <div className=\"grid grid-cols-4 items-center gap-4\">\r\n              <label htmlFor=\"narration\" className=\"text-right text-sm font-medium\">\r\n                Narration:\r\n              </label>\r\n              <input\r\n                id=\"narration\"\r\n                value={formData.narration}\r\n                onChange={(e) => setFormData({ ...formData, narration: e.target.value })}\r\n                className=\"col-span-3 border rounded-md p-2\"\r\n                required\r\n              />\r\n            </div>\r\n            <div className=\"grid grid-cols-4 items-center gap-4\">\r\n              <label htmlFor=\"quantity\" className=\"text-right text-sm font-medium\">\r\n                Quantity:\r\n              </label>\r\n              <input\r\n                id=\"quantity\"\r\n                type=\"number\"\r\n                value={formData.quantity}\r\n                onChange={(e) => setFormData({ ...formData, quantity: Number(e.target.value) })}\r\n                min=\"1\"\r\n                className=\"col-span-3 border rounded-md p-2\"\r\n                required\r\n              />\r\n            </div>\r\n            <div className=\"grid grid-cols-4 items-center gap-4\">\r\n              <label htmlFor=\"deliveryDate\" className=\"text-right text-sm font-medium\">\r\n                Date:\r\n              </label>\r\n              <input\r\n                id=\"deliveryDate\"\r\n                type=\"date\"\r\n                value={formData.deliveryDate}\r\n                onChange={(e) => setFormData({ ...formData, deliveryDate: e.target.value })}\r\n                className=\"col-span-3 border rounded-md p-2\"\r\n                required\r\n              />\r\n            </div>\r\n          </div>\r\n          <DialogFooter>\r\n            <Button type=\"button\" variant=\"outline\" onClick={onClose}>\r\n              Cancel\r\n            </Button>\r\n            <Button\r\n              type=\"submit\" \r\n              loading={loading}\r\n              className=\"ml-2\"\r\n              disabled={loading}\r\n            >\r\n              {loading ? \"Creating...\" : \"Add Transaction\"}\r\n            </Button>\r\n          </DialogFooter>\r\n        </form>\r\n      </DialogContent>\r\n    </Dialog>\r\n  );\r\n}\r\n\r\nexport default AddEntryModal;\r\n"], "names": ["StockTransactionType", "useState", "jsx", "jsxs"], "mappings": ";;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASK,MAAC,UAAU,iBAAiB,WAAW;AAAA,EAC1C;AAAA,IACE;AAAA,IACA;AAAA,MACE,GAAG;AAAA,MACH,KAAK;AAAA,IACX;AAAA,EACG;AAAA,EACD,CAAC,QAAQ,EAAE,GAAG,2CAA2C,KAAK,SAAQ,CAAE;AAAA,EACxE,CAAC,QAAQ,EAAE,GAAG,KAAK,GAAG,MAAM,OAAO,MAAM,QAAQ,KAAK,IAAI,KAAK,KAAK,SAAU,CAAA;AAChF,CAAC;ACTe,IAAA,yCAAAA,0BAAL;AACLA,wBAAA,UAAW,IAAA;AACXA,wBAAA,WAAY,IAAA;AACZA,wBAAA,SAAU,IAAA;AACVA,wBAAA,UAAW,IAAA;AACXA,wBAAA,WAAY,IAAA;AACZA,wBAAA,YAAa,IAAA;AANHA,SAAAA;AAAA,GAAA,wBAAA,CAAA,CAAA;ACEhB,SAAS,cAAc,EAAE,QAAQ,SAAS,QAAQ,cAAkC;AAClF,QAAM,CAAC,UAAU,WAAW,IAAIC,sBAAsC;AAAA,IACpE,sBAAsB,qBAAqB;AAAA;AAAA,IAC3C,WAAW;AAAA,IACX,UAAU;AAAA,IACV,mCAAkB,KAAK,GAAE,cAAc,MAAM,GAAG,EAAE,CAAC;AAAA,EAAA,CACpD;AACD,QAAM,CAAC,OAAO,QAAQ,IAAIA,aAAAA,SAAwB,IAAI;AACtD,QAAM,CAAC,SAAS,UAAU,IAAIA,aAAAA,SAAS,KAAK;AAEtC,QAAA,eAAe,OAAO,MAAuB;AACjD,MAAE,eAAe;AACb,QAAA,CAAC,SAAS,wBAAwB,CAAC,SAAS,aAAa,SAAS,YAAY,GAAG;AACnF,eAAS,2CAA2C;AACpD;AAAA,IAAA;AAGF,eAAW,IAAI;AACX,QAAA;AACF,YAAM,WAAW,QAAQ;AACb,kBAAA;AAAA,QACV,sBAAsB,qBAAqB;AAAA,QAC3C,WAAW;AAAA,QACX,UAAU;AAAA,QACV,mCAAkB,KAAK,GAAE,cAAc,MAAM,GAAG,EAAE,CAAC;AAAA,MAAA,CACpD;AACD,eAAS,IAAI;AACL,cAAA;AAAA,aACD,KAAU;AACR,eAAA,IAAI,WAAW,8BAA8B;AAAA,IAAA,UACtD;AACA,iBAAW,KAAK;AAAA,IAAA;AAAA,EAEpB;AAKE,SAAAC,kCAAA,IAAC,UAAO,MAAM,QAAQ,cAAc,SAClC,UAAAC,kCAAA,KAAC,eAAc,EAAA,WAAU,oBACvB,UAAA;AAAA,IAAAD,sCAAC,cACC,EAAA,UAAAA,kCAAA,IAAC,aAAY,EAAA,UAAA,sBAAmB,CAAA,GAClC;AAAA,IACAC,kCAAAA,KAAC,QAAK,EAAA,UAAU,cACb,UAAA;AAAA,MAAA,SAAUD,kCAAA,IAAA,OAAA,EAAI,WAAU,qBAAqB,UAAM,OAAA;AAAA,MACpDC,kCAAAA,KAAC,OAAI,EAAA,WAAU,mBACb,UAAA;AAAA,QAACA,kCAAAA,KAAA,OAAA,EAAI,WAAU,uCACb,UAAA;AAAA,UAAAD,sCAAC,SAAM,EAAA,SAAQ,mBAAkB,WAAU,kCAAiC,UAE5E,SAAA;AAAA,UACAC,kCAAA;AAAA,YAAC;AAAA,YAAA;AAAA,cACC,IAAG;AAAA,cACH,OAAO,SAAS;AAAA,cAChB,UAAU,CAAC,MAAM,YAAY,EAAE,GAAG,UAAU,sBAAsB,EAAE,OAAO,OAA+B;AAAA,cAC1G,WAAU;AAAA,cACV,UAAQ;AAAA,cAER,UAAA;AAAA,gBAACD,kCAAA,IAAA,UAAA,EAAO,OAAM,IAAG,UAAW,eAAA;AAAA,gBAC3BA,kCAAA,IAAA,UAAA,EAAO,OAAO,qBAAqB,UAAU,UAAQ,YAAA;AAAA,gBACrDA,kCAAA,IAAA,UAAA,EAAO,OAAO,qBAAqB,WAAW,UAAS,aAAA;AAAA,gBACvDA,kCAAA,IAAA,UAAA,EAAO,OAAO,qBAAqB,SAAS,UAAO,WAAA;AAAA,gBACnDA,kCAAA,IAAA,UAAA,EAAO,OAAO,qBAAqB,UAAU,UAAQ,YAAA;AAAA,gBACrDA,kCAAA,IAAA,UAAA,EAAO,OAAO,qBAAqB,YAAY,UAAU,cAAA;AAAA,gBACzDA,kCAAA,IAAA,UAAA,EAAO,OAAO,qBAAqB,WAAW,UAAS,YAAA,CAAA;AAAA,cAAA;AAAA,YAAA;AAAA,UAAA;AAAA,QAC1D,GACF;AAAA,QACAC,kCAAAA,KAAC,OAAI,EAAA,WAAU,uCACb,UAAA;AAAA,UAAAD,sCAAC,SAAM,EAAA,SAAQ,aAAY,WAAU,kCAAiC,UAEtE,cAAA;AAAA,UACAA,kCAAA;AAAA,YAAC;AAAA,YAAA;AAAA,cACC,IAAG;AAAA,cACH,OAAO,SAAS;AAAA,cAChB,UAAU,CAAC,MAAM,YAAY,EAAE,GAAG,UAAU,WAAW,EAAE,OAAO,OAAO;AAAA,cACvE,WAAU;AAAA,cACV,UAAQ;AAAA,YAAA;AAAA,UAAA;AAAA,QACV,GACF;AAAA,QACAC,kCAAAA,KAAC,OAAI,EAAA,WAAU,uCACb,UAAA;AAAA,UAAAD,sCAAC,SAAM,EAAA,SAAQ,YAAW,WAAU,kCAAiC,UAErE,aAAA;AAAA,UACAA,kCAAA;AAAA,YAAC;AAAA,YAAA;AAAA,cACC,IAAG;AAAA,cACH,MAAK;AAAA,cACL,OAAO,SAAS;AAAA,cAChB,UAAU,CAAC,MAAM,YAAY,EAAE,GAAG,UAAU,UAAU,OAAO,EAAE,OAAO,KAAK,GAAG;AAAA,cAC9E,KAAI;AAAA,cACJ,WAAU;AAAA,cACV,UAAQ;AAAA,YAAA;AAAA,UAAA;AAAA,QACV,GACF;AAAA,QACAC,kCAAAA,KAAC,OAAI,EAAA,WAAU,uCACb,UAAA;AAAA,UAAAD,sCAAC,SAAM,EAAA,SAAQ,gBAAe,WAAU,kCAAiC,UAEzE,SAAA;AAAA,UACAA,kCAAA;AAAA,YAAC;AAAA,YAAA;AAAA,cACC,IAAG;AAAA,cACH,MAAK;AAAA,cACL,OAAO,SAAS;AAAA,cAChB,UAAU,CAAC,MAAM,YAAY,EAAE,GAAG,UAAU,cAAc,EAAE,OAAO,OAAO;AAAA,cAC1E,WAAU;AAAA,cACV,UAAQ;AAAA,YAAA;AAAA,UAAA;AAAA,QACV,EACF,CAAA;AAAA,MAAA,GACF;AAAA,6CACC,cACC,EAAA,UAAA;AAAA,QAAAA,kCAAAA,IAAC,UAAO,MAAK,UAAS,SAAQ,WAAU,SAAS,SAAS,UAE1D,SAAA,CAAA;AAAA,QACAA,kCAAA;AAAA,UAAC;AAAA,UAAA;AAAA,YACC,MAAK;AAAA,YACL;AAAA,YACA,WAAU;AAAA,YACV,UAAU;AAAA,YAET,oBAAU,gBAAgB;AAAA,UAAA;AAAA,QAAA;AAAA,MAC7B,EACF,CAAA;AAAA,IAAA,EACF,CAAA;AAAA,EAAA,EAAA,CACF,EACF,CAAA;AAEJ;", "x_google_ignoreList": [0]}