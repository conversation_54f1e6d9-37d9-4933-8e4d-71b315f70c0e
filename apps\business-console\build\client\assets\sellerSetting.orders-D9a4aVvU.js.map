{"version": 3, "file": "sellerSetting.orders-D9a4aVvU.js", "sources": ["../../../app/routes/sellerSetting.orders.tsx"], "sourcesContent": ["import type React from \"react\"\r\n\r\nimport { useState, useEffect, useMemo, useCallback } from \"react\"\r\nimport { Badge } from \"~/components/ui/badge\"\r\nimport { But<PERSON> } from \"~/components/ui/button\"\r\nimport { Card, CardContent } from \"~/components/ui/card\"\r\nimport { Input } from \"~/components/ui/input\"\r\nimport { Label } from \"~/components/ui/label\"\r\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from \"~/components/ui/select\"\r\nimport { Clock, AlertCircle, Search, Filter, Phone, Store, Truck, MapPin, User, Eye, Package, AlertTriangle, MessageCircleQuestionIcon, RefreshCw, ArrowRight, CalendarIcon, Timer } from \"lucide-react\"\r\nimport { usePolling } from \"~/hooks/usePolling\"\r\nimport type { rNETOrder, OrderStatus } from \"~/types/api/businessConsoleService/rNETOrder\"\r\nimport { Dialog, DialogContent, DialogHeader, DialogTitle } from \"~/components/ui/dialog\"\r\nimport { Separator } from \"~/components/ui/separator\"\r\nimport { Textarea } from \"@headlessui/react\"\r\nimport { json, ActionFunction } from \"@remix-run/node\"\r\nimport { useFetcher } from \"@remix-run/react\"\r\nimport { withAuth, withResponse } from \"~/utils/auth-utils\"\r\nimport { getrNETOrders, cancelrNETOrder, updateLiveOrderStatus, markDelivered } from \"~/services/rNETOrders\"\r\nimport dayjs from \"dayjs\"\r\nimport { useToast } from \"~/hooks/use-toast\"\r\nimport { DateRange } from \"react-day-picker\"\r\nimport { Popover, PopoverContent, PopoverTrigger, PopoverClose } from \"~/components/ui/popover\"\r\nimport { cn } from \"lib/utils\"\r\nimport { format } from \"date-fns\"\r\nimport { Calendar } from \"~/components/ui/calendar\"\r\nimport { Tabs, TabsContent, TabsList, TabsTrigger } from \"~/components/ui/tabs\"\r\nimport { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from \"~/components/ui/table\"\r\n\r\ntype ActionIntent = \"Fetch Orders\" | \"Cancel Order\" | \"Mark Delivered\" | \"Update LiveOrder Status\";\r\n\r\ninterface OrderStatusCount {\r\n  status: string;\r\n  count: number;\r\n}\r\n\r\ninterface ActionData {\r\n  intent: ActionIntent;\r\n  errorMessage: string;\r\n  success: boolean;\r\n  data: { orders: rNETOrder[], totalElements: number, pageSize: number, currentPage: number, orderStatusCounts: OrderStatusCount[] };\r\n}\r\nexport const action: ActionFunction = withAuth(async ({ request, user }) => {\r\n  const formData = await request.formData();\r\n  const intent = formData.get(\"intent\") as ActionIntent;\r\n  const data = formData.get(\"data\") as any;\r\n\r\n  if (!intent) {\r\n    return json({ success: false, errorMessage: \"Invalid request\", intent: intent }, { status: 400 });\r\n  }\r\n\r\n  if (!user.sellerId) {\r\n    return json({ success: false, errorMessage: \"Seller ID not found\", intent: intent }, { status: 400 });\r\n  }\r\n\r\n  if (intent === \"Fetch Orders\") {\r\n    const { activeTab, searchType, searchTerm, filterDate, filterStatus, pageSize, currentPage } = JSON.parse(data);\r\n\r\n    // Convert dates to IST (UTC+5:30)\r\n    const fromDate = filterDate?.from\r\n      ? dayjs(filterDate.from).startOf('day').add(5, 'hours').add(30, 'minutes').toISOString()\r\n      : \"\";\r\n    const toDate = filterDate?.to\r\n      ? dayjs(filterDate.to).endOf(\"day\").add(5, 'hours').add(30, 'minutes').toISOString()\r\n      : filterDate?.from ? dayjs(filterDate.from).endOf(\"day\").add(5, 'hours').add(30, 'minutes').toISOString() : \"\";\r\n    const searchQuery = searchType === \"OrderID\" ? `&orderGroupId=${searchTerm}` : searchType === \"BuyerID\" ? `&buyerId=${searchTerm}` : searchType === \"BuyerMobile\" ? `&buyerMobile=${searchTerm}` : searchType === \"Name\" ? `&searchTerm=${searchTerm}` : \"\";\r\n    const queryParams = `&sellerId=${user.sellerId}&fromDate=${fromDate}&toDate=${toDate}${searchQuery}${filterStatus ? `&status=${filterStatus}` : \"\"}&pageSize=${pageSize}&currentPage=${currentPage}`;\r\n\r\n    try {\r\n      const response = await getrNETOrders(request, queryParams, \"seller\");\r\n      return withResponse({ success: true, intent: intent, data: response?.data }, response?.headers);\r\n    }\r\n    catch (error) {\r\n      return json({ success: false, intent: intent, errorMessage: \"Failed to fetch orders\" }, { status: 400 })\r\n    }\r\n  }\r\n  if (intent === \"Cancel Order\") {\r\n    try {\r\n      const { order, formData } = JSON.parse(data);\r\n\r\n      const response = await cancelrNETOrder(request, order.tripId, order.orderGroupId);\r\n      return withResponse({ success: true, intent: intent }, response.headers);\r\n    }\r\n    catch (error) {\r\n      return json({ success: false, intent: intent, errorMessage: \"Failed to cancel order\" }, { status: 400 })\r\n    }\r\n  }\r\n  if (intent === \"Update LiveOrder Status\") {\r\n    try {\r\n      const { order, formData } = JSON.parse(data);\r\n      const status = order.orderStatus === \"Created\" ? \"Accepted\" : order.orderStatus === \"Accepted\" ? \"Packed\" : order.orderStatus === \"Packed\" ? \"Assigned\" : order.orderStatus === \"Assigned\" ? \"PickedUp\" : \"Dispatched\";\r\n      const queryParams = status === \"Packed\" ? `${formData.boxes !== undefined ? `&boxes=${formData.boxes}` : \"\"}${formData.bags !== undefined ? `&bags=${formData.bags}` : \"\"}` : \"\";\r\n\r\n      const response = await updateLiveOrderStatus(request, order.orderGroupId, status);\r\n      return withResponse({ success: true, intent: intent }, response.headers);\r\n    }\r\n    catch (error) {\r\n      return json({ success: false, intent: intent, errorMessage: \"Failed to update order status\" }, { status: 400 })\r\n    }\r\n  }\r\n  if (intent === \"Mark Delivered\") {\r\n    try {\r\n      const { order, formData } = JSON.parse(data);\r\n      const queryParams = `${formData.deliveryCode !== undefined ? `&deliveryCode=${formData.deliveryCode}` : \"\"}${formData.creditAmount !== undefined ? `&creditAmount=${formData.creditAmount}` : \"\"}${formData.boxesGiven !== undefined ? `&boxesGiven=${formData.boxesGiven}` : \"\"}${formData.boxesTaken !== undefined ? `&boxesTaken=${formData.boxesTaken}` : \"\"}`;\r\n\r\n      const response = await markDelivered(request, order.tripId, order.orderGroupId, queryParams);\r\n      return withResponse({ success: true, intent: intent }, response.headers);\r\n    }\r\n    catch (error) {\r\n      return json({ success: false, intent: intent, errorMessage: \"Failed to mark order as delivered\" }, { status: 400 })\r\n    }\r\n  }\r\n\r\n  return json({ success: false, intent: intent, errorMessage: \"Invalid intent\" }, { status: 400 });\r\n});\r\n\r\n\r\nexport default function LiveOrderDashboard() {\r\n  const [allOrders, setAllOrders] = useState<rNETOrder[]>()\r\n  const [selectedOrder, setSelectedOrder] = useState<rNETOrder | null>(null)\r\n  const [orderStatusCounts, setOrderStatusCounts] = useState<OrderStatusCount[]>([])\r\n  const [activeTab, setActiveTab] = useState<\"live\" | \"all\">(\"live\")\r\n  const fetcher = useFetcher<ActionData>()\r\n\r\n  // action\r\n  const [actionType, setActionType] = useState<string>(\"\")\r\n  const [actionSelectedOrder, setActionSelectedOrder] = useState<rNETOrder | null>(null)\r\n  const [isSubmitting, setIsSubmitting] = useState(false)\r\n  const { toast } = useToast()\r\n\r\n  // Search and Filters\r\n  const [searchType, setSearchType] = useState<\"OrderID\" | \"BuyerID\" | \"BuyerMobile\" | \"Name\">(\"OrderID\")\r\n  const [searchTerm, setSearchTerm] = useState<string>(\"\")\r\n  const [filterDate, setFilterDate] = useState<DateRange | undefined>({ from: new Date(), to: new Date() });  // date object\r\n  const [filterStatus, setFilterStatus] = useState<OrderStatus | \"\">(\"\")\r\n  const [pageSize, setPageSize] = useState(20)\r\n  const [currentPage, setCurrentPage] = useState(0)\r\n  const [totalElements, setTotalElements] = useState(0)\r\n\r\n  // local state\r\n  const [dateRange, setDateRange] = useState<DateRange | undefined>({ from: new Date(), to: new Date() });  //date object\r\n\r\n  // debounce on search term\r\n  const [debounceSearchTerm, setDebounceSearchTerm] = useState<string>(\"\");\r\n  useEffect(() => {\r\n    const timer = setTimeout(() => {\r\n      setDebounceSearchTerm(searchTerm);\r\n    }, 500);\r\n\r\n    return () => {\r\n      clearTimeout(timer);\r\n    };\r\n  }, [searchTerm]);\r\n\r\n  // Animation state for smooth transitions\r\n  const [animationKey, setAnimationKey] = useState(0)\r\n\r\n  // polling for auto-refresh\r\n  const { isPolling, startPolling, stopPolling } = usePolling(() => refreshOrders(true), 59000);\r\n\r\n  const refreshOrders = useCallback((isAutoRefresh?: boolean) => {\r\n    console.log(\"Refreshing orders...\")\r\n\r\n    const formData = new FormData();\r\n    formData.append(\"intent\", \"Fetch Orders\");\r\n    const data = { activeTab, searchType, searchTerm: debounceSearchTerm, filterDate, filterStatus, pageSize, currentPage }\r\n    formData.append(\"data\", JSON.stringify(data))\r\n    fetcher.submit(formData, { method: \"post\" })\r\n  }, [activeTab, debounceSearchTerm, filterDate, filterStatus, pageSize, currentPage])\r\n\r\n  useEffect(() => {\r\n    refreshOrders()\r\n  }, [activeTab, debounceSearchTerm, filterDate, filterStatus, pageSize, currentPage])\r\n\r\n  useEffect(() => {\r\n    if (activeTab === \"live\") {\r\n      startPolling()\r\n    } else {\r\n      stopPolling()\r\n    }\r\n    return () => stopPolling()\r\n  }, [activeTab, startPolling, stopPolling])\r\n\r\n  // pause and resume polling when the tab is inactive\r\n  useEffect(() => {\r\n    const handleVisibilityChange = () => {\r\n      if (document.visibilityState === \"visible\" && activeTab === \"live\") {\r\n        startPolling()\r\n      } else {\r\n        stopPolling()\r\n      }\r\n    }\r\n    document.addEventListener(\"visibilitychange\", handleVisibilityChange)\r\n    return () => {\r\n      document.removeEventListener(\"visibilitychange\", handleVisibilityChange)\r\n    }\r\n  }, [activeTab, startPolling, stopPolling])\r\n\r\n  const handleTabChange = (newTab: string) => {\r\n    setSearchType(\"OrderID\")\r\n    setSearchTerm(\"\")\r\n    setDebounceSearchTerm(\"\")\r\n    if (newTab === \"live\") {\r\n      setFilterDate({ from: new Date(), to: new Date() })\r\n      setDateRange({ from: new Date(), to: new Date() })\r\n    } else {\r\n      setFilterDate(undefined)\r\n      setDateRange(undefined)\r\n    }\r\n    setFilterStatus(\"\")\r\n    setPageSize(20)\r\n    setCurrentPage(0)\r\n    setAllOrders([])\r\n    setOrderStatusCounts([])\r\n    setActiveTab(newTab as \"live\" | \"all\")\r\n  }\r\n\r\n  const searchTypeFilters = [\r\n    { label: \"Order ID\", value: \"OrderID\" },\r\n    { label: \"Buyer ID\", value: \"BuyerID\" },\r\n    { label: \"Buyer Mobile\", value: \"BuyerMobile\" },\r\n    { label: \"Name\", value: \"Name\" },\r\n  ]\r\n\r\n  const statusFilters = [\r\n    { label: \"Created\", value: \"Created\" },\r\n    { label: \"Accepted\", value: \"Accepted\" },\r\n    { label: \"Packed\", value: \"Packed\" },\r\n    { label: \"Assigned\", value: \"Assigned\" },\r\n    { label: \"Picked Up\", value: \"PickedUp\" },\r\n    { label: \"Dispatched\", value: \"Dispatched\" },\r\n    { label: \"Delivered\", value: \"Delivered\" },\r\n    { label: \"Cancelled\", value: \"Cancelled\" },\r\n  ]\r\n\r\n  // Separate live and completed orders\r\n  const { liveOrders, completedOrders } = useMemo(() => {\r\n    const live = allOrders?.filter((order) => ![\"Delivered\", \"Cancelled\"].includes(order.orderStatus))\r\n      .sort((a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime())\r\n\r\n    const completed = allOrders?.filter((order) => [\"Delivered\", \"Cancelled\"].includes(order.orderStatus))\r\n\r\n    return { liveOrders: live, completedOrders: completed }\r\n  }, [allOrders])\r\n\r\n  // Statistics from API response\r\n  const stats = {\r\n    total: totalElements,\r\n    created: orderStatusCounts.find((s) => s.status === \"Created\")?.count || 0,\r\n    accepted: orderStatusCounts.find((s) => s.status === \"Accepted\")?.count || 0,\r\n    packed: orderStatusCounts.find((s) => s.status === \"Packed\")?.count || 0,\r\n    assigned: orderStatusCounts.find((s) => s.status === \"Assigned\")?.count || 0,\r\n    dispatched: orderStatusCounts.find((s) => s.status === \"Dispatched\")?.count || 0,\r\n    delivered: orderStatusCounts.find((s) => s.status === \"Delivered\")?.count || 0,\r\n    cancelled: orderStatusCounts.find((s) => s.status === \"Cancelled\")?.count || 0,\r\n  }\r\n\r\n  const handleAction = (order: rNETOrder, action: string) => {\r\n    setActionSelectedOrder(order)\r\n    setActionType(action)\r\n  }\r\n\r\n  const handleSubmitAction = (formData: any) => {\r\n    const actionData = new FormData();\r\n    actionData.append(\"intent\", actionType);\r\n    actionData.append(\"data\", JSON.stringify({ order: actionSelectedOrder, formData }))\r\n    fetcher.submit(actionData, { method: \"post\" })\r\n    setIsSubmitting(true)\r\n  }\r\n\r\n  useEffect(() => {\r\n    if (fetcher.data?.intent === \"Fetch Orders\") {\r\n      if (fetcher.data?.success) {\r\n        fetcher.data?.data?.orders ? setAllOrders(fetcher.data.data.orders) : setAllOrders([])\r\n        fetcher.data?.data?.totalElements ? setTotalElements(fetcher.data.data.totalElements) : setTotalElements(0)\r\n        fetcher.data?.data?.orderStatusCounts ? setOrderStatusCounts(fetcher.data.data.orderStatusCounts) : setOrderStatusCounts([])\r\n      } else if (fetcher.data?.success === false) {\r\n        console.log(fetcher.data?.errorMessage)\r\n        toast({\r\n          title: \"Error\",\r\n          description: fetcher.data?.errorMessage,\r\n          variant: \"destructive\"\r\n        })\r\n      }\r\n    }\r\n    if (fetcher.data?.intent === \"Cancel Order\") {\r\n      if (fetcher.data?.success) {\r\n        toast({\r\n          title: \"Order Cancelled\",\r\n          description: \"Order cancelled successfully\",\r\n          style: {\r\n            backgroundColor: \"#00A33F\",\r\n            color: \"#ffffff\"\r\n          }\r\n        })\r\n        setActionSelectedOrder(null)\r\n        setActionType(\"\")\r\n        setIsSubmitting(false)\r\n        refreshOrders(false)\r\n      } else if (fetcher.data?.success === false) {\r\n        console.log(fetcher.data?.errorMessage)\r\n        setIsSubmitting(false)\r\n        toast({\r\n          title: \"Error\",\r\n          description: fetcher.data?.errorMessage,\r\n          variant: \"destructive\"\r\n        })\r\n      }\r\n    }\r\n    if (fetcher.data?.intent === \"Update LiveOrder Status\") {\r\n      if (fetcher.data?.success) {\r\n        toast({\r\n          title: \"Order Status Updated\",\r\n          description: \"Order status updated successfully\",\r\n          style: {\r\n            backgroundColor: \"#00A33F\",\r\n            color: \"#ffffff\"\r\n          }\r\n        })\r\n        setActionSelectedOrder(null)\r\n        setActionType(\"\")\r\n        setIsSubmitting(false)\r\n        refreshOrders(false)\r\n      } else if (fetcher.data?.success === false) {\r\n        console.log(fetcher.data?.errorMessage)\r\n        setIsSubmitting(false)\r\n        toast({\r\n          title: \"Error\",\r\n          description: fetcher.data?.errorMessage,\r\n          variant: \"destructive\"\r\n        })\r\n      }\r\n    }\r\n    if (fetcher.data?.intent === \"Mark Delivered\") {\r\n      if (fetcher.data?.success) {\r\n        toast({\r\n          title: \"Order Marked as Delivered\",\r\n          description: \"Order marked as delivered successfully\",\r\n          style: {\r\n            backgroundColor: \"#00A33F\",\r\n            color: \"#ffffff\"\r\n          }\r\n        })\r\n        setActionSelectedOrder(null)\r\n        setActionType(\"\")\r\n        setIsSubmitting(false)\r\n        refreshOrders(false)\r\n      } else if (fetcher.data?.success === false) {\r\n        console.log(fetcher.data?.errorMessage)\r\n        setIsSubmitting(false)\r\n        toast({\r\n          title: \"Error\",\r\n          description: fetcher.data?.errorMessage,\r\n          variant: \"destructive\"\r\n        })\r\n      }\r\n    }\r\n  }, [fetcher.data])\r\n\r\n  return (\r\n    <div className=\"min-h-screen p-6\">\r\n      <div className=\"mx-auto mb-6\">\r\n\r\n        {/* Header */}\r\n        <div className=\"mb-4\">\r\n          <div className=\"flex flex-col gap-2 sm:flex-row sm:items-center sm:justify-between\">\r\n            <div>\r\n              <h1 className=\"text-xl md:text-3xl font-bold text-gray-900\">Orders</h1>\r\n              <p className=\"mt-2 text-gray-600\">Manage and track all your restaurant orders</p>\r\n            </div>\r\n            <div className=\"flex flex-row items-center gap-2\">\r\n              <Button\r\n                variant=\"outline\"\r\n                size=\"sm\"\r\n                onClick={() => refreshOrders(false)}\r\n                disabled={(fetcher.state === \"loading\" || fetcher.state === \"submitting\") && fetcher.data?.intent === \"Fetch Orders\"}\r\n                className=\"w-fit sm:ml-auto flex items-center gap-2\"\r\n              >\r\n                <RefreshCw className={`w-4 h-4 ${(fetcher.state === \"loading\" || fetcher.state === \"submitting\") && fetcher.data?.intent === \"Fetch Orders\" ? \"animate-spin\" : \"\"}`} />\r\n                {(fetcher.state === \"loading\" || fetcher.state === \"submitting\") && fetcher.data?.intent === \"Fetch Orders\" ? \"Refreshing...\" : \"Refresh\"}\r\n              </Button>\r\n              <div className=\"flex items-center gap-2 text-sm text-gray-600\">\r\n                <div className={`w-2 h-2 rounded-full ${isPolling ? \"bg-green-500 animate-pulse\" : \"bg-gray-400\"}`} />\r\n                Auto-refresh {isPolling ? \"ON\" : \"OFF\"}\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Orders Type */}\r\n        <Tabs value={activeTab} onValueChange={handleTabChange}>\r\n          <TabsList className=\"w-full h-10 mb-2\">\r\n            <TabsTrigger value=\"live\" className=\"w-1/2 h-8 py-1\">⏱️ Live Orders</TabsTrigger>\r\n            <TabsTrigger value=\"all\" className=\"w-1/2 h-8 py-1\">📦 All Orders</TabsTrigger>\r\n          </TabsList>\r\n\r\n          {/* Search and Filters */}\r\n          <Card className=\"mb-1 bg-gray-100\">\r\n            <CardContent className=\"p-2\">\r\n              <div className=\"space-y-1\">\r\n                {/* Search */}\r\n                <div className=\"grid sm:grid-cols-2 lg:grid-cols-4 gap-2\">\r\n                  <div className=\"relative lg:col-span-1\">\r\n                    <Select value={searchType} onValueChange={(value: typeof searchType) => setSearchType(value)}>\r\n                      <SelectTrigger>\r\n                        <SelectValue placeholder=\"Search by\" />\r\n                      </SelectTrigger>\r\n                      <SelectContent>\r\n                        {searchTypeFilters.map((filter) => (\r\n                          <SelectItem key={filter.value} value={filter.value}>\r\n                            {filter.label}\r\n                          </SelectItem>\r\n                        ))}\r\n                      </SelectContent>\r\n                    </Select>\r\n                  </div>\r\n\r\n                  <div className=\"relative lg:col-span-2\">\r\n                    <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4\" />\r\n                    <Input\r\n                      placeholder={searchType === \"Name\" ? \"Search name\" : (searchType === \"BuyerMobile\") ? \"Search by Mobile\" : \"Search by ID\"}\r\n                      value={searchTerm}\r\n                      onChange={(e) => setSearchTerm(e.target.value)}\r\n                      className=\"pl-10\"\r\n                    />\r\n                  </div>\r\n\r\n                  <div className=\"relative sm:col-span-2 lg:col-span-1 \">\r\n                    <Popover>\r\n                      <PopoverTrigger asChild>\r\n                        <Button\r\n                          id=\"filterDate\"\r\n                          variant=\"outline\"\r\n                          className={cn(\r\n                            \"w-full justify-start text-left font-normal\",\r\n                            !filterDate && \"text-muted-foreground\"\r\n                          )}\r\n                        >\r\n                          <CalendarIcon />\r\n                          {filterDate?.from ? (\r\n                            filterDate.to ? (\r\n                              <>\r\n                                {format(filterDate.from, \"LLL dd, y\")} - {format(filterDate.to, \"LLL dd, y\")}\r\n                              </>\r\n                            ) : (\r\n                              format(filterDate.from, \"LLL dd, y\")\r\n                            )\r\n                          ) : (\r\n                            <span>Pick a date</span>\r\n                          )}\r\n                        </Button>\r\n                      </PopoverTrigger>\r\n                      <PopoverContent className=\"w-auto p-0\" align=\"start\">\r\n                        <Calendar\r\n                          initialFocus\r\n                          selected={dateRange}\r\n                          mode=\"range\"\r\n                          onSelect={(range: DateRange | undefined) => {\r\n                            if (!range?.from) return;\r\n                            setDateRange({\r\n                              from: range.from,\r\n                              to: range.to || undefined,\r\n                            });\r\n                          }}\r\n                        />\r\n                        <PopoverClose className=\"w-full\">\r\n                          <Button\r\n                            variant=\"ghost\"\r\n                            className=\"w-full text-blue-500 hover:text-blue-500 justify-center\"\r\n                            onClick={() => setFilterDate(dateRange)}\r\n                          >\r\n                            Set\r\n                          </Button>\r\n                        </PopoverClose>\r\n                      </PopoverContent>\r\n                    </Popover>\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Filters */}\r\n                <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-2\">\r\n                  <div className=\"space-y-1\">\r\n                    <Label className=\"text-sm font-medium\">Status</Label>\r\n                    <Select value={filterStatus} onValueChange={(value: OrderStatus) => setFilterStatus(value)}>\r\n                      <SelectTrigger>\r\n                        <SelectValue placeholder=\"Select status\" />\r\n                      </SelectTrigger>\r\n                      <SelectContent>\r\n                        {statusFilters.map((filter) => (\r\n                          <SelectItem key={filter.value} value={filter.value}>\r\n                            {filter.label}\r\n                          </SelectItem>\r\n                        ))}\r\n                      </SelectContent>\r\n                    </Select>\r\n                  </div>\r\n\r\n                  <div className=\"space-y-1 lg:col-start-4 flex items-end\">\r\n                    <Button\r\n                      onClick={() => {\r\n                        setSearchType(\"OrderID\")\r\n                        setSearchTerm(\"\")\r\n                        setDebounceSearchTerm(\"\")\r\n                        if (activeTab === \"live\") {\r\n                          setFilterDate({ from: new Date(), to: new Date() })\r\n                          setDateRange({ from: new Date(), to: new Date() })\r\n                        } else {\r\n                          setFilterDate(undefined)\r\n                          setDateRange(undefined)\r\n                        }\r\n                        setFilterStatus(\"\")\r\n                        setPageSize(20)\r\n                        setCurrentPage(0)\r\n                      }}\r\n                      className=\"w-full\"\r\n                    >\r\n                      <Filter className=\"w-4 h-4 mr-2\" />\r\n                      Clear Filters\r\n                    </Button>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </CardContent>\r\n          </Card>\r\n\r\n          {/* Pagination */}\r\n          <Card className=\"p-0 mb-1\">\r\n            <CardContent className=\"px-1.5 py-1\">\r\n              <div className=\"flex flex-row items-center justify-end gap-1.5\">\r\n                <Select value={pageSize.toString()} onValueChange={(value) => setPageSize(Number(value))}>\r\n                  <SelectTrigger className=\"w-[140px] h-[36px]\">\r\n                    <SelectValue />\r\n                  </SelectTrigger>\r\n                  <SelectContent>\r\n                    <SelectItem value=\"20\">20 per Page</SelectItem>\r\n                    <SelectItem value=\"50\">50 per Page</SelectItem>\r\n                    <SelectItem value=\"100\">100 per Page</SelectItem>\r\n                  </SelectContent>\r\n                </Select>\r\n                <Select value={currentPage.toString()} onValueChange={(value) => setCurrentPage(Number(value))}>\r\n                  <SelectTrigger className=\"w-[140px] h-[36px]\">\r\n                    <SelectValue />\r\n                  </SelectTrigger>\r\n                  <SelectContent>\r\n                    {Array.from({ length: Math.ceil(totalElements / pageSize) }, (_, i) => (\r\n                      <SelectItem key={i} value={i.toString()}>\r\n                        Page {i + 1}\r\n                      </SelectItem>\r\n                    ))}\r\n                  </SelectContent>\r\n                </Select>\r\n              </div>\r\n            </CardContent>\r\n          </Card>\r\n\r\n          {/* Statistics Cards */}\r\n          {activeTab === \"live\" && <div className=\"grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-8 gap-1 sm:gap-2 mb-4\">\r\n            <Card className=\"shadow-sm\">\r\n              <CardContent className=\"p-1 sm:p-2 text-center\">\r\n                <div className=\"text-xs sm:text-sm text-gray-800\">Total</div>\r\n                <div className=\"text-lg leading-6 font-semibold text-gray-900\">{stats.total}</div>\r\n              </CardContent>\r\n            </Card>\r\n            <Card className=\"shadow-sm\">\r\n              <CardContent className=\"p-1 sm:p-2 text-center\">\r\n                <div className=\"text-xs sm:text-sm text-gray-800\">Created</div>\r\n                <div className=\"text-lg leading-6 font-semibold text-purple-600\">{stats.created}</div>\r\n              </CardContent>\r\n            </Card>\r\n            <Card className=\"shadow-sm\">\r\n              <CardContent className=\"p-1 sm:p-2 text-center\">\r\n                <div className=\"text-xs sm:text-sm text-gray-800\">Accepted</div>\r\n                <div className=\"text-lg leading-6 font-semibold text-purple-600\">{stats.accepted}</div>\r\n              </CardContent>\r\n            </Card>\r\n            <Card className=\"shadow-sm\">\r\n              <CardContent className=\"p-1 sm:p-2 text-center\">\r\n                <div className=\"text-xs sm:text-sm text-gray-800\">Packed</div>\r\n                <div className=\"text-lg leading-6 font-semibold text-orange-600\">{stats.packed}</div>\r\n              </CardContent>\r\n            </Card>\r\n            <Card className=\"shadow-sm\">\r\n              <CardContent className=\"p-1 sm:p-2 text-center\">\r\n                <div className=\"text-xs sm:text-sm text-gray-800\">Assigned</div>\r\n                <div className=\"text-lg leading-6 font-semibold text-orange-600\">{stats.assigned}</div>\r\n              </CardContent>\r\n            </Card>\r\n            <Card className=\"shadow-sm\">\r\n              <CardContent className=\"p-1 sm:p-2 text-center\">\r\n                <div className=\"text-xs sm:text-sm text-gray-800\">Dispatched</div>\r\n                <div className=\"text-lg leading-6 font-semibold text-yellow-600\">{stats.dispatched}</div>\r\n              </CardContent>\r\n            </Card>\r\n            <Card className=\"shadow-sm\">\r\n              <CardContent className=\"p-1 sm:p-2 text-center\">\r\n                <div className=\"text-xs sm:text-sm text-gray-800\">Delivered</div>\r\n                <div className=\"text-lg leading-6 font-semibold text-green-600\">{stats.delivered}</div>\r\n              </CardContent>\r\n            </Card>\r\n            <Card className=\"shadow-sm\">\r\n              <CardContent className=\"p-1 sm:p-2 text-center\">\r\n                <div className=\"text-xs sm:text-sm text-gray-800\">Cancelled</div>\r\n                <div className=\"text-lg leading-6 font-semibold text-red-600\">{stats.cancelled}</div>\r\n              </CardContent>\r\n            </Card>\r\n          </div>}\r\n\r\n          <TabsContent value=\"live\">\r\n            {/* Live Orders Section */}\r\n            <div className=\"mb-8\">\r\n              <div className=\"flex items-center justify-between mb-4\">\r\n                <h2 className=\"text-xl sm:text-2xl font-bold flex items-center gap-2\">\r\n                  <div className=\"w-3 h-3 bg-green-500 rounded-full animate-pulse\" />\r\n                  Live Orders\r\n                  <Badge variant=\"secondary\" className=\"ml-2\">\r\n                    {totalElements - (stats.delivered + stats.cancelled) || 0}\r\n                  </Badge>\r\n                </h2>\r\n              </div>\r\n\r\n              {liveOrders?.length === 0 ? (\r\n                <Card>\r\n                  <CardContent className=\"p-8 text-center\">\r\n                    <div className=\"text-gray-500\">\r\n                      <Clock className=\"w-12 h-12 mx-auto mb-4 opacity-50\" />\r\n                      <p className=\"text-lg font-medium\">No live orders</p>\r\n                      <p className=\"text-sm\">No active orders for the selected date and filters.</p>\r\n                    </div>\r\n                  </CardContent>\r\n                </Card>\r\n              ) : (\r\n                <div\r\n                  key={`live-orders-${animationKey}`}\r\n                  className=\"grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-4 transition-all duration-300 ease-out\"\r\n                  style={{\r\n                    transform: 'translateY(0)',\r\n                    transition: 'transform 0.3s ease-out'\r\n                  }}\r\n                >\r\n                  {liveOrders?.map((order, index) => (\r\n                    <div\r\n                      key={`${order.orderGroupId}-${animationKey}`}\r\n                      className=\"animate-in fade-in slide-in-from-bottom-4 duration-500 ease-out transform transition-all hover:scale-[1.01]\"\r\n                      style={{\r\n                        animationDelay: `${index * 50}ms`,\r\n                        animationFillMode: 'both',\r\n                        willChange: 'transform'\r\n                      }}\r\n                    >\r\n                      <OrderCard\r\n                        order={order}\r\n                        onViewDetails={setSelectedOrder}\r\n                        onAction={handleAction}\r\n                      />\r\n                    </div>\r\n                  ))}\r\n                </div>\r\n              )}\r\n            </div>\r\n\r\n            {/* Completed Orders Section */}\r\n            <div className=\"mb-8\">\r\n              <div className=\"flex items-center justify-between mb-4\">\r\n                <h2 className=\"text-xl sm:text-2xl font-bold flex items-center gap-2\">\r\n                  <div className=\"w-3 h-3 bg-gray-500 rounded-full\" />\r\n                  Completed Orders\r\n                  <Badge variant=\"secondary\" className=\"ml-2\">\r\n                    {stats.delivered + stats.cancelled || 0}\r\n                  </Badge>\r\n                </h2>\r\n              </div>\r\n\r\n              {completedOrders?.length === 0 ? (\r\n                <Card>\r\n                  <CardContent className=\"p-8 text-center\">\r\n                    <div className=\"text-gray-500\">\r\n                      <AlertCircle className=\"w-12 h-12 mx-auto mb-4 opacity-50\" />\r\n                      <p className=\"text-lg font-medium\">No completed orders</p>\r\n                      <p className=\"text-sm\">No completed orders for the selected date and filters.</p>\r\n                    </div>\r\n                  </CardContent>\r\n                </Card>\r\n              ) : (\r\n                <div className=\"overflow-x-auto\">\r\n                  <div\r\n                    key={`completed-orders-${animationKey}`}\r\n                    className=\"flex gap-4 pb-4 min-w-max transition-all duration-300 ease-out\"\r\n                    style={{\r\n                      transform: 'translateX(0)',\r\n                      transition: 'transform 0.3s ease-out'\r\n                    }}\r\n                  >\r\n                    {completedOrders?.map((order, index) => (\r\n                      <div\r\n                        key={`${order.orderGroupId}-${animationKey}`}\r\n                        className=\"flex-shrink-0 w-72 sm:w-80 animate-in fade-in slide-in-from-right-4 duration-500 ease-out transform transition-all hover:scale-[1.01]\"\r\n                        style={{\r\n                          animationDelay: `${index * 50}ms`,\r\n                          animationFillMode: 'both',\r\n                          willChange: 'transform'\r\n                        }}\r\n                      >\r\n                        <OrderCard order={order} onViewDetails={setSelectedOrder} onAction={handleAction} />\r\n                      </div>\r\n                    ))}\r\n                  </div>\r\n                </div>\r\n              )}\r\n            </div>\r\n          </TabsContent>\r\n\r\n          <TabsContent value=\"all\">\r\n            <Table>\r\n              <TableHeader>\r\n                <TableRow>\r\n                  <TableHead className=\"h-10 px-2 py-1\">Order ID</TableHead>\r\n                  <TableHead className=\"h-10 px-2 py-1\">Customer</TableHead>\r\n                  <TableHead className=\"h-10 px-2 py-1\">Amount</TableHead>\r\n                  <TableHead className=\"h-10 px-2 py-1\">Payment Status</TableHead>\r\n                  <TableHead className=\"h-10 px-2 py-1\">Order Status</TableHead>\r\n                  <TableHead className=\"h-10 px-2 py-1\">Actions</TableHead>\r\n                </TableRow>\r\n              </TableHeader>\r\n              <TableBody>\r\n                {allOrders?.length === 0 ? (\r\n                  <TableRow>\r\n                    <TableCell colSpan={6} className=\"h-24 text-center\">\r\n                      No results.\r\n                    </TableCell>\r\n                  </TableRow>\r\n                ) : (\r\n                  allOrders?.map((order) => (\r\n                    <TableRow key={order.orderGroupId}>\r\n                      <TableCell className=\"px-2 py-2\">\r\n                        <p>{order.orderGroupId}</p>\r\n                        <p className=\"text-xs text-gray-600\">{dayjs(order.createdAt).format(\"DD/MM/YYYY, h:mm A\")}</p>\r\n                      </TableCell>\r\n                      <TableCell className=\"px-2 py-2 break-all\">\r\n                        <p>{order.buyerName}</p>\r\n                        <p className=\"text-xs text-gray-600\">{order.bAreaName}</p>\r\n                      </TableCell>\r\n                      <TableCell className=\"whitespace-nowrap px-2 py-2\">₹ {order.totalOrderGroupAmount.toLocaleString()}</TableCell>\r\n                      <TableCell className=\"px-2 py-2\">{order.paymentCompleted ? \"Paid\" : \"Pending\"}</TableCell>\r\n                      <TableCell className=\"px-2 py-2\">\r\n                        <Badge\r\n                          variant={\r\n                            order.orderStatus === \"Delivered\"\r\n                              ? \"default\"\r\n                              : order.orderStatus === \"Cancelled\"\r\n                                ? \"destructive\"\r\n                                : \"secondary\"\r\n                          }\r\n                        >\r\n                          {order.orderStatus}\r\n                        </Badge>\r\n                      </TableCell>\r\n                      <TableCell className=\"px-2 py-2\">\r\n                        <Button variant=\"outline\" size=\"sm\" onClick={() => setSelectedOrder(order)}>\r\n                          View\r\n                        </Button>\r\n                      </TableCell>\r\n                    </TableRow>\r\n                  ))\r\n                )}\r\n              </TableBody>\r\n            </Table>\r\n          </TabsContent>\r\n        </Tabs>\r\n\r\n        {/* Order Details Modal */}\r\n        <OrderDetailsModal order={selectedOrder} onClose={() => setSelectedOrder(null)} onAction={handleAction} />\r\n\r\n        {/* Action Modal */}\r\n        <ActionModal\r\n          order={actionSelectedOrder}\r\n          actionType={actionType}\r\n          onClose={() => {\r\n            setActionSelectedOrder(null)\r\n            setActionType(\"\")\r\n          }}\r\n          isSubmitting={isSubmitting}\r\n          onSubmit={handleSubmitAction}\r\n        />\r\n      </div>\r\n    </div >\r\n  )\r\n}\r\n\r\n\r\n// Order Card\r\ninterface OrderCardProps {\r\n  order: rNETOrder\r\n  onViewDetails: (order: rNETOrder) => void\r\n  onAction: (order: rNETOrder, action: string) => void\r\n}\r\n\r\nexport function OrderCard({ order, onViewDetails, onAction }: OrderCardProps) {\r\n  const [currentTime, setCurrentTime] = useState(Date.now())\r\n\r\n  // Update timer every second for live orders\r\n  useEffect(() => {\r\n    if (![\"Delivered\", \"Cancelled\"].includes(order.orderStatus)) {\r\n      const interval = setInterval(() => {\r\n        setCurrentTime(Date.now())\r\n      }, 1000)\r\n      return () => clearInterval(interval)\r\n    }\r\n  }, [order.orderStatus])\r\n\r\n  const styling = getOrderCardStyling(order)\r\n  const timeDiff = getTimeDifferenceInSeconds(order.createdAt)\r\n  const isNewOrder = timeDiff <= 60\r\n\r\n  //play sound if it is new order\r\n  useEffect(() => {\r\n    if (isNewOrder) {\r\n      const audio = new Audio(\"/new-order.mp3\")\r\n      audio.play().catch(console.warn)\r\n      return () => {\r\n        audio.pause()\r\n        audio.src = \"\"\r\n      }\r\n    }\r\n  }, [isNewOrder])\r\n\r\n  const handlePhoneClick = (phoneNumber: string, e: React.MouseEvent) => {\r\n    e.stopPropagation()\r\n    window.open(`tel:${phoneNumber}`, \"_self\")\r\n  }\r\n\r\n  const getActionButtons = () => {\r\n    const buttons = []\r\n\r\n    if (order.orderStatus === \"Created\") {\r\n      buttons.push(\r\n        <Button\r\n          key=\"cancel\"\r\n          variant=\"destructive\"\r\n          size=\"sm\"\r\n          onClick={(e) => {\r\n            e.stopPropagation()\r\n            onAction(order, \"Cancel Order\")\r\n          }}\r\n          className=\"text-xs\"\r\n        >\r\n          Cancel Order\r\n        </Button>,\r\n      )\r\n    }\r\n\r\n    if (showUpdateStatusButton(order)) {\r\n      buttons.push(\r\n        <Button\r\n          key=\"updateStatus\"\r\n          variant=\"default\"\r\n          size=\"sm\"\r\n          onClick={(e) => {\r\n            e.stopPropagation()\r\n            onAction(order, \"Update LiveOrder Status\")\r\n          }}\r\n          className=\"text-xs\"\r\n        >\r\n          Update to {order.orderStatus === \"Created\" ? \"Accepted\" : order.orderStatus === \"Accepted\" ? \"Packed\" : order.orderStatus === \"Packed\" ? \"Assigned\" : order.orderStatus === \"Assigned\" ? \"PickedUp\" : \"Dispatched\"} <ArrowRight className=\"w-3 h-3\" />\r\n        </Button>,\r\n      )\r\n    }\r\n\r\n    if (order.orderStatus === \"Dispatched\") {\r\n      buttons.push(\r\n        <Button\r\n          key=\"markDelivered\"\r\n          variant=\"default\"\r\n          size=\"sm\"\r\n          onClick={(e) => {\r\n            e.stopPropagation()\r\n            onAction(order, \"Mark Delivered\")\r\n          }}\r\n          className=\"text-xs bg-blue-500 hover:bg-blue-500\"\r\n        >\r\n          Mark Delivered\r\n        </Button>,\r\n      )\r\n    }\r\n\r\n    return buttons\r\n  }\r\n\r\n  return (\r\n    <Card\r\n      className={`${styling.className} will-change-transform cursor-pointer transition-all duration-500 h-full hover:shadow-lg`}\r\n      onClick={() => onViewDetails(order)}\r\n      onMouseEnter={(e) => {\r\n        e.currentTarget.style.animationPlayState = \"paused\"\r\n      }}\r\n      onMouseLeave={(e) => {\r\n        e.currentTarget.style.animationPlayState = \"running\"\r\n      }}\r\n    >\r\n      <CardContent className=\"p-3 sm:p-4\">\r\n        {/* Header with Order ID and Time */}\r\n        <div className=\"flex flex-col sm:flex-row sm:justify-between sm:items-start gap-2 mb-3\">\r\n          <div className=\"flex flex-wrap items-center gap-2\">\r\n            <div className=\"font-semibold text-base sm:text-lg\">#{order.orderGroupId}</div>\r\n            {isNewOrder && (\r\n              <Badge variant=\"secondary\" className=\"bg-gradient-to-r from-emerald-100 to-green-100 text-emerald-800 text-xs border border-emerald-200 shadow-sm font-semibold animate-bounce\">\r\n                ✨ NEW\r\n              </Badge>\r\n            )}\r\n            {order.supportTickets && order.supportTickets.length > 0 && (\r\n              <Badge variant=\"destructive\" className=\"flex items-center gap-1 text-xs\">\r\n                <MessageCircleQuestionIcon className=\"w-3 h-3\" />\r\n                {order.supportTickets.length}\r\n              </Badge>\r\n            )}\r\n          </div>\r\n          <div className=\"text-xs sm:text-sm text-gray-500 flex items-center gap-1\">\r\n            <Clock className=\"w-3 h-3 sm:w-4 sm:h-4\" />\r\n            {[\"Cancelled\"].includes(order.orderStatus)\r\n              ? formatTimeElapsed(getTimeDifferenceInSeconds(order.createdAt, order.deliveredTime || order.updatedAt))\r\n              : [\"Delivered\"].includes(order.orderStatus) ? formatTimeElapsed(getTimeDifferenceInSeconds(order.createdAt, order.deliveredTime || order.updatedAt)) : formatTimeElapsed(timeDiff)}\r\n          </div>\r\n        </div>\r\n\r\n        {/* Customer Info */}\r\n        <div className=\"mb-3\">\r\n          <div className=\"flex items-center gap-2 mb-1\">\r\n            <Timer className=\"w-3 h-3 sm:w-4 sm:h-4 text-gray-500 flex-shrink-0\" />\r\n            <span className=\"text-xs sm:text-sm text-gray-500\">\r\n              {dayjs(order.createdAt).format(\"DD MMM YY - h:mm A\")}\r\n            </span>\r\n          </div>\r\n          <div className=\"flex items-center gap-2 mb-1\">\r\n            <User className=\"w-3 h-3 sm:w-4 sm:h-4 text-gray-500 flex-shrink-0\" />\r\n            <span className=\"font-medium text-sm sm:text-base truncate\">{order.buyerName}</span>\r\n            <button\r\n              onClick={(e) => handlePhoneClick(order.buyerMobile, e)}\r\n              className=\"text-blue-600 hover:text-blue-800 flex-shrink-0 p-1 rounded hover:bg-blue-50\"\r\n            >\r\n              <Phone className=\"w-3 h-3 sm:w-4 sm:h-4\" />\r\n            </button>\r\n          </div>\r\n          <div className=\"text-xs sm:text-sm text-gray-600 flex items-start gap-2\">\r\n            <MapPin className=\"w-3 h-3 sm:w-4 sm:h-4 text-gray-500 mt-0.5 flex-shrink-0\" />\r\n            <span className=\"line-clamp-2\">{order.bAddress}</span>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Restaurant Info */}\r\n        <div className=\"mb-3\">\r\n          <div className=\"flex items-center gap-2 flex-wrap\">\r\n            <Store className=\"w-3 h-3 sm:w-4 sm:h-4 text-gray-500 flex-shrink-0\" />\r\n            <span className=\"font-medium text-sm sm:text-base truncate\">{order.sellerName}</span>\r\n            <button\r\n              onClick={(e) => handlePhoneClick(order.sellerMobile, e)}\r\n              className=\"text-blue-600 hover:text-blue-800 flex-shrink-0 p-1 rounded hover:bg-blue-50\"\r\n            >\r\n              <Phone className=\"w-3 h-3 sm:w-4 sm:h-4\" />\r\n            </button>\r\n            <Badge variant=\"outline\" className=\"text-xs\">\r\n              {order.logisticProvider}\r\n            </Badge>\r\n            {(order.pos !== \"none\" && order.pos !== undefined) && (\r\n              <Badge variant=\"outline\" className=\"text-xs\">\r\n                {order.pos}\r\n              </Badge>\r\n            )}\r\n          </div>\r\n        </div>\r\n\r\n        {/* Delivery Info */}\r\n        {order.logisticProvider === \"MP2\" && order.logisticDetails && (\r\n          <div className=\"mb-3\">\r\n            <div className=\"flex items-center gap-2 flex-wrap\">\r\n              <Truck className=\"w-3 h-3 sm:w-4 sm:h-4 text-gray-500 flex-shrink-0\" />\r\n              <span className=\"text-xs sm:text-sm truncate\">{order.logisticDetails.riderName}</span>\r\n              {order.logisticDetails.riderPhone && (\r\n                <button\r\n                  onClick={(e) => handlePhoneClick(order.logisticDetails?.riderPhone.toString() || \"\", e)}\r\n                  className=\"text-blue-600 hover:text-blue-800 flex-shrink-0 p-1 rounded hover:bg-blue-50\"\r\n                >\r\n                  <Phone className=\"w-3 h-3 sm:w-4 sm:h-4\" />\r\n                </button>\r\n              )}\r\n              {order.logStatus && (\r\n                <Badge variant=\"outline\" className=\"text-xs\">\r\n                  {getLogisticStatusDisplayName(order.logStatus)}\r\n                </Badge>\r\n              )}\r\n            </div>\r\n          </div>\r\n        )}\r\n\r\n        {/* Order Summary */}\r\n        <div className=\"mb-3\">\r\n          <div className=\"text-sm sm:text-base font-medium text-gray-900 mb-1\">\r\n            {order.totalItems} item(s) • ₹{order.totalOrderGroupAmount}\r\n          </div>\r\n          {order.orderDetails && (\r\n            <div className=\"text-xs text-gray-500 line-clamp-2\">\r\n              {order.orderDetails.slice(0, 3).map((item, idx) => (\r\n                <span key={item.orderId}>\r\n                  {item.itemName} x{item.qty}\r\n                  {idx < Math.min(order.orderDetails!.length, 3) - 1 ? \", \" : \"\"}\r\n                </span>\r\n              ))}\r\n              {order.orderDetails.length > 3 && \"...\"}\r\n            </div>\r\n          )}\r\n        </div>\r\n\r\n        {/* Status and Actions */}\r\n        <div className=\"flex flex-col gap-2\">\r\n          <div className=\"flex justify-between items-center\">\r\n            <Badge\r\n              variant={\r\n                order.orderStatus === \"Delivered\"\r\n                  ? \"default\"\r\n                  : order.orderStatus === \"Cancelled\"\r\n                    ? \"destructive\"\r\n                    : \"secondary\"\r\n              }\r\n              className=\"capitalize text-xs\"\r\n            >\r\n              {getStatusDisplayName(order.orderStatus)}\r\n            </Badge>\r\n            <Button\r\n              variant=\"outline\"\r\n              size=\"sm\"\r\n              onClick={(e) => {\r\n                e.stopPropagation()\r\n                onViewDetails(order)\r\n              }}\r\n              className=\"flex items-center gap-1 text-xs sm:text-sm\"\r\n            >\r\n              <Eye className=\"w-3 h-3 sm:w-4 sm:h-4\" />\r\n              Details\r\n            </Button>\r\n          </div>\r\n\r\n          {/* Action Buttons */}\r\n          {getActionButtons().length > 0 && <div className=\"flex gap-2 flex-wrap\">{getActionButtons()}</div>}\r\n        </div>\r\n\r\n        {/* Helper Text */}\r\n        {styling.helperText && (\r\n          <div className=\"mt-3 p-3 bg-gradient-to-r from-amber-50 via-amber-25 to-amber-50 border border-amber-200 rounded-lg text-xs text-amber-900 shadow-sm backdrop-blur-sm\">\r\n            <div className=\"flex items-start gap-2\">\r\n              <AlertCircle className=\"w-4 h-4 text-amber-600 mt-0.5 flex-shrink-0\" />\r\n              <span className=\"leading-relaxed font-medium\">{styling.helperText}</span>\r\n            </div>\r\n          </div>\r\n        )}\r\n      </CardContent>\r\n    </Card>\r\n  )\r\n}\r\n\r\n\r\n// Order Details Modal\r\ninterface OrderDetailsModalProps {\r\n  order: rNETOrder | null\r\n  onClose: () => void\r\n  onAction: (order: rNETOrder, action: string) => void\r\n}\r\n\r\nexport function OrderDetailsModal({ order, onClose, onAction }: OrderDetailsModalProps) {\r\n  if (!order) return null\r\n\r\n  const handlePhoneClick = (phoneNumber: string) => {\r\n    window.open(`tel:${phoneNumber}`, \"_self\")\r\n  }\r\n\r\n  const timeDiff = getTimeDifferenceInSeconds(order.createdAt)\r\n\r\n  return (\r\n    <Dialog open={!!order} onOpenChange={onClose}>\r\n      <DialogContent className=\"max-w-4xl max-h-[90vh] overflow-y-auto w-[95vw] sm:w-full rounded-md\">\r\n        <DialogHeader>\r\n          <DialogTitle className=\"text-lg sm:text-xl flex items-center gap-2\">\r\n            Order Details - #{order.orderGroupId}\r\n            <Badge\r\n              variant={\r\n                order.orderStatus === \"Delivered\"\r\n                  ? \"default\"\r\n                  : order.orderStatus === \"Cancelled\"\r\n                    ? \"destructive\"\r\n                    : \"secondary\"\r\n              }\r\n              className=\"capitalize\"\r\n            >\r\n              {getStatusDisplayName(order.orderStatus)}\r\n            </Badge>\r\n          </DialogTitle>\r\n        </DialogHeader>\r\n\r\n        <div className=\"space-y-6\">\r\n          {/* Order Timeline */}\r\n          <div>\r\n            <h3 className=\"font-semibold mb-3 flex items-center gap-2\">\r\n              <Clock className=\"w-4 h-4\" />\r\n              Order Timeline\r\n            </h3>\r\n            <div className=\"space-y-2 text-sm\">\r\n              <div className=\"flex justify-between\">\r\n                <span>Order Placed:</span>\r\n                <span>{dayjs(order.createdAt).format(\"ddd, DD MMM YYYY - h:mm:ss A\")}</span>\r\n              </div>\r\n              {order.acceptedTime && (\r\n                <div className=\"flex justify-between\">\r\n                  <span>Accepted:</span>\r\n                  <span>{dayjs(order.acceptedTime).format(\"ddd, DD MMM YYYY - h:mm:ss A\")}</span>\r\n                </div>\r\n              )}\r\n              {order.packedTime && (\r\n                <div className=\"flex justify-between\">\r\n                  <span>Packed:</span>\r\n                  <span>{dayjs(order.packedTime).format(\"ddd, DD MMM YYYY - h:mm:ss A\")}</span>\r\n                </div>\r\n              )}\r\n              {order.assignedTime && (\r\n                <div className=\"flex justify-between\">\r\n                  <span>Assigned:</span>\r\n                  <span>{dayjs(order.assignedTime).format(\"ddd, DD MMM YYYY - h:mm:ss A\")}</span>\r\n                </div>\r\n              )}\r\n              {order.pickedUpTime && (\r\n                <div className=\"flex justify-between\">\r\n                  <span>Picked Up:</span>\r\n                  <span>{dayjs(order.pickedUpTime).format(\"ddd, DD MMM YYYY - h:mm:ss A\")}</span>\r\n                </div>\r\n              )}\r\n              {order.deliveryStartTime && (\r\n                <div className=\"flex justify-between\">\r\n                  <span>Dispatched:</span>\r\n                  <span>{dayjs(order.deliveryStartTime).format(\"ddd, DD MMM YYYY - h:mm:ss A\")}</span>\r\n                </div>\r\n              )}\r\n              {order.deliveredTime && (\r\n                <div className=\"flex justify-between\">\r\n                  <span>Delivered:</span>\r\n                  <span>{dayjs(order.deliveredTime).format(\"ddd, DD MMM YYYY - h:mm:ss A\")}</span>\r\n                </div>\r\n              )}\r\n              <div className=\"flex justify-between font-medium\">\r\n                <span>Total Time:</span>\r\n                <span>\r\n                  {[\"Delivered\", \"Cancelled\"].includes(order.orderStatus)\r\n                    ? formatTimeElapsed(\r\n                      getTimeDifferenceInSeconds(order.createdAt, order.deliveredTime || order.updatedAt),\r\n                    )\r\n                    : formatTimeElapsed(timeDiff)}\r\n                </span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <Separator />\r\n\r\n          {/* Basic Order Info */}\r\n          <div className=\"grid grid-cols-1 sm:grid-cols-2 gap-6\">\r\n            <div>\r\n              <h3 className=\"font-semibold mb-3 flex items-center gap-2\">\r\n                <Package className=\"w-4 h-4\" />\r\n                Order Information\r\n              </h3>\r\n              <div className=\"space-y-2 text-sm\">\r\n                <div>\r\n                  <strong>Order ID:</strong> #{order.orderGroupId}\r\n                </div>\r\n                <div>\r\n                  <strong>Network:</strong> {order.networkName}\r\n                </div>\r\n                <div>\r\n                  <strong>Delivery Type:</strong> {order.deliveryType}\r\n                </div>\r\n                <div>\r\n                  <strong>Logistics Provider:</strong> {order.logisticProvider}\r\n                </div>\r\n                {order.pos !== \"none\" && order.pos !== undefined && (\r\n                  <div>\r\n                    <strong>POS:</strong> {order.pos}\r\n                  </div>\r\n                )}\r\n                {order.deliveryOtp && (\r\n                  <div>\r\n                    <strong>Delivery OTP:</strong> {order.deliveryOtp}\r\n                  </div>\r\n                )}\r\n              </div>\r\n            </div>\r\n\r\n            <div>\r\n              <h3 className=\"font-semibold mb-3 flex items-center gap-2\">\r\n                <User className=\"w-4 h-4\" />\r\n                Customer Information\r\n              </h3>\r\n              <div className=\"space-y-2 text-sm\">\r\n                <div>\r\n                  <strong>Name:</strong> {order.buyerName}\r\n                </div>\r\n                <div className=\"flex items-center gap-2\">\r\n                  <strong>Phone:</strong>\r\n                  <span>{order.buyerMobile}</span>\r\n                  <Button\r\n                    size=\"sm\"\r\n                    variant=\"outline\"\r\n                    onClick={() => handlePhoneClick(order.buyerMobile)}\r\n                    className=\"h-6 w-6 p-0\"\r\n                  >\r\n                    <Phone className=\"w-3 h-3\" />\r\n                  </Button>\r\n                </div>\r\n                <div>\r\n                  <strong>Address:</strong> {order.bAddress}\r\n                </div>\r\n                <div>\r\n                  <strong>Area:</strong> {order.bAreaName}\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <Separator />\r\n\r\n          {/* Restaurant Info */}\r\n          <div>\r\n            <h3 className=\"font-semibold mb-3 flex items-center gap-2\">\r\n              <Store className=\"w-4 h-4\" />\r\n              Restaurant Information\r\n            </h3>\r\n            <div className=\"grid grid-cols-1 sm:grid-cols-2 gap-4 text-sm\">\r\n              <div>\r\n                <div>\r\n                  <strong>Name:</strong> {order.sellerName}\r\n                </div>\r\n                <div className=\"flex items-center gap-2\">\r\n                  <strong>Phone:</strong>\r\n                  <span>{order.sellerMobile}</span>\r\n                  <Button\r\n                    size=\"sm\"\r\n                    variant=\"outline\"\r\n                    onClick={() => handlePhoneClick(order.sellerMobile)}\r\n                    className=\"h-6 w-6 p-0\"\r\n                  >\r\n                    <Phone className=\"w-3 h-3\" />\r\n                  </Button>\r\n                </div>\r\n              </div>\r\n              <div>\r\n                {order.agentName && (\r\n                  <div>\r\n                    <strong>Agent:</strong> {order.agentName}\r\n                  </div>\r\n                )}\r\n                {order.sellerMessage && (\r\n                  <div>\r\n                    <strong>Message:</strong> {order.sellerMessage}\r\n                  </div>\r\n                )}\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Delivery Partner Info */}\r\n          {order.logisticProvider === \"MP2\" && order.logisticDetails?.riderName && (\r\n            <>\r\n              <Separator />\r\n              <div>\r\n                <h3 className=\"font-semibold mb-3 flex items-center gap-2\">\r\n                  <Truck className=\"w-4 h-4\" />\r\n                  Delivery Partner\r\n                </h3>\r\n                <div className=\"text-sm space-y-2\">\r\n                  <div>\r\n                    <strong>Name:</strong> {order.logisticDetails.riderName}\r\n                  </div>\r\n                  {order.logisticDetails.riderPhone && (\r\n                    <div className=\"flex items-center gap-2\">\r\n                      <strong>Phone:</strong>\r\n                      <span>{order.logisticDetails.riderPhone}</span>\r\n                      <Button\r\n                        size=\"sm\"\r\n                        variant=\"outline\"\r\n                        onClick={() => handlePhoneClick(order.logisticDetails!.riderPhone.toString())}\r\n                        className=\"h-6 w-6 p-0\"\r\n                      >\r\n                        <Phone className=\"w-3 h-3\" />\r\n                      </Button>\r\n                    </div>\r\n                  )}\r\n                  {order.logStatus && (\r\n                    <div>\r\n                      <strong>Logistics Status:</strong>\r\n                      <Badge className=\"ml-2\">{getLogisticStatusDisplayName(order.logStatus)}</Badge>\r\n                    </div>\r\n                  )}\r\n                  {order.logisticDetails.trackingUrl && (\r\n                    <div>\r\n                      <strong>Tracking:</strong>\r\n                      <a\r\n                        href={order.logisticDetails.trackingUrl}\r\n                        target=\"_blank\"\r\n                        rel=\"noopener noreferrer\"\r\n                        className=\"ml-2 text-blue-600 hover:underline\"\r\n                      >\r\n                        Track Order\r\n                      </a>\r\n                    </div>\r\n                  )}\r\n                </div>\r\n              </div>\r\n            </>\r\n          )}\r\n\r\n          {/* Order Items */}\r\n          {order.orderDetails && order.orderDetails.length > 0 && (\r\n            <>\r\n              <Separator />\r\n              <div>\r\n                <h3 className=\"font-semibold mb-3\">Order Items</h3>\r\n                <div className=\"space-y-3\">\r\n                  {order.orderDetails.map((item) => (\r\n                    <div key={item.orderId} className=\"border rounded p-3 bg-gray-50\">\r\n                      <div className=\"flex justify-between items-start\">\r\n                        <div className=\"flex-1\">\r\n                          <div className=\"font-medium\">{item.itemName}</div>\r\n                          {item.itemRegionalLanguageName && <div className=\"text-sm text-gray-600\">{item.itemRegionalLanguageName}</div>}\r\n                          {item.variationName && (\r\n                            <div className=\"text-sm text-gray-600\">Variation: {item.variationName}</div>\r\n                          )}\r\n                          {item.addOns && (\r\n                            <div className=\"text-sm text-gray-600\">\r\n                              Add-ons:{\" \"}\r\n                              {item.addOns\r\n                                .flatMap((aog) => aog.addOnItemList.map((addon) => `${addon.name} (+₹${addon.price})`))\r\n                                .join(\", \")}\r\n                            </div>\r\n                          )}\r\n                          <div className=\"text-xs text-gray-500 mt-1\">\r\n                            {item.diet && (\r\n                              <span\r\n                                className={`inline-block w-2 h-2 rounded-full mr-1 ${item.diet === \"veg\"\r\n                                  ? \"bg-green-500\"\r\n                                  : item.diet === \"nonveg\"\r\n                                    ? \"bg-red-500\"\r\n                                    : \"bg-yellow-500\"\r\n                                  }`}\r\n                              />\r\n                            )}\r\n                            {item.diet} • {item.unit}\r\n                          </div>\r\n                        </div>\r\n                        <div className=\"text-right ml-4\">\r\n                          <div className=\"text-sm\">Qty: {item.qty}</div>\r\n                          <div className=\"text-sm\">\r\n                            ₹{item.pricePerUnit} per {item.unit}\r\n                          </div>\r\n                          <div className=\"font-semibold\">\r\n                            ₹{item.amount}\r\n                            {(item.strikeOffAmount && item.strikeOffAmount !== item.amount) && (\r\n                              <span className=\"ml-0.5 text-sm text-gray-600 line-through\">\r\n                                ₹{item.strikeOffAmount}\r\n                              </span>\r\n                            )}\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  ))}\r\n                </div>\r\n              </div>\r\n            </>\r\n          )}\r\n\r\n          <Separator />\r\n\r\n          {/* Payment Breakdown */}\r\n          <div>\r\n            <h3 className=\"font-semibold mb-3\">Payment Breakdown</h3>\r\n            <div className=\"bg-gray-50 rounded p-4 space-y-2 text-sm\">\r\n              {(order.totalItemsStrikeoffAmount && (order.totalItemsStrikeoffAmount !== order.itemsTotalAmount)) && (\r\n                <div className=\"flex justify-between\">\r\n                  <span>Item Total (strikeoff):</span>\r\n                  <span>₹{order.totalItemsStrikeoffAmount}</span>\r\n                </div>\r\n              )}\r\n              <div className=\"flex justify-between\">\r\n                <span>Item Total:</span>\r\n                <span>₹{order.itemsTotalAmount}</span>\r\n              </div>\r\n              <div className=\"flex justify-between\">\r\n                <span>Delivery Charge:</span>\r\n                <span>₹{order.totalDeliveryCharge}</span>\r\n              </div>\r\n              <div className=\"flex justify-between\">\r\n                <span>Packaging Charges:</span>\r\n                <span>₹{order.packagingCharges}</span>\r\n              </div>\r\n              <div className=\"flex justify-between\">\r\n                <span>Platform Fee:</span>\r\n                <span>₹{order.platformFee}</span>\r\n              </div>\r\n              <div className=\"flex justify-between\">\r\n                <span>Tax Amount:</span>\r\n                <span>₹{order.totalTaxAmount}</span>\r\n              </div>\r\n              <div className=\"flex justify-between text-red-600\">\r\n                <span>Discount:</span>\r\n                <span>-₹{order.totalDiscountAmount}</span>\r\n              </div>\r\n              <Separator />\r\n              <div className=\"flex justify-between font-semibold text-lg\">\r\n                <span>Total Amount:</span>\r\n                <span>₹{order.totalOrderGroupAmount}</span>\r\n              </div>\r\n              <div className=\"flex justify-between text-sm text-gray-600\">\r\n                <span>COD Amount:</span>\r\n                <span>₹{order.codAmount}</span>\r\n              </div>\r\n              {order.walletAmount > 0 && (\r\n                <div className=\"flex justify-between text-sm text-gray-600\">\r\n                  <span>Wallet Amount:</span>\r\n                  <span>₹{order.walletAmount}</span>\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n\r\n          {/* Support Tickets */}\r\n          {order.supportTickets && order.supportTickets.length > 0 && (\r\n            <>\r\n              <Separator />\r\n              <div>\r\n                <h3 className=\"font-semibold mb-3\">Support Tickets</h3>\r\n                <div className=\"space-y-3\">\r\n                  {order.supportTickets.map((ticket) => (\r\n                    <div key={ticket.ticketId} className=\"border rounded p-3\">\r\n                      <div className=\"flex justify-between items-start mb-2\">\r\n                        <div className=\"font-medium\">Ticket #{ticket.ticketId}</div>\r\n                        <Badge\r\n                          variant={\r\n                            ticket.status === \"OPEN\" ? \"destructive\" : ticket.status === \"WIP\" ? \"default\" : \"secondary\"\r\n                          }\r\n                        >\r\n                          {ticket.status}\r\n                        </Badge>\r\n                      </div>\r\n                      <div className=\"text-sm text-gray-600 mb-2\">{ticket.description}</div>\r\n                      <div className=\"text-xs text-gray-500\">\r\n                        Created: {new Date(ticket.createdDate).toLocaleString()}\r\n                      </div>\r\n                    </div>\r\n                  ))}\r\n                </div>\r\n              </div>\r\n            </>\r\n          )}\r\n\r\n          {/* Action Buttons */}\r\n          <div className=\"flex gap-2 pt-4 border-t\">\r\n            <Button variant=\"outline\" onClick={onClose}>\r\n              Close\r\n            </Button>\r\n            {order.orderStatus === \"Created\" && (\r\n              <Button variant=\"destructive\" onClick={() => onAction(order, \"Cancel Order\")}>\r\n                Cancel Order\r\n              </Button>\r\n            )}\r\n            {showUpdateStatusButton(order) && (\r\n              <Button variant=\"default\" onClick={() => onAction(order, \"Update LiveOrder Status\")}>\r\n                Update to {order.orderStatus === \"Created\" ? \"Accepted\" : order.orderStatus === \"Accepted\" ? \"Packed\" : order.orderStatus === \"Packed\" ? \"Assigned\" : order.orderStatus === \"Assigned\" ? \"PickedUp\" : \"Dispatched\"} <ArrowRight className=\"w-3 h-3\" />\r\n              </Button>\r\n            )}\r\n            {(order.orderStatus === \"Dispatched\") && (\r\n              <Button variant=\"default\" className=\"bg-blue-500 hover:bg-blue-500\" onClick={() => onAction(order, \"Mark Delivered\")}>\r\n                Mark Delivered\r\n              </Button>\r\n            )}\r\n          </div>\r\n        </div>\r\n      </DialogContent>\r\n    </Dialog>\r\n  )\r\n}\r\n\r\n\r\n// Action Modal\r\ninterface ActionModalProps {\r\n  order: rNETOrder | null\r\n  actionType: string\r\n  onClose: () => void\r\n  isSubmitting: boolean\r\n  onSubmit: (formData: any) => void\r\n}\r\n\r\nexport function ActionModal({ order, actionType, onClose, isSubmitting, onSubmit }: ActionModalProps) {\r\n  const [formData, setFormData] = useState({\r\n    // order cancel\r\n    reason: \"\",\r\n    // update status to packed\r\n    boxes: \"\",\r\n    bags: \"\",\r\n    // mark delivered\r\n    deliveryCode: \"\",\r\n    creditAmount: \"\",\r\n    boxesGiven: \"\",\r\n    boxesTaken: \"\"\r\n  })\r\n\r\n  const handleSubmit = (e: React.FormEvent) => {\r\n    e.preventDefault()\r\n    onSubmit(formData)\r\n  }\r\n\r\n  if (!order || !actionType) return null\r\n\r\n  const renderCancelModal = () => (\r\n    <div className=\"space-y-4\">\r\n      <div className=\"flex items-center gap-2 p-4 bg-red-50 border border-red-200 rounded\">\r\n        <AlertTriangle className=\"w-5 h-5 text-red-600\" />\r\n        <div>\r\n          <p className=\"font-medium text-red-800\">Cancel Order</p>\r\n          <p className=\"text-sm text-red-600\">\r\n            This action is irreversible. The order will be cancelled from all systems.\r\n          </p>\r\n        </div>\r\n      </div>\r\n\r\n      <div>\r\n        <Label htmlFor=\"reason\">Cancellation Reason</Label>\r\n        <Textarea\r\n          id=\"reason\"\r\n          value={formData.reason}\r\n          onChange={(e) => setFormData((prev) => ({ ...prev, reason: e.target.value }))}\r\n          placeholder=\"Enter reason for cancellation...\"\r\n          className=\"mt-1 w-72 block border-2 rounded-md\"\r\n        />\r\n      </div>\r\n\r\n      <div className=\"flex gap-2 pt-4\">\r\n        <Button variant=\"outline\" onClick={onClose} disabled={isSubmitting}>\r\n          Cancel\r\n        </Button>\r\n        <Button variant=\"destructive\" onClick={handleSubmit} disabled={isSubmitting}>\r\n          {isSubmitting ? \"Cancelling...\" : \"Confirm Cancellation\"}\r\n        </Button>\r\n      </div>\r\n    </div>\r\n  )\r\n\r\n  const renderUpdateStatusModal = () => (\r\n    <div className=\"space-y-4\">\r\n      <div className=\"flex items-center gap-2 p-4 bg-yellow-50 border border-yellow-200 rounded\">\r\n        <AlertTriangle className=\"w-5 h-5 text-yellow-600\" />\r\n        <div>\r\n          <p className=\"text-yellow-700\">This will update the status of the order to {order.orderStatus === \"Created\" ? \"Accepted\" : order.orderStatus === \"Accepted\" ? \"Packed\" : order.orderStatus === \"Packed\" ? \"Assigned\" : order.orderStatus === \"Assigned\" ? \"PickedUp\" : \"Dispatched\"}.</p>\r\n        </div>\r\n      </div>\r\n      {/* {order.orderStatus === \"Accepted\" && (\r\n        <div className=\"grid grid-cols-1 sm:grid-cols-2 gap-4\">\r\n          <div>\r\n            <Label htmlFor=\"boxes\">Number of Boxes</Label>\r\n            <Input\r\n              id=\"boxes\"\r\n              type=\"number\"\r\n              min={0}\r\n              value={formData.boxes}\r\n              onChange={(e) => setFormData((prev) => ({ ...prev, boxes: e.target.value }))}\r\n              placeholder=\"Enter number of boxes\"\r\n            />\r\n          </div>\r\n\r\n          <div>\r\n            <Label htmlFor=\"bags\">Number of Bags</Label>\r\n            <Input\r\n              id=\"bags\"\r\n              type=\"number\"\r\n              min={0}\r\n              value={formData.bags}\r\n              onChange={(e) => setFormData((prev) => ({ ...prev, bags: e.target.value }))}\r\n              placeholder=\"Enter number of bags\"\r\n            />\r\n          </div>\r\n        </div>\r\n      )} */}\r\n\r\n      <div className=\"flex gap-2 pt-4\">\r\n        <Button variant=\"outline\" onClick={onClose} disabled={isSubmitting}>\r\n          Cancel\r\n        </Button>\r\n        <Button\r\n          onClick={handleSubmit}\r\n          disabled={isSubmitting}\r\n        >\r\n          {isSubmitting ? \"Updating...\" : \"Confirm Update\"}\r\n        </Button>\r\n      </div>\r\n    </div>\r\n  )\r\n\r\n  const renderMarkDeliveredModal = () => (\r\n    <div className=\"space-y-4\">\r\n      <div className=\"flex items-center gap-2 p-4 bg-yellow-50 border border-yellow-200 rounded\">\r\n        <AlertTriangle className=\"w-5 h-5 text-yellow-600\" />\r\n        <div>\r\n          <p className=\"font-medium text-yellow-800\">Mark Order as Delivered</p>\r\n          <p className=\"text-sm text-yellow-600\">This will mark the order as delivered.</p>\r\n        </div>\r\n      </div>\r\n      {/*\r\n      <div className=\"grid grid-cols-1 sm:grid-cols-2 gap-4\">\r\n        <div>\r\n          <Label htmlFor=\"deliveryCode\">Delivery Code</Label>\r\n          <Input\r\n            id=\"deliveryCode\"\r\n            value={formData.deliveryCode}\r\n            onChange={(e) => setFormData((prev) => ({ ...prev, deliveryCode: e.target.value }))}\r\n            placeholder=\"Enter delivery code\"\r\n          />\r\n        </div>\r\n\r\n        <div>\r\n          <Label htmlFor=\"creditAmount\">Credit Amount</Label>\r\n          <Input\r\n            id=\"creditAmount\"\r\n            type=\"number\"\r\n            value={formData.creditAmount}\r\n            onChange={(e) => setFormData((prev) => ({ ...prev, creditAmount: e.target.value }))}\r\n            placeholder=\"Enter credit amount\"\r\n          />\r\n        </div>\r\n\r\n        <div>\r\n          <Label htmlFor=\"boxesGiven\">Number of Boxes Given</Label>\r\n          <Input\r\n            id=\"boxesGiven\"\r\n            type=\"number\"\r\n            min={0}\r\n            value={formData.boxesGiven}\r\n            onChange={(e) => setFormData((prev) => ({ ...prev, boxesGiven: e.target.value }))}\r\n            placeholder=\"Enter number of boxes given\"\r\n          />\r\n        </div>\r\n\r\n        <div>\r\n          <Label htmlFor=\"boxesTaken\">Number of Boxes Taken</Label>\r\n          <Input\r\n            id=\"boxesTaken\"\r\n            type=\"number\"\r\n            min={0}\r\n            value={formData.boxesTaken}\r\n            onChange={(e) => setFormData((prev) => ({ ...prev, boxesTaken: e.target.value }))}\r\n            placeholder=\"Enter number of boxes taken\"\r\n          />\r\n        </div>\r\n      </div> */}\r\n\r\n      <div className=\"flex gap-2 pt-4\">\r\n        <Button variant=\"outline\" onClick={onClose} disabled={isSubmitting}>\r\n          Cancel\r\n        </Button>\r\n        <Button\r\n          onClick={handleSubmit}\r\n          disabled={isSubmitting}\r\n          className=\"bg-blue-500 hover:bg-blue-500\"\r\n        >\r\n          {isSubmitting ? \"Marking...\" : \"Confirm Delivery\"}\r\n        </Button>\r\n      </div>\r\n    </div>\r\n  )\r\n\r\n  return (\r\n    <Dialog open={true} onOpenChange={onClose}>\r\n      <DialogContent className=\"max-w-2xl rounded-md\">\r\n        <DialogHeader>\r\n          <DialogTitle>\r\n            {actionType === \"Cancel Order\" && \"Cancel Order\"}\r\n            {actionType === \"Update LiveOrder Status\" && \"Update LiveOrder Status\"}\r\n            {actionType === \"Mark Delivered\" && \"Mark Delivered\"}\r\n          </DialogTitle>\r\n        </DialogHeader>\r\n\r\n        {actionType === \"Cancel Order\" && renderCancelModal()}\r\n        {actionType === \"Update LiveOrder Status\" && renderUpdateStatusModal()}\r\n        {actionType === \"Mark Delivered\" && renderMarkDeliveredModal()}\r\n      </DialogContent>\r\n    </Dialog>\r\n  )\r\n}\r\n\r\n\r\nexport const getTimeDifferenceInSeconds = (orderTime: string, endTime?: string): number => {\r\n  const orderDate = new Date(orderTime)\r\n  const compareDate = endTime ? new Date(endTime) : new Date()\r\n  return Math.floor((compareDate.getTime() - orderDate.getTime()) / 1000)\r\n}\r\n\r\n\r\nexport const formatTimeElapsed = (seconds: number): string => {\r\n  const minutes = Math.floor(seconds / 60)\r\n  const hours = Math.floor(minutes / 60)\r\n  const days = Math.floor(hours / 24)\r\n\r\n  if (days > 0) {\r\n    return `${days}d ${hours % 24}h`\r\n  }\r\n  if (hours > 0) {\r\n    return `${hours}h ${minutes % 60}m`\r\n  }\r\n  return `${minutes}m`\r\n}\r\n\r\n\r\nexport const getOrderCardStyling = (order: rNETOrder) => {\r\n  const timeDiff = getTimeDifferenceInSeconds(order.createdAt)\r\n  const baseClasses = \"border rounded-lg transition-all duration-500 \"\r\n  let pulseClasses = \"\"\r\n  let bgClasses = \"bg-white hover:shadow-md \"\r\n  let helperText = \"\"\r\n\r\n  // Status-based logic according to PRD with modern animations\r\n  switch (order.orderStatus) {\r\n    case \"Created\":\r\n      if (timeDiff > 180) {\r\n        bgClasses = \"bg-gradient-to-br from-red-200 to-red-50 \"\r\n        pulseClasses = \"card-alert-breathing \"\r\n        helperText = \"🚨 URGENT: Order not yet accepted. Please inform the seller to either accept or cancel the order.\"\r\n      } else if (timeDiff > 120) {\r\n        bgClasses = \"bg-gradient-to-br from-amber-200 to-amber-50 \"\r\n        pulseClasses = \"card-alert-warning \"\r\n        helperText = \"⚠️ Order yet to be accepted. Please inform the seller again.\"\r\n      }\r\n      break\r\n\r\n    case \"Accepted\":\r\n      if (order.logisticProvider === \"MP2\") {\r\n        if (\r\n          timeDiff > 900 &&\r\n          order.logStatus &&\r\n          [\"LOG_CREATED\", \"LOG_PENDING\", \"LOG_SEARCHING_AGENT\"].includes(order.logStatus)\r\n        ) {\r\n          bgClasses = \"bg-gradient-to-br from-red-200 to-red-50 \"\r\n          pulseClasses = \"card-alert-animated \"\r\n          helperText = \"🚨 CRITICAL: Please highlight the order with mp2 team or start manual fulfilment of the order.\"\r\n        } else if (\r\n          timeDiff > 600 &&\r\n          order.logStatus &&\r\n          [\"LOG_CREATED\", \"LOG_PENDING\", \"LOG_SEARCHING_AGENT\"].includes(order.logStatus)\r\n        ) {\r\n          bgClasses = \"bg-gradient-to-br from-orange-200 to-orange-50 \"\r\n          pulseClasses = \"card-alert-warning \"\r\n          helperText = \"⚡ Please highlight the order to mp2 team and start preparing for manual deliveries.\"\r\n        } else if (timeDiff >= 600 && order.logStatus === \"LOG_AGENT_ASSIGNED\") {\r\n          pulseClasses = \"card-alert-warning \"\r\n          helperText = \"📞 Please follow up with the rider and check if he is moving toward the restaurant.\"\r\n        }\r\n      } else if (order.logisticProvider === \"SELF\" && timeDiff > 900) {\r\n        pulseClasses = \"card-alert-warning \"\r\n        helperText = \"🏪 Please follow up with the seller for self delivery.\"\r\n      }\r\n      break\r\n\r\n    case \"Packed\":\r\n      if (order.logisticProvider === \"MP2\") {\r\n        if (timeDiff > 1200 && [\"LOG_CREATED\", \"LOG_PENDING\", \"LOG_SEARCHING_AGENT\"].includes(order.logStatus)) {\r\n          bgClasses = \"bg-gradient-to-br from-red-200 to-red-50 \"\r\n          pulseClasses = \"card-alert-breathing \"\r\n          helperText = \"🚨 URGENT: Please check with the delivery partner and raise the issue with the mp2 team.\"\r\n        } else if (timeDiff > 1200 && order.logStatus === \"LOG_AGENT_ASSIGNED\") {\r\n          bgClasses = \"bg-gradient-to-br from-red-200 to-red-50 \"\r\n          pulseClasses = \"card-alert-animated \"\r\n          helperText = \"🏃‍♂️ Please follow up with the rider and check if he is moving toward the restaurant.\"\r\n        }\r\n      } else if (order.logisticProvider === \"SELF\" && timeDiff > 1200) {\r\n        pulseClasses = \"card-alert-warning \"\r\n        helperText = \"🏃‍♂️ Please follow up with the seller for self delivery.\"\r\n      }\r\n      break\r\n  }\r\n\r\n  return {\r\n    className: baseClasses + bgClasses + pulseClasses,\r\n    helperText,\r\n  }\r\n}\r\n\r\n\r\nexport const getStatusDisplayName = (status: string): string => {\r\n  switch (status) {\r\n    case \"Created\":\r\n      return \"Acceptance Pending\"\r\n    case \"Packed\":\r\n      return \"Ready for Pickup\"\r\n    default:\r\n      return status\r\n  }\r\n}\r\n\r\n\r\nexport const getLogisticStatusDisplayName = (logStatus: string): string => {\r\n  return logStatus\r\n    .replace(\"LOG_\", \"\")\r\n    .replace(\"_\", \" \")\r\n    .toLowerCase()\r\n    .split(\" \")\r\n    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))\r\n    .join(\" \")\r\n}\r\n\r\nexport const showUpdateStatusButton = (order: rNETOrder) => {\r\n  const isPos = order.pos !== \"none\" && order.pos !== undefined\r\n  const isLogisticProvider = order.logisticProvider !== \"SELF\" && order.logisticProvider !== undefined\r\n  if (isPos && isLogisticProvider) {\r\n    return (order.orderStatus === \"Created\" || order.orderStatus === \"Accepted\" || order.orderStatus === \"Packed\" || order.orderStatus === \"Assigned\" || order.orderStatus === \"PickedUp\")\r\n    // return false\r\n  }\r\n  if (!isPos && !isLogisticProvider) {\r\n    return (order.orderStatus === \"Created\" || order.orderStatus === \"Accepted\" || order.orderStatus === \"Packed\" || order.orderStatus === \"Assigned\" || order.orderStatus === \"PickedUp\")\r\n    // return (order.orderStatus === \"Created\" || order.orderStatus === \"Accepted\" || order.orderStatus === \"Assigned\" || order.orderStatus === \"PickedUp\")\r\n  }\r\n  if (isPos && !isLogisticProvider) {\r\n    return (order.orderStatus === \"Created\" || order.orderStatus === \"Accepted\" || order.orderStatus === \"Packed\" || order.orderStatus === \"Assigned\" || order.orderStatus === \"PickedUp\")\r\n    // return (order.orderStatus === \"Assigned\")\r\n  }\r\n  if (!isPos && isLogisticProvider) {\r\n    return (order.orderStatus === \"Created\" || order.orderStatus === \"Accepted\" || order.orderStatus === \"Packed\" || order.orderStatus === \"Assigned\" || order.orderStatus === \"PickedUp\")\r\n    // return (order.orderStatus === \"Created\" || order.orderStatus === \"Accepted\" || order.orderStatus === \"PickedUp\")\r\n  }\r\n  return (order.orderStatus === \"Created\" || order.orderStatus === \"Accepted\" || order.orderStatus === \"Packed\" || order.orderStatus === \"Assigned\" || order.orderStatus === \"PickedUp\")\r\n}"], "names": ["LiveOrderDashboard", "allOrders", "setAllOrders", "useState", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedOrder", "orderStatusCounts", "setOrderStatusCounts", "activeTab", "setActiveTab", "fetcher", "useFetcher", "actionType", "setActionType", "actionSelectedOrder", "setActionSelectedOrder", "isSubmitting", "setIsSubmitting", "toast", "useToast", "searchType", "setSearchType", "searchTerm", "setSearchTerm", "filterDate", "setFilterDate", "from", "Date", "to", "filterStatus", "setFilterStatus", "pageSize", "setPageSize", "currentPage", "setCurrentPage", "totalElements", "setTotalElements", "date<PERSON><PERSON><PERSON>", "setDateRange", "debounceSearchTerm", "setDebounceSearchTerm", "useEffect", "timer", "setTimeout", "clearTimeout", "animationKey", "setAnimationKey", "isPolling", "startPolling", "stopPolling", "usePolling", "refreshOrders", "useCallback", "isAutoRefresh", "console", "log", "formData", "FormData", "append", "data", "JSON", "stringify", "submit", "method", "handleVisibilityChange", "document", "visibilityState", "addEventListener", "removeEventListener", "handleTabChange", "newTab", "searchTypeFilters", "label", "value", "statusFilters", "liveOrders", "completedOrders", "useMemo", "live", "filter", "order", "includes", "orderStatus", "sort", "a", "b", "createdAt", "getTime", "completed", "stats", "total", "created", "find", "s", "status", "count", "accepted", "packed", "assigned", "dispatched", "delivered", "cancelled", "handleAction", "action", "handleSubmitAction", "actionData", "intent", "success", "orders", "errorMessage", "title", "description", "variant", "style", "backgroundColor", "color", "className", "children", "jsxs", "jsx", "<PERSON><PERSON>", "size", "onClick", "disabled", "state", "RefreshCw", "Tabs", "onValueChange", "TabsList", "TabsTrigger", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Select", "SelectTrigger", "SelectValue", "placeholder", "SelectContent", "map", "SelectItem", "Search", "Input", "onChange", "e", "target", "Popover", "PopoverTrigger", "<PERSON><PERSON><PERSON><PERSON>", "id", "cn", "CalendarIcon", "Fragment", "format", "PopoverC<PERSON>nt", "align", "Calendar", "initialFocus", "selected", "mode", "onSelect", "range", "PopoverClose", "Label", "Filter", "toString", "Number", "Array", "length", "Math", "ceil", "_", "i", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Badge", "Clock", "transform", "transition", "index", "animationDelay", "animationFillMode", "<PERSON><PERSON><PERSON><PERSON>", "OrderCard", "onViewDetails", "onAction", "orderGroupId", "AlertCircle", "Table", "TableHeader", "TableRow", "TableHead", "TableBody", "TableCell", "colSpan", "dayjs", "buyerName", "bAreaName", "totalOrderGroupAmount", "toLocaleString", "paymentCompleted", "OrderDetailsModal", "onClose", "ActionModal", "onSubmit", "currentTime", "setCurrentTime", "now", "interval", "setInterval", "clearInterval", "styling", "getOrderCardStyling", "timeDiff", "getTimeDifferenceInSeconds", "isNewOrder", "audio", "Audio", "play", "catch", "warn", "pause", "src", "handlePhoneClick", "phoneNumber", "stopPropagation", "window", "open", "getActionButtons", "buttons", "push", "showUpdateStatusButton", "ArrowRight", "onMouseEnter", "currentTarget", "animationPlayState", "onMouseLeave", "supportTickets", "MessageCircleQuestionIcon", "formatTimeElapsed", "deliveredTime", "updatedAt", "Timer", "User", "buyerMobile", "Phone", "MapPin", "b<PERSON><PERSON><PERSON>", "Store", "sellerName", "sellerMobile", "logisticProvider", "pos", "logisticDetails", "Truck", "<PERSON><PERSON><PERSON>", "riderPhone", "logStatus", "getLogisticStatusDisplayName", "totalItems", "orderDetails", "slice", "item", "idx", "itemName", "qty", "min", "orderId", "getStatusDisplayName", "Eye", "helperText", "Dialog", "onOpenChange", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogHeader", "DialogTitle", "acceptedTime", "packedTime", "assignedTime", "pickedUpTime", "deliveryStartTime", "Separator", "Package", "networkName", "deliveryType", "deliveryOtp", "<PERSON><PERSON><PERSON>", "sellerMessage", "trackingUrl", "href", "rel", "itemRegionalLanguageName", "variationName", "addOns", "flatMap", "aog", "addOnItemList", "addon", "name", "price", "join", "diet", "unit", "pricePerUnit", "amount", "strikeOffAmount", "totalItemsStrikeoffAmount", "itemsTotalAmount", "totalDeliveryCharge", "packagingCharges", "platformFee", "totalTaxAmount", "totalDiscountAmount", "codAmount", "walletAmount", "ticket", "ticketId", "createdDate", "setFormData", "reason", "boxes", "bags", "deliveryCode", "creditAmount", "boxesGiven", "boxesTaken", "handleSubmit", "preventDefault", "renderCancelModal", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "htmlFor", "Textarea", "prev", "renderUpdateStatusModal", "renderMarkDeliveredModal", "orderTime", "endTime", "orderDate", "compareDate", "floor", "seconds", "minutes", "hours", "days", "baseClasses", "pulseClasses", "bgClasses", "replace", "toLowerCase", "split", "word", "char<PERSON>t", "toUpperCase", "isPos", "is<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqHA,SAAwBA,qBAAqB;;AAC3C,QAAM,CAACC,WAAWC,YAAY,IAAIC,sBAAsB;AACxD,QAAM,CAACC,eAAeC,gBAAgB,IAAIF,aAAAA,SAA2B,IAAI;AACzE,QAAM,CAACG,mBAAmBC,oBAAoB,IAAIJ,aAAAA,SAA6B,CAAA,CAAE;AACjF,QAAM,CAACK,WAAWC,YAAY,IAAIN,aAAAA,SAAyB,MAAM;AACjE,QAAMO,UAAUC,WAAuB;AAGvC,QAAM,CAACC,YAAYC,aAAa,IAAIV,aAAAA,SAAiB,EAAE;AACvD,QAAM,CAACW,qBAAqBC,sBAAsB,IAAIZ,aAAAA,SAA2B,IAAI;AACrF,QAAM,CAACa,cAAcC,eAAe,IAAId,aAAAA,SAAS,KAAK;AAChD,QAAA;AAAA,IAAEe;AAAAA,EAAM,IAAIC,SAAS;AAG3B,QAAM,CAACC,YAAYC,aAAa,IAAIlB,aAAAA,SAAyD,SAAS;AACtG,QAAM,CAACmB,YAAYC,aAAa,IAAIpB,aAAAA,SAAiB,EAAE;AACvD,QAAM,CAACqB,YAAYC,aAAa,IAAItB,sBAAgC;AAAA,IAAEuB,MAAU,oBAAAC,KAAQ;AAAA,IAAAC,IAAQ,oBAAAD,KAAA;AAAA,EAAO,CAAC;AACxG,QAAM,CAACE,cAAcC,eAAe,IAAI3B,aAAAA,SAA2B,EAAE;AACrE,QAAM,CAAC4B,UAAUC,WAAW,IAAI7B,aAAAA,SAAS,EAAE;AAC3C,QAAM,CAAC8B,aAAaC,cAAc,IAAI/B,aAAAA,SAAS,CAAC;AAChD,QAAM,CAACgC,eAAeC,gBAAgB,IAAIjC,aAAAA,SAAS,CAAC;AAGpD,QAAM,CAACkC,WAAWC,YAAY,IAAInC,sBAAgC;AAAA,IAAEuB,MAAU,oBAAAC,KAAQ;AAAA,IAAAC,IAAQ,oBAAAD,KAAA;AAAA,EAAO,CAAC;AAGtG,QAAM,CAACY,oBAAoBC,qBAAqB,IAAIrC,aAAAA,SAAiB,EAAE;AACvEsC,eAAAA,UAAU,MAAM;AACR,UAAAC,QAAQC,WAAW,MAAM;AAC7BH,4BAAsBlB,UAAU;AAAA,OAC/B,GAAG;AAEN,WAAO,MAAM;AACXsB,mBAAaF,KAAK;AAAA,IACpB;AAAA,EACF,GAAG,CAACpB,UAAU,CAAC;AAGf,QAAM,CAACuB,cAAcC,eAAe,IAAI3C,aAAAA,SAAS,CAAC;AAG5C,QAAA;AAAA,IAAE4C;AAAAA,IAAWC;AAAAA,IAAcC;AAAAA,EAAY,IAAIC,WAAW,MAAMC,cAAc,IAAI,GAAG,IAAK;AAEtF,QAAAA,gBAAgBC,aAAY,YAACC,mBAA4B;AAC7DC,YAAQC,IAAI,sBAAsB;AAE5B,UAAAC,WAAW,IAAIC,SAAS;AACrBD,aAAAE,OAAO,UAAU,cAAc;AAClC,UAAAC,OAAO;AAAA,MAAEnD;AAAAA,MAAWY;AAAAA,MAAYE,YAAYiB;AAAAA,MAAoBf;AAAAA,MAAYK;AAAAA,MAAcE;AAAAA,MAAUE;AAAAA,IAAY;AACtHuB,aAASE,OAAO,QAAQE,KAAKC,UAAUF,IAAI,CAAC;AAC5CjD,YAAQoD,OAAON,UAAU;AAAA,MAAEO,QAAQ;AAAA,IAAO,CAAC;AAAA,EAC7C,GAAG,CAACvD,WAAW+B,oBAAoBf,YAAYK,cAAcE,UAAUE,WAAW,CAAC;AAEnFQ,eAAAA,UAAU,MAAM;AACAU,kBAAA;AAAA,EAChB,GAAG,CAAC3C,WAAW+B,oBAAoBf,YAAYK,cAAcE,UAAUE,WAAW,CAAC;AAEnFQ,eAAAA,UAAU,MAAM;AACd,QAAIjC,cAAc,QAAQ;AACXwC,mBAAA;AAAA,IACf,OAAO;AACOC,kBAAA;AAAA,IACd;AACA,WAAO,MAAMA,YAAY;AAAA,EACxB,GAAA,CAACzC,WAAWwC,cAAcC,WAAW,CAAC;AAGzCR,eAAAA,UAAU,MAAM;AACd,UAAMuB,yBAAyBA,MAAM;AACnC,UAAIC,SAASC,oBAAoB,aAAa1D,cAAc,QAAQ;AACrDwC,qBAAA;AAAA,MACf,OAAO;AACOC,oBAAA;AAAA,MACd;AAAA,IACF;AACSgB,aAAAE,iBAAiB,oBAAoBH,sBAAsB;AACpE,WAAO,MAAM;AACFC,eAAAG,oBAAoB,oBAAoBJ,sBAAsB;AAAA,IACzE;AAAA,EACC,GAAA,CAACxD,WAAWwC,cAAcC,WAAW,CAAC;AAEnC,QAAAoB,kBAAmBC,YAAmB;AAC1CjD,kBAAc,SAAS;AACvBE,kBAAc,EAAE;AAChBiB,0BAAsB,EAAE;AACxB,QAAI8B,WAAW,QAAQ;AACP7C,oBAAA;AAAA,QAAEC,MAAU,oBAAAC;QAAQC,IAAI,oBAAID,KAAK;AAAA,MAAE,CAAC;AACrCW,mBAAA;AAAA,QAAEZ,MAAU,oBAAAC;QAAQC,IAAI,oBAAID,KAAK;AAAA,MAAE,CAAC;AAAA,IACnD,OAAO;AACLF,oBAAc,MAAS;AACvBa,mBAAa,MAAS;AAAA,IACxB;AACAR,oBAAgB,EAAE;AAClBE,gBAAY,EAAE;AACdE,mBAAe,CAAC;AAChBhC,iBAAa,CAAA,CAAE;AACfK,yBAAqB,CAAA,CAAE;AACvBE,iBAAa6D,MAAwB;AAAA,EACvC;AAEA,QAAMC,oBAAoB,CACxB;AAAA,IAAEC,OAAO;AAAA,IAAYC,OAAO;AAAA,EAAU,GACtC;AAAA,IAAED,OAAO;AAAA,IAAYC,OAAO;AAAA,EAAU,GACtC;AAAA,IAAED,OAAO;AAAA,IAAgBC,OAAO;AAAA,EAAc,GAC9C;AAAA,IAAED,OAAO;AAAA,IAAQC,OAAO;AAAA,EAAO,CAAA;AAGjC,QAAMC,gBAAgB,CACpB;AAAA,IAAEF,OAAO;AAAA,IAAWC,OAAO;AAAA,EAAU,GACrC;AAAA,IAAED,OAAO;AAAA,IAAYC,OAAO;AAAA,EAAW,GACvC;AAAA,IAAED,OAAO;AAAA,IAAUC,OAAO;AAAA,EAAS,GACnC;AAAA,IAAED,OAAO;AAAA,IAAYC,OAAO;AAAA,EAAW,GACvC;AAAA,IAAED,OAAO;AAAA,IAAaC,OAAO;AAAA,EAAW,GACxC;AAAA,IAAED,OAAO;AAAA,IAAcC,OAAO;AAAA,EAAa,GAC3C;AAAA,IAAED,OAAO;AAAA,IAAaC,OAAO;AAAA,EAAY,GACzC;AAAA,IAAED,OAAO;AAAA,IAAaC,OAAO;AAAA,EAAY,CAAA;AAI3C,QAAM;AAAA,IAAEE;AAAAA,IAAYC;AAAAA,EAAgB,IAAIC,qBAAQ,MAAM;AACpD,UAAMC,OAAO7E,uCAAW8E,OAAQC,WAAU,CAAC,CAAC,aAAa,WAAW,EAAEC,SAASD,MAAME,WAAW,GAC7FC,KAAK,CAACC,GAAGC,MAAM,IAAI1D,KAAKyD,EAAEE,SAAS,EAAEC,YAAY,IAAI5D,KAAK0D,EAAEC,SAAS,EAAEC;AAE1E,UAAMC,YAAYvF,uCAAW8E,OAAQC,WAAU,CAAC,aAAa,WAAW,EAAEC,SAASD,MAAME,WAAW;AAEpG,WAAO;AAAA,MAAEP,YAAYG;AAAAA,MAAMF,iBAAiBY;AAAAA,IAAU;AAAA,EACxD,GAAG,CAACvF,SAAS,CAAC;AAGd,QAAMwF,QAAQ;AAAA,IACZC,OAAOvD;AAAAA,IACPwD,WAASrF,uBAAkBsF,KAAMC,OAAMA,EAAEC,WAAW,SAAS,MAApDxF,mBAAuDyF,UAAS;AAAA,IACzEC,YAAU1F,uBAAkBsF,KAAMC,OAAMA,EAAEC,WAAW,UAAU,MAArDxF,mBAAwDyF,UAAS;AAAA,IAC3EE,UAAQ3F,uBAAkBsF,KAAMC,OAAMA,EAAEC,WAAW,QAAQ,MAAnDxF,mBAAsDyF,UAAS;AAAA,IACvEG,YAAU5F,uBAAkBsF,KAAMC,OAAMA,EAAEC,WAAW,UAAU,MAArDxF,mBAAwDyF,UAAS;AAAA,IAC3EI,cAAY7F,uBAAkBsF,KAAMC,OAAMA,EAAEC,WAAW,YAAY,MAAvDxF,mBAA0DyF,UAAS;AAAA,IAC/EK,aAAW9F,uBAAkBsF,KAAMC,OAAMA,EAAEC,WAAW,WAAW,MAAtDxF,mBAAyDyF,UAAS;AAAA,IAC7EM,aAAW/F,uBAAkBsF,KAAMC,OAAMA,EAAEC,WAAW,WAAW,MAAtDxF,mBAAyDyF,UAAS;AAAA,EAC/E;AAEM,QAAAO,eAAeA,CAACtB,OAAkBuB,YAAmB;AACzDxF,2BAAuBiE,KAAK;AAC5BnE,kBAAc0F,OAAM;AAAA,EACtB;AAEM,QAAAC,qBAAsBhD,cAAkB;AACtC,UAAAiD,aAAa,IAAIhD,SAAS;AACrBgD,eAAA/C,OAAO,UAAU9C,UAAU;AAC3B6F,eAAA/C,OAAO,QAAQE,KAAKC,UAAU;AAAA,MAAEmB,OAAOlE;AAAAA,MAAqB0C;AAAAA,IAAS,CAAC,CAAC;AAClF9C,YAAQoD,OAAO2C,YAAY;AAAA,MAAE1C,QAAQ;AAAA,IAAO,CAAC;AAC7C9C,oBAAgB,IAAI;AAAA,EACtB;AAEAwB,eAAAA,UAAU,MAAM;;AACV,UAAA/B,MAAAA,QAAQiD,SAARjD,gBAAAA,IAAcgG,YAAW,gBAAgB;AACvC,WAAAhG,MAAAA,QAAQiD,SAARjD,gBAAAA,IAAciG,SAAS;AACjBjG,UAAAA,OAAAA,MAAAA,QAAAiD,SAAAjD,gBAAAA,IAAMiD,SAANjD,gBAAAA,IAAYkG,UAAS1G,aAAaQ,QAAQiD,KAAKA,KAAKiD,MAAM,IAAI1G,aAAa,CAAA,CAAE;AAC7EQ,UAAAA,OAAAA,MAAAA,QAAAiD,SAAAjD,gBAAAA,IAAMiD,SAANjD,gBAAAA,IAAYyB,iBAAgBC,iBAAiB1B,QAAQiD,KAAKA,KAAKxB,aAAa,IAAIC,iBAAiB,CAAC;AAClG1B,UAAAA,OAAAA,MAAAA,QAAAiD,SAAAjD,gBAAAA,IAAMiD,SAANjD,gBAAAA,IAAYJ,qBAAoBC,qBAAqBG,QAAQiD,KAAKA,KAAKrD,iBAAiB,IAAIC,qBAAqB,CAAA,CAAE;AAAA,MAClH,aAAAG,MAAAA,QAAQiD,SAARjD,gBAAAA,IAAciG,aAAY,OAAO;AAClCrD,gBAAAC,KAAI7C,MAAAA,QAAQiD,SAARjD,gBAAAA,IAAcmG,YAAY;AAChC3F,cAAA;AAAA,UACJ4F,OAAO;AAAA,UACPC,cAAarG,aAAQiD,SAARjD,mBAAcmG;AAAAA,UAC3BG,SAAS;AAAA,QACX,CAAC;AAAA,MACH;AAAA,IACF;AACI,UAAAtG,aAAQiD,SAARjD,mBAAcgG,YAAW,gBAAgB;AACvC,WAAAhG,aAAQiD,SAARjD,mBAAciG,SAAS;AACnBzF,cAAA;AAAA,UACJ4F,OAAO;AAAA,UACPC,aAAa;AAAA,UACbE,OAAO;AAAA,YACLC,iBAAiB;AAAA,YACjBC,OAAO;AAAA,UACT;AAAA,QACF,CAAC;AACDpG,+BAAuB,IAAI;AAC3BF,sBAAc,EAAE;AAChBI,wBAAgB,KAAK;AACrBkC,sBAAc,KAAK;AAAA,MACV,aAAAzC,aAAQiD,SAARjD,mBAAciG,aAAY,OAAO;AAClCrD,gBAAAC,KAAI7C,aAAQiD,SAARjD,mBAAcmG,YAAY;AACtC5F,wBAAgB,KAAK;AACfC,cAAA;AAAA,UACJ4F,OAAO;AAAA,UACPC,cAAarG,aAAQiD,SAARjD,mBAAcmG;AAAAA,UAC3BG,SAAS;AAAA,QACX,CAAC;AAAA,MACH;AAAA,IACF;AACI,UAAAtG,aAAQiD,SAARjD,mBAAcgG,YAAW,2BAA2B;AAClD,WAAAhG,aAAQiD,SAARjD,mBAAciG,SAAS;AACnBzF,cAAA;AAAA,UACJ4F,OAAO;AAAA,UACPC,aAAa;AAAA,UACbE,OAAO;AAAA,YACLC,iBAAiB;AAAA,YACjBC,OAAO;AAAA,UACT;AAAA,QACF,CAAC;AACDpG,+BAAuB,IAAI;AAC3BF,sBAAc,EAAE;AAChBI,wBAAgB,KAAK;AACrBkC,sBAAc,KAAK;AAAA,MACV,aAAAzC,aAAQiD,SAARjD,mBAAciG,aAAY,OAAO;AAClCrD,gBAAAC,KAAI7C,aAAQiD,SAARjD,mBAAcmG,YAAY;AACtC5F,wBAAgB,KAAK;AACfC,cAAA;AAAA,UACJ4F,OAAO;AAAA,UACPC,cAAarG,aAAQiD,SAARjD,mBAAcmG;AAAAA,UAC3BG,SAAS;AAAA,QACX,CAAC;AAAA,MACH;AAAA,IACF;AACI,UAAAtG,aAAQiD,SAARjD,mBAAcgG,YAAW,kBAAkB;AACzC,WAAAhG,aAAQiD,SAARjD,mBAAciG,SAAS;AACnBzF,cAAA;AAAA,UACJ4F,OAAO;AAAA,UACPC,aAAa;AAAA,UACbE,OAAO;AAAA,YACLC,iBAAiB;AAAA,YACjBC,OAAO;AAAA,UACT;AAAA,QACF,CAAC;AACDpG,+BAAuB,IAAI;AAC3BF,sBAAc,EAAE;AAChBI,wBAAgB,KAAK;AACrBkC,sBAAc,KAAK;AAAA,MACV,aAAAzC,aAAQiD,SAARjD,mBAAciG,aAAY,OAAO;AAClCrD,gBAAAC,KAAI7C,aAAQiD,SAARjD,mBAAcmG,YAAY;AACtC5F,wBAAgB,KAAK;AACfC,cAAA;AAAA,UACJ4F,OAAO;AAAA,UACPC,cAAarG,aAAQiD,SAARjD,mBAAcmG;AAAAA,UAC3BG,SAAS;AAAA,QACX,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF,GAAG,CAACtG,QAAQiD,IAAI,CAAC;AAEjB,+CACG,OAAI;AAAA,IAAAyD,WAAU;AAAA,IACbC,UAACC,kCAAA,KAAA,OAAA;AAAA,MAAIF,WAAU;AAAA,MAGbC,UAAA,CAAAE,kCAAA,IAAC;QAAIH,WAAU;AAAA,QACbC,UAACC,kCAAA,KAAA,OAAA;AAAA,UAAIF,WAAU;AAAA,UACbC,UAAA,CAAAC,kCAAA,KAAC,OACC;AAAA,YAAAD,UAAA,CAACE,kCAAA,IAAA,MAAA;AAAA,cAAGH,WAAU;AAAA,cAA8CC,UAAM;AAAA,YAAA,CAAA,GACjEE,kCAAA,IAAA,KAAA;AAAA,cAAEH,WAAU;AAAA,cAAqBC,UAA2C;AAAA,YAAA,CAAA,CAAA;AAAA,UAC/E,CAAA,GACAC,kCAAA,KAAC,OAAI;AAAA,YAAAF,WAAU;AAAA,YACbC,UAAA,CAAAC,kCAAA,KAACE,QAAA;AAAA,cACCR,SAAQ;AAAA,cACRS,MAAK;AAAA,cACLC,SAASA,MAAMvE,cAAc,KAAK;AAAA,cAClCwE,WAAWjH,QAAQkH,UAAU,aAAalH,QAAQkH,UAAU,mBAAiBlH,aAAQiD,SAARjD,mBAAcgG,YAAW;AAAA,cACtGU,WAAU;AAAA,cAEVC,UAAA,CAAAE,kCAAA,IAACM,WAAU;AAAA,gBAAAT,WAAW,YAAY1G,QAAQkH,UAAU,aAAalH,QAAQkH,UAAU,mBAAiBlH,aAAQiD,SAARjD,mBAAcgG,YAAW,iBAAiB,iBAAiB,EAAE;AAAA,cAAI,CAAA,IACnKhG,QAAQkH,UAAU,aAAalH,QAAQkH,UAAU,mBAAiBlH,aAAQiD,SAARjD,mBAAcgG,YAAW,iBAAiB,kBAAkB,SAAA;AAAA,YAAA,CAClI,GACAY,kCAAA,KAAC,OAAI;AAAA,cAAAF,WAAU;AAAA,cACbC,UAAA,CAAAE,kCAAA,IAAC;gBAAIH,WAAW,wBAAwBrE,YAAY,+BAA+B,aAAa;AAAA,cAAI,CAAA,GAAE,iBACxFA,YAAY,OAAO,KAAA;AAAA,YACnC,CAAA,CAAA;AAAA,UACF,CAAA,CAAA;AAAA,QACF,CAAA;AAAA,MACF,CAAA,GAGCuE,kCAAA,KAAAQ,MAAA;AAAA,QAAKrD,OAAOjE;AAAAA,QAAWuH,eAAe1D;AAAAA,QACrCgD,UAAA,CAACC,kCAAA,KAAAU,UAAA;AAAA,UAASZ,WAAU;AAAA,UAClBC,UAAA,CAAAE,kCAAA,IAACU,aAAY;AAAA,YAAAxD,OAAM;AAAA,YAAO2C,WAAU;AAAA,YAAiBC,UAAc;AAAA,UAAA,CAAA,yCAClEY,aAAY;AAAA,YAAAxD,OAAM;AAAA,YAAM2C,WAAU;AAAA,YAAiBC,UAAa;AAAA,UAAA,CAAA,CAAA;AAAA,QACnE,CAAA,GAGAE,kCAAA,IAACW,MAAK;AAAA,UAAAd,WAAU;AAAA,UACdC,UAAAE,kCAAA,IAACY,aAAY;AAAA,YAAAf,WAAU;AAAA,YACrBC,UAAAC,kCAAA,KAAC,OAAI;AAAA,cAAAF,WAAU;AAAA,cAEbC,UAAA,CAACC,kCAAA,KAAA,OAAA;AAAA,gBAAIF,WAAU;AAAA,gBACbC,UAAA,CAAAE,kCAAA,IAAC,OAAI;AAAA,kBAAAH,WAAU;AAAA,kBACbC,UAAAC,kCAAA,KAACc,QAAO;AAAA,oBAAA3D,OAAOrD;AAAAA,oBAAY2G,eAAgBtD,WAA6BpD,cAAcoD,KAAK;AAAA,oBACzF4C,UAAA,CAAAE,kCAAA,IAACc,eACC;AAAA,sBAAAhB,UAAAE,kCAAA,IAACe,aAAY;AAAA,wBAAAC,aAAY;AAAA,sBAAY,CAAA;AAAA,oBACvC,CAAA,yCACCC,eACE;AAAA,sBAAAnB,UAAA9C,kBAAkBkE,IAAK1D,YACrBwC,kCAAAA,IAAAmB,YAAA;AAAA,wBAA8BjE,OAAOM,OAAON;AAAAA,wBAC1C4C,UAAOtC,OAAAP;AAAAA,sBAAA,GADOO,OAAON,KAExB,CACD;AAAA,oBACH,CAAA,CAAA;AAAA,kBACF,CAAA;AAAA,gBACF,CAAA,GAEA6C,kCAAA,KAAC,OAAI;AAAA,kBAAAF,WAAU;AAAA,kBACbC,UAAA,CAACE,kCAAA,IAAAoB,QAAA;AAAA,oBAAOvB,WAAU;AAAA,kBAA2E,CAAA,GAC7FG,kCAAA,IAACqB,OAAA;AAAA,oBACCL,aAAanH,eAAe,SAAS,gBAAiBA,eAAe,gBAAiB,qBAAqB;AAAA,oBAC3GqD,OAAOnD;AAAAA,oBACPuH,UAAWC,OAAMvH,cAAcuH,EAAEC,OAAOtE,KAAK;AAAA,oBAC7C2C,WAAU;AAAA,kBAAA,CACZ,CAAA;AAAA,gBACF,CAAA,GAECG,kCAAA,IAAA,OAAA;AAAA,kBAAIH,WAAU;AAAA,kBACbC,iDAAC2B,SACC;AAAA,oBAAA3B,UAAA,CAACE,kCAAA,IAAA0B,gBAAA;AAAA,sBAAeC,SAAO;AAAA,sBACrB7B,UAAAC,kCAAA,KAACE,QAAA;AAAA,wBACC2B,IAAG;AAAA,wBACHnC,SAAQ;AAAA,wBACRI,WAAWgC,GACT,8CACA,CAAC5H,cAAc,uBACjB;AAAA,wBAEA6F,UAAA,CAAAE,kCAAAA,IAAC8B,UAAa,EAAA,IACb7H,yCAAYE,QACXF,WAAWI,KAEN0F,kCAAAA,KAAAgC,kBAAAA,UAAA;AAAA,0BAAAjC,UAAA,CAAOkC,OAAA/H,WAAWE,MAAM,WAAW,GAAE,OAAI6H,OAAO/H,WAAWI,IAAI,WAAW,CAAA;AAAA,wBAC7E,CAAA,IAEA2H,OAAO/H,WAAWE,MAAM,WAAW,IAGrC6F,kCAAA,IAAC;0BAAKF,UAAW;AAAA,wBAAA,CAAA,CAAA;AAAA,sBAErB,CAAA;AAAA,oBACF,CAAA,GACCC,kCAAA,KAAAkC,gBAAA;AAAA,sBAAepC,WAAU;AAAA,sBAAaqC,OAAM;AAAA,sBAC3CpC,UAAA,CAAAE,kCAAA,IAACmC,YAAA;AAAA,wBACCC,cAAY;AAAA,wBACZC,UAAUvH;AAAAA,wBACVwH,MAAK;AAAA,wBACLC,UAAWC,WAAiC;AACtC,8BAAA,EAACA,+BAAOrI,MAAM;AACLY,uCAAA;AAAA,4BACXZ,MAAMqI,MAAMrI;AAAAA,4BACZE,IAAImI,MAAMnI,MAAM;AAAA,0BAClB,CAAC;AAAA,wBACH;AAAA,sBAAA,CACF,GACA2F,kCAAA,IAACyC,cAAa;AAAA,wBAAA5C,WAAU;AAAA,wBACtBC,UAAAE,kCAAA,IAACC,QAAA;AAAA,0BACCR,SAAQ;AAAA,0BACRI,WAAU;AAAA,0BACVM,SAASA,MAAMjG,cAAcY,SAAS;AAAA,0BACvCgF,UAAA;AAAA,wBAED,CAAA;AAAA,sBACF,CAAA,CAAA;AAAA,oBACF,CAAA,CAAA;AAAA,kBACF,CAAA;AAAA,gBACF,CAAA,CAAA;AAAA,cACF,CAAA,GAGAC,kCAAA,KAAC,OAAI;AAAA,gBAAAF,WAAU;AAAA,gBACbC,UAAA,CAACC,kCAAA,KAAA,OAAA;AAAA,kBAAIF,WAAU;AAAA,kBACbC,UAAA,CAACE,kCAAA,IAAA0C,OAAA;AAAA,oBAAM7C,WAAU;AAAA,oBAAsBC,UAAM;AAAA,kBAAA,CAAA,GAC7CC,kCAAA,KAACc;oBAAO3D,OAAO5C;AAAAA,oBAAckG,eAAgBtD,WAAuB3C,gBAAgB2C,KAAK;AAAA,oBACvF4C,UAAA,CAAAE,kCAAA,IAACc,eACC;AAAA,sBAAAhB,UAAAE,kCAAA,IAACe,aAAY;AAAA,wBAAAC,aAAY;AAAA,sBAAgB,CAAA;AAAA,oBAC3C,CAAA,yCACCC,eACE;AAAA,sBAAAnB,UAAA3C,cAAc+D,IAAK1D,YACjBwC,kCAAAA,IAAAmB,YAAA;AAAA,wBAA8BjE,OAAOM,OAAON;AAAAA,wBAC1C4C,UAAOtC,OAAAP;AAAAA,sBAAA,GADOO,OAAON,KAExB,CACD;AAAA,oBACH,CAAA,CAAA;AAAA,kBACF,CAAA,CAAA;AAAA,gBACF,CAAA,GAEA8C,kCAAA,IAAC,OAAI;AAAA,kBAAAH,WAAU;AAAA,kBACbC,UAAAC,kCAAA,KAACE,QAAA;AAAA,oBACCE,SAASA,MAAM;AACbrG,oCAAc,SAAS;AACvBE,oCAAc,EAAE;AAChBiB,4CAAsB,EAAE;AACxB,0BAAIhC,cAAc,QAAQ;AACViB,sCAAA;AAAA,0BAAEC,MAAU,oBAAAC;0BAAQC,IAAI,oBAAID,KAAK;AAAA,wBAAE,CAAC;AACrCW,qCAAA;AAAA,0BAAEZ,MAAU,oBAAAC;0BAAQC,IAAI,oBAAID,KAAK;AAAA,wBAAE,CAAC;AAAA,sBACnD,OAAO;AACLF,sCAAc,MAAS;AACvBa,qCAAa,MAAS;AAAA,sBACxB;AACAR,sCAAgB,EAAE;AAClBE,kCAAY,EAAE;AACdE,qCAAe,CAAC;AAAA,oBAClB;AAAA,oBACAkF,WAAU;AAAA,oBAEVC,UAAA,CAACE,kCAAA,IAAA2C,QAAA;AAAA,sBAAO9C,WAAU;AAAA,oBAAe,CAAA,GAAE,eAAA;AAAA,kBAErC,CAAA;AAAA,gBACF,CAAA,CAAA;AAAA,cACF,CAAA,CAAA;AAAA,YACF,CAAA;AAAA,UACF,CAAA;AAAA,QACF,CAAA,GAGAG,kCAAA,IAACW,MAAK;AAAA,UAAAd,WAAU;AAAA,UACdC,UAAAE,kCAAA,IAACY,aAAY;AAAA,YAAAf,WAAU;AAAA,YACrBC,UAAAC,kCAAA,KAAC,OAAI;AAAA,cAAAF,WAAU;AAAA,cACbC,UAAA,CAAAC,kCAAA,KAACc,QAAO;AAAA,gBAAA3D,OAAO1C,SAASoI,SAAY;AAAA,gBAAApC,eAAgBtD,WAAUzC,YAAYoI,OAAO3F,KAAK,CAAC;AAAA,gBACrF4C,UAAA,CAAAE,kCAAA,IAACc,eAAc;AAAA,kBAAAjB,WAAU;AAAA,kBACvBC,UAAAE,kCAAAA,IAACe,cAAY,CAAA;AAAA,gBACf,CAAA,0CACCE,eACC;AAAA,kBAAAnB,UAAA,CAACE,kCAAA,IAAAmB,YAAA;AAAA,oBAAWjE,OAAM;AAAA,oBAAK4C,UAAW;AAAA,kBAAA,CAAA,GACjCE,kCAAA,IAAAmB,YAAA;AAAA,oBAAWjE,OAAM;AAAA,oBAAK4C,UAAW;AAAA,kBAAA,CAAA,GACjCE,kCAAA,IAAAmB,YAAA;AAAA,oBAAWjE,OAAM;AAAA,oBAAM4C,UAAY;AAAA,kBAAA,CAAA,CAAA;AAAA,gBACtC,CAAA,CAAA;AAAA,cACF,CAAA,GACCC,kCAAA,KAAAc,QAAA;AAAA,gBAAO3D,OAAOxC,YAAYkI,SAAS;AAAA,gBAAGpC,eAAgBtD,WAAUvC,eAAekI,OAAO3F,KAAK,CAAC;AAAA,gBAC3F4C,UAAA,CAAAE,kCAAA,IAACc,eAAc;AAAA,kBAAAjB,WAAU;AAAA,kBACvBC,UAAAE,kCAAAA,IAACe,cAAY,CAAA;AAAA,gBACf,CAAA,GACAf,kCAAA,IAACiB;kBACEnB,UAAMgD,MAAA3I,KAAK;AAAA,oBAAE4I,QAAQC,KAAKC,KAAKrI,gBAAgBJ,QAAQ;AAAA,kBAAK,GAAA,CAAC0I,GAAGC,MAC/DpD,kCAAAA,KAACoB;oBAAmBjE,OAAOiG,EAAEP,SAAY;AAAA,oBAAA9C,UAAA,CAAA,SACjCqD,IAAI,CAAA;AAAA,kBADK,GAAAA,CAEjB,CACD;AAAA,gBACH,CAAA,CAAA;AAAA,cACF,CAAA,CAAA;AAAA,YACF,CAAA;AAAA,UACF,CAAA;AAAA,QACF,CAAA,GAGClK,cAAc,UAAW8G,kCAAAA,KAAA,OAAA;AAAA,UAAIF,WAAU;AAAA,UACtCC,UAAA,CAAAE,kCAAA,IAACW;YAAKd,WAAU;AAAA,YACdC,UAACC,kCAAA,KAAAa,aAAA;AAAA,cAAYf,WAAU;AAAA,cACrBC,UAAA,CAACE,kCAAA,IAAA,OAAA;AAAA,gBAAIH,WAAU;AAAA,gBAAmCC,UAAK;AAAA,cAAA,CAAA,GACtDE,kCAAA,IAAA,OAAA;AAAA,gBAAIH,WAAU;AAAA,gBAAiDC,gBAAM3B;AAAAA,cAAM,CAAA,CAAA;AAAA,YAC9E,CAAA;AAAA,UACF,CAAA,yCACCwC,MAAK;AAAA,YAAAd,WAAU;AAAA,YACdC,UAACC,kCAAA,KAAAa,aAAA;AAAA,cAAYf,WAAU;AAAA,cACrBC,UAAA,CAACE,kCAAA,IAAA,OAAA;AAAA,gBAAIH,WAAU;AAAA,gBAAmCC,UAAO;AAAA,cAAA,CAAA,GACxDE,kCAAA,IAAA,OAAA;AAAA,gBAAIH,WAAU;AAAA,gBAAmDC,gBAAM1B;AAAAA,cAAQ,CAAA,CAAA;AAAA,YAClF,CAAA;AAAA,UACF,CAAA,yCACCuC,MAAK;AAAA,YAAAd,WAAU;AAAA,YACdC,UAACC,kCAAA,KAAAa,aAAA;AAAA,cAAYf,WAAU;AAAA,cACrBC,UAAA,CAACE,kCAAA,IAAA,OAAA;AAAA,gBAAIH,WAAU;AAAA,gBAAmCC,UAAQ;AAAA,cAAA,CAAA,GACzDE,kCAAA,IAAA,OAAA;AAAA,gBAAIH,WAAU;AAAA,gBAAmDC,gBAAMrB;AAAAA,cAAS,CAAA,CAAA;AAAA,YACnF,CAAA;AAAA,UACF,CAAA,yCACCkC,MAAK;AAAA,YAAAd,WAAU;AAAA,YACdC,UAACC,kCAAA,KAAAa,aAAA;AAAA,cAAYf,WAAU;AAAA,cACrBC,UAAA,CAACE,kCAAA,IAAA,OAAA;AAAA,gBAAIH,WAAU;AAAA,gBAAmCC,UAAM;AAAA,cAAA,CAAA,GACvDE,kCAAA,IAAA,OAAA;AAAA,gBAAIH,WAAU;AAAA,gBAAmDC,gBAAMpB;AAAAA,cAAO,CAAA,CAAA;AAAA,YACjF,CAAA;AAAA,UACF,CAAA,yCACCiC,MAAK;AAAA,YAAAd,WAAU;AAAA,YACdC,UAACC,kCAAA,KAAAa,aAAA;AAAA,cAAYf,WAAU;AAAA,cACrBC,UAAA,CAACE,kCAAA,IAAA,OAAA;AAAA,gBAAIH,WAAU;AAAA,gBAAmCC,UAAQ;AAAA,cAAA,CAAA,GACzDE,kCAAA,IAAA,OAAA;AAAA,gBAAIH,WAAU;AAAA,gBAAmDC,gBAAMnB;AAAAA,cAAS,CAAA,CAAA;AAAA,YACnF,CAAA;AAAA,UACF,CAAA,yCACCgC,MAAK;AAAA,YAAAd,WAAU;AAAA,YACdC,UAACC,kCAAA,KAAAa,aAAA;AAAA,cAAYf,WAAU;AAAA,cACrBC,UAAA,CAACE,kCAAA,IAAA,OAAA;AAAA,gBAAIH,WAAU;AAAA,gBAAmCC,UAAU;AAAA,cAAA,CAAA,GAC3DE,kCAAA,IAAA,OAAA;AAAA,gBAAIH,WAAU;AAAA,gBAAmDC,gBAAMlB;AAAAA,cAAW,CAAA,CAAA;AAAA,YACrF,CAAA;AAAA,UACF,CAAA,yCACC+B,MAAK;AAAA,YAAAd,WAAU;AAAA,YACdC,UAACC,kCAAA,KAAAa,aAAA;AAAA,cAAYf,WAAU;AAAA,cACrBC,UAAA,CAACE,kCAAA,IAAA,OAAA;AAAA,gBAAIH,WAAU;AAAA,gBAAmCC,UAAS;AAAA,cAAA,CAAA,GAC1DE,kCAAA,IAAA,OAAA;AAAA,gBAAIH,WAAU;AAAA,gBAAkDC,gBAAMjB;AAAAA,cAAU,CAAA,CAAA;AAAA,YACnF,CAAA;AAAA,UACF,CAAA,yCACC8B,MAAK;AAAA,YAAAd,WAAU;AAAA,YACdC,UAACC,kCAAA,KAAAa,aAAA;AAAA,cAAYf,WAAU;AAAA,cACrBC,UAAA,CAACE,kCAAA,IAAA,OAAA;AAAA,gBAAIH,WAAU;AAAA,gBAAmCC,UAAS;AAAA,cAAA,CAAA,GAC1DE,kCAAA,IAAA,OAAA;AAAA,gBAAIH,WAAU;AAAA,gBAAgDC,gBAAMhB;AAAAA,cAAU,CAAA,CAAA;AAAA,YACjF,CAAA;AAAA,UACF,CAAA,CAAA;AAAA,QACF,CAAA,GAEAiB,kCAAA,KAACqD,aAAY;AAAA,UAAAlG,OAAM;AAAA,UAEjB4C,UAAA,CAACC,kCAAA,KAAA,OAAA;AAAA,YAAIF,WAAU;AAAA,YACbC,UAAA,CAAAE,kCAAA,IAAC;cAAIH,WAAU;AAAA,cACbC,UAACC,kCAAA,KAAA,MAAA;AAAA,gBAAGF,WAAU;AAAA,gBACZC,UAAA,CAACE,kCAAA,IAAA,OAAA;AAAA,kBAAIH,WAAU;AAAA,iBAAkD,GAAE,eAEnEG,kCAAA,IAACqD,OAAM;AAAA,kBAAA5D,SAAQ;AAAA,kBAAYI,WAAU;AAAA,kBAClCC,UAAAlF,iBAAiBsD,MAAMW,YAAYX,MAAMY,cAAc;AAAA,gBAC1D,CAAA,CAAA;AAAA,cACF,CAAA;AAAA,YACF,CAAA,IAEC1B,yCAAY2F,YAAW,IACtB/C,kCAAAA,IAACW,MACC;AAAA,cAAAb,UAAAE,kCAAA,IAACY,aAAY;AAAA,gBAAAf,WAAU;AAAA,gBACrBC,UAAAC,kCAAA,KAAC,OAAI;AAAA,kBAAAF,WAAU;AAAA,kBACbC,UAAA,CAACE,kCAAA,IAAAsD,OAAA;AAAA,oBAAMzD,WAAU;AAAA,kBAAoC,CAAA,GACpDG,kCAAA,IAAA,KAAA;AAAA,oBAAEH,WAAU;AAAA,oBAAsBC,UAAc;AAAA,kBAAA,CAAA,GAChDE,kCAAA,IAAA,KAAA;AAAA,oBAAEH,WAAU;AAAA,oBAAUC,UAAmD;AAAA,kBAAA,CAAA,CAAA;AAAA,gBAC5E,CAAA;AAAA,cACF,CAAA;AAAA,aACF,IAEAE,kCAAA,IAAC,OAAA;AAAA,cAECH,WAAU;AAAA,cACVH,OAAO;AAAA,gBACL6D,WAAW;AAAA,gBACXC,YAAY;AAAA,cACd;AAAA,cAEC1D,UAAY1C,yCAAA8D,IAAI,CAACzD,OAAOgG,UACvBzD,kCAAA,IAAC,OAAA;AAAA,gBAECH,WAAU;AAAA,gBACVH,OAAO;AAAA,kBACLgE,gBAAgB,GAAGD,QAAQ,EAAE;AAAA,kBAC7BE,mBAAmB;AAAA,kBACnBC,YAAY;AAAA,gBACd;AAAA,gBAEA9D,UAAAE,kCAAA,IAAC6D,WAAA;AAAA,kBACCpG;AAAAA,kBACAqG,eAAehL;AAAAA,kBACfiL,UAAUhF;AAAAA,gBACZ,CAAA;AAAA,iBAZK,GAAGtB,MAAMuG,YAAY,IAAI1I,YAAY,EAa5C;AAAA,YACD,GAvBI,eAAeA,YAAY,EAwBlC,CAAA;AAAA,UAEJ,CAAA,GAGAyE,kCAAA,KAAC,OAAI;AAAA,YAAAF,WAAU;AAAA,YACbC,UAAA,CAAAE,kCAAA,IAAC;cAAIH,WAAU;AAAA,cACbC,UAACC,kCAAA,KAAA,MAAA;AAAA,gBAAGF,WAAU;AAAA,gBACZC,UAAA,CAACE,kCAAA,IAAA,OAAA;AAAA,kBAAIH,WAAU;AAAA,iBAAmC,GAAE,oBAEpDG,kCAAA,IAACqD,OAAM;AAAA,kBAAA5D,SAAQ;AAAA,kBAAYI,WAAU;AAAA,kBAClCC,UAAM5B,MAAAW,YAAYX,MAAMY,aAAa;AAAA,gBACxC,CAAA,CAAA;AAAA,cACF,CAAA;AAAA,YACF,CAAA,IAECzB,mDAAiB0F,YAAW,IAC3B/C,kCAAAA,IAACW,MACC;AAAA,cAAAb,UAAAE,kCAAA,IAACY,aAAY;AAAA,gBAAAf,WAAU;AAAA,gBACrBC,UAAAC,kCAAA,KAAC,OAAI;AAAA,kBAAAF,WAAU;AAAA,kBACbC,UAAA,CAACE,kCAAA,IAAAiE,aAAA;AAAA,oBAAYpE,WAAU;AAAA,kBAAoC,CAAA,GAC1DG,kCAAA,IAAA,KAAA;AAAA,oBAAEH,WAAU;AAAA,oBAAsBC,UAAmB;AAAA,kBAAA,CAAA,GACrDE,kCAAA,IAAA,KAAA;AAAA,oBAAEH,WAAU;AAAA,oBAAUC,UAAsD;AAAA,kBAAA,CAAA,CAAA;AAAA,gBAC/E,CAAA;AAAA,cACF,CAAA;AAAA,YACF,CAAA,IAECE,kCAAA,IAAA,OAAA;AAAA,cAAIH,WAAU;AAAA,cACbC,UAAAE,kCAAA,IAAC,OAAA;AAAA,gBAECH,WAAU;AAAA,gBACVH,OAAO;AAAA,kBACL6D,WAAW;AAAA,kBACXC,YAAY;AAAA,gBACd;AAAA,gBAEC1D,UAAiBzC,mDAAA6D,IAAI,CAACzD,OAAOgG,UAC5BzD,kCAAA,IAAC,OAAA;AAAA,kBAECH,WAAU;AAAA,kBACVH,OAAO;AAAA,oBACLgE,gBAAgB,GAAGD,QAAQ,EAAE;AAAA,oBAC7BE,mBAAmB;AAAA,oBACnBC,YAAY;AAAA,kBACd;AAAA,kBAEA9D,gDAAC+D,WAAU;AAAA,oBAAApG;AAAAA,oBAAcqG,eAAehL;AAAAA,oBAAkBiL,UAAUhF;AAAAA,kBAAc,CAAA;AAAA,mBAR7E,GAAGtB,MAAMuG,YAAY,IAAI1I,YAAY,EAS5C;AAAA,iBAlBG,oBAAoBA,YAAY,EAoBvC;AAAA,YACF,CAAA,CAAA;AAAA,UAEJ,CAAA,CAAA;AAAA,QACF,CAAA,GAEC0E,kCAAA,IAAAoD,aAAA;AAAA,UAAYlG,OAAM;AAAA,UACjB4C,iDAACoE,OACC;AAAA,YAAApE,UAAA,CAACE,kCAAA,IAAAmE,aAAA;AAAA,cACCrE,iDAACsE,UACC;AAAA,gBAAAtE,UAAA,CAACE,kCAAA,IAAAqE,WAAA;AAAA,kBAAUxE,WAAU;AAAA,kBAAiBC,UAAQ;AAAA,gBAAA,CAAA,GAC7CE,kCAAA,IAAAqE,WAAA;AAAA,kBAAUxE,WAAU;AAAA,kBAAiBC,UAAQ;AAAA,gBAAA,CAAA,GAC7CE,kCAAA,IAAAqE,WAAA;AAAA,kBAAUxE,WAAU;AAAA,kBAAiBC,UAAM;AAAA,gBAAA,CAAA,GAC3CE,kCAAA,IAAAqE,WAAA;AAAA,kBAAUxE,WAAU;AAAA,kBAAiBC,UAAc;AAAA,gBAAA,CAAA,GACnDE,kCAAA,IAAAqE,WAAA;AAAA,kBAAUxE,WAAU;AAAA,kBAAiBC,UAAY;AAAA,gBAAA,CAAA,GACjDE,kCAAA,IAAAqE,WAAA;AAAA,kBAAUxE,WAAU;AAAA,kBAAiBC,UAAO;AAAA,gBAAA,CAAA,CAAA;AAAA,cAC/C,CAAA;AAAA,YACF,CAAA,GACAE,kCAAA,IAACsE;cACExE,WAAWpH,uCAAAqK,YAAW,IACpB/C,kCAAAA,IAAAoE,UAAA;AAAA,gBACCtE,gDAACyE,WAAU;AAAA,kBAAAC,SAAS;AAAA,kBAAG3E,WAAU;AAAA,kBAAmBC;gBAEpD,CAAA;AAAA,cACF,CAAA,IAEApH,uCAAWwI,IAAKzD,WACdsC,kCAAAA,KAACqE,UACC;AAAA,gBAAAtE,UAAA,CAACC,kCAAA,KAAAwE,WAAA;AAAA,kBAAU1E,WAAU;AAAA,kBACnBC,UAAA,CAACE,kCAAA,IAAA,KAAA;AAAA,oBAAGF,gBAAMkE;AAAAA,kBAAa,CAAA,GACvBhE,kCAAA,IAAC,KAAE;AAAA,oBAAAH,WAAU;AAAA,oBAAyBC,UAAA2E,MAAMhH,MAAMM,SAAS,EAAEiE,OAAO,oBAAoB;AAAA,kBAAE,CAAA,CAAA;AAAA,gBAC5F,CAAA,GACAjC,kCAAA,KAACwE,WAAU;AAAA,kBAAA1E,WAAU;AAAA,kBACnBC,UAAA,CAACE,kCAAA,IAAA,KAAA;AAAA,oBAAGF,gBAAM4E;AAAAA,kBAAU,CAAA,GACnB1E,kCAAA,IAAA,KAAA;AAAA,oBAAEH,WAAU;AAAA,oBAAyBC,gBAAM6E;AAAAA,kBAAU,CAAA,CAAA;AAAA,gBACxD,CAAA,GACA5E,kCAAA,KAACwE,WAAU;AAAA,kBAAA1E,WAAU;AAAA,kBAA8BC,UAAA,CAAA,MAAGrC,MAAMmH,sBAAsBC,eAAe,CAAA;AAAA,gBAAE,CAAA,yCAClGN,WAAU;AAAA,kBAAA1E,WAAU;AAAA,kBAAaC,UAAMrC,MAAAqH,mBAAmB,SAAS;AAAA,gBAAU,CAAA,GAC9E9E,kCAAA,IAACuE,WAAU;AAAA,kBAAA1E,WAAU;AAAA,kBACnBC,UAAAE,kCAAA,IAACqD,OAAA;AAAA,oBACC5D,SACEhC,MAAME,gBAAgB,cAClB,YACAF,MAAME,gBAAgB,cACpB,gBACA;AAAA,oBAGPmC,UAAMrC,MAAAE;AAAAA,kBACT,CAAA;AAAA,gBACF,CAAA,yCACC4G,WAAU;AAAA,kBAAA1E,WAAU;AAAA,kBACnBC,UAAAE,kCAAA,IAACC;oBAAOR,SAAQ;AAAA,oBAAUS,MAAK;AAAA,oBAAKC,SAASA,MAAMrH,iBAAiB2E,KAAK;AAAA,oBAAGqC;kBAE5E,CAAA;AAAA,gBACF,CAAA,CAAA;AAAA,cA5Ba,GAAArC,MAAMuG,YA6BrB;AAAA,YAGN,CAAA,CAAA;AAAA,UACF,CAAA;AAAA,QACF,CAAA,CAAA;AAAA,MACF,CAAA,GAGAhE,kCAAA,IAAC+E,mBAAkB;AAAA,QAAAtH,OAAO5E;AAAAA,QAAemM,SAASA,MAAMlM,iBAAiB,IAAI;AAAA,QAAGiL,UAAUhF;AAAAA,MAAc,CAAA,GAGxGiB,kCAAA,IAACiF,aAAA;AAAA,QACCxH,OAAOlE;AAAAA,QACPF;AAAAA,QACA2L,SAASA,MAAM;AACbxL,iCAAuB,IAAI;AAC3BF,wBAAc,EAAE;AAAA,QAClB;AAAA,QACAG;AAAAA,QACAyL,UAAUjG;AAAAA,MAAA,CACZ,CAAA;AAAA,IACF,CAAA;AAAA,EACF,CAAA;AAEJ;AAUO,SAAS4E,UAAU;AAAA,EAAEpG;AAAAA,EAAOqG;AAAAA,EAAeC;AAAS,GAAmB;AAC5E,QAAM,CAACoB,aAAaC,cAAc,IAAIxM,aAAAA,SAASwB,KAAKiL,KAAK;AAGzDnK,eAAAA,UAAU,MAAM;AACV,QAAA,CAAC,CAAC,aAAa,WAAW,EAAEwC,SAASD,MAAME,WAAW,GAAG;AACrD,YAAA2H,WAAWC,YAAY,MAAM;AAClBH,uBAAAhL,KAAKiL,KAAK;AAAA,SACxB,GAAI;AACA,aAAA,MAAMG,cAAcF,QAAQ;AAAA,IACrC;AAAA,EACF,GAAG,CAAC7H,MAAME,WAAW,CAAC;AAEhB,QAAA8H,UAAUC,oBAAoBjI,KAAK;AACnC,QAAAkI,WAAWC,2BAA2BnI,MAAMM,SAAS;AAC3D,QAAM8H,aAAaF,YAAY;AAG/BzK,eAAAA,UAAU,MAAM;AACd,QAAI2K,YAAY;AACR,YAAAC,QAAQ,IAAIC,MAAM,gBAAgB;AACxCD,YAAME,KAAK,EAAEC,MAAMlK,QAAQmK,IAAI;AAC/B,aAAO,MAAM;AACXJ,cAAMK,MAAM;AACZL,cAAMM,MAAM;AAAA,MACd;AAAA,IACF;AAAA,EACF,GAAG,CAACP,UAAU,CAAC;AAET,QAAAQ,mBAAmBA,CAACC,aAAqB/E,MAAwB;AACrEA,MAAEgF,gBAAgB;AAClBC,WAAOC,KAAK,OAAOH,WAAW,IAAI,OAAO;AAAA,EAC3C;AAEA,QAAMI,mBAAmBA,MAAM;AAC7B,UAAMC,UAAU,CAAC;AAEb,QAAAlJ,MAAME,gBAAgB,WAAW;AAC3BgJ,cAAAC,KACN5G,kCAAA,IAACC,QAAA;AAAA,QAECR,SAAQ;AAAA,QACRS,MAAK;AAAA,QACLC,SAAUoB,OAAM;AACdA,YAAEgF,gBAAgB;AAClBxC,mBAAStG,OAAO,cAAc;AAAA,QAChC;AAAA,QACAoC,WAAU;AAAA,QACXC,UAAA;AAAA,MAAA,GARK,QAUN,CACF;AAAA,IACF;AAEI,QAAA+G,uBAAuBpJ,KAAK,GAAG;AACzBkJ,cAAAC,KACN7G,kCAAA,KAACE,QAAA;AAAA,QAECR,SAAQ;AAAA,QACRS,MAAK;AAAA,QACLC,SAAUoB,OAAM;AACdA,YAAEgF,gBAAgB;AAClBxC,mBAAStG,OAAO,yBAAyB;AAAA,QAC3C;AAAA,QACAoC,WAAU;AAAA,QACXC,UAAA,CAAA,cACYrC,MAAME,gBAAgB,YAAY,aAAaF,MAAME,gBAAgB,aAAa,WAAWF,MAAME,gBAAgB,WAAW,aAAaF,MAAME,gBAAgB,aAAa,aAAa,cAAa,KAACqC,kCAAA,IAAC8G,YAAW;AAAA,UAAAjH,WAAU;AAAA,QAAU,CAAA,CAAA;AAAA,MAAA,GAThP,cAUN,CACF;AAAA,IACF;AAEI,QAAApC,MAAME,gBAAgB,cAAc;AAC9BgJ,cAAAC,KACN5G,kCAAA,IAACC,QAAA;AAAA,QAECR,SAAQ;AAAA,QACRS,MAAK;AAAA,QACLC,SAAUoB,OAAM;AACdA,YAAEgF,gBAAgB;AAClBxC,mBAAStG,OAAO,gBAAgB;AAAA,QAClC;AAAA,QACAoC,WAAU;AAAA,QACXC,UAAA;AAAA,MAAA,GARK,eAUN,CACF;AAAA,IACF;AAEO,WAAA6G;AAAAA,EACT;AAGE,SAAA3G,kCAAAA,IAACW,MAAA;AAAA,IACCd,WAAW,GAAG4F,QAAQ5F,SAAS;AAAA,IAC/BM,SAASA,MAAM2D,cAAcrG,KAAK;AAAA,IAClCsJ,cAAexF,OAAM;AACjBA,QAAAyF,cAActH,MAAMuH,qBAAqB;AAAA,IAC7C;AAAA,IACAC,cAAe3F,OAAM;AACjBA,QAAAyF,cAActH,MAAMuH,qBAAqB;AAAA,IAC7C;AAAA,IAEAnH,UAAAC,kCAAA,KAACa,aAAY;AAAA,MAAAf,WAAU;AAAA,MAErBC,UAAA,CAACC,kCAAA,KAAA,OAAA;AAAA,QAAIF,WAAU;AAAA,QACbC,UAAA,CAACC,kCAAA,KAAA,OAAA;AAAA,UAAIF,WAAU;AAAA,UACbC,UAAA,CAACC,kCAAA,KAAA,OAAA;AAAA,YAAIF,WAAU;AAAA,YAAqCC,UAAA,CAAA,KAAErC,MAAMuG,YAAA;AAAA,WAAa,GACxE6B,cACE7F,kCAAA,IAAAqD,OAAA;AAAA,YAAM5D,SAAQ;AAAA,YAAYI,WAAU;AAAA,YAA2IC,UAEhL;AAAA,UAAA,CAAA,GAEDrC,MAAM0J,kBAAkB1J,MAAM0J,eAAepE,SAAS,KACrDhD,kCAAA,KAACsD,OAAM;AAAA,YAAA5D,SAAQ;AAAA,YAAcI,WAAU;AAAA,YACrCC,UAAA,CAACE,kCAAA,IAAAoH,uBAAA;AAAA,cAA0BvH,WAAU;AAAA,YAAU,CAAA,GAC9CpC,MAAM0J,eAAepE,MAAA;AAAA,UACxB,CAAA,CAAA;AAAA,QAEJ,CAAA,GACAhD,kCAAA,KAAC,OAAI;AAAA,UAAAF,WAAU;AAAA,UACbC,UAAA,CAACE,kCAAA,IAAAsD,OAAA;AAAA,YAAMzD,WAAU;AAAA,UAAwB,CAAA,GACxC,CAAC,WAAW,EAAEnC,SAASD,MAAME,WAAW,IACrC0J,kBAAkBzB,2BAA2BnI,MAAMM,WAAWN,MAAM6J,iBAAiB7J,MAAM8J,SAAS,CAAC,IACrG,CAAC,WAAW,EAAE7J,SAASD,MAAME,WAAW,IAAI0J,kBAAkBzB,2BAA2BnI,MAAMM,WAAWN,MAAM6J,iBAAiB7J,MAAM8J,SAAS,CAAC,IAAIF,kBAAkB1B,QAAQ,CAAA;AAAA,QACrL,CAAA,CAAA;AAAA,MACF,CAAA,GAGA5F,kCAAA,KAAC,OAAI;AAAA,QAAAF,WAAU;AAAA,QACbC,UAAA,CAACC,kCAAA,KAAA,OAAA;AAAA,UAAIF,WAAU;AAAA,UACbC,UAAA,CAACE,kCAAA,IAAAwH,OAAA;AAAA,YAAM3H,WAAU;AAAA,UAAoD,CAAA,GACrEG,kCAAA,IAAC,QAAK;AAAA,YAAAH,WAAU;AAAA,YACbC,UAAA2E,MAAMhH,MAAMM,SAAS,EAAEiE,OAAO,oBAAoB;AAAA,UACrD,CAAA,CAAA;AAAA,QACF,CAAA,GACAjC,kCAAA,KAAC,OAAI;AAAA,UAAAF,WAAU;AAAA,UACbC,UAAA,CAACE,kCAAA,IAAAyH,MAAA;AAAA,YAAK5H,WAAU;AAAA,UAAoD,CAAA,GACnEG,kCAAA,IAAA,QAAA;AAAA,YAAKH,WAAU;AAAA,YAA6CC,gBAAM4E;AAAAA,UAAU,CAAA,GAC7E1E,kCAAA,IAAC,UAAA;AAAA,YACCG,SAAUoB,OAAM8E,iBAAiB5I,MAAMiK,aAAanG,CAAC;AAAA,YACrD1B,WAAU;AAAA,YAEVC,UAAAE,kCAAA,IAAC2H,OAAM;AAAA,cAAA9H,WAAU;AAAA,YAAwB,CAAA;AAAA,UAAA,CAC3C,CAAA;AAAA,QACF,CAAA,GACAE,kCAAA,KAAC,OAAI;AAAA,UAAAF,WAAU;AAAA,UACbC,UAAA,CAACE,kCAAA,IAAA4H,QAAA;AAAA,YAAO/H,WAAU;AAAA,UAA2D,CAAA,GAC5EG,kCAAA,IAAA,QAAA;AAAA,YAAKH,WAAU;AAAA,YAAgBC,gBAAM+H;AAAAA,UAAS,CAAA,CAAA;AAAA,QACjD,CAAA,CAAA;AAAA,MACF,CAAA,yCAGC,OAAI;AAAA,QAAAhI,WAAU;AAAA,QACbC,UAACC,kCAAA,KAAA,OAAA;AAAA,UAAIF,WAAU;AAAA,UACbC,UAAA,CAACE,kCAAA,IAAA8H,OAAA;AAAA,YAAMjI,WAAU;AAAA,UAAoD,CAAA,GACpEG,kCAAA,IAAA,QAAA;AAAA,YAAKH,WAAU;AAAA,YAA6CC,gBAAMiI;AAAAA,UAAW,CAAA,GAC9E/H,kCAAA,IAAC,UAAA;AAAA,YACCG,SAAUoB,OAAM8E,iBAAiB5I,MAAMuK,cAAczG,CAAC;AAAA,YACtD1B,WAAU;AAAA,YAEVC,UAAAE,kCAAA,IAAC2H,OAAM;AAAA,cAAA9H,WAAU;AAAA,YAAwB,CAAA;AAAA,UAAA,CAC3C,yCACCwD,OAAM;AAAA,YAAA5D,SAAQ;AAAA,YAAUI,WAAU;AAAA,YAChCC,gBAAMmI;AAAAA,UACT,CAAA,GACExK,MAAMyK,QAAQ,UAAUzK,MAAMyK,QAAQ,UACrClI,kCAAA,IAAAqD,OAAA;AAAA,YAAM5D,SAAQ;AAAA,YAAUI,WAAU;AAAA,YAChCC,gBAAMoI;AAAAA,UACT,CAAA,CAAA;AAAA,QAEJ,CAAA;AAAA,MACF,CAAA,GAGCzK,MAAMwK,qBAAqB,SAASxK,MAAM0K,mBACzCnI,kCAAA,IAAC,OAAI;AAAA,QAAAH,WAAU;AAAA,QACbC,UAAAC,kCAAA,KAAC,OAAI;AAAA,UAAAF,WAAU;AAAA,UACbC,UAAA,CAACE,kCAAA,IAAAoI,OAAA;AAAA,YAAMvI,WAAU;AAAA,UAAoD,CAAA,yCACpE,QAAK;AAAA,YAAAA,WAAU;AAAA,YAA+BC,UAAArC,MAAM0K,gBAAgBE;AAAAA,UAAU,CAAA,GAC9E5K,MAAM0K,gBAAgBG,cACrBtI,kCAAAA,IAAC,UAAA;AAAA,YACCG,SAAUoB;;AAAM8E,wCAAiB5I,WAAM0K,oBAAN1K,mBAAuB6K,WAAW1F,eAAc,IAAIrB,CAAC;AAAA;AAAA,YACtF1B,WAAU;AAAA,YAEVC,UAAAE,kCAAA,IAAC2H,OAAM;AAAA,cAAA9H,WAAU;AAAA,YAAwB,CAAA;AAAA,UAC3C,CAAA,GAEDpC,MAAM8K,aACLvI,kCAAAA,IAACqD,OAAM;AAAA,YAAA5D,SAAQ;AAAA,YAAUI,WAAU;AAAA,YAChCC,UAAA0I,6BAA6B/K,MAAM8K,SAAS;AAAA,UAC/C,CAAA,CAAA;AAAA,QAEJ,CAAA;AAAA,MACF,CAAA,GAIFxI,kCAAA,KAAC,OAAI;AAAA,QAAAF,WAAU;AAAA,QACbC,UAAA,CAACC,kCAAA,KAAA,OAAA;AAAA,UAAIF,WAAU;AAAA,UACZC,UAAA,CAAMrC,MAAAgL,YAAW,gBAAahL,MAAMmH,qBAAA;AAAA,QACvC,CAAA,GACCnH,MAAMiL,gBACJ3I,kCAAAA,KAAA,OAAA;AAAA,UAAIF,WAAU;AAAA,UACZC,UAAA,CAAMrC,MAAAiL,aAAaC,MAAM,GAAG,CAAC,EAAEzH,IAAI,CAAC0H,MAAMC,QACzC9I,kCAAAA,KAAC,QACE;AAAA,YAAAD,UAAA,CAAK8I,KAAAE,UAAS,MAAGF,KAAKG,KACtBF,MAAM7F,KAAKgG,IAAIvL,MAAMiL,aAAc3F,QAAQ,CAAC,IAAI,IAAI,OAAO,EAAA;AAAA,UAFnD,GAAA6F,KAAKK,OAGhB,CACD,GACAxL,MAAMiL,aAAa3F,SAAS,KAAK,KAAA;AAAA,QACpC,CAAA,CAAA;AAAA,MAEJ,CAAA,GAGAhD,kCAAA,KAAC,OAAI;AAAA,QAAAF,WAAU;AAAA,QACbC,UAAA,CAACC,kCAAA,KAAA,OAAA;AAAA,UAAIF,WAAU;AAAA,UACbC,UAAA,CAAAE,kCAAA,IAACqD,OAAA;AAAA,YACC5D,SACEhC,MAAME,gBAAgB,cAClB,YACAF,MAAME,gBAAgB,cACpB,gBACA;AAAA,YAERkC,WAAU;AAAA,YAETC,UAAAoJ,qBAAqBzL,MAAME,WAAW;AAAA,UAAA,CACzC,GACAoC,kCAAA,KAACE,QAAA;AAAA,YACCR,SAAQ;AAAA,YACRS,MAAK;AAAA,YACLC,SAAUoB,OAAM;AACdA,gBAAEgF,gBAAgB;AAClBzC,4BAAcrG,KAAK;AAAA,YACrB;AAAA,YACAoC,WAAU;AAAA,YAEVC,UAAA,CAACE,kCAAA,IAAAmJ,KAAA;AAAA,cAAItJ,WAAU;AAAA,YAAwB,CAAA,GAAE,SAAA;AAAA,UAAA,CAE3C,CAAA;AAAA,QACF,CAAA,GAGC6G,iBAAA,EAAmB3D,SAAS,2CAAM,OAAI;AAAA,UAAAlD,WAAU;AAAA,UAAwBC,UAAA4G,iBAAA;AAAA,QAAmB,CAAA,CAAA;AAAA,MAC9F,CAAA,GAGCjB,QAAQ2D,cACNpJ,kCAAAA,IAAA,OAAA;AAAA,QAAIH,WAAU;AAAA,QACbC,UAAAC,kCAAA,KAAC,OAAI;AAAA,UAAAF,WAAU;AAAA,UACbC,UAAA,CAACE,kCAAA,IAAAiE,aAAA;AAAA,YAAYpE,WAAU;AAAA,UAA8C,CAAA,GACpEG,kCAAA,IAAA,QAAA;AAAA,YAAKH,WAAU;AAAA,YAA+BC,kBAAQsJ;AAAAA,UAAW,CAAA,CAAA;AAAA,QACpE,CAAA;AAAA,MACF,CAAA,CAAA;AAAA,IAEJ,CAAA;AAAA,EAAA,CACF;AAEJ;AAUO,SAASrE,kBAAkB;AAAA,EAAEtH;AAAAA,EAAOuH;AAAAA,EAASjB;AAAS,GAA2B;;AAClF,MAAA,CAACtG,MAAc,QAAA;AAEb,QAAA4I,mBAAoBC,iBAAwB;AAChDE,WAAOC,KAAK,OAAOH,WAAW,IAAI,OAAO;AAAA,EAC3C;AAEM,QAAAX,WAAWC,2BAA2BnI,MAAMM,SAAS;AAGzD,SAAAiC,kCAAAA,IAACqJ,QAAO;AAAA,IAAA5C,MAAM,CAAC,CAAChJ;AAAAA,IAAO6L,cAActE;AAAAA,IACnClF,UAAAC,kCAAA,KAACwJ,eAAc;AAAA,MAAA1J,WAAU;AAAA,MACvBC,UAAA,CAAAE,kCAAA,IAACwJ,cACC;AAAA,QAAA1J,UAAAC,kCAAA,KAAC0J,aAAY;AAAA,UAAA5J,WAAU;AAAA,UAA6CC,UAAA,CAAA,qBAChDrC,MAAMuG,cACxBhE,kCAAAA,IAACqD,OAAA;AAAA,YACC5D,SACEhC,MAAME,gBAAgB,cAClB,YACAF,MAAME,gBAAgB,cACpB,gBACA;AAAA,YAERkC,WAAU;AAAA,YAETC,UAAAoJ,qBAAqBzL,MAAME,WAAW;AAAA,UAAA,CACzC,CAAA;AAAA,QACF,CAAA;AAAA,MACF,CAAA,GAEAoC,kCAAA,KAAC,OAAI;AAAA,QAAAF,WAAU;AAAA,QAEbC,UAAA,CAAAC,kCAAA,KAAC,OACC;AAAA,UAAAD,UAAA,CAACC,kCAAA,KAAA,MAAA;AAAA,YAAGF,WAAU;AAAA,YACZC,UAAA,CAACE,kCAAA,IAAAsD,OAAA;AAAA,cAAMzD,WAAU;AAAA,YAAU,CAAA,GAAE,gBAAA;AAAA,UAE/B,CAAA,GACAE,kCAAA,KAAC,OAAI;AAAA,YAAAF,WAAU;AAAA,YACbC,UAAA,CAACC,kCAAA,KAAA,OAAA;AAAA,cAAIF,WAAU;AAAA,cACbC,UAAA,CAAAE,kCAAA,IAAC;gBAAKF,UAAa;AAAA,cAAA,CAAA,GACnBE,kCAAA,IAAC;gBAAMF,UAAM2E,MAAAhH,MAAMM,SAAS,EAAEiE,OAAO,8BAA8B;AAAA,cAAE,CAAA,CAAA;AAAA,YACvE,CAAA,GACCvE,MAAMiM,gBACJ3J,kCAAAA,KAAA,OAAA;AAAA,cAAIF,WAAU;AAAA,cACbC,UAAA,CAAAE,kCAAA,IAAC;gBAAKF,UAAS;AAAA,cAAA,CAAA,GACfE,kCAAA,IAAC;gBAAMF,UAAM2E,MAAAhH,MAAMiM,YAAY,EAAE1H,OAAO,8BAA8B;AAAA,cAAE,CAAA,CAAA;AAAA,YAC1E,CAAA,GAEDvE,MAAMkM,cACJ5J,kCAAAA,KAAA,OAAA;AAAA,cAAIF,WAAU;AAAA,cACbC,UAAA,CAAAE,kCAAA,IAAC;gBAAKF,UAAO;AAAA,cAAA,CAAA,GACbE,kCAAA,IAAC;gBAAMF,UAAM2E,MAAAhH,MAAMkM,UAAU,EAAE3H,OAAO,8BAA8B;AAAA,cAAE,CAAA,CAAA;AAAA,YACxE,CAAA,GAEDvE,MAAMmM,gBACJ7J,kCAAAA,KAAA,OAAA;AAAA,cAAIF,WAAU;AAAA,cACbC,UAAA,CAAAE,kCAAA,IAAC;gBAAKF,UAAS;AAAA,cAAA,CAAA,GACfE,kCAAA,IAAC;gBAAMF,UAAM2E,MAAAhH,MAAMmM,YAAY,EAAE5H,OAAO,8BAA8B;AAAA,cAAE,CAAA,CAAA;AAAA,YAC1E,CAAA,GAEDvE,MAAMoM,gBACJ9J,kCAAAA,KAAA,OAAA;AAAA,cAAIF,WAAU;AAAA,cACbC,UAAA,CAAAE,kCAAA,IAAC;gBAAKF,UAAU;AAAA,cAAA,CAAA,GAChBE,kCAAA,IAAC;gBAAMF,UAAM2E,MAAAhH,MAAMoM,YAAY,EAAE7H,OAAO,8BAA8B;AAAA,cAAE,CAAA,CAAA;AAAA,YAC1E,CAAA,GAEDvE,MAAMqM,qBACJ/J,kCAAAA,KAAA,OAAA;AAAA,cAAIF,WAAU;AAAA,cACbC,UAAA,CAAAE,kCAAA,IAAC;gBAAKF,UAAW;AAAA,cAAA,CAAA,GACjBE,kCAAA,IAAC;gBAAMF,UAAM2E,MAAAhH,MAAMqM,iBAAiB,EAAE9H,OAAO,8BAA8B;AAAA,cAAE,CAAA,CAAA;AAAA,YAC/E,CAAA,GAEDvE,MAAM6J,iBACJvH,kCAAAA,KAAA,OAAA;AAAA,cAAIF,WAAU;AAAA,cACbC,UAAA,CAAAE,kCAAA,IAAC;gBAAKF,UAAU;AAAA,cAAA,CAAA,GAChBE,kCAAA,IAAC;gBAAMF,UAAM2E,MAAAhH,MAAM6J,aAAa,EAAEtF,OAAO,8BAA8B;AAAA,cAAE,CAAA,CAAA;AAAA,YAC3E,CAAA,GAEFjC,kCAAA,KAAC,OAAI;AAAA,cAAAF,WAAU;AAAA,cACbC,UAAA,CAAAE,kCAAA,IAAC;gBAAKF,UAAW;AAAA,cAAA,CAAA,GACjBE,kCAAA,IAAC;gBACEF,UAAC,CAAA,aAAa,WAAW,EAAEpC,SAASD,MAAME,WAAW,IAClD0J,kBACAzB,2BAA2BnI,MAAMM,WAAWN,MAAM6J,iBAAiB7J,MAAM8J,SAAS,CACpF,IACEF,kBAAkB1B,QAAQ;AAAA,cAChC,CAAA,CAAA;AAAA,YACF,CAAA,CAAA;AAAA,UACF,CAAA,CAAA;AAAA,QACF,CAAA,yCAECoE,WAAU,CAAA,CAAA,GAGXhK,kCAAAA,KAAC,OAAI;AAAA,UAAAF,WAAU;AAAA,UACbC,UAAA,CAAAC,kCAAA,KAAC,OACC;AAAA,YAAAD,UAAA,CAACC,kCAAA,KAAA,MAAA;AAAA,cAAGF,WAAU;AAAA,cACZC,UAAA,CAACE,kCAAA,IAAAgK,SAAA;AAAA,gBAAQnK,WAAU;AAAA,cAAU,CAAA,GAAE,mBAAA;AAAA,YAEjC,CAAA,GACAE,kCAAA,KAAC,OAAI;AAAA,cAAAF,WAAU;AAAA,cACbC,UAAA,CAAAC,kCAAA,KAAC,OACC;AAAA,gBAAAD,UAAA,CAAAE,kCAAA,IAAC;kBAAOF,UAAS;AAAA,gBAAA,CAAA,GAAS,MAAGrC,MAAMuG,YAAA;AAAA,cACrC,CAAA,0CACC,OACC;AAAA,gBAAAlE,UAAA,CAAAE,kCAAA,IAAC;kBAAOF,UAAQ;AAAA,gBAAA,CAAA,GAAS,KAAErC,MAAMwM,WAAA;AAAA,cACnC,CAAA,0CACC,OACC;AAAA,gBAAAnK,UAAA,CAAAE,kCAAA,IAAC;kBAAOF,UAAc;AAAA,gBAAA,CAAA,GAAS,KAAErC,MAAMyM,YAAA;AAAA,cACzC,CAAA,0CACC,OACC;AAAA,gBAAApK,UAAA,CAAAE,kCAAA,IAAC;kBAAOF,UAAmB;AAAA,gBAAA,CAAA,GAAS,KAAErC,MAAMwK,gBAAA;AAAA,cAC9C,CAAA,GACCxK,MAAMyK,QAAQ,UAAUzK,MAAMyK,QAAQ,iDACpC,OACC;AAAA,gBAAApI,UAAA,CAAAE,kCAAA,IAAC;kBAAOF,UAAI;AAAA,gBAAA,CAAA,GAAS,KAAErC,MAAMyK,GAAA;AAAA,cAC/B,CAAA,GAEDzK,MAAM0M,eACLpK,kCAAAA,KAAC,OACC;AAAA,gBAAAD,UAAA,CAAAE,kCAAA,IAAC;kBAAOF,UAAa;AAAA,gBAAA,CAAA,GAAS,KAAErC,MAAM0M,WAAA;AAAA,cACxC,CAAA,CAAA;AAAA,YAEJ,CAAA,CAAA;AAAA,UACF,CAAA,0CAEC,OACC;AAAA,YAAArK,UAAA,CAACC,kCAAA,KAAA,MAAA;AAAA,cAAGF,WAAU;AAAA,cACZC,UAAA,CAACE,kCAAA,IAAAyH,MAAA;AAAA,gBAAK5H,WAAU;AAAA,cAAU,CAAA,GAAE,sBAAA;AAAA,YAE9B,CAAA,GACAE,kCAAA,KAAC,OAAI;AAAA,cAAAF,WAAU;AAAA,cACbC,UAAA,CAAAC,kCAAA,KAAC,OACC;AAAA,gBAAAD,UAAA,CAAAE,kCAAA,IAAC;kBAAOF,UAAK;AAAA,gBAAA,CAAA,GAAS,KAAErC,MAAMiH,SAAA;AAAA,cAChC,CAAA,GACA3E,kCAAA,KAAC,OAAI;AAAA,gBAAAF,WAAU;AAAA,gBACbC,UAAA,CAAAE,kCAAA,IAAC;kBAAOF,UAAM;AAAA,gBAAA,CAAA,GACdE,kCAAA,IAAC,QAAM;AAAA,kBAAAF,UAAArC,MAAMiK;AAAAA,gBAAY,CAAA,GACzB1H,kCAAA,IAACC,QAAA;AAAA,kBACCC,MAAK;AAAA,kBACLT,SAAQ;AAAA,kBACRU,SAASA,MAAMkG,iBAAiB5I,MAAMiK,WAAW;AAAA,kBACjD7H,WAAU;AAAA,kBAEVC,UAAAE,kCAAA,IAAC2H,OAAM;AAAA,oBAAA9H,WAAU;AAAA,kBAAU,CAAA;AAAA,gBAAA,CAC7B,CAAA;AAAA,cACF,CAAA,0CACC,OACC;AAAA,gBAAAC,UAAA,CAAAE,kCAAA,IAAC;kBAAOF,UAAQ;AAAA,gBAAA,CAAA,GAAS,KAAErC,MAAMoK,QAAA;AAAA,cACnC,CAAA,0CACC,OACC;AAAA,gBAAA/H,UAAA,CAAAE,kCAAA,IAAC;kBAAOF,UAAK;AAAA,gBAAA,CAAA,GAAS,KAAErC,MAAMkH,SAAA;AAAA,cAChC,CAAA,CAAA;AAAA,YACF,CAAA,CAAA;AAAA,UACF,CAAA,CAAA;AAAA,QACF,CAAA,yCAECoF,WAAU,CAAA,CAAA,0CAGV,OACC;AAAA,UAAAjK,UAAA,CAACC,kCAAA,KAAA,MAAA;AAAA,YAAGF,WAAU;AAAA,YACZC,UAAA,CAACE,kCAAA,IAAA8H,OAAA;AAAA,cAAMjI,WAAU;AAAA,YAAU,CAAA,GAAE,wBAAA;AAAA,UAE/B,CAAA,GACAE,kCAAA,KAAC,OAAI;AAAA,YAAAF,WAAU;AAAA,YACbC,UAAA,CAAAC,kCAAA,KAAC,OACC;AAAA,cAAAD,UAAA,CAAAC,kCAAA,KAAC,OACC;AAAA,gBAAAD,UAAA,CAAAE,kCAAA,IAAC;kBAAOF,UAAK;AAAA,gBAAA,CAAA,GAAS,KAAErC,MAAMsK,UAAA;AAAA,cAChC,CAAA,GACAhI,kCAAA,KAAC,OAAI;AAAA,gBAAAF,WAAU;AAAA,gBACbC,UAAA,CAAAE,kCAAA,IAAC;kBAAOF,UAAM;AAAA,gBAAA,CAAA,GACdE,kCAAA,IAAC,QAAM;AAAA,kBAAAF,UAAArC,MAAMuK;AAAAA,gBAAa,CAAA,GAC1BhI,kCAAA,IAACC,QAAA;AAAA,kBACCC,MAAK;AAAA,kBACLT,SAAQ;AAAA,kBACRU,SAASA,MAAMkG,iBAAiB5I,MAAMuK,YAAY;AAAA,kBAClDnI,WAAU;AAAA,kBAEVC,UAAAE,kCAAA,IAAC2H,OAAM;AAAA,oBAAA9H,WAAU;AAAA,kBAAU,CAAA;AAAA,gBAAA,CAC7B,CAAA;AAAA,cACF,CAAA,CAAA;AAAA,YACF,CAAA,0CACC,OACE;AAAA,cAAAC,UAAA,CAAMrC,MAAA2M,oDACJ,OACC;AAAA,gBAAAtK,UAAA,CAAAE,kCAAA,IAAC;kBAAOF,UAAM;AAAA,gBAAA,CAAA,GAAS,KAAErC,MAAM2M,SAAA;AAAA,cACjC,CAAA,GAED3M,MAAM4M,iBACLtK,kCAAAA,KAAC,OACC;AAAA,gBAAAD,UAAA,CAAAE,kCAAA,IAAC;kBAAOF,UAAQ;AAAA,gBAAA,CAAA,GAAS,KAAErC,MAAM4M,aAAA;AAAA,cACnC,CAAA,CAAA;AAAA,YAEJ,CAAA,CAAA;AAAA,UACF,CAAA,CAAA;AAAA,QACF,CAAA,GAGC5M,MAAMwK,qBAAqB,WAASxK,WAAM0K,oBAAN1K,mBAAuB4K,cAExDtI,kCAAA,KAAAgC,4BAAA;AAAA,UAAAjC,UAAA,CAAAE,kCAAAA,IAAC+J,WAAU,CAAA,CAAA,0CACV,OACC;AAAA,YAAAjK,UAAA,CAACC,kCAAA,KAAA,MAAA;AAAA,cAAGF,WAAU;AAAA,cACZC,UAAA,CAACE,kCAAA,IAAAoI,OAAA;AAAA,gBAAMvI,WAAU;AAAA,cAAU,CAAA,GAAE,kBAAA;AAAA,YAE/B,CAAA,GACAE,kCAAA,KAAC,OAAI;AAAA,cAAAF,WAAU;AAAA,cACbC,UAAA,CAAAC,kCAAA,KAAC,OACC;AAAA,gBAAAD,UAAA,CAAAE,kCAAA,IAAC;kBAAOF,UAAK;AAAA,gBAAA,CAAA,GAAS,KAAErC,MAAM0K,gBAAgBE,SAAA;AAAA,cAChD,CAAA,GACC5K,MAAM0K,gBAAgBG,cACpBvI,kCAAAA,KAAA,OAAA;AAAA,gBAAIF,WAAU;AAAA,gBACbC,UAAA,CAAAE,kCAAA,IAAC;kBAAOF,UAAM;AAAA,gBAAA,CAAA,GACbE,kCAAA,IAAA,QAAA;AAAA,kBAAMF,UAAMrC,MAAA0K,gBAAgBG;AAAAA,gBAAW,CAAA,GACxCtI,kCAAA,IAACC,QAAA;AAAA,kBACCC,MAAK;AAAA,kBACLT,SAAQ;AAAA,kBACRU,SAASA,MAAMkG,iBAAiB5I,MAAM0K,gBAAiBG,WAAW1F,UAAU;AAAA,kBAC5E/C,WAAU;AAAA,kBAEVC,UAAAE,kCAAA,IAAC2H,OAAM;AAAA,oBAAA9H,WAAU;AAAA,kBAAU,CAAA;AAAA,gBAAA,CAC7B,CAAA;AAAA,cACF,CAAA,GAEDpC,MAAM8K,aACLxI,kCAAAA,KAAC,OACC;AAAA,gBAAAD,UAAA,CAAAE,kCAAA,IAAC;kBAAOF,UAAiB;AAAA,gBAAA,CAAA,yCACxBuD,OAAM;AAAA,kBAAAxD,WAAU;AAAA,kBAAQC,UAA6B0I,6BAAA/K,MAAM8K,SAAS;AAAA,gBAAE,CAAA,CAAA;AAAA,cACzE,CAAA,GAED9K,MAAM0K,gBAAgBmC,eACrBvK,kCAAAA,KAAC,OACC;AAAA,gBAAAD,UAAA,CAAAE,kCAAA,IAAC;kBAAOF,UAAS;AAAA,gBAAA,CAAA,GACjBE,kCAAA,IAAC,KAAA;AAAA,kBACCuK,MAAM9M,MAAM0K,gBAAgBmC;AAAAA,kBAC5B9I,QAAO;AAAA,kBACPgJ,KAAI;AAAA,kBACJ3K,WAAU;AAAA,kBACXC,UAAA;AAAA,gBAAA,CAED,CAAA;AAAA,cACF,CAAA,CAAA;AAAA,YAEJ,CAAA,CAAA;AAAA,UACF,CAAA,CAAA;AAAA,QACF,CAAA,GAIDrC,MAAMiL,gBAAgBjL,MAAMiL,aAAa3F,SAAS,KAE/ChD,kCAAA,KAAAgC,4BAAA;AAAA,UAAAjC,UAAA,CAAAE,kCAAAA,IAAC+J,WAAU,CAAA,CAAA,0CACV,OACC;AAAA,YAAAjK,UAAA,CAACE,kCAAA,IAAA,MAAA;AAAA,cAAGH,WAAU;AAAA,cAAqBC,UAAW;AAAA,YAAA,CAAA,yCAC7C,OAAI;AAAA,cAAAD,WAAU;AAAA,cACZC,UAAArC,MAAMiL,aAAaxH,IAAK0H,UACvB5I,kCAAA,IAAC;gBAAuBH,WAAU;AAAA,gBAChCC,UAACC,kCAAA,KAAA,OAAA;AAAA,kBAAIF,WAAU;AAAA,kBACbC,UAAA,CAACC,kCAAA,KAAA,OAAA;AAAA,oBAAIF,WAAU;AAAA,oBACbC,UAAA,CAAAE,kCAAA,IAAC,OAAI;AAAA,sBAAAH,WAAU;AAAA,sBAAeC,UAAA8I,KAAKE;AAAAA,oBAAS,CAAA,GAC3CF,KAAK6B,4BAA4BzK,kCAAAA,IAAC;sBAAIH,WAAU;AAAA,sBAAyBC,eAAK2K;AAAAA,oBAAyB,CAAA,GACvG7B,KAAK8B,iBACH3K,kCAAAA,KAAA,OAAA;AAAA,sBAAIF,WAAU;AAAA,sBAAwBC,UAAA,CAAA,eAAY8I,KAAK8B,aAAA;AAAA,oBAAc,CAAA,GAEvE9B,KAAK+B,UACH5K,kCAAAA,KAAA,OAAA;AAAA,sBAAIF,WAAU;AAAA,sBAAwBC,UAAA,CAAA,YAC5B,KACR8I,KAAK+B,OACHC,QAASC,SAAQA,IAAIC,cAAc5J,IAAK6J,WAAU,GAAGA,MAAMC,IAAI,OAAOD,MAAME,KAAK,GAAG,CAAC,EACrFC,KAAK,IAAI,CAAA;AAAA,oBACd,CAAA,GAEFnL,kCAAA,KAAC,OAAI;AAAA,sBAAAF,WAAU;AAAA,sBACZC,UAAA,CAAA8I,KAAKuC,QACJnL,kCAAAA,IAAC,QAAA;AAAA,wBACCH,WAAW,0CAA0C+I,KAAKuC,SAAS,QAC/D,iBACAvC,KAAKuC,SAAS,WACZ,eACA,eACJ;AAAA,sBACJ,CAAA,GAEDvC,KAAKuC,MAAK,OAAIvC,KAAKwC,IAAA;AAAA,oBACtB,CAAA,CAAA;AAAA,kBACF,CAAA,GACArL,kCAAA,KAAC,OAAI;AAAA,oBAAAF,WAAU;AAAA,oBACbC,UAAA,CAACC,kCAAA,KAAA,OAAA;AAAA,sBAAIF,WAAU;AAAA,sBAAUC,UAAA,CAAA,SAAM8I,KAAKG,GAAA;AAAA,oBAAI,CAAA,GACxChJ,kCAAA,KAAC,OAAI;AAAA,sBAAAF,WAAU;AAAA,sBAAUC,UAAA,CAAA,KACrB8I,KAAKyC,cAAa,SAAMzC,KAAKwC,IAAA;AAAA,oBACjC,CAAA,GACArL,kCAAA,KAAC,OAAI;AAAA,sBAAAF,WAAU;AAAA,sBAAgBC,UAAA,CAAA,KAC3B8I,KAAK0C,QACL1C,KAAK2C,mBAAmB3C,KAAK2C,oBAAoB3C,KAAK0C,UACtDvL,kCAAAA,KAAC,QAAK;AAAA,wBAAAF,WAAU;AAAA,wBAA4CC,UAAA,CAAA,KACxD8I,KAAK2C,eAAA;AAAA,sBACT,CAAA,CAAA;AAAA,oBAEJ,CAAA,CAAA;AAAA,kBACF,CAAA,CAAA;AAAA,gBACF,CAAA;AAAA,cA5CQ,GAAA3C,KAAKK,OA6Cf,CACD;AAAA,YACH,CAAA,CAAA;AAAA,UACF,CAAA,CAAA;AAAA,QACF,CAAA,yCAGDc,WAAU,CAAA,CAAA,0CAGV,OACC;AAAA,UAAAjK,UAAA,CAACE,kCAAA,IAAA,MAAA;AAAA,YAAGH,WAAU;AAAA,YAAqBC,UAAiB;AAAA,UAAA,CAAA,GACpDC,kCAAA,KAAC,OAAI;AAAA,YAAAF,WAAU;AAAA,YACXC,UAAA,CAAMrC,MAAA+N,6BAA8B/N,MAAM+N,8BAA8B/N,MAAMgO,oBAC7E1L,kCAAA,KAAA,OAAA;AAAA,cAAIF,WAAU;AAAA,cACbC,UAAA,CAAAE,kCAAA,IAAC;gBAAKF,UAAuB;AAAA,cAAA,CAAA,0CAC5B,QAAK;AAAA,gBAAAA,UAAA,CAAA,KAAErC,MAAM+N,yBAAA;AAAA,cAA0B,CAAA,CAAA;AAAA,YAC1C,CAAA,GAEFzL,kCAAA,KAAC,OAAI;AAAA,cAAAF,WAAU;AAAA,cACbC,UAAA,CAAAE,kCAAA,IAAC;gBAAKF,UAAW;AAAA,cAAA,CAAA,0CAChB,QAAK;AAAA,gBAAAA,UAAA,CAAA,KAAErC,MAAMgO,gBAAA;AAAA,cAAiB,CAAA,CAAA;AAAA,YACjC,CAAA,GACA1L,kCAAA,KAAC,OAAI;AAAA,cAAAF,WAAU;AAAA,cACbC,UAAA,CAAAE,kCAAA,IAAC;gBAAKF,UAAgB;AAAA,cAAA,CAAA,0CACrB,QAAK;AAAA,gBAAAA,UAAA,CAAA,KAAErC,MAAMiO,mBAAA;AAAA,cAAoB,CAAA,CAAA;AAAA,YACpC,CAAA,GACA3L,kCAAA,KAAC,OAAI;AAAA,cAAAF,WAAU;AAAA,cACbC,UAAA,CAAAE,kCAAA,IAAC;gBAAKF,UAAkB;AAAA,cAAA,CAAA,0CACvB,QAAK;AAAA,gBAAAA,UAAA,CAAA,KAAErC,MAAMkO,gBAAA;AAAA,cAAiB,CAAA,CAAA;AAAA,YACjC,CAAA,GACA5L,kCAAA,KAAC,OAAI;AAAA,cAAAF,WAAU;AAAA,cACbC,UAAA,CAAAE,kCAAA,IAAC;gBAAKF,UAAa;AAAA,cAAA,CAAA,0CAClB,QAAK;AAAA,gBAAAA,UAAA,CAAA,KAAErC,MAAMmO,WAAA;AAAA,cAAY,CAAA,CAAA;AAAA,YAC5B,CAAA,GACA7L,kCAAA,KAAC,OAAI;AAAA,cAAAF,WAAU;AAAA,cACbC,UAAA,CAAAE,kCAAA,IAAC;gBAAKF,UAAW;AAAA,cAAA,CAAA,0CAChB,QAAK;AAAA,gBAAAA,UAAA,CAAA,KAAErC,MAAMoO,cAAA;AAAA,cAAe,CAAA,CAAA;AAAA,YAC/B,CAAA,GACA9L,kCAAA,KAAC,OAAI;AAAA,cAAAF,WAAU;AAAA,cACbC,UAAA,CAAAE,kCAAA,IAAC;gBAAKF,UAAS;AAAA,cAAA,CAAA,0CACd,QAAK;AAAA,gBAAAA,UAAA,CAAA,MAAGrC,MAAMqO,mBAAA;AAAA,cAAoB,CAAA,CAAA;AAAA,YACrC,CAAA,yCACC/B,WAAU,CAAA,CAAA,GACXhK,kCAAAA,KAAC,OAAI;AAAA,cAAAF,WAAU;AAAA,cACbC,UAAA,CAAAE,kCAAA,IAAC;gBAAKF,UAAa;AAAA,cAAA,CAAA,0CAClB,QAAK;AAAA,gBAAAA,UAAA,CAAA,KAAErC,MAAMmH,qBAAA;AAAA,cAAsB,CAAA,CAAA;AAAA,YACtC,CAAA,GACA7E,kCAAA,KAAC,OAAI;AAAA,cAAAF,WAAU;AAAA,cACbC,UAAA,CAAAE,kCAAA,IAAC;gBAAKF,UAAW;AAAA,cAAA,CAAA,0CAChB,QAAK;AAAA,gBAAAA,UAAA,CAAA,KAAErC,MAAMsO,SAAA;AAAA,cAAU,CAAA,CAAA;AAAA,YAC1B,CAAA,GACCtO,MAAMuO,eAAe,KACnBjM,kCAAAA,KAAA,OAAA;AAAA,cAAIF,WAAU;AAAA,cACbC,UAAA,CAAAE,kCAAA,IAAC;gBAAKF,UAAc;AAAA,cAAA,CAAA,0CACnB,QAAK;AAAA,gBAAAA,UAAA,CAAA,KAAErC,MAAMuO,YAAA;AAAA,cAAa,CAAA,CAAA;AAAA,YAC7B,CAAA,CAAA;AAAA,UAEJ,CAAA,CAAA;AAAA,QACF,CAAA,GAGCvO,MAAM0J,kBAAkB1J,MAAM0J,eAAepE,SAAS,KAEnDhD,kCAAA,KAAAgC,4BAAA;AAAA,UAAAjC,UAAA,CAAAE,kCAAAA,IAAC+J,WAAU,CAAA,CAAA,0CACV,OACC;AAAA,YAAAjK,UAAA,CAACE,kCAAA,IAAA,MAAA;AAAA,cAAGH,WAAU;AAAA,cAAqBC,UAAe;AAAA,YAAA,CAAA,GACjDE,kCAAA,IAAA,OAAA;AAAA,cAAIH,WAAU;AAAA,cACZC,UAAMrC,MAAA0J,eAAejG,IAAK+K,YACzBlM,kCAAA,KAAC,OAA0B;AAAA,gBAAAF,WAAU;AAAA,gBACnCC,UAAA,CAACC,kCAAA,KAAA,OAAA;AAAA,kBAAIF,WAAU;AAAA,kBACbC,UAAA,CAACC,kCAAA,KAAA,OAAA;AAAA,oBAAIF,WAAU;AAAA,oBAAcC,UAAA,CAAA,YAASmM,OAAOC,QAAA;AAAA,kBAAS,CAAA,GACtDlM,kCAAA,IAACqD,OAAA;AAAA,oBACC5D,SACEwM,OAAO1N,WAAW,SAAS,gBAAgB0N,OAAO1N,WAAW,QAAQ,YAAY;AAAA,oBAGlFuB,UAAOmM,OAAA1N;AAAAA,kBAAA,CACV,CAAA;AAAA,gBACF,CAAA,GACCyB,kCAAA,IAAA,OAAA;AAAA,kBAAIH,WAAU;AAAA,kBAA8BC,iBAAON;AAAAA,gBAAY,CAAA,GAChEO,kCAAA,KAAC,OAAI;AAAA,kBAAAF,WAAU;AAAA,kBAAwBC,UAAA,CAAA,aAC3B,IAAI1F,KAAK6R,OAAOE,WAAW,EAAEtH,eAAe,CAAA;AAAA,gBACxD,CAAA,CAAA;AAAA,cAdQ,GAAAoH,OAAOC,QAejB,CACD;AAAA,YACH,CAAA,CAAA;AAAA,UACF,CAAA,CAAA;AAAA,QACF,CAAA,GAIFnM,kCAAA,KAAC,OAAI;AAAA,UAAAF,WAAU;AAAA,UACbC,UAAA,CAAAE,kCAAA,IAACC,QAAO;AAAA,YAAAR,SAAQ;AAAA,YAAUU,SAAS6E;AAAAA,YAASlF,UAE5C;AAAA,UAAA,CAAA,GACCrC,MAAME,gBAAgB,aACrBqC,kCAAAA,IAACC,QAAO;AAAA,YAAAR,SAAQ;AAAA,YAAcU,SAASA,MAAM4D,SAAStG,OAAO,cAAc;AAAA,YAAGqC,UAE9E;AAAA,UAAA,CAAA,GAED+G,uBAAuBpJ,KAAK,KAC3BsC,kCAAAA,KAACE,QAAO;AAAA,YAAAR,SAAQ;AAAA,YAAUU,SAASA,MAAM4D,SAAStG,OAAO,yBAAyB;AAAA,YAAGqC,UAAA,CAAA,cACxErC,MAAME,gBAAgB,YAAY,aAAaF,MAAME,gBAAgB,aAAa,WAAWF,MAAME,gBAAgB,WAAW,aAAaF,MAAME,gBAAgB,aAAa,aAAa,cAAa,KAACqC,kCAAA,IAAC8G,YAAW;AAAA,cAAAjH,WAAU;AAAA,YAAU,CAAA,CAAA;AAAA,UACtP,CAAA,GAEApC,MAAME,gBAAgB,gBACtBqC,kCAAAA,IAACC;YAAOR,SAAQ;AAAA,YAAUI,WAAU;AAAA,YAAgCM,SAASA,MAAM4D,SAAStG,OAAO,gBAAgB;AAAA,YAAGqC,UAEtH;AAAA,UAAA,CAAA,CAAA;AAAA,QAEJ,CAAA,CAAA;AAAA,MACF,CAAA,CAAA;AAAA,IACF,CAAA;AAAA,EACF,CAAA;AAEJ;AAYO,SAASmF,YAAY;AAAA,EAAExH;AAAAA,EAAOpE;AAAAA,EAAY2L;AAAAA,EAASvL;AAAAA,EAAcyL;AAAS,GAAqB;AACpG,QAAM,CAACjJ,UAAUmQ,WAAW,IAAIxT,sBAAS;AAAA;AAAA,IAEvCyT,QAAQ;AAAA;AAAA,IAERC,OAAO;AAAA,IACPC,MAAM;AAAA;AAAA,IAENC,cAAc;AAAA,IACdC,cAAc;AAAA,IACdC,YAAY;AAAA,IACZC,YAAY;AAAA,EACd,CAAC;AAEK,QAAAC,eAAgBrL,OAAuB;AAC3CA,MAAEsL,eAAe;AACjB3H,aAASjJ,QAAQ;AAAA,EACnB;AAEA,MAAI,CAACwB,SAAS,CAACpE,WAAmB,QAAA;AAElC,QAAMyT,oBAAoBA,MACvB/M,kCAAA,KAAA,OAAA;AAAA,IAAIF,WAAU;AAAA,IACbC,UAAA,CAACC,kCAAA,KAAA,OAAA;AAAA,MAAIF,WAAU;AAAA,MACbC,UAAA,CAACE,kCAAA,IAAA+M,eAAA;AAAA,QAAclN,WAAU;AAAA,MAAuB,CAAA,0CAC/C,OACC;AAAA,QAAAC,UAAA,CAACE,kCAAA,IAAA,KAAA;AAAA,UAAEH,WAAU;AAAA,UAA2BC,UAAY;AAAA,QAAA,CAAA,GACnDE,kCAAA,IAAA,KAAA;AAAA,UAAEH,WAAU;AAAA,UAAuBC,UAEpC;AAAA,QAAA,CAAA,CAAA;AAAA,MACF,CAAA,CAAA;AAAA,IACF,CAAA,0CAEC,OACC;AAAA,MAAAA,UAAA,CAACE,kCAAA,IAAA0C,OAAA;AAAA,QAAMsK,SAAQ;AAAA,QAASlN,UAAmB;AAAA,MAAA,CAAA,GAC3CE,kCAAA,IAACiN,GAAA;AAAA,QACCrL,IAAG;AAAA,QACH1E,OAAOjB,SAASoQ;AAAAA,QAChB/K,UAAWC,OAAM6K,YAAac,WAAU;AAAA,UAAE,GAAGA;AAAAA,UAAMb,QAAQ9K,EAAEC,OAAOtE;AAAAA,QAAM,EAAE;AAAA,QAC5E8D,aAAY;AAAA,QACZnB,WAAU;AAAA,MAAA,CACZ,CAAA;AAAA,IACF,CAAA,GAEAE,kCAAA,KAAC,OAAI;AAAA,MAAAF,WAAU;AAAA,MACbC,UAAA,CAAAE,kCAAA,IAACC;QAAOR,SAAQ;AAAA,QAAUU,SAAS6E;AAAAA,QAAS5E,UAAU3G;AAAAA,QAAcqG,UAEpE;AAAA,MAAA,CAAA,GACAE,kCAAA,IAACC,QAAO;AAAA,QAAAR,SAAQ;AAAA,QAAcU,SAASyM;AAAAA,QAAcxM,UAAU3G;AAAAA,QAC5DqG,UAAerG,eAAA,kBAAkB;AAAA,MACpC,CAAA,CAAA;AAAA,IACF,CAAA,CAAA;AAAA,EACF,CAAA;AAGF,QAAM0T,0BAA0BA,MAC7BpN,kCAAA,KAAA,OAAA;AAAA,IAAIF,WAAU;AAAA,IACbC,UAAA,CAACC,kCAAA,KAAA,OAAA;AAAA,MAAIF,WAAU;AAAA,MACbC,UAAA,CAACE,kCAAA,IAAA+M,eAAA;AAAA,QAAclN,WAAU;AAAA,MAA0B,CAAA,GAClDG,kCAAA,IAAA,OAAA;AAAA,QACCF,UAACC,kCAAA,KAAA,KAAA;AAAA,UAAEF,WAAU;AAAA,UAAkBC,UAAA,CAAA,gDAA6CrC,MAAME,gBAAgB,YAAY,aAAaF,MAAME,gBAAgB,aAAa,WAAWF,MAAME,gBAAgB,WAAW,aAAaF,MAAME,gBAAgB,aAAa,aAAa,cAAa,GAAA;AAAA,QAAC,CAAA;AAAA,MACvR,CAAA,CAAA;AAAA,IACF,CAAA,GA6BAoC,kCAAA,KAAC,OAAI;AAAA,MAAAF,WAAU;AAAA,MACbC,UAAA,CAAAE,kCAAA,IAACC;QAAOR,SAAQ;AAAA,QAAUU,SAAS6E;AAAAA,QAAS5E,UAAU3G;AAAAA,QAAcqG,UAEpE;AAAA,MAAA,CAAA,GACAE,kCAAA,IAACC,QAAA;AAAA,QACCE,SAASyM;AAAAA,QACTxM,UAAU3G;AAAAA,QAETqG,yBAAe,gBAAgB;AAAA,MAAA,CAClC,CAAA;AAAA,IACF,CAAA,CAAA;AAAA,EACF,CAAA;AAGF,QAAMsN,2BAA2BA,MAC9BrN,kCAAA,KAAA,OAAA;AAAA,IAAIF,WAAU;AAAA,IACbC,UAAA,CAACC,kCAAA,KAAA,OAAA;AAAA,MAAIF,WAAU;AAAA,MACbC,UAAA,CAACE,kCAAA,IAAA+M,eAAA;AAAA,QAAclN,WAAU;AAAA,MAA0B,CAAA,0CAClD,OACC;AAAA,QAAAC,UAAA,CAACE,kCAAA,IAAA,KAAA;AAAA,UAAEH,WAAU;AAAA,UAA8BC,UAAuB;AAAA,QAAA,CAAA,GACjEE,kCAAA,IAAA,KAAA;AAAA,UAAEH,WAAU;AAAA,UAA0BC,UAAsC;AAAA,QAAA,CAAA,CAAA;AAAA,MAC/E,CAAA,CAAA;AAAA,IACF,CAAA,GAiDAC,kCAAA,KAAC,OAAI;AAAA,MAAAF,WAAU;AAAA,MACbC,UAAA,CAAAE,kCAAA,IAACC;QAAOR,SAAQ;AAAA,QAAUU,SAAS6E;AAAAA,QAAS5E,UAAU3G;AAAAA,QAAcqG,UAEpE;AAAA,MAAA,CAAA,GACAE,kCAAA,IAACC,QAAA;AAAA,QACCE,SAASyM;AAAAA,QACTxM,UAAU3G;AAAAA,QACVoG,WAAU;AAAA,QAETC,yBAAe,eAAe;AAAA,MAAA,CACjC,CAAA;AAAA,IACF,CAAA,CAAA;AAAA,EACF,CAAA;AAIA,SAAAE,kCAAAA,IAACqJ;IAAO5C,MAAM;AAAA,IAAM6C,cAActE;AAAAA,IAChClF,UAAAC,kCAAA,KAACwJ,eAAc;AAAA,MAAA1J,WAAU;AAAA,MACvBC,UAAA,CAACE,kCAAA,IAAAwJ,cAAA;AAAA,QACC1J,iDAAC2J,aACE;AAAA,UAAA3J,UAAA,CAAAzG,eAAe,kBAAkB,gBACjCA,eAAe,6BAA6B,2BAC5CA,eAAe,oBAAoB,gBAAA;AAAA,QACtC,CAAA;AAAA,MACF,CAAA,GAECA,eAAe,kBAAkByT,qBACjCzT,eAAe,6BAA6B8T,wBAAwB,GACpE9T,eAAe,oBAAoB+T,yBAAyB,CAAA;AAAA,IAC/D,CAAA;AAAA,EACF,CAAA;AAEJ;AAGa,MAAAxH,6BAA6BA,CAACyH,WAAmBC,YAA6B;AACnF,QAAAC,YAAY,IAAInT,KAAKiT,SAAS;AACpC,QAAMG,cAAcF,UAAU,IAAIlT,KAAKkT,OAAO,wBAAQlT,KAAK;AACpD,SAAA4I,KAAKyK,OAAOD,YAAYxP,QAAA,IAAYuP,UAAUvP,aAAa,GAAI;AACxE;AAGa,MAAAqJ,oBAAqBqG,aAA4B;AAC5D,QAAMC,UAAU3K,KAAKyK,MAAMC,UAAU,EAAE;AACvC,QAAME,QAAQ5K,KAAKyK,MAAME,UAAU,EAAE;AACrC,QAAME,OAAO7K,KAAKyK,MAAMG,QAAQ,EAAE;AAElC,MAAIC,OAAO,GAAG;AACZ,WAAO,GAAGA,IAAI,KAAKD,QAAQ,EAAE;AAAA,EAC/B;AACA,MAAIA,QAAQ,GAAG;AACb,WAAO,GAAGA,KAAK,KAAKD,UAAU,EAAE;AAAA,EAClC;AACA,SAAO,GAAGA,OAAO;AACnB;AAGa,MAAAjI,sBAAuBjI,WAAqB;AACjD,QAAAkI,WAAWC,2BAA2BnI,MAAMM,SAAS;AAC3D,QAAM+P,cAAc;AACpB,MAAIC,eAAe;AACnB,MAAIC,YAAY;AAChB,MAAI5E,aAAa;AAGjB,UAAQ3L,MAAME,aAAa;AAAA,IACzB,KAAK;AACH,UAAIgI,WAAW,KAAK;AACNqI,oBAAA;AACGD,uBAAA;AACF3E,qBAAA;AAAA,MACf,WAAWzD,WAAW,KAAK;AACbqI,oBAAA;AACGD,uBAAA;AACF3E,qBAAA;AAAA,MACf;AACA;AAAA,IAEF,KAAK;AACC,UAAA3L,MAAMwK,qBAAqB,OAAO;AACpC,YACEtC,WAAW,OACXlI,MAAM8K,aACN,CAAC,eAAe,eAAe,qBAAqB,EAAE7K,SAASD,MAAM8K,SAAS,GAC9E;AACYyF,sBAAA;AACGD,yBAAA;AACF3E,uBAAA;AAAA,QAEb,WAAAzD,WAAW,OACXlI,MAAM8K,aACN,CAAC,eAAe,eAAe,qBAAqB,EAAE7K,SAASD,MAAM8K,SAAS,GAC9E;AACYyF,sBAAA;AACGD,yBAAA;AACF3E,uBAAA;AAAA,QACJ,WAAAzD,YAAY,OAAOlI,MAAM8K,cAAc,sBAAsB;AACvDwF,yBAAA;AACF3E,uBAAA;AAAA,QACf;AAAA,MACS,WAAA3L,MAAMwK,qBAAqB,UAAUtC,WAAW,KAAK;AAC/CoI,uBAAA;AACF3E,qBAAA;AAAA,MACf;AACA;AAAA,IAEF,KAAK;AACC,UAAA3L,MAAMwK,qBAAqB,OAAO;AAChC,YAAAtC,WAAW,QAAQ,CAAC,eAAe,eAAe,qBAAqB,EAAEjI,SAASD,MAAM8K,SAAS,GAAG;AAC1FyF,sBAAA;AACGD,yBAAA;AACF3E,uBAAA;AAAA,QACJ,WAAAzD,WAAW,QAAQlI,MAAM8K,cAAc,sBAAsB;AAC1DyF,sBAAA;AACGD,yBAAA;AACF3E,uBAAA;AAAA,QACf;AAAA,MACS,WAAA3L,MAAMwK,qBAAqB,UAAUtC,WAAW,MAAM;AAChDoI,uBAAA;AACF3E,qBAAA;AAAA,MACf;AACA;AAAA,EACJ;AAEO,SAAA;AAAA,IACLvJ,WAAWiO,cAAcE,YAAYD;AAAAA,IACrC3E;AAAAA,EACF;AACF;AAGa,MAAAF,uBAAwB3K,YAA2B;AAC9D,UAAQA,QAAQ;AAAA,IACd,KAAK;AACI,aAAA;AAAA,IACT,KAAK;AACI,aAAA;AAAA,IACT;AACS,aAAAA;AAAAA,EACX;AACF;AAGa,MAAAiK,+BAAgCD,eAA8B;AACzE,SAAOA,UACJ0F,QAAQ,QAAQ,EAAE,EAClBA,QAAQ,KAAK,GAAG,EAChBC,cACAC,MAAM,GAAG,EACTjN,IAAKkN,UAASA,KAAKC,OAAO,CAAC,EAAEC,YAAA,IAAgBF,KAAKzF,MAAM,CAAC,CAAC,EAC1DuC,KAAK,GAAG;AACb;AAEa,MAAArE,yBAA0BpJ,WAAqB;AAC1D,QAAM8Q,QAAQ9Q,MAAMyK,QAAQ,UAAUzK,MAAMyK,QAAQ;AACpD,QAAMsG,qBAAqB/Q,MAAMwK,qBAAqB,UAAUxK,MAAMwK,qBAAqB;AAC3F,MAAIsG,SAASC,oBAAoB;AAC/B,WAAQ/Q,MAAME,gBAAgB,aAAaF,MAAME,gBAAgB,cAAcF,MAAME,gBAAgB,YAAYF,MAAME,gBAAgB,cAAcF,MAAME,gBAAgB;AAAA,EAE7K;AACI,MAAA,CAAC4Q,SAAS,CAACC,oBAAoB;AACjC,WAAQ/Q,MAAME,gBAAgB,aAAaF,MAAME,gBAAgB,cAAcF,MAAME,gBAAgB,YAAYF,MAAME,gBAAgB,cAAcF,MAAME,gBAAgB;AAAA,EAE7K;AACI,MAAA4Q,SAAS,CAACC,oBAAoB;AAChC,WAAQ/Q,MAAME,gBAAgB,aAAaF,MAAME,gBAAgB,cAAcF,MAAME,gBAAgB,YAAYF,MAAME,gBAAgB,cAAcF,MAAME,gBAAgB;AAAA,EAE7K;AACI,MAAA,CAAC4Q,SAASC,oBAAoB;AAChC,WAAQ/Q,MAAME,gBAAgB,aAAaF,MAAME,gBAAgB,cAAcF,MAAME,gBAAgB,YAAYF,MAAME,gBAAgB,cAAcF,MAAME,gBAAgB;AAAA,EAE7K;AACA,SAAQF,MAAME,gBAAgB,aAAaF,MAAME,gBAAgB,cAAcF,MAAME,gBAAgB,YAAYF,MAAME,gBAAgB,cAAcF,MAAME,gBAAgB;AAC7K;"}