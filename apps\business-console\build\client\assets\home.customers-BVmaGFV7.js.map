{"version": 3, "file": "home.customers-BVmaGFV7.js", "sources": ["../../../app/components/ui/oldPagination.tsx", "../../../app/routes/home.customers.tsx"], "sourcesContent": ["import React from \"react\";\r\nimport { Form } from \"@remix-run/react\";\r\ninterface PaginationProps {\r\n  currentPage: number; // Current page index (starts from 0 internally)\r\n  hasMoreData: boolean; // Whether there's more data beyond the current page\r\n  tabValue : string;\r\n  sortBy : string;\r\n  searchBy : string;\r\n  sortByOrder : string;\r\n}\r\nconst Pagination: React.FC<PaginationProps> = ({ currentPage, hasMoreData,tabValue,sortBy,searchBy,sortByOrder }) => {\r\n  const displayPage = currentPage + 1; // Adjust for user-friendly display\r\n  const maxVisiblePages = 5; // Maximum number of pages to display around the current page\r\n  const lastPage = hasMoreData ? displayPage + 2 : displayPage;\r\n  // Generate dynamic page numbers\r\n  const getPageNumbers = (): (number | string)[] => {\r\n    const pageNumbers: (number | string)[] = [];\r\n    // Show the first page and ellipsis if necessary\r\n    if (currentPage > 2) {\r\n      pageNumbers.push(1);\r\n      if (currentPage > 3) pageNumbers.push(\"...\");\r\n    }\r\n    // Add pages around the current page\r\n    for (\r\n      let i = Math.max(1, displayPage - 2);\r\n      i <=  Math.min(displayPage + 2, currentPage + maxVisiblePages,lastPage);\r\n      i++\r\n    ) {\r\n      pageNumbers.push(i);\r\n    }\r\n    // Show ellipsis and the next button if there's more data\r\n    if (hasMoreData) {\r\n      if (currentPage + 3 < displayPage + maxVisiblePages) pageNumbers.push(\"...\");\r\n    }\r\n    return pageNumbers;\r\n  };\r\n  return (\r\n    <div className=\"flex justify-center items-center gap-2 mt-4\">\r\n      {/* Previous Button */}\r\n      {currentPage > 0 && (\r\n        <Form method=\"get\" className=\"inline\">\r\n          <input type=\"hidden\" name=\"page\" value={currentPage - 1} />\r\n          <input type=\"hidden\" name=\"tabValue\" value={tabValue} />\r\n          <input type=\"hidden\" name=\"sortBy\" value={sortBy} />\r\n          <input type=\"hidden\" name=\"search\" value={searchBy} />\r\n          <input type=\"hidden\" name=\"search\" value={sortByOrder} />\r\n          <button\r\n            type=\"submit\"\r\n            className=\"px-3 py-1  rounded-full border border-grey-200 hover:bg-primary-50\"\r\n          >\r\n            &lt;\r\n          </button>\r\n        </Form>\r\n      )}\r\n      {/* Page Numbers */}\r\n      {getPageNumbers().map((page, index) =>\r\n        page === \"...\" ? (\r\n          <span key={index} className=\"px-3 py-1 text-gray-500\">\r\n            ...\r\n          </span>\r\n        ) : (\r\n          <Form method=\"get\" key={index} className=\"inline\">\r\n            <input type=\"hidden\" name=\"page\" value={(page as number) - 1} />\r\n            <input type=\"hidden\" name=\"tabValue\" value={tabValue} />\r\n            <input type=\"hidden\" name=\"sortBy\" value={sortBy} />\r\n            <input type=\"hidden\" name=\"search\" value={searchBy} />\r\n            <input type=\"hidden\" name=\"search\" value={sortByOrder} />\r\n            <button\r\n              type=\"submit\"\r\n              className={`px-3 py-1 rounded-full ${\r\n                page === displayPage\r\n                  ? \"bg-primary text-white\"\r\n                  : \"border border-grey-200 hover:bg-primary-50\"\r\n              }`}\r\n            >\r\n              {page}\r\n            </button>\r\n          </Form>\r\n        )\r\n      )}\r\n      {/* Next Button */}\r\n      {hasMoreData && (\r\n        <Form method=\"get\" className=\"inline\">\r\n          <input type=\"hidden\" name=\"page\" value={currentPage + 1} />\r\n          <input type=\"hidden\" name=\"tabValue\" value={tabValue} />\r\n            <input type=\"hidden\" name=\"sortBy\" value={sortBy} />\r\n            <input type=\"hidden\" name=\"search\" value={searchBy} />\r\n            <input type=\"hidden\" name=\"search\" value={sortByOrder} />\r\n          <button\r\n            type=\"submit\"\r\n            className=\"px-3 py-1 rounded-full border border-grey-200 hover:bg-primary-50\"\r\n          >\r\n            &gt;\r\n          </button>\r\n        </Form>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\nexport default Pagination;", "\r\nimport { useState } from 'react'\r\nimport { <PERSON><PERSON> } from \"@components/ui/button\"\r\nimport { Input } from \"@components/ui/input\"\r\nimport { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from \"@components/ui/table\"\r\nimport { Tabs, TabsList, TabsTrigger } from \"@components/ui/tabs\"\r\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from \"@components/ui/select\"\r\nimport { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from \"@components/ui/dialog\"\r\nimport { Check, Edit, Eye, EyeOff, Plus, RefreshCcw, Upload, X, Send, ArrowDownWideNarrow, ArrowUp, ArrowDown, Save, Pencil, Info } from \"lucide-react\"\r\nimport { Form, Link, useFetcher, useLoaderData, useNavigate } from \"@remix-run/react\"\r\nimport type { ActionFunction, LoaderFunction, TypedResponse } from \"@remix-run/node\"\r\nimport { json } from \"@remix-run/node\"\r\nimport { getSession } from \"@utils/session.server\"\r\nimport type { User } from \"~/types\"\r\nimport { getBuyerSummary, updateFbDiscountPrice } from \"@services/businessConsoleService\"\r\nimport { BuyerSummaryDetailsResponseItem } from \"~/types/api/businessConsoleService/BuyerSummaryDetailsResponseItem\"\r\nimport { withAuth, withResponse } from \"@utils/auth-utils\"\r\nimport Pagination from '~/components/ui/oldPagination'\r\nimport { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger, } from '~/components/ui/tooltip'\r\nimport SpinnerLoader from '~/components/loader/SpinnerLoader'\r\nimport { Label } from '~/components/ui/label'\r\n\r\ninterface LoaderData {\r\n    data: BuyerSummaryDetailsResponseItem[];\r\n    currentPage: number;\r\n    hasNextPage: boolean;\r\n    hasMoreData: boolean;\r\n    tabValue: string;\r\n    sortByvalue: string;\r\n    searchBy: string;\r\n    sortByOrder: string;\r\n}\r\ninterface ErrorResponse {\r\n    error: string;\r\n}\r\nexport const loader = withAuth(async ({ user, request }) => {\r\n    const url = new URL(request.url);\r\n    const page = parseInt(url.searchParams.get(\"page\") || \"0\");\r\n    const tabValue = url.searchParams.get(\"tabValue\") || \"all\";\r\n    const sortBy = url.searchParams.get(\"sortBy\") || \"buyerName\";\r\n    const searchBy = url.searchParams.get(\"searchBy\") || \"\";\r\n    const sortByOrder = url.searchParams.get(\"sortByOrder\") || \"asc\";\r\n    const validSearchBy = searchBy && searchBy.length >= 3 ? searchBy : \"\";\r\n    const pageSize = 50;\r\n    try {\r\n        const response = await getBuyerSummary(user.userId, page, pageSize, tabValue, sortBy, validSearchBy, sortByOrder, request);\r\n        const hasNextPage = (response.data?.length ?? 0) >= pageSize;\r\n        const nextPageResponse = await getBuyerSummary(user.userId, page + 1, pageSize, tabValue, sortBy, validSearchBy, sortByOrder, request);\r\n        const hasMoreData = (nextPageResponse.data ?? []).length > 0;\r\n        return withResponse({\r\n            data: response.data,\r\n            currentPage: page,\r\n            hasNextPage,\r\n            hasMoreData,\r\n            tabValue,\r\n            sortBy,\r\n            searchBy,\r\n            sortByOrder\r\n        }, response.headers);\r\n    } catch (error) {\r\n        console.error(\"Buyer summary error:\", error);\r\n        throw new Response(\"Failed to fetch buyer summary data\", {\r\n            status: 500\r\n        });\r\n    }\r\n});\r\n\r\nexport const action = withAuth(async ({ request }) => {\r\n\r\n    const formData = await request.formData();\r\n    const intent = formData.get(\"_intent\");\r\n    const nBuyerId = Number(formData.get(\"nBuyerId\"))\r\n    const fBDiscount = Number(formData.get(\"fbDiscount\"))\r\n\r\n    if (intent === \"UpdateFbDiscount\") {\r\n\r\n        try {\r\n            const response = await updateFbDiscountPrice(nBuyerId, fBDiscount, request);\r\n\r\n            return withResponse(\r\n                { data: response.data }, response.headers\r\n            )\r\n        }\r\n        catch (error) {\r\n\r\n            throw new Response(\"Failed to updateFbDiscount\", {\r\n                status: 500\r\n            });\r\n        }\r\n    }\r\n\r\n\r\n\r\n})\r\n\r\nexport default function CustomersSection() {\r\n    const { data: customers, currentPage, hasNextPage, hasMoreData, sortByvalue } = useLoaderData<LoaderData>()\r\n    const [searchTerm, setSearchTerm] = useState('')\r\n    const [sortBy, setSortBy] = useState(\"buyerName\")\r\n    const [sortOrder, setSortOrder] = useState('asc')\r\n    const [activeTab, setActiveTab] = useState('all')\r\n    const navigate = useNavigate();\r\n    const [hiddenCustomers, setHiddenCustomers] = useState<number[]>([])\r\n    const [selectedCustomer, setSelectedCustomer] = useState<BuyerSummaryDetailsResponseItem | null>(null)\r\n    const [selectedTemplate, setSelectedTemplate] = useState('')\r\n    const [selectedBuyerData, setSelectedBuyerData] = useState<BuyerSummaryDetailsResponseItem | null>(null);\r\n\r\n    const templates = [\r\n        { name: 'hello_mnet', displayName: 'Hello mNet' },\r\n    ]\r\n\r\n    const handleTabChange = (newTab: string) => {\r\n        // Map tab labels to `tabValue`\r\n        const tabMap: { [key: string]: string } = {\r\n            all: \"all\",\r\n            oneOrder: \"one_order\",\r\n            frequent: \"frequent_orders\",\r\n            zero_orders: \"zero_orders\",\r\n        };\r\n\r\n        const validTabValue = tabMap[newTab] || \"all\"; // Default to \"all\" if tab is not recognized\r\n        setActiveTab(newTab);\r\n        console.log(newTab, \"444444444\")\r\n\r\n        // Navigate with the correct `tabValue`\r\n        navigate(`?tabValue=${validTabValue}&page=0&sortBy=${sortBy}&sortByOrder=${sortOrder}`);\r\n    };\r\n\r\n    const handleSort = (newSort: string) => {\r\n        setSortBy((prevSortBy) => {\r\n            const isSameSort = prevSortBy === newSort;\r\n            console.log(activeTab, \"444444444\")\r\n\r\n            setSortOrder((prevSortOrder) => {\r\n                const newOrder = isSameSort && prevSortOrder === \"asc\" ? \"desc\" : \"asc\";\r\n\r\n                navigate(`?tabValue=${activeTab}&page=0&sortBy=${newSort}&sortByOrder=${newOrder}&searchBy=${searchTerm}`);\r\n\r\n                return newOrder;\r\n            });\r\n\r\n            return newSort;\r\n        });\r\n    };\r\n\r\n    const getSortIcon = (column: string) => {\r\n        if (sortBy !== column) return null; // Only show the icon for the sorted column\r\n        return sortOrder === \"asc\" ? <ArrowUp className=\"w-4 h-4 ml-1\" /> : <ArrowDown className=\"w-4 h-4 ml-1\" />;\r\n    };\r\n    const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n        const value = e.target.value;\r\n        setSearchTerm(value);\r\n        const searchParam = value.length >= 3 ? `${value}` : \"\";\r\n        navigate(`?tabValue=${activeTab}&page=0&sortBy=${sortBy}&sortByOrder=${sortOrder}&searchBy=${searchParam}`);\r\n    };\r\n\r\n    const toggleHidden = (id: number) => {\r\n        setHiddenCustomers(prev =>\r\n            prev.includes(id) ? prev.filter(customerId => customerId !== id) : [...prev, id]\r\n        )\r\n    }\r\n\r\n    const filteredCustomers = customers\r\n\r\n    const [fbDiscounts, setFbDiscounts] = useState<{ [key: number]: string }>({});\r\n    const [isFbDis, setIsFbDis] = useState<{ [key: number]: boolean }>({});\r\n\r\n    const handleChangeFbDiscount = (nBuyerId: number, val: string) => {\r\n        if (/^\\d*\\.?\\d*$/.test(val) || val === \"\") {  // Allow empty string\r\n            setFbDiscounts((prev) => ({ ...prev, [nBuyerId]: val }));\r\n        }\r\n    };\r\n    const handleEdit = (customer: any) => {\r\n        setIsFbDis((prev) => ({ ...prev, [customer.nBuyerId]: true }));\r\n    };\r\n\r\n\r\n    const fbFetcher = useFetcher<BuyerSummaryDetailsResponseItem>()\r\n\r\n\r\n    const handleSave = (nBuyerId: number,) => {\r\n        const formData = new FormData()\r\n        formData.append(\"_intent\", \"UpdateFbDiscount\");\r\n        formData.append(\"nBuyerId\", nBuyerId.toString());\r\n        formData.append(\"fbDiscount\", fbDiscounts[nBuyerId]);\r\n        fbFetcher.submit(formData, { method: \"POST\" })\r\n        console.log(`Saving discount for Buyer ID ${nBuyerId}:`, fbDiscounts[nBuyerId]);\r\n        const updatedBuyer = customers.find(customer => customer.nBuyerId === nBuyerId);\r\n        if (updatedBuyer) {\r\n            setSelectedBuyerData({ ...updatedBuyer, discount: Number(fbDiscounts[nBuyerId]) });\r\n        }\r\n\r\n        setIsFbDis((prev) => ({ ...prev, [nBuyerId]: false }));\r\n    };\r\n    const [infoOpen, setInfoOpen] = useState(false);\r\n    const handleInfoOpen = () => {\r\n        setInfoOpen(true)\r\n    }\r\n    const handleClearInfo = () => {\r\n        setInfoOpen(false);\r\n\r\n    }\r\n\r\n    return (\r\n        <div className=\"container mx-auto p-6\">\r\n            <div className=\"flex justify-between items-center mb-6\">\r\n                <h1 className=\"text-2xl font-bold\">My Customers</h1>\r\n                {/* <div className=\"flex gap-2\">\r\n                    <Button variant=\"outline\" size=\"icon\">\r\n                        <Plus className=\"h-4 w-4\" />\r\n                    </Button>\r\n                    <Button variant=\"outline\" size=\"icon\">\r\n                        <Upload className=\"h-4 w-4\" />\r\n                    </Button>\r\n                    <Button variant=\"outline\" size=\"icon\">\r\n                        <RefreshCcw className=\"h-4 w-4\" />\r\n                    </Button>\r\n                </div> */}\r\n            </div>\r\n\r\n\r\n\r\n            <TooltipProvider>\r\n                <Tabs value={activeTab} onValueChange={handleTabChange} className=\"mb-6\">\r\n                    <TabsList>\r\n                        <Tooltip>\r\n                            <TooltipTrigger asChild>\r\n                                {/* Wrap the button inside a div to ensure proper highlight */}\r\n                                <div className=\"relative\">\r\n                                    <TabsTrigger value=\"all\">All</TabsTrigger>\r\n                                </div>\r\n                            </TooltipTrigger>\r\n                            <TooltipContent>All customers</TooltipContent>\r\n                        </Tooltip>\r\n\r\n                        <Tooltip>\r\n                            <TooltipTrigger asChild>\r\n                                <div className=\"relative\">\r\n                                    <TabsTrigger value=\"frequent\">Frequent customers</TabsTrigger>\r\n                                </div>\r\n                            </TooltipTrigger>\r\n                            <TooltipContent>Customers who have placed more than 10 orders in the last 3months.</TooltipContent>\r\n                        </Tooltip>\r\n\r\n                        <Tooltip>\r\n                            <TooltipTrigger asChild>\r\n                                <div className=\"relative\">\r\n                                    <TabsTrigger value=\"oneOrder\">One order customers</TabsTrigger>\r\n                                </div>\r\n                            </TooltipTrigger>\r\n                            <TooltipContent>Customers who have placed only one order in the last year </TooltipContent>\r\n                        </Tooltip>\r\n\r\n                        <Tooltip>\r\n                            <TooltipTrigger asChild>\r\n                                <div className=\"relative\">\r\n                                    <TabsTrigger value=\"zero_orders\"> New Customers</TabsTrigger>\r\n                                </div>\r\n                            </TooltipTrigger>\r\n                            <TooltipContent>Customers with no orders in the past 3 months</TooltipContent>\r\n                        </Tooltip>\r\n\r\n\r\n                    </TabsList>\r\n                </Tabs>\r\n            </TooltipProvider>\r\n            <div className=\"flex justify-between mb-4\">\r\n                <Input\r\n                    placeholder=\"Search by name or owner\"\r\n                    value={searchTerm}\r\n                    onChange={searchTerm.length >= 3 ? handleSearch : (e) => setSearchTerm(e.target.value)}\r\n                    className=\"max-w-sm\"\r\n                />\r\n                <Select value={sortBy} onValueChange={handleSort}>\r\n                    <SelectTrigger className=\"w-[180px]\">\r\n                        <SelectValue placeholder=\"Sort by\" />\r\n                    </SelectTrigger>\r\n                    <SelectContent>\r\n                        <SelectItem value=\"buyerName\">Name</SelectItem>\r\n                        <SelectItem value=\"totalOrders\">Number of Orders</SelectItem>\r\n                        <SelectItem value=\"pendingAmount\">Pending Balance</SelectItem>\r\n                        <SelectItem value=\"lastOrderedDate\">Order Duration Days</SelectItem>\r\n                        <SelectItem value=\"fbDiscount\">Contract Price ExpDate</SelectItem>\r\n                        <SelectItem value=\"fbDiscount\">F.B.Discount</SelectItem>\r\n\r\n                    </SelectContent>\r\n                </Select>\r\n            </div>\r\n            <Table >\r\n                {fbFetcher.state !== \"idle\" &&\r\n                    <SpinnerLoader loading={true} />}\r\n                <TableHeader >\r\n                    <TableRow  >\r\n                        <TableHead className=\"cursor-pointer \" onClick={() => handleSort(\"buyerName\")}>\r\n                            <span className='flex flex-row items-center gap-1'> <span>Name</span> <span>{getSortIcon(\"buyerName\")}</span></span>\r\n                        </TableHead>\r\n                        {/* <TableHead className=\"cursor-pointer   \" onClick={() => handleSort(\"totalOrders\")}>\r\n                            <span className='flex flex-row items-center gap-1'> <span>Area Name</span> {getSortIcon(\"totalOrders\")} </span>\r\n                        </TableHead>\r\n                        <TableHead className=\"cursor-pointer   \" onClick={() => handleSort(\"totalOrders\")}>\r\n                            <span className='flex flex-row items-center gap-1'> <span>Agent Name</span> {getSortIcon(\"totalOrders\")} </span>\r\n                        </TableHead> */}\r\n                        {/* <TableHead className=\"text-left\">Mobile Number</TableHead> */}\r\n\r\n                        <TableHead className=\"cursor-pointer   \" onClick={() => handleSort(\"totalOrders\")}>\r\n                            <span className='flex flex-row items-center gap-1'> <span>Num of Orders</span> {getSortIcon(\"totalOrders\")} </span>\r\n                        </TableHead>\r\n\r\n                        <TableHead className=\"cursor-pointer \" onClick={() => handleSort(\"pendingAmount\")}>\r\n                            <span className='flex flex-row items-center gap-1'>  <span>Pending Balance</span> {getSortIcon(\"pendingAmount\")} </span>\r\n                        </TableHead>\r\n\r\n                        <TableHead className=\"cursor-pointer    \" onClick={() => handleSort(\"lastOrderedDate\")}>\r\n                            <span className='flex flex-row items-center gap-1'>  <span>Order Duration Days</span> {getSortIcon(\"lastOrderedDate\")} </span>\r\n                        </TableHead>\r\n                        <TableHead className=\"cursor-pointer    \" onClick={() => handleSort(\"lastOrderedDate\")}>\r\n                            <span className='flex flex-row items-center gap-1'>  <span>Contract Price ExpDate</span> {getSortIcon(\"lastOrderedDate\")} </span>\r\n                        </TableHead>\r\n                        <TableHead >\r\n                            <span className='items-center gap-1' onClick={() => handleSort(\"fbDiscount\")}>\r\n                                <span className='flex flex-row items-center gap-1'><span>Freq Buyer Discount</span> {getSortIcon(\"fbDiscount\")} </span>\r\n                            </span>\r\n                        </TableHead>\r\n\r\n                    </TableRow>\r\n                </TableHeader>\r\n\r\n                <TableBody>\r\n                    {filteredCustomers.map((customer) => {\r\n                        const isHidden = hiddenCustomers.includes(customer.buyerId)\r\n\r\n\r\n\r\n                        return (\r\n                            <TableRow key={customer.buyerId}>\r\n\r\n\r\n                                <TableCell>\r\n                                    <div className='flex flex-row gap-2  items-center'>\r\n\r\n                                        <Link to={`/home/<USER>/${customer.buyerId}`}\r\n                                            className=\"text-blue-600 hover:underline\">\r\n                                            <div>{customer.buyerName !== \"\" ? customer.buyerName : \"( Name not given )\"}</div>\r\n                                        </Link>\r\n                                        <Dialog>\r\n                                            <DialogTrigger asChild>\r\n                                                <Info size={18} onClick={handleInfoOpen} className=\"cursor-pointer text-gray-600 hover:text-purple-600 transition-all\" />\r\n                                            </DialogTrigger>\r\n                                            <DialogContent className=\"sm:max-w-fit p-5 rounded-lg\">\r\n                                                <DialogHeader>\r\n                                                    <DialogTitle className=\"text-lg font-semibold text-gray-800\">About Customer</DialogTitle>\r\n                                                </DialogHeader>\r\n                                                <div className=\"flex flex-col gap-3 mt-2\">\r\n                                                    <div className=\"flex flex-row gap-2\">\r\n                                                        <span className=\"text-sm text-gray-600\">Buyer Name:</span>\r\n                                                        <span className=\"text-base text-purple-800 font-semibold\">{customer.buyerName}</span>\r\n                                                    </div>\r\n                                                    <div className=\"flex flex-row gap-2\">\r\n                                                        <span className=\"text-sm text-gray-600\">Mobile Number:</span>\r\n                                                        <span className=\"text-base text-purple-800 font-semibold\">{customer.mobileNumber}</span>\r\n                                                    </div>\r\n                                                </div>\r\n                                            </DialogContent>\r\n                                        </Dialog>\r\n\r\n                                    </div>\r\n\r\n                                </TableCell>\r\n                                {/* <TableCell>xxxxxxxxxxxxx</TableCell>\r\n                                <TableCell>xxxxxxxxxxxxx</TableCell> */}\r\n                                {/* <TableCell>{customer.mobileNumber}</TableCell> */}\r\n                                <TableCell>{customer.totalOrders}</TableCell>\r\n                                <TableCell>₹ {customer.pendingAmount.toLocaleString('en-IN')}</TableCell>\r\n                                <TableCell>\r\n                                    {customer.lastOrderedDate\r\n                                        ? (isNaN(new Date(customer.lastOrderedDate).getTime())\r\n                                            ? '-'\r\n                                            : Math.floor((new Date().getTime() - new Date(customer.lastOrderedDate).getTime()) / (1000 * 60 * 60 * 24)) + ' days')\r\n                                        : '-'}\r\n                                </TableCell>\r\n                                <TableCell\r\n                                    className={`${customer.contractPriceExpDate && new Date(customer.contractPriceExpDate.split(\"-\").reverse().join(\"-\")) < new Date() ? \"bg-orange-500\" : \"\"} ${customer.contractPriceExpDate && new Date(customer.contractPriceExpDate.split(\"-\").reverse().join(\"-\")) < new Date() ? 'text-blue-500' : 'text-gray-500'} ${customer.contractPriceExpDate && new Date(customer.contractPriceExpDate.split(\"-\").reverse().join(\"-\")) < new Date() ? 'rounded-full' : ''}`}\r\n                                >\r\n                                    {customer.contractPriceExpDate || \"-\"}\r\n                                </TableCell>\r\n                                <TableCell className='flex flex-row items-center gap-2'>\r\n                                    {isFbDis[customer.nBuyerId] ? (\r\n                                        <div className=\"flex flex-row justify-center items-center gap-3\">\r\n                                            <Input\r\n                                                value={String(fbDiscounts[customer.nBuyerId] ?? customer.fbDiscount ?? \"\")}\r\n                                                onChange={(e) => {\r\n                                                    let val = e.target.value;\r\n\r\n                                                    // Allow only numbers with optional decimals\r\n                                                    if (!/^\\d*\\.?\\d*$/.test(val)) return;\r\n\r\n                                                    // Convert to number for validation\r\n                                                    let numVal = parseFloat(val);\r\n\r\n                                                    // Enforce range (0 to 100)\r\n                                                    if (numVal > 100) numVal = 100;\r\n                                                    if (numVal < 0 || isNaN(numVal)) numVal = 0;\r\n\r\n                                                    handleChangeFbDiscount(customer.nBuyerId, String(numVal));\r\n                                                }}\r\n                                                disabled={!isFbDis[customer.nBuyerId]}\r\n                                                type=\"number\"\r\n                                                min=\"0\"\r\n                                                max=\"100\"\r\n                                                step=\"0.01\" // Allows decimals\r\n                                            />\r\n                                            <Save\r\n                                                size={24}\r\n                                                onClick={() => handleSave(customer.nBuyerId)}\r\n                                                className=\"cursor-pointer text-green-500\"\r\n                                            />\r\n                                            <X\r\n                                                color=\"red\"\r\n                                                size={24}\r\n                                                className=\"cursor-pointer text-red-500\"\r\n                                                onClick={() => setIsFbDis({})} // Close all inputs\r\n                                            />\r\n                                        </div>\r\n\r\n                                    ) : (\r\n                                        <div className='flex flex-row gap-3 items-center justify-center'>\r\n                                            <span>{customer.fbDiscount > 0 ? `${customer.fbDiscount} %` : \"-\"}</span>\r\n                                            <Pencil\r\n                                                size={15}\r\n                                                onClick={() => setIsFbDis({ [customer.nBuyerId]: true })} // Open only this row\r\n                                                className=\"cursor-pointer text-blue-500\"\r\n                                            />\r\n                                        </div>\r\n                                    )}\r\n                                </TableCell>\r\n\r\n                                {/* <TableCell>\r\n                                    <div className=\"flex gap-2\">\r\n                                        <Dialog>\r\n                                            <DialogTrigger asChild>\r\n                                                <Button\r\n                                                    variant=\"ghost\"\r\n                                                    size=\"icon\"\r\n                                                    onClick={() => setSelectedCustomer(customer)}\r\n                                                >\r\n                                                    <Send className=\"h-4 w-4\" />\r\n                                                </Button>\r\n                                            </DialogTrigger>\r\n                                            <DialogContent>\r\n                                                <DialogHeader>\r\n                                                    <DialogTitle>Send WhatsApp Message</DialogTitle>\r\n                                                </DialogHeader>\r\n                                                <Form method=\"post\">\r\n                                                    <input\r\n                                                        type=\"hidden\"\r\n                                                        name=\"phoneNo\"\r\n                                                        value={selectedCustomer?.mobileNumber || ''}\r\n                                                    />\r\n                                                    <div className=\"py-4\">\r\n                                                        <h3 className=\"mb-2 font-semibold\">Select a template:</h3>\r\n                                                        <Select\r\n                                                            value={selectedTemplate}\r\n                                                            onValueChange={setSelectedTemplate}\r\n                                                        >\r\n                                                            <SelectTrigger>\r\n                                                                <SelectValue placeholder=\"Choose a template\" />\r\n                                                            </SelectTrigger>\r\n                                                            <SelectContent>\r\n                                                                {templates.map((template) => (\r\n                                                                    <SelectItem key={template.name}\r\n                                                                        value={template.name}>\r\n                                                                        {template.displayName}\r\n                                                                    </SelectItem>\r\n                                                                ))}\r\n                                                            </SelectContent>\r\n                                                        </Select>\r\n                                                        <input\r\n                                                            type=\"hidden\"\r\n                                                            name=\"templateName\"\r\n                                                            value={selectedTemplate}\r\n                                                        />\r\n                                                    </div>\r\n                                                    <div className=\"flex justify-end\">\r\n                                                        <Button\r\n                                                            type=\"submit\"\r\n                                                            disabled={!selectedTemplate}\r\n                                                        >\r\n                                                            Send Message\r\n                                                        </Button>\r\n                                                    </div>\r\n                                                </Form>\r\n                                            </DialogContent>\r\n                                        </Dialog>\r\n                                        <Button variant=\"ghost\" size=\"icon\"\r\n                                            onClick={() => toggleHidden(customer.buyerId)}>\r\n                                            {isHidden ? <EyeOff className=\"h-4 w-4\" /> : <Eye className=\"h-4 w-4\" />}\r\n                                        </Button>\r\n                                        <Button variant=\"ghost\" size=\"icon\">\r\n                                            <Edit className=\"h-4 w-4\" />\r\n                                        </Button>\r\n                                    </div>\r\n                                </TableCell> */}\r\n                            </TableRow>\r\n                        )\r\n                    })}\r\n                </TableBody>\r\n            </Table>\r\n            <Pagination currentPage={currentPage} hasMoreData={hasMoreData}\r\n                tabValue={activeTab}\r\n                sortBy={sortBy}\r\n                searchBy={searchTerm}\r\n                sortByOrder={sortOrder}\r\n            />\r\n\r\n        </div>\r\n    )\r\n}\r\n"], "names": ["jsxs", "jsx", "CustomersSection", "data", "customers", "currentPage", "hasNextPage", "hasMoreData", "sortByvalue", "useLoaderData", "searchTerm", "setSearchTerm", "useState", "sortBy", "setSortBy", "sortOrder", "setSortOrder", "activeTab", "setActiveTab", "navigate", "useNavigate", "hiddenCustomers", "setHiddenCustomers", "selectedCustomer", "setSelectedCustomer", "selectedTemplate", "setSelectedTemplate", "selected<PERSON>uyerData", "setSelectedBuyerData", "handleTabChange", "newTab", "tabMap", "all", "oneOrder", "frequent", "zero_orders", "validTabValue", "console", "log", "handleSort", "newSort", "prevSortBy", "isSameSort", "prevSortOrder", "newOrder", "getSortIcon", "column", "ArrowUp", "className", "ArrowDown", "handleSearch", "e", "value", "target", "searchParam", "length", "filteredCustomers", "fbDiscounts", "setFbDiscounts", "isFbDis", "setIsFbDis", "handleChangeFbDiscount", "nBuyerId", "val", "test", "prev", "fb<PERSON><PERSON><PERSON>", "useFetcher", "handleSave", "formData", "FormData", "append", "toString", "submit", "method", "<PERSON><PERSON><PERSON><PERSON>", "find", "customer", "discount", "Number", "infoOpen", "setInfoOpen", "handleInfoOpen", "children", "TooltipProvider", "Tabs", "onValueChange", "TabsList", "<PERSON><PERSON><PERSON>", "TooltipTrigger", "<PERSON><PERSON><PERSON><PERSON>", "TabsTrigger", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Input", "placeholder", "onChange", "Select", "SelectTrigger", "SelectValue", "SelectContent", "SelectItem", "Table", "state", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "loading", "TableHeader", "TableRow", "TableHead", "onClick", "TableBody", "map", "includes", "buyerId", "TableCell", "Link", "to", "buyerName", "Dialog", "DialogTrigger", "Info", "size", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogHeader", "DialogTitle", "mobileNumber", "totalOrders", "pendingAmount", "toLocaleString", "lastOrderedDate", "isNaN", "Date", "getTime", "Math", "floor", "contractPriceExpDate", "split", "reverse", "join", "String", "fbDiscount", "numVal", "parseFloat", "disabled", "type", "min", "max", "step", "Save", "X", "color", "Pencil", "Pagination", "tabValue", "searchBy", "sortByOrder"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUA,MAAM,aAAwC,CAAC,EAAE,aAAa,aAAY,UAAS,QAAO,UAAS,kBAAkB;AACnH,QAAM,cAAc,cAAc;AAClC,QAAM,kBAAkB;AAClB,QAAA,WAAW,cAAc,cAAc,IAAI;AAEjD,QAAM,iBAAiB,MAA2B;AAChD,UAAM,cAAmC,CAAC;AAE1C,QAAI,cAAc,GAAG;AACnB,kBAAY,KAAK,CAAC;AAClB,UAAI,cAAc,EAAe,aAAA,KAAK,KAAK;AAAA,IAAA;AAG7C,aACM,IAAI,KAAK,IAAI,GAAG,cAAc,CAAC,GACnC,KAAM,KAAK,IAAI,cAAc,GAAG,cAAc,iBAAgB,QAAQ,GACtE,KACA;AACA,kBAAY,KAAK,CAAC;AAAA,IAAA;AAGpB,QAAI,aAAa;AACf,UAAI,cAAc,IAAI,cAAc,gBAAiB,aAAY,KAAK,KAAK;AAAA,IAAA;AAEtE,WAAA;AAAA,EACT;AAEE,SAAAA,kCAAA,KAAC,OAAI,EAAA,WAAU,+CAEZ,UAAA;AAAA,IAAA,cAAc,KACZA,kCAAA,KAAA,MAAA,EAAK,QAAO,OAAM,WAAU,UAC3B,UAAA;AAAA,MAAAC,sCAAC,WAAM,MAAK,UAAS,MAAK,QAAO,OAAO,cAAc,GAAG;AAAA,4CACxD,SAAM,EAAA,MAAK,UAAS,MAAK,YAAW,OAAO,UAAU;AAAA,4CACrD,SAAM,EAAA,MAAK,UAAS,MAAK,UAAS,OAAO,QAAQ;AAAA,4CACjD,SAAM,EAAA,MAAK,UAAS,MAAK,UAAS,OAAO,UAAU;AAAA,4CACnD,SAAM,EAAA,MAAK,UAAS,MAAK,UAAS,OAAO,aAAa;AAAA,MACvDA,kCAAA;AAAA,QAAC;AAAA,QAAA;AAAA,UACC,MAAK;AAAA,UACL,WAAU;AAAA,UACX,UAAA;AAAA,QAAA;AAAA,MAAA;AAAA,IAED,GACF;AAAA,IAGD,eAAiB,EAAA;AAAA,MAAI,CAAC,MAAM,UAC3B,SAAS,8CACN,QAAiB,EAAA,WAAU,2BAA0B,UAAA,MAAA,GAA3C,KAEX,IAEAD,kCAAA,KAAC,QAAK,QAAO,OAAkB,WAAU,UACvC,UAAA;AAAA,QAAAC,sCAAC,WAAM,MAAK,UAAS,MAAK,QAAO,OAAQ,OAAkB,GAAG;AAAA,8CAC7D,SAAM,EAAA,MAAK,UAAS,MAAK,YAAW,OAAO,UAAU;AAAA,8CACrD,SAAM,EAAA,MAAK,UAAS,MAAK,UAAS,OAAO,QAAQ;AAAA,8CACjD,SAAM,EAAA,MAAK,UAAS,MAAK,UAAS,OAAO,UAAU;AAAA,8CACnD,SAAM,EAAA,MAAK,UAAS,MAAK,UAAS,OAAO,aAAa;AAAA,QACvDA,kCAAA;AAAA,UAAC;AAAA,UAAA;AAAA,YACC,MAAK;AAAA,YACL,WAAW,0BACT,SAAS,cACL,0BACA,4CACN;AAAA,YAEC,UAAA;AAAA,UAAA;AAAA,QAAA;AAAA,MACH,EAAA,GAfsB,KAgBxB;AAAA,IAEJ;AAAA,IAEC,eACED,kCAAAA,KAAA,MAAA,EAAK,QAAO,OAAM,WAAU,UAC3B,UAAA;AAAA,MAAAC,sCAAC,WAAM,MAAK,UAAS,MAAK,QAAO,OAAO,cAAc,GAAG;AAAA,4CACxD,SAAM,EAAA,MAAK,UAAS,MAAK,YAAW,OAAO,UAAU;AAAA,4CACnD,SAAM,EAAA,MAAK,UAAS,MAAK,UAAS,OAAO,QAAQ;AAAA,4CACjD,SAAM,EAAA,MAAK,UAAS,MAAK,UAAS,OAAO,UAAU;AAAA,4CACnD,SAAM,EAAA,MAAK,UAAS,MAAK,UAAS,OAAO,aAAa;AAAA,MACzDA,kCAAA;AAAA,QAAC;AAAA,QAAA;AAAA,UACC,MAAK;AAAA,UACL,WAAU;AAAA,UACX,UAAA;AAAA,QAAA;AAAA,MAAA;AAAA,IAED,EACF,CAAA;AAAA,EAAA,GAEJ;AAEJ;ACHA,SAAwBC,mBAAmB;AACjC,QAAA;AAAA,IAAEC,MAAMC;AAAAA,IAAWC;AAAAA,IAAaC;AAAAA,IAAaC;AAAAA,IAAaC;AAAAA,MAAgBC,cAA0B;AAC1G,QAAM,CAACC,YAAYC,aAAa,IAAIC,aAAAA,SAAS,EAAE;AAC/C,QAAM,CAACC,QAAQC,SAAS,IAAIF,aAAAA,SAAS,WAAW;AAChD,QAAM,CAACG,WAAWC,YAAY,IAAIJ,aAAAA,SAAS,KAAK;AAChD,QAAM,CAACK,WAAWC,YAAY,IAAIN,aAAAA,SAAS,KAAK;AAChD,QAAMO,WAAWC,YAAY;AAC7B,QAAM,CAACC,iBAAiBC,kBAAkB,IAAIV,aAAAA,SAAmB,CAAA,CAAE;AACnE,QAAM,CAACW,kBAAkBC,mBAAmB,IAAIZ,aAAAA,SAAiD,IAAI;AACrG,QAAM,CAACa,kBAAkBC,mBAAmB,IAAId,aAAAA,SAAS,EAAE;AAC3D,QAAM,CAACe,mBAAmBC,oBAAoB,IAAIhB,aAAAA,SAAiD,IAAI;AAMjG,QAAAiB,kBAAmBC,YAAmB;AAExC,UAAMC,SAAoC;AAAA,MACtCC,KAAK;AAAA,MACLC,UAAU;AAAA,MACVC,UAAU;AAAA,MACVC,aAAa;AAAA,IACjB;AAEM,UAAAC,gBAAgBL,OAAOD,MAAM,KAAK;AACxCZ,iBAAaY,MAAM;AACXO,YAAAC,IAAIR,QAAQ,WAAW;AAG/BX,aAAS,aAAaiB,aAAa,kBAAkBvB,MAAM,gBAAgBE,SAAS,EAAE;AAAA,EAC1F;AAEM,QAAAwB,aAAcC,aAAoB;AACpC1B,cAAW2B,gBAAe;AACtB,YAAMC,aAAaD,eAAeD;AAC1BH,cAAAC,IAAIrB,WAAW,WAAW;AAElCD,mBAAc2B,mBAAkB;AAC5B,cAAMC,WAAWF,cAAcC,kBAAkB,QAAQ,SAAS;AAEzDxB,iBAAA,aAAaF,SAAS,kBAAkBuB,OAAO,gBAAgBI,QAAQ,aAAalC,UAAU,EAAE;AAElG,eAAAkC;AAAAA,MACX,CAAC;AAEM,aAAAJ;AAAAA,IACX,CAAC;AAAA,EACL;AAEM,QAAAK,cAAeC,YAAmB;AAChC,QAAAjC,WAAWiC,OAAe,QAAA;AACvB,WAAA/B,cAAc,QAAQd,kCAAA,IAAC8C,SAAQ;AAAA,MAAAC,WAAU;AAAA,KAAe,IAAK/C,kCAAA,IAACgD,WAAU;AAAA,MAAAD,WAAU;AAAA,IAAe,CAAA;AAAA,EAC5G;AACM,QAAAE,eAAgBC,OAA2C;AACvD,UAAAC,QAAQD,EAAEE,OAAOD;AACvBzC,kBAAcyC,KAAK;AACnB,UAAME,cAAcF,MAAMG,UAAU,IAAI,GAAGH,KAAK,KAAK;AAC5CjC,aAAA,aAAaF,SAAS,kBAAkBJ,MAAM,gBAAgBE,SAAS,aAAauC,WAAW,EAAE;AAAA,EAC9G;AAQA,QAAME,oBAAoBpD;AAE1B,QAAM,CAACqD,aAAaC,cAAc,IAAI9C,aAAAA,SAAoC,CAAA,CAAE;AAC5E,QAAM,CAAC+C,SAASC,UAAU,IAAIhD,aAAAA,SAAqC,CAAA,CAAE;AAE/D,QAAAiD,yBAAyBA,CAACC,UAAkBC,QAAgB;AAC9D,QAAI,cAAcC,KAAKD,GAAG,KAAKA,QAAQ,IAAI;AACxBL,qBAACO,WAAU;AAAA,QAAE,GAAGA;AAAAA,QAAM,CAACH,QAAQ,GAAGC;AAAAA,MAAI,EAAE;AAAA,IAC3D;AAAA,EACJ;AAMA,QAAMG,YAAYC,WAA4C;AAGxD,QAAAC,aAAcN,cAAsB;AAChC,UAAAO,WAAW,IAAIC,SAAS;AACrBD,aAAAE,OAAO,WAAW,kBAAkB;AAC7CF,aAASE,OAAO,YAAYT,SAASU,SAAA,CAAU;AAC/CH,aAASE,OAAO,cAAcd,YAAYK,QAAQ,CAAC;AACnDI,cAAUO,OAAOJ,UAAU;AAAA,MAAEK,QAAQ;AAAA,IAAO,CAAC;AAC7CrC,YAAQC,IAAI,gCAAgCwB,QAAQ,KAAKL,YAAYK,QAAQ,CAAC;AAC9E,UAAMa,eAAevE,UAAUwE,KAAiBC,cAAAA,SAASf,aAAaA,QAAQ;AAC9E,QAAIa,cAAc;AACO/C,2BAAA;AAAA,QAAE,GAAG+C;AAAAA,QAAcG,UAAUC,OAAOtB,YAAYK,QAAQ,CAAC;AAAA,MAAE,CAAC;AAAA,IACrF;AAEWF,eAACK,WAAU;AAAA,MAAE,GAAGA;AAAAA,MAAM,CAACH,QAAQ,GAAG;AAAA,IAAM,EAAE;AAAA,EACzD;AACA,QAAM,CAACkB,UAAUC,WAAW,IAAIrE,aAAAA,SAAS,KAAK;AAC9C,QAAMsE,iBAAiBA,MAAM;AACzBD,gBAAY,IAAI;AAAA,EACpB;AAOI,SAAAjF,kCAAAA,KAAC,OAAI;AAAA,IAAAgD,WAAU;AAAA,IACXmC,UAAA,CAAClF,kCAAA,IAAA,OAAA;AAAA,MAAI+C,WAAU;AAAA,MACXmC,UAAAlF,kCAAA,IAAC;QAAG+C,WAAU;AAAA,QAAqBmC;MAAY,CAAA;AAAA,IAYnD,CAAA,GAIAlF,kCAAA,IAACmF,iBACG;AAAA,MAAAD,UAAAlF,kCAAA,IAACoF,MAAK;AAAA,QAAAjC,OAAOnC;AAAAA,QAAWqE,eAAezD;AAAAA,QAAiBmB,WAAU;AAAA,QAC9DmC,UAAAnF,kCAAA,KAACuF,UACG;AAAA,UAAAJ,UAAA,CAAAnF,kCAAA,KAACwF,SACG;AAAA,YAAAL,UAAA,CAAAlF,kCAAA,IAACwF,gBAAe;AAAA,cAAAC,SAAO;AAAA,cAEnBP,UAAAlF,kCAAA,IAAC,OAAI;AAAA,gBAAA+C,WAAU;AAAA,gBACXmC,UAAAlF,kCAAA,IAAC0F,aAAY;AAAA,kBAAAvC,OAAM;AAAA,kBAAM+B,UAAA;AAAA,gBAAG,CAAA;AAAA,cAChC,CAAA;AAAA,YACJ,CAAA,GACAlF,kCAAA,IAAC2F;cAAeT,UAAa;AAAA,YAAA,CAAA,CAAA;AAAA,UACjC,CAAA,0CAECK,SACG;AAAA,YAAAL,UAAA,CAAAlF,kCAAA,IAACwF,gBAAe;AAAA,cAAAC,SAAO;AAAA,cACnBP,UAAAlF,kCAAA,IAAC,OAAI;AAAA,gBAAA+C,WAAU;AAAA,gBACXmC,UAAAlF,kCAAA,IAAC0F,aAAY;AAAA,kBAAAvC,OAAM;AAAA,kBAAW+B,UAAA;AAAA,gBAAkB,CAAA;AAAA,cACpD,CAAA;AAAA,YACJ,CAAA,GACAlF,kCAAA,IAAC2F;cAAeT,UAAkE;AAAA,YAAA,CAAA,CAAA;AAAA,UACtF,CAAA,0CAECK,SACG;AAAA,YAAAL,UAAA,CAAAlF,kCAAA,IAACwF,gBAAe;AAAA,cAAAC,SAAO;AAAA,cACnBP,UAAAlF,kCAAA,IAAC,OAAI;AAAA,gBAAA+C,WAAU;AAAA,gBACXmC,UAAAlF,kCAAA,IAAC0F,aAAY;AAAA,kBAAAvC,OAAM;AAAA,kBAAW+B,UAAA;AAAA,gBAAmB,CAAA;AAAA,cACrD,CAAA;AAAA,YACJ,CAAA,GACAlF,kCAAA,IAAC2F;cAAeT,UAA0D;AAAA,YAAA,CAAA,CAAA;AAAA,UAC9E,CAAA,0CAECK,SACG;AAAA,YAAAL,UAAA,CAAAlF,kCAAA,IAACwF,gBAAe;AAAA,cAAAC,SAAO;AAAA,cACnBP,UAAAlF,kCAAA,IAAC,OAAI;AAAA,gBAAA+C,WAAU;AAAA,gBACXmC,UAAAlF,kCAAA,IAAC0F,aAAY;AAAA,kBAAAvC,OAAM;AAAA,kBAAc+B,UAAA;AAAA,gBAAc,CAAA;AAAA,cACnD,CAAA;AAAA,YACJ,CAAA,GACAlF,kCAAA,IAAC2F;cAAeT,UAA6C;AAAA,YAAA,CAAA,CAAA;AAAA,UACjE,CAAA,CAAA;AAAA,QAGJ,CAAA;AAAA,MACJ,CAAA;AAAA,IACJ,CAAA,GACAnF,kCAAA,KAAC,OAAI;AAAA,MAAAgD,WAAU;AAAA,MACXmC,UAAA,CAAAlF,kCAAA,IAAC4F,OAAA;AAAA,QACGC,aAAY;AAAA,QACZ1C,OAAO1C;AAAAA,QACPqF,UAAUrF,WAAW6C,UAAU,IAAIL,eAAgBC,OAAMxC,cAAcwC,EAAEE,OAAOD,KAAK;AAAA,QACrFJ,WAAU;AAAA,MAAA,CACd,GACChD,kCAAA,KAAAgG,QAAA;AAAA,QAAO5C,OAAOvC;AAAAA,QAAQyE,eAAe/C;AAAAA,QAClC4C,UAAA,CAAAlF,kCAAA,IAACgG;UAAcjD,WAAU;AAAA,UACrBmC,gDAACe,aAAY;AAAA,YAAAJ,aAAY;AAAA,UAAU,CAAA;AAAA,QACvC,CAAA,0CACCK,eACG;AAAA,UAAAhB,UAAA,CAAClF,kCAAA,IAAAmG,YAAA;AAAA,YAAWhD,OAAM;AAAA,YAAY+B,UAAI;AAAA,UAAA,CAAA,GACjClF,kCAAA,IAAAmG,YAAA;AAAA,YAAWhD,OAAM;AAAA,YAAc+B,UAAgB;AAAA,UAAA,CAAA,GAC/ClF,kCAAA,IAAAmG,YAAA;AAAA,YAAWhD,OAAM;AAAA,YAAgB+B,UAAe;AAAA,UAAA,CAAA,GAChDlF,kCAAA,IAAAmG,YAAA;AAAA,YAAWhD,OAAM;AAAA,YAAkB+B,UAAmB;AAAA,UAAA,CAAA,GACtDlF,kCAAA,IAAAmG,YAAA;AAAA,YAAWhD,OAAM;AAAA,YAAa+B,UAAsB;AAAA,UAAA,CAAA,GACpDlF,kCAAA,IAAAmG,YAAA;AAAA,YAAWhD,OAAM;AAAA,YAAa+B,UAAY;AAAA,UAAA,CAAA,CAAA;AAAA,QAE/C,CAAA,CAAA;AAAA,MACJ,CAAA,CAAA;AAAA,IACJ,CAAA,0CACCkB,OACI;AAAA,MAAAlB,UAAA,CAAAjB,UAAUoC,UAAU,UAChBrG,kCAAAA,IAAAsG,eAAA;AAAA,QAAcC,SAAS;AAAA,MAAM,CAAA,GAClCvG,kCAAA,IAACwG,aACG;AAAA,QAAAtB,UAAAnF,kCAAA,KAAC0G,UACG;AAAA,UAAAvB,UAAA,CAAClF,kCAAA,IAAA0G,WAAA;AAAA,YAAU3D,WAAU;AAAA,YAAkB4D,SAASA,MAAMrE,WAAW,WAAW;AAAA,YACxE4C,UAAAnF,kCAAA,KAAC,QAAK;AAAA,cAAAgD,WAAU;AAAA,cAAmCmC,UAAA,CAAA,KAAClF,kCAAA,IAAC;gBAAKkF,UAAI;AAAA,eAAA,GAAO,KAAElF,kCAAA,IAAA,QAAA;AAAA,gBAAMkF,UAAYtC,YAAA,WAAW;AAAA,cAAE,CAAA,CAAA;AAAA,YAAO,CAAA;AAAA,UACjH,CAAA,GASC5C,kCAAA,IAAA0G,WAAA;AAAA,YAAU3D,WAAU;AAAA,YAAoB4D,SAASA,MAAMrE,WAAW,aAAa;AAAA,YAC5E4C,UAAAnF,kCAAA,KAAC,QAAK;AAAA,cAAAgD,WAAU;AAAA,cAAmCmC,UAAA,CAAA,KAAClF,kCAAA,IAAC;gBAAKkF,UAAa;AAAA,cAAA,CAAA,GAAO,KAAEtC,YAAY,aAAa,GAAE,GAAA;AAAA,YAAC,CAAA;AAAA,UAChH,CAAA,GAEC5C,kCAAA,IAAA0G,WAAA;AAAA,YAAU3D,WAAU;AAAA,YAAkB4D,SAASA,MAAMrE,WAAW,eAAe;AAAA,YAC5E4C,UAAAnF,kCAAA,KAAC,QAAK;AAAA,cAAAgD,WAAU;AAAA,cAAmCmC,UAAA,CAAA,MAAElF,kCAAA,IAAC;gBAAKkF,UAAe;AAAA,cAAA,CAAA,GAAO,KAAEtC,YAAY,eAAe,GAAE,GAAA;AAAA,YAAC,CAAA;AAAA,UACrH,CAAA,GAEC5C,kCAAA,IAAA0G,WAAA;AAAA,YAAU3D,WAAU;AAAA,YAAqB4D,SAASA,MAAMrE,WAAW,iBAAiB;AAAA,YACjF4C,UAAAnF,kCAAA,KAAC,QAAK;AAAA,cAAAgD,WAAU;AAAA,cAAmCmC,UAAA,CAAA,MAAElF,kCAAA,IAAC;gBAAKkF,UAAmB;AAAA,cAAA,CAAA,GAAO,KAAEtC,YAAY,iBAAiB,GAAE,GAAA;AAAA,YAAC,CAAA;AAAA,UAC3H,CAAA,GACC5C,kCAAA,IAAA0G,WAAA;AAAA,YAAU3D,WAAU;AAAA,YAAqB4D,SAASA,MAAMrE,WAAW,iBAAiB;AAAA,YACjF4C,UAAAnF,kCAAA,KAAC,QAAK;AAAA,cAAAgD,WAAU;AAAA,cAAmCmC,UAAA,CAAA,MAAElF,kCAAA,IAAC;gBAAKkF,UAAsB;AAAA,cAAA,CAAA,GAAO,KAAEtC,YAAY,iBAAiB,GAAE,GAAA;AAAA,YAAC,CAAA;AAAA,UAC9H,CAAA,GACC5C,kCAAA,IAAA0G,WAAA;AAAA,YACGxB,UAAClF,kCAAA,IAAA,QAAA;AAAA,cAAK+C,WAAU;AAAA,cAAqB4D,SAASA,MAAMrE,WAAW,YAAY;AAAA,cACvE4C,UAACnF,kCAAA,KAAA,QAAA;AAAA,gBAAKgD,WAAU;AAAA,gBAAmCmC,UAAA,CAAAlF,kCAAA,IAAC;kBAAKkF,UAAmB;AAAA,gBAAA,CAAA,GAAO,KAAEtC,YAAY,YAAY,GAAE,GAAA;AAAA,cAAC,CAAA;AAAA,YACpH,CAAA;AAAA,UACJ,CAAA,CAAA;AAAA,QAEJ,CAAA;AAAA,MACJ,CAAA,GAEC5C,kCAAA,IAAA4G,WAAA;AAAA,QACI1B,UAAkB3B,kBAAAsD,IAAKjC,cAAa;AAChBxD,0BAAgB0F,SAASlC,SAASmC,OAAO;AAI1D,wDACKN,UAGG;AAAA,YAAAvB,UAAA,CAAAlF,kCAAA,IAACgH,WACG;AAAA,cAAA9B,UAAAnF,kCAAA,KAAC,OAAI;AAAA,gBAAAgD,WAAU;AAAA,gBAEXmC,UAAA,CAAAlF,kCAAA,IAACiH,MAAA;AAAA,kBAAKC,IAAI,0BAA0BtC,SAASmC,OAAO;AAAA,kBAChDhE,WAAU;AAAA,kBACVmC,gDAAC,OAAK;AAAA,oBAAAA,UAAAN,SAASuC,cAAc,KAAKvC,SAASuC,YAAY;AAAA,kBAAqB,CAAA;AAAA,gBAAA,CAChF,0CACCC,QACG;AAAA,kBAAAlC,UAAA,CAAClF,kCAAA,IAAAqH,eAAA;AAAA,oBAAc5B,SAAO;AAAA,oBAClBP,UAAClF,kCAAA,IAAAsH,MAAA;AAAA,sBAAKC,MAAM;AAAA,sBAAIZ,SAAS1B;AAAAA,sBAAgBlC,WAAU;AAAA,oBAAoE,CAAA;AAAA,kBAC3H,CAAA,GACAhD,kCAAA,KAACyH,eAAc;AAAA,oBAAAzE,WAAU;AAAA,oBACrBmC,UAAA,CAAAlF,kCAAA,IAACyH;sBACGvC,UAAClF,kCAAA,IAAA0H,aAAA;AAAA,wBAAY3E,WAAU;AAAA,wBAAsCmC;sBAAc,CAAA;AAAA,oBAC/E,CAAA,GACAnF,kCAAA,KAAC,OAAI;AAAA,sBAAAgD,WAAU;AAAA,sBACXmC,UAAA,CAACnF,kCAAA,KAAA,OAAA;AAAA,wBAAIgD,WAAU;AAAA,wBACXmC,UAAA,CAAClF,kCAAA,IAAA,QAAA;AAAA,0BAAK+C,WAAU;AAAA,0BAAwBmC,UAAW;AAAA,wBAAA,CAAA,GAClDlF,kCAAA,IAAA,QAAA;AAAA,0BAAK+C,WAAU;AAAA,0BAA2CmC,mBAASiC;AAAAA,wBAAU,CAAA,CAAA;AAAA,sBAClF,CAAA,GACApH,kCAAA,KAAC,OAAI;AAAA,wBAAAgD,WAAU;AAAA,wBACXmC,UAAA,CAAClF,kCAAA,IAAA,QAAA;AAAA,0BAAK+C,WAAU;AAAA,0BAAwBmC,UAAc;AAAA,wBAAA,CAAA,GACrDlF,kCAAA,IAAA,QAAA;AAAA,0BAAK+C,WAAU;AAAA,0BAA2CmC,mBAASyC;AAAAA,wBAAa,CAAA,CAAA;AAAA,sBACrF,CAAA,CAAA;AAAA,oBACJ,CAAA,CAAA;AAAA,kBACJ,CAAA,CAAA;AAAA,gBACJ,CAAA,CAAA;AAAA,cAEJ,CAAA;AAAA,YAEJ,CAAA,GAIA3H,kCAAA,IAACgH,WAAW;AAAA,cAAA9B,UAAAN,SAASgD;AAAAA,YAAY,CAAA,0CAChCZ,WAAU;AAAA,cAAA9B,UAAA,CAAA,MAAGN,SAASiD,cAAcC,eAAe,OAAO,CAAA;AAAA,YAAE,CAAA,GAC5D9H,kCAAA,IAAAgH,WAAA;AAAA,cACI9B,UAASN,SAAAmD,kBACHC,MAAM,IAAIC,KAAKrD,SAASmD,eAAe,EAAEG,QAAQ,CAAC,IAC/C,MACAC,KAAKC,QAAO,oBAAIH,KAAK,GAAEC,QAAQ,IAAI,IAAID,KAAKrD,SAASmD,eAAe,EAAEG,QAAQ,MAAM,MAAO,KAAK,KAAK,GAAG,IAAI,UAChH;AAAA,YACV,CAAA,GACAlI,kCAAA,IAACgH,WAAA;AAAA,cACGjE,WAAW,GAAG6B,SAASyD,wBAAwB,IAAIJ,KAAKrD,SAASyD,qBAAqBC,MAAM,GAAG,EAAEC,QAAU,EAAAC,KAAK,GAAG,CAAC,IAAI,oBAAIP,KAAK,IAAI,kBAAkB,EAAE,IAAIrD,SAASyD,wBAAwB,IAAIJ,KAAKrD,SAASyD,qBAAqBC,MAAM,GAAG,EAAEC,QAAU,EAAAC,KAAK,GAAG,CAAC,IAAI,oBAAIP,KAAK,IAAI,kBAAkB,eAAe,IAAIrD,SAASyD,wBAAwB,IAAIJ,KAAKrD,SAASyD,qBAAqBC,MAAM,GAAG,EAAEC,QAAU,EAAAC,KAAK,GAAG,CAAC,IAAI,oBAAIP,KAAK,IAAI,iBAAiB,EAAE;AAAA,cAElc/C,mBAASmD,wBAAwB;AAAA,YAAA,CACtC,GACArI,kCAAA,IAACgH,WAAU;AAAA,cAAAjE,WAAU;AAAA,cAChBmC,UAAAxB,QAAQkB,SAASf,QAAQ,IACtB9D,kCAAAA,KAAC,OAAI;AAAA,gBAAAgD,WAAU;AAAA,gBACXmC,UAAA,CAAAlF,kCAAA,IAAC4F,OAAA;AAAA,kBACGzC,OAAOsF,OAAOjF,YAAYoB,SAASf,QAAQ,KAAKe,SAAS8D,cAAc,EAAE;AAAA,kBACzE5C,UAAW5C,OAAM;AACT,wBAAAY,MAAMZ,EAAEE,OAAOD;AAGnB,wBAAI,CAAC,cAAcY,KAAKD,GAAG,EAAG;AAG1B,wBAAA6E,SAASC,WAAW9E,GAAG;AAGvB,wBAAA6E,SAAS,IAAcA,UAAA;AAC3B,wBAAIA,SAAS,KAAKX,MAAMW,MAAM,EAAYA,UAAA;AAE1C/E,2CAAuBgB,SAASf,UAAU4E,OAAOE,MAAM,CAAC;AAAA,kBAC5D;AAAA,kBACAE,UAAU,CAACnF,QAAQkB,SAASf,QAAQ;AAAA,kBACpCiF,MAAK;AAAA,kBACLC,KAAI;AAAA,kBACJC,KAAI;AAAA,kBACJC,MAAK;AAAA,gBAAA,CACT,GACAjJ,kCAAA,IAACkJ,MAAA;AAAA,kBACG3B,MAAM;AAAA,kBACNZ,SAASA,MAAMxC,WAAWS,SAASf,QAAQ;AAAA,kBAC3Cd,WAAU;AAAA,gBAAA,CACd,GACA/C,kCAAA,IAACmJ,GAAA;AAAA,kBACGC,OAAM;AAAA,kBACN7B,MAAM;AAAA,kBACNxE,WAAU;AAAA,kBACV4D,SAASA,MAAMhD,WAAW,CAAE,CAAA;AAAA,gBAAA,CAChC,CAAA;AAAA,cAAA,CACJ,IAGA5D,kCAAA,KAAC,OAAI;AAAA,gBAAAgD,WAAU;AAAA,gBACXmC,UAAA,CAAClF,kCAAA,IAAA,QAAA;AAAA,kBAAMkF,mBAASwD,aAAa,IAAI,GAAG9D,SAAS8D,UAAU,OAAO;AAAA,gBAAI,CAAA,GAClE1I,kCAAA,IAACqJ,QAAA;AAAA,kBACG9B,MAAM;AAAA,kBACNZ,SAASA,MAAMhD,WAAW;AAAA,oBAAE,CAACiB,SAASf,QAAQ,GAAG;AAAA,kBAAK,CAAC;AAAA,kBACvDd,WAAU;AAAA,gBAAA,CACd,CAAA;AAAA,cACJ,CAAA;AAAA,YAER,CAAA,CAAA;AAAA,UAAA,GApGW6B,SAASmC,OAwKxB;AAAA,QAEP,CAAA;AAAA,MACL,CAAA,CAAA;AAAA,IACJ,CAAA,GACA/G,kCAAA,IAACsJ,YAAA;AAAA,MAAWlJ;AAAAA,MAA0BE;AAAAA,MAClCiJ,UAAUvI;AAAAA,MACVJ;AAAAA,MACA4I,UAAU/I;AAAAA,MACVgJ,aAAa3I;AAAAA,IAAA,CACjB,CAAA;AAAA,EAEJ,CAAA;AAER;"}