{"version": 3, "file": "share-2-BRs1HrxR.js", "sources": ["../../../node_modules/lucide-react/dist/esm/icons/share-2.js"], "sourcesContent": ["/**\n * @license lucide-react v0.453.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst Share2 = createLucideIcon(\"Share2\", [\n  [\"circle\", { cx: \"18\", cy: \"5\", r: \"3\", key: \"gq8acd\" }],\n  [\"circle\", { cx: \"6\", cy: \"12\", r: \"3\", key: \"w7nqdw\" }],\n  [\"circle\", { cx: \"18\", cy: \"19\", r: \"3\", key: \"1xt0gg\" }],\n  [\"line\", { x1: \"8.59\", x2: \"15.42\", y1: \"13.51\", y2: \"17.49\", key: \"47mynk\" }],\n  [\"line\", { x1: \"15.41\", x2: \"8.59\", y1: \"6.51\", y2: \"10.49\", key: \"1n3mei\" }]\n]);\n\nexport { Share2 as default };\n//# sourceMappingURL=share-2.js.map\n"], "names": [], "mappings": ";AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASK,MAAC,SAAS,iBAAiB,UAAU;AAAA,EACxC,CAAC,UAAU,EAAE,IAAI,MAAM,IAAI,KAAK,GAAG,KAAK,KAAK,UAAU;AAAA,EACvD,CAAC,UAAU,EAAE,IAAI,KAAK,IAAI,MAAM,GAAG,KAAK,KAAK,UAAU;AAAA,EACvD,CAAC,UAAU,EAAE,IAAI,MAAM,IAAI,MAAM,GAAG,KAAK,KAAK,UAAU;AAAA,EACxD,CAAC,QAAQ,EAAE,IAAI,QAAQ,IAAI,SAAS,IAAI,SAAS,IAAI,SAAS,KAAK,SAAQ,CAAE;AAAA,EAC7E,CAAC,QAAQ,EAAE,IAAI,SAAS,IAAI,QAAQ,IAAI,QAAQ,IAAI,SAAS,KAAK,SAAU,CAAA;AAC9E,CAAC;", "x_google_ignoreList": [0]}