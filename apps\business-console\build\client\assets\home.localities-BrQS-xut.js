import { r as reactExports, j as jsxRuntimeExports } from "./jsx-runtime-bB2y7OuD.js";
import { d as decodePolygon, L as LoadScript, G as GoogleMap, P as Polygon, D as DrawingManager, I as InfoWindow } from "./polyline-utils-DkQLXyiU.js";
import { B as Button } from "./button-ByAXMyvk.js";
import { I as Input } from "./input-3v87qohQ.js";
import { S as Select, a as SelectTrigger, b as SelectValue, c as SelectContent, d as SelectItem } from "./select-BFGSXKcr.js";
import { c as useLocation, a as useNavigate, O as Outlet } from "./index-DhHTcibu.js";
import { b as useSearchParams, d as useActionData, u as useLoaderData, F as Form } from "./components-D7UvGag_.js";
import { A as ArrowUpDown } from "./arrow-up-down-DLQ-Vrdr.js";
import { E as Eye } from "./eye-BtRudnNq.js";
import { c as createLucideIcon } from "./createLucideIcon-uwkRm45G.js";
import { P as Pencil } from "./pencil-C1goHmP8.js";
import { T as Trash2 } from "./trash-2-DjkfFIB-.js";
import { X } from "./x-CCG_WJDF.js";
import "./index-Vp2vNLNM.js";
import "./index-z_byfFrQ.js";
import "./index-ImHKLo0a.js";
import "./utils-GkgzjW3c.js";
import "./index-IXOTxK3N.js";
import "./index-D7VH9Fc8.js";
import "./index-qCtcpOcW.js";
import "./index-BTdCMChR.js";
import "./index-BXzPK7u0.js";
import "./index-CzZ6EYkN.js";
import "./index-CEHS9zzk.js";
import "./index-DVTNuYOr.js";
import "./index-DscYByPT.js";
import "./index-CkL5tk39.js";
import "./index-dIGjFc0I.js";
import "./index-CpKiYcZd.js";
import "./index-C88PRvfd.js";
import "./chevron-down-pCP5jmjX.js";
import "./check-_dbWxIzT.js";
/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */
const EyeOff = createLucideIcon("EyeOff", [
  [
    "path",
    {
      d: "M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",
      key: "ct8e1f"
    }
  ],
  ["path", { d: "M14.084 14.158a3 3 0 0 1-4.242-4.242", key: "151rxh" }],
  [
    "path",
    {
      d: "M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",
      key: "13bj9a"
    }
  ],
  ["path", { d: "m2 2 20 20", key: "1ooewy" }]
]);
var encode = function(path, precision) {
  if (precision === void 0) {
    precision = 5;
  }
  var factor = Math.pow(10, precision);
  var transform = function latLngToFixed(latLng) {
    if (!Array.isArray(latLng)) {
      latLng = [latLng.lat, latLng.lng];
    }
    return [round(latLng[0] * factor), round(latLng[1] * factor)];
  };
  return polylineEncodeLine(path, transform);
};
var polylineEncodeLine = function(array, transform) {
  var v = [];
  var start = [0, 0];
  var end;
  for (var i = 0, I = array.length; i < I; ++i) {
    end = transform(array[i]);
    polylineEncodeSigned(round(end[0]) - round(start[0]), v);
    polylineEncodeSigned(round(end[1]) - round(start[1]), v);
    start = end;
  }
  return v.join("");
};
var polylineEncodeSigned = function(value, array) {
  return polylineEncodeUnsigned(value < 0 ? ~(value << 1) : value << 1, array);
};
var polylineEncodeUnsigned = function(value, array) {
  while (value >= 32) {
    array.push(String.fromCharCode((32 | value & 31) + 63));
    value >>= 5;
  }
  array.push(String.fromCharCode(value + 63));
  return array;
};
var round = function(v) {
  return Math.floor(Math.abs(v) + 0.5) * (v >= 0 ? 1 : -1);
};
const BANGALORE_CENTER = {
  lat: 12.9716,
  lng: 77.5946
};
const MAP_CONTAINER_STYLE = {
  width: "100%",
  height: "100%"
};
function getPolygonColor(index) {
  const colors = ["#3b82f6", "#10b981", "#f59e0b", "#ef4444", "#8b5cf6", "#ec4899", "#06b6d4", "#f97316"];
  return colors[index % colors.length];
}
function LocalitiesSection() {
  var _a, _b, _c;
  const location = useLocation();
  useNavigate();
  const [searchParams, setSearchParams] = useSearchParams();
  const actionData = useActionData();
  const {
    areas: initialAreas,
    googleMapsApiKey,
    states,
    districts
  } = useLoaderData();
  const [localAreas, setLocalAreas] = reactExports.useState(initialAreas);
  reactExports.useEffect(() => {
    setLocalAreas(initialAreas);
  }, [initialAreas]);
  const [selectedState, setSelectedState] = reactExports.useState(searchParams.get("state") || "Karnataka");
  const [selectedDistrict, setSelectedDistrict] = reactExports.useState(searchParams.get("district") || "Bangalore");
  const [visibleAreas, setVisibleAreas] = reactExports.useState(/* @__PURE__ */ new Set());
  const [map, setMap] = reactExports.useState(null);
  const [mapLoaded, setMapLoaded] = reactExports.useState(false);
  const [searchTerm, setSearchTerm] = reactExports.useState("");
  const [sortOrder, setSortOrder] = reactExports.useState("asc");
  const [editingArea, setEditingArea] = reactExports.useState(null);
  const [editingPolygon, setEditingPolygon] = reactExports.useState("");
  const [selectedVertexIndex, setSelectedVertexIndex] = reactExports.useState(null);
  const [infoWindow, setInfoWindow] = reactExports.useState(null);
  const [drawingMode, setDrawingMode] = reactExports.useState(false);
  const [radiusMode, setRadiusMode] = reactExports.useState(false);
  const polygonRefs = reactExports.useRef({});
  reactExports.useEffect(() => {
    if (mapLoaded) {
      const initialVisible = new Set(localAreas.filter((a) => a.polygon).map((a) => a.id));
      setVisibleAreas(initialVisible);
    }
  }, [mapLoaded, localAreas]);
  const onLoadMap = reactExports.useCallback((mapInstance) => {
    setMap(mapInstance);
    setMapLoaded(true);
  }, []);
  const onUnmountMap = reactExports.useCallback(() => {
    setMap(null);
    setMapLoaded(false);
  }, []);
  const fitBounds = reactExports.useCallback(() => {
    if (map && visibleAreas.size > 0) {
      const bounds = new google.maps.LatLngBounds();
      localAreas.forEach((area) => {
        if (visibleAreas.has(area.id) && area.polygon) {
          decodePolygon(area.polygon).forEach((coord) => bounds.extend(coord));
        }
      });
      map.fitBounds(bounds);
    }
  }, [map, visibleAreas, localAreas]);
  reactExports.useEffect(() => {
    if (mapLoaded) {
      fitBounds();
    }
  }, [fitBounds, mapLoaded]);
  const toggleAreaVisibility = reactExports.useCallback((areaId) => {
    setVisibleAreas((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(areaId)) {
        newSet.delete(areaId);
      } else {
        newSet.add(areaId);
      }
      return newSet;
    });
  }, []);
  reactExports.useEffect(() => {
    fitBounds();
  }, [visibleAreas, fitBounds]);
  const setAllVisible = () => {
    const allWithPolygons = new Set(localAreas.filter((a) => a.polygon).map((a) => a.id));
    setVisibleAreas(allWithPolygons);
  };
  const setNoneVisible = () => setVisibleAreas(/* @__PURE__ */ new Set());
  const filteredAreas = reactExports.useMemo(() => {
    return localAreas.filter((area) => {
      const matchesSearch = area.name.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesState = selectedState === "all" || area.state === selectedState;
      const matchesDistrict = selectedDistrict === "all" || area.district === selectedDistrict;
      const polygonpresent = area.polygon;
      return matchesSearch && matchesState && matchesDistrict && polygonpresent;
    }).sort((a, b) => sortOrder === "asc" ? a.name.localeCompare(b.name) : b.name.localeCompare(a.name));
  }, [localAreas, searchTerm, selectedState, selectedDistrict, sortOrder]);
  const handleSort = () => {
    setSortOrder(sortOrder === "asc" ? "desc" : "asc");
  };
  if (location.pathname.endsWith("/new") || location.pathname.endsWith("/attach") || location.pathname.includes("/edit")) {
    return /* @__PURE__ */ jsxRuntimeExports.jsx(Outlet, {});
  }
  const handleAdd = () => {
    setDrawingMode(true);
    setEditingArea(null);
    setEditingPolygon("");
  };
  const handleRadiusAdd = () => {
    setRadiusMode(true);
  };
  reactExports.useCallback((poly) => {
    poly.setMap(null);
    const path = poly.getPath();
    const coords = [];
    for (let i = 0; i < path.getLength(); i++) {
      const latLng = path.getAt(i);
      coords.push({
        lat: latLng.lat(),
        lng: latLng.lng()
      });
    }
    const coordsArray = coords.map(({
      lat,
      lng
    }) => [lat, lng]);
    const deduped = deduplicateCoordinates2(coordsArray);
    const encoded = encode(deduped);
    const newArea = {
      id: 0,
      name: "",
      state: selectedState === "all" ? "" : selectedState,
      district: selectedDistrict === "all" ? "" : selectedDistrict,
      polygon: encoded
    };
    setLocalAreas((prev) => [...prev, newArea]);
    setEditingArea(newArea);
    setEditingPolygon(encoded);
    setDrawingMode(false);
  }, [selectedState, selectedDistrict]);
  const startEditing = (area) => {
    setEditingArea(area);
    setEditingPolygon(area.polygon || "");
    setSelectedVertexIndex(null);
  };
  const handlePolygonLoad = reactExports.useCallback((polygon, areaId) => {
    polygonRefs.current[areaId] = polygon;
  }, []);
  const handlePolygonEdit = reactExports.useCallback((areaId) => {
    const poly = polygonRefs.current[areaId];
    if (!poly) return;
    const path = poly.getPath();
    const coords = [];
    for (let i = 0; i < path.getLength(); i++) {
      const latLng = path.getAt(i);
      coords.push({
        lat: latLng.lat(),
        lng: latLng.lng()
      });
    }
    const coordsArray = coords.map(({
      lat,
      lng
    }) => [lat, lng]);
    const deduped = deduplicateCoordinates2(coordsArray);
    if (deduped.length < coordsArray.length) {
      const newPath = deduped.map(([lat, lng]) => ({
        lat,
        lng
      }));
      poly.setPath(newPath);
    }
    const encoded = encode(deduped);
    setEditingPolygon(encoded);
  }, []);
  function deduplicateCoordinates2(coords) {
    if (coords.length === 0) return coords;
    const deduped = [coords[0]];
    for (let i = 1; i < coords.length; i++) {
      const prev = deduped[deduped.length - 1];
      const curr = coords[i];
      if (prev[0] === curr[0] && prev[1] === curr[1]) continue;
      deduped.push(curr);
    }
    return deduped;
  }
  reactExports.useEffect(() => {
    if (editingArea) {
      const poly = polygonRefs.current[editingArea.id];
      if (poly) {
        const path = poly.getPath();
        const listener1 = google.maps.event.addListener(path, "set_at", () => {
          handlePolygonEdit(editingArea.id);
        });
        const listener2 = google.maps.event.addListener(path, "insert_at", () => {
          handlePolygonEdit(editingArea.id);
        });
        return () => {
          google.maps.event.removeListener(listener1);
          google.maps.event.removeListener(listener2);
        };
      }
    }
  }, [editingArea, handlePolygonEdit]);
  reactExports.useEffect(() => {
    function handleKeyDown(e) {
      if ((e.key === "Delete" || e.key === "Backspace") && editingArea !== null && selectedVertexIndex !== null) {
        const poly = polygonRefs.current[editingArea.id];
        if (poly) {
          const path = poly.getPath();
          if (path.getLength() > 3) {
            path.removeAt(selectedVertexIndex);
            handlePolygonEdit(editingArea.id);
            setSelectedVertexIndex(null);
          }
        }
      }
    }
    if (editingArea) {
      window.addEventListener("keydown", handleKeyDown);
    }
    return () => {
      window.removeEventListener("keydown", handleKeyDown);
    };
  }, [editingArea, selectedVertexIndex, handlePolygonEdit]);
  const handlePolygonClick2 = reactExports.useCallback((e, area) => {
    if (editingArea && editingArea.id === area.id && e.latLng) {
      const poly = polygonRefs.current[area.id];
      if (poly) {
        const path = poly.getPath();
        let closestIndex = null;
        let minDist = Infinity;
        for (let i = 0; i < path.getLength(); i++) {
          const vertex = path.getAt(i);
          const d = Math.sqrt(Math.pow(vertex.lat() - e.latLng.lat(), 2) + Math.pow(vertex.lng() - e.latLng.lng(), 2));
          if (d < minDist) {
            minDist = d;
            closestIndex = i;
          }
        }
        const threshold = 1e-4;
        if (minDist < threshold) {
          setSelectedVertexIndex(closestIndex);
        } else {
          setSelectedVertexIndex(null);
        }
      }
    } else {
      if (e.latLng && area) {
        setInfoWindow({
          position: e.latLng.toJSON(),
          content: `${area.id} - ${area.name}`
        });
      }
    }
  }, [editingArea]);
  const handleCancelEdit = () => {
    setEditingArea(null);
    setEditingPolygon("");
    setSelectedVertexIndex(null);
  };
  return /* @__PURE__ */ jsxRuntimeExports.jsx(LoadScript, {
    googleMapsApiKey,
    libraries: ["drawing"],
    children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
      className: "flex h-[calc(100vh-64px)] relative",
      children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
        className: "w-fit bg-white border-r border-gray-200 overflow-y-auto flex flex-col",
        children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
          className: "p-4 border-b border-gray-200",
          children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
            className: "flex justify-between items-center mb-4",
            children: [/* @__PURE__ */ jsxRuntimeExports.jsx("h2", {
              className: "text-lg font-semibold text-gray-900",
              children: "Service Areas"
            }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
              className: "space-x-2 flex ",
              children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
                onClick: handleAdd,
                size: "sm",
                children: "Add Polygon                "
              }), /* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
                onClick: handleRadiusAdd,
                size: "sm",
                children: "Add Radius"
              })]
            })]
          }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
            className: "flex gap-2 mb-4",
            children: [/* @__PURE__ */ jsxRuntimeExports.jsxs(Select, {
              value: selectedState,
              onValueChange: (value) => {
                setSelectedState(value);
                setSelectedDistrict("");
                setSearchParams({
                  state: value,
                  district: ""
                });
              },
              children: [/* @__PURE__ */ jsxRuntimeExports.jsx(SelectTrigger, {
                className: "w-1/2",
                children: /* @__PURE__ */ jsxRuntimeExports.jsx(SelectValue, {
                  placeholder: "Select state"
                })
              }), /* @__PURE__ */ jsxRuntimeExports.jsxs(SelectContent, {
                children: [/* @__PURE__ */ jsxRuntimeExports.jsx(SelectItem, {
                  value: "all",
                  children: "All States"
                }), states.map((state) => /* @__PURE__ */ jsxRuntimeExports.jsx(SelectItem, {
                  value: state,
                  children: state
                }, state))]
              })]
            }), /* @__PURE__ */ jsxRuntimeExports.jsxs(Select, {
              value: selectedDistrict,
              onValueChange: (value) => {
                setSelectedDistrict(value);
                setSearchParams({
                  state: selectedState,
                  district: value
                });
              },
              disabled: selectedState === "all",
              children: [/* @__PURE__ */ jsxRuntimeExports.jsx(SelectTrigger, {
                className: "w-1/2",
                children: /* @__PURE__ */ jsxRuntimeExports.jsx(SelectValue, {
                  placeholder: "Select district"
                })
              }), /* @__PURE__ */ jsxRuntimeExports.jsxs(SelectContent, {
                children: [/* @__PURE__ */ jsxRuntimeExports.jsx(SelectItem, {
                  value: "all",
                  children: "All Districts"
                }), selectedState !== "all" && ((_a = districts[selectedState]) == null ? void 0 : _a.map((d) => /* @__PURE__ */ jsxRuntimeExports.jsx(SelectItem, {
                  value: d,
                  children: d
                }, d)))]
              })]
            })]
          }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
            className: "flex justify-between items-center mb-4",
            children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Input, {
              placeholder: "Search areas...",
              value: searchTerm,
              onChange: (e) => setSearchTerm(e.target.value),
              className: "flex w-1/2"
            }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
              className: "space-x-2",
              children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
                onClick: setNoneVisible,
                variant: "outline",
                size: "sm",
                children: "Hide All"
              }), /* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
                onClick: setAllVisible,
                variant: "outline",
                size: "sm",
                children: "Show All"
              })]
            })]
          })]
        }), /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
          className: "flex-grow overflow-auto",
          children: /* @__PURE__ */ jsxRuntimeExports.jsxs("table", {
            className: "min-w-full divide-y divide-gray-200",
            children: [/* @__PURE__ */ jsxRuntimeExports.jsx("thead", {
              className: "bg-gray-50 sticky top-0",
              children: /* @__PURE__ */ jsxRuntimeExports.jsxs("tr", {
                children: [/* @__PURE__ */ jsxRuntimeExports.jsx("th", {
                  className: "px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",
                  children: /* @__PURE__ */ jsxRuntimeExports.jsxs(Button, {
                    variant: "ghost",
                    onClick: handleSort,
                    className: "flex items-center",
                    children: ["Name", /* @__PURE__ */ jsxRuntimeExports.jsx(ArrowUpDown, {
                      size: 14,
                      className: "ml-1"
                    })]
                  })
                }), /* @__PURE__ */ jsxRuntimeExports.jsx("th", {
                  className: "px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider",
                  children: "Actions"
                })]
              })
            }), /* @__PURE__ */ jsxRuntimeExports.jsx("tbody", {
              className: "bg-white divide-y divide-gray-200",
              children: filteredAreas.map((area, index) => {
                const isEditing = (editingArea == null ? void 0 : editingArea.id) === area.id;
                return /* @__PURE__ */ jsxRuntimeExports.jsxs("tr", {
                  className: `hover:bg-gray-50 ${isEditing ? "bg-blue-50" : ""}`,
                  children: [/* @__PURE__ */ jsxRuntimeExports.jsx("td", {
                    className: "px-6 py-4 whitespace-nowrap",
                    children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                      className: "flex items-center",
                      children: [/* @__PURE__ */ jsxRuntimeExports.jsx("div", {
                        className: "w-3 h-3 rounded-full mr-2",
                        style: {
                          backgroundColor: getPolygonColor(index),
                          opacity: visibleAreas.has(area.id) ? 1 : 0.3
                        }
                      }), /* @__PURE__ */ jsxRuntimeExports.jsxs("span", {
                        className: "text-sm text-gray-900",
                        children: [area.name, !area.polygon && /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                          className: "text-xs text-gray-400 ml-2",
                          children: "(no boundary)"
                        })]
                      })]
                    })
                  }), /* @__PURE__ */ jsxRuntimeExports.jsxs("td", {
                    className: "px-6 py-4 whitespace-nowrap text-right text-sm font-medium flex gap-2",
                    children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
                      variant: "ghost",
                      size: "sm",
                      onClick: () => toggleAreaVisibility(area.id),
                      disabled: !area.polygon,
                      children: visibleAreas.has(area.id) ? /* @__PURE__ */ jsxRuntimeExports.jsx(Eye, {
                        size: 16
                      }) : /* @__PURE__ */ jsxRuntimeExports.jsx(EyeOff, {
                        size: 16
                      })
                    }), /* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
                      variant: "ghost",
                      size: "sm",
                      className: "text-indigo-600 hover:text-indigo-900",
                      onClick: () => startEditing(area),
                      children: /* @__PURE__ */ jsxRuntimeExports.jsx(Pencil, {
                        size: 16
                      })
                    }), /* @__PURE__ */ jsxRuntimeExports.jsxs(Form, {
                      method: "post",
                      onSubmit: (e) => {
                        if (!confirm("Are you sure you want to delete this area?")) {
                          e.preventDefault();
                        }
                      },
                      children: [/* @__PURE__ */ jsxRuntimeExports.jsx("input", {
                        type: "hidden",
                        name: "id",
                        value: area.id
                      }), /* @__PURE__ */ jsxRuntimeExports.jsx("input", {
                        type: "hidden",
                        name: "action",
                        value: "delete"
                      }), /* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
                        type: "submit",
                        variant: "ghost",
                        size: "sm",
                        className: "text-red-600 hover:text-red-900",
                        children: /* @__PURE__ */ jsxRuntimeExports.jsx(Trash2, {
                          size: 16
                        })
                      })]
                    })]
                  })]
                }, area.id);
              })
            })]
          })
        })]
      }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
        className: "flex-1 bg-white relative",
        children: [/* @__PURE__ */ jsxRuntimeExports.jsxs(GoogleMap, {
          mapContainerStyle: MAP_CONTAINER_STYLE,
          center: BANGALORE_CENTER,
          zoom: 11,
          onLoad: onLoadMap,
          onUnmount: onUnmountMap,
          options: {
            styles: [{
              featureType: "all",
              elementType: "geometry.fill",
              stylers: [{
                visibility: "on"
              }]
            }],
            mapTypeControl: false,
            streetViewControl: false,
            fullscreenControl: false
          },
          children: [mapLoaded && localAreas.map((area, index) => {
            if (!area.polygon || !visibleAreas.has(area.id)) return null;
            const isEditing = (editingArea == null ? void 0 : editingArea.id) === area.id;
            const polygonPath = isEditing && editingPolygon ? decodePolygon(editingPolygon) : decodePolygon(area.polygon);
            const polygonOptions = isEditing ? {
              fillColor: getPolygonColor(index),
              fillOpacity: 0.5,
              strokeColor: "#FFD700",
              strokeOpacity: 1,
              strokeWeight: 4,
              clickable: true,
              editable: true
            } : {
              fillColor: getPolygonColor(index),
              fillOpacity: 0.2,
              strokeColor: getPolygonColor(index),
              strokeOpacity: 1,
              strokeWeight: 2,
              clickable: true,
              editable: false
            };
            return /* @__PURE__ */ jsxRuntimeExports.jsx(Polygon, {
              paths: polygonPath,
              options: polygonOptions,
              onLoad: (poly) => handlePolygonLoad(poly, area.id),
              onClick: (e) => handlePolygonClick2(e, area),
              onMouseUp: () => handlePolygonEdit(area.id),
              onDragEnd: () => handlePolygonEdit(area.id)
            }, area.id);
          }), drawingMode && /* @__PURE__ */ jsxRuntimeExports.jsx(DrawingManager, {
            options: {
              drawingControl: true,
              drawingMode: window.google.maps.drawing.OverlayType.POLYGON,
              polygonOptions: {
                fillColor: "#FF0000",
                fillOpacity: 0.5,
                strokeWeight: 2,
                clickable: true,
                editable: true,
                zIndex: 1
              }
            },
            onPolygonComplete: (poly) => {
              poly.setMap(null);
              const path = poly.getPath();
              const coords = [];
              for (let i = 0; i < path.getLength(); i++) {
                const latLng = path.getAt(i);
                coords.push({
                  lat: latLng.lat(),
                  lng: latLng.lng()
                });
              }
              const coordsArray = coords.map(({
                lat,
                lng
              }) => [lat, lng]);
              const deduped = deduplicateCoordinates2(coordsArray);
              const encoded = encode(deduped);
              const newArea = {
                id: 0,
                name: "",
                state: selectedState === "all" ? "" : selectedState,
                district: selectedDistrict === "all" ? "" : selectedDistrict,
                polygon: encoded
              };
              setLocalAreas((prev) => [...prev, newArea]);
              setEditingArea(newArea);
              setEditingPolygon(encoded);
              setDrawingMode(false);
            }
          }), infoWindow && /* @__PURE__ */ jsxRuntimeExports.jsx(InfoWindow, {
            position: infoWindow.position,
            options: {
              headerDisabled: true,
              disableAutoPan: true
            },
            children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
              className: "flex flex-col gap-2 overflow-hidden ",
              children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                className: "flex justify-between w-full align-middle items-center",
                children: [/* @__PURE__ */ jsxRuntimeExports.jsx("h2", {
                  className: "text-md font-semibold text-typography-300",
                  children: "Locality Info"
                }), /* @__PURE__ */ jsxRuntimeExports.jsx("button", {
                  className: "inline-flex items-center gap-1 hover:text-blue-800",
                  onClick: () => setInfoWindow(null),
                  children: /* @__PURE__ */ jsxRuntimeExports.jsx(X, {
                    className: "h-5 w-5"
                  })
                })]
              }), /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
                className: "flex flex-col gap-1 text-sm font-medium text-typography-700 w-[calc(100%-1rem)]",
                children: /* @__PURE__ */ jsxRuntimeExports.jsxs("p", {
                  children: [" ", infoWindow.content]
                })
              })]
            })
          })]
        }), radiusMode && /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
          className: "absolute top-4 right-4 w-80 bg-white p-4 shadow-lg rounded z-50",
          children: /* @__PURE__ */ jsxRuntimeExports.jsxs(Form, {
            method: "post",
            children: [/* @__PURE__ */ jsxRuntimeExports.jsx("input", {
              type: "hidden",
              name: "id",
              value: editingArea == null ? void 0 : editingArea.id
            }), /* @__PURE__ */ jsxRuntimeExports.jsx("input", {
              type: "hidden",
              name: "mode",
              value: "RadiusCreate",
              readOnly: true
            }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
              className: "mb-3",
              children: [/* @__PURE__ */ jsxRuntimeExports.jsx("label", {
                className: "block text-sm font-medium text-gray-700",
                children: "Name"
              }), /* @__PURE__ */ jsxRuntimeExports.jsx(Input, {
                className: "w-full",
                name: "name",
                defaultValue: editingArea == null ? void 0 : editingArea.name
              })]
            }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
              className: "mb-3",
              children: [/* @__PURE__ */ jsxRuntimeExports.jsx("label", {
                className: "block text-sm font-medium text-gray-700",
                children: "Radius"
              }), /* @__PURE__ */ jsxRuntimeExports.jsx(Input, {
                className: "w-full",
                name: "radius",
                defaultValue: editingArea == null ? void 0 : editingArea.radius
              })]
            }), (editingArea == null ? void 0 : editingArea.radius) !== null && /* @__PURE__ */ jsxRuntimeExports.jsxs(jsxRuntimeExports.Fragment, {
              children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                className: "mb-3",
                children: [/* @__PURE__ */ jsxRuntimeExports.jsx("label", {
                  className: "block text-sm font-medium text-gray-700",
                  children: "Latitude"
                }), /* @__PURE__ */ jsxRuntimeExports.jsx(Input, {
                  className: "w-full",
                  name: "latitude",
                  defaultValue: editingArea == null ? void 0 : editingArea.latitude,
                  required: !!(editingArea == null ? void 0 : editingArea.radius)
                })]
              }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                className: "mb-3",
                children: [/* @__PURE__ */ jsxRuntimeExports.jsx("label", {
                  className: "block text-sm font-medium text-gray-700",
                  children: "Longitude"
                }), /* @__PURE__ */ jsxRuntimeExports.jsx(Input, {
                  className: "w-full",
                  name: "longitude",
                  defaultValue: editingArea == null ? void 0 : editingArea.longitude,
                  required: !!(editingArea == null ? void 0 : editingArea.radius)
                })]
              })]
            }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
              className: "mb-3",
              children: [/* @__PURE__ */ jsxRuntimeExports.jsx("label", {
                className: "block text-sm font-medium text-gray-700",
                children: "State"
              }), /* @__PURE__ */ jsxRuntimeExports.jsxs(Select, {
                name: "state",
                defaultValue: editingArea == null ? void 0 : editingArea.state,
                children: [/* @__PURE__ */ jsxRuntimeExports.jsx(SelectTrigger, {
                  className: "w-full mt-1",
                  children: /* @__PURE__ */ jsxRuntimeExports.jsx(SelectValue, {
                    placeholder: "Select state"
                  })
                }), /* @__PURE__ */ jsxRuntimeExports.jsxs(SelectContent, {
                  children: [/* @__PURE__ */ jsxRuntimeExports.jsx(SelectItem, {
                    value: "state",
                    children: "States"
                  }), states.map((state) => /* @__PURE__ */ jsxRuntimeExports.jsx(SelectItem, {
                    value: state,
                    children: state
                  }, state))]
                })]
              })]
            }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
              className: "mb-3",
              children: [/* @__PURE__ */ jsxRuntimeExports.jsx("label", {
                className: "block text-sm font-medium text-gray-700",
                children: "District"
              }), /* @__PURE__ */ jsxRuntimeExports.jsxs(Select, {
                name: "district",
                value: selectedDistrict,
                onValueChange: (value) => {
                  setSelectedDistrict(value);
                  setSearchParams({
                    state: selectedState,
                    district: value
                  });
                },
                disabled: selectedState === "all",
                children: [/* @__PURE__ */ jsxRuntimeExports.jsx(SelectTrigger, {
                  className: "w-1/2",
                  children: /* @__PURE__ */ jsxRuntimeExports.jsx(SelectValue, {
                    placeholder: "Select district"
                  })
                }), /* @__PURE__ */ jsxRuntimeExports.jsxs(SelectContent, {
                  children: [/* @__PURE__ */ jsxRuntimeExports.jsx(SelectItem, {
                    value: "all",
                    children: "All Districts"
                  }), selectedState !== "all" && ((_b = districts[selectedState]) == null ? void 0 : _b.map((d) => /* @__PURE__ */ jsxRuntimeExports.jsx(SelectItem, {
                    value: d,
                    children: d
                  }, d)))]
                })]
              })]
            }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
              className: "flex justify-end space-x-3 mt-4",
              children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
                type: "button",
                variant: "outline",
                onClick: () => setRadiusMode(false),
                children: "Cancel"
              }), /* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
                type: "submit",
                children: (editingArea == null ? void 0 : editingArea.id) === 0 ? "Create" : "Save"
              })]
            })]
          })
        }), editingArea && /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
          className: "absolute top-4 right-4 w-80 bg-white p-4 shadow-lg rounded z-50",
          children: [/* @__PURE__ */ jsxRuntimeExports.jsx("h3", {
            className: "text-lg font-semibold mb-2",
            children: editingArea.id === 0 ? "Create New Area" : "Edit Area"
          }), (actionData == null ? void 0 : actionData.error) && /* @__PURE__ */ jsxRuntimeExports.jsx("p", {
            className: "text-red-500 mb-2",
            children: actionData.error
          }), /* @__PURE__ */ jsxRuntimeExports.jsxs(Form, {
            method: "post",
            children: [/* @__PURE__ */ jsxRuntimeExports.jsx("input", {
              type: "hidden",
              name: "id",
              value: editingArea.id
            }), /* @__PURE__ */ jsxRuntimeExports.jsx("input", {
              type: "hidden",
              name: "polygon",
              value: editingPolygon
            }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
              className: "mb-3",
              children: [/* @__PURE__ */ jsxRuntimeExports.jsx("label", {
                className: "block text-sm font-medium text-gray-700",
                children: "Name"
              }), /* @__PURE__ */ jsxRuntimeExports.jsx(Input, {
                className: "w-full",
                name: "name",
                defaultValue: editingArea.name
              })]
            }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
              className: "mb-3",
              children: [/* @__PURE__ */ jsxRuntimeExports.jsx("label", {
                className: "block text-sm font-medium text-gray-700",
                children: "State"
              }), /* @__PURE__ */ jsxRuntimeExports.jsxs(Select, {
                name: "state",
                defaultValue: editingArea.state,
                children: [/* @__PURE__ */ jsxRuntimeExports.jsx(SelectTrigger, {
                  className: "w-full mt-1",
                  children: /* @__PURE__ */ jsxRuntimeExports.jsx(SelectValue, {
                    placeholder: "Select state"
                  })
                }), /* @__PURE__ */ jsxRuntimeExports.jsx(SelectContent, {
                  children: states.map((state) => /* @__PURE__ */ jsxRuntimeExports.jsx(SelectItem, {
                    value: state,
                    children: state
                  }, state))
                })]
              })]
            }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
              className: "mb-3",
              children: [/* @__PURE__ */ jsxRuntimeExports.jsx("label", {
                className: "block text-sm font-medium text-gray-700",
                children: "District"
              }), /* @__PURE__ */ jsxRuntimeExports.jsxs(Select, {
                name: "district",
                defaultValue: editingArea.district,
                children: [/* @__PURE__ */ jsxRuntimeExports.jsx(SelectTrigger, {
                  className: "w-full mt-1",
                  children: /* @__PURE__ */ jsxRuntimeExports.jsx(SelectValue, {
                    placeholder: "Select district"
                  })
                }), /* @__PURE__ */ jsxRuntimeExports.jsx(SelectContent, {
                  children: editingArea.state && ((_c = districts[editingArea.state]) == null ? void 0 : _c.map((d) => /* @__PURE__ */ jsxRuntimeExports.jsx(SelectItem, {
                    value: d,
                    children: d
                  }, d)))
                })]
              })]
            }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
              className: "flex justify-end space-x-3 mt-4",
              children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
                type: "button",
                variant: "outline",
                onClick: handleCancelEdit,
                children: "Cancel"
              }), /* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
                type: "submit",
                children: editingArea.id === 0 ? "Create" : "Save"
              })]
            })]
          })]
        })]
      })]
    })
  });
}
export {
  LocalitiesSection as default
};
//# sourceMappingURL=home.localities-BrQS-xut.js.map
