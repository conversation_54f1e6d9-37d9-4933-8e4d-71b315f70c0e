import { j as jsxRuntimeExports } from "./jsx-runtime-bB2y7OuD.js";
import { L as Link, u as useLoaderData } from "./components-D7UvGag_.js";
import "./index-Vp2vNLNM.js";
import "./index-DhHTcibu.js";
function LandingPage({ brandName, contactEmail, websiteUrl }) {
  return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "max-w-[1200px] mx-auto text-gray-800 leading-relaxed", children: [
    /* @__PURE__ */ jsxRuntimeExports.jsxs("header", { className: "flex justify-between items-center py-6", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "flex items-center gap-2", children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "text-xl font-semibold text-fm-green", children: brandName }),
        /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "text-xs text-gray-500", children: "by Brih Solutions" })
      ] }) }),
      /* @__PURE__ */ jsxRuntimeExports.jsx("nav", { className: "flex gap-6", children: ["About", "Features", "Impact", "Contact"].map((item) => /* @__PURE__ */ jsxRuntimeExports.jsx(
        "a",
        {
          href: `#${item.toLowerCase()}`,
          className: "text-gray-600 hover:text-fm-green transition-colors",
          children: item
        },
        item
      )) })
    ] }),
    /* @__PURE__ */ jsxRuntimeExports.jsxs("main", { className: "space-y-16", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsxs("section", { className: "text-center bg-fm-light rounded-xl p-8 shadow-md transform hover:-translate-y-1 transition-all duration-300", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx("h1", { className: "text-4xl font-bold mb-4 text-fm-green", children: "Revolutionizing Vegetable Distribution" }),
        /* @__PURE__ */ jsxRuntimeExports.jsx("p", { className: "text-xl text-gray-600 max-w-2xl mx-auto", children: "Connecting wholesalers to retailers with cutting-edge technology" })
      ] }),
      /* @__PURE__ */ jsxRuntimeExports.jsxs("section", { className: "grid md:grid-cols-2 gap-8", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "bg-fm-light rounded-xl p-8 shadow-md transform hover:-translate-y-1 transition-all duration-300", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsxs("h2", { className: "text-2xl font-semibold mb-4", children: [
            "About ",
            brandName
          ] }),
          /* @__PURE__ */ jsxRuntimeExports.jsxs("p", { className: "text-gray-600 mb-6", children: [
            brandName,
            " is a pioneering platform that streamlines the vegetable supply chain, connecting wholesalers with retailers efficiently and reducing waste."
          ] }),
          /* @__PURE__ */ jsxRuntimeExports.jsx("h3", { className: "text-xl font-semibold mb-4", children: "Our Customers" }),
          /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "grid grid-cols-4 gap-8 px-4", children: [...Array(8)].map((_, index) => /* @__PURE__ */ jsxRuntimeExports.jsx(
            "div",
            {
              className: "bg-white p-3 rounded-full w-20 h-20 flex items-center justify-center shadow-sm hover:shadow-md transition-shadow",
              children: /* @__PURE__ */ jsxRuntimeExports.jsx(
                "img",
                {
                  src: `/customer${index + 1}.png?height=60&width=60`,
                  alt: `Customer logo ${index + 1}`,
                  className: "w-14 h-14 object-contain"
                }
              )
            },
            index
          )) })
        ] }),
        /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "relative group h-full", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx(
            "div",
            {
              className: "absolute -inset-[1px] bg-gradient-to-r from-fm-green to-fm-hover rounded-xl opacity-75 group-hover:opacity-100 transition duration-300"
            }
          ),
          /* @__PURE__ */ jsxRuntimeExports.jsxs(
            "div",
            {
              className: "relative bg-white rounded-xl p-8 shadow-lg h-full flex flex-col justify-between",
              children: [
                /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "space-y-8", children: [
                  /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex justify-between items-center", children: [
                    /* @__PURE__ */ jsxRuntimeExports.jsx("h2", { className: "text-2xl font-semibold", children: "Login to mNet" }),
                    /* @__PURE__ */ jsxRuntimeExports.jsx(
                      "img",
                      {
                        src: "/mnet-logo.svg",
                        alt: "mNet Logo",
                        width: 100,
                        height: 100
                      }
                    )
                  ] }),
                  /* @__PURE__ */ jsxRuntimeExports.jsxs("ul", { className: "space-y-4", children: [
                    /* @__PURE__ */ jsxRuntimeExports.jsxs("li", { className: "flex items-center gap-2", children: [
                      /* @__PURE__ */ jsxRuntimeExports.jsx(
                        "svg",
                        {
                          className: "w-5 h-5 text-fm-green",
                          fill: "none",
                          strokeLinecap: "round",
                          strokeLinejoin: "round",
                          strokeWidth: "2",
                          viewBox: "0 0 24 24",
                          stroke: "currentColor",
                          children: /* @__PURE__ */ jsxRuntimeExports.jsx("path", { d: "M5 13l4 4L19 7" })
                        }
                      ),
                      /* @__PURE__ */ jsxRuntimeExports.jsx("span", { children: "Launch your own app within a week" })
                    ] }),
                    /* @__PURE__ */ jsxRuntimeExports.jsxs("li", { className: "flex items-center gap-2", children: [
                      /* @__PURE__ */ jsxRuntimeExports.jsx(
                        "svg",
                        {
                          className: "w-5 h-5 text-fm-green",
                          fill: "none",
                          strokeLinecap: "round",
                          strokeLinejoin: "round",
                          strokeWidth: "2",
                          viewBox: "0 0 24 24",
                          stroke: "currentColor",
                          children: /* @__PURE__ */ jsxRuntimeExports.jsx("path", { d: "M5 13l4 4L19 7" })
                        }
                      ),
                      /* @__PURE__ */ jsxRuntimeExports.jsx("span", { children: "Go online and increase your sales" })
                    ] }),
                    /* @__PURE__ */ jsxRuntimeExports.jsxs("li", { className: "flex items-center gap-2", children: [
                      /* @__PURE__ */ jsxRuntimeExports.jsx(
                        "svg",
                        {
                          className: "w-5 h-5 text-fm-green",
                          fill: "none",
                          strokeLinecap: "round",
                          strokeLinejoin: "round",
                          strokeWidth: "2",
                          viewBox: "0 0 24 24",
                          stroke: "currentColor",
                          children: /* @__PURE__ */ jsxRuntimeExports.jsx("path", { d: "M5 13l4 4L19 7" })
                        }
                      ),
                      /* @__PURE__ */ jsxRuntimeExports.jsx("span", { children: "Build your own brand and gain more trust" })
                    ] }),
                    /* @__PURE__ */ jsxRuntimeExports.jsxs("li", { className: "flex items-center gap-2", children: [
                      /* @__PURE__ */ jsxRuntimeExports.jsx(
                        "svg",
                        {
                          className: "w-5 h-5 text-fm-green",
                          fill: "none",
                          strokeLinecap: "round",
                          strokeLinejoin: "round",
                          strokeWidth: "2",
                          viewBox: "0 0 24 24",
                          stroke: "currentColor",
                          children: /* @__PURE__ */ jsxRuntimeExports.jsx("path", { d: "M5 13l4 4L19 7" })
                        }
                      ),
                      /* @__PURE__ */ jsxRuntimeExports.jsx("span", { children: "Streamline customer communications" })
                    ] }),
                    /* @__PURE__ */ jsxRuntimeExports.jsxs("li", { className: "flex items-center gap-2", children: [
                      /* @__PURE__ */ jsxRuntimeExports.jsx(
                        "svg",
                        {
                          className: "w-5 h-5 text-fm-green",
                          fill: "none",
                          strokeLinecap: "round",
                          strokeLinejoin: "round",
                          strokeWidth: "2",
                          viewBox: "0 0 24 24",
                          stroke: "currentColor",
                          children: /* @__PURE__ */ jsxRuntimeExports.jsx("path", { d: "M5 13l4 4L19 7" })
                        }
                      ),
                      /* @__PURE__ */ jsxRuntimeExports.jsx("span", { children: "Send timely updates and reminders" })
                    ] }),
                    /* @__PURE__ */ jsxRuntimeExports.jsxs("li", { className: "flex items-center gap-2", children: [
                      /* @__PURE__ */ jsxRuntimeExports.jsx(
                        "svg",
                        {
                          className: "w-5 h-5 text-fm-green",
                          fill: "none",
                          strokeLinecap: "round",
                          strokeLinejoin: "round",
                          strokeWidth: "2",
                          viewBox: "0 0 24 24",
                          stroke: "currentColor",
                          children: /* @__PURE__ */ jsxRuntimeExports.jsx("path", { d: "M5 13l4 4L19 7" })
                        }
                      ),
                      /* @__PURE__ */ jsxRuntimeExports.jsx("span", { children: "Share promotional messages" })
                    ] })
                  ] })
                ] }),
                /* @__PURE__ */ jsxRuntimeExports.jsx(
                  Link,
                  {
                    to: "/login",
                    className: "block w-full text-center px-6 py-3 bg-fm-green text-white rounded-md hover:bg-fm-hover transition-colors font-semibold mt-8",
                    children: "Login to mNet"
                  }
                )
              ]
            }
          )
        ] })
      ] }),
      /* @__PURE__ */ jsxRuntimeExports.jsxs(
        "section",
        {
          id: "features",
          className: "bg-fm-light rounded-xl p-8 shadow-md transform hover:-translate-y-1 transition-all duration-300",
          children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx("h2", { className: "text-2xl font-semibold mb-6 text-center", children: "Empowering the Supply Chain" }),
            /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "grid grid-cols-1 md:grid-cols-3 gap-8", children: [
              /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "bg-white p-6 rounded-lg shadow-sm", children: [
                /* @__PURE__ */ jsxRuntimeExports.jsx("h3", { className: "text-lg font-medium text-fm-green mb-3", children: "Vast Network" }),
                /* @__PURE__ */ jsxRuntimeExports.jsx("p", { className: "text-gray-600", children: "Access over 8,000 retailers, vegetable shops, hotels, and supermarkets." })
              ] }),
              /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "bg-white p-6 rounded-lg shadow-sm", children: [
                /* @__PURE__ */ jsxRuntimeExports.jsx("h3", { className: "text-lg font-medium text-fm-green mb-3", children: "Dedicated Sales Force" }),
                /* @__PURE__ */ jsxRuntimeExports.jsx("p", { className: "text-gray-600", children: "Benefit from our network of sales executives to maximize your sales." })
              ] }),
              /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "bg-white p-6 rounded-lg shadow-sm", children: [
                /* @__PURE__ */ jsxRuntimeExports.jsx("h3", { className: "text-lg font-medium text-fm-green mb-3", children: "Tech-Driven Operations" }),
                /* @__PURE__ */ jsxRuntimeExports.jsx("p", { className: "text-gray-600", children: "Utilize our tech tools to streamline and scale your business operations." })
              ] })
            ] })
          ]
        }
      ),
      /* @__PURE__ */ jsxRuntimeExports.jsxs(
        "section",
        {
          id: "impact",
          className: "bg-fm-light rounded-xl p-8 shadow-md transform hover:-translate-y-1 transition-all duration-300",
          children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx("h2", { className: "text-2xl font-semibold mb-6 text-center", children: "Our Impact" }),
            /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "flex flex-col md:flex-row justify-around text-center mb-6", children: [
              { number: "10,000+", label: "Tonnes of Produce Sold" },
              { number: "8,000+", label: "Active Customers" },
              { number: "180,000+", label: "Successful Deliveries" }
            ].map((stat, index) => /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "mb-4 md:mb-0", children: [
              /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "block text-3xl font-semibold text-fm-green", children: stat.number }),
              /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "text-gray-600", children: stat.label })
            ] }, index)) })
          ]
        }
      ),
      /* @__PURE__ */ jsxRuntimeExports.jsxs(
        "section",
        {
          id: "contact",
          className: "bg-fm-light rounded-xl p-8 shadow-md transform hover:-translate-y-1 transition-all duration-300",
          children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx("h2", { className: "text-2xl font-semibold mb-4 text-center", children: "Join the Revolution" }),
            /* @__PURE__ */ jsxRuntimeExports.jsxs("p", { className: "text-gray-600 mb-6 max-w-2xl mx-auto text-center", children: [
              "Whether you're a retailer or wholesaler, ",
              brandName,
              " is here to propel your business forward."
            ] }),
            /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex justify-center gap-4", children: [
              /* @__PURE__ */ jsxRuntimeExports.jsx(
                "a",
                {
                  href: `mailto:${contactEmail}`,
                  className: "px-6 py-3 bg-fm-green text-white rounded-md hover:bg-fm-hover transition-colors",
                  children: "Contact Us"
                }
              ),
              /* @__PURE__ */ jsxRuntimeExports.jsx(
                "a",
                {
                  href: "tel:9036728830",
                  className: "px-6 py-3 border border-fm-green text-fm-green rounded-md hover:bg-fm-light transition-colors",
                  children: "Call Us"
                }
              )
            ] })
          ]
        }
      )
    ] }),
    /* @__PURE__ */ jsxRuntimeExports.jsxs("footer", { className: "text-center py-8 text-gray-600", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsxs("p", { children: [
        "© 2024 ",
        brandName,
        " by Brih Solutions Private Limited"
      ] }),
      /* @__PURE__ */ jsxRuntimeExports.jsx(
        "a",
        {
          href: `https://${websiteUrl}`,
          target: "_blank",
          rel: "noopener noreferrer",
          className: "text-fm-green hover:underline",
          children: websiteUrl
        }
      )
    ] })
  ] });
}
const meta = () => {
  return [{
    title: "mNet - Revolutionizing Vegetable Distribution"
  }, {
    name: "description",
    content: "Connecting wholesalers to retailers with cutting-edge technology"
  }];
};
function Index() {
  const brandData = useLoaderData();
  return /* @__PURE__ */ jsxRuntimeExports.jsx(LandingPage, {
    ...brandData
  });
}
export {
  Index as default,
  meta
};
//# sourceMappingURL=_index--Zbm0e8U.js.map
