import { r as reactExports } from "./jsx-runtime-bB2y7OuD.js";
function useDebounce(value, delay) {
  const [debouncedValue, setDebouncedValue] = reactExports.useState(value);
  reactExports.useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);
    return () => {
      clearTimeout(timer);
    };
  }, [value, delay]);
  return debouncedValue;
}
export {
  useDebounce as u
};
//# sourceMappingURL=useDebounce-BXbH_IFZ.js.map
