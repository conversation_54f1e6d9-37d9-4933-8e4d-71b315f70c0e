{"version": 3, "file": "home.metaRestaurantDashboard-B4bA-bXg.js", "sources": ["../../../app/routes/home.metaRestaurantDashboard.tsx"], "sourcesContent": ["import { useLoaderData, useSubmit, useSearchParams } from \"@remix-run/react\";\r\nimport { LoaderFunction, LoaderFunctionArgs, json } from \"@remix-run/node\";\r\nimport jwt from \"jsonwebtoken\";\r\nimport { useEffect, useState } from \"react\";\r\nimport { withAuth } from \"~/utils/auth-utils\";\r\n\r\nconst METABASE_SECRET_KEY = process.env.METABASE_SECRET_KEY || \"\";\r\nconst METABASE_SITE_URL = process.env.METABASE_SITE_URL || \"http://43.205.118.52:4001\";\r\n\r\nexport const loader: LoaderFunction = withAuth(async ({ request, user }) => {\r\n\r\n\r\n      if (!METABASE_SECRET_KEY) {\r\n            throw new Error(\"Metabase secret key is not configured.\");\r\n      }\r\n\r\n      const payload = {\r\n            resource: { dashboard: 8 },\r\n            params: {\r\n                 \r\n            },\r\n            exp: Math.round(Date.now() / 1000) + (10 * 60) // expires in 10 minutes\r\n      };\r\n\r\n      const token = jwt.sign(payload, METABASE_SECRET_KEY);\r\n      try{\r\n      const embedUrl = `${METABASE_SITE_URL}/embed/dashboard/${token}#bordered=false&titled=false`;\r\n\r\n      return json({ embedUrl });\r\n      }\r\n      catch(err){\r\n        console.log(err,\"error while showing embedUrl\")\r\n      }\r\n});\r\n\r\nexport default function MetaRestaurantDashBoard() {\r\n      const { embedUrl,  } = useLoaderData<typeof loader>();\r\n      const [isLoading, setIsLoading] = useState(true);\r\n     \r\n\r\n\r\n      // Handle iframe loading state\r\n      useEffect(() => {\r\n            if (embedUrl) {\r\n                  setIsLoading(false);\r\n            }\r\n      }, [embedUrl]);\r\n\r\n      // Scroll synchronization\r\n      useEffect(() => {\r\n            const handleScroll = () => {\r\n                  const iframe = document.getElementById(\"metabase-iframe\") as HTMLIFrameElement;\r\n                  if (iframe) {\r\n                        iframe.contentWindow?.scrollTo(0, window.scrollY);\r\n                  }\r\n            };\r\n\r\n            window.addEventListener(\"scroll\", handleScroll);\r\n            return () => window.removeEventListener(\"scroll\", handleScroll);\r\n      }, []);\r\n\r\n    \r\n\r\n      return (\r\n            <div className=\"flex min-h-screen\">\r\n                  <main className=\"flex-1 overflow-y-auto\">\r\n                        <div className=\"p-4 sm:p-6\">\r\n                             \r\n\r\n                              <div className=\"bg-white shadow-md rounded-md overflow-hidden\">\r\n                                    {isLoading ? (\r\n                                          <div className=\"flex justify-center items-center h-96\">\r\n                                                <div className=\"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500\"></div>\r\n                                          </div>\r\n                                    ) : embedUrl ? (\r\n                                          <iframe\r\n                                                id=\"metabase-iframe\"\r\n                                                src={embedUrl}\r\n                                                title=\"Metabase Dashboard\"\r\n                                              \r\n                                                className=\"w-full h-[2000px] border-0\"\r\n                                                allowTransparency\r\n                                          />\r\n                                    ) : (\r\n                                          <div className=\"p-6 text-center text-red-500\">\r\n                                                Failed to load the dashboard.\r\n                                          </div>\r\n                                    )}\r\n                              </div>\r\n                        </div>\r\n                  </main>\r\n            </div>\r\n      );\r\n}"], "names": ["MetaRestaurantDashBoard", "embedUrl", "useLoaderData", "isLoading", "setIsLoading", "useState", "useEffect", "handleScroll", "iframe", "document", "getElementById", "contentWindow", "scrollTo", "window", "scrollY", "addEventListener", "removeEventListener", "jsx", "className", "children", "id", "src", "title", "allowTransparency"], "mappings": ";;;;AAmCA,SAAwBA,0BAA0B;AACtC,QAAA;AAAA,IAAEC;AAAAA,EAAW,IAAIC,cAA6B;AACpD,QAAM,CAACC,WAAWC,YAAY,IAAIC,aAAAA,SAAS,IAAI;AAK/CC,eAAAA,UAAU,MAAM;AACV,QAAIL,UAAU;AACRG,mBAAa,KAAK;AAAA,IACxB;AAAA,EACN,GAAG,CAACH,QAAQ,CAAC;AAGbK,eAAAA,UAAU,MAAM;AACV,UAAMC,eAAeA,MAAM;;AACf,YAAAC,SAASC,SAASC,eAAe,iBAAiB;AACxD,UAAIF,QAAQ;AACNA,qBAAOG,kBAAPH,mBAAsBI,SAAS,GAAGC,OAAOC;AAAAA,MAC/C;AAAA,IACN;AAEOD,WAAAE,iBAAiB,UAAUR,YAAY;AAC9C,WAAO,MAAMM,OAAOG,oBAAoB,UAAUT,YAAY;AAAA,EACpE,GAAG,EAAE;AAKC,SAAAU,kCAAAA,IAAC,OAAI;AAAA,IAAAC,WAAU;AAAA,IACTC,UAAAF,kCAAA,IAAC,QAAK;AAAA,MAAAC,WAAU;AAAA,MACVC,UAAAF,kCAAA,IAAC,OAAI;AAAA,QAAAC,WAAU;AAAA,QAGTC,UAAAF,kCAAA,IAAC,OAAI;AAAA,UAAAC,WAAU;AAAA,UACRC,UAAAhB,YACMc,kCAAA,IAAA,OAAA;AAAA,YAAIC,WAAU;AAAA,YACTC,UAACF,kCAAA,IAAA,OAAA;AAAA,cAAIC,WAAU;AAAA,YAA4E,CAAA;AAAA,WACjG,IACFjB,WACEgB,kCAAA,IAAC,UAAA;AAAA,YACKG,IAAG;AAAA,YACHC,KAAKpB;AAAAA,YACLqB,OAAM;AAAA,YAENJ,WAAU;AAAA,YACVK,mBAAiB;AAAA,UAAA,CACvB,0CAEC,OAAI;AAAA,YAAAL,WAAU;AAAA,YAA+BC,UAE9C;AAAA,UAAA,CAAA;AAAA,QAEZ,CAAA;AAAA,MACN,CAAA;AAAA,IACN,CAAA;AAAA,EACN,CAAA;AAEZ;"}