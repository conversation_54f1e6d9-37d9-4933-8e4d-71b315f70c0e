{"version": 3, "file": "sellerSetting.nbanners-D-jUrYAG.js", "sources": ["../../../node_modules/lucide-react/dist/esm/icons/circle-minus.js", "../../../node_modules/lucide-react/dist/esm/icons/circle-plus.js", "../../../app/components/common/ActiveBanners.tsx", "../../../app/components/common/InActiveBanners.tsx", "../../../app/routes/sellerSetting.nbanners.tsx"], "sourcesContent": ["/**\n * @license lucide-react v0.453.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst CircleMinus = createLucideIcon(\"CircleMinus\", [\n  [\"circle\", { cx: \"12\", cy: \"12\", r: \"10\", key: \"1mglay\" }],\n  [\"path\", { d: \"M8 12h8\", key: \"1wcyev\" }]\n]);\n\nexport { CircleMinus as default };\n//# sourceMappingURL=circle-minus.js.map\n", "/**\n * @license lucide-react v0.453.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst CirclePlus = createLucideIcon(\"CirclePlus\", [\n  [\"circle\", { cx: \"12\", cy: \"12\", r: \"10\", key: \"1mglay\" }],\n  [\"path\", { d: \"M8 12h8\", key: \"1wcyev\" }],\n  [\"path\", { d: \"M12 8v8\", key: \"napkw2\" }]\n]);\n\nexport { CirclePlus as default };\n//# sourceMappingURL=circle-plus.js.map\n", "import { <PERSON><PERSON><PERSON>, <PERSON>ci<PERSON> } from \"lucide-react\";\r\nimport { But<PERSON> } from \"../ui/button\";\r\nimport { useState, useEffect } from \"react\";\r\nimport { Form, useFetcher } from \"@remix-run/react\";\r\nimport { BuyerNetworkBanners } from \"~/types/api/businessConsoleService/BuyerAccountingResponse\";\r\n\r\ninterface InActiveBannersProps {\r\n      bannerDetails: BuyerNetworkBanners;\r\n}\r\n\r\nexport default function ActiveBanners({ bannerDetails }: InActiveBannersProps) {\r\n      const [sequence, setSequence] = useState(bannerDetails.sequenceId);\r\n      const [isEditing, setIsEditing] = useState(false);\r\n      const [error, setError] = useState<string | null>(null);\r\n      const fetcher = useFetcher();\r\n      const loading = fetcher.state === \"submitting\";\r\n\r\n      // Reset sequence to original value when bannerDetails changes\r\n      useEffect(() => {\r\n            setSequence(bannerDetails.sequenceId);\r\n      }, [bannerDetails.sequenceId]);\r\n\r\n      // Handle input change and reset logic\r\n      const handleChange = (value: string) => {\r\n            const numValue = Number(value);\r\n            setSequence(numValue);\r\n\r\n            if (isNaN(numValue) || numValue < 1) {\r\n                  setError(\"Sequence must be a number greater than 0\");\r\n            } else {\r\n                  setError(null);\r\n            }\r\n      };\r\n\r\n      // Save the sequence\r\n      const handleSave = () => {\r\n            if (error || sequence === bannerDetails.sequenceId) {\r\n                  setIsEditing(false);\r\n                  return;\r\n            }\r\n\r\n            const formData = new FormData();\r\n            formData.append(\"netWorkId\", bannerDetails.networkId.toString());\r\n            formData.append(\"sequenceId\", sequence.toString());\r\n            formData.append(\"actionType\", \"updateSequence\");\r\n            formData.append(\"bannerId\", bannerDetails.id.toString());\r\n\r\n            fetcher.submit(formData, { method: \"put\" });\r\n            setIsEditing(false); // Exit edit mode after submission\r\n      };\r\n\r\n\r\n      return (\r\n            <div className=\"flex flex-col shadow-lg bg-white px-6 py-6 w-full rounded-xl border border-gray-200 mb-2 transition-all hover:shadow-xl\">\r\n                  {/* Banner Image Section */}\r\n                  <div className=\"flex gap-8 border-b border-dashed border-gray-300 pb-4 w-full\">\r\n                        {/* <p className=\"text-2xl font-semibold text-gray-800\">{bannerDetails?.id}.</p> */}\r\n                        <img\r\n                              src={bannerDetails.bannerUrl}\r\n                              alt=\"Banner\"\r\n                              className=\"rounded-lg sm: w-full md:max-w-md h-[160px] object-fill shadow-sm\"\r\n                        />\r\n                  </div>\r\n\r\n                  {/* Sequence & Remove Button Section */}\r\n                  <div className=\"mt-4 w-full\">\r\n                        <p className=\"text-sm text-gray-500 font-medium mb-1\">Sequence</p>\r\n                        <div className=\"flex items-center justify-between w-full gap-2\">\r\n                              {/* Sequence Input/Edit */}\r\n                              <div className=\"relative flex items-center\">\r\n                                    {isEditing ? (\r\n                                          <input\r\n                                                type=\"number\"\r\n                                                value={sequence}\r\n                                                onChange={(e) => handleChange(e.target.value)}\r\n                                                onBlur={() => handleSave()} // Call handleSave directly when input loses focus\r\n                                                onKeyPress={(e) => e.key === \"Enter\" && handleSave()}\r\n                                                className={`w-20 px-3 py-2 text-sm text-gray-800 bg-white border ${error ? \"border-red-500\" : \"border-gray-300 focus:ring-2 focus:ring-blue-500\"\r\n                                                      } rounded-md shadow-sm focus:outline-none transition-all`}\r\n                                                min=\"1\"\r\n                                                autoFocus\r\n                                          />\r\n                                    ) : (\r\n                                          <div className=\"flex items-center gap-2 px-3 py-2 bg-gray-100 border border-gray-300 rounded-md\">\r\n                                                <p className=\"text-sm text-gray-800 font-medium\">{sequence}</p>\r\n                                                <button\r\n                                                      onClick={() => setIsEditing(true)}\r\n                                                      className=\"text-gray-500 hover:text-blue-600 transition-colors\"\r\n                                                >\r\n                                                      <Pencil size={16} />\r\n                                                </button>\r\n                                          </div>\r\n                                    )}\r\n                              </div>\r\n                              {/* Remove Button */}\r\n                              <Form method=\"post\" className=\"flex-shrink-0\">\r\n                                    <input type=\"hidden\" name=\"actionType\" value=\"updateBannerStatus\" />\r\n                                    <input type=\"hidden\" name=\"bannerId\" value={bannerDetails?.id} />\r\n                                    <input\r\n                                          type=\"hidden\"\r\n                                          name=\"status\"\r\n                                          value={bannerDetails?.active != null ? String(bannerDetails.active) : \"false\"}\r\n                                    />\r\n                                    <Button\r\n                                          className=\"flex items-center gap-2 py-2 px-4 text-white bg-secondary-600 hover:bg-secondary-700 rounded-md shadow-md transition-all disabled:opacity-50\"\r\n                                          type=\"submit\"\r\n                                          disabled={loading}\r\n                                    >\r\n                                          {loading ? (\r\n                                                \"Updating...\"\r\n                                          ) : (\r\n                                                <>\r\n                                                      <CircleMinus size={20} />\r\n                                                      <span>Remove Banner</span>\r\n                                                </>\r\n                                          )}\r\n                                    </Button>\r\n                              </Form>\r\n                        </div>\r\n                  </div>\r\n            </div>\r\n      );\r\n}", "import { CirclePlus } from \"lucide-react\";\r\nimport { Button } from \"../ui/button\";\r\nimport { Form, useFetcher } from \"@remix-run/react\";\r\nimport { BuyerNetworkBanners } from \"~/types/api/businessConsoleService/BuyerAccountingResponse\";\r\n\r\n\r\n\r\ninterface InActiveBannersProps {\r\n  bannerDetails: BuyerNetworkBanners;\r\n\r\n}\r\n\r\nexport default function InActiveBanners({ bannerDetails }: InActiveBannersProps) {\r\n\r\n\r\n  const fetcher = useFetcher();\r\n\r\n  const loading = fetcher.state != \"idle\"\r\n\r\n  return (\r\n    <div className=\"flex flex-col shadow-md bg-white p-4 w-full  border-r-8 border-transparent rounded-lg gap-2  h-full\">\r\n      {/* Image Section */}\r\n      <div className=\"flex gap-8 border-b border-dashed border-gray-300 pb-4 w-full\">\r\n        <img\r\n          src={bannerDetails?.bannerUrl}\r\n          alt=\"Banner\"\r\n          className=\"rounded-lg h-[160px] object-cover shadow-sm  sm: w-full md:max-w-md\"\r\n        />\r\n      </div>\r\n      {/* Push the button to the bottom */}\r\n      <div className=\"flex flex-col w-full\">\r\n        <Form method=\"post\" className=\"flex justify-end w-full\">\r\n          <input type=\"hidden\" name=\"actionType\" value=\"updateBannerStatus\" />\r\n          <input type=\"hidden\" name=\"bannerId\" value={bannerDetails?.id} />\r\n          <input\r\n            type=\"hidden\"\r\n            name=\"status\"\r\n            value={bannerDetails?.active != null ? String(bannerDetails?.active) : \"false\"}\r\n          />\r\n          <input type=\"hidden\" name=\"sequence\" value={bannerDetails?.sequenceId} />\r\n          <Button\r\n            className=\"flex items-center gap-2 py-2 px-4 text-white bg-primary-600 hover:bg-red-700 rounded-md shadow-md transition-all disabled:opacity-50\"\r\n            type=\"submit\"\r\n            disabled={loading}\r\n          >\r\n            {loading ? (\r\n              \"Updating...\"\r\n            ) : (\r\n              <>\r\n                <CirclePlus size={20} />\r\n                <span>          Enable Banner\r\n                </span>\r\n              </>\r\n            )}\r\n          </Button>\r\n        </Form>\r\n      </div>\r\n    </div>\r\n\r\n  )\r\n}", "import { ActionFunction, json, LoaderFunction } from \"@remix-run/node\";\r\nimport { useLoaderData } from \"@remix-run/react\";\r\nimport ActiveBanners from \"~/components/common/ActiveBanners\";\r\nimport InActiveBanners from \"~/components/common/InActiveBanners\";\r\nimport { getBuyerNBanners, updateBuyerNBanners, updateBuyerNSequence } from \"~/services/buyerSetting\";\r\nimport { BuyerNetworkBanners } from \"~/types/api/businessConsoleService/BuyerAccountingResponse\";\r\nimport { withAuth, withResponse } from \"~/utils/auth-utils\";\r\n\r\nexport const loader: LoaderFunction = withAuth(async ({ request }) => {\r\n      try {\r\n            const response = await getBuyerNBanners(request);\r\n            return withResponse({ banners: response.data }, response.headers);\r\n      } catch (error) {\r\n            throw new Response(\"Failed to fetch banners\", { status: 500 });\r\n      }\r\n});\r\n\r\nexport const action: ActionFunction = withAuth(async ({ request }) => {\r\n      const formData = await request.formData();\r\n      const bannerId = Number(formData.get(\"bannerId\"));\r\n      const netWorkId = Number(formData.get(\"netWorkId\"));\r\n      const sequenceId = Number(formData.get(\"sequenceId\"));\r\n      const actionType = formData.get(\"actionType\");\r\n\r\n      if (actionType === \"updateBannerStatus\") {\r\n            try {\r\n                  if (!bannerId) return json({ errorMessage: \"BannerId must be a valid number\" });\r\n                  const response = await updateBuyerNBanners(bannerId, request);\r\n                  return withResponse({\r\n                        banners: response.data,\r\n                  }, response.headers);\r\n            } catch (error) {\r\n                  throw new Response(\"Failed to update banner status\", { status: 500 });\r\n            }\r\n      }\r\n\r\n      if (actionType === \"updateSequence\") {\r\n            try {\r\n                  if (!bannerId) return json({ errorMessage: \"BannerId must be a valid number\" });\r\n                  if (!sequenceId) return json({ errorMessage: \"SequenceId must be a valid number\" });\r\n                  if (!netWorkId) return json({ errorMessage: \"NetworkId must be a valid number\" });\r\n                  const payload = { id: bannerId, sequenceId: sequenceId, networkId: netWorkId };\r\n                  const response = await updateBuyerNSequence(payload, request);\r\n                  return withResponse({ message: \"Banner sequence updated successfully\" }, response.headers);\r\n            } catch (error) {\r\n                  throw new Response(\"Failed to update banner sequence\", { status: 500 });\r\n            }\r\n      }\r\n\r\n      return json({ errorMessage: \"Invalid action type\" }, { status: 400 });\r\n});\r\n\r\nexport default function Nbanners() {\r\n      const { banners } = useLoaderData<typeof loader>();\r\n      const inactiveBanners = banners?.filter((banner: BuyerNetworkBanners) => !banner?.active);\r\n      const activeBanners = banners?.filter((banner: BuyerNetworkBanners) => banner?.active);\r\n\r\n      return (\r\n            <div className=\"min-h-screen p-6\">\r\n                  <div className=\"mx-auto mb-6\">\r\n\r\n                        {/* Header */}\r\n                        <div className=\"mb-4\">\r\n                              <div className=\"flex flex-col gap-2 sm:flex-row sm:items-center sm:justify-between\">\r\n                                    <div>\r\n                                          <h1 className=\"text-xl md:text-3xl font-bold text-gray-900\">My Banners</h1>\r\n                                          <p className=\"mt-2 text-gray-600\">Manage your promotional banners and sequences</p>\r\n                                    </div>\r\n                              </div>\r\n                        </div>\r\n\r\n                        {/* Main Content */}\r\n                        <main className=\"max-w-7xl mx-auto\">\r\n                        {/* Active Banners Section */}\r\n                        <section>\r\n                              <h2 className=\"text-xl font-semibold text-foreground mb-4\">Active Banners</h2>\r\n                              {Array.isArray(activeBanners) && activeBanners.length > 0 ? (\r\n                                    <div className=\"grid gap-2 sm:grid-cols-1  \">\r\n                                          {activeBanners.map((banner: BuyerNetworkBanners) => (\r\n                                                <ActiveBanners key={banner.id} bannerDetails={banner} />\r\n                                          ))}\r\n                                    </div>\r\n                              ) : (\r\n                                    <div className=\"flex flex-col items-center justify-center py-12 bg-white rounded-lg border border-border\">\r\n                                          <svg\r\n                                                className=\"w-16 h-16 text-muted-foreground mb-4\"\r\n                                                fill=\"none\"\r\n                                                stroke=\"currentColor\"\r\n                                                viewBox=\"0 0 24 24\"\r\n                                                xmlns=\"http://www.w3.org/2000/svg\"\r\n                                          >\r\n                                                <path\r\n                                                      strokeLinecap=\"round\"\r\n                                                      strokeLinejoin=\"round\"\r\n                                                      strokeWidth=\"2\"\r\n                                                      d=\"M3 12h18M3 6h18M3 18h18\"\r\n                                                />\r\n                                          </svg>\r\n                                          <p className=\"text-lg font-medium text-foreground\">No Active Banners</p>\r\n                                          <p className=\"text-sm text-muted-foreground mt-1\">\r\n                                                Add some banners to get started!\r\n                                          </p>\r\n                                    </div>\r\n                              )}\r\n                        </section>\r\n\r\n                        {/* Inactive Banners Section */}\r\n                        <section className=\"mt-8\">\r\n                              <h2 className=\"text-xl font-semibold text-foreground mb-4\">Inactive Banners</h2>\r\n                              {Array.isArray(inactiveBanners) && inactiveBanners.length > 0 ? (\r\n                                    <div className=\"grid gap-4 sm:grid-cols-1 \">\r\n                                          {inactiveBanners.map((banner: BuyerNetworkBanners) => (\r\n                                                <InActiveBanners key={banner.id} bannerDetails={banner} />\r\n                                          ))}\r\n                                    </div>\r\n                              ) : (\r\n                                    <div className=\"flex flex-col items-center justify-center py-12 bg-white rounded-lg border border-border\">\r\n                                          <svg\r\n                                                className=\"w-16 h-16 text-muted-foreground mb-4\"\r\n                                                fill=\"none\"\r\n                                                stroke=\"currentColor\"\r\n                                                viewBox=\"0 0 24 24\"\r\n                                                xmlns=\"http://www.w3.org/2000/svg\"\r\n                                          >\r\n                                                <path\r\n                                                      strokeLinecap=\"round\"\r\n                                                      strokeLinejoin=\"round\"\r\n                                                      strokeWidth=\"2\"\r\n                                                      d=\"M3 12h18M3 6h18M3 18h18\"\r\n                                                />\r\n                                          </svg>\r\n                                          <p className=\"text-lg font-medium text-foreground\">No Inactive Banners</p>\r\n                                          <p className=\"text-sm text-muted-foreground mt-1\">\r\n                                                Inactive banners will appear here.\r\n                                          </p>\r\n                                    </div>\r\n                              )}\r\n                        </section>\r\n                        </main>\r\n                  </div>\r\n            </div>\r\n      );\r\n}"], "names": ["useState", "useEffect", "jsxs", "jsx", "Fragment", "Nbanners", "banners", "useLoaderData", "inactiveBanners", "filter", "banner", "active", "activeBanners", "className", "children", "Array", "isArray", "length", "map", "ActiveBanners", "bannerDetails", "id", "fill", "stroke", "viewBox", "xmlns", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "InActiveBanners"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA,MAAM,cAAc,iBAAiB,eAAe;AAAA,EAClD,CAAC,UAAU,EAAE,IAAI,MAAM,IAAI,MAAM,GAAG,MAAM,KAAK,UAAU;AAAA,EACzD,CAAC,QAAQ,EAAE,GAAG,WAAW,KAAK,SAAU,CAAA;AAC1C,CAAC;ACZD;AAAA;AAAA;AAAA;AAAA;AAAA;AASA,MAAM,aAAa,iBAAiB,cAAc;AAAA,EAChD,CAAC,UAAU,EAAE,IAAI,MAAM,IAAI,MAAM,GAAG,MAAM,KAAK,UAAU;AAAA,EACzD,CAAC,QAAQ,EAAE,GAAG,WAAW,KAAK,SAAQ,CAAE;AAAA,EACxC,CAAC,QAAQ,EAAE,GAAG,WAAW,KAAK,SAAU,CAAA;AAC1C,CAAC;ACHuB,SAAA,cAAc,EAAE,iBAAuC;AACzE,QAAM,CAAC,UAAU,WAAW,IAAIA,aAAAA,SAAS,cAAc,UAAU;AACjE,QAAM,CAAC,WAAW,YAAY,IAAIA,aAAAA,SAAS,KAAK;AAChD,QAAM,CAAC,OAAO,QAAQ,IAAIA,aAAAA,SAAwB,IAAI;AACtD,QAAM,UAAU,WAAW;AACrB,QAAA,UAAU,QAAQ,UAAU;AAGlCC,eAAAA,UAAU,MAAM;AACV,gBAAY,cAAc,UAAU;AAAA,EAAA,GACvC,CAAC,cAAc,UAAU,CAAC;AAGvB,QAAA,eAAe,CAAC,UAAkB;AAC5B,UAAA,WAAW,OAAO,KAAK;AAC7B,gBAAY,QAAQ;AAEpB,QAAI,MAAM,QAAQ,KAAK,WAAW,GAAG;AAC/B,eAAS,0CAA0C;AAAA,IAAA,OAClD;AACD,eAAS,IAAI;AAAA,IAAA;AAAA,EAEzB;AAGA,QAAM,aAAa,MAAM;AACf,QAAA,SAAS,aAAa,cAAc,YAAY;AAC9C,mBAAa,KAAK;AAClB;AAAA,IAAA;AAGA,UAAA,WAAW,IAAI,SAAS;AAC9B,aAAS,OAAO,aAAa,cAAc,UAAU,UAAU;AAC/D,aAAS,OAAO,cAAc,SAAS,SAAA,CAAU;AACxC,aAAA,OAAO,cAAc,gBAAgB;AAC9C,aAAS,OAAO,YAAY,cAAc,GAAG,UAAU;AAEvD,YAAQ,OAAO,UAAU,EAAE,QAAQ,OAAO;AAC1C,iBAAa,KAAK;AAAA,EACxB;AAIM,SAAAC,kCAAA,KAAC,OAAI,EAAA,WAAU,2HAET,UAAA;AAAA,IAACC,kCAAAA,IAAA,OAAA,EAAI,WAAU,iEAET,UAAAA,kCAAA;AAAA,MAAC;AAAA,MAAA;AAAA,QACK,KAAK,cAAc;AAAA,QACnB,KAAI;AAAA,QACJ,WAAU;AAAA,MAAA;AAAA,IAAA,GAEtB;AAAA,IAGAD,kCAAAA,KAAC,OAAI,EAAA,WAAU,eACT,UAAA;AAAA,MAACC,kCAAA,IAAA,KAAA,EAAE,WAAU,0CAAyC,UAAQ,YAAA;AAAA,MAC9DD,kCAAAA,KAAC,OAAI,EAAA,WAAU,kDAET,UAAA;AAAA,QAACC,kCAAA,IAAA,OAAA,EAAI,WAAU,8BACR,UACK,YAAAA,kCAAA;AAAA,UAAC;AAAA,UAAA;AAAA,YACK,MAAK;AAAA,YACL,OAAO;AAAA,YACP,UAAU,CAAC,MAAM,aAAa,EAAE,OAAO,KAAK;AAAA,YAC5C,QAAQ,MAAM,WAAW;AAAA,YACzB,YAAY,CAAC,MAAM,EAAE,QAAQ,WAAW,WAAW;AAAA,YACnD,WAAW,wDAAwD,QAAQ,mBAAmB,kDACxF;AAAA,YACN,KAAI;AAAA,YACJ,WAAS;AAAA,UAAA;AAAA,QAGf,IAAAD,kCAAA,KAAC,OAAI,EAAA,WAAU,mFACT,UAAA;AAAA,UAACC,kCAAA,IAAA,KAAA,EAAE,WAAU,qCAAqC,UAAS,UAAA;AAAA,UAC3DA,kCAAA;AAAA,YAAC;AAAA,YAAA;AAAA,cACK,SAAS,MAAM,aAAa,IAAI;AAAA,cAChC,WAAU;AAAA,cAEV,UAAAA,kCAAAA,IAAC,QAAO,EAAA,MAAM,GAAI,CAAA;AAAA,YAAA;AAAA,UAAA;AAAA,QACxB,EAAA,CACN,EAEZ,CAAA;AAAA,QAECD,kCAAA,KAAA,MAAA,EAAK,QAAO,QAAO,WAAU,iBACxB,UAAA;AAAA,UAAAC,sCAAC,WAAM,MAAK,UAAS,MAAK,cAAa,OAAM,sBAAqB;AAAA,UAClEA,sCAAC,WAAM,MAAK,UAAS,MAAK,YAAW,OAAO,+CAAe,IAAI;AAAA,UAC/DA,kCAAA;AAAA,YAAC;AAAA,YAAA;AAAA,cACK,MAAK;AAAA,cACL,MAAK;AAAA,cACL,QAAO,+CAAe,WAAU,OAAO,OAAO,cAAc,MAAM,IAAI;AAAA,YAAA;AAAA,UAC5E;AAAA,UACAA,kCAAA;AAAA,YAAC;AAAA,YAAA;AAAA,cACK,WAAU;AAAA,cACV,MAAK;AAAA,cACL,UAAU;AAAA,cAET,UAAA,UACK,gBAGMD,kCAAA,KAAAE,kBAAA,UAAA,EAAA,UAAA;AAAA,gBAACD,kCAAAA,IAAA,aAAA,EAAY,MAAM,GAAI,CAAA;AAAA,gBACvBA,kCAAAA,IAAC,UAAK,UAAa,gBAAA,CAAA;AAAA,cAAA,EACzB,CAAA;AAAA,YAAA;AAAA,UAAA;AAAA,QAEZ,EACN,CAAA;AAAA,MAAA,EACN,CAAA;AAAA,IAAA,EACN,CAAA;AAAA,EAAA,GACN;AAEZ;AC9GwB,SAAA,gBAAgB,EAAE,iBAAuC;AAG/E,QAAM,UAAU,WAAW;AAErB,QAAA,UAAU,QAAQ,SAAS;AAG/B,SAAAD,kCAAA,KAAC,OAAI,EAAA,WAAU,uGAEb,UAAA;AAAA,IAACC,kCAAAA,IAAA,OAAA,EAAI,WAAU,iEACb,UAAAA,kCAAA;AAAA,MAAC;AAAA,MAAA;AAAA,QACC,KAAK,+CAAe;AAAA,QACpB,KAAI;AAAA,QACJ,WAAU;AAAA,MAAA;AAAA,IAAA,GAEd;AAAA,IAEAA,kCAAAA,IAAC,SAAI,WAAU,wBACb,iDAAC,MAAK,EAAA,QAAO,QAAO,WAAU,2BAC5B,UAAA;AAAA,MAAAA,sCAAC,WAAM,MAAK,UAAS,MAAK,cAAa,OAAM,sBAAqB;AAAA,MAClEA,sCAAC,WAAM,MAAK,UAAS,MAAK,YAAW,OAAO,+CAAe,IAAI;AAAA,MAC/DA,kCAAA;AAAA,QAAC;AAAA,QAAA;AAAA,UACC,MAAK;AAAA,UACL,MAAK;AAAA,UACL,QAAO,+CAAe,WAAU,OAAO,OAAO,+CAAe,MAAM,IAAI;AAAA,QAAA;AAAA,MACzE;AAAA,MACAA,sCAAC,WAAM,MAAK,UAAS,MAAK,YAAW,OAAO,+CAAe,YAAY;AAAA,MACvEA,kCAAA;AAAA,QAAC;AAAA,QAAA;AAAA,UACC,WAAU;AAAA,UACV,MAAK;AAAA,UACL,UAAU;AAAA,UAET,UAAA,UACC,gBAGED,kCAAA,KAAAE,kBAAA,UAAA,EAAA,UAAA;AAAA,YAACD,kCAAAA,IAAA,YAAA,EAAW,MAAM,GAAI,CAAA;AAAA,YACtBA,kCAAAA,IAAC,UAAK,UACN,0BAAA,CAAA;AAAA,UAAA,EACF,CAAA;AAAA,QAAA;AAAA,MAAA;AAAA,IAEJ,EAAA,CACF,EACF,CAAA;AAAA,EAAA,GACF;AAGJ;ACRA,SAAwBE,WAAW;AACvB,QAAA;AAAA,IAAEC;AAAAA,EAAQ,IAAIC,cAA6B;AACjD,QAAMC,kBAAkBF,mCAASG,OAAQC,YAAgC,EAACA,iCAAQC;AAClF,QAAMC,gBAAgBN,mCAASG,OAAQC,YAAgCA,iCAAQC;AAE/E,+CACO,OAAI;AAAA,IAAAE,WAAU;AAAA,IACTC,UAACZ,kCAAA,KAAA,OAAA;AAAA,MAAIW,WAAU;AAAA,MAGTC,UAAA,CAACX,kCAAA,IAAA,OAAA;AAAA,QAAIU,WAAU;AAAA,QACTC,UAAAX,kCAAA,IAAC;UAAIU,WAAU;AAAA,UACTC,iDAAC,OACK;AAAA,YAAAA,UAAA,CAACX,kCAAA,IAAA,MAAA;AAAA,cAAGU,WAAU;AAAA,cAA8CC,UAAU;AAAA,YAAA,CAAA,GACrEX,kCAAA,IAAA,KAAA;AAAA,cAAEU,WAAU;AAAA,cAAqBC,UAA6C;AAAA,YAAA,CAAA,CAAA;AAAA,UACrF,CAAA;AAAA,QACN,CAAA;AAAA,MACN,CAAA,GAGAZ,kCAAA,KAAC,QAAK;AAAA,QAAAW,WAAU;AAAA,QAEhBC,UAAA,CAAAZ,kCAAA,KAAC,WACK;AAAA,UAAAY,UAAA,CAACX,kCAAA,IAAA,MAAA;AAAA,YAAGU,WAAU;AAAA,YAA6CC,UAAc;AAAA,UAAA,CAAA,GACxEC,MAAMC,QAAQJ,aAAa,KAAKA,cAAcK,SAAS,IACjDd,kCAAA,IAAA,OAAA;AAAA,YAAIU,WAAU;AAAA,YACRC,UAAcF,cAAAM,IAAKR,YACbP,kCAAAA,IAAAgB,eAAA;AAAA,cAA8BC,eAAeV;AAAAA,YAAA,GAA1BA,OAAOW,EAA2B,CAC3D;AAAA,UAAA,CACP,IAEAnB,kCAAA,KAAC,OAAI;AAAA,YAAAW,WAAU;AAAA,YACTC,UAAA,CAAAX,kCAAA,IAAC,OAAA;AAAA,cACKU,WAAU;AAAA,cACVS,MAAK;AAAA,cACLC,QAAO;AAAA,cACPC,SAAQ;AAAA,cACRC,OAAM;AAAA,cAENX,UAAAX,kCAAA,IAAC,QAAA;AAAA,gBACKuB,eAAc;AAAA,gBACdC,gBAAe;AAAA,gBACfC,aAAY;AAAA,gBACZC,GAAE;AAAA,cACR,CAAA;AAAA,YAAA,CACN,GACC1B,kCAAA,IAAA,KAAA;AAAA,cAAEU,WAAU;AAAA,cAAsCC,UAAiB;AAAA,YAAA,CAAA,GACnEX,kCAAA,IAAA,KAAA;AAAA,cAAEU,WAAU;AAAA,cAAqCC,UAElD;AAAA,YAAA,CAAA,CAAA;AAAA,UACN,CAAA,CAAA;AAAA,QAEZ,CAAA,GAGAZ,kCAAA,KAAC,WAAQ;AAAA,UAAAW,WAAU;AAAA,UACbC,UAAA,CAACX,kCAAA,IAAA,MAAA;AAAA,YAAGU,WAAU;AAAA,YAA6CC,UAAgB;AAAA,UAAA,CAAA,GAC1EC,MAAMC,QAAQR,eAAe,KAAKA,gBAAgBS,SAAS,IACrDd,kCAAA,IAAA,OAAA;AAAA,YAAIU,WAAU;AAAA,YACRC,UAAgBN,gBAAAU,IAAKR,YACfP,kCAAAA,IAAA2B,iBAAA;AAAA,cAAgCV,eAAeV;AAAAA,YAAA,GAA1BA,OAAOW,EAA2B,CAC7D;AAAA,UAAA,CACP,IAEAnB,kCAAA,KAAC,OAAI;AAAA,YAAAW,WAAU;AAAA,YACTC,UAAA,CAAAX,kCAAA,IAAC,OAAA;AAAA,cACKU,WAAU;AAAA,cACVS,MAAK;AAAA,cACLC,QAAO;AAAA,cACPC,SAAQ;AAAA,cACRC,OAAM;AAAA,cAENX,UAAAX,kCAAA,IAAC,QAAA;AAAA,gBACKuB,eAAc;AAAA,gBACdC,gBAAe;AAAA,gBACfC,aAAY;AAAA,gBACZC,GAAE;AAAA,cACR,CAAA;AAAA,YAAA,CACN,GACC1B,kCAAA,IAAA,KAAA;AAAA,cAAEU,WAAU;AAAA,cAAsCC,UAAmB;AAAA,YAAA,CAAA,GACrEX,kCAAA,IAAA,KAAA;AAAA,cAAEU,WAAU;AAAA,cAAqCC,UAElD;AAAA,YAAA,CAAA,CAAA;AAAA,UACN,CAAA,CAAA;AAAA,QAEZ,CAAA,CAAA;AAAA,MACA,CAAA,CAAA;AAAA,IACN,CAAA;AAAA,EACN,CAAA;AAEZ;", "x_google_ignoreList": [0, 1]}