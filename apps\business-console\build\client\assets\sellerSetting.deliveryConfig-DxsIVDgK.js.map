{"version": 3, "file": "sellerSetting.deliveryConfig-DxsIVDgK.js", "sources": ["../../../app/routes/sellerSetting.deliveryConfig.tsx"], "sourcesContent": ["\r\nimport { useState, useEffect } from \"react\"\r\nimport { <PERSON><PERSON> } from \"~/components/ui/button\"\r\nimport { <PERSON>, CardContent, CardHeader, CardTitle } from \"~/components/ui/card\"\r\nimport { Input } from \"~/components/ui/input\"\r\nimport { Label } from \"~/components/ui/label\"\r\nimport { Switch } from \"~/components/ui/switch\"\r\nimport { Trash2, Plus, Truck } from \"lucide-react\"\r\nimport { json, ActionFunction } from \"@remix-run/node\"\r\nimport { useFetcher, useLoaderData, useRevalidator } from \"@remix-run/react\"\r\nimport { withAuth, withResponse } from \"~/utils/auth-utils\"\r\nimport { LoaderFunction } from \"@remix-run/node\"\r\nimport { useToast } from \"~/components/ui/ToastProvider\";\r\nimport { getDeliveryConfigs, createDcConfig, deleteDeliveryConfig } from \"~/services/deliveryConfigService\"\r\nimport { dclistingResponse, DcBody, ConfigType } from \"~/types/api/businessConsoleService/DeliveryConfig\"\r\n\r\ninterface Loaderdata {\r\n  data: dclistingResponse[];\r\n}\r\n\r\nexport const loader: LoaderFunction = withAuth(async ({ request, user }) => {\r\n  const sellerId = user?.userDetails?.sellerId;\r\n  try {\r\n    if (!sellerId) {\r\n      throw new Response(\"Seller ID is required\", { status: 400 });\r\n    }\r\n    const response = await getDeliveryConfigs(sellerId, request);\r\n    return withResponse({ data: response.data?.data }, response.headers);\r\n  } catch (error) {\r\n    console.error(\"Error in loader:\", error);\r\n    throw new Response(\"Failed to fetch coupons data\", {\r\n      status: 500,\r\n    });\r\n  }\r\n});\r\n\r\nexport const action = withAuth(async ({ request, user }) => {\r\n  const formData = await request.formData();\r\n  const actionType = formData.get(\"actionType\");\r\n  const sellerId = user?.userDetails?.sellerId;\r\n\r\n  if (!sellerId) {\r\n    return json(\r\n      { data: null, actionType: actionType, success: false, error: \"Seller ID is required\" },\r\n      { status: 400 }\r\n    );\r\n  }\r\n\r\n  if (actionType === \"deleteDeliveryConfig\") {\r\n    const configId = Number(formData.get(\"configId\"));\r\n    try {\r\n      const response = await deleteDeliveryConfig(configId, request);\r\n      return withResponse(\r\n        { data: response.data, actionType: actionType, success: true },\r\n        response.headers\r\n      );\r\n    } catch (error) {\r\n      console.error(\"Error in action:\", error);\r\n      return json(\r\n        { data: null, actionType: actionType, success: false },\r\n        { status: 500 }\r\n      );\r\n    }\r\n  }\r\n\r\n  if (actionType === \"updateStatus\") {\r\n    const configId = Number(formData.get(\"configId\"));\r\n    const active = formData.get(\"active\") === \"true\";\r\n\r\n    try {\r\n      // Get current config data first, then update only the active status\r\n      const payload: Partial<DcBody> = {\r\n        active: active\r\n      };\r\n\r\n      const response = await createDcConfig(payload, request, configId);\r\n      return withResponse(\r\n        { data: response.data, actionType: actionType, success: true },\r\n        response.headers\r\n      );\r\n    } catch (error) {\r\n      console.error(\"Error in action:\", error);\r\n      return json(\r\n        { data: null, actionType: actionType, success: false },\r\n        { status: 500 }\r\n      );\r\n    }\r\n  }\r\n\r\n  if (actionType === \"addDeliveryConfig\") {\r\n    const slabChangeAt = Number(formData.get(\"slabChangeAt\"));\r\n    const lowerSlabCapAt = Number(formData.get(\"lowerSlabCapAt\"));\r\n    const higherSlabCapAt = Number(formData.get(\"higherSlabCapAt\"));\r\n\r\n    // Create two payloads\r\n    const payload1: Partial<DcBody> = {\r\n      sellerId: sellerId,\r\n      configType: ConfigType.ORDER_VALUE_BASED,\r\n      minOrderValue: 0,\r\n      maxOrderValue: slabChangeAt,\r\n      maxBuyerDeliveryCharge: lowerSlabCapAt,\r\n      // maxSellerDeliveryCharge: 0,\r\n      buyerPercentage: 100,\r\n      sellerPercentage: 0,\r\n      active: true\r\n    };\r\n\r\n    const payload2: Partial<DcBody> = {\r\n      sellerId: sellerId,\r\n      configType: ConfigType.ORDER_VALUE_BASED,\r\n      minOrderValue: slabChangeAt + 1,\r\n      maxOrderValue: 10000,\r\n      maxBuyerDeliveryCharge: higherSlabCapAt,\r\n      buyerPercentage: 100,\r\n      sellerPercentage: 0,\r\n      active: true\r\n    };\r\n\r\n    try {\r\n      // Create first configuration\r\n      await createDcConfig(payload1, request);\r\n      // Create second configuration\r\n      await createDcConfig(payload2, request);\r\n\r\n      return json(\r\n        { data: null, actionType: actionType, success: true },\r\n        { status: 200 }\r\n      );\r\n    } catch (error) {\r\n      console.error(\"Error in action:\", error);\r\n      return json(\r\n        { data: null, actionType: actionType, success: false },\r\n        { status: 500 }\r\n      );\r\n    }\r\n  }\r\n\r\n  console.log(\"Invalid action type:\", actionType);\r\n  return json(\r\n    { data: null, actionType: actionType, success: false },\r\n    { status: 400 }\r\n  );\r\n});\r\n\r\nexport function DeliveryConfigCard({\r\n  config,\r\n  onDelete,\r\n  onStatusToggle,\r\n}: {\r\n  config: dclistingResponse;\r\n  onDelete: (configId: number) => void;\r\n  onStatusToggle: (configId: number, currentStatus: boolean) => void;\r\n}) {\r\n  return (\r\n    <div className=\"p-4 rounded-xl shadow-[0px_2px_8px_0px_rgba(0,0,0,0.1)] hover:shadow-[0px_4px_12px_0px_rgba(0,0,0,0.15)] transition-shadow duration-200 bg-white border border-neutral-100\">\r\n      <div className=\"mb-3\">\r\n        <div className=\"flex flex-row justify-between items-center\">\r\n          <div className=\"p-1 border border-neutral-600 rounded-md bg-[#FCFCFD]\">\r\n            <h3 className=\"text-base font-semibold text-typography-400\">\r\n              Config #{config.id}\r\n            </h3>\r\n          </div>\r\n\r\n          <span\r\n            className={`px-2 py-1 rounded-md text-xs font-medium ${config.active\r\n              ? \"bg-green-100 text-green-800\"\r\n              : \"bg-red-100 text-red-800\"\r\n              }`}\r\n          >\r\n            {config.active ? \"Active\" : \"Inactive\"}\r\n          </span>\r\n        </div>\r\n        <p className=\"mt-2 text-sm text-typography-500\">\r\n          {config.configType === ConfigType.ORDER_VALUE_BASED\r\n            ? \"Order value based delivery configuration\"\r\n            : \"Percentage based delivery configuration\"}\r\n        </p>\r\n      </div>\r\n\r\n      <div className=\"border-b border-neutral-200\" />\r\n\r\n      <div className=\"my-3\">\r\n        <div className=\"grid grid-cols-2 gap-4\">\r\n          <div>\r\n            <p className=\"text-xs text-typography-400 mb-1\">Order Range</p>\r\n            <p className=\"text-sm font-medium text-typography-700\">\r\n              ₹{config.minOrderValue} - ₹{config.maxOrderValue}\r\n            </p>\r\n          </div>\r\n          <div className=\"text-right\">\r\n            <p className=\"text-xs text-typography-400 mb-1\">Max Buyer Charge</p>\r\n            <p className=\"text-sm font-medium text-typography-700\">\r\n              ₹{config.maxBuyerDeliveryCharge}\r\n            </p>\r\n          </div>\r\n        </div>\r\n        <div className=\"mt-4 grid grid-cols-2 gap-4\">\r\n          <div>\r\n            <p className=\"text-xs text-typography-400 mb-1\">Buyer Share</p>\r\n            <p className=\"text-sm font-medium text-typography-700\">\r\n              {config.buyerPercentage}%\r\n            </p>\r\n          </div>\r\n          <div className=\"text-right\">\r\n            <p className=\"text-xs text-typography-400 mb-1\">Seller Share</p>\r\n            <p className=\"text-sm font-medium text-typography-700\">\r\n              {config.sellerPercentage}%\r\n            </p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"border-b border-neutral-200\" />\r\n\r\n      <div className=\"relative mt-3 flex flex-row gap-3 justify-between items-center\">\r\n        <div className=\"flex items-center gap-2\">\r\n          <Switch\r\n            checked={config.active}\r\n            onCheckedChange={() => onStatusToggle(config.id, config.active)}\r\n          />\r\n          <span className=\"text-sm text-typography-600\">\r\n            {config.active ? \"Active\" : \"Inactive\"}\r\n          </span>\r\n        </div>\r\n\r\n        <button\r\n          className=\"border border-neutral-200 rounded-full p-2 cursor-pointer hover:bg-red-50 hover:border-red-200 transition-colors duration-200\"\r\n          onClick={() => onDelete(config.id)}\r\n          aria-label=\"Delete delivery configuration\"\r\n        >\r\n          <Trash2 className=\"w-4 h-4 text-red-500\" />\r\n        </button>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default function DeliveryConfig() {\r\n  const { data: deliveryConfigs } = useLoaderData<Loaderdata>();\r\n  const { revalidate } = useRevalidator();\r\n  const [showForm, setShowForm] = useState(false);\r\n  const [slabChangeAt, setSlabChangeAt] = useState(400);\r\n  const [lowerSlabCapAt, setLowerSlabCapAt] = useState(60);\r\n  const [higherSlabCapAt, setHigherSlabCapAt] = useState(30);\r\n\r\n  const fetcher = useFetcher<{\r\n    data: void;\r\n    success: boolean;\r\n    actionType: string;\r\n  }>();\r\n\r\n  const { showToast } = useToast();\r\n\r\n  useEffect(() => {\r\n    if (fetcher.state === \"idle\" && fetcher.data) {\r\n      if (\r\n        fetcher.data.success === true &&\r\n        fetcher.data.actionType === \"deleteDeliveryConfig\"\r\n      ) {\r\n        showToast(\"Delivery Configuration deleted successfully\", \"success\");\r\n        revalidate();\r\n      } else if (\r\n        fetcher.data.success === false &&\r\n        fetcher.data.actionType === \"deleteDeliveryConfig\"\r\n      ) {\r\n        showToast(\"Failed to delete Delivery Configuration\", \"error\");\r\n      } else if (\r\n        fetcher.data.success === true &&\r\n        fetcher.data.actionType === \"addDeliveryConfig\"\r\n      ) {\r\n        showToast(\"Delivery Configuration created successfully\", \"success\");\r\n        setShowForm(false);\r\n        revalidate();\r\n      } else if (\r\n        fetcher.data.success === false &&\r\n        fetcher.data.actionType === \"addDeliveryConfig\"\r\n      ) {\r\n        showToast(\"Failed to create Delivery Configuration\", \"error\");\r\n      } else if (\r\n        fetcher.data.success === true &&\r\n        fetcher.data.actionType === \"updateStatus\"\r\n      ) {\r\n        showToast(\"Delivery Configuration status updated successfully\", \"success\");\r\n        revalidate();\r\n      } else if (\r\n        fetcher.data.success === false &&\r\n        fetcher.data.actionType === \"updateStatus\"\r\n      ) {\r\n        showToast(\"Failed to update Delivery Configuration status\", \"error\");\r\n      }\r\n    }\r\n  }, [fetcher.state, fetcher.data]);\r\n\r\n  const handleDelete = (configId: number) => {\r\n    if (confirm(\"Are you sure you want to delete this delivery configuration?\")) {\r\n      const formData = new FormData();\r\n      formData.append(\"actionType\", \"deleteDeliveryConfig\");\r\n      formData.append(\"configId\", configId.toString());\r\n      fetcher.submit(formData, { method: \"POST\" });\r\n    }\r\n  };\r\n\r\n  const handleStatusToggle = (configId: number, currentStatus: boolean) => {\r\n    const formData = new FormData();\r\n    formData.append(\"actionType\", \"updateStatus\");\r\n    formData.append(\"configId\", configId.toString());\r\n    formData.append(\"active\", (!currentStatus).toString());\r\n    fetcher.submit(formData, { method: \"POST\" });\r\n  };\r\n\r\n  const handleAddConfig = () => {\r\n    const formData = new FormData();\r\n    formData.append(\"actionType\", \"addDeliveryConfig\");\r\n    formData.append(\"slabChangeAt\", slabChangeAt.toString());\r\n    formData.append(\"lowerSlabCapAt\", lowerSlabCapAt.toString());\r\n    formData.append(\"higherSlabCapAt\", higherSlabCapAt.toString());\r\n    fetcher.submit(formData, { method: \"POST\" });\r\n  };\r\n\r\n  return (\r\n    <div className=\"p-6\">\r\n      <div className=\"mb-6\">\r\n        <h1 className=\"text-3xl font-bold text-gray-900\">Delivery Charge Configurations</h1>\r\n        <p className=\"text-gray-600 mt-2\">Manage your delivery charge configurations</p>\r\n      </div>\r\n\r\n      <div\r\n        aria-labelledby=\"delivery-configs-list\"\r\n        className=\"pb-20 md:pb-5\"\r\n      >\r\n        {deliveryConfigs && deliveryConfigs.length > 0 ? (\r\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-4\">\r\n            {deliveryConfigs.map((config) => (\r\n              <DeliveryConfigCard\r\n                key={config.id}\r\n                config={config}\r\n                onDelete={handleDelete}\r\n                onStatusToggle={handleStatusToggle}\r\n              />\r\n            ))}\r\n          </div>\r\n        ) : (\r\n          <div className=\"flex flex-col items-center justify-center py-10 text-center\">\r\n            <div className=\"w-full max-w-4xl\">\r\n              <div className=\"text-center mb-8\">\r\n                <Truck className=\"h-16 w-16 text-gray-400 mx-auto mb-4\" />\r\n                <h3 className=\"text-2xl font-semibold text-foreground mb-2\">\r\n                  Create Your First Delivery Configuration\r\n                </h3>\r\n                <p className=\"text-muted-foreground\">\r\n                  Set up your delivery charge configuration to manage how delivery costs are split between you and your customers.\r\n                </p>\r\n              </div>\r\n\r\n              {!showForm ? (\r\n                <div className=\"text-center\">\r\n                  <Button\r\n                    onClick={() => setShowForm(true)}\r\n                    className=\"bg-blue-600 hover:bg-blue-700 text-white px-6 py-3\"\r\n                  >\r\n                    <Plus className=\"h-4 w-4 mr-2\" />\r\n                    Add Delivery Configuration\r\n                  </Button>\r\n                </div>\r\n              ) : (\r\n                <Card className=\"text-left\">\r\n                  <CardHeader>\r\n                    <CardTitle className=\"text-lg\">Configure Delivery Charges</CardTitle>\r\n                  </CardHeader>\r\n                  <CardContent className=\"space-y-4\">\r\n                    <div>\r\n                      <Label htmlFor=\"slabChangeAt\" className=\"text-sm font-medium\">\r\n                        Order Value Threshold (₹)\r\n                      </Label>\r\n                      <Input\r\n                        id=\"slabChangeAt\"\r\n                        type=\"number\"\r\n                        value={slabChangeAt}\r\n                        onChange={(e) => setSlabChangeAt(Number(e.target.value))}\r\n                        className=\"mt-1\"\r\n                        placeholder=\"400\"\r\n                      />\r\n                      <p className=\"text-xs text-gray-500 mt-1\">\r\n                        Orders above this amount will have lower delivery charges\r\n                      </p>\r\n                    </div>\r\n\r\n                    <div>\r\n                      <Label htmlFor=\"lowerSlabCapAt\" className=\"text-sm font-medium\">\r\n                        Max Delivery Charge for Orders Below Threshold (₹)\r\n                      </Label>\r\n                      <Input\r\n                        id=\"lowerSlabCapAt\"\r\n                        type=\"number\"\r\n                        value={lowerSlabCapAt}\r\n                        onChange={(e) => setLowerSlabCapAt(Number(e.target.value))}\r\n                        className=\"mt-1\"\r\n                        placeholder=\"60\"\r\n                      />\r\n                      <p className=\"text-xs text-gray-500 mt-1\">\r\n                        Maximum amount customers pay for orders under ₹{slabChangeAt}\r\n                      </p>\r\n                    </div>\r\n\r\n                    <div>\r\n                      <Label htmlFor=\"higherSlabCapAt\" className=\"text-sm font-medium\">\r\n                        Max Delivery Charge for Orders Above Threshold (₹)\r\n                      </Label>\r\n                      <Input\r\n                        id=\"higherSlabCapAt\"\r\n                        type=\"number\"\r\n                        value={higherSlabCapAt}\r\n                        onChange={(e) => setHigherSlabCapAt(Number(e.target.value))}\r\n                        className=\"mt-1\"\r\n                        placeholder=\"30\"\r\n                      />\r\n                      <p className=\"text-xs text-gray-500 mt-1\">\r\n                        Maximum amount customers pay for orders above ₹{slabChangeAt}\r\n                      </p>\r\n                    </div>\r\n\r\n                    <div className=\"bg-blue-50 p-3 rounded-lg\">\r\n                      <h4 className=\"text-sm font-medium text-blue-900 mb-2\">How it works:</h4>\r\n                      <ul className=\"text-xs text-blue-800 space-y-1\">\r\n                        <li>• Orders under ₹{slabChangeAt}: Customer pays max ₹{lowerSlabCapAt}, you cover the rest</li>\r\n                        <li>• Orders above ₹{slabChangeAt}: Customer pays max ₹{higherSlabCapAt}, you cover the rest</li>\r\n                        <li>• This encourages larger orders while keeping delivery affordable</li>\r\n                      </ul>\r\n                    </div>\r\n\r\n                    <div className=\"flex gap-3 pt-4\">\r\n                      <Button\r\n                        onClick={handleAddConfig}\r\n                        disabled={fetcher.state === \"submitting\" || fetcher.state === \"loading\"}\r\n                        className=\"bg-blue-600 hover:bg-blue-700 text-white flex-1\"\r\n                      >\r\n                        {fetcher.state === \"submitting\" ? \"Saving...\" : \"Save Configuration\"}\r\n                      </Button>\r\n                      <Button\r\n                        variant=\"outline\"\r\n                        onClick={() => setShowForm(false)}\r\n                        className=\"flex-1\"\r\n                      >\r\n                        Cancel\r\n                      </Button>\r\n                    </div>\r\n                  </CardContent>\r\n                </Card>\r\n              )}\r\n            </div>\r\n          </div>\r\n        )}\r\n      </div>\r\n\r\n      <div className=\"fixed bottom-0 left-0 right-0 z-30 p-4 flex justify-center md:static md:justify-end md:border-0\">\r\n        <Button\r\n          onClick={() => setShowForm(true)}\r\n          className=\"w-full md:w-auto\"\r\n        >\r\n          Add New Delivery Configuration\r\n        </Button>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": ["DeliveryConfigCard", "config", "onDelete", "onStatusToggle", "jsxs", "className", "children", "jsx", "id", "active", "configType", "ConfigType", "ORDER_VALUE_BASED", "minOrderValue", "maxOrderValue", "maxBuyerDeliveryCharge", "buyerPercentage", "sellerPercentage", "Switch", "checked", "onCheckedChange", "onClick", "Trash2", "DeliveryConfig", "data", "deliveryConfigs", "useLoaderData", "revalidate", "useRevalidator", "showForm", "setShowForm", "useState", "slabChangeAt", "setSlabChangeAt", "lowerSlabCapAt", "setLowerSlabCapAt", "higherSlabCapAt", "setHigherSlabCapAt", "fetcher", "useFetcher", "showToast", "useToast", "useEffect", "state", "success", "actionType", "handleDelete", "configId", "confirm", "formData", "FormData", "append", "toString", "submit", "method", "handleStatusToggle", "currentStatus", "handleAddConfig", "length", "map", "Truck", "<PERSON><PERSON>", "Plus", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardTitle", "<PERSON><PERSON><PERSON><PERSON>", "Label", "htmlFor", "Input", "type", "value", "onChange", "e", "Number", "target", "placeholder", "disabled", "variant"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAgJO,SAASA,mBAAmB;AAAA,EACjCC;AAAAA,EACAC;AAAAA,EACAC;AACF,GAIG;AAEC,SAAAC,kCAAAA,KAAC,OAAI;AAAA,IAAAC,WAAU;AAAA,IACbC,UAAA,CAACF,kCAAA,KAAA,OAAA;AAAA,MAAIC,WAAU;AAAA,MACbC,UAAA,CAACF,kCAAA,KAAA,OAAA;AAAA,QAAIC,WAAU;AAAA,QACbC,UAAA,CAAAC,kCAAA,IAAC;UAAIF,WAAU;AAAA,UACbC,UAACF,kCAAA,KAAA,MAAA;AAAA,YAAGC,WAAU;AAAA,YAA8CC,UAAA,CAAA,YACjDL,OAAOO,EAAA;AAAA,UAClB,CAAA;AAAA,QACF,CAAA,GAEAD,kCAAA,IAAC,QAAA;AAAA,UACCF,WAAW,4CAA4CJ,OAAOQ,SAC1D,gCACA,yBACF;AAAA,UAEDH,UAAAL,OAAOQ,SAAS,WAAW;AAAA,QAAA,CAC9B,CAAA;AAAA,MACF,CAAA,GACAF,kCAAA,IAAC;QAAEF,WAAU;AAAA,QACVC,iBAAOI,eAAeC,WAAWC,oBAC9B,6CACA;AAAA,MACN,CAAA,CAAA;AAAA,IACF,CAAA,GAEAL,kCAAA,IAAC,OAAI;AAAA,MAAAF,WAAU;AAAA,IAA8B,CAAA,GAE7CD,kCAAA,KAAC,OAAI;AAAA,MAAAC,WAAU;AAAA,MACbC,UAAA,CAACF,kCAAA,KAAA,OAAA;AAAA,QAAIC,WAAU;AAAA,QACbC,UAAA,CAAAF,kCAAA,KAAC,OACC;AAAA,UAAAE,UAAA,CAACC,kCAAA,IAAA,KAAA;AAAA,YAAEF,WAAU;AAAA,YAAmCC,UAAW;AAAA,UAAA,CAAA,GAC3DF,kCAAA,KAAC,KAAE;AAAA,YAAAC,WAAU;AAAA,YAA0CC,UAAA,CAAA,KACnDL,OAAOY,eAAc,QAAKZ,OAAOa,aAAA;AAAA,UACrC,CAAA,CAAA;AAAA,QACF,CAAA,GACAV,kCAAA,KAAC,OAAI;AAAA,UAAAC,WAAU;AAAA,UACbC,UAAA,CAACC,kCAAA,IAAA,KAAA;AAAA,YAAEF,WAAU;AAAA,YAAmCC,UAAgB;AAAA,UAAA,CAAA,GAChEF,kCAAA,KAAC,KAAE;AAAA,YAAAC,WAAU;AAAA,YAA0CC,UAAA,CAAA,KACnDL,OAAOc,sBAAA;AAAA,UACX,CAAA,CAAA;AAAA,QACF,CAAA,CAAA;AAAA,MACF,CAAA,GACAX,kCAAA,KAAC,OAAI;AAAA,QAAAC,WAAU;AAAA,QACbC,UAAA,CAAAF,kCAAA,KAAC,OACC;AAAA,UAAAE,UAAA,CAACC,kCAAA,IAAA,KAAA;AAAA,YAAEF,WAAU;AAAA,YAAmCC,UAAW;AAAA,UAAA,CAAA,GAC3DF,kCAAA,KAAC,KAAE;AAAA,YAAAC,WAAU;AAAA,YACVC,UAAA,CAAOL,OAAAe,iBAAgB,GAAA;AAAA,UAC1B,CAAA,CAAA;AAAA,QACF,CAAA,GACAZ,kCAAA,KAAC,OAAI;AAAA,UAAAC,WAAU;AAAA,UACbC,UAAA,CAACC,kCAAA,IAAA,KAAA;AAAA,YAAEF,WAAU;AAAA,YAAmCC,UAAY;AAAA,UAAA,CAAA,GAC5DF,kCAAA,KAAC,KAAE;AAAA,YAAAC,WAAU;AAAA,YACVC,UAAA,CAAOL,OAAAgB,kBAAiB,GAAA;AAAA,UAC3B,CAAA,CAAA;AAAA,QACF,CAAA,CAAA;AAAA,MACF,CAAA,CAAA;AAAA,IACF,CAAA,GAEAV,kCAAA,IAAC,OAAI;AAAA,MAAAF,WAAU;AAAA,IAA8B,CAAA,GAE7CD,kCAAA,KAAC,OAAI;AAAA,MAAAC,WAAU;AAAA,MACbC,UAAA,CAACF,kCAAA,KAAA,OAAA;AAAA,QAAIC,WAAU;AAAA,QACbC,UAAA,CAAAC,kCAAA,IAACW,QAAA;AAAA,UACCC,SAASlB,OAAOQ;AAAAA,UAChBW,iBAAiBA,MAAMjB,eAAeF,OAAOO,IAAIP,OAAOQ,MAAM;AAAA,QAAA,CAChE,yCACC,QAAK;AAAA,UAAAJ,WAAU;AAAA,UACbC,UAAOL,OAAAQ,SAAS,WAAW;AAAA,QAC9B,CAAA,CAAA;AAAA,MACF,CAAA,GAEAF,kCAAA,IAAC,UAAA;AAAA,QACCF,WAAU;AAAA,QACVgB,SAASA,MAAMnB,SAASD,OAAOO,EAAE;AAAA,QACjC,cAAW;AAAA,QAEXF,UAAAC,kCAAA,IAACe,QAAO;AAAA,UAAAjB,WAAU;AAAA,QAAuB,CAAA;AAAA,MAAA,CAC3C,CAAA;AAAA,IACF,CAAA,CAAA;AAAA,EACF,CAAA;AAEJ;AAEA,SAAwBkB,iBAAiB;AACvC,QAAM;AAAA,IAAEC,MAAMC;AAAAA,EAAgB,IAAIC,cAA0B;AACtD,QAAA;AAAA,IAAEC;AAAAA,EAAW,IAAIC,eAAe;AACtC,QAAM,CAACC,UAAUC,WAAW,IAAIC,aAAAA,SAAS,KAAK;AAC9C,QAAM,CAACC,cAAcC,eAAe,IAAIF,aAAAA,SAAS,GAAG;AACpD,QAAM,CAACG,gBAAgBC,iBAAiB,IAAIJ,aAAAA,SAAS,EAAE;AACvD,QAAM,CAACK,iBAAiBC,kBAAkB,IAAIN,aAAAA,SAAS,EAAE;AAEzD,QAAMO,UAAUC,WAIb;AAEG,QAAA;AAAA,IAAEC;AAAAA,EAAU,IAAIC,SAAS;AAE/BC,eAAAA,UAAU,MAAM;AACd,QAAIJ,QAAQK,UAAU,UAAUL,QAAQd,MAAM;AAC5C,UACEc,QAAQd,KAAKoB,YAAY,QACzBN,QAAQd,KAAKqB,eAAe,wBAC5B;AACAL,kBAAU,+CAA+C,SAAS;AACvDb,mBAAA;AAAA,MACb,WACEW,QAAQd,KAAKoB,YAAY,SACzBN,QAAQd,KAAKqB,eAAe,wBAC5B;AACAL,kBAAU,2CAA2C,OAAO;AAAA,MAC9D,WACEF,QAAQd,KAAKoB,YAAY,QACzBN,QAAQd,KAAKqB,eAAe,qBAC5B;AACAL,kBAAU,+CAA+C,SAAS;AAClEV,oBAAY,KAAK;AACNH,mBAAA;AAAA,MACb,WACEW,QAAQd,KAAKoB,YAAY,SACzBN,QAAQd,KAAKqB,eAAe,qBAC5B;AACAL,kBAAU,2CAA2C,OAAO;AAAA,MAC9D,WACEF,QAAQd,KAAKoB,YAAY,QACzBN,QAAQd,KAAKqB,eAAe,gBAC5B;AACAL,kBAAU,sDAAsD,SAAS;AAC9Db,mBAAA;AAAA,MACb,WACEW,QAAQd,KAAKoB,YAAY,SACzBN,QAAQd,KAAKqB,eAAe,gBAC5B;AACAL,kBAAU,kDAAkD,OAAO;AAAA,MACrE;AAAA,IACF;AAAA,KACC,CAACF,QAAQK,OAAOL,QAAQd,IAAI,CAAC;AAE1B,QAAAsB,eAAgBC,cAAqB;AACrC,QAAAC,QAAQ,8DAA8D,GAAG;AACrE,YAAAC,WAAW,IAAIC,SAAS;AACrBD,eAAAE,OAAO,cAAc,sBAAsB;AACpDF,eAASE,OAAO,YAAYJ,SAASK,SAAA,CAAU;AAC/Cd,cAAQe,OAAOJ,UAAU;AAAA,QAAEK,QAAQ;AAAA,MAAO,CAAC;AAAA,IAC7C;AAAA,EACF;AAEM,QAAAC,qBAAqBA,CAACR,UAAkBS,kBAA2B;AACjE,UAAAP,WAAW,IAAIC,SAAS;AACrBD,aAAAE,OAAO,cAAc,cAAc;AAC5CF,aAASE,OAAO,YAAYJ,SAASK,SAAA,CAAU;AAC/CH,aAASE,OAAO,WAAW,CAACK,eAAeJ,UAAU;AACrDd,YAAQe,OAAOJ,UAAU;AAAA,MAAEK,QAAQ;AAAA,IAAO,CAAC;AAAA,EAC7C;AAEA,QAAMG,kBAAkBA,MAAM;AACtB,UAAAR,WAAW,IAAIC,SAAS;AACrBD,aAAAE,OAAO,cAAc,mBAAmB;AACjDF,aAASE,OAAO,gBAAgBnB,aAAaoB,SAAA,CAAU;AACvDH,aAASE,OAAO,kBAAkBjB,eAAekB,SAAA,CAAU;AAC3DH,aAASE,OAAO,mBAAmBf,gBAAgBgB,SAAA,CAAU;AAC7Dd,YAAQe,OAAOJ,UAAU;AAAA,MAAEK,QAAQ;AAAA,IAAO,CAAC;AAAA,EAC7C;AAGE,SAAAlD,kCAAAA,KAAC,OAAI;AAAA,IAAAC,WAAU;AAAA,IACbC,UAAA,CAACF,kCAAA,KAAA,OAAA;AAAA,MAAIC,WAAU;AAAA,MACbC,UAAA,CAACC,kCAAA,IAAA,MAAA;AAAA,QAAGF,WAAU;AAAA,QAAmCC,UAA8B;AAAA,MAAA,CAAA,GAC9EC,kCAAA,IAAA,KAAA;AAAA,QAAEF,WAAU;AAAA,QAAqBC,UAA0C;AAAA,MAAA,CAAA,CAAA;AAAA,IAC9E,CAAA,GAEAC,kCAAA,IAAC,OAAA;AAAA,MACC,mBAAgB;AAAA,MAChBF,WAAU;AAAA,MAETC,UAAAmB,mBAAmBA,gBAAgBiC,SAAS,IAC3CnD,kCAAA,IAAC,OAAI;AAAA,QAAAF,WAAU;AAAA,QACZC,UAAAmB,gBAAgBkC,IAAK1D,YACpBM,kCAAAA,IAACP,oBAAA;AAAA,UAECC;AAAAA,UACAC,UAAU4C;AAAAA,UACV3C,gBAAgBoD;AAAAA,QAAA,GAHXtD,OAAOO,EAId,CACD;AAAA,MACH,CAAA,IAECD,kCAAA,IAAA,OAAA;AAAA,QAAIF,WAAU;AAAA,QACbC,UAAAF,kCAAA,KAAC,OAAI;AAAA,UAAAC,WAAU;AAAA,UACbC,UAAA,CAACF,kCAAA,KAAA,OAAA;AAAA,YAAIC,WAAU;AAAA,YACbC,UAAA,CAACC,kCAAA,IAAAqD,OAAA;AAAA,cAAMvD,WAAU;AAAA,YAAuC,CAAA,GACvDE,kCAAA,IAAA,MAAA;AAAA,cAAGF,WAAU;AAAA,cAA8CC,UAE5D;AAAA,YAAA,CAAA,GACCC,kCAAA,IAAA,KAAA;AAAA,cAAEF,WAAU;AAAA,cAAwBC,UAErC;AAAA,YAAA,CAAA,CAAA;AAAA,UACF,CAAA,GAEC,CAACuB,WACCtB,kCAAA,IAAA,OAAA;AAAA,YAAIF,WAAU;AAAA,YACbC,UAAAF,kCAAA,KAACyD,QAAA;AAAA,cACCxC,SAASA,MAAMS,YAAY,IAAI;AAAA,cAC/BzB,WAAU;AAAA,cAEVC,UAAA,CAACC,kCAAA,IAAAuD,MAAA;AAAA,gBAAKzD,WAAU;AAAA,cAAe,CAAA,GAAE,4BAAA;AAAA,YAEnC,CAAA;AAAA,UACF,CAAA,IAECD,kCAAA,KAAA2D,MAAA;AAAA,YAAK1D,WAAU;AAAA,YACdC,UAAA,CAAAC,kCAAA,IAACyD;cACC1D,UAACC,kCAAA,IAAA0D,WAAA;AAAA,gBAAU5D,WAAU;AAAA,gBAAUC;cAA0B,CAAA;AAAA,YAC3D,CAAA,GACAF,kCAAA,KAAC8D,aAAY;AAAA,cAAA7D,WAAU;AAAA,cACrBC,UAAA,CAAAF,kCAAA,KAAC,OACC;AAAA,gBAAAE,UAAA,CAAAC,kCAAA,IAAC4D,OAAM;AAAA,kBAAAC,SAAQ;AAAA,kBAAe/D,WAAU;AAAA,kBAAsBC,UAE9D;AAAA,gBAAA,CAAA,GACAC,kCAAA,IAAC8D,OAAA;AAAA,kBACC7D,IAAG;AAAA,kBACH8D,MAAK;AAAA,kBACLC,OAAOvC;AAAAA,kBACPwC,UAAWC,OAAMxC,gBAAgByC,OAAOD,EAAEE,OAAOJ,KAAK,CAAC;AAAA,kBACvDlE,WAAU;AAAA,kBACVuE,aAAY;AAAA,gBAAA,CACd,GACCrE,kCAAA,IAAA,KAAA;AAAA,kBAAEF,WAAU;AAAA,kBAA6BC,UAE1C;AAAA,gBAAA,CAAA,CAAA;AAAA,cACF,CAAA,0CAEC,OACC;AAAA,gBAAAA,UAAA,CAAAC,kCAAA,IAAC4D,OAAM;AAAA,kBAAAC,SAAQ;AAAA,kBAAiB/D,WAAU;AAAA,kBAAsBC,UAEhE;AAAA,gBAAA,CAAA,GACAC,kCAAA,IAAC8D,OAAA;AAAA,kBACC7D,IAAG;AAAA,kBACH8D,MAAK;AAAA,kBACLC,OAAOrC;AAAAA,kBACPsC,UAAWC,OAAMtC,kBAAkBuC,OAAOD,EAAEE,OAAOJ,KAAK,CAAC;AAAA,kBACzDlE,WAAU;AAAA,kBACVuE,aAAY;AAAA,gBAAA,CACd,GACAxE,kCAAA,KAAC,KAAE;AAAA,kBAAAC,WAAU;AAAA,kBAA6BC,UAAA,CAAA,mDACQ0B,YAAA;AAAA,gBAClD,CAAA,CAAA;AAAA,cACF,CAAA,0CAEC,OACC;AAAA,gBAAA1B,UAAA,CAAAC,kCAAA,IAAC4D,OAAM;AAAA,kBAAAC,SAAQ;AAAA,kBAAkB/D,WAAU;AAAA,kBAAsBC,UAEjE;AAAA,gBAAA,CAAA,GACAC,kCAAA,IAAC8D,OAAA;AAAA,kBACC7D,IAAG;AAAA,kBACH8D,MAAK;AAAA,kBACLC,OAAOnC;AAAAA,kBACPoC,UAAWC,OAAMpC,mBAAmBqC,OAAOD,EAAEE,OAAOJ,KAAK,CAAC;AAAA,kBAC1DlE,WAAU;AAAA,kBACVuE,aAAY;AAAA,gBAAA,CACd,GACAxE,kCAAA,KAAC,KAAE;AAAA,kBAAAC,WAAU;AAAA,kBAA6BC,UAAA,CAAA,mDACQ0B,YAAA;AAAA,gBAClD,CAAA,CAAA;AAAA,cACF,CAAA,GAEA5B,kCAAA,KAAC,OAAI;AAAA,gBAAAC,WAAU;AAAA,gBACbC,UAAA,CAACC,kCAAA,IAAA,MAAA;AAAA,kBAAGF,WAAU;AAAA,kBAAyCC,UAAa;AAAA,gBAAA,CAAA,GACpEF,kCAAA,KAAC,MAAG;AAAA,kBAAAC,WAAU;AAAA,kBACZC,UAAA,CAAAF,kCAAA,KAAC,MAAG;AAAA,oBAAAE,UAAA,CAAA,oBAAiB0B,cAAa,yBAAsBE,gBAAe,sBAAA;AAAA,kBAAoB,CAAA,0CAC1F,MAAG;AAAA,oBAAA5B,UAAA,CAAA,oBAAiB0B,cAAa,yBAAsBI,iBAAgB,sBAAA;AAAA,kBAAoB,CAAA,GAC5F7B,kCAAA,IAAC;oBAAGD,UAAiE;AAAA,kBAAA,CAAA,CAAA;AAAA,gBACvE,CAAA,CAAA;AAAA,cACF,CAAA,GAEAF,kCAAA,KAAC,OAAI;AAAA,gBAAAC,WAAU;AAAA,gBACbC,UAAA,CAAAC,kCAAA,IAACsD,QAAA;AAAA,kBACCxC,SAASoC;AAAAA,kBACToB,UAAUvC,QAAQK,UAAU,gBAAgBL,QAAQK,UAAU;AAAA,kBAC9DtC,WAAU;AAAA,kBAETC,UAAAgC,QAAQK,UAAU,eAAe,cAAc;AAAA,gBAAA,CAClD,GACApC,kCAAA,IAACsD,QAAA;AAAA,kBACCiB,SAAQ;AAAA,kBACRzD,SAASA,MAAMS,YAAY,KAAK;AAAA,kBAChCzB,WAAU;AAAA,kBACXC,UAAA;AAAA,gBAAA,CAED,CAAA;AAAA,cACF,CAAA,CAAA;AAAA,YACF,CAAA,CAAA;AAAA,UACF,CAAA,CAAA;AAAA,QAEJ,CAAA;AAAA,MACF,CAAA;AAAA,IAAA,CAEJ,GAEAC,kCAAA,IAAC,OAAI;AAAA,MAAAF,WAAU;AAAA,MACbC,UAAAC,kCAAA,IAACsD,QAAA;AAAA,QACCxC,SAASA,MAAMS,YAAY,IAAI;AAAA,QAC/BzB,WAAU;AAAA,QACXC,UAAA;AAAA,MAED,CAAA;AAAA,IACF,CAAA,CAAA;AAAA,EACF,CAAA;AAEJ;"}