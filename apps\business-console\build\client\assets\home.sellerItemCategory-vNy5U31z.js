import { r as reactExports, j as jsxRuntimeExports } from "./jsx-runtime-bB2y7OuD.js";
import { I as Input } from "./input-3v87qohQ.js";
import { T as Table, a as TableHeader, b as TableRow, c as TableHead, d as TableBody, e as TableCell } from "./table-CAxdJZsY.js";
import { u as useLoaderData } from "./components-D7UvGag_.js";
import "./utils-GkgzjW3c.js";
import "./index-Vp2vNLNM.js";
import "./index-DhHTcibu.js";
function SellerItemCategory() {
  var _a;
  const categories = useLoaderData();
  const [searchTerm, setSearchTerm] = reactExports.useState("");
  return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
    className: "container mx-auto p-6",
    children: [/* @__PURE__ */ jsxRuntimeExports.jsx("div", {
      className: "flex justify-between items-center mb-6",
      children: /* @__PURE__ */ jsxRuntimeExports.jsx("h1", {
        className: "text-2xl font-bold",
        children: "Seller Item Category"
      })
    }), /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
      className: "flex justify-between mb-4",
      children: /* @__PURE__ */ jsxRuntimeExports.jsx(Input, {
        placeholder: "Search by Item Name",
        value: searchTerm,
        onChange: (e) => setSearchTerm(e.target.value),
        className: "max-w-sm"
      })
    }), /* @__PURE__ */ jsxRuntimeExports.jsxs(Table, {
      children: [/* @__PURE__ */ jsxRuntimeExports.jsx(TableHeader, {
        children: /* @__PURE__ */ jsxRuntimeExports.jsxs(TableRow, {
          children: [/* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
            className: "cursor-pointer",
            children: "Item CategoryId "
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
            className: "cursor-pointer",
            children: "SICId"
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
            className: "cursor-pointer",
            children: " Image"
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
            className: "cursor-pointer",
            children: " Name "
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
            className: "cursor-pointer",
            children: "picturex "
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
            className: "cursor-pointer",
            children: "picturexx "
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
            className: "cursor-pointer",
            children: "level "
          })]
        })
      }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableBody, {
        children: (_a = categories.data) == null ? void 0 : _a.filter((x) => x.name.toLowerCase().includes(searchTerm.toLowerCase())).sort((a, b) => a.name.localeCompare(b.name)).map((item) => {
          return /* @__PURE__ */ jsxRuntimeExports.jsxs(TableRow, {
            children: [/* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
              children: item.itemCategoryId
            }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
              children: item.sICId
            }), /* @__PURE__ */ jsxRuntimeExports.jsxs(TableCell, {
              children: [" ", /* @__PURE__ */ jsxRuntimeExports.jsx("img", {
                src: item == null ? void 0 : item.picture,
                alt: "ItemImage",
                className: "h-10 w-10"
              })]
            }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
              children: item.name
            }), /* @__PURE__ */ jsxRuntimeExports.jsxs(TableCell, {
              children: [" ", /* @__PURE__ */ jsxRuntimeExports.jsx("img", {
                src: item == null ? void 0 : item.picturex,
                alt: "ItemImage",
                className: "h-10 w-10"
              })]
            }), /* @__PURE__ */ jsxRuntimeExports.jsxs(TableCell, {
              children: [" ", /* @__PURE__ */ jsxRuntimeExports.jsx("img", {
                src: item == null ? void 0 : item.picturexx,
                alt: "ItemImage",
                className: "h-10 w-10"
              })]
            }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
              children: item.level
            })]
          }, item.sICId);
        })
      })]
    })]
  });
}
export {
  SellerItemCategory as default
};
//# sourceMappingURL=home.sellerItemCategory-vNy5U31z.js.map
