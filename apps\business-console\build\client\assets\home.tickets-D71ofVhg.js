import { j as jsxRuntimeExports, r as reactExports } from "./jsx-runtime-bB2y7OuD.js";
import { T as Table, a as TableHeader, b as TableRow, c as TableHead, d as TableBody, e as TableCell } from "./table-CAxdJZsY.js";
import { B as Button } from "./button-ByAXMyvk.js";
import { c as cn$1, t as twMerge, a as clsx } from "./utils-GkgzjW3c.js";
import { B as Badge } from "./badge-BsHDHlRV.js";
import { D as Dialog, a as DialogContent, c as DialogHeader, b as DialogTitle, e as DialogFooter } from "./dialog-BqKosxNq.js";
import { I as Input } from "./input-3v87qohQ.js";
import { a as useFetcher, u as useLoaderData, b as useSearchParams } from "./components-D7UvGag_.js";
import { L as Label } from "./label-cSASrwzW.js";
import { R as RadioGroup, a as RadioGroupItem } from "./radio-group-ChzooXbR.js";
import { u as useToast } from "./use-toast-EUd7m8UG.js";
import { S as Select, a as SelectTrigger, b as SelectValue, c as SelectContent, d as SelectItem } from "./select-BFGSXKcr.js";
import { C as Calendar, a as Calendar$1 } from "./calendar-_8-DqkPN.js";
import { P as Popover, a as PopoverTrigger, b as PopoverContent } from "./popover-CD2vRFIm.js";
import { f as format } from "./format-82yT_5--.js";
import "./index-z_byfFrQ.js";
import "./index-ImHKLo0a.js";
import "./index-DdafHWkt.js";
import "./index-D7VH9Fc8.js";
import "./index-DVTNuYOr.js";
import "./index-dIGjFc0I.js";
import "./index-BXzPK7u0.js";
import "./index-CzZ6EYkN.js";
import "./index-Vp2vNLNM.js";
import "./index-CEHS9zzk.js";
import "./index-QLGF6kQx.js";
import "./x-CCG_WJDF.js";
import "./createLucideIcon-uwkRm45G.js";
import "./index-DhHTcibu.js";
import "./index-CG37gmC0.js";
import "./index-qCtcpOcW.js";
import "./index-BTdCMChR.js";
import "./index-CkL5tk39.js";
import "./index-CpKiYcZd.js";
import "./index-IXOTxK3N.js";
import "./index-DscYByPT.js";
import "./index-C88PRvfd.js";
import "./chevron-down-pCP5jmjX.js";
import "./check-_dbWxIzT.js";
import "./addMonths-Dj4hq91A.js";
import "./isSameDay-BQMn9z7h.js";
import "./addDays-CyH8qBoF.js";
import "./chevron-right-B-tR7Kir.js";
import "./chevron-left-CLqBlTg1.js";
function TicketStatusBadge({ status, className }) {
  const statusStyles = {
    OPEN: "bg-yellow-100 text-yellow-800 hover:bg-yellow-100",
    WIP: "bg-blue-100 text-blue-800 hover:bg-blue-100",
    CLOSED: "bg-green-100 text-green-800 hover:bg-green-100"
  };
  const style = statusStyles[status] || statusStyles.OPEN;
  const displayText = status === "WIP" ? "Work In Progress" : status.charAt(0) + status.slice(1).toLowerCase();
  return /* @__PURE__ */ jsxRuntimeExports.jsx(
    Badge,
    {
      className: cn$1(style, className),
      variant: "outline",
      children: displayText
    }
  );
}
function TicketNotesDialog({
  isOpen,
  onClose,
  ticketId,
  ticketNotes
}) {
  const [note, setNote] = reactExports.useState("");
  const addNoteFetcher = useFetcher();
  const [hasSubmitted, setHasSubmitted] = reactExports.useState(false);
  const isSubmitting = addNoteFetcher.state === "submitting";
  reactExports.useEffect(() => {
    var _a;
    if (hasSubmitted && addNoteFetcher.state === "idle" && ((_a = addNoteFetcher.data) == null ? void 0 : _a.success) === true) {
      setNote("");
      setHasSubmitted(false);
      onClose();
    }
  }, [addNoteFetcher.state, addNoteFetcher.data, hasSubmitted, onClose]);
  const handleSubmit = () => {
    if (!note.trim()) return;
    setHasSubmitted(true);
    addNoteFetcher.submit(
      {
        note,
        ticketId: ticketId.toString(),
        _action: "addNote"
      },
      {
        method: "post",
        action: `/home/<USER>/notes`
      }
    );
  };
  return /* @__PURE__ */ jsxRuntimeExports.jsx(Dialog, { open: isOpen, onOpenChange: onClose, children: /* @__PURE__ */ jsxRuntimeExports.jsxs(DialogContent, { className: "sm:max-w-[500px]", children: [
    /* @__PURE__ */ jsxRuntimeExports.jsx(DialogHeader, { children: /* @__PURE__ */ jsxRuntimeExports.jsx(DialogTitle, { children: "Ticket Notes" }) }),
    /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "max-h-[400px] overflow-y-auto py-4", children: ticketNotes && ticketNotes.length > 0 ? /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "space-y-4", children: ticketNotes.map((noteItem) => /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "border rounded-md p-3 bg-gray-50", children: /* @__PURE__ */ jsxRuntimeExports.jsx("p", { className: "text-sm", children: noteItem.note }) }, noteItem.ticketNoteId)) }) : /* @__PURE__ */ jsxRuntimeExports.jsx("p", { className: "text-center text-gray-500 py-4", children: "No notes available" }) }),
    /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "pt-4 border-t", children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex flex-col space-y-4", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx(
        Input,
        {
          value: note,
          onChange: (e) => setNote(e.target.value),
          placeholder: "Add a note...",
          className: "w-full"
        }
      ),
      /* @__PURE__ */ jsxRuntimeExports.jsx(
        Button,
        {
          onClick: handleSubmit,
          disabled: !note.trim() || isSubmitting,
          loading: isSubmitting,
          className: "self-end",
          children: "Add Note"
        }
      )
    ] }) })
  ] }) });
}
function TicketStatusDialog({
  isOpen,
  onClose,
  ticket
}) {
  const [status, setStatus] = reactExports.useState(ticket.status);
  const updateStatusFetcher = useFetcher();
  const [hasSubmitted, setHasSubmitted] = reactExports.useState(false);
  const isSubmitting = updateStatusFetcher.state === "submitting";
  reactExports.useEffect(() => {
    var _a;
    if (hasSubmitted && updateStatusFetcher.state === "idle" && ((_a = updateStatusFetcher.data) == null ? void 0 : _a.success) === true) {
      setHasSubmitted(false);
      onClose();
    }
  }, [updateStatusFetcher.state, updateStatusFetcher.data, hasSubmitted, onClose]);
  const handleSubmit = () => {
    if (status === ticket.status) {
      onClose();
      return;
    }
    setHasSubmitted(true);
    updateStatusFetcher.submit(
      {
        status,
        ticketId: ticket.ticketId.toString(),
        _action: "updateStatus"
      },
      {
        method: "post",
        action: `/home/<USER>/status`
      }
    );
  };
  return /* @__PURE__ */ jsxRuntimeExports.jsx(Dialog, { open: isOpen, onOpenChange: onClose, children: /* @__PURE__ */ jsxRuntimeExports.jsxs(DialogContent, { className: "sm:max-w-[425px]", children: [
    /* @__PURE__ */ jsxRuntimeExports.jsx(DialogHeader, { children: /* @__PURE__ */ jsxRuntimeExports.jsx(DialogTitle, { children: "Update Ticket Status" }) }),
    /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "py-4", children: /* @__PURE__ */ jsxRuntimeExports.jsxs(RadioGroup, { value: status, onValueChange: (value) => setStatus(value), children: [
      /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center space-x-2", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx(RadioGroupItem, { value: "OPEN", id: "open" }),
        /* @__PURE__ */ jsxRuntimeExports.jsx(Label, { htmlFor: "open", className: "cursor-pointer", children: "Open" })
      ] }),
      /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center space-x-2", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx(RadioGroupItem, { value: "WIP", id: "wip" }),
        /* @__PURE__ */ jsxRuntimeExports.jsx(Label, { htmlFor: "wip", className: "cursor-pointer", children: "Work In Progress" })
      ] }),
      /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center space-x-2", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx(RadioGroupItem, { value: "CLOSED", id: "closed" }),
        /* @__PURE__ */ jsxRuntimeExports.jsx(Label, { htmlFor: "closed", className: "cursor-pointer", children: "Closed" })
      ] })
    ] }) }),
    /* @__PURE__ */ jsxRuntimeExports.jsxs(DialogFooter, { children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx(Button, { variant: "outline", onClick: onClose, children: "Cancel" }),
      /* @__PURE__ */ jsxRuntimeExports.jsx(
        Button,
        {
          type: "submit",
          onClick: handleSubmit,
          loading: isSubmitting,
          disabled: isSubmitting || status === ticket.status,
          children: "Update"
        }
      )
    ] })
  ] }) });
}
function getTimeSinceCreation(date) {
  const now = /* @__PURE__ */ new Date();
  const createdAt = new Date(date);
  const diffMs = now.getTime() - createdAt.getTime();
  const diffDays = Math.floor(diffMs / (1e3 * 60 * 60 * 24));
  const diffHours = Math.floor(diffMs % (1e3 * 60 * 60 * 24) / (1e3 * 60 * 60));
  const diffMinutes = Math.floor(diffMs % (1e3 * 60 * 60) / (1e3 * 60));
  if (diffDays > 0) {
    return `${diffDays} day${diffDays > 1 ? "s" : ""}`;
  } else if (diffHours > 0) {
    return `${diffHours} hour${diffHours > 1 ? "s" : ""}`;
  } else {
    return `${diffMinutes} minute${diffMinutes > 1 ? "s" : ""}`;
  }
}
function cn(...inputs) {
  return twMerge(clsx(inputs));
}
function DatePicker({ date, onSelect, className, id }) {
  const [open, setOpen] = reactExports.useState(false);
  reactExports.useEffect(() => {
    if (!open) {
      return;
    }
  }, [open, date]);
  return /* @__PURE__ */ jsxRuntimeExports.jsxs(Popover, { open, onOpenChange: setOpen, children: [
    /* @__PURE__ */ jsxRuntimeExports.jsx(PopoverTrigger, { asChild: true, children: /* @__PURE__ */ jsxRuntimeExports.jsxs(
      Button,
      {
        variant: "outline",
        className: cn(
          "w-[240px] justify-start text-left font-normal",
          !date && "text-muted-foreground",
          className
        ),
        id,
        children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx(Calendar, { className: "mr-2 h-4 w-4" }),
          date ? format(date, "PPP") : /* @__PURE__ */ jsxRuntimeExports.jsx("span", { children: "Select date" })
        ]
      }
    ) }),
    /* @__PURE__ */ jsxRuntimeExports.jsxs(PopoverContent, { className: "w-auto p-0", align: "start", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx(
        Calendar$1,
        {
          mode: "single",
          selected: date || void 0,
          onSelect: (newDate) => {
            onSelect(newDate || null);
            setOpen(false);
          },
          initialFocus: true
        }
      ),
      date && /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "p-3 border-t border-border", children: /* @__PURE__ */ jsxRuntimeExports.jsx(
        Button,
        {
          variant: "ghost",
          className: "w-full justify-center",
          onClick: () => {
            onSelect(null);
            setOpen(false);
          },
          children: "Clear"
        }
      ) })
    ] })
  ] });
}
function TicketsPage() {
  const {
    tickets,
    totalCount,
    pageNo,
    pageSize
  } = useLoaderData();
  const [searchParams, setSearchParams] = useSearchParams();
  const {
    toast
  } = useToast();
  const [searchQuery, setSearchQuery] = reactExports.useState("");
  const [selectedTicket, setSelectedTicket] = reactExports.useState(null);
  const [statusDialogOpen, setStatusDialogOpen] = reactExports.useState(false);
  const [notesDialogOpen, setNotesDialogOpen] = reactExports.useState(false);
  const [ticketNotes, setTicketNotes] = reactExports.useState([]);
  const [isLoadingNotes, setIsLoadingNotes] = reactExports.useState(false);
  reactExports.useEffect(() => {
    if (!notesDialogOpen) {
      setTicketNotes([]);
    }
  }, [notesDialogOpen]);
  const filteredTickets = tickets.filter((ticket) => {
    var _a, _b;
    const query = searchQuery.toLowerCase();
    return ticket.userName.toLowerCase().includes(query) || ticket.userMobileNo.toLowerCase().includes(query) || ((_a = ticket.sellerName) == null ? void 0 : _a.toLowerCase().includes(query)) || ticket.description.toLowerCase().includes(query) || ticket.userType.toLowerCase().includes(query) || ((_b = ticket.ticketType) == null ? void 0 : _b.toLowerCase().includes(query));
  });
  const handleOpenNotesDialog = async (ticket) => {
    setSelectedTicket(ticket);
    setNotesDialogOpen(true);
    await fetchTicketNotes(ticket.ticketId);
  };
  const fetchTicketNotes = async (ticketId) => {
    setIsLoadingNotes(true);
    try {
      const form = new FormData();
      form.append("_action", "getNotes");
      form.append("ticketId", ticketId.toString());
      const response = await fetch("/home/<USER>", {
        method: "POST",
        body: form
      });
      const data = await response.json();
      if (data.success && data.notes) {
        setTicketNotes(data.notes);
        if (notesDialogOpen) {
          toast({
            title: "Notes Loaded",
            description: "Ticket notes loaded successfully",
            variant: "default"
          });
        }
      } else {
        setTicketNotes([]);
        toast({
          title: "Error",
          description: data.error || "Failed to load notes",
          variant: "destructive"
        });
      }
    } catch (error) {
      console.error("Error fetching notes:", error);
      toast({
        title: "Error",
        description: "Failed to load ticket notes",
        variant: "destructive"
      });
      setTicketNotes([]);
    } finally {
      setIsLoadingNotes(false);
    }
  };
  const handleOpenStatusDialog = (ticket) => {
    setSelectedTicket(ticket);
    setStatusDialogOpen(true);
  };
  const handleStatusFilterChange = (value) => {
    if (value === "ALL") {
      searchParams.delete("status");
    } else {
      searchParams.set("status", value);
    }
    searchParams.set("page", "0");
    setSearchParams(searchParams);
  };
  const handleFromDateChange = (date) => {
    if (date) {
      searchParams.set("fromDate", date.toISOString().split("T")[0]);
    } else {
      searchParams.delete("fromDate");
    }
    searchParams.set("page", "0");
    setSearchParams(searchParams);
  };
  const handleToDateChange = (date) => {
    if (date) {
      searchParams.set("toDate", date.toISOString().split("T")[0]);
    } else {
      searchParams.delete("toDate");
    }
    searchParams.set("page", "0");
    setSearchParams(searchParams);
  };
  const handlePageChange = (newPage) => {
    searchParams.set("page", newPage.toString());
    setSearchParams(searchParams);
  };
  const handlePageSizeChange = (newSize) => {
    searchParams.set("pageSize", newSize);
    searchParams.set("page", "0");
    setSearchParams(searchParams);
  };
  const totalPages = Math.ceil(totalCount / pageSize);
  return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
    className: "container mx-auto p-6",
    children: [/* @__PURE__ */ jsxRuntimeExports.jsx("div", {
      className: "flex justify-between items-center mb-6",
      children: /* @__PURE__ */ jsxRuntimeExports.jsx("h1", {
        className: "text-2xl font-bold",
        children: "Support Tickets"
      })
    }), /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
      className: "flex flex-col space-y-4 mb-6",
      children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
        className: "flex flex-wrap gap-4 items-end",
        children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
          className: "w-full md:w-auto",
          children: [/* @__PURE__ */ jsxRuntimeExports.jsx("label", {
            htmlFor: "status-filter",
            className: "block text-sm font-medium mb-1",
            children: "Status"
          }), /* @__PURE__ */ jsxRuntimeExports.jsxs(Select, {
            defaultValue: searchParams.get("status") || "ALL",
            onValueChange: handleStatusFilterChange,
            children: [/* @__PURE__ */ jsxRuntimeExports.jsx(SelectTrigger, {
              className: "w-[180px]",
              id: "status-filter",
              children: /* @__PURE__ */ jsxRuntimeExports.jsx(SelectValue, {
                placeholder: "Select status"
              })
            }), /* @__PURE__ */ jsxRuntimeExports.jsxs(SelectContent, {
              children: [/* @__PURE__ */ jsxRuntimeExports.jsx(SelectItem, {
                value: "ALL",
                children: "All Statuses"
              }), /* @__PURE__ */ jsxRuntimeExports.jsx(SelectItem, {
                value: "OPEN",
                children: "Open"
              }), /* @__PURE__ */ jsxRuntimeExports.jsx(SelectItem, {
                value: "WIP",
                children: "In Progress"
              }), /* @__PURE__ */ jsxRuntimeExports.jsx(SelectItem, {
                value: "CLOSED",
                children: "Closed"
              })]
            })]
          })]
        }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
          className: "w-full md:w-auto",
          children: [/* @__PURE__ */ jsxRuntimeExports.jsx("label", {
            htmlFor: "from-date",
            className: "block text-sm font-medium mb-1",
            children: "From Date"
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(DatePicker, {
            date: searchParams.get("fromDate") ? new Date(searchParams.get("fromDate")) : null,
            onSelect: handleFromDateChange,
            id: "from-date"
          })]
        }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
          className: "w-full md:w-auto",
          children: [/* @__PURE__ */ jsxRuntimeExports.jsx("label", {
            htmlFor: "to-date",
            className: "block text-sm font-medium mb-1",
            children: "To Date"
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(DatePicker, {
            date: searchParams.get("toDate") ? new Date(searchParams.get("toDate")) : null,
            onSelect: handleToDateChange,
            id: "to-date"
          })]
        })]
      })
    }), /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
      className: "rounded-md border",
      children: /* @__PURE__ */ jsxRuntimeExports.jsxs(Table, {
        children: [/* @__PURE__ */ jsxRuntimeExports.jsx(TableHeader, {
          children: /* @__PURE__ */ jsxRuntimeExports.jsxs(TableRow, {
            children: [/* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
              children: "Ticket ID"
            }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
              children: "User Type"
            }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
              children: "User Name"
            }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
              children: "Phone"
            }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
              children: "Seller"
            }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
              children: "Ticket Type"
            }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
              children: "Description"
            }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
              children: "Last Update"
            }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
              children: "Status"
            }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
              children: "Time Unresolved"
            }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
              children: "Actions"
            })]
          })
        }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableBody, {
          children: filteredTickets.length > 0 ? filteredTickets.map((ticket) => /* @__PURE__ */ jsxRuntimeExports.jsxs(TableRow, {
            children: [/* @__PURE__ */ jsxRuntimeExports.jsxs(TableCell, {
              children: ["#", ticket.ticketId]
            }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
              children: ticket.userType
            }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
              children: ticket.userName
            }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
              children: ticket.userMobileNo
            }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
              children: ticket.sellerName
            }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
              children: ticket.ticketType
            }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
              className: "max-w-[200px] truncate",
              children: ticket.description
            }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
              children: /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
                className: "flex flex-col",
                children: /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                  children: new Date(ticket.lastModifiedDate).toLocaleDateString()
                })
              })
            }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
              children: /* @__PURE__ */ jsxRuntimeExports.jsx(TicketStatusBadge, {
                status: ticket.status
              })
            }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
              children: getTimeSinceCreation(ticket.createdDate)
            }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
              children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                className: "flex gap-2",
                children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
                  size: "sm",
                  variant: "outline",
                  onClick: () => handleOpenNotesDialog(ticket),
                  children: "Notes"
                }), /* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
                  size: "sm",
                  onClick: () => handleOpenStatusDialog(ticket),
                  children: "Update"
                })]
              })
            })]
          }, ticket.ticketId)) : /* @__PURE__ */ jsxRuntimeExports.jsx(TableRow, {
            children: /* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
              colSpan: 11,
              className: "h-24 text-center",
              children: "No tickets found."
            })
          })
        })]
      })
    }), totalPages > 0 && /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
      className: "flex justify-between items-center mt-4",
      children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
        className: "flex items-center gap-2",
        children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("span", {
          className: "text-sm text-gray-600",
          children: ["Showing ", pageNo * pageSize, " to ", Math.min((pageNo + 1) * pageSize, totalCount), " of ", totalCount, " items"]
        }), /* @__PURE__ */ jsxRuntimeExports.jsxs(Select, {
          value: pageSize.toString(),
          onValueChange: handlePageSizeChange,
          children: [/* @__PURE__ */ jsxRuntimeExports.jsx(SelectTrigger, {
            className: "w-[80px]",
            children: /* @__PURE__ */ jsxRuntimeExports.jsx(SelectValue, {})
          }), /* @__PURE__ */ jsxRuntimeExports.jsxs(SelectContent, {
            children: [/* @__PURE__ */ jsxRuntimeExports.jsx(SelectItem, {
              value: "5",
              children: "5"
            }), /* @__PURE__ */ jsxRuntimeExports.jsx(SelectItem, {
              value: "10",
              children: "10"
            }), /* @__PURE__ */ jsxRuntimeExports.jsx(SelectItem, {
              value: "25",
              children: "25"
            }), /* @__PURE__ */ jsxRuntimeExports.jsx(SelectItem, {
              value: "50",
              children: "50"
            })]
          })]
        }), /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
          className: "text-sm text-gray-600",
          children: "per page"
        })]
      }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
        className: "flex gap-1",
        children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
          variant: "outline",
          size: "sm",
          onClick: () => handlePageChange(0),
          disabled: pageNo === 0,
          children: "First"
        }), /* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
          variant: "outline",
          size: "sm",
          onClick: () => handlePageChange(pageNo - 1),
          disabled: pageNo === 0,
          children: "Previous"
        }), /* @__PURE__ */ jsxRuntimeExports.jsxs("span", {
          className: "flex items-center px-3 text-sm",
          children: ["Page ", pageNo + 1, " of ", totalPages + 1]
        }), /* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
          variant: "outline",
          size: "sm",
          onClick: () => handlePageChange(pageNo + 1),
          disabled: pageNo >= totalPages,
          children: "Next"
        }), /* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
          variant: "outline",
          size: "sm",
          onClick: () => handlePageChange(totalPages - 1),
          disabled: pageNo >= totalPages,
          children: "Last"
        })]
      })]
    }), selectedTicket && /* @__PURE__ */ jsxRuntimeExports.jsx(TicketNotesDialog, {
      isOpen: notesDialogOpen,
      onClose: () => setNotesDialogOpen(false),
      ticketId: selectedTicket.ticketId,
      ticketNotes
    }), selectedTicket && /* @__PURE__ */ jsxRuntimeExports.jsx(TicketStatusDialog, {
      isOpen: statusDialogOpen,
      onClose: () => {
        setStatusDialogOpen(false);
      },
      ticket: selectedTicket
    })]
  });
}
export {
  TicketsPage as default
};
//# sourceMappingURL=home.tickets-D71ofVhg.js.map
