import { j as jsxRuntimeExports } from "./jsx-runtime-bB2y7OuD.js";
import { u as useLoaderData } from "./components-D7UvGag_.js";
import "./index-Vp2vNLNM.js";
import "./index-DhHTcibu.js";
function MetaRestaurantReport() {
  const {
    embedUrl,
    orderDate
  } = useLoaderData();
  return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
    className: "container mx-auto p-4",
    children: [/* @__PURE__ */ jsxRuntimeExports.jsx("h1", {
      className: "text-2xl font-bold mb-2",
      children: "Daily Report"
    }), /* @__PURE__ */ jsxRuntimeExports.jsxs("p", {
      className: "text-gray-500 mb-2",
      children: ["Order Date: ", orderDate]
    }), /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
      className: "w-full",
      children: embedUrl ? /* @__PURE__ */ jsxRuntimeExports.jsx("iframe", {
        id: "metabase-iframe",
        src: embedUrl,
        title: "Metabase Dashboard",
        className: "w-full h-screen border-0"
      }) : /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
        className: "p-6 text-center text-red-500",
        children: "Failed to load the dashboard."
      })
    })]
  });
}
export {
  MetaRestaurantReport as default
};
//# sourceMappingURL=open.reports.restaurant-daily-report-S-j8TfGL.js.map
