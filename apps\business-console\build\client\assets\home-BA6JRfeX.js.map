{"version": 3, "file": "home-BA6JRfeX.js", "sources": ["../../../node_modules/lucide-react/dist/esm/icons/chart-network.js", "../../../node_modules/lucide-react/dist/esm/icons/chart-no-axes-combined.js", "../../../node_modules/lucide-react/dist/esm/icons/chart-pie.js", "../../../node_modules/lucide-react/dist/esm/icons/cherry.js", "../../../node_modules/lucide-react/dist/esm/icons/contact.js", "../../../node_modules/lucide-react/dist/esm/icons/earth-lock.js", "../../../node_modules/lucide-react/dist/esm/icons/grape.js", "../../../node_modules/lucide-react/dist/esm/icons/headset.js", "../../../node_modules/lucide-react/dist/esm/icons/house.js", "../../../node_modules/lucide-react/dist/esm/icons/landmark.js", "../../../node_modules/lucide-react/dist/esm/icons/layout-list.js", "../../../node_modules/lucide-react/dist/esm/icons/panel-left.js", "../../../node_modules/lucide-react/dist/esm/icons/plane-takeoff.js", "../../../node_modules/lucide-react/dist/esm/icons/warehouse.js", "../../../app/hooks/use-mobile.tsx", "../../../app/components/ui/skeleton.tsx", "../../../app/components/ui/sidebar.tsx", "../../../node_modules/@radix-ui/react-alert-dialog/dist/index.mjs", "../../../app/components/ui/alert-dialog.tsx", "../../../app/routes/home.tsx"], "sourcesContent": ["/**\n * @license lucide-react v0.453.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst ChartNetwork = createLucideIcon(\"ChartNetwork\", [\n  [\"path\", { d: \"m13.11 7.664 1.78 2.672\", key: \"go2gg9\" }],\n  [\"path\", { d: \"m14.162 12.788-3.324 1.424\", key: \"11x848\" }],\n  [\"path\", { d: \"m20 4-6.06 1.515\", key: \"1wxxh7\" }],\n  [\"path\", { d: \"M3 3v16a2 2 0 0 0 2 2h16\", key: \"c24i48\" }],\n  [\"circle\", { cx: \"12\", cy: \"6\", r: \"2\", key: \"1jj5th\" }],\n  [\"circle\", { cx: \"16\", cy: \"12\", r: \"2\", key: \"4ma0v8\" }],\n  [\"circle\", { cx: \"9\", cy: \"15\", r: \"2\", key: \"lf2ghp\" }]\n]);\n\nexport { ChartNetwork as default };\n//# sourceMappingURL=chart-network.js.map\n", "/**\n * @license lucide-react v0.453.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst ChartNoAxesCombined = createLucideIcon(\"ChartNoAxesCombined\", [\n  [\"path\", { d: \"M12 16v5\", key: \"zza2cw\" }],\n  [\"path\", { d: \"M16 14v7\", key: \"1g90b9\" }],\n  [\"path\", { d: \"M20 10v11\", key: \"1iqoj0\" }],\n  [\n    \"path\",\n    { d: \"m22 3-8.646 8.646a.5.5 0 0 1-.708 0L9.354 8.354a.5.5 0 0 0-.707 0L2 15\", key: \"1fw8x9\" }\n  ],\n  [\"path\", { d: \"M4 18v3\", key: \"1yp0dc\" }],\n  [\"path\", { d: \"M8 14v7\", key: \"n3cwzv\" }]\n]);\n\nexport { ChartNoAxesCombined as default };\n//# sourceMappingURL=chart-no-axes-combined.js.map\n", "/**\n * @license lucide-react v0.453.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst ChartPie = createLucideIcon(\"ChartPie\", [\n  [\n    \"path\",\n    {\n      d: \"M21 12c.552 0 1.005-.449.95-.998a10 10 0 0 0-8.953-8.951c-.55-.055-.998.398-.998.95v8a1 1 0 0 0 1 1z\",\n      key: \"pzmjnu\"\n    }\n  ],\n  [\"path\", { d: \"M21.21 15.89A10 10 0 1 1 8 2.83\", key: \"k2fpak\" }]\n]);\n\nexport { ChartPie as default };\n//# sourceMappingURL=chart-pie.js.map\n", "/**\n * @license lucide-react v0.453.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst Cherry = createLucideIcon(\"Cherry\", [\n  [\"path\", { d: \"M2 17a5 5 0 0 0 10 0c0-2.76-2.5-5-5-3-2.5-2-5 .24-5 3Z\", key: \"cvxqlc\" }],\n  [\"path\", { d: \"M12 17a5 5 0 0 0 10 0c0-2.76-2.5-5-5-3-2.5-2-5 .24-5 3Z\", key: \"1ostrc\" }],\n  [\"path\", { d: \"M7 14c3.22-2.91 4.29-8.75 5-12 1.66 2.38 4.94 9 5 12\", key: \"hqx58h\" }],\n  [\"path\", { d: \"M22 9c-4.29 0-7.14-2.33-10-7 5.71 0 10 4.67 10 7Z\", key: \"eykp1o\" }]\n]);\n\nexport { Cherry as default };\n//# sourceMappingURL=cherry.js.map\n", "/**\n * @license lucide-react v0.453.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst Contact = createLucideIcon(\"Contact\", [\n  [\"path\", { d: \"M16 2v2\", key: \"scm5qe\" }],\n  [\"path\", { d: \"M7 22v-2a2 2 0 0 1 2-2h6a2 2 0 0 1 2 2v2\", key: \"1waht3\" }],\n  [\"path\", { d: \"M8 2v2\", key: \"pbkmx\" }],\n  [\"circle\", { cx: \"12\", cy: \"11\", r: \"3\", key: \"itu57m\" }],\n  [\"rect\", { x: \"3\", y: \"4\", width: \"18\", height: \"18\", rx: \"2\", key: \"12vinp\" }]\n]);\n\nexport { Contact as default };\n//# sourceMappingURL=contact.js.map\n", "/**\n * @license lucide-react v0.453.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst EarthLock = createLucideIcon(\"EarthLock\", [\n  [\"path\", { d: \"M7 3.34V5a3 3 0 0 0 3 3\", key: \"w732o8\" }],\n  [\"path\", { d: \"M11 21.95V18a2 2 0 0 0-2-2 2 2 0 0 1-2-2v-1a2 2 0 0 0-2-2H2.05\", key: \"f02343\" }],\n  [\"path\", { d: \"M21.54 15H17a2 2 0 0 0-2 2v4.54\", key: \"1djwo0\" }],\n  [\"path\", { d: \"M12 2a10 10 0 1 0 9.54 13\", key: \"zjsr6q\" }],\n  [\"path\", { d: \"M20 6V4a2 2 0 1 0-4 0v2\", key: \"1of5e8\" }],\n  [\"rect\", { width: \"8\", height: \"5\", x: \"14\", y: \"6\", rx: \"1\", key: \"1fmf51\" }]\n]);\n\nexport { EarthLock as default };\n//# sourceMappingURL=earth-lock.js.map\n", "/**\n * @license lucide-react v0.453.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst Grape = createLucideIcon(\"Grape\", [\n  [\"path\", { d: \"M22 5V2l-5.89 5.89\", key: \"1eenpo\" }],\n  [\"circle\", { cx: \"16.6\", cy: \"15.89\", r: \"3\", key: \"xjtalx\" }],\n  [\"circle\", { cx: \"8.11\", cy: \"7.4\", r: \"3\", key: \"u2fv6i\" }],\n  [\"circle\", { cx: \"12.35\", cy: \"11.65\", r: \"3\", key: \"i6i8g7\" }],\n  [\"circle\", { cx: \"13.91\", cy: \"5.85\", r: \"3\", key: \"6ye0dv\" }],\n  [\"circle\", { cx: \"18.15\", cy: \"10.09\", r: \"3\", key: \"snx9no\" }],\n  [\"circle\", { cx: \"6.56\", cy: \"13.2\", r: \"3\", key: \"17x4xg\" }],\n  [\"circle\", { cx: \"10.8\", cy: \"17.44\", r: \"3\", key: \"1hogw9\" }],\n  [\"circle\", { cx: \"5\", cy: \"19\", r: \"3\", key: \"1sn6vo\" }]\n]);\n\nexport { Grape as default };\n//# sourceMappingURL=grape.js.map\n", "/**\n * @license lucide-react v0.453.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst Headset = createLucideIcon(\"Headset\", [\n  [\n    \"path\",\n    {\n      d: \"M3 11h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-5Zm0 0a9 9 0 1 1 18 0m0 0v5a2 2 0 0 1-2 2h-1a2 2 0 0 1-2-2v-3a2 2 0 0 1 2-2h3Z\",\n      key: \"12oyoe\"\n    }\n  ],\n  [\"path\", { d: \"M21 16v2a4 4 0 0 1-4 4h-5\", key: \"1x7m43\" }]\n]);\n\nexport { Headset as default };\n//# sourceMappingURL=headset.js.map\n", "/**\n * @license lucide-react v0.453.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst House = createLucideIcon(\"House\", [\n  [\"path\", { d: \"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8\", key: \"5wwlr5\" }],\n  [\n    \"path\",\n    {\n      d: \"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z\",\n      key: \"1d0kgt\"\n    }\n  ]\n]);\n\nexport { House as default };\n//# sourceMappingURL=house.js.map\n", "/**\n * @license lucide-react v0.453.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst Landmark = createLucideIcon(\"Landmark\", [\n  [\"line\", { x1: \"3\", x2: \"21\", y1: \"22\", y2: \"22\", key: \"j8o0r\" }],\n  [\"line\", { x1: \"6\", x2: \"6\", y1: \"18\", y2: \"11\", key: \"10tf0k\" }],\n  [\"line\", { x1: \"10\", x2: \"10\", y1: \"18\", y2: \"11\", key: \"54lgf6\" }],\n  [\"line\", { x1: \"14\", x2: \"14\", y1: \"18\", y2: \"11\", key: \"380y\" }],\n  [\"line\", { x1: \"18\", x2: \"18\", y1: \"18\", y2: \"11\", key: \"1kevvc\" }],\n  [\"polygon\", { points: \"12 2 20 7 4 7\", key: \"jkujk7\" }]\n]);\n\nexport { Landmark as default };\n//# sourceMappingURL=landmark.js.map\n", "/**\n * @license lucide-react v0.453.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst LayoutList = createLucideIcon(\"LayoutList\", [\n  [\"rect\", { width: \"7\", height: \"7\", x: \"3\", y: \"3\", rx: \"1\", key: \"1g98yp\" }],\n  [\"rect\", { width: \"7\", height: \"7\", x: \"3\", y: \"14\", rx: \"1\", key: \"1bb6yr\" }],\n  [\"path\", { d: \"M14 4h7\", key: \"3xa0d5\" }],\n  [\"path\", { d: \"M14 9h7\", key: \"1icrd9\" }],\n  [\"path\", { d: \"M14 15h7\", key: \"1mj8o2\" }],\n  [\"path\", { d: \"M14 20h7\", key: \"11slyb\" }]\n]);\n\nexport { LayoutList as default };\n//# sourceMappingURL=layout-list.js.map\n", "/**\n * @license lucide-react v0.453.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst PanelLeft = createLucideIcon(\"PanelLeft\", [\n  [\"rect\", { width: \"18\", height: \"18\", x: \"3\", y: \"3\", rx: \"2\", key: \"afitv7\" }],\n  [\"path\", { d: \"M9 3v18\", key: \"fh3hqa\" }]\n]);\n\nexport { PanelLeft as default };\n//# sourceMappingURL=panel-left.js.map\n", "/**\n * @license lucide-react v0.453.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst PlaneTakeoff = createLucideIcon(\"PlaneTakeoff\", [\n  [\"path\", { d: \"M2 22h20\", key: \"272qi7\" }],\n  [\n    \"path\",\n    {\n      d: \"M6.36 17.4 4 17l-2-4 1.1-.55a2 2 0 0 1 1.8 0l.17.1a2 2 0 0 0 1.8 0L8 12 5 6l.9-.45a2 2 0 0 1 2.09.2l4.02 3a2 2 0 0 0 2.1.2l4.19-2.06a2.41 2.41 0 0 1 1.73-.17L21 7a1.4 1.4 0 0 1 .87 1.99l-.38.76c-.23.46-.6.84-1.07 1.08L7.58 17.2a2 2 0 0 1-1.22.18Z\",\n      key: \"fkigj9\"\n    }\n  ]\n]);\n\nexport { PlaneTakeoff as default };\n//# sourceMappingURL=plane-takeoff.js.map\n", "/**\n * @license lucide-react v0.453.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst Warehouse = createLucideIcon(\"Warehouse\", [\n  [\n    \"path\",\n    {\n      d: \"M22 8.35V20a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V8.35A2 2 0 0 1 3.26 6.5l8-3.2a2 2 0 0 1 1.48 0l8 3.2A2 2 0 0 1 22 8.35Z\",\n      key: \"gksnxg\"\n    }\n  ],\n  [\"path\", { d: \"M6 18h12\", key: \"9pbo8z\" }],\n  [\"path\", { d: \"M6 14h12\", key: \"4cwo0f\" }],\n  [\"rect\", { width: \"12\", height: \"12\", x: \"6\", y: \"10\", key: \"apd30q\" }]\n]);\n\nexport { Warehouse as default };\n//# sourceMappingURL=warehouse.js.map\n", "import * as React from \"react\"\r\n\r\nconst MO<PERSON>LE_BREAKPOINT = 768\r\n\r\nexport function useIsMobile() {\r\n  const [isMobile, setIsMobile] = React.useState<boolean | undefined>(undefined)\r\n\r\n  React.useEffect(() => {\r\n    const mql = window.matchMedia(`(max-width: ${MOBILE_BREAKPOINT - 1}px)`)\r\n    const onChange = () => {\r\n      setIsMobile(window.innerWidth < MOBILE_BREAKPOINT)\r\n    }\r\n    mql.addEventListener(\"change\", onChange)\r\n    setIsMobile(window.innerWidth < MOBILE_BREAKPOINT)\r\n    return () => mql.removeEventListener(\"change\", onChange)\r\n  }, [])\r\n\r\n  return !!isMobile\r\n}\r\n", "import { cn } from \"~/lib/utils\"\r\n\r\nfunction Skeleton({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLDivElement>) {\r\n  return (\r\n    <div\r\n      className={cn(\"animate-pulse rounded-md bg-muted\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Skeleton }\r\n", "import * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { VariantProps, cva } from \"class-variance-authority\"\r\nimport { PanelLeft } from \"lucide-react\"\r\n\r\nimport { useIsMobile } from \"~/hooks/use-mobile\"\r\nimport { cn } from \"~/lib/utils\"\r\nimport { Button } from \"~/components/ui/button\"\r\nimport { Input } from \"~/components/ui/input\"\r\nimport { Separator } from \"~/components/ui/separator\"\r\nimport { She<PERSON>, Sheet<PERSON>onte<PERSON> } from \"~/components/ui/sheet\"\r\nimport { Skeleton } from \"~/components/ui/skeleton\"\r\nimport {\r\n  Tooltip,\r\n  TooltipContent,\r\n  TooltipProvider,\r\n  TooltipTrigger,\r\n} from \"~/components/ui/tooltip\"\r\n\r\nconst SIDEBAR_COOKIE_NAME = \"sidebar:state\"\r\nconst SIDEBAR_COOKIE_MAX_AGE = 60 * 60 * 24 * 7\r\nconst SIDEBAR_WIDTH = \"16rem\"\r\nconst SIDEBAR_WIDTH_MOBILE = \"18rem\"\r\nconst SIDEBAR_WIDTH_ICON = \"3rem\"\r\nconst SIDEBAR_KEYBOARD_SHORTCUT = \"b\"\r\n\r\ntype SidebarContext = {\r\n  state: \"expanded\" | \"collapsed\"\r\n  open: boolean\r\n  setOpen: (open: boolean) => void\r\n  openMobile: boolean\r\n  setOpenMobile: (open: boolean) => void\r\n  isMobile: boolean\r\n  toggleSidebar: () => void\r\n}\r\n\r\nconst SidebarContext = React.createContext<SidebarContext | null>(null)\r\n\r\nfunction useSidebar() {\r\n  const context = React.useContext(SidebarContext)\r\n  if (!context) {\r\n    throw new Error(\"useSidebar must be used within a SidebarProvider.\")\r\n  }\r\n\r\n  return context\r\n}\r\n\r\nconst SidebarProvider = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.ComponentProps<\"div\"> & {\r\n    defaultOpen?: boolean\r\n    open?: boolean\r\n    onOpenChange?: (open: boolean) => void\r\n  }\r\n>(\r\n  (\r\n    {\r\n      defaultOpen = true,\r\n      open: openProp,\r\n      onOpenChange: setOpenProp,\r\n      className,\r\n      style,\r\n      children,\r\n      ...props\r\n    },\r\n    ref\r\n  ) => {\r\n    const isMobile = useIsMobile()\r\n    const [openMobile, setOpenMobile] = React.useState(false)\r\n\r\n    // This is the internal state of the sidebar.\r\n    // We use openProp and setOpenProp for control from outside the component.\r\n    const [_open, _setOpen] = React.useState(defaultOpen)\r\n    const open = openProp ?? _open\r\n    const setOpen = React.useCallback(\r\n      (value: boolean | ((value: boolean) => boolean)) => {\r\n        if (setOpenProp) {\r\n          return setOpenProp?.(\r\n            typeof value === \"function\" ? value(open) : value\r\n          )\r\n        }\r\n\r\n        _setOpen(value)\r\n\r\n        // This sets the cookie to keep the sidebar state.\r\n        document.cookie = `${SIDEBAR_COOKIE_NAME}=${open}; path=/; max-age=${SIDEBAR_COOKIE_MAX_AGE}`\r\n      },\r\n      [setOpenProp, open]\r\n    )\r\n\r\n    // Helper to toggle the sidebar.\r\n    const toggleSidebar = React.useCallback(() => {\r\n      return isMobile\r\n        ? setOpenMobile((open) => !open)\r\n        : setOpen((open) => !open)\r\n    }, [isMobile, setOpen, setOpenMobile])\r\n\r\n    // Adds a keyboard shortcut to toggle the sidebar.\r\n    React.useEffect(() => {\r\n      const handleKeyDown = (event: KeyboardEvent) => {\r\n        if (\r\n          event.key === SIDEBAR_KEYBOARD_SHORTCUT &&\r\n          (event.metaKey || event.ctrlKey)\r\n        ) {\r\n          event.preventDefault()\r\n          toggleSidebar()\r\n        }\r\n      }\r\n\r\n      window.addEventListener(\"keydown\", handleKeyDown)\r\n      return () => window.removeEventListener(\"keydown\", handleKeyDown)\r\n    }, [toggleSidebar])\r\n\r\n    // We add a state so that we can do data-state=\"expanded\" or \"collapsed\".\r\n    // This makes it easier to style the sidebar with Tailwind classes.\r\n    const state = open ? \"expanded\" : \"collapsed\"\r\n\r\n    const contextValue = React.useMemo<SidebarContext>(\r\n      () => ({\r\n        state,\r\n        open,\r\n        setOpen,\r\n        isMobile,\r\n        openMobile,\r\n        setOpenMobile,\r\n        toggleSidebar,\r\n      }),\r\n      [state, open, setOpen, isMobile, openMobile, setOpenMobile, toggleSidebar]\r\n    )\r\n\r\n    return (\r\n      <SidebarContext.Provider value={contextValue}>\r\n        <TooltipProvider delayDuration={0}>\r\n          <div\r\n            style={\r\n              {\r\n                \"--sidebar-width\": SIDEBAR_WIDTH,\r\n                \"--sidebar-width-icon\": SIDEBAR_WIDTH_ICON,\r\n                ...style,\r\n              } as React.CSSProperties\r\n            }\r\n            className={cn(\r\n              \"group/sidebar-wrapper flex min-h-svh w-full has-[[data-variant=inset]]:bg-sidebar\",\r\n              className\r\n            )}\r\n            ref={ref}\r\n            {...props}\r\n          >\r\n            {children}\r\n          </div>\r\n        </TooltipProvider>\r\n      </SidebarContext.Provider>\r\n    )\r\n  }\r\n)\r\nSidebarProvider.displayName = \"SidebarProvider\"\r\n\r\nconst Sidebar = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.ComponentProps<\"div\"> & {\r\n    side?: \"left\" | \"right\"\r\n    variant?: \"sidebar\" | \"floating\" | \"inset\"\r\n    collapsible?: \"offcanvas\" | \"icon\" | \"none\"\r\n  }\r\n>(\r\n  (\r\n    {\r\n      side = \"left\",\r\n      variant = \"sidebar\",\r\n      collapsible = \"offcanvas\",\r\n      className,\r\n      children,\r\n      ...props\r\n    },\r\n    ref\r\n  ) => {\r\n    const { isMobile, state, openMobile, setOpenMobile } = useSidebar()\r\n\r\n    if (collapsible === \"none\") {\r\n      return (\r\n        <div\r\n          className={cn(\r\n            \"flex h-full w-[--sidebar-width] flex-col bg-sidebar text-sidebar-foreground\",\r\n            className\r\n          )}\r\n          ref={ref}\r\n          {...props}\r\n        >\r\n          {children}\r\n        </div>\r\n      )\r\n    }\r\n\r\n    if (isMobile) {\r\n      return (\r\n        <Sheet open={openMobile} onOpenChange={setOpenMobile} {...props}>\r\n          <SheetContent\r\n            data-sidebar=\"sidebar\"\r\n            data-mobile=\"true\"\r\n            className=\"w-[--sidebar-width] bg-sidebar p-0 text-sidebar-foreground [&>button]:hidden\"\r\n            style={\r\n              {\r\n                \"--sidebar-width\": SIDEBAR_WIDTH_MOBILE,\r\n              } as React.CSSProperties\r\n            }\r\n            side={side}\r\n          >\r\n            <div className=\"flex h-full w-full flex-col\">{children}</div>\r\n          </SheetContent>\r\n        </Sheet>\r\n      )\r\n    }\r\n\r\n    return (\r\n      <div\r\n        ref={ref}\r\n        className=\"group peer hidden md:block text-sidebar-foreground\"\r\n        data-state={state}\r\n        data-collapsible={state === \"collapsed\" ? collapsible : \"\"}\r\n        data-variant={variant}\r\n        data-side={side}\r\n      >\r\n        {/* This is what handles the sidebar gap on desktop */}\r\n        <div\r\n          className={cn(\r\n            \"duration-200 relative h-svh w-[--sidebar-width] bg-transparent transition-[width] ease-linear\",\r\n            \"group-data-[collapsible=offcanvas]:w-0\",\r\n            \"group-data-[side=right]:rotate-180\",\r\n            variant === \"floating\" || variant === \"inset\"\r\n              ? \"group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)_+_theme(spacing.4))]\"\r\n              : \"group-data-[collapsible=icon]:w-[--sidebar-width-icon]\"\r\n          )}\r\n        />\r\n        <div\r\n          className={cn(\r\n            \"duration-200 fixed inset-y-0 z-10 hidden h-svh w-[--sidebar-width] transition-[left,right,width] ease-linear md:flex\",\r\n            side === \"left\"\r\n              ? \"left-0 group-data-[collapsible=offcanvas]:left-[calc(var(--sidebar-width)*-1)]\"\r\n              : \"right-0 group-data-[collapsible=offcanvas]:right-[calc(var(--sidebar-width)*-1)]\",\r\n            // Adjust the padding for floating and inset variants.\r\n            variant === \"floating\" || variant === \"inset\"\r\n              ? \"p-2 group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)_+_theme(spacing.4)_+2px)]\"\r\n              : \"group-data-[collapsible=icon]:w-[--sidebar-width-icon] group-data-[side=left]:border-r group-data-[side=right]:border-l\",\r\n            className\r\n          )}\r\n          {...props}\r\n        >\r\n          <div\r\n            data-sidebar=\"sidebar\"\r\n            className=\"flex h-full w-full flex-col bg-sidebar group-data-[variant=floating]:rounded-lg group-data-[variant=floating]:border group-data-[variant=floating]:border-sidebar-border group-data-[variant=floating]:shadow\"\r\n          >\r\n            {children}\r\n          </div>\r\n        </div>\r\n      </div>\r\n    )\r\n  }\r\n)\r\nSidebar.displayName = \"Sidebar\"\r\n\r\nconst SidebarTrigger = React.forwardRef<\r\n  React.ElementRef<typeof Button>,\r\n  React.ComponentProps<typeof Button>\r\n>(({ className, onClick, ...props }, ref) => {\r\n  const { toggleSidebar } = useSidebar()\r\n\r\n  return (\r\n    <Button\r\n      ref={ref}\r\n      data-sidebar=\"trigger\"\r\n      variant=\"ghost\"\r\n      size=\"icon\"\r\n      className={cn(\"h-7 w-7\", className)}\r\n      onClick={(event) => {\r\n        onClick?.(event)\r\n        toggleSidebar()\r\n      }}\r\n      {...props}\r\n    >\r\n      <PanelLeft />\r\n      <span className=\"sr-only\">Toggle Sidebar</span>\r\n    </Button>\r\n  )\r\n})\r\nSidebarTrigger.displayName = \"SidebarTrigger\"\r\n\r\nconst SidebarRail = React.forwardRef<\r\n  HTMLButtonElement,\r\n  React.ComponentProps<\"button\">\r\n>(({ className, ...props }, ref) => {\r\n  const { toggleSidebar } = useSidebar()\r\n\r\n  return (\r\n    <button\r\n      ref={ref}\r\n      data-sidebar=\"rail\"\r\n      aria-label=\"Toggle Sidebar\"\r\n      tabIndex={-1}\r\n      onClick={toggleSidebar}\r\n      title=\"Toggle Sidebar\"\r\n      className={cn(\r\n        \"absolute inset-y-0 z-20 hidden w-4 -translate-x-1/2 transition-all ease-linear after:absolute after:inset-y-0 after:left-1/2 after:w-[2px] hover:after:bg-sidebar-border group-data-[side=left]:-right-4 group-data-[side=right]:left-0 sm:flex\",\r\n        \"[[data-side=left]_&]:cursor-w-resize [[data-side=right]_&]:cursor-e-resize\",\r\n        \"[[data-side=left][data-state=collapsed]_&]:cursor-e-resize [[data-side=right][data-state=collapsed]_&]:cursor-w-resize\",\r\n        \"group-data-[collapsible=offcanvas]:translate-x-0 group-data-[collapsible=offcanvas]:after:left-full group-data-[collapsible=offcanvas]:hover:bg-sidebar\",\r\n        \"[[data-side=left][data-collapsible=offcanvas]_&]:-right-2\",\r\n        \"[[data-side=right][data-collapsible=offcanvas]_&]:-left-2\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n})\r\nSidebarRail.displayName = \"SidebarRail\"\r\n\r\nconst SidebarInset = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.ComponentProps<\"main\">\r\n>(({ className, ...props }, ref) => {\r\n  return (\r\n    <main\r\n      ref={ref}\r\n      className={cn(\r\n        \"relative flex min-h-svh flex-1 flex-col bg-background\",\r\n        \"peer-data-[variant=inset]:min-h-[calc(100svh-theme(spacing.4))] md:peer-data-[variant=inset]:m-2 md:peer-data-[state=collapsed]:peer-data-[variant=inset]:ml-2 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n})\r\nSidebarInset.displayName = \"SidebarInset\"\r\n\r\nconst SidebarInput = React.forwardRef<\r\n  React.ElementRef<typeof Input>,\r\n  React.ComponentProps<typeof Input>\r\n>(({ className, ...props }, ref) => {\r\n  return (\r\n    <Input\r\n      ref={ref}\r\n      data-sidebar=\"input\"\r\n      className={cn(\r\n        \"h-8 w-full bg-background shadow-none focus-visible:ring-2 focus-visible:ring-sidebar-ring\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n})\r\nSidebarInput.displayName = \"SidebarInput\"\r\n\r\nconst SidebarHeader = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.ComponentProps<\"div\">\r\n>(({ className, ...props }, ref) => {\r\n  return (\r\n    <div\r\n      ref={ref}\r\n      data-sidebar=\"header\"\r\n      className={cn(\"flex flex-col gap-2 p-2\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n})\r\nSidebarHeader.displayName = \"SidebarHeader\"\r\n\r\nconst SidebarFooter = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.ComponentProps<\"div\">\r\n>(({ className, ...props }, ref) => {\r\n  return (\r\n    <div\r\n      ref={ref}\r\n      data-sidebar=\"footer\"\r\n      className={cn(\"flex flex-col gap-2 p-2\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n})\r\nSidebarFooter.displayName = \"SidebarFooter\"\r\n\r\nconst SidebarSeparator = React.forwardRef<\r\n  React.ElementRef<typeof Separator>,\r\n  React.ComponentProps<typeof Separator>\r\n>(({ className, ...props }, ref) => {\r\n  return (\r\n    <Separator\r\n      ref={ref}\r\n      data-sidebar=\"separator\"\r\n      className={cn(\"mx-2 w-auto bg-sidebar-border\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n})\r\nSidebarSeparator.displayName = \"SidebarSeparator\"\r\n\r\nconst SidebarContent = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.ComponentProps<\"div\">\r\n>(({ className, ...props }, ref) => {\r\n  return (\r\n    <div\r\n      ref={ref}\r\n      data-sidebar=\"content\"\r\n      className={cn(\r\n        \"flex min-h-0 flex-1 flex-col gap-2 overflow-auto group-data-[collapsible=icon]:overflow-hidden\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n})\r\nSidebarContent.displayName = \"SidebarContent\"\r\n\r\nconst SidebarGroup = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.ComponentProps<\"div\">\r\n>(({ className, ...props }, ref) => {\r\n  return (\r\n    <div\r\n      ref={ref}\r\n      data-sidebar=\"group\"\r\n      className={cn(\"relative flex w-full min-w-0 flex-col p-2\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n})\r\nSidebarGroup.displayName = \"SidebarGroup\"\r\n\r\nconst SidebarGroupLabel = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.ComponentProps<\"div\"> & { asChild?: boolean }\r\n>(({ className, asChild = false, ...props }, ref) => {\r\n  const Comp = asChild ? Slot : \"div\"\r\n\r\n  return (\r\n    <Comp\r\n      ref={ref}\r\n      data-sidebar=\"group-label\"\r\n      className={cn(\r\n        \"duration-200 flex h-8 shrink-0 items-center rounded-md px-2 text-xs font-medium text-sidebar-foreground/70 outline-none ring-sidebar-ring transition-[margin,opa] ease-linear focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0\",\r\n        \"group-data-[collapsible=icon]:-mt-8 group-data-[collapsible=icon]:opacity-0\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n})\r\nSidebarGroupLabel.displayName = \"SidebarGroupLabel\"\r\n\r\nconst SidebarGroupAction = React.forwardRef<\r\n  HTMLButtonElement,\r\n  React.ComponentProps<\"button\"> & { asChild?: boolean }\r\n>(({ className, asChild = false, ...props }, ref) => {\r\n  const Comp = asChild ? Slot : \"button\"\r\n\r\n  return (\r\n    <Comp\r\n      ref={ref}\r\n      data-sidebar=\"group-action\"\r\n      className={cn(\r\n        \"absolute right-3 top-3.5 flex aspect-square w-5 items-center justify-center rounded-md p-0 text-sidebar-foreground outline-none ring-sidebar-ring transition-transform hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0\",\r\n        // Increases the hit area of the button on mobile.\r\n        \"after:absolute after:-inset-2 after:md:hidden\",\r\n        \"group-data-[collapsible=icon]:hidden\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n})\r\nSidebarGroupAction.displayName = \"SidebarGroupAction\"\r\n\r\nconst SidebarGroupContent = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.ComponentProps<\"div\">\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    data-sidebar=\"group-content\"\r\n    className={cn(\"w-full text-sm\", className)}\r\n    {...props}\r\n  />\r\n))\r\nSidebarGroupContent.displayName = \"SidebarGroupContent\"\r\n\r\nconst SidebarMenu = React.forwardRef<\r\n  HTMLUListElement,\r\n  React.ComponentProps<\"ul\">\r\n>(({ className, ...props }, ref) => (\r\n  <ul\r\n    ref={ref}\r\n    data-sidebar=\"menu\"\r\n    className={cn(\"flex w-full min-w-0 flex-col gap-1\", className)}\r\n    {...props}\r\n  />\r\n))\r\nSidebarMenu.displayName = \"SidebarMenu\"\r\n\r\nconst SidebarMenuItem = React.forwardRef<\r\n  HTMLLIElement,\r\n  React.ComponentProps<\"li\">\r\n>(({ className, ...props }, ref) => (\r\n  <li\r\n    ref={ref}\r\n    data-sidebar=\"menu-item\"\r\n    className={cn(\"group/menu-item relative\", className)}\r\n    {...props}\r\n  />\r\n))\r\nSidebarMenuItem.displayName = \"SidebarMenuItem\"\r\n\r\nconst sidebarMenuButtonVariants = cva(\r\n  \"peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left text-sm outline-none ring-sidebar-ring transition-[width,height,padding] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-[[data-sidebar=menu-action]]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[active=true]:bg-sidebar-accent data-[active=true]:font-medium data-[active=true]:text-sidebar-accent-foreground data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground group-data-[collapsible=icon]:!size-8 group-data-[collapsible=icon]:!p-2 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: \"hover:bg-sidebar-accent hover:text-sidebar-accent-foreground\",\r\n        outline:\r\n          \"bg-background shadow-[0_0_0_1px_hsl(var(--sidebar-border))] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:shadow-[0_0_0_1px_hsl(var(--sidebar-accent))]\",\r\n      },\r\n      size: {\r\n        default: \"h-8 text-sm\",\r\n        sm: \"h-7 text-xs\",\r\n        lg: \"h-12 text-sm group-data-[collapsible=icon]:!p-0\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nconst SidebarMenuButton = React.forwardRef<\r\n  HTMLButtonElement,\r\n  React.ComponentProps<\"button\"> & {\r\n    asChild?: boolean\r\n    isActive?: boolean\r\n    tooltip?: string | React.ComponentProps<typeof TooltipContent>\r\n  } & VariantProps<typeof sidebarMenuButtonVariants>\r\n>(\r\n  (\r\n    {\r\n      asChild = false,\r\n      isActive = false,\r\n      variant = \"default\",\r\n      size = \"default\",\r\n      tooltip,\r\n      className,\r\n      ...props\r\n    },\r\n    ref\r\n  ) => {\r\n    const Comp = asChild ? Slot : \"button\"\r\n    const { isMobile, state } = useSidebar()\r\n\r\n    const button = (\r\n      <Comp\r\n        ref={ref}\r\n        data-sidebar=\"menu-button\"\r\n        data-size={size}\r\n        data-active={isActive}\r\n        className={cn(sidebarMenuButtonVariants({ variant, size }), className)}\r\n        {...props}\r\n      />\r\n    )\r\n\r\n    if (!tooltip) {\r\n      return button\r\n    }\r\n\r\n    if (typeof tooltip === \"string\") {\r\n      tooltip = {\r\n        children: tooltip,\r\n      }\r\n    }\r\n\r\n    return (\r\n      <Tooltip>\r\n        <TooltipTrigger asChild>{button}</TooltipTrigger>\r\n        <TooltipContent\r\n          side=\"right\"\r\n          align=\"center\"\r\n          hidden={state !== \"collapsed\" || isMobile}\r\n          {...tooltip}\r\n        />\r\n      </Tooltip>\r\n    )\r\n  }\r\n)\r\nSidebarMenuButton.displayName = \"SidebarMenuButton\"\r\n\r\nconst SidebarMenuAction = React.forwardRef<\r\n  HTMLButtonElement,\r\n  React.ComponentProps<\"button\"> & {\r\n    asChild?: boolean\r\n    showOnHover?: boolean\r\n  }\r\n>(({ className, asChild = false, showOnHover = false, ...props }, ref) => {\r\n  const Comp = asChild ? Slot : \"button\"\r\n\r\n  return (\r\n    <Comp\r\n      ref={ref}\r\n      data-sidebar=\"menu-action\"\r\n      className={cn(\r\n        \"absolute right-1 top-1.5 flex aspect-square w-5 items-center justify-center rounded-md p-0 text-sidebar-foreground outline-none ring-sidebar-ring transition-transform hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 peer-hover/menu-button:text-sidebar-accent-foreground [&>svg]:size-4 [&>svg]:shrink-0\",\r\n        // Increases the hit area of the button on mobile.\r\n        \"after:absolute after:-inset-2 after:md:hidden\",\r\n        \"peer-data-[size=sm]/menu-button:top-1\",\r\n        \"peer-data-[size=default]/menu-button:top-1.5\",\r\n        \"peer-data-[size=lg]/menu-button:top-2.5\",\r\n        \"group-data-[collapsible=icon]:hidden\",\r\n        showOnHover &&\r\n        \"group-focus-within/menu-item:opacity-100 group-hover/menu-item:opacity-100 data-[state=open]:opacity-100 peer-data-[active=true]/menu-button:text-sidebar-accent-foreground md:opacity-0\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n})\r\nSidebarMenuAction.displayName = \"SidebarMenuAction\"\r\n\r\nconst SidebarMenuBadge = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.ComponentProps<\"div\">\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    data-sidebar=\"menu-badge\"\r\n    className={cn(\r\n      \"absolute right-1 flex h-5 min-w-5 items-center justify-center rounded-md px-1 text-xs font-medium tabular-nums text-sidebar-foreground select-none pointer-events-none\",\r\n      \"peer-hover/menu-button:text-sidebar-accent-foreground peer-data-[active=true]/menu-button:text-sidebar-accent-foreground\",\r\n      \"peer-data-[size=sm]/menu-button:top-1\",\r\n      \"peer-data-[size=default]/menu-button:top-1.5\",\r\n      \"peer-data-[size=lg]/menu-button:top-2.5\",\r\n      \"group-data-[collapsible=icon]:hidden\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nSidebarMenuBadge.displayName = \"SidebarMenuBadge\"\r\n\r\nconst SidebarMenuSkeleton = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.ComponentProps<\"div\"> & {\r\n    showIcon?: boolean\r\n  }\r\n>(({ className, showIcon = false, ...props }, ref) => {\r\n  // Random width between 50 to 90%.\r\n  const width = React.useMemo(() => {\r\n    return `${Math.floor(Math.random() * 40) + 50}%`\r\n  }, [])\r\n\r\n  return (\r\n    <div\r\n      ref={ref}\r\n      data-sidebar=\"menu-skeleton\"\r\n      className={cn(\"rounded-md h-8 flex gap-2 px-2 items-center\", className)}\r\n      {...props}\r\n    >\r\n      {showIcon && (\r\n        <Skeleton\r\n          className=\"size-4 rounded-md\"\r\n          data-sidebar=\"menu-skeleton-icon\"\r\n        />\r\n      )}\r\n      <Skeleton\r\n        className=\"h-4 flex-1 max-w-[--skeleton-width]\"\r\n        data-sidebar=\"menu-skeleton-text\"\r\n        style={\r\n          {\r\n            \"--skeleton-width\": width,\r\n          } as React.CSSProperties\r\n        }\r\n      />\r\n    </div>\r\n  )\r\n})\r\nSidebarMenuSkeleton.displayName = \"SidebarMenuSkeleton\"\r\n\r\nconst SidebarMenuSub = React.forwardRef<\r\n  HTMLUListElement,\r\n  React.ComponentProps<\"ul\">\r\n>(({ className, ...props }, ref) => (\r\n  <ul\r\n    ref={ref}\r\n    data-sidebar=\"menu-sub\"\r\n    className={cn(\r\n      \"mx-3.5 flex min-w-0 translate-x-px flex-col gap-1 border-l border-sidebar-border px-2.5 py-0.5\",\r\n      \"group-data-[collapsible=icon]:hidden\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nSidebarMenuSub.displayName = \"SidebarMenuSub\"\r\n\r\nconst SidebarMenuSubItem = React.forwardRef<\r\n  HTMLLIElement,\r\n  React.ComponentProps<\"li\">\r\n>(({ ...props }, ref) => <li ref={ref} {...props} />)\r\nSidebarMenuSubItem.displayName = \"SidebarMenuSubItem\"\r\n\r\nconst SidebarMenuSubButton = React.forwardRef<\r\n  HTMLAnchorElement,\r\n  React.ComponentProps<\"a\"> & {\r\n    asChild?: boolean\r\n    size?: \"sm\" | \"md\"\r\n    isActive?: boolean\r\n  }\r\n>(({ asChild = false, size = \"md\", isActive, className, ...props }, ref) => {\r\n  const Comp = asChild ? Slot : \"a\"\r\n\r\n  return (\r\n    <Comp\r\n      ref={ref}\r\n      data-sidebar=\"menu-sub-button\"\r\n      data-size={size}\r\n      data-active={isActive}\r\n      className={cn(\r\n        \"flex h-7 min-w-0 -translate-x-px items-center gap-2 overflow-hidden rounded-md px-2 text-sidebar-foreground outline-none ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 aria-disabled:pointer-events-none aria-disabled:opacity-50 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0 [&>svg]:text-sidebar-accent-foreground\",\r\n        \"data-[active=true]:bg-sidebar-accent data-[active=true]:text-sidebar-accent-foreground\",\r\n        size === \"sm\" && \"text-xs\",\r\n        size === \"md\" && \"text-sm\",\r\n        \"group-data-[collapsible=icon]:hidden\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n})\r\nSidebarMenuSubButton.displayName = \"SidebarMenuSubButton\"\r\n\r\nexport {\r\n  Sidebar,\r\n  SidebarContent,\r\n  SidebarFooter,\r\n  SidebarGroup,\r\n  SidebarGroupAction,\r\n  SidebarGroupContent,\r\n  SidebarGroupLabel,\r\n  SidebarHeader,\r\n  SidebarInput,\r\n  SidebarInset,\r\n  SidebarMenu,\r\n  SidebarMenuAction,\r\n  SidebarMenuBadge,\r\n  SidebarMenuButton,\r\n  SidebarMenuItem,\r\n  SidebarMenuSkeleton,\r\n  SidebarMenuSub,\r\n  SidebarMenuSubButton,\r\n  SidebarMenuSubItem,\r\n  SidebarProvider,\r\n  SidebarRail,\r\n  SidebarSeparator,\r\n  SidebarTrigger,\r\n  useSidebar,\r\n}\r\n", "\"use client\";\n\n// packages/react/alert-dialog/src/alert-dialog.tsx\nimport * as React from \"react\";\nimport { createContextScope } from \"@radix-ui/react-context\";\nimport { useComposedRefs } from \"@radix-ui/react-compose-refs\";\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\";\nimport { createDialogScope } from \"@radix-ui/react-dialog\";\nimport { composeEventHandlers } from \"@radix-ui/primitive\";\nimport { Slottable } from \"@radix-ui/react-slot\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar ROOT_NAME = \"AlertDialog\";\nvar [createAlertDialogContext, createAlertDialogScope] = createContextScope(ROOT_NAME, [\n  createDialogScope\n]);\nvar useDialogScope = createDialogScope();\nvar AlertDialog = (props) => {\n  const { __scopeAlertDialog, ...alertDialogProps } = props;\n  const dialogScope = useDialogScope(__scopeAlertDialog);\n  return /* @__PURE__ */ jsx(DialogPrimitive.Root, { ...dialogScope, ...alertDialogProps, modal: true });\n};\nAlertDialog.displayName = ROOT_NAME;\nvar TRIGGER_NAME = \"AlertDialogTrigger\";\nvar AlertDialogTrigger = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeAlertDialog, ...triggerProps } = props;\n    const dialogScope = useDialogScope(__scopeAlertDialog);\n    return /* @__PURE__ */ jsx(DialogPrimitive.Trigger, { ...dialogScope, ...triggerProps, ref: forwardedRef });\n  }\n);\nAlertDialogTrigger.displayName = TRIGGER_NAME;\nvar PORTAL_NAME = \"AlertDialogPortal\";\nvar AlertDialogPortal = (props) => {\n  const { __scopeAlertDialog, ...portalProps } = props;\n  const dialogScope = useDialogScope(__scopeAlertDialog);\n  return /* @__PURE__ */ jsx(DialogPrimitive.Portal, { ...dialogScope, ...portalProps });\n};\nAlertDialogPortal.displayName = PORTAL_NAME;\nvar OVERLAY_NAME = \"AlertDialogOverlay\";\nvar AlertDialogOverlay = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeAlertDialog, ...overlayProps } = props;\n    const dialogScope = useDialogScope(__scopeAlertDialog);\n    return /* @__PURE__ */ jsx(DialogPrimitive.Overlay, { ...dialogScope, ...overlayProps, ref: forwardedRef });\n  }\n);\nAlertDialogOverlay.displayName = OVERLAY_NAME;\nvar CONTENT_NAME = \"AlertDialogContent\";\nvar [AlertDialogContentProvider, useAlertDialogContentContext] = createAlertDialogContext(CONTENT_NAME);\nvar AlertDialogContent = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeAlertDialog, children, ...contentProps } = props;\n    const dialogScope = useDialogScope(__scopeAlertDialog);\n    const contentRef = React.useRef(null);\n    const composedRefs = useComposedRefs(forwardedRef, contentRef);\n    const cancelRef = React.useRef(null);\n    return /* @__PURE__ */ jsx(\n      DialogPrimitive.WarningProvider,\n      {\n        contentName: CONTENT_NAME,\n        titleName: TITLE_NAME,\n        docsSlug: \"alert-dialog\",\n        children: /* @__PURE__ */ jsx(AlertDialogContentProvider, { scope: __scopeAlertDialog, cancelRef, children: /* @__PURE__ */ jsxs(\n          DialogPrimitive.Content,\n          {\n            role: \"alertdialog\",\n            ...dialogScope,\n            ...contentProps,\n            ref: composedRefs,\n            onOpenAutoFocus: composeEventHandlers(contentProps.onOpenAutoFocus, (event) => {\n              event.preventDefault();\n              cancelRef.current?.focus({ preventScroll: true });\n            }),\n            onPointerDownOutside: (event) => event.preventDefault(),\n            onInteractOutside: (event) => event.preventDefault(),\n            children: [\n              /* @__PURE__ */ jsx(Slottable, { children }),\n              /* @__PURE__ */ jsx(DescriptionWarning, { contentRef })\n            ]\n          }\n        ) })\n      }\n    );\n  }\n);\nAlertDialogContent.displayName = CONTENT_NAME;\nvar TITLE_NAME = \"AlertDialogTitle\";\nvar AlertDialogTitle = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeAlertDialog, ...titleProps } = props;\n    const dialogScope = useDialogScope(__scopeAlertDialog);\n    return /* @__PURE__ */ jsx(DialogPrimitive.Title, { ...dialogScope, ...titleProps, ref: forwardedRef });\n  }\n);\nAlertDialogTitle.displayName = TITLE_NAME;\nvar DESCRIPTION_NAME = \"AlertDialogDescription\";\nvar AlertDialogDescription = React.forwardRef((props, forwardedRef) => {\n  const { __scopeAlertDialog, ...descriptionProps } = props;\n  const dialogScope = useDialogScope(__scopeAlertDialog);\n  return /* @__PURE__ */ jsx(DialogPrimitive.Description, { ...dialogScope, ...descriptionProps, ref: forwardedRef });\n});\nAlertDialogDescription.displayName = DESCRIPTION_NAME;\nvar ACTION_NAME = \"AlertDialogAction\";\nvar AlertDialogAction = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeAlertDialog, ...actionProps } = props;\n    const dialogScope = useDialogScope(__scopeAlertDialog);\n    return /* @__PURE__ */ jsx(DialogPrimitive.Close, { ...dialogScope, ...actionProps, ref: forwardedRef });\n  }\n);\nAlertDialogAction.displayName = ACTION_NAME;\nvar CANCEL_NAME = \"AlertDialogCancel\";\nvar AlertDialogCancel = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeAlertDialog, ...cancelProps } = props;\n    const { cancelRef } = useAlertDialogContentContext(CANCEL_NAME, __scopeAlertDialog);\n    const dialogScope = useDialogScope(__scopeAlertDialog);\n    const ref = useComposedRefs(forwardedRef, cancelRef);\n    return /* @__PURE__ */ jsx(DialogPrimitive.Close, { ...dialogScope, ...cancelProps, ref });\n  }\n);\nAlertDialogCancel.displayName = CANCEL_NAME;\nvar DescriptionWarning = ({ contentRef }) => {\n  const MESSAGE = `\\`${CONTENT_NAME}\\` requires a description for the component to be accessible for screen reader users.\n\nYou can add a description to the \\`${CONTENT_NAME}\\` by passing a \\`${DESCRIPTION_NAME}\\` component as a child, which also benefits sighted users by adding visible context to the dialog.\n\nAlternatively, you can use your own component as a description by assigning it an \\`id\\` and passing the same value to the \\`aria-describedby\\` prop in \\`${CONTENT_NAME}\\`. If the description is confusing or duplicative for sighted users, you can use the \\`@radix-ui/react-visually-hidden\\` primitive as a wrapper around your description component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/alert-dialog`;\n  React.useEffect(() => {\n    const hasDescription = document.getElementById(\n      contentRef.current?.getAttribute(\"aria-describedby\")\n    );\n    if (!hasDescription) console.warn(MESSAGE);\n  }, [MESSAGE, contentRef]);\n  return null;\n};\nvar Root2 = AlertDialog;\nvar Trigger2 = AlertDialogTrigger;\nvar Portal2 = AlertDialogPortal;\nvar Overlay2 = AlertDialogOverlay;\nvar Content2 = AlertDialogContent;\nvar Action = AlertDialogAction;\nvar Cancel = AlertDialogCancel;\nvar Title2 = AlertDialogTitle;\nvar Description2 = AlertDialogDescription;\nexport {\n  Action,\n  AlertDialog,\n  AlertDialogAction,\n  AlertDialogCancel,\n  AlertDialogContent,\n  AlertDialogDescription,\n  AlertDialogOverlay,\n  AlertDialogPortal,\n  AlertDialogTitle,\n  AlertDialogTrigger,\n  Cancel,\n  Content2 as Content,\n  Description2 as Description,\n  Overlay2 as Overlay,\n  Portal2 as Portal,\n  Root2 as Root,\n  Title2 as Title,\n  Trigger2 as Trigger,\n  createAlertDialogScope\n};\n//# sourceMappingURL=index.mjs.map\n", "import * as React from \"react\"\r\nimport * as AlertDialogPrimitive from \"@radix-ui/react-alert-dialog\"\r\n\r\nimport { cn } from \"~/lib/utils\"\r\nimport { buttonVariants } from \"~/components/ui/button\"\r\n\r\nconst AlertDialog = AlertDialogPrimitive.Root\r\n\r\nconst AlertDialogTrigger = AlertDialogPrimitive.Trigger\r\n\r\nconst AlertDialogPortal = AlertDialogPrimitive.Portal\r\n\r\nconst AlertDialogOverlay = React.forwardRef<\r\n  React.ElementRef<typeof AlertDialogPrimitive.Overlay>,\r\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Overlay>\r\n>(({ className, ...props }, ref) => (\r\n  <AlertDialogPrimitive.Overlay\r\n    className={cn(\r\n      \"fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\",\r\n      className\r\n    )}\r\n    {...props}\r\n    ref={ref}\r\n  />\r\n))\r\nAlertDialogOverlay.displayName = AlertDialogPrimitive.Overlay.displayName\r\n\r\nconst AlertDialogContent = React.forwardRef<\r\n  React.ElementRef<typeof AlertDialogPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Content>\r\n>(({ className, ...props }, ref) => (\r\n  <AlertDialogPortal>\r\n    <AlertDialogOverlay />\r\n    <AlertDialogPrimitive.Content\r\n      ref={ref}\r\n      className={cn(\r\n        \"fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  </AlertDialogPortal>\r\n))\r\nAlertDialogContent.displayName = AlertDialogPrimitive.Content.displayName\r\n\r\nconst AlertDialogHeader = ({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLDivElement>) => (\r\n  <div\r\n    className={cn(\r\n      \"flex flex-col space-y-2 text-center sm:text-left\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n)\r\nAlertDialogHeader.displayName = \"AlertDialogHeader\"\r\n\r\nconst AlertDialogFooter = ({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLDivElement>) => (\r\n  <div\r\n    className={cn(\r\n      \"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n)\r\nAlertDialogFooter.displayName = \"AlertDialogFooter\"\r\n\r\nconst AlertDialogTitle = React.forwardRef<\r\n  React.ElementRef<typeof AlertDialogPrimitive.Title>,\r\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Title>\r\n>(({ className, ...props }, ref) => (\r\n  <AlertDialogPrimitive.Title\r\n    ref={ref}\r\n    className={cn(\"text-lg font-semibold\", className)}\r\n    {...props}\r\n  />\r\n))\r\nAlertDialogTitle.displayName = AlertDialogPrimitive.Title.displayName\r\n\r\nconst AlertDialogDescription = React.forwardRef<\r\n  React.ElementRef<typeof AlertDialogPrimitive.Description>,\r\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Description>\r\n>(({ className, ...props }, ref) => (\r\n  <AlertDialogPrimitive.Description\r\n    ref={ref}\r\n    className={cn(\"text-sm text-muted-foreground\", className)}\r\n    {...props}\r\n  />\r\n))\r\nAlertDialogDescription.displayName =\r\n  AlertDialogPrimitive.Description.displayName\r\n\r\nconst AlertDialogAction = React.forwardRef<\r\n  React.ElementRef<typeof AlertDialogPrimitive.Action>,\r\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Action>\r\n>(({ className, ...props }, ref) => (\r\n  <AlertDialogPrimitive.Action\r\n    ref={ref}\r\n    className={cn(buttonVariants(), className)}\r\n    {...props}\r\n  />\r\n))\r\nAlertDialogAction.displayName = AlertDialogPrimitive.Action.displayName\r\n\r\nconst AlertDialogCancel = React.forwardRef<\r\n  React.ElementRef<typeof AlertDialogPrimitive.Cancel>,\r\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Cancel>\r\n>(({ className, ...props }, ref) => (\r\n  <AlertDialogPrimitive.Cancel\r\n    ref={ref}\r\n    className={cn(\r\n      buttonVariants({ variant: \"outline\" }),\r\n      \"mt-2 sm:mt-0\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nAlertDialogCancel.displayName = AlertDialogPrimitive.Cancel.displayName\r\n\r\nexport {\r\n  AlertDialog,\r\n  AlertDialogPortal,\r\n  AlertDialogOverlay,\r\n  AlertDialogTrigger,\r\n  AlertDialogContent,\r\n  AlertDialogHeader,\r\n  AlertDialogFooter,\r\n  AlertDialogTitle,\r\n  AlertDialogDescription,\r\n  AlertDialogAction,\r\n  AlertDialogCancel,\r\n}\r\n", "import {\r\n    Home as HomeIcon,\r\n    Users as UsersIcon,\r\n    Truck,\r\n    MapPin,\r\n    MessageCircle,\r\n    LayoutDashboard,\r\n    Landmark,\r\n    User as UserIcon,\r\n    LogOut,\r\n    Network,\r\n    MessageCircleIcon,\r\n    Contact,\r\n    Handshake,\r\n    Receipt,\r\n    IdCard,\r\n    Layers,\r\n    Grape,\r\n    ChartNetwork,\r\n    ChartNoAxesCombined,\r\n    User,\r\n    HandCoins,\r\n    Users,\r\n    Tractor,\r\n    UserRoundCog,\r\n    LayoutList,\r\n    Cherry,\r\n    EarthLock,\r\n    PlaneTakeoff,\r\n    ChartPie,\r\n    Store,\r\n    Headset,\r\n    Warehouse\r\n} from \"lucide-react\";\r\nimport {\r\n    Sidebar,\r\n    SidebarContent,\r\n    SidebarHeader,\r\n    SidebarMenu,\r\n    SidebarMenuItem,\r\n    SidebarMenuButton,\r\n    SidebarMenuSub,\r\n    SidebarMenuSubItem,\r\n    SidebarMenuSubButton,\r\n    SidebarProvider,\r\n    SidebarRail,\r\n    SidebarTrigger,\r\n} from \"@components/ui/sidebar\";\r\nimport {\r\n    AlertDialog,\r\n    AlertDialogAction,\r\n    AlertDialogCancel,\r\n    AlertDialogContent,\r\n    AlertDialogDescription,\r\n    AlertDialogFooter,\r\n    AlertDialogHeader,\r\n    AlertDialogTitle,\r\n    AlertDialogTrigger,\r\n} from \"@components/ui/alert-dialog\";\r\nimport { Link, Navigate, Outlet, useLoaderData, useLocation, useSubmit } from \"@remix-run/react\";\r\nimport { destroySession, getSession } from \"@utils/session.server\";\r\nimport { json, LoaderFunction, redirect } from \"@remix-run/node\";\r\nimport { useEffect, useState } from \"react\";\r\nimport { withAuth, withResponse } from \"@utils/auth-utils\";\r\n\r\ninterface LoaderData {\r\n    userPermissions: string[];\r\n    userDetails: {\r\n        userDetails: Record<string, any>;\r\n        businessName: string;\r\n    };\r\n}\r\n\r\nexport const loader: LoaderFunction = withAuth(async ({ user }) => {\r\n    return withResponse({\r\n        userDetails: user || {},\r\n        userPermissions: user?.userDetails?.roles || [],\r\n    });\r\n});\r\n\r\nexport const action = withAuth(async ({ request }) => {\r\n    const session = await getSession(request.headers.get(\"Cookie\"));\r\n    return redirect(\"/login\", {\r\n        headers: {\r\n            \"Set-Cookie\": await destroySession(session),\r\n        },\r\n    });\r\n});\r\n\r\nexport default function Home() {\r\n    const { userPermissions, userDetails } = useLoaderData<LoaderData>();\r\n    const { pathname } = useLocation();\r\n    const submit = useSubmit();\r\n    const activeSection = pathname.split(\"/\")[2];\r\n\r\n    const [isNetWorkManagerBasic, setIsNetWorkManagerBasic] = useState(false);\r\n    const [isSalesManagerBasic, setIsSalesManagerBasic] = useState(false);\r\n    const [adminBasic, setAdminBasic] = useState(false);\r\n    const [isSellerBasic, setIsSellerBasic] = useState(false);\r\n\r\n    useEffect(() => {\r\n        if (userPermissions) {\r\n            setIsNetWorkManagerBasic(userPermissions.includes(\"OC_Manager\"));\r\n            setIsSalesManagerBasic(userPermissions.includes(\"FmSalesManager\"));\r\n            setIsSellerBasic(userPermissions.includes(\"SC_Basic\"));\r\n            setAdminBasic(userPermissions.includes(\"AC_Basic\"));\r\n        }\r\n    }, [userPermissions]);\r\n\r\n    const handleLogout = () => submit(null, { method: \"post\" });\r\n\r\n    if (pathname === \"/home\") {\r\n        return <Navigate to=\"/home/<USER>\" replace />;\r\n    }\r\n\r\n    return (\r\n        <SidebarProvider>\r\n            <div className=\"flex h-screen w-screen bg-white\">\r\n                <Sidebar className=\"bg-white border-r \">\r\n                    <SidebarHeader className=\"px-6 py-4 bg-white\">\r\n                        <div className=\"flex flex-col items-start gap-2\">\r\n                            <img src=\"/mnet-logo.svg\" alt=\"mNet Logo\" className=\"h-12 w-auto\" />\r\n                            <div className=\"flex items-center mt-8 w-full px-4 py-2 border rounded-md cursor-pointer hover:bg-gray-100 transition-colors\">\r\n                                <div className=\"flex items-center space-x-3\">\r\n                                    <div className=\"h-6 w-6 rounded-full bg-primary flex items-center justify-center text-sm font-bold text-white\">\r\n                                        {userDetails?.userDetails?.businessName?.[0]?.toUpperCase() || \"B\"}\r\n                                    </div>\r\n                                    <div className=\"text-gray-900 text-sm font-medium\">\r\n                                        {userDetails?.userDetails?.businessName || \"Business Name\"}\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </SidebarHeader>\r\n\r\n                    <SidebarContent className=\"bg-white\">\r\n                        <SidebarMenu className=\"mt-2 space-y-1\">\r\n                            {isSellerBasic && (\r\n                                <SidebarMenuItem>\r\n                                    <Link to=\"/home/<USER>\">\r\n                                        <SidebarMenuButton\r\n                                            isActive={activeSection === \"dashboard\"}\r\n                                            className={`px-6 py-3 text-gray-900 hover:bg-gray-100 transition-colors rounded-md ${activeSection === \"dashboard\"\r\n                                                ? \"bg-blue-100 text-blue-800 font-semibold\"\r\n                                                : \"\"\r\n                                                }`}\r\n                                        >\r\n                                            <HomeIcon className=\"h-5 w-5 mr-3\" />\r\n                                            Dashboard\r\n                                        </SidebarMenuButton>\r\n                                    </Link>\r\n                                </SidebarMenuItem>\r\n                            )}\r\n\r\n                            {isSellerBasic && (\r\n                                <SidebarMenuItem>\r\n                                    <Link to=\"/home/<USER>\">\r\n                                        <SidebarMenuButton\r\n                                            isActive={activeSection === \"customers\"}\r\n                                            className={`px-6 py-3 text-gray-900 hover:bg-gray-100 transition-colors rounded-md ${activeSection === \"customers\"\r\n                                                ? \"bg-blue-100 text-blue-800 font-semibold\"\r\n                                                : \"\"\r\n                                                }`}\r\n                                        >\r\n                                            <Contact className=\"h-5 w-5 mr-3\" />\r\n                                            My Customers\r\n                                        </SidebarMenuButton>\r\n                                    </Link>\r\n                                </SidebarMenuItem>\r\n                            )}\r\n\r\n                            {isSellerBasic && (\r\n                                <SidebarMenuItem>\r\n                                    <Link to=\"/home/<USER>\">\r\n                                        <SidebarMenuButton\r\n                                            isActive={activeSection === \"myTrips\"}\r\n                                            className={`px-6 py-3 text-gray-900 hover:bg-gray-100 transition-colors rounded-md ${activeSection === \"myTrips\"\r\n                                                ? \"bg-blue-100 text-blue-800 font-semibold\"\r\n                                                : \"\"\r\n                                                }`}\r\n                                        >\r\n                                            <Truck className=\"h-5 w-5 mr-3\" />\r\n                                            My Trips\r\n                                        </SidebarMenuButton>\r\n                                    </Link>\r\n                                </SidebarMenuItem>\r\n                            )}\r\n\r\n                            {isSellerBasic && (\r\n                                <SidebarMenuItem>\r\n                                    <Link to=\"/home/<USER>\">\r\n                                        <SidebarMenuButton\r\n                                            isActive={activeSection === \"myItems\"}\r\n                                            className={`px-6 py-3 text-gray-900 hover:bg-gray-100 transition-colors rounded-md ${activeSection === \"myItems\"\r\n                                                ? \"bg-blue-100 text-blue-800 font-semibold\"\r\n                                                : \"\"\r\n                                                }`}\r\n                                        >\r\n                                            <Grape className=\"h-5 w-5 mr-3\" />\r\n                                            My Items\r\n                                        </SidebarMenuButton>\r\n                                    </Link>\r\n                                </SidebarMenuItem>\r\n                            )}\r\n\r\n                            {isNetWorkManagerBasic && (\r\n                                <SidebarMenuItem>\r\n                                    <Link to=\"/home/<USER>\">\r\n                                        <SidebarMenuButton\r\n                                            isActive={activeSection === \"whatsappconnect\"}\r\n                                            className={`px-6 py-3 text-gray-900 hover:bg-gray-100 transition-colors rounded-md ${activeSection === \"whatsappconnect\"\r\n                                                ? \"bg-blue-100 text-blue-800 font-semibold\"\r\n                                                : \"\"\r\n                                                }`}\r\n                                        >\r\n                                            <MessageCircleIcon className=\"h-5 w-5 mr-3\" />\r\n                                            Whatsapp\r\n                                        </SidebarMenuButton>\r\n                                    </Link>\r\n                                </SidebarMenuItem>\r\n                            )}\r\n\r\n                            {(isNetWorkManagerBasic || adminBasic) && (\r\n                                <SidebarMenuItem>\r\n                                    <Link to=\"/home/<USER>\">\r\n                                        <SidebarMenuButton\r\n                                            isActive={activeSection === \"masterItemCategory\"}\r\n                                            className={`px-6 py-3 text-gray-900 hover:bg-gray-100 transition-colors rounded-md ${activeSection === \"masterItemCategory\"\r\n                                                ? \"bg-blue-100 text-blue-800 font-semibold\"\r\n                                                : \"\"\r\n                                                }`}\r\n                                        >\r\n                                            <Cherry className=\"h-5 w-5 mr-3\" />\r\n                                            LiveOrderDashBoard\r\n                                        </SidebarMenuButton>\r\n                                    </Link>\r\n                                </SidebarMenuItem>\r\n                            )}\r\n                            {isNetWorkManagerBasic && (\r\n                                <SidebarMenuItem>\r\n                                    <Link to=\"/home/<USER>\">\r\n                                        <SidebarMenuButton\r\n                                            isActive={activeSection === \"masterItemCategory\"}\r\n                                            className={`px-6 py-3 text-gray-900 hover:bg-gray-100 transition-colors rounded-md ${activeSection === \"masterItemCategory\"\r\n                                                ? \"bg-blue-100 text-blue-800 font-semibold\"\r\n                                                : \"\"\r\n                                                }`}\r\n                                        >\r\n                                            <Cherry className=\"h-5 w-5 mr-3\" />\r\n                                            Master Item Category\r\n                                        </SidebarMenuButton>\r\n                                    </Link>\r\n                                </SidebarMenuItem>\r\n                            )}\r\n\r\n                            {isNetWorkManagerBasic && (\r\n                                <SidebarMenuItem>\r\n                                    <Link to=\"/home/<USER>\">\r\n                                        <SidebarMenuButton\r\n                                            isActive={activeSection === \"masterItems\"}\r\n                                            className={`px-6 py-3 text-gray-900 hover:bg-gray-100 transition-colors rounded-md ${activeSection === \"masterItems\"\r\n                                                ? \"bg-blue-100 text-blue-800 font-semibold\"\r\n                                                : \"\"\r\n                                                }`}\r\n                                        >\r\n                                            <LayoutList className=\"h-5 w-5 mr-3\" />\r\n                                            Master Items\r\n                                        </SidebarMenuButton>\r\n                                    </Link>\r\n                                </SidebarMenuItem>\r\n                            )}\r\n\r\n                            {isSellerBasic && (\r\n                                <SidebarMenuItem>\r\n                                    <Link to=\"/home/<USER>\">\r\n                                        <SidebarMenuButton\r\n                                            isActive={activeSection === \"sellerWiseSales\"}\r\n                                            className={`px-6 py-3 text-gray-900 hover:bg-gray-100 transition-colors rounded-md ${activeSection === \"sellerWiseSales\"\r\n                                                ? \"bg-blue-100 text-blue-800 font-semibold\"\r\n                                                : \"\"\r\n                                                }`}\r\n                                        >\r\n                                            <ChartNoAxesCombined className=\"h-5 w-5 mr-3\" />\r\n                                            Sales Report\r\n                                        </SidebarMenuButton>\r\n                                    </Link>\r\n                                </SidebarMenuItem>\r\n                            )}\r\n\r\n                            {isSellerBasic && (\r\n                                <SidebarMenuItem>\r\n                                    <Link to=\"/home/<USER>\">\r\n                                        <SidebarMenuButton\r\n                                            isActive={activeSection === \"metaSalesReports\"}\r\n                                            className={`px-6 py-3 text-gray-900 hover:bg-gray-100 transition-colors rounded-md ${activeSection === \"metaSalesReports\"\r\n                                                ? \"bg-blue-100 text-blue-800 font-semibold\"\r\n                                                : \"\"\r\n                                                }`}\r\n                                        >\r\n                                            <ChartNetwork className=\"h-5 w-5 mr-3\" />\r\n                                            Sales Dashboard\r\n                                        </SidebarMenuButton>\r\n                                    </Link>\r\n                                </SidebarMenuItem>\r\n                            )}\r\n                            {(isSellerBasic) && (\r\n                                <SidebarMenuItem>\r\n                                    <SidebarMenuButton className=\"px-6 py-3 hover:bg-gray-100 text-gray-900 font-semibold rounded-md\">\r\n                                        <Warehouse className=\"h-5 w-5 mr-3\" />\r\n                                        StockManagement\r\n                                    </SidebarMenuButton>\r\n                                    <SidebarMenuSub>\r\n                                        <SidebarMenuSubItem>\r\n                                            <Link to=\"/home/<USER>\">\r\n                                                <SidebarMenuSubButton\r\n                                                    isActive={activeSection === \"mystock\"}\r\n                                                    className={`px-6 py-2 text-gray-900 hover:bg-gray-100 transition-colors rounded-md ${activeSection === \"salesAnalysis\"\r\n                                                        ? \"bg-blue-100 text-blue-800 font-medium\"\r\n                                                        : \"\"\r\n                                                        }`}\r\n                                                >\r\n                                                    My Stock\r\n                                                </SidebarMenuSubButton>\r\n                                            </Link>\r\n                                        </SidebarMenuSubItem>\r\n                                        <SidebarMenuSubItem>\r\n                                            <Link to=\"/home/<USER>\">\r\n                                                <SidebarMenuSubButton\r\n                                                    isActive={activeSection === \"stockWithMe\"}\r\n                                                    className={`px-6 py-2 text-gray-900 hover:bg-gray-100 transition-colors rounded-md ${activeSection === \"sellerWiseSales\"\r\n                                                        ? \"bg-blue-100 text-blue-800 font-medium\"\r\n                                                        : \"\"\r\n                                                        }`}\r\n                                                >\r\n                                                    Stock With Me\r\n                                                </SidebarMenuSubButton>\r\n                                            </Link>\r\n                                        </SidebarMenuSubItem>\r\n                                    </SidebarMenuSub>\r\n                                </SidebarMenuItem>\r\n                            )}\r\n\r\n                            {(isSalesManagerBasic || adminBasic) && (\r\n                                <SidebarMenuItem>\r\n                                    <SidebarMenuButton className=\"px-6 py-3 hover:bg-gray-100 text-gray-900 font-semibold rounded-md\">\r\n                                        <ChartPie className=\"h-5 w-5 mr-3\" />\r\n                                        Sales Related\r\n                                    </SidebarMenuButton>\r\n                                    <SidebarMenuSub>\r\n                                        <SidebarMenuSubItem>\r\n                                            <Link to=\"/home/<USER>\">\r\n                                                <SidebarMenuSubButton\r\n                                                    isActive={activeSection === \"salesAnalysis\"}\r\n                                                    className={`px-6 py-2 text-gray-900 hover:bg-gray-100 transition-colors rounded-md ${activeSection === \"salesAnalysis\"\r\n                                                        ? \"bg-blue-100 text-blue-800 font-medium\"\r\n                                                        : \"\"\r\n                                                        }`}\r\n                                                >\r\n                                                    Sales Analysis\r\n                                                </SidebarMenuSubButton>\r\n                                            </Link>\r\n                                        </SidebarMenuSubItem>\r\n                                        <SidebarMenuSubItem>\r\n                                            <Link to=\"/home/<USER>\">\r\n                                                <SidebarMenuSubButton\r\n                                                    isActive={activeSection === \"sellerWiseSales\"}\r\n                                                    className={`px-6 py-2 text-gray-900 hover:bg-gray-100 transition-colors rounded-md ${activeSection === \"sellerWiseSales\"\r\n                                                        ? \"bg-blue-100 text-blue-800 font-medium\"\r\n                                                        : \"\"\r\n                                                        }`}\r\n                                                >\r\n                                                    Sales Report\r\n                                                </SidebarMenuSubButton>\r\n                                            </Link>\r\n                                        </SidebarMenuSubItem>\r\n                                        <SidebarMenuSubItem>\r\n                                            <Link to=\"/home/<USER>\">\r\n                                                <SidebarMenuSubButton\r\n                                                    isActive={activeSection === \"fmMetaHourlySales\"}\r\n                                                    className={`px-6 py-2 text-gray-900 hover:bg-gray-100 transition-colors rounded-md ${activeSection === \"fmMetaHourlySales\"\r\n                                                        ? \"bg-blue-100 text-blue-800 font-medium\"\r\n                                                        : \"\"\r\n                                                        }`}\r\n                                                >\r\n                                                    FOS HourlyWise\r\n                                                </SidebarMenuSubButton>\r\n                                            </Link>\r\n                                        </SidebarMenuSubItem>\r\n                                        <SidebarMenuSubItem>\r\n                                            <Link to=\"/home/<USER>\">\r\n                                                <SidebarMenuSubButton\r\n                                                    isActive={activeSection === \"sellerLevelreturns\"}\r\n                                                    className={`px-6 py-2 text-gray-900 hover:bg-gray-100 transition-colors rounded-md ${activeSection === \"sellerLevelreturns\"\r\n                                                        ? \"bg-blue-100 text-blue-800 font-medium\"\r\n                                                        : \"\"\r\n                                                        }`}\r\n                                                >\r\n                                                    Seller Level Returns\r\n                                                </SidebarMenuSubButton>\r\n                                            </Link>\r\n                                        </SidebarMenuSubItem>\r\n                                        <SidebarMenuSubItem>\r\n                                            <Link to=\"/home/<USER>\">\r\n                                                <SidebarMenuSubButton\r\n                                                    isActive={activeSection === \"fosLevelReturns\"}\r\n                                                    className={`px-6 py-2 text-gray-900 hover:bg-gray-100 transition-colors rounded-md ${activeSection === \"fosLevelReturns\"\r\n                                                        ? \"bg-blue-100 text-blue-800 font-medium\"\r\n                                                        : \"\"\r\n                                                        }`}\r\n                                                >\r\n                                                    FOS Level Returns\r\n                                                </SidebarMenuSubButton>\r\n                                            </Link>\r\n                                        </SidebarMenuSubItem>\r\n                                    </SidebarMenuSub>\r\n                                </SidebarMenuItem>\r\n                            )}\r\n\r\n                            {(isNetWorkManagerBasic || adminBasic) && (\r\n                                <SidebarMenuItem>\r\n                                    <Link to=\"/home/<USER>\">\r\n                                        <SidebarMenuButton\r\n                                            isActive={activeSection === \"bankTransactions\"}\r\n                                            className={`px-6 py-3 text-gray-900 hover:bg-gray-100 transition-colors rounded-md ${activeSection === \"bankTransactions\"\r\n                                                ? \"bg-blue-100 text-blue-800 font-semibold\"\r\n                                                : \"\"\r\n                                                }`}\r\n                                        >\r\n                                            <Landmark className=\"h-5 w-5 mr-3\" />\r\n                                            Bank Transactions\r\n                                        </SidebarMenuButton>\r\n                                    </Link>\r\n                                </SidebarMenuItem>\r\n                            )}\r\n\r\n                            {isNetWorkManagerBasic && (\r\n                                <SidebarMenuItem>\r\n                                    <Link to=\"/home/<USER>\">\r\n                                        <SidebarMenuButton\r\n                                            isActive={activeSection === \"localities\"}\r\n                                            className={`px-6 py-3 text-gray-900 hover:bg-gray-100 transition-colors rounded-md ${activeSection === \"localities\"\r\n                                                ? \"bg-blue-100 text-blue-800 font-semibold\"\r\n                                                : \"\"\r\n                                                }`}\r\n                                        >\r\n                                            <MapPin className=\"h-5 w-5 mr-3\" />\r\n                                            Master Localities\r\n                                        </SidebarMenuButton>\r\n                                    </Link>\r\n                                </SidebarMenuItem>\r\n                            )}\r\n                            {(isNetWorkManagerBasic || adminBasic) && <SidebarMenuItem>\r\n                                <Link to=\"/home/<USER>\">\r\n                                    <SidebarMenuButton isActive={activeSection === \"tickets\"}\r\n                                        className=\"px-6 py-6 hover:bg-gray-100 text-gray-900\"\r\n                                    >\r\n                                        <Headset className=\"h-6 w-6 mr-3\" />\r\n                                        Support Tickets\r\n                                    </SidebarMenuButton>\r\n                                </Link>\r\n                            </SidebarMenuItem>}\r\n\r\n                            {(isNetWorkManagerBasic || isSalesManagerBasic || adminBasic) && (\r\n                                <SidebarMenuItem>\r\n                                    <Link to=\"/home/<USER>\">\r\n                                        <SidebarMenuButton\r\n                                            isActive={activeSection === \"networkManagement\"}\r\n                                            className={`px-6 py-3 text-gray-900 hover:bg-gray-100 transition-colors rounded-md ${activeSection === \"networkManagement\"\r\n                                                ? \"bg-blue-100 text-blue-800 font-semibold\"\r\n                                                : \"\"\r\n                                                }`}\r\n                                        >\r\n                                            <EarthLock className=\"h-5 w-5 mr-3\" />\r\n                                            Network Management\r\n                                        </SidebarMenuButton>\r\n                                    </Link>\r\n                                </SidebarMenuItem>\r\n                            )}\r\n\r\n                            {(isNetWorkManagerBasic || adminBasic) && (\r\n                                <SidebarMenuItem>\r\n                                    <Link to=\"/home/<USER>\">\r\n                                        <SidebarMenuButton\r\n                                            isActive={activeSection === \"sellerManagement\"}\r\n                                            className={`px-6 py-3 text-gray-900 hover:bg-gray-100 transition-colors rounded-md ${activeSection === \"sellerManagement\"\r\n                                                ? \"bg-blue-100 text-blue-800 font-semibold\"\r\n                                                : \"\"\r\n                                                }`}\r\n                                        >\r\n                                            <PlaneTakeoff className=\"h-5 w-5 mr-3\" />\r\n                                            Seller Management\r\n                                        </SidebarMenuButton>\r\n                                    </Link>\r\n                                </SidebarMenuItem>\r\n                            )}\r\n\r\n                            {/* {(isNetWorkManagerBasic || adminBasic) && (\r\n                                <SidebarMenuItem>\r\n                                    <Link to=\"/home/<USER>\">\r\n                                        <SidebarMenuButton\r\n                                            isActive={activeSection === \"freeItemManagement\"}\r\n                                            className={`px-6 py-3 text-gray-900 hover:bg-gray-100 transition-colors rounded-md ${activeSection === \"sellerManagement\"\r\n                                                ? \"bg-blue-100 text-blue-800 font-semibold\"\r\n                                                : \"\"\r\n                                                }`}\r\n                                        >\r\n                                            <PlaneTakeoff className=\"h-5 w-5 mr-3\" />\r\n                                            Free Item Management\r\n                                        </SidebarMenuButton>\r\n                                    </Link>\r\n                                </SidebarMenuItem>\r\n                            )} */}\r\n                            \r\n\r\n                            {(isNetWorkManagerBasic || adminBasic) && (\r\n    <SidebarMenuItem>\r\n        <Link to=\"/home/<USER>\">\r\n            <SidebarMenuButton\r\n                isActive={activeSection === \"metaRestaurantDashboard\"}\r\n                className={`px-6 py-3 text-gray-900 hover:bg-gray-100 transition-colors rounded-md ${\r\n                    activeSection === \"metaRestaurantDashboard\"\r\n                        ? \"bg-blue-100 text-blue-800 font-semibold\"\r\n                        : \"\"\r\n                }`}\r\n            >\r\n                <LayoutDashboard className=\"h-5 w-5 mr-3\" />\r\n                Restaurant Dashboard\r\n            </SidebarMenuButton>\r\n        </Link>\r\n    </SidebarMenuItem>\r\n)}\r\n                            {(isNetWorkManagerBasic || adminBasic) && (\r\n                                <SidebarMenuItem>\r\n                                    <Link to=\"/home/<USER>\">\r\n                                        <SidebarMenuButton\r\n                                            isActive={activeSection === \"buyerManagement\"}\r\n                                            className={`px-6 py-3 text-gray-900 hover:bg-gray-100 transition-colors rounded-md ${activeSection === \"buyerManagement\"\r\n                                                ? \"bg-blue-100 text-blue-800 font-semibold\"\r\n                                                : \"\"\r\n                                                }`}\r\n                                        >\r\n                                            <Store className=\"h-5 w-5 mr-3\" />\r\n                                            Buyer Management\r\n                                        </SidebarMenuButton>\r\n                                    </Link>\r\n                                </SidebarMenuItem>\r\n                            )}\r\n\r\n                            {(isNetWorkManagerBasic || adminBasic) && (\r\n                                <SidebarMenuItem>\r\n                                    <Link to=\"/home/<USER>\">\r\n                                        <SidebarMenuButton\r\n                                            isActive={activeSection === \"tripManagement\"}\r\n                                            className={`px-6 py-3 text-gray-900 hover:bg-gray-100 transition-colors rounded-md ${activeSection === \"tripManagement\"\r\n                                                ? \"bg-blue-100 text-blue-800 font-semibold\"\r\n                                                : \"\"\r\n                                                }`}\r\n                                        >\r\n                                            <Truck className=\"h-5 w-5 mr-3\" />\r\n                                            Trip Management\r\n                                        </SidebarMenuButton>\r\n                                    </Link>\r\n                                </SidebarMenuItem>\r\n                            )}\r\n\r\n\r\n                            <SidebarMenuItem className=\"mt-auto\">\r\n                                <AlertDialog>\r\n                                    <AlertDialogTrigger asChild>\r\n                                        <SidebarMenuButton className=\"px-6 py-3 hover:bg-gray-100 text-gray-900 transition-colors rounded-md\">\r\n                                            <LogOut className=\"h-5 w-5 mr-3\" />\r\n                                            Logout\r\n                                        </SidebarMenuButton>\r\n                                    </AlertDialogTrigger>\r\n                                    <AlertDialogContent>\r\n                                        <AlertDialogHeader>\r\n                                            <AlertDialogTitle>Are you sure you want to logout?</AlertDialogTitle>\r\n                                            <AlertDialogDescription>\r\n                                                You will be redirected to the login page.\r\n                                            </AlertDialogDescription>\r\n                                        </AlertDialogHeader>\r\n                                        <AlertDialogFooter>\r\n                                            <AlertDialogCancel>Cancel</AlertDialogCancel>\r\n                                            <AlertDialogAction onClick={handleLogout}>Logout</AlertDialogAction>\r\n                                        </AlertDialogFooter>\r\n                                    </AlertDialogContent>\r\n                                </AlertDialog>\r\n                            </SidebarMenuItem>\r\n                        </SidebarMenu>\r\n                    </SidebarContent>\r\n                    <SidebarRail />\r\n                </Sidebar>\r\n\r\n                <main className=\"flex-1 overflow-y-auto p-4\">\r\n                    <SidebarTrigger className=\"mb-4 lg:hidden text-gray-900 hover:bg-gray-100 p-2 rounded-md\" />\r\n                    <Outlet />\r\n                </main>\r\n            </div>\r\n        </SidebarProvider>\r\n    );\r\n}"], "names": ["React.useState", "React.useEffect", "jsx", "React.createContext", "React.useContext", "React.forwardRef", "React.useCallback", "open", "React.useMemo", "jsxs", "AlertDialog", "DialogPrimitive.Root", "AlertDialogTrigger", "DialogPrimitive.Trigger", "AlertDialogPortal", "DialogPrimitive.Portal", "AlertDialogOverlay", "DialogPrimitive.Overlay", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "React.useRef", "DialogPrimitive.WarningProvider", "DialogPrimitive.Content", "AlertDialogTitle", "DialogPrimitive.Title", "AlertDialogDescription", "DialogPrimitive.Description", "AlertDialogAction", "DialogPrimitive.Close", "AlertDialogCancel", "AlertDialogPrimitive.Root", "AlertDialogPrimitive.Trigger", "AlertDialogPrimitive.Portal", "AlertDialogPrimitive.Overlay", "AlertDialogPrimitive.Content", "AlertDialogPrimitive.Title", "AlertDialogPrimitive.Description", "AlertDialogPrimitive.Action", "AlertDialogPrimitive.Cancel", "Home", "userPermissions", "userDetails", "useLoaderData", "pathname", "useLocation", "submit", "useSubmit", "activeSection", "split", "isNetWorkManagerBasic", "setIsNetWorkManagerBasic", "useState", "isSalesManagerBasic", "setIsSalesManagerBasic", "adminBasic", "setAdminBasic", "isSellerBasic", "setIsSellerBasic", "useEffect", "includes", "handleLogout", "method", "Navigate", "to", "replace", "SidebarProvider", "children", "className", "Sidebar", "SidebarHeader", "src", "alt", "businessName", "toUpperCase", "<PERSON>bar<PERSON><PERSON>nt", "SidebarMenu", "SidebarMenuItem", "Link", "SidebarMenuButton", "isActive", "HomeIcon", "Contact", "Truck", "Grape", "MessageCircleIcon", "Cherry", "LayoutList", "ChartNoAxesCombined", "ChartNetwork", "Warehouse", "SidebarMenuSub", "SidebarMenuSubItem", "SidebarMenuSubButton", "ChartPie", "Landmark", "MapPin", "Headset", "EarthLock", "PlaneTakeoff", "LayoutDashboard", "Store", "<PERSON><PERSON><PERSON><PERSON>", "LogOut", "Alert<PERSON><PERSON><PERSON>H<PERSON>er", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "onClick", "SidebarRail", "SidebarTrigger", "Outlet"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA,MAAM,eAAe,iBAAiB,gBAAgB;AAAA,EACpD,CAAC,QAAQ,EAAE,GAAG,2BAA2B,KAAK,SAAQ,CAAE;AAAA,EACxD,CAAC,QAAQ,EAAE,GAAG,8BAA8B,KAAK,SAAQ,CAAE;AAAA,EAC3D,CAAC,QAAQ,EAAE,GAAG,oBAAoB,KAAK,SAAQ,CAAE;AAAA,EACjD,CAAC,QAAQ,EAAE,GAAG,4BAA4B,KAAK,SAAQ,CAAE;AAAA,EACzD,CAAC,UAAU,EAAE,IAAI,MAAM,IAAI,KAAK,GAAG,KAAK,KAAK,UAAU;AAAA,EACvD,CAAC,UAAU,EAAE,IAAI,MAAM,IAAI,MAAM,GAAG,KAAK,KAAK,UAAU;AAAA,EACxD,CAAC,UAAU,EAAE,IAAI,KAAK,IAAI,MAAM,GAAG,KAAK,KAAK,SAAU,CAAA;AACzD,CAAC;ACjBD;AAAA;AAAA;AAAA;AAAA;AAAA;AASA,MAAM,sBAAsB,iBAAiB,uBAAuB;AAAA,EAClE,CAAC,QAAQ,EAAE,GAAG,YAAY,KAAK,SAAQ,CAAE;AAAA,EACzC,CAAC,QAAQ,EAAE,GAAG,YAAY,KAAK,SAAQ,CAAE;AAAA,EACzC,CAAC,QAAQ,EAAE,GAAG,aAAa,KAAK,SAAQ,CAAE;AAAA,EAC1C;AAAA,IACE;AAAA,IACA,EAAE,GAAG,0EAA0E,KAAK,SAAQ;AAAA,EAC7F;AAAA,EACD,CAAC,QAAQ,EAAE,GAAG,WAAW,KAAK,SAAQ,CAAE;AAAA,EACxC,CAAC,QAAQ,EAAE,GAAG,WAAW,KAAK,SAAU,CAAA;AAC1C,CAAC;ACnBD;AAAA;AAAA;AAAA;AAAA;AAAA;AASA,MAAM,WAAW,iBAAiB,YAAY;AAAA,EAC5C;AAAA,IACE;AAAA,IACA;AAAA,MACE,GAAG;AAAA,MACH,KAAK;AAAA,IACX;AAAA,EACG;AAAA,EACD,CAAC,QAAQ,EAAE,GAAG,mCAAmC,KAAK,SAAU,CAAA;AAClE,CAAC;AClBD;AAAA;AAAA;AAAA;AAAA;AAAA;AASA,MAAM,SAAS,iBAAiB,UAAU;AAAA,EACxC,CAAC,QAAQ,EAAE,GAAG,0DAA0D,KAAK,SAAQ,CAAE;AAAA,EACvF,CAAC,QAAQ,EAAE,GAAG,2DAA2D,KAAK,SAAQ,CAAE;AAAA,EACxF,CAAC,QAAQ,EAAE,GAAG,wDAAwD,KAAK,SAAQ,CAAE;AAAA,EACrF,CAAC,QAAQ,EAAE,GAAG,qDAAqD,KAAK,SAAU,CAAA;AACpF,CAAC;ACdD;AAAA;AAAA;AAAA;AAAA;AAAA;AASA,MAAM,UAAU,iBAAiB,WAAW;AAAA,EAC1C,CAAC,QAAQ,EAAE,GAAG,WAAW,KAAK,SAAQ,CAAE;AAAA,EACxC,CAAC,QAAQ,EAAE,GAAG,4CAA4C,KAAK,SAAQ,CAAE;AAAA,EACzE,CAAC,QAAQ,EAAE,GAAG,UAAU,KAAK,QAAO,CAAE;AAAA,EACtC,CAAC,UAAU,EAAE,IAAI,MAAM,IAAI,MAAM,GAAG,KAAK,KAAK,UAAU;AAAA,EACxD,CAAC,QAAQ,EAAE,GAAG,KAAK,GAAG,KAAK,OAAO,MAAM,QAAQ,MAAM,IAAI,KAAK,KAAK,SAAU,CAAA;AAChF,CAAC;ACfD;AAAA;AAAA;AAAA;AAAA;AAAA;AASA,MAAM,YAAY,iBAAiB,aAAa;AAAA,EAC9C,CAAC,QAAQ,EAAE,GAAG,2BAA2B,KAAK,SAAQ,CAAE;AAAA,EACxD,CAAC,QAAQ,EAAE,GAAG,kEAAkE,KAAK,SAAQ,CAAE;AAAA,EAC/F,CAAC,QAAQ,EAAE,GAAG,mCAAmC,KAAK,SAAQ,CAAE;AAAA,EAChE,CAAC,QAAQ,EAAE,GAAG,6BAA6B,KAAK,SAAQ,CAAE;AAAA,EAC1D,CAAC,QAAQ,EAAE,GAAG,2BAA2B,KAAK,SAAQ,CAAE;AAAA,EACxD,CAAC,QAAQ,EAAE,OAAO,KAAK,QAAQ,KAAK,GAAG,MAAM,GAAG,KAAK,IAAI,KAAK,KAAK,SAAU,CAAA;AAC/E,CAAC;AChBD;AAAA;AAAA;AAAA;AAAA;AAAA;AASA,MAAM,QAAQ,iBAAiB,SAAS;AAAA,EACtC,CAAC,QAAQ,EAAE,GAAG,sBAAsB,KAAK,SAAQ,CAAE;AAAA,EACnD,CAAC,UAAU,EAAE,IAAI,QAAQ,IAAI,SAAS,GAAG,KAAK,KAAK,UAAU;AAAA,EAC7D,CAAC,UAAU,EAAE,IAAI,QAAQ,IAAI,OAAO,GAAG,KAAK,KAAK,UAAU;AAAA,EAC3D,CAAC,UAAU,EAAE,IAAI,SAAS,IAAI,SAAS,GAAG,KAAK,KAAK,UAAU;AAAA,EAC9D,CAAC,UAAU,EAAE,IAAI,SAAS,IAAI,QAAQ,GAAG,KAAK,KAAK,UAAU;AAAA,EAC7D,CAAC,UAAU,EAAE,IAAI,SAAS,IAAI,SAAS,GAAG,KAAK,KAAK,UAAU;AAAA,EAC9D,CAAC,UAAU,EAAE,IAAI,QAAQ,IAAI,QAAQ,GAAG,KAAK,KAAK,UAAU;AAAA,EAC5D,CAAC,UAAU,EAAE,IAAI,QAAQ,IAAI,SAAS,GAAG,KAAK,KAAK,UAAU;AAAA,EAC7D,CAAC,UAAU,EAAE,IAAI,KAAK,IAAI,MAAM,GAAG,KAAK,KAAK,SAAU,CAAA;AACzD,CAAC;ACnBD;AAAA;AAAA;AAAA;AAAA;AAAA;AASA,MAAM,UAAU,iBAAiB,WAAW;AAAA,EAC1C;AAAA,IACE;AAAA,IACA;AAAA,MACE,GAAG;AAAA,MACH,KAAK;AAAA,IACX;AAAA,EACG;AAAA,EACD,CAAC,QAAQ,EAAE,GAAG,6BAA6B,KAAK,SAAU,CAAA;AAC5D,CAAC;AClBD;AAAA;AAAA;AAAA;AAAA;AAAA;AASA,MAAM,QAAQ,iBAAiB,SAAS;AAAA,EACtC,CAAC,QAAQ,EAAE,GAAG,8CAA8C,KAAK,SAAQ,CAAE;AAAA,EAC3E;AAAA,IACE;AAAA,IACA;AAAA,MACE,GAAG;AAAA,MACH,KAAK;AAAA,IACX;AAAA,EACA;AACA,CAAC;AClBD;AAAA;AAAA;AAAA;AAAA;AAAA;AASA,MAAM,WAAW,iBAAiB,YAAY;AAAA,EAC5C,CAAC,QAAQ,EAAE,IAAI,KAAK,IAAI,MAAM,IAAI,MAAM,IAAI,MAAM,KAAK,QAAO,CAAE;AAAA,EAChE,CAAC,QAAQ,EAAE,IAAI,KAAK,IAAI,KAAK,IAAI,MAAM,IAAI,MAAM,KAAK,SAAQ,CAAE;AAAA,EAChE,CAAC,QAAQ,EAAE,IAAI,MAAM,IAAI,MAAM,IAAI,MAAM,IAAI,MAAM,KAAK,SAAQ,CAAE;AAAA,EAClE,CAAC,QAAQ,EAAE,IAAI,MAAM,IAAI,MAAM,IAAI,MAAM,IAAI,MAAM,KAAK,OAAM,CAAE;AAAA,EAChE,CAAC,QAAQ,EAAE,IAAI,MAAM,IAAI,MAAM,IAAI,MAAM,IAAI,MAAM,KAAK,SAAQ,CAAE;AAAA,EAClE,CAAC,WAAW,EAAE,QAAQ,iBAAiB,KAAK,SAAU,CAAA;AACxD,CAAC;AChBD;AAAA;AAAA;AAAA;AAAA;AAAA;AASA,MAAM,aAAa,iBAAiB,cAAc;AAAA,EAChD,CAAC,QAAQ,EAAE,OAAO,KAAK,QAAQ,KAAK,GAAG,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,SAAQ,CAAE;AAAA,EAC5E,CAAC,QAAQ,EAAE,OAAO,KAAK,QAAQ,KAAK,GAAG,KAAK,GAAG,MAAM,IAAI,KAAK,KAAK,SAAQ,CAAE;AAAA,EAC7E,CAAC,QAAQ,EAAE,GAAG,WAAW,KAAK,SAAQ,CAAE;AAAA,EACxC,CAAC,QAAQ,EAAE,GAAG,WAAW,KAAK,SAAQ,CAAE;AAAA,EACxC,CAAC,QAAQ,EAAE,GAAG,YAAY,KAAK,SAAQ,CAAE;AAAA,EACzC,CAAC,QAAQ,EAAE,GAAG,YAAY,KAAK,SAAU,CAAA;AAC3C,CAAC;AChBD;AAAA;AAAA;AAAA;AAAA;AAAA;AASA,MAAM,YAAY,iBAAiB,aAAa;AAAA,EAC9C,CAAC,QAAQ,EAAE,OAAO,MAAM,QAAQ,MAAM,GAAG,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,SAAQ,CAAE;AAAA,EAC9E,CAAC,QAAQ,EAAE,GAAG,WAAW,KAAK,SAAU,CAAA;AAC1C,CAAC;ACZD;AAAA;AAAA;AAAA;AAAA;AAAA;AASA,MAAM,eAAe,iBAAiB,gBAAgB;AAAA,EACpD,CAAC,QAAQ,EAAE,GAAG,YAAY,KAAK,SAAQ,CAAE;AAAA,EACzC;AAAA,IACE;AAAA,IACA;AAAA,MACE,GAAG;AAAA,MACH,KAAK;AAAA,IACX;AAAA,EACA;AACA,CAAC;AClBD;AAAA;AAAA;AAAA;AAAA;AAAA;AASA,MAAM,YAAY,iBAAiB,aAAa;AAAA,EAC9C;AAAA,IACE;AAAA,IACA;AAAA,MACE,GAAG;AAAA,MACH,KAAK;AAAA,IACX;AAAA,EACG;AAAA,EACD,CAAC,QAAQ,EAAE,GAAG,YAAY,KAAK,SAAQ,CAAE;AAAA,EACzC,CAAC,QAAQ,EAAE,GAAG,YAAY,KAAK,SAAQ,CAAE;AAAA,EACzC,CAAC,QAAQ,EAAE,OAAO,MAAM,QAAQ,MAAM,GAAG,KAAK,GAAG,MAAM,KAAK,SAAU,CAAA;AACxE,CAAC;AClBD,MAAM,oBAAoB;AAEnB,SAAS,cAAc;AAC5B,QAAM,CAAC,UAAU,WAAW,IAAIA,aAAAA,SAAoC,MAAS;AAE7EC,eAAAA,UAAgB,MAAM;AACpB,UAAM,MAAM,OAAO,WAAW,eAAe,oBAAoB,CAAC,KAAK;AACvE,UAAM,WAAW,MAAM;AACT,kBAAA,OAAO,aAAa,iBAAiB;AAAA,IACnD;AACI,QAAA,iBAAiB,UAAU,QAAQ;AAC3B,gBAAA,OAAO,aAAa,iBAAiB;AACjD,WAAO,MAAM,IAAI,oBAAoB,UAAU,QAAQ;AAAA,EACzD,GAAG,EAAE;AAEL,SAAO,CAAC,CAAC;AACX;AChBA,SAAS,SAAS;AAAA,EAChB;AAAA,EACA,GAAG;AACL,GAAyC;AAErC,SAAAC,kCAAA;AAAA,IAAC;AAAA,IAAA;AAAA,MACC,WAAW,GAAG,qCAAqC,SAAS;AAAA,MAC3D,GAAG;AAAA,IAAA;AAAA,EACN;AAEJ;ACOA,MAAM,sBAAsB;AAC5B,MAAM,yBAAyB,KAAK,KAAK,KAAK;AAC9C,MAAM,gBAAgB;AACtB,MAAM,uBAAuB;AAC7B,MAAM,qBAAqB;AAC3B,MAAM,4BAA4B;AAYlC,MAAM,iBAAiBC,aAAM,cAAqC,IAAI;AAEtE,SAAS,aAAa;AACd,QAAA,UAAUC,aAAM,WAAW,cAAc;AAC/C,MAAI,CAAC,SAAS;AACN,UAAA,IAAI,MAAM,mDAAmD;AAAA,EAAA;AAG9D,SAAA;AACT;AAEA,MAAM,kBAAkBC,aAAM;AAAA,EAQ5B,CACE;AAAA,IACE,cAAc;AAAA,IACd,MAAM;AAAA,IACN,cAAc;AAAA,IACd;AAAA,IACA;AAAA,IACA;AAAA,IACA,GAAG;AAAA,KAEL,QACG;AACH,UAAM,WAAW,YAAY;AAC7B,UAAM,CAAC,YAAY,aAAa,IAAIL,aAAAA,SAAe,KAAK;AAIxD,UAAM,CAAC,OAAO,QAAQ,IAAIA,aAAAA,SAAe,WAAW;AACpD,UAAM,OAAO,YAAY;AACzB,UAAM,UAAUM,aAAM;AAAA,MACpB,CAAC,UAAmD;AAClD,YAAI,aAAa;AACR,iBAAA;AAAA,YACL,OAAO,UAAU,aAAa,MAAM,IAAI,IAAI;AAAA;AAAA,QAC9C;AAGF,iBAAS,KAAK;AAGd,iBAAS,SAAS,GAAG,mBAAmB,IAAI,IAAI,qBAAqB,sBAAsB;AAAA,MAC7F;AAAA,MACA,CAAC,aAAa,IAAI;AAAA,IACpB;AAGM,UAAA,gBAAgBA,aAAAA,YAAkB,MAAM;AACrC,aAAA,WACH,cAAc,CAACC,UAAS,CAACA,KAAI,IAC7B,QAAQ,CAACA,UAAS,CAACA,KAAI;AAAA,IAC1B,GAAA,CAAC,UAAU,SAAS,aAAa,CAAC;AAGrCN,iBAAAA,UAAgB,MAAM;AACd,YAAA,gBAAgB,CAAC,UAAyB;AAC9C,YACE,MAAM,QAAQ,8BACb,MAAM,WAAW,MAAM,UACxB;AACA,gBAAM,eAAe;AACP,wBAAA;AAAA,QAAA;AAAA,MAElB;AAEO,aAAA,iBAAiB,WAAW,aAAa;AAChD,aAAO,MAAM,OAAO,oBAAoB,WAAW,aAAa;AAAA,IAAA,GAC/D,CAAC,aAAa,CAAC;AAIZ,UAAA,QAAQ,OAAO,aAAa;AAElC,UAAM,eAAeO,aAAM;AAAA,MACzB,OAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MAAA;AAAA,MAEF,CAAC,OAAO,MAAM,SAAS,UAAU,YAAY,eAAe,aAAa;AAAA,IAC3E;AAGE,WAAAN,kCAAA,IAAC,eAAe,UAAf,EAAwB,OAAO,cAC9B,UAAAA,kCAAAA,IAAC,iBAAgB,EAAA,eAAe,GAC9B,UAAAA,kCAAA;AAAA,MAAC;AAAA,MAAA;AAAA,QACC,OACE;AAAA,UACE,mBAAmB;AAAA,UACnB,wBAAwB;AAAA,UACxB,GAAG;AAAA,QACL;AAAA,QAEF,WAAW;AAAA,UACT;AAAA,UACA;AAAA,QACF;AAAA,QACA;AAAA,QACC,GAAG;AAAA,QAEH;AAAA,MAAA;AAAA,OAEL,EACF,CAAA;AAAA,EAAA;AAGN;AACA,gBAAgB,cAAc;AAE9B,MAAM,UAAUG,aAAM;AAAA,EAQpB,CACE;AAAA,IACE,OAAO;AAAA,IACP,UAAU;AAAA,IACV,cAAc;AAAA,IACd;AAAA,IACA;AAAA,IACA,GAAG;AAAA,KAEL,QACG;AACH,UAAM,EAAE,UAAU,OAAO,YAAY,cAAA,IAAkB,WAAW;AAElE,QAAI,gBAAgB,QAAQ;AAExB,aAAAH,kCAAA;AAAA,QAAC;AAAA,QAAA;AAAA,UACC,WAAW;AAAA,YACT;AAAA,YACA;AAAA,UACF;AAAA,UACA;AAAA,UACC,GAAG;AAAA,UAEH;AAAA,QAAA;AAAA,MACH;AAAA,IAAA;AAIJ,QAAI,UAAU;AACZ,mDACG,OAAM,EAAA,MAAM,YAAY,cAAc,eAAgB,GAAG,OACxD,UAAAA,kCAAA;AAAA,QAAC;AAAA,QAAA;AAAA,UACC,gBAAa;AAAA,UACb,eAAY;AAAA,UACZ,WAAU;AAAA,UACV,OACE;AAAA,YACE,mBAAmB;AAAA,UACrB;AAAA,UAEF;AAAA,UAEA,UAACA,kCAAA,IAAA,OAAA,EAAI,WAAU,+BAA+B,SAAS,CAAA;AAAA,QAAA;AAAA,MAAA,GAE3D;AAAA,IAAA;AAKF,WAAAO,kCAAA;AAAA,MAAC;AAAA,MAAA;AAAA,QACC;AAAA,QACA,WAAU;AAAA,QACV,cAAY;AAAA,QACZ,oBAAkB,UAAU,cAAc,cAAc;AAAA,QACxD,gBAAc;AAAA,QACd,aAAW;AAAA,QAGX,UAAA;AAAA,UAAAP,kCAAA;AAAA,YAAC;AAAA,YAAA;AAAA,cACC,WAAW;AAAA,gBACT;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA,YAAY,cAAc,YAAY,UAClC,yFACA;AAAA,cAAA;AAAA,YACN;AAAA,UACF;AAAA,UACAA,kCAAA;AAAA,YAAC;AAAA,YAAA;AAAA,cACC,WAAW;AAAA,gBACT;AAAA,gBACA,SAAS,SACL,mFACA;AAAA;AAAA,gBAEJ,YAAY,cAAc,YAAY,UAClC,kGACA;AAAA,gBACJ;AAAA,cACF;AAAA,cACC,GAAG;AAAA,cAEJ,UAAAA,kCAAA;AAAA,gBAAC;AAAA,gBAAA;AAAA,kBACC,gBAAa;AAAA,kBACb,WAAU;AAAA,kBAET;AAAA,gBAAA;AAAA,cAAA;AAAA,YACH;AAAA,UAAA;AAAA,QACF;AAAA,MAAA;AAAA,IACF;AAAA,EAAA;AAGN;AACA,QAAQ,cAAc;AAEtB,MAAM,iBAAiBG,aAAM,WAG3B,CAAC,EAAE,WAAW,SAAS,GAAG,MAAM,GAAG,QAAQ;AACrC,QAAA,EAAE,cAAc,IAAI,WAAW;AAGnC,SAAAI,kCAAA;AAAA,IAAC;AAAA,IAAA;AAAA,MACC;AAAA,MACA,gBAAa;AAAA,MACb,SAAQ;AAAA,MACR,MAAK;AAAA,MACL,WAAW,GAAG,WAAW,SAAS;AAAA,MAClC,SAAS,CAAC,UAAU;AAClB,2CAAU;AACI,sBAAA;AAAA,MAChB;AAAA,MACC,GAAG;AAAA,MAEJ,UAAA;AAAA,QAAAP,kCAAA,IAAC,WAAU,EAAA;AAAA,QACVA,kCAAA,IAAA,QAAA,EAAK,WAAU,WAAU,UAAc,iBAAA,CAAA;AAAA,MAAA;AAAA,IAAA;AAAA,EAC1C;AAEJ,CAAC;AACD,eAAe,cAAc;AAE7B,MAAM,cAAcG,aAGlB,WAAA,CAAC,EAAE,WAAW,GAAG,MAAM,GAAG,QAAQ;AAC5B,QAAA,EAAE,cAAc,IAAI,WAAW;AAGnC,SAAAH,kCAAA;AAAA,IAAC;AAAA,IAAA;AAAA,MACC;AAAA,MACA,gBAAa;AAAA,MACb,cAAW;AAAA,MACX,UAAU;AAAA,MACV,SAAS;AAAA,MACT,OAAM;AAAA,MACN,WAAW;AAAA,QACT;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACC,GAAG;AAAA,IAAA;AAAA,EACN;AAEJ,CAAC;AACD,YAAY,cAAc;AAE1B,MAAM,eAAeG,aAGnB,WAAA,CAAC,EAAE,WAAW,GAAG,MAAM,GAAG,QAAQ;AAEhC,SAAAH,kCAAA;AAAA,IAAC;AAAA,IAAA;AAAA,MACC;AAAA,MACA,WAAW;AAAA,QACT;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACC,GAAG;AAAA,IAAA;AAAA,EACN;AAEJ,CAAC;AACD,aAAa,cAAc;AAE3B,MAAM,eAAeG,aAGnB,WAAA,CAAC,EAAE,WAAW,GAAG,MAAM,GAAG,QAAQ;AAEhC,SAAAH,kCAAA;AAAA,IAAC;AAAA,IAAA;AAAA,MACC;AAAA,MACA,gBAAa;AAAA,MACb,WAAW;AAAA,QACT;AAAA,QACA;AAAA,MACF;AAAA,MACC,GAAG;AAAA,IAAA;AAAA,EACN;AAEJ,CAAC;AACD,aAAa,cAAc;AAE3B,MAAM,gBAAgBG,aAGpB,WAAA,CAAC,EAAE,WAAW,GAAG,MAAM,GAAG,QAAQ;AAEhC,SAAAH,kCAAA;AAAA,IAAC;AAAA,IAAA;AAAA,MACC;AAAA,MACA,gBAAa;AAAA,MACb,WAAW,GAAG,2BAA2B,SAAS;AAAA,MACjD,GAAG;AAAA,IAAA;AAAA,EACN;AAEJ,CAAC;AACD,cAAc,cAAc;AAE5B,MAAM,gBAAgBG,aAGpB,WAAA,CAAC,EAAE,WAAW,GAAG,MAAM,GAAG,QAAQ;AAEhC,SAAAH,kCAAA;AAAA,IAAC;AAAA,IAAA;AAAA,MACC;AAAA,MACA,gBAAa;AAAA,MACb,WAAW,GAAG,2BAA2B,SAAS;AAAA,MACjD,GAAG;AAAA,IAAA;AAAA,EACN;AAEJ,CAAC;AACD,cAAc,cAAc;AAE5B,MAAM,mBAAmBG,aAGvB,WAAA,CAAC,EAAE,WAAW,GAAG,MAAM,GAAG,QAAQ;AAEhC,SAAAH,kCAAA;AAAA,IAAC;AAAA,IAAA;AAAA,MACC;AAAA,MACA,gBAAa;AAAA,MACb,WAAW,GAAG,iCAAiC,SAAS;AAAA,MACvD,GAAG;AAAA,IAAA;AAAA,EACN;AAEJ,CAAC;AACD,iBAAiB,cAAc;AAE/B,MAAM,iBAAiBG,aAGrB,WAAA,CAAC,EAAE,WAAW,GAAG,MAAM,GAAG,QAAQ;AAEhC,SAAAH,kCAAA;AAAA,IAAC;AAAA,IAAA;AAAA,MACC;AAAA,MACA,gBAAa;AAAA,MACb,WAAW;AAAA,QACT;AAAA,QACA;AAAA,MACF;AAAA,MACC,GAAG;AAAA,IAAA;AAAA,EACN;AAEJ,CAAC;AACD,eAAe,cAAc;AAE7B,MAAM,eAAeG,aAGnB,WAAA,CAAC,EAAE,WAAW,GAAG,MAAM,GAAG,QAAQ;AAEhC,SAAAH,kCAAA;AAAA,IAAC;AAAA,IAAA;AAAA,MACC;AAAA,MACA,gBAAa;AAAA,MACb,WAAW,GAAG,6CAA6C,SAAS;AAAA,MACnE,GAAG;AAAA,IAAA;AAAA,EACN;AAEJ,CAAC;AACD,aAAa,cAAc;AAE3B,MAAM,oBAAoBG,aAAAA,WAGxB,CAAC,EAAE,WAAW,UAAU,OAAO,GAAG,MAAM,GAAG,QAAQ;AAC7C,QAAA,OAAO,UAAU,OAAO;AAG5B,SAAAH,kCAAA;AAAA,IAAC;AAAA,IAAA;AAAA,MACC;AAAA,MACA,gBAAa;AAAA,MACb,WAAW;AAAA,QACT;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACC,GAAG;AAAA,IAAA;AAAA,EACN;AAEJ,CAAC;AACD,kBAAkB,cAAc;AAEhC,MAAM,qBAAqBG,aAAAA,WAGzB,CAAC,EAAE,WAAW,UAAU,OAAO,GAAG,MAAM,GAAG,QAAQ;AAC7C,QAAA,OAAO,UAAU,OAAO;AAG5B,SAAAH,kCAAA;AAAA,IAAC;AAAA,IAAA;AAAA,MACC;AAAA,MACA,gBAAa;AAAA,MACb,WAAW;AAAA,QACT;AAAA;AAAA,QAEA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACC,GAAG;AAAA,IAAA;AAAA,EACN;AAEJ,CAAC;AACD,mBAAmB,cAAc;AAEjC,MAAM,sBAAsBG,aAG1B,WAAA,CAAC,EAAE,WAAW,GAAG,MAAM,GAAG,QAC1BH,kCAAA;AAAA,EAAC;AAAA,EAAA;AAAA,IACC;AAAA,IACA,gBAAa;AAAA,IACb,WAAW,GAAG,kBAAkB,SAAS;AAAA,IACxC,GAAG;AAAA,EAAA;AACN,CACD;AACD,oBAAoB,cAAc;AAElC,MAAM,cAAcG,aAGlB,WAAA,CAAC,EAAE,WAAW,GAAG,MAAM,GAAG,QAC1BH,kCAAA;AAAA,EAAC;AAAA,EAAA;AAAA,IACC;AAAA,IACA,gBAAa;AAAA,IACb,WAAW,GAAG,sCAAsC,SAAS;AAAA,IAC5D,GAAG;AAAA,EAAA;AACN,CACD;AACD,YAAY,cAAc;AAE1B,MAAM,kBAAkBG,aAGtB,WAAA,CAAC,EAAE,WAAW,GAAG,MAAM,GAAG,QAC1BH,kCAAA;AAAA,EAAC;AAAA,EAAA;AAAA,IACC;AAAA,IACA,gBAAa;AAAA,IACb,WAAW,GAAG,4BAA4B,SAAS;AAAA,IAClD,GAAG;AAAA,EAAA;AACN,CACD;AACD,gBAAgB,cAAc;AAE9B,MAAM,4BAA4B;AAAA,EAChC;AAAA,EACA;AAAA,IACE,UAAU;AAAA,MACR,SAAS;AAAA,QACP,SAAS;AAAA,QACT,SACE;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACJ,SAAS;AAAA,QACT,IAAI;AAAA,QACJ,IAAI;AAAA,MAAA;AAAA,IAER;AAAA,IACA,iBAAiB;AAAA,MACf,SAAS;AAAA,MACT,MAAM;AAAA,IAAA;AAAA,EACR;AAEJ;AAEA,MAAM,oBAAoBG,aAAM;AAAA,EAQ9B,CACE;AAAA,IACE,UAAU;AAAA,IACV,WAAW;AAAA,IACX,UAAU;AAAA,IACV,OAAO;AAAA,IACP;AAAA,IACA;AAAA,IACA,GAAG;AAAA,KAEL,QACG;AACG,UAAA,OAAO,UAAU,OAAO;AAC9B,UAAM,EAAE,UAAU,MAAM,IAAI,WAAW;AAEvC,UAAM,SACJH,kCAAA;AAAA,MAAC;AAAA,MAAA;AAAA,QACC;AAAA,QACA,gBAAa;AAAA,QACb,aAAW;AAAA,QACX,eAAa;AAAA,QACb,WAAW,GAAG,0BAA0B,EAAE,SAAS,KAAK,CAAC,GAAG,SAAS;AAAA,QACpE,GAAG;AAAA,MAAA;AAAA,IACN;AAGF,QAAI,CAAC,SAAS;AACL,aAAA;AAAA,IAAA;AAGL,QAAA,OAAO,YAAY,UAAU;AACrB,gBAAA;AAAA,QACR,UAAU;AAAA,MACZ;AAAA,IAAA;AAGF,kDACG,SACC,EAAA,UAAA;AAAA,MAACA,kCAAA,IAAA,gBAAA,EAAe,SAAO,MAAE,UAAO,QAAA;AAAA,MAChCA,kCAAA;AAAA,QAAC;AAAA,QAAA;AAAA,UACC,MAAK;AAAA,UACL,OAAM;AAAA,UACN,QAAQ,UAAU,eAAe;AAAA,UAChC,GAAG;AAAA,QAAA;AAAA,MAAA;AAAA,IACN,GACF;AAAA,EAAA;AAGN;AACA,kBAAkB,cAAc;AAEhC,MAAM,oBAAoBG,aAMxB,WAAA,CAAC,EAAE,WAAW,UAAU,OAAO,cAAc,OAAO,GAAG,MAAA,GAAS,QAAQ;AAClE,QAAA,OAAO,UAAU,OAAO;AAG5B,SAAAH,kCAAA;AAAA,IAAC;AAAA,IAAA;AAAA,MACC;AAAA,MACA,gBAAa;AAAA,MACb,WAAW;AAAA,QACT;AAAA;AAAA,QAEA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,eACA;AAAA,QACA;AAAA,MACF;AAAA,MACC,GAAG;AAAA,IAAA;AAAA,EACN;AAEJ,CAAC;AACD,kBAAkB,cAAc;AAEhC,MAAM,mBAAmBG,aAGvB,WAAA,CAAC,EAAE,WAAW,GAAG,MAAM,GAAG,QAC1BH,kCAAA;AAAA,EAAC;AAAA,EAAA;AAAA,IACC;AAAA,IACA,gBAAa;AAAA,IACb,WAAW;AAAA,MACT;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,IACC,GAAG;AAAA,EAAA;AACN,CACD;AACD,iBAAiB,cAAc;AAE/B,MAAM,sBAAsBG,aAAAA,WAK1B,CAAC,EAAE,WAAW,WAAW,OAAO,GAAG,MAAM,GAAG,QAAQ;AAE9C,QAAA,QAAQG,aAAAA,QAAc,MAAM;AACzB,WAAA,GAAG,KAAK,MAAM,KAAK,WAAW,EAAE,IAAI,EAAE;AAAA,EAC/C,GAAG,EAAE;AAGH,SAAAC,kCAAA;AAAA,IAAC;AAAA,IAAA;AAAA,MACC;AAAA,MACA,gBAAa;AAAA,MACb,WAAW,GAAG,+CAA+C,SAAS;AAAA,MACrE,GAAG;AAAA,MAEH,UAAA;AAAA,QACC,YAAAP,kCAAA;AAAA,UAAC;AAAA,UAAA;AAAA,YACC,WAAU;AAAA,YACV,gBAAa;AAAA,UAAA;AAAA,QACf;AAAA,QAEFA,kCAAA;AAAA,UAAC;AAAA,UAAA;AAAA,YACC,WAAU;AAAA,YACV,gBAAa;AAAA,YACb,OACE;AAAA,cACE,oBAAoB;AAAA,YAAA;AAAA,UACtB;AAAA,QAAA;AAAA,MAEJ;AAAA,IAAA;AAAA,EACF;AAEJ,CAAC;AACD,oBAAoB,cAAc;AAElC,MAAM,iBAAiBG,aAGrB,WAAA,CAAC,EAAE,WAAW,GAAG,MAAM,GAAG,QAC1BH,kCAAA;AAAA,EAAC;AAAA,EAAA;AAAA,IACC;AAAA,IACA,gBAAa;AAAA,IACb,WAAW;AAAA,MACT;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,IACC,GAAG;AAAA,EAAA;AACN,CACD;AACD,eAAe,cAAc;AAE7B,MAAM,qBAAqBG,aAGzB,WAAA,CAAC,EAAE,GAAG,MAAA,GAAS,QAASH,kCAAA,IAAA,MAAA,EAAG,KAAW,GAAG,MAAO,CAAA,CAAE;AACpD,mBAAmB,cAAc;AAEjC,MAAM,uBAAuBG,aAO3B,WAAA,CAAC,EAAE,UAAU,OAAO,OAAO,MAAM,UAAU,WAAW,GAAG,MAAA,GAAS,QAAQ;AACpE,QAAA,OAAO,UAAU,OAAO;AAG5B,SAAAH,kCAAA;AAAA,IAAC;AAAA,IAAA;AAAA,MACC;AAAA,MACA,gBAAa;AAAA,MACb,aAAW;AAAA,MACX,eAAa;AAAA,MACb,WAAW;AAAA,QACT;AAAA,QACA;AAAA,QACA,SAAS,QAAQ;AAAA,QACjB,SAAS,QAAQ;AAAA,QACjB;AAAA,QACA;AAAA,MACF;AAAA,MACC,GAAG;AAAA,IAAA;AAAA,EACN;AAEJ,CAAC;AACD,qBAAqB,cAAc;ACntBnC,IAAI,YAAY;AAChB,IAAI,CAAC,0BAA0B,sBAAsB,IAAI,mBAAmB,WAAW;AAAA,EACrF;AACF,CAAC;AACD,IAAI,iBAAiB,kBAAmB;AACxC,IAAIQ,gBAAc,CAAC,UAAU;AAC3B,QAAM,EAAE,oBAAoB,GAAG,iBAAgB,IAAK;AACpD,QAAM,cAAc,eAAe,kBAAkB;AACrD,SAAuBR,kCAAG,IAACS,MAAsB,EAAE,GAAG,aAAa,GAAG,kBAAkB,OAAO,MAAM;AACvG;AACAD,cAAY,cAAc;AAC1B,IAAI,eAAe;AACnB,IAAIE,uBAAqBP,aAAgB;AAAA,EACvC,CAAC,OAAO,iBAAiB;AACvB,UAAM,EAAE,oBAAoB,GAAG,aAAY,IAAK;AAChD,UAAM,cAAc,eAAe,kBAAkB;AACrD,WAAuBH,kCAAG,IAACW,SAAyB,EAAE,GAAG,aAAa,GAAG,cAAc,KAAK,cAAc;AAAA,EAC9G;AACA;AACAD,qBAAmB,cAAc;AACjC,IAAI,cAAc;AAClB,IAAIE,sBAAoB,CAAC,UAAU;AACjC,QAAM,EAAE,oBAAoB,GAAG,YAAW,IAAK;AAC/C,QAAM,cAAc,eAAe,kBAAkB;AACrD,SAAuBZ,kCAAAA,IAAIa,QAAwB,EAAE,GAAG,aAAa,GAAG,YAAW,CAAE;AACvF;AACAD,oBAAkB,cAAc;AAChC,IAAI,eAAe;AACnB,IAAIE,uBAAqBX,aAAgB;AAAA,EACvC,CAAC,OAAO,iBAAiB;AACvB,UAAM,EAAE,oBAAoB,GAAG,aAAY,IAAK;AAChD,UAAM,cAAc,eAAe,kBAAkB;AACrD,WAAuBH,kCAAG,IAACe,SAAyB,EAAE,GAAG,aAAa,GAAG,cAAc,KAAK,cAAc;AAAA,EAC9G;AACA;AACAD,qBAAmB,cAAc;AACjC,IAAI,eAAe;AACnB,IAAI,CAAC,4BAA4B,4BAA4B,IAAI,yBAAyB,YAAY;AACtG,IAAIE,uBAAqBb,aAAgB;AAAA,EACvC,CAAC,OAAO,iBAAiB;AACvB,UAAM,EAAE,oBAAoB,UAAU,GAAG,aAAc,IAAG;AAC1D,UAAM,cAAc,eAAe,kBAAkB;AACrD,UAAM,aAAac,aAAY,OAAC,IAAI;AACpC,UAAM,eAAe,gBAAgB,cAAc,UAAU;AAC7D,UAAM,YAAYA,aAAY,OAAC,IAAI;AACnC,WAAuBjB,kCAAG;AAAA,MACxBkB;AAAAA,MACA;AAAA,QACE,aAAa;AAAA,QACb,WAAW;AAAA,QACX,UAAU;AAAA,QACV,UAA0BlB,kCAAG,IAAC,4BAA4B,EAAE,OAAO,oBAAoB,WAAW,UAA0BO,kCAAI;AAAA,UAC9HY;AAAAA,UACA;AAAA,YACE,MAAM;AAAA,YACN,GAAG;AAAA,YACH,GAAG;AAAA,YACH,KAAK;AAAA,YACL,iBAAiB,qBAAqB,aAAa,iBAAiB,CAAC,UAAU;;AAC7E,oBAAM,eAAgB;AACtB,8BAAU,YAAV,mBAAmB,MAAM,EAAE,eAAe,KAAI;AAAA,YAC5D,CAAa;AAAA,YACD,sBAAsB,CAAC,UAAU,MAAM,eAAgB;AAAA,YACvD,mBAAmB,CAAC,UAAU,MAAM,eAAgB;AAAA,YACpD,UAAU;AAAA,cACQnB,sCAAI,WAAW,EAAE,UAAU;AAAA,cAC3BA,sCAAI,oBAAoB,EAAE,WAAY,CAAA;AAAA,YACpE;AAAA,UACA;AAAA,QACA,EAAW,CAAA;AAAA,MACX;AAAA,IACK;AAAA,EACL;AACA;AACAgB,qBAAmB,cAAc;AACjC,IAAI,aAAa;AACjB,IAAII,qBAAmBjB,aAAgB;AAAA,EACrC,CAAC,OAAO,iBAAiB;AACvB,UAAM,EAAE,oBAAoB,GAAG,WAAU,IAAK;AAC9C,UAAM,cAAc,eAAe,kBAAkB;AACrD,WAAuBH,kCAAG,IAACqB,OAAuB,EAAE,GAAG,aAAa,GAAG,YAAY,KAAK,cAAc;AAAA,EAC1G;AACA;AACAD,mBAAiB,cAAc;AAC/B,IAAI,mBAAmB;AACvB,IAAIE,2BAAyBnB,aAAgB,WAAC,CAAC,OAAO,iBAAiB;AACrE,QAAM,EAAE,oBAAoB,GAAG,iBAAgB,IAAK;AACpD,QAAM,cAAc,eAAe,kBAAkB;AACrD,SAAuBH,kCAAG,IAACuB,aAA6B,EAAE,GAAG,aAAa,GAAG,kBAAkB,KAAK,cAAc;AACpH,CAAC;AACDD,yBAAuB,cAAc;AACrC,IAAI,cAAc;AAClB,IAAIE,sBAAoBrB,aAAgB;AAAA,EACtC,CAAC,OAAO,iBAAiB;AACvB,UAAM,EAAE,oBAAoB,GAAG,YAAW,IAAK;AAC/C,UAAM,cAAc,eAAe,kBAAkB;AACrD,WAAuBH,kCAAG,IAACyB,OAAuB,EAAE,GAAG,aAAa,GAAG,aAAa,KAAK,cAAc;AAAA,EAC3G;AACA;AACAD,oBAAkB,cAAc;AAChC,IAAI,cAAc;AAClB,IAAIE,sBAAoBvB,aAAgB;AAAA,EACtC,CAAC,OAAO,iBAAiB;AACvB,UAAM,EAAE,oBAAoB,GAAG,YAAW,IAAK;AAC/C,UAAM,EAAE,UAAW,IAAG,6BAA6B,aAAa,kBAAkB;AAClF,UAAM,cAAc,eAAe,kBAAkB;AACrD,UAAM,MAAM,gBAAgB,cAAc,SAAS;AACnD,WAAuBH,kCAAAA,IAAIyB,OAAuB,EAAE,GAAG,aAAa,GAAG,aAAa,KAAK;AAAA,EAC7F;AACA;AACAC,oBAAkB,cAAc;AAChC,IAAI,qBAAqB,CAAC,EAAE,iBAAiB;AAC3C,QAAM,UAAU,KAAK,YAAY;AAAA;AAAA,qCAEE,YAAY,qBAAqB,gBAAgB;AAAA;AAAA,4JAEsE,YAAY;AAAA;AAAA;AAGtK3B,eAAAA,UAAgB,MAAM;;AACpB,UAAM,iBAAiB,SAAS;AAAA,OAC9B,gBAAW,YAAX,mBAAoB,aAAa;AAAA,IAClC;AACD,QAAI,CAAC,eAAgB,SAAQ,KAAK,OAAO;AAAA,EAC7C,GAAK,CAAC,SAAS,UAAU,CAAC;AACxB,SAAO;AACT;AACA,IAAI,QAAQS;AACZ,IAAI,WAAWE;AACf,IAAI,UAAUE;AACd,IAAI,WAAWE;AACf,IAAI,WAAWE;AACf,IAAI,SAASQ;AACb,IAAI,SAASE;AACb,IAAI,SAASN;AACb,IAAI,eAAeE;AC5InB,MAAM,cAAcK;AAEpB,MAAM,qBAAqBC;AAE3B,MAAM,oBAAoBC;AAE1B,MAAM,qBAAqB1B,aAGzB,WAAA,CAAC,EAAE,WAAW,GAAG,MAAM,GAAG,QAC1BH,kCAAA;AAAA,EAAC8B;AAAAA,EAAA;AAAA,IACC,WAAW;AAAA,MACT;AAAA,MACA;AAAA,IACF;AAAA,IACC,GAAG;AAAA,IACJ;AAAA,EAAA;AACF,CACD;AACD,mBAAmB,cAAcA,SAA6B;AAE9D,MAAM,qBAAqB3B,aAAM,WAG/B,CAAC,EAAE,WAAW,GAAG,SAAS,QAC1BI,kCAAA,KAAC,mBACC,EAAA,UAAA;AAAA,EAAAP,kCAAA,IAAC,oBAAmB,EAAA;AAAA,EACpBA,kCAAA;AAAA,IAAC+B;AAAAA,IAAA;AAAA,MACC;AAAA,MACA,WAAW;AAAA,QACT;AAAA,QACA;AAAA,MACF;AAAA,MACC,GAAG;AAAA,IAAA;AAAA,EAAA;AACN,EAAA,CACF,CACD;AACD,mBAAmB,cAAcA,SAA6B;AAE9D,MAAM,oBAAoB,CAAC;AAAA,EACzB;AAAA,EACA,GAAG;AACL,MACE/B,kCAAA;AAAA,EAAC;AAAA,EAAA;AAAA,IACC,WAAW;AAAA,MACT;AAAA,MACA;AAAA,IACF;AAAA,IACC,GAAG;AAAA,EAAA;AACN;AAEF,kBAAkB,cAAc;AAEhC,MAAM,oBAAoB,CAAC;AAAA,EACzB;AAAA,EACA,GAAG;AACL,MACEA,kCAAA;AAAA,EAAC;AAAA,EAAA;AAAA,IACC,WAAW;AAAA,MACT;AAAA,MACA;AAAA,IACF;AAAA,IACC,GAAG;AAAA,EAAA;AACN;AAEF,kBAAkB,cAAc;AAEhC,MAAM,mBAAmBG,aAGvB,WAAA,CAAC,EAAE,WAAW,GAAG,MAAM,GAAG,QAC1BH,kCAAA;AAAA,EAACgC;AAAAA,EAAA;AAAA,IACC;AAAA,IACA,WAAW,GAAG,yBAAyB,SAAS;AAAA,IAC/C,GAAG;AAAA,EAAA;AACN,CACD;AACD,iBAAiB,cAAcA,OAA2B;AAE1D,MAAM,yBAAyB7B,aAG7B,WAAA,CAAC,EAAE,WAAW,GAAG,MAAM,GAAG,QAC1BH,kCAAA;AAAA,EAACiC;AAAAA,EAAA;AAAA,IACC;AAAA,IACA,WAAW,GAAG,iCAAiC,SAAS;AAAA,IACvD,GAAG;AAAA,EAAA;AACN,CACD;AACD,uBAAuB,cACrBA,aAAiC;AAEnC,MAAM,oBAAoB9B,aAGxB,WAAA,CAAC,EAAE,WAAW,GAAG,MAAM,GAAG,QAC1BH,kCAAA;AAAA,EAACkC;AAAAA,EAAA;AAAA,IACC;AAAA,IACA,WAAW,GAAG,eAAe,GAAG,SAAS;AAAA,IACxC,GAAG;AAAA,EAAA;AACN,CACD;AACD,kBAAkB,cAAcA,OAA4B;AAE5D,MAAM,oBAAoB/B,aAGxB,WAAA,CAAC,EAAE,WAAW,GAAG,MAAM,GAAG,QAC1BH,kCAAA;AAAA,EAACmC;AAAAA,EAAA;AAAA,IACC;AAAA,IACA,WAAW;AAAA,MACT,eAAe,EAAE,SAAS,WAAW;AAAA,MACrC;AAAA,MACA;AAAA,IACF;AAAA,IACC,GAAG;AAAA,EAAA;AACN,CACD;AACD,kBAAkB,cAAcA,OAA4B;ACnC5D,SAAwBC,OAAO;;AAC3B,QAAM;AAAA,IAAEC;AAAAA,IAAiBC;AAAAA,EAAY,IAAIC,cAA0B;AAC7D,QAAA;AAAA,IAAEC;AAAAA,EAAS,IAAIC,YAAY;AACjC,QAAMC,SAASC,UAAU;AACzB,QAAMC,gBAAgBJ,SAASK,MAAM,GAAG,EAAE,CAAC;AAE3C,QAAM,CAACC,uBAAuBC,wBAAwB,IAAIC,aAAAA,SAAS,KAAK;AACxE,QAAM,CAACC,qBAAqBC,sBAAsB,IAAIF,aAAAA,SAAS,KAAK;AACpE,QAAM,CAACG,YAAYC,aAAa,IAAIJ,aAAAA,SAAS,KAAK;AAClD,QAAM,CAACK,eAAeC,gBAAgB,IAAIN,aAAAA,SAAS,KAAK;AAExDO,eAAAA,UAAU,MAAM;AACZ,QAAIlB,iBAAiB;AACQU,+BAAAV,gBAAgBmB,SAAS,YAAY,CAAC;AACxCN,6BAAAb,gBAAgBmB,SAAS,gBAAgB,CAAC;AAChDF,uBAAAjB,gBAAgBmB,SAAS,UAAU,CAAC;AACvCJ,oBAAAf,gBAAgBmB,SAAS,UAAU,CAAC;AAAA,IACtD;AAAA,EACJ,GAAG,CAACnB,eAAe,CAAC;AAEpB,QAAMoB,eAAeA,MAAMf,OAAO,MAAM;AAAA,IAAEgB,QAAQ;AAAA,EAAO,CAAC;AAE1D,MAAIlB,aAAa,SAAS;AACtB,WAAQxC,kCAAAA,IAAA2D,UAAA;AAAA,MAASC,IAAG;AAAA,MAAkBC,SAAO;AAAA,IAAC,CAAA;AAAA,EAClD;AAEA,SACK7D,kCAAAA,IAAA8D,iBAAA;AAAA,IACGC,UAACxD,kCAAA,KAAA,OAAA;AAAA,MAAIyD,WAAU;AAAA,MACXD,UAAA,CAACxD,kCAAA,KAAA0D,SAAA;AAAA,QAAQD,WAAU;AAAA,QACfD,UAAA,CAAA/D,kCAAA,IAACkE;UAAcF,WAAU;AAAA,UACrBD,UAACxD,kCAAA,KAAA,OAAA;AAAA,YAAIyD,WAAU;AAAA,YACXD,UAAA,CAAA/D,kCAAA,IAAC;cAAImE,KAAI;AAAA,cAAiBC,KAAI;AAAA,cAAYJ,WAAU;AAAA,YAAc,CAAA,yCACjE,OAAI;AAAA,cAAAA,WAAU;AAAA,cACXD,UAACxD,kCAAA,KAAA,OAAA;AAAA,gBAAIyD,WAAU;AAAA,gBACXD,UAAA,CAAC/D,kCAAA,IAAA,OAAA;AAAA,kBAAIgE,WAAU;AAAA,kBACVD,YAAazB,4DAAAA,gBAAAA,mBAAa+B,iBAAb/B,mBAA4B,OAA5BA,mBAAgCgC,kBAAiB;AAAA,gBACnE,CAAA,yCACC,OAAI;AAAA,kBAAAN,WAAU;AAAA,kBACVD,YAAazB,gDAAAA,gBAAAA,mBAAa+B,iBAAgB;AAAA,gBAC/C,CAAA,CAAA;AAAA,cACJ,CAAA;AAAA,YACJ,CAAA,CAAA;AAAA,UACJ,CAAA;AAAA,QACJ,CAAA,yCAECE,gBAAe;AAAA,UAAAP,WAAU;AAAA,UACtBD,UAACxD,kCAAA,KAAAiE,aAAA;AAAA,YAAYR,WAAU;AAAA,YAClBD,UAAA,CAAAV,iBACIrD,kCAAA,IAAAyE,iBAAA;AAAA,cACGV,UAAC/D,kCAAA,IAAA0E,MAAA;AAAA,gBAAKd,IAAG;AAAA,gBACLG,UAAAxD,kCAAA,KAACoE,mBAAA;AAAA,kBACGC,UAAUhC,kBAAkB;AAAA,kBAC5BoB,WAAW,0EAA0EpB,kBAAkB,cACjG,4CACA,EACF;AAAA,kBAEJmB,UAAA,CAAC/D,kCAAA,IAAA6E,OAAA;AAAA,oBAASb,WAAU;AAAA,kBAAe,CAAA,GAAE,WAAA;AAAA,gBAEzC,CAAA;AAAA,cACJ,CAAA;AAAA,aACJ,GAGHX,iBACIrD,kCAAA,IAAAyE,iBAAA;AAAA,cACGV,UAAC/D,kCAAA,IAAA0E,MAAA;AAAA,gBAAKd,IAAG;AAAA,gBACLG,UAAAxD,kCAAA,KAACoE,mBAAA;AAAA,kBACGC,UAAUhC,kBAAkB;AAAA,kBAC5BoB,WAAW,0EAA0EpB,kBAAkB,cACjG,4CACA,EACF;AAAA,kBAEJmB,UAAA,CAAC/D,kCAAA,IAAA8E,SAAA;AAAA,oBAAQd,WAAU;AAAA,kBAAe,CAAA,GAAE,cAAA;AAAA,gBAExC,CAAA;AAAA,cACJ,CAAA;AAAA,aACJ,GAGHX,iBACIrD,kCAAA,IAAAyE,iBAAA;AAAA,cACGV,UAAC/D,kCAAA,IAAA0E,MAAA;AAAA,gBAAKd,IAAG;AAAA,gBACLG,UAAAxD,kCAAA,KAACoE,mBAAA;AAAA,kBACGC,UAAUhC,kBAAkB;AAAA,kBAC5BoB,WAAW,0EAA0EpB,kBAAkB,YACjG,4CACA,EACF;AAAA,kBAEJmB,UAAA,CAAC/D,kCAAA,IAAA+E,OAAA;AAAA,oBAAMf,WAAU;AAAA,kBAAe,CAAA,GAAE,UAAA;AAAA,gBAEtC,CAAA;AAAA,cACJ,CAAA;AAAA,aACJ,GAGHX,iBACIrD,kCAAA,IAAAyE,iBAAA;AAAA,cACGV,UAAC/D,kCAAA,IAAA0E,MAAA;AAAA,gBAAKd,IAAG;AAAA,gBACLG,UAAAxD,kCAAA,KAACoE,mBAAA;AAAA,kBACGC,UAAUhC,kBAAkB;AAAA,kBAC5BoB,WAAW,0EAA0EpB,kBAAkB,YACjG,4CACA,EACF;AAAA,kBAEJmB,UAAA,CAAC/D,kCAAA,IAAAgF,OAAA;AAAA,oBAAMhB,WAAU;AAAA,kBAAe,CAAA,GAAE,UAAA;AAAA,gBAEtC,CAAA;AAAA,cACJ,CAAA;AAAA,aACJ,GAGHlB,yBACI9C,kCAAA,IAAAyE,iBAAA;AAAA,cACGV,UAAC/D,kCAAA,IAAA0E,MAAA;AAAA,gBAAKd,IAAG;AAAA,gBACLG,UAAAxD,kCAAA,KAACoE,mBAAA;AAAA,kBACGC,UAAUhC,kBAAkB;AAAA,kBAC5BoB,WAAW,0EAA0EpB,kBAAkB,oBACjG,4CACA,EACF;AAAA,kBAEJmB,UAAA,CAAC/D,kCAAA,IAAAiF,eAAA;AAAA,oBAAkBjB,WAAU;AAAA,kBAAe,CAAA,GAAE,UAAA;AAAA,gBAElD,CAAA;AAAA,cACJ,CAAA;AAAA,YACJ,CAAA,IAGFlB,yBAAyBK,eACvBnD,kCAAAA,IAACyE;cACGV,UAAC/D,kCAAA,IAAA0E,MAAA;AAAA,gBAAKd,IAAG;AAAA,gBACLG,UAAAxD,kCAAA,KAACoE,mBAAA;AAAA,kBACGC,UAAUhC,kBAAkB;AAAA,kBAC5BoB,WAAW,0EAA0EpB,kBAAkB,uBACjG,4CACA,EACF;AAAA,kBAEJmB,UAAA,CAAC/D,kCAAA,IAAAkF,QAAA;AAAA,oBAAOlB,WAAU;AAAA,kBAAe,CAAA,GAAE,oBAAA;AAAA,gBAEvC,CAAA;AAAA,cACJ,CAAA;AAAA,aACJ,GAEHlB,yBACI9C,kCAAA,IAAAyE,iBAAA;AAAA,cACGV,UAAC/D,kCAAA,IAAA0E,MAAA;AAAA,gBAAKd,IAAG;AAAA,gBACLG,UAAAxD,kCAAA,KAACoE,mBAAA;AAAA,kBACGC,UAAUhC,kBAAkB;AAAA,kBAC5BoB,WAAW,0EAA0EpB,kBAAkB,uBACjG,4CACA,EACF;AAAA,kBAEJmB,UAAA,CAAC/D,kCAAA,IAAAkF,QAAA;AAAA,oBAAOlB,WAAU;AAAA,kBAAe,CAAA,GAAE,sBAAA;AAAA,gBAEvC,CAAA;AAAA,cACJ,CAAA;AAAA,aACJ,GAGHlB,yBACI9C,kCAAA,IAAAyE,iBAAA;AAAA,cACGV,UAAC/D,kCAAA,IAAA0E,MAAA;AAAA,gBAAKd,IAAG;AAAA,gBACLG,UAAAxD,kCAAA,KAACoE,mBAAA;AAAA,kBACGC,UAAUhC,kBAAkB;AAAA,kBAC5BoB,WAAW,0EAA0EpB,kBAAkB,gBACjG,4CACA,EACF;AAAA,kBAEJmB,UAAA,CAAC/D,kCAAA,IAAAmF,YAAA;AAAA,oBAAWnB,WAAU;AAAA,kBAAe,CAAA,GAAE,cAAA;AAAA,gBAE3C,CAAA;AAAA,cACJ,CAAA;AAAA,aACJ,GAGHX,iBACIrD,kCAAA,IAAAyE,iBAAA;AAAA,cACGV,UAAC/D,kCAAA,IAAA0E,MAAA;AAAA,gBAAKd,IAAG;AAAA,gBACLG,UAAAxD,kCAAA,KAACoE,mBAAA;AAAA,kBACGC,UAAUhC,kBAAkB;AAAA,kBAC5BoB,WAAW,0EAA0EpB,kBAAkB,oBACjG,4CACA,EACF;AAAA,kBAEJmB,UAAA,CAAC/D,kCAAA,IAAAoF,qBAAA;AAAA,oBAAoBpB,WAAU;AAAA,kBAAe,CAAA,GAAE,cAAA;AAAA,gBAEpD,CAAA;AAAA,cACJ,CAAA;AAAA,aACJ,GAGHX,iBACIrD,kCAAA,IAAAyE,iBAAA;AAAA,cACGV,UAAC/D,kCAAA,IAAA0E,MAAA;AAAA,gBAAKd,IAAG;AAAA,gBACLG,UAAAxD,kCAAA,KAACoE,mBAAA;AAAA,kBACGC,UAAUhC,kBAAkB;AAAA,kBAC5BoB,WAAW,0EAA0EpB,kBAAkB,qBACjG,4CACA,EACF;AAAA,kBAEJmB,UAAA,CAAC/D,kCAAA,IAAAqF,cAAA;AAAA,oBAAarB,WAAU;AAAA,kBAAe,CAAA,GAAE,iBAAA;AAAA,gBAE7C,CAAA;AAAA,cACJ,CAAA;AAAA,aACJ,GAEFX,wDACGoB,iBACG;AAAA,cAAAV,UAAA,CAACxD,kCAAA,KAAAoE,mBAAA;AAAA,gBAAkBX,WAAU;AAAA,gBACzBD,UAAA,CAAC/D,kCAAA,IAAAsF,WAAA;AAAA,kBAAUtB,WAAU;AAAA,gBAAe,CAAA,GAAE,iBAAA;AAAA,cAE1C,CAAA,0CACCuB,gBACG;AAAA,gBAAAxB,UAAA,CAAA/D,kCAAA,IAACwF,oBACG;AAAA,kBAAAzB,UAAA/D,kCAAA,IAAC0E,MAAK;AAAA,oBAAAd,IAAG;AAAA,oBACLG,UAAA/D,kCAAA,IAACyF,sBAAA;AAAA,sBACGb,UAAUhC,kBAAkB;AAAA,sBAC5BoB,WAAW,0EAA0EpB,kBAAkB,kBACjG,0CACA,EACF;AAAA,sBACPmB,UAAA;AAAA,oBAED,CAAA;AAAA,kBACJ,CAAA;AAAA,gBACJ,CAAA,GACC/D,kCAAA,IAAAwF,oBAAA;AAAA,kBACGzB,UAAC/D,kCAAA,IAAA0E,MAAA;AAAA,oBAAKd,IAAG;AAAA,oBACLG,UAAA/D,kCAAA,IAACyF,sBAAA;AAAA,sBACGb,UAAUhC,kBAAkB;AAAA,sBAC5BoB,WAAW,0EAA0EpB,kBAAkB,oBACjG,0CACA,EACF;AAAA,sBACPmB,UAAA;AAAA,oBAED,CAAA;AAAA,kBACJ,CAAA;AAAA,gBACJ,CAAA,CAAA;AAAA,cACJ,CAAA,CAAA;AAAA,YACJ,CAAA,IAGFd,uBAAuBE,eACrB5C,kCAAAA,KAACkE,iBACG;AAAA,cAAAV,UAAA,CAACxD,kCAAA,KAAAoE,mBAAA;AAAA,gBAAkBX,WAAU;AAAA,gBACzBD,UAAA,CAAC/D,kCAAA,IAAA0F,UAAA;AAAA,kBAAS1B,WAAU;AAAA,gBAAe,CAAA,GAAE,eAAA;AAAA,cAEzC,CAAA,0CACCuB,gBACG;AAAA,gBAAAxB,UAAA,CAAA/D,kCAAA,IAACwF,oBACG;AAAA,kBAAAzB,UAAA/D,kCAAA,IAAC0E,MAAK;AAAA,oBAAAd,IAAG;AAAA,oBACLG,UAAA/D,kCAAA,IAACyF,sBAAA;AAAA,sBACGb,UAAUhC,kBAAkB;AAAA,sBAC5BoB,WAAW,0EAA0EpB,kBAAkB,kBACjG,0CACA,EACF;AAAA,sBACPmB,UAAA;AAAA,oBAED,CAAA;AAAA,kBACJ,CAAA;AAAA,gBACJ,CAAA,GACC/D,kCAAA,IAAAwF,oBAAA;AAAA,kBACGzB,UAAC/D,kCAAA,IAAA0E,MAAA;AAAA,oBAAKd,IAAG;AAAA,oBACLG,UAAA/D,kCAAA,IAACyF,sBAAA;AAAA,sBACGb,UAAUhC,kBAAkB;AAAA,sBAC5BoB,WAAW,0EAA0EpB,kBAAkB,oBACjG,0CACA,EACF;AAAA,sBACPmB,UAAA;AAAA,oBAED,CAAA;AAAA,kBACJ,CAAA;AAAA,gBACJ,CAAA,GACC/D,kCAAA,IAAAwF,oBAAA;AAAA,kBACGzB,UAAC/D,kCAAA,IAAA0E,MAAA;AAAA,oBAAKd,IAAG;AAAA,oBACLG,UAAA/D,kCAAA,IAACyF,sBAAA;AAAA,sBACGb,UAAUhC,kBAAkB;AAAA,sBAC5BoB,WAAW,0EAA0EpB,kBAAkB,sBACjG,0CACA,EACF;AAAA,sBACPmB,UAAA;AAAA,oBAED,CAAA;AAAA,kBACJ,CAAA;AAAA,gBACJ,CAAA,GACC/D,kCAAA,IAAAwF,oBAAA;AAAA,kBACGzB,UAAC/D,kCAAA,IAAA0E,MAAA;AAAA,oBAAKd,IAAG;AAAA,oBACLG,UAAA/D,kCAAA,IAACyF,sBAAA;AAAA,sBACGb,UAAUhC,kBAAkB;AAAA,sBAC5BoB,WAAW,0EAA0EpB,kBAAkB,uBACjG,0CACA,EACF;AAAA,sBACPmB,UAAA;AAAA,oBAED,CAAA;AAAA,kBACJ,CAAA;AAAA,gBACJ,CAAA,GACC/D,kCAAA,IAAAwF,oBAAA;AAAA,kBACGzB,UAAC/D,kCAAA,IAAA0E,MAAA;AAAA,oBAAKd,IAAG;AAAA,oBACLG,UAAA/D,kCAAA,IAACyF,sBAAA;AAAA,sBACGb,UAAUhC,kBAAkB;AAAA,sBAC5BoB,WAAW,0EAA0EpB,kBAAkB,oBACjG,0CACA,EACF;AAAA,sBACPmB,UAAA;AAAA,oBAED,CAAA;AAAA,kBACJ,CAAA;AAAA,gBACJ,CAAA,CAAA;AAAA,cACJ,CAAA,CAAA;AAAA,YACJ,CAAA,IAGFjB,yBAAyBK,eACvBnD,kCAAAA,IAACyE;cACGV,UAAC/D,kCAAA,IAAA0E,MAAA;AAAA,gBAAKd,IAAG;AAAA,gBACLG,UAAAxD,kCAAA,KAACoE,mBAAA;AAAA,kBACGC,UAAUhC,kBAAkB;AAAA,kBAC5BoB,WAAW,0EAA0EpB,kBAAkB,qBACjG,4CACA,EACF;AAAA,kBAEJmB,UAAA,CAAC/D,kCAAA,IAAA2F,UAAA;AAAA,oBAAS3B,WAAU;AAAA,kBAAe,CAAA,GAAE,mBAAA;AAAA,gBAEzC,CAAA;AAAA,cACJ,CAAA;AAAA,aACJ,GAGHlB,yBACI9C,kCAAA,IAAAyE,iBAAA;AAAA,cACGV,UAAC/D,kCAAA,IAAA0E,MAAA;AAAA,gBAAKd,IAAG;AAAA,gBACLG,UAAAxD,kCAAA,KAACoE,mBAAA;AAAA,kBACGC,UAAUhC,kBAAkB;AAAA,kBAC5BoB,WAAW,0EAA0EpB,kBAAkB,eACjG,4CACA,EACF;AAAA,kBAEJmB,UAAA,CAAC/D,kCAAA,IAAA4F,QAAA;AAAA,oBAAO5B,WAAU;AAAA,kBAAe,CAAA,GAAE,mBAAA;AAAA,gBAEvC,CAAA;AAAA,cACJ,CAAA;AAAA,YACJ,CAAA,IAEFlB,yBAAyBK,eAAenD,kCAAAA,IAACyE;cACvCV,UAAC/D,kCAAA,IAAA0E,MAAA;AAAA,gBAAKd,IAAG;AAAA,gBACLG,UAAAxD,kCAAA,KAACoE,mBAAA;AAAA,kBAAkBC,UAAUhC,kBAAkB;AAAA,kBAC3CoB,WAAU;AAAA,kBAEVD,UAAA,CAAC/D,kCAAA,IAAA6F,SAAA;AAAA,oBAAQ7B,WAAU;AAAA,kBAAe,CAAA,GAAE,iBAAA;AAAA,gBAExC,CAAA;AAAA,cACJ,CAAA;AAAA,YACJ,CAAA,IAEElB,yBAAyBG,uBAAuBE,eAC9CnD,kCAAAA,IAACyE;cACGV,UAAC/D,kCAAA,IAAA0E,MAAA;AAAA,gBAAKd,IAAG;AAAA,gBACLG,UAAAxD,kCAAA,KAACoE,mBAAA;AAAA,kBACGC,UAAUhC,kBAAkB;AAAA,kBAC5BoB,WAAW,0EAA0EpB,kBAAkB,sBACjG,4CACA,EACF;AAAA,kBAEJmB,UAAA,CAAC/D,kCAAA,IAAA8F,WAAA;AAAA,oBAAU9B,WAAU;AAAA,kBAAe,CAAA,GAAE,oBAAA;AAAA,gBAE1C,CAAA;AAAA,cACJ,CAAA;AAAA,YACJ,CAAA,IAGFlB,yBAAyBK,eACvBnD,kCAAAA,IAACyE;cACGV,UAAC/D,kCAAA,IAAA0E,MAAA;AAAA,gBAAKd,IAAG;AAAA,gBACLG,UAAAxD,kCAAA,KAACoE,mBAAA;AAAA,kBACGC,UAAUhC,kBAAkB;AAAA,kBAC5BoB,WAAW,0EAA0EpB,kBAAkB,qBACjG,4CACA,EACF;AAAA,kBAEJmB,UAAA,CAAC/D,kCAAA,IAAA+F,cAAA;AAAA,oBAAa/B,WAAU;AAAA,kBAAe,CAAA,GAAE,mBAAA;AAAA,gBAE7C,CAAA;AAAA,cACJ,CAAA;AAAA,YACJ,CAAA,IAqBFlB,yBAAyBK,eACnDnD,kCAAAA,IAACyE;cACGV,UAAC/D,kCAAA,IAAA0E,MAAA;AAAA,gBAAKd,IAAG;AAAA,gBACLG,UAAAxD,kCAAA,KAACoE,mBAAA;AAAA,kBACGC,UAAUhC,kBAAkB;AAAA,kBAC5BoB,WAAW,0EACPpB,kBAAkB,4BACZ,4CACA,EACV;AAAA,kBAEAmB,UAAA,CAAC/D,kCAAA,IAAAgG,iBAAA;AAAA,oBAAgBhC,WAAU;AAAA,kBAAe,CAAA,GAAE,sBAAA;AAAA,gBAEhD,CAAA;AAAA,cACJ,CAAA;AAAA,YACJ,CAAA,IAE0BlB,yBAAyBK,eACvBnD,kCAAAA,IAACyE;cACGV,UAAC/D,kCAAA,IAAA0E,MAAA;AAAA,gBAAKd,IAAG;AAAA,gBACLG,UAAAxD,kCAAA,KAACoE,mBAAA;AAAA,kBACGC,UAAUhC,kBAAkB;AAAA,kBAC5BoB,WAAW,0EAA0EpB,kBAAkB,oBACjG,4CACA,EACF;AAAA,kBAEJmB,UAAA,CAAC/D,kCAAA,IAAAiG,OAAA;AAAA,oBAAMjC,WAAU;AAAA,kBAAe,CAAA,GAAE,kBAAA;AAAA,gBAEtC,CAAA;AAAA,cACJ,CAAA;AAAA,YACJ,CAAA,IAGFlB,yBAAyBK,eACvBnD,kCAAAA,IAACyE;cACGV,UAAC/D,kCAAA,IAAA0E,MAAA;AAAA,gBAAKd,IAAG;AAAA,gBACLG,UAAAxD,kCAAA,KAACoE,mBAAA;AAAA,kBACGC,UAAUhC,kBAAkB;AAAA,kBAC5BoB,WAAW,0EAA0EpB,kBAAkB,mBACjG,4CACA,EACF;AAAA,kBAEJmB,UAAA,CAAC/D,kCAAA,IAAA+E,OAAA;AAAA,oBAAMf,WAAU;AAAA,kBAAe,CAAA,GAAE,iBAAA;AAAA,gBAEtC,CAAA;AAAA,cACJ,CAAA;AAAA,YACJ,CAAA,GAIHhE,kCAAA,IAAAyE,iBAAA;AAAA,cAAgBT,WAAU;AAAA,cACvBD,iDAACvD,aACG;AAAA,gBAAAuD,UAAA,CAAA/D,kCAAA,IAACU;kBAAmBwF,SAAO;AAAA,kBACvBnC,UAACxD,kCAAA,KAAAoE,mBAAA;AAAA,oBAAkBX,WAAU;AAAA,oBACzBD,UAAA,CAAC/D,kCAAA,IAAAmG,QAAA;AAAA,sBAAOnC,WAAU;AAAA,oBAAe,CAAA,GAAE,QAAA;AAAA,kBAEvC,CAAA;AAAA,gBACJ,CAAA,0CACChD,oBACG;AAAA,kBAAA+C,UAAA,CAAAxD,kCAAA,KAAC6F,mBACG;AAAA,oBAAArC,UAAA,CAAA/D,kCAAA,IAACoB;sBAAiB2C,UAAgC;AAAA,oBAAA,CAAA,GAClD/D,kCAAA,IAACsB;sBAAuByC,UAExB;AAAA,oBAAA,CAAA,CAAA;AAAA,kBACJ,CAAA,0CACCsC,mBACG;AAAA,oBAAAtC,UAAA,CAAA/D,kCAAA,IAAC0B;sBAAkBqC,UAAM;AAAA,oBAAA,CAAA,GACxB/D,kCAAA,IAAAwB,mBAAA;AAAA,sBAAkB8E,SAAS7C;AAAAA,sBAAcM,UAAM;AAAA,oBAAA,CAAA,CAAA;AAAA,kBACpD,CAAA,CAAA;AAAA,gBACJ,CAAA,CAAA;AAAA,cACJ,CAAA;AAAA,YACJ,CAAA,CAAA;AAAA,UACJ,CAAA;AAAA,SACJ,yCACCwC,aAAY,EAAA,CAAA;AAAA,MACjB,CAAA,GAEAhG,kCAAA,KAAC,QAAK;AAAA,QAAAyD,WAAU;AAAA,QACZD,UAAA,CAAC/D,kCAAA,IAAAwG,gBAAA;AAAA,UAAexC,WAAU;AAAA,SAAgE,yCACzFyC,QAAO,EAAA,CAAA;AAAA,MACZ,CAAA,CAAA;AAAA,IACJ,CAAA;AAAA,EACJ,CAAA;AAER;", "x_google_ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 17]}