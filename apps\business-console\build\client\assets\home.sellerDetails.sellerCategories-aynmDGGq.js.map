{"version": 3, "file": "home.sellerDetails.sellerCategories-aynmDGGq.js", "sources": ["../../../app/routes/home.sellerDetails.sellerCategories.tsx"], "sourcesContent": ["import { j<PERSON>, use<PERSON><PERSON><PERSON>, use<PERSON><PERSON><PERSON><PERSON><PERSON>, useNavigate } from \"@remix-run/react\";\r\nimport { Pencil } from \"lucide-react\";\r\nimport { useEffect, useState } from \"react\";\r\nimport EditModal from \"~/components/common/EditModal\";\r\nimport { Dialog, DialogContent, DialogTitle } from \"~/components/ui/dialog\";\r\nimport { Input } from \"~/components/ui/input\";\r\nimport ResponsivePagination from \"~/components/ui/responsivePagination\";\r\nimport { ResponsiveTable } from \"~/components/ui/responsiveTable\";\r\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from \"~/components/ui/select\";\r\nimport { useToast } from \"~/components/ui/ToastProvider\";\r\nimport { useDebounce } from \"~/hooks/useDebounce\";\r\nimport { getSeCategories, updateSellerCategory } from \"~/services/businessConsoleService\";\r\nimport { getUserRoles } from \"~/services/masterItemCategories\";\r\nimport { MasterItemCategories } from \"~/types/api/businessConsoleService/MasterItemCategory\";\r\nimport { withAuth, withResponse } from \"~/utils/auth-utils\";\r\n\r\ninterface LoaderData {\r\n  googleMapsApiKey: string\r\n  sellerId: number,\r\n  sellerName: string,\r\n  activeTab: string,\r\n  url: string,\r\n  roles: { value: string; label: string }[],\r\n  sellerBId: number;\r\n  sellerCategories: MasterItemCategories[],\r\n  userId: number;\r\n  permission: string[];\r\n  currentPage: number;\r\n}\r\n\r\nexport const loader = withAuth(async ({ request, user }) => {\r\n  const googleMapsApiKey = process.env.GOOGLE_MAPS_API_KEY || ''\r\n  const url = new URL(request.url);\r\n  const sellerId = Number(url.searchParams.get(\"sellerId\"));\r\n  const sellerName = (url.searchParams.get(\"sellerName\"));\r\n  const sellerBId = Number(url.searchParams.get(\"sellerBId\"));\r\n  const userId = user.userId;\r\n  const permission = user.userDetails.roles;\r\n  const activeTab = url.searchParams.get(\"activeTab\") || 'SellerCategories';\r\n  let sellerCategories: MasterItemCategories[] | [] = [];\r\n\r\n  const page = parseInt(url.searchParams.get(\"page\") || \"0\");\r\n  const pageSize = parseInt(url.searchParams.get(\"pageSize\") || \"50\");\r\n  const matchBy = url.searchParams.get(\"matchBy\") || \"\";\r\n  let response;\r\n  try {\r\n    switch (activeTab) {\r\n      case \"SellerCategories\":\r\n        response = await getSeCategories(sellerId, page, pageSize, matchBy, request)\r\n        sellerCategories = response?.data || []\r\n        break;\r\n    }\r\n    const roleResponse = await getUserRoles(request);\r\n    const roleData = roleResponse.data as string[];\r\n    // Mapping fetched roles to the correct labels\r\n    const roleLabels: Record<string, string> = {\r\n      SellerOwner: \"Owner\",\r\n      DriverFull: \"Driver\",\r\n      AgentFull: \"Sales Agent\",\r\n      SellerManager: \"Manager\",\r\n      SellerSupervisor: \"Supervisor\",\r\n      AdvancedBuyer: \"Buyer\",\r\n      PickerFull: \"Warehouse Helper/Picker\",\r\n      ContractPriceFull: \"ContractPriceFull\",\r\n      NetworkManager: \"NetworkManager(mNET)\",\r\n      MnetManager: \"MnetManager(mNET)\",\r\n      MnetAdmin: \"MnetAdmin(mNET)\",\r\n      SalesManager: \"SalesManager(mNET)\",\r\n      MnetAgent: \"Agent(mNET)\",\r\n      WhatsappFull: \"WhatsappFull(mNET)\",\r\n      FmSalesManager: \"FmSalesManager\",\r\n      SC_Basic: \"SellerBasic\",\r\n      OC_Manager: \"OperationManager\",\r\n      AC_Basic: \"AccountManager\"\r\n    };\r\n    // Transform roleData into an array of `{ value, label }` objects\r\n    const roles = roleData\r\n      .filter((role) => roleLabels[role]) // Filter only the valid roles\r\n      .map((role) => ({ value: role, label: roleLabels[role] }));\r\n    return withResponse({\r\n      googleMapsApiKey,\r\n      sellerId,\r\n      sellerName,\r\n      activeTab,\r\n      url,\r\n      roles,\r\n      sellerBId,\r\n      sellerCategories,\r\n      userId,\r\n      permission,\r\n      page,\r\n    }, response?.headers);\r\n  } catch (error) {\r\n    console.log(\"loader failed\");\r\n    console.error(\"Error in loader:\", error);\r\n    // Return a JSON-based error shape\r\n    return [];\r\n  }\r\n});\r\n\r\ninterface ActionData {\r\n  success: boolean;\r\n}\r\n\r\nexport const action = withAuth(async ({ request }) => {\r\n  const formData = await request.formData();\r\n  const intent = formData.get(\"intent\");\r\n  const sellerCategory = formData.get(\"sellerCategory\") as string;\r\n  const categoryId = formData.get(\"categoryId\") as unknown as number;\r\n  const sellerId = formData.get(\"sellerId\") as unknown as number;\r\n  if (intent === \"editSellerCategory\") {\r\n    try {\r\n      const response = await updateSellerCategory(sellerId, categoryId, JSON.parse(sellerCategory), request);\r\n      return withResponse({ success: true }, response.headers);\r\n    }\r\n    catch (error) {\r\n      return json({ success: false }, { status: 400 })\r\n    }\r\n  }\r\n  return json({ success: false }, { status: 400 });\r\n});\r\n\r\nconst sellerCategoryHeader = [\r\n  \"Category Id\",\r\n  \"Category Name\",\r\n  \"No. of items\",\r\n  \"Sequence\",\r\n  \"\"\r\n];\r\n\r\nexport default function SellerCategories() {\r\n  const { sellerId, sellerName, activeTab, sellerBId, currentPage, sellerCategories } = useLoaderData<LoaderData>();\r\n  const navigate = useNavigate()\r\n  const fetcher = useFetcher<ActionData>()\r\n  const [searchTerm, setSearchTerm] = useState('');\r\n  const [pageSize, setPageSize] = useState(\"10\");\r\n  const [pageNum, setPageNum] = useState(0);\r\n\r\n  const [selectedCategory, setSelectedCategory] = useState<MasterItemCategories | null>(null);\r\n  const [isEditModalOpen, setIsEditModalOpen] = useState(false);\r\n\r\n  const handlePageSizeChange = (newPageSize: string) => {\r\n    setPageSize(newPageSize)\r\n    navigate(`?sellerId=${sellerId}&sellerName=${sellerName}&activeTab=${activeTab}&sellerBId=${sellerBId}&page=${currentPage}&pageSize=${newPageSize}&matchBy=${searchTerm}`)\r\n  }\r\n  const handlePageChange = (newPageSize: string) => {\r\n    setPageNum(Number(newPageSize))\r\n    navigate(`?sellerId=${sellerId}&sellerName=${sellerName}&activeTab=${activeTab}&sellerBId=${sellerBId}&page=${Number(newPageSize)}&pageSize=${pageSize}&matchBy=${searchTerm}`)\r\n  }\r\n\r\n  const handleEditModal = (row: any) => {\r\n    setSelectedCategory(row);\r\n    setIsEditModalOpen(true);\r\n  };\r\n  const handleSave = (updatedData: any) => {\r\n    console.log(\"Updated Data:\", updatedData);\r\n    const formData = new FormData()\r\n    formData.append(\"intent\", \"editSellerCategory\");\r\n    formData.append(\"sellerCategory\", JSON.stringify(updatedData));\r\n    formData.append(\"categoryId\", updatedData.id);\r\n    formData.append(\"sellerId\", sellerId.toString());\r\n    fetcher.submit(formData, { method: \"put\" })\r\n    setIsEditModalOpen(false);\r\n  };\r\n\r\n  const debounceSearchTerm = useDebounce(searchTerm, 300);\r\n  const handlePageSearch = (value: string) => {\r\n    setSearchTerm(value);\r\n  }\r\n  useEffect(() => {\r\n    if (debounceSearchTerm.length >= 3) {\r\n      // Perform search when the input has 3 or more characters\r\n      navigate(\r\n        `?sellerId=${sellerId}&sellerName=${sellerName}&activeTab=${activeTab}&sellerBId=${sellerBId}&page=${currentPage}&pageSize=${pageSize}&matchBy=${encodeURIComponent(debounceSearchTerm)}`\r\n      );\r\n    } else {\r\n      // Reset search when input is less than 3 characters\r\n      navigate(\r\n        `?sellerId=${sellerId}&sellerName=${sellerName}&activeTab=${activeTab}&sellerBId=${sellerBId}&page=${currentPage}&pageSize=${pageSize}`\r\n      );\r\n    }\r\n  }, [debounceSearchTerm])\r\n\r\n  useEffect(() => {\r\n    if (isEditModalOpen === false) {\r\n      setSelectedCategory(null)\r\n    }\r\n  }, [isEditModalOpen])\r\n\r\n  const { showToast } = useToast()\r\n  const [isSuccess, setIsSuccess] = useState(false);\r\n\r\n  useEffect(() => {\r\n    if (fetcher?.data?.success === true) {\r\n      setIsSuccess(true)\r\n      showToast(\"Sucessfully updated category sequence\", \"success\")\r\n      //revalidate\r\n      navigate(`?sellerId=${sellerId}&sellerName=${sellerName}&activeTab=${activeTab}&sellerBId=${sellerBId}&page=${currentPage}&pageSize=${pageSize}&matchBy=${searchTerm}`)\r\n    }\r\n    else if (fetcher.data?.success === false) {\r\n      setIsSuccess(false)\r\n      showToast(\"Failed to Update category sequence\", \"error\")\r\n    }\r\n  }, [fetcher.data])\r\n\r\n  return (\r\n    <div>\r\n      <div className=\"flex justify-between my-4\">\r\n        <Input\r\n          placeholder=\"Search by Category Name\"\r\n          value={searchTerm}\r\n          type='search'\r\n          onChange={(e) => handlePageSearch(e.target.value)}\r\n          className=\"max-w-sm  rounded-full\"\r\n        />\r\n\r\n        <Select value={pageSize} onValueChange={handlePageSizeChange}>\r\n          <SelectTrigger className=\"w-[180px]\">\r\n            <SelectValue placeholder=\"Categories per page\" />\r\n          </SelectTrigger>\r\n          <SelectContent>\r\n            <SelectItem value=\"5\">5 per page</SelectItem>\r\n            <SelectItem value=\"10\">10 per page</SelectItem>\r\n            <SelectItem value=\"20\">20 per page</SelectItem>\r\n            <SelectItem value=\"50\">50 per page</SelectItem>\r\n          </SelectContent>\r\n        </Select>\r\n      </div>\r\n      <ResponsiveTable\r\n        headers={sellerCategoryHeader}\r\n        data={sellerCategories}\r\n        renderRow={(row) => {\r\n          return (\r\n            <tr key={row.id} className=\"border-b\">\r\n              <td className=\"py-2 px-3 text-center whitespace-normal break-words sticky left-0 bg-white z-10\"\r\n              >{row.id}</td>\r\n              <td className=\"py-2 px-3 text-center sticky left-36 bg-white z-10\">\r\n                <div className=\"flex items-center gap-2 justify-center p-1 rounded-md\" >\r\n                  <img\r\n                    src={row?.picture}\r\n                    alt=\"image\"\r\n                    className=\"h-10 w-10 rounded-full object-cover flex-shrink-0\"\r\n                  />\r\n                  <span className=\"w-40 flex-wrap break-words text-left cursor-pointer\" >\r\n                    {row?.name}\r\n                  </span>\r\n                </div>\r\n              </td>\r\n              <td className=\"py-2 px-3 text-center  whitespace-normal break-words\"\r\n              >\r\n                {row?.totalItems}\r\n              </td>\r\n              <td className=\"py-2 px-3 text-center  whitespace-normal break-words flex flex-row gap-2 justify-center\"\r\n              >\r\n                {row?.sequence}\r\n                <Pencil className=\"w-4 h-4 my-auto cursor-pointer\" onClick={() => handleEditModal(row)} />\r\n              </td>\r\n            </tr>\r\n          )\r\n        }}\r\n      />\r\n      <div className=\"flex items-center justify-center space-x-2 py-4 overflow-x-auto whitespace-nowrap\">\r\n        <h2 className=\"shrink-0\">Current Page: {pageNum + 1}</h2>\r\n        <div className=\"overflow-x-auto\">\r\n          <ResponsivePagination\r\n            totalPages={Number(pageSize)}\r\n            currentPage={pageNum}\r\n            onPageChange={(pageNum) => handlePageChange(pageNum.toString())}\r\n          />\r\n        </div>\r\n      </div>\r\n      <Dialog open={isEditModalOpen} onOpenChange={setIsEditModalOpen}>\r\n        <DialogContent className=\"max-w-md p-6 bg-white rounded-xl shadow-2xl sm:max-w-lg\">\r\n          <DialogTitle className=\"text-xl font-bold text-gray-900 sm:text-2xl\">Edit Category</DialogTitle>\r\n          <div className=\"flex flex-col gap-4\">\r\n            <label>Category Sequence</label>\r\n            <Input\r\n              id=\"sequence\"\r\n              placeholder=\"Enter sequence\"\r\n              type=\"number\"\r\n              className=\"w-full h-8\"\r\n              required\r\n              value={selectedCategory?.sequence}\r\n              onChange={(e) => selectedCategory && setSelectedCategory({ ...selectedCategory, sequence: Number(e.target.value) })}\r\n            />\r\n          </div>\r\n          <div className=\"mt-6 flex flex-col gap-3 sm:flex-row sm:justify-end\">\r\n            <button\r\n              onClick={() => setIsEditModalOpen(false)}\r\n              className=\"w-full rounded-lg bg-gray-100 px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-300 sm:w-auto\"\r\n            >\r\n              Cancel\r\n            </button>\r\n            <button\r\n              onClick={() => handleSave(selectedCategory)}\r\n              className=\"w-full rounded-lg bg-blue-600 px-4 py-2 text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 sm:w-auto\"\r\n            >\r\n              Save\r\n            </button>\r\n          </div>\r\n        </DialogContent>\r\n      </Dialog>\r\n    </div>\r\n  )\r\n}\r\n"], "names": ["sellerCategoryHeader", "SellerCategories", "sellerId", "sellerName", "activeTab", "sellerBId", "currentPage", "sellerCategories", "useLoaderData", "navigate", "useNavigate", "fetcher", "useFetcher", "searchTerm", "setSearchTerm", "useState", "pageSize", "setPageSize", "pageNum", "setPageNum", "selectedCate<PERSON><PERSON>", "setSelectedCategory", "isEditModalOpen", "setIsEditModalOpen", "handlePageSizeChange", "newPageSize", "handlePageChange", "Number", "handleEditModal", "row", "handleSave", "updatedData", "console", "log", "formData", "FormData", "append", "JSON", "stringify", "id", "toString", "submit", "method", "debounceSearchTerm", "useDebounce", "handlePageSearch", "value", "useEffect", "length", "encodeURIComponent", "showToast", "useToast", "isSuccess", "setIsSuccess", "data", "success", "children", "jsxs", "className", "jsx", "Input", "placeholder", "type", "onChange", "e", "target", "Select", "onValueChange", "SelectTrigger", "SelectValue", "SelectContent", "SelectItem", "ResponsiveTable", "headers", "renderRow", "src", "picture", "alt", "name", "totalItems", "sequence", "Pencil", "onClick", "ResponsivePagination", "totalPages", "onPageChange", "Dialog", "open", "onOpenChange", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogTitle", "required"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0HA,MAAMA,uBAAuB,CAC3B,eACA,iBACA,gBACA,YACA,EAAA;AAGF,SAAwBC,mBAAmB;AACnC,QAAA;AAAA,IAAEC;AAAAA,IAAUC;AAAAA,IAAYC;AAAAA,IAAWC;AAAAA,IAAWC;AAAAA,IAAaC;AAAAA,MAAqBC,cAA0B;AAChH,QAAMC,WAAWC,YAAY;AAC7B,QAAMC,UAAUC,WAAuB;AACvC,QAAM,CAACC,YAAYC,aAAa,IAAIC,aAAAA,SAAS,EAAE;AAC/C,QAAM,CAACC,UAAUC,WAAW,IAAIF,aAAAA,SAAS,IAAI;AAC7C,QAAM,CAACG,SAASC,UAAU,IAAIJ,aAAAA,SAAS,CAAC;AAExC,QAAM,CAACK,kBAAkBC,mBAAmB,IAAIN,aAAAA,SAAsC,IAAI;AAC1F,QAAM,CAACO,iBAAiBC,kBAAkB,IAAIR,aAAAA,SAAS,KAAK;AAEtD,QAAAS,uBAAwBC,iBAAwB;AACpDR,gBAAYQ,WAAW;AACvBhB,aAAS,aAAaP,QAAQ,eAAeC,UAAU,cAAcC,SAAS,cAAcC,SAAS,SAASC,WAAW,aAAamB,WAAW,YAAYZ,UAAU,EAAE;AAAA,EAC3K;AACM,QAAAa,mBAAoBD,iBAAwB;AACrCN,eAAAQ,OAAOF,WAAW,CAAC;AAC9BhB,aAAS,aAAaP,QAAQ,eAAeC,UAAU,cAAcC,SAAS,cAAcC,SAAS,SAASsB,OAAOF,WAAW,CAAC,aAAaT,QAAQ,YAAYH,UAAU,EAAE;AAAA,EAChL;AAEM,QAAAe,kBAAmBC,SAAa;AACpCR,wBAAoBQ,GAAG;AACvBN,uBAAmB,IAAI;AAAA,EACzB;AACM,QAAAO,aAAcC,iBAAqB;AAC/BC,YAAAC,IAAI,iBAAiBF,WAAW;AAClC,UAAAG,WAAW,IAAIC,SAAS;AACrBD,aAAAE,OAAO,UAAU,oBAAoB;AAC9CF,aAASE,OAAO,kBAAkBC,KAAKC,UAAUP,WAAW,CAAC;AACpDG,aAAAE,OAAO,cAAcL,YAAYQ,EAAE;AAC5CL,aAASE,OAAO,YAAYlC,SAASsC,SAAA,CAAU;AAC/C7B,YAAQ8B,OAAOP,UAAU;AAAA,MAAEQ,QAAQ;AAAA,IAAM,CAAC;AAC1CnB,uBAAmB,KAAK;AAAA,EAC1B;AAEM,QAAAoB,qBAAqBC,YAAY/B,YAAY,GAAG;AAChD,QAAAgC,mBAAoBC,WAAkB;AAC1ChC,kBAAcgC,KAAK;AAAA,EACrB;AACAC,eAAAA,UAAU,MAAM;AACV,QAAAJ,mBAAmBK,UAAU,GAAG;AAElCvC,eACE,aAAaP,QAAQ,eAAeC,UAAU,cAAcC,SAAS,cAAcC,SAAS,SAASC,WAAW,aAAaU,QAAQ,YAAYiC,mBAAmBN,kBAAkB,CAAC,EACzL;AAAA,IACF,OAAO;AAELlC,eACE,aAAaP,QAAQ,eAAeC,UAAU,cAAcC,SAAS,cAAcC,SAAS,SAASC,WAAW,aAAaU,QAAQ,EACvI;AAAA,IACF;AAAA,EACF,GAAG,CAAC2B,kBAAkB,CAAC;AAEvBI,eAAAA,UAAU,MAAM;AACd,QAAIzB,oBAAoB,OAAO;AAC7BD,0BAAoB,IAAI;AAAA,IAC1B;AAAA,EACF,GAAG,CAACC,eAAe,CAAC;AAEd,QAAA;AAAA,IAAE4B;AAAAA,EAAU,IAAIC,SAAS;AAC/B,QAAM,CAACC,WAAWC,YAAY,IAAItC,aAAAA,SAAS,KAAK;AAEhDgC,eAAAA,UAAU,MAAM;;AACV,UAAApC,wCAAS2C,SAAT3C,mBAAe4C,aAAY,MAAM;AACnCF,mBAAa,IAAI;AACjBH,gBAAU,yCAAyC,SAAS;AAE5DzC,eAAS,aAAaP,QAAQ,eAAeC,UAAU,cAAcC,SAAS,cAAcC,SAAS,SAASC,WAAW,aAAaU,QAAQ,YAAYH,UAAU,EAAE;AAAA,IAE/J,aAAAF,aAAQ2C,SAAR3C,mBAAc4C,aAAY,OAAO;AACxCF,mBAAa,KAAK;AAClBH,gBAAU,sCAAsC,OAAO;AAAA,IACzD;AAAA,EACF,GAAG,CAACvC,QAAQ2C,IAAI,CAAC;AAEjB,gDACG,OACC;AAAA,IAAAE,UAAA,CAACC,kCAAA,KAAA,OAAA;AAAA,MAAIC,WAAU;AAAA,MACbF,UAAA,CAAAG,kCAAA,IAACC,OAAA;AAAA,QACCC,aAAY;AAAA,QACZf,OAAOjC;AAAAA,QACPiD,MAAK;AAAA,QACLC,UAAWC,OAAMnB,iBAAiBmB,EAAEC,OAAOnB,KAAK;AAAA,QAChDY,WAAU;AAAA,MAAA,CACZ,GAECD,kCAAA,KAAAS,QAAA;AAAA,QAAOpB,OAAO9B;AAAAA,QAAUmD,eAAe3C;AAAAA,QACtCgC,UAAA,CAAAG,kCAAA,IAACS;UAAcV,WAAU;AAAA,UACvBF,gDAACa,aAAY;AAAA,YAAAR,aAAY;AAAA,UAAsB,CAAA;AAAA,QACjD,CAAA,0CACCS,eACC;AAAA,UAAAd,UAAA,CAACG,kCAAA,IAAAY,YAAA;AAAA,YAAWzB,OAAM;AAAA,YAAIU,UAAU;AAAA,UAAA,CAAA,GAC/BG,kCAAA,IAAAY,YAAA;AAAA,YAAWzB,OAAM;AAAA,YAAKU,UAAW;AAAA,UAAA,CAAA,GACjCG,kCAAA,IAAAY,YAAA;AAAA,YAAWzB,OAAM;AAAA,YAAKU,UAAW;AAAA,UAAA,CAAA,GACjCG,kCAAA,IAAAY,YAAA;AAAA,YAAWzB,OAAM;AAAA,YAAKU,UAAW;AAAA,UAAA,CAAA,CAAA;AAAA,QACpC,CAAA,CAAA;AAAA,MACF,CAAA,CAAA;AAAA,IACF,CAAA,GACAG,kCAAA,IAACa,iBAAA;AAAA,MACCC,SAASzE;AAAAA,MACTsD,MAAM/C;AAAAA,MACNmE,WAAY7C,SAAQ;AAEhB,eAAA4B,kCAAAA,KAAC,MAAgB;AAAA,UAAAC,WAAU;AAAA,UACzBF,UAAA,CAAAG,kCAAA,IAAC,MAAA;AAAA,YAAGD,WAAU;AAAA,YACZF,UAAI3B,IAAAU;AAAAA,UAAA,CAAG,yCACR,MAAG;AAAA,YAAAmB,WAAU;AAAA,YACZF,UAACC,kCAAA,KAAA,OAAA;AAAA,cAAIC,WAAU;AAAA,cACbF,UAAA,CAAAG,kCAAA,IAAC,OAAA;AAAA,gBACCgB,KAAK9C,2BAAK+C;AAAAA,gBACVC,KAAI;AAAA,gBACJnB,WAAU;AAAA,cAAA,CACZ,GACCC,kCAAA,IAAA,QAAA;AAAA,gBAAKD,WAAU;AAAA,gBACbF,qCAAKsB;AAAAA,cACR,CAAA,CAAA;AAAA,YACF,CAAA;AAAA,UACF,CAAA,GACAnB,kCAAA,IAAC,MAAA;AAAA,YAAGD,WAAU;AAAA,YAEXF,UAAK3B,2BAAAkD;AAAAA,UAAA,CACR,GACAtB,kCAAA,KAAC,MAAA;AAAA,YAAGC,WAAU;AAAA,YAEXF,UAAA,CAAK3B,2BAAAmD,UACNrB,kCAAAA,IAACsB;cAAOvB,WAAU;AAAA,cAAiCwB,SAASA,MAAMtD,gBAAgBC,GAAG;AAAA,YAAG,CAAA,CAAA;AAAA,UAAA,CAC1F,CAAA;AAAA,QAAA,GAvBOA,IAAIU,EAwBb;AAAA,MAEJ;AAAA,IAAA,CACF,GACAkB,kCAAA,KAAC,OAAI;AAAA,MAAAC,WAAU;AAAA,MACbF,UAAA,CAACC,kCAAA,KAAA,MAAA;AAAA,QAAGC,WAAU;AAAA,QAAWF,UAAA,CAAA,kBAAetC,UAAU,CAAA;AAAA,MAAE,CAAA,GACpDyC,kCAAA,IAAC,OAAI;AAAA,QAAAD,WAAU;AAAA,QACbF,UAAAG,kCAAA,IAACwB,sBAAA;AAAA,UACCC,YAAYzD,OAAOX,QAAQ;AAAA,UAC3BV,aAAaY;AAAAA,UACbmE,cAAenE,cAAYQ,iBAAiBR,SAAQsB,SAAU,CAAA;AAAA,QAChE,CAAA;AAAA,MACF,CAAA,CAAA;AAAA,IACF,CAAA,GACAmB,kCAAA,IAAC2B;MAAOC,MAAMjE;AAAAA,MAAiBkE,cAAcjE;AAAAA,MAC3CiC,UAAAC,kCAAA,KAACgC,eAAc;AAAA,QAAA/B,WAAU;AAAA,QACvBF,UAAA,CAACG,kCAAA,IAAA+B,aAAA;AAAA,UAAYhC,WAAU;AAAA,UAA8CF,UAAa;AAAA,QAAA,CAAA,GAClFC,kCAAA,KAAC,OAAI;AAAA,UAAAC,WAAU;AAAA,UACbF,UAAA,CAAAG,kCAAA,IAAC;YAAMH,UAAiB;AAAA,UAAA,CAAA,GACxBG,kCAAA,IAACC,OAAA;AAAA,YACCrB,IAAG;AAAA,YACHsB,aAAY;AAAA,YACZC,MAAK;AAAA,YACLJ,WAAU;AAAA,YACViC,UAAQ;AAAA,YACR7C,OAAO1B,qDAAkB4D;AAAAA,YACzBjB,UAAWC,OAAM5C,oBAAoBC,oBAAoB;AAAA,cAAE,GAAGD;AAAAA,cAAkB4D,UAAUrD,OAAOqC,EAAEC,OAAOnB,KAAK;AAAA,YAAG,CAAA;AAAA,UAAA,CACpH,CAAA;AAAA,QACF,CAAA,GACAW,kCAAA,KAAC,OAAI;AAAA,UAAAC,WAAU;AAAA,UACbF,UAAA,CAAAG,kCAAA,IAAC,UAAA;AAAA,YACCuB,SAASA,MAAM3D,mBAAmB,KAAK;AAAA,YACvCmC,WAAU;AAAA,YACXF,UAAA;AAAA,UAAA,CAED,GACAG,kCAAA,IAAC,UAAA;AAAA,YACCuB,SAASA,MAAMpD,WAAWV,gBAAgB;AAAA,YAC1CsC,WAAU;AAAA,YACXF,UAAA;AAAA,UAAA,CAED,CAAA;AAAA,QACF,CAAA,CAAA;AAAA,MACF,CAAA;AAAA,IACF,CAAA,CAAA;AAAA,EACF,CAAA;AAEJ;"}