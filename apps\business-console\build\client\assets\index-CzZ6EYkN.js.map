{"version": 3, "file": "index-CzZ6EYkN.js", "sources": ["../../../node_modules/@radix-ui/react-primitive/dist/index.mjs"], "sourcesContent": ["// packages/react/primitive/src/primitive.tsx\nimport * as React from \"react\";\nimport * as ReactDOM from \"react-dom\";\nimport { Slot } from \"@radix-ui/react-slot\";\nimport { jsx } from \"react/jsx-runtime\";\nvar NODES = [\n  \"a\",\n  \"button\",\n  \"div\",\n  \"form\",\n  \"h2\",\n  \"h3\",\n  \"img\",\n  \"input\",\n  \"label\",\n  \"li\",\n  \"nav\",\n  \"ol\",\n  \"p\",\n  \"span\",\n  \"svg\",\n  \"ul\"\n];\nvar Primitive = NODES.reduce((primitive, node) => {\n  const Node = React.forwardRef((props, forwardedRef) => {\n    const { asChild, ...primitiveProps } = props;\n    const Comp = asChild ? Slot : node;\n    if (typeof window !== \"undefined\") {\n      window[Symbol.for(\"radix-ui\")] = true;\n    }\n    return /* @__PURE__ */ jsx(Comp, { ...primitiveProps, ref: forwardedRef });\n  });\n  Node.displayName = `Primitive.${node}`;\n  return { ...primitive, [node]: Node };\n}, {});\nfunction dispatchDiscreteCustomEvent(target, event) {\n  if (target) ReactDOM.flushSync(() => target.dispatchEvent(event));\n}\nvar Root = Primitive;\nexport {\n  Primitive,\n  Root,\n  dispatchDiscreteCustomEvent\n};\n//# sourceMappingURL=index.mjs.map\n"], "names": ["React.forwardRef", "jsx", "ReactDOM.flushSync"], "mappings": ";;;AAKA,IAAI,QAAQ;AAAA,EACV;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AACG,IAAC,YAAY,MAAM,OAAO,CAAC,WAAW,SAAS;AAChD,QAAM,OAAOA,aAAAA,WAAiB,CAAC,OAAO,iBAAiB;AACrD,UAAM,EAAE,SAAS,GAAG,eAAc,IAAK;AACvC,UAAM,OAAO,UAAU,OAAO;AAC9B,QAAI,OAAO,WAAW,aAAa;AACjC,aAAO,OAAO,IAAI,UAAU,CAAC,IAAI;AAAA,IACvC;AACI,WAAuBC,kCAAAA,IAAI,MAAM,EAAE,GAAG,gBAAgB,KAAK,cAAc;AAAA,EAC7E,CAAG;AACD,OAAK,cAAc,aAAa,IAAI;AACpC,SAAO,EAAE,GAAG,WAAW,CAAC,IAAI,GAAG,KAAM;AACvC,GAAG,CAAE,CAAA;AACL,SAAS,4BAA4B,QAAQ,OAAO;AAClD,MAAI,OAAQC,iBAAAA,UAAmB,MAAM,OAAO,cAAc,KAAK,CAAC;AAClE;", "x_google_ignoreList": [0]}