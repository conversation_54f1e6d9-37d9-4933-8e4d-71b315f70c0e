import { j as jsxRuntimeExports } from "./jsx-runtime-bB2y7OuD.js";
import { c as useLocation, O as Outlet } from "./index-DhHTcibu.js";
function MarketingLayout() {
  const location = useLocation();
  const activeTab = location.pathname.split("/").pop() || "templates";
  return /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
    className: "container mx-auto p-4 space-y-4",
    children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
      className: "flex flex-col space-y-4",
      children: [/* @__PURE__ */ jsxRuntimeExports.jsx("div", {
        className: "flex items-center gap-4",
        children: /* @__PURE__ */ jsxRuntimeExports.jsx("h1", {
          className: "text-2xl font-bold",
          children: activeTab === "templates" ? "Template Management" : "Marketing"
        })
      }), /* @__PURE__ */ jsxRuntimeExports.jsx(Outlet, {})]
    })
  });
}
export {
  MarketingLayout as default
};
//# sourceMappingURL=seller.marketing-DDiAhNDv.js.map
