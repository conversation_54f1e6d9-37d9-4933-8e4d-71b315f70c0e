{"version": 3, "file": "index-CkL5tk39.js", "sources": ["../../../node_modules/@radix-ui/react-use-size/dist/index.mjs"], "sourcesContent": ["// packages/react/use-size/src/useSize.tsx\nimport * as React from \"react\";\nimport { useLayoutEffect } from \"@radix-ui/react-use-layout-effect\";\nfunction useSize(element) {\n  const [size, setSize] = React.useState(void 0);\n  useLayoutEffect(() => {\n    if (element) {\n      setSize({ width: element.offsetWidth, height: element.offsetHeight });\n      const resizeObserver = new ResizeObserver((entries) => {\n        if (!Array.isArray(entries)) {\n          return;\n        }\n        if (!entries.length) {\n          return;\n        }\n        const entry = entries[0];\n        let width;\n        let height;\n        if (\"borderBoxSize\" in entry) {\n          const borderSizeEntry = entry[\"borderBoxSize\"];\n          const borderSize = Array.isArray(borderSizeEntry) ? borderSizeEntry[0] : borderSizeEntry;\n          width = borderSize[\"inlineSize\"];\n          height = borderSize[\"blockSize\"];\n        } else {\n          width = element.offsetWidth;\n          height = element.offsetHeight;\n        }\n        setSize({ width, height });\n      });\n      resizeObserver.observe(element, { box: \"border-box\" });\n      return () => resizeObserver.unobserve(element);\n    } else {\n      setSize(void 0);\n    }\n  }, [element]);\n  return size;\n}\nexport {\n  useSize\n};\n//# sourceMappingURL=index.mjs.map\n"], "names": ["React.useState", "useLayoutEffect"], "mappings": ";;AAGA,SAAS,QAAQ,SAAS;AACxB,QAAM,CAAC,MAAM,OAAO,IAAIA,aAAAA,SAAe,MAAM;AAC7CC,mBAAgB,MAAM;AACpB,QAAI,SAAS;AACX,cAAQ,EAAE,OAAO,QAAQ,aAAa,QAAQ,QAAQ,cAAc;AACpE,YAAM,iBAAiB,IAAI,eAAe,CAAC,YAAY;AACrD,YAAI,CAAC,MAAM,QAAQ,OAAO,GAAG;AAC3B;AAAA,QACV;AACQ,YAAI,CAAC,QAAQ,QAAQ;AACnB;AAAA,QACV;AACQ,cAAM,QAAQ,QAAQ,CAAC;AACvB,YAAI;AACJ,YAAI;AACJ,YAAI,mBAAmB,OAAO;AAC5B,gBAAM,kBAAkB,MAAM,eAAe;AAC7C,gBAAM,aAAa,MAAM,QAAQ,eAAe,IAAI,gBAAgB,CAAC,IAAI;AACzE,kBAAQ,WAAW,YAAY;AAC/B,mBAAS,WAAW,WAAW;AAAA,QACzC,OAAe;AACL,kBAAQ,QAAQ;AAChB,mBAAS,QAAQ;AAAA,QAC3B;AACQ,gBAAQ,EAAE,OAAO,QAAQ;AAAA,MACjC,CAAO;AACD,qBAAe,QAAQ,SAAS,EAAE,KAAK,aAAY,CAAE;AACrD,aAAO,MAAM,eAAe,UAAU,OAAO;AAAA,IACnD,OAAW;AACL,cAAQ,MAAM;AAAA,IACpB;AAAA,EACA,GAAK,CAAC,OAAO,CAAC;AACZ,SAAO;AACT;", "x_google_ignoreList": [0]}