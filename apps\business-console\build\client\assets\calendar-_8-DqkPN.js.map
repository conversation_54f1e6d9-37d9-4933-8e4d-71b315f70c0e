{"version": 3, "file": "calendar-_8-DqkPN.js", "sources": ["../../../node_modules/lucide-react/dist/esm/icons/calendar.js", "../../../node_modules/date-fns/addWeeks.mjs", "../../../node_modules/date-fns/addYears.mjs", "../../../node_modules/date-fns/max.mjs", "../../../node_modules/date-fns/min.mjs", "../../../node_modules/date-fns/differenceInCalendarMonths.mjs", "../../../node_modules/date-fns/differenceInCalendarWeeks.mjs", "../../../node_modules/date-fns/endOfMonth.mjs", "../../../node_modules/date-fns/startOfMonth.mjs", "../../../node_modules/date-fns/endOfWeek.mjs", "../../../node_modules/date-fns/endOfISOWeek.mjs", "../../../node_modules/date-fns/getDaysInMonth.mjs", "../../../node_modules/date-fns/getUnixTime.mjs", "../../../node_modules/date-fns/lastDayOfMonth.mjs", "../../../node_modules/date-fns/getWeeksInMonth.mjs", "../../../node_modules/date-fns/isAfter.mjs", "../../../node_modules/date-fns/isBefore.mjs", "../../../node_modules/date-fns/isSameMonth.mjs", "../../../node_modules/date-fns/isSameYear.mjs", "../../../node_modules/date-fns/subDays.mjs", "../../../node_modules/date-fns/setMonth.mjs", "../../../node_modules/date-fns/setYear.mjs", "../../../node_modules/react-day-picker/dist/index.esm.js", "../../../app/components/ui/calendar.tsx"], "sourcesContent": ["/**\n * @license lucide-react v0.453.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst Calendar = createLucideIcon(\"Calendar\", [\n  [\"path\", { d: \"M8 2v4\", key: \"1cmpym\" }],\n  [\"path\", { d: \"M16 2v4\", key: \"4m81vk\" }],\n  [\"rect\", { width: \"18\", height: \"18\", x: \"3\", y: \"4\", rx: \"2\", key: \"1hopcy\" }],\n  [\"path\", { d: \"M3 10h18\", key: \"8toen8\" }]\n]);\n\nexport { Calendar as default };\n//# sourceMappingURL=calendar.js.map\n", "import { addDays } from \"./addDays.mjs\";\n\n/**\n * @name addWeeks\n * @category Week Helpers\n * @summary Add the specified number of weeks to the given date.\n *\n * @description\n * Add the specified number of week to the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param date - The date to be changed\n * @param amount - The amount of weeks to be added.\n *\n * @returns The new date with the weeks added\n *\n * @example\n * // Add 4 weeks to 1 September 2014:\n * const result = addWeeks(new Date(2014, 8, 1), 4)\n * //=> Mon Sep 29 2014 00:00:00\n */\nexport function addWeeks(date, amount) {\n  const days = amount * 7;\n  return addDays(date, days);\n}\n\n// Fallback for modularized imports:\nexport default addWeeks;\n", "import { addMonths } from \"./addMonths.mjs\";\n\n/**\n * @name addYears\n * @category Year Helpers\n * @summary Add the specified number of years to the given date.\n *\n * @description\n * Add the specified number of years to the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param date - The date to be changed\n * @param amount - The amount of years to be added.\n *\n * @returns The new date with the years added\n *\n * @example\n * // Add 5 years to 1 September 2014:\n * const result = addYears(new Date(2014, 8, 1), 5)\n * //=> Sun Sep 01 2019 00:00:00\n */\nexport function addYears(date, amount) {\n  return addMonths(date, amount * 12);\n}\n\n// Fallback for modularized imports:\nexport default addYears;\n", "import { toDate } from \"./toDate.mjs\";\n\n/**\n * @name max\n * @category Common Helpers\n * @summary Return the latest of the given dates.\n *\n * @description\n * Return the latest of the given dates.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param dates - The dates to compare\n *\n * @returns The latest of the dates\n *\n * @example\n * // Which of these dates is the latest?\n * const result = max([\n *   new Date(1989, 6, 10),\n *   new Date(1987, 1, 11),\n *   new Date(1995, 6, 2),\n *   new Date(1990, 0, 1)\n * ])\n * //=> Sun Jul 02 1995 00:00:00\n */\nexport function max(dates) {\n  let result;\n  dates.forEach(function (dirtyDate) {\n    const currentDate = toDate(dirtyDate);\n\n    if (\n      result === undefined ||\n      result < currentDate ||\n      isNaN(Number(currentDate))\n    ) {\n      result = currentDate;\n    }\n  });\n\n  return result || new Date(NaN);\n}\n\n// Fallback for modularized imports:\nexport default max;\n", "import { toDate } from \"./toDate.mjs\";\n\n/**\n * @name min\n * @category Common Helpers\n * @summary Returns the earliest of the given dates.\n *\n * @description\n * Returns the earliest of the given dates.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param dates - The dates to compare\n *\n * @returns The earliest of the dates\n *\n * @example\n * // Which of these dates is the earliest?\n * const result = min([\n *   new Date(1989, 6, 10),\n *   new Date(1987, 1, 11),\n *   new Date(1995, 6, 2),\n *   new Date(1990, 0, 1)\n * ])\n * //=> Wed Feb 11 1987 00:00:00\n */\nexport function min(dates) {\n  let result;\n\n  dates.forEach((dirtyDate) => {\n    const date = toDate(dirtyDate);\n    if (!result || result > date || isNaN(+date)) {\n      result = date;\n    }\n  });\n\n  return result || new Date(NaN);\n}\n\n// Fallback for modularized imports:\nexport default min;\n", "import { toDate } from \"./toDate.mjs\";\n\n/**\n * @name differenceInCalendarMonths\n * @category Month Helpers\n * @summary Get the number of calendar months between the given dates.\n *\n * @description\n * Get the number of calendar months between the given dates.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param dateLeft - The later date\n * @param dateRight - The earlier date\n *\n * @returns The number of calendar months\n *\n * @example\n * // How many calendar months are between 31 January 2014 and 1 September 2014?\n * const result = differenceInCalendarMonths(\n *   new Date(2014, 8, 1),\n *   new Date(2014, 0, 31)\n * )\n * //=> 8\n */\nexport function differenceInCalendarMonths(dateLeft, dateRight) {\n  const _dateLeft = toDate(dateLeft);\n  const _dateRight = toDate(dateRight);\n\n  const yearDiff = _dateLeft.getFullYear() - _dateRight.getFullYear();\n  const monthDiff = _dateLeft.getMonth() - _dateRight.getMonth();\n\n  return yearDiff * 12 + monthDiff;\n}\n\n// Fallback for modularized imports:\nexport default differenceInCalendarMonths;\n", "import { millisecondsInWeek } from \"./constants.mjs\";\nimport { startOfWeek } from \"./startOfWeek.mjs\";\nimport { getTimezoneOffsetInMilliseconds } from \"./_lib/getTimezoneOffsetInMilliseconds.mjs\";\n\n/**\n * The {@link differenceInCalendarWeeks} function options.\n */\n\n/**\n * @name differenceInCalendarWeeks\n * @category Week Helpers\n * @summary Get the number of calendar weeks between the given dates.\n *\n * @description\n * Get the number of calendar weeks between the given dates.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param dateLeft - The later date\n * @param dateRight - The earlier date\n * @param options - An object with options.\n *\n * @returns The number of calendar weeks\n *\n * @example\n * // How many calendar weeks are between 5 July 2014 and 20 July 2014?\n * const result = differenceInCalendarWeeks(\n *   new Date(2014, 6, 20),\n *   new Date(2014, 6, 5)\n * )\n * //=> 3\n *\n * @example\n * // If the week starts on Monday,\n * // how many calendar weeks are between 5 July 2014 and 20 July 2014?\n * const result = differenceInCalendarWeeks(\n *   new Date(2014, 6, 20),\n *   new Date(2014, 6, 5),\n *   { weekStartsOn: 1 }\n * )\n * //=> 2\n */\nexport function differenceInCalendarWeeks(dateLeft, dateRight, options) {\n  const startOfWeekLeft = startOfWeek(dateLeft, options);\n  const startOfWeekRight = startOfWeek(dateRight, options);\n\n  const timestampLeft =\n    +startOfWeekLeft - getTimezoneOffsetInMilliseconds(startOfWeekLeft);\n  const timestampRight =\n    +startOfWeekRight - getTimezoneOffsetInMilliseconds(startOfWeekRight);\n\n  // Round the number of days to the nearest integer because the number of\n  // milliseconds in a days is not constant (e.g. it's different in the week of\n  // the daylight saving time clock shift).\n  return Math.round((timestampLeft - timestampRight) / millisecondsInWeek);\n}\n\n// Fallback for modularized imports:\nexport default differenceInCalendarWeeks;\n", "import { toDate } from \"./toDate.mjs\";\n\n/**\n * @name endOfMonth\n * @category Month Helpers\n * @summary Return the end of a month for the given date.\n *\n * @description\n * Return the end of a month for the given date.\n * The result will be in the local timezone.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param date - The original date\n *\n * @returns The end of a month\n *\n * @example\n * // The end of a month for 2 September 2014 11:55:00:\n * const result = endOfMonth(new Date(2014, 8, 2, 11, 55, 0))\n * //=> Tue Sep 30 2014 23:59:59.999\n */\nexport function endOfMonth(date) {\n  const _date = toDate(date);\n  const month = _date.getMonth();\n  _date.setFullYear(_date.getFullYear(), month + 1, 0);\n  _date.setHours(23, 59, 59, 999);\n  return _date;\n}\n\n// Fallback for modularized imports:\nexport default endOfMonth;\n", "import { toDate } from \"./toDate.mjs\";\n\n/**\n * @name startOfMonth\n * @category Month Helpers\n * @summary Return the start of a month for the given date.\n *\n * @description\n * Return the start of a month for the given date.\n * The result will be in the local timezone.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param date - The original date\n *\n * @returns The start of a month\n *\n * @example\n * // The start of a month for 2 September 2014 11:55:00:\n * const result = startOfMonth(new Date(2014, 8, 2, 11, 55, 0))\n * //=> Mon Sep 01 2014 00:00:00\n */\nexport function startOfMonth(date) {\n  const _date = toDate(date);\n  _date.setDate(1);\n  _date.setHours(0, 0, 0, 0);\n  return _date;\n}\n\n// Fallback for modularized imports:\nexport default startOfMonth;\n", "import { toDate } from \"./toDate.mjs\";\nimport { getDefaultOptions } from \"./_lib/defaultOptions.mjs\";\n\n/**\n * The {@link endOfWeek} function options.\n */\n\n/**\n * @name endOfWeek\n * @category Week Helpers\n * @summary Return the end of a week for the given date.\n *\n * @description\n * Return the end of a week for the given date.\n * The result will be in the local timezone.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param date - The original date\n * @param options - An object with options\n *\n * @returns The end of a week\n *\n * @example\n * // The end of a week for 2 September 2014 11:55:00:\n * const result = endOfWeek(new Date(2014, 8, 2, 11, 55, 0))\n * //=> Sat Sep 06 2014 23:59:59.999\n *\n * @example\n * // If the week starts on Monday, the end of the week for 2 September 2014 11:55:00:\n * const result = endOfWeek(new Date(2014, 8, 2, 11, 55, 0), { weekStartsOn: 1 })\n * //=> Sun Sep 07 2014 23:59:59.999\n */\nexport function endOfWeek(date, options) {\n  const defaultOptions = getDefaultOptions();\n  const weekStartsOn =\n    options?.weekStartsOn ??\n    options?.locale?.options?.weekStartsOn ??\n    defaultOptions.weekStartsOn ??\n    defaultOptions.locale?.options?.weekStartsOn ??\n    0;\n\n  const _date = toDate(date);\n  const day = _date.getDay();\n  const diff = (day < weekStartsOn ? -7 : 0) + 6 - (day - weekStartsOn);\n\n  _date.setDate(_date.getDate() + diff);\n  _date.setHours(23, 59, 59, 999);\n  return _date;\n}\n\n// Fallback for modularized imports:\nexport default endOfWeek;\n", "import { endOfWeek } from \"./endOfWeek.mjs\";\n\n/**\n * @name endOfISOWeek\n * @category ISO Week Helpers\n * @summary Return the end of an ISO week for the given date.\n *\n * @description\n * Return the end of an ISO week for the given date.\n * The result will be in the local timezone.\n *\n * ISO week-numbering year: http://en.wikipedia.org/wiki/ISO_week_date\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param date - The original date\n *\n * @returns The end of an ISO week\n *\n * @example\n * // The end of an ISO week for 2 September 2014 11:55:00:\n * const result = endOfISOWeek(new Date(2014, 8, 2, 11, 55, 0))\n * //=> Sun Sep 07 2014 23:59:59.999\n */\nexport function endOfISOWeek(date) {\n  return endOfWeek(date, { weekStartsOn: 1 });\n}\n\n// Fallback for modularized imports:\nexport default endOfISOWeek;\n", "import { toDate } from \"./toDate.mjs\";\nimport { constructFrom } from \"./constructFrom.mjs\";\n\n/**\n * @name getDaysInMonth\n * @category Month Helpers\n * @summary Get the number of days in a month of the given date.\n *\n * @description\n * Get the number of days in a month of the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param date - The given date\n *\n * @returns The number of days in a month\n *\n * @example\n * // How many days are in February 2000?\n * const result = getDaysInMonth(new Date(2000, 1))\n * //=> 29\n */\nexport function getDaysInMonth(date) {\n  const _date = toDate(date);\n  const year = _date.getFullYear();\n  const monthIndex = _date.getMonth();\n  const lastDayOfMonth = constructFrom(date, 0);\n  lastDayOfMonth.setFullYear(year, monthIndex + 1, 0);\n  lastDayOfMonth.setHours(0, 0, 0, 0);\n  return lastDayOfMonth.getDate();\n}\n\n// Fallback for modularized imports:\nexport default getDaysInMonth;\n", "import { toDate } from \"./toDate.mjs\";\n\n/**\n * @name getUnixTime\n * @category Timestamp Helpers\n * @summary Get the seconds timestamp of the given date.\n *\n * @description\n * Get the seconds timestamp of the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param date - The given date\n *\n * @returns The timestamp\n *\n * @example\n * // Get the timestamp of 29 February 2012 11:45:05 CET:\n * const result = getUnixTime(new Date(2012, 1, 29, 11, 45, 5))\n * //=> 1330512305\n */\nexport function getUnixTime(date) {\n  return Math.trunc(+toDate(date) / 1000);\n}\n\n// Fallback for modularized imports:\nexport default getUnixTime;\n", "import { toDate } from \"./toDate.mjs\";\n\n/**\n * @name lastDayOfMonth\n * @category Month Helpers\n * @summary Return the last day of a month for the given date.\n *\n * @description\n * Return the last day of a month for the given date.\n * The result will be in the local timezone.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param date - The original date\n *\n * @returns The last day of a month\n *\n * @example\n * // The last day of a month for 2 September 2014 11:55:00:\n * const result = lastDayOfMonth(new Date(2014, 8, 2, 11, 55, 0))\n * //=> Tue Sep 30 2014 00:00:00\n */\nexport function lastDayOfMonth(date) {\n  const _date = toDate(date);\n  const month = _date.getMonth();\n  _date.setFullYear(_date.getFullYear(), month + 1, 0);\n  _date.setHours(0, 0, 0, 0);\n  return _date;\n}\n\n// Fallback for modularized imports:\nexport default lastDayOfMonth;\n", "import { differenceInCalendarWeeks } from \"./differenceInCalendarWeeks.mjs\";\nimport { lastDayOfMonth } from \"./lastDayOfMonth.mjs\";\nimport { startOfMonth } from \"./startOfMonth.mjs\";\n\n/**\n * The {@link getWeeksInMonth} function options.\n */\n\n/**\n * @name getWeeksInMonth\n * @category Week Helpers\n * @summary Get the number of calendar weeks a month spans.\n *\n * @description\n * Get the number of calendar weeks the month in the given date spans.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param date - The given date\n * @param options - An object with options.\n *\n * @returns The number of calendar weeks\n *\n * @example\n * // How many calendar weeks does February 2015 span?\n * const result = getWeeksInMonth(new Date(2015, 1, 8))\n * //=> 4\n *\n * @example\n * // If the week starts on Monday,\n * // how many calendar weeks does July 2017 span?\n * const result = getWeeksInMonth(new Date(2017, 6, 5), { weekStartsOn: 1 })\n * //=> 6\n */\nexport function getWeeksInMonth(date, options) {\n  return (\n    differenceInCalendarWeeks(\n      lastDayOfMonth(date),\n      startOfMonth(date),\n      options,\n    ) + 1\n  );\n}\n\n// Fallback for modularized imports:\nexport default getWeeksInMonth;\n", "import { toDate } from \"./toDate.mjs\";\n\n/**\n * @name isAfter\n * @category Common Helpers\n * @summary Is the first date after the second one?\n *\n * @description\n * Is the first date after the second one?\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param date - The date that should be after the other one to return true\n * @param dateToCompare - The date to compare with\n *\n * @returns The first date is after the second date\n *\n * @example\n * // Is 10 July 1989 after 11 February 1987?\n * const result = isAfter(new Date(1989, 6, 10), new Date(1987, 1, 11))\n * //=> true\n */\nexport function isAfter(date, dateToCompare) {\n  const _date = toDate(date);\n  const _dateToCompare = toDate(dateToCompare);\n  return _date.getTime() > _dateToCompare.getTime();\n}\n\n// Fallback for modularized imports:\nexport default isAfter;\n", "import { toDate } from \"./toDate.mjs\";\n\n/**\n * @name isBefore\n * @category Common Helpers\n * @summary Is the first date before the second one?\n *\n * @description\n * Is the first date before the second one?\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param date - The date that should be before the other one to return true\n * @param dateToCompare - The date to compare with\n *\n * @returns The first date is before the second date\n *\n * @example\n * // Is 10 July 1989 before 11 February 1987?\n * const result = isBefore(new Date(1989, 6, 10), new Date(1987, 1, 11))\n * //=> false\n */\nexport function isBefore(date, dateToCompare) {\n  const _date = toDate(date);\n  const _dateToCompare = toDate(dateToCompare);\n  return +_date < +_dateToCompare;\n}\n\n// Fallback for modularized imports:\nexport default isBefore;\n", "import { toDate } from \"./toDate.mjs\";\n\n/**\n * @name isSameMonth\n * @category Month Helpers\n * @summary Are the given dates in the same month (and year)?\n *\n * @description\n * Are the given dates in the same month (and year)?\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param dateLeft - The first date to check\n * @param dateRight - The second date to check\n *\n * @returns The dates are in the same month (and year)\n *\n * @example\n * // Are 2 September 2014 and 25 September 2014 in the same month?\n * const result = isSameMonth(new Date(2014, 8, 2), new Date(2014, 8, 25))\n * //=> true\n *\n * @example\n * // Are 2 September 2014 and 25 September 2015 in the same month?\n * const result = isSameMonth(new Date(2014, 8, 2), new Date(2015, 8, 25))\n * //=> false\n */\nexport function isSameMonth(dateLeft, dateRight) {\n  const _dateLeft = toDate(dateLeft);\n  const _dateRight = toDate(dateRight);\n  return (\n    _dateLeft.getFullYear() === _dateRight.getFullYear() &&\n    _dateLeft.getMonth() === _dateRight.getMonth()\n  );\n}\n\n// Fallback for modularized imports:\nexport default isSameMonth;\n", "import { toDate } from \"./toDate.mjs\";\n\n/**\n * @name isSameYear\n * @category Year Helpers\n * @summary Are the given dates in the same year?\n *\n * @description\n * Are the given dates in the same year?\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param dateLeft - The first date to check\n * @param dateRight - The second date to check\n *\n * @returns The dates are in the same year\n *\n * @example\n * // Are 2 September 2014 and 25 September 2014 in the same year?\n * const result = isSameYear(new Date(2014, 8, 2), new Date(2014, 8, 25))\n * //=> true\n */\nexport function isSameYear(dateLeft, dateRight) {\n  const _dateLeft = toDate(dateLeft);\n  const _dateRight = toDate(dateRight);\n  return _dateLeft.getFullYear() === _dateRight.getFullYear();\n}\n\n// Fallback for modularized imports:\nexport default isSameYear;\n", "import { addDays } from \"./addDays.mjs\";\n\n/**\n * @name subDays\n * @category Day Helpers\n * @summary Subtract the specified number of days from the given date.\n *\n * @description\n * Subtract the specified number of days from the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param date - The date to be changed\n * @param amount - The amount of days to be subtracted.\n *\n * @returns The new date with the days subtracted\n *\n * @example\n * // Subtract 10 days from 1 September 2014:\n * const result = subDays(new Date(2014, 8, 1), 10)\n * //=> Fri Aug 22 2014 00:00:00\n */\nexport function subDays(date, amount) {\n  return addDays(date, -amount);\n}\n\n// Fallback for modularized imports:\nexport default subDays;\n", "import { constructFrom } from \"./constructFrom.mjs\";\nimport { getDaysInMonth } from \"./getDaysInMonth.mjs\";\nimport { toDate } from \"./toDate.mjs\";\n\n/**\n * @name setMonth\n * @category Month Helpers\n * @summary Set the month to the given date.\n *\n * @description\n * Set the month to the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param date - The date to be changed\n * @param month - The month index to set (0-11)\n *\n * @returns The new date with the month set\n *\n * @example\n * // Set February to 1 September 2014:\n * const result = setMonth(new Date(2014, 8, 1), 1)\n * //=> Sat Feb 01 2014 00:00:00\n */\nexport function setMonth(date, month) {\n  const _date = toDate(date);\n  const year = _date.getFullYear();\n  const day = _date.getDate();\n\n  const dateWithDesiredMonth = constructFrom(date, 0);\n  dateWithDesiredMonth.setFullYear(year, month, 15);\n  dateWithDesiredMonth.setHours(0, 0, 0, 0);\n  const daysInMonth = getDaysInMonth(dateWithDesiredMonth);\n  // Set the last day of the new month\n  // if the original date was the last day of the longer month\n  _date.setMonth(month, Math.min(day, daysInMonth));\n  return _date;\n}\n\n// Fallback for modularized imports:\nexport default setMonth;\n", "import { constructFrom } from \"./constructFrom.mjs\";\nimport { toDate } from \"./toDate.mjs\";\n\n/**\n * @name setYear\n * @category Year Helpers\n * @summary Set the year to the given date.\n *\n * @description\n * Set the year to the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param date - The date to be changed\n * @param year - The year of the new date\n *\n * @returns The new date with the year set\n *\n * @example\n * // Set year 2013 to 1 September 2014:\n * const result = setYear(new Date(2014, 8, 1), 2013)\n * //=> Sun Sep 01 2013 00:00:00\n */\nexport function setYear(date, year) {\n  const _date = toDate(date);\n\n  // Check if date is Invalid Date because Date.prototype.setFullYear ignores the value of Invalid Date\n  if (isNaN(+_date)) {\n    return constructFrom(date, NaN);\n  }\n\n  _date.setFullYear(year);\n  return _date;\n}\n\n// Fallback for modularized imports:\nexport default setYear;\n", "import { jsx, jsxs, Fragment } from 'react/jsx-runtime';\nimport { createContext, useContext, useState, forwardRef, useEffect, useRef, useLayoutEffect } from 'react';\nimport { format, startOfMonth, endOfMonth, startOfDay, isSameYear, setMonth, setYear, startOfYear, differenceInCalendarMonths, addMonths, isSameMonth, isBefore, startOfISOWeek, startOfWeek, addDays, isSameDay, isAfter, subDays, differenceInCalendarDays, isDate, max, min, addWeeks, addYears, endOfISOWeek, endOfWeek, getUnixTime, getISOWeek, getWeek, getWeeksInMonth, parse } from 'date-fns';\nimport { enUS } from 'date-fns/locale';\n\n/******************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise, SuppressedError, Symbol */\r\n\r\n\r\nvar __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    };\r\n    return __assign.apply(this, arguments);\r\n};\r\n\r\nfunction __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\r\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\r\n                t[p[i]] = s[p[i]];\r\n        }\r\n    return t;\r\n}\r\n\r\nfunction __spreadArray(to, from, pack) {\r\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\r\n        if (ar || !(i in from)) {\r\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\r\n            ar[i] = from[i];\r\n        }\r\n    }\r\n    return to.concat(ar || Array.prototype.slice.call(from));\r\n}\r\n\r\ntypeof SuppressedError === \"function\" ? SuppressedError : function (error, suppressed, message) {\r\n    var e = new Error(message);\r\n    return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\r\n};\n\n/** Returns true when the props are of type {@link DayPickerMultipleProps}. */\nfunction isDayPickerMultiple(props) {\n    return props.mode === 'multiple';\n}\n\n/** Returns true when the props are of type {@link DayPickerRangeProps}. */\nfunction isDayPickerRange(props) {\n    return props.mode === 'range';\n}\n\n/** Returns true when the props are of type {@link DayPickerSingleProps}. */\nfunction isDayPickerSingle(props) {\n    return props.mode === 'single';\n}\n\n/**\n * The name of the default CSS classes.\n */\nvar defaultClassNames = {\n    root: 'rdp',\n    multiple_months: 'rdp-multiple_months',\n    with_weeknumber: 'rdp-with_weeknumber',\n    vhidden: 'rdp-vhidden',\n    button_reset: 'rdp-button_reset',\n    button: 'rdp-button',\n    caption: 'rdp-caption',\n    caption_start: 'rdp-caption_start',\n    caption_end: 'rdp-caption_end',\n    caption_between: 'rdp-caption_between',\n    caption_label: 'rdp-caption_label',\n    caption_dropdowns: 'rdp-caption_dropdowns',\n    dropdown: 'rdp-dropdown',\n    dropdown_month: 'rdp-dropdown_month',\n    dropdown_year: 'rdp-dropdown_year',\n    dropdown_icon: 'rdp-dropdown_icon',\n    months: 'rdp-months',\n    month: 'rdp-month',\n    table: 'rdp-table',\n    tbody: 'rdp-tbody',\n    tfoot: 'rdp-tfoot',\n    head: 'rdp-head',\n    head_row: 'rdp-head_row',\n    head_cell: 'rdp-head_cell',\n    nav: 'rdp-nav',\n    nav_button: 'rdp-nav_button',\n    nav_button_previous: 'rdp-nav_button_previous',\n    nav_button_next: 'rdp-nav_button_next',\n    nav_icon: 'rdp-nav_icon',\n    row: 'rdp-row',\n    weeknumber: 'rdp-weeknumber',\n    cell: 'rdp-cell',\n    day: 'rdp-day',\n    day_today: 'rdp-day_today',\n    day_outside: 'rdp-day_outside',\n    day_selected: 'rdp-day_selected',\n    day_disabled: 'rdp-day_disabled',\n    day_hidden: 'rdp-day_hidden',\n    day_range_start: 'rdp-day_range_start',\n    day_range_end: 'rdp-day_range_end',\n    day_range_middle: 'rdp-day_range_middle'\n};\n\n/**\n * The default formatter for the caption.\n */\nfunction formatCaption(month, options) {\n    return format(month, 'LLLL y', options);\n}\n\n/**\n * The default formatter for the Day button.\n */\nfunction formatDay(day, options) {\n    return format(day, 'd', options);\n}\n\n/**\n * The default formatter for the Month caption.\n */\nfunction formatMonthCaption(month, options) {\n    return format(month, 'LLLL', options);\n}\n\n/**\n * The default formatter for the week number.\n */\nfunction formatWeekNumber(weekNumber) {\n    return \"\".concat(weekNumber);\n}\n\n/**\n * The default formatter for the name of the weekday.\n */\nfunction formatWeekdayName(weekday, options) {\n    return format(weekday, 'cccccc', options);\n}\n\n/**\n * The default formatter for the Year caption.\n */\nfunction formatYearCaption(year, options) {\n    return format(year, 'yyyy', options);\n}\n\nvar formatters = /*#__PURE__*/Object.freeze({\n    __proto__: null,\n    formatCaption: formatCaption,\n    formatDay: formatDay,\n    formatMonthCaption: formatMonthCaption,\n    formatWeekNumber: formatWeekNumber,\n    formatWeekdayName: formatWeekdayName,\n    formatYearCaption: formatYearCaption\n});\n\n/**\n * The default ARIA label for the day button.\n */\nvar labelDay = function (day, activeModifiers, options) {\n    return format(day, 'do MMMM (EEEE)', options);\n};\n\n/**\n * The default ARIA label for the WeekNumber element.\n */\nvar labelMonthDropdown = function () {\n    return 'Month: ';\n};\n\n/**\n * The default ARIA label for next month button in navigation\n */\nvar labelNext = function () {\n    return 'Go to next month';\n};\n\n/**\n * The default ARIA label for previous month button in navigation\n */\nvar labelPrevious = function () {\n    return 'Go to previous month';\n};\n\n/**\n * The default ARIA label for the Weekday element.\n */\nvar labelWeekday = function (day, options) {\n    return format(day, 'cccc', options);\n};\n\n/**\n * The default ARIA label for the WeekNumber element.\n */\nvar labelWeekNumber = function (n) {\n    return \"Week n. \".concat(n);\n};\n\n/**\n * The default ARIA label for the WeekNumber element.\n */\nvar labelYearDropdown = function () {\n    return 'Year: ';\n};\n\nvar labels = /*#__PURE__*/Object.freeze({\n    __proto__: null,\n    labelDay: labelDay,\n    labelMonthDropdown: labelMonthDropdown,\n    labelNext: labelNext,\n    labelPrevious: labelPrevious,\n    labelWeekNumber: labelWeekNumber,\n    labelWeekday: labelWeekday,\n    labelYearDropdown: labelYearDropdown\n});\n\n/**\n * Returns the default values to use in the DayPickerContext, in case they are\n * not passed down with the DayPicker initial props.\n */\nfunction getDefaultContextValues() {\n    var captionLayout = 'buttons';\n    var classNames = defaultClassNames;\n    var locale = enUS;\n    var modifiersClassNames = {};\n    var modifiers = {};\n    var numberOfMonths = 1;\n    var styles = {};\n    var today = new Date();\n    return {\n        captionLayout: captionLayout,\n        classNames: classNames,\n        formatters: formatters,\n        labels: labels,\n        locale: locale,\n        modifiersClassNames: modifiersClassNames,\n        modifiers: modifiers,\n        numberOfMonths: numberOfMonths,\n        styles: styles,\n        today: today,\n        mode: 'default'\n    };\n}\n\n/** Return the `fromDate` and `toDate` prop values values parsing the DayPicker props. */\nfunction parseFromToProps(props) {\n    var fromYear = props.fromYear, toYear = props.toYear, fromMonth = props.fromMonth, toMonth = props.toMonth;\n    var fromDate = props.fromDate, toDate = props.toDate;\n    if (fromMonth) {\n        fromDate = startOfMonth(fromMonth);\n    }\n    else if (fromYear) {\n        fromDate = new Date(fromYear, 0, 1);\n    }\n    if (toMonth) {\n        toDate = endOfMonth(toMonth);\n    }\n    else if (toYear) {\n        toDate = new Date(toYear, 11, 31);\n    }\n    return {\n        fromDate: fromDate ? startOfDay(fromDate) : undefined,\n        toDate: toDate ? startOfDay(toDate) : undefined\n    };\n}\n\n/**\n * The DayPicker context shares the props passed to DayPicker within internal\n * and custom components. It is used to set the default values and perform\n * one-time calculations required to render the days.\n *\n * Access to this context from the {@link useDayPicker} hook.\n */\nvar DayPickerContext = createContext(undefined);\n/**\n * The provider for the {@link DayPickerContext}, assigning the defaults from the\n * initial DayPicker props.\n */\nfunction DayPickerProvider(props) {\n    var _a;\n    var initialProps = props.initialProps;\n    var defaultContextValues = getDefaultContextValues();\n    var _b = parseFromToProps(initialProps), fromDate = _b.fromDate, toDate = _b.toDate;\n    var captionLayout = (_a = initialProps.captionLayout) !== null && _a !== void 0 ? _a : defaultContextValues.captionLayout;\n    if (captionLayout !== 'buttons' && (!fromDate || !toDate)) {\n        // When no from/to dates are set, the caption is always buttons\n        captionLayout = 'buttons';\n    }\n    var onSelect;\n    if (isDayPickerSingle(initialProps) ||\n        isDayPickerMultiple(initialProps) ||\n        isDayPickerRange(initialProps)) {\n        onSelect = initialProps.onSelect;\n    }\n    var value = __assign(__assign(__assign({}, defaultContextValues), initialProps), { captionLayout: captionLayout, classNames: __assign(__assign({}, defaultContextValues.classNames), initialProps.classNames), components: __assign({}, initialProps.components), formatters: __assign(__assign({}, defaultContextValues.formatters), initialProps.formatters), fromDate: fromDate, labels: __assign(__assign({}, defaultContextValues.labels), initialProps.labels), mode: initialProps.mode || defaultContextValues.mode, modifiers: __assign(__assign({}, defaultContextValues.modifiers), initialProps.modifiers), modifiersClassNames: __assign(__assign({}, defaultContextValues.modifiersClassNames), initialProps.modifiersClassNames), onSelect: onSelect, styles: __assign(__assign({}, defaultContextValues.styles), initialProps.styles), toDate: toDate });\n    return (jsx(DayPickerContext.Provider, { value: value, children: props.children }));\n}\n/**\n * Hook to access the {@link DayPickerContextValue}.\n *\n * Use the DayPicker context to access to the props passed to DayPicker inside\n * internal or custom components.\n */\nfunction useDayPicker() {\n    var context = useContext(DayPickerContext);\n    if (!context) {\n        throw new Error(\"useDayPicker must be used within a DayPickerProvider.\");\n    }\n    return context;\n}\n\n/** Render the caption for the displayed month. This component is used when `captionLayout=\"buttons\"`. */\nfunction CaptionLabel(props) {\n    var _a = useDayPicker(), locale = _a.locale, classNames = _a.classNames, styles = _a.styles, formatCaption = _a.formatters.formatCaption;\n    return (jsx(\"div\", { className: classNames.caption_label, style: styles.caption_label, \"aria-live\": \"polite\", role: \"presentation\", id: props.id, children: formatCaption(props.displayMonth, { locale: locale }) }));\n}\n\n/**\n * Render the icon in the styled drop-down.\n */\nfunction IconDropdown(props) {\n    return (jsx(\"svg\", __assign({ width: \"8px\", height: \"8px\", viewBox: \"0 0 120 120\", \"data-testid\": \"iconDropdown\" }, props, { children: jsx(\"path\", { d: \"M4.22182541,48.2218254 C8.44222828,44.0014225 15.2388494,43.9273804 19.5496459,47.9996989 L19.7781746,48.2218254 L60,88.443 L100.221825,48.2218254 C104.442228,44.0014225 111.238849,43.9273804 115.549646,47.9996989 L115.778175,48.2218254 C119.998577,52.4422283 120.07262,59.2388494 116.000301,63.5496459 L115.778175,63.7781746 L67.7781746,111.778175 C63.5577717,115.998577 56.7611506,116.07262 52.4503541,112.000301 L52.2218254,111.778175 L4.22182541,63.7781746 C-0.0739418023,59.4824074 -0.0739418023,52.5175926 4.22182541,48.2218254 Z\", fill: \"currentColor\", fillRule: \"nonzero\" }) })));\n}\n\n/**\n * Render a styled select component – displaying a caption and a custom\n * drop-down icon.\n */\nfunction Dropdown(props) {\n    var _a, _b;\n    var onChange = props.onChange, value = props.value, children = props.children, caption = props.caption, className = props.className, style = props.style;\n    var dayPicker = useDayPicker();\n    var IconDropdownComponent = (_b = (_a = dayPicker.components) === null || _a === void 0 ? void 0 : _a.IconDropdown) !== null && _b !== void 0 ? _b : IconDropdown;\n    return (jsxs(\"div\", { className: className, style: style, children: [jsx(\"span\", { className: dayPicker.classNames.vhidden, children: props['aria-label'] }), jsx(\"select\", { name: props.name, \"aria-label\": props['aria-label'], className: dayPicker.classNames.dropdown, style: dayPicker.styles.dropdown, value: value, onChange: onChange, children: children }), jsxs(\"div\", { className: dayPicker.classNames.caption_label, style: dayPicker.styles.caption_label, \"aria-hidden\": \"true\", children: [caption, jsx(IconDropdownComponent, { className: dayPicker.classNames.dropdown_icon, style: dayPicker.styles.dropdown_icon })] })] }));\n}\n\n/** Render the dropdown to navigate between months. */\nfunction MonthsDropdown(props) {\n    var _a;\n    var _b = useDayPicker(), fromDate = _b.fromDate, toDate = _b.toDate, styles = _b.styles, locale = _b.locale, formatMonthCaption = _b.formatters.formatMonthCaption, classNames = _b.classNames, components = _b.components, labelMonthDropdown = _b.labels.labelMonthDropdown;\n    // Dropdown should appear only when both from/toDate is set\n    if (!fromDate)\n        return jsx(Fragment, {});\n    if (!toDate)\n        return jsx(Fragment, {});\n    var dropdownMonths = [];\n    if (isSameYear(fromDate, toDate)) {\n        // only display the months included in the range\n        var date = startOfMonth(fromDate);\n        for (var month = fromDate.getMonth(); month <= toDate.getMonth(); month++) {\n            dropdownMonths.push(setMonth(date, month));\n        }\n    }\n    else {\n        // display all the 12 months\n        var date = startOfMonth(new Date()); // Any date should be OK, as we just need the year\n        for (var month = 0; month <= 11; month++) {\n            dropdownMonths.push(setMonth(date, month));\n        }\n    }\n    var handleChange = function (e) {\n        var selectedMonth = Number(e.target.value);\n        var newMonth = setMonth(startOfMonth(props.displayMonth), selectedMonth);\n        props.onChange(newMonth);\n    };\n    var DropdownComponent = (_a = components === null || components === void 0 ? void 0 : components.Dropdown) !== null && _a !== void 0 ? _a : Dropdown;\n    return (jsx(DropdownComponent, { name: \"months\", \"aria-label\": labelMonthDropdown(), className: classNames.dropdown_month, style: styles.dropdown_month, onChange: handleChange, value: props.displayMonth.getMonth(), caption: formatMonthCaption(props.displayMonth, { locale: locale }), children: dropdownMonths.map(function (m) { return (jsx(\"option\", { value: m.getMonth(), children: formatMonthCaption(m, { locale: locale }) }, m.getMonth())); }) }));\n}\n\n/**\n * Render a dropdown to change the year. Take in account the `nav.fromDate` and\n * `toDate` from context.\n */\nfunction YearsDropdown(props) {\n    var _a;\n    var displayMonth = props.displayMonth;\n    var _b = useDayPicker(), fromDate = _b.fromDate, toDate = _b.toDate, locale = _b.locale, styles = _b.styles, classNames = _b.classNames, components = _b.components, formatYearCaption = _b.formatters.formatYearCaption, labelYearDropdown = _b.labels.labelYearDropdown;\n    var years = [];\n    // Dropdown should appear only when both from/toDate is set\n    if (!fromDate)\n        return jsx(Fragment, {});\n    if (!toDate)\n        return jsx(Fragment, {});\n    var fromYear = fromDate.getFullYear();\n    var toYear = toDate.getFullYear();\n    for (var year = fromYear; year <= toYear; year++) {\n        years.push(setYear(startOfYear(new Date()), year));\n    }\n    var handleChange = function (e) {\n        var newMonth = setYear(startOfMonth(displayMonth), Number(e.target.value));\n        props.onChange(newMonth);\n    };\n    var DropdownComponent = (_a = components === null || components === void 0 ? void 0 : components.Dropdown) !== null && _a !== void 0 ? _a : Dropdown;\n    return (jsx(DropdownComponent, { name: \"years\", \"aria-label\": labelYearDropdown(), className: classNames.dropdown_year, style: styles.dropdown_year, onChange: handleChange, value: displayMonth.getFullYear(), caption: formatYearCaption(displayMonth, { locale: locale }), children: years.map(function (year) { return (jsx(\"option\", { value: year.getFullYear(), children: formatYearCaption(year, { locale: locale }) }, year.getFullYear())); }) }));\n}\n\n/**\n * Helper hook for using controlled/uncontrolled values from a component props.\n *\n * When the value is not controlled, pass `undefined` as `controlledValue` and\n * use the returned setter to update it.\n *\n * When the value is controlled, pass the controlled value as second\n * argument, which will be always returned as `value`.\n */\nfunction useControlledValue(defaultValue, controlledValue) {\n    var _a = useState(defaultValue), uncontrolledValue = _a[0], setValue = _a[1];\n    var value = controlledValue === undefined ? uncontrolledValue : controlledValue;\n    return [value, setValue];\n}\n\n/** Return the initial month according to the given options. */\nfunction getInitialMonth(context) {\n    var month = context.month, defaultMonth = context.defaultMonth, today = context.today;\n    var initialMonth = month || defaultMonth || today || new Date();\n    var toDate = context.toDate, fromDate = context.fromDate, _a = context.numberOfMonths, numberOfMonths = _a === void 0 ? 1 : _a;\n    // Fix the initialMonth if is after the to-date\n    if (toDate && differenceInCalendarMonths(toDate, initialMonth) < 0) {\n        var offset = -1 * (numberOfMonths - 1);\n        initialMonth = addMonths(toDate, offset);\n    }\n    // Fix the initialMonth if is before the from-date\n    if (fromDate && differenceInCalendarMonths(initialMonth, fromDate) < 0) {\n        initialMonth = fromDate;\n    }\n    return startOfMonth(initialMonth);\n}\n\n/** Controls the navigation state. */\nfunction useNavigationState() {\n    var context = useDayPicker();\n    var initialMonth = getInitialMonth(context);\n    var _a = useControlledValue(initialMonth, context.month), month = _a[0], setMonth = _a[1];\n    var goToMonth = function (date) {\n        var _a;\n        if (context.disableNavigation)\n            return;\n        var month = startOfMonth(date);\n        setMonth(month);\n        (_a = context.onMonthChange) === null || _a === void 0 ? void 0 : _a.call(context, month);\n    };\n    return [month, goToMonth];\n}\n\n/**\n * Return the months to display in the component according to the number of\n * months and the from/to date.\n */\nfunction getDisplayMonths(month, _a) {\n    var reverseMonths = _a.reverseMonths, numberOfMonths = _a.numberOfMonths;\n    var start = startOfMonth(month);\n    var end = startOfMonth(addMonths(start, numberOfMonths));\n    var monthsDiff = differenceInCalendarMonths(end, start);\n    var months = [];\n    for (var i = 0; i < monthsDiff; i++) {\n        var nextMonth = addMonths(start, i);\n        months.push(nextMonth);\n    }\n    if (reverseMonths)\n        months = months.reverse();\n    return months;\n}\n\n/**\n * Returns the next month the user can navigate to according to the given\n * options.\n *\n * Please note that the next month is not always the next calendar month:\n *\n * - if after the `toDate` range, is undefined;\n * - if the navigation is paged, is the number of months displayed ahead.\n *\n */\nfunction getNextMonth(startingMonth, options) {\n    if (options.disableNavigation) {\n        return undefined;\n    }\n    var toDate = options.toDate, pagedNavigation = options.pagedNavigation, _a = options.numberOfMonths, numberOfMonths = _a === void 0 ? 1 : _a;\n    var offset = pagedNavigation ? numberOfMonths : 1;\n    var month = startOfMonth(startingMonth);\n    if (!toDate) {\n        return addMonths(month, offset);\n    }\n    var monthsDiff = differenceInCalendarMonths(toDate, startingMonth);\n    if (monthsDiff < numberOfMonths) {\n        return undefined;\n    }\n    // Jump forward as the number of months when paged navigation\n    return addMonths(month, offset);\n}\n\n/**\n * Returns the next previous the user can navigate to, according to the given\n * options.\n *\n * Please note that the previous month is not always the previous calendar\n * month:\n *\n * - if before the `fromDate` date, is `undefined`;\n * - if the navigation is paged, is the number of months displayed before.\n *\n */\nfunction getPreviousMonth(startingMonth, options) {\n    if (options.disableNavigation) {\n        return undefined;\n    }\n    var fromDate = options.fromDate, pagedNavigation = options.pagedNavigation, _a = options.numberOfMonths, numberOfMonths = _a === void 0 ? 1 : _a;\n    var offset = pagedNavigation ? numberOfMonths : 1;\n    var month = startOfMonth(startingMonth);\n    if (!fromDate) {\n        return addMonths(month, -offset);\n    }\n    var monthsDiff = differenceInCalendarMonths(month, fromDate);\n    if (monthsDiff <= 0) {\n        return undefined;\n    }\n    // Jump back as the number of months when paged navigation\n    return addMonths(month, -offset);\n}\n\n/**\n * The Navigation context shares details and methods to navigate the months in DayPicker.\n * Access this context from the {@link useNavigation} hook.\n */\nvar NavigationContext = createContext(undefined);\n/** Provides the values for the {@link NavigationContext}. */\nfunction NavigationProvider(props) {\n    var dayPicker = useDayPicker();\n    var _a = useNavigationState(), currentMonth = _a[0], goToMonth = _a[1];\n    var displayMonths = getDisplayMonths(currentMonth, dayPicker);\n    var nextMonth = getNextMonth(currentMonth, dayPicker);\n    var previousMonth = getPreviousMonth(currentMonth, dayPicker);\n    var isDateDisplayed = function (date) {\n        return displayMonths.some(function (displayMonth) {\n            return isSameMonth(date, displayMonth);\n        });\n    };\n    var goToDate = function (date, refDate) {\n        if (isDateDisplayed(date)) {\n            return;\n        }\n        if (refDate && isBefore(date, refDate)) {\n            goToMonth(addMonths(date, 1 + dayPicker.numberOfMonths * -1));\n        }\n        else {\n            goToMonth(date);\n        }\n    };\n    var value = {\n        currentMonth: currentMonth,\n        displayMonths: displayMonths,\n        goToMonth: goToMonth,\n        goToDate: goToDate,\n        previousMonth: previousMonth,\n        nextMonth: nextMonth,\n        isDateDisplayed: isDateDisplayed\n    };\n    return (jsx(NavigationContext.Provider, { value: value, children: props.children }));\n}\n/**\n * Hook to access the {@link NavigationContextValue}. Use this hook to navigate\n * between months or years in DayPicker.\n *\n * This hook is meant to be used inside internal or custom components.\n */\nfunction useNavigation() {\n    var context = useContext(NavigationContext);\n    if (!context) {\n        throw new Error('useNavigation must be used within a NavigationProvider');\n    }\n    return context;\n}\n\n/**\n * Render a caption with the dropdowns to navigate between months and years.\n */\nfunction CaptionDropdowns(props) {\n    var _a;\n    var _b = useDayPicker(), classNames = _b.classNames, styles = _b.styles, components = _b.components;\n    var goToMonth = useNavigation().goToMonth;\n    var handleMonthChange = function (newMonth) {\n        goToMonth(addMonths(newMonth, props.displayIndex ? -props.displayIndex : 0));\n    };\n    var CaptionLabelComponent = (_a = components === null || components === void 0 ? void 0 : components.CaptionLabel) !== null && _a !== void 0 ? _a : CaptionLabel;\n    var captionLabel = (jsx(CaptionLabelComponent, { id: props.id, displayMonth: props.displayMonth }));\n    return (jsxs(\"div\", { className: classNames.caption_dropdowns, style: styles.caption_dropdowns, children: [jsx(\"div\", { className: classNames.vhidden, children: captionLabel }), jsx(MonthsDropdown, { onChange: handleMonthChange, displayMonth: props.displayMonth }), jsx(YearsDropdown, { onChange: handleMonthChange, displayMonth: props.displayMonth })] }));\n}\n\n/**\n * Render the \"previous month\" button in the navigation.\n */\nfunction IconLeft(props) {\n    return (jsx(\"svg\", __assign({ width: \"16px\", height: \"16px\", viewBox: \"0 0 120 120\" }, props, { children: jsx(\"path\", { d: \"M69.490332,3.34314575 C72.6145263,0.218951416 77.6798462,0.218951416 80.8040405,3.34314575 C83.8617626,6.40086786 83.9268205,11.3179931 80.9992143,14.4548388 L80.8040405,14.6568542 L35.461,60 L80.8040405,105.343146 C83.8617626,108.400868 83.9268205,113.317993 80.9992143,116.454839 L80.8040405,116.656854 C77.7463184,119.714576 72.8291931,119.779634 69.6923475,116.852028 L69.490332,116.656854 L18.490332,65.6568542 C15.4326099,62.5991321 15.367552,57.6820069 18.2951583,54.5451612 L18.490332,54.3431458 L69.490332,3.34314575 Z\", fill: \"currentColor\", fillRule: \"nonzero\" }) })));\n}\n\n/**\n * Render the \"next month\" button in the navigation.\n */\nfunction IconRight(props) {\n    return (jsx(\"svg\", __assign({ width: \"16px\", height: \"16px\", viewBox: \"0 0 120 120\" }, props, { children: jsx(\"path\", { d: \"M49.8040405,3.34314575 C46.6798462,0.218951416 41.6145263,0.218951416 38.490332,3.34314575 C35.4326099,6.40086786 35.367552,11.3179931 38.2951583,14.4548388 L38.490332,14.6568542 L83.8333725,60 L38.490332,105.343146 C35.4326099,108.400868 35.367552,113.317993 38.2951583,116.454839 L38.490332,116.656854 C41.5480541,119.714576 46.4651794,119.779634 49.602025,116.852028 L49.8040405,116.656854 L100.804041,65.6568542 C103.861763,62.5991321 103.926821,57.6820069 100.999214,54.5451612 L100.804041,54.3431458 L49.8040405,3.34314575 Z\", fill: \"currentColor\" }) })));\n}\n\n/** Render a button HTML element applying the reset class name. */\nvar Button = forwardRef(function (props, ref) {\n    var _a = useDayPicker(), classNames = _a.classNames, styles = _a.styles;\n    var classNamesArr = [classNames.button_reset, classNames.button];\n    if (props.className) {\n        classNamesArr.push(props.className);\n    }\n    var className = classNamesArr.join(' ');\n    var style = __assign(__assign({}, styles.button_reset), styles.button);\n    if (props.style) {\n        Object.assign(style, props.style);\n    }\n    return (jsx(\"button\", __assign({}, props, { ref: ref, type: \"button\", className: className, style: style })));\n});\n\n/** A component rendering the navigation buttons or the drop-downs. */\nfunction Navigation(props) {\n    var _a, _b;\n    var _c = useDayPicker(), dir = _c.dir, locale = _c.locale, classNames = _c.classNames, styles = _c.styles, _d = _c.labels, labelPrevious = _d.labelPrevious, labelNext = _d.labelNext, components = _c.components;\n    if (!props.nextMonth && !props.previousMonth) {\n        return jsx(Fragment, {});\n    }\n    var previousLabel = labelPrevious(props.previousMonth, { locale: locale });\n    var previousClassName = [\n        classNames.nav_button,\n        classNames.nav_button_previous\n    ].join(' ');\n    var nextLabel = labelNext(props.nextMonth, { locale: locale });\n    var nextClassName = [\n        classNames.nav_button,\n        classNames.nav_button_next\n    ].join(' ');\n    var IconRightComponent = (_a = components === null || components === void 0 ? void 0 : components.IconRight) !== null && _a !== void 0 ? _a : IconRight;\n    var IconLeftComponent = (_b = components === null || components === void 0 ? void 0 : components.IconLeft) !== null && _b !== void 0 ? _b : IconLeft;\n    return (jsxs(\"div\", { className: classNames.nav, style: styles.nav, children: [!props.hidePrevious && (jsx(Button, { name: \"previous-month\", \"aria-label\": previousLabel, className: previousClassName, style: styles.nav_button_previous, disabled: !props.previousMonth, onClick: props.onPreviousClick, children: dir === 'rtl' ? (jsx(IconRightComponent, { className: classNames.nav_icon, style: styles.nav_icon })) : (jsx(IconLeftComponent, { className: classNames.nav_icon, style: styles.nav_icon })) })), !props.hideNext && (jsx(Button, { name: \"next-month\", \"aria-label\": nextLabel, className: nextClassName, style: styles.nav_button_next, disabled: !props.nextMonth, onClick: props.onNextClick, children: dir === 'rtl' ? (jsx(IconLeftComponent, { className: classNames.nav_icon, style: styles.nav_icon })) : (jsx(IconRightComponent, { className: classNames.nav_icon, style: styles.nav_icon })) }))] }));\n}\n\n/**\n * Render a caption with a button-based navigation.\n */\nfunction CaptionNavigation(props) {\n    var numberOfMonths = useDayPicker().numberOfMonths;\n    var _a = useNavigation(), previousMonth = _a.previousMonth, nextMonth = _a.nextMonth, goToMonth = _a.goToMonth, displayMonths = _a.displayMonths;\n    var displayIndex = displayMonths.findIndex(function (month) {\n        return isSameMonth(props.displayMonth, month);\n    });\n    var isFirst = displayIndex === 0;\n    var isLast = displayIndex === displayMonths.length - 1;\n    var hideNext = numberOfMonths > 1 && (isFirst || !isLast);\n    var hidePrevious = numberOfMonths > 1 && (isLast || !isFirst);\n    var handlePreviousClick = function () {\n        if (!previousMonth)\n            return;\n        goToMonth(previousMonth);\n    };\n    var handleNextClick = function () {\n        if (!nextMonth)\n            return;\n        goToMonth(nextMonth);\n    };\n    return (jsx(Navigation, { displayMonth: props.displayMonth, hideNext: hideNext, hidePrevious: hidePrevious, nextMonth: nextMonth, previousMonth: previousMonth, onPreviousClick: handlePreviousClick, onNextClick: handleNextClick }));\n}\n\n/**\n * Render the caption of a month. The caption has a different layout when\n * setting the {@link DayPickerBase.captionLayout} prop.\n */\nfunction Caption(props) {\n    var _a;\n    var _b = useDayPicker(), classNames = _b.classNames, disableNavigation = _b.disableNavigation, styles = _b.styles, captionLayout = _b.captionLayout, components = _b.components;\n    var CaptionLabelComponent = (_a = components === null || components === void 0 ? void 0 : components.CaptionLabel) !== null && _a !== void 0 ? _a : CaptionLabel;\n    var caption;\n    if (disableNavigation) {\n        caption = (jsx(CaptionLabelComponent, { id: props.id, displayMonth: props.displayMonth }));\n    }\n    else if (captionLayout === 'dropdown') {\n        caption = (jsx(CaptionDropdowns, { displayMonth: props.displayMonth, id: props.id }));\n    }\n    else if (captionLayout === 'dropdown-buttons') {\n        caption = (jsxs(Fragment, { children: [jsx(CaptionDropdowns, { displayMonth: props.displayMonth, displayIndex: props.displayIndex, id: props.id }), jsx(CaptionNavigation, { displayMonth: props.displayMonth, displayIndex: props.displayIndex, id: props.id })] }));\n    }\n    else {\n        caption = (jsxs(Fragment, { children: [jsx(CaptionLabelComponent, { id: props.id, displayMonth: props.displayMonth, displayIndex: props.displayIndex }), jsx(CaptionNavigation, { displayMonth: props.displayMonth, id: props.id })] }));\n    }\n    return (jsx(\"div\", { className: classNames.caption, style: styles.caption, children: caption }));\n}\n\n/** Render the Footer component (empty as default).*/\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nfunction Footer(props) {\n    var _a = useDayPicker(), footer = _a.footer, styles = _a.styles, tfoot = _a.classNames.tfoot;\n    if (!footer)\n        return jsx(Fragment, {});\n    return (jsx(\"tfoot\", { className: tfoot, style: styles.tfoot, children: jsx(\"tr\", { children: jsx(\"td\", { colSpan: 8, children: footer }) }) }));\n}\n\n/**\n * Generate a series of 7 days, starting from the week, to use for formatting\n * the weekday names (Monday, Tuesday, etc.).\n */\nfunction getWeekdays(locale, \n/** The index of the first day of the week (0 - Sunday). */\nweekStartsOn, \n/** Use ISOWeek instead of locale/ */\nISOWeek) {\n    var start = ISOWeek\n        ? startOfISOWeek(new Date())\n        : startOfWeek(new Date(), { locale: locale, weekStartsOn: weekStartsOn });\n    var days = [];\n    for (var i = 0; i < 7; i++) {\n        var day = addDays(start, i);\n        days.push(day);\n    }\n    return days;\n}\n\n/**\n * Render the HeadRow component - i.e. the table head row with the weekday names.\n */\nfunction HeadRow() {\n    var _a = useDayPicker(), classNames = _a.classNames, styles = _a.styles, showWeekNumber = _a.showWeekNumber, locale = _a.locale, weekStartsOn = _a.weekStartsOn, ISOWeek = _a.ISOWeek, formatWeekdayName = _a.formatters.formatWeekdayName, labelWeekday = _a.labels.labelWeekday;\n    var weekdays = getWeekdays(locale, weekStartsOn, ISOWeek);\n    return (jsxs(\"tr\", { style: styles.head_row, className: classNames.head_row, children: [showWeekNumber && (jsx(\"td\", { style: styles.head_cell, className: classNames.head_cell })), weekdays.map(function (weekday, i) { return (jsx(\"th\", { scope: \"col\", className: classNames.head_cell, style: styles.head_cell, \"aria-label\": labelWeekday(weekday, { locale: locale }), children: formatWeekdayName(weekday, { locale: locale }) }, i)); })] }));\n}\n\n/** Render the table head. */\nfunction Head() {\n    var _a;\n    var _b = useDayPicker(), classNames = _b.classNames, styles = _b.styles, components = _b.components;\n    var HeadRowComponent = (_a = components === null || components === void 0 ? void 0 : components.HeadRow) !== null && _a !== void 0 ? _a : HeadRow;\n    return (jsx(\"thead\", { style: styles.head, className: classNames.head, children: jsx(HeadRowComponent, {}) }));\n}\n\n/** Render the content of the day cell. */\nfunction DayContent(props) {\n    var _a = useDayPicker(), locale = _a.locale, formatDay = _a.formatters.formatDay;\n    return jsx(Fragment, { children: formatDay(props.date, { locale: locale }) });\n}\n\n/**\n * The SelectMultiple context shares details about the selected days when in\n * multiple selection mode.\n *\n * Access this context from the {@link useSelectMultiple} hook.\n */\nvar SelectMultipleContext = createContext(undefined);\n/** Provides the values for the {@link SelectMultipleContext}. */\nfunction SelectMultipleProvider(props) {\n    if (!isDayPickerMultiple(props.initialProps)) {\n        var emptyContextValue = {\n            selected: undefined,\n            modifiers: {\n                disabled: []\n            }\n        };\n        return (jsx(SelectMultipleContext.Provider, { value: emptyContextValue, children: props.children }));\n    }\n    return (jsx(SelectMultipleProviderInternal, { initialProps: props.initialProps, children: props.children }));\n}\nfunction SelectMultipleProviderInternal(_a) {\n    var initialProps = _a.initialProps, children = _a.children;\n    var selected = initialProps.selected, min = initialProps.min, max = initialProps.max;\n    var onDayClick = function (day, activeModifiers, e) {\n        var _a, _b;\n        (_a = initialProps.onDayClick) === null || _a === void 0 ? void 0 : _a.call(initialProps, day, activeModifiers, e);\n        var isMinSelected = Boolean(activeModifiers.selected && min && (selected === null || selected === void 0 ? void 0 : selected.length) === min);\n        if (isMinSelected) {\n            return;\n        }\n        var isMaxSelected = Boolean(!activeModifiers.selected && max && (selected === null || selected === void 0 ? void 0 : selected.length) === max);\n        if (isMaxSelected) {\n            return;\n        }\n        var selectedDays = selected ? __spreadArray([], selected, true) : [];\n        if (activeModifiers.selected) {\n            var index = selectedDays.findIndex(function (selectedDay) {\n                return isSameDay(day, selectedDay);\n            });\n            selectedDays.splice(index, 1);\n        }\n        else {\n            selectedDays.push(day);\n        }\n        (_b = initialProps.onSelect) === null || _b === void 0 ? void 0 : _b.call(initialProps, selectedDays, day, activeModifiers, e);\n    };\n    var modifiers = {\n        disabled: []\n    };\n    if (selected) {\n        modifiers.disabled.push(function (day) {\n            var isMaxSelected = max && selected.length > max - 1;\n            var isSelected = selected.some(function (selectedDay) {\n                return isSameDay(selectedDay, day);\n            });\n            return Boolean(isMaxSelected && !isSelected);\n        });\n    }\n    var contextValue = {\n        selected: selected,\n        onDayClick: onDayClick,\n        modifiers: modifiers\n    };\n    return (jsx(SelectMultipleContext.Provider, { value: contextValue, children: children }));\n}\n/**\n * Hook to access the {@link SelectMultipleContextValue}.\n *\n * This hook is meant to be used inside internal or custom components.\n */\nfunction useSelectMultiple() {\n    var context = useContext(SelectMultipleContext);\n    if (!context) {\n        throw new Error('useSelectMultiple must be used within a SelectMultipleProvider');\n    }\n    return context;\n}\n\n/**\n * Add a day to an existing range.\n *\n * The returned range takes in account the `undefined` values and if the added\n * day is already present in the range.\n */\nfunction addToRange(day, range) {\n    var _a = range || {}, from = _a.from, to = _a.to;\n    if (from && to) {\n        if (isSameDay(to, day) && isSameDay(from, day)) {\n            return undefined;\n        }\n        if (isSameDay(to, day)) {\n            return { from: to, to: undefined };\n        }\n        if (isSameDay(from, day)) {\n            return undefined;\n        }\n        if (isAfter(from, day)) {\n            return { from: day, to: to };\n        }\n        return { from: from, to: day };\n    }\n    if (to) {\n        if (isAfter(day, to)) {\n            return { from: to, to: day };\n        }\n        return { from: day, to: to };\n    }\n    if (from) {\n        if (isBefore(day, from)) {\n            return { from: day, to: from };\n        }\n        return { from: from, to: day };\n    }\n    return { from: day, to: undefined };\n}\n\n/**\n * The SelectRange context shares details about the selected days when in\n * range selection mode.\n *\n * Access this context from the {@link useSelectRange} hook.\n */\nvar SelectRangeContext = createContext(undefined);\n/** Provides the values for the {@link SelectRangeProvider}. */\nfunction SelectRangeProvider(props) {\n    if (!isDayPickerRange(props.initialProps)) {\n        var emptyContextValue = {\n            selected: undefined,\n            modifiers: {\n                range_start: [],\n                range_end: [],\n                range_middle: [],\n                disabled: []\n            }\n        };\n        return (jsx(SelectRangeContext.Provider, { value: emptyContextValue, children: props.children }));\n    }\n    return (jsx(SelectRangeProviderInternal, { initialProps: props.initialProps, children: props.children }));\n}\nfunction SelectRangeProviderInternal(_a) {\n    var initialProps = _a.initialProps, children = _a.children;\n    var selected = initialProps.selected;\n    var _b = selected || {}, selectedFrom = _b.from, selectedTo = _b.to;\n    var min = initialProps.min;\n    var max = initialProps.max;\n    var onDayClick = function (day, activeModifiers, e) {\n        var _a, _b;\n        (_a = initialProps.onDayClick) === null || _a === void 0 ? void 0 : _a.call(initialProps, day, activeModifiers, e);\n        var newRange = addToRange(day, selected);\n        (_b = initialProps.onSelect) === null || _b === void 0 ? void 0 : _b.call(initialProps, newRange, day, activeModifiers, e);\n    };\n    var modifiers = {\n        range_start: [],\n        range_end: [],\n        range_middle: [],\n        disabled: []\n    };\n    if (selectedFrom) {\n        modifiers.range_start = [selectedFrom];\n        if (!selectedTo) {\n            modifiers.range_end = [selectedFrom];\n        }\n        else {\n            modifiers.range_end = [selectedTo];\n            if (!isSameDay(selectedFrom, selectedTo)) {\n                modifiers.range_middle = [\n                    {\n                        after: selectedFrom,\n                        before: selectedTo\n                    }\n                ];\n            }\n        }\n    }\n    else if (selectedTo) {\n        modifiers.range_start = [selectedTo];\n        modifiers.range_end = [selectedTo];\n    }\n    if (min) {\n        if (selectedFrom && !selectedTo) {\n            modifiers.disabled.push({\n                after: subDays(selectedFrom, min - 1),\n                before: addDays(selectedFrom, min - 1)\n            });\n        }\n        if (selectedFrom && selectedTo) {\n            modifiers.disabled.push({\n                after: selectedFrom,\n                before: addDays(selectedFrom, min - 1)\n            });\n        }\n        if (!selectedFrom && selectedTo) {\n            modifiers.disabled.push({\n                after: subDays(selectedTo, min - 1),\n                before: addDays(selectedTo, min - 1)\n            });\n        }\n    }\n    if (max) {\n        if (selectedFrom && !selectedTo) {\n            modifiers.disabled.push({\n                before: addDays(selectedFrom, -max + 1)\n            });\n            modifiers.disabled.push({\n                after: addDays(selectedFrom, max - 1)\n            });\n        }\n        if (selectedFrom && selectedTo) {\n            var selectedCount = differenceInCalendarDays(selectedTo, selectedFrom) + 1;\n            var offset = max - selectedCount;\n            modifiers.disabled.push({\n                before: subDays(selectedFrom, offset)\n            });\n            modifiers.disabled.push({\n                after: addDays(selectedTo, offset)\n            });\n        }\n        if (!selectedFrom && selectedTo) {\n            modifiers.disabled.push({\n                before: addDays(selectedTo, -max + 1)\n            });\n            modifiers.disabled.push({\n                after: addDays(selectedTo, max - 1)\n            });\n        }\n    }\n    return (jsx(SelectRangeContext.Provider, { value: { selected: selected, onDayClick: onDayClick, modifiers: modifiers }, children: children }));\n}\n/**\n * Hook to access the {@link SelectRangeContextValue}.\n *\n * This hook is meant to be used inside internal or custom components.\n */\nfunction useSelectRange() {\n    var context = useContext(SelectRangeContext);\n    if (!context) {\n        throw new Error('useSelectRange must be used within a SelectRangeProvider');\n    }\n    return context;\n}\n\n/** Normalize to array a matcher input. */\nfunction matcherToArray(matcher) {\n    if (Array.isArray(matcher)) {\n        return __spreadArray([], matcher, true);\n    }\n    else if (matcher !== undefined) {\n        return [matcher];\n    }\n    else {\n        return [];\n    }\n}\n\n/** Create CustomModifiers from dayModifiers */\nfunction getCustomModifiers(dayModifiers) {\n    var customModifiers = {};\n    Object.entries(dayModifiers).forEach(function (_a) {\n        var modifier = _a[0], matcher = _a[1];\n        customModifiers[modifier] = matcherToArray(matcher);\n    });\n    return customModifiers;\n}\n\n/** The name of the modifiers that are used internally by DayPicker. */\nvar InternalModifier;\n(function (InternalModifier) {\n    InternalModifier[\"Outside\"] = \"outside\";\n    /** Name of the modifier applied to the disabled days, using the `disabled` prop. */\n    InternalModifier[\"Disabled\"] = \"disabled\";\n    /** Name of the modifier applied to the selected days using the `selected` prop). */\n    InternalModifier[\"Selected\"] = \"selected\";\n    /** Name of the modifier applied to the hidden days using the `hidden` prop). */\n    InternalModifier[\"Hidden\"] = \"hidden\";\n    /** Name of the modifier applied to the day specified using the `today` prop). */\n    InternalModifier[\"Today\"] = \"today\";\n    /** The modifier applied to the day starting a selected range, when in range selection mode.  */\n    InternalModifier[\"RangeStart\"] = \"range_start\";\n    /** The modifier applied to the day ending a selected range, when in range selection mode.  */\n    InternalModifier[\"RangeEnd\"] = \"range_end\";\n    /** The modifier applied to the days between the start and the end of a selected range, when in range selection mode.  */\n    InternalModifier[\"RangeMiddle\"] = \"range_middle\";\n})(InternalModifier || (InternalModifier = {}));\n\nvar Selected = InternalModifier.Selected, Disabled = InternalModifier.Disabled, Hidden = InternalModifier.Hidden, Today = InternalModifier.Today, RangeEnd = InternalModifier.RangeEnd, RangeMiddle = InternalModifier.RangeMiddle, RangeStart = InternalModifier.RangeStart, Outside = InternalModifier.Outside;\n/** Return the {@link InternalModifiers} from the DayPicker and select contexts. */\nfunction getInternalModifiers(dayPicker, selectMultiple, selectRange) {\n    var _a;\n    var internalModifiers = (_a = {},\n        _a[Selected] = matcherToArray(dayPicker.selected),\n        _a[Disabled] = matcherToArray(dayPicker.disabled),\n        _a[Hidden] = matcherToArray(dayPicker.hidden),\n        _a[Today] = [dayPicker.today],\n        _a[RangeEnd] = [],\n        _a[RangeMiddle] = [],\n        _a[RangeStart] = [],\n        _a[Outside] = [],\n        _a);\n    if (dayPicker.fromDate) {\n        internalModifiers[Disabled].push({ before: dayPicker.fromDate });\n    }\n    if (dayPicker.toDate) {\n        internalModifiers[Disabled].push({ after: dayPicker.toDate });\n    }\n    if (isDayPickerMultiple(dayPicker)) {\n        internalModifiers[Disabled] = internalModifiers[Disabled].concat(selectMultiple.modifiers[Disabled]);\n    }\n    else if (isDayPickerRange(dayPicker)) {\n        internalModifiers[Disabled] = internalModifiers[Disabled].concat(selectRange.modifiers[Disabled]);\n        internalModifiers[RangeStart] = selectRange.modifiers[RangeStart];\n        internalModifiers[RangeMiddle] = selectRange.modifiers[RangeMiddle];\n        internalModifiers[RangeEnd] = selectRange.modifiers[RangeEnd];\n    }\n    return internalModifiers;\n}\n\n/** The Modifiers context store the modifiers used in DayPicker. To access the value of this context, use {@link useModifiers}. */\nvar ModifiersContext = createContext(undefined);\n/** Provide the value for the {@link ModifiersContext}. */\nfunction ModifiersProvider(props) {\n    var dayPicker = useDayPicker();\n    var selectMultiple = useSelectMultiple();\n    var selectRange = useSelectRange();\n    var internalModifiers = getInternalModifiers(dayPicker, selectMultiple, selectRange);\n    var customModifiers = getCustomModifiers(dayPicker.modifiers);\n    var modifiers = __assign(__assign({}, internalModifiers), customModifiers);\n    return (jsx(ModifiersContext.Provider, { value: modifiers, children: props.children }));\n}\n/**\n * Return the modifiers used by DayPicker.\n *\n * This hook is meant to be used inside internal or custom components.\n * Requires to be wrapped into {@link ModifiersProvider}.\n *\n */\nfunction useModifiers() {\n    var context = useContext(ModifiersContext);\n    if (!context) {\n        throw new Error('useModifiers must be used within a ModifiersProvider');\n    }\n    return context;\n}\n\n/** Returns true if `matcher` is of type {@link DateInterval}. */\nfunction isDateInterval(matcher) {\n    return Boolean(matcher &&\n        typeof matcher === 'object' &&\n        'before' in matcher &&\n        'after' in matcher);\n}\n/** Returns true if `value` is a {@link DateRange} type. */\nfunction isDateRange(value) {\n    return Boolean(value && typeof value === 'object' && 'from' in value);\n}\n/** Returns true if `value` is of type {@link DateAfter}. */\nfunction isDateAfterType(value) {\n    return Boolean(value && typeof value === 'object' && 'after' in value);\n}\n/** Returns true if `value` is of type {@link DateBefore}. */\nfunction isDateBeforeType(value) {\n    return Boolean(value && typeof value === 'object' && 'before' in value);\n}\n/** Returns true if `value` is a {@link DayOfWeek} type. */\nfunction isDayOfWeekType(value) {\n    return Boolean(value && typeof value === 'object' && 'dayOfWeek' in value);\n}\n\n/** Return `true` whether `date` is inside `range`. */\nfunction isDateInRange(date, range) {\n    var _a;\n    var from = range.from, to = range.to;\n    if (from && to) {\n        var isRangeInverted = differenceInCalendarDays(to, from) < 0;\n        if (isRangeInverted) {\n            _a = [to, from], from = _a[0], to = _a[1];\n        }\n        var isInRange = differenceInCalendarDays(date, from) >= 0 &&\n            differenceInCalendarDays(to, date) >= 0;\n        return isInRange;\n    }\n    if (to) {\n        return isSameDay(to, date);\n    }\n    if (from) {\n        return isSameDay(from, date);\n    }\n    return false;\n}\n\n/** Returns true if `value` is a Date type. */\nfunction isDateType(value) {\n    return isDate(value);\n}\n/** Returns true if `value` is an array of valid dates. */\nfunction isArrayOfDates(value) {\n    return Array.isArray(value) && value.every(isDate);\n}\n/**\n * Returns whether a day matches against at least one of the given Matchers.\n *\n * ```\n * const day = new Date(2022, 5, 19);\n * const matcher1: DateRange = {\n *    from: new Date(2021, 12, 21),\n *    to: new Date(2021, 12, 30)\n * }\n * const matcher2: DateRange = {\n *    from: new Date(2022, 5, 1),\n *    to: new Date(2022, 5, 23)\n * }\n *\n * const isMatch(day, [matcher1, matcher2]); // true, since day is in the matcher1 range.\n * ```\n * */\nfunction isMatch(day, matchers) {\n    return matchers.some(function (matcher) {\n        if (typeof matcher === 'boolean') {\n            return matcher;\n        }\n        if (isDateType(matcher)) {\n            return isSameDay(day, matcher);\n        }\n        if (isArrayOfDates(matcher)) {\n            return matcher.includes(day);\n        }\n        if (isDateRange(matcher)) {\n            return isDateInRange(day, matcher);\n        }\n        if (isDayOfWeekType(matcher)) {\n            return matcher.dayOfWeek.includes(day.getDay());\n        }\n        if (isDateInterval(matcher)) {\n            var diffBefore = differenceInCalendarDays(matcher.before, day);\n            var diffAfter = differenceInCalendarDays(matcher.after, day);\n            var isDayBefore = diffBefore > 0;\n            var isDayAfter = diffAfter < 0;\n            var isClosedInterval = isAfter(matcher.before, matcher.after);\n            if (isClosedInterval) {\n                return isDayAfter && isDayBefore;\n            }\n            else {\n                return isDayBefore || isDayAfter;\n            }\n        }\n        if (isDateAfterType(matcher)) {\n            return differenceInCalendarDays(day, matcher.after) > 0;\n        }\n        if (isDateBeforeType(matcher)) {\n            return differenceInCalendarDays(matcher.before, day) > 0;\n        }\n        if (typeof matcher === 'function') {\n            return matcher(day);\n        }\n        return false;\n    });\n}\n\n/** Return the active modifiers for the given day. */\nfunction getActiveModifiers(day, \n/** The modifiers to match for the given date. */\nmodifiers, \n/** The month where the day is displayed, to add the \"outside\" modifiers.  */\ndisplayMonth) {\n    var matchedModifiers = Object.keys(modifiers).reduce(function (result, key) {\n        var modifier = modifiers[key];\n        if (isMatch(day, modifier)) {\n            result.push(key);\n        }\n        return result;\n    }, []);\n    var activeModifiers = {};\n    matchedModifiers.forEach(function (modifier) { return (activeModifiers[modifier] = true); });\n    if (displayMonth && !isSameMonth(day, displayMonth)) {\n        activeModifiers.outside = true;\n    }\n    return activeModifiers;\n}\n\n/**\n * Returns the day that should be the target of the focus when DayPicker is\n * rendered the first time.\n *\n * TODO: this function doesn't consider if the day is outside the month. We\n * implemented this check in `useDayRender` but it should probably go here. See\n * https://github.com/gpbl/react-day-picker/pull/1576\n */\nfunction getInitialFocusTarget(displayMonths, modifiers) {\n    var firstDayInMonth = startOfMonth(displayMonths[0]);\n    var lastDayInMonth = endOfMonth(displayMonths[displayMonths.length - 1]);\n    // TODO: cleanup code\n    var firstFocusableDay;\n    var today;\n    var date = firstDayInMonth;\n    while (date <= lastDayInMonth) {\n        var activeModifiers = getActiveModifiers(date, modifiers);\n        var isFocusable = !activeModifiers.disabled && !activeModifiers.hidden;\n        if (!isFocusable) {\n            date = addDays(date, 1);\n            continue;\n        }\n        if (activeModifiers.selected) {\n            return date;\n        }\n        if (activeModifiers.today && !today) {\n            today = date;\n        }\n        if (!firstFocusableDay) {\n            firstFocusableDay = date;\n        }\n        date = addDays(date, 1);\n    }\n    if (today) {\n        return today;\n    }\n    else {\n        return firstFocusableDay;\n    }\n}\n\nvar MAX_RETRY = 365;\n/** Return the next date to be focused. */\nfunction getNextFocus(focusedDay, options) {\n    var moveBy = options.moveBy, direction = options.direction, context = options.context, modifiers = options.modifiers, _a = options.retry, retry = _a === void 0 ? { count: 0, lastFocused: focusedDay } : _a;\n    var weekStartsOn = context.weekStartsOn, fromDate = context.fromDate, toDate = context.toDate, locale = context.locale;\n    var moveFns = {\n        day: addDays,\n        week: addWeeks,\n        month: addMonths,\n        year: addYears,\n        startOfWeek: function (date) {\n            return context.ISOWeek\n                ? startOfISOWeek(date)\n                : startOfWeek(date, { locale: locale, weekStartsOn: weekStartsOn });\n        },\n        endOfWeek: function (date) {\n            return context.ISOWeek\n                ? endOfISOWeek(date)\n                : endOfWeek(date, { locale: locale, weekStartsOn: weekStartsOn });\n        }\n    };\n    var newFocusedDay = moveFns[moveBy](focusedDay, direction === 'after' ? 1 : -1);\n    if (direction === 'before' && fromDate) {\n        newFocusedDay = max([fromDate, newFocusedDay]);\n    }\n    else if (direction === 'after' && toDate) {\n        newFocusedDay = min([toDate, newFocusedDay]);\n    }\n    var isFocusable = true;\n    if (modifiers) {\n        var activeModifiers = getActiveModifiers(newFocusedDay, modifiers);\n        isFocusable = !activeModifiers.disabled && !activeModifiers.hidden;\n    }\n    if (isFocusable) {\n        return newFocusedDay;\n    }\n    else {\n        if (retry.count > MAX_RETRY) {\n            return retry.lastFocused;\n        }\n        return getNextFocus(newFocusedDay, {\n            moveBy: moveBy,\n            direction: direction,\n            context: context,\n            modifiers: modifiers,\n            retry: __assign(__assign({}, retry), { count: retry.count + 1 })\n        });\n    }\n}\n\n/**\n * The Focus context shares details about the focused day for the keyboard\n *\n * Access this context from the {@link useFocusContext} hook.\n */\nvar FocusContext = createContext(undefined);\n/** The provider for the {@link FocusContext}. */\nfunction FocusProvider(props) {\n    var navigation = useNavigation();\n    var modifiers = useModifiers();\n    var _a = useState(), focusedDay = _a[0], setFocusedDay = _a[1];\n    var _b = useState(), lastFocused = _b[0], setLastFocused = _b[1];\n    var initialFocusTarget = getInitialFocusTarget(navigation.displayMonths, modifiers);\n    // TODO: cleanup and test obscure code below\n    var focusTarget = (focusedDay !== null && focusedDay !== void 0 ? focusedDay : (lastFocused && navigation.isDateDisplayed(lastFocused)))\n        ? lastFocused\n        : initialFocusTarget;\n    var blur = function () {\n        setLastFocused(focusedDay);\n        setFocusedDay(undefined);\n    };\n    var focus = function (date) {\n        setFocusedDay(date);\n    };\n    var context = useDayPicker();\n    var moveFocus = function (moveBy, direction) {\n        if (!focusedDay)\n            return;\n        var nextFocused = getNextFocus(focusedDay, {\n            moveBy: moveBy,\n            direction: direction,\n            context: context,\n            modifiers: modifiers\n        });\n        if (isSameDay(focusedDay, nextFocused))\n            return undefined;\n        navigation.goToDate(nextFocused, focusedDay);\n        focus(nextFocused);\n    };\n    var value = {\n        focusedDay: focusedDay,\n        focusTarget: focusTarget,\n        blur: blur,\n        focus: focus,\n        focusDayAfter: function () { return moveFocus('day', 'after'); },\n        focusDayBefore: function () { return moveFocus('day', 'before'); },\n        focusWeekAfter: function () { return moveFocus('week', 'after'); },\n        focusWeekBefore: function () { return moveFocus('week', 'before'); },\n        focusMonthBefore: function () { return moveFocus('month', 'before'); },\n        focusMonthAfter: function () { return moveFocus('month', 'after'); },\n        focusYearBefore: function () { return moveFocus('year', 'before'); },\n        focusYearAfter: function () { return moveFocus('year', 'after'); },\n        focusStartOfWeek: function () { return moveFocus('startOfWeek', 'before'); },\n        focusEndOfWeek: function () { return moveFocus('endOfWeek', 'after'); }\n    };\n    return (jsx(FocusContext.Provider, { value: value, children: props.children }));\n}\n/**\n * Hook to access the {@link FocusContextValue}. Use this hook to handle the\n * focus state of the elements.\n *\n * This hook is meant to be used inside internal or custom components.\n */\nfunction useFocusContext() {\n    var context = useContext(FocusContext);\n    if (!context) {\n        throw new Error('useFocusContext must be used within a FocusProvider');\n    }\n    return context;\n}\n\n/**\n * Return the active modifiers for the specified day.\n *\n * This hook is meant to be used inside internal or custom components.\n *\n * @param day\n * @param displayMonth\n */\nfunction useActiveModifiers(day, \n/**\n * The month where the date is displayed. If not the same as `date`, the day\n * is an \"outside day\".\n */\ndisplayMonth) {\n    var modifiers = useModifiers();\n    var activeModifiers = getActiveModifiers(day, modifiers, displayMonth);\n    return activeModifiers;\n}\n\n/**\n * The SelectSingle context shares details about the selected days when in\n * single selection mode.\n *\n * Access this context from the {@link useSelectSingle} hook.\n */\nvar SelectSingleContext = createContext(undefined);\n/** Provides the values for the {@link SelectSingleProvider}. */\nfunction SelectSingleProvider(props) {\n    if (!isDayPickerSingle(props.initialProps)) {\n        var emptyContextValue = {\n            selected: undefined\n        };\n        return (jsx(SelectSingleContext.Provider, { value: emptyContextValue, children: props.children }));\n    }\n    return (jsx(SelectSingleProviderInternal, { initialProps: props.initialProps, children: props.children }));\n}\nfunction SelectSingleProviderInternal(_a) {\n    var initialProps = _a.initialProps, children = _a.children;\n    var onDayClick = function (day, activeModifiers, e) {\n        var _a, _b, _c;\n        (_a = initialProps.onDayClick) === null || _a === void 0 ? void 0 : _a.call(initialProps, day, activeModifiers, e);\n        if (activeModifiers.selected && !initialProps.required) {\n            (_b = initialProps.onSelect) === null || _b === void 0 ? void 0 : _b.call(initialProps, undefined, day, activeModifiers, e);\n            return;\n        }\n        (_c = initialProps.onSelect) === null || _c === void 0 ? void 0 : _c.call(initialProps, day, day, activeModifiers, e);\n    };\n    var contextValue = {\n        selected: initialProps.selected,\n        onDayClick: onDayClick\n    };\n    return (jsx(SelectSingleContext.Provider, { value: contextValue, children: children }));\n}\n/**\n * Hook to access the {@link SelectSingleContextValue}.\n *\n * This hook is meant to be used inside internal or custom components.\n */\nfunction useSelectSingle() {\n    var context = useContext(SelectSingleContext);\n    if (!context) {\n        throw new Error('useSelectSingle must be used within a SelectSingleProvider');\n    }\n    return context;\n}\n\n/**\n * This hook returns details about the content to render in the day cell.\n *\n *\n * When a day cell is rendered in the table, DayPicker can either:\n *\n * - render nothing: when the day is outside the month or has matched the\n *   \"hidden\" modifier.\n * - render a button when `onDayClick` or a selection mode is set.\n * - render a non-interactive element: when no selection mode is set, the day\n *   cell shouldn’t respond to any interaction. DayPicker should render a `div`\n *   or a `span`.\n *\n * ### Usage\n *\n * Use this hook to customize the behavior of the {@link Day} component. Create a\n * new `Day` component using this hook and pass it to the `components` prop.\n * The source of {@link Day} can be a good starting point.\n *\n */\nfunction useDayEventHandlers(date, activeModifiers) {\n    var dayPicker = useDayPicker();\n    var single = useSelectSingle();\n    var multiple = useSelectMultiple();\n    var range = useSelectRange();\n    var _a = useFocusContext(), focusDayAfter = _a.focusDayAfter, focusDayBefore = _a.focusDayBefore, focusWeekAfter = _a.focusWeekAfter, focusWeekBefore = _a.focusWeekBefore, blur = _a.blur, focus = _a.focus, focusMonthBefore = _a.focusMonthBefore, focusMonthAfter = _a.focusMonthAfter, focusYearBefore = _a.focusYearBefore, focusYearAfter = _a.focusYearAfter, focusStartOfWeek = _a.focusStartOfWeek, focusEndOfWeek = _a.focusEndOfWeek;\n    var onClick = function (e) {\n        var _a, _b, _c, _d;\n        if (isDayPickerSingle(dayPicker)) {\n            (_a = single.onDayClick) === null || _a === void 0 ? void 0 : _a.call(single, date, activeModifiers, e);\n        }\n        else if (isDayPickerMultiple(dayPicker)) {\n            (_b = multiple.onDayClick) === null || _b === void 0 ? void 0 : _b.call(multiple, date, activeModifiers, e);\n        }\n        else if (isDayPickerRange(dayPicker)) {\n            (_c = range.onDayClick) === null || _c === void 0 ? void 0 : _c.call(range, date, activeModifiers, e);\n        }\n        else {\n            (_d = dayPicker.onDayClick) === null || _d === void 0 ? void 0 : _d.call(dayPicker, date, activeModifiers, e);\n        }\n    };\n    var onFocus = function (e) {\n        var _a;\n        focus(date);\n        (_a = dayPicker.onDayFocus) === null || _a === void 0 ? void 0 : _a.call(dayPicker, date, activeModifiers, e);\n    };\n    var onBlur = function (e) {\n        var _a;\n        blur();\n        (_a = dayPicker.onDayBlur) === null || _a === void 0 ? void 0 : _a.call(dayPicker, date, activeModifiers, e);\n    };\n    var onMouseEnter = function (e) {\n        var _a;\n        (_a = dayPicker.onDayMouseEnter) === null || _a === void 0 ? void 0 : _a.call(dayPicker, date, activeModifiers, e);\n    };\n    var onMouseLeave = function (e) {\n        var _a;\n        (_a = dayPicker.onDayMouseLeave) === null || _a === void 0 ? void 0 : _a.call(dayPicker, date, activeModifiers, e);\n    };\n    var onPointerEnter = function (e) {\n        var _a;\n        (_a = dayPicker.onDayPointerEnter) === null || _a === void 0 ? void 0 : _a.call(dayPicker, date, activeModifiers, e);\n    };\n    var onPointerLeave = function (e) {\n        var _a;\n        (_a = dayPicker.onDayPointerLeave) === null || _a === void 0 ? void 0 : _a.call(dayPicker, date, activeModifiers, e);\n    };\n    var onTouchCancel = function (e) {\n        var _a;\n        (_a = dayPicker.onDayTouchCancel) === null || _a === void 0 ? void 0 : _a.call(dayPicker, date, activeModifiers, e);\n    };\n    var onTouchEnd = function (e) {\n        var _a;\n        (_a = dayPicker.onDayTouchEnd) === null || _a === void 0 ? void 0 : _a.call(dayPicker, date, activeModifiers, e);\n    };\n    var onTouchMove = function (e) {\n        var _a;\n        (_a = dayPicker.onDayTouchMove) === null || _a === void 0 ? void 0 : _a.call(dayPicker, date, activeModifiers, e);\n    };\n    var onTouchStart = function (e) {\n        var _a;\n        (_a = dayPicker.onDayTouchStart) === null || _a === void 0 ? void 0 : _a.call(dayPicker, date, activeModifiers, e);\n    };\n    var onKeyUp = function (e) {\n        var _a;\n        (_a = dayPicker.onDayKeyUp) === null || _a === void 0 ? void 0 : _a.call(dayPicker, date, activeModifiers, e);\n    };\n    var onKeyDown = function (e) {\n        var _a;\n        switch (e.key) {\n            case 'ArrowLeft':\n                e.preventDefault();\n                e.stopPropagation();\n                dayPicker.dir === 'rtl' ? focusDayAfter() : focusDayBefore();\n                break;\n            case 'ArrowRight':\n                e.preventDefault();\n                e.stopPropagation();\n                dayPicker.dir === 'rtl' ? focusDayBefore() : focusDayAfter();\n                break;\n            case 'ArrowDown':\n                e.preventDefault();\n                e.stopPropagation();\n                focusWeekAfter();\n                break;\n            case 'ArrowUp':\n                e.preventDefault();\n                e.stopPropagation();\n                focusWeekBefore();\n                break;\n            case 'PageUp':\n                e.preventDefault();\n                e.stopPropagation();\n                e.shiftKey ? focusYearBefore() : focusMonthBefore();\n                break;\n            case 'PageDown':\n                e.preventDefault();\n                e.stopPropagation();\n                e.shiftKey ? focusYearAfter() : focusMonthAfter();\n                break;\n            case 'Home':\n                e.preventDefault();\n                e.stopPropagation();\n                focusStartOfWeek();\n                break;\n            case 'End':\n                e.preventDefault();\n                e.stopPropagation();\n                focusEndOfWeek();\n                break;\n        }\n        (_a = dayPicker.onDayKeyDown) === null || _a === void 0 ? void 0 : _a.call(dayPicker, date, activeModifiers, e);\n    };\n    var eventHandlers = {\n        onClick: onClick,\n        onFocus: onFocus,\n        onBlur: onBlur,\n        onKeyDown: onKeyDown,\n        onKeyUp: onKeyUp,\n        onMouseEnter: onMouseEnter,\n        onMouseLeave: onMouseLeave,\n        onPointerEnter: onPointerEnter,\n        onPointerLeave: onPointerLeave,\n        onTouchCancel: onTouchCancel,\n        onTouchEnd: onTouchEnd,\n        onTouchMove: onTouchMove,\n        onTouchStart: onTouchStart\n    };\n    return eventHandlers;\n}\n\n/**\n * Return the current selected days when DayPicker is in selection mode. Days\n * selected by the custom selection mode are not returned.\n *\n * This hook is meant to be used inside internal or custom components.\n *\n */\nfunction useSelectedDays() {\n    var dayPicker = useDayPicker();\n    var single = useSelectSingle();\n    var multiple = useSelectMultiple();\n    var range = useSelectRange();\n    var selectedDays = isDayPickerSingle(dayPicker)\n        ? single.selected\n        : isDayPickerMultiple(dayPicker)\n            ? multiple.selected\n            : isDayPickerRange(dayPicker)\n                ? range.selected\n                : undefined;\n    return selectedDays;\n}\n\nfunction isInternalModifier(modifier) {\n    return Object.values(InternalModifier).includes(modifier);\n}\n/**\n * Return the class names for the Day element, according to the given active\n * modifiers.\n *\n * Custom class names are set via `modifiersClassNames` or `classNames`,\n * where the first have the precedence.\n */\nfunction getDayClassNames(dayPicker, activeModifiers) {\n    var classNames = [dayPicker.classNames.day];\n    Object.keys(activeModifiers).forEach(function (modifier) {\n        var customClassName = dayPicker.modifiersClassNames[modifier];\n        if (customClassName) {\n            classNames.push(customClassName);\n        }\n        else if (isInternalModifier(modifier)) {\n            var internalClassName = dayPicker.classNames[\"day_\".concat(modifier)];\n            if (internalClassName) {\n                classNames.push(internalClassName);\n            }\n        }\n    });\n    return classNames;\n}\n\n/** Return the style for the Day element, according to the given active modifiers. */\nfunction getDayStyle(dayPicker, activeModifiers) {\n    var style = __assign({}, dayPicker.styles.day);\n    Object.keys(activeModifiers).forEach(function (modifier) {\n        var _a;\n        style = __assign(__assign({}, style), (_a = dayPicker.modifiersStyles) === null || _a === void 0 ? void 0 : _a[modifier]);\n    });\n    return style;\n}\n\n/**\n * Return props and data used to render the {@link Day} component.\n *\n * Use this hook when creating a component to replace the built-in `Day`\n * component.\n */\nfunction useDayRender(\n/** The date to render. */\nday, \n/** The month where the date is displayed (if not the same as `date`, it means it is an \"outside\" day). */\ndisplayMonth, \n/** A ref to the button element that will be target of focus when rendered (if required). */\nbuttonRef) {\n    var _a;\n    var _b, _c;\n    var dayPicker = useDayPicker();\n    var focusContext = useFocusContext();\n    var activeModifiers = useActiveModifiers(day, displayMonth);\n    var eventHandlers = useDayEventHandlers(day, activeModifiers);\n    var selectedDays = useSelectedDays();\n    var isButton = Boolean(dayPicker.onDayClick || dayPicker.mode !== 'default');\n    // Focus the button if the day is focused according to the focus context\n    useEffect(function () {\n        var _a;\n        if (activeModifiers.outside)\n            return;\n        if (!focusContext.focusedDay)\n            return;\n        if (!isButton)\n            return;\n        if (isSameDay(focusContext.focusedDay, day)) {\n            (_a = buttonRef.current) === null || _a === void 0 ? void 0 : _a.focus();\n        }\n    }, [\n        focusContext.focusedDay,\n        day,\n        buttonRef,\n        isButton,\n        activeModifiers.outside\n    ]);\n    var className = getDayClassNames(dayPicker, activeModifiers).join(' ');\n    var style = getDayStyle(dayPicker, activeModifiers);\n    var isHidden = Boolean((activeModifiers.outside && !dayPicker.showOutsideDays) ||\n        activeModifiers.hidden);\n    var DayContentComponent = (_c = (_b = dayPicker.components) === null || _b === void 0 ? void 0 : _b.DayContent) !== null && _c !== void 0 ? _c : DayContent;\n    var children = (jsx(DayContentComponent, { date: day, displayMonth: displayMonth, activeModifiers: activeModifiers }));\n    var divProps = {\n        style: style,\n        className: className,\n        children: children,\n        role: 'gridcell'\n    };\n    var isFocusTarget = focusContext.focusTarget &&\n        isSameDay(focusContext.focusTarget, day) &&\n        !activeModifiers.outside;\n    var isFocused = focusContext.focusedDay && isSameDay(focusContext.focusedDay, day);\n    var buttonProps = __assign(__assign(__assign({}, divProps), (_a = { disabled: activeModifiers.disabled, role: 'gridcell' }, _a['aria-selected'] = activeModifiers.selected, _a.tabIndex = isFocused || isFocusTarget ? 0 : -1, _a)), eventHandlers);\n    var dayRender = {\n        isButton: isButton,\n        isHidden: isHidden,\n        activeModifiers: activeModifiers,\n        selectedDays: selectedDays,\n        buttonProps: buttonProps,\n        divProps: divProps\n    };\n    return dayRender;\n}\n\n/**\n * The content of a day cell – as a button or span element according to its\n * modifiers.\n */\nfunction Day(props) {\n    var buttonRef = useRef(null);\n    var dayRender = useDayRender(props.date, props.displayMonth, buttonRef);\n    if (dayRender.isHidden) {\n        return jsx(\"div\", { role: \"gridcell\" });\n    }\n    if (!dayRender.isButton) {\n        return jsx(\"div\", __assign({}, dayRender.divProps));\n    }\n    return jsx(Button, __assign({ name: \"day\", ref: buttonRef }, dayRender.buttonProps));\n}\n\n/**\n * Render the week number element. If `onWeekNumberClick` is passed to DayPicker, it\n * renders a button, otherwise a span element.\n */\nfunction WeekNumber(props) {\n    var weekNumber = props.number, dates = props.dates;\n    var _a = useDayPicker(), onWeekNumberClick = _a.onWeekNumberClick, styles = _a.styles, classNames = _a.classNames, locale = _a.locale, labelWeekNumber = _a.labels.labelWeekNumber, formatWeekNumber = _a.formatters.formatWeekNumber;\n    var content = formatWeekNumber(Number(weekNumber), { locale: locale });\n    if (!onWeekNumberClick) {\n        return (jsx(\"span\", { className: classNames.weeknumber, style: styles.weeknumber, children: content }));\n    }\n    var label = labelWeekNumber(Number(weekNumber), { locale: locale });\n    var handleClick = function (e) {\n        onWeekNumberClick(weekNumber, dates, e);\n    };\n    return (jsx(Button, { name: \"week-number\", \"aria-label\": label, className: classNames.weeknumber, style: styles.weeknumber, onClick: handleClick, children: content }));\n}\n\n/** Render a row in the calendar, with the days and the week number. */\nfunction Row(props) {\n    var _a, _b;\n    var _c = useDayPicker(), styles = _c.styles, classNames = _c.classNames, showWeekNumber = _c.showWeekNumber, components = _c.components;\n    var DayComponent = (_a = components === null || components === void 0 ? void 0 : components.Day) !== null && _a !== void 0 ? _a : Day;\n    var WeeknumberComponent = (_b = components === null || components === void 0 ? void 0 : components.WeekNumber) !== null && _b !== void 0 ? _b : WeekNumber;\n    var weekNumberCell;\n    if (showWeekNumber) {\n        weekNumberCell = (jsx(\"td\", { className: classNames.cell, style: styles.cell, children: jsx(WeeknumberComponent, { number: props.weekNumber, dates: props.dates }) }));\n    }\n    return (jsxs(\"tr\", { className: classNames.row, style: styles.row, children: [weekNumberCell, props.dates.map(function (date) { return (jsx(\"td\", { className: classNames.cell, style: styles.cell, role: \"presentation\", children: jsx(DayComponent, { displayMonth: props.displayMonth, date: date }) }, getUnixTime(date))); })] }));\n}\n\n/** Return the weeks between two dates.  */\nfunction daysToMonthWeeks(fromDate, toDate, options) {\n    var toWeek = (options === null || options === void 0 ? void 0 : options.ISOWeek)\n        ? endOfISOWeek(toDate)\n        : endOfWeek(toDate, options);\n    var fromWeek = (options === null || options === void 0 ? void 0 : options.ISOWeek)\n        ? startOfISOWeek(fromDate)\n        : startOfWeek(fromDate, options);\n    var nOfDays = differenceInCalendarDays(toWeek, fromWeek);\n    var days = [];\n    for (var i = 0; i <= nOfDays; i++) {\n        days.push(addDays(fromWeek, i));\n    }\n    var weeksInMonth = days.reduce(function (result, date) {\n        var weekNumber = (options === null || options === void 0 ? void 0 : options.ISOWeek)\n            ? getISOWeek(date)\n            : getWeek(date, options);\n        var existingWeek = result.find(function (value) { return value.weekNumber === weekNumber; });\n        if (existingWeek) {\n            existingWeek.dates.push(date);\n            return result;\n        }\n        result.push({\n            weekNumber: weekNumber,\n            dates: [date]\n        });\n        return result;\n    }, []);\n    return weeksInMonth;\n}\n\n/**\n * Return the weeks belonging to the given month, adding the \"outside days\" to\n * the first and last week.\n */\nfunction getMonthWeeks(month, options) {\n    var weeksInMonth = daysToMonthWeeks(startOfMonth(month), endOfMonth(month), options);\n    if (options === null || options === void 0 ? void 0 : options.useFixedWeeks) {\n        // Add extra weeks to the month, up to 6 weeks\n        var nrOfMonthWeeks = getWeeksInMonth(month, options);\n        if (nrOfMonthWeeks < 6) {\n            var lastWeek = weeksInMonth[weeksInMonth.length - 1];\n            var lastDate = lastWeek.dates[lastWeek.dates.length - 1];\n            var toDate = addWeeks(lastDate, 6 - nrOfMonthWeeks);\n            var extraWeeks = daysToMonthWeeks(addWeeks(lastDate, 1), toDate, options);\n            weeksInMonth.push.apply(weeksInMonth, extraWeeks);\n        }\n    }\n    return weeksInMonth;\n}\n\n/** Render the table with the calendar. */\nfunction Table(props) {\n    var _a, _b, _c;\n    var _d = useDayPicker(), locale = _d.locale, classNames = _d.classNames, styles = _d.styles, hideHead = _d.hideHead, fixedWeeks = _d.fixedWeeks, components = _d.components, weekStartsOn = _d.weekStartsOn, firstWeekContainsDate = _d.firstWeekContainsDate, ISOWeek = _d.ISOWeek;\n    var weeks = getMonthWeeks(props.displayMonth, {\n        useFixedWeeks: Boolean(fixedWeeks),\n        ISOWeek: ISOWeek,\n        locale: locale,\n        weekStartsOn: weekStartsOn,\n        firstWeekContainsDate: firstWeekContainsDate\n    });\n    var HeadComponent = (_a = components === null || components === void 0 ? void 0 : components.Head) !== null && _a !== void 0 ? _a : Head;\n    var RowComponent = (_b = components === null || components === void 0 ? void 0 : components.Row) !== null && _b !== void 0 ? _b : Row;\n    var FooterComponent = (_c = components === null || components === void 0 ? void 0 : components.Footer) !== null && _c !== void 0 ? _c : Footer;\n    return (jsxs(\"table\", { id: props.id, className: classNames.table, style: styles.table, role: \"grid\", \"aria-labelledby\": props['aria-labelledby'], children: [!hideHead && jsx(HeadComponent, {}), jsx(\"tbody\", { className: classNames.tbody, style: styles.tbody, children: weeks.map(function (week) { return (jsx(RowComponent, { displayMonth: props.displayMonth, dates: week.dates, weekNumber: week.weekNumber }, week.weekNumber)); }) }), jsx(FooterComponent, { displayMonth: props.displayMonth })] }));\n}\n\n/*\nThe MIT License (MIT)\n\nCopyright (c) 2018-present, React Training LLC\n\nPermission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the \"Software\"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n*/\n/* eslint-disable prefer-const */\n/* eslint-disable @typescript-eslint/ban-ts-comment */\n/*\n * Welcome to @reach/auto-id!\n * Let's see if we can make sense of why this hook exists and its\n * implementation.\n *\n * Some background:\n *   1. Accessibility APIs rely heavily on element IDs\n *   2. Requiring developers to put IDs on every element in Reach UI is both\n *      cumbersome and error-prone\n *   3. With a component model, we can generate IDs for them!\n *\n * Solution 1: Generate random IDs.\n *\n * This works great as long as you don't server render your app. When React (in\n * the client) tries to reuse the markup from the server, the IDs won't match\n * and React will then recreate the entire DOM tree.\n *\n * Solution 2: Increment an integer\n *\n * This sounds great. Since we're rendering the exact same tree on the server\n * and client, we can increment a counter and get a deterministic result between\n * client and server. Also, JS integers can go up to nine-quadrillion. I'm\n * pretty sure the tab will be closed before an app never needs\n * 10 quadrillion IDs!\n *\n * Problem solved, right?\n *\n * Ah, but there's a catch! React's concurrent rendering makes this approach\n * non-deterministic. While the client and server will end up with the same\n * elements in the end, depending on suspense boundaries (and possibly some user\n * input during the initial render) the incrementing integers won't always match\n * up.\n *\n * Solution 3: Don't use IDs at all on the server; patch after first render.\n *\n * What we've done here is solution 2 with some tricks. With this approach, the\n * ID returned is an empty string on the first render. This way the server and\n * client have the same markup no matter how wild the concurrent rendering may\n * have gotten.\n *\n * After the render, we patch up the components with an incremented ID. This\n * causes a double render on any components with `useId`. Shouldn't be a problem\n * since the components using this hook should be small, and we're only updating\n * the ID attribute on the DOM, nothing big is happening.\n *\n * It doesn't have to be an incremented number, though--we could do generate\n * random strings instead, but incrementing a number is probably the cheapest\n * thing we can do.\n *\n * Additionally, we only do this patchup on the very first client render ever.\n * Any calls to `useId` that happen dynamically in the client will be\n * populated immediately with a value. So, we only get the double render after\n * server hydration and never again, SO BACK OFF ALRIGHT?\n */\nfunction canUseDOM() {\n    return !!(typeof window !== 'undefined' &&\n        window.document &&\n        window.document.createElement);\n}\n/**\n * React currently throws a warning when using useLayoutEffect on the server. To\n * get around it, we can conditionally useEffect on the server (no-op) and\n * useLayoutEffect in the browser. We occasionally need useLayoutEffect to\n * ensure we don't get a render flash for certain operations, but we may also\n * need affected components to render on the server. One example is when setting\n * a component's descendants to retrieve their index values.\n *\n * Important to note that using this hook as an escape hatch will break the\n * eslint dependency warnings unless you rename the import to `useLayoutEffect`.\n * Use sparingly only when the effect won't effect the rendered HTML to avoid\n * any server/client mismatch.\n *\n * If a useLayoutEffect is needed and the result would create a mismatch, it's\n * likely that the component in question shouldn't be rendered on the server at\n * all, so a better approach would be to lazily render those in a parent\n * component after client-side hydration.\n *\n * https://gist.github.com/gaearon/e7d97cdf38a2907924ea12e4ebdf3c85\n * https://github.com/reduxjs/react-redux/blob/master/src/utils/useIsomorphicLayoutEffect.js\n *\n * @param effect\n * @param deps\n */\nvar useIsomorphicLayoutEffect = canUseDOM() ? useLayoutEffect : useEffect;\nvar serverHandoffComplete = false;\nvar id = 0;\nfunction genId() {\n    return \"react-day-picker-\".concat(++id);\n}\nfunction useId(providedId) {\n    // TODO: Remove error flag when updating internal deps to React 18. None of\n    // our tricks will play well with concurrent rendering anyway.\n    var _a;\n    // If this instance isn't part of the initial render, we don't have to do the\n    // double render/patch-up dance. We can just generate the ID and return it.\n    var initialId = providedId !== null && providedId !== void 0 ? providedId : (serverHandoffComplete ? genId() : null);\n    var _b = useState(initialId), id = _b[0], setId = _b[1];\n    useIsomorphicLayoutEffect(function () {\n        if (id === null) {\n            // Patch the ID after render. We do this in `useLayoutEffect` to avoid any\n            // rendering flicker, though it'll make the first render slower (unlikely\n            // to matter, but you're welcome to measure your app and let us know if\n            // it's a problem).\n            setId(genId());\n        }\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, []);\n    useEffect(function () {\n        if (serverHandoffComplete === false) {\n            // Flag all future uses of `useId` to skip the update dance. This is in\n            // `useEffect` because it goes after `useLayoutEffect`, ensuring we don't\n            // accidentally bail out of the patch-up dance prematurely.\n            serverHandoffComplete = true;\n        }\n    }, []);\n    return (_a = providedId !== null && providedId !== void 0 ? providedId : id) !== null && _a !== void 0 ? _a : undefined;\n}\n\n/** Render a month. */\nfunction Month(props) {\n    var _a;\n    var _b;\n    var dayPicker = useDayPicker();\n    var dir = dayPicker.dir, classNames = dayPicker.classNames, styles = dayPicker.styles, components = dayPicker.components;\n    var displayMonths = useNavigation().displayMonths;\n    var captionId = useId(dayPicker.id ? \"\".concat(dayPicker.id, \"-\").concat(props.displayIndex) : undefined);\n    var tableId = dayPicker.id\n        ? \"\".concat(dayPicker.id, \"-grid-\").concat(props.displayIndex)\n        : undefined;\n    var className = [classNames.month];\n    var style = styles.month;\n    var isStart = props.displayIndex === 0;\n    var isEnd = props.displayIndex === displayMonths.length - 1;\n    var isCenter = !isStart && !isEnd;\n    if (dir === 'rtl') {\n        _a = [isStart, isEnd], isEnd = _a[0], isStart = _a[1];\n    }\n    if (isStart) {\n        className.push(classNames.caption_start);\n        style = __assign(__assign({}, style), styles.caption_start);\n    }\n    if (isEnd) {\n        className.push(classNames.caption_end);\n        style = __assign(__assign({}, style), styles.caption_end);\n    }\n    if (isCenter) {\n        className.push(classNames.caption_between);\n        style = __assign(__assign({}, style), styles.caption_between);\n    }\n    var CaptionComponent = (_b = components === null || components === void 0 ? void 0 : components.Caption) !== null && _b !== void 0 ? _b : Caption;\n    return (jsxs(\"div\", { className: className.join(' '), style: style, children: [jsx(CaptionComponent, { id: captionId, displayMonth: props.displayMonth, displayIndex: props.displayIndex }), jsx(Table, { id: tableId, \"aria-labelledby\": captionId, displayMonth: props.displayMonth })] }, props.displayIndex));\n}\n\n/**\n * Render the wrapper for the month grids.\n */\nfunction Months(props) {\n    var _a = useDayPicker(), classNames = _a.classNames, styles = _a.styles;\n    return (jsx(\"div\", { className: classNames.months, style: styles.months, children: props.children }));\n}\n\n/** Render the container with the months according to the number of months to display. */\nfunction Root(_a) {\n    var _b, _c;\n    var initialProps = _a.initialProps;\n    var dayPicker = useDayPicker();\n    var focusContext = useFocusContext();\n    var navigation = useNavigation();\n    var _d = useState(false), hasInitialFocus = _d[0], setHasInitialFocus = _d[1];\n    // Focus the focus target when initialFocus is passed in\n    useEffect(function () {\n        if (!dayPicker.initialFocus)\n            return;\n        if (!focusContext.focusTarget)\n            return;\n        if (hasInitialFocus)\n            return;\n        focusContext.focus(focusContext.focusTarget);\n        setHasInitialFocus(true);\n    }, [\n        dayPicker.initialFocus,\n        hasInitialFocus,\n        focusContext.focus,\n        focusContext.focusTarget,\n        focusContext\n    ]);\n    // Apply classnames according to props\n    var classNames = [dayPicker.classNames.root, dayPicker.className];\n    if (dayPicker.numberOfMonths > 1) {\n        classNames.push(dayPicker.classNames.multiple_months);\n    }\n    if (dayPicker.showWeekNumber) {\n        classNames.push(dayPicker.classNames.with_weeknumber);\n    }\n    var style = __assign(__assign({}, dayPicker.styles.root), dayPicker.style);\n    var dataAttributes = Object.keys(initialProps)\n        .filter(function (key) { return key.startsWith('data-'); })\n        .reduce(function (attrs, key) {\n        var _a;\n        return __assign(__assign({}, attrs), (_a = {}, _a[key] = initialProps[key], _a));\n    }, {});\n    var MonthsComponent = (_c = (_b = initialProps.components) === null || _b === void 0 ? void 0 : _b.Months) !== null && _c !== void 0 ? _c : Months;\n    return (jsx(\"div\", __assign({ className: classNames.join(' '), style: style, dir: dayPicker.dir, id: dayPicker.id, nonce: initialProps.nonce, title: initialProps.title, lang: initialProps.lang }, dataAttributes, { children: jsx(MonthsComponent, { children: navigation.displayMonths.map(function (month, i) { return (jsx(Month, { displayIndex: i, displayMonth: month }, i)); }) }) })));\n}\n\n/** Provide the value for all the context providers. */\nfunction RootProvider(props) {\n    var children = props.children, initialProps = __rest(props, [\"children\"]);\n    return (jsx(DayPickerProvider, { initialProps: initialProps, children: jsx(NavigationProvider, { children: jsx(SelectSingleProvider, { initialProps: initialProps, children: jsx(SelectMultipleProvider, { initialProps: initialProps, children: jsx(SelectRangeProvider, { initialProps: initialProps, children: jsx(ModifiersProvider, { children: jsx(FocusProvider, { children: children }) }) }) }) }) }) }));\n}\n\n/**\n * DayPicker render a date picker component to let users pick dates from a\n * calendar. See http://react-day-picker.js.org for updated documentation and\n * examples.\n *\n * ### Customization\n *\n * DayPicker offers different customization props. For example,\n *\n * - show multiple months using `numberOfMonths`\n * - display a dropdown to navigate the months via `captionLayout`\n * - display the week numbers with `showWeekNumbers`\n * - disable or hide days with `disabled` or `hidden`\n *\n * ### Controlling the months\n *\n * Change the initially displayed month using the `defaultMonth` prop. The\n * displayed months are controlled by DayPicker and stored in its internal\n * state. To control the months yourself, use `month` instead of `defaultMonth`\n * and use the `onMonthChange` event to set it.\n *\n * To limit the months the user can navigate to, use\n * `fromDate`/`fromMonth`/`fromYear` or `toDate`/`toMonth`/`toYear`.\n *\n * ### Selection modes\n *\n * DayPicker supports different selection mode that can be toggled using the\n * `mode` prop:\n *\n * - `mode=\"single\"`: only one day can be selected. Use `required` to make the\n *   selection required. Use the `onSelect` event handler to get the selected\n *   days.\n * - `mode=\"multiple\"`: users can select one or more days. Limit the amount of\n *   days that can be selected with the `min` or the `max` props.\n * - `mode=\"range\"`: users can select a range of days. Limit the amount of days\n *   in the range with the `min` or the `max` props.\n * - `mode=\"default\"` (default): the built-in selections are disabled. Implement\n *   your own selection mode with `onDayClick`.\n *\n * The selection modes should cover the most common use cases. In case you\n * need a more refined way of selecting days, use `mode=\"default\"`. Use the\n * `selected` props and add the day event handlers to add/remove days from the\n * selection.\n *\n * ### Modifiers\n *\n * A _modifier_ represents different styles or states for the days displayed in\n * the calendar (like \"selected\" or \"disabled\"). Define custom modifiers using\n * the `modifiers` prop.\n *\n * ### Formatters and custom component\n *\n * You can customize how the content is displayed in the date picker by using\n * either the formatters or replacing the internal components.\n *\n * For the most common cases you want to use the `formatters` prop to change how\n * the content is formatted in the calendar. Use the `components` prop to\n * replace the internal components, like the navigation icons.\n *\n * ### Styling\n *\n * DayPicker comes with a default, basic style in `react-day-picker/style` – use\n * it as template for your own style.\n *\n * If you are using CSS modules, pass the imported styles object the\n * `classNames` props.\n *\n * You can also style the elements via inline styles using the `styles` prop.\n *\n * ### Form fields\n *\n * If you need to bind the date picker to a form field, you can use the\n * `useInput` hooks for a basic behavior. See the `useInput` source as an\n * example to bind the date picker with form fields.\n *\n * ### Localization\n *\n * To localize DayPicker, import the locale from `date-fns` package and use the\n * `locale` prop.\n *\n * For example, to use Spanish locale:\n *\n * ```\n * import { es } from 'date-fns/locale';\n * <DayPicker locale={es} />\n * ```\n */\nfunction DayPicker(props) {\n    return (jsx(RootProvider, __assign({}, props, { children: jsx(Root, { initialProps: props }) })));\n}\n\n/** @private */\nfunction isValidDate(day) {\n    return !isNaN(day.getTime());\n}\n\n/** Return props and setters for binding an input field to DayPicker. */\nfunction useInput(options) {\n    if (options === void 0) { options = {}; }\n    var _a = options.locale, locale = _a === void 0 ? enUS : _a, required = options.required, _b = options.format, format$1 = _b === void 0 ? 'PP' : _b, defaultSelected = options.defaultSelected, _c = options.today, today = _c === void 0 ? new Date() : _c;\n    var _d = parseFromToProps(options), fromDate = _d.fromDate, toDate = _d.toDate;\n    // Shortcut to the DateFns functions\n    var parseValue = function (value) { return parse(value, format$1, today, { locale: locale }); };\n    // Initialize states\n    var _e = useState(defaultSelected !== null && defaultSelected !== void 0 ? defaultSelected : today), month = _e[0], setMonth = _e[1];\n    var _f = useState(defaultSelected), selectedDay = _f[0], setSelectedDay = _f[1];\n    var defaultInputValue = defaultSelected\n        ? format(defaultSelected, format$1, { locale: locale })\n        : '';\n    var _g = useState(defaultInputValue), inputValue = _g[0], setInputValue = _g[1];\n    var reset = function () {\n        setSelectedDay(defaultSelected);\n        setMonth(defaultSelected !== null && defaultSelected !== void 0 ? defaultSelected : today);\n        setInputValue(defaultInputValue !== null && defaultInputValue !== void 0 ? defaultInputValue : '');\n    };\n    var setSelected = function (date) {\n        setSelectedDay(date);\n        setMonth(date !== null && date !== void 0 ? date : today);\n        setInputValue(date ? format(date, format$1, { locale: locale }) : '');\n    };\n    var handleDayClick = function (day, _a) {\n        var selected = _a.selected;\n        if (!required && selected) {\n            setSelectedDay(undefined);\n            setInputValue('');\n            return;\n        }\n        setSelectedDay(day);\n        setInputValue(day ? format(day, format$1, { locale: locale }) : '');\n    };\n    var handleMonthChange = function (month) {\n        setMonth(month);\n    };\n    // When changing the input field, save its value in state and check if the\n    // string is a valid date. If it is a valid day, set it as selected and update\n    // the calendar’s month.\n    var handleChange = function (e) {\n        setInputValue(e.target.value);\n        var day = parseValue(e.target.value);\n        var isBefore = fromDate && differenceInCalendarDays(fromDate, day) > 0;\n        var isAfter = toDate && differenceInCalendarDays(day, toDate) > 0;\n        if (!isValidDate(day) || isBefore || isAfter) {\n            setSelectedDay(undefined);\n            return;\n        }\n        setSelectedDay(day);\n        setMonth(day);\n    };\n    // Special case for _required_ fields: on blur, if the value of the input is not\n    // a valid date, reset the calendar and the input value.\n    var handleBlur = function (e) {\n        var day = parseValue(e.target.value);\n        if (!isValidDate(day)) {\n            reset();\n        }\n    };\n    // When focusing, make sure DayPicker visualizes the month of the date in the\n    // input field.\n    var handleFocus = function (e) {\n        if (!e.target.value) {\n            reset();\n            return;\n        }\n        var day = parseValue(e.target.value);\n        if (isValidDate(day)) {\n            setMonth(day);\n        }\n    };\n    var dayPickerProps = {\n        month: month,\n        onDayClick: handleDayClick,\n        onMonthChange: handleMonthChange,\n        selected: selectedDay,\n        locale: locale,\n        fromDate: fromDate,\n        toDate: toDate,\n        today: today\n    };\n    var inputProps = {\n        onBlur: handleBlur,\n        onChange: handleChange,\n        onFocus: handleFocus,\n        value: inputValue,\n        placeholder: format(new Date(), format$1, { locale: locale })\n    };\n    return { dayPickerProps: dayPickerProps, inputProps: inputProps, reset: reset, setSelected: setSelected };\n}\n\n/** Returns true when the props are of type {@link DayPickerDefaultProps}. */\nfunction isDayPickerDefault(props) {\n    return props.mode === undefined || props.mode === 'default';\n}\n\nexport { Button, Caption, CaptionDropdowns, CaptionLabel, CaptionNavigation, Day, DayContent, DayPicker, DayPickerContext, DayPickerProvider, Dropdown, FocusContext, FocusProvider, Footer, Head, HeadRow, IconDropdown, IconLeft, IconRight, InternalModifier, Months, NavigationContext, NavigationProvider, RootProvider, Row, SelectMultipleContext, SelectMultipleProvider, SelectMultipleProviderInternal, SelectRangeContext, SelectRangeProvider, SelectRangeProviderInternal, SelectSingleContext, SelectSingleProvider, SelectSingleProviderInternal, WeekNumber, addToRange, isDateAfterType, isDateBeforeType, isDateInterval, isDateRange, isDayOfWeekType, isDayPickerDefault, isDayPickerMultiple, isDayPickerRange, isDayPickerSingle, isMatch, useActiveModifiers, useDayPicker, useDayRender, useFocusContext, useInput, useNavigation, useSelectMultiple, useSelectRange, useSelectSingle };\n//# sourceMappingURL=index.esm.js.map\n", "import * as React from \"react\"\r\nimport { ChevronLeft, ChevronRight } from \"lucide-react\"\r\nimport { DayPicker } from \"react-day-picker\"\r\n\r\nimport { cn } from \"~/lib/utils\"\r\nimport { buttonVariants } from \"~/components/ui/button\"\r\n\r\nexport type CalendarProps = React.ComponentProps<typeof DayPicker>\r\n\r\nfunction Calendar({\r\n  className,\r\n  classNames,\r\n  showOutsideDays = true,\r\n  ...props\r\n}: CalendarProps) {\r\n  return (\r\n    <DayPicker\r\n      showOutsideDays={showOutsideDays}\r\n      className={cn(\"p-3\", className)}\r\n      classNames={{\r\n        months: \"flex flex-col sm:flex-row space-y-4 sm:space-x-4 sm:space-y-0\",\r\n        month: \"space-y-4\",\r\n        caption: \"flex justify-center pt-1 relative items-center\",\r\n        caption_label: \"text-sm font-medium\",\r\n        nav: \"space-x-1 flex items-center\",\r\n        nav_button: cn(\r\n          buttonVariants({ variant: \"outline\" }),\r\n          \"h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100\"\r\n        ),\r\n        nav_button_previous: \"absolute left-1\",\r\n        nav_button_next: \"absolute right-1\",\r\n        table: \"w-full border-collapse space-y-1\",\r\n        head_row: \"flex\",\r\n        head_cell:\r\n          \"text-muted-foreground rounded-md w-9 font-normal text-[0.8rem]\",\r\n        row: \"flex w-full mt-2\",\r\n        cell: \"h-9 w-9 text-center text-sm p-0 relative [&:has([aria-selected].day-range-end)]:rounded-r-md [&:has([aria-selected].day-outside)]:bg-accent/50 [&:has([aria-selected])]:bg-accent first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md focus-within:relative focus-within:z-20\",\r\n        day: cn(\r\n          buttonVariants({ variant: \"ghost\" }),\r\n          \"h-9 w-9 p-0 font-normal aria-selected:opacity-100\"\r\n        ),\r\n        day_range_end: \"day-range-end\",\r\n        day_selected:\r\n          \"bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground\",\r\n        day_today: \"bg-accent text-accent-foreground\",\r\n        day_outside:\r\n          \"day-outside text-muted-foreground aria-selected:bg-accent/50 aria-selected:text-muted-foreground\",\r\n        day_disabled: \"text-muted-foreground opacity-50\",\r\n        day_range_middle:\r\n          \"aria-selected:bg-accent aria-selected:text-accent-foreground\",\r\n        day_hidden: \"invisible\",\r\n        ...classNames,\r\n      }}\r\n      components={{\r\n        IconLeft: ({ ...props }) => <ChevronLeft className=\"h-4 w-4\" />,\r\n        IconRight: ({ ...props }) => <ChevronRight className=\"h-4 w-4\" />,\r\n      }}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\nCalendar.displayName = \"Calendar\"\r\n\r\nexport { Calendar }\r\n"], "names": ["Calendar", "lastDayOfMonth", "__assign", "toDate", "createContext", "jsx", "useContext", "formatCaption", "jsxs", "formatMonthCaption", "labelMonthDropdown", "Fragment", "formatYearCaption", "labelYearDropdown", "year", "useState", "setMonth", "_a", "month", "forwardRef", "labelPrevious", "labelNext", "formatWeekdayName", "labelWeekday", "formatDay", "min", "max", "_b", "InternalModifier", "useEffect", "useRef", "labelWeekNumber", "formatWeekNumber", "useLayoutEffect", "id", "props"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASK,MAACA,aAAW,iBAAiB,YAAY;AAAA,EAC5C,CAAC,QAAQ,EAAE,GAAG,UAAU,KAAK,SAAQ,CAAE;AAAA,EACvC,CAAC,QAAQ,EAAE,GAAG,WAAW,KAAK,SAAQ,CAAE;AAAA,EACxC,CAAC,QAAQ,EAAE,OAAO,MAAM,QAAQ,MAAM,GAAG,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,SAAQ,CAAE;AAAA,EAC9E,CAAC,QAAQ,EAAE,GAAG,YAAY,KAAK,SAAU,CAAA;AAC3C,CAAC;ACQM,SAAS,SAAS,MAAM,QAAQ;AACrC,QAAM,OAAO,SAAS;AACtB,SAAO,QAAQ,MAAM,IAAI;AAC3B;ACHO,SAAS,SAAS,MAAM,QAAQ;AACrC,SAAO,UAAU,MAAM,SAAS,EAAE;AACpC;ACEO,SAAS,IAAI,OAAO;AACzB,MAAI;AACJ,QAAM,QAAQ,SAAU,WAAW;AACjC,UAAM,cAAc,OAAO,SAAS;AAEpC,QACE,WAAW,UACX,SAAS,eACT,MAAM,OAAO,WAAW,CAAC,GACzB;AACA,eAAS;AAAA,IACf;AAAA,EACA,CAAG;AAED,SAAO,UAAU,oBAAI,KAAK,GAAG;AAC/B;ACfO,SAAS,IAAI,OAAO;AACzB,MAAI;AAEJ,QAAM,QAAQ,CAAC,cAAc;AAC3B,UAAM,OAAO,OAAO,SAAS;AAC7B,QAAI,CAAC,UAAU,SAAS,QAAQ,MAAM,CAAC,IAAI,GAAG;AAC5C,eAAS;AAAA,IACf;AAAA,EACA,CAAG;AAED,SAAO,UAAU,oBAAI,KAAK,GAAG;AAC/B;ACZO,SAAS,2BAA2B,UAAU,WAAW;AAC9D,QAAM,YAAY,OAAO,QAAQ;AACjC,QAAM,aAAa,OAAO,SAAS;AAEnC,QAAM,WAAW,UAAU,YAAW,IAAK,WAAW,YAAa;AACnE,QAAM,YAAY,UAAU,SAAQ,IAAK,WAAW,SAAU;AAE9D,SAAO,WAAW,KAAK;AACzB;ACSO,SAAS,0BAA0B,UAAU,WAAW,SAAS;AACtE,QAAM,kBAAkB,YAAY,UAAU,OAAO;AACrD,QAAM,mBAAmB,YAAY,WAAW,OAAO;AAEvD,QAAM,gBACJ,CAAC,kBAAkB,gCAAgC,eAAe;AACpE,QAAM,iBACJ,CAAC,mBAAmB,gCAAgC,gBAAgB;AAKtE,SAAO,KAAK,OAAO,gBAAgB,kBAAkB,kBAAkB;AACzE;ACjCO,SAAS,WAAW,MAAM;AAC/B,QAAM,QAAQ,OAAO,IAAI;AACzB,QAAM,QAAQ,MAAM,SAAU;AAC9B,QAAM,YAAY,MAAM,YAAa,GAAE,QAAQ,GAAG,CAAC;AACnD,QAAM,SAAS,IAAI,IAAI,IAAI,GAAG;AAC9B,SAAO;AACT;ACNO,SAAS,aAAa,MAAM;AACjC,QAAM,QAAQ,OAAO,IAAI;AACzB,QAAM,QAAQ,CAAC;AACf,QAAM,SAAS,GAAG,GAAG,GAAG,CAAC;AACzB,SAAO;AACT;ACMO,SAAS,UAAU,MAAM,SAAS;;AACvC,QAAM,iBAAiB,kBAAmB;AAC1C,QAAM,gBACJ,mCAAS,mBACT,8CAAS,WAAT,mBAAiB,YAAjB,mBAA0B,iBAC1B,eAAe,kBACf,0BAAe,WAAf,mBAAuB,YAAvB,mBAAgC,iBAChC;AAEF,QAAM,QAAQ,OAAO,IAAI;AACzB,QAAM,MAAM,MAAM,OAAQ;AAC1B,QAAM,QAAQ,MAAM,eAAe,KAAK,KAAK,KAAK,MAAM;AAExD,QAAM,QAAQ,MAAM,QAAO,IAAK,IAAI;AACpC,QAAM,SAAS,IAAI,IAAI,IAAI,GAAG;AAC9B,SAAO;AACT;ACzBO,SAAS,aAAa,MAAM;AACjC,SAAO,UAAU,MAAM,EAAE,cAAc,EAAC,CAAE;AAC5C;ACJO,SAAS,eAAe,MAAM;AACnC,QAAM,QAAQ,OAAO,IAAI;AACzB,QAAM,OAAO,MAAM,YAAa;AAChC,QAAM,aAAa,MAAM,SAAU;AACnC,QAAMC,kBAAiB,cAAc,MAAM,CAAC;AAC5C,EAAAA,gBAAe,YAAY,MAAM,aAAa,GAAG,CAAC;AAClD,EAAAA,gBAAe,SAAS,GAAG,GAAG,GAAG,CAAC;AAClC,SAAOA,gBAAe,QAAS;AACjC;ACTO,SAAS,YAAY,MAAM;AAChC,SAAO,KAAK,MAAM,CAAC,OAAO,IAAI,IAAI,GAAI;AACxC;ACDO,SAAS,eAAe,MAAM;AACnC,QAAM,QAAQ,OAAO,IAAI;AACzB,QAAM,QAAQ,MAAM,SAAU;AAC9B,QAAM,YAAY,MAAM,YAAa,GAAE,QAAQ,GAAG,CAAC;AACnD,QAAM,SAAS,GAAG,GAAG,GAAG,CAAC;AACzB,SAAO;AACT;ACMO,SAAS,gBAAgB,MAAM,SAAS;AAC7C,SACE;AAAA,IACE,eAAe,IAAI;AAAA,IACnB,aAAa,IAAI;AAAA,IACjB;AAAA,EACN,IAAQ;AAER;ACpBO,SAAS,QAAQ,MAAM,eAAe;AAC3C,QAAM,QAAQ,OAAO,IAAI;AACzB,QAAM,iBAAiB,OAAO,aAAa;AAC3C,SAAO,MAAM,YAAY,eAAe,QAAS;AACnD;ACJO,SAAS,SAAS,MAAM,eAAe;AAC5C,QAAM,QAAQ,OAAO,IAAI;AACzB,QAAM,iBAAiB,OAAO,aAAa;AAC3C,SAAO,CAAC,QAAQ,CAAC;AACnB;ACCO,SAAS,YAAY,UAAU,WAAW;AAC/C,QAAM,YAAY,OAAO,QAAQ;AACjC,QAAM,aAAa,OAAO,SAAS;AACnC,SACE,UAAU,YAAW,MAAO,WAAW,YAAa,KACpD,UAAU,eAAe,WAAW,SAAQ;AAEhD;ACZO,SAAS,WAAW,UAAU,WAAW;AAC9C,QAAM,YAAY,OAAO,QAAQ;AACjC,QAAM,aAAa,OAAO,SAAS;AACnC,SAAO,UAAU,kBAAkB,WAAW,YAAa;AAC7D;ACJO,SAAS,QAAQ,MAAM,QAAQ;AACpC,SAAO,QAAQ,MAAM,CAAC,MAAM;AAC9B;ACAO,SAAS,SAAS,MAAM,OAAO;AACpC,QAAM,QAAQ,OAAO,IAAI;AACzB,QAAM,OAAO,MAAM,YAAa;AAChC,QAAM,MAAM,MAAM,QAAS;AAE3B,QAAM,uBAAuB,cAAc,MAAM,CAAC;AAClD,uBAAqB,YAAY,MAAM,OAAO,EAAE;AAChD,uBAAqB,SAAS,GAAG,GAAG,GAAG,CAAC;AACxC,QAAM,cAAc,eAAe,oBAAoB;AAGvD,QAAM,SAAS,OAAO,KAAK,IAAI,KAAK,WAAW,CAAC;AAChD,SAAO;AACT;ACdO,SAAS,QAAQ,MAAM,MAAM;AAClC,QAAM,QAAQ,OAAO,IAAI;AAGzB,MAAI,MAAM,CAAC,KAAK,GAAG;AACjB,WAAO,cAAc,MAAM,GAAG;AAAA,EAClC;AAEE,QAAM,YAAY,IAAI;AACtB,SAAO;AACT;ACXA,IAAI,WAAW,WAAW;AACtB,aAAW,OAAO,UAAU,SAASC,UAAS,GAAG;AAC7C,aAAS,GAAG,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG,KAAK;AACjD,UAAI,UAAU,CAAC;AACf,eAAS,KAAK,EAAG,KAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC,EAAG,GAAE,CAAC,IAAI,EAAE,CAAC;AAAA,IAC9E;AACD,WAAO;AAAA,EACf;AACI,SAAO,SAAS,MAAM,MAAM,SAAS;AACzC;AAEA,SAAS,OAAO,GAAG,GAAG;AAClB,MAAI,IAAI,CAAA;AACR,WAAS,KAAK,EAAG,KAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC,KAAK,EAAE,QAAQ,CAAC,IAAI;AAC9E,MAAE,CAAC,IAAI,EAAE,CAAC;AACd,MAAI,KAAK,QAAQ,OAAO,OAAO,0BAA0B;AACrD,aAAS,IAAI,GAAG,IAAI,OAAO,sBAAsB,CAAC,GAAG,IAAI,EAAE,QAAQ,KAAK;AACpE,UAAI,EAAE,QAAQ,EAAE,CAAC,CAAC,IAAI,KAAK,OAAO,UAAU,qBAAqB,KAAK,GAAG,EAAE,CAAC,CAAC;AACzE,UAAE,EAAE,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;AAAA,IACvB;AACL,SAAO;AACX;AAEA,SAAS,cAAc,IAAI,MAAM,MAAM;AACC,WAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,IAAI,IAAI,GAAG,KAAK;AACjF,QAAI,MAAM,EAAE,KAAK,OAAO;AACpB,UAAI,CAAC,GAAI,MAAK,MAAM,UAAU,MAAM,KAAK,MAAM,GAAG,CAAC;AACnD,SAAG,CAAC,IAAI,KAAK,CAAC;AAAA,IACjB;AAAA,EACJ;AACD,SAAO,GAAG,OAAO,MAAM,MAAM,UAAU,MAAM,KAAK,IAAI,CAAC;AAC3D;AAEA,OAAO,oBAAoB,aAAa,kBAAkB,SAAU,OAAO,YAAY,SAAS;AAC5F,MAAI,IAAI,IAAI,MAAM,OAAO;AACzB,SAAO,EAAE,OAAO,mBAAmB,EAAE,QAAQ,OAAO,EAAE,aAAa,YAAY;AACnF;AAGA,SAAS,oBAAoB,OAAO;AAChC,SAAO,MAAM,SAAS;AAC1B;AAGA,SAAS,iBAAiB,OAAO;AAC7B,SAAO,MAAM,SAAS;AAC1B;AAGA,SAAS,kBAAkB,OAAO;AAC9B,SAAO,MAAM,SAAS;AAC1B;AAKA,IAAI,oBAAoB;AAAA,EACpB,MAAM;AAAA,EACN,iBAAiB;AAAA,EACjB,iBAAiB;AAAA,EACjB,SAAS;AAAA,EACT,cAAc;AAAA,EACd,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,eAAe;AAAA,EACf,aAAa;AAAA,EACb,iBAAiB;AAAA,EACjB,eAAe;AAAA,EACf,mBAAmB;AAAA,EACnB,UAAU;AAAA,EACV,gBAAgB;AAAA,EAChB,eAAe;AAAA,EACf,eAAe;AAAA,EACf,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,MAAM;AAAA,EACN,UAAU;AAAA,EACV,WAAW;AAAA,EACX,KAAK;AAAA,EACL,YAAY;AAAA,EACZ,qBAAqB;AAAA,EACrB,iBAAiB;AAAA,EACjB,UAAU;AAAA,EACV,KAAK;AAAA,EACL,YAAY;AAAA,EACZ,MAAM;AAAA,EACN,KAAK;AAAA,EACL,WAAW;AAAA,EACX,aAAa;AAAA,EACb,cAAc;AAAA,EACd,cAAc;AAAA,EACd,YAAY;AAAA,EACZ,iBAAiB;AAAA,EACjB,eAAe;AAAA,EACf,kBAAkB;AACtB;AAKA,SAAS,cAAc,OAAO,SAAS;AACnC,SAAO,OAAO,OAAO,UAAU,OAAO;AAC1C;AAKA,SAAS,UAAU,KAAK,SAAS;AAC7B,SAAO,OAAO,KAAK,KAAK,OAAO;AACnC;AAKA,SAAS,mBAAmB,OAAO,SAAS;AACxC,SAAO,OAAO,OAAO,QAAQ,OAAO;AACxC;AAKA,SAAS,iBAAiB,YAAY;AAClC,SAAO,GAAG,OAAO,UAAU;AAC/B;AAKA,SAAS,kBAAkB,SAAS,SAAS;AACzC,SAAO,OAAO,SAAS,UAAU,OAAO;AAC5C;AAKA,SAAS,kBAAkB,MAAM,SAAS;AACtC,SAAO,OAAO,MAAM,QAAQ,OAAO;AACvC;AAEA,IAAI,aAA0B,uBAAO,OAAO;AAAA,EACxC,WAAW;AAAA,EACX;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACJ,CAAC;AAKD,IAAI,WAAW,SAAU,KAAK,iBAAiB,SAAS;AACpD,SAAO,OAAO,KAAK,kBAAkB,OAAO;AAChD;AAKA,IAAI,qBAAqB,WAAY;AACjC,SAAO;AACX;AAKA,IAAI,YAAY,WAAY;AACxB,SAAO;AACX;AAKA,IAAI,gBAAgB,WAAY;AAC5B,SAAO;AACX;AAKA,IAAI,eAAe,SAAU,KAAK,SAAS;AACvC,SAAO,OAAO,KAAK,QAAQ,OAAO;AACtC;AAKA,IAAI,kBAAkB,SAAU,GAAG;AAC/B,SAAO,WAAW,OAAO,CAAC;AAC9B;AAKA,IAAI,oBAAoB,WAAY;AAChC,SAAO;AACX;AAEA,IAAI,SAAsB,uBAAO,OAAO;AAAA,EACpC,WAAW;AAAA,EACX;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACJ,CAAC;AAMD,SAAS,0BAA0B;AAC/B,MAAI,gBAAgB;AACpB,MAAI,aAAa;AACjB,MAAI,SAAS;AACb,MAAI,sBAAsB,CAAE;AAC5B,MAAI,YAAY,CAAE;AAClB,MAAI,iBAAiB;AACrB,MAAI,SAAS,CAAE;AACf,MAAI,QAAQ,oBAAI,KAAM;AACtB,SAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,MAAM;AAAA,EACT;AACL;AAGA,SAAS,iBAAiB,OAAO;AAC7B,MAAI,WAAW,MAAM,UAAU,SAAS,MAAM,QAAQ,YAAY,MAAM,WAAW,UAAU,MAAM;AACnG,MAAI,WAAW,MAAM,UAAUC,UAAS,MAAM;AAC9C,MAAI,WAAW;AACX,eAAW,aAAa,SAAS;AAAA,EACzC,WACa,UAAU;AACf,eAAW,IAAI,KAAK,UAAU,GAAG,CAAC;AAAA,EAC1C;AACI,MAAI,SAAS;AACT,IAAAA,UAAS,WAAW,OAAO;AAAA,EACnC,WACa,QAAQ;AACb,IAAAA,UAAS,IAAI,KAAK,QAAQ,IAAI,EAAE;AAAA,EACxC;AACI,SAAO;AAAA,IACH,UAAU,WAAW,WAAW,QAAQ,IAAI;AAAA,IAC5C,QAAQA,UAAS,WAAWA,OAAM,IAAI;AAAA,EACzC;AACL;AASA,IAAI,mBAAmBC,aAAa,cAAC,MAAS;AAK9C,SAAS,kBAAkB,OAAO;AAC9B,MAAI;AACJ,MAAI,eAAe,MAAM;AACzB,MAAI,uBAAuB,wBAAyB;AACpD,MAAI,KAAK,iBAAiB,YAAY,GAAG,WAAW,GAAG,UAAUD,UAAS,GAAG;AAC7E,MAAI,iBAAiB,KAAK,aAAa,mBAAmB,QAAQ,OAAO,SAAS,KAAK,qBAAqB;AAC5G,MAAI,kBAAkB,cAAc,CAAC,YAAY,CAACA,UAAS;AAEvD,oBAAgB;AAAA,EACxB;AACI,MAAI;AACJ,MAAI,kBAAkB,YAAY,KAC9B,oBAAoB,YAAY,KAChC,iBAAiB,YAAY,GAAG;AAChC,eAAW,aAAa;AAAA,EAChC;AACI,MAAI,QAAQ,SAAS,SAAS,SAAS,CAAA,GAAI,oBAAoB,GAAG,YAAY,GAAG,EAAE,eAA8B,YAAY,SAAS,SAAS,CAAE,GAAE,qBAAqB,UAAU,GAAG,aAAa,UAAU,GAAG,YAAY,SAAS,CAAE,GAAE,aAAa,UAAU,GAAG,YAAY,SAAS,SAAS,IAAI,qBAAqB,UAAU,GAAG,aAAa,UAAU,GAAG,UAAoB,QAAQ,SAAS,SAAS,CAAE,GAAE,qBAAqB,MAAM,GAAG,aAAa,MAAM,GAAG,MAAM,aAAa,QAAQ,qBAAqB,MAAM,WAAW,SAAS,SAAS,CAAE,GAAE,qBAAqB,SAAS,GAAG,aAAa,SAAS,GAAG,qBAAqB,SAAS,SAAS,IAAI,qBAAqB,mBAAmB,GAAG,aAAa,mBAAmB,GAAG,UAAoB,QAAQ,SAAS,SAAS,CAAE,GAAE,qBAAqB,MAAM,GAAG,aAAa,MAAM,GAAG,QAAQA,SAAQ;AACt0B,SAAQE,kBAAG,IAAC,iBAAiB,UAAU,EAAE,OAAc,UAAU,MAAM,UAAU;AACrF;AAOA,SAAS,eAAe;AACpB,MAAI,UAAUC,aAAU,WAAC,gBAAgB;AACzC,MAAI,CAAC,SAAS;AACV,UAAM,IAAI,MAAM,uDAAuD;AAAA,EAC/E;AACI,SAAO;AACX;AAGA,SAAS,aAAa,OAAO;AACzB,MAAI,KAAK,aAAc,GAAE,SAAS,GAAG,QAAQ,aAAa,GAAG,YAAY,SAAS,GAAG,QAAQC,iBAAgB,GAAG,WAAW;AAC3H,SAAQF,sBAAI,OAAO,EAAE,WAAW,WAAW,eAAe,OAAO,OAAO,eAAe,aAAa,UAAU,MAAM,gBAAgB,IAAI,MAAM,IAAI,UAAUE,eAAc,MAAM,cAAc,EAAE,OAAc,CAAE,EAAC,CAAE;AACvN;AAKA,SAAS,aAAa,OAAO;AACzB,SAAQF,sBAAI,OAAO,SAAS,EAAE,OAAO,OAAO,QAAQ,OAAO,SAAS,eAAe,eAAe,eAAgB,GAAE,OAAO,EAAE,UAAUA,kBAAAA,IAAI,QAAQ,EAAE,GAAG,2hBAA2hB,MAAM,gBAAgB,UAAU,UAAW,CAAA,EAAG,CAAA,CAAC;AACtuB;AAMA,SAAS,SAAS,OAAO;AACrB,MAAI,IAAI;AACR,MAAI,WAAW,MAAM,UAAU,QAAQ,MAAM,OAAO,WAAW,MAAM,UAAU,UAAU,MAAM,SAAS,YAAY,MAAM,WAAW,QAAQ,MAAM;AACnJ,MAAI,YAAY,aAAc;AAC9B,MAAI,yBAAyB,MAAM,KAAK,UAAU,gBAAgB,QAAQ,OAAO,SAAS,SAAS,GAAG,kBAAkB,QAAQ,OAAO,SAAS,KAAK;AACrJ,SAAQG,kBAAAA,KAAK,OAAO,EAAE,WAAsB,OAAc,UAAU,CAACH,kBAAAA,IAAI,QAAQ,EAAE,WAAW,UAAU,WAAW,SAAS,UAAU,MAAM,YAAY,EAAC,CAAE,GAAGA,kBAAG,IAAC,UAAU,EAAE,MAAM,MAAM,MAAM,cAAc,MAAM,YAAY,GAAG,WAAW,UAAU,WAAW,UAAU,OAAO,UAAU,OAAO,UAAU,OAAc,UAAoB,SAAkB,CAAE,GAAGG,uBAAK,OAAO,EAAE,WAAW,UAAU,WAAW,eAAe,OAAO,UAAU,OAAO,eAAe,eAAe,QAAQ,UAAU,CAAC,SAASH,kBAAAA,IAAI,uBAAuB,EAAE,WAAW,UAAU,WAAW,eAAe,OAAO,UAAU,OAAO,cAAa,CAAE,CAAC,EAAC,CAAE,CAAC,GAAG;AACtnB;AAGA,SAAS,eAAe,OAAO;AAC3B,MAAI;AACJ,MAAI,KAAK,aAAY,GAAI,WAAW,GAAG,UAAUF,UAAS,GAAG,QAAQ,SAAS,GAAG,QAAQ,SAAS,GAAG,QAAQM,sBAAqB,GAAG,WAAW,oBAAoB,aAAa,GAAG,YAAY,aAAa,GAAG,YAAYC,sBAAqB,GAAG,OAAO;AAE3P,MAAI,CAAC;AACD,WAAOL,kBAAG,IAACM,kBAAQ,UAAE,EAAE;AAC3B,MAAI,CAACR;AACD,WAAOE,kBAAG,IAACM,kBAAQ,UAAE,EAAE;AAC3B,MAAI,iBAAiB,CAAE;AACvB,MAAI,WAAW,UAAUR,OAAM,GAAG;AAE9B,QAAI,OAAO,aAAa,QAAQ;AAChC,aAAS,QAAQ,SAAS,SAAU,GAAE,SAASA,QAAO,YAAY,SAAS;AACvE,qBAAe,KAAK,SAAS,MAAM,KAAK,CAAC;AAAA,IACrD;AAAA,EACA,OACS;AAED,QAAI,OAAO,aAAa,oBAAI,KAAM,CAAA;AAClC,aAAS,QAAQ,GAAG,SAAS,IAAI,SAAS;AACtC,qBAAe,KAAK,SAAS,MAAM,KAAK,CAAC;AAAA,IACrD;AAAA,EACA;AACI,MAAI,eAAe,SAAU,GAAG;AAC5B,QAAI,gBAAgB,OAAO,EAAE,OAAO,KAAK;AACzC,QAAI,WAAW,SAAS,aAAa,MAAM,YAAY,GAAG,aAAa;AACvE,UAAM,SAAS,QAAQ;AAAA,EAC1B;AACD,MAAI,qBAAqB,KAAK,eAAe,QAAQ,eAAe,SAAS,SAAS,WAAW,cAAc,QAAQ,OAAO,SAAS,KAAK;AAC5I,SAAQE,sBAAI,mBAAmB,EAAE,MAAM,UAAU,cAAcK,oBAAkB,GAAI,WAAW,WAAW,gBAAgB,OAAO,OAAO,gBAAgB,UAAU,cAAc,OAAO,MAAM,aAAa,SAAU,GAAE,SAASD,oBAAmB,MAAM,cAAc,EAAE,OAAc,CAAE,GAAG,UAAU,eAAe,IAAI,SAAU,GAAG;AAAE,WAAQJ,sBAAI,UAAU,EAAE,OAAO,EAAE,YAAY,UAAUI,oBAAmB,GAAG,EAAE,OAAc,CAAE,EAAG,GAAE,EAAE,SAAQ,CAAE;AAAA,EAAG,CAAE,EAAC,CAAE;AACpc;AAMA,SAAS,cAAc,OAAO;AAC1B,MAAI;AACJ,MAAI,eAAe,MAAM;AACzB,MAAI,KAAK,aAAY,GAAI,WAAW,GAAG,UAAUN,UAAS,GAAG,QAAQ,SAAS,GAAG,QAAQ,SAAS,GAAG,QAAQ,aAAa,GAAG,YAAY,aAAa,GAAG,YAAYS,qBAAoB,GAAG,WAAW,mBAAmBC,qBAAoB,GAAG,OAAO;AACxP,MAAI,QAAQ,CAAE;AAEd,MAAI,CAAC;AACD,WAAOR,kBAAG,IAACM,kBAAQ,UAAE,EAAE;AAC3B,MAAI,CAACR;AACD,WAAOE,kBAAG,IAACM,kBAAQ,UAAE,EAAE;AAC3B,MAAI,WAAW,SAAS,YAAa;AACrC,MAAI,SAASR,QAAO,YAAa;AACjC,WAAS,OAAO,UAAU,QAAQ,QAAQ,QAAQ;AAC9C,UAAM,KAAK,QAAQ,YAAY,oBAAI,KAAM,CAAA,GAAG,IAAI,CAAC;AAAA,EACzD;AACI,MAAI,eAAe,SAAU,GAAG;AAC5B,QAAI,WAAW,QAAQ,aAAa,YAAY,GAAG,OAAO,EAAE,OAAO,KAAK,CAAC;AACzE,UAAM,SAAS,QAAQ;AAAA,EAC1B;AACD,MAAI,qBAAqB,KAAK,eAAe,QAAQ,eAAe,SAAS,SAAS,WAAW,cAAc,QAAQ,OAAO,SAAS,KAAK;AAC5I,SAAQE,sBAAI,mBAAmB,EAAE,MAAM,SAAS,cAAcQ,sBAAqB,WAAW,WAAW,eAAe,OAAO,OAAO,eAAe,UAAU,cAAc,OAAO,aAAa,YAAa,GAAE,SAASD,mBAAkB,cAAc,EAAE,QAAgB,GAAG,UAAU,MAAM,IAAI,SAAUE,OAAM;AAAE,WAAQT,kBAAAA,IAAI,UAAU,EAAE,OAAOS,MAAK,YAAW,GAAI,UAAUF,mBAAkBE,OAAM,EAAE,OAAgB,CAAA,KAAKA,MAAK,YAAW,CAAE;AAAA,EAAG,CAAE,EAAC,CAAE;AAC9b;AAWA,SAAS,mBAAmB,cAAc,iBAAiB;AACvD,MAAI,KAAKC,aAAAA,SAAS,YAAY,GAAG,oBAAoB,GAAG,CAAC,GAAG,WAAW,GAAG,CAAC;AAC3E,MAAI,QAAQ,oBAAoB,SAAY,oBAAoB;AAChE,SAAO,CAAC,OAAO,QAAQ;AAC3B;AAGA,SAAS,gBAAgB,SAAS;AAC9B,MAAI,QAAQ,QAAQ,OAAO,eAAe,QAAQ,cAAc,QAAQ,QAAQ;AAChF,MAAI,eAAe,SAAS,gBAAgB,SAAS,oBAAI,KAAM;AAC/D,MAAIZ,UAAS,QAAQ,QAAQ,WAAW,QAAQ,UAAU,KAAK,QAAQ,gBAAgB,iBAAiB,OAAO,SAAS,IAAI;AAE5H,MAAIA,WAAU,2BAA2BA,SAAQ,YAAY,IAAI,GAAG;AAChE,QAAI,SAAS,MAAM,iBAAiB;AACpC,mBAAe,UAAUA,SAAQ,MAAM;AAAA,EAC/C;AAEI,MAAI,YAAY,2BAA2B,cAAc,QAAQ,IAAI,GAAG;AACpE,mBAAe;AAAA,EACvB;AACI,SAAO,aAAa,YAAY;AACpC;AAGA,SAAS,qBAAqB;AAC1B,MAAI,UAAU,aAAc;AAC5B,MAAI,eAAe,gBAAgB,OAAO;AAC1C,MAAI,KAAK,mBAAmB,cAAc,QAAQ,KAAK,GAAG,QAAQ,GAAG,CAAC,GAAGa,YAAW,GAAG,CAAC;AACxF,MAAI,YAAY,SAAU,MAAM;AAC5B,QAAIC;AACJ,QAAI,QAAQ;AACR;AACJ,QAAIC,SAAQ,aAAa,IAAI;AAC7B,IAAAF,UAASE,MAAK;AACd,KAACD,MAAK,QAAQ,mBAAmB,QAAQA,QAAO,SAAS,SAASA,IAAG,KAAK,SAASC,MAAK;AAAA,EAC3F;AACD,SAAO,CAAC,OAAO,SAAS;AAC5B;AAMA,SAAS,iBAAiB,OAAO,IAAI;AACjC,MAAI,gBAAgB,GAAG,eAAe,iBAAiB,GAAG;AAC1D,MAAI,QAAQ,aAAa,KAAK;AAC9B,MAAI,MAAM,aAAa,UAAU,OAAO,cAAc,CAAC;AACvD,MAAI,aAAa,2BAA2B,KAAK,KAAK;AACtD,MAAI,SAAS,CAAE;AACf,WAAS,IAAI,GAAG,IAAI,YAAY,KAAK;AACjC,QAAI,YAAY,UAAU,OAAO,CAAC;AAClC,WAAO,KAAK,SAAS;AAAA,EAC7B;AACI,MAAI;AACA,aAAS,OAAO,QAAS;AAC7B,SAAO;AACX;AAYA,SAAS,aAAa,eAAe,SAAS;AAC1C,MAAI,QAAQ,mBAAmB;AAC3B,WAAO;AAAA,EACf;AACI,MAAIf,UAAS,QAAQ,QAAQ,kBAAkB,QAAQ,iBAAiB,KAAK,QAAQ,gBAAgB,iBAAiB,OAAO,SAAS,IAAI;AAC1I,MAAI,SAAS,kBAAkB,iBAAiB;AAChD,MAAI,QAAQ,aAAa,aAAa;AACtC,MAAI,CAACA,SAAQ;AACT,WAAO,UAAU,OAAO,MAAM;AAAA,EACtC;AACI,MAAI,aAAa,2BAA2BA,SAAQ,aAAa;AACjE,MAAI,aAAa,gBAAgB;AAC7B,WAAO;AAAA,EACf;AAEI,SAAO,UAAU,OAAO,MAAM;AAClC;AAaA,SAAS,iBAAiB,eAAe,SAAS;AAC9C,MAAI,QAAQ,mBAAmB;AAC3B,WAAO;AAAA,EACf;AACI,MAAI,WAAW,QAAQ,UAAU,kBAAkB,QAAQ,iBAAiB,KAAK,QAAQ,gBAAgB,iBAAiB,OAAO,SAAS,IAAI;AAC9I,MAAI,SAAS,kBAAkB,iBAAiB;AAChD,MAAI,QAAQ,aAAa,aAAa;AACtC,MAAI,CAAC,UAAU;AACX,WAAO,UAAU,OAAO,CAAC,MAAM;AAAA,EACvC;AACI,MAAI,aAAa,2BAA2B,OAAO,QAAQ;AAC3D,MAAI,cAAc,GAAG;AACjB,WAAO;AAAA,EACf;AAEI,SAAO,UAAU,OAAO,CAAC,MAAM;AACnC;AAMA,IAAI,oBAAoBC,aAAa,cAAC,MAAS;AAE/C,SAAS,mBAAmB,OAAO;AAC/B,MAAI,YAAY,aAAc;AAC9B,MAAI,KAAK,mBAAoB,GAAE,eAAe,GAAG,CAAC,GAAG,YAAY,GAAG,CAAC;AACrE,MAAI,gBAAgB,iBAAiB,cAAc,SAAS;AAC5D,MAAI,YAAY,aAAa,cAAc,SAAS;AACpD,MAAI,gBAAgB,iBAAiB,cAAc,SAAS;AAC5D,MAAI,kBAAkB,SAAU,MAAM;AAClC,WAAO,cAAc,KAAK,SAAU,cAAc;AAC9C,aAAO,YAAY,MAAM,YAAY;AAAA,IACjD,CAAS;AAAA,EACJ;AACD,MAAI,WAAW,SAAU,MAAM,SAAS;AACpC,QAAI,gBAAgB,IAAI,GAAG;AACvB;AAAA,IACZ;AACQ,QAAI,WAAW,SAAS,MAAM,OAAO,GAAG;AACpC,gBAAU,UAAU,MAAM,IAAI,UAAU,iBAAiB,EAAE,CAAC;AAAA,IACxE,OACa;AACD,gBAAU,IAAI;AAAA,IAC1B;AAAA,EACK;AACD,MAAI,QAAQ;AAAA,IACR;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACH;AACD,SAAQC,kBAAG,IAAC,kBAAkB,UAAU,EAAE,OAAc,UAAU,MAAM,UAAU;AACtF;AAOA,SAAS,gBAAgB;AACrB,MAAI,UAAUC,aAAU,WAAC,iBAAiB;AAC1C,MAAI,CAAC,SAAS;AACV,UAAM,IAAI,MAAM,wDAAwD;AAAA,EAChF;AACI,SAAO;AACX;AAKA,SAAS,iBAAiB,OAAO;AAC7B,MAAI;AACJ,MAAI,KAAK,gBAAgB,aAAa,GAAG,YAAY,SAAS,GAAG,QAAQ,aAAa,GAAG;AACzF,MAAI,YAAY,cAAa,EAAG;AAChC,MAAI,oBAAoB,SAAU,UAAU;AACxC,cAAU,UAAU,UAAU,MAAM,eAAe,CAAC,MAAM,eAAe,CAAC,CAAC;AAAA,EAC9E;AACD,MAAI,yBAAyB,KAAK,eAAe,QAAQ,eAAe,SAAS,SAAS,WAAW,kBAAkB,QAAQ,OAAO,SAAS,KAAK;AACpJ,MAAI,eAAgBD,kBAAAA,IAAI,uBAAuB,EAAE,IAAI,MAAM,IAAI,cAAc,MAAM,aAAc,CAAA;AACjG,SAAQG,kBAAAA,KAAK,OAAO,EAAE,WAAW,WAAW,mBAAmB,OAAO,OAAO,mBAAmB,UAAU,CAACH,kBAAG,IAAC,OAAO,EAAE,WAAW,WAAW,SAAS,UAAU,aAAY,CAAE,GAAGA,kBAAG,IAAC,gBAAgB,EAAE,UAAU,mBAAmB,cAAc,MAAM,cAAc,GAAGA,kBAAG,IAAC,eAAe,EAAE,UAAU,mBAAmB,cAAc,MAAM,aAAY,CAAE,CAAC,GAAG;AACtW;AAKA,SAAS,SAAS,OAAO;AACrB,SAAQA,sBAAI,OAAO,SAAS,EAAE,OAAO,QAAQ,QAAQ,QAAQ,SAAS,cAAe,GAAE,OAAO,EAAE,UAAUA,sBAAI,QAAQ,EAAE,GAAG,mhBAAmhB,MAAM,gBAAgB,UAAU,UAAW,CAAA,EAAG,CAAA,CAAC;AACjsB;AAKA,SAAS,UAAU,OAAO;AACtB,SAAQA,kBAAG,IAAC,OAAO,SAAS,EAAE,OAAO,QAAQ,QAAQ,QAAQ,SAAS,cAAe,GAAE,OAAO,EAAE,UAAUA,sBAAI,QAAQ,EAAE,GAAG,shBAAshB,MAAM,gBAAgB,EAAC,CAAE,CAAC;AAC/qB;AAGA,IAAI,SAASc,aAAU,WAAC,SAAU,OAAO,KAAK;AAC1C,MAAI,KAAK,aAAY,GAAI,aAAa,GAAG,YAAY,SAAS,GAAG;AACjE,MAAI,gBAAgB,CAAC,WAAW,cAAc,WAAW,MAAM;AAC/D,MAAI,MAAM,WAAW;AACjB,kBAAc,KAAK,MAAM,SAAS;AAAA,EAC1C;AACI,MAAI,YAAY,cAAc,KAAK,GAAG;AACtC,MAAI,QAAQ,SAAS,SAAS,CAAA,GAAI,OAAO,YAAY,GAAG,OAAO,MAAM;AACrE,MAAI,MAAM,OAAO;AACb,WAAO,OAAO,OAAO,MAAM,KAAK;AAAA,EACxC;AACI,SAAQd,kBAAAA,IAAI,UAAU,SAAS,CAAA,GAAI,OAAO,EAAE,KAAU,MAAM,UAAU,WAAsB,MAAY,CAAE,CAAC;AAC/G,CAAC;AAGD,SAAS,WAAW,OAAO;AACvB,MAAI,IAAI;AACR,MAAI,KAAK,aAAY,GAAI,MAAM,GAAG,KAAK,SAAS,GAAG,QAAQ,aAAa,GAAG,YAAY,SAAS,GAAG,QAAQ,KAAK,GAAG,QAAQe,iBAAgB,GAAG,eAAeC,aAAY,GAAG,WAAW,aAAa,GAAG;AACvM,MAAI,CAAC,MAAM,aAAa,CAAC,MAAM,eAAe;AAC1C,WAAOhB,kBAAG,IAACM,kBAAQ,UAAE,EAAE;AAAA,EAC/B;AACI,MAAI,gBAAgBS,eAAc,MAAM,eAAe,EAAE,QAAgB;AACzE,MAAI,oBAAoB;AAAA,IACpB,WAAW;AAAA,IACX,WAAW;AAAA,EACnB,EAAM,KAAK,GAAG;AACV,MAAI,YAAYC,WAAU,MAAM,WAAW,EAAE,QAAgB;AAC7D,MAAI,gBAAgB;AAAA,IAChB,WAAW;AAAA,IACX,WAAW;AAAA,EACnB,EAAM,KAAK,GAAG;AACV,MAAI,sBAAsB,KAAK,eAAe,QAAQ,eAAe,SAAS,SAAS,WAAW,eAAe,QAAQ,OAAO,SAAS,KAAK;AAC9I,MAAI,qBAAqB,KAAK,eAAe,QAAQ,eAAe,SAAS,SAAS,WAAW,cAAc,QAAQ,OAAO,SAAS,KAAK;AAC5I,SAAQb,uBAAK,OAAO,EAAE,WAAW,WAAW,KAAK,OAAO,OAAO,KAAK,UAAU,CAAC,CAAC,MAAM,gBAAiBH,sBAAI,QAAQ,EAAE,MAAM,kBAAkB,cAAc,eAAe,WAAW,mBAAmB,OAAO,OAAO,qBAAqB,UAAU,CAAC,MAAM,eAAe,SAAS,MAAM,iBAAiB,UAAU,QAAQ,QAASA,kBAAG,IAAC,oBAAoB,EAAE,WAAW,WAAW,UAAU,OAAO,OAAO,SAAQ,CAAE,IAAMA,sBAAI,mBAAmB,EAAE,WAAW,WAAW,UAAU,OAAO,OAAO,UAAU,GAAI,GAAI,CAAC,MAAM,YAAaA,sBAAI,QAAQ,EAAE,MAAM,cAAc,cAAc,WAAW,WAAW,eAAe,OAAO,OAAO,iBAAiB,UAAU,CAAC,MAAM,WAAW,SAAS,MAAM,aAAa,UAAU,QAAQ,QAASA,sBAAI,mBAAmB,EAAE,WAAW,WAAW,UAAU,OAAO,OAAO,SAAQ,CAAE,IAAMA,sBAAI,oBAAoB,EAAE,WAAW,WAAW,UAAU,OAAO,OAAO,UAAU,GAAI,CAAE,EAAC,CAAE;AACx4B;AAKA,SAAS,kBAAkB,OAAO;AAC9B,MAAI,iBAAiB,aAAY,EAAG;AACpC,MAAI,KAAK,cAAe,GAAE,gBAAgB,GAAG,eAAe,YAAY,GAAG,WAAW,YAAY,GAAG,WAAW,gBAAgB,GAAG;AACnI,MAAI,eAAe,cAAc,UAAU,SAAU,OAAO;AACxD,WAAO,YAAY,MAAM,cAAc,KAAK;AAAA,EACpD,CAAK;AACD,MAAI,UAAU,iBAAiB;AAC/B,MAAI,SAAS,iBAAiB,cAAc,SAAS;AACrD,MAAI,WAAW,iBAAiB,MAAM,WAAW,CAAC;AAClD,MAAI,eAAe,iBAAiB,MAAM,UAAU,CAAC;AACrD,MAAI,sBAAsB,WAAY;AAClC,QAAI,CAAC;AACD;AACJ,cAAU,aAAa;AAAA,EAC1B;AACD,MAAI,kBAAkB,WAAY;AAC9B,QAAI,CAAC;AACD;AACJ,cAAU,SAAS;AAAA,EACtB;AACD,SAAQA,kBAAG,IAAC,YAAY,EAAE,cAAc,MAAM,cAAc,UAAoB,cAA4B,WAAsB,eAA8B,iBAAiB,qBAAqB,aAAa,iBAAiB;AACxO;AAMA,SAAS,QAAQ,OAAO;AACpB,MAAI;AACJ,MAAI,KAAK,gBAAgB,aAAa,GAAG,YAAY,oBAAoB,GAAG,mBAAmB,SAAS,GAAG,QAAQ,gBAAgB,GAAG,eAAe,aAAa,GAAG;AACrK,MAAI,yBAAyB,KAAK,eAAe,QAAQ,eAAe,SAAS,SAAS,WAAW,kBAAkB,QAAQ,OAAO,SAAS,KAAK;AACpJ,MAAI;AACJ,MAAI,mBAAmB;AACnB,cAAWA,kBAAG,IAAC,uBAAuB,EAAE,IAAI,MAAM,IAAI,cAAc,MAAM,aAAY,CAAE;AAAA,EAChG,WACa,kBAAkB,YAAY;AACnC,cAAWA,kBAAG,IAAC,kBAAkB,EAAE,cAAc,MAAM,cAAc,IAAI,MAAM,GAAE,CAAE;AAAA,EAC3F,WACa,kBAAkB,oBAAoB;AAC3C,cAAWG,kBAAI,KAACG,kBAAQ,UAAE,EAAE,UAAU,CAACN,kBAAG,IAAC,kBAAkB,EAAE,cAAc,MAAM,cAAc,cAAc,MAAM,cAAc,IAAI,MAAM,GAAE,CAAE,GAAGA,kBAAG,IAAC,mBAAmB,EAAE,cAAc,MAAM,cAAc,cAAc,MAAM,cAAc,IAAI,MAAM,GAAI,CAAA,CAAC,EAAC,CAAE;AAAA,EAC3Q,OACS;AACD,cAAWG,kBAAAA,KAAKG,kBAAAA,UAAU,EAAE,UAAU,CAACN,kBAAAA,IAAI,uBAAuB,EAAE,IAAI,MAAM,IAAI,cAAc,MAAM,cAAc,cAAc,MAAM,aAAY,CAAE,GAAGA,kBAAG,IAAC,mBAAmB,EAAE,cAAc,MAAM,cAAc,IAAI,MAAM,GAAE,CAAE,CAAC,EAAG,CAAA;AAAA,EAC9O;AACI,SAAQA,sBAAI,OAAO,EAAE,WAAW,WAAW,SAAS,OAAO,OAAO,SAAS,UAAU,QAAO,CAAE;AAClG;AAIA,SAAS,OAAO,OAAO;AACnB,MAAI,KAAK,aAAY,GAAI,SAAS,GAAG,QAAQ,SAAS,GAAG,QAAQ,QAAQ,GAAG,WAAW;AACvF,MAAI,CAAC;AACD,WAAOA,kBAAG,IAACM,kBAAQ,UAAE,EAAE;AAC3B,SAAQN,kBAAG,IAAC,SAAS,EAAE,WAAW,OAAO,OAAO,OAAO,OAAO,UAAUA,sBAAI,MAAM,EAAE,UAAUA,kBAAAA,IAAI,MAAM,EAAE,SAAS,GAAG,UAAU,OAAM,CAAE,EAAG,CAAA,GAAG;AAClJ;AAMA,SAAS,YAAY,QAErB,cAEA,SAAS;AACL,MAAI,QAAQ,UACN,eAAe,oBAAI,KAAM,CAAA,IACzB,YAAY,oBAAI,QAAQ,EAAE,QAAgB,cAA4B;AAC5E,MAAI,OAAO,CAAE;AACb,WAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AACxB,QAAI,MAAM,QAAQ,OAAO,CAAC;AAC1B,SAAK,KAAK,GAAG;AAAA,EACrB;AACI,SAAO;AACX;AAKA,SAAS,UAAU;AACf,MAAI,KAAK,aAAY,GAAI,aAAa,GAAG,YAAY,SAAS,GAAG,QAAQ,iBAAiB,GAAG,gBAAgB,SAAS,GAAG,QAAQ,eAAe,GAAG,cAAc,UAAU,GAAG,SAASiB,qBAAoB,GAAG,WAAW,mBAAmBC,gBAAe,GAAG,OAAO;AACrQ,MAAI,WAAW,YAAY,QAAQ,cAAc,OAAO;AACxD,SAAQf,kBAAI,KAAC,MAAM,EAAE,OAAO,OAAO,UAAU,WAAW,WAAW,UAAU,UAAU,CAAC,kBAAmBH,kBAAAA,IAAI,MAAM,EAAE,OAAO,OAAO,WAAW,WAAW,WAAW,UAAS,CAAE,GAAI,SAAS,IAAI,SAAU,SAAS,GAAG;AAAE,WAAQA,sBAAI,MAAM,EAAE,OAAO,OAAO,WAAW,WAAW,WAAW,OAAO,OAAO,WAAW,cAAckB,cAAa,SAAS,EAAE,OAAgB,CAAA,GAAG,UAAUD,mBAAkB,SAAS,EAAE,OAAc,CAAE,EAAC,GAAI,CAAC;AAAA,EAAK,CAAA,CAAC,EAAC,CAAE;AACzb;AAGA,SAAS,OAAO;AACZ,MAAI;AACJ,MAAI,KAAK,gBAAgB,aAAa,GAAG,YAAY,SAAS,GAAG,QAAQ,aAAa,GAAG;AACzF,MAAI,oBAAoB,KAAK,eAAe,QAAQ,eAAe,SAAS,SAAS,WAAW,aAAa,QAAQ,OAAO,SAAS,KAAK;AAC1I,SAAQjB,kBAAG,IAAC,SAAS,EAAE,OAAO,OAAO,MAAM,WAAW,WAAW,MAAM,UAAUA,kBAAAA,IAAI,kBAAkB,CAAA,CAAE,EAAC,CAAE;AAChH;AAGA,SAAS,WAAW,OAAO;AACvB,MAAI,KAAK,aAAc,GAAE,SAAS,GAAG,QAAQmB,aAAY,GAAG,WAAW;AACvE,SAAOnB,sBAAIM,kBAAAA,UAAU,EAAE,UAAUa,WAAU,MAAM,MAAM,EAAE,OAAc,CAAE,EAAC,CAAE;AAChF;AAQA,IAAI,wBAAwBpB,aAAa,cAAC,MAAS;AAEnD,SAAS,uBAAuB,OAAO;AACnC,MAAI,CAAC,oBAAoB,MAAM,YAAY,GAAG;AAC1C,QAAI,oBAAoB;AAAA,MACpB,UAAU;AAAA,MACV,WAAW;AAAA,QACP,UAAU,CAAA;AAAA,MAC1B;AAAA,IACS;AACD,WAAQC,kBAAG,IAAC,sBAAsB,UAAU,EAAE,OAAO,mBAAmB,UAAU,MAAM,UAAU;AAAA,EAC1G;AACI,SAAQA,kBAAG,IAAC,gCAAgC,EAAE,cAAc,MAAM,cAAc,UAAU,MAAM,UAAU;AAC9G;AACA,SAAS,+BAA+B,IAAI;AACxC,MAAI,eAAe,GAAG,cAAc,WAAW,GAAG;AAClD,MAAI,WAAW,aAAa,UAAUoB,OAAM,aAAa,KAAKC,OAAM,aAAa;AACjF,MAAI,aAAa,SAAU,KAAK,iBAAiB,GAAG;AAChD,QAAIT,KAAI;AACR,KAACA,MAAK,aAAa,gBAAgB,QAAQA,QAAO,SAAS,SAASA,IAAG,KAAK,cAAc,KAAK,iBAAiB,CAAC;AACjH,QAAI,gBAAgB,QAAQ,gBAAgB,YAAYQ,SAAQ,aAAa,QAAQ,aAAa,SAAS,SAAS,SAAS,YAAYA,IAAG;AAC5I,QAAI,eAAe;AACf;AAAA,IACZ;AACQ,QAAI,gBAAgB,QAAQ,CAAC,gBAAgB,YAAYC,SAAQ,aAAa,QAAQ,aAAa,SAAS,SAAS,SAAS,YAAYA,IAAG;AAC7I,QAAI,eAAe;AACf;AAAA,IACZ;AACQ,QAAI,eAAe,WAAW,cAAc,CAAA,GAAI,QAAc,IAAI,CAAE;AACpE,QAAI,gBAAgB,UAAU;AAC1B,UAAI,QAAQ,aAAa,UAAU,SAAU,aAAa;AACtD,eAAO,UAAU,KAAK,WAAW;AAAA,MACjD,CAAa;AACD,mBAAa,OAAO,OAAO,CAAC;AAAA,IACxC,OACa;AACD,mBAAa,KAAK,GAAG;AAAA,IACjC;AACQ,KAAC,KAAK,aAAa,cAAc,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,cAAc,cAAc,KAAK,iBAAiB,CAAC;AAAA,EAChI;AACD,MAAI,YAAY;AAAA,IACZ,UAAU,CAAA;AAAA,EACb;AACD,MAAI,UAAU;AACV,cAAU,SAAS,KAAK,SAAU,KAAK;AACnC,UAAI,gBAAgBA,QAAO,SAAS,SAASA,OAAM;AACnD,UAAI,aAAa,SAAS,KAAK,SAAU,aAAa;AAClD,eAAO,UAAU,aAAa,GAAG;AAAA,MACjD,CAAa;AACD,aAAO,QAAQ,iBAAiB,CAAC,UAAU;AAAA,IACvD,CAAS;AAAA,EACT;AACI,MAAI,eAAe;AAAA,IACf;AAAA,IACA;AAAA,IACA;AAAA,EACH;AACD,SAAQrB,kBAAG,IAAC,sBAAsB,UAAU,EAAE,OAAO,cAAc,UAAoB;AAC3F;AAMA,SAAS,oBAAoB;AACzB,MAAI,UAAUC,aAAU,WAAC,qBAAqB;AAC9C,MAAI,CAAC,SAAS;AACV,UAAM,IAAI,MAAM,gEAAgE;AAAA,EACxF;AACI,SAAO;AACX;AAQA,SAAS,WAAW,KAAK,OAAO;AAC5B,MAAI,KAAK,SAAS,IAAI,OAAO,GAAG,MAAM,KAAK,GAAG;AAC9C,MAAI,QAAQ,IAAI;AACZ,QAAI,UAAU,IAAI,GAAG,KAAK,UAAU,MAAM,GAAG,GAAG;AAC5C,aAAO;AAAA,IACnB;AACQ,QAAI,UAAU,IAAI,GAAG,GAAG;AACpB,aAAO,EAAE,MAAM,IAAI,IAAI,OAAW;AAAA,IAC9C;AACQ,QAAI,UAAU,MAAM,GAAG,GAAG;AACtB,aAAO;AAAA,IACnB;AACQ,QAAI,QAAQ,MAAM,GAAG,GAAG;AACpB,aAAO,EAAE,MAAM,KAAK,GAAQ;AAAA,IACxC;AACQ,WAAO,EAAE,MAAY,IAAI,IAAK;AAAA,EACtC;AACI,MAAI,IAAI;AACJ,QAAI,QAAQ,KAAK,EAAE,GAAG;AAClB,aAAO,EAAE,MAAM,IAAI,IAAI,IAAK;AAAA,IACxC;AACQ,WAAO,EAAE,MAAM,KAAK,GAAQ;AAAA,EACpC;AACI,MAAI,MAAM;AACN,QAAI,SAAS,KAAK,IAAI,GAAG;AACrB,aAAO,EAAE,MAAM,KAAK,IAAI,KAAM;AAAA,IAC1C;AACQ,WAAO,EAAE,MAAY,IAAI,IAAK;AAAA,EACtC;AACI,SAAO,EAAE,MAAM,KAAK,IAAI,OAAW;AACvC;AAQA,IAAI,qBAAqBF,aAAa,cAAC,MAAS;AAEhD,SAAS,oBAAoB,OAAO;AAChC,MAAI,CAAC,iBAAiB,MAAM,YAAY,GAAG;AACvC,QAAI,oBAAoB;AAAA,MACpB,UAAU;AAAA,MACV,WAAW;AAAA,QACP,aAAa,CAAE;AAAA,QACf,WAAW,CAAE;AAAA,QACb,cAAc,CAAE;AAAA,QAChB,UAAU,CAAA;AAAA,MAC1B;AAAA,IACS;AACD,WAAQC,kBAAG,IAAC,mBAAmB,UAAU,EAAE,OAAO,mBAAmB,UAAU,MAAM,UAAU;AAAA,EACvG;AACI,SAAQA,kBAAG,IAAC,6BAA6B,EAAE,cAAc,MAAM,cAAc,UAAU,MAAM,UAAU;AAC3G;AACA,SAAS,4BAA4B,IAAI;AACrC,MAAI,eAAe,GAAG,cAAc,WAAW,GAAG;AAClD,MAAI,WAAW,aAAa;AAC5B,MAAI,KAAK,YAAY,IAAI,eAAe,GAAG,MAAM,aAAa,GAAG;AACjE,MAAIoB,OAAM,aAAa;AACvB,MAAIC,OAAM,aAAa;AACvB,MAAI,aAAa,SAAU,KAAK,iBAAiB,GAAG;AAChD,QAAIT,KAAIU;AACR,KAACV,MAAK,aAAa,gBAAgB,QAAQA,QAAO,SAAS,SAASA,IAAG,KAAK,cAAc,KAAK,iBAAiB,CAAC;AACjH,QAAI,WAAW,WAAW,KAAK,QAAQ;AACvC,KAACU,MAAK,aAAa,cAAc,QAAQA,QAAO,SAAS,SAASA,IAAG,KAAK,cAAc,UAAU,KAAK,iBAAiB,CAAC;AAAA,EAC5H;AACD,MAAI,YAAY;AAAA,IACZ,aAAa,CAAE;AAAA,IACf,WAAW,CAAE;AAAA,IACb,cAAc,CAAE;AAAA,IAChB,UAAU,CAAA;AAAA,EACb;AACD,MAAI,cAAc;AACd,cAAU,cAAc,CAAC,YAAY;AACrC,QAAI,CAAC,YAAY;AACb,gBAAU,YAAY,CAAC,YAAY;AAAA,IAC/C,OACa;AACD,gBAAU,YAAY,CAAC,UAAU;AACjC,UAAI,CAAC,UAAU,cAAc,UAAU,GAAG;AACtC,kBAAU,eAAe;AAAA,UACrB;AAAA,YACI,OAAO;AAAA,YACP,QAAQ;AAAA,UAChC;AAAA,QACiB;AAAA,MACjB;AAAA,IACA;AAAA,EACA,WACa,YAAY;AACjB,cAAU,cAAc,CAAC,UAAU;AACnC,cAAU,YAAY,CAAC,UAAU;AAAA,EACzC;AACI,MAAIF,MAAK;AACL,QAAI,gBAAgB,CAAC,YAAY;AAC7B,gBAAU,SAAS,KAAK;AAAA,QACpB,OAAO,QAAQ,cAAcA,OAAM,CAAC;AAAA,QACpC,QAAQ,QAAQ,cAAcA,OAAM,CAAC;AAAA,MACrD,CAAa;AAAA,IACb;AACQ,QAAI,gBAAgB,YAAY;AAC5B,gBAAU,SAAS,KAAK;AAAA,QACpB,OAAO;AAAA,QACP,QAAQ,QAAQ,cAAcA,OAAM,CAAC;AAAA,MACrD,CAAa;AAAA,IACb;AACQ,QAAI,CAAC,gBAAgB,YAAY;AAC7B,gBAAU,SAAS,KAAK;AAAA,QACpB,OAAO,QAAQ,YAAYA,OAAM,CAAC;AAAA,QAClC,QAAQ,QAAQ,YAAYA,OAAM,CAAC;AAAA,MACnD,CAAa;AAAA,IACb;AAAA,EACA;AACI,MAAIC,MAAK;AACL,QAAI,gBAAgB,CAAC,YAAY;AAC7B,gBAAU,SAAS,KAAK;AAAA,QACpB,QAAQ,QAAQ,cAAc,CAACA,OAAM,CAAC;AAAA,MACtD,CAAa;AACD,gBAAU,SAAS,KAAK;AAAA,QACpB,OAAO,QAAQ,cAAcA,OAAM,CAAC;AAAA,MACpD,CAAa;AAAA,IACb;AACQ,QAAI,gBAAgB,YAAY;AAC5B,UAAI,gBAAgB,yBAAyB,YAAY,YAAY,IAAI;AACzE,UAAI,SAASA,OAAM;AACnB,gBAAU,SAAS,KAAK;AAAA,QACpB,QAAQ,QAAQ,cAAc,MAAM;AAAA,MACpD,CAAa;AACD,gBAAU,SAAS,KAAK;AAAA,QACpB,OAAO,QAAQ,YAAY,MAAM;AAAA,MACjD,CAAa;AAAA,IACb;AACQ,QAAI,CAAC,gBAAgB,YAAY;AAC7B,gBAAU,SAAS,KAAK;AAAA,QACpB,QAAQ,QAAQ,YAAY,CAACA,OAAM,CAAC;AAAA,MACpD,CAAa;AACD,gBAAU,SAAS,KAAK;AAAA,QACpB,OAAO,QAAQ,YAAYA,OAAM,CAAC;AAAA,MAClD,CAAa;AAAA,IACb;AAAA,EACA;AACI,SAAQrB,kBAAAA,IAAI,mBAAmB,UAAU,EAAE,OAAO,EAAE,UAAoB,YAAwB,UAAsB,GAAE,SAAkB,CAAE;AAChJ;AAMA,SAAS,iBAAiB;AACtB,MAAI,UAAUC,aAAU,WAAC,kBAAkB;AAC3C,MAAI,CAAC,SAAS;AACV,UAAM,IAAI,MAAM,0DAA0D;AAAA,EAClF;AACI,SAAO;AACX;AAGA,SAAS,eAAe,SAAS;AAC7B,MAAI,MAAM,QAAQ,OAAO,GAAG;AACxB,WAAO,cAAc,CAAE,GAAE,OAAa;AAAA,EAC9C,WACa,YAAY,QAAW;AAC5B,WAAO,CAAC,OAAO;AAAA,EACvB,OACS;AACD,WAAO,CAAE;AAAA,EACjB;AACA;AAGA,SAAS,mBAAmB,cAAc;AACtC,MAAI,kBAAkB,CAAE;AACxB,SAAO,QAAQ,YAAY,EAAE,QAAQ,SAAU,IAAI;AAC/C,QAAI,WAAW,GAAG,CAAC,GAAG,UAAU,GAAG,CAAC;AACpC,oBAAgB,QAAQ,IAAI,eAAe,OAAO;AAAA,EAC1D,CAAK;AACD,SAAO;AACX;AAGA,IAAI;AAAA,CACH,SAAUsB,mBAAkB;AACzB,EAAAA,kBAAiB,SAAS,IAAI;AAE9B,EAAAA,kBAAiB,UAAU,IAAI;AAE/B,EAAAA,kBAAiB,UAAU,IAAI;AAE/B,EAAAA,kBAAiB,QAAQ,IAAI;AAE7B,EAAAA,kBAAiB,OAAO,IAAI;AAE5B,EAAAA,kBAAiB,YAAY,IAAI;AAEjC,EAAAA,kBAAiB,UAAU,IAAI;AAE/B,EAAAA,kBAAiB,aAAa,IAAI;AACtC,GAAG,qBAAqB,mBAAmB,CAAA,EAAG;AAE9C,IAAI,WAAW,iBAAiB,UAAU,WAAW,iBAAiB,UAAU,SAAS,iBAAiB,QAAQ,QAAQ,iBAAiB,OAAO,WAAW,iBAAiB,UAAU,cAAc,iBAAiB,aAAa,aAAa,iBAAiB,YAAY,UAAU,iBAAiB;AAEzS,SAAS,qBAAqB,WAAW,gBAAgB,aAAa;AAClE,MAAI;AACJ,MAAI,qBAAqB,KAAK,CAAE,GAC5B,GAAG,QAAQ,IAAI,eAAe,UAAU,QAAQ,GAChD,GAAG,QAAQ,IAAI,eAAe,UAAU,QAAQ,GAChD,GAAG,MAAM,IAAI,eAAe,UAAU,MAAM,GAC5C,GAAG,KAAK,IAAI,CAAC,UAAU,KAAK,GAC5B,GAAG,QAAQ,IAAI,CAAE,GACjB,GAAG,WAAW,IAAI,CAAE,GACpB,GAAG,UAAU,IAAI,CAAE,GACnB,GAAG,OAAO,IAAI,CAAE,GAChB;AACJ,MAAI,UAAU,UAAU;AACpB,sBAAkB,QAAQ,EAAE,KAAK,EAAE,QAAQ,UAAU,UAAU;AAAA,EACvE;AACI,MAAI,UAAU,QAAQ;AAClB,sBAAkB,QAAQ,EAAE,KAAK,EAAE,OAAO,UAAU,QAAQ;AAAA,EACpE;AACI,MAAI,oBAAoB,SAAS,GAAG;AAChC,sBAAkB,QAAQ,IAAI,kBAAkB,QAAQ,EAAE,OAAO,eAAe,UAAU,QAAQ,CAAC;AAAA,EAC3G,WACa,iBAAiB,SAAS,GAAG;AAClC,sBAAkB,QAAQ,IAAI,kBAAkB,QAAQ,EAAE,OAAO,YAAY,UAAU,QAAQ,CAAC;AAChG,sBAAkB,UAAU,IAAI,YAAY,UAAU,UAAU;AAChE,sBAAkB,WAAW,IAAI,YAAY,UAAU,WAAW;AAClE,sBAAkB,QAAQ,IAAI,YAAY,UAAU,QAAQ;AAAA,EACpE;AACI,SAAO;AACX;AAGA,IAAI,mBAAmBxB,aAAa,cAAC,MAAS;AAE9C,SAAS,kBAAkB,OAAO;AAC9B,MAAI,YAAY,aAAc;AAC9B,MAAI,iBAAiB,kBAAmB;AACxC,MAAI,cAAc,eAAgB;AAClC,MAAI,oBAAoB,qBAAqB,WAAW,gBAAgB,WAAW;AACnF,MAAI,kBAAkB,mBAAmB,UAAU,SAAS;AAC5D,MAAI,YAAY,SAAS,SAAS,CAAA,GAAI,iBAAiB,GAAG,eAAe;AACzE,SAAQC,kBAAG,IAAC,iBAAiB,UAAU,EAAE,OAAO,WAAW,UAAU,MAAM,UAAU;AACzF;AAQA,SAAS,eAAe;AACpB,MAAI,UAAUC,aAAU,WAAC,gBAAgB;AACzC,MAAI,CAAC,SAAS;AACV,UAAM,IAAI,MAAM,sDAAsD;AAAA,EAC9E;AACI,SAAO;AACX;AAGA,SAAS,eAAe,SAAS;AAC7B,SAAO,QAAQ,WACX,OAAO,YAAY,YACnB,YAAY,WACZ,WAAW,OAAO;AAC1B;AAEA,SAAS,YAAY,OAAO;AACxB,SAAO,QAAQ,SAAS,OAAO,UAAU,YAAY,UAAU,KAAK;AACxE;AAEA,SAAS,gBAAgB,OAAO;AAC5B,SAAO,QAAQ,SAAS,OAAO,UAAU,YAAY,WAAW,KAAK;AACzE;AAEA,SAAS,iBAAiB,OAAO;AAC7B,SAAO,QAAQ,SAAS,OAAO,UAAU,YAAY,YAAY,KAAK;AAC1E;AAEA,SAAS,gBAAgB,OAAO;AAC5B,SAAO,QAAQ,SAAS,OAAO,UAAU,YAAY,eAAe,KAAK;AAC7E;AAGA,SAAS,cAAc,MAAM,OAAO;AAChC,MAAI;AACJ,MAAI,OAAO,MAAM,MAAM,KAAK,MAAM;AAClC,MAAI,QAAQ,IAAI;AACZ,QAAI,kBAAkB,yBAAyB,IAAI,IAAI,IAAI;AAC3D,QAAI,iBAAiB;AACjB,WAAK,CAAC,IAAI,IAAI,GAAG,OAAO,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC;AAAA,IACpD;AACQ,QAAI,YAAY,yBAAyB,MAAM,IAAI,KAAK,KACpD,yBAAyB,IAAI,IAAI,KAAK;AAC1C,WAAO;AAAA,EACf;AACI,MAAI,IAAI;AACJ,WAAO,UAAU,IAAI,IAAI;AAAA,EACjC;AACI,MAAI,MAAM;AACN,WAAO,UAAU,MAAM,IAAI;AAAA,EACnC;AACI,SAAO;AACX;AAGA,SAAS,WAAW,OAAO;AACvB,SAAO,OAAO,KAAK;AACvB;AAEA,SAAS,eAAe,OAAO;AAC3B,SAAO,MAAM,QAAQ,KAAK,KAAK,MAAM,MAAM,MAAM;AACrD;AAkBA,SAAS,QAAQ,KAAK,UAAU;AAC5B,SAAO,SAAS,KAAK,SAAU,SAAS;AACpC,QAAI,OAAO,YAAY,WAAW;AAC9B,aAAO;AAAA,IACnB;AACQ,QAAI,WAAW,OAAO,GAAG;AACrB,aAAO,UAAU,KAAK,OAAO;AAAA,IACzC;AACQ,QAAI,eAAe,OAAO,GAAG;AACzB,aAAO,QAAQ,SAAS,GAAG;AAAA,IACvC;AACQ,QAAI,YAAY,OAAO,GAAG;AACtB,aAAO,cAAc,KAAK,OAAO;AAAA,IAC7C;AACQ,QAAI,gBAAgB,OAAO,GAAG;AAC1B,aAAO,QAAQ,UAAU,SAAS,IAAI,OAAM,CAAE;AAAA,IAC1D;AACQ,QAAI,eAAe,OAAO,GAAG;AACzB,UAAI,aAAa,yBAAyB,QAAQ,QAAQ,GAAG;AAC7D,UAAI,YAAY,yBAAyB,QAAQ,OAAO,GAAG;AAC3D,UAAI,cAAc,aAAa;AAC/B,UAAI,aAAa,YAAY;AAC7B,UAAI,mBAAmB,QAAQ,QAAQ,QAAQ,QAAQ,KAAK;AAC5D,UAAI,kBAAkB;AAClB,eAAO,cAAc;AAAA,MACrC,OACiB;AACD,eAAO,eAAe;AAAA,MACtC;AAAA,IACA;AACQ,QAAI,gBAAgB,OAAO,GAAG;AAC1B,aAAO,yBAAyB,KAAK,QAAQ,KAAK,IAAI;AAAA,IAClE;AACQ,QAAI,iBAAiB,OAAO,GAAG;AAC3B,aAAO,yBAAyB,QAAQ,QAAQ,GAAG,IAAI;AAAA,IACnE;AACQ,QAAI,OAAO,YAAY,YAAY;AAC/B,aAAO,QAAQ,GAAG;AAAA,IAC9B;AACQ,WAAO;AAAA,EACf,CAAK;AACL;AAGA,SAAS,mBAAmB,KAE5B,WAEA,cAAc;AACV,MAAI,mBAAmB,OAAO,KAAK,SAAS,EAAE,OAAO,SAAU,QAAQ,KAAK;AACxE,QAAI,WAAW,UAAU,GAAG;AAC5B,QAAI,QAAQ,KAAK,QAAQ,GAAG;AACxB,aAAO,KAAK,GAAG;AAAA,IAC3B;AACQ,WAAO;AAAA,EACV,GAAE,EAAE;AACL,MAAI,kBAAkB,CAAE;AACxB,mBAAiB,QAAQ,SAAU,UAAU;AAAE,WAAQ,gBAAgB,QAAQ,IAAI;AAAA,GAAQ;AAC3F,MAAI,gBAAgB,CAAC,YAAY,KAAK,YAAY,GAAG;AACjD,oBAAgB,UAAU;AAAA,EAClC;AACI,SAAO;AACX;AAUA,SAAS,sBAAsB,eAAe,WAAW;AACrD,MAAI,kBAAkB,aAAa,cAAc,CAAC,CAAC;AACnD,MAAI,iBAAiB,WAAW,cAAc,cAAc,SAAS,CAAC,CAAC;AAEvE,MAAI;AACJ,MAAI;AACJ,MAAI,OAAO;AACX,SAAO,QAAQ,gBAAgB;AAC3B,QAAI,kBAAkB,mBAAmB,MAAM,SAAS;AACxD,QAAI,cAAc,CAAC,gBAAgB,YAAY,CAAC,gBAAgB;AAChE,QAAI,CAAC,aAAa;AACd,aAAO,QAAQ,MAAM,CAAC;AACtB;AAAA,IACZ;AACQ,QAAI,gBAAgB,UAAU;AAC1B,aAAO;AAAA,IACnB;AACQ,QAAI,gBAAgB,SAAS,CAAC,OAAO;AACjC,cAAQ;AAAA,IACpB;AACQ,QAAI,CAAC,mBAAmB;AACpB,0BAAoB;AAAA,IAChC;AACQ,WAAO,QAAQ,MAAM,CAAC;AAAA,EAC9B;AACI,MAAI,OAAO;AACP,WAAO;AAAA,EACf,OACS;AACD,WAAO;AAAA,EACf;AACA;AAEA,IAAI,YAAY;AAEhB,SAAS,aAAa,YAAY,SAAS;AACvC,MAAI,SAAS,QAAQ,QAAQ,YAAY,QAAQ,WAAW,UAAU,QAAQ,SAAS,YAAY,QAAQ,WAAW,KAAK,QAAQ,OAAO,QAAQ,OAAO,SAAS,EAAE,OAAO,GAAG,aAAa,WAAY,IAAG;AAC1M,MAAI,eAAe,QAAQ,cAAc,WAAW,QAAQ,UAAUH,UAAS,QAAQ,QAAQ,SAAS,QAAQ;AAChH,MAAI,UAAU;AAAA,IACV,KAAK;AAAA,IACL,MAAM;AAAA,IACN,OAAO;AAAA,IACP,MAAM;AAAA,IACN,aAAa,SAAU,MAAM;AACzB,aAAO,QAAQ,UACT,eAAe,IAAI,IACnB,YAAY,MAAM,EAAE,QAAgB,cAA4B;AAAA,IACzE;AAAA,IACD,WAAW,SAAU,MAAM;AACvB,aAAO,QAAQ,UACT,aAAa,IAAI,IACjB,UAAU,MAAM,EAAE,QAAgB,cAA4B;AAAA,IAChF;AAAA,EACK;AACD,MAAI,gBAAgB,QAAQ,MAAM,EAAE,YAAY,cAAc,UAAU,IAAI,EAAE;AAC9E,MAAI,cAAc,YAAY,UAAU;AACpC,oBAAgB,IAAI,CAAC,UAAU,aAAa,CAAC;AAAA,EACrD,WACa,cAAc,WAAWA,SAAQ;AACtC,oBAAgB,IAAI,CAACA,SAAQ,aAAa,CAAC;AAAA,EACnD;AACI,MAAI,cAAc;AAClB,MAAI,WAAW;AACX,QAAI,kBAAkB,mBAAmB,eAAe,SAAS;AACjE,kBAAc,CAAC,gBAAgB,YAAY,CAAC,gBAAgB;AAAA,EACpE;AACI,MAAI,aAAa;AACb,WAAO;AAAA,EACf,OACS;AACD,QAAI,MAAM,QAAQ,WAAW;AACzB,aAAO,MAAM;AAAA,IACzB;AACQ,WAAO,aAAa,eAAe;AAAA,MAC/B;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,OAAO,SAAS,SAAS,IAAI,KAAK,GAAG,EAAE,OAAO,MAAM,QAAQ,EAAG,CAAA;AAAA,IAC3E,CAAS;AAAA,EACT;AACA;AAOA,IAAI,eAAeC,aAAa,cAAC,MAAS;AAE1C,SAAS,cAAc,OAAO;AAC1B,MAAI,aAAa,cAAe;AAChC,MAAI,YAAY,aAAc;AAC9B,MAAI,KAAKW,sBAAU,GAAE,aAAa,GAAG,CAAC,GAAG,gBAAgB,GAAG,CAAC;AAC7D,MAAI,KAAKA,sBAAU,GAAE,cAAc,GAAG,CAAC,GAAG,iBAAiB,GAAG,CAAC;AAC/D,MAAI,qBAAqB,sBAAsB,WAAW,eAAe,SAAS;AAElF,MAAI,eAAe,eAAe,QAAQ,eAAe,SAAS,aAAc,eAAe,WAAW,gBAAgB,WAAW,KAC/H,cACA;AACN,MAAI,OAAO,WAAY;AACnB,mBAAe,UAAU;AACzB,kBAAc,MAAS;AAAA,EAC1B;AACD,MAAI,QAAQ,SAAU,MAAM;AACxB,kBAAc,IAAI;AAAA,EACrB;AACD,MAAI,UAAU,aAAc;AAC5B,MAAI,YAAY,SAAU,QAAQ,WAAW;AACzC,QAAI,CAAC;AACD;AACJ,QAAI,cAAc,aAAa,YAAY;AAAA,MACvC;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACZ,CAAS;AACD,QAAI,UAAU,YAAY,WAAW;AACjC,aAAO;AACX,eAAW,SAAS,aAAa,UAAU;AAC3C,UAAM,WAAW;AAAA,EACpB;AACD,MAAI,QAAQ;AAAA,IACR;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,eAAe,WAAY;AAAE,aAAO,UAAU,OAAO,OAAO;AAAA,IAAI;AAAA,IAChE,gBAAgB,WAAY;AAAE,aAAO,UAAU,OAAO,QAAQ;AAAA,IAAI;AAAA,IAClE,gBAAgB,WAAY;AAAE,aAAO,UAAU,QAAQ,OAAO;AAAA,IAAI;AAAA,IAClE,iBAAiB,WAAY;AAAE,aAAO,UAAU,QAAQ,QAAQ;AAAA,IAAI;AAAA,IACpE,kBAAkB,WAAY;AAAE,aAAO,UAAU,SAAS,QAAQ;AAAA,IAAI;AAAA,IACtE,iBAAiB,WAAY;AAAE,aAAO,UAAU,SAAS,OAAO;AAAA,IAAI;AAAA,IACpE,iBAAiB,WAAY;AAAE,aAAO,UAAU,QAAQ,QAAQ;AAAA,IAAI;AAAA,IACpE,gBAAgB,WAAY;AAAE,aAAO,UAAU,QAAQ,OAAO;AAAA,IAAI;AAAA,IAClE,kBAAkB,WAAY;AAAE,aAAO,UAAU,eAAe,QAAQ;AAAA,IAAI;AAAA,IAC5E,gBAAgB,WAAY;AAAE,aAAO,UAAU,aAAa,OAAO;AAAA,IAAE;AAAA,EACxE;AACD,SAAQV,kBAAG,IAAC,aAAa,UAAU,EAAE,OAAc,UAAU,MAAM,UAAU;AACjF;AAOA,SAAS,kBAAkB;AACvB,MAAI,UAAUC,aAAU,WAAC,YAAY;AACrC,MAAI,CAAC,SAAS;AACV,UAAM,IAAI,MAAM,qDAAqD;AAAA,EAC7E;AACI,SAAO;AACX;AAUA,SAAS,mBAAmB,KAK5B,cAAc;AACV,MAAI,YAAY,aAAc;AAC9B,MAAI,kBAAkB,mBAAmB,KAAK,WAAW,YAAY;AACrE,SAAO;AACX;AAQA,IAAI,sBAAsBF,aAAa,cAAC,MAAS;AAEjD,SAAS,qBAAqB,OAAO;AACjC,MAAI,CAAC,kBAAkB,MAAM,YAAY,GAAG;AACxC,QAAI,oBAAoB;AAAA,MACpB,UAAU;AAAA,IACb;AACD,WAAQC,kBAAG,IAAC,oBAAoB,UAAU,EAAE,OAAO,mBAAmB,UAAU,MAAM,UAAU;AAAA,EACxG;AACI,SAAQA,kBAAG,IAAC,8BAA8B,EAAE,cAAc,MAAM,cAAc,UAAU,MAAM,UAAU;AAC5G;AACA,SAAS,6BAA6B,IAAI;AACtC,MAAI,eAAe,GAAG,cAAc,WAAW,GAAG;AAClD,MAAI,aAAa,SAAU,KAAK,iBAAiB,GAAG;AAChD,QAAIY,KAAI,IAAI;AACZ,KAACA,MAAK,aAAa,gBAAgB,QAAQA,QAAO,SAAS,SAASA,IAAG,KAAK,cAAc,KAAK,iBAAiB,CAAC;AACjH,QAAI,gBAAgB,YAAY,CAAC,aAAa,UAAU;AACpD,OAAC,KAAK,aAAa,cAAc,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,cAAc,QAAW,KAAK,iBAAiB,CAAC;AAC1H;AAAA,IACZ;AACQ,KAAC,KAAK,aAAa,cAAc,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,cAAc,KAAK,KAAK,iBAAiB,CAAC;AAAA,EACvH;AACD,MAAI,eAAe;AAAA,IACf,UAAU,aAAa;AAAA,IACvB;AAAA,EACH;AACD,SAAQZ,kBAAG,IAAC,oBAAoB,UAAU,EAAE,OAAO,cAAc,UAAoB;AACzF;AAMA,SAAS,kBAAkB;AACvB,MAAI,UAAUC,aAAU,WAAC,mBAAmB;AAC5C,MAAI,CAAC,SAAS;AACV,UAAM,IAAI,MAAM,4DAA4D;AAAA,EACpF;AACI,SAAO;AACX;AAsBA,SAAS,oBAAoB,MAAM,iBAAiB;AAChD,MAAI,YAAY,aAAc;AAC9B,MAAI,SAAS,gBAAiB;AAC9B,MAAI,WAAW,kBAAmB;AAClC,MAAI,QAAQ,eAAgB;AAC5B,MAAI,KAAK,gBAAe,GAAI,gBAAgB,GAAG,eAAe,iBAAiB,GAAG,gBAAgB,iBAAiB,GAAG,gBAAgB,kBAAkB,GAAG,iBAAiB,OAAO,GAAG,MAAM,QAAQ,GAAG,OAAO,mBAAmB,GAAG,kBAAkB,kBAAkB,GAAG,iBAAiB,kBAAkB,GAAG,iBAAiB,iBAAiB,GAAG,gBAAgB,mBAAmB,GAAG,kBAAkB,iBAAiB,GAAG;AACla,MAAI,UAAU,SAAU,GAAG;AACvB,QAAIW,KAAI,IAAI,IAAI;AAChB,QAAI,kBAAkB,SAAS,GAAG;AAC9B,OAACA,MAAK,OAAO,gBAAgB,QAAQA,QAAO,SAAS,SAASA,IAAG,KAAK,QAAQ,MAAM,iBAAiB,CAAC;AAAA,IAClH,WACiB,oBAAoB,SAAS,GAAG;AACrC,OAAC,KAAK,SAAS,gBAAgB,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,UAAU,MAAM,iBAAiB,CAAC;AAAA,IACtH,WACiB,iBAAiB,SAAS,GAAG;AAClC,OAAC,KAAK,MAAM,gBAAgB,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,OAAO,MAAM,iBAAiB,CAAC;AAAA,IAChH,OACa;AACD,OAAC,KAAK,UAAU,gBAAgB,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,WAAW,MAAM,iBAAiB,CAAC;AAAA,IACxH;AAAA,EACK;AACD,MAAI,UAAU,SAAU,GAAG;AACvB,QAAIA;AACJ,UAAM,IAAI;AACV,KAACA,MAAK,UAAU,gBAAgB,QAAQA,QAAO,SAAS,SAASA,IAAG,KAAK,WAAW,MAAM,iBAAiB,CAAC;AAAA,EAC/G;AACD,MAAI,SAAS,SAAU,GAAG;AACtB,QAAIA;AACJ,SAAM;AACN,KAACA,MAAK,UAAU,eAAe,QAAQA,QAAO,SAAS,SAASA,IAAG,KAAK,WAAW,MAAM,iBAAiB,CAAC;AAAA,EAC9G;AACD,MAAI,eAAe,SAAU,GAAG;AAC5B,QAAIA;AACJ,KAACA,MAAK,UAAU,qBAAqB,QAAQA,QAAO,SAAS,SAASA,IAAG,KAAK,WAAW,MAAM,iBAAiB,CAAC;AAAA,EACpH;AACD,MAAI,eAAe,SAAU,GAAG;AAC5B,QAAIA;AACJ,KAACA,MAAK,UAAU,qBAAqB,QAAQA,QAAO,SAAS,SAASA,IAAG,KAAK,WAAW,MAAM,iBAAiB,CAAC;AAAA,EACpH;AACD,MAAI,iBAAiB,SAAU,GAAG;AAC9B,QAAIA;AACJ,KAACA,MAAK,UAAU,uBAAuB,QAAQA,QAAO,SAAS,SAASA,IAAG,KAAK,WAAW,MAAM,iBAAiB,CAAC;AAAA,EACtH;AACD,MAAI,iBAAiB,SAAU,GAAG;AAC9B,QAAIA;AACJ,KAACA,MAAK,UAAU,uBAAuB,QAAQA,QAAO,SAAS,SAASA,IAAG,KAAK,WAAW,MAAM,iBAAiB,CAAC;AAAA,EACtH;AACD,MAAI,gBAAgB,SAAU,GAAG;AAC7B,QAAIA;AACJ,KAACA,MAAK,UAAU,sBAAsB,QAAQA,QAAO,SAAS,SAASA,IAAG,KAAK,WAAW,MAAM,iBAAiB,CAAC;AAAA,EACrH;AACD,MAAI,aAAa,SAAU,GAAG;AAC1B,QAAIA;AACJ,KAACA,MAAK,UAAU,mBAAmB,QAAQA,QAAO,SAAS,SAASA,IAAG,KAAK,WAAW,MAAM,iBAAiB,CAAC;AAAA,EAClH;AACD,MAAI,cAAc,SAAU,GAAG;AAC3B,QAAIA;AACJ,KAACA,MAAK,UAAU,oBAAoB,QAAQA,QAAO,SAAS,SAASA,IAAG,KAAK,WAAW,MAAM,iBAAiB,CAAC;AAAA,EACnH;AACD,MAAI,eAAe,SAAU,GAAG;AAC5B,QAAIA;AACJ,KAACA,MAAK,UAAU,qBAAqB,QAAQA,QAAO,SAAS,SAASA,IAAG,KAAK,WAAW,MAAM,iBAAiB,CAAC;AAAA,EACpH;AACD,MAAI,UAAU,SAAU,GAAG;AACvB,QAAIA;AACJ,KAACA,MAAK,UAAU,gBAAgB,QAAQA,QAAO,SAAS,SAASA,IAAG,KAAK,WAAW,MAAM,iBAAiB,CAAC;AAAA,EAC/G;AACD,MAAI,YAAY,SAAU,GAAG;AACzB,QAAIA;AACJ,YAAQ,EAAE,KAAG;AAAA,MACT,KAAK;AACD,UAAE,eAAgB;AAClB,UAAE,gBAAiB;AACnB,kBAAU,QAAQ,QAAQ,cAAa,IAAK,eAAgB;AAC5D;AAAA,MACJ,KAAK;AACD,UAAE,eAAgB;AAClB,UAAE,gBAAiB;AACnB,kBAAU,QAAQ,QAAQ,eAAc,IAAK,cAAe;AAC5D;AAAA,MACJ,KAAK;AACD,UAAE,eAAgB;AAClB,UAAE,gBAAiB;AACnB,uBAAgB;AAChB;AAAA,MACJ,KAAK;AACD,UAAE,eAAgB;AAClB,UAAE,gBAAiB;AACnB,wBAAiB;AACjB;AAAA,MACJ,KAAK;AACD,UAAE,eAAgB;AAClB,UAAE,gBAAiB;AACnB,UAAE,WAAW,gBAAiB,IAAG,iBAAkB;AACnD;AAAA,MACJ,KAAK;AACD,UAAE,eAAgB;AAClB,UAAE,gBAAiB;AACnB,UAAE,WAAW,eAAgB,IAAG,gBAAiB;AACjD;AAAA,MACJ,KAAK;AACD,UAAE,eAAgB;AAClB,UAAE,gBAAiB;AACnB,yBAAkB;AAClB;AAAA,MACJ,KAAK;AACD,UAAE,eAAgB;AAClB,UAAE,gBAAiB;AACnB,uBAAgB;AAChB;AAAA,IAChB;AACQ,KAACA,MAAK,UAAU,kBAAkB,QAAQA,QAAO,SAAS,SAASA,IAAG,KAAK,WAAW,MAAM,iBAAiB,CAAC;AAAA,EACjH;AACD,MAAI,gBAAgB;AAAA,IAChB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACH;AACD,SAAO;AACX;AASA,SAAS,kBAAkB;AACvB,MAAI,YAAY,aAAc;AAC9B,MAAI,SAAS,gBAAiB;AAC9B,MAAI,WAAW,kBAAmB;AAClC,MAAI,QAAQ,eAAgB;AAC5B,MAAI,eAAe,kBAAkB,SAAS,IACxC,OAAO,WACP,oBAAoB,SAAS,IACzB,SAAS,WACT,iBAAiB,SAAS,IACtB,MAAM,WACN;AACd,SAAO;AACX;AAEA,SAAS,mBAAmB,UAAU;AAClC,SAAO,OAAO,OAAO,gBAAgB,EAAE,SAAS,QAAQ;AAC5D;AAQA,SAAS,iBAAiB,WAAW,iBAAiB;AAClD,MAAI,aAAa,CAAC,UAAU,WAAW,GAAG;AAC1C,SAAO,KAAK,eAAe,EAAE,QAAQ,SAAU,UAAU;AACrD,QAAI,kBAAkB,UAAU,oBAAoB,QAAQ;AAC5D,QAAI,iBAAiB;AACjB,iBAAW,KAAK,eAAe;AAAA,IAC3C,WACiB,mBAAmB,QAAQ,GAAG;AACnC,UAAI,oBAAoB,UAAU,WAAW,OAAO,OAAO,QAAQ,CAAC;AACpE,UAAI,mBAAmB;AACnB,mBAAW,KAAK,iBAAiB;AAAA,MACjD;AAAA,IACA;AAAA,EACA,CAAK;AACD,SAAO;AACX;AAGA,SAAS,YAAY,WAAW,iBAAiB;AAC7C,MAAI,QAAQ,SAAS,CAAA,GAAI,UAAU,OAAO,GAAG;AAC7C,SAAO,KAAK,eAAe,EAAE,QAAQ,SAAU,UAAU;AACrD,QAAI;AACJ,YAAQ,SAAS,SAAS,CAAE,GAAE,KAAK,IAAI,KAAK,UAAU,qBAAqB,QAAQ,OAAO,SAAS,SAAS,GAAG,QAAQ,CAAC;AAAA,EAChI,CAAK;AACD,SAAO;AACX;AAQA,SAAS,aAET,KAEA,cAEA,WAAW;AACP,MAAI;AACJ,MAAI,IAAI;AACR,MAAI,YAAY,aAAc;AAC9B,MAAI,eAAe,gBAAiB;AACpC,MAAI,kBAAkB,mBAAmB,KAAK,YAAY;AAC1D,MAAI,gBAAgB,oBAAoB,KAAK,eAAe;AAC5D,MAAI,eAAe,gBAAiB;AACpC,MAAI,WAAW,QAAQ,UAAU,cAAc,UAAU,SAAS,SAAS;AAE3EY,eAAAA,UAAU,WAAY;AAClB,QAAIZ;AACJ,QAAI,gBAAgB;AAChB;AACJ,QAAI,CAAC,aAAa;AACd;AACJ,QAAI,CAAC;AACD;AACJ,QAAI,UAAU,aAAa,YAAY,GAAG,GAAG;AACzC,OAACA,MAAK,UAAU,aAAa,QAAQA,QAAO,SAAS,SAASA,IAAG,MAAO;AAAA,IACpF;AAAA,EACA,GAAO;AAAA,IACC,aAAa;AAAA,IACb;AAAA,IACA;AAAA,IACA;AAAA,IACA,gBAAgB;AAAA,EACxB,CAAK;AACD,MAAI,YAAY,iBAAiB,WAAW,eAAe,EAAE,KAAK,GAAG;AACrE,MAAI,QAAQ,YAAY,WAAW,eAAe;AAClD,MAAI,WAAW,QAAS,gBAAgB,WAAW,CAAC,UAAU,mBAC1D,gBAAgB,MAAM;AAC1B,MAAI,uBAAuB,MAAM,KAAK,UAAU,gBAAgB,QAAQ,OAAO,SAAS,SAAS,GAAG,gBAAgB,QAAQ,OAAO,SAAS,KAAK;AACjJ,MAAI,WAAYZ,kBAAAA,IAAI,qBAAqB,EAAE,MAAM,KAAK,cAA4B,gBAAkC,CAAA;AACpH,MAAI,WAAW;AAAA,IACX;AAAA,IACA;AAAA,IACA;AAAA,IACA,MAAM;AAAA,EACT;AACD,MAAI,gBAAgB,aAAa,eAC7B,UAAU,aAAa,aAAa,GAAG,KACvC,CAAC,gBAAgB;AACrB,MAAI,YAAY,aAAa,cAAc,UAAU,aAAa,YAAY,GAAG;AACjF,MAAI,cAAc,SAAS,SAAS,SAAS,CAAA,GAAI,QAAQ,IAAI,KAAK,EAAE,UAAU,gBAAgB,UAAU,MAAM,cAAc,GAAG,eAAe,IAAI,gBAAgB,UAAU,GAAG,WAAW,aAAa,gBAAgB,IAAI,IAAI,GAAE,GAAI,aAAa;AAClP,MAAI,YAAY;AAAA,IACZ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACH;AACD,SAAO;AACX;AAMA,SAAS,IAAI,OAAO;AAChB,MAAI,YAAYyB,aAAM,OAAC,IAAI;AAC3B,MAAI,YAAY,aAAa,MAAM,MAAM,MAAM,cAAc,SAAS;AACtE,MAAI,UAAU,UAAU;AACpB,WAAOzB,kBAAG,IAAC,OAAO,EAAE,MAAM,WAAU,CAAE;AAAA,EAC9C;AACI,MAAI,CAAC,UAAU,UAAU;AACrB,WAAOA,kBAAAA,IAAI,OAAO,SAAS,CAAA,GAAI,UAAU,QAAQ,CAAC;AAAA,EAC1D;AACI,SAAOA,sBAAI,QAAQ,SAAS,EAAE,MAAM,OAAO,KAAK,UAAS,GAAI,UAAU,WAAW,CAAC;AACvF;AAMA,SAAS,WAAW,OAAO;AACvB,MAAI,aAAa,MAAM,QAAQ,QAAQ,MAAM;AAC7C,MAAI,KAAK,gBAAgB,oBAAoB,GAAG,mBAAmB,SAAS,GAAG,QAAQ,aAAa,GAAG,YAAY,SAAS,GAAG,QAAQ0B,mBAAkB,GAAG,OAAO,iBAAiBC,oBAAmB,GAAG,WAAW;AACrN,MAAI,UAAUA,kBAAiB,OAAO,UAAU,GAAG,EAAE,QAAgB;AACrE,MAAI,CAAC,mBAAmB;AACpB,WAAQ3B,sBAAI,QAAQ,EAAE,WAAW,WAAW,YAAY,OAAO,OAAO,YAAY,UAAU,QAAO,CAAE;AAAA,EAC7G;AACI,MAAI,QAAQ0B,iBAAgB,OAAO,UAAU,GAAG,EAAE,QAAgB;AAClE,MAAI,cAAc,SAAU,GAAG;AAC3B,sBAAkB,YAAY,OAAO,CAAC;AAAA,EACzC;AACD,SAAQ1B,kBAAAA,IAAI,QAAQ,EAAE,MAAM,eAAe,cAAc,OAAO,WAAW,WAAW,YAAY,OAAO,OAAO,YAAY,SAAS,aAAa,UAAU,SAAS;AACzK;AAGA,SAAS,IAAI,OAAO;AAChB,MAAI,IAAI;AACR,MAAI,KAAK,aAAc,GAAE,SAAS,GAAG,QAAQ,aAAa,GAAG,YAAY,iBAAiB,GAAG,gBAAgB,aAAa,GAAG;AAC7H,MAAI,gBAAgB,KAAK,eAAe,QAAQ,eAAe,SAAS,SAAS,WAAW,SAAS,QAAQ,OAAO,SAAS,KAAK;AAClI,MAAI,uBAAuB,KAAK,eAAe,QAAQ,eAAe,SAAS,SAAS,WAAW,gBAAgB,QAAQ,OAAO,SAAS,KAAK;AAChJ,MAAI;AACJ,MAAI,gBAAgB;AAChB,qBAAkBA,kBAAAA,IAAI,MAAM,EAAE,WAAW,WAAW,MAAM,OAAO,OAAO,MAAM,UAAUA,kBAAAA,IAAI,qBAAqB,EAAE,QAAQ,MAAM,YAAY,OAAO,MAAM,OAAO,EAAC,CAAE;AAAA,EAC5K;AACI,SAAQG,kBAAI,KAAC,MAAM,EAAE,WAAW,WAAW,KAAK,OAAO,OAAO,KAAK,UAAU,CAAC,gBAAgB,MAAM,MAAM,IAAI,SAAU,MAAM;AAAE,WAAQH,kBAAG,IAAC,MAAM,EAAE,WAAW,WAAW,MAAM,OAAO,OAAO,MAAM,MAAM,gBAAgB,UAAUA,kBAAAA,IAAI,cAAc,EAAE,cAAc,MAAM,cAAc,KAAU,CAAE,EAAG,GAAE,YAAY,IAAI,CAAC;AAAA,GAAK,CAAC,GAAG;AACzU;AAGA,SAAS,iBAAiB,UAAUF,SAAQ,SAAS;AACjD,MAAI,UAAU,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,WAClE,aAAaA,OAAM,IACnB,UAAUA,SAAQ,OAAO;AAC/B,MAAI,YAAY,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,WACpE,eAAe,QAAQ,IACvB,YAAY,UAAU,OAAO;AACnC,MAAI,UAAU,yBAAyB,QAAQ,QAAQ;AACvD,MAAI,OAAO,CAAE;AACb,WAAS,IAAI,GAAG,KAAK,SAAS,KAAK;AAC/B,SAAK,KAAK,QAAQ,UAAU,CAAC,CAAC;AAAA,EACtC;AACI,MAAI,eAAe,KAAK,OAAO,SAAU,QAAQ,MAAM;AACnD,QAAI,cAAc,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,WACtE,WAAW,IAAI,IACf,QAAQ,MAAM,OAAO;AAC3B,QAAI,eAAe,OAAO,KAAK,SAAU,OAAO;AAAE,aAAO,MAAM,eAAe;AAAA,KAAa;AAC3F,QAAI,cAAc;AACd,mBAAa,MAAM,KAAK,IAAI;AAC5B,aAAO;AAAA,IACnB;AACQ,WAAO,KAAK;AAAA,MACR;AAAA,MACA,OAAO,CAAC,IAAI;AAAA,IACxB,CAAS;AACD,WAAO;AAAA,EACV,GAAE,EAAE;AACL,SAAO;AACX;AAMA,SAAS,cAAc,OAAO,SAAS;AACnC,MAAI,eAAe,iBAAiB,aAAa,KAAK,GAAG,WAAW,KAAK,GAAG,OAAO;AACnF,MAAI,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,eAAe;AAEzE,QAAI,iBAAiB,gBAAgB,OAAO,OAAO;AACnD,QAAI,iBAAiB,GAAG;AACpB,UAAI,WAAW,aAAa,aAAa,SAAS,CAAC;AACnD,UAAI,WAAW,SAAS,MAAM,SAAS,MAAM,SAAS,CAAC;AACvD,UAAIA,UAAS,SAAS,UAAU,IAAI,cAAc;AAClD,UAAI,aAAa,iBAAiB,SAAS,UAAU,CAAC,GAAGA,SAAQ,OAAO;AACxE,mBAAa,KAAK,MAAM,cAAc,UAAU;AAAA,IAC5D;AAAA,EACA;AACI,SAAO;AACX;AAGA,SAAS,MAAM,OAAO;AAClB,MAAI,IAAI,IAAI;AACZ,MAAI,KAAK,aAAY,GAAI,SAAS,GAAG,QAAQ,aAAa,GAAG,YAAY,SAAS,GAAG,QAAQ,WAAW,GAAG,UAAU,aAAa,GAAG,YAAY,aAAa,GAAG,YAAY,eAAe,GAAG,cAAc,wBAAwB,GAAG,uBAAuB,UAAU,GAAG;AAC5Q,MAAI,QAAQ,cAAc,MAAM,cAAc;AAAA,IAC1C,eAAe,QAAQ,UAAU;AAAA,IACjC;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACR,CAAK;AACD,MAAI,iBAAiB,KAAK,eAAe,QAAQ,eAAe,SAAS,SAAS,WAAW,UAAU,QAAQ,OAAO,SAAS,KAAK;AACpI,MAAI,gBAAgB,KAAK,eAAe,QAAQ,eAAe,SAAS,SAAS,WAAW,SAAS,QAAQ,OAAO,SAAS,KAAK;AAClI,MAAI,mBAAmB,KAAK,eAAe,QAAQ,eAAe,SAAS,SAAS,WAAW,YAAY,QAAQ,OAAO,SAAS,KAAK;AACxI,SAAQK,kBAAAA,KAAK,SAAS,EAAE,IAAI,MAAM,IAAI,WAAW,WAAW,OAAO,OAAO,OAAO,OAAO,MAAM,QAAQ,mBAAmB,MAAM,iBAAiB,GAAG,UAAU,CAAC,CAAC,YAAYH,kBAAAA,IAAI,eAAe,CAAA,CAAE,GAAGA,kBAAG,IAAC,SAAS,EAAE,WAAW,WAAW,OAAO,OAAO,OAAO,OAAO,UAAU,MAAM,IAAI,SAAU,MAAM;AAAE,WAAQA,kBAAG,IAAC,cAAc,EAAE,cAAc,MAAM,cAAc,OAAO,KAAK,OAAO,YAAY,KAAK,WAAY,GAAE,KAAK,UAAU;AAAA,EAAG,CAAE,EAAC,CAAE,GAAGA,kBAAG,IAAC,iBAAiB,EAAE,cAAc,MAAM,aAAc,CAAA,CAAC,EAAC,CAAE;AACrf;AAqEA,SAAS,YAAY;AACjB,SAAO,CAAC,EAAE,OAAO,WAAW,eACxB,OAAO,YACP,OAAO,SAAS;AACxB;AAyBA,IAAI,4BAA4B,cAAc4B,aAAAA,kBAAkBJ,aAAS;AACzE,IAAI,wBAAwB;AAC5B,IAAI,KAAK;AACT,SAAS,QAAQ;AACb,SAAO,oBAAoB,OAAO,EAAE,EAAE;AAC1C;AACA,SAAS,MAAM,YAAY;AAGvB,MAAI;AAGJ,MAAI,YAAY,eAAe,QAAQ,eAAe,SAAS,aAAc,wBAAwB,MAAO,IAAG;AAC/G,MAAI,KAAKd,aAAAA,SAAS,SAAS,GAAGmB,MAAK,GAAG,CAAC,GAAG,QAAQ,GAAG,CAAC;AACtD,4BAA0B,WAAY;AAClC,QAAIA,QAAO,MAAM;AAKb,YAAM,MAAK,CAAE;AAAA,IACzB;AAAA,EAEK,GAAE,EAAE;AACLL,eAAAA,UAAU,WAAY;AAClB,QAAI,0BAA0B,OAAO;AAIjC,8BAAwB;AAAA,IACpC;AAAA,EACK,GAAE,EAAE;AACL,UAAQ,KAAK,eAAe,QAAQ,eAAe,SAAS,aAAaK,SAAQ,QAAQ,OAAO,SAAS,KAAK;AAClH;AAGA,SAAS,MAAM,OAAO;AAClB,MAAI;AACJ,MAAI;AACJ,MAAI,YAAY,aAAc;AAC9B,MAAI,MAAM,UAAU,KAAK,aAAa,UAAU,YAAY,SAAS,UAAU,QAAQ,aAAa,UAAU;AAC9G,MAAI,gBAAgB,cAAa,EAAG;AACpC,MAAI,YAAY,MAAM,UAAU,KAAK,GAAG,OAAO,UAAU,IAAI,GAAG,EAAE,OAAO,MAAM,YAAY,IAAI,MAAS;AACxG,MAAI,UAAU,UAAU,KAClB,GAAG,OAAO,UAAU,IAAI,QAAQ,EAAE,OAAO,MAAM,YAAY,IAC3D;AACN,MAAI,YAAY,CAAC,WAAW,KAAK;AACjC,MAAI,QAAQ,OAAO;AACnB,MAAI,UAAU,MAAM,iBAAiB;AACrC,MAAI,QAAQ,MAAM,iBAAiB,cAAc,SAAS;AAC1D,MAAI,WAAW,CAAC,WAAW,CAAC;AAC5B,MAAI,QAAQ,OAAO;AACf,SAAK,CAAC,SAAS,KAAK,GAAG,QAAQ,GAAG,CAAC,GAAG,UAAU,GAAG,CAAC;AAAA,EAC5D;AACI,MAAI,SAAS;AACT,cAAU,KAAK,WAAW,aAAa;AACvC,YAAQ,SAAS,SAAS,CAAA,GAAI,KAAK,GAAG,OAAO,aAAa;AAAA,EAClE;AACI,MAAI,OAAO;AACP,cAAU,KAAK,WAAW,WAAW;AACrC,YAAQ,SAAS,SAAS,CAAA,GAAI,KAAK,GAAG,OAAO,WAAW;AAAA,EAChE;AACI,MAAI,UAAU;AACV,cAAU,KAAK,WAAW,eAAe;AACzC,YAAQ,SAAS,SAAS,CAAA,GAAI,KAAK,GAAG,OAAO,eAAe;AAAA,EACpE;AACI,MAAI,oBAAoB,KAAK,eAAe,QAAQ,eAAe,SAAS,SAAS,WAAW,aAAa,QAAQ,OAAO,SAAS,KAAK;AAC1I,SAAQ1B,kBAAAA,KAAK,OAAO,EAAE,WAAW,UAAU,KAAK,GAAG,GAAG,OAAc,UAAU,CAACH,sBAAI,kBAAkB,EAAE,IAAI,WAAW,cAAc,MAAM,cAAc,cAAc,MAAM,aAAc,CAAA,GAAGA,kBAAAA,IAAI,OAAO,EAAE,IAAI,SAAS,mBAAmB,WAAW,cAAc,MAAM,aAAc,CAAA,CAAC,EAAG,GAAE,MAAM,YAAY;AACnT;AAKA,SAAS,OAAO,OAAO;AACnB,MAAI,KAAK,aAAY,GAAI,aAAa,GAAG,YAAY,SAAS,GAAG;AACjE,SAAQA,kBAAG,IAAC,OAAO,EAAE,WAAW,WAAW,QAAQ,OAAO,OAAO,QAAQ,UAAU,MAAM,SAAQ,CAAE;AACvG;AAGA,SAAS,KAAK,IAAI;AACd,MAAI,IAAI;AACR,MAAI,eAAe,GAAG;AACtB,MAAI,YAAY,aAAc;AAC9B,MAAI,eAAe,gBAAiB;AACpC,MAAI,aAAa,cAAe;AAChC,MAAI,KAAKU,aAAAA,SAAS,KAAK,GAAG,kBAAkB,GAAG,CAAC,GAAG,qBAAqB,GAAG,CAAC;AAE5Ec,eAAAA,UAAU,WAAY;AAClB,QAAI,CAAC,UAAU;AACX;AACJ,QAAI,CAAC,aAAa;AACd;AACJ,QAAI;AACA;AACJ,iBAAa,MAAM,aAAa,WAAW;AAC3C,uBAAmB,IAAI;AAAA,EAC/B,GAAO;AAAA,IACC,UAAU;AAAA,IACV;AAAA,IACA,aAAa;AAAA,IACb,aAAa;AAAA,IACb;AAAA,EACR,CAAK;AAED,MAAI,aAAa,CAAC,UAAU,WAAW,MAAM,UAAU,SAAS;AAChE,MAAI,UAAU,iBAAiB,GAAG;AAC9B,eAAW,KAAK,UAAU,WAAW,eAAe;AAAA,EAC5D;AACI,MAAI,UAAU,gBAAgB;AAC1B,eAAW,KAAK,UAAU,WAAW,eAAe;AAAA,EAC5D;AACI,MAAI,QAAQ,SAAS,SAAS,CAAE,GAAE,UAAU,OAAO,IAAI,GAAG,UAAU,KAAK;AACzE,MAAI,iBAAiB,OAAO,KAAK,YAAY,EACxC,OAAO,SAAU,KAAK;AAAE,WAAO,IAAI,WAAW,OAAO;AAAA,EAAI,CAAA,EACzD,OAAO,SAAU,OAAO,KAAK;AAC9B,QAAIZ;AACJ,WAAO,SAAS,SAAS,IAAI,KAAK,IAAIA,MAAK,IAAIA,IAAG,GAAG,IAAI,aAAa,GAAG,GAAGA,IAAI;AAAA,EACnF,GAAE,EAAE;AACL,MAAI,mBAAmB,MAAM,KAAK,aAAa,gBAAgB,QAAQ,OAAO,SAAS,SAAS,GAAG,YAAY,QAAQ,OAAO,SAAS,KAAK;AAC5I,SAAQZ,sBAAI,OAAO,SAAS,EAAE,WAAW,WAAW,KAAK,GAAG,GAAG,OAAc,KAAK,UAAU,KAAK,IAAI,UAAU,IAAI,OAAO,aAAa,OAAO,OAAO,aAAa,OAAO,MAAM,aAAa,QAAQ,gBAAgB,EAAE,UAAUA,kBAAG,IAAC,iBAAiB,EAAE,UAAU,WAAW,cAAc,IAAI,SAAU,OAAO,GAAG;AAAE,WAAQA,kBAAG,IAAC,OAAO,EAAE,cAAc,GAAG,cAAc,SAAS,CAAC;AAAA,EAAG,CAAE,EAAG,CAAA,EAAG,CAAA,CAAC;AAClY;AAGA,SAAS,aAAa,OAAO;AACzB,MAAI,WAAW,MAAM,UAAU,eAAe,OAAO,OAAO,CAAC,UAAU,CAAC;AACxE,SAAQA,kBAAG,IAAC,mBAAmB,EAAE,cAA4B,UAAUA,kBAAAA,IAAI,oBAAoB,EAAE,UAAUA,kBAAG,IAAC,sBAAsB,EAAE,cAA4B,UAAUA,kBAAAA,IAAI,wBAAwB,EAAE,cAA4B,UAAUA,kBAAAA,IAAI,qBAAqB,EAAE,cAA4B,UAAUA,sBAAI,mBAAmB,EAAE,UAAUA,kBAAG,IAAC,eAAe,EAAE,SAAkB,CAAE,EAAG,CAAA,EAAG,CAAA,GAAG,EAAC,CAAE,EAAG,CAAA,GAAG;AACpZ;AAyFA,SAAS,UAAU,OAAO;AACtB,SAAQA,kBAAG,IAAC,cAAc,SAAS,CAAE,GAAE,OAAO,EAAE,UAAUA,kBAAG,IAAC,MAAM,EAAE,cAAc,MAAO,CAAA,EAAG,CAAA,CAAC;AACnG;AClnEA,SAAS,SAAS;AAAA,EAChB;AAAA,EACA;AAAA,EACA,kBAAkB;AAAA,EAClB,GAAG;AACL,GAAkB;AAEd,SAAAA,kCAAA;AAAA,IAAC;AAAA,IAAA;AAAA,MACC;AAAA,MACA,WAAW,GAAG,OAAO,SAAS;AAAA,MAC9B,YAAY;AAAA,QACV,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,SAAS;AAAA,QACT,eAAe;AAAA,QACf,KAAK;AAAA,QACL,YAAY;AAAA,UACV,eAAe,EAAE,SAAS,WAAW;AAAA,UACrC;AAAA,QACF;AAAA,QACA,qBAAqB;AAAA,QACrB,iBAAiB;AAAA,QACjB,OAAO;AAAA,QACP,UAAU;AAAA,QACV,WACE;AAAA,QACF,KAAK;AAAA,QACL,MAAM;AAAA,QACN,KAAK;AAAA,UACH,eAAe,EAAE,SAAS,SAAS;AAAA,UACnC;AAAA,QACF;AAAA,QACA,eAAe;AAAA,QACf,cACE;AAAA,QACF,WAAW;AAAA,QACX,aACE;AAAA,QACF,cAAc;AAAA,QACd,kBACE;AAAA,QACF,YAAY;AAAA,QACZ,GAAG;AAAA,MACL;AAAA,MACA,YAAY;AAAA,QACV,UAAU,CAAC,EAAE,GAAG8B,OAAY,MAAA9B,kCAAA,IAAC,aAAY,EAAA,WAAU,WAAU;AAAA,QAC7D,WAAW,CAAC,EAAE,GAAG8B,OAAY,MAAA9B,kCAAA,IAAC,cAAa,EAAA,WAAU,UAAU,CAAA;AAAA,MACjE;AAAA,MACC,GAAG;AAAA,IAAA;AAAA,EACN;AAEJ;AACA,SAAS,cAAc;", "x_google_ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22]}