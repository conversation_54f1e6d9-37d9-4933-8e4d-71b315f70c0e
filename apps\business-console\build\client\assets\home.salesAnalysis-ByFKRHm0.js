import { r as reactExports, j as jsxRuntimeExports } from "./jsx-runtime-bB2y7OuD.js";
import { B as Button } from "./button-ByAXMyvk.js";
import { C as Calendar, a as Calendar$1 } from "./calendar-_8-DqkPN.js";
import { P as Popover, a as PopoverTrigger, b as PopoverContent } from "./popover-CD2vRFIm.js";
import { R as ResponsivePagination } from "./responsivePagination-D-iSBEkA.js";
import { R as ResponsiveTable } from "./responsiveTable-CMOWL3Wl.js";
import { c as cn } from "./utils-GkgzjW3c.js";
import { T as Tooltip, p as Legend, R as ResponsiveContainer, a as Bar, B as BarChart, C as CartesianGrid, X as XAxis, Y as YAxis } from "./BarChart-DYWu-Gnb.js";
import { C as Card, b as <PERSON><PERSON>eader, c as <PERSON><PERSON>it<PERSON>, a as CardContent } from "./card-BJQMSLe_.js";
import { f as format, i as isValid } from "./format-82yT_5--.js";
import { D as Dialog, a as DialogContent, c as DialogHeader, b as DialogTitle, e as DialogFooter } from "./dialog-BqKosxNq.js";
import { I as Input } from "./input-3v87qohQ.js";
import { a as useFetcher, u as useLoaderData, b as useSearchParams } from "./components-D7UvGag_.js";
import { S as Select, a as SelectTrigger, b as SelectValue, c as SelectContent, d as SelectItem } from "./select-BFGSXKcr.js";
import { T as TooltipProvider, a as Tooltip$1, b as TooltipTrigger, c as TooltipContent } from "./tooltip-CmSNYR5K.js";
import { a as useNavigate } from "./index-DhHTcibu.js";
import { C as CircleX } from "./circle-x-CT1OEq17.js";
import { S as Store } from "./store-CHb1Nw9Z.js";
import { S as Search } from "./search-DzDJ71yc.js";
import { c as createLucideIcon } from "./createLucideIcon-uwkRm45G.js";
import "./index-z_byfFrQ.js";
import "./index-ImHKLo0a.js";
import "./addMonths-Dj4hq91A.js";
import "./isSameDay-BQMn9z7h.js";
import "./addDays-CyH8qBoF.js";
import "./chevron-right-B-tR7Kir.js";
import "./chevron-left-CLqBlTg1.js";
import "./index-D7VH9Fc8.js";
import "./index-BXzPK7u0.js";
import "./index-CzZ6EYkN.js";
import "./index-Vp2vNLNM.js";
import "./index-CEHS9zzk.js";
import "./index-DVTNuYOr.js";
import "./index-DscYByPT.js";
import "./index-CkL5tk39.js";
import "./index-QLGF6kQx.js";
import "./index-dIGjFc0I.js";
import "./index-DdafHWkt.js";
import "./x-CCG_WJDF.js";
import "./index-IXOTxK3N.js";
import "./index-qCtcpOcW.js";
import "./index-BTdCMChR.js";
import "./index-CpKiYcZd.js";
import "./index-C88PRvfd.js";
import "./chevron-down-pCP5jmjX.js";
import "./check-_dbWxIzT.js";
/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */
const PhoneCall = createLucideIcon("PhoneCall", [
  [
    "path",
    {
      d: "M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",
      key: "foiqr5"
    }
  ],
  ["path", { d: "M14.05 2a9 9 0 0 1 8 7.94", key: "vmijpz" }],
  ["path", { d: "M14.05 6A5 5 0 0 1 18 10", key: "13nbpp" }]
]);
const THEMES = { light: "", dark: ".dark" };
const ChartContext = reactExports.createContext(null);
function useChart() {
  const context = reactExports.useContext(ChartContext);
  if (!context) {
    throw new Error("useChart must be used within a <ChartContainer />");
  }
  return context;
}
const ChartContainer = reactExports.forwardRef(({ id, className, children, config, ...props }, ref) => {
  const uniqueId = reactExports.useId();
  const chartId = `chart-${id || uniqueId.replace(/:/g, "")}`;
  return /* @__PURE__ */ jsxRuntimeExports.jsx(ChartContext.Provider, { value: { config }, children: /* @__PURE__ */ jsxRuntimeExports.jsxs(
    "div",
    {
      "data-chart": chartId,
      ref,
      className: cn(
        "flex aspect-video justify-center text-xs [&_.recharts-cartesian-axis-tick_text]:fill-muted-foreground [&_.recharts-cartesian-grid_line[stroke='#ccc']]:stroke-border/50 [&_.recharts-curve.recharts-tooltip-cursor]:stroke-border [&_.recharts-dot[stroke='#fff']]:stroke-transparent [&_.recharts-layer]:outline-none [&_.recharts-polar-grid_[stroke='#ccc']]:stroke-border [&_.recharts-radial-bar-background-sector]:fill-muted [&_.recharts-rectangle.recharts-tooltip-cursor]:fill-muted [&_.recharts-reference-line_[stroke='#ccc']]:stroke-border [&_.recharts-sector[stroke='#fff']]:stroke-transparent [&_.recharts-sector]:outline-none [&_.recharts-surface]:outline-none",
        className
      ),
      ...props,
      children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx(ChartStyle, { id: chartId, config }),
        /* @__PURE__ */ jsxRuntimeExports.jsx(ResponsiveContainer, { children })
      ]
    }
  ) });
});
ChartContainer.displayName = "Chart";
const ChartStyle = ({ id, config }) => {
  const colorConfig = Object.entries(config).filter(
    ([_, config2]) => config2.theme || config2.color
  );
  if (!colorConfig.length) {
    return null;
  }
  return /* @__PURE__ */ jsxRuntimeExports.jsx(
    "style",
    {
      dangerouslySetInnerHTML: {
        __html: Object.entries(THEMES).map(
          ([theme, prefix]) => `
${prefix} [data-chart=${id}] {
${colorConfig.map(([key, itemConfig]) => {
            var _a;
            const color = ((_a = itemConfig.theme) == null ? void 0 : _a[theme]) || itemConfig.color;
            return color ? `  --color-${key}: ${color};` : null;
          }).join("\n")}
}
`
        ).join("\n")
      }
    }
  );
};
const ChartTooltip = Tooltip;
const ChartTooltipContent = reactExports.forwardRef(
  ({
    active,
    payload,
    className,
    indicator = "dot",
    hideLabel = false,
    hideIndicator = false,
    label,
    labelFormatter,
    labelClassName,
    formatter,
    color,
    nameKey,
    labelKey
  }, ref) => {
    const { config } = useChart();
    const tooltipLabel = reactExports.useMemo(() => {
      var _a;
      if (hideLabel || !(payload == null ? void 0 : payload.length)) {
        return null;
      }
      const [item] = payload;
      const key = `${labelKey || item.dataKey || item.name || "value"}`;
      const itemConfig = getPayloadConfigFromPayload(config, item, key);
      const value = !labelKey && typeof label === "string" ? ((_a = config[label]) == null ? void 0 : _a.label) || label : itemConfig == null ? void 0 : itemConfig.label;
      if (labelFormatter) {
        return /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: cn("font-medium", labelClassName), children: labelFormatter(value, payload) });
      }
      if (!value) {
        return null;
      }
      return /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: cn("font-medium", labelClassName), children: value });
    }, [
      label,
      labelFormatter,
      payload,
      hideLabel,
      labelClassName,
      config,
      labelKey
    ]);
    if (!active || !(payload == null ? void 0 : payload.length)) {
      return null;
    }
    const nestLabel = payload.length === 1 && indicator !== "dot";
    return /* @__PURE__ */ jsxRuntimeExports.jsxs(
      "div",
      {
        ref,
        className: cn(
          "grid min-w-[8rem] items-start gap-1.5 rounded-lg border border-border/50 bg-background px-2.5 py-1.5 text-xs shadow-xl",
          className
        ),
        children: [
          !nestLabel ? tooltipLabel : null,
          /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "grid gap-1.5", children: payload.map((item, index) => {
            const key = `${nameKey || item.name || item.dataKey || "value"}`;
            const itemConfig = getPayloadConfigFromPayload(config, item, key);
            const indicatorColor = color || item.payload.fill || item.color;
            return /* @__PURE__ */ jsxRuntimeExports.jsx(
              "div",
              {
                className: cn(
                  "flex w-full flex-wrap items-stretch gap-2 [&>svg]:h-2.5 [&>svg]:w-2.5 [&>svg]:text-muted-foreground",
                  indicator === "dot" && "items-center"
                ),
                children: formatter && (item == null ? void 0 : item.value) !== void 0 && item.name ? formatter(item.value, item.name, item, index, item.payload) : /* @__PURE__ */ jsxRuntimeExports.jsxs(jsxRuntimeExports.Fragment, { children: [
                  (itemConfig == null ? void 0 : itemConfig.icon) ? /* @__PURE__ */ jsxRuntimeExports.jsx(itemConfig.icon, {}) : !hideIndicator && /* @__PURE__ */ jsxRuntimeExports.jsx(
                    "div",
                    {
                      className: cn(
                        "shrink-0 rounded-[2px] border-[--color-border] bg-[--color-bg]",
                        {
                          "h-2.5 w-2.5": indicator === "dot",
                          "w-1": indicator === "line",
                          "w-0 border-[1.5px] border-dashed bg-transparent": indicator === "dashed",
                          "my-0.5": nestLabel && indicator === "dashed"
                        }
                      ),
                      style: {
                        "--color-bg": indicatorColor,
                        "--color-border": indicatorColor
                      }
                    }
                  ),
                  /* @__PURE__ */ jsxRuntimeExports.jsxs(
                    "div",
                    {
                      className: cn(
                        "flex flex-1 justify-between leading-none",
                        nestLabel ? "items-end" : "items-center"
                      ),
                      children: [
                        /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "grid gap-1.5", children: [
                          nestLabel ? tooltipLabel : null,
                          /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "text-muted-foreground", children: (itemConfig == null ? void 0 : itemConfig.label) || item.name })
                        ] }),
                        item.value && /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "font-mono font-medium tabular-nums text-foreground", children: item.value.toLocaleString() })
                      ]
                    }
                  )
                ] })
              },
              item.dataKey
            );
          }) })
        ]
      }
    );
  }
);
ChartTooltipContent.displayName = "ChartTooltip";
const ChartLegend = Legend;
const ChartLegendContent = reactExports.forwardRef(
  ({ className, hideIcon = false, payload, verticalAlign = "bottom", nameKey }, ref) => {
    const { config } = useChart();
    if (!(payload == null ? void 0 : payload.length)) {
      return null;
    }
    return /* @__PURE__ */ jsxRuntimeExports.jsx(
      "div",
      {
        ref,
        className: cn(
          "flex items-center justify-center gap-4",
          verticalAlign === "top" ? "pb-3" : "pt-3",
          className
        ),
        children: payload.map((item) => {
          const key = `${nameKey || item.dataKey || "value"}`;
          const itemConfig = getPayloadConfigFromPayload(config, item, key);
          return /* @__PURE__ */ jsxRuntimeExports.jsxs(
            "div",
            {
              className: cn(
                "flex items-center gap-1.5 [&>svg]:h-3 [&>svg]:w-3 [&>svg]:text-muted-foreground"
              ),
              children: [
                (itemConfig == null ? void 0 : itemConfig.icon) && !hideIcon ? /* @__PURE__ */ jsxRuntimeExports.jsx(itemConfig.icon, {}) : /* @__PURE__ */ jsxRuntimeExports.jsx(
                  "div",
                  {
                    className: "h-2 w-2 shrink-0 rounded-[2px]",
                    style: {
                      backgroundColor: item.color
                    }
                  }
                ),
                itemConfig == null ? void 0 : itemConfig.label
              ]
            },
            item.value
          );
        })
      }
    );
  }
);
ChartLegendContent.displayName = "ChartLegend";
function getPayloadConfigFromPayload(config, payload, key) {
  if (typeof payload !== "object" || payload === null) {
    return void 0;
  }
  const payloadPayload = "payload" in payload && typeof payload.payload === "object" && payload.payload !== null ? payload.payload : void 0;
  let configLabelKey = key;
  if (key in payload && typeof payload[key] === "string") {
    configLabelKey = payload[key];
  } else if (payloadPayload && key in payloadPayload && typeof payloadPayload[key] === "string") {
    configLabelKey = payloadPayload[key];
  }
  return configLabelKey in config ? config[configLabelKey] : config[key];
}
const chartConfig = {
  desktop: {
    label: "Desktop",
    color: "hsl(120, 90%, 50%)"
  },
  mobile: {
    label: "Mobile",
    color: "hsl(120, 90%, 50%)"
  }
};
function SalesAnalysisDashBoard({ salesData }) {
  var _a, _b;
  console.log(salesData.totalSales, "676776767676776676767867868");
  console.log(" Sales Data:", salesData);
  console.log("Total Sales Data:", salesData == null ? void 0 : salesData.totalSales);
  const uniqueDates = Array.from(
    new Set(((_b = (_a = salesData == null ? void 0 : salesData.totalSales) == null ? void 0 : _a.cols) == null ? void 0 : _b.map((col) => col.deliveryDate)) || [])
  ).sort();
  let barComponents;
  const chartData = uniqueDates.map((date) => {
    var _a2, _b2;
    const totalSalesQty = (_b2 = (_a2 = salesData == null ? void 0 : salesData.totalSales) == null ? void 0 : _a2.cols) == null ? void 0 : _b2.reduce((acc, col) => {
      return col.deliveryDate === date ? acc + (col.salesQty || 0) : acc;
    }, 0);
    return { date, salesQty: totalSalesQty.toFixed(2) };
  });
  barComponents = /* @__PURE__ */ jsxRuntimeExports.jsx(jsxRuntimeExports.Fragment, { children: /* @__PURE__ */ jsxRuntimeExports.jsx(
    Bar,
    {
      dataKey: "salesQty",
      stackId: "a",
      fill: "hsl(210, 90%, 50%)",
      radius: [0, 0, 0, 0],
      label: { position: "top", fill: "#000" },
      barSize: chartData.length === 1 ? 30 : void 0
    }
  ) });
  return /* @__PURE__ */ jsxRuntimeExports.jsxs(Card, { className: "mt-8", children: [
    /* @__PURE__ */ jsxRuntimeExports.jsx(CardHeader, { children: /* @__PURE__ */ jsxRuntimeExports.jsx(CardTitle, { children: "Sales Analysis Chart" }) }),
    /* @__PURE__ */ jsxRuntimeExports.jsx(CardContent, { children: /* @__PURE__ */ jsxRuntimeExports.jsx(ChartContainer, { config: chartConfig, children: /* @__PURE__ */ jsxRuntimeExports.jsxs(BarChart, { accessibilityLayer: true, data: chartData, children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx(CartesianGrid, { vertical: false }),
      /* @__PURE__ */ jsxRuntimeExports.jsx(
        XAxis,
        {
          dataKey: "date",
          tickLine: false,
          tickMargin: 10,
          axisLine: false,
          tickFormatter: (value) => format(new Date(value), "EEE dd/MM")
        }
      ),
      /* @__PURE__ */ jsxRuntimeExports.jsx(YAxis, {}),
      /* @__PURE__ */ jsxRuntimeExports.jsx(ChartTooltip, { content: /* @__PURE__ */ jsxRuntimeExports.jsx(ChartTooltipContent, { hideLabel: true }) }),
      /* @__PURE__ */ jsxRuntimeExports.jsx(ChartLegend, { content: /* @__PURE__ */ jsxRuntimeExports.jsx(ChartLegendContent, {}) }),
      barComponents,
      /* @__PURE__ */ jsxRuntimeExports.jsx(
        ChartTooltip,
        {
          cursor: false,
          content: /* @__PURE__ */ jsxRuntimeExports.jsx(ChartTooltipContent, { indicator: "dashed" })
        }
      )
    ] }) }) })
  ] });
}
function SalesDynamicSearchFilters({
  title,
  isOpen,
  onClose,
  onSelect,
  itemList,
  type
}) {
  const [search, setSearch] = reactExports.useState("");
  const [selectedSeller, setSelectedSeller] = reactExports.useState(null);
  const [items, setItems] = reactExports.useState(itemList);
  const [page, setPage] = reactExports.useState(0);
  const [totalPages, setTotalPages] = reactExports.useState(100);
  const itemFetcher = useFetcher();
  const handleGetItems = () => {
    if (search.length < 3 && page === 0) return;
    const formData = new FormData();
    formData.append("type", type);
    formData.append("intent", type);
    formData.append("pageNo", page.toString());
    formData.append("size", "10");
    formData.append("matchBy", search);
    itemFetcher.submit(formData, {
      method: "POST",
      encType: "application/x-www-form-urlencoded"
    });
  };
  reactExports.useEffect(() => {
    if (search.length >= 3 || page > 0) {
      handleGetItems();
    }
  }, [search, page]);
  reactExports.useEffect(() => {
    var _a;
    if (!search) {
      setItems(itemList);
    } else if ((_a = itemFetcher == null ? void 0 : itemFetcher.data) == null ? void 0 : _a.selectedData) {
      setItems(itemFetcher.data.selectedData);
    }
  }, [search, itemFetcher.data, itemList]);
  const handleSearchChange = (e) => {
    setSearch(e.target.value);
    setPage(0);
    if (e.target.value.length >= 3) {
      handleGetItems();
    }
  };
  console.log(items, "45454544455");
  const nextPage = () => {
    setPage((prev) => prev < totalPages ? prev + 1 : prev);
    handleGetItems();
  };
  const prevPage = () => {
    setPage((prev) => prev > 1 ? prev - 1 : prev);
    handleGetItems();
  };
  const handleAddFilter = () => {
    if (selectedSeller) {
      onSelect(selectedSeller);
    }
    onClose();
  };
  return /* @__PURE__ */ jsxRuntimeExports.jsx(Dialog, { open: isOpen, onOpenChange: onClose, children: /* @__PURE__ */ jsxRuntimeExports.jsxs(DialogContent, { className: "max-w-md p-6 bg-white rounded-lg shadow-lg", children: [
    /* @__PURE__ */ jsxRuntimeExports.jsx(DialogHeader, { children: /* @__PURE__ */ jsxRuntimeExports.jsx(DialogTitle, { className: "text-lg font-semibold", children: title }) }),
    /* @__PURE__ */ jsxRuntimeExports.jsx(
      Input,
      {
        placeholder: "Search...",
        value: search,
        onChange: handleSearchChange,
        className: "w-full mt-2 border rounded-md p-2"
      }
    ),
    itemFetcher.state !== "idle" ? /* @__PURE__ */ jsxRuntimeExports.jsx("p", { className: "text-center text-gray-500 mt-4", children: "Loading..." }) : /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "max-h-48 overflow-y-auto mt-4 space-y-2", children: items.length > 0 ? items.map((item) => /* @__PURE__ */ jsxRuntimeExports.jsxs("label", { className: "flex items-center space-x-2 cursor-pointer p-2 bg-gray-100 rounded-md hover:bg-gray-200", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx(
        "input",
        {
          type: "radio",
          name: "singleSelect",
          checked: (selectedSeller == null ? void 0 : selectedSeller.id) === item.id,
          onChange: () => setSelectedSeller(item),
          className: "form-radio h-5 w-5 text-blue-500"
        }
      ),
      /* @__PURE__ */ jsxRuntimeExports.jsx("span", { children: item.name })
    ] }, item.id)) : /* @__PURE__ */ jsxRuntimeExports.jsx("p", { className: "text-gray-500 text-center", children: "No results found." }) }),
    /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex justify-between items-center mt-4", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx(Button, { variant: "outline", onClick: prevPage, disabled: page === 1, children: "Prev" }),
      /* @__PURE__ */ jsxRuntimeExports.jsxs("span", { className: "text-gray-600", children: [
        "Page ",
        page,
        " of ",
        totalPages
      ] }),
      /* @__PURE__ */ jsxRuntimeExports.jsx(Button, { variant: "outline", onClick: nextPage, disabled: page === totalPages, children: "Next" })
    ] }),
    /* @__PURE__ */ jsxRuntimeExports.jsxs(DialogFooter, { className: "flex justify-end mt-4", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx(Button, { variant: "outline", onClick: onClose, children: "Close" }),
      /* @__PURE__ */ jsxRuntimeExports.jsx(Button, { className: "ml-2", onClick: handleAddFilter, disabled: !selectedSeller, children: "+ Add Filter" })
    ] })
  ] }) });
}
function SalesSearchFilters({ title, isOpen, onClose, items, onSelect }) {
  const [search, setSearch] = reactExports.useState("");
  const filteredItems = items == null ? void 0 : items.filter(
    (item) => {
      var _a;
      return (_a = item == null ? void 0 : item.name) == null ? void 0 : _a.toLowerCase().includes(search.toLowerCase());
    }
  );
  const [selectedSeller, setSelectedSeller] = reactExports.useState(null);
  const handleAddFilter = () => {
    if (selectedSeller) {
      onSelect(selectedSeller);
    }
    onClose();
  };
  return /* @__PURE__ */ jsxRuntimeExports.jsx(Dialog, { open: isOpen, onOpenChange: onClose, children: /* @__PURE__ */ jsxRuntimeExports.jsxs(DialogContent, { className: "max-w-md p-6 bg-white rounded-lg shadow-lg", children: [
    /* @__PURE__ */ jsxRuntimeExports.jsx(DialogHeader, { children: /* @__PURE__ */ jsxRuntimeExports.jsx(DialogTitle, { className: "text-lg font-semibold", children: title }) }),
    /* @__PURE__ */ jsxRuntimeExports.jsx(
      Input,
      {
        placeholder: "Search...",
        value: search,
        onChange: (e) => setSearch(e.target.value),
        className: "w-full mt-2 border rounded-md p-2"
      }
    ),
    /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "max-h-48 overflow-y-auto mt-4 space-y-2", children: filteredItems.length > 0 ? filteredItems == null ? void 0 : filteredItems.map((item) => /* @__PURE__ */ jsxRuntimeExports.jsxs(
      "label",
      {
        className: "flex items-center space-x-2 cursor-pointer p-2 bg-gray-100 rounded-md hover:bg-gray-200",
        children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx(
            "input",
            {
              type: "radio",
              name: "singleSelect",
              checked: (selectedSeller == null ? void 0 : selectedSeller.id) === item.id,
              onChange: () => setSelectedSeller({ id: item.id, name: item.name }),
              className: "form-radio h-5 w-5 text-blue-500"
            }
          ),
          /* @__PURE__ */ jsxRuntimeExports.jsx("span", { children: item.name })
        ]
      },
      item.id
    )) : /* @__PURE__ */ jsxRuntimeExports.jsx("p", { className: "text-gray-500 text-center", children: "No results found." }) }),
    /* @__PURE__ */ jsxRuntimeExports.jsxs(DialogFooter, { className: "flex justify-end mt-4", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx(Button, { variant: "outline", onClick: onClose, children: "Close" }),
      /* @__PURE__ */ jsxRuntimeExports.jsx(Button, { className: "ml-2", onClick: handleAddFilter, children: "+ Add Filter" })
    ] })
  ] }) });
}
function SalesAnalysis() {
  var _a, _b, _c, _d, _e;
  const {
    salesAnalysis,
    selectedDate,
    dataTypeWise,
    selectedDays,
    selectedSellerId,
    selectedAgentId,
    selectedBuyerId,
    selectedLocalityId,
    pageNo,
    pageSize
  } = useLoaderData();
  const [searchParams] = useSearchParams();
  const searchParamsDataType = reactExports.useMemo(() => searchParams.get("dataType") ?? dataTypeWise, [searchParams, dataTypeWise]);
  const searchParamsDays = reactExports.useMemo(() => searchParams.get("days") ?? (selectedDays == null ? void 0 : selectedDays.toString()), [searchParams, selectedDays]);
  const searchParamsDate = reactExports.useMemo(() => searchParams.get("date") ?? selectedDate, [searchParams, selectedDate]);
  const searchParamsDDate = searchParams.get("dDate");
  const searchParamsSellerId = Number(searchParams.get("sellerId") ?? selectedSellerId);
  const searchParamsAgentId = Number(searchParams.get("agentId") ?? selectedAgentId);
  const searchParamsBuyerId = Number(searchParams.get("buyerId") ?? selectedBuyerId);
  const searchParamsAreaId = Number(searchParams.get("areaId") ?? selectedLocalityId);
  Number(searchParams.get("pageNo") ?? pageNo);
  Number(searchParams.get("pageSize") ?? pageSize);
  const selectedSellerName = searchParams.get("sellerName") || "";
  const selectedAgentName = searchParams.get("agentName") || "";
  const selectedBuyerName = searchParams.get("buyerName") || "";
  const selectedAreaName = searchParams.get("areaName") || "";
  const [dataType, setDataType] = reactExports.useState(searchParamsDataType);
  reactExports.useEffect(() => {
    if (dataType !== searchParamsDataType) setDataType(searchParamsDataType);
    if (days !== searchParamsDays) setDays(searchParamsDays);
    if (date.toISOString().split("T")[0] !== searchParamsDate) {
      setDate(new Date(searchParamsDate));
    }
    if (searchParamsDDate) {
      const formattedDDate = new Date(searchParamsDDate).toISOString().split("T")[0];
      if (formattedDDate !== (dDate == null ? void 0 : dDate.toISOString().split("T")[0])) {
        setDDate(new Date(searchParamsDDate));
      }
    }
    if (selectedSellerData.id !== searchParamsSellerId) {
      setSelectedSellerData({
        id: searchParamsSellerId,
        name: selectedSellerName
      });
    }
    if (selectedAgentData.id !== searchParamsAgentId) {
      setSelectedAgentData({
        id: searchParamsAgentId,
        name: selectedAgentName
      });
    }
    if (selectedBuyerData.id !== searchParamsBuyerId) {
      setSelectedBuyerData({
        id: searchParamsBuyerId,
        name: selectedBuyerName
      });
    }
    if (selectedAreaData.id !== searchParamsAreaId) {
      setSelectedAreaData({
        id: searchParamsAreaId,
        name: selectedAreaName
      });
    }
  }, [searchParamsDataType, searchParamsDays, searchParamsDate, searchParamsSellerId, searchParamsAgentId, searchParamsBuyerId, searchParamsAreaId]);
  const [days, setDays] = reactExports.useState(selectedDays.toString());
  const [dashboardData, setDashboardData] = reactExports.useState(salesAnalysis || {});
  const fetcher = useFetcher();
  const navigate = useNavigate();
  const [date, setDate] = reactExports.useState(selectedDate ? new Date(selectedDate) : new Date((/* @__PURE__ */ new Date()).setDate((/* @__PURE__ */ new Date()).getDate() + 1)));
  const [dDate, setDDate] = reactExports.useState(searchParamsDDate ? new Date(searchParamsDDate) : void 0);
  const pageTotalSize = 50;
  const handleSubmit = () => {
    const formattedDate = new Date(date);
    const dformattedDate = dDate ? new Date(dDate) : void 0;
    setDashboardData({
      date: "",
      dataType: "",
      days: 0,
      rows: [],
      totalSales: []
    });
    const queryString = `?date=${format(formattedDate, "yyyy-MM-dd")}&dataType=${dataType}&days=${days}&sellerId=${selectedSellerData.id}&agentId=${selectedAgentData.id}&buyerId=${selectedBuyerData.id}&areaId=${selectedAreaData.id}&matchBy=${keyword}&pageNo=0&pageSize=${pageTotalSize}&sellerName=${selectedSellerData.name}&agentName=${selectedAgentData.name}&buyerName=${selectedBuyerData.name}&areaName=${selectedAreaData.name}`;
    const finalUrl = dformattedDate ? `${queryString}&dDate=${format(dformattedDate, "yyyy-MM-dd")}` : queryString;
    navigate(finalUrl);
  };
  reactExports.useEffect(() => {
    if (salesAnalysis) {
      setDashboardData(salesAnalysis);
    }
  }, [salesAnalysis]);
  console.log(salesAnalysis, "88888888");
  const groupedData = (_a = dashboardData == null ? void 0 : dashboardData.rows) == null ? void 0 : _a.reduce((acc, row) => {
    var _a2;
    if (!acc[row.name]) {
      acc[row.name] = {
        id: (_a2 = row == null ? void 0 : row.id) == null ? void 0 : _a2.toString(),
        sales: {},
        lastOrderDate: row == null ? void 0 : row.lastOrderedDate,
        agentName: row == null ? void 0 : row.agentName,
        buyerMobile: ((row == null ? void 0 : row.buyerMobileNumber) ?? "").toString()
      };
    }
    row.cols.forEach((col) => {
      acc[row == null ? void 0 : row.name].sales[col == null ? void 0 : col.deliveryDate] = {
        salesQty: col == null ? void 0 : col.salesQty,
        salesAmount: col == null ? void 0 : col.salesAmount,
        visit: col == null ? void 0 : col.visits,
        searchCount: col == null ? void 0 : col.searchCount,
        calls: col == null ? void 0 : col.calls
      };
    });
    return acc;
  }, {});
  const uniqueDates = Array.from(new Set((_b = dashboardData == null ? void 0 : dashboardData.rows) == null ? void 0 : _b.flatMap((row) => row.cols.map((col) => col.deliveryDate))));
  const headers = ["Name", ...uniqueDates.map((date2) => `${format(date2, "EEE dd/MM")}`)];
  const formattedTableData = Object.entries(groupedData || {}).map(([name, group]) => {
    const {
      id,
      sales = {},
      lastOrderDate,
      agentName,
      buyerMobile
    } = group || {};
    return {
      id,
      name,
      lastOrderDate,
      agentName,
      buyerMobile,
      // Add lastOrderDate
      ...uniqueDates.reduce((acc, date2) => {
        var _a2, _b2, _c2, _d2, _e2;
        acc[`${date2}_qty`] = ((_a2 = sales == null ? void 0 : sales[date2]) == null ? void 0 : _a2.salesQty) ? String(sales[date2].salesQty) : "-";
        acc[`${date2}_visit`] = ((_b2 = sales == null ? void 0 : sales[date2]) == null ? void 0 : _b2.visit) > 0;
        acc[`${date2}_search`] = ((_c2 = sales == null ? void 0 : sales[date2]) == null ? void 0 : _c2.searchCount) > 0;
        acc[`${date2}_call`] = ((_d2 = sales == null ? void 0 : sales[date2]) == null ? void 0 : _d2.calls) > 0;
        acc[`${date2}_qtycheck`] = ((_e2 = sales == null ? void 0 : sales[date2]) == null ? void 0 : _e2.salesQty) > 0;
        return acc;
      }, {})
    };
  });
  const [showSearchForm, setShowSearchForm] = reactExports.useState(false);
  const [showSearchAgentForm, setShowSearchAgentForm] = reactExports.useState(false);
  const [showSearchBuyerForm, setShowSearchBuyerForm] = reactExports.useState(false);
  const [showSearchAreaForm, setShowSearchAreaForm] = reactExports.useState(false);
  const [selectedSellerData, setSelectedSellerData] = reactExports.useState({
    id: searchParamsSellerId,
    name: selectedSellerName
  });
  const [selectedAgentData, setSelectedAgentData] = reactExports.useState({
    id: searchParamsAgentId,
    name: selectedAgentName
  });
  const [selectedBuyerData, setSelectedBuyerData] = reactExports.useState({
    id: searchParamsBuyerId,
    name: selectedBuyerName
  });
  const [selectedAreaData, setSelectedAreaData] = reactExports.useState({
    id: searchParamsAreaId,
    name: selectedAreaName
  });
  const [pageNum, setPageNum] = reactExports.useState(0);
  const handleAddFilers = async (value) => {
    if (value === "seller") {
      const formData = new FormData();
      formData.append("action", "sellerWise");
      sellerFetcher.submit(formData, {
        method: "POST"
      });
      await setShowSearchForm(true);
    } else if (value === "agent") {
      setShowSearchAgentForm(true);
      const formData = new FormData();
      formData.append("intent", value);
      formData.append("type", "agent");
      formData.append("pageNo", "0");
      formData.append("size", "100");
      agentFetcher.submit(formData, {
        method: "POST"
      });
    } else if (value === "buyer") {
      setShowSearchBuyerForm(true);
      const formData = new FormData();
      formData.append("intent", value);
      formData.append("type", "buyer");
      formData.append("pageNo", "0");
      formData.append("size", "100");
      buyerFetcher.submit(formData, {
        method: "POST"
      });
    } else if (value === "locality") {
      setShowSearchAreaForm(true);
      const formData = new FormData();
      formData.append("intent", value);
      formData.append("type", "locality");
      formData.append("pageNo", "0");
      formData.append("size", "100");
      localityFetcher.submit(formData, {
        method: "POST"
      });
    }
  };
  const handleSelectedSeller = (seller) => {
    setSelectedSellerData(seller);
    const formattedDate = new Date(date);
    const dformattedDate = dDate ? new Date(dDate) : "";
    navigate(`?date=${format(formattedDate, "yyyy-MM-dd")}&dataType=${dataType}&days=${days}&sellerId=${seller == null ? void 0 : seller.id}&agentId=${selectedAgentData == null ? void 0 : selectedAgentData.id}&buyerId=${selectedBuyerData == null ? void 0 : selectedBuyerData.id}&areaId=${selectedAreaData.id}&matchBy=${keyword}&pageNo=${0}&pageSize=${pageTotalSize}&sellerName=${seller == null ? void 0 : seller.name}&agentName=${selectedAgentData.name}&buyerName=${selectedBuyerData.name}&areaName=${selectedAreaData.name}${isValid(dformattedDate) ? `&dDate=${format(dformattedDate, "yyyy-MM-dd")}` : ""}`);
  };
  const handleSelectedAgent = (agent) => {
    setSelectedAgentData(agent);
    const formattedDate = new Date(date);
    const dformattedDate = dDate ? new Date(dDate) : "";
    navigate(`?date=${format(formattedDate, "yyyy-MM-dd")}&dataType=${dataType}&days=${days}&sellerId=${selectedSellerData == null ? void 0 : selectedSellerData.id}&agentId=${agent == null ? void 0 : agent.id}&buyerId=${selectedBuyerData == null ? void 0 : selectedBuyerData.id}&areaId=${selectedAreaData == null ? void 0 : selectedAreaData.id}&matchBy=${keyword}&pageNo=${0}&pageSize=${pageTotalSize}&sellerName=${selectedSellerData.name}&agentName=${agent == null ? void 0 : agent.name}&buyerName=${selectedBuyerData.name}&areaName=${selectedAreaData.name}${isValid(dformattedDate) ? `&dDate=${format(dformattedDate, "yyyy-MM-dd")}` : ""}`);
  };
  const handleSelectedBuyer = (buyer) => {
    setSelectedBuyerData(buyer);
    const formattedDate = new Date(date);
    const dformattedDate = dDate ? new Date(dDate) : "";
    navigate(`?date=${format(formattedDate, "yyyy-MM-dd")}&dataType=${dataType}&days=${days}&sellerId=${selectedSellerData == null ? void 0 : selectedSellerData.id}&agentId=${selectedAgentData == null ? void 0 : selectedAgentData.id}&buyerId=${buyer == null ? void 0 : buyer.id}&areaId=${selectedAreaData == null ? void 0 : selectedAreaData.id}&matchBy=${keyword}&pageNo=${0}&pageSize=${pageTotalSize}&sellerName=${selectedSellerData.name}&agentName=${selectedAgentData == null ? void 0 : selectedAgentData.name}&buyerName=${selectedBuyerData.name}&areaName=${selectedAreaData.name}${isValid(dformattedDate) ? `&dDate=${format(dformattedDate, "yyyy-MM-dd")}` : ""}`);
  };
  const handleSelectedArea = (locality) => {
    setSelectedAreaData(locality);
    const formattedDate = new Date(date);
    const dformattedDate = dDate ? new Date(dDate) : "";
    navigate(`?date=${format(formattedDate, "yyyy-MM-dd")}&dataType=${dataType}&days=${days}&sellerId=${selectedSellerData == null ? void 0 : selectedSellerData.id}&agentId=${selectedAgentData == null ? void 0 : selectedAgentData.id}&buyerId=${selectedBuyerData == null ? void 0 : selectedBuyerData.id}&areaId=${locality == null ? void 0 : locality.id}&matchBy=${keyword}&pageNo=${0}&pageSize=${pageTotalSize}&sellerName=${selectedSellerData.name}&agentName=${selectedAgentData.name}&buyerName=${selectedBuyerData.name}&areaName=${locality == null ? void 0 : locality.name}${isValid(dformattedDate) ? `&dDate=${format(dformattedDate, "yyyy-MM-dd")}` : ""}`);
  };
  const handleClearFilter = async (value) => {
    if (value === "seller") {
      setSelectedSellerData({
        id: null,
        name: ""
      });
      const formattedDate = new Date(date);
      const dformattedDate = dDate ? new Date(dDate) : "";
      navigate(`?date=${format(formattedDate, "yyyy-MM-dd")}&dataType=${dataType}&days=${days}&sellerId=${null}&agentId=${selectedAgentData == null ? void 0 : selectedAgentData.id}&buyerId=${selectedBuyerData == null ? void 0 : selectedBuyerData.id}&areaId=${selectedAreaData.id}&matchBy=${keyword}&pageNo=${0}&pageSize=${pageTotalSize}&sellerName=${""}&agentName=${selectedAgentData.name}&buyerName=${selectedBuyerData.name}&areaName=${selectedAreaData.name}${isValid(dformattedDate) ? `&dDate=${format(dformattedDate, "yyyy-MM-dd")}` : ""}`);
    } else if (value === "agent") {
      setSelectedAgentData({
        id: null,
        name: ""
      });
      const formattedDate = new Date(date);
      const dformattedDate = dDate ? new Date(dDate) : "";
      navigate(`?date=${format(formattedDate, "yyyy-MM-dd")}&dataType=${dataType}&days=${days}&sellerId=${selectedSellerData == null ? void 0 : selectedSellerData.id}&agentId=${null}&buyerId=${selectedBuyerData == null ? void 0 : selectedBuyerData.id}&areaId=${selectedAreaData == null ? void 0 : selectedAreaData.id}&matchBy=${keyword}&pageNo=${0}&pageSize=${pageTotalSize}&sellerName=${selectedSellerData.name}&agentName=${""}&buyerName=${selectedBuyerData.name}&areaName=${selectedAreaData.name}${isValid(dformattedDate) ? `&dDate=${format(dformattedDate, "yyyy-MM-dd")}` : ""}`);
    } else if (value === "buyer") {
      setSelectedBuyerData({
        id: null,
        name: ""
      });
      const formattedDate = new Date(date);
      const dformattedDate = dDate ? new Date(dDate) : "";
      navigate(`?date=${format(formattedDate, "yyyy-MM-dd")}&dataType=${dataType}&days=${days}&sellerId=${selectedSellerData == null ? void 0 : selectedSellerData.id}&agentId=${selectedAgentData == null ? void 0 : selectedAgentData.id}&buyerId=${null}&areaId=${selectedAreaData == null ? void 0 : selectedAreaData.id}&matchBy=${keyword}&pageNo=${0}&pageSize=${pageTotalSize}&sellerName=${selectedSellerData.name}&agentName=${selectedAgentData.name}&buyerName=${""}&areaName=${selectedAreaData.name}${isValid(dformattedDate) ? `&dDate=${format(dformattedDate, "yyyy-MM-dd")}` : ""}`);
    } else if (value === "locality") {
      setSelectedAreaData({
        id: null,
        name: ""
      });
      const formattedDate = new Date(date);
      const dformattedDate = dDate ? new Date(dDate) : "";
      navigate(`?date=${format(formattedDate, "yyyy-MM-dd")}&dataType=${dataType}&days=${days}&sellerId=${selectedSellerData == null ? void 0 : selectedSellerData.id}&agentId=${selectedAgentData == null ? void 0 : selectedAgentData.id}&buyerId=${selectedBuyerData == null ? void 0 : selectedBuyerData.id}&areaId=${null}&matchBy=${keyword}&pageNo=${0}&pageSize=${pageTotalSize}&sellerName=${selectedSellerData.name}&agentName=${selectedAgentData.name}&buyerName=${selectedBuyerData.name}&areaName=${""}&${isValid(dformattedDate) ? `&dDate=${format(dformattedDate, "yyyy-MM-dd")}` : ""}`);
    }
  };
  const localityFetcher = useFetcher();
  const [localityList, setLocalityList] = reactExports.useState([]);
  reactExports.useEffect(() => {
    var _a2;
    if ((_a2 = localityFetcher.data) == null ? void 0 : _a2.selectedData) {
      setLocalityList(localityFetcher.data.selectedData);
    }
  }, [localityFetcher.data]);
  const buyerFetcher = useFetcher();
  const [buyerList, setBuyerList] = reactExports.useState([]);
  const handlePageSizeChange = (newSize) => {
    setPageNum(Number(newSize));
    const formattedDate = new Date(date);
    const dformattedDate = dDate ? new Date(dDate) : "";
    navigate(`?date=${format(formattedDate, "yyyy-MM-dd")}&dataType=${dataType}&days=${days}&sellerId=${selectedSellerData.id}&agentId=${selectedAgentData.id}&buyerId=${selectedBuyerData.id}&areaId=${selectedAreaData.id}&matchBy=${keyword}&pageNo=${newSize}&pageSize=${pageTotalSize}&sellerName=${selectedSellerData.name}&agentName=${selectedAgentData.name}&buyerName=${selectedBuyerData.name}&areaName=${selectedAreaData.name}${isValid(dformattedDate) ? `&dDate=${format(dformattedDate, "yyyy-MM-dd")}` : ""}`);
  };
  reactExports.useEffect(() => {
    var _a2;
    if ((_a2 = buyerFetcher.data) == null ? void 0 : _a2.selectedData) {
      setBuyerList(buyerFetcher.data.selectedData);
    }
  }, [(_c = buyerFetcher.data) == null ? void 0 : _c.selectedData]);
  const agentFetcher = useFetcher();
  const [agentList, setAgentList] = reactExports.useState([]);
  reactExports.useEffect(() => {
    var _a2;
    if ((_a2 = agentFetcher.data) == null ? void 0 : _a2.selectedData) {
      setAgentList(agentFetcher.data.selectedData);
    }
  }, [(_d = agentFetcher.data) == null ? void 0 : _d.selectedData]);
  const sellerFetcher = useFetcher();
  const [sellerList, setSellerList] = reactExports.useState([]);
  const mappedSellerList = sellerList.map(({
    sellerId,
    seller
  }) => ({
    id: sellerId ?? null,
    // If sellerId is undefined, set it to null
    name: seller
  }));
  reactExports.useEffect(() => {
    var _a2;
    console.log("555555555555555");
    if ((_a2 = sellerFetcher.data) == null ? void 0 : _a2.sellerList) {
      setSellerList(sellerFetcher.data.sellerList);
      console.log(sellerFetcher.data.sellerList, "00000000000y7777");
    }
  }, [(_e = sellerFetcher.data) == null ? void 0 : _e.sellerList]);
  console.log(sellerList, "#################33");
  reactExports.useEffect(() => {
    if (fetcher.data && fetcher.state === "idle" && fetcher.data !== dashboardData) {
      setDashboardData(fetcher.data);
    }
  }, [fetcher.data, fetcher.state]);
  const [keyword, setKeyword] = reactExports.useState("");
  const [isKeywordEnabled, setIsKeywordEnabled] = reactExports.useState(false);
  const [dDateSelected, setdDateSelected] = reactExports.useState(false);
  const [open, setOpen] = reactExports.useState(false);
  const handleClickItem = (value, selectedId, name) => {
    if (value === "sellerwise") {
      setDataType("buyerwise");
      const formattedDate = new Date(date);
      const dformattedDate = dDate ? new Date(dDate) : "";
      setSelectedSellerData({
        id: selectedId,
        name
      });
      navigate(`?date=${format(formattedDate, "yyyy-MM-dd")}&dataType=buyerwise&days=${days}&sellerId=${selectedId}&agentId=${selectedAgentData.id}&buyerId=${selectedBuyerData.id}&areaId=${selectedAreaData.id}&matchBy=${keyword}&pageNo=${0}&pageSize=${pageTotalSize}&sellerName=${name}&agentName=${selectedAgentData.name}&buyerName=${selectedBuyerData.name}&areaName=${selectedAreaData.name}${isValid(dformattedDate) ? `&dDate=${format(dformattedDate, "yyyy-MM-dd")}` : ""}`);
    } else if (value === "agentwise") {
      setDataType("buyerwise");
      const formattedDate = new Date(date);
      const dformattedDate = dDate ? new Date(dDate) : "";
      setSelectedAgentData({
        id: selectedId,
        name
      });
      navigate(`?date=${format(formattedDate, "yyyy-MM-dd")}&dataType=buyerwise&days=${days}&sellerId=${selectedSellerData.id}&agentId=${selectedId}&buyerId=${selectedBuyerData.id}&areaId=${selectedAreaData.id}&matchBy=${keyword}&pageNo=${0}&pageSize=${pageTotalSize}&sellerName=${selectedSellerData.name}&agentName=${name}&buyerName=${selectedBuyerData.name}&areaName=${selectedAreaData.name}${isValid(dformattedDate) ? `&dDate=${format(dformattedDate, "yyyy-MM-dd")}` : ""}`);
    } else if (value === "localitywise") {
      setDataType("buyerwise");
      const formattedDate = new Date(date);
      const dformattedDate = dDate ? new Date(dDate) : "";
      setSelectedAreaData({
        id: selectedId,
        name
      });
      navigate(`?date=${format(formattedDate, "yyyy-MM-dd")}&dataType=buyerwise&days=${days}&sellerId=${selectedSellerData.id}&agentId=${selectedAgentData.id}&buyerId=${selectedBuyerData.id}&areaId=${selectedId}&matchBy=${keyword}&pageNo=${0}&pageSize=${pageTotalSize}&sellerName=${selectedSellerData.name}&agentName=${selectedAgentData.name}&buyerName=${selectedBuyerData.name}&areaName=${name}${isValid(dformattedDate) ? `&dDate=${format(dformattedDate, "yyyy-MM-dd")}` : ""}`);
    } else if (value === "buyerwise") {
      setDataType("itemwise");
      const formattedDate = new Date(date);
      const dformattedDate = dDate ? new Date(dDate) : "";
      setSelectedBuyerData({
        id: selectedId,
        name
      });
      navigate(`?date=${format(formattedDate, "yyyy-MM-dd")}&dataType=itemwise&days=${days}&sellerId=${selectedSellerData.id}&agentId=${selectedAgentData.id}&buyerId=${selectedId}&areaId=${selectedAreaData.id}&matchBy=${keyword}&pageNo=${0}&pageSize=${pageTotalSize}&sellerName=${selectedSellerData.name}&agentName=${selectedAgentData.name}&buyerName=${name}&areaName=${selectedAreaData.name}${isValid(dformattedDate) ? `&dDate=${format(dformattedDate, "yyyy-MM-dd")}` : ""}`);
    }
  };
  reactExports.useEffect(() => {
    if (date || dataType || days || (keyword == null ? void 0 : keyword.length) >= 3 || dDate) {
      handleSubmit();
    }
  }, [date, dataType, days, keyword, dDate]);
  const [isOpen, setIsOpen] = reactExports.useState(false);
  const [dDateOpen, setDdateOpen] = reactExports.useState(false);
  const handleQtyClick = async (deliveryDate, row) => {
    const formattedDate = new Date(date);
    const dformattedDate = deliveryDate ? new Date(deliveryDate) : "";
    navigate(`?date=${format(formattedDate, "yyyy-MM-dd")}&dataType=sellerwise&days=${days}&sellerId=${selectedSellerData.id}&agentId=${selectedAgentData.id}&buyerId=${row == null ? void 0 : row.id}&areaId=${selectedAreaData.id}&matchBy=${keyword}&pageNo=${0}&pageSize=${pageTotalSize}&sellerName=${selectedSellerData.name}&agentName=${selectedAgentData.name}&buyerName=${row == null ? void 0 : row.name}&areaName=${selectedAreaData.name}${isValid(dformattedDate) ? `&dDate=${format(dformattedDate, "yyyy-MM-dd")}` : ""}`);
    await setdDateSelected(true);
  };
  return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
    children: [/* @__PURE__ */ jsxRuntimeExports.jsx("div", {
      className: "flex flex-col my-5",
      children: /* @__PURE__ */ jsxRuntimeExports.jsx("h1", {
        className: "text-2xl font-bold",
        children: "Sales Analysis"
      })
    }), /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
      className: "flex flex-col sm:flex-row sm:space-x-3 my-3",
      children: /* @__PURE__ */ jsxRuntimeExports.jsxs(fetcher.Form, {
        method: "post",
        className: "flex flex-col sm:flex-row sm:space-x-2 space-y-2 sm:space-y-0 mb-3 sm:mb-0 w-full",
        children: [/* @__PURE__ */ jsxRuntimeExports.jsxs(Popover, {
          open: isOpen,
          onOpenChange: setIsOpen,
          children: [/* @__PURE__ */ jsxRuntimeExports.jsx(PopoverTrigger, {
            asChild: true,
            children: /* @__PURE__ */ jsxRuntimeExports.jsxs(Button, {
              variant: "outline",
              className: "w-full sm:w-[280px]",
              onClick: () => setIsOpen((prev) => !prev),
              children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Calendar, {
                className: "mr-2 h-4 w-4"
              }), date ? format(date, "PPP") : "Pick a date"]
            })
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(PopoverContent, {
            className: "w-auto p-0",
            children: /* @__PURE__ */ jsxRuntimeExports.jsx(Calendar$1, {
              mode: "single",
              selected: date,
              onSelect: (day) => {
                if (day) setDate(day);
                setIsOpen(false);
              },
              initialFocus: true
            })
          })]
        }), /* @__PURE__ */ jsxRuntimeExports.jsxs(Select, {
          value: dataType,
          onValueChange: setDataType,
          children: [/* @__PURE__ */ jsxRuntimeExports.jsx(SelectTrigger, {
            className: "w-full sm:w-[180px]",
            children: /* @__PURE__ */ jsxRuntimeExports.jsx(SelectValue, {
              placeholder: "Select Data Type"
            })
          }), /* @__PURE__ */ jsxRuntimeExports.jsxs(SelectContent, {
            children: [/* @__PURE__ */ jsxRuntimeExports.jsx(SelectItem, {
              value: "sellerwise",
              children: "Seller Wise"
            }), /* @__PURE__ */ jsxRuntimeExports.jsx(SelectItem, {
              value: "buyerwise",
              children: "Shop Wise"
            }), /* @__PURE__ */ jsxRuntimeExports.jsx(SelectItem, {
              value: "localitywise",
              children: "Locality Wise"
            }), /* @__PURE__ */ jsxRuntimeExports.jsx(SelectItem, {
              value: "agentwise",
              children: "Agent Wise"
            }), /* @__PURE__ */ jsxRuntimeExports.jsx(SelectItem, {
              value: "itemwise",
              children: "Item Wise"
            }), /* @__PURE__ */ jsxRuntimeExports.jsx(SelectItem, {
              value: "all",
              children: "All"
            })]
          })]
        }), /* @__PURE__ */ jsxRuntimeExports.jsxs(Select, {
          value: days ? days : selectedDays.toString(),
          onValueChange: setDays,
          children: [/* @__PURE__ */ jsxRuntimeExports.jsx(SelectTrigger, {
            className: "w-full sm:w-[180px]",
            children: /* @__PURE__ */ jsxRuntimeExports.jsx(SelectValue, {
              placeholder: "Select Days"
            })
          }), /* @__PURE__ */ jsxRuntimeExports.jsxs(SelectContent, {
            children: [/* @__PURE__ */ jsxRuntimeExports.jsx(SelectItem, {
              value: "8",
              children: "7 days"
            }), /* @__PURE__ */ jsxRuntimeExports.jsx(SelectItem, {
              value: "15",
              children: "14 days"
            })]
          })]
        })]
      })
    }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
      className: "flex flex-col sm:flex-row sm:flex-wrap gap-3 my-5 w-full p-4 bg-white rounded-xl shadow-md",
      children: [[{
        label: "Seller",
        data: selectedSellerData,
        type: "seller"
      }, {
        label: "Agent",
        data: selectedAgentData,
        type: "agent"
      }, {
        label: "Shop",
        data: selectedBuyerData,
        type: "buyer"
      }, {
        label: "Area",
        data: selectedAreaData,
        type: "locality"
      }].map(({
        label,
        data,
        type
      }) => /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
        className: "flex items-center gap-3 bg-gray-100 border border-gray-200 rounded-lg px-4 py-3 shadow-sm hover:shadow-md transition",
        children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("p", {
          className: "text-sm font-semibold text-gray-700",
          children: [label, ": ", /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
            className: "font-bold text-gray-900",
            children: data == null ? void 0 : data.name
          })]
        }), !(data == null ? void 0 : data.id) ? /* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
          variant: "outline",
          size: "sm",
          className: "rounded-full text-xs bg-blue-500 text-white hover:bg-blue-600",
          onClick: () => handleAddFilers(type),
          children: "+ Select"
        }) : /* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
          variant: "outline",
          size: "sm",
          className: "rounded-full text-xs bg-red-500 text-white hover:bg-red-600",
          onClick: () => handleClearFilter(type),
          children: /* @__PURE__ */ jsxRuntimeExports.jsx(CircleX, {
            className: "w-4 h-4"
          })
        })]
      }, type)), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
        className: "flex items-center gap-3 bg-gray-100 border border-gray-200 rounded-lg px-4 py-3 shadow-sm hover:shadow-md transition",
        children: [/* @__PURE__ */ jsxRuntimeExports.jsx("p", {
          className: "text-sm font-semibold text-gray-700",
          children: "Item:"
        }), isKeywordEnabled ? /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
          className: "flex items-center gap-2",
          children: [/* @__PURE__ */ jsxRuntimeExports.jsx("input", {
            type: "text",
            value: keyword,
            onChange: (e) => setKeyword(e.target.value),
            className: "border border-gray-300 rounded-md px-3 py-1 text-sm w-32 focus:ring-2 focus:ring-blue-400 focus:outline-none"
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
            variant: "outline",
            size: "sm",
            className: "rounded-full text-xs bg-red-500 text-white hover:bg-red-600",
            onClick: () => {
              setKeyword("");
              setIsKeywordEnabled(false);
            },
            children: /* @__PURE__ */ jsxRuntimeExports.jsx(CircleX, {
              className: "w-4 h-4"
            })
          })]
        }) : /* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
          variant: "outline",
          size: "sm",
          className: "rounded-full text-xs bg-blue-500 text-white hover:bg-blue-600",
          onClick: () => setIsKeywordEnabled(true),
          children: "+ Search"
        })]
      }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
        className: "flex items-center gap-3 bg-gray-100 border border-gray-200 rounded-lg px-4 py-3 shadow-sm hover:shadow-md transition",
        children: [/* @__PURE__ */ jsxRuntimeExports.jsx("p", {
          className: "text-sm font-semibold text-gray-700",
          children: "D.Date:"
        }), dDateSelected ? /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
          className: "flex items-center gap-2",
          children: [/* @__PURE__ */ jsxRuntimeExports.jsxs(Popover, {
            open: dDateOpen,
            onOpenChange: setDdateOpen,
            children: [/* @__PURE__ */ jsxRuntimeExports.jsx(PopoverTrigger, {
              asChild: true,
              children: /* @__PURE__ */ jsxRuntimeExports.jsxs(Button, {
                variant: "outline",
                className: "w-full sm:w-[280px]",
                onClick: () => setDdateOpen((prev) => !prev),
                children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Calendar, {
                  className: "mr-2 h-4 w-4"
                }), dDate ? format(dDate, "PPP") : "Pick a date"]
              })
            }), /* @__PURE__ */ jsxRuntimeExports.jsx(PopoverContent, {
              className: "w-auto p-0",
              children: /* @__PURE__ */ jsxRuntimeExports.jsx(Calendar$1, {
                mode: "single",
                selected: dDate,
                onSelect: (day) => {
                  if (day) setDDate(day);
                  setDdateOpen(false);
                },
                initialFocus: true
              })
            })]
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
            variant: "outline",
            size: "sm",
            className: "rounded-full text-xs bg-red-500 text-white hover:bg-red-600",
            onClick: () => {
              setDDate(void 0);
              setdDateSelected(false);
            },
            children: /* @__PURE__ */ jsxRuntimeExports.jsx(CircleX, {
              className: "w-4 h-4"
            })
          })]
        }) : /* @__PURE__ */ jsxRuntimeExports.jsx(Button, {
          variant: "outline",
          size: "sm",
          className: "rounded-full text-xs bg-blue-500 text-white hover:bg-blue-600",
          onClick: () => setdDateSelected(true),
          children: "+ Select"
        })]
      })]
    }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
      className: "overflow-x-auto rounded-md border max-w-full",
      children: [/* @__PURE__ */ jsxRuntimeExports.jsx("div", {
        className: "overflow-x-auto rounded-md border max-w-full",
        children: uniqueDates.length === 1 ? /* @__PURE__ */ jsxRuntimeExports.jsxs("table", {
          className: "w-full border-collapse",
          children: [/* @__PURE__ */ jsxRuntimeExports.jsx("thead", {
            children: /* @__PURE__ */ jsxRuntimeExports.jsxs("tr", {
              className: "bg-gray-100",
              children: [/* @__PURE__ */ jsxRuntimeExports.jsx("th", {
                className: "py-2 px-2 text-center w-1/5",
                children: "Name"
              }), " ", /* @__PURE__ */ jsxRuntimeExports.jsx("th", {
                className: "py-2 px-2 text-center w-2/3",
                children: format(uniqueDates[0], "EEE dd/MM")
              }), " "]
            })
          }), /* @__PURE__ */ jsxRuntimeExports.jsx("tbody", {
            children: formattedTableData.map((row) => {
              var _a2;
              return /* @__PURE__ */ jsxRuntimeExports.jsxs("tr", {
                className: "text-center",
                children: [/* @__PURE__ */ jsxRuntimeExports.jsx("td", {
                  className: "py-2 px-4 text-center cursor-pointer text-blue-300 font-bold sticky left-0 bg-white z-10 shadow-md",
                  onClick: () => handleClickItem(dashboardData == null ? void 0 : dashboardData.dataType, Number(row.id), row.name),
                  children: /* @__PURE__ */ jsxRuntimeExports.jsx(TooltipProvider, {
                    children: /* @__PURE__ */ jsxRuntimeExports.jsxs(Tooltip$1, {
                      children: [/* @__PURE__ */ jsxRuntimeExports.jsx(TooltipTrigger, {
                        asChild: true,
                        children: /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
                          className: "flex flex-wrap justify-center items-center break-words whitespace-normal ",
                          children: row.name
                        })
                      }), dataType === "buyerwise" && /* @__PURE__ */ jsxRuntimeExports.jsxs(TooltipContent, {
                        children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("p", {
                          children: ["AgentName: ", (row == null ? void 0 : row.agentName) || "-"]
                        }), /* @__PURE__ */ jsxRuntimeExports.jsxs("p", {
                          children: ["BuyerMobile: ", (row == null ? void 0 : row.buyerMobileNumber) || "-"]
                        })]
                      })]
                    })
                  })
                }), /* @__PURE__ */ jsxRuntimeExports.jsx("td", {
                  className: "py-2 px-2 text-center w-2/3 ",
                  children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                    className: `flex flex-row gap-1 justify-center items-center ${((_a2 = row == null ? void 0 : row.lastOrderDate) == null ? void 0 : _a2.split("T")[0]) >= uniqueDates[0] && row[`${uniqueDates[0]}_qtycheck`] ? "bg-yellow-100" : ""} `,
                    children: [/* @__PURE__ */ jsxRuntimeExports.jsx("button", {
                      className: `font-medium cursor-pointer ${dataType !== "buyerwise" ? "pointer-events-none opacity-50" : ""}`,
                      onClick: () => handleQtyClick(uniqueDates[0], row),
                      "aria-disabled": dataType !== "buyerwise",
                      children: row[`${uniqueDates[0]}_qty`] || "-"
                    }), /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                      className: "font-medium",
                      children: row[`${uniqueDates[0]}_visit`] && /* @__PURE__ */ jsxRuntimeExports.jsx(Store, {
                        color: "red",
                        size: 15
                      })
                    }), /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                      className: "font-medium",
                      children: row[`${uniqueDates[0]}_search`] && !row[`${uniqueDates[0]}_qtycheck`] && /* @__PURE__ */ jsxRuntimeExports.jsx(Search, {
                        color: "orange",
                        size: 15
                      })
                    }), /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                      className: "font-medium",
                      children: row[`${uniqueDates[0]}_call`] && /* @__PURE__ */ jsxRuntimeExports.jsx(PhoneCall, {
                        color: "green",
                        size: 15
                      })
                    })]
                  })
                })]
              }, row.id);
            })
          })]
        }) : /* @__PURE__ */ jsxRuntimeExports.jsx(ResponsiveTable, {
          headers,
          data: formattedTableData,
          renderRow: (row) => /* @__PURE__ */ jsxRuntimeExports.jsxs("tr", {
            className: "text-center",
            children: [/* @__PURE__ */ jsxRuntimeExports.jsx("td", {
              className: "py-2 px-4 text-center cursor-pointer text-blue-300 font-bold sticky left-0 bg-white z-10 shadow-md",
              onClick: () => handleClickItem(dashboardData == null ? void 0 : dashboardData.dataType, Number(row.id), row.name),
              children: /* @__PURE__ */ jsxRuntimeExports.jsx(TooltipProvider, {
                children: /* @__PURE__ */ jsxRuntimeExports.jsxs(Tooltip$1, {
                  children: [/* @__PURE__ */ jsxRuntimeExports.jsx(TooltipTrigger, {
                    asChild: true,
                    children: /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                      className: "cursor-pointer underline",
                      children: row.name
                    })
                  }), dataType === "buyerwise" && /* @__PURE__ */ jsxRuntimeExports.jsxs(TooltipContent, {
                    children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("p", {
                      children: ["AgentName: ", (row == null ? void 0 : row.agentName) || "-"]
                    }), /* @__PURE__ */ jsxRuntimeExports.jsxs("p", {
                      children: ["BuyerMobile: ", (row == null ? void 0 : row.buyerMobile) || "-"]
                    })]
                  })]
                })
              })
            }), uniqueDates.map((date2) => {
              var _a2;
              return /* @__PURE__ */ jsxRuntimeExports.jsx("td", {
                className: "py-2 px-7 text-center w-auto min-w-[100px]",
                children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                  className: `flex flex-row gap-1 justify-center items-center ${((_a2 = row == null ? void 0 : row.lastOrderDate) == null ? void 0 : _a2.split("T")[0]) >= date2 && row[`${date2}_qtycheck`] ? "bg-yellow-100" : ""}`,
                  children: [/* @__PURE__ */ jsxRuntimeExports.jsx("button", {
                    className: `font-medium ${dataType !== "buyerwise" ? "pointer-events-none opacity-50" : "cursor-pointer"}`,
                    onClick: () => handleQtyClick(date2, row),
                    disabled: dataType !== "buyerwise",
                    children: row[`${date2}_qty`] || "-"
                  }), /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                    className: "font-medium",
                    children: row[`${date2}_visit`] && /* @__PURE__ */ jsxRuntimeExports.jsx(Store, {
                      color: "red",
                      size: 15
                    })
                  }), /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                    className: "font-medium",
                    children: row[`${date2}_search`] && !row[`${date2}_qtycheck`] && /* @__PURE__ */ jsxRuntimeExports.jsx(Search, {
                      color: "orange",
                      size: 15
                    })
                  }), /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                    className: "font-medium",
                    children: row[`${date2}_call`] && /* @__PURE__ */ jsxRuntimeExports.jsx(PhoneCall, {
                      color: "green",
                      size: 15
                    })
                  })]
                })
              }, `${row.id}-${date2}`);
            })]
          }, row.id)
        })
      }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
        className: "flex items-center justify-end space-x-2 py-4 overflow-x-auto whitespace-nowrap",
        children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("h2", {
          className: "shrink-0",
          children: ["Current Page: ", pageNum + 1]
        }), /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
          className: "overflow-x-auto",
          children: /* @__PURE__ */ jsxRuntimeExports.jsx(ResponsivePagination, {
            totalPages: 50,
            currentPage: pageNum,
            onPageChange: (pageNum2) => handlePageSizeChange(pageNum2.toString())
          })
        })]
      })]
    }), /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
      children: /* @__PURE__ */ jsxRuntimeExports.jsx(SalesAnalysisDashBoard, {
        salesData: dashboardData
      })
    }), /* @__PURE__ */ jsxRuntimeExports.jsx(SalesSearchFilters, {
      title: "Select Seller Filter",
      isOpen: showSearchForm,
      onClose: () => setShowSearchForm(false),
      items: mappedSellerList || [],
      onSelect: (seller) => handleSelectedSeller(seller)
    }), /* @__PURE__ */ jsxRuntimeExports.jsx(SalesSearchFilters, {
      title: "Select Agent Filter",
      isOpen: showSearchAgentForm,
      onClose: () => setShowSearchAgentForm(false),
      items: agentList || [],
      onSelect: (agent) => handleSelectedAgent(agent)
    }), /* @__PURE__ */ jsxRuntimeExports.jsx(SalesDynamicSearchFilters, {
      title: "Select Buyer Filter",
      isOpen: showSearchBuyerForm,
      onClose: () => setShowSearchBuyerForm(false),
      itemList: buyerList || [],
      onSelect: (buyer) => handleSelectedBuyer(buyer),
      type: "buyer"
    }), /* @__PURE__ */ jsxRuntimeExports.jsx(SalesDynamicSearchFilters, {
      title: "Select Locality Filter",
      isOpen: showSearchAreaForm,
      onClose: () => setShowSearchAreaForm(false),
      itemList: localityList || [],
      onSelect: (locality) => handleSelectedArea(locality),
      type: "area"
    })]
  });
}
export {
  SalesAnalysis as default
};
//# sourceMappingURL=home.salesAnalysis-ByFKRHm0.js.map
