import { r as reactExports, j as jsxRuntimeExports } from "./jsx-runtime-bB2y7OuD.js";
import { A as AddMasterCategory } from "./addMasterCategory-TRqpdyp0.js";
import { I as Input } from "./input-3v87qohQ.js";
import { P as Pagination, a as PaginationContent, b as PaginationItem, c as PaginationPrevious, d as PaginationLink, e as PaginationNext } from "./pagination-DzgbTb6G.js";
import { T as Table, a as TableHeader, b as TableRow, c as TableHead, d as TableBody, e as TableCell } from "./table-CAxdJZsY.js";
import { T as Tabs, a as TabsList, b as TabsTrigger } from "./tabs-CfSdyzWr.js";
import { u as useDebounce } from "./useDebounce-BXbH_IFZ.js";
import { a as useNavigate } from "./index-DhHTcibu.js";
import { b as useSearchParams, u as useLoaderData, a as useFetcher } from "./components-D7UvGag_.js";
import "./label-cSASrwzW.js";
import "./index-CzZ6EYkN.js";
import "./index-Vp2vNLNM.js";
import "./index-z_byfFrQ.js";
import "./index-ImHKLo0a.js";
import "./utils-GkgzjW3c.js";
import "./dialog-BqKosxNq.js";
import "./index-DdafHWkt.js";
import "./index-D7VH9Fc8.js";
import "./index-DVTNuYOr.js";
import "./index-dIGjFc0I.js";
import "./index-BXzPK7u0.js";
import "./index-CEHS9zzk.js";
import "./index-QLGF6kQx.js";
import "./x-CCG_WJDF.js";
import "./createLucideIcon-uwkRm45G.js";
import "./button-ByAXMyvk.js";
import "./select-BFGSXKcr.js";
import "./index-IXOTxK3N.js";
import "./index-qCtcpOcW.js";
import "./index-BTdCMChR.js";
import "./index-DscYByPT.js";
import "./index-CkL5tk39.js";
import "./index-CpKiYcZd.js";
import "./index-C88PRvfd.js";
import "./chevron-down-pCP5jmjX.js";
import "./check-_dbWxIzT.js";
import "./radio-group-ChzooXbR.js";
import "./index-CG37gmC0.js";
import "./chevron-left-CLqBlTg1.js";
import "./chevron-right-B-tR7Kir.js";
function MasterItemCategory() {
  var _a;
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const categories = useLoaderData();
  const currentPage = parseInt(searchParams.get("page") || "0", 10);
  const activeTab = searchParams.get("tab") || "0";
  const [searchTerm, setSearchTerm] = reactExports.useState("");
  const debouncedSearchTerm = useDebounce(searchTerm, 500);
  const fetcher = useFetcher();
  const handleTabChange = (newTab) => {
    navigate(`?tab=${newTab}&page=0&searchTerm=${searchTerm}`);
  };
  const handlePageChange = (newPage) => {
    navigate(`?tab=${activeTab}&page=${newPage}&searchTerm=${searchTerm}`);
  };
  reactExports.useEffect(() => {
    if (debouncedSearchTerm.length >= 3) {
      navigate(`?tab=${activeTab}&page=${currentPage}&searchTerm=${debouncedSearchTerm.toLocaleLowerCase()}`);
    } else if (debouncedSearchTerm === "") {
      navigate(`?tab=${activeTab}&page=${currentPage}`);
    }
  }, [debouncedSearchTerm, navigate, activeTab, currentPage]);
  const handleSubmit = (ondcDomain, selectedLevel, categoryName, sequence, uploadedImageUrl, parentId, closeDialog, mode, icId) => {
    const formData = new FormData();
    formData.append("ondcDomain", ondcDomain);
    formData.append("categoryName", categoryName);
    formData.append("categoryLevel", selectedLevel.toString());
    formData.append("sequence", sequence.toString());
    formData.append("imageUrl", uploadedImageUrl);
    formData.append("parentId", JSON.stringify(parentId));
    formData.append("icId", icId);
    formData.append("mode", mode);
    formData.append("_intent", "_createCategory");
    fetcher.submit(formData, {
      method: "POST"
    });
    closeDialog();
  };
  return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
    className: "container mx-auto p-6",
    children: [/* @__PURE__ */ jsxRuntimeExports.jsx("div", {
      className: "flex justify-between items-center mb-4",
      children: /* @__PURE__ */ jsxRuntimeExports.jsx("h1", {
        className: "text-2xl font-bold",
        children: "Categories"
      })
    }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
      className: "flex flex-wrap justify-between items-start gap-4 mb-2",
      children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
        className: "w-full md:w-auto",
        children: [/* @__PURE__ */ jsxRuntimeExports.jsx("p", {
          className: "flex flex-wrap",
          children: "Type"
        }), /* @__PURE__ */ jsxRuntimeExports.jsx(Tabs, {
          value: activeTab,
          onValueChange: handleTabChange,
          className: "mb-6",
          children: /* @__PURE__ */ jsxRuntimeExports.jsxs(TabsList, {
            children: [/* @__PURE__ */ jsxRuntimeExports.jsx(TabsTrigger, {
              value: "1",
              children: "Level1"
            }), /* @__PURE__ */ jsxRuntimeExports.jsx(TabsTrigger, {
              value: "2",
              children: "Level2"
            }), /* @__PURE__ */ jsxRuntimeExports.jsx(TabsTrigger, {
              value: "3",
              children: "Level3"
            }), /* @__PURE__ */ jsxRuntimeExports.jsx(TabsTrigger, {
              value: "0",
              children: "All"
            })]
          })
        })]
      }), /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
        className: " flex flex-wrap justify-center gap-4 items-center",
        children: /* @__PURE__ */ jsxRuntimeExports.jsx(Input, {
          placeholder: "Search by Name",
          value: searchTerm,
          onChange: (e) => {
            setSearchTerm(e.target.value);
          },
          type: "search"
        })
      })]
    }), /* @__PURE__ */ jsxRuntimeExports.jsxs(Table, {
      children: [/* @__PURE__ */ jsxRuntimeExports.jsx(TableHeader, {
        children: /* @__PURE__ */ jsxRuntimeExports.jsxs(TableRow, {
          className: "border-b bg-gray-100",
          children: [/* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
            className: "font-bold",
            children: "Category Id"
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
            className: "cursor-pointer font-bold",
            children: "Category Name"
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
            className: "font-bold",
            children: "Category Level"
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
            className: "font-bold",
            children: "Parent Category"
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
            className: "font-bold",
            children: "Category Details"
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableHead, {
            className: "font-bold",
            children: "Actions"
          })]
        })
      }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableBody, {
        children: (categories == null ? void 0 : categories.data.length) === 0 ? /* @__PURE__ */ jsxRuntimeExports.jsx(TableRow, {
          children: /* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
            className: "py-4 text-center align-middle",
            colSpan: 100,
            children: "No Categories Found"
          })
        }) : (_a = categories == null ? void 0 : categories.data) == null ? void 0 : _a.map((item) => {
          var _a2;
          return /* @__PURE__ */ jsxRuntimeExports.jsxs(TableRow, {
            children: [/* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
              children: item.id
            }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
              className: `cursor-pointer text-blue-500 font-bold  items-center`,
              onClick: () => navigate(`/home/<USER>
              children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                className: "flex gap-2 items-center",
                children: [/* @__PURE__ */ jsxRuntimeExports.jsx("img", {
                  src: item == null ? void 0 : item.picture,
                  alt: "",
                  className: "w-12 h-12 object-cover"
                }), "    ", item.name]
              })
            }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
              children: item.level === 1 ? "L1" : item.level === 2 ? "L2" : item.level === 3 ? "L3" : "-"
            }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
              children: /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
                className: `flex flex-col gap-1  `,
                children: ((_a2 = item == null ? void 0 : item.parentCategories) == null ? void 0 : _a2.length) > 0 ? item == null ? void 0 : item.parentCategories.map((parent) => /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                  children: /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                    className: `${parent.disabled ? "text-red-600 font-bold" : ""} items-center`,
                    children: parent == null ? void 0 : parent.name
                  })
                }, parent.id)) : "-"
              })
            }), /* @__PURE__ */ jsxRuntimeExports.jsxs(TableCell, {
              children: [/* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                className: "text-xs",
                children: [/* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                  className: "font-medium",
                  children: "Items:"
                }), " ", item.totalItems || "-"]
              }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
                className: "text-xs",
                children: [/* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                  className: "font-medium",
                  children: "Sequence:"
                }), " ", item.sequence || "-"]
              }), (item == null ? void 0 : item.disabled) && /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
                className: "text-xs",
                children: /* @__PURE__ */ jsxRuntimeExports.jsx("span", {
                  className: " text-red-600 font-bold",
                  children: "Disabled"
                })
              })]
            }), /* @__PURE__ */ jsxRuntimeExports.jsx(TableCell, {
              children: /* @__PURE__ */ jsxRuntimeExports.jsx(AddMasterCategory, {
                buttonName: "Add New Category",
                categoryDetails: item,
                handleSubmit,
                mode: "Edit"
              })
            })]
          }, item.id);
        })
      })]
    }), /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
      className: "flex justify-between items-center mt-6 overflow-hidden",
      children: [/* @__PURE__ */ jsxRuntimeExports.jsx(Pagination, {
        children: /* @__PURE__ */ jsxRuntimeExports.jsxs(PaginationContent, {
          children: [currentPage > 0 && /* @__PURE__ */ jsxRuntimeExports.jsx(PaginationItem, {
            children: /* @__PURE__ */ jsxRuntimeExports.jsx(PaginationPrevious, {
              onClick: () => handlePageChange(currentPage - 1),
              className: "cursor-pointer"
            })
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(PaginationItem, {
            className: "cursor-pointer",
            children: /* @__PURE__ */ jsxRuntimeExports.jsx(PaginationLink, {
              children: currentPage + 1
            })
          }), /* @__PURE__ */ jsxRuntimeExports.jsx(PaginationItem, {
            children: /* @__PURE__ */ jsxRuntimeExports.jsx(PaginationNext, {
              onClick: () => handlePageChange(currentPage + 1),
              className: "cursor-pointer"
            })
          })]
        })
      }), /* @__PURE__ */ jsxRuntimeExports.jsx(AddMasterCategory, {
        buttonName: "Add New Category",
        handleSubmit
      })]
    })]
  });
}
export {
  MasterItemCategory as default
};
//# sourceMappingURL=home.masterItemCategory-CITOTm55.js.map
