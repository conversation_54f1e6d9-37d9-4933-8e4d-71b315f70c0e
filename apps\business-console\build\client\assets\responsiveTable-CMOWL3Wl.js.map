{"version": 3, "file": "responsiveTable-CMOWL3Wl.js", "sources": ["../../../app/components/ui/responsiveTable.tsx"], "sourcesContent": ["import { ReactNode } from \"react\";\r\n\r\ninterface TableProps<T> {\r\n      headers: string[]; // Header labels\r\n      data: T[]; // Data for table rows\r\n      renderRow: (item: T) => ReactNode; // Function to render each row\r\n      footerTotals?: (string | number)[]; // Optional footer totals\r\n      emptyMessage?: string; // Custom message when data is empty\r\n}\r\nexport const ResponsiveTable = <T,>({\r\n      headers,\r\n      data,\r\n      renderRow,\r\n      footerTotals,\r\n      emptyMessage = \"No results found.\",\r\n}: TableProps<T>) => {\r\n      return (\r\n            <div className=\"rounded-md border overflow-x-auto\">\r\n                  <div\r\n                        className=\"overflow-y-auto\"\r\n                        style={{ maxHeight: \"400px\" }}\r\n                  >\r\n\r\n\r\n                        <table className=\"w-full min-w-[600px] border-collapse\">\r\n                              {/* Table Header */}\r\n                              <thead className=\"sticky top-0 z-10\">\r\n                                    <tr className=\"bg-gradient-to-r from-blue-50 to-indigo-50\">\r\n                                          {headers.map((header, index) => (\r\n                                                <th\r\n                                                      key={index}\r\n                                                      className=\"py-2 px-7 text-center font-semibold whitespace-normal break-words\"\r\n\r\n                                                >\r\n                                                      {header}\r\n                                                </th>\r\n                                          ))}\r\n                                    </tr>\r\n                              </thead>\r\n\r\n                              {/* Table Body */}\r\n                              <tbody className=\" divide-gray-200\">\r\n                                    {Array.isArray(data) && data.length > 0\r\n                                          ? data.map((item, index) => (\r\n                                                // Use a debugging step if needed to check what `item` looks like\r\n                                                renderRow(item)\r\n\r\n                                          )\r\n                                          ) : (\r\n                                                <tr>\r\n                                                      <td\r\n                                                            colSpan={headers.length}\r\n                                                            className=\"py-2 px-3 text-center whitespace-normal break-words\"\r\n                                                      >\r\n                                                            {emptyMessage}\r\n                                                      </td>\r\n                                                </tr>\r\n                                          )}\r\n                              </tbody>\r\n\r\n                              {/* Table Footer */}\r\n                              {footerTotals && (\r\n                                    <tfoot>\r\n                                          <tr className=\"bg-gray-50 font-medium\">\r\n                                                {footerTotals.map((total, index) => (\r\n                                                      <td\r\n                                                            key={index}\r\n                                                            className={`py-2 px-3 ${index === 0 ? \"text-left\" : \"text-right\"\r\n                                                                  }`}\r\n                                                      >\r\n                                                            {total}\r\n                                                      </td>\r\n                                                ))}\r\n                                          </tr>\r\n                                    </tfoot>\r\n                              )}\r\n                        </table>\r\n                  </div>\r\n            </div>\r\n      );\r\n};\r\n"], "names": ["jsx", "jsxs"], "mappings": ";AASO,MAAM,kBAAkB,CAAK;AAAA,EAC9B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,eAAe;AACrB,MAAqB;AAET,SAAAA,kCAAAA,IAAC,OAAI,EAAA,WAAU,qCACT,UAAAA,kCAAA;AAAA,IAAC;AAAA,IAAA;AAAA,MACK,WAAU;AAAA,MACV,OAAO,EAAE,WAAW,QAAQ;AAAA,MAI5B,UAAAC,kCAAA,KAAC,SAAM,EAAA,WAAU,wCAEX,UAAA;AAAA,QAACD,kCAAA,IAAA,SAAA,EAAM,WAAU,qBACX,UAACA,kCAAAA,IAAA,MAAA,EAAG,WAAU,8CACP,UAAQ,QAAA,IAAI,CAAC,QAAQ,UAChBA,kCAAA;AAAA,UAAC;AAAA,UAAA;AAAA,YAEK,WAAU;AAAA,YAGT,UAAA;AAAA,UAAA;AAAA,UAJI;AAAA,QAAA,CAMhB,GACP,EACN,CAAA;AAAA,QAGAA,kCAAAA,IAAC,SAAM,EAAA,WAAU,oBACV,UAAA,MAAM,QAAQ,IAAI,KAAK,KAAK,SAAS,IAC9B,KAAK;AAAA,UAAI,CAAC,MAAM;AAAA;AAAA,YAEZ,UAAU,IAAI;AAAA;AAAA,QAGpB,0CACO,MACK,EAAA,UAAAA,kCAAA;AAAA,UAAC;AAAA,UAAA;AAAA,YACK,SAAS,QAAQ;AAAA,YACjB,WAAU;AAAA,YAET,UAAA;AAAA,UAAA;AAAA,WAEb,EAElB,CAAA;AAAA,QAGC,gBACMA,kCAAA,IAAA,SAAA,EACK,UAACA,kCAAAA,IAAA,MAAA,EAAG,WAAU,0BACP,UAAa,aAAA,IAAI,CAAC,OAAO,UACpBA,kCAAA;AAAA,UAAC;AAAA,UAAA;AAAA,YAEK,WAAW,aAAa,UAAU,IAAI,cAAc,YAC9C;AAAA,YAEL,UAAA;AAAA,UAAA;AAAA,UAJI;AAAA,QAMhB,CAAA,EACP,CAAA,EACN,CAAA;AAAA,MAAA,EAEZ,CAAA;AAAA,IAAA;AAAA,EAAA,GAEZ;AAEZ;"}