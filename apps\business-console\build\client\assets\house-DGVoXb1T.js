import { j as jsxRuntimeExports } from "./jsx-runtime-bB2y7OuD.js";
import { O as Outlet } from "./index-DhHTcibu.js";
function House() {
  return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", {
    children: [/* @__PURE__ */ jsxRuntimeExports.jsx("div", {
      children: "House Parent"
    }), /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
      children: /* @__PURE__ */ jsxRuntimeExports.jsx(Outlet, {})
    }), /* @__PURE__ */ jsxRuntimeExports.jsx("div", {
      children: "End"
    })]
  });
}
export {
  House as default
};
//# sourceMappingURL=house-DGVoXb1T.js.map
